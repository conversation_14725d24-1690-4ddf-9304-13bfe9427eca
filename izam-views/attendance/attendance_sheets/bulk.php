<?php

$extends = 'layouts/deprecated-layout';

?>
<style>
    .l-requisitions-list-footer {
    justify-content: flex-start;
    }

    .l-requisitions-list-footer .ui-btn {
    min-width: 250px;
    min-height: 50px;
    }
   
    .l-requisitions-list-footer .ui-btn .ui-btn-inner-text {
    font-size: 18px;
    }
</style>

<div class="m-app-content">
    <div class="l-list-box">
        <div class="l-container">
            <div class="top-actions top-actions-w100-mob">
                <div class="mb-opt-btn"></div>
            </div>

            <div class="l-list-table l-table-desktop">
                <div class="l-list-table-body u-bg-color-white">
                    <div class="l-requisitions-list-header ui-requisitions-list-header" data-bulk="bulk_requestions_state">
                        <div>
                            <h6 data-bulk="bulk_solved_requestions"></h6>
                            <p data-bulk="bulk_solved_requestions_text" class="ui-bulk-done-text"><?= sprintf(__t('%s Updated Successfully'), __t('Attendance Sheets')) ?></p>
                        </div>

                        <div class="ui-requisitions-list-result" data-bulk="result">
                        </div>

                        <div class="ui-requisitions-list-result" data-bulk="result">
                            <div class="ui-requisitions-list-result--success">
                                <span class="ui-result-num"><?= $success_number ?></span>
                                <span class="ui-result-text--success"><?= __t('Successful') ?></span>
                                <div class="l-icon">
                                    <i class="ui-icon--size-16 mdi mdi-check-bold l-inline-block u-text-color-success"></i>
                                </div>
                            </div>
                            <div class="ui-requisitions-list-result--fail">
                                <span class="ui-result-num"><?= $failed_number ?></span>
                                <span class="ui-result-text--fail"><?= __t('Failed') ?></span>
                                <div class="l-icon"><i class="ui-icon--size-16 mdi mdi-close-thick l-inline-block u-text-color-danger"></i></div>
                            </div>
                        </div>

                    </div>
                    <div class="ui-requisitions-list-content" data-bulk="bulk-list">
                        <?php foreach($results as $result): ?>
                            <li class="ui-requisition-item" data-bulk="bulk_item_<?= $result['id'] ?? '' ?>">
                                <div class="l-icon"><i class="ui-icon--size-16 l-inline-block mdi <?= $result['status'] ? 'mdi-check-bold u-text-color-success' : 'mdi-close-thick u-text-color-danger' ?>"></i></div><?= $result['message'] ?>
                            </li>
                        <?php endforeach; ?>
                    </div>
                    <?php  if (isset($back_url)) {?>
                   
                        <div class="l-requisitions-list-footer ui-requisitions-list-footer">
                            <a href="<?=$back_url?>" class="l-btn-inline l-btn--text-center ui-btn ui-btn--hover-ripple u-bg-color-secondary u-text-hover-color-primary ui-filter-box-btn l-filter-box-btn">
                                <span class="ui-btn-inner-content">
                                    <span class="ui-btn-inner-text"><?=__t('Close')?> </span>
                                </span>
                            </a>
                        </div>
                
                     <?php }?>

                </div>
            </div>
        </div>
    </div>

</div>

