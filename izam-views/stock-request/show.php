<?php
use Izam\Navigation\Services\PaginationService;
use Izam\Navigation\Helpers\PaginationHelper;
    use App\Utils\EntityKeyTypesUtil;
    use Izam\Navigation\Navigation;
    use Izam\Daftra\Common\Utils\PermissionUtil;
    use Izam\StockRequest\Models\StockRequest;
    use App\Facades\Permissions;
    use App\Models\Requisition;
    use App\Services\StockRequestService;

    $extends = 'layouts/deprecated-layout';
    $entityKey = EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY; 

    $this->viewVars['title_for_layout'] = sprintf(__t('Request #%s | %s'), $data->code, __t('Stock Requests'));
    $this->viewVars['_PageBreadCrumbs'] = $this->sharedVars['generalBreadCrumbs'];

    // $hasRequisitionViewPermission = Permissions::checkPermission(PermissionUtil::REQUISITION_VIEW);
    $stockRequestService = resolve(StockRequestService::class);
    $hasRequisitionViewPermission = $stockRequestService->canViewStockRequestRequisitions($data->id);

    $requisitionsIds = $stockRequestService->getRelatedRequisitionsIds($data->id);
    $requisitionsIdsStr = "";
    if (count($requisitionsIds)){
        $requisitionsIdsStr = implode(',' , $requisitionsIds);
    }

    $hasApproveOrRejectPermission = Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_STOCK_REQUEST);
    $hasDeletePermission = Permissions::checkPermission(PermissionUtil::DELETE_STOCK_REQUEST);
    $hasEditPermission = Permissions::checkPermission(PermissionUtil::EDIT_STOCK_REQUEST);

    $requisitionsOrderType = StockRequest::getRequisitionsOrderType($data->type);
    

    $requisitionsCount = count($requisitionsIds);

    $showRequisitionTab = $hasRequisitionViewPermission && (count($requisitionsIds) > 0);

    $showConvertBtn = Permissions::checkPermission(PermissionUtil::REQUISITION_ADD) && in_array($data->status, ['partially_processed', 'under_process']);
    $showPagination = PaginationService::exists($entityKey) && empty(request()->get('iframe'));
    $paginationHelper;
    $izamNavigation;
    if($showPagination){
        $paginationHelper = (new PaginationHelper());
        $paginationHelper->setPaginationInfo(PaginationService::get($entityKey));
        $izamNavigation= new  Navigation ();
        if ($paginationHelper->hasNext($id))    $izamNavigation->setPageNextUrl($paginationHelper->getNextUrl($id));
        if ($paginationHelper->hasPrevious($id))    $izamNavigation->setPagePreviousUrl($paginationHelper->getPreviousUrl($id));
        $izamNavigation->setPageAddUrl(route('owner.entity.create', $entityKey));
    }

    $main_status_bg_color = match($data->status){
        'pending' => 'primary',
        'under_process', 'partially_processed', 'processed' => 'success',
        'rejected' => 'danger'
    };
    $main_status_title = match($data->status){
        'pending' => ucfirst($data->status),
        'under_process', 'partially_processed', 'processed' => 'Approved',
        'rejected' => 'Rejected'
    };
    $sub_status_bg_color = match($data->status){
        'under_process', 'partially_processed' => 'orange',
        'processed' => 'var(--color-success);',
        default => ''
    };
    $sub_status_title = match($data->status){
        'under_process' => 'Under Process',
        'partially_processed' => 'Partially Processed',
        'processed' => 'Processed',
        default => ''
    };

    $main_status =  '<div class="l-badge-status ui-badge-status u-text-color-white u-bg-color-'.$main_status_bg_color.'">'.__t($main_status_title).'</div>';
    $sub_status = '<div class="l-badge-status ui-badge-status u-text-color-white" style="background-color: '.$sub_status_bg_color.';">'.__t($sub_status_title).'</div>';
    $repo = resolve(\Izam\Template\TemplateRepository::class);
    $view_templates= $repo->getEntityViewTemplates(EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY);
 ?>
<?php $this->section('page-head') ?>
<div class="m-app-page-head">
</div>

<?php $this->endSection() ?>

<style>
    [dir=ltr] .l-page-nav-btn-group>.l-btn-group {
        padding-left: 0px;
    }

    [dir=rtl] .l-page-nav-btn-group>.l-btn-group {
        padding-right: 0px;
    }

    .approve-reject-action-btn {
        height: 38px;
        margin-inline-end: 15px;
        font-weight: bold;
        outline: none;
        border: 0;
        padding: 0px 15px;
        display: flex;
        align-items: center;
        justify-content: center;

    }

    .approve-reject-action-btn i {
        font-size: 18px;
        margin-inline-end: 5px;
    }

    .reject-btn {
        background-color: #D1451c;
    }

    .approve-btn {
        background-color: #4d9b64;
    }

    .from-to-item {
        display: flex;
        align-items: center;
    }

    .from-to-item .from-to-label {
        color: var(--color-subtitle);
        display: block;
        font-weight: 400;
        margin-inline-end: 8px;
    }

    .from-to-item .from-to-arrow {
        color: #1977F2;
        margin: 0px 8px;
        font-size: 24px;
    }

    table {
        border-collapse: collapse;
        border: 1px solid var(--color-light-2);

    }

    th {
        color: var(--color-subtitle);
        font-weight: 400;
        width: 50%;
    }

    th,
    td {
        border-bottom: 1px solid var(--color-light-2);
        padding: 8px 10px;

    }

    th,
    td {}
</style>


<?php $this->section('page-head') ?>
<div class="m-app-page-head">
    <div class="m-app-page-head-pc">
        <div class="l-container">
            <div class="l-flex-row l-flex--align-center">
                <div class="l-flex-col-lg-6">
                    <div class="l-big-title l-big-title--title-only">
                            <div class="u-text-nowrap">
                                <h1 class="ui-big-title u-text-color-black">
                                    <span>
                                        <?=__t('Request')?> <span class="l-inline-block l-reservation-order-num">
                                            <span>#<?=$data->code?></span>
                                        </span>
                                    </span>
                                </h1>
                            </div>&nbsp;

                            <div class="l-v-line l-v-line--spacing-x">
                                <span class="u-bg-color-secondary"></span>
                            </div>
                            <?= $main_status ?>
                            &nbsp;
                            &nbsp;
                            &nbsp;
                            <?= $sub_status ?>
                    </div>
                </div>
                <div class="l-flex-col-lg-6 u-text-align-end l-flex l-flex--align-center l-flex--justify-end gap-10">

                <?php if($hasApproveOrRejectPermission): ?>  
                    <?php if($data->status == 'pending'):?>
                        
                                <button class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-danger u-text-color-white u-text-hover-color-white approve-reject-action-btn" type="button" data-bs-toggle="modal" data-bs-target="#rejectReasonModal">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-close-circle"></i>
                                        <span class="ui-btn-inner-text"><?=__t('Reject')?></span>
                                    </span>
                                </button>
                                <a href="/v2/owner/stock_request/update-status/<?=$data->id?>/approve" type="submit" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white approve-reject-action-btn">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-check-circle"></i>
                                        <span class="ui-btn-inner-text"><?=__t('Approve')?></span>
                                    </span>
                               </a>
                            
                        <?php elseif($data->status == 'rejected'): ?>
                                <a type="submit" href="/v2/owner/stock_request/update-status/<?=$data->id?>/undo_rejection" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-arrow-u-right-top"></i>
                                        <span class="ui-btn-inner-text"><?=__t('Undo Rejection')?></span>
                                    </span>
                                </a>
                        <?php elseif($data->status == 'under_process'):?>

                            <a href="/v2/owner/stock_request/update-status/<?=$data->id?>/undo_approval"  type="submit" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn" >
                                <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                <span class="ui-btn-inner-content">
                                    <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-arrow-u-right-top"></i>
                                    <span class="ui-btn-inner-text"><?=__t('Undo Approval')?></span>
                                </span>
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if($showConvertBtn):?>
                            <a href="/owner/stock_request/convert_to_requisition/<?=$data->id?>" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-white u-text-hover-color-white u-bg-color-primary u-bg-hover-color-default approve-reject-action-btn" >
                                <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                <span class="ui-btn-inner-content">
                                    <span class="ui-btn-inner-text"><?=__t('Convert To Requisition')?></span>
                                </span>
                            </a>
                    <?php endif; ?>

                    <div class="l-flex-inline l-flex--align-center l-page-nav-btn-group">
                        <div class="l-btn-group l-btn-group--separated" role="group">
                            <?php if($showPagination): ?>
                                <a onclick="initPageLoader()" title="<?=__t('Previous')?>" href="<?=$paginationHelper->getPreviousUrl($id)?>" <?=!$paginationHelper->hasPrevious($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-up"></i>
                                    </span>
                                </a>
                                <a onclick="initPageLoader()" title="<?=__t('Next')?>" href="<?=$paginationHelper->getNextUrl($id)?>" <?=!$paginationHelper->hasNext($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-down"></i>
                                    </span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="m-app-page-head-phone">
        <div class="l-container">
            <div class="l-items-list l-items-list--spacing-8 u-text-color-black">
                <div class="l-flex-row l-flex-row--spacing-5 l-flex--nowrap l-flex--align-center">
                    <div class="l-flex-col-auto l-flex-col--shrink-1 l-flex-col--overflow-auto">
                        <div class="l-big-title l-big-title--title-only">
                            <div class="u-text-nowrap">
                                <h1 class="ui-big-title u-text-color-black">
                                    <span><?=__t('Request')?>&nbsp;#<?=$data->code?></span>
                                </h1>
                            </div>
                        </div>
                        <?= $main_status ?>
                            &nbsp;
                            &nbsp;
                        <?= $sub_status ?>
                    </div>
                    <div class="l-flex-col-auto l-flex-col--ms-auto">

                        <div class="l-flex-inline" style="flex-direction: column; align-items: end; gap: 10px;">

                            <div class="l-flex-inline l-flex--align-center l-page-nav-btn-group">


                            <?php if($hasApproveOrRejectPermission): ?>  
                                    <?php if($data->status == 'pending'):?>
                                        <button class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-danger u-text-color-white u-text-hover-color-white approve-reject-action-btn" type="button" data-bs-toggle="modal" data-bs-target="#rejectReasonModal">
                                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                            <span class="ui-btn-inner-content">
                                                <span class="ui-btn-inner-text"><?=__t('Reject')?></span>
                                            </span>
                                        </button>
                                        <a href="/v2/owner/stock_request/update-status/<?=$data->id?>/approve" type="submit" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white approve-reject-action-btn">
                                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                            <span class="ui-btn-inner-content">
                                                <span class="ui-btn-inner-text"><?=__t('Approve')?></span>
                                            </span>
                                        </a>
                                    <?php elseif($data->status == 'rejected'): ?>
                                        <a type="submit" href="/v2/owner/stock_request/update-status/<?=$data->id?>/undo_rejection" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn">
                                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                            <span class="ui-btn-inner-content">
                                                <span class="ui-btn-inner-text"><?=__t('Undo Rejection')?></span>
                                            </span>
                                        </a>
                                    <?php elseif($data->status == 'under_process'):?>
                                        <a href="/v2/owner/stock_request/update-status/<?=$data->id?>/undo_approval"  type="submit" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn" >
                                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                            <span class="ui-btn-inner-content">
                                                <span class="ui-btn-inner-text"><?=__t('Undo Approval')?></span>
                                            </span>
                                        </a>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <div class="l-btn-group l-btn-group--separated" role="group">
                                    <?php if($showPagination): ?>
                                        <a onclick="initPageLoader()" title="<?=__t('Previous')?>" href="<?=$paginationHelper->getPreviousUrl($id)?>"  <?=!$paginationHelper->hasPrevious($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                            <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-up"></i>
                                            </span>
                                        </a>

                                        <a onclick="initPageLoader()" title="<?=__t('Next')?>" href="<?=$paginationHelper->getNextUrl($id)?>" <?=!$paginationHelper->hasNext($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                            <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-down"></i>
                                            </span>
                                        </a>
                                    <?php endif;?>
                                </div>

                            </div>

                            <div class="l-flex">
                                <?php if($showConvertBtn):?>
                                    <a href="/owner/stock_request/convert_to_requisition/<?=$data->id?>" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-white u-text-hover-color-white u-bg-color-primary u-bg-hover-color-default approve-reject-action-btn" >
                                        <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                        <span class="ui-btn-inner-content">
                                            <span class="ui-btn-inner-text"><?=__t('Convert To Requisition')?></span>
                                        </span>
                                    </a>
                                <?php endif; ?>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection() ?>

<div class="m-app-content">
    <div class="l-list-box">
        <div>
            <div class="l-show-card-box">
                <div class="l-container">
                    <div class="l-show-card-actions">
                        <div class="ui-show-card-actions">
                            <div class="l-btn-group-box" data-btn-group-box-responsive-btn-group-box="true">
                                <div class="l-btn-group l-btn-group--separated-2" role="group" data-btn-group-box-responsive-btn-group="true">
                                    <?php if($hasEditPermission):?>
                                        <a href="<?=route('owner.entity.edit', ['entityKey'=>$entityKey, 'id' => $data->id])?>" class="l-btn-inline l-btn-group-btn ui-btn u-bg-color-white u-text-color-action u-text-hover-color-white u-bg-hover-color-default">
                                            <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon ui-icon--size-16 l-icon l-icon--size-20 mdi mdi-pencil"></i>
                                                <span class="ui-btn-inner-text"><?=__t('Edit')?></span>
                                            </span>
                                        </a>
                                    <?php endif; ?>
                                    <?php if($hasDeletePermission):?>
                                        <a href="#" data-bs-toggle="modal" data-bs-target="#deletePromptModal" class="l-btn-inline l-btn-group-btn ui-btn u-bg-color-white u-text-color-action u-text-hover-color-white u-bg-hover-color-default">
                                            <span class="ui-btn-inner-content">
                                            <i class="ui-btn-inner-icon ui-icon--size-16 fas fa-trash-alt"></i>
                                                <span class="ui-btn-inner-text"><?=__t('Delete')?></span>
                                            </span>
                                        </a>
                                    <?php endif;?>
                                    <?= $this->includeSection('printable_templates/printables_dropdown-deprecated', ['view_templates'=> $view_templates, 'id' => $data->id]) ?>
                                </div>
                                <div class="l-btn-group-responsive-dropdown dropdown">
                                    <button type="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false" class="ui-btn u-bg-color-white u-text-color-action u-text-hover-color-white u-bg-hover-color-default ui-btn-group-responsive-btn" data-btn-group-box-responsive-btn="true" style="display: none;">
                                        <i class="mdi mdi-dots-horizontal"></i>
                                    </button>
                                    <div class="dropdown-menu ui-dropdown-menu l-btn-group-responsive-dropdown-menu" data-btn-group-box-responsive-dropdown-menu="true">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ui-show-card-content ui-show-card-content--with-tabs" data-tabs-box="true">
                        <div id="show-card-tabs">
                            <div class="ui-show-card-tabs-box">
                                <div class="l-flex-row l-flex--align-center">
                                    <div class="l-flex-col-auto">
                                        <ul class="ui-tabs nav" role="tablist" data-tabs-box-nav="true">
                                            <li class="ui-tab-item" role="presentation">
                                                <button class="ui-tab-link active" id="details-tab"  data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" data-tabs-box-nav-item-link="true" aria-selected="true" >
                                                    <?=__t('Details')?>
                                                </button>
                                            </li>

                                            <li class="ui-tab-item" role="presentation">
                                                <button class="ui-tab-link " id="activity-log-tab" data-lazy-iframe-btn="activity-log-iframe"  data-bs-toggle="tab"  data-bs-target="#activity-log"  type="button" role="tab" aria-controls="activity-log" data-tabs-box-nav-item-link="true" aria-selected="false" >
                                                    <?=__t('Activity Log')?>
                                                </button>
                                            </li>

                                            <?php if($showRequisitionTab):?>
                                                <li class="ui-tab-item" role="presentation">
                                                    <button class="ui-tab-link " id="requisitions-tab" data-lazy-iframe-btn="requisitions-iframe"  data-bs-toggle="tab"  data-bs-target="#requisitions"  type="button" role="tab" aria-controls="requisitions" data-tabs-box-nav-item-link="true" aria-selected="false" >
                                                        <?=__t('Requisitions') . " (".$requisitionsCount.")"?>
                                                    </button>
                                                </li>
                                            <?php endif; ?>

                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="show-card-tabs-content">
                            <div class="tab-content" data-tabs-box-tab-content="true">
                                
                                <div name="details" id="details" class="tab-pane fade active show" role="tabpanel" aria-labelledby="details-tab" data-tabs-box-tab-pane="true">
                                        <div class="ui-show-card-content-inner ui-show-card-content-inner--spacing-20">
                                                <div class="l-show-card-section-title-box">
                                                    <div class="l-flex-row">
                                                        <div class="l-flex-col-lg-12">
                                                            <div class="ui-card-section-title-box u-bg-color-light">
                                                                <h6 class="ui-card-section-title u-text-color-black"><?= __t(\Illuminate\Support\Str::singular($form->getLabel()).' Information')?></h6>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                    <div class="l-flex-row">
                                                        <div class="l-flex-col-lg-6">

                                                            <div class="l-show-card-section-item">
                                                                <div class="ui-show-card-section-item">
                                                                    <span class="ui-show-card-section-item-label"><?=__t('Code')?></span>
                                                                    <div class="ui-show-card-section-item-content">
                                                                        <span>
                                                                            <span class="l-inline-block l-reservation-order-num">
                                                                                <span>#<?=$data->code?></span>
                                                                            </span>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="l-show-card-section-item">
                                                                <div class="ui-show-card-section-item">
                                                                    <span class="ui-show-card-section-item-label"><?=__t('Type')?></span>

                                                                    <div class="l-flex-inline l-flex--align-center">
                                                                        <?php if($data->type == 'inbound'):?>
                                                                            <i class="mdi mdi-arrow-down-thick ui-icon--size-24 u-text-color-danger"></i>
                                                                        <?php elseif($data->type == 'outbound'):?>
                                                                            <i class="mdi mdi-arrow-up-thick ui-icon--size-24 u-text-color-primary"></i>
                                                                        <?php elseif($data->type == 'transfer'):?>
                                                                            <i class="mdi mdi-swap-horizontal-bold ui-icon--size-24 u-text-color-success"></i>
                                                                        <?php endif; ?>
                                                                        <span style="margin-inline-start: 5px;"> <?=__t(ucfirst($data->type))?></span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>

                                                        <div class="l-flex-col-lg-6">
                                                            <div class="l-show-card-section-item">
                                                                <div class="ui-show-card-section-item">
                                                                    <?php if($data->date): ?>
                                                                        <span class="ui-show-card-section-item-label"><?=__t('Date')?></span>
                                                                        <div class="ui-show-card-section-item-content">
                                                                            <p><?=format_date( $data->date,false,false )  ?></p>
                                                                        </div>
                                                                    <?php else: ?>
                                                                        <span class="ui-show-card-section-item-label"> &nbsp;</span>
                                                                        <div class="ui-show-card-section-item-content">
                                                                            <p>&nbsp;</p>
                                                                        </div>
                                                                    <?php endif;?>
                                                                </div>
                                                            </div>

                                                            <div class="l-show-card-section-item">
                                                                <div class="ui-show-card-section-item">
                                                                    <span class="ui-show-card-section-item-label"><?=__t('Warehouse')?></span>
                                                                    <div class="ui-show-card-section-item-content">
                                                                        <?php 
                                                                            $site_lang = '';
                                                                            if (getAuthStaff('language_code')) {
                                                                                $site_lang = getAuthStaff('language_code');
                                                                            } else if (!empty(getAuthClient('language_code'))) {
                                                                                $site_lang = getAuthStaff('language_code');
                                                                            } else {
                                                                                $site_lang = getAuthOwner('language_code');
                                                                            }
                                                                        
                                                                         if($data->type == "transfer"):?>
                                                                            <p class="from-to-item">
                                                                                <span class="from-to-label"><?=__t('From')?>:</span>
                                                                                <span><?=$data->store->name?></span>
                                                                                <i class="mdi mdi-arrow-<?= $site_lang == 7 ?'left':'right'?>-bold from-to-arrow"></i>
                                                                                <span class="from-to-label"><?=__t('To')?>:</span>
                                                                                <span><?=$data->store2?->name?></span>
                                                                            </p>
                                                                        <?php else:?>
                                                                            <p><?=$data->store->name?></p>
                                                                    <?php endif; ?>
                                                                    </div>
                                                                </div>
                                                            </div>


                                                        </div>
                                                        <?php if($data->action_comment):?>
                                                            <div class="l-flex-col-lg-6">
                                                                <div class="l-show-card-section-item">
                                                                    <div class="ui-show-card-section-item">
                                                                        <span class="ui-show-card-section-item-label"><?=__t('Rejection Reason')?></span>
                                                                        <p class="u-text-color-red" ><?=$data->action_comment?></p>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                    <?php endif; ?>
                                                    </div>
                                                </div>

                                                    <div class="l-show-card-section-title-box">
                                                        <div class="l-flex-row">
                                                            <div class="l-flex-col-lg-12">
                                                                <div class="ui-card-section-title-box u-bg-color-light">
                                                                    <h6 class="ui-card-section-title u-text-color-black">
                                                                        <?=__t('Stock Request Items')?></h6>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                        <div class="l-flex-row">

                                                            <div class="l-flex-col-lg-12">
                                                                <table style="width:100%">
                                                                    <tr>
                                                                        <th><?=__t('Item')?></th>
                                                                        <th><?=__t('Quantity')?></th>
                                                                    </tr>
                                                                    <?php foreach($data->stock_request_items as $item):?>
                                                                        <tr style="color:black">
                                                                            <td><?=$item->stock_request_items_product->name?></td>
                                                                            <td><?=($item->factor> 1 ? $item->quantity/$item->factor : $item->quantity) ." ". ($item->unit_small_name ?? "")?></td>
                                                                        </tr>
                                                                    <?php endforeach; ?>
                                                                </table>
                                                            </div>
                                                        </div>
                                                </div>
                                                </div>
                                                <?php if(!empty($data->notes) || $data->attachments):?>
                                                    <div class="ui-show-card-content-inner">
                                                        <div class="l-show-card-section-title-box">
                                                            <div class="l-flex-row">
                                                                <div class="l-flex-col-lg-12">
                                                                    <div class="ui-card-section-title-box u-bg-color-light">
                                                                        <h6 class="ui-card-section-title u-text-color-black">
                                                                            <?=__t('Additional Information')?> </h6>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <?php if(!empty($data->notes)):?>
                                                            <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                                <div class="l-flex-row">
                                                                    <div class="l-flex-col-lg-6">
                                                                        <div class="l-show-card-section-item">
                                                                            <div class="ui-show-card-section-item">
                                                                                <span class="ui-show-card-section-item-label"><?=__t('Notes')?></span>
                                                                                <div class="ui-show-card-section-item-content ui-show-card-section-item-content--text-pre ui-show-card-section-item-content--lh-normal">
                                                                                    <p><?=$data->notes?></p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                    <?php endif; ?>
                                                        <?php if($data->attachments) :?>
                                                            <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                                <div class="l-flex-row">
                                                                    
                                                                    <div class="l-flex-col-lg-12">
                                                                        <div class="l-show-card-section-item">
                                                                            <div class="ui-show-card-section-item">
                                                                            <?= $this->includeSection('partials/deprecated/multiplefile_preview_attachments',['attachments' => json_decode($form->get('attachments')->getAttributes()['data-izam1-forms1-value']) ]);?>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                    <?php endif; ?>
                                                    </div>
                                            <?php endif; ?>

                                </div>

                                <div name="activity-log" id="activity-log" class="tab-pane fade" role="tabpanel" aria-labelledby="activity-log-tab" data-tabs-box-tab-pane="true">
                                    <div class="ui-show-card-content-inner ui-show-card-content-inner--spacing-20">
                                        <div class="u-text-align-center u-text-color-default" data-lazy-iframe-placeholder="activity-log-iframe">
                                            <i class="ui-icon ui-icon--size-42 far fa-spin fa-spinner-third"></i>
                                        </div>
                                        <iframe id="activity-log-iframe" frameborder="0" style="display: none; overflow: hidden;" data-lazy-iframe="activity-log-iframe" src="about:blank" data-lazy-iframe-src="/v2/owner/activity_logs/entity/iframe?entity_key=<?=$entityKey?>&amp;entity_id=<?=$data->id?>&amp;sort=DESC&amp;layout2022=1" data-app-iframe="true" width="100%" scrolling="no"></iframe>
                                    </div>
                                </div>
                             
                                <?php if($showRequisitionTab): ?>
                                    <div name="requisitions" id="requisitions" class="tab-pane fade" role="tabpanel" aria-labelledby="requisitions-tab" data-tabs-box-tab-pane="true">
                                    <div class="ui-show-card-content-inner ui-show-card-content-inner--spacing-20">
                                        <div class="u-text-align-center u-text-color-default" data-lazy-iframe-placeholder="requisitions-iframe">
                                            <i class="ui-icon ui-icon--size-42 far fa-spin fa-spinner-third"></i>
                                        </div>
                                            <iframe onload="onRequisitionsIframeLoad(this)" id="requisitions-iframe" data-lazy-iframe="requisitions-iframe"  src="about:blank" data-lazy-iframe-src="/owner/requisitions/index?box=1&requisitions_ids=<?= $requisitionsIdsStr ?>&is_stock_request=true" data-app-iframe="true" style="width: 100%; min-height: 500px;border: 0" width="100%" scrolling="no"></iframe>
                                        </div>
                                    </div>
                                <?php endif;?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<?php if ($hasApproveOrRejectPermission):?>
    <div class="modal fade" id="rejectReasonModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="ui-modal-content ui-modal-content--height-auto">
                <form method="get" id="update-stock-request-status-form" action="/v2/owner/stock_request/update-status/<?=$data->id?>/reject">
                     <?=csrf_field()?>
                     <div class="l-modal-header ui-modal-header">
                        <h5 class="l-modal-title ui-modal-title">
                            <span><?=__t('Rejection Reason')?></span>
                        </h5>
                        <button type="button" class="ui-btn-icon l-modal-header-btn ui-modal-header-btn" data-bs-dismiss="modal" aria-label="Close">
                            <i class="mdi mdi-close-thick ui-icon l-icon ui-icon--size-14"></i>
                        </button>
                    </div>
                    <div class="ui-modal-body">
                        <div class="l-flex-row">
                            <div class="l-flex-col-lg-12">
                                <div class="l-flex l-flex--align-center l-flex--justify-between">
                                    <textarea class="form-control l-input ui-input" name="action_comment" rows="5" placeholder=""></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="l-modal-footer ui-modal-footer">
                        <div class="l-flex-row l-flex-row--spacing-5 l-flex-col">

                            <div class="l-flex-col-lg-4">
                                <button class="l-btn-inline l-btn--text-center ui-btn ui-btn--py-12 l-btn ui-btn--hover-ripple u-bg-color-secondary u-text-color-action u-text-hover-color-action" type="button" data-bs-dismiss="modal">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                    <span class="ui-btn-inner-content">
                                        <span class="ui-btn-inner-text l-flex l-flex--align-center">
                                            <span><?=__t('Cancel')?></span>
                                        </span>
                                    </span>
                                </button>
                            </div>

                            <div class="l-flex-col-lg-8">
                                <button class="l-btn-inline l-btn--text-center ui-btn ui-btn--py-12 l-btn ui-btn--hover-ripple u-bg-color-danger u-text-color-white u-text-hover-color-white" type="submit">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                                    <span class="ui-btn-inner-content">
                                        <span class="ui-btn-inner-text l-flex l-flex--align-center">
                                            <span><?=__t('Confirm Rejection')?></span>
                                        </span>
                                    </span>
                                </button>
                            </div>

                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php if($hasDeletePermission): ?>
    <div class="modal fade l-delete-modal" data-app-delete-modal="true" id="deletePromptModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-dialog-centered modal-md" style="max-width: 500px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <i class="mdi mdi-trash-can-outline trash-icon u-text-color-danger"></i>
                    <div data-app-delete-modal-message="true"><?=__t('Are you sure you want to delete this Stock Request?')?></div>
                </div>
                <form method="post" id="delete-row-action-form" name="row-action-form" action="<?=route('owner.entity.delete', ['entityKey'=>$entityKey, 'id' => $data->id])?>">
                    <?=@csrf_field()?>
                    <input type="hidden" name="_method" id="delete-prompt-method" value="DELETE">
                    <button type="submit" class="ui-btn u-bg-color-danger u-text-color-white"> <?= __t('Yes') ?></button>
                    <button type="button" class="ui-btn u-bg-color-secondary" data-bs-dismiss="modal"><?= __t('No') ?></button>
                </form>
            </div>
        </div>
    </div>
<?php endif;?>
 
 <script>
    function onRequisitionsIframeLoad(iframe){
        iframe.contentWindow.loadLinksOnParentWindow();
    }
 </script>