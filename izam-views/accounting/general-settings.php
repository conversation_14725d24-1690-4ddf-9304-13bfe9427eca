<?php

$extends = 'layouts/layout';
$this->sharedVars['extraContainerClass'] = '';
$this->viewVars['_PageBreadCrumbs'] = [
    ['link' => '/v2/owner/accounting/settings', 'title' => __("Accounting Settings", true)],
    ['link' => '#', 'title' => __("General Settings", true)],
];

use Izam\Entity\Utils\SizeUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\View\Form\Helper\FormCollection;
use Laminas\Form\Element\Checkbox;
use Laminas\Form\Form;

$inputs = [
    'tax_in_journals' => [
        'type' => 'checkox',
        'value' => $taxInJournals,
        'label' => __t('Display Tax In The Journal Entries', true),
    ],
    'display_cost_centers_in_journals' => [
        'type' => 'checkox',
        'value' => $display_cost_centers_in_journals,
        'label' => __t('Display Cost Center In The Journal Entries', true),
    ],
    SettingsUtil::ENABLE_TAGS_IN_JOURNAL => [
        'type' => 'checkox',
        'value' => $enable_tags_in_journal,
        'label' => __t('Assign Tags to Journals Transactions', true),
    ],
    SettingsUtil::UPDATE_JOURNAL_ENTRIES_CURRENCY_RATES => [
        'type' => 'checkox',
        'value' => $updateJournalEntriesCurrencyRates,
        'label' => __t('Update Journal Entries Currency Rates', true),
    ],
    SettingsUtil::ENABLE_INVOICE_ITEM_JOURNAL_ACCOUNT => [
        'type' => 'checkox',
        'value' => $enableInvoiceItemJournalAccount,
        'label' => sprintf(__t('Specify an Account for Each Item in the %s', true), __t('Invoices', true)),
    ],
    SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT => [
        'type' => 'checkox',
        'value' => $enablePurchaseInvoiceItemJournalAccount,
        'label' => sprintf(__t('Specify an Account for Each Item in the %s', true), __t('Purchases', true)),
            ],
    SettingsUtil::ENABLE_REQUISITION_ITEM_JOURNAL_ACCOUNT => [
        'type' => 'checkox',
        'value' => $enableRequisitionItemJournalAccount,
        'label' => sprintf(__t('Specify an Account for Each Item in the %s', true), __t('Requisitions', true)),
    ],
    SettingsUtil::ENABLE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION => [
        'type' => 'checkbox',
        'value' => $enableInvoiceItemCostCenterDistribution,
        'label' => sprintf(__t('Distribution of Cost Centers in %s for Each Item', true), __t('Invoices', true)),
    ],
    SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION => [
        'type' => 'checkbox',
        'value' => $enablePurchaseInvoiceItemCostCenterDistribution,
        'label' => sprintf(__t('Distribution of Cost Centers in %s for Each Item', true), __t('Purchase Invoices', true)),
    ],
    SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION => [
        'type' => 'checkbox',
        'value' => $enableRequisitionItemCostCenterDistribution,
        'label' => sprintf(__t('Distribution of Cost Centers in %s for Each Item', true), __t('Requisitions', true)),
    ]
];
$form = new Form();

foreach ($inputs as $name => $input){
    $element  = new Checkbox($input['type']);
    $element->setLabel($input['label']);
    $element->setOption('allow_beside', true);
    if ( isset($input['value']) && $input['value'] == "1" ){
        $element->setAttribute('checked' , 1);
    }
    $element->setLabelOption('hide_label', true);
    $element->setAttribute('class','ui-switch ui-switch--size-40 ui-switch--color-vintage');
    $element->setName($name);
    $element->setOption('allow_beside', true);
    $element->setOption('size', SizeUtil::getSizeClass(SizeUtil::MEDIUM));
    $form->add($element);
}
$helper = new FormCollection();
$form->prepare();
$buttons = [
    'desktop' => [
        'start' => [
        ],
        'end' => [
            ['type' => 'link', 'class' => 'btn-cancel', 'icon' => 'mdi-close-thick', 'text' => __t('Cancel'), 'url' => '/v2/owner/accounting/settings'],
            ['type' => 'button', 'class' => 'btn-success', 'icon' => 'mdi-content-save', 'text' => __t('Save'), 'url' => '#']
        ]
    ],
    'mobile' => [
        'end' => [
            ['type' => 'link', 'class' => 'btn-secondary', 'icon' => 'mdi-close-thick', 'text' => __t('Cancel'), 'url' => '/v2/owner/accounting/settings'],
            ['type' => 'button', 'class' => 'btn-success', 'icon' => 'mdi-content-save', 'text' => __t('Save'), 'url' => '#']
        ]
    ]
];
?>

<div class="m-app-content">
    <div class="container">
        <div class="my-10 vstack gap-10">
            <div class="card">
                <div class="card-header">
                    <?= __t('General Settings', true) ?>
                </div>
                <?php $this->section('article-start') ?>
                <form action="/owner/settings/accounting_general" method="post" data-app-form="accounting-settings">
                    <?php $this->endSection() ?>
                    <?php $this->section('page-head') ?>
                    <?= $this->includeSection('partials/form/head', ['buttons' => $buttons]); ?>
                    <?php $this->endSection() ?>
                    <div class="form-wrapper">
                        <div class="row">
                            <?php echo $helper->setShouldWrap(false)->render($form);?>
                        </div>
                    </div>
                    <?php $this->section('article-end') ?>
                </form>
                <?php $this->endSection() ?>
            </div>
        </div>
    </div>
</div>
