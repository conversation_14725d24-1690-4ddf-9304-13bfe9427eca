<?php

use App\Facades\Plugins;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use App\Utils\TreasuryTypeUtil;
use Izam\Daftra\Common\Utils\BankTransactionsStatusUtil;

?>

<div class="d-none d-lg-block">
    <form action="" method="POST" data-lt-form="true">
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table" data-lt="true">
                    <colgroup>
                        <col/>
                        <col/>
                        <col/>
                        <?php if ($showBalanceBeforeAndAfter || 1) {
                            echo '<col/>';
                        } ?>
                        <?php if ($treasuryData->type == App\Utils\TreasuryTypeUtil::BANK and !empty($treasuryData->currency_code)) {
                            echo "<col/>";
                        } ?>
                        <col/>
                    </colgroup>
                    <thead data-lt-head="true">
                    <tr>
                        <th class="listing-cell-start">
                            <div class="d-flex align-items-center gap-6">
                                <div class="listing-cell-check listing-cell-check-sm">
                                    <div class="dropdown" data-lt-dropdown="true">
                                        <button class="btn btn-link dropdown-toggle dropdown-toggle-2 px-0"
                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                type="button">
                                            <input class="form-check-input" type="checkbox"
                                                   data-lc-toggle="current-page"/>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                        data-lc-check="none">
                                                    <i class="mdi mdi-close text-primary"></i>
                                                    <span><?= __t('None') ?></span>
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                        data-lc-check="current-page">
                                                    <i class="mdi mdi-check-underline text-primary"></i>
                                                    <span><?= __t('All (Current Page)') ?></span>
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                        data-lc-check="all-filtered-pages">
                                                    <i class="mdi mdi-check-all text-primary"></i>
                                                    <span><?= __t('All Filtered (All Pages)') ?></span>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div><?= __t('Transaction') ?></div>
                            </div>
                        </th>
                        <th><?= __t('Deposit') ?></th>
                        <th><?= __t('Withdraw') ?></th>
                        <?php if ($showBalanceBeforeAndAfter) {
                            echo "<th>" . __t('Balance After') . "</th>";
                        } ?>
                        <?php if ($treasuryData->type == App\Utils\TreasuryTypeUtil::BANK and !empty($treasuryData->currency_code)) {
                            echo "<th>" . __t('Status') . "</th>";
                        } ?>
                        <th width="45" class="listing-cell-end text-end">
                            <div class="dropdown" data-lt-dropdown="true">
                                <?= $this->includeSection('system_transactions/partials/sort-options') ?>
                            </div>
                        </th>
                    </tr>
                    <?php if ($showBalanceBeforeAndAfter && (isset($_GET['sort_field']) && $_GET['sort_field'] === 'date-ASC')) { ?>
                        <tr>
                            <td class="listing-cell-start bg-light">
                                <span class="text-amount text-amount-sm text-body"><?= __t('Balance Before') ?></span>
                            </td>
                            <td class="listing-cell-end text-end bg-light" colspan="5">
                                <span class="text-amount text-amount-sm text-body"><?= format_price_simple($balance) ?></span>
                            </td>
                        </tr>
                    <?php } ?>

                    </thead>
                    <tbody>
                    <?php foreach ($pagination as $record) {
                        $record_files = array_map(function ($file) {
                            isset($file['size']) ? $file['size'] = formatFilesSize($file['size']) : '';
                            $file['download_url'] = $file['url'];
                            return $file;
                        }, $record->files);
                        $viewUrl = "/owner/journals/view/$record->journal_id";
                        $popupUrl = "/owner/journals/popup/$record->journal_id";
                        ?>
                        <tr tabindex="0">
                            <td class="listing-cell-start">
                                <?php if ($this->checkPermission(VIEW_ALL_JOURNALS) || ($this->checkPermission(VIEW_ALL_JOURNALS) && getAuthOwner()['staff_id'] == $record->journal->staff_id)) { ?>
                                    <a href="<?= $viewUrl ?>" data-mi-url="<?= $popupUrl ?>" data-mi-title="<?= __t('Journal') ?>" data-mi-size="xl"
                                       class="listing-cell-link"></a>
                                <?php } ?>
                                    <div class="d-flex gap-6">
                                        <div class="listing-cell-check listing-cell-check-sm">
                                            <input class="form-check-input" type="checkbox" name="ids[]"
                                                   value="<?= $record->id ?>" data-lc-item="true"/>
                                        </div>
                                        <div class="title-text vstack gap-3 flex-shrink-1">
                                            <p><?= format_date($record->journal->date) . ' (#' . $record->journal->number . ')' ?></p>
                                            <span class="title-subtitle-text"><?= $record->alter_description ?: $record->description ?></span>
                                            <div class="hstack gap-6 align-items-center">
                                                <?php if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) { ?>
                                                    <div class="text-meta">
                                                        <i class="mdi mdi-office-building"></i>
                                                        <span><?= $record->journal->branch->name ?></span>
                                                    </div>
                                                <?php } ?>

                                                <?php if (!empty($record->staff)) {
                                                    if (!empty($record->staff['img'])) {
                                                        $img =  $record->staff['img'];
                                                    }else{
                                                        $img = AvatarURLGenerator::generate($record->staff['name'], $record->staff['id'], 24, null);
                                                    }
                                                    ?>
                                                    <div class="text-meta">
                                                        <img class="thumb thumb-sm" loading="lazy"
                                                             src="<?= $img ?>"/>
                                                        <span><?= $record->staff['name'] ?></span>
                                                    </div>
                                                <?php } ?>

                                                <?php if (!empty($record->files)) {
                                                    if (count($record->files) == 1) { ?>
                                                        <a class="text-meta text-decoration-none"
                                                            target="_blank"
                                                            download="<?= $record->files[0]['name'] ?>"
                                                            href="<?= $record->files[0]['url'] ?>">
                                                            <i class="mdi mdi-file-document"></i>
                                                            <span>1</span>
                                                        </a>
                                                    <?php } else { ?>
                                                        <a class="text-meta text-decoration-none"
                                                           data-bs-toggle="tooltip"
                                                           data-bs-html="true"
                                                           href="javascript:void(0)"
                                                           data-ma-title="<?= __t('Attachments') ?>"
                                                           data-ma-data='<?= json_encode($record->files) ?>'
                                                           title='<?php foreach($record->files as $file) { ?>
                                                                <p class="my-4"><?= $file['name'] ?></p>
                                                            <?php } ?>'>
                                                            <i class="mdi mdi-file-document"></i>
                                                            <span><?= count($record->files) ?></span>
                                                        </a>
                                                    <?php } ?>
                                                <?php } ?>

                                            </div>
                                        </div>
                                    </div>
                            </td>
                            <td>
                                <?php if ($this->checkPermission(VIEW_ALL_JOURNALS) || ($this->checkPermission(VIEW_ALL_JOURNALS) && getAuthOwner()['staff_id'] == $record->journal->staff_id)) { ?>
                                    <a href="<?= $viewUrl ?>" data-mi-url="<?= $popupUrl ?>" data-mi-title="<?= __t('Journal') ?>" data-mi-size="xl"
                                       class="listing-cell-link"></a>
                                <?php } ?>
                                <?php if ($record->debit) { ?>
                                    <?php if ($treasuryData->type == App\Utils\TreasuryTypeUtil::TREASURY || empty($treasuryData->currency_code)) { ?>
                                        <div class="text-nowrap">
                                            <span class="text-amount text-amount-sm text-success"><?= format_price_simple($record->debit) ?></span>
                                        </div>
                                        <?php if ($record->currency_code != $system_currency) { ?>
                                            <div class="text-nowrap">
                                                <?php
                                                $tooltips_title = 1 . $record->currency_code . ' = ' . ($record->currency_rate) . $system_currency;
                                                ?>
                                                <abbr class="text-light-3 fs-7" data-bs-toggle="tooltip"
                                                      data-bs-html="true"
                                                      title="<?= $tooltips_title ?>">
                                                    <span> <?= format_price(($record->currency_debit), $record->currency_code) ?></span>
                                                    <i class="mdi mdi-help-circle"></i>
                                                </abbr>
                                            </div>
                                        <?php }
                                    } else if ($treasuryData->type == App\Utils\TreasuryTypeUtil::BANK) {
                                        if ($treasuryData->currency_code != $system_currency) { ?>
                                            <div class="text-nowrap">
                                                <span class="text-amount text-amount-sm text-success"><?= format_price($record->currency_debit, $record->currency_code) ?></span>
                                            </div>
                                        <?php } else if ($treasuryData->currency_code == $system_currency) { ?>
                                            <div class="text-nowrap">
                                                <span class="text-amount text-amount-sm text-success"><?= format_price_simple($record->debit) ?></span>
                                            </div>
                                            <?php if ($record->currency_code != $system_currency) { ?>
                                                <div class="text-nowrap">
                                                    <?php
                                                    $tooltips_title = 1 . $record->currency_code . ' = ' . ($record->currency_rate) . $system_currency;
                                                    ?>
                                                    <abbr class="text-light-3 fs-7" data-bs-toggle="tooltip"
                                                          data-bs-html="true"
                                                          title="<?= $tooltips_title ?>">
                                                        <span> <?= format_price(($record->currency_debit), $record->currency_code) ?></span>
                                                        <i class="mdi mdi-help-circle"></i>
                                                    </abbr>
                                                    </span>
                                                </div>
                                            <?php } ?>

                                        <?php } ?>

                                    <?php } ?>


                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($this->checkPermission(VIEW_ALL_JOURNALS) || ($this->checkPermission(VIEW_ALL_JOURNALS) && getAuthOwner()['staff_id'] == $record->journal->staff_id)) { ?>
                                    <a href="<?= $viewUrl ?>" data-mi-url="<?= $popupUrl ?>" data-mi-title="<?= __t('Journal') ?>" data-mi-size="xl"
                                       class="listing-cell-link"></a>
                                <?php } ?>
                                <?php if ($record->credit) { ?>
                                    <?php if ($treasuryData->type == App\Utils\TreasuryTypeUtil::TREASURY || empty($treasuryData->currency_code)) { ?>
                                        <div class="text-nowrap">
                                            <span class="text-amount text-amount-sm text-danger"><?= format_price_simple($record->credit) ?></span>
                                        </div>
                                        <?php if ($record->currency_code != $system_currency) { ?>
                                            <div class="text-nowrap">
                                                <?php
                                                $tooltips_title = 1 . $record->currency_code . ' = ' . ($record->currency_rate) . $system_currency;
                                                ?>
                                                <abbr class="text-light-3 fs-7" data-bs-toggle="tooltip"
                                                      data-bs-html="true"
                                                      title="<?= $tooltips_title ?>">
                                                    <span> <?= format_price(($record->currency_credit), $record->currency_code) ?></span>
                                                    <i class="mdi mdi-help-circle"></i>
                                                </abbr>
                                            </div>
                                        <?php }
                                    } else if ($treasuryData->type == App\Utils\TreasuryTypeUtil::BANK) {
                                        if ($treasuryData->currency_code != $system_currency) { ?>
                                            <div class="text-nowrap">
                                                <span class="text-amount text-amount-sm text-danger"><?= format_price($record->currency_credit, $record->currency_code) ?></span>
                                            </div>
                                        <?php } else if ($treasuryData->currency_code == $system_currency) { ?>
                                            <div class="text-nowrap">
                                                <span class="text-amount text-amount-sm text-danger"><?= format_price_simple($record->credit) ?></span>
                                            </div>
                                            <?php if ($record->currency_code != $system_currency) { ?>
                                                <div class="text-nowrap">
                                                    <?php
                                                    $tooltips_title = 1 . $record->currency_code . ' = ' . ($record->currency_rate) . $system_currency;
                                                    ?>
                                                    <abbr class="text-light-3 fs-7" data-bs-toggle="tooltip"
                                                          data-bs-html="true"
                                                          title="<?= $tooltips_title ?>">
                                                        <span> <?= format_price(($record->currency_credit), $record->currency_code) ?></span>
                                                        <i class="mdi mdi-help-circle"></i>
                                                    </abbr>
                                                    </span>
                                                </div>
                                            <?php } ?>

                                        <?php } ?>

                                    <?php } ?>


                                <?php } ?>
                            </td>
                            <?php if ($showBalanceBeforeAndAfter) { ?>
                                <td>
                                    <?php if ($this->checkPermission(VIEW_ALL_JOURNALS) || ($this->checkPermission(VIEW_ALL_JOURNALS) && getAuthOwner()['staff_id'] == $record->journal->staff_id)) { ?>
                                        <a href="<?= $viewUrl ?>" data-mi-url="<?= $popupUrl ?>" data-mi-title="<?= __t('Journal') ?>" data-mi-size="xl"
                                        class="listing-cell-link"></a>
                                    <?php } ?>
                                    <div class="text-nowrap">
                                        <span class="text-amount text-amount-sm"><?= format_price_simple($record->debit_after) ?></span>
                                    </div>
                                </td>
                            <?php } ?>


                            <?php if ($treasuryData->type == TreasuryTypeUtil::BANK and !empty($treasuryData->currency_code)) { ?>
                                <td>
                                    <?php if ($this->checkPermission(VIEW_ALL_JOURNALS) || ($this->checkPermission(VIEW_ALL_JOURNALS) && getAuthOwner()['staff_id'] == $record->journal->staff_id)) { ?>
                                        <a href="<?= $viewUrl ?>" data-mi-url="<?= $popupUrl ?>" data-mi-title="<?= __t('Journal') ?>" data-mi-size="xl"
                                        class="listing-cell-link"></a>
                                    <?php } ?>
                                    <div class="text-nowrap">
                                        <div class="vstack gap-3">
                                            <?php if (!empty($record->bank_transaction_id) || $record->status == BankTransactionsStatusUtil::MATCHED) { ?>
                                                <div class="status-circle">
                                                    <i class="bg-active"></i>
                                                    <span><?= __t('Matched') ?></span>
                                                </div>

                                                <div class="text-light-3">
                                                    <?php if (isset($record->bank_transaction->date) && !empty($record->bank_transaction->date)) { ?>
                                                        <p class="mb-0">
                                                            <?= format_date($record->bank_transaction->date) ?>
                                                            <?= (isset($record->bank_transaction->reference_id) && !empty($record->bank_transaction->reference_id)) ? $record->bank_transaction->reference_id : '#' . $record->bank_transaction_id ?>
                                                        </p>
                                                    <?php } ?>
                                                    <?php if (isset($record->bank_transaction->amount) && !empty($record->bank_transaction->amount)) { ?>
                                                        <p class="mb-0">
                                                            <span class="text-body fw-medium"><?= format_price($record->bank_transaction->amount, $treasuryData->currency_code) ?></span>
                                                        </p>
                                                    <?php } ?>
                                                </div>
                                                <?php foreach ($record->bank_transactions as $bank_transaction) { ?>
                                                    <div class="text-light-3">
                                                        <?php if (isset($bank_transaction->date) && !empty($bank_transaction->date)) { ?>
                                                            <p class="mb-0">
                                                                <?= format_date($bank_transaction->date) ?>
                                                                <?= (isset($bank_transaction->reference_id) && !empty($bank_transaction->reference_id)) ? $bank_transaction->reference_id : '#' . $bank_transaction->id ?>
                                                            </p>
                                                        <?php } ?>
                                                        <?php if (isset($bank_transaction->amount) && !empty($bank_transaction->amount)) { ?>
                                                            <p class="mb-0">
                                                                <span class="text-body fw-medium"><?= format_price($bank_transaction->amount, $treasuryData->currency_code) ?></span>
                                                            </p>
                                                        <?php } ?>
                                                    </div>
                                                <?php } ?>
                                                <?php
                                            } else { ?>
                                                <div class="status-circle">
                                                    <i class="bg-warning"></i>
                                                    <span class="fs-6"><?= __t('Not Matched') ?></span>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </td>
                            <?php } ?>


                            <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                                <?php if ($this->checkPermission(VIEW_ALL_JOURNALS) || ($this->checkPermission(VIEW_ALL_JOURNALS) && getAuthOwner()['staff_id'] == $record->journal->staff_id)) { ?>
                                    <a href="<?= $viewUrl ?>" data-mi-url="<?= $popupUrl ?>" data-mi-title="<?= __t('Journal') ?>" data-mi-size="xl"
                                       class="listing-cell-link"></a>
                                <?php } ?>
                                <?= $this->includeSection('system_transactions/partials/listing-actions', ['record' => $record]) ?>
                            </td>
                        </tr>
                    <?php } ?>


                    </tbody>
                    <?php if ($showBalanceBeforeAndAfter && (!isset($_GET['sort_field']) || (isset($_GET['sort_field']) && $_GET['sort_field'] === 'date-DESC'))) { ?>
                        <tfoot>
                        <tr>
                            <td class="listing-cell-start">
                                <span class="text-amount text-amount-sm text-body"><?= __t('Balance Before') ?></span>
                            </td>
                            <td class="listing-cell-end text-end" colspan="5">
                                <span class="text-amount text-amount-sm text-body"><?= format_price_simple($balance) ?></span>
                            </td>
                        </tr>
                        </tfoot>
                    <?php } ?>
                </table>
            </div>
        </div>
    </form>
</div>