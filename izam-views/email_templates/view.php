<?php
$extends = 'layouts/layout';
$this->addCss('create', '/webroot/frontend/assets/css/pages/email-template.css');
$this->addCss('placeholder', '/dist/app/js/pages/printable_templates/placeholders/placeholder.styles.css');
$selectedId = '';
if (isset($_GET['iframe_select_element']) && isset($_GET['iframe_select_id'])) {
    $selectedId = $_GET['iframe_select_id'];
}
$entityKey = $formData['form_record']->entity_key;

$breadCrumbsCount = count($_PageBreadCrumbs);
$redirectUrl = '';
if ($breadCrumbsCount > 1) {
    $redirectUrl = $_PageBreadCrumbs[$breadCrumbsCount-2]['link'];
}
$url = route('owner.send_template.send_smtp_email_address', [$formData['form_record']->id, $recordId]) . '?' . http_build_query(['redirect' => $redirectUrl]);

$isGlobal = $formData['form_record'] instanceof \App\Modules\Template\Models\GlobalTemplate;

?>
<?php $this->section('article-start'); ?>
    <form class="m-app-form" action="<?= $url ?>" method="POST" data-app-form="email_templates" id="form">
        <?php $this->endSection(); ?>
        <?php $this->section('page-head'); ?>

            <div class="page-head add-page-head">
                <div class="container" style="max-width: 900px">
                    <div class="page-head-row">
                        <?php
                            if ($availableTemplates && $availableTemplates->count() > 1) {
                                $helper = new \Izam\View\Form\Helper\Form\FormSplitButtonDropdowns();
                                echo $helper->renderEmailTemplate($availableTemplates);
                            }
                        ?>
                        <div class="end">
                            <div class="hstack align-items-center gap-8">
                                <button id="send" class="btn btn-success" type="submit">
                                    <i class="mdi mdi-send me-4"></i>
                                    <?= __t('Send') ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php $this->endSection(); ?>

        <?php $this->section('article-end'); ?>
    </form>
<?php $this->endSection(); ?>

    <article class="content-body" data-content-body="true">
        <div class="page-content">
            <div class="container" style="max-width: 900px">
                <div class="my-10 vstack email-template">
                    <div class="card new-message">
                        <h5 class="card-header"><?= sprintf(__t('New %s'), __t('Message')) ?></h5>
                        <div class="card-body">
                            <input type="hidden" name="is_global" value="<?=$isGlobal?>">
                            <input type="hidden" name="_token" value="<?= csrf_token() ?>">
                            <input type="hidden" name="entity_key" id="entity_key" value="<?= $entityKey ?>"/>
                            <input type="hidden" name="relations_depth" id="entity_tree_level" value="2"/>
                            <input type="hidden" name="type" value="email"/>
                            <div class="form-group d-flex" data-form-group="true">
                                <label for="subject" class="form-label">
                                    <?= __t('Subject') ?>
                                </label>
                                <div class="flex-grow-1">
                                    <label class="input-group mt-0 w-100 input-group-merge">
                                        <input name="subject"
                                               id="subject"
                                               type="text"
                                               class="form-control"
                                               data-form-rules="required"
                                               value="<?= old('subject', $emailData['subject']) ?>"
                                               data-subject="true"
                                        />
                                        <button type="button" class="input-group-text input-group-icon-end"
                                                data-bs-toggle="tooltip" title="<?= __t('Placeholders') ?>"
                                                data-subject-placeholder-btn="true">
                                            <i class="mdi mdi-tag-outline fs-10"></i>
                                        </button>
                                    </label>
                                    <div class="form-validation" data-form-validation="true">
                                        <?php if (isset($errors['subject'])) { ?>
                                            <p class="error">
                                                <?php echo $errors['subject'][0]; ?>
                                            </p>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <?php if (count($fromList) > 0): ?>
                                <div class="form-group d-flex" data-form-group="true">
                                    <label for="from" class="form-label">
                                        <?= __t('From') ?>
                                    </label>
                                    <div class="flex-grow-1">
                                        <select name="from" class="form-control" placeholder="[System Default]"
                                                data-select-input="id-1" required>
                                            <?php
                                            $selectedFrom = $formData['form_record']->emailTypeTemplate->from;
                                            foreach ($fromList as $key => $value) { ?>
                                                <option value="<?= $key ?>" <?= (($selectedFrom == $key || old('from') == $key) ? 'selected' : '') ?>>
                                                    <?= $value ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                        <div class="form-validation" data-form-validation="true"></div>
                                    </div>
                                    <script>
                                        $(document).ready(function () {
                                            var $input = $('[data-select-input="id-1"]');
                                            $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.single);
                                        });
                                    </script>
                                </div>
                            <?php endif; ?>
                            <div class="form-group d-flex" data-form-group="true" data-select-icon="true">
                                <label for="to" class="form-label">
                                    <?= __t("To") ?>
                                </label>
                                <div class="d-sm-flex flex-grow-1">

                                    <div class="flex-grow-1">
                                        <select data-select-input="id-to" multiple data-form-rules="required" name="to[]"
                                                class="form-control" placeholder="Please Select">
                                            <?php
                                            $selectedTo = $oldTo = $emailData['to'] ?? [];
                                            if (old('to')) {
                                                $oldTo = array_combine(old('to'), old('to'));
                                                $selectedTo = array_merge($oldTo, $selectedTo);
                                            }
                                            foreach ($emailTypeList as $key => $value) { ?>
                                                <option value="<?= $key ?>" <?= (array_key_exists($key, $oldTo)) ? 'selected' : '' ?>> <?= $value ?></option>
                                            <?php } ?>
                                        </select>
                                        <div class="form-validation" data-form-validation="true">
    
                                            <?php if (isset($errors['to'])) { ?>
                                                <p class="error">
                                                    <?php echo $errors['to'][0]; ?>
                                                </p>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <button class="btn btn-collapsed p-0 text-primary text-decoration-underline ms-0 me-10 me-sm-0 ms-sm-10 collapsed btn-optionCC"
                                                type="button" data-bs-toggle="collapse" data-bs-target="#optionCC"
                                                aria-expanded="false" aria-controls="optionCC">CC
                                        </button>
                                        <button class="btn btn-collapsed p-0 text-primary text-decoration-underline ms-0 me-10 me-sm-0 ms-sm-10 collapsed btn-optionBCC"
                                                type="button" data-bs-toggle="collapse" data-bs-target="#optionBCC"
                                                aria-expanded="false" aria-controls="optionBCC">BCC
                                        </button>
                                    </div>
                                </div>
                                <script>
                                    $(document).ready(function () {
                                        var $input = $('[data-select-input="id-to"]');
                                        $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.tags);
                                    });
                                </script>
                            </div>
                            <div class="collapse" id="optionCC">
                                <div class="form-group d-flex" data-form-group="true" data-select-icon="true">
                                    <label for="cc" class="form-label">CC</label>
                                    <div class="flex-grow-1">
                                        <select name="cc[]" class="form-control" multiple placeholder="Please Select"
                                                data-select-input="id-3">
                                            <?php
                                            $selectedCC = $oldCC = $emailData['cc'] ?? [];
                                            if (old('cc')) {
                                                $oldCC = array_combine(old('cc'), old('cc'));
                                                $selectedCC = array_merge($oldCC, $selectedCC);
                                            }
                                            foreach (array_merge($emailTypeList) as $key => $value) { ?>
                                                <option value="<?= $key ?>" <?= (array_key_exists($key, $oldCC)) ? 'selected' : '' ?>> <?= $value ?></option>
                                            <?php } ?>
                                        </select>
                                        <div class="form-validation" data-form-validation="true"></div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <button class="btn remove-button p-0 ms-5" type="button"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#optionCC"
                                                aria-expanded="false"
                                                aria-controls="optionCC">
                                            <i class="mdi mdi-close"></i>
                                        </button>
                                    </div>
                                    <script>
                                        $(document).ready(function () {
                                            var $input = $('[data-select-input="id-3"]');
                                            $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.tags);
                                            if ($input.val().length > 0) {
                                                $('.btn-optionCC').removeClass('collapsed');
                                                $('#optionCC').addClass('show');
                                            }
                                            $('#optionCC .btn.remove-button').on('click', function () {
                                                $input.get(0).selectize.setValue([])
                                            });
                                        });
                                    </script>
                                </div>
                            </div>
                            <div class="collapse" id="optionBCC">
                                <div class="form-group d-flex" data-form-group="true" data-select-icon="true">
                                    <label for="bcc" class="form-label">BCC</label>
                                    <div class="flex-grow-1">
                                        <select name="bcc[]" class="form-control" multiple placeholder="Please Select"
                                                data-select-input="id-4">
                                            <?php
                                            $selectedBCC = $oldBCC = $emailData['bcc'] ?? [];
                                            if (old('bcc')) {
                                                $oldBCC = array_combine(old('bcc'), old('bcc'));
                                                $selectedBCC = array_merge($oldBCC, $selectedBCC);
                                            }
                                            foreach (array_merge($emailTypeList) as $key => $value) { ?>
                                                <option value="<?= $key ?>" <?= (array_key_exists($key, $oldBCC)) ? 'selected' : '' ?>> <?= $value ?></option>
                                            <?php } ?>
                                        </select>
                                        <div class="form-validation" data-form-validation="true">
                                            <?php if (isset($errors['bcc'])) { ?>
                                                <p class="error">
                                                    <?php echo $errors['bcc'][0]; ?>
                                                </p>
                                            <?php } ?>
                                        </div>
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <button class="btn remove-button p-0 ms-5" type="button"
                                                data-bs-toggle="collapse" data-bs-target="#optionBCC"
                                                aria-expanded="false" aria-controls="optionBCC">
                                            <i class="mdi mdi-close"></i>
                                        </button>
                                    </div>
                                    <script>
                                        $(document).ready(function () {
                                            var $input = $('[data-select-input="id-4"]');
                                            $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.tags);
                                            if ($input.val().length > 0) {
                                                $('.btn-optionBCC').removeClass('collapsed');
                                                $('#optionBCC').addClass('show');
                                            }
                                            $('#optionBCC .btn.remove-button').on('click', function () {
                                                $input.get(0).selectize.setValue([])
                                            });
                                        });
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <?= __t('Message') ?>
                        </div>
                        <div class="card-body">
                            <div class="form-group mb-0" data-form-group="true">
                        <textarea class="form-control" data-editor="code" name="code">
                            <?= old('code', $emailData['code'] ?? ''); ?>
                        </textarea>
                                <div class="form-validation">
                                    <div class="error" style="color: #EB2121;">
                                        <?php if (isset($errors['code'])) { ?>
                                            <?php echo $errors['code'][0]; ?>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                            <script>
                                $(document).ready(function () {
                                    var $input = $('[data-editor="code"]');
                                    tinymce.init(
                                        Object.assign({}, window.APP.VENDORS.DEFAULTS.tinymce.template, {
                                            target: $input.get(0),
                                            content_css: '/css/saudi_riyal_symbol-regular.css',
                                            toolbar: window.APP.VENDORS.DEFAULTS.tinymce.template.toolbar + ' placeholders',
                                            setup: function (editor) {
                                                window.APP.VENDORS.DEFAULTS.tinymce.template.setup(editor);
                                                editor.ui.registry.addButton('placeholders', {
                                                    text: '<?= __t("Placeholders") ?>',
                                                    onAction: function (_) {
                                                        window.activeInput = 'body';
                                                        EntityExpolrer.load(window.entity_key.value);
                                                    }
                                                });
                                                window.bodyEditor = editor;
                                                editor.on('init', function () {
                                                    editor.dom.select('span.sar_symbol').forEach(function(span) {
                                                        var img = editor.dom.create('img', {
                                                            src: 'https://cdn.daftra.com/emails/assets/img/layout/icon-sar-symbol.png',
                                                            alt: 'SAR Symbol',
                                                            style: 'width: 24px; height: 24px; vertical-align: middle;'
                                                        });
                                                        editor.dom.replace(img, span);
                                                    });
                                                });
                                            }
                                        })
                                    );

                                    window.subjectInput = document.querySelector('[data-subject]');
                                    var subjectPlaceholderBtn = document.querySelector('[data-subject-placeholder-btn]');
                                    subjectPlaceholderBtn.addEventListener('click', function () {
                                        window.activeInput = 'subject';
                                        EntityExpolrer.load(window.entity_key.value)
                                    });
                                    EntityExpolrer.addEventListener("confirmed", (event) => {
                                        var {sendData, label, entity} = event.detail;
                                        var url = `/v2/owner/templates/generate_placeholders/${entity}`;
                                        var data = {keys: sendData, with_label: label};
                                        generatePlaceholders(url, data);
                                    });

                                    function generatePlaceholders(url, data) {
                                        fetch(url, {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                            body: JSON.stringify(data),
                                        })
                                            .then(function (response) {
                                                return response.text()
                                            })
                                            .then(function (data) {
                                                if (window.activeInput === 'body') {
                                                    window.bodyEditor.insertContent(data);
                                                } else if (window.activeInput === 'subject') {
                                                    window.subjectInput.value += $(data).text();
                                                }
                                                EntityExpolrer.close();
                                            });
                                    }

                                    document.addEventListener('close-placeholder', function () {
                                        EntityExpolrer.hide()
                                    });
                                });
                            </script>
                        </div>
                    </div>
                    <?= $this->includeSection('email_templates/attachments') ?>
                </div>
            </div>
        </div>
        <footer class="page-fixed-end"></footer>
    </article>

<?php
$this->section('page-after-js');
?>
    <div id="placeholder-modal"></div>
    <script src="/dist/app/js/pages/printable_templates/placeholders/placeholder.js?v=16"></script>
    <script src="/frontend/assets/js/pages/email-templates/attachments.js"></script>
<?= $this->includeSection('email_templates/send_test_modal') ?>

    <!-- Validate -->
    <script>
        $("form").first().each(function () {
            var $form = $(this);
            $form.validate(Object.assign({}, window.APP.VENDORS.DEFAULTS.validate.default($form), {}));
        });
        $("#viewModal form").first().each(function () {
            var $form = $(this);
            $form.validate(Object.assign({}, window.APP.VENDORS.DEFAULTS.validate.default($form), {}));
        });

        $("#send").on('click', function () {
            if ($("#form").valid()) {
                initPageLoader();
            }
        })

    </script>

<?php
$this->endSection();
?>