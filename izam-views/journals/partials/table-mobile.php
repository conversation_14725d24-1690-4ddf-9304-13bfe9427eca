<div class="d-block d-lg-none">
    <div>
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table">
                    <thead>
                    <tr>
                        <th class="listing-cell-start"></th>
                        <th></th>
                        <th width="45" class="listing-cell-end text-end">
                            <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($table->getRows() as $key => $row) {
                        $journal = $row->getData();
                        $showUrl = '/owner/journals/view/' . $journal->id;
                        $description = !empty(trim($journal->alter_description)) ? $journal->alter_description : $journal->description;
                        $staff_name = 'System';
                        $statusTitle = '';
                        if ($journal->staff->data) {
                            $staff_name = $journal->staff->name . ($journal->staff->last_name ? ' ' . $journal->staff->last_name : '');
                        }
                        if ($journal->is_automatic) {
                            $statusTitle = __t('Auto');
                        } elseif ($journal->draft) {
                            $statusTitle = __t('Draft');
                        }
                        ?>
                        <tr>
                            <td>
                                <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                                <div class="text-nowrap">
                                    <div class="title-text vstack gap-1">
                                        <span class="title-subtitle-text">#<?= $journal->number ?> - <?= format_date($journal->date) ?></span>
                                        <p><?= $description ?></p>
                                        <?php if (getAuthOwner('staff_id') != $journal->staff_id) { ?>
                                            <span class="text-dark-3"><span
                                                        class="text-black me-1">By:</span> <?= $staff_name ?>:</span>
                                        <?php }
                                        if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)) { ?>
                                            <span class="title-subtitle-text"><?= $journal->branch->name ?></span>
                                        <?php } ?>
                                    </div>
                                </div>
                            </td>
                            <td class="text-end">
                                <strong><?= $journal->currency_debit != 0 ? format_price($journal->currency_debit, $journal->currency_code) : "" ?>
                                    .</strong>
                                <br/>
                                <span class="badge text-bg-dark-3"><?= $statusTitle ?></span>
                            </td>
                            
                                <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                                    <?php if (!isset($_GET['hide_actions'])) {
                                        $this->includeSection('partials/row-actions-mobile', ['row' => $row]);
                                    } else { ?>
                                </td>
                            <?php } ?>
                        </tr>
                        <?php
                    } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>