<?php
$locale = app()->getLocale() === 'ara' ? 'ar' : 'en';
$appInstaller = resolve(\Izam\Daftra\AppManager\Services\AppInstaller::class);
/** @var \Izam\Daftra\AppManager\Services\AppSettingsService $service */
$service = resolve(\Izam\Daftra\AppManager\Services\AppSettingsService::class);

$getAuthLink = function ($redirectUrl, $appId) use ($service, $appInstaller) {
    if ($service->isSettingsRequired($appId) && $appInstaller->isSettingsRecordIsEmpty($appId, getCurrentSite('id'))) {
        return route('owner.apps-manager.settings', $appId);
    }
    return 'https://' . Portal_Full_Name . '/app/authorize?' . http_build_query([
        'redirect_url' => $redirectUrl,
        'token' => __createToken(getCurrentSite('id')),
        'site_id' => getCurrentSite('id'),
        'app_id' => $appId,
    ]);
};
?>

<!-- add styling in mobile screen  -->
<style>
   @media (max-width: 993px) {
    .plugin-item{
        padding:10px !important;
    }
    .plugin-item > div{
        gap:10px;
    }
    .left-side-container{
        max-width: 80px;
    }
    .image-container {
        max-width: 80px;
    }
    }
</style>

<div class="container-full">
    <div class="my-10 vstack gap-10">
        <div class="plugin-group">
            <div class="plugin-group-card">
                <div class="plugin-group-body">
                    <div class="app-listing-header">
                        <?= __t('My External Apps') ?>
                    </div>
                    <div class="app-grid-container">
                        <div class="app-grid">
                            <?php
                            /**@var \Izam\Daftra\AppManager\Models\AppSite $appSite */
                            foreach ($apps as $appSite):
                                $cardData = [
                                    'app' => $appSite->app,
                                    'locale' => $locale,
                                    'authLink' => $getAuthLink(explode(',', $appSite->app->redirect)[0], $appSite->app->id),
                                    'activeSiteAppIds' => $activeSiteAppIds,
                                    'installedAppsIds' => $installedAppsIds,
                                    'portalBaseUrl' => $portalBaseUrl,
                                    'isInstalledView' => true,
                                    'expires_at' => $appSite->expires_at,
                                    'paymentRequired' => $appSite->isPaymentRequired(),
                                ];
                                extract($cardData);
                                include __DIR__ . '/app-card.php';
                            endforeach; ?>

                        </div>
                        <?php if (count($apps) === 0): ?>
                            <?php
                            include __DIR__ . '/empty-result.php'; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <?php if ($topApps->count() > 0): ?>
                <div class="app-listing-header">
                    <?= __t('Top Apps') ?>
                </div>
                <div class="app-grid-container">
                    <div class="app-grid">
                        <?php foreach ($topApps as $app):
                            include __DIR__ . '/small-app-card.php';
                        endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include __DIR__ . '/rating-modal.php'; ?>

<script>
    var csrfToken = '<?= csrf_token() ?>';
    const sendReviewTitle = '<?= __t('Send Review for') ?>';
    const emptyRateErrorMessage = '<?= __t('The rate is required') ?>';
    const editRatingText = '<?= __t('Edit rating') ?>';
    const ratingTexts = [
        '<?= __t("Click to rate") ?>',
        '<?= __t("Poor") ?>',
        '<?= __t("Fair") ?>',
        '<?= __t("Good") ?>',
        '<?= __t("Very Good") ?>',
        '<?= __t("Excellent") ?>',
    ];
</script>
<script src="/frontend/assets/js/pages/app-manager/index/index.js?v=-6"></script>
<script src="/frontend/assets/js/pages/app-manager/rating-modal.js?v=-1" defer></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.clickable-card').forEach(function(card) {
            card.addEventListener('click', function(e) {
                const externalUrl = this.getAttribute('data-external-url');
                if (externalUrl) {
                    window.open(externalUrl, '_blank');
                }
            });
        });
    });
</script>