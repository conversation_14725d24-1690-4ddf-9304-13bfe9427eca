<?php

/**
 * App Card Component
 *
 * @var \Izam\Daftra\AppManager\Models\App $app The app data object.
 * @var string $locale Current locale ('en', 'ar', etc.).
 * @var string $portalBaseUrl Base URL for portal links.
 */

$name = $app->getName($locale);
$lang = CurrentSiteLang('code2') == 'ara' ? 'ar' : 'en';
$marketPlaceUrl = APP_MANAGER_URL. "/$lang/marketplace/show/";

?>
<article class="card plugin-item cursor-pointer clickable-card" data-plugin-id="<?= htmlspecialchars($app->id ?? ''); ?>" data-plugin-error-url='{"2": "<?= route('owner.apps-manager.settings', ($app->id ?? '')); ?>", "4": "<?= htmlspecialchars($portalBaseUrl . sprintf('/sites/purchase_app/%d/%d', getCurrentSite('id'), ($app->id ?? ''))) ?>"}'
    data-external-url="<?= htmlspecialchars($app->external_url ?? $marketPlaceUrl . $app->id); ?>">
    <!-- Header -->
    <div class="small-plugin-item-body">
        <div class="image-container flex-shrink-0">
            <img src="<?= htmlspecialchars($app->image_url ?? ('https://placehold.co/100x100/blue/white?text=' . urlencode(substr($name, 0, 2)))); ?>" />
        </div>
        <div class="flex-grow-1 min-width-0">
            <div class="d-flex align-items-start justify-content-between w-full">
                <div class="flex-grow-1 min-width-0">
                    <!-- App Name -->
                    <div class="">
                        <h2 class="plugin-item-name">
                            <?= htmlspecialchars($name); ?>
                        </h2>
                    </div>
                    <!-- Categories and availability -->
                    <div class="d-flex align-items-center my-2">
                        <?php
                        $categoriesList = [];
                        if (isset($app->categories) && !empty($app->categories)) {
                            foreach ($app->categories as $cat) {
                                $categoriesList[] = is_object($cat) && method_exists($cat, 'getName') ? $cat->getName() : (is_string($cat) ? $cat : '');
                            }
                        }
                        $categoriesList = array_filter($categoriesList);
                        $totalCategories = count($categoriesList);
                        $remainingCount = $totalCategories > 1 ? $totalCategories - 1 : 0;
                        foreach ($categoriesList as $index => $category) {
                            if ($index < 1) {
                                echo '<span class="category-pill me-2">' . htmlspecialchars($category) . '</span>';
                            }
                        }
                        if ($remainingCount > 0) {
                            $remainingCategories = array_slice($categoriesList, 1);
                            $tooltipContent = htmlspecialchars(implode(', ', $remainingCategories));
                            echo '<span class="category-pill" data-bs-toggle="tooltip" data-bs-placement="bottom" title="' . $tooltipContent . '">+' . $remainingCount . '</span>';
                        }
                        ?>
                    </div>
                </div>
                <div class="plugin-item-activate-button" onclick="event.stopPropagation();">
                    <a href="/v2/owner/apps-manager/install/<?=$app->id?>"
                        class="btn pe-8 btn-primary">
                        <i class="mdi mdi-download"></i>
                        <?= __t('Install'); ?>
                    </a>
                </div>
            </div>
            <div class="d-flex align-items-center justify-content-between w-full">
                <!-- Rating and review -->
                <div>
                    <?php if ($app->rating_count != '0'): ?>
                        <div class="d-flex align-items-center">
                            <i class="mdi mdi-star text-warning me-2"></i>
                            <span class="rating-value me-2"><?= htmlspecialchars($app->avg_rating) ?></span>
                            <span class="rating-count m-0 p-0"><?= '(' . $app->rating_count . ')' ?></span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Price -->
                <div class="price-container">
                    <?php if ($app->price && $app->monthly_subscription == 1): ?>
                        <p class="price-value">
                            $<?= (int)($app->price ?? 0) ?>
                            <span class="price-period">
                                / <?= __t('Month') ?>
                            </span>
                        </p>
                    <?php elseif ((int)($app->price ?? 0) == 0): ?>
                        <span class="price-value price-free">
                            <?= __t('Free') ?>
                        </span>
                    <?php else: ?>
                        <span class="price-value">
                            $<?= (int)($app->price ?? 0) ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</article>