<?php

/**
 * App Card Component
 *
 * @var \Izam\Daftra\AppManager\Models\App $app The app data object.
 * @var string $locale Current locale ('en', 'ar', etc.).
 * @var string $authLink Authorization link.
 * @var array $activeSiteAppIds Array of active app IDs for the current site.
 * @var array $installedAppsIds Array of all installed app IDs for the current site.
 * @var string $portalBaseUrl Base URL for portal links.
 * @var bool $isInstalledView True if the app is from "My Apps", false if from "Store".
 * @var string|null $expires_at App expiration date, if applicable.
 * @var bool $paymentRequired True if payment is required for the app.
 */


use Illuminate\Support\Carbon;

$description = $app->getDescription($locale);
$name = $app->getName($locale);

?>
<div class="card plugin-item" data-plugin-id="<?= htmlspecialchars($app->id ?? ''); ?>" data-plugin-error-url='{"2": "<?= route('owner.apps-manager.settings', ($app->id ?? '')); ?>", "4": "<?= htmlspecialchars($portalBaseUrl . sprintf('/sites/purchase_app/%d/%d', getCurrentSite('id'), ($app->id ?? ''))) ?>"}'>
    <!-- Header -->
    <div class="plugin-item-header">
        <div class="flex-grow-1 min-width-0">
            <!-- Categories and availability -->
            <div class="d-flex align-items-center mb-6">
                <?php
                $categoriesList = [];
                if (isset($app->categories) && !empty($app->categories)) {
                    foreach ($app->categories as $cat) {
                        $categoriesList[] = is_object($cat) && method_exists($cat, 'getName') ? $cat->getName() : (is_string($cat) ? $cat : '');
                    }
                }
                $categoriesList = array_filter($categoriesList);
                $totalCategories = count($categoriesList);
                $remainingCount = $totalCategories > 1 ? $totalCategories - 1 : 0;
                foreach ($categoriesList as $index => $category) {
                    if ($index < 1) {
                        echo '<span class="category-pill me-2">' . htmlspecialchars($category) . '</span>';
                    }
                }
                if ($remainingCount > 0) {
                    $remainingCategories = array_slice($categoriesList, 1);
                    $tooltipContent = htmlspecialchars(implode(', ', $remainingCategories));
                    echo '<span class="category-pill" data-bs-toggle="tooltip" data-bs-placement="bottom" title="' . $tooltipContent . '">+' . $remainingCount . '</span>';
                }
                ?>
            </div>

            <!-- App Name -->
            <h2 class="plugin-item-name">
                <?= htmlspecialchars($name); ?>
            </h2>
        </div>
        <div class="image-container flex-shrink-0">
            <img src="<?= htmlspecialchars($app->image_url ?? ('https://placehold.co/100x100/blue/white?text=' . urlencode(substr($name, 0, 2)))); ?>" />
        </div>
    </div>

    <div class="plugin-item-content">
        <!-- Description -->
        <p class="description-text">
            <?= htmlspecialchars($description); ?>
        </p>

        <div class="d-flex align-items-center justify-content-between">
            <!-- Rating and review -->
            <div class="d-flex align-items-md-start">
                <button class="btn btn-link p-0 text-decoration-none rating-button"
                    <?php if ($isInstalledView): ?>
                    data-bs-toggle="modal"
                    data-bs-target="#modalSimple"
                    <?php else: ?>
                    disabled
                    <?php endif; ?>
                    data-app-id="<?= htmlspecialchars($app->id ?? ''); ?>"
                    data-app-name="<?= htmlspecialchars($name); ?>">
                    <div class="d-flex flex-column align-items-start">
                        <div class="d-flex align-items-center">
                            <?php if ($app->rating_count == '0'): ?>
                                <span class="rating-value"><?= __t('No reviews yet') ?></span>
                            <?php else: ?>
                                <i class="mdi mdi-star text-warning me-2"></i>
                                <span class="rating-value"><?= htmlspecialchars($app->avg_rating) ?></span>
                            <?php endif; ?>
                        </div>
                        <span class="rating-count"><?= htmlspecialchars($app->rating_count == '0' ? __t('Click to add a review') : '(' . $app->rating_count . ' ' . __t('reviews') . ')') ?></span>
                    </div>
                </button>
            </div>

            <!-- Price -->
            <?php if ($isInstalledView && !empty($expires_at)): ?>
                <div class="price-container">
                    <p class="price-value">
                        <span class="price-value-number">
                            $<?= (int)($app->price ?? 0) ?>
                        </span>
                        <?php if ($app->monthly_subscription): ?>
                            <span>
                                / <?= __t('Month') ?>
                            </span>
                        <?php endif; ?>
                    </p>
                    <p class="price-value-expiration">
                        <?php
                        if ($expires_at < Carbon::now('UTC')): ?>
                            <span class="badge bg-danger"><?= __t('Expired') ?></span>
                        <?php else: ?>
                            <?= __t('Valid until') ?> <?= formatForView($expires_at, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) ?>
                        <?php endif; ?>
                    </p>
                </div>
            <?php elseif ((int)($app->price ?? 0) == 0): ?>
                <div class="price-container">
                    <span class="price-value-number">
                        <?= __t('Free') ?>
                    </span>
                </div>
            <?php else: ?>
                <div class="price-container">
                    <span class="price-value-number">
                        $<?= (int)($app->price ?? 0) ?>
                        <?php if ($app->monthly_subscription): ?>
                            <span>
                                / <?= __t('Month') ?>
                            </span>
                        <?php endif; ?>
                    </span>
                    <?php if ($isInstalledView):
                    ?>
                        <span class="price-value-expiration">
                            &nbsp;<?= __t('Requires Payment') ?>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="plugin-item-footer">
        <!-- Settings Button -->
        <div>
            <?php if (function_exists('showAppSettingsToggleButton') && showAppSettingsToggleButton($app, $installedAppsIds)): ?>
                <a href="<?= route('owner.apps-manager.settings', ($app->id ?? '')) ?>"
                    class="btn p-0 d-flex align-items-center justify-content-center">
                    <i class="mdi mdi-cog me-2"></i>
                    <?= __t('Settings') ?>
                </a>
            <?php endif; ?>
        </div>

        <?php
        $isActive = in_array(($app->id ?? null), $activeSiteAppIds);
        $appId = $app->id ?? uniqid('app_');
        ?>

        <!-- Hidden Checkbox that drives the logic -->
        <div style="display: none;">
            <input
                data-link="<?= !$paymentRequired ? htmlspecialchars($authLink, ENT_QUOTES, 'UTF-8') : '' ?>"
                onchange="typeof redirect === 'function' && redirect(this)"
                value="0"
                type="checkbox"
                class="plugin-item-checkbox"
                <?= $isActive ? 'checked' : ''; ?>
                id="<?= htmlspecialchars($appId); ?>"
                name="<?= htmlspecialchars($appId); ?>">
        </div>

        <!-- Visible Button to control the checkbox -->
        <?php if ($isInstalledView): ?>
            <div class="plugin-item-activate-button">
                <button type="button"
                    id="button-<?= htmlspecialchars($appId); ?>"
                    class="btn pe-8 <?= $isActive ? 'btn-secondary' : 'btn-success'; ?>"
                    onclick="document.getElementById('<?= htmlspecialchars($appId); ?>').click();">
                    <i class="mdi mdi-<?= $isActive ? 'pause' : 'play'; ?>"></i>
                    <?= $isActive ? __t('Deactivate') : __t('Activate'); ?>
                </button>
            </div>
        <?php else: ?>
            <div class="plugin-item-activate-button">
                <a href="<?= $authLink ?>"
                    class="btn pe-8 btn-primary">
                    <i class="mdi mdi-download"></i>
                    <?= __t('Install'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>