<?php
$elements = $filtersForm->getElements();
$hasHiddenElements = false;
foreach ($elements as $key => $element) {
    if ($element->getOption('advanced')) {
        $hasHiddenElements = true;
        break;
    }
}
?>
<div class="mb-10">
    <div class="collapse filters" data-filters="true">
        <form action="" method="GET" id="<?= $entityKey ?? ''?>">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-start justify-content-between">
                        <div>
                            <p class="pt-5 mb-0" data-filters-hidden="true"><?= __t('Search & Filters') ?></p>
                            <div class="d-none" id="filter-value" data-filters-hidden="true">
                                <div class="d-flex flex-column flex-lg-row align-items-start gap-4">
                                    <div class="hstack flex-wrap pb-3 py-lg-3 gap-2" data-id="filter-head">

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="btn-group-controller" role="group">
                            <?php if ($hasHiddenElements) { ?>
                                <button type="button" class="btn btn-filter" data-bs-toggle="collapse"
                                        data-bs-target="[data-filters-advanced]" aria-expanded="false"
                                        aria-controls="lf-advanced"
                                        data-filters-hidden="true">
                                    <i class="mdi mdi-tune me-4"></i>
                                    <span aria-expanded="false"><?= __t('Advanced') ?></span>
                                    <span aria-expanded="true"><?= __t('Simple') ?></span>
                                </button>
                                <span class="separator-v" data-bs-target="[data-filters-advanced]"></span>
                            <?php } ?>
                            <button type="button" class="btn btn-filter" data-bs-toggle="collapse"
                                    data-bs-target="[data-filters-collapse]" aria-expanded="true"
                                    aria-controls="lf-collapse">
                                <i class="mdi mdi-unfold-more-horizontal me-4" aria-expanded="false"></i>
                                <i class="mdi mdi-unfold-less-horizontal me-4" aria-expanded="true"></i>
                                <span aria-expanded="false"><?= __t('Show') ?></span>
                                <span aria-expanded="true"><?= __t('Hide') ?></span>
                            </button>
                        </div>

                    </div>
                </div>

                <div class="collapse show" data-id="filter-inputs" data-filters-collapse="true">
                    <div class="card-body">
                        <div class="row">
                            <?php

                            use Izam\View\Form\Helper\FormFilterCollection;

                            $form_helper = new FormFilterCollection();
                            $filtersForm->prepare();
                            echo $form_helper->render($filtersForm);
                            ?>
                            <?php if (isset($_GET['iframe'])) { ?>
                                <input type="hidden" name="iframe" value="1">
                            <?php } ?>

                            <?php echo $this->includeSection('entitiy/partials/custom-js') ?>

                            <div class="col-lg-4 ms-auto">
                                <div class="form-group d-flex align-items-center justify-content-end gap-2">
                                    <a href="<?= $reset_filters_url ?>"
                                       class="btn btn-clear"><?= __t('Clear All') ?></a>
                                    <button type="submit" class="btn btn-search"><?= __t('Search') ?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" name="filter_search" value="1">
            <input type="hidden" name="show_filters" value="1">
        </form>
    </div>
</div>