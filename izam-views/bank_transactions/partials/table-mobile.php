<?php

use Izam\Daftra\Common\Utils\BankTransactionsStatusUtil;

?>
<div class="d-block d-lg-none">
    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table">
                <thead>
                <tr>
                    <th class="listing-cell-start"></th>
                    <th></th>
                    <th width="45" class="listing-cell-end text-end">
                        <?= $this->includeSection('bank_transactions/partials/sort-options') ?>
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($pagination as $record) { ?>
                    <tr>
                        <td>
                            <div class="title-text vstack gap-3">
                                <p><?= $record->reference_id ?></p>
                                <small class="title-subtitle-text"><?= format_date($record->date) ?></small>
                            </div>
                            <div class="mt-3">
                                <div class="status-circle">
                                    <i class="bg-<?= BankTransactionsStatusUtil::getStatusCssClass($record->status) ?>"></i>
                                    <span class="fs-6"><?= BankTransactionsStatusUtil::getStatus($record->status) ?></span>
                                </div>
                                <?php foreach ($record->systemTransactions as $systemTransactions) { ?>
                                    <div class="mt-2 fs-6 text-light-3">
                                        <p class="mb-0">- <?= __t('Journal') ?>
                                            <a href="/owner/journals/view/<?= $systemTransactions->journal_id ?>">#<?= $systemTransactions->journal->number ?></a>
                                        </p>
                                        <p class="mb-0"><?= $systemTransactions->description ?>
                                            <span class="text-body fw-medium"><?= format_price($systemTransactions->debit > 0 ? $systemTransactions->debit : $systemTransactions->credit, $record->currency_code) ?></span>
                                        </p>
                                    </div>
                                <?php } ?>
                                <?php if ($record->systemTransaction) { ?>
                                    <div class="mt-2 fs-6 text-light-3">
                                        <p class="mb-0">- <?= __t('Journal') ?>
                                            <a href="/owner/journals/view/<?= $record->systemTransaction->journal_id ?>">#<?= $record->systemTransaction->journal->number ?></a>
                                        </p>
                                        <p class="mb-0"><?= $record->systemTransaction->description ?>
                                            <span class="text-body fw-medium"><?= format_price($record->systemTransaction->debit > 0 ? $record->systemTransaction->debit : $record->systemTransaction->credit, $record->currency_code) ?></span>
                                        </p>
                                    </div>
                                <?php } ?>
                            </div>
                        </td>
                        <td>
                            <?php if (!empty($record->deposit_amount)) { ?>
                                <p class="mb-0">
                                    <strong class="font-weight-medium fs-6 text-success"><?= format_price_simple($record->deposit_amount, $record->bank->currency_code) ?></strong>
                                </p>
                                <small class="text-light-3"><?= __t('Deposit') ?></small>
                                <div class="pt-2"></div>
                            <?php } ?>

                            <?php if (!empty($record->withdraw_amount)) { ?>
                                <p class="mb-0">
                                    <strong class="font-weight-medium fs-6 text-danger"<?= format_price_simple($record->withdraw_amount, $record->bank->currency_code) ?> </strong>
                                </p>
                                <small class="text-light-3"><?= __t('Withdraw') ?></small>
                            <?php } ?>
                        </td>
                        <td class="text-end h-0" data-lt-dropdown-cell="true">
                            <?= $this->includeSection('bank_transactions/partials/listing-actions', ['record' => $record]) ?>
                        </td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
