<?php

use Izam\Daftra\Common\Utils\BankTransactionsStatusUtil;

?>

<div class="d-none d-lg-block">
    <form action="" method="POST" data-lt-form="true">
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table" data-lt="true">
                    <colgroup>
                        <col/>
                        <col/>
                        <col/>
                        <col/>
                        <col/>
                        <col/>
                    </colgroup>
                    <thead data-lt-head="true">
                    <tr>
                        <th class="listing-cell-start">
                            <div class="d-flex align-items-center gap-6">
                                <div class="listing-cell-check listing-cell-check-sm">
                                    <div class="dropdown" data-lt-dropdown="true">
                                        <button class="btn btn-link dropdown-toggle dropdown-toggle-2 px-0"
                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                type="button">
                                            <input class="form-check-input" type="checkbox"
                                                   data-lc-toggle="current-page"/>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                        data-lc-check="none">
                                                    <i class="mdi mdi-close text-primary"></i>
                                                    <span><?= __t('None') ?></span>
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                        data-lc-check="current-page">
                                                    <i class="mdi mdi-check-underline text-primary"></i>
                                                    <span><?= __t('All') ?>  <small>(<?= __t('Current Page') ?>)</small></span>
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                        data-lc-check="all-filtered-pages">
                                                    <i class="mdi mdi-check-all text-primary"></i>
                                                    <span><?= __t('All Filtered') ?> <small>(<?= __t('All Pages') ?>)</small></span>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div>
                                    <?= __t('Date') ?>
                                </div>
                            </div>
                        </th>
                        <th><?= __t('Description') ?></th>
                        <th><?= __t('Deposit') ?></th>
                        <th><?= __t('Withdraw') ?></th>
                        <th width="50"><?= __t('Status') ?></th>
                        <th width="45" class="listing-cell-end text-end">
                            <?= $this->includeSection('bank_transactions/partials/sort-options') ?>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($pagination as $record) { ?>
                        <tr tabindex="0">
                            <td class="listing-cell-start">
                                <div class="text-nowrap">
                                    <div class="d-flex gap-6">
                                        <div class="listing-cell-check listing-cell-check-sm">
                                            <input class="form-check-input" type="checkbox" name="ids[]"
                                                   value="<?= $record->id ?>" data-lc-item="true"/>
                                        </div>
                                        <div class="title-text vstack gap-3">
                                            <p><?= $record->reference_id ?></p>
                                            <span class="title-subtitle-text"><?= format_date($record->date) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-long text-long-2-lines" data-bs-toggle="tooltip"
                                     data-bs-html="true"
                                     data-bs-title="<?= $record->description ?>"
                                     style="max-width: 300px;">
                                    <?= $record->description ?>
                                </div>
                            </td>
                            <td>
                                <div class="text-nowrap">
                                    <span class="text-amount text-amount-sm text-success"><?= !empty($record->deposit_amount) ? format_price_simple($record->deposit_amount, $record->bank->currency_code) : '' ?></span>
                                </div>
                            </td>
                            <td>
                                <div class="text-nowrap">
                                    <span class="text-amount text-amount-sm text-danger"><?= !empty($record->withdraw_amount) ? format_price_simple($record->withdraw_amount, $record->bank->currency_code) : '' ?></span>
                                </div>
                            </td>
                            <td>
                                <div class="text-nowrap">
                                    <div class="vstack gap-3">
                                        <div class="status-circle">
                                            <i class="bg-<?= BankTransactionsStatusUtil::getStatusCssClass($record->status) ?>"></i>
                                            <span><?= BankTransactionsStatusUtil::getStatus($record->status) ?></span>
                                        </div>
                                        <?php foreach ($record->systemTransactions as $systemTransactions) { ?>
                                            <div class="text-light-3">
                                                <p class="mb-0">- <?= __t('Journal') ?>
                                                    <a href="/owner/journals/view/<?= $systemTransactions->journal_id ?>">#<?= $systemTransactions->journal->number ?></a>
                                                </p>
                                                <span>,&nbsp;</span>
                                                <p class="mb-0 text-wrap"><?= $systemTransactions->description ?>
                                                    <span class="text-body fw-medium"><?= format_price($systemTransactions->debit > 0 ? $systemTransactions->debit : $systemTransactions->credit, $record->currency_code) ?></span>
                                                </p>
                                            </div>
                                        <?php } ?>
                                        <?php if ($record->systemTransaction) { ?>
                                            <div class="text-light-3">
                                                <p class="mb-0">- <?= __t('Journal') ?>
                                                    <a href="/owner/journals/view/<?= $record->systemTransaction->journal_id ?>">#<?= $record->systemTransaction->journal->number ?></a>
                                                </p>
                                                <span>,&nbsp;</span>
                                                <p class="mb-0 text-wrap"><?= $record->systemTransaction->description ?>
                                                    <span class="text-body fw-medium"><?= format_price($record->systemTransaction->debit > 0 ? $record->systemTransaction->debit : $record->systemTransaction->credit, $record->currency_code) ?></span>
                                                </p>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>
                            </td>
                            <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                                <?= $this->includeSection('bank_transactions/partials/listing-actions', ['record' => $record]) ?>
                            </td>
                        </tr>
                    <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
