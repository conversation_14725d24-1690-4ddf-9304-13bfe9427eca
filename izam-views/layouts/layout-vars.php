<?php
/**
 * #################### Layout Functions ####################
 * TODO: move to package that works on both: cake and laravel izam-views
 */

if (!function_exists('css_gradient_to_hex')) {
    function css_gradient_to_hex($gradient)
    {
        preg_match('/(?<=\#)(.*?)(?=\ )/', $gradient, $matches);
        if (!is_null($matches) && !empty($matches)) {
            return '#' . $matches[0];
        }
        return '#000000';
    }
}

/**
 * #################### Layout Variables ####################
 */

$dir = is_rtl() ? 'rtl' : 'ltr';
$theme = isset($_COOKIE['theme']) && !empty($_COOKIE['theme']) ? $_COOKIE['theme'] : "light";
$is_sidebar_shrinked = isset($_COOKIE['menuClass']) && !empty($_COOKIE['menuClass']) && $_COOKIE['menuClass'] == 'shrinked-sidebar' ? "true" : (isset($_COOKIE['sidebarType']) && !empty($_COOKIE['sidebarType']) && $_COOKIE['sidebarType'] == 'shrink' ? "true" : "false");
$this->sharedVars['iframe'] = $iframe = isset($_GET['iframe']) ? true : false;
$this->sharedVars['title'] = $title = (isset($title_for_layout) && !empty($title_for_layout) ? ucwords($title_for_layout . ' - ') : '') . (getCurrentSite('business_name'));
$this->sharedVars['favicon'] = $favicon = strtolower(Site_Full_name_NoSpace);
$site = getCurrentSite();
$this->sharedVars['theme_color'] = $theme_color = '#' . ($site['font_color'] ? $site['font_color'] : 'ffffff');
$this->sharedVars['theme_bg'] = $theme_bg = ($site['theme_color'] ? (strpos($site['theme_color'], 'linear-gradient') !== false ? css_gradient_to_hex($site['theme_color']) : '#' . $site['theme_color']) : '#000000');
// JS Variables
$this->sharedVars['date_format_for_js'] = $date_format_for_js = getDateFormats('std')[getCurrentSite('date_format') == "" ? "0" : getCurrentSite('date_format')];
$this->sharedVars['date_format_moment_for_js'] = $date_format_moment_for_js = getDateFormats('moment_js')[getCurrentSite('date_format') == "" ? "0" : getCurrentSite('date_format')];
$this->sharedVars['date_show_hijri_for_js'] = $date_show_hijri_for_js = getDateFormats('is_hijiri')[getCurrentSite('date_format')] ? true : false;
$this->sharedVars['layout_data_for_js'] = $layout_data_for_js = (object)[
    'isIframe' => $iframe,
];
$this->sharedVars['user_data_for_js'] = $user_data_for_js = (object)[
    'staffId' => getAuthOwner('staff_id'),
    'branchId' => getCurrentBranchID(),
    'dateFormat' => $this->sharedVars['date_format_for_js'],
    'dateFormatMoment' => $this->sharedVars['date_format_moment_for_js'],
    'dateShowHijri' => $this->sharedVars['date_show_hijri_for_js'],
    'currency' => getCurrentSite('currency_code'),
    'country' => getCurrentSite('country_code'),
];
$this->sharedVars['i18n_data_for_js'] = $i18n_data_for_js = [
    'This Field is Required' => __t('This Field is Required'),
    'This value should be between %s and %s.' => __t('This value should be between %s and %s.'),
    'This value should be greater than or equal to %s.' => __t('This value should be greater than or equal to %s.'),
    'This value should be lower than or equal to %s.' => __t('This value should be lower than or equal to %s.'),
    'Max file size %d %s' => __t('Max file size %d %s'),
    'MB' => __t('MB'),
    'Allowed file types' => __t('Allowed file types: (%s)'),
    'Please enter 1 keyword or more to search' => __t('Please enter 1 keyword or more to search'),
    'No results found' => __t('No results found'),
    'Clear' => __t('Clear'),
    'takenLeaves' => __t('Leaves Taken'),
    'futureLeaves' => __t('Future'),
    'noLeavesForEmployee' => __t('There is no leave balance available for the selected employee'),
    'Drop image here or' => __t('Drop image here or'),
    'select from your computer' => __t('select from your computer'),
    'View Profile' => __t('View Profile'),
    'General Information' => __t('General Information'),
    'Email' => __t('Email'),
    'Status' => __t('Status'),
    'Branch' => __t('Branch'),
    'Phone Number' => __t('Phone Number'),
    'Job Information' =>  __t('Job Information'), 
    'Department' =>  __t('Department'), 
    'Designation (Job Title)' =>  __t('Designation (Job Title)'), 
    'Direct Manager' =>  __t('Direct Manager'),
    'Manages' =>  __t('Manages'),
    'Unknown Error' => __t('Unknown Error'),   
    "Add a Direct Report" => __t('Add a Direct Report'),
    "Select an employee" => __t('Select an employee'), 
    "Choose a Reporting Employee" => __t('Choose a Reporting Employee'),  
    "Assign or Replace Manager" => __t('Assign or Replace Manager'), 
    "Assign or Replace Manager for Selected Employees" => __t('Assign or Replace Manager for Selected Employees'), 
    'Direct Reports' =>  __t('Direct Reports'),
    "Choose a Report" => __t('Choose a Report'),
    "Choose Direct Manager" => __t('Choose Direct Manager'), 
    "Select a Manager" => __t('Select a Manager'), 
    "Are you sure you want to replace %s manager from %s to %s?" => __t("Are you sure you want to replace %s manager from %s to %s?"), 
    "Are you sure you want to assign %s as the manager of %s?" => __t("Are you sure you want to assign %s as the manager of %s?"), 
    "Please select a valid manager" => __t('Please select a valid manager'), 
    "Yes, Assign" => __t('Yes, Assign'), 
    "Please select another manager" => __t('Please select another manager'), 
    "Please Wait Till We Prepare Your PDF & Start Your Download" => __t('Please Wait Till We Prepare Your PDF & Start Your Download'),
    "Employees" => __t('Employees'), 
    "Yes" => __t('Yes'), 
    "No" => __t('No'), 
    "All" => __t('All'),
    "Unassigned Employees" => __t('Unassigned Employees'),
    "No Dept. set" => __t('No Dept. set'),
    "Unassigned Employees" => __t('Unassigned Employees'),
    "Download" => __t('Download'),
    "Edit" => __t('Edit'),
    "Delete" => __t('Delete'),
    "Deactivate" => __t('Deactivate'),
    "Activate" => __t('Activate'),
];
$this->sharedVars['i18n_data_for_js'] = $i18n_data_for_js = array_merge($this->sharedVars['i18n_data_for_js'], $this->getJsTranslations());
$this->sharedVars['other_data_for_js'] = $other_data_for_js = (object)[
    // 'currencies' => include APP . 'config' . DS . 'currencies.php',
];
$this->sharedVars['util_names_for_js'] = $util_names_for_js = (object)[
    'DATA' => (object)[],
    'TEXT' => (object)[],
    'DATE' => (object)[],
    'CURRENCY' => (object)[],
];
$this->sharedVars['app_data_for_js'] = $app_data_for_js = [
    'LAYOUT' => $this->sharedVars['layout_data_for_js'],
    'USER' => $this->sharedVars['user_data_for_js'],
    'I18N' => $this->sharedVars['i18n_data_for_js'],
    'DATA' => $this->sharedVars['other_data_for_js'],
    'UTILS' => $this->sharedVars['util_names_for_js'],
];
// Navigation Shortcuts
if (!isset($izamNavigation)) {
    $this->sharedVars['izamNavigation'] = $izamNavigation = new Izam\Navigation\Navigation();
}

?>