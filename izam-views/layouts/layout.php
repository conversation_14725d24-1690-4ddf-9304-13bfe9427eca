<?php include('layout-vars.php');

$extraContainerClass = $this->sharedVars['extraContainerClass'] ?? '';
$i18n_data_for_js= $this->sharedVars['i18n_data_for_js'] ?? [];
?>
<!DOCTYPE html>
<html lang="en" dir="<?= $dir ?>" data-bs-theme="<?= $theme ?>" data-sidebar-shrink="<?= $is_sidebar_shrinked ?>">

<head>
    <?= $this->includeSection('partials/layouts/app-head') ?>
</head>

<body>

<?php
if (!$iframe) {
    if ($user['type'] == 'admin') {
        $this->includeSection("partials/layouts/menu-admin");
    } else {
        $this->includeSection("partials/layouts/menu", ['menus' => get_menus_data($this, $user['type'], getAuthClient() ? false : getAuthStaff('role_id'))]);
    }
}
?>

<main class="content" data-content="true">
    <?php if (!$iframe) { ?>
        <?= $this->includeSection('partials/layouts/header') ?>
    <?php } ?>
    <article class="content-body" data-content-body="true">
        <?= $this->yieldSection('article-start'); ?>
        <header class="page-fixed-start" data-page-start="true">
            <?= $this->yieldSection('page-head'); ?>
        </header>
        <div class="page-content">
            <!-- Alerts -->
            <div class="container <?= $extraContainerClass?>">
                <div class="alert-stack">
                    <?= $this->includeSection("partials/layouts/session-messages"); ?>
                    <?= $this->includeSection("partials/layouts/limit_exceeded"); ?>
                    <?= $this->yieldSection('alert-stack'); ?>
                </div>
            </div>
            <!-- /Alerts -->
            <!-- Content -->
            <?php
            if (strtolower($site['country_code']) == 'sa') {
                $content = str_replace([' ر.س'], "<span style=' display:inline-block;font-family: saudi_riyal_symbol, sans-serif !important; ' class='sar_symbol '> &#x5143;</span>", $content);
                if (!function_exists('replace_sr_symbol')) {
                    function replace_sr_symbol($matches)
                    {
                        return "<span style='display: inline-block; direction: ltr '><span style='display:inline-block;font-family: saudi_riyal_symbol, sans-serif !important;' class='sar_symbol'>&#x5143; </span>" . $matches[1] . '</span>';
                    }
                }
                $content = preg_replace_callback('/([-?\d.,]+) SR/', 'replace_sr_symbol', $content);
            }
            echo $content;
            ?>
            <!-- /Content -->
        </div>
        <!-- <footer class="page-fixed-end">-->
        <?php //= $this->yieldSection('page-end'); ?>
        <!-- </footer>-->
        <?= $this->yieldSection('article-end'); ?>
    </article>
</main>

<!-- Global Delete Modal -->
<div class="modal modal-delete fade" id="modalDelete"  data-md-modal="true" tabindex="-1" aria-labelledby="modalDeleteLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form action="" method="GET" data-md-form="true">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <i class="mdi mdi-trash-can-outline text-danger"></i>
                    <div data-md-form-message="true"></div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="submit" class="btn btn-danger"><?= __t('Yes') ?></button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __t('No') ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- /Global Delete Modal -->

<!-- Global Iframe Modal -->
<div class="modal modal-iframe fade" id="modalIframe" tabindex="-1" aria-labelledby="modalIframeLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalSimpleLabel"><span data-mi-modal-title="true"></span>&nbsp;</h5>
                    <button type="button" class="btn-close-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="mdi mdi-close-thick"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <iframe src="about:blank" frameborder="0" data-iframe='{"autoResize": true}' data-mi-iframe="true"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Global Iframe Modal -->

<!-- Global Attachments Modal -->
<div class="modal fade" id="modalAttachments" tabindex="-1" aria-labelledby="modalAttachmentsLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" data-ma-modal-title="true"><?= __t('Attachments') ?></h5>
                <button type="button" class="btn-close-2" data-bs-dismiss="modal" aria-label="Close">
                    <i class="mdi mdi-close-thick"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="row g-12" data-ma-modal-data="true">
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Global Attachments Modal -->

<!-- Help Modal -->
<div id="help-container">
    <div class="modal-bg-container">
        <div class="modal-bg"></div>
    </div>
    <div id="modal-container"></div>
</div>
<!-- /Help Modal -->
<!-- Global Low Priority Vendors -->

<script>
    var translations = <?= json_encode($i18n_data_for_js); ?>;
    var translate_languages = translations;
</script>
<script src="/frontend/assets/js/vendors/bootstrap.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/fancybox.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.text-copy.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Layout scripts -->
<script src="/frontend/assets/js/layout/default.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Global Modals -->
<script src="/frontend/assets/js/components/modal/modal-confirm.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/components/modal/modal-delete.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/components/modal/modal-attachments.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/components/modal/modal-iframe.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Page before JS stack  -->
<?php $this->stack('page-before-js'); ?>

<!-- Beta Version -->
<script src="/js/beta.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Navigation Shortcuts -->
<script>
    var izamNavigation = <?= $izamNavigation->toJson() ?>;
</script>
<script src="/js/navigation-shortcuts.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/js/navigation-listener.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Help Modal -->
<script src="/js/modals.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/js/modals-listener.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Date Input Vendors -->
<script src="/frontend/assets/js/vendors/moment.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/luxon.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/flatpickr.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/flatpickr.plugins.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/flatpickr.defaults.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Input Placeholder -->
<script src="/frontend/assets/js/vendors/jquery.mentions-input.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.input-placeholder.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Input Autosize Vendors -->
<script src="/frontend/assets/js/vendors/jquery.input-autosize.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Input Number Vendors -->
<script src="/frontend/assets/js/vendors/jquery.input-number.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Textarea Vendors -->
<script src="/frontend/assets/js/vendors/jquery.textarea-autosize.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Input Auto Complete -->
<script src="/frontend/assets/js/vendors/jquery.input-autocomplete.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Editor Vendors -->
<script src="/frontend//assets/js/vendors/tinymce.min.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend//assets/js/vendors/tinymce.defaults.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Map Location -->
<script src="/frontend/assets/js/vendors/google-maps-loader.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.input-map.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Subform Vendors -->
<script src="/frontend/assets/js/vendors/table-dragger.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.subform.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Uploader Vendors -->
<script src="/frontend/assets/js/vendors/jquery.uploader.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Input Phone Number Vendors -->
<script src="/frontend/assets/js/vendors/libphonenumber.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.input-phone.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Input Color Vendors -->
<script src="/frontend/assets/js/vendors/coloris.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/coloris.defaults.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Input Icon Picker Vendors -->
<script src="/frontend/assets/js/vendors/jquery.iconpicker.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.iconpicker.defaults.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Form Validate Vendors -->
<script src="/frontend/assets/js/vendors/jquery.validate.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.validate.defaults.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Select Input Vendors -->
<script src="/frontend/assets/js/vendors/jquery.selectize.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.selectize.plugins.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.selectize.defaults.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Listing Page Head -->
<script src="/frontend/assets/js/components/listing/listing-check.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/components/listing/listing-actions.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/components/listing/listing-floating-actions.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/components/listing/listing-jump.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Listing Table -->
<script src="/frontend/assets/js/vendors/jquery.doublescroll.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/components/listing/listing-table.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Filters -->
<script src="/frontend/assets/js/components/filters/filters.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Form Group (Floating Labels & Clear Button) -->
<script src="/frontend/assets/js/components/form/form-group.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Dropdown Dialog -->
<script src="/frontend/assets/js/components/dropdown/dropdown-dialog.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Tabs -->
<script src="/frontend/assets/js/components/tab/tab.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Iframes -->
<script src="/frontend/assets/js/components/iframe/iframe.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- View Actions -->
<script src="/frontend/assets/js/components/view/view-actions.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Page JS Files -->
<?php foreach ($this->getJs() as $js) { ?><script src="<?= $js ?>?v=<?= JAVASCRIPT_VERSION ?>"></script><?php } ?>

<!-- Map Location -->
<script src="/frontend/assets/js/vendors/google-maps-loader.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.input-map.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<!-- Custom Code -->
<?= js_injector(new \Izam\Daftra\Injector\SettingRepository()) ?>

<!-- add app page scripts -->
<?php if (isset($pageScripts)) {
    echo $pageScripts;
} ?>

<?php
if ( !empty($additional_code)){
    echo $additional_code;
}
?>

<!-- Page after JS stack  -->
<?php $this->stack('page-after-js'); ?>
</body>
</html>