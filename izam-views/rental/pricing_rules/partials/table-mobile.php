<div class="d-block d-lg-none">

    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table">
                <thead data-lt-head="true">
                <tr>
                    <th></th>
                    <th></th>
                    <th width="45" class="listing-cell-end text-end">
                        <?php $this->includeSection('rental/pricing_rules/partials/sort-options'); ?>
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($pagination as $key => $row) { ?>
                    <tr tabindex="0">
                        <td>
                            <a href="<?= $this->route('owner.rental_price_rule.show', [$row->id,   $page_url_param]) ?>" class="listing-cell-link"></a>
                            <div class="title-text">
                                <p><?= $row->name ?></p>
                                <small class="title-subtitle-text">#<?= $row->id ?></small>
                                <div class="mt-2">
                                    <?= __t($row->method_format_name) ?>
                                </div>
                                <div class="mt-2">
                                    <span class="text-amount"><?= !empty($row->priceRuleDetail) ?  priceRuleFormats($row) : '' ?></span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="<?= $this->route('owner.rental_price_rule.show', [$row->id,   $page_url_param]) ?>" class="listing-cell-link"></a>
                            <div class="text-nowrap">
                                <?php if ($row->status  == 1) { ?>
                                    <div class="status-circle">
                                        <i class="bg-active"></i>
                                        <span><?= __t('Active') ?></span>
                                    </div>
                                <?php } else { ?>
                                    <div class="status-circle">
                                        <i class="bg-inactive"></i>
                                        <span><?= __t('Inactive') ?></span>
                                    </div>
                                <?php } ?>
                            </div>
                        </td>
                        <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                            <?= $this->includeSection('rental/pricing_rules/partials/dropdown-menu-mobile',  ['row' => $row, 'page_url_param' => $page_url_param]) ?>
                        </td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>