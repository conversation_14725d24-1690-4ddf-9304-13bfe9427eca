<div class="dropstart mb-auto dropdown-dialog" data-dd="true" data-lt-dropdown="true">
    <button class="btn btn-dots" type="button" data-bs-offset="0,10" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="mdi mdi-dots-horizontal"></i>
    </button>
    <div class="dropdown-menu text-start">
        <div class="dropdown-dialog-header" data-bs-toggle="dropdown">
            <span class="dropdown-dialog-header-title" data-dd-title="true">
                <?= __t('Actions') ?>
            </span>
            <span class="dropdown-dialog-header-close">
                <i class="mdi mdi mdi-close-thick"></i>
            </span>
        </div>
        <ul class="dropdown-dialog-menu">
            <li>
                <a href="<?= $this->route('owner.rental_price_rule.show', [$row->id,  $page_url_param]); ?>"
                    class="dropdown-item">
                    <i class="mdi mdi-eye text-success"></i>
                    <span><?= __t('View') ?></span>
                </a>
            </li>
            <li>
                <a href="<?= $this->route('owner.rental_price_rule.edit', $row['id']); ?>" class="dropdown-item">
                    <i class="mdi mdi-pencil text-info"></i>
                    <span><?= __t('Edit') ?></span>
                </a>
            </li>
            <li>
                <button data-bs-toggle="modal" data-bs-target="#modalDelete"
                    data-md-message="<?= sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Pricing Rule') ) ?>"
                    data-md-action="<?= $this->route('owner.rental_price_rule.destroy', $row['id']);   ?>"
                    data-md-method="POST" data-md-data='{"_token": "<?= csrf_token() ?>", "_method": "DELETE"}'
                    type="button" class="dropdown-item">
                    <i class="mdi mdi-delete text-danger"></i>
                    <span><?= __t('Delete') ?></span>
                </button>
            </li>
        </ul>
    </div>
    <div class="dropdown-dialog-backdrop"></div>
</div>