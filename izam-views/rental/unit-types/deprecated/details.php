<div class="ui-show-card-content-inner-2-phone">
    <div class="">
        <div class="l-flex-row">
            <div class="l-flex-col-lg-12">
                <div class="ui-card-section-title-box u-bg-color-light">
                    <h6 class="ui-card-section-title u-text-color-black"><?= __t('Unit Type Information') ?> </h6>
                </div>
            </div>
        </div>
    </div>
    <div class=" ui-show-card-section-items-box">
        <div class="l-flex-row">

            <div class="l-flex-col-lg-6">
                <div class="l-show-card-section-item">
                    <div class="ui-show-card-section-item">
                        <div class="ui-show-card-section-item-content">
                            <div class="l-big-title l-big-title--icon-start l-big-title--icon-start--spacing-16 ui-big-title-box">

                                <?php
                                if (count($data->attachments) > 0) {
                                    ?>
                                    <div class="l-icon l-icon--size-126">
                                        <span class="l-item-img l-item-img--size-126 ui-item-img">
                                            <a href="<?= $data->attachments[0]->url ?>" data-app-lightbox="gallery">
                                                <img src="<?= $data->attachments[0]->url  ?>" class="ui-img-thumbnail" width="126" height="126" style="width: 126px;height: 126px;" />
                                            </a>
                                        </span>
                                    </div>

                                <?php } ?>

                                <div>
                                    <div class="ui-show-card-section-item">
                                        <div>
                                            <span class="ui-show-card-section-item-label"><?= __t('Name') ?></span>
                                            <h2 class="ui-big-title ui-big-title--lg u-text-color-black">
                                                <span><?= $data['name'] ?></span>
                                            </h2>
                                            <p class="ui-big-subtitle ui-big-subtitle--sm u-text-color-subtitle">#<?= $data['id'] ?></p>
                                        </div>
                                    </div>
                                    <?php if($data->get_pricing_rule): ?>
                                        <div>
                                            <span class="ui-show-card-section-item-label"><?= __t('Pricing Rule') ?></span>
                                            <div class="ui-show-card-section-item-content ui-timeline-item-txt">
                                                <?= ucfirst($data->get_pricing_rule->name); ?>
                                                <a href="<?= $this->route('owner.rental_price_rule.show', $data->get_pricing_rule->id) ?>"><?= '#' . $data->get_pricing_rule->id ?></a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php
            if ($data['method'] == 'days') {
                $labelOneTitle = __t('Check-In');
                $labelTwoTitle = __t('Check-Out');
                $labelOneValue = substr($data['check_in'], 0, 5);
                $labelTwoValue = substr($data['check_out'], 0, 5);
            } else {

                $labelOneTitle = __t('Minimum Hours');
                $labelTwoTitle = __t('Time Slots');
                $labelOneValue = $data['minimum_hours'];
                $labelTwoValue = $data['time_slots'] . ' ' . __t('Minute');
            }
            ?>

            <div class="l-flex-col-lg-6">
                <div class="l-flex-row l-flex-row--spacing-0">
                    <div class="l-flex-col-lg-6">
                        <div class="l-show-card-section-item">
                            <div class="ui-show-card-section-item">
                                <span class="ui-show-card-section-item-label"><?= __t('Pricing Method') ?></span>
                                <div class="ui-show-card-section-item-content">
                                    <p class="ui-big-title ui-big-title--lg"><?= ucfirst(__t($data['method'])) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="l-flex-col-lg-6">
                        <div class="ui-show-card-section-item">
                            <div class="l-flex l-flex--align-stretch">
                                <div class="l-timeline">
                                    <span class="l-timeline-spacer-vertical"></span>
                                </div>
                                <div class="l-timeline-content-box">
                                    <div class="l-timeline-content-item">
                                        <span class="ui-timeline-item-txt ui-show-card-section-item-label"><?= $labelOneTitle ?></span>
                                        <span class="ui-timeline-item-txt u-text-color-black"><?= $labelOneValue ?></span>
                                    </div>
                                    <div class="l-timeline-content-item">
                                        <span class="ui-timeline-item-txt ui-show-card-section-item-label"><?= $labelTwoTitle ?></span>
                                        <span class="ui-timeline-item-txt u-text-color-black"><?= $labelTwoValue ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if((!empty($data['description']) && $data['description'] != "") || (isset($data['get_first_tax']) && !empty($data['get_first_tax']->name)) || (isset($data['get_second_tax']) && !empty($data['get_second_tax']->name))){?>

        <div class="l-show-card-section-items-box">
            <div class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7;"></div>
        </div>
    <?php }?>

    <div class="l-show-card-section-items-box ui-show-card-section-items-box">
        <div class="l-flex-row">
            <?php if(!empty($data['get_first_tax']->name)){?>
                <div class="l-flex-col-lg-6">
                    <div class="l-show-card-section-item">
                        <div class="ui-show-card-section-item">
                            <span class="ui-show-card-section-item-label"><?= __t('Tax 1') ?></span>
                            <div class="ui-show-card-section-item-content">
                                <p><?= $data['get_first_tax']->name  ?> </p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php }?>

            <?php if(!empty($data['get_second_tax']->name)){?>
                <div class="l-flex-col-lg-6">
                    <div class="l-show-card-section-item">
                        <div class="ui-show-card-section-item">
                            <span class="ui-show-card-section-item-label"><?= __t('Tax 2') ?></span>
                            <div class="ui-show-card-section-item-content">
                                <p><?=  $data['get_second_tax']->name  ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php }?>
            <?php if(!empty($data['description'])){?>
                <div class="l-flex-col-lg-6">
                    <div class="l-show-card-section-item">
                        <div class="ui-show-card-section-item">
                            <span class="ui-show-card-section-item-label"><?= __t('Description') ?></span>
                            <div class="ui-show-card-section-item-content ui-show-card-section-item-content--text-pre ui-show-card-section-item-content--lh-normal">
                                <p><?=  $data['description'] ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php }?>

        </div>
    </div>

    <?php
    if (count($data->attachments) > 1) {
        ?>
        <div class="l-show-card-section-items-box">
            <div class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7;"></div>
        </div>

        <div class="l-show-card-section-items-box ui-show-card-section-items-box">
            <div class="l-flex-row">
                <div class="l-flex-col-lg-12">
                    <div class="l-show-card-section-item">
                        <div class="ui-show-card-section-item">
                            <span class="ui-show-card-section-item-label"> <?= __t('Images') ?> </span>
                            <div class="ui-show-card-section-item-content">
                                <div class="l-images-list-h ui-images-list-h">

                                    <?php
                                    if (count($data->attachments) > 1) {
                                        foreach ($data->attachments as $key => $value) {
                                            if ($key == 0) {
                                                continue;
                                            }
                                            # Cut name of IMage
                                            $imgName =  explode(' ', $value->name);
                                            ?>
                                            <div class="l-images-list-item ui-images-list-item" title="<?= end($imgName) ?>">
                                                <a class="l-images-list-item-link ui-images-list-item-link ui-images-list-item-link--one-line" data-thumb="" href="<?= $value->url ?>" data-app-lightbox="gallery" style="width: 96px;">
                                                    <img src="<?= $value->url ?>" class="ui-img-thumbnail" width="96" height="96" style="width: 96px;height: 96px;" />
                                                    <p class="l-images-list-item-name ui-images-list-item-name ui-images-list-item-name--one-line"><?= end($imgName) ?></p>
                                                </a>
                                            </div>
                                        <?php }
                                    } ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php } ?>

</div>