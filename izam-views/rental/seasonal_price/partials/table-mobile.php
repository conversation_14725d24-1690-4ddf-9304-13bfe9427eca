<?php $iframe  = isset($_GET['iframe']) ? true : false; ?>
<div class="d-block d-lg-none">

    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table">
                <thead data-lt-head="true">
                <tr>
                    <th></th>
                    <th width="45" class="listing-cell-end text-end">
                        <?php $this->includeSection('rental/seasonal_price/partials/sort-options'); ?>
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($pagination as $key => $raw) { ?>
                    <tr tabindex="0">
                        <td>
                            <a href="<?= $this->route('owner.seasonal_prices.show', $raw->id)?>" class="listing-cell-link"></a>
                            <div class="title-text">
                                <p><?= ucfirst($raw->name) ?></p>
                                <small class="title-subtitle-text">#<?= $raw->id ?></small>
                                <div class="mt-2">
                                    <?= ucfirst($raw->get_pricing_rule->name); ?>
                                </div>
                                <div class="mt-2">
                                    <div class="hstack align-items-center flex-wrap column-gap-3">

                                        <?php
                                        foreach ($raw->getUnitTypes as $key => $unitType) :
                                            if ($key > 0) :
                                                ?>
                                                <div class="listing-rule vr bg-secondary opacity-100"></div>
                                            <?php
                                            endif;
                                            echo ucfirst($unitType->name);
                                        endforeach;
                                        ?>

                                    </div>
                                </div>
                                <div class="mt-2">
                                    <?= displaySeasonalDays($raw->day_of_week); ?>
                                </div>
                                <div class="mt-2 text-light-3">
                                    <?= format_date($raw->date_from) ?> : <?= format_date($raw->date_to) ?>
                                </div>
                            </div>
                        </td>
                        <?php if (!$iframe) { ?>
                            <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">




                                <div class="dropstart mb-auto dropdown-dialog" data-dd="true" data-lt-dropdown="true">
                                    <button class="btn btn-dots" type="button" data-bs-offset="0,10"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="mdi mdi-dots-horizontal"></i>
                                    </button>
                                    <div class="dropdown-menu text-start">
                                        <div class="dropdown-dialog-header" data-bs-toggle="dropdown">
                                            <span class="dropdown-dialog-header-title" data-dd-title="true">
                                                <?= __t('Actions') ?>
                                            </span>
                                            <span class="dropdown-dialog-header-close">
                                                <i class="mdi mdi mdi-close-thick"></i>
                                            </span>
                                        </div>
                                        <ul class="dropdown-dialog-menu">
                                            <li>
                                                <a href="<?= $this->route('owner.seasonal_prices.show', [$raw->id, $page_url_param]); ?>" class="dropdown-item">
                                                    <i class="mdi mdi-eye text-success"></i>
                                                    <span><?= __t('View') ?></span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="<?= $this->route('owner.seasonal_prices.edit', [$raw->id]); ?>" class="dropdown-item">
                                                    <i class="mdi mdi-pencil text-info"></i>
                                                    <span><?= __t('Edit') ?></span>
                                                </a>
                                            </li>
                                            <li>
                                                <button data-bs-toggle="modal" data-bs-target="#modalDelete" data-md-message="<?= sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Seasonal Price')) ?>" data-md-action="<?= $this->route('owner.seasonal_prices.delete', [$raw->id]); ?>" data-md-method="POST" data-md-data='{"_token": "<?= csrf_token() ?>", "_method": "DELETE"}' type="button" class="dropdown-item">
                                                    <i class="mdi mdi-delete text-danger"></i>
                                                    <span><?= __t('Delete') ?></span>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="dropdown-dialog-backdrop"></div>
                                </div>
                            </td>
                        <?php } ?>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>