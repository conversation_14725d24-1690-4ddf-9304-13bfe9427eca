<?php
    use Izam\Attachment\Helpers\AttachmentMimeType;
    extract($listingExtraData);
    ?>  
<div class="d-block d-lg-none">

    <div>
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table">
                    <thead>
                        <tr>
                            <th class="listing-cell-start"></th>
                            <th></th>
                            <th width="45" class="listing-cell-end text-end">
                                <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($table->getRows() as $key => $row): 
                            $asset = $row->getData();
                            $showUrl = '/owner/assets/view/' . $asset->id;
                        ?>
                        <tr>
                            <td>
                                <a href="<?=$showUrl?>" class="listing-cell-link"></a>
                                <div class="text-nowrap">
                                    <div class="title-text mb-9">
                                        <div class="title-text mb-4">
                                            <p><?=$asset->name?><a
                                                    class="thumb-subtitle-text text-decoration-none"
                                                    href="#">#<?=$asset->code?></a></p>
                                        </div>
                                        <?php if ($catList && !empty($catList[$asset->journal_cat_id])) { ?>
                                            <div class="text-meta ">
                                                <i class="fa fa-archive opacity-50 fs-8"></i>
                                                <p class="opacity-50"><?= $catList[$asset->journal_cat_id] ?></p>
                                            </div>
                                        <?php } ?>
                                    </div>
                                    <div class="vstack gap-4">
                                        <div class="text-nowrap">
                                            <div class="vstack gap-2">
                                                <div class="hstack gap-2 justify-content-start">
                                                    <span style="color: #fff;background:<?=$statusBgMap[$asset->asset_status]?>" class="badge"><?php echo __t($statuses[$asset->asset_status]);?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                        <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                                        <div class="hstack flex-nowrap gap-3 align-items-center">
                                            <?php
                                            $attachments = $assetsAttachments[$asset->id];
                                            $attachmentsThumb = [];
                                            foreach ($attachments as $key => $attachment) {
                                                if (AttachmentMimeType::MAX_PREVIEW_ATTACHMENTS_IN_LISTING <= $key) {
                                                    $attachmentsThumb[] = $attachment['name'];
                                                    continue;
                                                }
                                                if ('file' == $attachment['type']) {
                                                    ?>
                                                    <a class="thumb-link" data-bs-toggle="tooltip" target="_blank"
                                                        href="<?= $aws->getPermanentUrl($attachment['path']) ?>"
                                                        data-bs-title="<?= $attachment['name'] ?>"
                                                        title="<?= $attachment['name'] ?>">
                                                        <i class="thumb far fa-file-<?= AttachmentMimeType::getIcon($attachment['mime_type']) ?>"></i>
                                                    </a>
                                                    <?php
                                                } elseif ('image' == $attachment['type']) { ?>
                                                    <a class="thumb-link" data-bs-toggle="tooltip" data-fancybox target="_blank"
                                                        href="<?= $aws->getPermanentUrl($attachment['path']) ?>"
                                                        data-src="<?= $aws->getPermanentUrl($attachment['path']) ?>"
                                                        data-bs-title="<?= $attachment['name'] ?>"
                                                        title="<?= $attachment['name'] ?>">
                                                        <img class="thumb" loading="lazy"
                                                                src="<?= $aws->getPermanentUrl($attachment['path']) ?>"/>
                                                    </a>
                                                <?php }
                                            }
                                            if ($attachmentsThumb) { ?>
                                                <a class="thumb thumb-link fs-7"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-html="true"
                                                    data-bs-title="<?= implode('<br>', $attachmentsThumb) ?>"
                                                    title="<?= implode(',', $attachmentsThumb) ?>"
                                                    href="<?= $showUrl ?>">+<?= count($attachmentsThumb) ?></a>
                                            <?php }
                                            ?>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-end h-0" data-lt-dropdown-cell="true">
                                <a href="<?=$showUrl?>" class="listing-cell-link"></a>
                                    <div class="mt-9 mb-9">
                                        <p class="mb-0"><strong><?= format_price($asset->asset_current_value, $asset->asset_currency)?></strong></p>
                                    </div>
                                </div>
                            </td>
                            <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                            <?= $this->includeSection('partials/row-actions-mobile', ['row' => $row]) ?>
                                            </td>
                        </tr>
                    <?php endforeach;?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>