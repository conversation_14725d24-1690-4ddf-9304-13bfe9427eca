<meta charset="UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<link rel="shortcut icon" href="/frontend/assets/img/layout/favicon/<?= $favicon ?>.ico" type="image/x-icon" />
<title><?= $title ?></title>
<!-- Beta version -->
<link rel="stylesheet" href="/css/beta.css?v=<?= CSS_VERSION ?>" />
<!-- Styles -->
<link rel="stylesheet" href="/frontend/assets/css/layout/default.css?v=<?= CSS_VERSION ?>" />
<!-- Help Modal -->
<link rel="stylesheet" href="/dist/app/js/pages/help/help.styles.css?v=<?= CSS_VERSION ?>" id="help-css" />
<!-- App manager dropdown -->
<link rel="stylesheet" href="/css/app-manager-dropdown.css?v=<?= CSS_VERSION ?>" />
<!-- CSS Files -->
<?php foreach ($this->getCss() as $css) { ?><link rel="stylesheet" href="<?= $css ?>?v=<?= CSS_VERSION ?>" /><?php } ?>
<?= $this->yieldSection('styles'); ?>

<!-- Global Variables & Utilities -->
<style>
    html {
        --theme-color: <?= $theme_color ?>;
        --theme-bg: <?= $theme_bg ?>;
    }
</style>
<script>
    var APP = <?= json_encode($app_data_for_js) ?>;
    var USER = <?= json_encode([
                        'staff_id' => getAuthOwner('staff_id'),
                        'branch_id' => getCurrentBranchID()
                    ]) ?>;
</script>
<!-- High Priority Vendors -->
<script src="/frontend/assets/js/vendors/jquery.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.showflex.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/jquery.toast.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/underscore.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/overlayscrollbars.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/iframeresizer.window.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/vendors/iframeresizer.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<!-- Global Utilities -->
<script src="/frontend/assets/js/utils/data.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/utils/text.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/utils/date.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/utils/currency.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/utils/file.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<?php if (strtolower($site['country_code']) == 'sa'): ?>
    <?php $fontURL = '/css/saudi_riyal_symbol-regular.ttf'; ?>
    <style>
        @font-face {
            font-family: 'saudi_riyal_symbol';
            src: url('<?= $fontURL ?>') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        .sar_symbol {
            font-family: 'saudi_riyal_symbol', sans-serif !important;
        }
    </style>
<?php endif; ?>