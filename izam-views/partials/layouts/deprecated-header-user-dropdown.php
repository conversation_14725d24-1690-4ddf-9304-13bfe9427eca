<?php


?>

<div class="dropdown">
    <button class="m-app-header-user-btn" type="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false" data-bs-offset="0,4">
        <span class="l-user-img l-user-img--inline l-user-img--size-28 ui-user-img">

            <?php if($user['type'] == 'client' && $user['photo']) { ?>
                <img src="<?= $user['photo_full_path'].'?w=30&amp;h=30&amp;c=1'; ?>" width="30" height="30" style="object-fit: unset;" />
            <?php }else{ ?>
                <img src="<?= ($user['staff_id'] == 0 && $user['site_logo']) ? '/files/images/site-logos/' . $user['site_logo'] : \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($username, $user['staff_id'], 30, empty($user['photo']) ? null : $user['photo']); ?>" width="30" height="30" style="object-fit: unset;" />
            <?php } ?>

        </span>
        <span class="m-app-header-user-info-box">
            <span class="m-app-header-user-title"><?= $username ?></span>
            <span class="m-app-header-user-subtitle"><?= $branchName? $branchName :  format_date(date("Y-m-d H:i:s"), false, true)  ?></span>
        </span>
        <i class="m-app-header-user-caret mdi mdi-menu-down"></i>
    </button>
    <div class="dropdown-menu dropdown-menu-end m-app-header-user-dropdown-menu">
        <div class="m-app-header-user-dropdown-menu-header">
            <span><?= __t('My Account') ?></span>
        </div>



        <?php if ($changeEmailUrl) { ?>
            <a class="m-app-header-user-dropdown-menu-item" href="<?= $changeEmailUrl ?>">
                <i class="mdi mdi-email"></i>
                <span><?= __t("Change Email") ?></span>
            </a>
        <?php } ?>

        <?php if ($changePasswordUrl) { ?>
            <a class="m-app-header-user-dropdown-menu-item" href="<?= $changePasswordUrl ?>">
                <i class="mdi mdi-lock"></i>
                <span> <?= __t("Change Password") ?></span>
            </a>
        <?php } ?>
        <?php
            if ($user['type'] == 'client') {
                $mailSubscriptionUrl = $this->url(array('controller' => 'entity_email_prefrences', 'action' => 'index', 'client' => true));
            } elseif (showEmailPreferences()) {
                $mailSubscriptionUrl = '/v2/owner/email-prefrence/subscription'; ?>
                <li>
                    <a class="m-app-header-user-dropdown-menu-item" href="<?= $mailSubscriptionUrl ?>">
                        <i class="mdi mdi-reply"></i>
                        <span><?= __t("E-mail preferences") ?> </span>
                    </a>
                </li>
            <?php } ?>


        <?php if ($QRCodeAccessUrl) { ?>
            <a class="m-app-header-user-dropdown-menu-item" href="<?= $QRCodeAccessUrl ?>">
                <i class="fa fa-qrcode middle"></i>
                <span> <?= sprintf(__t('%s Mobile Apps', true), __t(Site_Full_name_NoSpace)) ?></span>
            </a>
        <?php } ?>

        <?php if ($editMyDetails) { ?>
            <a class="m-app-header-user-dropdown-menu-item" href="<?= $editMyDetails ?>">
                <i class="mdi mdi-lock"></i>
                <span> <?= __t("Edit My Details") ?></span>
            </a>
        <?php } ?>



        <?php if (!empty($user['is_super_admin']) && $user['is_super_admin']) { ?>


            <?php if (!strtotime($user['expiry_date'])) { ?>

                <a target="_blank" class="m-app-header-user-dropdown-menu-item" href="<?= $this->url(array('controller' => 'sites', 'action' => 'upgrade')) ?>">
                    <i class="mdi mdi-refresh"></i>
                    <span><?= __t('Upgrade Subscription') ?></span>
                </a>
            <?php } else { ?>

                <a target="_blank" class="m-app-header-user-dropdown-menu-item" href="<?= $this->url(array('controller' => 'sites', 'action' => 'renew')) ?>">
                    <i class="mdi mdi-refresh"></i>
                    <span><?= __t('Renew Subscription') ?></span>
                </a>

            <?php } ?>



        <?php } ?>

       
          
            <?php foreach ($staffBranches as $staffBranchID => $staffBranch) { ?>
                <a class="m-app-header-user-dropdown-menu-item" href="<?= $this->url(['controller' => 'staffs', 'action' => 'update_current_branch',$staffBranchID /*, '?'=> ['last_url'=> $_SERVER['REQUEST_URI']]*/]) ?>">

                    <i class="fa fa-building middle"></i>
                    <span><?= $staffBranch ?></span>
                </a>

            <?php } ?>
      

        <?php if (IS_MOBILE) { ?>
        <a class="m-app-header-user-dropdown-menu-item" href="https://docs.<?= Domain_Short_Name ?>" target="_blank">
            <span><?= __t('System Manual') ?></span>
        </a>
        <a class="m-app-header-user-dropdown-menu-item submit-feedback" href="/owner/sites_enquiries/index">
            <span><?= __t('Submit Feedback') ?></span>
        </a>
        <a class="m-app-header-user-dropdown-menu-item" href="https://<?= Portal_Full_Name ?>/about-<?= Domain_Name_Only ?>" target="_blank">
            <span><?= __t('About') . ' ' . Site_Full_name_NoSpace ?></span>
        </a>
        <?php } ?>

        <a class="m-app-header-user-dropdown-menu-item u-text-color-danger u-text-hover-color-white u-bg-hover-color-danger" href="<?= $LogoutUrl; ?>">
            <i class="mdi mdi-logout"></i>
            <span><?= __t("Log out") ?></span>
        </a>







    </div>
</div>
