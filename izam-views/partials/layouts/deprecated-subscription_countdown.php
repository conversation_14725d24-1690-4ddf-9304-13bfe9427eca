<?php
$subscription_countdown = get_subscription_countdown_data();
if($subscription_countdown['countdown_message_background_class'] == 'danger') {
    $class = 'sticky-message--color-danger';
} else if($subscription_countdown['countdown_message_background_class'] == 'warning') {
    $class = 'sticky-message--color-warning';
} else {
    $class = $subscription_countdown['countdown_message_background_class'];
}
if (isset($subscription_countdown) && $subscription_countdown['countdown_status'] && empty(getAuthClient())) { ?>
    <div class="ui-sticky-message l-sticky-message <? echo $class; ?>">
        <a class="ui-sticky-message-link l-sticky-message-link" href="<?php echo $subscription_countdown['countdown_link']; ?>" title="<?= __t($subscription_countdown['countdown_link_title']); ?>" target="_blank">
            <i class="mdi mdi-alert-octagram l-sticky-message-icon ui-sticky-message-icon"></i>
            <span>
                <?= sprintf(__t($subscription_countdown['countdown_message'], true), $subscription_countdown['remaining_days']); ?>
            </span>
        </a>
    </div>

<?php } ?>