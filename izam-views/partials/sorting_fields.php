<?php
$parameters = array_merge($filtered, getEntityCustomPaginationParams($entityKey));
$entityKeyForUrl = $entityKey;
if (isset($_GET['iframe'])) {
    $parameters['iframe'] = 1;
}
if (isset($_GET['hide_filters'])) {
    $parameters['hide_filters'] = 1;
}
if (isset($_GET['hide_page_header'])) {
    $parameters['hide_page_header'] = 1;
}
if (isset($_GET['show_filters'])) {
    $parameters['show_filters'] = 1;
}
if (str_starts_with($entityKey, 'le_workflow-type') !== false) {
    $entityKeyForUrl = 'workflow/' . $entityKey;
}
?>
<div class="dropdown" data-lt-dropdown="true">
    <button class="btn btn-link listing-sort-btn" type="button" data-bs-toggle="dropdown"
            data-bs-offset="0,10" aria-expanded="false">
        <?= __t('Sort') ?>
        <?php
        $defaultIconDir = strtoupper($table->getActiveSort()->getDirection()) == \App\Helpers\ListingHelper::SORT_DIRECTION_DESC ? 'ascending' : 'descending';
        echo "<i class='mdi mdi-sort-".$defaultIconDir."'></i>";
        ?>
    </button>
    <ul class="dropdown-menu text-start">

        <?php foreach ($table->getSorts() as $sortField) { ?>

            <?php
            if ($sortField->getHidden()) {
                continue;
            } else {
                $sortFieldDir = $sortField->getDirection();
                if ($table->getActiveSort()->getKey() == $sortField->getKey()) {
                    $sortFieldDir = strtoupper($table->getActiveSort()->getDirection()) == \App\Helpers\ListingHelper::SORT_DIRECTION_DESC
                        ? \App\Helpers\ListingHelper::SORT_DIRECTION_ASC
                        : \App\Helpers\ListingHelper::SORT_DIRECTION_DESC;
                }
                $parameters['sort'] = [$sortField->getKey() => $sortFieldDir];
                $sort_url = '/v2/owner/entity/' . $entityKeyForUrl . '/list?' . http_build_query($parameters);
                ?>
                <li>
                    <a class="dropdown-item" href="<?= $sort_url ?>">
                        <?php
                            $iconDir = 'up';
                        if (strtoupper($sortFieldDir) == \App\Helpers\ListingHelper::SORT_DIRECTION_ASC) {
                            $iconDir = 'down';
                        }?>
                        <i class="fal fa-sort-alpha-<?= $iconDir ?> u-text-secondary"></i>
                        <span> <?= __t($sortField->getTitle()) ?></span>
                    </a>
                </li>
            <?php } ?>
        <?php } ?>
    </ul>
</div>
