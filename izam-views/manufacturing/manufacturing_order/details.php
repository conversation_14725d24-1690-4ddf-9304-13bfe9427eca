<?php

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\TrackStockTypesUtil;
use Izam\View\Form\Helper\Show\ShowFormElement;
use Izam\View\Form\Element\LinkWithTooltip;
use Izam\View\Form\Element\InfoItemElement;
use Izam\View\Form\Element\CollapsedSubform;
use Izam\View\Form\Helper\Show\CollapsedSubformHelper;
use Izam\View\Form\Helper\Show\ShowCustomFields;

    $helper = new ShowFormElement();
    $linkHelper = new \Izam\View\Form\Helper\Show\LinkWithTooltipHelper();
    $subformHelper = new  CollapsedSubformHelper();
    extract($extraData);
?>
<link rel="stylesheet" href="/dist/daftra/daftra.compat.styles.css?v={{css_ver}}">
<script src="/dist/daftra/daftra.compat.js?v={{js_ver}}"></script>

 <div class="info-section">
    <div class="info-section-title">
        <span> <?= sprintf(__t('%s Information'), __t('Manufacturing Order')) ?> </span>
    </div>
    <div class="info-section-body">
        <div class="row">
            <div class="col-lg-6">
                <div class="thumb-text-group product-thumbnail align-items-start">
                    <div>
                        <?php if ($productImage): ?>
                            <img class="thumb thumb-4xl" loading="lazy" src="<?=$productImage?>">
                        <?php else: ?>
                            <i class="thumb thumb-4xl fas fa-image text-white"></i>
                        <?php endif;?>
                    </div>
                    <div class="thumb-text">
                        <p class="thumb-subtitle-text"><?=__t('Product')?></p>
                        <p class="thumb-title-text"><?=$data->product->name?></p>
                        <p class="thumb-subtitle-text hstack gap-3">
                            <?= $linkHelper->render((new LinkWithTooltip())->setValue("/owner/products/view/".$data->product->id)->setLabel('#' .$data->product->product_code ?? $data->product->id))?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="row">
                    <div class="col-lg-6">
                        <?php
                            $quantity = $data->quantity;
                            if($data->factor){
                                $quantity = ($quantity/$data->factor). ' '.$data->unit_small_name;
                            }
                            echo $helper->render((new InfoItemElement())->setLabel(__t('Quantity'))->setValue(' <span class="fw-bold fs-10">'.$quantity.'</span>'));
                        ?>
                    </div>
                    <div class="col-lg-6">
                        <div class="info-item">
                            <div class="info-item-value">
                                <div class="info-timeline">
                                    <?php if($data->date_from): ?>
                                        <div class="info-timeline-item">
                                            <div class="info-item-label"><?=__t('Start Date')?>
                                            </div>
                                            <div class="info-item-value"><?=format_date($data->date_from)?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($data->date_to): ?>
                                        <div class="info-timeline-item">
                                            <div class="info-item-label"><?=__t('End Date')?>
                                            </div>
                                            <div class="info-item-value"><?=format_date($data->date_to)?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
           
        </div>
    </div>
</div>
<br>
<hr class="hr hr-dashed hr-md text-secondary opacity-100 mt-8 mb-16">

<div class="info-section">
    <div class="info-section-body">
        <div class="row">
            <div class="col-lg-6">
                <div class="info-item ">
                    <div class="info-item-label">
                        <?=__t('Name')?>
                    </div>
                    <div class="info-item-value">
                        <span class="fw-bold" ><?=$data->name?></span>
                    </div>
                </div>
            </div>
            <?php if($data->production_routing): ?> 
                <div class="col-lg-6">
                    <div class="info-item ">
                        <div class="info-item-label">
                            <?=__t('Production Routing')?>
                        </div>
                        <div class="info-item-value">
                            <span><?=$data->production_routing->name?></span>
                            <?=$linkHelper->render((new LinkWithTooltip())->setValue(route('owner.entity.show', ['entityKey' => EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY , 'id'=> $data->id]))->setLabel('#' .$data->production_routing['code']))?>
                        </div>
                    </div>
                </div>
            <?php endif; ?> 
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="info-item ">
                    <div class="info-item-label">
                        <?=__t('Account')?>
                    </div>
                    <div class="info-item-value">
                        <span><?=$data->account['name']?></span>
                        <?=$linkHelper->render((new LinkWithTooltip())->setValue("/v2/owner/chart-of-accounts/accounts/".$data->account->id)->setLabel('#' .$data->account['code']))?>
                    </div>
                </div>
            </div>
            <?php if($data->client): 
                    $client_image = \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($data->client->business_name, $data->client->id, 40, $data->client->client_photo_s3->client_attachment_file?->path);
                ?>
                <div class="col-lg-6">
                <div class="info-item ">
                    <div class="info-item-label">
                        <?=__t('Client')?>
                    </div>
                    <div class="thumb-text-group">
                        <img class="thumb thumb-sm text-dark-3" src="<?=$client_image?>" loading="lazy"> 
                        <div class="thumb-text">
                            <p><?=$data->client->business_name?> <span class="link-light-5 ms-2">-
                                 <?=$linkHelper->render((new LinkWithTooltip())->setValue("/owner/clients/view/".$data->client->id)->setLabel('#' .$data->client->id.' '.($data->client->email? '('.$data->client->email.')': '')))?>
                            </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php if($data->bom): ?>
                <div class="col-lg-6">
                    <div class="info-item ">
                        <div class="info-item-label">
                            <?=__t('BOM')?>
                        </div>
                        <div class="info-item-value">
                            <span class="fw-bold" ><?=$data->bom['name']?></span>
                            <?=$linkHelper->render((new LinkWithTooltip())->setValue(route('owner.entity.show', ['entityKey' => EntityKeyTypesUtil::BOM_ENTITY_KEY , 'id'=> $data->bom_id]))->setLabel('#' .$data->bom['code']?? $data->bom['id']))?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <?php if($data->manufacturing_order_employees): ?>
                <div class="col-lg-6">
                    <div class="info-item ">
                        <div class="info-item-label">
                            <?=__t('Employees')?>
                        </div>
                        <div class="info-item-value">
                        <?php 
                            foreach($data->manufacturing_order_employees as $i => $manufacturing_order_employee):
                                $employee = $manufacturing_order_employee->employee_manufacturing_order;
                                $staff_image = \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($employee->full_name, $employee->id, 40, $employee->photo, false); 
                            ?>
                            <div class="thumb-text-group">
                                <img class="thumb thumb-sm text-dark-3" src="<?=$staff_image?>" loading="lazy"> 
                                <div class="thumb-text">
                                    <p><?=$employee->name.' '.$employee->last_name??''?> <span class="link-light-5 ms-2">-
                                        <?=$linkHelper->render((new LinkWithTooltip())->setValue("/v2/owner/staff/".$employee->id)->setLabel('#' .($employee->code ?? $employee->id).' '.($employee->email_address? '('.$employee->email_address.')': '')))?>
                                    </div>
                            </div>
                            <br>
                        <?php endforeach; ?>
                    </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php 
    $materialsSubformRows = [];
    $totalActualMaterialCost = 0;
    foreach($data->materials as $materialProduct){
        $columns = '';
        $columns.= '
            <td>
                '.$materialProduct->product?->name.'
                <span class="hstack d-inline-flex gap-3">
                    '. $linkHelper->render((new LinkWithTooltip())->setValue("/owner/products/view/".$materialProduct->product?->id)->setLabel('#' .($materialProduct->product?->product_code? $materialProduct->product?->product_code: $materialProduct->product?->id))).'
                </span>
            </td>
            ';
        
        $quantity = $materialProduct->quantity;
        $deliveredQuantity = $materialProduct->actual_quantity;
        $actualSubtotal = 0;
        if($materialProduct->factor){
            $quantity = ($quantity/$materialProduct->factor). $materialProduct->unit_small_name;
            $actualSubtotal = $materialProduct->actual_price *  $deliveredQuantity;
            $deliveredQuantity = ($deliveredQuantity/$materialProduct->factor).$materialProduct->unit_small_name;
        }else{
            $actualSubtotal = $materialProduct->actual_price * $deliveredQuantity;
        }
        $totalActualMaterialCost += $actualSubtotal;
        $columns.= '
            <td>'.$quantity.'/'.$deliveredQuantity.'</td>
        ';
        $columns.= '
            <td>'.format_price_simple($materialProduct->price).'/'.format_price_simple($materialProduct->actual_price * (!is_null($materialProduct->factor) ? $materialProduct->factor : 1 ??0)).'</td>
        ';
        $columns.= '
            <td>'.($materialProduct->sub_production_routing->name ?? '') .'</td>
        ';
        $columns.= '
            <td>'.format_price_simple($materialProduct->sub_total_cost).'/'.format_price_simple($actualSubtotal).'</td>
        ';
        $materialsSubformRows[] = '<tr>'.$columns.'</tr>';
    }
?>

<hr class="hr hr-dashed hr-md text-secondary opacity-100 mt-8 mb-16">

<div class="info-section">
    <div class="info-section-body">
        <?=
        $subformHelper->render(
            (new CollapsedSubform())
                ->setLabel(__t('Materials'))
                ->setAttribute('subform_headers',[
                    __t('Product'),
                    __t('Planned/Received QTY'),
                    __t('Initial/Actual Price'),
                    __t('Production Operation'),
                    __t('Initial/Actual Cost'),
                ])
                ->setAttribute('subform_total_label', __t('Initial/Actual Cost'))
                ->setAttribute('subform_total_price', format_price_simple($data->materials_total_cost).'/'.format_price_simple($totalActualMaterialCost))
                ->setAttribute('subform_rows', $materialsSubformRows)
                ->setAttribute('subform_header_icon', 'mdi-package-variant')
            )
            ?>
    </div>
</div>
 

<?php 
    $expensesSubformRows = [];
    foreach($data->expenses as $expense){
        $columns = '';
        $columns.= '
            <td>
                '.$expense->account->name.'
                <span class="hstack d-inline-flex gap-3">
                    '. $linkHelper->render((new LinkWithTooltip())->setValue("/v2/owner/chart-of-accounts/accounts/".$expense->account->id)->setLabel('#' .$expense->account->code)).'
                </span>
            </td>
            ';
        $columns.= '
            <td>'.$expense->amount.'</td>
        ';
        
        $columns.= '
            <td>'.($expense->sub_production_routing->name ?? '').'</td>
        ';
        $columns.= '
            <td>'.($expense->description??'').'</td>
        ';
        $columns.= '
            <td>'.format_price_simple($expense->sub_total_cost).'</td>
        ';
        $expensesSubformRows[] = '<tr>'.$columns.'</tr>';
    }
?>

<div class="info-section">
    <div class="info-section-body">
        <?=
        $subformHelper->render(
            (new CollapsedSubform())
                ->setLabel(__t('Expenses'))
                ->setAttribute('subform_headers',[
                   __t('Account'),
                   __t('Amount'),
                   __t('Production Operation'),
                   __t('Description'),
                   __t('Subtotal'),
                ])                                                                             
                ->setAttribute('subform_total_label', __t('Total'))
                ->setAttribute('subform_total_price', format_price_simple($data->expenses_total_cost))
                ->setAttribute('subform_rows', $expensesSubformRows)
                ->setAttribute('subform_header_icon', 'mdi-cash-multiple')
            )
            ?>
    </div>
</div>
 

<?php 
    $operationsSubformRows = [];
    foreach($data->operations as $operation){
        $columns = '';
        $columns.= '
            <td>
                '.$operation->workstation->name.'
                <span class="hstack d-inline-flex gap-3">
                    '. $linkHelper->render((new LinkWithTooltip())->setValue(route('owner.entity.show', ['entityKey' => EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY , 'id'=> $operation->workstation->id]))->setLabel('#' .$operation->workstation->code)).'
                </span>
            </td>
            ';
        $columns.= '
            <td>'.$operation->operating_time.'</td>
        ';
        
        $columns.= '
            <td>'.($operation->sub_production_routing->name ?? '').'</td>
        ';
        $columns.= '
            <td>'.($operation->description??'').'</td>
        ';
        $columns.= '
            <td>'.format_price_simple($operation->sub_total_cost).'</td>
        ';
        $operationsSubformRows[] = '<tr>'.$columns.'</tr>';
    }
?>

<div class="info-section">
    <div class="info-section-body">
        <?=
        $subformHelper->render(
            (new CollapsedSubform())
                ->setLabel(__t('Manufacturing Operations'))
                ->setAttribute('subform_headers',[
                    __t('Workstation'),
                    __t('Operating Time'),
                    __t('Production Operation'),
                    __t('Description'),
                    __t('Subtotal'),
                ])                                                                             
                ->setAttribute('subform_total_label', __t('Total'))
                ->setAttribute('subform_total_price', format_price_simple($data->operations_total_cost))
                ->setAttribute('subform_rows', $operationsSubformRows)
                ->setAttribute('subform_header_icon', 'mdi-cog')
            )
            ?>
    </div>
</div>


<?php 
    $scrapsSubformRows = [];
    foreach($data->scraps as $scrap){
        $columns = '';
        $columns.= '
            <td>
                '.$scrap->product?->name.'
                <span class="hstack d-inline-flex gap-3">
                    '. $linkHelper->render((new LinkWithTooltip())->setValue("/owner/products/view/".$scrap->product?->id)->setLabel('#' .($scrap->product?->product_code? $scrap->product?->product_code: $scrap->product?->id))).'
                </span>
            </td>
            ';
        
        $quantity = $scrap->quantity;
        $actualSubtotal = 0;
        if($scrap->factor){
            $quantity = ($quantity/$scrap->factor). $scrap->unit_small_name;
        }
        $columns.= '
            <td>'.$quantity.'</td>
        ';
        $columns.= '
            <td>'.format_price_simple($scrap->price).'</td>
        ';
        $columns.= '
            <td>'.($scrap->sub_production_routing->name ?? '') .'</td>
        ';
        $columns.= '
            <td>'.format_price_simple($scrap->sub_total_cost).'</td>
        ';
        $scrapsSubformRows[] = '<tr>'.$columns.'</tr>';
    }
?>

<div class="info-section">
    <div class="info-section-body">
        <?=
        $subformHelper->render(
            (new CollapsedSubform())
                ->setLabel(__t('Scrap Items'))
                ->setAttribute('subform_headers',[
                    __t('Product'),
                    __t('QTY'),
                    __t('Price'),
                    __t('Production Operation'),
                    __t('Subtotal'),
                ])                                                                             
                ->setAttribute('subform_total_label', __t('Total'))
                ->setAttribute('subform_total_price', format_price_simple($data->scraps_total_cost))
                ->setAttribute('subform_rows', $scrapsSubformRows)
                ->setAttribute('subform_header_icon', 'mdi-basket-fill')
            )
            ?>
    </div>
</div>




<div class="info-section">
    <div class="info-section-body">
        <div class="row">
            <?php
                $actualGrandTotal = $data->total_cost - $data->materials_total_cost + $data->materials_actual_total_cost + ($data->total_indirect_costs ?? 0);
            ?>
            <div class="col-lg-6"></div>
            <div class="col-lg-6">
                <div class="mt-10 mt-lg-0 bg-black text-white fs-10 px-8 py-4 fw-medium d-flex justify-content-between flex-wrap gap-lg-10 gap-5">
                    <span><?=__t('Initial/Actual Cost')?>:</span>
                    <span> <?= (format_price_simple($data->total_cost)) . '/'.format_price_simple($actualGrandTotal). ' '. $currency ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
if ($data->custom_fields_form && ($data->custom_fields_form->getElements() || $data->custom_fields_form->getFieldsets())) {
    $helper = new ShowCustomFields();
    echo '<hr> '.$helper->render($data->custom_fields_form);
} ?>

<script>
    <?php $product= $data->product; if($data->status == 'in_progress'): ?>
        var generatedSerials = [];
        $(function () {
            var modalHtmlContent = `
                <div class="page-container">
                    <form class="m-app-form" data-app-form="finishOrder" method="post" action="<?= getCakeURL(['controller' => 'manufacturing_orders', 'action' => 'finish', 'prefix' => 'owner', 'order_id' => $data->id])?>">
                        <?= csrf_field() ?>
                        <div class="loader-container">
                            <div class="inner-loader"></div>
                        </div>

                        <div class="form-wrapper">
                            <?php
                                $issueMaterialWarning = false;
                                $issueMaterialWarningAllZeros = true;
                                foreach ($data->materials as $material) {
                                    if($material->actual_quantity != 0){
                                        $issueMaterialWarningAllZeros = false;
                                    }
                                    if($material->quantity != $material->actual_quantity){
                                        $issueMaterialWarning = true;
                                    }
                                }

                                if($issueMaterialWarning):
                            ?>
                                <div class="form-err mt-5 mb-16">
                                    <div class="m-app-flash-message">
                                        <div class="l-flash-message ui-flash-message ui-flash-message--info" data-flash-message="true">
                                            <div class="l-flex-row l-flex--align-center">
                                                <div class="l-flex-col-11">
                                                    <div class="l-icon-w-text">
                                                        <i class="far fa-exclamation-circle ui-icon ui-icon--size-16 ui-flash-message-body-icon"></i>
                                                        <div class="ui-flash-message-body-text">
                                                        <?php if($issueMaterialWarningAllZeros): ?>
                                                            <?= __t('You haven’t received any of the materials, so you need to finish the manufacturing order and generate the final product ?') ?>
                                                        <?php else: ?>
                                                            <?= __t('The requested quantity does not match the delivered/consumed quantity') ?>
                                                        <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif;?>
                            <?php
                                if(isset($_SESSION['err_messages'])){
                                    $customErrors = $_SESSION['err_messages'];
                                    unset($_SESSION['err_messages']);
                            ?>
                                <div class="form-err mt-5 mb-16">
                                    <div class="m-app-flash-message">
                                        <div class="l-flash-message ui-flash-message ui-flash-message--danger" data-flash-message="true">
                                <?php
                                    foreach ($customErrors as $errKey => $messages) {
                                        foreach ($messages as $key => $message) {
                                ?>
                                            <div class="l-flex-row l-flex--align-center">
                                                <div class="l-flex-col-11">
                                                    <div class="l-icon-w-text">
                                                        <i class="far fa-exclamation-circle ui-icon ui-icon--size-16 ui-flash-message-body-icon"></i>
                                                        <div class="ui-flash-message-body-text">
                                                            <?= $message ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                <?php }} ?>
                                        </div>
                                    </div>
                                </div>

                            <?php
                                }
                            ?>

                            
                            <div class="l-flex-row l-flex-row--spacing-8">
                                <div class="l-flex-col-lg-6">
                                    <div class="l-input-box">
                                        <label for="main_products_warehouse" class="l-input-label ui-input-label mb-6px"><?= sprintf(__t("%s Warehouse"), __t("Main Product"))?><span class="u-text-color-danger">&nbsp;*</span></label>

                                        <?php
                                            $finishOldData = [];
                                            if(!empty(CakeSession::read('finishOldData'))){
                                                $finishOldData = CakeSession::read('finishOldData');
                                                CakeSession::offsetUnset('finishOldData');
                                            }
                                        ?>
                                        <select data-input-hidden-text-id="main_store_text" name="main_store_id" id="main_store_id" class="form-control l-input ui-input" data-app-form-validate="required" data-app-form-select="true" placeholder="<?= sprintf(__t("%s Warehouse"), __t("Main Product"))?>"  data-app-form-select-template="ajax-simple-2" data-app-form-select-options='{"ajax": { "url": "/v2/owner/store/search_user_can_update?term=d&_type=query&q=__q__" } }' >
                                            <option value="<?= $finishOldData['main_store_id'] ?? '' ?>"><?= $finishOldData['main_store_text'] ?? '' ?></option>
                                            <?php foreach ($defaultStoreOptions as $store) { ?>
                                                <option value="<?= $store["id"] ?>"><?= $store['text'] ?></option>
                                            <?php } ?>
                                        </select>
                                        <input type="hidden" name="main_store_text" id="main_store_text" value="<?= $finishOldData['main_store_text'] ?? '' ?>">
                                    </div>
                                </div>
                                <?php if(!empty($data->scraps)){ ?>
                                    <div class="l-flex-col-lg-6">
                                        <div class="l-input-box">
                                            <label for="scrap_items_warehouse" class="l-input-label ui-input-label mb-6px"><?= sprintf(__t("%s Warehouse"), __t("Scrap Items"))?><span class="u-text-color-danger">&nbsp;*</span></label>
                                            <select data-input-hidden-text-id="scrap_store_text" name="scrap_store_id" id="scrap_store_id" class="form-control l-input ui-input" data-app-form-validate="required" data-app-form-select="true" placeholder="<?= sprintf(__t("%s Warehouse"), __t("Scrap Items"))?>"  data-app-form-select-template="ajax-simple-2" data-app-form-select-options='{"ajax": { "url": "/v2/owner/store/search_user_can_update?term=d&_type=query&q=__q__" } }' >
                                                <option value="<?= $finishOldData['scrap_store_id'] ?? '' ?>"><?= $finishOldData['scrap_store_text'] ?? '' ?></option>
                                                <?php foreach ($defaultStoreOptions as $store) { ?>
                                                    <option value="<?= $store["id"] ?>"><?= $store['text'] ?></option>
                                                <?php } ?>
                                            </select>
                                            <input type="hidden" name="scrap_store_text" id="scrap_store_text" value="<?= $finishOldData['scrap_store_text'] ?? '' ?>">
                                        </div>
                                    </div>
                                <?php } ?>
                                <div class="l-flex-col-lg-6">
                                    <div class="l-input-box">
                                        <label for="name" class="l-input-label ui-input-label mb-6px"><?= __t('Delivery Date')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                        <div class="l-input-w-icon-box l-input-w-icon-box--align-end">
                                            <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                            <input class="l-input ui-input" autocomplete="off" id="delivery_date" value="<?= $finishOldData['delivery_date'] ?? date('Y-m-d') ?>" type="text" name="delivery_date" placeholder="<?= sprintf(__t("Select %s", true),__t("Date", true)) ?>" data-app-form-date="true" data-app-form-validate="required">
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="l-flex-row l-flex-row--spacing-8">
                                <?php if(!empty($product->tracking_type) && $product->tracking_type != TrackStockTypesUtil::QUANTITY_ONLY && $product->tracking_type != TrackStockTypesUtil::TYPE_SERIAL): ?>
                                    <label for="name" class="l-input-label ui-input-label mb-6px"><?= __t("Main Product Details") ?><span class="u-text-color-danger">&nbsp;*</span></label>
                                <?php endif; ?>
                                <?php if($product->tracking_type == TrackStockTypesUtil::TYPE_SERIAL): ?>
                                    <div class="input-container l-flex l-flex--justify-between mb-6px">
                                        <label for="name" class="l-input-label ui-input-label"><?= __t("Main Product Details") ?><span class="u-text-color-danger">&nbsp;*</span></label>
                                        <span class="u-text-color-primary" data-main-generate-all-btn="true" style="cursor: pointer;">
                                            <i  class="mdi mdi-sync u-text-color-primary l-icon ui-icon ui-icon--size-16 generate-icon"></i><?= __t("Generate") ." " . __t("All") ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                <?php if($product->tracking_type == TrackStockTypesUtil::TYPE_LOT || $product->tracking_type == TrackStockTypesUtil::TYPE_LOT_EXPIRY ): ?>
                                    <div class="l-flex-col-lg-6">
                                        <div class="l-input-box">
                                            <input type="text" class="l-input ui-input" name="lot_no" value="<?= $finishOldData['lot_no'] ?? '' ?>" id="lot_no" placeholder="<?= __t("Lot Number") ?>" data-app-form-validate="required" />
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <?php if($product->tracking_type == TrackStockTypesUtil::TYPE_EXPIRY || $product->tracking_type == TrackStockTypesUtil::TYPE_LOT_EXPIRY ): ?>
                                    <div class="l-flex-col-lg-6">
                                        <div class="l-input-box">
                                            <div class="l-input-w-icon-box l-input-w-icon-box--align-end">
                                                <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                                <input class="l-input ui-input" autocomplete="off" id="expiry_date" value="<?= $finishOldData['expiry_date'] ?? '' ?>" type="text" name="expiry_date" placeholder="<?= __t('Expiry Date')?>" data-app-form-date="true" data-app-form-validate="required" />
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <?php if($product->tracking_type == TrackStockTypesUtil::TYPE_SERIAL): ?>
                                    <div class="l-flex-col-lg-12">
                                        <div class="l-input-box">
                                            <select class="ui-input attribute-options-select"
                                                    placeholder="<?= __t("Select Serial(s)") ?>" name="main_product_serials[]"
                                                    id="main_product_serials"
                                                    data-app-form-validate="required"
                                                    data-app-form-tag="true" multiple>
                                                <option value=""></option>
                                                <?php if(!empty($finishOldData['main_product_serials'])){ foreach ($finishOldData['main_product_serials'] as $serial) { ?>
                                                    <option value="<?= $serial ?>" selected><?= $serial ?></option>
                                                <?php }} ?>
                                            </select>
                                        </div>
                                    </div>
                                <?php endif; ?>

                            </div>

                            <?php
                                $showScrapInFinishAction = false;
                                if(!empty($data->scraps)) foreach ($data->scraps as $key => $scrap) {
                                    if(!empty($scrap->product->tracking_type) && $scrap->product->tracking_type != "quantity_only") $showScrapInFinishAction = true;
                                }
                                if($showScrapInFinishAction):
                            ?>
                                    <div class="l-flex-row">
                                        <div class="l-flex-col-lg-12" data-app-parent-form="finish-order-all-scrap-items">
                                            <div class="input-container l-flex l-flex--justify-between mb-6px">
                                                <label for="name" class="l-input-label ui-input-label mb-6px"><?= __t('List Of Tracking Scrap Items')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                                <?php if(!IS_PC) { ?>
                                                <span class="u-text-color-primary" data-scrap-generate-all-btn="true" style="cursor: pointer;"><i  class="mdi mdi-sync u-text-color-primary l-icon ui-icon ui-icon--size-16 generate-icon"></i><?= __t("Generate") ." " . __t("All") ?> 
                                                </span>
                                                <?php } ?>
                                            </div>
                                            <?php if(!empty($data->scraps)) foreach ($data->scraps as $key => $scrap) { if($scrap->product->tracking_type == "quantity_only") continue;?>

                                                <input type="hidden" name="scraps[<?= $key ?>][id]" value="<?= $scrap->id ?>" />

                                                <div class="l-table-box-wrapper" data-app-parent-form="finish-order-scrap-item">
                                                    <table class="l-table-box  subform-table" data-app-form-subform="finish-order-scrap-item" data-app-form-subform-options='{ "sortable": false, "minimumRows":1}'>
                                                        <?php if(IS_PC) { ?>
                                                            <thead>
                                                                <tr class="ui-table-box-row">
                                                                    <th width="600"><?=__t('Product')?></th>
                                                                    <th width="400"><?=__t('QTY')?></th>
                                                                    <?php if($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_LOT_EXPIRY): ?>
                                                                        <th width="600"><?=__t("Lot Number")?></th>
                                                                        <th width="600"><?=__t('Expiry Date')?></th>
                                                                    <?php elseif($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_LOT): ?>
                                                                        <th width="600"><?=__t("Lot Number")?></th>
                                                                    <?php elseif($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_EXPIRY): ?>
                                                                        <th width="600"><?=__t('Expiry Date')?></th>
                                                                    <?php elseif($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_SERIAL): ?>
                                                                        <th width="1000">
                                                                            <div class="input-container l-flex l-flex--justify-between">
                                                                                <span>
                                                                                    <?=__t('Select Serial(s)')?>
                                                                                </span>
                                                                                <span class="u-text-color-primary" data-scrap-generate-all-btn="true" style="cursor: pointer;">
                                                                                    <i  class="mdi mdi-sync u-text-color-primary l-icon ui-icon ui-icon--size-16 generate-icon"></i><?= __t("Generate") ." " . __t("All") ?> 
                                                                                </span>
                                                                            </div>
                                                                        </th>
                                                                    <?php endif; ?>
                                                                </tr>
                                                            </thead>
                                                        <?php } ?>
                                                        <tbody>
                                                            <tr class="ui-table-box-row" data-app-form-subform-row="true">

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input type="hidden" name="scraps[<?= $key ?>][product_id]" value="<?= $scrap->product->id ?>" />
                                                                        <input type="hidden" name="scraps[<?= $key ?>][product_name]" value="<?= $scrap->product->name ?>" />
                                                                        <input readonly type="text" class="l-input ui-input" value="<?= $scrap->product->name . " " . "#" .$scrap->product->product_code ?>" />
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container d-flex-row">
                                                                        <input type="hidden" name="scraps[<?= $key ?>][quantity]" data-product-qty value="<?= $scrap->quantity ?>" />
                                                                        <input type="hidden" name="scraps[<?= $key ?>][price]" value="<?= $scrap->price ?>" />
                                                                        <input readonly type="text" class="l-input ui-input" value="<?= ( !empty($scrap->factor) ? $scrap->quantity / $scrap->factor : $scrap->quantity ) . " " . ( !empty($scrap->factor) ? $scrap->unit_small_name : "" ) ?>" placeholder="<?=__t('QTY')?>" data-app-form-validate="required" data-app-form-number="true" />
                                                                    </div>
                                                                </td>

                                                                <?php if($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_LOT_EXPIRY): ?>
                                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                                            <input type="text" value="<?= $finishOldData['scraps'][$key]['lot_no'] ?? '' ?>" class="l-input ui-input" name="scraps[<?= $key ?>][lot_no]" placeholder="<?=__t("Lot Number")?>" data-app-form-validate="required" />
                                                                        </div>
                                                                    </td>

                                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                                            <div class="l-input-w-icon-box l-input-w-icon-box--align-end">
                                                                                <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                                                                <input class="l-input ui-input" autocomplete="off" value="<?= $finishOldData['scraps'][$key]['expiry_date'] ?? '' ?>" type="text" name="scraps[<?= $key ?>][expiry_date]" placeholder="<?= sprintf(__t("Select %s"),__t("Date")) ?>" data-app-form-date="true" data-app-form-validate="required" />
                                                                            </div>
                                                                        </div>
                                                                    </td>

                                                                <?php elseif($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_LOT): ?>
                                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                                            <input type="text" class="l-input ui-input" name="scraps[<?= $key ?>][lot_no]" value="<?= $finishOldData['scraps'][$key]['lot_no'] ?? '' ?>" placeholder="<?=__t("Lot Number")?>" data-app-form-validate="required" />
                                                                        </div>
                                                                    </td>
                                                                <?php elseif($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_EXPIRY): ?>
                                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                                            <div class="l-input-w-icon-box l-input-w-icon-box--align-end">
                                                                                <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                                                                <input class="l-input ui-input" autocomplete="off" value="<?= $finishOldData['scraps'][$key]['expiry_date'] ?? '' ?>" type="text" name="scraps[<?= $key ?>][expiry_date]" placeholder="<?= sprintf(__t("Select %s", true),__t("Date", true)) ?>" data-app-form-date="true" data-app-form-validate="required" />
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                <?php elseif($scrap->product->tracking_type == TrackStockTypesUtil::TYPE_SERIAL): ?>
                                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                                        <select class="ui-input attribute-options-select"
                                                                                placeholder="<?=__t('Select Serial(s)')?>" name="scraps[<?= $key ?>][serials][]"
                                                                                data-app-form-validate="required"
                                                                                data-app-form-tag="true" multiple>
                                                                            <option value=""></option>
                                                                            <?php 
                                                                                $scrapSerials = $finishOldData['scraps'][$key]['serials'] ?? [];
                                                                                if(!empty($scrapSerials)){ foreach ($scrapSerials as $serial) { ?>
                                                                                <option value="<?= $serial ?>" selected><?= $serial ?></option>
                                                                            <?php }} ?>

                                                                        </select>
                                                                        </div>
                                                                    </td>
                                                                <?php endif; ?>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                            <?php elseif(!empty($data->scraps)) : foreach ($data->scraps as $key => $scrap) { ?>
                                <input type="hidden" name="scraps[<?= $key ?>][product_id]" value="<?= $scrap->product_id ?>" />
                                <input type="hidden" name="scraps[<?= $key ?>][price]" value="<?= $scrap->price ?>" />
                                <input type="hidden" name="scraps[<?= $key ?>][quantity]" value="<?= $scrap->quantity ?>" />
                                <input type="hidden" name="scraps[<?= $key ?>][product_name]" value="<?= $scrap->product->name ?>" />
                            <?php 
                                }
                                endif; 
                            ?>

                        </div>
                        <div class="btn-container">
                            <button type="submit" class="btn btn-success submit-form" style="text-transform: uppercase;"><?php echo __t("Confirm"); ?></button>
                        </div>
                    </form>
                </div>`;

            <?php if(!empty($finishOldData)){ ?>
                addFinishModalHtmlContent()
            <?php } ?>

            function addFinishModalHtmlContent(){
                if($('#modal-container .forms-modal').length < 1) {
                    IzamModal.addHtmlModal(modalHtmlContent, '', '<?= __t('Finish Manufacturing Order') ?>', true);
                    setTimeout(() => {
                        (new window.IzLayout.UiSelect()).initAll();
                        (new window.IzLayout.UiDate()).initAll();
                        (new window.IzLayout.UiTag()).initAll();
                    }, 1);

                    const generateSerialsForSelectize = function (count, selectizeObj, lastNonEmptyValue = null, doneCallback = null){
                        let lastGeneratedSerial = generatedSerials.length > 0 ? generatedSerials[generatedSerials.length - 1] : null;
                        if(lastNonEmptyValue === null) {
                            lastNonEmptyValue = lastGeneratedSerial
                        }
                        $.ajax({
                            type: 'get',
                            data : {
                            type:'sku',
                            number_of_serials:count,
                            starting_serial:lastNonEmptyValue,
                            },
                            url: '/v2/owner/products/generate_serials',
                            dataType: "json",

                            success: function(data) {
                                if (!data) {
                                    if (doneCallback) doneCallback(lastNonEmptyValue);
                                    return;
                                }
                                generatedSerials = generatedSerials.concat(data)
                                let newLastSerial = data[data.length - 1];
                                selectizeObj.addOption(data ? data.map(value => ({ value, text: value })) : [])
                                selectizeObj.setValue(selectizeObj.getValue().concat((data ?? [])))
                                if (doneCallback) doneCallback(newLastSerial);
                            }
                        });
                    }

                    $('[data-input-hidden-text-id]').on('change', function() {
                        let jqueryElement = $(this)
                        let selectizeObj = jqueryElement[0].selectize
                        let valueOption = selectizeObj.options[jqueryElement.val()]
                        $(`[id="${jqueryElement.attr("data-input-hidden-text-id")}"]`).val(valueOption["text"])
                    })


                    $('[data-scrap-generate-all-btn="true"]').on('click', function() {
                        let isPC = <?php echo IS_PC ?? 0; ?> 
                        let generateBtnJqueryElement = $(this);
                        let parentJqueryElement;
                        if(isPC) {
                            parentJqueryElement = generateBtnJqueryElement.parents('[data-app-form-subform="finish-order-scrap-item"]');
                        } else {
                            parentJqueryElement = generateBtnJqueryElement.parents('[data-app-parent-form="finish-order-all-scrap-items"]');
                        }
                        let generatedSerials = [];
                        let lastSerial = null;

                        let serialsJqueryElement = parentJqueryElement.find('[data-app-form-tag="true"]');
                        let quantityJqueryElement = parentJqueryElement.find('[data-product-qty]');
                        let quantity = quantityJqueryElement.val() * 1;

                        function processElement(index) {
                            if (index >= serialsJqueryElement.length) return; // done with all

                            let selectizeObj = serialsJqueryElement[index].selectize;
                            if (!selectizeObj) {
                                processElement(index + 1); // skip if no selectize
                                return;
                            }

                            generateSerialsForSelectize(
                                quantity,
                                selectizeObj,
                                lastSerial,
                                function (newLastSerial) {
                                    // callback after ajax success
                                    lastSerial = newLastSerial;
                                    processElement(index + 1); // move to next element
                                }
                            );
                        }

                        processElement(0); // start from first element
                    })

                    $('[data-main-generate-all-btn="true"]').on('click', function() {
                        let serialsJqueryElement = $('#main_product_serials')
                        let selectizeObj = serialsJqueryElement[0].selectize
                        let currentValue = selectizeObj.getValue()
                        let count = <?= $data->quantity ?> - currentValue.length;
                        generateSerialsForSelectize(count, selectizeObj, currentValue.length ? currentValue[currentValue.length-1] : null)
                    })
                    setTimeout(() => {
                        (new window.IzLayout.UiForm()).initAll();
                    }, 100);
                } else {
                    $("#help-container").addClass("opened");
                    $(".forms-modal").removeClass("minimized");
                }
            }

            $("button[name='finish-manufacturing-order'").on("click", addFinishModalHtmlContent);
            
            
        })
    <?php endif; ?>
 </script>


