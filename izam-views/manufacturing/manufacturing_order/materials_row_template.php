<?php $index = $index ?? '__index__'; ?>

<tr class="ui-table-box-row" data-app-form-subform-row="true">
    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
        <div class="ui-table-box-body-item-editable-input-container">
            <select data-field-name="product_id" name="materials[<?= $index ?>][product_id]" data-quantity-fields-name-prefix="materials[<?= $index ?>]" data-filter-select="materials[<?= $index ?>][product_id]" class="ui-input" data-app-form-select="true" data-app-form-select-template="ajax-simple-3" data-app-form-select-options='{"ajax": { "url": "<?= $productWithBundlesDropdownApi ?>" } }' data-app-form-validate="required">
                <option value=""></option>
                <?php foreach ($productEditOptions as $product) { ?>
                    <option value="<?= $product["id"] ?>" data-data='<?= $product['htmlJson'] ?>'></option>
                <?php } ?>
            </select>
        </div>
    </td>
   
    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
        <div class="ui-table-box-body-item-editable-input-container d-flex-row">
            <div class="filter-group-input" data-qty-type-select="materials[<?= $index ?>][product_id]">
                <div class="ui-table-box-body-item-editable-input-container" >
                    <input  data-formula="{{$manufacturing_order.quantity}} * {{$bom_material.quantity}}" data-field-name="quantity" data-subtotal-factor="true" type="number" name="materials[<?= $index ?>][quantity]" class="l-input ui-input" value="" data-app-form-validate="required" data-app-form-number="true" />
                </div>
            </div>
        </div>
    </td>

    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
        <div class="ui-table-box-body-item-editable-input-container">
            <input type="hidden" data-product-average-price="true" value="">
            <input data-field-name="price" readonly data-subtotal-factor="true" type="text" name="materials[<?= $index ?>][price]" class="ui-input" data-app-form-validate="required" data-app-form-number="true"/>
        </div>
    </td>
    
    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
        <div class="ui-table-box-body-item-editable-input-container">
            <?= $this->includeSection('manufacturing/manufacturing_order/subform_dynamic_dropdown', [
                "dataAttributes" => [
                    "readonly" => "readonly",
                ],
                "dataRelationChild" => "production_routing_id",
                "optional" => true,
                "dataFieldName" => "sub_production_routing_id",
                'url' => "/v2/api/entity/sub_production_routing/list-photo/2?mode=listing&fields%5B0%5D=id&fields%5B1%5D=name",
                'fieldName' => "materials[". $index . "][sub_production_routing_id]",
            ]) ?>
        </div>
    </td>
    <td class="subform-cell-text">
        <span data-subtotal-text-subform="materials">0</span>
        <input data-field-name="sub_total_cost" data-subtotal-input-subform="materials" type="hidden" name="materials[<?= $index ?>][sub_total_cost]" value="0" />
    </td>
    <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
        <button data-row-remove-btn-subform="materials" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
            <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
        </button>
    </td>
</tr>
