<?php
    use \Izam\Daftra\Common\Utils\ManufacturingOrderStatusUtil;
    
    $extends = 'layouts/deprecated-layout';
    extract($extraData);


    $pageMode = isset($isClone) && $isClone ? 'clone' : ( $isEditing ? 'edit' :  ( isset( $_GET['bom']) ? 'convert' : 'create' ) );
    $productReadOnly = $pageMode == 'edit' || $pageMode == 'convert';
    $title = $pageMode == 'edit' ? sprintf(__t('Edit %s'), __t('Manufacturing Order')) : sprintf(__t('Add %s'), __t('Manufacturing Order'));

    $form->prepare();
    
    $this->viewVars['title_for_layout'] = sprintf($title, __t('Manufacturing Order'));
    $this->viewVars['_PageBreadCrumbs'] = $this->sharedVars['generalBreadCrumbs'];

?>

        <?php $this->section('page-start') ?>

        <script>
        var pageMode = "<?= isset($isClone) && $isClone ? 'clone' : ( $isEditing ? 'edit' :  ( isset( $_GET['bom']) ? 'convert' : 'create' ) )?>";
        </script>

        <link href="<?=getCdnAssetsUrl()?>css/forms/plugin_select2.min.css" rel="stylesheet"/>
        <link href="<?=getCdnAssetsUrl()?>css/forms/plugin_select2_rtl.min.css" rel="stylesheet"/>

            <style>
                :root {
                    --bs-dark: #4e5381;
                    --bs-dark-2: #373B50;
                    --bs-dark-3: #75799d;

                    --bs-light: #f6f9fc;
                    --bs-light-2: #c1d2e6;
                    --bs-light-3: #9b9eb8;
                    --bs-secondary: #E4EBF2;
                    --bs-border-width: 1px;
                    --bs-border-style: solid;
                    --bs-border-color: #dee2e6;
                    --bs-light-rgb: 246, 249, 252;
                    --bs-dark-3-rgb: 117, 121, 157;
                }

                .l-input-label {
                    height: 21px;
                }

                .accordion-collapse .accordion-collapse-btn {
                    width: 100%;
                    color: #202124;
                    background-color: #f6f9fc;
                    font-size: 0.875rem;
                    font-weight: 500;
                    border: 1px solid #E4EBF2;
                    line-height: 1;
                    opacity: 1;
                    min-height: 43px;
                }
                @media (min-width: 768px) { 
                    .accordion-collapse .accordion-collapse-btn {
                        padding-right: 17px;
                        padding-left: 17px;
                    }
                }

                .accordion-collapse .accordion-collapse-btn .accordion-collapse-arrow i {
                    font-size: 22px;
                }

                .gap-4,
                .status-icon,
                .status-circle,
                .accordion-collapse .accordion-collapse-btn .accordion-collapse-title {
                    gap: 0.5rem !important;
                }

                .align-items-center,
                .status-icon,
                .status-circle,
                .text-meta,
                .title-big-text,
                .accordion-collapse .accordion-collapse-btn .accordion-collapse-title,
                .toast {
                    align-items: center !important;
                }

                .d-inline-flex,
                .accordion-collapse .accordion-collapse-btn .accordion-collapse-title {
                    display: inline-flex !important;
                }

                .accordion-collapse .accordion-collapse-btn .accordion-collapse-title i {
                    font-size: 22px;
                }

                .text-dark-3 {
                    --bs-text-opacity: 1;
                    color: rgba(117, 121, 157, 1) !important;
                }

                .d-none {
                    display: none !important;
                }

                [dir=ltr] .text-end,
                [dir=rtl] .text-start {
                    text-align: right !important;
                }

                [dir=rtl] .text-end,
                [dir=ltr] .text-start {
                    text-align: left !important;
                }



                [dir=rtl] .text-end {
                    text-align: left !important;
                }

                [dir=ltr] .accordion-collapse-btn {
                    text-align: left !important;
                }

                [dir=rtl] .accordion-collapse-btn {
                    text-align: right !important;
                }


                .accordion-collapse .accordion-collapse-btn[aria-expanded^=false] [aria-expanded^=true] {
                    display: none;
                }

                .accordion-collapse .accordion-collapse-btn[aria-expanded^=true] [aria-expanded^=false] {
                    display: none;
                }

                /* Subform style */
                .subform-table thead th {
                    font-size: 0.875rem;
                    font-weight: 400;
                    color: var(--bs-dark-3);
                    padding: 10px;
                    background: var(--bs-light);
                    border: 1px solid var(--bs-secondary);
                }

                .subform-table tbody td {
                    border: 1px solid var(--bs-secondary);

                }

                .subform-table tbody tr td.subform-cell-text {
                    background: var(--bs-light);
                    color: #202124;
                    padding: 10px;
                    font-size: 0.875rem;
                }


                .subform-table tfoot tr td.subform-cell-text,
                .subform-table tfoot tr th.subform-cell-text {
                    background: var(--bs-light);
                    color: #202124;
                    padding: 10px;
                    font-size: 0.875rem;
                }

                .subform-table tbody tr td .input-container .form-group {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 0;
                }

                .ui-table-box-body-item--editable  .selectize-input, .ui-table-box-body-item--editable  textarea, .ui-table-box-body-item--editable  input{
                    height: 100%;
                }

                [dir="ltr"] .border-start  {
                    border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                }
                [dir="rtl"] .border-start {
                    border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                }

                .border-bottom {
                    border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                }
               
                [dir="ltr"] .border-end  {
                    border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                }
                [dir="rtl"] .border-end {
                    border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                }

                .d-table-row {
                    display: table-row !important;
                }

                .d-none {
                    display: none !important;
                }

                .fw-medium {
                    font-weight: 500 !important;
                }

                .fw-bold {
                    font-weight: 600 !important;
                }

                .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container.d-flex-row {
                    flex-direction: row;
                }

                .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container.d-flex-row>* {
                    flex: 1 1;
                }

                .d-flex {
                    display: flex;
                }

                .d-flex.justify-content-between {
                    display: flex;
                    justify-content: space-between;
                }

                .d-flex.align-items-center {
                    align-items: center;
                }

                .bg-light {
                    --bs-bg-opacity: 1;
                    background-color: rgba(var(--bs-light-rgb), 1) !important;
                }

                .filter-group-input {
                    display: flex;
                }

                .filter-group-input>* {
                    flex: 4 1 auto;
                }

                .text-dark-3 {
                    --bs-text-opacity: 1;
                    color: rgba(var(--bs-dark-3-rgb), 1) !important;
                }

                .px-10 {
                    padding-right: 1.25rem !important;
                    padding-left: 1.25rem !important;
                }

                .full-height {
                    height: 100%;
                }

                .total-cell {
                    background-color: rgba(32, 33, 36, 1);
                    color: #FFF;
                    font-weight: 500;
                    font-size: 1.25rem;
                    gap: 1.25rem;
                    padding-top: 0.5rem;
                    padding-bottom: 0.5rem;
                    padding-right: 1rem;
                    padding-left: 1rem;
                    margin-bottom: 1.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }


                @media (min-width: 992px) {
                    .d-lg-inline-block {
                        display: inline-block !important;
                    }

                    .subform-table tbody tr td {
                        border-bottom: 1px solid var(--bs-secondary);
                    }

                    .d-lg-table-row {
                        display: table-row !important;
                    }

                    .d-lg-none {
                        display: none !important;
                    }

                }

                @media (max-width: 993px) {
                    .subform-table thead {
                        display: none;
                    }
                }

                .ui-table-box-body-item-editable-input-container {
                    position: relative;
                }

                .generate-icon {
                    display: flex;
                }

                .prefix-text {
                    display: none;
                }

                .generate-icon,
                .prefix-text {
                    cursor: pointer;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    padding: 0 10px;
                    align-items: center;
                }

                .ui-table-box-body-item-editable-input-container:hover .prefix-text {
                    display: flex;
                }

                [dir="ltr"] .generate-icon,
                [dir="ltr"] .prefix-text {
                    right: 0;
                }

                [dir="rtl"] .generate-icon,
                [dir="rtl"] .prefix-text {
                    left: 0;
                }

                [dir="ltr"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .generate-input {
                    padding-right: 52px;
                }

                [dir="rtl"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .generate-input {
                    padding-left: 52px;
                }

                [dir="ltr"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .prefix-input {
                    padding-right: 102px;
                }

                [dir="rtl"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .prefix-input {
                    padding-left: 102px;
                }
                tr .select2-container--bootstrap4 .select2-selection{
                    border: 0px !important;
                }

                /* select2 */
                .select2-container--bootstrap4 .select2-selection {
                    min-height: 42px !important;
                    border-color: #e4ebf2;
                }

                .select2-container--bootstrap4 .select2-selection:hover {
                    border-color: #75799d;
                }
                .select2-container--bootstrap4.select2-container--focus .select2-selection {
                    border-color: #1877f2 !important;
                    box-shadow: 0 0 0 1px #1877f2 !important;
                }

                .select2-container .select2-selection--single .select2-selection__rendered {
                    padding-left: 12px;
                    padding-right: 50px;
                }
                [dir=rtl] .select2-container .select2-selection--single .select2-selection__rendered {
                    padding-right: 12px;
                    padding-left: 50px;
                }
                .select2-container--bootstrap4 .select2-selection__clear {
                    text-align: center;
                    padding-left: 1px;
                    position: absolute !important;
                    top: 50%;
                    transform: translateY(-50%);
                    margin: 0 !important;
                    right: 25px;
                }
                [dir=rtl] .select2-container--bootstrap4 .select2-selection__clear {
                    padding-right: 0px;
                    left: 25px;
                    right: auto;
                }

                .select2-container, .select2-container .selection {
                    height: 100%;
                }
                tr .select2-container--bootstrap4 .select2-selection {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 100%;
                    width: 100%;
                }
                .select2-container .select2-selection--single .select2-selection__rendered {
                    width: 100%;
                }
                .disabled, [disabled], div.disabled, input.disabled, select.disabled, span.disabled, textarea.disabled {
                    opacity: .5;
                    pointer-events: none;
                }

                input[type="select-one"] {
                    opacity: 0;
                    position: absolute;
                    left: -10000px;
                }

                .l-input-w-icon-box .ui-input-icon {
                    opacity: 1;
                    color: #202124;
                }
                [dir="ltr"]  .select2-results__options {
                    padding-left: 0 !important;
                }
                [dir="rtl"] ul.select2-results__options {
                    padding-right: 0 !important;
                }
                [dir="ltr"]  .select2-results__options.select2-results__options--nested li{
                    padding-left: 2rem;
                }

                [dir="rtl"]  .select2-results__options.select2-results__options--nested li{
                    padding-right: 2rem;
                }

                @media (min-width: 992px) {
                    .m-app-page-head-all {
                        margin: 0px 16px;
                    }
                }

                @media (max-width: 767.98px) {
                    .m-app-page-head-all .l-container {
                        padding-left: 4px;
                        padding-right: 4px;
                    }
                    .ui-table-box-row.d-lg-none td button {
                        min-height: 42px;
                        align-items: center;
                        margin-bottom: 24px;
                    }

                    /* table responsive */
                    table td {
                        display: block;
                        font-size: .8em;
                        text-align: right;
                    }
                    table tbody td.subform-cell-text,
                    .ui-table-box-body-item--removeable {
                        height:auto;
                        min-height: 50px;
                        display: inline-block;
                        border-top:none;
                    }
                    table tbody .ui-table-box-body-item--removeabl {
                        width: 50px;
                    }
                    table thead {
                        border: none;
                        clip: rect(0 0 0 0);
                        height: 1px;
                        margin: -1px;
                        overflow: hidden;
                        padding: 0;
                        position: absolute;
                        width: 1px;
                    }
                    .ui-table-box-body-item--removeable .ui-table-box-body-item-removeable-btn {
                        border-top: none;
                    }
                    [dir="ltr"] .ui-table-box-body-item--removeable .ui-table-box-body-item-removeable-btn  {
                        border-left: none !important;
                    }
                    [dir="rtl"] .ui-table-box-body-item--removeable .ui-table-box-body-item-removeable-btn  {
                        border-right: none !important;
                    }
                    .subform-table tbody tr td.subform-cell-text {
                        min-height: 51px;
                        width: calc(100% - 52px);
                    }
                    .subform-table tbody td {
                        border-top: none;
                    }
                    [dir="ltr"] .subform-table tbody tr td.subform-cell-text {
                        border-right: none;
                    }
                    [dir="rtl"] .subform-table tbody tr td.subform-cell-text {
                        border-left: none;
                    }

                    [dir="ltr"] .border-md-start  {
                    border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                    }
                    [dir="rtl"] .border-md-start {
                        border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                    }
                    [dir="ltr"] .border-md-end  {
                        border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                    }
                    [dir="rtl"] .border-md-end {
                        border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
                    }
                    .px-0 {
                        padding-right: 0px;
                        padding-left: 0px;
                    }
                }
                .ui-table-add-btn {
                    min-height: 42px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 0 !important;
                    margin-top: -1px;
                    margin-bottom: -1px;
                }

                [dir="ltr"] .ui-table-add-btn  {
                    margin-left: -1px;
                }
                [dir="rtl"] .ui-table-add-btn  {
                    margin-right: -1px;
                }
            </style>
        <form class="m-app-form" action="<?= $formUrl ?>" method="POST" data-app-form="manufacturing_order">
        <?php $this->endSection() ?>


                <input type="hidden" name="_method" value="<?= $method ?>">
                <input type="hidden" name="_token" value="<?= csrf_token() ?>">
                <input type="hidden" name="id" value="<?= isset($data['id']) && $pageMode != 'clone' ? $data['id'] : '' ?>" id="id">
                <?php if(!$isEditing){ ?>
                    <input type="hidden" name="staff_id" value="<?= getAuthStaff('id') ?>">
                <?php } ?>
                <?php if (\App\Facades\Plugins::pluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)): ?>
                    <input type="hidden" name="branch_id" value="<?= \App\Facades\Branch::getCurrentBranchID() ?>">
                <?php endif; ?>

                <div class="m-app-page-head">
                    <div class="m-app-page-head-all">
                        <div class="l-container">
                            <div class="l-flex-row l-flex--align-center">
                                <div class="l-flex-col-auto l-flex-col-lg-6 u-text-align-start">
                                    <div class="l-btn-list-h l-btn-list-h--inline">
                                        <?php if(!$isEditing || (isset($data['status']) && $data['status'] == ManufacturingOrderStatusUtil::DRAFT) ){ ?>
                                            <button type="button" id="draftBtn" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-secondary u-text-color-action u-text-hover-color-action">
                                                <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                                <span class="ui-btn-inner-content">
                                                    <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-content-save"></i>
                                                    <span class="ui-btn-inner-text d-lg-none"><?= __t('Draft') ?></span>
                                                    <span class="ui-btn-inner-text d-lg-inline-block" style="display: none;"><?= __t('Save As Draft') ?></span>
                                                </span>
                                            </button>
                                        <?php } ?>
                                    </div>
                                </div>

                                <div class="l-flex-col-auto l-flex-col-lg-6 u-text-align-end" style="margin-inline-start: auto">
                                    <div class="l-btn-list-h l-btn-list-h--inline">
                                        <a href="<?= $cancelUrl ?>" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-secondary u-text-color-action u-text-hover-color-action">
                                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                            <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-close-thick"></i>
                                                <span class="ui-btn-inner-text"><?= __t('Cancel') ?></span>
                                            </span>
                                        </a>
                                        <button class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white" type="submit">
                                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                                            <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-content-save"></i>
                                                <span class="ui-btn-inner-text"><?= __t('Save') ?></span>
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="m-app-content">
                    <div class="l-create-card-box">
                        <div class="l-container">

                            <div class="ui-card">
                                <div class="ui-card-header">
                                    <h3 class="ui-card-header-title"><?= sprintf(__t("%s Information"), __t("Manufacturing Order")) ?></h3>
                                </div>
                                <div class="ui-card-content ui-card-content-box--spacing-bottom-0">
                                    <div class="ui-card-content-box">
                                        <div id="order-information-fields" class="l-flex-row l-flex-row--spacing-8">
                                            <div class="l-flex-col-lg-4">
                                                <div class="l-input-box">
                                                    <label for="name" class="l-input-label ui-input-label"><?= __t("Name") ?><span class="u-text-color-danger">&nbsp;*</span></label>
                                                    <input type="text" class="l-input ui-input" name="name" id="name" value="<?= isset($data['name']) ? htmlspecialchars($data['name']) : '' ?>" data-app-form-validate="required" />
                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'name']) ?>
                                                </div>
                                            </div>

                                            <?php $codeValue = isset($data['code']) && is_array($data['code']) ? $data["code"]["code"] : $form->getFieldSets()['code']->getElements()['code']->getValue()?? "000001";?>
                                            <?php $autoNumberGenerated = $form->getFieldSets()['code']->getElements()['generated']->getValue() ?? "000002"; ?>

                                            <div class="l-flex-col-lg-2">
                                                <div class="l-input-box">
                                                    <label for="code" class="l-input-label ui-input-label"><?=__t('Code')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                                    <input type="text" class="l-input ui-input" name="code[code]" id="code" value="<?=$codeValue?>" data-app-form-validate="required" />
                                                    <input type="hidden" class="l-input ui-input" name="code[generated]" id="code" value="<?=$autoNumberGenerated?>" data-app-form-validate="required" />
                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'code.code']) ?>
                                                </div>
                                            </div>

                                            <div class="l-flex-col-lg-3">
                                                <label for="name" class="l-input-label ui-input-label"><?= __t('Date From')?></label>
                                                <div class="l-input-w-icon-box l-input-w-icon-box--align-end">
                                                    <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                                    <div class="flatpickr-wrapper">
                                                        <input class="l-input ui-input" autocomplete="off"
                                                        value="<?= !empty($isClone) && $isClone && empty($errors) ? '' : $data["date_from"] ?? '' ?>"
                                                        id="from_date"
                                                        type="text" name="date_from"
                                                        data-app-form-date="true" />
                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'date_from']) ?>
                                                    </div>

                                                </div>
                                            </div>
                                            <div class="l-flex-col-lg-3">
                                                <div class="l-input-box l-input-box--spacing-16">
                                                    <label for="name"
                                                    class="l-input-label ui-input-label"><?= __t('Date To')?></label>
                                                    <div class="l-input-w-icon-box l-input-w-icon-box--align-end ">
                                                    <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                                    <div class="flatpickr-wrapper">
                                                        <input class="l-input ui-input" autocomplete="off" 
                                                        value="<?= isset($isClone) && $isClone && empty($errors) ? '' : $data["date_to"] ?? '' ?>" 
                                                        id="to_date"
                                                        type="text" name="date_to"
                                                        data-app-form-date="true" />
                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'date_to']) ?>
                                                    </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="l-flex-col-lg-6">
                                                <div class="l-input-box">
                                                    <label for="journal_account_id" class="l-input-label ui-input-label"><?= __t("Account") ?><span class="u-text-color-danger">&nbsp;*</span></label>
                                                    <?php if($orderHasAnyTransaction){ ?>
                                                    <div class="disabled">
                                                    <?php } ?>
                                                    <select name="journal_account_id" id="journal_account_id" class="account-select ui-input" data-app-form-validate="required" data-app-form-select-options='<?= $journalAccountsDropdownMetaData ?>' >
                                                        <?php if(!empty($data['journal_account_id'])) { ?>
                                                            <option value="<?= $data['journal_account_id'] ?>" selected><?= !empty($journalAccountsMapById[$data['journal_account_id']]) ? $journalAccountsMapById[$data['journal_account_id']]?->text : '' ?></option>
                                                        <?php } ?>
                                                    </select>
                                                    <?php if($orderHasAnyTransaction){ ?>
                                                    </div>
                                                    <?php } ?>
                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'journal_account_id']) ?>                                                
                                                </div>
                                            </div>

                                            <div class="l-flex-col-lg-6">
                                                <div class="l-input-box">
                                                    <label for="employees[]" class="l-input-label ui-input-label"><?= $form->get("employees")->getLabel() ?></label>
                                                    <input type="hidden" name="employees" value="">
                                                    <select name="employees[]" id="employees[]" class="l-input ui-input" data-app-form-select="true" autocomplete="off" multiple data-app-form-select-template="client-employee" data-app-form-select-options='{"ajax":{"url":"/v2/owner/staff/search?term=d&_type=query&q=__q__"},"multiplePlus":true,"clearButton":true}'>
                                                        <option value=""></option>
                                                        <?php if(!empty($form->get("employees")->getValue())) { ?>
                                                            <?php foreach($form->get("employees")->getValue() as $arrayValue) { ?>
                                                                <option data-data='<?= !empty($staffsMapById[$arrayValue]) ? ($staffsMapById[$arrayValue]['htmlJson'] ?? '') : '' ?>' value="<?= $arrayValue ?>" selected></option>
                                                            <?php } ?>
                                                        <?php } ?>
                                                        <?php foreach($staffsMapById as $staffId => $staffData) { ?>
                                                            <option data-data='<?= $staffData['htmlJson'] ?? '' ?>' value="<?= $staffId ?>"></option>
                                                        <?php } ?>

                                                    </select>
                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "employees.0"]) ?>
                                                </div>
                                            </div>

                                            <div class="l-flex-col-lg-6">
                                                <div class="l-input-box">
                                                    <label for="client_id" class="l-input-label ui-input-label"><?= $form->get("client_id")->getLabel() ?></label>
                                                    <select name="client_id" id="client_id" class="l-input ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="client-employee" data-app-form-select-options='{"ajax": { "url": "<?= $clientDropdownApi ?>" } }'>
                                                        <?php if(!empty($client)) { ?>
                                                            <option value="<?= $client['id'] ?>" data-data='<?= $client['htmlJson'] ?? '' ?>' selected></option>
                                                        <?php }else{ ?>
                                                            <option value="" selected></option>
                                                        <?php } ?>
                                                        <?php foreach($clientsDefaultOptions as $clientOption) { ?>
                                                            <option data-data='<?= $clientOption['htmlJson'] ?? '' ?>' value="<?= $clientOption['id'] ?>"></option>
                                                        <?php } ?>
                                                    </select>
                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'client_id']) ?>
                                                </div>
                                            </div>

                                            <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>

                                            <div class="l-flex-col-lg-6">
                                                <div class="l-input-box">
                                                    <label for="product_id" class="l-input-label ui-input-label"><?= $label ?? __t($form->get('product_id')->getLabel()) ?><span class="u-text-color-danger">&nbsp;*</span></label>
                                                    <select value="" <?= $productReadOnly ? "readonly" : "" ?> data-relation-parent="product_id" name="product_id" id="product_id" class="l-input ui-input" data-filter-select="product_id" data-app-form-select="true" autocomplete="off" data-app-form-select-template="ajax-simple-3" data-app-form-select-options='{"ajax": { "url": "<?= $productDropdownApi ?>" } }' data-app-form-validate="required">
                                                        <?php if(!empty($data["product_id"])){ ?>
                                                            <option value="<?= $data["product_id"] ?>" data-data='<?= $productsMapById[$data["product_id"]]['htmlJson'] ?>' selected></option>
                                                        <?php }else { ?>
                                                            <option value=""></option>
                                                        <?php } ?>
                                                        <?php foreach ($productsDefaultOptions as $product) { ?>
                                                            <option value="<?= $product["id"] ?>" data-data='<?= $product['htmlJson'] ?>'></option>
                                                        <?php } ?>
                                                    </select>
                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'product_id']) ?>
                                                </div>
                                            </div>

                                            <div class="l-flex-col-lg-6">
                                                <div class="l-input-box">
                                                    <label for="quantity" class="l-input-label ui-input-label"><?= sprintf( __t("Req. %s"), __t("Quantity")) ?><span class="u-text-color-danger">&nbsp;*</span></label>
                                                    <div class="filter-group-input" data-qty-type-select="product_id">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <?php 
                                                                $quantityValue = null;
                                                                if(!empty($data)){
                                                                    $quantityValue = $data['quantity'];
                                                                    if(!empty($data['factor']) && !empty($productsMapById[$data['product_id']]['units']) && empty($errors)){
                                                                        $quantityValue = $data['quantity'] / $data['factor'];
                                                                    }
                                                                }
                                                            ?>

                                                            <input type="text" class="l-input ui-input" name="quantity" id="quantity" value="<?= $quantityValue ?>" data-app-form-validate="required" data-app-form-number="true"  <?= !empty($bomData) && !empty($bomData['quantity']) ? "data-bom-value=\"{$bomData['quantity']}\"" : '' ?> />
                                                            <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "quantity"]) ?>
                                                        </div>
                                                        <?php if(!empty($data) && !empty($data['factor']) && !empty($productsMapById[$data['product_id']]['units'])){ ?>
                                                            <div class="ui-table-box-body-item-editable-input-container unit-factor-select">
                                                                <select data-is-unit-factor-select="true" name="unit_factor_id" class="l-input ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="currency" data-app-form-validate="required">
                                                                    <?php $selectedUnitData = null; $data['unit_factor_id'] = !empty($data['unit_factor_id']) ? $data['unit_factor_id'] : 0; ?>
                                                                    <?php foreach($productsMapById[$data['product_id']]['units'] as $unit) { ?>
                                                                        <?php if($unit['id'] == $data['unit_factor_id']){ ?>
                                                                            <?php $selectedUnitData = $unit; ?>
                                                                            <option value="<?= $unit['id'] ?>" selected>
                                                                                <?= $unit['small_name'] ?>
                                                                            </option>
                                                                        <?php } else { ?>
                                                                            <option value="<?= $unit['id'] ?>">
                                                                                <?= $unit['small_name'] ?>
                                                                            </option>
                                                                        <?php } ?>
                                                                    <?php } ?>

                                                                </select>
                                                                <?php if(!empty($selectedUnitData)){ ?>
                                                                    <input data-field-name="factor" value="<?= $selectedUnitData['factor'] ?>" type="hidden" name="factor" />
                                                                    <input data-field-name="unit_name" value="<?= $selectedUnitData['factor_name'] ?>" type="hidden" name="unit_name" />
                                                                    <input data-field-name="unit_small_name" value="<?= $selectedUnitData['small_name'] ?>" type="hidden" name="unit_small_name" />
                                                                <?php } ?>
                                                            </div>
                                                            <script defer>
                                                                setTimeout(function () {
                                                                    $(`#product_id`).trigger("mainProductUnitFactorFieldsAdded");                                                                                                
                                                                },2100)
                                                            </script>
                                                        <?php } ?>
                                                        <?php if(!empty($data) && !empty($data['factor']) && empty($productsMapById[$data['product_id']]['units'])){ ?>
                                                            <input value="" type="hidden" name="unit_factor_id" />
                                                            <input value="" type="hidden" name="factor" />
                                                            <input value="" type="hidden" name="unit_name" />
                                                            <input value="" type="hidden" name="unit_small_name" />
                                                        <?php } ?>

                                                    </div>
                                                </div>
                                            </div>


                                            <div class="l-flex-col-lg-6">
                                                <?= $this->includeSection('manufacturing/manufacturing_order/dynamic_dropdown', [
                                                    'optional' => true,
                                                    'dataRelationChild' => 'product_id',
                                                    'readonly' => in_array($pageMode, ['create', 'edit', 'convert']) ? true : null, 'form'=> $form , 'data' => $data, 'fieldName' => 'bom_id', 'bomData' => $bomData]) ?>
                                            </div>


                                            <div class="l-flex-col-lg-6">
                                                <?= $this->includeSection('manufacturing/manufacturing_order/dynamic_dropdown', [
                                                    'optional' => true,
                                                    'dataRelationParent' => 'production_routing_id',
                                                    'fieldName' => 'production_routing_id',
                                                    'fieldLabel' => __t("Production Routing"),
                                                    'defaultValueOptions' => $defaultProductionRoutingOptions,
                                                    'valueOptionLabel' => !empty($form->get("production_routing_id")->getValueOptions())? $form->get("production_routing_id")->getValueOptions()[0]['label'] : "",
                                                    'data' => $data,
                                                ]) ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="l-show-card-section-title-box">
                                        <?php if(!empty($errors) && !empty($errors['materials'])): ?>
                                        <ul class="ui-input-errors is-backend-errors">
                                            <li>
                                                <?= sprintf(__t("You should add one Row at least in %s", true), __t('Materials')) ?>
                                            </li>
                                        </ul>
                                        <?php endif; ?>
                                        <div class="l-flex-row">
                                            <div class="l-flex-col-12">
                                                <div class="accordion-collapse">
                                                    <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='materials']" aria-expanded="true" aria-controls="materials">
                                                        <div class="l-flex-row l-flex-row--spacing-4 l-flex--nowrap">
                                                            <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                                <span class="accordion-collapse-title">
                                                                    <i class="mdi mdi-package-variant"></i>
                                                                    <span><?= __t('Materials') ?> (<span data-header-number-subform="materials"><?= isset($data['materials']) ? count($data['materials']) : 1 ?></span>)</span>
                                                                </span>
                                                            </span>
                                                            <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                                <div aria-expanded="false" style="margin-top: 5px;">
                                                                    <div class="l-flex-row ">
                                                                        <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </div>
                                                                        <div class="l-flex-col-12 l-flex-col-lg-2" data-total-text-subform="materials">
                                                                            <?= format_price_simple((float)($data['materials_total_cost'] ?? 0.00)) ?>
                                                                        </div>
                                                                        <input data-total-input-subform="materials" type="hidden" name="materials_total_cost" value="<?= $data['materials_total_cost'] ?? "0.00" ?>" />
                                                                    </div>
                                                                </div>
                                                            </span>
                                                            <span class="l-flex-col-1 text-end px-0">
                                                                <span class="accordion-collapse-arrow">
                                                                    <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                                    <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                                </span>
                                                            </span>
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="collapse show" data-accordion-collapse='materials'>
                                            <div class="l-flex-row">
                                                <div class="l-flex-col-lg-12">
                                                    <div class="l-table-box-wrapper">
                                                        <table class="l-table-box  subform-table " data-app-form-subform="materials" data-app-form-subform-options='{ "sortable": false }'>
                                                            <thead>
                                                                <tr class="ui-table-box-row">
                                                                    <th width="1000"><?= __t('Product') ?></th>
                                                                    <th width="1000"><?= __t("Planned QTY") ?></th>
                                                                    <th width="500"><?= __t("Initial Price") ?></th>
                                                                    <th width="1000"><?= __t("Production Operation") ?></th>
                                                                    <th width="550"><?= __t("Subtotal") ?></th>
                                                                    <th width="50"></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php if(isset($data['materials'])){ foreach($data['materials'] as $key => $material) { ?>
                                                                    <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                        <?php if($pageMode == 'edit' && !empty($material['id'])){ ?>
                                                                            <input value="<?= $material['id'] ?>" type="hidden" name="materials[<?= $key ?>][id]"/>
                                                                        <?php } ?>
                                                                        <?php if(!empty($material['bom_materials_id'])){ ?>
                                                                            <input value="<?= $material['bom_materials_id'] ?>" type="hidden" name="materials[<?= $key ?>][bom_materials_id]"/>
                                                                        <?php } ?>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <select data-field-name="product_id" id="materials[<?= $key ?>][product_id]" name="materials[<?= $key ?>][product_id]" data-quantity-fields-name-prefix="materials[<?= $key ?>]" data-filter-select="materials[<?= $key ?>][product_id]" class="ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="ajax-simple-3" data-app-form-select-options='{"ajax": { "url": "<?= $productWithBundlesDropdownApi ?>" } }' data-app-form-validate="required">
                                                                                    <option value=""></option>
                                                                                    <option value="<?= $material['product_id'] ?>" selected>
                                                                                        <?= !empty($productsMapById[$material['product_id']]) ? $productsMapById[$material['product_id']]['text'] : '' ?>
                                                                                    </option>
                                                                                    <?php foreach ($productsWithBundlesDefaultOptions as $product) { ?>
                                                                                        <option value="<?= $product["id"] ?>" data-data='<?= $product['htmlJson'] ?>'></option>
                                                                                    <?php } ?>
                                                                                </select>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$key.product_id"]) ?>
                                                                            </div>
                                                                        </td>
                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container d-flex-row">
                                                                                <div class="filter-group-input" data-qty-type-select="materials[<?= $key ?>][product_id]">
                                                                                    <?php 
                                                                                        $quantityValue = null;
                                                                                        $quantityValue = $material['quantity'];
                                                                                        if(!empty($material['factor']) && !empty($productsMapById[$material['product_id']]['units']) && empty($errors)){
                                                                                            $quantityValue = $material['quantity'] / $material['factor'];
                                                                                        }
                                                                                    ?>

                                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                                        <input data-formula="{{$manufacturing_order.quantity}} * {{$bom_material.quantity}}" data-field-name="quantity" data-subtotal-factor="true" type="text" name="materials[<?= $key ?>][quantity]" class="l-input ui-input" value="<?= $quantityValue ?>" data-app-form-validate="required" data-app-form-number="true" <?= !empty($material['bom_materials_id']) && !empty($bomMap['materials'][$material['bom_materials_id']]) ? "data-bom-value=\"{$bomMap['materials'][$material['bom_materials_id']]['quantity']}\"" : '' ?> />
                                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$key.quantity"]) ?>
                                                                                    </div>
                                                                                    <?php if(!empty($material['factor']) && !empty($productsMapById[$material['product_id']]['units'])){ ?>
                                                                                        <?php $selectedUnitData = null; $material['unit_factor_id'] = !empty($material['unit_factor_id']) ? $material['unit_factor_id'] : 0; ?>
                                                                                        <div class="ui-table-box-body-item-editable-input-container unit-factor-select">
                                                                                            <select data-is-unit-factor-select="true" name="materials[<?= $key ?>][unit_factor_id]" class="l-input ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="currency" data-app-form-validate="required">
                                                                                                <?php foreach($productsMapById[$material['product_id']]['units'] as $unit) { ?>
                                                                                                    <?php if($unit['id'] == $material['unit_factor_id']){ ?>
                                                                                                        <?php $selectedUnitData = $unit; ?>
                                                                                                        <option value="<?= $unit['id'] ?>" selected>
                                                                                                            <?= $unit['small_name'] ?> 
                                                                                                        </option>
                                                                                                        <?php } else { ?>
                                                                                                        <option value="<?= $unit['id'] ?>">
                                                                                                            <?= $unit['small_name'] ?> 
                                                                                                        </option>
                                                                                                        <?php } ?>
                                                                                                        <?php } ?>
                                                                                                        
                                                                                                    </select>
                                                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$key.unit_factor_id"]) ?>
                                                                                                    <?php if(!empty($selectedUnitData)){ ?>
                                                                                                        <input data-field-name="factor" value="<?= $selectedUnitData['factor'] ?>" type="hidden" name="materials[<?= $key ?>][factor]" />
                                                                                                        <input data-field-name="unit_name" value="<?= $selectedUnitData['factor_name'] ?>" type="hidden" name="materials[<?= $key ?>][unit_name]" />
                                                                                                <input data-field-name="unit_small_name" value="<?= $selectedUnitData['small_name'] ?>" type="hidden" name="materials[<?= $key ?>][unit_small_name]" />
                                                                                                <?php } ?>
                                                                                                <?php if(!empty($material['factor']) && empty($productsMapById[$material['product_id']]['units'])){ ?>
                                                                                                    <input value="" type="hidden" name="materials[<?= $key ?>][unit_factor_id]" />
                                                                                                    <input value="" type="hidden" name="materials[<?= $key ?>][factor]" />
                                                                                                <input value="" type="hidden" name="materials[<?= $key ?>][unit_name]" />
                                                                                                <input value="" type="hidden" name="materials[<?= $key ?>][unit_small_name]" />
                                                                                                <?php } ?>

                                                                                            </div>
                                                                                            <script defer>
                                                                                                setTimeout(function () {
                                                                                                    $(`[name="materials[<?= $key ?>][product_id]"]`).trigger("productUnitFactorFieldsAdded");                                                                                                
                                                                                                },2100)
                                                                                                </script>
                                                                                    <?php } ?>
                                                                                    
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <input type="hidden" data-product-average-price="true" value="<?= !empty($productsMapById[$material['product_id']]) ? $productsMapById[$material['product_id']]['average_price'] ?? '' : '' ?>">
                                                                                <input data-field-name="price" value="<?= $material['price'] ?>" <?= !empty($material['price']) && $material['price'] > 0 ? 'readonly' : '' ?> data-subtotal-factor="true" type="text" name="materials[<?= $key ?>][price]" class="ui-input" data-app-form-validate="required" />
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$key.price"]) ?>
                                                                            </div>
                                                                        </td>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <?= $this->includeSection('manufacturing/manufacturing_order/dynamic_dropdown', [
                                                                                    "dataRelationChild" => "production_routing_id",
                                                                                    "optional" => true,
                                                                                    'rowKey' => $key,
                                                                                    'record' => $material,
                                                                                    'subform' => "materials",
                                                                                    'fieldName' => "sub_production_routing_id",
                                                                                    'fieldLabel' => __t("Production Operation"),
                                                                                    'error_input_name' => "materials.$key.sub_production_routing_id",
                                                                                ]) ?>
                                                                            </div>
                                                                        </td>
                                                                        <td class="subform-cell-text border-start">
                                                                            <span data-subtotal-text-subform="materials"><?= format_price_simple((float)($material['sub_total_cost'] ?? 0.00)) ?></span>
                                                                            <input data-field-name="sub_total_cost" data-subtotal-input-subform="materials" type="hidden" name="materials[<?= $key ?>][sub_total_cost]" value="<?= $material['sub_total_cost'] ?>" />
                                                                        </td>
                                                                        <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                            <button data-row-remove-btn-subform="materials" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
                                                                                <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                                <?php } else { ?>
                                                                    <?= $this->includeSection('manufacturing/manufacturing_order/materials_row_template', ['index' => 0,'productWithBundlesDropdownApi' => $productWithBundlesDropdownApi, 'productEditOptions' => $productsWithBundlesDefaultOptions]) ?>
                                                                <?php } ?>
                                                            </tbody>
                                                            <tfoot>
                                                                <?php if (!IS_MOBILE && !IS_TABLET) { ?> 
                                                                    <tr class="d-lg-table-row d-none">
                                                                        <td colspan="3" valign="top">
                                                                            <div class="l-btn-group-box">
                                                                                <div class="l-btn-group l-btn-group--separated" role="group">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="materials">
                                                                                        <span class="ui-btn-inner-content">
                                                                                            <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                            <span class="ui-btn-inner-text u-text-color-black"><?= __t('Add') ?></span>
                                                                                        </span>
                                                                                    </button>

                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </td>
                                                                        <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-total-text-subform="materials">
                                                                            <?= format_price_simple((float)($data['materials_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                                <!-- Mobile row -->
                                                                <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                                    <tr class="ui-table-box-row d-lg-none">
                                                                        <td valign="top" class="border-bottom">
                                                                            <div class="d-flex justify-content-between full-height">
                                                                                <div class="btn-group gap-1">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-text-color-success u-text-hover-color-success u-bg-color-secondary" data-app-form-subform-add-btn="materials">
                                                                                        <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                                    </button>
                                                                                </div>
                                                                                <div class="d-lg-flex d-none align-items-center px-10 border-start subform-cell-text text-dark-3 bg-light">
                                                                                    <?= __t("Total") ?>
                                                                                </div>
                                                                            </div>
                                                                        </td>

                                                                        <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-total-text-subform="materials">
                                                                            <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                                    <?= __t("Total") ?>
                                                                                </div>
                                                                            <?= format_price_simple((float)($data['materials_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                            </tfoot>
                                                        </table>
                                                        <template data-app-form-subform-row-template="materials">
                                                            <?= $this->includeSection('manufacturing/manufacturing_order/materials_row_template', ['productWithBundlesDropdownApi' => $productWithBundlesDropdownApi, 'productEditOptions' => $productsWithBundlesDefaultOptions]) ?>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                                    <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>




                                    <div class="l-show-card-section-title-box">
                                        <div class="l-flex-row">
                                            <div class="l-flex-col-12">
                                                <div class="accordion-collapse">
                                                    <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='expenses']" aria-expanded="<?=$isEditing?($form->getFieldSets()['expenses']->getData()?? [] ? 'true':'false ') :"false"?>" aria-controls="expenses">
                                                        <div class="l-flex-row l-flex-row--spacing-4 l-flex--nowrap">
                                                            <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                                <span class="accordion-collapse-title">
                                                                    <i class="mdi mdi-cash-multiple"></i>
                                                                    <span><?= __t('Expenses') ?> (<span data-header-number-subform="expenses"><?= isset($data['expenses']) ? count($data['expenses']) : 0 ?></span>)</span>
                                                                </span>
                                                            </span>
                                                            <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                                <div aria-expanded="false" style="margin-top: 5px;">
                                                                    <div class="l-flex-row ">
                                                                        <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </div>
                                                                        <div class="l-flex-col-12 l-flex-col-lg-2" data-total-text-subform="expenses">
                                                                            <?= format_price_simple((float)($data['expenses_total_cost'] ?? 0.00)) ?>
                                                                        </div>
                                                                        <input data-total-input-subform="expenses" type="hidden" name="expenses_total_cost" value="<?= $data['expenses_total_cost'] ?? "0.00" ?>" />
                                                                    </div>
                                                                </div>
                                                            </span>

                                                            <span class="l-flex-col-1 text-end px-0">
                                                                <span class="accordion-collapse-arrow">
                                                                    <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                                    <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                                </span>
                                                            </span>
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="collapse <?=$isEditing?($form->getFieldSets()['expenses']->getData()?? [] ? 'show':'') :""?>" data-accordion-collapse='expenses'>
                                            <div class="l-flex-row">
                                                <div class="l-flex-col-lg-12">
                                                    <div class="l-table-box-wrapper">
                                                        <table class="l-table-box  subform-table " data-app-form-subform="expenses" data-app-form-subform-options='{ "sortable": false }'>
                                                            <thead>
                                                                <tr class="ui-table-box-row">
                                                                    <th width="2000"><?= __t('Account') ?></th>
                                                                    <th width="500"><?= __t("Estimated Amount") ?></th>
                                                                    <th width="700"><?= __t('Description') ?></th>
                                                                    <th width="500"><?= __t("Production Operation") ?></th>
                                                                    <th width="600"><?= __t("Subtotal") ?></th>
                                                                    <th width="50"></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php if(isset($data['expenses'])){ foreach($data['expenses'] as $key => $expense) { ?>
                                                                    <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                        <?php if($pageMode == 'edit' && !empty($expense['id'])){ ?>
                                                                            <input value="<?= $expense['id'] ?>" type="hidden" name="expenses[<?= $key ?>][id]"/>
                                                                        <?php } ?>
                                                                        <?php if(!empty($expense['bom_expenses_id'])){ ?>
                                                                            <input value="<?= $expense['bom_expenses_id'] ?>" type="hidden" name="expenses[<?= $key ?>][bom_expenses_id]"/>
                                                                        <?php } ?>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <select data-field-name="journal_account_id" name="expenses[<?= $key ?>][journal_account_id]" id="expenses[<?= $key ?>][journal_account_id]" class="account-select ui-input" data-app-form-validate="required" data-app-form-select-options='<?= $journalAccountsDropdownMetaData ?>'>
                                                                                    <?php if(!empty($expense['journal_account_id'])){ ?>
                                                                                        <option value="<?= $expense['journal_account_id'] ?>" selected><?= !empty($journalAccountsMapById[$expense['journal_account_id']]) ? $journalAccountsMapById[$expense['journal_account_id']]?->text : '' ?></option>
                                                                                    <?php } ?>
                                                                                </select>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$key.journal_account_id"]) ?>
                                                                            </div>
                                                                        </td>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <input data-bom-input-name="amount" value="<?= $expense['amount'] ?>" data-subtotal-factor="true" type="text" name="expenses[<?= $key ?>][amount]" class="ui-input" data-app-form-validate="required"  <?= !empty($expense['bom_expenses_id']) && !empty($bomMap['expenses'][$expense['bom_expenses_id']]) ? "data-bom-value=\"{$bomMap['expenses'][$expense['bom_expenses_id']]['amount']}\" data-bom-input-name=\"amount\" data-formula=\"{$bomMap['expenses'][$expense['bom_expenses_id']]['formula']}\" data-cost-type=\"{$bomMap['expenses'][$expense['bom_expenses_id']]['cost_type']}\"" : '' ?>/>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$key.amount"]) ?>
                                                                            </div>
                                                                        </td>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <textarea rows="1" name="expenses[<?= $key ?>][description]" class="ui-input"><?= $expense['description'] ?></textarea>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$key.description"]) ?>
                                                                            </div>
                                                                        </td>


                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <?= $this->includeSection('manufacturing/manufacturing_order/dynamic_dropdown', [
                                                                                    "dataRelationChild" => "production_routing_id",
                                                                                    "optional" => true,
                                                                                    'rowKey' => $key,
                                                                                    'record' => $expense,
                                                                                    'subform' => "expenses",
                                                                                    'fieldName' => "sub_production_routing_id",
                                                                                    'fieldLabel' => __t("Production Operation"),
                                                                                    'error_input_name' => "expenses.$key.sub_production_routing_id",
                                                                                ]) ?>
                                                                            </div>
                                                                        </td>
                                                                        <td class="subform-cell-text border-start">
                                                                            <span data-subtotal-text-subform="expenses"><?= format_price_simple((float)($expense['sub_total_cost'] ?? 0.00)) ?></span>
                                                                            <input data-field-name="sub_total_cost" data-subtotal-input-subform="expenses" type="hidden" name="expenses[<?= $key ?>][sub_total_cost]" value="<?= $expense['sub_total_cost'] ?>" />
                                                                        </td>
                                                                        <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                            <button data-row-remove-btn-subform="expenses" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
                                                                                <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                <?php } } ?>
                                                            </tbody>
                                                            <tfoot>
                                                                <?php if (!IS_MOBILE && !IS_TABLET) { ?> 
                                                                    <tr class="d-lg-table-row d-none">
                                                                        <td colspan="3" valign="top">
                                                                            <div class="l-btn-group-box">
                                                                                <div class="l-btn-group l-btn-group--separated" role="group">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="expenses">
                                                                                        <span class="ui-btn-inner-content">
                                                                                            <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                            <span class="ui-btn-inner-text u-text-color-black"><?= __t('Add') ?></span>
                                                                                        </span>
                                                                                    </button>

                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </td>
                                                                        <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-total-text-subform="expenses">
                                                                            <?= format_price_simple((float)($data['expenses_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                                <!-- Mobile row -->
                                                                <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                                    <tr class="ui-table-box-row d-lg-none">
                                                                        <td valign="top" class="border-bottom">
                                                                            <div class="d-flex justify-content-between full-height">
                                                                                <div class="btn-group gap-1">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="expenses">
                                                                                        <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                                    </button>
                                                                                </div>
                                                                                <div class="d-lg-flex d-none align-items-center px-10 border-start subform-cell-text text-dark-3 bg-light">
                                                                                    <?= __t("Total") ?>
                                                                                </div>
                                                                            </div>
                                                                        </td>

                                                                        <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-total-text-subform="expenses">
                                                                            <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                                <?= __t("Total") ?>
                                                                            </div>
                                                                            <?= format_price_simple((float)($data['expenses_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                            </tfoot>
                                                        </table>
                                                        <template data-app-form-subform-row-template="expenses">
                                                            <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <select data-field-name="journal_account_id" name="expenses[__index__][journal_account_id]" id="expenses[__index__][journal_account_id]" class="account-select ui-input" data-app-form-validate="required" data-app-form-select-options='<?= $journalAccountsDropdownMetaData ?>'>
                                                                        </select>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input data-app-form-number="true" data-bom-input-name="amount" data-field-name="amount" value="" data-subtotal-factor="true" type="text" name="expenses[__index__][amount]" class="ui-input" data-app-form-validate="required" />
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <textarea data-field-name="description" rows="1" name="expenses[__index__][description]" class="ui-input" ></textarea>
                                                                    </div>
                                                                </td>


                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <?= $this->includeSection('manufacturing/manufacturing_order/subform_dynamic_dropdown', [
                                                                            "dataAttributes" => [
                                                                                "readonly" => "readonly",
                                                                            ],                                                                            
                                                                            "dataRelationChild" => "production_routing_id",
                                                                            "optional" => true,
                                                                            'dataFieldName' => 'sub_production_routing_id', 'url' => "/v2/api/entity/sub_production_routing/list-photo/2?mode=listing&fields%5B0%5D=id&fields%5B1%5D=name", 'fieldName' => "expenses[__index__][sub_production_routing_id]"]) ?>
                                                                    </div>
                                                                </td>
                                                                <td class="subform-cell-text border-start">
                                                                    <span data-subtotal-text-subform="expenses">0</span>
                                                                    <input data-field-name="sub_total_cost" data-subtotal-input-subform="expenses" type="hidden" name="expenses[__index__][sub_total_cost]" value="0" />
                                                                </td>
                                                                <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                    <button data-row-remove-btn-subform="expenses" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
                                                                        <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>

                                    <div class="l-show-card-section-title-box">
                                        <div class="l-flex-row">
                                            <div class="l-flex-col-12">
                                                <div class="accordion-collapse">
                                                    <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='operations']" aria-expanded="<?=$isEditing?($form->getFieldSets()['operations']->getData()?? [] ? 'true':'false ') :"false"?>" aria-controls="operations">
                                                        <div class="l-flex-row l-flex-row--spacing-4 l-flex--nowrap">
                                                            <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                                <span class="accordion-collapse-title">
                                                                    <i class="mdi mdi-cog-outline"></i>
                                                                    <span><?= __t('Manufacturing Operations') ?> (<span data-header-number-subform="operations"><?= isset($data['operations']) ? count($data['operations']) : 0 ?></span>)</span>
                                                                </span>
                                                            </span>
                                                            <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                                <div aria-expanded="false" style="margin-top: 5px;">
                                                                    <div class="l-flex-row ">
                                                                        <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </div>
                                                                        <div class="l-flex-col-12 l-flex-col-lg-2" data-total-text-subform="operations">
                                                                            <?= format_price_simple((float)($data['operations_total_cost'] ?? 0.00)) ?>
                                                                        </div>
                                                                        <input data-total-input-subform="operations" type="hidden" name="operations_total_cost" value="<?= $data['operations_total_cost'] ?? "0.00" ?>" />
                                                                    </div>
                                                                </div>
                                                            </span>

                                                            <span class="l-flex-col-1 text-end px-0">
                                                                <span class="accordion-collapse-arrow">
                                                                    <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                                    <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                                </span>
                                                            </span>
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="collapse <?=$isEditing?($form->getFieldSets()['operations']->getData()?? [] ? 'show':'') :""?>"  data-accordion-collapse='operations'>
                                            <div class="l-flex-row">
                                                <div class="l-flex-col-lg-12">
                                                    <div class="l-table-box-wrapper">
                                                        <table class="l-table-box  subform-table " data-app-form-subform="operations" data-app-form-subform-options='{ "sortable": false }'>
                                                            <thead>
                                                                <tr class="ui-table-box-row">
                                                                    <th width="2000"><?= __t('Workstation') ?></th>
                                                                    <th width="500"><?= __t('Operating Time') ?></th>
                                                                    <th width="700"><?= __t("Description") ?></th>
                                                                    <th width="500"><?= __t("Production Operation") ?></th>
                                                                    <th width="600"><?= __t("Subtotal") ?></th>
                                                                    <th width="50"></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php if(isset($data['operations'])){ foreach($data['operations'] as $key => $operation) { ?>
                                                                    <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                        <?php if($pageMode == 'edit' && !empty($operation['id'])){ ?>
                                                                            <input value="<?= $operation['id'] ?>" type="hidden" name="operations[<?= $key ?>][id]"/>
                                                                        <?php } ?>
                                                                        <?php if(!empty($operation['bom_operations_id'])){ ?>
                                                                            <input value="<?= $operation['bom_operations_id'] ?>" type="hidden" name="operations[<?= $key ?>][bom_operations_id]"/>
                                                                        <?php } ?>
                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <select data-field-name="workstation_id" name="operations[<?= $key ?>][workstation_id]" class="ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="ajax-simple-3" data-app-form-select-options='{"ajax": { "url": "<?= $workstationApi ?>" } }' data-app-form-validate="required">
                                                                                    <option selected value="<?= $operation['workstation_id'] ?>" data-data='<?= !empty($workstationsMapById[$operation['workstation_id']]) ? ($workstationsMapById[$operation['workstation_id']]['htmlJson'] ?? '') : '' ?>'></option>
                                                                                    <option value=""></option>
                                                                                    <?php foreach ($defaultWorkstationOptions as $workstation) { ?>
                                                                                        <option value="<?= $workstation["id"] ?>" data-data='<?= $workstation['htmlJson'] ?? '' ?>'></option>
                                                                                    <?php } ?>
                                                                                </select>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$key.workstation_id"]) ?>
                                                                                <input value="<?= !empty($workstationsMapById[$operation['workstation_id']]) ? $workstationsMapById[$operation['workstation_id']]['total_cost'] : 0 ?>" type="hidden" data-field-name="workstation_total_cost" data-subtotal-factor="true">
                                                                            </div>
                                                                        </td>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <input value="<?= $operation['operating_time'] ?>" data-subtotal-factor="true" type="text" name="operations[<?= $key ?>][operating_time]" class="ui-input" data-app-form-validate="required" <?= !empty($operation['bom_operations_id']) && !empty($bomMap['operations'][$operation['bom_operations_id']]) ? "data-bom-value=\"{$bomMap['operations'][$operation['bom_operations_id']]['operating_time']}\" data-bom-input-name=\"operating_time\" data-formula=\"{$bomMap['operations'][$operation['bom_operations_id']]['formula']}\" data-cost-type=\"{$bomMap['operations'][$operation['bom_operations_id']]['cost_type']}\"" : '' ?>/>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$key.operating_time"]) ?>
                                                                            </div>
                                                                        </td>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <textarea rows="1" name="operations[<?= $key ?>][description]" class="ui-input" ><?= $operation['description'] ?></textarea>
                                                                            </div>
                                                                        </td>


                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <?= $this->includeSection('manufacturing/manufacturing_order/dynamic_dropdown', [
                                                                                    "dataRelationChild" => "production_routing_id",
                                                                                    "optional" => true,
                                                                                    'rowKey' => $key,
                                                                                    'record' => $operation,
                                                                                    'subform' => "operations",
                                                                                    'fieldName' => "sub_production_routing_id",
                                                                                    'fieldLabel' => __t("Production Operation"),
                                                                                    'error_input_name' => "operations.$key.sub_production_routing_id",
                                                                                ]) ?>
                                                                            </div>
                                                                        </td>
                                                                        <td class="subform-cell-text border-start">
                                                                            <span data-subtotal-text-subform="operations"><?= format_price_simple((float)($operation['sub_total_cost'] ?? 0.00)) ?></span>
                                                                            <input data-field-name="sub_total_cost" data-subtotal-input-subform="operations" type="hidden" name="operations[<?= $key ?>][sub_total_cost]" value="<?= $operation['sub_total_cost'] ?>" />
                                                                        </td>
                                                                        <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                            <button data-row-remove-btn-subform="operations" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
                                                                                <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                <?php } } ?>
                                                            </tbody>
                                                            <tfoot>
                                                                <?php if (!IS_MOBILE && !IS_TABLET) { ?>
                                                                    <tr class="d-lg-table-row d-none">
                                                                        <td colspan="3" valign="top">
                                                                            <div class="l-btn-group-box">
                                                                                <div class="l-btn-group l-btn-group--separated" role="group">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="operations">
                                                                                        <span class="ui-btn-inner-content">
                                                                                            <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                            <span class="ui-btn-inner-text u-text-color-black"><?= __t('Add') ?></span>
                                                                                        </span>
                                                                                    </button>

                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </td>
                                                                        <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-total-text-subform="operations">
                                                                            <?= format_price_simple((float)($data['operations_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                                <!-- Mobile row -->
                                                                <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                                    <tr class="ui-table-box-row d-lg-none">
                                                                        <td valign="top" class="border-bottom">
                                                                            <div class="d-flex justify-content-between full-height">
                                                                                <div class="btn-group gap-1">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="operations">
                                                                                        <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                                    </button>
                                                                                </div>
                                                                                <div class="d-lg-flex d-none align-items-center px-10 border-start subform-cell-text text-dark-3 bg-light">
                                                                                    <?= __t("Total") ?>
                                                                                </div>
                                                                            </div>
                                                                        </td>

                                                                        <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-total-text-subform="operations">
                                                                            <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                                    <?= __t("Total") ?>
                                                                            </div>
                                                                            <?= format_price_simple((float)($data['operations_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                            </tfoot>
                                                        </table>
                                                        <template data-app-form-subform-row-template="operations">
                                                            <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <?= $this->includeSection('manufacturing/manufacturing_order/subform_dynamic_dropdown', ['defaultValueOptions' => $defaultWorkstationOptions, 'dataFieldName' => "workstation_id", 'url' => $workstationApi, 'fieldName' => "operations[__index__][workstation_id]"]) ?>
                                                                        <input type="hidden" data-field-name="workstation_total_cost" data-subtotal-factor="true">
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input data-bom-input-name="operating_time" data-field-name="operating_time" value="" data-subtotal-factor="true" type="text" name="operations[__index__][operating_time]" class="ui-input" data-app-form-validate="required" data-app-form-number="true"/>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <textarea data-field-name="description" rows="1" name="operations[__index__][description]" class="ui-input"></textarea>
                                                                    </div>
                                                                </td>


                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <?= $this->includeSection('manufacturing/manufacturing_order/subform_dynamic_dropdown', [
                                                                            "dataAttributes" => [
                                                                                "readonly" => "readonly",
                                                                            ],
                                                                            "dataRelationChild" => "production_routing_id",
                                                                            "optional" => true,
                                                                            'dataFieldName' => "sub_production_routing_id", 'url' => "/v2/api/entity/sub_production_routing/list-photo/2?mode=listing&fields%5B0%5D=id&fields%5B1%5D=name", 'fieldName' => "operations[__index__][sub_production_routing_id]"]) ?>
                                                                    </div>
                                                                </td>
                                                                <td class="subform-cell-text border-start">
                                                                    <span data-subtotal-text-subform="operations">0</span>
                                                                    <input data-field-name="sub_total_cost" data-subtotal-input-subform="operations" type="hidden" name="operations[__index__][sub_total_cost]" value="0" />
                                                                </td>
                                                                <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                    <button data-row-remove-btn-subform="operations" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
                                                                        <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>

                                    <div class="l-show-card-section-title-box">
                                        <div class="l-flex-row">
                                            <div class="l-flex-col-12">
                                                <div class="accordion-collapse">
                                                    <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='scraps']" aria-expanded="<?=$isEditing?($form->getFieldSets()['scraps']->getData()?? [] ? 'true':'false ') :"false"?>" aria-controls="scraps">
                                                        <div class="l-flex-row l-flex-row--spacing-4 l-flex--nowrap">
                                                            <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                                <span class="accordion-collapse-title">
                                                                    <i class="mdi mdi-basket-fill"></i>
                                                                    <span><?= __t("Scrap Items") ?> (<span data-header-number-subform="scraps"><?= isset($data['scraps']) ? count($data['scraps']) : 0 ?></span>)</span>
                                                                </span>
                                                            </span>
                                                            <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                                <div aria-expanded="false" style="margin-top: 5px;">
                                                                    <div class="l-flex-row ">
                                                                        <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </div>
                                                                        <div class="l-flex-col-12 l-flex-col-lg-2" data-total-text-subform="scraps">
                                                                            <?= format_price_simple((float)($data['scraps_total_cost'] ?? 0.00)) ?>
                                                                        </div>
                                                                        <input data-total-input-subform="scraps" data-total-input-operation="subtract" type="hidden" name="scraps_total_cost" value="<?= $data['scraps_total_cost'] ?? "0.00" ?>" />
                                                                    </div>
                                                                </div>
                                                            </span>
                                                            <span class="l-flex-col-1 text-end px-0">
                                                                <span class="accordion-collapse-arrow">
                                                                    <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                                    <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                                </span>
                                                            </span>
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="collapse <?=$isEditing?($form->getFieldSets()['scraps']->getData()?? [] ? 'show':'') :""?>"  data-accordion-collapse='scraps'>
                                            <div class="l-flex-row">
                                                <div class="l-flex-col-lg-12">
                                                    <div class="l-table-box-wrapper">
                                                        <table class="l-table-box  subform-table " data-app-form-subform="scraps" data-app-form-subform-options='{ "sortable": false }'>
                                                            <thead>
                                                                <tr class="ui-table-box-row">
                                                                    <th width="1000"><?= __t('Product') ?></th>
                                                                    <th width="1000"><?= __t("QTY") ?></th>
                                                                    <th width="500"><?= __t("Price") ?></th>
                                                                    <th width="1000"><?= __t("Production Operation") ?></th>
                                                                    <th width="550"><?= __t("Subtotal") ?></th>
                                                                    <th width="50"></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php if(isset($data['scraps'])){ foreach($data['scraps'] as $key => $scrap) { ?>
                                                                    <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                        <?php if($pageMode == 'edit' && !empty($scrap['id'])){ ?>
                                                                            <input value="<?= $scrap['id'] ?>" type="hidden" name="scraps[<?= $key ?>][id]"/>
                                                                        <?php } ?>
                                                                        <?php if(!empty($scrap['bom_scraps_id'])){ ?>
                                                                            <input value="<?= $scrap['bom_scraps_id'] ?>" type="hidden" name="scraps[<?= $key ?>][bom_scraps_id]"/>
                                                                        <?php } ?>
                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <select data-field-name="product_id" id="scraps[<?= $key ?>][product_id]" name="scraps[<?= $key ?>][product_id]" data-quantity-fields-name-prefix="scraps[<?= $key ?>]" data-filter-select="scraps[<?= $key ?>][product_id]" class="ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="ajax-simple-3" data-app-form-select-options='{"ajax": { "url": "<?= $productWithBundlesDropdownApi ?>" } }' data-app-form-validate="required">
                                                                                    <option value=""></option>
                                                                                    <option value="<?= $scrap['product_id'] ?>" selected>
                                                                                        <?= !empty($productsMapById[$scrap['product_id']]) ? $productsMapById[$scrap['product_id']]['text'] : '' ?>
                                                                                    </option>
                                                                                    <?php foreach ($productsWithBundlesDefaultOptions as $product) { ?>
                                                                                        <option value="<?= $product["id"] ?>" data-data='<?= $product['htmlJson'] ?>'></option>
                                                                                    <?php } ?>
                                                                                </select>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$key.product_id"]) ?>
                                                                            </div>
                                                                        </td>
                                                                      
                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container d-flex-row">
                                                                                <div class="filter-group-input" data-qty-type-select="scraps[<?= $key ?>][product_id]">
                                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                                        <?php 
                                                                                            $quantityValue = null;
                                                                                            $quantityValue = $scrap['quantity'];
                                                                                            if(!empty($scrap['factor']) && !empty($productsMapById[$scrap['product_id']]['units']) && empty($errors)){
                                                                                                $quantityValue = $scrap['quantity'] / $scrap['factor'];
                                                                                            }
                                                                                        ?>
                                                                                        <input  data-formula="{{$manufacturing_order.quantity}} * {{$bom_scrap.quantity}}" data-field-name="quantity" data-subtotal-factor="true" type="text" name="scraps[<?= $key ?>][quantity]" class="l-input ui-input" value="<?= $quantityValue ?>" data-app-form-validate="required" data-app-form-number="true" <?= !empty($scrap['bom_scraps_id']) && !empty($bomMap['scraps'][$scrap['bom_scraps_id']]) ? "data-bom-value=\"{$bomMap['scraps'][$scrap['bom_scraps_id']]['quantity']}\"" : '' ?> />
                                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$key.quantity"]) ?>                                                                                        
                                                                                    </div>
                                                                                    <?php if(!empty($scrap['factor']) && !empty($productsMapById[$scrap['product_id']]['units']) ){ ?>
                                                                                        <?php $selectedUnitData = null; $scrap['unit_factor_id'] = !empty($scrap['unit_factor_id']) ? $scrap['unit_factor_id'] : 0; ?>
                                                                                        <div class="ui-table-box-body-item-editable-input-container unit-factor-select">
                                                                                            <select data-is-unit-factor-select="true" name="scraps[<?= $key ?>][unit_factor_id]" class="l-input ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="currency" data-app-form-validate="required">
                                                                                                <?php foreach($productsMapById[$scrap['product_id']]['units'] as $unit) { ?>
                                                                                                    <?php if($unit['id'] == $scrap['unit_factor_id']){ ?>
                                                                                                        <?php $selectedUnitData = $unit; ?>
                                                                                                        <option value="<?= $unit['id'] ?>" selected>
                                                                                                            <?= $unit['small_name'] ?> 
                                                                                                        </option>
                                                                                                    <?php } else { ?>
                                                                                                        <option value="<?= $unit['id'] ?>">
                                                                                                            <?= $unit['small_name'] ?> 
                                                                                                        </option>
                                                                                                    <?php } ?>
                                                                                                <?php } ?>

                                                                                            </select>
                                                                                            <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$key.unit_factor_id"]) ?>
                                                                                            <?php if(!empty($selectedUnitData)){ ?>
                                                                                                <input data-field-name="factor" value="<?= $selectedUnitData['factor'] ?>" type="hidden" name="scraps[<?= $key ?>][factor]" />
                                                                                                <input data-field-name="unit_name" value="<?= $selectedUnitData['factor_name'] ?>" type="hidden" name="scraps[<?= $key ?>][unit_name]" />
                                                                                                <input data-field-name="unit_small_name" value="<?= $selectedUnitData['small_name'] ?>" type="hidden" name="scraps[<?= $key ?>][unit_small_name]" />
                                                                                            <?php } ?>
                                                                                            <?php if(!empty($scrap['factor']) && empty($productsMapById[$scrap['product_id']]['units'])){ ?>
                                                                                                <input value="" type="hidden" name="scraps[<?= $key ?>][unit_factor_id]" />
                                                                                                <input value="" type="hidden" name="scraps[<?= $key ?>][factor]" />
                                                                                                <input value="" type="hidden" name="scraps[<?= $key ?>][unit_name]" />
                                                                                                <input value="" type="hidden" name="scraps[<?= $key ?>][unit_small_name]" />
                                                                                            <?php } ?>
                                                                                        </div>
                                                                                        <script defer>
                                                                                            setTimeout(function () {                                                                                                
                                                                                                $(`[name="scraps[<?= $key ?>][product_id]"]`).trigger("productUnitFactorFieldsAdded");
                                                                                            },2100)
                                                                                        </script>
                                                                                    <?php } ?>
                                                                                </div>
                                                                            </div>
                                                                        </td>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <input value="<?= $scrap['price'] ?>" data-subtotal-factor="true" type="text" name="scraps[<?= $key ?>][price]" class="ui-input" data-app-form-validate="required" />
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$key.price"]) ?>
                                                                            </div>
                                                                        </td>

                                                                        <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                                <?= $this->includeSection('manufacturing/manufacturing_order/dynamic_dropdown', [
                                                                                    "dataRelationChild" => "production_routing_id",
                                                                                    "optional" => true,
                                                                                    'rowKey' => $key,
                                                                                    'record' => $scrap,
                                                                                    'subform' => "scraps",
                                                                                    'fieldName' => "sub_production_routing_id",
                                                                                    'fieldLabel' => __t("Production Operation"),
                                                                                    'error_input_name' => "scraps.$key.sub_production_routing_id",
                                                                                ]) ?>
                                                                            </div>
                                                                        </td>
                                                                        <td class="subform-cell-text border-start">
                                                                            <span data-subtotal-text-subform="scraps"><?= format_price_simple((float)($scrap['sub_total_cost'] ?? 0.00)) ?></span>
                                                                            <input data-field-name="sub_total_cost" data-subtotal-input-subform="scraps" type="hidden" name="scraps[<?= $key ?>][sub_total_cost]" value="<?= $scrap['sub_total_cost'] ?>" />
                                                                        </td>
                                                                        <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                            <button data-row-remove-btn-subform="scraps" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
                                                                                <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                <?php }} ?>
                                                            </tbody>
                                                            <tfoot>
                                                                <?php if (!IS_MOBILE && !IS_TABLET) { ?>
                                                                    <tr class="d-lg-table-row d-none">
                                                                        <td colspan="3" valign="top">
                                                                            <div class="l-btn-group-box">
                                                                                <div class="l-btn-group l-btn-group--separated" role="group">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="scraps">
                                                                                        <span class="ui-btn-inner-content">
                                                                                            <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                            <span class="ui-btn-inner-text u-text-color-black"><?= __t('Add') ?></span>
                                                                                        </span>
                                                                                    </button>

                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                            <?= __t("Total") ?>
                                                                        </td>
                                                                        <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-total-text-subform="scraps">
                                                                            <?= format_price_simple((float)($data['scraps_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                                <!-- Mobile row -->
                                                                <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                                    <tr class="ui-table-box-row d-lg-none">
                                                                        <td valign="top" class="border-bottom">
                                                                            <div class="d-flex justify-content-between full-height">
                                                                                <div class="btn-group gap-1">
                                                                                    <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="scraps">
                                                                                        <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                                    </button>
                                                                                </div>
                                                                                <div class="d-lg-flex d-none align-items-center px-10 border-start subform-cell-text text-dark-3 bg-light">
                                                                                    <?= __t("Total") ?>
                                                                                </div>
                                                                            </div>
                                                                        </td>

                                                                        <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-total-text-subform="scraps">
                                                                            <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                                    <?= __t("Total") ?>
                                                                            </div>
                                                                            <?= format_price_simple((float)($data['scraps_total_cost'] ?? 0.00)) ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php } ?>
                                                            </tfoot>
                                                        </table>
                                                        <template data-app-form-subform-row-template="scraps">
                                                            <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <select data-field-name="product_id" name="scraps[__index__][product_id]" data-quantity-fields-name-prefix="scraps[__index__]" data-filter-select="scraps[__index__][product_id]" class="ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="ajax-simple-3" data-app-form-select-options='{"ajax": { "url": "<?= $productWithBundlesDropdownApi ?>" } }' data-app-form-validate="required">
                                                                            <option value=""></option>
                                                                            <?php foreach ($productsWithBundlesDefaultOptions as $product) { ?>
                                                                                <option value="<?= $product["id"] ?>" data-data='<?= $product['htmlJson'] ?>'></option>
                                                                            <?php } ?>
                                                                        </select>
                                                                    </div>
                                                                </td>
                                                              
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container d-flex-row">
                                                                        <div class="filter-group-input" data-qty-type-select="scraps[__index__][product_id]">
                                                                            <div class="ui-table-box-body-item-editable-input-container" >
                                                                                <input  data-formula="{{$manufacturing_order.quantity}} * {{$bom_scrap.quantity}}" data-field-name="quantity" data-subtotal-factor="true" type="text" name="scraps[__index__][quantity]" class="l-input ui-input" value="" data-app-form-validate="required" data-app-form-number="true" />
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input data-app-form-number="true" data-field-name="price" data-subtotal-factor="true" type="text" name="scraps[__index__][price]" class="ui-input" data-app-form-validate="required" />
                                                                    </div>
                                                                </td>
                                                                
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <?= $this->includeSection('manufacturing/manufacturing_order/subform_dynamic_dropdown', [
                                                                            "dataAttributes" => [
                                                                                "readonly" => "readonly",
                                                                            ],                                                                            
                                                                            "dataRelationChild" => "production_routing_id",
                                                                            "optional" => true,
                                                                            'dataFieldName' => "sub_production_routing_id", 'url' => "/v2/api/entity/sub_production_routing/list-photo/2?mode=listing&fields%5B0%5D=id&fields%5B1%5D=name", 'fieldName' => "scraps[__index__][sub_production_routing_id]"]) ?>
                                                                    </div>
                                                                </td>
                                                                <td class="subform-cell-text">
                                                                    <span data-subtotal-text-subform="scraps">0</span>
                                                                    <input data-field-name="sub_total_cost" data-subtotal-input-subform="scraps" type="hidden" name="scraps[__index__][sub_total_cost]" value="0" />
                                                                </td>
                                                                <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                    <button data-row-remove-btn-subform="scraps" class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="true">
                                                                        <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php
                                        $row = new \Izam\Forms\View\Helper\Form\FormRow();
                                        $fieldsets = $form->getFieldSets();
                                        $customFieldSet = $fieldsets['manufacturing_order_custom_data'] ?? null;
                                        if(!empty($customFieldSet)){
                                            $customFieldSet = $customFieldSet->getIterator();
                                    ?>
                                            <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>
                                            <div class="ui-card-header">
                                                <h3 class="ui-card-header-title"><?= sprintf(__t("%s More Information"), __t("Manufacturing Order")) ?></h3>
                                            </div>
                                            <div class="ui-card-content-box">
                                            <div class="l-flex-row l-flex-row--spacing-8">
                                                <?php
                                                if (isset($extraData['customForm'])){
                                                    $form = $extraData['customForm'];
                                                    $helper = new \Izam\View\Form\Helper\Deprecated\FormCollection();
                                                    $form->prepare();
                                                    echo $helper->render($form);
                                                }
                                               ?>
                                            </div>
                                            </div>
                                    <?php } ?>

                                    <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>
                                    <div class="l-show-card-section-title-box">
                                        <div class="l-flex-row">
                                            <div class="l-flex-col-lg-6"></div>
                                            <div class="l-flex-col-lg-6">
                                                <div class="total-cell">
                                                    <span><?= __t("Total Cost") ?>:</span>
                                                    <span><span data-total-text-for-all="true"><?= format_price_simple((float)($data['total_cost'] ?? 0.00)) ?? "0.00" ?></span> <?= get_currency_formatted() ?></span>
                                                    <input type="hidden" name="total_cost" value="<?= $data['total_cost'] ?? "0.00" ?>" data-total-input-for-all="true" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

<?php $this->section('page-after-js') ?>

<script>
    var jQuery = $;
</script>

<script src="<?=getCdnAssetsUrl()?>js/forms/plugin_select2.js"></script>
<script src="/v2/js/account-select.js"></script>
<script type="text/javascript" src="/js/functions.js"></script>

    <script>

        var convertBomId = <?= $_GET['bom'] ?? 'null' ?>;
        var pageJustLoaded = true
        var selectizeInputChangeTimes = {}
        <?php $productsMapById = array_map(function($productOption){unset($productOption['htmlJson']); return $productOption;},$productsMapById); ?>
        var productsMapById = <?= !empty($productsMapById) ? json_encode($productsMapById) : 'null' ?>;
        var errorsExist = <?= !empty($errors) ? 'true' : 'false' ?>;
        var formJqueryElement = $('form[data-app-form="manufacturing_order"]');

        function changeSubtotal(jqueryElement){
            let parentRow = jqueryElement.parents('[data-app-form-subform-row]')
            let subtotalValue = 0.0
            let factors = [1]
            parentRow.find(`[data-subtotal-factor]`).each(function(index,element){
                let value = $(element).val()
                if(value && !isNaN(value)){
                    value = parseFloat(value)
                    factors.push(value)
                }else {
                    factors.push(0)
                }
            })
            subtotalValue = factors.reduce((prev, current) => prev * current)
            parentRow.find(`[data-subtotal-input-subform]`).val(subtotalValue).trigger("change")
        }

        function changeSubformTotalCost(subformName){
            let totalNumber =  0.0
            $(`[data-subtotal-input-subform='${subformName}']`).each(function(index,element){
                let value = $(element).val();
                totalNumber = totalNumber + parseFloat(value)
            })
            $(`[data-total-input-subform='${subformName}']`).val(totalNumber).trigger("change")
        }

        function changeTotalCost(){
            let totalNumber =  0.0
            $(`[data-total-input-subform]`).each(function(){
                let value = $(this).val()
                let operation = $(this).attr('data-total-input-operation')
                if(operation == 'subtract'){
                    totalNumber = totalNumber - parseFloat(value)
                } else if (operation == 'skip') {

                } else {
                    totalNumber = totalNumber + parseFloat(value)
                }
            })
            $(`[data-total-text-for-all]`).text(format_price(totalNumber, "<?= $currencyCode ?>"))
            $(`[data-total-input-for-all]`).val(totalNumber)
        }

        function changeSubformHeaderNumber(subformName){
            $(`[data-header-number-subform="${subformName}"]`).text($(`[data-app-form-subform="${subformName}"] tbody [data-app-form-subform-row="true"]`).length);
            // remove last item close tab
            if($("[data-app-form-subform=" + subformName + "] tbody tr").length == 0) {
                $("[data-app-form-subform=" + subformName + "]").parents('.l-show-card-section-title-box').find('.collapse').collapse('hide');
            }
        }

        function changeWorkstationTotalCostHiddenValue(workstationJqueryElement){
            let parentRow = workstationJqueryElement.parents('[data-app-form-subform-row]')
            let workstationHiddenTotalCost = parentRow.find(`[data-field-name="workstation_total_cost"]`)
            setTimeout(function(){
                let workstationData = workstationJqueryElement.get(0).selectize.options[workstationJqueryElement.val()]
                workstationHiddenTotalCost.val(workstationData["total_cost"])
                workstationHiddenTotalCost.trigger("change")
            },1)
        }

        function setNullIfDuplicateProduct(productJqueryElement){
            let newProductId = productJqueryElement.get(0).selectize.getValue()
            let parentSubform = productJqueryElement.parents('[data-app-form-subform]')
            let rowsProductFields = parentSubform.find('[data-field-name="product_id"]')
            let subformName = parentSubform.attr('data-app-form-subform')
            let prevProductIds = {}

            rowsProductFields.each((index, element) => {
                let currentJqueryElement = $(element);
                let productId = currentJqueryElement.val()
                if(
                    productId
                    && currentJqueryElement.attr('name') != productJqueryElement.attr('name')
                ){
                    prevProductIds[productId] = true
                }
            });

            if(prevProductIds[newProductId]){
                productJqueryElement.get(0).selectize.setValue(null)
                return true
            }
            return false
        }

        function selectizeChangeIsRunTwice(jqueryElement){
            if(! selectizeInputChangeTimes[jqueryElement.attr('name')]){
                selectizeInputChangeTimes[jqueryElement.attr('name')] = 0
            }
            selectizeInputChangeTimes[jqueryElement.attr('name')]++
            if(selectizeInputChangeTimes[jqueryElement.attr('name')] % 2 == 0){
                return true
            }
            return false
        }

        function preventInvalidCharsForNumber(el){
            el.addEventListener('keydown', (e) => {
                var allowedChars = ["0","1","2","3","4","5","6","7","8","9","Backspace","Tab","ArrowRight","ArrowLeft","X","x","V","v","C","c","A","a","Control"];
                var invalidChars = ["+","E","e"];
                var numericalChars = new Set(allowedChars);
                if (!numericalChars.has(e.key) && invalidChars.includes(e.key)) {
                    e.preventDefault();
                }
            });
        }

        function addRowListeners(rowJqueryElement){

            $(`[data-app-form-number]`).each(function(i,el) {
                preventInvalidCharsForNumber(el)
            });


            rowJqueryElement.find('.account-select').initJournalAccountSelect();

            rowJqueryElement.find('[data-subtotal-input-subform]').on("change", function(){
                parentJqueryElement = $(this).parents('[data-app-form-subform-row]')
                parentJqueryElement.find(`[data-subtotal-text-subform]`).text(format_price($(this).val(), "<?= $currencyCode ?>"))
                changeSubformTotalCost($(this).attr('data-subtotal-input-subform'))
            })

            rowJqueryElement.find('[data-row-remove-btn-subform]').on("mouseup", function(){
                let subformName = $(this).attr('data-row-remove-btn-subform')
                setTimeout(function(){
                    changeSubformTotalCost(subformName)
                    changeSubformHeaderNumber(subformName)
                },1)
            })

            rowJqueryElement.find('[data-field-name="product_id"]').on('productUnitFactorFieldsAdded',function(event, parameters){
                let unitFactorJqueryElement = rowJqueryElement.find('[data-field-name="factor"]')
                // unitFactorJqueryElement.on("unitFactorFieldsChangedEvent", function(unitFactorEvent, unitFactorParameters){
                //     changeSubtotal($(this))
                // })
                rowJqueryElement.find('[data-is-unit-factor-select="true"]').on("change", function(){
                    if(selectizeChangeIsRunTwice($(this))){
                        return
                    }
                    setUnitFactorHiddenFields(rowJqueryElement, unitFactorJqueryElement)
                })
            })

            rowJqueryElement.find('[data-field-name="product_id"]').on("change", function(event){
                if(selectizeChangeIsRunTwice($(this))){
                    return
                }
                changeSubformProductListener(rowJqueryElement, $(this))
            })

            rowJqueryElement.find('[data-subtotal-factor="true"]').on("change", function(){
                changeSubtotal($(this))
            })


            rowJqueryElement.find(`[data-relation-child]`).each(function(index,element){
                element.selectize.settings.load = function(query, callback) {
                    setRelatedFieldFilter(query, callback, $(element))
                }
            })
        }

        function changeProductPrice(productJqueryElement){
            let parentRow = productJqueryElement.parents('[data-app-form-subform-row]')
            if(parentRow.length){
                parentRow.find(`[data-product-average-price]`).each(function(index, averagePrice){
                    var id = productJqueryElement.get(0).selectize.getValue();                
                    var data = productJqueryElement.get(0).selectize.options[id];
                    let priceJqueryElement = parentRow.find(`[data-field-name="price"]`)
                    if (data && data.average_price && data.average_price > 0) {
                        setInputValue($(averagePrice), data.average_price)
                        setInputValue(priceJqueryElement, data.average_price)
                    }else{
                        setInputValue($(averagePrice),0)
                        setInputValue(priceJqueryElement, 0)
                    }
                })
            }
        }

        function clearAllSubforms(){
            clearSubform("materials")
            clearSubform("expenses")
            clearSubform("operations")
            clearSubform("scraps")
            changeSubformHeaderNumber("materials")
            changeSubformHeaderNumber("expenses")
            changeSubformHeaderNumber("operations")
            changeSubformHeaderNumber("scraps")
            changeSubformTotalCost("materials")
            changeSubformTotalCost("expenses")
            changeSubformTotalCost("operations")
            changeSubformTotalCost("scraps")
        }

        function changeBom(jqueryElement, eventToTrigger = "bomChangedEvent", eventParameterObject = {}){            
            if(!jqueryElement.val()){
                clearAllSubforms()
                return
            }
            initPageLoader();
            $.ajax({
                url: `/v2/owner/entity/bom/${jqueryElement.val()}/show`,
                type: "GET",
                headers: {          
                    Accept: "application/json",
                },
                success: function(data){
                    let parsedData = JSON.parse(data)
                    setFieldValueByName("production_routing_id", parsedData["production_routing_id"])
                    if(pageMode == "convert"){
                        setFieldValueByName("product_id", parsedData["product_id"])
                    }

                    setBomValueByName("quantity", parsedData["quantity"])
                    if(pageMode == "edit"){

                    } else {
                        $('[name="quantity"]').unbind("change", changeMainQuantityListener)
                        let mainQuantity = parsedData["quantity"]
                        if(parsedData['factor']){
                            mainQuantity = mainQuantity/parsedData['factor']
                        }
                        setFieldValueByName("quantity", mainQuantity)
                        $('[name="quantity"]').on("change", changeMainQuantityListener)
                        if(parsedData['factor']){
                            let mainProductIntervalCount = 0
                            var mainProductInterval = setInterval(function() {
                                $(`[name="factor"]`).attr("data-bom-value", parsedData['factor'])
                                mainProductIntervalCount++
                                if ($("#order-information-fields").find('[data-is-unit-factor-select="true"]').length) {
                                    $("#order-information-fields").find('[data-is-unit-factor-select="true"]').get(0).selectize.setValue(parsedData['unit_factor_id'])
                                    clearInterval(mainProductInterval)
                                }
                                if(mainProductIntervalCount > 100){
                                    clearInterval(mainProductInterval)
                                    mainProductIntervalCount = 0 
                                }
                            }, 100);
                            setFieldValueByName("factor", parsedData['factor'])
                            setFieldValueByName("unit_name", parsedData['unit_name'])
                            setFieldValueByName("unit_small_name", parsedData['unit_small_name'])
                        }
                    }
                    // addUnitFactorFields()
                    // // "productUnitFactorFieldsAdded"
                    // // "unitFactorFieldsChangedEvent"
                    setFieldValueByName("journal_account_id", parsedData["journal_account_id"])
                    setFieldValueByName("materials_total_cost", parsedData["materials_total_cost"])
                    setFieldValueByName("expenses_total_cost", parsedData["expenses_total_cost"])
                    setFieldValueByName("operations_total_cost", parsedData["operations_total_cost"])
                    setFieldValueByName("scraps_total_cost", parsedData["scraps_total_cost"])
                    setFieldValueByName("total_cost", parsedData["total_cost"])

                    clearAllSubforms()
                    for (let index = 0; index < parsedData.materials.length; index++) {
                        const arrayValue = parsedData.materials[index];
                        addRow("materials", index, arrayValue)
                    }
                    setTimeout(function(){
                        changeSubformHeaderNumber("materials")
                    },1)

                    for (let index = 0; index < parsedData.expenses.length; index++) {
                        const arrayValue = parsedData.expenses[index];
                        addRow("expenses", index, arrayValue)
                    }
                    setTimeout(function(){
                        changeSubformHeaderNumber("expenses")
                    },1)

                    for (let index = 0; index < parsedData.operations.length; index++) {
                        const arrayValue = parsedData.operations[index];
                        addRow("operations", index, arrayValue)
                    }
                    setTimeout(function(){
                        changeSubformHeaderNumber("operations")
                    },1)

                    for (let index = 0; index < parsedData.scraps.length; index++) {
                        const arrayValue = parsedData.scraps[index];
                        addRow("scraps", index, arrayValue)
                    }
                    setTimeout(function(){
                        changeSubformHeaderNumber("scraps")
                    },1)

                    setTimeout(function(){
                        let parameters = {
                            bomData: parsedData
                        }
                        for (var attrname in eventParameterObject) { parameters[attrname] = eventParameterObject[attrname]; }

                        jqueryElement.trigger(eventToTrigger, parameters)
                    },1)
                    setTimeout(function(){
                        removePageLoader();
                    },1000)

                    var collapseButton = document.querySelectorAll(".accordion-collapse-btn");
                    collapseButton.forEach(element => {
                        setTimeout(function(){
                            if($(element).find('.accordion-collapse-title [data-header-number-subform]').text() >= 1) {
                                $(element).parents('.l-show-card-section-title-box').find('.collapse').collapse('show');
                            }
                        }, 1000);
                    });
                },
                error: function() {
                    removePageLoader();
                }
            })
        }

        function clearSubform(subformName){
            $(`[data-app-form-subform="${subformName}"] tbody [data-app-form-subform-row="true"]`).remove()
        }
        function addRow(subformName, rowIndex, rowData){
            let tableBodyJqueryElement = $(`[data-app-form-subform="${subformName}"] tbody`)
            let template = document.querySelector(`[data-app-form-subform-row-template="${subformName}"]`).innerHTML;
            let dataAppSubformElement = $(`[data-app-form-subform="${subformName}"]`)
            template =  template.replace(/__index__/g, dataAppSubformElement.data('subform').lastIndex);

            let addedRow = $(template);
            let uiSelect = new window.IzLayout.UiSelect()
            uiSelect.initAllInNode(addedRow);
            addedRow.append(`<input type="hidden" name="${subformName}[${dataAppSubformElement.data('subform').lastIndex}][bom_${subformName}_id]" value="${rowData['id']}">`)
            addedRow.appendTo(tableBodyJqueryElement)
            dataAppSubformElement.data('subform').lastIndex++
            addRowListeners(addedRow)
            addedRow.find(`[name]`).each(function(index,element){
                let fieldName = $(element).attr('name')
                let dataFieldName = $(element).attr("data-field-name")
                let value = dataFieldName ? rowData[dataFieldName] : rowData[fieldName]

                if(!value){
                    return
                }
                const valuesMapToSkip = {
                    "quantity": "quantity",
                    "unit_factor_id": "unit_factor_id",
                    "factor": "factor",
                    "unit_name": "unit_name",
                    "unit_small_name": "unit_small_name",
                    "sub_total_cost": "sub_total_cost",
                }
                if(valuesMapToSkip[dataFieldName]){
                    return
                }

                if(dataFieldName == "product_id"){
                    if(!rowData['factor']){
                        addedRow.find('[data-field-name="quantity"]').attr("data-bom-value", rowData['quantity'])
                        setFieldValue(addedRow.find('[data-field-name="quantity"]'), rowData['quantity'])
                    }
                    let productJqueryElement = $(element)
                    productJqueryElement.unbind('change').on("change", function(event){
                        if(selectizeChangeIsRunTwice(productJqueryElement)){
                            return
                        }
                        setTimeout(function(){
                            changeSubformProductListener(addedRow, productJqueryElement, "bomProductUnitFactorFieldsAdded", rowData)
                        },1000)
                    })

                    productJqueryElement.on('bomProductUnitFactorFieldsAdded', function(event, parameters){
                        let unitFactorIdJqueryElement = addedRow.find('[data-is-unit-factor-select="true"]')
                        let unitFactorJqueryElement = addedRow.find('[data-field-name="factor"]')
                        let rowBomData = parameters.bomData
                        let quantity = rowBomData.quantity
                        if(rowBomData && rowBomData['factor']){
                            quantity = quantity/rowBomData['factor']
                        }
                        
                        setFieldValue(unitFactorJqueryElement, rowBomData['factor'])
                        unitFactorJqueryElement.attr('data-bom-value', rowBomData['factor'])
                        unitFactorJqueryElement.trigger("unitFactorFieldsChangedEvent", {parentJqueryElement: addedRow})
                        setFieldValue(addedRow.find('[data-field-name="unit_name"]'), rowBomData['unit_name'])
                        setFieldValue(addedRow.find('[data-field-name="unit_small_name"]'), rowBomData['unit_small_name'])

                        if(pageMode == "edit"){
                            setTimeout(function(){
                                recalculateFormulas(true)
                            },1000)
                        }

                        unitFactorIdJqueryElement.unbind('change')
                        unitFactorIdJqueryElement.get(0).selectize.setValue(rowBomData['unit_factor_id'])
                        addedRow.find('[data-field-name="quantity"]').attr("data-bom-value", rowBomData.quantity)
                        setFieldValue(addedRow.find('[data-field-name="quantity"]'), quantity)
                        unitFactorIdJqueryElement.on("change", function(){
                            if(selectizeChangeIsRunTwice($(this))){
                                return
                            }
                            setUnitFactorHiddenFields(addedRow, unitFactorJqueryElement)
                            changeSubtotal(unitFactorJqueryElement)
                        })

                        productJqueryElement.unbind('bomProductUnitFactorFieldsAdded')
                        setTimeout(function(){                        
                            productJqueryElement.unbind('change').on("change", function(event){
                                if(selectizeChangeIsRunTwice(productJqueryElement)){
                                    return
                                }
                                changeSubformProductListener(addedRow, productJqueryElement)
                            })
                        },1)
                    })
                    setFieldValue(productJqueryElement, value)    
                }else{
                    setFormulaRelatedAttributes($(element), rowData, value)
                    setFieldValue($(element), value)
                }

                if(dataFieldName == "workstation_id"){
                    let workstationHiddenTotalCost = addedRow.find(`[data-field-name="workstation_total_cost"]`)
                    workstationHiddenTotalCost.val(rowData['workstation']['total_cost'])
                }
                addedRow.find('[data-subtotal-factor="true"]').on("change", function(){
                    let jqueryElement = $(this)
                    changeSubtotal(jqueryElement)
                })

            })
        }
        function setFormulaRelatedAttributes(jqueryElement, rowData, value){
            setBomValueAttr(jqueryElement, value)
            if(rowData["cost_type"]){
                jqueryElement.attr("data-cost-type", rowData["cost_type"])
                if(rowData["cost_type"] == "fixed_amount"){
                    return
                }
            }
            if(jqueryElement.attr("data-bom-input-name") && ( jqueryElement.attr("data-formula") || rowData["formula"] )){
                if(! jqueryElement.attr("data-formula")){
                    jqueryElement.attr("data-formula", rowData["formula"])
                }
            }
        }
        function setFieldValueByName(name, value){
            if($(`[name="${name}"]`).length){
                setFieldValue($(`[name="${name}"]`), value)
            }
        }
        function setBomValueByName(name, value){
            let jqueryElement = $(`[name="${name}"]`)
            setBomValueAttr(jqueryElement, value)
        }
        
        function setBomValueAttr(jqueryElement,value){
            if(!value){
                return
            }

            if(jqueryElement.length){
                jqueryElement.attr("data-bom-value", value)
            }            
        }


        function setFieldValue(jqueryElement, value){
            if(!value){
                return
            }
            let type = jqueryElement.prop('nodeName')
            if(type == "INPUT" || type == "TEXTAREA"){
                setInputValue(jqueryElement, value)
            }
            if(type == "SELECT"){
                setDropdownValue(jqueryElement, value)
            }
        }
        function setFormulaBasedValue(jqueryElement){
            let costType = jqueryElement.attr('data-cost-type')
            if(costType == "fixed_amount"){
                return
            }
            let formula = jqueryElement.attr('data-formula')
            let parentRow = jqueryElement.parents('[data-app-form-subform-row]')
            if(!parentRow.length){
                parentRow = $("#order-information-fields")
            }
            let unitFactorJqueryElement = parentRow.find(`[data-field-name="factor"]`)
            let unitFactor =  unitFactorJqueryElement.length ? unitFactorJqueryElement.val() : 1.0
            let bomUnitFactor = unitFactorJqueryElement.length ? unitFactorJqueryElement.attr('data-bom-value') : 1.0

            let orderQuantityFactor = $("#order-information-fields").find(`[data-field-name="factor"]`)
            let orderUnitFactor =  orderQuantityFactor.length ? orderQuantityFactor.val() : 1.0

            let orderMainUnitFactor = $('[name="factor"]').val() ?? 1.0
            let orderQuantity = $('[name="quantity"]').val() * orderMainUnitFactor

            let bomQuantity = $('[name="quantity"]').attr('data-bom-value')
            let bomMainUnitFactor = $('[name="factor"]').attr('data-bom-value') ?? 1.0
            let bomInputName = jqueryElement.attr('data-bom-input-name')
            let bomValue = jqueryElement.attr('data-bom-value')

            if(costType == "based_on_qty"){
                setInputValue(jqueryElement, (bomValue * ( orderQuantity / (bomQuantity * bomUnitFactor) )))
                return
            }

            let equationValues =  {
                "bom": {
                    'quantity': bomQuantity / bomMainUnitFactor,
                },
                "manufacturing_order":{
                    'quantity': orderQuantity / bomMainUnitFactor
                },
            }
            let subformName = jqueryElement.parents('table').attr('data-app-form-subform')
            if(!subformName){
                return
            }

            if(bomValue){
                let entityKey = "bom_"+ subformName.substr(0,subformName.length-1)
                equationValues[entityKey] = {} 
                equationValues[entityKey][bomInputName] = bomValue
                $.ajax({
                    url: "/v2/api/evaluateEquationByValues",
                    type:'POST',
                    data: {equation: formula, equation_values: JSON.stringify(equationValues)},
                    success: function(res){
                        setInputValue(jqueryElement, res.data)
                    }
                })
            }
        }
        function setInputValue(jqueryElement, value){
            jqueryElement.val(value)
            jqueryElement.trigger("keyup")
            jqueryElement.trigger("change")
        }
        function setDropdownValue(jqueryElement, value){
            let selectizeObj = jqueryElement.get(0).selectize
            let nameAttr = jqueryElement.attr("name")
            if(nameAttr.includes("journal_account_id")){
                $.ajax({
                    url: "/owner/journal_accounts/json_find_with_id?id=" + value,
                    type: "GET",
                    success: function(res) {
                        jqueryElement.append(`<option value="${res.id}">${res.name}</option>`)
                        jqueryElement.val(value)
                    }
                })
            }else{
                let selectAjaxOption = JSON.parse(jqueryElement.attr("data-app-form-select-options"))
                let url = ''
                if(nameAttr.includes("product_id")){
                    url = selectAjaxOption.ajax.url + '&id=' + value
                }else{
                    url = selectAjaxOption.ajax.url + '&filter[or][id][equal]=' + value
                }
                $.ajax({
                    url: url,
                    type: "GET",
                    success: function(res) {
                        selectizeObj.clearOptions()
                        selectizeObj.addOption(res)
                        selectizeObj.setValue(value)    
                    }
                })
            }
        }

        function setRelatedFieldFilter(query, callback, jqueryElement){
            let relationField = jqueryElement.attr('data-relation-child')
            let value = $(`[data-relation-parent="${relationField}"]`).val()
            let selectAjaxOption = JSON.parse(jqueryElement.attr("data-app-form-select-options"))
            let resultUrl = selectAjaxOption.ajax.url
            resultUrl = resultUrl.replaceAll("__q__", query)
            if(value){
                resultUrl += `&filter[${relationField}][equal]=` + value
            }
            $.ajax({
                url: resultUrl,
                type: "GET",
                error: function() {
                    callback();
                },
                success: function(results) {
                    jqueryElement.get(0).selectize.clearOptions()
                    if(results == undefined){
                        callback();
                    }else{
                        callback(results);
                    }
                }
            })
        }

        function setSubformProductsQuantitiesValues(productWithUnitsOnly = false){
            $('[data-field-name="quantity"]').each(function(index, element){

                let bomQuantity = $('[name="quantity"]').attr('data-bom-value')
                if(!bomQuantity){
                    return;
                }
                let parentRow = $(element).parents('[data-app-form-subform-row]')

                let unitFactorJqueryElement = parentRow.find(`[data-field-name="factor"]`)
                if(productWithUnitsOnly && !unitFactorJqueryElement.length){
                    return
                }
                let unitFactor =  unitFactorJqueryElement.length ? unitFactorJqueryElement.val() : 1.0

                let quantityValue = $(element).attr('data-bom-value')
                if(!quantityValue){
                    return
                }

                quantityValue = quantityValue / unitFactor

                let orderQuantityFactor = $("#order-information-fields").find(`[data-field-name="factor"]`)
                let orderUnitFactor =  orderQuantityFactor.length ? orderQuantityFactor.val() : 1.0

                let orderQuantity = $('[name="quantity"]').val() * orderUnitFactor

                setInputValue($(element), (quantityValue * orderQuantity / bomQuantity))
            })
        }

        function recalculateFormulas(productWithUnitsOnly = false){
            if(productWithUnitsOnly){
                setSubformProductsQuantitiesValues(true)
            }else{
                setSubformProductsQuantitiesValues()
                $('[data-bom-input-name]').each(function(index, element){
                    setFormulaBasedValue($(element))
                })
            }
        }

        function getDefaultBom(jqueryElement){
            let relationField = jqueryElement.attr('data-relation-parent')
            let value = jqueryElement.val()
            if(!value){
                return
            }
            let childJqueryElement = $(`[data-relation-child="${relationField}"]`)
            let selectizeObj = childJqueryElement.get(0).selectize
            let selectAjaxOption = JSON.parse(childJqueryElement.attr("data-app-form-select-options"))
            let resultUrl = selectAjaxOption.ajax.url.split("?")[0]
            resultUrl += `?fields[0]=id&fields[1]=code&fields[2]=name&filter[is_active][equal]=1&filter[product_id][equal]=${value}&filter[is_default][equal]=1`
            $.ajax({
                url: resultUrl,
                type: "GET",
                success: function(res) {
                    selectizeObj.clearOptions()
                    if(res.length > 0){
                        selectizeObj.addOption(res)
                        selectizeObj.setValue(res[0].id)
                    }else{
                        selectizeObj.setValue(null)
                    }
                }
            })
        }

        function addUnitFactorFields(parentJqueryElement, productJqueryElement, eventToTrigger = "productUnitFactorFieldsAdded", eventParameterObject = {}) {
            let quantityFieldsNamePrefix = productJqueryElement.attr('data-quantity-fields-name-prefix')
            let unitFactorList = productJqueryElement.get(0).selectize.options[productJqueryElement.val()]

            let oldUnitFactorSelectDiv = $("[data-qty-type-select='" + productJqueryElement.attr('data-filter-select') + "']").find('div.unit-factor-select')
            if(oldUnitFactorSelectDiv.length){
                oldUnitFactorSelectDiv.remove()
            }
            if(unitFactorList && unitFactorList.units && unitFactorList.units.length){
                var unitsOptions = ''
                let baseUnit = null
                for (var i = 0; i < unitFactorList.units.length; i++) {
                    if(unitFactorList.units[i].id == 0){
                        baseUnit = unitFactorList.units[i]
                        unitsOptions += `<option ${unitFactorList.units[i].id} selected value="${unitFactorList.units[i].id}">${unitFactorList.units[i].small_name}</option>`;
                    }else{
                        unitsOptions += `<option ${unitFactorList.units[i].id} value="${unitFactorList.units[i].id}">${unitFactorList.units[i].small_name}</option>`;
                    }
                }
                let names = {}
                if(!quantityFieldsNamePrefix){
                    names["unit_factor_id"] = "unit_factor_id"
                    names["factor"] = "factor"
                    names["unit_name"] = "unit_name"
                    names["unit_small_name"] = "unit_small_name"
                }else{
                    names["unit_factor_id"] = quantityFieldsNamePrefix + "[unit_factor_id]"
                    names["factor"] = quantityFieldsNamePrefix + "[factor]"
                    names["unit_name"] = quantityFieldsNamePrefix + "[unit_name]"
                    names["unit_small_name"] = quantityFieldsNamePrefix + "[unit_small_name]"
                }
                let unitFieldsTemplate = `
                <div class="ui-table-box-body-item-editable-input-container unit-factor-select">
                    <select data-is-unit-factor-select="true" name="${names["unit_factor_id"]}" class="l-input ui-input" data-app-form-select="true" autocomplete="off" data-app-form-select-template="currency" data-app-form-validate="required">
                        <option value=""></option>
                    ${unitsOptions}
                    </select>
                    <input data-field-name="factor" value="1" type="hidden" name="${names["factor"]}" />
                    <input data-field-name="unit_name" value="${baseUnit.unit_name}" type="hidden" name="${names["unit_name"]}" />
                    <input data-field-name="unit_small_name" value="${baseUnit.unit_small_name}" type="hidden" name="${names["unit_small_name"]}" />
                </div>
                `
                let unitFieldsDiv = $(unitFieldsTemplate)
                $("[data-qty-type-select='" + productJqueryElement.attr('data-filter-select') + "']").append(unitFieldsDiv);
                (new window.IzLayout.UiSelect()).initAllInNode(unitFieldsDiv, {onInitialize: function(){
                    let parameters = {
                        parentJqueryElement: parentJqueryElement,
                        unitFactorList: unitFactorList
                    }
                    for (var attrname in eventParameterObject) { parameters[attrname] = eventParameterObject[attrname]; }
    
                    if(productJqueryElement){
                        productJqueryElement.trigger(eventToTrigger, parameters)
                    }
                }});
    
            }

        }

        function getUnitFactorValuesFromSelect(productJqueryElement, unitFactorId){
            let unitFactorList = productJqueryElement.get(0).selectize.options[productJqueryElement.val()]
            let unitFactorValues = null
            if(!unitFactorList.units){
                unitFactorList = productsMapById ? productsMapById[productJqueryElement.val()] : null;
            }
            if(!unitFactorList.units){
                return
            }
            for (const iterator of unitFactorList.units) {
                if(iterator['id'] == unitFactorId){
                    unitFactorValues = iterator
                }                            
            }
            return unitFactorValues
        }

        function setUnitFactorHiddenFields(parentJqueryElement, unitFactorJqueryElement, eventToTrigger = "unitFactorFieldsChangedEvent", eventParameterObject = {}){
            let productJqueryElement = parentJqueryElement.find(`[data-field-name="product_id"]`)
            if(parentJqueryElement && parentJqueryElement.attr("id") == "order-information-fields"){
                productJqueryElement = parentJqueryElement.find(`[name="product_id"]`)
            }
            let unitFactorIdJqueryElement = parentJqueryElement.find(`[data-is-unit-factor-select="true"]`)
            let unitNameJqueryElement = parentJqueryElement.find(`[data-field-name="unit_name"]`)
            let unitSmallNameJqueryElement = parentJqueryElement.find(`[data-field-name="unit_small_name"]`)

            unitFactorValues = getUnitFactorValuesFromSelect(productJqueryElement, unitFactorIdJqueryElement.val())
            if(!unitFactorValues){
                return
            }
            unitFactorJqueryElement.val(unitFactorValues['factor'])
            unitNameJqueryElement.val(unitFactorValues['factor_name'] ?? unitFactorValues['name'])
            unitSmallNameJqueryElement.val(unitFactorValues['small_name'])
            unitFactorJqueryElement.promise().done(function(){
                let parameters = {
                    parentJqueryElement: parentJqueryElement
                }
                for (var attrname in eventParameterObject) { parameters[attrname] = eventParameterObject[attrname]; }
                unitFactorJqueryElement.trigger(eventToTrigger, parameters)  
            })
        }

        function changeSubformProductListener(parentRowJqueryElement, productJqueryElement, eventToTrigger = "productUnitFactorFieldsAdded", bomData = null){
            if(!productJqueryElement.val()){
                return
            }
            if(!setNullIfDuplicateProduct(productJqueryElement)){
                changeProductPrice(productJqueryElement)
                addUnitFactorFields(parentRowJqueryElement, productJqueryElement, eventToTrigger, {bomData:bomData})
            }
        }

        function setProductFactorFields(event = null, productJqueryElement, unitFactorValues = null, callback = null){
            let parentRow = productJqueryElement.parents('[data-app-form-subform-row]')
            if(!parentRow.length){
                return
            }

            let quantityJqueryElement = parentRow.find(`[data-field-name="quantity"]`).first()


            if(unitFactorValues){
                let unitFactorIdJqueryElement = parentRow.find(`[data-is-unit-factor-select="true"]`)
                let unitFactorJqueryElement = parentRow.find(`[data-field-name="factor"]`).first()
                let unitNameJqueryElement = parentRow.find(`[data-field-name="unit_name"]`).first()
                let unitSmallNameJqueryElement = parentRow.find(`[data-field-name="unit_small_name"]`).first()
                if(!event){
                    let intervalCounter = 0
                    var checkExist = setInterval(function() {
                        intervalCounter++
                        if (parentRow.find(`[data-is-unit-factor-select="true"]`).length) {
                            parentRow.find(`[data-is-unit-factor-select="true"]`).get(0).selectize.setValue(unitFactorValues['id'])
                            clearInterval(checkExist)
                        }
                        if(intervalCounter > 100){
                            clearInterval(checkExist)
                            intervalCounter = 0 
                        }
                    }, 100);
                }else{
                    unitFactorJqueryElement.val(unitFactorValues['factor'])
                    unitNameJqueryElement.val(unitFactorValues['name'])
                    unitSmallNameJqueryElement.val(unitFactorValues['small_name'])
                }
            }

            if(callback) setTimeout(function(){
                callback()
            },1)
        }

        function changeMainQuantityListener(getBom = false){
            if(pageMode == "edit" && getBom && $('[name="bom_id"]').val()){
                changeBom($('[name="bom_id"]'))
            }else{
                recalculateFormulas()
            }
        }

        function addSubProductionRoutingOptions(){
            let productionRoutingJqueryElement = $(`[name="production_routing_id"]`)
            if(!productionRoutingJqueryElement.val()){
                return;
            }
            $.ajax({
                url: "/v2/api/entity/sub_production_routing/list-photo/2?mode=listing&fields[0]=id&fields[1]=name&filter[production_routing_id][equal]=" + productionRoutingJqueryElement.val(),
                type: "GET",
                success: function(res) {
                    $(`[data-relation-child="production_routing_id"]`).each(function(index,element){
                        let selectizeObj = $(element).get(0).selectize                        
                        // selectizeObj.clearOptions()
                        if(res.length > 0){
                            selectizeObj.addOption(res)
                        }
                    })
                }
            })
        }

        function changeSubProductionRoutingReadonlyAttr(subProdJqueryElement){
            let productionRoutingJqueryElement = $(`[name="production_routing_id"]`)
            subProdJqueryElement.get(0).selectize.setValue(null)
            subProdJqueryElement.get(0).selectize.clearOptions()
            if(!productionRoutingJqueryElement.val()){
                subProdJqueryElement.attr("readonly", "readonly")
            }else{
                subProdJqueryElement.removeAttr("readonly")
            }
            subProdJqueryElement.trigger("change")
        }

        $(document).ready(function() {

            if(pageMode != "edit" && pageMode != "clone" && !errorsExist){
                setTimeout(function(){
                    $(`[data-relation-child="production_routing_id"]`).each(function(index,element){                    
                        changeSubProductionRoutingReadonlyAttr($(element))
                    })
                },1500)
            }
            setTimeout(function(){
                addSubProductionRoutingOptions()
            },1500)

            formJqueryElement.on("change", `[name="production_routing_id"]`, function(){
                $(`[data-relation-child="production_routing_id"]`).each(function(index, element){
                    changeSubProductionRoutingReadonlyAttr($(element))
                })
                addSubProductionRoutingOptions()
            })

            formJqueryElement.on("change", `[data-field-name="workstation_id"]`, function(){
                let workstationJqueryElement = $(this)
                changeWorkstationTotalCostHiddenValue(workstationJqueryElement)
            })

            formJqueryElement.on("unitFactorFieldsChangedEvent", `input[data-field-name="factor"]`, function(){
                let factorValue = $(this).val()
                let parentJqueryElement = $(this).parents('[data-app-form-subform-row]')
                let averagePriceJqueryElement = parentJqueryElement.find(`[data-product-average-price="true"]`)
                let priceJqueryElement = parentJqueryElement.find(`[data-field-name="price"]`)
                let averagePrice = averagePriceJqueryElement.val()
                if(!averagePrice) return
                priceJqueryElement.val(factorValue * averagePrice)
                priceJqueryElement.trigger("change")
            })

            $('#draftBtn').on("click", function(e){
                e.preventDefault()
                let autoNumberJqueryElement = $(`[name="code[code]"]`)
                let oldAutoNumberValue = autoNumberJqueryElement.val()
                formJqueryElement.append('<input type="hidden" name="is_draft_entity" value="1">')
                formJqueryElement.append('<input type="hidden" name="status" value="<?= ManufacturingOrderStatusUtil::DRAFT ?>">')
                let submitBtn = formJqueryElement.find('[type="submit"]')
                submitBtn.click()
                setTimeout(function(){
                    let errorsCount = Object.keys(formJqueryElement.appValidator.errors).length
                    if(errorsCount){
                        formJqueryElement.find(`input[name="is_draft_entity"]`).remove()
                        formJqueryElement.find(`input[name="status"]`).remove()
                        autoNumberJqueryElement.val(oldAutoNumberValue)
                    }
                },1)
            })
            
            $('select[name="product_id"]').on("change", function(){
                if(selectizeChangeIsRunTwice($(this))){
                    return
                }
                if(pageMode == "create" || pageMode == "clone"){
                    let bomJqueryElement = $(`[name="bom_id"]`)
                    bomJqueryElement.get(0).selectize.clearOptions()
                    bomJqueryElement.get(0).selectize.setValue("")
                    if($(this).val()){
                        bomJqueryElement.removeAttr("readonly")
                    }else{
                        bomJqueryElement.attr("readonly", "readonly")
                    }
                    bomJqueryElement.trigger("change")
                }
                if(pageMode == "edit"){
                    if(!pageJustLoaded){
                        addUnitFactorFields($("#order-information-fields"), $(this), "mainProductUnitFactorFieldsAdded")
                    }
                }else{
                    addUnitFactorFields($("#order-information-fields"), $(this), "mainProductUnitFactorFieldsAdded")
                }

                if(pageMode == "convert" || (pageMode == "edit" && pageJustLoaded)){
                    pageJustLoaded = false
                    return
                }
                getDefaultBom($(this))
            })

            $('#order-information-fields').on("mainUnitFactorFieldsChangedEvent", '[data-field-name="factor"]', function(unitFactorEvent, unitFactorParameters){
                changeMainQuantityListener(true)
            })
            $('#order-information-fields').on("change", '[data-is-unit-factor-select="true"]', function(){
                let unitFactorJqueryElement = $('#order-information-fields').find('[data-field-name="factor"]')
                setTimeout(function(){
                    setUnitFactorHiddenFields($('#order-information-fields'), unitFactorJqueryElement, "mainUnitFactorFieldsChangedEvent")
                },1)
            })
            
            $('select[name="bom_id"]').on("change", function(){
                if(selectizeChangeIsRunTwice($(this))){
                    return
                }

                changeBom($(this))
            })

            $('select[name="bom_id"]').on("bomChangedEvent", function(event, parameters){
                if(pageMode == "edit"){
                    recalculateFormulas()
                }
            })

            $('[name="quantity"]').on("change", changeMainQuantityListener)
            
            
            $('[data-total-input-subform]').on("change", function(){
                changeTotalCost()
                let subformName = $(this).attr('data-total-input-subform')
                $(`[data-total-text-subform="${subformName}"]`).text(format_price($(this).val(), "<?= $currencyCode ?>"))
            })

            setTimeout(function(){
                addRowListeners($("#order-information-fields"))
    
                $(`[data-app-form-subform-row="true"]`).each(function(index, row){
                    addRowListeners($(row))
                })
            },1000)

            $('[data-app-form-subform-add-btn]').on("click", function(){
                let btnJqueryElement = $(this)
                setTimeout(function(){
                    let addedRow =$(`[data-app-form-subform="${btnJqueryElement.attr('data-app-form-subform-add-btn')}"] tbody [data-app-form-subform-row="true"]`).last()
                    addRowListeners(addedRow)
                    changeSubformHeaderNumber(btnJqueryElement.attr('data-app-form-subform-add-btn'))
                    addedRow.find(`[data-relation-child="production_routing_id"]`).each(function(index,element){                    
                        changeSubProductionRoutingReadonlyAttr($(element))
                    })
                    addSubProductionRoutingOptions()
                },1)
            })

            if(pageMode == "convert"){
                formJqueryElement.append('<input type="hidden" name="is_convert_bom_to_mo" value="1">')                     
            }

            if(pageMode == "convert" && !errorsExist){
                let bomField = $('select[name="bom_id"]')
                setTimeout(function(){                    
                    setFieldValue(bomField, convertBomId)
                },1000)
            }

            /*** Add one row by default if empty subform when opened for the first time in BOM add/edit page ***/
            var collapseButton = document.querySelectorAll(".accordion-collapse-btn");
            collapseButton.forEach(element => {
                    var moduleName;
                    var addButtonCollapse = $(element).parents((".l-show-card-section-title-box")).find('[data-app-form-subform-add-btn]');
                    $(element).on('click', function(){
                        moduleName = addButtonCollapse.attr('data-app-form-subform-add-btn');
                        if($("[data-app-form-subform=" + moduleName + "] tbody tr").length == 0) {
                            addButtonCollapse.click();
                            setTimeout(() => {
                                $(element).parents((".l-show-card-section-title-box")).find(".collapse-qty").text($("[data-app-form-subform=" + moduleName + "] tbody tr").length)
                            }, 5);
                        }
                    });
            });

            // when submit form open all taps
            var errors = <?= json_encode($errors); ?> ?? [];
             openSubformOnErrors();
             $("button[type='submit']").on("click", function() {
                openSubformOnErrors();
            });

            function openSubformOnErrors() {
                collapseButton.forEach(element => {
                    if($(element).parents('.l-show-card-section-title-box').find("tbody tr").length || Object.keys(errors).length != 0) {
                        $(element).parents('.l-show-card-section-title-box').find('.collapse').collapse('show');
                    }
                });
            }
        })
    </script>
<?php $this->endSection() ?>


 

