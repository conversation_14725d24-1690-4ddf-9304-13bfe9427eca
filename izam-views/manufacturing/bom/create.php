
<?php

use App\Models\Workstation;
use App\Services\BomService;
use App\Services\ProductionRoutingService;
use App\Services\WorkstationService;
use App\Services\ProductService;

    $extends = 'layouts/deprecated-layout';
    $form->prepare();
    $row = new \Izam\Forms\View\Helper\Form\FormRow();
    $data = $form->getData();

    $method = 'POST';
    $formUrl = route('owner.entity.store', ['entityKey' => $entityKey]);
    $cancelUrl = route('owner.entity.list', ['entityKey' => $entityKey]);
    $isEditing = false;
    $subProductionRoutings = [];
    if($form->get('id')->getValue()){
        $isEditing = true;
        $method = 'PUT';
        $formUrl = route('owner.entity.update', ['entityKey' => $entityKey , 'id' => $data['id']]);
        $cancelUrl = route('owner.entity.show', ['entityKey' => $entityKey , 'id'=> $data['id']]);

    }
    $fillform = $isEditing || isset($isClone) || (session()->get('errors') != null);

    $title = $isEditing ? sprintf(__t('Edit %s'), __t('Bill of Material')) : sprintf(__t('Add %s'), __t('Bill of Material'));
    $this->viewVars['title_for_layout'] = sprintf($title, __t('Bill of Material'));
    $this->viewVars['_PageBreadCrumbs'] = $this->sharedVars['generalBreadCrumbs'];


$relatedWorkstationsIds = array_map(function($row){return $row['workstation_id'];}, $data['operations']?? []);
    $relatedWorkstations = [];
    if($relatedWorkstationsIds){
        $relatedWorkstations = Workstation::whereIn('id', $relatedWorkstationsIds)->get()->toArray();
        if($relatedWorkstations){
            $tmp = [];
            foreach($relatedWorkstations as $relatedWorkstation){
                $tmp[$relatedWorkstation['id']] = $relatedWorkstation;
            }
            $relatedWorkstations = $tmp;
        }
    }


    $productionRoutingService = resolve(ProductionRoutingService::class);
    $workstationService = resolve(WorkstationService::class);


    if ($isEditing || isset($isClone)){
        $subProductionRoutings =  $productionRoutingService->getSubProductionRoutingByMainRoute($data['production_routing_id']);
    }
    
    function initSelectOption($optionValues , $removeHashes = false){
        if($optionValues){
            $label = $optionValues['label'];
            if ($removeHashes){
                $label = str_replace('#','',$label);
            }
            echo '<option selected value="'.$optionValues['value'].'">'.$label.'</option>';
        }
    }

    function initQtyunitFactorSelection($unitFactorId, $selectName, $data, $productUnitTemplateId = null){
            $selectedUnitFactorId = '';
            if(!$productUnitTemplateId){
                return '';
            }
            if(isset($data['unit_factor_data']) || (isset($data['factor']) && $data['factor'])){
                if(isset($data['unit_factor_id'])){
                    if($data['factor'] === 1){
                        $selectedUnitFactorId = 0;
                    }else{
                        $selectedUnitFactorId = $data['unit_factor_id'];
                    }
                }elseif(isset($data['unit_factor_data'])){
                    $selectedUnitFactorId = explode('_', $data['unit_factor_data'])[0];
                }
            
                $unitFactors = [];
                if($unitFactorId->getValueOptions()??[]){

                    foreach($unitFactorId->getValueOptions() as $unitFactor){
                        $unitFactor = json_decode($unitFactor['attributes']['data-unit']);
                        $unitFactors[$unitFactor->id] = ["name"=>$unitFactor->small_name, "value"=>$unitFactor->id."_".$unitFactor->factor."_".$unitFactor->factor_name."_".$unitFactor->small_name];
                    }
                }
                $select = '
                <div class="unit-factor-select">
                <select name="'.$selectName.'" data-select-product-unit class="l-input ui-input" data-app-form-select="true" placeholder="'.__t('Unit').'" data-app-form-select-template="currency" data-app-form-validate="required">
                    <option value=""></option>
                ';
                foreach($unitFactors as $id => $unitFactor){
                    $select .= '<option '.($id == $selectedUnitFactorId ?'selected':'').' value="'.$unitFactor['value'] .'">'.$unitFactor['name'].'</option>';
                }
                $select .= '
                        </select>
                    </div>
                ';
                return $select; 
        }
        return '';
    }
    
    $productionRoutings = $productionRoutingService->searchMainProductionRouting();
    $workstations= $workstationService->searchWorkstations();
    $productIds = [];
    $productsMapById = [];
    if(!empty($data['product_id'])){
        $productIds[] = $data['product_id'];
    }
    if(!empty($data['materials'])){
        foreach($data['materials'] as $key => $material) {
            $productIds[] = $material['product_id'];
        }
    }
    if(!empty($data['scraps'])){
        foreach($data['scraps'] as $key => $scrap) {
            $productIds[] = $scrap['product_id'];
        }
    }
    $productService = resolve(ProductService::class);
    $productsMapById = $productService->getProductsWithUnitTemplate($productIds)
    ->map(function ($product) {
        $item = [];
        $item["id"] = $product->id;
        $item["average_price"] = $product->average_price ?? 0;
        if(!empty($product->unitTemplate)){
            $item["units"] = $product->unitTemplate->factors->map(function ($factor) {
                $factor = $factor->toArray();
                $factor['unit_name'] = $factor['factor_name'];
                $factor['unit_small_name'] = $factor['small_name'];
                return $factor;
            })->all();

            array_unshift($item["units"], [
                "id" => 0,
                "factor" => 1,
                "factor_name" => $product->unitTemplate->main_unit_name,
                "small_name" => $product->unitTemplate->unit_small_name,
                "unit_name" => $product->unitTemplate->main_unit_name,
                "unit_small_name" => $product->unitTemplate->unit_small_name,                
            ]);
            $product->units = $item["units"];
        }
        $product->text = $item["text"] = $product->name . " #" . $product->product_code;
        return $product;
    })->keyBy("id")->all();

    function handleSubformProductQty($subformData, $productsMapById){
        $qty = '';
        $unitFactorData = [];
        if(isset($subformData['unit_factor_data'])){
            $unitFactorData = explode('_', $subformData['unit_factor_data']);
        }
        if((($productsMapById[$subformData["product_id"]]->unit_template_id??null) == null) && ($subformData['quantity']['unit_factor_id']?? ($unitFactorData[0]??0))){
            $qty =  $subformData['quantity']['quantity'];
        }elseif(isset($subformData['quantity']['quantity'])?? []){
            if($subformData['quantity']['unit_factor_id']?? ($unitFactorData[0]??0)){
                $qty = $subformData['quantity']['view_quantity']?? $subformData['quantity']['quantity'];
            }else{
                $qty = $subformData['quantity']['quantity'];
            }
        }
        return $qty;
    }
    $currency = getCurrentSite('currency_code');
    $bomService = resolve(BomService::class);
    if (empty($data['code']['code'])) {
        if ($method == 'POST') {
            $autoNumber = $form->getFieldSets()['code']->getElements()['generated']->getValue() ?? "000001";
        } else {
            $bom = $bomService->find($data['id']);
            $autoNumber = $bom->code;
        }
    } else {
        $autoNumber = $form->getFieldSets()['code']->getElements()['code']->getValue() ?? "000001";
    }
    $autoNumberGenerated = $form->getFieldSets()['code']->getElements()['generated']->getValue() ?? "000001";

?>
     <link href="<?=getCdnAssetsUrl()?>css/forms/plugin_select2.min.css" rel="stylesheet"/>
     <link href="<?=getCdnAssetsUrl()?>css/forms/plugin_select2_rtl.min.css" rel="stylesheet"/>

 <style>

    #beta-dropdown {
        background-color: transparent;
    }

    @media (max-width: 767.98px) {

        .open .beta-dropdown,
        .beta-dropdown.show {
            width: 253px !important;
        }
    }
    :root {
        --bs-dark: #4e5381;
        --bs-dark-2: #373B50;
        --bs-dark-3: #75799d;

        --bs-light: #f6f9fc;
        --bs-light-2: #c1d2e6;
        --bs-light-3: #9b9eb8;
        --bs-secondary: #E4EBF2;
        --bs-border-width: 1px;
        --bs-border-style: solid;
        --bs-border-color: #dee2e6;
        --bs-light-rgb: 246, 249, 252;
        --bs-dark-3-rgb: 117, 121, 157;
    }

    .l-input-label {
        height: 21px;
    }

    .accordion-collapse .accordion-collapse-btn {
        width: 100%;
        color: #202124;
        background-color: #f6f9fc;
        font-size: 0.875rem;
        font-weight: 500;
        border: 1px solid #E4EBF2;
        line-height: 1;
        opacity: 1;
        min-height: 43px;
    }
    @media (min-width: 768px) { 
        .accordion-collapse .accordion-collapse-btn {
            padding-right: 17px;
            padding-left: 17px;
        }
    }


    .accordion-collapse .accordion-collapse-btn .accordion-collapse-arrow i {
        font-size: 22px;
    }

    .gap-4,
    .status-icon,
    .status-circle,
    .accordion-collapse .accordion-collapse-btn .accordion-collapse-title {
        gap: 0.5rem !important;
    }

    .align-items-center,
    .status-icon,
    .status-circle,
    .text-meta,
    .title-big-text,
    .accordion-collapse .accordion-collapse-btn .accordion-collapse-title,
    .toast {
        align-items: center !important;
    }

    .d-inline-flex,
    .accordion-collapse .accordion-collapse-btn .accordion-collapse-title {
        display: inline-flex !important;
    }

    .accordion-collapse .accordion-collapse-btn .accordion-collapse-title i {
        font-size: 22px;
    }

    /* .filter-group-input {
        display: flex;
    } */

    .filter-group-input>* {
        flex: 4 1 auto;
    }

    .text-dark-3 {
        --bs-text-opacity: 1;
        color: rgba(117, 121, 157, 1) !important;
    }

    .d-none {
        display: none !important;
    }

    [dir=ltr] .text-end,
    [dir=rtl] .text-start {
        text-align: right !important;
    }

    [dir=rtl] .text-end,
    [dir=ltr] .text-start {
        text-align: left !important;
    }



    [dir=rtl] .text-end {
        text-align: left !important;
    }

    [dir=ltr] .accordion-collapse-btn {
        text-align: left !important;
    }

    [dir=rtl] .accordion-collapse-btn {
        text-align: right !important;
    }


    .accordion-collapse .accordion-collapse-btn[aria-expanded^=false] [aria-expanded^=true] {
        display: none;
    }

    .accordion-collapse .accordion-collapse-btn[aria-expanded^=true] [aria-expanded^=false] {
        display: none;
    }

    /* Subform style */
    .subform-table thead th {
        font-size: 0.875rem;
        font-weight: 400;
        color: var(--bs-dark-3);
        padding: 10px;
        background: var(--bs-light);
        border: 1px solid var(--bs-secondary);
    }

    .subform-table tbody td {
        border: 1px solid var(--bs-secondary);

    }

    .subform-table tbody tr td.subform-cell-text {
        background: var(--bs-light);
        color: #202124;
        padding: 10px;
        font-size: 0.875rem;
    }


    .subform-table tfoot tr td.subform-cell-text,
    .subform-table tfoot tr th.subform-cell-text {
        background: var(--bs-light);
        color: #202124;
        padding: 10px;
        font-size: 0.875rem;
    }

    .subform-table tbody tr td .input-container .form-group {
        height: 100%;
        display: flex;
        flex-direction: column;
        margin-bottom: 0;
    }

    .ui-table-box-body-item--editable .selectize-input,
    .ui-table-box-body-item--editable textarea,
    .ui-table-box-body-item--editable input {
        height: 100%;
    }

    [dir="ltr"] .border-start  {
        border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    }
    [dir="rtl"] .border-start {
        border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    }

    .border-bottom {
        border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    }

    [dir="ltr"] .border-end  {
        border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    }
    [dir="rtl"] .border-end {
        border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    }

    .d-table-row {
        display: table-row !important;
    }

    .d-none {
        display: none !important;
    }

    .fw-medium {
        font-weight: 500 !important;
    }

    .fw-bold {
        font-weight: 600 !important;
    }


    .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container.d-flex-row>* {
        flex: 1 1;
    }

    .d-flex {
        display: flex;
    }

    .d-flex.justify-content-between {
        display: flex;
        justify-content: space-between;
    }

    .d-flex.align-items-center {
        align-items: center;
    }

    .bg-light {
        --bs-bg-opacity: 1;
        background-color: rgba(var(--bs-light-rgb), 1) !important;
    }

    .text-dark-3 {
        --bs-text-opacity: 1;
        color: rgba(var(--bs-dark-3-rgb), 1) !important;
    }

    .px-10 {
        padding-right: 1.25rem !important;
        padding-left: 1.25rem !important;
    }

    .full-height {
        height: 100%;
    }

    .total-cell {
        background-color: rgba(32, 33, 36, 1);
        color: #FFF;
        font-weight: 500;
        font-size: 1.25rem;
        gap: 1.25rem;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        padding-right: 1rem;
        padding-left: 1rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }


    @media (min-width: 992px) {
        .d-lg-inline-block {
            display: inline-block !important;
        }

        .subform-table tbody tr td {
            border-bottom: 1px solid var(--bs-secondary);
        }

        .d-lg-table-row {
            display: table-row !important;
        }

        .d-lg-none {
            display: none !important;
        }

        .d-lg-table-row  .l-btn-group-box,
        .l-btn-group-box .l-btn-group {
            height: 100%;
        }
    }

    @media (max-width: 993px) {
        .subform-table thead {
            display: none;
        }
    }
    .ui-table-box-body-item-editable-input-container {
        position: relative;
    }

    .generate-icon {
        display: flex;
    }

    .prefix-text {
        display: none;
    }

    .generate-icon,
    .prefix-text {
        cursor: pointer;
        position: absolute;
        top: 0;
        bottom: 0;
        padding: 0 10px;
        align-items: center;
    }

    .ui-table-box-body-item-editable-input-container:hover .prefix-text {
        display: flex;
    }

    .update-price-container{
        position: absolute;
        top: 0;
        height: 100%;
        display: flex;
        align-items: center;
    }
    .update-price-container:has(+ .accordion-collapse-btn[aria-expanded=false])    {
        display: none  !important;
        background-color: red !important;
    }
    .update-price-container button{
        border: 0;
        outline: none;
        background-color: transparent;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .update-price-container button i{
        color: #1877F2;
        font-size: 20px;
    }
    [dir="ltr"] .update-price-container{
        right: 65px;
    }
    [dir="rtl"] .update-price-container{
        left: 65px;
    }

    [dir="ltr"] .generate-icon,
    [dir="ltr"] .prefix-text {
        right: 0;
    }

    [dir="rtl"] .generate-icon,
    [dir="rtl"] .prefix-text {
        left: 0;
    }

    [dir="ltr"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .generate-input {
        padding-right: 52px;
    }

    [dir="rtl"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .generate-input {
        padding-left: 52px;
    }

    [dir="ltr"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .prefix-input {
        padding-right: 102px;
    }

    [dir="rtl"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .prefix-input {
        padding-left: 102px;
    }

    .select2-results__options {
        padding-left: 0 !important;
    }
    /* .account-select{
        height: 100% !important;
        background-color: red;
    } */
    .account-select .select2{
        height: 100% !important;;
    }
    .select2-container{
        height: 100% !important;
    }
    .ui-table-box-row .select2-container--bootstrap4 .select2-selection,.ui-table-box-row .select2-container--bootstrap4.select2-container--focus.select2-container--open .select2-selection{
        box-shadow:none !important;
        height: 100% !important;;
        border: 0 !important;;
    }

    [dir="rtl"] .select2-container--bootstrap4 .select2-selection__clear{
        float: left;
        padding-left: unset;
        padding-right: .3em;
    }

    [dir="rtl"] .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow{
        right: auto;
        left: 3px;
    }

    [dir="rtl"] ul.select2-results__options{
        padding-right: 0;
    }

    [dir="ltr"]  .select2-results__options.select2-results__options--nested li{
        padding-left: 2rem;
    }

    [dir="rtl"]  .select2-results__options.select2-results__options--nested li{
        padding-right: 2rem;
    }

    .ui-table-box td .select2-container:hover{
        background-color: #fff9db;
        box-shadow: none;
    }


    /* select2 */
    .select2-container--bootstrap4 .select2-selection {
        min-height: 42px !important;
        border-color: #e4ebf2;
    }
    .select2-container--bootstrap4 .select2-selection:hover {
        border-color: #75799d;
    }
    .select2-container--bootstrap4.select2-container--focus .select2-selection {
        border-color: #1877f2 !important;
        box-shadow: 0 0 0 1px #1877f2 !important;
    }
    .select2-container .select2-selection--single .select2-selection__rendered {
        padding-left: 12px;
        padding-right: 50px;
    }
    [dir=rtl] .select2-container .select2-selection--single .select2-selection__rendered {
        padding-right: 12px;
        padding-left: 50px;
    }
    .select2-container--bootstrap4 .select2-selection__clear {
        text-align: center;
        padding-left: 1px;
        position: absolute !important;
        top: 50%;
        transform: translateY(-50%);
        margin: 0 !important;
        right: 25px;
    }
    [dir=rtl] .select2-container--bootstrap4 .select2-selection__clear {
        padding-right: 0px;
        left: 25px;
        right: auto;
    }
    .select2-container, .select2-container .selection {
        height: 100%;
    }
    tr .select2-container--bootstrap4 .select2-selection {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        width: 100%;
    }
    .select2-container .select2-selection--single .select2-selection__rendered {
        width: 100%;
    }

    .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .select2-container:hover,
    .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .select2-container:focus  {
        background-color: #fff9db;
        box-shadow: none;
    }

    .ui-table-add-btn {
        min-height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0 !important;
        margin-top: -1px;
        margin-bottom: -1px;
    }

    @media (max-width: 767.98px) {
        .m-app-page-head-all .l-container {
            padding-left: 4px;
            padding-right: 4px;
        }
        .ui-table-box-row.d-lg-none td button {
            min-height: 42px;
            align-items: center;
            margin-bottom: 24px;
        }

        /* table responsive */
        table td {
            display: block;
            font-size: .8em;
            text-align: right;
        }
        table tbody td.subform-cell-text,
        .ui-table-box-body-item--removeable {
            height:auto;
            min-height: 50px;
            display: inline-block;
            border-top:none;
        }
        table tbody .ui-table-box-body-item--removeabl {
            width: 50px;
        }
        table thead {
            border: none;
            clip: rect(0 0 0 0);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            width: 1px;
        }
        .ui-table-box-body-item--removeable .ui-table-box-body-item-removeable-btn {
            border-top: none;
        }
        [dir="ltr"] .ui-table-box-body-item--removeable .ui-table-box-body-item-removeable-btn  {
            border-left: none !important;
        }
        [dir="rtl"] .ui-table-box-body-item--removeable .ui-table-box-body-item-removeable-btn  {
            border-right: none !important;
        }
        .subform-table tbody tr td.subform-cell-text {
            min-height: 51px;
            width: calc(100% - 52px);
        }
        .subform-table tbody td {
            border-top: none;
        }
        [dir="ltr"] .subform-table tbody tr td.subform-cell-text {
            border-right: none;
        }
        [dir="rtl"] .subform-table tbody tr td.subform-cell-text {
            border-left: none;
        }

        [dir="ltr"] .border-md-start  {
        border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }
        [dir="rtl"] .border-md-start {
            border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }
        [dir="ltr"] .border-md-end  {
            border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }
        [dir="rtl"] .border-md-end {
            border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }
        .px-0 {
            padding-right: 0px;
            padding-left: 0px;
        }
    }

    [dir="ltr"] .ui-table-add-btn  {
        margin-left: -1px;
    }
    [dir="rtl"] .ui-table-add-btn  {
        margin-right: -1px;
    }
</style>

<?php $this->section('page-head') ?>
    <div class="m-app-page-head">
        <div class="m-app-page-head-all">
            <div class="l-container">
                <div class="l-flex-row l-flex--align-center">
                    <div class="l-flex-col-lg-12 u-text-align-end">
                        <div class="l-btn-list-h l-btn-list-h--inline">
                            <a href="<?=$cancelUrl?>" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-secondary u-text-color-action u-text-hover-color-action">
                                <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                <span class="ui-btn-inner-content">
                                    <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-close-thick"></i>
                                    <span class="ui-btn-inner-text"><?=__t('Cancel')?></span>
                                </span>
                            </a>
                            <button class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white" type="submit">
                                <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                                <span class="ui-btn-inner-content">
                                    <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-content-save"></i>
                                    <span class="ui-btn-inner-text"><?=__t('Save')?></span>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $this->endSection() ?>

    <input type="hidden" name="_token" value="<?= csrf_token() ?>">
    <input type="hidden" name="_method" value="<?= $method ?>">
    <?php if (\App\Facades\Plugins::pluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)): ?>
        <?php echo $row($form->get('branch_id')->setAttribute('disable-container', true)); ?>
    <?php endif; ?>
    <?php echo $row($form->get('staff_id')->setAttribute('disable-container', true)); ?>
    <?php if($isEditing):?>
        <input type="hidden" name="id" value="<?= isset($data['id'])  ? $data['id'] : '' ?>" id="id">
    <?php endif; ?>


    <div class="m-app-content">
        <div class="l-create-card-box" style="margin-right: 0;margin-left: 0;">
            <div class="l-container">
                <div class="ui-card">
                    <div class="ui-card-header">
                        <h3 class="ui-card-header-title"><?= sprintf(__t("%s Information"), __t('Bill of Material')) ?></h3>
                    </div>
                    <div class="ui-card-content ui-card-content-box--spacing-bottom-0">
                        <div class="ui-card-content-box">
                            <div class="l-flex-row l-flex-row--spacing-8">

                                <div class="l-flex-col-lg-4">
                                    <div class="l-input-box">
                                        <label for="name" class="l-input-label ui-input-label"><?=__t('Name')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                        <input type="text" class="l-input ui-input" name="name" id="name"  value="<?= isset($data['name']) ? $data['name'] : '' ?>" data-app-form-validate="required" />
                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'name']) ?>
                                    </div>
                                </div>

                                <div class="l-flex-col-lg-2">
                                    <div class="l-input-box">
                                        <label for="code" class="l-input-label ui-input-label"><?=__t('Code')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                        <input type="text" class="l-input ui-input" name="code[code]" id="code" value="<?= isset($data['code']['code']) ? $data['code']['code'] : $autoNumber ?>" data-app-form-validate="required" />
                                        <input type="hidden" class="l-input ui-input" name="code[generated]" id="code" value="<?= isset($data['code']['generated']) ? $data['code']['generated'] : $autoNumberGenerated ?>" data-app-form-validate="required" />
                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'code.code']) ?>

                                    </div>
                                </div>

                                <div class="l-flex-col-lg-3">
                                    <label class="l-input-label ui-input-label"> </label>
                                    <div class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                                        <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                            <input type="checkbox" name="is_active" value="1" <?= isset($data['is_active']) ? ($data['is_active']?'checked':'') : 'checked' ?> class="ui-switch ui-switch--size-40 ui-switch--active-color-active">
                                            <span class="ui-radio-label-content"><?=__t('Active')?></span>
                                            <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'is_active']) ?>
                                        </label>
                                    </div>
                                </div>

                                <div class="l-flex-col-lg-3">
                                    <label class="l-input-label ui-input-label"> </label>
                                    <div class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                                        <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                            <input type="checkbox" name="is_default" value="1" <?= isset($data['is_default']) ? ($data['is_default']?'checked':'') : '' ?>  class="ui-switch ui-switch--size-40 ui-switch--active-color-active">
                                            <span class="ui-radio-label-content"><?=__t('Is Default')?></span>
                                            <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'is_default']) ?>

                                        </label>
                                    </div>
                                </div>


                                <div class="l-flex-col-lg-6">
                                    <div class="l-input-box">
                                        <label for="product" class="l-input-label ui-input-label"><?=__t('Product')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                        <select name="product_id" class="main-product-select l-input ui-input" data-bom-qty="true" data-qty-input-name="unit_factor_data" data-filter-select="filter-qty-type-select" data-app-form-select="true" data-app-form-select-template="ajax-simple-3" data-app-form-validate="required">
                                            <option value=""></option>
                                            <?php initSelectOption($form->getElements()['product_id']->getValueOptions()[0] ?? []); ?>
                                        </select>
                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'product_id']) ?>
                                    </div>
                                </div>

                                <div class="l-flex-col-lg-6">
                                    <div class="l-input-box">
                                        <label for="account" class="l-input-label ui-input-label"><?=__t('Account')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                        <select name="journal_account_id" id="account" class="account-select l-input ui-input" data-app-form-validate="required">
                                            <?php initSelectOption($form->getElements()['journal_account_id']->getValueOptions()[0] ?? []); ?>
                                        </select>
                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'journal_account_id']) ?>
                                    </div>
                                </div>

                                <div class="l-flex-col-lg-6">
                                    <div class="l-input-box" data-bom-qty-input>
                                        <label for="quantity" class="l-input-label ui-input-label"><?=__t('Quantity')?><span class="u-text-color-danger">&nbsp;*</span></label>
                                        <div class="filter-group-input" data-qty-type-select="filter-qty-type-select">
                                            <div>
                                                <?php 
                                                    $viewQuantity = $form->getData()['quantity']['view_quantity']?? null;
                                                    if($viewQuantity && (($productsMapById[$data["product_id"]]->unit_template_id??null) == null) && $form->getData()['quantity']['factor']){
                                                        $viewQuantity = $data['quantity']['quantity'];
                                                    }
                                                ?>
                                                <input type="text" class="l-input ui-input" name="quantity[quantity]" value="<?=$viewQuantity?? ($data['quantity']['quantity']??'')?>"  id="quantity" data-app-form-validate="required" data-app-form-number="true" />
                                            </div>
                                            <?php  
                                                echo initQtyunitFactorSelection($form->getFieldSets()['quantity']->getElements()['unit_factor_id'], 'unit_factor_data', $data, isset($data["product_id"])?($productsMapById[$data["product_id"]]->unit_template_id??null) : null);
                                            ?>
                                            <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'quantity.quantity']) ?>
                                         </div>
                                    </div>
                                </div>
                                
                                <div class="l-flex-col-lg-6">
                                    <div class="l-input-box">
                                        <label for="production-routing" class="l-input-label ui-input-label"><?=__t('Production Routing')?></label>
                                        <select name="production_routing_id" id="production-routing" class="main-production-routing-select l-input ui-input" data-app-form-select="true" data-app-form-select-template="ajax-simple-3">
                                            <?php initSelectOption($form->getElements()['production_routing_id']->getValueOptions()[0] ?? []); ?>
                                        </select>
                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'production_routing_id']) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 0 0 2rem 0;"></span>
                        <div class="l-show-card-section-title-box">
                            <div class="l-flex-row">
                                <div class="l-flex-col-12">
                                    <div class="accordion-collapse" style="position: relative;">
                                        <?php if($fillform):?>
                                            <div class="update-price-container" aria-expanded="true">
                                                <button id="update_avg_price_btn" type="button" class="update-price-btn"><i class="mdi mdi-update"></i> <?=__t('Update Prices')?></button>
                                            </div>
                                        <?php endif;?>
                                        <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='materials']" aria-expanded="true" aria-controls="materials">
                                            <div class="l-flex-row">
                                                <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                    <span class="accordion-collapse-title">
                                                        <i class="mdi mdi-package-variant"></i>
                                                        <span><?=__t('Materials')?> (<span class="collapse-qty"><?=$fillform?count($form->getFieldSets()['materials']->getData()?? []) :'1'?></span>)</span>
                                                    </span>
                                                </span>
                                                <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                    <div aria-expanded="false" style="margin-top: 5px;">
                                                        <div class="l-flex-row ">
                                                            <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </div>
                                                            <div class="l-flex-col-12 l-flex-col-lg-2" data-col-total="materials">
                                                                <span class="val"><?=$fillform?$data->materials_total_cost??0:'0'?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </span>
                                                <span class="l-flex-col-1 text-end px-0">
                                                    <span class="accordion-collapse-arrow">
                                                        <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                        <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="collapse show" data-accordion-collapse='materials'>
                                <div class="l-flex-row">
                                    <div class="l-flex-col-lg-12">
                                        <div class="l-table-box-wrapper">
                                            <table class="l-table-box  subform-table " data-app-form-subform="materials-subform" data-app-form-subform-options='{ "sortable": false, "minimumRows":1}'>
                                                <thead>
                                                    <tr class="ui-table-box-row">
                                                        <th width="1000"><?=__t('Product')?></th>
                                                        <th width="1000"><?=__t('QTY')?></th>
                                                        <th width="500"><?=__t('Price')?></th>
                                                        <th width="1000"><?=__t('Production Operation')?></th>
                                                        <th width="600"><?=__t('Subtotal')?></th>
                                                        <th width="50"></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if(($form->getFieldSets()['materials']->getData() ?? []) || $fillform):?>

                                                        <?php foreach($form->getFieldSets()['materials']->getData() ?? [] as $i => $material): ?>
                                                            <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                    <div class="ui-table-box-body-item-editable-input-container d-flex-row" >
                                                                        <?php if(isset($material['id'])):?>
                                                                            <input type="hidden" name="materials[<?=$i?>][id]" value="<?=$material['id']?>" id="id">
                                                                        <?php endif;?>
                                                                        <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Product')."'":''?> value="<?=$material['product_id']?>" name="materials[<?=$i?>][product_id]" id="filter-product" class="l-input ui-input material-select-product" data-qty-input-name="materials[<?=$i?>][unit_factor_data]" data-filter-select="materials-qty-unit[0]"   data-app-form-select-template="ajax-simple-3" data-app-form-validate="required">
                                                                            <option value=""></option>
                                                                            <?php initSelectOption($form->getFieldSets()['materials']->getFieldSets()[$i]->getElements()['product_id']->getValueOptions()[0]?? []); ?>
                                                                        </select>
                                                                        <input type="hidden" data-product-average-price value="<?= $material['price'] ?>" >
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$i.product_id"]) ?>
                                                                    </div>
                                                                </td>

                                                               
                                                            
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="d-flex ui-table-box-body-item-group" data-qty-type-select="materials-qty-unit[0]">
                                                                        <div class="ui-table-box-body-item-editable-input-container" >
                                                                        <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('QTY')."'":''?> type="text" data-product-qty class="l-input ui-input" name="materials[<?=$i?>][quantity][quantity]" value="<?=handleSubformProductQty($material, $productsMapById)?>"  data-app-form-validate="required" data-app-form-number="true" />
                                                                        </div>
                                                                    </div>
                                                                    <?php
                                                                        echo initQtyunitFactorSelection($form->getFieldSets()['materials']->getFieldSets()[$i]->getFieldSets()['quantity']->getElements()['unit_factor_id'], "materials[$i][unit_factor_data]", $data['materials'][$i], $productsMapById[$material["product_id"]]->unit_template_id??null);
                                                                    ?>
                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$i.quantity.quantity"]) ?>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Price')."'":''?> type="text" data-product-price readonly="readonly" value="<?=$material['price']?>" style="cursor: default;" name="materials[<?=$i?>][price]" class="ui-input" data-app-form-validate="required" />
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$i.price"]) ?>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Production Operation')."'":''?> class="ui-input sub-production-routings-select" name="materials[<?=$i?>][sub_production_routing_id]" data-app-form-select="true"  >
                                                                        <?php initSelectOption($form->getFieldSets()['materials']->getFieldSets()[$i]->getElements()['sub_production_routing_id']->getValueOptions()[0]?? [] , true); ?>
                                                                        <?php foreach($subProductionRoutings as $subProductionRouting):?>
                                                                            <option value="<?= $subProductionRouting['id'] ?>"><?= $subProductionRouting['name'] ?></option>
                                                                        <?php endforeach;?>
                                                                         </select>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.$i.sub_production_routing_id"]) ?>
                                                                    </div>
                                                                </td>

                                                                <td class="subform-cell-text border-start" data-col-subtotal="materials" >
                                                                    <span class="val">0</span>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                    <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="materials-subform">
                                                                        <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach;?>
                                                    <?php else: ?>
                                                        <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="ui-table-box-body-item-editable-input-container">
                                                                    <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Product')."'":''?> name="materials[0][product_id]" id="filter-product" class="l-input ui-input material-select-product" data-qty-input-name="materials[0][unit_factor_data]" data-filter-select="materials-qty-unit[0]" data-app-form-select-template="ajax-simple-3" data-app-form-validate="required">
                                                                        <option value=""></option>
                                                                    </select>
                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.0.product_id"]) ?>
                                                                </div>
                                                            </td>
                                                        
                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="d-flex ui-table-box-body-item-group" data-qty-type-select="materials-qty-unit[0]">
                                                                    <div class="ui-table-box-body-item-editable-input-container" >
                                                                        <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('QTY')."'":''?> type="text" data-product-qty class="l-input ui-input" name="materials[0][quantity][quantity]" value="" data-app-form-validate="required" data-app-form-number="true" />
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.0.quantity.quantity"]) ?>
                                                                    </div>
                                                                </div>
                                                            </td>

                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="ui-table-box-body-item-editable-input-container">
                                                                    <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Price')."'":''?> type="text" data-product-price readonly="readonly" style="cursor: default;" name="materials[0][price]" class="ui-input" data-app-form-validate="required" />
                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.0.price"]) ?>
                                                                </div>
                                                            </td>

                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="ui-table-box-body-item-editable-input-container">
                                                                    <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Production Operation')."'":''?> class="ui-input sub-production-routings-select" name="materials[0][sub_production_routing_id]" data-app-form-select="true"  >
                                                                    </select>
                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "materials.0.sub_production_routing_id"]) ?>
                                                                </div>
                                                            </td>
                                                            <td class="subform-cell-text border-start" data-col-subtotal="materials" >
                                                                <span class="val">0</span>
                                                            </td>
                                                            <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="materials-subform">
                                                                    <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endif;?>
                                               
                                                </tbody>
                                                <tfoot>
                                                    <?php if (!IS_MOBILE && !IS_TABLET) { ?> 
                                                        <tr class="d-lg-table-row d-none">
                                                            <td colspan="3" valign="top" style="padding: 0">
                                                                <div class="l-btn-group-box">
                                                                    <div class="l-btn-group l-btn-group--separated" role="group">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="materials-subform">
                                                                            <span class="ui-btn-inner-content">
                                                                                <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                <span class="ui-btn-inner-text u-text-color-black"><?=__t('Add')?></span>
                                                                            </span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </td>
                                                            <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-col-total="materials">
                                                                <span class="val"><?=$fillform?$data->materials_total_cost??0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                    <!-- Mobile row -->
                                                    <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                        <tr class="ui-table-box-row d-lg-none">
                                                            <td valign="top" class="border-bottom">
                                                                <div class="d-flex justify-content-between full-height">
                                                                    <div class="btn-group gap-1">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="materials-subform">
                                                                            <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                        </button>
                                                                    </div>
                                                                    <div class="d-lg-flex d-none align-items-center px-10 border-start border-bottom subform-cell-text text-dark-3 bg-light">
                                                                        <?=__t('Subtotal')?>
                                                                    </div>
                                                                </div>
                                                            </td>

                                                            <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-col-total="materials">
                                                                <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                        <?= __t("Subtotal") ?>
                                                                </div>
                                                                <span class="val"><?=$fillform?$data->materials_total_cost?? 0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                </tfoot>
                                            </table>
                                            <template data-app-form-subform-row-template="materials-subform">
                                                <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                        <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Product')."'":''?>  name="materials[__index__][product_id]" id="filter-product" class="l-input ui-input material-select-product" data-qty-input-name="materials[__index__][unit_factor_data]" data-filter-select="materials-qty-unit[0]" data-app-form-select-template="ajax-simple-3" data-app-form-validate="required">
                                                                <option value=""></option>
                                                            </select>
                                                        </div>
                                                    </td>
                                                 
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="d-flex ui-table-box-body-item-group" data-qty-type-select="materials-qty-unit[0]">
                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('QTY')."'":''?> type="text" data-product-qty class="l-input ui-input" name="materials[__index__][quantity][quantity]" value="" data-app-form-validate="required" data-app-form-number="true" />
                                                            </div>
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Price')."'":''?> type="text" data-product-price readonly="readonly" style="cursor: default;" name="materials[__index__][price]" class="ui-input" data-app-form-validate="required" />
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Production Operation')."'":''?> class="ui-input sub-production-routings-select" name="materials[__index__][sub_production_routing_id]" data-app-form-select="true">
                                                            </select>
                                                        </div>
                                                    </td>
                                                    <td class="subform-cell-text border-start" data-col-subtotal="materials" >
                                                            <span class="val">0</span>
                                                    </td>
                                                    <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                        <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="materials-subform">
                                                            <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'materials']) ?>

                        </div>

                        <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>
                        <div class="l-show-card-section-title-box">
                            <div class="l-flex-row">
                                <div class="l-flex-col-12">
                                    <div class="accordion-collapse">
                                        <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='expenses']" aria-expanded="<?=$fillform?($form->getFieldSets()['expenses']->getData()?? [] ? 'true':'false ') :"false"?>" aria-controls="expenses">
                                            <div class="l-flex-row">
                                                <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                    <span class="accordion-collapse-title">
                                                        <i class="mdi mdi-cash-multiple"></i>
                                                        <span><?=__t('Expenses')?> (<span class="collapse-qty"><?=$fillform?count($form->getFieldSets()['expenses']->getData()?? []) :'0'?></span>)</span>
                                                    </span>
                                                </span>
                                                <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                    <div aria-expanded="false" style="margin-top: 5px;">
                                                        <div class="l-flex-row ">
                                                            <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </div>
                                                            <div class="l-flex-col-12 l-flex-col-lg-2" data-col-total="expenses">
                                                                <span class="val"><?=$fillform?$data->expenses_total_cost??0:'0'?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </span>
                                                <span class="l-flex-col-1 text-end px-0">
                                                    <span class="accordion-collapse-arrow">
                                                        <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                        <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="collapse <?=$fillform?($form->getFieldSets()['expenses']->getData()?? [] ? 'show':'') :""?>" data-accordion-collapse='expenses'>
                                <div class="l-flex-row">
                                    <div class="l-flex-col-lg-12">
                                        <div class="l-table-box-wrapper">
                                            <table class="l-table-box  subform-table " data-app-form-subform="expenses-subform" data-app-form-subform-options='{ "sortable": false }'>
                                                <thead>
                                                    <tr class="ui-table-box-row">
                                                        <th width="1000"><?=__t('Account')?></th>
                                                        <th width="1000"><?=__t('Cost Type')?></th>
                                                        <th width="500"><?=__t('Amount')?></th>
                                                        <th width="700"><?=__t('Description')?></th>
                                                        <th width="500"><?=__t('Production Operation')?></th>
                                                        <th width="600"><?=__t('Subtotal')?></th>
                                                        <th width="50"></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                <?php if(($form->getFieldSets()['expenses']->getData() ?? []) || $fillform):?>
                                                    <?php foreach($form->getFieldSets()['expenses']->getData() ?? [] as $i => $expense): ?>
                                                            <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <?php if(isset($expense['id'])):?>
                                                                            <input type="hidden" name="expenses[<?=$i?>][id]" value="<?=$expense['id']?>" id="id">
                                                                        <?php endif;?>
                                                                         <select class="account-select2 l-input ui-input" name="expenses[<?=$i?>][journal_account_id]" data-app-form-validate="required" >
                                                                            <?php initSelectOption($form->getFieldSets()['expenses']->getFieldSets()[$i]->getElements()['journal_account_id']->getValueOptions()[0]?? []); ?>
                                                                        </select>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$i.journal_account_id"]) ?>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <div class="input-container">
                                                                            <div class="form-group" data-form-group="true">
                                                                                <select class="ui-input" data-select-formula-type="expenses" name="expenses[<?=$i?>][cost_type]" data-select-type="true" data-app-form-select="true" data-form-rules="required" data-app-form-validate="required">
                                                                                    <option value=""></option>
                                                                                    <option <?=$expense['cost_type'] == 'fixed_amount'?'selected':''?> value="fixed_amount" selected><?=__t('Fixed Amount')?></option>
                                                                                    <option <?=$expense['cost_type'] == 'based_on_qty'?'selected':''?> value="based_on_qty"><?=__t('Based on QTY')?></option>
                                                                                    <option <?=$expense['cost_type'] == 'equation'?'selected':''?> value="equation"><?=__t('Equation')?></option>
                                                                                </select>
                                                                                <div class="form-validation" data-form-validation="true"></div>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$i.cost_type"]) ?>
                                                                            </div>
                                                                        </div>
                                                                    

                                                                        <div class="input-container" data-placeholder-input="true" disabled style="display: none;">
                                                                            <div class="form-group" data-form-group="true">
                                                                                <div class="input-placeholder-container">
                                                                                    <button type="button" class="input-group-text input-group-icon-end placeholder-tooltip-btn" data-bs-toggle="tooltip" data-placement="top" title="View all variables" data-placeholder-btn="true">
                                                                                        <i class="mdi mdi-tag-outline fs-13 text-black"></i>
                                                                                    </button>
                                                                                    <textarea class="form-control placeholder-input" name="expenses[<?=$i?>][formula]" rows="1" data-form-rules="required" data-placeholder-text-input="subform" data-placeholder-url="/v2/api/placeholders/bom_expense/equation"><?=$expense['formula']??''?></textarea>
 
                                                                                    <div class="input-placeholder-tooltip" data-placeholder-tooltip="id-0">
                                                                                        <div class="input-placeholder-tooltip-body"></div>
                                                                                    </div>
                                                                                    <div class="form-validation" data-form-validation="true"></div>
                                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$i.formula"]) ?>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                            
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input type="text" data-row-factor name="expenses[<?=$i?>][amount]"  data-app-form-number="true"  value="<?=$expense['amount']?>"class="ui-input"  data-app-form-validate="required" />
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$i.amount"]) ?>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <textarea <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Description')."'":''?> rows="1" name="expenses[<?=$i?>][description]" class="ui-input"><?=$expense['description']??''?></textarea>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$i.description"]) ?>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <select class="ui-input sub-production-routings-select" name="expenses[<?=$i?>][sub_production_routing_id]" data-app-form-select="true">
                                                                            <option value=""></option>
                                                                            <?php initSelectOption($form->getFieldSets()['expenses']->getFieldSets()[$i]->getElements()['sub_production_routing_id']->getValueOptions()[0]?? [], true); ?>
                                                                            <?php foreach($subProductionRoutings as $subProductionRouting):?>
                                                                                <option value="<?= $subProductionRouting['id'] ?>"><?= $subProductionRouting['name'] ?></option>
                                                                            <?php endforeach;?>    
                                                                        </select>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "expenses.$i.sub_production_routing_id"]) ?>
                                                                    </div>
                                                                </td>
                                                                <td class="subform-cell-text border-start" data-col-subtotal="expenses">
                                                                    <span class="val">0</span>
                                                                </td>
                                                                <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                    <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="expenses-subform">
                                                                        <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>

                                                    <?php endif; ?>
                                                </tbody>
                                                <tfoot>
                                                    <?php if (!IS_MOBILE && !IS_TABLET) { ?>
                                                        <tr class="d-lg-table-row d-none">
                                                            <td colspan="4" valign="top" style="padding: 0">
                                                                <div class="l-btn-group-box">
                                                                    <div class="l-btn-group l-btn-group--separated" role="group">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="expenses-subform">
                                                                            <span class="ui-btn-inner-content">
                                                                                <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                <span class="ui-btn-inner-text u-text-color-black"><?=__t('Add')?></span>
                                                                            </span>
                                                                        </button>

                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </td>
                                                            <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-col-total="expenses">
                                                                <span class="val"><?=$fillform?$data->expenses_total_cost??0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                    <!-- Mobile row -->
                                                    <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                        <tr class="ui-table-box-row d-lg-none">
                                                            <td valign="top" class="border-bottom">
                                                                <div class="d-flex justify-content-between full-height">
                                                                    <div class="btn-group gap-1">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="expenses-subform">
                                                                            <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                        </button>
                                                                    </div>
                                                                    <div class="d-lg-flex d-none align-items-center px-10 border-start border-bottom subform-cell-text text-dark-3 bg-light">
                                                                        <?=__t('Subtotal')?>
                                                                    </div>
                                                                </div>
                                                            </td>

                                                            <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-col-total="expenses">
                                                                <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                        <?= __t("Subtotal") ?>
                                                                </div>
                                                                <span class="val"><?=$fillform?$data->expenses_total_cost??0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                </tfoot>
                                            </table>
                                            <template data-app-form-subform-row-template="expenses-subform">
                                                <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Account')."'":''?> class="account-select4 l-input ui-input" name="expenses[__index__][journal_account_id]" data-app-form-validate="required" >
                                                            </select>
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <div class="input-container">
                                                                <div class="form-group" data-form-group="true">
                                                                        <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Cost Type')."'":''?> class="ui-input" data-select-formula-type="expenses" name="expenses[__index__][cost_type]" data-select-type="true" data-app-form-select="true" data-form-rules="required" data-app-form-validate="required">
                                                                            <option value=""></option>
                                                                            <option value="fixed_amount" selected><?=__t('Fixed Amount')?></option>
                                                                            <option value="based_on_qty"><?=__t('Based on QTY')?></option>
                                                                            <option value="equation"><?=__t('Equation')?></option>
                                                                        </select>
                                                                    <div class="form-validation" data-form-validation="true"></div>
                                                                </div>
                                                            </div>
                                                          

                                                            <div class="input-container" data-placeholder-input="true" disabled style="display: none;">
                                                                <div class="form-group" data-form-group="true">
                                                                    <div class="input-placeholder-container">
                                                                        <button type="button" class="input-group-text input-group-icon-end placeholder-tooltip-btn" data-bs-toggle="tooltip" data-placement="top" title="View all variables" data-placeholder-btn="true">
                                                                            <i class="mdi mdi-tag-outline fs-13 text-black"></i>
                                                                        </button>
                                                                        <textarea class="form-control placeholder-input" name="expenses[__index__][formula]" rows="1" data-form-rules="required" data-placeholder-text-input="subform" data-placeholder-url="/v2/api/placeholders/bom_expense/equation"></textarea>
 
                                                                        <div class="input-placeholder-tooltip" data-placeholder-tooltip="id-0">
                                                                            <div class="input-placeholder-tooltip-body"></div>
                                                                        </div>
                                                                        <div class="form-validation" data-form-validation="true"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                           
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Amount')."'":''?> type="text" data-row-factor name="expenses[__index__][amount]" data-app-form-number="true"class="ui-input"  data-app-form-validate="required" />
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <textarea <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Description')."'":''?> rows="1" name="expenses[__index__][description]" class="ui-input" ></textarea>
                                                        </div>
                                                    </td>


                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Production Operation')."'":''?> class="ui-input sub-production-routings-select" name="expenses[__index__][sub_production_routing_id]"  data-app-form-select="true"  >
                                                            </select>
                                                        </div>
                                                    </td>
                                                    <td class="subform-cell-text border-start" data-col-subtotal="expenses">
                                                        <span class="val">0</span>
                                                    </td>
                                                    <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                        <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="expenses-subform">
                                                            <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>
                        <div class="l-show-card-section-title-box">
                            <div class="l-flex-row">
                                <div class="l-flex-col-12">
                                    <div class="accordion-collapse">
                                        <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='manufacturing-operations']" aria-expanded="<?=$fillform?($form->getFieldSets()['operations']->getData()?? [] ? 'true':'false') :"false"?>" aria-controls="expenses">
                                            <div class="l-flex-row">
                                                <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                    <span class="accordion-collapse-title">
                                                        <i class="mdi mdi-cog"></i>
                                                        <span><?=__t('Manufacturing Operations')?> (<span class="collapse-qty"><?=$fillform?count($form->getFieldSets()['operations']->getData()??[]):'0'?></span>)</span>
                                                    </span>
                                                </span>
                                                <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                    <div aria-expanded="false" style="margin-top: 5px;">
                                                        <div class="l-flex-row ">
                                                            <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </div>
                                                            <div class="l-flex-col-12 l-flex-col-lg-2" data-col-total="manufacturing-operations">
                                                                <span class="val"><?=$fillform?$data->operations_total_cost??0:'0'?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </span>
                                                <span class="l-flex-col-1 text-end px-0">
                                                    <span class="accordion-collapse-arrow">
                                                        <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                        <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="collapse <?=$fillform?($form->getFieldSets()['operations']->getData()?? [] ? 'show':'') :""?>" data-accordion-collapse='manufacturing-operations'>
                                <div class="l-flex-row">
                                    <div class="l-flex-col-lg-12">
                                        <div class="l-table-box-wrapper">
                                            <table class="l-table-box  subform-table " data-app-form-subform="manufacturing-operations-subform" data-app-form-subform-options='{ "sortable": false }'>
                                                <thead>
                                                    <tr class="ui-table-box-row">
                                                        <th width="1000"><?=__t('Workstation')?></th>
                                                        <th width="1000"><?=__t('Cost Type')?></th>
                                                        <th width="500"><?=__t('Operation Time')?></th>
                                                        <th width="700"><?=__t('Description')?></th>
                                                        <th width="500"><?=__t('Production Operation')?></th>
                                                        <th width="600"><?=__t('Subtotal')?></th>
                                                        <th width="50"></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if(($form->getFieldSets()['operations']->getData() ?? []) || $fillform):?>
                                                        <?php foreach($form->getFieldSets()['operations']->getData() ?? [] as $i => $operation): ?>

                                                            <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <?php if(isset($operation['id'])):?>
                                                                            <input type="hidden" name="operations[<?=$i?>][id]" value="<?=$operation['id']?>" id="id">
                                                                        <?php endif;?>
                                                                        <select class="workstations-select ui-input" data-select-workstation name="operations[<?=$i?>][workstation_id]" data-app-form-select="true" data-app-form-validate="required" data-app-form-select-template="ajax-simple-3" >
                                                                            <option value=""></option>
                                                                            <?php initSelectOption($form->getFieldSets()['operations']->getFieldSets()[$i]->getElements()['workstation_id']->getValueOptions()[0]?? []); ?>
                                                                        </select>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$i.workstation_id"]) ?>
                                                                    </div>
                                                                </td>
                                                                <input type="hidden" data-ws-total-cost value="<?=  $relatedWorkstations[$data['operations'][$i]['workstation_id']]['total_cost'] ?? 0?>" >
                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <div class="input-container">
                                                                            <div class="form-group" data-form-group="true">
                                                                                <select class="ui-input" data-select-formula-type="manufacturing-operations" name="operations[<?=$i?>][cost_type]" data-select-type="true" data-app-form-select="true" data-form-rules="required" data-app-form-validate="required">
                                                                                    <option value=""></option>
                                                                                    <option <?=$operation['cost_type'] == 'fixed_amount'?'selected':''?> value="fixed_amount" selected><?=__t('Fixed Amount')?></option>
                                                                                    <option <?=$operation['cost_type'] == 'based_on_qty'?'selected':''?> value="based_on_qty"><?=__t('Based on QTY')?></option>
                                                                                    <option <?=$operation['cost_type'] == 'equation'?'selected':''?> value="equation"><?=__t('Equation')?></option>
                                                                                </select>
                                                                                <div class="form-validation" data-form-validation="true"></div>
                                                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$i.cost_type"]) ?>
                                                                            </div>
                                                                        </div>
                                                                        
                                                                        <div class="input-container" data-placeholder-input="true" disabled style="display: none;">
                                                                            <div class="form-group" data-form-group="true">
                                                                                <div class="input-placeholder-container">
                                                                                    <button type="button" class="input-group-text input-group-icon-end placeholder-tooltip-btn" data-bs-toggle="tooltip" data-placement="top" title="View all variables" data-placeholder-btn="true">
                                                                                        <i class="mdi mdi-tag-outline fs-13 text-black"></i>
                                                                                    </button>
                                                                                    <textarea class="form-control placeholder-input" name="operations[<?=$i?>][formula]" rows="1" data-form-rules="required" data-placeholder-text-input="subform" data-placeholder-url="/v2/api/placeholders/bom_operation/equation"><?=$operation['formula']??''?></textarea>
 
                                                                                    <div class="input-placeholder-tooltip" data-placeholder-tooltip="id-0">
                                                                                        <div class="input-placeholder-tooltip-body"></div>
                                                                                    </div>
                                                                                    <div class="form-validation" data-form-validation="true"></div>
                                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$i.formula"]) ?>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input type="text" data-row-factor name="operations[<?=$i?>][operating_time]" data-app-form-number="true"  value="<?=$operation['operating_time']?>" class="ui-input"  data-app-form-validate="required" />
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$i.operating_time"]) ?>
                                                                    </div>
                                                                </td>

                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <textarea <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Description')."'":''?> rows="1" name="operations[<?=$i?>][description]" class="ui-input" ><?=$operation['description']??''?></textarea>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$i.description"]) ?>
                                                                    </div>
                                                                </td>


                                                                <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <select class="ui-input sub-production-routings-select" name="operations[<?=$i?>][sub_production_routing_id]"  data-app-form-select="true" >
                                                                        <?php initSelectOption($form->getFieldSets()['operations']->getFieldSets()[$i]->getElements()['sub_production_routing_id']->getValueOptions()[0]?? [],true); ?>
                                                                            <?php foreach($subProductionRoutings as $subProductionRouting):?>
                                                                                    <option value="<?= $subProductionRouting['id'] ?>"><?= $subProductionRouting['name'] ?></option>
                                                                            <?php endforeach;?>
                                                                        </select>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "operations.$i.sub_production_routing_id"]) ?>
                                                                    </div>
                                                                </td>
                                                                <td class="subform-cell-text border-start" data-col-subtotal="manufacturing-operations">
                                                                    <span class="val"><?=$operation->sub_total_cost ?? 0?></span>
                                                                </td>
                                                                <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                    <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="manufacturing-operations-subform">
                                                                        <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </tbody>
                                                <tfoot>
                                                    <?php if (!IS_MOBILE && !IS_TABLET) { ?>
                                                        <tr class="d-lg-table-row d-none">
                                                            <td colspan="4" valign="top" style="padding: 0">
                                                                <div class="l-btn-group-box">
                                                                    <div class="l-btn-group l-btn-group--separated" role="group">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="manufacturing-operations-subform">
                                                                            <span class="ui-btn-inner-content">
                                                                                <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                <span class="ui-btn-inner-text u-text-color-black"><?=__t('Add')?></span>
                                                                            </span>
                                                                        </button>

                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </td>
                                                            <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-col-total="manufacturing-operations">
                                                                <span class="val"><?=$fillform?$data->operations_total_cost??0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                    <!-- Mobile row -->
                                                    <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                        <tr class="ui-table-box-row d-lg-none">
                                                            <td valign="top" class="border-bottom">
                                                                <div class="d-flex justify-content-between full-height">
                                                                    <div class="btn-group gap-1">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="manufacturing-operations-subform">
                                                                            <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                        </button>
                                                                    </div>
                                                                    <div class="d-lg-flex d-none align-items-center px-10 border-start border-bottom subform-cell-text text-dark-3 bg-light">
                                                                        <?=__t('Subtotal')?>
                                                                    </div>
                                                                </div>
                                                            </td>

                                                            <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-col-total="manufacturing-operations">
                                                                <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                        <?= __t("Subtotal") ?>
                                                                </div>
                                                                <span class="val"><?=$fillform?$data->operations_total_cost??0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                </tfoot>
                                            </table>
                                            <template data-app-form-subform-row-template="manufacturing-operations-subform">
                                                <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Workstation')."'":''?> class="workstations-select ui-input"  data-select-workstation name="operations[__index__][workstation_id]"  data-app-form-select="true" data-app-form-validate="required" data-app-form-select-template="ajax-simple-3" >
                                                                <option value=""></option>
                                                            </select>
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <div class="input-container">
                                                                <div class="form-group" data-form-group="true">
                                                                    <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Cost Type')."'":''?> class="ui-input" data-select-formula-type="manufacturing-operations" name="operations[__index__][cost_type]"  data-select-type="true" data-app-form-select="true" data-form-rules="required" data-app-form-validate="required">
                                                                        <option value=""></option>
                                                                        <option value="fixed_amount" selected><?=__t('Fixed Amount')?></option>
                                                                        <option value="based_on_qty"><?=__t('Based on QTY')?></option>
                                                                        <option value="equation"><?=__t('Equation')?></option>
                                                                    </select>
                                                                    <div class="form-validation" data-form-validation="true"></div>
                                                                </div>
                                                            </div>
                                                             

                                                            <div class="input-container" data-placeholder-input="true" disabled style="display: none;">
                                                                <div class="form-group" data-form-group="true">
                                                                    <div class="input-placeholder-container">
                                                                        <button type="button" class="input-group-text input-group-icon-end placeholder-tooltip-btn" data-bs-toggle="tooltip" data-placement="top" title="View all variables" data-placeholder-btn="true">
                                                                            <i class="mdi mdi-tag-outline fs-13 text-black"></i>
                                                                        </button>
                                                                        <textarea class="form-control placeholder-input" name="operations[__index__][formula]" rows="1" data-form-rules="required" data-placeholder-text-input="subform" data-placeholder-url="/v2/api/placeholders/bom_operation/equation"></textarea>
 
                                                                        <div class="input-placeholder-tooltip" data-placeholder-tooltip="id-0">
                                                                            <div class="input-placeholder-tooltip-body"></div>
                                                                        </div>
                                                                        <div class="form-validation" data-form-validation="true"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                           
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Operation Time')."'":''?> type="text" data-row-factor name="operations[__index__][operating_time]" data-app-form-number="true"class="ui-input"  data-app-form-validate="required" />
                                                        </div>
                                                    </td>

                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <textarea <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Description')."'":''?> rows="1" name="operations[__index__][description]" class="ui-input" ></textarea>
                                                        </div>
                                                    </td>


                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Production Operation')."'":''?> class="ui-input sub-production-routings-select" name="operations[__index__][sub_production_routing_id]"  data-app-form-select="true" >
                                                            </select>
                                                        </div>
                                                    </td>
                                                    <td class="subform-cell-text border-start" data-col-subtotal="manufacturing-operations">
                                                        <span class="val">0</span>
                                                    </td>
                                                    <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                        <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="manufacturing-operations-subform">
                                                            <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>
                        <div class="l-show-card-section-title-box">
                            <div class="l-flex-row">
                                <div class="l-flex-col-12">
                                    <div class="accordion-collapse">
                                        <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='scrap-items']" aria-expanded="<?=$fillform?($form->getFieldSets()['scraps']->getData()?? [] ? 'true':'false') :"false"?>" aria-controls="scrap-items">
                                            <div class="l-flex-row">
                                                <span class="l-flex-col-lg-7 l-flex-col-6 text-start">
                                                    <span class="accordion-collapse-title">
                                                        <i class="mdi mdi-basket-fill"></i>
                                                        <span><?=__t('Scrap Items')?> (<span class="collapse-qty"><?=$fillform?count($form->getFieldSets()['scraps']->getData()??[]):'0'?></span>)</span>
                                                    </span>
                                                </span>
                                                <span class="l-flex-col-lg-4 l-flex-col-5 align-items-center">
                                                    <div aria-expanded="false" style="margin-top: 5px;">
                                                        <div class="l-flex-row ">
                                                            <div class="l-flex-col-10 d-none d-lg-inline-block text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </div>
                                                            <div class="l-flex-col-12 l-flex-col-lg-2" data-col-total="scrap-items">
                                                                <span class="val"><?=$fillform?$data->scraps_total_cost??0:'0'?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </span>
                                                <span class="l-flex-col-1 text-end px-0">
                                                    <span class="accordion-collapse-arrow">
                                                        <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                        <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="collapse <?=$fillform?($form->getFieldSets()['scraps']->getData()?? [] ? 'show':'') :""?>" data-accordion-collapse='scrap-items'>
                                <div class="l-flex-row">
                                    <div class="l-flex-col-lg-12">
                                        <div class="l-table-box-wrapper">
                                            <table class="l-table-box  subform-table " data-app-form-subform="scrap-items-subform" data-app-form-subform-options='{ "sortable": false }'>
                                                <thead>
                                                    <tr class="ui-table-box-row">
                                                        <th width="1000"><?=__t('Product')?></th>
                                                        <th width="1000"><?=__t('QTY')?></th>
                                                        <th width="500"><?=__t('Price')?></th>
                                                        <th width="1000"><?=__t('Production Operation')?></th>
                                                        <th width="600"><?=__t('Subtotal')?></th>
                                                        <th width="50"></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                <?php if(($form->getFieldSets()['scraps']->getData() ?? []) || $fillform):?>
                                                    <?php foreach($form->getFieldSets()['scraps']->getData() ?? [] as $i => $scrap): ?>
                                                        <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="ui-table-box-body-item-editable-input-container">
                                                                    <?php if(isset($scrap['id'])):?>
                                                                        <input type="hidden" name="scraps[<?=$i?>][id]" value="<?=$scrap['id']?>" id="id">
                                                                    <?php endif;?>
                                                                    <select name="scraps[<?=$i?>][product_id]" id="filter-product" class="scrap-product-select l-input ui-input" data-qty-input-name="scraps[<?=$i?>][unit_factor_data]" data-filter-select="materials-qty-unit[0]" data-app-form-select-template="ajax-simple-3" data-app-form-validate="required">
                                                                        <option value=""></option>
                                                                        <?php initSelectOption($form->getFieldSets()['scraps']->getFieldSets()[$i]->getElements()['product_id']->getValueOptions()[0]?? []); ?>
                                                                    </select>
                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$i.product_id"]) ?>
                                                                </div>
                                                            </td>
                                                          
                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="d-flex ui-table-box-body-item-group" data-qty-type-select="materials-qty-unit[0]">
                                                                    <div class="ui-table-box-body-item-editable-input-container">
                                                                        <input type="text" data-product-qty class="l-input ui-input" name="scraps[<?=$i?>][quantity][quantity]" value="<?=handleSubformProductQty($scrap, $productsMapById)?>" data-app-form-validate="required" data-app-form-number="true" placeholder="<?=__t('QTY')?>"/>
                                                                        <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$i.quantity.quantity"]) ?>
                                                                    </div>
                                                                </div>
                                                                <?php
                                                                    echo initQtyunitFactorSelection($form->getFieldSets()['scraps']->getFieldSets()[$i]->getFieldSets()['quantity']->getElements()['unit_factor_id'], "scraps[$i][unit_factor_data]", $data['scraps'][$i], $productsMapById[$scrap["product_id"]]->unit_template_id??null);
                                                                ?>
                                                            </td>

                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="ui-table-box-body-item-editable-input-container">
                                                                    <input type="text" data-product-price name="scraps[<?=$i?>][price]" value="<?=$scrap['price']?>" class="ui-input" data-app-form-validate="required" />
                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$i.price"]) ?>
                                                                </div>
                                                            </td>

                                                            <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                                <div class="ui-table-box-body-item-editable-input-container">
                                                                    <select class="ui-input sub-production-routings-select" name="scraps[<?=$i?>][sub_production_routing_id]" data-app-form-select="true"  >
                                                                        <option value=""></option>
                                                                        <?php initSelectOption($form->getFieldSets()['scraps']->getFieldSets()[$i]->getElements()['sub_production_routing_id']->getValueOptions()[0]?? [],true); ?>
                                                                        <?php foreach($subProductionRoutings as $subProductionRouting):?>
                                                                            <option value="<?= $subProductionRouting['id'] ?>"><?= $subProductionRouting['name'] ?></option>
                                                                        <?php endforeach;?>    
                                                                    </select>
                                                                    <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => "scraps.$i.sub_production_routing_id"]) ?>
                                                                </div>
                                                            </td>
                                                            <td class="subform-cell-text border-start" data-col-subtotal="scrap-items">
                                                                <span class="val">0</span>
                                                            </td>
                                                            <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                                <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="scrap-items-subform">
                                                                    <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach;?>
                                                    <?php endif;?>

                                                </tbody>
                                                <tfoot>
                                                    <?php if (!IS_MOBILE && !IS_TABLET) { ?>  
                                                        <tr class="d-lg-table-row d-none">
                                                            <td colspan="3" valign="top" style="padding: 0">
                                                                <div class="l-btn-group-box">
                                                                    <div class="l-btn-group l-btn-group--separated" role="group">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="scrap-items-subform">
                                                                            <span class="ui-btn-inner-content">
                                                                                <i class="ui-btn-inner-icon ui-icon--size-16 mdi mdi-plus-thick"></i>
                                                                                <span class="ui-btn-inner-text u-text-color-black"><?=__t('Add')?></span>
                                                                            </span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="border-start border-bottom subform-cell-text text-dark-3">
                                                                <?=__t('Subtotal')?>
                                                            </td>
                                                            <td class="border-end border-bottom subform-cell-text fw-medium" colspan="2" data-col-total="scrap-items">
                                                                <span class="val"><?=$fillform?$data->scraps_total_cost??0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                    <!-- Mobile row -->
                                                    <?php if (IS_MOBILE || IS_TABLET) { ?>
                                                        <tr class="ui-table-box-row d-lg-none">
                                                            <td valign="top" class="border-bottom">
                                                                <div class="d-flex justify-content-between full-height">
                                                                    <div class="btn-group gap-1">
                                                                        <button type="button" class="ui-table-add-btn l-btn-inline l-btn-group-btn ui-btn u-bg-color-secondary u-text-color-success u-text-hover-color-success" data-app-form-subform-add-btn="scrap-items-subform">
                                                                            <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                        </button>
                                                                    </div>
                                                                    <div class="d-lg-flex d-none align-items-center px-10 border-start border-bottom subform-cell-text text-dark-3 bg-light">
                                                                        <?=__t('Subtotal')?>
                                                                    </div>
                                                                </div>
                                                            </td>

                                                            <td class="border-end border-bottom subform-cell-text fw-medium d-flex align-items-center justify-content-between border-md-start" colspan="2" data-col-total="scrap-items">
                                                                <div class="d-flex d-lg-none align-items-center subform-cell-text text-dark-3 bg-light">
                                                                        <?= __t("Subtotal") ?>
                                                                </div>
                                                                <span class="val"><?=$fillform?$data->scraps_total_cost??0:'0'?></span>
                                                            </td>
                                                        </tr>
                                                    <?php } ?>
                                                </tfoot>
                                            </table>
                                            <template data-app-form-subform-row-template="scrap-items-subform">
                                                <tr class="ui-table-box-row" data-app-form-subform-row="true">
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Product')."'":''?> name="scraps[__index__][product_id]" id="filter-product" class="scrap-product-select l-input ui-input"  data-qty-input-name="scraps[__index__][unit_factor_data]" data-filter-select="materials-qty-unit[__index__]" data-app-form-select-template="ajax-simple-3" data-app-form-validate="required">
                                                                <option value=""></option>
                                                            </select>
                                                        </div>
                                                    </td>
                                                  
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="d-flex ui-table-box-body-item-group" data-qty-type-select="materials-qty-unit[__index__]">
                                                            <div class="ui-table-box-body-item-editable-input-container">
                                                                <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('QTY')."'":''?> type="text" data-product-qty class="l-input ui-input" name="scraps[__index__][quantity][quantity]" value="" data-app-form-validate="required" data-app-form-number="true" />
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <input <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Price')."'":''?> type="text" data-product-price  name="scraps[__index__][price]" class="ui-input"  data-app-form-validate="required" />
                                                        </div>
                                                    </td>
                                                    <td class="ui-table-box-body-item ui-table-box-head-item--border-end ui-table-box-body-item--editable u-text-color-black">
                                                        <div class="ui-table-box-body-item-editable-input-container">
                                                            <select <?=IS_MOBILE || IS_TABLET? "placeholder='".__t('Production Operation')."'":''?> class="ui-input sub-production-routings-select" name="scraps[__index__][sub_production_routing_id]" data-app-form-select="true" >
                                                            </select>
                                                        </div>
                                                    </td>
                                                    <td class="subform-cell-text" data-col-subtotal="scrap-items">
                                                        <span class="val">0</span>
                                                    </td>
                                                    <td class="ui-table-box-body-item ui-table-box-body-item--removeable u-text-align-center">
                                                        <button class="ui-btn-simple ui-table-box-body-item-removeable-btn u-bg-color-light u-bg-hover-color-danger u-text-color-danger u-text-hover-color-white border-start" type="button" data-app-form-subform-row-removable-btn="scrap-items-subform">
                                                            <i class="ui-icon mdi mdi-trash-can ui-icon--size-20"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <span class="l-h-line" style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>
                        <div class="l-show-card-section-title-box">
                            <div class="l-flex-row">
                                <div class="l-flex-col-lg-6"></div>
                                <div class="l-flex-col-lg-6">
                                    <div class="total-cell" data-col-total-cost="true">
                                        <span><?=__t('Total Cost')?>:</span>
                                        <span><span class="val"><?=$fillform?$data->total_cost??0:'0'?></span> <?=get_currency_formatted()?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


<?php $this->section('page-start') ?>
<form id="bom_create_form" class="m-app-form" action="<?=$formUrl?>" method="POST" data-app-form="bom">
<?php $this->endSection() ?>
<?php $this->section('page-end') ?>
</form>
<?php $this->endSection() ?>

<?php $this->section('page-after-js') ?>
<script>
    var jQuery = $;
</script>
 <script src="/v2/js/account-select.js"></script>
<script src="<?=getCdnAssetsUrl()?>js/forms/plugin_select2.js"></script>
<script type="text/javascript" src="/js/functions.js"></script>


    <script>
        var currencies = <?php echo json_encode(include app_path('Helpers/currencies.php')) ?>;
        var number_formats = <?php echo json_encode($number_formats) ?>;
        var country_code = '<?php echo getCurrentSite('country_code'); ?>';
        var currency_code = '<?php echo getCurrentSite('currency_code'); ?>';

        var bomCreateForm = $("#bom_create_form")
        var subProductionRoutesOptions = [];
        var defaultExpenxeEquation = "({{$manufacturing_order.quantity}} / {{$bom.quantity}} )* {{$bom_expense.amount}}";
        var defaultOperationsEquation = "({{$manufacturing_order.quantity}} /{{$bom.quantity}} )*{{$bom_operation.operating_time}}";
        var subProductionRoutingsSelect = $(".sub-production-routings-select");
        function initSubProductionRoutingSelect(){
            $(".sub-production-routings-select").selectize({
                maxItems: 1,
                options: subProductionRoutesOptions,
                placeholder: "<?=__t('Production Operation')?>" 
            });
            $(".sub-production-routings-select .selectize-dropdown").css("height", "auto");
            $('.sub-production-routings-select ').val('')
            for(let i = 0; i < subProductionRoutingsSelect.length ; i++){
                $(subProductionRoutingsSelect[i]).selectize()[0].selectize.clear()
            }
        }
        function loadSubProductionRoutesOptions(productionRouting, withInit = true){
            $.ajax({
                url: "/v2/owner/production_routing/get_sub_production_routing_by_main_route?mainProductionRouteId="+productionRouting,
                type: "GET",
                success: function(res) {
                    for (let j = 0; j < res.length; j++) {
                        subProductionRoutesOptions.push({
                            id: res[j].id,
                            value: res[j].id,
                            name: res[j].name,
                            text: res[j].name
                        });
                    }

                    if(withInit){
                        setTimeout(() => {
                            initSubProductionRoutingSelect()
                        }, 0);
                    }
                },
            });
        }

        function initPlaceholderDefaultFormula(elementRef){
            element = $(elementRef)
            let moduleName = element.closest('table').attr('data-app-form-subform')
            let placholderInput = element.closest("tr").find('textarea[data-placeholder-text-input]')

            if(!placholderInput.val()){
                if( moduleName == "expenses-subform"){
                    placholderInput.val(defaultExpenxeEquation);
                }else if(moduleName == "manufacturing-operations-subform"){
                    placholderInput.val(defaultOperationsEquation);
                }
            }
        }

        window.addEventListener('load', function() {
            setTimeout(function() {
                bomCreateForm.ready(function() {
                    for(var i = 0; i < $('[data-placeholder-text-input]').length; i++) {
                        $('[data-placeholder-text-input]').eq(i).inputPlaceholder();
                    }
                    <?php if($fillform && $data['production_routing_id']):?>
                        loadSubProductionRoutesOptions(<?=$data['production_routing_id']?>, false)
                    <?php endif;?>
                    $("[data-select-formula-type]").trigger('change')
                    $("[data-product-qty]").trigger('input')
                });
            }, 50);
        });      

      
            $('.account-select').initJournalAccountSelect(); 
            $('.account-select2').initJournalAccountSelect();
            $('.account-select3').initJournalAccountSelect();
            $('.account-select4').initJournalAccountSelect();
        bomCreateForm.ready(function() {
            $('.account-select').initJournalAccountSelect(); 
            $('.account-select2').initJournalAccountSelect();
            $('.account-select3').initJournalAccountSelect();
            $('.account-select4').initJournalAccountSelect();
            var $periodInput = $("[data-filter-select]");
  
            // handle show unit beside qty input if selected product has unit template
            bomCreateForm.on('input', "[data-filter-select]", function() {
                var _that = $(this);
                var id = _that.get(0).selectize.getValue();
                var data = _that.get(0).selectize.options[id];
                var moduleName = _that.closest('table').attr('data-app-form-subform')
                var currentRow = _that.attr('data-bom-qty')? $('[data-bom-qty-input]'):_that.closest("tr")
                if(moduleName == 'materials-subform'){//set average product price on product selection in material subform
                    currentRow.find('input[data-product-price]').val(data.average_price ?? 0)
                    calcProductSubTotal(this)
                }
                currentRow.find('div[class="unit-factor-select"]').remove();
                if (data && data.units && data.units.length) {
                    var unitsOptions = ''
                    for (var i = 0; i < data.units.length; i++) {
                        let unitValue = data.units[i].id+"_"+data.units[i].factor+"_"+data.units[i].factor_name+"_"+data.units[i].small_name;
                        unitsOptions += `<option value="${unitValue}">${data.units[i].small_name}</option>`;
                    }
                    $('select[name="scraps[0][unit_factor_data]"]')
                    currentRow.find("[data-qty-type-select='" + _that.attr('data-filter-select') + "']").find(".unit-factor-select").remove();
                    currentRow.find("[data-qty-type-select='" + _that.attr('data-filter-select') + "']").append(`
                    <div class="ui-table-box-body-item-editable-input-container unit-factor-select">
                            <select name="${_that.attr('data-qty-input-name')}" data-select-product-unit class="l-input ui-input" data-app-form-select="true" placeholder="<?=__t('Unit')?>" data-app-form-select-template="currency" data-app-form-validate="required">
                                <option value=""></option>
                            ${unitsOptions}
                            </select>
                    </div>
                    `);
 
                    (new window.IzLayout.UiSelect()).initAll();
                } else {
                    currentRow.find("[data-qty-type-select='" + _that.attr('data-filter-select') + "']").find(".unit-factor-select").remove()
                }
            });

            // set sub production route select options when main production routing changes
            $('#production-routing').on('change', function(e) {
                let productionRouting = $(this).val();
                subProductionRoutesOptions = [];
                loadSubProductionRoutesOptions(productionRouting, true);
             
            });

            // handle calc product row subtotal qty*price, on qty input change
            bomCreateForm.on('input',"[data-product-qty]", function() {
                calcProductSubTotal(this)
            })
            

            bomCreateForm.on('input', "[data-select-product-unit]", function() {
                let _that = $(this)
                let moduleName = _that.closest('table').attr('data-app-form-subform')
                let unitFactor = _that.val().split("_")[1]
                if(moduleName == "materials-subform"){
                    let avgPrice = _that.closest('tr').find('[data-product-price]')
                    let product = _that.closest("tr").find('[data-qty-input-name]')
                    if(product.get(0).selectize.getValue()){
                        let productAvgPrice = product.get(0).selectize.options[product.get(0).selectize.getValue()]['average_price'];
                        if(!productAvgPrice){
                            productAvgPrice = product.closest("tr").find('[data-product-average-price]').val()
                        }
                        avgPrice.val((productAvgPrice??0)*unitFactor)

                    }
                    calcProductSubTotal(this)
                }
            })

            // handle calc product row subtotal qty*price, on price input change
            bomCreateForm.on('input', "[data-product-price]", function() {
                calcProductSubTotal(this)
            })

            // handle calc expense and operations row subtotal based on type, on type change
            bomCreateForm.on('change',"[data-select-formula-type]", function() {
                if($(this).val() == 'equation'){
                    initPlaceholderDefaultFormula(this)
                }
                calcSubtotalBasedOnType(this)

            })

            // handle calc expense and operations row subtotal based on type, on factor(amount in expense or time in operation) change 
            bomCreateForm.on('input', "[data-row-factor]", function() {
                calcSubtotalBasedOnType(this)
            })

            bomCreateForm.on('change', "[data-select-workstation]", function() {
                calcSubtotalBasedOnType(this)
            })

            // evaluate placeholder formula at expenses & manufacturing operations subforms on placeholder textarea changes
            bomCreateForm.on('input', "[data-placeholder-text-input]", function() {
                evaluateFoumula($(this))
            });

            // init placeholder for cost formula at expenses & manufacturing operations subforms
            $('[data-placeholder-text-input]').inputPlaceholder();
            bomCreateForm.on('change', '[data-select-type]', function(e) {
                var _that = $(this);
                if (_that.val() == "equation") {
                    _that.parents('td').find('[data-placeholder-input]').css('display', 'block');
                    $("[data-expenses-amount-input]").attr('readonly', "true");
                    _that.parents('td').find('[data-placeholder-input]').removeAttr('disabled');
                } else {
                    _that.parents('td').find('[data-placeholder-input]').css('display', 'none');
                    $("[data-expenses-amount-input]").removeAttr('readonly');
                    _that.parents('td').find('[data-placeholder-input]').attr('disabled', "true");
                }
            })

            // handle calc expense and operations row subtotal based on type, on BOM QTY change 
            $("#quantity").on("input", function() {
                calcSubtotalBasedOnType(this)
            })

            function preventInvalidCharsForNumber(el){
                el.addEventListener('keydown', (e) => {
                    var allowedChars = ["0","1","2","3","4","5","6","7","8","9","Backspace","Tab","ArrowRight","ArrowLeft","X","x","V","v","C","c","A","a","Control"];
                    var invalidChars = ["+","E","e"];
                    var numericalChars = new Set(allowedChars);
                    if (!numericalChars.has(e.key) && invalidChars.includes(e.key)) {
                        e.preventDefault();
                    }
                });
            }

            // handle on add new row in any subform
            $("[data-app-form-subform-add-btn]").on("click", function() {
                var moduleName = $(this).attr('data-app-form-subform-add-btn');
                setTimeout(() => {
                    $(this).parents((".l-show-card-section-title-box")).find(".collapse-qty").text($("[data-app-form-subform=" + moduleName + "] tbody tr").length)
                    calcTotalPrice();
                    let subformRows = $("[data-app-form-subform=" + moduleName + "] tbody tr")
                    let lastRow = subformRows[subformRows.length - 1]

                    initRowSubProductionRouting(lastRow)
                    let element = $(lastRow)
                    if(moduleName == 'manufacturing-operations-subform'){
                        let select = element.closest("tr").find('.workstations-select')
                        initSelectizeDropdown(
                            select,
                            "workstations-select",
                            "/v2/owner/workstations/search_workstations?term=d&withTotalCost=1&getById=1&_type=query",
                            ['q']
                            ,
                            {
                                id: "id",
                                value: "id",
                                name: "text",
                                text: "text",
                                total_cost: "total_cost"
                            },
                            initialWorkstations
                        )
                    }else if(moduleName == 'materials-subform'){
                        let select = element.closest("tr").find('.material-select-product')
                        initSelectizeDropdown(
                            select,
                            "material-select-product",
                            "/v2/api/entity/product/list-photo/2?mode=listing&fields[0]=id&fields[1]=product_code&fields[2]=name&fields[3]=unit_template_id&fields[4]=average_price&filter[status][equal]=0&filter[type][in][]=0&filter[type][in][]=1&filter[type][in][]=3&per_page=50",
                            [ 
                                "filter[or][name][like]",
                                "filter[or][product_code][like]",
                                "filter[or][barcode][like]"
                            ],
                            {
                                id: "id",
                                value: "id",
                                name: "text",
                                text: "text",
                                average_price: "average_price",
                                units: "units"
                            },
                            initialMaterialsProducts
                        )  
                     }else if(moduleName == 'scrap-items-subform'){
                        let select = element.closest("tr").find('.scrap-product-select')
                        initSelectizeDropdown(
                            select,
                            "scrap-product-select",
                            "/v2/api/entity/product/list-photo/2?mode=listing&fields[0]=id&fields[1]=product_code&fields[2]=name&fields[3]=unit_template_id&filter[status][equal]=0&filter[type][in][]=0&filter[type][in][]=1&filter[type][in][]=3&per_page=50",
                            [ 
                                "filter[or][name][like]",
                                "filter[or][product_code][like]",
                                "filter[or][barcode][like]"
                            ],
                            {
                                id: "id",
                                value: "id",
                                name: "text",
                                text: "text",
                                average_price: "average_price",
                                units: "units"
                            },
                            initialMaterialsProducts
                        )   

                     }

                    if(moduleName == 'expenses-subform'){
                        $('.account-select4').initJournalAccountSelect();
                    }
                    $('[data-placeholder-text-input]').inputPlaceholder();

                    $(`[data-app-form-number]`).each(function(i,el) {
                        preventInvalidCharsForNumber(el)
                    });
                }, 0);
          
            })
          
            // calc total price and subform counter on remove btn clicked
            bomCreateForm.on("click", "[data-app-form-subform-row-removable-btn]", function() {
                var moduleName = $(this).attr('data-app-form-subform-row-removable-btn');
                setTimeout(() => {
                    $("[data-app-form-subform=" + moduleName + "]").parents((".l-show-card-section-title-box")).find(".collapse-qty").text($("[data-app-form-subform=" + moduleName + "] tbody tr").length);
                    // remove last item close tab
                    if($("[data-app-form-subform=" + moduleName + "] tbody tr").length == 0) {
                        $("[data-app-form-subform=" + moduleName + "]").parents('.l-show-card-section-title-box').find('.collapse').collapse('hide');
                    }
                    calcTotalPrice();
                }, 10);
            })

            bomCreateForm.on("change", ".sub-production-routings-select", function(e) { 
                $(this).prevAll('select option').removeAttr("selected");
                $(this).prevAll('select').append("<option value='" + $(this).val() + "' selected='selected'></option>");
            })

            $("#update_avg_price_btn").on("click", function() {
                let materialProducts = $('select.material-select-product')
                let materialProductsIds = []
                for(let i = 0; i < materialProducts.length; i++){
                    materialProductsIds.push($(materialProducts[i]).val())
                }
                $.ajax({
                    url: "/v2/owner/products/getProductsAveragePrices",
                    type:'GET',
                    data: {productsIds: materialProductsIds},
                    success: function(result){
                        if(result.data){
                            for(let i = 0; i < materialProducts.length; i++){
                                let product = $(materialProducts[i])
                                let unitFactor = product.closest("tr").find("[data-select-product-unit]")
                                let productAvgPrice = result.data[product.val()]
                                productAvgPrice = productAvgPrice ?? 0 
                                let avgPrice = product.closest("tr").find("[data-product-price]")
                                if(unitFactor.val()){
                                    unitFactor = unitFactor.val().split("_")[1]
                                    productAvgPrice = result.data[product.val()]
                                    if(product.get(0).selectize.getValue()){
                                        avgPrice.val(productAvgPrice*unitFactor).trigger('input')
                                    }
                                }else{
                                    avgPrice.val(productAvgPrice).trigger('input')
                                }
                            }
                        }
                    }
                });
            })
 
           
            function initRowSubProductionRouting(elementRef){
                let element = $(elementRef)
                let select = element.closest("tr").find('.sub-production-routings-select')
                select.selectize({
                    maxItems: 1,
                    options: subProductionRoutesOptions,
                });
                $(".sub-production-routings-select .selectize-dropdown").css("height", "auto");
            }


            function calcProductSubTotal(elementRef){
                let element = $(elementRef)
                let qty = element.closest("tr").find('input[data-product-qty]').val()
                let price = element.closest("tr").find('input[data-product-price]').val()
                element.closest("tr").find('td[data-col-subtotal] span').text(format_price_without_symbol((price * qty), "<?=$currency?>")) 
                calcTotalPrice()
            }

            function calcSubtotalBasedOnType(elementRef){
                let element = $(elementRef)
                if(element.attr('id') == "quantity"){
                    let typeSelectElments = $('select[data-select-formula-type]')
                    for(let i = 0; i< typeSelectElments.length; i++){
                        let element = $(typeSelectElments[i])
                        calcRowSubtotalBasedOnType(element)
                    }
                }else{
                    calcRowSubtotalBasedOnType(element)
                }
                calcTotalPrice()
            }

            function calcRowSubtotalBasedOnType(element){
                let typeSelect = element.closest("tr").find('select[data-select-formula-type]')
                let rowFactor = typeSelect.closest("tr").find('input[data-row-factor]').val()
                if(!rowFactor){
                    rowFactor = 0
                }
                let moduleName = typeSelect.closest('table').attr('data-app-form-subform')
                let rowSubtotal = typeSelect.closest("tr").find('td[data-col-subtotal] span')

                if((typeSelect.val() === 'fixed_amount') || (typeSelect.val() === 'based_on_qty')){ 
                    if(moduleName == "manufacturing-operations-subform"){
                        let workstation = element.closest("tr").find('[data-select-workstation]')
                        if(workstation.get(0).selectize.getValue()){
                            let workstationTotalCost = workstation.get(0).selectize.options[workstation.get(0).selectize.getValue()]['total_cost'];
                            if(!workstationTotalCost){
                                workstationTotalCost = workstation.closest("tr").find('[data-ws-total-cost]').val()
                            }
                          
                            rowSubtotal.text(format_price_without_symbol((rowFactor*(workstationTotalCost?workstationTotalCost:0)), "<?=$currency?>"))
                        }
                    }else{
                        rowSubtotal.text(format_price_without_symbol((rowFactor), "<?=$currency?>"))
                    }
                }else if(typeSelect.val() === 'equation'){
                    evaluateFoumula(element)
                }
            }

            function calcTotalPrice() {
                let totalPrice = 0;
                const accordions = $("[data-accordion-collapse]");

                for (let i = 0; i < accordions.length; i++) {
                    const moduleName = $(accordions[i]).attr('data-accordion-collapse');
                    let moduleTotal = 0;
                    const subtotals = $("[data-col-subtotal=" + moduleName + "]");

                    for (let j = 0; j < subtotals.length; j++) {
                        const subtotalText = $(subtotals[j]).text().trim().replace(/,/g, '');
                        const $val = parseFloat(subtotalText);

                        if (!isNaN($val)) {
                            moduleTotal += $val;
                        }
                    }

                    $("[data-col-total=" + moduleName + "] .val").text(format_price_without_symbol(moduleTotal, "<?=$currency?>"));

                    if (moduleName === "scrap-items") {
                        totalPrice -= moduleTotal;
                    } else {
                        totalPrice += moduleTotal;
                    }
                }
                $("[data-col-total-cost] .val").text(format_price_without_symbol(totalPrice, "<?=$currency?>"));
            }
          
            function evaluateFoumula(element){
                let bomQty = $('#quantity').val()
                let rowFactor = element.closest("tr").find('input[data-row-factor]').val()
                let moduleName = element.closest('table').attr('data-app-form-subform')
                let equation = element.closest('tr').find('textarea[data-placeholder-text-input]').val()
                let equationValues =  {
                    "bom": {
                        'quantity': bomQty,
                    },
                    "manufacturing_order":{
                        'quantity': bomQty
                    },
                };
                if(moduleName == 'expenses-subform'){
                    equationValues["bom_expense"] = {
                        "amount":rowFactor
                    };
                }else if(moduleName == 'manufacturing-operations-subform'){
                    equationValues["bom_operation"] = {
                        "operating_time":rowFactor
                    };
                }
                $.ajax({
                    url: "/v2/api/evaluateEquationByValues",
                    type:'POST',
                    data: {equation: equation, equation_values: JSON.stringify(equationValues)},
                    success: function(result){
                        if(moduleName == 'manufacturing-operations-subform'){
                            let workstation = element.closest("tr").find('[data-select-workstation]')
                            if(workstation.get(0).selectize.getValue()){
                                let workstationTotalCost = workstation.get(0).selectize.options[workstation.get(0).selectize.getValue()]['total_cost'];
                                if(!workstationTotalCost){
                                    workstationTotalCost = workstation.closest("tr").find('[data-ws-total-cost]').val()
                                }
                                element.closest("tr").find('td[data-col-subtotal] span').text(format_price_without_symbol((result.data*(workstationTotalCost? workstationTotalCost:0)), "<?=$currency?>") ) 
                            }
                        }else{
                            element.closest("tr").find('td[data-col-subtotal] span').text(format_price_without_symbol(result.data, "<?=$currency?>") ) 
                        }
                        calcTotalPrice()
                    }      
                });
            }
            
            /*** Add one row by default if empty subform when opened for the first time in BOM add/edit page ***/
            var collapseButton = document.querySelectorAll(".accordion-collapse-btn");
            collapseButton.forEach(element => {
                    var moduleName;
                    var addButtonCollapse = $(element).parents((".l-show-card-section-title-box")).find('[data-app-form-subform-add-btn]');
                    $(element).on('click', function(){
                        moduleName = addButtonCollapse.attr('data-app-form-subform-add-btn');
                        if($("[data-app-form-subform=" + moduleName + "] tbody tr").length == 0) {
                            addButtonCollapse.click();
                            setTimeout(() => {
                                $(element).parents((".l-show-card-section-title-box")).find(".collapse-qty").text($("[data-app-form-subform=" + moduleName + "] tbody tr").length)
                            }, 5);
                        }
                        if(moduleName == 'expenses-subform'){
                            $('.account-select4').initJournalAccountSelect();
                        }
                    });
            });

            // when submit form open all taps
            var errors = <?= json_encode($errors); ?> ?? [];
             openSubformOnErrors();
             $("button[type='submit']").on("click", function() {
                openSubformOnErrors();
            });

            function openSubformOnErrors() {
                collapseButton.forEach(element => {
                    if($(element).parents('.l-show-card-section-title-box').find("tbody tr").length || Object.keys(errors).length != 0) {
                        $(element).parents('.l-show-card-section-title-box').find('.collapse').collapse('show');
                    }
                });
            }

         })

         function loadSelectizeOptionsSelect(query, callback, ajaxUrl, queryFilters, optionData, ajaxData = null){
            let queryFiltersObjects = {}; 
            for(i = 0; i < queryFilters.length; i++){
                let filter = queryFilters[i]
                queryFiltersObjects[filter] = query
            }
            if(!ajaxData){
                $.ajax({
                    url: ajaxUrl,
                    type: 'GET',
                    dataType: 'json',
                    data: queryFiltersObjects,
                    error: function() {
                        callback();
                    },
                    success: function(res) {
                        callback(res.map(function(item) {
                        let optionDataObject = {}
                        Object.keys(optionData).forEach(function(key,index) {
                            optionDataObject[key] = item[optionData[key]]
                        });
                            return optionDataObject;
                        }))
                    }
                });
            }else{
                callback(ajaxData.map(function(item) {
                let optionDataObject = {}
                Object.keys(optionData).forEach(function(key,index) {
                    optionDataObject[key] = item[optionData[key]]
                });
                    return optionDataObject;
                }))
            }
           
        }

        function initSelectizeDropdown(select, selectClass, ajaxUrl, queryFilters, optionData, ajaxData = []){
            select[0]?.selectize?.destroy();
            select.selectize({
                maxItems: 1,
                // placeholder: "<?=__t('Search')?>...",
                load: function(query, callback) {
                    loadSelectizeOptionsSelect(query, callback, ajaxUrl, queryFilters, optionData)
                },
                onInitialize: function() {     
                    this.load(function(callback, query) {
                        loadSelectizeOptionsSelect(query, callback, ajaxUrl, queryFilters, optionData, ajaxData)
                    })
                 
                }
            });
            $("."+selectClass+" .selectize-dropdown").css("height", "auto");
        }
        var initialProductionRoutings = JSON.parse(JSON.stringify(<?= json_encode($productionRoutings) ?>)),
          initialWorkstations = JSON.parse(JSON.stringify(<?= json_encode($workstations) ?>)),
          initialMainProducts = [],
          initialMaterialsProducts = [];

        function loadInitialProducts(){
            $.ajax({
                url: "/v2/api/entity/product/list-photo/2?mode=listing&fields[0]=id&fields[1]=product_code&fields[2]=name&fields[3]=unit_template_id&fields[4]=average_price&fields[5]=type&filter[status][equal]=0&per_page=50",
                type: 'GET',
                dataType: 'json',
                data: '',
                error: function() {
                    callback();
                },
                success: function(res) {
                    for(let i = 0; i < res.length; i++){
                        if((res[i].type === 1) || (res[i].type === 0)){
                            initialMainProducts.push(res[i])
                            initialMaterialsProducts.push(res[i])
                        }else if(res[i].type === 3 ){
                            initialMaterialsProducts.push(res[i])
                        }
                    }

                    // init main products select
                    initSelectizeDropdown(
                        $(".main-product-select"),
                        "main-product-select",
                        "/v2/api/entity/product/list-photo/2?mode=listing&fields[0]=id&fields[1]=product_code&fields[2]=name&fields[3]=unit_template_id&fields[4]=average_price&filter[status][equal]=0&filter[type][in][]=0&filter[type][in][]=1&per_page=50",
                        [ 
                            "filter[or][name][like]",
                            "filter[or][product_code][like]",
                            "filter[or][barcode][like]"
                        ],
                        {
                            id: "id",
                            value: "id",
                            name: "text",
                            text: "text",
                            average_price: "average_price",
                            units: "units"
                        },
                        initialMainProducts
                    )

                    // init material products select
                    initSelectizeDropdown(
                        $(".material-select-product"),
                        "material-select-product",
                        "/v2/api/entity/product/list-photo/2?mode=listing&fields[0]=id&fields[1]=product_code&fields[2]=name&fields[3]=unit_template_id&fields[4]=average_price&filter[status][equal]=0&filter[type][in][]=0&filter[type][in][]=1&filter[type][in][]=3&per_page=50",
                        [ 
                            "filter[or][name][like]",
                            "filter[or][product_code][like]",
                            "filter[or][barcode][like]"
                        ],
                        {
                            id: "id",
                            value: "id",
                            name: "text",
                            text: "text",
                            average_price: "average_price",
                            units: "units"
                        },
                        initialMaterialsProducts
                    )
                    
                     // init scrap products select
                    initSelectizeDropdown(
                        $(".scrap-product-select"),
                        "scrap-product-select",
                        "/v2/api/entity/product/list-photo/2?mode=listing&fields[0]=id&fields[1]=product_code&fields[2]=name&fields[3]=unit_template_id&filter[status][equal]=0&filter[type][in][]=0&filter[type][in][]=1&filter[type][in][]=3&per_page=50",
                        [ 
                            "filter[or][name][like]",
                            "filter[or][product_code][like]",
                            "filter[or][barcode][like]"
                        ],
                        {
                            id: "id",
                            value: "id",
                            name: "text",
                            text: "text",
                            average_price: "average_price",
                            units: "units"
                        },
                        initialMaterialsProducts
                    )   
                }
            });
        }
        loadInitialProducts()

        // init main production routing select
        initSelectizeDropdown(
            $(".main-production-routing-select"),
            "main-production-routing-select",
            "/v2/owner/production_routing/search_main_production_routing?term=d&_type=query",
            ['q']
            ,
            {
                id: "id",
                value: "id",
                name: "text",
                text: "text"
            }, 
            initialProductionRoutings
        )

         // init workstations select
         initSelectizeDropdown(
            $(".workstations-select"),
            "workstations-select",
            "/v2/owner/workstations/search_workstations?term=d&withTotalCost=1&_type=query",
            ['q']
            ,
            {
                id: "id",
                value: "id",
                name: "text",
                text: "text",
                total_cost: "total_cost"
            },
            initialWorkstations
        )
        $(document).on('select2:open', () => {
            document.querySelector('.select2-search__field').focus();
        });
    </script>
<?php $this->endSection() ?>
