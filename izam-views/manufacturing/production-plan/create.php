<?php

use Izam\Daftra\Common\Utils\ProductStatusUtil;

$extends = 'layouts/layout';
$formUrl = route('owner.entity.store', ['entityKey' => 'production_plan']);
$method = 'POST';
$data = count(old()) ? old() : $form->getData();
$isUpdate = false;
$isConvert = false;
if(isset($isClone)) {
    $data['id'] = null;
}

if(isset($extraData['invoice_data'])) {
    $invoiceData = $extraData['invoice_data'];
    if($invoiceData->type == \App\Utils\Invoices\InvoiceTypeUtil::SALES_ORDER) {
        $data['source_type'] = 'sales_order';
    } else {
        $data['source_type'] = 'invoice';
    }
    $data['client_id'] = $invoiceData->client_id;
    $data['employees'] = [$invoiceData->sales_person_id];
    $isConvert = true;
}

if(isset($data['id'])) {
    $formUrl = route('owner.entity.update', ['entityKey' => 'production_plan', 'id' => $data['id']]);
    $method = 'PUT';
    $isUpdate = true;
}

$this->viewVars['_PageBreadCrumbs'] = $this->sharedVars['generalBreadCrumbs'];

$title = $isUpdate ? sprintf(__t('Edit %s'), __t('Production Plan')) : sprintf(__t('Add %s'), __t('Production Plan'));
$this->viewVars['title_for_layout'] = sprintf($title, __t('Production Plan'));
function addDisabledIfIsUpdate($attributes, $isUpdate) {
    if($isUpdate) {
        $attributes['readonly'] = 'readonly';
    }
    return $attributes;
}
$productsApi = '/v2/owner/products/ajax_products_filter_v2?search=__q__&status='. ProductStatusUtil::STATUS_ACTIVE .'&exclude[type]=2&added_relations[]=activeBoms';
?>
<style>
    .inactive-input{
        background-color: #e9ecef;
        opacity: 1;
        pointer-events : none;
    }
</style>
<?php $this->section('article-start') ?>
<form id="productionPlanForm" action="<?= $formUrl ?>" method="POST">
<?php $this->endSection() ?>

    <?php
    $buttons = [
        'desktop' => [
            'start' => [
            ],
            'end' => [
                ['type' => 'link', 'class' => 'btn-cancel', 'icon' => 'mdi-close-thick', 'text' => __t('Cancel'), 'url' =>  url()->previous( fallback: route('owner.entity.list', ['entityKey' => 'production_plan'])) ],
                ['type' => 'button', 'class' => 'btn-success', 'icon' => 'mdi-content-save', 'text' => __t('Save'), 'url' => '#']
            ]
        ],
        'mobile' => [
            'end' => [
                ['type' => 'link', 'class' => 'btn-secondary', 'icon' => 'mdi-close-thick', 'text' => __t('Cancel'), 'url' => url()->previous( fallback: route('owner.entity.list', ['entityKey' => 'production_plan'])) ],
                ['type' => 'button', 'class' => 'btn-success', 'icon' => 'mdi-content-save', 'text' => __t('Save'), 'url' => '#']
            ]
        ]
    ];

    if(!$isUpdate) {
        $buttons['desktop']['start'][] = ['type' => 'link', 'class' => 'btn-cancel save-as-draft', 'icon' => 'mdi-pencil', 'text' => __t('Save As Draft'), 'url' => '#'];
        $buttons['mobile']['start'][] = ['type' => 'link', 'class' => 'btn-cancel save-as-draft', 'icon' => 'mdi-pencil', 'text' => __t('Save As Draft'), 'url' => '#'];
    }
    $formUrl = route('owner.entity.store', ['entityKey' => 'production_plan']);
    $this->section('page-head');
    echo $this->includeSection('partials/form/head', ['buttons' => $buttons]);
    $this->endSection();
    ?>
    <input type="hidden" name="_token" value="<?= csrf_token() ?>">
    <input type="hidden" name="_method" value="<?= $method ?>">
    <?php if(!empty($data['id'])) { ?>
    <input type="hidden" name="id" value="<?= $data['id'] ?>" />
    <?php } ?>
    <?php if(isset($data['is_draft']) && $data['is_draft']) { ?>
        <input type="hidden" name="is_draft" value="1" />
    <?php } ?>
    <!-- Add Page heads (desktop and mobile are the same) -->

    <div class="page-content">
        <div class="container">
            <div class="my-10 vstack gap-10">


                <div class="card">
                    <div class="card-header">
                        <?= __t('Production Plan Information') ?>
                    </div>
                    <div class="card-body">
                        <div class="card-row">
                            <div class="col-lg-4">
                                <?php $this->includeSection('partials/form/input_template', [
                                    'name' => 'name',
                                    'label' => __t('Name') . '<span class="text-danger"> *</span>',
                                    'type' => 'text',
                                    'inputClass'=> 'form-control '. ($isUpdate ? 'inactive-input' : ''),
                                    'id' => 'name',
                                    'value' => $data['name']?? '',
                                    'attributes' => addDisabledIfIsUpdate(['data-form-rules' => 'required'], $isUpdate)
                                ]); ?>
                            </div>
                            <div class="col-lg-2">

                                <?php
                                $autoNumber = $form->getFieldSets()['code']->getElements()['code']->getValue()?? "000001";
                                $autoNumberGenerated = $form->getFieldSets()['code']->getElements()['generated']->getValue()?? "000001";
                                $autoNumber = old('code.code')?? $autoNumber;
                                $this->includeSection('partials/form/input_template', [
                                    'name' => 'code[code]',
                                    'label' => __t('Code') . '<span class="text-danger"> *</span>',
                                    'type' => 'text',
                                    'value' => $autoNumber,
                                    'id' => 'code',
                                    'attributes' => ['data-form-rules' => 'required']
                                ]); ?>
                                <input type="hidden" name="code[generated]" value="<?= $autoNumberGenerated ?>">
                            </div>

                            <div class="col-lg-3">
                                <?php $this->includeSection('partials/form/input_template', [
                                    'name' => 'date_from',
                                    'label' => __t('Date From'),
                                    'type' => 'date',
                                    'value' => $data['date_from']?? '',
                                    'attributes' => addDisabledIfIsUpdate(['placeholder' => __t('Select date'), 'data-date-input' => 'date_from'], $isUpdate)
                                ]); ?>

                            </div>

                            <div class="col-lg-3">
                                <?php $this->includeSection('partials/form/input_template', [
                                    'name' => 'date_to',
                                    'label' => __t('Date To'),
                                    'type' => 'date',
                                    'value' => $data['date_to']?? '',
                                    'attributes' => addDisabledIfIsUpdate(['placeholder' => __t('Select date'), 'data-date-input' => 'date_to'], $isUpdate)
                                ]); ?>

                            </div>

                            <div class="col-md-6">
                                <?php
                                $clientOptions = $extraData['clientOptions']?? [];
                                $this->includeSection('partials/form/input_template', [
                                    'name' => 'client_id',
                                    'label' => __t('Client'),
                                    'options' => $clientOptions,
                                    'type' => 'ajax_select',
                                    'value' => $data['client_id']?? '',
                                    'inputOptions' => [
                                        'disableJavascript' => true,
                                        'emptyOptionText' => __t('Select Client'),
//                                        'ajaxUrl' => '/v2/owner/clients/filter?allow_suspended=0&term=__q__&_type=query&q=__q__&fields[]=phone1',
                                    ],
                                    'attributes' => addDisabledIfIsUpdate([], $isUpdate || $isConvert)
                                ]); ?>
                            </div>

                            <div class="col-md-6">

                                <?php
                                $employeeOptions = $extraData['employeeOptions']?? [];
                                $this->includeSection('partials/form/input_template', [
                                    'name' => 'employees[]',
                                    'id' => 'employees',
                                    'label' => __t('Employees'),
                                    'type' => 'ajax_select',
                                    'emptyOptionText' => __t('Select Employee'),
                                    'options' => $employeeOptions,
                                    'value' => $data['employees']?? [],
                                    'inputOptions' => [
                                        'disableJavascript' => true,
                                        'ajaxUrl' => '/v2/owner/staff/search?_type=query&q=__q__',
                                    ],
                                    'attributes' => addDisabledIfIsUpdate(['multiple' => 'multiple'], $isUpdate)
                                ]); ?>
                            </div>


                            <div class="col-lg-6">
                                <?php $this->includeSection('partials/form/input_template', [
                                    'name' => 'description',
                                    'label' => __t('Description'),
                                    'type' => 'textarea',
                                    'id' => 'description',
                                    'value' => $data['description']?? '',
                                    'attributes' => ['data-textarea-autosize' => '{"space": -16}']
                                ]); ?>
                            </div>

                            <div class="col-md-3">
                                <?php $this->includeSection('partials/form/input_template', [
                                    'name' => 'source_type',
                                    'label' => __t('Source Type'),
                                    'type' => 'select',
                                    'id' => 'source_type',
                                    'value' => $data['source_type']?? '',
                                    'inputOptions' => [
                                        'emptyOptionText' => " ",
                                    ],
                                    'options' => [
                                            'invoice' => __t('Invoice'),
                                            'sales_order' => __t('Sales Order'),
                                    ],
                                    'attributes' => addDisabledIfIsUpdate(['data-source-type-select' => 'true'], $isUpdate || $isConvert)
                                ]); ?>
                            </div>

                            <div class="col-md-3">
                                <?php
                                $this->includeSection('partials/form/input_template', [
                                    'name' => 'source_id',
                                    'label' => __t('Source ID'),
                                    'type' => 'select',
                                    'options' => $extraData['invoiceOptions']?? [],
                                    'value' => $extraData['invoiceOptions'][0]['value'] ?? ($data['source_id']?? ''),
                                    'emptyOptionText' => __t('Please Select'),
                                    'inputOptions' => [
                                        'disableJavascript' => true,
                                    ],
                                    'attributes' => addDisabledIfIsUpdate([], $isUpdate || $isConvert)
                                ]); ?>
                            </div>
                        </div>
                    </div>

                    <hr class="hr hr-dashed hr-md text-secondary opacity-100" />


                    <div class="card-body">
                        <div class="accordion-collapse">
                            <button class="btn accordion-collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="[data-accordion-collapse='materials']" aria-expanded="true" aria-controls="materials">
                                        <span class="col-6 text-start">
                                            <span class="accordion-collapse-title">
                                                <i class="mdi mdi-package-variant"></i>
                                                <span id="productsCount"><?= __t('Products') ?> (1)</span>
                                            </span>
                                        </span>
                                <span class="col-5 fw-normal">

                                        </span>
                                <span class="col-1 text-end">
                                            <span class="accordion-collapse-arrow">
                                                <i class="mdi mdi-unfold-more-horizontal" aria-expanded="false"></i>
                                                <i class="mdi mdi-unfold-less-horizontal" aria-expanded="true"></i>
                                            </span>
                                        </span>
                            </button>
                            <div class="collapse show" data-accordion-collapse="materials">
                                <div class="accordion-collapse-body">
                                    <div class="subform-container" data-materials-subform="true">
                                        <table class="subform-table subform-table-mobile-placeholder">
                                            <thead>
                                            <tr>
                                                <th width="1000"><?= __t('Product') ?></th>
                                                <th width="500" ><?= __t('QTY') ?></th>
                                                <th width="1000"><?= __t('bom') ?></th>
                                                <th width="50"></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php if(empty($extraData['production_plan_items']) && (!isset($errors) || empty($errors))) { ?>
                                            <tr>
                                                <td class="subform-cell-editable border-1">
                                                    <div class="input-container">

                                                        <div class="form-group" data-form-group="true">
                                                            <?php $this->includeSection('partials/form/input_template', [
                                                                'name' => 'production_plan_items[0][product_id]',
                                                                'label' => false,
                                                                'type' => 'ajax_select',
                                                                'id' => 'production_plan_items_0_product_id',
                                                                'options' => $extraData['productOptions']?? [],
                                                                'emptyOptionText' => __t('Select Product'),
                                                                'inputOptions' => [
                                                                    'ajaxUrl' => $productsApi,
                                                                    'emptyOptionText' => __t('Select Product'),
                                                                    'disableImg' => true,
                                                                    'disableJavascript' => true,
                                                                ],
                                                                'attributes' => ['data-form-rules' => "required"]
                                                            ]); ?>

                                                        </div>
                                                    </div>
                                                </td>

                                                <td class="subform-cell-editable border-start">
                                                    <div class="input-container">
                                                        <div class="form-group">
                                                            <div class="input-group input-group-merge flex-nowrap">
                                                                <div class="col">
                                                                    <?php
                                                                    $this->includeSection('partials/form/input_template', [
                                                                        'name' => 'production_plan_items[0][quantity]',
                                                                        'label' => false,
                                                                        'type' => 'number',
                                                                        'id' => 'production_plan_items_0_quantity',
                                                                        'inputClass' => 'form-control item_quantity',
                                                                        'attributes' => [
                                                                                'placeholder' => __t('Quantity'),
                                                                                "min" => 0,
                                                                                'data-form-rules' => "required",
                                                                                'data-input-number' => json_encode(["negative" =>  false])
                                                                        ]
                                                                    ]);

                                                                    ?>
                                                                </div>
                                                                <span class="col-4 d-none unit-factor-span">
                                                                    <?php
                                                                    $this->includeSection('partials/form/input_template', [
                                                                        'name' => 'production_plan_items[0][unit_factor_id]',
                                                                        'label' => false,
                                                                        'type' => 'select',
                                                                        'options' => [],
                                                                        "div" => false,
                                                                        'id' => 'production_plan_items_0_factor',
                                                                        'inputClass' => 'form-control unit-factor-select',
                                                                        'inputOptions' => [
                                                                            'disableJavascript' => true,
                                                                            'emptyOptionText' => __t('Select Factor'),
                                                                        ],
                                                                        'attributes' => [
                                                                            'placeholder'=> __t("Please Select")
                                                                        ]
                                                                    ]);
                                                                    ?>
                                                                </span>
                                                                <input class="unit-factor-input-js" type="hidden" name="production_plan_items[0][unit_factor]">
                                                                <input class="unit-name-input-js" type="hidden" name="production_plan_items[0][unit_name]">
                                                                <input class="unit-small-name-input-js" type="hidden" name="production_plan_items[0][unit_small_name]">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="subform-cell-editable border-start">
                                                    <div class="input-container">
                                                        <?php $this->includeSection('partials/form/input_template', [
                                                            'name' => 'production_plan_items[0][bom_id]',
                                                            'label' => false,
                                                            'type' => 'select',
                                                            'id' => 'production_plan_items_0_bom_id',
                                                            'inputClass' => 'form-control select-bom',
                                                            'options' => [],
                                                            'inputOptions' => [
                                                                'emptyOptionText' => __t('Select Bom'),
                                                                'disableJavascript' => true,
                                                            ],
                                                            'attributes' => ['data-form-rules' => "required"]
                                                        ]); ?>
                                                    </div>
                                                    <script>

                                                        $(function() {
                                                            initProductionPlanItemSelectize('#production_plan_items_0_product_id', '#production_plan_items_0_bom_id', '#production_plan_items_0_factor');
                                                        });
                                                    </script>
                                                </td>

                                                <td class="subform-cell-actions subform-cell-actions-end bg-light" width="50">
                                                    <button class="subform-cell-remove " type="button" data-materials-remove="true">
                                                        <i class="mdi mdi-trash-can"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <?php } ?>
                                            <?php
                                            foreach ($extraData['production_plan_items'] as $k => $item) { ?>
                                                <tr>
                                                    <td class="subform-cell-editable border-1">
                                                        <div class="input-container">
                                                            <div class="form-group" data-form-group="true">
                                                                <?php
                                                                if(isset($item['id']) && !isset($isClone)) {
                                                                ?>
                                                                    <input type="hidden" name="<?= "production_plan_items[$k][id]" ?>" value="<?= $item['id'] ?>" />
                                                                    <input type="hidden" name="<?= "production_plan_items[$k][manufacturing_order_id]" ?>" value="<?= $item['manufacturing_order_id'] ?>" />
                                                                    <input type="hidden" name="<?= "production_plan_items[$k][invoice_item_id]" ?>" value="<?= $item['invoice_item_id'] ?>" />
                                                                <?php
                                                                }
                                                                if((isset($item['id']) || $isConvert) && !isset($isClone)) {
                                                                    ?>
                                                                    <input type="hidden" name="<?= "production_plan_items[$k][invoice_item_id]" ?>" value="<?= $item['invoice_item_id'] ?>" />
                                                                    <?php
                                                                }
                                                                ?>
                                                                <?php
                                                                if($isConvert) {

                                                                ?>
                                                                    <input type="hidden" name="converted" value="1">
                                                                <?php } ?>
                                                                <?php $this->includeSection('partials/form/input_template', [
                                                                    'name' => "production_plan_items[$k][product_id]",
                                                                    'label' => false,
                                                                    'type' => 'ajax_select',
                                                                    'options' => $extraData['productOptions'],
                                                                    'id' => "production_plan_items_{$k}_product_id",
                                                                    'emptyOptionText' => __t('Select Product'),
                                                                    'value' => $item['product_id'],
                                                                    'inputOptions' => [
                                                                        'emptyOptionText' => __t('Select Product'),
                                                                        'ajaxUrl' => $productsApi,
                                                                        'disableImg' => true,
                                                                        'disableJavascript' => true,
                                                                    ],
                                                                    'attributes' => addDisabledIfIsUpdate([
                                                                        'data-form-rules' => "required",
                                                                    ], $isUpdate)
                                                                ]); ?>

                                                            </div>
                                                        </div>
                                                    </td>

                                                    <td class="subform-cell-editable border-start">
                                                        <div class="input-container">
                                                            <div class="form-group">
                                                                <div class="input-group input-group-merge flex-nowrap">
                                                                    <div class="col">
                                                                        <?php
                                                                        $quantity = $item['quantity']?? 0;
                                                                        if(!empty($item['unit_factor']) && $item['unit_factor'] != 0 && empty(old('production_plan_items'))) {
                                                                            $quantity = $quantity / $item['unit_factor'];
                                                                        }

                                                                        $this->includeSection('partials/form/input_template', [
                                                                                'name' => "production_plan_items[{$k}][quantity]",
                                                                                'label' => false,
                                                                                'type' => 'number',
                                                                                'value' => $quantity,
                                                                                'id' => "production_plan_items_{$k}_quantity",
                                                                                'inputClass' => 'form-control item_quantity',
                                                                                'attributes' => addDisabledIfIsUpdate([
                                                                                    "min" => 0,
                                                                                    'data-form-rules' => "required",
                                                                                    'data-input-number' => '{"negative": false}'
                                                                                ], $isUpdate)
                                                                            ]);
                                                                        ?>
                                                                        <!-- <span class="input-group-text input-group-label">
                                                                            EGP
                                                                        </span> -->
                                                                        <?php
                                                                        $factorOptions = isset($extraData['unitFactors'][$item['product_id']]) ? $extraData['unitFactors'][$item['product_id']] : [];
                                                                        $displayFactor = !empty($factorOptions) || (isset($item['unit_factor_id']) || isset($item['unit_template_id']));

                                                                        ?>
                                                                    </div>
                                                                    <span class="col-4 <?= $displayFactor ? '' : 'd-none' ?> unit-factor-span">
                                                                        <?php
                                                                        $this->includeSection('partials/form/input_template', [
                                                                            'name' => "production_plan_items[$k][unit_factor_id]",
                                                                            'label' => false,
                                                                            'type' => 'select',
                                                                            'options' => $factorOptions,
                                                                            'value' => isset($item['unit_factor_id']) ? $item['unit_factor_id'] : null,
                                                                            "div" => false,
                                                                            'id' => "production_plan_items_{$k}_factor",
                                                                            'inputOptions' => [
                                                                                'disableJavascript' => true,
                                                                            ],
                                                                            'inputClass' => 'form-control unit-factor-select',
                                                                            'attributes' => addDisabledIfIsUpdate([
                                                                                'placeholder'=> __t("Please Select")
                                                                            ], $isUpdate)
                                                                        ]);
                                                                        ?>
                                                                    </span>
                                                                    <input class="unit-factor-input-js" type="hidden" name="production_plan_items[<?= $k ?>][unit_factor]" value="<?= $item['unit_factor'] ?? '' ?>">
                                                                    <input class="unit-name-input-js" type="hidden" name="production_plan_items[<?= $k ?>][unit_name]" value="<?= $item['unit_name'] ?? '' ?>">
                                                                    <input class="unit-small-name-input-js" type="hidden" name="production_plan_items[<?= $k ?>][unit_small_name]" value="<?= $item['unit_small_name'] ?? '' ?>">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="subform-cell-editable border-start">
                                                        <div class="input-container">
                                                            <?php
                                                            $options = isset($extraData['bomOptions']) && isset($extraData['bomOptions'][$item['product_id']]) ? $extraData['bomOptions'][$item['product_id']] : [];
                                                            $selectedOptionValue =$item['bom_id'];
                                                            if (!$isUpdate && !isset($isClone)) {
                                                                $selectedOption = array_filter($options ,function ($opt){return $opt['is_default'] == 1; });
                                                                $selectedOptionValue = $selectedOption[0]['value'] ?? null;
                                                            }
                                                            $this->includeSection('partials/form/input_template', [
                                                                'name' => "production_plan_items[$k][bom_id]",
                                                                'label' => false,
                                                                'type' => 'select',
                                                                'id' => "production_plan_items_{$k}_bom_id",
                                                                'inputClass' => 'form-control select-bom',
                                                                'value' => $selectedOptionValue,
                                                                'options' => $options,
                                                                'emptyOptionText' => __t('Select Bom'),
                                                                'inputOptions' => [
                                                                    'disableJavascript' => true,
                                                                ],
                                                                'attributes' => addDisabledIfIsUpdate(['data-form-rules' => 'required'], $isUpdate)
                                                            ]); ?>
                                                        </div>
                                                        <script>
                                                            $(function() {
                                                                initProductionPlanItemSelectize('#production_plan_items_<?= $k ?>_product_id', '#production_plan_items_<?= $k ?>_bom_id', '#production_plan_items_<?= $k ?>_factor');
                                                            });
                                                        </script>
                                                    </td>

                                                    <td class="subform-cell-actions subform-cell-actions-end bg-light" width="50">
                                                        <button class="subform-cell-remove " type="button" data-materials-remove="true">
                                                            <i class="mdi mdi-trash-can"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php } ?>
                                            </tbody>
                                            <tfoot>
                                            <tr class="d-lg-table-row d-none">
                                                <td colspan="3" valign="top">
                                                    <div class="btn-group gap-1">
                                                        <button type="button" class="btn btn-secondary btn-responsive-icon" id="addRowBtn" data-materials-btn="true">
                                                            <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                            <span><?= __t('Add') ?></span>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>

                                            <!-- Mobile row -->
                                            <tr class="d-table-row d-lg-none">
                                                <td valign="top">
                                                    <div class="d-flex justify-content-between">
                                                        <div class="btn-group gap-1">
                                                            <button type="button" class="btn btn-secondary btn-responsive-icon" data-materials-btn="true">
                                                                <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                <span>Add</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>

                                            </tfoot>

                                        </table>
                                        <template data-materials-template="true">

                                            <tr>
                                                <td class="subform-cell-editable border-start">
                                                    <div class="input-container">
                                                        <?php $this->includeSection('partials/form/input_template', [
                                                            'name' => 'production_plan_items[__index__][product_id]',
                                                            'id' => 'production_plan_items___index___product_id',
                                                            'label' => false,
                                                            'type' => 'select',
                                                            'inputClass' => 'form-control select-product',
                                                            'options' => $extraData['productOptions']?? [],
                                                            'emptyOptionText' => __t('Select Product'),
                                                            'inputOptions' => [
                                                                'emptyOptionText' => __t('Select Product'),
                                                                'disableJavascript' => true,
                                                            ],
                                                            'attributes' => [
                                                                    'data-form-rules' => 'required',
                                                                    'placeholder' => __t('Select Product')
                                                            ]
                                                        ]); ?>
                                                    </div>
                                                </td>

                                                <td class="subform-cell-editable border-start">
                                                    <div class="input-container">
                                                        <div class="form-group" data-form-group="true">
                                                            <div class="input-group input-group-merge flex-nowrap">
                                                                <div class="col">
                                                                    <?php
                                                                    $this->includeSection('partials/form/input_template', [
                                                                        'name' => "production_plan_items[__index__][quantity]",
                                                                        'label' => false,
                                                                        'type' => 'number',
                                                                        'id' => "production_plan_items___index___quantity",
                                                                        'inputClass' => 'form-control item_quantity',
                                                                        'attributes' => [
                                                                            'placeholder'=> __t('Quantity'),
                                                                            "min" => 0,
                                                                            'data-form-rules' => "required",
                                                                            'data-input-number' => '{"negative": false}'
                                                                        ]
                                                                    ]);
                                                                    ?>
                                                                </div>
                                                                <span class="col-4 d-none unit-factor-span">
                                                                    <?php
                                                                    $this->includeSection('partials/form/input_template', [
                                                                        'name' => "production_plan_items[__index__][unit_factor_id]",
                                                                        'label' => false,
                                                                        'type' => 'select',
                                                                        'options' => [],
                                                                        "div" => false,
                                                                        'id' => "production_plan_items___index___factor",
                                                                        'inputOptions' => [
                                                                            'emptyOptionText' => __t('Select Factor'),
                                                                            'disableJavascript' => true,
                                                                        ],
                                                                        'inputClass' => 'form-control unit-factor-select',
                                                                    ]);
                                                                    ?>
                                                                </span>
                                                                <input class="unit-factor-input-js" type="hidden" name="production_plan_items[__index__][unit_factor]">
                                                                <input class="unit-name-input-js" type="hidden" name="production_plan_items[__index__][unit_name]">
                                                                <input class="unit-small-name-input-js" type="hidden" name="production_plan_items[__index__][unit_small_name]">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="subform-cell-editable border-start">
                                                    <div class="input-container">
                                                        <?php $this->includeSection('partials/form/input_template', [
                                                            'name' => 'production_plan_items[__index__][bom_id]',
                                                            'id' => 'production_plan_items___index___bom_id',
                                                            'label' => false,
                                                            'type' => 'select',
                                                            'inputClass' => 'form-control select-bom',
                                                            'options' => [],
                                                            'emptyOptionText' => __t('Select Bom'),
                                                            'inputOptions' => [
                                                                'emptyOptionText' => __t('Select Bom'),
                                                                'disableJavascript' =>  true,
                                                            ],
                                                            'attributes' => [
                                                                'data-form-rules' => "required",
                                                            ]
                                                        ]); ?>
                                                    </div>
                                                </td>

                                                <td class="subform-cell-actions subform-cell-actions-end border-start" width="50">
                                                    <button class="subform-cell-remove" type="button" data-materials-remove="true">
                                                        <i class="mdi mdi-trash-can"></i>
                                                    </button>
                                                </td>
                                                <input name="production_plan_items[__index__][invoice_item_id]" class="invoice_item_id" type="hidden" />
                                            </tr>
                                        </template>
                                        <?php
                                        if(!empty($errors['production_plan_items']) && is_array($errors['production_plan_items'])) {

                                        ?>
                                            <div class="form-validation" data-form-validation="true">
                                                <?php
                                                foreach ($errors['production_plan_items'] as $error) {
                                                    ?>
                                                    <p class="error"><?= $error ?></p>
                                                <?php } ?>
                                            </div>
                                            <?php
                                        } ?>
                                    </div>


                                    <script>
                                        function updateRowsCount() {
                                            const rowsCount = $('.subform-table tbody tr').length
                                            $('#productsCount').text(`<?= __t('Products') ?> (${rowsCount})`);
                                        }
                                        $(document).ready(function() {
                                            $('[data-materials-subform]').subform({
                                                addBtn: '[data-materials-btn]',
                                                template: '[data-materials-template]',
                                                removeBtn: '[data-materials-remove]',
                                                dragBtn: null,
                                                minimumRows: -1,
                                                maximumRows: -1,
                                                onAddRow: function ($row) {
                                                    return true;
                                                },
                                                afterAddRow: function ($row) {
                                                    initProductionPlanItemSelectize('#' + $row.find('select.select-product').attr('id'), '#' + $row.find('select.select-bom').attr('id'), '#' + $row.find('select.unit-factor-select').attr('id'));
                                                    updateRowsCount();
                                                    $($row.find(':input[type="number"]')).inputNumber({negative: false});
                                                    return true;
                                                },
                                                afterRemoveRow: function ($row) {
                                                    updateRowsCount();
                                                    return true;
                                                },
                                                onRemoveRow: function ($row) {
                                                    return true;
                                                }

                                            });
                                            updateRowsCount();
                                        });
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                        //$extraData['customForm']->getElements() has 2 main elements, id and reference_id
                    if (!empty($extraData['customForm']) && ((isset($data['id']) && count($extraData['customForm']->getElements()) >= 2) || count($extraData['customForm']->getElements()) > 2)):
                    ?>
                        <div class="info-section my-8">
                            <!-- Info Section Title -->
                            <div class="info-section-title">
                                <span><?= sprintf(__t("%s More Information") , __t("Production Plan")) ?></span>
                            </div>

                            <div class="info-section-body">
                                <div class="row">
                                    <?php
                                        $helper = new \Izam\View\Form\Helper\FormCollection();
                                        $helper->setShouldWrap(false);
                                        $extraData['customForm']->prepare();
                                        echo $helper->render($extraData['customForm']);
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

               </div>

            </div>
        </div>
    </div>
<?php $this->section('article-end') ?>
</form>
<?php $this->endSection() ?>

<?php $this->section('page-after-js') ?>
<script src="/v2/js/manufacturing/create-production-plan.js"></script>
<?php
$this->includeSection('entitiy/partials/custom-js');
?>
<script>

    let _bomOptions = JSON.parse('<?= json_encode($extraData['bomOptions']?? []) ?>');
    let _unitFactors = JSON.parse('<?= json_encode($extraData['unitFactors']?? []) ?>');
    let _invoiceDefaultOptions = <?=  isset($extraData['invoiceDefaultOptions']) ? json_encode($extraData['invoiceDefaultOptions']) : '[]' ?>;
    let _salesOrderDefaultOptions = <?=  isset($extraData['salesOrderDefaultOptions']) ? json_encode($extraData['salesOrderDefaultOptions']) : '[]' ?>;
    $("form").each(function () {
        var $form = $(this);
        $form.validate(Object.assign({}, window.APP.VENDORS.DEFAULTS.validate.default($form), {}));
    });
</script>

<?php $this->endSection() ?>
