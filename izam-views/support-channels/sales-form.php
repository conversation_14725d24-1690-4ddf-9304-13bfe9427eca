<div class="page-container">
    <form action="#" name="sales-form" id="sales-form">
        <div class="loader-container">
            <div class="inner-loader"></div>
        </div>

        <div class="form-wrapper">
            <div class="form-header">
                <i class="mdi mdi-face-agent form-icon"></i>
                <div class="form-header-description">
                    <h2 class="title"><?php echo __t("I want to talk to someone in sales"); ?></h2>
                    <p class="desc">
                        <?php echo __t("Fill out the form to get in touch with one of our representatives."); ?>
                    </p>
                </div>
            </div>

            <div class="form-err mt-5 mb-5"></div>
            <div class="l-input-box">
                <label class="l-input-label ui-input-label" for="subject"><span class="l-input-label ui-input-label"><?php echo __t("Subject"); ?> </span> <span class="required">*</span></label>
                <input name="subject" type="text" class="l-input ui-input" maxlength="100" value="" id="subject" required placeholder="<?php echo __t("What is this message about?"); ?>">
                <div class="clear"></div>
            </div>


            <div class="l-input-box">
                <label class="l-input-label ui-input-label" for="Message"><?php echo __t("Message"); ?> <span class="required">*</span></label>
                <textarea name="data[Client][notes]" rows="5" class="l-input ui-input" cols="30" id="Message" required placeholder="<?php echo __t("Please tell us about your inquiry."); ?>"></textarea>
            </div>
            <div class="form-group attachments-input">
                <div class="l-input-box">
                    <label class="l-input-label ui-input-label"><?php echo __t("Attachments"); ?></label>
                    <input class="hidden" type="file" id="attachments" name="images[]" multiple style="display:none;">

                    <div class="attachments-container">
                        <div class="file-drop-area l-uploader ui-uploader">

                            <input class="file-input" type="file" name="images[]" multiple>
                            <div class="ui-uploader-drop-area l-uploader-drop-area">
                                <span class="ui-uploader-drop-area-icon l-uploader-drop-area-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="33.002" height="44.003" viewBox="0 0 33.002 44.003">
                                        <path d="M33-47.27a2.369,2.369,0,0,0-.6-1.459L23.98-57.146a2.37,2.37,0,0,0-1.459-.6H22v11H33ZM21.314-44a2.069,2.069,0,0,1-2.063-2.063V-57.75H2.063A2.063,2.063,0,0,0,0-55.687V-15.81a2.063,2.063,0,0,0,2.063,2.063H30.939A2.063,2.063,0,0,0,33-15.81V-44ZM9.672-42.624A4.125,4.125,0,0,1,13.8-38.5a4.125,4.125,0,0,1-4.125,4.125A4.125,4.125,0,0,1,5.547-38.5,4.125,4.125,0,0,1,9.672-42.624ZM27.548-22h-22l.042-4.167,3.4-3.4a.983.983,0,0,1,1.417.042l3.4,3.4,8.9-8.9a1.031,1.031,0,0,1,1.459,0l3.4,3.4Z" transform="translate(0 57.75)" fill="#e4ebf2"></path>
                                    </svg>
                                </span>
                                <span class="ui-uploader-drop-area-text l-uploader-drop-area-text">
                                    <i class="ui-uploader-drop-area-text-upload-icon fal fa-cloud-upload s2020 ui-icon--size-20 u-text-color-primary"></i>
                                    <p style="margin-bottom: 0;padding-bottom:0;"><span>&nbsp;<?php echo __t("Drop file here or"); ?> &nbsp;</span><span class="u-text-color-primary"><?php echo __t("select from your computer"); ?></span></p>
                                </span>
                            </div>
                        </div>

                    </div>
                    <div class="l-uploader-file-items l-uploader-file-items--spacing-top ui-uploader-file-items" style="max-height: none;">
                    </div>

                </div>
            </div>

        </div>
        <div class="btn-container">
            <button type="submit" class="btn btn-success submit-form"><?php echo __t("SEND"); ?></button>

        </div>
    </form>

</div>




<script>
    $(document).ready(function() {
        // (new window.IzLayout.UiUploader).initAllInNode($('.attachments-input'))
        if ($('[data-app-form-uploader-wrapper="images"]').length == 0) {
            (new window.IzLayout.UiUploader).initAllInNode($('.attachments-input'));
            $('[data-app-form-uploader-wrapper="images"]').insertBefore($('[data-app-form-uploader-file-items="images"]'));
        }
        $(document).on('input', '#subject', function(event) {
            $(this).attr('value', event.target.value);
        })

        $(document).on('input', '#Message', function(event) {
            $(this).text(event.target.value);
        })


        $(document).on('click', '.modal-close', function() {
            IzamModal.closeModals()
        })

        var dataTransfer = new DataTransfer();



        $('#sales-form').submit(function(event) {
            $(".loader-container").addClass("loading");
            var data = new FormData();
            $.each($('#attachments')[0].files, function(i, file) {
                data.append('file-' + i, file);
            });
            data.append('subject', $("#subject").val()),
                data.append('message', $("#Message").val()),
                data.append('type', 0),
                data.append('support_channel', 1),
                event.preventDefault();
            $.ajax({
                    type: "POST",
                    url: "/contact",
                    data: data,
                    contentType: false,
                    processData: false,
                    method: "POST",
                    encode: true,
                })
                .done(function(data) {
                    $('.forms-modal').html(`<div class="success-container">
    <div class="content-container">
    <i class="mdi mdi-face-agent form-icon mb-5"></i>
    <h2><?php echo __("Your request has been received,"); ?> </h2>
    <h2 class="mb-8"><?php echo __t("Thank you for reaching out!") ?></h2>
    <p><?php echo __t("We're absolutely thrilled to hear from you, we will get in touch with you as soon as possible on your registered") ?></p>
    <p><?php echo __t("email:") ?> <a href="mailto:<EMAIL>"><?= getCurrentSite('email')?></a>.</p>
    <button type="button" class="modal-close mt-8"><?php echo __t("Close") ?></button>
    </div>
</div>`)
$(".loader-container").removeClass("loading");

                })
                .fail(function(err, textStatus, errorMessage) {
                    $(".loader-container").removeClass("loading");
                    if(err.responseText){
                        $("#sales-form .form-wrapper .form-err").html(
                        '<div class="alert alert-danger text-center">'+JSON.parse(err.responseText).message+'</div>'
                    );
                    }
                    else{
                        $("#sales-form .form-wrapper .form-err").html(
                        '<div class="alert alert-danger text-center"><?php echo __t("Could not reach server, please try again later.") ?></div>'
                    );
                    }
                   
                });
            event.preventDefault();
        });


        // upload files
        function returnFileSize(number) {
            if (number < 1024) {
                return `${number} bytes`;
            } else if (number >= 1024 && number < 1048576) {
                return `${(number / 1024).toFixed(1)} KB`;
            } else if (number >= 1048576) {
                return `${(number / 1048576).toFixed(1)} MB`;
            }
        }

        $(document).on('click', '[data-app-form-uploader-delete-btn]', function() {
            dataTransfer.items.remove($(this).attr('tabIndex'))
            $('#attachments')[0].files = dataTransfer.files;
            $(this).parents(".l-uploader-file-item").remove();
            Array.from($('#attachments')[0].files).forEach(function(currFile, i) {
                $("[data-app-form-uploader-delete-btn]").eq(i).attr("tabIndex", i);
            })
        })

        var $fileInput = $('.file-input');
        var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
        $fileInput.on('change', function() {
            var filesCount = $(this)[0].files.length;
            var $textContainer = $(this).prev();
            Array.from($(this)[0].files).forEach(function(currFile, i) {
                dataTransfer.items.add(currFile);
                $('#attachments')[0].files = dataTransfer.files;
                var fileType = currFile["type"];
                var reader = new FileReader();
                reader.onload = function(e) {

                    $(".ui-uploader-file-items").append(`<div class="l-uploader-file-item ui-uploader-file-item l-uploader-file-item--multiple ui-uploader-file-item--multiple draggable" draggable="true">
                    <div class="l-uploader-file-item-img-container ui-uploader-file-item-img-container" data-app-form-uploader-img-container="true" style="display: ${($.inArray(fileType, validImageTypes) < 0) ? "none" : "inline-block"};">
                                <img class="ui-uploader-file-item-img l-uploader-file-item-img" data-app-lightbox="${e.target.result}" src="${e.target.result}">
                            </div>
                            <div class="l-uploader-file-item-icon-container ui-uploader-file-item-icon-container" data-app-form-uploader-icon-container="true" style="display: ${($.inArray(fileType, validImageTypes) < 0) ? "inline-block" : "none"};">
                                <i class="ui-uploader-file-item-icon l-uploader-file-item-icon mdi mdi-file-image"></i>
                                
                            </div>
                            <span class="l-uploader-file-item-name ui-uploader-file-item-name" title="${currFile.name}">${currFile.name}</span>
                            <span class="l-uploader-file-item-size ui-uploader-file-item-size" title="${returnFileSize(currFile.size)}">${returnFileSize(currFile.size)}</span>
                            <div class="l-uploader-file-item-actions">
                                <i class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-trash-can u-text-color-red" tabindex="${$('#attachments')[0].files.length -1}" data-app-keyboard-support="true"  title="Delete" data-app-form-uploader-delete-btn="true"></i>
                            </div>
                        </div>`)

                }
                reader.readAsDataURL(currFile);
            })

        });
    });
</script>