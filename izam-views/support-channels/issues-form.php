<?php
$domain_name = Domain_Name_Only == "daftra" ? __t("Daftra") : __t("Enerpize");
$support_url = Domain_Name_Only == "daftra" ? "https://docs.daftra.com/" : "https://docs.enerpize.com/";
?>
<div class="page-container">
    <form action="#" name="sales-form" id="sales-form">
        <div class="loader-container">
            <div class="inner-loader"></div>
        </div>
        <div class="hidden modal-type"></div>
        <div class="form-wrapper">
            <div class="form-header">
                <i class="mdi mdi-lifebuoy item-icon form-icon"></i>
                <div class="form-header-description">
                    <h2 class="title"><?php echo __t("I want to report an issue or suggestion");?></h2>
                    <p class="desc">
                        <?php echo __t("Fill out the form and we'll get you the help you need.");?>
                    </p>
                </div>
            </div>

            <div class="form-err mt-5 mb-5"></div>

            <div class="form-group  mt-5">

                <label for="subject"><span class="businessname_label"><?php echo __t("Inquiry Type");?> </span> <span class="required">*</span></label>
                <div class="issues-radio-group">
                    <div class="radio-container">
                        <div class="form-check">
                            <input class="ui-radio ui-radio--color-primary  issue-type" type="radio" name="defaultRadio" id="assistance-radio" checked radio-data="assistance">
                            <label class="form-check-label" for="assistance-radio">
                                <?php echo __t("Need Assistance");?>
                            </label>
                        </div>
                    </div>
                    <div class="radio-container">
                        <div class="form-check">
                            <input class=" ui-radio ui-radio--color-primary issue-type" type="radio" name="defaultRadio" id="suggestion-radio" radio-data="suggestion">
                            <label class="form-check-label" for="suggestion-radio">
                                <?php echo __t("Send Suggestion");?>
                            </label>
                        </div>
                    </div>
                </div>
            </div>


            <div class="l-input-box">
                <label class="l-input-label ui-input-label" for="subject"><span class="l-input-label ui-input-label"><?php echo __t("Complaint Subject");?> </span> <span class="required">*</span></label>
                <input name="subject" type="text" class="l-input ui-input" maxlength="100" value="" id="subject" required placeholder="<?php echo __t("What is this message about?");?>">
                <div class="clear"></div>
            </div>


            <div class="l-input-box">
                <label class="l-input-label ui-input-label" for="Message"><?php echo __t("Complaint Description");?> <span class="required">*</span></label>
                <textarea name="data[Client][notes]" rows="5" class="l-input ui-input" cols="30" id="Message" required placeholder="<?php echo __t("Please tell us about your inquiry.");?>"></textarea>
            </div>

            <div class="l-input-box">
                <label class="l-input-label ui-input-label" for="phone"><span class="l-input-label ui-input-label"><?php echo __t("Phone Number");?> </span></label>
                <input name="phone" type="text" class="l-input ui-input" maxlength="100" value="" id="phone" placeholder="<?php echo __t("Phone Number");?>">
                <div class="clear"></div>
            </div>

            <div class="form-group attachments-input">
                <div class="l-input-box">
                    <label class="l-input-label ui-input-label"><?php echo __t("Attachments");?></label>
                    <input class="hidden" type="file" id="attachments" name="images[]" multiple style="display:none;">

                    <div class="attachments-container">
                        <div class="file-drop-area l-uploader ui-uploader">

                            <input class="file-input" type="file" name="images[]" multiple>
                            <div class="ui-uploader-drop-area l-uploader-drop-area">
                                <span class="ui-uploader-drop-area-icon l-uploader-drop-area-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="33.002" height="44.003" viewBox="0 0 33.002 44.003">
                                        <path d="M33-47.27a2.369,2.369,0,0,0-.6-1.459L23.98-57.146a2.37,2.37,0,0,0-1.459-.6H22v11H33ZM21.314-44a2.069,2.069,0,0,1-2.063-2.063V-57.75H2.063A2.063,2.063,0,0,0,0-55.687V-15.81a2.063,2.063,0,0,0,2.063,2.063H30.939A2.063,2.063,0,0,0,33-15.81V-44ZM9.672-42.624A4.125,4.125,0,0,1,13.8-38.5a4.125,4.125,0,0,1-4.125,4.125A4.125,4.125,0,0,1,5.547-38.5,4.125,4.125,0,0,1,9.672-42.624ZM27.548-22h-22l.042-4.167,3.4-3.4a.983.983,0,0,1,1.417.042l3.4,3.4,8.9-8.9a1.031,1.031,0,0,1,1.459,0l3.4,3.4Z" transform="translate(0 57.75)" fill="#e4ebf2"></path>
                                    </svg>
                                </span>
                                <span class="ui-uploader-drop-area-text l-uploader-drop-area-text">
                                    <i class="ui-uploader-drop-area-text-upload-icon fal fa-cloud-upload s2020 ui-icon--size-20 u-text-color-primary"></i>
                                    <p style="margin-bottom: 0;padding-bottom:0;"><span>&nbsp;<?php echo __t("Drop file here or");?>&nbsp;</span><span class="u-text-color-primary"><?php echo __t("select from your computer");?></span></p>
                                </span>
                            </div>
                        </div>

                    </div>
                    <div class="l-uploader-file-items l-uploader-file-items--spacing-top ui-uploader-file-items" style="max-height: none;">
                    </div>

                </div>
            </div>
            <div class="btn-container">

                <button type="button" class="btn btn-disabled submit-form other-btn"><?php echo __t("Other <br/> Contact Methods");?></button>
                <button type="submit" class="btn btn-success submit-form"><?php echo __t("SEND");?></button>

            </div>
    </form>

</div>




<script>
    $(document).ready(function() {
        (new window.IzLayout.UiUploader).initAllInNode($('.attachments-input'));
        var isAssistance = true;
        $(document).on('input', '#subject', function(event) {
            $(this).attr('value', event.target.value);
        })

        $(document).on('input', '#Message', function(event) {
            $(this).text(event.target.value);
        })


        $(document).on('click', '.modal-close', function() {
            IzamModal.closeModals()
        })


        $(document).on('click', '.other-btn', function() {
            IzamModal.addUrlModal('/v2/owner/support-channels/contact-info', '<i class="mdi mdi-phone fs-40 text-blue-s2020"></i>', 'Contact Information')
        })

        $(document).on('change', '.issue-type', function() {
            if ($(this).attr('radio-data') == 'suggestion') {
                isAssistance = false
            }
        })



        var assistanceHTML = `<div class="success-container">
    <div class="content-container">
    <div class="mb-5 text-center">
    <svg xmlns="http://www.w3.org/2000/svg" width="104.416" height="86.414" viewBox="0 0 104.416 86.414">
  <g id="Group_56437" data-name="Group 56437" transform="translate(-20.029 50)">
    <g id="MDI_lifebuoy" data-name="MDI / lifebuoy" transform="translate(19.55 -50)">
      <g id="Boundary" transform="translate(0.479 0)" fill="#1877f2" stroke="rgba(0,0,0,0)" stroke-width="1" opacity="0">
        <rect width="85" height="85" stroke="none"/>
        <rect x="0.5" y="0.5" width="84" height="84" fill="none"/>
      </g>
      <path id="Path_lifebuoy" data-name="Path / lifebuoy" d="M65.12,49.58a30.217,30.217,0,0,0,0-24.2l-9.722,4.4a19.511,19.511,0,0,1,.035,15.4l9.686,4.4M49.615,9.841a30.3,30.3,0,0,0-24.233,0l4.4,9.686a19.6,19.6,0,0,1,15.434.035l4.4-9.722M9.841,25.346a30.387,30.387,0,0,0,0,24.269l9.722-4.435a19.448,19.448,0,0,1,0-15.434l-9.722-4.4M25.382,65.12a30.156,30.156,0,0,0,24.233-.035l-4.4-9.686a19.512,19.512,0,0,1-15.4.035L25.382,65.12M37.481,2A35.481,35.481,0,1,1,2,37.481,35.481,35.481,0,0,1,37.481,2m0,21.288A14.192,14.192,0,1,0,51.673,37.481,14.192,14.192,0,0,0,37.481,23.288Z" transform="translate(5.019 5.019)" fill="#13b272"/>
      <path id="Path_check-bold" data-name="Path / check-bold" d="M21.753,52.333,2.79,33.37l8.642-8.642L21.753,35.08,51.922,4.88l8.642,8.642Z" transform="translate(42.917 32.667)" fill="#13b272" stroke="#fff" stroke-width="2"/>
    </g>
  </g>
</svg>
</div>
    <h2><?php echo __t("Your assistance request has been received,");?></h2>
    <h2 class="mb-8"><?php echo __t("Thank you for reaching out!");?></h2>
    <p><?php echo __t("You are an invaluable part of everything we do here. And we're absolutely thrilled to provide the needed assistance. We will get in touch with you as soon as possible on your registered email");?>: <a href="mailto:<EMAIL>"><?= getCurrentSite('email')?></a>.</p>
    <p class="mt-5"><?php echo __t("In the interim, please visit");?> <a class="internal-link" href="<?= $support_url ?>" target="_blank"><?php echo sprintf(__t("%s Help Center"), $domain_name);?></a> <?php echo __t("as it may provide the needed assistance.");?></p>
    <button type="button" class="modal-close mt-8"><?php echo __t("Close");?></button>
    </div>
</div>`

        var suggestionHTML = `<div class="success-container">
    <div class="content-container">
    <div class="mb-5 text-center">
    <svg xmlns="http://www.w3.org/2000/svg" width="104.416" height="86.414" viewBox="0 0 104.416 86.414">
  <g id="Group_56437" data-name="Group 56437" transform="translate(-20.029 50)">
    <g id="MDI_lifebuoy" data-name="MDI / lifebuoy" transform="translate(19.55 -50)">
      <g id="Boundary" transform="translate(0.479 0)" fill="#1877f2" stroke="rgba(0,0,0,0)" stroke-width="1" opacity="0">
        <rect width="85" height="85" stroke="none"/>
        <rect x="0.5" y="0.5" width="84" height="84" fill="none"/>
      </g>
      <path id="Path_lifebuoy" data-name="Path / lifebuoy" d="M65.12,49.58a30.217,30.217,0,0,0,0-24.2l-9.722,4.4a19.511,19.511,0,0,1,.035,15.4l9.686,4.4M49.615,9.841a30.3,30.3,0,0,0-24.233,0l4.4,9.686a19.6,19.6,0,0,1,15.434.035l4.4-9.722M9.841,25.346a30.387,30.387,0,0,0,0,24.269l9.722-4.435a19.448,19.448,0,0,1,0-15.434l-9.722-4.4M25.382,65.12a30.156,30.156,0,0,0,24.233-.035l-4.4-9.686a19.512,19.512,0,0,1-15.4.035L25.382,65.12M37.481,2A35.481,35.481,0,1,1,2,37.481,35.481,35.481,0,0,1,37.481,2m0,21.288A14.192,14.192,0,1,0,51.673,37.481,14.192,14.192,0,0,0,37.481,23.288Z" transform="translate(5.019 5.019)" fill="#13b272"/>
      <path id="Path_check-bold" data-name="Path / check-bold" d="M21.753,52.333,2.79,33.37l8.642-8.642L21.753,35.08,51.922,4.88l8.642,8.642Z" transform="translate(42.917 32.667)" fill="#13b272" stroke="#fff" stroke-width="2"/>
    </g>
  </g>
</svg>
</div>
    <h2><?php echo __t("Your suggestion has been received,");?></h2>
    <h2 class="mb-8"><?php echo __t("Thank you for reaching out!")?></h2>
    <p><?php echo sprintf(__t("You are an invaluable part of everything we do at %s. And we're absolutely thrilled to hear from you. Be it your suggestions, ideas, or even criticism, we will get in touch with you as soon as possible on your registered email"), $domain_name);?>: <a href="mailto:<EMAIL>"><?= getCurrentSite('email')?></a>.</p>
    <button type="button" class="modal-close mt-8"><?php echo __t("Close");?></button>
    </div>
</div>`

        var dataTransfer = new DataTransfer();

        $('#sales-form').submit(function(event) {
            $(".loader-container").addClass("loading");
            var data = new FormData();
            $.each($('#attachments')[0].files, function(i, file) {
                data.append('file-' + i, file);
            });
            data.append('subject', $("#subject").val()),
                data.append('message', $("#Message").val()),
                data.append('type', 1),
                data.append('support_channel', 1),
                data.append('phone', $("#phone").val())
                event.preventDefault();
            $.ajax({
                    type: "POST",
                    url: "/contact",
                    data: data,
                    contentType: false,
                    processData: false,
                    method: "POST",
                    encode: true,
                })
                .done(function(data) {
                    $(".loader-container").removeClass("loading");
                    if (isAssistance) {
                            $('.forms-modal').html(assistanceHTML);
                        } else {
                            $('.forms-modal').html(suggestionHTML);

                        }
                })
                .fail(function(data) {
                    $(".loader-container").removeClass("loading");
                    $(".loader-container").removeClass("loading");
                    if(err.responseText){
                        $("#sales-form .form-wrapper .form-err").html(
                        '<div class="alert alert-danger text-center">'+ JSON.parse(err.responseText).message + '</div>'
                    );
                    }
                    else{
                        $("#sales-form .form-wrapper .form-err").html(
                        '<div class="alert alert-danger text-center"><?php echo __t("Could not reach server, please try again later.") ?></div>'
                    );
                    }
              
                });
        });

        if ($('.modal-type').text() == 'suggest') {
            $('#suggestion-radio').attr('checked', true);
        }


        // upload files
        function returnFileSize(number) {
            if (number < 1024) {
                return `${number} bytes`;
            } else if (number >= 1024 && number < 1048576) {
                return `${(number / 1024).toFixed(1)} KB`;
            } else if (number >= 1048576) {
                return `${(number / 1048576).toFixed(1)} MB`;
            }
        }

        $(document).on('click', '[data-app-form-uploader-delete-btn]', function() {
            dataTransfer.items.remove($(this).attr('tabIndex'))
            $('#attachments')[0].files = dataTransfer.files;
            $(this).parents(".l-uploader-file-item").remove();
            $('#attachments')[0].files.forEach(function(currFile, i) {
                $("[data-app-form-uploader-delete-btn]").eq(i).attr("tabIndex", i);
            })
        })

        var $fileInput = $('.file-input');
        var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
        $fileInput.on('change', function() {
            var filesCount = $(this)[0].files.length;
            var $textContainer = $(this).prev();
            
            $(this)[0].files.forEach(function(currFile, i) {
                dataTransfer.items.add(currFile);
                $('#attachments')[0].files = dataTransfer.files;
                var fileType = currFile["type"];
                var reader = new FileReader();
                reader.onload = function(e) {

                    $(".ui-uploader-file-items").append(`<div class="l-uploader-file-item ui-uploader-file-item l-uploader-file-item--multiple ui-uploader-file-item--multiple draggable" draggable="true">
                    
                            <div class="l-uploader-file-item-img-container ui-uploader-file-item-img-container" data-app-form-uploader-img-container="true" style="display: ${($.inArray(fileType, validImageTypes) < 0) ? "none" : "inline-block"};">
                                <img class="ui-uploader-file-item-img l-uploader-file-item-img" data-app-lightbox="${e.target.result}" src="${e.target.result}">
                            </div>
                            <div class="l-uploader-file-item-icon-container ui-uploader-file-item-icon-container" data-app-form-uploader-icon-container="true" style="display: ${($.inArray(fileType, validImageTypes) < 0) ? "inline-block" : "none"};">
                                <i class="ui-uploader-file-item-icon l-uploader-file-item-icon mdi mdi-file-image"></i>
                                
                            </div>
                            <span class="l-uploader-file-item-name ui-uploader-file-item-name" title="${currFile.name}">${currFile.name}</span>
                            <span class="l-uploader-file-item-size ui-uploader-file-item-size" title="${returnFileSize(currFile.size)}">${returnFileSize(currFile.size)}</span>
                            <div class="l-uploader-file-item-actions">
                                <i class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-trash-can u-text-color-red" tabindex="${$('#attachments')[0].files.length -1}" data-app-keyboard-support="true"  title="Delete" data-app-form-uploader-delete-btn="true"></i>
                            </div>
                        </div>`)

                }
                reader.readAsDataURL(currFile);
            })

        });
    });
</script>