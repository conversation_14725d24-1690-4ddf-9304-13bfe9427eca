<div class="d-block d-lg-none">

    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table">
                <thead data-lt-head="true">
                <tr>
                    <th width="50%" class="listing-cell-start"></th>
                    <th></th>
                    <th width="45" class="listing-cell-end text-end">
                        <?php $this->includeSection('treasuries/partials/sort-options'); ?>
                    </th>
                </tr>
                </thead>
                <tbody>

                <?php foreach ($pagination as $record) {
                    $showUrl = $this->url(array('controller' => 'treasuries', 'action' => 'view', $record->id), 'cake') . '?' . $page_url_param;
                    if ($record->is_primary) {
                        $status_class = "bg-primary";
                        $status = __t("Primary", true);
                    } else if ($record->active == 1) {
                        $status_class = "bg-active";
                        $status = __t("Active", true);
                    } else {
                        $status_class = "bg-inactive";

                        $status = __t("Inactive", true);
                    }

                    if ($record->type == 1) {
                        $treasury_type_text = __t('Bank Account', true);
                        $treasury_type_class = 'text-success mdi mdi-bank';
                    } else {
                        $treasury_type_text = __t('Treasury', true);
                        $treasury_type_class = 'mdi mdi-wallet  text-primary';
                    }
                    ?>
                    <tr tabindex="0">
                        <td>
                            <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                            <div class="title-text d-flex gap-5">
                                <?php if (isset($metainfo['bank']['portal_bank']['bank_logo']) && strlen($metainfo['bank']['portal_bank']['bank_logo']) > 0) { ?>
                                    <img src="<?php echo \Izam\Aws\Aws::getPermanentUrl($metainfo['bank']['portal_bank']['bank_logo']); ?>" class="thumb thumb-sm" width="24" height="24" alt="<?= $record->name ?>" />
                                <?php } else { ?>
                                    <i class="<?= $treasury_type_class ?>"></i>
                                <?php }?>
                                <div>
                                    <p class="lh-1"><?= $record->name ?></p>
                                    <small class="title-subtitle-text"><?= $treasury_type_text ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                            <div class="text-end">
                                <?php if (!empty($record->currency_code) && $record->currency_code != $system_currency) { ?>
                                    <div>
                                        <strong><?= format_price($treasuryBalances[$record->id]['balance'], $record->currency_code) ?></strong>
                                    </div>

                                    <?php
                                    if (isset($treasuryBalances[$record->id]['rate'])) {
                                        $tooltips_title = 1 . $record->currency_code  . ' = ' . ($treasuryBalances[$record->id]['rate'])  . $system_currency;
                                        ?>
                                        <div>
                                            <abbr class="text-light-3 fs-7" data-bs-toggle="tooltip" data-bs-html="true" title="<?= $tooltips_title ?>">
                                                <span><?= format_price($treasuryBalances[$record->id]['defaultBalance'], $system_currency) ?></span>
                                                <i class="mdi mdi-help-circle"></i>
                                            </abbr>
                                        </div>
                                    <?php } ?>
                                <?php } else { ?>
                                    <div>
                                        <strong><?= format_price($treasuryBalances[$record->id]['balance'], $system_currency) ?></strong>
                                    </div>
                                <?php } ?>
                                <div class="d-inline-flex">
                                    <div class="status-circle">
                                        <i class="<?= $status_class ?>"></i>
                                        <span><?= $status ?></span>
                                    </div>
                                </div>
                                <?php if (isset($metainfo['callback']['connection_id'])): ?>
                                    <div class="mt-2">
                                    <span class="badge bg-success d-inline-flex align-items-center">
                                        <span><?php echo __t('Connected'); ?></span>
                                        <i class="mdi mdi-link fs-10 ms-2"></i>
                                    </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                            <?= $this->includeSection('treasuries/partials/listing-actions', ['record' => $record,'page_url_param'=>$page_url_param]) ?>
                        </td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>