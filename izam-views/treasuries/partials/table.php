<div class="d-none d-lg-block">
    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table" data-lt="true">
                <colgroup>
                    <col />
                    <col />
                    <col />
                    <col />
                    <col />
                </colgroup>
                <thead data-lt-head="true">
                <tr>
                    <th><?= __t('Name') ?></th>
                    <th width="32%"><?= __t('Description') ?></th>
                    <th><?= __t('Amount') ?></th>
                    <th><?= __t('Status') ?></th>
                    <th width="45" class="listing-cell-end text-end">
                        <?php $this->includeSection('treasuries/partials/sort-options'); ?>
                    </th>
                </tr>
                </thead>
                <tbody>

                <?php
                foreach ($pagination as $record) {
                    $showUrl = $this->url(array('controller' => 'treasuries', 'action' => 'view', $record->id), 'cake') . '?' . $page_url_param;
                    $metainfo = json_decode($record->metainfo ?? "", true);
                    ?>

                    <?php
                    // dump($record);
                    if ($record->is_primary) {
                        $status_class = "bg-primary";
                        $status = __t("Primary", true);
                    } else if ($record->active == 1) {
                        $status_class = "bg-active";
                        $status = __t("Active", true);
                    } else {
                        $status_class = "bg-inactive";

                        $status = __t("Inactive", true);
                    }

                    if ($record->type == 1) {
                        $treasury_type_text = __t('Bank Account', true);
                        $treasury_type_class = 'text-success mdi mdi-bank';
                    } else {
                        $treasury_type_text = __t('Treasury', true);
                        $treasury_type_class = 'mdi mdi-wallet  text-primary';
                    }

                    ?>
                    <tr tabindex="0">
                        <td>
                            <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                            <div class="text-nowrap">
                                <div class="title-big-text">
                                    <?php if (isset($metainfo['bank']['portal_bank']['bank_logo']) && strlen($metainfo['bank']['portal_bank']['bank_logo']) > 0) { ?>
                                        <img src="<?php echo \Izam\Aws\Aws::getPermanentUrl($metainfo['bank']['portal_bank']['bank_logo']); ?>" class="thumb thumb-sm" width="24" height="24" alt="<?= $record->name ?>" />
                                    <?php } else { ?>
                                        <i class="<?= $treasury_type_class ?>"></i>
                                    <?php }?>
                                    <div>
                                        <h5><?= $record->name ?></h5>
                                        <p><?= $treasury_type_text ?></p>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                            <div class="text-long text-long-2-lines" data-bs-toggle="tooltip" data-bs-html="true" title="<?= $record->description ?>" style="max-width: 90%">
                                <?= mb_strimwidth($record->description, 0, 115, '...'); ?>
                            </div>
                        </td>
                        <td>
                            <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                            <?php if (!empty($record->currency_code) && $record->currency_code != $system_currency) { ?>
                                <div>
                                    <span class="text-amount"><?= format_price($treasuryBalances[$record->id]['balance'], $record->currency_code) ?></span>
                                </div>

                                <?php
                                if (isset($treasuryBalances[$record->id]['rate'])) {
                                    $tooltips_title = 1 . $record->currency_code  . ' = ' . ($treasuryBalances[$record->id]['rate'])  . $system_currency;
                                    ?>
                                    <div>
                                        <abbr class="text-light-3 fs-7" data-bs-toggle="tooltip" data-bs-html="true" title="<?= $tooltips_title ?>">
                                            <span><?= format_price($treasuryBalances[$record->id]['defaultBalance'], $system_currency) ?></span>
                                            <i class="mdi mdi-help-circle"></i>
                                        </abbr>
                                    </div>
                                <?php } ?>
                            <?php } else { ?>
                                <div>
                                    <span class="text-amount"><?= format_price($treasuryBalances[$record->id]['balance'], $system_currency) ?></span>
                                </div>
                            <?php } ?>
                        </td>
                        <td>
                            <a href="<?= $showUrl ?>" class="listing-cell-link"></a>
                            <div class="text-nowrap">
                                <div class="status-circle">
                                    <i class="<?= $status_class ?>"></i>
                                    <span><?= $status ?></span>
                                </div>
                                <?php if (isset($metainfo['callback']['connection_id'])): ?>
                                    <div class="mt-2">
                                    <span class="badge bg-success d-inline-flex align-items-center">
                                        <span><?php echo __t('Connected'); ?></span>
                                        <i class="mdi mdi-link fs-10 ms-2"></i>
                                    </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                            <?= $this->includeSection('treasuries/partials/listing-actions', ['record' => $record, 'page_url_param' => $page_url_param]) ?>
                        </td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>