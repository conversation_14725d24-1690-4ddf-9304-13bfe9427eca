<?php 
    if (!function_exists('getFileItemIcon')) {
        function getFileItemIcon($ext) {
            switch ($ext) {
                case "xls":
                case "xlsx":
                    return "mdi mdi-file-excel";
                    break;
                case "doc":
                case "docx":
                    return "mdi mdi-file-word";
                    break;
                case "pdf":
                    return "mdi mdi-file-pdf-box";
                    break;
                case "jpg":
                case "jpeg":
                case "png":
                case "gif":
                    return "mdi mdi-file-image";
                    break;
                case "txt":
                case "csv":
                    return "mdi mdi-file-document";
                    break;
                case "zip":
                case "rar":
                    return "mdi mdi-file-key";
                    break;

                default:
                    return "mdi mdi-file";
                    break;
            }
        }
    }

    $docsCount = count($attachments);
    $showCountSpan = false;
    if ($docsCount > 3 ){
        $showCountSpan = true;
    }
?>

<?php if ($attachments && (is_array($attachments) || $attachments->toArray())) { ?>
<div class="hstack align-items-center gap-4">


    <?php
    $aws = new \Izam\Aws\Aws();
    foreach ($attachments as $key => $value) :
        if ($key > 2 ) break;
        $value = $value->file;
        # Cut name of IMage
        $imgName =  explode(' ', $value->name);
        $size = array('B', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');
        $ext = pathinfo(end($imgName), PATHINFO_EXTENSION);
        $fileNameShort = str_replace('.' . $ext, '', end($imgName));
        if (strlen($fileNameShort) > 10) {
            $fileNameShort = substr($fileNameShort, 0, 10) . '...';
        }
        $ext = strtolower($ext);
        $thump =     getFileItemIcon($ext);
        $isImage = in_array($ext, ['png', 'jpeg', 'jpg', 'gif']);
        $factor = floor((strlen($value->file_size) - 1) / 3);

        $file_size = sprintf("%.2f", $value->file_size / pow(1024, $factor)) . @$size[$factor];
    ?>



    <?php if ($isImage) { ?>

    <a class="thumb-link" data-bs-toggle="tooltip" data-fancybox="" target="_blank"
        href="<?= $aws->getProxyUrl($value->path) ?>" data-src="<?= $aws->getProxyUrl($value->path) ?>"
        data-bs-title="<?=$value->name?>" aria-label="<?=$value->name?>" data-bs-original-title="<?=$value->name?>">
        <img class="thumb" loading="lazy" src="<?= $aws->getProxyUrl($value->path) ?>">
    </a>

    <?php } else { ?>

    <a class="thumb-link" data-bs-toggle="tooltip" data-fancybox="" data-src="<?= $aws->getProxyUrl($value->path) ?>" target="_top" href="<?= $aws->getProxyUrl($value->path) ?>"
        data-bs-title="<?=$value->name?>" aria-label="<?=$value->name?>" data-bs-original-title="<?=$value->name?>"
        download="<?=$value->name?>">
        <i class="thumb thumb-empty <?=$thump?>"></i>
    </a>

    <?php } ?>



    <?php endforeach;?>


    <?php if ($showCountSpan): ?>
    <a data-view-link="true" href="<?= $view_url ?>">
        <span class="thumb"  target="_parent" data-bs-title="+<?= $docsCount - 3 ?>"
              data-bs-toggle="tooltip">+<?= $docsCount - 3 ?></span>
    </a>
    <?php endif; ?>


</div>
<?php } ?>

<script>
    $(document).on("click", 'a[data-view-link="true"]', function (e) {
        e.preventDefault(); // prevent opening inside iframe
        window.parent.location.href = $(this).attr("href"); // open in parent window
    });
</script>
