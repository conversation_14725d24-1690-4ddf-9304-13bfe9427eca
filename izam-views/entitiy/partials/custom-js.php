<script>
    $(document).ready(function () {
        initAllInputs();
    });

    var dynamicDropdownSelector = '[type=select],[type=dynamic_dropdown]',
        dateTimeInput = '[type="date_time_picker"],[type="datetime"]',
        dateInput = '[type="date"]',
        timeInput = '[data-input-type="time"]',
        multipleDynamicDropdownSelector = '[data-select-input="input-multiple-dynamic-dropdown"]',
        linkListInput = '[data-input-type="link_list"]',
        autoSuggestInput = '[data-input-type="auto_suggest"]',
        assignedStaffInput = '[data-input-type="assigned-staff"]',
        dateRangeInput = '[data-date-input="date-range"]',
        currencyDropdownSelector = '[type="currency_dropdown"]',
        currencyRangeSelector = '[type="currency_range"]',
        followUpStatusSelector = '[data-input-type="follow_up_status"]',
        timeRangePickerInput = '[data-input-type="time_range_picker"]',
        tagInput = '[type="tags"]',
        uploaderInput = '[data-app-form-uploader-options]:not([data-app-form-uploader])',
        formattedTextInput = '[type="formatted_text"]',
        phoneCodeInput = '[data-input-phone-code]',
        phoneNumberInput = '[data-phone]',
        subForm = '.subform-container',
        conditionInput = '[type="condition"]',
        tagRadio = '[data-select-input-with-radio]',
        inputMap = '[data-input-map]';


    function initAllInputs() {
        initDynamicDropdown($(dynamicDropdownSelector));
        initDateTime($(dateTimeInput));
        initDate($(dateInput));
        initTime($(timeInput));
        initMultipleDynamicDropdownSelector($(multipleDynamicDropdownSelector));
        initLinkListInput($(linkListInput));
        initAutoSuggestInput($(autoSuggestInput));
        initAssignedStaffInput($(assignedStaffInput));
        initDataRangeInput($(dateRangeInput));
        initCurrencyRangeSelector($(currencyRangeSelector));
        initCurrencyDropdownSelector($(currencyDropdownSelector));
        initFollowUpStatusSelector($(followUpStatusSelector));
        initTimeRangePickerInput($(timeRangePickerInput));
        initTagInput($(tagInput));
        initUploaderInput($(uploaderInput));
        initFormatedTextInput($(formattedTextInput));
        initPhoneCodeInput($(phoneCodeInput));
        initPhoneNumberInput($(phoneNumberInput));
        initSubForm($(subForm));
        initConditionInput($(conditionInput));
        // initTagRadioInput($(tagRadio));
    }

</script>

<script>
    function initDynamicDropdown($inputs) {
        $inputs.each(function () {
            $(this).selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter, {}));
        })
    }

    function initDateTime($inputs) {
        $inputs.each(function () {
            $(this).flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.dateTimeFilter, {
                positionElement: $(this).closest('[data-form-group]').get(0),
            }));
        });
    }

    function initDate($inputs) {
        $inputs.each(function () {
            var self = this;
            $(self).flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.dateFilter, {
                positionElement: $(self).closest('[data-form-group]').get(0),
                onOpen: function(selectedDates, dateStr, instance) {
                    setTimeout(function() {
                        var $parent = $(self).closest('[data-form-group]');
                        $(instance.calendarContainer).css({
                            width: $parent.outerWidth() + 'px'
                        });
                        instance._positionCalendar();
                    }, 10);
                }
            }));
        });
    }

    function initTime($inputs) {
        $inputs.each(function () {
            $(this).flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.time, {
                positionElement: $(this).closest('[data-form-group]').get(0),
            }));
        });
    }

    function initMultipleDynamicDropdownSelector($inputs) {
        $inputs.each(function () {
            $(this).selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multipleFilter, {
                plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multipleFilter.plugins, {})
            }));
            $(this).selectize(window.APP.VENDORS.DEFAULTS.selectize.multipleFilter);
        });
    }

    function initLinkListInput($input) {
        if (!$input.length){
            return;
        }
        var dataObject = $input.data('app-form-select-options');
        var linkListOptions = Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter, {
            valueField: "id",
            labelField: "name",
            load: function (query, callback) {
                var url = dataObject.ajax.url;
                if (typeof this.$input.attr('multiple') == 'undefined') {
                    this.clearOptions();
                } else {
                    this.clearUnselectedOptions();
                }
                if (!query.length) return callback();
                $.ajax({
                    url: url.replaceAll('__q__', encodeURIComponent(query)),
                    type: "GET",
                    dataType: 'json',
                    error: function () {
                        callback();
                    },
                    success: function (response) {
                        if (response.data) {
                            callback(response.data);
                        } else if (response.results) {
                            callback(response.results);
                        } else if (Array.isArray(response)) {
                            callback(response);
                        } else {
                            callback([]);
                        }
                    },
                });
            },
            onChange: function (selectedOptionValue) {
                url = $input.data('app-form-select-options').redirect;
                if (url) {
                    window.top.location = url.replace('__value__', selectedOptionValue);
                }
            },
            plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter.plugins, {
                'template': {
                    'name': 'user'
                }
            }),
        })
        var template = $input.data('template');
        if (template) {
            linkListOptions.render = {
                item: function (item, escape) {
                    var out = template;
                    for (var i in item) {
                        out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                    }
                    out = out.replaceAll('()', '');
                    return "<div class='item'>" + out + "</div>";
                },
                option: function (item, escape) {
                    var out = template;
                    for (var i in item) {
                        out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                    }
                    out = out.replaceAll('()', '');
                    return "<div class='option'>" + out + "</div>";
                },
            }
        }
        $input.selectize(linkListOptions);
    }

    function initAutoSuggestInput($inputs) {
        $inputs.each(function () {
            var dataObject = $(this).data('app-form-select-options');
            var dataParsing = $(this).data('parsing-option');
            var staticOptions = $(this).data('static-options');
            var formRules = Boolean($(this).data('form-rules'));
            var $input = $(this);
            var optionGroups = $(this).data('options-group');
            var baseOptions = Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter);
            if (typeof $input.attr('multiple') != 'undefined') {
                baseOptions = Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multipleFilter);
            }
            var options = Object.assign({}, baseOptions, {
                valueField: $(this).data('value'),
                labelField: $(this).data('label'),
                searchField: [$(this).data('label')],

                load: function (query, callback) {
                    var that = this;
                    var selectizeInstance = this;
                    var url = dataObject.ajax.url;
                    if (typeof this.$input.attr('multiple') == 'undefined') {
                        this.clearOptions();
                    } else {
                        this.clearUnselectedOptions();
                    }
                    if (!query.length) {
                        this.clearOptions();
                        this.refreshOptions();
                        return callback();
                    }
                    $.ajax({
                        url: url.replaceAll('__q__', encodeURIComponent(query)),
                        type: "GET",
                        dataType: 'json',
                        error: function () {
                            callback();
                        },
                        success: function (response) {
                            var responseResults = [];
                            if (response.data) {
                                responseResults = response.data;
                            } else if (response.results) {
                                responseResults = response.results;
                            } else if (Array.isArray(response)) {
                                responseResults = response;
                            }
                            if (responseResults.length && typeof optionGroups !== 'undefined' && Object.values(optionGroups).length) {
                                for (i = 0; i < responseResults.length; i++) {
                                    if (responseResults[i].details !== "" && typeof responseResults[i].details != 'undefined') {
                                        var categoriesIds = responseResults[i].details.split(',').filter(function (i) {
                                            return i != -1
                                        });
                                        var name = '';
                                        for (x = 0; x < categoriesIds.length; x++) {
                                            var categoryId = categoriesIds[x];
                                            name += optionGroups[categoryId] + (x !== categoriesIds.length - 1 ? ' > ' : '');
                                        }
                                        selectizeInstance.addOptionGroup(responseResults[i].details, {
                                            value: responseResults[i].details,
                                            label: name
                                        });
                                    }
                                }
                            }
                            if(staticOptions) {
                                //merge static options and response results
                                responseResults = [...staticOptions, ...responseResults];
                            }
                            if(responseResults.length == 0) {
                                that.refreshOptions();
                            }
                            callback(responseResults);
                        },
                    });
                },
                plugins: Object.assign({}, baseOptions.plugins, {
                    'icon': {
                        'class': formRules ? '' : 'mdi mdi-magnify'
                    },
                    'template': {
                        'name': 'user'
                    },
                }),
            });
            if (typeof (optionGroups) != 'undefined') {
                options.optgroupField = 'details';
            }
            var template = $(this).data('template');
            if (template) {
                options.render = {
                    item: function (item, escape) {
                        var out = template;
                        for (var i in item) {
                            out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                        }
                        out = out.replaceAll('()', '');
                        return "<div class='item'>" + out + "</div>";
                    },
                    option: function (item, escape) {
                        var out = template;
                        for (var i in item) {
                            out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                        }
                        out = out.replaceAll('()', '');
                        return "<div class='option'>" + out + "</div>";
                    },
                }
            }
            $input.selectize(options);
        })
    }

    function initAssignedStaffInput($inputs) {
        $inputs.each(function () {
            var dataObject = $(this).data('app-form-select-options');
            var dataParsing = $(this).data('parsing-option');
            var $input = $(this);
            var optionGroups = $(this).data('options-group');
            var baseOptions = Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single);
            if (typeof $input.attr('multiple') != 'undefined') {
                baseOptions = Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multiple);
            }
            var options = Object.assign({}, baseOptions, {
                valueField: $(this).data('value'),
                labelField: $(this).data('label'),
                searchField: [$(this).data('label')],

                load: function (query, callback) {
                    var that = this;
                    var selectizeInstance = this;
                    var url = dataObject.ajax.url;
                    if (typeof this.$input.attr('multiple') == 'undefined') {
                        this.clearOptions();
                    } else {
                        this.clearUnselectedOptions();
                    }
                    if (!query.length) return callback();
                    $.ajax({
                        url: url.replaceAll('__q__', encodeURIComponent(query)),
                        type: "GET",
                        dataType: 'json',
                        error: function () {
                            callback();
                        },
                        success: function (response) {
                            var responseResults = [];
                            if (response.data) {
                                responseResults = response.data;
                            } else if (response.results) {
                                responseResults = response.results;
                            } else if (Array.isArray(response)) {
                                responseResults = response;
                            }
                            if (responseResults.length && typeof optionGroups !== 'undefined' && Object.values(optionGroups).length) {
                                for (i = 0; i < responseResults.length; i++) {
                                    if (responseResults[i].details !== "" && typeof responseResults[i].details != 'undefined') {
                                        var categoriesIds = responseResults[i].details.split(',').filter(function (i) {
                                            return i != -1
                                        });
                                        var name = '';
                                        for (x = 0; x < categoriesIds.length; x++) {
                                            var categoryId = categoriesIds[x];
                                            name += optionGroups[categoryId] + (x !== categoriesIds.length - 1 ? ' > ' : '');
                                        }
                                        selectizeInstance.addOptionGroup(responseResults[i].details, {
                                            value: responseResults[i].details,
                                            label: name
                                        });
                                    }
                                }
                            }
                            callback(responseResults);
                        },
                    });
                }
            });
            if (typeof (optionGroups) != 'undefined') {
                options.optgroupField = 'details';
            }
            var template = $(this).data('template');
            if (template) {
                options.render = {
                    item: function (item, escape) {
                        var out = template;
                        for (var i in item) {
                            out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                        }
                        out = out.replaceAll('()', '');
                        return "<div class='item'>" + out + "</div>";
                    },
                    option: function (item, escape) {
                        var out = template;
                        for (var i in item) {
                            out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                        }
                        out = out.replaceAll('()', '');
                        return "<div class='option'>" + out + "</div>";
                    },
                }
            }
            $input.selectize(options);
        })
    }

    function initDataRangeInput($inputs) {
        $inputs.each(function () {
            var $input = $(this);
            var $fromInput = $input.parent().find('[data-range="from"]');
            var $toInput = $input.parent().find('[data-range="to"]');
            if ($fromInput.val().length && $toInput.val().length) {
                $input.val($fromInput.val() + '  -  ' + $toInput.val());
            }
            $input.flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.dateRangeFilter, {
                positionElement: $input.closest('[data-form-group]').get(0),
            }));
            $input.on('change', function () {
                var dates = $input.get(0)._flatpickr.selectedDates;
                var format = $input.get(0)._flatpickr.formatDate;
                if (dates.length === 2) {
                    $fromInput.val(format(dates[0], 'Y-m-d'));
                    $toInput.val(format(dates[1], 'Y-m-d'));
                } else {
                    $fromInput.val('');
                    $toInput.val('');
                }
            });
        });
    }

    function initCurrencyDropdownSelector($inputs) {
        $inputs.each(function () {
            var baseOptions = Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter);
            var options = Object.assign({}, baseOptions, {
                searchField: ['text', 'name'],
                plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter.plugins, {
                    'template': {
                        'name': 'currency'
                    }
                }),
            });
            var template = $(this).data('template');
            if (template) {
                options.render = {
                    item: function (item, escape) {
                        return "<div class='item'>" + item.value + "</div>";
                    },
                    option: function (item, escape) {
                        var out = template;
                        if (item.value == '__clear__') {
                            return "<div class='option'>" + item.text + "</div>";
                        }
                        for (var i in item) {
                            out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                        }
                        out = out.replaceAll('()', '');
                        return out;
                    },
                }
            }
            $(this).selectize(options);
        });
    }

    function initCurrencyRangeSelector($inputs) {
        $inputs.each(function () {
            var baseOptions = Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single);
            var options = Object.assign({}, baseOptions, {
                searchField: ['text', 'name'],
                plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single.plugins, {
                    'template': {
                        'name': 'currency'
                    }
                }),
            });
            var template = $(this).data('template');
            if (template) {
                options.render = {
                    item: function (item, escape) {
                        return "<div class='item'>" + item.value + "</div>";
                    },
                    option: function (item, escape) {
                        var out = template;
                        if (item.value == '__clear__') {
                            return "<div class='option'>" + item.text + "</div>";
                        }
                        for (var i in item) {
                            out = out.replaceAll('{{' + i + '}}', item[i] ? item[i] : '');
                        }
                        out = out.replaceAll('()', '');
                        return out;
                    },
                }
            }
            $(this).selectize(options);
        });
    }

    function initFollowUpStatusSelector($inputs) {
        $inputs.each(function () {
            $(this).selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter, {
                valueField: "value",
                labelField: "text",
                plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter.plugins, {
                    'template': {
                        'name': 'badge'
                    }
                }),
                render: {
                    option: function (data, escape) {
                        return '<div title="' + escape(data.text) + '" class="option"><span style="background: ' + data.color + '; color: ' + data.textColor + '" class="status-badge">' + escape(data.text) + '</span></div>';
                    },
                    item: function (data, escape) {
                        return '<div title="' + escape(data.text) + '" class="item"><span style="background: ' + data.color + '; color: ' + data.textColor + '" class="status-badge">' + escape(data.text) + '</span></div>';
                    },
                },
            }));
        })
    }

    function initTimeRangePickerInput($inputs) {
        $inputs.each(function () {
            $(this).flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.timeFilter, {
                positionElement: $(this).closest('[data-form-group]').get(0),
            }));
        });
    }

    function initTagInput($inputs) {
        $inputs.each(function () {
            var $input = $(this);
            if (!$input.attr('data-select-input-with-radio')) {
                $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.tags);
            } else {
                $input.selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.tags, {
                    plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.tags.plugins, {
                        'dropdown-radio': {
                            inputs: $input.data('select-input-with-radio'),
                            name: 'tag_radio_name',
                        }
                    }),
                }));
            }
        })

    }

    function initUploaderInput($inputs) {
        $inputs.each(function () {
            var dataObject = $(this).data('app-form-uploader-options');
            $(this).inputUploader(dataObject);
        })
    }

    function initFormatedTextInput($inputs) {
        $inputs.each(function () {
            tinymce.init(
                Object.assign({}, window.APP.VENDORS.DEFAULTS.tinymce.default, {
                    target: $(this).get(0),
                })
            );
        });
    }

    function initPhoneCodeInput($inputs) {
        $inputs.each(function () {
            $(this).selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single, {
                valueField: "id",
                labelField: "text",
                plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single.plugins, {
                    'template': {
                        'name': 'flag'
                    },
                    "persistent": true,
                    "auto-width": true
                }),
                render: {
                    option: function (data, escape) {
                        return '<div title="' + escape(data.text) + '" class="option">' + (data.code ? '<img src="/frontend/assets/img/shared/flags/' + escape(data.code.toLowerCase()) + '.svg" alt="' + escape(data.text) + '"/>' : '') + '<span>' + escape(data.id) + '</span></div>';
                    },
                    item: function (data, escape) {
                        return '<div title="' + escape(data.text) + '" class="item">' + (data.code ? '<img src="/frontend/assets/img/shared/flags/' + escape(data.code.toLowerCase()) + '.svg" alt="' + escape(data.text) + '"/>' : '') + '</div>';
                    },
                },
            }));
        })
    }

    function initPhoneNumberInput($inputs) {
        $inputs.each(function () {
            $(this).inputPhone()
        })
    }

    // Global function to fix selectize dropdown values
    window.fixSelectizeValues = function($container) {
        $container = $container || $(document);
        var fixedCount = 0;

        $container.find('select.selectized').each(function() {
            var $select = $(this);
            var htmlValue = $select.attr('value');

            if (htmlValue && $select[0].selectize) {
                var selectizeInstance = $select[0].selectize;
                var currentValue = selectizeInstance.getValue();

                // Check if the dropdown has a value but selectize doesn't show it
                if (!currentValue || currentValue === '') {
                    // Add the option if it doesn't exist
                    if (!selectizeInstance.options[htmlValue]) {
                        var optionText = htmlValue;
                        var existingOption = $select.find('option[value="' + htmlValue + '"]');
                        if (existingOption.length > 0 && existingOption.text()) {
                            optionText = existingOption.text();
                        }

                        selectizeInstance.addOption({
                            id: htmlValue,
                            text: optionText,
                            value: htmlValue
                        });
                    }

                    // Set the value and trigger change to ensure form synchronization
                    selectizeInstance.setValue(htmlValue, true); // true = trigger change event

                    // Also ensure the original select element has the correct value
                    $select.val(htmlValue);

                    // Trigger change event on the original select to ensure form handling
                    $select.trigger('change');

                    console.log('Fixed selectize value:', htmlValue, 'for', $select.attr('id'));
                    fixedCount++;
                }
            }
        });

        console.log('Fixed', fixedCount, 'selectize dropdowns');
        return fixedCount;
    };

    // Auto-fix selectize values when page loads
    $(document).ready(function() {
        setTimeout(function() {
            if (typeof window.fixSelectizeValues === 'function') {
                window.fixSelectizeValues();
            }
        }, 2000);

        // Also run periodic checks for the first few seconds
        var checkCount = 0;
        var maxChecks = 3;
        var checkInterval = setInterval(function() {
            checkCount++;
            if (typeof window.fixSelectizeValues === 'function') {
                window.fixSelectizeValues();
            }

            if (checkCount >= maxChecks) {
                clearInterval(checkInterval);
            }
        }, 3000);
    });

    function initSubForm($subForms) {
        $subForms.each(function () {
            let template = $(this).first().next('[data-subform-template-row]');
            $(this).subform({
                addBtn: '[data-subform-add-row]',
                template: template,
                removeBtn: '[data-cell-remove]',
                dragBtn: '[data-cell-drag]',
            });

            var dataOptions = $(this).attr('data-options');
            var options = {};
            var totals = null;

            if (dataOptions && dataOptions !== 'undefined') {
                try {
                    options = JSON.parse(dataOptions);
                    totals = options.total;
                } catch (e) {
                    console.warn('Failed to parse data-options:', dataOptions, e);
                    options = {};
                    totals = null;
                }
            }
            $form_selector = $(this);

            // Fix for selectize dropdowns with pre-selected values
            setTimeout(function() {
                $form_selector.find('select.selectized').each(function() {
                    var $select = $(this);
                    var htmlValue = $select.attr('value');

                    if (htmlValue && $select[0].selectize) {
                        var selectizeInstance = $select[0].selectize;
                        var currentValue = selectizeInstance.getValue();

                        // Check if the dropdown has a value but selectize doesn't show it
                        if (!currentValue || currentValue === '') {
                            // Add the option if it doesn't exist
                            if (!selectizeInstance.options[htmlValue]) {
                                var optionText = htmlValue;
                                var existingOption = $select.find('option[value="' + htmlValue + '"]');
                                if (existingOption.length > 0 && existingOption.text()) {
                                    optionText = existingOption.text();
                                }

                                selectizeInstance.addOption({
                                    id: htmlValue,
                                    text: optionText,
                                    value: htmlValue
                                });
                            }

                            // Set the value and trigger change to ensure form synchronization
                            selectizeInstance.setValue(htmlValue, true); // true = trigger change event

                            // Also ensure the original select element has the correct value
                            $select.val(htmlValue);

                            // Trigger change event on the original select to ensure form handling
                            $select.trigger('change');

                            console.log('Fixed selectize dropdown:', $select.attr('id'), 'with value:', htmlValue);
                        }
                    }
                });
            }, 1500);

            if(totals){
                totals.forEach(function($value, $index){
                    $input_selector = 'input[name*="[' + $value + ']"]';
                    $form_selector.on('keyup change', $input_selector, function() {
                        var sum = 0;
                        $form_selector.find($input_selector).each(function() {
                            var val = parseFloat($(this).val());
                            if (!isNaN(val)) {
                                sum += val;
                            }
                        });
                        $form_selector.find('.table-total-' + $value).text(sum === 0 ? '0.00' : sum);
                    });
                    $($input_selector).trigger('change');
                });
            }

            $(this).on('subform:add', function (e, $row) {
                initDynamicDropdown($row.find(dynamicDropdownSelector));
                initDateTime($row.find(dateTimeInput));
                initDate($row.find(dateInput));
                initTime($row.find(timeInput));
                initMultipleDynamicDropdownSelector($row.find(multipleDynamicDropdownSelector));
                initLinkListInput($row.find(linkListInput));
                initAutoSuggestInput($row.find(autoSuggestInput));
                initDataRangeInput($row.find(dateRangeInput));
                initCurrencyDropdownSelector($row.find(currencyDropdownSelector));
                initFollowUpStatusSelector($row.find(followUpStatusSelector));
                initTimeRangePickerInput($row.find(timeRangePickerInput));
                initTagInput($row.find(tagInput));
                initUploaderInput($row.find(uploaderInput));
                initFormatedTextInput($row.find(formattedTextInput));
                initPhoneCodeInput($row.find(phoneCodeInput));
                initPhoneNumberInput($row.find(phoneNumberInput));
                initSubForm($row.find(subForm));
                initMap($row.find(inputMap));
            });
        });
    }
</script>


<script>
    $(document).ready(function () {
        let filteredObjects = parsingFilteredObjects();
        renderFilteredObjects(filteredObjects);
    });

    $('#filter-value [data-id="filter-head"]').on('click', '[data-id="filter-head-close"]', function () {
        var inputName = $(this).prev('span').data('name');
        parsingUrl(inputName);
    });

    function parsingFilteredObjects() {
        var inputsAndSelects = $("[data-id='filter-inputs'] :input");
        var filteredObjects = [];

        inputsAndSelects.each(function () {
            if ($(this).is("input")) {
                var inputType = $(this).attr("type"),
                    inputName = $(this).attr("name"),
                    inputValue = $(this).val(),
                    inputPlaceholder = $(this).attr('placeholder'),
                    inputLabel = $(this).parents('label').children('p').text(),
                    hidden = $(this).parents('.col-md-6').prop('hidden');
                if ('' != inputValue && typeof inputName != "undefined" && !hidden) {
                    var filteredInputObject = {
                        'type': inputType,
                        'name': inputName,
                        'placeholder': inputPlaceholder,
                        'label': inputLabel,
                        'value': inputValue
                    };
                    filteredObjects.push(filteredInputObject);
                }
            } else if ($(this).is("select")) {
                var selectName = $(this).attr("name"),
                    selectedOptionText = $(this).find("option:selected").text(),
                    selectPlaceholder = $(this).attr('placeholder'),
                    selectLabel = $(this).parents('label').children('p').text();
                if ('' != selectedOptionText) {
                    var filteredSelectObject = {
                        'type': 'select',
                        'name': selectName,
                        'placeholder': selectPlaceholder,
                        'label': selectLabel,
                        'value': selectedOptionText
                    };
                    filteredObjects.push(filteredSelectObject);
                }
            }
        });
        return filteredObjects;
    }

    function renderFilteredObjects(filteredObjects) {
        $('[data-id="filter-head-count"]').text(filteredObjects.length);
        $.each(filteredObjects, function (index, filteredObject) {
            var html = '<a href="#" class="btn btn-sm btn-secondary">' +
                '<span class="text-dark-3 me-2">' + filteredObject.label + ':</span>' +
                '<span data-name="' + filteredObject.name + '">' + filteredObject.value + '</span>' +
                '<i class="ms-5 fal fa-times" data-id="filter-head-close"></i>' +
                '</a>';
                if(filteredObject.name != 'iframe') {
                    $('#filter-value [data-id="filter-head"]').append(html);
                }

        });
    }

    function parsingUrl(inputName) {
        var currentURL = window.location.href,
            url = new URL(currentURL);
        url.searchParams.delete(inputName);
        window.location.href = url.href;
    }
 

    function initConditionInput(inputs) {
    
        inputs.each(function () {
            $(this).inputPlaceholder({
                url: '/v2/api/placeholders/'+$(this).attr('target_entity')+'/equation',
                // tooltip: '[data-placeholder-tooltip]',
                // viewAllButton: '[data-placeholder-btn]',
                // hiddenInput: '[data-placeholder-hidden-input]',
            })
        })
    }

    function initMap(inputs) {
        inputs.each(function () {
            $(this).inputMap()
        })
    }
    


</script>

