<?php

$extends = 'layouts/layout';

$this->viewVars['title_for_layout'] = __t('Chart');
$this->viewVars['_PageBreadCrumbs'] = [
    [
        'title' => __t('Organizational Chart'),
    ],
];
?>

<?php $this->section('styles') ?>
<link rel="stylesheet" href="/frontend/assets/css/pages/org-chart.css?v=<?= CSS_VERSION ?>" />
<style type="text/css">
  .company_node {
    box-shadow: 0px 0px 9.58px 0px #00000014;
    background-color: #FFFFFF;
    border-radius: 3px;
    padding: 15px 10px;
    position: relative;
    text-align: center;
  }
  .employees-text {
    display: flex;
    align-items: center;
    gap: 10px; justify-content: center;
  }

  .employees-text span {
    font-size: 16px
  }

  .hide {
    display: none;
  }

  .drag-enabled:not(.dragging-active) .node.draggable {
    stroke: grey;
    stroke-width: 3px;
    stroke-dasharray: 2px;
  }

  .drag-enabled.dragging-active .droppable {
    stroke: green;
    stroke-width: 3px;
    stroke-dasharray: 5px;
  }

  .node.dragging {
    stroke-dasharray: 0 !important;
    stroke-width: 0 !important;
  }

  .node.dragging .content-container {
    background-color: #ffffff;
  }
</style>
<?php $this->endSection() ?>

<?php $this->section('page-head') ?>

    <div class="page-head view-page-head">
            <div class="page-head-row">
                <div class="hstack chart-export-pdf "> 
                    <button id="download_pdf_btn" onclick="downloadPdf(chart)" type="button" class="btn btn-primary my-5 mx-25"> 
                        <span class="mdi mdi-export"></span> 
                        <?= __t("Export to PDF") ?>  
                    </button>
                </div>
            </div>
    </div>
<?php $this->endSection() ?> 

<div class="page-content"> 
    <div class="my-10 mx-25 vstack gap-10 m">
        <div class="view-actions" data-va="true">
            <div class="btn-group-controller" role="group">
                <div class="btn-group-controller" role="group" data-va-items="true">
                    <button onclick="collapseAllFunc()" id="collapse_all_btn" type="button" class="btn btn-inverted">
                        <span class="mdi mdi-arrow-collapse-all me-4 view-actions-icon"></span>

                        <?= __t("Collapse All") ?> 
                    </button>
                    <button onclick="expandAllFunc()" id="expand_all_btn" type="button" class="btn btn-inverted" >
                        <span class="mdi mdi-arrow-expand-all me-4 view-actions-icon"></span>
                        <?= __t("Expand All") ?> 
                        
                    </button>
                    
                    <div class="dropdown btn btn-inverted p-0">
                        <button id="filter_by_dep_btn" class="btn btn-inverted dropdown-toggle filter-by-dept-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <?= __t("Filter by Department") ?>  <span class="dropdown-selected ms-4">(<?=__t("All")?>)</span>
                        </button> 
                        <div class="dropdown-menu">
                            <div class="dropdown-search">
                                <input type="text" class="form-control" id="dropdownSearch" placeholder="<?= __t("Search") ?>...">
                            </div>
                            <div id="dropdownItems"> 
                                <button class="dropdown-item active" type="button" data-text="All Departments">
                                    <span class="dep-name" style="font-weight: bold;">
                                        <?= __t("All Departments") ?>
                                    </span> 
                                    <div class="d-flex"> 
                                        <span id="all-departments-emp-no"> 0 </span>
                                        <i class="fa-regular fa-user"></i> 
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>


                    <button type="button" class="btn btn-inverted" id="unassignedEmployees_btn" type="button" data-text="unassignedemployees">
                        <span class="fa-regular fa-user me-4"></span>  
                        <?= __t("Unassigned Employees") ?>   
                        <span id="unassigned-emp-no" class="ms-4"> (0) </span>
                    </button>

                    <button type="button" class="btn btn-inverted" id="multiselect_btn" type="button" onclick="toggleCheckbox()">
                        <span class="mdi mdi-format-list-bulleted me-4 view-actions-icon"></span>  
                        <?= __t("Multiselect") ?>   
                    </button>

                    
                </div>
                <div class="view-actions-dropdown dropdown">
                    <button type="button" class="btn btn-inverted" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false" data-va-btn="true" style="display: none;">
                        <i class="mdi mdi-dots-horizontal"></i>
                    </button>
                    <div class="dropdown-menu" data-va-dropdown="true">
                    </div>
                </div>
            </div>
        </div>

        <div class="card" id="chart_container_card">
            <div class="chart-container" id="chart_container">
            </div>
            <div class="zoom-actions">
                <button class="btn reset"  onclick="resetZooming(chart) " type="button"> <?= __t("Reset") ?>   </button>

                <button class="btn zoom-in"  type="button" onclick="chart.zoomIn()"> <span class="mdi mdi-magnify-plus" ></span> </button>
                <span class="zoom-value" >100%</span>
                <button class="btn zoom-out" type="button" onclick="chart.zoomOut()" > <span class="mdi mdi-magnify-minus"></span> </button>
            </div>
        </div>
    </div>  
    <div class="modal fade" id="assigningModal" tabindex="-1" aria-labelledby="assigningModalLabel">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title assign-model-header"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body modal-body-content">  
                    <div class="container">
                        <label><span id="dropdown-lable"></span>  <span class="required">*</span></label>
                        <div class="dropdown managers-list-dropdown"> 
                            <button class="btn btn-secondary dropdown-toggle" id="current-assinging-value" type="button" data-bs-toggle="dropdown"> </button>
                            <div class="dropdown-menu">
                                <div class="dropdown-search">
                                    <input type="text" class="form-control" id="managersListSearch" placeholder="<?= __t("Search") ?>...">
                                </div>
                                <ul id="managers-list-dropdown-items"> </ul>
                            </div>
                          
                            <span id="current-assinging-value-error"> </span>
                        </div>

                        <div id="selected_employees_list"> 
                        </div>

                    </div>
                  

                    <div class="btn-group gap-10 w-100" role="group" aria-label="Basic example">
                        <button type="button" class="btn btn-light btn-block" data-bs-dismiss="modal" aria-label="Close"><?= __t("Cancel") ?>  </button> 
                        <button type="button" class="btn btn-success btn-block" id="save_assign_changes"><?= __t("Save") ?>  </button>
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <div class="modal fade" id="assignConfirmation" tabindex="-1" aria-labelledby="assignConfirmationLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title assign-model-header"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body modal-body-content"> 
                    <label class="mb-15" id="confirm_assign_label"></label> 

                    <div class="btn-group gap-10 w-100" role="group" aria-label="Basic example">
                        <button type="button" class="btn btn-light btn-block" data-bs-dismiss="modal" aria-label="Close"><?= __t("Cancel") ?> </button> 
                        <button type="button" class="btn btn-success btn-block" id="confirm_assign"></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title assign-model-header"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body modal-body-content"> 
                    <label class="mb-15" id="errorModal_label">An error occured</label> 

                    <div class="btn-group gap-10 w-100" role="group" aria-label="Basic example">
                        <button type="button" class="btn btn-light btn-block" data-bs-dismiss="modal" aria-label="Close">Okay</button> 
                      
                    </div>
                </div>
            </div>
        </div>
    </div>

</div> 

<?php $this->section('article-end') ?>
<?php $this->endSection() ?>

<?php $this->section('page-before-js') ?>








<script>
    let companyDetailsData = {...<?= $siteData ?>};
</script> 
<script src="/frontend/assets/js/pages/org-chart/d3.v7.min.js"></script>
<script src="/frontend/assets/js/pages/org-chart/d3-org-chart.js"></script>
<script src="/frontend/assets/js/pages/org-chart/d3-flextree.js"></script> 
<script src="/frontend/assets/js/pages/org-chart/html2canvas.js"></script> 
<script src="/frontend/assets/js/pages/org-chart/jspdf.umd.min.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/pages/org-chart/html2pdf.bundle.min.js"></script>

<script src="/frontend/assets/js/pages/org-chart/chart-initital-render.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
<script src="/frontend/assets/js/pages/org-chart/drag-drop-functions.js?v=<?= JAVASCRIPT_VERSION ?>"></script> 
<script src="/frontend/assets/js/pages/org-chart/chart-functions.js?v=<?= JAVASCRIPT_VERSION ?>"></script> 
<script src="/frontend/assets/js/pages/org-chart/chart-inline-style.js?v=<?= JAVASCRIPT_VERSION ?>"></script> 
<?php $this->endSection() ?>

 


