<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/css/images/Daftra-favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><?php echo __t('Bookings Management') ?></title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap" />
</head>

<body>
  <div id="root" style="height: 100vh; width: 100vw;"></div>
  <script>
    var SITE_LANG = '<?php echo e(app()->getLocale()); ?>';
    var IS_RTL = <?php echo is_rtl() ? '1' : '0' ?>;
    </script>

    <script type="module" crossorigin src="/v2/js/app/booking-settings.js"></script>

    <!-- <script type="module">
      import { injectIntoGlobalHook } from "http://localhost:5173/v2/js/app/@react-refresh";
      injectIntoGlobalHook(window);
      window.$RefreshReg$ = () => {};
      window.$RefreshSig$ = () => (type) => type;
    </script>
    <script type="module" src="http://localhost:5173/v2/js/app/@vite/client"></script>
    <script type="module" src="http://localhost:5173/v2/js/app/src/pages/booking/settings.tsx"></script>  -->

</body>


</html>