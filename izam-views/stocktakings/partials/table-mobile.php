<?php

use Izam\Daftra\Common\Utils\StocktakingUtil;
use Izam\Daftra\Common\Utils\StocktakingSourceEntryUtil;

?>
<div class="d-block d-lg-none">

    <div>
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table">
                    <thead>
                    <tr>
                        <th class="listing-cell-start"></th>
                        <th></th>
                        <th width="45" class="listing-cell-end text-end">
                            <?= $this->includeSection('partials/sorting_fields') ?>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    foreach ($table->getRows() as $row) {
                        $stocktaking = $row->getData();
                        $viewUrl = "/owner/stocktakings/view/$stocktaking->id";
                        ?>
                        <tr>
                            <td>
                                <a href="<?= $viewUrl ?>"
                                   class="listing-cell-link"></a>

                                <div class="title-text vstack gap-3">
                                    <p><?= $stocktaking->stores->name ?></p>
                                    <small class="title-subtitle-text text-break-all"><?= '#' . $stocktaking->number . ' (' . format_date($stocktaking->date) . ')' ?></small>
                                    <?php if (StocktakingSourceEntryUtil::getSourceWeb() != $stocktaking->source_entry) { ?>
                                        <div class="vstack mt-3 gap-6 align-items-center">
                                            <div class="text-meta">
                                                <i class="mdi mdi-cog"></i>
                                                <span><?= __t('Source') . ': ' . StocktakingSourceEntryUtil::getLabel($stocktaking->source_entry) ?></span>
                                            </div>
                                        </div>
                                    <?php } ?>
                                </div>
                            </td>

                            <td>
                                <a href="<?= $viewUrl ?>"
                                   class="listing-cell-link"></a>
                                <div class="status-circle">
                                    <span class="badge text-bg-<?= StocktakingUtil::getStatusClass($stocktaking->status) ?>"><?= StocktakingUtil::getLabel($stocktaking->status) ?></span>
                                </div>

                                <?php if ($stocktaking->stocktaking_requisition_in->items) {
                                    $sum_in_all = getRequisitionitemsSum($stocktaking->stocktaking_requisition_in->items);
                                    if ($sum_in_all) {
                                        ?>
                                        <div class="text-nowrap">
                                            <?= __t("Shortage/Overage", true) . ' ' . format_price_simple($sum_in_all); ?>
                                        </div>
                                    <?php }
                                } ?>
                                <?php if ($stocktaking->stocktaking_requisition_out->items) {
                                    $sum_out_all = getRequisitionitemsSum($stocktaking->stocktaking_requisition_out->items);
                                    if ($sum_out_all) {
                                        ?>
                                        <div class="text-nowrap">
                                            <?= __t("Shortage/Overage", true) . ' ' . format_price_simple($sum_out_all); ?>
                                        </div>
                                    <?php }
                                } ?>
                            </td>

                            <?php if (!isset($_GET['hide_actions'])) { ?>
                                <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                                    <?php $this->includeSection('partials/row-actions-mobile', ['row' => $row]); ?>
                                </td>                    
                            <?php } else { ?>
                                <td class="text-end h-0 listing-cell-end" data-lt-dropdown-cell="true">
                                </td>
                            <?php } ?>
                            
                            
                        </tr>
                    <?php }
                    ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
