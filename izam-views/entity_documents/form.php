<?php

use Izam\View\Form\Helper\Form\FormHeader;
use Izam\View\Form\Helper\FormCollection;
use Izam\View\Form\Helper\FormRowWrapper;
$extends = 'layouts/layout';
$elementHelper = new FormCollection();
$helper = new FormHeader();
$helper->setView($this);
$this->sharedVars['extraContainerClass']= '';
?>
    <style>
        .document-settings-table .form-check-input[type=checkbox] {
            width: 1.125rem;
            height: 1.125rem;
        }
        .document-settings-table .form-check-label {
            display: none!important;
        }
        .document-settings-table .form-label {
            margin: 0;
            padding: 6px 5px;
        }
    </style>
<?php $this->section('page-head') ?>
<?= $helper->render($pageHead); ?>
<?php $this->endSection() ?>

<?php $this->section('article-start') ?>

<form action="<?= route('owner.update.entity.documents' , $entityKey) ?>" method="POST" data-app-form="document_types"  class="validate form-validate">
    <input type="hidden" name="_token" value="<?= csrf_token() ?>">
    <input type="hidden" name="_method" value="PUT">
    <?php $this->endSection() ?>

    <div class="container">
        <div class="my-10 vstack gap-10">
            <div class="collapse filters" data-filters="true">
                <form action="" method="GET">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex align-items-start justify-content-between">
                                <div>
                                    <p class="pt-5 mb-0" data-filters-hidden="true"><?= __t("Search & Filters") ?></p>

                                </div>
                                <div class="btn-group-controller" role="group">
                                    <button type="button" class="btn btn-filter" data-bs-toggle="collapse"
                                            data-bs-target="[data-filters-collapse]" aria-expanded="true"
                                            aria-controls="lf-collapse">
                                        <i class="mdi mdi-unfold-more-horizontal me-4" aria-expanded="false"></i>
                                        <i class="mdi mdi-unfold-less-horizontal me-4" aria-expanded="true"></i>
                                        <span aria-expanded="false"><?= __t('Show') ?></span>
                                        <span aria-expanded="true"><?= __t('Hide') ?></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="collapse show" data-filters-collapse="true">
                            <div class="card-body">
                                <div class="row">

                                    <!-- <?= __t('Enter Document Types Name') ?> -->
                                    <div class="col-md-6 col-lg-5 col-3xl-4">
                                        <label class="form-group floating-group is-active" data-form-group="true">
                                            <p class="floating-label"><?= __t('Enter Document Types Name') ?></p>
                                            <div class="input-group input-group-merge">
                                                <span class="input-group-text input-group-icon-start">
                                                    <i class="mdi mdi-magnify"></i>
                                                </span>
                                                <input type="text" class="form-control" id="type_name_search_input"
                                                       placeholder="<?= __t('Enter Document Types Name') ?>" data-form-input="true"
                                                       value="" data-app-input="search-input" />
                                                <span class="input-group-text input-group-icon-end">
                                                    <button type="button" class="btn btn-input-clear"
                                                            data-form-input-clear="true" data-app-input="search-reset"><i
                                                                class="fal fa-times"></i></button>
                                                </span>
                                            </div>
                                        </label>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="card-row">
                        <div class="col-lg-12 document-settings-table">
                            <p class="form-label">
                                <?= __t('List of Document Types') ?>
                            </p>
                            <?php
                                $elementHelper = new FormRowWrapper();
                                $helper = new FormCollection();
                                $form->prepare();
                                echo $helper->render($form);
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $this->section('article-end') ?>
</form>
<?php $this->endSection() ?>
<?php $this->section('page-after-js') ?>
    <script>
        var nameMustBeUniqueTxt = "<?= __t('Document type name must be unique and not empty.') ?>";
    </script>
    <script src="/frontend/assets/js/pages/entity-document-types/settings.js"></script>
<?php $this->endSection() ?>