<td class="<?php

 echo IS_MOBILE ? ' text-end h-0' : 'listing-cell-end'; ?> " data-lt-dropdown-cell="true">
    <a href="javascript::avoid(0)" class="listing-cell-link"></a>
    <div
        class="<?php echo IS_MOBILE ? ' d-flex flex-column h-100 align-items-end' : 'hstack gap-2 justify-content-end'; ?> ">
        <?php if (check_permission(\App\Utils\PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS)): ?>
            <button class="btn btn-dots btn-light-1 text-primary order-2 order-md-1" type="button" data-bs-toggle="tooltip"
                data-bs-placement="top" data-bs-title="Download">
                <i class="mdi mdi-download"></i>
            </button>
        <?php endif;?>
        <div class="dropstart mb-auto order-1 order-md-2" data-lt-dropdown="true">
            <button class="btn btn-dots" type="button" data-bs-offset="-20,10" data-bs-toggle="dropdown"
                aria-expanded="false">
                <i class="mdi mdi-dots-horizontal"></i>
            </button>
            <ul class="dropdown-menu text-start">
                <?php
                if (!empty($listingRowActions)) {
                    if (empty(request()->get('hide_actions')) || request()->get('hide_actions') != 1) {
                        $permissionActions = getEntityPermissionsForRowActions($entityKey);
                        $actions = getEntityRowActionsCheckerFirst($pageTitle, $row, $listingRowActions, $entityKey, $permissionActions);
                        foreach ($actions as $k => $action) {
                            echo $action;
                        }
                    }
                }
                ?>
            </ul>
        </div>

    </div>
</td>
<script>
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('button[data-bs-title="Download"]').forEach(function (btn) {
        btn.addEventListener('click', function (e) {
            const row = btn.closest('tr');
            if (!row) return;

            const checkbox = row.querySelector('input[name="ids[]"]');
            if (!checkbox) return;

            const rowId = checkbox.value;
            const downloadUrl = `/v2/owner/document_types/export_documents/0/<?=$row->getData()->entity_id?>?ids[]=${rowId}`;

            window.location.href = downloadUrl;
        });
    });
});

</script>