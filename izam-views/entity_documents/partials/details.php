<?php

use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;

    extract($this->viewVars['viewExtraData']);
?>

<div class="info-section">
    <!-- Info Section Title -->
    <div class="info-section-title">
        <span><?= sprintf(__t('%s Info'),__t('Document'))?></span>
    </div>
    <!-- Info Section Body -->
    <div class="info-section-body">
        <div class="row">

            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?= __t('Document Name')?>
                    </div>
                    <div class="info-item-value">
                        <span class="fw-medium"><?= $data->name?> </span>
                    </div>
                </div>
            </div>

            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                    <?= __t('Type')?>
                    </div>
                    <div class="info-item-value">
                        <?= $data->entity_document_type?->name?>
                    </div>
                </div>
            </div>

            <?php if( $isExpirable){?>
            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                    <?= __t('Expiry Date')?>
                    </div>
                    <div class="info-item-value">
                        <?= format_date($data->expiry_date)  ?> 
                            <?php if($isExpired){?>
                                <span class="text-danger fs-8 ms-2">
                                <i class="mdi mdi-clock-alert fs-10"></i> 
                                <?= __t('Expired From')?>
                                <?= $expireFrom?>
                            </span>
                            <?php }else{?>
                            <span class="text-warning-3 fs-8 ms-2">
                                <i class="mdi mdi-clock-alert fs-10"></i> <?= $expireIn?>
                            </span>
                        <?php }?>
                        </span>
                    </div>
                </div>
            </div>
            <?php }?>


            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                    <?= __t('Date of Creation')?>
                    </div>
                    <div class="info-item-value">
                    <?= format_date($data->created)  ?>
                    </div>
                </div>
            </div>


             <!-- Info Item -->
             <?php if($data->notes){?>
                <div class="col-lg-6">
                    <div class="info-item">
                        <div class="info-item-label">
                        <?= __t('Notes')?>
                        </div>
                        <div class="info-item-value">
                        <?= $data->notes  ?>
                        </div>
                    </div>
                </div>
            <?php }?>

        </div>
    </div>

</div>

<?php  if($staff){?>
    <div class="info-section">
        <!-- Info Section Title -->
        <div class="info-section-title">
            <span><?= __t('Contact Information')?></span>
        </div>
        <!-- Info Section Body -->
        <div class="info-section-body">
            <div class="row">

                <!-- Info Item -->
                <div class="col-lg-6">
                    <div class="info-item">
                        <div class="info-item-label">
                            <?= __t('Employee Name')?>
                        </div>
                        <div class="info-item-value">
                            <span class="fw-medium"><?= $staff->full_name?> </span>
                        </div>
                    </div>
                </div>

                <!-- Info Item -->
                <div class="col-lg-6">
                    <div class="info-item">
                        <div class="info-item-label">
                        <?= __t('Email')?>
                        </div>
                        <div class="info-item-value">
                            <?= $staff->email_address?>
                        </div>
                    </div>
                </div>

               <?php if ($staff->mobile): ?>
                <!-- Info Item -->
                <div class="col-lg-6">
                    <div class="info-item">
                        <div class="info-item-label">
                        <?= __t('Mobile Number')?>
                        </div>
                        <div class="info-item-value">
                            <?= $staff->mobile?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($staff->home_phone): ?>

                <!-- Info Item -->
                <div class="col-lg-6">
                    <div class="info-item">
                        <div class="info-item-label">
                        <?= __t('Phone Number')?>
                        </div>
                        <div class="info-item-value">
                            <?= $staff->home_phone?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                <?php if ($staff->staff_job?->join_date ?? null): ?>

                <!-- Info Item -->
                <div class="col-lg-6">
                    <div class="info-item">
                        <div class="info-item-label">
                        <?= __t('Joining Date')?>
                        </div>
                        <div class="info-item-value">
                            <?= format_date($staff->staff_job?->join_date ?? null)?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

            </div>
        </div>

    </div>
    <?php  }?>


<div class="info-section">
    <!-- Info Section Title -->
    <div class="info-section-title">
        <span><?= sprintf(__t("%s Uploaded Files"),__t('Document'))?></span> 
        <?php if($data->documents && $canManage && count($data->documents ?? []) > 1 ) :?>
        | <a  href="/v2/owner/document_types/export_documents/0/<?=$data->entity_id?>?ids[]=<?=$data->id?>" class="link-primary link-opacity-75-hover text-decoration-none" > <?=__t('Download All')?></a>
        <?php endif; ?>   

    </div>
   
    <div class="info-section-body">
        <div class="row">
            <?php 
                if($data->documents) :?>
                    <?= $this->includeSection('entitiy/partials/le_preview_attachments', [
                    'attachments' => $data->documents
                ]);?>
                
            <?php endif; ?>   
        </div>
    </div>
</div>