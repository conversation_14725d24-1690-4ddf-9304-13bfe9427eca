<?php

use App\Services\EntityDocumentService;
use App\Utils\EntityKeyTypesUtil;
use Carbon\Carbon;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;

   $isIframe = isset($_GET['iframe']);
   extract($listingExtraData);
   
?>
<div class="d-none d-lg-block">
    <form action="" method="POST" data-lt-form="true">
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table" data-lt="true">
                    <colgroup>
                        <col />
                        <col />
                        <col />
                        <col />
                        <col />
                    </colgroup>
                    <thead data-lt-head="true">
                        <tr>
                            <th class="listing-cell-start">
                                <?php if (!$isIframe) { ?>
                                    <div class="d-flex align-items-center gap-6">
                                        <div class="listing-cell-check">
                                            <input class="form-check-input" type="checkbox"
                                                data-lc-toggle="current-page" />

                                        </div>
                                        <div>
                                            <?= __t('Type') ?>
                                        </div>
                                    </div>
                                <?php } else { ?>

                                    <div class="d-flex align-items-center gap-6">
                                        <div class="listing-cell-check">
                                            <div class="dropdown" data-lt-dropdown="true">
                                                <button class="btn btn-link dropdown-toggle dropdown-toggle-2 px-0"
                                                    data-bs-toggle="dropdown" aria-expanded="false"
                                                    type="button">
                                                    <input class="form-check-input" type="checkbox"
                                                        data-lc-toggle="current-page" />
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <button class="dropdown-item" type="button"
                                                            data-lc-check="none">
                                                            <i class="mdi mdi-close text-primary"></i>
                                                            <span><?= __t('None') ?></span>
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item" type="button"
                                                            data-lc-check="current-page">
                                                            <i class="mdi mdi-check-underline text-primary"></i>
                                                            <span><?= __t('All (Current Page)') ?></span>
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item" type="button"
                                                            data-lc-check="all-filtered-pages">
                                                            <i class="mdi mdi-check-all text-primary"></i>
                                                            <span><?= __t('All Filtered (All Pages)') ?></span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div> <?= __t('Document Name')?></div>
                                    </div>
                                <?php } ?>
                            </th> 


                            <th>
                                    <?= __t('Type') ?>
                            </th>
                            <th>
                                <?= __t('Expiry Date/Status')?>
                            </th>
                            <th>
                                <?= __t('Date of Creation')?>
                            </th>
                            <th>
                                <?= __t('Attachments') ?>
                            </th>
                            <th>
                                <?= __t('Created by')?>
                            </th>
                            <th  class="listing-cell-end text-end">
                                <div class="dropdown" data-lt-dropdown="true">
                                    <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>

                                </div>
                            </th>
                           
                        </tr>
                    </thead>
                    <tbody>

                    
                    <?php foreach ($table->getRows() as $key => $tableRow):
                            $row = $tableRow->getData();
                            $status = resolve(EntityDocumentService::class)->calculateStatus($row);
                            $isExpirable = $row->entity_document_type?->is_expirable ?? 0;
                            $expireyDate = Carbon::parse($row->expiry_date);
                            $expireIn = $isExpirable ? $expireyDate->diffInDays(today()->subDay()) : null;
                            $rowTypeCount =  $documentTypesCount[$row->entity_document_type_id] ?? 0;
                            $createUrl = $canUload? route('owner.entity.create' ,['entityKey'=>'entity_document' , 'subEntityKey'=>'staff','subEntityId'=> $entity_id ,'entity_document_type_id' => $row->document_types_id, 'backUrl'=> route('owner.staff.show',[$entity_id]). '#documents']) : "#";
                            $showUrl =  $row->id ? route("owner.entity.show", [EntityKeyTypesUtil::ENTITY_DOCUMENT, 'id' => $row->id]): $createUrl;
                            // $filterUrl = '/v2/owner/entity/entity_document/list?iframe=1&filter[entity_key]=staff&filter[entity_id]='.$row->entity_id.'&show_filters=1&filter[entity_document_type_id]='.$row->entity_document_type_id;
                            $filterUrl = $showUrl.'#related-documents';
                            $created_by =  $row->created_by?->full_name ? $row->created_by?->full_name : ($row->staff_id === 0 ? getAuthOwner('business_name'): '--') ;
                            $id = $row->id;
                            $anchorAttribute = $isIframe ? "target = _blank" : "";
                            if(!$id){//missing document
                                $id = "type_".$row->document_types_id;
                                if(!$canUload){
                                    $anchorAttribute =  "" ;
                                }
                            }
                        ?>

                        <tr tabindex="0">
                            <td class="listing-cell-start">
                                <a href="<?= $showUrl ?>" <?= $anchorAttribute ?> class="listing-cell-link"></a>
                                <div class="text-nowrap">
                                    <div class="d-flex gap-6 align-items-center">
                                        <div class="listing-cell-check">
                                            <input class="form-check-input " type="checkbox" name="ids[]" value="<?= $id ?>" data-lc-item="true" />
                                        </div>
                                        <div>
                                            <a href="<?= $showUrl ?>" <?= $anchorAttribute ?> class="listing-cell-link"></a>
                                            <?php if($row->id){?>
                                                <?= $row->name ?>
                                                
                                            <?php }else{?>
                                            --
                                            <?php }?>
                                        </div>
                                    </div>
                                </div>
                            </td>

                            <td>
                                <div class="title-text">
                                    <p><?=  $row->document_types_name ?? null ?>
                                    <?php if ($rowTypeCount && $rowTypeCount > 1 && $row->id && !$isRelatedDocuments): ?>
                                        <a target="_blank" href=<?=$filterUrl?> class="link-primary text-decoration-none"  data-bs-toggle="tooltip"
                                            data-bs-placement="top" aria-label="more"
                                            data-bs-original-title="<?= sprintf(__t('%s Documents available of the same type, Click on the icon to show'),$rowTypeCount-1)?>"> +<?= $rowTypeCount-1 ?> </a>
                                    <?php endif; ?>
                                    </p>
                                </div>
                            </td>
                            <td> 

                                <div class="text-nowrap">
                                    <div class="vstack gap-2">
                                        <div class="hstack gap-2 justify-content-start">
                                           <?php  if($row->id){?>
                                                <span class="badge badge-md text-bg-<?=  EntityDocumentStatusUtil::getStatusCssClass($status)?>"><?=  EntityDocumentStatusUtil::getStatusTrans($status,$expireIn)?></span>
                                            <?php  }else{?>
                                                <span class="badge badge-md text-bg-<?=  EntityDocumentStatusUtil::getStatusCssClass(EntityDocumentStatusUtil::MISSING)?>">
                                                    <?=  EntityDocumentStatusUtil::getStatusTrans( EntityDocumentStatusUtil::MISSING)?>
                                                </span>
                                            <?php }?>
                                        </div>
                                        <?php if($row->expiry_date && $isExpirable){?>
                                            <span class="title-subtitle-text">
                                            <?= format_date($row->expiry_date)  ?></span>
                                        <?php } ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="<?= $showUrl ?>" <?= $anchorAttribute ?> class="listing-cell-link"></a>
                                <?= $row->created ? format_date($row->created) : "--" ?>
                            </td>
                            <td>
                                <?php if($row->id){?>
                                    <?= $this->includeSection('entitiy/partials/listing_preview_attachments', [
                                        'attachments' => $row->documents,
                                        'view_url'=> route('owner.entity.show', ['entityKey'=>EntityKeyTypesUtil::ENTITY_DOCUMENT , 'id'=>$row->id])
                                    ]);?>
                                <?php }?>
                            </td>
                            <td>
                                <a href="<?= $showUrl ?>" <?= $anchorAttribute ?> class="listing-cell-link"></a>
                                <?= $created_by ?>
                            </td>
                            <?php if($row->id){ ?>
                                <?= $this->includeSection('entity_documents/partials/row-actions', ['row' => $tableRow]) ?>
                            <?php }else{?>
                                <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                                        <?php if($canUload) { ?>
                                            <a href="<?= $createUrl ?>" class="btn btn-success text-nowrap" type="button" target="_blank">
                                                <i class="mdi mdi-upload me-sm-2 fs-10"></i><?= __t('Upload New')?>
                                            </a>
                                        <?php }?>
                                </td>
                                <?php }?>
                           
                        </tr>

                    <?php endforeach;  ?>

                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
<script>
window.addEventListener('message', function(event) {
    if (event.data?.action === 'resizeSelectize') {
        console.log('Resize message received');

        function resize() {
            const controls = document.querySelectorAll('.selectize-control');
            if (controls.length > 0) {
                console.log(`Resizing ${controls.length} Selectize controls`);
                controls.forEach(ctrl => {
                    // Fix the input width specifically
                    const input = ctrl.querySelector('.selectize-input input');
                    const selectizeInput = ctrl.querySelector('.selectize-input');
                    
                    if (input && selectizeInput) {
                        // Reset the problematic 4px width
                        input.style.width = '';
                        input.style.minWidth = '100px';
                        selectizeInput.style.width = '100%';
                        
                        // Trigger selectize refresh if available
                        const select = ctrl.querySelector('select');
                        if (select && select.selectize) {
                            select.selectize.refreshOptions();
                            select.selectize.refreshItems();
                        }
                    }
                });
                return true;
            }
            return false;
        }

        // Try immediate resize
        if (!resize()) {
            // Keep checking every 200ms until found (max 5s)
            let waited = 0;
            const interval = setInterval(() => {
                if (resize() || waited >= 5000) {
                    clearInterval(interval);
                }
                waited += 200;
            }, 200);
        }

        // Watch for dynamically added selectize controls
        const observer = new MutationObserver(() => {
            resize();
        });
        observer.observe(document.body, { childList: true, subtree: true });
    }
});
</script>

<?php 
    if(count($table->getRows()) < 10 ):
?>
        <script>
            $( document ).ready(function() {
                $('[data-bs-target="[data-filters-collapse]"]').click();
            });
        </script>
<?php endif;?>
