<?php

// use App\Utils\EntityKeyTypesUtil;
// use Carbon\Carbon;
// use Izam\Daftra\Common\Utils\ContractInstallmentUtil;

use App\Services\EntityDocumentService;
use App\Utils\EntityKeyTypesUtil;
use Carbon\Carbon;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;

$isIframe = isset($_GET['iframe']);
$anchorAttribute = $isIframe ? "target = _blank" : "";
extract($listingExtraData);
?>
<div class="d-block d-lg-none">
    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table">
                <thead>
                    <tr>
                        <th class="listing-cell-start">
                            <?php if (!$isIframe) { ?>
                            <div class="d-flex gap-6">
                                <div class="listing-cell-check">
                                    <input class="form-check-input" type="checkbox" data-lc-toggle="current-page" />

                                </div>
                            </div>
                            <?php } else { ?>

                            <div class="d-flex align-items-center gap-6">
                                <div class="listing-cell-check listing-cell-check-sm">
                                    <div class="dropdown" data-lt-dropdown="true">
                                        <button class="btn btn-link dropdown-toggle dropdown-toggle-2 px-0"
                                            data-bs-toggle="dropdown" aria-expanded="false" type="button">
                                            <input class="form-check-input" type="checkbox"
                                                data-lc-toggle="current-page" />
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <button class="dropdown-item" type="button" data-lc-check="none">
                                                    <i class="mdi mdi-close text-primary"></i>
                                                    <span><?= __t('None') ?></span>
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                    data-lc-check="current-page">
                                                    <i class="mdi mdi-check-underline text-primary"></i>
                                                    <span><?= __t('All (Current Page)') ?></span>
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item" type="button"
                                                    data-lc-check="all-filtered-pages">
                                                    <i class="mdi mdi-check-all text-primary"></i>
                                                    <span><?= __t('All Filtered (All Pages)') ?></span>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <?php } ?>
                        </th>
                        <th width="45" class="listing-cell-end text-end">
                            <div class="dropdown" data-lt-dropdown="true">
                                <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    
                    <?php foreach ($table->getRows() as $key => $tableRow):
                            $row = $tableRow->getData();
                            $status = resolve(EntityDocumentService::class)->calculateStatus($row);
                            $isExpirable = $row->entity_document_type?->is_expirable ?? 0;
                            $expireyDate = Carbon::parse($row->expiry_date);
                            $expireIn = $isExpirable ? $expireyDate->diffInDays(Carbon::today()) : null;
                            $rowTypeCount =  $documentTypesCount[$row->entity_document_type_id] ?? 0;
                            $createUrl = route('owner.entity.create' ,['entityKey'=>'entity_document' , 'subEntityKey'=>'staff','subEntityId'=> $entity_id ,'entity_document_type_id' => $row->document_types_id]);
                            $showUrl =  $row->id ? route("owner.entity.show", [EntityKeyTypesUtil::ENTITY_DOCUMENT, 'id' => $row->id]): $createUrl;
                            $filterUrl = '/v2/owner/entity/entity_document/list?iframe=1&filter[entity_key]=staff&filter[entity_id]='.$row->entity_id.'&show_filters=1&filter[entity_document_type_id]='.$row->entity_document_type_id;
                            $id = $row->id;
                            if(!$id){//missing document
                                $id = "type_".$row->document_types_id;
                            }
                        ?>
                    <tr>
                        <td>
                            <a href="<?= $showUrl ?>" <?= $anchorAttribute ?> class="listing-cell-link"></a>
                            <div class="d-flex gap-6">
                                <div class="listing-cell-check <?php if ($isIframe) echo "listing-cell-check-sm" ?>">
                                    <input class="form-check-input " type="checkbox" name="ids[]" value="<?= $id ?>"
                                        data-lc-item="true" />
                                </div>
                                <div class="text-nowrap">
                                    <div class="title-text mb-9">
                                        <div class="title-text mb-4">
                                            <p><?= $row->document_types_name ?? null ?>
                                                <?php if ($rowTypeCount && $rowTypeCount > 1 && $row->id && !$isRelatedDocuments): ?>
                                                    <a class="thumb-subtitle-text text-decoration-none"
                                                        href="<?=$filterUrl?>" data-bs-toggle="tooltip"
                                                        data-bs-placement="top" aria-label="more"
                                                        data-bs-original-title="<?= sprintf(__t('%s Documents available of the same type, Click on the icon to show'),$rowTypeCount-1)?>">
                                                        #<?= $row->id ?>
                                                    </a>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="vstack gap-4">
                                        <div class="text-nowrap">
                                            <div class="vstack gap-2">
                                                <div class="hstack gap-2 justify-content-start">
                                                    <?php if($row->id){?>
                                                        <span class="badge text-bg-<?=  EntityDocumentStatusUtil::getStatusCssClass($status)?>"><?=  EntityDocumentStatusUtil::getStatusTrans($status,$expireIn)?></span>
                                                    <?php }else{?>
                                                        <span class="badge text-bg-<?=  EntityDocumentStatusUtil::getStatusCssClass(EntityDocumentStatusUtil::MISSING)?>">
                                                            <?=  EntityDocumentStatusUtil::getStatusTrans( EntityDocumentStatusUtil::MISSING)?>
                                                        </span>
                                                    <?php }?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php if($row->expiry_date && $isExpirable){?>
                                            <div class="text-meta mt-3">
                                                <p class="opacity-50"><?= __t('Expiry Date')?>: <?= format_date($row->expiry_date) ?></p>
                                            </div>
                                        <?php } ?>
                                        <div class="text-meta mt-3">
                                            <p class="opacity-50"><?= $row->name ?? "--" ?></p>
                                        </div>
                                        <div class="text-meta mt-3">
                                            <p class="opacity-50"><?= format_date($row->created) ?? "--" ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <?php if($row->id){ ?>
                                <?= $this->includeSection('entity_documents/partials/row-actions', ['row' => $tableRow]) ?>
                            <?php }else{?>
                                <td class="text-end h-0" data-lt-dropdown-cell="true">
                                    
                                    <div class="d-flex flex-column h-100 align-items-end">
                                        <a href="<?= $createUrl ?>" class="btn btn-icon btn-success" type="button" target="_blank">
                                            <i class="mdi mdi-upload"></i>
                                        </a>
                                    </div>
                                </td>
                                <?php }?>
                       
                    </tr>
                    <?php endforeach;  ?>

                </tbody>
            </table>
        </div>
    </div>
</div>