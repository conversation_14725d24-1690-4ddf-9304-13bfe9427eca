<?php

use Izam\View\Form\Helper\Form\FormHeader;
use Izam\View\Form\Helper\FormCollection;
use Izam\View\Form\Helper\FormRow;

$extends = 'layouts/layout';
$this->sharedVars['extraContainerClass']= '';
$elementHelper = new FormCollection();
$helper = new FormHeader();
$helper->setView($this);
$formRowHelper = new FormRow();
$this->viewVars['_PageBreadCrumbs'] = $this->sharedVars['generalBreadCrumbs'];
?>
<?php $this->section('page-head') ?>
<?= $helper->render($pageHead); ?>
<?php $this->endSection() ?>

<?php $this->section('article-start') ?>
<form class="m-app-form" action="<?= $extraData['action'] ?>" method="post" data-app-form="units" class="validate form-validate">
    <input type="hidden" name="_token" value="<?= csrf_token() ?>">
    <input type="hidden" name="_method" value="<?= $extraData['method'] ?>">
    <?php $this->endSection() ?>

    <div class="container">
        <div class="my-10 vstack gap-10">

            <div class="card">
                <div class="card-header">
                   <?= __t('Document Details') ?>
                </div>
                <div class="card-body">
                    <div class="card-row">

                        <div class="col-lg-4">
                            <div class="form-group" data-form-group="true">
                                <label class="form-label mb-0"><?= __t("Name") ?><span
                                            class="text-danger"> *</span></label>
                                <?= $formRowHelper->render($form->get('name')->setValue($extraData['documentName'] ?? null)->setLabel(false)); ?>
                            </div>
                        </div>

                        <div class="col-lg-8" data-select-type-column="true">
                            <?php
                                $typeOptions = array_merge([
                                    ['value'=>'__clear__', 'label'=> __t('Please Select Document Type')]
                                ] , $extraData['entity_types']);
                            ?>
                            <div class="form-group" data-form-group="true">
                                <label class="form-label mb-0"><?= __t("Document Type") ?></label>
                                <?= $formRowHelper->render(
                                    $form->get('entity_document_type_id')->setValueOptions($typeOptions)->setValue($extraData['entity_document_type_id'] ?? null)->setLabel(false)->setAttribute('placeholder',__t('Please Select Document Type'))
                                ); ?>
                            </div>
                        </div>
                        <div class="col-lg-4 d-none" data-date-column="true">
                            <div class="form-group" data-form-group="true">
                                <label class="form-label mb-0"><?= __t("Expiry Date") ?><span class="text-danger"> *</span></label>
                                <?= $formRowHelper->render($form->get('expiry_date')->setLabel(false)->setAttribute('id','expiry_date')); ?>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group" data-form-group="true">
                                <label class="form-label mb-0"><?= __t("Documents") ?><span
                                            class="text-danger"> *</span></label>
                                <?=
                                    $formRowHelper->render(
                                        $form->get('documents')
                                            ->setLabel(false)
                                            ->setAttribute('data-form-rules','required')
                                            ->setAttribute('data-form-include',1)
                                            ->setAttribute('id','documents_input')
                                    );
                                ?>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group" data-form-group="true">
                                <label class="form-label mb-0"><?= __t("Notes") ?></label>
                                <?= $formRowHelper->render($form->get('notes')->setLabel(false)->setAttribute('id','notes_input')); ?>
                            </div>

                        </div>
                        <?= $formRowHelper->render($form->get('entity_key')->setValue($extraData['entity_key'])); ?>
                        <?= $formRowHelper->render($form->get('entity_id')->setValue($extraData['entity_id'])); ?>
                        <?= $formRowHelper->render($form->get('id')); ?>
                    </div>
                </div>

            </div>

        </div>
    </div>

    <?php $this->section('article-end') ?>
</form>
<?php $this->endSection() ?>
<?php
$this->section('page-after-js');?>
<script src="/frontend/assets/js/pages/entity-documents/create.js"></script>
<?php  if(!isset($extraData['id'] )):?>
<script>
$(document).ready(function () {
    const $typeSelect = $('#entity_document_type_id');
    const $nameInput  = $('[name="name"]'); 
    if ($typeSelect.length && $nameInput.length) {
        $typeSelect.on('change', function (value) {

            const option = $typeSelect.text();
            if (option && value !== "__clear__") {
                $nameInput.val(option);
            } else {
                $nameInput.val('');
            }
        });
    }
});
</script>
<?php endif; ?>
<?php $this->endSection();  ?>