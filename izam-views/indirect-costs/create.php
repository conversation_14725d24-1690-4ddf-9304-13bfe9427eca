<?php

use App\Repositories\JournalRepository;
use App\Repositories\JournalTransactionRepository;
use App\Repositories\ManufacturingOrderRepository;
use App\Services\ManufacturingIndirectCostService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Repository\DynamicRepo;
use App\Models\JournalTransaction;
$extends = 'layouts/deprecated-layout';
$dynamicRepo = resolve(DynamicRepo::class);

$entityKey = $form->getAttribute('name');
$data = count(old()) ? old() : $form->getData();
$isEditing = isset($data['id']);
$method = $isEditing ? 'PUT' : 'POST';
$account = null;
if (isset($data['journal_account_id'])){
    $account = $dynamicRepo->findRecord('journal_accounts',  $data['journal_account_id']);
}
$formUrl = $isEditing ? route('owner.entity.update', ['entityKey' => $entityKey, 'id' => $data['id']]) : route('owner.entity.store', ['entityKey' => $entityKey]);
$cancelUrl = $isEditing ? route('owner.entity.show', ['entityKey' => $entityKey, 'id' => $data['id']]) : route('owner.entity.list', ['entityKey' => $entityKey]);

$title = $isEditing ? sprintf(__t('Edit %s'), __t('Indirect Costs')) : sprintf(__t('Add %s'), __t('Indirect Costs'));
$this->viewVars['title_for_layout'] = sprintf($title, __t('Indirect cost'));
$this->viewVars['_PageBreadCrumbs'] = [
    ['title' => __t("Indirect Costs"), 'link' => route('owner.entity.list', ['entityKey' => $entityKey])],
];
if ($isEditing) {
    $editTitle = $account->name. ' #' . $account->code;
    $this->viewVars['_PageBreadCrumbs'][] = ['title' => "{$editTitle}", 'link' => $cancelUrl];
}
$this->viewVars['_PageBreadCrumbs'][] = ['title' => $isEditing ? __t("Edit") : __t("Add")];
$fillForm= false;
$logsData = [];
$loadedTransactions= [];
$ignoreFromJournalSelect= [];
if (count($data)){
    $manufacturingIndirectService = resolve(ManufacturingIndirectCostService::class);
    $manufacturingOrderRepo = resolve(ManufacturingOrderRepository::class);
    $loadedTransactions =[];

    if (isset($data['id'])){
        $distributions = $fullData->data['distributions'] ?? $data['distributions'];
        $transactions = $fullData->data['transactions'] ?? $data['transactions'];
    }else{
        $distributions = $data['distributions'] ?? [];
        $transactions = $data['transactions'] ?? [];
    }
    if ($account){
        $data['account']=(array) $account;
    }

    $oldOrders =$dynamicRepo->findWhereIn('manufacturing_orders','id' , array_column($distributions, 'manufacturing_order_id'));
    $ordersOptions  = $manufacturingOrderRepo->generateOptionsForIndirectCosts($oldOrders , $data['distributions']);
    $data['oldOrders']=[];
    foreach ($distributions as &$dis){
        $optArr = array_filter($ordersOptions , function ($o) use ($dis){
            return $o['id'] == $dis['manufacturing_order_id'];
        });
        if (count($optArr)){
            $item = array_shift($optArr);
            if (isset($dis['id'])){
                $item['dist_id'] = $dis['id'];
                $item['orderId'] = (string) $dis['manufacturing_order_id'];
            }
            $data['oldOrders'][]= $item;
        }
    }
    $transactionIds = [];
    foreach ($transactions as $fdt){
        $transactionIds[] = $fdt['journal_transaction_id'];
    }
    $oldTransactions = JournalTransaction::with('journal','journal_account')
        ->whereIn('id',$transactionIds)
        ->get();
    if ($isEditing){
        $logsData = $manufacturingIndirectService->resolveLogs($transactions,$data['created'] ?? null);
        $ignoreFromJournalSelect = array_column($logsData , 'journal_transaction_id');
    }
    $transactionOptions = [];
    foreach ($oldTransactions as &$transaction){
        $transactionOptions[]=$manufacturingIndirectService->createTransactionOption($transaction);
    }
    $loadedTransactions  = [];
    foreach ($transactions as &$tr){
        $optArr = array_filter($transactionOptions, function ($t) use ($tr){
            return $t['id'] == $tr['journal_transaction_id'];
        });
        if (count($optArr)){
            $item = array_shift($optArr);
            if (isset($tr['id'])){
                $item['tr_id'] = $tr['id'];
                $item['amount'] = $tr['amount'] ?? $item['amount'];
                $item['latest_journal_transaction_log_id'] = $tr['latest_journal_transaction_log_id'];
            }
            $loadedTransactions[]= $item;
        }else{
            $loadedTransactions[]= ['amount'=>$tr['amount']  ];
        }
    }
    $data['loadedTransactions']= $loadedTransactions;
    $fillForm = true;
}
?>


<?php $this->section('page-start') ?>
    <link href="<?= getCdnAssetsUrl() ?>css/forms/plugin_select2.min.css" rel="stylesheet"/>
    <link href="<?= getCdnAssetsUrl() ?>css/forms/plugin_select2_rtl.min.css" rel="stylesheet"/>

    <style>
        .ui-table-box-body-item-editable-input-container {
            position: relative;
        }

        .generate-icon {
            display: flex;
        }

        .prefix-text {
            display: none;
        }

        .generate-icon,
        .prefix-text {
            cursor: pointer;
            position: absolute;
            top: 0;
            bottom: 0;
            padding: 0 10px;
            align-items: center;
        }

        .ui-table-box-body-item-editable-input-container:hover .prefix-text {
            display: flex;
        }
        .daterangepicker{
            z-index: 99999999 !important;
        }

        [dir="ltr"] .generate-icon,
        [dir="ltr"] .prefix-text {
            right: 0;
        }

        [dir="rtl"] .generate-icon,
        [dir="rtl"] .prefix-text {
            left: 0;
        }

        [dir="ltr"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .generate-input {
            padding-right: 52px;
        }

        [dir="rtl"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .generate-input {
            padding-left: 52px;
        }

        [dir="ltr"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .prefix-input {
            padding-right: 102px;
        }

        [dir="rtl"] .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container .prefix-input {
            padding-left: 102px;
        }

        .select2-results__options {
            padding-left: 0 !important;
        }

        .account-select .select2 {
            height: 100% !important;;
        }

        .select2-container {
            height: 100% !important;
        }

        .ui-table-box-row .select2-container--bootstrap4 .select2-selection, .ui-table-box-row .select2-container--bootstrap4.select2-container--focus.select2-container--open .select2-selection {
            box-shadow: none !important;
            height: 100% !important;;
            border: 0 !important;;
        }
    </style>
    <style>
        :root {
            --bs-dark: #4e5381;
            --bs-dark-2: #373B50;
            --bs-dark-3: #75799d;

            --bs-light: #f6f9fc;
            --bs-light-2: #c1d2e6;
            --bs-light-3: #9b9eb8;
            --bs-secondary: #E4EBF2;
            --bs-border-width: 1px;
            --bs-border-style: solid;
            --bs-border-color: #dee2e6;
            --bs-light-rgb: 246, 249, 252;
            --bs-dark-3-rgb: 117, 121, 157;
        }

        .l-input-label {
            height: 21px;
        }

        .accordion-collapse .accordion-collapse-btn {
            width: 100%;
            color: #202124;
            background-color: #f6f9fc;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid #E4EBF2;
            line-height: 1;
            opacity: 1;
            min-height: 43px;
        }

        .accordion-collapse .accordion-collapse-btn .accordion-collapse-arrow i {
            font-size: 22px;
        }

        .gap-4,
        .status-icon,
        .status-circle,
        .accordion-collapse .accordion-collapse-btn .accordion-collapse-title {
            gap: 0.5rem !important;
        }

        .align-items-center,
        .status-icon,
        .status-circle,
        .text-meta,
        .title-big-text,
        .accordion-collapse .accordion-collapse-btn .accordion-collapse-title,
        .toast {
            align-items: center !important;
        }

        .d-inline-flex,
        .accordion-collapse .accordion-collapse-btn .accordion-collapse-title {
            display: inline-flex !important;
        }

        .accordion-collapse .accordion-collapse-btn .accordion-collapse-title i {
            font-size: 22px;
        }

        .filter-group-input {
            display: flex;
        }

        .filter-group-input > * {
            flex: 4 1 auto;
        }

        .text-dark-3 {
            --bs-text-opacity: 1;
            color: rgba(117, 121, 157, 1) !important;
        }

        .d-none {
            display: none !important;
        }

        [dir=ltr] .text-end,
        [dir=rtl] .text-start {
            text-align: right !important;
        }

        [dir=rtl] .text-end,
        [dir=ltr] .text-start {
            text-align: left !important;
        }


        [dir=rtl] .text-end {
            text-align: left !important;
        }

        [dir=ltr] .accordion-collapse-btn {
            text-align: left !important;
        }

        [dir=rtl] .accordion-collapse-btn {
            text-align: right !important;
        }


        .accordion-collapse .accordion-collapse-btn[aria-expanded^=false] [aria-expanded^=true] {
            display: none;
        }

        .accordion-collapse .accordion-collapse-btn[aria-expanded^=true] [aria-expanded^=false] {
            display: none;
        }

        /* Subform style */
        .subform-table thead th {
            font-size: 0.875rem;
            font-weight: 400;
            color: var(--bs-dark-3);
            padding: 10px 15px;
            background: var(--bs-light);
            border: 1px solid var(--bs-secondary);
        }

        .subform-table tbody td {
            border: 1px solid var(--bs-secondary);

        }

        .subform-table tbody tr td.subform-cell-text {
            background: var(--bs-light);
            color: var(--bs-black);
            padding: 10px 15px;
            font-size: 16px;
        }


        .subform-table tfoot tr td.subform-cell-text,
        .subform-table tfoot tr th.subform-cell-text {
            background: var(--bs-light);
            color: var(--bs-black);
            padding: 10px 15px;
            font-size: 0.875rem;
        }

        .subform-table tbody tr td .input-container .form-group {
            height: 100%;
            display: flex;
            flex-direction: column;
            margin-bottom: 0;
        }

        .ui-table-box-body-item--editable .selectize-input,
        .ui-table-box-body-item--editable textarea,
        .ui-table-box-body-item--editable input {
            height: 100%;
        }

        [dir="ltr"] .border-start  {
            border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }
        [dir="rtl"] .border-start {
            border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }

        .border-bottom {
            border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }

        [dir="ltr"] .border-end  {
            border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }
        [dir="rtl"] .border-end {
            border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        }

        .d-table-row {
            display: table-row !important;
        }

        .d-none {
            display: none !important;
        }

        .fw-medium {
            font-weight: 500 !important;
        }

        .fw-bold {
            font-weight: 600 !important;
        }

        .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container.d-flex-row {
            flex-direction: row;
        }

        .ui-table-box-body-item--editable .ui-table-box-body-item-editable-input-container.d-flex-row > * {
            flex: 1 1;
        }

        .d-flex {
            display: flex;
        }

        .d-flex.justify-content-between {
            display: flex;
            justify-content: space-between;
        }

        .d-flex.align-items-center {
            align-items: center;
        }

        .bg-light {
            --bs-bg-opacity: 1;
            background-color: rgba(var(--bs-light-rgb), 1) !important;
        }

        .text-dark-3 {
            --bs-text-opacity: 1;
            color: rgba(var(--bs-dark-3-rgb), 1) !important;
        }

        .px-10 {
            padding-right: 1.25rem !important;
            padding-left: 1.25rem !important;
        }

        .ms-2 {
            margin-inline-start: 16px;
        }

        .full-height {
            height: 100%;
        }

        .total-cell {
            background-color: rgba(32, 33, 36, 1);
            color: #FFF;
            font-weight: 500;
            font-size: 1.25rem;
            gap: 1.25rem;
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
            padding-right: 1rem;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .total-container-cell {
            display: flex;
            justify-content: space-between;
            align-items: center;

        }

        .total-container-cell .title {
            color: #75799D;
        }

        .total-container-cell .val {
            color: #202124;
            padding-inline-end: 50px;
        }

        .l-btn-group-box .l-btn-group {
            gap: 10px
        }

        .search-listing-table {
            width: 100%;
        }

        .search-listing-table tr {
            border: 1px solid #E4EBF2 !important;
        }

        .search-listing-table tbody tr td {
            padding: 10px 10px;

        }

        .search-listing-table tbody tr td a {
            text-decoration: none !important;
        }

        .underline-text {
            text-decoration: underline !important;
        }

        .mb-5 {
            margin-bottom: 5px;
        }

        .submit-form {
            border: 0;
            background-color: #13B272;
            outline: none;
            color: #FFF;
        }

        @media (min-width: 992px) {
            .d-lg-inline-block {
                display: inline-block !important;
            }

            .subform-table tbody tr td {
                border-bottom: 1px solid var(--bs-secondary);
            }

            .d-lg-table-row {
                display: table-row !important;
            }

            .d-lg-none {
                display: none !important;
            }

        }

        @media (max-width: 993px) {
            .subform-table thead {
                display: none;
            }
        }
        .alert{
            position: relative;
            padding: .75rem 1.25rem;
            border: 1px solid transparent;
        }
        .alert-warning {
            color: #855008;
            background-color: #ffebcf;
            border-color: #ffe2bc;
        }
  
        @media (min-width: 992px) {
            .m-app-page-head-all {
                margin: 0px 16px;
            }
            [dir="ltr"] .ui-table-add-btn  {
                margin-left: -2px;
            }
            [dir="rtl"] .ui-table-add-btn  {
                margin-right: -2px;
            }
        }

        @media (max-width: 767.98px) {
            .m-app-page-head-all .l-container {
                padding-left: 4px;
                padding-right: 4px;
            }
            /*.ui-table-box-body-item--editable {*/
            /*    max-width: 245px;*/
            /*}*/

            .accordion-collapse .accordion-collapse-btn {
                padding-inline-end: 12px;
            }
            [dir="ltr"] .ui-table-add-btn  {
                margin-left: -1px;
            }
            [dir="rtl"] .ui-table-add-btn  {
                margin-right: -1px;
            }
        }

        .ui-table-add-btn {
            min-height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0 !important;
            margin-top: -1px;
            margin-bottom: -1px;
        }

       
    </style>



    <form class="m-app-form" action="<?= $formUrl ?>" method="POST" data-app-form="indirect-cost">
        <?php $this->endSection() ?>
        <input type="hidden" name="_method" value="<?= $method ?>">
        <input type="hidden" name="_token" value="<?= csrf_token() ?>">
        <input type="hidden" name="id" value="<?= isset($data['id']) ? $data['id'] : '' ?>" id="id">

        <?php $this->section('page-head') ?>
        <div class="m-app-page-head">
            <div class="m-app-page-head-all">
                <div class="l-container">
                    <div class="l-flex-row l-flex--align-center">
                        <div class="l-flex-col-lg-12 u-text-align-end">
                            <div class="l-btn-list-h l-btn-list-h--inline">
                                <a href="<?= $cancelUrl ?>"
                                   class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-secondary u-text-color-action u-text-hover-color-action">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                    <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-close-thick"></i>
                                            <span class="ui-btn-inner-text"><?= __t('Cancel') ?></span>
                                        </span>
                                </a>
                                <button class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white"
                                        type="submit">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                                    <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-content-save"></i>
                                            <span class="ui-btn-inner-text"><?= __t('Save') ?></span>
                                        </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php if (isset($errors['transactions']) && count($errors['transactions']) && is_string($errors['transactions'][0])) {?>
            <div class="m-app-flash-message l-container">
                <div class="l-flash-message l-flash-message--spacing-top  ui-flash-message ui-flash-message--danger" data-flash-message="true">
                    <div class="l-flex-row l-flex--align-center">
                        <div class="l-flex-col-11">
                            <div class="l-icon-w-text">
                                <i class="far fa-check-circle ui-icon ui-icon--size-16 ui-flash-message-body-icon"></i>
                                <div class="ui-flash-message-body-text">
                                    <?= $errors['transactions'][0] ?>
                                </div>
                            </div>
                        </div>
                        <div class="l-flex-col-1 u-text-align-end" style="padding: 0;">
                            <button type="button" class="ui-btn-icon l-icon--size-24 ui-flash-message-close-icon" style="display: inline-flex; align-items: center; justify-content: center;" data-flash-message-close-btn="true">
                                <i class="mdi mdi-window-close ui-icon ui-icon--size-12" style="top: 0;"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php   } ?>
        <?php $this->endSection() ?>

        <div class="m-app-content">
            <div class="l-create-card-box">

                <div class="l-container">
                    <?= $this->includeSection('indirect-costs/partials/transaction-status', ['errors' => $errors,'logsData'=>$logsData]) ?>

                    <div class="ui-card">
                        <div class="ui-card-header">
                            <h3 class="ui-card-header-title"><?= __t('Indirect Cost Information') ?></h3>
                        </div>
                        <div class="ui-card-content ui-card-content-box--spacing-bottom-0">
                            <div class="ui-card-content-box">
                                <div class="l-flex-row l-flex-row--spacing-8">

                                    <?php if(!$isEditing && getAuthStaff('id') !=0){ ?>
                                        <input type="hidden" name="staff_id" value="<?= getAuthStaff('id') ?>">
                                    <?php } ?>
                                    <div class="l-flex-col-lg-6">
                                        <div class="l-input-box">
                                            <label for="account"
                                                   class="l-input-label ui-input-label"><?= __t('Account') ?><span
                                                        class="u-text-color-danger">&nbsp;*</span></label>
                                            <select name="journal_account_id" id="indirect-cost-account"
                                                    class="account-select l-input ui-input"
                                                    placeholder="<?= __t('Select') . ' ' . __t('Account') ?>"
                                                    data-app-form-validate="required">
                                                <?php if($fillForm && isset($data['account'])): ?>
                                                    <option selected value="<?= $data['account']['id'] ?>"><?= $data['account']['name'] ?></option>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="l-flex-col-lg-3">
                                        <div class="l-input-box">
                                            <label for="date_from"
                                                   class="l-input-label ui-input-label"><?= __t('Start Date') ?><span
                                                        class="u-text-color-danger">&nbsp;*</span></label>
                                            <div class="l-input-w-icon-box l-input-w-icon-box--align-end">
                                                <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                                <input class="l-input ui-input" autocomplete="off" id="date_from"
                                                       value="<?= $fillForm ? $data['date_from'] ?? '' : '' ?>" type="text" name="date_from"
                                                       placeholder="<?= __t('Select date') ?>" data-app-form-date="true"
                                                       >
                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'date_from']) ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="l-flex-col-lg-3">
                                        <div class="l-input-box">
                                            <label for="date_to"
                                                   class="l-input-label ui-input-label"><?= __t('End Date') ?><span class="u-text-color-danger">&nbsp;*</span></label>
                                            <div class="l-input-w-icon-box l-input-w-icon-box--align-end">
                                                <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon"></i>
                                                <input class="l-input ui-input" autocomplete="off" id="date_to" value="<?= $fillForm ? $data['date_to'] ?? '' : '' ?>"
                                                       type="text" name="date_to" placeholder="<?= __t('Select date') ?>"
                                                       data-app-form-date="true">
                                                <?= $this->includeSection('partials/input-error-message', ['errors' => $errors, 'input_name' => 'date_to']) ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="l-flex-col-lg-3">
                                        <label class="l-input-label ui-input-label"><?= __t('Distribution Type') ?><span
                                                    class="u-text-color-danger">&nbsp;*</span></label>

                                        <div class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                                            <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                                <input type="radio" name="type" value="based_on_quantity"
                                                       <?= (!$fillForm || ($fillForm && $data['type'] === 'based_on_quantity')) ? 'checked' : '' ?>
                                                       checked
                                                       class="ui-radio">
                                                <span class="ui-radio-label-content"><?= __t('Based on QTY') ?></span>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="l-flex-col-lg-3">
                                        <label class="l-input-label ui-input-label"> </label>

                                        <div class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                                            <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                                <input type="radio" name="type" value="based_on_amount"
                                                       class="ui-radio" <?= ($fillForm && $data['type'] === 'based_on_amount') ? 'checked' : '' ?>
                                                >
                                                <span class="ui-radio-label-content"><?= __t('Based on Amount') ?></span>
                                            </label>
                                        </div>
                                    </div>


                                </div>
                            </div>

                            <br/>
                            <?= $this->includeSection('indirect-costs/partials/transactions-subform', ['errors' => $errors,'fillForm'=>$fillForm ,'loadedTransactions'=>$loadedTransactions,'input_name' => 'name','ignoreFromJournalSelect'=>$ignoreFromJournalSelect]) ?>

                            <span class="l-h-line"
                                  style="border-bottom: 2px dashed var(--color-secondary);opacity: 0.7; margin: 2rem 0;"></span>
                            <?= $this->includeSection('indirect-costs/partials/manufacturing-orders-subform', ['errors' => $errors,'fillForm'=>$fillForm ,'data'=>$data,'input_name' => 'name']) ?>
                            <br/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php $this->section('page-end') ?>
    </form>
<?php $this->endSection() ?>

<?php $this->section('page-after-js') ?>

    <script>
        var jQuery = $;
    </script>

    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <script src="<?= getCdnAssetsUrl() ?>js/forms/plugin_select2.js"></script>

    <script src="/v2/js/account-select.js"></script>

    <script>
        $(document).ready(function () {
            $('.account-select').initJournalAccountSelect();
            setTimeout(()=>{
                var $account = $('#indirect-cost-account');
                $('.account-select').initJournalAccountSelect();
                var oldData =  <?= json_encode($data) ?> || [];
                var fillForm = <?= json_encode($fillForm) ;?> || false

                var $fromDateInput = $('input[name="date_from"]');
                var $toDateInput = $('input[name="date_to"]');
                var fromDate = $fromDateInput.val() || '';
                var toDate = $toDateInput.val() || '';

                var selectedAccountId = null;
                var selectedTransactions = [];
                var selectedTransactionsIds = [];
                var selectedTransactionManufactureOrders = [];
                var selectedTransactionManufactureOrdersIds = [];
                var transactionTotalAmount = 0;
                var manufactureOrdersTotalAmount = 0;
                var doneRendering = false;
                var allowLoading = false;
                setTimeout(()=>{
                    doneRendering = true
                },200)


                var calculationType = 'based_on_quantity';
                var evaluateManufactureOrdersTotalAmount = function (){
                    manufactureOrdersTotalAmount=0
                    selectedTransactionManufactureOrders.forEach((order,index)=>{
                        if (order && order.requestAmount){
                            manufactureOrdersTotalAmount+=parseFloat(order.requestAmount)
                        }
                    })
                    $('.manufacture_order_total_quantity').text(selectedTransactionManufactureOrders.filter(o => o).length)
                    $('.manufacturing_orders_total_amount').text(manufactureOrdersTotalAmount.toFixed(2))


                }
                var evaluateTransactionsTotalAmount = function (){
                    transactionTotalAmount=0
                    selectedTransactions.forEach((transaction,index)=>{
                        if (transaction){
                            transactionTotalAmount+= +transaction.amount
                        }
                    })
                    $('.journal-transactions-total-quantity').text(selectedTransactions.length)
                    setTimeout(()=>{
                        $('.journal_transactions_total_amount').text(transactionTotalAmount.toFixed(2))
                    },200)
                }

                if (oldData){
                    selectedAccountId = oldData.journal_account_id;
                    if ( oldData.loadedTransactions && oldData.loadedTransactions.length  ){
                        selectedTransactions = oldData.loadedTransactions;
                    }
                    if ( oldData.oldOrders && oldData.oldOrders.length  ){
                        selectedTransactionManufactureOrders = oldData.oldOrders;
                        evaluateManufactureOrdersTotalAmount();
                    }
                    if (oldData.type){
                        calculationType = oldData.type
                    }
                    evaluateTransactionsTotalAmount()
                }
                var transactionsApiUrl = `/v2/owner/indirect-costs/search-account-transactions?ignore=&accountId=${selectedAccountId}&fromDate=${fromDate}&toDate=${toDate}&term=__q__&_type=query&q=__q__`;
                let url = `/v2/owner/indirect-costs/search-manufacturing-orders?ignore=&term=__q__&_type=query&q=__q__`;

                var updateUrlDates = function (type , date){
                    var updatedUrl = '';
                    if (type === 'from'){
                        updatedUrl =transactionsApiUrl.replace("fromDate=" , `fromDate=${date}`)
                    }else{
                        updatedUrl =transactionsApiUrl.replace("toDate=" , `toDate=${date}`)
                    }
                    transactionsApiUrl = updatedUrl;
                    loadAccountTransactions(selectedAccountId);
                }

                $fromDateInput.on('change' ,function (e) {
                    fromDate = $(this).val();
                    updateUrlDates('from' , fromDate)
                })
                $toDateInput.on('change' ,function () {
                    toDate = $(this).val();
                    updateUrlDates('to' , toDate)
                })

                var removeOldTransactionOptions = function ($input , newTransactionOptions =[]){
                    var inputHtml = $input[0];
                    var selectizeControl = inputHtml.selectize;
                    var options = Array.from(Object.keys(selectizeControl.options))
                    options.forEach((opt) => {
                        let dataAttributes = selectizeControl.options[opt]
                        if (opt !== '__blank__' && !dataAttributes.isSelected && !selectedTransactionsIds.includes(`${opt}`)) {
                            selectizeControl.removeOption(opt)
                        }
                    })
                }

                function clearInputOptions($manufacturingInput) {
                    var options = Array.from(Object.keys($manufacturingInput[0].selectize.options))
                    options.forEach((opt) => {
                        if (opt !== '__blank__') {
                            $manufacturingInput[0].selectize.removeOption(opt)
                        }
                    })
                    $manufacturingInput[0].selectize.clear()
                }

                var resetSelectize = function (input){
                    if (!input || !input.selectize) return;
                    input.selectize.setValue('');
                    input.selectize.clear();
                    input.selectize.destroy();
                }

                var evaluateManufactureOrderAmount = function () {
                    if (!doneRendering) return
                    if (calculationType === 'based_on_quantity'){
                        var property = 'actualQuantity';
                    }else{
                        property = 'actualAmount'
                    }
                    const initialValue = 0;
                    const sum = selectedTransactionManufactureOrders.reduce(
                        (accumulator, currentValue) => accumulator + currentValue[property],
                        initialValue,
                    );
                    var sumOrders = 0;
                    $('.manufacturing_order_amount_input').each(function(index , input){
                        var selectedOrder = selectedTransactionManufactureOrders[index];
                        var orderValue = 0;
                        if (!selectedOrder) return;
                        orderValue = (((selectedOrder[property]) / sum) * transactionTotalAmount) || 0;
                        sumOrders+= orderValue;
                        $(input).val(orderValue)
                        selectedOrder.requestAmount = orderValue
                    })
                    $('.manufacturing_orders_total_amount').text(sumOrders.toFixed(2))

                }
                var accountTransactionOptions = [];
                var loadAccountTransactions = function (accountId){
                    $.ajax({
                        url: `/v2/owner/indirect-costs/search-account-transactions?ignore=&accountId=${accountId}&fromDate=${fromDate}&toDate=${toDate}`,
                        type: 'GET',
                        error: function () {
                        },
                        success: function (res) {
                            accountTransactionOptions = res
                            $('[data-select-account-transaction]').each(function (index,input){
                                removeOldTransactionOptions($(input) , accountTransactionOptions)
                                accountTransactionOptions.forEach((transactionOption)=>{
                                    if (!selectedTransactionsIds.includes(`${transactionOption.id}`)){
                                        input.selectize.addOption({
                                            text :transactionOption.text,
                                            id :transactionOption.id,
                                            amount:transactionOption.amount
                                        });
                                    }
                                })
                            })
                        }
                    });
                }
                $(document).on("change", ".manufacturing_order_amount_input", function (input){
                    var index = $(input.currentTarget).closest('tr').index()
                    selectedTransactionManufactureOrders[index].requestAmount = $(this).val();
                    setTimeout(()=>{
                        evaluateManufactureOrdersTotalAmount();
                    },300)
                });

                var filterDuplicated = function (res=[] , array) {
                    return res.filter((option)=>{
                        return !array.includes(`${option.id}`)
                    })
                }

                var disableSelectizeInput = function (inputHtml){
                    if (!$account.val()){
                        inputHtml.selectize.disable();
                    }
                }


                var resetManufacturingSelect = function (index = 0) {
                    var $manufacturingInput = $($('[data-select-manufacturing-order]').get(index));
                    resetSelectize($manufacturingInput[0]);
                    
                    $manufacturingInput.selectize({
                        valueField: 'id',
                        maxItems: 1, 
                        closeAfterSelect: true, 
                        plugins: ["auto_position"],
                        onType: function (input) {
                            url = url
                                .replace(/&q=.*/, `&q=${input}`)
                        },
                        score: function(search) {
                            var score = this.getScoreFunction(search);
                            return function(item) {
                                return 1 + score(item);
                            };
                        },
                        render: {
                            option: function (data, escape) {
                                return `<div data-actual-quantity="${data.actualQuantity}" data-actual-amount="${data.actualAmount}" data-request-amount="${data.requestAmount}" class="option">${escape(data.text)}</div>`
                            },
                            item: function (data, escape) {
                                return `<div data-actual-quantity="${data.actualQuantity}" data-actual-amount="${data.actualAmount}" data-request-amount="${data.requestAmount}" class="option">${escape(data.text)}</div>`
                            },
                        },
                        load: function (query, callback) {
                            if (!allowLoading) return callback()
                            $.ajax({
                                url: url,
                                type: 'GET',
                                error: function () {
                                    callback();
                                },
                                success: function (res) {
                                    if (!res.length) clearInputOptions($manufacturingInput)
                                    var newOptions =  filterDuplicated(res ,selectedTransactionManufactureOrdersIds);
                                    callback(newOptions);
                                }
                            });
                        },
                        onChange: function(value){
                           if(value && value !== "__blank__"){
                               if (selectedTransactionManufactureOrdersIds.includes(value)) return
                               var inputDataset = [];
                               if ($manufacturingInput[0].selectize.getOption($manufacturingInput[0].selectize.getValue()).length){
                                   inputDataset = $manufacturingInput[0].selectize.getOption($manufacturingInput[0].selectize.getValue())[0].dataset;
                               }
                               selectedTransactionManufactureOrders[index]= {
                                   actualAmount: +inputDataset.actualAmount,
                                   actualQuantity: +inputDataset.actualQuantity,
                                   orderId: value,
                               }
                               if (inputDataset.requestAmount && inputDataset.requestAmount !== "undefined"){
                                   selectedTransactionManufactureOrders[index].requestAmount =inputDataset.requestAmount
                               }
                               selectedTransactionManufactureOrdersIds.push(value)
                               evaluateManufactureOrdersTotalAmount();
                           }
                           if (value === "__blank__"){
                               selectedTransactionManufactureOrders.splice(index , 1)
                               selectedTransactionManufactureOrdersIds.splice(index , 1)
                               evaluateManufactureOrdersTotalAmount();
                               $manufacturingInput.closest('tr').find('.manufacturing_order_amount_input').val(null)
                           }
                            evaluateManufactureOrderAmount($manufacturingInput , index)
                        }
                    });

                    if (selectedTransactionManufactureOrders[index]){
                        $manufacturingInput[0].selectize.setValue(selectedTransactionManufactureOrders[index].id)
                    }else{
                        $manufacturingInput[0].selectize.setValue('');
                    }
                    $manufacturingInput[0].selectize.onSearchChange(' ');
                    $manufacturingInput.on("change", (function() {
                        var e = $(this).val();
                        if (e === "__blank__")
                            $(this)[0].selectize.setValue('');
                    }))
                }
                resetManufacturingSelect();


                var resetJournalsSelect = function (index = 0) {
                    var $accountTransactions = $($('[data-select-account-transaction]').get(index));
                    resetSelectize($accountTransactions[0])
                    $accountTransactions.selectize({
                        valueField: 'id',
                        maxItems: 1,
                        closeAfterSelect: true,
                        options: [],
                        onType: function (input) {
                            transactionsApiUrl = transactionsApiUrl
                                .replace(/&q=.*/, `&q=${input}`)
                        },
                        score: function(search) {
                            var score = this.getScoreFunction(search);
                            return function(item) {
                                return 1 + score(item);
                            };
                        },
                        render: {
                            option: function (data, escape) {
                                return `<div data-amount="${data.amount}" class="option">${escape(data.text)}</div>`
                            },
                            item: function (data, escape) {
                                return `<div data-amount="${data.amount}" class="option">${escape(data.text)}</div>`
                            },
                        },
                        load: function (query, callback) {
                            $.ajax({
                                url: transactionsApiUrl,
                                type: 'GET',
                                error: function () {
                                    callback();
                                },
                                success: function (res) {
                                    if (!res.length) clearInputOptions($accountTransactions)
                                    var newOptions =  filterDuplicated(res , selectedTransactionsIds);
                                    if (!newOptions.length) clearInputOptions($accountTransactions)
                                    callback(newOptions);
                                }
                            });
                        },
                        onChange: function(value){
                            if(value && value !== "__blank__"){
                                var inputDataset = [];
                                if ($accountTransactions[0].selectize.getOption($accountTransactions[0].selectize.getValue()).length){
                                    inputDataset = $accountTransactions[0].selectize.getOption($accountTransactions[0].selectize.getValue())[0].dataset;
                                }
                               if (inputDataset.amount){
                                   selectedTransactions[index]= {
                                       "id":value,
                                       "amount":+inputDataset.amount
                                   };
                                   $accountTransactions.closest('tr').find('.journal_transaction_amount_span').text(inputDataset.amount)
                                   evaluateTransactionsTotalAmount()
                               }
                                selectedTransactionsIds[index]=(value)
                            }
                            if (value === "__blank__"){
                                selectedTransactions.splice(index , 1)
                                selectedTransactionsIds.splice(index , 1)
                                evaluateTransactionsTotalAmount();
                                $accountTransactions.closest('tr').find('.journal_transaction_amount_span').text(0)
                            }
                            evaluateManufactureOrderAmount();
                        }
                    });
                    if (selectedTransactions[index]){
                        $accountTransactions[0].selectize.setValue(selectedTransactions[index].id)
                    }else{
                        $accountTransactions[0].selectize.setValue('');
                    }
                    $accountTransactions.on("change", (function() {
                        var val = $(this).val();
                        if (val === "__blank__")
                            $(this)[0].selectize.setValue('');
                    }))
                    if (selectedAccountId){
                        loadAccountTransactions(selectedAccountId);
                    }
                    disableSelectizeInput($accountTransactions[0])
                }
                resetJournalsSelect();

               disableSelectizeInput($('[data-select-account-transaction]')[0])

                $account.on('select2:open', function () {
                    $(".select2-search__field")[0].focus()
                })

                $account.on('change', function () {
                    selectedAccountId = $(this).val()
                    loadAccountTransactions(selectedAccountId);
                    $('[data-select-account-transaction]').each(function (index,input){
                        input.selectize.setValue('');
                        input.selectize.clear();
                        var options = Array.from(Object.keys(input.selectize.options))
                        options.forEach((opt) => {
                            if (opt !== '__blank__') {
                                input.selectize.removeOption(opt)
                            }
                        })
                        if (!selectedAccountId){
                            input.selectize.disable();
                        }else{
                            input.selectize.enable();
                        }
                    })
                    $('.journal_transaction_amount_span').text(0)
                    selectedTransactions=[];
                    evaluateTransactionsTotalAmount()
                    transactionsApiUrl = transactionsApiUrl
                        .replace(/accountId.*term/, `accountId=${selectedAccountId}&term`)
                })

                $('select').on('change', function () {
                    if ($(this).val() == '__blank__'){
                        $(this).val('')
                    }
                })

                $('input[type=radio][name=type]').change(function() {
                    calculationType = $(this).val()
                    evaluateManufactureOrderAmount()
                });

                $("[data-bulk-add-url]").on('click', function (e) {
                    var link = $(this).attr('data-bulk-add-url');
                    if (selectedTransactionManufactureOrdersIds.length){
                        link = link + '?ignore[]=' + selectedTransactionManufactureOrdersIds.join('&ignore[]=');
                    }
                    IzamModal.addUrlModal(link, '', "<?= __t('Bulk add manufacturing orders'); ?>", true);
                })

                var handleRowDeleteBtn = function ($tr,type='transaction'){
                   var $targetDeleteBtn = $tr.find('.ui-table-box-body-item-removeable-btn');
                   var $trIndex = $tr.index();
                   $targetDeleteBtn.on('click',function (){
                       if (type === 'transaction'){
                           selectedTransactions.splice($trIndex , 1)
                           evaluateTransactionsTotalAmount();
                           selectedTransactionsIds = selectedTransactions.map(t => t.id)
                       }else{
                           selectedTransactionManufactureOrders.splice($trIndex , 1)
                           evaluateManufactureOrdersTotalAmount();
                           selectedTransactionManufactureOrdersIds = selectedTransactionManufactureOrders.map(o => o.orderId)
                       }
                       setTimeout(()=>{
                           evaluateManufactureOrderAmount()
                       },300)
                   })
                }

                $(".add_manufacture_order_row_btn").on('click', function (e) {
                    var $manufactureTrs = $('.add_manufacture_order_row_js');
                    var $lastTr = $manufactureTrs.last();
                    var $lastTrIndex = $lastTr.index();
                    resetManufacturingSelect($lastTrIndex);
                    handleRowDeleteBtn($lastTr,'manufacture_order');
                })

                $(".add_journal_transaction_btn_js").on('click', function (e) {
                    var $journalTransactionRows = $('.add_journal_transaction_row_js');
                    var $lastTr = $journalTransactionRows.last();
                    var $lastTrIndex = $lastTr.index();
                    resetJournalsSelect($lastTrIndex);
                    handleRowDeleteBtn($lastTr);
                })


                if (fillForm){
                    selectedTransactionManufactureOrdersIds = selectedTransactionManufactureOrders.map(o => o.orderId)
                    selectedTransactionsIds = selectedTransactions.map(t => t.id)
                    $('.add_journal_transaction_row_js').each(function (index, row){
                        handleRowDeleteBtn($(row) , 'transaction');
                        if (index) resetJournalsSelect(index);

                    })
                    $('.add_manufacture_order_row_js').each(function (index, row){

                        handleRowDeleteBtn($(row) , 'manufacture_order');
                        if (index) resetManufacturingSelect(index);
                    })
                }else{
                    handleRowDeleteBtn($('.add_journal_transaction_row_js').last(), 'transaction');
                    handleRowDeleteBtn($('.add_manufacture_order_row_js').last(),'manufacture_order');
                }

                var setManufacturingOrderRowValues = function ($row , order) {
                    var $selectizeControl = $row.find('[data-select-manufacturing-order]')
                    var selectizeHtmlSettings = $selectizeControl[0].selectize;
                    selectizeHtmlSettings.addOption({
                        text :order.text,
                        id :order.id,
                        actualAmount:order.actualAmount,
                        actualQuantity: order.actualQuantity ,
                        requestAmount : 0
                    });
                    selectizeHtmlSettings.addItem(order.id);
                    selectizeHtmlSettings.setValue(order.id);
                    selectedTransactionManufactureOrdersIds.push(`${order.id}`)
                }

                $(document).on('addManufacturingOrders', function(event, manufacturingOrders = []) {
                   allowLoading = false;
                   manufacturingOrders.forEach((order , index)=>{
                       if(!selectedTransactionManufactureOrdersIds.includes(`${order.id}`)){
                           $(".add_manufacture_order_row_btn").click();
                           var $manufactureTrs = $('.add_manufacture_order_row_js');
                           var $lastTr = $manufactureTrs.last();
                           setManufacturingOrderRowValues($lastTr , order)
                       }
                   })
                    $('.add_manufacture_order_row_js').each(function (index , row){
                        var $lastTrSelect = $(row).find('[data-select-manufacturing-order]');
                        var $lastTrAmountInput = $(row).find('.manufacturing_order_amount_input');
                        if (!$lastTrSelect.val() && !$lastTrAmountInput.val()){
                            $(row).remove()
                        }
                    })
                    $(".loader-container").addClass("loading");

                   setTimeout(function () {
                        allowLoading = true
                    } ,400)

                   IzamModal.closeModals()
                });


                setTimeout(function () {
                    allowLoading = true
                } ,1000)

            },200)
        })

    </script>

<?php $this->endSection() ?>