<?php
    $followupStatusSettingEnabled = Settings::getValue(Izam\Daftra\Common\Utils\PluginUtil::MANUFACTURING_PLUGIN, 'production_plan_status_enabled' , null , false);
?>
<div class="d-block d-lg-none">
    <div>
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table" data-total-count="<?= $table->getData()->total()  ?>">
                    <thead>
                    <tr>
                        <?php if(!isset($_GET['iframe'])): ?>
                        <th class="listing-cell-start"></th>
                        <th width="45" class="listing-cell-end text-end">
                            <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>
                        </th>
                        <?php endif; ?>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($pagination as $i => $record):
                        $showUrl = route('owner.entity.show', ['entityKey' => \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::PRODUCTION_PLAN , 'id'=> $record->id]);
                        $row = $table->getRows()[$i];
                        $client = $row->getData()->client;
                        $imagePath = null;
                        if ($row->getData()->clientImage){
                            $imagePath = $row->getData()->clientImage;
                        }
                        ?>
                        <tr>
                            <td>
                                <a href="<?=$showUrl?>" class="listing-cell-link"></a>
                                <div class="text-nowrap">
                                    <div class="title-text mb-9">
                                        <p><?=$record->name?></p>
                                        <span class="title-subtitle-text">#<?=$record->code??$record->id?></span>
                                    </div>
                                    <?php if($record->source_type):?>
                                        <div class="title-text mb-9">
                                            <?php if($record->source_type == 'invoice'):?>
                                                <p><?=__t('Invoice')?></p>
                                                <span class="title-subtitle-text">#<?=$record->production_plan_source->no?></span>
                                            <?php elseif($record->source_type): ?>
                                                <p><?=__t('Sales Order')?></p>
                                                <span class="title-subtitle-text">#<?=$record->production_plan_source->no?></span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="date-text mb-9">
                                        <?php if($record->date_from):?>
                                            <p class="date-subtitle-text"><u><?=__t('Start')?>:</u> <strong><?=format_date($record->date_from)?></strong></p>
                                        <?php endif;?>
                                        <?php if($record->date_to):?>
                                            <p><u><?=__t('End')?>:</u> <?=format_date($record->date_to)?></p>
                                        <?php endif;?>
                                    </div>
                                    <?php if($record->client_id): ?>
                                        <div class="thumb-text-group mb-9">
                                            <a href="<?=$showUrl?>" class="listing-cell-link"></a>
                                            <div class="text-nowrap">
                                                <div class="thumb-text-group">
                                                    <?php if($imagePath):?>
                                                        <img class="thumb" loading="lazy" src="<?= $imagePath ?>" />
                                                    <?php endif; ?>
                                                    <div class="thumb-text">
                                                        <p><?=$client->business_name?></p>
                                                        <p class="thumb-subtitle-text">#<?=$client->client_number?? $client->id?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($followupStatusSettingEnabled && $record->follow_up_status_id):
                                        $selectedFollowUp = $row->getData()->selectedFollowUp; ?>
                                        <div class="date-text mb-9">
                                            <span class="badge badge-md status-badge" style='background-color:<?=$selectedFollowUp['background'] ?? 'blue'?>;color:<?=$selectedFollowUp['color'] ?? 'white'?>'><?=$selectedFollowUp['name'] ?? ''?></span>
                                        </div>
                                    <?php endif; ?>
                                    <?php foreach ($row->getCells() as $j => $cell) :?>
                                        <?php if ($cell->getMeta()->getEntityType() == 'custom_data'): ?>
                                            <div class="title-text mb-9">
                                                <p><?=$cell->getLabel()?></p>
                                                <p><?=$cell->getData()?></p>
                                            </div>
                                        <?php endif;?>
                                    <?php endforeach;?>
                                </div>
                            </td>
                            <td class="listing-cell-end text-end" data-lt-dropdown-cell="true">
                                <?= $this->includeSection('partials/row-actions-mobile', ['row' => $row]) ?>
                            </td>
                        </tr>
                    <?php endforeach;?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
