<?php 
use App\Facades\Plugins;
use App\Utils\PluginUtil;
use Izam\Rental\Utils\LeaseContractPaymentFrequencyUnitsUtil;
use Izam\Rental\Utils\LeasePeriodUnitsUtil;
use Izam\View\Form\Helper\Show\ShowCustomFields;

?>

    <div class="tab-pane-body">
        <div class="info-section">
            <div class="info-section-title">
                <span><?= sprintf(__t('%s Information'),__t('Lease Contract'))?></span>
            </div>
            <div class="info-section-body">
                <div class="row">
                   
                    <?= $this->includeSection('partials/client_info_with_avatar', [
                        'client' => $data->client
                    ]);?>
                    <div class="col-lg-6">
                        <div class="info-item">
                            <div class="info-item-value">
                                <div class="thumb-text-group">
                                    <?php 
                                      $unitType = $data->unit->unit_type;
                                      $unitTypeAttachments = $unitType->attachments;
                                      $unitTypeImage = null;
                                      if($unitTypeAttachments){
                                          $unitTypeImage = \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($unitType->name, $unitType->id, 30, $unitTypeAttachments[0]->client_attachment_file?->path);
                                      }
                                    ?>
                                    <?php if($unitTypeImage):?>
                                        <img class="thumb" loading="lazy" src="<?=$unitTypeImage?>" />
                                    <?php else: ?>    
                                        <i class="thumb thumb-empty fas fa-image"></i>
                                    <?php endif; ?>
                                    <div class="thumb-text">
                                        <p class="text-decoration-none fw-medium"><?=$data->unit->name?> (<?=$data->unit->unit_type->name?>)</p>
                                        <span class="thumb-subtitle-text">
                                            <a class="text-light-3" href="/v2/owner/rental/units/<?=$data->unit->id?>">#<?=$data->unit->id?></a>
                                            <a href="/v2/owner/rental/units/<?=$data->unit->id?>" target="_blank" class="link-light-3 text-decoration-none" data-bs-toggle="tooltip" data-bs-title="Open in New Window" title="Open in New Window">
                                                <i class="mdi mdi-open-in-new fs-8 lh-base"></i>
                                            </a>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Start Date')?>
                    </div>
                    <div class="info-item-value">
                        <?=format_date($data->start_date)?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('End Date')?>
                    </div>
                    <div class="info-item-value">
                        <?=format_date($data->end_date)?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Leasing Period')?>
                    </div>
                    <div class="info-item-value">
                        <?=$data->lease_period?> <?=__t(LeasePeriodUnitsUtil::getLeasePeriodUnitsList()[$data->lease_period_unit])?>
                    </div>
                </div>                                

            </div>

        </div>
    </div>
</div>

<div class="info-section">
    <div class="info-section-title">
        <span><?=__t('Payment Information')?></span>
    </div>
    <div class="info-section-body">
        <div class="row">

            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Payment Period')?>
                    </div>
                    <div class="info-item-value">
                        <?=$data->lease_period?> <?=__t(LeasePeriodUnitsUtil::getLeasePeriodUnitsList()[$data->lease_period_unit])?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Payment Frequency')?>
                    </div>
                    <div class="info-item-value">
                        <?=$data->payment_frequency?> <?=__t(LeaseContractPaymentFrequencyUnitsUtil::getLeaseContractPaymentFrequencyUnitsList()[$data->payment_frequency_unit])?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Payment Terms')?>
                    </div>
                    <div class="info-item-value">
                        <?=format_price($data->installment_amount, $data->currency_code)?>
                    </div>
                </div>
            </div>

                    <?php if($data->lease_tax): ?>
                        <div class="col-lg-6">
                            <div class="info-item">
                                <div class="info-item-label">
                                    <?=__t('Taxs')?>
                                </div>
                                <div class="info-item-value">
                                    <?=$data->lease_tax->name?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="col-lg-6">
                        <div class="info-item">
                            <div class="info-item-label">
                                <?=__t('Next Installment Date')?>
                            </div>
                            <div class="info-item-value">
                                <?=format_date($data->next_installment_date)?>
                                <span class="text-danger"><?=$isOverdue? '('.__t('Overdue').')':''?></span>
                            </div>
                        </div>
                    </div>
                    <?php if(Plugins::pluginActive(PluginUtil::AccountingPlugin) && $data->sales_account): ?>
                        <div class="col-lg-6">
                            <div class="info-item">
                                <div class="info-item-label">
                                    <?=__t('Sales Account')?>
                                </div>
                                <div class="info-item-value">
                                    #<?=$data->sales_account?->code?> - <?=$data->sales_account?->name?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if($data->notes || $data->tags || $data->attachments): ?>
            <div class="info-section">
                <div class="info-section-title">
                    <span><?=__t('Additional Information')?></span>
                </div>
                <div class="info-section-body">
                    <div class="row">

                        <?php if($data->notes): ?>
                            <div class="col-lg-6">
                                <div class="info-item">
                                    <div class="info-item-label">
                                        <?=__t('Notes')?>
                                    </div>
                                    <div class="info-item-value">
                                        <div class="info-item-pre"><?=$data->notes?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <?= $this->includeSection('partials/view_item_tags', [
                            'itemTags' => $data->tags
                        ]);?>
                        <?= $this->includeSection('entitiy/partials/le_preview_attachments', [
                            'attachments' => $data->attachments
                        ]);?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

<?php
$shouldShowForm = false;
foreach (($customFieldsForm?->getElements()??[]) as $element){
    $value = $element->getValue();
    $dynamicDropDownNotEmpty = $value !== ["" => ""];

    if (!empty($value) && $dynamicDropDownNotEmpty) {
        $shouldShowForm = true;
    }
}
if ($shouldShowForm && $customFieldsForm && ($customFieldsForm->getElements() || $customFieldsForm->getFieldsets())) {
    $helper = new ShowCustomFields();
    echo '<hr> '.$helper->render($customFieldsForm);
} ?>