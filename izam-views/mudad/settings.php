<?php

use Izam\View\Form\Helper\Form\FormHeader;
use Izam\View\Form\Helper\FormCollection;
use Izam\View\Form\Helper\FormRowWrapper;
$this->viewVars['title_for_layout']= __t("Mudad Settings");

$extends = 'layouts/layout';
$this->sharedVars['extraContainerClass'] = 'container-full';
$url = route('owner.mudad.store.settings');

$elementHelper = new FormCollection();
$helper = new FormHeader();
$helper->setView($this);

?>

<style>
    p.error{
        padding: 8px 12px;
    }
</style>

<?php $this->section('article-start') ?>
    <form class="m-app-form" action="<?= $url ?>" method="POST" data-app-form="units"  class="validate form-validate" >
        <input type="hidden" name="_token" value="<?= csrf_token() ?>">
        <?= $helper->render($pageHead); ?>
        <?php $this->endSection() ?>

        <div class="container container-full">
            <div class="my-10 vstack gap-10">
                <div class="card">
                    <div class="card-header">
                        <?= __t('Fields Mapping') ?>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php
                            $elementHelper = new FormRowWrapper();
                            $helper = new FormCollection();
                            $form->prepare();
                            echo $helper->render($form);
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php $this->section('article-end') ?>
    </form>
<?php $this->endSection() ?>
<?php $this->section('page-after-js') ?>
  <script src="/frontend/assets/js/pages/mudad/payroll/mudad-settings-form.js"></script>
<?php $this->endSection() ?>