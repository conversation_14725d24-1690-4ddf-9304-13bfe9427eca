<?php


/**
 * general method like
 * @sum
 * @total
 */
include_once(__DIR__ . '/Helper/helpers.php');
include_once(__DIR__ . '/Helper/currencies.php');
include_once(__DIR__ . '/Helper/translations.php');

function format_date($date, $locale = 'en_US')
{
    $toFormat = 'l, F j';
    $fromFormat = 'Y-m-d';
    // Create a DateTime object from the input date
    $dateObject = DateTime::createFromFormat($fromFormat, $date);

    if (!$dateObject) {
        return ' - ';
    }

    if ($locale == 'ar_EG') {
        $timestamp = strtotime($date);

        return arabicDate($timestamp);
    }

    return $dateObject->format($toFormat);
}

function trans($key)
{
    $translations = [
        'Regular Vacation' => 'إجازة اعتيادية',
        'Casual Leave' => 'إجازة عارضة',
        'Casual Vacation' => 'إجازة عارضة',
        'Approved' => 'موافق عليه',
        'Rejected' => 'مرفوض',
    ];

    return $translations[$key] ?? $key;
}


function arabicDate($time)
{
    $months = [
        "Jan" => "يناير", "Feb" => "فبراير", "Mar" => "مارس", "Apr" => "أبريل",
        "May" => "مايو", "Jun" => "يونيو", "Jul" => "يوليو", "Aug" => "أغسطس",
        "Sep" => "سبتمبر", "Oct" => "أكتوبر", "Nov" => "نوفمبر", "Dec" => "ديسمبر"
    ];
    $days = [
        "Sat" => "السبت", "Sun" => "الأحد", "Mon" => "الإثنين", "Tue" => "الثلاثاء",
        "Wed" => "الأربعاء", "Thu" => "الخميس", "Fri" => "الجمعة"
    ];

    $day = $days[date('D', $time)];
    $month = $months[date('M', $time)];

    $dayOfMonth = date('d', $time);

    return $day . ' ' . $dayOfMonth . ' ' . $month;
}

function format_number($amount, $lang = 'en_US', $fraction = 5)
{
    if (!in_array($lang, ['en_US', 'ar_SA'])) {
        $lang = 'en_US';
    }
    $fmt = new \NumberFormatter($lang, \NumberFormatter::DECIMAL);
    $fmt->setAttribute(\NumberFormatter::FRACTION_DIGITS, $fraction);
    $fmt->setSymbol(\NumberFormatter::DECIMAL_SEPARATOR_SYMBOL, '.');
    $fmt->setSymbol(\NumberFormatter::GROUPING_SEPARATOR_SYMBOL, ',');
    return $fmt->format($amount);
}

function format_currency($amount, $lang = 'en_US', $fraction = 5, $currencyCode = 'EGP', $numberlang = 'en_US')
{
    if (!in_array($lang, ['en_US', 'ar_SA'])) {
        $lang = 'en_US';
    }
    $currency_symbol = get_currency_symbol($currencyCode, substr($lang, 0, 2));

    $fmt = new \NumberFormatter($numberlang, \NumberFormatter::CURRENCY);
    $fmt->setAttribute(\NumberFormatter::FRACTION_DIGITS, $fraction);
    $fmt->setSymbol(\NumberFormatter::CURRENCY_SYMBOL, $currency_symbol);
    $formatted_amount = $fmt->formatCurrency($amount, $currencyCode);
    return str_replace($currency_symbol, '', $formatted_amount) . ' ' . $currency_symbol;
}

function tafket(float $amount, string $lang = 'en', string $currencyCode = 'EGP')
{
    include_once(__DIR__ . '/arabic/Tafket.php');
    $x = new \Tafket();
    return $x->convert($amount, $lang, $currencyCode);
}

