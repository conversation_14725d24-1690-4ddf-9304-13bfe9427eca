<html>
<head>
<title>Docs For Class I18N_Arabic_Salat</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Class: I18N_Arabic_Salat</h1>
Source Location: /Arabic/Salat.php<br /><br />


<table width="100%" border="0">
<tr><td valign="top">

<h3><a href="#class_details">Class Overview</a></h3>
<pre></pre><br />
<div class="description">This PHP class calculate the time of Muslim prayer according to the geographic  location.</div><br /><br />
<h4>Author(s):</h4>
<ul>
          <li>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</li>
                        </ul>




          
          

<h4>Copyright:</h4>
<ul>
  <li>2006-2016 Khaled Al-Sham'aa</li>
</ul>
        
</td>



<td valign="top">
<h3><a href="#class_methods">Methods</a></h3>
<ul>
    <li><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodcoordinate2deg">coordinate2deg</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodgetPrayTime">getPrayTime</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodgetPrayTime2">getPrayTime2</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodgetQibla">getQibla</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodsetConf">setConf</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodsetDate">setDate</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodsetLocation">setLocation</a></li>
  </ul>
</td>

</tr></table>
<hr />

<table width="100%" border="0"><tr>






</tr></table>
<hr />

<a name="class_details"></a>
<h3>Class Details</h3>
<div class="tags">
[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a170">170</a>]<br />
This PHP class calculate the time of Muslim prayer according to the geographic  location.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
</div><br /><br />
<div class="top">[ <a href="#top">Top</a> ]</div><br />


<hr />
<a name="class_methods"></a>
<h3>Class Methods</h3>
<div class="tags">

  <hr />
	<a name="methodcoordinate2deg"></a>
	<h3>method coordinate2deg <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a637">637</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>float coordinate2deg(
string
$value)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert coordinates presented in degrees, minutes and seconds  (i.e. 12°34'56&quot;S formula) into usual float number in degree unit scale  (i.e. -12.5822 value)<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Equivalent float number in degree unit scale                (i.e. -12.5822 value)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$value</b>&nbsp;&nbsp;</td>
        <td>Coordinate presented in degrees, minutes and seconds                       (i.e. 12°34'56&quot;S formula)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetPrayTime"></a>
	<h3>method getPrayTime <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a393">393</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getPrayTime(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Alias for getPrayTime2 method<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>of Salat times + sun rise in the following format                hh:mm where hh is the hour in local format and 24 mode                mm is minutes with leading zero to be 2 digits always                array items is [$Fajr, $Sunrise, $Dhuhr, $Asr, $Maghrib,                $Isha, $Sunset, $Midnight, $Imsak, array $timestamps]</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Hamid Zarrabi-Zadeh &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>source:</b>&nbsp;&nbsp;</td><td>http://praytimes.org/calculation</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetPrayTime2"></a>
	<h3>method getPrayTime2 <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a413">413</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getPrayTime2(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Calculate Salat times for the date set in setSalatDate methode, and  location set in setSalatLocation.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>of Salat times + sun rise in the following format                hh:mm where hh is the hour in local format and 24 mode                mm is minutes with leading zero to be 2 digits always                array items is [$Fajr, $Sunrise, $Dhuhr, $Asr, $Maghrib,                $Isha, $Sunset, $Midnight, $Imsak, array $timestamps]</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Hamid Zarrabi-Zadeh &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>source:</b>&nbsp;&nbsp;</td><td>http://praytimes.org/calculation</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetQibla"></a>
	<h3>method getQibla <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a601">601</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>float getQibla(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine Qibla direction using basic spherical trigonometric formula<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Qibla Direction (from the north direction) in degrees</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>S. Kamal Abdali &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>source:</b>&nbsp;&nbsp;</td><td>http://www.patriot.net/users/abdali/ftp/qibla.pdf</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetConf"></a>
	<h3>method setConf <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a352">352</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>object setConf(
[string
$sch = 'Shafi'], [decimal
$sunriseArc = -0.833333], [decimal
$ishaArc = -17.5], [decimal
$fajrArc = -19.5], [string
$view = 'Sunni'])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Setting rest of Salat calculation configuration<br /><br /><p>Convention                                 Fajr Angle  Isha Angle</p><p>Muslim World League                              -18       -17</p><p>Islamic Society of North America (ISNA)          -15       -15</p><p>Egyptian General Authority of Survey               -19.5     -17.5</p><p>Umm al-Qura University, Makkah                   -18.5  Isha 90  min after Maghrib, 120 min during Ramadan</p><p>University of Islamic Sciences, Karachi          -18       -18</p><p>Institute of Geophysics, University of Tehran      -17.7     -14(*)</p><p>Shia Ithna Ashari, Leva Research Institute, Qum  -16       -14</p><p>(*) Isha angle is not explicitly defined in Tehran method  Fajr Angle = $fajrArc, Isha Angle = $ishaArc</p><p><ul><li>حزب العلماء في لندن لدول</li></ul> أوروبا في خطوط عرض تزيد على 48</p><p>$ishaArc = -17       $fajrArc = -17</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>to build a fluent interface</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$sch</b>&nbsp;&nbsp;</td>
        <td>[Shafi|Hanafi] to define Muslims Salat                             calculation method (affect Asr time)</td>
      </tr>
          <tr>
        <td class="type">decimal&nbsp;&nbsp;</td>
        <td><b>$sunriseArc</b>&nbsp;&nbsp;</td>
        <td>Sun rise arc (default value is -0.833333)</td>
      </tr>
          <tr>
        <td class="type">decimal&nbsp;&nbsp;</td>
        <td><b>$ishaArc</b>&nbsp;&nbsp;</td>
        <td>Isha arc (default value is -18)</td>
      </tr>
          <tr>
        <td class="type">decimal&nbsp;&nbsp;</td>
        <td><b>$fajrArc</b>&nbsp;&nbsp;</td>
        <td>Fajr arc (default value is -18)</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$view</b>&nbsp;&nbsp;</td>
        <td>[Sunni|Shia] to define Muslims Salat calculation                             method (affect Maghrib and Midnight time)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetDate"></a>
	<h3>method setDate <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a263">263</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>object setDate(
[integer
$m = 8], [integer
$d = 2], [integer
$y = 1975])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Setting date of day for Salat calculation<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>to build a fluent interface</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$m</b>&nbsp;&nbsp;</td>
        <td>Month of date you want to calculate Salat in</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$d</b>&nbsp;&nbsp;</td>
        <td>Day of date you want to calculate Salat in</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Year (four digits) of date you want to calculate Salat in</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetLocation"></a>
	<h3>method setLocation <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html#a291">291</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>object setLocation(
[decimal
$l1 = 36.20278], [decimal
$l2 = 37.15861], [integer
$z = 2], [integer
$e = 0])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Setting location information for Salat calculation<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>to build a fluent interface</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">decimal&nbsp;&nbsp;</td>
        <td><b>$l1</b>&nbsp;&nbsp;</td>
        <td>Latitude of location you want to calculate Salat time in</td>
      </tr>
          <tr>
        <td class="type">decimal&nbsp;&nbsp;</td>
        <td><b>$l2</b>&nbsp;&nbsp;</td>
        <td>Longitude of location you want to calculate Salat time in</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$z</b>&nbsp;&nbsp;</td>
        <td>Time Zone, offset from UTC (see also Greenwich Mean Time)</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$e</b>&nbsp;&nbsp;</td>
        <td>Elevation, it is the observer's height in meters.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
</div><br />


        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:26:22 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>