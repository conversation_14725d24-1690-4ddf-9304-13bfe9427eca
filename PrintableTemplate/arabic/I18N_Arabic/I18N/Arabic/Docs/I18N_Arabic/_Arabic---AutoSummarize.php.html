<html>
<head>
<title>Docs for page AutoSummarize.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: AutoSummarize.php</h1>
Source Location: /Arabic/AutoSummarize.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a></dt>
	<dd>This PHP class do automatic keyphrase extraction to provide a quick  mini-summary for a long Arabic document</dd>
</div><br /><br />

<h2>Page Details:</h2>
----------------------------------------------------------------------<br /><br /><p>Copyright (c) 2006-2016 Khaled Al-Sham'aa.</p><p>http://www.ar-php.org</p><p>PHP Version 5</p><p>----------------------------------------------------------------------</p><p>LICENSE</p><p>This program is open source product; you can redistribute it and/or  modify it under the terms of the GNU Lesser General Public License (LGPL)  as published by the Free Software Foundation; either version 3  of the License, or (at your option) any later version.</p><p>This program is distributed in the hope that it will be useful,  but WITHOUT ANY WARRANTY; without even the implied warranty of  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the  GNU Lesser General Public License for more details.</p><p>You should have received a copy of the GNU Lesser General Public License  along with this program.  If not, see &lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</p><p>----------------------------------------------------------------------</p><p>Class Name: Arabic Auto Summarize Class</p><p>Filename: AutoSummarize.php</p><p>Original Author(s): Khaled Al-Sham'aa &lt;<EMAIL>&gt;</p><p>Purpose: Automatic keyphrase extraction to provide a quick mini-summary           for a long Arabic document.</p><p>----------------------------------------------------------------------</p><p>Arabic Auto Summarize</p><p>This class identifies the key points in an Arabic document for you to share with  others or quickly scan. The class determines key points by analyzing an Arabic  document and assigning a score to each sentence. Sentences that contain words  used frequently in the document are given a higher score. You can then choose a  percentage of the highest-scoring sentences to display in the summary.  &quot;ArAutoSummarize&quot; class works best on well-structured documents such as reports,  articles, and scientific papers.</p><p>&quot;ArAutoSummarize&quot; class cuts wordy copy to the bone by counting words and ranking  sentences. First, &quot;ArAutoSummarize&quot; class identifies the most common words in the  document and assigns a &quot;score&quot; to each word--the more frequently a word is used,  the higher the score.</p><p>Then, it &quot;averages&quot; each sentence by adding the scores of its words and dividing  the sum by the number of words in the sentence--the higher the average, the  higher the rank of the sentence. &quot;ArAutoSummarize&quot; class can summarize texts to  specific number of sentences or percentage of the original copy.</p><p>We use statistical approach, with some attention apparently paid to:</p><p><ul><li>Location: leading sentences of paragraph, title, introduction, and conclusion.</li><li>Fixed phrases: in-text summaries.</li><li>Frequencies of words, phrases, proper names</li><li>Contextual material: query, title, headline, initial paragraph</li></ul>  The motivation for this class is the range of applications for key phrases:</p><p><ul><li>Mini-summary: Automatic key phrase extraction can provide a quick mini-summary
   for a long document. For example, it could be a feature in a web sites; just
   click the summarize button when browsing a long web page.</li></ul> <ul><li>Highlights: It can highlight key phrases in a long document, to facilitate
   skimming the document.</li></ul> <ul><li>Author Assistance: Automatic key phrase extraction can help an author or editor
   who wants to supply a list of key phrases for a document. For example, the
   administrator of a web site might want to have a key phrase list at the top of
   each web page. The automatically extracted phrases can be a starting point for
   further manual refinement by the author or editor.</li></ul> <ul><li>Text Compression: On a device with limited display capacity or limited
   bandwidth, key phrases can be a substitute for the full text. For example, an
   email message could be reduced to a set of key phrases for display on a pager;
   a web page could be reduced for display on a portable wireless web browser.</li></ul>  This list is not intended to be exhaustive, and there may be some overlap in  the items.</p><p>Example:  <ol><li><div class="src-line">&nbsp;<span class="src-inc">include</span><span class="src-sym">(</span><span class="src-str">'./I18N/Arabic.php'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;<span class="src-var">$obj&nbsp;</span>=&nbsp;<span class="src-key">new&nbsp;</span><span class="src-id"><a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a></span><span class="src-sym">(</span><span class="src-str">'AutoSummarize'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;<span class="src-var">$file&nbsp;</span>=&nbsp;<span class="src-str">'Examples/Articles/Ajax.txt'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;<span class="src-var">$r&nbsp;</span>=&nbsp;<span class="src-num">20</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;<span class="src-comm">//&nbsp;get&nbsp;contents&nbsp;of&nbsp;a&nbsp;file&nbsp;into&nbsp;a&nbsp;string</span></div></li>
<li><div class="src-line">&nbsp;<span class="src-var">$fhandle&nbsp;</span>=&nbsp;<a href="http://www.php.net/fopen">fopen</a><span class="src-sym">(</span><span class="src-var">$file</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;r&quot;</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;<span class="src-var">$c&nbsp;</span>=&nbsp;<a href="http://www.php.net/fread">fread</a><span class="src-sym">(</span><span class="src-var">$fhandle</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/filesize">filesize</a><span class="src-sym">(</span><span class="src-var">$file</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;<a href="http://www.php.net/fclose">fclose</a><span class="src-sym">(</span><span class="src-var">$fhandle</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;<span class="src-var">$k&nbsp;</span>=&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">getMetaKeywords</span><span class="src-sym">(</span><span class="src-var">$c</span><span class="src-sym">,&nbsp;</span><span class="src-var">$r</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'&lt;b&gt;&lt;font&nbsp;color=#FFFF00&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'Keywords:&lt;/font&gt;&lt;/b&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'&lt;p&nbsp;dir=&quot;rtl&quot;&nbsp;align=&quot;justify&quot;&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-var">$k&nbsp;</span>.&nbsp;<span class="src-str">'&lt;/p&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;<span class="src-var">$s&nbsp;</span>=&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">doRateSummarize</span><span class="src-sym">(</span><span class="src-var">$c</span><span class="src-sym">,&nbsp;</span><span class="src-var">$r</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'&lt;b&gt;&lt;font&nbsp;color=#FFFF00&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'Summary:&lt;/font&gt;&lt;/b&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'&lt;p&nbsp;dir=&quot;rtl&quot;&nbsp;align=&quot;justify&quot;&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-var">$s&nbsp;</span>.&nbsp;<span class="src-str">'&lt;/p&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'&lt;b&gt;&lt;font&nbsp;color=#FFFF00&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'Full&nbsp;Text:&lt;/font&gt;&lt;/b&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'&lt;p&gt;&lt;a&nbsp;class=ar_link&nbsp;target=_blank&nbsp;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;echo&nbsp;<span class="src-str">'href='</span>.<span class="src-var">$file</span>.<span class="src-str">'&gt;Source&nbsp;File&lt;/a&gt;&lt;/p&gt;'</span><span class="src-sym">;</span></div></li>
</ol></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>filesource:</b>&nbsp;&nbsp;</td><td><a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html">Source Code for this file</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:50 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>
