<html>
<head>
<title>Docs for page KeySwap.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: KeySwap.php</h1>
Source Location: /Arabic/KeySwap.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a></dt>
	<dd>This PHP class convert keyboard language programmatically (English - Arabic)</dd>
</div><br /><br />

<h2>Page Details:</h2>
----------------------------------------------------------------------<br /><br /><p>Copyright (c) 2006-2016 Khaled Al-Sham'aa</p><p>http://www.ar-php.org</p><p>PHP Version 5</p><p>----------------------------------------------------------------------</p><p>LICENSE</p><p>This program is open source product; you can redistribute it and/or  modify it under the terms of the GNU Lesser General Public License (LGPL)  as published by the Free Software Foundation; either version 3  of the License, or (at your option) any later version.</p><p>This program is distributed in the hope that it will be useful,  but WITHOUT ANY WARRANTY; without even the implied warranty of  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the  GNU Lesser General Public License for more details.</p><p>You should have received a copy of the GNU Lesser General Public License  along with this program.  If not, see &lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</p><p>----------------------------------------------------------------------</p><p>Class Name: Arabic Keyboard Swapping Language</p><p>Filename:   KeySwap.php</p><p>Original    Author(s): Khaled Al-Sham'aa &lt;<EMAIL>&gt;</p><p>Purpose:    Convert keyboard language programmatically (English - Arabic)</p><p>----------------------------------------------------------------------</p><p>Arabic Keyboard Swapping Language</p><p>PHP class to convert keyboard language between English and Arabic  programmatically. This function can be helpful in dual language forms when  users miss change keyboard language while they are entering data.</p><p>If you wrote an Arabic sentence while your keyboard stays in English mode by  mistake, you will get a non-sense English text on your PC screen. In that case  you can use this class to make a kind of magic conversion to swap that odd text  by original Arabic sentence you meant when you type on your keyboard.</p><p>Please note that magic conversion in the opposite direction (if you type English  sentences while your keyboard stays in Arabic mode) is also available in this  class, but it is not reliable as much as previous case because in Arabic keyboard  we have some keys provide a short-cut to type two chars in one click (those keys  include: b, B, G and T).</p><p>Well, we try in this class to come over this issue by suppose that user used  optimum way by using short-cut keys when available instead of assemble chars  using stand alone keys, but if (s)he does not then you may have some typo chars  in converted text.</p><p>Example:  <ol><li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-inc">include</span><span class="src-sym">(</span><span class="src-str">'./I18N/Arabic.php'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj&nbsp;</span>=&nbsp;<span class="src-key">new&nbsp;</span><span class="src-id"><a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a></span><span class="src-sym">(</span><span class="src-str">'KeySwap'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<span class="src-str">&quot;Hpf&nbsp;lk&nbsp;hgkhs&nbsp;hglj'vtdkK&nbsp;Hpf&nbsp;hg`dk&nbsp;dldg,k&nbsp;f;gdjil&nbsp;Ygn&nbsp;,p]hkdm&nbsp;...&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;p&gt;&lt;u&gt;&lt;i&gt;Before:&lt;/i&gt;&lt;/u&gt;&lt;br&nbsp;/&gt;<span class="src-var">$str</span>&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$text&nbsp;</span>=&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">swapEa</span><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;u&gt;&lt;i&gt;After:&lt;/i&gt;&lt;/u&gt;&lt;br&nbsp;/&gt;<span class="src-var">$text</span>&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
</ol></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>filesource:</b>&nbsp;&nbsp;</td><td><a href="../__filesource/fsource_I18N_Arabic__ArabicKeySwap.php.html">Source Code for this file</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:26:05 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>
