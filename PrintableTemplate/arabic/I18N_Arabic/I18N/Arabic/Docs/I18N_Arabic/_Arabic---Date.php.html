<html>
<head>
<title>Docs for page Date.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: Date.php</h1>
Source Location: /Arabic/Date.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a></dt>
	<dd>This PHP class is an Arabic customization for PHP date function</dd>
</div><br /><br />

<h2>Page Details:</h2>
----------------------------------------------------------------------<br /><br /><p>Copyright (c) 2006-2016 Khaled Al-Sham'aa.</p><p>http://www.ar-php.org</p><p>PHP Version 5</p><p>----------------------------------------------------------------------</p><p>LICENSE</p><p>This program is open source product; you can redistribute it and/or  modify it under the terms of the GNU Lesser General Public License (LGPL)  as published by the Free Software Foundation; either version 3  of the License, or (at your option) any later version.</p><p>This program is distributed in the hope that it will be useful,  but WITHOUT ANY WARRANTY; without even the implied warranty of  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the  GNU Lesser General Public License for more details.</p><p>You should have received a copy of the GNU Lesser General Public License  along with this program.  If not, see &lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</p><p>----------------------------------------------------------------------</p><p>Class Name: Arabic Date</p><p>Filename:   Date.php</p><p>Original    Author(s): Khaled Al-Sham'aa &lt;<EMAIL>&gt;</p><p>Purpose:    Arabic customization for PHP date function</p><p>----------------------------------------------------------------------</p><p>Arabic Date</p><p>PHP class for Arabic and Islamic customization of PHP date function. It  can convert UNIX timestamp into string in Arabic as well as convert it into  Hijri calendar</p><p>The Islamic Calendar:</p><p>The Islamic calendar is purely lunar and consists of twelve alternating months  of 30 and 29 days, with the final 29 day month extended to 30 days during leap  years. Leap years follow a 30 year cycle and occur in years 1, 5, 7, 10, 13, 16,  18, 21, 24, 26, and 29. The calendar begins on Friday, July 16th, 622 C.E. in  the Julian calendar, Julian day 1948439.5, the day of Muhammad's separate from  Mecca to Medina, the first day of the first month of year 1 A.H.--&quot;Anno Hegira&quot;.</p><p>Each cycle of 30 years thus contains 19 normal years of 354 days and 11 leap  years of 355, so the average length of a year is therefore  ((19 x 354) + (11 x 355)) / 30 = 354.365... days, with a mean length of month of  1/12 this figure, or 29.53055... days, which closely approximates the mean  synodic month (time from new Moon to next new Moon) of 29.530588 days, with the  calendar only slipping one day with respect to the Moon every 2525 years. Since  the calendar is fixed to the Moon, not the solar year, the months shift with  respect to the seasons, with each month beginning about 11 days earlier in each  successive solar year.</p><p>The convert presented here is the most commonly used civil calendar in the  Islamic world; for religious purposes months are defined to start with the  first observation of the crescent of the new Moon.</p><p>The Julian Calendar:</p><p>The Julian calendar was proclaimed by Julius Casar in 46 B.C. and underwent  several modifications before reaching its final form in 8 C.E. The Julian  calendar differs from the Gregorian only in the determination of leap years,  lacking the correction for years divisible by 100 and 400 in the Gregorian  calendar. In the Julian calendar, any positive year is a leap year if divisible  by 4. (Negative years are leap years if when divided by 4 a remainder of 3  results.) Days are considered to begin at midnight.</p><p>In the Julian calendar the average year has a length of 365.25 days. compared to  the actual solar tropical year of 365.24219878 days. The calendar thus  accumulates one day of error with respect to the solar year every 128 years.  Being a purely solar calendar, no attempt is made to synchronise the start of  months to the phases of the Moon.</p><p>The Gregorian Calendar:</p><p>The Gregorian calendar was proclaimed by Pope Gregory XIII and took effect in  most Catholic states in 1582, in which October 4, 1582 of the Julian calendar  was followed by October 15 in the new calendar, correcting for the accumulated  discrepancy between the Julian calendar and the equinox as of that date. When  comparing historical dates, it's important to note that the Gregorian calendar,  used universally today in Western countries and in international commerce, was  adopted at different times by different countries. Britain and her colonies  (including what is now the United States), did not switch to the Gregorian  calendar until 1752, when Wednesday 2nd September in the Julian calendar dawned  as Thursday the 14th in the Gregorian.</p><p>The Gregorian calendar is a minor correction to the Julian. In the Julian  calendar every fourth year is a leap year in which February has 29, not 28 days,  but in the Gregorian, years divisible by 100 are not leap years unless they are  also divisible by 400. How prescient was Pope Gregory! Whatever the problems of  Y2K, they won't include sloppy programming which assumes every year divisible by  4 is a leap year since 2000, unlike the previous and subsequent years divisible  by 100, is a leap year. As in the Julian calendar, days are considered to begin  at midnight.</p><p>The average length of a year in the Gregorian calendar is 365.2425 days compared  to the actual solar tropical year (time from equinox to equinox) of 365.24219878  days, so the calendar accumulates one day of error with respect to the solar year  about every 3300 years. As a purely solar calendar, no attempt is made to  synchronise the start of months to the phases of the Moon.</p><p>date -- Format a local time/date  string date ( string format, int timestamp);</p><p>Returns a string formatted according to the given format string using the given  integer timestamp or the current local time if no timestamp is given. In  otherwords, timestamp is optional and defaults to the value of time().</p><p>Example:  <ol><li><div class="src-line">&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/date_default_timezone_set">date_default_timezone_set</a><span class="src-sym">(</span><span class="src-str">'UTC'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;<span class="src-var">$time&nbsp;</span>=&nbsp;<a href="http://www.php.net/time">time</a><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;<span class="src-inc">include</span><span class="src-sym">(</span><span class="src-str">'./I18N/Arabic.php'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;<span class="src-var">$obj&nbsp;</span>=&nbsp;<span class="src-key">new&nbsp;</span><span class="src-id"><a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a></span><span class="src-sym">(</span><span class="src-str">'Date'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setMode</span><span class="src-sym">(</span><span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setMode</span><span class="src-sym">(</span><span class="src-num">3</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setMode</span><span class="src-sym">(</span><span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
</ol></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>filesource:</b>&nbsp;&nbsp;</td><td><a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html">Source Code for this file</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:56 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>
