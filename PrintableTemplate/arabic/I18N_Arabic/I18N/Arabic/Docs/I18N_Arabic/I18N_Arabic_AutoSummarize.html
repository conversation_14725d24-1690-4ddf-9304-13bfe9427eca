<html>
<head>
<title>Docs For Class I18N_Arabic_AutoSummarize</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Class: I18N_Arabic_AutoSummarize</h1>
Source Location: /Arabic/AutoSummarize.php<br /><br />


<table width="100%" border="0">
<tr><td valign="top">

<h3><a href="#class_details">Class Overview</a></h3>
<pre></pre><br />
<div class="description">This PHP class do automatic keyphrase extraction to provide a quick  mini-summary for a long Arabic document</div><br /><br />
<h4>Author(s):</h4>
<ul>
          <li>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</li>
                        </ul>




          
          

<h4>Copyright:</h4>
<ul>
  <li>2006-2016 Khaled Al-Sham'aa</li>
</ul>
        
</td>



<td valign="top">
<h3><a href="#class_methods">Methods</a></h3>
<ul>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodacceptedWord">acceptedWord</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodcleanCommon">cleanCommon</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddoNormalize">doNormalize</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddoRateSummarize">doRateSummarize</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddoSummarize">doSummarize</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddraftStem">draftStem</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodgetMetaKeywords">getMetaKeywords</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodhighlightRateSummary">highlightRateSummary</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodhighlightSummary">highlightSummary</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodloadExtra">loadExtra</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodminAcceptedRank">minAcceptedRank</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodrankSentences">rankSentences</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodrankWords">rankWords</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodsummarize">summarize</a></li>
  </ul>
</td>

</tr></table>
<hr />

<table width="100%" border="0"><tr>






</tr></table>
<hr />

<a name="class_details"></a>
<h3>Class Details</h3>
<div class="tags">
[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a143">143</a>]<br />
This PHP class do automatic keyphrase extraction to provide a quick  mini-summary for a long Arabic document<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
</div><br /><br />
<div class="top">[ <a href="#top">Top</a> ]</div><br />


<hr />
<a name="class_methods"></a>
<h3>Class Methods</h3>
<div class="tags">

  <hr />
	<a name="methodacceptedWord"></a>
	<h3>method acceptedWord <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a668">668</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean acceptedWord(
string
$word)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Check some conditions to know if a given string is a formal valid word or not<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>True if passed string is accepted as a valid word else                  it will return False</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$word</b>&nbsp;&nbsp;</td>
        <td>String to be checked if it is a valid word or not</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcleanCommon"></a>
	<h3>method cleanCommon <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a470">470</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string cleanCommon(
string
$str)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Extracting common Arabic words (roughly)  from input Arabic string (document content)<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Arabic document as a string free of common words (roughly)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input normalized Arabic document as a string</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddoNormalize"></a>
	<h3>method doNormalize <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a448">448</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string doNormalize(
string
$str)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Normalized Arabic document<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Normalized Arabic document</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddoRateSummarize"></a>
	<h3>method doRateSummarize <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a317">317</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string doRateSummarize(
string
$str, integer
$rate, string
$keywords)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Summarize percentage of the input Arabic string (document content) into output<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Output summary requested</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$rate</b>&nbsp;&nbsp;</td>
        <td>Rate of output summary sentence number as                           percentage of the input Arabic string                           (document content)</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$keywords</b>&nbsp;&nbsp;</td>
        <td>List of keywords higlited by search process</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddoSummarize"></a>
	<h3>method doSummarize <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a296">296</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string doSummarize(
string
$str, integer
$int, string
$keywords)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Summarize input Arabic string (document content) into specific number of  sentences in the output<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Output summary requested</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$int</b>&nbsp;&nbsp;</td>
        <td>Number of sentences required in output summary</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$keywords</b>&nbsp;&nbsp;</td>
        <td>List of keywords higlited by search process</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddraftStem"></a>
	<h3>method draftStem <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a487">487</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string draftStem(
string
$str)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Remove less significant Arabic letter from given string (document content).<br /><br /><p>Please note that output will not be human readable.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Output string after removing less significant Arabic letter                 (not human readable output)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetMetaKeywords"></a>
	<h3>method getMetaKeywords <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a383">383</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getMetaKeywords(
string
$str, integer
$int)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Extract keywords from a given Arabic string (document content)<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>List of the keywords extracting from input Arabic string                (document content)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$int</b>&nbsp;&nbsp;</td>
        <td>Number of keywords required to be extracting                      from input string (document content)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodhighlightRateSummary"></a>
	<h3>method highlightRateSummary <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a363">363</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string highlightRateSummary(
string
$str, integer
$rate, string
$keywords, string
$style)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Highlight key sentences (summary) as percentage of the input string  (document content) using CSS and send the result back as an output.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Output highlighted key sentences summary (using CSS)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$rate</b>&nbsp;&nbsp;</td>
        <td>Rate of highlighted key sentences summary                           number as percentage of the input Arabic                           string (document content)</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$keywords</b>&nbsp;&nbsp;</td>
        <td>List of keywords higlited by search process</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$style</b>&nbsp;&nbsp;</td>
        <td>Name of the CSS class you would like to apply</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodhighlightSummary"></a>
	<h3>method highlightSummary <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a340">340</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string highlightSummary(
string
$str, integer
$int, string
$keywords, string
$style)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Highlight key sentences (summary) of the input string (document content)  using CSS and send the result back as an output<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Output highlighted key sentences summary (using CSS)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$int</b>&nbsp;&nbsp;</td>
        <td>Number of key sentences required to be                           highlighted in the input string                           (document content)</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$keywords</b>&nbsp;&nbsp;</td>
        <td>List of keywords higlited by search process</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$style</b>&nbsp;&nbsp;</td>
        <td>Name of the CSS class you would like to apply</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodloadExtra"></a>
	<h3>method loadExtra <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a184">184</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void loadExtra(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Load enhanced Arabic stop words list<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodminAcceptedRank"></a>
	<h3>method minAcceptedRank <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a627">627</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>integer minAcceptedRank(
array
$str, array
$arr, integer
$int, integer
$max)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Calculate minimum rank for sentences which will be including in the summary<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Minimum accepted sentence rank (sentences with rank more                  than this will be listed in the document summary)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Document sentences</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$arr</b>&nbsp;&nbsp;</td>
        <td>Sentences ranks</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$int</b>&nbsp;&nbsp;</td>
        <td>Number of sentences you need to include in your summary</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$max</b>&nbsp;&nbsp;</td>
        <td>Maximum number of characters accepted in your summary</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrankSentences"></a>
	<h3>method rankSentences <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a546">546</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array rankSentences(
array
$sentences, array
$stemmedSentences, array
$arr)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Ranks sentences in a given Arabic string (document content).<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Two dimension array, first item is an array of document                sentences, second item is an array of ranks of document                sentences.</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$sentences</b>&nbsp;&nbsp;</td>
        <td>Sentences of the input Arabic document                                 as an array</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$stemmedSentences</b>&nbsp;&nbsp;</td>
        <td>Stemmed sentences of the input Arabic                                 document as an array</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$arr</b>&nbsp;&nbsp;</td>
        <td>Words ranks array (word as an index and                                 value refer to the word frequency)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrankWords"></a>
	<h3>method rankWords <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a503">503</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>hash rankWords(
string
$str)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Ranks words in a given Arabic string (document content). That rank refers  to the frequency of that word appears in that given document.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Associated array where document words referred by index and               those words ranks referred by values of those array items.</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsummarize"></a>
	<h3>method summarize <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicAutoSummarize.php.html#a205">205</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string summarize(
string
$str, string
$keywords, integer
$int, string
$mode, string
$output, [string
$style = null])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Core summarize function that implement required steps in the algorithm<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Output summary requested</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Input Arabic document as a string</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$keywords</b>&nbsp;&nbsp;</td>
        <td>List of keywords higlited by search process</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$int</b>&nbsp;&nbsp;</td>
        <td>Sentences value (see $mode effect also)</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>Mode of sentences count [number|rate]</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$output</b>&nbsp;&nbsp;</td>
        <td>Output mode [summary|highlight]</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$style</b>&nbsp;&nbsp;</td>
        <td>Name of the CSS class you would like to apply</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
</div><br />


        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:52 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>