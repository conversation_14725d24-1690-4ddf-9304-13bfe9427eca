<html>
<head>
<title>Docs For Class I18N_Arabic</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Class: I18N_Arabic</h1>
Source Location: /Arabic.php<br /><br />


<table width="100%" border="0">
<tr><td valign="top">

<h3><a href="#class_details">Class Overview</a></h3>
<pre></pre><br />
<div class="description">Core PHP and Arabic language class</div><br /><br />
<h4>Author(s):</h4>
<ul>
          <li>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</li>
                        </ul>




          
          

<h4>Copyright:</h4>
<ul>
  <li>2006-2016 Khaled Al-Shamaa</li>
</ul>
        
</td>



<td valign="top">
<h3><a href="#class_methods">Methods</a></h3>
<ul>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#method__construct">__construct</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#method__destruct">__destruct</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodautoload">autoload</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodgetBrowserLang">getBrowserLang</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodgetClassFile">getClassFile</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodgetInputCharset">getInputCharset</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodgetOutputCharset">getOutputCharset</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodheader">header</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodisForum">isForum</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodload">load</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodmyErrorHandler">myErrorHandler</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodsetInputCharset">setInputCharset</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#methodsetOutputCharset">setOutputCharset</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic.html#method__call">__call</a></li>
  </ul>
</td>

</tr></table>
<hr />

<table width="100%" border="0"><tr>






</tr></table>
<hr />

<a name="class_details"></a>
<h3>Class Details</h3>
<div class="tags">
[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a71">71</a>]<br />
Core PHP and Arabic language class<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Shamaa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
</div><br /><br />
<div class="top">[ <a href="#top">Top</a> ]</div><br />


<hr />
<a name="class_methods"></a>
<h3>Class Methods</h3>
<div class="tags">
  <hr />
	<a name="methodautoload"></a>
	<h3>static method autoload <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a168">168</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>static null autoload(
string
$className)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Include file that include requested class<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$className</b>&nbsp;&nbsp;</td>
        <td>Class name</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetBrowserLang"></a>
	<h3>static method getBrowserLang <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a489">489</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>static string getBrowserLang(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get web browser chosen/default language using ISO 639-1 codes (2-letter)<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Language using ISO 639-1 codes (2-letter)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetClassFile"></a>
	<h3>static method getClassFile <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a412">412</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>static string getClassFile(
string
$class)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get sub class file path to be included (mapping between class name and  file name/path become independent now)<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Sub class file path</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$class</b>&nbsp;&nbsp;</td>
        <td>Sub class name</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodisForum"></a>
	<h3>static method isForum <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a506">506</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>static boolean isForum(
string
$html)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		There is still a lack of original, localized, high-quality content and  well-structured Arabic websites; This method help in tag HTML result pages  from Arabic forum to enable filter it in/out.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>True if the input HTML is belong to a forum page</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$html</b>&nbsp;&nbsp;</td>
        <td>The HTML content of the page in question</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmyErrorHandler"></a>
	<h3>static method myErrorHandler <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a184">184</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>static boolean myErrorHandler(
int
$errno, string
$errstr, string
$errfile, int
$errline)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Error handler function<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>FALSE</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$errno</b>&nbsp;&nbsp;</td>
        <td>The level of the error raised</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$errstr</b>&nbsp;&nbsp;</td>
        <td>The error message</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$errfile</b>&nbsp;&nbsp;</td>
        <td>The filename that the error was raised in</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$errline</b>&nbsp;&nbsp;</td>
        <td>The line number the error was raised at</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>

  <hr />
	<a name="method__construct"></a>
	<h3>constructor __construct <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a111">111</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>I18N_Arabic __construct(
string
$library, [boolean
$useAutoload = false], [boolean
$useException = false], [boolean
$compatibleMode = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Load selected library/class you would like to use its functionality<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$library</b>&nbsp;&nbsp;</td>
        <td>[AutoSummarize|CharsetC|CharsetD|Date|Gender|                                 Glyphs|Identifier|KeySwap|Numbers|Query|Salat|                                 Soundex|StrToTime|WordTag|CompressStr|Mktime|                                 Transliteration|Stemmer|Standard|Normalise]</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$useAutoload</b>&nbsp;&nbsp;</td>
        <td>True to use Autoload (default is false)</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$useException</b>&nbsp;&nbsp;</td>
        <td>True to use Exception (default is false)</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$compatibleMode</b>&nbsp;&nbsp;</td>
        <td>True to support old naming style before                                 version 3.0 (default is true)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="method__destruct"></a>
	<h3>destructor __destruct <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a307">307</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void __destruct(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Garbage collection, release child objects directly<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetInputCharset"></a>
	<h3>method getInputCharset <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a375">375</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getInputCharset(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the charset used in the input Arabic strings<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>return current setting for class input Arabic charset</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetOutputCharset"></a>
	<h3>method getOutputCharset <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a392">392</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getOutputCharset(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the charset used in the output Arabic strings<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>return current setting for class output Arabic charset</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodheader"></a>
	<h3>method header <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a430">430</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string header(
[string
$mode = 'http'], [resource
$conn = null])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Send/set output charset in several output media in a proper way<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>header formula if there is any (in cases of html,                 text_email, and html_email)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>[http|html|mysql|mysqli|pdo|text_email|html_email]</td>
      </tr>
          <tr>
        <td class="type">resource&nbsp;&nbsp;</td>
        <td><b>$conn</b>&nbsp;&nbsp;</td>
        <td>The MySQL connection handler/the link identifier</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodload"></a>
	<h3>method load <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a212">212</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>null load(
string
$library)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Load selected Arabic library and create an instance of its class<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$library</b>&nbsp;&nbsp;</td>
        <td>Library name</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetInputCharset"></a>
	<h3>method setInputCharset <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a323">323</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>TRUE setInputCharset(
string
$charset)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set charset used in class input Arabic strings<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>if success, or FALSE if fail</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$charset</b>&nbsp;&nbsp;</td>
        <td>Input charset [utf-8|windows-1256|iso-8859-6]</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetOutputCharset"></a>
	<h3>method setOutputCharset <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a350">350</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean setOutputCharset(
string
$charset)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set charset used in class output Arabic strings<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>TRUE if success, or FALSE if fail</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$charset</b>&nbsp;&nbsp;</td>
        <td>Output charset [utf-8|windows-1256|iso-8859-6]</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="method__call"></a>
	<h3>method __call <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__Arabic.php.html#a247">247</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>The __call(
string
$methodName, array
$arguments)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Magic method __call() allows to capture invocation of non existing methods.<br /><br /><p>That way __call() can be used to implement user defined method handling that  depends on the name of the actual method being called.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value returned from the __call() method will be returned to          the caller of the method.</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Shamaa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$methodName</b>&nbsp;&nbsp;</td>
        <td>Method name</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$arguments</b>&nbsp;&nbsp;</td>
        <td>Array of arguments</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
</div><br />


        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:49 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>