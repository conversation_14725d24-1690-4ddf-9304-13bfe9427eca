<html>
<head>
<title>Docs For Class I18N_Arabic_Date</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Class: I18N_Arabic_Date</h1>
Source Location: /Arabic/Date.php<br /><br />


<table width="100%" border="0">
<tr><td valign="top">

<h3><a href="#class_details">Class Overview</a></h3>
<pre></pre><br />
<div class="description">This PHP class is an Arabic customization for PHP date function</div><br /><br />
<h4>Author(s):</h4>
<ul>
          <li>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</li>
                        </ul>




          
          

<h4>Copyright:</h4>
<ul>
  <li>2006-2016 Khaled Al-Sham'aa</li>
</ul>
        
</td>



<td valign="top">
<h3><a href="#class_methods">Methods</a></h3>
<ul>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodarabicMonths">arabicMonths</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddateCorrection">dateCorrection</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoden2ar">en2ar</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodgetMode">getMode</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodgregToJd">gregToJd</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodhjConvert">hjConvert</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodislamicToJd">islamicToJd</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodjdToIslamic">jdToIslamic</a></li>
    <li><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodsetMode">setMode</a></li>
  </ul>
</td>

</tr></table>
<hr />

<table width="100%" border="0"><tr>






</tr></table>
<hr />

<a name="class_details"></a>
<h3>Class Details</h3>
<div class="tags">
[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a166">166</a>]<br />
This PHP class is an Arabic customization for PHP date function<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
</div><br /><br />
<div class="top">[ <a href="#top">Top</a> ]</div><br />


<hr />
<a name="class_methods"></a>
<h3>Class Methods</h3>
<div class="tags">

  <hr />
	<a name="methodarabicMonths"></a>
	<h3>method arabicMonths <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a414">414</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array arabicMonths(
integer
$mode)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Add Arabic month names to the replacement array<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Arabic month names in selected style</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>Naming mode of months in Arabic where:                        2) Arabic month names used in Middle East countries                        3) Arabic Transliteration of Gregorian month names                        4) Both of 2 and 3 formats together                        5) Libya style                        6) Algeria and Tunis style                        7) Morocco style</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddate"></a>
	<h3>method date <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a240">240</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string date(
string
$format, integer
$timestamp, [integer
$correction = 0])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Format a local time/date in Arabic string<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Format Arabic date string according to given format string                 using the given integer timestamp or the current local                 time if no timestamp is given.</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$format</b>&nbsp;&nbsp;</td>
        <td>Format string (same as PHP date function)</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$timestamp</b>&nbsp;&nbsp;</td>
        <td>Unix timestamp</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$correction</b>&nbsp;&nbsp;</td>
        <td>To apply correction factor (+/- 1-2) to                             standard hijri calendar</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddateCorrection"></a>
	<h3>method dateCorrection <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a534">534</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>integer dateCorrection(
integer
$time)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Calculate Hijri calendar correction using Um-Al-Qura calendar information<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Correction factor to fix Hijri calendar calculation using                  Um-Al-Qura calendar information</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$time</b>&nbsp;&nbsp;</td>
        <td>Unix timestamp</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoden2ar"></a>
	<h3>method en2ar <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a345">345</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string en2ar(
string
$str)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Translate English date/time terms into Arabic langauge<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Date/time string using Arabic terms</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>Date/time string using English terms</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetMode"></a>
	<h3>method getMode <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a222">222</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>Integer getMode(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Getting $mode value that refer to output mode format<br /><br /><p>1) Hijri format (Islamic calendar)                2) Arabic month names used in Middle East countries                3) Arabic Transliteration of Gregorian month names                4) Both of 2 and 3 formats together                5) Libyan way                6) Algeria and Tunis style                7) Morocco style                8) Hijri format (Islamic calendar) in English</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Value of $mode properity</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgregToJd"></a>
	<h3>method gregToJd <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a502">502</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>integer gregToJd(
integer
$m, integer
$d, integer
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Converts a Gregorian date to Julian Day Count<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The julian day for the given gregorian date as an integer</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$m</b>&nbsp;&nbsp;</td>
        <td>The month as a number from 1 (for January)                    to 12 (for December)</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$d</b>&nbsp;&nbsp;</td>
        <td>The day as a number from 1 to 31</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>The year as a number between -4714 and 9999</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodhjConvert"></a>
	<h3>method hjConvert <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a437">437</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array hjConvert(
integer
$Y, integer
$M, integer
$D)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert given Gregorian date into Hijri date<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Hijri date [int Year, int Month, int Day](Islamic calendar)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$Y</b>&nbsp;&nbsp;</td>
        <td>Year Gregorian year</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$M</b>&nbsp;&nbsp;</td>
        <td>Month Gregorian month</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$D</b>&nbsp;&nbsp;</td>
        <td>Day Gregorian day</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodislamicToJd"></a>
	<h3>method islamicToJd <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a484">484</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>integer islamicToJd(
integer
$year, integer
$month, integer
$day)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert given Hijri date into Julian day<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Julian day</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$year</b>&nbsp;&nbsp;</td>
        <td>Year Hijri year</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$month</b>&nbsp;&nbsp;</td>
        <td>Month Hijri month</td>
      </tr>
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$day</b>&nbsp;&nbsp;</td>
        <td>Day Hijri day</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodjdToIslamic"></a>
	<h3>method jdToIslamic <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a458">458</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array jdToIslamic(
integer
$jd)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert given Julian day into Hijri date<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Hijri date [int Year, int Month, int Day](Islamic calendar)</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$jd</b>&nbsp;&nbsp;</td>
        <td>Julian day</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetMode"></a>
	<h3>method setMode <span class="smalllinenumber">[line <a href="../__filesource/fsource_I18N_Arabic__ArabicDate.php.html#a197">197</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>object setMode(
[integer
$mode = 1])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Setting value for $mode scalar<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>to build a fluent interface</td>
  </tr>
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">integer&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>Output mode of date function where:                        1) Hijri format (Islamic calendar)                        2) Arabic month names used in Middle East countries                        3) Arabic Transliteration of Gregorian month names                        4) Both of 2 and 3 formats together                        5) Libya style                        6) Algeria and Tunis style                        7) Morocco style                        8) Hijri format (Islamic calendar) in English</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
</div><br />


        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:58 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>