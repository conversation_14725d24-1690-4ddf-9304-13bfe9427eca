.php {
	padding: 1em;
}
.php-src { font-family: 'Courier New', Courier, monospace; font-weight: normal; }

body
{
  color:              #000000;
  background-color:   #ffffff;
  background-image:   url("background.png");
  background-repeat:  repeat-y;
  font-family:        tahoma, verdana, arial, sans-serif;
  font-size:          10pt;
  margin:             0;
  padding:            0;
}

a
{
  color:              #000099;
  background-color:   transparent;
  text-decoration:    none;
}

a:hover
{
  text-decoration:    underline;
}

a.menu
{
  color:              #ffffff;
  background-color:   transparent;
}

td
{
  font-size:          10pt;
}

td.header_top
{
  color:              #ffffff;
  background-color:   #9999cc;
  font-size:          16pt;
  font-weight:        bold;
  text-align:         right;
  padding:            10px;
}

td.header_line
{
  color:              #ffffff;
  background-color:   #333366;
}

td.header_menu
{
  color:              #ffffff;
  background-color:   #666699;
  font-size:          8pt;
  text-align:         right;
  padding:            2px;
  padding-right:      5px;
}

td.menu
{
  padding:            2px;
  padding-left:       5px;
}

td.code_border
{
  color:              #000000;
  background-color:   #c0c0c0;
}

td.code
{
  color:              #000000;
  background-color:   #f0f0f0;
}

td.type
{
  font-style:         italic;
}

div.credit
{
  font-size:          8pt;
  text-align:         center;
}

div.package
{
  padding-left:       5px;
}

div.tags
{
  padding-left:       15px;
}

div.function
{
  padding-left:       15px;
}

div.top
{
  font-size:          8pt;
}

div.warning
{
  color:              #ff0000;
  background-color:   transparent;
}

div.description
{
  padding-left:       15px;
}

hr
{
  height:             1px;
  border-style:       solid;
  border-color:       #c0c0c0;
  margin-top:         10px;
  margin-bottom:      10px;
}

span.smalllinenumber
{
  font-size:          8pt;
}

ul {
	margin-left:		0px;
	padding-left:		8px;
}
/* Syntax highlighting */

.src-code { background-color: #f5f5f5; border: 1px solid #ccc9a4; padding: 0 0 0 1em; margin : 0px;
            font-family: 'Courier New', Courier, monospace; font-weight: normal; }
.src-line {  font-family: 'Courier New', Courier, monospace; font-weight: normal; color: #000088; }

.src-comm { color: green; }
.src-id { color: #0000BB; }
.src-inc { color: #000088; }
.src-key { color: #000088; }
.src-num { color: #CC0000; }
.src-str { color: #BB0000; }
.src-sym { font-weight: bold; }
.src-var { color: #008800; }

.src-php { font-weight: bold; }

.src-doc { color: #009999 }
.src-doc-close-template { color: #0000FF }
.src-doc-coretag { color: #0099FF; font-weight: bold }
.src-doc-inlinetag { color: #0099FF }
.src-doc-internal { color: #6699cc }
.src-doc-tag { color: #0080CC }
.src-doc-template { color: #0000FF }
.src-doc-type { font-style: italic }
.src-doc-var { font-style: italic }

.tute-tag { color: #009999 }
.tute-attribute-name { color: #0000FF }
.tute-attribute-value { color: #0099FF }
.tute-entity { font-weight: bold; }
.tute-comment { font-style: italic }
.tute-inline-tag { color: #636311; font-weight: bold }

/* tutorial */

.authors {  }
.author { font-style: italic; font-weight: bold }
.author-blurb { margin: .5em 0em .5em 2em; font-size: 85%; font-weight: normal; font-style: normal }
.example { border: 1px dashed #999999; background-color: #EEEEEE; padding: .5em; }
.listing { border: 1px dashed #999999; background-color: #EEEEEE; padding: .5em; white-space: nowrap; }
.release-info { font-size: 85%; font-style: italic; margin: 1em 0em }
.ref-title-box {  }
.ref-title {  }
.ref-purpose { font-style: italic; color: #666666 }
.ref-synopsis {  }
.title { font-weight: bold; margin: 1em 0em 0em 0em; padding: .25em; border: 2px solid #999999; background-color: #9999CC  }
.cmd-synopsis { margin: 1em 0em }
.cmd-title { font-weight: bold }
.toc { margin-left: 2em; padding-left: 0em }

