<html>
<head>
<title>File Source for CompressStr.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                  
      
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1 align="center">Source for file CompressStr.php</h1>
<p>Documentation is available at <a href="../I18N_Arabic/_Arabic---CompressStr.php.html">CompressStr.php</a></p>
<div class="src-code">
<ol><li><div class="src-line"><a name="a1"></a><span class="src-php">&lt;?php</span></div></li>
<li><div class="src-line"><a name="a2"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a3"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a4"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a5"></a><span class="src-doc">&nbsp;*&nbsp;Copyright&nbsp;(c)&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa.</span></div></li>
<li><div class="src-line"><a name="a6"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a7"></a><span class="src-doc">&nbsp;*&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a8"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a9"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;Version&nbsp;5</span></div></li>
<li><div class="src-line"><a name="a10"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a11"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a12"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a13"></a><span class="src-doc">&nbsp;*&nbsp;LICENSE</span></div></li>
<li><div class="src-line"><a name="a14"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a15"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;open&nbsp;source&nbsp;product;&nbsp;you&nbsp;can&nbsp;redistribute&nbsp;it&nbsp;and/or</span></div></li>
<li><div class="src-line"><a name="a16"></a><span class="src-doc">&nbsp;*&nbsp;modify&nbsp;it&nbsp;under&nbsp;the&nbsp;terms&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;(LGPL)</span></div></li>
<li><div class="src-line"><a name="a17"></a><span class="src-doc">&nbsp;*&nbsp;as&nbsp;published&nbsp;by&nbsp;the&nbsp;Free&nbsp;Software&nbsp;Foundation;&nbsp;either&nbsp;version&nbsp;3</span></div></li>
<li><div class="src-line"><a name="a18"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;the&nbsp;License,&nbsp;or&nbsp;(at&nbsp;your&nbsp;option)&nbsp;any&nbsp;later&nbsp;version.</span></div></li>
<li><div class="src-line"><a name="a19"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a20"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;distributed&nbsp;in&nbsp;the&nbsp;hope&nbsp;that&nbsp;it&nbsp;will&nbsp;be&nbsp;useful,</span></div></li>
<li><div class="src-line"><a name="a21"></a><span class="src-doc">&nbsp;*&nbsp;but&nbsp;WITHOUT&nbsp;ANY&nbsp;WARRANTY;&nbsp;without&nbsp;even&nbsp;the&nbsp;implied&nbsp;warranty&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a22"></a><span class="src-doc">&nbsp;*&nbsp;MERCHANTABILITY&nbsp;or&nbsp;FITNESS&nbsp;FOR&nbsp;A&nbsp;PARTICULAR&nbsp;PURPOSE.&nbsp;&nbsp;See&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a23"></a><span class="src-doc">&nbsp;*&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;for&nbsp;more&nbsp;details.</span></div></li>
<li><div class="src-line"><a name="a24"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a25"></a><span class="src-doc">&nbsp;*&nbsp;You&nbsp;should&nbsp;have&nbsp;received&nbsp;a&nbsp;copy&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License</span></div></li>
<li><div class="src-line"><a name="a26"></a><span class="src-doc">&nbsp;*&nbsp;along&nbsp;with&nbsp;this&nbsp;program.&nbsp;&nbsp;If&nbsp;not,&nbsp;see&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</span></div></li>
<li><div class="src-line"><a name="a27"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a28"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a29"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a30"></a><span class="src-doc">&nbsp;*&nbsp;Class&nbsp;Name:&nbsp;Compress&nbsp;string&nbsp;using&nbsp;Huffman-like&nbsp;coding</span></div></li>
<li><div class="src-line"><a name="a31"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a32"></a><span class="src-doc">&nbsp;*&nbsp;Filename:&nbsp;&nbsp;&nbsp;CompressStr.php</span></div></li>
<li><div class="src-line"><a name="a33"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a34"></a><span class="src-doc">&nbsp;*&nbsp;Original&nbsp;&nbsp;&nbsp;&nbsp;Author(s):&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a35"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a36"></a><span class="src-doc">&nbsp;*&nbsp;Purpose:&nbsp;&nbsp;&nbsp;&nbsp;This&nbsp;class&nbsp;will&nbsp;compress&nbsp;given&nbsp;string&nbsp;in&nbsp;binary&nbsp;format</span></div></li>
<li><div class="src-line"><a name="a37"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;variable-length&nbsp;code&nbsp;table&nbsp;(derived&nbsp;in&nbsp;a&nbsp;particular&nbsp;way</span></div></li>
<li><div class="src-line"><a name="a38"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;based&nbsp;on&nbsp;the&nbsp;estimated&nbsp;probability&nbsp;of&nbsp;occurrence&nbsp;for&nbsp;each</span></div></li>
<li><div class="src-line"><a name="a39"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;possible&nbsp;value&nbsp;of&nbsp;the&nbsp;source&nbsp;symbol)&nbsp;for&nbsp;encoding&nbsp;a&nbsp;source&nbsp;symbol</span></div></li>
<li><div class="src-line"><a name="a40"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a41"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a42"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a43"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;Compress&nbsp;String&nbsp;Class</span></div></li>
<li><div class="src-line"><a name="a44"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a45"></a><span class="src-doc">&nbsp;*&nbsp;Compress&nbsp;string&nbsp;using&nbsp;Huffman-like&nbsp;coding</span></div></li>
<li><div class="src-line"><a name="a46"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a47"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;class&nbsp;compresses&nbsp;text&nbsp;strings&nbsp;into&nbsp;roughly&nbsp;70%&nbsp;of&nbsp;their&nbsp;original&nbsp;size</span></div></li>
<li><div class="src-line"><a name="a48"></a><span class="src-doc">&nbsp;*&nbsp;by&nbsp;benefit&nbsp;from&nbsp;using&nbsp;compact&nbsp;coding&nbsp;for&nbsp;most&nbsp;frequented&nbsp;letters&nbsp;in&nbsp;a&nbsp;given</span></div></li>
<li><div class="src-line"><a name="a49"></a><span class="src-doc">&nbsp;*&nbsp;language.&nbsp;This&nbsp;algorithm&nbsp;associated&nbsp;with&nbsp;text&nbsp;language,&nbsp;so&nbsp;you&nbsp;will&nbsp;find&nbsp;6</span></div></li>
<li><div class="src-line"><a name="a50"></a><span class="src-doc">&nbsp;*&nbsp;different&nbsp;classes&nbsp;for&nbsp;the&nbsp;following&nbsp;languages:&nbsp;Arabic,&nbsp;English,&nbsp;French,</span></div></li>
<li><div class="src-line"><a name="a51"></a><span class="src-doc">&nbsp;*&nbsp;German,&nbsp;Italian&nbsp;and&nbsp;Spanish&nbsp;language.</span></div></li>
<li><div class="src-line"><a name="a52"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a53"></a><span class="src-doc">&nbsp;*&nbsp;Benefits&nbsp;of&nbsp;this&nbsp;compress&nbsp;algorithm&nbsp;include:</span></div></li>
<li><div class="src-line"><a name="a54"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a55"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;It&nbsp;is&nbsp;written&nbsp;in&nbsp;pure&nbsp;PHP&nbsp;code,&nbsp;so&nbsp;there&nbsp;is&nbsp;no&nbsp;need&nbsp;to&nbsp;any</span></div></li>
<li><div class="src-line"><a name="a56"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;PHP&nbsp;extensions&nbsp;to&nbsp;use&nbsp;it.</span></div></li>
<li><div class="src-line"><a name="a57"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;You&nbsp;can&nbsp;search&nbsp;in&nbsp;compressed&nbsp;string&nbsp;directly&nbsp;without&nbsp;any&nbsp;need&nbsp;uncompress</span></div></li>
<li><div class="src-line"><a name="a58"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;text&nbsp;before&nbsp;search&nbsp;in.</span></div></li>
<li><div class="src-line"><a name="a59"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;You&nbsp;can&nbsp;get&nbsp;original&nbsp;string&nbsp;length&nbsp;directly&nbsp;without&nbsp;need&nbsp;to&nbsp;uncompress</span></div></li>
<li><div class="src-line"><a name="a60"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;compressed&nbsp;text.</span></div></li>
<li><div class="src-line"><a name="a61"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a62"></a><span class="src-doc">&nbsp;*&nbsp;Note:</span></div></li>
<li><div class="src-line"><a name="a63"></a><span class="src-doc">&nbsp;*&nbsp;Unfortunately&nbsp;text&nbsp;compressed&nbsp;using&nbsp;this&nbsp;algorithm&nbsp;lose&nbsp;the&nbsp;structure&nbsp;that</span></div></li>
<li><div class="src-line"><a name="a64"></a><span class="src-doc">&nbsp;*&nbsp;normal&nbsp;zip&nbsp;algorithm&nbsp;used,&nbsp;so&nbsp;benefits&nbsp;from&nbsp;using&nbsp;ZLib&nbsp;functions&nbsp;on&nbsp;this</span></div></li>
<li><div class="src-line"><a name="a65"></a><span class="src-doc">&nbsp;*&nbsp;text&nbsp;will&nbsp;be&nbsp;reduced.</span></div></li>
<li><div class="src-line"><a name="a66"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a67"></a><span class="src-doc">&nbsp;*&nbsp;There&nbsp;is&nbsp;another&nbsp;drawback,&nbsp;this&nbsp;algorithm&nbsp;working&nbsp;only&nbsp;on&nbsp;text&nbsp;from&nbsp;a&nbsp;given</span></div></li>
<li><div class="src-line"><a name="a68"></a><span class="src-doc">&nbsp;*&nbsp;language,&nbsp;it&nbsp;does&nbsp;not&nbsp;working&nbsp;fine&nbsp;on&nbsp;binary&nbsp;files&nbsp;like&nbsp;images&nbsp;or&nbsp;PDF.</span></div></li>
<li><div class="src-line"><a name="a69"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a70"></a><span class="src-doc">&nbsp;*&nbsp;Example:</span></div></li>
<li><div class="src-line"><a name="a71"></a><span class="src-doc">&nbsp;*&nbsp;&lt;code&gt;</span></div></li>
<li><div class="src-line"><a name="a72"></a><span class="src-doc">&nbsp;*&nbsp;include('./I18N/Arabic.php');</span></div></li>
<li><div class="src-line"><a name="a73"></a><span class="src-doc">&nbsp;*&nbsp;$obj&nbsp;=&nbsp;new&nbsp;I18N_Arabic('CompressStr');</span></div></li>
<li><div class="src-line"><a name="a74"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a75"></a><span class="src-doc">&nbsp;*&nbsp;$obj-&gt;setInputCharset('windows-1256');</span></div></li>
<li><div class="src-line"><a name="a76"></a><span class="src-doc">&nbsp;*&nbsp;$obj-&gt;setOutputCharset('windows-1256');</span></div></li>
<li><div class="src-line"><a name="a77"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a78"></a><span class="src-doc">&nbsp;*&nbsp;$file&nbsp;=&nbsp;'Compress/ar_example.txt';</span></div></li>
<li><div class="src-line"><a name="a79"></a><span class="src-doc">&nbsp;*&nbsp;$fh&nbsp;&nbsp;&nbsp;=&nbsp;fopen($file,&nbsp;'r');</span></div></li>
<li><div class="src-line"><a name="a80"></a><span class="src-doc">&nbsp;*&nbsp;$str&nbsp;&nbsp;=&nbsp;fread($fh,&nbsp;filesize($file));</span></div></li>
<li><div class="src-line"><a name="a81"></a><span class="src-doc">&nbsp;*&nbsp;fclose($fh);</span></div></li>
<li><div class="src-line"><a name="a82"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a83"></a><span class="src-doc">&nbsp;*&nbsp;$zip&nbsp;=&nbsp;$obj-&gt;compress($str);</span></div></li>
<li><div class="src-line"><a name="a84"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a85"></a><span class="src-doc">&nbsp;*&nbsp;$before&nbsp;=&nbsp;strlen($str);</span></div></li>
<li><div class="src-line"><a name="a86"></a><span class="src-doc">&nbsp;*&nbsp;$after&nbsp;&nbsp;=&nbsp;strlen($zip);</span></div></li>
<li><div class="src-line"><a name="a87"></a><span class="src-doc">&nbsp;*&nbsp;$rate&nbsp;&nbsp;&nbsp;=&nbsp;round($after&nbsp;*&nbsp;100&nbsp;/&nbsp;$before);</span></div></li>
<li><div class="src-line"><a name="a88"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a89"></a><span class="src-doc">&nbsp;*&nbsp;echo&nbsp;&quot;String&nbsp;size&nbsp;before&nbsp;was:&nbsp;$before&nbsp;Byte&lt;br&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a90"></a><span class="src-doc">&nbsp;*&nbsp;echo&nbsp;&quot;Compressed&nbsp;string&nbsp;size&nbsp;after&nbsp;is:&nbsp;$after&nbsp;Byte&lt;br&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a91"></a><span class="src-doc">&nbsp;*&nbsp;echo&nbsp;&quot;Rate&nbsp;$rate&nbsp;%&lt;hr&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a92"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a93"></a><span class="src-doc">&nbsp;*&nbsp;$str&nbsp;=&nbsp;$obj-&gt;decompress($zip);</span></div></li>
<li><div class="src-line"><a name="a94"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a95"></a><span class="src-doc">&nbsp;*&nbsp;if&nbsp;($obj-&gt;search($zip,&nbsp;$word))&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a96"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;Search&nbsp;for&nbsp;$word&nbsp;in&nbsp;zipped&nbsp;string&nbsp;and&nbsp;find&nbsp;it&lt;hr&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a97"></a><span class="src-doc">&nbsp;*&nbsp;}&nbsp;else&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a98"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;Search&nbsp;for&nbsp;$word&nbsp;in&nbsp;zipped&nbsp;string&nbsp;and&nbsp;do&nbsp;not&nbsp;find&nbsp;it&lt;hr&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a99"></a><span class="src-doc">&nbsp;*&nbsp;}</span></div></li>
<li><div class="src-line"><a name="a100"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a101"></a><span class="src-doc">&nbsp;*&nbsp;$len&nbsp;=&nbsp;$obj-&gt;length($zip);</span></div></li>
<li><div class="src-line"><a name="a102"></a><span class="src-doc">&nbsp;*&nbsp;echo&nbsp;&quot;Original&nbsp;length&nbsp;of&nbsp;zipped&nbsp;string&nbsp;is&nbsp;$len&nbsp;Byte&lt;hr&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a103"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a104"></a><span class="src-doc">&nbsp;*&nbsp;echo&nbsp;'&lt;div&nbsp;dir=&quot;rtl&quot;&nbsp;align=&quot;justify&quot;&gt;'.nl2br($str).'&lt;/div&gt;';</span></div></li>
<li><div class="src-line"><a name="a105"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/code&gt;</span></div></li>
<li><div class="src-line"><a name="a106"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a107"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a108"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a109"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a110"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a111"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a112"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a113"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a114"></a><span class="src-doc">&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a115"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a116"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a117"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;PHP&nbsp;class&nbsp;compress&nbsp;Arabic&nbsp;string&nbsp;using&nbsp;Huffman-like&nbsp;coding</span></div></li>
<li><div class="src-line"><a name="a118"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a119"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a120"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a121"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a122"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a123"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a124"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a125"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a126"></a><span class="src-doc">&nbsp;*/&nbsp;</span></div></li>
<li><div class="src-line"><a name="a127"></a><span class="src-key">class&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a></div></li>
<li><div class="src-line"><a name="a128"></a><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a129"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-var">$_encode</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a130"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-var">$_binary</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a131"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a132"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-var">$_hex</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a133"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-var">$_bin</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a134"></a>&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a135"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a136"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Loads&nbsp;initialize&nbsp;values</span></div></li>
<li><div class="src-line"><a name="a137"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a138"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a139"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a140"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><span class="src-id">__construct</span><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a141"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a142"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode&nbsp;</span>=&nbsp;<span class="src-id">iconv</span><span class="src-sym">(</span><span class="src-str">'utf-8'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'cp1256'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;الميوتة'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a143"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_binary&nbsp;</span>=&nbsp;<span class="src-str">'0000|0001|0010|0011|0100|0101|0110|0111|'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a144"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a145"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_hex&nbsp;</span>=&nbsp;<span class="src-str">'0123456789abcdef'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a146"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_bin&nbsp;</span>=&nbsp;<span class="src-str">'0000|0001|0010|0011|0100|0101|0110|0111|1000|'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a147"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_bin&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_bin&nbsp;</span>.&nbsp;<span class="src-str">'1001|1010|1011|1100|1101|1110|1111|'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a148"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a149"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a150"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a151"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;required&nbsp;encode&nbsp;and&nbsp;binary&nbsp;hash&nbsp;of&nbsp;most&nbsp;probably&nbsp;character&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a152"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;selected&nbsp;language</span></div></li>
<li><div class="src-line"><a name="a153"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a154"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$lang&nbsp;</span><span class="src-doc">[en,&nbsp;fr,&nbsp;gr,&nbsp;it,&nbsp;sp,&nbsp;ar]&nbsp;Language&nbsp;profile&nbsp;selected</span></div></li>
<li><div class="src-line"><a name="a155"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a156"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a157"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a158"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a159"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodsetLang">setLang</a><span class="src-sym">(</span><span class="src-var">$lang</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a160"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a161"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">switch&nbsp;</span><span class="src-sym">(</span><span class="src-var">$lang</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a162"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">case&nbsp;</span><span class="src-str">'en'</span>:</div></li>
<li><div class="src-line"><a name="a163"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode&nbsp;</span>=&nbsp;<span class="src-str">'&nbsp;etaoins'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a164"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">break</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a165"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">case&nbsp;</span><span class="src-str">'fr'</span>:</div></li>
<li><div class="src-line"><a name="a166"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode&nbsp;</span>=&nbsp;<span class="src-str">'&nbsp;enasriu'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a167"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">break</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a168"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">case&nbsp;</span><span class="src-str">'gr'</span>:</div></li>
<li><div class="src-line"><a name="a169"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode&nbsp;</span>=&nbsp;<span class="src-str">'&nbsp;enristu'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a170"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">break</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a171"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">case&nbsp;</span><span class="src-str">'it'</span>:</div></li>
<li><div class="src-line"><a name="a172"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode&nbsp;</span>=&nbsp;<span class="src-str">'&nbsp;eiaorln'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a173"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">break</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a174"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">case&nbsp;</span><span class="src-str">'sp'</span>:</div></li>
<li><div class="src-line"><a name="a175"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode&nbsp;</span>=&nbsp;<span class="src-str">'&nbsp;eaosrin'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a176"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">break</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a177"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">default</span>:</div></li>
<li><div class="src-line"><a name="a178"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode&nbsp;</span>=&nbsp;<span class="src-id">iconv</span><span class="src-sym">(</span><span class="src-str">'utf-8'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'cp1256'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;الميوتة'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a179"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a180"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a181"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_binary&nbsp;</span>=&nbsp;<span class="src-str">'0000|0001|0010|0011|0100|0101|0110|0111|'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a182"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a183"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a184"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a185"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a186"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a187"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Compress&nbsp;the&nbsp;given&nbsp;string&nbsp;using&nbsp;the&nbsp;Huffman-like&nbsp;coding</span></div></li>
<li><div class="src-line"><a name="a188"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a189"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$str&nbsp;</span><span class="src-doc">The&nbsp;text&nbsp;to&nbsp;compress</span></div></li>
<li><div class="src-line"><a name="a190"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a191"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc">The&nbsp;compressed&nbsp;string&nbsp;in&nbsp;binary&nbsp;format</span></div></li>
<li><div class="src-line"><a name="a192"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a193"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a194"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodcompress">compress</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a195"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a196"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">iconv</span><span class="src-sym">(</span><span class="src-str">'utf-8'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'cp1256'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a197"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a198"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-id">str2bits</span><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a199"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$hex&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-id">bits2hex</span><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a200"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bin&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/pack">pack</a><span class="src-sym">(</span><span class="src-str">'h*'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hex</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a201"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a202"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$bin</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a203"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a204"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a205"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a206"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Uncompress&nbsp;a&nbsp;compressed&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a207"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a208"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc-var">$bin&nbsp;</span><span class="src-doc">The&nbsp;text&nbsp;compressed&nbsp;by&nbsp;compress().</span></div></li>
<li><div class="src-line"><a name="a209"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a210"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">The&nbsp;original&nbsp;uncompressed&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a211"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a212"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a213"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methoddecompress">decompress</a><span class="src-sym">(</span><span class="src-var">$bin</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a214"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a215"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$temp&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/unpack">unpack</a><span class="src-sym">(</span><span class="src-str">'h*'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$bin</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a216"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bytes&nbsp;</span>=&nbsp;<span class="src-var">$temp</span><span class="src-sym">[</span><span class="src-num">1</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a217"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a218"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-id">hex2bits</span><span class="src-sym">(</span><span class="src-var">$bytes</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a219"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-id">bits2str</span><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a220"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/iconv">iconv</a><span class="src-sym">(</span><span class="src-str">'cp1256'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'utf-8'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a221"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a222"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$str</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a223"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a224"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a225"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a226"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Search&nbsp;a&nbsp;compressed&nbsp;string&nbsp;for&nbsp;a&nbsp;given&nbsp;word</span></div></li>
<li><div class="src-line"><a name="a227"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a228"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc-var">$bin&nbsp;</span><span class="src-doc">&nbsp;Compressed&nbsp;binary&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a229"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$word&nbsp;</span><span class="src-doc">The&nbsp;string&nbsp;you&nbsp;looking&nbsp;for</span></div></li>
<li><div class="src-line"><a name="a230"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a231"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">boolean&nbsp;</span><span class="src-doc">True&nbsp;if&nbsp;found&nbsp;and&nbsp;False&nbsp;if&nbsp;not&nbsp;found</span></div></li>
<li><div class="src-line"><a name="a232"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a233"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a234"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodsearch">search</a><span class="src-sym">(</span><span class="src-var">$bin</span><span class="src-sym">,&nbsp;</span><span class="src-var">$word</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a235"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a236"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$word&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/iconv">iconv</a><span class="src-sym">(</span><span class="src-str">'utf-8'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'cp1256'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$word</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a237"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wBits&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-id">str2bits</span><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a238"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a239"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$temp&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/unpack">unpack</a><span class="src-sym">(</span><span class="src-str">'h*'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$bin</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a240"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bytes&nbsp;</span>=&nbsp;<span class="src-var">$temp</span><span class="src-sym">[</span><span class="src-num">1</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a241"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-id">hex2bits</span><span class="src-sym">(</span><span class="src-var">$bytes</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a242"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a243"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/strpos">strpos</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-var">$wBits</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a244"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-id">true</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a245"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a246"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-id">false</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a247"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a248"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a249"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a250"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a251"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Retrieve&nbsp;the&nbsp;original&nbsp;string&nbsp;length</span></div></li>
<li><div class="src-line"><a name="a252"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a253"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc-var">$bin&nbsp;</span><span class="src-doc">Compressed&nbsp;binary&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a254"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a255"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc">Original&nbsp;string&nbsp;length</span></div></li>
<li><div class="src-line"><a name="a256"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a257"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a258"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodlength">length</a><span class="src-sym">(</span><span class="src-var">$bin</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a259"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a260"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$temp&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/unpack">unpack</a><span class="src-sym">(</span><span class="src-str">'h*'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$bin</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a261"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bytes&nbsp;</span>=&nbsp;<span class="src-var">$temp</span><span class="src-sym">[</span><span class="src-num">1</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a262"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">self</span><span class="src-sym">::</span><span class="src-id">hex2bits</span><span class="src-sym">(</span><span class="src-var">$bytes</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a263"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a264"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$count&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a265"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$i&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a266"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a267"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">while&nbsp;</span><span class="src-sym">(</span>isset<span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a268"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$count</span>++<span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a269"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]&nbsp;</span>==&nbsp;<span class="src-num">1</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a270"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$i&nbsp;</span>+=&nbsp;<span class="src-num">9</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a271"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a272"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$i&nbsp;</span>+=&nbsp;<span class="src-num">4</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a273"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a274"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a275"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a276"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$count</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a277"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a278"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a279"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a280"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;textual&nbsp;string&nbsp;into&nbsp;binary&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a281"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a282"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$str&nbsp;</span><span class="src-doc">The&nbsp;textual&nbsp;string&nbsp;to&nbsp;convert</span></div></li>
<li><div class="src-line"><a name="a283"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a284"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc">The&nbsp;binary&nbsp;representation&nbsp;of&nbsp;textual&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a285"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a286"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a287"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodstr2bits">str2bits</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a288"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a289"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a290"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$total&nbsp;</span>=&nbsp;<a href="http://www.php.net/strlen">strlen</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a291"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a292"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$i&nbsp;</span>=&nbsp;-<span class="src-num">1</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a293"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">while&nbsp;</span><span class="src-sym">(</span>++<span class="src-var">$i&nbsp;</span>&lt;&nbsp;<span class="src-var">$total</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a294"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$char&nbsp;</span>=&nbsp;<span class="src-var">$str</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a295"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$pos&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/strpos">strpos</a><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode</span><span class="src-sym">,&nbsp;</span><span class="src-var">$char</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a296"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a297"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$pos&nbsp;</span>!==&nbsp;<span class="src-id">false</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a298"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>.=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_binary</span><span class="src-sym">,&nbsp;</span><span class="src-var">$pos</span>*<span class="src-num">5</span><span class="src-sym">,&nbsp;</span><span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a299"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a300"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$int&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">ord</span><span class="src-sym">(</span><span class="src-var">$char</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a301"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>.=&nbsp;<span class="src-str">'1'</span>.<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_bin</span><span class="src-sym">,&nbsp;</span>(int)<span class="src-sym">(</span><span class="src-var">$int</span>/<span class="src-num">16</span><span class="src-sym">)</span>*<span class="src-num">5</span><span class="src-sym">,&nbsp;</span><span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a302"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>.=&nbsp;<span class="src-id">substr</span><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_bin</span><span class="src-sym">,&nbsp;</span><span class="src-sym">(</span><span class="src-var">$int</span>%<span class="src-num">16</span><span class="src-sym">)</span>*<span class="src-num">5</span><span class="src-sym">,&nbsp;</span><span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a303"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a304"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a305"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a306"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Complete&nbsp;nibbel</span></div></li>
<li><div class="src-line"><a name="a307"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$add&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">strlen</span><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">)&nbsp;</span>%&nbsp;<span class="src-num">4</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a308"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>.=&nbsp;<a href="http://www.php.net/str_repeat">str_repeat</a><span class="src-sym">(</span><span class="src-str">'0'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$add</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a309"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a310"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$bits</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a311"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a312"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a313"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a314"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;binary&nbsp;string&nbsp;into&nbsp;textual&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a315"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a316"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc-var">$bits&nbsp;</span><span class="src-doc">The&nbsp;binary&nbsp;string&nbsp;to&nbsp;convert</span></div></li>
<li><div class="src-line"><a name="a317"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a318"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">The&nbsp;textual&nbsp;representation&nbsp;of&nbsp;binary&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a319"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a320"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a321"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodbits2str">bits2str</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a322"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a323"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a324"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">while&nbsp;</span><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a325"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$flag&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a326"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a327"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a328"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$flag&nbsp;</span>==&nbsp;<span class="src-num">1</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a329"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$byte&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">8</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a330"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-num">8</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a331"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a332"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$bits&nbsp;</span>||&nbsp;<a href="http://www.php.net/strlen">strlen</a><span class="src-sym">(</span><span class="src-var">$code</span><span class="src-sym">)&nbsp;</span>==&nbsp;<span class="src-num">8</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a333"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$int&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/base_convert">base_convert</a><span class="src-sym">(</span><span class="src-var">$byte</span><span class="src-sym">,&nbsp;</span><span class="src-num">2</span><span class="src-sym">,&nbsp;</span><span class="src-num">10</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a334"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$char&nbsp;</span>=&nbsp;<a href="http://www.php.net/chr">chr</a><span class="src-sym">(</span><span class="src-var">$int</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a335"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>.=&nbsp;<span class="src-var">$char</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a336"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a337"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a338"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$code&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">3</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a339"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-num">3</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a340"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a341"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$bits&nbsp;</span>||&nbsp;<a href="http://www.php.net/strlen">strlen</a><span class="src-sym">(</span><span class="src-var">$code</span><span class="src-sym">)&nbsp;</span>==&nbsp;<span class="src-num">3</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a342"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$pos&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/strpos">strpos</a><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_binary</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;</span><span class="src-str">0<span class="src-var">$code</span>|</span><span class="src-str">&quot;</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a343"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>.=&nbsp;<span class="src-id">substr</span><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_encode</span><span class="src-sym">,&nbsp;</span><span class="src-var">$pos</span>/<span class="src-num">5</span><span class="src-sym">,&nbsp;</span><span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a344"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a345"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a346"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a347"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a348"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$str</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a349"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a350"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a351"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a352"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;binary&nbsp;string&nbsp;into&nbsp;hexadecimal&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a353"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a354"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc-var">$bits&nbsp;</span><span class="src-doc">The&nbsp;binary&nbsp;string&nbsp;to&nbsp;convert</span></div></li>
<li><div class="src-line"><a name="a355"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a356"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">hexadecimal&nbsp;</span><span class="src-doc">The&nbsp;hexadecimal&nbsp;representation&nbsp;of&nbsp;binary&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a357"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a358"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a359"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodbits2hex">bits2hex</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a360"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a361"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$hex&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a362"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$total&nbsp;</span>=&nbsp;<span class="src-id">strlen</span><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">4</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a363"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a364"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">for&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i&nbsp;</span>&lt;&nbsp;<span class="src-var">$total</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i</span>++<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a365"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$nibbel&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$bits</span><span class="src-sym">,&nbsp;</span><span class="src-var">$i</span>*<span class="src-num">4</span><span class="src-sym">,&nbsp;</span><span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a366"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a367"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$pos&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/strpos">strpos</a><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_bin</span><span class="src-sym">,&nbsp;</span><span class="src-var">$nibbel</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a368"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$hex&nbsp;</span>.=&nbsp;<span class="src-id">substr</span><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_hex</span><span class="src-sym">,&nbsp;</span><span class="src-var">$pos</span>/<span class="src-num">5</span><span class="src-sym">,&nbsp;</span><span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a369"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a370"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a371"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$hex</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a372"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a373"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a374"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a375"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;hexadecimal&nbsp;string&nbsp;into&nbsp;binary&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a376"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a377"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">hexadecimal&nbsp;</span><span class="src-doc-var">$hex&nbsp;</span><span class="src-doc">The&nbsp;hexadezimal&nbsp;string&nbsp;to&nbsp;convert</span></div></li>
<li><div class="src-line"><a name="a378"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a379"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">binary&nbsp;</span><span class="src-doc">The&nbsp;binary&nbsp;representation&nbsp;of&nbsp;hexadecimal&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a380"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a381"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a382"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_CompressStr.html#methodhex2bits">hex2bits</a><span class="src-sym">(</span><span class="src-var">$hex</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a383"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a384"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a385"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$total&nbsp;</span>=&nbsp;<span class="src-id">strlen</span><span class="src-sym">(</span><span class="src-var">$hex</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a386"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a387"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">for&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i&nbsp;</span>&lt;&nbsp;<span class="src-var">$total</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i</span>++<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a388"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$pos&nbsp;&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/strpos">strpos</a><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_hex</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hex</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a389"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bits&nbsp;</span>.=&nbsp;<span class="src-id">substr</span><span class="src-sym">(</span><span class="src-id">self</span><span class="src-sym">::</span><span class="src-var">$_bin</span><span class="src-sym">,&nbsp;</span><span class="src-var">$pos</span>*<span class="src-num">5</span><span class="src-sym">,&nbsp;</span><span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a390"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a391"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a392"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$bits</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a393"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a394"></a><span class="src-sym">}</span></div></li>
</ol>
</div>
        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:55 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>