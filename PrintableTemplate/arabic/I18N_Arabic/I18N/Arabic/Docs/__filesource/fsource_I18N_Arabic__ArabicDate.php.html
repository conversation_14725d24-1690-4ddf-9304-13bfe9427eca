<html>
<head>
<title>File Source for Date.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                  
      
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1 align="center">Source for file Date.php</h1>
<p>Documentation is available at <a href="../I18N_Arabic/_Arabic---Date.php.html">Date.php</a></p>
<div class="src-code">
<ol><li><div class="src-line"><a name="a1"></a><span class="src-php">&lt;?php</span></div></li>
<li><div class="src-line"><a name="a2"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a3"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a4"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a5"></a><span class="src-doc">&nbsp;*&nbsp;Copyright&nbsp;(c)&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa.</span></div></li>
<li><div class="src-line"><a name="a6"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a7"></a><span class="src-doc">&nbsp;*&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a8"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a9"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;Version&nbsp;5</span></div></li>
<li><div class="src-line"><a name="a10"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a11"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a12"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a13"></a><span class="src-doc">&nbsp;*&nbsp;LICENSE</span></div></li>
<li><div class="src-line"><a name="a14"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a15"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;open&nbsp;source&nbsp;product;&nbsp;you&nbsp;can&nbsp;redistribute&nbsp;it&nbsp;and/or</span></div></li>
<li><div class="src-line"><a name="a16"></a><span class="src-doc">&nbsp;*&nbsp;modify&nbsp;it&nbsp;under&nbsp;the&nbsp;terms&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;(LGPL)</span></div></li>
<li><div class="src-line"><a name="a17"></a><span class="src-doc">&nbsp;*&nbsp;as&nbsp;published&nbsp;by&nbsp;the&nbsp;Free&nbsp;Software&nbsp;Foundation;&nbsp;either&nbsp;version&nbsp;3</span></div></li>
<li><div class="src-line"><a name="a18"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;the&nbsp;License,&nbsp;or&nbsp;(at&nbsp;your&nbsp;option)&nbsp;any&nbsp;later&nbsp;version.</span></div></li>
<li><div class="src-line"><a name="a19"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a20"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;distributed&nbsp;in&nbsp;the&nbsp;hope&nbsp;that&nbsp;it&nbsp;will&nbsp;be&nbsp;useful,</span></div></li>
<li><div class="src-line"><a name="a21"></a><span class="src-doc">&nbsp;*&nbsp;but&nbsp;WITHOUT&nbsp;ANY&nbsp;WARRANTY;&nbsp;without&nbsp;even&nbsp;the&nbsp;implied&nbsp;warranty&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a22"></a><span class="src-doc">&nbsp;*&nbsp;MERCHANTABILITY&nbsp;or&nbsp;FITNESS&nbsp;FOR&nbsp;A&nbsp;PARTICULAR&nbsp;PURPOSE.&nbsp;&nbsp;See&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a23"></a><span class="src-doc">&nbsp;*&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;for&nbsp;more&nbsp;details.</span></div></li>
<li><div class="src-line"><a name="a24"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a25"></a><span class="src-doc">&nbsp;*&nbsp;You&nbsp;should&nbsp;have&nbsp;received&nbsp;a&nbsp;copy&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License</span></div></li>
<li><div class="src-line"><a name="a26"></a><span class="src-doc">&nbsp;*&nbsp;along&nbsp;with&nbsp;this&nbsp;program.&nbsp;&nbsp;If&nbsp;not,&nbsp;see&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</span></div></li>
<li><div class="src-line"><a name="a27"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a28"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a29"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a30"></a><span class="src-doc">&nbsp;*&nbsp;Class&nbsp;Name:&nbsp;Arabic&nbsp;Date</span></div></li>
<li><div class="src-line"><a name="a31"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a32"></a><span class="src-doc">&nbsp;*&nbsp;Filename:&nbsp;&nbsp;&nbsp;Date.php</span></div></li>
<li><div class="src-line"><a name="a33"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a34"></a><span class="src-doc">&nbsp;*&nbsp;Original&nbsp;&nbsp;&nbsp;&nbsp;Author(s):&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a35"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a36"></a><span class="src-doc">&nbsp;*&nbsp;Purpose:&nbsp;&nbsp;&nbsp;&nbsp;Arabic&nbsp;customization&nbsp;for&nbsp;PHP&nbsp;date&nbsp;function</span></div></li>
<li><div class="src-line"><a name="a37"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a38"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a39"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a40"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;Date</span></div></li>
<li><div class="src-line"><a name="a41"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a42"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;class&nbsp;for&nbsp;Arabic&nbsp;and&nbsp;Islamic&nbsp;customization&nbsp;of&nbsp;PHP&nbsp;date&nbsp;function.&nbsp;It</span></div></li>
<li><div class="src-line"><a name="a43"></a><span class="src-doc">&nbsp;*&nbsp;can&nbsp;convert&nbsp;UNIX&nbsp;timestamp&nbsp;into&nbsp;string&nbsp;in&nbsp;Arabic&nbsp;as&nbsp;well&nbsp;as&nbsp;convert&nbsp;it&nbsp;into</span></div></li>
<li><div class="src-line"><a name="a44"></a><span class="src-doc">&nbsp;*&nbsp;Hijri&nbsp;calendar</span></div></li>
<li><div class="src-line"><a name="a45"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a46"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Islamic&nbsp;Calendar:</span></div></li>
<li><div class="src-line"><a name="a47"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a48"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Islamic&nbsp;calendar&nbsp;is&nbsp;purely&nbsp;lunar&nbsp;and&nbsp;consists&nbsp;of&nbsp;twelve&nbsp;alternating&nbsp;months</span></div></li>
<li><div class="src-line"><a name="a49"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;30&nbsp;and&nbsp;29&nbsp;days,&nbsp;with&nbsp;the&nbsp;final&nbsp;29&nbsp;day&nbsp;month&nbsp;extended&nbsp;to&nbsp;30&nbsp;days&nbsp;during&nbsp;leap</span></div></li>
<li><div class="src-line"><a name="a50"></a><span class="src-doc">&nbsp;*&nbsp;years.&nbsp;Leap&nbsp;years&nbsp;follow&nbsp;a&nbsp;30&nbsp;year&nbsp;cycle&nbsp;and&nbsp;occur&nbsp;in&nbsp;years&nbsp;1,&nbsp;5,&nbsp;7,&nbsp;10,&nbsp;13,&nbsp;16,</span></div></li>
<li><div class="src-line"><a name="a51"></a><span class="src-doc">&nbsp;*&nbsp;18,&nbsp;21,&nbsp;24,&nbsp;26,&nbsp;and&nbsp;29.&nbsp;The&nbsp;calendar&nbsp;begins&nbsp;on&nbsp;Friday,&nbsp;July&nbsp;16th,&nbsp;622&nbsp;C.E.&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a52"></a><span class="src-doc">&nbsp;*&nbsp;the&nbsp;Julian&nbsp;calendar,&nbsp;Julian&nbsp;day&nbsp;1948439.5,&nbsp;the&nbsp;day&nbsp;of&nbsp;Muhammad's&nbsp;separate&nbsp;from</span></div></li>
<li><div class="src-line"><a name="a53"></a><span class="src-doc">&nbsp;*&nbsp;Mecca&nbsp;to&nbsp;Medina,&nbsp;the&nbsp;first&nbsp;day&nbsp;of&nbsp;the&nbsp;first&nbsp;month&nbsp;of&nbsp;year&nbsp;1&nbsp;A.H.--&quot;Anno&nbsp;Hegira&quot;.</span></div></li>
<li><div class="src-line"><a name="a54"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a55"></a><span class="src-doc">&nbsp;*&nbsp;Each&nbsp;cycle&nbsp;of&nbsp;30&nbsp;years&nbsp;thus&nbsp;contains&nbsp;19&nbsp;normal&nbsp;years&nbsp;of&nbsp;354&nbsp;days&nbsp;and&nbsp;11&nbsp;leap</span></div></li>
<li><div class="src-line"><a name="a56"></a><span class="src-doc">&nbsp;*&nbsp;years&nbsp;of&nbsp;355,&nbsp;so&nbsp;the&nbsp;average&nbsp;length&nbsp;of&nbsp;a&nbsp;year&nbsp;is&nbsp;therefore</span></div></li>
<li><div class="src-line"><a name="a57"></a><span class="src-doc">&nbsp;*&nbsp;((19&nbsp;x&nbsp;354)&nbsp;+&nbsp;(11&nbsp;x&nbsp;355))&nbsp;/&nbsp;30&nbsp;=&nbsp;354.365...&nbsp;days,&nbsp;with&nbsp;a&nbsp;mean&nbsp;length&nbsp;of&nbsp;month&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a58"></a><span class="src-doc">&nbsp;*&nbsp;1/12&nbsp;this&nbsp;figure,&nbsp;or&nbsp;29.53055...&nbsp;days,&nbsp;which&nbsp;closely&nbsp;approximates&nbsp;the&nbsp;mean</span></div></li>
<li><div class="src-line"><a name="a59"></a><span class="src-doc">&nbsp;*&nbsp;synodic&nbsp;month&nbsp;(time&nbsp;from&nbsp;new&nbsp;Moon&nbsp;to&nbsp;next&nbsp;new&nbsp;Moon)&nbsp;of&nbsp;29.530588&nbsp;days,&nbsp;with&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a60"></a><span class="src-doc">&nbsp;*&nbsp;calendar&nbsp;only&nbsp;slipping&nbsp;one&nbsp;day&nbsp;with&nbsp;respect&nbsp;to&nbsp;the&nbsp;Moon&nbsp;every&nbsp;2525&nbsp;years.&nbsp;Since</span></div></li>
<li><div class="src-line"><a name="a61"></a><span class="src-doc">&nbsp;*&nbsp;the&nbsp;calendar&nbsp;is&nbsp;fixed&nbsp;to&nbsp;the&nbsp;Moon,&nbsp;not&nbsp;the&nbsp;solar&nbsp;year,&nbsp;the&nbsp;months&nbsp;shift&nbsp;with</span></div></li>
<li><div class="src-line"><a name="a62"></a><span class="src-doc">&nbsp;*&nbsp;respect&nbsp;to&nbsp;the&nbsp;seasons,&nbsp;with&nbsp;each&nbsp;month&nbsp;beginning&nbsp;about&nbsp;11&nbsp;days&nbsp;earlier&nbsp;in&nbsp;each</span></div></li>
<li><div class="src-line"><a name="a63"></a><span class="src-doc">&nbsp;*&nbsp;successive&nbsp;solar&nbsp;year.</span></div></li>
<li><div class="src-line"><a name="a64"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a65"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;convert&nbsp;presented&nbsp;here&nbsp;is&nbsp;the&nbsp;most&nbsp;commonly&nbsp;used&nbsp;civil&nbsp;calendar&nbsp;in&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a66"></a><span class="src-doc">&nbsp;*&nbsp;Islamic&nbsp;world;&nbsp;for&nbsp;religious&nbsp;purposes&nbsp;months&nbsp;are&nbsp;defined&nbsp;to&nbsp;start&nbsp;with&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a67"></a><span class="src-doc">&nbsp;*&nbsp;first&nbsp;observation&nbsp;of&nbsp;the&nbsp;crescent&nbsp;of&nbsp;the&nbsp;new&nbsp;Moon.</span></div></li>
<li><div class="src-line"><a name="a68"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a69"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Julian&nbsp;Calendar:</span></div></li>
<li><div class="src-line"><a name="a70"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a71"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Julian&nbsp;calendar&nbsp;was&nbsp;proclaimed&nbsp;by&nbsp;Julius&nbsp;Casar&nbsp;in&nbsp;46&nbsp;B.C.&nbsp;and&nbsp;underwent</span></div></li>
<li><div class="src-line"><a name="a72"></a><span class="src-doc">&nbsp;*&nbsp;several&nbsp;modifications&nbsp;before&nbsp;reaching&nbsp;its&nbsp;final&nbsp;form&nbsp;in&nbsp;8&nbsp;C.E.&nbsp;The&nbsp;Julian</span></div></li>
<li><div class="src-line"><a name="a73"></a><span class="src-doc">&nbsp;*&nbsp;calendar&nbsp;differs&nbsp;from&nbsp;the&nbsp;Gregorian&nbsp;only&nbsp;in&nbsp;the&nbsp;determination&nbsp;of&nbsp;leap&nbsp;years,</span></div></li>
<li><div class="src-line"><a name="a74"></a><span class="src-doc">&nbsp;*&nbsp;lacking&nbsp;the&nbsp;correction&nbsp;for&nbsp;years&nbsp;divisible&nbsp;by&nbsp;100&nbsp;and&nbsp;400&nbsp;in&nbsp;the&nbsp;Gregorian</span></div></li>
<li><div class="src-line"><a name="a75"></a><span class="src-doc">&nbsp;*&nbsp;calendar.&nbsp;In&nbsp;the&nbsp;Julian&nbsp;calendar,&nbsp;any&nbsp;positive&nbsp;year&nbsp;is&nbsp;a&nbsp;leap&nbsp;year&nbsp;if&nbsp;divisible</span></div></li>
<li><div class="src-line"><a name="a76"></a><span class="src-doc">&nbsp;*&nbsp;by&nbsp;4.&nbsp;(Negative&nbsp;years&nbsp;are&nbsp;leap&nbsp;years&nbsp;if&nbsp;when&nbsp;divided&nbsp;by&nbsp;4&nbsp;a&nbsp;remainder&nbsp;of&nbsp;3</span></div></li>
<li><div class="src-line"><a name="a77"></a><span class="src-doc">&nbsp;*&nbsp;results.)&nbsp;Days&nbsp;are&nbsp;considered&nbsp;to&nbsp;begin&nbsp;at&nbsp;midnight.</span></div></li>
<li><div class="src-line"><a name="a78"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a79"></a><span class="src-doc">&nbsp;*&nbsp;In&nbsp;the&nbsp;Julian&nbsp;calendar&nbsp;the&nbsp;average&nbsp;year&nbsp;has&nbsp;a&nbsp;length&nbsp;of&nbsp;365.25&nbsp;days.&nbsp;compared&nbsp;to</span></div></li>
<li><div class="src-line"><a name="a80"></a><span class="src-doc">&nbsp;*&nbsp;the&nbsp;actual&nbsp;solar&nbsp;tropical&nbsp;year&nbsp;of&nbsp;365.24219878&nbsp;days.&nbsp;The&nbsp;calendar&nbsp;thus</span></div></li>
<li><div class="src-line"><a name="a81"></a><span class="src-doc">&nbsp;*&nbsp;accumulates&nbsp;one&nbsp;day&nbsp;of&nbsp;error&nbsp;with&nbsp;respect&nbsp;to&nbsp;the&nbsp;solar&nbsp;year&nbsp;every&nbsp;128&nbsp;years.</span></div></li>
<li><div class="src-line"><a name="a82"></a><span class="src-doc">&nbsp;*&nbsp;Being&nbsp;a&nbsp;purely&nbsp;solar&nbsp;calendar,&nbsp;no&nbsp;attempt&nbsp;is&nbsp;made&nbsp;to&nbsp;synchronise&nbsp;the&nbsp;start&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a83"></a><span class="src-doc">&nbsp;*&nbsp;months&nbsp;to&nbsp;the&nbsp;phases&nbsp;of&nbsp;the&nbsp;Moon.</span></div></li>
<li><div class="src-line"><a name="a84"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a85"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Gregorian&nbsp;Calendar:</span></div></li>
<li><div class="src-line"><a name="a86"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a87"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Gregorian&nbsp;calendar&nbsp;was&nbsp;proclaimed&nbsp;by&nbsp;Pope&nbsp;Gregory&nbsp;XIII&nbsp;and&nbsp;took&nbsp;effect&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a88"></a><span class="src-doc">&nbsp;*&nbsp;most&nbsp;Catholic&nbsp;states&nbsp;in&nbsp;1582,&nbsp;in&nbsp;which&nbsp;October&nbsp;4,&nbsp;1582&nbsp;of&nbsp;the&nbsp;Julian&nbsp;calendar</span></div></li>
<li><div class="src-line"><a name="a89"></a><span class="src-doc">&nbsp;*&nbsp;was&nbsp;followed&nbsp;by&nbsp;October&nbsp;15&nbsp;in&nbsp;the&nbsp;new&nbsp;calendar,&nbsp;correcting&nbsp;for&nbsp;the&nbsp;accumulated</span></div></li>
<li><div class="src-line"><a name="a90"></a><span class="src-doc">&nbsp;*&nbsp;discrepancy&nbsp;between&nbsp;the&nbsp;Julian&nbsp;calendar&nbsp;and&nbsp;the&nbsp;equinox&nbsp;as&nbsp;of&nbsp;that&nbsp;date.&nbsp;When</span></div></li>
<li><div class="src-line"><a name="a91"></a><span class="src-doc">&nbsp;*&nbsp;comparing&nbsp;historical&nbsp;dates,&nbsp;it's&nbsp;important&nbsp;to&nbsp;note&nbsp;that&nbsp;the&nbsp;Gregorian&nbsp;calendar,</span></div></li>
<li><div class="src-line"><a name="a92"></a><span class="src-doc">&nbsp;*&nbsp;used&nbsp;universally&nbsp;today&nbsp;in&nbsp;Western&nbsp;countries&nbsp;and&nbsp;in&nbsp;international&nbsp;commerce,&nbsp;was</span></div></li>
<li><div class="src-line"><a name="a93"></a><span class="src-doc">&nbsp;*&nbsp;adopted&nbsp;at&nbsp;different&nbsp;times&nbsp;by&nbsp;different&nbsp;countries.&nbsp;Britain&nbsp;and&nbsp;her&nbsp;colonies</span></div></li>
<li><div class="src-line"><a name="a94"></a><span class="src-doc">&nbsp;*&nbsp;(including&nbsp;what&nbsp;is&nbsp;now&nbsp;the&nbsp;United&nbsp;States),&nbsp;did&nbsp;not&nbsp;switch&nbsp;to&nbsp;the&nbsp;Gregorian</span></div></li>
<li><div class="src-line"><a name="a95"></a><span class="src-doc">&nbsp;*&nbsp;calendar&nbsp;until&nbsp;1752,&nbsp;when&nbsp;Wednesday&nbsp;2nd&nbsp;September&nbsp;in&nbsp;the&nbsp;Julian&nbsp;calendar&nbsp;dawned</span></div></li>
<li><div class="src-line"><a name="a96"></a><span class="src-doc">&nbsp;*&nbsp;as&nbsp;Thursday&nbsp;the&nbsp;14th&nbsp;in&nbsp;the&nbsp;Gregorian.</span></div></li>
<li><div class="src-line"><a name="a97"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a98"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Gregorian&nbsp;calendar&nbsp;is&nbsp;a&nbsp;minor&nbsp;correction&nbsp;to&nbsp;the&nbsp;Julian.&nbsp;In&nbsp;the&nbsp;Julian</span></div></li>
<li><div class="src-line"><a name="a99"></a><span class="src-doc">&nbsp;*&nbsp;calendar&nbsp;every&nbsp;fourth&nbsp;year&nbsp;is&nbsp;a&nbsp;leap&nbsp;year&nbsp;in&nbsp;which&nbsp;February&nbsp;has&nbsp;29,&nbsp;not&nbsp;28&nbsp;days,</span></div></li>
<li><div class="src-line"><a name="a100"></a><span class="src-doc">&nbsp;*&nbsp;but&nbsp;in&nbsp;the&nbsp;Gregorian,&nbsp;years&nbsp;divisible&nbsp;by&nbsp;100&nbsp;are&nbsp;not&nbsp;leap&nbsp;years&nbsp;unless&nbsp;they&nbsp;are</span></div></li>
<li><div class="src-line"><a name="a101"></a><span class="src-doc">&nbsp;*&nbsp;also&nbsp;divisible&nbsp;by&nbsp;400.&nbsp;How&nbsp;prescient&nbsp;was&nbsp;Pope&nbsp;Gregory!&nbsp;Whatever&nbsp;the&nbsp;problems&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a102"></a><span class="src-doc">&nbsp;*&nbsp;Y2K,&nbsp;they&nbsp;won't&nbsp;include&nbsp;sloppy&nbsp;programming&nbsp;which&nbsp;assumes&nbsp;every&nbsp;year&nbsp;divisible&nbsp;by</span></div></li>
<li><div class="src-line"><a name="a103"></a><span class="src-doc">&nbsp;*&nbsp;4&nbsp;is&nbsp;a&nbsp;leap&nbsp;year&nbsp;since&nbsp;2000,&nbsp;unlike&nbsp;the&nbsp;previous&nbsp;and&nbsp;subsequent&nbsp;years&nbsp;divisible</span></div></li>
<li><div class="src-line"><a name="a104"></a><span class="src-doc">&nbsp;*&nbsp;by&nbsp;100,&nbsp;is&nbsp;a&nbsp;leap&nbsp;year.&nbsp;As&nbsp;in&nbsp;the&nbsp;Julian&nbsp;calendar,&nbsp;days&nbsp;are&nbsp;considered&nbsp;to&nbsp;begin</span></div></li>
<li><div class="src-line"><a name="a105"></a><span class="src-doc">&nbsp;*&nbsp;at&nbsp;midnight.</span></div></li>
<li><div class="src-line"><a name="a106"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a107"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;average&nbsp;length&nbsp;of&nbsp;a&nbsp;year&nbsp;in&nbsp;the&nbsp;Gregorian&nbsp;calendar&nbsp;is&nbsp;365.2425&nbsp;days&nbsp;compared</span></div></li>
<li><div class="src-line"><a name="a108"></a><span class="src-doc">&nbsp;*&nbsp;to&nbsp;the&nbsp;actual&nbsp;solar&nbsp;tropical&nbsp;year&nbsp;(time&nbsp;from&nbsp;equinox&nbsp;to&nbsp;equinox)&nbsp;of&nbsp;365.24219878</span></div></li>
<li><div class="src-line"><a name="a109"></a><span class="src-doc">&nbsp;*&nbsp;days,&nbsp;so&nbsp;the&nbsp;calendar&nbsp;accumulates&nbsp;one&nbsp;day&nbsp;of&nbsp;error&nbsp;with&nbsp;respect&nbsp;to&nbsp;the&nbsp;solar&nbsp;year</span></div></li>
<li><div class="src-line"><a name="a110"></a><span class="src-doc">&nbsp;*&nbsp;about&nbsp;every&nbsp;3300&nbsp;years.&nbsp;As&nbsp;a&nbsp;purely&nbsp;solar&nbsp;calendar,&nbsp;no&nbsp;attempt&nbsp;is&nbsp;made&nbsp;to</span></div></li>
<li><div class="src-line"><a name="a111"></a><span class="src-doc">&nbsp;*&nbsp;synchronise&nbsp;the&nbsp;start&nbsp;of&nbsp;months&nbsp;to&nbsp;the&nbsp;phases&nbsp;of&nbsp;the&nbsp;Moon.</span></div></li>
<li><div class="src-line"><a name="a112"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a113"></a><span class="src-doc">&nbsp;*&nbsp;date&nbsp;--&nbsp;Format&nbsp;a&nbsp;local&nbsp;time/date</span></div></li>
<li><div class="src-line"><a name="a114"></a><span class="src-doc">&nbsp;*&nbsp;string&nbsp;date&nbsp;(&nbsp;string&nbsp;format,&nbsp;int&nbsp;timestamp);</span></div></li>
<li><div class="src-line"><a name="a115"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a116"></a><span class="src-doc">&nbsp;*&nbsp;Returns&nbsp;a&nbsp;string&nbsp;formatted&nbsp;according&nbsp;to&nbsp;the&nbsp;given&nbsp;format&nbsp;string&nbsp;using&nbsp;the&nbsp;given</span></div></li>
<li><div class="src-line"><a name="a117"></a><span class="src-doc">&nbsp;*&nbsp;integer&nbsp;timestamp&nbsp;or&nbsp;the&nbsp;current&nbsp;local&nbsp;time&nbsp;if&nbsp;no&nbsp;timestamp&nbsp;is&nbsp;given.&nbsp;In</span></div></li>
<li><div class="src-line"><a name="a118"></a><span class="src-doc">&nbsp;*&nbsp;otherwords,&nbsp;timestamp&nbsp;is&nbsp;optional&nbsp;and&nbsp;defaults&nbsp;to&nbsp;the&nbsp;value&nbsp;of&nbsp;time().</span></div></li>
<li><div class="src-line"><a name="a119"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a120"></a><span class="src-doc">&nbsp;*&nbsp;Example:</span></div></li>
<li><div class="src-line"><a name="a121"></a><span class="src-doc">&nbsp;*&nbsp;&lt;code&gt;</span></div></li>
<li><div class="src-line"><a name="a122"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;date_default_timezone_set('UTC');</span></div></li>
<li><div class="src-line"><a name="a123"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;$time&nbsp;=&nbsp;time();</span></div></li>
<li><div class="src-line"><a name="a124"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a125"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;date('l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A',&nbsp;$time);</span></div></li>
<li><div class="src-line"><a name="a126"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;';</span></div></li>
<li><div class="src-line"><a name="a127"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a128"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;include('./I18N/Arabic.php');</span></div></li>
<li><div class="src-line"><a name="a129"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;$obj&nbsp;=&nbsp;new&nbsp;I18N_Arabic('Date');</span></div></li>
<li><div class="src-line"><a name="a130"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a131"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;$obj-&gt;date('l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A',&nbsp;$time);</span></div></li>
<li><div class="src-line"><a name="a132"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;';</span></div></li>
<li><div class="src-line"><a name="a133"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a134"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;$obj-&gt;setMode(2);</span></div></li>
<li><div class="src-line"><a name="a135"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;$obj-&gt;date('l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A',&nbsp;$time);</span></div></li>
<li><div class="src-line"><a name="a136"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;';</span></div></li>
<li><div class="src-line"><a name="a137"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a138"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;$obj-&gt;setMode(3);</span></div></li>
<li><div class="src-line"><a name="a139"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;$obj-&gt;date('l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A',&nbsp;$time);</span></div></li>
<li><div class="src-line"><a name="a140"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;';</span></div></li>
<li><div class="src-line"><a name="a141"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a142"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;$obj-&gt;setMode(4);</span></div></li>
<li><div class="src-line"><a name="a143"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;$obj-&gt;date('l&nbsp;dS&nbsp;F&nbsp;Y&nbsp;h:i:s&nbsp;A',&nbsp;$time);</span></div></li>
<li><div class="src-line"><a name="a144"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/code&gt;</span></div></li>
<li><div class="src-line"><a name="a145"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a146"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a147"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a148"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a149"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a150"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a151"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a152"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a153"></a><span class="src-doc">&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a154"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a155"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a156"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;PHP&nbsp;class&nbsp;is&nbsp;an&nbsp;Arabic&nbsp;customization&nbsp;for&nbsp;PHP&nbsp;date&nbsp;function</span></div></li>
<li><div class="src-line"><a name="a157"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a158"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a159"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a160"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a161"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a162"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a163"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a164"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a165"></a><span class="src-doc">&nbsp;*/&nbsp;</span></div></li>
<li><div class="src-line"><a name="a166"></a><span class="src-key">class&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a></div></li>
<li><div class="src-line"><a name="a167"></a><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a168"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-var">$_mode&nbsp;</span>=&nbsp;<span class="src-num">1</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a169"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-var">$_xml&nbsp;&nbsp;</span>=&nbsp;<span class="src-id">null</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a170"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a171"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a172"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Loads&nbsp;initialize&nbsp;values</span></div></li>
<li><div class="src-line"><a name="a173"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a174"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a175"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a176"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><span class="src-id">__construct</span><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a177"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a178"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml&nbsp;</span>=&nbsp;<a href="http://www.php.net/simplexml_load_file">simplexml_load_file</a><span class="src-sym">(</span><a href="http://www.php.net/dirname">dirname</a><span class="src-sym">(</span>__FILE__<span class="src-sym">)</span>.<span class="src-str">'/data/ArDate.xml'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a179"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a180"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a181"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a182"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Setting&nbsp;value&nbsp;for&nbsp;$mode&nbsp;scalar</span></div></li>
<li><div class="src-line"><a name="a183"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a184"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$mode&nbsp;</span><span class="src-doc">Output&nbsp;mode&nbsp;of&nbsp;date&nbsp;function&nbsp;where:</span></div></li>
<li><div class="src-line"><a name="a185"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1)&nbsp;Hijri&nbsp;format&nbsp;(Islamic&nbsp;calendar)</span></div></li>
<li><div class="src-line"><a name="a186"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2)&nbsp;Arabic&nbsp;month&nbsp;names&nbsp;used&nbsp;in&nbsp;Middle&nbsp;East&nbsp;countries</span></div></li>
<li><div class="src-line"><a name="a187"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3)&nbsp;Arabic&nbsp;Transliteration&nbsp;of&nbsp;Gregorian&nbsp;month&nbsp;names</span></div></li>
<li><div class="src-line"><a name="a188"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4)&nbsp;Both&nbsp;of&nbsp;2&nbsp;and&nbsp;3&nbsp;formats&nbsp;together</span></div></li>
<li><div class="src-line"><a name="a189"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5)&nbsp;Libya&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a190"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;6)&nbsp;Algeria&nbsp;and&nbsp;Tunis&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a191"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;7)&nbsp;Morocco&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a192"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;8)&nbsp;Hijri&nbsp;format&nbsp;(Islamic&nbsp;calendar)&nbsp;in&nbsp;English</span></div></li>
<li><div class="src-line"><a name="a193"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a194"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a195"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a196"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a197"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodsetMode">setMode</a><span class="src-sym">(</span><span class="src-var">$mode&nbsp;</span>=&nbsp;<span class="src-num">1</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a198"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a199"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$mode&nbsp;</span>=&nbsp;(int)&nbsp;<span class="src-var">$mode</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a200"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a201"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$mode&nbsp;</span>&gt;&nbsp;<span class="src-num">0&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$mode&nbsp;</span>&lt;&nbsp;<span class="src-num">9</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a202"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>=&nbsp;<span class="src-var">$mode</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a203"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a204"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a205"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a206"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a207"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a208"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a209"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Getting&nbsp;$mode&nbsp;value&nbsp;that&nbsp;refer&nbsp;to&nbsp;output&nbsp;mode&nbsp;format</span></div></li>
<li><div class="src-line"><a name="a210"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1)&nbsp;Hijri&nbsp;format&nbsp;(Islamic&nbsp;calendar)</span></div></li>
<li><div class="src-line"><a name="a211"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2)&nbsp;Arabic&nbsp;month&nbsp;names&nbsp;used&nbsp;in&nbsp;Middle&nbsp;East&nbsp;countries</span></div></li>
<li><div class="src-line"><a name="a212"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3)&nbsp;Arabic&nbsp;Transliteration&nbsp;of&nbsp;Gregorian&nbsp;month&nbsp;names</span></div></li>
<li><div class="src-line"><a name="a213"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4)&nbsp;Both&nbsp;of&nbsp;2&nbsp;and&nbsp;3&nbsp;formats&nbsp;together</span></div></li>
<li><div class="src-line"><a name="a214"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5)&nbsp;Libyan&nbsp;way</span></div></li>
<li><div class="src-line"><a name="a215"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;6)&nbsp;Algeria&nbsp;and&nbsp;Tunis&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a216"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;7)&nbsp;Morocco&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a217"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;8)&nbsp;Hijri&nbsp;format&nbsp;(Islamic&nbsp;calendar)&nbsp;in&nbsp;English</span></div></li>
<li><div class="src-line"><a name="a218"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a219"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">Integer&nbsp;</span><span class="src-doc">Value&nbsp;of&nbsp;$mode&nbsp;properity</span></div></li>
<li><div class="src-line"><a name="a220"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a221"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a222"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodgetMode">getMode</a><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a223"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a224"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a225"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a226"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a227"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a228"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Format&nbsp;a&nbsp;local&nbsp;time/date&nbsp;in&nbsp;Arabic&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a229"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a230"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">&nbsp;</span><span class="src-doc-var">$format&nbsp;</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Format&nbsp;string&nbsp;(same&nbsp;as&nbsp;PHP&nbsp;date&nbsp;function)</span></div></li>
<li><div class="src-line"><a name="a231"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$timestamp&nbsp;</span><span class="src-doc">&nbsp;Unix&nbsp;timestamp</span></div></li>
<li><div class="src-line"><a name="a232"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$correction&nbsp;</span><span class="src-doc">To&nbsp;apply&nbsp;correction&nbsp;factor&nbsp;(+/-&nbsp;1-2)&nbsp;to</span></div></li>
<li><div class="src-line"><a name="a233"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;standard&nbsp;hijri&nbsp;calendar</span></div></li>
<li><div class="src-line"><a name="a234"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a235"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">Format&nbsp;Arabic&nbsp;date&nbsp;string&nbsp;according&nbsp;to&nbsp;given&nbsp;format&nbsp;string</span></div></li>
<li><div class="src-line"><a name="a236"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;the&nbsp;given&nbsp;integer&nbsp;timestamp&nbsp;or&nbsp;the&nbsp;current&nbsp;local</span></div></li>
<li><div class="src-line"><a name="a237"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;time&nbsp;if&nbsp;no&nbsp;timestamp&nbsp;is&nbsp;given.</span></div></li>
<li><div class="src-line"><a name="a238"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a239"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a240"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-var">$format</span><span class="src-sym">,&nbsp;</span><span class="src-var">$timestamp</span><span class="src-sym">,&nbsp;</span><span class="src-var">$correction&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a241"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a242"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">1&nbsp;</span>||&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">8</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a243"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">1</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a244"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">ar_hj_month</span><span class="src-sym">-&gt;</span><span class="src-id">month&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$month</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a245"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$hj_txt_month</span><span class="src-sym">[</span><span class="src-str">&quot;</span>{<span class="src-var">$month</span><span class="src-sym">[</span><span class="src-str">'id'</span><span class="src-sym">]<span class="src-str"></span><span class="src-sym">}</span></span><span class="src-str">&quot;</span><span class="src-sym">]&nbsp;</span>=&nbsp;(string)<span class="src-var">$month</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a246"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a247"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a248"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a249"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">8</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a250"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">en_hj_month</span><span class="src-sym">-&gt;</span><span class="src-id">month&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$month</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a251"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$hj_txt_month</span><span class="src-sym">[</span><span class="src-str">&quot;</span>{<span class="src-var">$month</span><span class="src-sym">[</span><span class="src-str">'id'</span><span class="src-sym">]<span class="src-str"></span><span class="src-sym">}</span></span><span class="src-str">&quot;</span><span class="src-sym">]&nbsp;</span>=&nbsp;(string)<span class="src-var">$month</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a252"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a253"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a254"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a255"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$patterns&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a256"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a257"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a258"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'Y'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a259"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x1'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a260"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'y'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a261"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x2'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a262"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'M'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a263"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x3'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a264"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'F'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a265"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x3'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a266"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'n'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a267"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x4'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a268"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'m'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a269"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x5'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a270"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'j'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a271"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x6'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a272"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'d'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a273"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x7'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a274"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a275"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">8</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a276"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'S'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a277"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">''</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a278"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a279"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a280"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$format&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$format</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a281"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a282"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-var">$format</span><span class="src-sym">,&nbsp;</span><span class="src-var">$timestamp</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a283"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">1</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a284"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoden2ar">en2ar</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a285"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a286"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a287"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$timestamp&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$timestamp&nbsp;</span>+&nbsp;<span class="src-num">3600</span>*<span class="src-num">24</span>*<span class="src-var">$correction</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a288"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;list<span class="src-sym">(</span><span class="src-var">$Y</span><span class="src-sym">,&nbsp;</span><span class="src-var">$M</span><span class="src-sym">,&nbsp;</span><span class="src-var">$D</span><span class="src-sym">)&nbsp;</span>=&nbsp;<a href="http://www.php.net/explode">explode</a><span class="src-sym">(</span><span class="src-str">'&nbsp;'</span><span class="src-sym">,&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-str">'Y&nbsp;m&nbsp;d'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$timestamp</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a289"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a290"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;list<span class="src-sym">(</span><span class="src-var">$hj_y</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_m</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_d</span><span class="src-sym">)&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodhjConvert">hjConvert</a><span class="src-sym">(</span><span class="src-var">$Y</span><span class="src-sym">,&nbsp;</span><span class="src-var">$M</span><span class="src-sym">,&nbsp;</span><span class="src-var">$D</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a291"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a292"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$patterns&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a293"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a294"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a295"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x1'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a296"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_y</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a297"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x2'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a298"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$hj_y</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">2</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a299"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x3'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a300"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_txt_month</span><span class="src-sym">[</span><span class="src-var">$hj_m</span><span class="src-sym">]</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a301"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x4'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a302"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_m</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a303"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x5'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a304"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/sprintf">sprintf</a><span class="src-sym">(</span><span class="src-str">'%02d'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_m</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a305"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x6'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a306"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_d</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a307"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'x7'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a308"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/sprintf">sprintf</a><span class="src-sym">(</span><span class="src-str">'%02d'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$hj_d</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a309"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a310"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a311"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">elseif&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">5</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a312"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$year&nbsp;&nbsp;</span>=&nbsp;<a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-str">'Y'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$timestamp</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a313"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$year&nbsp;</span>-=&nbsp;<span class="src-num">632</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a314"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$yr&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-str">&quot;</span><span class="src-str"><span class="src-var">$year</span></span><span class="src-str">&quot;</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a315"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a316"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$format&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-str">'Y'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$year</span><span class="src-sym">,&nbsp;</span><span class="src-var">$format</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a317"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$format&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-str">'y'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$yr</span><span class="src-sym">,&nbsp;</span><span class="src-var">$format</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a318"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a319"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-var">$format</span><span class="src-sym">,&nbsp;</span><span class="src-var">$timestamp</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a320"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoden2ar">en2ar</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a321"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a322"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a323"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-var">$format</span><span class="src-sym">,&nbsp;</span><span class="src-var">$timestamp</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a324"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoden2ar">en2ar</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a325"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a326"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a327"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-num">0</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a328"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$outputCharset&nbsp;</span>==&nbsp;<span class="src-id">null</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{&nbsp;</span></div></li>
<li><div class="src-line"><a name="a329"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$outputCharset&nbsp;</span>=&nbsp;<span class="src-var">$main</span><span class="src-sym">-&gt;</span><span class="src-id">getOutputCharset</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a330"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a331"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<span class="src-var">$main</span><span class="src-sym">-&gt;</span><span class="src-id">coreConvert</span><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">,&nbsp;</span><span class="src-str">'utf-8'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$outputCharset</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a332"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a333"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a334"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$str</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a335"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a336"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a337"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a338"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Translate&nbsp;English&nbsp;date/time&nbsp;terms&nbsp;into&nbsp;Arabic&nbsp;langauge</span></div></li>
<li><div class="src-line"><a name="a339"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a340"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$str&nbsp;</span><span class="src-doc">Date/time&nbsp;string&nbsp;using&nbsp;English&nbsp;terms</span></div></li>
<li><div class="src-line"><a name="a341"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a342"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">Date/time&nbsp;string&nbsp;using&nbsp;Arabic&nbsp;terms</span></div></li>
<li><div class="src-line"><a name="a343"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a344"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a345"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoden2ar">en2ar</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a346"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a347"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$patterns&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a348"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a349"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a350"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="http://www.php.net/strtolower">strtolower</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a351"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a352"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">xpath</span><span class="src-sym">(</span><span class="src-str">&quot;//en_day/mode[@id='full']/search&quot;</span><span class="src-sym">)&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$day</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a353"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$day</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a354"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a355"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a356"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">ar_day</span><span class="src-sym">-&gt;</span><span class="src-id">replace&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$day</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a357"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$day</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a358"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a359"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a360"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span></div></li>
<li><div class="src-line"><a name="a361"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">xpath</span><span class="src-sym">(</span><span class="src-str">&quot;//en_month/mode[@id='full']/search&quot;</span><span class="src-sym">)&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$month</span></div></li>
<li><div class="src-line"><a name="a362"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a363"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$month</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a364"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a365"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a366"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements&nbsp;</span>=&nbsp;<a href="http://www.php.net/array_merge">array_merge</a><span class="src-sym">(</span></div></li>
<li><div class="src-line"><a name="a367"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span></div></li>
<li><div class="src-line"><a name="a368"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodarabicMonths">arabicMonths</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a369"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a370"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a371"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">xpath</span><span class="src-sym">(</span><span class="src-str">&quot;//en_day/mode[@id='short']/search&quot;</span><span class="src-sym">)&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$day</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a372"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$day</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a373"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a374"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a375"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">ar_day</span><span class="src-sym">-&gt;</span><span class="src-id">replace&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$day</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a376"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$day</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a377"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a378"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a379"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">xpath</span><span class="src-sym">(</span><span class="src-str">&quot;//en_month/mode[@id='short']/search&quot;</span><span class="src-sym">)&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$m</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a380"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$m</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a381"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a382"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a383"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements&nbsp;</span>=&nbsp;<a href="http://www.php.net/array_merge">array_merge</a><span class="src-sym">(</span></div></li>
<li><div class="src-line"><a name="a384"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span></div></li>
<li><div class="src-line"><a name="a385"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodarabicMonths">arabicMonths</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a386"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a387"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a388"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span></div></li>
<li><div class="src-line"><a name="a389"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">xpath</span><span class="src-sym">(</span><span class="src-str">&quot;//preg_replace[@function='en2ar']/pair&quot;</span><span class="src-sym">)&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$p</span></div></li>
<li><div class="src-line"><a name="a390"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a391"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$p</span><span class="src-sym">-&gt;</span><span class="src-id">search</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a392"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$p</span><span class="src-sym">-&gt;</span><span class="src-id">replace</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a393"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a394"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a395"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a396"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a397"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$str</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a398"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a399"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a400"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a401"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Add&nbsp;Arabic&nbsp;month&nbsp;names&nbsp;to&nbsp;the&nbsp;replacement&nbsp;array</span></div></li>
<li><div class="src-line"><a name="a402"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a403"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$mode&nbsp;</span><span class="src-doc">Naming&nbsp;mode&nbsp;of&nbsp;months&nbsp;in&nbsp;Arabic&nbsp;where:</span></div></li>
<li><div class="src-line"><a name="a404"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2)&nbsp;Arabic&nbsp;month&nbsp;names&nbsp;used&nbsp;in&nbsp;Middle&nbsp;East&nbsp;countries</span></div></li>
<li><div class="src-line"><a name="a405"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3)&nbsp;Arabic&nbsp;Transliteration&nbsp;of&nbsp;Gregorian&nbsp;month&nbsp;names</span></div></li>
<li><div class="src-line"><a name="a406"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4)&nbsp;Both&nbsp;of&nbsp;2&nbsp;and&nbsp;3&nbsp;formats&nbsp;together</span></div></li>
<li><div class="src-line"><a name="a407"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5)&nbsp;Libya&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a408"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;6)&nbsp;Algeria&nbsp;and&nbsp;Tunis&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a409"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;7)&nbsp;Morocco&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a410"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a411"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">array&nbsp;</span><span class="src-doc">Arabic&nbsp;month&nbsp;names&nbsp;in&nbsp;selected&nbsp;style</span></div></li>
<li><div class="src-line"><a name="a412"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a413"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a414"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodarabicMonths">arabicMonths</a><span class="src-sym">(</span><span class="src-var">$mode</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a415"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a416"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a417"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a418"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span></div></li>
<li><div class="src-line"><a name="a419"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_xml</span><span class="src-sym">-&gt;</span><span class="src-id">xpath</span><span class="src-sym">(</span><span class="src-str">&quot;</span><span class="src-str">//ar_month/mode[@id=<span class="src-var">$mode</span>]/replace</span><span class="src-str">&quot;</span><span class="src-sym">)&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$month</span></div></li>
<li><div class="src-line"><a name="a420"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a421"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$month</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a422"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span></div></li>
<li><div class="src-line"><a name="a423"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a424"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$replacements</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a425"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a426"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a427"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a428"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;given&nbsp;Gregorian&nbsp;date&nbsp;into&nbsp;Hijri&nbsp;date</span></div></li>
<li><div class="src-line"><a name="a429"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a430"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$Y&nbsp;</span><span class="src-doc">Year&nbsp;Gregorian&nbsp;year</span></div></li>
<li><div class="src-line"><a name="a431"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$M&nbsp;</span><span class="src-doc">Month&nbsp;Gregorian&nbsp;month</span></div></li>
<li><div class="src-line"><a name="a432"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$D&nbsp;</span><span class="src-doc">Day&nbsp;Gregorian&nbsp;day</span></div></li>
<li><div class="src-line"><a name="a433"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a434"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">array&nbsp;</span><span class="src-doc">Hijri&nbsp;date&nbsp;[int&nbsp;Year,&nbsp;int&nbsp;Month,&nbsp;int&nbsp;Day](Islamic&nbsp;calendar)</span></div></li>
<li><div class="src-line"><a name="a435"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a436"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a437"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodhjConvert">hjConvert</a><span class="src-sym">(</span><span class="src-var">$Y</span><span class="src-sym">,&nbsp;</span><span class="src-var">$M</span><span class="src-sym">,&nbsp;</span><span class="src-var">$D</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a438"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a439"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/function_exists">function_exists</a><span class="src-sym">(</span><span class="src-str">'GregorianToJD'</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a440"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$jd&nbsp;</span>=&nbsp;<span class="src-id">GregorianToJD</span><span class="src-sym">(</span><span class="src-var">$M</span><span class="src-sym">,&nbsp;</span><span class="src-var">$D</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Y</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a441"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a442"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$jd&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodgregToJd">gregToJd</a><span class="src-sym">(</span><span class="src-var">$M</span><span class="src-sym">,&nbsp;</span><span class="src-var">$D</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Y</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a443"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a444"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a445"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;list<span class="src-sym">(</span><span class="src-var">$year</span><span class="src-sym">,&nbsp;</span><span class="src-var">$month</span><span class="src-sym">,&nbsp;</span><span class="src-var">$day</span><span class="src-sym">)&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodjdToIslamic">jdToIslamic</a><span class="src-sym">(</span><span class="src-var">$jd</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a446"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a447"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-key">array</span><span class="src-sym">(</span><span class="src-var">$year</span><span class="src-sym">,&nbsp;</span><span class="src-var">$month</span><span class="src-sym">,&nbsp;</span><span class="src-var">$day</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a448"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a449"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a450"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a451"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;given&nbsp;Julian&nbsp;day&nbsp;into&nbsp;Hijri&nbsp;date</span></div></li>
<li><div class="src-line"><a name="a452"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a453"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$jd&nbsp;</span><span class="src-doc">Julian&nbsp;day</span></div></li>
<li><div class="src-line"><a name="a454"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a455"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">array&nbsp;</span><span class="src-doc">Hijri&nbsp;date&nbsp;[int&nbsp;Year,&nbsp;int&nbsp;Month,&nbsp;int&nbsp;Day](Islamic&nbsp;calendar)</span></div></li>
<li><div class="src-line"><a name="a456"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a457"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a458"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodjdToIslamic">jdToIslamic</a><span class="src-sym">(</span><span class="src-var">$jd</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a459"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a460"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$l&nbsp;</span>=&nbsp;(int)<span class="src-var">$jd&nbsp;</span>-&nbsp;<span class="src-num">1948440&nbsp;</span>+&nbsp;<span class="src-num">10632</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a461"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$n&nbsp;</span>=&nbsp;(int)<span class="src-sym">((</span><span class="src-var">$l&nbsp;</span>-&nbsp;<span class="src-num">1</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">10631</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a462"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$l&nbsp;</span>=&nbsp;<span class="src-var">$l&nbsp;</span>-&nbsp;<span class="src-num">10631&nbsp;</span>*&nbsp;<span class="src-var">$n&nbsp;</span>+&nbsp;<span class="src-num">354</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a463"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$j&nbsp;</span>=&nbsp;(int)<span class="src-sym">((</span><span class="src-num">10985&nbsp;</span>-&nbsp;<span class="src-var">$l</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">5316</span><span class="src-sym">)&nbsp;</span>*&nbsp;(int)<span class="src-sym">((</span><span class="src-num">50&nbsp;</span>*&nbsp;<span class="src-var">$l</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">17719</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a464"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+&nbsp;(int)<span class="src-sym">(</span><span class="src-var">$l&nbsp;</span>/&nbsp;<span class="src-num">5670</span><span class="src-sym">)&nbsp;</span>*&nbsp;(int)<span class="src-sym">((</span><span class="src-num">43&nbsp;</span>*&nbsp;<span class="src-var">$l</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">15238</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a465"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$l&nbsp;</span>=&nbsp;<span class="src-var">$l&nbsp;</span>-&nbsp;(int)<span class="src-sym">((</span><span class="src-num">30&nbsp;</span>-&nbsp;<span class="src-var">$j</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>*&nbsp;(int)<span class="src-sym">((</span><span class="src-num">17719&nbsp;</span>*&nbsp;<span class="src-var">$j</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">50</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a466"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;(int)<span class="src-sym">(</span><span class="src-var">$j&nbsp;</span>/&nbsp;<span class="src-num">16</span><span class="src-sym">)&nbsp;</span>*&nbsp;(int)<span class="src-sym">((</span><span class="src-num">15238&nbsp;</span>*&nbsp;<span class="src-var">$j</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">43</span><span class="src-sym">)&nbsp;</span>+&nbsp;<span class="src-num">29</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a467"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$m&nbsp;</span>=&nbsp;(int)<span class="src-sym">((</span><span class="src-num">24&nbsp;</span>*&nbsp;<span class="src-var">$l</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">709</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a468"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$d&nbsp;</span>=&nbsp;<span class="src-var">$l&nbsp;</span>-&nbsp;(int)<span class="src-sym">((</span><span class="src-num">709&nbsp;</span>*&nbsp;<span class="src-var">$m</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">24</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a469"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$y&nbsp;</span>=&nbsp;(int)<span class="src-sym">(</span><span class="src-num">30&nbsp;</span>*&nbsp;<span class="src-var">$n&nbsp;</span>+&nbsp;<span class="src-var">$j&nbsp;</span>-&nbsp;<span class="src-num">30</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a470"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a471"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-key">array</span><span class="src-sym">(</span><span class="src-var">$y</span><span class="src-sym">,&nbsp;</span><span class="src-var">$m</span><span class="src-sym">,&nbsp;</span><span class="src-var">$d</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a472"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a473"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a474"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a475"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;given&nbsp;Hijri&nbsp;date&nbsp;into&nbsp;Julian&nbsp;day</span></div></li>
<li><div class="src-line"><a name="a476"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a477"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$year&nbsp;</span><span class="src-doc">&nbsp;Year&nbsp;Hijri&nbsp;year</span></div></li>
<li><div class="src-line"><a name="a478"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$month&nbsp;</span><span class="src-doc">Month&nbsp;Hijri&nbsp;month</span></div></li>
<li><div class="src-line"><a name="a479"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$day&nbsp;</span><span class="src-doc">&nbsp;&nbsp;Day&nbsp;Hijri&nbsp;day</span></div></li>
<li><div class="src-line"><a name="a480"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a481"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc">Julian&nbsp;day</span></div></li>
<li><div class="src-line"><a name="a482"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a483"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a484"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodislamicToJd">islamicToJd</a><span class="src-sym">(</span><span class="src-var">$year</span><span class="src-sym">,&nbsp;</span><span class="src-var">$month</span><span class="src-sym">,&nbsp;</span><span class="src-var">$day</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a485"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a486"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$jd&nbsp;</span>=&nbsp;(int)<span class="src-sym">((</span><span class="src-num">11&nbsp;</span>*&nbsp;<span class="src-var">$year&nbsp;</span>+&nbsp;<span class="src-num">3</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">30</span><span class="src-sym">)&nbsp;</span>+&nbsp;(int)<span class="src-sym">(</span><span class="src-num">354&nbsp;</span>*&nbsp;<span class="src-var">$year</span><span class="src-sym">)&nbsp;</span>+&nbsp;(int)<span class="src-sym">(</span><span class="src-num">30&nbsp;</span>*&nbsp;<span class="src-var">$month</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a487"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;(int)<span class="src-sym">((</span><span class="src-var">$month&nbsp;</span>-&nbsp;<span class="src-num">1</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-num">2</span><span class="src-sym">)&nbsp;</span>+&nbsp;<span class="src-var">$day&nbsp;</span>+&nbsp;<span class="src-num">1948440&nbsp;</span>-&nbsp;<span class="src-num">385</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a488"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$jd</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a489"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a490"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a491"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a492"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Converts&nbsp;a&nbsp;Gregorian&nbsp;date&nbsp;to&nbsp;Julian&nbsp;Day&nbsp;Count</span></div></li>
<li><div class="src-line"><a name="a493"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a494"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$m&nbsp;</span><span class="src-doc">The&nbsp;month&nbsp;as&nbsp;a&nbsp;number&nbsp;from&nbsp;1&nbsp;(for&nbsp;January)</span></div></li>
<li><div class="src-line"><a name="a495"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;to&nbsp;12&nbsp;(for&nbsp;December)</span></div></li>
<li><div class="src-line"><a name="a496"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$d&nbsp;</span><span class="src-doc">The&nbsp;day&nbsp;as&nbsp;a&nbsp;number&nbsp;from&nbsp;1&nbsp;to&nbsp;31</span></div></li>
<li><div class="src-line"><a name="a497"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$y&nbsp;</span><span class="src-doc">The&nbsp;year&nbsp;as&nbsp;a&nbsp;number&nbsp;between&nbsp;-4714&nbsp;and&nbsp;9999</span></div></li>
<li><div class="src-line"><a name="a498"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a499"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc">The&nbsp;julian&nbsp;day&nbsp;for&nbsp;the&nbsp;given&nbsp;gregorian&nbsp;date&nbsp;as&nbsp;an&nbsp;integer</span></div></li>
<li><div class="src-line"><a name="a500"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a501"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a502"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methodgregToJd">gregToJd</a>&nbsp;<span class="src-sym">(</span><span class="src-var">$m</span><span class="src-sym">,&nbsp;</span><span class="src-var">$d</span><span class="src-sym">,&nbsp;</span><span class="src-var">$y</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a503"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a504"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$m&nbsp;</span>&lt;&nbsp;<span class="src-num">3</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a505"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$y</span>--<span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a506"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$m&nbsp;</span>+=&nbsp;<span class="src-num">12</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a507"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a508"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a509"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">((</span><span class="src-var">$y&nbsp;</span>&lt;&nbsp;<span class="src-num">1582</span><span class="src-sym">)&nbsp;</span>||&nbsp;<span class="src-sym">(</span><span class="src-var">$y&nbsp;</span>==&nbsp;<span class="src-num">1582&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$m&nbsp;</span>&lt;&nbsp;<span class="src-num">10</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a510"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;||&nbsp;<span class="src-sym">(</span><span class="src-var">$y&nbsp;</span>==&nbsp;<span class="src-num">1582&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$m&nbsp;</span>==&nbsp;<span class="src-num">10&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$d&nbsp;</span>&lt;=&nbsp;<span class="src-num">15</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a511"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a512"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;This&nbsp;is&nbsp;ignored&nbsp;in&nbsp;the&nbsp;GregorianToJD&nbsp;PHP&nbsp;function!</span></div></li>
<li><div class="src-line"><a name="a513"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$b&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a514"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a515"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$a&nbsp;</span>=&nbsp;(int)<span class="src-sym">(</span><span class="src-var">$y&nbsp;</span>/&nbsp;<span class="src-num">100</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a516"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$b&nbsp;</span>=&nbsp;<span class="src-num">2&nbsp;</span>-&nbsp;<span class="src-var">$a&nbsp;</span>+&nbsp;(int)<span class="src-sym">(</span><span class="src-var">$a&nbsp;</span>/&nbsp;<span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a517"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a518"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a519"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$jd&nbsp;</span>=&nbsp;(int)<span class="src-sym">(</span><span class="src-num">365.25&nbsp;</span>*&nbsp;<span class="src-sym">(</span><span class="src-var">$y&nbsp;</span>+&nbsp;<span class="src-num">4716</span><span class="src-sym">))&nbsp;</span>+&nbsp;(int)<span class="src-sym">(</span><span class="src-num">30.6001&nbsp;</span>*&nbsp;<span class="src-sym">(</span><span class="src-var">$m&nbsp;</span>+&nbsp;<span class="src-num">1</span><span class="src-sym">))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a520"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+&nbsp;<span class="src-var">$d&nbsp;</span>+&nbsp;<span class="src-var">$b&nbsp;</span>-&nbsp;<span class="src-num">1524.5</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a521"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a522"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><a href="http://www.php.net/round">round</a><span class="src-sym">(</span><span class="src-var">$jd</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a523"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a524"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a525"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a526"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Calculate&nbsp;Hijri&nbsp;calendar&nbsp;correction&nbsp;using&nbsp;Um-Al-Qura&nbsp;calendar&nbsp;information</span></div></li>
<li><div class="src-line"><a name="a527"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a528"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$time&nbsp;</span><span class="src-doc">Unix&nbsp;timestamp</span></div></li>
<li><div class="src-line"><a name="a529"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a530"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc">Correction&nbsp;factor&nbsp;to&nbsp;fix&nbsp;Hijri&nbsp;calendar&nbsp;calculation&nbsp;using</span></div></li>
<li><div class="src-line"><a name="a531"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Um-Al-Qura&nbsp;calendar&nbsp;information</span></div></li>
<li><div class="src-line"><a name="a532"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a533"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a534"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddateCorrection">dateCorrection</a>&nbsp;<span class="src-sym">(</span><span class="src-var">$time</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a535"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a536"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$calc&nbsp;</span>=&nbsp;<span class="src-var">$time&nbsp;</span>-&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-str">'j'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)&nbsp;</span>*&nbsp;<span class="src-num">3600&nbsp;</span>*&nbsp;<span class="src-num">24</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a537"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a538"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$file&nbsp;</span>=&nbsp;<a href="http://www.php.net/dirname">dirname</a><span class="src-sym">(</span>__FILE__<span class="src-sym">)</span>.<span class="src-str">'/data/um_alqoura.txt'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a539"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a540"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$content&nbsp;</span>=&nbsp;<a href="http://www.php.net/file_get_contents">file_get_contents</a><span class="src-sym">(</span><span class="src-var">$file</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a541"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a542"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$y&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-str">'Y'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a543"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$m&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Date.html#methoddate">date</a><span class="src-sym">(</span><span class="src-str">'n'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a544"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$offset&nbsp;</span>=&nbsp;<span class="src-sym">((</span><span class="src-var">$y</span>-<span class="src-num">1420</span><span class="src-sym">)&nbsp;</span>*&nbsp;<span class="src-num">12&nbsp;</span>+&nbsp;<span class="src-var">$m</span><span class="src-sym">)&nbsp;</span>*&nbsp;<span class="src-num">11</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a545"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a546"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$d&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$content</span><span class="src-sym">,&nbsp;</span><span class="src-var">$offset</span><span class="src-sym">,&nbsp;</span><span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a547"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$m&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$content</span><span class="src-sym">,&nbsp;</span><span class="src-var">$offset</span>+<span class="src-num">3</span><span class="src-sym">,&nbsp;</span><span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a548"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$y&nbsp;</span>=&nbsp;<a href="http://www.php.net/substr">substr</a><span class="src-sym">(</span><span class="src-var">$content</span><span class="src-sym">,&nbsp;</span><span class="src-var">$offset</span>+<span class="src-num">6</span><span class="src-sym">,&nbsp;</span><span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a549"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a550"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$real&nbsp;</span>=&nbsp;<a href="http://www.php.net/mktime">mktime</a><span class="src-sym">(</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-var">$m</span><span class="src-sym">,&nbsp;</span><span class="src-var">$d</span><span class="src-sym">,&nbsp;</span><span class="src-var">$y</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a551"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a552"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$diff&nbsp;</span>=&nbsp;(int)<span class="src-sym">((</span><span class="src-var">$calc&nbsp;</span>-&nbsp;<span class="src-var">$real</span><span class="src-sym">)&nbsp;</span>/&nbsp;<span class="src-sym">(</span><span class="src-num">3600&nbsp;</span>*&nbsp;<span class="src-num">24</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a553"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a554"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$diff</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a555"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a556"></a><span class="src-sym">}</span></div></li>
</ol>
</div>
        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:57 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>