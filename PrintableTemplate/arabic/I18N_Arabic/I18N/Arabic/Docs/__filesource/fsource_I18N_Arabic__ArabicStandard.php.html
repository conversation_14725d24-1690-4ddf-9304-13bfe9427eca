<html>
<head>
<title>File Source for Standard.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                  
      
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1 align="center">Source for file Standard.php</h1>
<p>Documentation is available at <a href="../I18N_Arabic/_Arabic---Standard.php.html">Standard.php</a></p>
<div class="src-code">
<ol><li><div class="src-line"><a name="a1"></a><span class="src-php">&lt;?php</span></div></li>
<li><div class="src-line"><a name="a2"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a3"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a4"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a5"></a><span class="src-doc">&nbsp;*&nbsp;Copyright&nbsp;(c)&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa.</span></div></li>
<li><div class="src-line"><a name="a6"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a7"></a><span class="src-doc">&nbsp;*&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a8"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a9"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;Version&nbsp;5</span></div></li>
<li><div class="src-line"><a name="a10"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a11"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a12"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a13"></a><span class="src-doc">&nbsp;*&nbsp;LICENSE</span></div></li>
<li><div class="src-line"><a name="a14"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a15"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;open&nbsp;source&nbsp;product;&nbsp;you&nbsp;can&nbsp;redistribute&nbsp;it&nbsp;and/or</span></div></li>
<li><div class="src-line"><a name="a16"></a><span class="src-doc">&nbsp;*&nbsp;modify&nbsp;it&nbsp;under&nbsp;the&nbsp;terms&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;(LGPL)</span></div></li>
<li><div class="src-line"><a name="a17"></a><span class="src-doc">&nbsp;*&nbsp;as&nbsp;published&nbsp;by&nbsp;the&nbsp;Free&nbsp;Software&nbsp;Foundation;&nbsp;either&nbsp;version&nbsp;3</span></div></li>
<li><div class="src-line"><a name="a18"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;the&nbsp;License,&nbsp;or&nbsp;(at&nbsp;your&nbsp;option)&nbsp;any&nbsp;later&nbsp;version.</span></div></li>
<li><div class="src-line"><a name="a19"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a20"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;distributed&nbsp;in&nbsp;the&nbsp;hope&nbsp;that&nbsp;it&nbsp;will&nbsp;be&nbsp;useful,</span></div></li>
<li><div class="src-line"><a name="a21"></a><span class="src-doc">&nbsp;*&nbsp;but&nbsp;WITHOUT&nbsp;ANY&nbsp;WARRANTY;&nbsp;without&nbsp;even&nbsp;the&nbsp;implied&nbsp;warranty&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a22"></a><span class="src-doc">&nbsp;*&nbsp;MERCHANTABILITY&nbsp;or&nbsp;FITNESS&nbsp;FOR&nbsp;A&nbsp;PARTICULAR&nbsp;PURPOSE.&nbsp;&nbsp;See&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a23"></a><span class="src-doc">&nbsp;*&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;for&nbsp;more&nbsp;details.</span></div></li>
<li><div class="src-line"><a name="a24"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a25"></a><span class="src-doc">&nbsp;*&nbsp;You&nbsp;should&nbsp;have&nbsp;received&nbsp;a&nbsp;copy&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License</span></div></li>
<li><div class="src-line"><a name="a26"></a><span class="src-doc">&nbsp;*&nbsp;along&nbsp;with&nbsp;this&nbsp;program.&nbsp;&nbsp;If&nbsp;not,&nbsp;see&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</span></div></li>
<li><div class="src-line"><a name="a27"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a28"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a29"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a30"></a><span class="src-doc">&nbsp;*&nbsp;Class&nbsp;Name:&nbsp;Arabic&nbsp;Text&nbsp;ArStandard&nbsp;Class</span></div></li>
<li><div class="src-line"><a name="a31"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a32"></a><span class="src-doc">&nbsp;*&nbsp;Filename:&nbsp;Standard.php</span></div></li>
<li><div class="src-line"><a name="a33"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a34"></a><span class="src-doc">&nbsp;*&nbsp;Original&nbsp;&nbsp;Author(s):&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a35"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a36"></a><span class="src-doc">&nbsp;*&nbsp;Purpose:&nbsp;&nbsp;Standardize&nbsp;Arabic&nbsp;text</span></div></li>
<li><div class="src-line"><a name="a37"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a38"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a39"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a40"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;Text&nbsp;Standardize&nbsp;Class</span></div></li>
<li><div class="src-line"><a name="a41"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a42"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;class&nbsp;to&nbsp;standardize&nbsp;Arabic&nbsp;text&nbsp;just&nbsp;like&nbsp;rules&nbsp;followed&nbsp;in&nbsp;magazines</span></div></li>
<li><div class="src-line"><a name="a43"></a><span class="src-doc">&nbsp;*&nbsp;and&nbsp;newspapers&nbsp;like&nbsp;spaces&nbsp;before&nbsp;and&nbsp;after&nbsp;punctuations,&nbsp;brackets&nbsp;and</span></div></li>
<li><div class="src-line"><a name="a44"></a><span class="src-doc">&nbsp;*&nbsp;units&nbsp;etc&nbsp;...</span></div></li>
<li><div class="src-line"><a name="a45"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a46"></a><span class="src-doc">&nbsp;*&nbsp;Example:</span></div></li>
<li><div class="src-line"><a name="a47"></a><span class="src-doc">&nbsp;*&nbsp;&lt;code&gt;</span></div></li>
<li><div class="src-line"><a name="a48"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;include('./I18N/Arabic.php');</span></div></li>
<li><div class="src-line"><a name="a49"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj&nbsp;=&nbsp;new&nbsp;I18N_Arabic('Standard');</span></div></li>
<li><div class="src-line"><a name="a50"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a51"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$str&nbsp;=&nbsp;$obj-&gt;standard($content);</span></div></li>
<li><div class="src-line"><a name="a52"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a53"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;$str;</span></div></li>
<li><div class="src-line"><a name="a54"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/code&gt;</span></div></li>
<li><div class="src-line"><a name="a55"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a56"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a57"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a58"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a59"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a60"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a61"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a62"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a63"></a><span class="src-doc">&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a64"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a65"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a66"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;PHP&nbsp;class&nbsp;standardize&nbsp;Arabic&nbsp;text</span></div></li>
<li><div class="src-line"><a name="a67"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a68"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a69"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a70"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a71"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a72"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a73"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a74"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a75"></a><span class="src-doc">&nbsp;*/&nbsp;</span></div></li>
<li><div class="src-line"><a name="a76"></a><span class="src-key">class&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a></div></li>
<li><div class="src-line"><a name="a77"></a><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a78"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a79"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Loads&nbsp;initialize&nbsp;values</span></div></li>
<li><div class="src-line"><a name="a80"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a81"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a82"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a83"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><span class="src-id">__construct</span><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a84"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a85"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a86"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a87"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a88"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;This&nbsp;method&nbsp;will&nbsp;standardize&nbsp;Arabic&nbsp;text&nbsp;to&nbsp;follow&nbsp;writing&nbsp;standards</span></div></li>
<li><div class="src-line"><a name="a89"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;(just&nbsp;like&nbsp;magazine&nbsp;rules)</span></div></li>
<li><div class="src-line"><a name="a90"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a91"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$text&nbsp;</span><span class="src-doc">Arabic&nbsp;text&nbsp;you&nbsp;would&nbsp;like&nbsp;to&nbsp;standardize</span></div></li>
<li><div class="src-line"><a name="a92"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a93"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">String&nbsp;</span><span class="src-doc">Standardized&nbsp;version&nbsp;of&nbsp;input&nbsp;Arabic&nbsp;text</span></div></li>
<li><div class="src-line"><a name="a94"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a95"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a96"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Standard.html#methodstandard">standard</a><span class="src-sym">(</span><span class="src-var">$text</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a97"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a98"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$patterns&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a99"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replacements&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a100"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a101"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\r\n/u'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/([^\@])\n([^\@])/u'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\r/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a102"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\n@@@\n&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\\1\n&amp;&amp;&amp;\n\\2&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\n###\n&quot;</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a103"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a104"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a105"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;النقطة،&nbsp;الفاصلة،&nbsp;الفاصلة&nbsp;المنقوطة،</div></li>
<li><div class="src-line"><a name="a106"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;النقطتان،&nbsp;علامتي&nbsp;الاستفهام&nbsp;والتعجب،</div></li>
<li><div class="src-line"><a name="a107"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;النقاط&nbsp;الثلاث&nbsp;المتتالية</div></li>
<li><div class="src-line"><a name="a108"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;يترك&nbsp;فراغ&nbsp;واحد&nbsp;بعدها&nbsp;جميعا</div></li>
<li><div class="src-line"><a name="a109"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;دون&nbsp;أي&nbsp;فراغ&nbsp;قبلها</div></li>
<li><div class="src-line"><a name="a110"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a111"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s*([\.\،\؛\:\!\؟])\s*/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a112"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'\\1&nbsp;'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a113"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a114"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a115"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;النقاط&nbsp;المتتالية&nbsp;عددها&nbsp;3&nbsp;فقط</div></li>
<li><div class="src-line"><a name="a116"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;(ليست&nbsp;نقطتان&nbsp;وليست&nbsp;أربع&nbsp;أو&nbsp;أكثر)</div></li>
<li><div class="src-line"><a name="a117"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a118"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/(\.&nbsp;){2,}/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a119"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'...'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a120"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a121"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a122"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;الأقواس&nbsp;(&nbsp;)&nbsp;[&nbsp;]&nbsp;{&nbsp;}&nbsp;يترك&nbsp;قبلها&nbsp;وبعدها&nbsp;فراغ</div></li>
<li><div class="src-line"><a name="a123"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;وحيد،&nbsp;فيما&nbsp;لا&nbsp;يوجد&nbsp;بينها&nbsp;وبين&nbsp;ما&nbsp;بداخلها</div></li>
<li><div class="src-line"><a name="a124"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;أي&nbsp;فراغ</div></li>
<li><div class="src-line"><a name="a125"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a126"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s*([\(\{\[])\s*/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a127"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;\\1'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a128"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a129"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s*([\)\}\]])\s*/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a130"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'\\1&nbsp;'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a131"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a132"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a133"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;علامات&nbsp;الاقتباس&nbsp;&quot;...&quot;</div></li>
<li><div class="src-line"><a name="a134"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;يترك&nbsp;قبلها&nbsp;وبعدها&nbsp;فراغ</div></li>
<li><div class="src-line"><a name="a135"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;وحيد،&nbsp;فيما&nbsp;لا&nbsp;يوجد&nbsp;بينها</div></li>
<li><div class="src-line"><a name="a136"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;وبين&nbsp;ما&nbsp;بداخلها&nbsp;أي&nbsp;فراغ</div></li>
<li><div class="src-line"><a name="a137"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a138"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s*\&quot;\s*(.+)((?&lt;!\s)\&quot;|\s+\&quot;)\s*/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a139"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;&quot;\\1&quot;&nbsp;'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a140"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a141"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a142"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;علامات&nbsp;الإعتراض&nbsp;-...-</div></li>
<li><div class="src-line"><a name="a143"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;يترك&nbsp;قبلها&nbsp;وبعدها&nbsp;فراغ</div></li>
<li><div class="src-line"><a name="a144"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;وحيد،&nbsp;فيما&nbsp;لا&nbsp;يوجد&nbsp;بينها</div></li>
<li><div class="src-line"><a name="a145"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;وبين&nbsp;ما&nbsp;بداخلها&nbsp;أي&nbsp;فراغ</div></li>
<li><div class="src-line"><a name="a146"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a147"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s*\-\s*(.+)((?&lt;!\s)\-|\s+\-)\s*/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a148"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;-\\1-&nbsp;'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a149"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a150"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a151"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;لا&nbsp;يترك&nbsp;فراغ&nbsp;بين&nbsp;حرف&nbsp;العطف&nbsp;الواو&nbsp;وبين</div></li>
<li><div class="src-line"><a name="a152"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;الكلمة&nbsp;التي&nbsp;تليه</div></li>
<li><div class="src-line"><a name="a153"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;إلا&nbsp;إن&nbsp;كانت&nbsp;تبدأ&nbsp;بحرف&nbsp;الواو</div></li>
<li><div class="src-line"><a name="a154"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a155"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\sو\s+([^و])/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a156"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;و\\1'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a157"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a158"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a159"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;الواحدات&nbsp;الإنجليزية&nbsp;توضع</div></li>
<li><div class="src-line"><a name="a160"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;على&nbsp;يمين&nbsp;الرقم&nbsp;مع&nbsp;ترك&nbsp;فراغ</div></li>
<li><div class="src-line"><a name="a161"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a162"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s+(\w+)\s*(\d+)\s+/'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a163"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;&lt;span&nbsp;dir=&quot;ltr&quot;&gt;\\2&nbsp;\\1&lt;/span&gt;&nbsp;'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a164"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a165"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s+(\d+)\s*(\w+)\s+/'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a166"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;&lt;span&nbsp;dir=&quot;ltr&quot;&gt;\\1&nbsp;\\2&lt;/span&gt;&nbsp;'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a167"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a168"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/**</div></li>
<li><div class="src-line"><a name="a169"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;النسبة&nbsp;المؤية&nbsp;دائما&nbsp;إلى&nbsp;يسار&nbsp;الرقم</div></li>
<li><div class="src-line"><a name="a170"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;وبدون&nbsp;أي&nbsp;فراغ&nbsp;يفصل&nbsp;بينهما&nbsp;40%&nbsp;مثلا</div></li>
<li><div class="src-line"><a name="a171"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</div></li>
<li><div class="src-line"><a name="a172"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\s+(\d+)\s*\%\s+/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a173"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&nbsp;%\\1&nbsp;'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a174"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a175"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\n?@@@\n?/u'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\n?&amp;&amp;&amp;\n?/u'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'/\n?###\n?/u'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a176"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\r\n&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\n&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\r&quot;</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a177"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a178"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$text&nbsp;</span>=&nbsp;<a href="http://www.php.net/preg_replace">preg_replace</a><span class="src-sym">(</span><span class="src-var">$patterns</span><span class="src-sym">,&nbsp;</span><span class="src-var">$replacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$text</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a179"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a180"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$text</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a181"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a182"></a><span class="src-sym">}</span></div></li>
</ol>
</div>
        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:26:25 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>