<?php

class Tafket
{

    public $arabicTafketObjArabic;
    public $arabicTafketObjEnglish;

    public function __construct()
    {
        include(__DIR__ . '/I18N_Arabic/I18N/Arabic.php');
        include(__DIR__ . '/ConvertNumberToEnglishWord.php');

        $this->arabicTafketObjArabic = new I18N_Arabic('Numbers');
        $this->arabicTafketObjEnglish = new ConvertNumberToEnglishWord();
    }


    function convert($number, $lang = 'ar', $currencyCode = 'EGP')
    {
        if ('ar' == $lang) {
            $number2Str = $this->arabicTafketObjArabic->int2str($number, $currencyCode);
            return $this->appendCurrencyToArabic($number2Str, $currencyCode);
        }
        return $this->arabicTafketObjEnglish->int2str($number, $currencyCode);
    }

    function appendCurrencyToArabic($string, $currencyCode)
    {
        if (mb_strpos($string, 'فاصلة')) {
            $name = get_currency_symbol($currencyCode, 'ar', 'name') . ' و ';
            return str_replace('فاصلة', $name, $string) . ' ' . get_currency_symbol($currencyCode, 'ar', 'minor');
        }
        return $string .' '. get_currency_symbol($currencyCode, 'ar', 'name');
    }
}