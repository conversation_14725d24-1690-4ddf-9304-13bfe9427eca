<?php
function __($key)
{
    global $translationsArray;
    return $translationsArray[$key] ?? '';
}

$translationsArray = [
    // Requisition
    "Manual Inbound" => "إضافة يدوي",
    "Inbound" => "إضافة",
    "Manual Outbound" => "صرف يدوي",
    "Outbound" => "صرف",
    "Manual Transfer" => "تحويل يدوي",
    "Transfer" => "تحويل",
    "Invoice" => "فاتورة",
    "Invoice Refund" => "استرداد الفاتورة",
    "Invoice Credit Note" => "فاتورة ائتمان",
    "Purchase Order" => "أمر شراء",
    "Purchase Refund" => "مرتجع مشتريات",
    "Transfer Requisition" => "طلب التحويل",
    "Transfer Inbound" => "نقل الوارد",
    "Transfer Outbound" => "نقل صادر",
    "POS Inbound" => "نقطة البيع الواردة",
    "POS Outbound" => "نقاط البيع الصادرة",
    "Stocktaking Out" => "جرد",
    "Stocktaking In" => "جرد",

    // Payrun
    "Daily" => "يوميًا",
    "Weekly" => "أسبوعي",
    "Monthly" => "شهريا",
    "annually" => "سنويا",
    "Bi-Weekly" => "نصف أسبوعي",
    "Quarterly" => "ربع سنوي",
    "Semi-Annually" => "نصف سنوى",
    "Paid" => "مدفوع",
    "Approved" => "تمت الموافقة",
    "Generated" => "تم إصداره",

    // Payslip
    'Present' => 'حاضر',
    "Day Off" => "يوم إجازة",
    'Absent' => 'غائب',

    // Contract
    "Draft" => 'حفظ مسودة',
    "Active" => "نشط",
    "Expired" => "منتهى",
    "Pending" => "تحت المراجعة",
    "Renewed" => "تم تجديده",
    "Cancelled" => "تم الإلغاء",
    "Superseded" => "استبدل",
    "Terminated" => "موقوف",
    "Year" => "السنة",
    "Month" => "شهر",

    // Receivable Cheque
    "Received" => "إستلم",
    "Rejected" => "مرفوض",
    "Collected" => "حصل",
    "Deposited" => "أودع",

];