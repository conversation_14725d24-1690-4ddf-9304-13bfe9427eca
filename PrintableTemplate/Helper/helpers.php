<?php


function array_column_count($data, $column, $target)
{
    return count(array_filter($data, function ($result) use ($column, $target) {
        return $result[$column] == $target;
    }));
}

function array_sum_column($data, $column)
{
    return array_sum(array_column($data, $column));
}

function add_due_days_to_date($data, $dateColumn, $dueDateColumn)
{
    return date('Y-m-d', strtotime($data[$dateColumn] . " + $data[$dueDateColumn] days"));
}

function days_between_dates($start_date, $end_date)
{
    $diff = strtotime($end_date) - strtotime($start_date);
    echo floor($diff / (60 * 60 * 24));
}

function get_specific_data($data, $column, $target)
{
    return array_filter($data, function ($row) use ($column, $target) {
        return $row[$column] == $target;
    });
}

function getAutoBarcode($item_id, $site_id, $item_type = 1)
{
 // way to use it
    //     <!--%  
    //     $barCode =getAutoBarcode({{Data.membership.client_id}},{{__system.business.email}});

    //     %-->
    // <p>{{barCode}}</p>
    $first_block = $item_type;

    $site_id = substr($site_id, -4);
    $second_block = str_pad($site_id, 4, '0', STR_PAD_LEFT); // 4 digits left padded for site_id
    $third_block = str_pad($item_id, 7, '0', STR_PAD_LEFT);
    $_12digitcode = $first_block . $second_block . $third_block;
    $code = $first_block . $second_block . $third_block . ean_checkdigit($_12digitcode);
    return $code;
}

function ean_checkdigit($code)
{
    $code = str_pad($code, 12, "0", STR_PAD_LEFT);
    $sum = 0;
    for ($i = (strlen($code) - 1); $i >= 0; $i--) {
        $sum += (($i % 2) * 2 + 1) * (int)$code[$i];
    }

    return (10 - ($sum % 10));
}

function calculateDateDifference($startDate, $endDate, $lang = 'en')
{
    // Convert the input strings to DateTime objects
    $start = new DateTime($startDate);
    $end = new DateTime($endDate);

    // Calculate the difference
    $interval = $start->diff($end);

    // Define translations for each language
    $translations = [
        'en' => [
            'year' => 'year',
            'years' => 'years',
            'month' => 'month',
            'months' => 'months',
            'day' => 'day',
            'days' => 'days',
            'hour' => 'hour',
            'hours' => 'hours',
            'minute' => 'minute',
            'minutes' => 'minutes',
            'second' => 'second',
            'seconds' => 'seconds',
        ],
        'ar' => [
            'year' => 'سنة',
            'years' => 'سنوات',
            'month' => 'شهر',
            'months' => 'أشهر',
            'day' => 'يوم',
            'days' => 'أيام',
            'hour' => 'ساعة',
            'hours' => 'ساعات',
            'minute' => 'دقيقة',
            'minutes' => 'دقائق',
            'second' => 'ثانية',
            'seconds' => 'ثوانٍ',
        ],
    ];

    // Build the output dynamically
    $parts = [];
    if ($interval->y > 0) {
        $parts[] = $interval->y . ' ' . ($interval->y == 1 ? $translations[$lang]['year'] : $translations[$lang]['years']);
    }
    if ($interval->m > 0) {
        $parts[] = $interval->m . ' ' . ($interval->m == 1 ? $translations[$lang]['month'] : $translations[$lang]['months']);
    }
    if ($interval->d > 0) {
        $parts[] = $interval->d . ' ' . ($interval->d == 1 ? $translations[$lang]['day'] : $translations[$lang]['days']);
    }
    if ($interval->h > 0) {
        $parts[] = $interval->h . ' ' . ($interval->h == 1 ? $translations[$lang]['hour'] : $translations[$lang]['hours']);
    }
    if ($interval->i > 0) {
        $parts[] = $interval->i . ' ' . ($interval->i == 1 ? $translations[$lang]['minute'] : $translations[$lang]['minutes']);
    }
    if ($interval->s > 0) {
        $parts[] = $interval->s . ' ' . ($interval->s == 1 ? $translations[$lang]['second'] : $translations[$lang]['seconds']);
    }

    // Return the result as a comma-separated string of non-zero units
    return implode(", ", $parts);
}