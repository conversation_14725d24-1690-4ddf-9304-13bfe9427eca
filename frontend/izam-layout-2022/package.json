{"name": "izam-layout-2022", "version": "1.0.0", "description": "", "main": "webpack.production.js", "scripts": {"start": "webpack --config webpack.development.js --progress --watch", "build": "webpack --config webpack.production.js", "json-db": "json-server -w db.json"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.16.10", "@babel/preset-react": "^7.16.7", "babel-loader": "^8.2.2", "babel-polyfill": "^6.26.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^10.2.4", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.4.1", "file-loader": "^6.2.0", "glob": "^7.2.0", "html-webpack-plugin": "^5.5.0", "json-server": "^0.17.0", "mini-css-extract-plugin": "^2.5.2", "react": "^17.0.2", "react-dom": "^17.0.2", "sass": "^1.49.0", "sass-loader": "^12.4.0", "svg-inline-loader": "^0.8.2", "url-loader": "^4.1.1", "webpack": "^5.50.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.0.0", "webpack-remove-empty-scripts": "^0.7.2"}, "dependencies": {"@babel/runtime": "^7.18.9", "@ckeditor/ckeditor5-build-decoupled-document": "^35.1.0", "@ckeditor/ckeditor5-html-support": "^35.1.0", "@fancyapps/ui": "^5.0.20", "@googlemaps/js-api-loader": "^1.15.1", "@popperjs/core": "^2.11.2", "@reduxjs/toolkit": "^1.7.1", "@selectize/selectize": "0.13.4", "@tinymce/tinymce-react": "^4.1.0", "base-64": "^1.0.0", "bootstrap": "5.1.3", "classnames": "^2.3.2", "contrast-color": "^1.0.1", "convert-units": "^2.3.4", "core-js": "^3.20.3", "escape-html": "^1.0.3", "file-extension": "^4.0.5", "flatpickr": "^4.6.13", "flatpickr-hijri-calendar": "^1.0.0", "i18next": "^24.0.2", "iframe-resizer": "^4.3.2", "interactjs": "^1.10.17", "jquery": "^3.6.0", "libphonenumber-js": "^1.10.14", "lodash": "^4.17.21", "luxon": "^3.4.4", "moment": "^2.29.1", "overlayscrollbars": "^1.13.1", "perfect-scrollbar": "^1.5.5", "query-string": "^7.1.1", "react-calendar-timeline": "^0.28.0", "react-datepicker": "^4.8.0", "react-datetime-picker": "^4.1.1", "react-i18next": "^15.1.2", "react-localization": "^1.0.18", "react-modal": "^3.14.4", "react-redux": "^7.2.6", "react-router-dom": "^6.2.2", "react-select": "^5.2.2", "react-toastify": "^9.1.1", "react-use-draggable-scroll": "^0.4.7", "resolve-url-loader": "^5.0.0", "sprintf-js": "^1.1.2", "table-dragger": "^1.0.3", "tinymce": "^6.2.0", "underscore": "^1.13.6", "validator": "^13.7.0"}}