import tinymce from 'tinymce/tinymce';
import 'tinymce/models/dom';
import 'tinymce/icons/default';
import 'tinymce/themes/silver';
import 'tinymce/plugins/table';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/link';
import 'tinymce/plugins/autoresize';
import 'tinymce/plugins/directionality';
import 'tinymce/plugins/pagebreak';
import 'tinymce/plugins/image';
import 'tinymce/plugins/code';

export default class Editor {
    constructor(name) {
        this.name = name;
    }
    init() {
        return new Promise((res, rej) => {
            const createErrorNotification = () => {
                tinymce.activeEditor.notificationManager.open({
                    text: __('Max file size') + ' ' + '5' + ' ' + __('MB'),
                    type: 'error'
                });
            };
            const image_upload_handler = (blobInfo, progress) => new Promise((resolve, reject) => {

                if (blobInfo.blob().size > 5120 * 5120) {
                    createErrorNotification();
                    return reject({message: 'File is too big!', remove: true});
                }

                // Do the rest
            });
            const toolbar = document.createElement("div");
            const entityKey = document.querySelector('[data-entity-key]')?.getAttribute('data-entity-key') || 'default';
            const fieldKey = this.name;
            
            toolbar.setAttribute(`data-section-toolbar`, 'true');
            toolbar.style.display = 'none';
            toolbar.setAttribute(`data-${this.name}-section-toolbar`, 'true');
            document.querySelector('[data-sections-toolbars="true"]').appendChild(toolbar);
    
            tinymce.init({
                selector: `[data-${this.name}="true"]`,
                inline: true,
                menubar: false,
                plugins: 'table link lists directionality pagebreak image code',
                /* enable automatic uploads of images represented by blob or data URIs*/
                // automatic_uploads: true,
                file_picker_types: 'image',
                // images_upload_url: ``,
                /* and here's our custom image picker*/
                file_picker_callback: (cb, value, meta) => {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');

                    input.addEventListener('change', (e) => {
                    const file = e.target.files[0];

                    const reader = new FileReader();
                    reader.addEventListener('load', () => {
                        /*
                        Note: Now we need to register the blob in TinyMCEs image blob
                        registry. In the next release this part hopefully won't be
                        necessary, as we are looking to handle it internally.
                        */
                        const id = 'blobid' + (new Date()).getTime();
                        const blobCache =  tinymce.activeEditor.editorUpload.blobCache;
                        const base64 = reader.result.split(',')[1];
                        const blobInfo = blobCache.create(id, file, base64);
                        blobCache.add(blobInfo);

                        /* call the callback and populate the Title field with the file name */
                        cb(blobInfo.blobUri(), { title: file.name });
                    });
                    reader.readAsDataURL(file);
                    });

                    input.click();
                },
                images_upload_handler: image_upload_handler,
                verify_html: false,
                allow_conditional_comments: true,
                extended_valid_elements: "span[*],style,link[href|rel]",
                custom_elements:"style,link,~link",
                newline_behavior: 'linebreak',
                contextmenu: false,
                hidden_input: false,
                fixed_toolbar_container_target: toolbar,
                editable_class: 'mce-inline-editable-area',
                noneditable_class: 'mce-inline-non-editable-area',
                toolbar_persist: true,
                relative_urls: false,
                convert_urls : false,
                visual: false,
                font_size_formats: '8px 10px 12px 14px 16px 18px 24px 36px 48px',
                // table_style_by_css: false,
                table_default_styles: {
                    // 'border-collapse': 'collapse',
                    'width': '100%',
                    // 'border-width': '1px',
                    // 'border-color': '#000000',
                    // 'border-style': 'solid',
                },
                protect: [
                    /<\!--%.*?\%-->/g  // Protect loops
                ],
                toolbar: "undo redo styles textstyle alignment indent outdent bullist numlist ltr rtl link image table code",
                setup: (editor) => {
                    editor.ui.registry.addGroupToolbarButton('alignment', {
                      icon: 'align-left',
                      tooltip: 'Alignment',
                      items: 'alignleft aligncenter alignright | alignjustify'
                    });
                    editor.ui.registry.addGroupToolbarButton('textstyle', {
                      icon: 'change-case',
                      tooltip: 'Text Style',
                      items: 'fontsize | bold italic underline | forecolor backcolor | fontfamily'
                    });
                    editor.on('focus', () => {
                        $('[data-section-toolbar]').not($(`[data-${this.name}-section-toolbar]`)).hide();
                        $(`[data-${this.name}-section-toolbar]`).show();
                    });
                    editor.on("init", () => {
                        res(editor);
                    });
                }
            });
            
            console.log(tinymce);
        });
    }
}
