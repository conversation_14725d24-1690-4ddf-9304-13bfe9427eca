.app-placeholder {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.3);
    z-index: 99;
}

.app-placeholder-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    width: 75%;
}

.placeholder-tree--header {
    padding: 15px 20px;
    min-height: 55px;
    background: #F3F8F9;
    border-bottom: 1px solid #EAEEEF;

}



.placeholder-tree--header h2 {
    font-size: 20px;
    margin: 0;
    color: #202124;
}

.upper {
    display: flex;
    justify-content: space-between;
    min-height: 400px;
}

.placeholder-tree {
    flex: 4;
    width: 70%
}

.placeholder-sidebar {
    flex: 1;
}

.placeholder-sidebar--head {
    display: flex;
    height: 55px;
}

.insert-label {
    background-color: #1877F2;
    color: #fff;
    border: 0;
    flex: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 9px;
    position: relative;
    font-size: 17px;
    cursor: pointer;
}

.insert-dropdown {
    list-style: none;
    padding: 0 !important;
    background: #fff;
    z-index: 99999;
    color: #1877f2;
    position: absolute;
    border: 1px solid;
    width: 100%;
    text-align: center;
    top: 100%;
}

.insert-dropdown li {
    display: block;
    padding: 8px;
    cursor: pointer;
    &:first-of-type {
        border-bottom: 1px solid #eee;
    }
}

.insert-dropdown li:hover {
    background-color: #F3F8F9;
}


.placeholder-sidebar--head .close-icon {
    flex: 0.5;
    height: 100%;
    text-align: center;
    line-height: 2.9;
    font-size: 18px;
    cursor: pointer;
    background: #F3F8F9;
}

.placeholder-sidebar--head .close-icon:hover {
    background-color: #da0000;
    color: #fff;
}

.placeholder-tree--body {
    width: 100%;
}

.placeholder-sidebar--body {
    background: #fff;
    min-height: 400px;
    max-height: 400px;
    overflow: auto;
    border-left: 1px solid #E4EBF2;
}

[dir=rtl] .placeholder-sidebar--body {
    border-left: 0;
    border-right: 1px solid #E4EBF2;
}

.placeholder-sidebar--body ul {
    list-style: none;
    padding: 0;
}


.placeholder-sidebar--body ul li a {
    display: block;
    padding: 19px 15px;
    text-decoration: none;
    color: #333;
    border-bottom: 1px solid #E4EBF2;
    position: relative;
}

.placeholder-sidebar--body ul li a i.active {
    transform: translateY(-50%) rotate(90deg);
}

.placeholder-sidebar--body ul li a.sub-entities {
    color: #ACAFC4;
    cursor: auto;
    &:hover {
        background-color: transparent;
    }
}


.placeholder-sidebar--body ul li a:hover {
    background-color: #F3F8F9;
}

.placeholder-sidebar--body ul li a span {
    color: #1877F2;
}

.placeholder-sidebar--body ul li a i {
    color: #000;
    font-size: 14px;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
}

[dir=rtl] .placeholder-sidebar--body ul li a i {
    right: auto;
    left: 16px;
    transform: rotate(180deg);
}

[dir="ltr"] .placeholder-sidebar--body ul li a {
    padding-right: 30px
}

[dir="rtl"] .placeholder-sidebar--body ul li a {
    padding-left: 30px
}

.placeholder-sidebar--body ul.sub-menu li a {
    font-size: 12px;
    padding: 13px 15px;
}

[dir="ltr"] .placeholder-sidebar--body ul.sub-menu li a {
    padding-right: 27px;
}

[dir="rtl"] .placeholder-sidebar--body ul.sub-menu li a {
    padding-left: 27px;
}

.down {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #E4EBF2;
}

.app-placeholder .btn {
    height: 42px;
    border-radius: 2px;
    min-width: 180px;
    border: 0;
    font-weight: 600;
    cursor: pointer;
    opacity: 0.8;
}

.app-placeholder .btn:hover {
    opacity: 1
}

.reset-all {
    background-color: #E4EBF2;
    color: #75799D;
    text-align: center;
    line-height: 42px;
}

.confirm {
    background-color: #13B272;
    color: #fff;
}

/*----------------tree-scroll----------*/

.tree-scroll::-webkit-scrollbar {
    width: 5px;
    height: 8px;
}
.tree-scroll::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #e4e4e4;
}
.tree-scroll::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
    transition: 0.5s;
}
.tree-scroll::-webkit-scrollbar-thumb:hover {
    background: #333;
    transition: 0.5s;
}

/*----------------tree----------*/
.tree-body{
    white-space: nowrap;
    overflow-y: auto;
    padding: 20px;
    max-height: 400px;
    padding-top: 10px;
}
.tree{
    display: inline-block;
}
.tree ul {
    padding-top: 20px; 
    position: relative;
    padding-left: 0px;
    display: flex;
    justify-content: center;
    display: none;
}
.tree li {
    display: flex;
    float: left; text-align: center;
    list-style-type: none;
    position: relative;
    padding: 20px 5px 0 5px;
}
.tree li::before{
    content: '';
    position: absolute; 
    top: 1px; 
    height: 1px;
    background-color: #000;
    width: 100%;
}
[dir="ltr"] .tree li::after{
    right: auto; left: 50%;
}
[dir="rtl"] .tree li::after{
    left: auto; right: 50.5%;
}
.tree li:only-child::after, .tree li:only-child::before {
    display: none;
}
.tree li:only-child{ 
    padding-top: 0;
}
.tree ul li:last-child::after {
    content: "";
    position: absolute;
    width: 100%;
    background: #fff;
}
[dir="ltr"] .tree ul li:last-child::after {
    top: 1px;
    height: 1px;
}
[dir="rtl"] .tree ul li:last-child::after {
    top: 0px;
    height: 3px;
}
.tree > ul > li  li:last-child:not(:has(ul)) a h3:after {
    content: "";
    position: absolute;
    top: -32px;
    width: 50%;
    height: 1px;
    left: 83px;
    background: #fff;
}

.tree ul ul::before{
    // content: "";
    position: absolute;
    top: 21px;
    left: -30px;
    /* border-top: 2px solid #202124; */
    width: 21px;
    height: 1px;
    background: #000;
}

[dir=rtl] .tree ul ul::before{
    left: auto;
    right: -30px;
}

.tree li a{
    text-decoration: none;
    color: #666;
    font-family: arial, verdana, tahoma;
    font-size: 11px;
    display: inline-block;
}

.tree li a:before {
    // content: "";
    position: absolute;
    width: 2px;
    height: 18px;
    background: #202124;
    top: 1px;
    left: 50px 
}

.tree li .first:before {
    display: none;
}

.tree li a:hover+ul li::after, 
.tree li a:hover+ul li::before, 
.tree li a:hover+ul::before, 
.tree li a:hover+ul ul::before{
    // border-color:  #fbba00;
}

/*--------------memeber-card-design----------*/
.member-view-box{
    padding:0px 20px;
    text-align: left;
    border-radius: 4px;
    position: relative;
}

[dir=rtl] .member-view-box {
    text-align: right;
}
.member-image{
    position: relative;
}
.member-details h3 {
    font-size: 1.5em !important;
    margin: 0.8em 0 !important;
    color: #000;
    position: relative;
    display: inline-block;
    text-align: left;
    background-color: #fff;
    z-index: 99;
    padding: 0 3px;
}

[dir=rtl] .member-details h3 {
    text-align: right;
}


.member-details h3:before {
    content: "";
    position: absolute;
    height: 18px;
    width: 1px;
    background: #000;
    left: 50%;
    transform: translateX(-50%);
    top: -31px;
}


.tree > ul > li > a .member-details h3:before {
    display: none;
}

[dir=rtl] .member-details h3:before {
    left: auto;
    right: 50%;
}

.first .member-details h3:before {
    display: none;
}

.app-placeholder .form-control {    
    display: flex;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
    gap: 6px;
    margin-bottom: 4px;
    font-size: 14px;
}

.app-placeholder .form-control label {
    position: relative;
}

.app-placeholder .form-control label:before {
    content: "";
    position: absolute;
    width: 15px;
    height: 15px;
    background: #fff;
    left: -20px;
    top: 3px;
    border: 1px solid #ccc;
    text-align: center;
    border-radius: 2px;
    font-size: 10px;
}

[dir=rtl] .app-placeholder .form-control label:before  {
    left: auto;
    right: -20px;
}

.app-placeholder .form-control input:checked + label span {
    position: absolute;
    width: 15px;
    height: 15px;
    background: #4075fe;
    color: #fff;
    left: -20px;
    top: 3px;
    border: 1px solid #ccc;
    text-align: center;
    border-radius: 2px;
    font-size: 10px;
}

[dir=rtl] .app-placeholder .form-control input:checked + label span {
    left: auto;
    right: -20px;
}

.app-placeholder .form-control label span {
    color: #ACAFC4;
}

// Loader
.lds-ring {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}
.lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 40px;
    height: 40px;
    margin: 8px;
    border: 4px solid #ACAFC4;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #ACAFC4 transparent transparent transparent;
}
.lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
}
.lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
}
.lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
}
@keyframes lds-ring {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
