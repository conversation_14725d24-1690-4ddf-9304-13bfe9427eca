// Style App

#help-container {
  width: 0px;
  height: 0px;
}

#help-container.opened .modal-bg-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1500;
  display: none;
}

#help-container.opened .modal-bg-container .modal-bg {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  opacity: 0;
  background-color: #000;
  transition: all 0.5;
}

#help-container.opened .modal-bg-container {
  display: block;
}

#help-container.opened .modal-bg-container .modal-bg {
  opacity: 0.5;
}

.general-help-modal {
  width: 650px;
  max-width: 100%;
  background-color: #fff;
}

.forms-modal {
  position: fixed;
  bottom: 0;
  // top: 0;
  right: -650px;
  width: 650px;
  max-width: 100%;
  background-color: #fff;
  opacity: 0;
  z-index: 1000000;
  transition: all 0.3s;
}


.modal-header-btn {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  cursor: pointer;
}

.forms-header-action {
  display: flex;
  align-items: center;
}
.modal-title-container {
  align-items: center;
  font-size: 18px;
  color: #202124;
  i {
    font-size: 40px;
    color: #1877f2;
    margin: 0px 5px 0px 15px;
  }
  .header-icon,
  .header-title {
    display: none;
  }
  .header-title {
    color: #202124;
    font-size: 20px;
    font-weight: 500;
    padding-inline-start: 15px;
  }
}

.back-modal-btn {
  background-color: #e4ebf2;
  color: #75799d !important;

  &:hover {
    background-color: #1877f2;
    color: #fff !important;
  }
}

.general-item-container {
  width: 100%;
  height: 140px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-color: #fff;
  border-bottom: 1px solid #e4ebf2;
  transition: all 0.3s;
  cursor: pointer;

  .item-icon {
    font-size: 80px;
    color: #1877f2;
    padding: 0px 40px;
  }
  .item-content {
    padding: 20px 0px;
    padding-inline-end: 15px;
  }
  .item-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .item-description {
    font-size: 15px;
  }

  &:hover {
    background-color: #e4ebf2;
  }
}

.help-close {
  background-color: #e4eaf2;
  color: #727679;

  &:hover {
    background-color: #fe0010;
    color: #fff;
  }
}

.mini-btn {
  background-color: #e4ebf2;
  color: #75799d;

  &:hover {
    background-color: #b0c2d4;
  }
}

.general-help-modal {
  height: 420px;
  overflow-y: auto;
  &::-webkit-scrollbar {
      background-color:#fff;
      width:16px
  }
  &::-webkit-scrollbar-track {
    background-color:#fff
  }
  &::-webkit-scrollbar-track:hover {
    background-color:#f4f4f4
  }
  &::-webkit-scrollbar-thumb {
    background-color:#babac0;
    border-radius:16px;
    border:5px solid #fff
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color:#a0a0a5;
    border:4px solid #f4f4f4
  }

  .general-help-header {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
  }

  .modal-bg2 {
    width: 100px;
    height: 100px;
    background-color: blue;
  }
  &.daftra{
    height: 560px;
  }
}

// Forms-moda container
.forms-modal {
  height: 100%;
  max-height: 100%;

  .mini-btn {
    display: flex;
  }
  .maxi-btn {
    display: none;
  }

  &.auto-height {
    height: auto;

    .mini-btn {
      display: none !important;
    }
  }

  &.opened {
    right: 0px;
    opacity: 1;
  }

  &.minimized {
    height: 60px;
    -webkit-box-shadow: 0px -1px 7px 0.5px rgba(0, 0, 0, 0.23);
    -moz-box-shadow: 0px -1px 7px 0.5px rgba(0, 0, 0, 0.23);
    box-shadow: 0px -1px 7px 0.5px rgba(0, 0, 0, 0.23);

    .btn-container {
      bottom: auto;
    }

    .forms-header {
      height: 60px;
    }

    .mini-btn {
      display: none;
    }
    .maxi-btn {
      display: flex;
    }
    .modal-title-container {
      display: flex;
      .header-icon,
      .header-title {
        display: flex;
      }
    }
    .back-modal-btn {
      display: none;
    }
    .modal-content-container {
      opacity: 0;
      transition-delay: 0s;
    }
  }

  .forms-header {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
  }

  &.show-title {
    .modal-title-container {
      .header-title {
        display: flex;
      }
    }
  }
}

.modal-content-container {
  width: 100%;
  height: calc(100% - 50px);
  padding: 0px;
  overflow: hidden;
  transition: all .2s ease-in-out;
  transition-delay: .2s;
}

#help-container {
  &.opened {
    .general-help-modal {
      right: 0px;
      opacity: 1;
    }
  }
}

.system-manual-link:hover{
text-decoration: none;
color: var(--body-text-color);
}

[dir="rtl"] {
  #help-container {
    .general-help-modal,
    .forms-modal {
      right: auto;
      left: -650px;

      &.opened {
        right: auto;
        left: 0px;
      }
    }
  }
}

.page-container {
  height: calc(100vh - 50px);
  max-height: calc(100vh - 50px);
  width: 100%;
  overflow: hidden;
  form {
    height: 100%;
  }
}

.form-wrapper {
  width: 100%;
  height: calc(100% - 100px);
  padding: 20px;
  overflow-y: auto;

  .form-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .form-icon {
      font-size: 55px;
      margin-inline-end: 20px;
      color: #1877f2;
    }

    .form-header-description {
      display: flex;
      flex-direction: column;
      .title {
        font-size: 20px;
        margin-bottom: 4px;
        color: #202124;
      }
      .desc {
        color: #202124;
        font-size: 16px;
        margin-bottom: 0px;
      }
    }
  }

  label.ui-input-label,
  label.ui-input-label span,
  .ui-input {
    font-size: 16px;
  }

  .required {
      &:not(.selectize-input) {
        color: #ff0a05;
        margin-inline-start: 2px;
    }
  }

  &.full-height {
    height: 100%;
  }

  &.no-scroll {
    overflow-y: hidden;
    height: calc(100% - 130px);
  }

  .external-link{
    color: #1877f2;
    cursor: pointer;
    font-size: 16px;
    &.title-link{
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;

      i{
        font-size: 24px;
      }
    }
  }
}
.btn-container {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 100px;
  background-color: #fff;
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;

  .btn {
    height: 60px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
  .other-btn {
    width: 40%;
    margin-inline-end: 15px;
    text-align: left;
    background-color: #e4ebf2;
    color: #75799d;
    &:hover {
      background-color: #b0c2d4;
    }
  }

  &.with-suggest {
    height: 140px;
    flex-direction: column;
  }
  .question-btn {
    background-color: #1877f2;
    color: #fff;
  }

  .open-modal-suggest {
    color: #202124;
    font-size: 16px;
    margin-bottom: 10px;
    cursor: pointer;
    height: 20px;
  }

  &.with-suggest {
    height: 140px;
    flex-direction: column;
  }
  .question-btn {
    background-color: #1877f2;
    color: #fff;
  }

  .open-modal-suggest {
    color: #202124;
    font-size: 16px;
    margin-bottom: 10px;
    cursor: pointer;
    height: 20px;
  }
}

.success-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .content-container {
    padding: 0px 40px;
    text-align: center;
    i {
      font-size: 90px;
      color: #13b272;
    }
    h2 {
      font-weight: bold;
      font-size: 24px;
    }
    p {
      font-size: 18px;
      margin-bottom: 0px;
      a {
        color: #13b272;
      }

      a.internal-link {
        color: #1877f2;
        text-decoration: underline;
      }
    }

    .modal-close {
      background-color: #e4ebf2;
      color: #75799d;
      font-weight: bold;
      border-radius: 2px;
      padding: 20px 40px;
      border: 0;
    }
  }
}

.issues-radio-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  .radio-container {
    display: flex;
    align-items: center;
    background-color: #f6f9fc;
    height: 50px;
    padding: 0px 20px;
    width: 49%;
    .form-check {
      display: flex;
      align-items: center;
    }
    label {
      margin-bottom: 0px;
      padding-bottom: 0px;
      margin-top: 4px;
      margin-inline-start: 8px;
    }
    .ui-radio {
      width: 20px;
    }
  }
}

.country-title {
  width: 100%;
  padding-inline-start: 60px;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 15px;
}
.contact-container {
  display: flex;

  .icon {
    width: 60px;
    text-align: center;
    i {
      font-size: 24px;
      color: #1877f2;

      &.whats-ico {
        color: #009e30;
      }
    }
  }
  .contact {
    display: flex;
    flex-direction: column;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #202124;
    }
    .phone {
      font-size: 16px;
      color: #202124;
    }
    .time-zone {
      font-size: 16px;

      color: #9ea1ba;
    }
  }
}

.results-title {
  font-size: 20px;
  color: #202124;
}

.results-container {
  width: 100%;
  max-height: calc(100vh - 450px);
  text-align: center;
  padding-top: 20px;
  overflow-y: auto;

  .no-results-img {
    margin: 0px auto;
    margin-top: 50px;
    width: 140px;
    opacity: 0.5;
    margin-bottom: 13px;
  
  }

  .no-question-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .external-link{
      font-size: 14px;

      i{
        font-size: 18px;
        margin-inline-start: 5px;
    margin-top: 5px;
    display: inline-block;
      }

     
    }
  }

  .no-result-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    h5 {
      font-size: 18px;
      color: #202124;
      margin-bottom: 0px;
    }
    p {
      font-size: 16px;
      color: #9ea1ba;
    }
    .clear-results {
      color: #1877f2;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 16px;
      justify-content: center;
      gap: 8px;
      i {
        color: #fff;
        background-color: #1877f2;
        border-radius: 50%;
        font-size: 14px;
        width: 22px;
        height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.search-clear-ico {
  i {
    color: #fff;
    background-color: #213242;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.result-row {
  &:hover {
    background-color: #e4ebf2;
    text-decoration: none;
  }

  .resutlt-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    border-bottom: 1px solid #e4ebf2;
    p {
      padding-bottom: 0px;
      margin-bottom: 0px;
      font-size: 16px;
      text-align: start;
    }
    .result-container {
      display: flex;
      align-items: center;
      i {
        color: #1877f2;
        font-size: 25px;
      }
      .result-type{
        color: #75799d;
      }
    }
    &:hover {
      background-color: #e4ebf2;
      text-decoration: none;
    }
  }
}

.loader-container {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(255,255,255,0.5);
  z-index: 9;
  display: none;
  &.loading{
display: block;
  }
}


.modal-loader-container {
  width: 100%;
  height: 100%;
  min-height: 560px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  &.min-height {
    height: 560px;
  }
}

.hidden.modal-type {
  display: none;
}

// upload file component
.attachments-container {
  width: 100%;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-drop-area {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 100%;
}

.fake-btn {
  flex-shrink: 0;
  background-color: #9699b3;
  border-radius: 3px;
  padding: 8px 15px;
  margin-right: 10px;
  font-size: 12px;
  text-transform: uppercase;
}

.file-msg {
  color: #9699b3;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-input {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  opacity: 0;
}

.file-input:focus {
  outline: none;
}
.template-Modules i{
display: none;
}
.select-container .selectize-dropdown{
max-width: 100% !important;
}

.support-modal-btn, .support-modal-btn:focus{
  outline: none;
  border: 0;
}

@media only screen and (max-width: 993px) {
  .forms-modal{
    top:0;
  }
  #help-container.opened .general-help-modal{
    height: 100%;
  }
  .general-item-container {
    height: auto;
    .item-icon {
      padding: 0px 10px;
      font-size: 65px;
    };
    .item-title{
      font-size: 18px;
    }
    .item-description{
      font-size: 14px;
    }
  }
}
