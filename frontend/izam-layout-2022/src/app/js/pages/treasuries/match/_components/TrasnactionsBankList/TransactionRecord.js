import React, { useEffect, useState } from 'react';
import { formatAmount } from '../../_utils';
import * as redux  from '../../_redux/transactionsSlice';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import { strings, text } from '../../_local';
import { returnLang } from '../../_utils';

function TransactionRecord(props) {
    const { item } = props;
    const dispatch = useDispatch();
    const { transactionId } = useParams();
    const [checked, setChecked] = useState(false);
    const { checkedTransactions,bankItems,  items, loading, err, selectedTransactionType, selectedTransaction } = useSelector(state => state.transactions);

    const handleGetTransaction = (id) => {
        if(!props.activeMode) {
            dispatch(redux.resetData({name: "items", value: []}));
            dispatch(redux.setSystemAmount({value: [], data: [], checked: false}));
            dispatch(redux.openSystemPage(true));
            dispatch(redux.getBankItem(item));
            dispatch(redux.setCheckedTransactions({item: []}));
            dispatch(redux.setBankAmount({ amount: item.amount }));
            dispatch(redux.setEditTransaction(false));
            dispatch(redux.setAddTransaction(false));
            dispatch(redux.fetchSystemTransactions(id));
        }
        else {
            dispatch(redux.setCheckedTransactions({item: []}));
            dispatch(redux.setSystemAmount({value: [], data: [], checked: false}));
            dispatch(redux.fetchSystemTransactions());
            if (selectedTransactionType == 'bank') {
                dispatch(redux.setSelectedTransactionType(null));
                dispatch(redux.setSelectedTransaction(null));
            }
        };
    }

    const handleInputClick = (e) => {
      e.stopPropagation();
    }

    useEffect(() => {
        if(checkedTransactions.findIndex(p => p.id == item.id) > -1) {
          setChecked(true);
        } else {
          setChecked(false);
        }
    }, [checkedTransactions]);

    useEffect(() => {
        if((bankItems && bankItems[0].id && !transactionId) || (bankItems && bankItems[0].id == transactionId)) {
            if(item.id === bankItems[0].id ) {
                dispatch(redux.setSystemPageNumber(null));
                dispatch(redux.openSystemPage(true));
                dispatch(redux.setCheckedTransactions({item: []}));
                dispatch(redux.setBankAmount({ amount: item.amount }));
                if (selectedTransactionType == 'bank') {
                    dispatch(redux.setSelectedTransactionType(null));
                    dispatch(redux.setSelectedTransaction(null));
                }
            }
        }
    }, []);

    useEffect(() => {
        if(item && transactionId) {
            if(transactionId == item.id && bankItems[0].id != transactionId) {
                props.handleActiveClass(bankItems && bankItems.findIndex(i => i.id == item.id));
                dispatch(redux.openSystemPage(true));
                dispatch(redux.getBankItem(item));
                dispatch(redux.setCheckedTransactions({item: []}));
                dispatch(redux.setBankAmount({ amount: item.amount }));
                dispatch(redux.fetchSystemTransactions(transactionId));
                dispatch(redux.setSelectedTransaction(item));
                dispatch(redux.setSelectedTransactionType('bank'));
            }
        }
    }, []);

    return (
        <div className={`transaction-record ${props.activeMode ? 'selected' : items?.length == 0 && loading ? 'not-done' : '' }`}>
            {selectedTransactionType == 'system' && (
                <div>
                    <div className="action">
                        <div onClick={handleInputClick}>
                            <input 
                                onChange={() => {
                                    setChecked(!checked);
                                    dispatch(redux.setSystemAmount({ value: item, checked: !checked, data: bankItems}))
                                    dispatch(redux.setCheckedTransactions({item, rowChecked: !checked}))
                                }}
                                className="styled-checkbox"
                                id={`styled-checkbox-${item.id}-${item.id}`}
                                type="checkbox"
                                value="value1"/>
                            <label htmlFor={`styled-checkbox-${item.id}-${item.id}`}></label>
                        </div>
                    </div>
                </div>
            )}
            <div className="date-time">
                <span className="date">{moment(item.date).format(window.__jsDateFormat)}</span>
                <span className="ref">
                    {item.reference_id ? (
                        <>
                            <span>#</span>
                            <span>{item.reference_id} </span>
                        </>
                    ) : ""}
                </span>
            </div>
            <div className="description">
                <span>{item.description}</span>
            </div>
            <div className={`amount ${item.type == 'deposit' ? '' : 'amount--red' }`}>
                {formatAmount(item.amount)} {item?.bank_transaction_treasury?.currency_code}
            </div>
            {(selectedTransactionType === null || selectedTransactionType === 'bank') && (
                <div className="action">
                    <button className={
                        `match-btn ${selectedTransaction && selectedTransaction.id !== item.id ?
                            `l-btn-inline l-btn--text-center ui-btn u-bg-color-secondary u-text-color-black ui-btn--disabled` :
                            `l-btn-inline l-btn--text-center ui-btn ui-btn--hover-ripple u-bg-color-primary u-text-color-white`}`
                    }
                    onClick={() => {
                        props.handleActiveClass(); // handle Active Class
                        handleGetTransaction(item.id);
                        if (selectedTransaction) {
                            dispatch(redux.setSelectedTransaction(null));
                            dispatch(redux.setSelectedTransactionType(null));
                        } else {
                            dispatch(redux.setSelectedTransactionType('bank'));
                            dispatch(redux.setSelectedTransaction(item));
                        }
                    }}
                    >
                        <span className="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                        <span className="ui-btn-inner-content">
                        <span className="ui-btn-inner-text">
                            {props.activeMode ? text.trans(strings, returnLang(), "unmatchTxt" ) : text.trans(strings, returnLang(), "matchTxt" )}
                        </span>
                        </span>
                    </button>
                </div>
            )}
        </div>
  );
}

export default TransactionRecord;
