import React from 'react';
import { text, strings } from '../../_local';
import { returnLang } from '../../_utils';
import { useSelector } from 'react-redux';

function DetailsBar(props) {
    const {title, amount, badgeNumber, currency} = props;
    const { selectedTransactionType } = useSelector(state => state.transactions);

    const showAmount = (item) => {
        let bankCurr =  window.matchData && window.matchData.bank.currency_code;
        let systemCurr = window.currency_code;
        if (selectedTransactionType == 'system') {
            return item.amount;
        } else {
            if(systemCurr === bankCurr) {
                return item.debit ? item.debit : item.credit
            } else {
                return item.currency_debit ? item.currency_debit : item.currency_credit
            }
        }
    } 
    return (
        <div className="details-bar">
            <div>
                <span className="badge-number">{badgeNumber}</span>
                <div>
                    <h3 className="transaction-title">{title}</h3>
                    <span className="details-txt">{text.trans(strings, returnLang(), "notMatched" )}</span>
                </div>
            </div>
            <div className='amount-currency'>
                <span className="details-total-amount">{window.format_price && window.format_price(typeof amount === 'object'? amount.reduce((acc, item) => acc + showAmount(item) , 0) : amount, currency)}</span>
            </div>
        </div>  
    );
}

export default DetailsBar;
