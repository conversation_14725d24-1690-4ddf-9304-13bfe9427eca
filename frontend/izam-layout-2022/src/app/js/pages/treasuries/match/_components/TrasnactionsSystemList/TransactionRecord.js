import React, {useState, useEffect} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as redux from '../../_redux/transactionsSlice';
import { formatAmountWithRound, returnLang } from '../../_utils';
import moment from 'moment';
import { strings, text } from '../../_local';

function TransactionRecord(props) {
  const {item, data} = props;
  const [checked, setChecked] = useState(false);
  const {checkedTransactions, selectedTransactionType, selectedTransaction} = useSelector(state => state.transactions);
  
  const dispatch = useDispatch();

  const handleGetTransaction = (id) => {
    if(!props.activeMode) {
        dispatch(redux.resetData({name: "bankItems", value: []}));
        dispatch(redux.getSystemItem(item));
        dispatch(redux.fetchBankTransactions({journalTransactionId: item.id, transactionId: null}));
    } else {
        dispatch(redux.setSystemAmount({value: [], data: [], checked: false}));
        dispatch(redux.setCheckedTransactions({item: []}));
        dispatch(redux.fetchBankTransactions({transactionId: null}));
    };
  }

  const handleFilePopup = (e, modalContent) => {
    e.stopPropagation();
    dispatch(redux.setOpenModal());
    dispatch(redux.setModalType("file-modal"));
    dispatch(redux.setModalFilesContent(modalContent))
  }

  const handleJournalPopup = (e, journalId) => {
    e.stopPropagation();
    dispatch(redux.setOpenModal());
    dispatch(redux.setTransactionJournalId(journalId));
    dispatch(redux.setModalType("journal-modal"));
  }

  const handleInputClick = (e) => {
    e.stopPropagation();
  }

  const handleEditTransaction = (e , item) => {
    e.stopPropagation();
    dispatch(redux.setEditTransaction(true));
    dispatch(redux.setEditedTransactionItem(item));
  }

  useEffect(() => {
    if(checkedTransactions.findIndex(p => p.id == item.id) > -1) {
      setChecked(true);
    } else {
      setChecked(false);
    }
  }, [checkedTransactions]);

  // render Journal Date and Id 
  const renderJournalDateAndIdHTML = () => {
    return (
      <div className="date-time">
        <span className="date">{moment(item.journal && item.journal.date).format(window.__jsDateFormat)} </span>
        {item.journal_id && <a href="#" onClick={(e) => handleJournalPopup(e, item.journal_id)} className="item-link ref">#{item.journal.number}</a>}
      </div>
    )
  }
  
  // render Journal Description 
  const renderJournalDescription = () => {
    return (
      <div className="description">
        <span>{item.journal && item.journal.description}</span>
        <p className="info">
            <span><i className="fa fa-building"></i> {item.branch_name}</span>
            <span><i className="fa fa-user"></i> {item.staff && item.staff.name}</span>
            {item.files && item.files.length == 1 &&  <a href={item.files[0].url} download={item.files[0].name} target="_blank" className="item-link"> <i className="fa fa-file"></i> 1 </a>}
            {item.files && item.files.length > 1 && <a onClick={(e) => handleFilePopup(e, item.files)} href="#" className='item-link'><i className="fa fa-file"></i> {item.files.length}</a>}
        </p>
      </div>
    )
  }

  // show amount
  const showAmount = () => {
    let bankCurr =  window.matchData && window.matchData.bank.currency_code;
    let systemCurr = window.currency_code;
    if(systemCurr === bankCurr) {
      return item.debit ? item.debit.toFixed(2) : item.credit.toFixed(2)
    } else {
      return item.currency_debit ? item.currency_debit : item.currency_credit
    }
  } 

  // render Journal Amount 
  const renderJournalAmount = () => {
    return (
      <div className={`amount ${item.currency_debit ? '' : 'amount--red'}`}>
        {formatAmountWithRound(showAmount())}
      </div>
    )
  }
  
  return (
    <>
      {/* Mobile View */}
      <div className={`transaction-record mobile-record ${checked || props.activeMode ? 'selected': ''}`}>
        <div className='record-header'>
          <div>
            {selectedTransactionType == 'bank' && (
              <div className="action">
                <div onClick={handleInputClick}>
                  <input 
                    onChange={() => {
                      setChecked(!checked);
                      dispatch(redux.setSystemAmount({ value: item, checked: !checked, data: data}));
                      dispatch(redux.setCheckedTransactions({item, rowChecked: !checked}));
                    }}
                    checked={checked}
                    className="styled-checkbox"
                    id={`styled-checkbox-${item.id}-${item.id}`}
                    type="checkbox"
                    value="value1"/>
                  <label htmlFor={`styled-checkbox-${item.id}-${item.id}`}></label>
                </div>
              </div>
            )}
            {renderJournalDateAndIdHTML()}
          </div>
          
          <div>
            {renderJournalAmount()}
            <div className="action">
              <button className={
                false ?
                `l-btn-inline l-btn--text-center ui-btn u-bg-color-secondary u-text-color-black ui-btn--disabled` :
                `l-btn-inline l-btn--text-center ui-btn ui-btn--hover-ripple u-bg-color-primary u-text-color-white`
              }
              onClick={() => {
                dispatch(redux.setSelectedTransactionType('system'));
              }}
              >
                <span className="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                <span className="ui-btn-inner-content">
                  <span className="ui-btn-inner-text">
                    {false ? text.trans(strings, returnLang(), "unmatchTxt" ) : text.trans(strings, returnLang(), "matchTxt" )}
                  </span>
                </span>
              </button>
            </div>
            <div className="action">
              <i className="fa fa-pencil edit-icon" onClick={(e) => handleEditTransaction(e, item)}></i>
            </div>
          </div>
        </div>
        {renderJournalDescription()}
      </div>

      {/* Desktop View */}
      <div className={`transaction-record desktop-record ${checked || checkedTransactions.findIndex(p => p.id == item.id) > -1 || props.activeMode ? 'selected': ''}`}>
        {selectedTransactionType == 'bank' && (
          <div className="action">
              <div onClick={handleInputClick}>
                <input 
                  onChange={() => {
                    setChecked(!checked);
                    dispatch(redux.setSystemAmount({ value: item, checked: !checked, data: data}))
                    dispatch(redux.setCheckedTransactions({item, rowChecked: !checked}))
                  }}
                  checked={checked}
                  className="styled-checkbox"
                  id={`styled-checkbox-${item.id}`}
                  type="checkbox"
                  value="value1"/>
                <label htmlFor={`styled-checkbox-${item.id}`}></label>
              </div>
          </div>
        )}
        {renderJournalDateAndIdHTML()}
        {renderJournalDescription()}
        {renderJournalAmount()}
        {(selectedTransactionType === null || selectedTransactionType === 'system') && (
          <div className="action">
            <button className={
                `match-btn ${selectedTransaction && selectedTransaction.id !== item.id ?
                `l-btn-inline l-btn--text-center ui-btn u-bg-color-secondary u-text-color-black ui-btn--disabled` :
                `l-btn-inline l-btn--text-center ui-btn ui-btn--hover-ripple u-bg-color-primary u-text-color-white`}`
            }
            onClick={() => {
              props.handleActiveClass(); // handle Active Class
              handleGetTransaction(item.id);
              if (selectedTransaction) {
                dispatch(redux.setSelectedTransaction(null));
                dispatch(redux.setSelectedTransactionType(null));
              } else {
                dispatch(redux.setSelectedTransactionType('system'));
                dispatch(redux.setSelectedTransaction(item));
              }
            }}
            >
              <span className="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
              <span className="ui-btn-inner-content">
                <span className="ui-btn-inner-text">
                  {props.activeMode ? text.trans(strings, returnLang(), "unmatchTxt" ) : text.trans(strings, returnLang(), "matchTxt" )}
                </span>
              </span>
            </button>
          </div>
        )}
          <div className="action">
            {!props.activeMode && (
              <i className="fa fa-pencil edit-icon" onClick={(e) => handleEditTransaction(e, item)}></i>
            )}
          </div>
      </div>
    </>
  );
}

export default TransactionRecord;
