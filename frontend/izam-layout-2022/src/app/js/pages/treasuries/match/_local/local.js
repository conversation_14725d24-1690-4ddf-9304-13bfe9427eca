const data = {
   en: {
      bankTitle: "Bank Statement",
      systemTitle: "System Transactions",
      searchBankPlaceholder:"Search by Ref ID, Description",
      searchSystemPlaceholder:"Search by Journal Number, Description",
      selectedBankTransaction: "Selected Bank Transaction:",
      back: "Back",
      notMatched: "Not Matched",
      cancel: "Cancel",
      edit: "Edit",
      dateFrom: 'Date From',
      dateTo: 'Date To',
      amountFrom: "Amount From",
      amountTo: "Amount To",
      type: "Type",
      deposit: "Deposit",
      withdraw: "Withdraw",
      description: "Description",
      refId: "Ref",
      filter: "Filter",
      journalNo: "Journal No",
      selectType: "Select Type",
      selectBranch: "Select Branch",
      err: "Something went Wrong!",
      finishMatch: "Finish Matching and Close",
      matchTxt: "Match",
      unmatchTxt: "Unmatch",
      addTransaction: "Add Transaction",
      NoTransactions: "No transactions found for matching",
      branchId: "Branch",
      loading: "loading",
      view: "View",
      download: "Download",
      attachments: "Attachments",
      all: "All",
      addIncome: 'Add Income',
      addExpense: 'Add Expense',
      discard: 'Discard',
      errorEditModal: 'You don’t have the permission to edit this transaction',
      errorAddModal: 'You don’t have the permission to add a new transaction',
      errorflashModal: 'Something went wrong, try again',
      enterNotAllow: 'You are not allowed to access this page'
   },
   ar: {
      bankTitle: "كشف حساب البنك",
      systemTitle: "معاملات النظام",
      searchBankPlaceholder:"ابحث بالرقم المرجعى أو الوصف",
      searchSystemPlaceholder: "ابحث برقم القيد او الوصف",
      selectedBankTransaction: "البنك الذى تم تحديده:",
      back: "رجوع",
      notMatched: "غير متطابقة",
      cancel: "إلغاء",
      edit: "تعديل",
      dateFrom: 'التاريخ من',
      dateTo: 'التاريخ إلى',
      amountFrom: "القيمة من",
      amountTo: "القيمة إلى",
      type: "النوع",
      deposit: "ايداع",
      withdraw: "سحب",
      description: "الوصف",
      refId: "الرقم المرجعى",
      filter: "ابحث",
      journalNo: "رقم القيد",
      selectType: "حدد النوع",
      selectBranch: "حدد الفرع",
      err: "شىء ما خطأ برجاء اعادة المحاولة",
      finishMatch: "إنهاء المطابقة وإغلاق",
      matchTxt: "مطابقة",
      unmatchTxt: "الغاء المطابقة",
      NoTransactions: "لم يتم العثور على معاملات للمطابقة",
      addTransaction: "اضف معاملة",
      branchId: "الفرع",
      loading: "تحميل",
      view: "عرض",
      download: "تحميل",
      attachments: "المرفقات",
      all: "الكل",
      addIncome: "إضافة سند قبض",
      addExpense: "إضافة سند صرف",
      discard: 'تجاهل',
      errorEditModal: 'ليس لديك إذن بتعديل هذه المعاملة',
      errorAddModal: 'ليس لديك إذن لإضافة هذه المعاملة',
      errorflashModal: 'هناك مشكلة, جرب مرة اخرى',
      enterNotAllow: "غير مسموح لك بدخول هذه الصفحة"
   }
};

export default data