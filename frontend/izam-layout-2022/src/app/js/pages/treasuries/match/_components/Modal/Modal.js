import React, { useEffect } from 'react';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import * as redux from '../../_redux/transactionsSlice';
import { text, strings } from '../../_local';
import { returnLang } from '../../_utils';

function formatFileSize (bytes, decimalPoint) {
    if(bytes == 0) return '0 Bytes';
    var k = 1000,
        dm = decimalPoint || 2,
        sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
        i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

function PopUp (props) {
    const {modalType, openModal, modalFilesContent, transactionJournalId, editPermission, modalCustomMessage} = useSelector(state => state.transactions);
    const dispatch = useDispatch();

    useEffect(() => {
    })
    const loadIframe = () => {
        document.querySelector('iframe').contentWindow.document.querySelector("body").style.margin = 0;
        if (modalType === 'journal-modal') {
            const iframe = document.querySelector('.journal-modal iframe');
            let iframeScrollHeight = 0;
            if (iframe.contentWindow.document.scrollingElement.scrollHeight > iframe.contentWindow.document.scrollingElement.getBoundingClientRect().height) {
                iframeScrollHeight = iframe.contentWindow.document.scrollingElement.scrollHeight;
            } else {
                iframeScrollHeight = iframe.contentWindow.document.scrollingElement.getBoundingClientRect().height;
            }
            iframe.style.height = (iframeScrollHeight + 1) + "px";
            iframe.contentWindow.document.querySelector("body").style.margin = 0;
            iframe.contentWindow.document.querySelector("body").style.wordBreak = 'break-all';
        }
    }

    const renderFilesModal = () => {
        return modalType === 'file-modal' && (
            <div className="modal-contents">
                <div className="modal-body">
                    <div className="ui-show-modal-content">
                        <div className="row">
                            <div className="l-flex-col-12">
                                <div className="ui-modal-view-info">
                                    <div className="l-modal-content-header">
                                        <h2 className="ui-attachment-title">{text.trans(strings, returnLang(), 'attachments')}</h2>
                                        <div className="l-modal-close-btn">
                                            <span className="ui-close-btn" onClick={() => dispatch(redux.setCloseModal())}> <i
                                                    className="ui-icon--size-20 fa fa-times"></i></span>
                                        </div>
                                    </div>

                                    <div className="l-attachment-list-h">
                                        {modalFilesContent && modalFilesContent.map(item => (
                                            <div className="ui-attachment-box">
                                                <div className="l-attachment-box-header u-bg-color-secondary">
                                                    <div className="ui-attachment-top-bar">
                                                        <i
                                                            className="ui-icon ui-icon--size-24 u-text-color-action fa fa-file-word"></i>
                                                        <span className="ui-attachment-size">{item.size ? formatFileSize(item.size) : ''}</span>
                                                    </div>
                                                    <span
                                                        className="ui-attachment-txt">{item.name}</span>

                                                </div>
                                                <div className="l-attachment-box-content">
                                                    <a href={item.url} target="_blank" className="ui-attachment-link-item">
                                                        <i
                                                            className="ui-icon--size-16 fa fa-eye ui-view-icon"></i>
                                                        {text.trans(strings, returnLang(), 'view')}
                                                    </a>
                                                    <span className="l-attachment-action">
                                                        <a href={item.url} download={item.name} target="_blank" className="ui-attachment-link-item">
                                                            <i
                                                                className="ui-icon--size-16 ui-download-icon fa fad fa-arrow-to-bottom"></i>
                                                            {text.trans(strings, returnLang(), 'download')}
                                                        </a>

                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    const renderRecordModal = () => {
        return modalType === 'record-modal' && (
            <div className="modal-contents">
                <div className="modal-header">
                    <h3>Record Modal Content</h3>
                    <span onClick={() => dispatch(redux.setCloseModal())}>&times;</span>
                </div>
            </div>
        );
    }

    const renderJournalModal = () => {
        return modalType === 'journal-modal' && (
            <div className="modal-contents">
                <div className="modal-header">
                    <span className="close-modal-icon" onClick={() => dispatch(redux.setCloseModal())}>&times;</span>
                </div>
                <div className="modal-body">
                    <iframe src={`/owner/journals/preview/${transactionJournalId}?box=1`} onLoad={loadIframe} width="100%" height="100%"></iframe>
                </div>
            </div>
        );
    }

    const renderErrorModal = () => {
        return modalType === 'error-modal' && (
            <div className="modal-contents">
                <div className="modal-header">
                    <span className="close-modal-icon" onClick={() => {
                        dispatch(redux.setAddPermission(false));
                        dispatch(redux.setEditPermission(false));
                        dispatch(redux.setCloseModal());
                    }}>&times;</span>
                </div>
                <div className="modal-body">
                    <i className="fa fa-exclamation-triangle u-text-color-dangerligth ui-icon--size-42"></i>
                    <p className="modal-msg">
                        {modalCustomMessage ? modalCustomMessage : (editPermission ? text.trans(strings, returnLang(), 'errorEditModal') : text.trans(strings, returnLang(), 'errorAddModal'))}
                        </p>
                    <button onClick={() => {
                        dispatch(redux.setAddPermission(false));
                        dispatch(redux.setEditPermission(false));
                        dispatch(redux.setCloseModal());
                    }} className="ui-btn u-bg-color-dangerligth u-text-color-white">{text.trans(strings, returnLang(), 'discard')}</button>
                </div>
            </div>
        )
    }

    const renderErrorFlashModal = () => {
        return modalType === 'error-flash' && (
            <div className="modal-contents">
                <div className="modal-header">
                    <span className="close-modal-icon" onClick={() => {
                        dispatch(redux.setAddPermission(false));
                        dispatch(redux.setEditPermission(false));
                        dispatch(redux.setCloseModal());
                    }}>&times;</span>
                </div>
                <div className="modal-body">
                    <i className="fa fa-exclamation-triangle u-text-color-dangerligth ui-icon--size-42"></i>
                    <p className="modal-msg">{text.trans(strings, returnLang(), 'errorflashModal')}</p>
                    <button onClick={() => {
                        dispatch(redux.setAddPermission(false));
                        dispatch(redux.setEditPermission(false));
                        dispatch(redux.setCloseModal());
                    }} className="ui-btn u-bg-color-dangerligth u-text-color-white">{text.trans(strings, returnLang(), 'discard')}</button>
                </div>
            </div>
        )
    }

    return (
            <Modal
                isOpen={openModal}
                onRequestClose={() => dispatch(redux.setCloseModal())}
                ariaHideApp={false}
                contentLabel="Example Modal"
                portalClassName={modalType === 'file-modal' ? 'file-modal' : modalType === 'error-modal' ||  modalType === 'error-flash' ? 'error-modal' : 'journal-modal'}
                style={{
                    content: {
                        top: 'auto',
                        left: 'auto',
                        bottom: 'auto',
                        right: 'auto',
                    }
                }}
            >
                {renderErrorModal()}
                {renderErrorFlashModal()}
                {renderFilesModal()}
                {renderRecordModal()}
                {renderJournalModal()}
            </Modal>
    );
}

export default PopUp;
