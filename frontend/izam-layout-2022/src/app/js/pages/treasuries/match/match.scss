// Style App
#js-app {
  height: 100%;
}
.app {
  height: calc(100vh - 44px);
}
.transactions-content {
  margin: 0 30px;
  display: flex;
  height: 100%;
  position: relative;
  @media (max-width: 800px) {
    // flex-direction: column;
    display: block;
  }
}

.item-link {
  color: var(--color-subtitle);
  &:hover {
    color: var(--color-default);
    text-decoration: none;
  }
}

.bank-transactions,
.system-transactions {
  background: #fff;
  height: 98%;
  overflow: hidden;
  position: relative;
}

.bank-transactions {
  width: 45%;
  @media (max-width: 800px) {
    width: 100%;
  }
}

.system-transactions {
  width: 65%;
  border-left: 1px solid #ccc;
  position: relative;
  @media (max-width: 800px) {
    width: 100%;
  }
  .bank-header {
    display: none;
  }
}

[dir="rtl"] .system-transactions {
  border-right: 1px solid #ccc;
  border-left: 0;
}

.details-bar {
  min-height: 45px;
  display: flex;
  align-items: center;
  font-size: 16px;
  padding: 5px 10px;
}

.details-bar > div {
  // margin: 0 4px;
  display: flex;
  align-items: center;
}

.details-bar > div > div {
  display: flex;
  align-items: center;
}

.details-bar > div > div > *,
.details-bar > div > * {
  margin: 0 4px;
}

.badge-number {
  min-width: 20px;
  width: auto;
  height: 20px;
  padding: 0 6px;
  border-radius: 2px;
  background: var(--color-orange);
  color: #fff;
  text-align: center;
  margin-right: 7px;
  line-height: 20px;
}

.transaction-title {
  font-size: 16px;
  margin-bottom: 0;
}

.details-txt,
.details-currency {
  color: var(--color-subtitle);
}

[dir="rtl"] .details-currency {
  order: 1;
}

.details-total-amount {
  font-weight: bold;
  font-size: 16px;
  color: var(--color-orange);
}

[dir="rtl"] .details-total-amount {
  order: 2;
}

.filter-bar {
  background-color: #f1f4f8;
  position: relative;
  min-height: 50px;
  border: 1px solid #ededed;
  border-right: 0;
  border-left: 0;
  &:hover {
    border-color: #d7d7d7;
  }
  .ui-input {
    &:focus {
      box-shadow: 0 0 0 1px var(--color-default);
    } 
  }
}

[dir=ltr] .filter-bar .l-filter-input-box .l-filter-input-label {
  left: 30px
}

[dir=rtl] .filter-bar .l-filter-input-box .l-filter-input-label {
  right: 30px
}

.filter-items {
  margin: 5px;
  padding-left: 50px
}

[dir=rtl] .filter-items {
  padding-left: 0;
  padding-right: 50px;
}

.filter-item {
  background: var(--color-default);
  color: #fff;
  padding: 2px 7px;
  display: inline-block;
  line-height: 1.2;
  border-radius: 5px;
  margin: 0 3px;
}
.filter-item-remove {cursor: pointer;}
.filter-bar .wrapper-input {
  height: 100%;
  border: 0;
  width: 100%;
  outline: none;
  background-color: #f1f4f8;
  padding: 0 45px;
  color: #7a7a7a;
  font-size: 17px;
  line-height: 50px;
  min-height: 50px;
}

.filter-bar .wrapper-input.remove-padd {
  padding-left: 0px;
}

[dir=rtl] .filter-bar .wrapper-input.remove-padd {
  padding-left: 45px;
  padding-right: 0px;
} 

.filter-bar i {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  color: #000;
  opacity: 0.2;
  z-index: 10;
  font-size: 20px;
  &.filter-icon {
    right: 10px;
    opacity: 0.6;
    font-size: 30px;
    padding: 2px;
    cursor: pointer;
  }

  &.search-icon {
    left: 10px;
  }
}

.filter-bar:hover i.filter-icon {
  background: #d7d7d7;
}

[dir="rtl"] .filter-bar i.filter-icon {
  right: auto;
  left: 10px;
}

[dir="rtl"] .filter-bar i.search-icon {
  left: auto;
  right: 10px;
}

.transaction-wrapper {
  height: calc(100% - 152px);
}

.transaction-records {
  height: 100%;
  overflow: auto;
  position: relative;
  scrollbar-color: #999 #f1f1f1 ;
}

.transaction-records::-webkit-scrollbar {
  width: 5px;
}

/* Track */
.transaction-records::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
.transaction-records::-webkit-scrollbar-thumb {
  background: #999;
}

/* Handle on hover */
.transaction-records::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.scrolling-loader {
  text-align: center;
  padding: 15px 0;
}

.iframe-wrapper {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.transaction-record {
  display: flex;
  padding: 20px;
  border-bottom: 1px solid #d5dbe2;
  box-shadow: 0 1px 0 0 #d5dbe2;
  justify-content: space-between;
  min-height: 86px;
  align-items: center;

  &.bank-item {
    cursor: auto;
    display: none;
  }

  .date-time {
    display: flex;
    flex-direction: column;

    .date {
      color: var(--color-default);
      font-size: 16px;
      font-weight: 600;
    }

    .ref {
      font-size: 14px;
      display: flex;
    }
  }

  .description {
    color: var(--color-subtitle);
    font-size: 12px;
    max-width: 50%;
    // min-height: 38px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    .info {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      margin-top: 5px;
      margin-bottom: 0;
      span {
        margin: 0 8px;
        &:first-child {
          margin: 0;
        }
      }
      i {
        font-size: 14px;
      }
    }
  }

  .amount {
    font-weight: bold;
    font-size: 16px;
    color: var(--color-save);
  }

  &.selected {
    background: #eef9fd;
    border-bottom: 1px solid var(--color-save);
  }

  &.not-done {
    pointer-events: none;
    cursor: not-allowed;
  }

  i.edit-icon {
    color: var(--color-default);
    opacity: 0.2;
    cursor: pointer;
    &:hover {
      color: #001aff;
      opacity: 0.6;
    }
  }

  .match-btn {
    overflow: hidden;
  }
}

.transaction-record.desktop-record {
  .action:first-child {
    width: 5%;
  }
  
  .action:last-child {
    width: 2%;
  }
  .date-time {
    width: 15%;
  }
  .amount {
    width: 15%;
    text-align: right;
  }
  .description {
    width: 45%;
  }
}

.amount--red {
  color: var(--color-danger) !important;
}

[dir=rtl] .transaction-record.desktop-record .amount {
  text-align: left;
}

[dir="rtl"] .transaction-record .date-time .ref span:first-child {
  order: 2;
}

[dir="rtl"] .transaction-record .date-time .ref span:last-child {
  order: 1;
}

.mobile-record {
  display: none;
}

.action-btns {
  display: flex;
  position: absolute;
  bottom: 0;
  width: 100%;
}

.action-btn {
  height: 55px;
  border: 0;
  color: #fff;
  cursor: pointer;
  position: relative;
  width: 100%;
  font-size: 20px;
  &:hover {
    opacity: 0.9;
  }
  .btn-txt {
    font-size: 17px;
  }
}

.finish-matching-btn {
  background-color: var(--color-green);
}

.add-transaction-btn {
  background-color: var(--color-orange);
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  align-items: center;
  .btn-amount {
    font-size: 20px;
  }
}

[dir=rtl] .add-transaction-btn .btn-amount {
  direction: ltr;
  text-align: right;
}

.match-transaction-btn {
  background-color: var(--color-primary);
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  align-items: center;
  .btn-amount {
    font-size: 26px;
    font-weight: bold;
  }
}

.btn-dimmed {
  background-color: #f1f4f8;
  color: #b9bbcd;
  pointer-events: none;
}

.styled-checkbox {
  position: absolute;
  opacity: 0;
}

.styled-checkbox + label {
  position: relative;
  cursor: pointer;
  padding: 0;
  margin-top: 2px;
}

.styled-checkbox + label:before {
  content: "";
  border: 2px solid #d5dbe2;
  display: inline-block;
  vertical-align: text-top;
  width: 20px;
  height: 20px;
  background: white;
}

.styled-checkbox:hover + label:before {
  background: var(--color-primary);
}

.styled-checkbox:focus + label:before {
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.12);
}

.styled-checkbox:checked + label:before {
  background: var(--color-primary);
}

.styled-checkbox:disabled + label {
  color: #b8b8b8;
  cursor: auto;
}

.styled-checkbox:disabled + label:before {
  box-shadow: none;
  background: #ddd;
}

.styled-checkbox:checked + label:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 10px;
  background: white;
  width: 2px;
  height: 2px;
  box-shadow: 2px 0 0 white, 4px 0 0 white, 4px -2px 0 white, 4px -4px 0 white,
    4px -6px 0 white, 4px -8px 0 white;
  transform: rotate(45deg);
}

// Skelton Css

.shimmer {
  max-width: 824px;
  margin: 50px auto;

  &__wrapper {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
  }

  &__item {
    background: #fff;
    min-height: 79px;
    padding: 14px 32px 13px;
    display: flex;
    flex-wrap: wrap;
    &:not(:last-child) {
      border-bottom: 1px solid rgba(40, 45, 58, 0.1);
    }
    > div {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      flex-basis: 100%;
      max-width: 100%;
    }
  }

  &__block {
    animation-duration: 2s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: placeHolderShimmer;
    animation-timing-function: linear;
    animation-delay: 0.5s;
    background-color: #ededed;
    background-image: linear-gradient(
      90deg,
      #ededed 14.36%,
      #d7d6d6 56.29%,
      #ededed 100%
    );
    background-repeat: no-repeat;
    background-size: 244px 104px;
    position: relative;
    height: 19px;
    border-radius: 10px;
    width: 100%;
    margin: 3px 6px 3px 0px;
  }
}

@keyframes placeHolderShimmer {
  0% {
    background-position: calc(0% - 300px) 0;
  }

  20% {
    background-position: calc(0% - 300px) 0;
  }

  80% {
    background-position: calc(100% + 300px) 0;
  }

  100% {
    background-position: calc(100% + 300px) 0;
  }
}

.alert-msg {
  padding: 15px 20px;
  margin: 10px 30px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    cursor: pointer;
    font-size: 18px;
  }
  &.success {
    color: var(--color-success);
    background-color: #d4edda;
  }
  &.danger {
    background-color: #f7d7da;
    color: #721c24;
  }
}

.spinner-loading {
  position: absolute;
  width: 100%;
  right: 0;
  left: 0;
  z-index: 20;
  background-color: rgba(255, 255, 255, 1);
  top: 0;
  bottom: 0;
}

.spinner-loading i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 30;
}

button.active {
  background: red;
}

.filters-inputs {
  // display: flex;
  // flex-wrap: wrap;
  // justify-content: flex-start;
  // margin: 10px;
  // gap: 8px;
  background: #fcfcfc;
  // padding: 10px;
  // border-radius: 4px;
  // border: 1px solid #ddd;
  position: relative;
  z-index: 10;
}

// .filters-inputs .form-group {
//   width: 49%;
// }

// .filters-inputs label {
//   font-size: 12px;
//   color: #9b9b9b;
// }

// .form-group input,
// .form-group select {
//   width: 100%;
//   height: 40px;
//   padding: 5px;
//   border: 1px solid #ededed;
//   border-radius: 2px;
//   margin-bottom: 11px;
//   background: #fff;
// }

// .form-group input:focus,
// .form-group select:focus {
//   outline: 1px solid var(--color-primary);
// }

// .filter-btn {
//   display: block;
//   height: 40px;
//   width: 100%;
//   margin: 10px 5px;
//   background-color: var(--color-primary);
//   color: #fff;
//   opacity: 0.9;
//   border: 0;
//   border-radius: 5px;
//   &:hover {
//     opacity: 1;
//   }
// }

.edit-title {
  font-size: 20px;
  background: #fff;
  padding: 10px;
  box-shadow: 1px 1px 3px #b5b5b5;
  span {
    font-weight: 400;
  }
}

.add-cancel-btn {
  position: absolute;
  right: 107px;
  bottom: 22px;
  padding: 5px 10px;
  min-height: 34px;
  color: #212529;
  background-color: #dbe7f3;
  border-color: #dbe7f3;
  border: 1px solid #c3c3c3;
}

.ReactModal__Overlay {
  z-index: 2000;
  background-color: rgb(0 0 0 / 75%) !important;
}

.ReactModal__Content {
  width: 60%;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%);
}

// .file-modal .ReactModal__Content {
//   height: 240px;
// }

.ReactModal__Content iframe {
  height: 320px;
}

// .journal-modal .ReactModal__Content {
//   height: 450px;
// }

.ReactModal__Content iframe body {
  margin: 0;
}

.error-modal .ReactModal__Content {
  width: 32% !important;
  text-align: center;
}

.error-modal .modal-msg {
  margin: 14px 0;
  padding-bottom: 30px;
  font-size: 17px;
  border-bottom: 1px solid #eee;
}

.error-modal button {
  padding: 15px 20px;
  font-size: 18px;
  font-weight: bold;
}

@media (max-width: 768px) {
  .error-modal .ReactModal__Content {
    width: 80% !important
  }
  
}

.ui-modal-view-info {
  padding: 0;
}

.modal-header {
  display: flex;
  justify-content: flex-end;

  .close-modal-icon {
    font-size: 25px;
    cursor: pointer;
    opacity: 0.7;
    &:hover {
      opacity: 1;
    }
  }
}

@media (max-width: 900px) {
  .transactions-content {
    position: relative;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
  }
  .bank-transactions,
  .system-transactions {
    height: 100%;
    position: absolute;
    top: 0;
  }
  .bank-transactions {
    z-index: 10;
  }
  .system-transactions {
    z-index: 8;
    right: -100%;
    transition: right 0.5s;
  }
  .system-transactions.open-system {
    z-index: 12;
    right: 0;
  }
  .details-bar > div {
    &:first-child {
      flex: 2;
    }
    &:last-child {
      flex: 1;
    }
  }
  .details-bar > div > div {
    flex-direction: column;
    align-items: flex-start;
  }
  .details-bar > div > div .transaction-title {
    order: 2;
  }
  .details-bar > div > div .details-txt {
    order: 1;
  }
  .details-txt {
    font-size: 10px;
  }
  .details-bar > div.amount-currency {
    flex-direction: column;
    align-items: flex-end;
  }
  .details-total-amount {
    order: 2;
  }
  .details-currency {
    order: 1;
    font-size: 10px;
  }
  .filters-inputs .form-group {
    width: 100%;
  }
  .desktop-record {
    display: none;
  }
  .mobile-record {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: normal;
  }
  .record-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .mobile-record .record-header + .description {
    margin-left: 30px;
  }
  .record-header > div {
    display: flex;
    flex: 1;
  }
  .record-header > div .action {
    margin-right: 10px;
  }
  [dir=rtl] .record-header > div .action {
    margin-right: 0;
    margin-left: 10px;
  }
  .record-header > div:last-child {
    justify-content: flex-end;
  }
  .mobile-record .description .info {
    align-items: flex-start;
    flex-direction: column;
  }
  .mobile-record .description .info span {
    margin: 0;
  }
  .mobile-record .amount {
    margin-right: 10px;
  }
  .transaction-wrapper {
    height: 100vh;
  }
  // .transaction-records {
  //   overflow: hidden;
  // }
  // .system-transactions .transaction-records {
  //   height: auto;
  // }
  .transaction-record {
    &.bank-item {
      display: flex;
    }
  }
  .bank-header {
    background: #fff;
    display: flex !important;
    align-items: center;
    padding: 8px 10px;
    justify-content: space-between;
    color: #a1a3b8;
    h4 {
      margin: 0;
      font-size: 14px;
      color: #b4b6ce;
    }
    .back-link {
      cursor: pointer;
      i {
        margin: 0 3px;
      }
      &:hover {
        color: var(--body-text-color);
      }
    }
  }
  .transaction-wrapper {
    height: calc(100% - 150px);
  }
}

.danger-alert {
  color: var(--color-danger);
  padding: 10px;
  font-size: 18px;
  position: absolute;
  font-weight: bold;
  text-transform: capitalize;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

.zero-state {
  padding: 10px;
  color: var(--color-default);
  font-size: 18px;
  position: absolute;
  font-weight: bold;
  text-transform: capitalize;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

.something-wrong {
  padding: 10px;
  color: var(--color-danger);
  font-size: 18px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.did-floating-label-content { 
  position:relative; 
  margin-bottom:20px; 
}
.did-floating-label {
  color:#1e4c82; 
  font-size:13px;
  font-weight:normal;
  position:absolute;
  pointer-events:none;
  left:15px;
  top:11px;
  padding:0 5px;
  background:#fff;
  transition:0.2s ease all; 
  -moz-transition:0.2s ease all; 
  -webkit-transition:0.2s ease all;
}
.did-floating-input {
  font-size:12px;
  display:block;
  width:100%;
  height:36px;
  padding: 0 20px;
  background: #fff;
  color: #323840;
  border: 1px solid #3D85D8;
  border-radius: 4px;
  box-sizing: border-box;
  &:focus{
    outline:none;
    ~ .did-floating-label{
      top:-8px;
      font-size:13px;
    }
  }
}

// ============================= Date Picker Css
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow,
.react-datepicker__navigation-icon::before {
  border-color: #ccc;
  border-style: solid;
  border-width: 3px 3px 0 0;
  content: "";
  display: block;
  height: 9px;
  position: absolute;
  top: 6px;
  width: 9px;
}
.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle,
.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
  margin-left: -4px;
  position: absolute;
  width: 0;
}
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before,
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before,
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::after,
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::after {
  box-sizing: content-box;
  position: absolute;
  border: 8px solid transparent;
  height: 0;
  width: 1px;
  content: "";
  z-index: -1;
  border-width: 8px;
  left: -8px;
}
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before,
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before {
  border-bottom-color: #aeaeae;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
  top: 0;
  margin-top: -8px;
}
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before,
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::after {
  border-top: none;
  border-bottom-color: #f0f0f0;
}
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::after {
  top: 0;
}
.react-datepicker-popper[data-placement^="bottom"]
  .react-datepicker__triangle::before {
  top: -1px;
  border-bottom-color: #aeaeae;
}

.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle {
  bottom: 0;
  margin-bottom: -8px;
}
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before,
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::after {
  border-bottom: none;
  border-top-color: #fff;
}
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::after {
  bottom: 0;
}
.react-datepicker-popper[data-placement^="top"]
  .react-datepicker__triangle::before {
  bottom: -1px;
  border-top-color: #aeaeae;
}

.react-datepicker-wrapper {
  display: inline-block;
  padding: 0;
  border: 0;
  width: 100%;
}

.react-datepicker {
  font-family: "Helvetica Neue", helvetica, arial, sans-serif;
  font-size: 0.8rem;
  background-color: #fff;
  color: #000;
  border: 1px solid #aeaeae;
  border-radius: 0.3rem;
  display: inline-block;
  position: relative;
}

.react-datepicker--time-only .react-datepicker__triangle {
  left: 35px;
}
.react-datepicker--time-only .react-datepicker__time-container {
  border-left: 0;
}
.react-datepicker--time-only .react-datepicker__time,
.react-datepicker--time-only .react-datepicker__time-box {
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.react-datepicker__triangle {
  position: absolute;
  left: 50px;
}

.react-datepicker-popper {
  z-index: 1;
}
.react-datepicker-popper[data-placement^="bottom"] {
  padding-top: 10px;
}
.react-datepicker-popper[data-placement="bottom-end"]
  .react-datepicker__triangle,
.react-datepicker-popper[data-placement="top-end"] .react-datepicker__triangle {
  left: auto;
  right: 50px;
}
.react-datepicker-popper[data-placement^="top"] {
  padding-bottom: 10px;
}
.react-datepicker-popper[data-placement^="right"] {
  padding-left: 8px;
}
.react-datepicker-popper[data-placement^="right"] .react-datepicker__triangle {
  left: auto;
  right: 42px;
}
.react-datepicker-popper[data-placement^="left"] {
  padding-right: 8px;
}
.react-datepicker-popper[data-placement^="left"] .react-datepicker__triangle {
  left: 42px;
  right: auto;
}

.react-datepicker__header {
  text-align: center;
  background-color: #f0f0f0;
  border-bottom: 1px solid #aeaeae;
  border-top-left-radius: 0.3rem;
  padding: 8px 0;
  position: relative;
}
.react-datepicker__header--time {
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}
.react-datepicker__header--time:not(.react-datepicker__header--time--only) {
  border-top-left-radius: 0;
}
.react-datepicker__header:not(.react-datepicker__header--has-time-select) {
  border-top-right-radius: 0.3rem;
}

.react-datepicker__year-dropdown-container--select,
.react-datepicker__month-dropdown-container--select,
.react-datepicker__month-year-dropdown-container--select,
.react-datepicker__year-dropdown-container--scroll,
.react-datepicker__month-dropdown-container--scroll,
.react-datepicker__month-year-dropdown-container--scroll {
  display: inline-block;
  margin: 0 2px;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  margin-top: 0;
  color: #000;
  font-weight: bold;
  font-size: 0.944rem;
}

.react-datepicker-time__header {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.react-datepicker__navigation {
  align-items: center;
  background: none;
  display: flex;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  position: absolute;
  top: 2px;
  padding: 0;
  border: none;
  z-index: 1;
  height: 32px;
  width: 32px;
  text-indent: -999em;
  overflow: hidden;
}
.react-datepicker__navigation--previous {
  left: 2px;
}
.react-datepicker__navigation--next {
  right: 2px;
}
.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button) {
  right: 85px;
}
.react-datepicker__navigation--years {
  position: relative;
  top: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.react-datepicker__navigation--years-previous {
  top: 4px;
}
.react-datepicker__navigation--years-upcoming {
  top: -4px;
}
.react-datepicker__navigation:hover *::before {
  border-color: #a6a6a6;
}

.react-datepicker__navigation-icon {
  position: relative;
  top: -1px;
  font-size: 20px;
  width: 0;
}
.react-datepicker__navigation-icon--next {
  left: -2px;
}
.react-datepicker__navigation-icon--next::before {
  transform: rotate(45deg);
  left: -7px;
}
.react-datepicker__navigation-icon--previous {
  right: -2px;
}
.react-datepicker__navigation-icon--previous::before {
  transform: rotate(225deg);
  right: -7px;
}

.react-datepicker__month-container {
  float: left;
}

.react-datepicker__year {
  margin: 0.4rem;
  text-align: center;
}
.react-datepicker__year-wrapper {
  display: flex;
  flex-wrap: wrap;
  max-width: 180px;
}
.react-datepicker__year .react-datepicker__year-text {
  display: inline-block;
  width: 4rem;
  margin: 2px;
}

.react-datepicker__month {
  margin: 0.4rem;
  text-align: center;
}
.react-datepicker__month .react-datepicker__month-text,
.react-datepicker__month .react-datepicker__quarter-text {
  display: inline-block;
  width: 4rem;
  margin: 2px;
}

.react-datepicker__input-time-container {
  clear: both;
  width: 100%;
  float: left;
  margin: 5px 0 10px 15px;
  text-align: left;
}
.react-datepicker__input-time-container .react-datepicker-time__caption {
  display: inline-block;
}
.react-datepicker__input-time-container
  .react-datepicker-time__input-container {
  display: inline-block;
}
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input {
  display: inline-block;
  margin-left: 10px;
}
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input {
  width: auto;
}
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input[type="time"]::-webkit-inner-spin-button,
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input[type="time"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__input
  input[type="time"] {
  -moz-appearance: textfield;
}
.react-datepicker__input-time-container
  .react-datepicker-time__input-container
  .react-datepicker-time__delimiter {
  margin-left: 5px;
  display: inline-block;
}

.react-datepicker__time-container {
  float: right;
  border-left: 1px solid #aeaeae;
  width: 85px;
}
.react-datepicker__time-container--with-today-button {
  display: inline;
  border: 1px solid #aeaeae;
  border-radius: 0.3rem;
  position: absolute;
  right: -72px;
  top: 0;
}
.react-datepicker__time-container .react-datepicker__time {
  position: relative;
  background: white;
  border-bottom-right-radius: 0.3rem;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box {
  width: 85px;
  overflow-x: hidden;
  margin: 0 auto;
  text-align: center;
  border-bottom-right-radius: 0.3rem;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list {
  list-style: none;
  margin: 0;
  height: calc(195px + (1.7rem / 2));
  overflow-y: scroll;
  padding-right: 0;
  padding-left: 0;
  width: 100%;
  box-sizing: content-box;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item {
  height: 30px;
  padding: 5px 10px;
  white-space: nowrap;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item:hover {
  cursor: pointer;
  background-color: #f0f0f0;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--selected {
  background-color: #216ba5;
  color: white;
  font-weight: bold;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--selected:hover {
  background-color: #216ba5;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--disabled {
  color: #ccc;
}
.react-datepicker__time-container
  .react-datepicker__time
  .react-datepicker__time-box
  ul.react-datepicker__time-list
  li.react-datepicker__time-list-item--disabled:hover {
  cursor: default;
  background-color: transparent;
}

.react-datepicker__week-number {
  color: #ccc;
  display: inline-block;
  width: 1.7rem;
  line-height: 1.7rem;
  text-align: center;
  margin: 0.166rem;
}
.react-datepicker__week-number.react-datepicker__week-number--clickable {
  cursor: pointer;
}
.react-datepicker__week-number.react-datepicker__week-number--clickable:hover {
  border-radius: 0.3rem;
  background-color: #f0f0f0;
}

.react-datepicker__day-names,
.react-datepicker__week {
  white-space: nowrap;
}

.react-datepicker__day-names {
  margin-bottom: -8px;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #000;
  display: inline-block;
  width: 1.7rem;
  line-height: 1.7rem;
  text-align: center;
  margin: 0.166rem;
}

.react-datepicker__month--selected,
.react-datepicker__month--in-selecting-range,
.react-datepicker__month--in-range,
.react-datepicker__quarter--selected,
.react-datepicker__quarter--in-selecting-range,
.react-datepicker__quarter--in-range {
  border-radius: 0.3rem;
  background-color: #216ba5;
  color: #fff;
}
.react-datepicker__month--selected:hover,
.react-datepicker__month--in-selecting-range:hover,
.react-datepicker__month--in-range:hover,
.react-datepicker__quarter--selected:hover,
.react-datepicker__quarter--in-selecting-range:hover,
.react-datepicker__quarter--in-range:hover {
  background-color: #1d5d90;
}
.react-datepicker__month--disabled,
.react-datepicker__quarter--disabled {
  color: #ccc;
  pointer-events: none;
}
.react-datepicker__month--disabled:hover,
.react-datepicker__quarter--disabled:hover {
  cursor: default;
  background-color: transparent;
}

.react-datepicker__day,
.react-datepicker__month-text,
.react-datepicker__quarter-text,
.react-datepicker__year-text {
  cursor: pointer;
}
.react-datepicker__day:hover,
.react-datepicker__month-text:hover,
.react-datepicker__quarter-text:hover,
.react-datepicker__year-text:hover {
  border-radius: 0.3rem;
  background-color: #f0f0f0;
}
.react-datepicker__day--today,
.react-datepicker__month-text--today,
.react-datepicker__quarter-text--today,
.react-datepicker__year-text--today {
  font-weight: bold;
}
.react-datepicker__day--highlighted,
.react-datepicker__month-text--highlighted,
.react-datepicker__quarter-text--highlighted,
.react-datepicker__year-text--highlighted {
  border-radius: 0.3rem;
  background-color: #3dcc4a;
  color: #fff;
}
.react-datepicker__day--highlighted:hover,
.react-datepicker__month-text--highlighted:hover,
.react-datepicker__quarter-text--highlighted:hover,
.react-datepicker__year-text--highlighted:hover {
  background-color: #32be3f;
}
.react-datepicker__day--highlighted-custom-1,
.react-datepicker__month-text--highlighted-custom-1,
.react-datepicker__quarter-text--highlighted-custom-1,
.react-datepicker__year-text--highlighted-custom-1 {
  color: magenta;
}
.react-datepicker__day--highlighted-custom-2,
.react-datepicker__month-text--highlighted-custom-2,
.react-datepicker__quarter-text--highlighted-custom-2,
.react-datepicker__year-text--highlighted-custom-2 {
  color: green;
}
.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range,
.react-datepicker__year-text--in-range {
  border-radius: 0.3rem;
  background-color: #216ba5;
  color: #fff;
}
.react-datepicker__day--selected:hover,
.react-datepicker__day--in-selecting-range:hover,
.react-datepicker__day--in-range:hover,
.react-datepicker__month-text--selected:hover,
.react-datepicker__month-text--in-selecting-range:hover,
.react-datepicker__month-text--in-range:hover,
.react-datepicker__quarter-text--selected:hover,
.react-datepicker__quarter-text--in-selecting-range:hover,
.react-datepicker__quarter-text--in-range:hover,
.react-datepicker__year-text--selected:hover,
.react-datepicker__year-text--in-selecting-range:hover,
.react-datepicker__year-text--in-range:hover {
  background-color: #1d5d90;
}
.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected {
  border-radius: 0.3rem;
  background-color: #2579ba;
  color: #fff;
}
.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__month-text--keyboard-selected:hover,
.react-datepicker__quarter-text--keyboard-selected:hover,
.react-datepicker__year-text--keyboard-selected:hover {
  background-color: #1d5d90;
}
.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range),
.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range),
.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range),
.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range) {
  background-color: rgba(33, 107, 165, 0.5);
}
.react-datepicker__month--selecting-range
  .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range),
.react-datepicker__month--selecting-range
  .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range),
.react-datepicker__month--selecting-range
  .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range),
.react-datepicker__month--selecting-range
  .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range, .react-datepicker__month-text--in-selecting-range, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__year-text--in-selecting-range) {
  background-color: #f0f0f0;
  color: #000;
}
.react-datepicker__day--disabled,
.react-datepicker__month-text--disabled,
.react-datepicker__quarter-text--disabled,
.react-datepicker__year-text--disabled {
  cursor: default;
  color: #ccc;
}
.react-datepicker__day--disabled:hover,
.react-datepicker__month-text--disabled:hover,
.react-datepicker__quarter-text--disabled:hover,
.react-datepicker__year-text--disabled:hover {
  background-color: transparent;
}

.react-datepicker__month-text.react-datepicker__month--selected:hover,
.react-datepicker__month-text.react-datepicker__month--in-range:hover,
.react-datepicker__month-text.react-datepicker__quarter--selected:hover,
.react-datepicker__month-text.react-datepicker__quarter--in-range:hover,
.react-datepicker__quarter-text.react-datepicker__month--selected:hover,
.react-datepicker__quarter-text.react-datepicker__month--in-range:hover,
.react-datepicker__quarter-text.react-datepicker__quarter--selected:hover,
.react-datepicker__quarter-text.react-datepicker__quarter--in-range:hover {
  background-color: #216ba5;
}
.react-datepicker__month-text:hover,
.react-datepicker__quarter-text:hover {
  background-color: #f0f0f0;
}

.react-datepicker__input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.react-datepicker__year-read-view,
.react-datepicker__month-read-view,
.react-datepicker__month-year-read-view {
  border: 1px solid transparent;
  border-radius: 0.3rem;
  position: relative;
}
.react-datepicker__year-read-view:hover,
.react-datepicker__month-read-view:hover,
.react-datepicker__month-year-read-view:hover {
  cursor: pointer;
}
.react-datepicker__year-read-view:hover
  .react-datepicker__year-read-view--down-arrow,
.react-datepicker__year-read-view:hover
  .react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-read-view:hover
  .react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view:hover
  .react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view:hover
  .react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-year-read-view:hover
  .react-datepicker__month-read-view--down-arrow {
  border-top-color: #b3b3b3;
}
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
  transform: rotate(135deg);
  right: -16px;
  top: 0;
}

.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown,
.react-datepicker__month-year-dropdown {
  background-color: #f0f0f0;
  position: absolute;
  width: 50%;
  left: 25%;
  top: 30px;
  z-index: 1;
  text-align: center;
  border-radius: 0.3rem;
  border: 1px solid #aeaeae;
}
.react-datepicker__year-dropdown:hover,
.react-datepicker__month-dropdown:hover,
.react-datepicker__month-year-dropdown:hover {
  cursor: pointer;
}
.react-datepicker__year-dropdown--scrollable,
.react-datepicker__month-dropdown--scrollable,
.react-datepicker__month-year-dropdown--scrollable {
  height: 150px;
  overflow-y: scroll;
}

.react-datepicker__year-option,
.react-datepicker__month-option,
.react-datepicker__month-year-option {
  line-height: 20px;
  width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.react-datepicker__year-option:first-of-type,
.react-datepicker__month-option:first-of-type,
.react-datepicker__month-year-option:first-of-type {
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}
.react-datepicker__year-option:last-of-type,
.react-datepicker__month-option:last-of-type,
.react-datepicker__month-year-option:last-of-type {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}
.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover,
.react-datepicker__month-year-option:hover {
  background-color: #ccc;
}
.react-datepicker__year-option:hover
  .react-datepicker__navigation--years-upcoming,
.react-datepicker__month-option:hover
  .react-datepicker__navigation--years-upcoming,
.react-datepicker__month-year-option:hover
  .react-datepicker__navigation--years-upcoming {
  border-bottom-color: #b3b3b3;
}
.react-datepicker__year-option:hover
  .react-datepicker__navigation--years-previous,
.react-datepicker__month-option:hover
  .react-datepicker__navigation--years-previous,
.react-datepicker__month-year-option:hover
  .react-datepicker__navigation--years-previous {
  border-top-color: #b3b3b3;
}
.react-datepicker__year-option--selected,
.react-datepicker__month-option--selected,
.react-datepicker__month-year-option--selected {
  position: absolute;
  left: 15px;
}

.react-datepicker__close-icon {
  cursor: pointer;
  background-color: transparent;
  border: 0;
  outline: 0;
  padding: 0 6px 0 0;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: table-cell;
  vertical-align: middle;
}
.react-datepicker__close-icon::after {
  cursor: pointer;
  background-color: #216ba5;
  color: #fff;
  border-radius: 50%;
  height: 16px;
  width: 16px;
  padding: 2px;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  display: table-cell;
  vertical-align: middle;
  content: "×";
}

.react-datepicker__today-button {
  background: #f0f0f0;
  border-top: 1px solid #aeaeae;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
  padding: 5px 0;
  clear: left;
}

.react-datepicker__portal {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  left: 0;
  top: 0;
  justify-content: center;
  align-items: center;
  display: flex;
  z-index: 2147483647;
}
.react-datepicker__portal .react-datepicker__day-name,
.react-datepicker__portal .react-datepicker__day,
.react-datepicker__portal .react-datepicker__time-name {
  width: 3rem;
  line-height: 3rem;
}
@media (max-width: 400px), (max-height: 550px) {
  .react-datepicker__portal .react-datepicker__day-name,
  .react-datepicker__portal .react-datepicker__day,
  .react-datepicker__portal .react-datepicker__time-name {
    width: 2rem;
    line-height: 2rem;
  }
}
.react-datepicker__portal .react-datepicker__current-month,
.react-datepicker__portal .react-datepicker-time__header {
  font-size: 1.44rem;
}
