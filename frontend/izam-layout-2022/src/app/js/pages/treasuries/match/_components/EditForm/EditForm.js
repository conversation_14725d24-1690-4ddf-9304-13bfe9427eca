import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { text, strings } from '../../_local';
import * as redux from '../../_redux/transactionsSlice';
import { returnLang } from '../../_utils';

function EditForm() {
  const { editedTransactionItem, bankItem } = useSelector(state => state.transactions);
  const dispatch = useDispatch();
  const iframeRef = useRef();
  const [url, setUrl] = useState(null); 
  const [loading, setLoading] = useState(false); 
  const [itration, setItration] = useState(0);
  useEffect(() => {
    setLoading(true)
    setUrl(iframeRef.current.contentWindow.location.href)
  }, [iframeRef.current])
  
  const loadIframe = (e) => {
    setItration(itration + 1);
    setLoading(false);
    let newUrl = iframeRef.current.contentWindow.location.href;
    let cancelBtn = iframeRef.current.contentWindow.document.querySelector('.cancel-btn');
    if(cancelBtn) {
      cancelBtn.addEventListener('click', function() {
        dispatch(redux.setEditTransaction(false));
        dispatch(redux.setSystemPageNumber(false));
        dispatch(redux.resetFilterSystemOptions());
      })
    }
    let checkIfno = iframeRef.current.contentWindow.document.querySelector('.clip-check.check-info');
    if(checkIfno) {
      checkIfno.remove();
    }

    let errorAppear = iframeRef.current.contentWindow.document.querySelector('.Errormessage');
    let errorMessage = iframeRef.current.contentWindow.document.querySelector('.Errormessage')?.textContent;
    let isPermissionMessage = errorAppear?.classList?.contains('PermissionMessage');    
    let txt = text.trans(strings, returnLang(), "enterNotAllow");

    if(errorAppear) {
      if (isPermissionMessage) {
        dispatch(redux.setModalCustomMessage(''));
      } else {
        dispatch(redux.setModalCustomMessage(errorMessage));
      }
      if(errorAppear.getAttribute('id') === 'flashMessage' && !errorAppear.textContent.trim() === txt) {
        dispatch(redux.setEditPermission(true));
        dispatch(redux.setEditTransaction(false));
        dispatch(redux.setOpenModal());
        dispatch(redux.setModalType("error-flash"));
        dispatch(redux.resetFilterSystemOptions());
      } else {
        dispatch(redux.setEditPermission(true));
        dispatch(redux.setEditTransaction(false));
        dispatch(redux.setOpenModal());
        dispatch(redux.setModalType("error-modal"));
        dispatch(redux.resetFilterSystemOptions());
      }
    }
    
    let form = iframeRef.current.contentWindow.document.querySelector('form');
    if(form) {
      form.addEventListener('submit', function() {
        setLoading(true);
      })
    }
    // console.log("newUrl", newUrl);
    // console.log("url", url);
    // console.log("itration", itration);
    if(window.location.href === newUrl) {
      dispatch(redux.setEditPermission(true));
      dispatch(redux.setEditTransaction(false));
      dispatch(redux.setOpenModal());
      dispatch(redux.setModalType("error-modal"));
      dispatch(redux.resetFilterSystemOptions());
    }
    if(newUrl != url && itration == 1) {
      // console.log("eeeee");
      dispatch(redux.resetData({name: "items", value: []}));
      dispatch(redux.setEditTransaction(false));
      dispatch(redux.setSystemPageNumber(false));
      dispatch(redux.fetchSystemTransactions(bankItem.id));
    }
  }
  return (
    <div className="iframe-wrapper">
        <h2 className="edit-title">{text.trans(strings, returnLang(), "edit" )}{editedTransactionItem.journal.description ? ' :' : '' } <span>{editedTransactionItem.journal.description}</span> </h2>
        <div className="iframe-wrapper">
          <div style={{ display: (loading ? 'block' : 'none'), opacity: (loading ? 1 : 0) }} className="spinner-loading"> <i className="ui-icon ui-icon--size-42 u-text-color-action far fa-spin fa-spinner-third"></i> </div>
          <iframe src={`${editedTransactionItem.link}?box=1&from_matching=1`} ref={iframeRef} onLoad={loadIframe} width="100%" height="530" style={{ height: '100%', opacity: (loading ? 0.1 : 1) }}></iframe>
        </div>
    </div>
  );
}

export default EditForm;
