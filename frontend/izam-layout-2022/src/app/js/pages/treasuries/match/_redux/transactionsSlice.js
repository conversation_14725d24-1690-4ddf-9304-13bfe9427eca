import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

export const fetchBankTransactions = createAsyncThunk('transactions/fetchBankTransactions',
  async (data, thunkAPI) => {
    let filterOptions = thunkAPI.getState().transactions.filterOptions;
    let page = thunkAPI.getState().transactions.bankPage;
    let url = `/v2/api/bank_transactions/${window.matchData.bank.id}/match_transactions`;
    if(data?.journalTransactionId) {
      url = `/v2/api/system_transactions/${data.journalTransactionId}/bank_transactions/${window.matchData.bank.id}`;
    }
    let queryString;
    if(filterOptions) {
      queryString = Object.keys(filterOptions).map(key => key + '=' + filterOptions[key]).join('&');
    }
    const { transactions }  = await(await fetch(`${url}${data?.transactionId ?  '/' + data.transactionId  : ''}${queryString ? '?' + queryString : '' }${queryString && page ? queryString + '&' + page : page && !queryString ? '?' + page : ""}`)).json();
    if(transactions && transactions.data && transactions.data.length > 0) thunkAPI.dispatch(setBankScrollLoading(null));
    return transactions;
  }
)

export const fetchSystemTransactions = createAsyncThunk('transactions/fetchSystemTransactions',
  async (id, thunkAPI) => {
    let filterOptions = thunkAPI.getState().transactions.filterSystemOptions;
    let page = thunkAPI.getState().transactions.systemPageNumber;
    let bankId = thunkAPI.getState().transactions.selectedBankID;
    let url = `/v2/api/system_transactions/${bankId}/match_transactions`;
    if(id) {
      url = `/v2/api/bank_transactions/${id}/system_transactions`;
    }
    let queryString;
    if(filterOptions) {
      queryString = Object.keys(filterOptions).map(key => key + '=' + filterOptions[key]).join('&');
    }
    const { transactions }  = await(await fetch(`${url}${queryString ? '?' + queryString : '' }${queryString && page ? queryString + '&' + page : page && !queryString ? '?' + page : ""}`)).json();
    if(transactions && transactions.data && transactions.data.length > 0) thunkAPI.dispatch(setSystemScrollLoading(null));
    return transactions;
  }
)

export const fetchSearchBankTransactions = createAsyncThunk('transactions/fetchSearchBankTransactions',
  async (info, thunkAPI) => {
    let url = `/v2/api/bank_transactions/${window.matchData.bank.id}/match_transactions`;
    if(info?.journalTransactionId) {
      url = `/v2/api/system_transactions/${info.journalTransactionId}/bank_transactions/${window.matchData.bank.id}`;
    }
    const {transactions} = await(await fetch(`${url}?filter[or][reference_id][like]=${info.ref}&filter[or][description][like]=${info.desc}`)).json()
    return transactions;
  }
)

export const fetchSearchSystemTransactions = createAsyncThunk('transactions/fetchSearchSystemTransactions',
  async (info, thunkAPI) => {
    let bankId = thunkAPI.getState().transactions.selectedBankID;
    let url = `/v2/api/system_transactions/${bankId}/match_transactions`;
    if(info?.id) {
      url = `/v2/api/bank_transactions/${info.id}/system_transactions`;
    }
    const {transactions} = await(await fetch(`${url}?filter[or][journal.number][like]=${info.ref}&filter[or][journal.description][like]=${info.desc}`)).json()
    return transactions;
  }
)

export const fetchAddedTransaction = createAsyncThunk('transactions/fetchAddedTransaction', 
  async (id, thunkAPI) => {
    const data = await(await fetch(`/v2/api/bank_transactions/${window.matchData.bank_account.id}/last_saved_transaction/${id}`)).json();
    return data;
  }
)

export const matchTransactionsApi = createAsyncThunk('transactions/matchTransactionsApi',
  async (info, thunkAPI) => {
    let data = {};
    let url = '';
    if (info.type == 'system') {
      data.bank_transactions = info.ids;
      url = `/v2/api/system_transactions/${info.id}/match_transactions`;
    } else {
      data.system_transactions = info.ids;
      url = `/v2/api/bank_transactions/${info.id}/match_transactions`;
    }
    const requestOptions = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    };
    fetch(url, requestOptions)
        .then(response => response.json())
        .then(data => thunkAPI.dispatch(matchTransactions(data)));
  }
)

const initialState = {
  loading: null,
  err: null,
  items: [],
  matchedTxt: "",
  spinnerLoading: false,
  bankAmount: 0,
  systemAmount: [],
  checkedTransactions: [],
  bankItem: null,
  systemPage: false,
  addTransaction: false,
  editTransaction: false,
  editedTransactionItem: null,
  modalType: "",
  modalCustomMessage: "",
  bankLoading: null,
  bankErr: null,
  bankItems: [],
  openModal: false,
  filterOptions: null,
  filterSystemOptions: null,
  modalFilesContent: null,
  selectedBankID: null,
  transactionJournalId: null,
  matchCode: null,
  remainAddTransaction: null,
  showAlert: false,
  bankPage: null,
  systemPageNumber: null,
  isPageNext: null,
  isSystemPageNext: null,
  bankScrollLoading: null,
  systemScrollLoading: null,
  selectFirstItem: null,
  divHeight: null,
  filterBoxHeight: null,
  matchedFlag: false,
  addedTransactionItem: null,
  editPermission: null,
  addPermission: null,
  selectedTransaction: null,
  selectedTransactionType: null
}

export const transactionsReducer = createSlice({
  name: 'transactions',
  initialState,
    reducers: {
    resetSystemTrasactions: (state) => {
      state.loading = null,
      state.err = null,
      state.items = [],
      state.matchedTxt = "",
      state.spinnerLoading = false,
      state.bankAmount = 0,
      state.systemAmount = [],
      state.checkedTransactions = [],
      state.bankItem = null,
      state.systemPage = false,
      state.addTransaction = false,
      state.editTransaction = false,
      state.editedTransactionItem = null,
      state.modalType = "",
      state.modalCustomMessage = "",
      state.bankLoading = null,
      state.bankErr = null,
      state.openModal = false,
      state.modalFilesContent = null,
      state.selectedBankID = null,
      state.transactionJournalId = null,
      state.remainAddTransaction = null,
      state.showAlert = false,
      state.bankPage = null,
      state.systemPageNumber = null,
      state.isPageNext = null,
      state.matchCode = null,
      state.isSystemPageNext = null,
      state.bankScrollLoading = null,
      state.systemScrollLoading = null,
      state.selectFirstItem = null,
      state.divHeight = null,
      state.filterBoxHeight = null,
      state.addPermission = null,
      state.editPermission = null
    },
    resetFilterOptions: (state) => {
      state.filterOptions = null
    },
    resetData: (state, action) => {
      state[action.payload.name] = action.payload.value;
      // state.filterSystemOptions = null;
    },
    matchTransactions: (state, action) => {
      if(action.payload.code === 200) {
        state.matchedTxt = action.payload.message;
        state.matchCode = action.payload.code;
      } else {
        state.matchedTxt = action.payload.message;
        state.matchCode = action.payload.code;
      }
      state.spinnerLoading = false;
    },
    setMatchedFlag: (state, action) => {
      state.matchedFlag = action.payload
    },
    spinnerLoading: (state) => {
      state.spinnerLoading = true;
    },
    setBankAmount: (state, action) => {
      state.bankAmount = action.payload.amount
    },
    setSystemAmount: (state, action) => {
      const {data, checked, value} = action.payload;
      if(data.length > 0 && checked) {
        state.systemAmount = [...state.systemAmount, value];
      } else if( value.length === 0 || data.length === 0 ){
        state.systemAmount = [];
      } else {
        state.systemAmount = state.systemAmount.filter(i => i.id != value.id)
      }
    },
    setCheckedTransactions: (state, action) => {
      if(action.payload.rowChecked) {
        state.checkedTransactions = [...state.checkedTransactions, action.payload.item];
      } else if( action.payload.item.length === 0) {
        state.checkedTransactions = []
      } else {
        state.checkedTransactions = state.checkedTransactions.filter(i => i.id != action.payload.item.id)
      }
    },
    getBankItem: (state, action) => {
      state.bankItem = action.payload
    },
    getSystemItem: (state, action) => {
      state.systemItem = action.payload
    },
    openSystemPage: (state, action) => {
      state.systemPage = action.payload
    },
    setAddTransaction: (state, action) => {
      state.addTransaction = action.payload
    },
    setEditTransaction : (state, action) => {
      state.editTransaction = action.payload
    },
    setEditedTransactionItem: (state, action) => {
      state.editedTransactionItem = action.payload
    },
    setModalType: (state, action) => {
      state.modalType = action.payload
    },
    setModalCustomMessage: (state, action) => {
      state.modalCustomMessage = action.payload
    },
    setOpenModal: (state) => {
      state.openModal = true
    },
    setCloseModal: (state) => {
      state.openModal = false
    },
    setFilterOptions: (state, action) => {
      state.filterOptions = action.payload      
    },
    resetFilterSystemOptions: (state) => {
      state.filterSystemOptions = null      
    },
    setFilterSystemOptions: (state, action) => {
      state.filterSystemOptions = action.payload      
    },
    setModalFilesContent: (state, action) => {
      state.modalFilesContent = action.payload
    },
    setSelectedBankID: (state, action) => {
      state.selectedBankID = action.payload
    },
    setTransactionJournalId: (state, action) => {
      state.transactionJournalId = action.payload
    },
    setRemainAddTransaction: (state, action) => {
      state.remainAddTransaction = action.payload
    },
    setShowAlert: (state, action) => {
      state.showAlert = action.payload
    },
    setBankPage: (state, action) => {
      state.bankPage = action.payload
    },
    setSystemPageNumber: (state, action) => {
      state.systemPageNumber = action.payload
    },
    setBankScrollLoading: (state, action) => {
      state.bankScrollLoading = action.payload
    },
    setSystemScrollLoading: (state, action) => {
      state.systemScrollLoading = action.payload
    },
    setSelectFirstItem: (state, action) => {
      state.selectFirstItem = action.payload
    },
    setDivHeight: (state, action) => {
      state.divHeight = action.payload
    },
    setFilterBoxHeight: (state, action) => {
      state.filterBoxHeight = action.payload
    },
    setAddTransactionItem: (state, action) => {
      state.addedTransactionItem = null
    },
    removeMatchedItem: (state, action) => {
      state.bankItems = state.bankItems.filter(i => i.id != action.payload.id)
    },
    removeSystemMatchedItem: (state, action) => {
      state.items = state.items.filter(i => i.id != action.payload.id)
    },
    setEditPermission: (state, action) => {
      state.editPermission = action.payload
    },
    setAddPermission: (state, action) => {
      state.addPermission = action.payload
    },
    setSelectedTransactionType: (state, action) => {
      state.selectedTransactionType = action.payload
    },
    setSelectedTransaction: (state, action) => {
      state.selectedTransaction = action.payload
    }
  },
  extraReducers: {
    // Fetch System Transactions
    [fetchSystemTransactions.pending]: (state) => {
      state.loading = true;
    },
    [fetchSystemTransactions.fulfilled] : (state, action) => {
      let data = [...action.payload?.data || []];
      state.items = data;
      state.isSystemPageNext = action.payload.next_page_url;
      state.loading = false;
      state.err = false;
    },
    [fetchSystemTransactions.rejected]: (state) => {
      state.loading = false;
      state.err = true
    },

    // Fetch Bank Transactions
    [fetchBankTransactions.pending]: (state) => {
      state.bankLoading = true;
    },
    [fetchBankTransactions.fulfilled] : (state, action) => {
      state.bankLoading = false;
      state.bankItems = [...action.payload.data];
      state.isPageNext = action.payload.next_page_url;
      state.bankErr = false
    },
    [fetchBankTransactions.rejected]: (state) => {
        state.bankLoading = false;
        state.bankErr = true
    },

    // Fetch Added Transaction 
    [fetchAddedTransaction.pending]: (state) => {
      // state.bankLoading = true;
    },
    [fetchAddedTransaction.fulfilled] : (state, action) => {
      // state.bankLoading = false;
      state.addedTransactionItem = action.payload;
      state.items = [action.payload, ...state.items]
      // state.isPageNext = action.payload.next_page_url;
    },
    [fetchAddedTransaction.rejected]: (state) => {
        // state.bankLoading = false;
        // state.bankErr = true
    },

    // Fetch Search Transaction 
    [fetchSearchBankTransactions.pending]: (state) => {
      state.bankLoading = true;
    },
    [fetchSearchBankTransactions.fulfilled] : (state, action) => {
      state.bankLoading = false;
      state.bankItems = action.payload.data;
      state.bankErr = false
    },
    [fetchSearchBankTransactions.rejected]: (state) => {
        state.bankLoading = false;
        state.bankErr = true
    },

    [fetchSearchSystemTransactions.pending]: (state) => {
      state.loading = true;
    },
    [fetchSearchSystemTransactions.fulfilled] : (state, action) => {
      state.loading = false;
      state.items = action.payload.data;
    },
    [fetchSearchSystemTransactions.rejected]: (state) => {
      state.loading = false;
      state.err = true
    },
  }
})

// Action creators are generated for each case reducer function
export const { 
  resetSystemTrasactions, 
  matchTransactions, 
  spinnerLoading,
  setBankAmount,
  setSystemAmount,
  setCheckedTransactions,
  getBankItem,
  getSystemItem,
  openSystemPage,
  setAddTransaction,
  setEditTransaction,
  setEditedTransactionItem,
  setModalType,
  setModalCustomMessage,
  setOpenModal,
  setCloseModal,
  setFilterOptions,
  setFilterSystemOptions,
  setModalFilesContent,
  setSelectedBankID,
  setTransactionJournalId,
  resetFilterOptions,
  resetData,
  setRemainAddTransaction,
  setShowAlert,
  resetFilterSystemOptions,
  setBankPage,
  setSystemPageNumber,
  setBankScrollLoading,
  setSystemScrollLoading,
  setSelectFirstItem,
  setDivHeight,
  setFilterBoxHeight,
  setAddTransactionItem,
  removeMatchedItem,
  removeSystemMatchedItem,
  setMatchedFlag,
  setAddPermission,
  setEditPermission,
  setSelectedTransactionType,
  setSelectedTransaction
} = transactionsReducer.actions

export default transactionsReducer.reducer