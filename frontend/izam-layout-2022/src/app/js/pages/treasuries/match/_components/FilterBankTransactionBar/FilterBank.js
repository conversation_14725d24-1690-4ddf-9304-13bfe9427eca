import React, {useEffect, useRef, useState} from 'react';
import * as redux from '../../_redux/transactionsSlice';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import DatePicker from "react-datepicker";
import moment from 'moment'
import FilterItem from './FilterItem';
import { text, strings } from '../../_local';
import { returnLang } from '../../_utils';

function FilterBank(props) {
    const { placeholder } = props;
    const ref = useRef(null);
    const inputRef = useRef(null);
    const filterBoxRef = useRef(null);
    
    const { id } = useParams();
    const [inputValue, setInputValue] = useState(false);
    const [inputValues, setInputValues] = useState({});
    const [filter, setFilter] = useState(false);
    const [showFilter, setShowFilters] = useState(false);

    const { filterOptions,bankPage, selectedTransaction } = useSelector(state => state.transactions);
    const dispatch = useDispatch();

    const handleOnChange = event => {
        const { name, value } = event.target;
        setInputValues({ ...inputValues, [name]: value });
        setFilter(false);
        setInputValue(true);
    };

    const listener = event => {
        if (event.code === "Enter" || event.code === "NumpadEnter") {
            event.preventDefault();
        }
    };

    useEffect(() => {
        if(filter) {
            dispatch(redux.resetData({name: "bankItems", value: []}));
            dispatch(redux.setFilterOptions(inputValues));
        }
    }, [filter]);

    useEffect(() => {
        dispatch(redux.resetData({name: "bankItems", value: []}))
        if(filterOptions != null) {
            if(Object.keys(filterOptions).length === 0) {
                dispatch(redux.setBankPage(null));
                setFilter(false);
            } else {
                // dispatch(redux.resetFilterOptions())
            }
            if(Object.keys(filterOptions).length != 0) {
                // window.history.pushState("", "", `?${Object.keys(filterOptions).map(key => key + '=' + filterOptions[key]).join('&')}`)
            } else {
                // window.history.pushState("", "", window.location.pathname);
            }
            if(!bankPage) dispatch(redux.fetchBankTransactions({transactionId: null }))
        // dispatch(redux.resetSystemTrasactions())
        }

    }, [filterOptions])

    const handleClickFilterButton = () => {
        setFilter(true);
        setShowFilters(false);
    }

    const deleteItemFromFilter = (item, setHide) => {
        setHide(false);
        dispatch(redux.resetData({name: "bankItems", value: []}))

        let filters = filterOptions || inputValues;
        for(let key in filters) {
            if(key == item) {
                filters = Object.keys(filters).filter(key =>
                    key !== item).reduce((obj, key) =>
                    {
                        obj[key] = filters[key];
                        return obj;
                    }, {}
                );
            }
        }
        if(Object.keys(filters).length == 0) inputRef.current.value = "";
        if(filters) {
            setInputValues(filters)
            dispatch(redux.setFilterOptions(filters));
        } else {
            // console.log("no filters");
        }
    }

    const handleSearchAndFilter = (e) => {
        dispatch(redux.resetData({name: "bankItems", value: []}));
        if (selectedTransaction) {
            dispatch(redux.fetchSearchBankTransactions({
                journalTransactionId: selectedTransaction.id,
                ref: e.target.value,
                desc: e.target.value
            }))
        } else {
            dispatch(redux.fetchSearchBankTransactions({
                id: null,
                ref: e.target.value,
                desc: e.target.value
            }))
        }
    }

    const handleRefOrDescriptionChange = (e) => {
        if(e.target.value === "") {
            handleSearchAndFilter(e)
        }
        if (e.code === "Enter" || e.code === "NumpadEnter") {
            handleSearchAndFilter(e)
        }
    }

    return (
        
        <>
            <div className="filter-bar">
                <i className="fa fa-search search-icon"></i>
                <div className="" style={{display: 'flex'}} ref={ref}>
                    {filter || (filterOptions != null && Object.keys(filterOptions).length > 0) ? (
                        <div className="filter-items">
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterOptions} item="filter[date][from]" filterName={text.trans(strings, returnLang(), "dateFrom")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterOptions} item="filter[date][to]" filterName={text.trans(strings, returnLang(), "dateTo")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterOptions} item="filter[amount][from]" filterName={text.trans(strings, returnLang(), "amountFrom")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterOptions} item="filter[amount][to]" filterName={text.trans(strings, returnLang(), "amountTo")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterOptions} item="filter[reference_id][equal]" filterName={text.trans(strings, returnLang(), "refId")}/>
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterOptions} item="filter[description][like]" filterName={text.trans(strings, returnLang(), "description")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterOptions} item="filter[type][equal]" filterName={text.trans(strings, returnLang(), "type")} />
                        </div>
                    ) : '' } 
                    <input type="text" onKeyUp={handleRefOrDescriptionChange} placeholder={placeholder} className={`wrapper-input ${filterOptions && Object.keys(filterOptions).length > 0 ? 'remove-padd' : ''}`} ref={inputRef}  />
                </div>
                <i className="mdi mdi-tune filter-icon" onClick={() => setShowFilters(!showFilter)}></i>
            </div>
            {showFilter && ( 
                <div className="ui-table-border filters-inputs" ref={filterBoxRef}>
                    <div className="ui-filter-box-content">
                        <form onSubmit={listener} className="l-flex-row l-flex-row--spacing-8">

                            {
                                filterOptions && filterOptions["filter[reference_id][equal]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-input-w-icon-box l-filter-input-box" data-app-table-filter-input-box="true">
                                                <i className="l-icon mdi mdi-magnify u-text-color-filter ui-icon ui-icon--size-22 ui-input-icon"></i>
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "refId")} </label>
                                                <input type="text" name="filter[reference_id][equal]" onChange={handleOnChange} value={filterOptions && filterOptions["filter[reference_id][equal]"] && inputValues["filter[reference_id][equal]"]} placeholder={text.trans(strings, returnLang(), "refId")} data-app-table-filter-input="true" className="ui-input l-input" />
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                            {
                                filterOptions && filterOptions["filter[type][equal]"] ? '' :(
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box l-filter-input-box--has-value" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "type")} </label>
                                                <select name="filter[type][equal]" onChange={handleOnChange} value={filterOptions && filterOptions["filter[type][equal]"] && inputValues["filter[type][equal]"]} placeholder={text.trans(strings, returnLang(), "type")} data-app-table-filter-input="true" className="ui-select l-input">
                                                    <option selected hidden value="">{text.trans(strings, returnLang(), "selectType")}</option>
                                                    <option value="deposit">{text.trans(strings, returnLang(), "deposit")}</option>
                                                    <option value="withdraw">{text.trans(strings, returnLang(), "withdraw")}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                            
                            {
                                filterOptions && filterOptions["filter[date][from]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-input-w-icon-box l-filter-input-box" data-app-table-filter-input-box="true">
                                                <i className="mdi mdi-calendar-today l-icon l-icon--size-16 u-text-color-default ui-icon ui-icon--size-16 ui-input-icon"></i>
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "dateFrom")} </label>
                                                <DatePicker name="filter[date][from]" onChange={(v) => handleOnChange({target: {name: "filter[date][from]", value: moment(v).format(window.__jsDateFormat)}})} value={filterOptions && filterOptions["filter[date][from]"] || inputValues["filter[date][from]"]} placeholderText={text.trans(strings, returnLang(), "dateFrom")} data-app-table-filter-input="true" className="ui-input l-input" />
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                            {
                                filterOptions && filterOptions["filter[date][to]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-input-w-icon-box l-filter-input-box" data-app-table-filter-input-box="true">
                                                <i className="mdi mdi-calendar-today l-icon l-icon--size-16 u-text-color-default ui-icon ui-icon--size-16 ui-input-icon"></i>
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "dateTo")} </label>
                                                <DatePicker name="filter[date][to]" onChange={(v) => handleOnChange({target: {name: "filter[date][to]", value: moment(v).format(window.__jsDateFormat) }})} value={filterOptions && filterOptions["filter[date][to]"] || inputValues["filter[date][to]"]} placeholderText={text.trans(strings, returnLang(), "dateTo")} data-app-table-filter-input="true" className="ui-input l-input" />
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterOptions && filterOptions["filter[amount][from]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "amountFrom")}</label>
                                                <input type="number" name="filter[amount][from]" onChange={handleOnChange} value={filterOptions && filterOptions["filter[amount][from]"] || inputValues["filter[amount][from]"]} placeholder={text.trans(strings, returnLang(), "amountFrom")} data-app-table-filter-input="true" className="ui-input l-input"/>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterOptions && filterOptions["filter[amount][to]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "amountTo")} </label>
                                                <input type="number" name="filter[amount][to]" onChange={handleOnChange} value={filterOptions && filterOptions["filter[amount][to]"] || inputValues["filter[amount][to]"]} placeholder={text.trans(strings, returnLang(), "amountTo")} data-app-table-filter-input="true" className="ui-input l-input"/>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterOptions && filterOptions["filter[description][like]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "description")} </label>
                                                <input type="text" name="filter[description][like]" onChange={handleOnChange} value={inputValues["filter[description][like]"]} placeholder={text.trans(strings, returnLang(), "description")} data-app-table-filter-input="true" className="ui-input l-input"/> 
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                        
                            <div className="l-input-box l-flex-col-auto l-flex-col--ms-auto">
                                <button style={{pointerEvents: Object.keys(inputValues).length > 0 ? 'auto': 'none', opacity: Object.keys(inputValues).length > 0 ? '1': '0.7'}} className='l-btn-inline l-btn--text-center ui-btn ui-btn--hover-ripple u-bg-color-secondary u-text-color-black u-text-hover-color-primary ui-filter-box-btn l-filter-box-btn' onClick={handleClickFilterButton}>
                                    <span className="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                    <span className="ui-btn-inner-content">
                                        <span className="ui-btn-inner-text">{text.trans(strings, returnLang(), "filter")}</span>
                                    </span>
                                </button> 
                            </div>

                        </form>
                    </div>

                </div>
            )}
        </>
    );
}

export default FilterBank;
