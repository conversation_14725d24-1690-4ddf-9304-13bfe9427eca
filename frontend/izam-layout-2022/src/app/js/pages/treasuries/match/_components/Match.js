import React, { useEffect } from 'react';
import BankTransaction from './BankTransaction';
import SystemTransaction from './SystemTransaction';
import { useSelector, useDispatch } from 'react-redux';
import PopUp from './Modal/Modal';
import { resetSystemTrasactions, removeMatchedItem, setShowAlert, setMatchedFlag, removeSystemMatchedItem } from '../_redux/transactionsSlice';
import { useParams, useNavigate } from 'react-router-dom';
import * as redux from '../_redux/transactionsSlice';

function Match() {
  const { id, transactionId } = useParams();
  const navigate = useNavigate();
  const { matchedTxt, matchCode, spinnerLoading, showAlert, bankItem, systemItem, selectedTransactionType } = useSelector(state => state.transactions);
  const dispatch = useDispatch();

  useEffect(() => {
    if(matchCode === 200) {
      if(transactionId) navigate(`/v2/owner/banks/${window.matchData&&window.matchData.bank.id}/match`);
      setTimeout(() => {
        dispatch(redux.setCheckedTransactions({item: []}));
        dispatch(redux.setSelectedTransactionType(null));
        dispatch(redux.setSelectedTransaction(null));
        dispatch(redux.setSystemAmount({value: [], data: [], checked: false}));
        dispatch(redux.fetchSystemTransactions());
        dispatch(redux.fetchBankTransactions());
        if (selectedTransactionType === 'bank') {
          dispatch(removeMatchedItem(bankItem));
        } else {
          dispatch(removeSystemMatchedItem(systemItem));
        }
        dispatch(setMatchedFlag(true));
      }, 1000);
    }
  }, [matchCode])

  useEffect(() => {
    if(id) {
      dispatch(redux.setSelectedBankID(id));
    }
  }, [id])

  return (
    <>
      {matchedTxt && (
        <div className={`alert-msg ${matchCode == 200 ? 'success' : 'danger'}`} style={{display: showAlert ? 'none' : 'flex'}}>
          {matchedTxt}

          <span onClick={() => dispatch(setShowAlert(true))}> &times; </span>
        </div>
      )}

      <div className='transactions-content'>
        {spinnerLoading ? <div className="spinner-loading"> <i className="ui-icon ui-icon--size-42 u-text-color-action far fa-spin fa-spinner-third"></i> </div> : ''}
        <BankTransaction />
        <SystemTransaction />
      </div>
      
      <PopUp />
    </>
  );
}

export default Match;
