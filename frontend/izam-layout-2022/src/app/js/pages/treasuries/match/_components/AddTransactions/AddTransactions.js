import React, {useEffect, useRef, useState} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { strings, text } from '../../_local';
import * as redux from '../../_redux/transactionsSlice';
import { returnLang } from '../../_utils';

function AddTransactions() {
  const dispatch = useDispatch();
  const {bankItem, remainAddTransaction, items: data} = useSelector(state => state.transactions);
  const iframeRef = useRef();
  const [url, setUrl] = useState(null); 
  const [loading, setLoading] = useState(false); 
  const [itration, setItration] = useState(0);
  
  useEffect(() => {
    setLoading(true);
    setUrl(iframeRef.current.contentWindow.location.href)
  }, [iframeRef.current])

  const loadIframe = (e) => {
    setLoading(false);

    let newUrl = iframeRef.current.contentWindow.location.href;
    let cancelBtn = iframeRef.current.contentWindow.document.querySelector('.cancel-btn');
    if(cancelBtn) {
      cancelBtn.addEventListener('click', function() {
        dispatch(redux.setAddTransaction(false));
        dispatch(redux.setSystemPageNumber(false));
      })
    }
    // dispatch()
    let checkIfno = iframeRef.current.contentWindow.document.querySelector('.clip-check.check-info');
    if(checkIfno) {
      checkIfno.remove();
    }

    let errorAppear = iframeRef.current.contentWindow.document.querySelector('.Errormessage');
    let errorMessage = iframeRef.current.contentWindow.document.querySelector('.Errormessage')?.textContent;
    let isPermissionMessage = errorAppear?.classList?.contains('PermissionMessage');
    let txt = text.trans(strings, returnLang(), "enterNotAllow");
    
    if(errorAppear) {
      if (isPermissionMessage) {
        dispatch(redux.setModalCustomMessage(''));
      } else {
        dispatch(redux.setModalCustomMessage(errorMessage));
      }
      if(errorAppear.getAttribute('id') === 'flashMessage' && !errorAppear.textContent.trim() === txt) {
        dispatch(redux.setAddTransaction(false));
        dispatch(redux.setAddPermission(true));
        dispatch(redux.setOpenModal());
        dispatch(redux.setModalType("error-flash"));
      } else {
        dispatch(redux.setAddTransaction(false));
        dispatch(redux.setAddPermission(true));
        dispatch(redux.setOpenModal());
        dispatch(redux.setModalType("error-modal"));
      }
    }

    let form = iframeRef.current.contentWindow.document.querySelector('form');
    if(form) {
      form.addEventListener('submit', function() {
        setLoading(true);
      })
    }
    let expenseId = iframeRef.current.contentWindow.document.querySelector('input[type=hidden]');

    setItration(itration + 1);
    if(newUrl != url && itration == 1) {
      dispatch(redux.setSystemPageNumber(null));
      dispatch(redux.setAddTransaction(false));
      if(expenseId) dispatch(redux.fetchAddedTransaction(expenseId.value));
    }
  }

  let urlType = bankItem && bankItem.type == "deposit" ? 'incomes' : 'expenses';
  return (
    <div className="iframe-wrapper">
      <div style={{ display: (loading ? 'block' : 'none'), opacity: (loading ? 1 : 0) }} className="spinner-loading"> <i className="ui-icon ui-icon--size-42 u-text-color-action far fa-spin fa-spinner-third"></i> </div>
      <iframe src={`/owner/${urlType}/add?amount=${remainAddTransaction}&bank_transaction_id=${bankItem.id}&source=bank_transaction&box=1&${returnLang()}`} ref={iframeRef} onLoad={loadIframe} width="100%" height="592" style={{ height: '100%' }}></iframe>
    </div>
  );
}

export default AddTransactions;
