import React from 'react';
import { useSelector } from 'react-redux';
import DetailsBar from './DetailsBar/DetailsBar';
import FilterBank from './FilterBankTransactionBar/FilterBank';
import TransactionsBankList from './TrasnactionsBankList/TransactionsBankList';
import { returnLang } from '../_utils';
import { text, strings } from '../_local';

function BankTransaction(props) {

  const { bankAmount, systemAmount, bankItem, selectedTransactionType, selectedTransaction } = useSelector(state => state.transactions);
  const { bankItems: data} = useSelector(state => state.transactions);

  return (
    <div className="bank-transactions">
      {/* { Details Bar } */}
      <DetailsBar title={text.trans(strings, returnLang(), "bankTitle")} amount={selectedTransactionType == 'system' ? systemAmount : bankAmount} badgeNumber={data && data.length} currency={selectedTransactionType == 'system' ? selectedTransaction.currency_code : bankItem && bankItem.bank_transaction_treasury.currency_code} />

      {/* { Filter Bar } */}
      <FilterBank placeholder={text.trans(strings, returnLang(), "searchBankPlaceholder" )} />
      
      {/* { transaction Wrapper } */}
      <TransactionsBankList  />
    </div>
  );
}

export default BankTransaction;
