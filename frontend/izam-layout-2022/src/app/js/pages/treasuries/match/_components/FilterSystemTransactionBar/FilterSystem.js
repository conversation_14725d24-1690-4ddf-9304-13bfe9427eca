import React, {useEffect, useRef, useState} from 'react';
import FilterItem from './FilterItem';
import * as redux from '../../_redux/transactionsSlice';
import { useDispatch, useSelector } from 'react-redux';
import DatePicker from "react-datepicker";
import moment from 'moment';
import { returnLang } from '../../_utils';
import { text, strings } from '../../_local';

function FilterSystem(props) {
    const { placeholder } = props;
    const ref = useRef(null);
    const inputRef = useRef(null);
    const [inputValues, setInputValues] = useState({});
    const [filter, setFilter] = useState(false);
    const [showFilter, setShowFilters] = useState(false);
    
    const { filterSystemOptions, bankItem, selectedBankID, selectedTransaction } = useSelector(state => state.transactions);

    const dispatch = useDispatch();
    
    const handleOnChange = event => {
        const { name, value } = event.target;
        setInputValues({ ...inputValues, [name]: value });
        setFilter(false);
    };

    const listener = event => {
        if (event.code === "Enter" || event.code === "NumpadEnter") {
            event.preventDefault();
        }
    };
    
    useEffect(() => {
        if(filter) {
            dispatch(redux.resetData({name: "items", value: []}));
            dispatch(redux.setFilterSystemOptions(inputValues));
        }
    }, [filter]);

    useEffect(() => {
        if(filterSystemOptions != null) {
            if(Object.keys(filterSystemOptions).length === 0) {
                dispatch(redux.setSystemPageNumber(null));
                setFilter(false)
            }
            if(selectedTransaction) {
                dispatch(redux.fetchSystemTransactions(bankItem.id))
            } else {
                dispatch(redux.fetchSystemTransactions())
            }
        }
    }, [filterSystemOptions])

    const handleClickFilterButton = () => {
        setFilter(true);
        setShowFilters(false);
    }

    const deleteItemFromFilter = (item, setHide) => {
        setHide(false);
        dispatch(redux.resetData({name: "items", value: []}));

        let filters = inputValues;
        for(let key in filters) {
            if(key == item) {
                filters = Object.keys(filters).filter(key =>
                    key !== item).reduce((obj, key) =>
                    {
                        obj[key] = filters[key];
                        return obj;
                    }, {}
                );
            }
        }
        if(Object.keys(filters).length == 0) inputRef.current.value = ""
        if(filters) {
            setInputValues(filters)
            dispatch(redux.setFilterSystemOptions(filters));
        } else {
            // console.log("no filters");
        }
    }   

    const handleSearchAndFilter = (e) => {
        dispatch(redux.resetData({name: "items", value: []}));
        if (selectedTransaction) {
            dispatch(redux.fetchSearchSystemTransactions({
                id: bankItem.id,
                ref: e.target.value,
                desc: e.target.value
            }))
        } else {
            dispatch(redux.fetchSearchSystemTransactions({
                id: null,
                ref: e.target.value,
                desc: e.target.value
            }))
        }
    }


    const handleRefOrDescriptionChange = (e) => {
        if(e.target.value === "") {
            handleSearchAndFilter(e)
        }
        if (e.code === "Enter" || e.code === "NumpadEnter") {
            handleSearchAndFilter(e)
        }
    }

    return (
        <>
            <div className="filter-bar">
                <i className="fa fa-search search-icon"></i>
                <div className="" style={{display: 'flex'}} ref={ref}>
                   {filter || (filterSystemOptions != null && Object.keys(filterSystemOptions).length > 0) ? (
                        <div className="filter-items">
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} item="filter[journal.number][equal]" filterName={text.trans(strings, returnLang(), "journalNo")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} item="filter[journal.entity_type][equal]" filterName={text.trans(strings, returnLang(), "type")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} item="filter[journal.date][from]" filterName={text.trans(strings, returnLang(), "dateFrom")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} item="filter[journal.date][to]" filterName={text.trans(strings, returnLang(), "dateTo")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} item={`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][from]`} filterName={text.trans(strings, returnLang(), "amountFrom")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} item={`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][to]`} filterName={text.trans(strings, returnLang(), "amountTo")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} item="filter[description][like]" filterName={text.trans(strings, returnLang(), "description")} />
                            <FilterItem deleteItemFromFilter={deleteItemFromFilter} inputValues={Object.keys(inputValues).length > 0 ? inputValues : filterSystemOptions} p="branch_id" item="filter[journal.branch_id][equal]" filterName={text.trans(strings, returnLang(), "branchId")}/>
                        </div>
                    ) : '' } 
                    <input type="text" onKeyUp={handleRefOrDescriptionChange} placeholder={placeholder} className={`wrapper-input ${filterSystemOptions && Object.keys(filterSystemOptions).length > 0 ? 'remove-padd' : ''}`} ref={inputRef}/> 
                </div>
                <i className="mdi mdi-tune filter-icon" onClick={() => setShowFilters(!showFilter)}></i>
            </div>
            {showFilter && ( 
                <div className="ui-table-border filters-inputs">

                    <div className="ui-filter-box-content">
                        <form onSubmit={listener} className="l-flex-row l-flex-row--spacing-8">
                            {
                                filterSystemOptions && filterSystemOptions["filter[journal.number][equal]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-input-w-icon-box l-filter-input-box" data-app-table-filter-input-box="true">
                                                <i className="l-icon mdi mdi-magnify u-text-color-filter ui-icon ui-icon--size-22 ui-input-icon"></i>
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "journalNo")}  </label>
                                                <input type="number" name="filter[journal.number][equal]" onChange={handleOnChange} value={inputValues["filter[journal.number][equal]"]} placeholder={text.trans(strings, returnLang(), "journalNo")} data-app-table-filter-input="true" className="ui-input l-input"/>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterSystemOptions && filterSystemOptions["filter[journal.entity_type][equal]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "type")}  </label>
                                                <select name="filter[journal.entity_type][equal]" onChange={handleOnChange} value={inputValues["filter[journal.entity_type][equal]"]} placeholder={text.trans(strings, returnLang(), "type")} data-app-table-filter-input="true" className="ui-select l-input">
                                                    <option selected hidden value="">{text.trans(strings, returnLang(), "selectType")} </option>
                                                    {window.matchData && window.matchData.types ? 
                                                        Object.keys(window.matchData.types).map(i => (
                                                            <option value={window.matchData.types[i]}>{window.matchData.types[i]}</option>
                                                        )) : ""
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterSystemOptions && filterSystemOptions["filter[journal.date][from]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-input-w-icon-box l-filter-input-box" data-app-table-filter-input-box="true">
                                                <i className="mdi mdi-calendar-today l-icon l-icon--size-16 u-text-color-default ui-icon ui-icon--size-16 ui-input-icon"></i>
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "dateFrom")}  </label>
                                                <DatePicker name="filter[journal.date][from]" onChange={(v) => handleOnChange({target: {name: "filter[journal.date][from]", value: moment(v).format(window.__jsDateFormat) }})} value={inputValues["filter[journal.date][from]"]} placeholderText={text.trans(strings, returnLang(), "dateFrom")} data-app-table-filter-input="true" className="ui-input l-input" />
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterSystemOptions && filterSystemOptions["filter[journal.date][to]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-input-w-icon-box l-filter-input-box" data-app-table-filter-input-box="true">
                                                <i className="mdi mdi-calendar-today l-icon l-icon--size-16 u-text-color-default ui-icon ui-icon--size-16 ui-input-icon"></i>
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "dateTo")} </label>
                                                <DatePicker name="filter[journal.date][to]" onChange={(v) => handleOnChange({target: {name: "filter[journal.date][to]", value: moment(v).format(window.__jsDateFormat) }})} value={inputValues["filter[journal.date][to]"]} placeholderText={text.trans(strings, returnLang(), "dateTo")} data-app-table-filter-input="true" className="ui-input l-input" />
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterSystemOptions && filterSystemOptions[`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][from]`] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "amountFrom")}  </label>
                                                <input type="number" name={`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][from]`} onChange={handleOnChange} value={inputValues[`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][from]`]} placeholder={text.trans(strings, returnLang(), "amountFrom")} data-app-table-filter-input="true" className="ui-input l-input"/>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterSystemOptions && filterSystemOptions[`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][to]`] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "amountTo")}  </label>
                                                <input type="number" name={`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][to]`} onChange={handleOnChange} value={inputValues[`filter[${bankItem.type == 'deposit' ? 'debit' : 'credit'}][to]`]} placeholder={text.trans(strings, returnLang(), "amountTo")} data-app-table-filter-input="true" className="ui-input l-input"/>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterSystemOptions && filterSystemOptions["filter[description][like]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "description")}  </label>
                                                <input type="text" name="filter[description][like]" onChange={handleOnChange} value={inputValues["filter[description][like]"]} placeholder={text.trans(strings, returnLang(), "description")} data-app-table-filter-input="true" className="ui-input l-input"/> 
                                            </div>
                                        </div>
                                    </div>
                                )
                            }

                            {
                                filterSystemOptions && filterSystemOptions["filter[journal.branch_id][equal]"] ? '' : (
                                    <div className="l-flex-col-lg-6">
                                        <div className='l-input-box'>
                                            <div className="l-filter-input-box" data-app-table-filter-input-box="true">
                                                <label data-app-table-filter-input-label="true" className="l-filter-input-label"> {text.trans(strings, returnLang(), "type")}  </label>
                                                <select name="filter[journal.branch_id][equal]" onChange={handleOnChange} value={inputValues["filter[journal.branch_id][equal]"]} placeholder={text.trans(strings, returnLang(), "type")} data-app-table-filter-input="true" className="ui-select l-input">
                                                    <option selected hidden>{text.trans(strings, returnLang(), "selectBranch")} </option>
                                                    {window.matchData && window.matchData.branches ? 
                                                        Object.keys(window.matchData.branches).map(i => (
                                                            <option value={i}>{window.matchData.branches[i]}</option>
                                                        )) : ""
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                        
                            <div className="l-input-box l-flex-col-auto l-flex-col--ms-auto">
                                <button style={{pointerEvents: Object.keys(inputValues).length > 0 ? 'auto': 'none', opacity: Object.keys(inputValues).length > 0 ? '1': '0.7'}} className='l-btn-inline l-btn--text-center ui-btn ui-btn--hover-ripple u-bg-color-secondary u-text-color-black u-text-hover-color-primary ui-filter-box-btn l-filter-box-btn' onClick={handleClickFilterButton}>
                                    <span className="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                    <span className="ui-btn-inner-content">
                                        <span className="ui-btn-inner-text">{text.trans(strings, returnLang(), "filter")}</span>
                                    </span>
                                </button> 
                            </div>

                        </form>
                    </div>
                    
                </div>
            )}
        </>
    );
}

export default FilterSystem;
