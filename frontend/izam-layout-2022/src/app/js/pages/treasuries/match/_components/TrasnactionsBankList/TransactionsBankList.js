import React, { useEffect, useState } from 'react';
import TransactionRecord from './TransactionRecord';
import TransactionButton from '../TransactionButton/TransactionButton';
import * as redux from '../../_redux/transactionsSlice'; 
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { text, strings } from '../../_local';
import { returnLang, Skeleton } from '../../_utils';

function TransactionsBankList(props) {
    const { id, transactionId } = useParams();
    const [selectedIndex, setSelectedIndex] = useState(null);
    
    const {matchedFlag, bankLoading: loading, bankPage, bankItems: data, bankErr: err, isPageNext, bankScrollLoading} = useSelector(state => state.transactions);
    const dispatch = useDispatch();

    const handleActiveClass = (index) => {
        let selected = selectedIndex === index ? null : index;	
        if(index == -1) selected = 0;
        setSelectedIndex(selected);
    }


    useEffect(() => {
        if(data.length > 0 && matchedFlag) {
            dispatch(redux.setSystemPageNumber(null));
            dispatch(redux.openSystemPage(true));
            dispatch(redux.setCheckedTransactions({item: []}));
            dispatch(redux.fetchSystemTransactions());
            dispatch(redux.setMatchedFlag(false))
        }
    }, [matchedFlag])

    useEffect(() => {
        const params = id || (window.matchData && window.matchData.bank.id);
        dispatch(redux.fetchBankTransactions({ transactionId: null }));

    }, [bankPage]);
    
    useEffect(() => {
        if(transactionId) { 
            handleActiveClass(0);
        }
    }, [transactionId]);

    useEffect(() => {
        if(data.length === 0) {
            // dispatch(redux.resetData({name: "items", value: []}));
            dispatch(redux.getBankItem(null));
        }
    }, [data])

    const handleScroll = (e) => {
        const bottom = e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
        if (bottom && isPageNext) { 
            dispatch(redux.setBankPage(isPageNext.split("?")[1]));
            dispatch(redux.setBankScrollLoading(true));
        }
    }

    return (
        <div className="transaction-wrapper">
            <div className="transaction-records" onScroll={handleScroll} >
                {loading && <Skeleton count="3" />}
                
                {data && data.length > 0 && data.map((item, index) => (
                    <TransactionRecord 
                        key={item.id}
                        item={item}
                        index={index}
                        activeMode={selectedIndex == index}
                        selectedIndex={selectedIndex}
                        checkId={transactionId}
                        handleActiveClass={() => handleActiveClass(index)}
                    />
                ))}

                {bankScrollLoading && <div className="scrolling-loader"><i className="ui-icon ui-icon--size-42 u-text-color-action far fa-spin fa-spinner-third"></i></div>}   

                {data && data.length == 0 && loading == false && !err && <div className="alert alert-info zero-state"> {text.trans(strings, returnLang(), "NoTransactions" )} </div>}
                
                {err && <div className='alert danger-alert'> {text.trans(strings, returnLang(), "err" )} </div>}
            </div>
            <div className="action-btns">
                <TransactionButton 
                    type="finish" 
                    btnTxt={text.trans(strings, returnLang(), "finishMatch" )} 
                    btnClass="finish-matching-btn"
                    dimmed={(data && data.length > 0 ) || (data && data.length == 0 ) ? false : true}
                />
            </div>
        </div>
    );
}

export default TransactionsBankList;
