import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as redux from '../../_redux/transactionsSlice';
import { formatAmount, formatAmountWithRound } from '../../_utils';
import { useNavigate } from 'react-router-dom'

function TransactionButton(props) {
  const {type, btnTxt, btnClass, dimmed, amount} = props;
  const { checkedTransactions, bankItem, selectedTransaction, selectedTransactionType } = useSelector(state => state.transactions)
  const dispatch = useDispatch();
  const navigate = useNavigate()

  const handleMatchTransactions = (matchedAmount) => {
    dispatch(redux.spinnerLoading());
    setTimeout(function(){
        dispatch(redux.setShowAlert(false));
        let ids = checkedTransactions.map(t => t.id);
        if (selectedTransactionType == 'system') {
          dispatch(redux.matchTransactionsApi({id: selectedTransaction.id, ids, type: 'system'}))
        } else {
          dispatch(redux.matchTransactionsApi({id: bankItem.id, ids, type: 'bank'}))
        }
    }, 2000)
  }

  const handleAddTransactionView = () => {
    dispatch(redux.setAddTransaction(true));
    dispatch(redux.setSystemAmount({value: [], data: [], checked: false}));
    if(type == "add") dispatch(redux.setRemainAddTransaction(amount));
  }

  const handleFinishTransaction = () => {
    let id = window.matchData.bank.id
    window.open('/owner/treasuries/view/' + id , "_self");
  }

  return (
    <>
      {type === "finish" && (
         <button onClick={() => handleFinishTransaction()} className={`action-btn ${btnClass} ${dimmed ? 'btn-dimmed' : ''}`}>{btnTxt}</button>
      )}

      {type === 'match' && (
        <button className={`action-btn ${btnClass} ${dimmed ? 'btn-dimmed' : ''}`} onClick={() => handleMatchTransactions(amount)}>
            <span className="btn-txt">{btnTxt}</span>
            <span className="btn-amount">{formatAmountWithRound(amount)}</span>
        </button>
      )}

      {type === 'add' && (
        <button className={`action-btn ${btnClass} ${dimmed ? 'btn-dimmed' : ''}`} onClick={handleAddTransactionView}>
            <span className="btn-txt">{btnTxt}</span>
            <span className="btn-amount">{formatAmountWithRound(amount)}</span>
        </button>
      )}
    </>

  );
}

export default TransactionButton;
