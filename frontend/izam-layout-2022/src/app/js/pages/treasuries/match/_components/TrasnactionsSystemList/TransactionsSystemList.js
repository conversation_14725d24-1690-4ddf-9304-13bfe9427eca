import React, { useEffect, useState } from 'react';
import TransactionButton from '../TransactionButton/TransactionButton';
import TransactionRecord from './TransactionRecord';
import { useSelector, useDispatch } from 'react-redux';
import * as redux from '../../_redux/transactionsSlice'
import { returnLang, Skeleton } from '../../_utils';
import { text, strings } from '../../_local';
import { useParams } from 'react-router-dom';

function TransactionsSystemList(props) {
    const { bankAmount, checkedTransactions, addedTransactionItem, loading, items: data , err, isSystemPageNext, systemScrollLoading, systemPageNumber, bankItem, selectedTransactionType, matchedFlag} = useSelector(state => state.transactions)
    const [selectedIndex, setSelectedIndex] = useState(null);
    const dispatch = useDispatch();
    const { id, transactionId } = useParams();

    const handleActiveClass = (index) => {
        let selected = selectedIndex === index ? null : index;	
        if(index == -1) selected = 0;
        setSelectedIndex(selected);
    }

    const showAmount = (item) => {
        let bankCurr =  window.matchData && window.matchData.bank.currency_code;
        let systemCurr = window.currency_code;
        if (selectedTransactionType == 'system') {
            return item.amount;
        } else {
            if(systemCurr === bankCurr) {
                return item.debit ? item.debit : item.credit
            } else {
                return item.currency_debit ? item.currency_debit : item.currency_credit
            }
        }
    }

    useEffect(() => {
        if(data, addedTransactionItem) {
            dispatch(redux.setCheckedTransactions({item: data[0], rowChecked: true}));
            dispatch(redux.setAddTransactionItem());
        }
    }, [data]);

    useEffect(() => {
        if(isSystemPageNext, systemScrollLoading) {
            dispatch(redux.fetchSystemTransactions(bankItem.id));
        }
    }, [systemPageNumber]);

    useEffect(() => {
        if(id) {
          dispatch(redux.setSelectedBankID(id));
          if (!transactionId) {
            dispatch(redux.fetchSystemTransactions());
          }
        }
    }, []);

    useEffect(() => {
        if(data.length > 0 && matchedFlag) {
          setSelectedIndex(null);
        }
    }, [matchedFlag]);

    let calcAmountTransactions = checkedTransactions.reduce((acc, item) => acc + showAmount(item), 0);
    let rounded = Math.round(Number(calcAmountTransactions) * 100) / 100;
    calcAmountTransactions=rounded.toFixed(2);

    const handleScroll = (e) => {
        let { scrollHeight, scrollTop, clientHeight } = e.target;
        const bottom = scrollHeight - scrollTop === clientHeight;
        if (bottom && isSystemPageNext) {
            dispatch(redux.setSystemPageNumber(isSystemPageNext.split("?")[1]));
            dispatch(redux.setSystemScrollLoading(true));
        }
    }

    const returnButtonText = () => {
        if(bankItem && bankItem.type === 'deposit') {
            return text.trans(strings, returnLang(), "addIncome");
        } else {
            return text.trans(strings, returnLang(), "addExpense")
        }
    }

    return (
        <div className="transaction-wrapper">
            <div className="transaction-records" onScroll={handleScroll}>
                {loading && <Skeleton count="4" />}

                {data && data.length > 0 && data.map((item, index) => (
                    <TransactionRecord
                        item={item} 
                        index={index}
                        activeMode={selectedIndex == index}
                        selectedIndex={selectedIndex}
                        key={item.id}
                        data={data}
                        handleActiveClass={() => handleActiveClass(index)}
                    />
                ))}

                {systemScrollLoading && <div className="scrolling-loader"><i className="ui-icon ui-icon--size-42 u-text-color-action far fa-spin fa-spinner-third"></i></div>}   

                {data && data.length == 0 && loading == false && !err && <div className="alert alert-info zero-state"> {text.trans(strings, returnLang(), "NoTransactions" )} </div>}           

                {err && <div className="alert danger-alert"> {text.trans(strings, returnLang(), "err" )} </div>}
            </div>
            <div className="action-btns">
                {selectedTransactionType === 'bank' && 
                    <TransactionButton 
                        type="add"
                        btnTxt={returnButtonText()}
                        amount={data.length > 0 ? (bankAmount - calcAmountTransactions) : bankAmount }
                        btnClass="add-transaction-btn"
                        dimmed={(bankAmount - calcAmountTransactions) > 0 ? false : true}
                    />
                }
                <TransactionButton 
                    type="match"
                    btnTxt={text.trans(strings, returnLang(), "matchTxt" )}
                    amount={data.length > 0 ? calcAmountTransactions : 0}
                    btnClass="match-transaction-btn"
                    dimmed={checkedTransactions.length > 0 ? false: true}
                />
            </div>
        </div>
    );
}

export default TransactionsSystemList;
