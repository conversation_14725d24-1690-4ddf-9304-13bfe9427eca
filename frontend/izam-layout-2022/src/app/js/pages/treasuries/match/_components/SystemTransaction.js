import React from 'react';
import DetailsBar from './DetailsBar/DetailsBar';
import EditForm from './EditForm/EditForm';
import AddTransactions from './AddTransactions/AddTransactions';
import TransactionsSystemList from './TrasnactionsSystemList/TransactionsSystemList';
import FilterSystem from './FilterSystemTransactionBar/FilterSystem';
import { useSelector, useDispatch } from 'react-redux';
import { openSystemPage } from '../_redux/transactionsSlice';
import { formatAmount, returnLang } from '../_utils';
import { strings, text } from '../_local';

function SystemTransaction() {
  const { systemAmount, bankAmount, bankItem, systemPage, addTransaction, editTransaction, items: data, selectedTransactionType, selectedTransaction } = useSelector(state => state.transactions);
  const dispatch = useDispatch();
  return (
    <div className={`system-transactions ${systemPage ? "open-system" : ""}`}>
      {editTransaction ? (
        <EditForm />
      ) : addTransaction ? (
        <AddTransactions />
      ) : (
        <>
        {bankItem && Object.keys(bankItem).length > 0 && (
          <>
            <div className='bank-header'>
              <h4>{text.trans(strings, returnLang(), "selectedBankTransaction" )}</h4>
              <span className='back-link' onClick={() => dispatch(openSystemPage(false))}>
                <i className='fa fa-angle-left'></i>
                {text.trans(strings, returnLang(), "back")}
              </span>
            </div>
            <div className={`transaction-record selected bank-item`}>
              <div className="date-time">
                <span className="date">{bankItem.date}</span>
                <span className="ref">{bankItem.ref}</span>
              </div>
              <div className="description">
                <span>{bankItem.description}</span>
              </div>
              <div className={`amount ${bankItem.type == 'deposit' ? '' : 'amount--red' }`}>
                {formatAmount(bankItem.amount)}
              </div>
            </div>
          </>
          )}
        
          <>
            {/* { Details Bar } */}
            <DetailsBar title={text.trans(strings, returnLang(), "systemTitle" )} badgeNumber={data?.length} amount={selectedTransactionType == 'system' ? bankAmount : systemAmount} currency={selectedTransactionType == 'system' ? selectedTransaction.currency_code : bankItem && bankItem.bank_transaction_treasury.currency_code}  />

            {/* { Filter Bar } */}
            <FilterSystem placeholder={text.trans(strings, returnLang(), "searchSystemPlaceholder" )} />

            {/* {transaction Wrapper List} */}
            <TransactionsSystemList />
          </>
        </>
      )}
    </div>
  );
}

export default SystemTransaction;
