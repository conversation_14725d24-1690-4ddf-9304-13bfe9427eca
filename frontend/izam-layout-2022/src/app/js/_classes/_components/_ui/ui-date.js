import flatpickr from 'flatpickr';
import { DateTime } from "luxon";

const dateFormat = window.top.document.documentElement.dataset.siteData ? JSON.parse(window.top.document.documentElement.dataset.siteData).dateFormat : (window?.top?.APP?.USER?.dateFormat ? window.top.APP.USER.dateFormat : 'Y-m-d');
const showHijri = window.top.document.documentElement.dataset.siteData ? JSON.parse(window.top.document.documentElement.dataset.siteData).showHijri : (window?.top?.APP?.USER?.dateShowHijri ? window.top.APP.USER.dateShowHijri : false);


// Hijri Plugin

var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};

function createElement(tag, className, content) {
    var e = window.document.createElement(tag);
    className = className || "";
    content = content || "";
    e.className = className;
    if (content !== undefined)
        e.textContent = content;
    return e;
}

var defaultConfig = {
    showHijriDates: true,
    showHijriToggle: false,
    theme: "light",
    hijriToggleText: "Show Hijri Date",
};
function hijriCalendarPlugin(dateTime, pluginConfig) {
    if (!dateTime || typeof dateTime.fromJSDate === "undefined") {
        throw new Error("hijriCalendarPlugin requires luxon DateTime class.");
    }
    var config = __assign(__assign({}, defaultConfig), pluginConfig);
    return function (fp) {
        var self = {
            luxon: null,
            hijriMonthContainer: null,
            hijriMonthName: null,
        };
        function build() {
            if (!fp.rContainer)
                return;
            self.hijriMonthContainer = createElement("div", "flatpickr-hijri-month-container");
            self.hijriMonthName = createElement("span", "flatpickr-hijri-month-name");
            self.hijriMonthName.innerHTML = "رمضان";
            self.hijriMonthContainer.appendChild(self.hijriMonthName);
            fp.monthNav.insertAdjacentElement("afterend", self.hijriMonthContainer);
            self.hijriMonthContainer.tabIndex = -1;
            buildMonth();
            buildActions();
            return;
        }
        function buildDay(_dObj, _dStr, _fp, dayElem) {
            if (!config.showHijriDates) {
                return;
            }
            var hijriDate = dateTime
                .fromJSDate(dayElem.dateObj)
                .reconfigure({ outputCalendar: "islamic-umalqura" })
                .toFormat("dd");
            var date = dayElem.innerText;
            var wrapper = createElement("span", "flatpickr-hijri-date-wrapper", "");
            var dateEl = createElement("span", "flatpickr-hijri-date-date", date);
            var className = "flatpickr-hijri-date-hijri";
            if (dayElem.classList.contains("nextMonthDay") ||
                dayElem.classList.contains("prevMonthDay")) {
                className += " flatpickr-hijri-date-not-allowed";
            }
            if (dayElem.classList.contains("selected")) {
                className += " flatpickr-hijri-date-selected";
            }
            var hijriEl = createElement("span", className, hijriDate);
            wrapper.appendChild(dateEl);
            wrapper.appendChild(hijriEl);
            dayElem.innerHTML = wrapper.outerHTML;
        }
        function buildMonth() {
            if (!self.hijriMonthContainer || !self.hijriMonthName) {
                return;
            }
            var d = new Date(fp.currentYear, fp.currentMonth);
            var dt = dateTime.fromJSDate(d);
            if (typeof fp.config.locale === "string" &&
                fp.config.locale.startsWith("ar")) {
                dt = dt.setLocale(fp.config.locale);
            }
            dt = dt.reconfigure({
                outputCalendar: "islamic-umalqura",
            });
            var monthBegin = dt.startOf("month").toFormat("LLLL");
            var monthEnd = dt.endOf("month").toFormat("LLLL");
            var yearBegin = dt.startOf("month").toFormat("y");
            var yearEnd = dt.endOf("month").toFormat("y");
            var month;
            if (yearBegin !== yearEnd) {
                if (monthBegin !== monthEnd) {
                    month = "".concat(monthBegin, " ").concat(yearBegin, " / ").concat(monthEnd, " ").concat(yearEnd);
                }
                else {
                    month = monthBegin;
                }
            }
            else {
                if (monthBegin !== monthEnd) {
                    month = "".concat(monthBegin, " / ").concat(monthEnd, " ").concat(yearBegin);
                }
                else {
                    month = "".concat(monthBegin, " ").concat(yearBegin);
                }
            }
            self.hijriMonthName.innerHTML = month;
        }
        function buildActions() {
            var actionsContainer = createElement("div", "flatpickr-hijri-actions ".concat(config.showHijriToggle ? "visible" : "", " ").concat(config.theme, "Theme"), "ACTIONS");
            actionsContainer.innerHTML = "\n        <label for=\"flatpickr-hijri-switch\">".concat(config.hijriToggleText, "</label>\n        <label class=\"flatpickr-hijri-switch\">\n            <input id=\"flatpickr-hijri-switch\" class=\"flatpickr-hijri-switch\" type=\"checkbox\">\n            <span class=\"flatpickr-hijri-slider\"></span>\n        </label>\n      ");
            actionsContainer.tabIndex = -1;
            var confirmDateContainer = fp.calendarContainer.querySelector(".flatpickr-confirm");
            fp.calendarContainer.appendChild(actionsContainer);
            fp.calendarContainer.insertBefore(actionsContainer, confirmDateContainer);
            var switchInput = fp.calendarContainer.querySelector("input.flatpickr-hijri-switch");
            switchInput.checked = true;
            switchInput.addEventListener("change", function (event) {
                var _a;
                config.showHijriDates = (_a = event.target) === null || _a === void 0 ? void 0 : _a.checked;
                if (self.hijriMonthName) {
                    self.hijriMonthName.innerHTML = "";
                }
                if (config.showHijriDates) {
                    buildMonth();
                }
                fp.redraw();
            });
        }
        return {
            onMonthChange: [buildMonth],
            onDayCreate: [buildDay],
            onReady: [build],
        };
    };
}


const defaultOptions = (node) => {
    return ({
        altFormat: dateFormat,
        dateFormat: "Y-m-d",
        allowInput: true,
        altInput: true,
        static: false,
        disableMobile: true,
        onReady: function (selectedDates, dateStr, instance) {
            instance.altInput.setAttribute('dir', 'ltr');
            var $group = $(instance.altInput).closest('[data-app-table-filter-input-box]');
            if ($group.length) {
                var $clearBtn = $group.find('[data-app-table-filter-input-clear]');
                $clearBtn.on('click', function () {
                    instance.clear();
                    $group.removeClass('l-filter-input-box--has-value');
                    $group.removeClass('l-filter-input-box--show-label');
                });
            }
            if ($(instance.input).val().length) {
                $group.addClass('l-filter-input-box--has-value');
                $group.addClass('l-filter-input-box--show-label');
            } else {
                $group.removeClass('l-filter-input-box--has-value');
                $group.removeClass('l-filter-input-box--show-label');
            }
            instance.input.flatpickr = instance;
            instance.altInput.flatpickr = instance;
        },
        onOpen: function (selectedDates, dateStr, instance) {
            var $input = $(instance.altInput);
            var width = $input.innerWidth();
            var $group = $(instance.altInput).closest('[data-app-table-filter-input-box]');
            if ($group.length) {
                width = $group.innerWidth();
            }
            instance.calendarContainer.style.minWidth = width + 'px';
        },
        onClose: function (selectedDates, dateStr, instance) {
            setTimeout(function () {
                var $group = $(instance.altInput).closest('[data-app-table-filter-input-box]');
                if ($group.length && instance.altInput.value && instance.altInput.value.length) {
                    $group.addClass('l-filter-input-box--has-value');
                    $group.addClass('l-filter-input-box--show-label');
                } else {
                    $group.removeClass('l-filter-input-box--has-value');
                    $group.removeClass('l-filter-input-box--show-label');
                }
            }, 1);
        },
        plugins: showHijri ? [
            hijriCalendarPlugin(DateTime),
        ] : [],
    })
};

export default class UiDate {
    initAll() {
        $('[data-app-form-date]').each((i, elm) => {
            this.initDatePicker(elm);
        });
    }
    initAllInNode($node) {
        $node.find('[data-app-form-date]').each((i, elm) => {
            this.initDatePicker(elm);
        });
    }
    initDatePicker(el, options = {}) {
        let modifiedOptions = {...defaultOptions(el), ...options};
        if (el.dataset.appFormDateOptions != null) {
            const options = JSON.parse(el.dataset.appFormDateOptions);
            for (const optionKey in options) {
                if (Object.hasOwnProperty.call(options, optionKey)) {
                    const value = options[optionKey];
                    switch (optionKey) {
                        case 'time':
                            modifiedOptions = {
                                ...modifiedOptions,
                                enableTime: true,
                                time_24hr: true,
                                dateFormat: modifiedOptions.dateFormat + ' H:i',
                                altFormat: modifiedOptions.altFormat + ' H:i',
                            }
                            break;
                    }
                }
            }
        }
        flatpickr(el, modifiedOptions);
    }
}