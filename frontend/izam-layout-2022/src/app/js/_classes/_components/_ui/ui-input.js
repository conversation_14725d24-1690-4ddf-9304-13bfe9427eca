export default class UiInput {
    initAll() {
        $('[data-app-form-textarea]').each((i, elm) => {
            this.autoAdjustHeightForTextArea(elm);
            this.autoAdjustHeightForTextAreaEventListener(elm);
        });
        $('[data-app-form-number]').each((i, elm) => {
            this.preventInvalidCharsForNumber(elm);
        });
    }

    autoAdjustHeightForTextArea(el) {
        el.style.height = 'auto';
        el.style.height = (el.scrollHeight + 3) +'px';
        el.scrollTop = el.scrollHeight;
    }

    autoAdjustHeightForTextAreaEventListener(el) {
        el.addEventListener('input', () => {
            this.autoAdjustHeightForTextArea(el);
        });
    }

    preventInvalidCharsForNumber(el) {
        el.addEventListener('keydown', (e) => {
            const numericalChars = new Set([".","-","+","0","1","2","3","4","5","6","7","8","9"]);
            if (
                (!e.ctrlKey && !e.shiftKey && !e.altKey) &&
                !numericalChars.has(e.key) &&
                (e.code != 'Backspace' && e.code != 'Tab' && e.code != 'ArrowUp' && e.code != 'ArrowDown' && e.code != 'ArrowRight' && e.code != 'ArrowLeft')
                ) {
                e.preventDefault();
            }
        });
    }
}