import Selectize from '@selectize/selectize';
import { contrastColor } from 'contrast-color';
import escapeHtml from 'escape-html';
import { encode } from 'base-64';

const defaultOptions = (el, component = null) => ({
    onChange: (newValue) => {
        if (component && newValue !== component.value) {
            component.value = newValue;
            const event = new Event('change');
            el.dispatchEvent(event);
        }
    },
    selectOnTab: false,
    hideSelected: false,
    loadThrottle: 600,
    plugins: {
        "autofill_disable": {},
        "multiple_remove_button": {},
        "auto_position": {},
        "add_classes": {},
        "blank_option": {},
        "readonly": {},
        "option_deselect": {},
        "preserve_option_attributes": {},
        "disable_auto_scroll": {},
    },
});

function draw_attributes(data) {
    var html = '';
    if (Object.values(data).length && Object.values(data).length > 4) {
        var entries = Object.entries(data).filter(function(e) {
            return e[0] !== '$order' && e[0] !== 'disabled';
        });
        var entriesHtml = '';
        entries.forEach(function(entry, index) {
            entriesHtml = entriesHtml + '"' + entry[0] + '"' + ': ' + (typeof entry[1] === 'string' || entry[1] instanceof String ? '"' + entry[1] + '"' :entry[1]) + (entries.length - 1 == index ? '' : ',');
        });
        html = " data-data='{" + entriesHtml + "}'";
    }
    return html;
}

function escape_html(string) {
    var str = '' + string;
    var matchHtmlRegExp = /["'&<>]/;
    var match = matchHtmlRegExp.exec(str);

    if (!match) {
        return str;
    }

    var escape;
    var html = '';
    var index = 0;
    var lastIndex = 0;

    for (index = match.index; index < str.length; index++) {
        switch (str.charCodeAt(index)) {
            case 34: // "
                escape = '&quot;';
                break;
            case 38: // &
                escape = '&amp;';
                break;
            case 39: // '
                escape = '&#39;';
                break;
            case 60: // <
                escape = '&lt;';
                break;
            case 62: // >
                escape = '&gt;';
                break;
            default:
                continue;
        }

        if (lastIndex !== index) {
            html += str.substring(lastIndex, index);
        }

        lastIndex = index + 1;
        html += escape;
    }

    return lastIndex !== index
        ? html + str.substring(lastIndex, index)
        : html;
}

export default class UiSelect {
    initAll() {
        if (typeof window.uiSelectPluginsAdded == 'undefined') {
            window.uiSelectPluginsAdded = true;
            this.addPluginsAndMethods();
        }
        $('[data-app-form-select]:not([data-exclude])').each((i, elm) => {
            new UiSelectComponent(elm).initCustomizedSelect();
        });
    }
    initAllInNode($node, options = {}) {
        if (typeof window.uiSelectPluginsAdded == 'undefined') {
            window.uiSelectPluginsAdded = true;
            this.addPluginsAndMethods();
        }
        $node.find('[data-app-form-select]').each((i, elm) => {
            new UiSelectComponent(elm, options).initCustomizedSelect();
        });
    }

    addPluginsAndMethods() {
        Selectize.prototype.clearUnselectedOptions = function(silent) {
			var self = this;
	
			self.loadedSearches = {};
			self.userOptions = {};
			self.renderCache = {};
			var options = self.options;
			$.each(self.options, function(key, value) {
				if(self.items.indexOf(key) == -1) {
					delete options[key];
				}
			});
			self.options = self.sifter.items = options;
			self.lastQuery = null;
        }

        Selectize.define('locked_options', function(options) {
            var self = this;
            var selectOptions = self.options;
            var originalRemoveItem = self.removeItem;
            self.removeItem = function(item) {
                console.log(selectOptions[item]);
                if (!(selectOptions[item] && typeof selectOptions[item]['$locked'] !== 'undefined' && selectOptions[item]['$locked'])) {
                    return originalRemoveItem.apply(this, arguments);
                }
            };

        });

        Selectize.define('persistent', function(options) {
            var self = this;
            self.on('focus', function() {
                var originalFocus = self.onFocus;
                return function(e) {
                    var value = self.getItem(self.getValue()).text();
                    self.clear(true);
                    self.setTextboxValue(value);
                    // self.$control_input.select();
                    setTimeout( function () {
                        if (self.settings.selectOnTab) {
                            self.setActiveOption(self.getFirstItemMatchedByTextContent(value));
                        }
                        self.settings.score = null;
                    }, 5);
                    return originalFocus.apply(this, arguments);
                };
            }());

            self.onBlur = (function() {
                var originalBlur = self.onBlur;
                return function(e) {
                    if (self.getValue() === "" && self.lastValidValue !== self.getValue()) {
                        self.setValue(self.lastValidValue);
                    }
                    setTimeout( function () {
                        self.settings.score = function() {
                            return function() {
                                return 1;
                            };
                        };
                    }, 0 );
                    return originalBlur.apply(this, arguments);
                }
            }());
            self.settings.score = function() {
                return function() { return 1; };
            };

        });

        Selectize.define("add_classes", function () {
            var self = this;
            self.on('initialize', () => {
                const template = self.$input.get(0).dataset.appFormSelectTemplate;
                self.$control.addClass(`${template ? 'template-' + template : ''}`);
            });
        });

        Selectize.define("auto_position", function (options) {
            var self = this;

            const POSITION = {
                top: 'top',
                bottom: 'bottom',
            };

            self.positionDropdown = (function() {
                return function() {
                const $control = this.$control;
                const offset = this.settings.dropdownParent === 'body' ? $control.offset() : $control.position();
                offset.top += $control.outerHeight(true);

                const dropdownHeight = this.$dropdown.prop('scrollHeight') + 5; // 5 - padding value;
                const controlPosTop = this.$control.get(0).getBoundingClientRect().top;
                const wrapperHeight = this.$wrapper.height();
                const position = controlPosTop + dropdownHeight + wrapperHeight  > window.innerHeight ? POSITION.top : POSITION.bottom;
                const styles = {
                    'white-space': 'nowrap'
                };

                if ($('html').attr('dir') === 'rtl') {
                    styles.right = offset.right;
                } else {
                    styles.left = offset.left;
                }

                if (this.$control.parents('.l-input-group').length) {
                    styles.minWidth = this.$control.parents('.l-input-group').outerWidth() + 'px';
                }
                styles.width = 'auto';

                if (position === POSITION.top) {
                    Object.assign(styles, {bottom: offset.top, top: 'unset', margin: '0'});
                    this.$dropdown.addClass('selectize-position-top');
                } else {
                    Object.assign(styles, {top: offset.top, bottom: 'unset', margin: '0'});
                    this.$dropdown.removeClass('selectize-position-top');
                }

                this.$dropdown.css(styles);
                }
            }());
        });

        Selectize.define('multiple_remove_button', function(options) {
            if (this.settings.mode !== 'single') {
                options = $.extend({
                        label     : '&times;',
                        title     : 'Remove',
                        className : 'remove',
                        append    : true
                    }, options);

                    // var singleClose = function(thisRef, options) {

                    //     options.className = 'remove-single';

                    //     var self = thisRef;
                    //     var html = '<a href="javascript:void(0)" class="' + options.className + '" tabindex="-1" title="' + escapeHtml(options.title) + '">' + options.label + '</a>';

                    //     /**
                    //      * Appends an element as a child (with raw HTML).
                    //      *
                    //      * @param {string} html_container
                    //      * @param {string} html_element
                    //      * @return {string}
                    //      */
                    //     var append = function(html_container, html_element) {
                    //         return $('<span>').append(html_container)
                    //             .append(html_element);
                    //     };

                    //     thisRef.setup = (function() {
                    //         var original = self.setup;
                    //         return function() {
                    //             // override the item rendering method to add the button to each
                    //             if (options.append) {
                    //                 var id = $(self.$input.context).attr('id');
                    //                 var selectizer = $('#'+id);

                    //                 var render_item = self.settings.render.item;
                    //                 self.settings.render.item = function(data) {
                    //                     return append(render_item.apply(thisRef, arguments), html);
                    //                 };
                    //             }

                    //             original.apply(thisRef, arguments);

                    //             // add event listener
                    //             thisRef.$control.on('click', '.' + options.className, function(e) {
                    //                 e.preventDefault();
                    //                 if (self.isLocked) return;

                    //                 self.clear();
                    //             });

                    //         };
                    //     })();
                    // };

                    var multiClose = function(thisRef, options) {

                        var self = thisRef;
                        var html = '<a href="javascript:void(0)" class="' + options.className + '" tabindex="-1" title="' + escapeHtml(options.title) + '"><i class="fal fa-times"></i></a>';

                        /**
                         * Appends an element as a child (with raw HTML).
                         *
                         * @param {string} html_container
                         * @param {string} html_element
                         * @return {string}
                         */
                        var append = function(html_container, html_element) {
                            var pos = html_container.search(/(<\/[^>]+>\s*)$/);
                            return html_container.substring(0, pos) + html_element + html_container.substring(pos);
                        };

                        thisRef.setup = (function() {
                            var original = self.setup;
                            return function() {
                                // override the item rendering method to add the button to each
                                if (options.append) {
                                    var render_item = self.settings.render.item;
                                    self.settings.render.item = function(data) {
                                        return append(render_item.apply(thisRef, arguments), html);
                                    };
                                }

                                original.apply(thisRef, arguments);

                                // add event listener
                                thisRef.$control.on('click', '.' + options.className, function(e) {
                                    e.preventDefault();
                                    if (self.isLocked) return;

                                    var $item = $(e.currentTarget).parent();
                                    self.setActiveItem($item);
                                    if (self.deleteSelection()) {
                                        self.setCaret(self.items.length);
                                    }
                                    return false;
                                });

                            };
                        })();
                    };

                // if (this.settings.mode === 'single') {
                    // singleClose(this, options);
                    // return;
                // } else {
                    multiClose(this, options);
                // }
            }
        });

        Selectize.define("autofill_disable", function (options) {
            var self = this;
        
            self.setup = (function () {
            var original = self.setup;
            return function () {
                original.apply(self, arguments);
        
                // https://stackoverflow.com/questions/30053167/autocomplete-off-vs-false
                self.$control_input.attr({ autocomplete: "off", autofill: "no" });
            };
            })();
        });

        Selectize.define("append_values_to", function (options) {
            var self = this;
            const appendValues = () => {
                self.$control_input.attr('placeholder', self.settings.placeholder);
                $(options.selector).html('');
                const values = self.getValue();
                for (const id of values) {
                    const selectedValueHtml = document.createElement('span');
                    selectedValueHtml.classList.add('l-select-value', 'ui-select-value');
                    const selectedValueTextHtml = document.createElement('span');
                    selectedValueTextHtml.classList.add('ui-select-value-content');
                    selectedValueTextHtml.innerText = Object.values(self.options).find((obj) => obj.value == id).text;
                    const selectedValueRemoveIconHtml = document.createElement('i');
                    selectedValueRemoveIconHtml.classList.add('ui-select-value-remove-icon', 'fal', 'fa-times');
                    selectedValueRemoveIconHtml.addEventListener('click', () => {
                        self.removeItem(id);
                    });
                    selectedValueHtml.appendChild(selectedValueTextHtml);
                    selectedValueHtml.appendChild(selectedValueRemoveIconHtml);
                    $(options.selector).append(selectedValueHtml);
                }
            }
            self.onChange = () => {
                appendValues();
            }
            self.on('initialize', () => {
                appendValues();
            });
        });

        Selectize.define("blank_option", function (options) {
            var self = this;
            self.on('change', () => {
                let values = self.getValue();
                if (!Array.isArray(values)) {
                    values = [values];
                }
                const isBlankOption = values.find(id => id === '__blank__');
                if (isBlankOption) {
                    self.setValue('');
                }
            });
        });

        Selectize.define("readonly", function (options) {
            var self = this;
            self.on('initialize', () => {
                if (self.$input.get(0).attributes.readonly) {
                    self.lock();
                }
                self.$input.on('change', () => {
                    if (self.$input.get(0).attributes.readonly) {
                        self.lock();
                    } else {
                        self.unlock();
                    }
                });
                self.on('change', () => {
                    if (self.$input.get(0).attributes.readonly) {
                        self.lock();
                    } else {
                        self.unlock();
                    }
                });
            });
        });
        
        // Selectize.define("input_icon", function (options) {
        //     var self = this;
        //     const loadIcon = () => {
        //         self.$control.find('i').remove();
        //         const iconClass = options.iconClass;
        //         const iconHtml = document.createElement('i');
        //         iconClass.split(' ').map((e) => iconHtml.classList.add(e));
        //         self.$control.append(iconHtml);
        //     }
        //     self.on('initialize', () => {
        //         loadIcon()
        //     });
        //     self.onChange = () => {
        //         loadIcon();
        //     }
        // });
        

        Selectize.define("dropdown_footer", function (options) {
            var self = this;
        
            options = $.extend({
                title : 'Add',
                icon  : 'fa fa-plus',
                class : 'l-btn ui-btn u-bg-color-primary u-text-color-white u-bg-hover-color-default',
                type  : 'button',
                attributes : [],
        
                html: function(data) {
                    let htmlAttributes = '';
                    if (data.attributes.length) {
                        data.attributes.map(attr => {
                            htmlAttributes = htmlAttributes + `${Object.keys(attr)[0]}="${Object.values(attr)[0]}"`
                        });
                    }
                    return `
                        <${data.type} class="${data.class}" ${htmlAttributes} ${data.type == 'button' ? 'type="button"' : ''} style="position: relative;top: 1px;">
                            ${data.icon ? '<i class="'+ data.icon +'" style="pointer-events: none;"></i>&nbsp;&nbsp;' : ''}
                            <span style="pointer-events: none;">${data.title}</span>
                        </${data.type}>
                    `;
                }
            }, options);

            self.on('type', function(str) {
                self.$dropdown.show();
            });

            self.on('focus', function() {
                var originalFocus = self.onFocus;
                return function(e) {
                    self.positionDropdown();
                    self.$dropdown.show();
                    return originalFocus.apply(this, arguments);
                };
            }());
        
            self.setup = (function() {
                var original = self.setup;
                return function() {
                    original.apply(self, arguments);
                    self.$dropdown_footer = $(options.html(options));
                    self.$dropdown.append(self.$dropdown_footer);
                };
            })();
        });
        

        Selectize.define("dropdown_header", function (options) {
            var self = this;
        
            options = $.extend({
                title : 'Add',
                icon  : 'fa fa-plus',
                class : 'l-btn ui-btn u-bg-color-primary u-text-color-white u-bg-hover-color-default',
                type  : 'button',
                attributes : [],
        
                html: function(data) {
                    let htmlAttributes = '';
                    if (data.attributes.length) {
                        data.attributes.map(attr => {
                            htmlAttributes = htmlAttributes + `${Object.keys(attr)[0]}="${Object.values(attr)[0]}"`
                        });
                    }
                    return `
                        <${data.type} class="${data.class}" ${htmlAttributes} ${data.type == 'button' ? 'type="button"' : ''} style="position: relative;top: 1px;">
                            ${data.icon ? '<i class="'+ data.icon +'" style="pointer-events: none;"></i>&nbsp;&nbsp;' : ''}
                            <span style="pointer-events: none;">${data.title}</span>
                        </${data.type}>
                    `;
                }
            }, options);

            self.on('type', function(str) {
                self.$dropdown.show();
            });

            self.on('focus', function() {
                var originalFocus = self.onFocus;
                return function(e) {
                    self.positionDropdown();
                    self.$dropdown.show();
                    return originalFocus.apply(this, arguments);
                };
            }());
        
            self.setup = (function() {
                var original = self.setup;
                return function() {
                    original.apply(self, arguments);
                    self.$dropdown_header = $(options.html(options));
                    self.$dropdown.prepend(self.$dropdown_header);
                };
            })();
        });
        

        Selectize.define("ajax_helper_text", function (options) {
            var self = this;
        
            options = $.extend({
                title: __("Please enter 1 keyword or more to search"),
                noResultsTitle: __("No results found"),
                class: 'selectize-helper-text',
                icon: '',
                type: 'div',
                attributes: [],
        
                html: function(data) {
                    let htmlAttributes = '';
                    if (data.attributes.length) {
                        data.attributes.map(attr => {
                            htmlAttributes = htmlAttributes + `${Object.keys(attr)[0]}="${Object.values(attr)[0]}"`
                        });
                    }
                    return `
                        <${data.type} class="${data.class}" ${htmlAttributes} ${data.type == 'button' ? 'type="button"' : ''} style="position: relative;top: 1px;">
                            ${data.icon ? '<i class="'+ data.icon +'" style="pointer-events: none;"></i>&nbsp;&nbsp;' : ''}
                            <span style="pointer-events: none;" data-title>${data.title}.</span>
                            <span style="pointer-events: none;" data-no-results-title>${data.noResultsTitle}.</span>
                        </${data.type}>
                    `;
                }
            }, options);

            self.refreshOptions = (function () {
                var original = self.refreshOptions;
                return function () {
                    original.apply(self, arguments);
                    if (typeof self.$ajax_helper_text !== 'undefined') {
                        if (this.hasOptions || !this.lastQuery) {
                            self.$ajax_helper_text.find('[data-title]').show();
                            self.$ajax_helper_text.find('[data-no-results-title]').hide();
                        } else {
                            self.$ajax_helper_text.find('[data-title]').hide();
                            self.$ajax_helper_text.find('[data-no-results-title]').show();
                        }
                    }
                }
            })();
            
            self.on('type', function(str) {
                self.$dropdown.show();
            });

            self.on('focus', function() {
                var originalFocus = self.onFocus;
                return function(e) {
                    self.positionDropdown();
                    self.$dropdown.show();
                    return originalFocus.apply(this, arguments);
                };
            }());
        
            self.setup = (function() {
                var original = self.setup;
                return function() {
                    original.apply(self, arguments);
                    self.$ajax_helper_text = $(options.html(options));
                    self.$dropdown.prepend(self.$ajax_helper_text);
                };
            })();
        });



        /**
         * Multiple Plus Plugin
         */

        if (typeof Selectize !== 'undefined') {
            Selectize.define("multiple_plus", function (pluginOptions) {
                var instance = this;
                if (instance.settings.mode === 'multi') {
                    function handleSelectedOptions() {
                        // if (instance.getValue().length < 2 && typeof instance.oneLineLimit == 'undefined') {
                        //     return;
                        // }

                        // if (typeof instance.oneLineLimit == 'undefined') {
                        //     instance.oneLineLimit = 0;
                        // }

                        // if (typeof instance.startHiding == 'undefined') {
                        //     instance.startHiding = false;
                        // }

                        // if (instance.getValue().length < instance.oneLineLimit || instance.oneLineLimit == 0) {
                        //     var $visibleItems = instance.$control.find('.item[data-value]');
                        //     instance.startHiding = false;
                        //     instance.oneLineLimit = 0;
                        //     instance.$control.find('.count').remove();
                        //     $visibleItems.show();
                        //     instance.positionDropdown();
                        // }

                        // if (instance.getValue().length && instance.$control.get(0).offsetHeight > 42 && !instance.startHiding) {
                        //     instance.oneLineLimit = instance.getValue().length - 1;
                        //     instance.startHiding = true;
                        // }

                        // if (instance.startHiding && instance.getValue().length > instance.oneLineLimit) {
                        //     var hiddenItemsCount = instance.getValue().length - instance.oneLineLimit;
                        //     var $hiddenItems = instance.$control.find('.item[data-value]:nth-child(n+' + (instance.oneLineLimit + 1) + ')');
                        //     var $visibleItems = instance.$control.find('.item[data-value]:nth-child(-n+' + (instance.oneLineLimit) + ')');
                        //     $hiddenItems.hide();
                        //     $visibleItems.show();
                        //     if (instance.$control.find('.count').length) {
                        //         instance.$control.find('.count').text('+' + (hiddenItemsCount));
                        //     } else {
                        //         instance.$control.append('<div class="item count">+' + (hiddenItemsCount) + '</div>');
                        //     }
                        //     instance.positionDropdown();
                        // }

                        // if (instance.getValue().length == instance.oneLineLimit) {
                        //     instance.$control.find('.count').remove();
                        //     instance.$control.find('.item').show();
                        // }

                        if (instance.initialHeight == 0) {
                            instance.$control.find('.item[data-value]:nth-child(n+2)').hide();
                            var hiddenItemsCount = 0;
                            instance.$control.find('.item[data-value]').each(function(i) {
                                if ($(this).attr('style')) {
                                    hiddenItemsCount = hiddenItemsCount + 1;
                                }
                            });
                            if (hiddenItemsCount) {
                                if (instance.$control.find('.count').length) {
                                    instance.$control.find('.count').text('+' + hiddenItemsCount);
                                } else {
                                    instance.$control.append('<div class="item count">+' + (hiddenItemsCount) + '</div>');
                                }
                            }
                            instance.initialHeight = instance.$control.get(0).offsetHeight;
                        } else {

                            if (instance.getValue().length) {
                                instance.$control.find('.item[data-value]').show();
                                if (instance.getValue().length == 1) {
                                    instance.$control.find('.count').remove();
                                    instance.$control.find('.item[data-value]').show();
                                } else if (instance.$control.get(0).offsetHeight > instance.initialHeight) {
                                    var top = 0;
                                    var hiddenItemsCount = 0;
                                    var lastVisibleItem = null;
                                    instance.$control.find('.item[data-value]').each(function(i) {
                                        if (i == 0) {
                                            top = this.offsetTop;
                                        }
                                        var action = this.offsetTop == top ? 'show' : 'hide';
                                        if (action == 'show') {
                                            lastVisibleItem = this;
                                        }
                                        $(this)[action]();
                                        if (!$(this).is(':visible')) {
                                            hiddenItemsCount = hiddenItemsCount + 1;
                                        }
                                    });
                                    if (instance.$control.find('.item[data-value]')[0] !== lastVisibleItem) {
                                        $(lastVisibleItem).hide();
                                        hiddenItemsCount = hiddenItemsCount + 1;
                                    }
                                    if (hiddenItemsCount) {
                                        if (instance.$control.find('.count').length) {
                                            instance.$control.find('.count').text('+' + hiddenItemsCount);
                                        } else {
                                            instance.$control.append('<div class="item count">+' + (hiddenItemsCount) + '</div>');
                                        }
                                        instance.positionDropdown();
                                    }
                                }
                            } else {
                                instance.$control.find('.count').remove();
                                instance.$control.find('.item').show();
                            }
                        }
                    }

                    instance.on('change', function () {
                        handleSelectedOptions();
                    });
                    instance.initialHeight = instance.$input.get(0).offsetHeight;
                    instance.on('initialize', function () {
                        handleSelectedOptions();
                    });
                }
            });
        }



        /**
         * Disable Auto scroll Plugin
         */

        if (typeof Selectize !== 'undefined') {
            Selectize.define("disable_auto_scroll", function () {
                var instance = this;
                instance.setActiveOption = function ($option, scroll, animate) {
                    var self = this;
            
                    if (self.$activeOption) {
                        self.$activeOption.removeClass('active');
                        self.trigger('dropdown_item_deactivate', self.$activeOption.attr('data-value'));
                    }
                    self.$activeOption = null;
    
                    $option = $($option);
                    if (!$option.length) return;
            
                    self.$activeOption = $option.addClass('active');
                    if (self.isOpen) self.trigger('dropdown_item_activate', self.$activeOption.attr('data-value'));
                };
            });
        }

       /**
        * Option Deselect Plugin
        */

        if (typeof Selectize !== 'undefined') {
            Selectize.define("option_deselect", function () {
                var instance = this;
                instance.on('initialize', function () {
                    $(instance.$dropdown).off('click', '[data-selectable]');
                });
                instance.onOptionSelect = function (e) {
                    var value, $target, $option, self = this;
            
                    if (e.preventDefault) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
            
                    // prevent right click on option
                    if (e.button && e.button === 2) {
                        return;
                    }
            
                    $target = $(e.currentTarget);
                    if ($target.hasClass('create')) {
                        self.createItem(null, function() {
                            if (self.settings.closeAfterSelect) {
                                self.close();
                            }
                        });
                    } else {
                        value = $target.attr('data-value');
                        if (typeof value !== 'undefined') {
                            if (self.items.indexOf(value) >= 0) {
                                self.removeItem(value);
                                self.refreshOptions(true);
                                if (self.settings.closeAfterSelect) {
                                    self.close();
                                }
                            } else {
                                self.lastQuery = null;
                                self.setTextboxValue('');
                                self.addItem(value);
                                if (self.settings.closeAfterSelect) {
                                    self.close();
                                } else if (!self.settings.hideSelected && e.type && /mouse/.test(e.type)) {
                                    self.setActiveOption(self.getOption(value));
                                }
                            }
                        }
                    }
                };
            });
        }

        /**
         * Preserve Option Attributes Plugin
         */

        if (typeof Selectize !== 'undefined') {
            Selectize.define("preserve_option_attributes", function (pluginOptions) {
                var instance = this;
                instance.updateOriginalInput = function(opts) {
                    var i, n, options, label;
                    if (opts = opts || {},
                    1 === this.tagType) {
                        for (options = [],
                                 i = 0,
                                 n = this.items.length; i < n; i++)
                            label = this.options[this.items[i]][this.settings.labelField] || "",
                                options.push('<option value="' + escape_html(this.items[i]) + '" selected="selected" ' + draw_attributes(this.options[this.items[i]]) + '>' + escape_html(label) + "</option>");
                        options.length || this.$input.attr("multiple") || options.push('<option value="" selected="selected"></option>'),
                            this.$input.html(options.join(""))
                    } else
                        this.$input.val(this.getValue()),
                            this.$input.attr("value", this.$input.val());
                    this.isSetup && (opts.silent || this.trigger("change", this.$input.val()))
                }
            });
        }

        /**
         * Clear Button Plugin
         */

        if (typeof Selectize !== 'undefined') {
            Selectize.define("clear_button", function () {
                var instance = this;
                var $clearBtn = $('<span class="clear"><button type="button" class="l-btn ui-btn l-btn-input-clear ui-btn-input-clear"><i class="fal fa-times"></i></button></span>');
                function handleClearButton() {
                    instance.$control.append($clearBtn);
                    $clearBtn.find('button').on('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation();
                        instance.setValue([]);
                        instance.refreshOptions();
                        setTimeout(function () {
                            instance.blur();
                        }, 1);
                    });
                }
                instance.on('initialize', function () {
                    if (instance.getValue().length) {
                        handleClearButton();
                    }
                });
                instance.on('change', function () {
                    if (!instance.$control.find('.clear').length && instance.getValue().length) {
                        handleClearButton();
                    }
                    if (!instance.getValue().length) {
                        instance.$control.find('.clear').remove();
                    }
                });
            });
        }

    }
}

class UiSelectComponent {
    constructor(el, options = {}) {
        this.el = el;
        this.options = options;
        this.value = el.value;
    }
    
    initSimpleSelect() {
        const el = this.el;
        const options = this.options;
        return $(el).selectize({...defaultOptions(el, this), ...options});
    }

    initCustomizedSelect() {
        const el = this.el;
        const options = this.options;
        let modifiedOptions = {...defaultOptions(el, this), ...options};
        if (el.dataset.appFormSelectTemplate != null) {
            switch (el.dataset.appFormSelectTemplate) {
                case 'currency':
                    modifiedOptions = {
                        ...modifiedOptions,
                        searchField: ['text', 'fullName'],
                        render: {
                            option: function (data, escape) {
                                return `<div class="option ui-select-template-currency">${escape(data.text)}${data.fullName ? '<span class="ui-select-template-currency--full-name">&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;' + escape(data.fullName) + '</span>' : ''}</div>`
                            },
                            item: function (data, escape) {
                                return `<div class="item ui-select-template-currency">${escape(data.text)}</div>`
                            },
                        },
                    }
                    modifiedOptions.plugins.auto_position.width = 'auto';
                    break;
                case 'client-employee':
                    modifiedOptions = {
                        ...modifiedOptions,
                        valueField: "id",
                        labelField: "text",
                        create: false,
                        score: function(search) {
                            var score = this.getScoreFunction(search);
                            return function(item) {
                                return 1 + score(item);
                            };
                        },
                        render: {
                            option: function (data, escape) {
                                if (!data.id || data.id === "__blank__") {
                                    return `<div title="${escape(data.text)}" class="option ui-select-template-client-employee"><span>${escape(data.text)}</span></div>`

                                }
                                let img = data.img && data.img.indexOf('account-def-md.png') < 0 ? data.img : null;
                                if (!img) {
                                    var avatarQueryParams = new URLSearchParams({
                                        n: (data.text.trim()).indexOf('#') == 0 ? (data.text.trim()).split(' ')[1] : data.text.trim(),
                                        id: data.id,
                                        s: 78,
                                    });
                                    img = '/avatar.php?q=' + encode(avatarQueryParams.toString());
                                }
                                return `<div title="${escape(data.text)}" class="option ui-select-template-client-employee"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"><img src="${img}" /></span> &nbsp;&nbsp;</span><span>${escape(data.text)}</span></div>`
                            },
                            item: function (data, escape) {
                                let img = data.img && data.img.indexOf('account-def-md.png') < 0 ? data.img : null;
                                if (!img) {
                                    var avatarQueryParams = new URLSearchParams({
                                        n: (data.text.trim()).indexOf('#') == 0 ? (data.text.trim()).split(' ')[1] : data.text.trim(),
                                        id: data.id,
                                        s: 78,
                                    });
                                    img = '/avatar.php?q=' + encode(avatarQueryParams.toString());
                                }
                                return `<div title="${escape(data.text)}" class="item ui-select-template-client-employee"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"><img src="${img}" /></span> &nbsp;&nbsp;</span><span>${escape(data.text)}</span></div>`
                            },
                        },
                    }
                    break;
                    case 'client-employee-2':
                        modifiedOptions = {
                            ...modifiedOptions,
                            valueField: "id",
                            labelField: "name",
                            searchField: ['id', 'name', 'phone1', 'phone2'],
                            create: false,
                            score: function(search) {
                                var score = this.getScoreFunction(search);
                                return function(item) {
                                    return 1 + score(item);
                                };
                            },
                            render: {
                                option: function (data, escape) {
                                    if (!data.id) {
                                        return '<div></div>';
                                    }
                                    let img = data.photo && data.photo.indexOf('account-def-md.png') < 0 ? data.photo : null;
                                    if (!img) {
                                        var avatarQueryParams = new URLSearchParams({
                                            n: (data.name.trim()).indexOf('#') == 0 ? (data.name.trim()).split(' ')[1] : data.name.trim(),
                                            id: data.id,
                                            s: 78,
                                        });
                                        img = '/avatar.php?q=' + encode(avatarQueryParams.toString());
                                    }
                                    return `<div title="${escape(data.name)}" class="option ui-select-template-client-employee"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"><img src="${img}" /></span> &nbsp;&nbsp;</span><span>${escape(data.name)}</span></div>`
                                },
                                item: function (data, escape) {
                                    if (!data.id) {
                                        return '<div></div>';
                                    }
                                    let img = data.photo && data.photo.indexOf('account-def-md.png') < 0 ? data.photo : null;
                                    if (!img) {
                                        var avatarQueryParams = new URLSearchParams({
                                            n: (data.name.trim()).indexOf('#') == 0 ? (data.name.trim()).split(' ')[1] : data.name.trim(),
                                            id: data.id,
                                            s: 78,
                                        });
                                        img = '/avatar.php?q=' + encode(avatarQueryParams.toString());
                                    }
                                    return `<div title="${escape(data.name)}" class="item ui-select-template-client-employee"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"><img src="${img}" /></span> &nbsp;&nbsp;</span><span>${escape(data.name)}</span></div>`
                                },
                            },
                        }
                        break;


                        case 'bank-provider':
                            var selectedVal = true;
                            modifiedOptions = {
                                ...modifiedOptions,
                                valueField: "bank_name_en",
                                labelField: "name",
                                searchField: ['id', 'bank_name_en', 'bank_name_ar'],
                                createOnBlur: true,
                                persist: false,    
                                create:true,           
                                emptyOptionLabel:true,
                                showAddOptionOnCreate:false,            
                                score: function(search) {
                                    var score = this.getScoreFunction(search);
                                    return function(item) {
                                        return 1 + score(item);
                                    };
                                },
                                onDropdownClose: function() {
                                    this.clearUnselectedOptions();
                                },
                                render: {
                                    option: function (data, escape) {
                                        var dataIcon = this.$input.attr('data-bank-icon');
                                        var bankLogo = data.bank_logo  || dataIcon;
                                        var isRTL = document.dir =='rtl' ? true : false;
                                        this.$input.removeAttr('data-bank-icon')
                                                
                                        if(data.id &&  typeof data.bank_name_en !== "undefined" && escape(data.bank_name_en)){
                                            if(isRTL){
                                                return `<div title="${escape(data.bank_name_en)}" class="option ui-select-template-bank-provider"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"> ${bankLogo && bankLogo.indexOf('account-def-md.png') < 0 ? '<img src="' + bankLogo + ' "  style="object-fit: contain; height: auto;"/>' : '<i class="ui-icon--size-20 l-inline-block u-text-color-success mdi mdi-bank"></i>'} </span> &nbsp;&nbsp;</span><span>${escape(data.bank_name_ar)}</span></div>`
        
                                                }
                                                return `<div title="${escape(data.bank_name_en)}" class="option ui-select-template-bank-provider"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"> ${bankLogo && bankLogo.indexOf('account-def-md.png') < 0 ? '<img src="' + bankLogo + ' "  style="object-fit: contain; height: auto;" />' : '<i class="ui-icon--size-20 l-inline-block u-text-color-success mdi mdi-bank"></i>'} </span> &nbsp;&nbsp;</span><span>${escape(data.bank_name_en)}</span></div>`
                                                
                                            }
                                        return `<div title="${escape(data.name)}" class="option ui-select-template-bank-provider"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"> ${bankLogo && bankLogo.indexOf('account-def-md.png') < 0 ? '<img src="' + bankLogo + ' "  style="object-fit: contain; height: auto;"/>' : '<i class="ui-icon--size-20 l-inline-block u-text-color-success mdi mdi-bank"></i>'} </span> &nbsp;&nbsp;</span><span>${escape(data.name)}</span></div>`
                                   
                                    },
                                    item: function (data, escape) {
                                        var dataIcon = this.$input.attr('data-bank-icon');
                                        var bankLogo = data.bank_logo  || dataIcon;
                                        var isRTL = document.dir =='rtl' ? true : false;
                                        window.selectedBankData = data;
                                        
                                        if(data.id && typeof data.bank_name_en !== "undefined" && escape(data.bank_name_en)){
                                            if(isRTL){
                                                return `<div title="${escape(data.bank_name_en)}" class="item ui-select-template-bank-provider"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"> ${bankLogo && bankLogo.indexOf('account-def-md.png') < 0 ? '<img src="' + bankLogo + ' "  style="object-fit: contain; height: auto;"/>' : '<i class="ui-icon--size-20 l-inline-block u-text-color-success mdi mdi-bank"></i>'} </span> &nbsp;&nbsp;</span><span>${escape(data.bank_name_ar)}</span></div>`
                                            
                                            }
                                            return `<div title="${escape(data.bank_name_en)}" class="item ui-select-template-bank-provider"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"> ${bankLogo && bankLogo.indexOf('account-def-md.png') < 0 ? '<img src="' + bankLogo + ' "  style="object-fit: contain; height: auto;"/>' : '<i class="ui-icon--size-20 l-inline-block u-text-color-success mdi mdi-bank"></i>'} </span> &nbsp;&nbsp;</span><span>${escape(data.bank_name_en)}</span></div>`
                                        }
                                        return `<div title="${escape(data.name)}" class="option ui-select-template-bank-provider"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"> ${bankLogo && bankLogo.indexOf('account-def-md.png') < 0 ? '<img src="' + bankLogo + ' "  style="object-fit: contain; height: auto;"/>' : '<i class="ui-icon--size-20 l-inline-block u-text-color-success mdi mdi-bank"></i>'} </span> &nbsp;&nbsp;</span><span>${escape(data.name)}</span></div>`
                                    },
                                },
                            }
                            break;
                case 'flag':
                    modifiedOptions = {
                        ...modifiedOptions,
                        valueField: "id",
                        labelField: "text",
                        create: false,
                        score: function(search) {
                            var score = this.getScoreFunction(search);
                            return function(item) {
                                return 1 + score(item);
                            };
                        },
                        render: {
                            option: function (data, escape) {
                                return `<div class="option ui-select-template-flag"><img src="/dist/public/app/img/components/ui-select/templates/flag/${data.code.toLowerCase()}.svg" /> <span>${data.id}</span></div>`
                            },
                            item: function (data, escape) {
                                return `<div class="item ui-select-template-flag ui-select-template-flag--item"><img src="/dist/public/app/img/components/ui-select/templates/flag/${data.code.toLowerCase()}.svg" /></div>`
                            },
                        },
                    }
                    modifiedOptions.plugins.auto_position.width = 'auto';
                    break;
                case 'colored':
                    modifiedOptions = {
                        ...modifiedOptions,
                        valueField: "value",
                        labelField: "text",
                        create: false,
                        score: function(search) {
                            var score = this.getScoreFunction(search);
                            return function(item) {
                                return 1 + score(item);
                            };
                        },
                        render: {
                            option: function (data, escape) {
                                return `<div class="option"><span class="ui-badge-status l-badge-status" style="${data.bgColor ? 'background: ' + data.bgColor + ';color: ' + contrastColor({bgColor: data.bgColor}) : ''}">${data.icon ? '<i class="' + data.icon + '"></i>' : ''} ${data.text}</span></div>`
                            },
                            item: function (data, escape) {
                                return `<div class="item"><span class="ui-badge-status l-badge-status" style="${data.bgColor ? 'background: ' + data.bgColor + ';color: ' + contrastColor({bgColor: data.bgColor}) : ''}">${data.icon ? '<i class="' + data.icon + '"></i>' : ''} ${data.text}</span></div>`
                            },
                        },
                    }
                    break;
                case 'unit':
                    modifiedOptions = {
                        ...modifiedOptions,
                        valueField: "id",
                        labelField: "name",
                        create: false,
                        score: function(search) {
                            var score = this.getScoreFunction(search);
                            return function(item) {
                                return 1 + score(item);
                            };
                        },
                        render: {
                            option: function (data, escape) {
                                return `<div class="option">${escape(data.name)}${data.unit_type && data.unit_type.name ? '<span class="">&nbsp;&nbsp;(' + escape(data.unit_type.name) + ')</span>' : ''}</div>`
                            },
                            item: function (data, escape) {
                                return `<div class="item">${escape(data.name)}${data.unit_type && data.unit_type.name ? '<span class="">&nbsp;&nbsp;(' + escape(data.unit_type.name) + ')</span>' : ''}</div>`
                            },
                        },
                    }
                    break;
                case 'ajax-simple':
                modifiedOptions = {
                    ...modifiedOptions,
                    valueField: "text",
                    labelField: "text",
                    score: function(search) {
                        var score = this.getScoreFunction(search);
                        return function(item) {
                            return 1 + score(item);
                        };
                    },
                    render: {
                        option: function (data, escape) {
                            return `<div class="option">${escape(data.text)}</div>`
                        },
                        item: function (data, escape) {
                            return `<div class="item">${escape(data.text)}</div>`
                        },
                    },
                }
                break;
            case 'ajax-simple-2':
                modifiedOptions = {
                    ...modifiedOptions,
                    valueField: "id",
                    labelField: "name",
                    score: function(search) {
                        var score = this.getScoreFunction(search);
                        return function(item) {
                            return 1 + score(item);
                        };
                    },
                    render: {
                        option: function (data, escape) {
                            return `<div class="option">${escape(data.name)}</div>`
                        },
                        item: function (data, escape) {
                            return `<div class="item">${escape(data.name)}</div>`
                        },
                    },
                }
                break;
            case 'ajax-simple-3':
                modifiedOptions = {
                    ...modifiedOptions,
                    valueField: "id",
                    labelField: "text",
                    create: false,
                    score: function(search) {
                        var score = this.getScoreFunction(search);
                        return function(item) {
                            return 1 + score(item);
                        };
                    },
                    render: {
                        option: function (data, escape) {
                            return `<div title="${escape(data.text)}" class="option">${escape(data.text)}</div>`
                        },
                        item: function (data, escape) {
                            return `<div title="${escape(data.text)}" class="item">${escape(data.text)}</div>`
                        },
                    },
                }
                break;

            case 'Modules':{
                modifiedOptions = {
                    ...modifiedOptions,
                    valueField: "value",
                    labelField: "text",
                    create: false,
                    score: function(search) {
                        var score = this.getScoreFunction(search);
                        return function(item) {
                            return 1 + score(item);
                        };
                    },
                    render: {
                        option: function (data, escape) {
                            return `<div class="option ${data.icon ? 'd-flex align-items-center' : ''}">${data.icon ? `<i style="${data.iconColor ? 'color: ' + data.iconColor + ';margin-inline-end:5px;' : ''}" class="${data.icon} fs-20"></i>` : ''} <span>${data.text}</span></div>`
                        },
                        item: function (data, escape) {
                            return `<div class="item ${data.icon ? 'd-flex align-items-center' : ''}">${data.icon ? `<i style="${data.iconColor ? 'color: ' + data.iconColor + ';margin-inline-end:5px;' : ''}" class="${data.icon} fs-20"></i>` : ''} <span>${data.text}</span></div>`
                        },
                    },
                }
            }
            }
        }
        if (el.dataset.appFormSelectOptions != null) {
            try {
                const options = JSON.parse(el.dataset.appFormSelectOptions);
                for (const optionKey in options) {
                    if (Object.hasOwnProperty.call(options, optionKey)) {
                        const value = options[optionKey];
                        switch (optionKey) {
                            case 'persistent':
                                if (value == true) {
                                    modifiedOptions = {
                                        ...modifiedOptions,
                                        plugins: {
                                            'persistent': {},
                                            ...modifiedOptions.plugins
                                        },
                                    }
                                }
                                break;
                            case 'appendValuesTo':
                                modifiedOptions = {
                                    ...modifiedOptions,
                                    plugins: {
                                        'append_values_to': {
                                            selector: value
                                        },
                                        ...modifiedOptions.plugins
                                    },
                                }
                                break;
                            case 'multiplePlus':
                                modifiedOptions = {
                                    ...modifiedOptions,
                                    plugins: {
                                        'multiple_plus': {},
                                        ...modifiedOptions.plugins
                                    },
                                }
                                break;
                            case 'clearButton':
                                modifiedOptions = {
                                    ...modifiedOptions,
                                    plugins: {
                                        'clear_button': {},
                                        ...modifiedOptions.plugins
                                    },
                                }
                                break;
                            case 'ajax':
                                modifiedOptions = {
                                    ...modifiedOptions,
                                    loadThrottle: 600,
                                    load: function (query, callback) {
                                        if (typeof el.selectize.$input.attr('multiple') == 'undefined') {
                                            el.selectize.clearOptions();
                                        } else {
                                            el.selectize.clearUnselectedOptions();
                                        }
                                        if (!query.length) return callback();
                                        const url = value.url.replaceAll('__q__', encodeURIComponent(query));
                                        $.ajax({
                                            url,
                                            type: "GET",
                                            dataType: 'json',
                                            error: function () {
                                                callback();
                                            },
                                            success: function (res) {
                                                if (res.data) {
                                                    callback(res.data);
                                                } else if (res.results) {
                                                    callback(res.results);
                                                } else {
                                                    callback(res);
                                                }
                                            },
                                        });
                                    },
                                    plugins: {
                                        'ajax_helper_text': {
                                            "title": __("Please enter 1 keyword or more to search"),
                                            "noResultsTitle": __("No results found"),
                                            "class": 'selectize-helper-text',
                                            "icon": '',
                                            "type": 'div',
                                        },
                                        ...modifiedOptions.plugins
                                    },
                                }
                                break;
                            case 'inputIcon':
                                modifiedOptions = {
                                    ...modifiedOptions,
                                    plugins: {
                                        'input_icon': {
                                            iconClass: value
                                        },
                                        ...modifiedOptions.plugins
                                    },
                                }
                                break;
                            case 'dropdownFooter':
                                modifiedOptions = {
                                    ...modifiedOptions,
                                    plugins: {
                                        'dropdown_footer': {},
                                        ...modifiedOptions.plugins
                                    },
                                }
                                const keys = Object.entries(value);
                                keys.forEach(op => {
                                    const opKey = op[0];
                                    const opValue = op[1];
                                    modifiedOptions['plugins']['dropdown_footer'][opKey] = opValue;
                                })
                                modifiedOptions.plugins.auto_position.width = 'auto';
                                break;
                            case 'redirect':
                                modifiedOptions = {
                                    ...modifiedOptions,
                                    onChange: function (selectedOptionValue) {
                                        if (value) {
                                            const url = value;
                                            window.top.location = url.replace('__value__', selectedOptionValue);
                                        }
                                    }
                                }
                                break;
                        }
                    }
                }
            } catch (e) {}
        }
        return $(el).selectize(modifiedOptions);
    }
}
