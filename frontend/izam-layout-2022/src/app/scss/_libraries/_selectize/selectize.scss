@import '../../mixins';

.selectize-control.plugin-drag_drop.multi > .selectize-input > div.ui-sortable-placeholder {
    visibility: visible !important;
    background: #f2f2f2 !important;
    background: rgba(0, 0, 0, 0.06) !important;
    border: 0 none !important;
    box-shadow: inset 0 0 12px 4px #fff;
}
.selectize-control.plugin-drag_drop .ui-sortable-placeholder::after {
    content: "!";
    visibility: hidden;
}
.selectize-control.plugin-drag_drop .ui-sortable-helper {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.selectize-control .dropdown-header {
    position: relative;
    padding: 10px 8px;
    border-bottom: 1px solid #d0d0d0;
    background: #f8f8f8;
    border-radius: 3px 3px 0 0;
}
.selectize-control .dropdown-header-close {
    position: absolute;
    right: 8px;
    top: 50%;
    color: #303030;
    opacity: 0.4;
    margin-top: -12px;
    line-height: 20px;
    font-size: 20px !important;
}
.selectize-control .dropdown-header-close:hover {
    color: #000;
}
.selectize-dropdown.plugin-optgroup_columns .selectize-dropdown-content {
    display: flex;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup {
    border-right: 1px solid #f2f2f2;
    border-top: 0 none;
    flex-grow: 1;
    flex-basis: 0;
    min-width: 0;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:last-child {
    border-right: 0 none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup:before {
    display: none;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup-header {
    border-top: 0 none;
}
.selectize-control.plugin-multiple_remove_button .item {
    display: inline-flex;
    align-items: center;
}
[dir="ltr"] .selectize-control.plugin-multiple_remove_button .item[data-value] {
    padding-right: 0 !important;
}
[dir="rtl"] .selectize-control.plugin-multiple_remove_button .item[data-value] {
    padding-left: 0 !important;
}
.selectize-control.plugin-multiple_remove_button .item .remove {
    text-decoration: none;
    vertical-align: middle;
    display: inline-block;
    padding: 2px 6px;
    box-sizing: border-box;
    color: var(--color-default);
    transition: all 0.15s ease-in-out;
}
.selectize-control.plugin-multiple_remove_button .item.locked .remove {
    padding: 2px 4px;
    width: 0;
}
.selectize-control.plugin-multiple_remove_button .item.locked .remove i {
    visibility: hidden;
    pointer-events: none;
}
.selectize-control.plugin-multiple_remove_button .item .remove:hover {
    color: var(--color-danger);
}
// .selectize-control.plugin-multiple_remove_button .item.active .remove {
//     color: var(--color-danger);
// }
.selectize-control.plugin-multiple_remove_button .disabled .item .remove:hover {
    color: var(--color-action);
}
// .selectize-control.plugin-multiple_remove_button .disabled .item .remove {
//     border-left-color: #fff;
// }
// .selectize-control.plugin-multiple_remove_button .remove-single {
//     position: absolute;
//     right: 0;
//     top: 0;
//     font-size: 23px;
// }
.selectize-control {
    position: relative;
}
.selectize-control.loading::before {
    content: '';
    position: absolute;
    width: 25px;
    height: 25px;
    border-radius: 50px;
    z-index: 2;
    background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M456.433 371.72l-27.79-16.045c-7.192-4.152-10.052-13.136-6.487-20.636 25.82-54.328 23.566-118.602-6.768-171.03-30.265-52.529-84.802-86.621-144.76-91.424C262.35 71.922 256 64.953 256 56.649V24.56c0-9.31 7.916-16.609 17.204-15.96 81.795 5.717 156.412 51.902 197.611 123.408 41.301 71.385 43.99 159.096 8.042 232.792-4.082 8.369-14.361 11.575-22.424 6.92z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: 20px;
    background-position: center;
    animation: fa-spin 1s linear infinite;
    top: calc(50% - 11.5px);
}
[dir="ltr"] .selectize-control.loading::before {
    right: 10px;
}
[dir="rtl"] .selectize-control.loading::before {
    left: 10px;
}
.selectize-control.loading .selectize-input::after {
    display: none;
}
.selectize-dropdown,
.selectize-input,
.selectize-input input {
    color: var(--color-black);
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 18px;
    font-smoothing: inherit;
}
.selectize-control.single .selectize-input.input-active,
.selectize-input {
    background: var(--color-white);
    cursor: text;
    // display: inline-block;
}
.selectize-input {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    overflow: hidden;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    color: var(--color-black);
    font-weight: 400;
    font-size: .875rem;
    border: 1px solid var(--color-secondary);
    padding: 0 16px;
    background-color: var(--color-white);
    border-radius: var(--input-border-radius);
    line-height: 16px;
    transition: 0.15s all ease-in-out;
    cursor: pointer;
    &.focus {
        cursor: text;
    }
    @include ltr {
        padding-right: 2.3rem;
    }
    @include rtl {
        padding-left: 2.3rem;
    }
    &.template-client-employee {
        padding: 8px 16px;
        min-height: 42px;
        display: flex !important;
        align-items: center;
        padding-inline-end: 32px;
        input{
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    }
}
.selectize-control.multi .selectize-input.has-items {
    // padding: 4px 5px 0 5px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    // @include ltr() {
    //     padding: 4px 22px 0 5px;
    // }
    // @include rtl() {
    //     padding: 4px 5px 0 22px;
    // }
}
.selectize-helper-text {
    padding: 10px 16px;
    line-height: 16px;
    font-size: 0.875rem;
    color: var(--color-default);
    white-space: normal;
}
.selectize-input.full {
    background-color: var(--color-white);
}
.selectize-input.disabled,
.selectize-input.disabled *, 
.selectize-input.locked,
.selectize-input.locked * {
    cursor: default !important;
}
.selectize-input:hover {
    border-color: var(--color-action);
}
.selectize-input.focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 1px var(--color-primary);
}
.selectize-input.dropdown-active {
    border-radius: var(--input-border-radius);
    border-color: var(--color-primary);
    box-shadow: 0 0 0 1px var(--color-primary);
}
.is-invalid-box .selectize-input {
    border-color: var(--color-danger);
}
.selectize-input > * {
    vertical-align: baseline;
    display: inline-block;
    zoom: 1;
}
.selectize-control.multi .selectize-input {
    min-height: 42px;
    // line-height: 1.5;
}
.selectize-control.multi .selectize-input > div {
    cursor: pointer;
    margin: 4px 4px 4px 0;
    padding: 2px 8px;
    background: var(--color-light);
    // border: 1px solid var(--color-secondary);
    border-radius: var(--input-border-radius);
    color: var(--color-black);
}
.selectize-control.multi .selectize-input > div > .remove {
    
}
.selectize-control.multi .selectize-input > div.active {
    background: var(--color-secondary);
}
.selectize-control.multi .selectize-input > div.locked {
    pointer-events: none;
    opacity: 0.7;
}
.selectize-control.multi .selectize-input.disabled > div,
.selectize-control.multi .selectize-input.disabled > div.active, 
.selectize-control.multi .selectize-input.locked > div,
.selectize-control.multi .selectize-input.locked > div.active {
    color: #7d7d7d;
    background: var(--color-white);
    border: 0 solid var(--color-white);
}
.selectize-input > input {
    display: inline-block !important;
    padding: 0 !important;
    min-height: 0 !important;
    max-height: none !important;
    max-width: 100% !important;
    margin: 0 !important;
    text-indent: 0 !important;
    border: 0 none !important;
    background: 0 0 !important;
    user-select: auto !important;
    box-shadow: none !important;
    // width: 90% !important;
    &:focus,
    &:focus-visible {
        &::placeholder {
            color: transparent;
        }
    }
    @include ltr() {
        line-height: inherit !important;
    }
    @include rtl() {
        line-height: 20px !important;
    }
}
// .selectize-control.single .selectize-input.has-items > input {
//     width: auto !important;
// }
// .selectize-input:not(.has-items) > input[placeholder] {
//     width: auto !important;
// }
.selectize-input > input::-ms-clear {
    display: none;
}
.selectize-input > input:focus {
    outline: 0 !important;
}
.selectize-input > input[placeholder] {
    box-sizing: initial;
}
.selectize-input > input::placeholder {
    color: var(--color-placeholder);
    transition: color 0.15s ease-in-out;
}
.selectize-input.has-items > input {
    margin: 0 0 !important;
}
.selectize-input::after {
    content: " ";
    display: block;
    clear: left;
}
.selectize-input.dropdown-active::before {
    content: " ";
    display: block;
    position: absolute;
    background: #f0f0f0;
    height: 1px;
    bottom: 0;
    left: 0;
    right: 0;
}
.selectize-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    z-index: 300;
    background: var(--color-white);
    margin: 0;
    border-top: 0 none;
    box-sizing: border-box;
    border-radius: var(--input-border-radius);
    border-left: 1px solid var(--color-primary);
    box-shadow: 0 0 0 1px var(--color-primary);
    border-right: 1px solid var(--color-primary);
    border-bottom: 1px solid var(--color-primary);
    padding-top: 5px;
    padding-bottom: 5px;
    margin-bottom: 10px;
    min-width: 100%;
}
[dir="rtl"] .selectize-dropdown {
    left: unset;
    right: 0;
}
.selectize-dropdown [data-selectable] {
    cursor: pointer;
    overflow: hidden;
}
.selectize-dropdown [data-selectable] .highlight {
    background: rgba(125, 168, 208, 0.2);
    border-radius: 1px;
}
.selectize-dropdown .create,
.selectize-dropdown .no-results,
.selectize-dropdown .optgroup-header,
.selectize-dropdown .option {
    padding: 10px 16px;
}
.selectize-dropdown .optgroup .optgroup-header {
    padding: 5px 8px;
}
.selectize-dropdown .option,
.selectize-dropdown [data-disabled],
.selectize-dropdown [data-disabled] [data-selectable].option {
    cursor: inherit;
    opacity: 0.5;
}
.selectize-dropdown [data-selectable].option:first-child:is(.active) {
    border-radius: 1px 1px 0 0;
}
.selectize-dropdown [data-selectable].option:last-child:is(.active) {
    border-radius: 0 0 1px 1px;
}
.selectize-dropdown [data-selectable].option {
    opacity: 1;
    cursor: pointer;
}
.selectize-dropdown .optgroup:first-child .optgroup-header {
    border-top: 0 none;
}
.selectize-dropdown .optgroup-header {
    color: var(--color-white);
    cursor: default;
    font-weight: 700;
    background-color: var(--color-primary);    
}
.selectize-dropdown .active,
.selectize-dropdown .selected {
    background-color: var(--color-secondary);
    color: var(--color-black);
}
.selectize-dropdown .active.create {
    color: var(--color-black);
}
.selectize-dropdown .create {
    color: rgba(48, 48, 48, 0.5);
}
.selectize-dropdown-content {
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 190px;
    overflow-scrolling: touch;
}
.selectize-dropdown-emptyoptionlabel {
    text-align: center;
}
.selectize-dropdown .spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    margin: 5px 8px;
}
.selectize-dropdown .spinner:after {
    content: " ";
    display: block;
    width: 24px;
    height: 24px;
    margin: 3px;
    border-radius: 50%;
    border: 5px solid #d0d0d0;
    border-color: #d0d0d0 transparent #d0d0d0 transparent;
    animation: lds-dual-ring 1.2s linear infinite;
}
@keyframes lds-dual-ring {
    0% {
        transform: rotate(0);
    }
    100% {
        transform: rotate(360deg);
    }
}
.selectize-control.single .selectize-input {
    min-height: 42px;
}
// .selectize-control.single .selectize-input,
// .selectize-control.single .selectize-input input {
//     cursor: pointer;
// }
.selectize-control.single .selectize-input.input-active,
.selectize-control.single .selectize-input.input-active input {
    cursor: text;
}
.selectize-control .selectize-input:after {
    content: " ";
    position: absolute;
	border-style: solid;
	border-width: 0.15em 0.15em 0 0;
    border-top-color: var(--color-black);
    border-right-color: var(--color-black);
	display: inline-block;
	width: 8px;
	height: 8px;
	top: calc(50% - 6px);
	transform: rotate(135deg);
    @include ltr() {
        right: 15px;
    }
    @include rtl() {
        left: 15px;
    }
}
.selectize-control .selectize-input.dropdown-active:after {
    border-top-color: var(--color-primary);
    border-right-color: var(--color-primary);
}
.selectize-control.single .selectize-input.dropdown-active:after,
.selectize-control.multi.plugin-append_values_to .selectize-input.dropdown-active:after {
	transform: rotate(-45deg);
	top: calc(50% - 3px);
}
.selectize-control.rtl {
    text-align: right;
}
.selectize-control.rtl.single .selectize-input:after {
    left: 15px;
    right: auto;
}
.selectize-control.rtl .selectize-input > input {
    margin: 0 0 0 -2px !important;
}
.selectize-control .selectize-input.disabled,
.selectize-control .selectize-input.locked {
    opacity: 0.5;
    background-color: #fafafa;
    pointer-events: none;
}
.selectize-control.multi.plugin-append_values_to .selectize-input {
    @include ltr() {
        padding: 12px 16px;
    }
    @include rtl() {
        padding: 12px 16px;
    }
}
.selectize-control.multi.plugin-append_values_to .selectize-input.has-items > input {
    width: 100% !important;
}
.selectize-control.multi.plugin-append_values_to .selectize-input .item {
    display: none;
}

.selectize-control .option.ui-select-template-bank-provider{
    display: flex;
    align-items: center;
  }

.selectize-control.plugin-multiple_plus {
    &.multi {
        .selectize-input {
            .item {
                max-width: 80%;
            }
            .count {
                line-height: 1;
                font-size: 0.875rem;
                // padding: 6px 10px;
                text-shadow: none;
                border-radius: 2px;
                background: var(--color-light);
                box-shadow: none;
                border: 0;
                color: var(--color-black);
                pointer-events: none;
            }
            > * {
                margin-top: 0;
                margin-bottom: 0;
            }
            > div {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}


// Clear Button Plugin

.selectize-control.plugin-clear_button {
    &.single,
    &.multi {
        .selectize-input {
            @include ltr {
                padding-right: 4.1rem;
            }
            @include rtl {
                padding-left: 4.1rem;
            }
            .clear {
                position: absolute;
                top: 0;
                bottom: 0;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                @include ltr {
                    right: 42px;
                }
                @include rtl {
                    left: 42px;
                }
            }
        }
    }
}