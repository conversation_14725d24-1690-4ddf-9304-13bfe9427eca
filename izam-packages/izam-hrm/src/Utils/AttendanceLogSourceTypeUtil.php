<?php

namespace Izam\Hrm\Utils;

class AttendanceLogSourceTypeUtil
{
    const ATTENDANCE_LOG_SOURCE_TYPE_MACHINE = 'machine';
    const ATTENDANCE_LOG_SOURCE_TYPE_SUPERVISOR = 'supervisor';
    const ATTENDANCE_LOG_SOURCE_TYPE_FILE = 'file';
    const ATTENDANCE_LOG_SOURCE_TYPE_SELF = 'self';
    
    public static function getSourceTypeList()
    {
        return [
            self::ATTENDANCE_LOG_SOURCE_TYPE_MACHINE => __t('Machine'),
            self::ATTENDANCE_LOG_SOURCE_TYPE_SUPERVISOR => __t('Supervisor'),
            self::ATTENDANCE_LOG_SOURCE_TYPE_FILE => __t('File'),
            self::ATTENDANCE_LOG_SOURCE_TYPE_SELF => __t('Self'),
        ];
    }
}
