<?php

namespace Izam\Hrm\Utils;

class AttendanceLogStatusUtil
{
    const ATTENDANCE_LOG_DRAFT = 'draft';
    const ATTENDANCE_LOG_SIGN_IN = 'sign_in';
    const ATTENDANCE_LOG_SIGN_OUT = 'sign_out';
    const ATTENDANCE_LOG_REPEATED_SIGN_IN = 'repeated_sign_in';
    const ATTENDANCE_LOG_REPEATED_SIGN_OUT = 'repeated_sign_out';
    const ATTENDANCE_LOG_INVALID = 'invalid';
    const ATTENDANCE_LOG_UNKNOWN_EMPLOYEE = 'unknown_employee';

    public static function getStatusList()
    {
        return [
            self::ATTENDANCE_LOG_DRAFT  => __t('Not Calculated'),
            self::ATTENDANCE_LOG_SIGN_IN  => __t('Sign In'),
            self::ATTENDANCE_LOG_SIGN_OUT  => __t('Sign Out'),
            self::ATTENDANCE_LOG_REPEATED_SIGN_IN  => __t('Repeated Sign In'),
            self::ATTENDANCE_LOG_REPEATED_SIGN_OUT  => __t('Repeated Sign Out'),
            self::ATTENDANCE_LOG_INVALID  => __t('Invalid'),
            self::ATTENDANCE_LOG_UNKNOWN_EMPLOYEE => __t('Unknown Employee'),
        ];
    }

    public static function getStatusColor($status): string
    {
        return match ($status) {
            self::ATTENDANCE_LOG_DRAFT  => 'orange',
            self::ATTENDANCE_LOG_SIGN_IN, self::ATTENDANCE_LOG_REPEATED_SIGN_IN => 'success',
            self::ATTENDANCE_LOG_SIGN_OUT, self::ATTENDANCE_LOG_REPEATED_SIGN_OUT  => 'dark',
            self::ATTENDANCE_LOG_INVALID  => 'red',
            default => ''
        };
    }
}
