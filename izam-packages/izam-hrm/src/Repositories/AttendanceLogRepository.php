<?php

namespace Izam\Hrm\Repositories;

use Izam\Hrm\Models\AttendanceLog;

class AttendanceLogRepository{
    public function getAttLog($staffMachineId, $time, $staffId){
        return AttendanceLog::where('staff_machine_id', $staffMachineId)
                             ->where('time', $time)
                             ->where('staff_id', $staffId)
                             ->first();
    }

    public function insert($data){
        return AttendanceLog::insert($data);
    }

}