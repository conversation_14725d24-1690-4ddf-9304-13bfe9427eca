<?php

namespace Izam\ZktAttendanceMachine\Factories;

use Izam\ZktAttendanceMachine\Exceptions\UndefinedOperationTypeException;
use Izam\ZktAttendanceMachine\Helpers\ZktMachineAttendanceLogHandler;
use Izam\ZktAttendanceMachine\Helpers\ZktMachineOperationLogHandler;
use Izam\ZktAttendanceMachine\Utils\ZktMachineOperationTypeUtil;

class ZktMachineEndpointHandlerFactory{

    public static function create($tableParam, $inputData, $machine){
        return match ($tableParam) {
            ZktMachineOperationTypeUtil::OPER_LOG_TYPE => new ZktMachineOperationLogHandler($inputData, $machine),
            ZktMachineOperationTypeUtil::ATT_LOG_TYPE => new ZktMachineAttendanceLogHandler($inputData, $machine),
            default => throw new UndefinedOperationTypeException(),
        };

    }
}