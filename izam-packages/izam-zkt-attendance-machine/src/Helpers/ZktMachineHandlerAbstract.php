<?php

namespace Izam\ZktAttendanceMachine\Helpers;

abstract class ZktMachineHandlerAbstract
{
    public $data;
    public function __construct(public $inputData, public $machine) {
        $this->data = $this->formatData($inputData);
    }

    public function isIgnoredValue($value){
        return empty($value) && $value != "0";
    }

    abstract public function formatData($data);
    abstract public function handle();
}