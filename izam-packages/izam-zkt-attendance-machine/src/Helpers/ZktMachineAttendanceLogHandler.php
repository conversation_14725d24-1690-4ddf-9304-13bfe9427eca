<?php

namespace Izam\ZktAttendanceMachine\Helpers;

use Izam\ZktAttendanceMachine\Repositories\MachineMappingRepository;
use Izam\Hrm\Repositories\AttendanceLogRepository;
use Izam\Hrm\Utils\AttendanceLogSourceTypeUtil;
use Izam\Entity\Formatter\Formatter;
use Izam\Hrm\Utils\AttendanceLogStatusUtil;
use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;

class ZktMachineAttendanceLogHandler extends ZktMachineHandlerAbstract{

    private MachineMappingRepository $machineMappingRepo;
    private AttendanceLogRepository $attendanceLogRepo;
    
    public function __construct(){
        $this->machineMappingRepo = resolve(MachineMappingRepository::class); 
        $this->attendanceLogRepo = resolve(AttendanceLogRepository::class); 
        parent::__construct(...func_get_args());
    }

    public function formatData($data) {
        $formattedData = [];
        $rows = explode("\n",$data);
        foreach ($rows as $rowKey => $row) {
            $row = trim($row);
            if($this->isIgnoredValue($row)) continue;
            $formattedData[$rowKey] = [];
            $rowArray = explode("\t",$row);
            $formattedData[$rowKey]["staff_machine_id"] = isset($rowArray[0]) && !empty(trim($rowArray[0])) ? trim($rowArray[0]) : null;
            $formattedData[$rowKey]["time"] = isset($rowArray[1]) && !empty(trim($rowArray[1])) ? trim($rowArray[1]) : null;
        }
        return $formattedData;
    }
    
    public function handle() {

        $insertData = $this->getAttLogAndMappingToInsertFromZktData($this->machine, $this->data);
        $mappingsToInsert = $insertData['mappings_to_insert'];
        $attendanceLogsAttemptedToInsert = $insertData['attendance_logs_to_insert'];

        if(!empty($mappingsToInsert)){
            $this->machineMappingRepo->insert($mappingsToInsert);
        }
        $attendanceLogsToInsert = [];
        if(!empty($attendanceLogsAttemptedToInsert)){
            foreach ($attendanceLogsAttemptedToInsert as $key => $attLogToInsert) {
                if(empty($this->attendanceLogRepo->getAttLog(
                    $attLogToInsert['staff_machine_id'],
                    $attLogToInsert['time'],
                    $attLogToInsert['staff_id']
                ))){
                    $attendanceLogsToInsert[] = $attLogToInsert;
                }
            }
            if(empty($attendanceLogsToInsert)){
                return null;
            }
            return $this->attendanceLogRepo->insert($attendanceLogsToInsert);
        }
        return null;
    }

    private function getAttLogAndMappingToInsertFromZktData($machineId, $dataArray){
        $dataCollection = collect($dataArray);
        $staffMachineIds = $dataCollection->pluck('staff_machine_id')->unique()->toArray();
        $mappings = $this->machineMappingRepo->getMappings($machineId, $staffMachineIds);
        $mappingsByStaffMachineId = $mappings->keyBy('staff_machine_id')->toArray();
        $mappingsToInsert = [];
        $attendanceLogsToInsert = [];
        foreach ($dataArray as $item) {
            $isUnknownEmployee = empty($mappingsByStaffMachineId[$item['staff_machine_id']]);
            if($isUnknownEmployee){
                $mappingsToInsert[] = [
                    'machine_id' => $machineId,
                    'staff_machine_id' => $item['staff_machine_id'],
                    'staff_id' => -1,    
                ];

                $mappingsByStaffMachineId[$item['staff_machine_id']] = [
                    'machine_id' => $machineId,
                    'staff_machine_id' => $item['staff_machine_id'],
                    'staff_id' => -1,
                ];
            }
            $isIgnoredEmployee = !$isUnknownEmployee && $mappingsByStaffMachineId[$item['staff_machine_id']]['staff_id'] == -2;
            if($isIgnoredEmployee){
                continue;
            }
            $time = Formatter::formatForDb($item['time'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER);
            $attendanceLogsToInsert[] = [
                'staff_id' => $isUnknownEmployee ? -1 : $mappingsByStaffMachineId[$item['staff_machine_id']]['staff_id'],
                'time' => $time,
                'staff_machine_id' => $item['staff_machine_id'],
                'status' => $isUnknownEmployee ? AttendanceLogStatusUtil::ATTENDANCE_LOG_UNKNOWN_EMPLOYEE : AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT,
                'source_id' => $machineId,
                'source_type' => AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_MACHINE,
            ];
        }
        return [
            "mappings_to_insert" => $mappingsToInsert,
            "attendance_logs_to_insert" => $attendanceLogsToInsert,
        ];
    }

}
