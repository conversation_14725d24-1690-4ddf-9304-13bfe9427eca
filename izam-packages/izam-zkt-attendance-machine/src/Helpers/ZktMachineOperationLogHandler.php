<?php

namespace Izam\ZktAttendanceMachine\Helpers;

use Izam\ZktAttendanceMachine\Repositories\MachineMappingRepository;

class ZktMachineOperationLogHandler extends ZktMachineHandlerAbstract {

    private MachineMappingRepository $machineMappingRepo;

    public function __construct(){
        $this->machineMappingRepo = resolve(MachineMappingRepository::class); 
        parent::__construct(...func_get_args());
    }

    public function formatData($data) {
        $formattedData = [];
        $rows = explode("\n",$data);
        foreach ($rows as $rowKey => $row) {
            $row = trim($row);
            if($this->isIgnoredValue($row)) continue;

            $formattedData[$rowKey] = [];
            $rowArray = explode("\t",$row);
            foreach ($rowArray as $column) {
                $column = trim($column);
                if($this->isIgnoredValue($column)) continue;
    
                $columnArray = explode("=",$column);
                foreach ($columnArray as $columnKey => $column) {
                    $columnArray[$columnKey] = trim($column);
                }
                if(empty($columnArray[0])) continue;
                $formattedData[$rowKey][$columnArray[0]] = isset($columnArray[1]) ? $columnArray[1] : null;
            }
        }
        return $formattedData;
    }

    public function handle() {
        $dataCollection = collect($this->data);
        $staffMachineIds = $dataCollection->pluck('USER PIN')->unique()->toArray();
        $alreadyExistingMappings = $this->machineMappingRepo->getMappings($this->machine, $staffMachineIds);
        $alreadyExistingMappingsByUserPin = $alreadyExistingMappings->keyBy('staff_machine_id')->toArray();
        $dataToInsert = $dataCollection->reject(fn($item) => isset($alreadyExistingMappingsByUserPin[$item['USER PIN']]))
            ->map(function ($item) {
                return [
                    'machine_id' => $this->machine,
                    'staff_machine_id' => $item['USER PIN'],
                    'machine_employee_name' => $item['Name'],
                    'staff_id' => -1,
                ];
            })->unique('staff_machine_id');
        if ($dataToInsert->isNotEmpty()) {
            return $this->machineMappingRepo->insert($dataToInsert->toArray());
        }
        return null;
    }
}
