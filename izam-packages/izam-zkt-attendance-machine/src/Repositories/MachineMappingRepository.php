<?php

namespace Izam\ZktAttendanceMachine\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Izam\ZktAttendanceMachine\Models\MachineMapping;

class MachineMappingRepository{
    public function getMappings($machineId, array $staffMachineIds) : Collection {
        return MachineMapping::where('machine_id', $machineId)->whereIn('staff_machine_id', $staffMachineIds)->get();
    }

    public function insert($data){
        return MachineMapping::insert($data);
    }
}