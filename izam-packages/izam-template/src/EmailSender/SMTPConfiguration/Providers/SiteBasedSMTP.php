<?php

namespace Izam\Template\EmailSender\SMTPConfiguration\Providers;

use Izam\Daftra\Portal\Models\Site;
use Izam\Template\EmailSender\DTO\SMTPConfig;
use Izam\Template\EmailSender\SMTPConfiguration\Interfaces\ISMTPConfigProvider;
use Izam\Template\EmailSender\Utils\MailProvidersUtil;

class SiteBasedSMTP implements ISMTPConfigProvider
{
    public static function get($data = []): SMTPConfig
    {
        $currentSite = Site::find(getCurrentSite('id'));

        $id = $currentSite['id'] ?? null;
        $type = MailProvidersUtil::OTHERS;
        $host = $currentSite['smtp_host'];
        $port = $currentSite['smtp_port'];
        $username = $currentSite['smtp_user_name'];
        $password = $currentSite['smtp_password'];
        $senderMail = $currentSite['smtp_from_email'];
        $senderName = $currentSite['smtp_from_name'];
        $securityProtocol = ($currentSite['smtp_ssl']) ? 'ssl' : '';
        $replyTo = $currentSite['smtp_from_email'];
        $credentials = $currentSite['smtp_credentials'];

        if (!$currentSite['use_smtp']) {
            $host = AWS_SES_SMTP_HOST;
            $username = AWS_SES_SMTP_USERNAME;
            $password = AWS_SES_SMTP_PASSWORD;
            $senderName = mb_encode_mimeheader(getCurrentSite('business_name'), 'UTF-8', 'B','');
            $port = AWS_SES_SMTP_PORT;
            $replyTo = $currentSite['email'];
            $senderMail = str_replace("@", "_", $currentSite['email']) . '@' . MailDeliveryDomain;
            $securityProtocol = 'tls';
        }

        return new SMTPConfig($id, $type, $host, $port, $securityProtocol, $username, $password, $senderMail, $senderName, $replyTo, $credentials);
    }
}
