<?php

namespace Izam\View\Form;

use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\AssignedStaff;
use Izam\View\Form\Element\AuthLink;
use Izam\View\Form\Element\AutoSuggest;
use Izam\View\Form\Element\AutoSuggestV2;
use Izam\View\Form\Element\Badge;
use Izam\View\Form\Element\BranchDynamicDropdown;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\Card;
use Izam\View\Form\Element\CurrencyDropdown;
use Izam\View\Form\Element\CurrencyRange;
use Izam\View\Form\Element\ImageHeader;
use Izam\View\Form\Element\MultipleFile;
use Izam\View\Form\Element\MultipleSelectActionIframe;
use Izam\View\Form\Element\NumberRange;
use Izam\View\Form\Helper\Form\FormCard;
use Izam\View\Form\Helper\Form\FormImageHeader;
use Izam\View\Form\Helper\Form\FormMultipleFile;
use Izam\View\Form\Helper\Form\FormMultipleSelectActionIframe;
use Izam\View\Form\Helper\Form\FormNumberRange;
use Izam\View\Form\Helper\Form\FormPaginationMobile;
use Izam\View\Form\Helper\Form\FormTags;
use Laminas\Form\Element\DateTime;
use Izam\View\Form\Element\DemoElement;
use Izam\View\Form\Element\FollowUpStatus;
use Izam\View\Form\Element\Icon;
use Izam\View\Form\Element\Link;
use Izam\View\Form\Element\LinkList;
use Izam\View\Form\Element\ListingQuickFilters;
use Izam\View\Form\Element\MultipleSelectAction;
use Izam\View\Form\Element\Pagination;
use Izam\View\Form\Element\Paragraph;
use Izam\View\Form\Element\Phone;
use Izam\View\Form\Element\Price;
use Izam\View\Form\Element\QuantityWithUnit;
use Izam\View\Form\Element\Sperator;
use Izam\View\Form\Element\SplitButtonDropdowns;
use Izam\View\Form\Element\Stack;
use Izam\View\Form\Element\StatusCircle;
use Izam\View\Form\Element\Subform;
use Izam\View\Form\Element\Tags;
use Izam\View\Form\Element\TitleText;
use Izam\View\Form\Element\TimeRange;
use Izam\View\Form\Element\ViewActions;
use Izam\View\Form\Helper\Form\FormAutoNumber;
use Izam\View\Form\Helper\Form\FormAuthLink;
use Izam\View\Form\Helper\Form\FormAutoSuggest;
use Izam\View\Form\Helper\Form\FormBadge;
use Izam\View\Form\Helper\Form\FormBlank;
use Izam\View\Form\Helper\Form\FormButton;
use Izam\View\Form\Helper\Form\FormButtonMobile;
use Izam\View\Form\Helper\Form\FormCurrencyDropdown;
use Izam\View\Form\Helper\Form\FormCurrencyRange;
use Izam\View\Form\Helper\Form\FormDateTime;
use Izam\View\Form\Helper\Form\FormDemoElement;
use Izam\View\Form\Helper\Form\FormDynamicDropdown;
use Izam\View\Form\Helper\Form\FormFile;
use Izam\View\Form\Helper\Form\FormFollowUpStatus;
use Izam\View\Form\Helper\Form\FormFormattedText;
use Izam\View\Form\Element\Currency;
use Izam\View\Form\Element\DynamicDropdown;
use Izam\View\Form\Helper\Form\FormIcon;
use Izam\View\Form\Helper\Form\FormLink;
use Izam\View\Form\Helper\Form\FormLinkList;
use Izam\View\Form\Helper\Form\FormPassword;
use Izam\View\Form\Helper\Form\FormListingQuickFilters;
use Izam\View\Form\Helper\Form\FormListingQuickFiltersMobile;
use Izam\View\Form\Helper\Form\FormMultipleSelectAction;
use Izam\View\Form\Helper\Form\FormPageHeadAnchor;
use Izam\View\Form\Helper\Form\FormPageHeadAnchorMobile;
use Izam\View\Form\Helper\Form\FormStackMobile;
use Izam\View\Form\Helper\Form\FormPagination;
use Izam\View\Form\Helper\Form\FormParagraph;
use Izam\View\Form\Helper\Form\FormPrice;
use Izam\View\Form\Helper\Form\FormQuantityWithUnit;
use Izam\View\Form\Helper\Form\FormSperator;
use Izam\View\Form\Helper\Form\FormSplitButtonDropdowns;
use Izam\View\Form\Helper\Form\FormStack;
use Izam\View\Form\Helper\Form\FormStatusCircle;
use Izam\View\Form\Helper\Form\FormStatusCircleMobile;
use Izam\View\Form\Helper\Form\FormTag;
use Izam\View\Form\Helper\Form\FormTextArea;
use Izam\View\Form\Helper\Form\FormTimeRange;
use Izam\View\Form\Helper\Form\FormViewActions;
use Izam\View\Form\Helper\Form\PageHeadFormSplitButtonDropdownsMobile;
use Izam\View\Form\Helper\Form\FormText;
use Izam\View\Form\Element\AutoNumber;
use Laminas\Form\Element\Checkbox;
use Izam\View\Form\Element\DateRange;
use Izam\View\Form\Element\ForeignKey;
use Izam\View\Form\Element\FormattedText;
use Izam\View\Form\Element\Map;
use Izam\View\Form\Element\MultipleDropdown;
use Izam\View\Form\Element\MultipleDynamicDropdown;
use Izam\View\Form\Element\NotImplemented;
use Izam\View\Form\Element\PrimaryKey;
use Izam\View\Form\Element\Select;
use Izam\View\Form\Element\Separator;
use Izam\View\Form\Element\AutoStaff;
use Izam\View\Form\Element\Condition;
use Izam\View\Form\Element\StaticContent;
use Izam\View\Form\Element\Time;
use Izam\View\Form\Helper\Form\FormAssignedStaff;
use Izam\View\Form\Helper\Form\FormCheckbox;
use Izam\View\Form\Helper\Form\FormCondition;
use Izam\View\Form\Helper\Form\FormCurrency;
use Izam\View\Form\Helper\Form\FormDate;
use Izam\View\Form\Helper\Form\FormDateRange;
use Izam\View\Form\Helper\Form\FormHidden;
use Izam\View\Form\Helper\Form\FormMap;
use Izam\View\Form\Helper\Form\FormMultipleDropDown;
use Izam\View\Form\Helper\Form\FormMultipleSelectActionMobile;
use Izam\View\Form\Helper\Form\FormNotImplemented;
use Izam\View\Form\Helper\Form\FormNumber;
use Izam\View\Form\Helper\Form\FormPhone;
use Izam\View\Form\Helper\Form\FormPhoto;
use Izam\View\Form\Helper\Form\FormSelect;
use Izam\View\Form\Helper\Form\FormSeparator;
use Izam\View\Form\Helper\Form\FormStaticContent;
use Izam\View\Form\Helper\Form\FormSubForm;
use Izam\View\Form\Helper\Form\FormTime;
use Izam\View\Form\Helper\Form\FormURL;
use Izam\View\Form\Helper\FormCollectionWrapper;
use Izam\View\Form\Helper\FormRadio;
use Izam\View\Form\Helper\Show\ShowTitleText;
use Laminas\Form\Element\Collection;
use Laminas\Form\Element\Date;
use Laminas\Form\Element\Hidden;
use Laminas\Form\Element\Image;
use Laminas\Form\Element\File;
use Laminas\Form\Element\Number;
use Laminas\Form\Element\Password;
use Laminas\Form\Element\Radio;
use Laminas\Form\Element\Text;
use Laminas\Form\Element\Textarea;
use Laminas\Form\Element\Url;
use Laminas\Form\Fieldset;

class Mapper
{
    protected static array $map = [
        Text::class => FormText::class,
        Price::class => FormPrice::class,
        Textarea::class => FormTextArea::class,
        Select::class => FormSelect::class,
        Radio::class => FormRadio::class,
        PrimaryKey::class => FormHidden::class,
        MultipleDropdown::class => FormMultipleDropDown::class,
        Image::class => FormPhoto::class,
        File::class => FormFile::class,
        MultipleFile::class => FormMultipleFile::class,
        Url::class => FormURL::class,
        Number::class => FormNumber::class,
        Hidden::class => FormHidden::class,
        ForeignKey::class => FormHidden::class,
        Date::class => FormDate::class,
        Checkbox::class => FormCheckbox::class,
        Separator::class => FormSeparator::class,
        DynamicDropdown::class => FormDynamicDropdown::class,
        AutoStaff::class => FormAutoSuggest::class,
        FollowUpStatus::class => FormFollowUpStatus::class,
        BranchDynamicDropdown::class => FormDynamicDropdown::class,
        MultipleDynamicDropdown::class => FormMultipleDropDown::class,
        FormattedText::class => FormFormattedText::class,
        Time::class => FormTime::class,
        Map::class => FormMap::class,
        Collection::class => FormCollectionWrapper::class,
        Currency::class => FormCurrency::class,
        CurrencyDropdown::class => FormCurrencyDropdown::class,
        CurrencyRange::class => FormCurrencyRange::class,
        Phone::class => FormPhone::class,
        NotImplemented::class => FormNotImplemented::class,
        Subform::class => FormSubForm::class,
        StaticContent::class => FormStaticContent::class,
        Link::class => FormLink::class,
        AuthLink::class => FormAuthLink::class,
        Fieldset::class => FormSubForm::class,
        LinkList::class => FormLinkList::class,
        AutoSuggest::class => FormAutoSuggest::class,
        AutoSuggestV2::class => FormAutoSuggest::class,
        DemoElement::class => FormDemoElement::class,
        QuantityWithUnit::class => FormQuantityWithUnit::class,
        AutoNumber::class => FormAutoNumber::class,
        Anchor::class => FormPageHeadAnchor::class,
        SplitButtonDropdowns::class => FormSplitButtonDropdowns::class,
        ListingQuickFilters::class => FormListingQuickFilters::class,
        Button::class => FormButton::class,
        Paragraph::class => FormParagraph::class,
        Sperator::class => FormSperator::class,
        Badge::class => FormBadge::class,
        Pagination::class => FormPagination::class,
        ViewActions::class => FormViewActions::class,
        Stack::class => FormStack::class,
        StatusCircle::class => FormStatusCircle::class,
        Icon::class => FormIcon::class,
        DateRange::class => FormDateRange::class,
        TimeRange::class => FormTimeRange::class,
        MultipleSelectAction::class => FormMultipleSelectAction::class,
        MultipleSelectActionIframe::class => FormMultipleSelectActionIframe::class,
        TitleText::class => ShowTitleText::class,
        DateTime::class => FormDateTime::class,
        Tags::class => FormTags::class,
        Password::class => FormPassword::class,
        Card::class => FormCard::class,
        AssignedStaff::class => FormAssignedStaff::class,
        Condition::class => FormCondition::class,
        NumberRange::class => FormNumberRange::class,
        ImageHeader::class => FormImageHeader::class,
        \Izam\View\Form\Element\Checkbox::class => FormCheckbox::class,

    ];

    protected static array $mobileMap = [
        Anchor::class => FormPageHeadAnchorMobile::class,
        SplitButtonDropdowns::class => PageHeadFormSplitButtonDropdownsMobile::class,
        ListingQuickFilters::class => FormListingQuickFiltersMobile::class,
        Button::class => FormButtonMobile::class,
        Paragraph::class => FormParagraph::class,
        Sperator::class => FormBlank::class, /** @Todo revisit wz jadallah */
        Badge::class => FormBadge::class,
        Pagination::class => FormPaginationMobile::class,
        Icon::class => FormIcon::class,
        MultipleSelectAction::class => FormMultipleSelectActionMobile::class,
        Stack::class => FormStackMobile::class,
        StatusCircle::class => FormStatusCircleMobile::class,
        TitleText::class => ShowTitleText::class,
        ImageHeader::class => FormImageHeader::class,
    ];

    protected static array $helpers = [];

    public static function getHelper($element)
    {
        $helperClass = static::$map[$element::class] ?? FormText::class;
        if (isset(static::$helpers[$helperClass])) {
            return static::$helpers[$helperClass];
        }

        return static::$helpers[$helperClass] = new $helperClass;
    }

    public static function hasHelper($element)
    {
        return array_key_exists($element::class, self::$map);
    }

    public static function getMobileHelper($element)
    {
        $helperClass = static::$mobileMap[$element::class] ?? FormText::class;
        if (isset(static::$helpers[$helperClass])) {
            return static::$helpers[$helperClass];
        }

        return static::$helpers[$helperClass] = new $helperClass;
    }
}
