<?php

namespace Izam\View\Form\Helper;

use Izam\View\View\FormInput;
use Laminas\Form\Element\Collection;
use Laminas\Form\ElementInterface;
use Laminas\Form\FieldsetInterface;

class FormCollectionWrapper extends FormInput
{
    /**
     * @param $element ElementInterface
     */
    public function render(ElementInterface $element): string
    {
        $js_options = [];

        $fieldsetHelper = new FormCollection();

        $fieldsetHelper
            ->setElementHelper(new SubformElementWrapper())
            ->setFieldsetHelper(new FieldSetWrapper())
            ->setWrapper('%1$s')
            ->setTemplateWrapper('%s');

        $markup = $templateMarkup = '';
        if($element->getTemplateElement()){
            $element->getTemplateElement()->setOption('show_index',$element->getOption('show_index'));
            $element->getTemplateElement()->setOption('from_template_element',true);
        }
        
        if ($element instanceof Collection && $element->shouldCreateTemplate()) {
            $templateMarkup = $fieldsetHelper->renderTemplate($element);
        }

        $wrapper = new FieldSetWrapper();
        $elmentsTableHeader = '';
        $subformElementWrapper = new SubformElementWrapper();

        foreach ($element->getTargetElement() as $el) {
            $elmentsTableHeader .= $subformElementWrapper->renderTableHeader($el);
        }

        foreach ($element->getIterator() as $elementOrFieldset) {
            if ($elementOrFieldset instanceof FieldsetInterface) {
                $elementOrFieldset->setOption('show_index',$element->getOption('show_index'));
                $markup .= $wrapper($elementOrFieldset, true);
            }
        }

        $id = $this->getId($element);

        $tfoot = '<tfoot>
                        <tr>
                            <td colspan="12">
                                <div class="btn-group gap-1">
                                    <button type="button" class="btn btn-secondary btn-responsive-icon" data-subform-add-row data-row-add-'.$id.'>
                                        <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                        <span>'.__t('Add').'</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tfoot>';
        $total = $element->getOption('total');
        if(!empty($total)) {
            $js_options['total'] = $total;
            $fieldsCount = $element->getTemplateElement()->count();
            $tableCoulmnCount = $fieldsCount + 2; // 2 for sort column and action column
            $colspan = 2;
            $tfoot = '<tfoot>
                <tr class="ui-table-box-row d-lg-table-row d-none">
                    <td colspan="'. $tableCoulmnCount - $colspan .'">
                        <div class="btn-group gap-1">
                            <button type="button" class="btn btn-secondary btn-responsive-icon" data-subform-add-row data-row-add-'.$id.'>
                                <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                <span>'.__t('Add').'</span>
                            </button>
                        </div>
                    </td>
                     <td colspan="'. $colspan .'" class="border-bottom border-end border-start subform-cell-text fw-medium">
                        <span class="text-dark-3">Total: </span>
                        <span class="table-total-' . $total[0] . '">0.00</span> 
                        './*L.E.*/'
                    </td>
                </tr>
                <tr class="ui-table-box-row d-lg-none">
                    <td>
                        <div class="btn-group gap-1">
                            <button type="button" class="btn btn-secondary btn-responsive-icon" data-subform-add-row data-row-add-'.$id.'>
                                <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                <span>'.__t('Add').'</span>
                            </button>
                        </div>
                    </td>
                        <td colspan="2" class="border-bottom border-end border-start subform-cell-text fw-medium">
                        <span class="text-dark-3">Total: </span>
                        <span class="table-total-' . $total[0] . '">0.00</span> 
                        './*L.E*/'.
                    </td>
                </tr>
            </tfoot>';
        }

        if ($element->getOption('tfoot')) $tfoot = $element->getOption('tfoot');
        $hideDelete = $element->getOption('hide_delete');
        $deleteTableHeader = '<th width="50"></th>';
        if ($hideDelete) $deleteTableHeader = '';
        $extraClasses = $element->getOption('hide_drag') ? ' d-none' : '';
        return '<div class="subform-container" data-subform-'.$id.'  data-options=\'' . json_encode($js_options) . '\'>
                    <table class="subform-table l-table-box ui-table-box" data-app-form-subform="'.$id.'" data-app-form-subform-options=\'{"sortable": ' . ($element->getAttribute('allow_sorting') ? 'true' : 'false') . ', "minimumRows": ' . ($element->getAttribute('minimum_row') ?? 0) . '}\'>
                    <thead>
                        <tr>
                            <th class="'. $extraClasses.'" width="50" >'.($element->getOption('show_index')? __t('No.'):'').'</th>
                            '.$elmentsTableHeader. $deleteTableHeader .'
                        </tr>
                    </thead>
                    <tbody>
                     ' .$markup. '
                     </tbody>
                     '. $tfoot .'
                </table>
                </div><template data-subform-template-row data-subform-template-'.$id.' >'. $templateMarkup. '</template>';
    }
}
