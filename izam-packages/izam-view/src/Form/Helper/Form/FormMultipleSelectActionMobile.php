<?php

namespace Izam\View\Form\Helper\Form;

use Izam\View\View\FormInput;
use Laminas\Form\ElementInterface;


class FormMultipleSelectActionMobile extends FormInput
{

    public function render(ElementInterface $element): string
    {
        if (!$element->getOption('show_in_mobile')) {
            return (new FormBlank())->render($element);
        }
        $out = '';
        $btnHelper = new FormButton();
        foreach ($element as $el) {
            $el->setLabelOption('disable_html_escape', true);
            $el->setAttribute('class', 'dropdown-item');
            if (!$el->getOption('modal')) {
                $el->setAttributes([
                    'data-la-action' => $el->getOption('action'),
                    'data-la-method' => $el->getOption('method'),
                ]);
            }
            $content = sprintf(
                "<i class='mdi mdi-%s me-4'></i><span>%s</span>",
                $el->getOption('icon') ?? '',
                $el->getLabel()
            );
            $out .= "<li>" . $btnHelper->render($el, $content) . "</li>";
        }
        return sprintf($this->getWrapper(), $out);
    }


    private function getWrapper(): string
    {
        return '<div class="hstack align-items-center gap-4 order-2">
                    <div class="dropdown listing-actions" data-lc-visible="true" style="display: none;">
                          <button type="button" class="btn btn-secondary gap-2 btn-touch" data-bs-toggle="dropdown"
                                aria-expanded="false" data-lc-actions="true">
                            <span class="listing-actions-selected" data-lc-selected="true"
                                  style="display: none;">(<span
                                        data-lc-count="true"></span> ' . __t('Selected') . ')</span>
                            <i class="mdi mdi-dots-vertical ms-4"></i>
                        </button>
                        <ul class="dropdown-menu">
                            %s
                        </ul> 
                    </div> 
                </div>';
    }

}
