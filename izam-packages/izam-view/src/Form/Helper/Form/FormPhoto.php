<?php

namespace Izam\View\Form\Helper\Form;

use Laminas\Form\ElementInterface;
use Izam\Entity\Models\File;

class FormPhoto extends BaseFormPhoto
{
    public function render(ElementInterface $element): string
    {
        $element->setAttributes($this->getAttributes($element));
        return parent::render($element);
    }

    /**
     * add new attributes to fit the new design for izam-view
     * a better soln is to be able to inject these attributes only once
     */
    private function getAttributes($element)
    {
        $attributes = $element->getAttributes();
        $url = $element->getOption('url');

        if ($url === null) {
            $fieldKey = $attributes['fieldKey'];
            $entityKey = $attributes['entityKey'];
            $url = "/v2/api/entity/upload_file/$entityKey/$fieldKey";
        }

        $allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'jpg', 'png', 'gif', 'zip', 'jpeg'];

        if ($attributes['type'] === 'photo') {
            $allowedExtensions = ['jpg', 'png', 'gif', 'zip', 'jpeg'];
        }
        $attributes['mimes'] = null;
        $attributes['max'] = null;
        $allowedExtensions = null;
        if (isset($attributes['mimes'])) {
            $allowedExtensions = str_replace(' ', '', $attributes['mimes']);
            $allowedExtensions = explode(',', $allowedExtensions);
        }

        $value = $element->getValue();
        //if single file it won't contain ,
        if (is_string($value) && str_contains($value, ',')) {
            $value = explode(',', $value);
        }

        if (!is_array(value: $value)) {
            $file = new File();
            $fileRecord = $file->find($value);
            if ($fileRecord) {
                /** dynamic attribute */
                $fileRecord->url = $fileRecord->url;
                $value = [$fileRecord];
            } else {
                $value = [];
            }
        } else {
            /** TODO re-fact this section */
            if (isset($value['key'])) {
                $value = [$value['key']];
            }
            /** @TODO : check here /v2/owner/entity/lease_contract/create */
            elseif (isset($value[0]) && is_array($value[0])) {
                $temp = array_column($value, 'key');
                $value = !empty($temp) ? $temp : array_column($value, 'id');
            }
            $files = (new File())->whereIn('id', $value)->get();
            foreach ($files as &$file) {
                $file->url = $file->url;
            }

            $value = $files->toArray();
        }

        $imageIds = [];
        if ($value) {
            $imageIds = array_column($value, 'id');
        }
        return [
            'type' => $attributes['type'],
            'name' => $attributes['name'],
            'data-app-form-uploader-options' => json_encode([
                'lockFormSubmitWhileUploading' => true,
                'uploadUrl' => $url,
                'rules' => [
                    'max' => $this->fixMaxSize($attributes['max']),
                    'ext' => $allowedExtensions,
                ],
                'multiple' => $attributes['multiple'] ?? false,
                'sortable' => $attributes['sortable'] ?? false,
            ]),
            'value' => implode(',', $imageIds),
            'data-input-uploader-value' => json_encode($this->getValue($value)),
        ];
    }

    private function getValue($value): array
    {
        if (isset($value['value'])) {
            $value = [$value['value']];
        }
        return array_map(function ($value) {
            return [
                "id" => $value['id'],
                "name" => htmlspecialchars($value['name'], ENT_QUOTES, 'UTF-8'),
                "size" => $value['file_size'],
                "url" => $value['url']
            ];
        }, $value);
    }

    private function fixMaxSize($max)
    {
        if (!$max) return null;
        return ($max / 1000) * 1024 * 1024;
    }
}
