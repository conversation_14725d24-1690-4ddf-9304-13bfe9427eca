<?php

namespace Izam\View\Form\Helper\Form;

use Izam\View\View\FormInput;
use Laminas\Form\ElementInterface;

class FormCheckbox extends FormInput
{
    public function render(ElementInterface $element): string
    {
        $labelHelper = new FormLabel();

        if ($element->isChecked()) {
            $element->setAttribute('checked', 'checked');
        }

        $element->setLabelOption('always_wrap', true);
        $defaultClasses = "form-check-input";
        if ($element->getOption('extraClasses') !== null) {
            $defaultClasses .= " " . $element->getOption('extraClasses');
        }
        $element->setAttribute('class', $defaultClasses);
        $element->setAttribute('id', $this->getId($element));
        $hideLabelClassAttribute = $element->getOption('hideLabelClassAttribute');
        if (!isset($hideLabelClassAttribute)) {
            $element->setLabelAttributes(['class' => 'form-check form-check-custom form-switch']);
        }
        $out = $this->renderCheckBox($element);
        $out .= sprintf('<span class="form-check-label">%s %s</span>', __t($element->getLabel()), $this->getTooltip($element));
        return $labelHelper($element, $out);
    }

    public function getTooltip($element){
        $tooltip = $element->getOption('tooltip');
        if($tooltip) {
            return sprintf('<span class="tip-circle tip ps-2" title="%s"><i class="fas fa-question-circle"></i></span>', $tooltip);
        }
        return '';
    }

    /**
     *  Laminas Original Render
     */
    private function renderCheckBox(ElementInterface $element): string
    {
        $name = $element->getName();
        if ($name === null || $name === '') {
            throw new Exception(sprintf(
                '%s requires that the element has an assigned name; none discovered',
                __METHOD__
            ));
        }

        $attributes = $element->getAttributes();
        $attributes['name'] = $name;
        $attributes['type'] = $this->getType($element); // toDO change getType method to getInputType
        $attributes['value'] = $element->getCheckedValue();
        $closingBracket = $this->getInlineClosingBracket();

        if ($element->isChecked()) {
            $attributes['checked'] = 'checked';
        }
        $rendered = sprintf(
            '<input %s%s',
            $this->createAttributesString($attributes),
            $closingBracket
        );
        if ( method_exists($element, 'useHiddenElement') && $element->useHiddenElement()) {
            $hiddenAttributes = [
                'disabled' => $attributes['disabled'] ?? false,
                'name' => $attributes['name'],
                'value' => $element->getUncheckedValue(),
            ];
            $rendered = sprintf('<input type="hidden" %s%s', $this->createAttributesString($hiddenAttributes), $closingBracket) . $rendered;
        }
        return $rendered;
    }


    protected function getType($element)
    {
        return 'checkbox';
    }
}
