<?php

namespace Izam\View\Form\Helper\Form;

use Laminas\Form\ElementInterface;

class FormCard extends FormAnchor
{

    public function render(ElementInterface $element, ?string $content = null): string
    {
        $element->setAttribute('class', 'setting-card mb-10');

        if ( $element->getOption('customIcon') ) {
            $content = sprintf('<div class="card-body">
                                      <div class="vstack gap-8">
                                        '. $element->getOption('customIcon') .'
                                         <span class="title">%s</span>
                                      </div>
                                </div>', $element->getLabel());
        } else {
            $content = sprintf('<div class="card-body">
                                      <div class="vstack gap-8">
                                        <i class="icon mdi mdi-%s"></i>
                                         <span class="title">%s</span>
                                      </div>
                                </div>', $element->getOption('icon'), $element->getLabel());
        }
        return parent::render($element, $content);
    }

    protected function getType($element)
    {
        return 'card';
    }
}
