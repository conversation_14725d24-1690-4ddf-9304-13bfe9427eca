<?php

namespace Izam\View\Form\Helper\Show;

use Izam\Forms\View\Helper\Show\ShowQuantity;
use Izam\View\Form\Element\AdvancedDynamicDropdown;
use Izam\View\Form\Element\AssignedStaff;
use Izam\View\Form\Element\AutoSuggest;
use Izam\View\Form\Element\Currency;
use Izam\View\Form\Element\DynamicDropdown;
use Izam\View\Form\Element\DynamicPermission;
use Izam\View\Form\Element\FactorText;
use Izam\View\Form\Element\FollowUpStatus;
use Izam\View\Form\Element\IconDropdown;
use Izam\View\Form\Element\Map;
use Izam\View\Form\Element\MultipleDropdown;
use Izam\View\Form\Element\MultipleDynamicDropdown;
use Izam\View\Form\Element\MultipleFile;
use Izam\View\Form\Element\Phone as PhoneElement;
use Izam\View\Form\Element\Quantity;
use Izam\View\Form\Element\Select;
use Izam\View\Form\Element\Separator;
use Izam\View\Form\Element\AutoStaff;
use Izam\View\Form\Element\StaticContent;
use Izam\View\Form\Element\Tags;
use Izam\View\Form\Element\UnitText;
use Izam\View\Form\Helper\Show\CheckBox as ShowCheckBox;
use Laminas\Form\Element\Collection;
use Laminas\Form\Element\Email;
use Laminas\Form\Element\File;
use Laminas\Form\Element\Image;
use Laminas\Form\Element\Url;
use Laminas\Form\Element\Checkbox;

class ShowMapper
{
    protected static array $map = [
        MultipleDropdown::class => \Izam\View\Form\Helper\Show\MultipleDropDown::class,
        DynamicDropdown::class => \Izam\View\Form\Helper\Show\MultipleDropDown::class,
        AutoSuggest::class => \Izam\View\Form\Helper\Show\MultipleDropDown::class,
        MultipleDynamicDropdown::class => \Izam\View\Form\Helper\Show\MultipleDropDown::class,
        Tags::class => ShowTags::class,
        Image::class => \Izam\View\Form\Helper\Show\Image::class,
        File::class => \Izam\View\Form\Helper\Show\File::class,
        Select::class => \Izam\View\Form\Helper\Show\DropDown::class,
        Collection::class => CollectionWrapper::class,
        Checkbox::class => ShowCheckBox::class,
        StaticContent::class => ShowStaticContent::class,
        Currency::class => \Izam\View\Form\Helper\Show\Currency::class,
        Map::class => \Izam\View\Form\Helper\Show\Map::class,
        Separator::class => \Izam\View\Form\Helper\Show\Separator::class,
        PhoneElement::class => Phone::class,
        Url::class => \Izam\View\Form\Helper\Show\URL::class,
        Email::class => \Izam\View\Form\Helper\Show\Email::class,
        IconDropdown::class => \Izam\View\Form\Helper\Show\IconDropdown::class,
        DynamicPermission::class => ShowDynamicPermission::class,
        Quantity::class => ShowQuantity::class,
        AdvancedDynamicDropdown::class => ShowAdvancedDynamicDropDown::class,
        FactorText::class => ShowFactorText::class,
        UnitText::class => ShowUnitText::class,
        AutoStaff::class =>\Izam\View\Form\Helper\Show\MultipleDropDown::class,
        AssignedStaff::class => ShowAssignedStaff::class,
        FollowUpStatus::class => ShowFollowUpStatus::class,
        MultipleFile::class => ShowMultipleFile::class,
    ];

    protected static array $helpers = [];

    public static function getHelper($element)
    {

        $helperClass = static::$map[$element::class] ?? Text::class;
        if (isset(static::$helpers[$helperClass])) {
            return static::$helpers[$helperClass];
        }

        return static::$helpers[$helperClass] = new $helperClass;
    }
}
