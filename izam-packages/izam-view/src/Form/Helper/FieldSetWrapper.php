<?php

namespace Izam\View\Form\Helper;

use Izam\View\Form\Mapper;
use Izam\View\View\FormInput;
use Laminas\Form\Element\Hidden;
use Laminas\Form\ElementInterface;

class FieldSetWrapper extends FormInput
{
    public function render(ElementInterface $element): string
    {
        $rowAttribute = $element->getOption('row-attribute') ?? '';
        $markup = '';
        $hiddenInputs = '';
        $subformElementWrapper = new SubformElementWrapper();
        foreach ($element->getIterator() as $subElement) {
            $markup .= $subformElementWrapper->render($subElement);
            if ($subElement instanceof Hidden) {
                $hiddenInputs .=  Mapper::getHelper($subElement)->render($subElement);
            }
        }
        $extraClasses = $element->getOption('hide_drag') ? ' d-none' : '';
        $hideDelete = $element->getOption('hide_delete');
        $deleteBtnContent = '
                <td class="subform-cell-actions subform-cell-actions-end" width="50" >'.$hiddenInputs.'
                    <button class="subform-cell-remove" type="button" data-cell-remove="true" data-cell-remove>
                        <i class="mdi mdi-trash-can"></i>
                    </button>
                </td>
            </tr>
        ';
        if ($hideDelete) $deleteBtnContent = '';
         return  '
            <tr '.$rowAttribute. ' >
            '.($element->getOption('show_index')? 
                '
                <td width="50" data-row-no="true" class="subform-cell-text">
                '.($element->getOption('from_template_element')? '__row_no__':'1').'
                </td>
                '
            :
                '<td class="subform-cell-actions ' . $extraClasses . '" width="50" >
                    <button class="subform-cell-drag" type="button" data-cell-drag>
                        <i class="mdi mdi-drag-vertical"></i>
                    </button>
                </td>'
                ).
                $markup . $deleteBtnContent;
    }
}
