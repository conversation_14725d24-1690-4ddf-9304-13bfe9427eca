<?php

namespace Izam\Entity\Repository;

use Izam\Entity\Models\EntityView;
use Izam\Database\Capsule\Manager;

/**
 * Repository class for managing entity view logs.
 * 
 * This repository handles the persistence and retrieval of entity view records,
 * tracking when staff members view specific entities in the system.
 * 
 * @package Izam\Entity\Repository
 */
class EntityViewsLogsRepository extends BaseRepository
{
    public function model()
    {
        return new EntityView();
    }
 

    /**
     * Inserts a new entity view log record if it doesn't already exist.
     * 
     * This method checks if a view log already exists for the given combination
     * of entity key, entity ID, and staff ID. If no record exists, it creates
     * a new one to track that the staff member has viewed this entity.
     * 
     * @param string $entityKey The type/key of the entity being viewed
     * @param int    $entityId  The unique identifier of the entity
     * @param int    $staffId   The unique identifier of the staff member
     * 
     * @return void
     */
    public static function insertIfNotExist($entityKey, $entityId, $staffId)
    {
        $currentLog = EntityView::where('entity_key', $entityKey)
            ->where('entity_id', $entityId)
            ->where('staff_id', $staffId)
            ->get()->toArray();
        if(!$currentLog){
            Manager::table('entity_views')->insert(
                [
                    'entity_key' => $entityKey,
                    'entity_id' => $entityId,
                    'staff_id' => $staffId
                ]
            );
        }
    }

    public function getEntityViewsLogs($entityKey, $entityIds, $staffId){
        return $this->model
            ->where('entity_key', $entityKey)
            ->whereIn('entity_id', $entityIds)
            ->where('staff_id', $staffId)
            ->get()->keyBy('entity_id')->toArray();
    }
   
}
