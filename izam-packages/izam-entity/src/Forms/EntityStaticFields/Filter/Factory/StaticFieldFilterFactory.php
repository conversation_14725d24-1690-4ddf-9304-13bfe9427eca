<?php

namespace Izam\Entity\Forms\EntityStaticFields\Filter\Factory;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Forms\EntityStaticFields\Filter\Interfaces\IStaticFieldFilter;
use Izam\Entity\Forms\EntityStaticFields\Filter\Interfaces\IStaticFieldFilterFactory;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\MultiCycleApprovalConfigurationStaticFilter;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\AssetStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\ContractInstallmentStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\ContractStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\DefaultStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\JournalStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\LeaseContractStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\LeaveApplicationStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\ManufacturingOrdersIndirectCostsStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\ManufacturingOrdersStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\MaterialBillsStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\ProductionRoutingStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\PurchaseInvoiceStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\RecurringProfileStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\RentalReservationOrderStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\RentalUnitStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\SalesOrderStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\StockRequestStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\ProductionPlanStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\TreasuryTransferStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\WorkFlowStaticFilterFields;
use Izam\Entity\Forms\EntityStaticFields\Filter\Specs\EntityDocumentStaticFilterFields;

class StaticFieldFilterFactory implements IStaticFieldFilterFactory
{

    public static function getEntitySpec(string $entityKey, $extraData = []): IStaticFieldFilter
    {
        if (str_starts_with($entityKey, 'le_workflow-type') !== false) {
            return new WorkFlowStaticFilterFields($entityKey);
        }
        return match ($entityKey) {
            EntityKeyTypesUtil::CONTRACT => new ContractStaticFilterFields(),
            EntityKeyTypesUtil::PURCHASE_INVOICE, EntityKeyTypesUtil::PURCHASE_REFUND => new PurchaseInvoiceStaticFilterFields(),
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => new LeaveApplicationStaticFilterFields(),
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => new StockRequestStaticFilterFields(),
            EntityKeyTypesUtil::SALES_ORDER => new SalesOrderStaticFilterFields(),
            EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY => new ProductionRoutingStaticFilterFields(),
            EntityKeyTypesUtil::BOM_ENTITY_KEY => new MaterialBillsStaticFilterFields(),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => new ManufacturingOrdersStaticFilterFields(),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => new ManufacturingOrdersIndirectCostsStaticFilterFields(),
            EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER => new RentalReservationOrderStaticFilterFields(),
            EntityKeyTypesUtil::RENTAL_UNIT => new RentalUnitStaticFilterFields(),
            EntityKeyTypesUtil::JOURNAL => new JournalStaticFilterFields(),
            EntityKeyTypesUtil::TREASURY_TRANSFER => new TreasuryTransferStaticFilterFields(),
            EntityKeyTypesUtil::PRODUCTION_PLAN => new ProductionPlanStaticFilterFields(),
            EntityKeyTypesUtil::ASSET => new AssetStaticFilterFields($extraData),
            EntityKeyTypesUtil::RECURRING_PROFILE => new RecurringProfileStaticFilterFields(),
            EntityKeyTypesUtil::LEASE_CONTRACT => new LeaseContractStaticFilterFields($extraData),
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => new ContractInstallmentStaticFilterFields(),
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => new MultiCycleApprovalConfigurationStaticFilter($extraData),
            EntityKeyTypesUtil::ENTITY_DOCUMENT => new EntityDocumentStaticFilterFields($extraData),
            default => new DefaultStaticFilterFields(),
        };
    }
}
