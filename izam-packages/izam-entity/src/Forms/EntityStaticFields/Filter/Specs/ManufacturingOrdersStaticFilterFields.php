<?php

namespace Izam\Entity\Forms\EntityStaticFields\Filter\Specs;

use App\Facades\Branch;
use App\Facades\Plugins;
use App\Repositories\ClientRepository;
use App\Repositories\ProductRepository;
use App\Repositories\WorkstationRepository;
use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\Formatter\ClientImageAvatar;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ClientOption;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ProductOption;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\StaffOption;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\SubProductionRoutingOption;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\WorkstationOption;
use Izam\Entity\Forms\EntityStaticFields\Filter\Interfaces\IStaticFieldFilter;
use Izam\View\Form\Element\AutoSuggest;
use Izam\Entity\Repository\DynamicRepo;
use Laminas\Form\Element\Hidden;

class ManufacturingOrdersStaticFilterFields implements IStaticFieldFilter
{

    public function generateElements(): array
    {
        $formFilters = [
            [
                'spec' => [
                    'name' => 'filter[manufacturing_order_employees.employee_id][in]',
                    'type' => AutoSuggest::class,
                    'options' => [
                        "identifier" => "id",
                        "property" => "name",
                        'order' => 2,
                        "filter_size"=> 4,
                        'label' => __t('Search by Name, Code, E-mail, Department, Branch, country...etc'),
                        "find_method" =>  ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::STAFF_ENTITY_KEY]],
                        'target_class'=> DynamicRepo::class,
                        'auto_suggest_url' =>  ProductOption::productsFilterAutoSuggestUrl(),
                        "auto_suggest_url" => "/v2/owner/staff/search?term=d&_type=query&q=__q__",

                        'template' => StaffOption::class,
                        'target_entity' => EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                        'advanced'=>false,
                    ],
                    'attributes' => [
                        'multiple' => "multiple",
                        'placeholder' => __t('Search by Name, Code, E-mail, Department, Branch, country...etc'),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[materials.product_id]',
                    'type' => AutoSuggest::class,
                    'options' => [
                        "identifier" => "id",
                        "property" => "name",
                        'order' => 3,
                        "filter_size"=> 4,
                        'label' => __t('Materials'),
                        "find_method" =>  ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::PRODUCT_ENTITY_KEY]],
                        'target_class'=> DynamicRepo::class,
                        'auto_suggest_url' =>  ProductOption::productsFilterAutoSuggestUrl(),
                        'template' => ProductOption::class,
                        'target_entity' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                        "is_single"=>true,
                        'advanced'=>true,
                    ],
                    'attributes' => [
                        'placeholder' => sprintf(__t('Filter by %s'), __t('Materials')),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[scraps.product_id]',
                    'type' => AutoSuggest::class,
                    'options' => [
                        "identifier" => "id",
                        "property" => "name",
                        'order' =>4,
                        "filter_size"=> 4,
                        'label' => __t('Scrap product'),
                        "find_method" =>  ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::PRODUCT_ENTITY_KEY]],
                        'target_class'=> DynamicRepo::class,
                        'auto_suggest_url'=> ProductOption::getAutoSuggestUrl(),
                        'template' => ProductOption::class,
                        'target_entity' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                        'advanced'=>true,
                        "is_single"=>true,
                    ],
                    'attributes' => [
                        'placeholder' => sprintf(__t('Filter by %s'), __t('Scrap Items')),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[operations.workstation_id]',
                    'type' => AutoSuggest::class,
                    'order_index' => 10,
                    'options' => [
                        'auto_suggest_url' =>  WorkstationOption::getAutoSuggestUrl(),
                        'template' => WorkstationOption::class,
                        "property" => "name",
                        'order' =>10,
                        "filter_size"=> 4,
                        "identifier" => "id",
                        'target_class'=> DynamicRepo::class,
                        "find_method" =>  ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY]],
                        'target_entity' => EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY,
                        "is_single"=>true,
                        'advanced'=>true,
                    ],
                    'attributes' => [
                        'placeholder' =>sprintf(__t('Select %s'), __t('Workstation')),
                    ],
                ],
            ],[
                //materials
                'spec' => [
                    'name' => 'filter[or][materials.sub_production_routing_id]',
                    "attributes" => [
                        "data-sub-production_routing_input" => true,
                        'placeholder' =>sprintf(__t('Select %s'),__t("Production Operation"))
                    ],
                    'type' => AutoSuggest::class,
                    'options' => [
                        'label' => __t('Production Operation'),
                        'order' => 5,
                        "filter_size"=> 4,
                        'advanced' => false,
                        'auto_suggest_url' =>  SubProductionRoutingOption::getSubProductionRoutingUrl(),
                        "identifier" => "id",
                        "property" => "name",
                        "target_class" => DynamicRepo::class,
                        "is_single"=>true,
                        "find_method" =>  ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::SUB_PRODUCTION_ROUTING_ENTITY_KEY]]
                    ]
                ],
            ],[
                //operations
                'spec' => [
                    'name' => 'filter[or][operations.sub_production_routing_id]',
                    "attributes" => [
                        "data-sub-production-routing-hidden-input" => true
                    ],
                    'type' => Hidden::class,
                ],
            ],[
                //expenses
                'spec' => [
                    'name' => 'filter[or][expenses.sub_production_routing_id]',
                    "attributes" => [
                        "data-sub-production-routing-hidden-input" => true
                    ],
                    'type' => Hidden::class,
                ],
            ],[
                //scraps
                'spec' => [
                    'name' => 'filter[or][scraps.sub_production_routing_id]',
                    "attributes" => [
                        "data-sub-production-routing-hidden-input" => true
                    ],
                    'type' => Hidden::class,
                ],
            ]
        ];


        return $formFilters;
    }

    public function generateFieldSets(): array
    {
        return [

        ];
    }
}
