<?php

namespace Izam\Entity\Service;

use Izam\Entity\Forms\EntityStaticFields\Form\Factory\StaticFieldFormFactory;
use Izam\Entity\Repository\Proxy\FieldRelationRepository;
use Izam\Entity\Repository\Proxy\SchemaRepository;
use Laminas\Form\Element\Collection;
use Laminas\Form\Element\Hidden;
use Laminas\Form\Factory;

class EntityToForm
{
    public const FORM_CREATE = 'form_create';
    public const FORM_UPDATE = 'form_update';
    public const FORM_SHOW = 'form_show';

    private FieldRelationRepository $fieldRelationRepository;
    private SchemaRepository $schemaRepository;

    public function __construct(
        private SpecBuilder $specBuilder,

    ) {
        $this->fieldRelationRepository = resolve(FieldRelationRepository::class);
        $this->schemaRepository = resolve(SchemaRepository::class);
    }

    public function build($entityKey, $repo = null , $mode = self::FORM_CREATE, $backwardCompatibility = false)
    {
        $entity = $this->schemaRepository->getEntity($entityKey);

        if (!$entity) {
            throw new \Exception("Entity $entityKey not found");
        }

        foreach ($entity->fields as $i => $field) {
            if ($field['field_type'] == 'subform') {
                $subFormsIndexes[$field['key']] = $i;
            }
        }

        $fields = $entity->fields
            ->where('field_type', '!=', 'subform');

        if ($mode !== self::FORM_SHOW) {
            $fields = $fields->filter(function ($field) use ($mode) {
                $fieldMatch = match ($mode) {
                    self::FORM_UPDATE => ($field['on_update'] ?? 0) == 1,
                    self::FORM_CREATE => ($field['on_create'] ?? 0) == 1,
                };
                return $fieldMatch || $field['field_type'] == 'separator';
            });
        } else {
            $fields = $fields->filter(function ($field) use ($mode) {
                return $field['field_type'] !== 'extend_foreign_key';
            });
        }

        if ($fields->count() == 0) {
            return null;
        }

        $spec = $this->specBuilder->build($fields, $backwardCompatibility);
        $spec['elements'] = array_merge($spec['elements'], StaticFieldFormFactory::getEntitySpec($entityKey)::generateElements());
        $spec['elements'] = $this->sortSpec(
            $spec['elements'], $this->handleSubForms($entity, $subFormsIndexes ?? [], $mode)
        );
        $factory = new Factory();

        return $factory->createForm($spec);
    }

    private function handleSubForms($entity, $indexes, $mode): array
    {
        $subForms = $entity->fields->where('field_type', 'subform');

        $specs = [];

        $specBuilder = new SpecBuilder();

        foreach ($subForms as $subForm) {
            $temp = [];

            $fieldRelation = $this->fieldRelationRepository->findOneBy(['field_key' => $subForm['key']]);

            if (!$fieldRelation) {
                throw new \Exception("Field relation not found");
            }

            $entityKey = $fieldRelation->reference_entity_key;

            $subformEntity = $this->schemaRepository->getEntity($entityKey);

            if (!$subformEntity) {
                throw new \Exception("Entity $entityKey not found");
            }

            $formName = $fieldRelation->name;

            if ($mode != self::FORM_SHOW) {
                $fields = $subformEntity->fields
                    ->where(match ($mode) {
                        self::FORM_UPDATE => 'on_update',
                        self::FORM_CREATE => 'on_create'
                    }, 1);
            } else {
                $fields = $subformEntity->fields;
            }

            $factory = new Factory();
            $targetFieldSet = $factory->createFieldset($specBuilder->build($fields));
            $options = json_decode($subForm['options'], true);
            $options['target_element'] = $targetFieldSet;
            $options['count'] = 1;
            $options['should_create_template'] = true;
            $options['allow_add'] = true;
            if ($entity->is_global) {
                $options['theme'] = 'table_with_headers';
            }
            $options['attributes'] = true;
            $options['label'] = $subForm['label'];
            $options['show_index'] = is_string($subForm['options']) ?
                json_decode($subForm['options'])->show_index ?? false : false;

            $temp['spec'] = [
                'name' => $formName,
                'type' => Collection::class,
                'options' => $options,
                'attributes' => $subForm['validation_rules']
            ];

            $specs[$indexes[$subForm['key']]] = $temp;
        }

        return $specs;
    }

    private function sortSpec($fields, $subForms)
    {
        $allElements = $subForms;

        foreach ($fields as $i => $field) {
            $nextIndex = $i;

            while (array_key_exists($nextIndex, $allElements)) {
                $nextIndex++;
            }

            $allElements[$nextIndex] = $field;
        }

        ksort($allElements);

        return $allElements;
    }

    public function buildWithToken($entityKey, $method = 'POST')
    {
        $fields = $this->schemaRepository->get($entityKey, [
            'on_' . ($method == 'POST' ? 'create' : 'update') => 1
        ]);

        if ($fields->count() == 0) {
            return null;
        }

        $spec = $this->specBuilder->build($fields);
        $factory = new Factory();
        $form = $factory->createForm($spec);

        $form->add(new Hidden('_token'));
        $form->add(new Hidden('_method'));

        $form->remove('created');
        $form->remove('modified');

        $form->setData([
            '_token' => csrf_token(),
            '_method' => $method,
        ]);

        return $form;
    }
}
