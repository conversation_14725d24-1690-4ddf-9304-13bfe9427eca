<?php

namespace Izam\Entity\Components\ListingPageHeader\EntityComponents;

use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\MultipleSelectAction;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class StockRequestComponent extends DefaultEntityComponent implements IEntityComponent
{

    public static function generateRightPageHeaderButtons(): array
    {
        $anchor = new Anchor('Add-Stock-Request');
        $anchor->setLabel(sprintf(__t('Add %s'), __t('Stock Request')))
            ->setAttribute('href', '/v2/owner/entity/stock_request/create');

        $searchBtn = new Button('search-btn');
        $searchBtn->setOption('theme', 'hideInDesktop');

        return [
            $searchBtn,
            $anchor,
        ];
    }

    public static function generateLeftPageHeaderButtons(): array
    {
        $multipleSelectAction = new MultipleSelectAction('Multiple-Select-Action');

        $btnExport = new Button('Export');
        $btnExport->setOption('icon', 'export')
            ->setOption('action', route('owner.viewExport', ['entity' => EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY]))
            ->setOption('method', 'GET')
            ->setAttribute('onclick', 'addMultiIDsURLDiffName(this)')
            ->setLabel(__t('Export'));

        $multipleSelectAction->add($btnExport);

        return [
            $multipleSelectAction
        ];
    }

    public static function pageButtonsWithoutData(): array
    {
        $addBtn = new Anchor('Add-Stock-Request');
        $addBtn->setLabel(sprintf(__t('Add %s'), __t('Stock Request')))
            ->setOption('icon', 'plus')
            ->setOption('theme', 'defaultSuccess')
            ->setAttribute('href', '/v2/owner/entity/stock_request/create');

        return [
            $addBtn,
        ];
    }
}