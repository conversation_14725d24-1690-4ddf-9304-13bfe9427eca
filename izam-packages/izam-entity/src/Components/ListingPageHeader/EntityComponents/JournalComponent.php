<?php

namespace Izam\Entity\Components\ListingPageHeader\EntityComponents;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;
use Izam\Recurring\Repositories\RecurringProfilesRepository;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\MultipleSelectAction;

class JournalComponent extends DefaultEntityComponent implements IEntityComponent
{

    public static function generateRightPageHeaderButtons(): array
    {
        $anchor = new Anchor('Add-Entry');
        $anchor->setLabel(sprintf(__t('Add %s'), __t('Entry')))
            ->setAttribute('href', '/owner/journals/add');

        $anchorProfiles = static::RecurringJournalButton();
        $anchorImport = static::importJournalButton();

        $anchorLogs = new Anchor('Journal-Logs');
        $anchorLogs->setLabel(__t('Journal Logs'))
            ->setOption('theme', 'light')
            ->setOption('icon', 'book')
            ->setAttribute('href', '/owner/journal_logs/index');

        $searchBtn = new Button('search-btn');
        $searchBtn->setOption('theme', 'hideInDesktop');

        return [
            $searchBtn,
            $anchorImport,
            $anchorLogs,
            $anchorProfiles,
            $anchor,
        ];
    }

    public static function generateLeftPageHeaderButtons(): array
    {
        $multipleSelectAction = new MultipleSelectAction('Multiple-Select-Action');

        $btnPrintPdf = new Button('Print-PDF');
        $btnPrintPdf->setOption('icon', 'export')
            ->setOption('action', '/owner/printable_templates/multi_pdfs/journal')
            ->setOption('method', 'POST')
            ->setAttribute('data-la-separate', 'comma')
            ->setLabel(__t('Print PDF'));

        $btnDelete = new Button('Delete');
        $btnDelete->setOption('icon', 'delete')
            ->setLabel(__t('Delete'))
            ->setOption('action', '/v2/owner/entity-bulk-delete/journal')
            ->setOption('method', 'POST')
            ->setAttributes([
                'data-la-confirm' => json_encode([
                    'message' => sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Journal Entries')),
                    'icon' => 'mdi mdi-trash-can-outline',
                    'color' => 'danger',
                ]),
                'data-la-data' => json_encode([
                    'entity_key' => EntityKeyTypesUtil::JOURNAL,
                    'entity_name' => __t('Journal'),
                    'target_url' => '/api2/journals/__id__',
                    'title' => __t('Delete'),
                    'back_url' => '/v2/owner/entity/journal/list',
                    'action_type' => 'Deleting',
                    'action' => 'DELETE',
                    '_method' => 'POST',
                    'filter_query' => json_encode(request()->query()),
                    '_token' => csrf_token(),
                    'title_for_layout' => sprintf(__t('Deleting %s', true), __t('Journal Entries', true)),
                    'breadcrumbs' => json_encode([
                        [
                            'title' => __t('Journal', true),
                            'link' => '/owner/journals/index',
                        ],
                        ['title' => __t('Delete')]
                    ])
                ]),
            ]);

        $multipleSelectAction->add($btnPrintPdf)
            ->add($btnDelete);

        return [
            $multipleSelectAction
        ];
    }

    public static function pageButtonsWithoutData(): array
    {
        $addBtn = new Anchor('Add-Entry');
        $addBtn->setLabel(sprintf(__t('Add %s'), __t('Entry')))
            ->setOption('icon', 'plus')
            ->setOption('theme', 'defaultSuccess')
            ->setAttribute('href', '/owner/journals/add');

        return [
            $addBtn,
        ];
    }

    private static function RecurringJournalButton()
    {
        $anchorProfiles = null;
        $conditionsOfRecurring = [
            ['entity_key', EntityKeyTypesUtil::JOURNAL]
        ];
        if (ifPluginActive(PluginUtil::BranchesPlugin)) {
            $conditionsOfRecurring[] = ['branch_id', getCurrentBranchID()];
        }
        if (!check_permission(MANAGE_ALL_JOURNALS) && check_permission(MANAGE_OWN_JOURNALS)) {
            $conditionsOfRecurring[] = ['staff_id', getAuthStaff('id')];
        }
        $recurringProfiles = RecurringProfilesRepository::getRecurringProfilesByConditions($conditionsOfRecurring);
        if ($recurringProfiles && count($recurringProfiles)) {
            $anchorProfiles = new Anchor('Recurring-Journal-Profiles');
            $anchorProfiles->setLabel(__t('Recurring Journal Profiles'))
                ->setOption('theme', 'primary')
                ->setOption('icon', 'table')
                ->setAttribute('href', '/v2/owner/recurring-profile');
        }
        return $anchorProfiles;
    }

    private static function importJournalButton() : ?Anchor
    {
        if (!check_permission(MANAGE_ALL_JOURNALS) && !check_permission(MANAGE_OWN_JOURNALS) && !check_permission(MANAGE_DRAFT_JOURNALS)) { 
            return null;
        }

        $anchorImport = new Anchor('Import-Journal');
        $anchorImport->setLabel(__t('Import Journal'))
            ->setOption('theme', 'primary')
            ->setOption('icon', 'file-import')
            ->setAttribute('href', '/v2/owner/import/journal');

        return $anchorImport;
    }
}