<?php

namespace Izam\Entity\Components\ListingPageHeader\EntityComponents;


use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\MultipleSelectAction;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class MultiCycleApprovalConfigComponent extends DefaultEntityComponent implements IEntityComponent
{

    public static function generateRightPageHeaderButtons(): array
    {
        $anchor = new Anchor('Add-Approval-Config');
        $anchor->setLabel(sprintf(__t('Add %s'), __t('Approval Configuration')))
            ->setAttribute('href', route('owner.entity.create', ['entityKey' => EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION]));

        $searchBtn = new Button('search-btn');
        $searchBtn->setOption('theme', 'hideInDesktop');
    
        return [
            $searchBtn,
            $anchor,
        ];
    }

    public static function pageButtonsWithoutData(): array
    {
        $anchor = new Anchor('Add-Approval-Config');
        $anchor->setLabel(sprintf(__t('Add %s'), __t('Approval Configuration')))
            ->setAttribute('href', route('owner.entity.create', ['entityKey' => EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION]))
            ->setOption('icon', 'plus')
            ->setOption('theme', 'defaultSuccess');

        return [
            $anchor,
        ];
    }
}