<?php

namespace Izam\Entity\Components\ListingPageHeader\EntityComponents;

use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\MultipleSelectAction;

class ContractInstallmentComponent extends DefaultEntityComponent implements IEntityComponent
{


    public static function generateRightPageHeaderButtons(): array
    {
        $searchBtn = new Button('search-btn');
        $searchBtn->setOption('theme', 'hideInDesktop');

        return [
            $searchBtn,
        ];
    }

    public static function generateLeftPageHeaderButtons($pagination = []): array
    {
        if(
            empty($pagination)
            || (is_object($pagination) && method_exists($pagination, 'total') && $pagination->total() == 0)
        ) return [];
        $multipleSelectAction = new MultipleSelectAction('Multiple-Select-Action');
        //get all query params as string
        $queryParams = urldecode(http_build_query($_GET));
        $multipleSelectAction->setOption('conditions_link', $queryParams)
            ->setOption('show_in_mobile', true);
        $btnPrintPdf = new Button('Export-Installment');
        $btnInvoice = new Button('pay-Installment');
        $btnPrintPdf->setOption('icon', 'export')
            ->setOption('action', '/v2/owner/entity/export/contract_installment')
            ->setOption('method', 'GET')
            ->setAttribute('data-la-separate', 'comma')
            ->setLabel(__t('Export'));
        $btnInvoice->setOption('icon', 'pay')
            ->setOption('action', '/v2/owner/entity/get_multi_action_ids/contract_installment')
            ->setOption('method', 'GET')
            ->setOption('icon', 'currency-usd')
            ->setAttribute('data-la-data', json_encode([
                'toUrl' => "/owner/invoices/add"
            ]))
            ->setAttribute('data-la-separate', '[]')
            ->setLabel(__t('Pay'));

        $multipleSelectAction->add($btnPrintPdf);
        $multipleSelectAction->add($btnInvoice);

        return [
            $multipleSelectAction
        ];
    }
    public static function generateIframeMultiActionButtons($data = []): array
    {
        $multipleSelectAction = new MultipleSelectAction('Multiple-Select-Action-Iframe');

        $params = $_GET ?? [];
        $btnExport = new Button('export');
        $btnExport->setOption('icon', 'database-export')
            ->setLabel(__t('Export'))
            ->setOption('action', "/v2/owner/entity/export/contract_installment")
            ->setOption('method', 'GET')
            ->setAttributes([
                'data-la-separate' => 'comma',
                'data-la-data' => json_encode([
                   'filters' => $params,
                ]),
            ]);
        $filters = [];
        if(isset($_GET['iframe']) && !empty($_GET['filter']['contract_id'])) {
            $filters['contract_id'] = $_GET['filter']['contract_id'];
        }
        $btnDelete = new Button('Pay');
        $btnDelete->setOption('icon', 'currency-usd')
            ->setLabel(__t('Pay'))
            ->setOption('action', '/v2/owner/entity/get_multi_action_ids/contract_installment')
            ->setOption('method', 'GET')
            ->setAttribute('data-la-data', json_encode([
                'toUrl' => "/owner/invoices/add",
                'filter' => $filters,
            ]));
        $multipleSelectAction
            ->add($btnExport)
            ->add($btnDelete);

        return [
            $multipleSelectAction
        ];
    }
    public static function pageButtonsWithoutData(): array
    {

        return [];
    }
}
