<?php

namespace Izam\Entity\Components\ListingPageHeader\EntityComponents;

use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;
use Izam\Entity\Utils\RestrictedQueryParamsUtil;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\Pagination;
use Laminas\Form\Element\Number;

class DefaultEntityComponent implements IEntityComponent
{
    public static function generateRightPageHeaderButtons(): array
    {
        return [];
    }

    public static function generateCenterPageHeaderButtons(): array
    {
        return [];
    }

    public static function generateLeftPageHeaderButtons(): array
    {
        return [];
    }

    public static function PageHeaderButtons($pagination, $exceptQuery = []): Header
    {
        $pageHeader = new Header('page-content-header');
        array_map([$pageHeader, 'addLeft'], array_merge(static::generateLeftPageHeaderButtons($pagination), static::generatePagination($pagination, $exceptQuery)));
        array_map([$pageHeader, 'addCenter'], array_filter(static::generateCenterPageHeaderButtons()));
        array_map([$pageHeader, 'addRight'], array_filter(static::generateRightPageHeaderButtons()));
        return $pageHeader;
    }

    public static function pageButtonsWithoutData(): array
    {
        return [];
    }

    public static function generatePageModals(): array
    {
        return [];
    }

    public static function generatePagination($pagination, $exceptQuery = []): array
    {
        $queryParams = array_filter($_GET, function ($key) use ($exceptQuery) {
            return !in_array($key, array_merge(RestrictedQueryParamsUtil::getParams(), $exceptQuery));
        }, ARRAY_FILTER_USE_KEY);
        if (empty($queryParams) && $pagination->total() == 0) {
            return [];
        }
        $params = $_GET;
        if (isset($params['page']) || isset($params['url'])) {
            unset($params['page'], $params['url']);
        }
        $params = (!empty(http_build_query($params))) ? '&' . http_build_query($params) : '';
        $pagePagination = new Pagination('Listing-Pagination');

        $paginationPreviousBtn = new Anchor('Pagination-Previous-Btn');
        $paginationPreviousBtn->setLabel(__t('Previous Page'))
            ->setAttributes([
                'id' => 'top-nav-prev',
                'title' => __t('Previous Page'),
                'class' => 'btn btn-secondary',
                'href' => $pagination->previousPageUrl() . $params,
                'data-bs-placement' => 'bottom',
                'data-bs-title' => __t('Previous Page'),
                'data-bs-toggle' => 'tooltip',
            ])
            ->setOptions([
                'icon' => 'chevron-left',
                'theme' => 'listing',
            ]);
        if (!$pagination->previousPageUrl()) {
            $paginationPreviousBtn->setOption('disabled', 'disabled');
        }


        $paginationInputNumber = new Number('Pagination-Page');
        $paginationInputNumber->setAttributes([
            'placeholder' => __t('Page') . ' ' . $pagination->currentPage() . ' ' . __t('of') . ' ' . $pagination->lastPage(),
            'title' => __t('Jump to Page'),
            'data-bs-title' => __t('Jump to Page'),
            'data-bs-toggle' => 'tooltip',
            'data-lj-name' => 'page',
            'min' => 1,
            'max' => $pagination->lastPage(),
            'data-lj-current' => $pagination->currentPage(),
            'data-lj-params' => $params,
        ]);

        $paginationNextBtn = new Anchor('Pagination-Next-Btn');
        $paginationNextBtn->setLabel(__t('Next Page'))
            ->setAttributes([
                'id' => 'top-nav-next',
                'title' => __t('Next Page'),
                'class' => 'btn btn-secondary',
                'href' => $pagination->nextPageUrl() . $params,
                'data-bs-placement' => 'bottom',
                'data-bs-title' => __t('Next Page'),
                'data-bs-toggle' => 'tooltip',
            ])
            ->setOptions([
                'icon' => 'chevron-right',
                'theme' => 'listing',
            ]);
        if (!$pagination->nextPageUrl()) {
            $paginationNextBtn->setOption('disabled', 'disabled');
        }

        $pagePagination
            ->setOptions([
                'theme' =>  array_key_exists('iframe', $_GET) ? 'iframe' : 'listing',
                'firstItem' => $pagination->firstItem() ? $pagination->firstItem() . " - " : 0,
                'lastItem' => $pagination->lastItem(),
                'total' => $pagination->total(),
            ])
            ->add($paginationPreviousBtn)
            ->add($paginationInputNumber)
            ->add($paginationNextBtn);

        return [
            $pagePagination,
        ];
    }

    public static function generateIframeMultiActionButtons($data = []): array
    {
        return [];
    }
}