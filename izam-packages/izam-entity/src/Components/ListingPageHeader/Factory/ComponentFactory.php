<?php

namespace Izam\Entity\Components\ListingPageHeader\Factory;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\AssetComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\BankTransactionComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\BomComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\BrandComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ClientComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ContractComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ContractInstallmentComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\DefaultEntityComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\FinancialYearComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ItemGroupComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\JournalComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\JournalTransactionComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\LeaseContractComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\LeaveApplicationComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ManufacturingOrderComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ManufacturingOrderIndirectCostComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\MultiCycleApprovalConfigComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ProductionPlanComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ProductionRoutingComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\PurchaseDebitNoteComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\PurchaseInvoiceComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\PurchaseRefundComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\RentalPriceRuleComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\RentalReservationOrderComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\RentalSeasonalPriceComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\RentalUnitComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\RentalUnitTypeComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\SalesOrderComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\ServiceFormComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\SmtpEmailAddressComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\StaffComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\StockRequestComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\StocktakingComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\SupplierComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\TreasuryComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\TreasuryTransferComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\VehicleComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\WorkFlowComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\WorkstationComponent;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\EntityDocumentComponent;
use Izam\Entity\Components\ListingPageHeader\Interfaces\IComponentFactory;
use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;

class ComponentFactory implements IComponentFactory
{

    public static function getEntityComponent(string $entityKey): IEntityComponent
    {
        if (str_contains($entityKey,'le_workflow-type'))
            return new WorkFlowComponent($entityKey);

        return match ($entityKey) {
            EntityKeyTypesUtil::CLIENT_ENTITY_KEY => new ClientComponent(),
            EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY => new SupplierComponent(),
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => new StockRequestComponent(),
            EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY => new ProductionRoutingComponent(),
            EntityKeyTypesUtil::CONTRACT => new ContractComponent(),
            EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => new WorkstationComponent(),
            EntityKeyTypesUtil::STAFF_ENTITY_KEY => new StaffComponent(),
            EntityKeyTypesUtil::BOM_ENTITY_KEY => new BomComponent(),
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => new LeaveApplicationComponent(),
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => new ItemGroupComponent(),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => new ManufacturingOrderComponent(),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => new ManufacturingOrderIndirectCostComponent(),
            EntityKeyTypesUtil::SMTP_EMAIL_ADDRESS => new SmtpEmailAddressComponent(),

            EntityKeyTypesUtil::JOURNAL => new JournalComponent(),
            EntityKeyTypesUtil::PURCHASE_INVOICE => new PurchaseInvoiceComponent(),
            EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE => new PurchaseDebitNoteComponent(),
            EntityKeyTypesUtil::PURCHASE_REFUND => new PurchaseRefundComponent(),
            EntityKeyTypesUtil::SALES_ORDER => new SalesOrderComponent(),
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => new MultiCycleApprovalConfigComponent(),
            EntityKeyTypesUtil::BANK_TRANSACTION => new BankTransactionComponent(),

            EntityKeyTypesUtil::TREASURY => new TreasuryComponent(),
            EntityKeyTypesUtil::RENTAL_PRICE_RULE => new RentalPriceRuleComponent(),
            EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER => new RentalReservationOrderComponent(),
            EntityKeyTypesUtil::RENTAL_SEASONAL_PRICE => new RentalSeasonalPriceComponent(),
            EntityKeyTypesUtil::RENTAL_UNIT => new RentalUnitComponent(),
            EntityKeyTypesUtil::RENTAL_UNIT_TYPE => new RentalUnitTypeComponent(),
            EntityKeyTypesUtil::FINANCIAL_YEAR_ENTITY_KEY => new FinancialYearComponent(),
            EntityKeyTypesUtil::TREASURY_TRANSFER => new TreasuryTransferComponent(),
            EntityKeyTypesUtil::JOURNAL_TRANSACTION => new JournalTransactionComponent(),

            EntityKeyTypesUtil::PRODUCTION_PLAN => new ProductionPlanComponent(),
            EntityKeyTypesUtil::ASSET => new AssetComponent(),
            EntityKeyTypesUtil::BRAND => new BrandComponent(),
            EntityKeyTypesUtil::VEHICLE => new VehicleComponent(),

            EntityKeyTypesUtil::STOCKTAKING => new StocktakingComponent(),


            EntityKeyTypesUtil::LEASE_CONTRACT => new LeaseContractComponent(),
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => new ContractInstallmentComponent(),
            EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => new ServiceFormComponent(),
            EntityKeyTypesUtil::ENTITY_DOCUMENT => new EntityDocumentComponent(),

            default => new DefaultEntityComponent(),
        };
    }
}
