<?php

namespace Izam\Entity\ListingMetaData\Formatter;

use Izam\Entity\ListingMetaData\Formatter\Interfaces\IFormatter;

class DropDownFormatter implements IFormatter
{

    public function format($value, $meta, $row = null)
    {
        if (empty($value) && !is_numeric($value)) {
            return '--';
        }
        return ($meta->getAllowedValues() && isset($meta->getAllowedValues()[$value])) ? __t($meta->getAllowedValues()[$value]) : $value;
    }

}
