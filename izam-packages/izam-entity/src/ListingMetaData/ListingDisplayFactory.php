<?php

namespace Izam\Entity\ListingMetaData;

use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;
use Izam\Entity\ListingMetaData\Formatter\AdvancedDropDownFormatter;
use Izam\Entity\ListingMetaData\Formatter\AutoStaffFormatter;
use Izam\Entity\ListingMetaData\Formatter\CurrencyFormatter;
use Izam\Entity\ListingMetaData\Formatter\DateFormatter;
use Izam\Entity\ListingMetaData\Formatter\DateTimeFormatter;
use Izam\Entity\ListingMetaData\Formatter\DynamicDropDownFormatter;
use Izam\Entity\ListingMetaData\Formatter\EmailFormatter;
use Izam\Entity\ListingMetaData\Formatter\FileFormatter;
use Izam\Entity\ListingMetaData\Formatter\FollowUpStatusFormatter;
use Izam\Entity\ListingMetaData\Formatter\MultipleDropDownFormatter;
use Izam\Entity\ListingMetaData\Formatter\MultipleDynamicDropDownFormatter;
use Izam\Entity\ListingMetaData\Formatter\SubFormFormatter;
use Izam\Entity\ListingMetaData\Formatter\TelephoneFormatter;
use Izam\Entity\ListingMetaData\Formatter\TemplateFormatter;
use Izam\Entity\ListingMetaData\Formatter\TextareaFormatter;
use Izam\Entity\ListingMetaData\Formatter\TextFormatter;
use Izam\Entity\ListingMetaData\Formatter\ToggleFormatter;
use Izam\Entity\ListingMetaData\Formatter\UrlFormatter;
use Izam\Entity\ListingMetaData\Formatter\DropDownFormatter;

class ListingDisplayFactory
{
    public static function getFormatter(string $type)
    {
        switch ($type) {
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FILE:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_PHOTO:
                return (new FileFormatter());

            case EntityFieldUtil::ENTITY_FIELD_TYPE_EMAIL:
                return (new EmailFormatter());

            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_PICKER:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_RANGE_PICKER:
                return (new DateFormatter());
            case EntityFieldUtil::ENTITY_FIELD_TYPE_TIMESTAMP:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC:
                return (new DateTimeFormatter());

            case EntityFieldUtil::ENTITY_FIELD_TYPE_CHECKBOX:
                return (new ToggleFormatter());

            case EntityFieldUtil::ENTITY_FIELD_TYPE_URL:
                return (new UrlFormatter());


            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DROPDOWN:
                return (new MultipleDropDownFormatter());

            case EntityFieldUtil::ENTITY_FIELD_TYPE_CURRENCY_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_CURRENCY_FIELD:
                return (new CurrencyFormatter());

            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_BRANCH:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST:
                return (new DynamicDropDownFormatter());
            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS:
                return (new  MultipleDynamicDropDownFormatter());
            case EntityFieldUtil::ENTITY_FIELD_TYPE_PHONE_NUMBER:
                return (new TelephoneFormatter());

            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN_ADVANCED:
                return (new AdvancedDropDownFormatter());
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOLLOW_UP_STATUS:
                return new FollowUpStatusFormatter();
            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF:
                return (new AutoStaffFormatter());
            case EntityFieldUtil::ENTITY_FIELD_TYPE_TEMPLATE:
                return (new TemplateFormatter());
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN:
                return (new DropDownFormatter());
            case EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM:
                return new SubFormFormatter();
            case EntityFieldUtil::ENTITY_FIELD_TYPE_TEXTAREA:
                return new TextAreaFormatter();
            default:
                return new TextFormatter();
        }
    }
}
