<?php

namespace Izam\Entity\ListingMetaData;

use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;

class ListingMetaDataFactory
{
    public static function getMetaObject($type)
    {
        switch ($type) {
            case EntityFieldUtil::ENTITY_FIELD_TYPE_PHOTO:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FILE:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE:
                return new FileMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN:
                return new DropDownMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOLLOW_UP_STATUS:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN_ADVANCED:
                return new DynamicDropDownMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_CURRENCY_DROPDOWN:
                return new CurrencyMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_QUANTITY:
                return new QuantityMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_ASSIGNED_STAFF:
                return new MultipleDynamicDropDownMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_STATIC_CONTENT:
                return new StaticContentMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE:
                return new DateMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM:
                return new SubFormMeta();

            default:
                return new DefaultMeta();
        }
    }
}
