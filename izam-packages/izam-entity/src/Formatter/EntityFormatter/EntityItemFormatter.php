<?php

namespace Izam\Entity\Formatter\EntityFormatter;

use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;

use Izam\Entity\Formatter\Strategy\AdvancedDynamicViewFormatter;
use Izam\Entity\Formatter\Strategy\AssignedStaffViewFormatter;
use Izam\Entity\Formatter\Strategy\AutoStaffViewFormatter;
use Izam\Entity\Formatter\Strategy\DateFormatter;
use Izam\Entity\Formatter\Strategy\DateTimeFormatter;
use Izam\Entity\Formatter\Strategy\DefaultFormatter;
use Izam\Entity\Formatter\Strategy\DropDownViewFormatter;
use Izam\Entity\Formatter\Strategy\DynamicViewFormatter;
use Izam\Entity\Formatter\Strategy\FollowUpStatusViewFormatter;
use Izam\Entity\Formatter\Strategy\HasOneFormatter;
use Izam\Entity\Formatter\Strategy\MapFormatter;
use Izam\Entity\Formatter\Strategy\MultipleDropdownFormatter;
use Izam\Entity\Formatter\Strategy\MultipleDynamicViewFormatter;
use Izam\Entity\Formatter\Strategy\MultipleFileFormatter;
use Izam\Entity\Formatter\Strategy\QuantityFormatter;
use Izam\Entity\Formatter\Strategy\StaticFormatter;
use Izam\Entity\Formatter\Strategy\SubFormFormatter;
use Izam\Entity\Formatter\Strategy\TimeFormatter;
use Izam\Entity\Formatter\Strategy\UnitTextFormatter;
use Izam\Entity\Formatter\Strategy\FileFormatter;
use Izam\Entity\Formatter\Strategy\CurrencyFormatter;

class EntityItemFormatter implements IEntityItemFormatter
{
    public function format($item, $metaItems)
    {
        if (!$item) {
            return ;
        }

        $obj = get_class($item) == AppEntityData::class ? $item : new \stdClass();
        foreach ($metaItems as $meta) {
            if ($meta->getHasOneName()) {
                if (!isset($obj->{$meta->getHasOneName()})) {
                    $obj->{$meta->getHasOneName()} = new \stdClass();
                }
                $this->getFactory($meta->getType())->format($item->{$meta->getHasOneName()}, $meta, $obj->{$meta->getHasOneName()});
            } else {
                $this->getFactory($meta->getType())->format($item, $meta, $obj);
            }
        }
        return $obj;
    }


    protected function getFactory($type)
    {
        $data = [
            EntityFieldUtil::ENTITY_FIELD_TYPE_CURRENCY_DROPDOWN => (new CurrencyFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN => (new MultipleDynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_ASSIGNED_STAFF => (new AssignedStaffViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_FOLLOW_UP_STATUS => (new FollowUpStatusViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS => (new MultipleDynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN =>  (new DynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST =>  (new DynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY =>  (new DynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN_ADVANCED =>  (new AdvancedDynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN => new DropDownViewFormatter($this),
            EntityFieldUtil::ENTITY_FIELD_TYPE_STATIC_CONTENT =>  (new StaticFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DATE =>  (new DateFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_TIMESTAMP =>  (new DateFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_TIME =>  (new TimeFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MAP =>(new MapFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DROPDOWN => (new MultipleDropdownFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_FILE =>  (new FileFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE =>  (new MultipleFileFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_PHOTO =>  (new FileFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM => (new SubFormFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_QUANTITY => (new QuantityFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_UNIT_TEXT => (new UnitTextFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF => (new AutoStaffViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER => (new DateTimeFormatter($this)),
            'hasOne' => (new HasOneFormatter($this)),

        ];
        if (!isset($data[$type])) {
            return new DefaultFormatter($this);
        } else {
            return $data[$type];
        }
    }

    public function accessData($record, $key)
    {
        $keys = explode('.', $key);
        foreach ($keys as $index => $key) {
            $record =  $record->{$key}??'';
        }
        return $record;
    }

    public function accessDataArray($record, $key, $value)
    {
        $tempKey = $keys = explode('.', $key);
        unset($tempKey[0]);
        $tempKey = implode('.', $tempKey);

        $tempValue = $values = explode('.', $value);
        unset($tempValue[0]);
        $tempValue = implode('.', $tempValue);

        $retKeys = [];

        foreach ($record->{$keys[0]} as $datum) {
            $retKeys[] = $this->accessData($datum, $tempKey);
        }
        $retValues = [];
        foreach ($record->{$values[0]} as $datum) {
            $retValues[] = $this->accessData($datum, $tempValue);
        }
        return array_combine($retKeys, $retValues);
    }
}
