<?php

namespace Izam\Entity\Listing\Filters\Entity;

use App\Facades\Permissions;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\JoinCriteria;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\QueryCriteria;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\SelectCriteria;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\SortCriteria;
use App\Modules\LocalEntity\Prototype\DataMeta;
use App\Modules\LocalEntity\Prototype\DataStructure\AndClause;
use App\Modules\LocalEntity\Prototype\DataStructure\OrClause;
use App\Modules\LocalEntity\Prototype\Filters\ConditionMeta;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Modules\LocalEntity\Prototype\Filters\RawFilter;
use App\Services\EntityDocumentService;
use Carbon\Carbon;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Database\Capsule\Manager as DB;
use Izam\Daftra\Common\EntityStructure\IEntity;
use Izam\Daftra\Common\Utils\ContractInstallmentUtil;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;
use Izam\Entity\Listing\Filters\FilterInterface;

class EntityDocumentListingFilter implements FilterInterface
{
    public function __construct() {
    }

    public function process(IEntity $entity, QueryCriteria $criteria, DataMeta $dataMeta): void
    {

        $requestData = ($dataMeta->getRequestAllData());
        $entity_id = $requestData['filter']['entity_id'] ?? null;
        $onCondition = 'entity_document_types.id = entity_documents.entity_document_type_id';
        if($entity_id){
            $onCondition = $onCondition. ' and entity_documents.entity_id = '.$entity_id;
        }

        $criteria->addJoin(new JoinCriteria("right", "entity_document_types", [new FilterMeta(null ,'raw', $onCondition)] ));

        $criteria->addSelect(new SelectCriteria('entity_document_types.id','document_types_id'));
        $criteria->addSelect(new SelectCriteria('entity_document_types.name','document_types_name'));
        $criteria->addSelect(new SelectCriteria(\DB::raw('IF(entity_documents.id IS NULL, 0, 1)'), 'doc_id'));
        $criteria->addSelect(new SelectCriteria(\DB::raw('IF(entity_documents.created IS NULL, 0, 1)'), 'created_at'));
        $criteria->addSelect(new SelectCriteria(\DB::raw('IF(entity_documents.expiry_date IS NULL, 0, 1)'), 'expire_at'));
        $this->handleFilters($criteria);
        $this->handelListingSort($criteria);

    }

    private function handelListingSort(QueryCriteria $criteria){

        $sorts =  $criteria->getSorts();
        $criteria->clearSort();
        $criteria->addSort(new SortCriteria('doc_id','asc'));
        $criteria->addSort(new SortCriteria('entity_document_types.display_order','asc'));

        foreach($sorts as $sort){
            if($sort->getField() == "entity_documents.expiry_date"){
                $criteria->addSort(new SortCriteria('expire_at','asc'));
                $criteria->addSort(new SortCriteria('entity_documents.expiry_date', $sort->getDirection()));
            }

            if($sort->getField() == "entity_documents.created"){
                $criteria->addSort(new SortCriteria('created_at','asc'));
                $criteria->addSort(new SortCriteria('entity_documents.created', $sort->getDirection()));
            }

        }

    }

    public function handleFilters($criteria){
        $showMissings = true;
        $filters = request()->get('filter');
        
        if (!empty($filters['name']['like'])) {
            $showMissings = false;
        }

        if (!empty($filters['staff_id'])) {
            $showMissings = false;
        }

        if (!empty($filters['created']['lte'])) {
            $showMissings = false;
        }

        if (!empty($filters['expiry_date']['lte'])) {
            $showMissings = false;
        }
        if (!empty($filters['get_unique'])){
            $staffId = request('filter.entity_id');
            /** @var EntityDocumentService $service */
            $service = resolve(EntityDocumentService::class);
            $latest = $service->getDocumentLatestIds(EntityKeyTypesUtil::STAFF_ENTITY_KEY , $staffId);
            $criteria->addWhere(new FilterMeta('entity_documents.id', 'in', $latest));
        }
        if (!empty($filters['status'])) {
            $value = $filters['status'];
            $criteria->removeWhere('status');
            switch ($value) {
                case EntityDocumentStatusUtil::VALID:
                    $showMissings = false;
                    $today = Carbon::today()->toDateString();
                    $after30Days = Carbon::today()->addDays(30)->toDateString();
                    $rawQuery = "((DATE(`entity_documents`.`expiry_date`) > '{$today}' AND DATE(`entity_documents`.`expiry_date`) > '{$after30Days}' And entity_document_types.is_expirable = 1) OR entity_document_types.is_expirable = 0 )";
                    $criteria->addWhere(new FilterMeta(null, 'raw', $rawQuery));
                    break;
                case EntityDocumentStatusUtil::EXPIRED:
                    $showMissings = false;
                    $rawQuery = "((DATE(`entity_documents`.`expiry_date`) < '" . Carbon::today()->toDateString() . "' OR entity_documents.expiry_date Is NULL) And entity_document_types.is_expirable = 1)";
                    $criteria->addWhere(new FilterMeta(null, 'raw', $rawQuery));
                    break;
                case EntityDocumentStatusUtil::EXPIRING_SOON:
                    $showMissings = false;
                    $today = Carbon::today()->toDateString();
                    $after30Days = Carbon::today()->addDays(30)->toDateString();

                    $rawQuery = "DATE(`entity_documents`.`expiry_date`) >= '{$today}' AND DATE(`entity_documents`.`expiry_date`) <= '{$after30Days}'";
                    $criteria->addWhere(new FilterMeta(null, 'raw', $rawQuery));
                    break;
                case EntityDocumentStatusUtil::MISSING:
                    $rawQuery = "entity_documents.id  is null";
                    $criteria->addWhere(new FilterMeta(null, 'raw', $rawQuery));
                    break;

            }
        }
        $defaultFilter = new AndClause();
        foreach($criteria->getWheres() as $filter){
            $defaultFilter->pushChildren($filter);
        }
        if (!empty($filters['entity_document_type_id'])) {
            $missingOrClause = new OrClause();
            $value = (int) $filters['entity_document_type_id'];
            if( $showMissings ){
                $missingOrClause->pushChildren($defaultFilter);
                $missingOrClause->pushChildren(
                    new RawFilter("(entity_document_types.is_required  = 1 and entity_documents.id  is null  and  entity_document_types.id = {$value})")
                );
                $criteria->setWheres([$missingOrClause]);
                $showMissings = false;

            }else{
                $criteria->addWhere(new RawFilter("entity_document_types.id = {$value}"));
            }
        }
        if($showMissings){
            $missingOrClause = new OrClause();
            $missingOrClause->pushChildren($defaultFilter);
            $missingOrClause->pushChildren(new RawFilter("( entity_document_types.is_required  = 1 and entity_documents.id  is null )"));
            $criteria->setWheres([$missingOrClause]);
        }

    }
}