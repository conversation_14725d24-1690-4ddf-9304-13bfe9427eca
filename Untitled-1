(function ($) {
  var pluginName = "subform";

  function SubformPlugin(element, options) {
    var $el = $(element);
    var dragger;
    options = $.extend({}, $.fn[pluginName].defaults, options);
    var template,
      addBtn = $(options["addBtn"]),
      dragBtn = options["dragBtn"],
      removeBtn = options["removeBtn"],
      onAddRow = options["onAddRow"],
      afterAddRow = options["afterAddRow"],
      onRemoveRow = options["onRemoveRow"],
      afterRemoveRow = options["afterRemoveRow"],
      lastIndex = options["lastIndex"],
      minimumRows = options["minimumRows"],
      maximumRows = options["maximumRows"],
      rowNo = options["rowNo"];
      validation = options["validation"];


    if ($el.find(options["template"]).length) {
      template = $el.find(options["template"]).html();
    } else {
      template = $(options["template"]).html()
    }

    var $addBtn = null;
    if ($el.find(addBtn).length) {
      $addBtn = $el.find(addBtn);
    } else {
      $addBtn = $(addBtn)
    }

    var $validation = null;
    if ($el.find(validation).length) {
      $validation = $el.find(validation);
    }

    if (lastIndex === 0) {
      lastIndex = $el.find('tbody').children().length;
    }

    var $form = $el.closest('form');

    function init() {
      refreshRowNumbers();
      bindEvents();
    }

    function bindEvents() {
      $el.on("click", removeBtn, function (e) {
        e.preventDefault();
        removeRow(this);
      });

      $addBtn.on("click", function (e) {
        e.preventDefault();
        addRow();
      });

      if (dragBtn) {
        enableDraggable();
      }

    }

    function refreshRowNumbers() {
      var $rowNo = $el.find(rowNo);
      if ($rowNo.length) {
        if ($rowNo.is(':input')) {
          $rowNo.val(function (i) {
            return i + 1;
          });
        } else {
          $rowNo.text(function (i) {
            return i + 1;
          });
        }
      }
    }

    function enableDraggable() {
      var el = $el.find("table")[0];
      dragger = tableDragger.default(el, {
        mode: "row",
        onlyBody: true,
        dragHandler: dragBtn,
      });

      dragger.on('drop', function (from, to) {
        refreshRowNumbers();
      });

    }

    function disableDraggable() {
      if (typeof dragger != 'undefined' && dragger && dragger.destroy) {
        dragger.destroy();
      }
    }

    function updateInputValue($input, value) {
      switch ($input.get(0).nodeName) {
        case 'INPUT':
        case 'TEXTAREA':
          if ($input.attr('type') === 'checkbox' || $input.attr('type') === 'radio') {
            var key = $input.data('subform-name');
            $input = $input.closest('tr').find(`input[data-subform-name="${key}"][value="${value}"]`);
            $input.prop('checked', true);
          } else {
            $input.val(value);
          }
          break;

        case 'SELECT':
          // Check if this is a Selectize-enabled dropdown
          if ($input[0].selectize) {
            // For Selectize dropdowns, use the Selectize API
            var selectizeInstance = $input[0].selectize;

            // Check if the option exists in the selectize options
            if (selectizeInstance.options[value]) {
              selectizeInstance.setValue(value, false);
            } else {
              // For dynamic dropdowns, we need to add the option first
              // Get the text from existing option or use value as fallback
              var existingOption = $input.find(`option[value="${value}"]`);
              var optionText = existingOption.length > 0 ? existingOption.text() : value;

              // Add the option to selectize
              selectizeInstance.addOption({
                id: value,
                text: optionText,
                value: value
              });
              selectizeInstance.setValue(value, false);
            }
          } else if ($input.attr('data-app-form-select') === 'true') {
            // For dropdowns that will be initialized with Selectize later
            // Store the value to be set after initialization
            $input.attr('data-selected-value', value);

            // Also try to set it on the original select element
            if ($input.find(`option[value="${value}"]`).length === 0) {
              // Get option text from data attributes if available
              var optionText = value;
              var optionData = $input.find(`option[value="${value}"]`).attr('data-data');
              if (optionData) {
                try {
                  var parsedData = JSON.parse(optionData);
                  optionText = parsedData.text || value;
                } catch (e) {
                  optionText = value;
                }
              }
              $input.append(`<option value="${value}" selected data-subform-added="true">${optionText}</option>`);
            }
            $input.val(value);

            // Set up a more robust listener to set the value after Selectize initialization
            var attempts = 0;
            var maxAttempts = 10;
            var checkInterval = 200;

            function trySetSelectizeValue() {
              attempts++;
              if ($input[0].selectize && $input.attr('data-selected-value')) {
                var storedValue = $input.attr('data-selected-value');
                var selectizeInstance = $input[0].selectize;

                if (!selectizeInstance.options[storedValue]) {
                  var existingOption = $input.find(`option[value="${storedValue}"]`);
                  var optionText = existingOption.length > 0 ? existingOption.text() : storedValue;

                  selectizeInstance.addOption({
                    id: storedValue,
                    text: optionText,
                    value: storedValue
                  });
                }
                selectizeInstance.setValue(storedValue, false);
                $input.removeAttr('data-selected-value');
              } else if (attempts < maxAttempts) {
                setTimeout(trySetSelectizeValue, checkInterval);
              }
            }

            setTimeout(trySetSelectizeValue, checkInterval);
          } else {
            // For regular dropdowns, use the original logic
            if ($input.find(`option[value="${value}"]`).length === 0) {
              $input.append(`<option value="${value}" selected>${value}</option>`);
            }
            $input.val(value);
          }
          break;
      
        default:
          break;
      }
    }

    function updateRowData($row, data) {
      var $inputs = $row.find(`[data-subform-name]`);
      $inputs.each(function () {
        var $input = $(this);
        var key = $input.data('subform-name');
        var chunks = key.split('.');
        var value = '';
        if (chunks.length === 1) {
          value = data[key];
        } else {
          value = chunks.reduce(function (accumulator, chunk, index) {
            return accumulator[chunk];
          }, data);
        }
        updateInputValue($input, value);
      });
    }

    function addRow(data) {
      return new Promise (function (res, rej) {
        if (maximumRows != -1 && $el.find("tbody").children().length >= maximumRows) {
          rej(new Error('Error adding row, maybe settings has maximumRows set'));
          return;
        }
        lastIndex++;
        var newTemplate = template.replace(/__index__/g, lastIndex.toString())
        newTemplate = newTemplate.replace(/__row_no__/g, $el.find("tbody").children().length + 1);
        var $row = $(newTemplate);
        if (data) {
          updateRowData($row, data);
        }
        var addRow = onAddRow($row, lastIndex);
        if (addRow) {
          if ($validation !== null) {
            $validation.html('');
          }
          $row = $row.appendTo($el.find('table').find("tbody"));
          refreshRowNumbers();
          $el.trigger('subform:add', [$row, lastIndex]);
          if (dragBtn) {
            disableDraggable();
            enableDraggable();
          }
          if (typeof $.fn.refreshValidation !== 'undefined' && $form) {
            $form.refreshValidation();
          }
          afterAddRow($row, lastIndex);
          res($row);
        }
      });
    }

    function addRows(countOrData) {
      if (typeof countOrData === 'number' || typeof countOrData === 'string') {
        for (var index = 0; index < countOrData; index++) {
          addRow();
        }
      } else if (Array.isArray(countOrData)) {
        for (var index = 0; index < countOrData.length; index++) {
          addRow(countOrData[index]);
        }
      }
    }

    function getRowByEl(btn) {
        return $(btn).closest("tr");
    }

    function getRowByIndex(index) {
        return $el.find('tbody').find("tr")[index];
    }

    function getLastRow() {
        var index = $el.find('tbody').find("tr").length - 1;
        if (index > -1) {
          return $el.find('tbody').find("tr")[index];
        }
        return null;
    }

    function removeRow(indexOrEl) {
      return new Promise (function (res, rej) {
        if (
          minimumRows <= 0 ||
          (minimumRows > 0 && $el.find("tbody").children().length > minimumRows)
        ) {
          var $row = {};
          if (typeof indexOrEl == 'string' || typeof indexOrEl === 'number') {
            $row = getRowByIndex(indexOrEl);
          } else if (typeof indexOrEl == 'object')  {
            $row = getRowByEl(indexOrEl);
          } else {
            $row = getLastRow();
          }
          if ($row) {
            var removeRow = onRemoveRow($row, lastIndex);
            if (removeRow) {
              $row.remove();
              refreshRowNumbers();
              $el.trigger('subform:remove', [$row, lastIndex]);
              if (typeof $.fn.refreshValidation !== 'undefined' && $form) {
                $form.refreshValidation();
              }
              afterRemoveRow($row, lastIndex);
              res($row);
            }
          } else {
            rej(new Error('Error deleting row, table is empty'));
          }
        } else {
          rej(new Error('Error deleting row, maybe settings has minimumRows set'));
        }
      });
    }


    init();

    return {
      $el,
      addRow,
      addRows,
      removeRow,
      onAddRow,
      afterAddRow,
      onRemoveRow,
      afterRemoveRow,
      lastIndex,
      minimumRows,
      maximumRows
    }
  }

  $.fn[pluginName] = function (options) {
    if ($(this).data(pluginName) && $(this).data(pluginName) != true) {
      return $(this).data(pluginName);
    }

    if (typeof arguments[0] === "string") {
      var methodName = arguments[0];
      var args = Array.prototype.slice.call(arguments, 1);
      var returnVal;
      this.each(function () {
        if (
          $.data(this, pluginName) &&
          typeof $.data(this, pluginName)[methodName] === "function"
        ) {
          returnVal = $.data(this, pluginName)[methodName].apply(
            this,
            args
          );
        } else {
          throw new Error(
            "Method " + methodName + " does not exist on jQuery." + pluginName
          );
        }
      });
      if (returnVal !== undefined) {
        return returnVal;
      } else {
        return this;
      }
    } else if (typeof options === "object" || !options) {
      var instances = [];
      this.each(function () {
        if (!$.data(this, pluginName) || ($.data(this, pluginName) == true)) {
          var instance = new SubformPlugin(this, options);
          $.data(
            this,
            pluginName,
            instance
          );
          instances.push(instance);
        }
      });
      if (instances.length == 1) {
        return instances[0];
      } else {
        return instances;
      }
      return
    }
  };

  $.fn[pluginName].defaults = {
    addBtn: "[data-row-add]",
    dragBtn: "[data-cell-drag]",
    removeBtn: "[data-cell-remove]",
    template: "[data-subform-template]",
    validation: "[data-subform-validation]",
    rowNo: "[data-row-no]",
    onAddRow: function () {
      return true;
    },
    afterAddRow: function () {
    },
    onRemoveRow: function () {
      return true;
    },
    afterRemoveRow: function () {
      return true;
    },
    lastIndex: 0,
    minimumRows: 0,
    maximumRows: -1
  };

  // Helper function to handle pending selectize values
  function handlePendingSelectizeValues($container) {
    // Handle elements with data-selected-value attribute
    $container.find('select[data-selected-value][data-app-form-select="true"]').each(function() {
      var $input = $(this);
      var storedValue = $input.attr('data-selected-value');

      if ($input[0].selectize && storedValue) {
        var selectizeInstance = $input[0].selectize;

        if (!selectizeInstance.options[storedValue]) {
          var existingOption = $input.find(`option[value="${storedValue}"]`);
          var optionText = existingOption.length > 0 ? existingOption.text() : storedValue;

          selectizeInstance.addOption({
            id: storedValue,
            text: optionText,
            value: storedValue
          });
        }
        selectizeInstance.setValue(storedValue, false);
        $input.removeAttr('data-selected-value');
      }
    });

    // Also handle selectize dropdowns that have a value attribute but haven't been set
    $container.find('select.selectized[data-app-form-select="true"]').each(function() {
      var $input = $(this);
      var htmlValue = $input.attr('value');

      if ($input[0].selectize && htmlValue && !$input[0].selectize.getValue()) {
        var selectizeInstance = $input[0].selectize;

        // Check if this is an AJAX dropdown (has load function and ajax_helper_text plugin)
        var isAjaxDropdown = typeof selectizeInstance.settings.load === 'function' &&
                           selectizeInstance.plugins &&
                           selectizeInstance.plugins.ajax_helper_text;

        if (isAjaxDropdown) {
          // For AJAX dropdowns, we need to add the option directly since
          // the load function won't be called for existing values
          var optionText = htmlValue; // Default to value as text

          // Try to get better text from existing option
          var existingOption = $input.find(`option[value="${htmlValue}"]`);
          if (existingOption.length > 0 && existingOption.text()) {
            optionText = existingOption.text();
          }

          // Add the option directly to selectize
          selectizeInstance.addOption({
            id: htmlValue,
            text: optionText,
            value: htmlValue
          });

          // Set the value
          selectizeInstance.setValue(htmlValue, false);

          console.log('Set AJAX dropdown value:', htmlValue, 'with text:', optionText);
        } else {
          // For regular dropdowns, use the original logic
          if (!selectizeInstance.options[htmlValue]) {
            var existingOption = $input.find(`option[value="${htmlValue}"]`);
            var optionText = existingOption.length > 0 ? existingOption.text() : htmlValue;

            selectizeInstance.addOption({
              id: htmlValue,
              text: optionText,
              value: htmlValue
            });
          }

          selectizeInstance.setValue(htmlValue, false);
        }
      }
    });
  }

  // Listen for subform events to handle selectize initialization
  $(document).on('subform:add', function(event, $row) {
    // When a new row is added, check for pending selectize values
    setTimeout(function() {
      handlePendingSelectizeValues($row);
    }, 300);
  });

  // Also listen for when UI components are initialized
  $(document).on('DOMContentLoaded', function() {
    setTimeout(function() {
      handlePendingSelectizeValues($(document));
    }, 1000);
  });

  // Handle existing subforms when jQuery is ready
  $(document).ready(function() {
    // Give some time for Selectize to initialize, then check for pending values
    setTimeout(function() {
      handlePendingSelectizeValues($(document));
    }, 1500);

    // Also set up a periodic check for the first few seconds after page load
    var checkCount = 0;
    var maxChecks = 5;
    var checkInterval = setInterval(function() {
      checkCount++;
      handlePendingSelectizeValues($(document));

      if (checkCount >= maxChecks) {
        clearInterval(checkInterval);
      }
    }, 1000);
  });

  // Expose a global function to fix selectize values manually
  window.fixSubformSelectizeValues = function($container) {
    $container = $container || $(document);
    handlePendingSelectizeValues($container);
  };

  // Expose a specific function to fix AJAX selectize dropdowns
  window.fixAjaxSelectizeValue = function(selector, value, text) {
    var $input = $(selector);
    if ($input.length && $input[0].selectize) {
      var selectizeInstance = $input[0].selectize;

      // Add the option
      selectizeInstance.addOption({
        id: value,
        text: text || value,
        value: value
      });

      // Set the value
      selectizeInstance.setValue(value, false);

      console.log('Manually set selectize value:', value, 'for', selector);
      return true;
    }
    return false;
  };

  (function () {
    $("[data-subform]").subform();
  })();
})(jQuery);
