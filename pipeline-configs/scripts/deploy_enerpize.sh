#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status

# Define the EFS file system ID, mount point, and region
EFS_ID="fs-0f82d874026633641"
MOUNT_POINT="/enerpize_efs"
REGION="eu-west-1"
SECRET_ENV=$2
NEW_IMAGE=$1
echo "Deploying image: $NEW_IMAGE"

# Check if NFS utilities are installed
if ! command -v mount.nfs4 &> /dev/null; then
    echo "NFS utilities not found. Installing..."
    sudo apt-get update
    sudo apt-get install -y nfs-common
fi

# Check if the EFS is already mounted
if mountpoint -q "$MOUNT_POINT"; then
    echo "EFS is already mounted at $MOUNT_POINT."
else
    echo "EFS is not mounted. Mounting now..."
    # Create the mount point directory if it doesn't exist
    sudo mkdir -p "$MOUNT_POINT"
    
    # Mount the EFS file system
    sudo mount -t nfs4 -o nfsvers=4.1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport "$EFS_ID.efs.$REGION.amazonaws.com:/" "$MOUNT_POINT"
    # Check if the mount was successful
    if mountpoint -q "$MOUNT_POINT"; then
        echo "EFS mounted successfully at $MOUNT_POINT."
    else
        echo "Failed to mount EFS."
    fi
fi



cd /home/<USER>/izam-daftra

echo "Building env file for laravel"
bash pipeline-configs/scripts/construct_env_file_laravel_enerpize.sh $SECRET_ENV
echo "Building env file for cake"
bash pipeline-configs/scripts/construct_env_file_cake_enerpize.sh $SECRET_ENV
echo "Building configure.php file"
bash pipeline-configs/scripts/construct_php_config_enerpize.sh $SECRET_ENV
echo "Building oauth keys"
bash pipeline-configs/scripts/construct_oauth_keys_enerpize.sh
echo "Downloading ssl certs from s3"
bash pipeline-configs/scripts/get_ssl_dir_enerpize.sh

sudo chmod -R 777 /tmp
sudo mkdir -p /var/www/html/daftra/cake/webroot/rollbar_logs/
sudo chmod -R 777 /var/www/html/daftra/cake/webroot/rollbar_logs/
sudo mkdir -p /var/www/html/daftra/laravel/storage/logs/
sudo chmod -R 777 /var/www/html/daftra/laravel/storage/logs

sudo chmod 777 /var/log/mail.log

# Replace the current image with the new image in docker-compose-enerpize.yaml
sed -i "s|image: IMAGE_NAME|image: $NEW_IMAGE|g" docker-compose-enerpize.yaml

# Login to ECR and deploy
aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 651984943872.dkr.ecr.us-east-2.amazonaws.com 

sed -i "s|image: IMAGE_NAME|image: $NEW_IMAGE|g" docker-compose.prod.yaml
docker compose -f docker-compose-enerpize.yaml -f docker-compose.prod.yaml up -d --build
sed -i "s|image: $NEW_IMAGE|image: IMAGE_NAME|g" docker-compose.prod.yaml
docker exec daftra-system bash -c "chmod +x /app/pipeline-configs/scripts/init-composer.sh && /app/pipeline-configs/scripts/init-composer.sh"

sed -i "s|image: $NEW_IMAGE|image: IMAGE_NAME|g" docker-compose-enerpize.yaml

sudo chmod 755 /var/lib/docker
sudo chmod -R 755  /var/lib/docker/volumes/