#!/bin/bash

set -e

# Function to check if files exist
function checkFileExists {
    if [ ! -f "$1" ]; then
        echo "File $1 does not exist."
        exit 1
    else
        echo "File $1 exists."
    fi
}

# Check the existence of required files
checkFileExists "/var/www/html/daftra/cake/.env"
checkFileExists "/var/www/html/daftra/laravel/.env"
checkFileExists "/var/www/html/daftra/configure.php"

function exportBoolean {
    if [ "${!1}" = "**Boolean**" ]; then
        export ${1}=''
    else 
        export ${1}='Yes.'
    fi
}

exportBoolean LOG_STDOUT
exportBoolean LOG_STDERR

mkdir -p /var/www/html/daftra/laravel/storage/logs
mkdir -p /var/www/html/daftra/cake/webroot/files/images/tmp-logos
mkdir -p /var/www/html/daftra/cake/webroot/files/images/site-logos

chmod 777 /var/lib/php/sessions/
chmod -R 777 /tmp
chmod 777 /var/www/html/daftra/cake/webroot/rollbar_logs
chown www-data:www-data /var/www/html/daftra/laravel/storage/logs
chmod 755 /var/www/html/daftra/laravel/storage/logs
chmod 775 "/var/www/html/daftra/laravel/storage/firebase-service-acc-key.p12"
chown www-data:www-data "/var/www/html/daftra/laravel/storage/firebase-service-acc-key.p12"
chown -R www-data:www-data "/var/www/html/daftra/cake/webroot/files/images"


echo "run services"
 

echo "export SERVER_ID=$(php -r "include '/var/www/html/daftra/configure.php'; echo CURRENT_SERVER_NAME;")" >> /etc/apache2/envvars

# ssh
#/etc/init.d/ssh start
echo "//////////////////////  Check Apache Config //////////////////////"
apache2ctl configtest
echo "//////////////////////  start apache2 service //////////////////////"
/etc/init.d/apache2 start
echo "//////////////////////  start php8.0-fpm service //////////////////////"
/etc/init.d/php8.0-fpm start
echo "//////////////////////  start php5.6-fpm service //////////////////////"
/etc/init.d/php5.6-fpm start
echo "//////////////////////  start sendmail service //////////////////////"
/etc/init.d/sendmail start
echo "//////////////////////  start cron service //////////////////////"
/etc/init.d/cron start
echo "//////////////////////  start rsyslog service //////////////////////"
# /etc/init.d/rsyslog start

echo "//////////////////////  run send_mail_to_smtp //////////////////////"
sed -i "s|%RATE_LIMIT_REDIS%|${REDIS_SERVER}|g" /opt/LuaHookLog.lua
sed -i "s|%RATE_LIMIT_REDIS%|${REDIS_SERVER}|g" /opt/LuaQuickHandler.lua
bash /usr/sbin/send_mail_to_smtp.sh

php /var/www/html/daftra/laravel/artisan package:discover --ansi

echo "//////////////////////  enable opcache //////////////////////"
phpdismod opcache

echo "//////////////////////  install zatca //////////////////////"
cd /var/www/html/daftra/laravel/app/Modules/KSAElectronicInvoice/zatca-einvoicing-sdk-230-R3.1.6
pwd
source install.sh
fatoora
cp ~/.bash-profile /var/www/

echo "//////////////////////  get translations file from db //////////////////////"

curl --insecure  --resolve translate.daftra.me:443:127.0.0.1 "https://translate.daftra.me/language_messages/process_language?token=7c4a8d09ca3762af61e59520943dc26494f8941b"


echo "//////////////////////  run php5.6 /php.php //////////////////////"
php5.6 /php.php

echo "//////////////////////  End script //////////////////////////////////////////"
