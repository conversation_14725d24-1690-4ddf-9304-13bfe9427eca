<VirtualHost *:443>
    DocumentRoot "/var/www/html/daftra/izam-daftra/myredis/"
    ServerName myredis.daftra.me
    LogLevel info
    ErrorLog /var/logs/daftra/myredis-error.log
    LogFormat "%v %{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" combined
    ##SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
    CustomLog "/var/logs/daftra/myredis-access.log" combined
    <Directory /var/www/html/daftra/izam-daftra/myredis>
            Options Indexes FollowSymLinks MultiViews
            AllowOverride All
    </Directory>
 SSLEngine on
    SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
    SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>
</VirtualHost>
## Daftra Translate
##################################################################
<VirtualHost *:80>
    ServerName webstatus.daftara.me
    ServerAlias webstatus.daftra.me
        DocumentRoot /var/www/html/daftra/
    <Directory /var/www/html/daftra/>
        Options -Indexes
        AllowOverride all
    </Directory>
</VirtualHost>
<VirtualHost *:80>
    DocumentRoot "/var/www/html/daftra/daftra-translate/"
    ServerAlias testing-translate.daftra.me
    ServerAlias staging-translate.daftra.me
    <Directory /var/www/html/daftra/daftra-translate/>
            AllowOverride All
            Require all granted
            <FilesMatch \.php$>
                SetHandler "proxy:unix:/run/php/php5.6-fpm.sock|fcgi://localhost/"
            </FilesMatch>
    </Directory>
</VirtualHost>

<VirtualHost *:443>
    DocumentRoot "/var/www/html/daftra/daftra-translate/"
    ServerAlias translate.daftra.me
    ServerAlias translate.daftara.me
ServerAlias staging-translate.daftra.me
    LogLevel info
    ErrorLog /var/logs/daftra/daftara-translate-error.log
    ##SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
    CustomLog "/var/logs/daftra/daftara-transalte-access.log" combined
    <Directory /var/www/html/daftra/daftra-translate/>
            Options Indexes FollowSymLinks MultiViews
            AllowOverride All
            <FilesMatch \.php$>
                SetHandler "proxy:unix:/run/php/php5.6-fpm.sock|fcgi://localhost/"
            </FilesMatch>
    </Directory>
 SSLEngine on
    SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
    SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>
</VirtualHost>
##################################################################

#CDN App
##################################################################
<VirtualHost *:80>
    DocumentRoot "/var/www/html/daftra/cdn/app/"
    ServerName cdn.daftara.me
    ServerAlias cdn.daftra.me
    Header set Access-Control-Allow-Origin "*"
    <Directory /var/www/html/daftra/cdn/>
            AllowOverride All
            Require all granted
    </Directory>
</VirtualHost>
<VirtualHost *:443>
    DocumentRoot "/var/www/html/daftra/cdn/app/"
    ServerName webstatus.daftara.me
    ServerAlias webstatus.daftra.me
    LogLevel info
    ErrorLog /var/logs/daftra/daftara-cdn-error.log

    #SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
    CustomLog "/var/logs/daftra/daftara-cdn-access.log" combined
    Header set Access-Control-Allow-Origin "*"
    <Directory /var/www/html/daftra/cdn/>
            Options Indexes FollowSymLinks MultiViews
            AllowOverride All
    </Directory>
    SSLEngine on
    SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
    SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>
</VirtualHost>

<VirtualHost *:443>
    DocumentRoot "/var/www/html/daftra/cdn/app/"
    ServerName cdn.daftara.me
    ServerAlias cdn.daftra.me
    LogLevel info
    ErrorLog /var/logs/daftra/daftara-cdn-error.log

    #SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
    CustomLog "/var/logs/daftra/daftara-cdn-access.log" combined
    Header set Access-Control-Allow-Origin "*"
    <Directory /var/www/html/daftra/cdn/>
            Options Indexes FollowSymLinks MultiViews
            AllowOverride All
    </Directory>
    SSLEngine on
    SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
    SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>
</VirtualHost>

# <VirtualHost *:443>
#     DocumentRoot "/var/www/html/daftra/cdn/app/"
#     ServerName cdn.daftra.me
#     ServerAlias cdn.daftra.me
#     LogLevel info
#     ErrorLog /var/logs/daftra/daftra-cdn-error.log
#     #SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
#     CustomLog "/var/logs/daftra/daftra-cdn-access.log" combined
#     Header set Access-Control-Allow-Origin "*"
#     <Directory /var/www/html/daftra/cdn/>
#             Options Indexes FollowSymLinks MultiViews
#             AllowOverride All
#     </Directory>
#     SSLEngine on
#     SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
#     SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

#     <FilesMatch "\.(cgi|shtml|phtml|php)$">
#         SSLOptions +StdEnvVars
#     </FilesMatch>
# </VirtualHost>

##################################################################



#Daftra  System
##################################################################
<VirtualHost *:443>
    ##ServerAdmin <EMAIL>
    DocumentRoot "/var/www/html/daftra/izam-app-manager/public"
    ServerAlias app.daftara.me
    ServerAlias app.daftra.me
   LogLevel info
    ErrorLog /var/logs/daftra/izam-app-manager-error.log

    #SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
    CustomLog "/var/logs/daftra/izam-app-manager-access.log" combined
    <Directory /var/www/html/daftra/izam-app-manager/public>
        Options Indexes FollowSymLinks
        #Options Indexes FollowSymLinks MultiViews
            AllowOverride All
    </Directory>

    SSLEngine on
    SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
    SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>
</VirtualHost>


<VirtualHost *:80>
    ##ServerAdmin <EMAIL>
    DocumentRoot "/var/www/html/daftra/cake/"
    ServerAlias *.daftara.me
    ServerAlias *.daftra.me
<Directory /var/www/html/daftra/cake/>
        AllowOverride All
        Require all granted
</Directory>
</VirtualHost>

<VirtualHost *:443>
    DocumentRoot /var/www/html/daftra/izam-cache
    ServerName cache.daftra.me
    ServerAlias cache.daftra.me
    LogLevel error
    ErrorLog /var/logs/daftra/daftra-cache-error.log
    CustomLog "/var/logs/daftra/daftra-cache-access.log" combined
    <Directory /var/www/html/daftra/izam-cache>
        SSLRenegBufferSize 209715200
          Options -Indexes
          AllowOverride all
    </Directory>
    LogLevel error
    SSLEngine on
    SSLCertificateFile /etc/apache2/ssl/daftra_com.crt
    SSLCertificateKeyFile /etc/apache2/ssl/server.key
    SSLCACertificateFile /etc/apache2/ssl/daftra_com.ca
</VirtualHost>
<VirtualHost *:443>
    DocumentRoot /var/www/html/daftra/izam-cache
    ServerName cache.daftra.me
    ServerAlias cache.daftra.me
    LogLevel error
    ErrorLog /var/logs/daftra/daftra-cache-error.log
    CustomLog "/var/logs/daftra/daftra-cache-access.log" combined
    <Directory /var/www/html/daftra/izam-cache>
        SSLRenegBufferSize 209715200
          Options -Indexes
          AllowOverride all
    </Directory>
    LogLevel error
    SSLEngine on
    SSLCertificateFile /etc/apache2/ssl/daftra_com.crt
    SSLCertificateKeyFile /etc/apache2/ssl/server.key
    SSLCACertificateFile /etc/apache2/ssl/daftra_com.ca
</VirtualHost>
<VirtualHost *:443>
    DocumentRoot /var/www/html/daftra/myredis
    ServerName myredis.daftra.me
    ServerAlias myredis.daftra.me
    LogLevel error
    ErrorLog /var/logs/daftra/daftra-myredis-error.log
    CustomLog "/var/logs/daftra/daftra-myredis-access.log" combined
    <Directory /var/www/html/daftra/izam-myredis>
        SSLRenegBufferSize 209715200
          Options -Indexes
          AllowOverride all
    </Directory>
    LogLevel error
    SSLEngine on
    SSLCertificateFile /etc/apache2/ssl/daftra_com.crt
    SSLCertificateKeyFile /etc/apache2/ssl/server.key
    SSLCACertificateFile /etc/apache2/ssl/daftra_com.ca
</VirtualHost>
##################################################################
<VirtualHost *:443>
    LuaQuickHandler /opt/LuaQuickHandler.lua  rate_limit
    LuaHookLog /opt/LuaHookLog.lua rate_limit
    ##ServerAdmin <EMAIL>
    DocumentRoot "/var/www/html/daftra/cake/"
    ServerAlias *.daftra.me
    LogLevel info
    ErrorLog /var/logs/daftra/daftra-system-error.log
    #SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
    CustomLog "/var/logs/daftra/daftra-system-access.log" combined
    SetEnv no-gzip 1
    <Directory /var/www/html/daftra/cake/>
CGIPassAuth on
        Options Indexes FollowSymLinks
        #Options Indexes FollowSymLinks MultiViews
            AllowOverride All
    </Directory>
    Alias /v2 "/var/www/html/daftra/laravel/public/"
    <Directory /var/www/html/daftra/laravel/public/>
        AllowOverride All
        Require all granted
    </Directory>

    SSLEngine on
    SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
    SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>
</VirtualHost>

##################################################################
<VirtualHost *:443>
    ##ServerAdmin <EMAIL>
    DocumentRoot "/var/www/html/daftra/cake/"
    ServerAlias *.daftara.me
    LogLevel info
    ErrorLog /var/logs/daftra/daftara-system-error.log

    #SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
    CustomLog "/var/logs/daftra/daftara-system-access.log" combined
  SetEnv no-gzip 1


    <Directory /var/www/html/daftra/cake/>
        Options Indexes FollowSymLinks
        #Options Indexes FollowSymLinks MultiViews
            AllowOverride All
    </Directory>
    Alias /v2 "/var/www/html/daftra/laravel/public/"
    <Directory /var/www/html/daftra/laravel/public/>
        AllowOverride All
        Require all granted
    </Directory>

    SSLEngine on
    SSLCertificateFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/fullchain.pem"
    SSLCertificateKeyFile "/etc/apache2/ssl/letsencrypt/live/daftara.me/privkey.pem"

    <FilesMatch "\.(cgi|shtml|phtml|php)$">
        SSLOptions +StdEnvVars
    </FilesMatch>
</VirtualHost>


##################################################################
#
