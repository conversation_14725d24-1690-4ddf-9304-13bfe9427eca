---
description: 
globs: 
alwaysApply: false
---
---
description: How to add or edit Cursor rules in our project
globs: 
alwaysApply: false
---
# Cursor Rules Location

How to add new cursor rules to the project

## Rule Placement Strategy

1. **App-specific rules**: Place in `apps/{app-name}/.cursor/rules/`
2. **Package-specific rules**: Place in `packages/{package-name}/.cursor/rules/`
3. **General monorepo rules**: Place in `PROJECT_ROOT/.cursor/rules/`

## Directory Structure Examples

### For App-Specific Rules
```
frontend/
└── app/
    └── .cursor/
        └── rules/
            ├── react-components.mdc
            └── api-usage.mdc
```

### For Package-Specific Rules
```
frontend/
├── design-system/
│   └── .cursor/
│       └── rules/
│           ├── component-api.mdc
│           └── theme-usage.mdc
│           └── plugin-development.mdc
└── core/
    └── .cursor/
        └── rules/
            └── utility-functions.mdc
```

### For General Monorepo Rules
```
PROJECT_ROOT/
├── .cursor/
│   └── rules/
│       ├── monorepo-structure.mdc
│       └── general-conventions.mdc
└── ...
```

## Naming Convention

- Use kebab-case for filenames
- Always use .mdc extension
- Make names descriptive of the rule's purpose
- Prefix with context when needed (e.g., `webapp-routing.mdc`, `design-system-theming.mdc`)

## Rule Placement Guidelines

### Place rules in app directories when:
- Rules are specific to that app's technology stack
- Rules apply only to that app's codebase
- Rules are about app-specific patterns or conventions

### Place rules in package directories when:
- Rules are about the package's API or usage patterns
- Rules apply to consumers of the package
- Rules are about package-specific development patterns

### Place rules in monorepo root when:
- Rules apply across multiple apps/packages
- Rules are about general code quality or conventions
- Rules are about monorepo structure or tooling

## Never place rule files:
- In random subdirectories
- Mixed together when they serve different purposes

## Cursor Rule Structure

````
---
description: Short description of the rule's purpose
globs: optional/path/pattern/**/* 
alwaysApply: false
---
# Rule Title

Main content explaining the rule with markdown formatting.

1. Step-by-step instructions
2. Code examples, taken from the code
3. Guidelines

Example:
```typescript
// Good example
function goodExample() {
  // Implementation following guidelines
}

// Bad example
function badExample() {
  // Implementation not following guidelines
}
```
````