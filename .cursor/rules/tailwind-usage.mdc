---
description: Tailwind CSS usage with required `tw:` prefix — prefix comes before variants, for consistent scanning and parsing.
globs:
alwaysApply: false
------------------

# Tailwind — Use `tw:` Prefix (Before Variants)

This rule enforces that **all Tailwind utility classes must be prefixed with `tw:`**, and the prefix must come **before any variant** (e.g. `tw:hover:bg-blue-600`, not `hover:tw:bg-blue-600`).

## Core Principles

### 1 Prefix every Tailwind utility

**Bad**

```tsx
<div className="p-4 bg-white rounded-lg" />
```

**Good**

```tsx
<div className="tw:p-4 tw:bg-white tw:rounded-lg" />
```

---

### 2 Variants keep prefix before variant

The `tw:` goes at the very start of the variant chain.
This ensures **every class token** starts with `tw:`, making scanning & linting easier.

**Bad**

```tsx
<button className="hover:tw:bg-blue-600 md:tw:px-6" />
```

**Good**

```tsx
<button className="tw:hover:bg-blue-600 tw:md:px-6" />
```

---

### 3 Compose safely with helpers

Use `clsx` and `twMerge` to handle conditionals & deduplication.

```tsx
import { twMerge } from "tailwind-merge";
import clsx from "clsx";

const btn = twMerge(
  clsx(
    "tw:inline-flex tw:items-center tw:justify-center tw:rounded-2xl tw:px-4 tw:py-2",
    "tw:text-sm tw:font-medium",
    disabled ? "tw:bg-gray-200 tw:text-gray-500" : "tw:bg-blue-600 tw:text-white",
    full ? "tw:w-full" : null
  )
);
```

---

### 4 Coexist with non-Tailwind classes

Old clases or BEM classes stay unprefixed.

```tsx
<Card className="card--elevated tw:p-6 tw:shadow-md" />
```

---

### 5 Arbitrary values & complex selectors

Prefix applies **before variant** even with arbitrary values.

**Bad**

```tsx
<div className="mt-[3.5rem] [&>*:first-child]:mb-2" />
```

**Good**

```tsx
<div className="tw:mt-[3.5rem] tw:[&>*:first-child]:mb-2" />
```

For complex selectors (`[&>*:first-child]:…`), the `tw:` is placed **before the variant/selector**.

---

## Patterns & Examples

**Interactive states**

```tsx
<a className="tw:text-blue-600 tw:hover:text-blue-700 tw:focus:outline-none tw:focus:ring-2 tw:focus:ring-offset-2" />
```

**Responsive**

```tsx
<div className="tw:grid tw:grid-cols-1 tw:md:grid-cols-2 tw:lg:grid-cols-3 tw:gap-4" />
```

**Data attributes**

```tsx
<button
  data-state={open ? "open" : "closed"}
  className="tw:transition-colors tw:data-[state=open]:bg-emerald-600 tw:data-[state=closed]:bg-slate-700"
/>
```

---

## DO / DON’T

**DO**

* ✅ Prefix **every** Tailwind class with `tw:`.
* ✅ Put `tw:` **before variants** (`tw:hover:bg-blue-600`, not `hover:tw:bg-blue-600`).
* ✅ Use `clsx` + `twMerge` for conditional and merging.
* ✅ Keep component/DS classes unprefixed.

**DON’T**

* ❌ Mix prefixed and unprefixed utilities.
* ❌ Put prefix after variant.
* ❌ Remove prefix in complex selectors.
* ❌ Replace DS classes with Tailwind when composition is better.

