---
description: 
globs: frontend/**/*
alwaysApply: false
---
# Custom Hooks Pattern for Webapp

This rule guides AI agents to prioritize creating custom hooks to encapsulate complex logic, making components cleaner and logic reusable.

## Core Principle

**Always extract complex logic into custom hooks rather than keeping it in components.**

## When to Create Custom Hooks

### ✅ Create custom hooks for:
- **Complex state management** (filters, form state, UI state)
- **Data transformation logic** (API format conversion, filtering, sorting)
- **Business logic** that spans multiple components
- **URL query parameter management**
- **Complex calculations** or derived state
- **Event handling logic** with multiple steps
- **Integration with external APIs** or services
- **Reusable logic** across multiple components

### ❌ Don't create custom hooks for:
- Simple one-line operations
- Component-specific UI state that won't be reused
- Basic React built-in hooks usage without additional logic

## Pattern Examples from Codebase

### 1. Filter Management Pattern (`useActionsFilter.ts`)

**Use Case**: Managing complex filter state with URL synchronization

```typescript
type Filters = {
    showCompleted?: boolean | null
    showStarred?: boolean | null
    showMyActions?: boolean | null
    keyword: string
    groupBy: string
    users: FormattedUser[]
    status: string[]
    priority: string[]
    groups: { id: string }[]
    // ... more filters
}

const defaultValues: Filters = {
    showCompleted: false,
    showStarred: false,
    showMyActions: false,
    keyword: '',
    groupBy: GROUPING_TYPES.STATUS,
    users: [],
    // ... more defaults
}

const useActionsFilter = () => {
    const { resetFilters, applyFilterUpdates, checkFiltersApplied } =
        useBaseFiltersUtils<Filters>()
    const query = useQueryParams() as Record<keyof Filters, string>

    const clearFilters = useCallback(() => {
        resetFilters([
            'keyword',
            'users',
            'status',
            // ... more filter keys
        ])
    }, [resetFilters])

    const filters = useMemo(() => parseQuery(query), [query])

    const isFiltersApplied = useMemo(() => {
        return checkFiltersApplied(filters) || filters.showCompleted === false
    }, [checkFiltersApplied, filters])

    return {
        filters,
        clearFilters,
        updateFilter: applyFilterUpdates,
        isFiltersApplied,
    }
}
```

**Key Features**:
- ✅ Encapsulates complex filter parsing logic
- ✅ Provides clean API for components
- ✅ Manages URL synchronization
- ✅ Uses proper TypeScript typing
- ✅ Leverages memoization for performance

### 2. API Transformation Pattern (`useActionsApiFilters.ts`)

**Use Case**: Converting UI filters to API-compatible format

```typescript
type UseActionsApiFiltersReturn = {
    where: ActionFilterInput
    order: Array<ActionSortInput>
    category: ActionQueryCategory
}

const useActionsApiFilters = (): UseActionsApiFiltersReturn => {
    const {
        filters: {
            mainTab,
            listTab,
            users,
            status,
            priority,
            // ... more filters
        },
    } = useActionsFiltersAndGrouping()
    const { user } = useUser()

    const where = useMemo<ActionFilterInput>(() => {
        const filter: Array<ActionFilterInput> = [{ isArchived: { eq: false } }]
        
        if (mainTab === 'myActions' || listTab === 'myActions' || showMyActions) {
            filter.push({
                assignees: {
                    some: {
                        userId: { eq: user?.id },
                        assigneeRole: { neq: AssigneeRole.Auditor },
                    },
                },
            })
        }
        // ... more filter logic
        
        return { and: filter }
    }, [
        completedOnRange,
        createdOnRange,
        dueDateRange,
        // ... dependencies
    ])

    const order: Array<ActionSortInput> = useMemo(() => {
        if (listTab === 'starred') {
            return [
                { starredAtUtc: SortEnumType.Desc },
                { createdAtUtc: SortEnumType.Asc },
            ]
        }
        return [
            { dueDate: SortEnumType.Desc },
            { createdAtUtc: SortEnumType.Asc },
        ]
    }, [listTab])

    return {
        where,
        order,
        category,
    }
}
```

**Key Features**:
- ✅ Transforms UI state to API format
- ✅ Complex conditional logic encapsulated
- ✅ Proper dependency management with useMemo
- ✅ Clear return interface
- ✅ Leverages other custom hooks

## Custom Hook Structure Template

```typescript
// 1. Define types for parameters and return value
type UseYourHookProps = {
    // Input parameters
}

type UseYourHookReturn = {
    // Return value structure
}

// 2. Create the custom hook
const useYourHook = ({
    // destructured parameters with proper typing
}: UseYourHookProps): UseYourHookReturn => {
    // 3. Use other hooks as needed
    const { /* ... */ } = useOtherHooks()
    
    // 4. Complex logic with proper memoization
    const computedValue = useMemo(() => {
        // Complex computation logic
        return result
    }, [/* dependencies */])

    // 5. Event handlers with useCallback
    const handleSomething = useCallback(() => {
        // Event handling logic
    }, [/* dependencies */])

    // 6. Return structured API
    return {
        // data
        computedValue,
        // actions
        handleSomething,
        // state
        loading,
        error,
    }
}

export { useYourHook }
```

## Best Practices

### Structure & Organization

1. **File Naming**: Use descriptive names with `use` prefix
   ```
   ✅ useActionsFilter.ts
   ✅ useActionsApiFilters.ts
   ✅ useMeetingState.ts
   
   ❌ actionsFilter.ts
   ❌ hooks.ts
   ❌ utils.ts
   ```

2. **Location**: Place in appropriate directories
   ```
   ✅ src/hooks/useGlobalHook.ts           (global hooks)
   ✅ src/services/hooks/useServiceHook.ts (service-specific)
   ✅ src/components/MyComponent/useMyComponentHook.ts (component-specific)
   ```

3. **Export Pattern**: Use named exports
   ```typescript
   // ✅ Good
   export { useActionsFilter }
   
   // ❌ Avoid
   export default useActionsFilter
   ```

### TypeScript Best Practices

1. **Type Definitions**: Always define proper types
   ```typescript
   // ✅ Good
   type UseActionsFilterReturn = {
       filters: Filters
       clearFilters: () => void
       updateFilter: (updates: Partial<Filters>) => void
       isFiltersApplied: boolean
   }
   
   // ❌ Avoid
   const useActionsFilter = () => {
       // returning untyped object
   }
   ```

2. **Generic Types**: Use generics for reusable hooks
   ```typescript
   // ✅ Good
   const useFilters = <T extends Record<string, any>>(
       defaultValues: T
   ): UseFiltersReturn<T> => {
       // implementation
   }
   ```

### Performance Optimization

1. **Memoization**: Use useMemo for expensive computations
   ```typescript
   // ✅ Good
   const expensiveValue = useMemo(() => {
       return heavyComputation(dependencies)
   }, [dependencies])
   
   // ❌ Avoid
   const expensiveValue = heavyComputation(dependencies) // Runs every render
   ```

2. **Callbacks**: Use useCallback for event handlers
   ```typescript
   // ✅ Good
   const handleClick = useCallback(() => {
       // handler logic
   }, [dependencies])
   
   // ❌ Avoid
   const handleClick = () => {
       // handler logic - new function every render
   }
   ```

3. **Dependency Arrays**: Be precise with dependencies
   ```typescript
   // ✅ Good
   const result = useMemo(() => {
       return processData(data.items)
   }, [data.items]) // Only the specific property needed
   
   // ❌ Avoid
   const result = useMemo(() => {
       return processData(data.items)
   }, [data]) // Entire object, may cause unnecessary re-renders
   ```

## Summary

- **Extract complex logic** into custom hooks
- **Follow naming conventions** and proper structure
- **Use TypeScript** for type safety
- **Optimize performance** with memoization

This pattern keeps components clean, makes logic reusable, and improves maintainability across the webapp.
