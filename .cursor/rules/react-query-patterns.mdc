---
description: React Query patterns — custom hooks, Suspense-first reads, cache hygiene, and safe invalidation/optimistic updates.
globs:
alwaysApply: false
------------------

# React Query PatternsWebapp

This rule provides guidance on using **React Query** effectively in the webapp, focusing on **custom hooks**, **Suspense-first reads**, **stable query keys**, **cache configuration**, and **proper invalidation/optimistic updates**.

## Core Principles

### 1 Encapsulate complex data fetching in custom hooks

Always create **custom hooks** for non-trivial data access instead of calling React Query primitives directly in components.

**Good Example** (`useUniversalQuickSearch.ts` adapted):

```typescript
import { useMemo } from "react";
import { useSuspenseQuery, queryOptions } from "@tanstack/react-query";
import { api } from "@/lib/api"; // your fetcher

type Filter = "All" | "Meeting" | "Group" | "Other";

const universalQuickSearchQuery = (keyword: string, filter: Filter) =>
  queryOptions({
    queryKey: ["universal-search", { keyword, filter }],
    queryFn: async () => {
      const type =
        filter === "Meeting" ? { eq: "Meeting" } :
        filter === "Group"   ? { eq: "Group" } :
        { nin: ["Meeting", "Group"] };

      return api.search({
        pattern: keyword,
        first: 15,
        where: filter === "All" ? null : { type },
        ...(filter === "All" && { limit: 5 }),
      });
    },
    staleTime: 60_000, // 1 min: avoids thrashing
  });

export function useUniversalQuickSearch(keyword: string, filter: Filter) {
  const opts = useMemo(() => universalQuickSearchQuery(keyword, filter), [keyword, filter]);
  const { data } = useSuspenseQuery(opts);
  return { data };
}
```

**Bad Example**:

```typescript
function MyComponent() {
  // ❌ Complex query & variables inline in component
  const { data } = useSuspenseQuery({ queryKey: ["x"], queryFn: () => fetch(/*...*/) });
}
```

---

### 2 Always use **Suspense** for reads

Prefer `useSuspenseQuery` (or `useQuery({ suspense: true })`) so loading states are handled by **React Suspense** instead of ad-hoc booleans.

**Required pattern**:

```tsx
// boundary placement in UI
<Suspense fallback={<Spinner />}>
  <MyDataSection />
</Suspense>
```

Key points:

* Use **Suspense boundaries** near sections, not the whole page.
* Co-locate related queries within the same boundary when they load together.

---

### 3 Stable, descriptive **query keys**

Use **array keys** with a string namespace and a small params object. Put keys in a central module if reused.

```ts
// keys.ts
export const qk = {
  user: (id: string) => ["user", { id }] as const,
  actions: (filters: { status?: string }) => ["actions", { filters }] as const,
};
```

---

### 4 Cache strategy (stale vs. garbage collection)

Be explicit with `staleTime` (freshness) and `gcTime` (cache lifetime):

* Default: `staleTime: 60_000`, `gcTime: 5 * 60_000`.
* Real-time/fast-changing data: `staleTime: 0`.
* Rarely changing: `staleTime: 5 * 60_000` or higher.

```ts
useSuspenseQuery({
  queryKey: qk.user(userId),
  queryFn: () => api.getUser(userId),
  staleTime: 60_000,
  gcTime: 300_000,
});
```

---

### 5 Invalidate after mutations (and prefer targeted invalidation)

After any successful write, **invalidate** the minimal set of affected queries.

```ts
import { useMutation, useQueryClient } from "@tanstack/react-query";

function useUpdateUser() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: (input: { id: string; data: any }) => api.updateUser(input),
    onSuccess: (_data, vars) => {
      // Always invalidate relevant cache after a mutation
      qc.invalidateQueries({ queryKey: qk.user(vars.id) });
      // If list views depend on it:
      qc.invalidateQueries({ queryKey: qk.actions({}) });
    },
  });
}
```

---

### 6 Optimistic updates (safe & reversible)

Use `onMutate/onError/onSettled` to optimistically update lists/details, then roll back if the server rejects.

```ts
function useToggleActionDone() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: ({ id, done }: { id: string; done: boolean }) =>
      api.toggleAction(id, done),
    onMutate: async (vars) => {
      await qc.cancelQueries({ queryKey: qk.actions({}) });
      const prev = qc.getQueryData<YourActionType[]>(qk.actions({}));

      // optimistic update
      qc.setQueryData<YourActionType[]>(qk.actions({}), (old) =>
        (old ?? []).map(a => a.id === vars.id ? { ...a, done: vars.done } : a)
      );

      return { prev };
    },
    onError: (_err, _vars, ctx) => {
      // rollback
      if (ctx?.prev) qc.setQueryData(qk.actions({}), ctx.prev);
    },
    onSettled: () => {
      // final revalidation
      qc.invalidateQueries({ queryKey: qk.actions({}) });
    },
  });
}
```

---

## Custom Hook Structure

Follow this structure for data hooks:

```ts
// 1) Define query key + options
import { queryOptions, useSuspenseQuery } from "@tanstack/react-query";
import { qk } from "@/services/keys";
import { api } from "@/lib/api";

const userQuery = (id: string) =>
  queryOptions({
    queryKey: qk.user(id),
    queryFn: () => api.getUser(id),
    select: (data) => ({ id: data.id, name: data.name }), // shape early
    staleTime: 60_000,
  });

type UseUserProps = { id: string };

// 2) Create the hook
export function useUser({ id }: UseUserProps) {
  const opts = userQuery(id);
  const { data } = useSuspenseQuery(opts);
  return { user: data };
}
```

For writes:

```ts
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useRenameUser() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      api.renameUser(id, name),
    onSuccess: (_data, { id }) => {
      qc.invalidateQueries({ queryKey: qk.user(id) });
    },
  });
}
```

---

## Best Practices

### DO

* ✅ Use **custom hooks** for complex queries/mutations.
* ✅ Prefer **`useSuspenseQuery`** and **React Suspense** for loading states.
* ✅ Keep **query keys stable** and descriptive; centralize shared keys.
* ✅ Use `select` to **shape data** before it reaches components.
* ✅ Configure **`staleTime`/`gcTime`** per use case.
* ✅ **Always invalidate** relevant queries after mutations.
* ✅ Use **optimistic updates** for snappy UX; roll back on error.
* ✅ Handle errors with **Error Boundaries** and mutation error UI.

### DON’T

* ❌ Scatter complex query logic inside components.
* ❌ Rely on `useEffect` + `fetch/axios` for server state.
* ❌ Skip invalidation after a write.
* ❌ Recreate heavy objects each render (stabilize inputs with `useMemo` when needed).
* ❌ Hide loading with ad-hoc booleans when Suspense is available.
