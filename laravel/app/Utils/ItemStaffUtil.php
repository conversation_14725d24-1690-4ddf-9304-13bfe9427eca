<?php
namespace App\Utils;

/**
 * <AUTHOR> <<EMAIL>>
 */
class ItemStaffUtil{
    const CLIENT_ITEM_TYPE = 1;
    const INVOICE_ITEM_TYPE = 2;
    const ESTIMATE_ITEM_TYPE = 3;
    const PO_ITEM_TYPE = 4;
    const STAFF_ITEM_TYPE = 5;
    const SUPPLIER_ITEM_TYPE = 6;
    const PRODUCT_ITEM_TYPE = 7;
    const WORK_ORDER_ITEM_TYPE = 8;
    const APPOINTMENT_ITEM_TYPE = 9;
    const SHIFT_ITEM_TYPE = 10;
    const BOOKING_ITEM_TYPE = 11;
    const SALES_ITEM_TYPE = 12;
    const BRANCH_ITEM_TYPE = 13;
    const SALES_ORDER_TYPE = 14;
    const INVOICE_ITEM_SALES_TYPE = 15;
    const OFF_DAY_ITEM_TYPE = 16;

    static $mappedItemStaff = [
        EntityKeyTypesUtil::CLIENT_ENTITY_KEY  => self::CLIENT_ITEM_TYPE,
        EntityKeyTypesUtil::INVOICE_ENTITY_KEY  => self::INVOICE_ITEM_TYPE,
        EntityKeyTypesUtil::ESTIMATE  => self::ESTIMATE_ITEM_TYPE,
        EntityKeyTypesUtil::POST  => self::PO_ITEM_TYPE,
        EntityKeyTypesUtil::STAFF_ENTITY_KEY  => self::STAFF_ITEM_TYPE,
        EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY  => self::SUPPLIER_ITEM_TYPE,
        EntityKeyTypesUtil::PRODUCT_ENTITY_KEY  => self::PRODUCT_ITEM_TYPE,
        EntityKeyTypesUtil::WORK_ORDER  => self::WORK_ORDER_ITEM_TYPE,
        EntityKeyTypesUtil::APPOINTMENTS_STAFF  => self::APPOINTMENT_ITEM_TYPE,
        EntityKeyTypesUtil::POS_SHIFT_ENTITY_KEY  => self::SHIFT_ITEM_TYPE,
        EntityKeyTypesUtil::BOOKING  => self::BOOKING_ITEM_TYPE,
        EntityKeyTypesUtil::BRANCH_ENTITY_KEY  => self::BRANCH_ITEM_TYPE,
        EntityKeyTypesUtil::SALES_ORDER  => self::SALES_ORDER_TYPE,
        EntityKeyTypesUtil::INVOICE_ITEM_ENTITY_KEY  => self::INVOICE_ITEM_SALES_TYPE,
        EntityKeyTypesUtil::OFF_DAY_ENTITY_KEY  => self::OFF_DAY_ITEM_TYPE,
    ];
    public static function entityHasItemStaffType($entity) {
        return isset(self::$mappedItemStaff[$entity]);
    }

    public static function getEntityItemStaffType($entity) {
        return (self::$mappedItemStaff[$entity]);
    }
}
