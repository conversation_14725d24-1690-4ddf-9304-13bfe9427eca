<?php

namespace app\Utils;

class EntityDocumentPermissionsUtil
{
    public static function getEntityDocumentPermissions(string $entityKey)
    {
        return match ($entityKey){
            'staff'=> ['create'=> PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS , 'show'=> PermissionUtil::VIEW_EMPLOYEE_DOCUMENTS , 'update'=> PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS , 'delete'=> PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS],
            default => []
        };
    }


}
