<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 17/04/19
 * Time: 08:43 ص
 */

namespace App\Utils;

use \Izam\Daftra\Common\Utils\PluginUtil as BasePluginUtil;

class PluginUtil extends BasePluginUtil
{
    /** @todo remove this file content */
    CONST ClientsPlugin = 1;
    CONST InvoicesPlugin = 2;
    CONST SalesPlugin = self::InvoicesPlugin;
    CONST PaymentsPlugin = 3;
    CONST EstimatesPlugin = 4;
    CONST StaffPlugin = 5;
    CONST ProductsPlugin = 6;
    CONST TimeTrackingPlugin = 8;
    CONST ExpensesPlugin = 9;
    CONST FormsPlugin = 10;
    CONST ReportsPlugin = 11;
    CONST SettingsPlugin = 12;
    CONST HRM_CHEQUE_CYCLE_PLUGIN = 69;
    CONST InventoryPlugin = 72;
    CONST FollowupPlugin = 74;
    CONST WorkOrderPlugin = 75;
    CONST PrescriptionPlugin = 76;
    CONST AccountingPlugin = 78;
    CONST WebsiteFrontPlugin = 79;
    CONST SMSPlugin = 80;
    CONST AutoNumberPlugin = 81;
    CONST PosPlugin = 82;
    CONST CustomFormsPlugin = 83;
    CONST BookingPlugin = 84;
    CONST BNR = 85;
    CONST BnrPlugin = 85;
    CONST OffersPlugin = 86;
    CONST BranchesPlugin = 87;
    CONST HRM_PLUGIN = 88;
    const HRM_ATTENDANCE_PLUGIN = 89;
    const HRM_PAYROLL_PLUGIN = 90;
    const INSURANCE_PLUGIN = 91;
    const PRODUCT_TRACKING_PLUGIN = 92;
    const REQUESTS_PLUGIN = 93;
    const CREDIT_PLUGIN = 94;
    const MEMBERSHIP_PLUGIN = 95;
    const CLIENT_ATTENDANCE_PLUGIN = 96;
    const INSTALLMENT_AGREEMENT_PLUGIN = 97;
    const COMMISSION_PLUGIN = 100;
    const ETA_PLUGIN = 101;
    const LOCAL_ENTITIES = 9700;
    const EINVOICE_PLUGIN=105;
    const PURCHASE_CYCLE_PLUGIN=106;
    const WORKFLOW_PLUGIN = 107;
    const CLIENT_LOYALTY = 108;
    const RENTAL_PLUGIN = 109;
    const MANUFACTURING_PLUGIN = 110;
    const MILEAGE_PLUGIN = 111;
    const LEASE_CONTRACT_PLUGIN = 112;
    const NEW_BOOKING_PLUGIN = 113;
    const JORDAN_EINVOICE_PLUGIN = 114;

    const MUDAD_PLUGIN = 115;

    public static function getClassOfPlugin($plugin_id)
    {
        return [
            self::InventoryPlugin => array('class' => 'flaticon-products'),
            self::FollowupPlugin => array('class' => 'flaticon-clients'),
            self::PrescriptionPlugin => array('class' => 'flaticon-clinic'),
            self::TimeTrackingPlugin => array('class' => 'flaticon-time_tracking'),
            self::ExpensesPlugin => array('class' => 'flaticon-expenses'),
            self::WorkOrderPlugin => array('class' => 'flaticon-work_orders'),
            self::AccountingPlugin => array('class' => 'flaticon-accounting'),
            self::WebsiteFrontPlugin => array('class' => 'flaticon-shop-front'),
            self::SMSPlugin => array('class' => 'flaticon-sms'),
            self::CustomFormsPlugin => array('class' => 'flaticon-database'),
            self::PosPlugin => array('class' => 'flaticon-shop-front', 'defaultTemplateID_ar' => 2010179, 'defaultTemplateID_en' => 2010180),
            self::BookingPlugin => array('class' => 'flaticon-bookings'),
            self::BnrPlugin => array('class' => 'flaticon-airplane-flight'),
            self::BranchesPlugin => array('class' => 'flaticon-shop-front'),
            self::StaffPlugin => array('class' => 'list-icon mdi mdi-account-multiple-outline'),
            self::OffersPlugin => array('class' => 'flaticon-shop-front'),
            self::HRM_PLUGIN => array('class' => 'mdi mdi-briefcase-account'),
            self::HRM_ATTENDANCE_PLUGIN => array('class' => 'mdi mdi-calendar-month'),
            self::HRM_PAYROLL_PLUGIN => array('class' => 'mdi mdi-cash-usd'),
            self::INSURANCE_PLUGIN => array('class' => 'mdi mdi-shield'),
            self::HRM_CHEQUE_CYCLE_PLUGIN => array('class' => 'mdi mdi-checkbook'),
            self::REQUESTS_PLUGIN => array('class' => 'fal fa-clipboard-list-check'),
            self::CREDIT_PLUGIN => array('class' => 'fas fa-layer-group'),
            self::MEMBERSHIP_PLUGIN => array('class' => 'mdi mdi-account-badge-horizontal'),
            self::CLIENT_ATTENDANCE_PLUGIN => array('class' => 'mdi mdi-calendar-account'),
            self::INSTALLMENT_AGREEMENT_PLUGIN=>array('class'=>'mdi mdi-account-cash'),
            self::COMMISSION_PLUGIN => ['class' => 's2020 fas fa-donate'],
            self::SalesPlugin => ['class' => 'flaticon-invoices'],
            self::EINVOICE_PLUGIN => ['class' => 'flaticon-invoices'],
	        self::ETA_PLUGIN => ['class' => 'far fa-file-invoice-dollar'],
            self::ClientsPlugin => ['class' => 'flaticon-clients'],
            self::PURCHASE_CYCLE_PLUGIN => ['class' => 'mdi  mdi-truck-delivery-outline'],
            self::WORKFLOW_PLUGIN => array('class' => 'flaticon-work_orders'),
            self::CLIENT_LOYALTY => ['class' => 'mdi  mdi-gift'],
            self::RENTAL_PLUGIN => ['class' => 'mdi mdi-table-key'],
            self::MANUFACTURING_PLUGIN => ['class' => 'mdi mdi-hammer-wrench'],
            self::MILEAGE_PLUGIN => array('class' => 'mdi mdi-routes'),
            self::LEASE_CONTRACT_PLUGIN => ['class' => 'mdi mdi-file-sign'],
            self::NEW_BOOKING_PLUGIN => ['class' => 'flaticon-bookings'],
            self::JORDAN_EINVOICE_PLUGIN => ['class' => 'flaticon-invoices'],

        ][$plugin_id]??array('class' => 'flaticon-work_orders');

    }
}
