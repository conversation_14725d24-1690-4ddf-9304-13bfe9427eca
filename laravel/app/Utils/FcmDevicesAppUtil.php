<?php

namespace App\Utils;

use App\DTO\NotificationPreferenceDto;

/**
 * Class FcmDevicesAppUtil
 *
 * Utility class for handling constants and helper methods related to Firebase Cloud Messaging (FCM)
 * for different applications (e.g., Leave App).
 *
 * This class provides:
 * - Default notification preferences for each app
 * - Mapping between app entities, actions, and notification preference keys
 */

class FcmDevicesAppUtil{

    /**
     * Key used in the `entity_app_data` table to store notification preferences
     */
    CONST ENTITY_APP_DATA_NOTIFICATION_PREFERENCE_KEY = 'push_notification_preferences';

    /**
     * Constant to indicate that the notification is related to a staff member.
     */
    CONST STAFF_USER_TYPE = 'staff';
    
    /**
     * App key for the Leave Application system
     */
    CONST LEAVE_APP = 'leave_app';    

    /**
     * Returns all default notification preferences for the specified application.
     *
     * @param string $appKey The app key (e.g., 'leave_app').
     * @return array|null Returns an array of default preferences or null if not configured.
     */
    public static function getAllNotificationPreferenceForApp($appKey){
        $defaults = [
            self::LEAVE_APP => [
                [
                    'key' => 'leave_application_notifiers',
                    'title' => 'Leave application created',
                    'value' => 1,
                ],
                [
                    'key' => 'leave_application_updates',
                    'title' => 'Leave application approved or rejected',
                    'value' => 1,
                ],
                [
                    'key' => 'payslip_approve',
                    'title' => 'Payslips',
                    'value' => 1,
                ],
            ],
        ];

        return $defaults[$appKey] ?? null;
    }

    /**
     * Maps an action performed on an entity to its corresponding notification preference key.
     *
     * Used to determine if a notification should be sent based on user preferences.
     *
     * @param string $appKey The app identifier (e.g., 'leave_app').
     * @param string $entityKey The entity type (e.g., 'leave_application', 'payslip').
     * @param string $action The action performed (e.g., 'create', 'approve').
     * @return string|null The notification preference key or null if not mapped.
     */
    public static function getPreferenceKeyByEntityAndAction($appKey, $entityKey, $action){
        $map = [
            self::LEAVE_APP => [
                'leave_application' => [
                    'create' => 'leave_application_notifiers',
                    'update' => 'leave_application_notifiers',
                    'delete' => 'leave_application_notifiers',
                    'approve' => 'leave_application_updates',
                    'reject' => 'leave_application_updates',
                    'undo_approve' => 'leave_application_updates',
                    'undo_reject' => 'leave_application_updates',
                ],
                'payslip' => [
                    'approve' => 'payslip_approve',
                ],
            ],
        ];

        return $map[$appKey][$entityKey][$action] ?? null;
    }

}