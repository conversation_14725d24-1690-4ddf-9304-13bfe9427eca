<?php
/**
 * Created by PhpStorm.
 * User: Bassel
 * Date: 07/11/19
 * Time: 01:13 م
 */

namespace App\Utils;
class AdditionalImportPartialUtil
{
    public static function getImportPartialsByEntity($entityKey = '')
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::ATTENDANCE_LOG:
                return 'partials.additional_import_inputs.attendance_logs';
            case EntityKeyTypesUtil::PRICE_LIST_ITEMS:
                return 'partials.additional_import_inputs.price_list_items';
            case EntityKeyTypesUtil::ASSET:
                return 'partials.additional_import_inputs.depreciation_method';
            case EntityKeyTypesUtil::INVOICE_ENTITY_KEY:
                return 'partials.additional_import_inputs.invoice';
            case EntityKeyTypesUtil::JOURNAL:
                return 'partials.additional_import_inputs.journal';
            default:
                return null;
        }
    }
}
