<?php

namespace App\Utils\Invoices;

use Izam\Daftra\Invoice\Utils\InvoiceTypeUtil as PackageInvoiceTypeUtil;

class InvoiceTypeUtil extends PackageInvoiceTypeUtil
{
    /**
     * @const InvoiceTypeUtil::INVOICE invoice type invoice
     */
    CONST INVOICE = 0;

    /**
     * @const InvoiceTypeUtil::TEMPLATE invoice type template
     */
    CONST TEMPLATE = 2;

    /**
     * @const InvoiceTypeUtil::SUBSCRIPTION invoice type subscription
     */
    CONST SUBSCRIPTION = 2;

    /**
     * @const InvoiceTypeUtil::ESTIMATE invoice type estimate
     */
    CONST ESTIMATE = 3;

    /**
     * @const InvoiceTypeUtil::CREDIT_NOTE invoice type credit_note
     */
    CONST CREDIT_NOTE = 5;

    /**
     * @const InvoiceTypeUtil::REFUND_RECEIPT invoice type refund_receipt
     */
    CONST REFUND_RECEIPT = 6;

    /**
     * @const InvoiceTypeUtil::RESELLERS invoice type resellers
     */
    CONST RESELLERS = 7;

    /**
     * @const InvoiceTypeUtil::BOOKING invoice type booking
     */
    CONST BOOKING = 8;

    /**
     * @const InvoiceTypeUtil::TEMP_INVOICE invoice type Temp
     */
    CONST TEMP_INVOICE = 9;

    /**
     * @const InvoiceTypeUtil::INSURANCE_INVOICE invoice type Insurance
     */
    CONST INSURANCE_INVOICE = 10;
    CONST SALES_ORDER = 12;
    const DEBIT_NOTE = 16;

    const ADVANCE_PAYMENT_INVOICE = 17;

    private static $types = [
        self::INVOICE => "Invoice",
        self::REFUND_RECEIPT => "Refund Receipt",
        self::CREDIT_NOTE => "Credit Note",
        self::SUBSCRIPTION => "Subscription",
        self::TEMPLATE => "Template",
        self::ADVANCE_PAYMENT_INVOICE => "Advance Payment",
    ];

    /**
     * @param int $id
     * @param int $type
     * @return mixed|string
     */
    public static function getUrlBasedOnType(int $id, int $type)
    {
        switch ($type) {
            case self::INVOICE:
                return getCakeURL(["prefix" => "owner", "controller" => "invoices", "action" => "view", $id]);
            case self::CREDIT_NOTE:
                return getCakeURL(["prefix" => "owner", "controller" => "invoices", "action" => "view_creditnote", $id]);
            case self::REFUND_RECEIPT:
                return getCakeURL(["prefix" => "owner", "controller" => "invoices", "action" => "view_refund", $id]);
            case self::ADVANCE_PAYMENT_INVOICE:
                return getCakeURL(["prefix" => "owner", "controller" => "invoices", "action" => "view_advance_payment", $id]);
            default :
                return CAKE_BASE_URL;
        }
    }

    public static function getTypesList()
    {
        return [
            self::INVOICE  => __t('Invoice'),
            self::SUBSCRIPTION  => __t('Subscription'),
            self::ESTIMATE  => __t('Estimate'),
            self::CREDIT_NOTE  => __t('Credit Note'),
            self::REFUND_RECEIPT  => __t('Refund Receipt'),
            self::RESELLERS  => __t('BNR'),
            self::BOOKING  => __t('Booking'),
            self::TEMP_INVOICE  => __t('Temp Invoice'),
            self::INSURANCE_INVOICE  => __t('Insurance Invoice'),
            self::SALES_ORDER  => __t('Sales Order'),
            self::DEBIT_NOTE  => __t('Debit Note'),
            self::ADVANCE_PAYMENT_INVOICE  => __t('Advance Payment'),
        ];
    }

    /**
     * @param int $type
     * @return mixed
     */
    public static function getType(int $type = 0)
    {
        return self::getTypesList()[$type];
    }
}
