<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 03/03/20
 * Time: 02:52 م
 */

namespace App\Utils;


class ValidationUtil
{
    public static function getMessages() {
          return [
            'required' => __t('This is a required field and could not be empty'),
            // 'required' =>__t('The :attribute field is required.'),

            'email' => __t('Please Enter a Valid Email'),
            // 'email' =>__t('The :attribute must be a valid email address.'),

            'unique' => __t('This Field Already Exist'),
            // 'unique' =>__t('The :attribute has already been taken.'),

            'numeric' => __t('Please Enter a Valid Number'),
            // 'numeric' =>__t('The :attribute must be a number.'),

            'date_format' => __t('Please Enter Correct Date'),
            // 'date_format' =>__t('The :attribute does not match the format :format.'),

            'url' => __t('Please Enter a Valid Url'),
            // 'url' =>__t('The :attribute format is invalid.'),

            'exists' => __t("This Field Reference Doesn't Exists"),
            // 'exists' =>__t('The selected :attribute is invalid.'),

            'integer' => __t("This Field Must be Integer"),
            // 'integer' =>__t('The :attribute must be an integer.'),

            'max' => __t('This Field Exceeded its Maximum Value :max'),
            // 'max' => [
            //     'numeric' =>__t('The :attribute may not be greater than :max.'),
            //     'file' =>__t('The :attribute may not be greater than :max kilobytes.'),
            //     'string' =>__t('The :attribute may not be greater than :max characters.'),
            //     'array' =>__t('The :attribute may not have more than :max items.'),
            // ],

            'min' => __t('This Field Less Than its Minimum Value :min'),
            // 'min' => [
            //     'numeric' => __t( 'The :attribute must be at least :min.'),
            //     'file' =>__t('The :attribute must be at least :min kilobytes.'),
            //     'string' =>__t('The :attribute must be at least :min characters.'),
            //     'array' =>__t('The :attribute must have at least :min items.'),
            // ],
            'uploaded' => __t('Max file size'),
            // 'uploaded' =>__t('The :attribute failed to upload.'),

            'image'=> __t('Invalid file type, the allowed file types are (%s)'),
            // 'image' =>__t('The :attribute must be an image.'),



            // default MESSAGES
            'accepted' =>__t('The :attribute must be accepted.'),
            'active_url' =>__t('The :attribute is not a valid URL.'),
            'after' =>__t('The :attribute must be a date after :date.'),
            'after_or_equal' =>__t('The :attribute must be a date after or equal to :date.'),
            'alpha' =>__t('The :attribute may only contain letters.'),
            'alpha_dash' =>__t('The :attribute may only contain letters, numbers, dashes and underscores.'),
            'alpha_num' =>__t('The :attribute may only contain letters and numbers.'),
            'array' =>__t('The :attribute must be an array.'),
            'before' =>__t('The :attribute must be a date before :date.'),
            'before_or_equal' =>__t('The :attribute must be a date before or equal to :date.'),
            'between' => [
                'numeric' =>__t('The :attribute must be between :min and :max.'),
                'file' =>__t('The :attribute must be between :min and :max kilobytes.'),
                'string' =>__t('The :attribute must be between :min and :max characters.'),
                'array' =>__t('The :attribute must have between :min and :max items.'),
            ],
            'boolean' =>__t('The :attribute field must be true or false.'),
            'confirmed' =>__t('The :attribute confirmation does not match.'),
            'date' =>__t('The :attribute is not a valid date.'),
            'date_equals' =>__t('The :attribute must be a date equal to :date.'),
            'different' =>__t('The :attribute and :other must be different.'),
            'digits' =>__t('The :attribute must be :digits digits.'),
            'digits_between' =>__t('The :attribute must be between :min and :max digits.'),
            'dimensions' =>__t('The :attribute has invalid image dimensions.'),
            'distinct' =>__t('The :attribute field has a duplicate value.'),
            'ends_with' =>__t('The :attribute must end with one of the following: :values.'),
            'file' =>__t('The :attribute must be a file.'),
            'filled' =>__t('The :attribute field must have a value.'),
            'gt' => [
                'numeric' =>__t('The :attribute must be greater than :value.'),
                'file' =>__t('The :attribute must be greater than :value kilobytes.'),
                'string' =>__t('The :attribute must be greater than :value characters.'),
                'array' =>__t('The :attribute must have more than :value items.'),
            ],
            'gte' => [
                'numeric' =>__t('The :attribute must be greater than or equal :value.'),
                'file' =>__t('The :attribute must be greater than or equal :value kilobytes.'),
                'string' =>__t('The :attribute must be greater than or equal :value characters.'),
                'array' =>__t('The :attribute must have :value items or more.'),
            ],
            'in' =>__t('The selected :attribute is invalid.'),
            'in_array' =>__t('The :attribute field does not exist in :other.'),
            'ip' =>__t('The :attribute must be a valid IP address.'),
            'ipv4' =>__t('The :attribute must be a valid IPv4 address.'),
            'ipv6' =>__t('The :attribute must be a valid IPv6 address.'),
            'json' =>__t('The :attribute must be a valid JSON string.'),
            'lt' => [
                'numeric' =>__t('The :attribute must be less than :value.'),
                'file' =>__t('The :attribute must be less than :value kilobytes.'),
                'string' =>__t('The :attribute must be less than :value characters.'),
                'array' =>__t('The :attribute must have less than :value items.'),
            ],
            'lte' => [
                'numeric' =>__t('The :attribute must be less than or equal :value.'),
                'file' =>__t('The :attribute must be less than or equal :value kilobytes.'),
                'string' =>__t('The :attribute must be less than or equal :value characters.'),
                'array' =>__t('The :attribute must not have more than :value items.'),
            ],

            'mimes' =>__t('The :attribute must be a file of type: :values.'),
            'mimetypes' =>__t('The :attribute must be a file of type: :values.'),

            'not_in' =>__t('The selected :attribute is invalid.'),
            'not_regex' =>__t('The :attribute format is invalid.'),
            'password' =>__t('The password is incorrect.'),
            'present' =>__t('The :attribute field must be present.'),
            'regex' =>__t('The :attribute format is invalid.'),
            'required_if' =>__t('The :attribute field is required when :other is :value.'),
            'required_unless' =>__t('The :attribute field is required unless :other is in :values.'),
            'required_with' =>__t('The :attribute field is required when :values is present.'),
            'required_with_all' =>__t('The :attribute field is required when :values are present.'),
            'required_without' =>__t('The :attribute field is required when :values is not present.'),
            'required_without_all' =>__t('The :attribute field is required when none of :values are present.'),
            'same' =>__t('The :attribute and :other must match.'),
            'size' => [
                'numeric' =>__t('The :attribute must be :size.'),
                'file' =>__t('The :attribute must be :size kilobytes.'),
                'string' =>__t('The :attribute must be :size characters.'),
                'array' =>__t('The :attribute must contain :size items.'),
            ],
            'starts_with' =>__t('The :attribute must start with one of the following: :values.'),
            'string' =>__t('The :attribute must be a string.'),
            'timezone' =>__t('The :attribute must be a valid zone.'),
            'uuid' =>__t('The :attribute must be a valid UUID.'),





        ];
    }
    /**
     * @param string $entityKey
     * @return  array
     * @desc  return different messages based on entity key
     */
    // TODO : refactor to classes and a mapper ? also can we add column for custom validation messages ?
    public static function EntityCustomValidationMessages(string $entityKey) : array
    {
        return match ($entityKey){
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => [
                'unique' =>__t('This :attribute already exists'),
            ],
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
                'date_from.after_or_equal' =>__t('You cannot enter a start date in the past'),
                'date_to.after_or_equal' =>__t('The end date should be greather than or equal the start date'),
            ],
            EntityKeyTypesUtil::ENTITY_DOCUMENT => [
                'expiry_date.after_or_equal' =>__t("You cannot enter an expiry date in the past"),
            ],
            default => []
        };
    }
}
