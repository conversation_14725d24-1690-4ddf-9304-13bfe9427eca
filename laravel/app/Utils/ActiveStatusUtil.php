<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 19/06/19
 * Time: 01:13 م
 */

namespace App\Utils;


class ActiveStatusUtil
{
    const ACTIVE = 1;
    const IN_ACTIVE = 0;

    public static function getStatusList($status = null)
    {
        $statuses = [
            self::ACTIVE => __t('Active'),
            self::IN_ACTIVE => __t('Inactive'),
        ];

        if ($status !== null) {
            return $statuses[$status];
        } else {
            return $statuses;
        }
    }

    /**
     * @param null $status
     * @return array|mixed|null
     */
    public static function getStatusClassColors($status = null)
    {
        $statues = [
            self::ACTIVE => 'success',
            self::IN_ACTIVE => 'inactive'
        ];

        if (!is_null($status)) {
            return $statues[$status] ?? null;
        }
        return $statues;
    }

    public static function getStatuOptions(): array
    {
        return [
            [
                'label' => __t('Active'),
                'value' => self::ACTIVE,
            ],
            [
                'label' => __t('Inactive'),
                'value' => self::IN_ACTIVE,
            ],
        ];
    }
}
