<?php

namespace App\Utils;

/**
 * PostTypesUtil Class post types
 * @package App\Utils
 * <AUTHOR> <<EMAIL>>
 */
class PostTypesUtil
{
    /**
     * @const PostTypesUtil::CLIENT_TYPE post client type
     */
    const CLIENT_TYPE = 1;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post invoice type
     */
    const INVOICE_TYPE = 2;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post estimate type
     */
    const ESTIMATE_TYPE = 3;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post po type
     */
    const PO_TYPE = 4;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post staff type
     */
    const STAFF_TYPE = 5;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post supplier type
     */
    const SUPPLIER_TYPE = 6;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post product type
     */
    const PRODUCT_TYPE = 7;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post work order type
     */
    const WORK_ORDER_TYPE = 8;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post appointment type
     */
    const APPOINTMENT_TYPE = 9;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post sift type
     */
    const SHIFT_TYPE = 10;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post booking type
     */
    const BOOKING_TYPE = 11;

    /**
     * @const PostTypesUtil::CLIENT_TYPE post booking type
     */
    const PRODUCT_TRACKING_TYPE = 12;

    /**
     * @const PostTypesUtil::BOOKING_ITEM_TYPE post booking type
     */
    const BOOKING_ITEM_TYPE = 14;

    /**
     * get all post types
     * @return array
     */
    static public function getPostTypes()
    {
        return [
            self::CLIENT_TYPE,
            self::INVOICE_TYPE,
            self::ESTIMATE_TYPE,
            self::PO_TYPE,
            self::STAFF_TYPE,
            self::SUPPLIER_TYPE,
            self::PRODUCT_TYPE,
            self::WORK_ORDER_TYPE,
            self::APPOINTMENT_TYPE,
            self::SHIFT_TYPE,
            self::BOOKING_TYPE,
            self::PRODUCT_TRACKING_TYPE
        ];
    }
}
