<?php

namespace App\DTO;

/**
 * Class NotificationPreferenceDto
 *
 * A Data Transfer Object (DTO) that encapsulates the structure of a single push notification preference.
 * This class is primarily used to standardize access to and modification of a user's notification settings
 * for a given application context (e.g., leave application updates).
 *
 * Implements JsonSerializable for convenient JSON encoding (e.g., API responses).
 */
class NotificationPreferenceDto implements \JsonSerializable
{
    /**
     * @var string The unique identifier key for this notification preference (e.g., 'leave_application_notifiers').
     */
    private string $key;

    /**
     * @var string A human-readable title or description of this preference.
     */
    private string $title;

    /**
     * @var int The current state/value of this preference (1 = enabled, 0 = disabled).
     */
    private int $value;

    /**
     * NotificationPreferenceDto constructor.
     *
     * @param array $attributes An associative array containing 'key', 'title', and 'value'.
     */
    public function __construct(
        array $attributes
    ) {
        $this->key = $attributes['key'] ?? '';
        $this->title = $attributes['title'] ?? '';
        $this->value = $attributes['value'] ?? 1;
    }

    /**
     * Get the key for the notification preference.
     *
     * @return string
     */
    public function getKey(): string
    {
        return $this->key;
    }

    /**
     * Get the title/description for the notification preference.
     *
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * Get the current value/state of the preference (1 = enabled, 0 = disabled).
     *
     * @return int
     */
    public function getValue(): int
    {
        return $this->value;
    }

    /**
     * Set the key for this preference.
     *
     * @param string $key
     * @return $this
     */
    public function setKey(string $key): self
    {
        $this->key = $key;
        return $this;
    }

    /**
     * Set the title/description of this preference.
     *
     * @param string $title
     * @return $this
     */
    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    /**
     * Set the value/state of this preference.
     *
     * @param int $value
     * @return $this
     */
    public function setValue(int $value): self
    {
        $this->value = $value;
        return $this;
    }

    public function jsonSerialize(): mixed
    {
        return get_object_vars($this);
    }
}
