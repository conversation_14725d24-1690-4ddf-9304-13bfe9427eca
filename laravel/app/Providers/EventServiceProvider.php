<?php

namespace App\Providers;

use App\Events\ActivityLogs\ActivityLogAddEvent;
use App\Events\ActivityLogs\ActivityLogDeleteEvent;
use App\Events\ActivityLogs\ActivityLogUpdateEvent;
use App\Events\AgreementInstallments\ActivityLogs\AgreementInstallmentsUpdateEvent;
use App\Events\Caching\CacheEntities;
use App\Events\Commissions\CommissionCalculatedEvent;
use App\Events\Commissions\CommissionSheetUpdatedEvent;
use App\Events\CreditCharges\AddCreditChargeEvent;
use App\Events\CreditCharges\EditCreditChargeEvent;
use App\Events\CreditUsages\AddCreditUsageEvent;
use App\Events\CreditUsages\CreditUsageDeletedEvent;
use App\Events\CreditUsages\EditCreditUsageEvent;
use App\Events\ExpenseDistribution\UpdateExpenseDistributionRelatedDataEvent;
use App\Events\InvalidateNotificationCache;
use App\Events\InvoiceAgreements\InstallmentUpdatedEvent;
use App\Events\InvoiceAgreements\InvoiceInstallmentAgreementCreatedEvent;
use App\Events\InvoiceAgreements\InvoiceInstallmentAgreementUpdatedEvent;
use App\Events\JournalEntityCreated;
use App\Events\Loans\Installments\InstallmentPaidEvent;
use App\Events\Loans\Installments\InstallmentUnPaidEvent;
use App\Events\Loans\LoanCreatedEvent;
use App\Events\Loans\LoanDeletedEvent;
use App\Events\Loans\LoanUpdatedEvent;
use App\Events\NewDayCalculationEvent;
use App\Events\NotificationDispatcherEvent;
use App\Events\PayableChequeSavedEvent;
use App\Events\PayRunPaymentDeletedEvent;
use App\Events\PayRunPaymentSavedEvent;
use App\Events\PayslipDeletedEvent;
use App\Events\PayslipsApprovedEvent;
use App\Events\PayslipsUnApprovedEvent;
use App\Events\PurchaseOrderDeleted;
use App\Events\PurchaseQuotationDeleted;
use App\Events\ReceivableChequeSavedEvent;
use App\Events\ServiceFormDeleted;
use App\Events\StaffSavedEvent;
use App\Listeners\ActivityLogs\ActivityLogAddListener;
use App\Listeners\ActivityLogs\ActivityLogDeleteListener;
use App\Listeners\ActivityLogs\ActivityLogUpdateListener;
use App\Listeners\AgreementInstallments\ActivityLogs\AgreementInstallmentsUpdateListener;
use App\Listeners\Caching\CacheEntitiesListener;
use App\Listeners\ClearEntityPermissionListener;
use App\Listeners\Commissions\CommissionSheetTargetListener;
use App\Listeners\Commissions\CommissionSheetUpdateListener;
use App\Listeners\CreateJournalFromEntity;
use App\Listeners\CreatePayRunPaymentExpenseListener;
use App\Listeners\CreditCharges\ChangeCreditChargeStatusListener;
use App\Listeners\CreditUsages\ChangeCreditChargeStatusForCreditUsagesListener;
use App\Listeners\DayCalculationListener;
use App\Listeners\DeletePayRunPaymentExpenseListener;
use App\Listeners\DeletePurchaseOrderTaxes;
use App\Listeners\ExpenseDistribution\UpdateExpenseDistributionRelatedDataListener;
use App\Listeners\InvoiceAgreements\AddInvoiceAgreementInstallmentsListener;
use App\Listeners\InvoiceAgreements\UpdateInvoiceAgreementDueInstallmentIDListener;
use App\Listeners\InvoiceAgreements\UpdateInvoiceAgreementStatusListener;
use App\Listeners\InvoiceAgreements\UpdateInvoicePaymentDaysListeners;
use App\Listeners\Loans\AddLoanExpenseListener;
use App\Listeners\Loans\AddLoanInstallmentsListener;
use App\Listeners\Loans\DeleteLoanExpenseListener;
use App\Listeners\Loans\DeleteLoanInstallmentsListener;
use App\Listeners\Loans\Installments\DeleteInstallmentUnPaidJournal;
use App\Listeners\Loans\Installments\GenerateInstallmentPaidJournal;
use App\Listeners\Loans\UpdateLoanExpenseListener;
use App\Listeners\Loans\UpdatePayslipInstallmentsStatusListener;
use App\Listeners\NotificationDispatcherListener;
use App\Listeners\NotificationSentListener;
use App\Listeners\SavePayableChequeJournalListener;
use App\Listeners\SaveReceivableChequeJournalListener;
use App\Listeners\ServiceFormDeleteListener;
use App\Listeners\UpdateNotificationCache;
use App\Listeners\UpdatePayRunJournalListener;
use App\Listeners\UpdatePayrunStatusListener;
use App\Listeners\UpdatePurchaseQuotationStatus;
use App\Listeners\UpdateQuotationRequest;
use App\Models\PriceList;
use App\Models\PriceListItems;
use App\Observers\PriceListItemsObserver;
use App\Observers\PriceListObserver;
use App\Queue\Listeners\ClearEntityCacheListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Notifications\Events\NotificationSent;
use App\Events\Workflow\WorkflowPostCreatedEvent;
use App\Events\Workflow\WorkflowPostUpdatedEvent;
use App\Listeners\MenuItemCacheListener;
use App\Listeners\WorkflowPostListener;
use App\Models\ShippingOption;
use App\Observers\ShippingOptionsObserver;

/**
 * Class EventServiceProvider
 * @package App\Providers
 * <AUTHOR> Azzam <<EMAIL>>
 */
class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        StaffSavedEvent::class => [
            ClearEntityPermissionListener::class
        ],
        JournalEntityCreated::class => [
            CreateJournalFromEntity::class
        ],
        NewDayCalculationEvent::class => [
            DayCalculationListener::class
        ],
        PayslipsApprovedEvent::class => [
            UpdatePayrunStatusListener::class,
            UpdatePayRunJournalListener::class
        ],
        PayslipDeletedEvent::class => [
            UpdatePayrunStatusListener::class
        ],
        PayRunPaymentSavedEvent::class => [
            UpdatePayrunStatusListener::class,
            CreatePayRunPaymentExpenseListener::class
        ],
        PayRunPaymentDeletedEvent::class => [
            DeletePayRunPaymentExpenseListener::class,
            UpdatePayrunStatusListener::class
        ],
        PayslipsUnApprovedEvent::class => [
            UpdatePayrunStatusListener::class,
            UpdatePayRunJournalListener::class,
            UpdatePayslipInstallmentsStatusListener::class
        ],
        PayableChequeSavedEvent::class => [
            SavePayableChequeJournalListener::class
        ],
        ReceivableChequeSavedEvent::class => [
            SaveReceivableChequeJournalListener::class
        ],
        LoanCreatedEvent::class => [
            AddLoanExpenseListener::class,
            AddLoanInstallmentsListener::class
        ],
        LoanDeletedEvent::class => [
            DeleteLoanExpenseListener::class,
            DeleteLoanInstallmentsListener::class,
        ],
        LoanUpdatedEvent::class => [
            UpdateLoanExpenseListener::class,
            DeleteLoanInstallmentsListener::class,
            AddLoanInstallmentsListener::class
        ],
        InstallmentPaidEvent::class => [
            GenerateInstallmentPaidJournal::class
        ],
        InstallmentUnPaidEvent::class => [
            DeleteInstallmentUnPaidJournal::class
        ],
        AddCreditChargeEvent::class => [
            ChangeCreditChargeStatusListener::class
        ],
        EditCreditChargeEvent::class => [
            ChangeCreditChargeStatusListener::class
        ],
        AddCreditUsageEvent::class => [
            ChangeCreditChargeStatusForCreditUsagesListener::class
        ],
        EditCreditUsageEvent::class => [
            ChangeCreditChargeStatusForCreditUsagesListener::class
        ],
        CreditUsageDeletedEvent::class => [
            ChangeCreditChargeStatusForCreditUsagesListener::class
        ],
        CacheEntities::class => [
            CacheEntitiesListener::class
        ],
        // activity log listeners
        ActivityLogAddEvent::class => [
            ActivityLogAddListener::class
        ],
        ActivityLogUpdateEvent::class => [
            ActivityLogUpdateListener::class
        ],
        ActivityLogDeleteEvent::class => [
            ActivityLogDeleteListener::class
        ],
        InvoiceInstallmentAgreementCreatedEvent::class => [
            AddInvoiceAgreementInstallmentsListener::class,
            UpdateInvoiceAgreementStatusListener::class,
            UpdateInvoiceAgreementDueInstallmentIDListener::class,
            UpdateInvoicePaymentDaysListeners::class,
        ],
        InvoiceInstallmentAgreementUpdatedEvent::class => [
            AddInvoiceAgreementInstallmentsListener::class,
            UpdateInvoiceAgreementStatusListener::class,
            UpdateInvoiceAgreementDueInstallmentIDListener::class,
            UpdateInvoicePaymentDaysListeners::class
        ],
        InstallmentUpdatedEvent::class => [
            UpdateInvoiceAgreementStatusListener::class,
            UpdateInvoiceAgreementDueInstallmentIDListener::class,
            UpdateInvoicePaymentDaysListeners::class
        ],
        AgreementInstallmentsUpdateEvent::class => [
            AgreementInstallmentsUpdateListener::class
        ],
        NotificationDispatcherEvent::class =>[
            NotificationDispatcherListener::class
        ],
        CommissionCalculatedEvent::class => [
            CommissionSheetUpdateListener::class
        ],
        CommissionSheetUpdatedEvent::class => [
            CommissionSheetTargetListener::class
        ],
        UpdateExpenseDistributionRelatedDataEvent::class => [
            UpdateExpenseDistributionRelatedDataListener::class
        ],
        InvalidateNotificationCache::class => [
            UpdateNotificationCache::class
        ],
        NotificationSent::class => [
          NotificationSentListener::class
        ],
        PurchaseQuotationDeleted::class => [
            UpdateQuotationRequest::class,
            DeletePurchaseOrderTaxes::class
        ],
        PurchaseOrderDeleted::class => [
            UpdatePurchaseQuotationStatus::class,
            DeletePurchaseOrderTaxes::class
        ],
        WorkflowPostCreatedEvent::class => [
            WorkflowPostListener::class,
            MenuItemCacheListener::class,
        ],
        WorkflowPostUpdatedEvent::class => [
            WorkflowPostListener::class,
            MenuItemCacheListener::class,
        ],
        ServiceFormDeleted::class => [
            ServiceFormDeleteListener::class,
            ClearEntityCacheListener::class

        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
        PriceList::observe(PriceListObserver::class);
		PriceListItems::observe(PriceListItemsObserver::class);
        ShippingOption::observe(ShippingOptionsObserver::class);
    }
}
