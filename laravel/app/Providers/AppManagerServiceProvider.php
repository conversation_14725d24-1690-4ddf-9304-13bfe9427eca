<?php

namespace App\Providers;

use App\Cache\CacheDriver;
use App\Queue\Listeners\AppTriggerHandler;
use App\Repositories\AppSiteRepository;
use App\Repositories\Interfaces\AppSiteRepositoryInterface;
use App\Services\Session;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\AppManager\Repositories\Cache\AppButtonRepository;
use Izam\Daftra\AppManager\Repositories\Cache\AppEntityRepository;
use Izam\Daftra\AppManager\Repositories\Cache\AppRepository;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppButtonRepositoryInterface;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppEntityRepositoryInterface;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppRepositoryInterface;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppTriggerRepositoryInterface;
use Izam\Daftra\AppManager\Services\ButtonService;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\ButtonServiceErrorHandlerDecorator;
use Izam\Daftra\AppManager\Services\Interfaces\AppEntitiesFormHandlerInterface;
use Izam\Daftra\AppManager\Services\Interfaces\ButtonServiceInterface;
use Izam\Daftra\AppManager\Services\UpdateEntityService;
use Izam\Daftra\Common\EntityStructure\CacheDriverInterface;
use Izam\Daftra\Common\Session\SessionInterface;
use Izam\Entity\LocalEntityForm\AppEntitiesFormService;
use Psr\Http\Client\ClientInterface;
use Illuminate\Support\Facades\Blade;
use Izam\Daftra\AppManager\Repositories\PageScriptRepository;
use Izam\Daftra\AppManager\Repositories\Cache\PageScriptRepository as CachedPageScriptRepository;
use Izam\Daftra\AppManager\Repositories\Interfaces\PageScriptRepositoryInterface;
use App\Repositories\PluginRepository;
use Izam\Daftra\AppManager\Repositories\PluginRepositoryInterface;
use Throwable;

class AppManagerServiceProvider extends \Illuminate\Support\ServiceProvider
{
    public function register()
    {
        /** @todo remove this, and depend on package interface */
        $this->app->bind(
            AppSiteRepositoryInterface::class,
            AppSiteRepository::class
        );

        $this->app->bind(ClientInterface::class, function ($app) {
            return new Client();
        });

        $this->app->bind(CacheDriverInterface::class, function ($app) {
            return new CacheDriver();
        });

        $this->app->bind(AppRepositoryInterface::class, function ($app) {
            return $this->app->get(AppRepository::class);
        });

        $this->app->when([AppEntityRepository::class])
            ->needs(AppEntityRepositoryInterface::class)
            ->give(function () {
                return new \Izam\Daftra\AppManager\Repositories\AppEntityRepository();
            });

        $this->app->bind(AppEntityRepositoryInterface::class, function ($app) {
            return $app->get(AppEntityRepository::class);
        });

        $this->app->bind(PluginRepositoryInterface::class, function ($app) {
            return $app->get(PluginRepository::class);
        });

        $this->app->when([CachedPageScriptRepository::class])
            ->needs(PageScriptRepositoryInterface::class)
        ->give(function () {
            return new PageScriptRepository($this->app->get(PluginRepository::class));
        });

        $this->app->bind(PageScriptRepositoryInterface::class, function ($app) {
            return $app->get(CachedPageScriptRepository::class);
        });

        $this->app->bind(\Izam\Daftra\AppManager\Repositories\Interfaces\AppSiteRepositoryInterface::class, function ($app) {
            return $this->app->get(\Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository::class);
        });

        $this->app->when([AppTriggerHandler::class])
            ->needs(AppTriggerRepositoryInterface::class)
            ->give(function () {
                return new \Izam\Daftra\AppManager\Repositories\Cache\AppTriggerRepository();
            });

        $this->app->when([\Izam\Daftra\AppManager\Repositories\Cache\AppTriggerRepository::class])
            ->needs(AppTriggerRepositoryInterface::class)
            ->give(function () {
                return new \Izam\Daftra\AppManager\Repositories\AppTriggerRepository();
            });

        $this->app->bind(AppButtonRepositoryInterface::class, function ($app) {
            return $this->app->get(AppButtonRepository::class);
        });

        $this->app->when([ButtonServiceErrorHandlerDecorator::class])
            ->needs(ButtonServiceInterface::class)
            ->give(function () {
                return resolve(ButtonService::class);
            });

        $this->app->bind(SessionInterface::class, function () {
            return $this->app->get(Session::class);
        });

        $this->app->bind(AppEntitiesFormHandlerInterface::class, function () {
            return $this->app->get(AppEntitiesFormService::class);
        });
    }

    public function boot()
    {
        $this->updateAppEntities();

        Blade::directive('appPageScripts', function ($expression) {
            return '<?php
            $url = "v2/" . request()->path();

            $pageScriptService = resolve(\Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\PageScriptErrorHandlerDecorator::class);
            echo $pageScriptService->getByUrl($url);
            ?>';
        });
    }

    private function updateAppEntities()
    {
        try {
            /** @var UpdateEntityService $service */
            $service = resolve(UpdateEntityService::class);
            $service->syncAppsEntities();
        } catch (Throwable $e) {
            Log::error($e);
        }
    }
}
