<?php

namespace App\Providers;

use App\Http\Controllers\MachineController;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //

        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();

        //
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware('web')
             ->namespace($this->namespace)
             ->group(function ($route) {
                 require base_path('routes/web.php');
                 require base_path('routes/attendanceRoutes.php');
                 require base_path('routes/payrollRoutes.php');
                 require base_path('routes/chequeCycleRoutes.php');
                 require base_path('routes/productTrackingRoutes.php');
                 require base_path('routes/productRoutes.php');
                 require base_path('routes/priceListRoutes.php');
                 require base_path('routes/loansRoutes.php');
                 require base_path('routes/requestRoutes.php');
                 require base_path('routes/creditRoutes.php');
                 require base_path('routes/exportRoutes.php');
                 require base_path('routes/membershipRoutes.php');
                 require base_path('routes/clientAttendanceRoutes.php');
                 require base_path('routes/shopFrontRoutes.php');
                 require base_path('routes/shippingOptionsRoutes.php');
                 require base_path('routes/invoiceAgreementInstallmentRoutes.php');
                 require base_path('routes/commissionsRoutes.php');
                 require base_path('routes/stocktakingRoutes.php');
                 require base_path('routes/dashboardRoutes.php');
	             require base_path('routes/expenseDistributionRoutes.php');
	             require base_path('routes/workOrderRoutes.php');
	             require base_path('routes/workFlowRoutes.php');
	             require base_path('routes/tags.php');
	             require base_path('routes/accountingRoutes.php');
	             require base_path('app/Modules/ElectronicInvoice/routes.php');
                 require base_path('routes/followUpRoutes.php');
                 require base_path('routes/postRoutes.php');
                 require base_path('routes/banks.php');
                 require base_path('app/Modules/ElectronicInvoice/routes.php');
                 require base_path('app/Modules/KSAElectronicInvoice/routes.php');
                 require base_path('routes/diagnosticsRoutes.php');
                 require base_path('routes/crmRoutes.php');
                 require base_path('routes/fixRoutes.php');
                 require base_path('routes/settings.php');
                 require base_path('routes/leaveApplicationRoutes.php');
                 require base_path('routes/stockRequestsRoutes.php');
                 require base_path('routes/salesRoutes.php');
                 require base_path('routes/manufacturingRoutes.php');
                 require base_path('routes/followUpStatusRoutes.php');
                 require base_path('routes/invoiceRoutes.php');
                 require base_path('routes/bookingRoutes.php');
                 require base_path('routes/emailPreferencesRoutes.php');
             });
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
             ->middleware('api')
             ->namespace($this->namespace)
             ->group(function ($route) {
	             require base_path('routes/api.php');
	             require base_path('routes/mobileApiRoutes.php');
                 require base_path('routes/desktopRoutes.php');
                 require base_path('app/Modules/ElectronicInvoice/api.php');
                 require base_path('app/Modules/KSAElectronicInvoice/api.php');
                 require base_path('routes/implementation.php');
             });
    }
}
