<?php
namespace App\Modules\KSAElectronicInvoice\Retriever;

use App\Facades\CurrencyConverter;
use App\Modules\ElectronicInvoice\ConfigurationRetriever\Interfaces\InvoiceDataInterface;
use App\Modules\KSAElectronicInvoice\DTO\Invoice\TaxTotalDTO;
use Carbon\Carbon;

class InvoiceDataRetriever implements InvoiceDataInterface
{

    private $invoice;
    private $invoiceItems;
    private $invoiveItemsRetrievers;
    public function __construct($invoice)
    {
        $this->invoice = $invoice;
        $this->invoiceItems = $invoice->invoiceItems;
        $this->invoiveItemsRetrievers = [];
        foreach ($this->invoiceItems as $item){
            $this->invoiveItemsRetrievers[] = new InvoiceItemDataRetriever($item);
        }

    }

    public function getInternalID(){
        return $this->invoice->no;
    }

    public function getPurchaseOrderReference(){
        return $this->invoice->id;
    }

    public function getSalesOrderReference(){
        return $this->invoice->id;
    }

    public function getProformaInvoiceNumber(){
        return $this->invoice->id;
    }

    public function getIssuedDate(){

        $created = Carbon::parse($this->invoice->created);
        $dateColumn = $this->invoice->issue_date ?? $this->invoice->date;
        if ($this->invoice->issue_date == "0000-00-00 00:00:00" || Carbon::parse($this->invoice->issue_date)->year === 1970) {
            $dateColumn = $this->invoice->date;
        }
        $date = (!empty($dateColumn)) ? Carbon::createFromFormat("Y-m-d", $dateColumn)->setHour($created->format('H'))
            ->setMinute($created->format('i'))->setSecond($created->format('s')) : $created;
        $date->setTimezone('UTC');
        return $date;
    }


    public function getAdvancePayments()
    {
        return $this->invoice->relatedInvoices;
    }

    public function getInvoiceLines(): array
    {
        return $this->invoiveItemsRetrievers;
    }

    public function getTotalSalesAmount()
    {
        $total = 0;

        foreach ($this->invoiveItemsRetrievers as $invoiceData){
            $total += $invoiceData->getLineSalesTotal();
        }
        return round($total, 4);
    }

    public function getAdjustmentLabel()
    {
        return $this->invoice->adjustment_label;

    }

    public function getAdjustmentValue()
    {
        $adjustments = [];


        if (abs((float) $this->invoice->adjustment_value)){
            $adjustments[] = [
                "value" => round($this->invoice->adjustment_value * $this->getCurrencyExchangeRate(), 4),
                "label" => $this->invoice->adjustment_label,
                ];

        }
        $extraData = json_decode($this->invoice->extra_details, true);
        if (!empty($extraData['invoice_accounts'])){
            $account = array_filter($extraData['invoice_accounts'], function ($acc){
                return abs((double) $acc['value']);
            });
            foreach ($account as $account){

                $adjustments[] = [
                    "value" => round($account['value'] * $this->getCurrencyExchangeRate(), 4),
                    "label" => $account['label'],
                ];
            }
        }
        return $adjustments;
    }

    public function getTotalDiscountAmount()
    {
        $total = 0;

        foreach ($this->invoiveItemsRetrievers as $invoiceData){
            $total += $invoiceData->getLineDiscount()->getAmount();
        }
        $total = round($total, 4);
        if ($this->invoice->adjustment_value && round($this->invoice->adjustment_value, 4) < 0) {
            $total+= abs(round($this->invoice->adjustment_value * $this->getCurrencyExchangeRate(), 4));
        }
        return round($total, 4);
    }

    public function getNetAmount()
    {
        $total = 0;

        foreach ($this->invoiveItemsRetrievers as $invoiceData){
            $total += $invoiceData->getLineNetTotal();
        }

        if ($this->getShippingFees()) {
           $total+= $this->getShippingFees();
        }

        return round($total, 4);
    }

    public function getTaxTotals() : array
    {
        $taxesTotal = [];
        $taxes = [];

        foreach ($this->invoice->invoiceItems as $item) {
            $invoiceItemRetriver = new InvoiceItemDataRetriever($item);
            foreach ($invoiceItemRetriver->getTaxableItems() as $tax) {
                $taxes[$tax->getId()]= ['amount' => (round($tax->getAmount(), 2) + ($taxes[$tax->getId()]['amount'] ?? 0)), 'rate' => $tax->getTaxRate(), 'key' => $tax->getTaxType()];
            }
       }


        if ($this->invoice->shipping_tax_id && $this->invoice->shipping_amount) {
            $taxId = $this->invoice->shipping_tax_id;
            $taxes[$taxId] = ['amount' => ($this->getShippingTaxAmount() + ($taxes[$taxId]['amount'] ?? 0)), 'rate' => $this->getShippingTaxPercent(), 'key' => 'tax'];
        }

        foreach ($taxes as $tax) {
            $taxesTotal[] = new TaxTotalDTO($tax['rate'], round($tax['amount'], 4), $tax['key']);
        }
        return $taxesTotal;
    }


    /**
     * @param int $number
     * @return float
     */
    public function getTaxTotalBasedOnPrecision(int $number) : float
    {
        $total = 0;
        foreach ($this->getTaxTotals() as $tax) {
            $total+= round($tax->getAmount(), $number);
        }
        return $total;

    }

    public function getExtraDiscountAmount()
    {
        return 0;
        return $this->invoice->summary_discount;
    }

    public function getTotalItemsDiscountAmount()
    {
        return 0;
    }

    public function getTotalAmount()
    {
        return $this->invoice->summary_total;
    }

    public function getSignatures(): array
    {
        // TODO: Implement getSignatures() method.
    }

    public function getInvoice()
    {
        return $this->invoice;
    }

    public function getCurrencyCode()
    {
        return $this->invoice->currency_code;
    }

    public function getShippingFees()
    {
        if ($this->invoice->shipping_tax && $this->invoice->shipping_tax->included) {
            $amount =  ($this->invoice->shipping_amount / (100 + $this->invoice->shipping_tax->value)) * 100;
            return  round($amount *  $this->getCurrencyExchangeRate(), 4);
        }
        return round($this->invoice->shipping_amount  * $this->getCurrencyExchangeRate(), 4);
    }

    public function getShippingTaxPercent()
    {
        return $this->invoice->shipping_tax->value ?? 0;
    }

    public function getShippingTaxName()
    {
        return $this->invoice->shipping_tax->name ?? "";
    }

    /**
     * @return mixed
     */
    public function getShippingTaxAmount()
    {
        return round((($this->getShippingTaxPercent() / 100) * $this->getShippingFees()), 4);
    }

    public function getCurrencyExchangeRate()
    {
        return round(CurrencyConverter::convert($this->getCurrencyCode(), "SAR", $this->getIssuedDate()), 4);
    }
}
