<?php

namespace App\Modules\ElectronicInvoice\Strategy;

use App\Jobs\SendETARequest;
use App\Modules\ElectronicInvoic\Validation\SubmitReceipt;
use App\Modules\ElectronicInvoice\Builder\BuildParamsForReceipt;
use App\Modules\ElectronicInvoice\DTO\Interfaces\DocumentDTOInterface;
use App\Modules\ElectronicInvoice\DTO\Interfaces\Receipt\ReceiptDTOInterface;
use App\Modules\ElectronicInvoice\Strategy\Interfaces\GenerateElectronicDocumentInterface;

class StrategyGenerateEgyptianReceipt extends AbstractElectronicInvoice implements GenerateElectronicDocumentInterface
{

    protected ReceiptDTOInterface $receiptDTO;



    public function submitDocument(DocumentDTOInterface $receiptDTO, $id): bool {
        $this->receiptDTO = $receiptDTO;
        $url = $this->urlManager::SYS_API . '/receiptsubmissions';
        $paramsBuilder = new BuildParamsForReceipt();
        $params = $paramsBuilder->build($receiptDTO, $id);
        $submitDocumentValidator = app(SubmitReceipt::class, ['receiptData' => $params, 'id' => $id]);
        if (!$submitDocumentValidator->validate()) {
            $this->messages = $submitDocumentValidator->getMessages();
            return false;
        }
        unset($params['signatures']);
        return (new SendETARequest($url, $params, [], $id))->handle();
    }
}
