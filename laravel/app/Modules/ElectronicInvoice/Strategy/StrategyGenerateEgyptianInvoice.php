<?php

namespace App\Modules\ElectronicInvoice\Strategy;

use App\Facades\Settings;

use App\Jobs\SendETARequest;
use App\Modules\ElectronicInvoic\Validation\SubmitDocument;
use App\Modules\ElectronicInvoice\DTO\Interfaces\DocumentDTOInterface;
use App\Modules\ElectronicInvoice\Strategy\Interfaces\GenerateElectronicDocumentInterface;
use Izam\Daftra\Common\Utils\PluginUtil;

class StrategyGenerateEgyptianInvoice extends AbstractElectronicInvoice implements GenerateElectronicDocumentInterface
{

    public function submitDocument(DocumentDTOInterface $invoice_data, $id): bool
    {
        $this->invoice_data = $invoice_data;
        $url = $this->urlManager::SYS_API . '/documentsubmissions';
        $params['documents'][] = $this->invoice_data->toArray();
        $submitDocumentValidator = app(SubmitDocument::class, ['invoiceData' => $params['documents'][0], 'id' => $id]);
        if (!$submitDocumentValidator->validate()) {
            $this->messages = $submitDocumentValidator->getMessages();
            return false;
        }
        if (!Settings::getValue(PluginUtil::ETA_PLUGIN, 'use_signature')) {
            unset($params['documents'][0]['signatures']);
        }
        return (new SendETARequest($url, $params, [], $id))->handle();
    }
}
