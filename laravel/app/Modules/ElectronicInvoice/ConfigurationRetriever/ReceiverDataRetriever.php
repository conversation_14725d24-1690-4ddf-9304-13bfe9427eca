<?php
namespace App\Modules\ElectronicInvoice\ConfigurationRetriever;

use App\Modules\ElectronicInvoice\ConfigurationRetriever\Interfaces\ReceiverDataInterface;
use App\Repositories\ClientRepository;

class ReceiverDataRetriever implements ReceiverDataInterface
{

    private $client;
    private $invoice;
    private $address;
    public function __construct($invoice)
    {
        $clientRepo = resolve(ClientRepository::class);
        $this->invoice = $invoice;
        $this->client = $clientRepo->find($invoice->client_id);
        $this->address = $this->getAddress();
    }

    public function getAddress()
    {
        $address = '-';
        if(isset($this->client['address1']) && $this->client['address1'] != ''){
            $address = $this->client['address1'];
        }elseif(isset($this->client['address2']) && $this->client['address2'] != ''){
            $address = $this->client['address2'];
        }
        return $address;
    }

    public function getReceiverType()
    {
        if ($this->getReceiverCountry() != "EG") {
            return "F";
        }
        return $this->client->type == 2 ? "P":"B";
    }

    public function getReceiverRegistrationId()
    {
        if ($this->getReceiverType() == "F") {
            return $this->client->bn1;
        }
        return ($this->client->type == 2) ?  $this->client->national_id : $this->client->bn2;
    }

    public function getMobileNumber()
    {
        $phone =  ($this->client->phone1) ? $this->client->phone1 : $this->client->phone2;
        if (empty($phone)) {
            $phone = "";
        }
        return $phone;
    }

    public function getReceiverName()
    {
        return !empty($this->client->business_name)? $this->client->business_name : ($this->client->first_name." ".$this->client->first_name);
    }

    public function getReceiverCountry()
    {
        return !empty($this->client->country_code) ? $this->client->country_code : "EG";
    }

    public function getReceiverGovernorate()
    {
        return !empty($this->client->city) ? $this->client->city : "-";
    }

    public function getReceiverRegionCity()
    {
        return !empty($this->client->state) ? $this->client->state : "-";
    }

    public function getReceiverStreet()
    {
        return !empty($this->address) ?  $this->address : "-";
    }

    public function getReceiverBuildingNumber()
    {
        $buildingNumber = substr($this->address, 0, strspn($this->address, "0123456789"));
        return $buildingNumber?: "-";
    }

    public function getPostalCode(){
        return is_null($this->client->postal_code) ? "" : $this->client->postal_code;
    }
}
