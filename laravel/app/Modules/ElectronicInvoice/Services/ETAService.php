<?php

namespace App\Services;


use App\Facades\Settings;
use App\Jobs\GetETADocument;
use App\Modules\ElectronicInvoice\Builder\InvoiceDTOFactory;
use App\Modules\ElectronicInvoice\Strategy\GenerateElectronicInvoiceStrategyFactory;
use App\Repositories\CustomFormFieldRepository;
use App\Repositories\EntityAppDataRepository;
use App\Repositories\InvoiceRepository;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use Izam\Daftra\Common\ETA\ErrorMapper;
use Izam\Daftra\Common\Utils\ElectronicInvoicesActionsUtil;
use Izam\Daftra\Common\Utils\ElectronicInvoicesStatus;
use Izam\Daftra\Common\Utils\ElectronicInvoicesStatusUtil;
use Izam\Daftra\Common\Utils\EntityAppDataKeysUtil;

class ETAService extends BaseService
{


    private $invoiceRepo;
    private $customFormFieldRepo;
    private $apiErrors = [];
    private EntityAppDataRepository $entityAppDataRepo;
    private $data;

    /**
     * @param InvoiceRepository $invoiceRepository
     * @param CustomFormFieldRepository $customFormFieldRepository
     * @param EntityAppDataRepository $entityAppDataRepository
     */
    public function __construct(InvoiceRepository $invoiceRepository, CustomFormFieldRepository $customFormFieldRepository, EntityAppDataRepository $entityAppDataRepository) {
        $this->invoiceRepo = $invoiceRepository;
        $this->customFormFieldRepo = $customFormFieldRepository;
        $this->entityAppDataRepo = $entityAppDataRepository;
    }

    public function getInvoiceDTOByInvoiceId($id){
        $invoice = $this->invoiceRepo->getInvoiceById($id);
    }

    public function addSignatureToInvoice($invoice_id, $signature)
    {
        $this->addToAppEntityDataTable($invoice_id, ElectronicInvoicesActionsUtil::SIGNATURE, $signature);
    }

    public function addUuidToInvoice($invoice_id, $value)
    {
        $this->addToAppEntityDataTable($invoice_id, ElectronicInvoicesActionsUtil::UUID, $value);
    }


    private function addToAppEntityDataTable($invoice_id, $action_key, $value)
    {
        $this->entityAppDataRepo->deleteCritria(
            EntityAppDataKeysUtil::ELECTRONIC_INVOICE, $action_key,
            EntityKeyTypesUtil::INVOICE_ENTITY_KEY, $invoice_id
        );

        $this->entityAppDataRepo->insertGetId(
            [
                'entity_key'=> EntityKeyTypesUtil::INVOICE_ENTITY_KEY,
                'entity_id'=> $invoice_id,
                'app_key'=> EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
                'data'=> json_encode(['value' => $value]),
                'action_key'=> $action_key
            ]);
    }

    private function validateSettingBeforeRequest()
    {
        $item_code_tax = Settings::getValue(PluginUtil::ETA_PLUGIN, 'item_code_tax');
        $item_code_type = Settings::getValue(PluginUtil::ETA_PLUGIN, 'item_code_type');
        if (empty($item_code_tax) || empty($item_code_type)){
            $formField = $this->customFormFieldRepo->find($item_code_tax);
            $formField2 = $this->customFormFieldRepo->find($item_code_type);
            if (empty($formField) || empty($formField2)){
                $this->apiErrors = [__t('You should select the field related to Item Code Input  and Item Type Input, please check').' <a href="'.route('owner.electronic_invoice_settings').'" target="_blank">'.__t('Electronic Invoice Settings').'</a>'];
                return false;
            }
        }
        $client_id = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id');
        $client_secret = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret');

        $client_id_test = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id_test');
        $client_secret_test = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret_test');
        $is_testing = Settings::getValue(PluginUtil::ETA_PLUGIN, 'is_testing');
        if ((((empty($client_id) || empty($client_secret)) && !$is_testing) || ($is_testing && (empty($client_id_test) || empty($client_secret_test)))) && !Settings::getValue(PluginUtil::ETA_PLUGIN, 'use_receipt')) {
            $this->apiErrors = [
                __t('You should insert client id and client secret, please check').' <a href="'.route('owner.electronic_invoice_settings').'" target="_blank">'.__t('Electronic Invoice Settings').'</a>'];
            return false;
        }

        $is_testing_receipt = Settings::getValue(PluginUtil::ETA_PLUGIN, 'is_testing_receipt');
        $client_receipt_id = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id_receipt');
        $client_receipt_secret = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret_receipt');

        $client_id_receipt_test = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id_test_receipt');
        $client_secret_receipt_test = Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret_test_receipt');

        if ((((empty($client_receipt_id) || empty($client_receipt_secret)) && !$is_testing_receipt) || ($is_testing_receipt && (empty($client_id_receipt_test) || empty($client_secret_receipt_test)))) && Settings::getValue(PluginUtil::ETA_PLUGIN, 'use_receipt')) {
            $this->apiErrors = [
                __t('You should insert client id and client secret, please check').' <a href="'.route('owner.electronic_invoice_settings').'" target="_blank">'.__t('Electronic Invoice Settings').'</a>'];
            return false;
        }
        return true;
    }

    public function sendInvoice($type, $id){
        ini_set('serialize_precision', -1);
        $invoice = $this->invoiceRepo->getInvoiceByIdWithItems($id);
        $getInvoiceData = $invoice->app_entity_get_data;

        if ($invoice->draft) {

            $this->apiErrors = [__t("Invoice Is Draft"). " <a href='/owner/invoices/view/".$invoice->id."' target='_blank'>".$invoice->no."</a>"];
            return false;
        }

        if (!$this->validateSettingBeforeRequest()) {
            return false;
        }
        if (
            !empty($getInvoiceData) &&
            in_array(json_decode($getInvoiceData->data, true)['status'],  [ElectronicInvoicesStatus::VALID, ElectronicInvoicesStatus::IN_PROGRESS, ElectronicInvoicesStatus::SUBMITTED])
            ||
            empty($getInvoiceData) && !empty($invoice->app_entity_submit_data())
        ) {
            $this->apiErrors = [__t("You can't submit this document, it's already submitted"). " <a href='/owner/invoices/view/".$invoice->id."' target='_blank'>".$invoice->no."</a>"];
            return false;
        }
        $invoiceDTO = InvoiceDTOFactory::create($id,$type);
        $this->data = $invoiceDTO->toArray();
        $strategy = GenerateElectronicInvoiceStrategyFactory::getElectronicInvoiceStrategy($type);
        $result = $strategy->submitDocument($invoiceDTO, $id);
        if (!$result) {
            $this->apiErrors = $strategy->getMessages();
           return false;
        }

        $this->entityAppDataRepo->deleteCritria(
            EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
            ElectronicInvoicesActionsUtil::GET_DOCUMENT,
            EntityKeyTypesUtil::INVOICE_ENTITY_KEY,
            $id
        );
        $this->entityAppDataRepo->deleteCritria(
            EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
            ElectronicInvoicesActionsUtil::ERROR,
            EntityKeyTypesUtil::INVOICE_ENTITY_KEY,
            $id
        );

        $this->entityAppDataRepo->insertGetId(
            [
                'entity_key' => EntityKeyTypesUtil::INVOICE_ENTITY_KEY,
                'entity_id' => $id,
                'app_key' => EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
                'data' => json_encode(['date' => Carbon::now()]),
                'action_key' => ElectronicInvoicesActionsUtil::SENT_DOCUMENT
            ]
        );

        return true;

    }

    public function getInvoiceState($type,$id){

        $entityAppSubmissionData= $this->entityAppDataRepo->getDataByAppAndEntity(EntityAppDataKeysUtil::ELECTRONIC_INVOICE,ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT,EntityKeyTypesUtil::INVOICE_ENTITY_KEY,$id);
        if ($entityAppSubmissionData == null){
            $this->apiErrors = [__t('Please Submit First')];
            return false;
        }

        $raw_data = json_decode($entityAppSubmissionData->data,true);
        $isReceipt = !empty($raw_data["acceptedDocuments"][0]["receiptNumber"]);
        $uuid = $raw_data['acceptedDocuments'][0]['uuid'] ?? null;
        GetETADocument::dispatchSync( $uuid, $id, $isReceipt ,  $raw_data["submissionId"]);
        return true;

    }

    public function cancelInvoice($type,$id){
        $strategy = GenerateElectronicInvoiceStrategyFactory::getElectronicInvoiceStrategy('type');
        $entityAppSubmissionData= $this->entityAppDataRepo->getDataByAppAndEntity(EntityAppDataKeysUtil::ELECTRONIC_INVOICE,ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT,EntityKeyTypesUtil::INVOICE_ENTITY_KEY,$id);
        if ($entityAppSubmissionData == null){
            $this->apiErrors = [__t('Please Submit First')];
            return false;
        }
        $raw_data = json_decode($entityAppSubmissionData->data,true);
        $uuid = $raw_data['acceptedDocuments'][0]['uuid'];
        $result = $strategy->cancelDocument($uuid);

        $entityAppCancelDocData= $this->entityAppDataRepo->getDataByAppAndEntity(EntityAppDataKeysUtil::ELECTRONIC_INVOICE,ElectronicInvoicesActionsUtil::CANCEL_DOCUMENT,EntityKeyTypesUtil::INVOICE_ENTITY_KEY,$id);
        if(is_null($entityAppCancelDocData)){
            $this->entityAppDataRepo->insertGetId(
                [
                    'entity_key'=>EntityKeyTypesUtil::INVOICE_ENTITY_KEY,
                    'entity_id'=>$id,
                    'app_key'=>EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
                    'data'=>$strategy->getCancelDocResponseBody(),
                    'action_key'=>ElectronicInvoicesActionsUtil::CANCEL_DOCUMENT
                ]);
        }else{
            $this->entityAppDataRepo->update($entityAppCancelDocData->id,['data'=>$strategy->getCancelDocResponseBody()]);
        }

        if(!$result){
            $this->apiErrors = $strategy->getMessages();
            return false;
        }

        return true;

    }


    public function getErrors()
    {
       return ErrorMapper::getErrors($this->apiErrors, $this->data);
    }

}
