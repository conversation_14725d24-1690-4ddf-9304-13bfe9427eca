<?php


namespace App\Modules\LocalEntity\AppEntities\Listing\CriteriaExecutors;

use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\JoinCriteria;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\QueryCriteria;
use App\Modules\LocalEntity\Prototype\Filters\ConditionMeta;

class JoinExecutor
{
    public function execute($queryBuilder,JoinCriteria $join) {
        $table = $join->getTable() . " as " . ($join->getAlias() ? $join->getAlias() : $join->getTable());
        $onConditions = $join->getOnConditions();
        switch ($join->getType()) {
            case 'left':
                $queryBuilder = $queryBuilder->leftJoin($table, function ($query) use ($onConditions) {
                    $exec = new ConditionExecutor();
                    foreach ($onConditions as $condition) {
                        if($condition instanceof ConditionMeta) {
                            $query->on(...$condition->toArray());
                        } else {
                            //we use our custom condition executor bcs
                            //it handles different types of operations
                            $exec->execute($query, $condition, false);
                        }
                    }
                });
                break;
                case 'right':
                    $queryBuilder = $queryBuilder->rightJoin($table, function ($query) use ($onConditions) {
                        $exec = new ConditionExecutor();
                        foreach ($onConditions as $condition) {
                            if($condition instanceof ConditionMeta) {
                                $query->on(...$condition->toArray());
                            } else {
                                //we use our custom condition executor bcs
                                //it handles different types of operations
                                $exec->execute($query, $condition, false);
                            }
                        }
                    });
                break;
        }
        return $queryBuilder;
    }

}
