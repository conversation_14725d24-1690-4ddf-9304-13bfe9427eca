<?php


namespace App\Modules\LocalEntity\AppEntities\Listing\CriteriaExecutors;


use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\SelectCriteria;

class SelectExecutor
{
    public function execute($queryBuilder, SelectCriteria $select) {
        $field = $select->getField();
        $alias = $select->getAlias();

        // If field is already a DB::raw, use it directly
        if ($field instanceof \Illuminate\Database\Query\Expression) {
            return $queryBuilder->addSelect(\DB::raw("{$field->getValue()} as {$alias}"));
        }

        // Else treat it as string
        return $queryBuilder->addSelect("{$field} as {$alias}");
    }
}