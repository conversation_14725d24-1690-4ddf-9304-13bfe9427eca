<?php

namespace App\Modules\LocalEntity\AppEntities\Listing\Modes;


use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\AppEntities\AppEntityRepository;
use App\Modules\LocalEntity\Helpers\CacheLogger;
use Izam\Daftra\Common\EntityStructure\EntityAndRelationCache;
use Izam\Daftra\Common\EntityStructure\IEntity;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;

class HasManyDataFetcher
{

    public static function assign(IEntity $entity, &$data, $hasOne = false, $metaObject = null)
    {
        $types = [EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN, EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS];
        if($metaObject && $metaObject->get('load_subform')){
            array_push($types, EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM);
        }
        $fields = $entity->getFieldsBy(['is_listing' => 1, 'type' => $types]);

        if($entity->getEntityKey() == EntityKeyTypesUtil::PRODUCT_ENTITY_KEY){
            $fields = array_merge($fields, $entity->getFieldsBy(['is_listing' => 1, 'type' => EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE]));
        }

        $primaryKeyString = $entity->getPrimaryKey();

        if ($hasOne) {
            $primaryKeysData =  array_column(array_column($data->toArray(), $hasOne),  $primaryKeyString);
        } else {
            $primaryKeysData =  (array_column($data->toArray(), $primaryKeyString));
        }


        /**
         * @var AppEntityRepository
         */
        $repo = resolve(AppEntityRepository::class);
        $isFieldsQuery = false;
        if (!is_null($metaObject)) {
            $fieldsQuery = $metaObject->getMode()->getFields();
            $isFieldsQuery = $fieldsQuery[0] != '*';
        }
        foreach ($fields as $field) {
            if ($isFieldsQuery && !in_array($field->getKey(), $fieldsQuery)) {
                continue;
            }
            $mappedData = self::prepareData($repo, $field, $primaryKeysData);
            foreach ($data as $item) {
                if ($hasOne) {
                    $item->{$hasOne}->{$field->getRelation()->getName()} = $mappedData[$item->{$primaryKeyString}] ?? null;
                } else {
                    $item->{$field->getRelation()->getName()} = $mappedData[$item->{$primaryKeyString}] ?? null;
                }

            }
        }
    }

    private static function prepareData($repo, $field, $primaryKeysData)
    {
        $target_records = $repo->getRelatedManyData($field->getRelation(), $primaryKeysData);
        $relation = $field->getRelation();
        $targetRelation = $field->getBelongsToRelation();
        if (empty($targetRelation)) {
            CacheLogger::log($field);
            EntityAndRelationCache::clearCache();
            resolve(AppEntityStructureGetter::class)->buildEntity($field->getEntityKey());
        }
        $pivot_target_ids = array_column($target_records,  $targetRelation->getSourceDb());
        $targetEntity = $targetRelation->getTargetEntity();
        $records = $repo->getAllWithShowAll($targetEntity, $pivot_target_ids);
        $ret = [];
        foreach ($target_records as $record) {
            ;
            $pivot_records = array_values(array_filter($target_records, function ($item) use($relation, $record) {
                return $item->{$relation->getTargetDb()} == $record->{$relation->getTargetDb()};
            }));

            $pivot_records = array_map(function ($item) use ($targetRelation, $records, $targetEntity) {
                $item->{$targetRelation->getName()} = self::getItem($records, $targetEntity->getPrimaryKey(), $item->{$targetRelation->getSourceDb()});
                return $item;
            }, $pivot_records);
            $ret[$record->{$relation->getTargetDb()}] = $pivot_records;
        }
        return $ret;
    }


    private static function getItem($records, $primaryKey, $id)
    {
        foreach ($records as $record) {
            if ($record->{$primaryKey} == $id) {
                return $record;
            }
        }
        return false;
    }
}
