<?php


namespace App\Modules\LocalEntity\Creators;


use Izam\Daftra\Common\EntityStructure\Utils\LocalEntityTypeUtil;
use App\Modules\LocalEntity\Services\LocalEntityService;
use App\Utils\PluginUtil;
use Izam\Booking\Utils\ServiceFormUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class ServiceFormsCustomDataEntityCreator
{
    /**
     * @var EntityCreatorInterface
     */
    private $service;

    public function __construct(LocalEntityService $service)
    {
        $this->service = $service;
    }

    public function create(array $serviceForm)
    {
        $this->service->findOrCreateEntity(
            [
                'key' => ServiceFormUtil::getAdditionalPrefix() . $serviceForm['id'],
                'label' => $serviceForm['label'],
                'status' => 1,
                'db_name' => NULL,
                'name_field' => 'id',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'type' => LocalEntityTypeUtil::CHILD_ITEM,
                'extend' => NULL,
                'parent_entity' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'extend_options' => NULL
            ]
        );
    }
}
