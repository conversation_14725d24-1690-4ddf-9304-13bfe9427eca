<?php

namespace App\Modules\LocalEntity\Queue\Listeners;

use App\Modules\LocalEntity\Creators\ServiceFormsCustomDataEntityCreator;
use Izam\Daftra\Queue\Listeners\IEventHandler;

class ServiceFormsCustomDataCreatedListener implements IEventHandler
{
    public function __construct(
        private ServiceFormsCustomDataEntityCreator $entityCreator,
    )
    {
    }

    public function handle($event)
    {
        $data = $event->data;
        $requestType = [
            'id' => $data->id,
            'label' => "Service Form $data->id  More Information",
        ];
        $this->entityCreator->create($requestType);
    }

    public static function getInstance()
    {
        return new self(resolve(ServiceFormsCustomDataEntityCreator::class));
    }
}
