<?php


namespace App\Modules\LocalEntity\Queue\Events;

use Izam\Daftra\Common\Queue\EventTypeUtil;
use Izam\Daftra\Queue\Events\EventAbstract;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceFormsCustomDataCreated extends EventAbstract
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function getType()
    {
        return EventTypeUtil::SERVICE_FORMS_CUSTOM_DATA_CREATED;
    }
}
