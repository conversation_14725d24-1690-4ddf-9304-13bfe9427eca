<?php


namespace App\Modules\LocalEntity\Listing\Parser;

use Illuminate\Http\Request;

class FormParser implements Parser
{

    protected $request;
    protected $data = [];

    private $perPage = null;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function parseFilters() : array
    {
        $data = $this->request->all('filter')['filter']?? [];
        if(isset($this->data['filter'])) {
            $data  = array_merge($data, $this->data['filter']);
        }

        //Check that data is array in case it was sent by mistake as string to prevent array type errors
        if(!is_array($data)) {
            return [];
        }

        return $this->getFilters($data);

    }

    private function getFilters($data)
    {
        $return = [];
        $data = array_filter($data, function($item){
            if(is_null($item)){
                return false;
            }

            return true;
        });
        foreach ($data as $key => $filter) {
            if (is_array($data[$key]) && (in_array($key, ['and','or']) || is_numeric($key))) {
                $return[$key] = $this->getFilters($data[$key]);
            } else if (is_array($data[$key]) && !in_array($key, ['and','or'])) {
                foreach ($data[$key] as $operator => $value) {
                    $return[$key][] = $this->returnFilter($value, $operator);
                }
            }else {
                $return[$key][] = $this->returnFilter($filter, null);
            }
        }
        return $return;
    }

    public function parseMode(): array
    {
        return  [
            'type' => $this->request->get('mode'),
            'page' => $this->request->get('page') ??null,
            'per_page' => $this->request->get('per_page') ?? $this->perPage,
            'fields' => $this->request->get('fields')??[],
        ];
    }

    public function setPerPage($perPage)
    {
        $this->perPage = $perPage;
    }

    public function parseSorting(): array
    {
        $data = $this->request->get('sort')??[];
        if(isset($this->data['sort'])) {
            $data  = array_merge($data, $this->data['sort']);
        }
        $sorts =  [];
        foreach ($data as $key => $item) {
            $sorts[] =  [
                'sort' => $key,
                'direction' => $item
            ];
        }
        return $sorts;
    }

    private function returnFilter($value, $operator)
    {
        return [
            'value' => $value,
            'operator' => $operator,
        ];
    }

    public function setData($data): Parser
    {
        $this->data = $data;
        return $this;
    }

    public function addFilter($filter)
    {
    }

    public function get($param)
    {
        return $this->request->get($param);
    }

}
