<?php

namespace App\Modules\LocalEntity\Listing\Structure;

use App\Modules\LocalEntity\Prototype\DataStructure\AndClause;
use App\Modules\LocalEntity\Prototype\DataStructure\Clause;
use App\Modules\LocalEntity\Prototype\DataStructure\Filter;
use App\Modules\LocalEntity\Prototype\DataStructure\OrClause;
use App\Modules\LocalEntity\Prototype\DataStructure\Sort;
use App\Modules\LocalEntity\Prototype\ListingMetaObject;
use App\Modules\LocalEntity\Listing\Parser\Parser;


class Generator
{

    /**
     * @var ListingMetaObject $listingMetaObject
     */
    private $listingMetaObject;

    /**
     * @var Parser $parser;
     */
    private $parser;

    public function __construct(Parser $parser)
    {
        $this->listingMetaObject = new ListingMetaObject();
        $this->parser = $parser;
    }

    public function get(): ListingMetaObject
    {
        $this->setFilters($this->parser->parseFilters());
        $this->setSorting($this->parser->parseSorting());
        $this->setMode($this->parser->parseMode());
        $this->setLoadSubForm($this->parser->get('load_subform'));
        return  $this->listingMetaObject;
    }


    private function setFilters($items)
    {
//
//        $items['or']['and'] = [
//            "age" => [
//                [
//                    "value" => 15,
//
//                ]
//            ],
//            "name" => [
//                [
//                    "value" => "tamer",
//                    "operator" => "like"
//                ]
//            ]
//        ];

        $conditions = $this->getConditions($items);
        foreach ($conditions as $condition) {
            $this->listingMetaObject->setFilter($condition);
        }
    }

    private function getConditions($items)
    {
        $conditions = [];
        foreach ($items as $key => $filters) {

            if ($key == "or") {
                $conditions[] =  $this->resolveClause($filters, new OrClause());
            } else if ($key == "and") {
                $conditions[] =  $this->resolveClause($filters, new AndClause());
            }else if(is_numeric($key)){
                // Handle Filter Groups in URL Queries
                $temp = $this->getConditions($filters);
                $conditions[] = array_shift($temp);
            } else {
                foreach ($filters as $filter) {
                    if(is_array($filter)) {
                       $conditions[] = $this->getFilter($key, $filter);
                    }
                }
            }
        }
        return $conditions;
    }

    private function resolveClause($filters, Clause $clause)
    {
        $obj = $clause;
        $clauseConditions = $this->getConditions($filters);
        foreach ($clauseConditions as $condition) {
            $obj->pushChildren($condition);
        }
        return $obj;
    }

    private function getFilter($key, $filter)
    {
        return new Filter($key, $filter['value'],$filter['operator']??null);
    }

    private function setSorting($data)
    {
        foreach ($data as $item) {
            $this->listingMetaObject->setSorting(
                new Sort($item['sort'], $item['direction'])
            );
        }
    }


    private function setMode($mode)
    {
        $this->listingMetaObject->setMode(
            $mode
        );
    }
    private function setLoadSubForm($value)
    {
        $this->listingMetaObject->set('load_subform', $value);
    }
}
