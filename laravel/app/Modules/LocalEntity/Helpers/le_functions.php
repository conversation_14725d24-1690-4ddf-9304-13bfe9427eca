<?php

use App\IzamViews\IzamView;
use App\Facades\Permissions;
use App\Modules\LocalEntity\Repositories\TagsRepository;
use App\Repositories\FollowUpStatusRepository;
use App\Utils\CategoryTypesUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\FollowUpItemTypeUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Izam\Booking\Utils\ServiceFormUtil;
use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Entity\Service\Spec\TypeHelper;
use Laminas\Form\Factory;
use App\Modules\LocalEntity\Permissions\PermissionsRepositoryInterface;
use App\Utils\EntityPermissionKeyUtil;
use App\Modules\LocalEntity\Services\RowActionsService;
use Izam\Entity\Repository\DynamicRepo;

if (!function_exists('getCustomCreate')) {


    function getCustomCreate($entityKey , $createData) {
        if (str_starts_with($entityKey,'le_workflow-type') !== false) {
            return view('work_orders.owner.create', $createData);
        }

        $view = match ($entityKey){
            EntityKeyTypesUtil::PURCHASE_REQUEST => view('purchase_requests.owner.create' , $createData),
            EntityKeyTypesUtil::QUOTATION_REQUEST => view('quotation_requests.owner.create', $createData),
            EntityKeyTypesUtil::WORK_FLOW_TYPE => view('workflows.owner.create', $createData),
            EntityKeyTypesUtil::WORK_ORDER => view('work_orders.owner.create', $createData),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => new IzamView('manufacturing/manufacturing_order/create', $createData),
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => view('leave_applications.owner.create', $createData),
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => view('stock_requests.owner.create', $createData),
            EntityKeyTypesUtil::REQUEST_TYPE_ENTITY_KEY => view('request_types.owner.create_entity', $createData),
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => new IzamView('item-groups/create', $createData),
            EntityKeyTypesUtil::BOM_ENTITY_KEY => new IzamView('manufacturing/bom/create', $createData),
            EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => new IzamView('workstation/create', $createData),
            EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY => new IzamView('production_routing/create', $createData),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST  => new IzamView('indirect-costs/create', $createData),
            EntityKeyTypesUtil::PRODUCTION_PLAN  => new IzamView('manufacturing/production-plan/create', $createData),
            default => view('LocalEntity::create.create' , $createData)
        };

        return $view ;
    }
}

if (!function_exists('urlFactory')) {
    function urlFactory($entityKey, $id)
    {

        switch ($entityKey) {

            case EntityKeyTypesUtil::PRODUCT_ENTITY_KEY:
            case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY:
            case EntityKeyTypesUtil::INVOICE_ENTITY_KEY:
                return getCakeURL(['controller' => $entityKey.'s', 'action' => 'view', $id]);

            case EntityKeyTypesUtil::CLIENT_CATEGORY_ENTITY_KEY:
                return null;

            default:
                return route('owner.entity.show',  ['entityKey' => $entityKey, 'id'=> $id]);

        }
    }
}

if (!function_exists('getPhotoPath')) {
    function getPhotoPath($entityKey, $path)
    {


        switch ($entityKey) {

            case \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                return '/files/' . getSiteHash() . '/photos/' . $path;

            case \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                return Storage::temporaryUrl(
                    $path, Carbon::now()->addMinutes(env('AWS_LIFE_TIME', 1))
                );

            default:
                return $path;

        }
    }
}


if (!function_exists('getCustomView')) {
    function getCustomView($entityKey, $viewData)
    {
        if (str_starts_with($entityKey,'le_workflow-type')) {
            return view('work_orders.owner.view', $viewData);
        }
        return match($entityKey){
            EntityKeyTypesUtil::PURCHASE_REQUEST => view('purchase_requests.owner.view', $viewData),
            EntityKeyTypesUtil::QUOTATION_REQUEST => view('quotation_requests.owner.view', $viewData),
            EntityKeyTypesUtil::WORK_ORDER => view('work_orders.owner.view', $viewData),
            EntityKeyTypesUtil::PURCHASE_QUOTATION => view('purchase_quotations.owner.view', $viewData),
            EntityKeyTypesUtil::PURCHASE_ORDER => view('purchase_orders.owner.view', $viewData),
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => new IzamView('leave_application/show', $viewData),
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => new IzamView('stock-request/show', $viewData),
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => new IzamView('item-groups/view', $viewData),
            EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY => new IzamView('production_routing/view', $viewData),
            EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => new IzamView('workstation/view', $viewData),
            EntityKeyTypesUtil::BOM_ENTITY_KEY => new IzamView('bill-of-materials/view', $viewData),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => new IzamView('manufacturing-orders/view', $viewData),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => new IzamView('manufacturing/manufacturing_order/indirect_costs/view', $viewData),

            default => view('LocalEntity::show.show', $viewData)
        };
    }
}

if (!function_exists('getViewFolder')) {
    function getViewFolder($structure)
    {
        if ($structure->isExtendedEntity()) {
            return match($structure->getEntityKey()){
                EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => 'settings/multi_cycle_approval',
                default => Str::plural($structure->getExtendedEntityKeyOrEntityKey())
            };
        }

        $entityKey = $structure->getEntityKey();
        return match($entityKey) {
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => 'manufacturing/manufacturing_order',
            default => Str::plural($entityKey)
        };
    }
}

if (!function_exists('getCustomViewRoute')) {
    function getCustomViewRoute($parameters)
    {
        $regex = '/le_workflow-type-entity-\d+/';
        $builder = resolve(\App\Modules\LocalEntity\Actions\AppEntityStructureGetter::class);
        /**
         * @var \Izam\Daftra\Common\EntityStructure\Entity $entity
         */
        $entity = $builder->buildEntity($parameters['entity_key']);
        switch ($parameters['entity_key']) {
            case ($entity->getType() === \App\Modules\LocalEntity\Utils\LocalEntityTypeUtil::CHILD_ITEM &&
                preg_match($regex,$entity->getParentEntityData()->getEntityKey())
            ):
                return getCakeURL(['controller' => 'work_orders', 'action' => 'workflow_view', $parameters['record']['reference_id']]);
            case (preg_match($regex, $parameters['entity_key']  , $matches) ? true : false):
                return getCakeURL(['controller' => 'work_orders', 'action' => 'workflow_view', $parameters['id']]);
            case EntityKeyTypesUtil::REQUEST_TYPE_ENTITY_KEY:
                return route('owner.request_types.show', ['request_type' => $parameters['id']]);
            case EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY:
                return route('owner.local_entities.builder',
                    [
                        'entityKey' => ServiceFormUtil::getAdditionalPrefix() . $parameters['id'],
                        'redirect' => route('owner.entity.show', [$parameters['entity_key'], $parameters['id']]),
                        'related_entity' => $parameters['entity_key'],
                    ]
                );
            default:
				if ($entity->getParentEntityData() != null) {
                    switch ($entity->getParentEntityData()->getEntityKey()) {
                        case 'staff':
                            return route('owner.staff.show', ['staff' => $parameters['record']['reference_id']]);
                        case 'contract':
                            return route('owner.contracts.show', ['id' => $parameters['record']['reference_id']]);
                        case 'client':
                            return getCakeURL(['prefix' => 'owner', 'controller' => 'clients', 'action' => 'view', $parameters['record']['reference_id']]);
                        case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY:
                            return getCakeURL(['prefix' => 'owner', 'controller' => 'suppliers', 'action' => 'view', $parameters['record']['reference_id']]);
                    }
				}
                return route('owner.entity.show', [$parameters['entity_key'], $parameters['id']]);
        }
    }
}


if (!function_exists('resetSubformIndex')) {
    function resetSubformIndex($request, $entityKey) {
        $structureGetter = resolve(\App\Modules\LocalEntity\Actions\AppEntityStructureGetter::class);
        $structure = $structureGetter->buildEntity($entityKey);
        foreach ($structure->getSubFormRelations() as $relation) {
            if (isset($request[$relation->getName()])) {
                $request[$relation->getName()] = array_values($request[$relation->getName()] ?? []);
            }
        }
        foreach ($structure->getHasOneRelations() as $relation) {
           if (isset($request[$relation->getName()])) {
               $request[$relation->getName()] = resetSubformIndex($request[$relation->getName()],  $relation->getTargetEntity()->getEntityKey());
           }
        }
       return $request;
    }
}

if (!function_exists('setCustomListingGlobalVar')) {
    function setCustomListingGlobalVar($entityKey, $ids) {
        if ($entityKey == 'client') {

            if (ifPluginActive(PluginUtil::FollowupPlugin)) {
                $followUpRepository = (resolve(FollowUpStatusRepository::class));
                $_statuses = $followUpRepository->getList($followUpRepository->getItemTypeStatuses(FollowUpItemTypeUtil::CLIENT_TYPE));
                $statuses = [];
                foreach ($_statuses as $k => $followUpStatus) {
                    $statuses[$k] = [
                        'label' => $followUpStatus['name'],
                        'value' => $k,
                        'attributes' => ['data-color' => $followUpStatus['color']]
                    ];
                }
                /* used in follow-up status cell */
                \Illuminate\Support\Facades\View::share('__statuses', $statuses);
            }

            if (empty($ids)) {
                return;
            }

            /** @var $tagsRepository TagsRepository */
            $tagsRepository = (resolve(TagsRepository::class));
            $tags = $tagsRepository->getClientTagsByClientIds($ids);
            \Illuminate\Support\Facades\View::share('__tags', $tags);
        } elseif ($entityKey == EntityKeyTypesUtil::PURCHASE_INVOICE) {
            if (ifPluginActive(PluginUtil::FollowupPlugin)) {
                $followUpRepository = (resolve(FollowUpStatusRepository::class));
                $_statuses = $followUpRepository->getList($followUpRepository->getItemTypeStatuses(FollowUpItemTypeUtil::PO_TYPE));
                $statuses = [];
                foreach ($_statuses as $k => $followUpStatus) {
                    $statuses[$k] = [
                        'label' => $followUpStatus['name'],
                        'value' => $k,
                        'attributes' => ['data-color' => $followUpStatus['color']]
                    ];
                }
                \Illuminate\Support\Facades\View::share('__statuses', $statuses);
            }
        } elseif ($entityKey == EntityKeyTypesUtil::SALES_ORDER) {
            if (ifPluginActive(PluginUtil::FollowupPlugin)) {
                $followUpRepository = (resolve(FollowUpStatusRepository::class));
                $_statuses = $followUpRepository->getList($followUpRepository->getItemTypeStatuses(FollowUpItemTypeUtil::SALES_ORDER_TYPE));

                $statuses = [];
                foreach ($_statuses as $k => $followUpStatus) {
                    $statuses[$k] = [
                        'label' => $followUpStatus['name'],
                        'value' => $k,
                        'attributes' => ['data-color' => $followUpStatus['color']]
                    ];
                }
                \Illuminate\Support\Facades\View::share('__statuses', $statuses);
            }
        } elseif ($entityKey == EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY) {
            \Illuminate\Support\Facades\View::share('isSimple',false);
            \Illuminate\Support\Facades\View::share('multi_listing_actions',[]);
        }
    }
}

if (!function_exists('getCustomListingActions')) {

    function getCustomListingActions($entityKey, $listingActions) {
        switch ($entityKey) {

            case EntityKeyTypesUtil::PURCHASE_QUOTATION:
                $listingActions['add']['url']['value'] = '/owner/purchase_quotations/add';
                break;
            case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY:
                if (isset($listingActions['add'])) {
                    $listingActions['add']['url']['value'] = '/owner/suppliers/add';
                }

                $listingActions['import'] = [
                    'class' => 'btn btn-primary btn-responsive-icon',
                    'icon' => '<i class="fa fa-cloud-upload me-xl-4"></i>',
                    'class-mobile' =>  'btn btn-primary btn-touch btn-icon',
                    'title' => __t('Import'),
                    'url' => [
                        'name' => 'Import',
                        'value' => '/owner/suppliers/import',
                    ]
                ];

                break;

            case EntityKeyTypesUtil::PURCHASE_ORDER:
                $listingActions['add']['url']['value'] = '/owner/purchase_orders/add';
                break;
            case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                if (
                    !isSiteSuspended() &&
                    ifPluginActive(PluginUtil::HRM_PLUGIN) &&
                \App\Facades\Permissions::checkPermission(\App\Utils\Export\ExportPermissionsUtil::getPermissions(\App\Utils\PermissionUtil::Staff_Add_New_Staffs))
                ) {
                    $listingActions['add'] = [
                        'type' => 'dropdown',
                        'class' => 'btn btn-icon btn-success responsive text-white',
                        'url' => [
                            [
                                'name' => __t('User'),
                                'value' => route('owner.staff.create', \Izam\Daftra\Staff\Util\StaffTypeUtil::USER)
                            ],
                            [
                                'name' => __t('Employee'),
                                'value' => route('owner.staff.create', \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE)
                            ],
                        ],
                        'icon' => '<i class="mdi mdi-plus-thick me-xl-4"></i>',
                        'title' => __t('Add New'),
                    ];
                } else {
                    $listingActions['add'] = [
                        'class' => 'btn btn-icon btn-success responsive text-white',
                        'url' => [
                            'name' => route('owner.staff.create', \Izam\Daftra\Staff\Util\StaffTypeUtil::USER),
                            'value' => route('owner.staff.create', \Izam\Daftra\Staff\Util\StaffTypeUtil::USER)
                        ],
                        'icon' => '<i class="fal fa-plus"></i>',
                        'title' => sprintf(__t('New %s'), __t('User'))
                    ];
                }

                if (
                    !isSiteSuspended() &&
                    \App\Facades\Permissions::checkPermission(\App\Utils\Export\ExportPermissionsUtil::getPermissions(\App\Utils\EntityKeyTypesUtil::STAFF_ENTITY_KEY))
                ) {
                    $listingActions['export'] = [
                        'class' => 'btn btn-primary btn-secondary-icon',
                        'class-mobile' =>  'btn btn-primary btn-touch btn-icon',
                        'url' => [
                            'name' => route('owner.viewExport',[
                                'entity' => \App\Utils\EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                                'ids' => 'all',
                            ]),
                            'value' => route('owner.viewExport',[
                                'entity' => \App\Utils\EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                                'ids' => 'all',
                            ]),
                        ],
                        'icon' => '<i class="fas fa-cloud-download me-xl-4"></i>',
                        'title' => __t('Export')
                    ];
                }

                break;
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                $listingActions['import'] = [
                    'class' => 'btn btn-primary btn-responsive-icon',
                    'icon' => '<i class="fa fa-cloud-upload me-xl-4"></i>',
                    'class-mobile' =>  'btn btn-primary btn-touch btn-icon',
                    'title' => __t('Import'),
                    'url' => [
                        'name' => 'Import',
                        'value' => '/owner/clients/import',
                    ]
                ];
                $listingActions['settings'] = [
                    'class' => 'btn btn-primary btn-responsive-icon',
                    'icon' => '<i class="fa fa-cogs me-xl-4"></i>',
                    'class-mobile' =>  'btn btn-primary btn-touch btn-icon',
                    'title' => '',
                    'tooltip' => __t('Settings'),
                    'url' => [
                        'name' => 'client.settings',
                        'value' => '/owner/clients/settings',
                    ]
                ];

                if (ifPluginActive(PluginUtil::FollowupPlugin)) {
                    $listingActions['status'] = [
                        'class' => 'btn btn-primary btn-responsive-icon',
                        'icon' => '<i class="fa fa-cog me-xl-4"></i>',
                        'class-mobile' =>  'btn btn-primary btn-touch btn-icon',
                        'title' => __t('Edit Statuses List'),
                        'url' => [
                            'name' => 'client.manage_status_list',
                            'value' => '/owner/follow_up_statuses/index/1',
                        ]
                    ];
                }

                if (isset($listingActions['add'])) {
                    $listingActions['add']['icon'] = '<i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-plus-thick"></i>';
                    $listingActions['add']['url']['value'] = '/owner/clients/add';
                }
                break;
            case EntityKeyTypesUtil::CONTRACT:
                $listingActions['add']['url']['value'] = '/v2/owner/contracts/create';
                $listingActions['add']['icon'] = '<i class="mdi mdi-plus"></i>';
                $listingActions['add']['title'] = sprintf(__t('Add %s'), __t(\Illuminate\Support\Str::singular('Contract')));
                break;
            case EntityKeyTypesUtil::PURCHASE_INVOICE:
                if (isset($listingActions['add']['url']['value'])) {
                    $listingActions['add']['url']['value'] = '/owner/purchase_invoices/add';
                }

                $listingActions['settings'] = [
                    'class' => 'btn btn-icon btn-secondary responsive',
                    'icon' => '<i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 fa fa fa-cog"></i>',
                    'title' => __t(''),
                    'url' => [
                        'value' => "/owner/settings/purchase_invoices",
                    ]
                ];

                break;
            case EntityKeyTypesUtil::PURCHASE_REFUND:
                if (isset($listingActions['add']['url']['value'])) {
                    $listingActions['add']['url']['value'] = '/owner/purchase_invoices/add_refund';
                }

                break;
            case EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY:
                if( \App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::ADD_STOCK_REQUEST)
                ){
                    $listingActions['add']['class'] = 'u-bg-color-subprimary';
                }
                break;
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY:
                $classList = ' l-btn-inline l-btn-group-btn l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple';

                $requestHasFilterCondition = request()->has('filter') && isset(request()->filter['status']);
                $requestStatus = request()->status;
                $pendingConditions = ($requestHasFilterCondition && request()->filter['status'] == 'pending')  ||(!$requestHasFilterCondition && $requestStatus == "pending" )|| (!$requestStatus && !$requestHasFilterCondition );;
                $approvedConditions = ($requestHasFilterCondition && request()->filter['status'] == 'approved') || (!$requestHasFilterCondition && $requestStatus == "approved" );
                $rejectedConditions = ($requestHasFilterCondition && request()->filter['status'] == 'rejected') || (!$requestHasFilterCondition &&  $requestStatus == "rejected" );
                $allTabConditions = !$requestHasFilterCondition && request()->status == 'all';

                $pendingIsActive =   $pendingConditions ? 'u-bg-color-secondary u-border-1 u-border-color-secondary disabled u-text-color-default ' : 'u-bg-color-light u-border-1 u-border-color-secondary u-text-color-default';
                $approvedIsActive = $approvedConditions  ? 'u-bg-color-secondary u-border-1 u-border-color-secondary disabled u-text-color-default ' : 'u-bg-color-light u-border-1 u-border-color-secondary u-text-color-default';
                $rejectedIsActive = $rejectedConditions  ? 'u-bg-color-secondary u-border-1 u-border-color-secondary disabled u-text-color-default ' : 'u-bg-color-light u-border-1 u-border-color-secondary u-text-color-default';
                $allIsActive =    $allTabConditions  ? 'u-bg-color-secondary u-border-1 u-border-color-secondary disabled u-text-color-default ' : 'u-bg-color-light u-border-1 u-border-color-secondary u-text-color-default';

                $listingActions['leave_balance']= [
                    'custom_content' => '<button type="button" class="l-btn--text-center ui-btn ui-btn--hover-ripple u-bg-color-primary u-text-color-white u-text-hover-color-white ui-btn--hover-ripple" id="balance-modal" data-bs-toggle="modal" data-bs-target="#leaveBalanceModal"><span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span><span class="ui-btn-inner-content"><span class="ui-btn-inner-text">'
                    . __t("Leave Balance")
                    .'</span></span></button>'
                ];

                $listingActions['group']=[
                    'group' => true, // -> this is for grouped buttons
                    'containerClasses'=> 'l-btn-group', // -> group container classes
                    'items'=>[
                        [
                            'class' =>  $allIsActive .$classList,
                            'title' => __t('All'),
                            'url' => [
                                'value' => "/v2/owner/entity/leave_application/list?".http_build_query(array_merge(request()->all() , ['status'=>request()->filter['status']  ?? 'all'])),
                            ]
                        ],
                        [
                            'class' =>  $pendingIsActive. $classList,
                            'title' => __t('Pending'),
                            'url' => [
                                'value' => "/v2/owner/entity/leave_application/list?".http_build_query(array_merge(request()->all() , ['status'=>request()->filter['status'] ?? 'pending'])),
                            ]
                        ],
                        [
                            'class' =>  $approvedIsActive .$classList,
                            'title' =>  __t('Approved'),
                            'url' => [
                                'value' => "/v2/owner/entity/leave_application/list?".http_build_query(array_merge(request()->all() , ['status'=>request()->filter['status'] ?? 'approved'])),
                            ]
                        ],
                        [
                            'class' => $rejectedIsActive . $classList,
                            'title' => __t('Rejected'),
                            'url' => [
                                'value' => "/v2/owner/entity/leave_application/list?".http_build_query(array_merge(request()->all() , ['status'=> request()->filter['status'] ?? 'rejected'])),
                            ]
                        ],
                    ]
                ];
                break;
                case EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY:
                $classList = ' l-btn-inline l-btn-group-btn l-btn--text-center u-bg-color-light-2 u-text-color-action ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple';
                $requestHasFilterCondition = request()->has('filter') && isset(request()->filter['status']);

                $requestStatus = request()->status;
                $openStatuses = ['open','draft','in_progress','finished'];
                $closedStatuses = ['closed'];
                $allConditions = !$requestHasFilterCondition && (!$requestStatus);
                $openConditions = ($requestHasFilterCondition && in_array(request()->filter['status'], $openStatuses)) || (!$requestHasFilterCondition &&  in_array($requestStatus , $openStatuses));
                $closedConditions = ($requestHasFilterCondition && in_array(request()->filter['status'],$closedStatuses)) || (!$requestHasFilterCondition && in_array($requestStatus, $closedStatuses));
                $allIsActive =   $allConditions ? ' u-text-color-white  u-bg-color-switch-off ' : 'u-bg-color-secondary u-text-color-black ';
                $openIsActive = $openConditions  ? ' u-text-color-white  u-bg-color-switch-off ' : 'u-bg-color-secondary u-text-color-black ';
                $closedIsActive = $closedConditions  ? ' u-text-color-white  u-bg-color-switch-off ' : 'u-bg-color-secondary u-text-color-black ';


                $listingActions['group']=[
                    'group' => true,
                    'containerClasses'=> 'l-btn-group l-btn-group--separated',
                    'items'=>[
                        [
                            'class' => $allIsActive.$classList  ,
                            'title' => __t('All'),
                            'url' => [
                                'value' => "/v2/owner/entity/manufacturing_order/list?reset=1",
                            ]
                        ],
                        [
                            'class' =>  $openIsActive.$classList,
                            'title' => __t('Open'),
                            'url' => [
                                'value' => "/v2/owner/entity/manufacturing_order/list?status=open",
                            ]
                        ],
                        [
                            'class' =>  $closedIsActive.$classList,
                            'title' =>  __t('Closed'),
                            'url' => [
                                'value' => "/v2/owner/entity/manufacturing_order/list?status=closed",
                            ]
                        ],
                    ]
                ];
                break;
            case EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY:
                if(
                    \App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::Proudcts_Add_New_Proudct)
                ){
                    $listingActions['add']['class'] = 'u-bg-color-subprimary';
                }
                break;
            case EntityKeyTypesUtil::BOM_ENTITY_KEY:
                if( \App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::ADD_NEW_BILL_OF_MATERIAL)
                ){
                    $listingActions['add']['class'] = 'u-bg-color-save';
                    $listingActions['add']['title'] =  sprintf(__t('Add %s'), __t('Bill of Material'));
                }
                break;
            case EntityKeyTypesUtil::SMTP_EMAIL_ADDRESS:
                $listingActions['add']['url']['value'] = route('owner.smtp_email_address.create');
                if (!Permissions::checkPermission(\App\Utils\PermissionUtil::Edit_General_Settings)) {
                    $listingActions = [];
                }
                break;
            case EntityKeyTypesUtil::PRODUCTION_PLAN:
                if( \App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::ADD_PRODUCTION_PLANS)
                ){
                    $listingActions['add']['class'] = 'u-bg-color-save';
                    $listingActions['add']['title'] =  sprintf(__t('Add %s'), __t('Production Plan'));
                }
                break;
        }
        return $listingActions;
    }
}

if (!function_exists('getAddUrl')) {
    function getAddUrl($entityKey)
    {
        $addUrl = '#';
        switch ($entityKey) {
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                $addUrl = '/owner/clients/add';
                break;
            case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY:
                $addUrl = '/owner/suppliers/add';
                break;
            case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                $addUrl = '/v2/owner/staff/create/user';
                break;
            case EntityKeyTypesUtil::CONTRACT:
                $addUrl = '/v2/owner/contracts/create';
                break;
            case EntityKeyTypesUtil::SALES_ORDER:
                $addUrl = '/owner/invoices/add_sales_order';
                break;
            case EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE:
                $addUrl = '/owner/purchase_invoices/add_debit_note';
                break;
            case EntityKeyTypesUtil::PURCHASE_INVOICE:
                $addUrl = '/owner/purchase_invoices/add';
                break;
            case EntityKeyTypesUtil::PURCHASE_REFUND:
                $addUrl = '/owner/purchase_invoices/add_refund';
                break;
            default:
                $addUrl = route('owner.entity.create', $entityKey);
                break;
        }
        return $addUrl;
    }
}

if (!function_exists('getCustomMultiListingActions')) {
    function getCustomMultiListingActions($entityKey, $entityLabel)
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                $followUpRepository = (resolve(FollowUpStatusRepository::class));
                $manulStatuses = $followUpRepository->getList($followUpRepository->getItemTypeStatuses(FollowUpItemTypeUtil::CLIENT_TYPE));
                $statuses = [];
                foreach ($manulStatuses as $k => $followUpStatus) {
                    $statuses[] = [
                        'label' => $followUpStatus['name'],
                        'value' => $k,
                        'attributes' => [
                            'data-data' => json_encode(["bgColor" => $followUpStatus['color']])
                        ]
                    ];
                }

                return [
                    'delete' => [
                        'icon' => '',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                '_token' => csrf_token(),
                                'target_url' => "/api2/{$entityKey}s/__id__",
                                'filter_query' =>  json_encode(request()->query()),
                                'title' => __t('Delete'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Delete')]

                                ]))
                            ]),
                            'action' => route('owner.entity.bulk-delete', $entityKey),
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ],
                    'suspend' => [
                        'icon' => '',
                        'title' => __t('Suspend'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/owner/clients/suspend_users/1',
                                'title' => __t('Suspend'),
                                'back_url' => '/v2/owner/entity/client/list',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' =>'/v2/owner/entity/client/list',
                                    ],
                                    ['title' => __t('Suspend')]

                                ]))
                            ]),
                            'action' => '/owner/clients/suspend_users/1',
                            'confirm' => true,
                            'title' => __t('Suspend'),
                            'ajax_bulk' => 'ajax_bulk',
                        ]
                    ],
                    'unsuspend' => [
                        'icon' => '',
                        'title' => __t('Unsuspend'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/owner/clients/suspend_users',
                                'title' => __t('Unsuspend'),
                                'back_url' => '/v2/owner/entity/client/list',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' =>'/v2/owner/entity/client/list',
                                    ],
                                    ['title' => __t('Unsuspend')]

                                ]))
                            ]),
                            'action' => '/owner/clients/suspend_users',
                            'confirm' => true,
                            'title' => __t('Unsuspend'),
                            'ajax_bulk' => 'ajax_bulk',
                        ]
                    ],
                    'sendEmails' => [
                        'icon' => '',
                        'title' => __t('Send Emails'),
                        'id' => 'send_emails',
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/owner/clients/send_emails',
                                'title' => __t(' Send Emails'),
                                'back_url' => '/v2/owner/entity/client/list',
                                'filter_query' =>  json_encode(request()->query()),
                                'toUrl' => '/owner/clients/send_emails',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' =>'/v2/owner/entity/client/list',
                                    ],
                                    ['title' => __t('Send Emails')]

                                ])),
                                'action_type' => 'Sending Email',
                                'action' => 'GET',
                                '_token' => csrf_token(),
                            ]),
                            'action' => route('owner.entity.get-ids', $entityKey),
                            'title' => __t('Send Emails'),
                            'ajax_bulk' => 'ajax_bulk',
                        ]
                    ],
                    'changeCategory' => [
                        'icon' => '',
                        'title' => __t('Change Category'),
                        'id' => 'change-client-cat',
                        'prompt' => [
                            'target_id' => 'changeCategoryModal',
                            'url' => route('owner.entity.get-ids', $entityKey),
                            'toUrl' => '/owner/clients/change_category',
                            'filter_query' =>  json_encode(request()->query()),
                            'method' => 'post',
                            'form' => (new Factory())->createForm([
                                'elements' => [
                                    [
                                        'spec' => [
                                            'name' => 'data[Client][category][]',
                                            'type'  => TypeHelper::getByType(EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT),
                                            'options' => [
                                                'label' => __('Category'),
                                                'empty_option' => __('Category')
                                            ],
                                            'attributes' => [
                                                'placeholder' => __t('Category'),
                                                'data-app-form-datalist-options' => '{"ajax": "/v2\/api\/entity\/auto-suggest\/multi-fields\/category\/categories.name?fields[categories.category_type]=' . CategoryTypesUtil::CATEGORY_TYPE_CLIENT . '&fields[categories.name]=__q__", "class": "ui-input-datalist-menu-item l-input-datalist-menu-item"}',
                                            ]
                                        ]
                                    ]
                                ]
                            ])
                        ]
                    ],
                    'assignStaff' => [
                        'icon' => '',
                        'title' => __t('Assign Employees'),
                        'check' => function () {
                            return ifPluginActive(PluginUtil::FollowupPlugin) AND Permissions::checkPermission(\App\Utils\PermissionUtil::Assign_Clients_To_Staff);
                        },
                        'prompt' => [
                            'target_id' => 'changeStaffModal',
                            'url' => '/owner/clients/assign_staff_multi',
                            'method' => 'post',
                            'form' => (new Factory())->createForm([
                                'elements' => [
                                    [
                                        'spec' => [
                                            'name' => 'data[Client][staff][]',
                                            'type'  => TypeHelper::getByType(EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN),
                                            'options' => [
                                                'label' => __('Employees'),
                                                'empty_option' => __('Employees'),

                                            ],
                                            'attributes' => [
                                                'placeholder' => __t('Employees'),
                                                'data-app-form-select-template' => 'client-employee',
                                                'data-app-form-select-options' => '{"ajax": { "url": "/v2/owner/staff/search?term=__q__&_type=query&q=__q__" } }',
                                                'multiple' => true,
                                                ]
                                        ]
                                    ]
                                ]

                            ])
                        ]
                    ],
                    'changeStatus' => [
                        'icon' => '',
                        'title' => __t('Change Status'),
                        'check' => function () {
                            return ifPluginActive(PluginUtil::FollowupPlugin);
                        },
                        'prompt' => [
                            'target_id' => 'changeStatusModal',
                            'url' => '/owner/clients/change_statuses',
                            'method' => 'post',
                            'form' => (new Factory())->createForm([
                                'elements' => [
                                    [
                                        'spec' => [
                                            'name' => 'data[Client][status]',
                                            'type'  => TypeHelper::getByType(EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN),
                                            'options' => [
                                                'label' => __('Select Status'),
                                                'empty_option' => __('Select Status'),
                                                'value_options' => $statuses
                                            ],
                                            'attributes' => [
                                                'placeholder' => __('Select Status'),
                                                "data-app-form-select" => true,
                                                "data-app-form-select-template" => "colored"
                                            ]
                                        ]
                                    ]
                                ]
                            ])
                        ],
                    ],
                    'sendSms' => [
                        'icon' => '',
                        'title' => __t('Send SMS'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/owner/sms_campaigns/send_bulk_sms/clients',
                                'title' => __t(' Send SMS'),
                                'back_url' => '/v2/owner/entity/client/list',
                                'filter_query' =>  json_encode(request()->query()),
                                'toUrl' => '/owner/sms_campaigns/send_bulk_sms/clients',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' =>'/v2/owner/entity/client/list',
                                    ],
                                    ['title' => __t('Send SMS')]

                                ])),
                                'action_type' => 'Send SMS',
                                'action' => 'GET',
                                '_token' => csrf_token(),
                            ]),
                            'action' => route('owner.entity.get-ids', $entityKey),
                            'confirm' => true,
                            'title' => __t('Send SMS'),
                            'ajax_bulk' => 'ajax_bulk',
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY:
                return [
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/api2/suppliers/__id__',
                                'title' => __t('Delete'),
                                '_token' => csrf_token(),
                                'filter_query' =>  json_encode(request()->query()),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Delete')]

                                ]))
                            ]),
                            'action' => route('owner.entity.bulk-delete', $entityKey),
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY:
            case EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY:
            // case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
            case EntityKeyTypesUtil::BOM_ENTITY_KEY:
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY:
            case EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY:
            case EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY:
            case EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST:
            case EntityKeyTypesUtil::SMTP_EMAIL_ADDRESS:
            case EntityKeyTypesUtil::BRAND:
            case EntityKeyTypesUtil::VEHICLE:
            case EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY:
                return [];
            case EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY:
                return [
                    'export' => [
                        'url' => [
                            'name' => route('owner.viewExport',['entity' => $entityKey]),
                        ],
                        'onclick' => 'addMultiIDsURLDiffName(this)',
                        'method' => 'get',
                        'icon' => '',
                        'title' => __t('Export')
                    ]
                ];
            case EntityKeyTypesUtil::PURCHASE_INVOICE:
                return [
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => __t($entityLabel),
                                'target_url' => '/api2/purchase_invoices/__id__',
                                'title' => __t('Delete'),
                                'back_url' => '/v2/owner/entity/purchase_order/list',
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => json_encode([
                                    [
                                        'title' => __t('Purchase Invoices'),
                                        'link' => '/v2/owner/entity/purchase_order/list',
                                    ],
                                    ['title' => __t('Delete')]

                                ])
                            ]),
                            'action' => '/owner/bulk/update',
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE:
                    return [
                        'delete' => [
                            'icon' => 'mdi mdi-delete u-text-color-danger',
                            'title' => __t('Delete'),
                            'bulk' => [
                                'data' => json_encode([
                                    'entity_key' => $entityKey,
                                    'entity_name' => __t($entityLabel),
                                    'target_url' => '/api2/purchase_invoices/__id__',
                                    'title' => __t('Delete'),
                                    'back_url' => '/v2/owner/entity/purchase_debit_note/list',
                                    'action_type' => 'Deleting',
                                    'action' => 'DELETE',
                                    'breadcrumbs' => json_encode([
                                        [
                                            'title' => __t('Purchase Debit Notes'),
                                            'link' => '/v2/owner/entity/purchase_debit_note/list',
                                        ],
                                        ['title' => __t('Delete')]

                                    ])
                                ]),
                                'action' => '/owner/bulk/update',
                                'confirm' => true,
                                'title' => __t('Delete'),
                                'ajax_bulk' => 'ajax_bulk'
                            ]
                        ]
                    ];
            case EntityKeyTypesUtil::PURCHASE_REFUND:
                return [
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => __t($entityLabel),
                                'target_url' => '/api2/purchase_invoices/__id__',
                                'title' => __t('Delete'),
                                'back_url' => '/v2/owner/entity/purchase_refund/list',
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => json_encode([
                                    [
                                        'title' => __t('Purchase Refunds'),
                                        'link' => 'v2/owner/entity/purchase_refund/list',
                                    ],
                                    ['title' => __t('Delete')]

                                ])
                            ]),
                            'action' => '/owner/bulk/update',
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::SALES_ORDER:
                return [
                    'print_pdf' => [
                        'icon' => 'mdi mdi-file',
                        'title' => __t('Print PDF'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                '_token' => csrf_token(),
                                'target_url' => "/owner/invoices/multi_pdf",
                                'filter_query' =>  json_encode(request()->query()),
                                'title' => __t('Print PDF'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Print PDF',
                                'action' => 'POST',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Print PDF')]

                                ]))
                            ]),
                            'ajax_ids_getter_url' => route('owner.entity.bulk-get-ids', $entityKey),
                            'confirm' => true,
                            'title' => __t('Print PDF'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ],
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => __t($entityLabel),
                                'target_url' => '/api2/sales_orders/__id__',
                                'title' => __t('Delete'),
                                'back_url' => '/v2/owner/entity/sales_order/list',
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => json_encode([
                                    [
                                        'title' => __t('Sales Orders'),
                                        'link' => '/v2/owner/entity/sales_order/list',
                                    ],
                                    ['title' => __t('Delete')]

                                ])
                            ]),
                            'action' => '/owner/bulk/update',
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::STOCKTAKING:
                return [
                    'delete' => [
                        'icon' => '',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                '_token' => csrf_token(),
                                'target_url' => "/api2/{$entityKey}s/__id__",
                                'filter_query' => json_encode(request()->query()),
                                'title' => __t('Delete'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Delete')]

                                ]))
                            ]),
                            'action' => route('owner.entity.bulk-delete', $entityKey),
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ],
                ];

            case EntityKeyTypesUtil::PURCHASE_QUOTATION:
                return [
                    'print_pdf' => [
                        'icon' => 'mdi mdi-file',
                        'title' => __t('Print PDF'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                '_token' => csrf_token(),
                                'target_url' => "/owner/purchase_quotations/multi_pdf",
                                'filter_query' =>  json_encode(request()->query()),
                                'title' => __t('Print PDF'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Print PDF',
                                'action' => 'POST',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Print PDF')]

                                ]))
                            ]),
                            'action' => "/owner/purchase_quotations/multi_pdf",
                            'ajax_ids_getter_url' => route('owner.entity.bulk-get-ids', $entityKey),
                            'confirm' => true,
                            'title' => __t('Print PDF'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ],
                    'export' => [
                        'icon' => 'mdi mdi-file',
                        'title' => __t('Export'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                '_token' => csrf_token(),
                                'target_url' => "/owner/purchase_orders/export_quotation",
                                'filter_query' =>  json_encode(request()->query()),
                                'title' => __t('Export'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Export',
                                'action' => 'POST',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Export')]

                                ]))
                            ]),
                            'action' => "/owner/purchase_orders/export_quotation",
                            'ajax_ids_getter_url' => route('owner.entity.bulk-get-ids', $entityKey),
                            'confirm' => true,
                            'title' => __t('Export'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ],
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/v2/api/entity/delete/' . $entityKey . '/__id__',
                                'title' => __t('Delete'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Delete')]

                                ]))
                            ]),
                            'action' => '/owner/bulk/update',
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::PURCHASE_ORDER:
                return [
                    'print_pdf' => [
                        'icon' => 'mdi mdi-file',
                        'title' => __t('Print PDF'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                '_token' => csrf_token(),
                                'target_url' => "/owner/purchase_orders/multi_pdf",
                                'filter_query' =>  json_encode(request()->query()),
                                'title' => __t('Print PDF'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Print PDF',
                                'action' => 'POST',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Print PDF')]

                                ]))
                            ]),
                            'action' => "/owner/purchase_orders/multi_pdf",
                            'ajax_ids_getter_url' => route('owner.entity.bulk-get-ids', $entityKey),
                            'confirm' => true,
                            'title' => __t('Print PDF'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ],
                    'export' => [
                        'icon' => 'mdi mdi-file',
                        'title' => __t('Export'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                '_token' => csrf_token(),
                                'target_url' => "/owner/purchase_orders/export",
                                'filter_query' =>  json_encode(request()->query()),
                                'title' => __t('Export'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Export',
                                'action' => 'POST',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Export')]

                                ]))
                            ]),
                            'action' => "/owner/purchase_orders/export",
                            'ajax_ids_getter_url' => route('owner.entity.bulk-get-ids', $entityKey),
                            'confirm' => true,
                            'title' => __t('Export'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ],
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/v2/api/entity/delete/' . $entityKey . '/__id__',
                                'title' => __t('Delete'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Delete')]

                                ]))
                            ]),
                            'action' => '/owner/bulk/update',
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::REQUEST_ENTITY_KEY:
                return [
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'title' => __t('Delete'),
                                'target_url' => "/v2/owner/request_types/" . str_replace('le_request_type_', '', request('entityKey')) . "/requests/__id__",
                                'back_url' => route('owner.entity.list', request('entityKey')),
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                '_method' => 'POST',
                                'filter_query' => json_encode(request()->query()),
                                '_token' => csrf_token(),
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Delete')]

                                ]))
                            ]),
                            'action' => '/owner/bulk/update',
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
            case EntityKeyTypesUtil::PURCHASE_REQUEST:  
            case EntityKeyTypesUtil::QUOTATION_REQUEST:
             return ['export' => [
                    'url' => [
                        'name' => route('owner.viewExport',['entity' => $entityKey]),
                    ],
                    'onclick' => 'addMultiIDsURLDiffName(this)',
                    'method' => 'get',
                    'icon' => '',
                    'title' => __t('Export')
                ],
                'delete' => [
                    'icon' => 'mdi mdi-delete u-text-color-danger',
                    'title' => __t('Delete'),
                    'bulk' => [
                        'data' => json_encode([
                            'entity_key' => $entityKey,
                            'entity_name' => $entityLabel,
                            'target_url' => '/v2/api/entity/delete/' . $entityKey . '/__id__',
                            'title' => __t('Delete'),
                            'back_url' => route('owner.entity.list', $entityKey),
                            'action_type' => 'Deleting',
                            'action' => 'DELETE',
                            'breadcrumbs' => addslashes(json_encode([
                                [
                                    'title' => $entityLabel,
                                    'link' => route('owner.entity.list', $entityKey),
                                ],
                                ['title' => __t('Delete')]

                            ]))
                        ]),
                        'action' => '/owner/bulk/update',
                        'confirm' => true,
                        'title' => __t('Delete'),
                        'ajax_bulk' => 'ajax_bulk'
                    ]
                ]
            ];
            default :
                return [
                    'delete' => [
                        'icon' => 'mdi mdi-delete u-text-color-danger',
                        'title' => __t('Delete'),
                        'bulk' => [
                            'data' => json_encode([
                                'entity_key' => $entityKey,
                                'entity_name' => $entityLabel,
                                'target_url' => '/v2/api/entity/delete/' . $entityKey . '/__id__',
                                'title' => __t('Delete'),
                                'back_url' => route('owner.entity.list', $entityKey),
                                'action_type' => 'Deleting',
                                'action' => 'DELETE',
                                'breadcrumbs' => addslashes(json_encode([
                                    [
                                        'title' => $entityLabel,
                                        'link' => route('owner.entity.list', $entityKey),
                                    ],
                                    ['title' => __t('Delete')]

                                ]))
                            ]),
                            'action' => '/owner/bulk/update',
                            'confirm' => true,
                            'title' => __t('Delete'),
                            'ajax_bulk' => 'ajax_bulk'
                        ]
                    ]
                ];
        }
    }
}


if (!function_exists('getCustomCloneTitle')) {
    function getCustomCloneTitle($entityKey) {
        switch ($entityKey) {

            case 'quotation_request':
                return 'Convert';
            default:
                return 'Clone';

        }
    }
}


if (!function_exists('checkKeyExistsInArray')) {
    function checkKeyExistsInArray($key, $assocArr) {
       foreach($assocArr as $arr) {
           if(isset($arr[$key])) {
               return true;
           }
       }
       return false;
    }
}

if (!function_exists('getEntityRowActionsCheckerFirst')) {
    function getEntityRowActionsCheckerFirst($pageTitle, $row, $listingRowActions, $entityKey, $permissionActions): array
    {
        return app(RowActionsService::class)->getEntityRowActionsCheckerFirst($pageTitle, $row, $listingRowActions, $entityKey, $permissionActions);
    }
}

if (!function_exists('getEntityPermissionsForRowActions')) {
    function getEntityPermissionsForRowActions($entityKey): array
    {
        $permissionsRepository = app(PermissionsRepositoryInterface::class);
        $allPermissions = $permissionsRepository->getPermission($entityKey);
        $rowActionPermissions = [
            'view' => [
                'all' => $allPermissions[EntityPermissionKeyUtil::LIST] ?? false,
                'own' => $allPermissions[EntityPermissionKeyUtil::LIST_OWN] ?? false
            ],
            'update' => [
                'all' => $allPermissions[EntityPermissionKeyUtil::UPDATE] ?? false,
                'own' => $allPermissions[EntityPermissionKeyUtil::UPDATE_OWN] ?? false
            ],
            'delete' => [
                'all' => $allPermissions[EntityPermissionKeyUtil::DELETE] ?? false,
                'own' => $allPermissions[EntityPermissionKeyUtil::DELETE_OWN] ?? false
            ]
        ];
        return array_merge($rowActionPermissions, getEntityPermissionsForRowActionsCustom($entityKey, $allPermissions));
    }

}

if (!function_exists('getEntityPermissionsForRowActionsCustom')) {
    function getEntityPermissionsForRowActionsCustom($entityKey, $allPermissions): array
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                return [
                    'clone' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::CLONE] ?? false,
                    ],
                    'statement' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::STATEMENT] ?? false,
                    ],
                    'login_as' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::LOGIN_AS] ?? false,
                    ]
                ];
            case EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION:
                return [
                    'set-status-active' => [
                        'all' => true,
                    ],
                    'set-status-inactive' => [
                        'all' => true,
                    ],                  
                ];
            case EntityKeyTypesUtil::PURCHASE_INVOICE:
                return [
                    'clone' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::CLONE] ?? false,
                    ],
                    'email_to_supplier' => [
                        'all' => true
                    ],
                    'print' => [
                        'all' => true
                    ],
                    'pdf' => [
                        'all' => true
                    ],
                ];
            case EntityKeyTypesUtil::PURCHASE_REFUND:
                return [
                    'email_to_supplier' => [
                        'all' => true
                    ],
                    'print' => [
                        'all' => true
                    ],
                    'pdf' => [
                        'all' => true
                    ],
                ];
            case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                return [
                    'send-login' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::SEND_LOGIN] ?? false,
                    ],
                    'change-password' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::CHANGE_PASSWORD] ?? false,
                    ],
                    'set-status-active' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::SET_STATUS_ACTIVE] ?? false,
                    ],
                    'set-status-inactive' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::SET_STATUS_INACTIVE] ?? false,
                    ]
                ];
            case EntityKeyTypesUtil::SALES_ORDER:
                return [
                    'pdf' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::LIST] ?? false,
                        'own' => $allPermissions[EntityPermissionKeyUtil::LIST_OWN] ?? false],
                    'print' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::LIST] ?? false,
                        'own' => $allPermissions[EntityPermissionKeyUtil::LIST_OWN] ?? false],
                    'mail_to_client' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::LIST] ?? false,
                        'own' => $allPermissions[EntityPermissionKeyUtil::LIST_OWN] ?? false],
                ];

            case EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE:
                return [
                    'email_to_supplier' => [
                        'all' => true
                    ],
                    'print' => [
                        'all' => true
                    ],
                    'pdf' => [
                        'all' => true
                    ],
                ];
            case EntityKeyTypesUtil::BOM_ENTITY_KEY:
                return [
                    'clone' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::CLONE] ?? false,
                    ],
                ];
            case EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY:
                return [
                    'clone' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::CLONE] ?? false,
                    ],
                ];
            case EntityKeyTypesUtil::PRODUCTION_PLAN:
                return [
                    'clone' => [
                        'all' => $allPermissions[EntityPermissionKeyUtil::CLONE] ?? false,
                    ],
                ];
                case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY:
                    return [
                        'undo' => [
                            'all' => true,
                        ],
                        'undo_rejection' => [
                            'all' => true,
                        ],
                        'approve' => [
                            'all' => true,
                        ],
                        "reject" => [
                            'all' => true,
                        ]
                    ];

            case EntityKeyTypesUtil::CONTRACT_INSTALLMENT:
                return [
                    'pay' => [
                        'all' => true,
                    ],
                ];
            case EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY:
                return [
                    'form_builder' => [
                        'all' => true,
                    ],
                    'manage_printable_templates' => [
                        'all' => true,
                    ],
                ];
            case EntityKeyTypesUtil::ENTITY_DOCUMENT:
                return [
                    'upload' => [
                        'all' => true,
                    ],
                ];
            default:
                return [];
        }
    }

    if (!function_exists('getEntitiesHaveActionPermissions')) {
        function getEntitiesHaveActionPermissions($relatedEntitiesKey = [], $action = 'view_all'): array
        {
            $entityKeys = [];
            $structureGetter = app(\App\Modules\LocalEntity\Actions\AppEntityStructureGetter::class);
            foreach ($relatedEntitiesKey as $entityKey){
                $structure = $structureGetter->buildEntity($entityKey);
                $entityPermission = $structure->getPermissions();
                if (!isset($entityPermission->$action)){
                    $entityKeys[] = $entityKey;
                    continue;
                }
                if (Permissions::checkPermission($entityPermission->$action ?? [])) {
                    $entityKeys[] = $entityKey;
                }
            }
            return $entityKeys;
        }
    }
}


if (!function_exists('getEntityCustomPaginationParams')){
    /**
     * @param $entityKey
     * @return array
     * desc the returned value will be appended to the pagination links in case you had custom filters
     */
    function getEntityCustomPaginationParams($entityKey): array
    {
        $params = [];
        switch ($entityKey) {
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY:
                if (request()->status && in_array(request()->status, ['all', 'approved', 'rejected'])) {
                    $params['status'] = request()->status;
                }
                return $params;
            case EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY:
                if (request()->status && in_array(request()->status , ['open','closed'])){
                    $params['status'] = request()->status;
                }
                return $params;
            case EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY:
                if (request()->stock_request_ids){
                    $params['stock_request_ids'] =  request()->stock_request_ids;
                }
                return $params;
            default:
                return $params;
        }
    }
}

if (!function_exists('isEntityListable')){
    function isEntityListable($entityKey) : ?bool
    {
        return isOwner() ?: !in_array($entityKey, [EntityKeyTypesUtil::EMAIL_TEMPLATE_LOG]);
    }
}
