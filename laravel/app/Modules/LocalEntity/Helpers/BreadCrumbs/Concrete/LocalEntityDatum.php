<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 3/22/20
 * Time: 9:09 AM
 */

namespace App\Modules\LocalEntity\Helpers\BreadCrumbs\Concrete;


use App\Helpers\BreadCrumbs\Concrete\Ordinary;
use App\Modules\LocalEntity\Utils\LocalEntityTypeUtil;
use Izam\Daftra\ActivityLog\TitleConstructor;
use Izam\Daftra\Common\EntityStructure\Entity;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class LocalEntityDatum extends Ordinary
{
    public $pageTypeMapping = ['list' => ['type' => SELF::BC_LISTING_PAGE_TYPE]];
    protected $parentRecord = null;

    public function __construct($repo, $url, $entity = null)
    {
        parent::__construct($repo, $url, $entity);
    }

    public function getParents()
    {
        $result = parent::getParents();
        try {
            /**
             * @var Entity $entity
             */
            $entity=  $this->entity;

            if($entity && $entity->getParentEntityData()) {
                if(empty($this->parentRecord)) {
                    if($this->data) {
                        $parentRecord = $this->repo->getRecordFromTable($this->entity->getParentEntityData()->getEntity()->getTable(), $this->data->reference_id);
                    } else if(isset($this->url['params'][0][1])) {
                        $parentRecord = $this->repo->getRecordFromTable($this->entity->getParentEntityData()->getEntity()->getTable(), $this->url['params'][0][1]);
                    }
                } else {
                    $parentRecord = $this->parentRecord;
                }
                switch ($this->entity->getParentEntityData()->getEntity()->getEntityKey()) {
                    case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                        $parentBc = [
                            ['link' => route('owner.staff.index'), 'title' => 'Employees'],
                            ['link' => route('owner.staff.show', ['staff' => $parentRecord->id]), 'title' => "#{$parentRecord->id} {$parentRecord->full_name}"],
                        ];
                        $result = array_merge($parentBc, $result);
                        break;
                    case EntityKeyTypesUtil::CONTRACT:
                        if(!empty($parentRecord)) {
                            $parentBc = [
                                ['link' => route('owner.contracts.index'), 'title' => 'Contracts'],
                                ['link' => route('owner.contracts.show', ['id' => $parentRecord->id]), 'title' => sprintf(__t('%s #%s'), __t('Contract'), $parentRecord->code)],
                            ];
                            $result = array_merge($parentBc, $result);
                        }
                    case EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                        if(!empty($parentRecord)) {
                            $parentBc = [
                                ['link' => '/owner/clients', 'title' => 'Clients'],
                                ['link' => '/owner/clients/view/'.$parentRecord->id, 'title' => sprintf(__t('%s #%s'), $parentRecord->business_name, $parentRecord->client_number)],
                            ];
                            $result = array_merge($parentBc, $result);
                        }
                        break;
                    case EntityKeyTypesUtil::REQUEST_ENTITY_KEY:
                        $parentBc = [
                            ['link' => '/v2/owner/request_types/manage', 'title' => __t('Manage Requests')],
                        ];
                        $result = array_merge($parentBc, $result);
                        break;
                    case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY:
                        if(!empty($parentRecord)) {
                            $parentBc = [
                                ['link' => '/owner/suppliers', 'title' => 'Suppliers'],
                                [
                                    'link' => '/owner/suppliers/view/'.$parentRecord->id,
                                    'title' => sprintf(__t('%s #%s'), $parentRecord->business_name, $parentRecord->supplier_number)
                                ],
                            ];
                            $result = array_merge($parentBc, $result);
                        }
                        break;
                }
                if($this->entity->getParentEntityData()->getEntity()->getExtendedEntityKeyOrEntityKey() === EntityKeyTypesUtil::WORK_ORDER) {
                    $relatedEntityParts = explode('-',$this->entity->getParentEntityData()->getEntity()->getEntityKey());
                    $workflowTypeId = end($relatedEntityParts);
                    $workFlowType = $this->repo->getRecordFromTable('workflow_types', $workflowTypeId);
                    $workFlowTypeBc = [
                        [
                            'link' => route('owner.workflows.workflow.type.list',[$this->entity->getParentEntityData()->getEntity()->getEntityKey()]),
                            'title' => "$workFlowType->name"
                        ]
                    ];
                    if ($this->pageData['type'] == self::BC_VIEW_PAGE_TYPE || $this->pageData['type'] == self::BC_EDIT_PAGE_TYPE) {
                        $tableName = $this->entity->getParentEntityData()->getEntity()->getTable();
                        $id = $this->data->{$this->entity->getParentEntityData()->getForeignKeyName()};
                        $parentRecord = $this->repo->getRecordFromTable($tableName, $id);

                        $workFlowTypeBc[] = [
                            'link' => getCakeURL(['controller' => 'work_orders', 'action' => 'workflow_view', $id]),
                            'title' => "#{$parentRecord->number} {$parentRecord->title}"
                        ];
                    } else {
                        $workFlowId = $this->url['params'][0][1]??null;
                        if ($workFlowId) {
                            $workFlow = $this->repo->getRecordFromTable('work_orders', $workFlowId);
                            $workFlowTypeBc[] = [
                                'link' => getCakeURL(['controller' => 'workflows', 'action' => 'view', $workFlowId]),
                                'title' => "#{$workFlow->number} {$workFlow->title}"
                            ];
                        }
                    }

                    $result = array_merge($workFlowTypeBc, $result);
                }
            }
        }catch (\Exception $exception) {
        } finally {
            return $result;
        }
    }


    public function getListingTitle()
    {
        $listingTitle['title'] = __t($this->entity->getLabel());
        $regex = '/le_workflow-type-entity-\d+/';
        if (preg_match($regex, $this->entity->getEntityKey(), $matches)) {
            $listingTitle['link'] = route('owner.workflows.workflow.type.list', [$this->entity->getEntityKey()]);
        } else {
            if(
                $this->entity->getType() === LocalEntityTypeUtil::CHILD_ITEM &&
                preg_match($regex,$this->entity->getParentEntityData()->getEntityKey()) &&
                $this->data
            ) {
                $listingTitle['link'] = "/owner/work_orders/workflow_view/{$this->data->reference_id}";
            } else {
                $listingTitle['link'] = route('owner.entity.list', [$this->entity->getEntityKey()]);
            }
        }
        return $listingTitle;
    }

    /**
     * return the create page title and link
     * @return array ['title' => $title, 'link' => $link]|null
     */
    function getCreateFormTitle(){
        $title = 'Add';
        $params = $this->url['params'] ?? [];
        $link = route('owner.local_entities_data.create', [$this->entity->getEntityKey()]);
        return ['link' => $link, 'title' => __t($title)];
    }

    /**
     * return the update page title and link
     * @return array ['title' => $title, 'link' => $link]|null
     */
    function getUpdateFormTitle(){
        if(empty($this->data))
        {
            return null;
        }
        $title = 'Edit';
        $params = $this->url['params'] ?? [];
        $link = route('owner.local_entities_data.edit', [$this->entity->getEntityKey(), $this->data->id]);
        return ['link' => $link, 'title' => __t($title)];
    }


    /**
     * sets the record data used in case of an edit or view
     */
    protected function setData(){
        $id = $this->url['params'][0][1]??false;
        if(isset($this->url['name']) && 'owner.child.create' === $this->url['name'] && empty($this->url['queryParams']['iframe'])) {
            $this->parentRecord = $this->repo->getRecordFromTable($this->entity->getParentEntityData()->getEntity()->getTable(), $this->url['params'][0][1]);
        }
        if ($id) {
            $record = $this->repo->find($this->entity, $id);
            if ($record)
            {
                $this->data = $record;
            }
        } else {
            $this->data = null;
        }
    }

    function getViewTitle(){
        if(empty($this->data))
        {
            return null;
        }
        if(!empty($this->entity) && $this->entity->getListingField() && $this->entity->getListingField() != null && $this->entity->getListingField()->getName() != 'id')
        {
            $displayField = $this->data->{$this->entity->getListingField()->getName()};
        } else {
            $displayField = $this->getDisplayField();
            $displayField = \DateTime::createFromFormat('Y-m-d', $displayField) ? formatForView($displayField, \App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : $displayField;
//        $numberField = $this->url['params'][0][1]??'';
//        if((method_exists( $this->entity,'getListingField') ? $this->entity->getListingField()->getName() : $this->entity->name_field) == "code"){
//            $numberField = $this->data->code;
//        }
        }
        $title = TitleConstructor::getTitle($this->entity, (array) $this->data);
        $id = $this->data->id;
        if($id)
        {
            $params = $this->url['params'];
            $regex = '/le_workflow-type-entity-\d+/';
            if (preg_match($regex, $this->entity->getEntityKey(), $matches)) {
                $link = getCakeURL(['controller' => 'work_orders', 'action' => 'workflow_view', $id]);
            } else {
                $link = route(strtolower('owner.entity.show'), [$this->entity->getEntityKey(), $id]);
            }
            return ['link' => $link, 'title' => $title];
        }else{
            return null;
        }

    }

    public function getCloneFormTitle()
    {
        $title = 'Clone';
        $params = $this->url['params'] ?? [];
        $link = route('owner.entity.clone', $params[0]);
        return ['link' => $link, 'title' => __t(getCustomCloneTitle($this->entity->getEntityKey()))];
    }

    function getDisplayField()
    {
        return $this->entity->getLabel();
    }


}
