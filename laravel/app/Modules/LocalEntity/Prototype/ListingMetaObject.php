<?php


namespace App\Modules\LocalEntity\Prototype;

use App\Modules\LocalEntity\Prototype\DataStructure\Filter;
use App\Modules\LocalEntity\Prototype\DataStructure\ICondition;
use App\Modules\LocalEntity\Prototype\DataStructure\Sort;

class ListingMetaObject
{
    /**
     * @var Filter $conditions
     */
    private $conditions = [];

    /**
     * @var array $sorts;
     */
    private $sorts = [];


    /**
     * @var IMode $mode
     */
    private $mode;



    public function setFilter(ICondition $filterMeta)
    {
        $this->conditions[] = $filterMeta;
        return $this;
    }

    public function setSorting(Sort $sortMeta)
    {
        $this->sorts[] = $sortMeta;
    }

    public function getSorting()
    {
        return $this->sorts;
    }

    public function getMode()
    {
        return $this->mode;
    }

    public function getFilters()
    {
        return $this->conditions;
    }


    public function setMode($mode)
    {
        $modeObject = null;
        $fields = $mode['fields'] ?? '*';
        switch ($mode['type']??'') {
            case 'listing';
                $modeObject = new ListingMeta($mode['fields']??[], $mode['per_page'] ?? null);
                break;

            case 'count':
                $modeObject = new CountMeta();
                break;
            case 'api':
                $modeObject = new PaginationMetaApi($mode['per_page'], $mode['page']??null, $fields);
                break;
            case 'export':
                $modeObject = new ExportMeta($mode['fields']);
                break;
            default:
                $modeObject = new PaginationMeta($mode['per_page'], $mode['page']??null);
                break;
        }
        $this->mode = $modeObject;
    }
    public function set($key, $value)
    {
        $this->$key = $value;
    }
    public function get($key)
    {
        return $this->$key ?? NULL;
    }

}
