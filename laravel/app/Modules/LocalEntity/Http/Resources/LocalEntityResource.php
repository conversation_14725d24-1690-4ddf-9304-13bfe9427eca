<?php
namespace App\Modules\LocalEntity\Http\Resources;

use App\Utils\ActiveStatusUtil;
use Illuminate\Http\Resources\Json\JsonResource;

class LocalEntityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'key' => $this->key,
            'name_field' => $this->name_field,
            'parent_entity' => $this->parent_entity,
        ];
    }
}