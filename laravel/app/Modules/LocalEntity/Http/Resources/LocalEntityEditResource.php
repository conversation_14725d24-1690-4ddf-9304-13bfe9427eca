<?php
namespace App\Modules\LocalEntity\Http\Resources;

use App\Utils\ActiveStatusUtil;
use Illuminate\Http\Resources\Json\JsonResource;
use Izam\Booking\Resources\ServiceFormResource;
use Izam\Booking\Utils\ServiceFormsFillingTimeUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class LocalEntityEditResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        return [
            'permission' => $this['related_form_data']['permission'],
            'entity_permissions' => $this['related_form_data']['permissions'],
            'permission_filter_criteria' => $this['related_form_data']['value_options_criteria_type'],
            'permission_filter_url' => route('api.local_entities.permissions.criteria_type_data', ['type' => '_type_', 'q' => '_q_']),
            'status_list' => ActiveStatusUtil::getStatuOptions(),
            'name_field_options' => $this['nameFieldOptions'],
            'label' => $this['form_record']['label'],
            'key' => $this['form_record']['key'],
            'status' => $this['form_record']['status'],
            'description' => $this['form_record']['description'],
            'row' => new LocalEntityResource(resource: $this['form_record']),
            EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => [
                "filling_time" => ServiceFormsFillingTimeUtil::getOptions(),
                "row" => new ServiceFormResource($this[EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY]),
            ],
        ];
    }
}