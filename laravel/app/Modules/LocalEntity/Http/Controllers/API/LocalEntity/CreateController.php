<?php

namespace App\Modules\LocalEntity\Http\Controllers\API\LocalEntity;

use App\Modules\LocalEntity\Http\Resources\LocalEntityCreateResource;
use App\Modules\LocalEntity\Services\LocalEntityService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Exception;

class CreateController
{
    public function __construct(
        private LocalEntityService $service,
    ) {
    }

    public function __invoke(Request $request)
    {
        try {
            $formData = $this->service->getFormData();
            return new JsonResponse([
                'status' => true,
                'message' => '',
                'data' => new LocalEntityCreateResource($formData),
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return new JsonResponse([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}
