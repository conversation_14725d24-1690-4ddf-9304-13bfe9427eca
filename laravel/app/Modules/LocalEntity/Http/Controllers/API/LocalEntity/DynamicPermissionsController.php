<?php

namespace App\Modules\LocalEntity\Http\Controllers\API\LocalEntity;

use App\Helpers\DynamicPermission\DynamicPermissionHelper;
use App\Http\Requests\ListValuesOfPermissionCriteriaRequest;
use App\Services\EntityPermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Exception;


class DynamicPermissionsController
{
    public function __construct(
        private EntityPermissionService $service,
        private DynamicPermissionHelper $dynamicPermissionHelper
    ) {
        $this->service = $service;
    }

    public function __invoke(ListValuesOfPermissionCriteriaRequest $request): JsonResponse
    {
        try {
            $data = $this->dynamicPermissionHelper->getValuesOfCriteriaType($request->type, [], $request->q);
            return new JsonResponse([
                'status' => true,
                'message' => '',
                'data' => $data,
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return new JsonResponse([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
