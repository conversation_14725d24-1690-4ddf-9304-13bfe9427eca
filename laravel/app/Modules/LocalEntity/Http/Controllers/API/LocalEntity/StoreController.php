<?php

namespace App\Modules\LocalEntity\Http\Controllers\API\LocalEntity;

use App\Events\LocalEntityBeforeCreate;
use App\Exceptions\PluginIsNotInstalled;
use App\Exceptions\UnexpectedBehavior;
use App\Modules\LocalEntity\Services\LocalEntityService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Exception;
use Illuminate\Validation\ValidationException;

class StoreController
{
    public function __construct(
        private LocalEntityService $service,
    ) {
    }

    public function __invoke(Request $request)
    {
        try {
            LocalEntityBeforeCreate::dispatch($request);
            $this->service->add($request);
            return new JsonResponse([
                'status' => true,
                'message' => sprintf(__t('%s Added Successfully'), $request->get('label')),
                'data' => [
                    'targert_url' => getCustomViewRoute(['entity_key' => $request->get('parent_entity'), 'id' => $request->get('id')])
                ],
            ], Response::HTTP_OK);
        } catch (ValidationException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return new JsonResponse([
                'status' => false,
                'message' => __t('The given data was invalid, Please Check Errors Below'),
                'errors' => $errors,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (UnexpectedBehavior | PluginIsNotInstalled $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return new JsonResponse([
                'status' => false,
                'message' => $exception->getMessage(),
                'errors' => $errors,
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return new JsonResponse([
                'status' => false,
                'message' => $exception->getMessage(),
                'errors' => $errors,
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}
