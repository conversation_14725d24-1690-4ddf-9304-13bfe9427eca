<?php

namespace App\Modules\LocalEntity\Http\Controllers\API\LocalEntity;

use App\Modules\LocalEntity\Http\Resources\LocalEntityEditResource;
use App\Modules\LocalEntity\Services\LocalEntityService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Exception;
use Izam\Booking\Services\ServiceFormService;
use Izam\Booking\Utils\ServiceFormUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class EditController
{
    public function __construct(
        private LocalEntityService $service,
        private ServiceFormService $serviceFormService
    ) {
    }

    public function __invoke(string $entityKey)
    {
        try {
            $formData = $this->service->getFormData($entityKey);
            if (str_starts_with($entityKey, ServiceFormUtil::getAdditionalPrefix())) {
                $this->getParentEntityData($entityKey, $formData);
            }
            return new JsonResponse([
                'status' => true,
                'message' => '',
                'data' => new LocalEntityEditResource($formData),
            ], Response::HTTP_OK);
        } catch (Exception $exception) {
            return new JsonResponse([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function getParentEntityData($entityKey, &$formData)
    {
        $id = str_replace(ServiceFormUtil::getAdditionalPrefix(), '', $entityKey);
        if(empty($id)){
            return;
        }
        $formData[EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY] = $this->serviceFormService->find($id);
    }

}
