<?php

namespace App\Modules\LocalEntity\Http\Controllers;

use App\Events\Workflow\WorkflowDeletedEvent;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\PluginIsNotInstalled;
use App\Exceptions\UnexpectedBehavior;
use App\Facades\CakeSession;
use App\Facades\Permissions;
use App\Facades\Plugins;
use App\Helpers\DynamicPermission\DynamicPermissionHelper;
use App\Http\Controllers\BaseController;
use App\Models\BaseModel;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Helpers\FieldSavedCallback;
use App\Modules\LocalEntity\Http\Requests\SaveFieldsRequest;
use App\Modules\LocalEntity\Repositories\LocalEntityRepository;
use App\Modules\LocalEntity\Services\LocalEntityService;
use App\Modules\LocalEntity\Utils\LocalEntityTypeUtil;
use App\Repositories\WorkflowTypeRepository;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PermissionUtil;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Izam\Daftra\Common\Events\EntityStructureSaved;

/**
 * Class LocalEntityController
 *
 * @property LocalEntityService $service
 *
 * @package App\Http\Controllers
 */
class LocalEntityController extends BaseController
{
    public $folder = 'LocalEntity::local_entities';
    public $routeUrl = 'local_entities';
    protected $service;
    protected $label = 'Local Entity';


    function __construct(LocalEntityService $service, private AppEntityStructureGetter $structureGetter)
    {
        parent::__construct($service);
    }

    public function getFormValidationRules(Request $request, $id = null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $rules = [
            'label' => 'required',
            'key' => 'required|not_regex:/^le_(.*)/|unique:currentSite.local_entities,key,' . ($id ? $id : 'NULL') . ',key',
            'status' => 'required|in:0,1',
        ];
        $rules = array_merge($rules, DynamicPermissionHelper::getValidationRules(EntityKeyTypesUtil::LOCAL_ENTITY));
	    $messages = DynamicPermissionHelper::getValidationMessages();
        if($id) {
            $rules['key'] = 'nullable';
            $rules['name_field'] = "nullable|exists:currentSite.local_entity_fields,key,entity,$id,deleted_at,NULL";
        }
        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    public function builder($entityKey, AppEntityStructureGetter $getter)
    {
        try {
            if (
                (isRentalRelatedCustomEntity($entityKey) && !Permissions::checkPermission(PermissionUtil::MANAGE_RENTAL_SETTINGS) && !Permissions::checkPermission(PermissionUtil::Edit_General_Settings))
                || (isWorkFlowTypeEntity($entityKey) && !Permissions::checkPermission(PermissionUtil::MANAGE_WORKFLOW_SETTINGS))
            ) {
                CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
                return redirect('/');
            }
            $entity = $getter->buildEntity($entityKey);

            if(!$this->hasPermissionToSetting(request()->get('related_entity'))){
                CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
                return redirect()->route('owner.dashboard');
            }

            if ($entity->isExtendedEntity()) {
                return $this->createCustomDataEntity($entityKey, $getter);
            }
            return view('LocalEntity::local_entities.owner.builder');
        } catch (EntityNotFoundException $e) {
            return redirect()->route($this->routesPrefix . $this->routeUrl . '.index', $this->queryParameters)
                ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t($this->label)));
        }
    }

    protected function getRouteName(string $action): string
    {
        $name = strtolower($this->routesPrefix . $this->routeUrl . '.' . $action);
        $routeName = strtolower($this->routesPrefix . $this->route . '.' . $action);


        if (Route::has($routeName)) {
            $name = $routeName;
        }
        return $name;
    }

    public function index(Request $request)
    {
        if ($request->get('save')) {
            $request->session()->flash('success', sprintf(__t('%s Saved Successfully'), __t('Entity')));
        }
        if(!$this->hasPermissionToSetting(request()->get('parent_entity'))){
            CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
            return redirect()->route('owner.dashboard');
        }
        if (request()->has('parent_entity')) {
            try {
                $structure = $this->structureGetter->buildEntity(request()->get('parent_entity'));
            } catch (\Exception $e) {
                flashMessageIntoCake(sprintf("Unsupported entity type: %s", request()->get('parent_entity')), 'Errormessage');
                return redirect(CAKE_BASE_URL);
            }
            if ($structure->getPlugin() && !Plugins::pluginActive($structure->getPlugin())) {
                flashMessageIntoCake(__t('Entity Plugin Not Active'), 'Errormessage');
                return redirect(CAKE_BASE_URL);
            }
        }

        $listingData = $this->service->listing();

        if ($request->get('parent_entity')) {
            $listingData['forcedEntityName'] = __t('Custom Form');
        }

        if (isApi()) {
            return response()->json($listingData['pagination']->toArray(), 200);
        } else {
            $blade = $this->blade ?? 'index';
            return view($this->folder . $this->viewsPrefix . '.' . $blade, $listingData);
        }
    }

    public function ajaxNameFilter(Request $request)
    {
        $query = $request->query->get('q');
        if (!empty($query)) {
            $results = $this->service->filterAjax($query);
            if ($results) {
                $processedResults = [];
                foreach ($results as $k => $v) {
                    $processedResults['results'][$k] = ['text' => "#{$v->key} {$v->label}", 'id' => $v->key];
                }
                return response()->json($processedResults);
            }
        }
        return response()->json([]);
    }

    public function saveFields(SaveFieldsRequest $request, $entityKey, FieldSavedCallback $fieldSavedCallback, LocalEntityRepository $repository){
        try {
            \DB::transaction(function () use ($request, $entityKey, $fieldSavedCallback) {
                $this->service->saveFields($entityKey, $request->fields, $fieldSavedCallback);
            });
            Session::flash('success', sprintf(__t('%s Saved Successfully'), __t($this->label)));
            $this->izamFlashMessage(sprintf(__t('%s Saved Successfully'), __t($this->label)), 'success');
            dispatch_event_action(new EntityStructureSaved($entityKey));
            $entity = $repository->find($entityKey);
            $data = $this->getRedirectUrl($entity, $request->headers->get('referer'));
            return response()->json($data, 200);
        } catch (\App\Modules\LocalEntity\Exceptions\ValidationLocalEntitiesValidation $exception) {
            return response()->json(['error' => $exception->getMessage()], 422);
        } catch (\App\Modules\LocalEntity\Exceptions\CanNotDeleteUsedField $exception) {
            return response()->json(['message' => $exception->getMessage()], 404);
        } catch (\Exception $exception) {
            return response()->json(['message' => __t('Something Went Wrong')], 500);
        }
    }

    public function getFields($entityKey)
    {
        try {
            return response()->json($this->service->getBuilderFields($entityKey));
        }catch (EntityNotFoundException $exception) {
            return response()->json(['message' => 'entity Not Found'],404);
        }catch (\Exception $exception) {
            return response()->json(['message' => 'error'],400);
        }

    }

    public function getViewInParentList(LocalEntityService $localEntityService)
    {
        $localEntities = $localEntityService->all();
        $listInParentEntities = include(resource_path('/utils/view-in-parent.php'));
        foreach($localEntities as $localEntity) {
            $listInParentEntities[] = $localEntity->key;
        }
        return response()->json($listInParentEntities);
    }

    public function getLocalEntityConfig()
    {
        $dateFormat = getCurrentSite('date_format');
        $jsFormats = getDateFormats('moment_js');
        $site = getCurrentSite();
        $owner = getAuthOwner();
        return response()->json(
            [
                'siteLogoUrl' => (isset($owner['site_logo_full_path'] ) ? ($owner['site_logo_full_path']."?w=200&h=100"): null),
                'dateFormat' => $jsFormats[$dateFormat],
                'businessName' => $site['business_name']
            ], 200);
    }

    protected function getShowRouteUrl(BaseModel $model)
    {
        return route($this->routesPrefix . $this->routeUrl . '.builder', ['entityKey' => $model->key]);
    }

    /**
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     * @throws \Exception
     */
    public function update(Request $request, $id)
    {
        try {
            $this->handledExceptions = array_merge($this->handledExceptions,[
                UnexpectedBehavior::class,
                ValidationException::class,
                PluginIsNotInstalled::class,
            ]);
            return parent::update($request, $id);
        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            $allInputs = $request->all();
            $allInputs = $this->service->prepareInputs($allInputs);
            return redirect()->back()
                    ->withInput($allInputs)
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
        } catch (PluginIsNotInstalled | UnexpectedBehavior $exception) {
            return redirect()->back()
                ->with('danger', __t($exception->getMessage()));
        } catch (\Exception $exception) {
            return redirect()->back()
                ->with('danger', __t('Some thing went wrong '));
        }
    }

	/**
	 * Store a newly created resource in storage.
	 * @param \Illuminate\Http\Request $request
	 * @return \Illuminate\Http\RedirectResponse
	 * @throws \Exception
	 */
	public function store(Request $request)
	{
		try {
			$this->handledExceptions = array_merge($this->handledExceptions,[
				UnexpectedBehavior::class,
				ValidationException::class,
				PluginIsNotInstalled::class,
			]);
			return parent::store($request);
		} catch (ValidationException $exception) {
			$exceptionMessage = 'The given data was invalid, Please Check Errors Below';
			$errors = method_exists($exception, 'errors') ? $exception->errors() : [];
				$allInputs = $request->all();
				$allInputs = $this->service->prepareInputs($allInputs);
				return redirect()->back()
					->withInput($allInputs)
					->withErrors($errors)
					->with('danger', __t($exceptionMessage));
		} catch (PluginIsNotInstalled | UnexpectedBehavior $exception) {
			return redirect()->back()
				->with('danger', __t($exception->getMessage()));
		} catch (\Exception $exception) {
            return redirect()->back()
                ->with('danger', __t('Some thing went wrong '));
        }
	}

    public function listAllEntities()
    {
        $entitiesList = $this->service->getAllSystemEntitiesList();
        return response()->json($entitiesList, 200);
    }

    public function listEntityFields($entityKey)
    {
        try{
            $fields = $this->service->listEntityFields($entityKey);
            return response()->json($fields, 200);
        }catch (EntityNotFoundException $exception)
        {
            return response()->json(['message' => __t('This Record Doesn\'t Exist')], 404);
        }
    }

    public function getFieldValues($fieldKey)
    {
        return redirect(route('entity.list-photo', [
            'entityKey' => explode('.', $fieldKey)[0], 'dataLevel' => 1, 'fields' => [explode('.', $fieldKey)[1]],
            'mode' => 'listing',
        ]));
        try{
            $values = $this->service->getFieldValues($fieldKey);
            return response()->json($values, 200);
        }catch (EntityNotFoundException $exception)
        {
            return response()->json(['message' => $exception->getMessage()], 404);
        }
    }

    public function create()
    {
        if (isWorkFlowTypeEntity(\request('parent_entity')) && !Permissions::checkPermission(PermissionUtil::MANAGE_WORKFLOW_SETTINGS)) {
            CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
            return redirect('/');
        } elseif (!isWorkFlowTypeEntity(\request('parent_entity')) && !Permissions::checkPermission(PermissionUtil::Edit_General_Settings)) {
            CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
            return redirect('/');
        }
        return parent::create();
    }

    public function edit($id)
    {
        if (isWorkFlowTypeEntity(\request('parent_entity')) && !Permissions::checkPermission(PermissionUtil::MANAGE_WORKFLOW_SETTINGS)) {
            CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
            return redirect('/');
        } elseif (!isWorkFlowTypeEntity(\request('parent_entity')) && !Permissions::checkPermission(PermissionUtil::Edit_General_Settings)) {
            CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
            return redirect('/');
        }
        return parent::edit($id);
    }

    public function createCustomDataEntity($entity, AppEntityStructureGetter $getter)
    {
        $st = $getter->buildEntity($entity);
        if(!in_array($entity, [
            EntityKeyTypesUtil::CONTRACT,
            EntityKeyTypesUtil::STAFF_ENTITY_KEY,
            EntityKeyTypesUtil::PAYSLIP,
            EntityKeyTypesUtil::RENTAL_UNIT,
            EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER,
            EntityKeyTypesUtil::CLIENT_ENTITY_KEY,
            EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY,
            EntityKeyTypesUtil::PURCHASE_INVOICE,
            EntityKeyTypesUtil::JOURNAL,
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY,
            EntityKeyTypesUtil::PRODUCTION_PLAN,
            EntityKeyTypesUtil::ASSET,
            EntityKeyTypesUtil::LEASE_CONTRACT,
        ]) && !$st->isExtendedEntity()) {
            return \redirect()-> route('owner.local_entities.index')->with('danger', 'This Entity Does Not Support Local Entity');
        }

        $entityObject = $this->service->createCustomEntityIfNotExists($entity);
        $routeParams = [ 'entityKey' =>  $entityObject->key, 'related_entity' => $entity];
        if(request()->get('redirect')) {
            $routeParams['redirect'] = request()->get('redirect');
        }
        return \redirect()->route('owner.local_entities.builder',$routeParams );
    }

    public function hasPermissionToSetting($entityKey){
        $permissions = match ($entityKey) {
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY,EntityKeyTypesUtil::PRODUCTION_PLAN => PermissionUtil::MANAGE_MANUFACTURING_SETTINGS,
            default => null
        };
        if(!empty($permissions)){
            return Permissions::checkPermission($permissions);
        }
        return true;
    }

    public function destroy($id)
    {
        try {
            $leRecord = $this->service->find($id);
            $this->service->delete($id);

            if ($leRecord && preg_match('/le_workflow-type-entity-\d+/', $leRecord->key)) {
                /** @var $workflowTypeRepository WorkflowTypeRepository */
                $workflowTypeRepository = resolve(WorkflowTypeRepository::class);

                $workflow = $workflowTypeRepository->findWhere(['entity_key' => $id])->first();

                /** @var $appEntityShowAction AppEntityShowAction*/
                $appEntityShowAction = resolve(AppEntityShowAction::class);

                $data = $appEntityShowAction->handle(EntityKeyTypesUtil::WORK_FLOW_TYPE, $workflow->id, 2);

                $workflowTypeRepository->delete($workflow->id);

                dispatch_event_action(new WorkflowDeletedEvent((array)$data));
                delete_menus();
            }

            if (isApi()) {
                return response()->json([], 200);
            }else if(isset($this->service->parameters['tab'])) {
                return redirect(back()->getTargetUrl().'#'.$this->service->parameters['tab'])
                    ->with('success', sprintf(__t('%s Deleted Successfully'), __t($this->label)));
            } else {
                return redirect()->route($this->getRouteName('index'), $this->queryParameters)
                    ->with('success', sprintf(__t('%s Deleted Successfully'), __t($this->label)));
            }
        } catch (QueryException $exception) {
            Log::error('Base Controller Query Exception on Destroy', ['Message' => $exception->getMessage(), 'Exception' => $exception]);
            if (isApi()) {
                return response()->json(['message' => sprintf(__t('%s Deleting Failed'), __t($this->label))], 400);
            }else if(isset($this->service->parameters['tab'])) {
                return redirect(back()->getTargetUrl().'#'.$this->service->parameters['tab'])
                    ->with('danger', sprintf(__t('%s Deleting Failed'), __t($this->label)));
            }
            else {
                return redirect()->route($this->getRouteName('index'), $this->queryParameters)
                    ->with('danger', sprintf(__t('%s Deleting Failed'), __t($this->label)));
            }
        } catch (\Exception $exception) {
            if (in_array(get_class($exception), $this->handledExceptions)) {
                throw $exception;
            }
            if (isApi()) {
                return response()->json(['message' => __t($exception->getMessage())], 400);
            }else if(isset($this->service->parameters['tab'])) {
                return redirect(back()->getTargetUrl().'#'.$this->service->parameters['tab'])
                    ->with('danger', __t($exception->getMessage()));
            } else {
                return redirect(back()->getTargetUrl())
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }

    private function getRedirectUrl($entity, $referrer): array
    {
        $data = [];
        if ($entity->type === LocalEntityTypeUtil::CHILD_ITEM) {
            $data['redirect'] = route('owner.local_entities.index', ['save' => true, 'parent_entity' => $entity->parent_entity]);
        }
        if ($referrer) {
            $queryString = parse_url($referrer, PHP_URL_QUERY);
            parse_str($queryString, $queryParams);
            return  $queryParams;
        }
        return $data;
    }

}
