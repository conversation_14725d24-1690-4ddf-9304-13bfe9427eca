<?php

namespace App\Modules\LocalEntity\Http\Controllers\Entity;

use App\Http\Controllers\Controller;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Actions\ListAction;
use App\Modules\LocalEntity\Factories\EntityIdsExtractor\EntityIdsExtractorFactory;
use App\Modules\LocalEntity\Listing\Parser\FormParser;
use IzamViewData;

class EntityBaseBulkActionController extends Controller
{
    const PER_PAGE = 10000;
    const LEVEL = 2;

    public function __construct(
        private FormParser               $parser,
        private ListAction               $action,
        private AppEntityStructureGetter $structureGetter,
    )
    {
        new IzamViewData();
    }

    public function getBulkIds($entityKey)
    {
        if ((!request()->has('ids') || empty(request()->get('ids'))) && empty(request()->get('id'))) {
            return redirect("/owner/entity/$entityKey/list");
        }
        $ids = request()->get('ids', request()->get('id'));
        if ('all' == $ids) {
            $structure = $this->structureGetter->buildEntity($entityKey);
            if ((request()->has('filter_query') || request()->has('conditions_link')) && !request()->has('filter')) {
                $this->handleFilters();
            }
            request()->merge(['per_page' => static::PER_PAGE]);
            $data = $this->action->handle($structure, static::LEVEL, $this->parser, [], request()->all());
            $ids = EntityIdsExtractorFactory::extractIds($data, $entityKey);
        }

        return $ids;
    }

    private function handleFilters()
    {
        $filters = [];
        if(request()->get('filter_query')) {
            $filterQuery = request()->get('filter_query');
            $filterQuery = json_decode($filterQuery, true);
            $filters = $filterQuery['filter'] ?? [];
            $filters = array_filter($filters, function ($value) {
                return $value != "" ;
            });
        } else if(request()->get('conditions_link')){
            $filterQuery = request()->get('conditions_link');
            parse_str(request()->get('conditions_link'), $filterQuery);
            $filters = $filterQuery['filter'];
            $filters = array_filter($filters, function ($value) {
                return $value != "" ;
            });
        }
        $customFilters = $filterQuery['custom_filter'] ?? [];
        request()->merge(
            [
                'filter' => $filters,
                'custom_filter' => $customFilters,
            ]
        );
        if ($customFilters) {
            $this->setCustomFilters($customFilters, $filters);
        }
    }

    private function setCustomFilters($customFilters = [], $filters = []): void
    {
        $temp = [];
        foreach ($customFilters as $customFilter) {
            parse_str($customFilter, $output);
            $temp = array_merge($temp, $output);
        }
        $temp = array_merge($filters, $temp);
        request()->merge(['filter' => $temp]);
    }

}
