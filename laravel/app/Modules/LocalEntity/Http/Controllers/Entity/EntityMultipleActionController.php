<?php

namespace App\Modules\LocalEntity\Http\Controllers\Entity;

use App\Http\Controllers\Controller;
use App\IzamViews\IzamView;


class EntityMultipleActionController extends EntityBaseBulkActionController
{
    public function getIds($entityKey, $redirectMethod = 'POST')
    {
        $view = 'bulk/redirect';
        if(Request()->has('data')){
           $id = Request()->get('data')['Client']['ids'];
           $filter_query =  Request()->get('filter_query');
           $toUrl = Request()->toUrl;
           request()->merge(['ids' => $id , 'toUrl' => $toUrl , 'filter_query' => $filter_query]);
        }
        $url = getCakeURL(request()->toUrl);
        $ids = $this->getBulkIds($entityKey);

        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            
            return response()->json([
                'ids' => $ids,
                'url' => $url,
            ],200);
        }

        if($redirectMethod == 'GET'){
            $query = http_build_query(['ids' => $ids]);
            return redirect("{$url}?{$query}");
        } 

        return new IzamView($view, [
            'ids' => $ids,
            'entity_key' => $entityKey,
            'url' => $url,
        ]);
    }
}
