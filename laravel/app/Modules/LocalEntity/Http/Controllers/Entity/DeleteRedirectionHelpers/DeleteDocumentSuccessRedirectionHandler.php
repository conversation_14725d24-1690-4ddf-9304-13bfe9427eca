<?php

namespace App\Modules\LocalEntity\Http\Controllers\Entity\DeleteRedirectionHelpers;

use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\RedirectHelperTrait;
use Illuminate\Support\Str;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\Daftra\Common\EntityStructure\Entity;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class DeleteDocumentSuccessRedirectionHandler extends DeleteEntityDefaultSuccessRedirectionHandler{



    public function redirect(){
        return redirect()->to($this->getRedirectionUrl());
    }

    private function getRedirectionUrl(){
        return route('owner.staff.show', $this->data['entity_id']) . '#documents';
    }


}
