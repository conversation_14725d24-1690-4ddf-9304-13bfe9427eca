<?php

namespace App\Modules\LocalEntity\Http\Controllers\Entity\DeleteRedirectionHelpers;

use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\EntityStructure\AppEntityData;

class DeleteEntitySuccessRedirectionHandlerFactory{

    public static function create($entityStructure, AppEntityData $data) : DeleteEntityDefaultSuccessRedirectionHandler{
        return match($entityStructure->getEntityKey()){
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => new DeleteStockRequestSuccessRedirectionHandler($entityStructure, $data),
            EntityKeyTypesUtil::ENTITY_DOCUMENT => new DeleteDocumentSuccessRedirectionHandler($entityStructure, $data),
            default => new DeleteDefaultRedirectionHandler($entityStructure, $data)
        };
    }
}