<?php

namespace App\Modules\LocalEntity\Http\Controllers\Entity;

use App\Helpers\DynamicPermission\DynamicPermissionCheckerHelper;
use App\Http\Controllers\Controller;
use App\Modules\LocalEntity\Actions\AppEntityDeleteAction;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\ActivityLog\EntityActivityLogRequestsCreator;
use App\Modules\LocalEntity\Dto\ElementData;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\EntityCrudRules\CrudRulesManager;
use App\Modules\LocalEntity\Events\EntityActionsEventManager;
use App\Modules\LocalEntity\Events\RecoredDeletedEvent;
use App\Modules\LocalEntity\Exceptions\AppException;
use App\Modules\LocalEntity\Exceptions\ValidationExceptions\DeleteEntityBelongsToRecordFoundException;
use App\Modules\LocalEntity\Factories\ElementListenerFactory;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\HandlesBeforeAfterCheckers;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\PermissionCheckerTrait;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\RedirectHelperTrait;
use App\Modules\LocalEntity\Permissions\ResourceBasedPermissionsManager;
use App\Modules\LocalEntity\Services\Form\EntityDeletionDispatcher;
use App\Services\CommonActivityLogService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityPermissionKeyUtil;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Modules\LocalEntity\Http\Controllers\Entity\DeleteRedirectionHelpers\DeleteEntitySuccessRedirectionHandlerFactory;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestCustomFormatter;
use Izam\Navigation\Services\PaginationService;

class EntityDeleteController extends Controller
{
    use HandlesBeforeAfterCheckers;
    use PermissionCheckerTrait;
    use RedirectHelperTrait;
    public function __invoke(
        $entityKey,
        $id,
        AppEntityDeleteAction $action,
        AppEntityShowAction $showAction,
        AppEntityStructureGetter $structureGetter,
        CommonActivityLogService $activityLogService,
        EntityActionsEventManager $manager,
        Request $request,
        CrudRulesManager $crudRulesManager,
        DynamicPermissionCheckerHelper $dynamicPermissionCheckerHelper,
        ResourceBasedPermissionsManager $resourceBasedPermissionsManager
    )
    {
        $st = $structureGetter->buildEntity($entityKey);
        if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::DELETE, $st)) {
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
            );
        }
        $this->checkPermission($dynamicPermissionCheckerHelper, $st, EntityPermissionKeyUtil::DELETE);

        try {
            $action->validateDelete($st, $id);
            $data = $showAction->handle($entityKey, $id, 2);
            $appEntityData = clone $data;
            /**
             * resource based permission handling
             */
            if (!$this->checkAfterCheckers($entityKey, EntityPermissionKeyUtil::DELETE, $st, $data->data)) {
                $customRedirectToEntityViewUrl = $this->getCustomRedirectToEntityViewUrl();
                return $this->handleCheckerDenial(
                    $this->resourceBasedPermissionsManager->getMessage(),
                    redirectUrl: $customRedirectToEntityViewUrl
                );
            }
            $rules = $crudRulesManager->get($entityKey,  EntityPermissionKeyUtil::DELETE);
            if($rules){
                try{
                    $crudRulesManager->processRules($rules, $data);
                }
                catch(Exception $exception){
                    if ($request->wantsJson()) {
                        return response()->json([
                            'error' => $exception->getMessage(),
                        ], 400);
                    } else {
                        \Session::flash('danger', $exception->getMessage());
                        return redirect()->back();
                    }
                }
            }
            foreach ($st->getFields() as $field) {
                $handler = ElementListenerFactory::getListener($field->getType());
                if ($handler) {
                    $elementData = new ElementData($st, $field, $id, [], $data);
                    $handler->handle($elementData);
                }
            }
            $action->delete($st, $id);
            try {
                PaginationService::remove($entityKey);
            }catch (Exception $exception) {}

            $requests = (new EntityActivityLogRequestsCreator())->create($st, [], toArray($data));
            $requests = EntityActivityLogRequestCustomFormatter::format($st, $requests, null, $data);
            foreach ($requests as $requestObject) {
                $activityLogService->addActivity($requestObject);
            }
            $manager->triggerEvents($entityKey, 'delete', $data);
            event(new RecoredDeletedEvent(new RecoredInfo($entityKey, toArray($data))));
            EntityDeletionDispatcher::dispatch($entityKey, $data);
            if ($request->wantsJson()) {
                return response()->json(['message' => sprintf(__t('%s Deleted Successfully'), __t(Str::singular($st->getLabel())))], 200);
            }

            if ($st->getParentEntityData()) {
                $extended = $st->getParentEntityData()->getEntity()->getExtendedData();
                if ($extended && $extended->getEntityKey() === 'work_order') {
                    flashMessageIntoCake(sprintf(__t('%s Deleted Successfully'), __t(Str::singular($st->getLabel()))));
                    return redirect(getCakeURL([
                        'controller' => 'work_orders', 'action' => 'workflow_view', $data['reference_id']
                    ]));
                }
            }

            $successRedirectionHandler = DeleteEntitySuccessRedirectionHandlerFactory::create($st, $appEntityData);

            $successRedirectionHandler->flashMessage();

            return $successRedirectionHandler->redirect();
        } catch (DeleteEntityBelongsToRecordFoundException $exception) {
            $msg = EntityDeletionDispatcher::getMessage($entityKey,$exception->getMessage());
            if ($request->wantsJson()) {
                return response()->json(['message' => $msg], 400);
            }

            \Session::flash('danger', $msg);
            return redirect()->back();
        } catch (AppException $exception) {
            if ($request->wantsJson()) {
                return response()->json(['message' => $exception->getMessage()], 400);
            }
            \Session::flash('danger', $exception->getMessage());
            return redirect()->back();
        } catch (\Exception $exception) {
            $entityName = isset($st) ? $st->getLabel() : $entityKey;
            Log::error('Entity Record Deletion Failed', ['exception' => $exception]);
            if ($request->wantsJson()) {
                return response()->json(['message' => sprintf(__t('%s Deleting Failed'), __t($entityName))], 500);
            }
            \Session::flash('danger', sprintf(__t('%s Deleting Failed'), __t($entityName)));
            return redirect()->back();
        }
    }

    private function getCustomRedirectToEntityViewUrl(){
        $urlComponents = parse_url(\Illuminate\Support\Facades\Request::header('referer'));
        // If there is a query string, parse it into an array
        $queryParams = [];
        if (isset($urlComponents['query'])) {
            parse_str($urlComponents['query'], $queryParams);
        }
        $redirectToEntity = $queryParams['redirect_to_entity'] ?? null;
        $redirectToEntityId = $queryParams['redirect_to_entity_id'] ?? null;

        //handle delete case
        if (\request()->get('redirect_to_entity') && \request()->get('redirect_to_entity_id')){
            $redirectToEntity = \request()->get('redirect_to_entity');
            $redirectToEntityId = \request()->get('redirect_to_entity_id');
        }
        if($redirectToEntity && $redirectToEntityId){
            return route('owner.entity.show', ['entityKey' => $redirectToEntity , 'id'=> $redirectToEntityId]);
        }
        return null;
    }

}



