<?php


namespace App\Modules\LocalEntity\Http\Controllers\Entity;

use App\Facades\CakeSession;
use App\Factories\EntityLimitationMapper;
use App\Helpers\DynamicPermission\DynamicPermissionCheckerHelper;
use App\Helpers\DynamicPermission\DynamicPermissionHelper;
use App\Http\Controllers\Controller;
use App\IzamViews\IzamView;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\ActivityLog\EntityActivityLogRequestsCreator;
use App\Modules\LocalEntity\Creators\WorkflowTypeEntityCreator;
use App\Modules\LocalEntity\EntityCrudRules\CrudRulesManager;
use App\Modules\LocalEntity\Dto\ElementData;
use App\Modules\LocalEntity\Events\RecoredCreatedEvent;
use App\Modules\LocalEntity\Events\EntityActionsEventManager;
use App\Modules\LocalEntity\Factories\ElementListenerFactory;
use App\Modules\LocalEntity\Factories\HandleEntityCustomFieldsFactory;
use App\Modules\LocalEntity\Forms\SaveFormCreator;
use App\Modules\LocalEntity\Helpers\BreadCrumbs\InitializeBreadCrumbs;
use App\Modules\LocalEntity\Helpers\DataValidationGetterForInputs;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Helpers\FormValidationMetaAssigner;
use App\Modules\LocalEntity\Helpers\Saver\Formatter\FormatterManger;
use App\Modules\LocalEntity\Helpers\Saver\SaverManager;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\HandlesBeforeAfterCheckers;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Modules\LocalEntity\Services\ChildEntityCanBeAdded;
use App\Modules\LocalEntity\Services\Form\FormExtraDataFactory;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Modules\Resource\Http\Controllers\ControllerTrait;
use App\Services\CommonActivityLogService;
use App\Services\ProductionPlanService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityPermissionKeyUtil;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Exception;
use Illuminate\Http\Request;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\Factories\EntityDataHandlerFactory;
use Illuminate\Validation\ValidationException;
use Izam\Forms\Element\Hidden;
use App\Modules\LocalEntity\Permissions\ResourceBasedPermissionsManager;
use Izam\Navigation\Services\PaginationService;
use App\Facades\Plugins;
use App\Modules\LocalEntity\Repositories\LocalEntityRepository;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestCustomFormatter;

class EntityCreateController extends Controller
{
    use ControllerTrait;
    use HandlesBeforeAfterCheckers;
    public function __construct(
        protected InitializeBreadCrumbs $breadCrumbs,
        private DynamicPermissionCheckerHelper $dynamicPermissionCheckerHelper,
        protected ResourceBasedPermissionsManager $resourceBasedPermissionsManager,
        private LocalEntityRepository $localEntityRepository
    )
    {
        $this->middleware(function ($request, $next) use ($breadCrumbs) {
            $breadCrumbs->setBreadCrumbs();
            return $next($request);
        });
        new \IzamViewData();
    }

    public function store($entityKey,
                          Request $request,
                          AppEntityMetaRepository $repository,
                          EntityRulesGetter $entityRulesGetter,
                          SaverManager $appEntitySaver,
                          AppEntityShowAction $showAction,
                          CommonActivityLogService $activityLogService,
                          AppEntityStructureGetter $structureGetter,
                          EntityActionsEventManager $manager,
                          CrudRulesManager $crudRulesManager,
                          DynamicPermissionHelper $dynamicPermissionHelper
    )
    {
        ini_set('memory_limit', '3G');
        $st = $structureGetter->buildEntity($entityKey);

        if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::CREATE, $st)) {
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
            );
        }

        if (!$this->dynamicPermissionCheckerHelper->checkPermission($st, EntityPermissionKeyUtil::CREATE)) {
            if ($request->wantsJson()) {
                return response()->json([
                    'error' => 'You are not allowed to view this page',
                ], 401);
            } else {
                CakeSession::flashMessage(__t('You are not allowed to view this page',));
                $redirectUrl = \Illuminate\Support\Facades\Request::header('referer') ?? '/';
                return redirect($redirectUrl);
            }
        }
        $limitation_key = EntityLimitationMapper::map($entityKey);
        $limitationTitle = EntityLimitationMapper::mapLimitationTitle($entityKey);
        if (!is_null($limitation_key)) {
            $this->handleEntitySiteLimit(
                checkSiteLimit(getCurrentSite('id'), $limitation_key),
                route(EntityLimitationMapper::indexRedirect($entityKey), $entityKey),
                limitationTitle: $limitationTitle
            );
        }
        $data = resetSubformIndex($request->all(), $entityKey);
        $validator = new DaftraValidator($entityKey, $entityRulesGetter);
        $validator->setData($data);

        if (!$validator->isValid()) {
            if (isApi()) {
                return response()->json(['message' => 'Entity not saved', 'errors' => $validator->getErrors()], 422);
            } else {
                $this->izamFlashMessage(__t('The given data was invalid, Please Check Errors Below'), 'danger');
                return redirect()->back()->withInput($data)
                    ->withErrors($validator->getErrors());
            }
        }


       $rules = $crudRulesManager->get($entityKey,  EntityPermissionKeyUtil::CREATE);
       if($rules){
           try{
               $crudRulesManager->processRules($rules, $data);
           }
           catch (ValidationException $exception){
               return redirect()->back()->withInput($data)
                   ->withErrors($exception->validator->getMessageBag());
           }
           catch(Exception $exception){
               if ($request->wantsJson()) {
                   return response()->json(['message' => $exception->getMessage()], 400);
               } else {
                   \Session::flash('danger', $exception->getMessage());
                   return redirect()->back()->withInput($data);
               }
           }
       }
        $formattedRequest = FormatterManger::format($data, $entityKey, $repository);



        if ($st->getPlugin() && !Plugins::pluginActive($st->getPlugin())) {
            if ($request->wantsJson()) {
                return response()->json(['message' => __t("Plugin is not installed")], 400);
            } else {
                CakeSession::flashMessage(__t("Plugin is not installed"));
                $redirectUrl = \Illuminate\Support\Facades\Request::header('referer') ?? '/';
                return redirect($redirectUrl);
            }
        }

        $entityHasBeforeCheckers = $this->resourceBasedPermissionsManager->hasBeforeCheckers($entityKey);

        if($entityHasBeforeCheckers){
            $entity_rb_before_checkers = $this->resourceBasedPermissionsManager->getBeforeCheckers($entityKey);
            $before_checkers = $entity_rb_before_checkers[EntityPermissionKeyUtil::CREATE] ?? [];
            $is_valid = $this->resourceBasedPermissionsManager->check($before_checkers,$st);
            if(!$is_valid) {
                if ($request->wantsJson()) {
                    return response()->json(['message' => $this->resourceBasedPermissionsManager->getMessage()], 400);
                } else {
                    CakeSession::flashMessage($this->resourceBasedPermissionsManager->getMessage());
                    $redirectUrl = \Illuminate\Support\Facades\Request::header('referer') ?? '/';
                    return redirect($redirectUrl)->withInput($data);
                }
            }
        }
        $formattedRequest = EntityDataHandlerFactory::getEntityStrategy($entityKey, $formattedRequest)->onCreate();
        $id = $appEntitySaver->save($st, $formattedRequest);
        HandleEntityCustomFieldsFactory::handleForm($entityKey , $id ,$request->all());
        if ($id) {
           PaginationService::remove($entityKey);
            foreach ($st->getFields() as $field) {
                $handler = ElementListenerFactory::getListener($field->getType());
                if ($handler) {
                    $elementData = new ElementData($st, $field, $id, $data);
                    $handler->handle($elementData);
                }
            }
        }
        if (request()->has('permissions')) {
            $dynamicPermissionHelper->addPermissionToEntity($request->all(), $entityKey, $id);
        }
        $recordData = $showAction->handle($entityKey, $id, 4);
        $recordData =  toArray($recordData);
        $recordData['customEventExtraData'] = $this->getEventExtraData($entityKey, $recordData, $data);
        $manager->triggerEvents($entityKey, 'create', $recordData);
        event(new RecoredCreatedEvent(new RecoredInfo($entityKey, $recordData)));
        unset($recordData['customEventExtraData']);
        // $st = $structureGetter->buildEntity($entityKey);


        if (!empty($data['is_clone'])) {
            $st->getParentEntityData()->getForeignKeyName();
            $parentRelationName = $st->getParentEntityData()->getChildRelation()->getName();
            $recordData['is_clone'] = [$parentRelationName => $recordData[$parentRelationName]];
        }
        if(!empty($data['is_draft_entity'])){
            $recordData['is_draft_entity'] = 1;
        }
        $requests = (new EntityActivityLogRequestsCreator())->create($st, $recordData);
        $requests = EntityActivityLogRequestCustomFormatter::format($st, $requests, $recordData, null);

        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }
        if ($request->wantsJson()) {
            return response()->json(['message' => sprintf(__t('%s Added Successfully'), __t($st->getLabel())), 'id' => $id]);
        } else {
            $message = sprintf(__t('%s Added Successfully'), __t(Str::singular($st->getLabel())));
            if (!empty($request->get('success_redirect'))) {
                $url = rtrim(getCakeBaseURl(), '/') . $request->get('success_redirect');
            }else{
                if ($entityKey == EntityKeyTypesUtil::WORK_FLOW_TYPE) {
                    $url = route('owner.local_entities.builder', ['entityKey' => WorkflowTypeEntityCreator::getWorkFlowEntityName($id), 'redirect' => "/v2/owner/entity/workflow_type/$id/show"]);
                }elseif($st->getParentEntityData()?->getEntityKey() && \request()->reference_id){
                    $url = getCustomViewRoute(['entity_key' => $st->getParentEntityData()->getEntityKey(), 'id' => \request()->reference_id]);
                } else {
                    $url = getCustomViewRoute(['entity_key' => $entityKey, 'id' => $id, 'record' => $recordData]);
                }
            }
            if (strpos($url, '/v2') !== false) {
                \Session::flash('success', $message);
            } else {
                flashMessageIntoCake($message);
            }
            return redirect($url);
        }
    }

    private function getEventExtraData($entityKey, $recordData, $data){
        if(isset($data['converted']) && $data['converted']) {
            return ['converted' => 1];
        }
        return match($entityKey){
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => [
                'is_convert_bom_to_mo' => $data['is_convert_bom_to_mo'] ?? false,
            ],
            default => null
        };
    }

    public function create(
        $entityKey, AppEntityStructureGetter $entityStructureGetter,
        DataValidationGetterForInputs $dataValidationGetterForInputs,
        SaveFormCreator $saveFormCreator,
        FormValidationMetaAssigner $formValidationMetaAssigner,
        DynamicPermissionHelper $dynamicPermissionHelper,
        $refId = null,
    )
    {
        $structure = $entityStructureGetter->buildEntity($entityKey);

        if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::CREATE, $structure)) {
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
            );
        }

        if (!$this->dynamicPermissionCheckerHelper->checkPermission($structure, EntityPermissionKeyUtil::CREATE)) {
            CakeSession::flashMessage(__t('You are not allowed to view this page',));

            $redirectUrl = \Illuminate\Support\Facades\Request::header('referer') ?? '/';
            return redirect($redirectUrl);

        }
        if ((substr($entityKey, 0, 3) === 'le_') &&  !$this->localEntityRepository->isActiveLocalEntity($entityKey)) { // check if local entity is active
            flashMessageIntoCake(__t('Local Entity Not Active'), 'Errormessage');
            return redirect(CAKE_BASE_URL);
        }
        $limitation_key = EntityLimitationMapper::map($entityKey);
        $limitationTitle = EntityLimitationMapper::mapLimitationTitle($entityKey);

        if (!is_null($limitation_key)) {
            $this->handleEntitySiteLimit(
                checkSiteLimit(getCurrentSite('id'), $limitation_key),
                route(EntityLimitationMapper::indexRedirect($entityKey), $entityKey),
                limitationTitle: $limitationTitle
            );
        }

        if (!ChildEntityCanBeAdded::check($structure, $refId)) {
            $parentEntity = $entityStructureGetter->buildEntity($structure->getParentEntityData()->getEntityKey());
            $url = route(EntityLimitationMapper::indexRedirect($parentEntity->getEntityKey()), $parentEntity->getEntityKey());
            return redirect($url)
                ->with('danger', sprintf(__t("You can't add %s without %s"), $structure->getLabel(), $parentEntity->getLabel()));
        }
        $form = $saveFormCreator->create($structure);
         if ($refId) {
             $refIdField = new Hidden($structure->getParentEntityData()->getForeignKeyName());
             $refIdField->setValue($refId);
             $form->add($refIdField);
         }
        $permissions = $dynamicPermissionHelper->getDataHelper(EntityKeyTypesUtil::WORK_FLOW_TYPE);
        $messages = [];
        if (!empty(session()->get('errors'))) {
           $messages = session()->get('errors')->getBag('default')->getMessages();
        }
        if (!empty($messages)) {
            $data = request()->old();
            $values = $dataValidationGetterForInputs->get($structure, $data);
            $form->setData($data);
            $form->setMessages(array_undot($messages));
            $formValidationMetaAssigner->assign($form, $values);
        }
        $messageArray = [];
        foreach ($form as $filed) {
            if ($filed->hasAttribute('general-message') && !empty($filed->getAttribute('general-message')) && !empty($filed->getMessages())) {
                $messageArray[] = implode(',', array_values($filed->getMessages()));
            }
        }

        $extraData = FormExtraDataFactory::getEntityExtraDataHelper($structure)->getDataForCreate();
        if (!empty($messageArray)) {
            \Session::flash('danger', implode('<br/>', $messageArray));
        }
        \View::share('extraContainerClass', 'container');

        if ($structure->getIsGlobal() || str_starts_with($entityKey, 'le_')){
            return getCustomCreate($entityKey, compact(
                'form', 'entityKey', 'permissions', 'extraData'
            ));
        }

        return new IzamView('entity_list_drawer/create', ['form' => $form, 'entityKey' => $entityKey, 'extraData' => $extraData]);
    }

    public function creat2e(
        $entityKey, AppEntityStructureGetter $entityStructureGetter,
        DataValidationGetterForInputs $dataValidationGetterForInputs,
        SaveFormCreator $saveFormCreator,
        FormValidationMetaAssigner $formValidationMetaAssigner,
        $refId = null,
        DynamicPermissionHelper $dynamicPermissionHelper,

    )
    {
        ini_set('memory_limit', '3G');
        $structure = $entityStructureGetter->buildEntity($entityKey);

        $entityHasBeforeCheckers = $this->resourceBasedPermissionsManager->hasBeforeCheckers($entityKey);

        if($entityHasBeforeCheckers){
            $entity_rb_before_checkers = $this->resourceBasedPermissionsManager->getBeforeCheckers($entityKey);
            $before_checkers = $entity_rb_before_checkers[EntityPermissionKeyUtil::CREATE] ?? [];
            $is_valid = $this->resourceBasedPermissionsManager->check($before_checkers,$structure);
            if(!$is_valid) {
                CakeSession::flashMessage($this->resourceBasedPermissionsManager->getMessage());
                $redirectUrl = \Illuminate\Support\Facades\Request::header('referer') ?? '/';
                return redirect($redirectUrl);
            }
        }


        if (!$this->dynamicPermissionCheckerHelper->checkPermission($structure, EntityPermissionKeyUtil::CREATE)) {
            CakeSession::flashMessage(__t('You are not allowed to view this page',));

            $redirectUrl = \Illuminate\Support\Facades\Request::header('referer') ?? '/';
            return redirect($redirectUrl);

        }

        $limitation_key = EntityLimitationMapper::map($entityKey);
        $limitationTitle = EntityLimitationMapper::mapLimitationTitle($entityKey);

        if (!is_null($limitation_key)) {
            $this->handleEntitySiteLimit(
                checkSiteLimit(getCurrentSite('id'), $limitation_key),
                route(EntityLimitationMapper::indexRedirect($entityKey), $entityKey),
                limitationTitle: $limitationTitle);
        }

        if(!ChildEntityCanBeAdded::check($structure, $refId)) {
            $parentEntity = $entityStructureGetter->buildEntity($structure->getParentEntityData()->getEntityKey());
            $url = route(EntityLimitationMapper::indexRedirect($parentEntity->getEntityKey()), $parentEntity->getEntityKey());
            return redirect($url)
                ->with('danger',sprintf(__t("You can't add %s without %s"), $structure->getLabel(), $parentEntity->getLabel()));
        }
        $form = $saveFormCreator->create($structure);
        if($refId) {
            $refIdField = new Hidden($structure->getParentEntityData()->getForeignKeyName());
            $refIdField->setValue($refId);
            $form->add($refIdField);
        }
        $permissions = $dynamicPermissionHelper->getDataHelper(EntityKeyTypesUtil::WORK_FLOW_TYPE);
        $messages = [];
        if (!empty(session()->get('errors'))) {
            $messages =  $messages = session()->get('errors')->getBag('default')->getMessages() ;
        }
        if (!empty($messages)) {
            $data = request()->old();
            $values = $dataValidationGetterForInputs->get($structure, $data);
            $form->setData($data);
//            $form->setMessages(array_undot($messages));
            $formValidationMetaAssigner->assign($form, $values);
        }
        $messageArray = [];
        foreach ($form as $filed) {
            if ($filed->hasAttribute('general-message') && !empty($filed->getAttribute('general-message')) && !empty($filed->getMessages())) {
                $messageArray[] = implode(',', array_values($filed->getMessages()));
            }
        }
        $extraData = FormExtraDataFactory::getEntityExtraDataHelper($structure)->getDataForCreate();
        if (!empty($messageArray)) {
            \Session::flash('danger', implode('<br/>', $messageArray));
        }
        new \IzamViewData();
        $extraData = FormExtraDataFactory::getEntityExtraDataHelper($structure)->getDataForCreate();

        return getCustomCreate($entityKey, compact(
            'form', 'entityKey', 'permissions', 'extraData'
        ));
    }

    private function setOldData(): array
    {
        if (Session::has('_old_input')) {
            return Session::get('_old_input');
        }
        return [];
    }

    private function getErros()
    {
        if (Session::has('errors')) {
            return Session::get('errors')->getMessages();
        }
        return [];
    }
}
