<?php


namespace App\Modules\LocalEntity\Http\Controllers\Entity;

use App\Facades\CakeSession;
use App\Factories\EntityLimitationMapper;
use App\Helpers\DynamicPermission\DynamicPermissionCheckerHelper;
use App\Helpers\DynamicPermission\DynamicPermissionHelper;
use App\Http\Controllers\Controller;
use App\IzamViews\IzamView;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\ActivityLog\EntityActivityLogRequestsCreator;
use App\Modules\LocalEntity\Dto\ElementData;
use App\Modules\LocalEntity\EntityCrudRules\CrudRulesManager;
use App\Modules\LocalEntity\Events\EntityActionsEventManager;
use App\Modules\LocalEntity\Exceptions\LaminasValidationException;
use App\Modules\LocalEntity\Factories\ElementListenerFactory;
use App\Modules\LocalEntity\Formatter\EntityFormFormatter;
use App\Modules\LocalEntity\Helpers\BreadCrumbs\InitializeBreadCrumbs;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Helpers\Saver\Formatter\FormatterManger;
use App\Modules\LocalEntity\Helpers\Saver\SaverManager;
use App\Modules\LocalEntity\Helpers\ViewMetaDataGenerator;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\HandlesBeforeAfterCheckers;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\PermissionCheckerTrait;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\SharedEntityFormOperationsTrait;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Modules\LocalEntity\Services\Form\Customize\Custom\EntitySaveFormCustomizerFactory;
use App\Modules\LocalEntity\Services\Form\FormExtraDataFactory;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Services\CommonActivityLogService;
use App\Utils\EntityPermissionKeyUtil;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Modules\LocalEntity\Events\RecoredUpdatedEvent;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\Factories\EntityDataHandlerFactory;
use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestCustomFormatter;
use Izam\Daftra\Common\Entity\Actions\ShowAction\EagerRelation\ActivityLogEagerRelationCreator;
use Izam\Daftra\Common\Exception\AppException;
use App\Modules\LocalEntity\Permissions\ResourceBasedPermissionsManager;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use Izam\Entity\Components\EditCeatePageHeader\Factory\ComponentFactory;
use Izam\Entity\Service\EntityToForm;
use Izam\Entity\Service\SpecBuilder;
use Izam\Navigation\Services\PaginationService;
use Laminas\Form\Element\Hidden;
use View;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Facades\Plugins;
use App\Facades\Branch;

class EntityUpdateIzamViewController extends Controller
{
    use HandlesBeforeAfterCheckers;
    use PermissionCheckerTrait;
    use SharedEntityFormOperationsTrait;
    public function __construct(
        InitializeBreadCrumbs $breadCrumbs,
        private DynamicPermissionCheckerHelper $dynamicPermissionCheckerHelper,
        protected ResourceBasedPermissionsManager $resourceBasedPermissionsManager
    ) {
        new \IzamViewData();
        $this->middleware(function ($request, $next) use ($breadCrumbs) {
            $breadCrumbs->setBreadCrumbs();
            return $next($request);
        });
    }

    public function edit(
        $entityKey,
        $id,
        AppEntityStructureGetter $entityStructureGetter,
        ViewMetaDataGenerator $viewMetaDataGenerator,
        EntityFormFormatter $entityFormFormatter,
        AppEntityShowAction $showAction,
        Request $request
    ) {
        $structure = $entityStructureGetter->buildEntity($entityKey);
        $this->checkPermission($this->dynamicPermissionCheckerHelper, $structure, EntityPermissionKeyUtil::UPDATE);
        if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::UPDATE, $structure)) {
            $redirectUrl = route('owner.entity.show', ['entityKey' => $entityKey, 'id' => $id]);
            if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::VIEW, $structure)) {
                $redirectUrl = route('owner.dashboard');
            }
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
                redirectUrl: $redirectUrl
            );
        }
        $additionalFieldService = resolve(AdditionalFieldsAwareService::class);
        $customForm = $additionalFieldService->getFormData($entityKey, $id);

        $meta = $viewMetaDataGenerator->generate($structure);
        $eagerRelations = ActivityLogEagerRelationCreator::createForEntity($structure);
        $data = $showAction->handle($entityKey, $id, $this->getViewActionLevel($structure->getExtendedEntityKeyOrEntityKey()), $eagerRelations);

        try {
            $entityHasAfterCheckers = $this->resourceBasedPermissionsManager->hasAfterCheckers($entityKey);
            if ($entityHasAfterCheckers) {
                $entityRbAfterCheckers = $this->resourceBasedPermissionsManager->getAfterCheckers($entityKey);
                $after_checkers = $entityRbAfterCheckers[EntityPermissionKeyUtil::UPDATE] ?? [];
                $is_valid = $this->resourceBasedPermissionsManager->check($after_checkers, $structure, $data);
                if (!$is_valid) {
                    if ($request->wantsJson()) {
                        return response()->json(['message' => $this->resourceBasedPermissionsManager->getMessage()], 400);
                    } else {
                        $redirectUrl =  route('owner.entity.show', ['entityKey' => $entityKey, 'id' => $id]);
                        return $this->handleCheckerDenial(
                            $this->resourceBasedPermissionsManager->getMessage(),
                            redirectUrl: $redirectUrl
                        );
                    }
                }
            }
        } catch (\Exception $exception) {
            if ($request->wantsJson()) {
                return response()->json(['message' => 'Record not found',], 400);
            } else {
                \Session::flash('danger', 'Record not found');
                return  redirect()->back(fallback: '/');
            }
        }

        $extraData = FormExtraDataFactory::getEntityExtraDataHelper($structure)->getDataForUpdate($data);
        $form = $this->getEditForm($structure, $extraData);

        if (empty(old())){
            $formattedData = $entityFormFormatter->format($data, $meta);
            $formattedData = json_decode(json_encode($formattedData), true);
            $form->setData($formattedData);
        }
        $this->setPageTitleFromBreadCrumbs(view()->shared('generalBreadCrumbs'));

        if (!$this->checkAfterCheckers($entityKey, EntityPermissionKeyUtil::UPDATE, $structure, $data->data)) {
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
            );
        }


        $entityComponent = ComponentFactory::getEntityComponent($entityKey);
        \View::share('extraContainerClass', null);
        return new IzamView($entityComponent::getEditViewName(), [
            'isEdit' => 1,
            'recordId' => $id,
            'form' => $form,
            'customForm' => $customForm,
            'pageHead' => $entityComponent::generateCreatePageHeaderButtons($data),
            'entityKey' => $structure->getEntityKey(),
            'entityLabel' => $structure->getLabel(),
            'isGlobal' => $structure->getIsGlobal(),
            'view_folder' => getViewFolder($structure),
            'extraData'=>$extraData
        ]);
    }

    public function update(
        $entityKey,
        $id,
        Request $request,
        AppEntityMetaRepository $repository,
        EntityRulesGetter $entityRulesGetter,
        SaverManager $appEntitySaver,
        AppEntityShowAction $showAction,
        EntityActionsEventManager $manager,
        CommonActivityLogService $activityLogService,
        AppEntityStructureGetter $structureGetter,
        CrudRulesManager $crudRulesManager,
        DynamicPermissionHelper $dynamicPermissionHelper
    ) {
        $st = $structureGetter->buildEntity($entityKey);
        if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::UPDATE, $st)) {
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
            );
        }

        $this->checkPermission($this->dynamicPermissionCheckerHelper, $st, EntityPermissionKeyUtil::UPDATE);

        try {
            $data = resetSubformIndex($request->all(), $entityKey);
            $viewLevel = $this->getViewActionLevel($st->getExtendedEntityKeyOrEntityKey());
            $eagerRelations = ActivityLogEagerRelationCreator::createForEntity($st);
        } catch (AppException $exception) {
            if ($request->wantsJson()) {
                return response()->json(['message' => $exception->getMessage(),], 404);
            } else {
                \Session::flash('danger', $exception->getMessage());
                return  redirect()->back(fallback: '/');
            }
        }

        try {
            $oldData = $showAction->handle($entityKey, $id, $viewLevel, $eagerRelations);
            \App\Repositories\IdLoader\ShowActionRepo::resetCache();

            if (!$this->checkAfterCheckers($entityKey, EntityPermissionKeyUtil::UPDATE, $st, $oldData->data)) {
                return $this->handleCheckerDenial(
                    $this->resourceBasedPermissionsManager->getMessage(),
                );
            }
        } catch (\Exception $exception) {
            if ($request->wantsJson()) {
                return response()->json(['message' => 'Record not found',], 400);
            } else {
                \Session::flash('danger', 'Record not found');
                return  redirect()->back(fallback: '/');
            }
        }
        if (isset($data['status']) && $oldData->status != 1 && $data['status'] == 1) {
            $limitation_key = EntityLimitationMapper::map($entityKey);
            if (!is_null($limitation_key)) {
                $this->handleEntitySiteLimit(
                    checkSiteLimit(getCurrentSite('id'), $limitation_key),
                    route(EntityLimitationMapper::updateRedirect($entityKey), $entityKey)
                );
            }
        }
        $entityRulesGetter->useRelationName = false;
        $validator = new DaftraValidator($entityKey, $entityRulesGetter);
        $validator->setData($data);
        if (!$validator->isValid()) {
            if ($request->wantsJson()) {
                return response()->json(['message' => 'Entity not saved', 'errors' => $validator->getErrors()], 400);
            } else {
                return  redirect()->back(fallback: '/')->withInput($data)
                    ->withErrors($validator->getErrors());
            }
        }
        $rules = $crudRulesManager->get($entityKey,  EntityPermissionKeyUtil::UPDATE);
        if ($rules) {
            try {
                $crudRulesManager->processRules($rules, $oldData, $data);
            }catch (LaminasValidationException $exception){
                return redirect()->back()->withInput($data)
                    ->withErrors($exception->getErrors());
            }catch (Exception $exception) {
                $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
                \Session::flash('danger', __t($exception->getMessage()));
                return  redirect()->back(fallback: '/')
                    ->withInput($data)->withErrors($errors);
            }
        }
        $formattedRequest = FormatterManger::format($data, $entityKey, $repository);
        $formattedRequest = EntityDataHandlerFactory::getEntityStrategy($entityKey, $formattedRequest)->onUpdate();
        $appEntitySaver->useRelationName = false;
        $id = $appEntitySaver->save($st, $formattedRequest);
        if ($id) {
            PaginationService::remove($entityKey);
            foreach ($st->getFields() as $field) {
                $handler = ElementListenerFactory::getListener($field->getType());
                if ($handler) {
                    $elementData = new ElementData($st, $field, $id, $data, $oldData);
                    $handler->handle($elementData);
                }
            }
        }
        if (\request()->has('permissions')) {
            $dynamicPermissionHelper->updatePermissionForEntity($entityKey, $id, $request->all());
        }
        $newData = $showAction->handle($entityKey, $id, $viewLevel, $eagerRelations);
        $manager->triggerEvents($entityKey, 'update', $newData);
        event(new RecoredUpdatedEvent(new RecoredInfo($entityKey, toArray($newData))));
        $requests = (new EntityActivityLogRequestsCreator())->create($st, $newData, toArray($oldData), []);
        $requests = EntityActivityLogRequestCustomFormatter::format($st, $requests, $newData, $oldData);

        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }
        if ($request->wantsJson()) {
            return response()->json(['message' => sprintf(__t('%s Updated Successfully'), __t($st->getLabel())), 'id' => $id]);
        } else {
            $url = getCustomViewRoute(['entity_key' => $entityKey, 'id' => $id, 'record' => $newData]);
            $message = sprintf(__t('%s Updated Successfully'), __t(Str::singular($st->getLabel())));
            if (strpos($url, '/v2') !== false) {
                \Session::flash('success', $message);
            } else {
                flashMessageIntoCake($message);
            }
            return  redirect($url);
        }
    }

    private function getEditForm($structure, $extraData)
    {
        $entityKey = $structure->getEntityKey();
        if ($structure->isExtendedEntity()) {
            $entityKey = $structure->getExtendedEntityKeyOrEntityKey();
        }

        $formBuilder = new EntityToForm(new SpecBuilder());

        $form = $formBuilder->build($entityKey, mode: EntityToForm::FORM_UPDATE)
        ->setLabel(sprintf(__t('%s Information'), __t(Str::singular($structure->getLabel()))));

        $entitySaveFormCustomizer = EntitySaveFormCustomizerFactory::create($structure->getEntityKey());
        $entitySaveFormCustomizer->changeFormAfterBuild($form, $extraData);

        if (!empty(session()->get('errors'))) {
            $messages = $this->transformFormErrorMessages(session('errors')->getMessages());
            $form->setMessages($messages);
            $oldData = request()->old();
            if (array_key_exists('attachments' , $oldData)){
                $oldData['attachments'] = explode(',', $oldData['attachments']);
            }
            $form->setData($oldData);
        }
        return $form;
    }

    public function getViewActionLevel($entityKey)
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::PURCHASE_REQUEST:
            case EntityKeyTypesUtil::QUOTATION_REQUEST:
            case EntityKeyTypesUtil::PURCHASE_QUOTATION:
            case EntityKeyTypesUtil::PURCHASE_ORDER:
            case EntityKeyTypesUtil::WORK_ORDER:
                return 0;
            default:
                return 4;
        }
    }
}
