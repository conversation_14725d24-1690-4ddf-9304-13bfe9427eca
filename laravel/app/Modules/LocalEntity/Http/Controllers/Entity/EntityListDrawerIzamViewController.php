<?php

namespace App\Modules\LocalEntity\Http\Controllers\Entity;

use App\Facades\CakeSession;
use App\Helpers\DynamicPermission\DynamicPermissionCheckerHelper;
use App\Facades\Plugins;
use App\Http\Controllers\Controller;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Actions\ListAction;
use App\Modules\LocalEntity\Factories\ListBehaviorFactory;
use App\Modules\LocalEntity\Forms\RowAction\RowActionManager;
use App\Modules\LocalEntity\Helpers\BreadCrumbs\InitializeBreadCrumbs;
use App\Modules\LocalEntity\Helpers\DataOptionAssigner;
use App\Modules\LocalEntity\Helpers\DataValidationGetterForInputs;
use App\Modules\LocalEntity\Helpers\ListingDataAssigner;
use App\Modules\LocalEntity\Helpers\ListingMetaDataGenerator;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\HandlesBeforeAfterCheckers;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\PermissionCheckerTrait;
use App\Modules\LocalEntity\Listing\ListingActionsManager;
use App\Modules\LocalEntity\Listing\Parser\FormParser;
use App\Modules\LocalEntity\Permissions\PermissionsManager;
use App\Modules\LocalEntity\Prototype\Table;
use App\Modules\LocalEntity\Services\Form\FormExtraDataFactory;
use App\Modules\LocalEntity\Services\ListingRedirectService;
use Izam\Entity\Components\ListingPageHeader\Factory\ComponentFactory;
use Izam\Navigation\Services\PaginationService;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityPermissionKeyUtil;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use Izam\Daftra\Common\EntityStructure\EntityAndRelationCache;
use Izam\Daftra\Common\Exception\AppException;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Service\CreateFilterFormSpecs;
use Izam\Entity\Service\ShowLegacyListing;
use IzamViewData;
use Laminas\Form\Fieldset;
use App\Modules\LocalEntity\Permissions\ResourceBasedPermissionsManager;
use Symfony\Component\HttpFoundation\Response;

class EntityListDrawerIzamViewController extends Controller
{
    use HandlesBeforeAfterCheckers;
    use PermissionCheckerTrait;

    public function __construct(
        protected InitializeBreadCrumbs $breadCrumbs,
        private RowActionManager $rowActionManager,
        private DynamicPermissionCheckerHelper $dynamicPermissionCheckerHelper,
        private PermissionsManager $permissionsManager,
        private FormParser $parser,
        private ListAction $action,
        private AppEntityStructureGetter $structureGetter,
        private ListingMetaDataGenerator $listingMetaDataGenerator,
        private ListingDataAssigner $listingDataAssigner,
        private DataOptionAssigner $dataOptionAssigner,
        private DataValidationGetterForInputs $dataValidationGetterForInputs,
        private ListingActionsManager $listingActionsManager,
        protected ResourceBasedPermissionsManager $resourceBasedPermissionsManager,
    ) {
        /** set Default View Data */
        new IzamViewData();
    }

    public function index(
        $entityKey
    ) {
        if (!isEntityListable($entityKey)) {
            CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
            return redirect(CAKE_BASE_URL);
        }
        $structure = $this->structureGetter->buildEntity($entityKey);
        $response = $this->checkRedirect($structure);

        if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::LIST, $structure)) {
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
                redirectUrl: route('owner.dashboard')
            );
        }

        /** @todo will be event based outside this class */
        if ($response instanceof Response) {
            return $response;
        }

        if (!empty($_SESSION['site_limits'])) {
            $result = $_SESSION['site_limits'];
            unset($_SESSION['site_limits']);

            $this->handleEntitySiteLimit($result, route('owner.entity.list', $entityKey));
        }

        $this->breadCrumbs->setBreadCrumbs();
        $listBehavior = ListBehaviorFactory::get($entityKey);
        try {

            $level = $this->getIndexLevel($entityKey);
            $this->handleOldFilter($entityKey);
            $filters = request()->get('filter', []);
            $customFilters = request()->get('custom_filter', []);
            if ($customFilters){
                $this->setCustomFilters($customFilters, $filters);
            }
            $listBehavior = ListBehaviorFactory::get($entityKey);
            $listBehavior->beforeList();
            $data = $this->action->handle($structure, $level, $this->parser, [], request()->all());
            $data = $listBehavior->afterList($data);
            PaginationService::init($structure, $data);
        } catch (AppException $exception) {
            if (!request()->query('reload')) {
                Log::error('Exception on listing', [
                    'Message' => $exception->getMessage(),
                    'Exception' => $exception,
                    'entities' => EntityAndRelationCache::getAllEntities(),
                    'fields' => EntityAndRelationCache::getAllFields($entityKey),
                    'relations' => EntityAndRelationCache::getAllRelations($entityKey)
                ]);
                EntityAndRelationCache::clearCache();
                //Retrieve current query strings:
                $currentQueries = request()->query();
                $newQueries = ['reload' => '1'];
                $allQueries = array_merge($currentQueries, $newQueries);
                return redirect(request()->fullUrlWithQuery($allQueries));
            }
        }
        $allFieldsMeta = $this->listingMetaDataGenerator->generate($structure);
        $allFieldsMeta = $listBehavior->afterMetaGeneration($allFieldsMeta);
        //Assign in case we need to process any data for specific entity
        $data = $this->listingDataAssigner->execute($data, $structure->getExtendedEntityKeyOrEntityKey());
        $data->appends(['filter' => $filters]);

        $tableFieldsMeta = array_filter($allFieldsMeta, function ($item) {
            return ($item->getIsListing() || $item->getSortable() || $item->getType() == EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY);
        });
        $table = $this->createTable($data, $tableFieldsMeta);
        $extraData = FormExtraDataFactory::getEntityExtraDataHelper($structure)->getDataForListing($data);
        $form = CreateFilterFormSpecs::create($structure, $extraData);
        /**  @todo review this section */
            $filters = CreateFilterFormSpecs::getFilterFormValue($form, $filters);
        foreach ($customFilters as $key => $customFilter) {
            $filters['custom_filter[' . $key . ']'] = $customFilter;
            unset($customFilters[$key]);
        }
        $form->setData($filters);

        $this->handleFilterFormHasValue($form);

        foreach ($data as $record) {
            $ids[] = $record->id;
        }

        $allDataPrimaryKeyValues = $this->getAllDataPrimaryKeys($structure, $level, $data);

        setCustomListingGlobalVar($entityKey, $ids ?? []);
        $allowedActions = $this->getAllowedActions($structure);
        $this->dataOptionAssigner->assign($form, $this->dataValidationGetterForInputs->get($structure, $filters));
        $this->listingActionsManager->setUrlParams(request()->all());
        $this->listingActionsManager->setRequestEntityKey($entityKey);
        $entityListingActions = $this->listingActionsManager->getEntityActions($structure->getExtendedEntityKeyOrEntityKey());
        $label = __t(Str::plural($structure->getLabel()));

        $reset_filters_url = $listBehavior->getResetFiltersUrl();

        // Here, we expect that the entities have extend will use same view folder.
        $isGlobal = $structure->getIsGlobal();
        if ($structure->isExtendedEntity()) {
            $isGlobal = true;
            if (str_starts_with($entityKey, 'le_workflow-type') !== false) {  // TODO: Convert workflow to match extended entity.
                $reset_filters_url = url('owner/entity/workflow/' . $entityKey . '/list?reset=1');
            }
        }
            $listingData = [
            'structure'=> $structure,
            'entityLabel' => $structure->getLabel(),
            'entityKey' => $entityKey,
            'form' => $form,
            'pagination' => $data,
            'table' => $table,
            'listingRowActions' => $this->getRowActions($structure, $allowedActions),
            'entityListingActions' => $entityListingActions,
            'allowedActions'=> $allowedActions,
            'filtered' => $filters,
            'allDataPrimaryKeyValues' => $allDataPrimaryKeyValues,
            // new values for default index
            'filtersForm' => $form,
            'isGlobal' => $isGlobal,
            'view_folder' => getViewFolder($structure),
            'reset_filters_url' => $reset_filters_url,
            'current_url_for_sorting' => $filters,
            'pageHead' => ComponentFactory::getEntityComponent($entityKey)::PageHeaderButtons($data),
            'modals' => ComponentFactory::getEntityComponent($entityKey)::generatePageModals(),
            'iframeActions' => ComponentFactory::getEntityComponent($entityKey)::generateIframeMultiActionButtons(),
            'title_for_layout' => $label,
            '_PageBreadCrumbs' =>   view()->shared('generalBreadCrumbs'),
            'links' => array_filter(ComponentFactory::getEntityComponent($entityKey)::pageButtonsWithoutData()),
            'listingExtraData' => $extraData,
        ];
        \View::share('extraContainerClass', 'container-full');
        return ListingRedirectService::redirectTo($listingData);
    }

    /** @todo move to event driven */
    private function checkRedirect($structure)
    {
        if (!$structure) {
            flashMessageIntoCake(__t('Entity Not Found'), 'Errormessage');
            return redirect(CAKE_BASE_URL);
        }

        if ($structure->getPlugin() && !Plugins::pluginActive($structure->getPlugin())) {
            flashMessageIntoCake(__t('Entity Plugin Not Active'), 'Errormessage');
            return redirect(CAKE_BASE_URL);
        }
        $this->checkPermission($this->dynamicPermissionCheckerHelper, $structure, EntityPermissionKeyUtil::VIEW);
        $entityKey = $structure->getEntityKey();

        if (ShowLegacyListing::check($entityKey)) {
            return redirect(ShowLegacyListing::getLegacyIndexURL($entityKey));
        }

        if (!$structure->getTable()) {
            \Session::flash('danger', __t('Please drag and drop at least one field in the builder'));
            return redirect(route('owner.local_entities.builder', ['entityKey' => $entityKey]));
        }

        return '';
    }

    private function createTable($data, $meta): Table
    {
        $table = new Table();
        if (!empty(request()->get('hide_actions')) ||  request()->get('hide_actions') == 1) {
            $table->setHaveSort(false);
        }

        usort($meta, function ($a, $b) {
            return $a->getOrderIndex() <=> $b->getOrderIndex();
        });
        $table->setHeader($meta);

        $table->setDataWithMeta($data, $meta);
        $activeField = $this->getActiveField($meta);

        if ($activeField) {
            $table->setActiveSort($activeField);
        }

        return $table;
    }

    public function getActiveField($meta)
    {
        $activeField = null;
        if (request()->has('sort') && @!empty(request()->get('sort'))) {
            $sort = request()->get('sort');
            $key = array_keys($sort)[0];
            $direction = array_values($sort)[0];
            $activeFields = array_filter($meta, function ($item) use ($key) {
                return $item->getSortKey() == $key;
            });
            $activeField = array_pop($activeFields);
            if (empty($activeField)) {
                $activeField = $this->getDefaultActive($meta);
            }
            $activeField->setSortDir($direction);
        } else {
            $activeField = $this->getDefaultActive($meta);
        }

        return $activeField;
    }

    private function getDefaultActive($meta)
    {
        foreach ($meta as $field) {
            if ($field->getType() == EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY) {
                return $field;
            }
        }

        return null;
    }

    private function getAllowedActions($structure): array
    {
        $entityKey = $structure->getEntityKey();
        $actions = [EntityPermissionKeyUtil::CREATE, EntityPermissionKeyUtil::DELETE, EntityPermissionKeyUtil::UPDATE];

        foreach ($actions as $action) {
            $permissions = $this->permissionsManager->getEntityActionPermissions($entityKey, $action);
            $isDynamicPermission = false;
            if (empty($permissions)) {
                $isDynamicPermission = true;
            }
            $allowedActions[$action] = $this->permissionsManager->checkPermission($structure, $action, $permissions, $isDynamicPermission);

            if ($allowedActions[$action]) {
                $this->rowActionManager->addAllowedAction($action);
            }
        }

        return $allowedActions ?? [];
    }

    private function getRowActions($structure, $allowedActions): array
    {
        $entityKey = $structure->getEntityKey();

        $listingRowActions = $this->rowActionManager->get($entityKey);

        foreach ($listingRowActions as $key => $listingRowAction) {
            if (isset($allowedActions[$key]) && $allowedActions[$key] !== true) {
                unset($listingRowActions[$key]);
            }
        }

        $entityLabel = $structure->getLabel();
        $pageTitle = ($entityLabel);

        return array_map(function ($action) use ($pageTitle, $entityLabel) {
            if (isset($action['prompt']['message'])) {
                $action['prompt']['message'] = sprintf(__t($action['prompt']['message']), __t(Str::singular($pageTitle)));
                if(!isset($action['icon']) || !$action['icon'] ){
                    $action['icon'] = '<i class="mdi mdi-delete text-danger"></i>';
                }
            }
            $action['class'] = isset($action['class']) ? ($action['class'] . " ui-dropdown-menu-item") : "ui-dropdown-menu-item";
            return $action;
        }, $listingRowActions);
    }


    private function getIndexLevel($entityKey)
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::PURCHASE_ORDER:
            case EntityKeyTypesUtil::PURCHASE_REQUEST:
            case EntityKeyTypesUtil::QUOTATION_REQUEST:
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY:
            case EntityKeyTypesUtil::PURCHASE_QUOTATION:
            case (preg_match('/^le_workflow-type-entity/', $entityKey) ? true : false):
                return 1;

            default:
                return 2;
        }
    }

    private function handleFilterFormHasValue(&$form)
    {
        foreach ($form as $element) {
            if ($element instanceof Fieldset) {
                $element->setOptions(array_merge(['is_filter' => true], $element->getOptions()));
                foreach ($element->getElements() as $subElement) {
                    if ($subElement->getValue()) {
                        $subElement->setOption('advanced', false);
                        $element->setOption('advanced', false);
                    }
                }
            } else {
                if ($element->getValue()) {
                    $element->setOption('advanced', false);
                }
            }
        }
    }

    private function setCustomFilters($customFilters = [], $filters = []): void
    {
        $temp = [];
        foreach ($customFilters as $customFilter) {
            parse_str($customFilter, $output);
            $temp = array_merge($temp, $output);
        }
        $temp = array_merge($filters, $temp);
        request()->merge(['filter' => $temp]);
    }
    private function handleOldFilter($entityKey)
    {
        $sessionKey = 'entity_filter.';
        if (empty(request()->query())) {
            if (session()->has("$sessionKey.$entityKey")) {
                $sessionData = session("$sessionKey.$entityKey");
                unset($sessionData['sort']);
                return request()->merge($sessionData);
            }
            return null;
        }
        if (array_key_exists('reset', request()->query())) {
            session()->forget("$sessionKey.$entityKey");
        } else {
            //don't save filters if it's coming from iframe
            if (!array_key_exists('iframe' , request()->all() )){
                if (request()->has('filter')) {
                    session()->put("$sessionKey.$entityKey", [
                        'filter' => request()->get('filter')
                    ]);
                }
            }
        }
    }

    private function getAllDataPrimaryKeys($structure, $level, $data): array
    {
        $allIds = $data->pluck('id')->toArray();
        return $allIds;
        if ($data->total() == $data->count()) {
            return $allIds;
        }
        $currentPage = request('page', 1);
        for ($page = 1; $page <= $data->lastpage(); $page++) {
            if ($currentPage == $page) {
                continue;
            }
            request()->merge(['page' => $page]);
            $newData = $this->action->handle($structure, $level, $this->parser);
            $allIds = array_merge($allIds, $newData->pluck('id')->toArray());
        }
        request()->merge(['page' => $currentPage]);
        return $allIds;
    }

}
