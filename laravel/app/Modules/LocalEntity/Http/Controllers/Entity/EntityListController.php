<?php

namespace App\Modules\LocalEntity\Http\Controllers\Entity;


use App\Helpers\DynamicPermission\DynamicPermissionCheckerHelper;
use App\Facades\Branch;
use App\Facades\Plugins;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Actions\ListAction;
use App\Modules\LocalEntity\Actions\ListActionV2;
use App\Modules\LocalEntity\AppEntities\AppEntityData;
use App\Modules\LocalEntity\Helpers\UnitTemplateOptionGetter;
use App\Modules\LocalEntity\Listing\FilterHelper;
use App\Modules\LocalEntity\Listing\Parser\FormParser;
use App\Http\Controllers\Controller;
use App\Modules\LocalEntity\Factories\EntityListPhotoDataFormatterFactory;
use App\Modules\LocalEntity\Factories\ListBehaviorFactory;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Repositories\ClientRepository;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\ProductImageRepository;
use App\Repositories\StaffRepository;
use App\Repositories\UnitTemplateRepository;
use App\Services\StoreService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Illuminate\Http\Request;
use Illuminate\Pagination\AbstractPaginator;
use Izam\Daftra\Common\EntityStructure\IEntityStructureGetter;
use Illuminate\Support\Facades\Log;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Entity\Formatter\Listing\API\FormatterManager;

class EntityListController extends Controller
{

    public function __invoke($entityKey, Request $request, ListAction $listAction, FormParser $parser, DynamicPermissionCheckerHelper $dynamicPermissionCheckerHelper,IEntityStructureGetter $structureGetter, $level = 2)
    {
        // **TEMP** Implement a change in api to be backward compatibility to add and display types of leave only in the leave application
        if(
            $entityKey == EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY
            &&
            (
                (($request->header('X-App-Os') == 'android') && (version_compare($request->header('X-App-Version'), '1.1.8', '<=')))
                ||
                (($request->header('X-App-Os') == 'ios') && (version_compare($request->header('X-App-Version'), '1.16.0', '<=')) )
            )
        ){
            if($request->has('filter')){
                $request->merge(['filter'=> array_merge($request->all('filter')['filter'], ['type'=>'leave'])]);
            }else{
                $request->merge(['filter'=> ['type'=>'leave']]);
            }
        }

        if(!$request->has('mode')  ){
            $request->merge(['mode'=> 'api']);
        }
        try {
            $level = $this->getListingActionLevel($entityKey, $level);
            $entity = $structureGetter->buildEntity($entityKey);
            if (empty($entity)) {
                throw new \Exception("Entity Not exists");
            }

            if ($entity->getPlugin() && !Plugins::pluginActive($entity->getPlugin())) {
                throw new \Exception("Plugin not active"); // TO DO get plugin name
            }
            $listBehavior = ListBehaviorFactory::get($entityKey);
            $listBehavior->beforeList();
            /** @var $paginator AbstractPaginator */
            $paginator = $listAction->handle($entity, $level, $parser, [], $request->all());
            $paginator = $listBehavior->afterList($paginator);

            if ($request->has('version') && $request->get('version') == 2) {
                $paginator->getCollection()->transform(
                    new FormatterManager($entity, ['image_size' => $request->get('image_size')])
                );
            }

            return $paginator;
        } catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 400);
        }
    }

    public function listPosDevices(Request $request, IEntityStructureGetter $structureGetter, ListActionV2 $listActionV2, FormParser $parser, $level = 3)
    {
        try {
            $entityKey = 'category';
            // validator will implement validation interface and get massage (entityExists)
            $entity = $structureGetter->buildEntity($entityKey);
            if (empty($entity)) {
                throw new \Exception("Entity Not exists");
            }

            $structure = $listActionV2->parseUrl($parser);
            if($entity->isExtendedEntity()) {
                $filters = $entity->getExtendedData()->getFilters();
                foreach (FilterHelper::generateFilters($filters)  as $filter) {
                    $structure->setFilter($filter);
                }
            }
            $validators = $listActionV2->getUrlValidationRules($structure, $entity, $level);
            $data = $listActionV2->assignRequestData($structure, $entity, $level, $validators);
            $data->setFilter(new FilterMeta('categories.category_type', 'equal', '3'));
            if(Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $branches = Branch::getAllBranches();
                $branchMeta = new FilterMeta('categories.branch_id', 'in', array_keys($branches));
                $branchMeta->setAlias('branch_id');
                $data->setFilter($branchMeta);
            }
            if (getAuthOwner('staff_id') != 0 ){
                $accessibleStore = (app(StoreService::class))->getStoresUsersCanCreateInvoice('', false);
                $storeMeta = new FilterMeta('categories.classification_id1', 'in', array_column($accessibleStore, 'id'));
                $storeMeta->setAlias('store_id');
                $data->setFilter($storeMeta);
            }
            $mode = $listActionV2->getMode($data);
            $responseData = $listActionV2->modeExecute($mode, $entity, $level);
            return $listActionV2->respond($entity, $structure ,$responseData);
        } catch (\Exception $exception) {
			return response()->json([
				'error' => $exception->getMessage(),
			], 400);
        }
    }


    public function listPhoto($entityKey, Request $request,
                              FormParser $parser,
                              ListAction $listAction,
                              ProductImageRepository $repository,
                              UnitTemplateRepository $templateRepository,
                              IEntityStructureGetter $entityStructureGetter,
                              $level = 3

    )
    {
        $entity = $entityStructureGetter->buildEntity($entityKey);
        if (empty($entity)) {
            throw new \Exception("Entity Not exists");
        }

        if ($entity->getPlugin() && !Plugins::pluginActive($entity->getPlugin())) {
            throw new \Exception("Plugin not active"); // TO DO get plugin name
        }

        $listingFields = $request->all()['fields'] ?? [$entity->getListingField()->getName()];
        if (count($listingFields) == 1) {
             array_unshift($listingFields, $entity->getPrimaryKey());
        }


        if (!empty($entity->getThumbnailField())) {
            $photoField =  $entity->getThumbnailField()->getName();
            $listingFields[] = $photoField;
        }

        $request->request->add(['fields'=> $listingFields]);
        $data = [];
        $itemsListing = $listAction->handle($entity, $this->getViewActionLevel($entityKey), $parser, [], $request->all());
        $entityListPhotoDataFormatter = EntityListPhotoDataFormatterFactory::getEntityListPhotoDataFormatter($entityKey);
        $excludeFromLabel = $request->all()['exclude_from_label'] ?? $entityListPhotoDataFormatter->getDefaultExcludeFromLabelFields();

        foreach ($itemsListing as $key=> $value) {
            if (is_array($value) || is_object($value)) {
                $value = new AppEntityData($value);
                $value->setEntity($entity);
                $text = "#";
                foreach ($listingFields as $index => $field)
                {

                    //i want to ignore id writing in text(label)
                    if ($index == 0 || in_array($field, $excludeFromLabel)) {
                        continue;
                    }
                    $tmpData = null;
                    if ( $field == ($photoField ?? '') && !empty($value[$field])) {
                        $tmpData = ['img' => getPhotoPath($entityKey, $value[$field])];
                    } else {
                        if (strpos($value[$field], '@') != false) {
                            $text .=  '('.$value[$field]. ')';

                        } else {
                            if($entityKey == EntityKeyTypesUtil::PRODUCT_ENTITY_KEY && $field == 'product_code' && empty($value[$field])){
                                $field = 'id';
                            }
                            $text .=  ($value[$field]??''). ' ';
                        }
                    }

                }
                $itemData = ['id' => $value[$listingFields[0]], 'text' => $text,];
                if (!empty($tmpData)) {
                    $itemData = $itemData + $tmpData;
                }
                $data[] = $itemData;
            } else {
                $data[] = ['id' => $key, 'text' =>  $value];
            }
        }

        return $entityListPhotoDataFormatter->formatlistPhotoData($data, $itemsListing, $listingFields);
    }

    protected  function getRepository($entityKey)
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                return  resolve(StaffRepository::class);
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                return  resolve(ClientRepository::class);
            default:
                return  resolve(ClientRepository::class);
        }
    }



    public function listForZapier($entityKey, AppEntityShowAction $showAction, AppEntityStructureGetter $getter,Request $request, FormParser $parser, ListAction $listAction) {
        try {
            $entity = $getter->buildEntity($entityKey);
            if (empty($entity)) {
                throw new \Exception("Entity Not exists");
            }

            if ($entity->getPlugin() && !Plugins::pluginActive($entity->getPlugin())) {
                throw new \Exception("Plugin not active"); // TO DO get plugin name
            }

            $items = $listAction->handle($entity, 1, $parser);
            $res = ['data' => []];
            foreach ($items as $item) {
                $res['data'][] = $showAction->handle($entityKey, $item->{$entity->getPrimaryKey()}, 2);
            }
            return response()->json($res, 200);
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json(['message' => 'something went wrong'], 400);
        }
    }

    public function getFieldKey($filedKey, Request $request, FormParser $parser, ListAction $listAction)
    {
        $obj = new AppEntityMetaRepository();
        $filed = $obj->getField($filedKey);
        return $obj->getValuesForField($filed, $request->q);
    }

    public function getMultiFieldKeys($entityKey, $pluckBy, Request $request, FormParser $parser, ListAction $listAction)
    {
        $obj = new AppEntityMetaRepository();
        return $obj->getValuesForFieldQuery($request->fields, $entityKey, $pluckBy);
    }

    public function getViewActionLevel($entityKey): int
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::PRODUCT_ENTITY_KEY => 0,
            EntityKeyTypesUtil::CLIENT_ENTITY_KEY => -1,
            default => 2,
        };
    }

    public function getListingActionLevel(string $entityKey, int $level): int
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::STOCKTAKING => 2,
            default => $level,
        };
    }

}
