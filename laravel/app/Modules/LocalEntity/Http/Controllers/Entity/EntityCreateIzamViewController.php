<?php


namespace App\Modules\LocalEntity\Http\Controllers\Entity;

use App\Exceptions\EntityNotFoundException;
use App\Facades\CakeSession;
use App\Facades\Plugins;
use App\Factories\EntityLimitationMapper;
use App\Helpers\DynamicPermission\DynamicPermissionCheckerHelper;
use App\Helpers\DynamicPermission\DynamicPermissionHelper;
use App\Http\Controllers\Controller;
use App\IzamViews\IzamView;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\ActivityLog\EntityActivityLogRequestsCreator;
use App\Modules\LocalEntity\Creators\WorkflowTypeEntityCreator;
use App\Modules\LocalEntity\EntityCrudRules\CrudRulesManager;
use App\Modules\LocalEntity\Dto\ElementData;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\Events\EntityActionsEventManager;
use App\Modules\LocalEntity\Events\RecoredCreatedEvent;
use App\Modules\LocalEntity\Exceptions\LaminasValidationException;
use App\Modules\LocalEntity\Factories\ElementListenerFactory;
use App\Modules\LocalEntity\Factories\EntityDataHandlerFactory;
use App\Modules\LocalEntity\Helpers\BreadCrumbs\InitializeBreadCrumbs;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Helpers\HandelFormData;
use App\Modules\LocalEntity\Helpers\Saver\Formatter\FormatterManger;
use App\Modules\LocalEntity\Helpers\Saver\SaverManager;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\HandlesBeforeAfterCheckers;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\PermissionCheckerTrait;
use App\Modules\LocalEntity\Http\Controllers\Entity\Traits\SharedEntityFormOperationsTrait;
use App\Modules\LocalEntity\Permissions\ResourceBasedPermissionsManager;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Modules\LocalEntity\Services\ChildEntityCanBeAdded;
use App\Modules\LocalEntity\Services\Form\Customize\Custom\EntitySaveFormCustomizerFactory;
use App\Modules\LocalEntity\Services\Form\FormExtraDataFactory;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Modules\Resource\Http\Controllers\ControllerTrait;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use App\Services\CommonActivityLogService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityPermissionKeyUtil;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Exception;
use Illuminate\Http\Request;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestCustomFormatter;
use Izam\Entity\Components\EditCeatePageHeader\Factory\ComponentFactory;
use Izam\Dynamic\List\Utils\DynamicListUtil;
use Izam\Entity\Service\EntityToForm;
use Izam\Entity\Service\SpecBuilder;
use Izam\Navigation\Services\PaginationService;
use Laminas\Form\Element\Hidden;
use View;

class EntityCreateIzamViewController extends Controller
{
    use ControllerTrait;
    use HandlesBeforeAfterCheckers;
    use PermissionCheckerTrait;
    use SharedEntityFormOperationsTrait;

    public function __construct(
        InitializeBreadCrumbs $breadCrumbs,
        private DynamicPermissionCheckerHelper $dynamicPermissionCheckerHelper,
        protected ResourceBasedPermissionsManager $resourceBasedPermissionsManager
    ) {
        new \IzamViewData();
        $this->middleware(function ($request, $next) use ($breadCrumbs) {
            $breadCrumbs->setBreadCrumbs();
            return $next($request);
        });
    }

    public function create(
        $entityKey,
        AppEntityStructureGetter $entityStructureGetter,
        $refId = null
    ) {
        try {
            $structure = $entityStructureGetter->buildEntity($entityKey);

            if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::CREATE, $structure)) {

                $redirectUrl = route('owner.entity.list', ['entityKey' => $entityKey]);
                if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::LIST, $structure)) {
                    $redirectUrl = route('owner.dashboard');
                }
                return $this->handleCheckerDenial(
                    $this->resourceBasedPermissionsManager->getMessage(),
                    redirectUrl: $redirectUrl
                );
            }

            $this->checkPermission($this->dynamicPermissionCheckerHelper, $structure, EntityPermissionKeyUtil::CREATE);
            $limitation_key = EntityLimitationMapper::map($entityKey);

            if (!is_null($limitation_key)) {
                $this->handleEntitySiteLimit(
                    checkSiteLimit(getCurrentSite('id'), $limitation_key),
                    route(EntityLimitationMapper::indexRedirect($entityKey), $entityKey)
                );
            }

            if (!ChildEntityCanBeAdded::check($structure, $refId)) {
                $parentEntity = $entityStructureGetter->buildEntity($structure->getParentEntityData()->getEntityKey());
                $url = route(EntityLimitationMapper::indexRedirect($parentEntity->getEntityKey()), $parentEntity->getEntityKey());
                return redirect($url)
                    ->with('danger', sprintf(__t("You can't add %s without %s"), $structure->getLabel(), $parentEntity->getLabel()));
            }
            $extraData = FormExtraDataFactory::getEntityExtraDataHelper($structure)->getDataForCreate();

            $form = $this->getCreateForm($structure, $extraData);

            if ($structure->getExtendedData()) {
                HandelFormData::setDefaultValue($structure, $form);
            }

            if ($refId) {
                $referenceIdKey = str_starts_with($entityKey, DynamicListUtil::ADDITIONAL_FIELDS_PREFIX) ? 'reference_id' : $structure->getParentEntityData()->getForeignKeyName();
                if ($form->has($referenceIdKey)) {
                    /* reference_id */
                    $form->get($referenceIdKey)->setValue($refId);
                }
            }

            $additionalFieldService = resolve(AdditionalFieldsAwareService::class);
            $customForm = $additionalFieldService->getCreateDate($entityKey);
            $this->setPageTitleFromBreadCrumbs(view()->shared('generalBreadCrumbs'));
            $entityComponent = ComponentFactory::getEntityComponent($entityKey);
            \View::share('extraContainerClass', null);
            return new IzamView($entityComponent::getCreateViewName(), [
                'isEdit' => 0,
                'form' => $form,
                'customForm' => $customForm,
                'pageHead' => $entityComponent::generateCreatePageHeaderButtons(),
                'entityKey' => $structure->getEntityKey(),
                'isGlobal' => $structure->getIsGlobal(),
                'view_folder' => getViewFolder($structure),
                'entityLabel' => $structure->getLabel(),
                'extraData' => $extraData,
            ]);
        } catch (EntityNotFoundException $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    private function getCreateForm($structure, $extraData)
    {
        $entityKey = $structure->getEntityKey();
        if ($structure->isExtendedEntity()) {
            $entityKey = $structure->getExtendedEntityKeyOrEntityKey();
        }

        $formBuilder = new EntityToForm(new SpecBuilder());

        $form = $formBuilder->build($entityKey, mode: EntityToForm::FORM_CREATE)
            ->setLabel(sprintf(__t('%s Information'), __t($structure->getLabel())));
        $entitySaveFormCustomizer = EntitySaveFormCustomizerFactory::create($structure->getEntityKey());
        $entitySaveFormCustomizer->changeFormAfterBuild($form, $extraData);
        if (!empty(session()->get('errors'))) {
            $messages = $this->transformFormErrorMessages(session('errors')->getMessages());
            $form->setMessages($messages);
            $oldData = request()->old();

            $oldData['attachments'] = !empty($oldData['attachments']) && is_string($oldData['attachments']) ? explode(',', $oldData['attachments']) : [];
            $form->setData($oldData);
            $form->setMessages(session('errors')->getMessages());
        }
        return $form;
    }

    private function setOldData(): array
    {
        if (Session::has('_old_input')) {
            return Session::get('_old_input');
        }
        return [];
    }

    private function getErros()
    {
        if (Session::has('errors')) {
            return Session::get('errors')->getMessages();
        }
        return [];
    }

    public function store($entityKey,
                          Request $request,
                          AppEntityMetaRepository $repository,
                          EntityRulesGetter $entityRulesGetter,
                          SaverManager $appEntitySaver,
                          AppEntityShowAction $showAction,
                          CommonActivityLogService $activityLogService,
                          AppEntityStructureGetter $structureGetter,
                          EntityActionsEventManager $manager,
                          CrudRulesManager $crudRulesManager,
                          DynamicPermissionHelper $dynamicPermissionHelper
    )
    {
        $st = $structureGetter->buildEntity($entityKey);
        if (!$this->checkBeforeCheckers($entityKey, EntityPermissionKeyUtil::CREATE, $st)) {
            return $this->handleCheckerDenial(
                $this->resourceBasedPermissionsManager->getMessage(),
            );
        }

        $this->checkPermission($this->dynamicPermissionCheckerHelper, $st, EntityPermissionKeyUtil::CREATE);
        $limitation_key = EntityLimitationMapper::map($entityKey);
        if (!is_null($limitation_key)) {
            $this->handleEntitySiteLimit(
                checkSiteLimit(getCurrentSite('id'), $limitation_key),
                route(EntityLimitationMapper::indexRedirect($entityKey), $entityKey)
            );
        }
        $data = resetSubformIndex($request->all(), $entityKey);
        $entityRulesGetter->useRelationName = false;
        $validator = new DaftraValidator($entityKey, $entityRulesGetter);
        $validator->setData($data);
        if (!$validator->isValid()) {
            if (isApi()) {
                return response()->json(['message' => 'Entity not saved', 'errors' => $validator->getErrors()], 422);
            } else {
                $this->izamFlashMessage(__t('The given data was invalid, Please Check Errors Below'), 'danger');
                return redirect()->back()->withInput($data)
                    ->withErrors($validator->getErrors());
            }
        }

       $rules = $crudRulesManager->get($entityKey,  EntityPermissionKeyUtil::CREATE);
       if($rules){
           try{
               $crudRulesManager->processRules($rules, $data);
           }
           catch (LaminasValidationException $exception){
               return redirect()->back()->withInput($data)
                   ->withErrors($exception->getErrors());
           }
           catch(\Throwable $exception){
               if ($request->wantsJson()) {
                   return response()->json(['message' => $exception->getMessage()], 400);
               } else {
                $errors = method_exists($exception, 'errors') ? $exception->errors() : [];

                   \Session::flash('danger', __t($exception->getMessage()));
                   return redirect()->back()
                   ->withInput($data)->withErrors($errors);
               }
           }
       }
        $formattedRequest = FormatterManger::format($data, $entityKey, $repository);


        if ($st->getPlugin() && !Plugins::pluginActive($st->getPlugin())) {
            if ($request->wantsJson()) {
                return response()->json(['message' => __t("Plugin is not installed")], 400);
            } else {
                CakeSession::flashMessage(__t("Plugin is not installed"));
                $redirectUrl = \Illuminate\Support\Facades\Request::header('referer') ?? '/';
                return redirect($redirectUrl);
            }
        }

        $formattedRequest = EntityDataHandlerFactory::getEntityStrategy($entityKey, $formattedRequest)->onCreate();
        $appEntitySaver->useRelationName = false;
        $id = $appEntitySaver->save($st, $formattedRequest);
        if ($id) {
           PaginationService::remove($entityKey);
            foreach ($st->getFields() as $field) {
                $handler = ElementListenerFactory::getListener($field->getType());
                if ($handler) {
                    $elementData = new ElementData($st, $field, $id, $data);
                    $handler->handle($elementData);
                }
            }
        }
        if (request()->has('permissions')) {
            $dynamicPermissionHelper->addPermissionToEntity($request->all(), $entityKey, $id);
        }
        $recordData = $showAction->handle($entityKey, $id, 4);
        $recordData =  toArray($recordData);
        $recordData['customEventExtraData'] = $this->getEventExtraData($entityKey, $recordData, $data);
        $manager->triggerEvents($entityKey, 'create', $recordData);
        event(new RecoredCreatedEvent(new RecoredInfo($entityKey, $recordData)));
        unset($recordData['customEventExtraData']);
        // $st = $structureGetter->buildEntity($entityKey);


        if (!empty($data['is_clone'])) {
            $st->getParentEntityData()->getForeignKeyName();
            $parentRelationName = $st->getParentEntityData()->getChildRelation()->getName();
            $recordData['is_clone'] = [$parentRelationName => $recordData[$parentRelationName]];
        }
        if(!empty($data['is_draft_entity'])){
            $recordData['is_draft_entity'] = 1;
        }
        $requests = (new EntityActivityLogRequestsCreator())->create($st, $recordData);
        $requests = EntityActivityLogRequestCustomFormatter::format($st, $requests, $recordData, null);

        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }
        if ($request->wantsJson()) {
            return response()->json(['message' => sprintf(__t('%s Added Successfully'), __t($st->getLabel())), 'id' => $id]);
        } else {
            $message = sprintf(__t('%s Added Successfully'), __t(Str::singular($st->getLabel())));
            if ($entityKey == EntityKeyTypesUtil::WORK_FLOW_TYPE) {
                $url = route('owner.local_entities.builder', ['entityKey' => WorkflowTypeEntityCreator::getWorkFlowEntityName($id), 'redirect' => "/v2/owner/entity/workflow_type/$id/show"]);
            }elseif($st->getParentEntityData()?->getEntityKey() && \request()->reference_id){
                $url = getCustomViewRoute(['entity_key' => $st->getParentEntityData()->getEntityKey(), 'id' => \request()->reference_id]);
            }else{
                $url = getCustomViewRoute(['entity_key' => $entityKey, 'id' => $id, 'record' => $recordData]);
            }
            if (strpos($url, '/v2') !== false) {
                \Session::flash('success', $message);
            } else {
                flashMessageIntoCake($message);
            }
            return redirect($url);
        }
    }

    private function getEventExtraData($entityKey, $recordData, $data){
        return match($entityKey){
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => [
                'is_convert_bom_to_mo' => $data['is_convert_bom_to_mo'] ?? false
            ],
            default => null
        };
    }



}
