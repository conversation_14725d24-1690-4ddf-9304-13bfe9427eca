<?php

namespace App\Modules\LocalEntity\Factories\Formatter;

class AutoStaffFormatter extends FieldFormatter
{
    public function format(&$request, $field)
    {
        if (!empty($request[$field->getEntity()->getPrimaryKey()])) {
            return ;
        }
        if (!empty($request[$field->getName()])) { //when user want to save its data to add value for staff id
            return ;
        }
        /** this should be either owner or staff only */
        $value =  getAuthOwner('staff_id');

        return [
            ['data' => $value , 'key' => $field->getName()],
        ];
    }
}
