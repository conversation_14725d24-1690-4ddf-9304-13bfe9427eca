<?php

namespace App\Modules\LocalEntity\Factories;

use App\Modules\LocalEntity\Factories\ListBehaviors\ContractListBehavior;
use App\Modules\LocalEntity\Factories\ListBehaviors\DefaultListBehavior;
use App\Modules\LocalEntity\Factories\ListBehaviors\DocumentListBehavior;
use App\Modules\LocalEntity\Factories\ListBehaviors\LeaveApplicationListBehavior;
use App\Modules\LocalEntity\Factories\ListBehaviors\ListBehavior;
use App\Modules\LocalEntity\Factories\ListBehaviors\StaffListBehavior;
use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class ListBehaviorFactory
{
    public static function get(string $entityKey): ListBehavior|null
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                return new StaffListBehavior($entityKey);
            case EntityKeyTypesUtil::PAYROLL_CONTRACT:
                return new ContractListBehavior($entityKey);
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY:
                return new LeaveApplicationListBehavior($entityKey);
            case EntityKeyTypesUtil::ENTITY_DOCUMENT:
                return new DocumentListBehavior($entityKey);
            default:
                return new DefaultListBehavior($entityKey);
        }
    }
}
