<?php


namespace App\Modules\LocalEntity\Factories;


use App\Modules\LocalEntity\Factories\ListingMetaData\CurrencyMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\QuantityMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\DateMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\DefaultMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\DropDownMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\DynamicDropDownMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\FileMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\MultipleDynamicDropDownMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\StaticContentMeta;
use App\Modules\LocalEntity\Factories\ListingMetaData\SubFormMeta;
use App\Utils\EntityFieldUtil;

class ListingMetaDataFactory
{

    public static function getMetaObject($type)
    {
        switch ($type) {

            case EntityFieldUtil::ENTITY_FIELD_TYPE_PHOTO:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FILE:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE:
                  return new FileMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN:
                return new DropDownMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_BRANCH:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOLLOW_UP_STATUS:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN_ADVANCED:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST:
                   return new DynamicDropDownMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_CURRENCY_DROPDOWN:
                return new CurrencyMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_QUANTITY:
                return new QuantityMeta();


            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_ASSIGNED_STAFF:
                return new MultipleDynamicDropDownMeta();


            case EntityFieldUtil::ENTITY_FIELD_TYPE_STATIC_CONTENT:
                return new StaticContentMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE:
                return new DateMeta();

            case EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM:
                return new SubFormMeta();

            default:
                return new DefaultMeta();

        }
    }


}
