<?php

namespace App\Modules\LocalEntity\Factories\EntityIdsExtractor;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class EntityIdsExtractorFactory
{
    /**
     * Get the appropriate IDs extractor for the given entity key
     *
     * @param string $entityKey The entity key
     * @return IdsExtractor The IDs extractor
     */
    public static function get(string $entityKey): IdsExtractor
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::ENTITY_DOCUMENT:
                return new DocumentIdsExtractor();
            default:
                return new DefaultIdsExtractor();
        }
    }

    /**
     * Extract IDs from data for the given entity key
     *
     * @param mixed $data The data to extract IDs from
     * @param string $entityKey The entity key
     * @return array The extracted IDs
     */
    public static function extractIds($data, string $entityKey): array
    {
        $extractor = self::get($entityKey);
        return $extractor->extract($data);
    }
}
