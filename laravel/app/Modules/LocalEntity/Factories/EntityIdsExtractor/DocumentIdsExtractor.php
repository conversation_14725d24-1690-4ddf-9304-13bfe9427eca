<?php

namespace App\Modules\LocalEntity\Factories\EntityIdsExtractor;

class DocumentIdsExtractor implements IdsExtractor
{
    /**
     * Extract IDs from document entity data
     *
     * @param mixed $data The document data to extract IDs from
     * @return array The extracted IDs
     */
    public function extract($data): array
    {
        $ids = [];
        foreach ($data as $document) {
            $ids[] = $document->id ?? "type_" . $document->document_types_id;
        }
        return $ids;
    }
}
