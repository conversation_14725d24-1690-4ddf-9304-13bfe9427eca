<?php

namespace App\Modules\LocalEntity\Factories;

use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\ApprovalCycleHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\BomDataHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\EntityDataDefaultHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\LeaseContractDataHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\LeaveApplicationDataHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\ManufactureOrderDataHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\ManufactureOrderIndirectCostDataHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\ProductionPlanDataHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\StockRequestDataHandler;
use App\Modules\LocalEntity\Factories\Strategies\EntityDataHandler\WorkstationDataHandler;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class EntityDataHandlerFactory
{
    protected static $handlers = [
        EntityKeyTypesUtil::BOM_ENTITY_KEY => BomDataHandler::class,
        EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => WorkstationDataHandler::class,
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => ManufactureOrderDataHandler::class,
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => ManufactureOrderIndirectCostDataHandler::class,
        EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => StockRequestDataHandler::class,
        EntityKeyTypesUtil::PRODUCTION_PLAN => ProductionPlanDataHandler::class,
        EntityKeyTypesUtil::LEASE_CONTRACT => LeaseContractDataHandler::class,
        EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => ApprovalCycleHandler::class,
        EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => LeaveApplicationDataHandler::class,
    ];

    public static function getEntityStrategy(string $entityKey, $entityData)
    {
        if(isset(static::$handlers[$entityKey])){
            return new static::$handlers[$entityKey]($entityData);
        }else{
            return new EntityDataDefaultHandler($entityData);
        }
    }
}
