<?php

namespace App\Modules\LocalEntity\Factories;

use App\Facades\Plugins;
use App\Strategies\EntityNotifications\AddMembershipNotificationStrategy;
use App\Strategies\EntityNotifications\LeaveApplicationNotificationStrategy;
use App\Strategies\EntityNotifications\PayslipNotificationStrategy;
use App\Strategies\EntityNotifications\StockRequestNotificationStrategy;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use App\Utils\PluginUtil;

class EntityNotificationFactory
{
    public static function getNotificationStrategy($entityKey, $entityData)
    {
        switch ($entityKey) {
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY:
                if (Plugins::pluginInstalled(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
                    return  new LeaveApplicationNotificationStrategy($entityData);
                }
                break;
            case EntityKeyTypesUtil::PAYSLIP:
                if(Plugins::pluginInstalled(PluginUtil::HRM_PAYROLL_PLUGIN)) {
                    return new PayslipNotificationStrategy($entityData);
                }
                break;
            case EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY:
                if (Plugins::pluginInstalled(PluginUtil::InventoryPlugin)) {
                    return  new StockRequestNotificationStrategy($entityData);
                }
                break;
            case EntityKeyTypesUtil::MEMBERSHIP_ENTITY_KEY:
                return new AddMembershipNotificationStrategy($entityData);
        }

        return false;
    }
}
