<?php

namespace App\Modules\LocalEntity\Factories\ListBehaviors;

class DefaultListBehavior implements ListBehavior
{
    public function __construct(protected $entity)
    {
    }

    public function beforeList()
    {

    }

    public function afterList($data)
    {
        return $data;
    }

    public function afterMetaGeneration(array $metaFields) :array
    {
        return $metaFields;
    }

    public function getResetFiltersUrl(): string
    {
        return url('owner/entity/' . $this->entity . '/list?reset=1');
    }
}
