<?php

namespace App\Modules\LocalEntity\Factories\ListBehaviors;

use Illuminate\Support\Facades\Request;

class DocumentListBehavior extends DefaultListBehavior implements ListBehavior
{
    public function getResetFiltersUrl(): string
    {
        $filter = Request::input('filter', []);
        return url('/owner/entity/entity_document/list?iframe=1&filter[entity_key]=' . ($filter['entity_key'] ?? '') . '&filter[entity_id]=' . ($filter['entity_id'] ?? '') . '&show_filters=1&filter[get_unique]=1');
    }
}