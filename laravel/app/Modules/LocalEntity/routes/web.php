<?php

use App\Http\Middleware\CheckEntityActionPermission;
use App\Modules\LocalEntity\Http\Controllers\E\EntityDynamicController;
use App\Utils\EntityKeyTypesUtil;
use Illuminate\Support\Facades\Route;
use Izam\Daftra\Common\Utils\PermissionUtil;
$needsCustomViewsArray = [
    EntityKeyTypesUtil::PURCHASE_REQUEST,
    EntityKeyTypesUtil::QUOTATION_REQUEST,
    EntityKeyTypesUtil::WORK_ORDER,
    EntityKeyTypesUtil::WORK_FLOW_TYPE,
    EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY,
    EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY,
    EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY,
    EntityKeyTypesUtil::REQUEST_TYPE_ENTITY_KEY,
    EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY,
    EntityKeyTypesUtil::BOM_ENTITY_KEY,
    EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY,
    EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY,
    EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST,
    EntityKeyTypesUtil::PRODUCTION_PLAN,
    EntityKeyTypesUtil::LOYALTY_RULE,
];
Route::group(['namespace' => '\App\Modules\LocalEntity\Http\Controllers'], function(){
    Route::get('entity/files/get_implementer_studio_file/{id}', 'FileUploadController@getImplementerStudioFile')
        ->name('entity.files.get_implementer_studio_file');
});
Route::group(['namespace' => '\App\Modules\LocalEntity\Http\Controllers','prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite', 'checkAssignedItemStaff']], function () use ($needsCustomViewsArray) {
    // Removed 'isOwner' middleware since this route is also accessed by clients to preview S3-hosted images.
    Route::get('entity/files/preview/{id}', 'FileUploadController@getMedia')
        ->name('entity.files.preview')->withoutMiddleware(['isOwner']);

    /** @todo will be remove later after test new drawer */
    $oldEntitiesListing = ['purchase_request', 'quotation_request', 'purchase_quotation', 'purchase_order-13', 'loyalty_rule'];
    foreach ($oldEntitiesListing as $entityKey) {
        Route::get("entity/{$entityKey}/list", 'Entity\EntityListDrawerController@index')
            ->defaults('entityKey', $entityKey)
            ->name('entity.list')->middleware('checkEntityActionPermissions:list');
    }
    Route::get('entity/get_multi_action_ids/{entityKey}/{redirectMethod?}', 'Entity\EntityMultipleActionController@getIds')
        ->name('entity.get_ids_for_multi_action');

    Route::get('entity/{entityKey}/list', function($entityKey) {
        if (preg_match('/^le_|^app_/', $entityKey)) {
            return app()->call('App\Modules\LocalEntity\Http\Controllers\Entity\EntityListDrawerController@index', ['entityKey' => $entityKey]);
        }

        if (str_starts_with($entityKey, 'le_request_type_')) {
            return app()->call('App\Modules\LocalEntity\Http\Controllers\Entity\EntityListDrawerController@index', ['entityKey' => $entityKey]);
        }
        return app()->call('App\Modules\LocalEntity\Http\Controllers\Entity\EntityListDrawerIzamViewController@index', ['entityKey' => $entityKey]);
    })->name('entity.list')->middleware('checkEntityActionPermissions:list');

    Route::get('entity/{entityKey}/old/list', 'Entity\EntityListDrawerController@index')
    ->name('entity.list-old')->middleware('checkEntityActionPermissions:list_old');

    //this route is actually getting the ids & redirecting data to another page
    Route::POST('entity/bulk-delete/{entityKey}', 'Entity\EntityBulkDeleteController@destroy')
        ->name('entity.bulk-delete')->middleware('checkEntityActionPermissions:delete');
    Route::POST('entity-bulk-delete/{entityKey}', 'Entity\EntityBulkDeleteController@destroy')
        ->name('entity.bulk-delete')->middleware('checkEntityActionPermissions:delete');

    Route::POST('entity/bulk-get-ids/{entityKey}', 'Entity\EntityBulkDeleteController@jsonGetIds')
        ->name('entity.bulk-get-ids');

    Route::get('entity/{entityKey}/grid', 'Entity\EntityGridListDrawerController@index')
        ->name('entity.grid_list')->middleware('checkEntityActionPermissions:list');

    Route::get('entity/{entityKey}/timeline', 'Entity\EntityTimelineListDrawerController@index')
        ->name('entity.timeline_list')->middleware('checkEntityActionPermissions:list');

    Route::get('entity/{entityKey}/create', 'Entity\EntityCreateController@create')
        ->middleware('checkEntityActionPermissions:create')
        ->name('entity.create');

    Route::get('entity/{entityKey}/create', function ($entityKey) use ($needsCustomViewsArray){
        if (in_array($entityKey , $needsCustomViewsArray) || str_starts_with($entityKey, 'le_')) {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityCreateController@create', ['entityKey' => $entityKey]);
        }else{
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityCreateIzamViewController@create', ['entityKey' => $entityKey]);
        }
    })
        ->middleware(['checkEntityActionPermissions:create','BreadCrumbsMiddleware'])
        ->name('entity.create');

    Route::get('children/{entityKey}/create/{refId}', function ($entityKey, $refId) use ($needsCustomViewsArray){
        /** specifically for app manager related entities with type workflow */
        if (str_starts_with($entityKey, 'app_') || str_starts_with($entityKey, \Izam\Dynamic\List\Utils\DynamicListUtil::ADDITIONAL_FIELDS_PREFIX)) {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityCreateIzamViewController@create', [
                'entityKey' => $entityKey,
                'refId' => $refId,
            ]);
        }

        return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityCreateController@create', [
            'entityKey' => $entityKey,
            'refId' => $refId,
        ]);
    })
        ->middleware(['BreadCrumbsMiddleware'])
        ->name('child.create');

    Route::post('entity/{entityKey}', function ($entityKey) use ($needsCustomViewsArray) {
        if (in_array($entityKey, $needsCustomViewsArray) || str_starts_with($entityKey, 'le_')) {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityCreateController@store', ['entityKey' => $entityKey]);
        } else {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityCreateIzamViewController@store', ['entityKey' => $entityKey]);
        }
    })
        ->middleware('checkEntityActionPermissions:create')
        ->name('entity.store');

    Route::delete('entity/{entityKey}/{id}/delete', 'Entity\EntityDeleteController')
        ->name('entity.delete')->middleware(['checkEntityActionPermissions:delete']);

    // DEPRECATED
    Route::delete('entity/{entityKey}/{id}', 'Entity\EntityDeleteController')
        ->name('entity.delete.old')->middleware(['checkEntityActionPermissions:delete']);




    $newUIViews = [
        EntityKeyTypesUtil::PRODUCTION_PLAN,
        EntityKeyTypesUtil::BOM_ENTITY_KEY,
        EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY,
        EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY,
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST,
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY,
        EntityKeyTypesUtil::EMAIL_TEMPLATE_LOG,
        EntityKeyTypesUtil::BRAND,
        EntityKeyTypesUtil::VEHICLE,
        EntityKeyTypesUtil::CONTRACT_INSTALLMENT,
        EntityKeyTypesUtil::LEASE_CONTRACT,
        EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
        EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION,
        EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
        EntityKeyTypesUtil::ENTITY_DOCUMENT,
    ];

    Route::get('entity/{entityKey}/{id}/show', function ($entityKey, $id) use ($newUIViews){
        if (in_array($entityKey , $newUIViews)) {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityViewIzamViewController@show', ['entityKey' => $entityKey, 'id' => $id]);
        }else{
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityViewController@show', ['entityKey' => $entityKey, 'id' => $id]);
        }
    })
        ->middleware(['checkEntityActionPermissions:view', 'BreadCrumbsMiddleware'])
        ->name('entity.show');


    Route::get('future-entity/{entityKey}/{id}/show', 'Entity\EntityViewIzamViewController@show')
        ->name('future-entity.show')
        ->middleware(['checkEntityActionPermissions:view']);

    Route::get('fake_entity/{entityKey}/{id}/show', 'Entity\EntityFakeViewController@show')
        ->name('entity.fake_show')
        ->middleware(['checkEntityActionPermissions:view'])
    ;

    /** commented because duplicated below, will be overwritten anyway */
    /*Route::get('entity/{entityKey}/{id}/edit', 'Entity\EntityUpdateController@edit')
        ->name('entity.edit')
        ->middleware(['checkEntityActionPermissions:update']);*/

    Route::get('entity/{entityKey}/{id}/edit', function ($entityKey, $id) use ($needsCustomViewsArray) {
        if (in_array($entityKey, $needsCustomViewsArray) || str_starts_with($entityKey, 'le_')) {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityUpdateController@edit', ['entityKey' => $entityKey, 'id' => $id]);
        } else {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityUpdateIzamViewController@edit', ['entityKey' => $entityKey, 'id' => $id]);
        }
    })
        ->name('entity.edit')
        ->middleware(['checkEntityActionPermissions:update', 'BreadCrumbsMiddleware']);

    Route::get('entity/{sourceEntityKey}/{id}/clone/{entityKey}', 'Entity\EntityCloneController')
        ->name('entity.clone')->middleware(['checkEntityActionPermissions:clone']);

    Route::put('entity/{entityKey}/{id}', function ($entityKey, $id) use ($needsCustomViewsArray) {
        if (in_array($entityKey, $needsCustomViewsArray) || str_starts_with($entityKey, 'le_'))  {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityUpdateController@update', ['entityKey' => $entityKey, 'id' => $id]);
        } else {
            return app()->call('\App\Modules\LocalEntity\Http\Controllers\Entity\EntityUpdateIzamViewController@update', ['entityKey' => $entityKey, 'id' => $id]);
        }
    })
        ->name('entity.update')->middleware(['checkEntityActionPermissions:update']);


    Route::get('entity/{entityKey}/permissions', 'Entity\EntityDynamicPermissionController@edit')
        ->name('entity.permissions');

    Route::post('entity/{entityKey}/permissions', 'Entity\EntityDynamicPermissionController@save')
        ->name('entity.permissions.post');

    Route::POST('entity/get_ids/{entityKey}', 'Entity\EntityBulkActionController@getIds')
        ->name('entity.get-ids');

    Route::get('local_entities', 'LocalEntityController@index')
        ->name('local_entities.index');
    Route::get('local_entities/custom_data/{entity}/create', 'LocalEntityController@createCustomDataEntity')
        ->name('local_entities.createCustomData');



    Route::get('local_entities/{entityKey}/edit', 'LocalEntityController@edit')
        ->name('local_entities.edit')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::DELETE('local_entities/destroy/{entityKey}', 'LocalEntityController@destroy')
        ->name('local_entities.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('local_entities/create', 'LocalEntityController@create')
        ->name('local_entities.create');

    Route::get('entity/get-nav-next', 'Entity\PaginationNextCalculatorController')
        ->name('entity.get-nav-next');

    Route::get('entity/get-nav-previous', 'Entity\PaginationPreviousCalculatorController')
        ->name('entity.get-nav-previous');

    Route::get('entity/entity-get-nav-next', 'Entity\EntityPaginationNextCalculatorController')
        ->name('entity.entity-get-nav-next');

    Route::get('entity/entity-get-nav-previous', 'Entity\EntityPaginationPreviousCalculatorController')
        ->name('entity.entity-get-nav-previous');


    Route::get('local_entities/{entityKey}/edit', 'LocalEntityController@edit')
        ->name('local_entities.edit');

    Route::post('local_entities', 'LocalEntityController@store')
        ->name('local_entities.store')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::put('local_entities/{entityKey}', 'LocalEntityController@update')
        ->name('local_entities.update')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);;

    Route::get('local_entities/builder/{entityKey}', 'LocalEntityController@builder')
        ->name('local_entities.builder')
      //   ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings)
        ;

    Route::post('local_entities/{entityKey}/save_fields', 'LocalEntityController@saveFields')
        ->name('local_entities.save_fields')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::delete('local_entities/{entityKey}', 'LocalEntityController@destroy')
        ->name('local_entities.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('local_entities/unique', 'LocalEntityController@unique')
        ->name('local_entities.unique');

    Route::get('local_entities/search', 'LocalEntityController@ajaxNameFilter')->name('local_entities.search');

    Route::get('local_entities/{entityKey}', 'LocalEntityController@show')
        ->name('local_entities.show');

    Route::get('local_entities_data/{localEntityKey}', 'LocalEntityDataController@index')
        ->name('local_entities_data.index');

    Route::get('local_entities_data/{localEntityKey}/create', 'LocalEntityDataController@create')
        ->name('local_entities_data.create');


    Route::post('local_entities_data/{localEntityKey}', 'LocalEntityDataController@store')
        ->name('local_entities_data.store');


    Route::post('local_entities_data/{localEntityKey}', 'LocalEntityDataController@store')
        ->name('local_entities_data.store');

    Route::put('local_entities_data/{localEntityKey}/{id}', 'LocalEntityDataController@update')
        ->name('local_entities_data.update');


    Route::get('local_entities_data/{localEntityKey}/{id}/edit', 'LocalEntityDataController@edit')
        ->name('local_entities_data.edit');

    Route::get('local_entities_data/{localEntityKey}/{id}', 'LocalEntityDataController@show')
        ->name('local_entities_data.show');

    Route::delete('local_entities_data/{localEntityKey}/{id}/destroy', 'LocalEntityDataController@destroy')
        ->name('local_entities_data.destroy');


    Route::get('local_entity_trashed/{localEntityKey}', 'LocalEntityDataTrashedController')
        ->name('local_entities_data.trashed');

    Route::PATCH('local_entity_trashed/{localEntityKey}', 'LocalEntityDataRestoreController')
        ->name('local_entities_data.restore');

    /**
     * Refactor Entities .
    */
    Route::get('e/list',[EntityDynamicController::class , 'index'])->name('entity-list');
    Route::get('e/{key}/show/{id}',[EntityDynamicController::class , 'show'])->name('entity-show');
    Route::delete('e/{key}/delete/{id}',[EntityDynamicController::class , 'delete'])->name('entity-delete');
    Route::get('e/{key}/add',[EntityDynamicController::class , 'add'])->name('entity-add');
    Route::post('e/{key}/store',[EntityDynamicController::class , 'store'])->name('entity-store');
    Route::get('e/{key}/list',[EntityDynamicController::class , 'list'])->name('entity-list');
    Route::get('e/{key}/edit/{id}',[EntityDynamicController::class , 'edit'])->name('entity-edit');
    Route::post('e/{key}/update',[EntityDynamicController::class , 'update'])->name('entity-update');

    Route::get('entity-dynamic-search/json-find','DynamicLocalEntityDataController@getDynamicEntityDataAjax')->name('dynamic_entity_data_search_ajax');

    });

    Route::get('owner\local_entities/criteria_type_data', '\App\Http\Controllers\DynamicPermissionsController@getValuesOfCriteriaType')
        ->name('owner.local_entities.getValuesOfCriteriaType');
