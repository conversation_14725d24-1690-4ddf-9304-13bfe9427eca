<?php
use Illuminate\Support\Facades\Route;
use Izam\Daftra\Common\Utils\PermissionUtil;

// Fixed CORS Don't Edit
// if (!empty($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
//    header('Access-Control-Allow-Origin: *');
//    header('Access-Control-Allow-Methods: POST, GET, DELETE, PUT, PATCH, OPTIONS');
//    header('Access-Control-Allow-Headers: token, Content-Type');
//    header('Access-Control-Max-Age: 1728000');
//    header('Content-Length: 0');
//    header('Content-Type: text/plain');
//    die();
// }
// header('Access-Control-Allow-Origin: *');
// header('Content-Type: application/json');
// Route::group(['namespace' => '\App\Modules\LocalEntity\Http\Controllers'], function () {
//     Route::get('currencies/list', 'CurrencyController@getList')->name('api.currencies.get_list');
//     Route::post('local_entities/{entityKey}/save_fields', 'LocalEntityController@saveFields');

//     Route::get('local_entities/{entityKey}/get_fields', 'LocalEntityController@getFields')
//     ->name('local_entities.get_fields');

//     Route::get('local_entities/site_config', 'LocalEntityController@getLocalEntityConfig')
//     ->name('local_entities.site_config');

//     Route::get('local_entities/get_entities_list', 'LocalEntityController@listAllEntities')
//     ->name('local_entities.list_all_entities');

//     Route::get('local_entities/{entityKey}/list_entity_fields', 'LocalEntityController@listEntityFields')
//     ->name('local_entities.list_entity_fields');

//     Route::get('local_entities/{fieldKey}/get_field_values', 'LocalEntityController@getFieldValues')
//     ->name('local_entities.get_field_values');

//     Route::get('local_entities/view_parent_list', 'LocalEntityController@getViewInParentList')
//     ->name('local_entities.view_parent_list');

//     Route::get('entity/{entityKey}/list-photo/{dataLevel?}', 'Entity\EntityListController@listPhoto')
//         ->name('entity.list-photo');
// });

Route::group(['namespace' => '\App\Modules\LocalEntity\Http\Controllers', 'middleware' => ['double-oauth:api']], function () {

    Route::delete('entity/delete_file/{fieldId}', 'FileUploadController@delete_file')
    ->name('entity.delete_file');

    Route::post('local_entities/{entityKey}/save_fields', 'LocalEntityController@saveFields');

    Route::get('local_entities/{entityKey}/get_fields', 'LocalEntityController@getFields')
        ->name('local_entities.get_fields');

    Route::get('local_entities/site_config', 'LocalEntityController@getLocalEntityConfig')
        ->name('local_entities.site_config');

    Route::get('local_entities/get_entities_list', 'LocalEntityController@listAllEntities')
        ->name('local_entities.list_all_entities');

    Route::get('local_entities/{entityKey}/list_entity_fields', 'LocalEntityController@listEntityFields')
        ->name('local_entities.list_entity_fields');

    Route::get('local_entities/{fieldKey}/get_field_values', 'LocalEntityController@getFieldValues')
        ->name('local_entities.get_field_values');

    Route::get('local_entities/view_parent_list', 'LocalEntityController@getViewInParentList')
        ->name('local_entities.view_parent_list');

    Route::post('/entity/upload_file/{entityKey}/{fieldKey}', 'FileUploadController@upload_file')
        ->name('entity.upload_file');
    
    Route::post('/entity_custom/upload_file/{entityKey}/{fieldKey}', 'FileUploadController@upload_file_custom_entity')
        ->name('entity.upload_file_entity_custom');

    Route::delete('entity/delete/{entityKey}/{id}', 'Entity\EntityDeleteController')
        ->middleware('checkEntityActionPermissions:delete')
        ->name('entity.delete');

    Route::post('local_entities_data/{localEntityKey}', 'Entity\EntityCreateController@store')
        ->name('local_entities_data.store');


    Route::put('local_entities_data/{localEntityKey}/{id}', 'Entity\\EntityUpdateController@update')
        ->name('local_entities_data.update');

    Route::get('entity/{entityKey}/fields', 'Entity\EntityFieldAction')
        ->name('entity.fields');

    Route::get('entity/{entityKey}/old-fields', 'Entity\EntityFieldAction@getOldFieldsStructure')
        ->name('entity.fields');

    Route::get('entities/{entityKey}/schema/{level?}', 'Entity\ShowEntitySchemaController')
        ->name('entities.get_fully_schema');

    Route::get('entities/{entityKey}/fields/{name}', 'Entity\ShowFieldPropertiesController')
        ->name('entities.get_field_properties');

    Route::get('entities/{entityKey}/relations', 'Entity\ShowEntityRelationsController')
        ->name('entities.get_entity_relations');

    Route::get('entity/{entityKey}/list/{dataLevel?}', 'Entity\EntityListController')
        ->name('entity.list');

    Route::get('pos_devices/list/{dataLevel?}', 'Entity\EntityListController@listPosDevices')
        ->name('entity.pos_devices');

    Route::get('entity/{entityKey}/list-photo/{dataLevel?}', 'Entity\EntityListController@listPhoto')
        ->name('entity.list-photo');

    Route::get('entity/{entityKey}/list-zapier', 'Entity\EntityListController@listForZapier')
        ->name('entity.list-zapier');

    Route::get('entity/auto-suggest/multi-fields/{entityKey}/{pluckBy}', 'Entity\EntityListController@getMultiFieldKeys')
        ->name('entity.multi-fields-auto-suggest');

    Route::get('/entity/{entity}/filter-auto-suggest/{dataLevel?}', 'Entity\EntityAutoSuggestController')
        ->name('entity.filter-auto-suggest');

    Route::get('entity/auto-suggest/{fieldKey}', 'Entity\EntityListController@getFieldKey')
        ->name('entity.field-auto-suggest');

    Route::get('entity/{entityKey}/structure', 'Entity\EntityStructureController')
        ->name('entity.structure');

    Route::get('entity/{entityKey}/fake/{dataLevel?}', 'Entity\EntityFakeDataController')
        ->name('entity.fake');

    Route::get('entity/{entityKey}/{id}/{dataLevel?}', 'Entity\EntityShowController')
        ->name('entity.show');

    Route::post('entity/{entityKey}', 'Entity\EntityCreateController@store')
        ->name('entity.store')->middleware('checkEntityActionPermissions:create');

    Route::put('entity/{entityKey}/{id}', 'Entity\EntityUpdateController@update')
        ->name('entity.update')->middleware('checkEntityActionPermissions:update');


    Route::post('entity/{entityKey}/{id}/fields', 'Entity\\EntityUpdateController@updateCols')
        ->name('entity.updateFields');

    Route::post('/entity/upload_file_withouConfig/{entityKey}/{fieldKey}', 'FileUploadController@upload_file_withoutConfig')
        ->name('entity.upload_file_withoutConfig');

    Route::get('entities/list', 'Entity\ListEntitiesController')
        ->name('entities.list');

    Route::get('/entity/set_file_default/{entityKey}/{entityId}/{fieldId}', 'FileUploadController@set_as_default')
    ->name('entity.set_default');

    Route::get('/list/s3Attachmnets/{entity}', 'Entity\EntityS3AttachmentsController@listS3Attachmnets');
    Route::get('/list/s3Attachmnets/{entity}/{entityId}', 'Entity\EntityS3AttachmentsController@listS3AttachmnetsByEntityKeyEntityId');
    Route::post('/getS3Path', 'Entity\EntityS3AttachmentsController@getS3Path');
});


Route::group([
    'namespace' => '\App\Modules\LocalEntity\Http\Controllers\API\LocalEntity',
    'middleware' => [
        'double-oauth:api',
        'checkPermissions:' . PermissionUtil::Edit_General_Settings,
    ],
    'prefix' => 'local_entities',
    'as' => 'api.local_entities.'
], function () {
    Route::get('create', 'CreateController');
    Route::post('/', 'StoreController');
    Route::get('{entityKey}/edit', 'EditController');
    Route::put('{entityKey}', 'UpdateController');
    Route::get('permissions/criteria_type_data', 'DynamicPermissionsController')->name('permissions.criteria_type_data');
});
