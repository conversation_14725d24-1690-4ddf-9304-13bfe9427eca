<?php

namespace App\Modules\LocalEntity\Events;

use App\Modules\LocalEntity\Queue\Events\BomCreated;
use App\Modules\LocalEntity\Queue\Events\BrandUpdated;
use App\Modules\LocalEntity\Queue\Events\ContractInstallmentchanged;
use App\Modules\LocalEntity\Queue\Events\ContractInstallmentDeleted;
use App\Modules\LocalEntity\Queue\Events\ItemGroupCreated;
use App\Modules\LocalEntity\Queue\Events\LeaveApplication\LeaveApplicationCreated;
use App\Modules\LocalEntity\Queue\Events\LeaveApplication\LeaveApplicationUpdated;
use App\Modules\LocalEntity\Queue\Events\ManufacturingOrderCreated;
use App\Modules\LocalEntity\Queue\Events\ManufacturingOrderIndirectCostCreated;
use App\Modules\LocalEntity\Queue\Events\ManufacturingOrderIndirectCostDeleted;
use App\Modules\LocalEntity\Queue\Events\ManufacturingOrderIndirectCostUpdated;
use App\Modules\LocalEntity\Queue\Events\ServiceFormsCustomDataCreated;
use App\Modules\LocalEntity\Queue\Events\MultiCycleApprovalConfigurationChanged;
use Izam\ManufacturingOrder\Events\ManufacturingOrderUpdated;
use App\Modules\LocalEntity\Queue\Events\PayrunCreatedEvent;
use App\Modules\LocalEntity\Queue\Events\ProductionPlanCreated;
use App\Modules\LocalEntity\Queue\Events\ProductionPlanDeleted;
use App\Modules\LocalEntity\Queue\Events\ProductionPlanItemDeleted;
use App\Modules\LocalEntity\Queue\Events\ProductionPlanUpdated;
use App\Modules\LocalEntity\Queue\Events\RequestTypeCreatedEvent;
use App\Modules\LocalEntity\Queue\Events\RequestTypeUpdatedEvent;
use App\Modules\LocalEntity\Queue\Events\StockRequestDeleted;
use App\Modules\LocalEntity\Queue\Events\WorkFlowTypeCratedEvent;
use App\Modules\LocalEntity\Queue\Events\WorkFlowTypeDeletedEvent;
use App\Modules\LocalEntity\Queue\Events\WorkFlowTypeUpdatedEvent;
use App\Modules\LocalEntity\Queue\Events\WorkstationUpdated;
use App\Utils\EntityKeyTypesUtil;

class EntityActionsEventRepo implements EntityActionEventRepoInterface
{
    private $actionEvents = [
        EntityKeyTypesUtil::WORK_FLOW_TYPE => [
            'create' => [
                WorkFlowTypeCratedEvent::class,
            ],
            'update' => [
              WorkFlowTypeUpdatedEvent::class
            ],
            'delete' => [
                WorkFlowTypeDeletedEvent::class
            ],
        ],
        EntityKeyTypesUtil::PAY_RUN => [
            'create' => [
                PayrunCreatedEvent::class,
            ]
        ],
        EntityKeyTypesUtil::REQUEST_TYPE_ENTITY_KEY => [
            'create' => [
                RequestTypeCreatedEvent::class,
            ],
            'update' => [
                RequestTypeUpdatedEvent::class,
            ]
        ],
        EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => [
            'create' => [
                ItemGroupCreated::class,
            ],
            'update'=>[
                ItemGroupCreated::class,
            ]
        ],
        EntityKeyTypesUtil::BOM_ENTITY_KEY => [
            'create' => [
                BomCreated::class
            ],
            'update'=>[
                BomCreated::class,
            ]
        ],
        EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => [
            'update'=>[
                WorkstationUpdated::class,
            ]
        ],
        EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => [
            'delete'=>[
                StockRequestDeleted::class,
            ]
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => [
            'create' => [
                ManufacturingOrderCreated::class
            ],
            'update'=>[
                ManufacturingOrderUpdated::class,
            ]
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => [
            'create' => [
                ManufacturingOrderIndirectCostCreated::class
            ],
            'update'=>[
                ManufacturingOrderIndirectCostUpdated::class,
            ],
            'delete' => [
                ManufacturingOrderIndirectCostDeleted::class
            ],
        ],
        EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
            'create' => [
                LeaveApplicationCreated::class
            ],
            'update' => [
                LeaveApplicationUpdated::class
            ],
        ],
        EntityKeyTypesUtil::PRODUCTION_PLAN => [
            'create' => [
                ProductionPlanCreated::class
            ],
            'update' => [
                ProductionPlanUpdated::class
            ],
            'delete' => [
                ProductionPlanDeleted::class
            ],
        ],
        EntityKeyTypesUtil::PRODUCTION_PLAN_ITEM_ENTITY_KEY => [
            'delete' => [
                ProductionPlanItemDeleted::class
            ],
        ],
        EntityKeyTypesUtil::BRAND => [
            'update' => [
                BrandUpdated::class
            ],
        ],
        EntityKeyTypesUtil::CONTRACT_INSTALLMENT => [
            'create' => [
                ContractInstallmentchanged::class
            ],
            'update' => [
                ContractInstallmentchanged::class
            ],
            'delete' => [
                ContractInstallmentDeleted::class
            ],

        ],
        EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => [
            'create' => [
                ServiceFormsCustomDataCreated::class
            ],
        ],
        EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => [
            'create' => [
                MultiCycleApprovalConfigurationChanged::class
            ],
            'update' => [
                MultiCycleApprovalConfigurationChanged::class
            ],
            'delete' => [
                MultiCycleApprovalConfigurationChanged::class
            ],

        ],

    ];

    public function getEntityEvents($entityKey): array
    {
        return $this->actionEvents[$entityKey]??[];
    }
}
