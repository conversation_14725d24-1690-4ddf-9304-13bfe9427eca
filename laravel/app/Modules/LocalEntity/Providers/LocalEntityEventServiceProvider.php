<?php

namespace App\Modules\LocalEntity\Providers;

use App\Events\LocalEntityBeforeCreate;
use App\Events\LocalEntityBeforeUpdate;
use App\Listeners\EntityApproveActionNotificationListener;
use App\Listeners\EntityCreatedNotificationListener;
use App\Listeners\EntityDeletedNotificationListener;
use App\Listeners\EntityLabelUpdateListener;
use App\Listeners\EntityRejectActionNotificationListener;
use App\Listeners\EntityUndoApproveNotificationListener;
use App\Listeners\EntityUndoRejectNotificationListener;
use App\Listeners\EntityUpdatedNotificationListener;
use App\Modules\LocalEntity\Events\ActivityLogs\AddEvent;
use App\Modules\LocalEntity\Events\ActivityLogs\DeleteEvent;
use App\Modules\LocalEntity\Events\ActivityLogs\UpdateEvent;
use App\Modules\LocalEntity\Events\ElementTagsEvent;
use App\Modules\LocalEntity\Events\RecoredCreatedEvent;
use App\Modules\LocalEntity\Events\RecoredUpdatedEvent;
use App\Modules\LocalEntity\Events\RecoredDeletedEvent;
use App\Modules\LocalEntity\Events\EntitySaved;
use App\Modules\LocalEntity\Listeners\ActivityLogs\AddListener;
use App\Modules\LocalEntity\Listeners\ActivityLogs\DeleteListener;
use App\Modules\LocalEntity\Listeners\ActivityLogs\UpdateListener;
use App\Modules\LocalEntity\Listeners\ElementTagsListener;
use App\Modules\LocalEntity\Listeners\UpdateTempFiles;
use App\Modules\LocalEntity\Listeners\AutoReminderMessageListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use App\Listeners\MenuItemCacheListener;
use App\Listeners\ValidateAndCreateServiceForm;
use App\Listeners\ValidateAndUpdateServiceForm;
use App\Modules\LocalEntity\Events\RecoredApprovedEvent;
use App\Modules\LocalEntity\Events\RecoredRejectedEvent;
use App\Modules\LocalEntity\Events\RecoredUndoApproveEvent;
use App\Modules\LocalEntity\Events\RecoredUndoRejectEvent;
use Illuminate\Support\Facades\Event;

class LocalEntityEventServiceProvider extends ServiceProvider
{
    protected $listen = [
        EntitySaved::class => [
            UpdateTempFiles::class
        ],
        AddEvent::class => [
            AddListener::class
        ],
        UpdateEvent::class => [
            UpdateListener::class
        ],
        DeleteEvent::class => [
            DeleteListener::class
        ],
        ElementTagsEvent::class => [
            ElementTagsListener::class
        ],
        RecoredCreatedEvent::class => [
            AutoReminderMessageListener::class,
            MenuItemCacheListener::class,
            EntityCreatedNotificationListener::class
        ],
        RecoredUpdatedEvent::class => [
            AutoReminderMessageListener::class,
            MenuItemCacheListener::class,
            EntityLabelUpdateListener::class,
            EntityUpdatedNotificationListener::class,
        ],
        RecoredDeletedEvent::class => [
            MenuItemCacheListener::class,
            EntityDeletedNotificationListener::class
        ],
        RecoredApprovedEvent::class => [
            EntityApproveActionNotificationListener::class
        ],
        RecoredRejectedEvent::class => [
            EntityRejectActionNotificationListener::class
        ],
        RecoredUndoApproveEvent::class => [
            EntityUndoApproveNotificationListener::class
        ],
        RecoredUndoRejectEvent::class => [
            EntityUndoRejectNotificationListener::class
        ]
    ];

    public function boot()
    {
        Event::listen(
            LocalEntityBeforeCreate::class,
            ValidateAndCreateServiceForm::class,
        );
        Event::listen(
            LocalEntityBeforeUpdate::class,
            ValidateAndUpdateServiceForm::class,
        );
    }
}
