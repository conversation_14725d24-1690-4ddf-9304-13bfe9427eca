<?php

namespace App\Modules\LocalEntity\Services;

use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Factories\Views\DefaultViewActionsFactory;
use App\Modules\LocalEntity\Forms\ViewAction\ViewActionManager;
use App\Services\Attendance\LeaveApplication\ShowService;
use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\View\Form\Element\ViewActions;

class EntityViewActions
{
    public static function get(string $entityKey, AppEntityData $data, $formattedData, $viewExtraData = []): ViewActions
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => self::getLeaveApplicationActions($data),
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => self::getItemGroupActions($data),
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => self::getStockRequestActions($data),
            EntityKeyTypesUtil::EMAIL_TEMPLATE_LOG => new ViewActions(),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => self::getManufacturingOrderActions($data, $viewExtraData),
            EntityKeyTypesUtil::BOM_ENTITY_KEY => self::getBomActions($data),
            EntityKeyTypesUtil::PRODUCTION_PLAN => self::getProductionPlanActions($data),
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => self::getContractInstallmentActions($data),
            EntityKeyTypesUtil::VEHICLE => self::getVehicleActions($data),
            EntityKeyTypesUtil::LEASE_CONTRACT => self::getLeaseContractActions($data, $viewExtraData),
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => self::getApprovalCycleActions($data),
            EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => self::getServiceFormActions($data, $viewExtraData),
            EntityKeyTypesUtil::ENTITY_DOCUMENT => self::getEntityDocumentActions($data, $viewExtraData),
            default => self::getDefaultActions($entityKey, $data, $formattedData)
        };
    }

    private static function getDefaultActions($entityKey, $data, $formattedData): ViewActions
    {
        /** @var $entityStructureGetter AppEntityStructureGetter */
        $entityStructureGetter = resolve(AppEntityStructureGetter::class);
        $structure = $entityStructureGetter->buildEntity($entityKey);

        $actionManager= resolve(ViewActionManager::class);

        $actions = $actionManager->get($structure, $formattedData);

        return DefaultViewActionsFactory::create($actions, $structure, $data);
    }

    private static function getLeaveApplicationActions(AppEntityData $data): ViewActions
    {
        /** @var $showService ShowService */
        $showService = resolve(ShowService::class);

        return $showService->getActions($data);
    }

    private static function getItemGroupActions(AppEntityData $data): ViewActions
    {
        /** @var $showService \App\Services\Inventory\ItemGroup\ShowService */
        $showService = resolve(\App\Services\Inventory\ItemGroup\ShowService::class);

        return $showService->getActions($data);
    }


    private static function getStockRequestActions(AppEntityData $data): ViewActions
    {
        /** @var $showService \App\Services\Inventory\StockRequest\ShowService */
        $showService = resolve(\App\Services\Inventory\StockRequest\ShowService::class);

        return $showService->getActions($data);
    }

    private static function getManufacturingOrderActions(AppEntityData $data, $viewExtraData = []): ViewActions
    {
        /** @var $showService \App\Services\Manufacturing\ManufacturingOrder\ShowService */
        $showService = resolve(\App\Services\Manufacturing\ManufacturingOrder\ShowService::class);
        return $showService->getActions($data, $viewExtraData);
    }

    private static function getBomActions(AppEntityData $data)
    {
        /** @var $showService \App\Services\Manufacturing\BOM\ShowService */
        $showService = resolve(\App\Services\Manufacturing\BOM\ShowService::class);

        return $showService->getActions($data);

    }

    private static function getProductionPlanActions(AppEntityData $data)
    {
        /** @var $showService \App\Services\Manufacturing\ProductionPlan\ShowService */
        $showService = resolve(\App\Services\Manufacturing\ProductionPlan\ShowService::class);
        return $showService->getActions($data);
    }


    private static function getContractInstallmentActions(AppEntityData $data)
    {
        $showService = resolve(\App\Services\LeaseContract\ContractInstallment\ShowService::class);
        return $showService->getActions($data);
    }

    private static function getLeaseContractActions(AppEntityData $data, $viewExtraData = [])
    {
        $showService = resolve(\App\Services\LeaseContract\LeaseContract\ShowService::class);
        return $showService->getActions($data, $viewExtraData);
    }
    private static function getVehicleActions(AppEntityData $data): ViewActions
    {
        $showService = resolve(\App\Services\Mileage\Vehicle\ShowService::class);

        return $showService->getActions($data);
    }

    private static function getApprovalCycleActions(AppEntityData $data): ViewActions
    {
        /** @var $showService ShowService */
        $showService = resolve(\App\Services\MultiCycleApprovalConfiguration\ShowService::class);
        return $showService->getActions($data);
    }

    private static function getServiceFormActions(AppEntityData $data): ViewActions
    {
        $showService = resolve(\App\Services\Booking\ServiceForm\ShowService::class);
        return $showService->getActions($data);
    }

    private static function getEntityDocumentActions(AppEntityData $data): ViewActions
    {
        $showService = resolve(\App\Services\EntityDocument\ShowService::class);
        return $showService->getActions($data);
    }
}
