<?php
/**
 * Helper class for filtering approval configurations based on approver types,
 * and generate conditions for each type
 * to be used in filtering leave applications that user can approve.
 * configurations based on the user's role (direct manager, department manager, or approver).
 */
namespace App\Modules\LocalEntity\Services\MultiCycleApproval\Listing;

use App\Modules\LocalEntity\Prototype\DataStructure\OrClause;
use App\Modules\LocalEntity\Prototype\Filters\RawFilter;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfiguration;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;

class ApprovalConfigurationFilterHelper
{
    /**
     * Create a new ApprovalConfigurationFilterHelper instance.
     *
     * @param int $staffId The staff ID of the authenticated user
     * @param array $directlyManagedStaffIds Staff IDs directly managed by the user
     * @param array $managedDepartmentStaffIds Staff IDs in departments managed by the user
     */
    public function __construct(protected $staffId, protected array $directlyManagedStaffIds = [], protected array $managedDepartmentStaffIds = [])
    {
    }

    /**
     * @var MultiCycleApprovalConfiguration[] $configurationsWithAssignedManager Configurations with assigned manager
     */
    protected array $configurationsWithAssignedManager = [];

    /**
     * @var MultiCycleApprovalConfiguration[] $configurationsWithDirectManager Configurations with direct manager
     */
    protected array $configurationsWithDirectManager = [];

    /**
     * @var MultiCycleApprovalConfiguration[] $configurationsWithStaffListedAsAnApprover Configurations with staff listed as an approver
     */
    protected array $configurationsWithStaffListedAsAnApprover = [];

    /**
     * @param MultiCycleApprovalConfiguration[] $configurations
     * @return OrClause
     * builds or conditions containing the staff ids and configuration ids
     */
    public function getStaffApprovalConfigurationRelatedItemsConditions(array $configurations, $dbTableName = '')
    {
        $this->process($configurations);
        $configurationsConditions = new OrClause();
        /** @var $this ApprovalConfigurationFilterHelper */
        if($this->managedDepartmentStaffIds){
            $configurationAssignedManagerCondition = $this->getConfigurationsCondition(
                $this->mapConfigurationIds(
                    $this->getConfigurationsWithAssignedManager()
                ),
                $this->managedDepartmentStaffIds,
                dbTableName: $dbTableName
            );
            $configurationsConditions->pushChildren(new RawFilter($configurationAssignedManagerCondition));
        }

        if($this->directlyManagedStaffIds){
            $configurationsWithDirectManagerCondition = $this->getConfigurationsCondition(
                $this->mapConfigurationIds(
                    $this->getConfigurationsWithDirectManager()
                ),
                $this->directlyManagedStaffIds,
                dbTableName: $dbTableName
            );
            $configurationsConditions->pushChildren(new RawFilter($configurationsWithDirectManagerCondition));
        }
        $configurationsWithUserAsStaffIdCondition = $this->getConfigurationsCondition(
            $this->mapConfigurationIds(
                $this->getConfigurationsWithStaffListedAsAnApprover()
            ),
            dbTableName: $dbTableName
        );
        $configurationsConditions->pushChildren(new RawFilter($configurationsWithUserAsStaffIdCondition));
        return $configurationsConditions;
    }

    /**
     * @param MultiCycleApprovalConfiguration[] $configurations
     * @return void
     * separate configurations into 3 approver sets
     * one with assigned manager
     * one with direct manager
     * one with staff listed as an approver
     */
    public function process(array $configurations) {
        $configurationsWithAssignedManger = [];
        $configurationsWithDirectManager = [];
        $configurationsWithStaffListedAsAnApprover = [];
        foreach ($configurations as $configuration) {
            $hasAssignedManager = false;
            $hasDirectManager = false;
            $isStaffListedAsAnApprover = false;

            /* @var $configuration \App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfiguration */
            foreach ($configuration->getApprovalLevels() as $approvalLevel) {
                if($approvalLevel->hasAssignedManager()) {
                    $hasAssignedManager = true;
                }

                if($approvalLevel->hasDirectManager()) {
                    $hasDirectManager = true;
                }

                if(in_array($this->staffId, $approvalLevel->getEmployees())) {
                    $isStaffListedAsAnApprover = true;
                }
            }
            if($isStaffListedAsAnApprover) {
                $configurationsWithStaffListedAsAnApprover[] = $configuration;
                continue;
            }

            if($hasDirectManager) {
                $configurationsWithDirectManager[] = $configuration;
            }

            if($hasAssignedManager) {
                $configurationsWithAssignedManger[] = $configuration;
            }
        }
        $this->configurationsWithAssignedManager = $configurationsWithAssignedManger;
        $this->configurationsWithDirectManager = $configurationsWithDirectManager;
        $this->configurationsWithStaffListedAsAnApprover = $configurationsWithStaffListedAsAnApprover;
    }


    /**
     * @return MultiCycleApprovalConfiguration[]
     */
    public function getConfigurationsWithAssignedManager(): array
    {
        return $this->configurationsWithAssignedManager;
    }

    /**
     * @return MultiCycleApprovalConfiguration[]
     */
    public function getConfigurationsWithDirectManager(): array
    {
        return $this->configurationsWithDirectManager;
    }

    /**
     * @return MultiCycleApprovalConfiguration[]
     */
    public function getConfigurationsWithStaffListedAsAnApprover(): array
    {
        return $this->configurationsWithStaffListedAsAnApprover;
    }

    /**
     * @param MultiCycleApprovalConfiguration[]
     * @return array
     * return configurations ids
     */
    public function mapConfigurationIds(array $configurations) {
        $ids = [];
        foreach ($configurations as $configuration) {
            /** @var $configuration MultiCycleApprovalConfiguration */
            $ids[] = $configuration->getId();
        }
        return $ids;
    }


    /**
     * @param array $configIds
     * @param array $staffIds
     * @return string|void
     * return the conditions to match the configs with staff id
     */
    public function getConfigurationsCondition(array $configIds,array $staffIds = [], $dbTableName = '') {
        if($configIds and $staffIds) {
            return "( approval_cycle_configuration_id IN (".implode(',', $configIds).") and ".($dbTableName? ($dbTableName.".") : '')."staff_id in (".implode(',',$staffIds).") )";
        } else if($configIds) {
            return "( approval_cycle_configuration_id IN (".implode(',', $configIds).") )";
        }
    }


        /**
     * @param MultiCycleApprovalConfiguration[] $configurations
     * @return string|void
     * builds or raw sql conditions containing the staff ids and configuration ids
     */
    public function getStaffApprovalConfigurationRelatedItemsRawSql(array $configurations)
    {
        $this->process($configurations);
        $configurationsConditions = [];
        $configurationsWithAssignedManagerIds = $this->mapConfigurationIds($this->getConfigurationsWithAssignedManager());
        /** @var $this ApprovalConfigurationFilterHelper */
        if($this->managedDepartmentStaffIds && $configurationsWithAssignedManagerIds){
            $configurationAssignedManagerCondition = $this->getConfigurationsCondition(
                $configurationsWithAssignedManagerIds,
                $this->managedDepartmentStaffIds
            );
            $configurationsConditions[] = $configurationAssignedManagerCondition;
        }

        $configurationsWithDirectManagerIds = $this->mapConfigurationIds($this->getConfigurationsWithDirectManager());
        if($this->directlyManagedStaffIds && $configurationsWithDirectManagerIds){
            $configurationsWithDirectManagerCondition = $this->getConfigurationsCondition(
                $configurationsWithDirectManagerIds,
                $this->directlyManagedStaffIds
            );
            $configurationsConditions[] = $configurationsWithDirectManagerCondition;
        }

        $configurationsWithStaffListedAsAnApprover = $this->getConfigurationsWithStaffListedAsAnApprover();
        if($configurationsWithStaffListedAsAnApprover){
            $configurationsWithUserAsStaffIdCondition = $this->getConfigurationsCondition(
                $this->mapConfigurationIds(
                    $configurationsWithStaffListedAsAnApprover
                ),
            );

            $configurationsConditions[] = $configurationsWithUserAsStaffIdCondition;
        }

        $configurationsConditions = array_filter($configurationsConditions, fn($v) => $v !== null);
        if ($configurationsConditions) {
            return implode(' OR ', $configurationsConditions);
        }
        return '';
    }

    public function getDirectlyManagedStaffIds(): array
    {
        return $this->directlyManagedStaffIds;
    }

    public function getManagedDepartmentStaffIds(): array
    {
        return $this->managedDepartmentStaffIds;
    }


}
