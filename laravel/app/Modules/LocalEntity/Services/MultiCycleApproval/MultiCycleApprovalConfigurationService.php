<?php

namespace App\Modules\LocalEntity\Services\MultiCycleApproval;

use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Repositories\StaffRepository;
use App\Services\StaffService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use App\Modules\LocalEntity\Actions\ListAction;
use App\Modules\LocalEntity\Listing\Parser\FormParser;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Modules\LocalEntity\Repositories\MultiCycleApprovalConfigurationRepository;
use Illuminate\Support\Facades\DB;
use App\Facades\Permissions;
use App\Modules\LocalEntity\Prototype\DataStructure\AndClause;
use App\Modules\LocalEntity\Prototype\DataStructure\OrClause;
use App\Modules\LocalEntity\Prototype\Filters\MultipleFilter;
use App\Modules\LocalEntity\Prototype\Filters\RawFilter;
use App\Modules\LocalEntity\Services\MultiCycleApproval\Listing\ApprovalConfigurationFilterHelper;
use App\Repositories\RequestRepository;
use App\Utils\PermissionUtil;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\Common\Utils\LeaveApplicationStatusUtil;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;

/**
 * Class MultiCycleApprovalConfigurationService
 *
 * Handles retrieval of approvers for various levels of multi-cycle approval workflows.
 */
class MultiCycleApprovalConfigurationService
{
    private static $configurations = [];
    public function __construct(private MultiCycleApprovalConfigurationRepository $multiCycleApprovalConfigurationRepository){
    }

    public  function isLastApprover($authId, $request){

        $approvalData = $request->getApprovalData();
        $currentLevel = $approvalData->current_level ?? 1;
        if($request->status != LeaveApplicationStatusUtil::Rejected){
            $currentLevel = $currentLevel - 1;
        }
        $lastLevelApprovers = $this->getLevelApprovers(
            $request->approval_cycle_configuration_id,
            $currentLevel,
            $request->staff_id
        );
        return  in_array($authId, $lastLevelApprovers);
    }

    public function isRequestApprover($authId, $request){
        $requestRepository = resolve(RequestRepository::class);
        $requestStaffData = $requestRepository->getRequestStaffData($request->id);
        if(isset($requestStaffData[0])){
            $configuration = $requestStaffData[0]->approval_cycle_configuration_id ?
            $this->getConfigurations($requestStaffData[0]->approval_cycle_configuration_id) : null;
            $currentLevel = null;
            if($requestStaffData[0]->approval_data){
                $approvalData = json_decode($requestStaffData[0]->approval_data ?? []);
                $currentLevel = $approvalData->current_level;
            }
            return $this->isActiveApproval($requestStaffData[0], $configuration, $currentLevel, $authId);
        }
        return false;
    }

    /**
     * Get the list of staff IDs who are allowed to approve or reject
     * the request at the specified approval cycle level.
     *
     * @param int $configurationId The ID of the approval configuration.
     * @param int $level The current approval cycle level.
     * @param int $requestStaffId The staff ID of the person making the request.
     * @return array An array of staff IDs who can approve/reject.
     */
    public function getLevelApprovers($configurationId, $level, $requestStaffId)
    {
        $staffService = resolve(StaffService::class);
        $requestStaff = $staffService->find($requestStaffId);
        $approversData = $this->getConfigurationLevelApproversData($configurationId, $level);
        if (!$approversData) {
            return [];
        }
        return $this->collectApproverIds($requestStaff, $approversData);
    }

    /**
     * Get the list of staff IDs who can approve/reject across all
     * approval cycle levels for a given configuration.
     *
     * @param int $configurationId The ID of the approval configuration.
     * @param int $requestStaffId The staff ID of the requester.
     * @return array Array of approver staff IDs.
     */
    public function getAllLevelsApproversData($configurationId, $requestStaffId)
    {
        $configuration = $this->getConfigurations($configurationId);
        if (!$configuration) {
            return [];
        }

        $staffService = resolve(StaffService::class);
        $requestStaff = $staffService->find($requestStaffId);

        $combinedApproversData = (object)[
            'has_direct_manager' => false,
            'has_assigned_department_manager' => false,
            'employees' => []
        ];

        foreach ($configuration->getApprovalLevels() as $levelData) {
            if ($levelData->hasDirectManager()) {
                $combinedApproversData->has_direct_manager = true;
            }
            if ($levelData->hasAssignedManager()) {
                $combinedApproversData->has_assigned_department_manager = true;
            }
            $combinedApproversData->employees = array_merge(
                $combinedApproversData->employees,
                $levelData->getEmployees()
            );
        }

        return $this->collectApproverIds($requestStaff, $combinedApproversData);
    }

    /**
     * Fetch the approvers configuration data for a specific level in a configuration.
     *
     * @param int $configurationId The ID of the configuration.
     * @param int $level The level to fetch approvers for.
     * @return array|null Array of approvers data, or null if not found.
     */
    private function getConfigurationLevelApproversData($configurationId, $level)
    {
        $configuration = $this->getConfigurations($configurationId);
        if(!$configuration){
            return [];
        }

        foreach($configuration->getApprovalLevels() as $levelData){
            if($levelData->getLevel() == $level){
                return $levelData;
            }
        }

        return [];
    }

    /**
     * Collect approver IDs based on configuration options.
     *
     * @param object $staff The staff object.
     * @param object|array $approversData Configuration for approvers.
     * @return array Array of unique approver IDs.
     */
    private function collectApproverIds($staff, $approversData): array
    {
        $approverIds = [];

        // Add direct manager if applicable
        if ($approversData->has_direct_manager ?? ($approversData->hasDirectManager()?? false)) {
            $directManagerId = $this->getDirectManagerId($staff);
            if ($directManagerId) {
                $approverIds[] = $directManagerId;
            }
        }

        // Add department managers if applicable
        if ($approversData->has_assigned_department_manager ?? ($approversData->hasAssignedManager() ?? false)) {
            $approverIds = array_merge($approverIds, $this->getDepartmentManagerIds($staff));
        }

        // Add individual employees
        if ($approversData->employees ?? ($approversData->getEmployees() ?? false)) {
            $approverIds = array_merge($approverIds, $approversData->employees ?? $approversData->getEmployees());
        }

        return array_unique($approverIds);
    }

    /**
     * Retrieve the direct manager ID of a given staff member.
     *
     * @param object $staff The staff object.
     * @return int|null The direct manager ID, or null if not found.
     */
    private function getDirectManagerId($staff): ?int
    {
        $directManager = $staff->staff_info?->direct_manager;
        return $directManager ? $directManager->id : null;
    }

    /**
     * Retrieve all department manager IDs for a given staff member.
     *
     * @param object $staff The staff object.
     * @return array An array of department manager IDs.
     */
    private function getDepartmentManagerIds($staff): array
    {
        $managerIds = [];
        $departments = $staff->department;
        foreach ($departments as $department) {
            foreach ($department->managers as $manager) {
                $managerIds[] = $manager->id;
            }
        }
        return $managerIds;
    }

    /**
     * Retrieves approval cycle configurations based on the provided configuration ID.
     *
     * This function can return either a single MultiCycleApprovalConfiguration object
     * or an array of MultiCycleApprovalConfiguration objects. If a configuration ID is
     * provided, the function fetches and returns the specific configuration.
     * If no ID is provided, the function retrieves all available configurations.
     *
     * @param string|null $configurationId The ID of the specific configuration to retrieve. If null, all configurations will be fetched.
     *
     * @return array|MultiCycleApprovalConfiguration|null Returns a single MultiCycleApprovalConfiguration
     * if a configuration ID is provided, an array of MultiCycleApprovalConfiguration objects if no ID is given, or null if no configurations are found.
     *
     */
    public function getConfigurations($configurationId = null, $configurationType = null): array|MultiCycleApprovalConfiguration|null
    {
        if($configurationId){
            $appEntityShowAction = resolve(AppEntityShowAction::class);
            try {
                if (!isset(self::$configurations[$configurationId])) {
                    self::$configurations[$configurationId] = $appEntityShowAction->handle(EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION, $configurationId, 1);
                }

                if(self::$configurations[$configurationId]){
                    return new MultiCycleApprovalConfiguration(self::$configurations[$configurationId]);
                }
            } catch (\Throwable $th) {
                return null;
            }
        }else{
            if(!$configurationType){
                $configurationType = 'all';
            }
            if (!isset(self::$configurations[$configurationType])) {
                $data = $this->multiCycleApprovalConfigurationRepository->getExistingConfigurationsByType(configurationType: $configurationType != 'all'? $configurationType : null);
                foreach($data as $configuration){
                    self::$configurations[$configurationType][] = new MultiCycleApprovalConfiguration(json_decode($configuration));
                }
            }
            return isset(self::$configurations[$configurationType]) ? self::$configurations[$configurationType] : [];
        }
        return [];
    }

    public function getLevelsEmployees($approval_levels){
        if (!empty($approval_levels)) {

            $staffRepo = resolve(StaffRepository::class);
            $allEmployeeIds = collect($approval_levels)->flatten()->unique()->toArray();

            $allEmployees = $staffRepo->findWhereIn('id', $allEmployeeIds);

            $employeesById = collect($allEmployees)->keyBy('id');
            foreach ($approval_levels as $key => $level) {
                $approval_levels[$key]['employees_obj'] = collect($level)
                    ->map(fn($id) => $employeesById[$id] ?? null)
                    ->filter()
                    ->values();
            }
        }
        return $approval_levels;
    }

    public function getMaxLevelsCountByConfigurationType($configurationType){
        return $this->multiCycleApprovalConfigurationRepository->getMaxLevelsCountByConfigurationType($configurationType);
    }

    public function getApprovalLevelStatus($record){
        $approvalData = json_decode($record->approval_data);
        $configuration = $this->getConfigurations($record->approval_cycle_configuration_id);
        if(empty($configuration) || empty($approvalData)) return LeaveApplicationStatusUtil::statusLabelTransList()[$record->status];
        $currentLevel = $approvalData->current_level??1;
        $configurationMaxLevel = $configuration?->getLevelsCount();
        if(isset($approvalData->rejected_by)) return LeaveApplicationStatusUtil::getStatusTrans(LeaveApplicationStatusUtil::Rejected);
        if($currentLevel > $configurationMaxLevel) return LeaveApplicationStatusUtil::getStatusTrans(LeaveApplicationStatusUtil::Approved);
        return $currentLevel === 1? __t('Pending') : sprintf(__t('%s Approval Received (%s/%s)'),$this->getLevelName(--$currentLevel) , $currentLevel, $configurationMaxLevel);
    }

    public function getApprovalLevelsStatusFilterOptions($configurationType){
        $statueses = [];
        $max_levels = $this->getMaxLevelsCountByConfigurationType($configurationType);

        for($i = 1;$i < $max_levels;$i++){
            $levelName = $this->getLevelName($i);
            $statueses["approval_level_$i"] = sprintf(__t('%s Approval Received (%s/%s)'), $levelName, $i, $max_levels) ;
        }

        return $statueses;
    }

    private function getLevelName($index){
        $ordinalNames = [
            1 => __t('First '),
            2 => __t('Second'),
            3 => __t('Third'),
            4 => __t('Fourth'),
            5 => __t('Fifth'),
            6 => __t('Sixth'),
            7 => __t('Seventh'),
            8 => __t('Eighth'),
            9 => __t('Ninth'),
        ];
        return $ordinalNames[$index] ?? "";
    }



    public function handleStatusFilter($filterCriteria, $multiCycleApprovalStatuses){

        $pendingOnMeConditions =null;
        if(in_array( LeaveApplicationStatusUtil::PENDING_ON_ME, $multiCycleApprovalStatuses??[])){
            $pendingOnMeConditions = resolve(PendingOnMeService::class)->getPendingOnMeConditions();
        }
        self::handleFilterRequestByMultiCycleApprovalLevel($filterCriteria, $multiCycleApprovalStatuses, $pendingOnMeConditions);
    }

    public static function handleFilterRequestByMultiCycleApprovalLevel($filterCriteria, $multiCycleApprovalStatuses, $pendingOnMeConditions = null)
    {
        if (empty($multiCycleApprovalStatuses)) {
            return;
        }

        $filterCriteria->removeWhere('status');
        $configurationsAndClause = new AndClause();
        $orClause = new OrClause();
        $allConditions = [];

        if(!is_array($multiCycleApprovalStatuses)) $multiCycleApprovalStatuses = [$multiCycleApprovalStatuses];
        foreach ($multiCycleApprovalStatuses as $multiCycleApprovalStatus) {
            if ( preg_match('/approval_level_(\d+)/', $multiCycleApprovalStatus)) {

                preg_match('/approval_level_(\d+)/', $multiCycleApprovalStatus, $matches);
                $levelId = isset($matches[1]) ? intval($matches[1]) : null;
                if ($levelId) {
                    $allConditions[] = self::getApprovalLevelCondition($levelId);
                }
                continue;
            }

            if (LeaveApplicationStatusUtil::PENDING_ON_ME == $multiCycleApprovalStatus) {
                if ($pendingOnMeConditions) {

                    $orClause->pushChildren(new RawFilter($pendingOnMeConditions));

                }
                continue; // Skip this status, as it doesn't require any filtering.
            }

            if (LeaveApplicationStatusUtil::Pending == $multiCycleApprovalStatus) {
                $allConditions[] = self::getPendingStatusCondition();
                continue;
            }

            // Default: handle other statuses like 'approved', 'rejected', etc.
            $allConditions[] = "status = '" . addslashes($multiCycleApprovalStatus) . "'";
        }

        if (!empty($allConditions)) {
            $combinedCondition = '(' . implode(' OR ', $allConditions) . ')';
            $orClause->pushChildren(new MultipleFilter(null, 'raw', $combinedCondition));
        }
        $configurationsAndClause->pushChildren($orClause);
        $filterCriteria->addWhere($configurationsAndClause);
    }

    private static function getApprovalLevelCondition($levelId)
    {
        return "(JSON_EXTRACT(approval_data, '$.current_level') = " . ($levelId + 1) . " AND status = 'pending')";
    }

    public static function getPendingStatusCondition()
    {
        return "(( approval_cycle_configuration_id IS NULL AND status = 'pending') OR (JSON_EXTRACT(approval_data, '$.current_level') = 1 AND status = 'pending'))";
    }


    public function updateConfigurationStatus($configurationId, $status){
        $configuration = DB::connection('currentSite')
            ->table('settings')
            ->where('id', $configurationId)
            ->first();

        if ($configuration) {
            $configurationData = json_decode($configuration->value, true);

            $configurationData['status'] = $status;
            $newValue = json_encode($configurationData);
            DB::connection('currentSite')
                ->table('settings')
                ->where('id', $configurationId)
                ->update(['value' => $newValue]);
            }

            return $configurationData;
        }

    public function getLevelApproversWithApproveOrRejectLeavePermission($configurationId, $level, $requestStaffId){
        $usersThatcanApproveIds = $this->getLevelApprovers($configurationId, $level, $requestStaffId);
        foreach($usersThatcanApproveIds as $key => $employeeId){
            if(!Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION, $employeeId) ){
                unset($usersThatcanApproveIds[$key]);
            }
        }
        return $usersThatcanApproveIds;
    }

    /**
     * Determines whether the given staff member is actively responsible
     * for approving a leave application at the current level.
     *
     * This function checks the approval configuration to see if the given staff member
     * is one of the approvers (direct manager, department manager, or manually assigned)
     * for the current or any earlier level in the approval process.
     *
     * @param object $applicationStaffData Contains staff-related data including:
     *                                     - direct_manager_id (int|null)
     *                                     - staff_department_managers (comma-separated string of IDs)
     *
     * @param object $configuration An object that provides approval level configuration.
     *                               It should have a method `getApprovalLevels()` returning an array.
     *                               Each level's data object should have:
     *                                 - hasDirectManager(): bool
     *                                 - hasAssignedManager(): bool
     *                                 - getEmployees(): ?array of user IDs
     *
     * @param int $currentLevel The current level of the approval process (1-based index).
     *
     * @param int $staffId The ID of the staff member being checked for active approval responsibility.
     *
     * @return bool Returns true if the given staff member is an active approver at or above
     *              the current level in the approval chain; otherwise, false.
     */
    public function isActiveApproval($applicationStaffData, $configuration, $currentLevel, $staffId)
    {
        if (!$configuration) {
            return false;
        }

        $approverIds = [];
        $approverLevels = [];
        foreach ($configuration->getApprovalLevels() as $level => $levelData) {
            $levelApprovers = [];
            if ($levelData->hasDirectManager()) {
                $directManagerId = $applicationStaffData->direct_manager_id;
                if ($directManagerId) {
                    $levelApprovers[] = $directManagerId;
                }
            }
            if ($levelData->hasAssignedManager()) {
                $assignedManagers = explode(",", $applicationStaffData->staff_department_managers);
                $levelApprovers = array_merge($levelApprovers, $assignedManagers);

            }

            $levelApprovers = array_merge($levelApprovers, $levelData->getEmployees()??[] );
            foreach($levelApprovers as $approver){
                $approverLevels[$approver] = $level + 1;
            }
            $approverIds = array_merge($approverIds, $levelApprovers);
        }
        $approverIds = array_unique($approverIds);

        // Check if staff is in approver list and eligible for current or lower level
        if(in_array($staffId, $approverIds) && ($currentLevel <=  $approverLevels[$staffId])){
            return true;
        }

        return false;
    }

    public function removeEmployeeFromApprovalLevels($employeeId){
        $configurations = $this->getConfigurations();
        foreach($configurations as $configuration){
            $approvalLevels = $configuration->getConfigurationData()->approval_levels;
            foreach ($approvalLevels as $approvalLevel) {
                $approvalLevelApprovers = $approvalLevel->approvers;
                $key = array_search($employeeId, $approvalLevelApprovers->employees);
                if ($key !== false) {
                    unset($approvalLevelApprovers->employees[$key]);
                    $newValue = json_encode($configuration->getConfigurationData());
                    $this->multiCycleApprovalConfigurationRepository->update($configuration->getId(), ['value'=> $newValue]);
                }
            }
        }
    }

    public function hasInactiveEmployeesInApprovalLevels($configurationId){
        $configuration = $this->getConfigurations($configurationId);
        $employees = [];
        foreach ($configuration->getApprovalLevels() as $approvalLevel) {
            $employees = array_merge($employees, $approvalLevel->getEmployees());
        }

        if($employees){
            $staffRepo = resolve(StaffRepository::class);
            $staffs = $staffRepo->find($employees);
            foreach($staffs as $staff){
                if(!$staff->active){
                    return sprintf(__t("%s #%s is not an active employee, please remove or change."), $staff->full_name, '<a target="_blank" href="/v2/owner/staff/'.$staff->id.'#details">'.$staff->code.'</a>');
                }
            }
        }
        return false;
    }

    public function hasEmptyApprovalLevels($configurationId){
        $configuration = $this->getConfigurations($configurationId);
        $staffRepo = resolve(StaffRepository::class);
        foreach ($configuration->getApprovalLevels() as $level => $approvalLevel) {
           $emptyLevel = false;
           if(!$approvalLevel->hasAssignedManager() && !$approvalLevel->hasDirectManager()){
                if(!$approvalLevel->getEmployees()){
                    $emptyLevel = true;
                }elseif(count($approvalLevel->getEmployees()) == 1){
                    $staffs = $staffRepo->find($approvalLevel->getEmployees()[0]);
                    if(!$staffs){
                        $emptyLevel = true;
                    }
                }
           }
           if($emptyLevel){
                return sprintf(__t("Approval Level %s is empty. Please assign at least one approver."), ($level+1));
            }
        }
        return false;
    }

    public function getStaffApprovalConfigurationRelatedItemsRawSql(){
        $staffRepository = resolve(StaffRepository::class);
        $configurations = $this->getConfigurations();
        $configurationsOrClauseRawSql = null;
        if($configurations) {
            $authUser = getAuthOwner();
            $directManagedStaff = $staffRepository->directManagedStaffbyAuthUser($authUser['staff_id']);
            $directManagedStaffIds = $directManagedStaff->pluck('id')->toArray();
            $staffDepartmentManagedByUsers = $staffRepository->getStaffByDepartmentManagerId($authUser['staff_id']);
            $staffDepartmentManagedByUsersIds = $staffDepartmentManagedByUsers->pluck('id')->toArray();

            $approvalConfigurationFilterHelper = new ApprovalConfigurationFilterHelper($authUser['staff_id'], $directManagedStaffIds, $staffDepartmentManagedByUsersIds);
            $configurationsOrClauseRawSql = $approvalConfigurationFilterHelper->getStaffApprovalConfigurationRelatedItemsRawSql($configurations);
        }
        return $configurationsOrClauseRawSql;
    }

    public function getConfigurationLabel($record){

        $configurationLabel = __t('Basic Settings');
        if($record->approval_cycle_configuration_id ){
            $configurationLabel = sprintf(__t('Deleted %s'),__t('Approval Configuration'));
            $configuration = $this->getConfigurations($record->approval_cycle_configuration_id);
            if(isset($configuration)){
                $configurationLabel = sprintf(('%s #%s'), $configuration->getName(), $configuration->getId());
                if($configuration->getStatus() == MultiCycleApprovalUtil::STATUS_INACTIVE){
                    $configurationLabel =  $configurationLabel . " (".__t('Inactive').")";

                }
                $configurationLabel = ' <a href="' . route('owner.entity.show', ['entityKey' => \App\Utils\EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION, 'id' => $record->approval_cycle_configuration_id]) . '" class="text-light-3">
                    ' . $configurationLabel . '
                    <i class="mdi mdi-open-in-new fs-8 lh-base"></i>
                </a>';
            }
        }
        return $configurationLabel;
    }
}
