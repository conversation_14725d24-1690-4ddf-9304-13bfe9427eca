<?php

namespace App\Modules\LocalEntity\Services;

use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Services\Attendance\LeaveApplication\ShowService;
use App\Services\PaginationStackTrait;
use App\Services\WorkflowTypeService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\Daftra\Common\EntityStructure\Entity;
use Izam\Daftra\Common\Utils\StatusUtil;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\Sperator;
use Izam\View\Form\Element\StatusCircle;
use Izam\View\Form\Element\TitleText;

class EntityViewHead
{
    use PaginationStackTrait;

    public static function get(string $entityKey, AppEntityData $data, $viewExtraData = [])
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => self::getLeaveApplicationHead($data),
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => self::getStockRequestHead($data),
            EntityKeyTypesUtil::WORK_FLOW_TYPE => self::getWorkflowTypeHead($data),
            EntityKeyTypesUtil::PURCHASE_ORDER => self::getPurchaseOrderHead($data, $viewExtraData),
            EntityKeyTypesUtil::PURCHASE_REQUEST => self::getPurchaseRequestHead($data, $viewExtraData),
            EntityKeyTypesUtil::PURCHASE_QUOTATION => self::getPurchaseQuotationHead($data, $viewExtraData),
            EntityKeyTypesUtil::QUOTATION_REQUEST => self::getQuotationRequestHead($data, $viewExtraData),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => self::getIndirectCostHead($data),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => self::getManufacturingOrderHead($data, $viewExtraData),
            EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY => self::getProductionRoutingHead($data),
            EntityKeyTypesUtil::EMAIL_TEMPLATE_LOG => self::getEmailTemplateLogHead($data),
            EntityKeyTypesUtil::BOM_ENTITY_KEY => self::getBomHead($data),
            EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => self::getWorkstationHead($data),
            EntityKeyTypesUtil::PRODUCTION_PLAN => self::getProductionPlanHead($data),
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => self::getContractInstallmentHead($data),
            EntityKeyTypesUtil::LEASE_CONTRACT => self::getLeaseContractHead($data, $viewExtraData),
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => self::getApprovalCycleConfigurationHead($data),
            EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => self::getServiceFormHead($data),
            EntityKeyTypesUtil::ENTITY_DOCUMENT => self::getEntityDocumentFormHead($data),
            default => self::getDefaultPageHead($entityKey, $data)
        };
    }

    private static function getNameField($entityKey)
    {
        $appEntityStructureGetter = resolve(AppEntityStructureGetter::class);

        /** @var Entity */
        $entity = $appEntityStructureGetter->buildEntity($entityKey);

        return $entity->getListingField()?$entity->getListingField()->getName() : 'id';
    }

    private static function getDefaultPageHead(string $entityKey, AppEntityData $data): Header
    {
        $pageTitle = new TitleText('page-title');

        $nameField = self::getNameField($entityKey);

        $pageTitle
            ->setTitle($data->{$nameField} ?? '')
            ->setSecondaryInfo('#' .$data->id);

        $header = new Header('page-header');

        $header
            ->addLeft($pageTitle);

        if (isset($data->status)) {
            $statusElement = new StatusCircle('status-icon');
            $statusElement
                ->setOption('color', StatusUtil::getStatusClass($data->status))
                ->setLabel(__t(StatusUtil::getLabel($data->status)))
            ;

            $separator = new Sperator('separator');

            $header
                ->addLeft($separator)
                ->addLeft($statusElement);
        }

        $header->addRight(self::getPagination($entityKey, $data->id));

        return $header;
    }

    public static function getIndirectCostHead(AppEntityData $data): Header {
        /** @var \App\Services\Manufacturing\IndirectCosts\ShowService $showService */
        $showService = resolve(\App\Services\Manufacturing\IndirectCosts\ShowService::class);
        return $showService->getPageHead($data);
    }

    public static function getLeaveApplicationHead(AppEntityData $data): Header
    {
        /** @var ShowService */
        $showService = resolve(ShowService::class);
        return $showService->getPageHead($data);
    }

    private static function getStockRequestHead(AppEntityData $data): Header
    {
        /** @var \App\Services\Inventory\StockRequest\ShowService */
        $showService = resolve(\App\Services\Inventory\StockRequest\ShowService::class);
        return $showService->getPageHead($data);
    }

    private static function getWorkflowTypeHead(AppEntityData $data)
    {
        /** @var WorkflowTypeService */
        $service = resolve(WorkflowTypeService::class);
        return $service->getPageHead($data);
    }

    private static function getPurchaseOrderHead(AppEntityData $data, $viewExtraData= [])
    {
        /** @var \App\Services\Purchases\PurchaseOrder\ShowService */
        $service = resolve(\App\Services\Purchases\PurchaseOrder\ShowService::class);
        return $service->getPageHead($data, $viewExtraData);
    }

    private static function getPurchaseRequestHead(AppEntityData $data, $viewExtraData = [])
    {
        /** @var \App\Services\Purchases\PurchaseRequest\ShowService */
        $service = resolve(\App\Services\Purchases\PurchaseRequest\ShowService::class);
        return $service->getPageHead($data, $viewExtraData);
    }

    private static function getPurchaseQuotationHead(AppEntityData $data, $viewExtraData = [])
    {
        /** @var \App\Services\Purchases\PurchaseQuotation\ShowService */
        $service = resolve(\App\Services\Purchases\PurchaseQuotation\ShowService::class);
        return $service->getPageHead($data, $viewExtraData);
    }

    private static function getQuotationRequestHead(AppEntityData $data, $viewExtraData = [])
    {
        /** @var \App\Services\Purchases\QuotationRequest\ShowService */
        $service = resolve(\App\Services\Purchases\QuotationRequest\ShowService::class);
        return $service->getPageHead($data, $viewExtraData);
    }

    private static function getManufacturingOrderHead(AppEntityData $data, $viewExtraData = [])
    {
        /** @var \App\Services\Manufacturing\ManufacturingOrder\ShowService */
        $service = resolve(\App\Services\Manufacturing\ManufacturingOrder\ShowService::class);
        return $service->getPageHead($data, $viewExtraData);
    }

    private static function getProductionRoutingHead(AppEntityData $data): Header
    {
       /** @var \App\Services\Manufacturing\ProductionRouting\ShowService */
       $service = resolve(\App\Services\Manufacturing\ProductionRouting\ShowService::class);
       return $service->getPageHead($data);
    }

    private static function getBomHead(AppEntityData $data)
    {
        /** @var \App\Services\Manufacturing\BOM\ShowService */
        $service = resolve(\App\Services\Manufacturing\BOM\ShowService::class);
        return $service->getPageHead($data);
    }

    private static function getEmailTemplateLogHead(AppEntityData $data)
    {
        /** @var \App\Modules\Template\Services\Email\EmailTemplateLog\ShowService */
        $service = resolve(\App\Modules\Template\Services\Email\EmailTemplateLog\ShowService::class);
        return $service->getPageHead($data);
    }

    private static function getWorkstationHead(AppEntityData $data)
    {
        /** @var \App\Services\Manufacturing\Workstation\ShowService */
        $service = resolve(\App\Services\Manufacturing\Workstation\ShowService::class);
        return $service->getPageHead($data);
    }

    private static function getProductionPlanHead(AppEntityData $data)
    {
        /** @var \App\Services\Manufacturing\ProductionPlan\ShowService */
        $service = resolve(\App\Services\Manufacturing\ProductionPlan\ShowService::class);
        return $service->getPageHead($data);
    }

    private static function getApprovalCycleConfigurationHead(AppEntityData $data)
    {
        /** @var \App\Services\MultiCycleApprovalConfiguration\ShowService */
        $service = resolve(\App\Services\MultiCycleApprovalConfiguration\ShowService::class);
        return $service->getPageHead($data);
    }
    private static function getContractInstallmentHead(AppEntityData $data)
    {
        $showService = resolve(\App\Services\LeaseContract\ContractInstallment\ShowService::class);
        return $showService->getPageHead($data);
    }

    private static function getLeaseContractHead(AppEntityData $data, $viewExtraData)
    {
        $showService = resolve(\App\Services\LeaseContract\LeaseContract\ShowService::class);
        return $showService->getPageHead($data, $viewExtraData);
    }

    private static function getServiceFormHead(AppEntityData $data, $viewExtraData = [])
    {
        $showService = resolve(\App\Services\ServiceForm\ShowService::class);
        return $showService->getPageHead($data, $viewExtraData);
    }

    private static function getEntityDocumentFormHead(AppEntityData $data, $viewExtraData = [])
    {
        /** @var \App\Services\EntityDocument\ShowService $showService */
        $showService = resolve(\App\Services\EntityDocument\ShowService::class);;
        return $showService->getPageHead($data, $viewExtraData);
    }
}
