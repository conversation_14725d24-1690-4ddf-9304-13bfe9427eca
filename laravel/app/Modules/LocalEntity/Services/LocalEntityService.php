<?php

namespace App\Modules\LocalEntity\Services;

use App\Modules\LocalEntity\AppEntities\EntityLoader;
use App\Exceptions\EntityNotFoundException;
use App\Modules\LocalEntity\Creators\EntityCreatorInterface;
use App\Modules\LocalEntity\Exceptions\ValidationLocalEntitiesValidation;
use App\Modules\LocalEntity\Factories\FieldFactory;
use App\Modules\LocalEntity\Factories\FieldInterface;
use App\Helpers\DynamicPermission\DynamicPermissionHelper;
use App\Modules\LocalEntity\Helpers\EntityAndRelationCache;
use App\Modules\LocalEntity\Helpers\FieldSavedCallback;
use App\Repositories\Criteria\ActivePluginEntities;
use App\Repositories\Criteria\CustomFind;
use App\Modules\LocalEntity\Repositories\LocalEntityFieldRepository;
use App\Modules\LocalEntity\Repositories\LocalEntityRepository;
use App\Helpers\FilterOperations;
use App\Modules\LocalEntity\Repositories\LocalFieldRelationRepository;
use App\Services\BaseService;
use App\Utils\EntityFieldRelationUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityPermissionCriteriaUtil;
use App\Utils\EntityPermissionKeyUtil;
use App\Modules\LocalEntity\Utils\LocalEntityFieldKeyUtil;
use App\Modules\LocalEntity\Utils\LocalEntityFieldTypeUtil;
use App\Repositories\Criteria\EqualCriteria;
use App\Modules\LocalEntity\Models\LocalEntity;
use App\Modules\LocalEntity\Models\LocalEntityField;
use App\Modules\LocalEntity\Models\LocalFieldRelation;
use App\Repositories\Criteria\JsonArrayColumnFilterCriteria;
use App\Repositories\Criteria\WhereNotLikeCriteria;
use App\Repositories\EntityRepository;
use App\Requests\DefaultRequest;
use Closure;
use Izam\Daftra\Common\EntityStructure\Utils\LocalEntityTypeUtil;

/**
 * Class LocalEntityService
 *
 * @property LocalEntityRepository $repo
 *
 * @package App\Services
 */
class LocalEntityService extends BaseService implements EntityCreatorInterface
{
    var $filters = [];

    protected $showRouteName = 'owner.local_entities.builder';

    /**
     * @var LocalEntityFieldRepository
     */
    private $entityFieldRepository;

    /**
     * @var DynamicPermissionHelper
     */
    private $dynamicPermissionHelper;


    /**
     * @var LocalFieldRelationRepository $localFieldRelationRepository
     */
    private $localFieldRelationRepository;
    /**
     * LocalEntityService constructor.
     * @param LocalEntityRepository $repo
     * @param LocalEntityFieldRepository $entityFieldRepository
     * @param EntityRepository $entityRepo
     * @param DynamicPermissionHelper $dynamicPermissionHelper
     * @param LocalFieldRelationRepository $localFieldRelationRepository
     */

    public function __construct(
        LocalEntityRepository $repo,
        LocalEntityFieldRepository $entityFieldRepository,
        EntityRepository $entityRepo,
        DynamicPermissionHelper $dynamicPermissionHelper,
        LocalFieldRelationRepository $localFieldRelationRepository
    )
    {
        $this->localFieldRelationRepository = $localFieldRelationRepository;
        $this->entityFieldRepository = $entityFieldRepository;
        $this->dynamicPermissionHelper = $dynamicPermissionHelper;
        parent::__construct($repo, $entityRepo);
    }

    protected function getListingConditions()
    {
        $conditions =  parent::getListingConditions(); // TODO: Change the autogenerated stub
        if(isset($this->parameters['parent_entity'])) {
            $conditions[] = ['field' => 'type', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => LocalEntityTypeUtil::CHILD_ITEM];
            $conditions[] = ['field' => 'parent_entity', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => $this->parameters['parent_entity']];
        } else {
            $conditions[] = ['field' => 'type', 'operation' => FilterOperations::IN_FILTER_OPERATION, 'value' => [LocalEntityTypeUtil::FORM, LocalEntityTypeUtil::CHILD_ITEM]];
        }
        return $conditions;
    }

    /**
     * @return array
     */
    public function getFormRelatedData()
    {
        return [
            'relatedEntities' => $this->entityRepository->list(['key', 'label'])
                ->concat($this->repo->list(['key', 'label'])),
        ];
    }

    public function getFilters()
    {
        $this->filters = parent::getFilters();
        if (!empty($this->filters)) {
            return $this->filters;
        }
        $customFormList = [];
        if (isset($this->parameters['label']) && !empty($this->parameters['label'])) {
            $customForm = $this->repo->find($this->parameters['label']);
            if ($customForm) {
                $customFormList = [$customForm->key => "#{$customForm->key} {$customForm->label}"];
            }
        }
        $this->filters['label'] = [
            'simple' => true, /* displays the filter outside the toggle */
            'after' => '<i class="input-icon fal fa-search"></i></div>',
            'before' => '<div class="form-group-icon  form-group">',
            'label' => false,
            'attributes' => [
                'placeholder' => sprintf(__t('Search by %s or %s'), __t('Custom Form Name'), __t('Key')),
                'id' => 'customFormSelect',
            ],
            'div' => 'col-md-8',
            'options' => $customFormList,
            'type' => 'select',
            'filter_options' => [
                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                'field' => 'key',
            ],
        ];
        $this->filters['parent_entity'] = [
            'simple' => true, /* displays the filter outside the toggle */
            'label' => false,
            'div' => false,
            'type' => 'hidden',
            'filter_options' => [
                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                'field' => 'parent_entity',
            ],
        ];
        $this->filters['status'] = [
            'simple' => true, /* displays the filter outside the toggle */
            'attributes' => [
                'placeholder' => __t('All Status'),
            ],
            'label' => '',
            'div' => 'col-md-4 form-group',
            'options' => [1 => __t('Active'), 0 => __t('Inactive')],
            'type' => 'select',
            'inputClass' => 'select-filter form-control',
            'filter_options' => [
                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                'field' => 'status',
            ],
        ];
        return $this->filters;
    }

	/**
	 * @param array $inputs
	 * @return array
	 */
	public function prepareInputs(array $inputs):array
	{
		return $this->dynamicPermissionHelper->prepareInputs($inputs, EntityKeyTypesUtil::LOCAL_ENTITY);
	}

    function filterAjax($query)
    {
        return $this->repo->getAutoCompleteResult($query);
    }

    public function getSortFields()
    {
        return [
            'fields' => [
                'created' => ['title' => __t('Date of Creation'), 'field' => 'created', 'direction' => 'ASC'],
            ],
            'active' => ['field' => 'created', 'direction' => 'DESC'],
        ];
    }

    public function saveFields($entityKey, $fields, FieldSavedCallback $fieldSavedCallback)
    {
        $entity = $this->find($entityKey);
        $savedIds = [];
        $oldFieldIds = $this->repo->getEntityFieldsIds($entityKey);
//        $this->validateDeletedFields($entity, $fields);
        $updateRequest = new DefaultRequest();
        $updateRequest['db_name'] = $entityKey;
        $creatingTableForFirstTime = empty($entity->db_name);
        $entity->db_name = $entityKey;
        $entityFieldsSql = [];
        foreach ($fields as &$field) {
            if ($field['type'] == LocalEntityFieldTypeUtil::SEPARATOR) {
                continue;
            }
            $localEntityField = FieldFactory::createFieldFromJson($entity, $field);
            $localEntityField->parseFormBuilder($field);
            try {
               $data = $this->processFields($localEntityField, $field, $creatingTableForFirstTime);
               $fieldSavedCallback->handle($localEntityField, $field);
               $savedIds = array_merge($data['savedIds'], $savedIds);
               if(!empty($data['entityFieldsSql'])) {
                   $entityFieldsSql[] = $data['entityFieldsSql'];
               }
            } catch (ValidationLocalEntitiesValidation $exception) {
                    throw $exception;
            }
            $field['id'] = $localEntityField->getId();
        }
        $entityFieldsSql = array_filter($entityFieldsSql);
        $deletedIds = array_diff($oldFieldIds, $savedIds);
        $this->saveFieldsAndTableData($deletedIds, $entity, $entityFieldsSql, $updateRequest, $fields, $creatingTableForFirstTime);
    }

    private function saveFieldsAndTableData($deletedIds, $entity, $entityFieldsSql, $updateRequest, $fields, $creatingTableForFirstTime)
    {
        $entityKey = $entity->key;
        if (!empty($deletedIds)) {
            if (!$this->isAppEntity($entityKey)) {
                $this->saveTrashedLayout($entity, $this->getDeletedLayoutFields($entity, $deletedIds));
                $this->entityFieldRepository->softDelete($deletedIds);
            }
        }

        if (!$this->isAppEntity($entityKey)) {
            $updateRequest['layout'] = json_encode($fields);
        }
        $this->repo->update($entityKey, $updateRequest->all());
        $entity = $this->repo->find($entityKey);
        $entityFieldsSql = array_filter($entityFieldsSql);
        if (!$creatingTableForFirstTime && !empty($entityFieldsSql)) {
            $this->repo->updateTableForEntity($entityKey, $entityFieldsSql);
        } else if ($creatingTableForFirstTime) {
            $this->appendMetaColumnsToDataTable($entityKey, $entityFieldsSql);
            $this->repo->createTableForEntity($entityKey, $entityFieldsSql);
            $dataToInsert = [
                [
                    'key' => $entityKey.'.id',
                    'entity' => $entityKey,
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                    'plugin_id' => null,
                    'label' => 'Id',
                    'db_name' => 'id',
                    'is_filter' => '0',
                    'on_create' => '1',
                    'on_update' => '1',
                ]
            ];
            if ($entity->type == LocalEntityTypeUtil::CUSTOM_DATA) {
                $parentEntity = EntityLoader::load(request()->get('related_entity'));
                $dataToInsert[] = [
                    'key' => $entityKey.'.reference_id',
                    'entity' => $entityKey,
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'plugin_id' => null,
                    'label' => 'Reference',
                    'db_name' => 'reference_id',
                    'is_filter' => '0',
                    'on_create' => '1',
                    'on_update' => '1',
                ];

                $this->localFieldRelationRepository->add(
                    [
                        'name' => $parentEntity->key.'_custom_data',
                        'type' => EntityFieldRelationUtil::HAS_ONE,
                        'entity' => $parentEntity->key,
                        'field_key' => $parentEntity->db_name.'.'.$parentEntity->primary_key,
                        'reference_field_key' => $entity->key.'.reference_id' ,
                        'reference_entity_key' => $entity->key,
                        'options' => null
                    ]
                );
            } else if($entity->type == LocalEntityTypeUtil::CHILD_ITEM) {
                $parentEntity = EntityLoader::load($entity->parent_entity);
                $dataToInsert[] = [
                    'key' => $entityKey.'.reference_id',
                    'entity' => $entityKey,
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'plugin_id' => null,
                    'label' => 'Reference',
                    'db_name' => 'reference_id',
                    'is_filter' => '1',
                    'on_create' => '1',
                    'on_update' => '1',
                ];

                $this->localFieldRelationRepository->add(
                    [
                        'name' => $parentEntity->key.'_related_'.$entity->key,
                        'type' => EntityFieldRelationUtil::HAS_MANY_TYPE,
                        'entity' => $parentEntity->key,
                        'field_key' => $parentEntity->db_name.'.'.$parentEntity->primary_key,
                        'reference_field_key' => $entity->key.'.reference_id' ,
                        'reference_entity_key' => $entity->key,
                        'options' => null
                    ]
                );

                $this->localFieldRelationRepository->add(
                    [
                        'name' => $parentEntity->key,
                        'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
                        'entity' => $entity->key,
                        'field_key' => $entity->key.'.reference_id',
                        'reference_field_key' => $parentEntity->db_name.'.'.$parentEntity->primary_key,
                        'reference_entity_key' => $parentEntity->key,
                        'options' => null
                    ],
                );
            }
            $this->entityFieldRepository->insertBulk($dataToInsert);
        }
        if(!$entity->name_field) {
            $nameField = $this
                ->entityFieldRepository
                ->getEntityNameField($entity->key);
            if($nameField) {
                $this->repo->update($entityKey, ['name_field' => $nameField->db_name]);
            }
        }
    }

    /**
     * @param string $entityKey
     * @param array $entityFieldsSql
     * @return array
     */
    private function appendMetaColumnsToDataTable(string $entityKey, array &$entityFieldsSql)
    {
        $entity = $this->repo->find($entityKey);
        $entityId = $entity->id;
        array_unshift($entityFieldsSql, '`id` INT(11) PRIMARY KEY NOT NULL AUTO_INCREMENT');
        if($entity->type === LocalEntityTypeUtil::SUBFORM) {
            $entityFieldsSql[] = "`reference_id`   INT NOT NULL DEFAULT 0, INDEX `{$entityId}_reference_id_index` (reference_id)";
            $entityFieldsSql[] = "`display_order`   INT NOT NULL DEFAULT 1, INDEX `{$entityId}_display_order_index` (display_order)";
        } else if($entity->type === LocalEntityTypeUtil::CHILD_ITEM) {
            $entityFieldsSql[] = "`reference_id`   INT NOT NULL DEFAULT 0, INDEX `{$entityId}_reference_id_index` (reference_id)";
        } else {
            $entityFieldsSql[] = "`staff_id`   INT NOT NULL DEFAULT 0, INDEX `{$entityId}_staff_id_index` (staff_id)";
            $entityFieldsSql[] = "`branch_id`  INT NULL DEFAULT NULL, INDEX `{$entityId}_branch_id_index` (branch_id)";
            if ($entity->type == LocalEntityTypeUtil::CUSTOM_DATA) {
                $entityFieldsSql[] = "`reference_id`   VARCHAR(255) NOT NULL, INDEX `{$entityId}_reference_id_index` (reference_id)";
            }
        }
        $entityFieldsSql[] = '`created`    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP';
        $entityFieldsSql[] = '`modified`   DATETIME on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP';
    }

    /**
     * @param bool $isUpdateField
     * @param FieldInterface $localEntityField
     * @param array $field
     */
    private function updateExistedColumns(FieldInterface $localEntityField, array $field) : void
    {
        try {
            if (isset($field['attributes']['property']['is_filtering']) &&  $localEntityField->addIndexFlag) {
                $localEntityField->updateLocalEntityDataCol($field);
            }
        } catch (\Exception $exception) {

        }

        $localEntityField->updateEntityField($field['id'], $this->entityFieldRepository, $this->repo);
    }

    private function processFields(FieldInterface $localEntityField, array &$field, bool $creatingTableForFirstTime) : array
    {
        $localEntityField->backendValidate($field["attributes"]["validation"] ?? []);
        $isUpdateField = !empty($field['id']);
        if ($creatingTableForFirstTime) {
            $localEntityField->saveFieldAndItsRelations($this->entityFieldRepository, $this->repo);
            $entityFieldsSql = $localEntityField->getFullCreateQuery($field);
        } else {
            if ($isUpdateField) {
               $this->updateExistedColumns($localEntityField, $field);
            } else {
                $localEntityField->saveFieldAndItsRelations($this->entityFieldRepository, $this->repo);
                $entityFieldsSql = $localEntityField->getFullUpdateQuery($field);
            }
        }
        $id = $field['id'] = $localEntityField->getId();
        return ['savedIds' => is_array($id) ? $id : [$id], 'entityFieldsSql' => ($entityFieldsSql??null)];
    }

	public function add($request, array $excludedFields = [], Closure $callback = null)
	{
	    if(strpos($request['key'] ,'le_') !== 0) {
            $request['key'] = 'le_' . $request['key'];
        }
		$toStore = $request->all();
	    if(isset($toStore['parent_entity'])) {
            $toStore['type'] = LocalEntityTypeUtil::CHILD_ITEM;
        }
        return parent::add(new DefaultRequest($toStore), ['permissions'], function ($result) use ($toStore) {
            if (isset($toStore['permissions'])) {
                $this->dynamicPermissionHelper->addPermissionToEntity($toStore, $toStore['extend'] ?? EntityKeyTypesUtil::LOCAL_ENTITY, $result->key);
            }
		});
	}

	public function addSubFormEntity($data) {
        return parent::add(new DefaultRequest($data));
    }

	public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $toUpdate = $request->all();
        return parent::update($id, $request, ['permissions'], function ($result) use ($toUpdate) {
            $this->dynamicPermissionHelper->updatePermissionForEntity(EntityKeyTypesUtil::LOCAL_ENTITY, $result->key, $toUpdate);

        });
    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        return $record->getAttributes();
    }

    public function checkUnique($field, $value, $primaryKeyFieldValue = false, $primaryKeyFieldName = 'id')
    {
        return parent::checkUnique('key', 'le_' . $value, $primaryKeyFieldValue, 'key');
    }

    /**
     * @param $entityKey
     * @return mixed
     * @throws EntityNotFoundException
     */
    public function getFormEntityEntity($entityKey)
    {
        $entity = $this->repo->getEntityByKey($entityKey);
        if (!$entity) {
            throw new EntityNotFoundException(__t('Local Entity'));
        }
        return $entity;
    }

    public function validateDeleteLocalEntity(LocalEntity $entity){
        $multipleDropdownFields = $this->entityFieldRepository->pushCriteria(new JsonArrayColumnFilterCriteria('options', 'related_entity', $entity->key))->all(['*'], ['local_field_entity']);
        $localRelations = $this->localFieldRelationRepository->pushCriteria(new EqualCriteria('reference_entity_key', $entity->key))
            ->pushCriteria(new WhereNotLikeCriteria('field_key', '%_' . $entity->key . '_pivot.target_id'))
            ->all(['*'], ['local_relation_field', 'sourceEntity']);
        $errors = [];
        foreach ($multipleDropdownFields as $multipleDropdownField) {
            $errors[] = $this->getErrorMessageUsedEntityInMultipleDynamicField($entity, $multipleDropdownField);
        }
        foreach($localRelations as $localRelation) {
            if(!empty($localRelation->local_relation_field)){
                $errors[] = $this->getErrorMessageUsedEntityInField($entity, $localRelation);
            }
        }
        return $errors;
    }

    public function getErrorMessageUsedEntityInField(LocalEntity $entity, LocalFieldRelation $localFieldRelation) : string {
        if(!empty($localFieldRelation->options)){
            $relationOptions = json_decode($localFieldRelation->options, true);
            if(!empty($relationOptions['relation'])) {
                $sourceEntity = $this->find($relationOptions['relation']['source_entity']);
                $sourceEntityLabel = $sourceEntity?->label ?? $relationOptions['relation']['source_entity'];
                return sprintf(__t('The entity "%s" is used in a field with label "%s" in the entity "%s"'), $entity->label,  $localFieldRelation->local_relation_field?->label ?? '', $sourceEntityLabel);
            }
        }
        return sprintf(__t('The entity "%s" is used in a field with label "%s" in the entity "%s"'), $entity->label, $localFieldRelation->local_relation_field?->label ?? '', $localFieldRelation->sourceEntity?->label ?? '');
    }

    public function getErrorMessageUsedEntityInMultipleDynamicField(LocalEntity $entity, LocalEntityField $field) : string {

        return sprintf(__t('The entity "%s" is used in a field with label "%s" in the entity "%s"'), $entity->label, $field->label, $field->local_field_entity?->label ?? '');
    }

    public function delete($id, Closure $callback = null)
    {
        $entity = $this->find($id);
        $errors = $this->validateDeleteLocalEntity($entity);
        try {
            if(!empty($errors)){
                throw new \Exception(implode("<br>", $errors));
            }
            $callback = function ($oldData) use (&$id, $entity) {
                $this->localFieldRelationRepository->deleteLocalEntityRelation($entity->getKey());
                $oldData->fields()->delete();
                $this->dynamicPermissionHelper->deleteEntitiesPermissions($id, EntityKeyTypesUtil::LOCAL_ENTITY);
            };
            $record = parent::delete($id, $callback);

            EntityAndRelationCache::clearCache();
            // TODO need to refactor some entity have db name called work_orders and can not know how this happened
            if (empty($entity->extend)) {
                $this->repo->dropTable($entity->db_name);
            }
            return $record;
        } catch (\Exception $exception) {
            throw $exception;
        }

    }

    public function getBuilderFields($entityKey) {
        return $this->repo
            ->pushCriteria(new CustomFind([['field' => 'key' , 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => $entityKey]]))
            ->first();
    }

    /**
     * gets all system entities local and general as list [entitykey => entitylabel]
     * @param EntityRepository $entityRepository
     * @return array
     */
    public function getAllSystemEntitiesList()
    {
        $localEntities = $this->repo->pushCriteria(new ActivePluginEntities())->pushCriteria(new CustomFind([[
            'field' => 'type',
            'operation' => FilterOperations::IN_FILTER_OPERATION,
            'value' => [LocalEntityTypeUtil::FORM, LocalEntityTypeUtil::CUSTOM_DATA, LocalEntityTypeUtil::CHILD_ITEM]
        ]]))->list(['key', 'label'], true);
        if (empty($localEntities)) {
            $localEntities = [];
        }
        $globalEntities = $this->entityRepository->pushCriteria(new ActivePluginEntities())->list(['key', 'label'], true);
        $allEntities = array_merge($localEntities, $globalEntities);
        asort($allEntities, SORT_STRING | SORT_FLAG_CASE);
        return $allEntities;
    }

    public function getEntityFromAllSources($entityKey)
    {
        $entity = $this->repo->find($entityKey);
        $localEntity = true;
        if(!$entity)
        {
            $entity = $this->entityRepository->find($entityKey);
            $localEntity = false;
        }
        if(!$entity)
        {
            throw  new EntityNotFoundException('Entity');
        }
        return ['entity' => $entity, 'local' => $localEntity];
    }

    public function getExtendedEntityFromAllSources($entityKey)
    {
        $result = $this->getEntityFromAllSources($entityKey);
        if ($result['entity']->extend) {
            $result = $this->getEntityFromAllSources($result['entity']->key);
        }
        return $result;
    }

    public function getFormData($id = false)
    {
        $result = parent::getFormData($id);
        $nameFieldsOptions = [];
        if($result['form_record']) {
            $nameFields = $this->getEntityNameFields($result['form_record']);
            foreach ($nameFields as $nameField) {
                $nameFieldsOptions[$nameField->key] = $nameField->label;
            }
        }
        $result['nameFieldOptions'] = $nameFieldsOptions;

        $result["related_form_data"] = $this->dynamicPermissionHelper->getDataHelper(EntityKeyTypesUtil::LOCAL_ENTITY, $id);
        return $result;
    }

    private function getEntityNameFields($entity) {
        $nameFields = [];
        foreach($entity->fields as $field) {
            $fieldOptions = json_decode($field->options);
            if (in_array($fieldOptions->type??'', [LocalEntityFieldTypeUtil::EMAIL, LocalEntityFieldTypeUtil::SINGLE_LINE])) {
                $nameFields[] = $field;
            }
        }
        return $nameFields;
    }

    /**
     * @param $entitykey
     * @return bool|\Illuminate\Database\Eloquent\Model
     */
    public function listEntityFields($entityKey)
    {
        $result = $this->getExtendedEntityFromAllSources($entityKey);
        $fields = $this->repo->getEntityListingFields($result['entity'], $result['local']);
        $fields = array_map(function ($item) use ($result) {
            $item->key = str_replace([$result['entity']->db_name], [$result['entity']->key], $item->key);
            return $item;
        }, $fields);
        return $fields;
    }

    public function getFieldValues($fieldKey)
    {
        $field = $this->repo->getListingField($fieldKey);
        if (!$field) {
            throw  new EntityNotFoundException('Field');
        }
        $result = $this->getEntityFromAllSources($field->entity);
        $entity = $result['entity'];
        $tableName = $entity->db_name;
        $connection = $entity->connection;
        $primaryKey = $entity->primary_key;
        $listingField = $field->db_name;
        $fieldValues = $this->repo->getFieldValues($tableName, $connection, $primaryKey, $listingField);
        $formattedFieldValues = [];
        foreach ($fieldValues as $fieldValue) {
            $formattedFieldValues[] = ['text' => $fieldValue->{$listingField}, 'value' => $fieldValue->{$primaryKey}];
        }
        return $formattedFieldValues;
    }

    public function createFieldIfNotExists(string  $key, array  $data)
    {
        if (!$this->entityFieldRepository->fieldsExits($key)) {
            $this->entityFieldRepository->bulkInsert($data);
        }
    }

    /**
     * @param string $entity
     */
    public function createCustomEntityIfNotExists(string  $globalEntity)
    {
        $entity_name = LocalEntityFieldKeyUtil::getCustomDataEntityKey($globalEntity);
        $localEntity = $this->repo->find($entity_name);
        $globalEntityObject = EntityLoader::load($globalEntity);

        if (empty($localEntity)) {
            $data = [
                'key' => $entity_name,
                'label' => ucfirst($globalEntityObject->label . " More Information"),
                'status' => 1,
                'type' => LocalEntityTypeUtil::CUSTOM_DATA,
                'permissions' => [
                    EntityPermissionKeyUtil::VIEW => [
                        'type' => EntityPermissionCriteriaUtil::EVERYONE
                    ],
                    EntityPermissionKeyUtil::UPDATE => [
                        'type' =>  EntityPermissionCriteriaUtil::EVERYONE
                    ],
                    EntityPermissionKeyUtil::DELETE => [
                        'type' =>  EntityPermissionCriteriaUtil::EVERYONE
                    ],
                    EntityPermissionKeyUtil::CREATE => [
                        'type' =>  EntityPermissionCriteriaUtil::EVERYONE
                    ],
                ]
            ];
            $entity = $this->add(new DefaultRequest($data));
            return  $entity;
        }
        return $localEntity;
    }

    public function findOrCreateEntity($data)
    {
        $record =  null;
        if(isset($data['key'])) {
            $record = $this->repo->find($data['key']);
            if(!$record) {
                $record = $this->add(new DefaultRequest($data));
            }
        }
        return $record;
    }
    private function getDeletedLayoutFields($entity, array $deletedIds): array
    {
        $layoutFields = json_decode($entity->layout, true);
        $result = [];
        foreach ($layoutFields as $layout) {
            if (in_array($layout['id'], $deletedIds)) {
                $layout['deleted_at'] = now();
                $result[] = $layout;
            }
        }
        return $result;
    }

    private function saveTrashedLayout($entity, array $deletedLayoutFields): void
    {
        $options = json_decode($entity->options, true) ?? [];
        $options['trashed'] = array_merge(
            $options['trashed'] ?? [],
            $deletedLayoutFields
        );
        $entity->options = json_encode($options);
        $entity->save();
    }


    private function extractFieldValues(array $fields, string $key = 'id'): array
    {
        $result = [];
        foreach ($fields as $field) {
            $result[] = $field[$key];
            if (!empty($field['type']) && $field['type'] === 'SubForm' && !empty($field['fields'])) {
                $result = array_merge($result, $this->extractFieldValues($field['fields']));
            }
        }
        return $result;
    }

    private function validateDeletedFields($entity, $fields): void
    {
        if (empty($entity->layout)) {
            return;
        }
        $oldFieldIdsWithRelated = $this->extractFieldValues(json_decode($entity->layout, true));
        $requestFieldIds = $this->extractFieldValues($fields);
        $deletedFieldIds = array_diff($oldFieldIdsWithRelated, $requestFieldIds);
        if ($deletedFieldIds) {
            $this->entityFieldRepository->validateIfFieldsDeletable($deletedFieldIds);
        }
    }

    private function isAppEntity($entityKey): bool
    {
        return str_starts_with($entityKey, 'app_');
    }
}
