<?php

namespace App\Modules\LocalEntity\Services;

use App\Services\Attendance\LeaveApplication\ShowService;
use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\View\Form\Tab\IframeElement;
use Izam\View\Form\Tab\Services\CreateTabsServices;
use Izam\View\Form\Tab\Tabs;
use Laminas\Form\Element;

class EntityViewTabs
{
    public static function get(string $entityKey, AppEntityData $data, $form, $viewExtraData = []): Tabs
    {
        if (str_contains($entityKey, 'le_workflow-type-entity-')) {
            return self::getWorkflowTabs($data, $form, $entityKey);
        }

        return match ($entityKey) {
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => self::getLeaveApplicationTabs($data, $form),
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => self::getItemGroupTabs($data, $form),
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => self::getStockRequestTabs($data, $form),
            EntityKeyTypesUtil::PURCHASE_ORDER => self::getPurchaseOrderTabs($data, $form),
            EntityKeyTypesUtil::PURCHASE_QUOTATION => self::getPurchaseQuotationTabs($data, $form),
            EntityKeyTypesUtil::PURCHASE_REQUEST => self::getPurchaseRequestTabs($data, $form),
            EntityKeyTypesUtil::QUOTATION_REQUEST => self::getQuotationRequestTabs($data, $form),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => self::getIndirectCostTabs($data, $form),
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => self::getManufacturingOrderTabs($data, $form, $viewExtraData),
            EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY => self::getProductionRoutingTabs($data, $form),
            EntityKeyTypesUtil::EMAIL_TEMPLATE_LOG => self::getEmailTemplateLogTabs($data, $form),
            EntityKeyTypesUtil::BOM_ENTITY_KEY => self::getBomTabs($data, $form),
            EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => self::getWorkstationsTab($data, $form),
            EntityKeyTypesUtil::PRODUCTION_PLAN => self::getProductionPlanTabs($data, $form),
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => self::getContractInstallmentTabs($data, $form),
            EntityKeyTypesUtil::VEHICLE => self::getVehicleTabs($data, $form),
            EntityKeyTypesUtil::LEASE_CONTRACT => self::getLeaseContractTabs($data, $form, $viewExtraData),
            EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY => self::getBookingPackageTabs($data, $form, $viewExtraData),
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => self::getMultiCycleApprovalConfigurationTabs($data,$form,$viewExtraData),
            EntityKeyTypesUtil::ENTITY_DOCUMENT => self::getEntityDocumentTabs($data,$form,$viewExtraData),

            default => self::getDefaultTabs($entityKey, $form, $data)
        };
    }

    private static function getDefaultTabs($entityKey, $form, $data)
    {
        $tabs = [
            [
                'type' => Element::class,
                'name' => 'details',
                'label' => __t('Details'),
                'value' => [
                    'viewPath' => 'entitiy/partials/details',
                    'context' => array_merge(['data' => $data, 'form' => $form])
                ]
            ], [
                'type' => IframeElement::class,
                'name' => 'activity-log',
                'label' => __t('Activity Log'),
                'attributes' => [
                    'src' => '/v2/owner/activity_logs/entity/iframe?entity_key=' . $entityKey.'&entity_id=' . $data['id'] . '&sort=DESC&layout2022=1',
                ]
            ]
        ];

        return CreateTabsServices::createTabs($tabs);
    }

    private static function getIndirectCostTabs(AppEntityData $data, $form): Tabs
    {
        /** @var $showService \App\Services\Manufacturing\IndirectCosts\ShowService */
        $showService = resolve(\App\Services\Manufacturing\IndirectCosts\ShowService::class);
        return $showService->getTabs($data, $form);
    }

    private static function getLeaveApplicationTabs(AppEntityData $data, $form): Tabs
    {
        /** @var $showService ShowService */
        $showService = resolve(ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getItemGroupTabs(AppEntityData $data, $form): Tabs
    {
        /** @var $showService \App\Services\Inventory\ItemGroup\ShowService */
        $showService = resolve(\App\Services\Inventory\ItemGroup\ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getStockRequestTabs(AppEntityData $data, $form): Tabs
    {
        /** @var $showService \App\Services\Inventory\StockRequest\ShowService */
        $showService = resolve(\App\Services\Inventory\StockRequest\ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getPurchaseOrderTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Purchases\PurchaseOrder\ShowService */
        $showService = resolve(\App\Services\Purchases\PurchaseOrder\ShowService::class);

        return $showService->getTabs($data);
    }

    private static function getPurchaseRequestTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Purchases\PurchaseRequest\ShowService */
        $showService = resolve(\App\Services\Purchases\PurchaseRequest\ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getPurchaseQuotationTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Purchases\PurchaseQuotation\ShowService */
        $showService = resolve(\App\Services\Purchases\PurchaseQuotation\ShowService::class);

        return $showService->getTabs($data);
    }

    private static function getQuotationRequestTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Purchases\QuotationRequest\ShowService */
        $showService = resolve(\App\Services\Purchases\QuotationRequest\ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getManufacturingOrderTabs(AppEntityData $data, $form, $viewExtraData = [])
    {
        /** @var $showService \App\Services\Manufacturing\ManufacturingOrder\ShowService */
        $showService = resolve(\App\Services\Manufacturing\ManufacturingOrder\ShowService::class);
        return $showService->getTabs($data, $form, $viewExtraData);
    }

    private static function getProductionRoutingTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Manufacturing\ProductionRouting\ShowService */
        $showService = resolve(\App\Services\Manufacturing\ProductionRouting\ShowService::class);
        return $showService->getTabs($data, $form);
    }

    private static function getBomTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Manufacturing\BOM\ShowService */
        $showService = resolve(\App\Services\Manufacturing\BOM\ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getWorkstationsTab(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Manufacturing\BOM\ShowService */
        $showService = resolve(\App\Services\Manufacturing\Workstation\ShowService::class);
        return $showService->getTabs($data, $form);
    }

    private static function getProductionPlanTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Services\Manufacturing\ProductionPlan\ShowService */
        $showService = resolve(\App\Services\Manufacturing\ProductionPlan\ShowService::class);
        return $showService->getTabs($data, $form);
    }

    private static function getEmailTemplateLogTabs(AppEntityData $data, $form)
    {
        /** @var $showService \App\Modules\Template\Services\Email\EmailTemplateLog\ShowService */
        $showService = resolve(\App\Modules\Template\Services\Email\EmailTemplateLog\ShowService::class);
        return $showService->getTabs($data, $form);
    }

    private static function getWorkflowTabs(AppEntityData $data, $form, $entityKey): Tabs
    {
        /** @var $showService \App\Services\WorkFlowService */
        $showService = resolve(\App\Services\WorkFlowService::class);
        return $showService->getWorkflowTabs($data, $form, $entityKey);
    }


    private static function getContractInstallmentTabs(AppEntityData $data, $form): Tabs
    {
        $showService = resolve(\App\Services\LeaseContract\ContractInstallment\ShowService::class);
        return $showService->getTabs($data, $form);
    }


    private static function getLeaseContractTabs(AppEntityData $data, $form, $viewExtraData): Tabs
    {
        $showService = resolve(\App\Services\LeaseContract\LeaseContract\ShowService::class);

        return $showService->getTabs($data, $form, $viewExtraData);
    }
    private static function getVehicleTabs(AppEntityData $data, $form): Tabs
    {
        /** @var $showService ShowService */
        $showService = resolve(\App\Services\Mileage\Vehicle\ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getBookingPackageTabs(AppEntityData $data, $form): Tabs
    {
        /** @var $showService ShowService */
        $showService = resolve(\App\Services\Booking\BookingPackage\ShowService::class);

        return $showService->getTabs($data, $form);
    }

    private static function getMultiCycleApprovalConfigurationTabs(AppEntityData $data, $form, $viewExtraData = []): Tabs
    {
        /** @var $showService ShowService */
        $showService = resolve(\App\Services\MultiCycleApprovalConfiguration\ShowService::class);

        return $showService->getTabs($data, $form, $viewExtraData);
    }

    private static function getEntityDocumentTabs(AppEntityData $data, $form, $viewExtraData = [] ) : Tabs
    {
        /** @var \App\Services\EntityDocument\ShowService $showService */
        $showService = resolve(\App\Services\EntityDocument\ShowService::class);;
        return $showService->getTabs($data, $form, $viewExtraData);
    }
}
