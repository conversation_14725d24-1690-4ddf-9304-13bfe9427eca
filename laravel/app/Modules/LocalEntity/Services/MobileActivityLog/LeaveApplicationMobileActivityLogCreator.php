<?php

namespace App\Modules\LocalEntity\Services\MobileActivityLog;

use App\Models\LeaveApplication;
use App\Models\Request;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;
use App\Services\StaffService;
use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\LeaveApplicationStatusUtil;
use Izam\Daftra\ActivityLog\Dto\ActivityLogFilterCriteria;
use Izam\Daftra\ActivityLog\Repositores\ActionLineRepository;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;
use Izam\Entity\Formatter\Formatter;

class LeaveApplicationMobileActivityLogCreator implements MobileActivityLogCreator
{

    CONST FINAL_APPROVE_ACTION = 'final_approved';
    CONST APPROVE_ACTION = 'approved';
    CONST REJECT_ACTION = 'rejected';
    CONST UNDO_ACTION = 'cancelled';
    CONST CREATE_ACTION = 'created';

    public $isRequest = false;
    public function __construct(
        private ActionLineRepository $actionlineRepo,
        private MultiCycleApprovalConfigurationService $multiCycleApprovalConfigurationService,
        private StaffService $staffService,
    ) {
    }

    /**
     * Create mobile activity logs from the provided data.
     *
     * @param LeaveApplication $leaveApplication The data to create logs from
     * @return array<MobileLogDto> Array of mobile log DTOs
     */
    public function create($leaveApplication): array
    {
        $this->isRequest = $leaveApplication instanceof Request;
        if(!isset($leaveApplication->approval_cycle_configuration_id)){
            return [];
        }
        $results = [];
        $activityLogFilterCriteria = new ActivityLogFilterCriteria(
            getAuthOwner('staff_id'),
            true,
        );
        $activityLogFilterCriteria->setEntityKey($this->isRequest? EntityKeyTypesUtil::REQUEST_ENTITY_KEY :  EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY);
        $activityLogFilterCriteria->setEntityId($leaveApplication->id);
        $activityLogFilterCriteria->setIsPrimary(1);
        $activityLogFilterCriteria->setPerPage(1000);
        $logs = $this->actionlineRepo->getMobileActivityLog($activityLogFilterCriteria);
        foreach($logs as $log){

            if(!in_array($log->action_type, [ActionLineMainOperationTypesUtil::ADD_ACTION,ActionLineMainOperationTypesUtil::UPDATE_ACTION])){
                continue;
            }
            $approvalData = $this->getApprovalDataFromLog($log);
            $statusData = $this->getStatusDataFromLog($log);
            if($log->action_type == ActionLineMainOperationTypesUtil::UPDATE_ACTION && empty($approvalData) && empty($statusData)) continue;
            $actionLog = $this->getMobileActionLog($leaveApplication, $log, $approvalData, $statusData);
            if($actionLog){
                array_push($results, $actionLog);
            }
        }
        return $results;
    }

    private function getMobileActionLog($leaveApplication, $log, $approvalData = null, $statusData = null) : ?MobileLogDto {
        $data = !empty($approvalData) ? $approvalData : $statusData;
        $actionBy = $log->actor_id != 0 ? $this->staffService->find($log->actor_id)?->name : getCurrentSite('business_name');
        $newVal = !empty($data?->newVal) ? json_decode($data->newVal, true) : [];
        $oldVal = !empty($data?->oldVal) ? json_decode($data->oldVal, true) : [];
        if($newVal == $oldVal){
            return null;
        }
        $result = new MobileLogDto();
        $result->setDate(Formatter::formatForView($log->created, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE));
        $result->setTime(date('H:i:s', strtotime($log->created)));

        if($log->action_type == ActionLineMainOperationTypesUtil::ADD_ACTION){
            $result->setTitle($this->isRequest? __t('Request created'):  __t('Leave application created'));
            $result->setIcon($this->getMobileActivityLogIconUrl(self::CREATE_ACTION));
            return $result;
        }else{
            $result->setNewData([
                'status' => $this->getStatus($leaveApplication, $newVal),
            ]);
            $result->setOldData([
                'status' => $this->getStatus($leaveApplication, $oldVal),
            ]);
            if(isset($newVal['rejected_by'])){
                $result->setTitle(sprintf(__t('Rejected by %s'), $actionBy));
                $result->setIcon($this->getMobileActivityLogIconUrl(self::REJECT_ACTION));
                return $result;
            }
            if(isset($oldVal['rejected_by'])){
                $result->setTitle(sprintf(__t('Rejection undone by %s'), $actionBy));
                $result->setIcon($this->getMobileActivityLogIconUrl(self::UNDO_ACTION));
                return $result;
            }

            if(isset($oldVal['current_level']) && isset($newVal['current_level'])){
                if($oldVal['current_level'] > $newVal['current_level']){
                    $result->setTitle(sprintf(__t('Approval undone by %s'), $actionBy));
                    $result->setIcon($this->getMobileActivityLogIconUrl(self::UNDO_ACTION));
                }else{
                    $status = self::APPROVE_ACTION;
                    if ($result->getNewData()['status'] == LeaveApplicationStatusUtil::getStatusTrans(LeaveApplicationStatusUtil::Approved)){
                        $status = self::FINAL_APPROVE_ACTION;
                    }
                    $result->setTitle(sprintf(__t('Approved by %s'), $actionBy));
                    $result->setIcon($this->getMobileActivityLogIconUrl($status));
                }
            }

            return $result;
        }
    }

    private function getStatus($leaveApplication, $approvalData){
        $currentLeaveApplication = clone $leaveApplication;
        $currentLeaveApplication->setApprovalData($approvalData);
        return $this->multiCycleApprovalConfigurationService->getApprovalLevelStatus($currentLeaveApplication);
    }

    private function getApprovalDataFromLog($log){
        $dataArray = json_decode($log->data);
        foreach($dataArray as $arrVal){
            if(isset($arrVal?->key) && strtolower($arrVal->key) == 'approval data'){
                return $arrVal;
            }
        }
        return null;
    }

    private function getStatusDataFromLog($log){
        $dataArray = json_decode($log->data);
        foreach($dataArray as $arrVal){
            if(isset($arrVal?->key)){
                $key = strtolower($arrVal->key);
                if($key == 'status') return $arrVal;
            }
        }
        return null;
    }


    private function getMobileActivityLogIconUrl($action){
        return match($action){
            self::FINAL_APPROVE_ACTION => getCdnAssetsUrl() . "img/icons/final_approved.svg",
            self::APPROVE_ACTION => getCdnAssetsUrl() . "img/icons/activity_log_approved.svg",
            self::REJECT_ACTION => getCdnAssetsUrl() . "img/icons/activity_log_rejected.svg",
            self::UNDO_ACTION => getCdnAssetsUrl() . "img/icons/activity_log_undo.svg",
            self::CREATE_ACTION => getCdnAssetsUrl() . "img/icons/activity_log_created.svg",
            default => '',
        };
    }

}
