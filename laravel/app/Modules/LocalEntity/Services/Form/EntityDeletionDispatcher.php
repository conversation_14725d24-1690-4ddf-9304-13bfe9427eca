<?php

namespace App\Modules\LocalEntity\Services\Form;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use App\Events\PurchaseOrderDeleted;
use App\Events\PurchaseQuotationDeleted;
use App\Events\ServiceFormDeleted;
use App\Events\Workflow\WorkflowDeletedEvent;

class EntityDeletionDispatcher
{

    public static function dispatch($entityKey, $data): void
    {
        $regex = '/^le_custom_data_service_form_[0-9]+$/';
        switch ($entityKey) {
            case EntityKeyTypesUtil::PURCHASE_QUOTATION:
                PurchaseQuotationDeleted::dispatch($data, $entityKey);
                break;
            case EntityKeyTypesUtil::PURCHASE_ORDER:
                PurchaseOrderDeleted::dispatch($data, $entityKey);
                break;
            case  EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY:
                ServiceFormDeleted::dispatch($data, $entityKey);
                break;
            case (preg_match($regex, $entityKey, $matches) ? true : false):
                dispatch_event_action(new WorkflowDeletedEvent((array)$data));
                break;
        }
    }


    public static function getMessage($entityKey, $msg): string
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::PURCHASE_QUOTATION => __t("You cannot delete the Purchase quotation as there’s related purchase orders"),
            EntityKeyTypesUtil::WORK_FLOW_TYPE => __t('You cannot delete the Workflow Type as there’s related records created from this type'),
            EntityKeyTypesUtil::PURCHASE_REQUEST => __t("You cannot delete the Purchase request as there’s related quotation requests"),
            EntityKeyTypesUtil::QUOTATION_REQUEST => __t("You cannot delete the Quotation request as there’s related purchase quotations"),
            default => $msg,
        };
    }
}
