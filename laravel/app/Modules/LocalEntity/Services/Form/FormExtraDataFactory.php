<?php

namespace App\Modules\LocalEntity\Services\Form;

use Izam\Daftra\Common\EntityStructure\IEntity;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class FormExtraDataFactory
{
    public static function getEntityExtraDataHelper(IEntity $entity) : EntityExtraDataInterface
    {
        switch ($entity->getEntityKey()) {
            case EntityKeyTypesUtil::PURCHASE_REQUEST:
                return new PurchaseRequestExtraData($entity);
            case EntityKeyTypesUtil::QUOTATION_REQUEST:
                return new QuotationRequestExtraData($entity);
            case EntityKeyTypesUtil::PURCHASE_QUOTATION:
                return new PurchaseQuotationExtraData($entity);
            case EntityKeyTypesUtil::PURCHASE_ORDER:
                return new PurchaseOrderExtraData($entity);
            case EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY:
                return new ManufacturingOrderExtraData($entity);
            case EntityKeyTypesUtil::PRODUCTION_PLAN:
                return new ProductionPlanExtraData($entity);
            case EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY:
                return new ItemGroupExtraData($entity);
            case EntityKeyTypesUtil::JOURNAL:
                return new JournalExtraData($entity);
            case EntityKeyTypesUtil::ASSET:
                return new AssetExtraData($entity);
            case EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION:
                return new MultiCycleApprovalConfigurationExtraData($entity);
            case EntityKeyTypesUtil::CONTRACT_INSTALLMENT:
                return new LeaseContractInstallmentExtraData($entity);
            case EntityKeyTypesUtil::LEASE_CONTRACT:
                return new LeaseContractExtraData($entity);
            case EntityKeyTypesUtil::ENTITY_DOCUMENT:
                return new EntityDocumentExtraData($entity);
            default:
                return new DefaultEntityExtraData($entity);
        }
    }
}
