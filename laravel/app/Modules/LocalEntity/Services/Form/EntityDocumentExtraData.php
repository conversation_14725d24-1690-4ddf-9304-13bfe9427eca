<?php

namespace App\Modules\LocalEntity\Services\Form;

use App\Repositories\EntityDocumentRepository;
use App\Repositories\EntityDocumentTypeRepository;
use App\Services\EntityDocumentService;
use App\Services\EntityDocumentTypeService;
use App\Services\StaffService;
use App\Utils\EntityDocumentPermissionsUtil;
use App\Utils\EntityKeyTypesUtil;
use Carbon\Carbon;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;

class EntityDocumentExtraData  extends AbstractExtraData implements EntityExtraDataInterface
{

    /**
     * @param IEntity $entity
     */
    public function __construct(\Izam\Daftra\Common\EntityStructure\IEntity $entity)
    {
    }

    public function getDataForCreate(): array
    {
        $subEntityKey = request()->get('subEntityKey');
        $subEntityId = request()->get('subEntityId');
        $typeOptions = $this->getTypeOptions($subEntityKey);
        $entityDocumentTypeId = request()->query('entity_document_type_id') ?? null ;
        foreach($typeOptions as $type){ if($type['value'] == $entityDocumentTypeId) $typeName = $type['label'];}
        $documentName = old('name') ?? ($typeName ?? null);
        return [
            'entity_types'=> $typeOptions,
            'entity_key' => $subEntityKey,
            'entity_id' => $subEntityId,
            'documentName' => $documentName,
            'entity_document_type_id' => old('entity_document_type_id') ?? $entityDocumentTypeId,
            'method' => 'POST',
            'action' => route('owner.entity.store', ['entityKey' => EntityKeyTypesUtil::ENTITY_DOCUMENT]),
        ];
    }

    public function getDataForUpdate($record): array
    {
        $typeOptions = $this->getTypeOptions($record->entity_key);
        return [
            'entity_types'=> $typeOptions,
            'entity_key' => $record->entity_key,
            'entity_id' => $record->entity_id,
            'documentName' => $record->name,
            'entity_document_type_id' => $record->entity_document_type_id,
            'method' => 'PUT',
            'id' => $record->id,
            'action' => route('owner.entity.update', ['entityKey' => EntityKeyTypesUtil::ENTITY_DOCUMENT , 'id' => $record->id]),
        ];
    }

    private function getTypeOptions($subEntityKey)
    {
        /** @var EntityDocumentTypeService $service */
        $service = resolve(EntityDocumentTypeService::class);
        $types = $service->getEntityDocumentTypes($subEntityKey);

        $typeOptions = [] ;
        foreach ($types as $type){
            $typeOptions[] =[
                "value"=>$type['id'] ,
                "label"=>$type['name'] ,
                'attributes'=> ['data-data'=> json_encode(['isExpirable'=> $type['is_expirable']])]
            ];
        }
        return $typeOptions;
    }


    public function getDataForListing($record): array
    {
        $entityData  = request()->filter;
        if(isset($entityData['entity_key']) && isset($entityData['entity_id']) ) {
            $entity_id = $entityData['entity_id'];
        }

        $isRelatedDocuments = request()->related_document;
        $statuses = EntityDocumentStatusUtil::statusLabelTransList();
        $documentTypesCount = resolve(EntityDocumentRepository::class)->documentTypesCount('staff', $entityData['entity_id']);
        $documentTypes = $this->getTypeOptions("staff");
        $permissions = EntityDocumentPermissionsUtil::getEntityDocumentPermissions('staff');
        $canUload = check_permission($permissions['create']);
        return [
            'entity_id' => $entity_id ?? null,
            'statuses' => $statuses,
            'document_types' => $documentTypes,
            'isRelatedDocuments' => $isRelatedDocuments ,
            'documentTypesCount' =>$documentTypesCount,
            'canUload' => $canUload
        ];
    }

    public function getDataForView($record): array
    {
        $isExpirable = $record->expiry_date  && $record->entity_document_type?->is_expirable;
        $expireyDate = Carbon::parse($record->expiry_date);
        $expireFrom = $isExpirable ? $expireyDate->diffInDays() : null;
        $expireFromLabel = $isExpirable ? $expireFrom .' '.__t('days') : null;
        $expireIn = $isExpirable ? $expireyDate->diffInDays(today()->subDay()) : null;
        $expireInLabel = $isExpirable ? ($expireIn .' '.__t('days')) : null;

        $isExpired = resolve(EntityDocumentService::class)->calculateStatus($record) == EntityDocumentStatusUtil::EXPIRED;
        if($record->entity_key == "staff"){
            $staff = resolve(StaffService::class)->find($record->entity_id);
        }
        $canManage = check_permission(\App\Utils\PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS);
        return [

            'isExpirable' => $isExpirable,
            'expireIn' => $expireInLabel,
            'expireFrom' => $expireFromLabel,
            "isExpired" => $isExpired && $isExpirable,
            'staff' => $staff ?? null,
            'canManage' => $canManage

        ];
    }

}
