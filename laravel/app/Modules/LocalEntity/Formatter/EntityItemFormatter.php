<?php

namespace App\Modules\LocalEntity\Formatter;

use App\Modules\LocalEntity\Formatter\Strategy\AdvancedDynamicViewFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\AssignedStaffViewFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\AutoStaffViewFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\CurrencyFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\DateFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\DefaultFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\DropDownViewFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\DynamicCurrencyFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\DynamicPermissionFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\DynamicViewFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\FollowUpStatusViewFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\HasOneFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\MapFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\MultipleDropdownFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\MultipleDynamicViewFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\MultipleFileFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\StaticFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\SubFormFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\FileFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\QuantityFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\TimeFormatter;
use App\Modules\LocalEntity\Formatter\Strategy\UnitTextFormatter;
use App\Utils\EntityFieldUtil;
use Izam\Forms\Element\AutoStaff;

class EntityItemFormatter implements IEntityItemFormatter
{
    public function format($item, $metaItems)
    {
        $obj = new \stdClass();
        foreach ($metaItems as $meta) {
            if($meta->getHasOneName()) {
                if(!isset($obj->{$meta->getHasOneName()})) {
                    $obj->{$meta->getHasOneName()} = new \stdClass();
                }
                $this->getFactory($meta->getType())->format($item->{$meta->getHasOneName()}, $meta, $obj->{$meta->getHasOneName()});
            } else {
                $this->getFactory($meta->getType())->format($item, $meta, $obj);
            }
        }
        return $obj;
    }


    protected function getFactory($type)
    {
        $data = [
            EntityFieldUtil::ENTITY_FIELD_TYPE_CURRENCY_DROPDOWN => (new CurrencyFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN => (new MultipleDynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_ASSIGNED_STAFF => (new AssignedStaffViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_FOLLOW_UP_STATUS => (new FollowUpStatusViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS => (new MultipleDynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN =>  (new DynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY =>  (new DynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST =>  (new DynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN_ADVANCED =>  (new AdvancedDynamicViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN => new DropDownViewFormatter($this),
            EntityFieldUtil::ENTITY_FIELD_TYPE_STATIC_CONTENT =>  (new StaticFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DATE =>  (new DateFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_TIMESTAMP =>  (new DateFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_TIME =>  (new TimeFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MAP =>(new MapFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DROPDOWN => (new MultipleDropdownFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_FILE =>  (new FileFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE =>  (new MultipleFileFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_PHOTO =>  (new FileFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM => (new SubFormFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_QUANTITY => (new QuantityFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_UNIT_TEXT => (new UnitTextFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF => (new AutoStaffViewFormatter($this)),
            EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_PERMISSION => (new DynamicPermissionFormatter($this)),
            'hasOne' => (new HasOneFormatter($this)),

        ];
        if (!isset($data[$type])) {
            return new DefaultFormatter($this);
        } else {
            return $data[$type];
        }
    }

    public function accessData($record, $key)
    {
        $keys = explode('.', $key);
        foreach ($keys as $index => $key) {
            $record =  $record->{$key}??'';
        }
        return $record;
    }

    public function accessDataArray($record, $key, $value)
    {
        $tempKey = $keys = explode('.', $key);
        unset($tempKey[0]);
        $tempKey = implode('.', $tempKey);

        $tempValue = $values = explode('.', $value);
        unset($tempValue[0]);
        $tempValue = implode('.', $tempValue);

        $retKeys = [];

        foreach ($record->{$keys[0]} as $datum) {
            $retKeys[] = $this->accessData($datum, $tempKey);
        }
        $retValues = [];
        foreach ($record->{$values[0]} as $datum) {
            $retValues[] = $this->accessData($datum, $tempValue);
        }
        return array_combine($retKeys, $retValues);
    }

}
