<?php

namespace App\Modules\LocalEntity\EntityCrudRules;

use App\Exceptions\ServiceFormHasLeCustomDataForDelete;
use App\Utils\Invoices\InvoiceTypeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Izam\Booking\Utils\ServiceFormUtil;

class ServiceFormCrudRule implements ICrudRule
{

    protected $params;

    public function __construct($params = [])
    {
        $this->params = $params;
    }

    public function validate($data = null)
    {
        if (isset($this->params['action']) && $this->params['action'] == 'delete') {
            if ($relatedData = $this->delete($data)) {
                throw new ServiceFormHasLeCustomDataForDelete(
                    '#' . $relatedData->invoice_no,
                    '/v2/owner/new-booking?booking_id=' . $relatedData->invoice_id,
                );
            }
        }
    }

    private function delete($data): bool|object
    {
        $tableName = ServiceFormUtil::getAdditionalPrefix() . $data->id;
        $tableExists= Schema::connection('currentSite')->hasTable($tableName);
        $invoiceType = InvoiceTypeUtil::BOOKING;
        if (!$tableExists) return false;
        $hasData = DB::connection('currentSite')->select("
                                        SELECT `invoice_items`.invoice_id, `invoices`.no AS invoice_no
                                        FROM `$tableName` 
                                        JOIN `invoice_items` ON `invoice_items`.`id` = `$tableName`.`reference_id` AND `invoice_items`.`product_id` = {$data->id}
                                        JOIN `invoices` ON `invoices`.`id` = `invoice_items`.`invoice_id` AND  `invoices`.`type` = {$invoiceType}
                                        LIMIT 1;");
        if (!$hasData) return false;
        return $hasData[0];
    }

}
