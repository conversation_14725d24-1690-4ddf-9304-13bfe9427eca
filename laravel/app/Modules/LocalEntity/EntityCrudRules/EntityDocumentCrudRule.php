<?php

namespace App\Modules\LocalEntity\EntityCrudRules;

class EntityDocumentCrudRule implements ICrudRule
{

    protected $params;

    public function __construct($params = [])
    {
        $this->params = $params;
    }

    public function validate($data = null)
    {
        $method = request()->method();
        $data = request()->all();
        if (!in_array($method , ['POST' , 'PUT'])) return;
        $files = explode(',', $data['documents']);
        if (count($files) > 50){
            throw new \Exception(__t('Maximum number of files allowed is 50'));
        }
    }

}
