<?php

namespace App\Modules\LocalEntity\EntityCrudRules;

use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityPermissionKeyUtil;
use App\Utils\PurchaseOrderStatusUtil;
use App\Utils\PurchaseQuotationStatusUtil;
use App\Utils\PurchaseRequestStatusUtil;
use App\Utils\QuotationRequestStatusUtil;

return [
    'dataSource' => [
        EntityKeyTypesUtil::PURCHASE_REQUEST => [
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'type'   => CrudRuleTypeUtil::VALIDATION,
                    'field'  => [
                        'input' => CrudRuleFieldInputUtil::OLD_DATA,
                        'name' => 'status',
                        'value' => [PurchaseRequestStatusUtil::Approved, PurchaseRequestStatusUtil::Rejected],
                        'operator' => CrudRuleCondtionOperatorUtil::IN
                    ],
                    'message'=> __t('You cannot edit the approved or rejected purchase request')
                ],
                [
                    'handler' => PurchaseRequestCrudRule::class
                ]
            ],
            EntityPermissionKeyUtil::DELETE => [
                [
                    'type'   => CrudRuleTypeUtil::VALIDATION,
                    'field'  => [
                        'input' => CrudRuleFieldInputUtil::OLD_DATA,
                        'name' => 'status',
                        'value' => [PurchaseRequestStatusUtil::Approved, PurchaseRequestStatusUtil::Rejected],
                        'operator' => CrudRuleCondtionOperatorUtil::IN
                    ],
                    'message'=> __t('You cannot delete the approved or rejected purchase request')
                ],
                [
                    'handler' => PurchaseRequestCrudRule::class,
                    'params'  => ['action' => 'delete']
                ],
            ]
         ],
         EntityKeyTypesUtil::QUOTATION_REQUEST => [
            EntityPermissionKeyUtil::DELETE => [
                [
                    'type'   => CrudRuleTypeUtil::VALIDATION,
                    'field'  => [
                        'input' => CrudRuleFieldInputUtil::OLD_DATA,
                        'name' => 'status',
                        'value' => [QuotationRequestStatusUtil::Quoted, QuotationRequestStatusUtil::PartiallyQuoted],
                        'operator' => CrudRuleCondtionOperatorUtil::IN
                    ],
                    'message'=> __t('You cannot delete the quotation request with status quoted or partially quoted')
                ]
            ],
             EntityPermissionKeyUtil::UPDATE => [
                 [
                     'handler' => QuotationRequestEditCrudRule::class,
                     'params'  => ['action' => 'update']
                 ],
             ]
        ],
          EntityKeyTypesUtil::PURCHASE_QUOTATION => [
            EntityPermissionKeyUtil::DELETE => [
                [
                    'type'   => CrudRuleTypeUtil::VALIDATION,
                    'field'  => [
                        'input' => CrudRuleFieldInputUtil::OLD_DATA,
                        'name' => 'payment_status',
                        'value' => [PurchaseQuotationStatusUtil::Approved, PurchaseQuotationStatusUtil::Rejected],
                        'operator' => CrudRuleCondtionOperatorUtil::IN
                    ],
                    'message'=> __t('You cannot delete the approved or rejected purchase Quotation')
                ],
                [
                    'type'   => CrudRuleTypeUtil::VALIDATION,
                    'field'  => [
                        'input' => CrudRuleFieldInputUtil::OLD_DATA,
                        'name' => 'payment_status',
                        'value' => PurchaseQuotationStatusUtil::Ordered,
                        'operator' => CrudRuleCondtionOperatorUtil::EQUALS
                    ],
                    'message'=> __t('You cannot delete the Purchase quotation with status ordered')
                ],
                [
                    'handler' => PurchaseQuotationDeleteCrudRule::class,
                    'params'  => ['action' => 'delete']
                ]
            ],
        ],
        EntityKeyTypesUtil::PURCHASE_ORDER => [
            EntityPermissionKeyUtil::DELETE => [
                [
                    'type'   => CrudRuleTypeUtil::VALIDATION,
                    'field'  => [
                        'input' => CrudRuleFieldInputUtil::OLD_DATA,
                        'name' => 'payment_status',
                        'value' => [PurchaseOrderStatusUtil::Cancelled, PurchaseOrderStatusUtil::Invoiced],
                        'operator' => CrudRuleCondtionOperatorUtil::IN
                    ],
                    'message'=> __t('You cannot delete the Purchase Order with status Cancelled and Invoiced')
                ],
                [
                    'handler' => PurchaseOrderDeleteCrudRule::class,
                    'params'  => ['action' => 'delete']
                ]
            ],
        ],
        EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => [
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'handler' => ItemGroupUpdateProductsCrudRule::class,
                    'params'  => ['action' => 'update']
                ]
            ],
            EntityPermissionKeyUtil::CREATE => [
                [
                    'handler' => ItemGroupUpdateProductsCrudRule::class,
                    'params'  => ['action' => 'create']
                ]
            ]
        ],
        EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                [
                    'handler' => StockRequestCreateCrudRule::class,
                    'params'  => ['action' => 'create']
                ]
            ],
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'input' => CrudRuleFieldInputUtil::NEW_DATA,
                    'handler' => StockRequestUpdateCrudRule::class,
                    'params'  => ['action' => 'update']
                ]
            ],
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => [
            EntityPermissionKeyUtil::CREATE => [
                [
                    'handler' => ManufacturingOrderIndirectCostCreateCrudRule::class,
                    'params'  => ['action' => 'create']
                ]
            ],
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'handler' => ManufacturingOrderIndirectCostCreateCrudRule::class,
                    'params'  => ['action' => 'update']
                ]
            ],
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => [
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'handler' => ManufacturingOrderUpdateCrudRule::class,
                    'params'  => ['action' => 'update']
                ]
            ],
        ],
        EntityKeyTypesUtil::PRODUCTION_PLAN => [
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'handler' => ProductionPlanCrudRule::class,
                    'params'  => ['action' => 'update']
                ]
            ],
            EntityPermissionKeyUtil::CREATE => [
                [
                    'handler' => ProductionPlanCrudRule::class,
                    'params'  => ['action' => 'create']
                ]
            ]
        ],
        EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => [

            EntityPermissionKeyUtil::CREATE => [
                    [
                        'handler' => ApprovalCycleCrudRule::class,
                        'params'  => ['action' => 'create']
                    ]
                ],
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'handler' => ApprovalCycleCrudRule::class,
                        'params'  => ['action' => 'update']
                    ]
                ]
        ],
        EntityKeyTypesUtil::CONTRACT_INSTALLMENT => [
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'handler' => ContractInstallmentCrudRule::class,
                    'params'  => ['action' => 'update']
                ]
            ],
            EntityPermissionKeyUtil::CREATE => [
                [
                    'handler' => ContractInstallmentCrudRule::class,
                    'params'  => ['action' => 'create']
                ]
            ]
        ],
        EntityKeyTypesUtil::LEASE_CONTRACT => [
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'handler' => LeaseContractCrudRule::class,
                    'params'  => ['action' => 'update']
                ]
            ],
            EntityPermissionKeyUtil::CREATE => [
                [
                    'handler' => LeaseContractCrudRule::class,
                    'params'  => ['action' => 'create']
                ]
            ]
        ],
        EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => [
            EntityPermissionKeyUtil::DELETE => [
                [
                    'type'   => CrudRuleTypeUtil::VALIDATION,
                    'message'=> __t('You cannot delete Service Form'),
                    'field'  => [
                        'input' => CrudRuleFieldInputUtil::OLD_DATA,
                        'name' => 'id',
                        'value' => [],
                        'operator' => CrudRuleCondtionOperatorUtil::EQUALS
                    ],
                ],
                [
                    'handler' => ServiceFormCrudRule::class,
                    'params'  => ['action' => 'delete']
                ],
            ]
        ],
        EntityKeyTypesUtil::ENTITY_DOCUMENT => [
            EntityPermissionKeyUtil::CREATE => [
                [
                    'handler' => EntityDocumentCrudRule::class,
                    'params'  => ['action' => 'create']
                ],
            ],
            EntityPermissionKeyUtil::UPDATE => [
                [
                    'handler' => EntityDocumentCrudRule::class,
                    'params'  => ['action' => 'update']
                ],
            ],
        ],
    ]
];
