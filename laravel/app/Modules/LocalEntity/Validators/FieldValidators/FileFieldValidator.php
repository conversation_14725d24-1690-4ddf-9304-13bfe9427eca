<?php


namespace App\Modules\LocalEntity\Validators\FieldValidators;



use App\Rules\SafeFile;
use App\Utils\ValidationUtil;
use App\Rules\SiteFilesLimitRule;
use Illuminate\Support\Facades\Validator;

class FileFieldValidator implements FieldValidatorInterface
{
    private $errors = [];
    private $validator;
    public function getRulesAndMessages($field): array
    {
        $fieldRules = explode('|', $field->validation_rules);
        $rules = [];
        $messages = [];
        foreach ($fieldRules as $validationRule) {
            $ruleParts = explode(':', $validationRule);
            $ruleType = $ruleParts[0];
            if (isset($ruleParts[1])) {
                $ruleParts = explode(',', $ruleParts[1]);
            }
            switch ($ruleType) {
                case 'mimes':
                    $messages[$ruleType] = sprintf(__t('invalid file type supported extensions are (%s)'), implode(',', $ruleParts));
                    break;
                case 'max':
                    $messages[$ruleType] = sprintf(__t('maximum file size exceeded, max file size (%s MB) '), number_format($ruleParts[0] / 1024, 2));
                    break;
            }
            if ($validationRule == 'safe_file'){
                $rules[] = new SafeFile();
            }else{
                $rules[] = $validationRule;
            }
        }
        $rules[] = new SiteFilesLimitRule();
        return ['messages' => $messages, 'rules' => $rules];
    }

    public function validate($data, $field)
    {
        $validationData = $this->getRulesAndMessages($field);
        $validator = Validator::make([$field->db_name => $data], [$field->db_name => $validationData['rules']], array_merge(ValidationUtil::getMessages(), $validationData['messages']));
        $this->validator = $validator;
        $this->validator->validate();
    }

    public function getErrors(): array
    {
        return $this->validator->getMessageBag()->toArray();
    }
}
