@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{{ CDN_URL }}IzamV1/FormsV1/FormsV1.css?v={{CSS_VERSION}}"/>
    @if(file_exists(public_path('/css/').$entityKey.'.css'))
        <link rel="stylesheet" href="/v2/css/{{$entityKey}}.css"/>
    @endif
@endpush

@php
        $successRedirect = request()->get('success_redirect');
        $form->prepare();
@endphp
@section('content')
    <div class="card mb-3">

        <div class="card-header">
            @if(isset($_GET['booking']))
                <h6>{{$form->getLabel()}}</h6>
            @else
                <h6>{{__t('General Information')}}</h6>
            @endif
        </div>

        <div class="card-body">

            {!! \Izam\Forms\IzamFormView::filterForm()->openTag($form) !!}
            @csrf
            @if(!isset($_GET['booking']))
                @include('partials.form.page-head', [
                    'actions' => [
                        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
                        ['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'departmentAddButton', 'attr' => ['key' => 'value']]
                    ]
                ])
            @endif
            <div class="form-row">

                @foreach($form as $field)
                    {!! (new \Izam\Forms\View\Helper\Form\FormRow())($field) !!}

                @endforeach
                @if(!empty($successRedirect))
                    <input type="hidden" name="success_redirect" value="{{$successRedirect}}">
                @endif

            {!! \Izam\Forms\IzamFormView::filterForm()->closeTag() !!}
        </div>
    </div>

@endsection
@push('scripts')
    @include('LocalEntity::create.init_js')
    @if(file_exists(public_path('/js/').$entityKey.'.js'))
        <script src="/v2/js/{{$entityKey}}.js"></script>
    @endif
@endpush
