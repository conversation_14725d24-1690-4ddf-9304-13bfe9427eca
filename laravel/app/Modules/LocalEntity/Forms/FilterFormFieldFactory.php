<?php
/**
 * TODO add translator and interfaces
 */

namespace App\Modules\LocalEntity\Forms;



use App\Modules\LocalEntity\Helpers\DefaultValuesOptionsAssigner;
use App\Modules\LocalEntity\Helpers\ValidationFormSetter;
use App\Modules\LocalEntity\Prototype\Field;
use App\Modules\LocalEntity\Prototype\Relation\Relation;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Modules\LocalEntity\Repositories\LocalEntityDataRepository;
use App\Repositories\CurrencyRepository;
use App\Repositories\FollowUpStatusRepository;
use App\Services\CurrencyService;
use App\Utils\EntityFieldUtil;
use Izam\Daftra\Common\EntityStructure\Fields\AutoSuggestAwareInterface;
use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;
use Izam\Forms\Element\AdvancedDynamicDropdown;
use Izam\Forms\Element\AjaxSelect;
use Izam\Forms\Element\AssignedStaff;
use Izam\Forms\Element\AssignedStaffDropdown;
use Izam\Forms\Element\Currency;
use Izam\Forms\Element\Date;
use Izam\Forms\Element\DynamicDropdown;
use Izam\Forms\Element\MultipleDropdown;
use Izam\Forms\Element\Number;
use Izam\Forms\Element\Select;
use Izam\Forms\Element\Tags;
use Izam\Forms\Element\Text;
use Izam\Forms\ElementInterface;
use Izam\Forms\FieldSet;
use App\Modules\LocalEntity\Forms\FollowUpStatus\FollowUpStatusData;

class FilterFormFieldFactory
{

    /**
     * @var CurrencyService
     */
    private $currencyService;

    private $currencyRepository;

    public function __construct(CurrencyService $currencyService, AppEntityMetaRepository $metaRepository, CurrencyRepository $currencyRepository)
    {
        $this->currencyService = $currencyService;
        $this->metaRepo = $metaRepository;
        $this->currencyRepository = $currencyRepository;
    }

    /**
     * @param Relation $relation
     * @return array
     */
    public function getRelationOptions($field, $entity)
    {
        $searches = $field->getSearchFields();
        $conditions = $field->getConditionsFields();
        $listFields  = $field->getListingField();


        if (empty($listFields)) {
            $listFields[] = $entity->getPrimaryKey();
            $listFields[] = $this->getListingColumn($field) ?? $entity->getListingField()->getName();
        }


        $filterString = $this->buildFilters($searches, $conditions);

        if (empty($conditions) && empty($searches)) {
            $filterString["filter[{$listFields[1]}][like]"] = '%term%';
            $filterString['per_page'] = 15;
        }

        $params =    [
            'url' => route('entity.list-photo', ['entityKey' => $entity->getEntityKey(), 'dataLevel' => 2, 'mode' => 'listing', 'fields' => $listFields]),
            'queryParams' => $filterString,
            'processResult' => ['valueKey' => $listFields[0], 'textKey' => $listFields[1], 'dataKey' => null],
        ];

        if (method_exists($field, 'getDefaultImg')) {
            $params['default_img'] = $field->getDefaultImg();
        }
        if (!empty($field->getOptions()['form']['show_all'])) {
            $tempOptions = [['text' => 'All', 'id' => -1]];
            $params['show_all'] = $tempOptions;
        }
        return $params;
    }

    /**
     * @param Relation $relation
     * @return array
     */
    public function getRelationAjaxOptions($relation) {

        $entity = $relation->getTargetEntity();
        if(!$entity) {
            throw new \Exception('Entity Not Found');
        }
        $listFields = [$entity->getPrimaryKey(), $entity->getListingField()->getName()];
        $listMeta = $relation->getOptions();
        if($listMeta) {
            $listFields = [$entity->getPrimaryKey(), str_replace($entity->getTable().'.','',$listMeta['list_field'])];
        }
        return [
            'url' => route('entity.list', ['entityKey' => $entity->getEntityKey(), 'dataLevel' => 1, 'mode' => 'listing', 'fields' => $listFields]),
            'queryParams' => [
                "filter[{$listFields[1]}][like]" => '%term%',
            ],
            'processResult' => ['valueKey' => $listFields[0], 'textKey' => $listFields[1], 'dataKey' => null]
        ];
    }

    private function buildFilters($searchs, $conditions = [])
    {
        $filters = [];
        if (!empty($conditions)) {
            foreach ($conditions as $condition) {
                $filters['filter['.$condition['col'].']'.'['.($condition['operator'] ?? 'equal').']'] = $condition['value'];

            }
        }
        foreach ($searchs as $search) {
            $filters['filter[or]['.$search['col'].']['.$search['operator'].']'] = '%term%';
        }
        $filters['per_page'] = 15;
        return $filters;
    }

    /**
     * @param Field $field
     * @return mixed
     * @throws \Exception
     */
    public function getDynamicDropDownFieldOptions($field) {
        if(!$field->getRelation()) {
            throw new \Exception('Relation Not Found');
        }
        return $this->getRelationOptions($field->getRelation());
    }

    public function getCheckBoxOptions() {
        return [
            '' => __t('All'),
            '0' => __t('No'),
            '1' => __t('Yes')
        ];
    }
    /**
     * @param Field $filterField
     */
    public function createFilterFields($filterField) {
        switch ($filterField->getType()) {
            case EntityFieldUtil::ENTITY_FIELD_TYPE_CHECKBOX:
                $field = new Select($filterField->getName(), ['label' =>__t($filterField->getLabel())]);
                $field->setValueOptions($this->getCheckBoxOptions());
                return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_NUMBER:
                $field = new Text($filterField->getName(), ['label' => __t($filterField->getLabel()),]);
                return $field;

            case EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER:
                $numberSet = new FieldSet($filterField->getName());
                $fromField = new Number('from', ['label' => sprintf(__t('%s From'),__t($filterField->getLabel()))]);
                $fromField->setAttribute('class', 'form-control')->setAttribute('id', $filterField->getName() . '-' . 'from');
                $toField = new Number('to', ['label' => sprintf(__t('%s To'),__t($filterField->getLabel()))]);
                $toField->setAttribute('class', 'form-control')->setAttribute('id', $filterField->getName() . '-' . 'to');;
                $numberSet->add($fromField)->add($toField);
                return $numberSet;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_PHONE_NUMBER:
                $field = new Text($filterField->getName(), ['label' =>__t($filterField->getLabel())]);
                $field->setOption('operator', 'like');
                return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_CURRENCY_DROPDOWN:
                $isRange = isset($filterField->getOptions()['filter']['type']) && $filterField->getOptions()['filter']['type'] == "range";

                $field = new Currency($filterField->getName(), ['label' =>__t($filterField->getLabel())]);
                $field->remove($field->get('number')->getName());
                if($isRange) {
                    $fromField = new Number('from', ['label' => sprintf(__t('%s From'),__t($filterField->getLabel()))]);
                    $fromField->setAttribute('class', 'form-control')->setAttribute('id', $filterField->getName() . '-' . 'from');
                    $toField = new Number('to', ['label' => sprintf(__t('%s To'),__t($filterField->getLabel()))]);
                    $toField->setAttribute('class', 'form-control')->setAttribute('id', $filterField->getName() . '-' . 'to');
                    $field->add($fromField)->add($toField);
                }
                 $selectField = $field->get('currency');
                 $selectField->setAttribute('data-placeholder', __t('The Currency'));
                 $selectField->setAttribute('class', 'custom-select');
                 $selectField->setAttribute('data-select', 'true');
                 $selectField->setEmptyOption(__t('The Currency'));
                 $selectField->setValueOptions($this->currencyRepository->getCurrencyList()->toArray());
                 return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN:
                $field = new Select($filterField->getName(), ['label' => __t($filterField->getLabel())]);
                $field->setValueOptions(FilterDataAllowedValues::get(
                    $filterField->getEntity()->getExtendedEntityKeyOrEntityKey(),
                    $filterField->getName(),
                    $filterField->getAllowedFields(),
                    $filterField->getEntity()
                ));
                $field->getLayout()['attributes']['property']['placeholder']['value']?? __t('Please Select');
                $field->setAttribute('class', 'form-control')->setAttribute('data-izam1-forms1-dropdown', true);
                // -1 in valueOption [workorder] so (all) option will pass as option
                if (!array_key_exists(-1, $field->getValueOptions())) {
                    $field->setEmptyOption(__t($filterField->getLayout()['attributes']['property']['placeholder']['value'] ?? __t('All')));
                }
                return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DROPDOWN:
                $field = new MultipleDropdown($filterField->getName(), ['label' => $filterField->getLabel()]);
                $field->setValueOptions($filterField->getAllowedFields());
                $field->setValue(explode(',', $filterField->getDefaultValue()));
                $field->setAttribute('class', 'form-control')->setAttribute('data-izam1-forms1-dropdown', true);
                $field->setAttribute('data-placeholder', $filterField->getLayout()['attributes']['property']['placeholder']['value']?? __t('Please Select'));
                return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN_ADVANCED:
                $field = new AdvancedDynamicDropdown($filterField->getName(), ['label' => $filterField->getLabel()]);
                $field->setValue($filterField->getDefaultValue());
                $options = (new DefaultValuesOptionsAssigner())->assign($filterField, $filterField->getDefaultValue());
                $field->setValueOptions($options);
                $field->setEmptyOption(__t($filterField->getLayout()['attributes']['property']['placeholder']['value']??__t('Please Select')));
                $validationsSetter = new ValidationFormSetter();
                $validationsSetter->setAttributes($filterField->getValidationRules(),   $field);
                $field->setAttribute('data-placeholder', $filterField->getLayout()['attributes']['property']['placeholder']['value']?? __t('Please Select'));
                $field->setAttribute('class', 'form-control');
                $field->setLayout($filterField->getLayout()??[]);
                $field->setAttribute('data-izam1-forms1-advanced-drop-down', json_encode($this->getRelationOptionsPhotos($filterField, $filterField->getRelation()->getTargetEntity())));
                return $field;

            case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF:
                $field = new AdvancedDynamicDropdown($filterField->getName(), ['label' => $filterField->getLabel()]);
                $field->setValue($filterField->getDefaultValue());
                $options = (new DefaultValuesOptionsAssigner())->assign($filterField, $filterField->getDefaultValue());
                $field->setValueOptions($options);
                $field->setEmptyOption(__t($filterField->getLayout()['attributes']['property']['placeholder']['value']??__t(getEmployeesFieldPlaceholder())));
                $validationsSetter = new ValidationFormSetter();
                $validationsSetter->setAttributes($filterField->getValidationRules(),   $field);
                $field->setAttribute('data-placeholder', $filterField->getLayout()['attributes']['property']['placeholder']['value']?? __t(getEmployeesFieldPlaceholder()));
                $field->setAttribute('class', 'form-control');
                $field->setLayout($filterField->getLayout()??[]);

                $params =    [
                    'url' => "/v2/owner/staff/search",
                    'queryParams' => ['with_results_key' => 0, 'allow_inactive'=> '1', 'term'=> 'd', '_type'=> 'query', 'q' => '%term%'],
                    'processResult' => ['valueKey' => 'id', 'textKey' => 'name', 'dataKey' => null],
                ];
                $field->setAttribute('data-izam1-forms1-advanced-drop-down', json_encode($params));
                return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY:
                $isMultiple = false ;
                $label = $filterField->getOptions()['filter']['label'] ?? $filterField->getLabel();
                if($filterField->getOptions() && isset($filterField->getOptions()['type']) && $filterField->getOptions()['type'] == "multiple"){
                    $isMultiple = true;
                }

                if($isMultiple) {
                    $dateSet = new FieldSet($filterField->getName());
                    $field = new DynamicDropdown('in', ['label' =>__t($label)]);
                    $field->setAttribute($filterField->getOptions()['type'], true);
                } else {
                    $field = new DynamicDropdown($filterField->getName(), ['label' =>__t($label)]);
                }
                $field->setEmptyOption(__t($filterField->getLayout()['attributes']['property']['placeholder']['value']??__t('Please Select')));
                $field->setAttribute('data-placeholder',__t($label)??'');
                if ($filterField->getRelation()) {
                    $field->setRelation($filterField->getRelation());
                    $field->setAttribute('data-izam1-forms1-dynamic-drop-down', json_encode($this->getRelationOptions($filterField, $filterField->getRelation()->getTargetEntity())));
                    $field->setFillValueOptionsUsingRelation(true);
                }

                if($filterField->getOptions() && isset($filterField->getOptions()['type']) && $filterField->getOptions()['type']){
                    $field->setAttribute($filterField->getOptions()['type'], true);
                }

                if(!$isMultiple) {
                    return $field;
                }

                $dateSet->add($field);

                return $dateSet;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_ASSIGNED_STAFF:
                $name = $filterField->getRelation()->getName().'.'.$filterField->getBelongsToRelation()->getSourceField()->getName();
                $field = new AssignedStaff($name, ['label' => $filterField->getLabel(), 'operator' => 'in']);
                $options = (new DefaultValuesOptionsAssigner())->assign($filterField,  json_decode($filterField->getDefaultValue(), true));
                $field->setValueOptions($options);
                $field->setValue(array_keys($options));
                $validationsSetter = new ValidationFormSetter();
                $validationsSetter->setAttributes($filterField->getValidationRules(),   $field);
                $field->setLayout($filterField->getLayout()??[]);
                $field->setAttribute('class', 'form-control')->setAttribute('data-placeholder', $filterField->getLayout()['attributes']['property']['placeholder']['value'] ?? __t('Please Select'));
                $field->setAttribute('data-izam1-forms1-advanced-drop-down', json_encode($this->getRelationOptionsPhotos($filterField,  $filterField->getBelongsToRelation()->getTargetEntity())));
                return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN:
                $name = $filterField->getRelation()->getName().'.'.$filterField->getBelongsToRelation()->getSourceField()->getName();
                $dateSet = new FieldSet($name);
                $field = new AjaxSelect('in', ['label' =>__t($filterField->getLabel())]);
                $field->setAttribute('data-placeholder',__t($filterField->getLabel())??'');
                $field->setAttribute('data-izam1-forms1-dynamic-drop-down', json_encode($this->getRelationOptions($filterField, $filterField->getRelation()->getTargetEntity()->getBelongToRelation()[0]->getTargetEntity())));
                $field->setAttribute('multiple', true);
                $dateSet->add($field);
                return $dateSet;

            case EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS:
                $name = $filterField->getRelation()->getName().'.'.$filterField->getBelongsToRelation()->getSourceField()->getName();
                $dateSet = new FieldSet($name);
                $field = new AjaxSelect('in', ['label' =>__t($filterField->getLabel())]);
                $field->setAttribute('data-placeholder',__t($filterField->getLabel())??'');
                $field->setAttribute('data-izam1-forms1-tags', json_encode($this->getRelationAjaxOptions($filterField->getBelongsToRelation())));
                $field->setAttribute('multiple', true);
                $dateSet->add($field);
                return $dateSet;

            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_PICKER:
            case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC:
                $dateSet = new FieldSet($filterField->getName());
                $fromField = new Date('from', ['label' => sprintf('%s %s', __t($filterField->getLabel()), __t('From'))]);
                $fromField->setAttribute('type', EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN);
                $fromField->setAttribute('placeholder',__t($filterField->getLabel())??'');
                $fromField->setAttribute('data-placeholder',__t($filterField->getLabel())??'');
                $options = ['type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE];
                $type = EntityFieldUtil::ENTITY_FIELD_TYPE_DATE;
                $fromField->setAttribute('autocomplete', 'off');
                $format =  getDateFormats('moment_js')[getCurrentSite('date_format')];
                $options["locale"]['format'] = $format;
                $fromField->setAttribute('data-izam1-forms1-date', json_encode($options));
                $fromField->setAttribute('class', 'form-control')->setAttribute('id', $filterField->getName() . '-' . 'from');
                $toField = new Date('to', ['label' => sprintf('%s %s',__t($filterField->getLabel()), __t('To'))]);
                $toField->setAttribute('type', EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN);
                $toField->setAttribute('autocomplete', 'off');
                $toField->setAttribute('data-izam1-forms1-date', json_encode($options));
                $toField->setAttribute('placeholder' ,__t($filterField->getLabel())??'');
                $toField->setAttribute('data-placeholder',__t($filterField->getLabel())??'');
                $toField->setAttribute('class', 'form-control')->setAttribute('id', $filterField->getName() . '-' . 'to');;
                $dateSet->add($fromField)->add($toField);
                return $dateSet;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT:
                $label = $filterField->getOptions()['filter']['label'] ?? $filterField->getLabel();

                $field = new Text($filterField->getName(), ['label' =>__t($label),'operator' => 'like']);
                if ($this->isAutoSuggest($field, $filterField)) {
                    $field->setAttribute('data-izam1-forms1-datalist', json_encode(
                        ['requestUrl' => route('entity.field-auto-suggest', $filterField->getKey())]
                    ));
                }
                return $field;
            case EntityFieldUtil::ENTITY_FIELD_TYPE_FOLLOW_UP_STATUS:
                $entityKey = $filterField->getEntityKey();
                $entityExists = FollowUpStatusData::entityHasFollowUp($entityKey);
                $isFollowUpSettingActive = FollowUpStatusData::getEntityKeyFollowUpSetting($entityKey);
                if($entityExists) {
                    $followUpRepository = (resolve(FollowUpStatusRepository::class));
                    $followUpKey = FollowUpStatusData::getEntityFollowUpKey($entityKey);
                    $manulStatuses = $followUpRepository->getList($followUpRepository->getItemTypeStatuses($followUpKey));
                    $statuses = [];
                    foreach ($manulStatuses as $k => $followUpStatus) {
                        $statuses[] = ['label' => $followUpStatus['name'], 'value' => $k, 'attributes' => ['data-color' => $followUpStatus['color']]];
                    }
                    $field = new Select($filterField->getName(), ['label' => __t($filterField->getLabel())]);
                    $field->setValueOptions($statuses);
                    $field->getLayout()['attributes']['property']['placeholder']['value']?? __t('Please Select');
                    $field->setAttribute('class', 'form-control')->setAttribute('data-izam1-forms1-dropdown', true);
                    $field->setEmptyOption(__t($filterField->getLayout()['attributes']['property']['placeholder']['value']??__t('All')));
                    return $field;
                }
            default:
                $field = new Text($filterField->getName(), ['label' =>__t($filterField->getLabel()),'operator' => 'like']);
                return $field;
        }
    }

    /**
     * @param Relation $relation
     * @return array
     */
    public function getRelationOptionsPhotos($field, $entity)
    {
        $searches = $field->getSearchFields();
        $conditions = $field->getConditionsFields();
        $listFields  = $field->getListingField();


        if (empty($listFields)) {
            $listFields[] = $entity->getPrimaryKey();
            $listFields[] = $entity->getListingField()->getName();
        }


        $filterString = $this->buildFilters($searches, $conditions);

        if (empty($conditions) && empty($searches)) {
            $filterString["filter[{$listFields[1]}][like]"] = '%term%';
            $filterString['per_page'] = 15;
        }

        $params =    [
            'url' => route('entity.list-photo', ['entityKey' => $entity->getEntityKey(), 'dataLevel' => 2, 'mode' => 'listing', 'fields' => $listFields]),
            'queryParams' => $filterString,
            'processResult' => ['valueKey' => $listFields[0], 'textKey' => $listFields[1], 'dataKey' => null],
        ];

        if (method_exists($field, 'getDefaultImg')) {
            $params['default_img'] = $field->getDefaultImg();
        }

        if (!empty($field->getOptions()['form']['show_all'])) {
            $tempOptions = [['text' => 'All', 'id' => -1]];
            $params['show_all'] = $tempOptions;
        }


        return $params;
    }


    private function isAutoSuggest($field, AutoSuggestAwareInterface $filterField)
    {
        return !empty($field->getLayout()['attributes']['property']['auto_suggest']['value']) ||
            $filterField->isAutoSuggest();
    }

    private function getListingColumn($field): string|null
    {
        try {
            if (empty($field->getLayout()['attributes']['property']['entity_listing_field']['value'])) {
                return null;
            }
            $listingKey = $field->getLayout()['attributes']['property']['entity_listing_field']['value'];
            return explode('.', $listingKey)[1];
        } catch (\Exception $e) {
            return null;
        }
    }
}
