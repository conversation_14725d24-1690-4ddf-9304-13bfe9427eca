<?php

use App\Modules\LocalEntity\Creators\WorkflowTypeEntityCreator;
use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Facades\Settings;
use App\Facades\Permissions;
use App\Services\LeaveApplicationService;
use App\Models\Staff;
use App\Services\EntityDocumentService;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Rental\Utils\ContractInstallmentStatusUtill;

$entityKey = request('entityKey');
if (str_starts_with($entityKey, 'le_workflow-type') !== false) {
    return [
        'dataSource' => [
            $entityKey => [
                'view' => [
                    'icon' => '<i class="mdi mdi-eye text-info"></i>',
                    'title' => 'View',
                    'link' => '/owner/work_orders/workflow_view/%id'
                ],
                'update' => [
                    'icon' => '<i class="mdi mdi-circle-edit-outline text-primary"></i>',
                    'title' => 'Edit',
                    'url' => [
                        'name' => 'owner.entity.edit',
                        'parameters' => [
                            'entityKey' => ['value' => $entityKey],
                            'id' => ['field' => 'id']
                        ]
                    ]
                ],
                'delete' => [
                    'class' => 'delete-action',
                    'prompt' => [
                        'global' => 'true',
                        'method' => 'DELETE',
                        'message' => 'Are you sure you want to delete this %s?',
                        'header' => 'Delete'
                    ],
                    'title' => 'Delete',
                    'url' => [
                        'name' => 'owner.entity.delete',
                        'parameters' => [
                            'entityKey' => ['value' => $entityKey],
                            'id' => ['field' => 'id']
                        ]
                    ]
                ]
            ]
        ],
        'defaultActions' => [
            'default' => [
                'view'
            ]
        ]
    ];
}
return [
    'dataSource' => [
        EntityKeyTypesUtil::POST => [
            'update' => [
                'icon' => '<i class="mdi mdi-circle-edit-outline text-primary"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id'],
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ]
        ],
        'default' => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id'],

                    ]
                ],
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-primary"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'global' => 'true',
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ]
        ],
        EntityKeyTypesUtil::EMAIL_TEMPLATE_LOG => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-info"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id'],

                    ]
                ],
            ],
        ],
        EntityKeyTypesUtil::REQUEST_ENTITY_KEY => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-info"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.requests.show',
                    'parameters' => [
                        'request' => ['field' => 'id'],
                        'request_type' => ['value' => str_replace('le_request_type_', '', $entityKey)]
                    ]
                ],
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-circle-edit-outline text-primary"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.requests.edit',
                    'parameters' => [
                        'request_type' => ['value' => str_replace('le_request_type_', '', $entityKey)],
                        'request' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'global' => 'true',
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.requests.destroy',
                    'parameters' => [
                        'request_type' => ['value' => str_replace('le_request_type_', '', $entityKey)],
                        'request' => ['field' => 'id']
                    ]
                ]
            ]
        ],
        EntityKeyTypesUtil::WORK_FLOW => [
            'view' => [
                'class' => 'view-action',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ],
                ],
            ],
            'update' => [
                'class' => 'edit-action',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'clone' => [
                'class' => 'clone-action',
                'title' => 'Clone',
                'url' => [
                    'name' => 'owner.entity.clone',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'sourceEntityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::WORK_ORDER => [
            'view' => [
                'class' => 'view-action',
                'title' => 'View',
                'link' => '/owner/work_orders/workflow_view/%id'
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-circle-edit-outline text-primary"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'clone' => [
                'class' => 'clone-action',
                'title' => 'Clone',
                'url' => [
                    'name' => 'owner.entity.clone',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'sourceEntityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],

        ],
        EntityKeyTypesUtil::PURCHASE_ORDER => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-info"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ],
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-circle-edit-outline text-primary"></i>',
                'title' => 'Edit',
                'link'  => '/owner/purchase_orders/edit/%id'
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
              ],
                    'print' => [
                        'icon' => '<i class="far fa-print fs-18 mr-1 text-center text-purple" style="width: 22px;"></i>',
                        'title' => 'Print',
                        'link'  => '/v2/owner/entity/'.$entityKey.'/%id/show/#print'
                    ],
                    'pdf' => [
                        'icon' => '<i class="far fa-file-pdf fs-20 mr-1 text-center text-danger" style="width: 22px;"></i>',
                        'title' => 'PDF',
                        'link'  => '/owner/purchase_orders/view/%id.pdf'
                    ],
                    'email_to_supplier' => [
                        'icon' =>  '<i class="mdi mdi-email mr-1 fs-20 text-center" style="width: 22px;"></i>',
                        'title' => 'Email To Supplier',
                        'link'  => '/owner/purchase_orders/send_to_supplier/%id'
                    ],
        ],
        EntityKeyTypesUtil::PURCHASE_QUOTATION => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-info"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ],
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-primary"></i>',
                'title' => 'Edit',
                'link'  => '/owner/purchase_quotations/edit/%id'
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ]
        ],
        EntityKeyTypesUtil::PURCHASE_INVOICE => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'callback' => function($rowData) {
                      return ($rowData->type == 0) ? '/owner/purchase_invoices/view/'.$rowData->id : '/owner/purchase_invoices/view_credit_note/'.$rowData->id;
                    }
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'callback' => function($rowData) {
                      return ($rowData->type == 0) ? '/owner/purchase_invoices/edit/'.$rowData->id : '/owner/purchase_invoices/edit_credit_note/'.$rowData->id;
                    }
                ]
            ],
            'pdf' => [
                'icon' =>  '<i class="mdi mdi-file-pdf-box text-dark"></i>',
                'title' => 'PDF',
                'url' => [
                    'callback' => function($rowData) {
                      return ($rowData->type == 0) ? '/owner/purchase_invoices/view/'.$rowData->id.'.pdf' : '/owner/purchase_invoices/view_credit_note/'.$rowData->id.'.pdf';
                    }
                ]
            ],
            'print' => [
                'icon' =>  '<i class="mdi mdi-printer text-primary"></i>',
                'title' => 'Print',
                'link'  => '/owner/purchase_invoices/view/%id/print:1',
                'url' => [
                    'callback' => function($rowData) {
                      return ($rowData->type == 0) ? '/owner/purchase_invoices/view/'.$rowData->id.'/print:1' : '/owner/purchase_invoices/view_credit_note/'.$rowData->id.'/print:1';
                    }
                ]

            ],
            'email_to_supplier' => [
                'icon' =>  '<i class="mdi mdi-email text-dark"></i>',
                'title' => 'Email To Supplier',
                'link'  => '/owner/purchase_invoices/send_to_supplier/%id',
                /*'check' => function ($rowData) {
                    will cost extra queries
                    return !empty($rowData->supplier->email);
                },*/
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'delete-action',
                'title' => 'Delete',
                'prompt' => [
                    'message' => 'Are you sure you want to delete this %s?'
                ],
                'link' => '/owner/purchase_invoices/delete/%id'
            ],
            'clone' => [
                'icon' => '<i class="mdi mdi-content-copy text-primary"></i>',
                'title' => 'Clone',
                'link'  => '/owner/purchase_invoices/add/%id',
                'check' => function ($rowData) {
                    return ($rowData->type == 0)?true:false;
                },
            ],
        ],
        EntityKeyTypesUtil::PURCHASE_REFUND => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'link' => '/owner/purchase_invoices/view_refund/%id',
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'link' => '/owner/purchase_invoices/edit_refund/%id',
            ],
            'pdf' => [
                'icon' =>  '<i class="mdi mdi-file-pdf-box text-dark"></i>',
                'title' => 'PDF',
                'link'  => '/owner/purchase_invoices/view_refund/%id.pdf'
            ],
            'print' => [
                'icon' =>  '<i class="mdi mdi-printer text-primary"></i>',
                'title' => 'Print',
                'link'  => '/owner/purchase_invoices/view_refund/%id/print:1',
            ],
            'email_to_supplier' => [
                'icon' =>  '<i class="mdi mdi-email text-dark"></i>',
                'title' => 'Email To Supplier',
                'link'  => '/owner/purchase_invoices/send_to_supplier/%id',
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'delete-action',
                'title' => 'Delete',
                'prompt' => [
                    'message' => 'Are you sure you want to delete this %s?'
                ],
                'link' => '/owner/purchase_invoices/delete/%id'
            ],
        ],
        EntityKeyTypesUtil::EMAIL_LOG => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-info"></i>',
                'title'=> 'View',
                'link' => '/owner/email_logs/view/%id',
                'attributes'=> [
                    'data-ajax-modal' => true
                ]
            ]
        ],
        'workflow_type' => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-info"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-circle-edit-outline text-primary"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'check' => 'checkAppWorkflowTypes',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'form_builder' => [
                'class' => '',
                'icon' => '<i class="mdi mdi-auto-fix"></i>',
                'title' => 'Custom Fields',
                'check' => 'checkAppWorkflowTypes',
                'url' => [
                    'callback' => function($rowData) {
                        return route('owner.local_entities.builder', ['entityKey' => WorkflowTypeEntityCreator::getWorkFlowEntityName($rowData->id), 'redirect' => '/v2/owner/entity/workflow_type/grid']);
                    },
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'related_forms' => [
                'class' => '',
                'icon' => '<i class="mdi mdi-pencil-ruler"></i>',
                'title' => 'Related Forms',
                'check' => 'checkAppWorkflowTypes',
                'url' => [
                    'callback' => function($rowData) {
                        return route('owner.local_entities.index', ['parent_entity' => \App\Modules\LocalEntity\Creators\WorkflowTypeEntityCreator::getWorkFlowEntityName($rowData->id)]);
                    },
                ]
            ],
            'manage_statuses' => [
                'class' => '',
                'icon' => '<i class="mdi mdi-circle-slice-5 text-active"></i>',
                'title' => 'Manage Statuses',
                'url' => [
                    'name' => 'owner.status.create',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'manage_actions' => [
                'class' => '',
                'icon' => '<i class="mdi mdi-check-underline-circle text-blue"></i>',
                'title' => 'Manage Actions',
                'url' => [
                    'name' => 'owner.follow_up_actions.create',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'manage_permissions' => [
                'class' => '',
                'icon' => '<i class="mdi mdi-account-lock text-danger"></i>',
                'title' => 'Manage Permissions',
                'url' => [
                    'name' => 'owner.entity.permissions',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id'],
                    ]
                ]
            ]
        ],
        EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title'=> 'View',
                'class' => 'dropdown-item',
                'link' => '/owner/suppliers/view/%id'
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'class' => 'dropdown-item',
                'link'  => '/owner/suppliers/edit/%id'
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'dropdown-item',
                'title' => 'Delete',
                'link'  => '/owner/suppliers/delete/%id',
            ]
        ],
        EntityKeyTypesUtil::CONTRACT => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'class' => 'dropdown-item',
                'title' => 'View',
                'link' => route('owner.contracts.show', '%id'),
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'class' => 'dropdown-item',
                'title' => 'Edit',
                'link' => route('owner.contracts.edit', '%id'),
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'dropdown-item',
                'action' => 'destroy',
                'title' => 'Delete',
                'link'  => '/v2/owner/contracts/%id',
                'data'  => [
                    'target' => "#modalDelete",
                    'toggle' => "modal",
                    'message' => "Are You Sure You Want To Delete This %s ?",
                    'action' => '/v2/owner/contracts/%id',
                    'method' => "POST",
                    'data' => json_encode(['_token' => csrf_token(),'_method' => 'DELETE']),
                ],
            ],
        ],
        EntityKeyTypesUtil::CLIENT_ENTITY_KEY => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title'=> 'View',
                'class' => 'dropdown-item',
                'link' => '/owner/clients/view/%id'
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'class' => 'dropdown-item',
                'link'  => '/owner/clients/edit/%id'
            ],
            'clone' => [
                'icon' => '<i class="mdi mdi-content-copy text-primary"></i>',
                'title' => 'Clone',
                'link'  => '/owner/clients/add/%id',
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'dropdown-item',
                'title' => 'Delete',
                'link'  => '/owner/clients/delete_client/%id'
            ],
            'login_as' => [
                'icon' => '<i class="mdi mdi-login u-text-color-subprimary"></i>',
                'title' => 'Login as',
                'link' => '/owner/clients/login_as/%id',
                'check' => function () {
                    $clientOnlineAccess = Settings::getValue(PluginUtil::ClientsPlugin, 'client_disable_online_access');
                    return (!empty(getAuthOwner('is_super_admin')) && 1 != $clientOnlineAccess);
                },
            ],
            'statement' => [
                'icon' => '<i class="mdi mdi-calculator u-text-color-subprimary"></i>',
                'title' => 'Statement',
                'link'  => '/owner/clients/statement/%id',
            ],
        ],
        EntityKeyTypesUtil::STAFF_ENTITY_KEY => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'link' => '/v2/owner/staff/%id',
                'check' => function($rowData) {
                    return !isSiteSuspended();
                },
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'link' => '/v2/owner/staff/%id/edit'
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'delete-action',
                'title' => 'Delete',
                'prompt' => [
                    'message' => 'Are you sure you want to delete this %s?'
                ],
                'link' => '/v2/owner/staff/%id',
                'check' => function($rowData) {
                    return !isOwnerStaffRole($rowData->role_id);
                },
            ],
            'send-login' => [
                'icon' => '<i class="mdi mdi-page-next-outline u-text-color-action"></i>',
                'title' => 'Send Login Details',
                'link' => '/v2/owner/staff/sendLoginDetails/%id',
                'check' => function($rowData) {
                    return $rowData->can_access_system && !isSiteSuspended() && !isOwnerStaffRole($rowData->role_id);
                },
            ],
            'change-password' => [
                'icon' => '<i class="mdi mdi-lock-reset u-text-color-orange"></i>',
                'title' => __t('Change Password'),
                'url' => [
                    'name' => 'owner.staff.changePassword',
                    'parameters' => [
                        'staff' => ['field' => 'id']
                    ]
                ],
                'check' => function($rowData) {

                    return $rowData->can_access_system && !isSiteSuspended() && (
                        (
                            isOwnerStaffRole($rowData->role_id) && isOwner()
                        )
                            ||
                        !isOwnerStaffRole($rowData->role_id)
                        );

                },
            ],
            'set-status-active' => [
                'icon' => '<i class="mdi mdi-account-check u-text-color-subprimary"></i>',
                'title' => __t('Mark as Active'),
                'check' => function($rowData) {
                    return !$rowData->active && (!isOwnerStaffRole($rowData->role_id ) || isOwner());
                },
                'url' => [
                    'name' => 'owner.staff.activate',
                    'parameters' => [
                        'staff' => ['field' => 'id']
                    ]
                ]
            ],
            'set-status-inactive' => [
                'icon' => '<i class="mdi mdi-account-remove u-text-color-subprimary"></i>',
                'title' => __t('Mark as Inactive'),
                'check' => function($rowData) {
                    return $rowData->active && (!isOwnerStaffRole($rowData->role_id ) || isOwner());
                },
                'url' => [
                    'name' => 'owner.staff.deactivate',
                    'parameters' => [
                        'staff' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'undo' => [
                'icon' => '<i class="mdi  text-info mdi-arrow-u-right-top "></i>',
                'title' => 'Undo Approval',
                'check' => function($rowData) {
                    return (($rowData->status == "approved" || $rowData->status == "pending") && $rowData->currentUserCanUndoApproval);
                },
                "link" =>"/v2/owner/entity/leave_application/%id/revert-to-pending",
            ],

            'undo_rejection' => [
                'icon' => '<i class="mdi  text-info mdi-arrow-u-right-top "></i>',
                'title' => 'Undo Rejection',
                'check' => function($rowData) {
                    return ($rowData->status == "rejected" && $rowData->currentUserCanUpdateStatus);
                },
                "link" =>"/v2/owner/entity/leave_application/%id/revert-to-pending",
            ],

            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'approve' =>[
                'icon' => '<i class="mdi mdi-check text-success"></i>',
                'title' => 'Approve',
                "attributes"=>[
                    "target"=>"_blank",
                ],
                "link"=> "/v2/owner/entity/leave_application/%id/show?action=approve",
                'check' => function($rowData) {
                    return ($rowData->status == "pending" && $rowData->currentUserCanUpdateStatus);
                },
            ],
            'reject' =>[
                'icon' => '<i class="mdi mdi-close text-danger"></i>',
                'title' => 'Reject',
                "attributes"=>[
                    "target"=>"_blank",
                ],
                "link"=> "/v2/owner/entity/leave_application/%id/show?action=reject",
                'check' => function($rowData) {
                    return ($rowData->status == "pending" && $rowData->currentUserCanUpdateStatus);
                },
            ],
        ],
        EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::SALES_ORDER => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'link' => '/owner/invoices/view_sales_order/%id'
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'link' => '/owner/invoices/edit_sales_order/%id'
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'delete-action',
                'title' => 'Delete',
                'link' => '/owner/sales_orders/delete/%id'
            ],
            'pdf' => [
                'icon' => '<i class="mdi mdi-file-pdf-box u-text-color-primary"></i>',
                'title' => 'PDF',
                'link' => '/owner/invoices/view_sales_order/%id.pdf'
            ],
            'print' => [
                'icon' => '<i class="mdi mdi-printer text-primary"></i>',
                'title' => 'Print',
                'link' => '/owner/invoices/view_sales_order/%id/print:1'
            ],
            'mail_to_client' => [
                'icon' => '<i class="mdi mdi-email-box text-dark"></i>',
                'title' => 'Email To Client',
                'link' => '/owner/invoices/send_to_client/%id'
            ],
        ],
        EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'link' => '/owner/purchase_invoices/view_debit_note/%id',
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'link' => '/owner/purchase_invoices/edit_debit_note/%id',
            ],
            'pdf' => [
                'icon' => '<i class="mdi mdi-file-pdf-box u-text-color-primary"></i>',
                'title' => 'PDF',
                'link'  => '/owner/purchase_invoices/view_debit_note/%id.pdf'
            ],
            'print' => [
                'icon' =>  '<i class="mdi mdi-printer text-primary"></i>',
                'title' => 'Print',
                'link'  => '/owner/purchase_invoices/view_debit_note/%id/print:1',

            ],
            'email_to_supplier' => [
                'icon' => '<i class="mdi mdi-email-box text-dark"></i>',
                'title' => 'Email To Supplier',
                'link'  => '/owner/purchase_invoices/send_to_supplier/%id',
                /*'check' => function ($rowData) {
                    will cost extra queries
                    return !empty($rowData->supplier->email);
                },*/
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'delete-action',
                'title' => 'Delete',
                'prompt' => [
                    'message' => 'Are you sure you want to delete this %s?'
                ],
                'link' => '/owner/purchase_debit_notes/delete/%id'
            ],
            'clone' => [
                'icon' => '<i class="mdi mdi-content-copy text-primary"></i>',
                'title' => 'Clone',
                'link'  => '/owner/purchase_invoices/add/%id'
            ],
        ],
        EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.item_groups.destroy',
                    'parameters' => [
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::BOM_ENTITY_KEY =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'clone' => [
                'icon' => '<i class="mdi mdi-content-copy text-primary"></i>',
                'class' => 'clone-action',
                'title' => 'Clone',
                'url' => [
                    'name' => 'owner.entity.clone',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'sourceEntityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'clone' => [
                'icon' => '<i class="mdi mdi-content-copy text-primary"></i>',
                'class' => 'clone-action',
                'title' => 'Clone',
                'url' => [
                    'name' => 'owner.entity.clone',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'sourceEntityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'class' => 'dropdown-item',
                'title' => 'View',
                'link' => route('owner.reservation_orders.show', '%id'),
            ],
            'update' => [
                'check' => function () {
                    return Permissions::checkPermission(PermissionUtil::MANAGE_RESERVATION_ORDERS);
                },
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'class' => 'dropdown-item',
                'title' => 'Edit',
                'link' => route('owner.rental_reservation_order_manual.edit', '%id'),
            ],
            'delete' => [
                'check' => function () {
                    return Permissions::checkPermission(PermissionUtil::MANAGE_RESERVATION_ORDERS);
                },
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'dropdown-item',
                'action' => 'destroy',
                'title' => 'Delete',
                'link' => route('owner.reservation_orders.delete', '%id'),
                'data' => [
                    'target' => "#modalDelete",
                    'toggle' => "modal",
                    'message' => "Are You Sure You Want To Delete This %s ?",
                    'action' => route('owner.reservation_orders.delete', '%id'),
                    'method' => "POST",
                    'data' => json_encode(['_token' => csrf_token(), '_method' => 'DELETE']),
                ],
            ],
        ],
        EntityKeyTypesUtil::JOURNAL => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title'=> 'View',
                'class' => 'dropdown-item',
                'link' => '/owner/journals/view/%id'
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'class' => 'dropdown-item',
                'link'  => '/owner/journals/edit/%id'
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'dropdown-item',
                'title' => 'Delete',
                'link'  => '/owner/journals/delete/%id',
            ]
        ],
        EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are You Sure You Want To Delete This %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'set-status-active' => [
                'icon' => '<i class="mdi mdi-check u-text-color-subprimary"></i>',
                'title' => 'Activate',
                'check' => function ($rowData) {
                    $status = json_decode($rowData->value, true)['status'];
                    return $status == MultiCycleApprovalUtil::STATUS_INACTIVE;
                },
                'url' => [
                    'name' => 'owner.multi_cycle_approval_configuration.update_status',
                    'parameters' => [
                        'status' => ['value' => MultiCycleApprovalUtil::STATUS_ACTIVE],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'set-status-inactive' => [
                'icon' => '<i class="mdi mdi-close u-text-color-subprimary"></i>',
                'title' => 'Deactivate',
                'check' => function ($rowData) {
                    $status = json_decode($rowData->value, true)['status'];
                    return $status == MultiCycleApprovalUtil::STATUS_ACTIVE;
                },
                'prompt' => [
                    'method' => 'GET',
                    'message' => 'Are you sure you want to deactivate this configuration?',
                    'header' => 'Deactivate'
                ],
                'url' => [
                    'name' => 'owner.multi_cycle_approval_configuration.update_status',
                    'parameters' => [
                        'status' => ['value' => MultiCycleApprovalUtil::STATUS_INACTIVE],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::SMTP_EMAIL_ADDRESS => [
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-primary"></i>',
                'title' => 'Edit',
                'link'  => route('owner.smtp_email_address.edit', '%id'),
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'icon' => '',
                'title' => 'Delete',
                'link'  => route('owner.smtp_email_address.destroy', '%id'),
            ],
        ],
        EntityKeyTypesUtil::PRODUCTION_PLAN =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'check' => function($data) {
                    if(Permissions::checkPermission(PermissionUtil::EDIT_DELETE_ALL_PRODUCTION_PLANS)) {
                        return true;
                    }
                    $staffId = getAuthOwner('staff_id');
                    if(Permissions::checkPermission(PermissionUtil::EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS)) {
                        return $data->staff_id == $staffId || in_array($staffId, $data->employees);
                    }
                    return false;
                },
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'clone' => [
                'icon' => '<i class="mdi mdi-content-copy text-primary"></i>',
                'class' => 'clone-action',
                'title' => 'Clone',
                'url' => [
                    'name' => 'owner.entity.clone',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'sourceEntityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'check' => function($data) {
                    if (Permissions::checkPermission(PermissionUtil::EDIT_DELETE_ALL_PRODUCTION_PLANS)) {
                        return true;
                    }
                    $staffId = getAuthOwner('staff_id');
                    if (Permissions::checkPermission(PermissionUtil::EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS)) {
                        return $data->staff_id == $staffId || in_array($staffId, $data->employees);
                    }
                    return false;
                },
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::ASSET =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'callback' => function($rowData) {
                        return getCakeUrl(['action'=> 'view' , 'controller' => 'assets' , $rowData->id]);
                    },
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'callback' => function($rowData) {
                        return getCakeUrl(['action'=> 'edit' , 'controller' => 'assets' , $rowData->id]);
                    },
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'title' => 'Delete',
                'url' => [
                    'callback' => function($rowData) {
                        return getCakeUrl(['action'=> 'delete' , 'controller' => 'assets' , $rowData->id]);
                    },
                ]
            ],
        ],
        EntityKeyTypesUtil::STOCKTAKING => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title'=> 'View',
                'class' => 'dropdown-item',
                'link' => '/owner/stocktakings/view/%id'
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'class' => 'dropdown-item',
                'link'  => '/owner/stocktakings/edit/%id'
            ],
            'delete' => [
                'icon' => '<i class="mdi mdi-delete text-danger"></i>',
                'class' => 'dropdown-item',
                'title' => 'Delete',
                'link'  => '/owner/stocktakings/delete/%id'
            ],
        ],
        EntityKeyTypesUtil::CONTRACT_INSTALLMENT =>[
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'check' => function($data) {
                    if(Permissions::checkPermission(PermissionUtil::MANAGE_RESERVATION_ORDERS)) {
                        return true;
                    }
                    return false;
                },
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],

            'pay' => [
                'icon' => '<i class="mdi mdi-currency-usd text-info"></i>',
                'title' => 'Pay',
                'check' => function($data) {

                    if(Permissions::checkPermission(PermissionUtil::MANAGE_RESERVATION_ORDERS)
                    && $data->status != ContractInstallmentStatusUtill::PAID) {
                        return true;
                    }
                    return false;
                },
                "link" => "/v2/owner/entity/get_multi_action_ids/contract_installment?toUrl=/owner/invoices/add&id[]=%id", //contract_installment_ids

            ],

            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'check' => function($data) {
                    if (Permissions::checkPermission(PermissionUtil::DELETE_RESERVATION_ORDERS)) {
                        return true;
                    }

                },
                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => [
            'view' => [
                'icon' => '<i class="mdi mdi-eye text-info"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id'],
                    ]
                ],
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-circle-edit-outline text-primary"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'form_builder' => [
                'icon' => '<i class="mdi mdi-auto-fix"></i>',
                'title' => 'Form Builder',
                'url' => [
                    'callback' => function ($rowData) use ($entityKey) {
                        return route('owner.local_entities.builder',
                            [
                                'entityKey' => \Izam\Booking\Utils\ServiceFormUtil::getAdditionalPrefix() . $rowData->id,
                                'redirect' => route('owner.entity.list', $entityKey),
                                'related_entity' => $entityKey,
                            ]
                        );
                    }
                ]
            ],
            'manage_printable_templates' => [
                'icon' => '<i class="mdi mdi-printer"></i>',
                'title' => 'Manage Printable Templates',
                'url' => [
                    'callback' => function ($rowData) use ($entityKey) {
                        return route('owner.templates.list_entity_templates', ['pdf', \Izam\Booking\Utils\ServiceFormUtil::getAdditionalPrefix() . $rowData->id]);
                    }
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'prompt' => [
                    'global' => 'true',
                    'method' => 'DELETE',
                    'message' => 'Are you sure you want to delete this %s?',
                    'header' => 'Delete'
                ],
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],
        EntityKeyTypesUtil::ENTITY_DOCUMENT =>[

            'view' => [
                'icon' => '<i class="mdi mdi-eye text-success"></i>',
                'title' => 'View',
                'url' => [
                    'name' => 'owner.entity.show',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'update' => [
                'icon' => '<i class="mdi mdi-pencil text-info"></i>',
                'title' => 'Edit',
                'url' => [
                    'name' => 'owner.entity.edit',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
            'upload' => [
                'icon' => '<i class="mdi mdi-upload text-success"></i>',
                'title' => 'Upload New',
                'check' => function ($rowData) {
                    if (!Permissions::checkPermission(PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS)) {
                        return false;
                    }
                    $service = resolve(EntityDocumentService::class);
                    $status = $service->calculateStatus($rowData);
                    return ($status == EntityDocumentStatusUtil::EXPIRED);
                },
                'url' => [
                    'callback' => function ($rowData) use ($entityKey) {
                        return route('owner.entity.create',
                            [
                                'entityKey'=>'entity_document',
                                'subEntityKey'=>'staff',
                                'subEntityId'=> $rowData->entity_id ,
                                'entity_document_type_id' => $rowData->document_types_id,
                                'backUrl'=> route('owner.staff.show',[$rowData->entity_id]). '#documents',
                            ]
                        );
                    }
                ]
            ],
            'delete' => [
                'class' => 'delete-action',
                'attributes'=> [
                    'data-event-shared-delete' => true
                ],
                'prompt' => [
                    'method' => 'DELETE',
                    'message' => "Are you sure you want to delete this %s? \n The document and its attachments will be deleted permanently",
                    'header' => 'Delete'
                ],

                'icon' => '',
                'title' => 'Delete',
                'url' => [
                    'name' => 'owner.entity.delete',
                    'parameters' => [
                        'entityKey' => ['value' => $entityKey],
                        'id' => ['field' => 'id']
                    ]
                ]
            ],
        ],

    ],
    'defaultActions' => [
        'default' => [
            'view',
        ],

        EntityKeyTypesUtil::STAFF_ENTITY_KEY => [
            'view',
            'send-login',
            'change-password',
            'set-status-active',
            'set-status-inactive'
        ],
        EntityKeyTypesUtil::CLIENT_ENTITY_KEY => [
            'view',
            'clone',
            'statement',
            'login_as',
        ],
        EntityKeyTypesUtil::PURCHASE_ORDER => [
           'view',
           'email_to_supplier',
           'pdf',
           'print'
        ]  ,
        EntityKeyTypesUtil::PURCHASE_INVOICE => [
            'view',
            'email_to_supplier',
            'pdf',
            'print',
            'clone',
        ],
        EntityKeyTypesUtil::PURCHASE_REFUND => [
            'view',
            'email_to_supplier',
            'pdf',
            'print',
        ],
        "le_workflow-type-entity" => [
           'view',
           'update',
           'delete',
           'clone'
        ]  ,
        'workflow_type' => [
            'view',
            'update',
            'delete',
            'form_builder',
            'related_forms',
            'manage_statuses',
            'manage_actions',
            'manage_permissions',
        ],
        EntityKeyTypesUtil::SALES_ORDER => [
            'view',
            'pdf',
            'print',
            'mail_to_client',
        ],
        EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => [
            'view',
            'update',
            'delete',
            'set-status-active',
            'set-status-inactive',
        ],
        EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
            'view',
            'update',
            'undo',
            "undo_rejection",
            'delete',
            'approve',
            "reject",
        ],
        EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE => [
            'view',
            'pdf',
            'print',
            'email_to_supplier',
        ],EntityKeyTypesUtil::BOM_ENTITY_KEY => [
            'view',
            'update',
            'delete',
            'clone',
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => [
            'view',
            'update',
            'clone',
            'delete',
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => [
            'view',
            'update',
            'delete',
        ],
        EntityKeyTypesUtil::REQUEST_ENTITY_KEY => [
            'view',
            'update',
            'delete',
        ],
        EntityKeyTypesUtil::SMTP_EMAIL_ADDRESS => [
            'update',
            'delete',
        ],EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER => [
            'view',
            'update',
            'delete',
        ],EntityKeyTypesUtil::PRODUCTION_PLAN => [
            'view',
            'update',
            'delete',
            'clone',
        ],
        EntityKeyTypesUtil::CONTRACT_INSTALLMENT => [
            'view',
            'update',
            'delete',
            'pay',
        ],
        EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => [
            'view',
            'update',
            'delete',
            'form_builder',
            'manage_printable_templates',
        ],
        EntityKeyTypesUtil::ENTITY_DOCUMENT=>[
            'view',
            'update',
            'delete',
            'upload',
        ]
    ]
];

function checkAppWorkflowTypes($rowData)
{
    try {
        return !$rowData->app_related_data->id;
    } catch (Throwable $e) {
        return true;
    }
}
