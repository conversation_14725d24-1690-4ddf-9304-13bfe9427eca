<?php

use App\Utils\EntityPermissionKeyUtil;
use \App\Utils\EntityKeyTypesUtil;
use App\Utils\PermissionUtil;

return [
    'data_source' => [
        // before_getting_data
        'before' =>[
            "*"=>[
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckDynamicEntityViewPermission::class,
                    ]
                ],
            ],
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
                EntityPermissionKeyUtil::LIST => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckRoleBasedPermission::class,
                        "permissions" => [
                            PermissionUtil::VIEW_HIS_OWN_LEAVE_APPLICATIONS,
                            PermissionUtil::VIEW_ALL_LEAVE_APPLICATION,
                            PermissionUtil::VIEW_HIS_TEAM_LEAVE_APPLICATIONS
                        ],
                        // 'isAnd' => false
                    ]
                ],
            ],
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => [
                    EntityPermissionKeyUtil::CREATE => [
                        [
                            'checker' => App\Modules\LocalEntity\Permissions\Checkers\CreateContractInstallementChecker::class,
                        ]
                    ],

            ],
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => [
                EntityPermissionKeyUtil::LIST => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckRoleBasedPermission::class,
                        "permissions" => [
                            PermissionUtil::MANAGE_HRM_SYSTEM,
                            PermissionUtil::MANAGE_ATTENDANCE_SETTINGS
                        ],
                    ]
                ],
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckRoleBasedPermission::class,
                        "permissions" => [
                            PermissionUtil::MANAGE_HRM_SYSTEM,
                            PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
                        ],
                    ]
                ],
                EntityPermissionKeyUtil::CREATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckRoleBasedPermission::class,
                        "permissions" => [
                            PermissionUtil::MANAGE_HRM_SYSTEM,
                            PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
                        ],
                    ]
                ],
            ],
            EntityKeyTypesUtil::ENTITY_DOCUMENT => [
                EntityPermissionKeyUtil::CREATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckEntityDocumentRelatedEntity::class,
                    ],
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckEntityDocumentPermissions::class,
                        'action' => 'create',
                    ],
                ],
            ]
        ],
        // after_getting_data
        'after' =>[
            "*" => [
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckEntityRecordBranchIdCondition::class,
                    ]
                ],
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckEntityRecordBranchIdCondition::class,
                    ]
                ],
                EntityPermissionKeyUtil::DELETE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckEntityRecordBranchIdCondition::class,
                    ]
                ],
            ],
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckLeaveApplicationStatusPending::class,
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckLeaveApplicationStatusPending::class,
                    ],
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckIsOwnRoleBasedPermission::class,
                        // "owner_property_name" => "staff_id",
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS
                        ],
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_LEAVE_APPLICATIONS
                        ]
                    ]
                ],
                EntityPermissionKeyUtil::DELETE => [
                    [
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckLeaveApplicationStatusPending::class,
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckLeaveApplicationStatusPending::class,
                    ],
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckIsOwnRoleBasedPermission::class,
                        // "owner_property_name" => "staff_id",
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS
                        ],
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_LEAVE_APPLICATIONS
                        ]
                    ]
                ],
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckLeaveApplicationViewPermission::class,
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckLeaveApplicationViewPermission::class
                    ]
                ],
            ],
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => [
                EntityPermissionKeyUtil::VIEW =>[
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\IndirectCosts\CheckIndirectCostPermission::class,
                        // 'message' => __t("")
                        "permissions" => [
                            PermissionUtil::VIEW_ALL_MANUFACTURING_ORDERS
                        ],
                        "is_own_permissions" => [
                            PermissionUtil::VIEW_HIS_OWN_MANUFACTURING_ORDERS,
                        ],
                    ]
                ],
                EntityPermissionKeyUtil::DELETE =>[
                    [
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\IndirectCosts\CheckIndirectCostPermission::class,
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\IndirectCosts\DeleteIndirectCostPermission::class,
                        // 'message' => __t("")
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS,
                        ],
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS,
                        ],
                    ]
                ],
                EntityPermissionKeyUtil::UPDATE =>[
                    [
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\IndirectCosts\CheckIndirectCostPermission::class,
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\IndirectCosts\CheckIndirectCostPermission::class,
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS,
                        ],
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS,
                        ],
                    ]
                ],
            ],
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => [
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\StockRequestViewPermissionChecker::class,
                    ]
                ],
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckStockRequestStatusPending::class,
                    ]
                ],
                EntityPermissionKeyUtil::DELETE =>[
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\StockRequests\StockRequestChecker::class,
                        "permissions" => [
                            PermissionUtil::DELETE_STOCK_REQUEST,
                        ],
                    ]
                ],
            ],
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => [
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckItemGroupViewPermission::class,
                    ]
                ],
                EntityPermissionKeyUtil::UPDATE =>[
                    [
                        'checker'=> App\Modules\LocalEntity\Permissions\Checkers\CheckItemGroupCanUpdateItemGroup::class,
                    ]
                ]
            ],
            EntityKeyTypesUtil::BOM_ENTITY_KEY=> [
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckIsOwnRoleBasedPermission::class,
                        // "owner_property_name" => "staff_id",
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_BILL_OF_MATERIALS
                        ],
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_BILL_OF_MATERIALS
                        ]
                    ]
                ],
                EntityPermissionKeyUtil::VIEW => [
                    [

                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckIsOwnRoleBasedPermission::class,

                        "is_own_permissions" => [
                            PermissionUtil::VIEW_HIS_OWN_BILL_OF_MATERIALS
                        ],
                        "permissions" => [
                            PermissionUtil::VIEW_ALL_BILL_OF_MATERIALS
                        ]
                    ]
                ],
            ],
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY=> [
                EntityPermissionKeyUtil::DELETE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\ManageManufacturingOrderChecker::class,
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\ManageManufacturingOrderChecker::class,
                        // "owner_property_name" => "staff_id",
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS
                        ],
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS
                        ]
                    ],
                    [
                        'checker'=> App\Modules\LocalEntity\Permissions\Checkers\DeleteManufacturingOrderChecker::class,
                    ]
                ],
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\ManageManufacturingOrderChecker::class,
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\ManageManufacturingOrderChecker::class,
                        // "owner_property_name" => "staff_id",
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS
                        ],
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS
                        ]
                    ],
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckStatusNotFinishedOrClosed::class,
                    ],
                ],
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckManufacturingOrderViewPermission::class,
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\ManageManufacturingOrderChecker::class,
                        // "owner_property_name" => "staff_id",
                        "is_own_permissions" => [
                            PermissionUtil::VIEW_HIS_OWN_MANUFACTURING_ORDERS
                        ],
                        "permissions" => [
                            PermissionUtil::VIEW_ALL_MANUFACTURING_ORDERS
                        ]
                    ]
                ]
            ],
            EntityKeyTypesUtil::PRODUCTION_PLAN => [
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckProductionPlanViewPermission::class,
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckProductionPlanViewPermission::class,
                        // "owner_property_name" => "staff_id",
                        "is_own_permissions" => [
                            PermissionUtil::VIEW_HIS_OWN_PRODUCTION_PLANS
                        ],
                        "permissions" => [
                            PermissionUtil::VIEW_ALL_PRODUCTION_PLANS
                        ]
                    ]
                ],
                EntityPermissionKeyUtil::DELETE => [
                    [
                        'checker'=> App\Modules\LocalEntity\Permissions\Checkers\DeleteProductionPlanChecker::class,
                        'list_action_checker' => App\Modules\LocalEntity\Permissions\Checkers\ManageProductionPlanHisOwnChecker::class,
                        "is_own_permissions" => [
                            PermissionUtil::EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS
                        ],
                        "permissions" => [
                            PermissionUtil::EDIT_DELETE_ALL_PRODUCTION_PLANS
                        ]
                    ]
                ],
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\AllOrHisOwnPermissionChecker::class,
                        'his_own' => PermissionUtil::EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS,
                        'all' => PermissionUtil::EDIT_DELETE_ALL_PRODUCTION_PLANS,
                        'multiple' => [
                            'table' => 'production_plan_employees',
                            'staff_id_field' => 'staff_id',
                            'foreign_key' => 'production_plan_id'
                        ],
                        'staff_id_field' => 'staff_id',
                        'table_name' => 'production_plans'
                    ],
                    [
                        'checker' => \App\Modules\LocalEntity\Permissions\Checkers\UpdateProductionPlanChecker::class
                    ]
                ],



            ],
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => [
                EntityPermissionKeyUtil::UPDATE => [
                    [

                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\UpdateContractInstallementChecker::class,
                    ]
                ],
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\ViewContractInstallementChecker::class,
                    ]
                ],
                EntityPermissionKeyUtil::DELETE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\DeleteContractInstallmentChecker::class,
                    ]
                ],
            ],
            EntityKeyTypesUtil::LEASE_CONTRACT => [
                EntityPermissionKeyUtil::DELETE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\DeleteLeaseContractChecker::class,
                    ]
                ],

            ],
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION  =>[
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckRoleBasedPermission::class,
                        "permissions" => [
                            PermissionUtil::MANAGE_HRM_SYSTEM,
                            PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
                        ],
                    ]
                ],
            ],
            EntityKeyTypesUtil::ENTITY_DOCUMENT => [
                EntityPermissionKeyUtil::VIEW => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckEntityDocumentPermissions::class,
                        'action' => 'show',
                    ],
                ],
                EntityPermissionKeyUtil::UPDATE => [
                    [
                        'checker' => App\Modules\LocalEntity\Permissions\Checkers\CheckEntityDocumentPermissions::class,
                        'action' => 'update',
                    ],
                ],
            ]

        ],


    ]
];
