<?php

use App\Utils\EntityPermissionKeyUtil;
use App\Utils\PermissionUtil;
use \App\Utils\EntityKeyTypesUtil;
return [
    'data_source' => [
        EntityKeyTypesUtil::PURCHASE_INVOICE => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Add_New_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::CLONE => [
                PermissionUtil::Add_New_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::Edit_Delete_his_own_created_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::Edit_Delete_All_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::View_All_Purchase_Orders
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::View_his_own_created_Purchase_Orders
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::Edit_Delete_his_own_created_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Edit_Delete_All_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::View_All_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::VIEW_OWN => [
                PermissionUtil::View_his_own_created_Purchase_Orders,
            ],
        ],
        EntityKeyTypesUtil::WORK_FLOW_TYPE => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_WORKFLOW_SETTINGS
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_WORKFLOW_SETTINGS
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_WORKFLOW_SETTINGS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_WORKFLOW_SETTINGS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_WORKFLOW_SETTINGS
            ],
        ],
        EntityKeyTypesUtil::PURCHASE_REQUEST => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_PURCHASE_REQUESTS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_PURCHASE_REQUESTS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_PURCHASE_REQUESTS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_PURCHASE_REQUESTS
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_PURCHASE_REQUESTS
            ]
        ],
        EntityKeyTypesUtil::REQUEST_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::CREATE_REQUESTS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::UPDATE_REQUESTS,
            ],
            EntityPermissionKeyUtil::APPROVE_REJECT => [
                PermissionUtil::APPROVE_REJECT_REQUESTS
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_REQUESTS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::DELETE_REQUESTS
            ],
        ],
        EntityKeyTypesUtil::QUOTATION_REQUEST => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS
            ]
        ],
        EntityKeyTypesUtil::WORK_ORDER => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_WORKFLOW
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_WORKFLOW_SETTINGS,
                PermissionUtil::VIEW_ALL_WORKFLOWS,
                PermissionUtil::VIEW_HIS_OWN_WORKFLOWS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::EDIT_AND_DELETE_ALL_WORKFLOWS,
                PermissionUtil::EDIT_AND_DELETE_HIS_OWN_WORKFLOWS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::EDIT_AND_DELETE_ALL_WORKFLOWS,
                PermissionUtil::EDIT_AND_DELETE_HIS_OWN_WORKFLOWS,
            ]
        ],
        EntityKeyTypesUtil::PURCHASE_QUOTATION => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_PURCHASE_QUOTATIONS
            ]
        ],
        EntityKeyTypesUtil::PURCHASE_ORDER => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_PURCHASE_ORDERS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_PURCHASE_ORDERS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_PURCHASE_ORDERS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_PURCHASE_ORDERS
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_PURCHASE_ORDERS
            ]
        ],
        EntityKeyTypesUtil::LOYALTY_RULE => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_LOYALTY_RULES
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_LOYALTY_RULES,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_LOYALTY_RULES
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_LOYALTY_RULES
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_LOYALTY_RULES
            ]
        ],
        EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Add_New_Supplier
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::Edit_Delete_all_suppliers,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::View_All_Suppliers,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Edit_Delete_all_suppliers,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::View_All_Suppliers,
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::View_his_own_Suppliers
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::Edit_And_delete_his_own_added_suppliers,
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::Edit_And_delete_his_own_added_suppliers,
            ],


        ],
        EntityKeyTypesUtil::CLIENT_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Clients_Add_New_Client
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::Edit_Delete_all_clients,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::Clients_View_All_Clients,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Edit_Delete_all_clients,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::Clients_View_All_Clients
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::Clients_View_his_own_Clients
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::Edit_And_delete_his_own_added_clients,
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::Edit_And_delete_his_own_added_clients,
            ],
            EntityPermissionKeyUtil::CLONE => [
                PermissionUtil::Clients_Add_New_Client,
            ],
            EntityPermissionKeyUtil::STATEMENT => [
                PermissionUtil::Invoices_View_All_Invoices,
            ],
        ],
        EntityKeyTypesUtil::STAFF_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Staff_Add_New_Staffs
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::Staff_Edit_Staffs,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::Staff_View_Staffs
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Staff_Edit_Staffs
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::Staff_View_Staffs
            ]
        ],
        EntityKeyTypesUtil::CONTRACT => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_PAYROLL_CONTRACT
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::EDIT_DELETE_PAYROLL_CONTRACTS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_PAYROLL_CONTRACT
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::EDIT_DELETE_PAYROLL_CONTRACTS
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_PAYROLL_CONTRACT,
                PermissionUtil::VIEW_HIS_OWN_CONTRACT
            ],
            //own
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::VIEW_HIS_OWN_CONTRACT
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_PAYROLL_CONTRACTS,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_PAYROLL_CONTRACTS
            ],
        ],
        EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_ALL_LEAVE_APPLICATION,
                PermissionUtil::VIEW_HIS_OWN_LEAVE_APPLICATIONS,
                PermissionUtil::VIEW_HIS_TEAM_LEAVE_APPLICATIONS
            ],
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_LEAVE_APPLICATION
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::EDIT_DELETE_ALL_LEAVE_APPLICATIONS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::EDIT_DELETE_ALL_LEAVE_APPLICATIONS,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS,
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_ALL_LEAVE_APPLICATION,
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::VIEW_HIS_OWN_LEAVE_APPLICATIONS,
                \Izam\Daftra\Common\Utils\PermissionUtil::VIEW_HIS_TEAM_LEAVE_APPLICATIONS

            ],
        ],
        EntityKeyTypesUtil::SALES_ORDER => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS,
                PermissionUtil::SALES_ORDER_ADD_NEW_TO_HIS_OWN_CLIENTS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::SALES_ORDER_EDIT_DELETE_ALL_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::SALES_ORDER_VIEW_ALL_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::SALES_ORDER_EDIT_DELETE_ALL_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::SALES_ORDER_VIEW_ALL_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::SALES_ORDER_EDIT_DELETE_HIS_OWN_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::SALES_ORDER_EDIT_DELETE_HIS_OWN_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::PRINT_PDF => [
                PermissionUtil::SALES_ORDER_VIEW_ALL_SALES_ORDER,
                PermissionUtil::SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::PAGE_PRINT => [
                PermissionUtil::SALES_ORDER_VIEW_ALL_SALES_ORDER,
                PermissionUtil::SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER,
            ],
            EntityPermissionKeyUtil::MAIL_TO_CLIENT => [
                PermissionUtil::SALES_ORDER_VIEW_ALL_SALES_ORDER,
                PermissionUtil::SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER,
            ],
        ],
        EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => [
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_STOCK_REQUESTS
            ],
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_STOCK_REQUEST
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::EDIT_STOCK_REQUEST,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_STOCK_REQUESTS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::DELETE_STOCK_REQUEST
            ]
        ],
        EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Add_New_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::CLONE => [
                PermissionUtil::Add_New_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::Edit_Delete_his_own_created_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::Edit_Delete_All_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::View_All_Purchase_Orders
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::View_his_own_created_Purchase_Orders
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::Edit_Delete_his_own_created_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Edit_Delete_All_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::View_All_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::VIEW_OWN => [
                PermissionUtil::View_his_own_created_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::PRINT_PDF => [
                PermissionUtil::View_All_Purchase_Orders,
                PermissionUtil::View_his_own_created_Purchase_Orders,
            ],
            EntityPermissionKeyUtil::PAGE_PRINT => [
                PermissionUtil::View_All_Purchase_Orders,
                PermissionUtil::View_his_own_created_Purchase_Orders,
            ]
        ],
        EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => [
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::View_his_own_Products,
                PermissionUtil::View_All_Products,
            ], EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Proudcts_Add_New_Proudct,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::View_his_own_Products,
                PermissionUtil::View_All_Products,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Edit_Delete_all_Products,
            ],
            EntityPermissionKeyUtil::UPDATE =>[
                PermissionUtil::Edit_Delete_all_Products,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN =>[
                PermissionUtil::Edit_And_delete_his_own_added_Products,
            ],
            EntityPermissionKeyUtil::DELETE_OWN =>[
                PermissionUtil::Edit_And_delete_his_own_added_Products,
            ]
        ],
        EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY => [
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_PRODUCTION_ROUTINGS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_PRODUCTION_ROUTINGS,
            ],
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_PRODUCTION_ROUTINGS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_PRODUCTION_ROUTINGS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_PRODUCTION_ROUTINGS,
            ],
        ],
        EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => [
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_WORKSTATIONS,
            ],
            EntityPermissionKeyUtil::CREATE =>[
                PermissionUtil::MANAGE_WORKSTATIONS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_WORKSTATIONS,
            ],
            EntityPermissionKeyUtil::DELETE =>[
                PermissionUtil::MANAGE_WORKSTATIONS,
            ],
            EntityPermissionKeyUtil::VIEW =>[
                PermissionUtil::MANAGE_WORKSTATIONS,
            ],
        ],
        EntityKeyTypesUtil::BOM_ENTITY_KEY =>[
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_ALL_BILL_OF_MATERIALS,
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::VIEW_HIS_OWN_BILL_OF_MATERIALS,
            ],
            EntityPermissionKeyUtil::CLONE => [
                PermissionUtil::ADD_NEW_BILL_OF_MATERIAL,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::EDIT_DELETE_ALL_BILL_OF_MATERIALS,
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_BILL_OF_MATERIALS,
            ],
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_NEW_BILL_OF_MATERIAL,
            ],
            EntityPermissionKeyUtil::UPDATE =>[
                PermissionUtil::EDIT_DELETE_ALL_BILL_OF_MATERIALS,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN =>[
                PermissionUtil::EDIT_DELETE_HIS_OWN_BILL_OF_MATERIALS,
            ],
            EntityPermissionKeyUtil::VIEW =>[
                PermissionUtil::VIEW_ALL_BILL_OF_MATERIALS,
                PermissionUtil::VIEW_HIS_OWN_BILL_OF_MATERIALS,
            ],

        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_NEW_MANUFACTURING_ORDER
            ],EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_ALL_MANUFACTURING_ORDERS,
                PermissionUtil::VIEW_HIS_OWN_MANUFACTURING_ORDERS
            ],EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_ALL_MANUFACTURING_ORDERS,
                PermissionUtil::VIEW_HIS_OWN_MANUFACTURING_ORDERS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS,
                PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS,
                PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS
            ],
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_NEW_MANUFACTURING_ORDER,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS,
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_ALL_MANUFACTURING_ORDERS,
                PermissionUtil::VIEW_HIS_OWN_MANUFACTURING_ORDERS,
            ],
            EntityPermissionKeyUtil::CLONE => [
                PermissionUtil::ADD_NEW_MANUFACTURING_ORDER,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_ALL_MANUFACTURING_ORDERS,
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::VIEW_HIS_OWN_MANUFACTURING_ORDERS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::EDIT_DELETE_ALL_MANUFACTURING_ORDERS,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS,
            ],
        ],
        EntityKeyTypesUtil::PRODUCTION_PLAN => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::ADD_PRODUCTION_PLANS
            ],
            EntityPermissionKeyUtil::CLONE => [
                PermissionUtil::ADD_PRODUCTION_PLANS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::EDIT_DELETE_ALL_PRODUCTION_PLANS,
            ],
            EntityPermissionKeyUtil::DELETE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::EDIT_DELETE_ALL_PRODUCTION_PLANS,
                PermissionUtil::EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS,
            ],
            EntityPermissionKeyUtil::UPDATE_OWN => [
                PermissionUtil::EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_ALL_PRODUCTION_PLANS,
            ],
            EntityPermissionKeyUtil::LIST_OWN => [
                PermissionUtil::VIEW_HIS_OWN_PRODUCTION_PLANS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_ALL_PRODUCTION_PLANS,
                PermissionUtil::VIEW_HIS_OWN_PRODUCTION_PLANS,
            ],
        ],
        EntityKeyTypesUtil::BRAND => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Proudcts_Add_New_Proudct
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Edit_Delete_all_Products,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::Edit_Delete_all_Products,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::View_All_Products,
                PermissionUtil::View_his_own_Products,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::View_All_Products,
                PermissionUtil::View_his_own_Products,
            ],
        ],
        EntityKeyTypesUtil::VEHICLE => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::Edit_General_Settings
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::Edit_General_Settings,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::Edit_General_Settings,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::Edit_General_Settings,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::Edit_General_Settings,
            ],
        ],
        EntityKeyTypesUtil::HOLIDAY_LIST_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
        ],
        EntityKeyTypesUtil::HOLIDAY_LIST_DAY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
        ],
        EntityKeyTypesUtil::LEASE_CONTRACT => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::DELETE_RESERVATION_ORDERS,
            ],
        ],
        EntityKeyTypesUtil::CONTRACT_INSTALLMENT => [
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::VIEW_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::VIEW_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_RESERVATION_ORDERS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::DELETE_RESERVATION_ORDERS,
            ],
        ],
        EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY => [
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
        ],
        EntityKeyTypesUtil::HOLIDAY_LIST_ENTITY_KEY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
        ],
        EntityKeyTypesUtil::HOLIDAY_LIST_DAY => [
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
            ],
        ],
        EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY => [
            EntityPermissionKeyUtil::LIST => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
            EntityPermissionKeyUtil::CREATE => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS
            ],
            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS,
            ],
            EntityPermissionKeyUtil::VIEW => [
                PermissionUtil::MANAGE_BOOKING_SETTINGS
            ],
        ],
        EntityKeyTypesUtil::ENTITY_DOCUMENT => [

            EntityPermissionKeyUtil::UPDATE => [
                PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS,
            ],
            EntityPermissionKeyUtil::DELETE => [
                PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS,
            ],

        ],

    ]
];
