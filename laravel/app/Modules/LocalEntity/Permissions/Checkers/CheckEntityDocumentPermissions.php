<?php

namespace App\Modules\LocalEntity\Permissions\Checkers;
use App\Facades\Permissions;

use App\Modules\LocalEntity\Permissions\ResourceBasedPermissionCheckerInterface;
use App\Utils\EntityDocumentPermissionsUtil;

class CheckEntityDocumentPermissions extends <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> implements ResourceBasedPermissionCheckerInterface {

    protected $message = '';
    public function check() : bool {
        $entityKey =  request()->get('subEntityKey');
        if (request()->method() == 'POST' || request()->method() == 'PUT'){
            $entityKey = request()->get('entity_key');
        }
        //handle show
        if (request()->method() == 'GET' && !$entityKey){
            $entityKey = $this->data['entity_key'];
        }
        if (!$entityKey){
            return false;
        }

        $permissions = EntityDocumentPermissionsUtil::getEntityDocumentPermissions($entityKey);
        if( !$permissions || !isset($permissions[$this->params['action']]) ) {
            return false;
        }
        return Permissions::checkPermission($permissions[$this->params['action']]);
    }

    public function message() : string{
        return __t('You are not authorized');
    }

}
