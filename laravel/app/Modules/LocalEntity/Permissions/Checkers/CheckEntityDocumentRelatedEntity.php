<?php

namespace App\Modules\LocalEntity\Permissions\Checkers;
use App\Facades\Permissions;

use App\Modules\LocalEntity\Permissions\ResourceBasedPermissionCheckerInterface;
use App\Services\EntityDocumentTypeService;
use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Daftra\Common\Exception\EntityRecordNotFound;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class CheckEntityDocumentRelatedEntity extends AbstractChecker implements ResourceBasedPermissionCheckerInterface {

    protected $message = '';
    public function check() : bool {
        try {
            $entityKey = request()->get('subEntityKey');
            $entityId = request()->get('subEntityId');
            if (request()->method() == 'POST'){
                $entityKey = request()->get('entity_key');
                $entityId = request()->get('entity_id');
            }
            if (!$entityKey || !$entityId){
                return false;
            }

            /** @var EntityDocumentTypeService $entityDocumentService */
            $entityDocumentService = resolve(EntityDocumentTypeService::class);
            $entityDocumentService->validateEntityKey($entityKey);

            /** @var AppEntityShowAction $appEntityShowAction */
            $appEntityShowAction = resolve(AppEntityShowAction::class);
            $record = $appEntityShowAction->handle($entityKey, $entityId);
            return true;
        } catch (EntityRecordNotFound $exception){
            $this->message = __t('Record not found');
            return false;
        } catch (\Throwable $th) {
            $this->message = __t('Un Supported Entity Type');
            return false;
        }
    }

    public function message() : string{
        return $this->message;
    }

}
