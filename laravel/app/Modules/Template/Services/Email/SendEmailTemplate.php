<?php

namespace App\Modules\Template\Services\Email;

use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\Template\Factories\ReceiverFactory;
use App\Modules\Template\Models\LocalTemplate;
use App\Modules\Template\Models\Template;
use App\Services\EntityEmailPrefrence\EntityEmailPrefrenceService;
use App\Modules\Template\Http\Controllers\SMTPEmailAddress\SendEmailController;
use App\Modules\Template\Services\TemplateService;
use Izam\Daftra\Common\Exception\EntityRecordNotFound;
use Izam\Template\EmailSender\Services\EmailLogService;
use Izam\Template\EmailSender\Services\SMTPMessageSender;
use Webmozart\Assert\InvalidArgumentException;

class SendEmailTemplate
{
    private $isAuto = false;
    private $isMultiple = false;

    public function __construct(
        private TemplateService $templateService,
        private AppEntityShowAction $appEntityShowAction,
        private SMTPAccountFinder $smtpAccountFinder,
        private EmailMessageRenderer $emailMessageRenderer,
        private EmailLogService $emailLogService,
        private ReceiverFactory $receiverFactory,
        private EntityEmailPrefrenceService $entityEmailPreferenceService,
        private EmailTemplateAttachmentFormatterService $attachmentsService,
    )
    {
    }

    public function execute($template, $recordId, array $customData = [])
    {
        $entityKey = $template->getEntityKey();

        $attachments = $this->getAttachments($template, $entityKey);
        $template->setAttachments( app(SendEmailController::class)->prepareAttachments($attachments, $template->id));

        try {
            $record = $this->appEntityShowAction->handle($entityKey, $recordId, $template->getLevel());
        } catch (EntityRecordNotFound $exception) {
            $this->emailLogService->saveLog([
                'entity_key' => $entityKey,
                'entity_id' => $recordId,
                'status' => 0,
                'error' => $exception->getMessage()
            ], $entityKey, $recordId);
            return null;
        }

        (new SMTPEmailLimitationService($this->emailLogService, $template))->checkLimitation();

        $senderAccount = $this->smtpAccountFinder->find($template->getFrom());
        if($this->isAuto) {
            if ($this->isMultiple) {
                $receivers = $customData['to'];
                foreach ($receivers as $receiver) {
                    $customData['to'] = [$receiver['email']];
                    $customData['reciever'] = $receiver;
                    if ($this->entityEmailPreferenceService->isReceiverSubscribedEntity($customData['reciever']['id'], $customData['reciever']['role'], $entityKey)) {
                        $this->sendMessage($template, $record, $customData, $entityKey, $recordId, $senderAccount);
                    }
                }
                return null;
            } else {
                $customData['reciever'] = $this->receiverFactory->format($entityKey, $record);
                if (!$customData['reciever']) {
                    return null;
                }
                if (!$this->entityEmailPreferenceService->isReceiverSubscribedEntity($customData['reciever']['id'], $customData['reciever']['role'], $entityKey)) {
                    return null;
                }
            }
        }
        $this->sendMessage($template, $record, $customData, $entityKey, $recordId, $senderAccount);
    }

    private function sendMessage($template, $record, $customData, $entityKey, $recordId, $senderAccount): void
    {
        $message = $this->emailMessageRenderer->render($template, $record, $customData);

        /** not sure if we should log this or not*/
        if (count($message->to()) == 0) {
            $emailLogData['status'] = 0;
            $emailLogData['error'] = 'At least one valid email is required. ';
            $this->emailLogService->saveLog($emailLogData, $entityKey, $recordId);
            return ;
        }

        $attachmentNames = implode(', ', array_map(function ($attachment) {
            return $attachment->getName();
        }, $message->getAttachments()));

        $emailLogData = $this->emailLogService->createLog($entityKey, $recordId, $message, $senderAccount, $attachmentNames);

        try {
            SMTPMessageSender::send($message, $senderAccount);
        } catch (\Swift_TransportException | InvalidArgumentException $exception) {
            $emailLogData = array_merge($emailLogData, [
                'status' => 0,
                'error' => $exception->getMessage(),
            ]);

            $this->emailLogService->saveLog($emailLogData, $entityKey, $recordId);

            // Suppress Rollbar logging for STARTTLS is required to send mail
            if ($exception instanceof \Swift_TransportException && $exception->getCode() !== 451) {
                throw $exception;
            }
        }

        $emailLogData['status'] = 1;

        $emailLogData['sent_at'] = now();
        $this->emailLogService->saveLog($emailLogData, $entityKey, $recordId);
    }

    public function setIsAuto($isAuto)
    {
        $this->isAuto = $isAuto;
    }

    public function setisMultiple(bool $isMultiple)
    {
        $this->isMultiple = $isMultiple;
    }

    private function getAttachments(Template $template , $entityKey)
    {
        $templates = $this->templateService->getPrintableTemplatesForEmail($entityKey);

        $data = ($template instanceof LocalTemplate) ?
            $this->templateService->getFormData($template->id) :
            $this->templateService->getGlobalTemplateDate($template->id);

        $attachments = $this->attachmentsService->format($data, $templates,[]);

        $attachmentsObj = html_entity_decode($attachments['value']);
        return json_decode($attachmentsObj, true);
    }
}
