<?php

namespace App\Modules\Template\Services\Traits;

use App\Models\Owner;
use Izam\Daftra\Common\Utils\EntityEmailPreferenceUserTypeUtil;

trait FormatStaffData
{
    private function formatStaffDataToEmailReceiverDetails($staffs): array
    {
        $to = [];

        foreach ($staffs as $staff) {
            if ($staff instanceof Owner) {
                $to[] = [
                    'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                    'id' => -1,
                    'email' => getCurrentSite('email')
                ];
            } else {
                $to[] = [
                    'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                    'id' => $staff['id'],
                    'email' => $staff['email_address'],
                ];
            }
        }

        return $to;
    }
}
