<?php

namespace App\Modules\Template\Repositories;

use App\Facades\Branch;
use App\Facades\Plugins;
use App\Modules\LocalEntity\Exceptions\AppException;
use App\Services\StaffService;
use App\Utils\PluginUtil;

class GlobalPlaceholderRepository
{
    const PREFIX = '__system';

    public function get($key) {
        $all = $this->getAll();
        if(!isset($all[$key])) {
            throw new AppException('System place holder not found:'. $key);
        }
        return $all[$key];
    }

    public function getAll($lang = 'en') {
        // Note: in case of logged as user, user data will be wrapped in 'Site' key
        if (!empty($user['Site'])) {
            $user = $user['Site'];
        }

        $site = getCurrentSite();
        $systemName = $lang == 'ar' ? 'دفترة': Site_Full_name;
        $systemLogo = 'https://' . Domain_Short_Name . '/themed/'. Domain_Name_Only .'/images/logo-'. (getCurrentSite('language_code') == 7 ? 'ar' :'en') .'.png';
        $subdomain = getCustomDomain() ?? $site['subdomain'];
        $placeholders =  [
            self::PREFIX . '.env.current_date' => [
                'label' => 'Current Date',
                'path' => self::PREFIX . '.env.current_date',
                'value' => format_date(date('Y-m-d'))
            ],
            self::PREFIX . '.env.current_time' => [
                'label' => 'Current Time',
                'path' => self::PREFIX . '.env.current_time',
                'value' => date('H:i:s')
            ],
            self::PREFIX . '.env.page_no' => [
                'label' => 'Page Number',
                'path' => self::PREFIX . '.env.page_no',
                'value' => 'temp',
            ],
            self::PREFIX . '.env.name' => [
                'label' => 'System Name',
                'path' => self::PREFIX . '.env.name',
                'value' => $systemName,
            ],
            self::PREFIX . '.env.logo' => [
                'label' => 'System Logo',
                'path' => self::PREFIX . '.env.logo',
                'value' => $systemLogo,
            ],
            self::PREFIX . '.env.domain' => [
                'label' => 'System Domain',
                'path' => self::PREFIX . '.env.domain',
                'value' => Domain_Full_Name,
            ],
            self::PREFIX . '.env.view_link' => [
                'label' => 'View Link',
                'path' => self::PREFIX . '.env.view_link',
                'value' => 'temp',
            ],
            self::PREFIX . '.business.name' => [
                'label' => 'Name',
                'path' => self::PREFIX . '.business.name',
                'value' => "{$site['business_name']}"
            ],
            self::PREFIX . '.business.first_name' => [
                'label' => 'First Name',
                'path' => self::PREFIX . '.business.first_name',
                'value' => "{$site['first_name']}"
            ],
            self::PREFIX . '.business.last_name' => [
                'label' => 'Last Name',
                'path' => self::PREFIX . '.business.last_name',
                'value' => "{$site['last_name']}"
            ],
            self::PREFIX . '.business.subdomain' => [
                'label' => 'Subdomain',
                'path' => self::PREFIX . '.business.subdomain',
                'value' => "{$subdomain}"
            ],
            self::PREFIX . '.business.site_logo' => [
                'label' => 'Site Logo',
                'path' => self::PREFIX . '.business.site_logo',
                'value' => isset($site['site_logo']) ? 'https://'.$subdomain . "/files/images/site-logos/{$site['site_logo']}" : null
            ],
            self::PREFIX . '.business.address1' => [
                'label' => 'Address 1',
                'path' => self::PREFIX . '.business.address1',
                'value' => "{$site['address1']}"
            ],
            self::PREFIX . '.business.address2' => [
                'label' => 'Address 2',
                'path' => self::PREFIX . '.business.address2',
                'value' => "{$site['address2']}"
            ],
            self::PREFIX . '.business.city' => [
                'label' => 'City',
                'path' => self::PREFIX . '.business.city',
                'value' => "{$site['city']}"
            ],
            self::PREFIX . '.business.state' => [
                'label' => 'State',
                'path' => self::PREFIX . '.business.state',
                'value' => "{$site['state']}"
            ],
            self::PREFIX . '.business.postal_code' => [
                'label' => 'Postal Code',
                'path' => self::PREFIX . '.business.postal_code',
                'value' => "{$site['postal_code']}"
            ],
            self::PREFIX . '.business.phone1' => [
                'label' => 'Phone 1',
                'path' => self::PREFIX . '.business.phone1',
                'value' => "{$site['phone1']}"
            ],
            self::PREFIX . '.business.phone2' => [
                'label' => 'Phone 2',
                'path' => self::PREFIX . '.business.phone2',
                'value' => "{$site['phone2']}"
            ],
            self::PREFIX . '.business.country_code' => [
                'label' => 'Country Code',
                'path' => self::PREFIX . '.business.country_code',
                'value' => "{$site['country_code']}"
            ],
        ];

        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $branch = Branch::getCurrentBranch();
            $branch = $branch->toArray();
            $placeholders = array_merge($placeholders, [
                self::PREFIX . '.branch.id' => [
                    'label' => 'Id',
                    'path' => self::PREFIX . '.branch.id',
                    'value' => $branch['id'],
                ],
                self::PREFIX . '.branch.code' => [
                    'label' => 'Code',
                    'path' => self::PREFIX . '.branch.code',
                    'value' => $branch['code'],
                ],
                self::PREFIX . '.branch.name' => [
                    'label' => 'Name',
                    'path' => self::PREFIX . '.branch.name',
                    'value' => $branch['name'],
                ],
                self::PREFIX . '.branch.phone1' => [
                    'label' => 'Phone 1',
                    'path' => self::PREFIX . '.branch.phone1',
                    'value' => $branch['phone1'],
                ],
                self::PREFIX . '.branch.phone2' => [
                    'label' => 'Phone 2',
                    'path' => self::PREFIX . '.branch.phone2',
                    'value' => $branch['phone2'],
                ],
                self::PREFIX . '.branch.description' => [
                    'label' => 'Description',
                    'path' => self::PREFIX . '.branch.description',
                    'value' => $branch['description'],
                ],
                self::PREFIX . '.branch.address1' => [
                    'label' => 'Address 1',
                    'path' => self::PREFIX . '.branch.address1',
                    'value' => $branch['address1'],
                ],
                self::PREFIX . '.branch.city' => [
                    'label' => 'City',
                    'path' => self::PREFIX . '.branch.city',
                    'value' => $branch['city'],
                ],
                self::PREFIX . '.branch.state' => [
                    'label' => 'State',
                    'path' => self::PREFIX . '.branch.state',
                    'value' => $branch['state'],
                ],
                self::PREFIX . '.branch.country_code' => [
                    'label' => 'Country Code',
                    'path' => self::PREFIX . '.branch.country_code',
                    'value' => $branch['country_code'],
                ],
                self::PREFIX . '.branch.status' => [
                    'label' => 'Status',
                    'path' => self::PREFIX . '.branch.status',
                    'value' => $branch['status'],
                ],
                self::PREFIX . '.branch.map_location' => [
                    'label' => 'Map Location',
                    'path' => self::PREFIX . '.branch.map_location',
                    'value' => $branch['map_location'],
                ],
            ]);
        }
        return array_merge($placeholders, $this->getCurrentUserData());
    }


    private function getCurrentUserData():array
    {
        $user = getAuthOwner();
        $user = array_merge($user, $this->getStaffInfo($user['staff_id']));
        $fullName = isset($user['full_name']) ? $user['full_name'] : $user['first_name'] . ' ' . $user['last_name'];
        return [
            self::PREFIX . '.current_user.name' => [
                'label' => 'Name',
                'path' => self::PREFIX . '.current_user.name',
                'value' => (isset($user['first_name']) ? $user['first_name'] : '') . ' ' . isset($user['last_name']) ? $user['last_name'] : '',
            ],
            self::PREFIX . '.current_user.first_name' => [
                'label' => 'First Name',
                'path' => self::PREFIX . '.current_user.first_name',
                'value' => isset($user['first_name']) ? $user['first_name'] : '',
            ],
            self::PREFIX . '.current_user.middle_name' => [
                'label' => 'Last Name',
                'path' => self::PREFIX . '.current_user.middle_name',
                'value' => isset($user['last_name']) ? $user['last_name'] : '',
            ],
            self::PREFIX . '.current_user.last_name' => [
                'label' => 'Middle Name',
                'path' => self::PREFIX . '.current_user.last_name',
                'value' => isset($user['middle_name']) ? $user['middle_name'] : '',
            ],
            self::PREFIX . '.current_user.full_name' => [
                'label' => 'Full Name',
                'path' => self::PREFIX . '.current_user.full_name',
                'value' => "$fullName",
            ],
            self::PREFIX . '.current_user.email' => [
                'label' => 'Email',
                'path' => self::PREFIX . '.current_user.email',
                'value' => isset($user['email']) ? $user['email'] : '',
            ],
            self::PREFIX . '.current_user.home_phone' => [
                'label' => 'Phone Number',
                'path' => self::PREFIX . '.current_user.home_phone',
                'value' => "{$user['home_phone']}",
            ],
            self::PREFIX . '.current_user.business_phone' => [
                'label' => 'Business Phone',
                'path' => self::PREFIX . '.current_user.business_phone',
                'value' => "{$user['business_phone']}",
            ],
            self::PREFIX . '.current_user.address1' => [
                'label' => 'Address 1',
                'path' => self::PREFIX . '.current_user.address1',
                'value' => "{$user['address1']}",
            ],
            self::PREFIX . '.current_user.address2' => [
                'label' => 'Address 2',
                'path' => self::PREFIX . '.current_user.address2',
                'value' => "{$user['address2']}",
            ],
            self::PREFIX . '.current_user.city' => [
                'label' => 'City',
                'path' => self::PREFIX . '.current_user.city',
                'value' => "{$user['city']}",
            ],
            self::PREFIX . '.current_user.state' => [
                'label' => 'State',
                'path' => self::PREFIX . '.current_user.state',
                'value' => "{$user['state']}",
            ],
            self::PREFIX . '.current_user.country_code' => [
                'label' => 'Country Code',
                'path' => self::PREFIX . '.current_user.country_code',
                'value' => "{$user['country_code']}",
            ],
            self::PREFIX . '.current_user.department_name' => [
                'label' => 'Department Name',
                'path' => self::PREFIX . '.current_user.department_name',
                'value' => "{$user['department_name']}",
            ],
            self::PREFIX . '.current_user.designation_name' => [
                'label' => 'Designation Name',
                'path' => self::PREFIX . '.current_user.designation_name',
                'value' => "{$user['designation_name']}",
            ],
            self::PREFIX . '.current_user.employment_level_name' => [
                'label' => 'Employment Level Name',
                'path' => self::PREFIX . '.current_user.employment_level_name',
                'value' => "{$user['employment_level_name']}",
            ],
            self::PREFIX . '.current_user.employment_type_name' => [
                'label' => 'Employment Type Name',
                'path' => self::PREFIX . '.current_user.employment_type_name',
                'value' => "{$user['employment_type_name']}",
            ],

        ];
    }


    private function getStaffInfo(int $staffId): array
    {
        $staffData = [
            'full_name' => null,
            'first_name' => null,
            'last_name' => null,
            'home_phone' => null,
            'business_phone' => null,
            'address1' => null,
            'address2' => null,
            'country_code' => null,
            'city' => null,
            'state' => null,
            'department_name' => null,
            'designation_name' => null,
            'employment_level_name' => null,
            'employment_type_name' => null,
        ];
        if (empty($staffId)) {
            return $staffData;
        }
        $staff = app(StaffService::class)->find($staffId);
        $staffData = [
            'full_name' => $staff->full_name,
            'first_name' => $staff->first_name,
            'last_name' => $staff->last_name,
            'home_phone' => $staff->home_phone,
            'business_phone' => $staff->business_phone,
            'address1' => $staff->address1,
            'address2' => $staff->address2,
            'country_code' => $staff->country_code,
            'city' => $staff->city,
            'state' => $staff->state,
            'department_name' => null,
            'designation_name' => null,
            'employment_level_name' => null,
            'employment_type_name' => null,
            "first_name" => $staff->name,
            "last_name" => $staff->last_name,
            "full_name" => $staff->full_name,
            "email" => $staff->email_address

        ];
        if (ifPluginActive(PluginUtil::HRM_PLUGIN) && $staff->staff_info) {
            $staffInfo = $staff->staff_info;
            $staffData['department_name'] = optional($staffInfo->department)->name;
            $staffData['designation_name'] = optional($staffInfo->designation)->name;
            $staffData['employment_level_name'] = optional($staffInfo->employmentType)->name;
            $staffData['employment_type_name'] = optional($staffInfo->employmentLevel)->name;
        }
        return $staffData;
    }

}
