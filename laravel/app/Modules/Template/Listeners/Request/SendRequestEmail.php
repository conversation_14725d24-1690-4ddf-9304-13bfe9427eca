<?php

namespace App\Modules\Template\Listeners\Request;

use App\Modules\Template\Services\Email\SendEmailTemplate;
use App\Services\RequestService;
use Izam\Daftra\Queue\Listeners\IEventHandler;

class SendRequestEmail implements IEventHandler
{
    public function __construct(
        private SendEmailTemplate $sendEmailTemplate,
        private RequestService $requestRepository,
    )
    {
    }

    public function handle($event)
    {
        $request = $this->requestRepository->find($event['data']->id);

        if ( ! $request->requestType->notify_approvers_by_email) {
            return null;
        }

        $template = $this->requestRepository->getRequestEmailTemplate($request);
        $customData['to'] = $this->requestRepository->getRequestEmailsList($request);

        if (empty($customData['to'])) {
            return;
        }

        $this->sendEmailTemplate->setIsAuto(true);
        $this->sendEmailTemplate->setIsMultiple(true);
        $this->sendEmailTemplate->execute($template, $event['data']->id, $customData);
    }

    public static function getInstance()
    {
        return new self(resolve(SendEmailTemplate::class), resolve(RequestService::class));
    }
}
