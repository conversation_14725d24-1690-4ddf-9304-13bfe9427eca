<?php

namespace App\Modules\Template\Listeners\Request;

use App\Modules\Template\Services\Email\DefaultEmailTemplate;
use App\Modules\Template\Services\Email\SendEmailTemplate;
use App\Modules\Template\Services\Traits\FormatStaffData;
use App\Services\RequestService;
use App\Services\StaffService;
use App\Utils\Requests\RequestStatusUtil;
use Izam\Daftra\Queue\Listeners\IEventHandler;
use Izam\Template\EmailSender\Utils\EmailTemplateReservedKeysUtil;
use Izam\Template\Utils\TemplateListingModeUtil;


class SendApprovedRequestEmail implements IEventHandler
{
    use FormatStaffData;

    public function __construct(
        private SendEmailTemplate $sendEmailTemplate,
        private DefaultEmailTemplate $defaultEmailTemplate,
        private RequestService $requestService,
        private StaffService $staffService
    ) {}

    public function handle($event)
    {

        $templateKey = EmailTemplateReservedKeysUtil::REQUEST_ACCEPTED_EMAIL_TEMPLATE;
        if ($event['data']->approval_cycle_configuration_id && ($event['data']->status == RequestStatusUtil::PENDING)) {
            $templateKey = EmailTemplateReservedKeysUtil::REQUEST_NOTIFICATION_EMAIL_TEMPLATE;
        }


        $template = $this->defaultEmailTemplate->getTemplate($templateKey);
        if (!$template || !$template->isActive() || $template->getListingMode() !== TemplateListingModeUtil::AVAILABLE) {
            throw new \Exception('Template not found');
        }

        $usersThatcanApprove = $this->requestService->getOnApproveNotifiableStaffs(toArray($event['data']));

        $customData['to'] = $this->formatStaffDataToEmailReceiverDetails($usersThatcanApprove);
        $this->sendEmailTemplate->setIsAuto(true);
        $this->sendEmailTemplate->setIsMultible(true);
        $this->sendEmailTemplate->execute($template, $event['data']->id, $customData);
    }

    public static function getInstance()
    {
        return new self(resolve(SendEmailTemplate::class), resolve(DefaultEmailTemplate::class), resolve(RequestService::class), resolve(StaffService::class));
    }
}
