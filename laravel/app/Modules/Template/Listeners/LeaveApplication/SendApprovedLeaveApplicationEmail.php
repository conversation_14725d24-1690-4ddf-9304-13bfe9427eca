<?php

namespace App\Modules\Template\Listeners\LeaveApplication;

use App\Modules\Template\Services\Email\DefaultEmailTemplate;
use App\Modules\Template\Services\Email\SendEmailTemplate;
use App\Modules\Template\Services\Traits\FormatStaffData;
use App\Services\LeaveApplicationService;
use App\Services\StaffService;
use App\Utils\LeaveApplicationStatusUtil;
use Izam\Daftra\Queue\Listeners\IEventHandler;
use Izam\Template\EmailSender\Utils\EmailTemplateReservedKeysUtil;
use Izam\Template\Utils\TemplateListingModeUtil;

class SendApprovedLeaveApplicationEmail implements IEventHandler
{
    use FormatStaffData;

    public function __construct(
        private SendEmailTemplate $sendEmailTemplate,
        private DefaultEmailTemplate $defaultEmailTemplateSender,
        private LeaveApplicationService $leaveApplicationService,
        private StaffService $staffService
    ) {}

    public function handle($event)
    {
        $templateKey = EmailTemplateReservedKeysUtil::LEAVE_APPLICATION_ACCEPTED_EMAIL_TEMPLATE;
        if ($event['data']->approval_cycle_configuration_id && ($event['data']->status == LeaveApplicationStatusUtil::getPendingStatus())) {
            $templateKey = EmailTemplateReservedKeysUtil::LEAVE_APPLICATION_NOTIFICATION_EMAIL_TEMPLATE;
        }

        $template = $this->defaultEmailTemplateSender->getTemplate($templateKey);
        if (!$template || !$template->isActive() || $template->getListingMode() !== TemplateListingModeUtil::AVAILABLE) {
            throw new \Exception(sprintf('Template with id %s not found', $template));
        }

        $usersThatcanApproveIds = $this->leaveApplicationService->getOnApproveNotifiableStaffs(toArray($event['data']));

        // $customData initialization, for more information about $customData, please refer to the EmailMessageRenderer::render class
        $customData['to'] = $this->formatStaffDataToEmailReceiverDetails($usersThatcanApproveIds);
        $this->sendEmailTemplate->setIsAuto(true);
        $this->sendEmailTemplate->setIsMultiple(true);
        $this->sendEmailTemplate->execute($template, $event['data']->id, $customData);
    }

    public static function getInstance()
    {
        return new self(resolve(SendEmailTemplate::class), resolve(DefaultEmailTemplate::class), resolve(LeaveApplicationService::class), resolve(StaffService::class));
    }
}
