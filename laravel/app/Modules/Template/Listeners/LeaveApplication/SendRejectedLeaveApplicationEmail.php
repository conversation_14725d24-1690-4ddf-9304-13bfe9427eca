<?php

namespace App\Modules\Template\Listeners\LeaveApplication;

use App\Modules\Template\Services\Email\DefaultEmailTemplate;
use App\Modules\Template\Services\Email\SendEmailTemplate;
use App\Services\LeaveApplicationService;
use Izam\Daftra\Queue\Listeners\IEventHandler;
use Izam\Template\EmailSender\Utils\EmailTemplateReservedKeysUtil;
use Izam\Template\Utils\TemplateListingModeUtil;

class SendRejectedLeaveApplicationEmail implements IEventHandler
{
    public function __construct(
        private SendEmailTemplate $sendEmailTemplate,
        private DefaultEmailTemplate $defaultEmailTemplateSender,
        private LeaveApplicationService $leaveApplicationService
    )
    {
    }

    public function handle($event)
    {
        $template = $this->defaultEmailTemplateSender->getTemplate(EmailTemplateReservedKeysUtil::LEAVE_APPLICATION_REJECTED_EMAIL_TEMPLATE);

        if (!$template || !$template->isActive() || $template->getListingMode() !== TemplateListingModeUtil::AVAILABLE) {
            throw new \Exception(sprintf('Template with id %s not found', $template));
        }

        // $customData initialization, for more information about $customData, please refer to the EmailMessageRenderer::render class
        $customData['to'] = $this->leaveApplicationService->getOwnerAndApplicantEmails($event['data']);

        $this->sendEmailTemplate->setIsAuto(true);
        $this->sendEmailTemplate->setIsMultiple(true);
        $this->sendEmailTemplate->execute($template, $event['data']->id, $customData);
    }

    public static function getInstance()
    {
        return new self(resolve(SendEmailTemplate::class), resolve(DefaultEmailTemplate::class), resolve(LeaveApplicationService::class));
    }
}
