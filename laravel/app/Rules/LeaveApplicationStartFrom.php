<?php

namespace App\Rules;

use App\Models\LeaveApplication;
use Illuminate\Contracts\Validation\Rule;
use Validator;
use App\Utils\PluginUtil;
use App\Facades\Settings;
use App\Services\LeaveApplicationService;

class LeaveApplicationStartFrom implements Rule
{

    private $message;
    private $data;
    public function __construct($data)
    {
        $this->data = $data;
        $this->message = __t("You cannot enter a start date in the past");
    }
    /**
     * @inheritDoc
     */
    public function passes($attribute, $value)
    {
        $disableValidationOnMobile = false;
        if(
        ((request()->header('X-App-Os') == 'android') && (version_compare(request()->header('X-App-Version'), '1.1.8', '<=')))
        ||
        ((request()->header('X-App-Os') == 'ios') && (version_compare(request()->header('X-App-Version'), '1.16.0', '<=')) )
        ){
            $disableValidationOnMobile = true;
        }

        if(Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'allow_entering_leave_applications_in_past_dates') || $disableValidationOnMobile){
            return true;
        }
        $leaveApplicationService = resolve(LeaveApplicationService::class);
        $multiCycleConfiguration = $leaveApplicationService->getMatchedApprovalConfigurations($this->data);
        $allowPastDate = false;
        if($multiCycleConfiguration){
           $isPastDateAllowed = $multiCycleConfiguration->getAllowPastDate();
           $allowPastDate = $isPastDateAllowed;
        }else{
            if(Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'allow_entering_leave_applications_in_past_dates')){
                $allowPastDate = true;
            }
        }
        if($allowPastDate){
            return true;
        }
        $oldLeaveApplication = null;
        $validationError = false;
        if($this->data['id'] != null){
            $oldLeaveApplication = LeaveApplication::where('id', $this->data['id'])
            ->first();
            if($oldLeaveApplication->date_from != $value){
                $validationError = $this->validateFromDate();
            }
        }else{
            $validationError = $this->validateFromDate();
        }

        if($validationError){
            return false;
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function message()
    {
        return $this->message;
    }

    private function validateFromDate(){
        $validator = Validator::make(request()->all(), [
            'date_from' => 'after_or_equal:today',
        ]);
        if ($validator->fails()) {
            return true;
        }

    }

}
