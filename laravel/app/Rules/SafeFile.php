<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class SafeFile implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public $data;

    public $message = '';

    protected array $blockedExt = [
        // Executables
        'exe', 'msi', 'com', 'cmd',

        // Scripts
        'js', 'sh', 'bat', 'ps1', 'php', 'phtml', 'phar',

        // Libraries
        'dll', 'jar',

        // Macro-enabled Office files
        'docm', 'xlsm', 'pptm',
    ];

    protected array $blockedMime = [
        // Executables
        'application/x-msdownload', 'application/x-dosexec',

        // Scripts
        'application/x-php', 'text/x-php',
        'application/x-sh', 'application/x-bat', 'text/javascript', 'application/javascript',
        'application/x-powershell',

        // Libraries
        'application/java-archive', 'application/x-msdownload',

        // Macro-enabled Office files
        'application/vnd.ms-word.document.macroEnabled.12',
        'application/vnd.ms-excel.sheet.macroEnabled.12',
        'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    ];
    public function __construct()
    {
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $ext  = strtolower($value->getClientOriginalExtension() ?: '');
        $mime = strtolower($value->getMimeType() ?: '');
        if (in_array($ext, $this->blockedExt, true) || in_array($mime, $this->blockedMime, true)) {
           return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {

        return __t("This file type is not allowed for security reasons.");
    }
}
