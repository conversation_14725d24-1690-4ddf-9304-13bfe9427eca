<?php

namespace App\Adapters;

use App\Contracts\HttpCallOptions;
use App\Entities\Accounting\Expense;
use App\Exceptions\Accounting\ExpenseSaveFailed;
use App\Exceptions\Invoices\CreateInvoiceApiFailed;
use App\Exceptions\ProductSaveFailed;
use App\Models\Product;
use App\Services\Accounting\EntityJournal;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\Log;

class InternalHttpAdapter
{
    private $httpAdapter = null;
    /**
     * @var \App\Repositories\ApiKeyRepository
     */
    private $apiKeyRepo;
    private $currentSite;
    /**
     * @var \App\Models\ApiKey
     */
    private $apiKey = null;
    public function __construct($currentSite = false)
    {
        $this->currentSite = $currentSite?:getCurrentSite();
        $this->apiKeyRepo = app()->make('App\Repositories\ApiKeyRepository');
        $this->apiKey = $this->apiKeyRepo->findOrCreateKey('internalApi', $this->currentSite['id']);
        $this->httpAdapter = new GuzzleAdapter("https://".getCurrentSiteSubdomain());
//        try {
//            Log::info('internalAdapater', [$this->currentSite, $this->apiKey, $this->httpAdapter]);
//        } catch (\Throwable $th) {
//        }
    }


    public function addInvoice($invoice, $type = "invoice")
    {
        $branchId = getCurrentBranchID();
        $response = $this->httpAdapter->post(new HttpCallOptions('api2/invoices?request_branch_id='.$branchId."&send=".$type,
            ['HTTP_APIKEY' => $this->apiKey->key, 'APIKEY' => $this->apiKey->key],
            $invoice
        ));
        return $response;
    }
    /**
     * gets menu from array
     *
     * @param  int  $force_menu
     *
     * @return bool|array menu
     */
    function getMenu($force_menu = 0)
    {

        $menu = false;
        $httpCallOptions = new HttpCallOptions('api2/layout?debug=0&force_menu='.$force_menu);
        $response = $this->httpAdapter->get($httpCallOptions);

        if ($response) {
            $menu = $response['data']['Layout']['menu'];
        }
        return $menu;
    }
    function getMenuData($force_menu = 0)
    {
        $menu =[];
        $httpCallOptions = new HttpCallOptions('api2/menu_data?debug=0&force_menu='.$force_menu);
        $response = $this->httpAdapter->get($httpCallOptions);
        if ($response) {
            $menu = $response['menu_data'];
        }
        // dd($menu);
        return $menu;
    }

    /**
     * saves journals via internal api
     * @param $journalData \App\Models\Journal
     * @return bool
     */
    function saveJournal(EntityJournal $entityJournal)
    {

        try{
            $branchId = $entityJournal->getBranchId();
//            dd(json_encode($entityJournal->toArray()), ['APIKEY' => $this->apiKey->key]);
            $response = $this->httpAdapter->post(new HttpCallOptions('api2/journals/save_auto?request_branch_id='.$branchId,
                ['HTTP_APIKEY' => $this->apiKey->key, 'APIKEY' => $this->apiKey->key],
                $entityJournal->toArray()
            ));
            if(isset($response['id']))
            {
                return $response['id'];
            }else{
                Log::error("Journal Save Error", ['response' => $response, 'journal' => $entityJournal->toArray()]);
            }
        }catch (\Exception $e)
        {
            Log::error("Journal Save Error", ['exception' => $e, 'journal' => $entityJournal->toArray()]);
        }
    }

    /**
     * @param $expenseId
     * @param Expense $expense
     * @return \Psr\Http\Message\ResponseInterface
     * @throws ExpenseSaveFailed
     */
    function updateExpense($expenseId, Expense $expense)
    {
        $response = $this->httpAdapter->put(new HttpCallOptions("api2/expenses/{$expenseId}?debug=0",
            ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key],
            $expense->toArray()
        ));
        if(!isset($response['code']) && $response['code'] != 200) {
            throw new ExpenseSaveFailed;
        } else {
            return $response;
        }
    }

    /**
     * @param Expense $expense
     * @return \Psr\Http\Message\ResponseInterface
     * @throws ExpenseSaveFailed
     */
    function saveExpense(Expense $expense)
    {

        $response = $this->httpAdapter->post(new HttpCallOptions('api2/expenses?debug=0',
            ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key],
            $expense->toArray()
        ));
        if(!isset($response['id']))
        {
            throw new ExpenseSaveFailed;
        }else{
            return $response;
        }

    }

    /**
     * @param Product $product
     * @return \Psr\Http\Message\ResponseInterface
     * @throws ProductSaveFailed
     */
    function saveProduct(Product $product)
    {
        $response = $this->httpAdapter->post(new HttpCallOptions('api2/products/?debug=0',
            [],
            array('Product'=>$product->toArray())
        ));
        if(!isset($response['id']) && !isset($response['code']) && $response['code'] != 202 )
        {
            throw new ProductSaveFailed();
        }else{
            return $response;
        }

    }

    /**
     * @param $productId
     * @param Product $product
     * @return \Psr\Http\Message\ResponseInterface
     * @throws ProductSaveFailed
     */
    function updateProduct($productId,Product $product)
    {
        $response = $this->httpAdapter->post(new HttpCallOptions("api2/products/{$productId}?debug=0",
            [],
            array('Product'=>$product->toArray())
        ));
        if(!isset($response['code']) && $response['code'] != 200) {
            throw new ProductSaveFailed();
        }else{
            return $response;
        }

    }

    /**
     * delete an expense
     * @param $expenseId
     * @return \Psr\Http\Message\ResponseInterface
     */
    function deleteExpense($expenseId, $branchId = null)
    {
        return $this->httpAdapter->delete(new HttpCallOptions("api2/expenses/$expenseId",
            ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key],
            [],
            ['request_branch_id' => $branchId ?? getCurrentBranchID()]
        ));
    }
    /**
     * delete an product
     * @param $productId
     * @return \Psr\Http\Message\ResponseInterface
     */
    function deleteProduct(int $productId)
    {

        return $this->httpAdapter->delete(new HttpCallOptions("api2/products/$productId",
            []
        ));
    }

    /**
     * @param Expense $expense
     * @return \Psr\Http\Message\ResponseInterface
     */
    function deleteJournal($journalId)
    {
        try{
            $response = $this->httpAdapter->delete(new HttpCallOptions("api2/journals/$journalId",
                ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key]
            ));
            return $response;
        }catch (\Exception $e)
        {
            Log::error("Journal Delete Error", ['exception' => $e, 'id' => $journalId]);
        }

    }

    function sendLoginData($staffID, string $url = null)
    {
        $url = $url ?? getCakeURL(['prefix' =>'api2', 'controller' => 'staff', 'action' => 'send_login_details', $staffID]);
        $this->httpAdapter->get(new HttpCallOptions($url, ['Accept' => 'application/json']), []);
    }

    function getJournalAccounts()
    {
        $response = $this->httpAdapter->get(new HttpCallOptions('api2/journal_accounts/?debug=0'
        ));
        $journalAccounts = array_reduce($response['data'], function ($journalAccounts, $journalAccount) {
            if(!$journalAccounts)
            {
                $journalAccounts = [];
            }
            $journalAccounts[] = $journalAccount['JournalAccount'];
            return $journalAccounts;
        });
        //paginate journal accounts
    }

    public function getPermittedTreasuries($permission)
    {
        try{
            $response = $this->httpAdapter->get(
                new HttpCallOptions('api2/treasuries/list?permission='.$permission)
            );
            return $response['data'];
        }catch (\Exception $e)
        {
            return [];
        }

    }

    /**
     * @param array $data
     * @return \Psr\Http\Message\ResponseInterface
     * @throws CreateInvoiceApiFailed
     */
    public function createInvoice(array $data)
    {
        try {
            $response = $this->httpAdapter->post(new HttpCallOptions('api2/invoices/?debug=0',
                ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key],
                $data
            ));
        } catch (ClientException $exception) {
            $content = $exception->getResponse()->getBody()->getContents();
            throw new CreateInvoiceApiFailed($content);
        }

        if (!isset($response['id']) && (!isset($response['code']) || $response['code'] != 202)) {
            throw new CreateInvoiceApiFailed(__t("Failed to Create Invoice."). " ".$response["message"] ?? "");
        } else {
            return $response;
        }
    }

    public function updateRequisitionData(int $requisition_id)
    {
        try{
            $url = "/api2/requisitions/trigger_update/$requisition_id";

            $response = $this->httpAdapter->get(new HttpCallOptions($url, ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key]), []);
            return $response['status'];
        }catch (\Exception $e)
        {
            return [];
        }
    }

    public function callAPI($url)
    {
        try {
            // we resolve domain on the same machine, so this removes the curl config that ensures this 
            // please don't revert this unless you discuss with the DevOps team
            // $this->httpAdapter->setCustomCurlHeaders([]);
            $this->httpAdapter->setCustomQueryParameters([]);
            $response = $this->httpAdapter->get(new HttpCallOptions(urldecode($url), ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key]), []);
            return $response;
        } catch (\Exception $e) {
            return [];
        }
    }

    public function spelledCurrency($salary, $currency)
    {
        $response = $this->httpAdapter->get(
            new HttpCallOptions("api2/spelled-currency&salary=$salary&currency=$currency")
        );
        if ($response) {
            return $response;
        }
        return false;
    }

    public function callAPIPost($url, $postData)
    {
        try {

            $response = $this->httpAdapter->post(new HttpCallOptions($url,
                ['HTTP_APIKEY' => $this->apiKey->key, 'APIKEY' => $this->apiKey->key],
                $postData
            ));

            return $response;
        } catch (\Exception $e) {
            return [];
        }
    }
    public function callAPIDelete($url)
    {
        try {

            $response = $this->httpAdapter->delete(new HttpCallOptions($url,
                ["Accept"=> "application/json"],
                []
            ));

            return $response;
        } catch (\Exception $e) {
            return $e;
        }
    }

    public function resaveManufacturingOrderJournal($id)
    {
        try {
            $response = $this->httpAdapter->get(new HttpCallOptions("/api2/manufacturing_orders/update_journal/$id",
                ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key]
            ));
            return $response;
        } catch (\Exception $e) {
            return [];
        }
    }

    public function updateOrderInboundRequisition($id , $data)
    {
        try {
            $response = $this->httpAdapter->post(new HttpCallOptions("/api2/manufacturing_orders/update_order_inbound_requisition/$id",
                ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key],
                $data
            ));
            return $response;
        } catch (\Exception $e) {
            return [];
        }
    }

    function checkAutoReminderMessage($entity_type)
    {
        try {
            $response = $this->httpAdapter->get(new HttpCallOptions(
                "api2/auto_reminder_rules/check_auto_reminder_message/{$entity_type}?debug=0",
                ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key],
                []
            ));

            return $response;
        } catch (\Exception $e) {
            return $e;
        }
    }

    function updatePlugin($pluginId, $active = true)
    {
        $active = $active ? 'true' : 'false';
        try {
            return $this->httpAdapter->get(new HttpCallOptions(
                '/api2/update_plugin/' . $pluginId . '/' . $active,
                ['HTTP_APIKEY' => $this->apiKey->key, "APIKEY" => $this->apiKey->key],
                []
            ));
        } catch (\Exception $e) {
            return $e;
        }
    }

}
