<?php

namespace App\Trait;

use App\Services\Traits\DefaultIzamViewData;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Request;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\AppManagerOrderComponent;
use Laminas\Form\Factory;
use Laminas\Form\Form;
use Izam\View\Form\Element\Select;
use Laminas\Form\Element\Text;

trait AppManagerIndexData
{
    use DefaultIzamViewData;

    public function getIndexData($apps, $categories, $allAppsCount): array
    {
        $enableFiltersAppsCount = 30;
        $currentPage = LengthAwarePaginator::resolveCurrentPage();
        $perPage = 20;
        $currentPageItems = $apps->slice(($currentPage - 1) * $perPage, $perPage)->values();
        
        $paginator = new LengthAwarePaginator(
            $currentPageItems,
            $apps->count(),
            $perPage,
            $currentPage,
            ['path' => Request::url(), 'query' => Request::query()]
        );

        return [
            'filtersForm' => ($allAppsCount >= $enableFiltersAppsCount)?$this->getFilterForm($categories):NULL,
            'extraContainerClass' => 'container-full',
            'links' => AppManagerOrderComponent::pageButtonsWithoutData(),
            'pageHead' => AppManagerOrderComponent::PageHeaderButtons($paginator),
            'isGlobal' => true,
            'pagination' => $paginator,
            'reset_filters_url' => route('owner.apps-manager.index'),
            'view_folder' => 'app_manager',
        ];
    }

    public function getFilterForm($categories): Form
    {
        $factory = new Factory();
        $form = $factory->createForm($this->getFilterSpecs($categories));
        $parameters = request()->has('filter') ? request()->get('filter') : request()->all();
        $form->setData($parameters);
        return $form;
    }

    public function getFilterSpecs($categories): array
    {
        $specs = [
            'elements' =>
                [
                    [
                        'spec' => [
                            'name' => 'name',
                            'type' => Text::class,
                            'options' => [
                                'label' => __t('Search by') . ' ' . __t('Name'),
                                'order' => 1,
                                'advanced' => false,
                            ],
                            'attributes' => [
                                'placeholder' => __t('Search by') . ' ' . __t('Name'),
                            ],
                        ]
                    ],
                    [
                        'spec' => [
                            'name' => 'category',
                            'type' => Select::class,
                            'options' => [
                                'label' => __t('Filter by') . ' ' . __t('Category'),
                                'order' => 2,
                                'empty_option' => __t('Category'),
                                'value_options' => $categories,
                                'advanced' => false,
                            ],
                            'attributes' => [
                                'placeholder' => __t('Filter by') . ' ' . __t('Category'),
                            ],
                        ]
                    ],
                ]
        ];

        return $specs;
    }

}