<?php


namespace App\Queue\Listeners;

use App\Repositories\Interfaces\AppSiteRepositoryInterface;
use App\Utilities\Interfaces\HttpClientInterface;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\AppManager\Repositories\AppTriggerRepository;
use Izam\Daftra\Queue\Listeners\IEventHandler;
use \Exception;

class AppHttpHandler implements IEventHandler
{
    private HttpClientInterface $httpClient;
    private AppSiteRepositoryInterface $appSiteRepository;

    public function __construct(HttpClientInterface $httpClient, AppSiteRepositoryInterface $appSite)
    {
        $this->httpClient = $httpClient;
        $this->appSiteRepository = $appSite;
    }

    public function handle($event)
    {
        $payload = [];
        $data = json_decode(json_encode($event->data), true);
        $eventData = $data['data'];
        $triggerData = $data['trigger'];
        $payload['json'] = $eventData;
        $settings = $this->getAppSiteSettings($triggerData['app_id'], $triggerData['site_id']);
        $payload['headers'] = $this->bindAppSettingsHeaders($triggerData['params']['headers'] ?? [], $settings['params']['headers'] ?? []);
        
        $payload['headers']['x-site-id'] = $triggerData['site_id'];
        $payload['headers']['x-event'] = $triggerData['event'] ?? null;
        
        try {
            $response = $this->httpClient->sendRequest($triggerData['params']['url'], $triggerData['params']['method'] ?? 'POST', $payload);
//            Log::info("Trigger Http Client Success");
//            Log::info($response->getBody());
//            Log::info($response->getBody()->getContents());
        } catch (ClientException $exception) {
            // If your application receives a 410 response,
            // that webhook subscription is no longer active, and you should stop sending data to it
            if ($exception->getResponse()->getStatusCode() === 410) {
                $repo = resolve(AppTriggerRepository::class);
                $repo->delete($triggerData['id']);
                return;
            }
            if ($triggerData['site_id'] != 0) {
                Log::info("Trigger Http Client Exception");
                Log::info($exception->getMessage());
                Log::info($exception->getResponse()->getBody()->getContents());
                Log::error($exception);
            }
        } catch (Exception $exception) {
            if ($triggerData['site_id'] != 0) {
                Log::info("Trigger Http Exception");
                Log::info($exception->getMessage());
                Log::error($exception);
            }
        }
    }

    public static function getInstance()
    {
        return resolve(AppHttpHandler::class);
    }

    private function bindAppSettingsHeaders(array $triggerHeaders, array $appHeaders) : array
    {
        if(empty($triggerHeaders)) {
            return [];
        }
        if(empty($appHeaders)) {
            return $triggerHeaders;
        }
        foreach ($appHeaders as $key => $setting) {
            if (isset($triggerHeaders[$key])) {
                $triggerHeaders[$key] = $setting;
            }
        }
        return $triggerHeaders;
    }

    protected function getAppSiteSettings(int $appId, int $siteId)
    {
        if ($siteId === 0) {
            return [];
        }

        $appSite = $this->appSiteRepository->getAppSite($appId, $siteId);
        $appSiteSettings =  json_decode($appSite['settings'], true);
        return $appSiteSettings['settings'] ?? [];
    }
}
