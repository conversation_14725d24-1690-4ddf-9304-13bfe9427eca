<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Izam\Attachment\Models\EntityAttachment;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class EntityDocument extends BaseModel
{
    use HasFactory;

    public function documentType() {
        return $this->belongsTo('App\Models\EntityDocumentType', 'entity_document_type_id', 'id');
    }

    public function entityAttachment()
    {
        return $this->hasMany(EntityAttachment::class, 'entity_id')
            ->where('entity_key', EntityKeyTypesUtil::ENTITY_DOCUMENT);
    }

    public function createdBy() {
        return $this->belongsTo('App\Models\staff', 'staff_id', 'id');
    }

    public function staff() {
        return $this->belongsTo('App\Models\Staff', 'staff_id', 'id');
    }

}
