<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * EmploymentType Class model for dealing with employment_types table
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 */
class EmploymentType extends BaseModel
{
    use SoftDeletes, HasFactory;

    /**
     * @var string table name will deal with.
     */
    protected $table = 'employment_types';

    /**
     * @var array table attributes could be filled.
     */
    protected $fillable = [
        'name',
        'description',
        'active'
    ];

    /**
     * setup a relation with staff information
     * @return \Illuminate\Database\Eloquent\Relations\belongsToMany
     */
    public function staff()
    {
//        return $this->hasMany(StaffInfo::class, 'employment_type_id');
        return $this->belongsToMany('App\Models\Staff', 'staff_info', 'employment_type_id', 'staff_id');
    }

    /**
     * get number of staff
     * @return int
     */
    public function numberOfStaff()
    {
        return $this->staff->count();
    }
}
