<?php
namespace App\Models;

class JournalTransaction extends BaseModel
{
    const CREATED_AT = 'created';
    const UPDATED_AT = 'modified';

    protected $guarded = [];
    function journal()
    {
        return $this->belongsTo('App\Models\Journal');
    }

    function journals()
    {
        return $this->belongsTo('App\Models\Journal', 'journal_id');
    }

    function journal_account()
    {
        return $this->belongsTo(JournalAccount::class);
    }

    function bank_transaction()
    {
        return $this->belongsTo(BankTransaction::class,'bank_transaction_id','id');
    }

    function costCenterTransactions() {
        return $this->hasMany(CostCenterTransaction::class);
    }

    function transactionLogs() {
        return $this->hasMany(JournalTransactionLog::class);
    }

    function bank_transactions()
    {
        return $this->hasMany(BankTransaction::class);
    }
}
