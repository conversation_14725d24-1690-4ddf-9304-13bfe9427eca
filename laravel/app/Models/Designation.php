<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Designation Class model for dealing with designations table
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 */
class Designation extends BaseModel
{
    use SoftDeletes, HasFactory;

    /**
     * @var string table name will deal with.
     */
    protected $table = 'designations';

    /**
     * @var array table attributes could be filled.
     */
    protected $fillable = [
        'name',
        'description',
        'active',
//        'role_id',
//        'department_id',
//        'employment_level_id',
//        'employment_type_id',
        'payroll_frequency',
        'currency_code',
        'salary_structure_id',
        'department_id',
    ];
//
//    /**
//     * make a relation with role model
//     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
//     */
//    public function role()
//    {
//        return $this->belongsTo(Role::class, 'role_id');
//    }
//
//    /**
//     * make a relation with department model
//     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
//     */
//    public function department()
//    {
//        return $this->belongsTo(Department::class, 'department_id');
//    }
//
//    /**
//     * make a relation with employment level model
//     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
//     */
//    public function employmentLevel()
//    {
//        return $this->belongsTo(EmploymentLevel::class, 'employment_level_id');
//    }
//
//    /**
//     * make a relation with employment type model
//     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
//     */
//    public function employmentType()
//    {
//        return $this->belongsTo(EmploymentType::class, 'employment_type_id');
//    }

    /**
     * make a relation with staff model
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function staff()
    {
        return $this->belongsToMany('App\Models\Staff', 'staff_info', 'designation_id', 'staff_id');
    }

    /**
     * get number of staff
     * @return int
     */
    public function numberOfStaff()
    {
        return $this->staff->count();
    }

    /**
     * make a relation with department model
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }
}
