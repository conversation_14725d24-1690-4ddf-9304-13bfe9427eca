<?php

namespace App\Models;

use App\Facades\Plugins;
use App\Utils\ActiveStatusUtil;
use App\Utils\PluginUtil;
use App\Utils\ShiftUtils;
use App\Utils\ShiftWeekdayUtil;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;


class Shift extends BaseModel
{
    use SoftDeletes;
    static $MorphClassName = '';
    protected $fillable = array(
        0 => 'name',
        1 => 'type',
    );

    protected $with = ['shiftDays'];
    public function __construct(array $attributes = [])
    {
        if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN))
        {
            $this->with[] = 'AttendanceFlags';
        }
        parent::__construct($attributes);
    }

    function getMorphClass()
    {
        return self::$MorphClassName;
    }

    function shiftDays()
    {
    	return $this->hasMany('App\Models\ShiftDay');

    }

    public function AttendanceFlags()
    {
        return $this->belongsToMany('App\Models\AttendanceFlag', 'shift_attendance_flags', 'shift_id', 'attendance_flag_id');
    }

    public function staffInfo()
    {
        return $this->hasMany('App\Models\StaffInfo','attendance_shift_id');
    }

    public function staff()
    {
        return $this->hasManyThrough('App\Models\Staff', 'App\Models\StaffInfo', 'attendance_shift_id', 'id');
    }

    public function activeStaffList(){
        return $this->staff()->where('active', ActiveStatusUtil::ACTIVE);
    }

    public function allocatedShifts()
    {
        return $this->hasMany('App\Models\AllocatedShift', 'shift_id');

    }

    public function assignedBookingStaff()
    {
        self::$MorphClassName = '10';
        return $this->morphToMany('App\Models\Staff', 'item', 'item_staffs');
    }

    public function getDaysOffAttribute()
    {
        $weekDays = ShiftWeekdayUtil::getDaysListTranslated();
        $shiftWeekDays = [];
        if($shiftDays = $this->shiftDays())
        {
            $shiftWeekDays = $shiftDays->pluck('weekday')->toArray();
        }
        $offDays = array_diff(array_keys($weekDays), $shiftWeekDays);

        return implode(', ',array_map(function($day) use ($weekDays){
            return $weekDays[$day];
        }, $offDays));
    }

    /**
     * get shift day by date
     * @param string $date
     * @return ShiftDay|null
     */
    public function getShiftDayByDate(string $date)
    {
        $weekDay = strtolower(Carbon::parse($date)->format('l'));
        $shiftDays = $this->shiftDays;
        $siftWeekDay = null;
        foreach ($shiftDays as $shiftDay) {
            if ($shiftDay->weekday == $weekDay) {
                $siftWeekDay = $shiftDay;
                break;
            }
        }
        return $siftWeekDay;
    }

    public function staffSecondaryShift()
    {
        return $this->hasManyThrough('App\Models\Staff', 'App\Models\StaffInfo', 'secondary_shift_id', 'id')->where('staff_info.has_secondary_shift', 1);
    }

    public function uniqueStaffInShift()
    {
        $staffFirstShift = $this->staff()->get();
        $staffSecondaryShift = $this->staffSecondaryShift()->get();
        return $staffFirstShift->merge($staffSecondaryShift)->unique('id');
    }

    public function countUniqueStaffInShift(): int
    {
        return $this->uniqueStaffInShift()->count();
    }
}

