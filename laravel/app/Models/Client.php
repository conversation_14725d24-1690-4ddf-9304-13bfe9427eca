<?php

namespace App\Models;

use App\Models\Interfaces\HasBasicInfoInterface;
use App\Models\Interfaces\NotifiableInterface;
use App\Models\Traits\HasCustomData;
use App\Utils\CategoryTypesUtil;
use App\Utils\ItemStaffUtil;
use Illuminate\Auth\Authenticatable;
use Illuminate\Notifications\Notifiable;
use Izam\Attachment\Models\EntityAttachment;
use Izam\Daftra\Common\Auth\AuthUserTypeUtil;
use Laravel\Passport\HasApiTokens;
use App\Utils\Invoices\InvoiceTypeUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Booking\Models\Booking;

class Client extends BaseBranchesModel implements NotifiableInterface, HasBasicInfoInterface
{
    use Notifiable;
    use HasCustomData;
    use Authenticatable;
    use HasApiTokens;

    public $customDataTable = 'clients_custom_data';

    public $appends = ['path'];

    protected $fillable = [
        'last_ip',
        'last_login'
    ];

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifiable')->orderBy('created_at', 'desc');
    }

    public function membership(){
        return $this->hasOne(Membership::class,'client_id');
    }

    public function tags(){
        return $this->belongsToMany(Tag::class, 'items_tags', 'item_id', 'tag_id')->wherePivot('item_type', '=', 2);
    }

    public function assignedStaff(){
        return $this->hasManyThrough(Staff::class, ItemStaff::class, 'item_id', 'id', null, 'staff_id')->where('item_type', ItemStaffUtil::CLIENT_ITEM_TYPE);
    }

    public function primaryStatus(){
        return $this->belongsTo(FollowUpStatus::class, 'follow_up_status');
    }

    public function secondaryStatus(){
        return $this->belongsTo(FollowUpStatus::class, 'secondary_follow_up_status');
    }

    public function clientTimezone(){
        return $this->belongsTo(Timezone::class, 'timezone');
    }

    public function notes(){
        return $this->hasMany(Post::class, 'item_id')->where('item_type', 1)->orderBy('date', 'DESC');
    }

    public function incompletedAppointment(){
        return $this->hasOne(FollowUpReminder::class, 'item_id')->where('item_type', 1)->where('status', 0)->orderBy('date');
    }

    public function invoices(){
        return $this->hasMany(Invoice::class, 'client_id')->where('type', InvoiceTypeUtil::INVOICE);
    }

    public function estimates(){
        return $this->hasMany(Invoice::class, 'client_id')->where('type', InvoiceTypeUtil::ESTIMATE);
    }

    /**
     * get photo path
     * @return string
     */
    public function getPhotoPathAttribute(): string
    {
        if (isset($this->photo) && !empty($this->photo)) {
            return CAKE_BASE_URL . "files/" . getSiteHash() . "/photos/" . $this->photo;
        }
        return getDefaultAccountImage();
    }

    public function getPhotoFilePathAttribute(): string
    {
        $attachments=$this->attachments;
        foreach($attachments as $attachment){
            if($attachment->entityAttachmnet && $attachment->entityAttachmnet->entity_field_key == 'clients.photo'){
                return \Izam\Aws\Aws::getPermanentUrl($attachment->path);
            }
        }

        if (isset($this->photo) && !empty($this->photo)) {
            return 'files/' . getSiteHash() . '/photos/' . $this->photo;
        }
        return \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($this->business_name, $this->id, 78, null);
    }

    public function getId()
    {
        return $this->id;
    }

    /* for oauth2 to allow multiple auth, prefix was needed */
    public function getAuthIdentifier()
    {
        return $this->{$this->getAuthIdentifierName()};
    }

    public function getFirstName(): ?string
    {
        return $this->first_name;
    }

    public function getLastName(): ?string
    {
        return $this->last_name;
    }

    public function getBusinessName(): ?string
    {
        return $this->business_name;
    }

    public function getPhoto(): ?string
    {
        return $this->photo;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }


    public function getPathAttribute()
    {
        if ($this->photo) {
            return "/files/" . dechex(crc32(getCurrentSite('id'))) . "/photos/".$this->photo;
        } else {
            return '';
        }
    }

    public function getRoleId()
    {
        return 0;
    }

    public function getUserType(): ?string
    {
        return AuthUserTypeUtil::CLIENT;
    }

    public function category_relation()
    {
        return $this->belongsTo(Category::class, 'category_id')
            ->where('category_type', CategoryTypesUtil::CATEGORY_TYPE_CLIENT);
    }

    public function attachments()
    {
        return $this->belongsToMany(File::class, 'entity_attachments', 'entity_id', 'file_id')->where('entity_attachments.entity_key', '=', EntityKeyTypesUtil::CLIENT_ENTITY_KEY)->whereNull('entity_attachments.deleted_at');
    }

    public function entityAttachment()
    {
        return $this->hasOne(EntityAttachment::class, 'entity_id')
            ->where('entity_key', 'client');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'client_id');
    }
}
