<?php

namespace App\Models;

use App\Modules\Template\Models\SMTPEmailAddress;
use Carbon\Carbon;
use App\Utils\ActiveStatusUtil;
use Laravel\Passport\HasApiTokens;
use App\Models\Traits\HasCustomData;
use Illuminate\Auth\Authenticatable;
use App\Models\Commissions\Commission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Notifications\Notifiable;
use Izam\Daftra\Staff\Util\StaffTypeUtil;
use App\Models\Commissions\CommissionRule;
use Illuminate\Database\Eloquent\SoftDeletes;
use Izam\Daftra\Common\Auth\AuthUserTypeUtil;
use App\Models\Interfaces\NotifiableInterface;
use App\Models\Interfaces\HasBasicInfoInterface;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Izam\Aws\Aws;

class Staff extends BaseModel implements NotifiableInterface, HasBasicInfoInterface
{
    use SoftDeletes, HasCustomData, Notifiable, Authenticatable, Has<PERSON><PERSON>Tokens, HasFactory;
    public $customDataTable = 'staffs_custom_data';

    protected $table = 'staffs';
    protected $guarded = [];

    const UPDATED_AT = null;
    const OWNER_STAFF_ID = 0;
    const OWNER_ROLE_ID = -1;
    public $appends = ['path'];

    static $MorphClassName = '';

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifiable')->orderBy('created_at', 'desc');
    }

    function role()
    {
        return $this->belongsTo('App\Models\Role');
    }

    function branches()
    {
        Branch::$MorphClassName = '13';
        return $this->morphedByMany('App\Models\Branch', 'item', 'item_staffs');
    }

    function accessibleBranches()
    {
        $branches = $this->branches;
        $branches = $branches->empty() && $this->type == StaffTypeUtil::EMPLOYEE ? collect([$this->branch]) : $branches;
        return $branches;
    }


    function branch()
    {
        return $this->belongsTo('App\Models\Branch', 'branch_id', 'id');
    }

    function shift()
    {
        Shift::$MorphClassName = '10';
        return $this->morphedByMany('App\Models\Shift', 'item', 'item_staffs');
    }

    function staff_info()
    {
        return $this->hasOne('App\Models\StaffInfo');
    }


    function staff_job()
    {
        return $this->hasOne('App\Models\StaffJob');
    }

    function country()
    {
        return $this->belongsTo('App\Models\Country', 'country_code', 'code');
    }
    function staff_nationality()
    {
        return $this->belongsTo('App\Models\Country', 'nationality', 'code');
    }

    function language()
    {
        return $this->belongsTo('App\Models\Language', 'language_code');
    }

    function holiday_lists()
    {
        return $this->belongsToMany('App\Models\HolidayList', 'staff_holiday_lists');
    }

    function getImageAttribute()
    {
        if ($this->photo) {
            // return Storage::temporaryUrl(
            //     $this->photo, Carbon::now()->addMinutes(env('AWS_LIFE_TIME', 100))
            // );
            $awsService = new Aws;
            return $awsService->getPermanentUrl($this->photo);
        }
        return '';
    }

    function department()
    {
        return $this->belongsToMany('App\Models\Department', 'staff_info', 'staff_id', 'department_id');
    }

    function treasury()
    {
        return $this->belongsTo('App\Models\Treasury', 'default_treasury_id');
    }

    function departments(){
        return $this->department();
    }

    function designation()
    {
        return $this->belongsToMany('App\Models\Designation', 'staff_info', 'staff_id', 'designation_id');
    }

    function attendance_shift()
    {
        return $this->belongsToMany('App\Models\Shift', 'staff_info', 'staff_id', 'attendance_shift_id');
    }

    function attendance_sheets(){
        return $this->hasMany('App\Models\AttendanceSheet');
    }

    function leave_policy()
    {
        return $this->belongsToMany('App\Models\LeavePolicy', 'staff_info', 'staff_id', 'leave_policy_id');
    }

    function employment_type()
    {
        return $this->belongsToMany('App\Models\EmploymentType', 'staff_info', 'staff_id', 'employment_type_id');
    }

    function employee_level()
    {
        return $this->belongsToMany('App\Models\EmploymentLevel', 'staff_info', 'staff_id', 'employment_level_id');
    }

    public function machines()
    {
        return $this->belongsToMany('App\Models\Machine', 'machine_employee_mappings', 'staff_id', 'machine_id');
    }

    public function contracts(){
        return $this->hasMany('App\Models\Contract');
    }

    public function defaultAccount()
    {
        return $this->belongsTo(JournalAccount::class, 'default_account_id');
    }
    public function defaultStore()
    {
        return $this->belongsTo(Store::class, 'default_store_id');
    }

    public function managedDepartment()
    {
        return $this->hasOne('App\Models\Department', 'manager_id');
    }

    public function managedDepartments()
    {
        return $this->belongsToMany('App\Models\Department','department_managers','manager_id','department_id');
    }
    /**
     * make the loan relation
     * @return HasMany
     */
    public function loans(){
        return $this->hasMany(Loan::class,'staff_id');
    }

    public function commission()
    {
        return $this->hasMany(Commission::class);
    }

    public function commissionRules()
    {
        return $this->belongsToMany(CommissionRule::class,'commission_rule_staffs');
    }

    public function activeCommissionRules(){
        return $this->belongsToMany(CommissionRule::class,'commission_rule_staffs')->where('status',ActiveStatusUtil::ACTIVE);
    }

    public function attendanceRestriction()
    {
        //Get only active restriction
        return $this->belongsTo('App\Models\AttendanceRestriction', 'attendance_restriction_id')->where('status', 1);
    }

    public function getFirstAndLastNameAttribute()
    {
        return sprintf("%s %s", $this->name, $this->last_name);
    }

    public function getFullNameAttribute($full_name)
    {
        return strlen(trim($full_name)) != 0 ? $full_name : trim(sprintf("%s %s %s", $this->name, $this->middle_name, $this->last_name));
    }

    public function getId()
    {
        return $this->id;
    }

    /* for oauth2 to allow multiple auth, prefix was needed */
    public function getAuthIdentifier()
    {
        return $this->{$this->getAuthIdentifierName()};
    }

    public function getFirstName(): ?string
    {
        return $this->name;
    }

    public function getLastName(): ?string
    {
        return $this->last_name;
    }

    public function getBusinessName(): ?string
    {
        return null;
    }

    public function getPhoto(): ?string
    {
        return $this->photo;
    }

    public function getEmail(): ?string
    {
        return $this->email_address;
    }

    public function getPathAttribute()
    {
        return $this->photo;
    }

    public function initialsName()
    {
        $towChar = '';
        $full_name = explode(' ', $this->full_name);
        if (!empty($full_name[0])) {
            $towChar = ucfirst(mb_substr($full_name[0], 0, 1));
        }
        if (!empty($full_name[count($full_name)-1])) {
            $towChar .=' '. ucfirst(mb_substr($full_name[count($full_name)-1], 0, 1));
        }
        return $towChar;
    }




    public function getRoleId()
    {
        return $this->role_id;
    }

    public function getUserType(): ?string
    {
        if ($this->type == StaffTypeUtil::USER) { //TO DO if not using staff value remove this condition
            return    AuthUserTypeUtil::STAFF;
        }
        return  $this->type;
    }

    public function additionalFields()
    {
        return $this->hasOne(LeCustomDataStaff::class, 'reference_id');
    }

    function direct_manager()
    {
        return $this->belongsToMany('App\Models\Staff', 'staff_info', 'staff_id', 'direct_manager_id');
    }

    public function isActive()
    {
        return $this->active;
    }

    function smtpEmailAddress()
    {
        return $this->belongsTo(SMTPEmailAddress::class, 'smtp_email_address_id', 'id');
    }

    function attendanceLogs()
    {
        return $this->hasMany(AttendanceLog::class)->latest('created')->limit(10);
    }
}
