<?php

namespace App\Models;

use App\Services\AttendanceDayService;
use App\Utils\AttendanceDayStatusTypesUtil;
use Carbon\CarbonInterval;

/**
 * AttendanceDay Class attendance day model
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 */
class AttendanceDay extends BaseModel
{
    public $incrementing = false;
    /**
     * @var array $fillable fillable array
     */
    protected $fillable =  [
        'expected_working_hours',
        'actual_working_hours',
        'calculation_type',
        'leave_type_id',
        'leave_count',
        'status',
        'sign_in',
        'sign_out',
        'on_duty',
        'off_duty',
        'beginning_in',
        'beginning_out',
        'ending_in',
        'ending_out',
        'shift_id',
        'attendance_delay',
        'early_leave',
        'attendance_permission_id',
        'notes',
        "staff_id",
        "date",
        "id",
        'can_work_on_off_days'
    ];

    protected $with = ['leaveType'];

    /**
     * @inheritdoc
     */
    protected $casts = [
        'error_log' => 'array'
    ];

    protected $appends = [
        'day_status_color',
        'sign_in_note',
        'sign_out_note',
    ];

    /**
     * set a relation between attendance day and employee
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function employee()
    {
        return $this->belongsTo(Staff::class, 'staff_id')->withTrashed();
    }

    public function attendanceLogs() {
        return $this->hasMany(AttendanceLog::class);
    }

    public function staffs(){
        return $this->employee();
    }

    public function staff_info(){
        return $this->belongsTo(StaffInfo::class, 'staff_id','staff_id')->withTrashed();
    }

    /**
     * set a relation between attendance day and leave type
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function leaveType()
    {
        return $this->belongsTo(LeaveType::class, 'leave_type_id')->withTrashed();
    }

    /**
     * set a relation between attendance day and shift
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function shift()
    {
        return $this->belongsTo(Shift::class, 'shift_id')->withTrashed();
    }

    /**
     * set a relation between attendance day and permission
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function attendancePermission()
    {
        return $this->belongsTo(AttendancePermission::class, 'attendance_permission_id');
    }

    /**
     * set a relation between attendance day and sheet
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function attendanceSheet()
    {
        return $this->belongsTo(AttendanceSheet::class, 'attendance_sheet_id');
    }

    public function attendance_sheets()
    {
        return $this->attendanceSheet();
    }

    /**
     * set a relation between attendance day and attendance flags
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function attendanceFlags()
    {
        return $this->belongsToMany(
            AttendanceFlag::class,
            'attendance_day_flags',
            'attendance_day_id',
            'attendance_flag_id'
        )->withPivot('value');
    }

    function department()
    {
        return $this->belongsToMany('App\Models\Department', 'staff_info', 'staff_id', 'department_id','staff_id');
    }

    public function getDayStatusColorAttribute()
    {
        return match ($this->status){
            AttendanceDayStatusTypesUtil::ABSENT => '#EB2121',
            AttendanceDayStatusTypesUtil::LEAVE => $this->leaveType->color ?? '#026BFF',
            AttendanceDayStatusTypesUtil::DAY_OFF=> '#E5F0FD',
            AttendanceDayStatusTypesUtil::PRESENT => '#29B80B',
            AttendanceDayStatusTypesUtil::SIGN_IN_ONLY, AttendanceDayStatusTypesUtil::SIGN_OUT_ONLY => '#F7912B',
            default => '#FFF',
        };
    }

    /**
     *  @desc: returns attendance day status note
     */
    public function getSignInNoteAttribute(): string
    {
        $baseText = AttendanceDayService::getStatus($this->status);
        $extraText = '';
        if ( $this->status == AttendanceDayStatusTypesUtil::DAY_OFF ) {
           $extraText = ' (' . __t($this->day_off_title) . ')';
        }
        if (in_array($this->status, [
                AttendanceDayStatusTypesUtil::PRESENT,
                AttendanceDayStatusTypesUtil::SIGN_IN_ONLY,
                AttendanceDayStatusTypesUtil::SIGN_OUT_ONLY
            ]) && $this->attendance_delay) {
            $duration = CarbonInterval::minutes($this->attendance_delay)->cascade();
            $formatted = $this->formatDuration($duration);
            $baseText = sprintf(__t('Late by %s'), $formatted);
            $extraText = '';
        }
        if ( $this->status == AttendanceDayStatusTypesUtil::LEAVE && $this->leaveType ) {
           $extraText = " (" . $this->leave_count . " ". ucfirst(__t($this->leaveType->name)).")";
        }
        return '<p>' . $baseText . $extraText . '</p>';
    }

    public function getSignOutNoteAttribute()
    {
        $baseText= '-';
        if ( $this->status == AttendanceDayStatusTypesUtil::PRESENT && $this->early_leave ) {
            $duration = CarbonInterval::minutes($this->early_leave)->cascade();
            $formatted = $this->formatDuration($duration);
            $baseText =  sprintf(__t("Early by %s") , $formatted);
        }
        if ( $this->status == AttendanceDayStatusTypesUtil::PRESENT && !$this->early_leave ) {
            $baseText =  __t('On Time');
        }
        return "<p>$baseText</p>";
    }

    private function formatDuration($duration, string $color = 'red'): string
    {
        $hours = $duration->hours . __t('h');
        $minutes = $duration->minutes . __t('m');
        return sprintf('<span style="color:%s">%s %s</span>', $color, $hours, $minutes);
    }

}
