<?php

namespace App\Models;

use App\Models\Commissions\Commission;
use App\Models\Traits\HasCustomData;
use App\Utils\CreditChargeSuspensionTypeUtil;
use Izam\Daftra\Common\Utils\ElectronicInvoicesActionsUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\EntityAppDataKeysUtil;

class Invoice extends BaseModel
{
    use HasCustomData;
    public $customDataTable = 'invoices_custom_data';
    /**
     * @var array
     */
    protected $guarded = [];

    public $timestamps = ['created'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function creditCharges()
    {
        return $this->hasMany(CreditCharge::class, 'invoice_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function membershipRenewals()
    {
        return $this->hasMany(MembershipRenewal::class, 'invoice_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function creditChargesWithAutomaticSuspension()
    {
        return $this->creditCharges()->where('suspension_type', '=', CreditChargeSuspensionTypeUtil::AUTOMATIC);
    }


    public function invoice_items()
    {
        return $this->invoiceItems();
    }
    public function invoiceItems() {
        return $this->hasMany(InvoiceItem::class, 'invoice_id');
    }

    public function taxes() {
        return $this->hasMany(InvoiceTax::class);
    }

    public function shipping_tax()
    {
        return $this->belongsTo(Tax::class, 'shipping_tax_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function products()
    {
        return $this->belongsToMany(
            Product::class,
            'invoice_items',
            'invoice_id',
            'product_id')
            ->withPivot(['quantity', 'subtotal']);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Model|\Illuminate\Database\Eloquent\Relations\HasMany|object|null
     */
    public function installmentAgreement()
    {
        return $this->hasMany(InvoiceInstallmentAgreement::class, 'invoice_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function packageServices()
    {
        return $this->products()->where('source_type', 'package');
    }

    public function commission()
    {
        return $this->hasMany(Commission::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function sales_person() {
        return $this->belongsTo(Staff::class, 'sales_person_id');
    }

    public function app_entity_data()
    {
        return $this->hasMany(EntityAppData::class, 'entity_id')->where('entity_key', EntityKeyTypesUtil::INVOICE_ENTITY_KEY);
    }

    public function app_entity_data_eta()
    {
        return $this->app_entity_data()->where('app_key', EntityAppDataKeysUtil::ELECTRONIC_INVOICE);
    }

    public function app_entity_get_data()
    {
        return $this->hasOne(EntityAppData::class, 'entity_id')->where('action_key', ElectronicInvoicesActionsUtil::GET_DOCUMENT)->where( 'entity_key' , \App\Utils\EntityKeyTypesUtil::INVOICE_ENTITY_KEY);
    }

    public function app_entity_submit_data()
    {
        return $this->app_entity_data_eta()->where('action_key', ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT)->first();
    }

    public function app_entity_error_data()
    {
        return $this->app_entity_data_eta()->where('action_key', ElectronicInvoicesActionsUtil::ERROR)->first();
    }


    public function originalInvoice()
    {
        return $this->belongsTo(Invoice::class, 'subscription_id');
    }

    public function branch() {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the related invoices (advance payments from sales invoices)
     */
    public function relatedInvoices()
    {
        return $this->belongsToMany(Invoice::class, 'related_invoices', 'invoice_id', 'related_invoice_id')
            ->withPivot('id','invoice_id', 'related_invoice_id', 'amount', 'created');
    }

    /**
     *  Get the reverse related invoices (sales invoices from advance payments)
     */
    public function relatedInvoicesReverse()
    {
        return $this->belongsToMany(Invoice::class, 'related_invoices', 'related_invoice_id', 'invoice_id')
            ->withPivot('invoice_id', 'related_invoice_id', 'amount');
    }

    public function staff()
    {
        return $this->belongsTo(Staff::class);
    }

}
