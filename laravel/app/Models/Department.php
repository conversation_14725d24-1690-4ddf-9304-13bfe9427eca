<?php
namespace App\Models;

use App\Models\Traits\HasCustomData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Department extends BaseBranchesModel
{
    use SoftDeletes, HasCustomData, HasFactory;

    public $customDataTable = 'departments_custom_data';

    protected $fillable = [
        'name', 'description', 'abbr', 'active', 'manager_id'
    ];

    function employees()
    {
        return $this->belongsToMany('App\Models\Staff', 'staff_info', 'department_id', 'staff_id');
    }

    function designations()
    {
        return $this->hasMany('App\Models\Designation');
    }
    
    function getDesignationsCountAttribute()
    {
        return $this->designations->count();
    }

    function staffs(){
        return $this->manager();
    }

    function staff_info(){
        return $this->belongsTo(StaffInfo::class, 'manager_id','staff_id')->withTrashed();
    }

    function getEmployeesCountAttribute()
    {
        return $this->employees->count();

    }

    function manager()
    {
        return $this->belongsTo('App\Models\Staff', 'manager_id')->withTrashed();
    }

    function managers()
    {
        return $this->belongsToMany('App\Models\Staff','department_managers', 'department_id','manager_id')->withTrashed();
    }


    function staffInfo()
    {
        return $this->hasMany('App\Models\StaffInfo');
    }
}
