<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class StaffJob extends BaseModel
{
    use HasFactory;

    protected $table = 'staff_job';
    protected $guarded = [];
    protected $primaryKey = 'staff_id';

    public function staffs()
    {
        return $this->belongsTo('App\Models\Staff')->withTrashed();
    }

    public function employmentLevel()
    {
        return $this->belongsTo('App\Models\EmploymentLevel');
    }

    public function designation()
    {
        return $this->belongsTo('App\Models\Designation');
    }

    public function employmentType()
    {
        return $this->belongsTo('App\Models\EmploymentType');
    }

    public function department()
    {
        return $this->belongsTo('App\Models\Department');
    }
}
