<?php
namespace App\Models;

use App\Models\InvoiceItem;

use App\Models\Traits\HasCustomData;

class FollowUpReminder extends BaseModel
{
    use HasCustomData;
    protected $fillable = array (
        'item_type',
        'item_id',
        'staff_id',
        'date',
        'end_date',
        'body',
        'status',
        'partner_id',
        'partner_type',
        'share_with_partner'
    );
    //
    public function note(){
        return $this->belongsTo(Post::class, 'post_id')->where('item_type', 1);
    }

    public function client(){
        return $this->belongsTo(Client::class, 'partner_id');
    }

    public function bookingItem(){
        return $this->belongsTo(InvoiceItem::class, 'item_id')->with('booking');
    }
}
