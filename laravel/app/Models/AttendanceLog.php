<?php

namespace App\Models;

use App\Utils\AttendanceLogSourceTypeUtil;

use App\Services\AttendanceRestrictionApplier;

class AttendanceLog extends BaseModel
{
    protected $fillable = [
        'staff_id',
        'time',
        'staff_machine_id',
        'session_id',
        'status',
        'source_id',
        'source_name',
        'source_type',
        'source_method',
        'attendance_restriction_log_id',
        'created'
    ];

    function attendance_log_staff_relation()
    {
        return $this->belongsTo('App\Models\Staff', 'staff_id')->withTrashed()->withDefault(function(Staff $staff, AttendanceLog $attendanceLog){
            if($attendanceLog->source_type == AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_MACHINE){
                $staffName = '(' . __t('User Not Mapped') . ')';
                $mapping = MachineMapping::where('staff_machine_id', $attendanceLog->staff_machine_id)->where('machine_id', $attendanceLog->source_id)->first();
                if(!empty($mapping?->machine_employee_name)){
                    $staffName =  $mapping->machine_employee_name . " " . $staffName;
                }
                $staff->id = -1;
                $staff->name = $staffName;
                $staff->code = '0000';
                return $staff;
            }
        });
    }

    function attendance_log_attendance_session_relation()
    {
        return $this->belongsTo('App\Models\AttendanceSession', 'session_id');
    }

    function attendance_restriction_log(){
        return $this->belongsTo('App\Models\AttendanceRestrictionLog', 'attendance_restriction_log_id');
    }

    function department()
    {
        return $this->belongsToMany('App\Models\Department', 'staff_info', 'staff_id', 'department_id','staff_id');
    }

    function staff_info(){
        return $this->belongsTo(StaffInfo::class, 'staff_id','staff_id')->withTrashed();
    }

    public function getLocationLabel(): string|null{
        /** @var AttendanceRestrictionApplier */
        $restrictionApplier = resolve(AttendanceRestrictionApplier::class);
        $location = $restrictionApplier->getAttendanceLogLocation($this);
        return $location != false ? ($location['label']??'') : null;
    }

}
