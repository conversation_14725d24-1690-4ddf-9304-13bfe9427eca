<?php
namespace App\Models;

use Izam\Booking\Models\Booking;
use App\Modules\ElectronicInvoice\Models\SystemETAUnit;
use Izam\Daftra\Common\Utils\FollowUpReminderUtil;

class InvoiceItem extends BaseModel
{
    protected $with = ['invoice'];

    protected $guarded = [];
    public function invoice(){
        return $this->belongsTo('App\Models\Invoice');
    }

    function taxInfo1() {
        return $this->belongsTo(Tax::class, 'tax1');
    }

    function taxInfo2() {
        return $this->belongsTo(Tax::class, 'tax2');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function salesPerson()
    {
        return $this->belongsTo(Staff::class, 'sales_person_id');
    }

    public function eta_template()
    {
        $id = -1;
        if (!empty($this->product)) {
            $id = $this->product->unit_template_id;
        }
        return $this->belongsTo(SystemETAUnit::class,'unit_factor_id', 'system_unit')->where('system_eta_units.template', $id);
    }

    public function booking()
    {
        return $this->belongsTo(Booking::class, 'invoice_id');
    }

    public function bookingAppointment()
    {
        return $this->hasOne(FollowUpReminder::class, 'item_id')->where('item_type', FollowUpReminderUtil::BOOKING_ITEM_TYPE);
    }
}
