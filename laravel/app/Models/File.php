<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;
use Izam\Attachment\Models\EntityAttachment;
use Izam\Aws\Aws;
use Izam\Logging\Service\RollbarLogService;

class File extends BaseModel
{
    use HasFactory;

    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    protected $fillable = array(
        'name',
        'path',
        'file_size',
        'last_access',
        'storage_service_provider',
        'mime_type',
        'entity_key',
        'is_temp',
    );

    public function getUrlAttribute()
    {
        if ($this->path) {
            return Storage::temporaryUrl(
                $this->path, Carbon::now()->addMinutes(env('AWS_LIFE_TIME', 1)),
                ['ResponseContentDisposition' => 'attachment; filename="' . utf8_encode($this->name) . '"']
            );
        }
    }

    public function getPermanentUrlAttribute()
    {
        if ($this->path) {
            $aws = new Aws(); 
            $fileDetails = ['entity'=> $this->entity, 'path' => $this->path, 'mime_type' => $this->mime_type, 'name' => $this->name ];
            RollbarLogService::logTempFilesToRollbar($this->is_temp,$fileDetails);

            return Storage::temporaryUrl(
                $this->path, Carbon::now()->addMinutes(env('AWS_LIFE_TIME', 12960000)),
                ['ResponseContentDisposition' => 'attachment; filename="' .  utf8_encode(str_replace(' ', '-', $this->name)) . '"']
            );
        }
    }

    public function getName()
    {
        return $this->name;
    }

    public function entityAttachmnet(){
        return $this->hasOne( EntityAttachment::class,'file_id');
    }
    public static function boot() {
        parent::boot();
        static::deleting(function($file) {
            $file->entityAttachmnet()->delete();
        });
    }
}

