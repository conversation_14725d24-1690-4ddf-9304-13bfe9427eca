<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\StaffInfo;
use App\Models\Staff;

class EmploymentLevel extends BaseModel
{
	use SoftDeletes, HasFactory;

    protected $table = 'employment_levels';

    protected $fillable = [
        'name',
        'description',
        'active'
    ];

	public function staffs() {
        return $this->belongsToMany('App\Models\Staff', 'staff_info', 'employment_level_id', 'staff_id');
	}

	public function count() {
		return $this->staffs->count();
	}
}
