<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Izam\Attachment\Models\File;
use Izam\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\SoftDeletes;


class EntityAttachment extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $connection = 'site';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'entity_id',
        'entity_key',
        'file_id',
        'sort_index'
    ];
    protected $table = "entity_attachments";

    protected $dates = ['deleted_at'];

    public $timestamps = false;

    public function files(){
        return $this->belongsTo(File::class,'file_id');
    }

}
