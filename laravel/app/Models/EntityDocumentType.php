<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class EntityDocumentType extends BaseModel
{
    use HasFactory;

    protected $table = 'entity_document_types';

    protected $fillable = [
        'entity_key',
        'name',
        'is_required',
        'is_expirable',
        'display_order',
        'created',
        'modified',
    ];

    public function documents() {
        return $this->hasMany('App\Models\EntityDocument', 'entity_document_type_id', 'id');
    }
}
