<?php

namespace App\Models;

use App\Utils\EntityKeyTypesUtil;

class QuotationRequest extends BaseBranchesModel
{

    protected $table = 'quotation_requests';
    protected $guarded = [];

    public function purchase_quotations()
    {
        return $this->morphMany(PurchaseQuotation::class, 'source');
    }

    public function purchase_requests()
    {
        return $this->belongsTo(PurchaseRequest::class, 'purchase_request_id');
    }

    public function items() {
        return $this->hasMany(QuotationRequestItems::class, 'reference_id');
    }

    public function suppliers() {
        return $this->hasMany(QuotationRequestSuppliers::class, 'quotation_request_id');
    }

}
