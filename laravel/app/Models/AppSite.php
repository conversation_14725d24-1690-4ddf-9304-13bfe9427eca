<?php

namespace App\Models;

/** @todo remove this and use the one in the package */
class AppSite extends BaseModel
{
    protected $guarded = [];
    protected $connection = 'mysql';
    protected $table = 'app_site';

    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    public function app()
    {
        return $this->belongsTo(App::class);
    }

    public function settings()
    {
        return $this->hasOne(AppSiteSetting::class, 'app_site_id', 'id');
    }
}
