<?php

namespace App\Models;

use App\Models\Staff;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\EmploymentLevel;
use App\Models\Designation;
use App\Models\EmploymentType;
use App\Models\Department;
use Illuminate\Database\Eloquent\SoftDeletes;


class StaffInfo extends BaseModel
{
    use SoftDeletes, HasFactory;

	protected $table = 'staff_info';
	protected $guarded = [];
    protected $primaryKey = 'staff_id';

	public function staff() {
		return $this->belongsTo(Staff::class)->withTrashed();
	}

	public function employmentLevel() {
		return $this->belongsTo(EmploymentLevel::class);
	}

	public function designation() {
		return $this->belongsTo(Designation::class);
	}

	public function employmentType() {
		return $this->belongsTo(EmploymentType::class);
	}

	public function department() {
		return $this->belongsTo(Department::class);
	}

	public function attendanceShift() {
		return $this->belongsTo(Shift::class, 'attendance_shift_id');
	}

    public function secondaryShift() {
        return $this->belongsTo(Shift::class, 'secondary_shift_id');
    }

	public function leavePolicy() {
		return $this->belongsTo(LeavePolicy::class);
	}
	
	function direct_manager()
    {
         return $this->belongsTo('App\Models\Staff', 'direct_manager_id');

    }
}
