<?php

namespace App\Models;

use App\Models\Traits\HasCustomData;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Izam\Booking\Models\ServiceForm;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class Product extends BaseBranchesModel
{
    static $MorphClassName = '';

    use HasCustomData;
    //
    public $customDataTable = 'products_custom_data';

    protected $branchIdField = 'products.branch_id';

    /**
     * @var string table name will deal with.
     */
    protected $table = 'products';

    protected $guarded = [];

    function getMorphClass()
    {
        return self::$MorphClassName;
    }

    public function categories()
    {
        self::$MorphClassName = '1';
        return $this->morphToMany(Category::class, 'item', 'items_categories');
    }

    public function categories_names()
    {
        $names = $this->categories->pluck('name')->toArray();
        return implode(', ', $names);
    }

    /**
     * @return HasOne
     */
    public function package()
    {
        return $this->hasOne(Package::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function commissionRuleSubItem()
    {
        return $this->morphMany(Product::class,'relatedItem');
    }

    public function getSelectNameAttribute(){
        return "{$this->name} #{$this->product_code}";
    }

    public function unitTemplate() {
        return $this->belongsTo(UnitTemplate::class);
    }

	public function bundleProducts() {
		if (!$this->isBundle()) {
			return null;
		}
		return $this->hasMany(ProductBundle::class, 'bundle_product_id');
	}

	public function isBundle() {
		return $this->type == 3;
	}

	function average_price() {
		if (!$this->isBundle()) {
			return $this->average_price;
		}
		$bundleProducts = $this->bundleProducts;
		$totalCost = 0;
		foreach ($bundleProducts as $bundleProduct) {
            if($bundleProduct->product){
                $totalCost += $bundleProduct['quantity'] * $bundleProduct->product->average_price;
            }
		}
		return $totalCost;
	}

    public function attributeOptions()
    {
        return $this->hasMany(ProductAttributeOption::class, 'product_id');
    }

    public function productItemBarcode()
    {
        return $this->hasMany(ProductItemBarcode::class,  'product_id','id');
    }

    public function boms()
    {
        return $this->hasMany(Bom::class, 'product_id');
    }

    public function activeBoms(){
        return $this->boms()->where('is_active',1);
    }

    public function getBranchIdField()
    {
        return $this->branchIdField;
    }

    public function assignedStaffs()
    {
        return $this->belongsToMany(Staff::class, 'staff_services', 'service_id', 'staff_id');
    }

    public function getAssignedStaffIds()
    {
        return $this->assignedStaffs()->pluck('staff_id')->toArray();
    }

    public function serviceForm(){
        return $this->hasOne(ServiceForm::class, 'service_id');
    }

    public function attachments()
    {
        return $this->belongsToMany(File::class, 'entity_attachments', 'entity_id', 'file_id')->where('entity_attachments.entity_key', '=', EntityKeyTypesUtil::PRODUCT_ENTITY_KEY)->whereNull('entity_attachments.deleted_at');
    }

    public function getDefaultAttachment()
    {
        return $this->attachments()->where('is_default', 1)->first();
    }
}
