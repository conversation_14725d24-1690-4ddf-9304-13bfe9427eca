<?php

namespace App\Models;

use App\Utils\AttendanceMachineMappingUtil;

/**
 * Class MachineMapping
 * @package App\Models
 */
class MachineMapping extends BaseModel
{
    /**
     * @var array
     */
    protected $guarded = [];

    protected $appends = ['employee_name'];

    /**
     * @var string
     */
    protected $table = 'machine_employee_mappings';

    public $timestamps = false;

    public function employee()
    {
        return $this->belongsTo('App\Models\Staff', 'staff_id')->withDefault(function () {
            $staff = Staff::withTrashed()->find($this->staff_id);
            if ($staff) {
                $staff->name .= ' ('.__t('Deleted').')';
            } else {
                $staff = new Staff();
                $staff->name = __t("Not exist");
                $staff->id = '';
            }
            return $staff;
        });
    }

    public function getEmployeeNameAttribute()
    {
        if($this->staff_id == AttendanceMachineMappingUtil::UNKNOWN) {
            return __t('Unknown');
        } 
        elseif($this->staff_id == AttendanceMachineMappingUtil::IGNORED) {
            return __t('Ignored');
        } elseif($this->staff_id) {
            return $this->employee->full_name;
        }

        return '';
    }

    public function machine()
    {
        return $this->belongsTo('App\Models\Machine', 'machine_id');
    }
}
