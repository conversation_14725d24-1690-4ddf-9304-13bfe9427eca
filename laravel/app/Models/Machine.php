<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Machine Class machine model
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 */
class Machine extends BaseModel
{
    use SoftDeletes;
    protected $fillable = [
        'name',
        'machine_type_key',
        'serial_number',
        'host',
        'port',
        'communication_key',
        'status',
        'last_sign_date',
        'last_pull_date',
        'uuid',
    ];

    public function employees()
    {
        return $this->belongsToMany('App\Models\Staff', 'machine_employee_mappings', 'machine_id', 'staff_id');
    }
    public function machineType()
    {
        $this->connection = 'mysql';
        return $this->belongsTo(MachineType::class, 'machine_type_key');
    }
}
