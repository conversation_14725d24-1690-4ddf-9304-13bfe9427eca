<?php

namespace App\Models;

use App\Models\Treasury;
use Illuminate\Support\Facades\DB;

class BankTransaction extends BaseModel
{
    static $MorphClassName = '';
    protected $table = 'bank_transactions';
    protected $fillable = [
        'bank_id',
        'date',
        'deposit_amount',
        'withdraw_amount',
        'amount',
        'reference_id',
        'description',
        'type',
        'status',
        'created',
        'modified',
        'journal_transaction_id',
    ];


    function bank()
    {
        return $this->belongsTo(Treasury::class,'bank_id','id');
    }

    function systemTransactions() {
        return $this->hasMany(JournalTransaction::class, 'bank_transaction_id');
    }

    function systemTransaction()
    {
        return $this->belongsTo(JournalTransaction::class,'journal_transaction_id','id');
    }
}


