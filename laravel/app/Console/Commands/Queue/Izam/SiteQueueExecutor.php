<?php


namespace App\Console\Commands\Queue\Izam;


use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\Common\Queue\EventPlatformUtil;
use Izam\Daftra\Queue\Services\QueueServerService;

class SiteQueueExecutor extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'izam-queue:run-site {site_id} {server_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gets Sites that has events and run 2 processes for each one of them';

    /**
     * @var QueueServerService $queueService
     */
    private $queueService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(QueueServerService $queueService)
    {
        parent::__construct();
        $this->queueService = $queueService;
        DB::disconnect('mysql');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(): void
    {
        $siteId = $this->argument('site_id');
        $MyPidFile = "/var/run/php/run-site-" . $siteId . ".pid";
        if (file_exists($MyPidFile)) {
            $oldPid = (int)file_get_contents($MyPidFile);
            if (file_exists("/proc/{$oldPid}/cmdline")) {
                $cmdLine = file_get_contents("/proc/{$oldPid}/cmdline");
                if (strpos($cmdLine, "izam-queue:run-site") && strpos($cmdLine, $siteId)) {
                    echo "Site Already Processing Events \n";
                    die();
                }
            } else {
                file_put_contents($MyPidFile, getmypid());
            }
        } else {
            file_put_contents($MyPidFile, getmypid());
        }
        try {
            $serverId = $this->argument('server_id');
            if ($serverId) {
                $processCount = $this->queueService->getCurrentProcessingListenersCountPerServer($serverId);
            } else {
                $processCount = $this->queueService->getCurrentProcessingListenersCount();
            }
            if ($processCount > config('event_queue.max_process_count')) {
                unlink($MyPidFile);
                exit;
            }
            $listeners = $this->queueService->getListenersWillBeRun($siteId);
            while ($listeners->count()) {
                $listener = $listeners->first();
                $event = $listener->action_event;
                if (empty($event)) {
                    $this->queueService->killProcess($listener, "There are is no event");
                    continue;
                }
                $this->queueService->processEvent($event);
                $fullVersion = explode('.', PHP_VERSION);
                $version = $fullVersion[0] . '.' . $fullVersion[1];
                if ($listener->platform == EventPlatformUtil::LARAVEL) {
                    exec("php{$version} /var/www/html/" . LARAVEL_DIR . "/artisan laravel-listener:execute {$listener->id} {$serverId}", $output, $status);
                } else {
                    exec(
                        "/usr/bin/php{$version} /var/www/html/" . CAKE_DIR . "/webroot/cron.php /cron/cake_listener_execute/{$listener->id}/{$serverId}",
                        $output,
                        $status
                    );
                }
                $listeners = $this->queueService->getListenersWillBeRun($siteId);
                $this->queueService->finishEvent($event);

                if ($serverId) {
                    $processCount = $this->queueService->getCurrentProcessingListenersCountPerServer($serverId);
                } else {
                    $processCount = $this->queueService->getCurrentProcessingListenersCount();
                }
                if ($processCount > config('event_queue.max_process_count')) {
                    unlink($MyPidFile);
                    exit;
                }
            }
        } catch (\Throwable $exception) {
            Log::error("[SiteQueueExecutor] Error : " . $exception->getMessage());
        } finally {
            unlink($MyPidFile);
        }
    }
}
