<?php

namespace App\Jobs;

use App\Modules\ElectronicInvoice\Traits\CommonRequest;
use App\Repositories\EntityAppDataRepository;
use App\Utils\EntityKeyTypesUtil;
use Carbon\Carbon;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Bus\Queueable;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Response;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\Common\ETA\ErrorMapper;
use Izam\Daftra\Common\Utils\ElectronicInvoicesActionsUtil;
use Izam\Daftra\Common\Utils\EntityAppDataKeysUtil;

class SendETARequest
{
    use  CommonRequest;


    public $tries = 1;
    private  EntityAppDataRepository $entityAppDataRepo;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(private $url, protected $params, private $extra_payload, private $id)
    {
        $this->connection = 'sync';
    }

    public function handle()
    {
        ini_set('serialize_precision', -1);
        if (!isset($this->params['receipts'])) {
            $access_token = $this->logIn();
            $isReceipt = false;
        } else {
            $access_token = $this->logInPos();
            $isReceipt = true;
        }

        $payload['headers'] = ['Content-Type' => 'application/json'];
        $payload['headers']['Authorization'] = 'Bearer ' . $access_token;
        $payload['body'] = json_encode($this->params, JSON_UNESCAPED_UNICODE);
        $payload = array_merge($payload, $this->extra_payload);

        try {
            $response = $this->sendRequest($this->url, 'POST', $payload);
            $status_code = $response->getStatusCode();
            $body = json_decode($response->getBody()->getContents(), true);
            if (empty($body))  {
                $this->extractErrorsAndInsert([
                    ['target' => 'portal', 'details' => (string) $response->getBody()]
                ]);
                $this->deleteCritria(ElectronicInvoicesActionsUtil::SENT_DOCUMENT);
            }

            if (!empty($body['rejectedDocuments']) || empty($body['acceptedDocuments'][0]['uuid'])) {
                $this->extractErrorsAndInsert(array_column($body['rejectedDocuments'], 'error'));
                $this->deleteCritria(ElectronicInvoicesActionsUtil::SENT_DOCUMENT);
            }

            if (in_array($status_code, [Response::HTTP_ACCEPTED, Response::HTTP_OK]) && !empty($body) ) {
                $this->insertSubmit($body);
                $this->deleteCritria(ElectronicInvoicesActionsUtil::SENT_DOCUMENT);
                GetETADocument::dispatch( $body['acceptedDocuments'][0]['uuid'], $this->id, $isReceipt , $body['submissionId'])->delay(Carbon::now()->addSeconds(1));
                return true;
            }
            //Log::error('ETA something wrong', ['body' => $body, 'response' => json_encode($response), 'response_status' => $status_code, 'type' => 'not valid 200 response']);
        } catch (ClientException $exception) {

            $this->deleteCritria(ElectronicInvoicesActionsUtil::SENT_DOCUMENT);
            $this->deleteCritria(ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT);
            $error_response = json_decode($exception->getResponse()->getBody(), true);
            if (isset($error_response['error'])) {
                $this->insertError( [$error_response['error']]);
            } else {
                $this->insertError( [$exception->getMessage()]);
            }
            //Log::info('Login ETA Invoice Client Http Exception Response '.$exception->getMessage());
        } catch (Exception $exception) {
            $this->deleteCritria(ElectronicInvoicesActionsUtil::SENT_DOCUMENT);
            $this->deleteCritria(ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT);
            $this->insertError( [ __t('Unknown error happened when try to sent electronic invoice from tax invoice portal, please try later')]);
            //Log::info('Client Http Exception ETA' . $exception->getMessage());
        }
        return false;
    }

    public function insertSubmit($body)
    {
        $this->deleteErrorsAppEntityData();
        $this->insertToAppEntityData(json_encode($body), ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT);
    }


    public function failed(\Exception $exception)
    {
        $this->deleteCritria(ElectronicInvoicesActionsUtil::SENT_DOCUMENT);
        $this->deleteCritria(ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT);

    }
}
