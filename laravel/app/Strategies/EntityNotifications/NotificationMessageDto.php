<?php

namespace App\Strategies\EntityNotifications;

/**
 * Class NotificationMessageDto
 *
 * Represents the full structure of a push notification message to be sent through FCM.
 * It includes the core notification content (`title`, `body`), the device token, and additional metadata (`data`).
 *
 * This DTO simplifies the message-building process for FCM push notifications by encapsulating all required
 * fields and formatting logic.
 *
 * @package App\Strategies\EntityNotifications
 */
class NotificationMessageDto implements \JsonSerializable
{
     /**
     * NotificationMessageDto constructor.
     *
     * @param NotificationDto $notification The notification content (title and body).
     * @param string $token The FCM token of the device to receive the notification.
     * @param array $data Additional key-value data to include in the payload.
     */
    public function __construct(
        private NotificationDto $notification,
        private $token,
        private $data,
    ) {
    }

    /**
     * Converts the object into a JSON-serializable structure.
     *
     * This will be used when encoding the message into a JSON format to send to a push service like FCM.
     *
     * @return array{
     *     notification: array{title: string, body: string},
     *     token: string,
     *     data: array
     * }
     */
    public function jsonSerialize(): mixed
    {
        return [
            'notification' => $this->notification->jsonSerialize(),
            'token' => $this->token,
            'data' => $this->data
        ];
    }

}
