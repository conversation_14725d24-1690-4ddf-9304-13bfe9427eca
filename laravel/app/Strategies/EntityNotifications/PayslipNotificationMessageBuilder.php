<?php

namespace App\Strategies\EntityNotifications;

use Carbon\Carbon;

/**
 * This class is responsible for building notification messages for different actions related to leave applications.
 * It implements the NotificationMessageBuilder interface.
 */
class PayslipNotificationMessageBuilder implements NotificationMessageBuilder
{

    CONST ACTION_APPROVE = 'approve';
    CONST ACTION_UNDO_APPROVE = 'undo_approve';


    public function __construct(private $action, private $data){
    }

    /**
     * Returns a notification message based on the action and data provided.
     * Uses a match statement to determine which method to call to build the notification message.
     *
     * @param string $token
     * @param mixed $user
     * @return NotificationMessageDto
     */
    public function getMessage($token, $user): NotificationMessageDto
    {
        return match ($this->action) {
            self::ACTION_APPROVE => $this->approveActionNotificationData($this->data, $token),
            default => throw new \Exception('Action not found'),            
        };
    }
    /**
     * Builds a notification message for an approved leave application.
     *
     * @param array $data
     * @param string $token
     * @return NotificationMessageDto
     */
    public function approveActionNotificationData($data, $token): NotificationMessageDto{
        app()->getLocale() == "ara" ? Carbon::setlocale('ar') : '';
        $month = (new Carbon($data['start_date']))->translatedFormat("M, Y");
        $notificationDto = new NotificationDto(
            __t("New Payslip Available"),
            sprintf(__t("Your payslip for %s is now available. Tap to view details."), $month),
        );
        return new NotificationMessageDto($notificationDto, $token, ['payslip_id'=> (string)$data['id']]);
    }

}
