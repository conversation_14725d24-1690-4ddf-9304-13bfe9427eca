<?php

namespace App\Strategies\EntityNotifications;

use App\Notifications\AddLeaveApplicationNotification;
use App\Requests\Notification\NotificationDataRequest;
use App\Requests\Notification\NotificationRequest;
use App\Services\NotificationService;
use App\Notifications\ApproveLeaveApplicationNotification;
use App\Notifications\RejectLeaveApplicationNotification;
use App\Services\DeviceAppNotificationService;
use App\Services\LeaveApplicationService;
use App\Services\LeaveAppNotificationService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\FcmDevicesAppUtil;
use Illuminate\Database\Eloquent\Collection;
use Izam\Daftra\Common\Utils\LeaveApplicationStatusUtil;

class LeaveApplicationNotificationStrategy implements NotificationPreferenceOnInterface, EntityCreationalNotificationsInterface, EntityActionsNotificationsInterface
{
    protected $data;
    private $notificationService;
    private $leaveApplicationService;
    /** @var LeaveAppNotificationService */
    private $leaveAppNotificationService;
    private DeviceAppNotificationService $deviceAppNotificationService;

    public function __construct($data)
    {
        $this->data = $data;
        $this->notificationService = resolve(NotificationService::class);
        $this->leaveApplicationService = resolve(LeaveApplicationService::class);
        $this->leaveAppNotificationService = resolve(LeaveAppNotificationService::class);
        $this->deviceAppNotificationService = resolve(DeviceAppNotificationService::class);
    }

    public function preferenceIsOn($actionKey) : bool{
        $preferenceKey = FcmDevicesAppUtil::getPreferenceKeyByEntityAndAction(FcmDevicesAppUtil::LEAVE_APP, EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY, $actionKey);
        /** @var DeviceAppNotificationService */
        $deviceAppNotificationService = resolve(DeviceAppNotificationService::class);
        return $deviceAppNotificationService->preferenceIsOn($this->data['staff_id'], FcmDevicesAppUtil::LEAVE_APP, $preferenceKey);
    }
    public function onEntityCreated(): void
    {
        $notifiable = $this->leaveApplicationService->getOnCreatedNotifiableStaffs($this->data);
        $staffPreferences = $this->getStaffPreferences('create', $notifiable);
        if (!$staffPreferences->contains(true)) {
            return;
        }
        $notifiable = $notifiable->filter(
            fn($staff) => $staffPreferences[$staff->id] ?? false
        );
        if ($notifiable->isEmpty()) {
            return;
        }
        $this->buildNotificationRequest($notifiable);
        $this->runMainNotificationLogic(LeaveApplicationNotificationMessageBuilder::ACTION_CREATE, $notifiable);
    }

    public function onEntityUpdated(): void
    {
        if (!$this->preferenceIsOn('update')) {
            return;
        }
        $notifiable = $this->leaveApplicationService->getOnCreatedNotifiableStaffs($this->data);
        if ($notifiable->isEmpty()) {
            return;
        }
        $this->buildNotificationRequest($notifiable);
        $this->runMainNotificationLogic(LeaveApplicationNotificationMessageBuilder::ACTION_UPDATE, $notifiable);
    }

    public function onEntityDeleted(){
        if(!$this->preferenceIsOn('delete')) return;
        $notifiable = $this->leaveApplicationService->getOnCreatedNotifiableStaffs($this->data);
        $notificationData = new NotificationDataRequest((int)$this->data['id']);
        $notificationRequest = new NotificationRequest(
            AddLeaveApplicationNotification::class,
            $notificationData,
            $notifiable
        );

        $this->notificationService->unNotify($notificationRequest);

        $action = LeaveApplicationNotificationMessageBuilder::ACTION_DELETE;
        $this->runMainNotificationLogic($action, $notifiable);
    }

    public function onEntityApproveAction(){
        if(!$this->preferenceIsOn('approve')) return;
        $notifiable = $this->leaveApplicationService->getOnApproveNotifiableStaffs($this->data);
        $notificationData = new NotificationDataRequest((int)$this->data['id']);
        $notificationData->appendData('leave_application_id', $this->data['id']);
        $notificationData->appendData('leave_from_date', $this->data['date_from']);
        $notificationData->appendData('leave_to_date', $this->data['date_to']);
        $notificationData->appendData('employee_full_name', $this->data['staff_leave_application']['full_name']);

        $userFullName = "";
        if($this->data['leave_application_approver']){
            $userFullName =  $this->data['leave_application_approver']['full_name'];
        }else{
            $owner = getAuthOwner();
            $userFullName = $owner['first_name'] ." ".  $owner['last_name'];
        }
        $notificationData->appendData('user_full_name',$userFullName);

        $notificationType = AddLeaveApplicationNotification::class;
        $action = LeaveApplicationNotificationMessageBuilder::ACTION_CREATE;

        if($this->data['status'] == LeaveApplicationStatusUtil::Approved){
            $notificationType = ApproveLeaveApplicationNotification::class;
            $action = LeaveApplicationNotificationMessageBuilder::ACTION_APPROVE;

        }

        $notificationRequest = new NotificationRequest(
            $notificationType,
            $notificationData,
            $notifiable
        );

        $this->notificationService->notify($notificationRequest);

        $this->data['user_full_name'] = $userFullName;

        $this->runMainNotificationLogic($action, $notifiable);
    }

    public function onEntityRejectAction(){
        if(!$this->preferenceIsOn('reject')) return;
        $notifiable = $this->leaveApplicationService->getOnApproveNotifiableStaffs($this->data);
        $notificationData = new NotificationDataRequest((int)$this->data['id']);
        $notificationData->appendData('leave_application_id', $this->data['id']);
        $notificationData->appendData('leave_from_date', $this->data['date_from']);
        $notificationData->appendData('leave_to_date', $this->data['date_to']);
        $userFullName = "";
        if($this->data['leave_application_approver']){
            $userFullName =  $this->data['leave_application_approver']['full_name'];
        }else{
            $owner = getAuthOwner();
            $userFullName = $owner['first_name'] ." ".  $owner['last_name'];
        }
        $notificationData->appendData('user_full_name',$userFullName);


        $notificationRequest = new NotificationRequest(
            RejectLeaveApplicationNotification::class,
            $notificationData,
            $notifiable
        );

        $this->notificationService->notify($notificationRequest);

        $this->data['user_full_name'] = $userFullName;

        $action = LeaveApplicationNotificationMessageBuilder::ACTION_REJECT;
        $this->runMainNotificationLogic($action, $notifiable);
    }

    public function onEntityUndoApprove(){
        if(!$this->preferenceIsOn('undo_approve')) return;
        $notifiable = $this->leaveApplicationService->getOnApproveNotifiableStaffs($this->data);
        $notificationData = new NotificationDataRequest((int)$this->data['id']);
        $notificationType = AddLeaveApplicationNotification::class;
        $revertedFromFinalApproval = isset($this->data['undo_from_final_approve']) && $this->data['undo_from_final_approve'];
        if($revertedFromFinalApproval){
            $notificationType = ApproveLeaveApplicationNotification::class;
        }
        $notificationRequest = new NotificationRequest(
            $notificationType,
            $notificationData,
            $notifiable
        );

        $this->notificationService->unNotify($notificationRequest);

        if($revertedFromFinalApproval){
            if($this->data['leave_application_approver']??[]){
                $this->data['user_full_name'] =  $this->data['leave_application_approver']['full_name'];
            }else{
                $this->data['user_full_name'] = $this->getLoggedInUserName();
            }
            $action = LeaveApplicationNotificationMessageBuilder::ACTION_UNDO_APPROVE;
            $this->runMainNotificationLogic($action, $notifiable);
        }
    }

    public function onEntityUndoReject(){
        if(!$this->preferenceIsOn('undo_reject')) return;
        $notifiable = $this->leaveApplicationService->getOnFinalApproveOrRejectedNotifiableStaffs($this->data);
        $notificationData = new NotificationDataRequest((int)$this->data['id']);

        $notificationRequest = new NotificationRequest(
            RejectLeaveApplicationNotification::class,
            $notificationData,
            $notifiable
        );

        $this->notificationService->unNotify($notificationRequest);
        if($this->data['leave_application_approver']){
            $this->data['user_full_name'] =  $this->data['leave_application_approver']['full_name'];
        }else{
            $this->data['user_full_name'] = $this->getLoggedInUserName();
        }
        $action = LeaveApplicationNotificationMessageBuilder::ACTION_UNDO_REJECT;
        $this->runMainNotificationLogic($action, $notifiable);
    }

    private function getLoggedInUserName(){
        $loggedInUser = getAuthStaff();
        $userFullName = '';
        if($loggedInUser){
            $userFullName = $loggedInUser['full_name'];
        }else{
            $loggedInUser = getAuthOwner();
            $userFullName = $loggedInUser['first_name'] ." ".  $loggedInUser['last_name'];
        }
        return $userFullName;
    }

    public function runMainNotificationLogic($action, $notifiable){
        $messageBuilder = new LeaveApplicationNotificationMessageBuilder($action, $this->data);
        $allMessagesGetter = new NotificationLanguageSetterDecorator($messageBuilder);
        $notificationDataList = $allMessagesGetter->getMessages($notifiable, FcmDevicesAppUtil::LEAVE_APP);
        $this->leaveAppNotificationService->pushNotification($notificationDataList);
    }

    private function getStaffPreferences(string $actionKey, $notifiable): \Illuminate\Support\Collection
    {
        $preferenceKey = FcmDevicesAppUtil::getPreferenceKeyByEntityAndAction(
            FcmDevicesAppUtil::LEAVE_APP,
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY,
            $actionKey
        );
        return collect($notifiable)->mapWithKeys(function ($staff) use ($preferenceKey) {
            return [
                $staff->id => $this->deviceAppNotificationService
                    ->preferenceIsOn($staff->id, FcmDevicesAppUtil::LEAVE_APP, $preferenceKey)
            ];
        });
    }

    private function buildNotificationData(): NotificationDataRequest
    {
        $notificationData = new NotificationDataRequest((int)$this->data['id']);
        $notificationData->appendData('leave_application_id', $this->data['id'])
            ->appendData('employee_full_name', $this->data['staff_leave_application']['full_name'])
            ->appendData('employee_id', $this->data['staff_id'])
            ->appendData('leave_from_date', $this->data['date_from'])
            ->appendData('leave_to_date', $this->data['date_to']);
        return $notificationData;
    }

    private function buildNotificationRequest(Collection $notifiable): void
    {
        $notificationData = $this->buildNotificationData();
        $notificationRequest = new NotificationRequest(
            AddLeaveApplicationNotification::class,
            $notificationData,
            $notifiable
        );
        $this->notificationService->notify($notificationRequest);
    }

}
