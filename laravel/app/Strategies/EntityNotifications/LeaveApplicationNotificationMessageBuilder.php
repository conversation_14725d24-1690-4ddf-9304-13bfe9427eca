<?php

namespace App\Strategies\EntityNotifications;

use App\Services\LeaveApplicationService;

/**
 * This class is responsible for building notification messages for different actions related to leave applications.
 * It implements the NotificationMessageBuilder interface.
 */
class LeaveApplicationNotificationMessageBuilder implements NotificationMessageBuilder
{

    CONST ACTION_CREATE = 'create';
    CONST ACTION_UPDATE = 'update';
    CONST ACTION_DELETE = 'delete';
    CONST ACTION_REJECT = 'reject';
    CONST ACTION_APPROVE = 'approve';
    CONST ACTION_UNDO_APPROVE = 'undo_approve';
    CONST ACTION_UNDO_REJECT = 'undo_reject';

    private LeaveApplicationService $leaveApplicationService;

    public function __construct(private $action, private $data){
        $this->leaveApplicationService = resolve(LeaveApplicationService::class);
    }

    /**
     * Returns a notification message based on the action and data provided.
     * Uses a match statement to determine which method to call to build the notification message.
     *
     * @param string $token
     * @param mixed $user
     * @return NotificationMessageDto
     */
    public function getMessage($token, $user): NotificationMessageDto
    {
        return match ($this->action) {
            self::ACTION_CREATE => $this->createActionNotificationData($this->data, $token),
            self::ACTION_UPDATE => $this->updateActionNotificationData($this->data, $token),
            self::ACTION_DELETE => $this->deleteActionNotificationData($this->data, $token),
            self::ACTION_APPROVE => $this->approveActionNotificationData($this->data, $token),
            self::ACTION_REJECT => $this->rejectActionNotificationData($this->data, $token),
            self::ACTION_UNDO_APPROVE => $this->undoApproveActionNotificationData($this->data, $token),
            self::ACTION_UNDO_REJECT => $this->undoRejectActionNotificationData($this->data, $token),
            default => throw new \Exception('Action not found'),            
        };
    }

    /**
     * Builds a notification message for a new leave application.
     *
     * @param array $data
     * @param string $token
     * @return NotificationMessageDto
     */
    public function createActionNotificationData($data, $token) : NotificationMessageDto {
        $notificationDto = new NotificationDto(
            __t("New Leave Application"),
            $this->leaveApplicationService->getOnSaveNotificationBody($data['staff_leave_application']['full_name'], $data['date_from'], $data['date_to']),
        );
        return new NotificationMessageDto($notificationDto, $token, ['leave_application_id'=> (string)$data['id']]);
    }

    /**
     * Builds a notification message for an updated leave application.
     *
     * @param array $data
     * @param string $token
     * @return NotificationMessageDto
     */
    public function updateActionNotificationData($data, $token) : NotificationMessageDto {
        $notificationDto = new NotificationDto(
            __t("Leave Application Updated"),
            $this->leaveApplicationService->getOnSaveNotificationBody($data['staff_leave_application']['full_name'], $data['date_from'], $data['date_to']),
        );
        return new NotificationMessageDto($notificationDto, $token, ['leave_application_id'=> (string)$data['id']]);
    }

    /**
     * Builds a notification message for a deleted leave application.
     *
     * @param array $data
     * @param string $token
     * @return NotificationMessageDto
     */    
    public function deleteActionNotificationData($data, $token) : NotificationMessageDto {
        $notificationDto = new NotificationDto(
            __t("Leave Application Deleted"),
            $this->leaveApplicationService->getOnDeleteNotificationBody($data['staff_leave_application']['full_name'], $data['date_from'], $data['date_to']),
        );
        return new NotificationMessageDto($notificationDto, $token, ['leave_application_id'=> (string)$data['id']]);
    }

    /**
     * Builds a notification message for an approved leave application.
     *
     * @param array $data
     * @param string $token
     * @return NotificationMessageDto
     */
    public function approveActionNotificationData($data, $token): NotificationMessageDto{
        $notificationDto = new NotificationDto(
            __t("Leave Application Approved"),
            $this->leaveApplicationService->getOnApproveNotificationBody($data['user_full_name'], $data['date_from'], $data['date_to']),
        );
        return new NotificationMessageDto($notificationDto, $token, ['leave_application_id'=> (string)$data['id']]);
    }

    public function rejectActionNotificationData($data, $token): NotificationMessageDto{
        $notificationDto = new NotificationDto(
            __t("Leave Application Rejected"),
            $this->leaveApplicationService->getOnRejectNotificationBody($data['user_full_name'], $data['date_from'], $data['date_to']),
        );
        return new NotificationMessageDto($notificationDto, $token, ['leave_application_id'=> (string)$data['id']]);        
    }

    public function undoApproveActionNotificationData($data, $token): NotificationMessageDto{
        $notificationDto = new NotificationDto(
            __t("Leave Application Approval Canceled"),
            $this->leaveApplicationService->getOnUndoApproveNotificationBody($data['user_full_name'], $data['date_from'], $data['date_to']),
        );
        return new NotificationMessageDto($notificationDto, $token, ['leave_application_id'=> (string)$data['id']]);
    }

    public function undoRejectActionNotificationData($data, $token): NotificationMessageDto{
        $notificationDto = new NotificationDto(
            __t("Leave Application Rejection Undone"),
            $this->leaveApplicationService->getOnUndoRejectNotificationBody($data['user_full_name'], $data['date_from'], $data['date_to']),
        );
        return new NotificationMessageDto($notificationDto, $token, ['leave_application_id'=> (string)$data['id']]);
    }

}
