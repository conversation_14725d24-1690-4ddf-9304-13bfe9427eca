<?php

namespace App\Strategies\EntityNotifications;

/**
 * Interface EntityActionsNotificationsInterface
 *
 * Defines a contract for handling push notifications related to specific entity actions,
 * such as approval, rejection, and their reversal (undo). Implementing classes should 
 * provide concrete logic for each action to notify the appropriate users.
 */
interface EntityActionsNotificationsInterface
{
    /**
     * Handle the logic for sending notifications when an entity is approved.
     *
     * @return void
     */
    public function onEntityApproveAction();

    /**
     * Handle the logic for sending notifications when an entity is rejected.
     *
     * @return void
     */
    public function onEntityRejectAction();

    /**
     * Handle the logic for sending notifications when an entity's approval is undone.
     *
     * @return void
     */
    public function onEntityUndoApprove();

    /**
     * Handle the logic for sending notifications when an entity's rejection is undone.
     *
     * @return void
     */
    public function onEntityUndoReject();

}
