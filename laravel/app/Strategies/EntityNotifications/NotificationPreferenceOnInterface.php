<?php

namespace App\Strategies\EntityNotifications;

/**
 * Interface NotificationPreferenceOnInterface
 *
 * Declares a contract for checking if a specific notification preference is enabled
 * for a given action key. Typically used by notification strategies to determine
 * whether to proceed with sending a notification.
 */
interface NotificationPreferenceOnInterface
{
    /**
     * Determine whether the notification preference for the given action key is enabled.
     *
     * @param string $actionKey The identifier for the notification action (e.g., 'leave_application_notifiers').
     * @return bool Returns true if the notification is enabled, false otherwise.
     */
    public function preferenceIsOn($actionKey) : bool;
}