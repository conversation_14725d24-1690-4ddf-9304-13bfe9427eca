<?php

namespace App\Strategies\EntityNotifications;


/**
 * Interface EntityCreationalNotificationsInterface
 *
 * Provides a contract for sending push notifications when an entity undergoes
 * creation, update, or deletion events. Implement this interface in entity-specific
 * notification classes to define how notifications should be handled per action.
 */
interface EntityCreationalNotificationsInterface
{
    /**
     * Handle the logic for sending notifications when an entity is created.
     *
     * @return void
     */
    public function onEntityCreated();

    /**
     * Handle the logic for sending notifications when an entity is updated.
     *
     * @return void
     */
    public function onEntityUpdated();


    /**
     * Handle the logic for sending notifications when an entity is deleted.
     *
     * @return void
     */
    public function onEntityDeleted();
}
