<?php

namespace App\Strategies\EntityNotifications;

use App\Models\Staff;
use App\Requests\Notification\NotificationDataRequest;
use App\Requests\Notification\NotificationRequest;
use App\Services\NotificationService;
use App\Notifications\ApprovePayslipNotification;
use App\Services\DeviceAppNotificationService;
use App\Services\StaffService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\FcmDevicesAppUtil;
use Illuminate\Database\Eloquent\Collection;
use Izam\Fcm\Services\FcmService;

class PayslipNotificationStrategy implements NotificationPreferenceOnInterface, EntityCreationalNotificationsInterface, EntityActionsNotificationsInterface
{
    protected $data;
    private $notificationService;
    /** @var StaffService */
    private $staffService;
    /** @var FcmService */
    private $fcmService;

    public function __construct($data)
    {
        $this->data = $data;
        $this->notificationService = resolve(NotificationService::class);
        $this->staffService = resolve(StaffService::class);
        $this->fcmService = resolve(FcmService::class);
    }

    public function preferenceIsOn($actionKey) : bool{
        $preferenceKey = FcmDevicesAppUtil::getPreferenceKeyByEntityAndAction(FcmDevicesAppUtil::LEAVE_APP, EntityKeyTypesUtil::PAYSLIP, $actionKey);
        /** @var DeviceAppNotificationService */
        $deviceAppNotificationService = resolve(DeviceAppNotificationService::class);
        return $deviceAppNotificationService->preferenceIsOn($this->data['staff_id'], FcmDevicesAppUtil::LEAVE_APP, $preferenceKey);
    }


    public function onEntityCreated($onUpdate = false){
    }

    public function onEntityUpdated(){
    }

    public function onEntityDeleted(){
    }

    public function onEntityApproveAction(){
        if(!$this->preferenceIsOn('approve')) return;
        $notifiable = Collection::make([new Staff($this->data['staff'])]);
        $notificationData = new NotificationDataRequest((int)$this->data['id']);
        $notificationData->appendData('payslip_id', $this->data['id']);
        $notificationData->appendData('start_date', $this->data['start_date']);
        $notificationData->appendData('end_date', $this->data['end_date']);

        $notificationType = ApprovePayslipNotification::class;

        $notificationRequest = new NotificationRequest(
            $notificationType,
            $notificationData,
            $notifiable
        );

        $this->notificationService->notify($notificationRequest);

        $this->runMainNotificationLogic(PayslipNotificationMessageBuilder::ACTION_APPROVE, $notifiable);
    }

    public function onEntityRejectAction(){
    }

    public function onEntityUndoApprove(){
        $notifiable = Collection::make([new Staff($this->data['staff'])]);
        $notificationData = new NotificationDataRequest((int)$this->data['id']);
        $notificationType = ApprovePayslipNotification::class;
        $notificationRequest = new NotificationRequest(
            $notificationType,
            $notificationData,
            $notifiable
        );

        $this->notificationService->unNotify($notificationRequest);
    }

    public function onEntityUndoReject(){
    }

    public function runMainNotificationLogic($action, $notifiable){
        $messageBuilder = new PayslipNotificationMessageBuilder($action, $this->data);
        $allMessagesGetter = new NotificationLanguageSetterDecorator($messageBuilder);
        $notificationDataList = $allMessagesGetter->getMessages($notifiable, FcmDevicesAppUtil::LEAVE_APP);
        $this->fcmService->mobilePushNotification($notificationDataList, 'payslip');
    }
}
