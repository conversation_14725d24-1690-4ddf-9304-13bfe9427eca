<?php

namespace App\Strategies\EntityNotifications;

/**
 * Class NotificationDto
 *
 * A Data Transfer Object (DTO) that encapsulates the title and body of a notification message.
 * Used to standardize the structure of notification content across the system.
 *
 * Implements JsonSerializable to allow easy serialization for API responses and push notification payloads.
 *
 * @package App\Strategies\EntityNotifications
 */
class NotificationDto implements \JsonSerializable
{
    /**
     * NotificationDto constructor.
     *
     * @param string $title The notification title.
     * @param string $body The notification body.
     */
    public function __construct(
        private $title,
        private $body,
    ) {
    }

    /**
     * Specify data which should be serialized to JSON.
     *
     * @return array{
     *     title: string,
     *     body: string
     * }
     */
    public function jsonSerialize(): mixed
    {
        return [
            'title' => $this->title,
            'body' => $this->body
        ];
    }
}
