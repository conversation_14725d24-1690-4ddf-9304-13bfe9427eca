<?php

namespace App\Strategies\AppEntityPermissions;
use Izam\Daftra\Common\Utils\PermissionUtil;
use App\Facades\Permissions;
use App\Services\AttendanceLogService;
use App\Services\AttendanceReportsService;
use App\Services\LeaveApplicationService;
use App\Facades\Plugins;
use App\Models\Shift;
use App\Models\StaffInfo;
use App\Services\StaffService;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Utils\EntityKeyTypesUtil;
use App\Modules\LocalEntity\Factories\FilterConditionFactory;
use App\Repositories\ShiftRepository;
use App\Repositories\StaffRepository;
use App\Services\HolidayListService;
use App\Utils\AttendancePermissionTypesUtil;

class LeaveApplicationCustomResponse implements AppCustomResponseInterface
{
    public function get():array {
        if(!Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)){
            return [];
        }

        // **TEMP** Implement a change in api to be backward compatibility to add and display types of leave only in the leave application
        $leaveApplicationType = null;
        if(
        ((request()->header('X-App-Os') == 'android') && (version_compare(request()->header('X-App-Version'), '1.1.8', '<=')))
        ||
        ((request()->header('X-App-Os') == 'ios') && (version_compare(request()->header('X-App-Version'), '1.16.0', '<=')) )
        ){
            $leaveApplicationType = "leave";
        }

        /** @var HolidayListService */
        $holidayListService = resolve(HolidayListService::class);
        $leaveApplicationService = resolve(LeaveApplicationService::class);
        $attendanceLogService = resolve(AttendanceLogService::class);
        $staffService = resolve(StaffService::class);
        $allEmployeesLeaveApplications = $leaveApplicationService->getAllEmployeesLeaveApplications($leaveApplicationType);
        $leaveApplicationsCanApproveCount = count($allEmployeesLeaveApplications);
        $recentEmployeesLeaveApplication = $allEmployeesLeaveApplications[0] ?? null;
        $curruentStaffId = getAuthOwner('staff_id');
        $entityStructureGetter = resolve(AppEntityStructureGetter::class);
        $structure = $entityStructureGetter->buildEntity(EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY);
        $filterFields = $structure->getFilterFields();
        $displayShiftTypeFilter = false;
        foreach ($filterFields as $field) {
            if($field->getName() == "shift_type"){
                $displayShiftTypeFilter = FilterConditionFactory::getFilterCondition($field)->isFilter($field);
                break;
            }
        }

        $primaryShiftDaysOff = '';
        $staffInfo = StaffInfo::find($curruentStaffId);
        if($staffInfo){
            $attendanceShiftId = $staffInfo->attendance_shift_id;
            if($attendanceShiftId){
                $shift = Shift::find($attendanceShiftId);
                if($shift){
                    $primaryShiftDaysOff = $shift->getDaysOffAttribute();
                }
            }
        }
        return [
            "permission"=>[
                'ADD_LEAVE_APPLICATION'=> Permissions::checkPermission(PermissionUtil::ADD_LEAVE_APPLICATION),
                'EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS'=> Permissions::checkPermission(PermissionUtil::EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS),
                'EDIT_DELETE_ALL_LEAVE_APPLICATIONS'=> Permissions::checkPermission(PermissionUtil::EDIT_DELETE_ALL_LEAVE_APPLICATIONS),
                'VIEW_HIS_OWN_LEAVE_APPLICATIONS'=> Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_LEAVE_APPLICATIONS),
                'VIEW_ALL_LEAVE_APPLICATION'=> Permissions::checkPermission(PermissionUtil::VIEW_ALL_LEAVE_APPLICATION),
                'APPROVE_OR_REJECT_LEAVE_APPLICATION'=> Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION),
                'TAKE_HIS_OWN_ATTENDANCE'=> $curruentStaffId == 0? false: Permissions::checkPermission(PermissionUtil::TAKE_HIS_OWN_ATTENDANCE),
                'MANAGE_ATTENDANCE_PERMISSION'=> Permissions::checkPermission(PermissionUtil::MANAGE_ATTENDANCE_PERMISSION),
                'VIEW_HIS_DEPARTMENT_ATTENDANCE'=> Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE),
                'EDIT_HIS_DEPARTMENT_ATTENDANCE'=> Permissions::checkPermission(PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE),
                'VIEW_HIS_OWN_PAYSLIPS'=> Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_PAYSLIPS),
                'VIEW_HIS_DEPARTMENT_PAYSLIPS'=> Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_PAYSLIPS),
                'VIEW_HIS_OWN_ATTENDANCE_LOG'=> Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG),
                'VIEW_HIS_OWN_ATTENDANCE_SHEET'=> Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET),
            ],
            "leave_balance"=> $this->getCurrentStaffLeaveBalance(),
            "upcoming_holidays" => $holidayListService->getHolidaysForStaff(getAuthOwner('staff_id')),
            "my_leave_applications_count"=> $leaveApplicationService->getMyLeaveApplicationsCount($leaveApplicationType),
            "employees_leave_applications_count"=> $leaveApplicationsCanApproveCount,
            "my_last_leave_applications"=> $leaveApplicationService->getMyLastLeaveApplications($leaveApplicationType),
            "recent_employees_leave_application"=> $recentEmployeesLeaveApplication,
            'last_staff_session_log' =>  $attendanceLogService->getLastStaffAttendanceLog($curruentStaffId),
            'multi_shift_enabled' => \App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift')??'0' == '1'? true : false,
            'current_employee_has_secondary_shift' => $staffService->getHasSecondaryShift(getAuthOwner('staff_id'))['has_secondary_shift'],
            'mult_shift_types'=> \App\Utils\MultipleShiftTypeUtil::getTypes(),
            'display_shift_type_filter' => $displayShiftTypeFilter,
            'leave_applications_types'=> AttendancePermissionTypesUtil::getOptions(),
            'allow_entering_leave_applications_in_past_dates' => (\App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'allow_entering_leave_applications_in_past_dates')??'0') == '1'? true : false,
            'primary_shift_days_off' => $primaryShiftDaysOff,
            'staff_info' => [
                'department' => $staffInfo?->department->name,
                'designation' => $staffInfo?->designation->name,
                'birth_date' => $staffInfo?->birth_date,
            ],
        ];
    }


    private function getCurrentStaffLeaveBalance()
    {
        $reportsService = resolve(AttendanceReportsService::class);
        $leaveData = $reportsService->getLeaveBalanceForStaffIdByType(getAuthOwner('staff_id'), true);
        $responseData = [];
        foreach($leaveData['leaves']??[] as $k => $leaveType){
            $remaining = (int)explode(" ",$leaveType['remaining'])[0];
            $responseData[] = [
                'id'=> $k,
                'name'=> $leaveType['leave_type_name'],
                'credit_before'=> $leaveType['credit_before'],
                'remaining'=> $remaining,
                'taken_before'=> $leaveType['leaves'],
                'total'=> $remaining + $leaveType['leaves']
            ];
        }
        return  $responseData;
    }




}
