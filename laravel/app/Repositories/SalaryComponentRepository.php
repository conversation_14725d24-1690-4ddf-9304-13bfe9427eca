<?php

namespace App\Repositories;

use App\Models\EntityField;
use App\Models\SalaryComponent;
use App\Utils\ActiveStatusUtil;
use App\Utils\SalaryComponents\SalaryComponentsSourceTypeUtil;
use App\Utils\SalaryComponentTypeUtil;
use App\Utils\SalaryComponentValueTypeUtil;

class SalaryComponentRepository extends BaseRepository
{
    function model()
    {
        return 'App\Models\SalaryComponent';
    }

    public function getPaginationDefaultSorting()
    {
            return [];
    }

    public function find($id){
        $this->applyCriteria();
        return $this->model->with('defaultAccount')->find($id);

    }

    public function getBasicComp()
    {
        $this->makeModel();
        return $this->model->where('is_basic', '=', 1)->first();
    }

    public function findValueTypeAmount()
    {
        return $this->findWhere([
            ['status', 1],
            ['value_type', SalaryComponentValueTypeUtil::AMOUNT_VALUE_TYPE],
        ]);
    }

    /**
     * @return SalaryComponent|null
     */
    public function getLoanComponent(): ?SalaryComponent
    {
        return $this->model->where('source_type', '=', SalaryComponentsSourceTypeUtil::LOAN)->first();
    }

    /**
     * @return mixed
     */
    public function getDeductionComponentsForForm()
    {
        $this->resetCriteria();
        return $this->model
            ->where('status', '=', ActiveStatusUtil::ACTIVE)
            ->where('type', '=', SalaryComponentTypeUtil::SALARY_COMPONENT_DEDUCTION)
            ->where(function ($query) {
                $query->where('source_type', '!=', SalaryComponentsSourceTypeUtil::LOAN)
                    ->orWhereNull('source_type');
            })->get();
    }

    public function getEarningComponentsForForm()
    {
        $this->resetCriteria();

        return $this->model
            ->where(function($sql) {
                $sql->where('source_type', '!=', SalaryComponentsSourceTypeUtil::COMMISSION)
                ->orWhereNull('source_type');
            })
            ->where('status', '=', ActiveStatusUtil::ACTIVE)
            ->where('type', '=', SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING)
            ->where('is_basic', '!=', 1)->get();
    }

    /**
     * get commission components
     *
     * @return SalaryComponent
     */
    public function getCommissionComp()
    {
        return $this->model->where('source_type', '=', 'commission')->first();
    }

    /**
     * Summary of mapSalaryComponentsToFields
     * @param mixed $fields
     * @return mixed
     */
    public function mapSalaryComponentsToFields($fields, $idAsKey = true)
    {
        $components = $this->model->where('is_excluded', 0)->where('status', 1)->orderBy('is_basic', 'desc')->orderBy('type', 'desc')->get();
        foreach ($components as $component) {
            // Initialize each component as an EntityField
            $newField = new EntityField([
                'id' => $component->id,
                'label' => $component->name . " (" . __t("Salary Component") . ")",
                'db_name' => 'salary_component_' . $component->id,
                'field_type' => 'number',
                'validation_rules' => ['numeric', $component->is_basic ? 'required' : 'nullable'],
                'allowed_values' => null,
                'key' => 'salary_component_ids'
            ]);
            if ($idAsKey) {
                $fields[$component->id] = $newField;
            } else {
                $fields[] = $newField;
            }
        }
        return $fields;
    }


}
