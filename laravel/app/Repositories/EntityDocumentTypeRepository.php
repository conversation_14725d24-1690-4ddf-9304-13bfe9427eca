<?php

namespace App\Repositories;


use App\Models\EntityDocumentType;
use Illuminate\Support\Facades\DB;

class EntityDocumentTypeRepository extends BaseRepository
{
    function model()
    {
       return EntityDocumentType::class;
    }

    public function getDocumentsByTypesIds(array $typesIds)
    {
        return DB::connection('currentSite')
            ->table('entity_documents')
            ->whereIn('entity_document_type_id', $typesIds)
            ->get();
    }

    public function updateTypes($types)
    {
        foreach ($types as $type) {
           $this->model->where('id', $type['id'])->update($type);
        }
    }

    public function saveTypes($types)
    {
        DB::connection('currentSite')
            ->table('entity_document_types')
            ->insert($types);
    }

    public function deleteDocuments(array $deletedIds)
    {
        DB::connection('currentSite')
            ->table('entity_document_types')
            ->whereIn('id', $deletedIds)
            ->delete();
    }

}
