<?php
namespace App\Repositories;

use App\Models\Category;
use App\Repositories\CategoryRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Izam\Daftra\Common\Utils\FollowUpReminderUtil;
use Illuminate\Support\Carbon;
use App\Facades\Permissions;
use App\Utils\PermissionUtil;
use App\Facades\Branch;
use App\Utils\PluginUtil;
use App\Facades\Plugins;
use App\Models\FollowUpReminder;

class FollowUpReminderRepository extends BaseRepository{

    function model()
    {
        return 'App\Models\FollowUpReminder';
    }

    /**
     * check if employee has followup reminders intersected with the given start and end date
     * @param $staffId
     * @param $startDate
     * @param $endDate
     * @return FollowUpReminder|null
     */
    public function employeeHasBookingAppointmentInDate($staffId, $startDate, $endDate) : ?FollowUpReminder
    {
        return $this->model->where('staff_id', $staffId)->where('item_type', FollowUpReminderUtil::BOOKING_ITEM_TYPE)
            ->where(function($query) use ($startDate, $endDate) {
                $query->whereBetween('date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function($query) use ($startDate, $endDate) {
                        $query->where('date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                    });
            })
            ->first();
    }

    public function getBookingAppointments($date, $type, $staffId, $branchId = null)
    {
        $query = $this->model->where('item_type', FollowUpReminderUtil::BOOKING_ITEM_TYPE)->with([
            'client' => function ($query) {
                $query->select(['id', 'business_name', 'client_number']);
            },
            'bookingItem' => function ($query) {
                $query->select(['id', 'invoice_id'])->without('invoice')->with(['booking' => function ($query) {
                    $query->select(['id', 'payment_status'])->with(['invoice' => function ($query) {
                        $query->select(['id', 'payment_status', 'source_id']);
                    }]);
                }]);
            }
        ])
            ->select(['id','item_id', 'date', 'end_date', 'body', 'partner_id', 'staff_id', 'status']);
        if ($staffId) {
            $query->where('staff_id', $staffId);
        }

        $query->whereHas('bookingItem.invoice', function($q) {
            if(!Permissions::checkPermission(PermissionUtil::VIEW_ALL_BOOKINGS) && Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_BOOKINGS)){
                $q->where('staff_id', getAuthOwner('staff_id'));
            }
            if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
                $q->where('branch_id', $branchId ?? getCurrentBranchID());
            }
        });
        $carbonDate = Carbon::parse($date);
        switch ($type) {
            case 'day':
                $startDate = $carbonDate->format('Y-m-d 00:00:00');
                $endDate = $carbonDate->format('Y-m-d 23:59:59');
                break;
            case 'week':
                // 0 is for sunday
                // 6 is for saturday
                $startDate = $carbonDate->startOfWeek(0)->format('Y-m-d 00:00:00');
                $endDate = $carbonDate->endOfWeek(6)->format('Y-m-d 23:59:59');
                break;
            case 'month':
                $startDate = $carbonDate->startOfMonth()->format('Y-m-d 00:00:00');
                $endDate = $carbonDate->endOfMonth()->format('Y-m-d 23:59:59');
                break;
        }
        $query->where(function($query) use ($startDate, $endDate) {
            $query->WhereBetween('date', [$startDate, $endDate]);
            $query->orWhereBetween('end_date', [$startDate, $endDate]);
        });
        $results = $query->orderBy('date')->get();
        if (!$staffId) {
            return $results->groupBy('staff_id')->toArray();
        } else {
            return $results->groupBy(function ($item) {
                return Carbon::parse($item->date)->format('Y-m-d');
            })->toArray();
        }
        return $results->toArray();
    }

}
