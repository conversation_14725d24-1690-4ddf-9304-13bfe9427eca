<?php

namespace App\Repositories;

use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Interfaces\BankTransactionRepositoryInterface;
use App\Utils\BankTransactionsStatusUtil;

class BankTransactionRepository extends BaseRepository implements BankTransactionRepositoryInterface
{
    function model()
    {
        return 'App\Models\BankTransaction';
    }

    public function getTransactionToBeMatched($transactionId) {
        $bankTransaction = $this
            ->pushCriteria(new EqualCriteria('id', $transactionId))
            ->pushCriteria(new EqualCriteria('status', BankTransactionsStatusUtil::NOT_MATCHED))
            ->first();
        return $bankTransaction;
    }

    public function getLastBankTransaction($bankId) {
        $bankTransaction = $this->model::query()->select('reference_id', 'date')->where('bank_id', $bankId)->orderBy('date', 'desc')->first();
        return $bankTransaction;
    }

    public function getBankUnMatchedBankTransactions(
        $bankId,
        $transactionType,
        $bankTransactionIds = [],
    ) {
        $this->pushCriteria(new EqualCriteria('bank_transactions.bank_id', $bankId))
            ->pushCriteria(new EqualCriteria('bank_transactions.type', $transactionType))
            ->pushCriteria(new EqualCriteria('bank_transactions.status', BankTransactionsStatusUtil::NOT_MATCHED))
            ->pushCriteria(new InArrayCriteria('bank_transactions.id', $bankTransactionIds));
        return $this->all(['bank_transactions.*']);
    }

    public function updateJournalTransactionId($bankTransactionsIds, $system_transaction_id) {
        $this->model()::whereIn('id', $bankTransactionsIds)
            ->update(['journal_transaction_id' => $system_transaction_id, 'status' => BankTransactionsStatusUtil::MATCHED]);
    }
}
