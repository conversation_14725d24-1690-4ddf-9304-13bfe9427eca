<?php

namespace App\Repositories;

use App\Models\AttendanceDay;
use App\Models\AttendanceLog;
use App\Utils\AttendanceLogStatusUtil;
use App\Utils\AttendanceMachineMappingUtil;
use App\Utils\AttendanceSheetStatusTypesUtil;
use Illuminate\Support\Facades\DB;

class AttendanceLogRepository extends BaseRepository
{
    protected $paginateWith = ['attendance_log_staff_relation'];

    function model()
    {
        return 'App\Models\AttendanceLog';
    }

    /**
     * @param $sessionId
     * @param $staffId
     * @return mixed
     */
    public function getLastStaffSessionLog($sessionId, $staffId)
    {
        $conditions = [['staff_id', '=', $staffId]];
        if($sessionId)
        {
            $conditions[] = ['session_id', '=', $sessionId];
        }
        return $this->model->Where($conditions)->orderBy('time', 'desc')->get();
    }

    public function getSessionNonDraftLogsCount($sessionId)
    {
        return $this->model->where([['session_id', '=', $sessionId], ['status', '!=', AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT]])->get()->count();
    }

    /**
     * @param $staffID
     * @param $beginningIn
     * @param $endingIn
     * @param $beginningOut
     * @param $endingOut
     * @return null|array
     */
    public function getSigns($staffID, $beginningIn, $endingIn, $beginningOut, $endingOut)
    {
        return DB::connection('currentSite')
            ->select("SELECT id, time,IF(time >= ? AND time <= ?, 'IN', 'OUT') as type FROM attendance_logs
                WHERE staff_id = ? AND status IN ( ? , ? )
                  AND ((time >= ? AND time <= ?) OR (time >= ? AND time <= ?))
                  ORDER BY time",
                [
                    $beginningIn,
                    $endingIn,
                    $staffID,
                    AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT,
                    AttendanceLogStatusUtil::ATTENDANCE_LOG_INVALID,
                    $beginningIn,
                    $endingIn,
                    $beginningOut,
                    $endingOut
                ]) ?: null;
    }

    public function resetLogs($employee_ids, $from_date, $to_date){
        $inQuery = implode(',', $employee_ids);
        $from_date .= " 00:00:00";
        $to_date .= " 23:59:59";

        $deleteLogs =
            DB::connection('currentSite')
                ->update("UPDATE attendance_logs SET status = :log_status WHERE staff_id in ({$inQuery}) AND (`time` BETWEEN :from_date AND :to_date)",
                    ['from_date' => $from_date, 'to_date' => $to_date, 'log_status' => AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT]);
        return $deleteLogs;
    }

    /**
     * @param $staffIds
     * @param $dateFrom
     * @param $dateTo
     * @return int
     */
    public function setInvalidSignsInRange($staffIds, $dateFrom, $dateTo): int
    {
        $staffIdsString = implode(',', $staffIds);
        $dateFrom .= " 00:00:00";
        $dateTo .= " 23:59:59";
        $sql = "UPDATE attendance_logs SET status=:status WHERE status=:draftStatus AND staff_id in ({$staffIdsString}) AND `time` BETWEEN :dateFrom AND :dateTo";
        return DB::connection('currentSite')->update($sql, ['draftStatus' => AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT, 'status'=>AttendanceLogStatusUtil::ATTENDANCE_LOG_INVALID, 'dateFrom' => $dateFrom, 'dateTo' => $dateTo]);
    }

    public function setInvalidSignsInDate($staffIds, $date): int
    {
        $staffIdsString = implode(',', $staffIds);
        $dateFrom = $date . " 00:00:00";
        $dateTo = $date . " 23:59:59";
        $sql = "UPDATE attendance_logs SET status=:status WHERE status=:draftStatus AND staff_id in ({$staffIdsString}) AND `time` BETWEEN :dateFrom AND :dateTo";
        return DB::connection('currentSite')->update($sql, ['draftStatus' => AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT, 'status'=>AttendanceLogStatusUtil::ATTENDANCE_LOG_INVALID, 'dateFrom' => $dateFrom, 'dateTo' => $dateTo]);
    }

    /**
     * @param $signs
     * @return int
     */
    public function setDaysSigns($signIDs, $signDays)
    {
        if ($signIDs) {
            $allIDs = [];
            $sql = 'UPDATE attendance_logs SET status = (CASE ';
            foreach ($signIDs as $status => $ids) {
                if ($ids) {
                    $allIDs = array_merge($allIDs, $ids);
                    $sql .= 'WHEN (id IN (' . implode(',', $ids) . ')) THEN "' . $status . '" ';
                }
            }
            $sql .= 'END ), attendance_day_id = (CASE ';
            foreach ($signDays as $day => $ids) {
                if ($ids) {
                    $sql .= 'WHEN (id IN (' . implode(',', $ids) . ')) THEN "' . $day . '" ';
                }
            }
            $sql .= 'END ) WHERE id IN (' . implode(',', $allIDs) . ');';
            return DB::connection('currentSite')->update($sql);
        }
    }

    public function getAttendanceShiftOfAttendanceLog($attendanceLog) {
        $attendanceDayId = $attendanceLog->staff_id . "--" . date('Y-m-d', strtotime($attendanceLog->time));
        $attendanceDay = AttendanceDay::where('id', $attendanceDayId)->first();
        return $attendanceDay->shift_id ?? null;
    }

    /**
     * @params $staffIds coma seperated ids of staffs
     */
    public function getDraftAttendanceLogsDetails(string $staffIds = ""){
        $status = AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT;
        if (empty($staffIds)){
            $staffIds = "select id from staffs where deleted_at is null and active = 1";
        }
        return DB::connection('currentSite')
            ->select("
                        SELECT
                        SUM(id_sum) AS attendance_logs,
                        COUNT(DISTINCT staff_id) AS employees_number,
                        MIN(min_created_minus_day) AS from_date,
                        MAX(max_created_plus_day) AS to_date
                    FROM
                    (
                        SELECT
                            staff_id,
                            COUNT(id) AS id_sum,
                            DATE_SUB(MIN(time), INTERVAL 1 DAY) AS min_created_minus_day,
                            DATE_ADD(MAX(time), INTERVAL 1 DAY) AS max_created_plus_day
                        FROM
                            attendance_logs
                        WHERE
                            status = '{$status}' and
                            staff_id in ($staffIds) and
                            time >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
                        GROUP BY
                            staff_id
                    ) SubqueryAlias;
            ");
    }

    public function getAttendanceDay($attendanceLog) {
        $attendanceDayId = $attendanceLog->staff_id . "--" . date('Y-m-d', strtotime($attendanceLog->time));
        $attendanceDay = AttendanceDay::where('id', $attendanceDayId)->first();
        return $attendanceDay;
    }

    public function checkThatSessionRelatedToApprovedSheet($sessionId)
    {
        $approvedLogs = DB::connection('currentSite')->table('attendance_logs')->select('attendance_sheets.auto_id as attendance_sheet_id')
            ->join('attendance_days', 'attendance_days.id', '=', DB::raw('CONCAT(attendance_logs.staff_id, "--", DATE_FORMAT(attendance_logs.time, "%Y-%m-%d"))'))
            ->join('attendance_sheets', 'attendance_days.attendance_sheet_id', '=', 'attendance_sheets.id')
            ->where('attendance_logs.session_id', $sessionId)
            ->where('attendance_sheets.status', AttendanceSheetStatusTypesUtil::APPROVED)
            ->get();
        return $approvedLogs;
    }

    public function mapLogsAfterMachineMapping($machine_id, $mapping)
    {
        if( $machine_id && count($mapping)){
            foreach($mapping as $staff_machine_id => $staff_id){
                AttendanceLog::where('source_type', 'machine')
                              ->where('source_id', $machine_id)
                              ->where('staff_machine_id', $staff_machine_id)
                              ->where('staff_id', AttendanceMachineMappingUtil::UNKNOWN)
                              ->update([
                                'status'=> AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT, 
                                'staff_id' => $staff_id
                            ]);          
            }            
        }
        return true;
    }
}
