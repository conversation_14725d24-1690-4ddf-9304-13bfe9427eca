<?php

namespace App\Repositories;

use App\Models\CostCenterTransaction;
use App\Facades\Branch;
use App\Facades\Plugins;
use App\Utils\PluginUtil;
use App\Facades\Settings;




class CostCenterRepository extends BaseRepository
{
    function model()
    {
        return 'App\Models\CostCenter';
    }


    public function doesCostCenterCodeOrNameExist($account){
        $query = $this->model->where(function($q) use ($account) {
            $q->where('code', $account)->orWhere('name', $account);
        });
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_cost_centers')) {
           $query = $query->where('branch_id', Branch::getCurrentBranchID());
        }
        return $query->exists();
    }

    public function getCostCenterByCodeORName($account){
        return $this->model->where('code',$account)->orWhere('name',$account)->first();
    }

    public function saveTransactions($transactions) {
        CostCenterTransaction::insert($transactions);
    }

}
