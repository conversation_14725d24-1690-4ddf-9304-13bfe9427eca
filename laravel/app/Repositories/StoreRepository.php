<?php

namespace App\Repositories;

use App\Models\ItemPermission;
use App\Models\Staff;
use App\Models\Store;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\ItemPermissionUtil;

/**
 * StoreRepository Class store repository deals with store model
 * @package App\Repositories
 */
class StoreRepository extends BaseRepository
{
    /**
     * set model
     * @return string
     */
    function model()
    {
        return Store::class;
    }

    public function findBy(array $request = []): array
    {
        $status = !empty($request['status']) ? ($request['status'] == 'true' || $request['status'] == 1 ? 1 : 0) : false;

        return $this->model
            ->when($status, function ($q, $status) {
                $q->whereActive($status);
            })->get($request['columns'] ?? ['*'])->toArray();
    }

    public function getStoresForDesktopSelectOption(): array
    {
        return $this->model->whereActive('1')->pluck('name', 'id')->toArray();
    }

    public function getStoresByPermission(string $permissionLevel, string $query = '', bool $paginate = true): array
    {
        $authUser = getAuthOwner();

        $storeQuery = $this->model->whereActive('1');
        if (!empty($query) && is_string($query)) {
            $storeQuery->where('name', "like", "%{$query}%");
        } elseif ($paginate) {
            $storeQuery->skip(0)->take(10);
        }

        $stores = $storeQuery->get(['name', 'id', 'name as text'])->toArray();

        if ($authUser['staff_id'] == 0) {
            return $stores;
        }

        $staff = Staff::find($authUser['staff_id']);
        $storesIds = array_column($stores, 'id');

        $storesPermissions = ItemPermission::where('item_type', ItemPermissionUtil::ITEM_TYPE_STORE)
            ->whereIn('item_id', $storesIds)
            ->where('permission_level', $permissionLevel)
            ->where(function ($q) use ($staff) {
                $q->where('group_type', ItemPermissionUtil::GROUP_EVERYONE)
                    ->orWhere(function ($q) use ($staff) {
                        $q->where('group_type', ItemPermissionUtil::GROUP_ROLE)
                            ->where('group_id', $staff->role_id);
                    })
                    ->orWhere(function ($q) use ($staff) {
                        $q->where('group_type', ItemPermissionUtil::GROUP_STAFF)
                            ->where('group_id', $staff->id);
                    });

                if (ifPluginActive(PluginUtil::BranchesPlugin)) {
                    $q->orWhere(function ($q) use ($staff) {
                        $q->where('group_type', ItemPermissionUtil::GROUP_BRANCH)
                            ->whereIn('group_id', getStaffBranches());
                    });
                }
            })
            ->pluck('item_id')
            ->toArray();

        return array_values(array_filter($stores, function ($store) use ($storesPermissions) {
            return in_array($store['id'], $storesPermissions);
        }));
    }

    public function getStoresUsersCanUpdate(string $query = '', bool $paginate = true): array
    {
        return $this->getStoresByPermission(ItemPermissionUtil::PERMISSION_STOCK_UPDATING, $query, $paginate);
    }

    public function getStoresUsersCanCreateInvoice(string $query = '', bool $paginate = true): array
    {
        return $this->getStoresByPermission(ItemPermissionUtil::PERMISSION_INVOICING, $query, $paginate);
    }

    public function getPrimaryStore()
    {
        return $this->model->where('active', 1)->where('primary', 1)->first();
    }

    /**
     * @return string[]
     */
    public function getAllStores($query = ''): array
    {
        $modelQ =  $this->model->query();
        if(!empty($query) && is_string($query)){
            $modelQ = $modelQ->where('name', "like", "%".$query."%");
        }else{
            $modelQ = $modelQ->skip(0)->take(10);
        }
        if (request()->disabled == 'false'){
            $modelQ->whereActive('1');
        }

        $stores = $modelQ->get([ 'id' , 'name as text'])->toArray();
        return  $stores;
    }


/* $canViewAll when true this method check if the user can view all $storesIds,
    when false, check if the user can view at least one store from $storesIds */
    public function getStoresUserCanView($storesIds, $canViewAll = true): bool
    {
        $authUser = getAuthOwner();

        if($authUser['staff_id'] == 0){
            return true;
        }else{
            $staff = Staff::find($authUser['staff_id']);
            $storesPermissions = ItemPermission::where('item_type', "=", ItemPermissionUtil::ITEM_TYPE_STORE)
                ->whereIn('item_id', $storesIds)
                ->where('permission_level', "=", ItemPermissionUtil::PERMISSION_VIEW)
                ->where(function($query) use($staff){
                    $query = $query->where('group_type', "=", ItemPermissionUtil::GROUP_EVERYONE)
                        ->orWhere(function($query) use($staff){
                            $query->where('group_type', "=", ItemPermissionUtil::GROUP_ROLE)
                                ->where('group_id', "=", $staff->role_id);
                        });
                        if(ifPluginActive(PluginUtil::BranchesPlugin)){
                            $query = $query->orWhere(function($query) use($staff){
                                $query->where('group_type', "=", ItemPermissionUtil::GROUP_BRANCH)
                                    ->whereIn('group_id' ,getStaffBranches());
                            });
                        }
                        $query = $query->orWhere(function($query) use($staff){
                            $query->where('group_type', "=", ItemPermissionUtil::GROUP_STAFF)
                                ->where('group_id', "=", $staff->id);
                        });
                })
                ->get()->toArray();
            $storesIdsUsersCanView = array_map(function($storePermissions){return $storePermissions['item_id'];}, $storesPermissions);
            if($canViewAll){
                foreach($storesIds as $storesId){
                    if(!in_array($storesId, $storesIdsUsersCanView)){
                        return false;
                    }
                }
                return true;
            }else{
                foreach($storesIds as $storesId){
                    if(in_array($storesId, $storesIdsUsersCanView)){
                        return true;
                    }
                }
                return false;
            }

        }
    }

    public function getActiveAndSuspendedStoresId()
    {
        return $this->model->whereIn('active' , [1,2])->pluck('id')->toArray();
    }

}
