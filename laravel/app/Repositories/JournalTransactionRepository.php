<?php
namespace App\Repositories;

use App\Facades\Branch;
use App\Facades\Plugins;
use App\Models\Journal;
use App\Models\JournalLog;
use App\Models\JournalTransaction;
use App\Models\JournalTransactionLog;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Criteria\IsNullCriteria;
use App\Repositories\Criteria\JoinCriteria;
use App\Repositories\Criteria\LargerThanCriteria;
use App\Repositories\Interfaces\JournalTransactionRepositoryInterface;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\BankTransactionsStatusUtil;
use Izam\Daftra\Common\Utils\PluginUtil;

class JournalTransactionRepository extends BaseRepository implements JournalTransactionRepositoryInterface {


    function model()
    {
        return 'App\Models\JournalTransaction';
    }

    public function getPaginationDefaultSorting() {
        return  [
            'field' => 'id',
            'direction' => 'DESC'
        ];
    }

    public function getBankUnMatchedTransactions($bankAccountId,
                                                 $transactionType,
                                                 $systemTransactionIds = [],
                                                 $currencyCode = null) {
        if($transactionType === 'deposit') {
            $transactionAmountField = 'debit';
        } else {
            $transactionAmountField = 'credit';
        }
        $this
            ->pushCriteria(new JoinCriteria([
                [
                    'table' => 'journals',
                    'conditions' => [
                        ['journals.id', '=', 'journal_transactions.journal_id']
                    ]
                ]
            ]))
            ->pushCriteria(new EqualCriteria('journal_transactions.journal_account_id', $bankAccountId))
            ->pushCriteria(new IsNullCriteria('journal_transactions.bank_transaction_id'))
            ->pushCriteria(new LargerThanCriteria('journal_transactions.'.$transactionAmountField, 0))
        ;
        if($currencyCode) {
            $this->pushCriteria(new EqualCriteria('journals.currency_code', $currencyCode));
        }
        $this->pushCriteria(new InArrayCriteria('journal_transactions.id',$systemTransactionIds));
        return $this->all(['journal_transactions.*']);
    }

    public function updateBankTransactionId($transactionIds, $bankTransactionId) {
        JournalTransaction::whereIn('id', $transactionIds)
            ->update(['bank_transaction_id' => $bankTransactionId, 'status' => BankTransactionsStatusUtil::MATCHED]);
    }

    public function getCurrenciesList() {
        $this->applyCriteria();
        $values = $this->model
            ->join('journals', 'journal_transactions.journal_id', '=', 'journals.id')
            ->select('journals.currency_code')
            ->distinct()
            ->get()
            ->pluck('currency_code')
        ;
        return $values;
    }
    /**
     * Search Using unused debit journal transactions
     * using Journal Number or Description or Account Name or Account Code
     * to be used in expense distribution
     *
     * @param [type] $query
     * @param boolean $ignoreAlreadySelected
     * @return void
     */
    function getTransactionsForExpenseDistribution($query, $updateExpenseDistributionId = null, $branchId = null, $returnAll = false , $excludeEntity = false) {
        $searchQuery = JournalTransaction::with(['journal','journal_account'])
        ->join('journals', 'journals.id','=', 'journal_transactions.journal_id')
        ->join('journal_accounts', 'journal_accounts.id','=', 'journal_transactions.journal_account_id')
        ->where(function($sql) use($query) {
            $sql
            ->where('journals.number','like', "%$query%")
            ->orWhere('journals.description','like', "%$query%")
            ->orWhere('journals.alter_description','like', "%$query%")
            ->orWhere('journal_accounts.name','like', "%$query%")
            ->orWhere('journal_accounts.code','like', "%$query%")
            ;
        })
        ->where('journal_transactions.debit','>',0)
        ->where('journals.draft', 0);
        if($excludeEntity) {
            $searchQuery->where('journals.entity_type', '!=', $excludeEntity);
        }
        if($branchId) {
            $searchQuery->where('journals.branch_id', '=', $branchId);
        }
        $searchQuery->whereNotIn('journal_transactions.id', function($sql) use($updateExpenseDistributionId) {
            $sql->from('expense_distribution_items');
            if($updateExpenseDistributionId) {
                $sql->where('expense_distribution_id', '!=', $updateExpenseDistributionId);
            }
            $sql->whereNotNull('transaction_id');
            $sql->select('transaction_id');
        });
        if(!$returnAll) {
            $searchQuery->limit(10);
        }
        return $searchQuery->get('journal_transactions.*');
    }
    function getDebitTransactionForJournalAccount($query, $journalAccountId = null, $branchId = null , $ignoredTransactions = [] , $fromDate =null , $toDate =null) {
        $searchQuery = JournalTransaction::with(['journal','journal_account'])
            ->join('journals', 'journals.id','=', 'journal_transactions.journal_id')
            ->join('journal_accounts', 'journal_accounts.id','=', 'journal_transactions.journal_account_id')
            ->where('journal_accounts.id',$journalAccountId)
            ->when(!empty($fromDate) || !empty($toDate), function ($dq) use ($fromDate, $toDate) {
                if (!empty($fromDate) && !empty($toDate)) {
                    $dq->whereBetween('journals.date', [$fromDate, $toDate]);
                } elseif (!empty($fromDate)) {
                    $dq->where('journals.date', '>=', Carbon::parse($fromDate)->format('Y-m-d'));
                } else {
                    $dq->where('journals.date', '<=', Carbon::parse($toDate)->format('Y-m-d'));
                }
            })
            ->when($ignoredTransactions , function ($hq) use ($ignoredTransactions){
                $ignoredTransactionsIds = array_column($ignoredTransactions , 'journal_transaction_id');
                $hq->whereNotIn('journal_transactions.id', $ignoredTransactionsIds);
            })
            ->when($query, function ($qq) use ($query){
                $qq->where(function($sql) use($query) {
                   $sql
                       ->where('journals.number','like', "%$query%")
                       ->orWhere('journals.description','like', "%$query%")
                       ->orWhere('journals.alter_description','like', "%$query%")
                       ->orWhere('journal_accounts.name','like', "%$query%")
                       ->orWhere('journal_accounts.code','like', "%$query%")
                   ;
               });
            })
            ->where('journal_transactions.debit','>',0)
            ->where('journals.draft', 0)
        ;
        if($branchId) {
            $searchQuery->where('journals.branch_id', '=', $branchId);
        }
        $searchQuery->limit(50);
        return $searchQuery->get('journal_transactions.*');
    }

    public function getTransactionsLatestLogs(array $transactionsIds)
    {
        return DB::connection("currentSite")
            ->table("journal_transaction_logs")
            ->whereIn("journal_transaction_id", $transactionsIds)
            ->orderBy('created', 'desc')
            ->get()
            ->unique('journal_transaction_id')
        ;
    }

    public function getTransactionsLogs(array $transactionsIds )
    {
        return DB::connection("currentSite")
            ->table("journal_transaction_logs")
            ->whereIn("journal_transaction_id", $transactionsIds)
            ->orderBy('created', 'desc')
            ->get()
        ;
    }

    public function getDeletedTransactionsLogs($deletedTransactionsIds , $logsIds = []){
        $logs = JournalTransactionLog::with('journals')
            ->whereIn('journal_transaction_id',$deletedTransactionsIds)
            ->whereIn('id',$logsIds)
            ->where('action','add')
            ->where('debit','!=',0)
            ->get();
       $deletedJournalsIds = [];
       foreach ($logs as &$log){
           if ($log->journals){
               $log->journal_number = $log->journals->number;
           }else{
               $deletedJournalsIds[] = $log->journal_id;
           }
       }
        $deletedJournals = JournalLog::whereIn('journal_id' , $deletedJournalsIds)
            ->where('action','delete')
            ->orderBy('created', 'desc')
            ->get();
        foreach ($logs as &$log){
            $deletedAccount = $deletedJournals
                ->filter(function ($item) use ($log){
                    return $log->journal_id == $item->journal_id ;
                })
                ->first();
            if ($deletedAccount){
                $log->journal_number = $deletedAccount->number;
                $log->journal_deleted = true;
            }
        }
        return $logs;
    }

    public function getTransactionsWithRelatedRelations($transactionIds , $relations =[])
    {
        return JournalTransaction::with($relations)->whereIn('id', $transactionIds)
            ->get();
    }
}
