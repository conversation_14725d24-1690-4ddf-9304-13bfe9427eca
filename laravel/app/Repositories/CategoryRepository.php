<?php

namespace App\Repositories;

use App\Facades\Branch;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Utils\CategoryTypesUtil;
use App\Utils\PluginUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CategoryRepository extends BaseRepository
{
    public $applyBranches = ['onSave' => true, 'onFind' => true];
    public $listingColumns = ['id', 'name'];

    function model()
    {
        return 'App\Models\Category';
    }

    public function getProductCategories()
    {
        $query = $this->model->where('category_type', 1);

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {

            $query->where('branch_id', Branch::getCurrentBranchID());
        }

        return $query->get();
    }

    public function getClientCategoryAllOptionId()
    {
        return $this->model->where('category_type', CategoryTypesUtil::CATEGORY_TYPE_CLIENT)->where('name', 'All')->first()->id;
    }

    public function updateOrCreate(Request $request)
    {
        return $this->model->updateOrCreate([
            'mac_address' => $request->mac_address,
            'category_type' => CategoryTypesUtil::CategoryTypePOSDEVICE,
        ], [
            'name' => $request->pos_device,
            'classification_id1' => $request->warehouse_id,
        ]);
    }

    public function checkIfPosDeviceExistsByNameOrMac($pos_device = "", $mac_address = "")
    {
        return $this->model->where('name', $pos_device)->orWhere('mac_address', $mac_address)->first();
    }

    public function getDesktopPosDeviceData($mac_address)
    {
        return $this->model->where('mac_address', $mac_address)->first(['mac_address', 'name AS pos_device']);
    }

    public function getCategoriesOfType($categoryType)
    {
        $query = $this->model->where('category_type', $categoryType);
        if ($categoryType == CategoryTypesUtil::CATEGORY_TYPE_CLIENT) {
            //get only used categories
            $query->distinct('clients.category')->select($this->model->getTable() . '.*')
                  ->join('clients', function($join) {
                      $join->on(DB::raw('CONVERT(clients.category USING utf8mb4) COLLATE utf8mb4_unicode_ci'), '=', DB::raw('CONVERT(' . $this->model->getTable() . '.name USING utf8mb4) COLLATE utf8mb4_unicode_ci'))
                      ->wherenotnull('clients.category')
                      ->where($this->model->getTable() . '.name', '!=', '');
                  });
        }
        return $query->get()->toArray();
    }

    public function searchProductCategories($categoryName)
    {

        $categories = $this->model->where('category_type', 1);

        if (!(empty($categoryName) || !is_string($categoryName))) {
            $categories = $categories->where('name', 'like', '%' . $categoryName . '%');
        }

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $categories = $categories->where('branch_id', Branch::getCurrentBranchID());
        }

        $categories = $categories->get()->toArray();
        $result = [];
        foreach ($categories as $category) {
            $result[] = [
                'name' => $category['name'],
                'id' => $category['name'],
            ];
        }

        return $result;
    }

    public function findOrCreateByName($categoryName, $branchId = null)
    {

        $category = $this->model
            ->where('category_type', CategoryTypesUtil::CategoryTypeProduct)
            ->where('name', $categoryName)
            ->first();
        $newlyCreated = false;
        if (!$category) {
            $newlyCreated = true;
            $modelData = [
                'name' => $categoryName,
                'category_type' => CategoryTypesUtil::CategoryTypeProduct
            ];
            if ($branchId){
                $modelData['branch_id']=$branchId;
            }
         $category = $this->model
             ->create($modelData);
        }
        return ['category'=>$category , 'isNew'=>$newlyCreated];
    }

    public function getCategoriesWithProductsCount()
    {
        $this->applyBranchesFind();
        return $this->model->where('category_type', CategoryTypesUtil::CategoryTypeProduct)->whereHas('products', function($query){
            if(Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')){
                $query->where('products.branch_id', Branch::getCurrentBranchID());
            }})->withCount('products')->get()->toArray();
    }
}
