<?php

namespace App\Repositories;

use App\Models\DeviceAppNotification;
use App\Utils\FcmDevicesAppUtil;

/**
 * Class DeviceAppNotificationRepository
 *
 * Repository class for managing device application notifications.
 * Provides helper methods to add notifications for specific applications such as leave apps.
 */
class DeviceAppNotificationRepository extends BaseRepository
{

    /**
     * Returns the model class associated with this repository.
     *
     * @return string
     */
    public function model()
    {
        return DeviceAppNotification::class;
    }

    public function addForApp($appKey, $data)
    {
        $data['app_key'] = $appKey;
        return $this->add($data);
    }

    public function addForLeaveApplication($data)
    {
        return $this->addForApp(FcmDevicesAppUtil::LEAVE_APP, $data);
    }
}
