<?php


namespace App\Repositories\Criteria;


use App\Repositories\BaseRepositoryInterface;

class BetweenCriteria extends Criteria
{
    public function __construct(private $field, private $minVal, private $maxVal)
    {
    }

    public function apply($model, CriteriaInterface $repository)
    {
        return $model->when($this->minVal, function($query){
            return $query->where($this->field, '>=', $this->minVal);
        })->when($this->maxVal, function($query){
            return $query->where($this->field, '<=', $this->maxVal);
        });
    }
}
