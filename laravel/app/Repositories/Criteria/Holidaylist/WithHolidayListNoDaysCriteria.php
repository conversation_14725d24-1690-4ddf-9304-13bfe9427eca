<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 28/06/20
 * Time: 01:15 م
 */

namespace App\Repositories\Criteria\Holidaylist;


use App\Repositories\Criteria\Criteria;
use App\Repositories\Criteria\CriteriaInterface;

class WithHolidayListNoDaysCriteria extends Criteria
{
    private $staffId;
    public function __construct()
    {
    }

    public function apply($model, CriteriaInterface $repository)
    {
        return $model->with(['holidayList' => function($query){$query->without(['days']);}]);
    }
}