<?php


namespace App\Repositories\Criteria;


class WhereHasCriteria extends Criteria
{
    public function __construct(private $relation, private $field, private $operator, private $value, private $callbackQuery = null)
    {
    }

    public function apply($model, CriteriaInterface $repository)
    {
        if(isset($this->callbackQuery)){
            return $model->whereHas($this->relation, $this->callbackQuery);
        }
        return $model->whereHas($this->relation, function ($query) {
            $query->where($this->field, $this->operator, $this->value);
        });
    }
}
