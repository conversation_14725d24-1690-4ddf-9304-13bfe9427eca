<?php

namespace App\Repositories;
use App\Utils\AttendanceLogSourceTypeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class MachineRepository extends BaseRepository
{
    function model()
    {
        return 'App\Models\Machine';
    }


    public function add($data)
    {
        $data['uuid'] = Str::uuid()->toString();
        if (!$data['host']) {
            $data['host'] = '';
        }
        return parent::add($data);
    }

    public function findByUuid($uuid)
    {
        $this->applyCriteria();
        return $this->model->where('uuid', $uuid)->first();
    }

    /**
     * @param $machineId
     * @return array key is the machine staff id the value is staff id
     */
    function getMachineMappingsList($machineId)
    {
        $results = DB::connection('currentSite')
            ->table('machine_employee_mappings')
            ->select(["staff_id","staff_machine_id"])
            ->where('machine_id', $machineId)
            ->pluck('machine_employee_mappings.staff_id', 'machine_employee_mappings.staff_machine_id');
        return $results->toArray();
    }

    function getTotalPulledSigns($machineId) {
        $count = DB::connection('currentSite')
            ->table('attendance_logs')
            ->where('source_id', '=', $machineId)
            ->where('source_type', '=', AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_MACHINE)
            ->count();
        return $count;
    }
}
