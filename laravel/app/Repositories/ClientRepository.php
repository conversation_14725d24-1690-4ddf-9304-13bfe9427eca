<?php

namespace App\Repositories;

use App\Exceptions\EntityNotFoundException;
use App\Facades\Branch;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Models\Client;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\LimitCriteria;
use App\Utils\CreditChargeStatusUtil;
use App\Utils\Memberships\MemberShipStatusUtil;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Illuminate\Container\Container as App;
use Izam\Attachment\Service\AttachmentsService;
use App\Facades\Permissions;
use App\Utils\PermissionUtil;

/**
 * ClientRepository Class
 * @package App\Repositories
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class ClientRepository extends BaseRepository
{
    public $applyBranches = ['onSave' => true, 'onFind' => true];
    public function __construct(App $app, Collection $collection)
    {
        parent::__construct($app, $collection);
    }
    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return Client::class;
    }

    /**
     * auto complete client result
     * @param mixed $q
     * @param bool $allowSuspended
     * @return mixed
     */
    public function getAutoCompleteResult($q, bool $allowSuspended = false)
    {
        $query = $this->model->where(function ($query) use ($q) {
            $query->orWhere('business_name', 'like','%'.$q.'%')
                ->orWhere('id', '=', $q)
                ->orWhere('client_number', '=',$q)
                ->orWhere('phone1', 'like','%'.$q.'%')
                ->orWhere('phone2', 'like','%'.$q.'%')
                ->orWhere('first_name', 'like','%'.$q.'%')
                ->orWhere('last_name', 'like','%'.$q.'%')
                ->orWhere('email', 'like', '%'.$q.'%');
        });
        if (!$allowSuspended) {
            $query = $query->where(function ($query) {
                $query->where('suspend', '=', 0);
            });
        }
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $shareAllClients = Settings::getValue(PluginUtil::BranchesPlugin, 'share_clients', null, false);
            if (!$shareAllClients) {
                $branchId = Branch::getCurrentBranchID();
                $query->where('branch_id', '=', $branchId);
            }
        }
        return $query->get();
    }

    public function getClientCreditSummaryData(int $client_id)
    {
        $today = Carbon::today()->format('y-m-d');
        $data = DB::connection('currentSite')->table('credit_types')
            ->select([
                'credit_types.id as credit_types_id',
                'credit_types.name as credit_types_name',
                'credit_types.unit as credit_types_unit',
                'credit_charges.amount as credit_charges_amount_sum',
                DB::Raw('SUM(charge_usages.amount) as charge_usages_amount_sum')
            ])
            ->leftJoin('credit_charges', function ($credit_charges_join) use ($client_id, $today) {
                /** @var JoinClause $credit_charges_join*/
                $credit_charges_join->on('credit_types.id', '=', 'credit_charges.credit_type_id');
                $credit_charges_join->where('credit_charges.client_id', '=', $client_id);
                $credit_charges_join->whereIn('credit_charges.status', [CreditChargeStatusUtil::AVAILABLE, CreditChargeStatusUtil::CONSUMED]);
                $credit_charges_join->where('credit_charges.start_date', '<=', $today);
                $credit_charges_join->where(function ($query) use ($today){
                    /** @var JoinClause $query*/
                    $query->where('credit_charges.expiry_date','>=', $today);
                    $query->orWhereNull('credit_charges.expiry_date');
                });
                $credit_charges_join->whereNull('credit_charges.deleted_at');
            })
            ->leftJoin('credit_usages', function ($credit_usages_join) use ($client_id){
                /** @var JoinClause $credit_usages_join*/
                $credit_usages_join->on('credit_types.id', '=', 'credit_usages.credit_type_id');
                $credit_usages_join->where('credit_usages.client_id', '=', $client_id);
                $credit_usages_join->whereNull('credit_usages.deleted_at');
            })
            ->leftJoin('charge_usages', function ($charge_usages_join){
                /** @var JoinClause $charge_usages_join*/
                $charge_usages_join->on('charge_usages.credit_charge_id', '=', 'credit_charges.id');
                $charge_usages_join->on('charge_usages.credit_usage_id', '=', 'credit_usages.id');
            })
            ->whereNull('credit_types.deleted_at')
            ->groupBy(['credit_types.id', 'credit_charges.id'])
            ->get();

        return $data->toArray();
    }

    public function getClientMembership($id){
        $client = $this->model->with(['membership'])->find($id);
        if($client) {
            return $client->membership()->first();
        }

        throw new EntityNotFoundException('Client');
    }

    public function getDefaultClientOptions(): Collection{
        $query = $this->model->where('suspend', 0)->limit(50)->orderBy('id','desc');
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_clients', null, false)) {
            $query = $query->where('branch_id', Branch::getCurrentBranchID());
        }
        return $query->get();
    }

    public function getClientsWithMemberships()
    {
        return DB::connection('currentSite')->table('memberships')
            ->select('client_id')
            ->whereNull('deleted_at')
            ->distinct()->get()->pluck('client_id');
    }

    public function getClientsWithAcitveMemberships()
    {
        return DB::connection('currentSite')->table('memberships')
            ->select('client_id')
            ->where('status', '=', MemberShipStatusUtil::ACTIVE)
            ->whereNull('deleted_at')
            ->distinct()->get()->pluck('client_id');
    }
    public function getSelectedClient($clientConditions=[])
    {
        if (count($clientConditions) && isset($clientConditions['id'])) {
            $id = $clientConditions['id'];
            $this->applyBranches['onFind']=false;
            $client = $this->pushCriteria(new EqualCriteria('id' , $id))->all();
            if ($client) {
                return $client;
            }
        }
        return  $this->pushCriteria(new LimitCriteria())->all();
    }
    public function getDefaultOptions($limit = 50) :Collection
    {
        $query = $this->model->query()->latest('created');
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $shareAllClients = Settings::getValue(PluginUtil::BranchesPlugin, 'share_clients', null, false);
            if (!$shareAllClients) {
                $branchId = Branch::getCurrentBranchID();
                $query->where('branch_id', '=', $branchId);
            }
        }
        return $query->limit($limit)->get();
    }

    public function getClientsWithContactInfo($q, $branchId = null) : array
    {
        $query = $this->model->select('id', 'client_number', 'business_name', 'phone1', 'phone2', 'email')
            ->where('suspend', 0)
            ->where(function($query) use ($q){
                $query->where('client_number', 'like', '%' .$q. '%')
                    ->orWhere('business_name', 'like', '%' .$q. '%')
                    ->orWhere('phone1', 'like', '%' .$q. '%')
                    ->orWhere('phone2', 'like', '%' .$q. '%')
                    ->orWhere('email', 'like', '%' .$q. '%')
                    ->orWhere('id', $q);
            });
        if(!Permissions::checkPermission(PermissionUtil::Clients_View_All_Clients) && Permissions::checkPermission(PermissionUtil::Clients_View_his_own_Clients)){
            $staffId = getAuthOwner('staff_id');
            $query->where(function ($query) use ($staffId) {
               $query->where('staff_id', $staffId)->orWhereHas('assignedStaff', function($query) use ($staffId){
                    $query->where('staff_id', $staffId);
               });
            });
        }
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $shareAllClients = Settings::getValue(PluginUtil::BranchesPlugin, 'share_clients', null, false);
            if (!$shareAllClients) {
                $branchId = $branchId ?? Branch::getCurrentBranchID();
                $query->where('branch_id', '=', $branchId);
            }
        }
        $clients = $query->paginate(20);
        foreach($clients as &$client){
            $client->photo = $client->getPhotoFilePathAttribute();
        }
        return $clients->toArray();
    }
}
