<?php

namespace App\Repositories;

use App\Repositories\Interfaces\BankTransactionRepositoryInterface;
use Illuminate\Support\Collection;

class AssetRepository extends BaseRepository implements BankTransactionRepositoryInterface
{
    function model()
    {
        return 'App\\Models\\Asset';
    }

    public function getAssetsByIds($ids) : Collection {
        return $this->model->whereIn("id", $ids)->get();    
    }

    public function getAssetsWithAttachments($ids) : Collection {
        return $this->model->whereIn("id", $ids)->with(['attachments'])->get();    
    }

    public function updateJournalTransactionId($bankTransactionsIds, $system_transaction_id)
    {
        // Implement logic as needed for assets, or leave empty if not applicable
        return null;
    }

    public function getBankUnMatchedBankTransactions($bankId, $transactionType, $bankTransactionIds = [])
    {
        // Implement logic as needed for assets, or leave empty if not applicable
        return collect();
    }
}
