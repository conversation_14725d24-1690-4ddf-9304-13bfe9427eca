<?php

namespace App\Repositories;

use App\Repositories\Interfaces\BankTransactionRepositoryInterface;

class AssetLocationRepository extends BaseRepository implements BankTransactionRepositoryInterface
{
    function model()
    {
        return 'App\\Models\\AssetLocation';
    }

    public function findOrCreate($name)
    {
        return $this->model->firstOrCreate(['name' => $name]);
    }

    public function updateJournalTransactionId($bankTransactionsIds, $system_transaction_id)
    {
        // Implement logic as needed for asset locations, or leave empty if not applicable
        return null;
    }

    public function getBankUnMatchedBankTransactions($bankId, $transactionType, $bankTransactionIds = [])
    {
        // Implement logic as needed for asset locations, or leave empty if not applicable
        return collect();
    }
}
