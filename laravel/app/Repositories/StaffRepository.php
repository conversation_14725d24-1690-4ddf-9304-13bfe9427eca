<?php

namespace App\Repositories;

use App\Entities\AttendanceCalculationCriteria;
use App\Entities\AttendanceCalculationMultipleCriteria;
use App\Facades\Branch;
use App\Facades\Plugins;
use App\Models\ItemStaff;
use App\Models\Owner;
use App\Models\Staff;
use App\Models\StaffInfo;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;
use App\Repositories\Criteria\Criteria;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\SelectCriteria;
use App\Utils\ContractStatusUtil;
use App\Utils\ItemStaffUtil;
use App\Utils\PluginUtil;
use App\Utils\PostTypesUtil;
use Carbon\Carbon;
use Illuminate\Container\Container as App;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\Auth\AuthUserTypeUtil;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Daftra\Staff\Util\StaffTypeUtil;
use Illuminate\Support\Facades\Cache;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;
use Izam\Daftra\Staff\Util\StaffCitizenshipStatus;

class StaffRepository extends BaseRepository
{
    public $numberField = 'code';
    protected $paginateWith = ['role','defaultStore'];
    public $displayField = 'name';
    static $ownerStaff;
    public function __construct(App $app, Collection $collection)
    {
        parent::__construct($app, $collection);
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $this->paginateWith[] = 'branches';
        }
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $this->paginateWith[] = 'staff_info';
        }
        $this->applyBranches['onSave'] = true;
    }
    public function anyStaffHasActiveCommissionRule($staffIds,$commissionRuleId){

        $results = $this->model::whereHas('activeCommissionRules')
            ->whereIn('id',$staffIds)->get();
        if(!$results){
            return null;
        }
        foreach ($results as $result){
            if(count($result->activeCommissionRules) > 1 || $result->activeCommissionRules[0]->id !== (int)$commissionRuleId){
                return $result;
            }
        }
        return null;
    }

    public function getPaginationDefaultSorting()
    {
            return [
                'field' => 'active',
                'direction' => 'DESC'
            ];
    }

    public function getByPermission($permission, $includeAdmins = true, $includeOwner =true )
    {

        $qb = DB::connection('currentSite')->table('roles');
        $qb->leftJoin('roles_permissions', 'roles.id', '=', 'roles_permissions.role_id');

        if( $includeAdmins )
        {
               $qb->where('roles.is_super_admin',1);
        }

        $roles = $qb->orWhere('roles_permissions.permission_id',$permission)->select('roles.id')->get();
        $roleArray = [];
        foreach ($roles as $key=>$role)
            $roleArray[] = $role->id;


        $list = $this->findWhereIn('role_id',$roleArray);

        if( $includeOwner )
        {
            $siteId = getCurrentSite('id');
            $list->add( Owner::find($siteId));
        }

        return $list;
    }

    function model()
    {
        return 'App\Models\Staff';
    }

    public function saveStaffInfo($staff, $info)
    {
        return $staff->staff_info()->updateOrCreate(['staff_id' => $staff->id], $info);
    }

    public function saveStaffJob($staff, $job)
    {
        return $staff->staff_job()->updateOrCreate(['staff_id' => $staff->id], $job);
    }

    public function getEntityPluralName()
    {
        return __t('Employees');
    }

    /**
     * @param array $customFindCriteria
     * @return array
     */
    public function getStaffsFullName($customFindCriteria = [])  : array
    {
        if (!empty($customFindCriteria)) {
            foreach ($customFindCriteria as $customFind) {
                if ($customFind instanceof CustomFind) {
                    $this->pushCriteria($customFind);
                }
            }
        }
        $staffs = $this->all(['id','name','last_name','middle_name']);
        $staffArray = [];
        foreach ($staffs as $staff) {
            $staffArray[$staff->id] = $staff->name  . ' ' . $staff->middle_name  . ' ' . $staff->last_name;
        }
        return $staffArray;
    }

    public function getEntitySingularName()
    {
        return __t('Employee');
    }

    public function getAttendanceDayCriteriaStaff(AttendanceCalculationCriteria $criteria, $allowInActive = false)
    {
        $queryBuilder = DB::connection('currentSite')
            ->table('staffs as S')
            ->leftJoin('staff_info AS SI', 'S.id', '=', 'SI.staff_id')
            ->leftJoin('staff_job AS SJ','S.id', '=', 'SJ.staff_id')
            ->whereNull('S.deleted_at');
        $employeeIds = $criteria->getEmployeeIds();
        if($employeeIds)
        {
            $queryBuilder->whereIn('S.id', $employeeIds);
        }else{
            $branch = $criteria->getBranchId();
            if($branch)
            {
                $queryBuilder->where('S.branch_id', $branch);
            }
            $department = $criteria->getDepartmentId();
            if($department)
            {
                $queryBuilder->where('SI.department_id', $department);

            }
            $shiftId = $criteria->getShiftId();
            if($shiftId)
            {
                $queryBuilder->where('SI.attendance_shift_id', $shiftId);

            }
            $designationId = $criteria->getDesignationId();
            if($designationId)
            {
                $queryBuilder->where('SI.designation_id', $designationId);
            }
            $excludedEmployees = $criteria->getExcludedEmployees();
            if($excludedEmployees) {
                $queryBuilder->whereNotIn('S.id', $excludedEmployees);
            }
        }

        if (!$allowInActive) {
            $queryBuilder->where('S.active', 1) ;
        }
        $results = $queryBuilder->selectRaw('id,
        SJ.fiscal_day,
        SJ.fiscal_month,
        S.active,
        CONVERT(if(SJ.join_date, SJ.join_date, S.created), DATE) AS join_date,
               S.branch_id, SI.attendance_shift_id, SI.has_secondary_shift, SI.secondary_shift_id, SI.department_id, SI.designation_id')->get();

        return $results;
    }

    public function allPlusOwner()
    {
        $staffs = $this->model
            ->selectRaw("id, name, concat(name,' ',IFNULL(last_name,'')) as full_name")
            ->where('deleted_at', null)
            ->get();

        $owner = new \stdClass();
        $owner->full_name = getAuthOwnerName();
        $owner->id = 0;
        $staffs->prepend($owner);

        return $staffs;
    }

    public function getAutoCompleteResult($q, $allowInActive = false, array $accessibleBranchIds = null, $selected = [])
    {
        $query = $this->model->where(function ($query) use ($q) {
            $query->orWhere('name', 'like', '%' . $q . '%')
                ->orWhere('id', '=', $q)
                ->orWhere('code', '=', $q)
		        ->orWhere('code', 'like', '%' . $q . '%')
                ->orWhere('middle_name', 'like', '%' . $q . '%')
                ->orWhere('last_name', 'like', '%' . $q . '%')
                ->orWhere(DB::raw("CONCAT(`name`, ' ', `middle_name`,' ',`last_name`) COLLATE utf8_bin"), "like", '%' . $q . '%')
                ->orWhere(DB::raw("CONCAT(`name`,' ',`last_name`) COLLATE utf8_bin"), "like", '%' . $q . '%')
                ->orWhere(DB::raw("CONCAT(`name`, ' ', `middle_name`, ' ', `last_name`)"), "like", '%' . $q . '%')
                ->orWhere(DB::raw("CONCAT(`name`, ' ', `last_name`)"), "like", '%' . $q . '%')
                ->orWhere('email_address', 'like', '%' . $q . '%');

        });

        if(isset($accessibleBranchIds)){
            $query->whereIn('branch_id',$accessibleBranchIds);
        }

        if(!empty($selected)){
            $query->whereNotIn('id', $selected );
        }

        if (!$allowInActive) {
            $query = $query->where(function ($query)
            {
                $query->where('active', '=', 1);
            });
        }
        return $query->get();
    }


    public function getShifts($staff_id)
    {
        return \DB::connection('currentSite')->table('shifts')->join('item_staffs', 'shifts.id', 'item_staffs.item_id')
            ->where('item_staffs.staff_id', $staff_id)->where('item_staffs.item_type', 10)->whereNull('shifts.deleted_at')->get();
    }

    public function getPostsCount($id)
    {
        return $query = DB::connection('currentSite')->table('posts')
            ->selectRaw('Count(*) as count')
            ->where('item_type', '=', PostTypesUtil::STAFF_TYPE)
            ->where('item_id', '=', $id)
            ->get()->pluck('count')->toArray()[0];
    }

    public function isManager($id)
    {
        $record = $this->find($id);
        return $record->managedDepartments->first();
    }

    public function getStaffInfo($id){
        return StaffInfo::where('staff_id', $id)->first();
    }

    public function isDirectManager($id)
    {
        $staffInfo = StaffInfo::where('direct_manager_id', $id)->get();

        if(count($staffInfo) > 0){
            $employeesIds = [];
            foreach($staffInfo as $employee){
                $employeesIds[] = $employee->staff_id;
            }
            $staffs = $this->findWhereIn("id",$employeesIds);
            if(count($staffs) > 0){
                return $staffs[0];
            }
        }
        return null;
    }

    public function hasActiveContract($employee_id){
        return DB::connection('currentSite')->table('contracts')
            ->where('staff_id', '=', $employee_id)
            ->whereIn('status', ContractStatusUtil::getAutomatic())
            ->whereNull('deleted_at')
            ->count();
    }

    /**
     * @param int $id
     * @return int
     */
    public function getContractsCount(int $id)
    {
        return DB::connection('currentSite')->table('contracts')->where('staff_id',$id)->whereNull('deleted_at')->count();
    }

    public function hasLoans($id){
        return (bool) $this->find($id)->loans()->count();
    }

    public function getStaffByAppointmentId($appointmentId){
        return $this->getStaffNameByItemId(ItemStaffUtil::APPOINTMENT_ITEM_TYPE,$appointmentId);
    }



    public function getStaffNameByItemId($itemType,$itemId){
       return DB::connection('currentSite')->table('item_staffs')->join('staffs','item_staffs.staff_id','=','staffs.id')
        ->where('item_type',$itemType)->where('item_id',$itemId)
        ->select('staffs.name')->get()->map(function ($record){
            return $record->name;
           });
    }

    /**
     * @param int $id
     * @return int
     */
    public function incrementAuthId(int $id)
    {
        /* revoke all access tokens and refresh tokens for this user */
        AuthHelper::revoke(AuthUserTypeUtil::STAFF, $id);
        return DB::connection('currentSite')->table('staffs')->where('id',$id)->increment('auth_id');
    }

    /**
     * @param $ids
     * @return array
     */
    public function getActiveStaffList(?string $q, array $ids = [])
    {
        $query = $this->model->whereActive(true)
        ->select(\DB::raw('id, TRIM(REPLACE(CONCAT(IFNULL(name, "")," ", IFNULL(middle_name, "")," ",IFNULL(middle_name, ""), " #", code, " ", "(", ifnull(email_address, "") COLLATE utf8_general_ci, ")"), "()", "")) as text, photo'))
        ->where(function ($query) use ($q) {
                $query->orWhere('name', 'like','%'.$q.'%')
                    ->orWhere('id', '=', $q)
                    ->orWhere('code', '=', $q)
                    ->orWhere('code', 'like','%'.$q.'%')
                    ->orWhere('middle_name', 'like','%'.$q.'%')
                    ->orWhere('last_name', 'like','%'.$q.'%')
                    ->orWhere('email_address', 'like', '%'.$q.'%');

            });
        !empty($ids) ? $query->whereIn('id', $ids) : $query;
        return $query->get();
    }

    /**
     * @param string $query
     * @param array $ids
     * @return mixed
     */
    public function getEmployeesAutoCompleteForRequests(string $query, array $ids)
    {
        $builder = $this->model
            ->whereIn('id', $ids)
            ->where(function ($builder) use ($query, $ids) {
                $builder->orWhere('name', 'like', '%' . $query . '%')
                    ->orWhere('id', '=', $query)
                    ->orWhere('middle_name', 'like', '%' . $query . '%')
                    ->orWhere(DB::raw("CONCAT(`name`, ' ', `middle_name`,' ',`last_name`)"), "like", '%' . $query . '%')
                    ->orWhere(DB::raw("CONCAT(`name`,' ',`last_name`)"), "like", '%' . $query . '%')
                    ->orWhere('last_name', 'like', '%' . $query . '%')
                    ->orWhere('email_address', 'like', '%' . $query . '%');

            });
        return $builder->get();
    }

    /**
     * @return mixed
     */
    public function activeEmployeeCount(): int
    {
        return $this->model->where([
            ['type', StaffTypeUtil::EMPLOYEE],
            ['active', 1]
        ])->count();
    }

    /**
     * @return mixed
     */
    public function activeUsersCount(): int
    {
        $activeUsersCount = $this->model->where([
            ['type', StaffTypeUtil::USER],
            ['active', 1]
        ])->count();
        $ownerUser = $this->getOwnerUser();
        if($ownerUser){
            $activeUsersCount--;
        }
        return $activeUsersCount;
    }

    /**
     * @return mixed
     */
    public function activeStaffCount(): int
    {
        return $this->model->where('active', 1)->count();
    }

    public function getFirstStaffMemberByAttendanceRestrictionId(int $restriction_id): object|null
    {
        return $this->model->where([
            ['attendance_restriction_id', $restriction_id]
        ])->select('id','name','last_name')->first();
    }

    /**
     * @param $department_id
     * @return Collection
     */
    public function getStaffByDepartmentId($department_id): Collection
    {
        return $this->model->whereHas('staff_info', function ($query) use ($department_id) {
            $query->where('department_id', $department_id);
        })->get();
    }

    public function getWithHolidayLists() {
        $this->applyCriteria();
        return $this->model->with('holiday_lists')->get()->keyBy('id');
    }

    public function getWithAttendanceRestriction() {
        $this->applyCriteria();
        return $this->model->with('attendanceRestriction')->get()->keyBy('id');
    }

    public function getWithAttendanceShfit() {
        $this->applyCriteria();
        return $this->model->with('attendance_shift')->get()->keyBy('id');
    }

    public function getWithLeavePolicy() {
        $this->applyCriteria();
        return $this->model->with('leave_policy')->get()->keyBy('id');
    }

    function paginate($limit = 20, $with = [], $pageNumber = null)
    {
        $this->pushCriteria(new SelectCriteria('staffs.*', false));
        return parent::paginate($limit, $with, $pageNumber);
    }

    public function getByCriteria(Criteria $criteria) {
        $this->pushCriteria(new SelectCriteria('staffs.*', false));
        return parent::getByCriteria($criteria);
    }

    /**
     * returns staff members managed by a given direct manager id
     *
     * @param  mixed $directManagerId
     */
    public function directManagedStaffbyAuthUser($directManagerId)
    {
        return $this->model->whereHas('staff_info', function ($query) use ($directManagerId) {
            $query->where('direct_manager_id', $directManagerId);
        })->get();
    }

    /**
     * returns staff members managed department manager
     *
     * @param  mixed $managerId
     */
    public function getStaffByDepartmentManagerId($managerId)
    {
        return $this->model->whereHas('department.managers', function ($query) use ($managerId) {
            $query->where('department_managers.manager_id', $managerId);
        })->get();
    }

    /**
     * returns all staff members ids
     *
     * @return array
     */
    public function getAllStaffIds() : array{
       return $this->model->all()->pluck('id')->toArray();
    }

    function getStaffIdsByWorkingBranchesIds(array $workingBranchesIds): array
    {
        if (!count($workingBranchesIds)) {
            return [];
        }
        return $this->model->whereHas('branch', function ($query) use ($workingBranchesIds) {
            $query->whereIn('id', $workingBranchesIds);
        })->pluck('id')->toArray();
    }

    function getStaffIdsByDesignationIds(array $designationIds)
    {
        return $this->model->whereHas('staff_info', function ($query) use ($designationIds) {
            $query->whereIn('designation_id', $designationIds);
        })->pluck('id')->toArray();
    }
    public function getActiveUsers()
    {
        return $this->model->where([
            ['type', StaffTypeUtil::USER],
            ['active', 1]
        ])->pluck('id')->toArray();
    }

    function getStaffByIds(array $staffIds) : Collection{
        return $this->model->whereIn('id', $staffIds)->get();
    }

    public function getDefaultActiveOptions($limit = 50) : Collection {
        return $this->model->where('active', 1)->limit($limit)->get();
    }
    public function getWithRelations($staffId, array $relations = []) {
        $this->applyCriteria();
        return $this->model->where('id' , $staffId)->with($relations)->first();
    }

    public function getStaffShiftsAndPolicies($staffId)
    {
        return $this->model
            ->where('id' ,$staffId)
            ->with([
            'attendanceRestriction',
            'holiday_lists',
            'staff_info.leavePolicy',
            'staff_info.attendanceShift',
            'staff_info.secondaryShift',
        ])->first();
    }

    public function getStaffWithAttendanceLogs($staffId)
    {
        return $this->model->with('attendanceLogs.attendance_restriction_log')->where('id' ,$staffId )->first();
    }

    function getOwnerUser($ignoreCache = false){
        $cachedOwnerStaff = self::$ownerStaff;
        if(!$ignoreCache && $cachedOwnerStaff){
            return $cachedOwnerStaff;
        }else{
            $ownerStaff = DB::connection('currentSite')->table('staffs')->where('role_id',Staff::OWNER_ROLE_ID)->first();
            if($ownerStaff){
                self::$ownerStaff = $ownerStaff;
                return $ownerStaff;
            }
        }
        return null;
    }

    public function getAttendanceDayMultipleCriteriaStaff(AttendanceCalculationCriteria $criteria, $allowInActive = false)
    {
        $queryBuilder = DB::connection('currentSite')
            ->table('staffs as S')
            ->leftJoin('staff_info AS SI', 'S.id', '=', 'SI.staff_id')
            ->leftJoin('staff_job AS SJ', 'S.id', '=', 'SJ.staff_id')
            ->whereNull('S.deleted_at');
        $employeeIds = $criteria->getEmployeeIds();
        if ($employeeIds) {
            $queryBuilder->whereIn('S.id', $employeeIds);
        } else {
            $branches = $criteria->getBranches();
            if (count($branches)) {
                $queryBuilder->whereIn('S.branch_id', $branches);
            }
            $departments = $criteria->getDepartments();
            if (count($departments)) {
                $queryBuilder->whereIn('SI.department_id', $departments);
            }
            $shifts = $criteria->getShifts();
            if (count($shifts)) {
                $queryBuilder->whereIn('SI.attendance_shift_id', $shifts);
            }
            $designations = $criteria->getDesignations();
            if (count($designations)) {
                $queryBuilder->whereIn('SI.designation_id', $designations);
            }
            $excludedEmployees = $criteria->getExcludedEmployees();
            if ($excludedEmployees) {
                $queryBuilder->whereNotIn('S.id', $excludedEmployees);
            }
        }

        if (!$allowInActive) {
            $queryBuilder->where('S.active', 1);
        }
        $results = $queryBuilder->selectRaw('id,
        SJ.fiscal_day,
        SJ.fiscal_month,
        S.active,
        CONVERT(if(SJ.join_date, SJ.join_date, S.created), DATE) AS join_date,
               S.branch_id, SI.attendance_shift_id, SI.has_secondary_shift, SI.secondary_shift_id, SI.department_id, SI.designation_id')->get();

        return $results;
    }
    public function getDefaultStaffOptions() : Collection{
        $accessibleBranchesIds = null;
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $accessibleBranchesIds = Branch::getStaffBranchesIds();
        }
        return $this->model->when($accessibleBranchesIds, fn($q) => $q->whereIn('branch_id', $accessibleBranchesIds))->where('active', 1)->limit(50)->orderBy('id','desc')->get();
    }

    public function getInactiveStaffByIds(array $staffIds) : Collection{
        return $this->model->whereIn('id', $staffIds)->where('active', 0)->get();
    }

    public function findByFullNameOrId($fullNameOrId)
    {
        return $this->model->where(function ($query) use ($fullNameOrId) {
            $query->where('name', 'like', '%' . $fullNameOrId . '%')
                ->orWhere('middle_name', 'like', '%' . $fullNameOrId . '%')
                ->orWhere('last_name', 'like', '%' . $fullNameOrId . '%')
                ->orWhere(DB::raw("CONCAT(`name`, ' ', `middle_name`,' ',`last_name`)"), "like", '%' . $fullNameOrId . '%')
                ->orWhere(DB::raw("CONCAT(`name`,' ',`last_name`)"), "like", '%' . $fullNameOrId . '%')
                ->orWhere('id', $fullNameOrId)
                ->orWhere('code', $fullNameOrId);
        })->get();
    }

    public function isAssignedToClient($id)
    {
        return ItemStaff::where(['staff_id' => $id, 'item_type' => 1])->first();
    }

    public function getAllStaffManagedDeparments($staffId)
    {
        $record = $this->find($staffId);
        return $record?->managedDepartments->all() ?? [];
    }

    public function findFirstToExpireContracts(int $limit, $accessibleBranchStaffIds = null): Collection
    {
        $today = Carbon::now()->toDateString();
        $threeMonthsLater = Carbon::now()->addMonths(3)->toDateString();

        $expiringStaffs = $this->model
            ->where('residence_expiry_date', '>=', $today)
            ->where('residence_expiry_date', '<=', $threeMonthsLater)
            ->where('active', '1')
            ->where('citizenship_status', StaffCitizenshipStatus::RESIDENT)
            ->when(!empty($accessibleBranchStaffIds), fn($query) => $query->whereIn('id', $accessibleBranchStaffIds))
            ->orderBy('residence_expiry_date', 'asc')
            ->limit($limit)
            ->get();

        $limit = $limit - $expiringStaffs->count();
        $expiredStaffs = $this->model
            ->where('residence_expiry_date', '<', $today)
            ->where('active', '1')
            ->where('citizenship_status', StaffCitizenshipStatus::RESIDENT)
            ->when(!empty($accessibleBranchStaffIds), fn($query) => $query->whereIn('id', $accessibleBranchStaffIds))
            ->orderBy('residence_expiry_date', 'desc')
            ->limit($limit)
            ->get();

        $allStaffs = $expiringStaffs->concat($expiredStaffs);

        return $allStaffs;
    }

    public function getStaffsNameAndPhoto($q, $fromAccessibleBranches = false){
        $query = $this->model->select('id', 'full_name', 'email_address', 'code', 'photo')->where('active', 1)
        ->where(function($query) use ($q){
            $query->where('code', 'like', '%' .$q. '%')
                ->orWhere('full_name', 'like', '%' .$q. '%')
                ->orWhere('email_address', 'like', '%' .$q. '%')
                ->orWhere('id', $q);
        });
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            if(!$fromAccessibleBranches){
                $query->where('branch_id', '=', Branch::getCurrentBranchID());
            }else{
                $accessibleBranchIds = Branch::getStaffBranchesIds();
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }
        $staffs = $query->paginate(20);

        foreach($staffs as &$staff){
            $staff->photo = AvatarURLGenerator::generate($staff->full_name, $staff->id, 78, $staff->photo);
        }

        return $staffs->toArray();
    }

    public function hasAssignedAsApproverInConfigurationCycle($id){
        $multiCycleApprovalConfigurationService = resolve(MultiCycleApprovalConfigurationService::class);
        $configurations = $multiCycleApprovalConfigurationService->getConfigurations();
        $resultedConfigurations = [];
        foreach($configurations as $configuration){
            if($configuration->getStatus() == MultiCycleApprovalUtil::STATUS_ACTIVE){
                foreach ($configuration->getApprovalLevels() as $approvalLevel) {
                    if(in_array($id, $approvalLevel->getEmployees())) {
                        $resultedConfigurations[] = $configuration; 
                    }
                }
            }
        }
        return $resultedConfigurations;
    }

    public function getStaffForShiftUpdate($oldShiftId = null)
    {
        return $this->model->where(function ($q) use ($oldShiftId) {
            if ($oldShiftId) {
                $q->whereHas('shift', function ($qq) use ($oldShiftId) {
                    $qq->where('shifts.id', $oldShiftId);
                })->orWhereDoesntHave('shift');
            } else {
                $q->whereDoesntHave('shift');
            }
        })->get();
    }

}
