<?php

namespace App\Repositories;

use App\Adapters\EntityFieldAdapterCustomField;
use App\Facades\Plugins;
use App\Models\Entity;
use App\Modules\Template\Models\LocalTemplate;
use App\Modules\Template\Utils\TemplateTypeUtil;
use App\Utils\Cache\EntitiesCachingKeysUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyPluginMappingUtil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EntityRepository extends BaseRepository
{
    public $applyBranches = ['onSave' => false, 'onFind' => false];
    /**
     * @var array key value with entity key with it's fields
     */
    public static $entityFields = [];

    /**
     * @var array entity fields with modes
     */
    public static $entityFieldsWithMode = [];

    /**
     * @var array entities
     */
    public static $entities = [];


    /**
     * {@inheritDoc}
     */
    public function model()
    {
        return Entity::class;
    }

    /**
     * @param $entityKey
     * @param string|null $operation
     * @param bool $already_deep
     * @return array|mixed
     */
    public function getEntityFields($entityKey, string $operation = null, $already_deep = false)
    {
        $activePlugins = array_unique(Plugins::getPluginArray());
        $mainEntity = Cache::get(EntitiesCachingKeysUtil::ENTITIES)[$entityKey] ?? null;

        if (!$mainEntity) {
         //   Log::error("Can't fetch main entity in getEntityFields {$entityKey}", ['message' => (new \Exception("Can't fetch main entity in getEntityFields {$entityKey}"))->getMessage()]);
            return collect([]);
        }
        foreach ($mainEntity->fields as $key => $item) {
            if (!empty($item->plugin_id) && !in_array($item->plugin_id, $activePlugins)) {
                $mainEntity->fields->forget($key);
            }
            if ($operation && in_array($operation, ['create', 'update', 'import', 'export'])) {
                $field = "on_" . $operation;
                if ($item->$field != 1) {
                    $mainEntity->fields->forget($key);
                }
            }
        }
        $return = $mainEntity->fields;
        if ($mainEntity->customForm) {
            $customForm = $mainEntity->customForm;
            $adaptedEntityFields = [];
            foreach ($customForm->customFormFields as $listingCustomField) {
                $adaptedEntityFields[] = new EntityFieldAdapterCustomField($mainEntity, $customForm, $listingCustomField);
            }
            $return = $return->concat($adaptedEntityFields);
        }

        foreach ($mainEntity->relatedEntities as $relatedEntity) {
            $this->makeModel();
            if (!isset($relatedEntity->plugin_id) || Plugins::pluginActive($relatedEntity->plugin_id)) {
                $return = $return->concat($this->getEntityFields($relatedEntity->key, $operation, $already_deep));
            }
        }

        foreach ($return as $field) {
            if ( in_array($field->field_type ,[EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY , EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF] ) && !$already_deep && isset($field->relation)) {
                $return = $return->concat($this->getEntityFields($field->relation->reference_entity_key, $operation, true));
            }
        }
        $return = $return ? $return->unique('key') : [];
        return $return;
    }

    public function findRepositoryRow( $entity_key, $id )
    {
        $plugins = EntityKeyPluginMappingUtil::getAll();

        if (isset($plugins[$entity_key])) {
            if (!Plugins::pluginActive($plugins[$entity_key])) {
                return null;
            }
        }

        $model_with_namespace = 'App\Models\\' . ucfirst(Str::camel($entity_key));
        $model_instance = resolve($model_with_namespace);
        $model = call_user_func( [$model_instance, 'find'], $id);

        return $model;
    }

    /**
     * @param string $entityKey
     * @return Model|null
     */
    public function findOneByKey(string $entityKey)
    {
        if(empty(self::$entities)){
            self::$entities = Cache::get(EntitiesCachingKeysUtil::ENTITIES);
        }
        return self::$entities[$entityKey] ?? null;
    }

    public function getAllEntitiesFromCache(){
        if(empty(self::$entities)){
            self::$entities = Cache::get(EntitiesCachingKeysUtil::ENTITIES);
        }
        if(empty(self::$entities)){
            self::$entities = parent::all()->mapWithKeys(function ($entity) {
                return [$entity->key => $entity];
            });
            Cache::put(EntitiesCachingKeysUtil::ENTITIES, self::$entities);
        }
        return self::$entities;
    }

    public function getEntityFieldsWithMode( $entity_key, $mode, $field_types=[] )
    {
        $entityFieldsWithModeKey = $entity_key . $mode . implode(',', $field_types);
        if (isset(static::$entityFieldsWithMode[$entityFieldsWithModeKey])) {
            return static::$entityFieldsWithMode[$entityFieldsWithModeKey];
        }
        // get active plugin ids to get fields based on it
        $active_plugins_ids = array_unique(Plugins::getPluginArray());
        $active_plugins_ids[] = 0;
        $entity = $this->findOneByKey($entity_key);
        if (!$entity) {
         //   Log::error("Can't fetch entity in getEntityFieldsWithMode {$entity_key}", ['message' => (new \Exception("Can't fetch entity in getEntityFieldsWithMode {$entity_key}"))->getMessage()]);
            static::$entityFieldsWithMode[$entityFieldsWithModeKey] = collect();
            return static::$entityFieldsWithMode[$entityFieldsWithModeKey];
        }

        $return = $this->findOneByKey($entity_key)->fields()->with('relation', 'type')->whereHas('type', function ($query) use ($mode, $field_types) {
            $query->whereJsonContains('modes', $mode);
            if( $field_types ) {
                $query->whereIn('field_type', $field_types);
            }
        })->Where(function ($query) use ($active_plugins_ids) {
            $query->whereNull('plugin_id')->orWhereIn('plugin_id', $active_plugins_ids);
        })->get();
        static::$entityFieldsWithMode[$entityFieldsWithModeKey] = $return;
        return $return;
    }

    protected static $entitiesWithRelation = [];
    /**
     * @param string $entityKey
     * @return array
     */
    public function getEntityRelations(string $entityKey)
    {
        if (empty(self::$entitiesWithRelation)) {
            self::$entitiesWithRelation = parent::all(['*'], ['relatedEntities'])->mapWithKeys(function ($entity) {
                return [$entity->key => $entity];
            });
        }
        $activePlugins = array_unique(Plugins::getPluginArray());
        $relatedEntities = self::$entitiesWithRelation[$entityKey]->relatedEntities ?? collect();
        foreach ($relatedEntities as $key => $relatedEntity) {
            if (!is_null($relatedEntity->plugin_id) && !in_array($relatedEntity->plugin_id, $activePlugins)) {
                $relatedEntities->forget($key);
            }
        }
        return $relatedEntities;
    }

    /**
     * @param string $entityKey
     * @return array mixed
     * returns [field_type => field db_name]
     */
    function getEntityFieldTypes(string $entityKey = null): array
    {
        $entityFields = $this->getEntityFields($entityKey);
        if (!$entityFields) {
            return [];
        }
        return $entityFields->pluck('field_type', 'db_name')->toArray();
    }

    /**
     * @param string $entityKey
     * @return Model|null
     */
    public static function getEntityByKey(string $entityKey)
    {
        return Cache::get(EntitiesCachingKeysUtil::ENTITIES)[$entityKey] ?? null;
    }

    public function cacheEntities()
    {
        $entities = $this->getAllEntitiesFields()->mapWithKeys(function ($entity) {
            return [$entity->key => $entity];
        });

        Cache::forever(EntitiesCachingKeysUtil::ENTITIES, $entities);
    }

    /**
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection|Model[]
     */
    private function getAllEntitiesFields()
    {
        return $this->model->with(['fields', 'relatedEntities', 'fields.relation'])->get();
    }

    /**
     *{@inheritDoc}
     */
    public function find($entityKey)
    {
        return Cache::get(EntitiesCachingKeysUtil::ENTITIES)[$entityKey] ?? parent::find($entityKey);
    }

    /**
     * {@inheritDoc}
     */
    public function all($columns = ['*'], $with = [])
    {
        $entities = Cache::get(EntitiesCachingKeysUtil::ENTITIES);
        if (empty($entities)) {
            $entities = parent::all($columns, $with)
                ->mapWithKeys(function ($entity) {
                    return [$entity->key => $entity];
                });
            Cache::put(EntitiesCachingKeysUtil::ENTITIES, $entities);
        }
        return  $entities;
    }

    public function getGlobalEntitiesThatHasAvailableGlobalTemplateByIndustryId($industryId = null, $type = TemplateTypeUtil::PDF)
    {
        return $this->model
            ->whereIn('key', function($query) use ($industryId, $type) {
                return $query
                    ->select('entity_key')
                    ->distinct()
                    ->from('global_templates')
                    ->where('type', $type)
                    ->whereIn('id', function($query) use ($industryId) {
                        $query
                            ->select('template_id')
                            ->from('template_industries')
                            ->where('listing_mode', 'available');
                        if ($industryId) {
                            $query->whereRaw("(`industry_id` = $industryId OR `industry_id` = -1)");
                        } else {
                            $query->where('industry_id', '=', '-1');
                        }
                        return $query;
                    });
            })->get()->all();
    }

    public function getGlobalEntitiesThatHasLocalTemplate(string $type)
    {
        $entityKeys = LocalTemplate::where('type', $type)
            ->selectRaw('entity_key')->distinct()->pluck('entity_key')->toArray();

        return $this->model->WhereIn('key', $entityKeys)->get()->all();
    }

    public function getEntitiesByEntityKey($entityKeys)
    {
        return $this->model->WhereIn('key', $entityKeys)->get()->all();
    }

    public function updateFollowUpStatus($entityKey, $entityId, $statusId)
    {
        $entity = $this->find($entityKey);
        if($entity){
            return DB::connection('currentSite')->table($entity->db_name)->where('id', $entityId)->update(['status_id' => $statusId]);
        }
        return null;
    }
}
