<?php
namespace App\Repositories;

use App\Models\EntityDocument;
use App\Models\EntityDocumentType;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EntityDocumentRepository extends BaseRepository  {


    function model()
    {
        return 'App\Models\EntityDocument';
    }


    /**
     * Get documents that will expire within a month and have is_expirable=1 in their document type
     *
     * @param string|null $entity_key
     * @param int|null $entity_id
     * @param bool $excludeIfNewerExists If true, excludes expiring documents if a newer non-expiring version exists
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDocumentsExpiringWithinMonth($entity_key = null, $entity_id = null, $excludeIfNewerExists = false)
    {
        $now = Carbon::today();
        $oneMonthLater = Carbon::today()->addMonth();

        $query = $this->model
            ->whereHas('documentType', function($query) {
                $query->where('is_expirable', 1);
            })
            ->whereBetween('expiry_date', [$now, $oneMonthLater])
            ->when($entity_id, function($query) use($entity_id) {
                $query->where('entity_id', $entity_id);
            })
            ->when($entity_key, function($query) use($entity_key) {
                $query->where('entity_key', $entity_key);
            });

        if ($excludeIfNewerExists) {
            // Get document type IDs that have documents not expiring soon
            $nonExpiringDocumentTypeIds = EntityDocument::
                whereHas('documentType', function($query) {
                    $query->where('is_expirable', 1);
                })
                ->where('expiry_date', '>', $oneMonthLater)
                ->when($entity_id, function($query) use($entity_id) {
                    $query->where('entity_id', $entity_id);
                })
                ->when($entity_key, function($query) use($entity_key) {
                    $query->where('entity_key', $entity_key);
                })
                ->pluck('entity_document_type_id')
                ->toArray();

            // Exclude documents expiring within a month if a newer version exists that isn't expiring soon
            $query->whereNotIn('entity_document_type_id', $nonExpiringDocumentTypeIds)
            ->select(\DB::raw('MAX(id) as id'))
            ->groupBy('entity_document_type_id');
        }

        $latestExpiredIds = $query->pluck('id');

        // Get the actual document records
        return $this->model
            ->whereIn('id', $latestExpiredIds)
            ->get();
    }


   /**
     * Get documents that have already expired and have is_expirable=1 in their document type
     *
     * @param string|null $entity_key
     * @param int|null $entity_id
     * @param bool $excludeIfNewerExists If true, excludes expired documents if a newer non-expired version exists
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getExpiredDocuments($entity_key = null, $entity_id = null, $excludeIfNewerExists = false)
    {
        $now = Carbon::today();

        $baseQuery = $this->model
            ->whereHas('documentType', function ($query) {
                $query->where('is_expirable', 1);
            })
            ->where('expiry_date', '<', $now)
            ->when($entity_id, function ($query) use ($entity_id) {
                $query->where('entity_id', $entity_id);
            })
            ->when($entity_key, function ($query) use ($entity_key) {
                $query->where('entity_key', $entity_key);
            });

        if ($excludeIfNewerExists) {
            // Get document type IDs that have non-expired documents
            $nonExpiredDocumentTypeIds = EntityDocument::
                whereHas('documentType', function ($query) {
                    $query->where('is_expirable', 1);
                })
                ->where('expiry_date', '>=', $now)
                ->when($entity_id, function ($query) use ($entity_id) {
                    $query->where('entity_id', $entity_id);
                })
                ->when($entity_key, function ($query) use ($entity_key) {
                    $query->where('entity_key', $entity_key);
                })
                ->pluck('entity_document_type_id')
                ->toArray();

                $baseQuery->whereNotIn('entity_document_type_id', $nonExpiredDocumentTypeIds)
                ->select(\DB::raw('MAX(id) as id'))
                ->groupBy('entity_document_type_id');
        }

        $latestExpiredIds = $baseQuery->pluck('id');

        // Get the actual document records
        return $this->model
            ->whereIn('id', $latestExpiredIds)
            ->get();
    }



    /**
     * Get required document types that have not been uploaded for a specific entity
     *
     * @param string|null $entity_key
     * @param int|null $entity_id
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRequiredAndNotUploadedDocuments($entity_key = null, $entity_id = null)
    {
        // Get all required document types
        $requiredDocumentTypes = EntityDocumentType::where('is_required', 1)->get();
        // Get IDs of document types that have already been uploaded for this entity
        $uploadedDocumentTypeIds = $this->model
            ->when($entity_id, function($query) use($entity_id) {
                $query->where('entity_id', $entity_id);
            })
            ->when($entity_key, function($query) use($entity_key) {
                $query->where('entity_key', $entity_key);
            })
            ->pluck('entity_document_type_id')
            ->toArray();
        // Filter out document types that have already been uploaded
        return $requiredDocumentTypes->whereNotIn('id', $uploadedDocumentTypeIds);
    }


    public function getDocumentAlertsCount($entity_key = null, $entity_id = null)
    {
        $alerts = $this->getRequiredAndNotUploadedDocuments($entity_key, $entity_id)->count()
        + $this->getDocumentsExpiringWithinMonth($entity_key, $entity_id, true)->count()
        + $this->getExpiredDocuments($entity_key, $entity_id, true)->count();
        return $alerts;
    }

    public function getDocumentsByIds($documentsIds)
    {
        return $this->model
                ->whereIn('id', $documentsIds)
                ->with(['entityAttachment.files', 'documentType', 'createdBy'])
                ->get();
    }


    public function documentTypesCount($entity_key = null, $entity_id = null){

        return $this->model
        ->select('entity_document_type_id', \DB::raw('COUNT(*) as document_count'))
        ->when($entity_id, function ($query) use ($entity_id) {
            $query->where('entity_id', $entity_id);
        })
        ->when($entity_key, function ($query) use ($entity_key) {
            $query->where('entity_key', $entity_key);
        })
        ->groupBy('entity_document_type_id')
        ->get()->pluck('document_count', 'entity_document_type_id')->toArray();
    }

    public function getLatestStaffDocumentsIds(string $entity_id)
    {
        return DB::connection('currentSite')
            ->table('entity_documents')
            ->where('entity_key', 'staff')
            ->where('entity_id', $entity_id)
            ->selectRaw('MAX(id) as max_id')
            ->groupBy('entity_document_type_id')
            ->pluck('max_id')
            ->toArray();
    }
}
