<?php

namespace App\Repositories;

use App\Facades\Branch;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Repositories\Criteria\LimitCriteria;
use App\Helpers\AutoNumber;
use App\Utils\PluginUtil;
use App\Adapters\InternalHttpAdapter;
use App\Models\Product;
use App\Models\ProductItemBarcode;
use App\Repositories\Criteria\OrderByCriteria;
use App\Utils\ProductTypeUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Illuminate\Support\Collection;
use App\Modules\PriceList;

class ProductRepository extends BaseRepository
{
    public $listingColumns = ['id', 'name'];

    /** Branch filtering requested By Bilal to match Cake's implementation */
    public $applyBranches = ['onSave' => true, 'onFind' => true];

    function model()
    {
        return Product::class;
    }

    /**
     *
     * @return $product_id
     */
    public function insertProductInCake(array $criteria)
    {
        $product = $this->prepareModelWithCriteria($criteria);
        $httpAdapter = new InternalHttpAdapter(getCurrentSite());
        return $httpAdapter->saveProduct($product);
    }

    public function updateProductInCake($id,array $criteria)
    {
        $product = $this->prepareModelWithCriteria($criteria);
        $httpAdapter = new InternalHttpAdapter(getCurrentSite());
        return $httpAdapter->updateProduct($id,$product);
    }

    public function getAutoCompleteResult($q, $allowInActive = false)
    {
        return $this->getAutoCompletePorudctWithNameOrCode($q)->get();
    }

    public function createWithName($name)
    {
        $product = $this->model->create(
            [
                "name" => $name,
                "status" => ProductStatusUtil::STATUS_ACTIVE,
                "site_id" => getCurrentSite("id"),
                "staff_id" => getAuthStaff("id"),
                "tracking_type" => "quantity_only",
                "updated_price" => 0,
                "is_featured" => false,
                "track_stock" => 1,
                "type" => ProductTypeUtil::PRODUCT,
                "branch_id" => getCurrentBranchID(),
                "product_code" => AutoNumber::get_auto_serial(AutoNumber::TYPE_PRODUCT)
            ]
        );
        AutoNumber::update_auto_serial(AutoNumber::TYPE_PRODUCT);
        return $product;
    }

    public function getAutoCompletePorudctWithNameOrCode($q)
    {
        if ($this->isDatamatrix($q)) {
            $q = substr($q, 2, 14);
        }

        $keywords = explode(' ', $q);

        $query = $this->model->where(function ($query) use ($keywords,$q) {
            $query->where(function ($query) use ($keywords, $q) {
                if (count($keywords) > 1) {
                    foreach ($keywords as $keyword) {
                        $query->where('name', 'like', '%' . $keyword . '%');
                    }
                } else {
                    $query->where('name', 'like', '%' . $q . '%');
                }
            })
            ->orWhere('barcode', 'like', '%' . $q . '%')
            ->orWhere('product_code', 'like', '%' . $q . '%');
        })
        ->where(function ($query) {
            $query->where('status', ProductStatusUtil::STATUS_ACTIVE)
                ->orWhereNull('status');
        });

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query->where('branch_id', Branch::getCurrentBranchID());
        }

        $query->with('unitTemplate');
        return $query;
    }


    public function getAutoCompletePorudctWithNameOrCode2($q)
    {
        if ($this->isDatamatrix($q)) {
            $q = substr($q, 2, 14);
        }

        $keywords = explode(' ', $q);

        $query = $this->model->where(function ($query) use ($keywords,$q) {
            $query->where(function ($query) use ($keywords, $q) {
                if (count($keywords) > 1) {
                    foreach ($keywords as $keyword) {
                        $query->where('name', 'like', '%' . $keyword . '%');
                    }
                } else {
                    $query->where('name', 'like', '%' . $q . '%');
                }
            })
                ->orWhereHas('productItemBarcode', function ($query) use ($q) {
                    return $query->where('barcode', $q);
                })
                ->orWhere('barcode', $q )
                ->orWhere('product_code', $q);
        });

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query->where('branch_id', Branch::getCurrentBranchID());
        }

        $query->with('unitTemplate');
        return $query;
    }

    public function getAutoCompleteCode($q)
    {
        if ($this->isDatamatrix($q)) {
            $q = substr($q, 2, 14);
        }
        return ProductItemBarcode::where('barcode', $q)->first();
    }


    public function getAutoCompleteProduct($q)
    {
        return $this->getAutoCompletePorudctWithNameOrCode2($q)->first();
    }

    private function isDatamatrix($code): bool
    {
        $identifiers = ['01', '17', '10', '21'];
        foreach ($identifiers as $identifier) {
            if (!str_contains($code, $identifier)) {
                return false;
            }
        }

        return (bool) preg_match('/^01\d{14}/', $code);
    }

    public function findProductWithBranch($id)
    {
    	$query = $this->model->where('id', $id);
	    if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
		    $query->where('branch_id', Branch::getCurrentBranchID());
	    }
    }

    public function getAutoCompleteResultForStockTaking($q)
    {
        $query = $this->getAutoCompletePorudctWithNameOrCode($q)
            ->orWhereHas('productItemBarcode', function ($query) use ($q) {
                $query->where('product_item_barcode.barcode', 'like', '%' . $q . '%');
            });
        return $this->applyStockTakingCriteriaQuery($query, false)->get();
    }

    public function applyStockTakingCriteria($id)
    {
        $query = $this->model->where('id', $id);
        return $this->applyStockTakingCriteriaQuery($query)->first();
    }

    private function applyStockTakingCriteriaQuery($query, $tracking_type = true)
    {

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query->where('branch_id', Branch::getCurrentBranchID());
        }

        return $query->where(function ($sql) {
            // type = product or type = bundle
            $sql->where('type', 1)->orWhere('type', 3)->orWhere('type', 0)->orWhereNull('type');
        })->where(function ($sql) {
            $sql->where('status', ProductStatusUtil::STATUS_ACTIVE)->orWhereNull('status');
        });
    }

    public function getDistinctBrands()
    {
        return DB::connection('currentSite')->table('products')->select('brand')
            ->whereNotNull('brand')
            ->where('brand', '<>', '')
            ->distinct()->get()->pluck('brand')->toArray();
    }

    /**
     * @return array
     */
    public function getBrandsList(): array
    {
        return parent::list(['brand', 'brand'], true);
    }

    /**
     * @param int $priceListId
     * @param array $categories
     * @param array $brands
     * @param int $limit
     * @return array
     */
    public function getProductsByPriceListFilters(int $priceListId, array $categories = [], array $brands = [], int $limit = 100): array
    {
        $query = DB::connection('currentSite')->table('products')
            ->select([
                'products.id',
                'product_prices.price as old_price',
                'product_prices.id as product_price_id',
                'name',
                'product_code as code',
                'products.brand as brand'
            ])
            ->join('product_prices', function ($join) use ($priceListId) {
                $join->on('product_prices.product_id', '=', 'products.id');
                $join->where('group_price_id', '=', $priceListId);
            })
            ->whereColumn('products.id', '=', 'product_prices.product_id')
            ->limit($limit);

        if (!empty($categories)) {
            $query->join("items_categories", function ($join) use ($categories) {
                $join->on('products.id', '=', 'items_categories.item_id');
                $join->where('items_categories.item_type', '=', 1);
                $join->whereIn('items_categories.category_id', $categories);
            });
        }
        if (!empty($brands)) {
            $query->whereIn('products.brand', $brands);
        }
        return $query->get()->toArray();
    }

    /**
     * @param array $categories
     * @param array $brands
     * @param int $limit
     * @return array
     */
    public function getProductsByAmountSelectionFilters(array $categories = [], array $brands = []): array
    {
        $query = DB::connection('currentSite')->table('products')
            ->select([
                'products.id',
                'unit_price as old_price',
                'name',
                'product_code as code'
            ]);
        if (!empty($brands)) {
            $query->whereIn('products.brand', $brands);
        }
        if (!empty($categories)) {
            $query->join("items_categories", function ($join) {
                $join->on('products.id', '=', 'items_categories.item_id');
                $join->where('items_categories.item_type', '=', 1);
            });
            $query->whereIn("items_categories.category_id", $categories);
        }
        return $query->get()->toArray();
    }

    /**
     * @param int $amount
     * @return array
     */
    public function getProductsLessThan(int $amount, int $priceListId=null, $category = null): array
    {
        return DB::connection('currentSite')->table('products')
            ->where('unit_price', '<=', abs($amount))
            ->when($priceListId, function ($q) use ($priceListId) {
                return $q->join('product_prices', function ($join) use ($priceListId) {
                    $join->on('product_prices.product_id', '=', 'products.id')
                         ->where('group_price_id', '=', $priceListId);
                });
            })
            ->when($category, function ($q) use ($category) {
                return $q->join('items_categories', function ($join) use ($category) {
                    $join->on('items_categories.item_id', '=', 'products.id')
                         ->whereIn('items_categories.category_id', $category)
                         ->where('items_categories.item_type', '=', 1);//product category
                });
            })
            ->get()
            ->toArray();
    }
    function list($columns = [], $toArray = false)
    {
        $this->applyCriteria();
        $this->applyBranchesFind();
        if(empty($columns))
        {
            $columns = $this->listingColumns;
        }
        $results = $this->model->get()->pluck($columns[1],$columns[0]);
        if ($toArray && $results) {
            return array_filter($results->toArray());
        }
        return $results;
    }

    /**
     * @param array $products
     * @param int $priceListId
     * @return int
     */
    public function bulkUpdateForPriceList(array $products, int $priceListId)
    {
        $productsIds = array_map(function ($product) {
            return $product->id;
        }, $products);
        $productsIds = implode(",", $productsIds);
        $query = "UPDATE product_prices SET price = (CASE ";
        foreach ($products as $product) {
            $query .= "WHEN (product_id = $product->id) THEN $product->new_price ";
        }
        $query .= " END) WHERE product_id IN ($productsIds) AND group_price_id = $priceListId";
        return DB::connection('currentSite')->update($query);
    }

    /**
     *
     * @param array $products
     * @return int
     */
    public function bulkUpdateForAmountSelection(array $products)
    {
        $productsIds = array_map(function ($product) {
            return $product->id;
        }, $products);
        $productsIds = implode(",", $productsIds);
        $query = "UPDATE products SET unit_price = (CASE ";
        foreach ($products as $product) {
            $query .= "WHEN (id = $product->id) THEN $product->new_price ";
        }
        $query .= " END) WHERE id IN ($productsIds)";
        return DB::connection('currentSite')->update($query);
    }

    public function findByBarcode($barcode , $checkItemBarcode = false) {
        $query = $this->model->where('barcode', '=', $barcode);
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query = $query->where('branch_id', Branch::getCurrentBranchID());
        }
        if ($checkItemBarcode === true){
            $query->orWhereHas('productItemBarcode', function ($q) use ($barcode) {
                $q->where('product_item_barcode.barcode', 'like', '%' . $barcode . '%');
            });
        }
        return $query->first();
    }
    public function findByCode($code) {
        $query = $this->model->where('product_code', '=', $code);
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
           $query = $query->where('branch_id', Branch::getCurrentBranchID());
         }
        return $query->first();
    }

    public function findByName($name) {
        $query = $this->model;
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query = $query->where('branch_id', Branch::getCurrentBranchID());
        }
        return $query->where('name', '=', $name);
    }

    public function productExistsByNameOrCode($nameOrCode){
        $query = $this->model->where(function($query) use($nameOrCode){
            $clearedName = str_replace(' ', '', $nameOrCode);
            $query->orWhere('product_code', $nameOrCode)
            ->orWhere('name', $nameOrCode)
            ->orWhereRaw("REPLACE(`name`,' ','') LIKE '%".addslashes($clearedName)."%' ESCAPE '|'");
        });
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
           $query->where('branch_id', Branch::getCurrentBranchID());
        }

        return $query->count();
    }

    public function productByNameOrCode($nameOrCode){
        $model = $this->model;
        $hasCustomDataTable = Schema::connection('currentSite')->hasTable('products_custom_data');
        if($hasCustomDataTable){
           $model = $model->with('customData');
        }
        $query = $model->where(function($query) use($nameOrCode){
            $clearedName = str_replace(' ', '', $nameOrCode);
            $query->orWhere('product_code', $nameOrCode)
            ->orWhere('name', $nameOrCode)
            ->orWhereRaw("REPLACE(`name`,' ','') LIKE '%".addslashes($clearedName)."%' ESCAPE '|'");
        });
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query->where('branch_id', Branch::getCurrentBranchID());
        }

        $query = $query->leftJoin('product_images', function($join) {
            $join->on('product_images.product_id', '=', 'products.id');
            $join->where('product_images.default', '=', '1');
        })
        ->addSelect(['products.*','product_images.file']);

        // Order the results by priority first if product_code =$nameOrCode  get it first
        $query = $query->orderByRaw("CASE
               WHEN `product_code` = ? THEN 1
               WHEN `name` = ? THEN 2
               ELSE 3
               END", [$nameOrCode, $nameOrCode]);

        return $query->first();
    }

    public function getProductsByNameOrCode($names_or_codes){
        $query = $this->model->where(function($query) use($names_or_codes){
            $query->orWhere('product_code', $names_or_codes)->orWhere('name', $names_or_codes);
        });
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query->where('branch_id', Branch::getCurrentBranchID());
        }

        return $query->distinct()->get();
    }

    function findProductByIdWithCustomData($id)
    {
        return $this->model->with('customData')->where('id',$id)->first();
    }

    public function lazy()
    {
        return $this->model->lazy();
    }

    function getNumberOfProductsInGroupPrice($groupPriceId) : int {
        $numberOfProducts = DB::connection('currentSite')
            ->table('product_prices')
            ->where('product_prices.group_price_id', '=', $groupPriceId)->count();
        return $numberOfProducts;
    }

    public function searchDistinctBrands($brandName)
    {
        $brands = DB::connection('currentSite')->table('products')->select('brand')
         ->whereNotNull('brand');

        if(!(empty($brandName) || !is_string($brandName))){
            $brands = $brands->where('brand', 'like', '%' .$brandName. '%');
        }

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $brands->where('branch_id', Branch::getCurrentBranchID());
        }
        $brands =  $brands->distinct()->get()->pluck('brand')->toArray();
        $result = [];
        foreach ($brands as $brand) {
            $result[] = [
                'name' => $brand,
                'id' => $brand,
            ];
        }
        return $result;
    }

    public function searchAutoSuggestProducts($productNameOrSku, $activeOnly = 0, $get_products_only = false, $get_services_only = false, $for_booking = false)
    {
        $products = Product::with('unitTemplate');
        if (!empty($productNameOrSku) && is_string($productNameOrSku)) {
            $products->where(function($q) use ($productNameOrSku) {
                $q->where('name', 'like', '%' .$productNameOrSku. '%')
                ->orWhere('product_code', 'like', '%' .$productNameOrSku. '%')
                ->orWhere('barcode', 'like', '%' .$productNameOrSku. '%')
                ->orWhere('id', $productNameOrSku);
            });
        }

        if($activeOnly == 1){
            $products->where(function($q){
                $q->where('status', ProductStatusUtil::STATUS_ACTIVE)
                    ->orWhereNull('status');
            });
        }
        if ($get_products_only){
            $products->where('type' , ProductTypeUtil::PRODUCT);
        }
        if ($get_services_only){
            $products->where('type' , ProductTypeUtil::SERVICE);
        }
        if ($for_booking){
            $products->whereNotNull('duration_minutes');
        }
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products' , null ,false)) {
            $products->where('branch_id', Branch::getCurrentBranchID());
        }
        if (is_array($productNameOrSku) && isset($productNameOrSku["id"])){
            $products->where('id', $productNameOrSku["id"]);
        }
        $products =  $products->orderBy("id", "desc")->limit(50)->get();
        $productsIds = $products->pluck('id')->all();
        $attachmentService = resolve(AttachmentsService::class);
        $productAttachments = $attachmentService->getProductsDefaultImages($productsIds);
        $result = [];
        foreach ($products as $product) {
            $item = [];
            $item['text'] = $product->name.' #'. ($product->product_code ?? $product->id);
            $item['name'] = $item['text'];
            $item['id'] = $product->id;
            $item['avatar'] = $attachmentService->resolveProductDefaultImagePath($product , $productAttachments);
            $item['default_buy_factor_id'] = $product->default_buy_factor_id;
            if($for_booking){
                $item['unit_price'] = $product->unit_price;
                $item['duration_minutes'] = $product->duration_minutes;
            }
            if(!empty($product->unitTemplate)){
                $item['units'] = [
                    [
                        "id" => 0,
                        "small_name" => $product->unitTemplate->unit_small_name ?? '',
                        "factor_name" => $product->unitTemplate->main_unit_name ?? '',
                        "factor" => 1,
                    ]
                ];
                $item['units'] = array_merge(
                    $item['units'],
                    $product->unitTemplate->factors->map(function($factor){
                        return [
                            "id" => $factor->id,
                            "small_name" => $factor->small_name ?? '',
                            "factor_name" => $factor->factor_name ?? '',
                            "factor" => $factor->factor ?? 1,
                        ];
                    })->toArray()
                );
            }
            $result[] = $item;
        }
        return $result;
    }

    public function getAutoSuggestProductsV2($limit = 50, $with = []): Collection {
        $with[] = 'unitTemplate';
        if(isset($limit)){
            $this->pushCriteria(new LimitCriteria($limit));
        }
        $this->pushCriteria(new OrderByCriteria('id', 'DESC'));
        return $this->all(['*'], $with);
    }

    public function getProductsAveragePrices($productsIds) {
        return  $this->model->whereIn("id", $productsIds)->get(["id", "average_price"]);
    }

    public function checkProductOwnsAttributes($itemGroupId  ,  $attributes)
    {
        $query = $this->model
            ->where('item_group_id', '=', $itemGroupId);
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query = $query->where('branch_id', Branch::getCurrentBranchID());
        }
        foreach ($attributes as $index => $attribute) {
            $query->whereHas('attributeOptions', function ($sq) use ($attribute) {
                $sq->where('attribute', $attribute['attribute'])
                    ->where('option', $attribute['option']);
            });
        }
        return $query->first();
    }

    function getProductsWithUnitTemplate(array $productIds) : Collection {
        return $this->model->with(["unitTemplate", "unitTemplate.factors"])->whereIn("id", $productIds)->get();
    }
    public function getDefaultOptions($limit = 50): Collection
    {
        return $this->getAutoSuggestProductsV2($limit);
    }

    public function updateProductsBrand($brandData){
        return  $this->model->where('brand_id', $brandData->id)->update(['brand'=> $brandData->name]);
    }

    public function getSearchAutoSuggestActviceServices(){
        return $this->searchAutoSuggestProducts(null, 1, false, true);
    }

    function getNameLike($q)
    {
        /**
         * This method normalizes the input string by:
         *  - Trimming leading/trailing spaces.
         *  - Converting multiple consecutive spaces into a single space.
         * Example:
         *   "جلبة 1/2 بوصة ضغط 40 م . ع"
         *   and
         *   "جلبة   1/2  بوصة   ضغط 40  م  . ع"
         *   will both be matched.
         */
        $q = preg_replace('/\s+/', ' ', trim($q));
        $query = $this->model->whereRaw(
            "REPLACE(name, '  ', ' ') LIKE ?",
            ['%' . str_replace('  ', ' ', $q) . '%']
        );
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $query = $query->where('branch_id', Branch::getCurrentBranchID());
        }
        return $query;
    }

    public function getSearchAutoSuggestBookingServices(){
        return $this->searchAutoSuggestProducts(null, 1, false, true, true);
    }

    public function getServicesForBooking($id, $q, $categoryId = null, $branchId = null){
        $query = $this->model->select('id', 'name', 'unit_price', 'duration_minutes', 'discount', 'discout_type')
            ->where('type', ProductTypeUtil::SERVICE)
            ->where('status', ProductStatusUtil::STATUS_ACTIVE)
            ->whereNotNull('duration_minutes')
            ->whereHas('categories', function($query) use ($categoryId){
                if(!empty($categoryId)){
                    $query->where('category_id', $categoryId);
                }
            })
            ->where(function($query) use ($q){
                $query->where('name', 'like', '%' .$q. '%')
                    ->orWhere('product_code', 'like', '%' .$q. '%')
                    ->orWhere('barcode', 'like', '%' .$q. '%')
                    ->orWhere('id', $q);
            })->with(['serviceForm', 'categories' => function ($query) {
                $query->select('name', 'category_id');
            }]);
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $shareAllProducts = Settings::getValue(PluginUtil::BranchesPlugin, 'share_products', null, false);
                if (!$shareAllProducts) {
                    $query->where('branch_id', '=', $branchId ?? Branch::getCurrentBranchID());
                }
            }
        if(!empty($id)){
            $query->Where('id', $id);
        }

        $services = $query->limit(20)->get();
        $serviceIds = $services->pluck('id')->all();
        $attachmentService = resolve(AttachmentsService::class);
        $serviceAttachments = $attachmentService->getProductsDefaultImages($serviceIds);
        foreach($services as &$service){
            $service->photo = $attachmentService->resolveProductDefaultImagePath($service, $serviceAttachments);
            $service->assigned_staff_ids = $service->getAssignedStaffIds();
            $service->discount_type = $service->discout_type;
            unset($service->discout_type);
        }

        return $services->toArray();
    }

    public function createService($data)
    {
        $data['type'] = ProductTypeUtil::SERVICE;
        $data['status'] = ProductStatusUtil::STATUS_ACTIVE;
        $data['product_code'] = AutoNumber::get_auto_serial(AutoNumber::TYPE_PRODUCT);
        $data['site_id'] = getCurrentSite('id');
        $data['branch_id'] = Branch::getCurrentBranchID();
        $assignedStaffs = $data['assigned_staff_ids'] ?? [];
        $photo = $data['photo'] ?? null;
        $categories = $data['category_ids'] ?? [];
        unset($data['assigned_staff_ids'], $data['photo'], $data['category_ids']);
        $service = $this->model->create($data);
        if($service){
            $service->categories()->sync($categories);
            $service->assignedStaffs()->sync($assignedStaffs);
            AutoNumber::update_auto_serial(AutoNumber::TYPE_PRODUCT);
        }
        return [
            'status' => true,
            'message' => __t('Service created successfully'),
            'data' => $service
        ];
    }

    public function updateService($id, $data)
    {
        $service = $this->model->find($id);
        if (!$service) {
            return [
                'status' => false,
                'message' => __t('Service not found'),
                'data' => null
            ];
        }
        $assignedStaffs = $data['assigned_staff_ids'] ?? [];
        $categories = $data['category_ids'] ?? [];
        $photo = $data['photo'] ?? null;
        unset($data['assigned_staff_ids'], $data['photo'], $data['category_ids']);
        $service->update($data);
        $service->categories()->sync($categories);
        $service->assignedStaffs()->sync($assignedStaffs);
        return [
            'status' => true,
            'message' => __t('Service updated successfully'),
            'data' => $service
        ];
    }

    public function deleteService($id)
    {
        $service = $this->model->find($id);
        if ($service) {
            $service->categories()->detach();
            $service->assignedStaffs()->detach();
            $service->delete();
        }
        return [
            'status' => true,
            'message' => __t('Service deleted successfully')
        ];
    }

    public function listServices($filters = [])
    {
        $this->applyBranchesFind();
        $query = $this->model->where('type', ProductTypeUtil::SERVICE);

        // Filter by status (0 or 1)
        if (isset($filters['status']) && in_array($filters['status'], [0, 1])) {
            $query->where('products.status', $filters['status']);
        }

        // Filter by name search using LIKE operator
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where(function($q) use ($filters){
                $q->where('products.name', 'like', '%' . $filters['search'] . '%')
                    ->orWhere('products.product_code', 'like', '%' . $filters['search'] . '%');
            });
        }

        // Filter by assigned staffs
        if (isset($filters['assigned_staffs']) && !empty($filters['assigned_staffs'])) {
            $query->whereHas('assignedStaffs', function($q) use ($filters) {
                $q->whereIn('staff_id', $filters['assigned_staffs']);
            });
        }

        // Join with categories to order by category display_order
        $query->leftJoin('items_categories', 'products.id', '=', 'items_categories.item_id')
              ->leftJoin('categories', 'items_categories.category_id', '=', 'categories.id')
            //   ->groupBy('products.id')
              ->orderBy('categories.display_order', 'asc')
              ->orderBy('items_categories.display_order', 'asc')
              ->orderBy('products.id', 'asc');

        // Load relationships
        $query->select('products.id', 'products.name', 'products.unit_price', 'products.duration_minutes', 'products.tax1', 'products.tax2', 'products.status', 'items_categories.display_order', 'categories.id as category_id');

        // Get results
        $services = $query->get();

        return $services;
    }
}
