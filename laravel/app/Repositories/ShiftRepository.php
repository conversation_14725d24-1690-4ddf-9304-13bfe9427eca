<?php

namespace App\Repositories;
use App\Models\ShiftDay;
use Illuminate\Container\Container as App;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Models\StaffInfo;

class ShiftRepository extends BaseRepository
{
    private $shiftDayModel = null;
    public function __construct(App $app, Collection $collection)
    {
        $this->shiftDayModel = new ShiftDay();
        parent::__construct($app, $collection);
    }

    function model()
    {
        return 'App\Models\Shift';
    }

    /**
     * @param $shiftDay array
     */
    function saveShiftDay($shiftDay)
    {
        if(!isset($shiftDay['late_time']) || $shiftDay['late_time'] == '')
        {
            $shiftDay['late_time'] = 0;
        }
        return $this->shiftDayModel->create($shiftDay);
    }

    /**
     * @param $shiftId
     * @return mixed
     */
    function deleteShiftDays($shiftId)
    {
        return $this->model->find($shiftId)->shiftDays()->delete();
    }

    /**
     * @param $shiftId
     * @return mixed
     */
    function deleteShiftAttendanceFlags($shiftId)
    {
        return $this->model->find($shiftId)->AttendanceFlags()->detach();
    }

    /**
     * @param $shiftId
     * @param $shiftAttendanceFlags
     */
    function saveShiftAttendanceFlags($shiftId, $shiftAttendanceFlags = [])
    {
       $attandanceFlags = $this->model->find($shiftId)->AttendanceFlags();
       $attandanceFlags->detach();
       return $attandanceFlags->attach($shiftAttendanceFlags);
    }

    /**
     * @param $shiftId
     */

    function getShiftAttendanceFlagsList($shiftId)
    {
        return $this->model->find($shiftId)->AttendanceFlags()->pluck( 'id');
    }

    /**
     * @param $shiftId
     * @return array
     */
    function getShiftDays($shiftId)
    {
        $sql = 'SELECT `shift_days`.* FROM `shift_days` WHERE `deleted_at` IS NULL AND `shift_days`.`shift_id` = :shift_id';
        return DB::connection('currentSite')->select($sql, ['shift_id' => $shiftId]);
    }

    /**
     * @param $emp_id
     * @return array|null
     */
    public function getPrimaryShiftByStaffId($emp_id)
    {
        $sql = '
        SELECT `shifts`.`id`, `shifts`.`name` FROM `shifts`
        INNER JOIN `staff_info` ON `staff_info`.`attendance_shift_id` = `shifts`.`id`
        WHERE  `staff_info`.`staff_id` = :emp_id
        ';
        $shifts = DB::connection('currentSite')->select($sql, ['emp_id' => $emp_id]);
        return array_shift($shifts);
    }

    /**
     * @param $emp_id
     * @return array|null
     */
    public function getSecondaryShiftByStaffId($emp_id)
    {
        $sql = '
        SELECT `shifts`.`id`, `shifts`.`name` FROM `shifts`
        INNER JOIN `staff_info` ON `staff_info`.`secondary_shift_id` = `shifts`.`id`
        WHERE  `staff_info`.`staff_id` = :emp_id
        ';
        $shifts = DB::connection('currentSite')->select($sql, ['emp_id' => $emp_id]);
        return array_shift($shifts);
    }

    public function getShiftEmployeesIds($shift_id){
        $query = null;
        $vars = [];

        if(is_null($shift_id)){
            $query = 'SELECT staff_id as id FROM staff_info LEFT JOIN shifts ON staff_info.attendance_shift_id = shifts.id';
        } else {
            $query = 'SELECT staff_id as id FROM staff_info LEFT JOIN shifts ON staff_info.attendance_shift_id = shifts.id WHERE staff_info.attendance_shift_id = :shift_id';
            $vars = ['shift_id' => $shift_id];
        }

        $result = DB::connection('currentSite')->select($query, $vars);
        return array_map(function($x){
            return $x->id;
        }, $result);
    }

    /**
     * @param $shiftId
     * @param $employeeIds
     * @return mixed affected records count
     *
     */
    public function assignToEmployees($shiftId, $employeeIds) {
        $employees = StaffInfo::whereIn('staff_id', $employeeIds)->where(function ($query) use ($shiftId){
            $query->where('attendance_shift_id', '!=', $shiftId)->orWhereNull('attendance_shift_id');
        })->update(['attendance_shift_id' => $shiftId]);
        return $employees;
    }


    public function undoDeleteShift($shiftId)
    {
        $shift = $this->model->where('id',$shiftId)->withTrashed()->first();
        $shiftDayModel = resolve(ShiftDay::class);
        if($shift){
            $shiftDaysIds = $shiftDayModel->where('shift_id',$shiftId)->withTrashed()->pluck('id')->toArray();
            $this->model->where('id',$shiftId)->withTrashed()->restore();
            if($shiftDaysIds){
                $shiftDayModel->whereIn('id', $shiftDaysIds)->withTrashed()->restore();
            }
        }
    }

    public function getBookingShiftsWithDaysByStaffId($emp_id) : array
    {
        $shifts = $this->model->whereHas('assignedBookingStaff', function($query) use ($emp_id) {
            $query->where('staff_id', $emp_id);
        })->with('shiftDays')->get();
        return $shifts->toArray();
    }

    public function getAutoCompleteResult($q)
    {
        $query = $this->model->where(function ($query) use ($q) {
            $query->orWhere('name', 'like','%'.$q.'%')
                ->orWhere('id', '=', $q);
        });
        return $query->get();
    }


}
