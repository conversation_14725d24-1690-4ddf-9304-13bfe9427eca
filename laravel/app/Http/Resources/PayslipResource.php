<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PayslipResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'notes' => $this->notes,
            'gross_pay' => (float)$this->gross_pay,
            'total_deduction' => (float)$this->total_deduction,
            'net_pay' => (float)$this->net_pay,

            'payrun_id' => $this->payrun_id,
            'payment_id' => $this->payment_id,
            'staff_id' => $this->staff_id,
            'expense_id' => $this->expense_id,
            'designation_id' => $this->designation_id,
            'department_id' => $this->department_id,
            'branch_id' => $this->branch_id,
            'contract_id' => $this->contract_id,

            'designation_name' => $this->designation_name,
            'department_name' => $this->department_name,
            'branch_name' => $this->branch_name,
            'attendance_sheet_id' => $this->attendance_sheet_id,

            'posting_date' => $this->posting_date,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'format_posting_date' => format_date($this->posting_date, false, true),
            'format_start_date' => format_date($this->start_date, false, true),
            'format_end_date' => format_date($this->end_date, false, true),

            'deleted_at' => $this->deleted_at,
            'created' => $this->created,
            'modified' => $this->modified,
            'format_created' => format_date($this->created, false, true),
            'format_modified' => format_date($this->modified, false, true),

            'viewHisOwnAttendanceLog' => $this->viewHisOwnAttendanceLog,
            'viewHisOwnAttendanceSheet' => $this->viewHisOwnAttendanceSheet,
            'salaryComponents' => $this->salaryComponents,
            'pay_run' => new PayrunResource($this->payRun),
            'staff' => new StaffResource($this->staff),
            'attendanceSheet' => new AttendanceSheetResource($this->attendanceSheet),
            'attachments' => $this->attachments,
        ];
    }
}