<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceSheetResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {

        return [
            'id' => $this->id,
            'auto_id' => $this->auto_id,
            'staff_id' => $this->staff_id,

            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'status' => $this->status,
            'working_days' => $this->working_days,
            'actual_working_hours' => $this->actual_working_hours,
            'expected_working_hours' => $this->expected_working_hours,
            'present_days' => $this->present_days,
            'absence_days' => $this->absence_days,
            'leaves' => $this->leaves,

            'fiscal_start_date' => $this->fiscal_start_date,
            'sign_in_only_count' => $this->sign_in_only_count,
            'sign_out_only_count' => $this->sign_out_only_count,
            'total_delay_amount' => $this->total_delay_amount,
            'total_delay_count' => $this->total_delay_count,
            'total_early_leave_amount' => $this->total_early_leave_amount,
            'total_early_leave_count' => $this->total_early_leave_count,
            'off_working_days' => $this->off_working_days,

            'modified' => $this->modified,
            'created' => $this->created,

            'takenLeaves' => $this->takenLeaves,
            'attendance_flags' => $this->attendance_flags,
            'employee' => $this->employee,
            'attendance_days' => AttendanceDayResource::collection($this->attendanceDays),
        ];
    }

}