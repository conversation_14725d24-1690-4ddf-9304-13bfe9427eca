<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceDayResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        $isArabic = $request->headers->get('lang') == 'ar';
        return [
            "id" => $this->id,
            "staff_id" => $this->staff_id,
            "attendance_sheet_id" => $this->attendance_sheet_id,
            "status" => $this->status,
            "shift_id" => $this->shift_id,
            "attendance_permission_id" => $this->attendance_permission_id,

            "date" => $this->date,
            "sign_in" => $this->sign_in,
            "sign_out" => $this->sign_out,
            "on_duty" => $this->on_duty,
            "beginning_in" => $this->beginning_in,
            "beginning_out" => $this->beginning_out,
            "off_duty" => $this->off_duty,
            "ending_in" => $this->ending_in,
            "ending_out" => $this->ending_out,
            "created" => $this->created,
            "modified" => $this->modified,

            "attendance_delay" => $this->attendance_delay,
            "expected_working_hours" => $this->expected_working_hours,
            "actual_working_hours" => $this->actual_working_hours,
            "calculation_type" => $this->calculation_type,
            "day_off_type" => $this->day_off_type,
            "day_off_title" => $this->day_off_title,
            "notes" => $this->notes,

            "is_secondary" => $this->is_secondary,
            "can_work_on_off_days" => $this->can_work_on_off_days,

            "day_status_color" => $this->day_status_color,
            "sign_in_note" => $isArabic ? $this->formatTimeToArabic($this->sign_in_note) : $this->sign_in_note,
            "sign_out_note" => $isArabic ? $this->formatTimeToArabic($this->sign_out_note) : $this->sign_out_note,

            "leave_type_id" => $this->leave_type_id,
            "leave_count" => $this->leave_count,
            "early_leave" => $this->early_leave,
            "leave_type" => $this->leave_type,
        ];
    }

    private function formatTimeToArabic($notes)
    {
        if (!$notes) return null;

        $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $eastern = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

        return str_replace($western, $eastern, $notes);
    }
}