<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PayrunResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,

            'posting_date' => $this->posting_date,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'format_posting_date' => format_date($this->posting_date, false,),
            'format_start_date' => format_date($this->start_date, false,),
            'format_end_date' => format_date($this->end_date, false),

            'currency_code' => $this->currency_code,
            'format_currency_code' => get_currency_formatted($this->currency_code),

            'department_id' => $this->department_id,
            'designation_id' => $this->designation_id,
            'branch_id' => $this->branch_id,
            'created_at_branch_id' => $this->created_at_branch_id,
            'payroll_frequency' => $this->payroll_frequency,
            'added_by' => $this->added_by,
            'exclude_criteria' => $this->exclude_criteria,
            'status' => $this->status,
            'validate_attendance' => (bool)$this->validate_attendance,
            'payslips_count' => (int)$this->payslips_count,
            'criteria' => $this->criteria,

            'deleted_at' => $this->deleted_at,
            'created' => $this->created,
            'modified' => $this->modified,
            'format_created' => format_date($this->created, false, true),
            'format_modified' => format_date($this->modified, false, true),

            // Collections / relations (nullable)
            'branches' => $this->branches,
            'designations' => $this->designations,
            'departments' => $this->departments,
            'payroll_frequencies' => $this->payroll_frequencies,

            // "All" labels
            'branches_names' => $this->branches_names,
            'departments_names' => $this->departments_names,
            'designations_names' => $this->designations_names,
            'payroll_frequency_names' => $this->payroll_frequency_names,

            // single relations
            'branch' => $this->branch,
            'department' => $this->department,
            'designation' => $this->designation,
        ];
    }
}