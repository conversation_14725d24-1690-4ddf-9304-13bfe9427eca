<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * Indicates whether the XSRF-TOKEN cookie should be set on the response.
     *
     * @var bool
     */
    protected $addHttpCookie = true;

    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        'owner/shop_front_media/upload_media',
        'owner/machines/*/iclock/cdata',
        'owner/status/*',
        'owner/follow_up_actions/*',
        'api/*',
        'owner/eta/send-bulk',
        'owner/templates/render',
        'owner/templates/render/entity-on-fly/*',
        'owner/templates/render/multi-entity-on-fly/*',
        'owner/templates/render/global-on-fly/*',
        'owner/templates/generate_placeholders/*'
    ];
}
