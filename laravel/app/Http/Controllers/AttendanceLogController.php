<?php

namespace App\Http\Controllers;

use App\Exceptions\EntityNotFoundException;
use App\Facades\Formatter;
use App\Requests\DefaultRequest;
use App\Services\AttendanceLogAutoCalcService;
use App\Services\AttendanceLogService;
use App\Services\AttendanceRestrictionApplier;
use App\Services\Traits\BulkDeleteController;
use App\Utils\AttendanceLogSourceTypeUtil;
use App\Utils\AttendanceSessionSourceTypeUtil;
use App\Utils\EntityFieldUtil;
use Carbon\Carbon;
use App\Utils\PermissionUtil;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Facades\CakeSession;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Services\MachineMappingService;

class AttendanceLogController extends BaseController
{
    use BulkDeleteController;

    public $folder = 'attendance_logs';
    protected $service;
    protected $machineMappingService;
    protected $attendanceRestrictionApplier;
    protected $label = 'Attendance Log';

    function __construct(AttendanceLogService $service, AttendanceRestrictionApplier $attendanceRestrictionApplier, MachineMappingService $machineMappingService)
    {
        $this->service = $service;
        $this->machineMappingService = $machineMappingService;
        $this->attendanceRestrictionApplier = $attendanceRestrictionApplier;
        parent::__construct($service);
    }

    public function getFormValidationRules(Request $request, $id=null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $commonRules = [
            'source_type' => 'in:'.implode(',', array_keys(AttendanceLogSourceTypeUtil::getSourceTypeList())),
        ];
        if($request->get('source_type') == AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_SUPERVISOR)
        {
            $commonRules += [
                'staff_id' => 'required|exists:currentSite.staffs,id,active,1,deleted_at,NULL',
                'session_id' => 'required|exists:currentSite.attendance_sessions,id,deleted_at,NULL',
            ];
        }

        $messages = [];
        if ($id) {
            $rules = array();

        } else {
            $rules = array();

        }
        $rules += $commonRules;
        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }


    public function getStaffLastSessionSign($staffId, $sessionId = false){
        $lastStaffSessionLogs = $this->service->getStaffLastSessionSign($sessionId, $staffId);
        if($lastStaffSessionLogs->isNotEmpty())
        {
            $lastStaffSessionLog = $lastStaffSessionLogs->shift();
            $otherSigns  = [];
            if($lastStaffSessionLogs->isNotEmpty())
            {
                $otherSigns = $lastStaffSessionLogs->map(function ($item, $key){
                    return Formatter::formatForView($item->time, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC, null, ['seconds' => true]);
                });

            }
            $lastSign = convertFromUtc($lastStaffSessionLog->time)->toDateTimeString();
            $formattedLastSign = Formatter::formatForView($lastSign, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER, null, ['seconds' => true]);
            return response()->json(['result' => true, 'last_sign' => $lastSign, 'last_sign_formatted' => $formattedLastSign, 'signs' => $otherSigns]);
        }else{
            return response()->json(['result' => false]);
        }
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     * sign employee to session
     */
    public function sign(Request $request, $sessionId = false)
    {
        try {
            $restrictionDetails = [];
            $validatedData = $this->validateFormData($request);
            $storeRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());
            //Check if the current staff has a restriction for attendance then apply this restriction
            if ($request['source_type'] != AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_SUPERVISOR && $this->attendanceRestrictionApplier->hasRestriction()) {
                $restrictionDetails =  $this->attendanceRestrictionApplier->apply($storeRequest);
                if ($restrictionDetails) {
                    $logId = $this->attendanceRestrictionApplier->addRestrictionLog($restrictionDetails, $storeRequest);
                    $storeRequest['attendance_restriction_log_id'] = $logId;
                }
            }

            $this->service->add($storeRequest);

            if (!isApi()) {
                if ($request->session_id) {
                    $redirectRoute = route($this->routesPrefix . 'attendance_sessions' . '.show', ['#takeAttendance', 'attendance_session' => $sessionId]);
                } else {
                    $redirectRoute = route($this->routesPrefix . $this->folder . '.index');
                }
            }
            if (isApi()) {
                return response()->json(['status' => true, 'attendance_session' => $sessionId], 200);
            }
            return redirect($redirectRoute)->with('success', sprintf(__t('%s Added Successfully'), __t($this->label)));
        } catch (QueryException $exception) {
            if (isApi()) {
                return response()->json(['status' => false, 'message' => sprintf(__t('%s Adding Failed'), __t($this->label))], 404);
            }
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('danger', sprintf(__t('%s Adding Failed'), __t($this->label)));
        } catch (\Exception $exception) {
            if (isApi()) {
                return response()->json(['status' => false, 'message' => sprintf(__t('%s Adding Failed'), __t($this->label))], 404);
            }
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('danger', __t($exception->getMessage()));
        }
    }

    public function destroyMulti(Request $request)
    {
        if($request['ids'] == 'none'){
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('danger', sprintf(__t('No %s selected'), __t('Attendance Log')));
        } elseif ($request['ids'] == 'all'){
            $allData = $this->service->all(['*'])->pluck('id')->toArray();
            $result = $this->service->deleteMany($allData);
        } else {
            $ids = explode(',', $request['ids']);
            $result = $this->service->deleteMany($ids);
        }

        if ($result['deletedCount'] > 0 && $result['unDeletedCount'] > 0){
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Attendance Logs')))
                ->with('danger', sprintf(__t('(%d) %s cannot be deleted as they are already in use'), $result['unDeletedCount'], __t('Attendance Logs')));
        } elseif ($result['deletedCount'] > 0 && $result['unDeletedCount'] == 0){
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Attendance Logs')));
        } elseif ($result['deletedCount'] == 0 && $result['unDeletedCount'] > 0){
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('danger', sprintf(__t('(%d) %s cannot be deleted as they are already in use'), $result['unDeletedCount'], __t('Attendance Logs')));
        }
    }

    public function index(Request $request)
    {
        $listingData = $this->service->listing();
        $AttendanceLogAutoCalcService = resolve(AttendanceLogAutoCalcService::class);
        $uncalculatedLogs = $AttendanceLogAutoCalcService->getCalculateAttendanceMessage(Route::current()->action['as']);
        if($uncalculatedLogs['found']) {
            session()->flash('warning', $uncalculatedLogs['message']);
        }
        if (request()->isXmlHttpRequest()) {
            unset($listingData['sort_fields']);
            return view('partials.listing.grids.attendance_logs', $listingData);
        }

        if (isApi()) {
            return response()->json($listingData['pagination']->toArray(), 200);
        } else {

            if($unmappedMessage = $this->machineMappingService->getUnmappedUsersMessage()){
                session()->flash('danger', $unmappedMessage);
            }

            return view($this->folder . $this->viewsPrefix . '.index', $listingData);
        }
    }
    public function getAllLogsForAttendanceDay(Request $request, $attendance_day_id)
    {
        $listingData = $this->service->listAllLogsForAttendanceDay($attendance_day_id);
        unset($listingData['sort_fields']);
        return view('partials.listing.grids.attendance_logs', $listingData);
    }

    public function show($id) {
        try {
            $data = $this->service->getViewData($id);
            $navData = $this->service->getViewNavData($data['record']);
            if (isApi()) {
                return response()->json(['data' => $data['record']->toArray()], 200);
            } else {
                $blade = $this->blade ?? 'show';
                if (isset($data['message'])) {
                    session()->flash('warning', $data['message']);
                }
                return view(
                    $this->folder . $this->viewsPrefix . '.' . $blade,
                    array_merge($data, ['query' => $this->service->parameters, 'navData' => $navData])
                );
            }
        } catch (NotAccessibleStaffBranchException $exception) {
            CakeSession::flashMessage(__t($exception->getMessage()), 'Errormessage', 'secondaryMessage');
            return redirect(CAKE_BASE_URL);
        } catch (EntityNotFoundException $exception) {
            if (isApi()) {
                return response()->json(['message' => __t('This Record Doesn\'t Exist')], 404);
            }
	        return redirect()->route($this->getRouteName('index'), $this->queryParameters)
	            ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t($this->label)));
        } catch (\Exception $exception) {
            if(in_array(get_class($exception),$this->handledExceptions)){
                throw $exception;
            }
            if (isApi()) {
                return response()->json(['message' => __t('This Record Doesn\'t Exist')], 404);
            }
	        return redirect()->route($this->getRouteName('index'), $this->queryParameters)
	            ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t($this->label)));
        }
    }

}
