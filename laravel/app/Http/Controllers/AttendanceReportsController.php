<?php

namespace App\Http\Controllers;

use App\Exports\AttendanceExport;
use App\Facades\Permissions;
use App\Facades\Settings;
use App\Forms\LeaveBalanceFilterForm;
use App\IzamViews\IzamView;
use App\Services\AttendanceCalculator\Entities\Fiscal;
use App\Services\AttendanceReportsService;
use App\Services\PdfExporterService;
use App\Utils\MultipleShiftTypeUtil;
use App\Utils\PermissionUtil;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class AttendanceReportsController
 * @package App\Http\Controllers
 *
 * @property AttendanceReportsService $reportsService
 */
class AttendanceReportsController extends ReportsController
{
    public function __construct(AttendanceReportsService $attendanceReportsService,   private PdfExporterService $pdfExporterService)
    {
        $this->reportTabID = 'employees_reports';
        parent::__construct($attendanceReportsService);
    }

    public function shiftReportForm()
    {
        $this->setBreadCrumbs(__t('Attendance Shift'));
        $this->reportsService->setShiftsFilters();
        return view('attendance.reports.shift_report.shift', $this->reportsService->getFormsData());
    }

    public function shiftReportData(Request $request)
    {
        if (!$request->isXmlHttpRequest()) {
            $dateFormatIndex = getCurrentSite('date_format');
            $dateFormats = getDateFormats('std');
            $request->validate([
                'from_date' => 'required|date_format:'.$dateFormats[$dateFormatIndex],
                'to_date' => 'required|date_format:'.$dateFormats[$dateFormatIndex],
                'shift_type' => 'nullable|in:'.implode(',', MultipleShiftTypeUtil::types())
            ], [
                'required' => __t('This Field is Required'),
                'unique' => __t('This Field must be unique'),
                'numeric' => __t('Please Enter a Valid Number'),
                'date_format' => __t('Please Enter Correct Date and Time'),
                'url' => __t('Please Enter a Valid Url'),
                'exists' => __t("This Field Reference Doesn't Exists"),
                'integer' => __t("This Field Must be Integer"),
                'max' => __t('This Field Exceeded its Maximum Value')
            ]);
            $this->setBreadCrumbs(__t('Attendance Shift'));
            $this->reportsService->setShiftsFilters();
        }
        $this->reportsService->setShiftData();
        if (!$request->isXmlHttpRequest()) {
            return view('attendance.reports.shift_report.shift', $this->reportsService->getReportData());
        } else {
            return view('attendance.reports.shift_report.shift_table', ['reportData' => $this->reportsService->getData()]);
        }
    }

    public function attendanceReportMultipleForm(Request $request)
    {
        $this->setBreadCrumbs(__t('Detailed Attendance Report (Multiple Employees)'));
        $this->reportsService->setAttendanceMultipleFilters();

        if (isset($this->getParameters['from_date']) && isset($this->getParameters['to_date'])) {
            $this->reportsService->setParameters($this->getParameters);
            $this->reportsService->setAttendanceMultipleData();
            $data = $this->reportsService->getReportData();
            if (isset($this->reportsService->getData()['message'])) {
                Session::flash('danger', $this->reportsService->getData()['message']);
            }
        } else {
            $data = $this->reportsService->getFormsData();
        }
        $data = ['filterMethod' => 'GET'] + $data;

        if ($request->isXmlHttpRequest()) {
            return view('attendance.reports.attendance_multiple_report.attendance_table', $data);
        }

        return view('attendance.reports.attendance_multiple_report.attendance', $data);
    }

    public function attendanceMultipleExportExcel(Request $request)
    {
        try {
            if (!count($request->all())) {
                return redirect()->back();
            }
            $validator = $this->reportsService->filterValidatorForAttendanceMultipleReport($request->all());
            if ($validator->fails()) {
                return redirect()->back();
            }

            $this->reportsService->setParameters($this->getParameters);
            ini_set("memory_limit", "5542M");
            $this->reportsService->setIsExporting(true);
            $this->reportsService->setAttendanceMultipleData();
            $this->reportsService->setIsExporting(false);
            $data = $this->reportsService->getReportData();
            if($data){
                return $this->reportsService->prepareExcelForAttendanceMultipleReport(__t('Detailed Attendance Report (Multiple Employees)', true), $data['reportData']);
            }

            return redirect()->back();
        } catch (\Exception $exception) {
            $this->izamFlashMessage(__t($exception->getMessage()), 'danger');
            return redirect()->back();
        }
    }

    public function attendanceReportSingleForm()
    {
        $fiscal_day = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_day');
        $fiscal_month = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_month');
        $fiscal = new Fiscal($fiscal_day,$fiscal_month);
        $fiscal_date = $fiscal->getFiscalDateFromDate(Carbon::now()->toDateString());

        $this->setBreadCrumbs(__t('Detailed Attendance Report (Single Employee)'));
        $this->reportsService->setAttendanceSingleFilters();

        if (isset($this->getParameters['name']) && isset($this->getParameters['from_date']) && isset($this->getParameters['to_date'])) {
            $this->reportsService->setParameters($this->getParameters);
            $this->reportsService->setAttendanceSingleData();
            $form_data = $this->reportsService->getReportData();
        } else {
            $form_data = $this->reportsService->getFormsData();
        }

        $form_data = ['filterMethod' => 'GET'] + $form_data;

        if (isset($this->reportsService->getData()['message'])) {
            Session::flash('danger', $this->reportsService->getData()['message']);
        }

        $form_data['filters']['from_date']['value'] = formatForView($fiscal_date, 'date');
        return view('attendance.reports.attendance_single_report.attendance', $form_data);
    }

    public function leaveBalanceAttendance()
    {
        $this->setBreadCrumbs(__t('Employees Leave Balance Report'));
        $this->reportsService->setLeaveBalanceFilters();
        $form = new LeaveBalanceFilterForm();
        $form_data = [];
        try {
            if (isset($this->getParameters['from_date']) && isset($this->getParameters['to_date'])) {
                $this->reportsService->setParameters($this->getParameters);
                $this->reportsService->setLeaveBalanceData();
                $form_data = $this->reportsService->getReportData();
            } else {
                $form_data = $this->reportsService->getFormsData();
            }

        } catch (ValidationException $exception) {
            $form->setMessages(['from_date' => $exception->errors()]);
        }

        $parameters = $this->reportsService->getParameters();
        if (empty($parameters['from_date'])) {
            $fiscal_day = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_day');
            $fiscal_month = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_month');
            $fiscal = new Fiscal($fiscal_day,$fiscal_month);
            $fiscal_date = $fiscal->getFiscalDateFromDate(Carbon::now()->toDateString());
            $parameters['from_date'] = $fiscal_date;
        }

        if (empty($this->getParameters['to_date'])) {
            $parameters['to_date'] = Carbon::now();
        }
        $form_data = ['filterMethod' => 'GET'] + $form_data;

        $form_data['filtered'] = $parameters;
        $form->setData($parameters);
        $form_data['form'] = $form;;
        //return view('reports/attendance/leave_balance', compact('form_data'));
        return new IzamView('reports/attendance/leave_balance', $form_data);
    }

    public function leaveBalanceAttendanceExport(Request $request)
    {
        if ($request->has('ext') && in_array($request->get('ext'), ["excel", "pdf", "csv"])) {
            $extension  = $request->get('ext');
        } else {
            $extension = "excel";
        }
        try {
            $reportName = "Employees_Leave_Balance_Report-".date("Y-m-d_H-i-s");
            if (isset($this->getParameters['from_date']) && isset($this->getParameters['to_date'])) {
                $this->reportsService->setParameters($this->getParameters);
                $this->reportsService->setIsExporting(true);
                $this->reportsService->setLeaveBalanceData();
                $form_data = $this->reportsService->getReportData();
                $form_data['filtered'] = \request()->all();
                if ($extension == "excel") {
                    return Excel::download(new AttendanceExport($form_data), ($reportName.'.xlsx'));
                } else if ($extension == "csv") {
                    return Excel::download(new AttendanceExport($form_data), ($reportName.'.csv'), \Maatwebsite\Excel\Excel::CSV);
                } else {
                    $this->pdfExporterService->setPath('attendance-reports');
                    return $this->pdfExporterService->createPdf($reportName, compact('form_data'), 'reports.attendance.leave_balance');
                }

            }

        } catch (ValidationException $exception) {
            $this->izamFlashMessage(__t($exception->getMessage()), 'danger');
            return redirect()->back();
        }
    }

    public function flags() {
        try {
            $staffId = null;
            $requestParams = request()->all();
            if(!Permissions::checkPermission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG)) {
                $staffId = getAuthStaff('id');
            }
            $filterOptions = ['from_date' => $requestParams['from_date']??null, 'to_date' => $requestParams['to_date']??null, 'staff_id' => $staffId];
            $flagsData = $this->reportsService->getAttendanceDayFlagsGrouped($filterOptions);
            $earlyLeaveData = $this->reportsService->getEarlyLeaveDays($filterOptions);
            $lateData = $this->reportsService->getLateDays($filterOptions);
            $formattedData = [
                    [
                    'name' => __t('Total Early Leave'),
                    'minutes' => [
                        'count' => $earlyLeaveData->early_leave_minutes,
                        'text' => __t('Minute(s)')
                    ],
                    'days' => [
                        'count' => $earlyLeaveData->early_leave_count,
                        'text' => __t('Day(s)')
                    ]
                    ],
                    [
                        'name' => __t('Total Late'),
                        'minutes' => [
                            'count' => $lateData->delay_minutes,
                            'text' => __t('Minute(s)')
                        ],
                        'days' => [
                            'count' => $lateData->delay_count,
                            'text' => __t('Day(s)')
                        ]
                    ]
            ];

            foreach($flagsData as $record) {
                $formattedData[] = [
                    'name' => $record->flag_name,
                    'minutes' => [
                        'count' => $record->flag_value,
                        'text' => __t('')
                        ],
                    'days' => [
                        'count' => $record->flag_count,
                        'text' => __t('')
                    ]];
            }
            return response()->json(['id' => 3,'data' => ['flags' => $formattedData]], 200);
        }catch (\Exception $e) {
            Log::error('attendance flags report error', ['Exception' => $e]);
            response()->json(['data' => [], 400]);
        }

    }

    public function leaveBalanceAttendanceForAuthStaff(Request $request)
    {
        $staffId = $request->get('staff_id') ?? getAuthOwner('staff_id');
        return json_encode($this->reportsService->getLeaveBalanceForStaffIdByType($staffId));
    }
}
