<?php

namespace App\Http\Controllers;

use App\Facades\Plugins;
use App\IzamViews\IzamView;
use App\Services\StaffService;
use App\Services\Traits\DefaultIzamViewData;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;

class StaffOrganizationalChartController
{

    use DefaultIzamViewData;

    public function __construct()
    {
        $this->setDefaultViewData();
    }

    public function __invoke(): IzamView
    {
        return new IzamView("staff/organizational-chart", $this->getChartData());
    }

    private function getChartData(): array
    {
        $owner = getAuthOwner();
        $department_id = null;
        if ($owner['staff_id'] && Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $staff = resolve(StaffService::class)->find($owner['staff_id']);
            $department_id = $staff->staff_info?->department_id;
        }
        return [
            "siteData" => json_encode([
                'business_name' => $owner['business_name'],
                'first_name' => $owner['first_name'],
                'last_name' => $owner['last_name'],
                'site_logo_full_path' => $owner['site_logo_full_path'] ?? getCurrentSite('site_logo_full_path'),
                'staff_id' => $owner['staff_id'],
                'is_super_admin' => $owner['is_super_admin'],
                'edit_delete_employee_permission' => check_permission(PermissionUtil::Staff_Edit_Staffs),
                'view_only_department_chart_permission' => check_permission(PermissionUtil::VIEW_ONLY_DEPARTMENT_CHART),
                'manage_hrm_system' => check_permission(PermissionUtil::MANAGE_HRM_SYSTEM),
                'active_HRM_plugin' => Plugins::pluginActive(PluginUtil::HRM_PLUGIN),
                'department_id' => $department_id,
            ])
        ];
    }
}
