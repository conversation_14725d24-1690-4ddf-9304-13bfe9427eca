<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Izam\Booking\Services\UpdatedShiftDayService;

class UpdatedShiftDayController extends Controller
{
    protected $updatedShiftDayService;

    public function __construct(UpdatedShiftDayService $updatedShiftDayService)
    {
        $this->updatedShiftDayService = $updatedShiftDayService;
    }

    public function updateShiftDay(Request $request)
    {
        $validatedData = $request->validate([
            'staff_id' => 'required|array',
            'staff_id.*' => 'required|integer|exists:currentSite.staffs,id',
            'date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i',
        ]);
        $result = $this->updatedShiftDayService->updateShiftDay($validatedData);

        if($result['status'] == 'success') {
            return response()->json([
                'status' => $result['status'],
                'message' => $result['message'],
                'data' => $result['data'],
            ], 200);
        }else{
            return response()->json([
                'status' => $result['status'],
                'message' => $result['message'],
            ], 400);
        }
    }
    
}