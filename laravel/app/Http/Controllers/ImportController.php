<?php

namespace App\Http\Controllers;

use App\Exceptions\EntityNotFoundException;
use App\Facades\Plugins;
use App\Models\Treasury;
use App\Services\BaseService;
use App\Services\ImportService;
use App\Services\ItemGroupService;
use App\Services\MappingService;
use App\Services\PriceListItemsService;
use App\Services\PriceListService;
use App\Utils\AdditionalImportPartialUtil;
use App\Utils\EntityKeyTypesUtil;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use App\Facades\Permissions;
use Izam\Daftra\Common\Utils\PermissionUtil;
use App\Facades\CakeSession;
use InvalidArgumentException;
use Izam\Limitation\LimitationService;
use Izam\Limitation\Utils\LimitationUtil;


class ImportController extends BaseController
{
    public $folder = 'import';

    /**
     * @var ImportService $service
     */
    protected $service;
    /**
     * @var MappingService $mappingService
     */
    private $mappingService;

    function __construct(ImportService $service, MappingService $mappingService)
    {
        $this->service = $service;
        $this->mappingService = $mappingService;
        parent::__construct($service);
    }

    public function importForm($entityKey, Request $request)
    {
        $entity = $this->mappingService->getEntityByKey($entityKey);

        if ($entityKey == "bank_transaction") {
            if (!Permissions::checkPermission(PermissionUtil::Edit_General_Settings) && !Permissions::checkPermission(PermissionUtil::MANAGE_JOURNAL_ACCOUNTS)) {
                CakeSession::flashMessage(__t('You are not allowed to access this page'), 'Errormessage', 'secondaryMessage');
                return redirect(CAKE_BASE_URL);
            }
        }

        if ($entityKey == "payslip" && !Permissions::checkPermission(PermissionUtil::CREATE_PAY_RUN)) {
            return redirect()->route('owner.payslips.index')->with('danger', __t("You are not allowed to access this page"));
        }
        if ($entityKey == EntityKeyTypesUtil::STOCKTAKING_RECORD && !Permissions::checkPermission(PermissionUtil::REQUISITION_VIEW)) {
            return redirect()->back()->with('danger', __t("You are not allowed to access this page"));
        }

        if ($entityKey == EntityKeyTypesUtil::CONTRACT && !Permissions::checkPermission(PermissionUtil::ADD_PAYROLL_CONTRACT)) {
            return redirect()->route('owner.contracts.index')->with('danger', __t("You are not allowed to access this page"));
        }

        if ($entityKey == "invoice" && !Permissions::checkPermission([PermissionUtil::Invoices_Add_New_Invoices, PermissionUtil::INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS])) {
            CakeSession::flashMessage( __t("You are not allowed to access this page"));
            return redirect(getCakeURL(['action' => 'index', 'controller' => 'invoices']));
        }

        if (
            $entityKey == EntityKeyTypesUtil::JOURNAL &&
            !Permissions::checkPermission(MANAGE_ALL_JOURNALS) &&
            !Permissions::checkPermission(MANAGE_OWN_JOURNALS) &&
            !Permissions::checkPermission(MANAGE_DRAFT_JOURNALS)
        ) { 
            CakeSession::flashMessage( __t("You are not allowed to access this page"));
            return redirect(getCakeURL(['action' => 'index', 'controller' => 'journals']));
        }

        if ($request->has('count') && $request->count == 0) {
            return redirect()->back()->with('danger', __t("You should add at least one price list to import"));
        }
        if ($entityKey == EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY) {
            if (!Permissions::checkPermission(PermissionUtil::Edit_Delete_all_Products) && !Permissions::checkPermission(PermissionUtil::Edit_And_delete_his_own_added_Products)) {
                return redirect()->route('owner.entity.list',['entityKey'=>EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY])->with('danger',__("You are not allowed to access this page"));
            }
            $userId = getAuthOwner('staff_id');
            $itemGroupService = resolve(ItemGroupService::class);
            if (Permissions::checkPermission(PermissionUtil::Edit_And_delete_his_own_added_Products) && $userId !==0 ) {
                $itemGroupId = \request()->entity_id;
                if ($itemGroupId) {
                    $userCanAccess = $itemGroupService->checkUserHasAccess($itemGroupId, $userId);
                    if (!$userCanAccess) {
                        return redirect()->route('owner.entity.list', ['entityKey' => EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY])->with('danger', __("You are not allowed to access this page"));
                    }
                }
            }
        }
        if ($entity && (empty($entity->plugin_id) || Plugins::pluginActive($entity->plugin_id))) {
            try {
                $this->setBreadCrumbs($entityKey);
            } catch (EntityNotFoundException $exception) {
                return back()->with('danger', $exception->getMessage());
            }
            //Get Extra Data
            /** @var BaseService $importedService */
            $importedService = App::make('App\Services\\' . $entity->service_name);
            View::share($importedService->getImportViewShareData());
            $download_sample = [];
            if (method_exists($importedService, 'getImportDownloadSample')) {
                $download_sample = $importedService->getImportDownloadSample();
            }
            return view($this->folder . '.import', [
                'entityKey' => $entityKey,
                'download_sample' => $download_sample,
                'additionalInputs' => AdditionalImportPartialUtil::getImportPartialsByEntity($entityKey),
                'parameters' => $this->service->parameters ?? null
            ]);
        }
        return abort(404);
    }

    public function importMap(Request $request, $entityKey)
    {
        $validateRoles = [
            'input_file' => 'required|file|mimes:csv,txt',
            'delimiter' => 'required|in:1,2,3'
        ];
        $rulesMessages = [
            'required' => __t('This field is required'),
            'mimes' => __t("This input must be CSV")
        ];
        $validator = Validator::make($request->all(), $validateRoles, $rulesMessages);
        $validator->validate();
        try {
            $this->setBreadCrumbs($entityKey);
        } catch (EntityNotFoundException $exception) {
            return back()->with('danger', $exception->getMessage());
        }
        $handleFile = $this->service->handleFile($request, $entityKey);
        if ($handleFile['status']) {
            $entityFields = $this->service->getEntityImportedFields($entityKey);
            return view($this->folder . '.import-2', ['entityFields' => $entityFields, 'entityKey' => $entityKey, 'formRecord' => ['delimiter' => $handleFile['delimiter'], 'import_first_row' => $handleFile['import_first_row'], 'fileName' => $handleFile['fileName'], 'originalFileName' => $handleFile['originalFileName'], 'headers' => $handleFile['headers'], 'extraData' => $handleFile['extraData']]]);
        } else {
            throw ValidationException::withMessages([
                'errorMessage' => $handleFile['message']
            ]);
        }
    }

    public function import(Request $request, $entityKey)
    {
        try {
            $this->setBreadCrumbs($entityKey);
            $requiresDataOnly = $this->mapEntityRequiresInsertion($entityKey);
            $response = $this->service->importData($request, $entityKey, $requiresDataOnly);
            return $this->mapEntityImportResponse($entityKey , $response);
        } catch (EntityNotFoundException $exception) {
            return back()->with('danger', $exception->getMessage());
        } catch (QueryException $exception) {
            $importURL = route("owner.import", ["entity_key" => $entityKey]);
            $importURL .= "?entity_id=" . $request->entity_id;
            return redirect($importURL)->with('danger', $exception->getMessage());
        } catch (Exception $exception) {
            $importURL = route("owner.import", ["entity_key" => $entityKey]);
            $importURL .= "?entity_id=" . $request->entity_id;
            return redirect($importURL)->with('danger', $exception->getMessage());
        }
    }

    /**
     * gets the bread crumbs data and set it for all views
     * @param $entityKey
     * @throws EntityNotFoundException
     */
    protected function setBreadCrumbs($entityKey, $return = false)
    {
        if (is_string($entityKey)) {
            $entity = $this->mappingService->getEntityByKey($entityKey);
            if (is_null($entity))
                $link = null;
            else
                $link = $this->mapEntityListingPage($entityKey);


            $entityName = ($entity->label == '') ? $entityKey : $entity->label;
            $breadCrumbs[] = [
                'link' => $link,
                'title' => __t(Str::plural(ucfirst($entityName)))
            ];
            $breadCrumbs[] = [
                'link' => null,
                'title' => 'Import'
            ];

            if (request('entity_key') == "price_list_items") {
                $priceListService = resolve(PriceListService::class);
                $priceList = $priceListService->find(request('entity_id'));
                if ($priceList) {
                    $breadCrumbs = [
                        [
                            'link' => route('owner.price_lists.index'),
                            'title' => __t(Str::plural(ucfirst($entityName)))
                        ],
                        [
                            'link' => route('owner.price_lists.show', $priceList->id),
                            'title' => $priceList->name . " #" . $priceList->id
                        ],
                        [
                            'link' => null,
                            'title' => 'Import'
                        ]
                    ];
                } else {
                    $breadCrumbs = [
                        [
                            'link' => route('owner.price_lists.index'),
                            'title' => __t(Str::plural(ucfirst($entityName)))
                        ],
                        [
                            'link' => null,
                            'title' => 'Import'
                        ]
                    ];
                }


            }
            if (request('entity_key') == "item_group") {
                $itemGroupService = resolve(ItemGroupService::class);
                $breadCrumbs = $itemGroupService->getImportBreadCrumbs(request('entity_key') , $entityName);
            }
            if (request('entity_key') == "bank_transaction") {
                $breadCrumbs = [];
                $breadCrumbs[] = [

                    'link' => '/v2/owner/banks/treasury',
                    'title' => __t('Treasuries & Bank Accounts')
                ];
                $treasury = false;
                if (isset(request()->extraData)) {

                    $treasury = Treasury::find(request()->extraData['bank_id']);
                    if (!$treasury) {
                        throw new EntityNotFoundException(__t("Bank"));
                    }

                    $breadCrumbs[] = [
                        'link' => "/owner/treasuries/view/" . $treasury->id . "#bank-transactions",
                        'title' => $treasury->name . " #" . $treasury->id
                    ];

                    $breadCrumbs[] = [
                        'link' => '/v2/owner/import/bank_transaction?extraData[bank_id]=' . $treasury->id,
                        'title' => __t('Import') . ' ' . __t('Bank Statement')
                    ];
                }


                $breadCrumbs[] = [
                    'link' => null,
                    'title' => 'Import'
                ];

            }

            try {
                $breadCrumbs = $this->service->getBreadCrumbs($entityKey, $breadCrumbs);
            } catch (\Exception $exception) {

            }

            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        }
    }

    public function mapEntityRequiresInsertion($entityKey)
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => true,
            default => false
        };

    }
    private function mapEntityImportResponse($entityKey , $response)
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => $this->handleItemGroupImportResponse($entityKey , $response) ,
            EntityKeyTypesUtil::INVOICE_ENTITY_KEY, EntityKeyTypesUtil::ASSET, EntityKeyTypesUtil::JOURNAL => $this->handleJsonFileBulkImport($entityKey, $response),
            default => view($this->folder . '.import-3', $response)
        };
    }

    private function mapEntityListingPage(string $entityKey)
    {
        return match ($entityKey){
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY =>   route("owner.entity.list" , ['entityKey'=>$entityKey]) ,
            EntityKeyTypesUtil::ASSET => '/owner/assets/index',
            EntityKeyTypesUtil::INVOICE_ENTITY_KEY =>   "/owner/invoices" ,
            EntityKeyTypesUtil::JOURNAL =>   "/owner/journals/index" ,
            default => route("owner." . Str::plural($entityKey) . ".index")
        };
    }

    private function handleItemGroupImportResponse($entityKey, $response)
    {
        if (!$response['status']){
            return view($this->folder . '.import-3', $response);
        }
        return redirect()->route('owner.entity.edit', ['entityKey' => $entityKey, 'id' => request()->entity_id , 'importedProducts'=>$response['data']]);
    }

    private function handleJsonFileBulkImport($entity, $response)
    {
        if (!$response['status']) {
            return view($this->folder . '.import-3', $response);
        }

        $flattenedMessages = Arr::flatten($response['invalidData']);
        CakeSession::flashMessage(implode("<br/>", $flattenedMessages));

        return redirect(getCakeURL(['controller' => 'bulk', 'action' => 'import_records', $entity, config('data.import-count-id')]));
    }

    public function importingRecordsApi($entity, $id, $last = 0)
    {
        if ($limitationKey = getEntityLimitationKeyIfExists($entity)) {
            $status =  checkSiteLimit(getCurrentSite('id'), constant("\\Izam\\Limitation\\Utils\\LimitationUtil::$limitationKey"));
            $this->handleSiteLimit(
                $status,
                [
                    'type' => 'cake',
                    "url" =>  getCakeURL(["controller" => "invoices", "action" => "index"]),
                ]
            );
        }

       try {
           return response()->json(
               $this->service->getImportedRecords($entity, $id)
           );
       } catch (InvalidArgumentException $exception) {
           return  response()->json(["error" => $exception->getMessage(), "message" => $exception->getMessage(), "last_id" => $last], 300);
       } catch (Exception $exception) {
           return  response()->json(["error" => "Something went wrong", "message" => "Something went wrong", "last_id" => $last], 300);
       }
    }
}
