<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Izam\BlockTime\Services\BlockTimeService;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Services\CommonActivityLogService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use App\Facades\Permissions;
use App\Utils\PermissionUtil;
use Carbon\Carbon;
class BlockTimeController extends Controller
{
    protected $blockTimeService;
    protected $activityLogService;

    public function __construct(
        BlockTimeService $blockTimeService
    ) {
        $this->blockTimeService = $blockTimeService;
    }

    public function createBlockTime(Request $request)
    {
        if(!Permissions::checkPermission(PermissionUtil::MANAGE_EMPLOYEES_BREAKS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to manage employees block times')
            ], 403);
        }

        $result = $this->blockTimeService->createBlockTime($request->all());

        if ($result['status'] === 'error') {
            return response()->json($result, 422);
        }

        $result['data'] = $this->formatDates($result['data']);

        $request = new ActivityLogRequest(
            $result['data']['id'],
            EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
            ActionLineMainOperationTypesUtil::ADD_ACTION,
            "Block time created",
            null,
            $this->blockTimeService->formatBlockTimeForActivityLog($result['data'])
        );
        $activityLogService = resolve(CommonActivityLogService::class);
        $activityLogService->addActivity($request);

        return response()->json($result, 201);
    }

    public function updateBlockTime(Request $request, $id)
    {
        if(!Permissions::checkPermission(PermissionUtil::MANAGE_EMPLOYEES_BREAKS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to manage employees block times')
            ], 403);
        }

        $result = $this->blockTimeService->updateBlockTime($id, $request->all());

        if ($result['status'] === 'error') {
            return response()->json($result, 422);
        }

        $result['data'] = [
            'old_block_time' => $this->formatDates($result['data']['old_block_time']),
            'new_block_time' => $this->formatDates($result['data']['new_block_time'])
        ];
        $request = new ActivityLogRequest(
            $result['data']['old_block_time']['id'],
            EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
            ActionLineMainOperationTypesUtil::UPDATE_ACTION,
            "Block time updated #{$id}",
            null,
            $this->blockTimeService->formatBlockTimeForActivityLog($result['data']['old_block_time']),
            $this->blockTimeService->formatBlockTimeForActivityLog($result['data']['new_block_time'])
        );
        $activityLogService = resolve(CommonActivityLogService::class);
        $activityLogService->addActivity($request);

        return response()->json($result, 200);
    }

    public function getBlockTime(Request $request, $id)
    {
        $result = $this->blockTimeService->getBlockTime($id);
        if(!$result){
            return response()->json([
                'status' => 'error',
                'message' => __t('Block time not found')
            ], 404);
        }
        $result = $this->formatDates($result);

        return response()->json($result, 200);
    }

    protected function formatDates(array $data)
    {
        $data['start_date'] = Carbon::parse($data['start_date'])->format('Y-m-d');
        $data['end_date'] = isset($data['end_date']) ? Carbon::parse($data['end_date'])->format('Y-m-d') : null;
        $data['start_time'] = Carbon::parse($data['start_time'])->format('H:i:s');
        return $data;
    }

    public function deleteBlockTime(Request $request, $id)
    {
        if(!Permissions::checkPermission(PermissionUtil::MANAGE_EMPLOYEES_BREAKS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to manage employees block times')
            ], 403);
        }

        $result = $this->blockTimeService->deleteBlockTime($id);

        if ($result['status'] === 'error') {
            return response()->json($result, 422);
        }

        $request = new ActivityLogRequest(
            $id,
            EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
            ActionLineMainOperationTypesUtil::DELETE_ACTION,
            "Block time deleted #{$id}",
            null
        );
        $activityLogService = resolve(CommonActivityLogService::class);
        $activityLogService->addActivity($request);

        return response()->json($result, 200);
    }
}