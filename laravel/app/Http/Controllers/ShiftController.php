<?php

namespace App\Http\Controllers;

use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Shift\NoShiftSelectedException;
use App\Exceptions\ShiftNeedWeekDaysException;
use App\Exceptions\ShiftSaveFailedException;
use App\Facades\Plugins;
use App\Requests\DefaultRequest;
use App\Rules\LateTimeRule;
use App\Rules\TimeFormat;
use App\Services\ShiftService;
use App\Services\StaffService;
use App\Utils\ActiveStatusUtil;
use App\Utils\PluginUtil;
use App\Utils\ShiftTypeUtil;
use App\Utils\ShiftWeekdayUtil;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\ValidationException;
use App\Facades\Branch;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\ShiftRepository;
use App\Repositories\StaffRepository;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Services\ActivityLogService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\ActionLineMainOperationTypesUtil;


class ShiftController extends BaseController
{
    public $folder = 'shifts';
    /**
     * @var ShiftService
     */
    protected $service;
    protected $label = 'Shift';

    function __construct(ShiftService $service)
    {
        parent::__construct($service);
    }


    private function getShiftDaysValidationRules($shiftWeekDays)
    {
        $shiftWeekDaysValidationRules = [];
        $shiftDaysData = request()->get('shift_days');
        foreach ($shiftWeekDays as $weekDay) {
            if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN))
            {
                $shiftWeekDaysValidationRules["shift_days.$weekDay.beginning_in"] =  ["required", new TimeFormat];
                $shiftWeekDaysValidationRules["shift_days.$weekDay.beginning_out"] =  ["required", new TimeFormat];
                $shiftWeekDaysValidationRules["shift_days.$weekDay.ending_in"] =  ["required", new TimeFormat];
                $shiftWeekDaysValidationRules["shift_days.$weekDay.ending_out"] =  ["required", new TimeFormat];
                if($shiftDaysData[$weekDay]['late_time'] > 0)
                {
                    $shiftWeekDaysValidationRules["shift_days.$weekDay.late_time"] =  ["nullable","integer", new LateTimeRule($shiftDaysData[$weekDay]['from_time'],$shiftDaysData[$weekDay]['ending_in'])];
                }
            }
            $shiftWeekDaysValidationRules["shift_days.$weekDay.weekday"] =  "required|in:".implode(',', $shiftWeekDays);
            $shiftWeekDaysValidationRules["shift_days.$weekDay.from_time"] =  ["required", new TimeFormat];
            $shiftWeekDaysValidationRules["shift_days.$weekDay.to_time"] =  ["required", new TimeFormat];
        }
        return $shiftWeekDaysValidationRules;

    }

    public function getFormValidationRules(Request $request, $id = null)
    {

        $parentValidations = parent::getFormValidationRules($request, $id);
        $messages = ['exists' => __t("This Field Reference Dose'nt Exists or Not Active")];
        $commonRules = [
            'type' => "required|in:".implode(',', array_keys($this->service->getTypesList())),

        ];
        if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN))
        {
            $commonRules += [
                'attendance_flags.*' => 'exists:currentSite.attendance_flags,id,status,'.ActiveStatusUtil::ACTIVE.',deleted_at,NULL',
            ];
        }
        $requestShiftWeekDays = [];
        if($request->has('shift_days'))
        {
            foreach ($request->get('shift_days') as $shiftDay) {
                $requestShiftWeekDays[] = $shiftDay['weekday'];
            }
        }
        $commonRules += $this->getShiftDaysValidationRules($requestShiftWeekDays);

        if($id)
        {
            $rules = [
                'name' => "required|unique:currentSite.shifts,name,$id,id,deleted_at,NULL",
            ];

        }else{
            $rules = [
                'name' => "required|unique:currentSite.shifts,name,NULL,id,deleted_at,NULL",
            ];

        }

        $rules += $commonRules;
        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    public function create()
    {

        return parent::create(); // TODO: Change the autogenerated stub
    }

    /**
     * Store a newly created resource in storage.
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $requestOldData = $request->except(['_token']);
            $formattedShiftData = $this->service->formatFormData($requestOldData);
            $redirectData = $formattedShiftData;

            if(isset($requestOldData['all']) && empty($formattedShiftData['all']))
            {
                $redirectData = $requestOldData['all'];
            }
            $allErrors = [];
            $request->merge($formattedShiftData);
            $validatedData = $this->validateFormData($request);
            $storeRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());

            $result = $this->service->add($storeRequest);

            if (isApi()) {
                return response()->json(['data' => $this->service->find($result->id)], 200);
            } else {
                if ($this->service->mainEntity->name_field) {
                    $label = $validatedData[$this->service->mainEntity->name_field];
                } else {
                    $label = __t($this->service->mainEntity->label) . ' #' . $result->id;
                }
                return redirect()
                    ->route($this->routesPrefix . $this->folder . '.show', [$this->service->mainEntity->key => $result->id])
                    ->with('success', sprintf(__t('%s Added Successfully'), $label));
            }
        }
        catch (ValidationException $validationException){
            if($request->get('type') == ShiftTypeUtil::SHIFT_TYPE_STANDARD)
            {
                $weekDays = array_keys(ShiftWeekdayUtil::getDaysList());
                foreach ($validationException->errors() as $k => $error)
                {
                    $allErrors[str_replace($weekDays, 'all', $k)] = $error;
                }
                $validationException = ValidationException::withMessages($allErrors);
                $request->merge($redirectData);
            }
            if(isApi()) {
                return response()->json(['errors' => $validationException->errors(), 'message' => __t($validationException->getMessage())], 400);
            }else{
                return redirect()->route($this->routesPrefix . $this->folder . '.create')
                    ->withInput($redirectData)
                    ->withErrors($validationException->errors())
                    ->with('danger', sprintf(__t('%s Adding Failed'), __t($this->label)));
            }
        }catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route($this->routesPrefix . $this->folder . '.create')
                    ->withInput($redirectData)
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('%s Adding Failed'), __t($this->label)));
            }
        } catch (\Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route($this->routesPrefix . $this->folder . '.create')
                    ->withInput($redirectData)
                    ->withErrors($errors)
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }


    /**
     * Update the specified resource in storage.
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $requestOldData = $request->except(['_token']);
            $formattedShiftData = $this->service->formatFormData($request->except(['_token']));
            $redirectData = $formattedShiftData;

            $request->merge($formattedShiftData);
            if(isset($requestOldData['all']) && empty($formattedShiftData['all']))
            {
                $redirectData = $requestOldData['all'];
            }
            $validatedData = $this->validateFormData($request, $id);
            $allErrors = [];
            $updateRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());

            $result = $this->service->update($id, $updateRequest);
            if (isApi()) {
                return response()->json(['data' => $result], 200);
            } else {
                if ($this->service->mainEntity->name_field) {
                    $label = $validatedData[$this->service->mainEntity->name_field];
                } else {
                    $label = __t($this->service->mainEntity->label) . ' #' . $result->id;
                }
                return redirect()
                    ->route($this->routesPrefix . $this->folder . '.show', [$this->service->mainEntity->key => $id])
                    ->with('success', sprintf(__t('%s Saved Successfully'), $label));
            }
        }catch (ValidationException $validationException)
        {
            $allErrors = [];
            if($request->get('type') == ShiftTypeUtil::SHIFT_TYPE_STANDARD)
            {
                $weekDays = array_keys(ShiftWeekdayUtil::getDaysList());
                foreach ($validationException->errors() as $k => $error)
                {
                    $allErrors[str_replace($weekDays, 'all', $k)] = $error;
                }
                $validationException = ValidationException::withMessages($allErrors);
                $request->merge($redirectData);

            }
            if(isApi()) {
                return response()->json(['errors' => $validationException->errors(), 'message' => __t($validationException->getMessage())], 400);
            }else{
                return redirect()->route($this->routesPrefix . $this->folder . '.create')
                    ->withInput($redirectData)
                    ->withErrors($validationException->errors())
                    ->with('danger', sprintf(__t('%s Adding Failed'), __t($this->label)));
            }
        }
        catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route($this->routesPrefix . $this->folder . '.edit', [$this->service->mainEntity->key => $id])
                    ->withInput($redirectData)
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('%s Updating Failed'), __t($this->label)));
            }
        } catch (\Exception $exception) {

            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if(isApi())
            {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            }else{
                return redirect()->route($this->routesPrefix . $this->folder . '.edit', [$this->service->mainEntity->key => $id])
                    ->withInput($redirectData)
                    ->withErrors($errors)
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }

//    public function store(Request $request)
//    {
//        $requestOldData = $request->except(['_token']);
//        $formattedShiftData = $this->service->formatFormData($requestOldData);
//        $request->merge($formattedShiftData);
//        try{
//            $this->validateFormData($request);
//        }catch (ValidationException $validationException){
//            $allErrors = [];
//            if($request->get('type') == ShiftTypeUtil::SHIFT_TYPE_STANDARD)
//            {
//                $weekDays = array_keys(ShiftWeekdayUtil::getDaysList());
//                foreach ($validationException->errors() as $k => $error)
//                {
//                    $allErrors[str_replace($weekDays, 'all', $k)] = $error;
//                }
//                throw ValidationException::withMessages($allErrors);
//            }
//        }
//        return parent::store($request); // TODO: Change the autogenerated stub
//    }

//    public function update(Request $request, $id)
//    {
//        $formattedShiftData = $this->service->formatFormData($request->except(['_token']));
//        $request->merge($formattedShiftData);
//        try{
//            $this->validateFormData($request, $id);
//        }catch (ValidationException $validationException)
//        {
//            $allErrors = [];
//            if($request->get('type') == ShiftTypeUtil::SHIFT_TYPE_STANDARD)
//            {
//                $weekDays = array_keys(ShiftWeekdayUtil::getDaysList());
//                foreach ($validationException->errors() as $k => $error)
//                {
//                    $allErrors[str_replace($weekDays, 'all', $k)] = $error;
//                }
//                throw ValidationException::withMessages($allErrors);
//            }
//        }
//        return parent::update($request, $id); // TODO: Change the autogenerated stub
//    }

    public function cloneView($id)
    {
        try {
            $formData = $this->service->getCloneData($id);
            return view($this->folder . $this->viewsPrefix . ".clone", $formData);
        } catch (EntityNotFoundException $exception) {
            return redirect()->route('owner.shifts.show', $id)
                ->with('danger', $exception->getMessage());
        }
    }

    public function clone(Request $request, $id)
    {
        try {
            $requestOldData = $request->except(['_token']);
            $formattedShiftData = $this->service->formatFormData($requestOldData);
            $request->merge($formattedShiftData);
            $validatedData = $this->validateFormData($request);
            $cloneRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());
            $result = $this->service->clone($id, $cloneRequest);
            $label = $validatedData[$this->service->mainEntity->name_field] ?? __t($this->service->mainEntity->label) . ' #' . $result->id;
            return redirect()
                ->route($this->routesPrefix . $this->folder . '.show', [$this->service->mainEntity->key => $result->id])
                ->with('success', sprintf(__t('%s Cloned Successfully'), $label));
        } catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            Log::error('Shifts Controller Query Exception on Clone', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label)));
        } catch (ShiftNeedWeekDaysException | ShiftSaveFailedException $exception) {
            return redirect()->back()
                ->withInput(request()->all())
                ->with('danger', __t($exception->getMessage()));

        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exceptionMessage));

        } catch (\Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));
        }
    }

    protected function setBreadCrumbs($service,$return = false)
    {
        $breadCrumbs = [];
        if ($this->currentRoute->action['as'] == "owner.shifts.cloneView") {
            $breadCrumbs[] = [
                'link' => route('owner.shifts.index'),
                'title' => __t('Shifts')
            ];

            $shift = $this->service->find(request('shift'));
            $breadCrumbs[] = [
                'link' => route('owner.shifts.show', request('shift')),
                'title' => $shift->name
            ];

            $breadCrumbs[] = [
                'link' => "",
                'title' => __t("Clone")
            ];

            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);

        } else {
            parent::setBreadCrumbs($service);
        }
    }

    public function assignEmployees(
        $shift_id,
        ShiftRepository $shiftRepository,
        DepartmentRepository $departmentRepository,
        DesignationRepository $designationRepository,
    ) {
        $shift = $shiftRepository->find($shift_id);
        if(!$shift) {
            abort(404);
        }
        $activeBranches = [];
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $activeBranches = Branch::getStaffBranches()->toArray();
        }
        $activeDepartments = $departmentRepository->getActiveDepartments()->toArray();
        $activeDesignations = $designationRepository->getActiveDesignations()->toArray();
        $departments = $designations = [];
        foreach($activeDepartments as $department)
            $departments[$department['id']] = $department['name'];
        foreach ($activeDesignations as $designation)
            $designations[$designation['id']] = $designation['name'];
        $_PageBreadCrumbs= [
            ['title' => __t('Shift'), 'link' => route('owner.shifts.index')],
            ['title' => "{$shift->name} #{$shift->id}", 'link' => route('owner.shifts.show', ['shift' => $shift->id])],
            ['title' => __t('Assign To Employees'), 'link' => route('owner.shifts.index')],
        ];
        return view('assign_employees', [
            'departments' => $departments,
            'designations' => $designations,
            'branches' => $activeBranches,
            'entity' => $shift,
            'entity_key' => 'shift',
            'staffOptionsAttributes' => [],
            'generalBreadCrumbs' => $_PageBreadCrumbs,
            'submit_route' => route('owner.shifts.save_assign_employees')
        ]);
    }
    public function saveAssignEmployees(
        Request $request,
        ShiftRepository $shiftRepository,
        StaffRepository $staffRepository,
        ActivityLogService $activityLogService,
    ) {
        if(!isset($request['shift_id'])) {
            abort(404);
        }
        $shift = $shiftRepository->find($request['shift_id']);
        if(!$shift) {
            abort(404);
        }
        /** @var StaffService $staffService */
        $staffService = resolve(\App\Services\StaffService::class);
        $employee_ids = $staffService->getIdsByMultipleCriteriaSelection(
            $request->get('criteria'), $request->get('employees'),
            $request->get('branches') ?? [], $request->get('departments') ?? [], $request->get('designations') ?? [],
            null, null, $request->get('exclude_criteria') ?? []
        );
        $request = $request->all();
        $employeesWithOldLists = $staffRepository->pushCriteria(new InArrayCriteria('id', $employee_ids))->getWithAttendanceShfit()->toArray();
        $affectedCount = $shiftRepository->assignToEmployees($request['shift_id'], $employee_ids);
        $activityLogRequests = [];
        foreach ($employeesWithOldLists as $employeesWithOldList) {
            if(isset($employeesWithOldList['attendance_shift'][0]) && $employeesWithOldList['attendance_shift'][0]['id'] == $request['shift_id']){
                continue;
            }
            $activityLogRequests[] = new ActivityLogRequest(
                $employeesWithOldList['id'],
                EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                ActionLineMainOperationTypesUtil::UPDATE_ACTION,
                $employeesWithOldList['name'],
                route('owner.staff.show', ['staff' => $employeesWithOldList['id']]),
                [__t('Shift') => $shift->name],
                [__t('Shift') => $employeesWithOldList['attendance_shift'][0]['name'] ?? ''],
            );
        }
        foreach ($activityLogRequests as $activityLogRequest) {
            $activityLogService->addActivity($activityLogRequest);
        }
        return redirect(route('owner.shifts.show', ['shift' => $request['shift_id']]))
            ->with('success', sprintf(__t('The Attendance Shift assigned to (%s) Employee(s) Successfully'), $affectedCount));
    }

    /**
     * @param Request $request
     * @return view
     */
    public function destroyMulti(Request $request)
    {

        try{
            $redirectRoute = $this->routesPrefix . $this->folder . '.index';
            if($request['ids'] == 'none'){
                return redirect()->route($this->routesPrefix . $this->folder . '.index')
                    ->with('danger', sprintf(__t('No %s selected'), __t('Shift')));
            } elseif ($request['ids'] == 'all'){
                $allData = $this->service->getFilteredRecords()->toArray();
                $ids = array_keys($allData);
                $result = $this->service->deleteMany($ids);
            } else {
                $ids = explode(',', $request['ids']);
                $result = $this->service->deleteMany($ids);
            }

            if ($result['deletedCount'] > 0 && $result['unDeletedCount'] > 0){
                return redirect()->route($redirectRoute, $this->queryParameters)
                    ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Shift')))
                    ->with('invalidBulkDeleteData',   $result['invalidDeleteData']);
            } elseif ($result['deletedCount'] > 0 && $result['unDeletedCount'] == 0){
                return redirect()->route($redirectRoute, $this->queryParameters)
                    ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Shift')));
            } elseif ($result['deletedCount'] == 0 && $result['unDeletedCount'] > 0){
                return redirect()->route($redirectRoute, $this->queryParameters)
                    ->with('invalidBulkDeleteData',   $result['invalidDeleteData']);
            }
        }catch (NoShiftSelectedException $exception){
            return redirect()->route($redirectRoute, $this->queryParameters)
                ->with('danger',$exception->getMessage());
        }catch (EntityNotFoundException $exception){
            return redirect()->route($redirectRoute, $this->queryParameters)
                ->with('danger',$exception->getMessage());
        }
    }


    public function ajaxNameFilter(Request $request)
    {
        $query = $request->query->get('q');
        if (!empty($query)) {
            $results = $this->service->filterName($query);
            if ($results) {
                $processedResults = [];
                foreach ($results as $k => $v) {
                    $processedResults['results'][$k] = ['text' => "#{$v->id} {$v->name}", 'id' => $v->id];
                    if($request->query->get('fields')){
                        foreach ($request->query->get('fields') as $field)
                        {
                            $fieldParts = explode('.', $field);
                            if(count($fieldParts) > 1)
                            {
                                $processedResults['results'][$k][$field] = $v->{$fieldParts[0]} ? $v->{$fieldParts[0]}->{$fieldParts[1]} : '';
                            }else{
                                $processedResults['results'][$k][$field] = $v->{$fieldParts[0]};
                            }
                        }
                    }
                }
                return response()->json($processedResults);
            }
        }
        return response()->json([]);
    }
}
