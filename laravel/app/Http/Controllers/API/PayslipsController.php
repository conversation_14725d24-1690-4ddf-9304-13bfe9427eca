<?php

namespace App\Http\Controllers\API;

use App\Facades\Permissions;
use App\Http\Controllers\BaseController;
use App\Http\Resources\PayslipResource;
use App\Services\PaySlipService;
use App\Utils\PermissionUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Repository\EntityViewsLogsRepository;
use Symfony\Component\HttpFoundation\Response;

class PayslipsController extends BaseController
{
    /** @var PaySlipService */
    protected $service;

    public function __construct(PaySlipService $service)
    {
        parent::__construct($service);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function ownIndex(Request $request)
    {
        if(!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_PAYSLIPS))
            return response()->json(['message' => __t('You are not allowed to access this page')], Response::HTTP_FORBIDDEN);

        return response()->json($this->service->listingApprovedForUser(getAuthOwner('staff_id')), Response::HTTP_OK);
    }

    public function ownShow($id)
    {
        $payslipData = $this->service->getEssViewData($id);

        if(!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_PAYSLIPS) || empty($payslipData) || $payslipData->staff_id != getAuthOwner('staff_id'))
            return response()->json(['message' => __t('You are not allowed to access this page')], Response::HTTP_FORBIDDEN);

        $currentStaffId = getAuthOwner('staff_id');
        if($currentStaffId){
            EntityViewsLogsRepository::insertIfNotExist(EntityKeyTypesUtil::PAYSLIP, $id, $currentStaffId);
        }
        return response()->json(['data' => new PayslipResource($payslipData)], Response::HTTP_OK);
    }
    
}
