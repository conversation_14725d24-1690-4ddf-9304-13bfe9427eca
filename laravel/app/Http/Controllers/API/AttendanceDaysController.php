<?php

namespace App\Http\Controllers\API;

use App\Facades\Permissions;
use App\Http\Controllers\BaseController;
use App\Http\Resources\AttendanceDayResource;
use App\Services\AttendanceDayService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Symfony\Component\HttpFoundation\Response;

class AttendanceDaysController extends BaseController
{
    /** @var AttendanceDayService */
    protected $service;

    public function __construct(AttendanceDayService $service)
    {
        parent::__construct($service);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function ownIndex(Request $request)
    {
        if (!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG)){
            $errMsg = __t("You are not allowed to access this page");
            return response()->json([
                'error' => $errMsg,
                'message' => $errMsg
            ], 403);
        }
        
        $orderByDir = $request->get('orderBy', 'desc') == 'asc' ? 'asc' : 'desc';
        $attendanceDays = $this->service->listingForUser(
            getAuthOwner('staff_id'),
            $request->get('date_from'),
            $request->get('date_to'),
            $orderByDir
        );
        $data = AttendanceDayResource::collection($attendanceDays);
        $response = $attendanceDays->toArray();
        $response['data'] = $data->resolve();
        return response()->json($response, Response::HTTP_OK);
    }

}
