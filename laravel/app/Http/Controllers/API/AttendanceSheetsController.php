<?php

namespace App\Http\Controllers\API;

use App\Facades\Permissions;
use App\Http\Controllers\BaseController;
use App\Services\AttendanceSheetService;
use App\Utils\PermissionUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Repository\EntityViewsLogsRepository;
use Symfony\Component\HttpFoundation\Response;

class AttendanceSheetsController extends BaseController
{
    /** @var AttendanceSheetService */
    protected $service;

    public function __construct(AttendanceSheetService $service)
    {
        parent::__construct($service);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function ownIndex(Request $request)
    {
        if(!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET))
            return response()->json(['message' => __t('You are not allowed to access this page')], Response::HTTP_FORBIDDEN);

        return response()->json(['data' => $this->service->listingForUser(
            getAuthOwner('staff_id'),
            $request->get('start_date_from'),
            $request->get('end_date_from'),
            $request->get('start_date_to'),
            $request->get('end_date_to'),
            $request->get('orderBy') == 'asc' ? 'asc' : 'desc',
        )], Response::HTTP_OK);
    }

    public function ownShow($id)
    {
        $attendanceSheetData = $this->service->getAttendanceMobileAppViewData($id);

        if(!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET) || empty($attendanceSheetData) || $attendanceSheetData->staff_id != getAuthOwner('staff_id'))
            return response()->json(['message' => __t('You are not allowed to access this page')], Response::HTTP_FORBIDDEN);

        $currentStaffId = getAuthOwner('staff_id');
        if($currentStaffId){
            EntityViewsLogsRepository::insertIfNotExist(EntityKeyTypesUtil::ATTENDANCE_SHEET, $id, $currentStaffId);
        }
        return response()->json(['data' => $attendanceSheetData], Response::HTTP_OK);
    }



}
