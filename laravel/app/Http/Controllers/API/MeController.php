<?php

declare(strict_types=1);

namespace App\Http\Controllers\API;

use App\Factories\AppCustomResponseFactory;
use App\Facades\Branch;
use App\Models\Client;
use App\Models\Interfaces\HasBasicInfoInterface;
use App\Models\Staff;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Facades\Plugins;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\EntityEmailPreferenceUserTypeUtil;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Config;

class MeController
{
    public function __invoke(Request $request): JsonResponse
    {
        /** @var HasBasicInfoInterface $user */
        $user = $request->user();
        $site = getCurrentSite();
        $formattedBranches = [];
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            if($user->getUserType() == 'employee'){
                $employeeBranch = $user->branch;
                $formattedBranches[] = ['branch_id'=> $employeeBranch->id, 'name'=>$employeeBranch->name];
            }else{
                $branches = Branch::getStaffBranches();
                foreach($branches as $id => $name){
                    $formattedBranches[] = ['branch_id'=> $id, 'name'=>$name];
                }
            }
        }

        $tolerant = (int) $site['tolerant'] ?? 0;
        $responseArray = [
            'id' => $user->getId(),
            'user_type' => $user->getUserType(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
            'email' => $user->getEmail(),
            'email_address' => $user->getEmail(),
            'photo' => $this->getPhotoFullPath($user),
            'business_name' => $user->getBusinessName(),
            'role_id' => $user->getRoleId(),
            'branches' =>  $formattedBranches,
            'site_info'=> [
                'id'=> $site['id'],
                'business_name'=> $site['business_name'],
                'url'=> request()->getSchemeAndHttpHost(),
                'subdomain'=> $site['subdomain'],
                'site_logo'=> $site['site_logo_full_path'],
                'business_image'=> $site['site_logo_full_path'],
                'is_expired' => ($site['plan_id'] != 1 && (date('Y-m-d') > date("Y-m-d",strtotime($site['expiry_date']." +$tolerant day")))),
                'expiry_date' => date("Y-m-d", strtotime($site['expiry_date'])),
                'date_format'=> getDateFormats('moment_js')[getCurrentSite('date_format')]?? 'DD/MM/YYYY',
                'beta_version'=> $site['beta_version'],
                'is_beta'=> $site['beta_version']? true : false,
                'logo'=> 'https://' . $_SERVER['SERVER_NAME'] . $site['site_logo_full_path'],
                'timezone' => Config::get('app.timezone'),

            ]
        ];

        $customResponse = [];
        if($request->header('x-app-name')){
            $customResponse = AppCustomResponseFactory::getCustomResponse('x-app-name', $request->header('x-app-name'));
        }elseif($request->header('x-app-id')){
            $customResponse = AppCustomResponseFactory::getCustomResponse('x-app-id', $request->header('x-app-id'));
        }
        $responseArray = array_merge($responseArray, $customResponse);

        return new JsonResponse($responseArray, 200);
    }

    /* todo clean this */
    private function getPhotoFullPath($user): ?string
    {
        if ($user instanceof Client) {
            return $user->getPhotoPathAttribute();
        }

        if ($user instanceof Staff) {
            return $user->getImageAttribute();
        }

        return null;
    }

    public function siteInfo(): array
    {
        $site = getCurrentSite();
        $tolerant = (int) $site['tolerant'] ?? 0;
        return [
            'id' => $site['id'],
            'business_name' => $site['business_name'],
            'url' => request()->getSchemeAndHttpHost(),
            'subdomain' => $site['subdomain'],
            'site_logo' => $site['site_logo_full_path'],
            'business_image' => $site['site_logo_full_path'],
            'is_expired' => ($site['plan_id'] != 1 && (date('Y-m-d') > date("Y-m-d", strtotime($site['expiry_date'] . " +$tolerant day")))),
            'expiry_date' => date("Y-m-d", strtotime($site['expiry_date'])),
            'date_format' => getDateFormats('std')[$site['date_format'] == "" ? "0" : $site['date_format']],
            'is_hijiri' => getDateFormats('is_hijiri')[$site['date_format']] ? true : false,
            'country_code' => $site['country_code'],
            'timezone' => Config::get('app.timezone'),
            'beta_version' => $site['beta_version'],
            'is_beta' => $site['beta_version'] ? true : false,
            'logo' => 'https://' . $_SERVER['SERVER_NAME'] . $site['site_logo_full_path'],
            'language' => CurrentSiteLang(),
        ];
    }

    public function viewByToken(Request $request): JsonResponse
    {
        $userId = $request->get('user_id');
        $userType = $request->get('user_type');

        if (!$userId || !$userType) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $user = null;
        if ($userType === EntityEmailPreferenceUserTypeUtil::getStaff()) {
            $user = Staff::find($userId);
        } elseif ($userType === EntityEmailPreferenceUserTypeUtil::getClient()) {
            $user = Client::find($userId);
        }

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        return (new UserResource($user))->response();
    }
}
