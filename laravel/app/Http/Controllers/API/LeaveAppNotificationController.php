<?php

namespace App\Http\Controllers\API;

use App\Http\Resources\FcmDevicesAppResource;
use App\Services\DeviceAppNotificationService;
use App\Services\LeaveAppNotificationService;
use App\Utils\FcmDevicesAppUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\JsonResponse;
use Illuminate\Support\Facades\App;

/**
 * Class LeaveAppNotificationController
 *
 * Handles HTTP requests related to push notifications for leave applications.
 * Includes registration of push tokens, updating language preferences,
 * and managing notification preference settings.
 */
class LeaveAppNotificationController
{

    /**
     * LeaveAppNotificationController constructor.
     *
     * @param LeaveAppNotificationService $service
     * @param DeviceAppNotificationService $deviceAppNotificationService
     */
    public function __construct(
        private LeaveAppNotificationService $service,
        private DeviceAppNotificationService $deviceAppNotificationService,
    ) {
    }


    /**
     * Registers a push token for a user and optionally a device ID.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'push_token' => ['required'],
            'device_id' => ['nullable', 'string']
        ]);

        if ($validator->fails()) {
            return new JsonResponse([
                'code' => $statusCode = 400, 'message' => 'validation error', 'errors' => $validator->messages()
            ], $statusCode);
        }
        try {
            $data = array_merge($validator->getData(), ['user_id' => $request->user()->id]);
            $this->service->store($data);
            return new JsonResponse([
                'code' => $statusCode = 200, 'message' => 'done'
            ], $statusCode);
        } catch (\Exception $e) {
            return new JsonResponse([
                'code' => $statusCode = 500, 'message' => $e->getMessage()
            ], $statusCode);
        }
    }

    /**
     * Updates the language used for push notifications for a given user and device.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function changeLanguage(Request $request){
        try {
            $this->service->changeLanguageForPushNotifications(App::getLocale(), $request->user()->id, $request->get('device_id'), $request->get('push_token'));
            return new JsonResponse([
                'code' => $statusCode = 200, 'message' => 'done'
            ], $statusCode);
        } catch (\Exception $e) {
            return new JsonResponse([
                'code' => $statusCode = 500, 'message' => $e->getMessage()
            ], $statusCode);
        }
    }

    /**
     * Lists the push notification preferences for the authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listNotificationPreferences(Request $request){
        try {
            $data = $this->deviceAppNotificationService->listNotificationPreferences($request->user()->id, FcmDevicesAppUtil::LEAVE_APP);
            return new JsonResponse([
                'code' => $statusCode = 200,
                'data' => FcmDevicesAppResource::collection($data),
            ], $statusCode);
        } catch (\Exception $e) {
            return new JsonResponse([
                'code' => $statusCode = 500, 'message' => $e->getMessage()
            ], $statusCode);
        }
    }

    /**
     * Updates a specific notification preference for the authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateNotificationPreferences(Request $request){
        try {
            $this->deviceAppNotificationService->updateNotificationPreferences($request->user()->id, $request->all(), FcmDevicesAppUtil::LEAVE_APP);
            return new JsonResponse([
                'code' => $statusCode = 200, 'message' => "Success"
            ], $statusCode);
        } catch (\Exception $e) {
            return new JsonResponse([
                'code' => $statusCode = 500, 'message' => $e->getMessage()
            ], $statusCode);
        }        
    }

}
