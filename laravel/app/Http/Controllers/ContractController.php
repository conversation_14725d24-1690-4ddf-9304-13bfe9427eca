<?php

namespace App\Http\Controllers;

use App\Exceptions\BulkUpdate\InvalidBulkUpdateData;
use App\Exceptions\ContractCannotBeRenewed;
use App\Exceptions\ContractNotFoundException;
use App\Exceptions\ContractOverlappedException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\UnexpectedBehavior;
use App\Facades\Formatter;
use App\Forms\Payroll\ContractSettingCardList;
use App\IzamViews\IzamView;
use App\Modules\LocalEntity\Helpers\RelatedFormsHelper;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use App\Repositories\ContractRepository;
use App\Requests\DefaultRequest;
use App\Rules\FormattedDateOperation;
use App\Rules\FormulaFormat;
use App\Rules\IsValidPlaceholder;
use App\Rules\OnlyOneExistRule;
use App\Services\ContractService;
use App\Utils\EntityKeyTypesUtil;
use Doctrine\DBAL\Query\QueryException;
use App\Utils\ContractStatusUtil;
use App\Utils\ActiveStatusUtil;
use App\Utils\ContractDurationUnitsUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\PayrollFrequencyUtil;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Izam\Entity\Helper\EntityHasLegacyCustomFields;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Utils\PluginUtil;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\TitleText;

class ContractController extends BaseController {
    /**
     * @var ContractService
     */
    protected $service;
    protected $label = 'Contract';
    protected $entityKey = 'id';
    public $folder = 'contracts';

    public function __construct(ContractService $service, private AdditionalFieldsAwareService $additionalFieldsAwareService, private EntityRulesGetter $entityRulesGetter)
    {
        parent::__construct($service);
    }

    public function settings()
    {
        \View::share('extraContainerClass', 'container');
        return new IzamView('default/page_cards', [
            'cardList' => ContractSettingCardList::getCardList(),
            'title_for_layout' => __t('Payroll Settings'),
            '_PageBreadCrumbs' => $this->getBreadCrumbs(),
            'pageHead' => $this->getPageHeader(),
        ]);
    }

    /**
     * @param  Request  $request
     *
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function store(Request $request)
    {
        try {
            $validator = new DaftraValidator(EntityKeyTypesUtil::CONTRACT, $this->entityRulesGetter);
            $validator->setData($request->all());
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            if (!$validator->isValid()) {
                if (isApi()) {
                    return response()->json(['message' => __t($exceptionMessage), 'errors' => $validator->getErrors()], 422);
                } else {
                    return  redirect()->back()->withInput($request->all())
                        ->withErrors($validator->getErrors());
                }
            }
            $this->additionalFieldsAwareService->validateAdditionalFieldsFormData($request->all(), EntityKeyTypesUtil::CONTRACT);
            return parent::store($request);
        } catch (ValidationException $exception) {
            if (in_array(get_class($exception), $this->handledExceptions)) {
                throw $exception;
            }
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->route($this->getRouteName('create'), ['type' => request('type')])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }
    }


    public function getFormValidationRules(Request $request, $id=null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        $siteDateFormat = $dateFormats[$dateFormatIndex];

        if ($request->get('duration_unit') && $request->get('duration_value') && $request->get('start_date')) {
            $endDate = Carbon::createFromFormat($siteDateFormat, $request->get('start_date'));
            if ($request->get('duration_unit') === ContractDurationUnitsUtil::MONTH) {
                $monthsToAdd = $request->get('duration_value');
            } else {
                $monthsToAdd = $request->get('duration_value') * 12;
            }
            $endDate = $endDate->addMonth($monthsToAdd);
            $request->merge(['end_date' => $endDate->format($siteDateFormat)]);
        } else {
            $request->merge(['duration_unit' => null, 'duration_value' => null]);
        }

        $messages = [
            'mimes' => sprintf(__t('Invalid file type, the allowed file types are (%s)'), 'png,jpg,gif,bmp,rar,zip and all office files'),
            'size' => sprintf('You have exceeded the maximum file size upload (%sMB)', '25'),
            'required_without' => __t('Please enter only amount or formula'),
            'not_in' => __t('You can\'t add same salary component twice in same salary structure')
        ];

        $commonRules = [
            'designation_id' => 'nullable|exists:currentSite.designations,id,active,' . ActiveStatusUtil::ACTIVE . ',deleted_at,NULL',
            'department_id' => 'nullable|exists:currentSite.departments,id,status,' . ActiveStatusUtil::ACTIVE . ',deleted_at,NULL',
            'master_contract_id' => 'nullable|exists:currentSite.contracts,id,deleted_at,NULL',
            'start_date' => [
                'required',
                'date_format:' . $siteDateFormat,
            ],
            'end_date' => ['required', 'date_format:' . $siteDateFormat, new FormattedDateOperation('after', $request->get('start_date'), __t('The Contract End Date should be greater than the Start Date')),],
            'contract_sign_date' => 'nullable|date_format:' . $siteDateFormat,
            'join_date' => ['required', 'date_format:' . $siteDateFormat, new FormattedDateOperation('before_or_equal', $request->get('probation_end_date'), __t('The Probation period date should be greater than join date'))],
            'probation_end_date' => ['required', 'date_format:' . $siteDateFormat, new FormattedDateOperation('after_or_equal', $request->get('join_date'), __t('The Contract Probation Period should be greater than the Join date')),],
            'duration_unit' => ['nullable', Rule::in(array_keys(ContractDurationUnitsUtil::getDurationUnits()))],
            'duration_value' => 'nullable',
            'currency_code' => 'required|exists:currencies,code',
            'salary_structure_id' => 'nullable|exists:currentSite.salary_structures,id,status,' . ActiveStatusUtil::ACTIVE . ',deleted_at,NULL',
            'payroll_frequency' => ['required', Rule::in(array_keys(PayrollFrequencyUtil::getPayrollFrequency()))],
            'contractComponent.*.*.salary_component_id' => 'required|exists:currentSite.salary_components,id,status,' . ActiveStatusUtil::ACTIVE . ',deleted_at,NULL',
            'contractComponent.earning.basic.salary_component_id' => 'exists:currentSite.salary_components,id,is_basic,1,deleted_at,NULL',
            'contractComponent.earning.basic.amount' => 'required|numeric',
            'attachments.*.file' => ['mimes:txt,jpeg,bmp,gif,png,doc,docx,xls,csv,pdf,rar,zip|size:' . (25 * 1024)],
            'contractComponent.earning.*.amount' => ['nullable', 'numeric', 'required_without:contractComponent.earning.*.formula', new OnlyOneExistRule(['contractComponent.earning.*.formula'], __t('Please enter only amount or formula'))],
            'contractComponent.deduction.*.amount' => ['nullable', 'numeric', 'required_without:contractComponent.deduction.*.formula', new OnlyOneExistRule(['contractComponent.*.*.formula'], __t('Please enter only amount or formula'))],
            'contractComponent.earning.*.formula' => ['required_without:contractComponent.earning.*.amount', new IsValidPlaceholder('payslip'), new FormulaFormat],
            'contractComponent.deduction.*.formula' => ['required_without:contractComponent.deduction.*.amount', new IsValidPlaceholder('payslip'), new FormulaFormat],
        ];

        if ($id) {
            $rules = [
                'code' => "required|unique:currentSite.contracts,code,$id,id,deleted_at,NULL",
                'staff_id' => 'required|exists:currentSite.staffs,id',
            ];
        } else {
            $rules = [
                'code' => 'required|unique:currentSite.contracts,code,NULL,id,deleted_at,NULL',
                'staff_id' => 'required|exists:currentSite.staffs,id,active,' . ActiveStatusUtil::ACTIVE . ',deleted_at,NULL',
            ];
        }

        $rules += $commonRules;
        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    public function index(Request $request)
    {
        $_SESSION = array_merge($_SESSION, session()->all());
        if (!EntityHasLegacyCustomFields::check('contracts')) {
            return redirect()->route('owner.entity.list', [
                    'entityKey' => EntityKeyTypesUtil::CONTRACT,
                ] + $this->buildEntityListingQueryParamsForContract($request->query()));
        }
        if(ifPluginActive(PluginUtil::HRM_PAYROLL_PLUGIN)){
            /** @var ContractService */
            $contractService = resolve(ContractService::class);
            $contractService->autoRenewContracts();
        }
        $this->service->checkOverdueContracts();
        if ($request->ajax()) {
            $listingData = $this->service->listing();
            return view('partials.listing.grids.contracts', $listingData);
        } else {
            $listingData = $this->service->listing();
            return view($this->folder . $this->viewsPrefix . '.index', $listingData);
        }
    }

    private function formatRequest($request)
    {
        $request->merge([
            'start_date' => Formatter::formatForDB($request->get('start_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
            'end_date' => Formatter::formatForDB($request->get('end_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
            'join_date' => Formatter::formatForDB($request->get('join_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
            'probation_end_date' => Formatter::formatForDB($request->get('probation_end_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
            'contract_sign_date' => Formatter::formatForDB($request->get('contract_sign_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) ?: null,
        ]);
    }

    public function update(Request $request, $id)
    {
        try {
            // TODO: refactor this shit.
            $contract = $this->service->find($id);
            $rules = $this->getFormValidationRules($request, $id);
            $this->additionalFieldsAwareService->validateAdditionalFieldsFormData($request->all(), EntityKeyTypesUtil::CONTRACT);
            if (in_array($contract->status, [ContractStatusUtil::CANCELLED, ContractStatusUtil::TERMINATED])) {
                $dateFormatIndex = getCurrentSite('date_format');
                $dateFormats = getDateFormats('std');
                $rules['rules'] += [
                    'status_date' => [
                        'required',
                        'date_format:' . $dateFormats[$dateFormatIndex],
                        new FormattedDateOperation('before_or_equal', $request->end_date, __t('The Contract Date Should Be Less Than Or Equal The Contract End date')),
                        new FormattedDateOperation('after_or_equal', $request->start_date, __t('The Contract Date Should Be Greater Than Or Equal The Contract Start date')),
                    ],
                    'status_reason' => 'string|nullable'
                ];

            }
            $request->merge(['staff_id' => $contract->staff_id]);
            $request->validate($rules['rules'], $rules['messages']);
            return parent::update($request, $id);
        } catch (ValidationException $exception) {
            if (in_array(get_class($exception), $this->handledExceptions)) {
                throw $exception;
            }
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->route($this->getRouteName('edit'), ['id' => $id, 'type' => request('type')])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (UnexpectedBehavior $exception) {
            return redirect()->route('owner.request_types.index')->with('danger', $exception->getMessage());
        }
    }

    private function getBulkIds(Request $request){
        $ids = null;
        if(empty($request->get('ids')) || $request->get('ids') == 'none'){
            throw new InvalidBulkUpdateData();
        }elseif($request->get('ids') == 'all'){
            $ids = $this->service->getFilteredRecords()->pluck('id')->toArray();
        }elseif(is_array($request->get('ids'))){
            $ids = $request->get('ids');
        }else{
            $ids = explode(',', $request->get('ids'));
        }
        return $ids;
    }

    public function assignSalaryComponents(Request $request){
        try {
            $ids = $this->getBulkIds($request);
            return view('contracts.owner.progress', [
                'mainActionText' => __t('Assign Salary Component'),
                'mainSuccessText' => __t('Assign Salary Components was completed successfully'),
                'backUrl' => route('owner.entity.list', [EntityKeyTypesUtil::CONTRACT]),
                'ids' => $ids,
                'actionUrl' => route('owner.contracts.assign_one_contract_salary_component'),
                'actionMethod' => 'POST',
                'actionData' => [
                    'salary_component_ids' => $request->get('salary_component_ids'),
                    'override' => $request->get('override', 0),
                ],
            ]);
        } catch (InvalidBulkUpdateData $th) {
            $this->izamFlashMessage(sprintf(__t("No %s selected"),__t('Contract')),'danger');
            return redirect()->route('owner.entity.list', [EntityKeyTypesUtil::CONTRACT]);
        } catch (\Throwable $th) {
            $this->izamFlashMessage(__t("Invalid Data Supplied"),'danger');
            return redirect()->route('owner.entity.list', [EntityKeyTypesUtil::CONTRACT]);
        }
    }

    public function assignSalaryComponent(Request $request){
        try {
            return response()->json($this->service->assignSalaryComponents($request));
        } catch (\Throwable $th) {
            return response()->json([
                'error' => true,
                'message' => __t('Something went wrong'),
            ]);
        }
    }

    public function removeSalaryComponents(Request $request){
        try {
            $ids = $this->getBulkIds($request);
            return view('contracts.owner.progress', [
                'mainActionText' => __t('Remove Salary Component.'),
                'mainSuccessText' => __t('Remove Salary Components was completed successfully'),
                'backUrl' => route('owner.entity.list', [EntityKeyTypesUtil::CONTRACT]),
                'ids' => $ids,
                'actionUrl' => route('owner.contracts.remove_salary_component_from_one_contract'),
                'actionMethod' => 'POST',
                'actionData' => [
                    'salary_component_ids' => $request->get('salary_component_ids'),
                ],
            ]);
        } catch (InvalidBulkUpdateData $th) {
            $this->izamFlashMessage(sprintf(__t("No %s selected"),__t('Contract')),'danger');
            return redirect()->route('owner.entity.list', [EntityKeyTypesUtil::CONTRACT]);
        } catch (\Throwable $th) {
            $this->izamFlashMessage(__t("Invalid Data Supplied"),'danger');
            return redirect()->route('owner.entity.list', [EntityKeyTypesUtil::CONTRACT]);
        }
    }

    public function removeSalaryComponent(Request $request){
        try {
            return response()->json($this->service->removeSalaryComponents($request));
        } catch (\Throwable $th) {
            return response()->json([
                'error' => true,
                'message' => __t('Something went wrong'),
            ]);
        }
    }

    // In contracts routes the slug is "id" not "contract"
    public function getRouteSlug()
    {
        return 'id';
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function setAsDraft(Request $request, $id)
    {
        try {
            $updateRequest = new DefaultRequest(['status' => ContractStatusUtil::DRAFT]);
            $result = $this->service->setAsDraft($id, $updateRequest);

            if (isApi()) {
                return response()->json(['data' => $result]);
            } else {
                $status = ContractStatusUtil::getStatus(ContractStatusUtil::DRAFT);
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->with('success', __t("{$this->service->mainEntity->label} Saved As {$status}"));
            }
        } catch (QueryException $exception) {
            $exceptionMessage = sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label));
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => $exceptionMessage], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', $exceptionMessage);
            }
        } catch (\ErrorException $exception) {
            $exceptionMessage = 'Something went wrong';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Error Exception on setAsDraft', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => [], 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Exception on setAsDraft', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }
    }

    /**
     * @param $request
     * @param $id
     * @return mixed
     */
    private function validateCancelledAndTerminatedData($request, $id)
    {
        $contract = $this->service->find($id);
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        $rules = parent::getFormValidationRules($request, $id);
        $rules['rules'] += [
            'status_date' => [
                'required',
                'date_format:' . $dateFormats[$dateFormatIndex],
                new FormattedDateOperation('before_or_equal', formatForView($contract->end_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE), __t('The Contract Date Should Be Less Than Or Equal The Contract End date')),
                new FormattedDateOperation('after_or_equal', formatForView($contract->start_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE), __t('The Contract Date Should Be Greater Than Or Equal The Contract Start date')),
            ],
            'status_reason' => 'string|nullable'
        ];
        $request->validate($rules['rules'], $rules['messages']);
        return $request;
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function cancelView($id)
    {
        $contract = $this->service->find($id);
        $pageTitle = __t("Contract Cancellation");
        return view($this->folder . $this->viewsPrefix . '.cancellation', compact('id', 'contract', 'pageTitle'));
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function setAsCancelled(Request $request, $id)
    {
        try {
            $request->merge(['status' => ContractStatusUtil::CANCELLED]);
            $validatedData = $this->validateCancelledAndTerminatedData($request,$id);
            $updateRequest = new DefaultRequest($validatedData->only(['status','status_reason','status_date']));
            $result = $this->service->setAsCancelled($id, $updateRequest);

            if (isApi()) {
                return response()->json(['data' => $result]);
            } else {
                $status = ContractStatusUtil::getStatus(ContractStatusUtil::CANCELLED);
                return redirect()
                    ->route('owner.contracts.show',['id' => $id])
                    ->with('success',__t("{$this->service->mainEntity->label} Saved As {$status}"));
            }
        } catch (QueryException $exception) {
            $exceptionMessage = sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label));
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => $exceptionMessage], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', $exceptionMessage);
            }
        } catch (\ErrorException $exception) {
            $exceptionMessage = 'Something went wrong';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Error Exception on setAsCancelled', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => [], 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.cancel.show', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Exception on setAsCancelled', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.cancel.show', ['id' => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function terminateView($id)
    {
        $contract = $this->service->find($id);
        $pageTitle = __t("Contract Termination");
        return view($this->folder . $this->viewsPrefix . '.termination', compact('id','contract','pageTitle'));
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function setAsTerminated(Request $request, $id)
    {
        try {
            $request->merge(['status' => ContractStatusUtil::TERMINATED]);
            $validatedData = $this->validateCancelledAndTerminatedData($request, $id);
            $updateRequest = new DefaultRequest($validatedData->only(['status', 'status_reason', 'status_date']));
            $result = $this->service->setAsTerminated($id, $updateRequest);

            if (isApi()) {
                return response()->json(['data' => $result]);
            } else {
                $status = ContractStatusUtil::getStatus(ContractStatusUtil::TERMINATED);
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->with('success', __t("{$this->service->mainEntity->label} Saved As {$status}"));
            }
        } catch (QueryException $exception) {
            $exceptionMessage = sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label));
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => $exceptionMessage], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', $exceptionMessage);
            }
        } catch (\ErrorException $exception) {
            $exceptionMessage = 'Something went wrong';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Error Exception on setAsTerminated', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => [], 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.terminate.show', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Exception on setAsTerminated', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.terminate.show', ['id' => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }
    }

    /**
     * activate contract
     * @param Request $request
     * @param int $id contract id
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function activate(Request $request, int $id)
    {
        $this->handledExceptions = array_merge($this->handledExceptions, [
            EntityNotFoundException::class,
            ContractOverlappedException::class
        ]);

        try {
            $result = $this->service->activate($id);

            if (isApi()) {
                return response()->json(['data' => $result]);
            } else {
                $status = $result->status;
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->with('success',sprintf(__t('Contract Saved as %s'), __t(ucwords($status))));
            }
        } catch (QueryException $exception) {
            $exceptionMessage = sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label));
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => $exceptionMessage], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', $exceptionMessage);
            }
        } catch (\ErrorException $exception) {
            $exceptionMessage = 'Something went wrong';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Error Exception on Activate', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => [], 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Exception on Activate', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()
                    ->route('owner.contracts.show', ['id' => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }
    }

    /**
     * @param $request
     * @param $id
     * @return mixed
     */
    private function validateSupersedeData(Request $request, $id)
    {
        $rules = $this->getFormValidationRules($request,$id);
        $rules['rules'] += [
            'status_date' => [
                'required'
            ],
            'status_reason' => 'string|nullable'
        ];

        $request->validate($rules['rules'], $rules['messages']);
        return $request;
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function supersedeView($id)
    {
        $data = $this->service->getSupersedeData($id);
        $blade = $this->blade ?? 'create';
        $data['action_label'] = __t("Supersede Information");
        $data['action'] = [
            'route' => route('owner.contracts.supersede', ['id' => $id]),
            'method' => 'POST'
        ];

        return view($this->folder . $this->viewsPrefix . '.' . $blade, $data);
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     * @throws ValidationException
     */
    public function supersede(Request $request, $id)
    {
        $this->handledExceptions = array_merge($this->handledExceptions, [
            EntityNotFoundException::class,
            ContractOverlappedException::class
        ]);

        try {
            $validatedData = $this->validateSupersedeData($request, $id);
            $storeRequest = new DefaultRequest($validatedData->input());

            $result = $this->service->supersede($storeRequest, $id);

            if (isApi()) {
                return response()->json(['data' => $this->service->find($result->id)], 200);
            } else {
                if ($this->service->mainEntity->name_field) {
                    $label = $validatedData[$this->service->mainEntity->name_field];
                } else {
                    $label = __t($this->service->mainEntity->label) . ' #' . $result->id;
                }

                return redirect()
                    ->route($this->routesPrefix . $this->folder . '.show', ['id' => $result->id])
                    ->with('success', sprintf(__t('%s Superseded Successfully'), $result->oldContractCode));
            }
        } catch (QueryException $exception) {
            $exceptionMessage = sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label));
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => $exceptionMessage], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', $exceptionMessage);
            }
        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\ErrorException $exception) {
            $exceptionMessage = 'Something went wrong';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Error Exception on Supersede', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => [], 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->route($this->routesPrefix . $this->folder . '.edit', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Exception on Supersede', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }
    }

    public function renewView($id)
    {
        try{
            $formData = $this->service->getRenewData($id);
        } catch (ContractNotFoundException $exception){
            return redirect()->route('owner.contracts.index')
                ->with('danger', $exception->getMessage());
        }

        if (empty($formData['form_record'])) {
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t(strtolower($this->label))));
        }

        if(!$this->service->isValidStatusForRenew($formData['form_record']['status'])){
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                ->with('danger', sprintf(__t('You cannot renew %s, %s, %s or %s  contract'), __t(ContractStatusUtil::getStatus(ContractStatusUtil::CANCELLED)),__t(ContractStatusUtil::getStatus(ContractStatusUtil::TERMINATED)),__t(ContractStatusUtil::getStatus(ContractStatusUtil::PENDING)),__t(ContractStatusUtil::getStatus(ContractStatusUtil::SUPERSEDED))));
        }
        $formData['action'] = [
            'route' => route('owner.contracts.renew',$id),
            'method' => 'POST'
        ];
        $pageTitle = __t('Contract Renew');
        $formData['pageTitle'] = $pageTitle;
        return view($this->folder . $this->viewsPrefix . '.' . 'create', $formData);
    }

    public function renew(Request $request, $id)
    {
        $this->handledExceptions = array_merge($this->handledExceptions, [
            EntityNotFoundException::class,
            ContractOverlappedException::class
        ]);

        try {
            if (isset($request['CustomData'])) {
                foreach ($request['CustomData'] as $key => $row) {
                    $request->replace($request->except("CustomData.$key.id"));
                }
            }

            $validatedData = $this->validateFormData($request);
            $storeRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());
            $result = $this->service->renewContract($id, $storeRequest);//$this->service->add($storeRequest,[],$callBack);

            if (isApi()) {
                return response()->json(['data' => $this->service->find($result->id)]);
            } else {
                if ($this->service->mainEntity->name_field) {
                    $label = __t($result->oldCode);
                } else {
                    $label = __t($this->service->mainEntity->label) . ' #' . $result->oldCode;
                }

                return redirect()
                    ->route($this->routesPrefix . $this->folder . '.show', ['id' => $result->id])
                    ->with('success', sprintf(__t('%s Renewed Successfully'), $label));
            }
        } catch (ContractCannotBeRenewed $exception) {
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                ->with('danger', sprintf($exception->getMessage(), __t(ContractStatusUtil::getStatus(ContractStatusUtil::CANCELLED)), __t(ContractStatusUtil::getStatus(ContractStatusUtil::TERMINATED)), __t(ContractStatusUtil::getStatus(ContractStatusUtil::PENDING))));
        } catch (QueryException $exception) {
            $exceptionMessage = sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label));
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['message' => $exceptionMessage], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', $exceptionMessage);
            }
        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\ErrorException $exception) {
            $exceptionMessage = 'Something went wrong';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Error Exception on Renew', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => [], 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->route($this->routesPrefix . $this->folder . '.edit', ['id' => $id])
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Contract Controller Exception on Renew', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }
    }

    protected function setBreadCrumbs($service,$return = false)
    {
        $listing = '/v2/owner/contracts';
        if (!\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('contracts')) {
            $listing =  '/v2/owner/entity/contract/list';
        }
        $breadCrumbs = [];
        if ($this->currentRoute->action['as'] == 'owner.contracts.cancel.show') {
            $breadCrumbs[] = [
                'link' => $listing,
                'title' => __t('Contracts')
            ];

            $breadCrumbs[] = [
                'link' => route('owner.contracts.show', ['id' => request('id')]),
                'title' => __t('#' . request('id'))
            ];

            $breadCrumbs[] = [
                'link' => null,
                'title' => __t('Cancel')
            ];

            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } elseif ($this->currentRoute->action['as'] == 'owner.contracts.terminate.show') {
            $breadCrumbs[] = [
                'link' => $listing,
                'title' => __t('Contracts')
            ];

            $breadCrumbs[] = [
                'link' => route('owner.contracts.show', ['id' => request('id')]),
                'title' => __t('#' . request('id'))
            ];

            $breadCrumbs[] = [
                'link' => null,
                'title' => __t('Terminate')
            ];

            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } elseif ($this->currentRoute->action['as'] == 'owner.contracts.supersede.show') {
            $breadCrumbs[] = [
                'link' => $listing,
                'title' => __t('Contracts')
            ];

            $breadCrumbs[] = [
                'link' => route('owner.contracts.show', ['id' => request('id')]),
                'title' => __t('#' . request('id'))
            ];

            $breadCrumbs[] = [
                'link' => null,
                'title' => __t('Supersede')
            ];

            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } elseif ($this->currentRoute->action['as'] == 'owner.contracts.renew.show') {
            $breadCrumbs[] = [
                'link' => $listing,
                'title' => __t('Contracts')
            ];

            $breadCrumbs[] = [
                'link' => route('owner.contracts.show', ['id' => request('id')]),
                'title' => __t('#' . request('id'))
            ];

            $breadCrumbs[] = [
                'link' => null,
                'title' => __t('Renew')
            ];

            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } elseif ($this->currentRoute->action['as'] == 'owner.contracts.show') {
            $breadCrumbs[] = [
                'link' => $listing,
                'title' => __t('Contracts')
            ];

            $breadCrumbs[] = [
                'link' => route('owner.contracts.show', ['id' => request('id')]),
                'title' => __t('Contract') . ' ' . __t('#' . request('id'))
            ];

            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } elseif ($this->currentRoute->action['as'] == 'owner.contracts.create') {
            $breadCrumbs[] = [
                'link' => $listing,
                'title' => __t('Contracts')
            ];

            $breadCrumbs[] = [
                'link' => null,
                'title' => __t('Add')
            ];
            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } elseif ($this->currentRoute->action['as'] == 'owner.contracts.edit') {
            $breadCrumbs[] = [
                'link' => $listing,
                'title' => __t('Contracts')
            ];

            $breadCrumbs[] = [
                'link' => route('owner.contracts.show', ['id' => request('id')]),
                'title' => __t('Contract') . ' ' . __t('#' . request('id'))
            ];

            $breadCrumbs[] = [
                'link' => null,
                'title' => __t('Edit')
            ];
            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } else {
            $name = $service->mainEntity->name_field;
            $service->mainEntity->name_field = null;
            $result = parent::setBreadCrumbs($service);
            $service->mainEntity->name_field = $name;

            return $result;
        }
    }

    public function show($id)
    {
        $this->service->autoRenewContract($id);
        RelatedFormsHelper::setViewData($id, EntityKeyTypesUtil::CONTRACT);
        return parent::show($id);
    }

    /**
     * ToDo convert breadCrumbs to separated service
     */
    private function getBreadCrumbs(): array
    {
        return [
            [
                'title' => __t('Payroll Settings'),
            ],
        ];
    }
    /**
     * ToDo convert pageHeader to separated service
     */
    private function getPageHeader(): Header
    {
        $pageTitle = new TitleText('page-title');
        $pageTitle->setTitle(__t('Payroll Settings'));

        $header = new Header('page-header');
        $header->addLeft($pageTitle);

        return $header;
    }


    public function apiGetPayrollCurrencies()
    {
        try {
            return response()->json($this->service->getPayrollCurrencies());
        } catch (\Throwable $t) {
            return response()->json([
                'error' => $t->getMessage(),
            ], 400);
        }
    }

    private function buildEntityListingQueryParamsForContract($params = []): array
    {
        $queryParameters = [];
        foreach ($params as $key => $value) {
            match ($key) {
                'staff_status' => $queryParameters['filter']['contract_staff.active'] = $value,
                'expiry_type' => $queryParameters['filter']['status'] = $value == 'not_renewed' ? 'expired' : $value,
                default => null,
            };
        }
        if (!empty($params['sort_field'])) {
            $sort = explode('-', $params['sort_field']);
            $queryParameters['sort'][$sort[0]] = $sort[1];
        }
        return $queryParameters;
    }
}
