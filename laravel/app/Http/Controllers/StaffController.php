<?php

namespace App\Http\Controllers;

use App\Exceptions\AccountSuspendedException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Staff\StaffAssignedToContract;
use App\Exceptions\Staff\StaffAssignedToLoan;
use App\Exceptions\Staff\StaffIsDepartmentManager;
use App\Exceptions\Staff\StaffIsInConfigurationApprovalLevel;
use App\Exceptions\Staff\StaffTypeInvalid;
use App\Exceptions\SystemLimit\SystemLimitExceeded;
use App\Facades\CakeSession;
use App\Facades\Plugins;
use App\Facades\Staff as FacadesStaff;
use App\Facades\SystemLimitFactory;
use App\Helpers\AutoNumber;
use App\Models\BaseModel;
use App\Models\Staff;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Helpers\RelatedFormsHelper;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use App\Repositories\ContractRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\LanguageRepository;
use App\Repositories\TokenRepository;
use App\Requests\DefaultRequest;
use App\Rules\ActiveBranchRule;
use App\Services\AttendanceLogService;
use App\Services\AttendancePermissionService;
use App\Services\AttendanceReportsService;
use App\Services\AttendanceSheetService;
use App\Services\AttendanceDayService;
use App\Services\ContractService;
use App\Services\LeaveApplicationService;
use App\Services\LoanService;
use App\Services\PaySlipService;
use App\Services\SettingService;
use App\Services\StaffService;
use App\Utils\AttendanceDayStatusTypesUtil;
use App\Utils\AttendanceLogStatusUtil;
use App\Utils\AttendanceSheetStatusTypesUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\ValidationException;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;
use Izam\Daftra\Staff\Util\StaffTypeUtil;
use Izam\Entity\Helper\EntityHasLegacyCustomFields;
use Izam\Entity\LocalEntityForm\AdditionalFormsHandler;
use Izam\Entity\Service\ShowLegacyListing;
use Barryvdh\Debugbar\Facades\Debugbar;

class StaffController extends BaseController
{
    /**
     * @var $folder
     */
    public $folder = 'staff';
    /**
     * @var $viewsPrefix
     */
    public $viewsPrefix = 'staff';
    /**
     * @var StaffService $service
     */
    protected $service;

    protected $label = 'Employee';

    function __construct( SettingService $settingService, private TokenRepository $tokenRepository, private AdditionalFieldsAwareService $additionalFieldsAwareService , private AttendancePermissionService $attendancePermissionService , private ContractService $contractService , private LoanService $loanService , private PaySlipService $paySlipService,StaffService $service)
    {
        $this->settingService = $settingService;
        parent::__construct($service);
    }

    public function settings()
    {
        $customFieldsUrl = '/v2/owner/local_entities/custom_data/staff/create?redirect=/v2/owner/staff/settings';

        if (EntityHasLegacyCustomFields::check('staffs')) {
            $customFieldsUrl = '/owner/custom_forms/edit_custom_fields/staffs?redir=0';
        }

        return view($this->folder . $this->viewsPrefix . '.settings' , compact('customFieldsUrl'));
    }

    private function formatSearchResults($results)
    {
        $processedResults = [];
        foreach ($results as $k => $v) {
            $numberField= $v->code ??$v->id;
            if (!empty($v->email_address)) {
                $processedResults['results'][$k] = [
                    'img' => AvatarURLGenerator::generate($v->name, $v->id, 30, $v->photo)/*!empty($v->image) ? resizeImage($v->image, ['w' => 30, 'h' => 30, 'c' => 1]) : getDefaultAccountImage()*/,
                    'text' => "#{$numberField} {$v->name} {$v->last_name} ({$v->email_address})",
                    'id' => $v->id
                ];
            } else {
                $processedResults['results'][$k] = [
                    'img' => AvatarURLGenerator::generate($v->name, $v->id, 30, $v->photo),
                    'text' => "#{$numberField} {$v->name} {$v->last_name}",
                    'id' => $v->id
                ];
            }

            if (request()->query->get('fields')) {
                foreach (request()->query->get('fields') as $field) {
                    $fieldParts = explode('.', $field);
                    if (count($fieldParts) > 1) {
                        $processedResults['results'][$k][$field] = $v->{$fieldParts[0]} ? $v->{$fieldParts[0]}->{$fieldParts[1]} : '';
                    } else {
                        $processedResults['results'][$k][$field] = $v->{$fieldParts[0]};
                    }
                }
            }
        }
        return $processedResults;
    }

    public function getStaffUsersOnly(Request $request)
    {
        $query = $request->query->get('q');
        $allowInActive = $request->query->get('allow_inactive') ?? false;
        $getSuspended = $request->query->get('get_branch_suspended') ?? false;
        $selected =  $request->query->get('selected') ? explode(',',$request->query->get('selected')): [];
        if (!empty($query)) {
            $results = $this->service->filterName($query, $allowInActive, $getSuspended, $selected);
            $results = $results->filter(function ($value) {
                return $value->type === StaffTypeUtil::USER;
            })->values();
            if ($results) {
                return response()->json($this->formatSearchResults($results));
            }
        }
        return response()->json([]);
    }

    public function getAllUsers(Request $request) {
        $query = $request->query->get('q');
        $allowInActive = $request->query->get('allow_inactive') ?? false;
        $getSuspended = $request->query->get('get_branch_suspended') ?? false;
        $selected =  $request->query->get('selected') ? explode(',',$request->query->get('selected')): [];

        if (!empty($query)) {
            $results = $this->service->filterName($query, $allowInActive, $getSuspended, $selected);
            if ($results) {
                return response()->json($this->formatSearchResults($results));
            }
        }
        return response()->json([]);
    }

    public function ajaxNameFilter(Request $request, SmartEmployeeSearchService $smartEmployeeSearchService)
    {
        Debugbar::startMeasure('ajaxNameFilter', 'Ajax Name Filter - Total Request');

        // Get query without trimming spaces
        Debugbar::startMeasure('parse_parameters', 'Parse Request Parameters');
        $query = $request->input('q');
        $allowInActive = $request->query->get('allow_inactive') ?? false;
        $getSuspended = $request->query->get('get_branch_suspended') ?? false;
        $withResultsKey = $request->query->get('with_results_key') ?? true;
        $selected =  $request->query->get('selected') ? explode(',',$request->query->get('selected')): [];
        Debugbar::stopMeasure('parse_parameters');

        if (!empty($query)) {
            Debugbar::startMeasure('employee_search', 'SmartEmployeeSearchService - Search Execution');
            $results = $smartEmployeeSearchService->searchWithInactiveAndSuspended($query, 10, $allowInActive, $getSuspended, $selected);
            Debugbar::stopMeasure('employee_search');

            if ($results) {
                Debugbar::startMeasure('format_response', 'Format JSON Response');
                if($withResultsKey){
                    $response = response()->json(["results" => $results]);
                }else{
                    $response = response()->json($results);
                }
                Debugbar::stopMeasure('format_response');
                Debugbar::stopMeasure('ajaxNameFilter');
                return $response;
            }
        }

        Debugbar::stopMeasure('ajaxNameFilter');
        return response()->json([]);
    }

    public function searchForRequests(Request $request, SmartEmployeeSearchService $smartEmployeeSearchService)
    {
        $query = $request->query->get('q');
        $requestTypeId = $request->query->get('request_type');
        if (!empty($query)) {

            $results = $smartEmployeeSearchService->autoCompleteForRequests($query, $requestTypeId);
            if ($results) {
                return response()->json(["results" => $results]);
            }
        }
        return response()->json([]);
    }

    public function index(Request $request)
    {
        $_SESSION = array_merge($_SESSION, session()->all());
        if (!EntityHasLegacyCustomFields::check('staffs')) {
            return redirect()->route('owner.entity.list', [EntityKeyTypesUtil::STAFF_ENTITY_KEY]);
        }
        if(ifPluginActive(PluginUtil::HRM_PAYROLL_PLUGIN)){
            /** @var ContractService */
            $contractService = resolve(ContractService::class);
            $contractService->autoRenewContracts();
        }
        $listingData = $this->service->listing();
        $HRM_active = 0;
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN))
            $HRM_active = 1;
        if (request()->isXmlHttpRequest()) {
            unset($listingData['sort_fields']);

            return view('partials.listing.grids.staff', $listingData)->with('HRM_active', $HRM_active);
        }
        return view($this->folder . $this->viewsPrefix . '.index', $listingData)->with('HRM_active', $HRM_active)->with('force_menu', 1);
    }

    public function show($id)
    {
        if (!$this->service->ownerBybassIsAccessibleStaffId($id)) {
            $this->izamFlashMessage(sprintf(__t('You do not have access to this %s', true), __t('Record', true)), 'danger');
            return redirect()->route('owner.entity.list', ['entityKey' => EntityKeyTypesUtil::STAFF_ENTITY_KEY]);
        }
        if(ifPluginActive(PluginUtil::HRM_PAYROLL_PLUGIN)){
            $staff = $this->service->find($id);
            /** @var ContractService */
            $contractService = resolve(ContractService::class);
            $contractService->autoRenewContracts($staff->contracts ?? null);
        }
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $this->blade = 'show-hr';
        }
        $defaultFiscalDate = $this->settingService->getFiscalDate();
        \Illuminate\Support\Facades\View::share($defaultFiscalDate);
        RelatedFormsHelper::setViewData($id, EntityKeyTypesUtil::STAFF_ENTITY_KEY);

        if (request()->has('mobile_beta')) {
            session()->flash('mobile_beta', true);
        }

        return parent::show($id);
    }

    public function create()
    {
        if (!Session::has('danger')) {
            try {

                $this->service->checkStaffType(request('type'));

                SystemLimitFactory::init(request('type'))->check(getCurrentSite('id'));

            } catch (SystemLimitExceeded $exception) {
                return redirect()->route($this->routesPrefix . $this->folder . '.index')->with('limit_exceeded', $exception->getResult());
            } catch (AccountSuspendedException $exception) {
                return redirect()->route($this->routesPrefix . $this->folder . '.index')->with('danger', $exception->getMessage());
            } catch (StaffTypeInvalid $exception) {
                abort(404);
            }
        }
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $this->blade = 'create-hr';
        }
        return parent::create();
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function store(Request $request)
    {
        try {
            $this->service->checkStaffType(request('type'));
            $this->additionalFieldsAwareService->validateAdditionalFieldsFormData($request->all(), EntityKeyTypesUtil::STAFF_ENTITY_KEY);
            $this->service->checkStaffAccessable($request, "direct_manager_id");

            SystemLimitFactory::init($request->type)->check(getCurrentSite('id'));

            $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
            $isValid = $additionalFormsHandler->validate($request->all());

            $staff = parent::store($request);
            if (!$isValid) {
                $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
                if (isApi()) {
                    return response()->json(['errors' => $additionalFormsHandler->getErrors(), 'message' => __t($exceptionMessage)], 400);
                } else {
                    return redirect(route($this->getRouteName('create'), ['type' => request('type')]))
                        ->withInput(request()->all())
                        ->with('danger', __t($exceptionMessage));
                }
            }

            return $staff;

        } catch (StaffTypeInvalid $exception) {
            if(isApi()){
                return response()->json(['message' => 'Staff type is invalid'], 400);
            }
            abort(404);
        } catch (ValidationException $exception) {
            if (in_array(get_class($exception), $this->handledExceptions)) {
                throw $exception;
            }
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->route($this->getRouteName('create'), ['type' => request('type')])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (SystemLimitExceeded $exception) {
            if(isApi()){
                $result = $exception->getResult();
                return response()->json(['message' => sprintf(__t($result['message']), __t($result['title']))], 400);
            }
            return redirect()->route($this->routesPrefix . $this->folder . '.index')->with('limit_exceeded', $exception->getResult());
        }
    }

    public function routeParam(): array
    {
        return ['type' => request('type') ?? StaffTypeUtil::USER];
    }

    /**
     * Update the specified resource in storage.
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return Response
     * @throws \Exception
     */
    public function update(Request $request, $id)
    {
        try {
            $oldStaffData = $this->service->getFormData($id);

            if (!$request['can_access_system'] && ($oldStaffData['form_record']->role_id != Staff::OWNER_ROLE_ID)) {
                if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                    $request['branches'] = [];
                }
                $request['role_id'] = null;
                $request['language_code'] = null;
            }
            if($oldStaffData['form_record']->role_id == Staff::OWNER_ROLE_ID){
                $request['role_id'] = Staff::OWNER_ROLE_ID;
                unset($request['name']);
                unset($request['last_name']);
                unset($request['email_address']);
                unset($request['code']);
                unset($request['can_access_system']);
                unset($request['send_credentials']);
                unset($request['language_code']);
                unset($request['branches[]']);
                unset($request['mobile']);
                unset($request['address1']);
                unset($request['address2']);
                unset($request['city']);
                unset($request['state']);
                unset($request['postal_code']);
                unset($request['type']);
                unset($request['home_phone']);
                unset($request['branch_id']);
                unset($request['country_code']);
                $request['name'] = $oldStaffData['form_record']['name'];
                $request['last_name'] = $oldStaffData['form_record']['last_name'];
            }

            if (!$this->service->ownerBybassIsAccessibleStaffId($id, true)) {
                throw new \Exception(sprintf(__t('You do not have access to this %s', true), __t('Record', true)));
            }

            /** important to validate this before validating original form, to keep errors and data */
            $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
            $isValid = $additionalFormsHandler->validate($request->all());

            $validatedData = $this->validateFormData($request, $id);
            $this->service->checkStaffAccessable($request, "direct_manager_id");
            $this->additionalFieldsAwareService->validateAdditionalFieldsFormData($request->all(), EntityKeyTypesUtil::STAFF_ENTITY_KEY);

            if (!$isValid) {
                $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
                if (isApi()) {
                    return response()->json(['errors' => $additionalFormsHandler->getErrors(), 'message' => __t($exceptionMessage)], 400);
                } else {
                    return redirect(route($this->getRouteName('edit'), [$this->service->mainEntity->key => $id]))
                        ->withInput(request()->all())
                        ->with('danger', __t($exceptionMessage));
                }
            }

            $updateRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());

            $result = $this->service->update($id, $updateRequest);
            if (isApi()) {
                return response()->json(['data' => $result], 200);
            } else {
                $label = $this->getSaveLabel($validatedData, $result);

                return redirect($this->getShowRouteUrl($result))
                    ->with('success', sprintf(__t('%s Updated Successfully'), __t($label)));
            }
        } catch (SystemLimitExceeded $exception) {
            return redirect()->route($this->routesPrefix . $this->folder . '.index')->with('limit_exceeded', $exception->getResult());
        } catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            Log::error('Base Controller Query Exception on Update', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {

                return redirect()->route($this->getRouteName('edit'), [$this->service->mainEntity->key => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('Couldn\'t save the %s, Please try again'), __t($this->label)));
            }
        } catch (ValidationException $exception) {
            if (in_array(get_class($exception), $this->handledExceptions)) {
                throw $exception;
            }
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->route($this->getRouteName('edit'), [$this->service->mainEntity->key => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\ErrorException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            Log::error('Base Controller Syntax error  Exception on Update', ['Message' => $exception->getMessage(), "trace"=>$exception->getTrace(), 'Errors' => $errors, 'Exception' => $exception]);
            $exceptionMessage = 'Something went wrong';
            if (isApi()) {
                return response()->json(['errors' => [], 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->route($this->getRouteName('edit'), [$this->service->mainEntity->key => $id])
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        } catch (\Exception $exception) {
            if (in_array(get_class($exception), $this->handledExceptions)) {
                throw $exception;
            }
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route($this->getRouteName('edit'), [$this->service->mainEntity->key => $id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }

    protected function getShowRouteUrl(BaseModel $model)
    {
        if (isSiteSuspended()) {
            return route($this->getRouteName('index'), [$this->service->mainEntity->key => $model->id, 'force_menu' => 1]);
        } else {
            return parent::getShowRouteUrl($model);
        }
    }

    public function edit($id)
    {
        if (!$this->service->ownerBybassIsAccessibleStaffId($id, true)) {
            if (isApi()) {
                return response()->json(['message' => sprintf(__t('You do not have access to this %s', true), __t('Record', true))], 400);
            } else {
                return redirect()->back()->with('danger', sprintf(__t('You do not have access to this %s', true), __t('Record', true)));
            }
        }
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $this->blade = 'create-hr';
        }
        return parent::edit($id);
    }

    //General Methods
    public function getFormValidationRules(Request $request, $id = null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        if ($id === false) {
            $id = 'NULL';
        }
        $validateRoles = [
            'name' => 'required',
            'code' => 'required|unique:currentSite.staffs,code'. ($id ? ',' . $id : '') . ',id,deleted_at,NULL',
            'official_id' => 'nullable|regex:/^[A-Za-z0-9-]+$/|unique:currentSite.staffs,official_id'. ($id ? ',' . $id : '') . ',id,deleted_at,NULL',
            'default_account_id' => 'nullable|exists:currentSite.journal_accounts,id',
            'employee_picture_file' => 'nullable|mimes:jpeg,png,jpg,gif,svg|max:10240',
            'country_code' => 'nullable|exists:mysql.countries,code',
            'email_address' => 'email|unique:currentSite.staffs,email_address' . ($id ? ',' . $id : '') . ',id,deleted_at,NULL',
            'role_id' => 'required|gt:-1|exists:currentSite.roles,id'
        ];
        $ownerUser = $this->service->getOwnerUser();
        $isOwnerUpdate = false;

        if($ownerUser){
            $ownerId = is_array($ownerUser) ? $ownerUser['id'] : $ownerUser->id;
            $isOwnerUpdate = $ownerId == $id;
        }
        if(isset($request['role_id']) && ($isOwnerUpdate)){
            unset($validateRoles['name']);
            unset($validateRoles['country_code']);
            unset($validateRoles['email_address']);
            unset($validateRoles['role_id']);
            unset($validateRoles['code']);
            unset($validateRoles['can_access_system']);

        }
        if(!(isset($request['role_id']) && ($isOwnerUpdate))){
            if ($id == 'NULL') {
                $validateRoles['active'] = 'required|in:0,1';
            }
            if ($request['can_access_system']) {
                if ($request['type'] == "employee") {
                    unset($validateRoles['role_id']);
                }
                $validateRoles['email_address'] = 'required|' . $validateRoles['email_address'];
            } else {
                $request['can_access_system'] = false;
                unset($validateRoles['role_id']);
                unset($validateRoles['language_code']);
                $validateRoles['email_address'] = 'nullable|' . $validateRoles['email_address'];
            }
        }
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            if(!(isset($request['role_id']) && ($isOwnerUpdate))){
                $validateRoles['branch_id'] = ['required', new ActiveBranchRule()];
            }
            if ($request['can_access_system'] && request('type') === \Izam\Daftra\Staff\Util\StaffTypeUtil::USER) {
                $validateRoles += [
                    'branches' => 'required',
                    'branches.*' => [new ActiveBranchRule()]
                ];

            } else {
                unset($validateRoles['branches']);
                unset($validateRoles['branches.*']);
            }
        }
        if (Plugins::pluginActive(PluginUtil::BookingPlugin) || Plugins::pluginActive(PluginUtil::NEW_BOOKING_PLUGIN)) {
            $validateRoles += [
                'shift.*' => 'nullable|exists:currentSite.shifts,id'
            ];
        }
        /** @var LanguageRepository $languageRepo */
        $languageRepo = App::make('App\Repositories\LanguageRepository');
        $languageCriteria = new CustomFind([['field' => 'active', 'value' => 1]]);
        if ($request['can_access_system'] && $languageRepo->resetCriteria()->getByCriteria($languageCriteria)->count() > 1) {
            $validateRoles += [
                'language_code' => 'required|exists:mysql.languages,id',
            ];
        }
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $validateRoles += [
                'birth_date' => 'required|date_format:' . $dateFormats[$dateFormatIndex],
                'gender' => 'nullable|in:Male,Female',
                'personal_email' => 'nullable|email',
                'join_date' => 'required|date_format:' . $dateFormats[$dateFormatIndex],
                'direct_manager_id' => 'nullable|exists:currentSite.staffs,id,active,1,deleted_at,NULL|not_in:'.$id,
            ];
        }
        if (Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            $validateRoles += [
                'leave_policy_id' => 'nullable|exists:currentSite.leave_policies,id',
                'holiday_lists.*' => 'exists:currentSite.holiday_lists,id',
                'attendance_shift_id' => 'nullable|required_with:has_secondary_shift|exists:currentSite.shifts,id',
                'secondary_shift_id' => 'nullable|exists:currentSite.shifts,id|different:attendance_shift_id'
            ];
        }
        $parentValidations['rules'] += $validateRoles;

        $parentValidations['messages'] += [
            'attendance_shift_id.required_with' => __t('You must choose the Main Attendance Shift when you enable the Secondary Shift option'),
            'secondary_shift_id.different' => __t('You cannot choose the same Shift in the Main and Secondary Attendance Shift'),
            'direct_manager_id.exists' => __t('Direct Manager must have an active status.'),
            'direct_manager_id.not_in' => __t('The direct manager cannot be the employee themselves.'),
            "official_id.regex" =>  __t('Only letters, numbers, and dashes are allowed'),
            "official_id.unique" =>  __t('The field value must be unique, Another employee has the same ID'),

        ];
        return $parentValidations;
    }

    public function sendLoginDetails($id)
    {
        $url = '/owner/entity/staff/list';
        if (ShowLegacyListing::check('staff')) {
            $url = (ShowLegacyListing::getLegacyIndexURL('staff'));
        }
        try {
            $this->service->sendLoginData($id);
        } catch (\Throwable $th) {
            /** @todo this should be done from cake side */
            if (str_contains($th->getMessage(), 'Smtp Error')) {
                return redirect()->route('owner.staff.index')->with('danger', __t('Please recheck SMTP Settings.'));
            }
            return redirect($url)
                ->with('danger', __t($th->getMessage()));
        }
        return redirect($url)->with('success', __t('Credentials sent successfully'));
    }

    public function changePassword($id)
    {
        $url = getCakeURL([
            'controller' => 'staffs',
            'action' => 'change_staff_password/' . $id
        ]);
        return redirect($url);
    }

    public function activate($id, Request $request)
    {
        $activeRequest = new DefaultRequest();
        try {
            return $this->service->activate($id, $activeRequest);
        } catch (SystemLimitExceeded $exception) {
            return redirect()->route($this->routesPrefix . $this->folder . '.index')->with('limit_exceeded', $exception->getResult());
        } catch (AccountSuspendedException $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    public function deactivate($id, Request $request)
    {
        try {
            return $this->service->deactivate($id, $request);
        } catch (EntityNotFoundException $exception) {
            return redirect()->route($this->routesPrefix . $this->folder . '.index')->with('danger', $exception->getMessage());
        }catch (StaffIsInConfigurationApprovalLevel $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }

    }

    /**
     * {@inheritDoc}
     */
    public function destroy($id)
    {
        try {
            $this->handledExceptions = array_merge($this->handledExceptions, [
                StaffAssignedToContract::class,
                StaffAssignedToLoan::class,
                StaffIsDepartmentManager::class,
            ]);
            return parent::destroy($id);
        } catch (StaffIsDepartmentManager|StaffAssignedToContract|StaffAssignedToLoan $exception) {
            if (isApi()) {
                return response()->json(['message' => __t($exception->getMessage())], 400);
            } else if (isset($this->service->parameters['tab'])) {
                return redirect(back()->getTargetUrl() . '#' . $this->service->parameters['tab'])
                    ->with('danger', __t($exception->getMessage()));
            } else {
                return back()
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }


    public function refresh($staff_id, $expire_time = null): JsonResponse
    {
        $siteId = getCurrentSite('id');

        $token = sha1(time() . json_encode(['site_id' => $siteId]));

        // @todo create an queue event for better experience
        $this->tokenRepository->cleanUsedAndExpiredTokensBySiteId($siteId);

        $expirationMinutes = ($expire_time) ?? QR_TOKEN_EXPIRATION;

        $cc = 'staffs';
        $userId = $staff_id;
        $this->tokenRepository->add([
            'user_id' => $userId,
            'token' => $token,
            'cc' => $cc,
            'site_id' => $siteId,
            'expires_at' => Carbon::now()->addMinutes($expirationMinutes)
        ]);

        return new JsonResponse([
            'qr' => $token,
            'd' => $this->getSubdomain(),
        ]);
    }

    /* this function is almost duplicated in app-manager repo and could be extracted in common*/
    //used in qrcode controller
    private function getSubdomain()
    {
        $betaDomain = Domain_Short_Name;
        $mainDomain = Beta_Domain;

        if (getCurrentSite('beta_version')) {
            $betaDomain = Beta_Domain;
            $mainDomain = Domain_Short_Name;
        }

        $subDomain = str_replace($mainDomain, $betaDomain, getCurrentSite('subdomain'));

        return $subDomain;
    }

    public function ajaxHasSecondaryShift(Request $request)
    {
        $staffId = $request->query->get('staff_id');
        $results = $this->service->getHasSecondaryShift($staffId);
        return response()->json($results);
    }

      public function apiGetStaffPendingLeaveApplications(Request $request , $staffId)
      {
          /** @var LeaveApplicationService $leaveApplicationService */
          $leaveApplicationService = resolve(LeaveApplicationService::class);

          $data = $this->validateStaffIdForApiRequests($request , $staffId);
          $leaveApplications = $leaveApplicationService
              ->getStaffLatestPendingLeaveApplications($data['staff_id']);
          $link = route(
              "owner.entity.list" ,
                ["entityKey"=>EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY , 'filter[staff_id]'=>$data['staff_id'],'filter[status]'=>'pending']
          );
          return [
              'title'=>__t("Pending Leave Application"). " (" . count($leaveApplications) . ") " ,
              'viewUrl'=>$link,
              "viewTitle"=>__t("View all"),
              "items"=> $leaveApplications,
          ];
      }

    public function apiGetStaffLeaveBalance(Request $request, $staffId)
    {
        /** @var AttendanceReportsService $service */
        $service = resolve(AttendanceReportsService::class);
        $fromDate = $request->get('from_date');
        $toDate = $request->get('to_date');

        $data = $this->validateStaffIdForApiRequests($request , $staffId);
        $leaves = $service->getStaffAttendanceBalanceReportData($data['staff_id'] , $fromDate , $toDate);
        return [
            "title" => __t("Leave Balance"),
            "canUpdateCredit" => true,
            "items" => $leaves
        ];
    }

    public function apiGetShiftAndPolicies(Request $request, $staffId)
    {
        /** @var StaffService $service */
        $service = resolve(StaffService::class);
        $data = $this->validateStaffIdForApiRequests($request , $staffId);
        $shiftsAndPolicies = $service->getStaffShiftsAndPolicies($data['staff_id']);
        return [
            "title" => __t("Shifts & Policies"),
            "items" => $shiftsAndPolicies
        ];
    }


    public function apiGetLatestAttendanceSheets(Request $request, $staffId)
    {
        /** @var AttendanceSheetService $service */
        $service = resolve(AttendanceSheetService::class);
        $data = $this->validateStaffIdForApiRequests($request , $staffId);
        $sheets = $service->getStaffRelatedSheets($data['staff_id']);
        return [
            "title" => __t("Attendance Sheets"),
            "viewUrl" => route("owner.attendance_sheets.index",[ 'name[]'=>$staffId ]),
            "viewTitle" => __t("View all"),
            "items" => $sheets,
            'availableStatuses' => AttendanceSheetStatusTypesUtil::getStatuses()
        ];
    }

    public function apiGetLatestAttendanceLogs(Request $request, $staffId)
    {
        /** @var AttendanceLogService $service */
        $service = resolve(AttendanceLogService::class);
        $data = $this->validateStaffIdForApiRequests($request , $staffId);
        $logs = $service->getStaffRelatedLogs($data['staff_id']);
        return [
            "title" => __t("Latest Attendance Logs"),
            "viewUrl" => route("owner.attendance_logs.index",['staff_id'=>$staffId ]),
            "viewTitle" => __t("View all"),
            "items" => $logs,
            'availableStatuses' => AttendanceLogStatusUtil::getStatusList()
        ];
    }

    public function apiGetStaffMonthlyAttendance(Request $request, $staffId)
    {
        /** @var AttendanceDayService $service */
        $service = resolve(AttendanceDayService::class);
        $data = $this->validateStaffIdForApiRequests($request , $staffId);

        $carbonDateInstance = Carbon::parse(\request()->date);

        $fromDate = $carbonDateInstance->clone()->startOfMonth()->format('Y-m-d');
        $fromDateFormatted = format_date($fromDate);
        $toDate= $carbonDateInstance->clone()->endOfMonth()->format('Y-m-d');
        $toDateFormatted = format_date($toDate);

        $response = $service->getStaffMonthlyAttendanceDate($staffId , $fromDate);

        return [
            "title" => "",
            "totals" => $response['totals'] ?? [],
            "availableStatuses"=> AttendanceDayStatusTypesUtil::getStatuses(),
            "viewUrl" => route("owner.attendance_days.index",['name[]'=>$data['staff_id'] , 'from_date'=> $fromDateFormatted , 'to_date'=>$toDateFormatted]),
            "viewTitle" => __t("View all"),
            "items" => $response['items'] ?? []
        ];
    }


    public function apiGetCheckUnCalculatedLogs(Request $request, $staffId)
    {
        try {
            $data = $this->validateStaffIdForApiRequests($request , $staffId);
            return $this->service->checkStaffUnCalculatedLogs($data['staff_id']) ?? [];
        } catch (\Throwable $t) {
            return response()->json([
                'error' => $t->getMessage(),
            ], 400);
        }
    }

      public function apiGetStaffCheckList(Request $request , $staffId)
      {
          try {
              $data = $this->validateStaffIdForApiRequests($request , $staffId);
              return $this->service->getStaffMissingDataCheckList($data['staff_id']);
          }catch (\Throwable $t){
              return response()->json([
                  'error' => $t->getMessage(),
              ], 400);
          }
      }

      public function apiGetLatestAttendancePermissions(Request $request , $staffId)
      {
          try {
              if (empty($staffId)) return [];
              $data = $this->validateStaffIdForApiRequests($request , $staffId);
              return $this->attendancePermissionService->getStaffLatestAttendancePermissions($data['staff_id']) ?? [];
          } catch (\Throwable $t) {
              return response()->json([
                  'error' => $t->getMessage(),
              ], 400);
          }
      }

      public function apiGetLatestStaffContracts(Request $request , $staffId)
      {
          try {
              $data = $this->validateStaffIdForApiRequests($request , $staffId);
              return response()->json($this->contractService->getStaffLatestContracts($data['staff_id']));
          } catch (\Throwable $t) {
              return response()->json([
                  'error' => $t->getMessage(),
              ], 400);
          }
      }

      public function apiGetLatestStaffContractDetails(Request $request , $staffId)
      {
          try {
              $data = $this->validateStaffIdForApiRequests($request , $staffId);
              return response()->json($this->contractService->getStaffContractDetails($data['staff_id']));
          } catch (\Throwable $t) {
              return response()->json([
                  'error' => $t->getMessage(),
              ], 400);
          }
      }

      public function apiGetLatestStaffLoans(Request $request , $staffId)
      {
          try {
              $data = $this->validateStaffIdForApiRequests($request , $staffId);
              return response()->json($this->loanService->getStaffLatestLoans($data['staff_id']));
          } catch (\Throwable $t) {
              return response()->json([
                  'error' => $t->getMessage(),
              ], 400);
          }
      }

      public function apiGetLatestStaffPayslips(Request $request , $staffId)
      {
          try {
              $data = $this->validateStaffIdForApiRequests($request , $staffId);
              $currency = $request->get('currency');
              return response()->json($this->paySlipService->getStaffLatestPayslips($data['staff_id'] , $currency));
          } catch (\Throwable $t) {
              return response()->json([
                  'error' => $t->getMessage(),
              ], 400);
          }
      }

    private function validateStaffIdForApiRequests(Request $request, $staffId)
    {
        return Validator::make(
                ['staff_id' => $staffId],
                ['staff_id' => ['required', 'integer']
            ])
            ->validate();
    }

    public function apiGetStaffPayrollCheckList(Request $request, $staffId)
    {
        try {
            $data = $this->validateStaffIdForApiRequests($request , $staffId);
            /** @var StaffService $staffService */
            $staffService = resolve(StaffService::class);
            return response()->json($staffService->getPayrollChecklist($data['staff_id']));
        } catch (\Throwable $t) {
            return response()->json([
                'error' => $t->getMessage(),
            ], 400);
        }

    }

    public function apiUpdateDirectManager(Request $request): JsonResponse
    {
        try {
            if (!Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
                throw new \Exception(__t("Plugin not active"));
            }
            Validator::make($request->all(), [
                'staff_id' => 'required|array|min:1',
                'staff_id.*' => 'required|integer|exists:currentSite.staffs,id,deleted_at,NULL',
                'direct_manager_id' => 'nullable|integer|exists:currentSite.staffs,id,active,1,deleted_at,NULL',
            ])->validate();
            $manager = $this->service->find($request->get('direct_manager_id'));
            foreach ($request->get('staff_id') as $staffId) {
                $this->service->updateDirectManager($staffId, $request, $manager);
            }
            return response()->json([
                'message' => 'Direct manager updated successfully',
                'data' => [],
            ], Response::HTTP_OK);
        } catch (ValidationException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            return response()->json([
                'errors' => $errors,
                'message' => __t($exceptionMessage)],
                Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStaffsNameAndPhoto(Request $request, $getFromAccessibleBranches = false){
        $staffData = $this->service->getStaffNameAndPhoto($request['q'], $getFromAccessibleBranches);
        return response()->json($staffData, 200);
    }

}
