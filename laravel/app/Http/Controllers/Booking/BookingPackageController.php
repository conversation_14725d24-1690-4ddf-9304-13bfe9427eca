<?php

namespace App\Http\Controllers\Booking;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Izam\Booking\Services\BookingPackageService;

class BookingPackageController extends Controller
{
    public function __construct(
        protected BookingPackageService $bookingPackageService
    ) {
    }

    public function apiList(Request $request)
    {
        $result = $this->bookingPackageService->list($request->all());
        return response()->json($result);
    }

}