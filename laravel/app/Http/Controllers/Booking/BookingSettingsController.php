<?php

namespace App\Http\Controllers\Booking;

use App\Http\Controllers\Controller;
use App\IzamViews\IzamView;
use App\Services\SettingService;
use App\Services\Traits\DefaultIzamViewData;
use Illuminate\Http\Request;
use App\Utils\PermissionUtil;
use App\Facades\Permissions;
use Izam\Booking\Utils\BookingUtil;

class BookingSettingsController extends Controller
{
    use DefaultIzamViewData;
    public function __construct(protected SettingService $settingService)
    {
        $this->setDefaultViewData();
    }

    public function index()
    {
        return new IzamView('new-booking/settings');
    }

    public function getSettings()
    {
        if(!Permissions::checkPermission(PermissionUtil::MANAGE_BOOKING_SETTINGS)) {
            return response()->json([
                'status' => false,
                'message' => __t('You are not allowed to update the settings')
            ], 403);
        }
        return response()->json($this->settingService->getBookingGeneralSttings(), 200);
    }

    public function updateSettings(Request $request)
    {
        if(!Permissions::checkPermission(PermissionUtil::MANAGE_BOOKING_SETTINGS)) {
            return response()->json([
                'status' => false,
                'message' => __t('You are not allowed to update the settings')
            ], 403);
        }

        if(!isset($request['booking_service_setting']) || empty($request['booking_service_setting'])) {
            return response()->json([
                'status' => false,
                'message' => __t('Booking service setting is required')
            ], 400);
        }elseif(!in_array($request['booking_service_setting'], [BookingUtil::BOOK_DEFAULT_SERVICE, BookingUtil::BOOK_ONE_SERVICE, BookingUtil::BOOK_MULTIPLE_SERVICES])) {
            return response()->json([
                'status' => false,
                'message' => __t('Invalid booking service setting')
            ], 400);
        }

        if(!isset($request['service_employee_setting']) || empty($request['service_employee_setting'])) {
            return response()->json([
                'status' => false,
                'message' => __t('Service employee setting is required')
            ], 400);
        }elseif(!in_array($request['service_employee_setting'], [BookingUtil::NO_EMPLOYEE_REQUIRED, BookingUtil::SELECT_ONE_EMPLOYEE, BookingUtil::SELECT_MULTIPLE_EMPLOYEES])) {
            return response()->json([
                'status' => false,
                'message' => __t('Invalid service employee setting')
            ], 400);
        }

        if((!isset($request['default_booking_service']) || empty($request['default_booking_service'])) && $request['booking_service_setting'] == BookingUtil::BOOK_DEFAULT_SERVICE) {
            return response()->json([
                'status' => false,
                'message' => __t('Default booking service is required')
            ], 400);
        }

        $this->settingService->saveBookingGeneralSttings($request);
        return response()->json([
            'status' => true,
            'message' => __t('Settings Updated Successfully')
        ], 200);
    }
}
