<?php

namespace App\Http\Controllers;

use App\Exceptions\EntityNotFoundException;
use App\Models\Machine;
use App\Services\MachineMappingService;
use App\Services\MachineService;
use App\Services\MachineTypeService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Izam\ZktAttendanceMachine\Factories\ZktMachineEndpointHandlerFactory;
use Izam\ZktAttendanceMachine\Utils\ZktMachineOperationTypeUtil;
use Rollbar\Rollbar;

/**
 * MachineController Class machine controller
 * @package App\Http\Controllers
 * <AUTHOR> <<EMAIL>>
 */
class MachineController extends BaseController
{
    /**
     * @var string folder
     */
    public $folder = 'machines';

    /**
     * @var MachineService machine service
     */
    protected $service;

    /**
     * @var string label
     */
    protected $label = 'Machines';

    /**
     * {@inheritDoc}
     * @param MachineTypeService $machineTypeService
     */
    function __construct(MachineService $service)
    {
        parent::__construct($service);
    }

    public function show($id)
    {
        $machineMappingService = resolve(MachineMappingService::class);

        if($unmappedMessage = $machineMappingService->getUnmappedUsersMessage($id)){
            session()->flash('warning', $unmappedMessage);
        }

        return parent::show($id)
                    ->with('haveUnmappedUsers', $unmappedMessage?true:false);
    }

    /**
     * {@inheritDoc}
     */
    public function create()
    {
        try {
            return parent::create();
        } catch (EntityNotFoundException $e) {
            return redirect()
                ->route($this->routesPrefix . $this->folder . '.index')
                ->with('danger', __t('No Machine Type Selected'));
        }
    }

    /**
     * {@inheritDoc}
     */
    public function destroy($id)
    {
        $this->label = "Machine";
        return parent::destroy($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getFormValidationRules(Request $request, $id=null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $rules = [
//            'host' => 'required|regex:/^\S*$/u',
            'port' => 'nullable|integer|max:65535|min:0',
            'status' => 'required',
        ];
        if($id) {
            $rules['name'] = "required|unique:currentSite.machines,name,$id,id,deleted_at,NULL";
        } else {
            $rules['name'] = 'required|unique:currentSite.machines,name,NULL,id,deleted_at,NULL';
        }
        $messages = [
            'name.unique' => sprintf(__t('This %s already exist.'), __t('name')),
            'host.regex' => __t('You should enter host name without spaces')
        ];
        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    public function zktMachineEndpoint(Request $request, $uuid) {
        $request->validate([
            'table' => ['required', Rule::in(ZktMachineOperationTypeUtil::getValidOperationTypesArray())]
        ]);

        $machine = $this->service->findByUuid($uuid);
        if (empty($machine)) {
            throw new EntityNotFoundException(__t('Machine'));
        }

        try {
            $tableParam = $request->get('table');
            $inputData = file_get_contents('php://input');

            $zktMachineEndpointHandler = ZktMachineEndpointHandlerFactory::create($tableParam, $inputData, $machine->id);
            $zktMachineEndpointHandler->handle();
            return response('Success', 200);
        } catch (\Throwable $th) {
            report($th);
            return response('Failed', 400);
        }
    }

    public function testConnection($machine)
    {
        try{

            $result = $this->service->testConnection($machine);
            if($result)
            {
                return response()->json(['status' => 'success', 'message' => __t('Connected Successfully')], 200);
            }else{
                return response()->json(['status' => 'fail', 'message' => __t('Connection Failed !')], 200);

            }
        }catch (ModelNotFoundException $e)
        {
            return response()->json(['status' => 'fail', 'message' => sprintf(__t('%s Not Found'), __t('Machine'))], 200);

        }catch (\Exception $e)
        {
            return response()->json(['status' => 'fail', 'message' => __t('Connection Failed !')], 200);
        }
    }

    public function index(Request $request)
    {
        return parent::index($request); // TODO: Change the autogenerated stub
    }

    public function pullData($machine){
        try{
            set_time_limit(600);
            $result = $this->service->pullData($machine);

                return redirect()->route('owner.attendance_sessions.show', [
                    'attendance_session' => $result->getSessionId(),
                    'signs_count' => $result->getSignsCount(),
                    'invalid_staff_ids' => $result->getInvalidStaffIds()
                    ]
                );
        }catch (ModelNotFoundException $e)
        {
            return redirect()->back()->with('danger', sprintf(__t('%s Not Found'), __t('Machine')));

        }catch (QueryException $exception) {

            Log::error($exception->getMessage(), ['Exception' => $exception]);
            return redirect()->back()->with('danger', __t("Could'nt Pull Data From Machine."));

        }
        catch (\Exception $e)
        {
//            Log::error($e->getMessage(), ['Exception' => $e]);
            return redirect()->back()->with('danger', $e->getMessage());
        }
    }


    /**
     * activate machine
     * @param int     $id      machine id
     * @param Request $request request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function activate(int $id, Request $request)
    {
        $request->request->add(['status' => 1]);
        $this->service->update($id, $request);
        return redirect()
            ->route('owner.machines.show', ['machine' => $id])
            ->with('success', __t('Activated'));
    }

    /**
     * deactivate machine
     * @param int     $id      machine id
     * @param Request $request request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deactivate(int $id, Request $request)
    {
        $request->request->add(['status' => 0]);
        $this->service->update($id, $request);
        return redirect()
            ->route('owner.machines.show', ['machine' => $id])
            ->with('success', __t('Deactivated'));
    }
}
