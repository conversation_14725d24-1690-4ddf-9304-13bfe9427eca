<?php

namespace App\Http\Controllers;

use app\Services\BaseService;
use App\Services\CategoryService;
use Illuminate\Http\Request;
use Izam\Daftra\Common\Utils\PermissionUtil;

class CategoryController extends BaseController
{
    public $folder = 'categories';
    protected $service;

    function __construct(CategoryService $service)
    {
        parent::__construct($service);
    }

    public function ajaxProductCategoryFilter(Request $request)
	{
        return response()->json( 
            $this->service->searchProductCategories( 
            $request->query->get('q')
            ) ?? [] );
	}

    public function apiGetCategoriesWithProductsCount(Request $reques)
    {
        if(!check_permission(PermissionUtil::View_All_Products) && !check_permission(PermissionUtil::View_his_own_Products) && !str_contains($this->referer(), '/pos/')){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to access this page')
            ], 403);
        }
        return response()->json($this->service->getCategoriesWithProductsCount());
    }


}
