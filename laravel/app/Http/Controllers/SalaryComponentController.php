<?php

namespace App\Http\Controllers;

use App\Exceptions\ComponentUsedInMudadSettingsException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Payroll\NoSalaryComponentSelectedException;
use App\Facades\Plugins;
use App\Rules\ConditionFormat;
use App\Rules\FormulaFormat;
use App\Rules\IsValidPlaceholder;
use App\Services\PayrollComponent\PayrollComponentTemplateService;
use App\Services\SalaryComponentService;
use App\Utils\PluginUtil;
use App\Utils\SalaryComponentTypeUtil;
use App\Utils\SalaryComponentValueTypeUtil;
use Illuminate\Http\Request;
use App\Exceptions\UnAuthorizedException;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Facades\CakeSession;
use App\IzamViews\IzamView;
use App\Modules\LocalEntity\Formatter\EntityFormFormatter;
use Izam\Daftra\Common\EntityStructure\Entity;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\SalaryComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\UnitComponent;
use Izam\Entity\Repository\Proxy\SchemaRepository;
use Izam\Entity\Service\EntityToForm;
use Izam\Entity\Service\SpecBuilder;
use Laminas\Form\Form;
class SalaryComponentController extends BaseController
{
    public $folder = 'salary_components';
    protected $service;
    protected $label = 'Salary Component';

    private $payrollComponentTemplateService;

    function __construct(SalaryComponentService $service, PayrollComponentTemplateService $payrollComponentTemplateService)
    {
        parent::__construct($service);
        $this->payrollComponentTemplateService = $payrollComponentTemplateService;
    }


    public function getFormValidationRules(Request $request, $id = null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $messages = [
            'min' => __t("This Field Should Be Greater Than or equal to Zero"),
        ];
        $value_type = isset($request['value_type'])? $request['value_type']:null;

        if ($id) {
            $rules = [
                'name' => "required|unique:currentSite.salary_components,name,$id,id,deleted_at,NULL",
            ];
        } else {
            $rules = [
                'name' => 'required|unique:currentSite.salary_components,name,NULL,id,deleted_at,NULL',
                'type' => 'required|in:' . implode(',', SalaryComponentTypeUtil::getSalaryComponentsTypes()),
                'status' => 'required'
            ];
        }

        $etc_rules = [
            'comp-description' => 'nullable',
            'condition' => ['nullable', new IsValidPlaceholder('payslip'), new ConditionFormat],
            'value_type' => 'required',
            'ref_value' => 'nullable'
        ];

        /**
         * If the source is payroll component then exclude value type
         */
        if(isset($request['payroll_component_id'])) {
            unset($etc_rules['value_type']);
            $etc_rules['payroll_component_id'] = 'required|exists:payroll_components,id';
        }

        $rules = array_merge($rules, $etc_rules);

        if (Plugins::pluginActive(PluginUtil::AccountingPlugin) && !isset($request['ref_value'])){
            $rules = array_merge($rules, ['default_account_id' => 'nullable|exists:currentSite.journal_accounts,id']);
        } elseif (Plugins::pluginActive(PluginUtil::AccountingPlugin) && isset($request['ref_value'])){
            $rules = array_merge($rules, ['default_account_id' => 'nullable|exists:currentSite.journal_accounts,id']);
        }

        if (!is_null($value_type) && $value_type == 'amount' && $value_type != 'commission'){
            $rules = array_merge($rules,['amount' => 'nullable|numeric|min:0']);
        }
        else if (!is_null($value_type) && $value_type == 'formula' && $value_type != 'commission'){
            $rules =array_merge($rules, ['formula' => ['nullable', new IsValidPlaceholder('payslip'), new FormulaFormat]]);
        }
        if ($value_type == 'commission')
            $request['value_type'] = SalaryComponentValueTypeUtil::AMOUNT_VALUE_TYPE;

        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    public function activate($id)
    {
        try {
            $this->service->activate($id);
            return redirect()->back()->with('success', __t('Salary Component Activated'));
        } catch (EntityNotFoundException $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }

    }

    public function deactivate($id)
    {
        try {
            $this->service->deactivate($id);
            return redirect()->back()->with('success', __t('Salary Component Deactivated'));
        } catch (ComponentUsedInMudadSettingsException $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        } catch (EntityNotFoundException $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    public function getCreateforModal()
    {
        $formData = $this->service->getFormData();
        $earnings = $this->service->getEarningComponentList();
        $deductions = $this->service->getDeductionComponentList();
        return view('salary_components.owner.test', $formData)->with('earnings', $earnings)->with('deductions', $deductions);
    }

    private function setFormData($forms): void
    {
        if (empty(session()->get('errors'))) {
            return;
        }
        $messages = session()->get('errors')->getBag('default')->getMessages();
        foreach ($forms as $key => $form) {
            if ($form instanceof Form) {
                $form->setMessages(array_undot($messages));
                $form->setData(request()->old());
            }
        }
    }

    public function create()
    {
        try {
            //Check if the salary component has payroll component
            $template_id = request('template_id');
            $formData = $this->service->getFormData();
            $oldData = old();
            $customForm = false;
            $is_payroll_component = false;
            $extraScript = '';
            if($oldData && old('payroll_component_id')) {
                $template_id = old('payroll_component_id');
            }

            $structureGetter = getEntityBuilder();
            $structure = $structureGetter->buildEntity('salary_component');

            $form = $this->getForm($structure);

            $forms = [$form];
            $payroll_component_id = false;
            $template = [];
            if($template_id) {
                $payrollComponentData = $this->payrollComponentTemplateService->generatePayrollComponentData($template_id);
                $template['type'] = $payrollComponentData['type'];
                $formData['fields'] = $payrollComponentData['form'];
                $is_payroll_component = true;
                $formData['payroll_component_id'] = $template_id;
                $payroll_component_id = $template_id;
                $formData['fields_meta']          = $payrollComponentData['fieldsMeta'];
                $formData['extra_script']         = $payrollComponentData['extra_script'];
                $customForm = $payrollComponentData['form'];
                $extraScript = $payrollComponentData['extra_script'];
                $forms[]  = [$customForm];
                $form->getElements()['type']->setAttribute('readonly','readonly');
                $form->getElements()['status']->setValue(1);
                $form->setData($template);

            }

            $this->setFormData($forms);



        } catch (\Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));

        }

        $pageHead = SalaryComponent::generateCreatePageHeaderButtons();
        if($template_id){

            return new IzamView('salary_component/create',compact('pageHead','form','customForm','extraScript', 'is_payroll_component','payroll_component_id'));
        }

        $blade = $this->blade ?? 'create';
        return view($this->folder . $this->viewsPrefix . '.' . $blade, $formData);
    }


    private function getForm(Entity $structure, $id = null)
    {
        $formBuilder = new EntityToForm(new SpecBuilder());

        $form = $formBuilder
            ->build('salary_component', resolve(SchemaRepository::class))
            ->setLabel(sprintf(__t('%s Information'), __t($structure->getLabel())));

        // if ($id) {
        //     $data = $this->formatRecord(self::ENTITY_KEY, $id, new EntityFormFormatter());
        //     $form->setData($data);
        // }
        return $form;
    }


    public function edit($id) {
        try{
            $formData = $this->service->getFormData($id);


              $customForm = false;
              $is_payroll_component = false;
              $extraScript = '';


              $structureGetter = getEntityBuilder();
              $structure = $structureGetter->buildEntity('salary_component');

              $form = $this->getForm($structure);
              $formDataUpdated = $formData['form_record']->toArray();
              $formDataUpdated['comp-name'] = $formDataUpdated['name'];
              $form->setData($formDataUpdated);


              $forms = [$form];
              $payroll_component_id = false;
              $template = [];
              if($formData['form_record'] && $formData['form_record']->payroll_component_id) {

                $payroll_component_id = $formData['form_record']->payroll_component_id;
                $payrollComponentData = $this->payrollComponentTemplateService->generatePayrollComponentData($payroll_component_id, $formData['form_record']);

                $template['comp-name']     = $formData['form_record']['name'];
                $formData['template']['type']     = $payrollComponentData['type'];
                $formData['fields']               = $payrollComponentData['form'];
                $formData['fields_meta']          = $payrollComponentData['fieldsMeta'];
                $formData['is_payroll_component'] = true;
                $formData['payroll_component_id'] = $payroll_component_id;
                $formData['extra_script']         = $payrollComponentData['extra_script'];
                $template['type'] = $payrollComponentData['type'];
                $formData['fields'] = $payrollComponentData['form'];
                $is_payroll_component = true;
                $payroll_component_id = $formData['form_record']->payroll_component_id;

                  $customForm = $payrollComponentData['form'];

                  $extraScript = $payrollComponentData['extra_script'];
                  $forms[]  = [$customForm];

                  $form->getElements()['type']->setAttribute('readonly','readonly');
                  $form->getElements()['status']->setValue(1);

                  $form->setData(array_merge($formData['form_record']->toArray() , $template));


              }

              $this->setFormData($forms);



        }catch (NotAccessibleStaffBranchException | UnAuthorizedException $exception) { //redirect to home page if has no permission
            CakeSession::flashMessage(__t($exception->getMessage()), 'Errormessage', 'secondaryMessage');
            return redirect(CAKE_BASE_URL);
        }catch (\Exception $exception){
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));

        }
        if (empty($formData['form_record'])) {
            return redirect()->route($this->getRouteName('index'), $this->queryParameters)
                ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t(strtolower($this->label))));
        }
        $isEdit = true;
        $componentId = $formData['form_record']['id'];

        if($formData['form_record']['is_basic'] == 1 || !$formData['form_record']->payroll_component_id) {
            $blade = $this->blade ?? 'create';
            return view($this->folder . $this->viewsPrefix . '.' . $blade, $formData);
        }

        $pageHead = SalaryComponent::generateCreatePageHeaderButtons();

        return new IzamView('salary_component/create',compact('pageHead','form','customForm','extraScript', 'is_payroll_component','payroll_component_id','isEdit','componentId','formData'));


    }

     /**
     * @param Request $request
     * @return view
     */
    public function destroyMulti(Request $request)
    {
        try{
            $redirectRoute = $this->routesPrefix . $this->folder . '.index';
            if($request['ids'] == 'none'){
                return redirect()->route($this->routesPrefix . $this->folder . '.index')
                    ->with('danger', sprintf(__t('No %s selected'), __t('Salary Component')));
            } elseif ($request['ids'] == 'all'){
                $allData = $this->service->getFilteredRecords()->toArray();
                $ids = array_keys($allData);
                $result = $this->service->deleteMany($ids);
            } else {
                $ids = explode(',', $request['ids']);
                $result = $this->service->deleteMany($ids);
            }

            if ($result['deletedCount'] > 0 && $result['unDeletedCount'] > 0){
                return redirect()->route($redirectRoute, $this->queryParameters)
                    ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Salary Component')))
                    ->with('invalidBulkDeleteData',   $result['invalidDeleteData']);
            } elseif ($result['deletedCount'] > 0 && $result['unDeletedCount'] == 0){
                return redirect()->route($redirectRoute, $this->queryParameters)
                    ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Salary Component')));
            } elseif ($result['deletedCount'] == 0 && $result['unDeletedCount'] > 0){
                return redirect()->route($redirectRoute, $this->queryParameters)
                    ->with('invalidBulkDeleteData',   $result['invalidDeleteData']);
            }
        }catch (NoSalaryComponentSelectedException $exception){
            return redirect()->route($redirectRoute, $this->queryParameters)
                ->with('danger',$exception->getMessage());
        }catch (EntityNotFoundException $exception){
            return redirect()->route($redirectRoute, $this->queryParameters)
                ->with('danger',$exception->getMessage());


        }
    }


}
