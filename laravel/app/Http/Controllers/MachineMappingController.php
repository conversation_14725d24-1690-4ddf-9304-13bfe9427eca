<?php

namespace App\Http\Controllers;

use App\Exceptions\EntityNotFoundException;
use App\Models\Machine;
use App\Requests\DefaultRequest;
use App\Services\MachineMappingService;
use App\Services\MachineService;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;

/**
 * MachineController Class machine controller
 * @package App\Http\Controllers
 * <AUTHOR> <<EMAIL>>
 */
class MachineMappingController extends BaseController
{
    /**
     * @var string folder
     */
    public $folder = 'machine_mappings';

    /**
     * @var MachineMappingService machine service
     */
    protected $service;

    /**
     * @var MachineService machine service
     */
    protected $machineService;

    /**
     * @var Machine machine
     */
    protected $machine;

    /**
     * @var string label
     */
    protected $label = 'Machine Employee Mapping';

    private $machine_id;
    private $record_id;

    /**
     * {@inheritDoc}
     * @param MachineMappingService $machineTypeService
     */
    function __construct(MachineMappingService $service, MachineService $machineService)
    {
        $this->machineService = $machineService;
        parent::__construct($service);
        if (!app()->runningInConsole()) {
            $this->machine_id = Route::current()->parameter('machine');
            $this->record_id = Route::current()->parameter('mapping');
            $this->service->machine_id = $this->machine_id;
            $machine = $this->machineService->find($this->machine_id);
            if (is_null($machine)) {
                redirect()->route( 'owner.machines.index')
                    ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t('Machine')))->send();
            }
        }
    }


    /**
     * {@inheritDoc}
     */
    public function getFormValidationRules(Request $request, $id=null)
    {
        $parentValidations = parent::getFormValidationRules($request, $this->record_id);
        $messages = [
            'system_emp_id.*.unique' => __t('This Employee is already mapped'),
            'system_emp_id.unique' => __t('This Employee is already mapped'),
            'machine_emp_id.*.unique' => __t('This ID is already used'),
            'machine_emp_id.unique' => __t('This ID is already used'),
            'system_emp_id.required' => __t('Machine must contain at least 1 Mapping'),
            'machine_emp_id.required' => __t('Machine must contain at least 1 Mapping'),
            'machine_emp_id.*.min' => __t('Value must not be less than 1'),
            'machine_emp_id.*.integer' => __t('Value must be numeric'),
            'system_emp_id.*.distinct' => __t('Cannot duplicate Employee'),
            'machine_emp_id.*.distinct' => __t('Cannot duplicate Machine Employee ID')
        ];

        if($this->record_id) // Edit Validation Rules
        {
            $rules['system_emp_id'] = "required|unique:currentSite.machine_employee_mappings,staff_id,$id,id,machine_id,{$this->machine_id}";
            $rules['machine_emp_id'] = "required|min:1|integer|unique:currentSite.machine_employee_mappings,staff_machine_id,$id,id,machine_id,{$this->machine_id}";
        } else { // Add Validation Rules
            $rules['system_emp_id'] = "required";
            $rules['machine_emp_id'] = "required";
            $rules['system_emp_id.*'] = "required|distinct|unique:currentSite.machine_employee_mappings,staff_id,NULL,staff_id,machine_id,{$this->machine_id}";
            $rules['machine_emp_id.*'] = "required|distinct|min:1|integer|unique:currentSite.machine_employee_mappings,staff_machine_id,NULL,staff_machine_id,machine_id,{$this->machine_id}";
        }

        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    public function index(Request $request)
    {
        if($unmappedMessage = $this->service->getUnmappedUsersMessage($this->machine_id)){
            session()->flash('warning', $unmappedMessage);
        }

        return parent::index($request)
                ->with('machine_id', $this->machine_id);
    }

    public function create()
    {
        return parent::create()
            ->with('machine_id', $this->machine_id)
            ->with('isAdd',true);
    }

    public function edit($id)
    {
        return parent::edit($this->record_id)
            ->with('machine_id', $this->machine_id)
            ->with('isAdd',false);
    }

    public function store(Request $request)
    {
        $request['machine_id'] = $this->machine_id;
        try{
            $validatedData = $this->validateFormData($request);
            $storeRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());
            $result = $this->service->add($storeRequest);
            if (isApi()) {
                return response()->json(['data' => $this->service->find($result->id)], 200);
            } else {
                return redirect()
                    ->route('owner.machines.mappings.index', array_merge(['machine' => $this->machine_id],$this->queryParameters))
                    ->with('success', sprintf(__t('%s Added Successfully'), __t($this->label)));
            }
        } catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route('owner.machines.mappings.create', ['machine' => $this->machine_id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('%s Adding Failed'), __t($this->label)));
            }
        } catch (\Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route('owner.machines.mappings.create', ['machine' => $this->machine_id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }

    public function update(Request $request, $id)
    {
        $request['machine_id'] = $this->machine_id;
        try {
            $validatedData = $this->validateFormData($request, $this->record_id);
            $updateRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());
            $result = $this->service->update($this->record_id, $updateRequest);
            if (isApi()) {
                return response()->json(['data' => $result], 200);
            } else {
                return redirect()
                    ->route('owner.machines.mappings.index', array_merge(['machine' => $this->machine_id],$this->queryParameters))
                    ->with('success', sprintf(__t('%s Saved Successfully'), __t($this->label)));
            }
        } catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()
                    ->route('owner.machines.mappings.edit', ['machine' => $this->machine_id, 'mapping' => $this->record_id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('%s Updating Failed'), __t($this->label)));
            }
        } catch (\Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if(isApi())
            {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            }else{
                return redirect()
                    ->route('owner.machines.mappings.edit', ['machine' => $this->machine_id, 'mapping' => $this->record_id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }

    public function destroy($id)
    {
        try{
            $this->service->delete($this->record_id);
            if(isApi())
            {
                return response()->json([], 200);
            }else{
                return redirect()
                    ->route('owner.machines.mappings.index', array_merge(['machine' => $this->machine_id],$this->queryParameters))
                    ->with('success', sprintf(__t('%s Deleted'), __t($this->label)));
            }
        }
        catch (QueryException $exception)
        {
            if(isApi())
            {
                return response()->json(['message' => sprintf(__t('%s Deleting Failed'), __t($this->label))], 400);
            }else{
                return redirect()
                    ->route('owner.machines.mappings.index', array_merge(['machine' => $this->machine_id],$this->queryParameters))
                    ->with('danger', sprintf(__t('%s Deleting Failed'), __t($this->label)));
            }
        }
        catch (\Exception $exception)
        {
            if(isApi())
            {
                return response()->json(['message' => __t($exception->getMessage())], 400);
            }else{
                return redirect()
                    ->route('owner.machines.mappings.index', array_merge(['machine' => $this->machine_id],$this->queryParameters))
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }

}
