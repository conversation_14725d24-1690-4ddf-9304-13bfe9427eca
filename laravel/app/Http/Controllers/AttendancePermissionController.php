<?php

namespace App\Http\Controllers;

use App\Exceptions\AttendancePermissions\CanNotAddAttendancePermissionInApprovedAttendanceSheet;
use App\Exceptions\AttendancePermissions\CanNotEditOrDeleteAttendancePermissionInApprovedAttendanceSheet;
use App\Facades\Settings;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\Events\RecoredUndoApproveEvent;
use App\Rules\FormattedDateOperation;
use App\Services\AttendancePermissionService;
use App\Utils\AttendancePermissionTypesUtil;
use App\Utils\MultipleShiftTypeUtil;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AttendancePermissionController extends BaseController
{
    public $folder = 'attendance_permissions';
    /**
     * @var AttendancePermissionService
     */
    protected $service;
    protected $label = 'Attendance Permission';

    function __construct(AttendancePermissionService $service)
    {
        $this->middleware("checkFreeTrail")->only(["create","store"]);
        parent::__construct($service);
    }
    public function edit($id)
    {
        try{
            $this->service->throwUsedAttendancePermissionCannotBeUpdatedOrDeleted($id);
            return parent::edit($id); // TODO: Change the autogenerated stub

        } catch (CanNotEditOrDeleteAttendancePermissionInApprovedAttendanceSheet $exception) {

            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect(back()->getTargetUrl())
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exception->getMessage()));
            }
        } catch (\App\Exceptions\NotAccessibleStaffBranchException $exception){
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                ->with('danger', $exception->getMessage());
        }
    }

    public function getFormValidationRules(Request $request, $id=null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        $siteDateFormat = $dateFormats[$dateFormatIndex];
        $messages = $parentValidations['messages'];
        $messages += [
            'from_date.required' => ''
        ];
        $formDateRules = ['required',  'date_format:'.$siteDateFormat];
        $toDateRules = ['nullable', 'date_format:'.$siteDateFormat,  new FormattedDateOperation('after_or_equal', $request->get('from_date'), __t('Permission end date must be after permission start date'))];
        $rules = [
            'staff_id' => 'required',
            'type' => 'required',
            'from_date' => $formDateRules,
            'to_date' => $toDateRules,
        ];
        if ($request->get('application_date')) {
            $rules['application_date'] = $formDateRules;
//            $rules['application_date'] [] = new FormattedDateOperation('before', $request->get('from_date'), __t('Permission start date must be after application date'));
        }

        try {
            $date = Carbon::createFromFormat($dateFormats[$dateFormatIndex], $request['from_date']);
        }catch (\Exception $e) {
            throw new \Exception($messages['date_format']);
        }
        if ($request['type'] == AttendancePermissionTypesUtil::DELAY) {
            $rules['late_time'] = 'required|numeric|min:1';
            $messages['late_time.min'] = __t('time must be more than 1 minute');
        } else if($request['type'] == AttendancePermissionTypesUtil::EARLY) {
            $rules['early_time'] = 'required|numeric|min:1';
            $messages['early_time.min'] = __t('time must be more than 1 minute');
        }

        if (Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift'))
            $rules['shift_type'] = 'required|in:' . implode(',', array_keys(MultipleShiftTypeUtil::getTypes()));

        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages];
    }

    public function store(Request $request)
    {
        $this->handledExceptions [] = CanNotAddAttendancePermissionInApprovedAttendanceSheet::class;
        try {
            $result = parent::store($request);
            if (!str_contains($result->getTargetURl(), 'attendance_permissions/create') && (bool)$request['saveAndAdd'] ?? false) {
                return redirect()->route($this->getRouteName('create'), $this->routeParam())
                    ->with('success', sprintf(__t('%s Added Successfully'), __t($this->label)));
            }
            return $result;

        } catch (CanNotAddAttendancePermissionInApprovedAttendanceSheet $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exception->getMessage()));
            }
        }
    }

    public function destroy($id){
        $leaveApplicationData = $this->service->setLeaveApplicationStatusToPending($id);
        if($leaveApplicationData){
            $leaveApplicationData['undo_from_final_approve'] = true;
            event(new RecoredUndoApproveEvent(new RecoredInfo('leave_application',  $leaveApplicationData)));
        }

        return parent::destroy($id);
    }

}
