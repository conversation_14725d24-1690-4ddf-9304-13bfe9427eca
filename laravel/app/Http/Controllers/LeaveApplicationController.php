<?php

namespace App\Http\Controllers;

use App\Modules\LocalEntity\Events\RecoredUndoApproveEvent;
use App\Modules\LocalEntity\Queue\Events\LeaveApplication\LeaveApplicationApproved;
use App\Modules\LocalEntity\Queue\Events\LeaveApplication\LeaveApplicationRejected;
use App\Utils\LeaveApplicationStatusUtil;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\RedirectResponse;
use App\Services\LeaveApplicationService;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\Events\RecoredApprovedEvent;
use App\Modules\LocalEntity\Events\RecoredRejectedEvent;
use App\Modules\LocalEntity\Events\RecoredUndoRejectEvent;
use App\Services\AttendanceReportsService;
use App\Services\LeavePolicyService;
use App\Services\StaffService;
use Illuminate\Http\JsonResponse;
use Izam\Daftra\Common\Exception\EntityRecordNotFound;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use App\Repositories\AttendancePermissionRepository;
use App\Exceptions\AttendancePermissions\CanNotAddAttendancePermissionInApprovedAttendanceSheet;
use App\Models\LeaveApplication;
use App\Services\LeaveTypeService;

class LeaveApplicationController extends Controller
{
    private $service;
    private $permissionService;
    private $entityKey;
    private $appEntityShowAction;
    protected $staffService;
    protected $leaveTypeService;
    public function __construct(LeaveApplicationService $service, StaffService $staffService, LeaveTypeService $leaveTypeService)
    {
        $this->service = $service;
        $this->entityKey = 'leave_application';
        $this->appEntityShowAction = resolve(AppEntityShowAction::class);
        $this->staffService = $staffService;
        $this->leaveTypeService = $leaveTypeService;
    }

    public function apiGetFullData(Request $request): JsonResponse
    {
        try{
            $leaveApplicationId = $request->get('id');
            $data = $this->appEntityShowAction->handle(EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY, $leaveApplicationId, 4, []);
             // check branch constraints & check view permission
            if (!($this->staffService->isAccessableStaffId($data->staff_id,true) && $this->service->isLeaveApplicationViewAllowed($data))) {
                return response()->json([
                    'error' =>  __t('You don’t have the permission to view this leave application'),
                ], 403);
            }

            // format response
            $responseData =  $this->service->formatApplicationForMobileApi($data);
            return new JsonResponse(
                $responseData, 200);
        } catch (EntityRecordNotFound $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 404);
        }catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 400);
        }
    }

    public function apiGetEmployeeLeaveBalance(Request $request): JsonResponse
    {
        try{
            $user = $request->user();
            $employeeId = $request->get('employee_id');
            $reportsService = resolve(AttendanceReportsService::class);
            $leaveData = $reportsService->getLeaveBalanceForStaffIdByType($employeeId? $employeeId : $user->getId(), true);
            $responseData = [];
            foreach($leaveData['leaves']??[] as $k => $leaveType){
                $remaining = (int)explode(" ",$leaveType['remaining'])[0];
                 $responseData[] = [
                    'id'=> $k,
                    'name'=> $leaveType['leave_type_name'],
                    'credit_before'=> $leaveType['credit_before'],
                    'remaining'=> $remaining,
                    'taken_before'=> $leaveType['leaves'],
                    'future'=> $leaveType['future'] ?? 0,
                    'total'=> $leaveType['balance'] ?? ($remaining + $leaveType['leaves'])
                ];
            }
             return new JsonResponse(
                ["data"=>$responseData], 200);
        }catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 400);
        }
    }

    public function apiGetStatusFilterOptions(): JsonResponse
    {
        return new JsonResponse(["data" => $this->service->getDropDownStatusesData()], 200);
    }

    public function apiUpdateStatus(Request $request): JsonResponse
    {
        try{
            $data = $request->validate([
                'comment' => ['sometimes', 'nullable','string', 'max:1000'],
                'status' => ['required', 'string',Rule::in(['approved', 'rejected']) ],
                'id' => ['required', 'exists:currentSite.leave_applications,id'],
            ]);

            if($data['status'] == 'rejected' && !$data['comment']){
                return response()->json([
                    'error' => __t("Rejection reason is a required field and could not be empty"),
                ], 400);
            }

            $leaveApplicationId = $data['id'];
            $actionComment = $data['comment'];
            $updateToStatus = $data['status'];


            $oldLeaveApplication = LeaveApplication::where('id', $leaveApplicationId)->first()->toArray();

            if($updateToStatus == 'approved'){
                $attendancePermissionRepository = resolve(AttendancePermissionRepository::class);
                if ($attendanceSheet = $attendancePermissionRepository->isDateIncludedInApprovedAttendanceSheet($oldLeaveApplication["staff_id"], $oldLeaveApplication["date_from"], $oldLeaveApplication["date_to"])) {
                    return response()->json([
                        'error' => sprintf(__t("You cannot create attendance permission in an approved attendance sheet %s"), "")
                    ], 400);
                }
            }

            $updatedApplication = $this->service->updateApplicationStatus(['comment'=> $actionComment, 'status'=> $updateToStatus], $leaveApplicationId);
            $recordData =  $this->appEntityShowAction->handle($this->entityKey, $leaveApplicationId, 4);
            $recordData =  toArray($recordData);
            $message = $this->handleOnUpdateStatus($updatedApplication, $leaveApplicationId, $recordData);

            $responseData = ["message"=> $message];
            return new JsonResponse(
                $responseData, 200);
        }catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 400);
        }
    }

    public function apiUndoRejection(Request $request): JsonResponse
    {
        try{
            $data = $request->validate([
                'id' => ['required', 'exists:currentSite.leave_applications,id'],
            ]);

            $leaveApplicationId = $data['id'];

            $applicationsData =$this->service->revertToPending($leaveApplicationId);
            $message = __t("Leave application Reverted to pending");
            $recordData =  $this->appEntityShowAction->handle($this->entityKey, $leaveApplicationId, 4);
            $recordData =  toArray($recordData);
            if($applicationsData['oldApplication']['status'] == LeaveApplicationStatusUtil::getApprovedStatus()){
                event(new RecoredUndoApproveEvent(new RecoredInfo($this->entityKey,  $recordData)));
            }elseif($applicationsData['oldApplication']['status'] == LeaveApplicationStatusUtil::getRejectedStatus()){
                event(new RecoredUndoRejectEvent(new RecoredInfo($this->entityKey,  $recordData)));
            }

            $responseData = ["message"=> $message];
            return new JsonResponse(
                $responseData, 200);
        }catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 400);
        }
    }

    public function apiGetLeaveTypes(Request $request): JsonResponse
    {
        try{
            $data = $request->validate([
                'employee_id' => ['required', 'exists:currentSite.staffs,id'],
            ]);
            $responseData =  $this->leaveTypeService->getUserAvaialableLeaveTypes($data['employee_id']);
            $responseData = ['data'=> $responseData['leave_types']];
            return new JsonResponse(
                $responseData, 200);
        }catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 400);
        }
    }

    public function  getUserAvaialableLeaveTypes($user_id){
        try{
            $responseData = $this->leaveTypeService->getUserAvaialableLeaveTypes($user_id);
            return new JsonResponse(
                $responseData, 200);

        }catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ], 400);
        }
    }

    private function handleOnUpdateStatus($updatedApplication, $id, $recordData){
        $message = '';
        if($updatedApplication->status == 'approved'){
            $this->service->createRelatedPermission($updatedApplication);
            $message = __t("Leave application Approved");
        }else{
            $message = __t("Leave application Rejected");
        }

        if($updatedApplication->status == LeaveApplicationStatusUtil::Rejected){
            event(new RecoredRejectedEvent(new RecoredInfo($this->entityKey,  $recordData)));
        }else{
            event(new RecoredApprovedEvent(new RecoredInfo($this->entityKey, $recordData)));
        }
        return $message;
    }
    /**
     * * Update the application status to either approved or rejected
     *
     * @return RedirectResponse
     */
    public function updateStatus(Request $request, $id) : RedirectResponse
    {
        try {
            $data = $request->validate([
                'comment' => ['sometimes', 'nullable','string', 'max:1000'],
                'status' => ['required', 'string',Rule::in(['approved', 'rejected']) ],
            ]);

            if($data['status'] == 'rejected' && !$data['comment']){
                return redirect()->back()->with('danger', __t("Rejection reason is a required field and could not be empty"));
            }

            $oldLeaveApplication = LeaveApplication::where('id', $id)->first()->toArray();

            if($data['status'] == 'approved'){
                $attendancePermissionRepository = resolve(AttendancePermissionRepository::class);
                if ($attendanceSheet = $attendancePermissionRepository->isDateIncludedInApprovedAttendanceSheet($oldLeaveApplication["staff_id"], $oldLeaveApplication["date_from"], $oldLeaveApplication["date_to"])) {
                    throw new CanNotAddAttendancePermissionInApprovedAttendanceSheet($attendanceSheet["id"], $attendanceSheet["auto_id"]);
                }
            }

            $updatedApplication = $this->service->updateApplicationStatus($data , $id);
            $recordData =  $this->appEntityShowAction->handle($this->entityKey, $id, 4);
            $recordData =  toArray($recordData);
            if($updatedApplication->status == 'approved'){
                $this->service->createRelatedPermission($updatedApplication);
                $message = __t("Leave application Approved");
                dispatch_event_action(new LeaveApplicationApproved($updatedApplication));
            }elseif( $updatedApplication->status == LeaveApplicationStatusUtil::Pending){
                $message = __t("Leave application Approved");
                dispatch_event_action(new LeaveApplicationApproved($updatedApplication));
            }
            else{
                $message = __t("Leave application Rejected");
                dispatch_event_action(new LeaveApplicationRejected($updatedApplication));
            }

            if($updatedApplication->status == LeaveApplicationStatusUtil::Rejected){
                event(new RecoredRejectedEvent(new RecoredInfo($this->entityKey,  $recordData)));
            }else{
                event(new RecoredApprovedEvent(new RecoredInfo($this->entityKey, $recordData)));
            }

            return redirect()->back()->with('success', $message);
        } catch (\Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }


    /**
     * * reverts the application status to pending status , you will have to delete the permission manually if there is a one
     *
     * @return RedirectResponse
     */
    public function revertToPending($id) : RedirectResponse
    {
        try {

            $applicationsData = $this->service->revertToPending($id);
            $message = __t("Leave application Reverted to pending");
            $recordData =  $this->appEntityShowAction->handle($this->entityKey, $id, 4);
            $recordData =  toArray($recordData);
            if($applicationsData['oldApplication']['status'] == LeaveApplicationStatusUtil::getApprovedStatus()){
                event(new RecoredUndoApproveEvent(new RecoredInfo($this->entityKey,  $recordData)));
            }elseif($applicationsData['oldApplication']['status'] == LeaveApplicationStatusUtil::getRejectedStatus()){
                event(new RecoredUndoRejectEvent(new RecoredInfo($this->entityKey,  $recordData)));
            }
            return redirect()->back()->with('success', $message);
        } catch (\Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    public function checkConfigurationRematch($id)
{
    try {
        $willRematch = $this->service->checkConfigurationRematch($id);
        return response()->json([
            'success' => true,
            'configurationWillRematch' => $willRematch,
        ]);
    } catch (\Exception $exception) {
        return response()->json([
            'success' => false,
            'configurationWillRematch' => true, // Default to true on error to show warning
            'message' => $exception->getMessage()
        ]);
    }
}


}
