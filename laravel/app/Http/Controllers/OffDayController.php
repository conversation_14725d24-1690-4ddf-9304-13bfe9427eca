<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Izam\Booking\Services\OffDayService;

class OffDayController extends Controller
{
    protected $offDayService;

    public function __construct(OffDayService $offDayService)
    {
        $this->offDayService = $offDayService;
    }

    /**
     * Create an off day
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createOffDay(Request $request)
    {
        $validatedData = $request->validate([
            'label' => 'required|string',
            'staff_id' => 'required|array',
            'staff_id.*' => 'required|integer|exists:currentSite.staffs,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
        ]);
        $result = $this->offDayService->createOffDay($validatedData);
        
        if($result['status'] == 'success') {
            return response()->json([
                'status' => $result['status'],
                'message' => $result['message'],
                'data' => $result['data'],
            ], 200);
        }else{
            return response()->json([
                'status' => $result['status'],
                'message' => $result['message'],
            ], 400);
        }
    }

    public function updateOffDay(Request $request, $id)
    {
        $validatedData = $request->validate([
            'label' => 'required|string',
            'staff_id' => 'required|array',
            'staff_id.*' => 'required|integer|exists:currentSite.staffs,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
        ]);
        $result = $this->offDayService->updateOffDay($validatedData, $id);
        if($result['status'] == 'success') {
            return response()->json([
                'status' => $result['status'],
                'message' => $result['message'],
                'data' => $result['data'],
            ], 200);
        }else{
            return response()->json([
                'status' => $result['status'],
                'message' => $result['message'],
            ], 400);
        }
    }

    /**
     * Delete an off day
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteOffDay($id)
    {
        $result = $this->offDayService->deleteOffDay($id);
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message'],
        ], $result['status'] == 'success' ? 200 : 400);
    }

    public function getOffDay($id)
    {
        $result = $this->offDayService->getOffDay($id);
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message'],
            'data' => $result['data'],
        ], 200);
    }
}