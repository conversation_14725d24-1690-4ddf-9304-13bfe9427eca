<?php

namespace App\Http\Controllers;

use App\Models\Machine;
use App\Requests\DefaultRequest;
use App\Services\MachineService;
use App\Services\MachineUnmappedEmployeeService;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/**
 * MachineController Class machine controller
 *
 * <AUTHOR> <<EMAIL>>
 */
class MachineUnmappedEmployeeController extends BaseController
{
    /**
     * @var string folder
     */
    public $folder = 'machine_unmapped_employee';

    /**
     * @var MachineUnmappedEmployeeService machine service
     */
    protected $service;

    /**
     * @var MachineService machine service
     */
    protected $machineService;

    /**
     * @var Machine machine
     */
    protected $machine;

    /**
     * @var string label
     */
    protected $label = 'Machine Employee Mappings';

    private $machine_id;

    private $record_id;

    /**
     * {@inheritDoc}
     *
     * @param  MachineUnmappedEmployeeService  $machineTypeService
     */
    public function __construct(MachineUnmappedEmployeeService $service, MachineService $machineService)
    {
        $this->machineService = $machineService;
        parent::__construct($service);
        $this->middleware(function ($request, $next) {
            $this->machine_id = Route::current()->parameter('machine');
            $this->service->machine_id = $this->machine_id;
            $machine = $this->machineService->find($this->machine_id);
            if (is_null($machine)) {
                return redirect()->route('owner.machines.index')
                    ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t('Machine')))->send();
            }

            return $next($request);
        });
    }

    /**
     * {@inheritDoc}
     */
    public function getFormValidationRules(Request $request, $id = null)
    {
        $parentValidations = parent::getFormValidationRules($request, $this->record_id);
        $messages = [
            'system_emp_id.*.unique' => __t('This employee is already mapped'),
            'system_emp_id.unique' => __t('This employee is already mapped'),
            //            'system_emp_id.*.distinct' => __t('Cannot duplicate Employee'),
        ];

        $rules['system_emp_id.*'] = [

            function ($attribute, $value, $fail) use ($request) {
                $index = str_replace(['system_emp_id.', '[', ']'], '', $attribute);
                if (empty($request->input("ignore.$index"))) {
                    if (empty($value)) {
                        return;
                    }

                    $values = array_filter($request->input('system_emp_id', []), function ($key) use ($request) {
                        return empty($request->input("ignore.$key"));
                    }, ARRAY_FILTER_USE_KEY);

                    if (count(array_filter($values, function ($v) use ($value) {
                        return $v == $value;
                    })) > 1) {
                        $fail(__t('Cannot duplicate Employee'));
                    }
                }
            },
            "unique:currentSite.machine_employee_mappings,staff_id,NULL,id,machine_id,{$this->machine_id}",
        ];

        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    public function create()
    {
        return parent::create()
            ->with('machine_id', $this->machine_id);
    }

    public function store(Request $request)
    {
        $request['machine_id'] = $this->machine_id;
        try {
            $validatedData = $this->validateFormData($request);
            $storeRequest = new DefaultRequest($validatedData->input(), $validatedData->allFiles());

            $this->service->sync($storeRequest);
            if (isApi()) {
                return response()->json(['status' => 'success', 'message' => sprintf(__t('%s Updated Successfully'), __t($this->label))]);
            } else {
                return redirect()
                    ->route('owner.machines.mappings.index', array_merge(['machine' => $this->machine_id], $this->queryParameters))
                    ->with('success', sprintf(__t('%s Updated Successfully'), __t($this->label)));
            }
        } catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route('owner.machines.unmapped_employee.create', ['machine' => $this->machine_id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('%s Updating Failed, Please Check Errors Below'), __t($this->label)));
            }
        } catch (\Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exception->getMessage())], 400);
            } else {
                return redirect()->route('owner.machines.unmapped_employee.create', ['machine' => $this->machine_id])
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('%s Updating Failed, Please Check Errors Below'), __t($this->label)));
            }
        }
    }
}
