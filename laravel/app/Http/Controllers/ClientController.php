<?php

namespace App\Http\Controllers;

use App\Exceptions\EntityNotFoundException;
use App\Facades\CakeSession;
use App\Services\ClientService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Izam\Attachment\Service\AttachmentsService;
use Matrix\Exception;

class ClientController extends BaseController
{
    /**
     * @var ClientService
     */
    protected $service;
    protected $attachmentService;

    /**
     * ClientController constructor.
     * @param ClientService $clientService
     */
    public function __construct(ClientService $clientService, AttachmentsService $attachmentService)
    {
        $this->attachmentService = $attachmentService;
        parent::__construct($clientService);
    }

    /**
     * filter clients by name
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ajaxNameFilter(Request $request)
    {
        $query = $request->query->get('q');
        $allowSuspended = $request->query->get('allow_suspended') ?? false;
        if (!empty($query)) {
            $results = $this->service->filterByName($query, $allowSuspended);
            if ($results) {
                $processedResults = [];
                foreach ($results as $k => $v) {
                    $email = !is_null($v->email) ?  trim($v->email) : $v->email;
                    $img =$this->attachmentService->resolveClientImage($v);
                    if ($email) {
                        $processedResults['results'][$k] = ['avatar' => $img,'img' => $img,'text' => "{$v->business_name} #{$v->client_number} ({$email})", 'id' => $v->id];
                    } else {
                        $processedResults['results'][$k] = ['avatar' => $img,'img' => $img,'text' => "{$v->business_name} #{$v->client_number}", 'id' => $v->id];
                    }
                    if($request->query->get('fields')){
                        foreach ($request->query->get('fields') as $field) {
                            $fieldParts = explode('.', $field);
                            if(count($fieldParts) > 1) {
                                $processedResults['results'][$k][$field] = $v->{$fieldParts[0]} ? $v->{$fieldParts[0]}->{$fieldParts[1]} : '';
                            }else{
                                $processedResults['results'][$k][$field] = $v->{$fieldParts[0]};
                            }
                        }
                    }
                }
                return response()->json($processedResults);
            }
        }
        return response()->json([]);
    }

    public function getClientCreditSummary(Request $request)
    {
        try {
            $clientCreditSummaryData = $this->service->prepareClientCreditSummary($request['client_id']);
            $activePackagesCount = $this->service->getActivePackagesCount();
            return View('clients.owner.credit_summary')
                ->with('creditTypesData', $clientCreditSummaryData)
                ->with('activePackagesCount', $activePackagesCount)
                ->with('client_id', $request['client_id']);
        } catch (\Exception $exception) {
            return View('clients.owner.credit_summary')->with('exception', $exception);
        }
    }
    public function getClientMembership(Request $request)
    {
        try {
            $clientMembership = $this->service->getClientMembership($request['client_id']);
            $client= $this->service->find($request['client_id']);
            return View('clients.owner.membership')
                ->with('clientMembership', $clientMembership)
                ->with('client', $client);
        }
        catch (EntityNotFoundException $e)
        {
            CakeSession::flashMessage($e->getMessage());
            return redirect()->to(CAKE_BASE_URL.'/owner/clients/index');
        }
        catch (\Exception $exception) {
            return View('clients.owner.membership')->with('exception', $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClientDataForAttendance(Request $request)
    {
        try {
            $clientData = $this->service->getClientDataForAttendance($request['client_id']);
            return response()->json(['data' => $clientData], 200);
        } catch (\Exception $exception) {
            return response()->json(['data' => $exception->getMessage()], 404);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClientsWithContactInfo(Request $request){
        $clientData = $this->service->getClientsForBooking($request['q'], $request['branch_id']);
        return response()->json($clientData, 200);
    }
}
