<?php

namespace App\Http\Controllers\AppManager;

use App\IzamViews\IzamView;
use App\Trait\AppManagerIndexData;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppRepositoryInterface;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppSiteRepositoryInterface;
use Izam\Daftra\AppManager\Services\AppListService;

class IndexController
{
    use AppManagerIndexData;

    public function __construct(
        private AppRepositoryInterface $appRepository,
        private AppSiteRepositoryInterface $appSiteRepository,
        private AppListService $appListService
    ) {
        $this->setDefaultViewData();
    }

    private function getInstalledAppsIds(): array
    {
        $activeSiteApps = $this->appSiteRepository
            ->getInstalledApps(getAuthOwner('id'));

        return $activeSiteApps->pluck('app_id')->toArray();
    }

    private function getActiveSiteAppsIds(): array
    {
        $activeSiteApps = $this->appSiteRepository
            ->getActiveApps(getAuthOwner('id'));

        return $activeSiteApps->pluck('app_id')->toArray();
    }

    public function __invoke($type = null)
    {
        $apps = $this->appSiteRepository
            ->getInstalledApps(getAuthOwner('id'));

        $allAppsCount = $apps->count();
        $filters = array_merge([
            'app_type' => $type
        ], request()->only(['app_status', 'category', 'name']));
        $filteredApps = $this->appListService->appFilters($apps, $filters);
        $categories = $this->appListService->getAvailableCategories($filteredApps);
        $indexedData = $this->getIndexData($filteredApps, $categories, $allAppsCount);

        $lang = getCurrentSite('language_code') == '41' ? 'en' : 'ar';
        $portalBaseUrl = sprintf('https://%s/%s', Portal_Full_Name, $lang);

        $industryId = is_int(getCurrentSite('system_industry_id')) ? getCurrentSite('system_industry_id') : 0;

        $installedAppsIds = $apps->pluck('app_id');

        $countryCode = getCurrentSite('country_code');

        $topApps = $this->appRepository
            ->getInstallableApps($countryCode, $industryId)
            ->whereNotIn('id', $installedAppsIds)
            ->where('type', $type ?? 'general')
            ->sortByDesc('installed_count')
            ->take(4);

        $hasFilters = false;
        if (collect(request()->only(['app_status', 'category', 'name']))->filter()->isNotEmpty()) {
            $hasFilters = true;
        }

        $marketPlaceUrl = '#';
        if (defined('APP_MANAGER_URL')) {
            $lang = CurrentSiteLang('code2') == 'ara' ? 'ar' : 'en';
            $marketPlaceUrl = APP_MANAGER_URL. "/$lang/marketplace";
        }

        $data = [
            'portalBaseUrl' => $portalBaseUrl,
            'apps' => $indexedData['pagination'],
            'topApps' => $topApps,
            'activeSiteAppIds' => $this->getActiveSiteAppsIds(),
            'installedAppsIds' => $this->getInstalledAppsIds(),
            'title_for_layout' => getCurrentSite('business_name') . ' - ' . __t('Applications Manager'),
            'hasFilters' => $hasFilters,
            'marketPlaceUrl' => $marketPlaceUrl
        ];

        $view = new IzamView('default/index', array_merge($data, $indexedData));

        $view->addCss('app-manager', '/frontend/assets/css/pages/app-manager.css');

        return $view;
    }
}
