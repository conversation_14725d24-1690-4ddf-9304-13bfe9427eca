<?php

namespace App\Http\Controllers\AppManager;

use App\Http\Requests\AppManager\AppReviewRequest;
use Izam\Daftra\AppManager\Repositories\AppReviewRepository;
use Izam\Daftra\AppManager\Repositories\AppSiteRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Http\Controllers\BaseController;

class AppReviewController extends BaseController
{
    public function __construct(
        protected AppReviewRepository $appReviewRepository,
        protected AppSiteRepository $appSiteRepo
    )
    {}

    public function updateAppReview($appId, AppReviewRequest $request)
    {
        $site = getCurrentSite();

        $data = [
            'rate' => $request->rate,
            'comment' => !empty($request->comment)?$request->comment:'',
            'status' => 'pending',
            'reviewer_id' => null,
            'reviewer_type' => null,
        ];

        try{
            $this->appReviewRepository->updateOrCreate(
                ['app_id' => $appId, 'site_id' => $site['id']],
                array_merge($data, [
                    'name' => $site['first_name'] . ' ' . $site['last_name']
                ])
            );
        }catch(\Exception $e){
            return new JsonResponse([
                'code' => $statusCode = 400, 'message' => __t('Failed to save your rating'), 'errors' => $e->getMessage()
            ], $statusCode);
        }

        return new JsonResponse([
            'code' => $statusCode = 200, 'message' => __t('Your rating has been saved.'), 'errors' => null
        ], $statusCode);
    }

    public function show($appId)
    {
        $review =  $this->appReviewRepository->get(getCurrentSite('id'), $appId);
        return new JsonResponse(['review' => $review], 200);
    }
}
