<?php

namespace app\Http\Controllers\AppManager;

use App\Trait\AppManagerIndexData;
use Carbon\Carbon;
use Izam\Daftra\AppManager\Models\App;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppSiteRepositoryInterface;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppRepositoryInterface;
use Izam\Daftra\AppManager\Services\AppInstaller;
use Symfony\Component\HttpFoundation\JsonResponse;

class AppManagerController
{
    use AppManagerIndexData;

    const DEACTIVATED = 3;
    const NOT_INSTALLED = 2;
    const ACTIVATED = 1;
    const ERROR = 0;
    const PAYMENT_REQUIRED = 4;

    public function __construct(
        private AppRepositoryInterface $appRepository,
        private AppSiteRepositoryInterface $appSiteRepository,
        private AppInstaller $appInstaller,
    ) {
        $this->setDefaultViewData();
    }

    public function install($id)
    {
        $app = $this->appRepository->findById($id);

        if (!$app) {
            return ['status' => false, 'code'=> self::ERROR, 'msg' => __t('App not found')];
        }

        $siteId = getCurrentSite('id');

        if ($this->appInstaller->isInstalled($id, $siteId)) {
            return redirect(route('owner.apps-manager.index', $app->type))->with('success', __t('App is Already installed'));
        }

        if ($this->isFree($app)) {
            $this->appInstaller->activate($id, $siteId);
        } elseif ($this->isPaidAndHasTrialPeriod($app)) {
            if ($this->isSettingsRequired($id)) {
                $this->appInstaller->installDeactivated($id, $siteId, Carbon::now()->addDays($app->trial_period)->toDateTimeString());
                if (!isApi()) {
                    return redirect(route('owner.apps-manager.settings', $id));
                }

                return new JsonResponse(['status' => false, 'code'=> self::NOT_INSTALLED, 'msg' => __t('Settings Required')], 400);
            }

            $this->appInstaller->activate($id, $siteId, Carbon::now()->addDays($app->trial_period)->toDateTimeString());
        } else {
            $this->appInstaller->installDeactivated($id, $siteId);
        }

        return redirect(route('owner.apps-manager.index', $app->type))->with('success', __t('App installed successfully'));
    }

    public function activate($id, $status)
    {
        $app = $this->appRepository->findById($id);

        if (!$app) {
            return ['status' => false, 'code'=> self::ERROR, 'msg' => __t('App not found')];
        }

        $siteId = getCurrentSite('id');

        if (!$this->appInstaller->isInstalled($id, $siteId)) {
            $this->appInstaller->installDeactivated($id, $siteId);
        }

        if (
            $this->isSettingsRequired($id) &&
            $this->appInstaller->isSettingsRecordIsEmpty($id, $siteId)
        ) {

            if (!isApi()) {
                return redirect(route('owner.apps-manager.settings', $id));
            }

            return new JsonResponse(['status' => false, 'code'=> self::NOT_INSTALLED, 'msg' => __t('Settings Required')], 400);
        }

        if ($status === 'true') {
            if (!$this->isPurchased($app)) {
                return new JsonResponse([
                    'status' => false,
                    'code'=> self::PAYMENT_REQUIRED,
                    'msg' => __t('You cannot activate this application because it has not been purchased or renewed. Please complete the purchase first.'),
                ], 400);
            }

            $this->appInstaller->activate($id, $siteId);

            if (!isApi()) {
                /** if in the future a condition is used to redirect directly implement it here */
                if (true) {
                    $authorizeURL = 'https://'. Portal_Full_Name .'/app/authorize?' . http_build_query([
                        'redirect_url' => $app->redirect,
                        'token' => __createToken(getCurrentSite('id')),
                        'site_id' => getCurrentSite('id'),
                        'app_id' => $app->id,
                    ]);

                    return redirect($authorizeURL);
                }

                return redirect(route('owner.apps-manager.index'))->with('success', __t('App activated successfully'));
            }

            return ['status' => true, 'code'=> self::ACTIVATED, 'msg' => __t('App activated successfully')];
        }

        $this->appInstaller->deactivate($id, $siteId);
        return ['status' => true, 'code'=> self::DEACTIVATED, 'msg' => __t('App deactivated successfully')];
    }

    private function isSettingsRequired($id): bool
    {
        $app = $this->appRepository->findById($id);

        /** we could check for mandatory fields later */
        if (is_array($app->getSettings()) && !empty($app->getSettings())) {
            return true;
        }
        return false;
    }

    private function isFree(App $app): bool
    {
        return $app->price == 0;
    }

    public function uninstall($id)
    {
        $app = $this->appRepository->findById($id);

        if (!$app) {
            return ['status' => false, 'msg' => __t('App not found')];
        }

        $this->appInstaller->uninstall($id, getCurrentSite('id'));

        return ['status' => true, 'msg' => __t('App uninstalled successfully')];
    }

    private function isPurchased($app): bool
    {
        if ($app->price == 0) {
            return true;
        }

        $appSite = $this->appSiteRepository->getAppSite($app->id, getAuthOwner('id'));

        if (!$appSite || !$appSite->expires_at) {
            return false;
        }

        $expiryDate = Carbon::parse($appSite->expires_at);

        return $expiryDate->isFuture();
    }

    private function isPaidAndHasTrialPeriod(App $app): bool
    {
        return $app->price > 0 && $app->trial_period > 0;
    }
}
