<?php
namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;

class EmployeeSearchController extends Controller
{
    private SmartEmployeeSearchService $searchService;

    public function __construct(SmartEmployeeSearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    public function search(Request $request): JsonResponse
    {
        $term = $request->get('term', '');
        $limit = $request->get('limit', 10);
        if(empty($term)) return response()->json([]);
        $results = $this->searchService->search($term, (int)$limit);

        return response()->json($results);
    }
}
