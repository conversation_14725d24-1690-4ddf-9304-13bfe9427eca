<?php

namespace App\Http\Controllers;

use App\Exceptions\ExportNotAllowed;
use App\Helpers\BreadCrumbs\BreadCrumbs;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Factories\Views\ShowEntityViewFactory;
use App\Modules\LocalEntity\Views\DetailedEntityFieldView;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use App\Repositories\EntityRepository;
use App\Utils\Export\ExportPermissionsUtil;
use Illuminate\Http\Request;
use App\Services\ExportService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;

class ExportController extends BaseController
{
	public $folder = 'export';
	protected $entity;
	protected $entityName;
	protected $service;

	function __construct(ExportService $service, private AdditionalFieldsAwareService $additionalFieldsAwareService)
	{
		$this->service = $service;
		parent::__construct($service);
	}


    public function viewExport(Request $request)
    {
        try {
            if ($request->has('count') && $request->count == 0) {
                return redirect()->back()->with('danger', __t("There is no items to export"));
            }
            $entity = $request['entity'];
            $filters = $request['filters'];

            if( $request->filled('conditions_link')){
                $queryString = urldecode($request['conditions_link']);
                parse_str($queryString, $filters);
                $filters = $filters['filter'];
            }

            $exportedIDs = $request['ids'] ?? 'all';
            $exportedIDs = is_array($exportedIDs) ? implode(',', $exportedIDs) : $exportedIDs;
            $this->entity = $entity;
            $this->entityName = $entity;
            $label = $this->service->getEntityLabel($this->entityName)->label;
            if ($exportedIDs == 'none')
                return redirect()->back()->with('danger', sprintf(__t('No %s Selected'), __t($label)));
            $entities = $this->service->getEntitiesForExport($entity);
            $additionalFields = $this->additionalFieldsAwareService->getImportFields($entity);
            return view($this->folder . '.export', ['entities' => $entities, 'filters' => $filters, 'entity' => $entity, 'exportedIDs' => $exportedIDs, 'additionalFields' => $additionalFields]);
        } catch (\Exception $exception) {
            return redirect(CAKE_BASE_URL)->with('danger', $exception->getMessage());
        }
    }


    public function submitExport(Request $request)
	{
		$validate = $request->validate([
    	    'data' => 'required',
    	    'entity' => 'required'
        ]);
		// Selected entity fields data
        $selectedFields = $request['data'];
        $entity = $request['entity'];
        $extraSelectedFields = $this->service->getExtraSelectedFields($entity);
        $selectedFields = array_merge($selectedFields, $extraSelectedFields);
        $exportedIDs = [];
        $exportNoneFlag = false;
        if(isset($request['exportedIDs']) && !empty($request['exportedIDs'])){
            if($request['exportedIDs'] == 'all')
                $exportedIDs = null;
            elseif($request['exportedIDs'] == 'none')
                $exportNoneFlag = true;
            else
                $exportedIDs = explode(',',$request['exportedIDs']);
        }

        $labels = $this->service->getFieldsLabels($entity,$selectedFields);
        $relations = $this->service->getRelations($selectedFields);
        if($exportNoneFlag)
            $dataCollection = [];
        else
        $dataCollection = $this->service->prepareData($entity,$selectedFields,$exportedIDs);
        if (!empty($extraSelectedFields) && count($dataCollection) && isset($dataCollection[0])) {
            $relations = $this->service->getRelationsIndex($relations,$dataCollection[0]);

        }
    $exportData = $this->service->injectRelationData($dataCollection,$relations);
        $exportData = $this->service->formatData($exportData,$entity);

        return $this->service->prepareCSV($entity, $exportData, $labels);
	}


    protected function setBreadCrumbs($service,$return = false)
    {
        if (!request()->entity) {
            abort(404);
        }
        $entity = request()->entity;
        $breadCrumbsEntity = $entity;
        if (request()->has('from_entity')) {
            $breadCrumbsEntity = request('from_entity');
            $from_entity_id = request('from_entity_id');
        }
        $entityModel = EntityRepository::getEntityByKey($breadCrumbsEntity);
        if ($entityModel) {
            $repoName = "\App\Repositories\\" . $entityModel->repo_name;
            if(!class_exists($repoName)) {
                $repoName = $entityModel->repo_name;

            }
            $controllerName = str_replace('Repository', 'Controller', $repoName);
            $ids = request()->get('ids');
            // Handle array format (ids[0]=1&ids[1]=2) and convert to comma-separated string
            if (is_array($ids)) {
                $ids = implode(',', $ids);
            }
            
            $url = ['prefix' => 'owner.', 'controller' => $controllerName, 'action' => 'index', 'params' => []];
            if (request()->has('from_entity')) {
                $url['action'] = 'show';
                $url['params'] = [[$from_entity_id]];
            } elseif ($ids && count(explode(',', $ids)) == 1 && $ids != 'all' && $ids != 'none') {
                $url['action'] = 'show';
                $url['params'] = [[$ids]];
            }
            $serviceName =  'App\Services\\' . $entityModel->service_name;
            if(!class_exists($serviceName)) {
                $serviceName = $entityModel->service_name;
            }
            if (!empty($entityModel->service_name)&& method_exists(app()->make($serviceName),'exportBreadCrumbs')) {
                $breadCrumbs=app()->make($serviceName )->exportBreadCrumbs();
            }else
            {
                $breadCrumbs = BreadCrumbs::generateBreadCrumbs(app()->make($repoName), $url, EntityRepository::getEntityByKey($breadCrumbsEntity));
            }
            $breadCrumbs [] = [
                'title' => __t('Export')
            ];
            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        } else {
            $breadCrumbs = [
                ['title' => __t('Export')]
            ];
            View::share('generalBreadCrumbs', $breadCrumbs);
            $this->setPageTitleFromBreadCrumbs($breadCrumbs);
        }
    }

}
