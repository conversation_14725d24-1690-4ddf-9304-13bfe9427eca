<?php

namespace App\Http\Controllers;

use App\Exceptions\ProductsBulkUpdateFails;
use App\Repositories\CategoryRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\PriceListRepository;
use App\Repositories\ProductRepository;
use App\Repositories\StockTransactionRepository;
use App\Requests\Inventory\GetProductBalanceRequest;
use App\Services\ProductService;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\ValidationException;
use Izam\Daftra\Common\Utils\PermissionUtil;
use App\Facades\Permissions;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use App\Services\CommonActivityLogService;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use App\Requests\ActivityLog\ActivityLogRequest;


class ProductController extends BaseController
{
    public $folder = 'products';
    /**
     * @var ProductService
     */
    protected $service;

    function __construct(ProductService $service)
    {
        parent::__construct($service);
    }

    public function ajaxNameFilter(Request $request)
    {
        $query = $request->query->get('q');
        $allowInActive = $request->query->get('allow_inactive') ?? false;
        if (!empty($query)) {
            $results = $this->service->filterName($query, $allowInActive);
            if ($results) {
                $processedResults = [];
                foreach ($results as $k => $v) {
                    if (!is_null($v->email_address))
                        $processedResults['results'][$k] = ['text' => "{$v->name} #{$v->product_code}", 'id' => $v->id];
                    else
                        $processedResults['results'][$k] = ['text' => "{$v->name} #{$v->product_code}", 'id' => $v->id];

                    if($request->query->get('fields')){
                        foreach ($request->query->get('fields') as $field)
                        {
                            $fieldParts = explode('.', $field);
                            if(count($fieldParts) > 1)
                            {
                                $processedResults['results'][$k][$field] = $v->{$fieldParts[0]} ? $v->{$fieldParts[0]}->{$fieldParts[1]} : '';
                            }else{
                                $processedResults['results'][$k][$field] = $v->{$fieldParts[0]};
                            }
                        }
                    }
                }
                return response()->json($processedResults);
            }
        }
        return response()->json([]);
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function bulkUpdateViewForm()
    {
        return view($this->folder . '.' . $this->routesPrefix . 'bulk_update', $this->service->getBulkUpdateData());
    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function submitBulkUpdateFilters(Request $request)
    {
        try {
            $rules = $this->getBulkUpdateValidationRules();
            $request->validate($rules['rules'], $rules['messages']);
            $data = $this->service->bulkUpdateFilters($request);
            $data['pagination'] = $this->paginate($data['products'], 20, $request->page);
            $data['type'] = $request->type;
            return view($this->folder . '.' . $this->routesPrefix . '.' . 'bulk_update_confirmation', $data);
        } catch (QueryException $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            Log::error('Bulk update filters Query Exception', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t('Fetching Items fails'));
        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exceptionMessage));
        } catch (ProductsBulkUpdateFails $exception) {
            return redirect()->back()->withInput(request()->all())->with('danger', __t($exception->getMessage()));
        } catch (\Exception $exception) {
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            Log::error('Bulk update filters Exception', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));
        }
    }

    public function bulkUpdate(Request $request)
    {
        try{
            $result = $this->service->bulkUpdate($request);
            if ($result) {
                return redirect()->route('owner.price_lists.index')->with('success', __t('Items Updated successfully'));
            } else {
                return redirect()->route('owner.price_lists.index')->with('danger', __t('Items Updated failed'));
            }
        } catch (\Exception $exception){
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            Log::error('Bulk update filters Exception', ['Message' => $exception->getMessage(), 'Errors' => $errors, 'Exception' => $exception]);
            return redirect()->back()
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));
        }
    }

    /**
     * @return array
     */
    private function getBulkUpdateValidationRules() : array
    {
        $rules = [];
        if (request('type') === "price_list") {
            $rules['price_list'] = "required|numeric|exists:currentSite.group_prices,id,deleted_at,NULL";
        }
        if (request('amount_type') === "amount_selection") {
            $rules['amount'] = "required|numeric";
        } else {
            $rules['percent_type'] = "required|in:0,1";
            $rules['percent_amount'] = "required|numeric|min:0";
            if (request("percent_type") === "0") {
                $rules['percent_amount'] = "required|numeric|min:0|max:99";
            }
        }
        $messages = array_merge($this->getValidationRulesMessages(),[
            "max" => ":attribute should be less than :max",
            "min" => ":attribute should be more than :min",
        ]);
        return compact('rules', 'messages');
    }

    protected function setBreadCrumbs($service,$return = false)
    {
        parent::setBreadCrumbs($service);
        $breadcrumbs = [];
        $routeName = Route::current()->action['as'] ?? null;
        if($routeName == "owner.products.bulk-update.view" || $routeName == "owner.products.bulk-update-filters"){
            $breadcrumbs = [
                [
                    'title' => __t('Price List'),
                    'link' => route('owner.price_lists.index'),
                ],
                [
                    'title' => __t("Bulk Update"),
                    'link' => null
                ]
            ];
        }
        View::share('generalBreadCrumbs', $breadcrumbs);
        $this->setPageTitleFromBreadCrumbs($breadcrumbs);
    }

    public function getProductUnitFactors($productId) {
        try{
            $product = $this->service->find($productId);
            if($product && $product->unitTemplate && $product->unitTemplate->factors) {
               $templates = $product->unitTemplate->factors;
                return response()->json(['factors' => array_merge( [$product->unitTemplate->toArray()],$templates->toArray())??false]);
            } else {
                return response()->json(['factors' => []]);
            }
        }catch (\Exception $e) {
            $factors = false;
            Log::error('error getting product factors', ['exception' => $e]);
        }

    }

    public function ajaxBrandFilter(Request $request)
	{
        return response()->json(
            $this->service->getDistinctBrands(
            $request->query->get('q')
            ) ?? [] );
	}

    public function ajaxProductsFilter(Request $request)
	{
        return response()->json(
            $this->service->getAutoSuggestProducts(
            $request->query->get('q'),
            $request->query->get('active_only', 0),
            $request->query->get('products_only', 0),
            $request->query->get('services_only', 0),
            $request->query->get('for_booking', 0)
            ) ?? [] );
	}

    public function ajaxProductsFilterV2(Request $request)
	{
        $validParams = [
            'get_by_id_only' => true,
            'id' => true,
            'status' => true,
            'search' => true,
            'exclude' => true,
            'added_relations' => true,
        ];
        $params = array_filter(
            $request->all(),
            fn($paramKey) => isset($validParams[$paramKey]),
            ARRAY_FILTER_USE_KEY
        );
        return response()->json($this->service->getAutoSuggestProductsV2($params)->toArray());
	}


    public function getProductsAveragePrices(Request $request) {
        try{
            $productsIds = $request->query->get('productsIds') ?? [];
            if($productsIds){
                return response()->json(['data' => $this->service->getProductsAveragePrices($productsIds)]);
            }else{
                return response()->json(['data' => []]);
            }
        }catch (\Exception $e) {
            // Log::error('error getting product average prices', ['exception' => $e]);
        }

    }

    public function generateSerials(Request $request) {
        $numberOfSerials = $request->get('number_of_serials', 1);
        $startingSerial = $request->get('starting_serial');
        if(empty($startingSerial)){
            $startingSerial = $this->service->getLastTrackingSerial();
        }
        return json_encode($this->service->generateSerials($numberOfSerials, $startingSerial));
    }

    /**
     * Get paginated and filtered list of products for API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiIndex(Request $request)
    {
        try {
            // inject mode=api to the request
            $request->merge(['mode' => 'api']);

            $products = $this->service->getWithOffers($request->get('filters'));

            return response()->json($products);
        } catch (\Exception $exception) {
            Log::error('API Products listing error', [
                'message' => $exception->getMessage(),
                'exception' => $exception
            ]);

            return response()->json([
                'status' => 'error',
                'message' => __t('Error retrieving products'),
                'errors' => method_exists($exception, 'errors') ? $exception->errors() : []
            ], 500);
        }
    }

    public function apiCreateService(Request $request, AppEntityStructureGetter $structureGetter, CommonActivityLogService $activityLogService)
    {
        if(!Permissions::checkPermission(PermissionUtil::Proudcts_Add_New_Proudct)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to create a service')
            ], 403);
        }
        $data = $request->json()->all();
        $result = $this->service->createService($data);
        if (!$result['status']) {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }
        $st = $structureGetter->buildEntity(EntityKeyTypesUtil::SERVICE_ENTITY_KEY);
        $requests = (new EntityActivityLogRequestsCreator())->create($st, $result['data'], []);
        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }   
        return response()->json([
            'status' => 'success',
            'message' => __t('Service created successfully'),
            'data' => $result['data']
        ]);
    }

    public function apiUpdateService($id, Request $request, AppEntityStructureGetter $structureGetter, CommonActivityLogService $activityLogService)
    {
        $service = $this->service->find($id);
        if(!$service){
            return response()->json([
                'status' => 'error',
                'message' => __t('Service not found')
            ], 404);
        }
        if(!Permissions::checkPermission(PermissionUtil::Edit_Delete_all_Products) && !(Permissions::checkPermission(PermissionUtil::Edit_And_delete_his_own_added_Products) && $service['staff_id'] == getAuthOwner('staff_id'))){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to update a service')
            ], 403);
        }
        $data = $request->json()->all();
        $result = $this->service->updateService($id, $data);
        if (!$result['status']) {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }
        $st = $structureGetter->buildEntity(EntityKeyTypesUtil::SERVICE_ENTITY_KEY);

        $requests = (new EntityActivityLogRequestsCreator())->create($st, $result['data']->toArray(), $service->toArray());
        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }
        return response()->json([
            'status' => 'success',
            'message' => __t('Service updated successfully'),
            'data' => $result['data']
        ]);
    }

    public function apiDeleteService($id, AppEntityStructureGetter $structureGetter, CommonActivityLogService $activityLogService)
    {
        $service = $this->service->find($id);
        if(!$service){
            return response()->json([
                'status' => 'error',
                'message' => __t('Service not found')
            ], 404);
        }
        if (!Permissions::checkPermission(PermissionUtil::Edit_Delete_all_Products) &&
            !(Permissions::checkPermission(PermissionUtil::Edit_And_delete_his_own_added_Products) && $service['staff_id'] == getAuthOwner('staff_id'))) {
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to delete this service')
            ], 403);
        }

        $result = $this->service->deleteService($id);
        if (!$result['status']) {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }

        // Log activity
        $st = $structureGetter->buildEntity(EntityKeyTypesUtil::SERVICE_ENTITY_KEY);
        $requests = (new EntityActivityLogRequestsCreator())->create($st, [], $service->toArray());
        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }

        return response()->json([
            'status' => 'success',
            'message' => __t('Service deleted successfully')
        ]);
    }

    public function apiListServices(Request $request)
    {
        if(!Permissions::checkPermission(PermissionUtil::View_his_own_Products)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to view services')
            ], 403);
        }

        $result = $this->service->listServices($request->all());
        return response()->json($result);
    }

    public function apiGetService($id)
    {
        $service = $this->service->find($id);
        if(!$service){
            return response()->json([
                'status' => 'error',
                'message' => __t('Service not found')
            ], 404);
        }

        $result = $this->service->getService($id);
        return response()->json($result);
    }

}
