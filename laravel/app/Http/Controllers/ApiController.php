<?php

namespace App\Http\Controllers;

use App\Facades\Settings;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;

class ApiController extends Controller
{
    public function __invoke()
    {
        $roles = settings::getValue(0, SettingsUtil::ROLES_LAST_UPDATED_AT, null, false,false);
        $branches = settings::getValue(PluginUtil::BranchesPlugin, SettingsUtil::BRANCHES_LAST_UPDATED_AT, null, true,false);
	    $branches_share_settings = settings::getValue(PluginUtil::BranchesPlugin, SettingsUtil::BRANCHES_SHARE_SETTINGS_UPDATED_AT, null, false,false);
        $terminals = settings::getValue(PluginUtil::PosPlugin, SettingsUtil::TERMINALS_LAST_UPDATED_AT, null, false,false);
        $clients = settings::getValue(PluginUtil::ClientsPlugin, SettingsUtil::CLIENTS_LAST_UPDATED_AT, null, false,false);
        $categories = settings::getValue(0, SettingsUtil::CATEGORIES_LAST_UPDATED_AT, null, false,false);
        $products = settings::getValue(0, SettingsUtil::PRODUCTS_LAST_UPDATED_AT, null, false,false);
        $taxes = settings::getValue(0, SettingsUtil::TAXES_LAST_UPDATED_AT, null, false,false);
        $pos_settings = settings::getValue(PluginUtil::PosPlugin, SettingsUtil::POS_SETTINGS_UPDATED_AT, null, false,false);
		$price_list_items = settings::getValue(PluginUtil::InventoryPlugin, SettingsUtil::PRICE_LIST_ITEMS_UPDATED_AT, null, false,false);
		$invoice_templates = settings::getValue(0, SettingsUtil::INVOICE_TEMPLATES_LAST_UPDATED_AT, null, false,false);
		$item_staff = settings::getValue(0, SettingsUtil::STAFFS_LAST_UPDATED_AT, null, false,false);
		$unit_template = settings::getValue(0, SettingsUtil::UNIT_TEMPLATES_UPDATED_AT, null, false,false);
		$settings = settings::getValue(0, SettingsUtil::SETTINGS_GENERAL_UPDATED_AT, null, false,false);
		$site_info = settings::getValue(0, SettingsUtil::SITE_INFO_UPDATED_AT, null, false,false);
		$tracking_number = settings::getValue(PluginUtil::PRODUCT_TRACKING_PLUGIN, SettingsUtil::TRACKING_NUMBER_UPDATED_AT, null, false,false);
        $plugins_info = settings::getValue(0, SettingsUtil::PLUGINS_UPDATED_AT, null, false,false);
        $journal_accounts = settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::JOURNAL_ACCOUNT_UPDATED_AT, null, false,false);
        $journal_categories = settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::JOURNAL_CATEGORIES_UPDATED_AT, null, false,false);
        $expense_categories = settings::getValue(0, SettingsUtil::EXPENSE_CATEGORIES_UPDATED_AT, null, false,false);
        $offers = settings::getValue(PluginUtil::OffersPlugin, SettingsUtil::OFFERS_UPDATED_AT, null, false,false);
        $inventory_settings = settings::getValue(PluginUtil::InventoryPlugin, SettingsUtil::INVENTORY_SETTINGS_UPDATED_AT, null, false,false);
        $shipping_options = settings::getValue(PluginUtil::InvoicesPlugin, SettingsUtil::SHIPPING_OPTIONS_UPDATED_AT, null, false,false);
        $order_source = settings::getValue(PluginUtil::InventoryPlugin, SettingsUtil::ORDER_SOURCE, null,false,false);
        $expensesIsMandatoryEnabled = !!settings::getValue(PluginUtil::ExpensesPlugin , 'selecting_expense_account_is_mandatory' , allowCache: false);
        $incomeIsMandatoryEnabled = !!settings::getValue(PluginUtil::ExpensesPlugin , 'selecting_income_account_is_mandatory' , allowCache: false);
        return response()->json([
            'branches' => $branches ? (int) $branches : 0,
			'branches_share_settings' => $branches_share_settings ? (int) $branches_share_settings : 0,
            'roles' => $roles ? (int) $roles : 0,
            'terminals' => $terminals ? (int) $terminals : 0,
            'clients' => $clients ? (int) $clients : 0,
            'categories' => $categories ? (int) $categories : 0,
            'products' => $products ? (int) $products : 0,
            'taxes' => $taxes ? (int) $taxes : 0,
            'pos_settings' => $pos_settings ? (int) $pos_settings : 0,
	        'price_list_items' => (int) ($price_list_items ?? 0),
	        'invoice_templates' =>  (int) ($invoice_templates ?? 0),
			'unit_template' => (int) ($unit_template ?? 0),
			'settings' => (int) ($settings ?? 0),
	        'item_staff' => $item_staff ?? 0,
	        'site_info' => (int) ($site_info ?? 0),
	        'tracking_number' => (int) ($tracking_number ?? 0),
            'plugins' => (int) ($plugins_info ?? 0),
            'journal_accounts' => (int) ($journal_accounts ?? 0),
            'expense_categories' => (int) ($expense_categories ?? 0),
            'journal_categories' => (int) ($journal_categories ?? 0),
            'offers' => (int) ($offers ?? 0),
            'inventory_settings' => (int) ($inventory_settings ?? 0),
            'shipping_options' => (int) ($shipping_options ?? 0),
            'order_source' => (int) ($order_source ?? 0),
            'expensesIsMandatoryEnabled'=>$expensesIsMandatoryEnabled,
            'incomeIsMandatoryEnabled'=>$incomeIsMandatoryEnabled
        ]);
    }
}
