<?php

namespace App\Http\Controllers;

use App\Facades\Plugins;
use App\Facades\Staff;
use App\IzamViews\IzamView;
use App\Models\BankTransaction;
use App\Models\Branch;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Actions\ListActionV2;
use App\Modules\LocalEntity\AppEntities\Listing\Modes\PaginateMode;
use App\Modules\LocalEntity\AppEntities\Listing\Modes\Responses\PaginateModeResponse;
use App\Modules\LocalEntity\Listing\Parser\FormParser;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Repositories\BankTransactionRepository;
use App\Repositories\BranchRepository;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Criteria\JoinCriteria;
use App\Repositories\JournalTransactionRepository;
use App\Repositories\TreasuryRepository;
use App\Services\Bank\BankSystemTransactionService;
use App\Services\BankTransactionService;
use App\Services\CommonActivityLogService;
use App\Services\JournalTransactionService;
use App\Utils\BankTransactionsStatusUtil;
use App\Utils\PluginUtil;
use App\Utils\TreasuryTypeUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\JournalUtil;
use Izam\Daftra\Journal\Services\AutoAccountFinder;
use Izam\Daftra\Journal\Services\File\FileFacade;

class BankTransactionsController extends Controller
{
    function __construct(private JournalTransactionService $journalTransactionService, private BankTransactionService $bankTransactionService)
    {
        parent::__construct();
    }
    public function getBankTransactionSystemTransactions(
        $transaction_id,
        BankTransactionRepository $bankTransactionRepository,
        BranchRepository $branchRepository,
        AppEntityStructureGetter $structureGetter,
        FormParser $parser,
        ListActionV2 $listAction,
    ) {
        $bankTransaction = $bankTransactionRepository->getTransactionToBeMatched($transaction_id);
        //If there is not trasnaction found return empty transactions list
        if(!$bankTransaction) {
            return response()->json(['transactions' => []]);
        }
        $bank = $bankTransaction->bank;
        $finder = new AutoAccountFinder(getPdo());
        $bankAccount = $finder->getAutoAccount('treasury', $bank->id);
        $entity = $structureGetter->buildEntity('journal_transaction');
        $listingMetaObject = $listAction->parseUrl($parser);
        $systemCurrency = get_default_currency();
        if($bankTransaction->type === 'deposit') {
            $transactionAmountField = 'journal_transactions.debit';
            $transactionSortField = 'journal_transactions.debit';
            if($bank->currency_code && $systemCurrency !== $bank->currency_code) {
                $transactionSortField = "journal_transactions.currency_debit";
            }
        } else {
            $transactionAmountField = 'journal_transactions.credit';
            $transactionSortField = 'journal_transactions.credit';
            if($bank->currency_code && $systemCurrency !== $bank->currency_code) {
                $transactionSortField = "journal_transactions.currency_credit";
            }
        }
        $level = 1;
        $validators = $listAction->getUrlValidationRules($listingMetaObject, $entity, $level);
        $data = $listAction->assignRequestData($listingMetaObject, $entity, $level, $validators);
        if($systemCurrency !== $bank->currency_code) {
            $data->setFilter(new FilterMeta('journal_transactions__journal.currency_code','=',$bank->currency_code));
        }

        $branchesActive = Plugins::pluginActive(PluginUtil::BranchesPlugin);
        $branches = [];
        if($branchesActive) {
            $branches = $branchRepository->list(['id', 'name'], true);
            $data->setFilter(new FilterMeta('journal_transactions__journal.branch_id','in',getStaffBranches()));
        }

        $data->setFilter(new FilterMeta('journal_transactions.bank_transaction_id', 'is_null', null));
        $data->setFilter(new FilterMeta('journal_transactions.status', 'equal', 'not_matched'));
        $data->setFilter(new FilterMeta('journal_transactions.journal_account_id', 'equal', $bankAccount['id']));
        $data->setFilter(new FilterMeta("$transactionAmountField", '>', 0));
        $data->setFilter(new FilterMeta('journal_transactions__journal.entity_type', 'not_in', ['year_opening_balance', 'year_closing_balance']));
        $metaRepository = new AppEntityMetaRepository();
        $modeMeta = $listingMetaObject->getMode();
        $mode = new PaginateMode($metaRepository, $data);
        $query = $mode->getProcessQueryBuilder( $entity, $level);
        $query->reorder()
            ->orderBy(DB::raw("ABS(DATEDIFF('{$bankTransaction->date}', journal_transactions__journal.date))","ASC"))
            ->orderBy(DB::raw( "ABS({$bankTransaction->amount} - $transactionSortField)","ASC"));


        $responseData = $query->paginate($modeMeta->getPerPage(),['*'], 'page', $modeMeta->getPage());

        $response = new PaginateModeResponse($listingMetaObject);
        $systemTransactions = $response->respond($entity, $responseData);

        $systemTransactions->getCollection()->transform(function($transaction) use ($branchesActive, $branches){
            toArray($transaction);
            return $this->journalTransactionService->formatSystemTransactionForMatching($transaction, $branches);
        });
        return response()->json(['transactions' => $systemTransactions]);
    }

    public function getBankTransactionsForMatch($bank_id,
                                                AppEntityStructureGetter $structureGetter,
                                                FormParser $parser,
                                                ListActionV2 $listAction,
                                                $bank_transaction_id = null) {
        $entity = $structureGetter->buildEntity('bank_transaction');
        $urlStructure = $listAction->parseUrl($parser);
        $level = 1;
        $validators = $listAction->getUrlValidationRules($urlStructure, $entity, $level);
        $data = $listAction->assignRequestData($urlStructure, $entity, $level, $validators);
        if($bank_transaction_id) {
            $data->setFilter(new FilterMeta('bank_transactions.id', 'equal', $bank_transaction_id));
        }
        $data->setFilter(new FilterMeta('bank_transactions.bank_id', 'equal', $bank_id));
        $data->setFilter(new FilterMeta('bank_transactions.status', 'equal', 'not_matched'));
        $metaRepository = new AppEntityMetaRepository();
        $mode = new PaginateMode($metaRepository, $data);
        $responseData = $listAction->modeExecute($mode, $entity, $level);
        $response = new PaginateModeResponse($urlStructure);
        $responseData = $response->respond($entity, $responseData);

        return response()->json(['transactions' => $responseData]);
    }

    public function matchBankTransactionToSystemTransactions(
        $bank_transaction_id,
        Request $request,
        BankSystemTransactionService $bankSystemTransactionService,
        JournalTransactionRepository $journalTransactionRepository,
        BankTransactionRepository $bankTransactionRepository,
        CommonActivityLogService $activityLogService
    ) {
        $bankTransaction = $bankTransactionRepository->getTransactionToBeMatched($bank_transaction_id);
        $systemTransactionIds = $request->get('system_transactions');
        if(!$bankTransaction) {
            return response()->json(['code' => 404, 'message' => 'bank transaction not found']);
        }
        $bank = $bankTransaction->bank;
        $finder = new AutoAccountFinder(getPdo());
        $bankAccount = $finder->getAutoAccount('treasury', $bank->id);
        if(!$bankAccount) {
            return response()->json(['code' => 404, 'message' => 'bank account not found']);
        }

        $systemCurrency = get_default_currency();
        $transactionsToMatch = $bankSystemTransactionService->getBankTransactionSystemTransactionsToBeMatched(
            $journalTransactionRepository,
            $bankAccount['id'],
            $bankTransaction->type,
            $systemTransactionIds,
            $systemCurrency !== $bank->currency_code ? $bank->currency_code : null
        );
        if($bank->currency_code === get_default_currency()) {
            $useLocalAmounts = true;
        } else {
            $useLocalAmounts = false;
        }
        $result = $bankSystemTransactionService->validateTransactionsCanBeMatched($transactionsToMatch, $bankTransaction->amount, $useLocalAmounts);
        if($result !== true) {
            return response()->json(['code' => 400,'message' => $result]);
        }
        $systemTransactionsIds = $transactionsToMatch->pluck('id');
        $bankSystemTransactionService->matchTransactions($journalTransactionRepository, $bankTransactionRepository, $bank_transaction_id, $systemTransactionsIds);
        $activityLogRequest = $bankSystemTransactionService->createMatchingActivityLog(
            $bankTransaction,
            ['status' => BankTransactionsStatusUtil::MATCHED],
            ['status' => BankTransactionsStatusUtil::NOT_MATCHED]
        );
        $activityLogService->addActivity($activityLogRequest);
        return response()->json(['message' => __t('Transactions matched successfully'), 'code' => 200]);
    }

    public function unMatchSystemTransaction(
        $system_transaction_id,
        CommonActivityLogService $activityLogService
    ) {
        $result = $this->journalTransactionService->unMatchSystemTransaction(
            $system_transaction_id,
            $activityLogService
        );
        $this->izamFlashMessage(sprintf(__t('%s Updated Successfully'), __t('System Transaction')), 'success');
        return redirect(getCakeURL(['prefix' => 'owner', 'controller' => 'treasuries', 'action' => 'view', $result->bank_id]).'#system-transactions');
    }

    public function unMatch(
        $bank_transaction_id,
        CommonActivityLogService $activityLogService
    ) {
        try {
            $result = $this->bankTransactionService->unMatch(
                $bank_transaction_id,
                $activityLogService
            );
            $this->izamFlashMessage(sprintf(__t('%s Updated Successfully'), __t('Bank Transaction')), 'success');
            return redirect(getCakeURL([
                'prefix' => 'owner',
                'controller' => 'treasuries',
                'action' => 'view',
                $result->bank_id,
                '?' => request()->all()
            ]) . '#bank-transactions');
        } catch (\Throwable $th) {
            return abort(404);
        }
    }

    public function match(
        $bank_id,
        $bank_transaction_id=null,
        TreasuryRepository $repository,
        BranchRepository $branchRepository
    ) {
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $branches = \App\Facades\Branch::getStaffBranches('list', getAuthOwner('staff_id'));
        } else {
            $branches = [];
        }

        $bank = $repository->pushCriteria(new EqualCriteria('type', TreasuryTypeUtil::BANK))
            ->pushCriteria(new EqualCriteria('id', $bank_id))
            ->first();

        if(!$bank) {
            return abort(404);
        }

        $site = getCurrentSite();
        $finder = new AutoAccountFinder(getPdo());
        $bankAccount = $finder->getAutoAccount('treasury', $bank_id);

        $types = JournalUtil::getEntityTypeList();
        unset($types[0]);
        $types[''] =  __t('Manual Journal');
        $title_for_layout = $site['business_name'] . ' | '. sprintf(__t('Match %s Transactions'), "#$bank->id ".$bank->name);
        $bank_transaction=null;
        if (!empty($bank_transaction_id)) {
           $bank_transaction=BankTransaction::find($bank_transaction_id);
        }

        $_PageBreadCrumbs = [
            ['title' => __t('Treasuries & Bank Accounts'), 'link' => '/v2/owner/banks/treasury'],
        ];
        if (!empty($bank) ) {
            $_PageBreadCrumbs[]= ['title' => $bank->name . ' #' . $bank->id, 'link' => '/owner/treasuries/view/' . $bank->id . '#bank-transactions'];
        }
        $_PageBreadCrumbs[]= ['title' => __t('Match')];

        $izamView = new IzamView(
            'treasuries/owner_match',
            compact(
                'types',
                'bank_transaction',
                'bankAccount',
                'branches',
                'bank',
                'title_for_layout',
                'site',
                '_PageBreadCrumbs',
            )
        );
        return $izamView;
    }

    public function getAccountLastSavedTransaction($account_id, $expense_id, JournalTransactionRepository $repository) {
        $lastSavedTransaction = $repository->pushCriteria(new EqualCriteria('journal_account_id', $account_id))
            ->pushCriteria(new JoinCriteria([['table' => 'journals', 'conditions' => [['journals.id', '=', 'journal_transactions.journal_id']]]]))
            ->pushCriteria(new EqualCriteria('journals.entity_id', $expense_id))
            ->pushCriteria(new InArrayCriteria('entity_type', [JournalUtil::AUTO_JOURNAL_TYPE_EXPENSE, JournalUtil::AUTO_JOURNAL_TYPE_INCOME]))
            ->first('journal_transactions.*');
        ;
        $lastSavedTransaction->load('journal');
        return $this->journalTransactionService->formatSystemTransactionForMatching($lastSavedTransaction->toArray());
    }

    public function getBankSystemTransactionsForMatch(
        $bank_id,
        $system_transaction_id = null
    ) {

        return response()->json([
            'transactions' => $this->journalTransactionService->getBankSystemTransactionsForMatch(
                $bank_id,
                $system_transaction_id,
            )
        ]);
    }

    public function getSystemTransactionBankTransaction(
        $transaction_id,
        $bank_id,
    ) {
        return response()->json(['transactions' => $this->journalTransactionService->getSystemTransactionBankTransaction(
            $transaction_id,
            $bank_id
        )]);
    }

    public function matchSystemTransactionToBankTransactions(
        $system_transaction_id,
        Request $request,
        CommonActivityLogService $activityLogService
    ) {
        return response()->json($this->journalTransactionService->matchSystemTransactionToBankTransactions(
            $system_transaction_id,
            $request,
            $activityLogService
        ));
    }
}
