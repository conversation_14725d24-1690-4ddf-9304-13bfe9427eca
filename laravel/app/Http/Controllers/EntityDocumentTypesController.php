<?php

namespace App\Http\Controllers;

use App\Forms\EntityDocumentTypesForm;
use App\IzamViews\IzamView;
use App\Services\EntityDocumentTypeService;
use App\Services\StaffDocumentExportService;
use App\Utils\PermissionUtil;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class EntityDocumentTypesController
{

    public function __construct(protected EntityDocumentTypeService $service, protected StaffDocumentExportService $staffDocumentExportService)
    {

    }

    public function view($entityKey)
    {
        try {
            if (!check_permission(PermissionUtil::Staff_View_Staffs) || !check_permission(PermissionUtil::MANAGE_HRM_SYSTEM)){
                return redirect()->route('owner.staff.settings')->with('danger', __t('You Are Not Allowed to Submit This Action'));
            }
            $this->service->validateEntityKey($entityKey);
            $types = $this->service->getEntityDocumentTypes($entityKey);
            $form = new EntityDocumentTypesForm();
            $form->setData(['entity_document_types_settings'=> $types]);
            if (!empty(session()->get('errors'))) {
                $oldData = request()->old();
                $form->setData(['entity_document_types_settings'=> $oldData['entity_document_types_settings']]);
                $errors = session('errors')->getMessages();
                $errorsMessages = [];
                foreach ($errors as $key => $value) {
                    $keyArray = explode('.', $key);
                    $errorIndex = $keyArray[1] ;
                    $errorsMessages[$errorIndex] = [$keyArray[2] => $value];
                }
                $form->setMessages(['entity_document_types_settings' => $errorsMessages]);
            }
            new \IzamViewData();
            return new IzamView('entity_documents/form' , [
                'form'=> $form ,
                'entityKey'=> $entityKey,
                'pageHead' => $form->getPageHeader(),
                'title_for_layout' => __t('Documents Management'),
                '_PageBreadCrumbs' => $form->getBreadCrumbs(),
            ]);
        }catch (\Throwable $e) {
            return redirect()->back()->with('danger', $e->getMessage());
        }
    }

    public function update(Request $request , $entityKey)
    {
        try {
            if (!check_permission(PermissionUtil::Staff_View_Staffs) || !check_permission(PermissionUtil::MANAGE_HRM_SYSTEM)){
                return redirect()->route('owner.staff.settings')->with('danger', __t('You Are Not Allowed to Submit This Action'));
            }
            $this->service->validateEntityKey($entityKey);
            $data = $request->validate([
                'entity_document_types_settings'=>['sometimes','nullable','array'],
                'entity_document_types_settings.*.id'=>['sometimes','nullable','exists:currentSite.entity_document_types,id'],
                'entity_document_types_settings.*.name'=>[
                    'required',
                    function ($attribute, $value, $fail) use ($request, $entityKey) {
                        $index = explode('.', $attribute)[1];
                        $id = $request->input("entity_document_types_settings.$index.id");
                        $rule = Rule::unique('currentSite.entity_document_types', 'name')
                            ->where(fn ($query) => $query->where('entity_key', $entityKey));
                        if ($id) {
                            $rule->ignore($id);
                        }
                        $validator = validator(
                            ['name' => $value], //data
                            ['name' => [$rule]] //rules
                        );
                        if ($validator->fails()) {
                            $fail(__t("Is Taken, and can't be repeated"));
                        }
                    }
                ],
            ]);
            $this->service->updateEntityDocumentTypes($entityKey , array_values($data['entity_document_types_settings'] ?? []));

            return redirect()->route('owner.manage.entity.documents',[ 'entityKey' => $entityKey ])->with('success', __t('Document Types Updated Successfully'));
        }  catch (ValidationException $validationException) {
            return redirect()->back()->withInput($request->all())->withErrors($validationException->validator);
        } catch (\Throwable $e) {
            return redirect()->back()->with('danger', $e->getMessage());
        }

    }

    

    public function exportDocuments(Request $request, $withMetadata = 1, $staffId = 0)
    {
        if (!check_permission(PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS)){
            return redirect()->route('owner.staff.settings')->with('danger', __t('You Are Not Allowed to Submit This Action'));
        }
        $documentsIds = $request->input("ids");

        if($documentsIds && is_string($documentsIds)){
            $documentsIds = explode(',', $documentsIds);
        }
        try {
            $zipPath = $this->staffDocumentExportService->exportDocuments($documentsIds, $withMetadata, $staffId);
        } catch (Exception $e) {
            return redirect("/owner/staff/$staffId#documents")->with('danger', __t('Export failed: ' . $e->getMessage()));
        }
  
        $exportName = "employee_documents_" . now()->format('Y_m_d_h_i') . '.' .pathinfo($zipPath, PATHINFO_EXTENSION);
        return Response::download($zipPath, $exportName)->deleteFileAfterSend(true);   

    }

}
