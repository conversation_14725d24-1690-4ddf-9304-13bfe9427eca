<?php

namespace App\Http\Controllers;

use App\Facades\Branch;
use App\Utils\PluginUtil;
use Illuminate\Http\JsonResponse;
use Izam\Booking\Services\BookingService;
use App\Facades\Settings;
use App\Facades\Permissions;
use Illuminate\Http\Request;
use Izam\Booking\Utils\BookingUtil;
use App\Utils\PermissionUtil;
use App\Facades\Plugins;
use Izam\Daftra\Common\Utils\InvoicePaymentStatusUtil;
use Izam\Booking\Utils\ServiceFormsFillingTimeUtil;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Services\CommonActivityLogService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use App\IzamViews\IzamView;

class BookingController extends Controller
{
    /**
     * @var BookingService
     */
    protected $service;

    /**
     * BookingController constructor.
     * @param BookingService $service
     */
    public function __construct(BookingService $service, private AppEntityStructureGetter $structureGetter, private CommonActivityLogService $activityLogService)
    {
        $this->service = $service;
    }

    public function index()
    {
        return new IzamView('new-booking/index');
    }

    /**
     * return created booking
     * @param Request $request
     * @return JsonResponse
     */
    public function createBooking(Request $request) : JsonResponse
    {
        if(!Permissions::checkPermission(PermissionUtil::ADD_NEW_BOOKING)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to create a booking')
            ], 403);
        }
        $data = $request->all();
        $result = $this->service->createBooking($data);
        
        if ($result['status']) {
            $st = $this->structureGetter->buildEntity(EntityKeyTypesUtil::BOOKING);
            $requests = (new EntityActivityLogRequestsCreator())->create($st, $result['booking'], []);
            foreach ($requests as $requestObj) {
                $this->activityLogService->addActivity($requestObj);
            }
            return response()->json([
                'status' => 'success',
                'message' => $result['message'],
                'data' => $this->service->getBooking($result['booking']['id'])
            ], 201);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * Validate the booking service
     * @param Request $request
     * @return JsonResponse
     */
    public function validateBookingService(Request $request) : JsonResponse
    {
        $data = $request->all();
        if(empty($data['service_id']) || empty($data['staff_id']) || empty($data['date']) || empty($data['duration'])){
            $errors = [];
            if(empty($data['service_id'])){
                $errors['service_id'] = __t('Service ID is required');
            }
            if(empty($data['staff_id'])){
                $errors['staff_id'] = __t('Staff ID is required');
            }
            if(empty($data['date'])){
                $errors['date'] = __t('Date is required');
            }
            if(empty($data['duration'])){
                $errors['duration'] = __t('Duration is required');
            }
            return response()->json([
                'status' => 'error',
                'message' => __t('Service is required'),
                'errors' => $errors
            ], 422);
        }
        $valid = $this->service->validateBookingService($data);
        return response()->json([
            'message' => $valid['message'],
            'data' => $valid
        ], 200);
    }

    /**
     * Get all bookings
     * @param int|null $clientId
     * @return JsonResponse
     */
    public function getBookings(int $clientId = null) : JsonResponse
    {
        if(!Permissions::checkPermission(PermissionUtil::VIEW_ALL_BOOKINGS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to view this booking')
            ], 403);
        }
        $bookings = $this->service->getBookings($clientId);
        return response()->json([
            'status' => 'success',
            'message' => __t('Bookings retrieved successfully'),
            'data' => $bookings
        ]);
    }

    /**
     * Get a specific booking
     * @param int $id
     * @return JsonResponse
     */
    public function getBooking($id) : JsonResponse
    {
        $booking = $this->service->getBooking($id);
        if(empty($booking)) {
            return response()->json([
                'status' => 'error',
                'message' => __t('Booking not found')
            ], 404);
        }
        
        if(Permissions::checkPermission(PermissionUtil::VIEW_ALL_BOOKINGS) || 
           (Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_BOOKINGS) && $booking['staff_id'] == getAuthOwner('staff_id'))) {
            return response()->json([
                'status' => 'success',
                'message' => __t('Booking retrieved successfully'),
                'data' => $booking
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to view this booking')
            ], 403);
        }
    }

    /**
     * Get booking Data for calendar view
     * @param string $date The date to get appointments for
     * @param string $type Type of view (day, week, month)
     * @param int $staff_id ID of the staff member
     * @return JsonResponse
     */
    public function bookingCalendarData(Request $request, $date = '', $type = 'day', $staffId = null) : JsonResponse
    {
        if(!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_BOOKINGS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to view this data')
            ], 403);
        }

        if (empty($date)) {
            return response()->json([
                'status' => 'error',
                'message' => __t('Date is required')
            ], 422);
        }
        
        if(!in_array($type, ['day', 'week', 'month'])){
            return response()->json([
                'status' => 'error',
                'message' => __t('Invalid type'),
                'errors' => [
                    'type' => __t('Type must be one of: day, week, month')
                ]
            ], 422);
        }
        
        if(\Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'service_employee_setting', null, false) == BookingUtil::NO_EMPLOYEE_REQUIRED && empty($staffId)){
            $staffId = 1;
        }
        $staffId = (int)$staffId;
        if($type != 'day' && empty($staffId)){
            return response()->json([
                'status' => 'error',
                'message' => __t('staff_id is required'),
                'errors' => [
                    'staff_id' => __t('Staff ID is required for week and month views')
                ]
            ], 422);
        }
        
        
        if(!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_BOOKINGS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not authorized to view this data')
            ], 403);
        }
        
         
        if(!Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_BOOKINGS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not authorized to view this data')
            ], 403);
        }

        $branchId = $request->query->get('branch_id');
        $appointments = $this->service->getBookingCalenderData($date, $type, $staffId, $branchId);
        return response()->json([
            'status' => 'success',
            'message' => __t('Calendar data retrieved successfully'),
            'data' => $appointments
        ]);
    }

    /**
     * return booking settings
     * @return JsonResponse
     */
    public function bookingInit(): JsonResponse
    {
        $settings = [
            'current_staff_id' => getAuthStaff('id'),
            'current_staff_name' => !empty(getAuthStaff('full_name')) ? getAuthStaff('full_name') : (getAuthStaff('name') . ' ' . ( !empty(getAuthStaff('middle_name')) ? getAuthStaff('middle_name') . ' ' : '') . getAuthStaff('last_name')),
            'booking_service_setting' => Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'booking_service_setting'),
            'booking_service_setting_values' => [
                BookingUtil::BOOK_DEFAULT_SERVICE => 'Book The Default Service',
                BookingUtil::BOOK_ONE_SERVICE => 'Book Only One Service',
                BookingUtil::BOOK_MULTIPLE_SERVICES =>  'Book Multiple Services'
            ],
            'default_booking_service' => Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'default_booking_service'),
            'service_employee_setting' => Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'service_employee_setting'),
            'service_employee_setting_values' => [
                BookingUtil::NO_EMPLOYEE_REQUIRED => 'No Employee Required',
                BookingUtil::SELECT_ONE_EMPLOYEE => 'Select One Employee For All The Booked Service',
                BookingUtil::SELECT_MULTIPLE_EMPLOYEES =>  'Select Employee For Each Service'
            ],
            'statuses' => BookingUtil::getBookingStatuses(),
            'default_currency_code' => getCurrentSite('currency_code'),
            'default_currency_formatted' => get_currency_formatted(),
            'invoice_statuses' => InvoicePaymentStatusUtil::getInvoiceStatuses(),
            'service_forms_filling_time' => ServiceFormsFillingTimeUtil::getList(),
            'booking_default_shift' => Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'booking_default_shift', null, false),
        ];
        
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $settings['current_branch_id'] = getCurrentBranchID();
            $settings['branches'] = Branch::getStaffBranches();
            $settings['share_products'] = Settings::getValue(PluginUtil::BranchesPlugin, 'share_products', null, true,false);
            $settings['share_clients'] = Settings::getValue(PluginUtil::BranchesPlugin, 'share_clients', null, true, false);
        }
        
        return response()->json([
            'status' => 'success',
            'message' => __t('Booking settings retrieved successfully'),
            'data' => $settings
        ]);
    }

    /**
     * return services and booking packages
     * @param Request $request
     * @return JsonResponse
     */
    public function getServicesAndPackages(Request $request): JsonResponse
    {
        $id = $request->query->get('id');
        $clientId = $request->query->get('client_id');
        $query = $request->query->get('q');
        $categoryId = $request->query->get('category_id');
        $branchId = $request->query->get('branch_id');
        $services = $this->service->getServicesAndPackages($id, $clientId, $query, $categoryId, $branchId);
        return response()->json([
            'status' => 'success',
            'message' => __t('Services and packages retrieved successfully'),
            'data' => $services
        ]);
    }

    /**
     * update booking status
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function updateBookingStatus(int $id, Request $request): JsonResponse
    {
        if(!Permissions::checkPermission(PermissionUtil::EDIT_DELETE_HIS_OWN_BOOKINGS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not allowed to update this booking')
            ], 403);
        }
        $status = $request->input('status');
        $result = $this->service->updateBookingStatus($id, $status);
        
        if ($result['status']) {
            $request = new ActivityLogRequest(
                $id,
                EntityKeyTypesUtil::BOOKING,
                ActionLineMainOperationTypesUtil::UPDATE_ACTION,
                $result['old_data']['id'] . ' #' . $result['old_data']['no'],
                null,
                ['payment_status' => BookingUtil::getBookingStatuses()[$status]],
                ['payment_status' => BookingUtil::getBookingStatuses()[$result['old_data']['payment_status']]]
            );
            $activityLogService = resolve(CommonActivityLogService::class);
            $activityLogService->addActivity($request);
            return response()->json([
                'status' => 'success',
                'message' => $result['message']
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * delete booking
     * @param int $id
     * @return JsonResponse
     */
    public function deleteBooking(int $id): JsonResponse
    {
        if(!Permissions::checkPermission(PermissionUtil::EDIT_DELETE_HIS_OWN_BOOKINGS)){
            return response()->json([
                'status' => 'error',
                'message' => __t('You are not Allowed to delete this booking')
            ], 403);
        }
        $result = $this->service->deleteBooking($id);
        if($result['status']){
            $st = $this->structureGetter->buildEntity(EntityKeyTypesUtil::BOOKING);
            $requests = (new EntityActivityLogRequestsCreator())->create($st, [], $result['booking']->toArray());
            foreach ($requests as $requestObj) {
                $this->activityLogService->addActivity($requestObj);
            }
            return response()->json([
                'status' => 'success',
                'message' => $result['message']
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * Update an existing booking
     * @param int $id The ID of the booking to update
     * @param Request $request The request containing updated booking data
     * @return JsonResponse
     */
    public function updateBooking(int $id, Request $request): JsonResponse
    {
        $data = $request->all();
        $result = $this->service->updateBooking($id, $data);
        
        if ($result['status']) {
            $st = $this->structureGetter->buildEntity(EntityKeyTypesUtil::BOOKING);
            $logData = $result['booking']->toArray();
            $logData['invoice_item'] = $result['booking']->bookingItems->toArray();
            unset($logData['booking_items']);
            $oldData = $result['old_data']->toArray();
            $oldData['invoice_item'] = $result['old_data']->bookingItems->toArray();
            unset($oldData['booking_items']);
            $requests = (new EntityActivityLogRequestsCreator())->create($st, $logData, $oldData);
            foreach ($requests as $requestObj) {
                $this->activityLogService->addActivity($requestObj);
            }
            return response()->json([
                'status' => 'success',
                'message' => $result['message'],
                'data' => $this->service->getBooking($result['booking']['id'])
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }
    }

    public function getBookingPos(int $id): JsonResponse
    {
        $booking = $this->service->getBookingPos($id);
        return response()->json($booking);
    }

    public function getShiftRoaster(): JsonResponse
    {
        $json_text = '{"days":[{"day":"2025-01-02","employees":[{"employee":{"id":1,"name":"john doe","image":"www.example.com/image.png"},"time_slots":[{"id":1,"type":"regular_shift","label":"morning shift","start_date_time":"2025-01-02 12:00","end_date_time":"2025-01-02 20:00"},{"id":2,"type":"time_block","start_time":"2025-01-02 14:00","end_time":"2025-01-02 15:00"}]},{"employee":{"id":2,"name":"lorem ipsum","image":"www.example.com/image.png"},"time_slots":[{"id":1,"type":"day_shift","start_time":"2025-01-02 10:00","end_time":"2025-01-02 18:00"}]}]},{"day":"2025-01-03","employees":[{"employee":{"id":1,"name":"john doe","image":"www.example.com/image.png"},"time_slots":[{"id":1,"type":"morning shift","label":"regular_shift","start_time":"2025-01-03 12:00","end_time":"2025-01-03 20:00"}]},{"employee":{"id":2,"name":"lorem ipsum","image":"www.example.com/image.png"},"time_slots":[{"id":1,"type":"regular_shift","label":"night shift","start_time":"2025-01-03 22:00","end_time":"2025-01-04 06:00"}]}]},{"day":"2025-01-04","employees":[{"employee":{"id":1,"name":"john doe","image":"www.example.com/image.png"},"time_slots":[{"id":1,"type":"regular_shift","label":"morning shift","start_time":"2025-01-04 12:00","end_time":"2025-01-04 20:00"}]},{"employee":{"id":2,"name":"lorem ipsum","image":"www.example.com/image.png"},"time_slots":[{"id":1,"type":"day_shift","label":"night shift","start_time":"2025-01-03 22:00","end_time":"2025-01-04 06:00"},{"id":1,"type":"day_shift","label":"night shift","start_time":"2025-01-04 22:00","end_time":"2025-01-05 06:00"}]}]}]}';
        return response()->json(json_decode($json_text));
    }

    public function setBookingDefaultShift(Request $request): JsonResponse
    {
        $request->validate([
            'shift_id' => 'required|integer|min:1',
            'apply_to_employees' => 'sometimes|boolean',
        ]);

        $result = $this->service->setBookingDefaultShift($request->all());
        $ok = ($result['status'] === true || $result['status'] === 'success');

        return response()->json([
            'status' => $ok ? 'success' : 'error',
            'message' => $result['message'],
        ], $ok ? 200 : 400);
    }

    public function getBookingDefaultShift(): JsonResponse
    {
        $shift = Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'booking_default_shift', null, false);
        return response()->json([
            'status' => 'success',
            'shift_id' => $shift
        ]);
    }

    public function apiRemoveStaffBookingShift(){
        $result = $this->service->removeStaffBookingShift(request()->get('staff_id'));
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message'],
        ], $result['status'] == 'success' ? 200 : 400);
    }

    public function apiAssignStaffBookingShift(){
        $validatedData = request()->validate([
            'staff_id' => 'required|array',
            'staff_id.*' => 'required|integer|exists:currentSite.staffs,id',
            'shift_id' => 'required|integer|exists:currentSite.shifts,id',
        ]);
        
        $result = $this->service->assignStaffBookingShift($validatedData['staff_id'], $validatedData['shift_id']);
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message'],
            'attached_count' => $result['attached_count'] ?? 0,
        ], $result['status'] == 'success' ? 200 : 400);
    }
    
}
