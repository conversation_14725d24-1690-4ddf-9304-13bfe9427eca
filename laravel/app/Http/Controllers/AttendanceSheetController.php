<?php

namespace App\Http\Controllers;

use App\Exceptions\AttendanceCalculationNoEmployeesException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\PageAccessNotAllowedException;
use App\Facades\Formatter;
use App\Requests\DefaultRequest;
use App\Rules\ActiveBranchRule;
use App\Services\AttendanceLogAutoCalcService;
use App\Services\AttendanceSheetService;
use App\Services\ProductService;
use App\Services\RoleService;
use App\Services\StaffService;
use App\Services\Traits\BulkDeleteController;
use App\Utils\AttendanceSheetStatusTypesUtil;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Repository\EntityViewsLogsRepository;

class AttendanceSheetController extends BaseController
{
    use BulkDeleteController;

    public $folder = 'attendance_sheets';
    protected $service;
    protected $label = 'Attendance Sheet';

    function __construct(AttendanceSheetService $service, StaffService $staffService)
    {
        parent::__construct($service);
    }

    /**
     * {@inheritDoc}
     */
    public function getFormValidationRules(Request $request, $id=null)
    {
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        $parentValidations = parent::getFormValidationRules($request, $id);
        $three_months_from_now = Carbon::now()->addMonths(3);

        $messages = [
            'to_date.after_or_equal' => __t('To date must be greater than from date'),
            'to_date.before'         => __t('To date shouldn\'t exceed 3 months from now')
        ];
        $rules = [
            'from_date' => 'required|date_format:'.$dateFormats[$dateFormatIndex],
            'to_date' => 'required|date_format:'.$dateFormats[$dateFormatIndex].'|after_or_equal:from_date|before:'.$three_months_from_now,
            'branch_id' => ['nullable', new ActiveBranchRule()]
        ];

        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    /**
     * @param $attendanceSheetId
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     * toggels attendance sheet status
     */
    public function changeStatus($attendanceSheetId)
    {
        try {
            $result = $this->service->toggleStatus($attendanceSheetId);
            if (isApi()) {
                return response()->json(['data' => $result], 200);
            } else {
                return redirect()->back()
                    ->with('success', sprintf(__t('%s Updated Successfully'), __t($this->label)));
            }
        } catch (\Exception $e) {
            if (isApi()) {
                return response()->json(['message' => __t($e->getMessage())], 400);
            } else {
                return redirect()->back()
                    ->with('danger', $e->getMessage());
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    public function store(Request $request)
    {
        $validatedData = $this->validateFormData($request);
        $storeRequest = new DefaultRequest($validatedData->input());
        try {
            $redirectParameters = $this->service->add($storeRequest);
        } catch (AttendanceCalculationNoEmployeesException $e) {
            return redirect()
                ->route('owner.attendance_sheets.create')
                ->withInput(request()->all())
                ->with('danger', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e->getMessage(), ['exception' => $e]);
            return redirect()
                ->route('owner.attendance_sheets.create')
                ->withInput(request()->all())
                ->with('danger', __t('Attendance sheet calculation failed'));
        }
        return redirect()
            ->route('owner.attendance_sheets.index', $redirectParameters);

    }

    public function changeMultiStatus($status,Request $request)
    {
        $defaultRequest = new DefaultRequest($request->all());
        $itemIds = $this->service->mapMultiActions($defaultRequest);
        if(!$itemIds)
        {
            return redirect()
                ->route('owner.attendance_sheets.index')
                ->with('danger', sprintf(__t('No %s Selected'), __t($this->label)));
        }
        if(!in_array($status, AttendanceSheetStatusTypesUtil::getStatuses()))
        {
            return redirect()
                ->route('owner.attendance_sheets.index')
                ->with('danger', sprintf(__t('%s Updating Failed'), __t($this->label)));
        }
        if($status == AttendanceSheetStatusTypesUtil::PENDING){
            $result = $this->service->bulkUndoApproval($itemIds);
            $result['back_url'] = route('owner.attendance_sheets.index');
            return new \App\IzamViews\IzamView('attendance/attendance_sheets/bulk', $result);
        }
        $this->service->updateStatusMulti($status, $itemIds);
        return redirect()
            ->route('owner.attendance_sheets.index')
            ->with('success', sprintf(__t('%s Updated Successfully'), __t('Attendance Sheets')));
    }

    public function destroyMulti(Request $request)
    {
        try{
            if($request['ids'] == 'none'){
                return redirect()->route($this->routesPrefix . $this->folder . '.index')
                    ->with('danger', sprintf(__t('No %s selected'), __t('Attendance Sheet')));
            } elseif ($request['ids'] == 'all'){
                $allData = $this->service->getFilteredRecords()->pluck('id')->toArray();
                $result = $this->service->deleteMany($allData);
            } else {
                $ids = explode(',', $request['ids']);
                $result = $this->service->deleteMany($ids);
            }
        } catch (\Exception $e) {
            if (isApi()) {
                return response()->json(['message' => __t($e->getMessage())], 400);
            } else {
                return redirect()->back()
                    ->with('danger', $e->getMessage());
            }
        }

        if ($result['deletedCount'] > 0 && $result['unDeletedCount'] > 0){
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Attendance Sheets')))
                ->with('danger', sprintf(__t('(%d) %s cannot be deleted as they are already in use'), $result['unDeletedCount'], __t('Attendance Sheets')));
        } elseif ($result['deletedCount'] > 0 && $result['unDeletedCount'] == 0){
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Attendance Sheets')));
        } elseif ($result['deletedCount'] == 0 && $result['unDeletedCount'] > 0){
            return redirect()->route($this->routesPrefix . $this->folder . '.index')
                ->with('danger', sprintf(__t('(%d) %s cannot be deleted as they are already approved'), $result['unDeletedCount'], __t('Attendance Sheets')));
        }
    }

    public function show($id)
    {
        try {
            $data = $this->service->getViewData($id);
            $navData = $this->service->getViewNavData($data['record']);
            $currentStaffId = getAuthOwner('staff_id');
            if($currentStaffId){
                EntityViewsLogsRepository::insertIfNotExist(EntityKeyTypesUtil::ATTENDANCE_SHEET, $id, $currentStaffId);
            }
            if (isApi()) {
                return response()->json(['data' => $data['record']->toArray()], 200);
            } else {
                if(request()->isXmlHttpRequest())
                {
                    return view('partials.listing.grids.attendance_sheet',$data);
                }
                $blade = $this->blade ?? 'show';
                return view(
                    $this->folder . $this->viewsPrefix . '.' . $blade,
                    array_merge($data, ['query' => $this->service->parameters, 'navData' => $navData])
                );
            }
        } catch (EntityNotFoundException $exception) {
            if (isApi()) {
                return response()->json(['message' => __t('This Record Doesn\'t Exist')], 404);
            } else {
                return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                    ->with('danger', sprintf(__t('The %s does not exist or has been deleted'), __t(strtolower($this->label))));
            }
        } catch (\App\Exceptions\NotAccessibleStaffBranchException $exception){
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                ->with('danger', $exception->getMessage());
        } catch (PageAccessNotAllowedException $exception){
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)->with('danger', $exception->getMessage());
        }
    }

    public function index(Request $request)
    {
        $AttendanceLogAutoCalcService = resolve(AttendanceLogAutoCalcService::class);
        $uncalculatedLogs = $AttendanceLogAutoCalcService->getCalculateAttendanceMessage(Route::current()->action['as']);
        if($uncalculatedLogs['found']) {
            session()->flash('warning', $uncalculatedLogs['message']);
        }
        return parent::index($request);
    }

}
