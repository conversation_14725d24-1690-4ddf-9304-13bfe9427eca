<?php

namespace App\Http\Controllers;

use App\Exceptions\ClosedPeriodException;
use App\Exceptions\Payroll\CanNotApproveNegativePayslip;
use App\Exceptions\NoPayslipSelectedException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\PayslipCannotBeEditedException;
use App\Exceptions\UnexpectedBehavior;
use App\Rules\FormattedDateOperation;
use App\Rules\InClosedPeriodRule;
use App\Services\PaySlipService;
use App\Utils\ActiveStatusUtil;
use App\Utils\PermissionUtil;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Repository\EntityViewsLogsRepository;

/**
 * PaySlipController Class pay slip controller
 * @package App\Http\Controllers
 * <AUTHOR> <<EMAIL>>
 */
class PaySlipController extends BaseController
{
    /**
     * @var string folder name
     */
    public $folder = 'payslips';

    /**
     * @var string view prefix
     */
    public $viewsPrefix = 'payslips';

    /**
     * PaySlipController constructor.
     * @param PaySlipService $service payslip service
     */
    public function __construct(PaySlipService $service)
    {
        $this->middleware('checkPermissions:' . implode(',',[
            PermissionUtil::VIEW_HIS_DEPARTMENT_PAYSLIPS,
            PermissionUtil::VIEW_HIS_OWN_PAYSLIPS,
            PermissionUtil::VIEW_PAY_RUN,
        ]))->only(['index', 'show']);
        $this->middleware('checkPermissions:' . PermissionUtil::EDIT_PAYSLIPS . ',' . PermissionUtil::EDIT_HIS_DEPARTMENT_PAYSLIPS)->only(['edit', 'update']);
        $this->middleware('checkPermissions:' . PermissionUtil::DELETE_PAYSLIPS)->only(['delete', 'destroyMulti']);
        $this->middleware('checkPermissions:' . PermissionUtil::CREATE_PAY_RUN)->only(['create', 'store']);
        $this->middleware("checkFreeTrail")->only(["edit", "update"]);
        parent::__construct($service);
    }

    /**
     * Payslips index
     * @param Request
     * @return view
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $listingData = $this->service->listing();
            return view('partials.listing.grids.payslips', $listingData);
        } else {
            $listingData = $this->service->listing();
            return view($this->folder . $this->viewsPrefix . '.index', $listingData);
        }
    }

    public function create($payrun_id = null)
    {
        try {
            $formData = $this->service->getFormData();
            if ($payrun_id) {
                $formData['related_form_data']['payrun_data'] = $this->service->getPayrunFormData($payrun_id);
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];

            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Payslip Controller Exception on Create', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));

        }

        $blade = $this->blade ?? 'create';
        return view($this->folder . $this->viewsPrefix . '.' . $blade, $formData);
    }

    public function edit($id)
    {
        $this->handledExceptions = array_merge($this->handledExceptions, [
            ClosedPeriodException::class,
            PayslipCannotBeEditedException::class,
            EntityNotFoundException::class
        ]);

        try {
            $this->service->validateEditPayslip($id);
        } catch (QueryException $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => $exceptionMessage], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', $exceptionMessage);
            }
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];

            if ($exception instanceof ValidationException) {
                $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            }

            if (!in_array(get_class($exception), $this->handledExceptions)) {
                Log::error('Payslip Controller Exception on Edit', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);
            }

            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => __t($exceptionMessage)], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', __t($exceptionMessage));
            }
        }

        return parent::edit($id);
    }

    /**
     * @param Request $request
     * @return view
     */
    public function destroyMulti(Request $request)
    {
        try{
            if($request['ids'] == 'none'){
                return redirect()->route($this->routesPrefix . $this->folder . '.index')
                    ->with('danger', sprintf(__t('No %s selected'), __t('Pay Slip')));
            } elseif ($request['ids'] == 'all'){
                $allData = $this->service->all(['*'])->pluck('id')->toArray();
                $result = $this->service->deleteMany($allData);
            } else {
                $ids = explode(',', $request['ids']);
                $result = $this->service->deleteMany($ids);
            }

            if ($result['deletedCount'] > 0 && $result['unDeletedCount'] > 0){
                return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                    ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Pay Slip')))
                    ->with('danger', sprintf(__t('(%d) %s cannot be deleted'), $result['unDeletedCount'], __t('Pay Slip')));
            } elseif ($result['deletedCount'] > 0 && $result['unDeletedCount'] == 0){
                return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                    ->with('success', sprintf(__t('(%d) %s deleted successfully'), $result['deletedCount'], __t('Pay Slip')));
            } elseif ($result['deletedCount'] == 0 && $result['unDeletedCount'] > 0){
                return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                    ->with('danger', sprintf(__t('(%d) %s cannot be deleted'), $result['unDeletedCount'], __t('Pay Slip')));
            }
        }catch (NoPayslipSelectedException $exception){
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                ->with('danger',$exception->getMessage());
        }catch (EntityNotFoundException $exception){
            return redirect()->route($this->routesPrefix . $this->folder . '.index', $this->queryParameters)
                ->with('danger',$exception->getMessage());


        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return array
     */
    public function getFormValidationRules(Request $request, $id = null)
    {
        $dateFormats = getDateFormats('std');
        $siteDateFormat = $dateFormats[getCurrentSite('date_format')];

        $parentValidations = parent::getFormValidationRules($request, $id);
        $commonRules = [
            'payslipComponent.earning.basic.salary_component_id' => 'exists:currentSite.salary_components,id,is_basic,1,deleted_at,NULL',
            'payslipComponent.earning.basic.amount' => 'required|numeric',
            'payslipComponent.*.*.salary_component_id' => 'required|exists:currentSite.salary_components,id,status,' . ActiveStatusUtil::ACTIVE . ',deleted_at,NULL',
            'payslipComponent.*.*.amount' => 'required|numeric',
            'payslipComponent.*.*.order' => 'required|numeric',
            'gross_pay' => 'required|numeric',
            'total_deduction' => 'required|numeric',
            'net_pay' => 'required|numeric'
        ];

        if ($id) {
            $rules = [];
        } else {
            $rules = [
                'employee' => 'required|exists:currentSite.staffs,id,active,1,deleted_at,NULL',
                'posting_date' => ['required_without:payrun_id', 'date_format:' . $siteDateFormat, new InClosedPeriodRule],
                'start_date' => ['required_without:payrun_id', 'date_format:' . $siteDateFormat],
                'end_date' => ['required_without:payrun_id', 'date_format:' . $siteDateFormat, new FormattedDateOperation('after', $request->get('start_date'), __t('The Payslip End Date should be greater than the Start Date')),],
                'currency' => 'required_without:payrun_id|exists:currencies,code'
            ];
        }

        $messages = [];
        return ['rules' => $rules + $commonRules + $parentValidations['rules'], 'messages' => $messages + $parentValidations['messages']];
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function approveAll($id)
    {
        try {
            $result = $this->service->approveAll($id);

            return redirect()->route('owner.payruns.show', $id)
                ->with('success', $result ? sprintf(__t('All %s Approved Successfully'), __t(Str::plural($this->label))) : sprintf(__t('All Generated %s Already Approved'), __t(Str::plural($this->label))));

        } catch (QueryException $exception) {
            Log::error("Approve all payslip failed Query exception", ['Exception' => $exception]);
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            if (isApi()) {
                return response()->json(['errors' => $errors, 'message' => sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label))], 400);
            } else {
                return redirect()->back()
                    ->withInput(request()->all())
                    ->withErrors($errors)
                    ->with('danger', sprintf(__t('%s Adding Failed, Please Check Errors Below'), __t($this->label)));
            }
        } catch (CanNotApproveNegativePayslip $exception) {
                return redirect()->back()
                    ->with('danger', $exception->getMessage());
        } catch (EntityNotFoundException $exception) {
            return redirect()->back()
                ->with('danger', $exception->getMessage());
        } catch (UnexpectedBehavior $exception) {
            Log::error("UnexpectedBehavior while approve all payslip", ['Exception' => $exception]);
            return redirect()->back()
                ->with('danger', $exception->getMessage());
        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exceptionMessage));
        } catch (\Exception $exception) {
            Log::error("Approve payslip failed", ['Exception' => $exception]);
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function approve(Request $request)
    {
        try {
            $payslips_ids = is_array($request->id) ? $request->id : [$request->id];
            $result = $this->service->approve($payslips_ids);
            $url = isset($this->service->parameters['tab']) ? back()->getTargetUrl() . '#' . $this->service->parameters['tab'] : back()->getTargetUrl();
            $successCount = $result['success'];
            $failedCount = $result['failed'];
            return redirect($url)
                ->with('success', $successCount ? sprintf(__t("(%s) %s Approved Successfully"), __t($successCount), __t($this->label)) : null)
                ->with('danger', $failedCount ? sprintf(__t("(%s) %s Failed To Approve"), __t($failedCount), __t($this->label)) : null);
        } catch (QueryException $exception) {
            Log::error("Approve payslip failed query exception", ['Exception' => $exception]);
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', sprintf(__t('%s Approving Failed, Please Check Errors Below'), __t($this->label)));
        } catch (UnexpectedBehavior $exception) {
            Log::error("UnexpectedBehavior while approve payslip", ['Exception' => $exception]);
            return redirect()->back()
                ->with('danger', $exception->getMessage());
        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exceptionMessage));
        } catch (\Exception $exception) {
            Log::error("Approve payslip failed", ['Exception' => $exception]);
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exceptionMessage));
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function unApprove(Request $request)
    {
        try {
            $payslips_ids = is_array($request->id) ? $request->id : [$request->id];
            $result = $this->service->unApprove($payslips_ids);
            $url = isset($this->service->parameters['tab']) ? back()->getTargetUrl() . '#' . $this->service->parameters['tab'] : back()->getTargetUrl();
            $successCount = $result['success'];
            $failedCount = $result['failed'];
            return redirect($url)
                ->with('success', $successCount ? sprintf(__t("(%s) %s Un-Approved Successfully"), __t($successCount), __t($this->label)) : null)
                ->with('danger', $failedCount ? sprintf(__t("(%s) %s Failed To Un-Approve"), __t($failedCount), __t($this->label)) : null);
        } catch (QueryException $exception) {
            Log::error("unApprove payslip failed query exception", ['Exception' => $exception]);
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', sprintf(__t('%s Approving Failed, Please Check Errors Below'), __t($this->label)));
        } catch (UnexpectedBehavior $exception) {
            Log::error("UnexpectedBehavior while unApprove payslip", ['Exception' => $exception]);
            return redirect()->back()
                ->with('danger', $exception->getMessage());
        } catch (ValidationException $exception) {
            $exceptionMessage = 'The given data was invalid, Please Check Errors Below';
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exceptionMessage));
        } catch (\Exception $exception) {
            Log::error("unApprove payslip failed", ['Exception' => $exception]);
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];
            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exceptionMessage));
        }

    }

    /**
     * {@inheritDoc}
     */
    public function show($id)
    {
        try{
            $currentStaffId = getAuthOwner('staff_id');
            if($currentStaffId){
                EntityViewsLogsRepository::insertIfNotExist(EntityKeyTypesUtil::PAYSLIP, $id, $currentStaffId);
            }
            return parent::show($id);
        } catch (\Exception $exception) {
            return redirect()->route($this->routesPrefix . $this->folder . '.index')->with('danger', __t($exception->getMessage()));
        }
    }

    public function getLoansPartial(Request $request)
    {
        if (!$request->has('employee_id') || empty($request->input('employee_id'))) {
            return response()->json([
                'status' => false,
                'message' => sprintf(__t('%s is a required field and could not be empty'), __t('Employee'))
            ], 400);
        } else if (!$request->has('currency_code')) {
            return response()->json([
                'status' => false,
                'message' => sprintf(__t('%s is a required field and could not be empty'), __t('Currency Code'))
            ], 400);
        }

        $loansPartialData = $this->service->getLoansForAddManual($request['employee_id'], $request['currency_code']);
        if (!empty($loansPartialData))
            return response()->json([
                'status' => true,
                'content' => View::make('partials.payslips.loans_dev', ['loans' => $loansPartialData])->render()
            ]);
        else
            return response()->json(['status' => false, 'content' => null]);
    }

    public function getCommissionsPartial(Request $request)
    {
        if (!$request->has('employee_id')) {
            return response()->json([
                'status' => false,
                'message' => sprintf(__t('%s is a required field and could not be empty'), __t('Employee'))
            ], 400);
        } else if (!$request->has('currency_code')) {
            return response()->json([
                'status' => false,
                'message' => sprintf(__t('%s is a required field and could not be empty'), __t('Currency Code'))
            ], 400);
        }

        $commissionSheetsOptions = $this->service->getCommissionsForAddManual($request['employee_id'], $request['currency_code']);
        if ($commissionSheetsOptions)
            return response()->json([
                'status' => true,
                'content' => View::make('partials.payslips.commissions_dev', [
                    'commissionSheetsOptions' => $commissionSheetsOptions,
                    'selectedSheetsIDs' => []
                ])->render()
            ]);
        else
            return response()->json(['status' => false, 'content' => null]);
    }

    public function clone(int $payslip_id)
    {
        try {
            $formData = $this->service->getCloneFormData($payslip_id);
        } catch (\Exception $exception) {
            $exceptionMessage = $exception->getMessage();
            $errors = method_exists($exception, 'errors') ? $exception->errors() : [];

            if (!in_array(get_class($exception), $this->handledExceptions))
                Log::error('Payslip Controller Exception on Create', ['Message' => $exceptionMessage, 'Errors' => $errors, 'Exception' => $exception]);

            return redirect()->back()
                ->withInput(request()->all())
                ->withErrors($errors)
                ->with('danger', __t($exception->getMessage()));

        }

        $blade = $this->blade ?? 'create';
        return view($this->folder . $this->viewsPrefix . '.' . $blade, $formData);
    }
}
