<?php

namespace App\Http\Controllers;

use App\Forms\Payroll\MudadSettingsForm;
use App\IzamViews\IzamView;
use App\Models\Payrun;
use App\Services\MudadService;
use App\Services\PayRunService;
use App\Services\StaffService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class MudadController extends Controller
{

    public function __construct(protected MudadService $service, protected StaffService $staffService)
    {

    }

    public function settings()
    {
        $form = new MudadSettingsForm();
        new \IzamViewData();
        $data = $this->service->getMudadSalaryComponents();
        $form->setData(['mudad_settings' => $data]);
        \View::share('extraContainerClass', 'container-full');
        if (!empty(session()->get('errors'))) {
            $errors = session('errors')->getMessages();
            $errorsMessages = [];
            foreach ($errors as $key => $value) {
                $keyArray = explode('.', $key);
                $errorIndex = $keyArray[1];
                $errorsMessages[$errorIndex] = ["salary_components" => $value];
            }
            $form->setMessages(['mudad_settings' => $errorsMessages]);
            $oldData = request()->old();
            $form->setData($oldData);
        }
        return new IzamView('mudad/settings', [
            'form' => $form,
            'title_for_layout' => __t('General Settings'),
            '_PageBreadCrumbs' => $form->getBreadCrumbs(),
            'pageHead' => $form->getPageHeader(),
        ]);
    }

    public function updateSettings(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'mudad_settings' => ['required', 'array', 'min:3', 'max:3'],
                'mudad_settings.*.salary_components' => ['required', 'array', 'min:1'],
                'mudad_settings.*.salary_components.*' => ['required', 'numeric', 'exists:currentSite.salary_components,id'],
            ]);

            $validator->after(function ($validator) use ($request) {
                $ids = [];
                foreach ($request->input('mudad_settings', []) as $index => $group) {
                    foreach ($group['salary_components'] ?? [] as $componentId) {
                        if (in_array($componentId, $ids)) {
                            $validator->errors()->add('mudad_settings.' . $index . '.salary_components', sprintf(__t("Salary Component %s already selected and can't  be repeated"), $componentId));
                        } else {
                            $ids[] = $componentId;
                        }
                    }
                }
            });
            $data = $validator->validate();
            $this->service->setMudadSalaryComponents($data['mudad_settings']);
            $this->izamFlashMessage(__t('Mudad Settings Updated Successfully'));
            return redirect()->route('owner.contract.settings');
        } catch (ValidationException $validationException) {
            return redirect()->back()->withInput($request->all())->withErrors($validationException->validator);
        } catch (\Throwable $exception) {
            $this->izamFlashMessage(__t('Something Went Wrong'));
            return redirect()->back()->withInput($request->all())->withErrors($exception->getMessage());
        }
    }


    public function getExtractPayrunForm()
    {
        return new IzamView('mudad/settings', []);
    }

    public function getGeneratePayrunForm($payRunId, PayRunService $payRunService)
    {
        $isSettingSet = $this->service->isMudadSettingsSet();
        if(!$isSettingSet){
            return redirect()->back()->with('danger',
             sprintf(__t('Please complete Mudad mapping in %s to be able to generate a Mudad Pay run.'),   '<a href="'.route('owner.mudad.settings').'"> '.__t('Mudad Settings').'</a>'));
        }

        $hasSaudiEmployees = $payRunService->hasSaudiEmployees($payRunId);
        if(!$hasSaudiEmployees){
            return redirect()->back()->with('danger', __t('No Saudi-based employees found in this payrun.'));
        }

        return view('mudad/generate', ['id' => $payRunId]);

    }

    // mudad form first step save file
    public function saveMudadFile($payRunId, Request $request)
    {
        // Validate the request
        $validator = \Validator::make($request->all(), [
            'file_id' => [
                'required',
                'file',
                'mimes:xlsx',
                'max:10240',
            ]
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $result = $this->service->processMudadFile($payRunId, $request->file('file_id'));

            return new JsonResponse([
                'success' => true,
                'data' => $result->getOriginalJsonData()
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function getMudadStats($payRunId){
        try {

            $result = $this->service->getMudadStats($payRunId);
            return new JsonResponse([
                'success' => true,
                'data' => $result
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
         * Map staff official IDs for Mudad integration
         *
         * This method updates the official IDs for multiple staff members and
         * saves the mapping data in the Mudad configuration for the specified payrun.
         *
         * @param int $payRunId The ID of the payrun to update mapping data for
         * @param array $staffsMappingData Array of staff mapping data. Each element should be an array with:
         *        - 'employee_id' (int): The internal staff/employee ID
         *        - 'official_id' (string): The official ID to be assigned to the staff member
         *
         * @return array The updated Mudad mapping data
         * @throws \Exception If there's an error updating the staff records or mapping data
     */
    public function mapStaffOfficailId($payRunId, Request $request){
        try {
            $validator = \Validator::make($request->all(), [
                'data' => ['nullable', 'array'],
                'data.employee_id' => ['required', 'integer', 'exists:currentSite.staffs,id,deleted_at,NULL'],
                'data.official_id' => ['required', 'string', 'max:255']
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                ], 422);
            }

            $data = $this->service->updateMudadMappingData($payRunId, $request['data']);
            if(!$data){
                return new JsonResponse([
                    'success' => false,
                    'message' => __t('Employee already mapped')
                ], 400);
            }
            return new JsonResponse([
                'success' => true,
                'data' => $data
            ], 200);
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function saveThirdStep($payRunId){
        try {
            $mudadData = $this->service->updateStep($payRunId, 4);
            $this->staffService->updateStaffOfficialIds($mudadData->getEmployeesMapping());

            return new JsonResponse([
                'success' => true,
                'data' => $mudadData->getOriginalJsonData()
            ], 200);
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }


    public function clearMudadFile($payRunId){
        try {
            $this->service->clearMudadFile($payRunId);
            return new JsonResponse([
                'success' => true,
            ], 200);
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function getMudadData($payRunId){
        try {
            $data = $this->service->getMudadData($payRunId);
            return new JsonResponse([
                    'success' => true,
                    'data' => $data
                ], 200);
        } catch (\Exception $e) {

            return response()->json([
                'success' => false,
                'message' =>  $e->getMessage()
            ], 400);
        }
    }

    public function getMudadFileComponents(Request $request , $payrunId)
    {
        try {
            $response = $this->service->getMudadFileComponents($payrunId);
            return response()->json($response);
        }catch (\Throwable $throwable){
            return response()->json([
                'success' => false,
                'message' =>  $throwable->getMessage()
            ] , 500);
        }
    }

    public function updateMudadFileComponents(Request $request , $payrunId)
    {
        try {
            $data = $request->validate([
                'mappings' => [
                    'required',
                    'array',
                    function ($attribute, $value, $fail) {
                        $usedComponents = [];
                        foreach (['earnings', 'deductions'] as $type) {
                            foreach ($value[$type] ?? [] as $index => $group) {
                                foreach ($group['components'] ?? [] as $component) {
                                    if (in_array($component, $usedComponents)) {
                                        throw ValidationException::withMessages([
                                            "mappings.$type.$index.components" => [
                                                "Component ID $component is already used in another section."
                                            ]
                                        ]);
                                    }
                                    $usedComponents[] = $component;
                                }
                            }
                        }
                    }
                ],
                'mappings.earnings' => ['required', 'array'],
                'mappings.earnings.*.name' => ['required', 'string'],
                'mappings.earnings.*.components' => ['required', 'array'],
                'mappings.deductions' => ['required', 'array'],
                'mappings.deductions.*.name' => ['required', 'string'],
                'mappings.deductions.*.components' => ['required', 'array'],
            ]);
            $this->service->updateMudadFileComponents($payrunId , $data);
            return response()->json([
                'success' => true,
                'message' =>  'Mudad File Components Updated Successfully'
            ]);
        } catch (ValidationException $validationException) {
            return response()->json([
                'success' => false,
                'message' =>  $validationException->validator->getMessageBag()
            ] , 405);
        }  catch (\Throwable $throwable){
            return response()->json([
                'success' => false,
                'message' =>  $throwable->getMessage()
            ] , 500);
        }
    }

    public function exportPayrunMudadFile($payrunId)
    {
        try {
            $result =  $this->service->exportPayrunMudadFile($payrunId);
            $updatedSheet = $result['updatedSheet'];
            $fileName = $result['fileName'];
            return response()->streamDownload( function () use ($updatedSheet) {
                $writer = new Xlsx($updatedSheet);
                $writer->save('php://output');
            }, $fileName , [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }


}
