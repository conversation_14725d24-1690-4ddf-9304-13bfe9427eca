<?php

namespace App\Http\Controllers\Sales;

use App\Facades\Permissions;
use App\Facades\Settings;
use App\Http\Controllers\Controller;
use App\Utils\PermissionUtil;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\ButtonServiceErrorHandlerDecorator;
use Izam\Daftra\AppManager\Utils\AppButtonLocationUtil;
use Izam\Daftra\Common\DTO\TabAction;

class SettingsController extends Controller
{
    public function __construct(
        private ButtonServiceErrorHandlerDecorator $buttonService
    ) {
        parent::__construct();
    }

    public function __invoke()
    {
        $staffId = getAuthStaff('id');
        if (
            Permissions::checkPermission(PermissionUtil::INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS, $staffId) ||
            Permissions::checkPermission(PermissionUtil::Invoices_Add_New_Invoices, $staffId)
        ) {
            $cards = $this->buttonService->getCardsByLocationKey(AppButtonLocationUtil::SALES_SETTINGS)->toArray();
        }

        $links = array_merge($this->getStaticLinks(), array_map(function (TabAction $tabAction) {
            return ['title' => $tabAction->getLabel(), 'url'=> $tabAction->getUrl(), 'icon' => $tabAction->getIcon()];
        }, $cards ?? []));

        return view('sales_settings/settings', compact('links'));
    }

    private function getStaticLinks(): array
    {
        $enableEstimateStatus= Settings::getValue(\App\Utils\PluginUtil::InvoicesPlugin, 'enable_estimate_status') && \App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::FollowupPlugin);
        $enableSalesOrderStatus= Settings::getValue(\App\Utils\PluginUtil::InvoicesPlugin, 'enable_sales_order_status') && \App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::FollowupPlugin);
        $enableInvoiceStatus= Settings::getValue(\App\Utils\PluginUtil::InvoicesPlugin, 'enable_invoice_status') && \App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::FollowupPlugin);
        $enableEmployeeMaximumDiscount = Settings::getValue(\App\Utils\PluginUtil::InventoryPlugin, 'enable_maximum_discount', null, false);
        $enableOffersPlugin = \App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::OffersPlugin);
        $webFrontPluginActive = \App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::WebsiteFrontPlugin);
        $ETAPluginActive = \App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::ETA_PLUGIN);
        $KSAPluginActive = \App\Facades\Plugins::pluginActive(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN);
        $jordanianEInvoicePlugin = \App\Facades\Plugins::pluginActive(\Izam\Daftra\Common\Utils\PluginUtil::JORDAN_EINVOICE_PLUGIN);
        $links = [
            [
                'title' => 'General Invoice/Estimate Settings',
                'url' => '/owner/invoices/settings',
                'icon' => 'mdi mdi-file-cog'
            ],
            [
                'title' => 'Invoice/Estimate Layouts',
                'url' => '/owner/invoice_layouts/index',
                'icon' => 'fa fa-file-invoice'
            ]
        ];
        if ($enableInvoiceStatus) {
            $links[] = [
                'title' => 'Invoice Statuses',
                'url' => '/owner/follow_up_statuses/index/2',
                'icon' => 'fa fa-flag'
            ];
        }
        if ($enableEstimateStatus) {
            $links[] = [
                'title' => 'Estimate Statuses',
                'url' => '/owner/follow_up_statuses/index/3',
                'icon' => 'fa fa-flag'
            ];
        }
        if ($enableSalesOrderStatus) {
            $links[] = [
                'title' => 'Sales Order Statuses',
                'url' => '/owner/follow_up_statuses/index/21',
                'icon' => 'fa fa-flag'
            ];
        }
        if ($enableOffersPlugin) {
            $links[] = [
                'title' => 'Offers',
                'url' => '/owner/offers/index',
                'icon' => 'mdi mdi-sale'
            ];
        }
        $links[] = [
            'title' => 'Custom Fields',
            'url' => '/owner/custom_forms/edit_custom_fields/invoices/?redir=0',
            'icon' => 'mdi mdi-auto-fix'
        ];
        if ($enableEmployeeMaximumDiscount) {
            $links[] = [
                'title' => 'Employees Maximum Discounts',
                'url' => route('owner.staff_maximum_discounts.index'),
                'icon' => 'fa fa-user-tag'
            ];
        }

        $authUserID = getAuthOwner('staff_id');
        if (\App\Facades\Permissions::checkPermission(PermissionUtil::Edit_General_Settings,$authUserID)) {
            $links[] = [
                'title' => __t('Shipping Options'),
                'url' => route("owner.shipping_options.index"),
                'icon' => 'fa fa-flag'
            ];
        }

        if ($ETAPluginActive) {
            $links[] = [
                'title' => __t('Electronic Invoice Settings'),
                'url' => route('owner.electronic_invoice_settings'),
                'icon' => 'mdi mdi-receipt'
            ];
        }

        if ($KSAPluginActive) {
            $links[] = [
                'title' => __t('Electronic Invoice Settings'),
                'url' => route('owner.list_ksa_electronic_invoice_settings'),
                'icon' => 'mdi mdi-receipt'
            ];
        }

        if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::Edit_General_Settings, $authUserID)) {
            $links[] = [
                'title' => __t('Order Sources'),
                'url' => route('owner.order_sources.index'),
                'icon' => 'mdi mdi-source-branch'
            ];
        }

        if ($jordanianEInvoicePlugin) {
            $links[] = [
                'title' => __t('Jordanian E-Invoice Settings'),
                'url' => route('owner.jordanian.einvoice.settings.list'),
                'icon' => 'mdi mdi-receipt'
            ];
        }

        return $links;
    }
}
