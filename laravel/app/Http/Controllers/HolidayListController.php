<?php

namespace App\Http\Controllers;

use App\Services\StaffService;
use Carbon\Carbon;
use GuzzleHttp\Client;
use App\Facades\Branch;
use App\Facades\Plugins;
use App\Utils\PluginUtil;
use Illuminate\Http\Request;
use App\Adapters\GuzzleAdapter;
use App\Rules\HolidayListsDate;
use App\Requests\DefaultRequest;
use App\Utils\EntityKeyTypesUtil;
use App\Contracts\HttpCallOptions;
use App\Services\ActivityLogService;
use App\Services\HolidayListService;
use App\Repositories\StaffRepository;
use App\Utils\ActivityLogActionsUtil;
use App\Repositories\BranchRepository;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\HolidayListRepository;
use App\Repositories\Criteria\InArrayCriteria;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use Illuminate\Http\Response;

class HolidayListController extends BaseController
{
    public $folder = 'holiday_lists';
    /** @var HolidayListService */
    protected $service;
    protected $label = 'Holiday Lists';

    function __construct(HolidayListService $service)
    {
        parent::__construct($service);
    }

    public function getFormValidationRules(Request $request, $id=null)
    {
        $parentValidations = parent::getFormValidationRules($request, $id);
        $messages = $parentValidations['messages'];
        $dates = $request->get('date');
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        if ($dates && is_array($dates)) {
            try {
                foreach ($dates as $date) {
                    Carbon::createFromFormat($dateFormats[$dateFormatIndex], $date);
                }
            }catch (\Exception $e) {
                throw new \Exception($messages['date_format']);
            }
        }
        $rules = [
            'name' => 'required|unique:currentSite.holiday_lists,name,' . ($id ? $id : 'NULL') . ',id,deleted_at,NULL',
            'title' => 'required|array|min:1',
            'date' => ['required','array','min:1'],
            'title.*' => 'required',
            'date.*' =>  new HolidayListsDate(),
        ];
        return ['rules' => $rules + $parentValidations['rules'], 'messages' => $messages];
    }

    public function assignEmployees(
        $holiday_list_id,
        HolidayListRepository $holidayListRepository,
        DepartmentRepository $departmentRepository,
        DesignationRepository $designationRepository,
    ) {
        $holidayList = $holidayListRepository->find($holiday_list_id);
        if(!$holidayList) {
            abort(404);
        }
        $activeBranches = [];
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $activeBranches = Branch::getStaffBranches()->toArray();
        }
        $activeDepartments = $departmentRepository->getActiveDepartments()->toArray();
        $activeDesignations = $designationRepository->getActiveDesignations()->toArray();
        $departments = $designations = [];
        foreach($activeDepartments as $department)
            $departments[$department['id']] = $department['name'];

        foreach ($activeDesignations as $designation)
            $designations[$designation['id']] = $designation['name'];


        $_PageBreadCrumbs= [
            ['title' => __t('Holiday Lists'), 'link' => route('owner.holiday_lists.index')],
            ['title' => "{$holidayList->name} #{$holidayList->id}", 'link' => route('owner.holiday_lists.show', ['holiday_list' => $holidayList->id])],
            ['title' => __t('Assign To Employees'), 'link' => route('owner.holiday_lists.index')],
        ];


        return view('assign_employees', [
            'departments' => $departments,
            'designations' => $designations,
            'branches' => $activeBranches,
            'entity' => $holidayList,
            'entity_key' => 'holiday_list',
            'staffOptionsAttributes' => [],
            'generalBreadCrumbs' => $_PageBreadCrumbs,
            'submit_route' => route('owner.holiday_lists.save_assign_employees')
        ]);
    }

    public function saveAssignEmployees(
        Request $request,
        HolidayListRepository $holidayListRepository,
        StaffRepository $staffRepository,
        ActivityLogService $activityLogService,
    ) {
        if(!isset($request['holiday_list_id'])) {
            abort(404);
        }
        $holidayList = $holidayListRepository->find($request['holiday_list_id']);
        if(!$holidayList) {
            abort(404);
        }
        /** @var StaffService $staffService */
        $staffService = resolve(\App\Services\StaffService::class);
        $employee_ids = $staffService->getIdsByMultipleCriteriaSelection(
            $request->get('criteria'), $request->get('employees'),
            $request->get('branches')?? [], $request->get('departments')?? [], $request->get('designations')?? [],
            null, null, $request->get('exclude_criteria')??[]
        );

        $request = $request->all();
        $employeesWithOldLists = $staffRepository->pushCriteria(new InArrayCriteria('id', $employee_ids))->getWithHolidayLists()->toArray();
        $affectedCount = $holidayListRepository->assignToEmployees($request['holiday_list_id'], $employee_ids);
        $employeesWithNewLists = $staffRepository->pushCriteria(new InArrayCriteria('id', $employee_ids))->getWithHolidayLists()->toArray();
        $activityLogRequests = [];
        foreach ($employeesWithOldLists as $employeesWithOldList) {
            $employeeWithNewList = $employeesWithNewLists[$employeesWithOldList['id']];
            if(count($employeeWithNewList['holiday_lists']) == count($employeesWithOldList['holiday_lists'])) {
                continue;
            }
            $activityLogRequests[] = new ActivityLogRequest(
                $employeeWithNewList['id'],
                EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                ActivityLogActionsUtil::UPDATE,
                $employeesWithOldList['name'],
                route('owner.staff.show', ['staff' => $employeeWithNewList['id']]),
                [__t('Holiday List') => implode(',', array_column($employeeWithNewList['holiday_lists'],'name'))],
                [__t('Holiday List') => implode(',', array_column($employeesWithOldList['holiday_lists'], 'name'))],
            );
        }
        foreach ($activityLogRequests as $activityLogRequest) {
            $activityLogService->addActivity($activityLogRequest);
        }
        return redirect(route('owner.holiday_lists.show', ['holiday_list' => $request['holiday_list_id']]))
            ->with('success', sprintf(__t('The Holiday List assigned to (%s) Employee(s) Successfully'), $affectedCount));
    }



    function getPublicHolidays(Request $request)
    {
        $year = date("Y");
        $country = $request->country_code;
        try {
            $client = new Client();
            $response = $client->get('https://www.'. Domain_Short_Name .'/country_holidays/holidays', [
                'query' => [
                    'year' => $year,
                    'country' => $country
                ],
                'verify' => false
            ]);

            $response = json_decode($response->getBody()->getContents(), true);

            if (empty($response['error'])) {
                $input = ['holidaysFromApi' => $response['data']['holidays'],"id" => $request->holiday_list_id, 'language' => 'en'];
                $updateRequest = new DefaultRequest($input);
                $this->service->updateHolidayListFromApi($updateRequest);
            } else {
                \Rollbar\Rollbar::warning('Error in getting  holiday list', [
                    'request' => $request->all(),
                    'response' => $response
                ]);
            }
        } catch (\Throwable $th) {

            \Rollbar\Rollbar::warning($th->getMessage(), [
                'request' => $request->all(),
            ]);
            return redirect(route('owner.holiday_lists.show', ['holiday_list' => $request['holiday_list_id']]))
            ->with('danger', __t('Error in getting  holiday list'));
        }
        return redirect(route('owner.holiday_lists.show', ['holiday_list' => $request['holiday_list_id']]))
        ->with('success', __t('Holiday Lists Updated Successfully'));
    }

    public function authHolidaysApi(Request $request){
        return response()->json(['data' => $this->service->getHolidaysForStaff(getAuthOwner('staff_id'), $request->get('date_from'), $request->get('date_to')), 'status' => Response::HTTP_OK]);
    }
}
