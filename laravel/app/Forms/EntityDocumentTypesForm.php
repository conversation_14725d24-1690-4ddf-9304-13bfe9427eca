<?php

namespace App\Forms;


use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\SalaryComponentOption;
use Izam\Dynamic\List\Repository\DynamicRepo;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\AutoSuggest;
use Izam\View\Form\Element\Stack;
use Izam\View\Service\SizeUtil;
use Laminas\Form\Element\Button;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\TitleText;
use Laminas\Form\Element\Checkbox;
use Laminas\Form\Element\Collection;
use Laminas\Form\Element\Hidden;
use Laminas\Form\Element\Text;
use Laminas\Form\Fieldset;
use Laminas\Form\Form;

class EntityDocumentTypesForm extends Form
{
    public function     __construct()
    {
        parent::__construct();

        $csrf = new Hidden('_token');
        $csrf->setValue(csrf_token());
        $subform = new Collection('entity_document_types_settings');;
        $fi = new Fieldset('fieldset');

        $subform->setShouldCreateTemplate(true);

        $idInput = new Hidden('id');
        $displayOrder = new Hidden('display_order');;

        $nameInput = new Text('name');
        $nameInput->setLabel('Name')
            ->setAttributes([
                "placeholder" => __t('Please Enter Document Type Name'),
                "required" => true,
                "data-form-rules" => "required",
                "data-name-input" => true,
                "unique" => true,
            ])
            ->setOption('classes', "form-control");


        $isRequired = new Checkbox('is_required');
        $isRequired->setLabel(__t('Is Required'))
            ->setOption('hideLabelClassAttribute', true)
            ->setOption('extraClasses', 'm-5')
            ->setLabelAttributes(['class' => 'form-label'])
            ->setOption('size', SizeUtil::MEDIUM);

        $hasExpiryDate = new Checkbox('is_expirable');
        $hasExpiryDate->setLabel(__t('Has Expiry Date'))
            ->setOption('hideLabelClassAttribute', true)
            ->setOption('extraClasses', 'm-5')
            ->setLabelAttributes(['class' => 'form-label'])
            ->setOption('size', SizeUtil::MEDIUM);


        $fi->add($idInput);
        $fi->add($nameInput);
        $fi->add($isRequired);
        $fi->add($hasExpiryDate);
        $fi->add($displayOrder);

        $subform->setTargetElement($fi);

        $this->add($subform);

    }

    public function getBreadCrumbs(): array
    {
        return [
            [
                'title' => __t('Employees Settings'),
                'link' => route('owner.staff.settings')
            ],
            [
                'title' => __t('Documents Management')
            ]
        ];
    }

    public function getPageHeader(): Header
    {
        $pageTitle = new TitleText('page-title');
        $pageTitle->setTitle(__t('Documents Management'));

        $header = new Header('page-header');
        $header->addLeft($pageTitle);

        $rightStack = new Stack('left-stack');
        $rightStack->setOption('theme', 'theme4');

        $saveBtn = new Button('Save');
        $saveBtn->setLabel(__t('Save'))
            ->setOption('icon', 'content-save')
            ->setLabelOption('disable_html_escape', true)
            ->setAttribute('class', 'btn-success')
            ->setAttribute('type', 'submit');

        $cancelAnchor = new Anchor('Cancel');
        $cancelAnchor->setLabel(__t('Cancel'))
            ->setOption('icon', 'close')
            ->setOption('theme', 'secondary')
            ->setAttribute('href', route('owner.staff.settings'));

        $rightStack->add($cancelAnchor)->add($saveBtn);
        $header->addRight($rightStack);

        return $header;
    }

}
