<?php

namespace App\Forms;

use App\Facades\Branch;
use App\Facades\Plugins;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Formatter\Staff;
use Izam\Daftra\Common\Formatter\StaffAvatar;
use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;
use Izam\Entity\Repository\DynamicRepo;
use Izam\View\Form\Element\AutoSuggest;
use Izam\View\Form\Element\DynamicDropdown;
use Izam\View\Form\Element\Select;
use Laminas\Form\Element\Date;
use Laminas\Form\Form;

class LeaveBalanceFilterForm extends Form
{

    public function __construct()
    {
        parent::__construct();

        $this->add([
            'name' => 'name',
            'type' => AutoSuggest::class,

            'attributes' => [
                "placeholder" => __t("Employee"),
                'multiple' => true,
            ],
            'options' => [
                'ajax' => false,
                'auto_suggest_url' => '/v2/owner/staff/search?allow_inactive=1&get_branch_suspended=1&term=d&_type=query&q=__q__',
//                'value_options' => $this->getAllowedValues(),
                "property" => "text",
                "identifier" => "id",
                "target_class" => SmartEmployeeSearchService::class,
                "find_method" => ['name' => 'getFormattedRecordById'],
            ]
        ])
            ->add([
                'name' => 'from_date',
                'type' => Date::class,

                'attributes' => [
                    'required' => 'required',
                    "placeholder" => __t("From Date"),
                ],

            ])
            ->add([
                'name' => 'to_date',
                'type' => Date::class,

                'attributes' => [
                    'required' => 'required',
                    "placeholder" => __t("To Date"),
                ],

            ])
            ->add([
                'name' => 'shift',
                'type' => DynamicDropdown::class,

                'attributes' => [
                    "placeholder" => __t("Shift"),

                ],
                'options' => [
                    'label' => __t('Shift'),
                    'ajax' => false,
                    'empty_option' => __t('Select') . ' ' . __t('Shift'),
                    'target_class' => DynamicRepo::class,
                    'find_method' => ['name' => 'getByEntityKey', 'params' => ["shift"]]

                ]

            ])
            ->add([
                'name' => 'designation',
                'type' => DynamicDropdown::class,

                'attributes' => [
                    "placeholder" => __t("Designation"),
                ],
                'options' => [
                    'label' => __t('Designation'),
                    'ajax' => false,
                    'empty_option' => __t('Select') . ' ' . __t('Designation'),
                    'target_class' => DynamicRepo::class,
                    'find_method' => ['name' => 'getByEntityKey', 'params' => ["designation"]]

                ]

            ])
            ->add([
                'name' => 'employment_type',
                'type' => DynamicDropdown::class,

                'attributes' => [
                    "placeholder" => __t("Employment Type"),
                ],
                'options' => [
                    'label' => __t('Employment Type'),
                    'ajax' => false,
                    'empty_option' => __t('Select') . ' ' . __t('Employment Type'),
                    'target_class' => DynamicRepo::class,
                    'find_method' => ['name' => 'getByEntityKey', 'params' => ["employment_type"]]

                ]

            ])
            ->add([
                'name' => 'employment_level',
                'type' => DynamicDropdown::class,

                'attributes' => [
                    "placeholder" => __t("Employment Level"),
                ],
                'options' => [
                    'label' => __t('Employment Level'),
                    'ajax' => false,
                    'empty_option' => __t('Select') . ' ' . __t('Employment Level'),
                    'target_class' => DynamicRepo::class,
                    'find_method' => ['name' => 'getByEntityKey', 'params' => ["employment_level"]]

                ]

            ])
            ->add([
                'name' => 'status',
                'type' => Select::class,

                'attributes' => [
                    "placeholder" => __t("Employee Status"),
                ],
                'options' => [
                    'label' => __t('Employee Status'),
                    'empty_option' => __t('Select') . ' ' . __t('Employee Status'),
                    'options' => ['1' => __t('Active'), '0' => __t('In-active')],

                ]

            ])
            ->add([
                'name' => 'department',
                'type' => DynamicDropdown::class,

                'attributes' => [
                    "placeholder" => __t("Department"),
                ],
                'options' => [
                    'label' => __t('Department'),
                    'ajax' => false,
                    'empty_option' => __t('Select') . ' ' . __t('Department'),
                    'target_class' => DynamicRepo::class,
                    'find_method' => ['name' => 'getByEntityKey', 'params' => ["department"]]

                ]

            ]);

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $branches = Branch::getStaffBranchesSuspended()->toArray();
            $this->add([
                'name' => 'branch',
                'type' => DynamicDropdown::class,

                'attributes' => [
                    "placeholder" => __t("Branch"),
                ],
                'options' => [
                    'label' => __t('Branch'),
                    'empty_option' => __t('Select') . ' ' .__t( 'Branch'),
                    'options' => $branches,

                ]

            ]);
        }

    }
}
