<?php

namespace App\Forms\Payroll;


use Izam\View\Form\Element\Card;

class CardListFactory
{
    public static function make(array $cards): CardList
    {
        $cardList = new CardList();
        foreach ($cards as $card) {
            $cardElement = new Card($card['label']);
            $cardElement->setLabel($card['label'])
                ->setAttribute('href', $card['href'])
                ->setOption('icon', $card['icon'])
                ->setOption('customIcon', $card['customIcon'] ?? null);
            $cardList->add($cardElement);
        }
        return $cardList;
    }
}

