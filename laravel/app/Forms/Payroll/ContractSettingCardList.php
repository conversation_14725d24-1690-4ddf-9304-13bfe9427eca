<?php

namespace App\Forms\Payroll;

use App\Facades\Plugins;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Entity\Helper\EntityHasLegacyCustomFields;
use Izam\Template\Utils\EmailTemplatesGroupUtil;
use Izam\Template\Utils\PrintableTemplatesGroupUtil;

class ContractSettingCardList extends CardList
{

    public static function getCardList(): CardList
    {
        return CardListFactory::make(static::getCards());
    }

    private static function getCards(): array
    {
        $customFieldsUrl = '/v2/owner/local_entities/custom_data/contract/create?redirect=/v2/owner/payroll_settings';
        if (EntityHasLegacyCustomFields::check('contracts')) {
            $customFieldsUrl = '/owner/custom_forms/edit_custom_fields/contracts?redirect=0';
        }

        $links = [
            [
                'label' => __t('General Settings'),
                'href' => route('owner.payroll.settings'),
                'icon' => 'cog',
            ],
            [
                'label' => __t('Contract Custom Fields'),
                'href' => $customFieldsUrl,
                'icon' => 'cog',
            ],
            [
                'label' => __t('Contract Notifications'),
                'href' => '/v2/owner/payroll_settings/notifications',
                'icon' => 'bell-ring-outline',
            ],
            [
                'label' => __t('Related Forms'),
                'href' => route('owner.local_entities.index', ['parent_entity' => EntityKeyTypesUtil::CONTRACT]),
                'icon' => 'pencil-ruler',
            ],
            [
                'label' => __t('Printable Templates'),
                'href' => route('owner.manage_printable_templates_for_group', PrintableTemplatesGroupUtil::PAYROLL_TYPE),
                'icon' => 'receipt-text',
            ],
            [
                'label' => __t('Email Templates'),
                'href' => route('owner.manage_email_templates_for_group', EmailTemplatesGroupUtil::PAYROLL_TYPE),
                'icon' => 'mdi mdi-email',
            ],
        ];
        if (Plugins::pluginActive(PluginUtil::COMMISSION_PLUGIN)) {
            $links[] = [
                'label' => __t('Commissions Settings'),
                'href' => '/v2/owner/commissions/settings',
                'icon' => 'cash-multiple'
            ];
        }
        if ( Plugins::pluginActive(PluginUtil::MUDAD_PLUGIN) ) {
            $links[] = [
                'label' => __t('Mudad Settings'),
                'href' => route('owner.mudad.settings'),
                'icon' => '',
                'customIcon'=> '<img src="/img/mudad.png" style="width:50px;height:50px" alt="Mudad" class="" />'
            ];
        }
        return $links;
    }
}
