<?php

namespace App\Forms\Payroll;


use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\SalaryComponentOption;
use Izam\Dynamic\List\Repository\DynamicRepo;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\AutoSuggest;
use Izam\View\Form\Element\Stack;
use Laminas\Form\Element\Button;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\TitleText;
use Laminas\Form\Element\Collection;
use Laminas\Form\Element\Hidden;
use Laminas\Form\Element\Text;
use Laminas\Form\Fieldset;
use Laminas\Form\Form;

class MudadSettingsForm extends Form
{
    public function     __construct()
    {
        parent::__construct();

        $csrf = new Hidden('_token');
        $csrf->setValue(csrf_token());
        $subform = new Collection('mudad_settings');
        $subform->setOption('tfoot', '<tfoot></tfoot>');
        $fi = new Fieldset('fieldset');

        $subform->setShouldCreateTemplate(true);
        $subform->setOption('hide_drag', true);
        $subform->setOption('hide_delete', true);
        $fi->setOption('hide_delete', true);
        $fi->setOption('hide_drag', true);


        $nameInput = new Text('name');
        $nameInput->setLabel('Mudad Standard Fields')
            ->setAttribute('readonly', true)
            ->setAttribute('data-unsettled-amount', true)
            ->setOption('classes', "form-control bg-dimmed-2");


        $this->autoSuggestInput = new AutoSuggest('salary_components');
        $this->autoSuggestInput->setOptions([
            "identifier" => "id",
            "property" => "name",
            "allow_beside" => true,
            "multiple" => true,
            'label' => __t('Daftra Salary Components'),
            'entityKey' => 'salary_component',
            "target_class" => DynamicRepo::class,
            "find_method" => ['name' => 'getByEntityKeyWithLimit', 'params' => [EntityKeyTypesUtil::SALARY_COMPONENT,[ 'status'=> 1 , 'is_excluded' => 0 ], 50, "desc"]],
            "template" => SalaryComponentOption::class,
        ])->setAttributes([
            "required" => true,
            "multiple" => true,
            "data-form-rules" => "required",
            'placeholder' => __t('Select Salary Components'),
            "data-select-input" => 'components',
            "unique" => true,
        ]);


        $fi->add($nameInput);
        $fi->add($this->autoSuggestInput);

        $subform->setTargetElement($fi);

        $this->add($subform);

    }

    public function getBreadCrumbs(): array
    {
        return [
            [
                'title' => __t('Payroll Settings'),
                'link' => route('owner.contract.settings')
            ],
            [
                'title' => __t('Mudad Settings')
            ]
        ];
    }

    public function getPageHeader(): Header
    {
        $pageTitle = new TitleText('page-title');
        $pageTitle->setTitle(__t('Mudad Settings'));
        $pageTitle->setSubtitle(__t("Select which Daftra salary components should be mapped to each Mudad fields"));

        $header = new Header('page-header');
        $header->addLeft($pageTitle);

        $rightStack = new Stack('left-stack');
        $rightStack->setOption('theme', 'theme4');

        $saveBtn = new Button('Save');
        $saveBtn->setLabel(__t('Save'))
            ->setOption('icon', 'content-save')
            ->setLabelOption('disable_html_escape', true)
            ->setAttribute('class', 'btn-success')
            ->setAttribute('type', 'submit');

        $cancelAnchor = new Anchor('Cancel');
        $cancelAnchor->setLabel(__t('Cancel'))
            ->setOption('icon', 'close')
            ->setOption('theme', 'secondary')
            ->setAttribute('href', route('owner.contract.settings'));

        $rightStack->add($cancelAnchor)->add($saveBtn);
        $header->addRight($rightStack);

        return $header;
    }

}
