<?php

namespace App\Exceptions;

class DefaultBookingShiftException extends \Exception
{
    public function __construct(string $message = "", int $code = 0, \Throwable $previous = null)
    {
        $message = sprintf(__t('You cannot delete the default booking shift. Go to %s to change it first'), '<a href="'.route('owner.booking_settings.index').'">'.__t('Booking Settings').'</a>');
        parent::__construct($message, $code, $previous);
    }
}
