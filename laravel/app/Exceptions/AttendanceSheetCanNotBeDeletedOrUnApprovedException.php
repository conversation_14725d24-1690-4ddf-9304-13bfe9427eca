<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AttendanceSheetCanNotBeDeletedOrUnApprovedException
 * @package App\Exceptions
 * <AUTHOR> <<EMAIL>>
 */
class AttendanceSheetCanNotBeDeletedOrUnApprovedException extends Exception
{
    /**
     * AttendanceSheetCanNotBeDeletedOrUnApprovedException constructor.
     * @param Model $paySlip payslip
     */
    public function __construct($paySlip)
    {
        $paySlipLink = "<a class='alert-link' href='".route('owner.payslips.show', $paySlip->id)."'><span>". " #". $paySlip->id."</span> </a>";
        $message = __t("You cannot unapprove an attendance sheet linked to Payslip") . " " . $paySlipLink;
        parent::__construct($message);
    }
}
