<?php

namespace App\Exceptions;

class ServiceFormHasLeCustomDataForDelete extends \Exception
{
    public function __construct($name = "", $url = "", $code = 0, $previous = null)
    {
        parent::__construct($name, $code, $previous);
        $this->message = sprintf(__t("You cannot delete the service form as there is a related record from the service in booking %s."),
            "<a href='{$url}'>{$name}</a>");
    }
}
