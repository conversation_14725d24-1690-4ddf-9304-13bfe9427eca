<?php

namespace App\Services;

use App\Facades\Formatter;
use App\Modules\LocalEntity\Repositories\LocalEntityRepository;
use App\Repositories\ActivityLogRepository;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Criteria\JoinCriteria;
use App\Repositories\EntityRepository;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Requests\DefaultRequest;
use App\Responses\ArrayChangeResponse;
use App\Utils\ActionLineMainOperationTypesUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\PluginUtil;
use Closure;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * ActivityLogService Class Activity Log service deals with Activity Log repository @see ActivityLogRepository
 * @package App\Services
 * <AUTHOR> <<EMAIL>>
 */
class ActivityLogService extends BaseService
{
    /**
     * @var ActivityLogRelationService  activity log relation service
     */
    public $activityLogRelationService;

    /**
     * @var array $exceptKeys except keys that won't be logged
     */
    protected $exceptKeys = ['created_at', 'updated_at', 'deleted_at', 'modified', 'created', 'deleted_at', 'password', 'id'];

    /**
     * @var array $replacedLabels label keys that will be logged with different label
     */
    protected $replacedLabels = [];

    /**
     * @var array $defaultValuesIfEmpty values will be set for a key in case of it's not set
     */
    protected $defaultValuesIfEmpty = [];

    /**
     * @var array associative array for entities data
     */
    protected $cachedEntities = [];

    private $localEntityRepo;

    /** @var ActivityLogRepository */
    public $repo;

    /**
     * @var EntityRepository
     */
    protected $entityRepository;


    /**
     * ActivityLogService constructor.
     * @param ActivityLogRepository      $repo                       Activity Log repository
     * @param ActivityLogRelationService $activityLogRelationService Activity Log relation service
     * @param EntityRepository           $entityRepository           entity field repository
     */
    public function __construct(
        ActivityLogRepository $repo,
        ActivityLogRelationService $activityLogRelationService,
        EntityRepository $entityRepository,
        LocalEntityRepository $localEntityRepository
    ) {
        $this->activityLogRelationService = $activityLogRelationService;
        $this->localEntityRepo = $localEntityRepository;
        parent::__construct($repo, $entityRepository);
    }

    function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $data = $request->except($excludedFields);
        $result = $this->repo->add($data);
        return $result;
    }

    /**
     * log an insertion activity on the system.
     * note: set old record with null if you want to add new record only
     * @param ActivityLogRequest $activityLogRequest          activity log request
     * @param bool               $applyActivityLogUTCTimeZone apply activity log utc timezone
     */
    public function addActivity(ActivityLogRequest $activityLogRequest, bool $applyActivityLogUTCTimeZone = false)
    {
        $data = $this->prepareActivityLogRequest($activityLogRequest, $applyActivityLogUTCTimeZone);
        if (isset($data['activity_log'])) {
            $activityLogRecordId = $this->repo->insertGetId($data['activity_log']);
            foreach ($data['relations'] as &$relation) {
                $relation['activity_log_id'] = $activityLogRecordId;
            }
            $this->activityLogRelationService->repo->bulkInsert($data['relations']);
        }
    }

    /**
     * log multiple activity log on the system.
     * @param array $activityLogRequests          activity log request
     * @param bool               $applyActivityLogUTCTimeZone apply activity log utc timezone
     */
    public function addMultipleActivityLogs(array $activityLogRequests, bool $applyActivityLogUTCTimeZone = false)
    {
        $activityLogs = [];
        $relations = [];
        foreach ($activityLogRequests as $activityLogRequest) {
            if (!($activityLogRequest instanceof ActivityLogRequest)) {
                continue;
            }
            $data = $this->prepareActivityLogRequest($activityLogRequest, $applyActivityLogUTCTimeZone);
            if (isset($data['activity_log'])) {
                $activityLogs[] = $data['activity_log'];
                $relations = array_merge($relations, $data['relations']);
            }
        }
        if ($activityLogs) {
            $uuid_id_dict = $this->repo->bulkInsertAndGetIds($activityLogs);
            foreach ($relations as &$relation) {
                $relation['activity_log_id'] = $uuid_id_dict[$relation['activity_log_id']];
            }
            $this->activityLogRelationService->repo->bulkInsert($relations);
        }
    }

    public function prepareActivityLogRequest(ActivityLogRequest $activityLogRequest, bool $applyActivityLogUTCTimeZone = false)
    {
        $activityLog = null;
        $relations = [];
        // get main relation which contains entity key
        $mainRelation = $activityLogRequest->getMainActivityLogRelation();
        if ($mainRelation) {
            $activityLogRequest->setIP(get_real_ip());
            $entityKey = $mainRelation->getEntityKey();
            if (!isset($this->cachedEntities[$entityKey])) {
                $entity = $this->entityRepository->find($entityKey);
                if(!$entity) {
                    $entity = $this->localEntityRepo->find($entityKey);
                }
                $this->cachedEntities[$entityKey] = $entity;
            } else {
                $entity = $this->cachedEntities[$entityKey];
            }
            // don't log if you didn't find the entity.
            if ($entity) {
                $actionType = $activityLogRequest->getActionType();
                $entityFields = $entity->fields;
                $data = $this->constructActivityData($activityLogRequest);
                // remove all un-wanted general keys will not be logged
                $data = $this->removeDataKeys($data);
                $data = $this->changeKeysWithEntityLabelsAndFormatValue($entityFields, $data, $applyActivityLogUTCTimeZone);
                // do nothing if there's nothing to update
                if ($actionType == ActionLineMainOperationTypesUtil::UPDATE_ACTION && empty($data)) {
                    return;
                }
                $data = json_encode($data);
                // change data key from oldVal to newVal in case of deleting the entity.
                if ($actionType == ActionLineMainOperationTypesUtil::DELETE_ACTION) {
                    $data = str_replace('newVal', 'oldVal', $data);
                }
                // log in case of add action or there's data to be logged.
                /** Handling Default Request */
                $request = new DefaultRequest();
                $authenticatedUser = getAuthOwner();
                $action = $actionType . ' ' . ($activityLogRequest->getReplacedEntityLabel() ?? $entity->label);
                if (ActionLineMainOperationTypesUtil::isOneOfUpdateActions($actionType)) {
                    $actionType = ActionLineMainOperationTypesUtil::UPDATE_ACTION;
                }
                $activityLog = [
                    'id' => $activityLogRequest->getId(),
                    'label' => $activityLogRequest->getLabel(),
                    'data' => $data,
                    'uuid' => $activityLogRequest->getUuid(),
                    // action will be generated by adding the action type to entity label
                    // ex: if action_type is `added` and entity label is `Designation` so the action will be `Designation added`
                    'action' => $action,
                    'action_type' => $actionType,
                    'url' => $activityLogRequest->getUrl() ? $activityLogRequest->getUrl() : null,
                    'actor_type' => $authenticatedUser['activity_log_actor_type'] ?? '',
                    'actor_id' => $authenticatedUser['staff_id'],
                    'ip' => $activityLogRequest->getIP(),
                    'branch_id' => $activityLogRequest->getBranch()
                ];
                foreach ($activityLogRequest->getRelations() as $relation) {
                    /**
                     * expected relation to have keys called entity_id and entity_key then append activity_log_id key
                     */
                    $relation->setActivityLogId($activityLogRequest->getUuid());
                    /** Handling Default Request */
                    $relations[]  = $relation->toArray();
                }
            }
        }
        return ['activity_log' => $activityLog, 'relations' => $relations];
    }

    private function ignoredType($type) {
        $ignoredTypes = [EntityFieldUtil::ENTITY_FIELD_TYPE_FILE, EntityFieldUtil::ENTITY_FIELD_TYPE_PHOTO];
        return in_array($type, $ignoredTypes);
    }

    /**
     * change keys with entity labels and fromat value if necessary
     * @param Collection $entityFields                 entity field
     * @param array      $data                         data
     * @param bool       $applyActivityLogUTCTimeZone  will apply activity log utc timezone or not
     * @return array
     */
    public function changeKeysWithEntityLabelsAndFormatValue(
        Collection $entityFields,
        array $data,
        bool $applyActivityLogUTCTimeZone = false
    ) : array {
        $dataKeys = array_column($data, 'key');
        foreach ($entityFields as $field) {
            $key = $field['db_name'];
            $value = $this->replacedLabels[$key] ?? $field['label'];
            $replacedKeyIndex = array_search($key, $dataKeys);
            if (!is_bool($replacedKeyIndex)) {
                $fieldType = $field['field_type'];
                // to refer to date with date formatter in case of date picker
                $fieldType = ($fieldType == \App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_PICKER) ? \App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE : $fieldType;
                $data[$replacedKeyIndex]['key'] = $value;
                $data[$replacedKeyIndex]['field_type'] = $fieldType;
                if (isset($data[$replacedKeyIndex]['oldVal'])) {
                    if (!$this->ignoredType($fieldType)) {
                        $data[$replacedKeyIndex]['oldVal'] = (string)Formatter::formatForView(
                            $data[$replacedKeyIndex]['oldVal'],
                            $fieldType,
                            $field,
                            ['applyTranslation' => false]
                        );
                    }
                }
                if (!$this->ignoredType($fieldType)) {
                    $data[$replacedKeyIndex]['newVal'] = (string)Formatter::formatForView(
                        $data[$replacedKeyIndex]['newVal'],
                        $fieldType,
                        $field,
                        ['applyTranslation' => false]
                    );
                }
                if (
                    ($field['field_type'] == EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER ||
                        $field['field_type'] == EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC) &&
                    $applyActivityLogUTCTimeZone
                ) {
                    $data[$replacedKeyIndex]['utc_timezone'] = true;
                }
            }
        }
        return array_values($data);
    }

    /**
     * append key to the array of keys that will not be inserted into activity log
     * @param string $key database key
     * @return $this
     */
    public function appendKeyToExceptKeys(string $key)
    {
        $this->exceptKeys[] = $key;
        return $this;
    }

    /**
     * append key to the array of keys that will be inserted into activity log with different label
     * @param string $key           database key
     * @param string $replacedLabel replaced label
     * @return $this
     */
    public function appendKeyToReplacedLabels(string $key, string $replacedLabel)
    {
        $this->replacedLabels[$key] = $replacedLabel;
        return $this;
    }

    /**
     * append key to the array of default values that will be inserted into activity log with different (sent) value
     * @param string $key   database key
     * @param string $value default Value
     * @return $this
     */
    public function appendValueToDefaultValuesIfEmpty(string $key, string $value)
    {
        $this->defaultValuesIfEmpty[$key] = $value;
        return $this;
    }

    /**
     * remove data keys that exists in except keys array @see ActivityLogService::$exceptKeys
     * @param array $data data will remove their keys
     * @return array
     */
    public function removeDataKeys(array $data) : array
    {
        $dataKeys = array_column($data, 'key');
        foreach ($this->exceptKeys as $key) {
            $replacedKeyIndex = array_search($key, $dataKeys);
            if (!is_bool($replacedKeyIndex)) {
                unset($data[$replacedKeyIndex]);
            }
        }
        return array_values($data);
    }

    /**
     * get activity logs by criteria
     * @param array  $criteria   criteria will be searched with.
     * @param int    $pageNumber pagination page number
     * @param string $sort       sort ascending or descending
     * @return mixed
     */
    public function getActivityLogs(array $criteria, int $pageNumber, string $sort)
    {
        return $this->repo->model->getActivityLogs($criteria, $pageNumber, $sort);
    }

    public function getActivityLogsByIds(array $ids){
        $columns = ['activity_log.*', 'activity_log_relation.*'];
        $joins = [
            [
                'table' => 'activity_log_relation',
                'conditions' => [
                    ['activity_log.id', '=', 'activity_log_relation.activity_log_id']
                ]
            ]
        ];
        if (ifPluginActive(PluginUtil::BranchesPlugin)) {
            $columns[] = 'branch.name as branch_name';
            $joins[] = [
                'table' => 'branches',
                'custom_alias' => 'branch',
                'conditions' => [
                    ['activity_log.branch_id', '=', 'branch.id']
                ]
            ];
        }
        return $this->repo->pushCriteria(new JoinCriteria($joins))
        ->pushCriteria(new InArrayCriteria('activity_log.id', $ids))
        ->all($columns);
    }

    /**
     * get all actions for entity if entity key is sent
     * @param string|null $entityKey entity key
     * @param string|null    $entityId  entity id
     * @return array
     */
    public function getActions(string $entityKey = null, string $entityId = null) : array
    {
        $criteria = [];
        if ($entityKey) {
            $criteria['entity_key'] = $entityKey;
            if ($entityId) {
                $criteria['entity_id'] = $entityId;
            }
        }
        $actions = $this->repo->getActions($criteria);
        $allActions = [];
        foreach($actions as $action) {
            $text = explode(' ', $action->action, 2);
            $text = __t(str_replace('-', ' ', $text[0])) . ' ' . __t($text[1]);
            $text = ucwords($text);
            $value = $action->action;
            $allActions[] = [
                'value' => $value,
                'text' => $text
            ];
        }
        return $allActions;
    }

    /**
     * get all actors
     * @param string|null $entityKey entity key
     * @param string|null    $entityId  entity id
     * @return array
     */
    public function getAllActors(string $entityKey = null, string $entityId = null) : array
    {
        // check for permissions ??
        $criteria = [];
        if ($entityKey) {
            $criteria['is_primary'] = 1;
            $criteria['entity_key'] = $entityKey;
            if ($entityId) {
                $criteria['entity_id'] = $entityId;
            }
        }
        return $this->repo->listActors($criteria);
    }

    /**
     * convert activity data to array without redundant
     * @param ActivityLogRequest $activityLogRequest activity log service
     * @return array
     */
    private function constructActivityData(ActivityLogRequest $activityLogRequest) : array
    {
        $data = [];
        $newData = $activityLogRequest->getNewData();
        $oldData = $activityLogRequest->getOldData();
        if ($oldData && !empty($oldData)) {
            // add only the differences
            foreach ($newData as $key => $value) {
                $appendedAttribute = null;
                if (is_array($value)) {
                    $itemsLogData = $this->getItemsLogData($key, $value, $oldData[$key]);
                    $data = array_merge($data, array_values($itemsLogData));
                } else if (isset($newData[$key]) && !empty($newData[$key]) && !isset($oldData[$key])) {
                    $appendedAttribute = [
                        'key' => $key,
                        'newVal' => $value,
                        'oldVal' => ''
                    ];
                } else if (!isset($newData[$key]) && isset($oldData[$key]) && !empty($oldData[$key])) {
                    $appendedAttribute = [
                        'key' => $key,
                        'newVal' => '',
                        'oldVal' => $oldData[$key]
                    ];
                } else if (isset($newData[$key]) && isset($oldData[$key]) && $newData[$key] != $oldData[$key]) {
                    $appendedAttribute = [
                        'key' => $key,
                        'newVal' => $value,
                        'oldVal' => $oldData[$key]
                    ];
                }
                if (!is_null($appendedAttribute)) {
                    if (empty($appendedAttribute['newVal']) && isset($this->defaultValuesIfEmpty[$key])) {
                        $appendedAttribute['newVal'] = $this->defaultValuesIfEmpty[$key];
                    }
                    $data[] = $appendedAttribute;
                }
            }
        } else {
            foreach ($newData as $key => $value) {
                $data[] = [
                    'key' => $key,
                    'newVal' => $value
                ];
            }
        }
        return $data;
    }

    private function getItemsLogData($key, $newData, $oldData) {
        $data = [];
        $arrayChanges = new ArrayChangeResponse($newData, $oldData);
        $updatedKeys = $arrayChanges->getUpdatedKeys();
        $deletedKeys = $arrayChanges->getDeletedKeys();
        $newKeys = $arrayChanges->getNewKeys();
        if ($updatedKeys) {
            foreach ($updatedKeys as $updatedKey) {
                $data[] = [
                    'key' => 'Updated ' . ucfirst($key),
                    'newVal' => $updatedKey . ': ' . $newData[$updatedKey],
                    'oldVal' => $updatedKey . ': ' . $oldData[$updatedKey],
                ];
            }
        }
        if ($deletedKeys) {
            foreach ($deletedKeys as $deletedKey) {
                $data[] = [
                    'key' => 'Deleted ' . ucfirst($key),
                    'newVal' => null,
                    'oldVal' => $deletedKey . ': ' . $oldData[$deletedKey],
                ];
            }
        }
        if ($newKeys) {
            foreach ($newKeys as $newKey) {
                $data[] = [
                    'key' => 'New ' . ucfirst($key),
                    'newVal' => $newKey . ': ' . $newData[$newKey],
                    'oldVal' => null,
                ];
            }
        }
        return $data;
    }
}
