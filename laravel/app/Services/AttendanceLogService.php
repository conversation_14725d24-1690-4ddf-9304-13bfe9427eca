<?php

namespace App\Services;

use App\Events\NewDayCalculationEvent;
use App\Exceptions\AttendanceLogNoSavePermission;
use App\Exceptions\AttendanceLogNotDraft;
use App\Exceptions\AttendanceLogNotFound;
use App\Exceptions\CanNotDeleteAttendanceDayWithApprovedSheet;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\SessionAlreadyClosed;
use App\Exceptions\SessionNotFoundException;
use App\Facades\Permissions;
use App\Helpers\AttendanceLogSourceParser;
use App\Helpers\FilterOperations;
use App\Repositories\AttendanceLogRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\WhereLikeCriteria;
use App\Repositories\EntityRepository;
use App\Repositories\MachineRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Requests\AttendanceCalculation\ResetDayRequest;
use App\Services\Traits\BulkDeleteService;
use App\Utils\EntityFieldUtil;
use App\Utils\PluginUtil;
use App\Utils\ActiveStatusUtil;
use App\Utils\AttendanceLogSourceTypeUtil;
use App\Utils\AttendanceLogStatusUtil;
use App\Utils\AttendanceSessionStatusUtil;
use App\Utils\PermissionUtil;
use App\Repositories\DepartmentRepository;
use App\Facades\Plugins;
use App\Requests\AttendanceCalculation\NewDayCalculationEventRequest;
use App\Requests\DefaultRequest;
use App\Utils\AttendanceSheetStatusTypesUtil;
use App\Utils\CalculationTypesUtil;
use Carbon\Carbon;
use Closure;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Exceptions\PageAccessNotAllowedException;
use App\Facades\Branch;
use App\Facades\Staff;
use App\Models\AttendanceRestrictionLog;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use App\Repositories\StaffRepository;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Repositories\AttendanceRestrictionRepository;
use App\Repositories\Criteria\AttendanceLogLocationLabelFilterCriteria;
use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;

class AttendanceLogService extends BaseService
{
    use BulkDeleteService;

    var $filters = [];

    public $fieldsWithDefaultValueForApi = [
        'name' => '',
        'status' => 0,
        'location_range_type' => 0,
        'matching_ip_required' => 0,
        'matching_range_required' => 0,
        'matching_camera_required' => 0,
        'locations_list' => [],
        'formatted_locations' => [],

    ];
    /**
     * @var DepartmentRepository
     */
    public $deptRepo;

    /**
     * @var AttendanceLogRepository
     */
    var $repo;
    private $openedSession = null;
    /**
     * @var AttendanceSessionService $attendanceSessionService
     */
    private $attendanceSessionService;

    /**
     * @var \App\Repositories\AttendanceSessionRepository
     */
    private $attendanceSessionRepository;
    /**
     * @var MachineRepository
     */
    private $machineRepo;
    protected $showRouteName = 'owner.attendance_logs.index';

    private $machineMapping = null;

    /**
     * @var \App\Repositories\StaffRepository
     */
    protected $staffRepository;

    public function __construct (
        protected StaffService $staffService,
        AttendanceLogRepository $repo,
        \App\Repositories\StaffRepository $staffRepository,
        \App\Repositories\AttendanceSessionRepository $attendanceSessionRepository,
        EntityRepository $entityRepo = null,
        AttendanceSessionService $attendanceSessionService,
        MachineRepository $machineRepository,
        DepartmentRepository $deptRepo,
        protected AttendanceRestrictionRepository $attendanceRestrictionRepo,
        private AttendanceDayService $attendanceDayService,
    ) {
        $this->staffRepository = $staffRepository;
        $this->attendanceSessionRepository = $attendanceSessionRepository;
        $this->attendanceSessionService = $attendanceSessionService;
        $this->machineRepo = $machineRepository;
        $this->deptRepo = $deptRepo;
        parent::__construct($repo, $entityRepo);
    }


    public function getFormRelatedData()
    {
        return ['attendance_log_staff_relations' => $this->staffRepository->list(['id', 'name']), 'attendance_log_attendance_session_relations' => $this->attendanceSessionRepository->list(['id', ''])];
    }


    public function listing(): array
    {
        $listingData = parent::listing(); // TODO: Change the autogenerated stub
        $canTakeHisAttendance = Permissions::checkPermission(\App\Utils\PermissionUtil::TAKE_HIS_OWN_ATTENDANCE) && $this->authenticatedUser['staff_id'] != 0;
        if($canTakeHisAttendance){
            $listingData['staff'] = $this->staffRepository->find($this->authenticatedUser['staff_id']);
        }
        if(Permissions::checkPermission(PermissionUtil::PULL_MACHINE_ATTENDANCE_LOG))
        {
            $machines = $this->machineRepo->pushCriteria(new CustomFind([['field' => 'status', 'value' => ActiveStatusUtil::ACTIVE, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]]))->list([], true);
            $listingData['machines'] = $machines;
        }

        return $listingData;
    }

    public function listAllLogsForAttendanceDay($attendanceDayId): array
    {
        $date = substr($attendanceDayId, -10);
        $staff_id = explode("-", $attendanceDayId, 2)[0];
        $criteria = new CustomFind([
            ['field' => 'staff_id', 'value' => $staff_id],
            'OR' => [
                ['field' => 'attendance_day_id', 'value' => $attendanceDayId],
                ['field' => 'time', 'value' => [$date . " 00:00:00", $date . " 23:59:59"], 'operation' => FilterOperations::BETWEEN_FILTER_OPERATION]
            ]
        ]);
        $this->repo->resetCriteria()->pushCriteria($criteria);
        $listingData = parent::listing();
        return $listingData;
    }

    function getFilters()
    {
        $staffList = [];
        if (isset($this->parameters['staff_id']) && !empty($this->parameters['staff_id'])) {
            $staff = $this->staffRepository->find($this->parameters['staff_id']);
            if ($staff) {
                $staffList = [
                    Staff::getStaffOptionFormated($staff)
                ];
            }
        }

        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            $depts = collect([]);
            if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
                $depts = $this->deptRepo->list(['id', 'name']);
            }
            $this->filters = [
                'staff_id' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'before' => '<div class="form-group-icon  form-group">',
                    'label' => __t('Employee'),
                    'attributes' => [
                        'placeholder' => __t(getEmployeesFieldPlaceholder()),
                        'id' => 'staffSelect',
                        'data-staff-url' => route('owner.staff.search', ['allow_inactive' => true, 'get_branch_suspended' => 1])
                    ],
                    'div' => 'col-md-6',
                    'options' => $staffList,
                    'type' => 'selectStaff',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'staff_id',
                    ]
                ],
                'status' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'inputClass' => 'form-control select-filter',
                    'attributes' => [
                        'placeholder' => __t('Filter by').' '.__t('Status'),
                        'id' => 'statusSelect'
                    ],
                    'div' => 'col-md-6',
                    'options' => AttendanceLogStatusUtil::getStatusList(),
                    'type' => 'select',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'status',
                    ]
                ],
                'from_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'before' => '<div class="form-group form-group-icon">',
                    'after' => '</div>',
                    'label' => false,
                    'div' => 'col-md-4',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'attributes' => ['placeholder' => __t('Filter by').' '.__t('From Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "time"
                    ],
                ],
                'to_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'before' => '<div class="form-group form-group-icon">',
                    'after' => '</div>',
                    'label' => false,
                    'div' => 'col-md-4',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'attributes' => ['placeholder' => __t('Filter by').' '.__t('To Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "time"
                    ],
                ],
                'source_type' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'attributes' => [
                        'placeholder' => __t('Filter by').' '.__t('Source Type'),
                        'id' => 'typeSelect'
                    ],
                    'div' => 'col-md-4',
                    'inputClass' => 'form-control select-filter',
                    'options' => AttendanceLogSourceTypeUtil::getSourceTypeList(),
                    'type' => 'select',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'source_type',
                    ]
                ],
                'session_id' => [
                    'simple' => true,
                    'hidden' => true,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'inputClass' => 'form-control select-filter',
                    'attributes' => [
                        'placeholder' => __t('Filter by').' '.__t('Session ID'),
                        'id' => 'sessionSelect'
                    ],
                    'div' => 'col-md-4 d-none',
                    'options' => null,
                    'type' => 'select',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'session_id',
                    ]
                ],
                'attendance_day_id' => [
                    'simple' => true,
                    'hidden' => true,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'inputClass' => 'form-control',
                    'attributes' => [
                        'placeholder' => __t('Filter by').' '.__t('Attendance Day ID'),
                        'id' => 'daySelect'
                    ],
                    'div' => 'col-md-4 d-none',
                    'options' => null,
                    'type' => 'select',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'attendance_day_id',
                    ]
                    ]
            ];

            if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
                $this->filters['depts'] = [
                    'label' => '',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Department'),
                        'id' => 'deptSelect'
                    ],
                    'div' => 'col-md-4 form-group',
                    'inputClass' => 'select-filter form-control',
                    'options' => $depts->toArray(),
                    'type' => 'select',
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'department',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'department_id'
                    ]
                    ];

            }
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $branches = Branch::getStaffBranchesSuspended();
                if(count($branches) > 1) {
                    $this->filters['branch'] = [
                        'param_name' => 'branch',
                        'simple'=>true,
                        'label' => false,
                        'after' => '</div>',
                        'before' => '<div class="form-group">',
                        'attributes' => [
                            'placeholder' => __t('Select') . ' ' . __t('Branch'),
                            'id' => 'branchSelect',
                        ],
                        'div' => 'col-md-4',
                        'inputClass' => 'select-filter form-control',
                        'options' => $branches,
                        'type' => 'select', 'filter_options' => [
                            'table' => 'staffs',
                            'model' => 'attendance_log_staff_relation',
                            'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                            'field' => 'branch_id',
                        ]
                    ];
                }
            }

            $labelArray = $this->attendanceRestrictionRepo->getLocationsLabels();
            if(!empty($labelArray)){
                $labelList = [];
                foreach ($labelArray as $label) {
                    $labelList[$label] = $label;
                }
                $selectedLabels = $this->parameters['label'] ?? [];
                $this->filters['label[]'] = [
                    'param_name' => 'label',
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'value' => $selectedLabels ?? [],
                    'attributes' => [
                        'placeholder' => __t('Search by').' '. __t('Label'),
                        'multiple' => 'multiple',
                    ],
                    'div' => 'col-md-4',
                    'inputClass' => 'select-filter form-control',
                    'type' => 'select',
                    'options' => $labelList ?? [],
                    "filter_options" => [
                        "operation" => FilterOperations::CLOSURE,
                        "field" => "locations->label",
                        'function' => function ($value) {
                            return [
                                "operation" => FilterOperations::CRITERIA,
                                "criteria" => new AttendanceLogLocationLabelFilterCriteria($value),
                            ];
                        }
                    ],
                ];
            }

            return $this->filters;
        }

    }

    /**
     * {@inheritDoc}
     */
    protected function getListingConditions()
    {
        $listingConditions = parent::getListingConditions();
        $authUserID = getAuthOwner('staff_id');
        if(Permissions::isOwnerOrAdmin()){
            return $listingConditions;
        }

        $accessibleBranchListingCondition = $this->getAccessibleBranchListingCondition();
        if(!empty($accessibleBranchListingCondition))
            $listingConditions[] = $accessibleBranchListingCondition;


        if(Permissions::checkPermission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG)){
            return $listingConditions;
        }
        if (Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE)){
            $listingConditions[] = [
                'operation' => FilterOperations::RAW_OPERATION,
                'field' => 'attendance_logs.staff_id IN ( select staff_id from staff_info where department_id = ? )',
                'value' => [$this->staffService->getAuthDepartmentId()],
            ];
            return $listingConditions;
        }

        $listingConditions[] = [
            "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
            "field" => "staff_id",
            "value" => $authUserID
        ];
        return $listingConditions;
    }

    public function getSortFields()
    {
        return [
            'fields' => [
                'time' => [
                    'title' => __t('Date and Time'),
                    'field' => 'time',
                    'direction' => 'DESC'
                ],
                'id' => [
                    'title' => __t('ID'),
                    'field' => 'id',
                    'hidden' => true,
                    'direction' => 'DESC'
                ],
            ],
            'active' => [
                'field' => 'id',
                'direction' => 'DESC',
            ]
        ];
    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['staff_id'] = $record->attendance_log_staff_relation ? $record->attendance_log_staff_relation->name : '';
        return $data;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record): array
    {
        $relations = parent::getActivityLogRelations($record);
        if ($record->attendance_log_staff_relation) {
            $relations[] = new ActivityLogRelationRequest($record->attendance_log_staff_relation->id, 'staff');

        }
        if ($record->attendance_log_attendance_session_relation) {
            $relations[] = new ActivityLogRelationRequest($record->attendance_log_attendance_session_relation->id, 'attendance_session');

        }


        return $relations;
    }

    /**
     * @param $sessionId
     * @param $staffId
     * @return mixed
     * gets the staff last sign in the given session
     */
    public function getStaffLastSessionSign($sessionId, $staffId)
    {
        $lastStaffSessionLog = $this->repo->getLastStaffSessionLog($sessionId, $staffId);
        return $lastStaffSessionLog;
    }

    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $this->disableLogging();
        $staffService = resolve(StaffService::class);
        if(!$request->get("source_type") == 'self' && !$staffService->isAccessableStaffId($request->get('staff_id'),false)){
            throw new \Exception(sprintf(__t('You do not have access to this %s',true),__t('Record',true)));
        }

        if($request['source_type'] === AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_SUPERVISOR){
            $session = $this->attendanceSessionRepository->find($request['session_id']);
            if($session->status == AttendanceSessionStatusUtil::ATTENDANCE_SESSION_STATUS_CLOSE)
            {
                throw new SessionAlreadyClosed;
            }
            $request['source_id'] = getAuthOwner('staff_id');
            if($session->source_id != $request['source_id'] && !isOwnerOrSuperAdmin()){
                throw new AttendanceLogNoSavePermission;
            }
        }else if($request['source_type'] === AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_SELF)
        {
            $staffId = getAuthOwner('staff_id');
            $request['source_id'] = $staffId;
            $request['staff_id'] = $staffId;
        }
        if (empty($request['time'])) {
            $request['time'] = Carbon::now('UTC')->toDateTimeString();
        }
        $request['status'] = AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT;

        $request['created'] =  Carbon::now('UTC')->toDateTimeString();

        $result = parent::add($request, $excludedFields);
        if($result->session_id)
        {
            $this->attendanceSessionRepository->increaseSignsCount($result->session_id);
        }
        return $result;
    }

    public function beforeImport($fileName)
    {
        $this->openedSession = $this->attendanceSessionService->createSession(
            AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_FILE,
            null,
            $fileName ?? 'attendanceLog.csv'
        );
    }

    public function validateImportData($data, $validationRules, $headersInfo, $entityKey, $rowCount, $isUpdate, $updateUsing, &$invalidData, &$willBeUpdated , $customData=null)
    {
        $entityRulesGetter = resolve(EntityRulesGetter::class);
        $validator = new DaftraValidator($entityKey, $entityRulesGetter);

        $customValidationRules = $this->staffService::getImportingValidationRules();
        $messages =$this->staffService::getImportingValidationMessages();

        $validator->setData($data);
        $validator->addRules($customValidationRules);
        $validator->setMessages($messages);

        if (!$validator->isValid()) {
            foreach ($validator->getErrors() as $key => $errors) {
                foreach ($errors as $error) {
                    $invalidData[$rowCount][] = sprintf(__t('%s in row #%s'), $error, $rowCount);
                }
            }
            return false;
        }
        return true;
    }

    public function preProcessDataForImportUsingExtraData($data, $extraData)
    {
        if (!empty($extraData['machine_id'])) {
            if (empty($this->machineMapping)) {
                $this->machineMapping = $this->machineRepo->getMachineMappingsList($extraData['machine_id']);
            }
            $data['staff_id'] = $this->machineMapping[$data['staff_id']] ?? $data['staff_id'];
        }else{
            $staff = $this->staffRepository->model->where(['code'=>$data['staff_id']])->orWhere(['full_name'=>$data['staff_id']])->first();
            $data['staff_id'] = $staff ? $staff->id : null ;
        }
        return $data;
    }

    public function import($request)
    {
        if ($this->openedSession) {
            $request['session_id'] = $this->openedSession->id;
            $request['source_id'] = $this->openedSession->source_id;
            $request['source_name'] = $this->openedSession->source_name;
            $request['source_type'] = $this->openedSession->source_type;
            if (empty($request['time'])) {
                throw new \Exception(__t('Failed to format the time make sure it is formatted in "Y-m-d H:i:s" format like "2019-06-25 14:30:00"'));
            }
            return parent::import($request);
        } else {
            throw new SessionNotFoundException();
        }
    }

    public function afterImport()
    {
        if ($this->openedSession) {
            $this->attendanceSessionService->closeSession($this->openedSession->id);
        }
    }

    public function getImportDownloadSample() {
        return ['path' => '/samples/attendance-log-sample.csv', 'file_name' => __t('attendance log sample') . '.csv'];
    }

    public function delete($id, Closure $callback = null)
    {
        $oldData = $this->find($id);
        if (!$oldData)
            throw new EntityNotFoundException('Attendance Log');

        $staffService = resolve(StaffService::class);
        if(!Permissions::isOwnerOrAdmin() && !$staffService->isAccessableStaffId($oldData->staff_id, false)){
            throw new \Exception(sprintf(__t('You do not have access to this %s',true),__t('Record',true)));
        }

        $attendance_session = $oldData->attendance_log_attendance_session_relation;
        if(isset($oldData) && !is_null($oldData)){
            if ($oldData->status == AttendanceLogStatusUtil::ATTENDANCE_LOG_DRAFT){
                if($delete = parent::delete($id) && !is_null($attendance_session)){
                    $this->attendanceSessionService->repo->decreaseSignsCount($attendance_session->id);
                }
                return $delete;
            } else {
                $attendanceDay = $this->repo->getAttendanceDay($oldData);
                $attendanceSheet = $attendanceDay? $attendanceDay->attendanceSheet : null;
                if ($attendanceSheet && $attendanceSheet->status == AttendanceSheetStatusTypesUtil::APPROVED) {
                    throw new CanNotDeleteAttendanceDayWithApprovedSheet($attendanceSheet->auto_id);
                } else {
                    if($delete = parent::delete($id) && !is_null($attendance_session)){
                        $this->attendanceSessionService->repo->decreaseSignsCount($attendance_session->id);
                    }
                    if ($attendanceDay) {
                        $this->calculateAttendanceDay($attendanceDay);
                    }
                }
                return $delete;
            }
        } else {
            throw new AttendanceLogNotFound();
        }
    }

    private function calculateAttendanceDay($attendanceDay) {
        if ($attendanceDay['calculation_type'] == CalculationTypesUtil::AUTOMATIC_CALC) {

            $calculationData = $this->attendanceDayService->prepareAutomaticCalculationData(new DefaultRequest([
                'override_manual_attendance' => 0,
                'from_date' => $attendanceDay->date,
                'to_date' => $attendanceDay->date,
                'employees' => [$attendanceDay->staff_id],
                'branch_id' => null,
                'department_id' => null,
                'designation_id' => null,
                'shift_id' => null,
                'exclude_criteria' => []
            ]));
            $calculateRequest = new NewDayCalculationEventRequest(
                $calculationData['fromDate'],
                $calculationData['toDate'],
                $calculationData['calculationCriteria'],
                0,
                false,
                null,
            );
            event(new NewDayCalculationEvent($calculateRequest));
        }
    }
    public function deleteMany(array $ids, string $selectType = null)
    {
        $deletedCount = 0;
        $unDeletedCount = 0;

        foreach ($ids as $key => $value) {
            try {
                $this->delete($value);
                $deletedCount++;
            } catch (AttendanceLogNotDraft $exception) {
                $unDeletedCount++;
                continue;
            } catch (AttendanceLogNotFound $exception) {
                $unDeletedCount++;
                continue;
            }
        }

        return ['deletedCount' => $deletedCount, 'unDeletedCount' => $unDeletedCount];
    }

    /**
     * @param $staffID
     * @param $beginningIn
     * @param $endingIn
     * @param $beginningOut
     * @param $endingOut
     * @return array|null
     */
    public function getSigns($staffID, $beginningIn, $endingIn, $beginningOut, $endingOut)
    {
        return $this->repo->getSigns($staffID, $beginningIn, $endingIn, $beginningOut, $endingOut);
    }

    public function resetLogsViaCalculator(ResetDayRequest $request){
        $this->repo->resetLogs(
            $request->getEmployeeId(),
            $request->getFromDate(),
            $request->getToDate()
        );
    }

    public function setDaysSigns($signIDs, $signDays)
    {
        return $this->repo->setDaysSigns($signIDs, $signDays);
    }

    public function setInvalidSignsInRange($staffIds, $dateFrom, $dateTo): int
    {
        return $this->repo->setInvalidSignsInRange($staffIds, $dateFrom, $dateTo);
    }

    public function setInvalidSignsInDate($staffIds, $date): int
    {
        return $this->repo->setInvalidSignsInDate($staffIds, $date);
    }

    public function saveMachineSigns($machineSigns)
    {
        $signsToInsert = [];
        $lastSignTime = null;
        foreach($machineSigns as $machineSign)
        {
            $sign = $machineSign->toArray();
            if($sign['time'] !== $lastSignTime)
            {
                $signsToInsert[] = $sign;
            }
            $lastSignTime = $sign['time'];
        }
        $result = $this->repo->insertBulk($signsToInsert, true);
        $lastSign = end($signsToInsert);
        $count = $this->repo->pushCriteria(new CustomFind([['field' => 'session_id', 'value' => $lastSign['session_id'], 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]]))->count();

        $this->attendanceSessionRepository->update($lastSign['session_id'], ['signs_count' => count($signsToInsert)]);
        return [$lastSignTime, $count];
    }

    public function getImportViewShareData(): array
    {
        $machines = $this->machineRepo->pushCriteria(new CustomFind([['field' => 'status', 'value' => ActiveStatusUtil::ACTIVE, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]]))->list([], true);
        return ['machines' => $machines];
    }

    function addDefaultValueForApiResponse(object|null $object, array $fields = [])
    {
        if (empty($object)) {
            return null;
        }
        if (empty($fields)) {
            $fields = $this->fieldsWithDefaultValueForApi ?? [];
        }

        if(($object->use_ip ?? 0) == 0){
            $object->matching_ip_required = 0;
        }
        if(($object->use_location ?? 0) == 0){
            $object->matching_range_required = 0;
        }
        if(($object->use_camera ?? 0) == 0){
            $object->matching_camera_required = 0;
        }

        foreach ($fields as $field => $value) {
            $object->{$field} = $object->{$field} ?? $value;
        }
        return $object;
    }

    public function getViewData($id)
    {
        $result = parent::getViewData($id);
        $staffService = resolve(StaffService::class);
        if(
            $result['record']->staff_id != -1
            && !$staffService->isAccessableStaffId($result['record']->staff_id,true)
        ){
            throw new NotAccessibleStaffBranchException;
        }

        $record = $result['record'];
        $result['authDeptId'] = $this->staffService->getAuthDepartmentId();
        if(!Permissions::isOwnerOrAdmin() && !Permissions::checkPermission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG)){
            $hasOwnViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG);
            $hasDeptViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE);
            $recordDeptId = $record->staff_info?->department_id ?? null;
            $sameDept = isset($recordDeptId) && isset($result['authDeptId']) && $result['authDeptId'] == $recordDeptId;
            $recordStaffId = $record['staff_id'] ?? null;
            $authId = getAuthOwner('staff_id');
            $isOwn = isset($recordStaffId) && isset($authId) && $recordStaffId == $authId;
            $hasViewPermission = ($sameDept && $hasDeptViewPermission) || ($isOwn && $hasOwnViewPermission);
            if(!$hasViewPermission){
                throw new PageAccessNotAllowedException;
            }
        }

        if ($result['record']->status == AttendanceLogStatusUtil::ATTENDANCE_LOG_INVALID) {
            $attendanceShiftId = $this->repo->getAttendanceShiftOfAttendanceLog($result['record']);
            if ($attendanceShiftId) {
                $route = route("owner.shifts.show", $attendanceShiftId);
                $tag = sprintf("<a href='$route' target='_blank'>%s</a>", sprintf(__t('Attendance Shift') . ' #%s', $attendanceShiftId));
                $message = sprintf(__t("The attendance log is out the range of the on duty and off duty time of the shift , so you can update the %s values and re-calculate the attendance day or the log has already been calculated in an approved attendance sheet, and it will be considered invalid. Therefore, you need to unapprove the sheet, recalculate the attendance days, and the log will be reflected successfully."), $tag);
                $result['message'] = $message;
            }
        }
        return $result;
    }

    public function getLastStaffAttendanceLog($staff_id){
        $result = null;
        $lastSignTime = null;
        $staffSessionLog = $this->getStaffLastSessionSign(false, $staff_id);

        if ($staffSessionLog->isNotEmpty()) {
            $result = [
                "time"=> null,
                "location"=> false,
                "image"=> null,
            ];
            $lastSign = $staffSessionLog->shift();
            $lastSignTime = convertFromUtc($lastSign->time)->toDateTimeString();
            $result['time'] = $lastSignTime;
            if($lastSign->attendance_restriction_log_id){
                $restrictionLog = AttendanceRestrictionLog::find($lastSign->attendance_restriction_log_id);
                $result['location'] = $restrictionLog->getLocationAttribute()? true : false;
                $result['image'] = $restrictionLog->image;
            }
        }

        return $result;
    }

    public function getStaffRelatedLogs($staffId)
    {
       $formattedLogs = [];
        $userCanViewAttendanceLogs = $this->userCanViewAttendanceLogs($staffId);
        if (!$userCanViewAttendanceLogs){
            return [];
        }
       $staffWithLogs = $this->staffRepository->getStaffWithAttendanceLogs($staffId);
       foreach ($staffWithLogs->attendanceLogs as $attendanceLog){
           $formattedLogs[] = $this->formatStaffAttendanceLogs($attendanceLog , $staffWithLogs);
       }
       return $formattedLogs;
    }

    private function formatStaffAttendanceLogs($attendanceLog , $staff)
    {
        $formattedLog = [];
        $date= $day= $time = $img =null;
        if ($attendanceLog->time){
            $logDateTime = Carbon::parse($attendanceLog->time);
            $date = format_date($attendanceLog->time);
            $day =$logDateTime->dayName;
            $time = convertFromUtc($logDateTime)->format('H:i:s');
        }
        if(!empty($attendanceLog->attendance_restriction_log->image)){
            $img = resizeImage($attendanceLog->attendance_restriction_log->image, ["w" => 30, "h" => 30, "c" => 1]);
        }
        $dayNameTrans = __t($day);
        $formattedLog =[
            "id" => $attendanceLog->id,
            "img" => $img,
            "day"=> $dayNameTrans,
            "date" => $dayNameTrans ." " .$date,
            "time" => $time,
            "location" => [
                "lng" => $attendanceLog->attendance_restriction_log?->location_long ?? null,
                "lat" => $attendanceLog->attendance_restriction_log?->location_lat ?? null
            ],
            "source" => AttendanceLogSourceParser::parseSource($attendanceLog),
            "sessionId" => $attendanceLog->session_id,
            "status" => __(\App\Utils\AttendanceLogStatusUtil::getStatusList()[$attendanceLog->status]),
            "statusValue" => $attendanceLog->status
        ];
        return $formattedLog;
    }

    private function userCanViewAttendanceLogs($staffId)
    {
        if (Permissions::checkPermission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG)) return true;
        $authStaff = getAuthOwner('staff_id');
        if (Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG) && $authStaff == $staffId) return true;
        return  false;
    }
}
