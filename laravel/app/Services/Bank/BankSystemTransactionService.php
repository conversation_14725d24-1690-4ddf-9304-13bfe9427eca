<?php

namespace App\Services\Bank;

use App\Repositories\BankTransactionRepository;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Criteria\IsNullCriteria;
use App\Repositories\Criteria\JoinCriteria;
use App\Repositories\Criteria\LargerThanCriteria;
use App\Repositories\Interfaces\BankTransactionRepositoryInterface;
use App\Repositories\Interfaces\JournalTransactionRepositoryInterface;
use App\Repositories\JournalTransactionRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Services\CommonActivityLogService;
use App\Utils\BankTransactionsStatusUtil;
use Izam\Daftra\ActivityLog\Requests\ActivityLogRequest;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class BankSystemTransactionService
{

    public function getBankTransactionSystemTransactionsToBeMatched(
        JournalTransactionRepositoryInterface $journalTransactionRepository,
        $bankAccountId,
        $transactionType,
        $systemTransactionIds = [],
        $currencyCode = null
    ) {
        return $journalTransactionRepository->getBankUnMatchedTransactions($bankAccountId,
            $transactionType,
            $systemTransactionIds,
            $currencyCode);
    }

    public function validateTransactionsCanBeMatched($transactionsToMatch, $amount, $useLocalAmounts = true) {
        $sysTransactionsSum = 0;
        foreach ($transactionsToMatch as $transaction) {
            if($useLocalAmounts) {
                $sysTransactionsSum += $transaction->credit > 0 ? $transaction->credit : $transaction->debit;
            } else {
                $sysTransactionsSum += $transaction->currency_credit > 0 ? $transaction->currency_credit : $transaction->currency_debit;
            }
        }
        if(abs($sysTransactionsSum - $amount) >= 0.01) {
            return __t('The value of the bank transaction must be equal to the value of the system transactions');
        }
        return true;
    }

    public function matchTransactions(
        JournalTransactionRepositoryInterface $journalTransactionRepository,
        BankTransactionRepositoryInterface $bankTransactionRepository,
        $bank_transaction_id,
        $systemTransactionsIds,
    ) {

        $bankTransactionRepository->update($bank_transaction_id, ['status' => BankTransactionsStatusUtil::MATCHED]);
        $journalTransactionRepository->updateBankTransactionId($systemTransactionsIds, $bank_transaction_id);
    }

    public function createMatchingActivityLog($bankTransaction, $newData, $oldData) {
        if(isset($newData['status'])) {
            $newData['status'] = BankTransactionsStatusUtil::getStatus($newData['status']);
        }

        if(isset($oldData['status'])) {
            $oldData['status'] = BankTransactionsStatusUtil::getStatus($oldData['status']);
        }
        return new ActivityLogRequest(
            $bankTransaction->id,
            EntityKeyTypesUtil::BANK_TRANSACTION,
            ActionLineMainOperationTypesUtil::UPDATE_ACTION,
            "#{$bankTransaction->id}" . $bankTransaction->reference_id ? " ref: {$bankTransaction->reference_id}":"",
            "/owner/treasuries/view/{$bankTransaction->bank_id}",
            $newData,
            $oldData,
            [
                new ActivityLogRelationRequest($bankTransaction->id, EntityKeyTypesUtil::BANK_TRANSACTION, true),
                new ActivityLogRelationRequest($bankTransaction->bank_id, EntityKeyTypesUtil::TREASURY, false)
            ]
        );
    }

    public function getSystemTransactionBankTransactionsToBeMatched(
        BankTransactionRepositoryInterface $bankTransactionRepository,
        $bankId,
        $transactionType,
        $bankTransactionIds = [],
    ) {
        return $bankTransactionRepository->getBankUnMatchedBankTransactions(
            $bankId,
            $transactionType,
            $bankTransactionIds,
        );
    }

    public function validateBankTransactionsCanBeMatched($bankTransactionsToMatch, $amount) {
        $bankTransactionsSum = collect($bankTransactionsToMatch)->sum('amount');
        if(abs($bankTransactionsSum - $amount) >= 0.01) {
            return __t('The value of the system transaction must be equal to the value of the bank transactions');
        }
        return true;
    }

    public function matchBankTransactions(
        JournalTransactionRepositoryInterface $journalTransactionRepository,
        BankTransactionRepositoryInterface $bankTransactionRepository,
        $system_transaction_id,
        $bankTransactionsIds,
    ) {

        $journalTransactionRepository->update($system_transaction_id, ['status' => BankTransactionsStatusUtil::MATCHED]);
        $bankTransactionRepository->updateJournalTransactionId($bankTransactionsIds, $system_transaction_id);
    }
}
