<?php

namespace App\Services;

use App\Models\Staff;
use App\Facades\Settings;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Modules\LocalEntity\Services\MobileActivityLog\MobileActivityLogCreatorFactory;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\LeaveTypeRepository;
use App\Utils\LeaveApplicationStatusUtil;
use App\Utils\PluginUtil;
use App\Facades\Permissions;
use App\Facades\Plugins;
use Illuminate\Http\Request;
use App\Utils\PermissionUtil;
use App\Models\LeaveApplication;
use App\Repositories\EntityRepository;
use Illuminate\Database\Eloquent\Model;
use App\Services\AttendancePermissionService;
use App\Repositories\LeaveApplicationRepository;
use App\Repositories\StaffRepository;
use App\Models\Owner;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\Events\RecoredApprovedEvent;
use App\Modules\LocalEntity\Events\RecoredUndoApproveEvent;
use App\Modules\LocalEntity\Prototype\DataStructure\OrClause;
use App\Modules\LocalEntity\Prototype\Filters\ConditionMeta;
use App\Modules\LocalEntity\Queue\Events\LeaveApplication\LeaveApplicationApproved;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationMatcher;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalMatchRequest;
use App\Repositories\PostRepository;
use App\Utils\AttendancePermissionTypesUtil;
use Illuminate\Support\Facades\Log;
use Izam\Aws\Aws;
use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;
use Izam\Daftra\Common\Utils\PostTypeUtil;
use Izam\Daftra\ActivityLog\Requests\ActivityLogRequest;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\JoinCriteria;
use App\Modules\LocalEntity\Queue\Events\LeaveApplication\LeaveApplicationUpdated;
use App\Modules\LocalEntity\Repositories\MultiCycleApprovalConfigurationRepository;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfiguration;
use Izam\Daftra\Common\Utils\EntityEmailPreferenceUserTypeUtil;

class LeaveApplicationService extends BaseService
{

    private $leaveApplicationRepository;
    private $permissionService;
    private $staffService;
    private $staffRepository;
    private $reportsService;
    private $attendancePermissionService;
    static  $leaveApplicationsStaffs = [];

    public function __construct(LeaveApplicationRepository $leaveApplicationRepository, StaffService $staffService, EntityRepository $entityRepo, AttendancePermissionService $permissionService,
    StaffRepository $staffRepository, AttendanceReportsService $reportsService, AttendancePermissionService $attendancePermissionService, protected MultiCycleApprovalConfigurationService $multiCycleApprovalConfigurationService, protected MultiCycleApprovalConfigurationMatcher $multiCycleApprovalConfigurationMatcher)
    {
        parent::__construct($leaveApplicationRepository, $entityRepo);
        $this->leaveApplicationRepository = $leaveApplicationRepository;
        $this->permissionService = $permissionService;
        $this->staffService = $staffService;
        $this->staffRepository = $staffRepository;
        $this->reportsService = $reportsService;
        $this->attendancePermissionService = $attendancePermissionService;

    }


    /**
     * * handles the updating process
     *
     * @return Model
     */
    public function updateApplicationStatus($data, $applicationId, $checkPermission = true): Model
    {
        $this->validateApplicantIsActive($applicationId);

        if($checkPermission){
            $userCanUpdateStatus = $this->authenticateUserCanUpdateStatus($applicationId);
            if (!$userCanUpdateStatus) {
                throw new \Exception(__t("You don't have a permission to Approve or Reject this leave application"));
            }
        }
        $leaveApplication = $this->leaveApplicationRepository->find($applicationId);

        if ($leaveApplication['status'] != 'pending') {
            throw new \Exception(__t("You cannot approve or reject not pending leave applications"));
        }

        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($leaveApplication->staff_id, true)){
            throw new \App\Exceptions\NotAccessibleStaffBranchException;
        }

        if(isset($leaveApplication->approval_cycle_configuration_id)){
            $data = $this->updateApproversData($leaveApplication, $data);
        }
        $updatedApplication = $this->leaveApplicationRepository->updateStatus($applicationId, $data);
        $this->logLeaveApplicationActivity($updatedApplication , $leaveApplication);
        return $updatedApplication;
    }


    private function updateApproversData($leaveApplication, $data)
{
    $configuration = $this->multiCycleApprovalConfigurationService
                          ->getConfigurations($leaveApplication->approval_cycle_configuration_id);

    if (empty($configuration)) {
        return $data;
    }

    $approvalData = $leaveApplication->getApprovalData();
    $currentLevel = $approvalData->current_level;
    $approvers = $approvalData->approvers;
    $configLevelsCount = $configuration->getLevelsCount();

    $approvers[] = [
        'level' => $currentLevel,
        'staff_id' => getAuthOwner('staff_id'),
        'note' => $data['comment'] ?? '',
        'date' => (new \DateTime())->format("Y-m-d H:i:s"),
    ];

    if ($data['status'] === LeaveApplicationStatusUtil::Approved) {
        $data['approval_data'] = json_encode([
            'current_level' => $currentLevel + 1,
            'approvers' => $approvers,
        ]);

        $isFinalApproval = ($configLevelsCount == $currentLevel);
        $data['status'] = $isFinalApproval
            ? (LeaveApplicationStatusUtil::Approved)
            : (LeaveApplicationStatusUtil::Pending);

    } else {
        $data['approval_data'] = json_encode([
            'current_level' => $currentLevel,
            'rejected_by' => getAuthOwner('staff_id'),
            'approvers' => $approvers,
        ]);
    }
    return $data;
}

    /**
     * * validates applicant is active
     *
     * @return bool
     */
    public function validateApplicantIsActive($applicationId): bool
    {
        $leaveApplication = $this->leaveApplicationRepository->find($applicationId);
        $applicant = $this->staffService->find($leaveApplication->staff_id);
        if (!$applicant || !$applicant->isActive()) {
            throw new \Exception(__t("You cannot approve or convert the leave application as the employee already deleted or deactivated"));
        }
        return true;
    }


    /**
     * * validates current user can approve or reject application
     *
     * @return bool
     */
     public function authenticateUserCanUpdateStatus($application): bool
    {
        $hasPermission = Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION);
        if (!$hasPermission) {
            return false;
        }
        $leaveApplicationStaffData =  $this->leaveApplicationRepository->getLeaveApplicationsStaffData(leaveApplicationId: $application->id?? $application, withApprovalData: true)->first();
        if($leaveApplicationStaffData){
            $configuration = $leaveApplicationStaffData->approval_cycle_configuration_id ?
            $this->multiCycleApprovalConfigurationService->getConfigurations($leaveApplicationStaffData->approval_cycle_configuration_id) : null;
            $currentLevel = null;
            if($leaveApplicationStaffData->approval_data){
                $approvalData = json_decode($leaveApplicationStaffData->approval_data ?? []);
                $currentLevel = $approvalData->current_level;
            }
            return  $this->isActiveApprover($leaveApplicationStaffData, getAuthOwner('staff_id'), $configuration, $currentLevel);
        }
        return false;
    }

    public function authenticateUserCanUndoApproval($id): bool
    {
        $currentUserId = getAuthOwner('staff_id');
        $hasPermission = Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION);
        if (!$hasPermission) {
            return false;
        }
        $leaveApplicationStaffData =  $this->leaveApplicationRepository->getLeaveApplicationsStaffData(leaveApplicationId: $id, withApprovalData: true)->first();
        if($leaveApplicationStaffData->staff_id == null){
            return false;
        }
        if ($leaveApplicationStaffData->approval_cycle_configuration_id && $this->multiCycleApprovalConfigurationService->getConfigurations($leaveApplicationStaffData->approval_cycle_configuration_id)) {
            $approvalData = json_decode($leaveApplicationStaffData->approval_data);
            $currentLevel = $approvalData->current_level ?? 1;
            if($leaveApplicationStaffData->status != LeaveApplicationStatusUtil::Rejected){
                $currentLevel = $currentLevel - 1;
            }
            $lastLevelApprovers = $this->multiCycleApprovalConfigurationService->getLevelApprovers(
                $leaveApplicationStaffData->approval_cycle_configuration_id,
                $currentLevel,
                $leaveApplicationStaffData->staff_id
            );
            return in_array($currentUserId, $lastLevelApprovers);
        }

        if(getAuthOwner('is_super_admin')){
            $usersThatcanApproveIds[] = getAuthOwner('staff_id');
        }


        if($leaveApplicationStaffData->status != LeaveApplicationStatusUtil::Pending){
            $usersThatcanApproveIds = $this->getUsersCanUpdateStatusFromBasicSettings($leaveApplicationStaffData);
            return in_array($currentUserId, $usersThatcanApproveIds);
        }
        return false;
    }


    public function applicationCanUndoApproval($id): bool{
        $application = $this->leaveApplicationRepository->getApplicationWithRelatedData($id);
       return  !empty($application->getApprovalData()) && (count((array)$application->getApprovalData()->approvers) > 0 && $application->status == LeaveApplicationStatusUtil::Pending ) || $application->status == LeaveApplicationStatusUtil::Approved;
    }

    /**
     * * handles creating a new permission in case of approval
     *
     * @return Model
     */
    public function createRelatedPermission($updatedApplication): Model
    {
        $attendancePermissionData['to_date'] = $updatedApplication->date_to;
        $attendancePermissionData['from_date'] = $updatedApplication->date_from;
        $attendancePermissionData['staff_id'] = $updatedApplication->staff_id;
        $attendancePermissionData['type'] = $updatedApplication->type;
        if(in_array($updatedApplication->type, ["leave", 'half_leave'])){
            $attendancePermissionData['leave_type_id'] = $updatedApplication->leave_type_id;
        }elseif($updatedApplication->type == "delay"){
            $attendancePermissionData['late_time'] = $updatedApplication->late_time;
        }elseif($updatedApplication->type == "early"){
            $attendancePermissionData['early_time'] = $updatedApplication->early_time;
        }

        $attendancePermissionData['shift_type'] = $updatedApplication->shift_type ?? \App\Utils\MultipleShiftTypeUtil::PRIMARY;

        $permissionRequest = new Request($attendancePermissionData);
        //AttendancePermissionService->add first param is a request object
        $attendancePermission = $this->permissionService->add($permissionRequest);

        if ($attendancePermission) {
            $this->leaveApplicationRepository->update($updatedApplication->id, [
                'attendance_permission_id' => $attendancePermission->id
            ]);
        };
        return $updatedApplication;
    }

    /**
     * * checks if there is a permission related to a specific application
     *
     * @return void
     */
    public function checkRelatedPermission($id): void
    {
        $application = $this->leaveApplicationRepository->find($id);
        if ($application->attendance_permission) {
            if($application->attendance_permission->attendanceDays->count()){
                throw new \Exception(__t("You cannot undo the leave application as there is a related attendance permission already calculated in the attendance record"));
            }
        }
    }


    /**
     * * handles reverting application status to pending status
     *
     * @return Model
     */
    public function revertToPending($id)
    {
        $this->validateApplicantIsActive($id);
        $userCanUpdateStatus = $this->authenticateUserCanUndoApproval($id);
        if (!$userCanUpdateStatus) {
            throw new \Exception(__t("You don't have a permission to Approve or Reject this leave application"));
        }
        $this->checkRelatedPermission($id);

        $application = $this->leaveApplicationRepository->find($id);
        // if ( !in_array($application['status'], ['rejected', 'approved'])) {
        //     throw new \Exception(__t("You cannot undo a pending leave application"));
        // }
        $data = ['status' => 'pending', 'comment' => null, 'attendance_permission_id' => null, 'approved_by'=> null];
        if(isset($application->approval_cycle_configuration_id)){
            $approval_data = $application->getApprovalData();

                $prevLevel = $approval_data->current_level - 1;
                if($application->status == LeaveApplicationStatusUtil::Rejected){
                    $prevLevel = $approval_data->current_level;
                }
                $approvers = array_filter($approval_data->approvers, function ($approver) use ($prevLevel) {
                    return $approver->level != $prevLevel;
                });
                $data['approval_data'] = json_encode([
                    'current_level'=> $prevLevel,
                    'approvers' => $approvers,
                ]);
            }
        $leaveApplicationData = $application->toArray();
        if($application->attendance_permission){
            $this->attendancePermissionService->delete($application->attendance_permission->id);
            $leaveApplicationData['undo_from_final_approve'] = true;
        }
        event(new RecoredUndoApproveEvent(new RecoredInfo('leave_application',  $leaveApplicationData)));
        $updatedApplication = $this->leaveApplicationRepository->update($id, $data);
        dispatch_event_action(new LeaveApplicationUpdated($updatedApplication));
        $this->logLeaveApplicationActivity($updatedApplication , $application);
        return ['updatedApplication'=> $updatedApplication, 'oldApplication'=> $application];
    }

    public function checkConfigurationRematch($id){
        $configurationWillRematch = false;
        $leaveApplication = $this->find($id);
        $matchedApprovalConfigurations = $this->getMatchedApprovalConfigurations($leaveApplication);
        $configurationWillRematch = ($matchedApprovalConfigurations?->getId() ?? null) != $leaveApplication->approval_cycle_configuration_id;
        return $configurationWillRematch;
    }

    /**
     * * is staff can view the leave application
     *
     * @return array
     */
    public function isLeaveApplicationViewAllowed($leaveApplication, $staffId = 0): bool{
        if($staffId == 0){
            $staffId = getAuthOwner('staff_id');
        }
        $canViewAll = Permissions::checkPermission(PermissionUtil::VIEW_ALL_LEAVE_APPLICATION, $staffId);
        if($canViewAll){
            return true;
        }
        $directManagedStaff = $this->staffRepository->directManagedStaffbyAuthUser($staffId);
        $directManagedStaffIds = $directManagedStaff->pluck('id')->toArray();

        $staffDepartmentManagedByUsers = $this->staffRepository->getStaffByDepartmentManagerId($staffId);
        $staffDepartmentManagedByUsersIds = $staffDepartmentManagedByUsers->pluck('id')->toArray();

        $viewHisOwn = Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_LEAVE_APPLICATIONS, $staffId);
        $approveLeaveApplication = Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION, $staffId);
        $canViewHisTeamLeaveApplication = Permissions::checkPermission(\Izam\Daftra\Common\Utils\PermissionUtil::VIEW_HIS_TEAM_LEAVE_APPLICATIONS);

        if($viewHisOwn && ($leaveApplication['staff_id'] == $staffId || $leaveApplication['created_by'] == $staffId )){
            return true;
        }
        if ($viewHisOwn && $approveLeaveApplication){
            $currentUserId = getAuthOwner('staff_id');

            if($leaveApplication['approval_cycle_configuration_id']){
                $approval_data = json_decode($leaveApplication['approval_data']?? []);
                if($approval_data){
                    $usersThatcanApproveIds = $this->multiCycleApprovalConfigurationService->getAllLevelsApproversData($leaveApplication['approval_cycle_configuration_id'], $leaveApplication['staff_id']);
                    return in_array($currentUserId, $usersThatcanApproveIds);
                }
            }
            $usersThatcanApproveIds = $this->getUsersCanUpdateStatusFromBasicSettings($leaveApplication);
            return in_array($staffId, $usersThatcanApproveIds);
        }

        if($canViewHisTeamLeaveApplication){
            $myTeamIds = array_merge($directManagedStaffIds, $staffDepartmentManagedByUsersIds);
            if(in_array($leaveApplication['staff_id'], $myTeamIds)) {
                return true;
            }
        }

        return false;
    }


    public function getOnCreatedNotifiableStaffs($entityData){
        $employees = [];
        $isArray = is_array($entityData);
        if($isArray? $entityData['approval_cycle_configuration_id'] : $entityData->approval_cycle_configuration_id){
            $approval_data = json_decode($isArray ? ($entityData['approval_data']?? []):$entityData->approval_data);
            if($approval_data){
                $usersThatcanApproveIds = $this->multiCycleApprovalConfigurationService->getLevelApproversWithApproveOrRejectLeavePermission($isArray? $entityData['approval_cycle_configuration_id']:$entityData->approval_cycle_configuration_id, $approval_data->current_level, $isArray? $entityData['staff_id']:$entityData->staff_id);
                $employees = $usersThatcanApproveIds;
            }
        }else{
            $employees = $this->getUsersCanUpdateStatusFromBasicSettings($entityData);
        }


        $notifiable = $this->staffRepository->findWhereIn('id', $employees);

        return $notifiable;
    }

    public function canApproveOrRejectLeaveApplication($entityData){

        return $this->getOnCreatedNotifiableStaffs($entityData)->contains('id', '=', getAuthOwner('staff_id'));
    }

    public function getOnApproveNotifiableStaffs($entityData){
        if(!isset($entityData['approval_cycle_configuration_id']) || ($entityData['status'] != LeaveApplicationStatusUtil::Pending)){
            return $this->getOnFinalApproveOrRejectedNotifiableStaffs($entityData);
        }

        $approvalData = json_decode($entityData['approval_data']);
        $usersThatcanApproveIds = $this->multiCycleApprovalConfigurationService->getLevelApproversWithApproveOrRejectLeavePermission($entityData['approval_cycle_configuration_id'], $approvalData->current_level, $entityData['staff_id']);
        $notifiable = $this->staffRepository->findWhereIn('id', $usersThatcanApproveIds);
        return $notifiable;
     }

     public function getOnFinalApproveOrRejectedNotifiableStaffs($entityData){
        $employees[] = $entityData['staff_id'];
        if($entityData['staff_id'] != $entityData['created_by'] ){
            $employees[] = $entityData['created_by'];
        }

        $notifiable = $this->staffRepository->findWhereIn('id', $employees);

        if($entityData['created_by'] == 0 && isset($entityData['leave_application_approver'])){
            $notifiable[] = Owner::find(getCurrentSite('id'));
        }

        return $notifiable;
    }

    public function getApplicantsCount()
    {
        return $this->leaveApplicationRepository->getStaffConut();
    }

    private function logLeaveApplicationActivity(LeaveApplication $updatedApplication , LeaveApplication $leaveApplication)
    {
        $approverId = null;
        $leaveOldApprove = $leaveApplication['approved_by'];
        //being approved
        if ($updatedApplication->status == LeaveApplicationStatusUtil::Approved){
            $approverId = $updatedApplication['approved_by'];
            //show name in approved by instead of id
            $leaveApplication['approved_by'] = null;
            $updatedApplication['approved_by'] = $updatedApplication->approver?->name;
        }else if ($updatedApplication->status == LeaveApplicationStatusUtil::Pending){
            // being reverted to pending
            //hide approved by from logs as its null by now
            $updatedApplication['approved_by'] = null;
            $leaveApplication['approved_by'] = null;
        }else if($updatedApplication->status ==  LeaveApplicationStatusUtil::Rejected){
            $approverId = $leaveApplication['approved_by'];
            $leaveApplication['rejected by'] = null;
            $updatedApplication['rejected by'] =$updatedApplication->approver?->name;

            $updatedApplication['approved_by'] = null;
            $leaveApplication['approved_by'] = null;
        }
        $this->logUpdate($updatedApplication, $leaveApplication);

        //reset values
        $updatedApplication['approved_by'] = $approverId;
        $leaveApplication['approved_by'] = $leaveOldApprove;
    }

    public function getMyLeaveApplicationsCount($leaveApplicationType = null)
    {
        return $this->leaveApplicationRepository->getLeaveApplicationsCountByStaff([getAuthOwner('staff_id')], $leaveApplicationType);
    }

    public function getAllEmployeesLeaveApplications($leaveApplicationType = null)
    {
        $result = [];
        $recentApplications = $this->leaveApplicationRepository->getRecentLeaveApplication(0, null, [getAuthOwner('staff_id')], 'pending', $leaveApplicationType);
        $appEntityShowAction = resolve(AppEntityShowAction::class);
        foreach($recentApplications as $application){
            $data = $appEntityShowAction->handle(EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY, $application->id, 4, ['staff_leave_application']);
            if ($this->staffService->isAccessableStaffId($data->staff_id,true) && $this->isLeaveApplicationViewAllowed($data)) {
                $result[] = $this->formatApplicationForMobileApi($data);
            }
        }
        return $result;
    }

    public function getMyLastLeaveApplications($leaveApplicationType = null)
    {
        $myRecentApplications = $this->leaveApplicationRepository->getRecentLeaveApplication(getAuthOwner('staff_id'), 3, [], 'all', $leaveApplicationType);
        $result = [];
        $appEntityShowAction = resolve(AppEntityShowAction::class);
        foreach($myRecentApplications as $application){
            $data = $appEntityShowAction->handle(EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY, $application->id, 4, ['staff_leave_application']);
            $result[] = $this->formatApplicationForMobileApi($data);
        }
        return $result;
    }

    public function formatApplicationForMobileApi($data){
        $leaveApplication = $this->find($data->id);
        $aws = new Aws;
        $attachments = $data->attachments??[];
        foreach($attachments as $k => $attachment){
            $data->attachments[$k]->file->path = $aws->getPermanentUrl($data->attachments[$k]->file->path) ;
        }
        $dateFormat = getDateFormats('std')[getCurrentSite('date_format')];
        $staff = $data->staff_leave_application;
        $staffImage = AvatarURLGenerator::generate($staff->full_name, $staff->id, 126, $staff->photo);
        $mobileLogCreator = MobileActivityLogCreatorFactory::create(EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY);
        $config = $this->getMatchedApprovalConfigurations($leaveApplication);
        $leaveApplication->approval_cycle_configuration_id = !empty($config) ? $config->getId() : null;
        $mobileLogs = $mobileLogCreator->create($leaveApplication);
        $responseData = [
            'id'=> $data->id,
            'type'=> $data->type,
            'early_time'=> $data->early_time,
            'late_time'=> $data->late_time,
            'status'=> $data->status,
            'date_from'=> $data->date_from,
            'date_to'=> $data->date_to,
            'description'=> $data->description,
            'full_status' => $this->getLeaveApplicationStatus($data),
            'activity_log' => $mobileLogs,

            'staff_leave_application'=> [
                'id'=> $staff->id,
                'name'=> $staff->name,
                'middle_name'=> $staff->middle_name,
                'last_name'=> $staff->last_name,
                'email_address'=> $staff->email_address,
                'photo'=>  $staffImage,
                'designation_name'=> $staff->staff_info->designation->name??''
            ],
            'attachments'=> $data->attachments,
            'may_exceed_balance' => null,
            'shift_type'=> $data->shift_type ?? null,
            'leave_application_type'=> null,
            'modified'=> $data->modified,
        ];
        if($data->leave_application_type){
            $responseData['leave_application_type']= [
                'id'=> $data->leave_application_type->id,
                'name'=> $data->leave_application_type->name,
                'description'=> $data->leave_application_type->description,
                'color'=> $data->leave_application_type->color,
            ];
        }


        if($data->leave_application_approver){
            $actionByRole = '';

            if($staff->staff_info->staff_direct_manager){
                if($data->approved_by == $staff->staff_info->staff_direct_manager->id){
                    $actionByRole = __t('Direct Manager');
                }
            }

            $departmentManagers = $staff->staff_info->department->department_managers ?? [];
            foreach($departmentManagers as $departmentManager){
                if($departmentManager->manager_id == $data->approved_by){
                    $actionByRole = __t('Department Manager');
                    break;
                }
            }

            $approverImage = AvatarURLGenerator::generate($data->leave_application_approver->full_name, $data->leave_application_approver->id, 126, $data->leave_application_approver->photo);
            $responseData['leave_application_approver']=[
                'id'=> $data->leave_application_approver->id,
                'role'=> $actionByRole,
                'name'=> $data->leave_application_approver->name,
                'middle_name'=> $data->leave_application_approver->middle_name,
                'last_name'=> $data->leave_application_approver->last_name,
                'email'=> $data->leave_application_approver->email_address,
                'photo'=>  $approverImage
            ];
        }else{
            $responseData['action_by'] = null;
        }

        if($data->status == 'approved'){
            $responseData['attendance_permission_id'] = $data->attendance_permission_id;
        }

        if($data->status != 'pending'){
            $responseData['comment'] = $data->comment;
        }

        if(($data->status == 'pending')  && $data->leave_application_type){
            $LeaveBalanceForStaff = $this->reportsService->getLeaveBalanceForStaffIdByType($staff->id);
            $remainingBalance = $LeaveBalanceForStaff['leaves'][$data->leave_application_type->id]['remainingValue'] ?? 0;
            $responseData['may_exceed_balance'] = $remainingBalance < $data->days;
        }

        $userCanUpdateStatus = $this->authenticateUserCanUpdateStatus($data->id);
        $responseData['can_approve_reject'] = $leaveApplication->status == LeaveApplicationStatusUtil::Pending &&
            $userCanUpdateStatus;
        $responseData['can_update_status'] = $userCanUpdateStatus;
        $responseData['can_undo'] = $this->authenticateUserCanUndoApproval($data->id);
        $responseData['show_shift_type_input'] = $this->showShiftTypeInputInForm($data->data, true);
        return $responseData;
    }

    public function displayShiftTypeFilter(){
        return \App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift') || ($this->leaveApplicationRepository->getApplicationWithShiftTypeCount() > 0);
    }

    public function showShiftTypeInputInForm($data, $isUpdate = false){
        $multi_shift_enabled = \App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift');
        $employeeHasSecondaryShift = false;
        if ($data["staff_id"] ?? 0){
            $staffService = resolve(\App\Services\StaffService::class);
            $employeeHasSecondaryShift = $staffService->getHasSecondaryShift($data['staff_id'])['has_secondary_shift'];
        }
        if($multi_shift_enabled && $employeeHasSecondaryShift) {
            return true;
        } elseif ($isUpdate && (($data['shift_type'] ?? 0) &&($data['shift_type'] != \App\Utils\MultipleShiftTypeUtil::PRIMARY))) {
            return true;
        }
        return false;
    }

    public function getOwnerAndApplicantEmails($leaveApplication): array
    {
        $to = [];

        if ($leaveApplication->created_by == 0) {
            $to[] = [
                'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                'id' => -1,
                'email' => getCurrentSite('email')
            ];
            $to[] = [
                'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                'id' => -1,
                'email' => getCurrentSite('email')
            ];
        }

        $staffs = $this->staffService->find([$leaveApplication->staff_id, $leaveApplication->created_by])->toArray();

        foreach ($staffs as $staff) {
            $to[] = [
                'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                'id' => $staff['id'],
                'email' => $staff['email_address'],
            ];
        }
        return $to;
    }

    public function getStaffEmailsWhoCanApproveTheLeaveApplication($leaveApplication): array
    {
        $to = [];
        $applicationWithRelatedData = $this->leaveApplicationRepository->getApplicationWithRelatedData($leaveApplication->id);

        if (!$applicationWithRelatedData) {
            return [];
        }

        $directManagerCanApprove = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'direct_managers_can_approve');
        if ($directManagerCanApprove) {
            $directManager = $applicationWithRelatedData->applicant->staff_info?->direct_manager;
            if($directManager) {
                $to[] = [
                    'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                    'id' => $directManager->id,
                    'email' => $directManager->email_address
                ];
            }
        }

        $staffs = $this->staffService->find([$leaveApplication->staff_id, $leaveApplication->created_by])->toArray();

        foreach ($staffs as $staff) {
            $to[] = [
                'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                'id' => $staff['id'],
                'email' => $staff['email_address'],
            ];
        }
        return $to;
        $assignedDepartmentManagersCanApprove  = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'assigned_department_managers_can_approve');;
        if ($assignedDepartmentManagersCanApprove) {
            $applicantDepartments = $applicationWithRelatedData->applicant->department;
            foreach ($applicantDepartments as $department) {
                foreach ($department->managers as $manager) {
                    $to[] = [
                        'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                        'id' => $manager->id,
                        'email' => $manager->email_address
                    ];
                }
            }
        }

        $employeesCanApprove = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_can_approve');
        if ($employeesCanApprove) {
            $employeesThatCanApproveIds = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_that_can_approve_ids');
            $employeesThatCanApproveIdsArray = explode(',', $employeesThatCanApproveIds);
            resolve(StaffService::class)->find($employeesThatCanApproveIdsArray)->each(function($staff) use (&$to){
                $to[] = [
                    'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                    'id' => $staff->id,
                    'email' => $staff->email_address
                ];
            });
        }

        $staffs = $this->staffService->find([$leaveApplication->staff_id, $leaveApplication->created_by])->pluck('email_address')->toArray();
        foreach ($staffs as $staff) {
            $to[] = [
                'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                'id' => $staff['id'],
                'email' => $staff['email_address'],
            ];
        }

        return $to;
    }

    public function getStaffLatestPendingLeaveApplications($staffId)
    {
        $result = [];


        $recentApplicationsIds = $this->leaveApplicationRepository->getStaffLatestPendingLeaveApplicationsIds($staffId, 10);
        $recentApplications = $this->leaveApplicationRepository->model->whereIn('id',$recentApplicationsIds)->latest('created')->get();
        foreach($recentApplications as $application){
            if ($this->staffService->isAccessableStaffId($application->staff_id) && $this->isLeaveApplicationViewAllowed($application)) {
                $applicationFormattedData = $this->formatApplicationForStaffProfile($application);
                $result[] = $applicationFormattedData;
            }
        }


        return $result;
    }

    private function formatApplicationForStaffProfile($application)
    {
        $allowedActions = ["view"];
        if ( $this->authenticateUserCanUpdateStatus($application->id) ){
            $allowedActions[] = "approve_reject";
        }
        if(
            Permissions::checkPermission(PermissionUtil::EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS)
            ||
            Permissions::checkPermission(PermissionUtil::EDIT_DELETE_ALL_LEAVE_APPLICATIONS)
        ){
            $allowedActions = array_merge($allowedActions , [ "edit","delete"]) ;
        }
        $count = $application->days;
        $countType = __t("days");
        if ($count < 1 ){
            $countType = __t("hours");
        }
        return [
            "id" => $application->id,
            "leaveType"=>$application->leaveType?->name ?? '',
            "count"=> $application->days,
            "countType"=> $countType,
            "startDate"=> format_date($application->date_from),
            "endDate"=> format_date($application->date_to),
            "allowedActions"=>$allowedActions,
        ];

    }

    public function getOnSaveNotificationBody($employeeFullName, $dateFrom, $dateTo){
        if($dateFrom == $dateTo){
            return sprintf(
                __t("%s requested a new leave on %s"),
                $employeeFullName,
                __t(date("l, F j", strtotime($dateFrom)))
            );
        }else{
            return sprintf(
                __t("%s requested a new leave from %s to %s"),
                $employeeFullName,
                __t(date("l, F j", strtotime($dateFrom))),
                __t(date("l, F j", strtotime($dateTo)))
            );
        }
    }

    public function getOnDeleteNotificationBody($employeeFullName, $dateFrom, $dateTo){
        if($dateFrom == $dateTo){
            return sprintf(
                __t("%s has deleted the leave application dated %s"),
                $employeeFullName,
                __t(date("l, F j", strtotime($dateFrom)))
            );
        }else{
            return sprintf(
                __t("%s has deleted the leave application dated from %s, to %s"),
                $employeeFullName,
                __t(date("l, F j", strtotime($dateFrom))),
                __t(date("l, F j", strtotime($dateTo)))
            );
        }
    }

    public function getOnApproveNotificationBody($staffFullName, $dateFrom, $dateTo){
        if($dateFrom == $dateTo){
            return sprintf(
                __t("%s has approved the leave application dated %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom)))
            );
        }else{
            return sprintf(
                __t("%s has approved the leave application dated %s, to %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom))),
                __t(date("l, F j", strtotime($dateTo)))
            );
        }
    }

    public function getOnRejectNotificationBody($staffFullName, $dateFrom, $dateTo){
        if($dateFrom == $dateTo){
            return sprintf(
                __t("%s has rejected the leave application dated %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom)))
            );
        }else{
            return sprintf(
                __t("%s has rejected the leave application dated %s, to %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom))),
                __t(date("l, F j", strtotime($dateTo)))
            );
        }
    }

    public function getOnUndoRejectNotificationBody($staffFullName, $dateFrom, $dateTo){
        if($dateFrom == $dateTo){
            return sprintf(
                __t("%s has undone the rejection of the leave application dated %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom)))
            );
        }else{
            return sprintf(
                __t("%s has undone the rejection of the leave application dated %s, to %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom))),
                __t(date("l, F j", strtotime($dateTo)))
            );
        }
    }

    public function getOnUndoApproveNotificationBody($staffFullName, $dateFrom, $dateTo){
        if($dateFrom == $dateTo){
            return sprintf(
                __t("%s has undone the approval of the leave application dated %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom)))
            );
        }else{
            return sprintf(
                __t("%s has undone the approval of the leave application dated %s, to %s"),
                $staffFullName,
                __t(date("l, F j", strtotime($dateFrom))),
                __t(date("l, F j", strtotime($dateTo)))
            );
        }
    }

    public function getMatchedApprovalConfigurations($leaveApplicationData){
        $multiCycleApprovalMatchRequest = new MultiCycleApprovalMatchRequest();
        $multiCycleApprovalMatchRequest
            ->setConfigurationType(MultiCycleApprovalUtil::CONFIG_TYPE_LEAVES)
            ->setLeaveApplicationType($leaveApplicationData['type']);

        if(in_array($leaveApplicationData['type'], [AttendancePermissionTypesUtil::LEAVE, AttendancePermissionTypesUtil::HALF_LEAVE])){
            $multiCycleApprovalMatchRequest->setLeaveTypeId($leaveApplicationData['leave_type_id']);
        }
        $staffService = resolve(StaffService::class);
        $applicant = $staffService->find($leaveApplicationData['staff_id']);
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            if($applicant?->branch_id){
                $multiCycleApprovalMatchRequest->setBranchId($applicant->branch_id);
            }
        }
        if($applicant?->staff_info?->department_id){
            $multiCycleApprovalMatchRequest->setDepartmentId($applicant->staff_info?->department_id);
        }
        return (new MultiCycleApprovalConfigurationMatcher())->match($multiCycleApprovalMatchRequest);
    }

    function getRecordUpdatedActivityLogData($recordData, $oldRecordData)
    {
        $request = parent::getRecordUpdatedActivityLogData($recordData, $oldRecordData);
        if(empty($request)) return $request;
        $newData = $request->getNewData();
        $oldData = $request->getOldData();
        if(!empty($recordData['approval_data']) && is_array($recordData['approval_data'])){
            $recordData->setApprovalData($recordData['approval_data']);
        }
        $newData['status'] = $this->getLeaveApplicationStatus($recordData);
        $oldData['status'] = $this->getLeaveApplicationStatus($oldRecordData);

        $request->setNewData($newData);
        $request->setOldData($oldData);
        return $request;
    }

    public function getLeaveApplicationStatus($leaveApplication){
        if(!$leaveApplication->approval_cycle_configuration_id || ($leaveApplication->status != LeaveApplicationStatusUtil::Pending)){
            return LeaveApplicationStatusUtil::statusLabelTransList()[$leaveApplication->status];
        }
        return $this->multiCycleApprovalConfigurationService->getApprovalLevelStatus($leaveApplication);
     }

     public function getPostsCount($id){
        $noteRepository = resolve(PostRepository::class);
        return $noteRepository->getPostsCount($id ,PostTypeUtil::LEAVE_APPLICATION_TYPE);
     }


     public  function getDropDownStatusesData(){

        $statueses[LeaveApplicationStatusUtil::Pending] =  LeaveApplicationStatusUtil::getStatusTrans(LeaveApplicationStatusUtil::Pending);
        // $statueses[LeaveApplicationStatusUtil::PENDING_ON_ME] =  LeaveApplicationStatusUtil::getStatusTrans(LeaveApplicationStatusUtil::PENDING_ON_ME);
        $approvalLevelsStatusFilterOptions = resolve(MultiCycleApprovalConfigurationService::class)->getApprovalLevelsStatusFilterOptions(MultiCycleApprovalUtil::CONFIG_TYPE_LEAVES);
        $statueses = array_merge($statueses, $approvalLevelsStatusFilterOptions);
        $statueses[LeaveApplicationStatusUtil::Approved] =  LeaveApplicationStatusUtil::getStatusTrans(LeaveApplicationStatusUtil::Approved);
        $statueses[LeaveApplicationStatusUtil::Rejected] =  LeaveApplicationStatusUtil::getStatusTrans(LeaveApplicationStatusUtil::Rejected);
        return $statueses;
    }

    public function isActiveApprover($leaveApplicationStaffData, $staffId, $configuration, $currentLevel): bool{
        if($configuration){
            return $this->multiCycleApprovalConfigurationService->isActiveApproval($leaveApplicationStaffData, $configuration, $currentLevel, $staffId);
        }else{
            $usersThatcanApproveIds = $this->getUsersCanUpdateStatusFromBasicSettings($leaveApplicationStaffData);
            return in_array($staffId, $usersThatcanApproveIds);
        }

        return false;
    }

    /**
     * Get a list of user IDs who can update the status of a leave application based on settings and permissions.
     *
     * @param mixed $staffData Either:
     *    - an object with `direct_manager_id` and `staff_department_managers` (comma-separated string), OR
     *    - a full model with nested `staff_info.direct_manager_id` and `staff_info.department.managers`
     *
     * @return array Array of user IDs who are allowed to update the status.
     */
    private function getUsersCanUpdateStatusFromBasicSettings($staffData): array
    {
        $usersThatCanApproveIds = [0];

        $directManagersEnabled = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'direct_managers_can_approve');
        $departmentManagersEnabled = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'assigned_department_managers_can_approve');
        $employeesCanApprove = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_can_approve');

        // Employees manually assigned
        if ($employeesCanApprove) {
            $employeeIds = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_that_can_approve_ids');
            $usersThatCanApproveIds = array_merge($usersThatCanApproveIds, explode(',', $employeeIds));
        }

        // Direct manager
        if ($directManagersEnabled) {
            $directManagerId = null;
            if (is_object($staffData) && isset($staffData->direct_manager_id)) {
                $directManagerId = $staffData->direct_manager_id;
            }elseif (is_object($staffData) && isset($staffData->staff_leave_application->staff_info->direct_manager_id)) {
                $directManagerId = $staffData->staff_leave_application->staff_info->direct_manager_id;
            } elseif (is_array($staffData) && isset($staffData['staff_leave_application'])) {
                $staffLeaveApplication = $staffData['staff_leave_application'];;
                if($staffLeaveApplication && isset($staffLeaveApplication['staff_info'])){
                    $directManagerId = $staffLeaveApplication['staff_info']['direct_manager_id'];
                }
            }
            if ($directManagerId) {
                $usersThatCanApproveIds[] = $directManagerId;
            }
        }

        // Department managers
        if ($departmentManagersEnabled) {
            if (is_object($staffData) && isset($staffData->staff_department_managers)) {
                $managerIds = explode(',', $staffData->staff_department_managers);
                $usersThatCanApproveIds = array_merge($usersThatCanApproveIds, $managerIds);
            } elseif ((is_array($staffData) && isset($staffData['staff_leave_application'])) || is_array($staffData)) {
                $staffLeaveApplication = $staffData['staff_leave_application'];;

                if($staffLeaveApplication && isset($staffLeaveApplication['staff_info']['department']['managers'])){
                    foreach ($staffLeaveApplication['staff_info']['department']['managers'] as $manager) {
                        $usersThatCanApproveIds[] = $manager;
                    }
                }
            }
        }

        // Clean up, remove nulls and duplicates
        $usersThatCanApproveIds = array_filter(array_unique($usersThatCanApproveIds));

        // Filter by permission
        foreach ($usersThatCanApproveIds as $key => $userId) {
            if (!Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION, $userId)) {
                unset($usersThatCanApproveIds[$key]);
            }
        }
        return array_values($usersThatCanApproveIds); // Reset array keys
    }

    /**
     * Constructs the filter conditions and join clauses required to retrieve leave applications
     * for employees who are considered part of the current user's team.
     *
     * A team member is determined based on one of the following:
     * - The user is their direct manager (matched via `staff_info.direct_manager_id`)
     * - The user manages their department (matched via `staff_info.department_id`)
     *
     * This method returns:
     * - An array of JOIN conditions required to access `staffs` and `staff_info` data
     * - An `OrClause` condition
     *
     * @return array{
     *     joins: \App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\JoinCriteria[],
     *     condition: \App\Modules\LocalEntity\Prototype\DataStructure\OrClause
     * }
     */
    public function handleMyTeamFilter(){
        $joins[] = [];
        $filterOrClause = new OrClause();
        $joinCondition = new ConditionMeta();
        $curruentStaffId = getAuthOwner('staff_id');

        $joinCondition
            ->setOtherColumn("leave_applications.staff_id")
            ->setColName("id")
            ->setAlias("staffs.id")
            ->setAliasTable("staffs")
            ->setTable("staffs")
            ->setOperator( new FilterMeta(operator:'equal'));
        $joinCriteria = new JoinCriteria('left', 'staffs', [$joinCondition]);
        $joins[] = $joinCriteria;
        $joinCondition = new ConditionMeta();
        $joinCondition
            ->setOtherColumn("leave_applications.staff_id")
            ->setColName("staff_id")
            ->setAlias("staff_info.staff_id")
            ->setAliasTable("staff_info")
            ->setTable("staff_info")
            ->setOperator( new FilterMeta(operator:'equal'));
        $joinCriteria = new JoinCriteria('left', 'staff_info', [$joinCondition]);
        $joins[] = $joinCriteria;
        $filterOrClause->pushChildren(new FilterMeta('staff_info.direct_manager_id', 'equal', $curruentStaffId));

        $staffRepository = resolve(StaffRepository::class);
        $loggedInStaffManagedDepartment =  $staffRepository->getAllStaffManagedDeparments($curruentStaffId);
        if($loggedInStaffManagedDepartment){
            $filterOrClause->pushChildren(new FilterMeta('staff_info.department_id', 'in', array_column($loggedInStaffManagedDepartment, 'id')));
        }
        return ['joins'=> $joins, 'condition'=> $filterOrClause];
    }

}
