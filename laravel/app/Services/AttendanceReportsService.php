<?php

namespace App\Services;
use App\Entities\StaffAttendanceCriteria;
use App\Entities\StaffCriteria;
use App\Facades\Branch;
use App\Facades\Formatter;
use App\Facades\Permissions;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Facades\Staff;
use App\Repositories\AttendanceReportsRepository;
use App\Repositories\BaseRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\SelectCriteria;
use App\Services\AttendanceCalculator\Entities\Fiscal;
use App\Utils\AttendancePermissionTypesUtil;
use App\Utils\BusinessNumberFieldsUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\MultipleShiftTypeUtil;
use App\Utils\PermissionUtil;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use App\Helpers\FilterOperations;
use App\Services\Attendance\AttendanceMultipleReportExport;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class AttendanceReportsService
 * @package App\Services
 *
 * @property AttendanceReportsRepository $reportsRepository
 */
class AttendanceReportsService extends ReportsService
{
    /** @var StaffService $staffService */
    private $staffService;
    private $rawParameters = [];

    private $isExporting = false;

    public function isExporting(): bool
    {
        return $this->isExporting;
    }

    public function setIsExporting(bool $isExporting): void
    {
        $this->isExporting = $isExporting;
    }

    private bool $considerFutureLeaves = true;
    public function __construct(AttendanceReportsRepository $attendanceReportsRepository, StaffService $staffService, private StaffLeaveTypeCreditService $staffLeaveTypeCreditService)
    {
        $this->staffService = $staffService;
        $this->staffService->getRepo()->setPaginateWith([]);
        parent::__construct($attendanceReportsRepository);
    }

    private function staffFilter($name = 'name', $isMultiple = true, $isRequired = false)
    {
        $attributes = ['placeholder' => sprintf(__t('Search by %s, %s or %s'),__t('Employee Name'), __t('ID'), __t('Email')), 'data-staff-url' => route('owner.staff.search',['allow_inactive' => true, 'get_branch_suspended' => 1])];
        if ($isMultiple) {
            $attributes['multiple'] = 'multiple';
        }
        if ($isRequired) {
            $attributes['required'] = 'required';
        }
        $staffList = [];
        $staffAttributes = [];
        $staffRepo = $this->staffService->getRepo();
        if (isset($this->parameters['name']) && !empty($this->parameters['name'])) {
            $staffObjects = $staffRepo->find($this->parameters['name']);
            if (!is_array($this->parameters['name'])) {
                $staffObjects = [$staffObjects];
            }
            foreach ($staffObjects as $staff) {
                $staffList[] = Staff::getStaffOptionFormated($staff) ;
            }
        }
        return [
            'simple' => true,
            'label' => '',
            'type' => 'selectStaffV2',
            'inputClass' => 'form-control staff-dropdown',
            'div' => 'col-md-6 form-group',
            'name' => $name . ($isMultiple ? '[]' : ''),
            'after' => '<i class="input-icon fal fa-search"></i></div>',
            'before' => '<div class="form-group-icon">',
            'attributes' => $attributes,
            'optionsAttributes' => $staffAttributes,
            'options' => $staffList
        ];
    }

    private function dateFilter($name = 'date', $isRequired = true, $defaultValue = null)
    {
        $attributes = ['placeholder' => sprintf(__t('Filter by %s'), __t(Str::title(str_replace('_', ' ', $name))))];;
        if ($isRequired)
            $attributes['required'] = 'required';
        return [
            'simple' => true,
            'label' => '',
            'inputClass' => 'form-control',
            'div' => 'col-md-3 form-group',
            'after' => '<i class="input-icon far fa-calendar-day"></i></div>',
            'before' => '<div class="form-group-icon">',
            'name' => $name,
            'attributes' => $attributes,
            'type' => 'date',
            'value' => $defaultValue
        ];
    }

    private function relationListFilter($name, $repo, $conditions = [], $fields = ['id', 'name'])
    {
        /** @var BaseRepository $repo */
        $options = $repo->pushCriteria(new CustomFind($conditions))->list($fields);
        return [
            'simple' => true,
            'label' => '',
            'inputClass' => 'form-control select-filter',
            'div' => 'col-md-3 form-group',
            'name' => $name,
            'options' => $options,
            'attributes' => ['placeholder' => sprintf(__t('Filter by %s'), __t(ucfirst(humanize($name))))],
            'type' => 'select'
        ];
    }

    private function branchesFilter($name = 'branch')
    {
        $branches = Branch::getStaffBranchesSuspended();
        return [
            'simple' => true,
            'label' => '',
            'inputClass' => 'form-control select-filter',
            'div' => 'col-md-3 form-group',
            'name' => $name,
            'options' => $branches,
            'attributes' => ['placeholder' => sprintf(__t('Filter by %s'), __t('Branch'))],
            'type' => 'select'
        ];
    }

    private function shiftTypeFilter()
    {
        $shifts = MultipleShiftTypeUtil::getTypes();
        return [
            'simple' => true,
            'label' => '',
            'inputClass' => 'form-control select-filter',
            'div' => 'col-md-3 form-group',
            'name' => 'shift_type',
            'options' => $shifts,
            'value' => MultipleShiftTypeUtil::PRIMARY,
            'attributes' => [
                'placeholder' => __t('Shift Type'),
                'required' => 'required',
            ],
            'type' => 'select'
        ];
    }

    private function getStaffListPagination($includeActiveStatus = true, $fromMobileApi = false , $onlyInactive = false, $staffNameWithCode = false)
    {
        $staffCriteria = new StaffAttendanceCriteria(
            $this->parameters['name'] ?? [],
            $this->parameters['branch'] ?? null,
            $this->parameters['department'] ?? null,
            $this->parameters['designation'] ?? null,
            $this->parameters['shift'] ?? null,
            [],
            $this->parameters['status'] ?? null,
            $this->parameters['employment_type'] ?? null,
            $this->parameters['employment_level'] ?? null
        );

        $staffRepo = $this->staffService->getRepo();

        if ($includeActiveStatus) {
            $staffRepo->pushCriteria(new CustomFind([['field' => 'active', 'value' => 1]]));
        }

        if ($onlyInactive && !$includeActiveStatus){
            $staffRepo->pushCriteria(new CustomFind([['field' => 'active', 'value' => 0]]));
        }

        if(Plugins::pluginActive(PluginUtil::BranchesPlugin) && !$fromMobileApi){
            $branchesIds = Branch::getStaffBranchesIDsSuspended();
            $staffRepo->pushCriteria(new CustomFind([
                ['operation' => FilterOperations::IN_FILTER_OPERATION, 'field' => 'branch_id', 'value' => $branchesIds]
            ]));
        }

        $staffRepo->pushCriteria($staffCriteria->getConditionsCustomFind());
        if ($this->isExporting()){
            $staffRepo->pushCriteria(new SelectCriteria('staffs.*', false));
            $this->pagination = $staffRepo->all();
        }else{
            $this->pagination = $staffRepo->paginate();
        }

        $staffList = [];
        foreach ($this->pagination as $staff) {
            $staffList[$staff->id] = $staff->full_name . ($staffNameWithCode? " #".$staff->code: '');
        }
        return $staffList;
    }

    private function getAllStaffRawQuery()
    {
        $staffCriteria = new StaffCriteria(
            $this->parameters['name'] ?? [],
            $this->parameters['branch'] ?? null,
            $this->parameters['department'] ?? null,
            $this->parameters['designation'] ?? null,
            $this->parameters['shift'] ?? null
        );
        return $this->staffService->getAttendanceDayCriteriaStaff($staffCriteria, true);
    }

    private function handleDates()
    {
        $this->rawParameters = $this->parameters;
        $this->parameters['from_date'] = Formatter::formatForDB($this->parameters['from_date'], 'date');
        $this->parameters['to_date'] = Formatter::formatForDB($this->parameters['to_date'], 'date');
        if (strtotime($this->parameters['from_date']) > strtotime($this->parameters['to_date'])) {
            $from = $this->parameters['to_date'];
            $this->parameters['to_date'] = $this->parameters['from_date'];
            $this->parameters['from_date'] = $from;
        }
        if (strtotime($this->parameters['from_date']) < strtotime(date('2012-01-01'))) {
            throw ValidationException::withMessages([
                'from_date' => [__t("You can't view report from dates older than 2012-01-01")]
            ]);
        }
        $startDateCheck = Carbon::parse($this->parameters['from_date'])->copy();
        $startDate = Carbon::parse($this->parameters['from_date'])->copy();
        $endDate = Carbon::parse($this->parameters['to_date']);
        if ($endDate->gt($startDateCheck->addYear()->subDay())) {
            throw ValidationException::withMessages([
                'to_date' => [__t("The report will not contain more than one year")]
            ]);
        }
        $this->data = ['data' => [], 'dates' => CarbonPeriod::create($startDate, $endDate->endOfDay())];
    }

    private function getCommonFiltersForStaff($except = [])
    {
        $filters = [];
        if (!in_array('name', $except)) {
            $filters['name'] = $this->staffFilter();
        }
        $filters += [
            'from_date' => $this->dateFilter('from_date', true, formatForView(Carbon::now()->startOfMonth()->format('Y-m-d'), 'date')),
            'to_date' => $this->dateFilter('to_date', true, formatForView(date('Y-m-d'), 'date')),
        ];
        if (!in_array('designation', $except)) {
            $designationsRepo = App::make('App\Repositories\DesignationRepository');
            $filters['designation'] = $this->relationListFilter('designation', $designationsRepo);
        }
        if (!in_array('department', $except)) {
            $departmentsRepo = App::make('App\Repositories\DepartmentRepository');
            $filters['department'] = $this->relationListFilter('department', $departmentsRepo);
        }
        if (!in_array('branch', $except) && Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $filters['branch'] = $this->branchesFilter();
        }
        if (!in_array('shift', $except)) {
            $shiftsRepo = App::make('App\Repositories\ShiftRepository');
            $filters['shift'] = $this->relationListFilter('shift', $shiftsRepo);
        }
        foreach ($except as $item) {
            unset($filters[$item]);
        }
        return $filters;
    }

    public function setShiftsFilters()
    {
        $employees_count = $this->reportsRepository->employeesHasSecondaryShiftCount();
        $allocated_shifts_count = $this->reportsRepository->allocatedShiftsCountByType(MultipleShiftTypeUtil::SECONDARY);

        if ($employees_count == 0 && $allocated_shifts_count == 0) {
            $this->filters = $this->filters = $this->getCommonFiltersForStaff();
        } else {
            $this->filters = array_merge(
                $this->filters = $this->getCommonFiltersForStaff(),
                ['shift_type' => $this->shiftTypeFilter()]
            );
        }
    }

    public function setShiftData()
    {
        $from_date = $this->parameters['from_date'];
        $to_date   = $this->parameters['to_date'];
        $this->handleDates();
        $staffList = $this->getStaffListPagination();
        $staffIDs = array_keys($staffList);
        $records = $this->reportsRepository->getShiftReportsData($staffIDs, $this->parameters['from_date'], $this->parameters['to_date'], $this->parameters['shift_type'] ?? null);
        foreach ($records as $record) {
            $this->data['data'][$record->id][] = ['id' => $record->id, 'name' => $record->name, 'shift' => $record->shiftName, 'shiftID' => $record->shiftID, 'allocatedShiftID' => $record->allocatedShiftID];
        }

        $this->parameters['from_date'] = $from_date;
        $this->parameters['to_date']   = $to_date;
    }

    public function setAttendanceMultipleFilters()
    {
        $filters['employee_status'] = $this->getEmployeeStatusFilter();
        $this->filters = array_merge($this->getCommonFiltersForStaff() ,$filters );
    }

    public function setAttendanceMultipleData($ignoreStaffLimitation = false)
    {
        $from_date = $this->parameters['from_date'];
        $to_date   = $this->parameters['to_date'];
        $this->handleDates();
        $employeeStatus = $this->parameters['employee_status'] ?? 1 ;
        $staffList = $this->getStaffListPagination(includeActiveStatus: $employeeStatus ,onlyInactive: !$employeeStatus, staffNameWithCode: true );
        $staffIDs = array_keys($staffList);
        /** ignore limitation in DashboardWidget statistic  */
        if ($ignoreStaffLimitation){
            $this->staffService->getRepo()->model->getQuery()->limit(null);
            $this->staffService->getRepo()->model->getQuery()->offset = null;
        }
        $allStaffIDsReturn = $this->getAllStaffIDs();
        $allStaffIDs = $allStaffIDsReturn['all'];
        if (empty($allStaffIDs)) {
            return $this->data['data'] = [];
        }
        $allStaffActiveIDs = $employeeStatus ?  $allStaffIDsReturn['active']  :  $allStaffIDs;
        $this->checkIfCalculationUpToData($allStaffActiveIDs, $this->parameters['from_date'], $this->parameters['to_date']);
        $this->data['statistics'] = $this->getAttendanceStatistics($allStaffIDs, $this->parameters['from_date'], $this->parameters['to_date']);
        $records = $this->reportsRepository->getAttendanceMultipleReportsData($staffIDs, $this->parameters['from_date'], $this->parameters['to_date']);
        $recordsData = [];
        foreach ($records as $record) {
            if (isset($record->sign_in)) {
                $record->sign_in = Formatter::formatForView($record->sign_in, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC, null, ['time_only' => true]);
            }
            if (isset($record->sign_out)) {
                $record->sign_out = Formatter::formatForView($record->sign_out, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC, null, ['time_only' => true]);
            }

            if ($record->shift_type === MultipleShiftTypeUtil::SECONDARY_NUMBER) {
                $recordsData[$record->id][MultipleShiftTypeUtil::SECONDARY][$record->date] = $record;
            } else {
                $recordsData[$record->id][MultipleShiftTypeUtil::PRIMARY][$record->date] = $record;
            }
        }
        $this->data['data'] = $recordsData;
        $this->data['staff'] = $staffList;

        $this->parameters['from_date'] = $from_date;
        $this->parameters['to_date']   = $to_date;
    }

    public function setAttendanceSingleFilters()
    {
        $this->filters = array_merge(
            ['name' => $this->staffFilter('name', false, true)],
            $this->getCommonFiltersForStaff(['name', 'shift', 'department', 'designation', 'branch']),
            ['shift_type' => $this->shiftTypeFilter()],
        );
    }


    public function setLeaveBalanceFilters()
    {
        $this->filters =  $this->getCommonFiltersForStaff(['name', 'shift', 'department', 'designation', 'branch', 'status']);
    }

    public function setAttendanceSingleData()
    {
        $from_date = $this->parameters['from_date'];
        $to_date   = $this->parameters['to_date'];
        $this->handleDates();
        $dates = $this->data['dates'];
        $shift_type = $this->parameters['shift_type'] ?? null;
        $this->data['dates'] = [];
        foreach ($dates as $date) {
            $this->data['dates'][$date->format('Y')][$date->format('F')][str_pad($date->day, 2, '0' , STR_PAD_LEFT)] = 1;
        }
        $this->parameters['name'] = [$this->parameters['name']];
        $staffIDs = array_keys($this->getStaffListPagination());
        $allStaffIDsReturn = $this->getAllStaffIDs();
        $allStaffIDs = $allStaffIDsReturn['all'];
        if (empty($allStaffIDs)) {
            return $this->data['data'] = [];
        }
        $allStaffActiveIDs = $allStaffIDsReturn['active'];
        $this->checkIfCalculationUpToData($allStaffActiveIDs, $this->parameters['from_date'], $this->parameters['to_date']);
        $this->data['statistics'] = $this->getAttendanceStatistics($staffIDs, $this->parameters['from_date'], $this->parameters['to_date'], $shift_type);
        $records = $this->reportsRepository->getAttendanceMultipleReportsData($staffIDs, $this->parameters['from_date'], $this->parameters['to_date'], $shift_type);
        $recordsData = [];
        foreach ($records as $record) {
            if (isset($record->sign_in)) {
                $record->sign_in = Formatter::formatForView($record->sign_in, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC, null, ['time_only' => true]);
            }
            if (isset($record->sign_out)) {
                $record->sign_out = Formatter::formatForView($record->sign_out, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC, null, ['time_only' => true]);
            }
            $carbonDate = Carbon::parse($record->date);
            $recordsData[$carbonDate->format('Y')][$carbonDate->format('F')][$carbonDate->format('d')] = $record;
        }

        $this->data['data'] = $recordsData;
        $this->parameters['from_date'] = $from_date;
        $this->parameters['to_date']   = $to_date;
    }


    public function setLeaveBalanceData($fromMobileApi = false)
    {

        $shouldConsiderFuture = $this->getConsiderFutureLeaves();
        $this->handleDates();
        $from_date = $this->parameters['from_date'];
        $to_date   = $this->parameters['to_date'];
        $dates = $this->data['dates'];
        $this->data['dates'] = [];
        foreach ($dates as $date) {
            $this->data['dates'][$date->format('Y')][$date->format('F')][str_pad($date->day, 2, '0' , STR_PAD_LEFT)] = 1;
        }
        $staffIDs = array_keys($this->getStaffListPagination(false, $fromMobileApi));
        $allStaffIDsReturn = $this->getAllStaffIDs();
        $allStaffIDs = $allStaffIDsReturn['all'];
        if (empty($allStaffIDs)) {
            return $this->data['data'] = [];
        }

        $fiscal_day = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_day');
        $fiscal_month = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_month');
        $fiscal = new Fiscal($fiscal_day,$fiscal_month);
        $fiscal_date = $fiscal->getFiscalDateFromDate(Carbon::createFromDate($from_date)->toDateString());
        $nextYear =  Carbon::createFromDate($fiscal_date)->addYear()->format('Y-m-d');
        $yearRange = [$fiscal_date, $nextYear];
        $day_before  = Carbon::createFromDate($this->parameters['from_date'])->addDay('-1');
        if ($day_before->lt($fiscal_date)) {
            $day_before= $fiscal_date;
        } else {
            $day_before =  $day_before->format('Y-m-d');
        }
        $recordsData = $this->reportsRepository->getLeaveBalanceReportData($staffIDs, $from_date, $to_date, $yearRange);
        $creditBefore = $this->reportsRepository->getLeaveBalanceReportData($staffIDs, $fiscal_date, $day_before, $yearRange);
        $leaveTypes = $this->reportsRepository->getLeaveTypes();
        $leavePolices  = $this->reportsRepository->getLeavePolicies();
        if (!empty($this->parameters['branch']) && Plugins::pluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)) {
            $this->data['branch'] =  $this->reportsRepository->getBranch($this->parameters['branch']);
        }
        if (!empty($this->parameters['shift'])) {
            $this->data['shift'] =  $this->reportsRepository->getShift($this->parameters['shift']);
        }

        $staffs = [];
        $visitedLeaves = [];
        foreach ($recordsData as $record) {
            $visitedLeaves[] = $record->leave_type_id;
            $staffs[$record->staff_id][$record->leave_type_id] = $record;
        }
        $creditBeforeArray = [];
        foreach ($creditBefore as $credit) {
            $visitedLeaves[] = $credit->leave_type_id;
            $creditBeforeArray[$credit->staff_id][$credit->leave_type_id] = $credit;
        }

        $futureCredit = [];
        if ($shouldConsiderFuture){
            $futureCredit = $this->reportsRepository->getLeaveBalanceFutureLeaves($staffIDs , $from_date , $to_date);
        }
        $futureCreditArray = [];
        foreach ($futureCredit as $fc) {
            $visitedLeaves[] = $fc->leave_type_id;
            if ($fc->from_date == $fc->to_date){
                $daysDiff = ( $fc->type == AttendancePermissionTypesUtil::LEAVE ?  1 : 0.5 );
            }else{
                $dayDateFrom = Carbon::parse($fc->from_date);
                $dayDateTo = Carbon::parse($fc->to_date);
                $daysDiff = $dayDateTo->diffInDays($dayDateFrom) + 1;
            }
            $fc->leaves = $daysDiff;
            $futureCreditArray[$fc->staff_id][$fc->leave_type_id][] = $fc;
        }
        $newData = [];
        foreach ($staffIDs as $staffID) {
            $customLeaveTypesCredit = [];
            $fiscalYear = $this->getFiscalYearByAtttendanceDayDate($from_date);
            $customLeaveTypesCredit = $this->staffLeaveTypeCreditService->getDataByFisaclYear($staffID, $fiscalYear, true);
            $staff = $this->pagination->where('id', $staffID)->first();
            foreach ($leaveTypes as $leaveType) {
                $leaveTypeCustomCreditNote = '';
                $customCredit = $leaveType->max_leave_days_allowed_per_year;
                if(isset($customLeaveTypesCredit['leave_types_credit'][$leaveType->id])){
                    $customCredit = $customLeaveTypesCredit['leave_types_credit'][$leaveType->id]['credit'];
                    $leaveTypeCustomCreditNote = $customLeaveTypesCredit['leave_types_credit'][$leaveType->id]['note'];
                }
                if (isset($staffs[$staffID][$leaveType->id])) {
                    $total =  ($staffs[$staffID][$leaveType->id]->leaves??0 +  $creditBeforeArray[$staffID][$leaveType->id]->leaves??0);
                    $leavePolicy = $this->pagination->where('id', $staffID)->first()->staff_info->leave_policy_id ?? null;
                    $credit = 0;
                    if (!empty($leavePolices->where('id', $leavePolicy)->first()->types) && !empty($leavePolices->where('id', $leavePolicy)->first()->types->where('id', $leaveType->id)->first() )) {
                        $visitedLeaves[] = $leaveType->id;
                        $remain =  ($customCredit -  $total);
                        $credit = $customCredit;
                    } else {
                        $remain =  isset($customLeaveTypesCredit['leave_types_credit'][$leaveType->id]) ? ($customCredit -  $total) : 0;
                        $credit =  isset($customLeaveTypesCredit['leave_types_credit'][$leaveType->id]) ? $customCredit : 0;
                    }
                    $newData[$staffID]["leaves"][$leaveType->id] = [
                        "leave_type_name" => $leaveType->name,
                        "leaves" => $staffs[$staffID][$leaveType->id]->leaves, "credit_before" => $creditBeforeArray[$staffID][$leaveType->id]->leaves??0,
                        "total" => $total,
                        "remaining" => $remain,
                        "note" => $leaveTypeCustomCreditNote,
                        "credit" => $credit
                    ];
                } else {
                    $leavePolicy = $this->pagination->where('id', $staffID)->first()->staff_info->leave_policy_id ?? null;
                    if ((!empty($leavePolices->where('id',  $leavePolicy)->first()->types) && !empty($leavePolices->where('id',  $leavePolicy)->first()->types->where('id', $leaveType->id)->first() )))
                     {
                        $visitedLeaves[] = $leaveType->id;

                        if (isset($creditBeforeArray[$staffID][$leaveType->id])) {
                            $remain = ($customCredit -  $creditBeforeArray[$staffID][$leaveType->id]->leaves??0);
                        } else {
                            $remain = $customCredit;
                        }
                    } else {
                        $remain =  isset($customLeaveTypesCredit['leave_types_credit'][$leaveType->id]) ? ($customCredit ) : 0;
                        $customCredit =  $remain;
                    }
                    $newData[$staffID]['leaves'][$leaveType->id] = [
                        "leave_type_name" => $leaveType->name,
                        "leaves" => 0, "credit_before" => $creditBeforeArray[$staffID][$leaveType->id]->leaves??0,
                        "total" => $creditBeforeArray[$staffID][$leaveType->id]->leaves ?? 0,
                        "remaining" => $remain,
                        "note" => $leaveTypeCustomCreditNote,
                        "credit" => $customCredit

                    ];
                }
                $newData[$staffID]['leave_color'][$leaveType->id] = $leaveType->color;
                if (isset($futureCreditArray[$staffID][$leaveType->id]) && isset($newData[$staffID]['leaves'][$leaveType->id]) && $shouldConsiderFuture){
                    $futureLeaveTypeLeaves = 0;
                    foreach ($futureCreditArray[$staffID][$leaveType->id] as $futureLeaveTypeLeave){
                        $futureLeaveTypeLeaves += $futureLeaveTypeLeave->leaves;
                    }
                    $newData[$staffID]['leaves'][$leaveType->id]['balance'] =  $newData[$staffID]['leaves'][$leaveType->id]['remaining'] +  $newData[$staffID]['leaves'][$leaveType->id]['total'];

                    $newData[$staffID]['leaves'][$leaveType->id]['remaining'] -= $newData[$staffID]['leaves'][$leaveType->id]['credit'] == 0 ? 0 :  $futureLeaveTypeLeaves;
                    $newData[$staffID]['leaves'][$leaveType->id]['future'] = $futureLeaveTypeLeaves;
                }
            }
            $newData[$staffID]['staff'] = ['name' => $staff->full_name, 'id' => $staff->id];
            $newData[$staffID]['totals'] = [
                "leaves" => array_sum(array_column($newData[$staffID]['leaves']??[], 'leaves')), "credit_before" => array_sum(array_column($newData[$staffID]['leaves']??[], 'credit_before')),
                "total" => array_sum(array_column($newData[$staffID]['leaves']??[], 'total')),
                "future" => array_sum(array_column($newData[$staffID]['leaves']??[], 'future')),
                "remaining" => array_sum(array_column($newData[$staffID]['leaves']??[], 'remaining'))
            ];
        }

        $visitedLeaves = array_unique($visitedLeaves);

        foreach ($newData as &$data) {
          if(isset($data['leaves']) && is_array($data['leaves'])){
            foreach ($data['leaves']  as  $key => $leave) {
                if (!in_array($key, $visitedLeaves)) {
                     unset($data['leaves'][$key]);
                    unset($data['leave_color'][$key]);
                }
            }
          }
        }
        $this->data['data'] = $newData;
        $this->data['now'] = Carbon::now();
        $this->data['day_before'] = $day_before;
        $this->data['day_after'] = Carbon::createFromDate($this->parameters['to_date'])->addDay('+1')->format('Y-m-d');
        $this->data['fiscal_date'] = $fiscal_date;
        $this->data['next_year'] = $nextYear;
        $this->data['tax_label'] = BusinessNumberFieldsUtil::getByCountryCodeAndField('bn1', getAuthOwner('country_code'));
        $this->data['leave_types'] = $leaveTypes->filter(function ($leave) use ($visitedLeaves) {
            return in_array($leave->id, $visitedLeaves);
        });
    }


    private function getAttendanceStatistics(array $staffIDs, $from_date, $to_date, $shift_type = null)
    {
        $statistics = $this->reportsRepository->getAttendanceMultipleReportsStatics($staffIDs, $from_date, $to_date, $shift_type);
        $statisticsData = ['present' => 0, 'absent' => 0, 'leaves' => [], 'holiday' => 0];
        foreach ($statistics as $statistic) {
            if ($statistic->status == 'present') {
                $statisticsData['present'] += $statistic->count;
                if (isset($statistic->name)) {
                    if (!isset($statisticsData['leaves'][$statistic->name])) {
                        $statisticsData['leaves'][$statistic->name] = ['id' => $statistic->leave_type_id, 'color' => $statistic->color ?? '#000000', 'name' => $statistic->name, 'count' => 0];
                    }
                    $statisticsData['leaves'][$statistic->name]['count'] += $statistic->leave_count;
                }
            } else if ($statistic->status == 'absent') {
                $statisticsData['absent'] += $statistic->count;
            } else if ($statistic->status == 'day_off' && $statistic->day_off_type == 'holiday') {
                $statisticsData['holiday'] += $statistic->count;
            } else if ($statistic->status == 'leave') {
                if (!isset($statisticsData['leaves'][$statistic->name]))
                    $statisticsData['leaves'][$statistic->name] = ['id' => $statistic->leave_type_id, 'color' => $statistic->color ?? '#000000', 'name' => $statistic->name, 'count' => 0];
                $statisticsData['leaves'][$statistic->name]['count'] += $statistic->count;
            }
        }
        return $statisticsData;
    }

    private function checkIfCalculationUpToData(array $staffIDs, $from_date, $to_date)
    {
        $isUpToDate = true;
        if (!request()->isXmlHttpRequest()) {
            $isUpToDate = $this->reportsRepository->checkDraftLogs($staffIDs, $from_date, $to_date) == 0;
            if ($isUpToDate) {
                $isUpToDate = count($this->reportsRepository->checkDaysCount($staffIDs, $from_date, $to_date, count($staffIDs))) == 0;
            }
            if (!$isUpToDate) {
                unset($this->parameters['_token']);
                $this->data['message'] = sprintf(__t('Some days not calculated yet, %s'), '<a class="alert-link" target="_blank" href="' . route('owner.attendance_days.calculation.create', $this->rawParameters) . '">' . __t('Calculate now') . '</a>');
            }
        }
        return $isUpToDate;
    }

    private function getAllStaffIDs() {
        $allStaff = $this->getAllStaffRawQuery();
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $staffService = resolve(StaffService::class);
            $accessableStaffMap = $staffService->getAccessibleStaffIds(null, true, true);
            if(is_array($allStaff)){
                $allStaff = collect($allStaff);
            }
            $allStaff = $allStaff->filter(fn($staff, $key) => isset($accessableStaffMap[$staff->id]));
        }
        $allStaffIDs = [];
        $allStaffActiveIDs = [];
        if ($allStaff) {
            foreach ($allStaff as $index => $staff) {
                if (isset($allStaffIDs[$staff->id]))
                    unset($allStaff[$index]);
                if ($staff->active)
                    $allStaffActiveIDs[$staff->id] = 1;
                $allStaffIDs[$staff->id] = 1;
            }
            $allStaffIDs = array_keys($allStaffIDs);
            $allStaffActiveIDs = array_keys($allStaffActiveIDs);
        }
        return ['all' => $allStaffIDs, 'active' => $allStaffActiveIDs];
    }

    public function getAttendanceDayFlagsGrouped($filterOptions) {
        $result = $this->reportsRepository->getAttendanceDayFlagsGrouped($filterOptions);
        return $result;
    }

    public function getEarlyLeaveDays($filterOptions) {
        $result = $this->reportsRepository->getEarlyLeaveAttendanceDaysGrouped($filterOptions);
        return $result;
    }

    public function getLateDays($filterOptions) {
        $result = $this->reportsRepository->getLateAttendanceDaysGrouped($filterOptions);
        return $result;
    }

    // public function getLeaveBalanceForStaffIdByType($staffId, Carbon $startDate, Carbon $endDate)
    public function getLeaveBalanceForStaffIdByType($staffId, $fromMobileApi = false, $fiscalYear = null)
    {
        if($fiscalYear){
            $fiscalYear = $this->getSelectedFiscalYearStartAndEnd($fiscalYear);
        }else{
            $fiscalYear = $this->getCurrentFiscalYearStartAndEnd();
        }

        $this->setParameters([
            'from_date' => $fiscalYear['startDate'],
            'to_date' => $fiscalYear['endDate'],
            'name' => [$staffId]
        ]);

        $this->setConsiderFutureLeaves(true);
        $this->setLeaveBalanceData($fromMobileApi);
        $data = $this->data['data'];
        $result = null;
        foreach ($data as $value) {
            $result = $value;
            if(!empty($result['leaves']))
            {
                foreach ($result['leaves'] as $key => $leave) {
                    $result['leaves'][$key]['remainingValue']  = $result['leaves'][$key]['remaining'];
                    $result['leaves'][$key]['remaining'] .= " " . __t("Days");
                }
                $result['totals']['remaining'] .= " " . __t("Days");
            }
            break;
        }
        return $result;
    }

    private function getCurrentFiscalYearStartAndEnd(){

        $currentDate = new Carbon();
        $currentTimestamp = $currentDate->getTimestamp();

        $startDate = $this->getFiscalDate();
        $startDate->setTime(0,0,0);

        $endDate = $this->getFiscalDate();
        $endDate->subtract("days", 1);
        $endDate->setTime(23,59,59);

        if($currentTimestamp <= $startDate->getTimestamp()){
            $startDate = $startDate->subtract("year", 1);
        } else {
            $endDate = $endDate->add("year", 1);
        }

        return [
            'startDate' => $startDate->format('Y-m-d H:i:s'),
            'endDate' => $endDate->format('Y-m-d H:i:s')
        ];
    }
    public function getSelectedFiscalYearStartAndEnd($fiscalYear){
        $currentDate = new Carbon();
        $currentTimestamp = $currentDate->getTimestamp();
        $fiscalDate = $this->getFiscalDate();
        $startDate = $this->getFiscalDate();
        $startDate->setYear($fiscalYear);
        $startDate->setTime(0,0,0);
        $endDate = $this->getFiscalDate();
        $endDate->setYear($fiscalYear);
        $endDate = $endDate->add("year", 1);
        $endDate->subtract("days", 1);
        $endDate->setTime(23,59,59);

        return [
            'startDate' => $startDate->format('Y-m-d H:i:s'),
            'endDate' => $endDate->format('Y-m-d H:i:s')
        ];
    }

    public function getFiscalYearByAtttendanceDayDate($atttendanceDayDate){
        $atttendanceDayDateYear = Carbon::createFromFormat('Y-m-d', $atttendanceDayDate)->year;
        $atttendanceDayDateFiscalYear = $this->getSelectedFiscalYearStartAndEnd($atttendanceDayDateYear);
        return Carbon::createFromFormat('Y-m-d', explode(" ",$atttendanceDayDateFiscalYear['startDate'])[0])->year;
    }

    private function getFiscalDate($carbonParameter = null) {
        $fiscalDay = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_day');
        $fiscalMonth = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_month');
        $date = new Carbon($carbonParameter);
        $date = $date->setMonth($fiscalMonth);
        $date = $date->setDay($fiscalDay);
        return $date;
    }

    public function getStaffAttendanceBalanceReportData($staffId , $fromDate=null , $toDate =null)
    {
        $canViewStaffReportData = $this->userCanViewAttendanceSheets($staffId);
        if (!$canViewStaffReportData) return [];
        if (!$fromDate || !$toDate){
            $dates = $this->getCurrentFiscalYearStartAndEnd();
            $params = [
                "name"=>[$staffId],
                "from_date"=>$dates['startDate'],
                "to_date"=>$dates['endDate'],
            ];
        }else{
            $params = [
                "name"=>[$staffId],
                "from_date"=>$fromDate,
                "to_date"=>$toDate,
            ];
        }
        $response = [];
        $this->setConsiderFutureLeaves(true);
        $this->setParameters($params);
        $this->setLeaveBalanceData();
        $data = $this->getReportData()['reportData']['data'][$staffId] ?? [];
        if (!count($data))return $data;
        $leaves = $data['leaves'];
        foreach ($leaves as $leaveTypeId => $leave){
            $response[] = [
                "leaveTypeId" => $leaveTypeId,
                "leaveType" => $leave['leave_type_name'],
                "taken" => $leave['total'],
                "future" => $leave['future'] ?? 0,
                "remaining" => $leave['remaining'],
                "credit" => $leave['balance'] ?? ($leave['total'] + $leave['remaining']),
            ];
        }
        return $response;
    }

    public function setConsiderFutureLeaves(bool $value) : void
    {
        $this->considerFutureLeaves = $value;
    }

    public function getConsiderFutureLeaves() : bool
    {
        return $this->considerFutureLeaves;
    }

    public function userCanViewAttendanceSheets($staffId)
    {
        if (Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS)) return  true;
        $authStaff = getAuthOwner('staff_id');
        if (Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET) && $authStaff == $staffId) return  true;
        return false;
    }

    private function getEmployeeStatusFilter()
    {
        return [
            'simple' => true,
            'label' => '',
            'inputClass' => 'form-control select-filter',
            'div' => 'col-md-3 form-group',
            'name' => 'employee_status',
            'options' => ['1' => __t('Active'), '0' => __t('In-active')],
            'value' => MultipleShiftTypeUtil::PRIMARY,
            'attributes' => [
                'placeholder' => __t('Employee Status'),
            ],
            'type' => 'select'
        ];
    }


    public function filterValidatorForAttendanceMultipleReport($data)
    {
        $validationRules = $this->getFormValidationRulesForAttendanceMultipleReport();

        $validator = Validator::make($data, $validationRules['rules'], $validationRules['messages']);

        return $validator;
    }

    private function getFormValidationRulesForAttendanceMultipleReport()
    {
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        $validateRoles =
            [
                'from_date' => 'required|date_format:'.$dateFormats[$dateFormatIndex],
                'to_date' => 'required|after_or_equal:from_date|date_format:'.$dateFormats[$dateFormatIndex],
            ];

        $messages = [
            'required' => __t('This is a required field and could not be empty'),
            'after_or_equal' => __t('The to date should be greater than or equal the from date'),
        ];

        return ['rules' => $validateRoles, 'messages' => $messages];
    }

    public function prepareExcelForAttendanceMultipleReport($reportName, $dataArr)
    {
        $date = date("Y-m-d_H-i-s");
        $filename = "$reportName-$date.xlsx";

        return Excel::download(new AttendanceMultipleReportExport($dataArr), $filename);
    }
}
