<?php

namespace App\Services;

use App\Facades\Plugins;
use App\Facades\Staff;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Actions\ListActionV2;
use App\Modules\LocalEntity\AppEntities\Listing\Modes\PaginateMode;
use App\Modules\LocalEntity\AppEntities\Listing\Modes\Responses\PaginateModeResponse;
use App\Modules\LocalEntity\Listing\Parser\FormParser;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Repositories\BankTransactionRepository;
use App\Repositories\BranchRepository;
use App\Repositories\JournalTransactionRepository;
use App\Repositories\TreasuryRepository;
use App\Services\Bank\BankSystemTransactionService;
use Illuminate\Http\Request;
use Izam\Daftra\Common\Utils\BankTransactionsStatusUtil;
use Izam\Daftra\Common\Utils\JournalUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Journal\Services\AutoAccountFinder;
use Izam\Daftra\Journal\Services\File\FileFacade;

class JournalTransactionService extends BaseService
{
    public function __construct(
        JournalTransactionRepository $repo,
        private TreasuryRepository $treasuryRepository,
        private BranchRepository $branchRepository,
        private AppEntityStructureGetter $appEntityStructureGetter,
        private FormParser $parser,
        private ListActionV2 $listAction,
        private BankSystemTransactionService $bankSystemTransactionService,
        private BankTransactionRepository $bankTransactionRepository,
    ) {
        parent::__construct($repo);
    }

    public function getBankSystemTransactionsForMatch(
        $bank_id,
        $system_transaction_id = null,
    ) {
        $bank = $this->treasuryRepository->find($bank_id);
        //If there is not bank found return empty transactions list
        if (!$bank) {
            return [];
        }
        $finder = new AutoAccountFinder(getPdo());
        $bankAccount = $finder->getAutoAccount('treasury', $bank->id);
        $entity = $this->appEntityStructureGetter->buildEntity('journal_transaction');
        $listingMetaObject = $this->listAction->parseUrl($this->parser);
        $systemCurrency = get_default_currency();
        $level = 1;
        $validators = $this->listAction->getUrlValidationRules($listingMetaObject, $entity, $level);
        $data = $this->listAction->assignRequestData($listingMetaObject, $entity, $level, $validators);
        if ($systemCurrency !== $bank->currency_code) {
            $data->setFilter(new FilterMeta('journal_transactions__journal.currency_code', '=', $bank->currency_code));
        }

        $branchesActive = Plugins::pluginActive(PluginUtil::BranchesPlugin);
        $branches = [];
        if ($branchesActive) {
            $branches = $this->branchRepository->list(['id', 'name'], true);
            $data->setFilter(new FilterMeta('journal_transactions__journal.branch_id', 'in', getStaffBranches()));
        }

        $data->setFilter(new FilterMeta('journal_transactions.bank_transaction_id', 'is_null', null));
        $data->setFilter(new FilterMeta('journal_transactions.status', 'equal', 'not_matched'));
        $data->setFilter(new FilterMeta('journal_transactions.journal_account_id', 'equal', $bankAccount['id']));
        $data->setFilter(new FilterMeta('journal_transactions__journal.entity_type', 'not_in', ['year_opening_balance', 'year_closing_balance']));
        if ($system_transaction_id) {
            $data->setFilter(new FilterMeta('journal_transactions.id', 'equal', $system_transaction_id));
        }
        $metaRepository = new AppEntityMetaRepository();
        $modeMeta = $listingMetaObject->getMode();
        $mode = new PaginateMode($metaRepository, $data);
        $query = $mode->getProcessQueryBuilder($entity, $level);
        $query->reorder();

        $responseData = $query->paginate($modeMeta->getPerPage(), ['*'], 'page', $modeMeta->getPage());

        $response = new PaginateModeResponse($listingMetaObject);
        $systemTransactions = $response->respond($entity, $responseData);

        $systemTransactions->getCollection()->transform(function ($transaction) use ($branchesActive, $branches) {
            toArray($transaction);
            return $this->formatSystemTransactionForMatching($transaction, $branches);
        });
        return $systemTransactions;
    }

    public function formatSystemTransactionForMatching($transaction, $branches = [])
    {
        $transactionDto = $transaction;
        if ($branches) {
            $transactionDto['branch_name'] = $branches[$transactionDto['journal']['branch_id']];
        }
        $transactionDto['staff'] = Staff::getStaffDetails($transactionDto['journal']['staff_id']);

        switch ($transactionDto['journal']['entity_type']) {
            case JournalUtil::AUTO_JOURNAL_TYPE_INVOICE_PAYMENT:
                $url = "/owner/invoice_payments/edit/" . $transactionDto['journal']['entity_id'];
                break;
            case JournalUtil::AUTO_JOURNAL_TYPE_PURCHASE_ORDER_PAYMENT:
                $url = "/owner/purchase_order_payments/edit/" . $transactionDto['journal']['entity_id'];
                break;
            case JournalUtil::AUTO_JOURNAL_TYPE_EXPENSE:
                $url = "/owner/expenses/edit/" . $transactionDto['journal']['entity_id'];
                break;
            case JournalUtil::AUTO_JOURNAL_TYPE_INCOME:
                $url = "/owner/incomes/edit/" . $transactionDto['journal']['entity_id'];
                break;
            default:
                $url = '/owner/journals/edit/' . $transactionDto['journal']['id'];
        }
        $transactionDto['link'] = $url;
        $transactionDto['files'] = FileFacade::getJournalFiles($transactionDto['journal']);
        return $transactionDto;
    }

    public function getSystemTransactionBankTransaction(
        $transaction_id,
        $bank_id
    ) {
        $systemTransaction = $this->repo->find($transaction_id);
        $entity = $this->appEntityStructureGetter->buildEntity('bank_transaction');
        $urlStructure = $this->listAction->parseUrl($this->parser);
        if ($systemTransaction->debit > 0) {
            $bankTransactionsType = 'deposit';
        } else {
            $bankTransactionsType = 'withdraw';
        }
        $level = 1;
        $validators = $this->listAction->getUrlValidationRules($urlStructure, $entity, $level);
        $data = $this->listAction->assignRequestData($urlStructure, $entity, $level, $validators);
        $data->setFilter(new FilterMeta('bank_transactions.bank_id', 'equal', $bank_id));
        $data->setFilter(new FilterMeta('bank_transactions.type', 'equal', $bankTransactionsType));
        $data->setFilter(new FilterMeta('bank_transactions.status', 'equal', 'not_matched'));
        $metaRepository = new AppEntityMetaRepository();
        $mode = new PaginateMode($metaRepository, $data);
        $responseData = $this->listAction->modeExecute($mode, $entity, $level);
        $response = new PaginateModeResponse($urlStructure);
        $responseData = $response->respond($entity, $responseData);

        return $responseData;
    }

    public function matchSystemTransactionToBankTransactions(
        $system_transaction_id,
        Request $request,
        CommonActivityLogService $activityLogService
    ) {
        $systemTransaction = $this->repo->find($system_transaction_id);
        $bankTransactionIds = $request->get('bank_transactions');
        if (!$systemTransaction) {
            return ['code' => 404, 'message' => 'system transaction not found'];
        }

        $bankId = $systemTransaction->journal_account->entity_id;
        $bankTransactionsToMatch = $this->bankSystemTransactionService->getSystemTransactionBankTransactionsToBeMatched(
            $this->bankTransactionRepository,
            $bankId,
            $systemTransaction->debit > 0 ? 'deposit' : 'withdraw',
            $bankTransactionIds,
        );
        $bank = $this->treasuryRepository->find($bankId);
        if ($bank->currency_code === get_default_currency()) {
            $useLocalAmounts = true;
        } else {
            $useLocalAmounts = false;
        }
        $amount = 0;
        if ($useLocalAmounts) {
            $amount = $systemTransaction->debit > 0 ? $systemTransaction->debit : $systemTransaction->credit;
        } else {
            $amount = $systemTransaction->currency_debit > 0 ? $systemTransaction->currency_debit : $systemTransaction->currency_credit;
        }
        $result = $this->bankSystemTransactionService->validateBankTransactionsCanBeMatched($bankTransactionsToMatch, $amount);
        if ($result !== true) {
            return ['code' => 400, 'message' => $result];
        }
        $bankTransactionsIds = $bankTransactionsToMatch->pluck('id');
        $this->bankSystemTransactionService->matchBankTransactions($this->repo, $this->bankTransactionRepository, $system_transaction_id, $bankTransactionsIds);
        foreach ($bankTransactionIds as $bankTransactionId) {
            $bankTransaction = $this->bankTransactionRepository->find($bankTransactionId);
            $activityLogRequest = $this->bankSystemTransactionService->createMatchingActivityLog(
                $bankTransaction,
                ['status' => BankTransactionsStatusUtil::MATCHED],
                ['status' => BankTransactionsStatusUtil::NOT_MATCHED]
            );
        }
        $activityLogService->addActivity($activityLogRequest);
        return ['message' => __t('Transactions matched successfully'), 'code' => 200];
    }

    public function unMatchSystemTransaction(
        $system_transaction_id,
        CommonActivityLogService $activityLogService
    ) {
        $systemTransaction = $this->repo->find($system_transaction_id);
        if (!$systemTransaction) {
            return abort(404);
        }
        $bankTransaction = $systemTransaction->bank_transaction;
        if ($bankTransaction) {
            foreach ($bankTransaction->systemTransactions as $systemTransaction) {
                $this->repo->update($systemTransaction->id, ['bank_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
            }
            $this->bankTransactionRepository->update($bankTransaction->id, ['journal_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
            $activityLogRequest = $this->bankSystemTransactionService->createMatchingActivityLog(
                $bankTransaction,
                ['status' => BankTransactionsStatusUtil::NOT_MATCHED],
                ['status' => BankTransactionsStatusUtil::MATCHED],
            );
            $activityLogService->addActivity($activityLogRequest);
        }
        if (count($systemTransaction->bank_transactions)) {
            foreach ($systemTransaction->bank_transactions as $bankTransaction) {
                $this->bankTransactionRepository->update($bankTransaction->id, ['journal_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
                $activityLogRequest = $this->bankSystemTransactionService->createMatchingActivityLog(
                    $bankTransaction,
                    ['status' => BankTransactionsStatusUtil::NOT_MATCHED],
                    ['status' => BankTransactionsStatusUtil::MATCHED],
                );
                $activityLogService->addActivity($activityLogRequest);
            }
            $this->repo->update($systemTransaction->id, ['bank_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
        }
        flashMessageIntoCake(sprintf(__t('%s Updated Successfully'), __t('Bank Transaction')));
        $activityLogRequest = $this->bankSystemTransactionService->createMatchingActivityLog(
            $bankTransaction,
            ['status' => BankTransactionsStatusUtil::NOT_MATCHED],
            ['status' => BankTransactionsStatusUtil::MATCHED],
        );
        $activityLogService->addActivity($activityLogRequest);
        return $bankTransaction;
    }
}
