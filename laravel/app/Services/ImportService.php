<?php

namespace App\Services;

use App\Exceptions\Import\ImportErrorException;
use App\Facades\Formatter;
use App\Facades\Plugins;
use App\Helpers\FilterOperations;
use App\Models\EntityField;
use App\Modules\LocalEntity\AppEntities\AppEntityRepository;
use App\Repositories\BaseRepositoryInterface;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\EntityAppDataRepository;
use App\Repositories\EntityFieldRepository;
use App\Repositories\EntityRepository;
use App\Requests\DefaultRequest;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\FilePathUtil;
use Exception;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use InvalidArgumentException;
use Ramsey\Uuid\Uuid;

class ImportService extends BaseService
{
    private $mappingService;
    /**
     * @var $importedEntityRepo BaseRepositoryInterface
     */
    private $importedEntityRepo = null;
    /**
     * @var $importedEntityService BaseService
     */
    private $importedEntityService = null;
    /**
     * @var $currentEntity EntityField
     */
    private $currentEntity = null;

    private $importEntityHandler = null;

    /**
     * @var EntityAppDataRepository
     */
    private $appEntityRepo;

    protected string $importFileName = '';

    public function __construct(EntityRepository $entityRepository, MappingService $mappingService, EntityAppDataRepository $appEntityRepository, protected EntityAppDataRepository $appEntityDataRepo)
    {
        $this->mappingService = $mappingService;
        $this->appEntityRepo = $appEntityRepository;
        parent::__construct($entityRepository, $entityRepository);
    }

    public function handleFile(Request $request, $entityKey)
    {
        $fileName = getCurrentSite('id') . '_' . $entityKey . '_' . Uuid::uuid4()->toString() . '.csv';
        $path = FilePathUtil::importTempPath();
        switch ($request['delimiter']) {
            case '1':
                $delimiter = ",";
                break;
            case '2':
                $delimiter = ";";
                break;
            case '3':
                $delimiter = "\t";
                break;
            default:
                $delimiter = ",";
        }
        $originalFileName = $request->file('input_file')->getClientOriginalName();
        $request->file('input_file')->storeAs($path, $fileName, 'cakeFiles');
        try {
            $file = Storage::disk('cakeFiles')->readStream($path . $fileName);
        } catch (FileNotFoundException $e) {
            return ['status' => false, 'message' => [__t('File not found')]];
        }
        $header = fgetcsv($file, 0, $delimiter);
        if (count($header) <= 1) {
            return ['status' => false, 'message' => __t('Sorry, it is not allowed to import one column file, make sure you have selected a file with the correct format')];
        }
        while (($data = fgetcsv($file, 0, $delimiter)) !== FALSE) {
            if (count($data) != count($header)) {
                return ['status' => false, 'message' => __t('CSV columns count is not the same through all file rows')];
            }
        }
        if ($request['import_first_row']) {
            $initChar = 'A';
            for ($i = 0; $i < count($header); $i++) {
                $header[$i] = $initChar++;
            }
        }
        return ['status' => true, 'fileName' => $fileName, 'originalFileName' => $originalFileName, 'headers' => $header, 'delimiter' => $request['delimiter'], 'import_first_row' => $request['import_first_row'], 'extraData' => $request['extraData'] ?? []];
    }

    public function getEntityImportedFields(string $entityKey)
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => $this->mappingService->getProductImportEntityFields(),
            EntityKeyTypesUtil::PRICE_LIST_ITEMS => resolve(PriceListItemsService::class)->getImportFieldsLabel(),
            EntityKeyTypesUtil::INVOICE_ENTITY_KEY => resolve(InvoiceService::class)->getImportFieldsLabel(),
            EntityKeyTypesUtil::JOURNAL => resolve(JournalService::class)->getImportFieldsLabel(),
            EntityKeyTypesUtil::PAYSLIP => $this->mappingService->getPayslipImportEntityFields(),
            EntityKeyTypesUtil::ASSET => $this->mappingService->getAssetImportEntityFields(),
            EntityKeyTypesUtil::STOCKTAKING_RECORD => $this->mappingService->getStocktakingRecordImportEntityFields(),
            EntityKeyTypesUtil::CONTRACT => $this->mappingService->getContractImportEntityFields(),
            default => $this->mappingService->getImportEntityFields($entityKey)
        };
    }

    /**
     * @throws Exception
     */
    public function importUsingJsonFile($request, $entityKey): void
    {
        try {
            if (empty($this->importFileName)) {
                $path =  __DIR__."/../../../cake/webroot/files/".SITE_HASH."/";

                if (!is_dir($path)) {
                    mkdir($path, 0777, true);
                }

                $this->importFileName = $path.uniqid($entityKey).".json";
            }

            $data = $this->importedEntityService->getJsonImportData($request);

            $allData = file_exists($this->importFileName)
                ? json_decode(file_get_contents($this->importFileName), true)
                : [];

            $allData[] = $data;

            file_put_contents($this->importFileName, json_encode($allData));
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    public function afterImportUsingJsonFile(string $entityKey)
    {
        $count = count(json_decode(file_get_contents($this->importFileName), 1)??[]);
        $branchID = getCurrentBranchID();
        $id = $this->appEntityDataRepo->insertGetId([
            'entity_key' => $entityKey,
            'entity_id' => 0,
            'app_key' => "import",
            "action_key" => "import-count",
            "data" => json_encode([
                "file" => $this->importFileName,
                "count" => $count,
                "finished" => 0,
                'branch_id' => $branchID,
                "staff_id" =>   getAuthOwner("staff_id"),
            ])
        ]);

        $phpVersion = implode('.', array_slice(explode('.', PHP_VERSION), 0, 2));
        $siteID = getCurrentSite("id");


        // site_id and branch_id are required to set the correct site connection
        $command = sprintf(
            'nohup php%s /var/www/html/%s/webroot/cron.php /cron/import_bulk_using_json_file/%s/%s/%s/%s > /dev/null 2>&1 & echo $!',
            $phpVersion,
            CAKE_DIR,
            $siteID,
            $branchID,
            getAuthOwner("staff_id"),
            $id
        );

        exec($command, $output, $status);

        config(['data.import-count-id' => $id]);
    }

    public function importData(Request $request, $entityKey, $requireDataOnly = false)
    {

        set_time_limit ( 3600);
        ini_set('memory_limit','20G');
        $isUpdate = false;
        $updateUsing = "";
        if ($request['update_data'])
            $isUpdate = true;
        $ignoreErrors = false;
        if ($request['ignore_errors'])
            $ignoreErrors = true;
        $insertedData = [];
        $updatedData = [];
        $invalidData = [];
        if ($isUpdate) {
            $updateUsing = $request['update_using'];
        }
        $relatedEntities = [];
        $fieldRelations = [];
        $rawFileData = [];
        $insertedDataCpy = [];
        $insertedWithCustom = [];
        switch ($request['delimiter']) {
            case '1':
                $delimiter = ",";
                break;
            case '2':
                $delimiter = ";";
                break;
            case '3':
                $delimiter = "\t";
                break;
            default:
                $delimiter = ",";
        }
        $fileName = $request['id'] . '.csv';
        $originalFileName = $request['originalFileName'];
        $path = FilePathUtil::importTempPath();
        try {
            $file = Storage::disk('cakeFiles')->readStream($path . $fileName);
        } catch (FileNotFoundException $e) {
            return ['status' => false, 'insertedCount' => 0, 'updatedCount' => 0, 'invalidData' => [__t('File not found')]];
        }

        $importingEntity = $this->mapEntityImport($entityKey);
        $entityFieldsInfo = $this->getEntityImportedFields($importingEntity);
        $this->initEntityData($importingEntity);
        if (isset($this->importedEntityService->extraImportFields) && !empty($this->importedEntityService->extraImportFields)) {
            $entityFieldsRepo = resolve(EntityFieldRepository::class);
            $fields = $entityFieldsRepo->findWhereIn('key', $this->importedEntityService->extraImportFields);
            foreach ($fields as $field) {
                $entityFieldsInfo[$field->id] = $field;
                $request->request->add(['mapping' => array_merge($request['mapping'], ['field_' . $field->id => $field->db_name])]);
            }
        }
        $updateExistings = false;
        if (isset($request->extraData['update_existing']) && $request->extraData['update_existing']) {
            $updateExistings = true;
        }
        $rowCount = 0;
        $header = [];
        if (!$request['import_first_row']) {
            $rowCount++;
            $header = fgetcsv($file, 0, $delimiter);
        }
        $extraData = $request['extraData'] ?? [];
        $this->importedEntityService->beforeImport($originalFileName);
        $mapping = $request['mapping'];


        while (($data = fgetcsv($file, 0, $delimiter)) !== FALSE) {
            if (empty($header) && $request['import_first_row']) {
                $initChar = 'A';
                for ($i = 0; $i < count($data); $i++) {
                    $header[$i] = $initChar++;
                }
            }
            $rowCount++;
            if (isset($mapping['field'])) {
                foreach (array_filter($mapping['field']) as $key => $field) {
                    $tempMapping = $mapping;
                    unset($tempMapping['field']);
                    $tempMapping[$key] = $field;
                    try {
                        [$rinsertedWithCustom, $rinsertedData, $rupdatedData, $rinvalidData, $rinsertedDataCpy] = $this->doImport($data, $tempMapping, $entityFieldsInfo, $header, $entityKey, $extraData, $rowCount, $isUpdate, $updateUsing, $path, $ignoreErrors, $fileName, $key);
                    } catch (ImportErrorException $exception) {
                        return ['status' => false, 'insertedCount' => 0, 'updatedCount' => 0, 'invalidData' => $exception->getInvalidData()];
                    }

                    $insertedWithCustom = array_merge($insertedWithCustom, $rinsertedWithCustom);
                    $insertedData = array_merge($insertedData, $rinsertedData);
                    $updatedData = array_merge($updatedData, $rupdatedData);
                    $invalidData = array_merge($invalidData, $rinvalidData);
                    $insertedDataCpy = array_merge($insertedDataCpy, $rinsertedDataCpy);
                }
            } else {
                try {
                    [$rinsertedWithCustom, $rinsertedData, $rupdatedData, $rinvalidData, $rinsertedDataCpy] = $this->doImport($data, $mapping, $entityFieldsInfo, $header, $entityKey, $extraData, $rowCount, $isUpdate, $updateUsing, $path, $ignoreErrors, $fileName, null);
                } catch (ImportErrorException $exception) {
                    return ['status' => false, 'insertedCount' => 0, 'updatedCount' => 0, 'invalidData' => $exception->getInvalidData()];
                }
                $insertedWithCustom = array_merge($insertedWithCustom, $rinsertedWithCustom);
                /**
                 * array_merge() will not preserve array keys so, next lines were replaced to preserve array keys will merging the arrays
                 */
                $insertedData += $rinsertedData;
                $updatedData += $rupdatedData;
                $invalidData +=  $rinvalidData;
                $insertedDataCpy += $rinsertedDataCpy;

            }
        }
        if ($requireDataOnly) {
            return ['data' => $insertedWithCustom, 'status' => true, 'invalidData' => $invalidData];
        }
        $errors = [];
        if (method_exists($this->importedEntityService, 'validateAllItems')) {
            try {
                $errors = $this->importedEntityService->validateAllItems($insertedData, $request->all(), $entityFieldsInfo);
            } catch (ImportErrorException $exception) {
                return ['status' => false, 'insertedCount' => 0, 'updatedCount' => 0, 'invalidData' => $exception->getInvalidData()];
            }

        }


        if (empty($invalidData) && !empty($errors)) {
            $invalidData =   $errors;
        }

        //Actually Importing the Data
        $anyErrorHappened = false;
        $insertDataArray = [];
        if (method_exists($this->importedEntityService, 'beforeInsert')) {
            $insertedData = $this->importedEntityService->beforeInsert($insertedData, $extraData, $invalidData);
        }

        try {
            DB::transaction(function () use (&$insertedData, $entityKey, $isUpdate, $updateUsing, &$updatedData, $ignoreErrors, &$invalidData, $updateExistings, $insertDataArray, $anyErrorHappened) {
                $invalidData = $this->getCorrectIgnoredIndex($invalidData, $insertedData);
                foreach ($insertedData as $rowNum => $insertedDataItem) {
                    try {
                        if (!isset($invalidData[$rowNum])) {
                            $this->importEntityHandler->validateItem($insertedDataItem);
                            $this->insertData($entityKey, $insertedDataItem, $updateExistings);
                            //$insertDataArray[] = $insertedDataItem;
                        } else {
                            unset($insertedData[$rowNum]);
                        }
                    } catch (Exception $exception) {
                        $anyErrorHappened = true;
                        unset($insertedData[$rowNum]);
                        $invalidData[$rowNum] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowNum, $exception->getMessage());
                        if (!$ignoreErrors) {
                            throw new InvalidArgumentException();
                        }

                    }
                }

                /* if (!$anyErrorHappened  || $ignoreErrors) {
                     foreach ($insertDataArray as $item) {
                         $this->insertData($entityKey, $item, $updateExistings);
                     }
                 }*/
                $insertDataArray = [];
                $anyErrorHappened = false;
                if ($isUpdate) {
                    foreach ($updatedData as $rowNum => $updatedDataItem) {
                        try {
                            if (!isset($invalidData[$rowNum])) {
                                $this->importEntityHandler->validateItem($updatedDataItem);

                                if (method_exists($this->importedEntityService, 'prepareBeforeUpdate')) {
                                    $this->importedEntityService->prepareBeforeUpdate($updatedDataItem, $rowNum);
                                }

                                //$insertDataArray[] = $updatedDataItem;
                                $this->updateData($entityKey, $updatedDataItem, $updateUsing);
                            } else {
                                unset($updatedData[$rowNum]);
                            }
                        } catch (Exception $exception) {
                            $anyErrorHappened = true;
                            unset($updatedData[$rowNum]);
                            $invalidData[$rowNum] = sprintf(__t('Failed to update row #%s with error message %s'), $rowNum, $exception->getMessage());
                            if (!$ignoreErrors) {
                                throw new InvalidArgumentException();
                            }
                        }
                    }

                    /*  if (!$anyErrorHappened  || $ignoreErrors) {
                          foreach ($insertDataArray as $item) {
                              $this->updateData($entityKey, $item, $updateUsing);
                          }
                      }*/
                }
            });
        } catch (Exception $exception) {
            $this->importedEntityService->afterImport();
            Storage::disk('local')->delete($path . $fileName);
            return ['status' => false, 'insertedCount' => 0, 'updatedCount' => 0, 'invalidData' => $invalidData];
        }
        if ($this->importedEntityService->importUsingJsonFile) {
            if ($this->importFileName) {
                $this->afterImportUsingJsonFile($entityKey);
            } else {
                return ['status' => false, 'insertedCount' => 0, 'updatedCount' => 0, 'invalidData' => $invalidData, 'rawFileData' => []];
            }
        } else {
            $this->importedEntityService->afterImport();
        }
        Storage::disk('local')->delete($path . $fileName);
        return ['status' => true, 'insertedCount' => count($insertedData), 'updatedCount' => count($updatedData), 'invalidData' => $invalidData, 'rawFileData' => $rawFileData];
    }


    public function getCorrectIgnoredIndex($ignoredData, $insertedData)
    {

        if (method_exists($this->importedEntityService, 'getCorrectIgnoredIndex')) {
            return $this->importedEntityService->getCorrectIgnoredIndex($ignoredData, $insertedData);
        }
        return $ignoredData;
    }

    public function doImport($data, $mapping, $entityFieldsInfo, $header, $entityKey, $extraData, $rowCount, &$isUpdate, $updateUsing, $path, $ignoreErrors, $fileName, $key)
    {
        $insertedData = [];
        $updatedData = [];
        $invalidData = [];
        $insertedWithCustom = [];
        //Map Data from File to db data

        $mappedData = $this->mappingService->mapImportedData($data, $mapping, $entityFieldsInfo, $header, $entityKey);
        $parsedData = $mappedData['data'];
        $validationRules = $mappedData['validation'];
        $fieldsInfo = $mappedData['fields'];
        $headersInfo = $mappedData['header'];
        if (!empty($extraData) && empty($extraData[array_keys($extraData)[0]])) {
            $extraData[array_keys($extraData)[0]] = str_replace('field_', '', $key);
        }
        $parsedData = $this->importedEntityService->preProcessDataForImportUsingExtraData($parsedData, $extraData);
        $willBeUpdated = null;
        if (method_exists($this->importedEntityService, 'validateImportData')) {
            $this->importedEntityService->validateImportData(
                $parsedData,
                $validationRules,
                $headersInfo,
                $entityKey,
                $rowCount,
                $isUpdate,
                $updateUsing,
                $invalidData,
                $willBeUpdated,
                $mappedData['custom_data'] ?? []
            );
        } else {
            $this->validateData(
                $parsedData,
                $validationRules,
                $headersInfo,
                $entityKey,
                $rowCount,
                $isUpdate,
                $updateUsing,
                $invalidData,
                $willBeUpdated
            );
        }

        //Process Relations
        $this->processRelationData($parsedData, $fieldsInfo, $headersInfo, $rowCount, $invalidData, $relatedEntities, $fieldRelations);
        //Check if there is an error in this row
        if (!$ignoreErrors && isset($invalidData[$rowCount])) {
            $this->importedEntityService->afterImport();
            Storage::disk('local')->delete($path . $fileName);
            throw  new ImportErrorException($invalidData);
        }
        //Format Data For DB
        $parsedData = $this->formatData($parsedData, $fieldsInfo);
        //Now our data is ready to be inserted or updated
        if ($willBeUpdated) {
            $updatedData[$rowCount] = $parsedData;
        } else {
            $insertedData[$rowCount] = $parsedData;
        }
        //Store Raw File Data in case it was needed later
        $rawFileData[$rowCount] = $data;

        $insertedDataCpy = $insertedData;
        if (isset($mappedData['custom_data'])) {
            $insertedDataCpy[$rowCount]['custom_data'] = $mappedData['custom_data'];
            $insertedWithCustom[] = $insertedDataCpy[$rowCount];
        }

        return [
            $insertedWithCustom,
            $insertedData,
            $updatedData,
            $invalidData,
            $insertedDataCpy,
        ];
    }

    //Helper Functions
    private function initEntityData($entityKey)
    {
        if (!isset($this->importedEntityRepo)) {
            $this->currentEntity = $this->entityRepository->find($entityKey);
            $this->importedEntityService = App::make('App\Services\\' . $this->currentEntity->service_name);
            $this->importEntityHandler = $this->importedEntityService->getImportHandler();
            $this->importedEntityRepo = $this->importedEntityService->repo;
        }
    }

    private function validateData($data, $validationRules, $headersInfo, $entityKey, $rowCount, $isUpdate, $updateUsing, &$invalidData, &$willBeUpdated)
    {
        $willBeUpdated = false;
        //Handle existence of the data if we are in update mode
        if ($isUpdate) {
            if (empty($data[$updateUsing])) {
                $invalidData[$rowCount] = sprintf(__t('Invalid Data, Missing required field %s at row #%s'), $headersInfo[$updateUsing], $rowCount);
                return false;
            }
            if ($this->getRecordCount($entityKey, [$updateUsing => $data[$updateUsing]]) > 0) {
                $willBeUpdated = true;
            }
        }
        //Validate Data
        foreach ($validationRules as $field => $rules) {
            foreach ($rules as $ruleKey => $rule) {
                if ($ruleKey === 'allowedValues') {
                    $value = $data[$field];
                    if ($value) {
                        if (in_array($value, array_keys($rule)))
                            return true;
                        if (in_array(strtolower($value), array_map(function ($item) {
                            return strtolower($item);
                        }, array_values($rule))))
                            return true;
                        if (in_array($value, array_map(function ($item) {
                            return __t($item);
                        }, array_values($rule))))
                            return true;
                        $invalidData[$rowCount] = sprintf(__t('Invalid Data, wrong value for field %s at row #%s'), $headersInfo[$field], $rowCount);
                        return false;
                    }
                } else {
                    switch ($rule) {
                        case 'required':
                            if (empty($data[$field]) && $data[$field] !== "0" && !$willBeUpdated) {
                                $invalidData[$rowCount] = sprintf(__t('Invalid Data, Missing required field or not exist %s at row #%s'), $headersInfo[$field], $rowCount);
                                return false;
                            }
                            break;
                        case 'unique':
                            if (!empty($data[$field])) {
                                if (!$willBeUpdated && $this->getRecordCount($entityKey, [$field => $data[$field]]) > 0) {
                                    $invalidData[$rowCount] = sprintf(__t('Invalid Data, duplicate %s with value %s at row #%s'), $headersInfo[$field], $data[$field], $rowCount);
                                    return false;
                                }
                            }
                            break;
                        case 'date':
                            if (!empty($data[$field])) {
                               $value = Formatter::formatForDB($data[$field], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
                               if (empty($value)) {
                                   $invalidData[$rowCount] = sprintf(__t("Invalid Data, Data must be in the correct date format in '%s' field at row #%d"), $headersInfo[$field], $rowCount);
                                   return false;
                               }
                            }
                            break;
                        case 'positive':
                            if ($data[$field] < 0 && is_numeric($data[$field])) {
                                $invalidData[$rowCount] = sprintf(__t("Invalid Data, Data must be greater than or equal zero %s at row %s"), $headersInfo[$field], $rowCount);

                                return false;
                            }

                            if ($data[$field] !== "" && is_string($data[$field]) && !is_numeric($data[$field])) {
                                $invalidData[$rowCount] = sprintf(__t('Invalid Data, Data must be number  %s at row #%s'), $headersInfo[$field], $rowCount);
                                return false;
                            }
                            break;
                        default:
                            $messages = method_exists($this->importedEntityService, 'getValidationMessages') ? $this->importedEntityService->getValidationMessages() : [];
                            $validator = Validator::make(
                                $data,
                                [$field => $rule],
                                $messages
                            );
                            if ($validator->fails()) {
                                // Validation failed
                                $invalidData[$rowCount] = sprintf(__t('Invalid Data, %s at row #%s'), $validator->errors()->first($field), $rowCount);
                                return false;
                            }
                            break;
                    }
                }
            }
        }
        return true;
    }


    private function processRelationData(&$data, $entityFieldsInfo, $headersInfo, $rowCount, &$invalidData, &$relatedEntities = [], &$fieldRelations = [])
    {
        foreach ($data as $fieldID => $fieldData) {
            // isset() -> is added to validate if staff_id isset to 0, so it will throw an exception
            if (!empty($fieldData)) {
                // Robust encoding and question mark check
                $hasEncodingIssue = (strpos(trim($fieldData), '?') !== false || !mb_check_encoding($fieldData, 'UTF-8'));
                if ($hasEncodingIssue) {
                    if (empty($invalidData[$rowCount])) {
                        $invalidData[$rowCount] = sprintf(__t('Invalid Data, encoding issue or corrupted characters in field %s at row #%s'), $headersInfo[$fieldID], $rowCount);
                    }
                    return false;
                }
                $fieldRelation = $fieldRelations[$fieldID] ?? $fieldRelations[$fieldID] = $entityFieldsInfo[$fieldID]->relation;
                if ($fieldRelation) {
                    $relatedEntity = $relatedEntities[$fieldRelation->reference_entity_key] ?? $relatedEntities[$fieldRelation->reference_entity_key] = $this->mappingService->getEntityByKey($fieldRelation->reference_entity_key);
                    $relatedEntityRepo = App::make('App\Repositories\\' . $relatedEntity->repo_name);
                    $escapedData = DB::connection('currentSite')->getPdo()->quote($fieldData);
                    $orCondition = [
                        'OR' => [
                            ['field' => "LOWER({$relatedEntity->name_field}) = LOWER({$escapedData})", 'operation' => FilterOperations::RAW_OPERATION, 'value' => $fieldData],
                        ]
                    ];
                    if (is_numeric($fieldData)) {
                        //some time when search in id with string value it return result  ex:SELECT * FROM `products` WHERE id = '6649KIDS C4'  it convert search value to double;
                        $orCondition['OR'][] = ['field' => $relatedEntity->primary_key, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => $fieldData];
                    }
                    $findCondition = new CustomFind($orCondition);
                    $relatedEntityRepo->pushCriteria($findCondition);
                    $relatedEntityData = $relatedEntityRepo->first();
                    if ($relatedEntityData) {
                        $data[$fieldID] = $relatedEntityData->{$relatedEntity->primary_key};
                    } else {
                        if (empty($invalidData[$rowCount])) {
                            $invalidData[$rowCount] = $this->importEntityHandler->getNotFoundRelationMessage($headersInfo[$fieldID], $rowCount);
                        }
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private function formatData($parsedData, $fieldsInfo)
    {
        foreach ($parsedData as $field => &$data) {
            $data = Formatter::formatForDB($data, $fieldsInfo[$field]->field_type, $fieldsInfo[$field]);
        }
        return $parsedData;
    }

    private function getRecordCount($entityKey, $recordConditions)
    {
        $this->initEntityData($entityKey);
        return $this->importedEntityRepo->findWhere($recordConditions)->count();
    }

    public function getRecord($entityKey, $recordConditions)
    {
        $this->initEntityData($entityKey);
        return $this->importedEntityRepo->findWhere($recordConditions)->first();
    }

    private function updateData($entityKey, $data, $updateUsing)
    {
        $this->initEntityData($entityKey);

        $updateImportConditions = array_merge($this->importedEntityService->updateImportConditions(), [$updateUsing => $data[$updateUsing]]);

        $records = $this->importedEntityRepo->findWhere($updateImportConditions);
        $hasError = false;
        foreach ($records as $record) {
            $recordID = $record->getKey();
            $updateRequest = new DefaultRequest($data);
            try {
                if (method_exists($this->importedEntityService, 'mapImportRequest')) {
                    $updateRequest = $this->importedEntityService->mapImportRequest($updateRequest);
                }
                if (!$this->importedEntityService->update($recordID, $updateRequest)) {
                    $hasError = true;
                }
            } catch (Exception $exception) {
                $hasError = true;
            }
        }
        if ($hasError) {
            throw new InvalidArgumentException(__t('Unknown Error'));
        }
    }

    private function insertData($entityKey, $data, $updateExistings = false)
    {
        $this->initEntityData($entityKey);
        $addRequest = new DefaultRequest($data);
        try {
            if ($this->importedEntityService->importUsingJsonFile && !empty($data)) {
                $this->importUsingJsonFile($addRequest, $entityKey);
            } else {
                if ($this->importedEntityService->import($addRequest, $updateExistings) === false) {
                    throw new InvalidArgumentException(__t('Unknown Error'));
                }
            }
        } catch (QueryException $exception) {
            Log::error('Import Query Exception', ['Exception' => $exception]);
            if (strpos($exception->getMessage(), 'Duplicate entry') !== false) {
                $re = '/for key \'(?<key>.*)\'.* insert into `(?<table>\w+)`.*\((?<cols>.*`)\)/';

                preg_match($re, $exception->getMessage(), $matches);
                $cols = explode(',', str_replace('`', '', $matches['cols']));
                $destinationCol = '';
                foreach ($cols as $col) {
                    $col = trim($col, ' ');
                    if (strpos($matches['key'], $col) !== false) {
                        $destinationCol = $col;
                    }
                }
                $message = __t(', The value is already entered before in column') . ' ' . __t($destinationCol);
            } else {
                $message = __t('Unknown Error');
            }
            throw new InvalidArgumentException($message);
        }
    }

    public function getBreadCrumbs($entityKey, $breadCrumbs)
    {
        $entity = $this->find($entityKey);
        $service = resolve("\App\Services\\" . $entity->service_name);
        return $service->getImportHandler()->getBreadCrumbs($breadCrumbs);
    }

    private function mapEntityImport($entityKey)
    {
        return match ($entityKey) {
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
            default => $entityKey
        };
    }

    public function getImportedRecords($entity, $id)
    {
        $appData = $this->appEntityRepo->findWhere(['entity_key' => $entity, 'id' => $id])->first();
        return json_decode($appData->data, true);
    }
}
