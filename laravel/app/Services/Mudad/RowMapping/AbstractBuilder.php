<?php

namespace App\Services\Mudad\RowMapping;

use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;

abstract class AbstractBuilder implements MudadReviewDataBuilder
{

    protected MudadData $data;

    protected MudadFileData $fileData;

    public function setMudadData(MudadData $data): MudadReviewDataBuilder
    {
        $this->data = $data;
        return $this;
    }

    public function setMudadFileData(MudadFileData $mudadFileData): MudadReviewDataBuilder
    {
        $this->fileData = $mudadFileData;
        return $this;
    }
}
