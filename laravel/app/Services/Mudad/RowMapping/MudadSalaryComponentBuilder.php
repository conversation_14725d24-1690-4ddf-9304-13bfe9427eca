<?php
namespace App\Services\Mudad\RowMapping;

use App\Facades\Settings;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;
use App\Utils\PluginUtil;

class MudadSalaryComponentBuilder extends AbstractBuilder implements MudadReviewDataBuilder
{

    public function build(ReviewData $reviewData): ReviewData
    {
        $MudadComponent = $this->fileData->getPrimaryComponents() ?? [];
        $reviewData->setMudadSalaryComponents($MudadComponent);
        return $reviewData;
    }
}