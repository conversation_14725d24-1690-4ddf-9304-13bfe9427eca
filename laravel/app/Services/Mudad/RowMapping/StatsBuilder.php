<?php
namespace App\Services\Mudad\RowMapping;

use App\Facades\Settings;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;
use App\Utils\PluginUtil;

class StatsBuilder extends AbstractBuilder implements MudadReviewDataBuilder
{


    public function build(ReviewData $reviewData): ReviewData
    {
        $employeesRows = $reviewData->getRows();
        $totalEmployees = $this->fileData->getRowsCount();
        $mappedUsersCount = count(array_filter($employeesRows, fn($el) => !empty($el->getMappedEmployee())));

        $reviewData->setStats([
            [
                'title' => __t('Users Found in Mudad File'),
                'value' => $totalEmployees
            ],
            [
                'title' => __t('Mapped Users'),
                'value' => $mappedUsersCount
            ],
            [
                'title' => __t('Unmapped Users'),
                'value' => $totalEmployees - $mappedUsersCount
            ]
        ]);
        return $reviewData;
    }
}