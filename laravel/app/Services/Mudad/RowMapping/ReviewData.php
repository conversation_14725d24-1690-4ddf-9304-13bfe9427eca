<?php
/**
 * this class represents this json data
 * {
 * "stats": [
 * {"title":  "Mapped Users", "value":  "1"}
 * ],
 * "errors": [
 * {
 * "text": "mismatch bla bla bla", "actions":  [
 * {"url":  "https://google.com", "text": "view details"}
 * ]
 * }
 * ],
 * "mudad_salary_components": [
 * "Basic Salary",
 * "Hosing",
 * "..etc"
 * ],
 * "addition_components": [
 * "خارج دوام (قيمة)"
 * //..etc
 * ],
 * "deduction_components": [
 * "absent"
 * //..etc
 * ],
 * "rows": [
 * {
 * "no": 1,
 * "mudad_name": "<PERSON><PERSON><PERSON>",
 * "official_id": "12345",
 * "mapped_employee": {"id": 1, "name": "<PERSON><PERSON><PERSON>", "official_id": "12345"},
 * "basic_components": [100,120,300],
 * "mudad_basic_components": [100,120,300],
 * "additions_components": [100,120,500],
 * "deductions_components": [100,120,600],
 * "net_salary": 1000
 * }
 * ]
 * }
 */
namespace App\Services\Mudad\RowMapping;

class ReviewData
{
    protected array $stats;

    protected array $errors;

    protected array $mudadSalaryComponents;

    protected array $additionComponents;

    protected array $deductionComponents;

    protected array $rows;

    protected array $warnings;


    public function getStats(): array
    {
        return $this->stats;
    }

    public function setStats(array $stats): void
    {
        $this->stats = $stats;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function setErrors(array $errors): void
    {
        $this->errors = $errors;
    }

    public function getMudadSalaryComponents(): array
    {
        return $this->mudadSalaryComponents;
    }

    public function setMudadSalaryComponents(array $mudadSalaryComponents): void
    {
        $this->mudadSalaryComponents = $mudadSalaryComponents;
    }

    public function getAdditionComponents(): array
    {
        return $this->additionComponents;
    }

    public function setAdditionComponents(array $additionComponents): void
    {
        $this->additionComponents = $additionComponents;
    }

    public function getDeductionComponents(): array
    {
        return $this->deductionComponents;
    }

    public function setDeductionComponents(array $deductionComponents): void
    {
        $this->deductionComponents = $deductionComponents;
    }

    public function getRows(): array
    {
        return $this->rows;
    }

    public function setRows(array $rows): void
    {
        $this->rows = $rows;
    }

    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function setWarnings(array $warnings): void
    {
        $this->warnings = $warnings;
    }

    public function formatSalaryComponets($component){
        return array_map(function($item){
            return is_array($item) ? $item['name'] : $item->name;
        },$component);
    }

    public function toArray(){
        return [
            'stats' => $this->getStats(),
            'errors' => $this->getErrors(),
            'mudadSalaryComponents' =>  $this->formatSalaryComponets($this->getMudadSalaryComponents()),
            'additionComponents' => $this->formatSalaryComponets($this->getAdditionComponents()),
            'deductionComponents' => $this->formatSalaryComponets($this->getDeductionComponents()),
            'rows' => array_map(function($row) {
                return $row->toArray();
            }, $this->getRows()),
            'warnings' => $this->getWarnings(),
        ];
    }
}
