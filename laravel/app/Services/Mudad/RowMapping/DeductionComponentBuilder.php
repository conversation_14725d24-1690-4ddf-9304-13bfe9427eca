<?php
namespace App\Services\Mudad\RowMapping;

use App\Facades\Settings;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;
use App\Utils\PluginUtil;

class DeductionComponentBuilder extends AbstractBuilder implements MudadReviewDataBuilder
{

    public function build(ReviewData $reviewData): ReviewData
    {
        $mappings = $this->data->getMappings();
        $reviewData->setDeductionComponents($mappings['deductions'] ?? []);
        return $reviewData;
    }
}