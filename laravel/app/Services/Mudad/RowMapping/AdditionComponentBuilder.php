<?php
namespace App\Services\Mudad\RowMapping;

use App\Facades\Settings;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;
use App\Utils\PluginUtil;

class AdditionComponentBuilder extends AbstractBuilder implements MudadReviewDataBuilder
{

    public function build(ReviewData $reviewData): ReviewData
    {
        $mappings = $this->data->getMappings();
        $reviewData->setAdditionComponents($mappings['earnings'] ?? []);
        return $reviewData;
    }
}