<?php
namespace App\Services\Mudad\RowMapping;

use App\Facades\Settings;
use App\Helpers\Staff;
use App\Repositories\StaffRepository;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;

class ErrorsBuilder extends AbstractBuilder implements MudadReviewDataBuilder
{

    public function build(ReviewData $reviewData): ReviewData
    {
        $errors = [];
        $MudadComponent = $this->fileData->getPrimaryComponents();
        $employeeRows = $reviewData->getRows();
        $existsEmployees = [];
        if(count($reviewData->getRows()) < 1){
            $errors[] = [
                'text' => __t('Mudad File is Empty'),
                'actions' => []
            ];
        };

        $hasPercentageSalaryComponents = $this->hasPercentageSalaryComponents();
        foreach($employeeRows as $rowKey => $row){
            if($row->getMappedEmployee() == null){
                $errors[] = [
                    'text' => sprintf(__t('%s - No matching Employee found in Daftra'),$row->getName()),
                    'actions' => []
                ];
                continue;
            }
            if(in_array($row->getOfficialId(), $existsEmployees)){
                $dublicatedEmployee = resolve(StaffRepository::class)->findWhere(['official_id' => $row->getOfficialId()])?->first();
                $officialIdName = Staff::getStaffOfficialIdName($row->getMappedEmployee()['country_code'], $row->getMappedEmployee()['citizenship_status']);

                $errors[] = [
                    'text' => sprintf(__t('%s already exists for another employee at row %s'), $officialIdName, $rowKey +1),
                    'actions' => ["url"=> route('owner.staff.show', $dublicatedEmployee->id), "text" => $dublicatedEmployee->full_name.' #'.$dublicatedEmployee->code?? $dublicatedEmployee->id ]
                ];
                $row->setMappedEmployee([]);
                continue;
            }

            $existsEmployees[] = $row->getOfficialId();
            $basicComponents = $row->getPrimaryComponents();
            $mudaComponents = $row->getMudadComponents();
            if(array_sum($mudaComponents) != array_sum($basicComponents)){
                $errors[] =[
                    'text' => sprintf(__t('Gross salary does not match Mudad at row %s. Please review'), $rowKey +1),
                    'actions' => []
                ];
            }
            if(!$row->getPaySlipId()){
                $errors[] =[
                    'text' => sprintf(__t('Can not get values from daftara, due to missing Payslip from the selected payrun at row %s'), $rowKey +1),
                    'actions' => []
                ];
            }
            if(($mudaComponents[0] + $mudaComponents[1] == 0) && $hasPercentageSalaryComponents){
                $errors[] =[
                    'text' => sprintf(__t('Can not Calculate percentage salary components at row %s'), $rowKey +1),
                    'actions' => []
                ];
            }

            foreach($mudaComponents as $key => $component){
                if(!isset($basicComponents[$key]) || ($component != $basicComponents[$key])){
                    $errors[] = [
                        'text' => sprintf(__t('Mismatch in %s: Mudad = %s, Daftra = %s, at row %s'), $MudadComponent[$key]->name, $component, $basicComponents[$key], $rowKey +1),
                        'actions' => $row->getPaySlipId()? ["url"=> route('owner.payslips.show', $row->getPaySlipId()), "text" => __t('Review Payslip')]: []
                    ];
                }
            }
        }

        $reviewData->setErrors($errors);
        return $reviewData;
    }

    private function hasPercentageSalaryComponents(): bool
    {

        $mappings = $this->data->getMappings();
        $mappedAdditionComponents = $mappings['earnings'] ?? [];
        $mappedDeductionComponents = $mappings['deductions'] ?? [];
        foreach(array_merge($mappedAdditionComponents, $mappedDeductionComponents) as $component){
            if(str_contains($component['name'], '(نسبة)')){
                return true;
            }

        }
        return false;
    }
}