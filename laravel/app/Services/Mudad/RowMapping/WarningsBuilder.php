<?php
namespace App\Services\Mudad\RowMapping;

use App\Facades\Settings;
use App\Helpers\Staff;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;
use App\Utils\PluginUtil;

class WarningsBuilder extends AbstractBuilder implements MudadReviewDataBuilder
{

    public function build(ReviewData $reviewData): ReviewData
    {
        $warnings = [];
        $employeeRows = $reviewData->getRows();

        foreach($employeeRows as $rowKey => $row){
            $mappedEmplyee = $row->getMappedEmployee();
            if(is_array($mappedEmplyee) && $row->getOfficialId() != $mappedEmplyee['official_id']){
                $officialIdName = Staff::getStaffOfficialIdName($mappedEmplyee['country_code'], $mappedEmplyee['citizenship_status']);
                if(isset($row->getMappedEmployee()['official_id'])){
                    $warnings[] = sprintf(__t('Employee %s, changing it from %s to %s.'), $mappedEmplyee['name']?? "", $row->getMappedEmployee()['official_id'] ?? "", $row->getOfficialId() ?? "");
                }else{
                    $warnings[] = sprintf(__t('Employee %s, Assign new ID %s (no existing Haweya ID found for this employee).'), $mappedEmplyee['name']?? "", $row->getOfficialId() ?? "");

                }
            }
        }
        $reviewData->setWarnings($warnings);
        return $reviewData;
    }
}