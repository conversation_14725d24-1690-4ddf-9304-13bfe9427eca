<?php
namespace App\Services\Mudad\RowMapping;

use App\Facades\Settings;
use App\Repositories\PayRunRepository;
use App\Repositories\StaffRepository;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadEmployeeRow;
use App\Services\Mudad\MudadFileData;
use App\Utils\ContractStatusUtil;
use App\Utils\PluginUtil;

class RowsBuilder extends AbstractBuilder implements MudadReviewDataBuilder
{

    CONST EMPLOYEE_NAME_INDEX = 0;
    CONST EMPLOYEE_OFFICIAL_ID_INDEX = 1;
    CONST BASIC_ROW_INDEX = 2;
    CONST HOUSING_ROW_INDEX = 3;
    CONST TRANSPORTATION_ROW_INDEX = 4;

    public function build(ReviewData $reviewData): ReviewData
    {
        $mudadFileData = $this->fileData;
        $mudadData = $this->data;
        $fileEmployeesData = $mudadFileData->getEmployeesRows();
        $officialIDs = array_column($fileEmployeesData, self::EMPLOYEE_OFFICIAL_ID_INDEX);
        $manuallyMappedEmployeesMap = $this->getManuallyMappedEmployees($mudadData);
        $manuallyMappedEmployeesIds = array_values($manuallyMappedEmployeesMap);

        $staffRepository = resolve(StaffRepository::class);
        $staffs = $staffRepository->findWhereIn('official_id', $officialIDs);
        $manuallyMappedEmployees = $staffRepository->findWhereIn('id', $manuallyMappedEmployeesIds);
        $payRun = resolve(PayRunRepository::class)->find($this->data->getPayRunId());
        $currencyCode = $payRun->currency_code;
        $mappedEmployees = [];

        $mappings = $this->data->getMappings();
        $mappedAdditionComponents = $mappings['earnings'] ?? [];
        $mappedDeductionComponents = $mappings['deductions'] ?? [];
        foreach ($fileEmployeesData as $rowKey => $row) {
            if(empty($row[self::EMPLOYEE_OFFICIAL_ID_INDEX])) continue;
            $deductionWarnings = [];
            $additionWarnings = [];

            $additionPrefixes = [];
            $deductionPrefixes = [];

            // Create employee row object with basic data
            $employeeRow = new MudadEmployeeRow();
            $employeeRow->setName($row[self::EMPLOYEE_NAME_INDEX] ?? "");
            $employeeRow->setOfficialId($row[self::EMPLOYEE_OFFICIAL_ID_INDEX] ?? "");
            $employeeRow->setMappedEmployee([]);

            // Set basic salary components
            $basic = intval($row[self::BASIC_ROW_INDEX] ?? 0);
            $housing = intval($row[self::HOUSING_ROW_INDEX] ?? 0);
            $transport = intval($row[self::TRANSPORTATION_ROW_INDEX] ?? 0);
            $employeeRow->setMudadComponents([$basic, $housing, $transport]);

            $mappedEmployee= null;
            $mappedOfficialId = $manuallyMappedEmployeesMap[$row[self::EMPLOYEE_OFFICIAL_ID_INDEX]]??null;
            if(!empty($mappedOfficialId)) {
                $mappedEmployee = $manuallyMappedEmployees->where('id', $mappedOfficialId)->first();
            }

            if(empty($mappedEmployee)) {
                $mappedEmployee = $staffs->where('official_id', $row[self::EMPLOYEE_OFFICIAL_ID_INDEX])->first();
            }

            if(!empty($mappedEmployee)) {
                // Get payslip and salary components
                $payslip = $payRun->payslips()->where('staff_id', $mappedEmployee->id)->first();
                $paySlipId = $payslip?->id ?? 0;
                $employeeRow->setPaySlipId($paySlipId);
                $allSalaryComponents = $payslip?->components?->pluck('pivot.amount', 'id') ?? [];


                if($paySlipId){
                    $additionComponents = $this->processComponentGroup($mappedAdditionComponents, $allSalaryComponents, $basic, $housing);
                    $employeeRow->setAdditionComponents($additionComponents);

                    $deductionsComponents = $this->processComponentGroup($mappedDeductionComponents, $allSalaryComponents, $basic, $housing);
                    $employeeRow->setDeductionComponents($deductionsComponents);

                    foreach($additionComponents as $key => $component){
                        if($component == null){
                            $additionWarnings[] = sprintf(__t('No salary component found for %s on Payslip %s, Value set to 0'), $mappedAdditionComponents[$key]['name'], '<a href="'.route('owner.payslips.show', $paySlipId).'" target="_blank" rel="noopener noreferrer" style="color: #fff">#'.$paySlipId.'</a>');
                        }else{
                            $additionWarnings[] = null;
                        }
                    }
                    foreach($deductionsComponents as $key => $component){
                        if($component == null){
                            $deductionWarnings[] = sprintf(__t('No salary component found for %s on Payslip %s, Value set to 0'), $mappedDeductionComponents[$key]['name'], '<a href="'.route('owner.payslips.show', $paySlipId).'" target="_blank" rel="noopener noreferrer" style="color: #fff">#'.$paySlipId.'</a>');
                        }else{
                            $deductionWarnings[] = null;
                        }
                    }
                }


                // Process primary Mudad components
                $mudadsComponents = [];
                $mudadComponents = $mudadFileData->getPrimaryComponents();
                foreach($mudadComponents as $component) {
                    $value = 0;
                    foreach($component->salary_components as $componentId) {
                        $value += $allSalaryComponents[$componentId] ?? null;
                    }
                    $mudadsComponents[] = $value ?? null;
                }
                $employeeRow->setPrimaryComponents($mudadsComponents);

                // Calculate gross salary
                $employeeRow->setNetSalary(
                    round($payslip->net_pay ?? 0, 2)
                );


                $employeeRow->setAdditionSalaryComponentsWarnings($additionWarnings);
                $employeeRow->setDeductionSalaryComponentsWarnings($deductionWarnings);

                $additionPrefixes = $this->getSalayComponentsPrefixes( $mappedAdditionComponents, $currencyCode);
                $deductionPrefixes = $this->getSalayComponentsPrefixes( $mappedDeductionComponents, $currencyCode);
                $employeeRow->setAdditionSalaryComponentsPrefixes($additionPrefixes);
                $employeeRow->setDeductionSalaryComponentsPrefixes($deductionPrefixes);


                // Set employee mapping data
                $employeeRow->setMappedEmployee([
                    "id" => $mappedEmployee->id,
                    'name' => $mappedEmployee->name,
                    'official_id' => $mappedEmployee->official_id,
                    'country_code' => $mappedEmployee->country_code,
                    'citizenship_status' => $mappedEmployee->citizenship_status,
                ]);
            }

            $mappedEmployees[] = $employeeRow;
        }

        $reviewData->setRows($mappedEmployees);
        return $reviewData;
    }


    /**
     * Processes a group of salary components to calculate their values
     *
     * @param array $componentsGroup An array of component groups where each group contains:
     * - 'name': string The name of the component group
     * - 'components': array List of component IDs
     * @param array $allSalaryComponents Associative array of component IDs and their amounts
     * [component_id => amount]
     * @param float|int $basic The basic salary amount
     * @param float|int $housing The housing allowance amount
     *
     * @return array List of calculated component values where each value is either:
     * - A direct sum of component amounts
     * - A percentage calculation if component name contains '(نسبة)'
     *
     * @example
     * // Input examples:
     * $componentsGroup = [
     *   ['name' => 'Transportation', 'components' => [1, 2]],
     *   ['name' => 'Bonus (نسبة)', 'components' => [3]]
     * ];
     * $allSalaryComponents = [1 => 1000, 2 => 500, 3 => 300];
     * $basic = 5000;
     * $housing = 1000;
     *
     * // Returns: [1500, 0.05] // Where 0.05 is (300 / (5000 + 1000))
     */
    private function processComponentGroup($componentsGroup, $allSalaryComponents, $basic, $housing)
    {
        $result = [];
        foreach($componentsGroup as $components) {
            $componentVal = null;
            foreach($components['components'] as $componentId) {
                // Handle percentage-based components differently
                if(!isset($allSalaryComponents[$componentId])) continue;
                $componentVal += $allSalaryComponents[$componentId];
            }
            if(str_contains($components['name'], '(نسبة)')) {
                if($basic + $housing != 0){
                    $componentVal = (($componentVal / ($basic + $housing))*100);
                }else{
                    $componentVal = null;
                }
            }
            $result[] = $componentVal;
        }
        return $result;
    }


    private function getSalayComponentsPrefixes($salaryComponrntNames, $currencyCode)
    {
        $result = [];

        foreach($salaryComponrntNames as $key => $component) {
            $prefix = $currencyCode;
            if(str_contains($component['name'], '(نسبة)')) {
                $prefix = "%";
            }
            $result[] = $prefix;
        }
        return $result;
    }

    public function getManuallyMappedEmployees(MudadData $mudadData)
    {
        $mappedIds =[];
        $mappedEmployees =  $mudadData->getEmployeesMapping();
        foreach($mappedEmployees as $key => $mappedEmployee) {
            $mappedIds[$mappedEmployee['official_id']]  = $mappedEmployee['employee_id'];
        }
        return $mappedIds;

    }
}