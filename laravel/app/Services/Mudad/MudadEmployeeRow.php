<?php

namespace App\Services\Mudad;

class MudadEmployeeRow
{
    protected string $name;

    protected string $officialId;

    protected array $mappedEmployee;

    protected array $primaryComponents = [];

    protected array $mudadComponents = [];

    protected array $additionComponents = [];

    protected array $deductionComponents = [];

    protected float $netSalary = 0.0;

    protected int $paySlipId = 0;

    protected array $deductionSalaryComponentsWarnings = [];
    protected array $additionSalaryComponentsWarnings = [];

    protected array $additionSalaryComponentsPrefixes = [];
    protected array $deductionSalaryComponentsPrefixes = [];

    public function getMappedEmployee(): ?array
    {
        return empty($this->mappedEmployee) ? null : $this->mappedEmployee;
    }

    public function setMappedEmployee(array $mappedEmployee): void
    {
        $this->mappedEmployee = $mappedEmployee;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getOfficialId(): string
    {
        return $this->officialId;
    }

    public function setOfficialId(string $officialId): void
    {
        $this->officialId = $officialId;
    }

    public function getPrimaryComponents(): array
    {
        return $this->primaryComponents;
    }

    public function setPrimaryComponents(array $primaryComponents): void
    {
        $this->primaryComponents = $primaryComponents;
    }
    public function setMudadComponents(array $mudadComponents): void
    {
        $this->mudadComponents = $mudadComponents;
    }

    public function getMudadComponents(): array
    {
        return $this->mudadComponents;
    }


    public function getAdditionComponents(): array
    {
        return $this->additionComponents;
    }

    public function setAdditionComponents(array $additionComponents): void
    {
        $this->additionComponents = $additionComponents;
    }

    public function getDeductionComponents(): array
    {
        return $this->deductionComponents;
    }

    public function setDeductionComponents(array $deductionComponents): void
    {
        $this->deductionComponents = $deductionComponents;
    }

    public function getNetSalary(): float
    {
        return $this->netSalary;
    }

    public function setNetSalary(float $netSalary): void
    {
        $this->netSalary = $netSalary;
    }

    public function getPaySlipId(): int
    {
        return $this->paySlipId;
    }

    public function setPaySlipId(int $paySlipId): void
    {
        $this->paySlipId = $paySlipId;
    }

    public function getDeductionSalaryComponentsWarnings(): array
    {
        return $this->deductionSalaryComponentsWarnings;
    }

    public function setDeductionSalaryComponentsWarnings(array $deductionSalaryComponentsWarnings): void
    {
        $this->deductionSalaryComponentsWarnings = $deductionSalaryComponentsWarnings;
    }

    public function getAdditionSalaryComponentsWarnings(): array
    {
        return $this->additionSalaryComponentsWarnings;
    }

    public function setAdditionSalaryComponentsWarnings(array $additionSalaryComponentsWarnings): void
    {
        $this->additionSalaryComponentsWarnings = $additionSalaryComponentsWarnings;
    }

    public function getAdditionSalaryComponentsPrefixes(): array
    {
        return $this->additionSalaryComponentsPrefixes;
    }

    public function setAdditionSalaryComponentsPrefixes(array $additionSalaryComponentsPrefixes): void
    {
        $this->additionSalaryComponentsPrefixes = $additionSalaryComponentsPrefixes;
    }

    public function getDeductionSalaryComponentsPrefixes(): array
    {
        return $this->deductionSalaryComponentsPrefixes;
    }

    public function setDeductionSalaryComponentsPrefixes(array $deductionSalaryComponentsPrefixes): void
    {
        $this->deductionSalaryComponentsPrefixes = $deductionSalaryComponentsPrefixes;
    }

    public function toArray(): array
    {
        return [
            'mudad_name' => $this->getName(),
            'official_id' => $this->getOfficialId(),
            'basic_components' => $this->getPrimaryComponents(),
            'mudad_basic_components' => $this->getMudadComponents(),
            'additions_components' => $this->getAdditionComponents(),
            'deductions_components' => $this->getDeductionComponents(),
            'net_salary' => $this->getNetSalary(),
            'mapped_employee' => $this->getMappedEmployee() ?? null,
            'gross_salary' => $this->getNetSalary(),
            'addition_components_warnings' => $this->getAdditionSalaryComponentsWarnings(),
            'deduction_components_warnings' => $this->getDeductionSalaryComponentsWarnings(),
            'addition_components_prefixes' => $this->getAdditionSalaryComponentsPrefixes(),
            'deduction_components_prefixes' => $this->getDeductionSalaryComponentsPrefixes(),
        ];
    }
}
