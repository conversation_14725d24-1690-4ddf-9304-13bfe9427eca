<?php

namespace App\Services\Mudad;

use App\Services\Mudad\RowMapping\AdditionComponentBuilder;
use App\Services\Mudad\RowMapping\DeductionComponentBuilder;
use App\Services\Mudad\RowMapping\ErrorsBuilder;
use App\Services\Mudad\RowMapping\MudadReviewDataBuilder;
use App\Services\Mudad\RowMapping\MudadSalaryComponentBuilder;
use App\Services\Mudad\RowMapping\ReviewData;
use App\Services\Mudad\RowMapping\RowsBuilder;
use App\Services\Mudad\RowMapping\StatsBuilder;
use App\Services\Mudad\RowMapping\UploadedFileData;
use App\Services\Mudad\RowMapping\UploadedFileDataBuilder;
use App\Services\Mudad\RowMapping\WarningsBuilder;

class MudadRowsMappingDataPipeline
{
    /** @var array<MudadReviewDataBuilder> */
    protected array $builders = [
        RowsBuilder::class,
        StatsBuilder::class,
        ErrorsBuilder::class,
        MudadSalaryComponentBuilder::class,
        AdditionComponentBuilder::class,
        DeductionComponentBuilder::class,
        WarningsBuilder::class,
    ];

    public function process(MudadData $data, MudadFileData $mudadFileData): ReviewData
    {
        $reviewData = new ReviewData();
        foreach ($this->builders as $builder) {
            $builderClass = resolve($builder);
            $builderClass->setMudadData($data)
                    ->setMudadFileData($mudadFileData);
            $reviewData = $builderClass->build($reviewData);
        }
        return $reviewData;
    }





}
