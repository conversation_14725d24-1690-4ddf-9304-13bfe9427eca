<?php
/**
 * MudadData.php
 *
 * This class represents the data structure for Mudad integration in the Daftra system.
 * Mudad is a Saudi Arabian payroll system that requires specific data formatting.
 *
 * The MudadData class encapsulates all the necessary information needed to process
 * payroll data for Mudad integration, including file metadata, mapping configurations,
 * and employee information.
 *
 * Data Structure:
 * {
 *   "file_id": 1,                  // ID of the uploaded file in the system
 *   "step": 1,                     // Current processing step (1-3)
 *   "file_meta": {                 // Metadata about the file
 *     "file_name": "payroll.json", // Original filename
 *     "type": "json",              // File type
 *     "uploaded_by": "<PERSON>",   // User who uploaded the file
 *     "upload_date": "2023-01-01"  // Date of upload
 *   },
 *   "mappings": {                  // Mappings for salary components
 *     "additions": [               // Addition components (bonuses, allowances, etc.)
 *       {
 *         "name": "خارج دوام (قيمة)", // Component name in Mudad
 *         "components": [          // Corresponding components in Daftra
 *           {"id": 1, "name": "Overtime"}
 *         ]
 *       }
 *     ],
 *     "deductions": [              // Deduction components (absences, loans, etc.)
 *       {
 *         "name": "absent",        // Component name in Mudad
 *         "components": [          // Corresponding components in Daftra
 *           {"id": 2, "name": "Absence"}
 *         ]
 *       }
 *     ]
 *   },
 *   "employees_mapping": [         // Mapping between Daftra employees and Mudad IDs
 *     {
 *       "employee_id": 1,          // Daftra employee ID
 *       "official_id": "12345",    // National ID/Iqama number for Mudad
 *       "name": "John Doe",        // Employee name for display
 *       "mapped": true             // Whether the employee is successfully mapped
 *     }
 *   ]
 * }
 *
 * Usage:
 * - Step 1: Upload file and initialize MudadData
 * - Step 2: Configure mappings between Daftra and Mudad components
 * - Step 3: Map employees and validate data
 * - Step 4: Generate final Mudad file
 *
 * @package App\Services\Mudad
 * <AUTHOR> Development Team
 * @version 1.0
 */
namespace App\Services\Mudad;

class MudadData
{

    protected int $payRunId;
    /**
     * The original JSON data as a string.
     * This preserves the exact format of the data as it was received.
     *
     * @var string
     */
    protected string $originalJsonData;

    /**
     * The ID of the file in the system's storage.
     * This references the uploaded file in the attachments table.
     *
     * @var int
     */
    protected int $fileId;

    /**
     * The current processing step.
     * - Step 1: File upload
     * - Step 2: Component mapping
     * - Step 3: Employee mapping
     *
     * @var int
     */
    protected int $step;

    /**
     * Metadata about the file.
     * Contains information such as:
     * - file_name: Original name of the file
     * - type: File type (json, csv, xlsx)
     * - uploaded_by: User ID who uploaded the file
     * - upload_date: Date and time of upload
     *
     * @var array
     */
    protected array $fileMeta;

    /**
     * Mappings between Daftra salary components and Mudad components.
     * Organized into two categories:
     * - additions: Positive components (bonuses, allowances)
     * - deductions: Negative components (absences, loans)
     *
     * Each mapping contains:
     * - name: The name of the component in Mudad
     * - components: Array of Daftra components that map to this Mudad component
     *
     * @var array
     */
    protected array $mappings;

    /**
     * Mappings between Daftra employees and their Mudad identifiers.
     * Each mapping contains:
     * - employee_id: The Daftra employee ID
     * - official_id: The national ID or Iqama number used in Mudad
     * - name: The employee's name (for display purposes)
     * - mapped: Boolean indicating if the employee is successfully mapped
     *
     * @var array
     */
    protected array $employeesMapping;

    /**
     * MudadData constructor.
     *
     * Initializes a new MudadData object with the provided JSON data and properties.
     * If no JSON data is provided, an empty JSON object is used as default.
     *
     * @param string $jsonData The original JSON data as a string
     */
    public function __construct(string $jsonData)
    {
        $this->originalJsonData = $jsonData;
    }

    /**
     * Get the original JSON data.
     *
     * @return string The original JSON data as a string
     */
    public function getOriginalJsonData(): string
    {
        return $this->originalJsonData;
    }

    /**
     * Set the original JSON data.
     *
     * @param string $originalJsonData The JSON data to set
     * @return void
     */
    public function setOriginalJsonData(string $originalJsonData): void
    {
        $this->originalJsonData = $originalJsonData;
    }

    /**
     * Get the file ID.
     *
     * @return int The ID of the file in the system
     */
    public function getFileId(): int
    {
        return $this->fileId;
    }

    /**
     * Set the file ID.
     *
     * @param int $fileId The ID of the file to set
     * @return void
     */
    public function setFileId(int $fileId): void
    {
        $this->fileId = $fileId;
    }

    /**
     * Get the current processing step.
     *
     * @return int The current step (1-3)
     */
    public function getStep(): int
    {
        return $this->step;
    }

    /**
     * Set the current processing step.
     *
     * @param int $step The step to set
     * @return void
     */
    public function setStep(int $step): void
    {
        $this->step = $step;
    }

    /**
     * Get the file metadata.
     *
     * @return array The file metadata
     */
    public function getFileMeta(): array
    {
        return $this->fileMeta;
    }

    /**
     * Set the file metadata.
     *
     * @param array $fileMeta The file metadata to set
     * @return void
     */
    public function setFileMeta(array $fileMeta): void
    {
        $this->fileMeta = $fileMeta;
    }

    /**
     * Get the component mappings.
     *
     * @return array The mappings between Daftra and Mudad components
     */
    public function getMappings(): array
    {
        return $this->mappings;
    }

    /**
     * Set the component mappings.
     *
     * @param array $mappings The mappings to set
     * @return void
     */
    public function setMappings(array $mappings): void
    {
        $this->mappings = $mappings;
    }

    /**
     * Get the employee mappings.
     *
     * @return array The mappings between Daftra employees and Mudad IDs
     */
    public function getEmployeesMapping(): array
    {
        return $this->employeesMapping;
    }

    /**
     * Set the employee mappings.
     *
     * @param array $employeesMapping The employee mappings to set
     * @return void
     */
    public function setEmployeesMapping(array $employeesMapping): void
    {
        $this->employeesMapping = $employeesMapping;
    }

    public function getPayRunId(): int
    {
        return $this->payRunId;
    }

    public function setPayRunId(int $payRunId): void
    {
        $this->payRunId = $payRunId;
    }

    /**
     * Convert the MudadData object to an array.
     *
     * This method is useful for serializing the object to JSON or
     * for passing the data to views or other components.
     *
     * @return array The object data as an array
     */
    public function toArray(): array
    {
        return [
            'file_id' => $this->fileId,
            'step' => $this->step,
            'file_meta' => $this->fileMeta,
            'mappings' => $this->mappings,
            'employees_mapping' => $this->employeesMapping
        ];
    }
}
