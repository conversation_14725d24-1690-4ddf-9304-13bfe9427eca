<?php
/**
 * MudadDataBuilder.php
 * this file is part of the Izam Daftra Mudad package.
 */
namespace App\Services\Mudad;

use App\Repositories\StaffRepository;
use Carbon\Carbon;

class MudadDataBuilder
{
    public function __construct(protected StaffRepository $staffRepository)
    {
    }

    public function build(string $data): MudadData
    {
        $mudadData = new MudadData($data);
        $data = json_decode($data, true) ?: [];
        $data['file_meta'] = $this->mapFileUploaderName($data['file_meta'] ?? []);
        $data['file_meta'] = $this->formatDates($data['file_meta'] ?? []);
        $mudadData->setFileId($data['file_id'] ?? 0);
        $mudadData->setStep($data['step'] ?? 1);
        $mudadData->setFileMeta($data['file_meta'] ?? []);
        $mudadData->setMappings($data['mappings'] ?? []);
        $mudadData->setEmployeesMapping($data['employeesMapping'] ?? []);
        $mudadData->setOriginalJsonData(json_encode($mudadData->toArray()));
        return $mudadData;
    }

    private function mapFileUploaderName($fileMeta){

        if(!isset($fileMeta['uploaded_by'])) return $fileMeta;
        if($fileMeta['uploaded_by']  == 0) {
            $fileMeta['uploaded_by'] = getCurrentSite('business_name');
            return $fileMeta;
        }

        $staff = $this->staffRepository->find($fileMeta['uploaded_by']);
        $fileMeta['uploaded_by'] = $staff?->full_name ?? "";
        return $fileMeta;
    }


    private function formatDates($fileMeta){
        $dateFormats = getDateFormats('std');
        $dateFormat = $dateFormats[getCurrentSite('date_format')];
        if(isset($fileMeta['start_date'])) $fileMeta['start_date'] = Carbon::parse($fileMeta['start_date'])->format($dateFormat);
        if(isset($fileMeta['end_date'])) $fileMeta['end_date'] = Carbon::parse($fileMeta['end_date'])->format($dateFormat);
        if(isset($fileMeta['uploaded_on'])) $fileMeta['uploaded_on'] = Carbon::parse($fileMeta['uploaded_on'])->format($dateFormat." H:i:s");
        return $fileMeta;
    }
}
