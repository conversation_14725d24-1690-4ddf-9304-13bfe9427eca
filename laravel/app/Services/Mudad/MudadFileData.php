<?php

namespace App\Services\Mudad;

class MudadFileData
{
    /**
     * @var MudadEmployeeRow[] $employeesRows
     */
    protected array $employeesRows = [];

    protected array $columns = [];

    protected array $primaryComponents;
    protected array $additionComponents;

    protected array $deductionComponents;

    protected int $rowsCount;

    protected int $mappedUsersCount;

    /**
     * @return MudadEmployeeRow[]
     */
    public function getEmployeesRows(): array
    {
        return $this->employeesRows;
    }

    /**
     * @param MudadEmployeeRow[] $employeesRows
     */
    public function setEmployeesRows(array $employeesRows): void
    {
        $this->employeesRows = $employeesRows;
    }

    public function getColumns(): array
    {
        return $this->columns;
    }

    public function setColumns(array $columns): void
    {
        $this->columns = $columns;
    }

    public function getPrimaryComponents(): array
    {
        return $this->primaryComponents;
    }

    public function setPrimaryComponents(array $primaryComponents): void
    {
        $this->primaryComponents = $primaryComponents;
    }

    public function getAdditionComponents(): array
    {
        return $this->additionComponents;
    }

    public function setAdditionComponents(array $additionComponents): void
    {
        $this->additionComponents = $additionComponents;
    }

    public function getDeductionComponents(): array
    {
        return $this->deductionComponents;
    }

    public function setDeductionComponents(array $deductionComponents): void
    {
        $this->deductionComponents = $deductionComponents;
    }

    public function getRowsCount(): int
    {
        return $this->rowsCount;
    }

    public function setRowsCount(int $rowsCount): void
    {
        $this->rowsCount = $rowsCount;
    }

    public function getMappedUsersCount(): int
    {
        return $this->mappedUsersCount;
    }

    public function setMappedUsersCount(int $mappedUsersCount): void
    {
        $this->mappedUsersCount = $mappedUsersCount;
    }

}
