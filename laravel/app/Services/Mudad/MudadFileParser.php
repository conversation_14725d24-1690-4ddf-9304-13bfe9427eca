<?php

namespace App\Services\Mudad;

use App\Facades\Settings;
use App\Models\File;
use App\Repositories\PayRunRepository;
use App\Repositories\StaffRepository;
use App\Services\Mudad\MudadEmployeeRow;
use App\Utils\ContractStatusUtil;
use App\Services\FileService;
use App\Utils\PluginUtil;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Setting;

class MudadFileParser
{
    CONST EMPLOYEE_NAME_INDEX = 0;
    CONST EMPLOYEE_OFFICIAL_ID_INDEX = 1;
    CONST BASIC_ROW_INDEX = 2;
    CONST HOUSING_ROW_INDEX = 3;
    CONST TRANSPORTATION_ROW_INDEX = 4;

    const EMPLOYEE_DATA_STARTING_ROW = 5;

    const EMPLOYEE_OFFICIAL_ID_COLUMN = 'B';

    protected Worksheet $sheet;

    public const MUDAD_FILE_EARNINGS_COLUMN = 'الإستحقاقات';
    public const MUDAD_FILE_DEDUCTIONS_COLUMN = 'الاستقطاعات';

    /**
     * @throws FileNotFoundException
     */
    public function __construct(protected string $fileId)
    {
        $this->loadFileSheet();
    }

    /**
     * @throws FileNotFoundException
     */
    public function parse(): array
    {
        $headers = $this->getFileHeaders();
        return [
          'earnings' => $this->formatComponents($headers[0]),
          'deductions' => $this->formatComponents($headers[1]),
        ];
    }


    /**
     * @desc loads a file from s3 into a temp file to be able to access it in Excel as ->get($file->path) will return binary string
     * @throws FileNotFoundException
     */
    private function loadFileSheet(): Worksheet
    {
        $fileService = resolve(FileService::class);
        $file = $fileService->repo->find($this->fileId);
        $fileContent = Storage::disk('s3')->get($file->path);

        $tempPath = storage_path('app/' . basename($file->path));
        file_put_contents($tempPath, $fileContent);

        $spreadsheet = IOFactory::load($tempPath);
        $this->sheet = $spreadsheet->getActiveSheet();

        // Delete temp file
        unlink($tempPath);
        return $this->sheet;
    }


    /**
     * Extracts header values from the rows below earnings and deductions sections in the spreadsheet.
     *
     * This function identifies the earnings and deductions column ranges in the spreadsheet,
     * then extracts the header values from the row immediately below each of these ranges.
     * These headers typically represent different types of earnings (like basic salary, allowances)
     * and deductions (like taxes, insurance) that will be used for data processing.
     *
     * @return array A nested array where each inner array contains the headers for one section, where index 0 is for earnings and index 1 is for deductions
     *
     * @throws FileNotFoundException
     * @throws \Exception
     */
    private function getFileHeaders(): array
    {

        $earningsAndDeductionsColumnsRange = $this->getEarningAndDeductionsColumns();

        $subHeaders = [];
        foreach ($earningsAndDeductionsColumnsRange as $key => $value) {

            [$start, $end] = explode(':', $value);

            // Extract the column letter from the start cell
            [$startCol,] = Coordinate::coordinateFromString($start);
            // Extract the column letter and row number from the end cell
            [$endCol, $endRow] = Coordinate::coordinateFromString($end);

            // Convert column letters to numeric indices
            $startIndex = Coordinate::columnIndexFromString($startCol);
            $endIndex = Coordinate::columnIndexFromString($endCol);


            $setOfHeaders = [];
            // Loop through each column in the range
            for ($i = $startIndex; $i <= $endIndex; $i++) {
                $colLetter = Coordinate::stringFromColumnIndex($i);
                $value = $this->sheet->getCell($colLetter . ($endRow + 1))->getValue();
                $setOfHeaders[] = trim($value);
            }
            $subHeaders[] = $setOfHeaders;
        }
        return $subHeaders;
    }


    /**
     * Retrieves the column ranges for earnings and deductions sections in a worksheet.
     *
     * This function scans through all merged cells in the provided worksheet and identifies
     * the ranges that contain the headers for earnings ('الإستحقاقات') and deductions ('الاستقطاعات').
     * These ranges are used to determine which columns contain earnings and deductions data.
     *
     * @return array An array of cell ranges (strings in format 'A1:B2') that contain earnings and deductions headers, where the first index is the earnings components, the second is the deduction
     */
    private function getEarningAndDeductionsColumns(): array
    {
        $mergedCells = $this->sheet->getMergeCells();
        $targetedHeaders = [self::MUDAD_FILE_EARNINGS_COLUMN, self::MUDAD_FILE_DEDUCTIONS_COLUMN];

        $earningsAndDeductions = [];
        foreach ($mergedCells as $range) {
            [$startCell,] = explode(':', $range);
            $cellValue = trim($this->sheet->getCell($startCell)->getValue());

            if (in_array($cellValue, $targetedHeaders)) {
                $earningsAndDeductions[] = $range;
            }
        }
        if (count($earningsAndDeductions) !== 2) {
            throw new \Exception(__t("You are not using a correct version of Mudad Sheet, please upload the correct file"));
        }
        return $earningsAndDeductions;
    }

    private function formatComponents(array $components): array
    {
        $defaultSalaryComponents = Settings::getValue(PluginUtil::MUDAD_PLUGIN , 'mudad_default_headers_components');
        $oldHeaderComponents = json_decode($defaultSalaryComponents ?? "" , true);

        $result = [];
        foreach ($components as $component) {
            $components = [];

            if (array_key_exists($component , $oldHeaderComponents)) {
                $components = array_values($oldHeaderComponents[$component]);
            }

            $result[] = [
                "name" => $component,
                "components" => $components,
            ];
        }
        return $result;
    }

    public function mapEmployeesData(MudadData $mudadData, MudadFileData $mudadFileData)
    {
        // Get employee data from sheet
        $rows = $this->sheet->toArray();
        $fileEmployeesData = array_slice($rows, 4);
        $mudadFileData->setEmployeesRows($fileEmployeesData);

        $officialIDs = array_column($fileEmployeesData, self::EMPLOYEE_OFFICIAL_ID_INDEX);
        $mudadFileData->setRowsCount(count(array_filter($officialIDs, fn($el) => !empty($el))));

        return $mudadFileData;
    }

    /**
     * Process a group of components (earnings or deductions)
     */
    private function processComponentGroup($componentsGroup, $allSalaryComponents, $basic, $housing)
    {
        $result = [];
        foreach($componentsGroup as $components) {
            $componentVal = null;
            foreach($components['components'] as $componentId) {
                // Handle percentage-based components differently
                if(!isset($allSalaryComponents[$componentId])) continue;
                if(str_contains($components['name'], '(نسبة)')) {
                    $componentVal += round(($allSalaryComponents[$componentId] ?? 0) / ($basic + $housing), 2) ?? null;
                } else {
                    $componentVal += $allSalaryComponents[$componentId];
                }
            }
            $result[] = $componentVal;
        }
        return $result;
    }

    /**
     * @param File $file
     * @param $employeesRows , array of values mapped from salary components
     * @return \PhpOffice\PhpSpreadsheet\Spreadsheet
     * @throws \Exception
     * @desc writes mapped values into the mudad file and returns  it as a response
     */
    public function exportMudadFile(File $file,$employeesRows)
    {
        try {
            $content = Storage::disk('s3')->get($file->path);
            $tempPath = storage_path('app/' . basename($file->path));
            file_put_contents($tempPath, $content);
            $spreadsheet = IOFactory::load($tempPath);
            $sheet = $spreadsheet->getActiveSheet();

            // Register a shutdown function to delete the temporary file
            // Either this or store mudad files in a directory that gets cleaned daily
            register_shutdown_function(function () use ($tempPath) {
                @unlink($tempPath);
            });
            $earningsAndDeductions = $this->getEarningAndDeductionsColumns();

            $earnings = explode(':', $earningsAndDeductions[0]);
            $earningsRange = range($earnings[0], $earnings[1]);

            $deductions = explode(':', $earningsAndDeductions[1]);
            $deductionsRange = range($deductions[0], $deductions[1]);

            $highestRow = $sheet->getHighestRow();

            $employeeRowMap = [];
            for ($row = self::EMPLOYEE_DATA_STARTING_ROW ; $row <= $highestRow; $row++) {
                $officialIdColumn = self::EMPLOYEE_OFFICIAL_ID_COLUMN . $row;
                $cellValue = $sheet->getCell($officialIdColumn)->getValue();
                if ($cellValue) {
                    $employeeRowMap[$cellValue] = $row;
                }
            }

            foreach ($employeesRows as $employeesRow) {
                $employeesId = $employeesRow->getOfficialId();
                if (isset($employeeRowMap[$employeesId])) {
                    $row = $employeeRowMap[$employeesId];
                    $this->updateSheetCellsForRange($sheet, $row, $earningsRange, $employeesRow->getAdditionComponents());
                    $this->updateSheetCellsForRange($sheet, $row, $deductionsRange, $employeesRow->getDeductionComponents());
                }
            }

            return $spreadsheet;
        }  catch (\PhpOffice\PhpSpreadsheet\Exception $e) {
            throw new \RuntimeException('Error processing spreadsheet: ' . $e->getMessage());
        } catch (\Illuminate\Contracts\Filesystem\FileNotFoundException $e) {
            throw new \RuntimeException('Error accessing file: ' . $e->getMessage());
        }
    }

    private function updateSheetCellsForRange(Worksheet $sheet, int $row , array $cellsRange, array $componentsValues)
    {
        foreach ($cellsRange as $index => $col) {
            $sheet->setCellValue("{$col}{$row}", $componentsValues[$index] ?? 0);
        }
    }

}
