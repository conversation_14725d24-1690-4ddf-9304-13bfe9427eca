<?php

namespace App\Services\ExcelForms\Forms;

use App\Repositories\ProductRepository;
use App\Services\ExcelForms\EntityFormInterface;
use App\Services\ExcelForms\ProcessableFormInterface;
use Illuminate\Support\Facades\Validator;
use App\Repositories\UnitFactorRepository;
use App\Repositories\UnitTemplateRepository;
use App\Models\StoreStockBalance;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Repositories\CostCenterRepository;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Common\Utils\TrackStockTypesUtil;

class OutboundRequisitionForm implements EntityFormInterface, ProcessableFormInterface{

    use FormTrait,ProcessProductTrait;

    public function getEntityName() : string{
        return "outbound-requisition";
    }

    public function getFields() : array{
        $commonFields = [
            'name' => ['type' => 'text', 'label' => __t('Item Code / Name'), 'size' => 65],
            'quantity' => ['type' => 'number', 'label' => __t('Quantity'), 'size' => 15, 'options' => ['step' => '0.*********']],
          //  'price' => ['type' => 'number', 'label' => __t('Unit Price'), 'size' => 15, 'options' => ['step' => '0.*********']]
        ];

        if(Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION)){
            $commonFields['cost_center_id'] = ['type' => 'text', 'label' => __t('Cost Center Code / Name'), 'size' => 65];
        }

        if(Plugins::PluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN)){
            $commonFields['tracking_data'] = ['type' => 'text', 'label' => __t('Tracking Data'), 'size' => 80];
        }

        return $commonFields;
    }

    public function getValidations() : array{
        /**
         * Custom validation rule
         */
        Validator::extend('product_exists_by_name_or_code', function($attribute, $value, $parameters)
        {
            $productRepo = resolve(ProductRepository::class);
            $result = $productRepo->productExistsByNameOrCode($value);
            return $result;
        });

        Validator::extend('cost_center_code_or_name_exists', function($attribute, $value, $parameters)
        {
            $costCenterRepo = resolve(CostCenterRepository::class);
            return $costCenterRepo->doesCostCenterCodeOrNameExist($value);
        });

        return [
            "*.name"      => "required|string|product_exists_by_name_or_code:currentSite.products",
            "*.cost_center_id"      => "nullable|string|cost_center_code_or_name_exists:currentSite.cost_centers",
            "*.quantity"  => "required|nullable|numeric|min:0",
            //"*.price"     => "nullable|numeric|min:0"
        ];
    }

    public function getValidationsMessages() : array{
        return [
            "product_exists_by_name_or_code" => __t("The Item code not Found in the system"),
            "*.quantity.min" => __t("You should enter valid positive numbers in the quantity"),
            "cost_center_code_or_name_exists" => __t("The Cost center name / code not Found in the system"),
          //  "*.price.min" => __t("You should enter valid positive numbers in the unit price")
        ];
    }

    public function process($data, $request) : array{
        $productRepo = resolve(ProductRepository::class);
        $unitFactorRepo = resolve(UnitFactorRepository::class);
        $unitTemplateRepo = resolve(UnitTemplateRepository::class);
        $costCenterRepo = resolve(CostCenterRepository::class);
        $result = [];
        foreach($data as $record){
            $product = $productRepo->productByNameOrCode($record['name']);
            $product->average_price = $product->average_price ?? ($record['price'] ?? 0);
            $product->quantity = $record['quantity'];
            $product = $product->toArray();
            //This flag is added to override unit template default calculation when using copy from Excel
            $product['ignoreDefault'] = true;
            $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first();
            if($unitTemplate) {
                 $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first()->toArray();
                 $product['factor'] = $unitTemplate;
                 $unitTemplateFirst = ['id' => 0 , 'small_name' => ($unitTemplate['unit_small_name']? $unitTemplate['unit_small_name']: "" ),
                                     'factor_name' =>$unitTemplate['main_unit_name'],'factor' => 1];

                 $product['unit_factors'] =  $unitFactorRepo->findWhere(['unit_template_id' => $product['unit_template_id']])->toArray();
                 array_unshift($product['unit_factors'], $unitTemplateFirst);
            }
            if (!empty($record['tracking_data']) && $product['tracking_type'] !== TrackStockTypesUtil::QUANTITY_ONLY) {
                $product['tracking_data'] = $this->getTrackingData($product['tracking_type'], $record['tracking_data']);
            }

            $storeBalances = StoreStockBalance::where("product_id", $product['id'])->get();

            $storeBalanceArray = $storeBalances->mapWithKeys(function ($item) {
                return [$item->store_id => $item->balance];
            });

            $product['store_balance'] = $storeBalanceArray->toArray();
            if (!empty($record['tracking_data']) && $product['tracking_type'] !== TrackStockTypesUtil::QUANTITY_ONLY) {
                $product['tracking_data'] = $this->getTrackingData($product['tracking_type'], $record['tracking_data']);
            }

           if(isset($record['cost_center_id'])) {
                $costCenter = $costCenterRepo->getCostCenterByCodeORName($record['cost_center_id']);
                $product['costCenter'] = $costCenter->id;
                $product['costCenterName'] = $costCenter->name;
            }
            

            $result[] = $product;
        }
        return ['data' => $result, 'clear_old_records' => $request->get('clear_old_records') == "on" ? true : false];
    }

}
