<?php

namespace App\Services\ExcelForms\Forms;

use App\Repositories\ProductRepository;
use App\Repositories\StockTransactionRepository;
use App\Requests\Inventory\GetProductBalanceRequest;
use App\Services\ExcelForms\EntityFormInterface;
use App\Services\ExcelForms\ProcessableFormInterface;
use Illuminate\Support\Facades\Validator;
use App\Repositories\UnitFactorRepository;
use App\Repositories\UnitTemplateRepository;
use App\Facades\Settings;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\TrackStockTypesUtil;
use App\Facades\Plugins;
use App\Repositories\CostCenterRepository;
use Izam\Daftra\Common\Utils\SettingsUtil;

class OrderForm implements EntityFormInterface, ProcessableFormInterface{

    use FormTrait ,ProcessProductTrait;

    public function getEntityName() : string
    {
        return "purchase-order";
    }

    public function getFields() : array
    {
        $commonFields = [
            'name' => ['type' => 'text', 'label' => __t('Item Code / Name'), 'size' => 65],
            'quantity' => ['type' => 'number', 'label' => __t('Quantity'), 'size' => 15, 'options' => ['step' => '0.*********']],
            'price' => ['type' => 'number', 'label' => __t('Unit Price'), 'size' => 15, 'options' => ['step' => '0.*********']]
        ];

        if(Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION)){
            $commonFields['cost_center_id'] = ['type' => 'text', 'label' => __t('Cost Center Code / Name'), 'size' => 65];
        }
        
        if(Plugins::PluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN) && !Settings::getValue(PluginUtil::InventoryPlugin, 'enable_requisitions_po')){
            $commonFields['tracking_data'] = ['type' => 'text', 'label' => __t('Tracking Data'), 'size' => 80];
        }
        
        return $commonFields;
    }

    public function getValidations() : array
    {
        /**
         * Custom validation rule 
         */
        Validator::extend('product_exists_by_name_or_code', function($attribute, $value, $parameters)
        {
            $productRepo = resolve(ProductRepository::class);
            $result = $productRepo->productExistsByNameOrCode($value);
            return $result;
        });

        Validator::extend('cost_center_code_or_name_exists', function($attribute, $value, $parameters)
        {
            $costCenterRepo = resolve(CostCenterRepository::class);
            return $costCenterRepo->doesCostCenterCodeOrNameExist($value);
        });

        return [
            "*.name"      => "required|string|product_exists_by_name_or_code:currentSite.products",
            "*.cost_center_id"      => "nullable|string|cost_center_code_or_name_exists:currentSite.cost_centers",
            "*.quantity"  => "required|numeric|min:0",
            "*.price"     => "nullable|numeric|min:0"
        ];
    }

    public function getValidationsMessages() : array
    {
        return [
            "product_exists_by_name_or_code" => __t("The Item code not Found in the system"),
            "cost_center_code_or_name_exists" => __t("The Cost center name / code not Found in the system"),
            "*.quantity.min" => __t("You should enter valid positive numbers in the quantity"),
            "*.quantity.required" => __t("You should enter valid positive numbers in the quantity"),
            "*.price.min" => __t("You should enter valid positive numbers in the unit price")];
    }

    public function process($data, $request) : array
    {  
        $productRepo = resolve(ProductRepository::class);
        $stockTransactionRepo = resolve(StockTransactionRepository::class);
        $unitFactorRepo = resolve(UnitFactorRepository::class); 
        $unitTemplateRepo = resolve(UnitTemplateRepository::class); 
        $costCenterRepo = resolve(CostCenterRepository::class);
        $result = [];
        foreach($data as $record){
           $product = $productRepo->productByNameOrCode($record['name']);
           if (!empty($record['price']) && ($product->unit_price != $record['price'])) {
                $product->unit_price = $record['price'];
                $product->discount = null;

           } else {
               $product->unit_price = $product->buy_price;
           }
           $product->quantity = $record['quantity'] ?? $product->quantity;
           $product = $product->toArray(); 
           //This flag is added to override unit template default calculation when using copy from Excel
           $product['ignoreDefault'] = true;
           $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first();
           if($unitTemplate) {
                $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first()->toArray();
                $product['factor'] = $unitTemplate;
                $unitTemplateFirst = ['id' => 0 , 'small_name' => ($unitTemplate['unit_small_name']? $unitTemplate['unit_small_name']: "" ),
                                    'factor_name' =>$unitTemplate['main_unit_name'],'factor' => 1];
                $buyUnitTemplate = $unitFactorRepo->findWhere(['id' => $product['default_buy_factor_id']])->first();
                if (isset($buyUnitTemplate) && $buyUnitTemplate->factor != 0) {
                    $product['unit_price'] = $product['unit_price'] / $buyUnitTemplate->factor;
                } 
                $product['unit_factors'] =  $unitFactorRepo->findWhere(['unit_template_id' => $product['unit_template_id']])->toArray();
                array_unshift($product['unit_factors'], $unitTemplateFirst);
           }

           $balanceRequest = new GetProductBalanceRequest($product['id']);
           $balanceRequest->setStoreId($product['raw_store_id']);
           $product['store_balance'][$product['raw_store_id']] = $stockTransactionRepo->getBalance($balanceRequest);
          
           if (!empty($record['tracking_data']) && $product['tracking_type'] !== TrackStockTypesUtil::QUANTITY_ONLY) {
                $product['tracking_data'] = $this->getTrackingData($product['tracking_type'], $record['tracking_data']);
           }

           if(isset($record['cost_center_id'])) {
                $costCenter = $costCenterRepo->getCostCenterByCodeORName($record['cost_center_id']);
                $product['costCenter'] = $costCenter->id;
                $product['costCenterName'] = $costCenter->name;
            }

           $result[] = $product;
        }
     
        return ['data' => $result, 'clear_old_records' => $request->get('clear_old_records') == "on" ? true : false];
    }

}