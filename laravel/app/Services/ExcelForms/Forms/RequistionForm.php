<?php

namespace App\Services\ExcelForms\Forms;

use App\Repositories\ProductRepository;
use App\Repositories\StockTransactionRepository;
use App\Requests\Inventory\GetProductBalanceRequest;
use App\Services\ExcelForms\EntityFormInterface;
use App\Services\ExcelForms\ProcessableFormInterface;
use Illuminate\Support\Facades\Validator;
use App\Repositories\UnitFactorRepository;
use App\Repositories\UnitTemplateRepository;
use App\Repositories\StoreRepository;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Repositories\CostCenterRepository;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Common\Utils\TrackStockTypesUtil;

class RequistionForm implements EntityFormInterface, ProcessableFormInterface{

    use FormTrait,ProcessProductTrait;

    public function getEntityName() : string{
        return "requistions";
    }

    public function getFields() : array{
        $commonFields = [
            'name' => ['type' => 'text', 'label' => __t('Item Code / Name'), 'size' => 65],
            'quantity' => ['type' => 'number', 'label' => __t('Quantity'), 'size' => 15, 'options' => ['step' => '0.*********']]
        ];

        
        if(Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION)){
            $commonFields['cost_center_id'] = ['type' => 'text', 'label' => __t('Cost Center Code / Name'), 'size' => 65];
        }

        if(Plugins::PluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN)){
            $commonFields['tracking_data'] = ['type' => 'text', 'label' => __t('Tracking Data'), 'size' => 80];
        }

        return $commonFields;
    }

    public function getValidations() : array{
        /**
         * Custom validation rule 
         */
        Validator::extend('product_exists_by_name_or_code', function($attribute, $value, $parameters)
        {
            $productRepo = resolve(ProductRepository::class);
            $result = $productRepo->productExistsByNameOrCode($value);
            return $result;
        });

        Validator::extend('cost_center_code_or_name_exists', function($attribute, $value, $parameters)
        {
            $costCenterRepo = resolve(CostCenterRepository::class);
            return $costCenterRepo->doesCostCenterCodeOrNameExist($value);
        });

        return [
            "*.name"      => "required|string|product_exists_by_name_or_code:currentSite.products",
            "*.cost_center_id"      => "required|string|cost_center_code_or_name_exists:currentSite.cost_centers",
            "*.quantity"  => "required|nullable|numeric|min:0"
        ];
    }

    public function getValidationsMessages() : array{
        return [
            "product_exists_by_name_or_code" => __t("The Item code not Found in the system"),
            "cost_center_code_or_name_exists" => __t("The Cost center name / code not Found in the system"),
            "*.quantity.min" => __t("You should enter valid positive numbers in the quantity")];
    }

    public function process($data, $request) : array{  
        $productRepo = resolve(ProductRepository::class);
        $stockTransactionRepo = resolve(StockTransactionRepository::class);
        $unitFactorRepo = resolve(UnitFactorRepository::class); 
        $unitTemplateRepo = resolve(UnitTemplateRepository::class); 
        $costCenterRepo = resolve(CostCenterRepository::class);
        $result = [];
        $tempProductIds = [];
        foreach($data as $record){
           $product = $productRepo->productByNameOrCode($record['name']);
           $product->unit_price = $record['price'] ?? $product->average_price;
           $product->quantity = $record['quantity'] ?? $product->quantity;
           $product = $product->toArray(); 
           $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first();
           //This flag is added to override unit template default calculation when using copy from Excel
           $product['ignoreDefault'] = true;
           if($unitTemplate) {
                $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first()->toArray();
                $product['factor'] = $unitTemplate;
                $unitTemplateFirst = ['id' => 0 , 'small_name' => ($unitTemplate['unit_small_name']? $unitTemplate['unit_small_name']: "" ),
                                    'factor_name' =>$unitTemplate['main_unit_name'],'factor' => 1];
                                    
                $product['unit_factors'] =  $unitFactorRepo->findWhere(['unit_template_id' => $product['unit_template_id']])->toArray();
                array_unshift($product['unit_factors'], $unitTemplateFirst);
           }
           $tempProductIds[] = $product['id'];
            if (!empty($record['tracking_data']) && $product['tracking_type'] !== TrackStockTypesUtil::QUANTITY_ONLY) {
                $product['tracking_data'] = $this->getTrackingData($product['tracking_type'], $record['tracking_data']);
            }

           if(isset($record['cost_center_id'])) {
                $costCenter = $costCenterRepo->getCostCenterByCodeORName($record['cost_center_id']);
                $product['costCenter'] = $costCenter->id;
                $product['costCenterName'] = $costCenter->name;
            }


            $result[] = $product;
        }


        $productsStock = $stockTransactionRepo->getBalanceByProductIds($tempProductIds);

         $result = array_map(function($product) use($productsStock) {
             $tempStoreBalance = $productsStock->where('product_id', $product['id']);
             foreach($tempStoreBalance as $productStock) {
                 $product['store_balance'][$productStock->store_id] = $productStock->quantity;
             }
             return $product;
         }, $result);
        
        return ['data' => $result, 'clear_old_records' => $request->get('clear_old_records') == "on" ? true : false];
    }

}