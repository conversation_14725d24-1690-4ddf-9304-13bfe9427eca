<?php

namespace App\Services\ExcelForms\Forms;

use App\Repositories\PriceListItemsRepository;
use App\Repositories\ProductRepository;
use App\Repositories\StockTransactionRepository;
use App\Repositories\UnitFactorRepository;
use App\Repositories\UnitTemplateRepository;
use App\Requests\Inventory\GetProductBalanceRequest;
use App\Services\ExcelForms\EntityFormInterface;
use App\Services\ExcelForms\ProcessableFormInterface;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Izam\Daftra\Common\Utils\TrackStockTypesUtil;
use App\Facades\Settings;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Facades\Plugins;
use App\Repositories\CostCenterRepository;
use Izam\Daftra\Common\Utils\SettingsUtil;

class InvoiceForm implements EntityFormInterface, ProcessableFormInterface{

    use FormTrait ,ProcessProductTrait;

    public function getEntityName() : string{
        return "invoices";
    }

    public function getFields() : array{
        $commonFields = [
            'name' => ['type' => 'text', 'label' => __t('Item Code / Name'), 'size' => 65],
            'quantity' => ['type' => 'number', 'label' => __t('Quantity'), 'size' => 15, 'options' => ['step' => '0.*********']],
            'price' => ['type' => 'number', 'label' => __t('Unit Price'), 'size' => 15, 'options' => ['step' => '0.*********']]
        ];

        if(Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::ENABLE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION)){
            $commonFields['cost_center_id'] = ['type' => 'text', 'label' => __t('Cost Center Code / Name'), 'size' => 65];
        }
        
        
        if(Plugins::PluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN) &&  !Settings::getValue(PluginUtil::InventoryPlugin, 'enable_requisitions')){
            $commonFields['tracking_data'] = ['type' => 'text', 'label' => __t('Tracking Data'), 'size' => 80];
        }
        
        return $commonFields;
        
    }

    public function getValidations() : array{
        /**
         * Custom validation rule 
         */
        Validator::extend('product_exists_by_name_or_code', function($attribute, $value, $parameters)
        {
            $productRepo = resolve(ProductRepository::class);
            $result = $productRepo->productExistsByNameOrCode($value);
            return $result;
        });

        Validator::extend('cost_center_code_or_name_exists', function($attribute, $value, $parameters)
        {
            $costCenterRepo = resolve(CostCenterRepository::class);
            return $costCenterRepo->doesCostCenterCodeOrNameExist($value);
        });

        return [
            "*.name"      => "required|string|product_exists_by_name_or_code:currentSite.products",
            "*.cost_center_id"      => "nullable|string|cost_center_code_or_name_exists:currentSite.cost_centers",
            "*.quantity"  => "required|numeric|min:0",
            "*.price"     => "nullable|numeric|min:0"
        ];
    }

    public function getValidationsMessages() : array{
        return [
            "product_exists_by_name_or_code" => __t("The Item code not Found in the system"),
            "cost_center_code_or_name_exists" => __t("The Cost center name / code not Found in the system"),
            "*.quantity.min" => __t("You should enter valid positive numbers in the quantity"),
            "*.price.min" => __t("You should enter valid positive numbers in the unit price")];
    }

    public function process($data, $request) : array{  
        $productRepo = resolve(ProductRepository::class);
        $stockTransactionRepo = resolve(StockTransactionRepository::class);
        $unitFactorRepo = resolve(UnitFactorRepository::class); 
        $unitTemplateRepo = resolve(UnitTemplateRepository::class); 
        $costCenterRepo = resolve(CostCenterRepository::class);
        $result = [];
        $clientId = $request->get('client_id');
        foreach($data as $record){
           $product = $productRepo->productByNameOrCode($record['name']);
           $product->unit_price = $this->getProductPrice($record, $product, $clientId);
           
           if($record['price']){
               $product->discount = 0;
           }
           $product->quantity = $record['quantity'] ?? $product->quantity;    
           $product = $product->toArray(); 
           //This flag is added to override unit template default calculation when using copy from Excel
           $product['ignoreDefault'] = true;
           $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first();
           if($unitTemplate) {
                $unitTemplate = $unitTemplateRepo->findWhere(['id' => $product['unit_template_id']])->first()->toArray();
                $product['factor'] = $unitTemplate;
                $unitTemplateFirst = ['id' => 0 , 'small_name' => ($unitTemplate['unit_small_name']? $unitTemplate['unit_small_name']: "" ),
                                    'factor_name' =>$unitTemplate['main_unit_name'],'factor' => 1];
                $retailUnitTemplate = $unitFactorRepo->findWhere(['id' => $product['default_retail_factor_id']])->first();
                if (isset($retailUnitTemplate) && $retailUnitTemplate->factor != 0) {
                    $product['unit_price'] = $product['unit_price'] / $retailUnitTemplate->factor;
                }
                                    
                $product['unit_factors'] =  $unitFactorRepo->findWhere(['unit_template_id' => $product['unit_template_id']])->toArray();
                array_unshift($product['unit_factors'], $unitTemplateFirst);
           }

           $balanceRequest = new GetProductBalanceRequest($product['id']);
           $balanceRequest->setStoreId($product['raw_store_id']);
           $product['store_balance'][$product['raw_store_id']] = $stockTransactionRepo->getBalance($balanceRequest);
           if(isset($product['custom_data']) && !empty($product['custom_data'])){
                foreach($product['custom_data'] as $fieldKey => $fieldVal){
                    if(!in_array($fieldKey, ['id','product_id','created','modified','staff_id'])){
                      $product[$fieldKey] = $fieldVal;
                    }
                }
           }
           $product['prices'] = $this->getPrices($product['id']);
            if (!empty($record['tracking_data']) && $product['tracking_type'] !== TrackStockTypesUtil::QUANTITY_ONLY) {
                $product['tracking_data'] = $this->getTrackingData($product['tracking_type'], $record['tracking_data']);
            }

            if(isset($record['cost_center_id'])) {
               $costCenter = $costCenterRepo->getCostCenterByCodeORName($record['cost_center_id']);
               $product['costCenter'] = $costCenter->id;
               $product['costCenterName'] = $costCenter->name;
            }

           $result[] = $product;  
        }
        return ['data' => $result, 'clear_old_records' => $request->get('clear_old_records') == "on" ? true : false];
    }

    private function getProductPrice($record, $product, $clientId)
    {
        /* provided in request data */
        if (isset($record['price']) && $record['price']) {
            return $record['price'];
        }

        if (!$clientId) {
            return $product->unit_price;    
        }

        $clientPrice = $this->getPriceFromClientGroupPrice($product->id, $clientId);
        
        if (!$clientPrice) {
            return $product->unit_price;
        }

        return $clientPrice;
    }
    
    private function getPriceFromClientGroupPrice($productId, $clientId)
    {
        /** @var PriceListItemsRepository $priceListItemsRepository */
        $priceListItemsRepository = resolve(PriceListItemsRepository::class);
        
        $productPrice = $priceListItemsRepository->model
            ->join('group_prices', 'group_prices.id', '=', 'product_prices.group_price_id')
            ->join('clients', function ($join) use ($clientId) {
                $join->on('clients.group_price_id', '=', 'group_prices.id')
                    ->where('clients.id', '=', $clientId)
                    ->where('group_prices.branch_id', '=', getCurrentBranchID());
            })
            ->where(['product_id' => $productId])
            ->first();
        
        if ($productPrice) {
            return $productPrice->price;
        }

        return null;
    }

    private function getPrices($productId)
    {
        /** @var PriceListItemsRepository $priceListItemsRepository */
        $priceListItemsRepository = resolve(PriceListItemsRepository::class);
        
        return $priceListItemsRepository->model
            ->where(['product_id' => $productId])
            ->get()->toArray();
    }
}
