<?php

namespace App\Services;

use App\Facades\Staff;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\SelectCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\MachineMappingRepository;
use App\Repositories\MachineRepository;
use App\Repositories\StaffRepository;
use App\Repositories\EntityRepository;
use App\Helpers\FilterOperations;
use App\Repositories\Criteria\EqualCriteria;
use App\Utils\AttendanceMachineMappingUtil;
use Closure;


/**
 * Class MachineMappingService
 * @package App\Services
 */
class MachineMappingService extends BaseService
{
    /**
     * @var array filters
     */
    var $filters = [];

    /**
     * @var MachineMappingRepository
     */
    var $repo;

    /**
     * @var string show route name
     */
    protected $showRouteName = null;

    /**
     * @var
     */
    public $machine_id;

    /**
     * @var StaffRepository
     */
    private $staffRepository;

    /**
     * @var MachineRepository
     */
    private $machineRepo;

    /**
     * MachineService constructor.
     * @param MachineMappingRepository $repo
     * @param StaffRepository $staffRepository
     * @param MachineRepository $machineRepo
     * @param EntityRepository|null $entityRepo
     */
    public function __construct(MachineMappingRepository $repo, MachineRepository $machineRepo, StaffRepository $staffRepository, EntityRepository $entityRepo = null)
    {
        $this->staffRepository = $staffRepository;
        $this->machineRepo = $machineRepo;
        parent::__construct($repo, $entityRepo);
    }

    /**
     * @return array
     */
    public function getFormRelatedData()
    {
        $relatedData = parent::getFormRelatedData();
        $staffs  = $this->staffRepository->pushCriteria(new CustomFind([['field' => 'active', 'operation' => '=', 'value' => 1]],['field' => 'name', 'direction' => 'ASC']))->all();
        $relatedData['staffs'] = [];
        foreach ($staffs as $staff) {
            $relatedData['staffs'][$staff->id] = $staff->id . '- ' . $staff->name . ' ' . $staff->middle_name . ' ' . $staff->last_name;
        }
        return $relatedData;
    }

    /**
     * @return array
     */
    public function getSortFields()
    {
        return [
            'fields' => [
                'staff_id' => [
                    'simple' => false, /* displays the filter outside the toggle */
                    'hidden' => true,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('Filter by').' '.__t('Employee'),
                        'id' => 'typeSelect'
                    ],
                    'div' => 'col-md-4',
                    'options' => null,
                    'type' => 'select',
                    'field' => 'staff_id',
                    'direction' => 'DESC',
                    'title' => __t('Employee Name'),
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'staff_id',
                    ]
                ],
                'staffs.name' => [
                    'title' => __t('Employee Name'),
                    'direction' => 'ASC',
                    'field' => 'staffs.name',
                    'relation'=> [
                        'join' => ['fromAlias' => 'staffs', 'join' => 'staffs.id', 'alias' => 'machine_employee_mappings.staff_id'],
                        'tableName' => 'staffs',
                        'tableField' => 'name'
                    ]
                ],
            ],
            'active' => [
                'field' => 'staff_id',
                'direction' => 'DESC',
            ]
        ];
    }

    /**
     * @return array
     */
    function getFilters()
    {
        $staffList = [];
        if (isset($this->parameters['staff_id']) && !empty($this->parameters['staff_id'])) {
            $staff = $this->staffRepository->pushCriteria(new CustomFind([['field' => 'active', 'operation' => '=', 'value' => 1]], ['field' => 'name', 'direction' => 'ASC']))->find($this->parameters['staff_id']);
            if ($staff) {
                $staffList = [
                    Staff::getStaffOptionFormated($staff)
                ];
            }
        }
        $this->filters = [
            'staff_id' => [
                'simple' => true, /* displays the filter outside the toggle */
                'label' => false,
                'before' => '<div class="form-group-icon  form-group">',
                'after' => '<i class="input-icon fal fa-search"></i></div>',
                'attributes' => [
                    'placeholder' => sprintf(__t('Search by %s, %s or %s'),__t('Employee Name'), __t('ID'), __t('Email')),
                    'id' => 'staffSelect'
                ],
                'div' => 'col-md-6',
                'options' => $staffList,
                'type' => 'selectStaff',
                'filter_options' => [
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'staff_id',
                ]
            ]
        ];

        if(!empty($this->filters))
        {
            return $this->filters;
        }else{
            return  $this->filters = [];
        }
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record) : array
    {
        $relations = parent::getActivityLogRelations($record);



        return $relations;
    }

    /**
     * @return array
     */
    protected function getListingConditions()
    {
        $listingConditions = parent::getListingConditions();
        $listingConditions[] = [
            "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
            "field" => "machine_id",
            "value" => $this->machine_id
        ];
        return $listingConditions;
    }

    /**
     * @param \App\Requests\DefaultRequest|\Illuminate\Http\Request $request
     * @param array $excludedFields
     * @param Closure|null $callback
     * @return \Illuminate\Database\Eloquent\Model|mixed|null
     */
    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $data = $request->except(['_token']);
        $cn = $this->repo->getDBConnection('currentSite');

        $result = null;
        foreach ($data['system_emp_id'] as $k => $v){
            $formattedData = [
                'machine_id' => $data['machine_id'],
                'staff_id' => $data['system_emp_id'][$k],
                'staff_machine_id' => $data['machine_emp_id'][$k]
            ];
            $result = $this->repo->add($formattedData);
        }
        return $result;
    }

    /**
     * @param int $id
     * @param \App\Requests\DefaultRequest|\Illuminate\Http\Request $request
     * @param array $excludedFields
     * @param Closure|null $callback
     * @return \Illuminate\Database\Eloquent\Model|mixed|void|null
     */
    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $data = $request->except(['_token']);
        $cn = $this->repo->getDBConnection('currentSite');

        $result = null;
        $formattedData = [
            'machine_id' => $data['machine_id'],
            'staff_id' => $data['system_emp_id'],
            'staff_machine_id' => $data['machine_emp_id']
        ];
        $result = $this->repo->update($id,$formattedData);
        return $result;
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getMachineName($id)
    {
        return $this->machineRepo->find($id)->name;
    }

    public function getUnmappedUsersCount($machine_id = null)
    {
        $query = $this->repo
                    ->pushCriteria(
                        new SelectCriteria(
                            'staff_id'
                        )
                    )
                    ->pushCriteria(
                        new InArrayCriteria('staff_id', [AttendanceMachineMappingUtil::UNKNOWN])
                    );

        if($machine_id){
            $query = $query->pushCriteria(
                new EqualCriteria('machine_id', $machine_id)
            );
        }

        return $query->count();
    }

    public function getUnmappedUsersMessage($machine_id = null)
    {
        $message = false;
        if($unmappedCount = $this->getUnmappedUsersCount($machine_id)){

            $mapUrl = '';
            if($machine_id) {
                $mapRoute = route('owner.machines.unmapped_employee.create', ['machine' => $machine_id]);
                $mapUrl = sprintf("<a href='%s' target='_blank'>%s</a>", $mapRoute, sprintf(__t('Map Now')));
            }
            $message = sprintf('(%d) %s %s', 
                                $unmappedCount, 
                                __t('Machine users not mapped to system employees'),
                                $mapUrl);
        }
        return $message;
    }
}
