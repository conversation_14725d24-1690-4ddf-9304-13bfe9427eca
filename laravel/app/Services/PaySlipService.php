<?php

namespace App\Services;

use App\Events\PayslipDeletedEvent;
use App\Events\PayslipsApprovedEvent;
use App\Events\PayslipsUnApprovedEvent;
use App\Exceptions\ClosedPeriodException;
use App\Exceptions\EntityCouldntBeCreatedException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Import\ImportErrorException;
use App\Exceptions\LoanSalaryComponentNotFound;
use App\Exceptions\NoPayslipSelectedException;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Exceptions\PageAccessNotAllowedException;
use App\Exceptions\Payroll\CanNotApproveNegativePayslip;
use App\Exceptions\Payroll\SalaryComponentRepeated;
use App\Exceptions\PayslipCannotBeDeleted;
use App\Exceptions\PayslipCannotBeEditedException;
use App\Exceptions\PayslipOverlapPayrunException;
use App\Facades\Branch;
use App\Facades\Formatter;
use App\Facades\Permissions;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Facades\Staff;
use App\Helpers\FilterOperations;
use App\Models\Currency;
use App\Models\EntityField;
use App\Models\Payslip;
use App\Modules\LocalEntity\Dto\RecoredInfo;
use App\Modules\LocalEntity\Events\RecoredApprovedEvent;
use App\Modules\LocalEntity\Queue\Events\PayslipApprovedEvent;
use App\Repositories\ClosedPeriodRepository;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\IsNullCriteria;
use App\Repositories\Criteria\WhereLikeCriteria;
use App\Repositories\CurrencyRepository;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\EntityRepository;
use App\Repositories\InstallmentRepository;
use App\Repositories\LoanRepository;
use App\Repositories\PayRunRepository;
use App\Repositories\PaySlipRepository;
use App\Repositories\SalaryComponentRepository;
use App\Repositories\StaffRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Requests\DefaultRequest;
use App\Requests\Loans\PayInstallmentRequest;
use App\Requests\Payroll\PayslipValidatorRequest;
use App\Services\Commissions\CommissionSheetService;
use App\Utils\ActivityLogActionsUtil;
use App\Utils\Commission\CommissionSheetStatusUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PayrunCriteriaUtil;
use App\Utils\PayrunStatusUtil;
use App\Utils\PaySlipStatusUtil;
use App\Utils\PermissionUtil;
use App\Utils\PluginUtil;
use App\Utils\SalaryComponentTypeUtil;
use App\Utils\SalaryComponentValueTypeUtil;
use App\Validators\PayrollValidator\PayslipValidatorService;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Entity\Repository\EntityViewsLogsRepository;
use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;
use Izam\Payslip\Repositories\PayslipComponentRepository;
use Izam\Template\Utils\TemplateTypeUtil;

/**
 * PaySlipService Class designation service deals with pay slip repository @see PaySlipRepository
 * @package App\Services
 * <AUTHOR> Faesal <<EMAIL>>
 */
class PaySlipService extends BaseService
{
    /**
     * @var string $showRouteName pay run show route name
     */
    protected $showRouteName = 'owner.payslips.show';

    /**
     * @var PayRunRepository
     */
    private $payrunRepository;

    /**
     * @var StaffRepository
     */
    private $staffRepository;

    /**
     * @var DepartmentRepository
     */
    private $departmentRepo;

    /**
     * @var DesignationRepository
     */
    private $designationRepo;

    /**
     * @var SalaryComponentRepository
     */
    private $salaryComponentRepository;

    /**
     * @var LoanRepository
     */
    private $loanRepo;

    /**
     * @var InstallmentRepository
     */
    private $installmentRepo;

    /**
     * @var InstallmentService
     */
    private $installmentService;

    /**
     * @var CommissionSheetService
     */
    private $commissionSheetService;

    /**
     * @var CurrencyRepository
     */
    private $currencyRepository;

    /**
     * @var PayslipComponentRepository
     */
    protected $payslipComponentRepository;


    /**
     * {{@inheritDoc}}
     * PaySlipService constructor.
     * @param PaySlipRepository $repo pay run repository
     */
    public function __construct(
        protected StaffService $staffService,
        PaySlipRepository $repo,
        EntityRepository $entityRepo,
        StaffRepository $staffRepository,
        DepartmentRepository $departmentRepository,
        DesignationRepository $designationRepository,
        PayRunRepository $payRunRepository,
        LoanRepository $loanRepository,
        InstallmentRepository $installmentRepository,
        InstallmentService $installmentService,
        SalaryComponentRepository $salaryComponentRepository,
        CommissionSheetService $commissionSheetService,
        CurrencyRepository $currencyRepository,
        PayslipComponentRepository $payslipComponentRepository,
    ) {
        $this->payrunRepository = $payRunRepository;
        $this->staffRepository = $staffRepository;
        $this->departmentRepo = $departmentRepository;
        $this->designationRepo = $designationRepository;
        $this->salaryComponentRepository = $salaryComponentRepository;
        $this->loanRepo = $loanRepository;
        $this->installmentRepo = $installmentRepository;
        $this->installmentService = $installmentService;
        parent::__construct($repo, $entityRepo);
        $this->activityLogService = $this->getActivityLogService();
        $this->activityLogService->appendKeyToExceptKeys('payment_id');
        $this->commissionSheetService = $commissionSheetService;
        $this->currencyRepository = $currencyRepository;
        $this->payslipComponentRepository = $payslipComponentRepository;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record) : array
    {
        $relations = parent::getActivityLogRelations($record);
        if ($record->payrun_id) {
            $relations[] = new ActivityLogRelationRequest($record->payrun_id, EntityKeyTypesUtil::PAY_RUN);
        }
        if ($record->staff_id) {
            $relations[] = new ActivityLogRelationRequest($record->staff_id, EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        }
        return $relations;
    }

    public function getFormData($id = false): array
    {
        $data = parent::getFormData($id);
        if ($id) {
            $payslip = $data['form_record'];
            $staff_id = $data['form_record']->staff->id;
            $selectedComponents = $this->repo->components($payslip);

            /** Handle Commission Sheets and Loans **/
            if (Plugins::pluginInstalled(PluginUtil::COMMISSION_PLUGIN)) {
                $commissions_data = $this->getCommissionsForEdit($payslip);
                $data['related_form_data']['selectedSheetsIDs'] = $commissions_data['ids'];
                $data['related_form_data']['commissionSheetsOptions'] = $commissions_data['options'];
            }

            $data['related_form_data']['loans'] = $this->getLoansForEditView($payslip);
        } else {
            $selectedComponents = [];
            $staff_id = old('employee');
        }

        if ($staff_id) {
            $staff = $this->staffRepository->find($staff_id);
            if ($staff) {
                $data['staffOptions'] = [Staff::getStaffOptionFormated($staff) ];
            }
        }

        $earningComps = [];
        $earningIDs = [];
        $dedComps = [];
        $dedIDs = [];
        $basicComp = null;
        foreach ($selectedComponents as $salaryComponent) {
            if ($salaryComponent->is_basic) {
                $basicComp = $salaryComponent;
                continue;
            }
            if ($salaryComponent->type === SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING) {
                $earningComps[$salaryComponent->id] = $salaryComponent;
                $earningIDs[] = $salaryComponent->id;
            } else {
                $dedComps[$salaryComponent->id] = $salaryComponent;
                $dedIDs[] = $salaryComponent->id;
            }
        }

        if (!$basicComp)
            $basicComp = $this->salaryComponentRepository->getBasicComp();

        $earOptions = [];
        $dedOptions = [];
        $earningSalaryComponents = $this->salaryComponentRepository
            ->getEarningComponentsForForm();
        foreach ($earningSalaryComponents as $earningSalaryComponent) {
            if (in_array($earningSalaryComponent->id, $earningIDs)) {
                $check = false;
                $amount = $earningComps[$earningSalaryComponent->id]->pivot->amount;
                $formula = $earningComps[$earningSalaryComponent->id]->pivot->formula;
            } else {
                $check = true;
                $amount = $earningSalaryComponent->amount;
                $formula = $earningSalaryComponent->formula;
            }

            $earOptions[] = [
                'text' => $earningSalaryComponent->name,
                'value' => $earningSalaryComponent->id,
                'data' => [
                    'data-check' => $check,
                    'data-amount' => $amount,
                    'data-formula' => $formula,
                    'data-condition' => $earningSalaryComponent->condition,
                    'data-auto' => ($earningSalaryComponent->payroll_component_id) ?? 0
                ]
            ];
        }

        $dedSalaryComponents = $this->salaryComponentRepository->getDeductionComponentsForForm();
        foreach ($dedSalaryComponents as $dedSalaryComponent) {
            if (in_array($dedSalaryComponent->id, $dedIDs)) {
                $check = false;
                $amount = $dedComps[$dedSalaryComponent->id]->pivot->amount;
                $formula = $dedComps[$dedSalaryComponent->id]->pivot->formula;
            } else {
                $check = true;
                $amount = $dedSalaryComponent->amount;
                $formula = $dedSalaryComponent->formula;
            }

            $dedOptions[] = [
                'text' => $dedSalaryComponent->name,
                'value' => $dedSalaryComponent->id,
                'data' => [
                    'data-check' => $check,
                    'data-amount' => $amount,
                    'data-formula' => $formula,
                    'data-condition' => $dedSalaryComponent->condition ,
                    'data-auto' => ($dedSalaryComponent->payroll_component_id) ?? 0
                ]
            ];
        }

        $types = SalaryComponentTypeUtil::getSalaryComponentsTypesforView();
        $data['related_form_data']['basicComp'] = $basicComp;
        $data['salaryComponents'] = ['earning' => $earningComps, 'deduction' => $dedComps];
        $data['related_form_data']['earningOptions'] = $earOptions;
        $data['related_form_data']['deductionOptions'] = $dedOptions;
        $data['related_form_data']['comp_types'] = $types;
        $data['related_form_data']['comp_acc_options'] = [];
        $data['related_form_data']['currencies'] = $this->currencyRepository->list(['code', 'code'], true);

        return $data;
    }

    /**
     * @param int $payrun_id
     * @return array
     * @throws EntityNotFoundException
     */
    public function getPayrunFormData(int $payrun_id): array
    {
        $payrun = $this->payrunRepository->find($payrun_id);
        if (!$payrun)
            throw new EntityNotFoundException('Payrun');

        return [
            'id' => $payrun->id,
            'posting_date' => $payrun->posting_date,
            'start_date' => $payrun->start_date,
            'end_date' => $payrun->end_date,
            'currency' => $payrun->currency_code
        ];
    }

    public function getCloneFormData(int $payslip_id)
    {
        $data = $this->getFormData();
        $payslip = $this->repo->find($payslip_id);
        if (!$payslip)
            throw new EntityNotFoundException($this->mainEntity->label);

        $data['related_form_data']['clone_data'] = [
            'employee' => null,
            'posting_date' => $payslip->posting_date,
            'start_date' => $payslip->start_date,
            'end_date' => $payslip->end_date,
            'currency' => $payslip->payrun->currency_code
        ];

        $staff = $this->staffRepository->find($payslip->staff_id);
        if ($staff && $staff->active) {
            $data['staffOptions'] = [Staff::getStaffOptionFormated($staff)];
        }

        $selectedComponents = $this->repo->components($payslip);
        $earningComps = [];
        $earningIDs = [];
        $dedComps = [];
        $dedIDs = [];
        $basicComp = null;
        foreach ($selectedComponents as $salaryComponent) {
            if ($salaryComponent->is_basic) {
                $basicComp = $salaryComponent;
                continue;
            }
            if ($salaryComponent->type === SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING) {
                $earningComps[$salaryComponent->id] = $salaryComponent;
                $earningIDs[] = $salaryComponent->id;
            } else {
                $dedComps[$salaryComponent->id] = $salaryComponent;
                $dedIDs[] = $salaryComponent->id;
            }
        }

        if (!$basicComp)
            $basicComp = $this->salaryComponentRepository->getBasicComp();

        $earOptions = [];
        $dedOptions = [];
        $earningSalaryComponents = $this->salaryComponentRepository
            ->getEarningComponentsForForm();
        foreach ($earningSalaryComponents as $earningSalaryComponent) {
            if (in_array($earningSalaryComponent->id, $earningIDs)) {
                $check = false;
                $amount = $earningComps[$earningSalaryComponent->id]->pivot->amount;
                $formula = $earningComps[$earningSalaryComponent->id]->pivot->formula;
            } else {
                $check = true;
                $amount = $earningSalaryComponent->amount;
                $formula = $earningSalaryComponent->formula;
            }

            $earOptions[] = [
                'text' => $earningSalaryComponent->name,
                'value' => $earningSalaryComponent->id,
                'data' => [
                    'data-check' => $check,
                    'data-amount' => $amount,
                    'data-formula' => $formula,
                    'data-condition' => $earningSalaryComponent->condition,
                    'data-auto' => ($earningSalaryComponent->payroll_component_id) ?? 0

                ]
            ];
        }

        $dedSalaryComponents = $this->salaryComponentRepository->getDeductionComponentsForForm();
        foreach ($dedSalaryComponents as $dedSalaryComponent) {
            if (in_array($dedSalaryComponent->id, $dedIDs)) {
                $check = false;
                $amount = $dedComps[$dedSalaryComponent->id]->pivot->amount;
                $formula = $dedComps[$dedSalaryComponent->id]->pivot->formula;
            } else {
                $check = true;
                $amount = $dedSalaryComponent->amount;
                $formula = $dedSalaryComponent->formula;
            }

            $dedOptions[] = [
                'text' => $dedSalaryComponent->name,
                'value' => $dedSalaryComponent->id,
                'data' => [
                    'data-check' => $check,
                    'data-amount' => $amount,
                    'data-formula' => $formula,
                    'data-condition' => $dedSalaryComponent->condition,
                    'data-auto' => ($dedSalaryComponent->payroll_component_id) ?? 0
                ]
            ];
        }

        $data['related_form_data']['basicComp'] = $basicComp;
        $data['salaryComponents'] = ['earning' => $earningComps, 'deduction' => $dedComps];
        $data['related_form_data']['earningOptions'] = $earOptions;
        $data['related_form_data']['deductionOptions'] = $dedOptions;

        return $data;
    }

    private function getApprovedCommissionSheets($staffId, $currencyCode)
    {
        $approvedCommissions = $this->commissionSheetService->getApprovedCommissionSheets($staffId, $currencyCode);
        return $approvedCommissions;
    }

    /**
     * @param Payslip $payslip
     * @return array
     */
    public function getLoansForEditView(Payslip $payslip): array
    {
        $payslipCurrency = $this->repo->getCurrency($payslip->id);
        $loans = $this->loanRepo->getLoansWithInstallments($payslip->staff_id, $payslipCurrency,$payslip->id);

        return $this->formatLoans($loans,$this->installmentRepo->getSelectedInstallmentsIDsForPayslip($payslip->id));
    }

    public function getLoansForAddManual($employee_id, $currency_code)
    {
        $loans = $this->loanRepo->getLoansWithInstallments($employee_id, $currency_code);
        return $this->formatLoans($loans, []);
    }

    /**
     * @param Payslip $payslip
     * @return array
     */
    public function getCommissionsForEdit(Payslip $payslip): array
    {
        $commissionSheetIDs = [];
        $commissionSheetsOptions = [];

        $commissionSheets = $this->getApprovedCommissionSheets($payslip->staff_id, $payslip->payrun->currency_code);
        if (!$payslip->commissionSheets->isEmpty()) {
            $commissionSheets = $commissionSheets->merge($payslip->commissionSheets);
            $commissionSheetIDs = $commissionSheets->pluck('id')->toArray();
        }

        if ($commissionSheets) {
            foreach ($commissionSheets as $key => $commissionSheet) {
                $commissionSheetsOptions[] = [
                    'text' => sprintf(__t('Commission Sheet #%s (%s) %s'),
                        $commissionSheet->id,
                        formatForView($commissionSheet->total_commission, EntityFieldUtil::ENTITY_FIELD_TYPE_PRICE, null, ['currency_code' => $commissionSheet->currency]),
                        formatForView($commissionSheet->to_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)),
                    'value' => $commissionSheet->id,
                    'data' => ['commission' => $commissionSheet->total_commission]
                ];
            }
        }

        return ['ids' => $commissionSheetIDs, 'options' => $commissionSheetsOptions];
    }

    public function getCommissionsForAddManual($employee_id, $currency_code): ?array
    {
        if (Plugins::pluginActive(PluginUtil::COMMISSION_PLUGIN)) {
            $commissionSheetsOptions = [];
            $commissionSheets = $this->getApprovedCommissionSheets($employee_id, $currency_code);
            if ($commissionSheets) {
                foreach ($commissionSheets as $key => $commissionSheet) {
                    $commissionSheetsOptions[] = [
                        'text' => sprintf(__t('Commission Sheet #%s (%s) %s'),
                            $commissionSheet->id,
                            formatForView($commissionSheet->total_commission, EntityFieldUtil::ENTITY_FIELD_TYPE_PRICE, null, ['currency_code' => $commissionSheet->currency]),
                            formatForView($commissionSheet->to_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)),
                        'value' => $commissionSheet->id,
                        'data' => ['commission' => $commissionSheet->total_commission]
                    ];
                }
            }

            return $commissionSheetsOptions;
        } else
            return null;
    }

    /**
     * @param array $loans
     * @param array $selectedInstallments
     * @return array
     */
    private function formatLoans(array &$loans, array $selectedInstallments = []): array
    {
        foreach ($loans as $key => $loan) {
            $installments = [];
            foreach ($loan['installments'] as $index => $installment) {
                $installments[] = [
                    'text' => "#" . $installment['id'] . " - " . formatForView($installment['due_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) . " (" . $installment['amount'] . ")",
                    'value' => implode(",", [$installment['amount'], $installment['id']]),
                    'data' => [
                        'data-amount' => $installment['amount']
                    ]
                ];
                if (in_array($installment['id'], $selectedInstallments)) {
                    $installments[$index]['data']['selected'] = true;
                }
            }
            $loans[$key]['installments'] = $installments;
        }
        return $loans;
    }

    /**
     * {@inheritDoc}
     */
    public function find($id)
    {
        $record = parent::find($id);

        if ($this->isUserCanViewThisPayslip($record)) {
            return $record;
        }
        throw new \Exception('Not allowed to view this payslip');
    }

    /**
     * @param $record
     * @return bool
     */
    public function isUserCanViewThisPayslip($record)
    {
        $authUserID = getAuthOwner('staff_id');
        if (Permissions::isOwnerOrAdmin($authUserID))  {
            return true;
        }

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $recordStaffData = $record->staff;
            $accessibleBranchesIds = Branch::getStaffBranchesIds();
            if(!in_array($recordStaffData->branch_id, $accessibleBranchesIds)){
                return false;
            }
        }


        if(Permissions::checkSpecificPermissionForStaff(PermissionUtil::VIEW_PAY_RUN, $authUserID)){
            return true;
        }

        // if he has only a permission VIEW_HIS_OWN_PAYSLIPS and the payslip is his payslip or is not generated will return
        if (
            Permissions::checkSpecificPermissionForStaff(PermissionUtil::VIEW_HIS_OWN_PAYSLIPS, $authUserID) &&
            $record->staff_id == $authUserID &&
            $record->status != PaySlipStatusUtil::GENERATED
        ) {
            return true;
        }

        $authDeptId = $this->staffService->getAuthDepartmentId();
        $hasDeptViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_PAYSLIPS);
        $recordDeptId = $record->staffInfo?->department_id ?? null;
        $sameDept = isset($recordDeptId) && isset($authDeptId) && $authDeptId == $recordDeptId;
        if($sameDept && $hasDeptViewPermission){
            return true;
        }
        return false;
    }
    /**
     * {@inheritDoc}
     */
    function getSortFields()
    {
        return [
            'fields' => [
                'name' => [
                    'title' => __t('Name'),
                    'direction' => 'DESC',
                    'field'=>'name',
                    'relation'=> [
                        'join' => ['fromAlias' => 'staffs', 'join' => 'staffs.id', 'alias' => 'payslips.staff_id'],
                        'tableName' => 'staffs',
                        'tableField' => 'name'
                    ]
                ],
                'status' => [
                    'title' => __t('Status'),
                    'field' => 'status',
                    'direction' => 'ASC'
                ],
                'created' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'created',
                    'direction' => 'DESC'
                ],
                'staffs.code' => [
                    'title' => __t('Code'),
                    'direction' => 'DESC',
                    'field'=>'staffs.code',
                    'relation'=> [
                        'join' => ['fromAlias' => 'staffs', 'join' => 'staffs.id', 'alias' => 'payslips.staff_id'],
                        'tableName' => 'staffs',
                        'tableField' => 'code'
                    ]
                ],
            ],
            'active' => [
                'field' => 'created',
                'direction' => 'DESC',
            ]
        ];
    }

    /**
     * {@inheritDoc}
     */
    function getFilters()
    {
        $staffList = [];
        $departments = [];
        if (isset($this->parameters['staff']) && !empty($this->parameters['staff'])) {
            $staffs = $this->staffRepository->findWhereIn('id', $this->parameters['staff']);
            foreach ($staffs as $staff)
                $staffList[] = Staff::getStaffOptionFormated($staff);
        }
        $payrunList = [];
        if (isset($this->parameters['payrun']) && !empty($this->parameters['payrun'])) {
            $payruns = $this->payrunRepository->filterList('id', $this->parameters['payrun']);
            foreach ($payruns as $payrun)
                $payrunList += [$payrun->id => "#{$payrun->id} {$payrun->name}"];
        }
        $departmentList = $this->repo->departmentList();

        $designationList = $this->repo->designationList();

        $currenciesList = $this->repo->currencyList();


        if (isset($this->parameters['start_from_date']) && !empty($this->parameters['end_to_date'])) {
            $fromDate = $this->parameters['start_from_date'];
            $toDate = $this->parameters['end_to_date'];
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['start_from_date'] = $toDate;
                $this->parameters['end_to_date'] = $fromDate;
            }
        }
        if (isset($this->parameters['posting_from_date']) && !empty($this->parameters['posting_to_date'])) {
            $fromDate = $this->parameters['posting_from_date'];
            $toDate = $this->parameters['posting_to_date'];
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['posting_from_date'] = $toDate;
                $this->parameters['posting_to_date'] = $fromDate;
            }
        }
        if (isset($this->parameters['creation_from_date']) && !empty($this->parameters['creation_to_date'])) {
            $fromDate = $this->parameters['creation_from_date'];
            $toDate = $this->parameters['creation_to_date'];
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['creation_from_date'] = $toDate;
                $this->parameters['creation_to_date'] = $fromDate;
            }
        }
        $statuses = PaySlipStatusUtil::getAll();
        foreach ($statuses as $key=>$status)
            $statuses[$key]= __t($status);

        $filters = [
            'staff[]' => [
                'simple' => true, /* displays the filter outside the toggle */
                'label' => __t('Employee'),
                'before' => '<div class="form-group-icon form-group">',
                'after' => '<i class="input-icon fal fa-search"></i></div>',
                'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                'param_name' => 'staff',
                'attributes' => [
                    'placeholder' => __t(getEmployeesFieldPlaceholder()),
                    'id' => 'staffSelect',
                    'multiple' => true,
                    'data-staff-url' => route('owner.staff.search', ['allow_inactive' => true, 'get_branch_suspended' => 1])
                ],
                'div' => 'col-md-4',
                'options' => $staffList,
                'type' => 'selectStaff',
                'filter_options' => [
                    'operation' => FilterOperations::IN_FILTER_OPERATION,
                    'field' => 'staff_id',
                ]
            ],
	        'payrun' => [
		        'simple' => true,
		        'label' => false,
		        'inputClass' => 'custom-select form-control',
		        'div' => 'col-md form-group',
		        'after' => '<i class="input-icon fal fa-search"></i></div>',
		        'before' => '<div class="form-group-icon">',
		        'attributes' => [
			        'placeholder' => sprintf(__t('Filter by %s'), __t('Pay Run')),
			        'id' => 'payrunSelect'
		        ],
		        'type' => 'select',
		        'options' => $payrunList,
		        'filter_options' => [
			        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
			        'field' => 'payrun_id',
		        ]
	        ],
            'department' => [
                'param_name' => 'department',
                'simple'=>true,
                'label' => false,
                'after' => '</div>',
                'before' => '<div class="form-group">',
                'attributes' => [
                    'placeholder' => __t('Select') . ' ' . __t('Department'),
                    'id' => 'departmentSelect',
                ],
                'div' => 'col-md-4',
                'inputClass' => 'select-filter form-control',
                'options' => $departmentList,
                'type' => 'select',
                'filter_options' => [
                    'table' => 'staff_info',
                    'model' => 'staffInfo',
                    'key_path' => 'payslip_staff.staff_info.department_id',
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'department_id',
                ]
            ],
            'currency_code' => [
                'param_name' => 'currency_code',
                'label' => false,
                'after' => '</div>',
                'before' => '<div class="form-group">',
                'attributes' => [
                    'placeholder' => __t('Select') . ' ' . __t('Currency'),
                    'id' => 'currencySelect',
                ],
                'div' => 'col-md-4',
                'inputClass' => 'select-filter form-control',
                'options' => $currenciesList,
                'type' => 'select',
                'filter_options' => [
                    'table' => 'payruns',
                    'model' => 'Payrun',
                    'key_path' => 'payslip_payrun.currency_code',
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'currency_code',
                ]
            ],
            'start_from_date' => [
                'type' => 'date',
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' => ['placeholder' => __t('Period') . ' (' . __t('From') . ')'],
                "filter_options" => [
                    "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "start_date"
                ]
            ],
            'end_to_date' => [
                'type' => 'date',
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' =>['placeholder' => __t('Period') . ' (' . __t('To') . ')'],
                "filter_options" => [
                    "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "end_date"
                ],
            ],
            'designation' => [
                'param_name' => 'designation',
                'simple'=>false,
                'label' => false,
                'after' => '</div>',
                'before' => '<div class="form-group">',
                'attributes' => [
                    'placeholder' => __t('Select') . ' ' . __t('Designation'),
                    'id' => 'designationSelect',
                ],
                'div' => 'col-md-4',
                'inputClass' => 'select-filter form-control',
                'options' => $designationList,
                'type' => 'select',
                'filter_options' => [
                    'table' => 'staff_info',
                    'model' => 'staffInfo',
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'designation_id',
                ]
            ],
            'posting_from_date' => [
                'type' => 'date',
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' => ['placeholder' => __t('Posting Date') . ' (' . __t('From') . ')'],
                "filter_options" => [
                    "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "posting_date"
                ],
            ],
            'posting_to_date' => [
                'type' => 'date',
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' => ['placeholder' => __t('Posting Date') . ' (' . __t('To') . ')'],
                "filter_options" => [
                    "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "posting_date"
                ],
            ],
            'status' => [
                'simple' => false, /* displays the filter outside the toggle */
                'label' => false,
                'inputClass' => 'select-filter form-control',
                'div' => 'col-md-4 form-group',
                'attributes' => ['placeholder' => __t('All Statuses'), 'data-allow-clear' => 'true'],
                'name' => 'status',
                'type' => 'select',
                'options' => $statuses,
                'filter_options' => [
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'status'
                ]
            ],
            'creation_from_date' => [
                'type' => 'date',
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' => ['placeholder' => __t('Date of Creation') . ' (' . __t('From') . ')'],
                "filter_options" => [
                    "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "created"
                ],
            ],
            'creation_to_date' => [
                'type' => 'date',
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' => ['placeholder' => __t('Date of Creation') . ' (' . __t('To') . ')'],
                "filter_options" => [
                    "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "created"
                ]
            ],
            'overlap_start_date' => [
                'type' => 'date',
                'hidden' => true,
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' => ['placeholder' => __t('Overlap Start')],
                "filter_options" => [
                    "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "start_date"
                ]
            ],
            'overlap_end_date' => [
                'type' => 'date',
                'hidden' => true,
                'before' => '<div class="form-group form-group-icon">',
                'after' => '</div>',
                'label' => false,
                'div' => 'col-md-4',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'attributes' => ['placeholder' => __t('Overlap End')],
                "filter_options" => [
                    "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "end_date"
                ]
            ],
        ];
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $branches = Branch::getStaffBranchesSuspended();
            if(count($branches) > 1){
                $filters = $filters + [
                        'branches[]' => [
                            'param_name' => 'branches',
                            'simple'=>false,
                            'label' => false,
                            'after' => '</div>',
                            'before' => '<div class="form-group">',
                            'attributes' => [
                                'placeholder' => __t('Select') . ' ' . __t('Branches'),
                                'id' => 'branchSelect',
                                'multiple' => 'multiple'
                            ],
                            'div' => 'col-md-4',
                            'inputClass' => 'select-filter form-control',
                            'options' => $branches,
                            'type' => 'select',
                            'filter_options' => [
                                'table' => 'staffs',
                                'model' => 'staff',
                                'operation' => FilterOperations::IN_FILTER_OPERATION,
                                'field' => 'branch_id',
                                'key_path' => 'payslip_staff.branch_id',
                            ]
                        ],
                    ];
            }
        }


        return $filters;
    }

    private $eagerLoadedRelationsForListing = ['staffInfo'];

    function getWithRelations() : array {
        return $this->eagerLoadedRelationsForListing;
    }

    public function setWithRelations(array $with){
        $this->eagerLoadedRelationsForListing = $with;
        return $this;
    }

    public function listingApprovedForUser($userId){
        $this->setWithRelations(['staffInfo', 'payRun']);
        $conditions = parent::getListingConditions();
        $conditions[] = [
            "operation" => FilterOperations::IN_FILTER_OPERATION,
            "field" => "status",
            "value" => [PaySlipStatusUtil::APPROVED, PaySlipStatusUtil::PAID],
        ];
        $conditions[] = [
            "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
            "field" => "staff_id",
            "value" => $userId
        ];
        $sortBy = request('sort') ?? [];
        if($sortBy){
            $sortByField = array_key_first($sortBy);
            $sortBy = ['field' =>  $sortByField,'direction' => $sortBy[$sortByField]?? 'ASC'];
        }
        $data = $this->paginate(
            $conditions,
            $this->noOfRecordsWillBePaginated,
            $sortBy,
            $this->getWithRelations() ?? []
        );

        $payslipsIds = [];
        foreach($data as $payslip){
            $payslipsIds[] = $payslip->id;
        }
        $entityViewsLogsRepository = resolve(EntityViewsLogsRepository::class);
        $currentStaffIds = getAuthOwner('staff_id');
        $entityViewsLogs = $entityViewsLogsRepository->getEntityViewsLogs(EntityKeyTypesUtil::PAYSLIP, $payslipsIds, $currentStaffIds);
        foreach($data as &$payslip){
            $payslip['read_at'] = $entityViewsLogs[$payslip->id]['read_at']?? null;
            $payslip->formatted_net_pay = formatForView($payslip->net_pay,\App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_PRICE,null,array("currency_code"=>$payslip->payRun->currency_code));
        }
        return $data;
    }



    protected function getListingConditions()
    {
        $conditions = parent::getListingConditions();
        $authUser = getAuthOwner();

        $shouldShowHisOwnOnly = true;
        if (Permissions::isOwnerOrAdmin($authUser['staff_id']) || Permissions::checkSpecificPermissionForStaff(PermissionUtil::VIEW_PAY_RUN, $authUser['staff_id']))  {
            $shouldShowHisOwnOnly = false;
        }

        if(!Permissions::isOwnerOrAdmin($authUser['staff_id'])){
            $accessibleBranchListingCondition = $this->getAccessibleBranchListingCondition([
                "table" => false,
                "model" => "staff",
                "operation" => FilterOperations::IN_FILTER_OPERATION,
                "field" => "staff_id",
            ]);
            if (!empty($accessibleBranchListingCondition) && !$shouldShowHisOwnOnly){
                $conditions[] = $accessibleBranchListingCondition;
            }

            if(Permissions::checkSpecificPermissionForStaff(PermissionUtil::VIEW_PAY_RUN, $authUser['staff_id'])){
                return $conditions;
            }

            if (Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_PAYSLIPS)){
                if($this->staffService->getAuthDepartmentId()){
                    $conditions[] =[
                        'operation' => FilterOperations::RAW_OPERATION,
                        'field' => 'payslips.staff_id IN ( select staff_id from staff_info where department_id = ? )',
                        'value' => [$this->staffService->getAuthDepartmentId()],
                    ];
                    return $conditions;
                }
            }

            if ($shouldShowHisOwnOnly){
                $conditions[]=  [
                    'operation'=>FilterOperations::IN_FILTER_OPERATION,
                    'table'=>false,
                    'model'=>false,
                    'field'=>'status',
                    'value'=>[
                        0 => PaySlipStatusUtil::APPROVED,
                        1 => PaySlipStatusUtil::PAID,
                    ]
                ];
                $conditions[] =[
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'table' => false,
                    'model' => 'staff',
                    'field' => 'staff_id',
                    'value' => $authUser['staff_id']
                ];
            }
        }
        return $conditions;
    }

    public function listing(): array {
        $listingData = parent::listing();
        $listingData['authDeptId'] = $this->staffService->getAuthDepartmentId();
        return $listingData;
    }


    public function deleteMany(array $ids, string $selectType = null)
    {
        $this->checkPayslipIdsNotEmpty($ids);
        $deletedCount = 0;
        $unDeletedCount = 0;
        foreach ($ids as $key => $value) {
            try {
                $this->delete($value);
                $deletedCount++;
            } catch (PayslipCannotBeDeleted $exception) {
                $unDeletedCount++;
                continue;
            }
        }

        return ['deletedCount' => $deletedCount, 'unDeletedCount' => $unDeletedCount];
    }

    public function add($request, array $excludedFields = [], ?Closure $callback = null)
    {
        $payslipsCount = 1;
        $payrun_id = null;
        $authUser = getAuthOwner();

        if (
            $authUser['staff_id'] != 0
            && $request->get('employee') != $authUser['staff_id']
            && Plugins::pluginActive(PluginUtil::BranchesPlugin)
        ) {
            $staffService = resolve(StaffService::class);
            if(!$staffService->isAccessableStaffId($request->get('employee'),false)){
                throw ValidationException::withMessages(['employee' => sprintf(__t('You do not have access to this %s',true),__t('Record',true))]);
            }
        }

        if (isset($request['payrun_id'])) {
            $payrun = $this->payrunRepository->find($request['payrun_id']);
            if (!$payrun)
                throw new EntityNotFoundException('Payrun');

            $start_date = $payrun->start_date;
            $end_date = $payrun->end_date;
            $posting_date = $payrun->posting_date;
            $currency = $payrun->currency_code;
            $payrun_id = $payrun->id;
            $payslipsCount = $payrun->payslips_count + 1;
        } else {
            /** Format Data */
            $start_date = Formatter::formatForDB($request['start_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
            $end_date = Formatter::formatForDB($request['end_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
            $posting_date = Formatter::formatForDB($request['posting_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
            $currency = $request['currency'] ?? getCurrentSite('currency_code');
        }
        $countOverlapPayslips = $this->repo->countPayslipsInRange($start_date, $end_date, $request['employee']);
        if ($countOverlapPayslips) {
            $payslips = $this->repo->getPayslipsInRange($start_date, $end_date, $request['employee']);
            throw new PayslipOverlapPayrunException($start_date, $end_date, $request['employee'], $payslips->first());
        }

        if (!isset($request['payrun_id'])) {
            /** Check Payrun and Create if not Exists **/
            $payruns = $this->payrunRepository->getPayrunsInPeriod($start_date, $end_date, $posting_date, $currency);
            if ($payruns->isEmpty()) {
                $payrun_data = [
                    'name' => 'Payrun (' . $posting_date . ') ' . $currency,
                    'posting_date' => $posting_date,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'currency_code' => $currency,
                    'status' => PayrunStatusUtil::GENERATED,
                    'criteria' => PayrunCriteriaUtil::EMPLOYEE_SELECTION
                ];

                $payrun = $this->payrunRepository->add($payrun_data);
                if ($payrun)
                    $payrun_id = $payrun->id;
                else
                    throw new EntityCouldntBeCreatedException('Payrun');
            } else {
                $last_created_payrun = $payruns->first();
                $payrun_id = $last_created_payrun->id;
                $payslipsCount = $last_created_payrun->payslips_count + 1;
            }
        }
        $employee = $this->staffRepository->find($request['employee']);
        $payslip_data = [
            'posting_date' => $posting_date,
            'status' => PaySlipStatusUtil::GENERATED,
            'staff_id' => $request['employee'],
            'payrun_id' => $payrun_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'gross_pay' => $request['gross_pay'],
            'total_deduction' => $request['total_deduction'],
            'net_pay' => $request['net_pay'],
            'notes' => $request['notes'],
            'department_name' => $employee->department->first()->name ?? '',
            'designation_name' => $employee->designation->first()->name ?? '',
            'branch_name' => Branch::getCurrentBranch()?->name,
            'department_id' => $employee->department->first()->id ?? '',
            'designation_id' => $employee->designation->first()->id ?? '',
            'branch_id' => Branch::getCurrentBranch()?->id
        ];

        $additionalCallback = function ($payslip) use ($callback, $request, $payslipsCount) {
            if ($payslip) {
                /** Handle Payslip Components **/
                $components = [];
                foreach ($request['payslipComponent'] as $type => $component_arr) {
                    foreach ($component_arr as $payslipComponent) {
                        $components[] = [
                            'payslip_id' => $payslip->id,
                            'salary_component_id' => $payslipComponent['salary_component_id'],
                            'amount' => $payslipComponent['amount'],
                            'formula' => $payslipComponent['formula'] ?? null,
                            'order' => $payslipComponent['order'],
                            'name' => null,
                            'source_id' => null
                        ];
                    }
                }

                /** Handle Loan Components **/
                if (isset($request['loans']) && count($request['loans'])) {
                    $loanSalaryComponentID = $this->salaryComponentRepository->getLoanComponent()->id ?? null;
                    $parsedLoans = $this->parseLoans($request['loans'], $loanSalaryComponentID, $payslip->id);

                    $installmentsIDs = $parsedLoans['installmentsIDs'] ?? [];
                    if (count($installmentsIDs))
                        $this->installmentRepo->assignInstallmentsToPayslip($installmentsIDs, $payslip->id);

                    $loanComponents = array_values($parsedLoans['components']) ?? [];
                    if (count($loanComponents))
                        $components = array_merge($components, $loanComponents);
                }

                /** Handle Commission Components **/
                if (isset($request['commissionSheets']) && count($request['commissionSheets'])) {
                    $this->commissionSheetService->setPayslipId($payslip->id, $request['commissionSheets']);
                    $components = array_merge($components, $this->getCommissionComponent($payslip));
                }

                $this->repo->insertPayslipComponents($components);
                $this->payrunRepository->updatePayrunPayslipsCount($payslip->payrun_id, $payslipsCount);
            }

            if ($callback) {
                $callback($payslip);
            }
        };

        return parent::add(new DefaultRequest($payslip_data), $excludedFields, $additionalCallback);
    }

    public function addFromPayrun($request, array $excludedFields = [], ?Closure $callback = null)
    {
        if(Plugins::pluginInstalled(PluginUtil::COMMISSION_PLUGIN) && Settings::getValue(PluginUtil::COMMISSION_PLUGIN, 'commission_sheet_auto_pay')) {
            $callback = function($payslip) use($callback) {
                $approvedSheets = $this->getApprovedCommissionSheets($payslip->staff_id,$payslip->contract->currency_code);
                if($approvedSheets) {
                    $this->commissionSheetService->setPayslipId($payslip->id, $approvedSheets->pluck('id')->toArray());
                    $components =  $this->getCommissionComponent($payslip);
                    if($components) {
                        $this->repo->insertPayslipComponents($components);
                    }
                }
                $callback($payslip);
            };
        }
        return parent::add($request, $excludedFields, $callback);
    }

    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $components = array_merge(
            $request['payslipComponent']['earning'] ?? [],
            $request['payslipComponent']['deduction']??[]
        );
        if ($components) {
            $this->validateEditPayslip($id);
        }

        $payslipComponent = $request['payslipComponent'];

        foreach ($components as $key => $value){
            $components[$key] = array_merge($value, ['payslip_id' => $id]);
            if ($key === 'basic')
                $components[$key] = array_merge($components[$key], ['formula' => null]);
        }

        $oldPayslip = $this->find($id);
        $excludedFields = array_merge($excludedFields, ['_method', '_token', 'payslipComponent','loans']);
        $oldPayslipComponents = $this->repo->components($oldPayslip);
        $oldBasicComponentsAmount = $this->repo->getBasicComponentAmount($oldPayslip);

        //todo : make sure there's difference in components before update it
        $salaryComponentsIds = [];
        foreach ($components as $component) {
            if (in_array($component['salary_component_id'], $salaryComponentsIds)) {
                throw new SalaryComponentRepeated;
            } else {
                $salaryComponentsIds[] = $component['salary_component_id'];
            }
        }
        $loanSalaryComponentID = $this->salaryComponentRepository->getLoanComponent()->id ?? null;
        if (!$loanSalaryComponentID) {
            throw new LoanSalaryComponentNotFound;
        }

        foreach ($components as $key => $component) {
            $components[$key]['source_id'] = null;
            $components[$key]['name'] = null;
        }

        $loans = $request['loans'] ?? [];
        $parsedLoans = $this->parseLoans($loans,$loanSalaryComponentID,$id);
        $components = array_merge($components,$parsedLoans['components'] ?? []);

        $installmentsIDs = $parsedLoans['installmentsIDs'] ?? [];
        $commissionSheetIds = false;

        if(Plugins::pluginActive(PluginUtil::COMMISSION_PLUGIN)) {
            $commissionSheetIds = $request['commissionSheets']??[];
            unset($request['commissionSheets']);
        }
        $additionalCallback = function ($result, $oldData) use ($callback, $id, $installmentsIDs, $commissionSheetIds, $components) {

            if(Plugins::pluginActive(PluginUtil::COMMISSION_PLUGIN) && $commissionSheetIds !== false) {
                $this->commissionSheetService->setPayslipId($id, $commissionSheetIds);
            }
            $components = array_merge($components, $this->getCommissionComponent($result));
            if($components) {
                $this->repo->deletePayslipComponents($id);
                $this->repo->insertPayslipComponents(array_values($components));
            }
            $this->installmentRepo->unAssignPayslipFromInstallments($id);
            $this->installmentRepo->assignInstallmentsToPayslip($installmentsIDs, $id);
            if ($callback) {
                $callback($result, $oldData);
            }
        };

        $this->disableLogging();

        $request = $request->only(['payslipComponent','gross_pay','total_deduction','net_pay','notes']);

        $payslip = parent::update($id, $request, $excludedFields, $additionalCallback);

        $this->activityLogService = $this->getActivityLogService();
        $activityLogRequest = $this->getRecordUpdatedActivityLogData($payslip, $oldPayslip);
        $newPayslipComponents = $payslip->components->toArray();

        if (isset($payslipComponent['earning']) || isset($payslipComponent['deduction'])) {
            $components = $newPayslipComponents;
            $componentsTypes = SalaryComponentTypeUtil::getSalaryComponentsTypes();
            foreach ($componentsTypes as $componentType) {
                foreach ($components as $key => $component) {
                    if ($component['type'] == $componentType) {
                        if ($component['is_basic'] == 1) {
                            $label = "Basic Earning Amount";
                            $newBasicComponentAmount = $component['pivot']['amount'];
                            $activityLogRequest->appendNewData($label, $newBasicComponentAmount);
                            $activityLogRequest->appendOldData($label, $oldBasicComponentsAmount);
                        } else {
                            $found = false;
                            $foundKey = "";
                            foreach ($oldPayslipComponents as $oldKey => $oldPayslipComponent) {
                                if (
                                    $oldPayslipComponent['type'] == $componentType &&
                                    $oldPayslipComponent['is_basic'] == 0 && $oldPayslipComponent['id'] == $component['id']
                                ) {
                                    $found = $oldPayslipComponent;
                                    $foundKey = $oldKey;
                                    break;
                                }
                            }

                            if ($found) {
                                // exist before and need to be logged as updated
                                $label =
                                    "Updated '" . $component['name'] . "' $componentType component with ";

                                $oldFormula = \App\Helpers\Placeholder\PlaceholderHelper::preview($found['pivot']['formula']);
                                $oldAmount = $found['pivot']['amount'];

                                $newFormula = \App\Helpers\Placeholder\PlaceholderHelper::preview($component['pivot']['formula']);
                                $newAmount = $component['pivot']['amount'];

                                if($oldFormula !== $newFormula){
                                    $activityLogRequest->appendOldData($label . SalaryComponentValueTypeUtil::FORMULA_VALUE_TYPE,$oldFormula);
                                    $activityLogRequest->appendNewData($label . SalaryComponentValueTypeUtil::FORMULA_VALUE_TYPE,$newFormula);
                                }

                                if($oldAmount !== $newAmount){
                                    $activityLogRequest->appendOldData($label . SalaryComponentValueTypeUtil::AMOUNT_VALUE_TYPE,$oldAmount);
                                    $activityLogRequest->appendNewData($label . SalaryComponentValueTypeUtil::AMOUNT_VALUE_TYPE,$newAmount);
                                }

                                unset($oldPayslipComponents[$foundKey]);
                            } else {
                                // not exist before and need to be logged as added
                                if (isset($component['pivot']['amount']) && !is_null($component['pivot']['amount'])) {
                                    $label = "New '" . $component['name'] . "' $componentType component with amount" ;
                                    $newValue = $component['pivot']['amount'];
                                } else {
                                    $label = "New '" . $component['name'] . "' $componentType component with formula";
                                    $newValue = \App\Helpers\Placeholder\PlaceholderHelper::preview($component['pivot']['formula']);
                                }
                                $activityLogRequest->appendNewData($label, $newValue);
                                $activityLogRequest->appendOldData($label, '');
                            }
                        }
                    }
                }
            }
            // for deleted ones
            $setEarningsAsDeletedInActivityLogs = [];
            $setDeductionAsDeletedInActivityLogs = [];
            foreach ($oldPayslipComponents as $oldPayslipComponent) {
                if ($oldPayslipComponent['is_basic'] == 0) {
                    if ($oldPayslipComponent['type'] == SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING) {
                        $setEarningsAsDeletedInActivityLogs[] = $oldPayslipComponent['name'];
                    } else {
                        $setDeductionAsDeletedInActivityLogs[] = $oldPayslipComponent['name'];
                    }
                }
            }
            if ($setEarningsAsDeletedInActivityLogs) {
                $activityLogRequest->appendOldData('Deleted Earning Components', implode(', ', $setEarningsAsDeletedInActivityLogs));
                $activityLogRequest->appendNewData('Deleted Earning Components', '');
            }
            if ($setDeductionAsDeletedInActivityLogs) {
                $activityLogRequest->appendOldData('Deleted Deduction Components', implode(', ', $setDeductionAsDeletedInActivityLogs));
                $activityLogRequest->appendNewData('Deleted Deduction Components', '');
            }
        }
        $this->saveActivityLog($activityLogRequest);

        return $payslip;
    }

    /**
     * take array of user selected installments of loans and map them to database structure and return it with array of installments id's
     * @param array $loans
     * @param int $salaryComponentID
     * @param int $payslipID
     * @return array
     */
    public function parseLoans(array $loans, ?int $salaryComponentID, ?int $payslipID): array
    {
        $installmentsIDs = [];
        $components = [];
        if (empty($loans)) {
            return compact('components', 'installmentsIDs');
        }

        foreach ($loans as $loanID => $loan) {
            foreach ($loan as $key => $installment) {
                $explodedData = explode(',', $installment);
                $installmentsIDs [] = $explodedData[1] ?? null;
                $loans[$loanID][$key] = $explodedData[0] ?? null;
            }
            $components[] = [
                'order' => "-1",
                'salary_component_id' => $salaryComponentID,
                'amount' => array_sum($loans[$loanID]),
                'payslip_id' => $payslipID,
                'formula' => null,
                'source_id' => $loanID,
                'name' => "Loan #" . $loanID,
            ];
        }

        return compact('components', 'installmentsIDs');
    }

    /**
     * delete payslip
     * @param $id
     * @param bool $callback
     * @throws PayslipCannotBeDeleted
     * @throws EntityNotFoundException
     */
    public function delete($id, Closure $callback = null)
    {
        $paySlip = $this->find($id);

        if(!$paySlip) {
            throw new EntityNotFoundException($this->mainEntity->label);
        }
        $staffService = resolve(StaffService::class);
        if(getAuthOwner("staff_id") != 0  && !$staffService->isAccessableStaffId($paySlip->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }
        if ($paySlip->status !== PaySlipStatusUtil::GENERATED) {
            throw new PayslipCannotBeDeleted();
        }

        if(ClosedPeriodRepository::getClosedPeriodsCountInDate($paySlip->posting_date, $paySlip->branch_id))
        {
            throw new ClosedPeriodException($paySlip->posting_date);
        }
        $payrunId = $paySlip->payrun_id;

        $additionalCallback = function ($oldData) use ($callback) {
            $this->installmentRepo->unAssignPayslipFromInstallments($oldData->id);
            $this->commissionSheetService->unattachCommissionSheets($oldData->id);
            if ($callback) {
                $callback($oldData);
            }
        };
        parent::delete($id,$additionalCallback);

        $count = $this->payrunRepository->payslipsCount($payrunId);
        $request = new DefaultRequest();
        $request->payslips_count = $count;
        $this->payrunRepository->update($payrunId, $request->all());
        $payslipEvent = new PayslipDeletedEvent($id);
        event($payslipEvent);
    }

    /**
     * @param $id
     * @return bool
     * @throws EntityNotFoundException
     * @throws \App\Exceptions\UnexpectedBehavior
     * @throws CanNotApproveNegativePayslip
     * @throws \App\Exceptions\Loans\InstallmentCanNotBePaid
     */
    public function approveAll($id)
    {
        $payrun = $this->payrunRepository->find($id);
        if (!$payrun) {
            throw new EntityNotFoundException($this->mainEntity->label);
        }

        $payslips_ids = $this->repo->getGeneratedIds($id);
        if (empty($payslips_ids)) {
            return false;
        }

        $payslipValidatorRequest = new PayslipValidatorRequest($payrun->posting_date);
        $payslipValidator = new PayslipValidatorService($payslipValidatorRequest);
        $payslipValidator->setup($this->payrunRepository);
        $payslipValidator->validate();
        // $old_payslips = $this->repo->getPayslipsForPayrun($id);
        // foreach ($old_payslips as $payslip) {
        //     if (!($payslip->net_pay > 0)) {
        //         throw new CanNotApproveNegativePayslip($payslip->id);
        //     }
        // }
//        $this->repo->multipleUpdateByIds($payslips_ids,['status' => PaySlipStatusUtil::APPROVED]);
//        $new_payslips = $this->repo->getPayslipsForPayrun($id);
//        $this->logCollection(ActivityLogActionsUtil::UPDATE, $new_payslips, $old_payslips);
        $this->approve($payslips_ids);
        event(new PayslipsApprovedEvent($id));

        return true;
    }

    /**
     * @param array $payslips_ids
     * @return array|void
     * @throws \App\Exceptions\UnexpectedBehavior
     * @throws \App\Exceptions\Loans\InstallmentCanNotBePaid
     * @throws NoPayslipSelectedException
     */
    public function approve(array $payslips_ids)
    {
        $this->checkPayslipIdsNotEmpty($payslips_ids);

        $payslips = $this->repo->find($payslips_ids);
        $payslips->load('staff');

        $generatedPayslips = [];
        $approvedPayslipsCount = 0;
        foreach ($payslips as $payslip) {
            if ($payslip->status == PaySlipStatusUtil::GENERATED) {
                    $generatedPayslips[] = $payslip;
            }
            if ($payslip->status == PaySlipStatusUtil::APPROVED) {
                $approvedPayslipsCount++;
            }
        }
        $success = count($generatedPayslips) + $approvedPayslipsCount;
        $failed = count($payslips) - $success;

        if (!count($generatedPayslips)) {
            return compact('success','failed');
        }

        $payruns_ids = $payslips_ids = [];

        foreach ($generatedPayslips as $payslip) {
            if (!in_array($payslip->payrun_id, $payruns_ids)) {
                $payruns_ids[] = $payslip->payrun_id;
            }
            $payslips_ids[] = $payslip->id;
        }

        $payruns = $this->payrunRepository->find($payruns_ids);

        foreach ($payruns as $payrun) {
            $payslipValidatorRequest = new PayslipValidatorRequest($payrun->posting_date);
            $payslipValidator = new PayslipValidatorService($payslipValidatorRequest);
            $payslipValidator->setup($this->payrunRepository);
            $payslipValidator->validate();
        }

        $this->repo->setStatus($payslips_ids, PaySlipStatusUtil::APPROVED);

        if (settings::getValue(PluginUtil::HRM_PAYROLL_PLUGIN, SettingsUtil::SEND_APPROVED_PAYSLIPS_VIA_EMAIL)) {
            foreach ($payslips as $payslip) {
                dispatch_event_action(new PayslipApprovedEvent($payslip));
            }
        }

        foreach ($payslips as $payslip) {
            $payslipClone = clone $payslip;
            event(new RecoredApprovedEvent(new RecoredInfo(EntityKeyTypesUtil::PAYSLIP, toArray($payslipClone))));
            $installmentsIds = $this->installmentRepo->getInstallmentsIdsForPayslip($payslip->id);
            $payInstallmentRequest = new PayInstallmentRequest($installmentsIds,$payslip->end_date,null,null,$payslip->id);
            $this->installmentService->pay($payInstallmentRequest);
            $this->installmentRepo->payInstallmentsAssignedToPayslip($payslip->id, $payslip->end_date);
        }

        $this->logCollection(ActivityLogActionsUtil::UPDATE, $this->repo->find($payslips_ids)->all(), $generatedPayslips);
        if(Plugins::pluginInstalled(PluginUtil::COMMISSION_PLUGIN)) {
            foreach($payslips_ids as $payslipId) {
                $this->commissionSheetService->changePayslipCommissionSheetStatus($payslipId, CommissionSheetStatusUtil::PAID);
            }
        }
        foreach ($payruns_ids as $payrun_id) {
            event(new PayslipsApprovedEvent($payrun_id));
        }

        return [
            'success' => $successPayslipsCount = count($generatedPayslips) + $approvedPayslipsCount,
            'failed' => count($payslips) - $successPayslipsCount
        ];
    }


    /**
     * @param array $payslips_ids
     * @return array|void
     * @throws \App\Exceptions\UnexpectedBehavior
     */
    public function unApprove(array $payslips_ids)
    {
        $this->checkPayslipIdsNotEmpty($payslips_ids);

        $payslips = $this->repo->find($payslips_ids);

        $approvedPayslips = [];
        $generatedPayslipsCount = 0;
        foreach ($payslips as $payslip) {
            if ($payslip->status == PaySlipStatusUtil::APPROVED) {
                $approvedPayslips[] = $payslip;
            }
            if ($payslip->status == PaySlipStatusUtil::GENERATED) {
                $generatedPayslipsCount++;
            }
        }

        $success = $generatedPayslipsCount + count($approvedPayslips);
        $failed = count($payslips) - $success;

        if (!count($approvedPayslips)) {
            return compact('success','failed');
        }

        $payruns_ids = $payslips_ids = [];
        $installments = [];
        foreach ($approvedPayslips as $payslip) {
            if (!in_array($payslip->payrun_id, $payruns_ids)) {
                $payruns_ids[] = $payslip->payrun_id;
            }
            $installments = array_merge($installments,$this->repo->getPayslipInstallments($payslip->id)->pluck('id')->toArray());
            $payslips_ids[] = $payslip->id;

        }

        $payruns = $this->payrunRepository->find($payruns_ids);

        foreach ($payruns as $payrun) {
            $payslipValidatorRequest = new PayslipValidatorRequest($payrun->posting_date);
            $payslipValidator = new PayslipValidatorService($payslipValidatorRequest);
            $payslipValidator->setup($this->payrunRepository);
            $payslipValidator->validate();
        }

        $this->repo->setStatus($payslips_ids, PaySlipStatusUtil::GENERATED);

        $this->logCollection(ActivityLogActionsUtil::UPDATE, $this->repo->find($payslips_ids)->all(), $approvedPayslips);
        if(Plugins::pluginInstalled(PluginUtil::COMMISSION_PLUGIN)) {
            foreach($payslips_ids as $payslipId) {
                $this->commissionSheetService->changePayslipCommissionSheetStatus($payslipId, CommissionSheetStatusUtil::APPROVED);
            }
        }

        foreach ($payruns_ids as $payrun_id) {
            event(new PayslipsUnApprovedEvent($payrun_id,$installments));
        }

        return [
            'success' => $successPayslipsCount = $generatedPayslipsCount + count($approvedPayslips),
            'failed' => count($payslips) - $successPayslipsCount
        ];
    }

    public function getViewData($id)
    {
        $data = parent::getViewData($id);
        $data['authDeptId'] = $this->staffService->getAuthDepartmentId();

        if(!Permissions::checkPermission(PermissionUtil::VIEW_PAY_RUN)){
            $hasOwnViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_PAYSLIPS);
            $hasDeptViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_PAYSLIPS);
            $recordDeptId = $data['record']->staffInfo?->department_id ?? null;
            $sameDept = isset($recordDeptId) && isset($data['authDeptId']) && $data['authDeptId'] == $recordDeptId;
            $recordStaffId = $data['record']['staff_id'] ?? null;
            $authId = getAuthOwner('staff_id');
            $isOwn = isset($recordStaffId) && isset($authId) && $recordStaffId == $authId;
            $hasViewPermission = ($sameDept && $hasDeptViewPermission) || ($isOwn && $hasOwnViewPermission);
            if(!$hasViewPermission){
                throw new PageAccessNotAllowedException;
            }
        }

        $salaryComps = ['earningComponents' => [], 'deductionComponents' => []];
        foreach($data['record']->pivot_components as $comp)
        {
            if($comp->type === SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING)
            {
                $salaryComps['earningComponents'][] = $comp;
            }else{
                $salaryComps['deductionComponents'][] = $comp;
            }
        }
        $data['salaryComponents'] = $salaryComps;
        $data['currency'] = $data['record']->payRun->currency_code;
        if (Plugins::pluginInstalled(PluginUtil::COMMISSION_PLUGIN)) {
            $data['commissionSheets'] = $data['record']->commissionSheets;
        }

        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $data['view_templates'] = $repo->getEntityViewTemplates(EntityKeyTypesUtil::PAYSLIP);
        $data['email_templates'] = $repo->getEntityViewTemplates(EntityKeyTypesUtil::PAYSLIP, TemplateTypeUtil::EMAIL);
        return $data;
    }

    /**
     * This function is used in on of the Attendance Mobile App APIs (ESS App)
     * returns payslip detailed data with attendance data if available
     *
     * @param int $id
     * @return Payslip
     */
    public function getEssViewData($id){
        $data = parent::getViewData($id);
        $salaryComps = ['earningComponents' => [], 'deductionComponents' => []];
        foreach($data['record']->pivot_components as $comp)
        {
            if($comp->type === SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING)
            {
                $salaryComps['earningComponents'][] = $comp;
            }else{
                $salaryComps['deductionComponents'][] = $comp;
            }
        }
        $data['record']['salaryComponents'] = $salaryComps;
        $data['record']->payRun;
        // if (Plugins::pluginInstalled(PluginUtil::COMMISSION_PLUGIN)) {
        //     $data['record']->commissionSheets;
        // }
        if (Plugins::pluginInstalled(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            $data['record']['viewHisOwnAttendanceSheet'] = Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET);
            $data['record']['viewHisOwnAttendanceLog'] = Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG);
            $data['record']->load('attendanceSheet');
            $data['record']->load('attendanceSheet.attendanceDays');
            $data['record']->load('attendanceSheet.attendanceDays.leaveType');
            $takenLeaves = [];
            if(!empty($data['record']->attendanceSheet)){
                foreach($data['record']->attendanceSheet->attendanceDays as $day){
                    if(!empty($day->leaveType)){
                        if(!isset($takenLeaves[$day->leaveType->id])){
                            $takenLeaves[$day->leaveType->id] = [
                                'leave_type_id' => $day->leaveType->id,
                                'count' => 0,
                                'color' => $day->leaveType->color,
                                'name' => $day->leaveType->name,
                            ];
                        }
                        $takenLeaves[$day->leaveType->id]['count'] += 1;
                    }
                }
                $data['record']->attendanceSheet['takenLeaves'] = array_values($takenLeaves);
                $data['record']->attendanceSheet['attendance_flags'] = $data['record']->attendanceSheet->attendanceFlags;
            }
        }
        unset($data['record']['components']);
        return $data['record'];
    }

    /**
     * @param $id
     * @throws ClosedPeriodException
     * @throws PayslipCannotBeEditedException
     * @throws EntityNotFoundException
     */
    public function validateEditPayslip($id)
    {
        $payslip = $this->repo->find($id);
        if (!$payslip)
            throw new EntityNotFoundException($this->mainEntity->label);

        if ($payslip->status != PaySlipStatusUtil::GENERATED)
            throw new PayslipCannotBeEditedException(payslip: $payslip);

        if(ClosedPeriodRepository::getClosedPeriodsCountInDate($payslip->posting_date))
            throw new ClosedPeriodException($payslip->posting_date);

        if(!Permissions::checkPermission(PermissionUtil::EDIT_PAYSLIPS)){
            $authDeptId = $this->staffService->getAuthDepartmentId();
            $hasDeptEditPermission = Permissions::checkPermission(PermissionUtil::EDIT_HIS_DEPARTMENT_PAYSLIPS);
            $recordDeptId = $payslip->staffInfo?->department_id ?? null;
            $sameDept = isset($recordDeptId) && isset($authDeptId) && $authDeptId == $recordDeptId;
            if(!$sameDept || !$hasDeptEditPermission){
                throw new PageAccessNotAllowedException;
            }
        }
    }

    /**
     * @param Payslip $payslip
     */
    public function automaticAssignInstallmentsToPayslip(Payslip $payslip)
    {
        $currency = $payslip->payRun->currency_code ?? getCurrentSite("currency_code");

        $loansWithInstallments = $this->loanRepo->getInstallmentsIdsBeforeDueDate($payslip->staff_id, $currency, Formatter::formatForDB($payslip->end_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE));

        $loanSalaryComponentID = $this->salaryComponentRepository->getLoanComponent()->id ?? null;

        $parsedLoans = $this->parseLoans($loansWithInstallments, $loanSalaryComponentID, $payslip->id);

        $this->installmentRepo->assignInstallmentsToPayslip($parsedLoans['installmentsIDs'] ?? [], $payslip->id);

        $this->repo->insertPayslipComponents(array_values($parsedLoans['components']) ?? []);
    }

    /**
     * @array $payslipIds
     * @throws NoPayslipSelectedException
     */
    private function checkPayslipIdsNotEmpty(array $payslipsIds)
    {
        if (implode(null, $payslipsIds) == null)
            throw new NoPayslipSelectedException();
    }

    public function getPayrunBreadCrumbs(int $id): array
    {
        $payslip = $this->repo->find($id);
        $payrun = $payslip->payRun;

        $breadCrumbs[] = [
            'link' => route('owner.payruns.index'),
            'title' => __t('Payruns')
        ];

        $breadCrumbs[] = [
            'link' => route('owner.payruns.show', ['payrun' => $payrun->id]),
            'title' => sprintf('%s #%s', $payrun->name, $payrun->id)
        ];

        return $breadCrumbs;
    }

    private function getCommissionComponent($payslip)
    {
        $result = [];
        if(!Plugins::pluginInstalled(PluginUtil::COMMISSION_PLUGIN)) {
            return $result;
        }
        $sheets = $this->commissionSheetService->getPayslipCommissionSheets($payslip->id);
        $comissionComp = $this->salaryComponentRepository->getCommissionComp();
        if($sheets->isEmpty()) {
            return $result;
        }
        $amount = 0;
        foreach($sheets as $sheet) {
            $amount += $sheet->total_commission;
        }
        $result[0] = [
            'payslip_id' => $payslip->id,
            'amount' => $amount,
            'order' => -1,
            'salary_component_id' => $comissionComp->id,
            'name' => $comissionComp->name,
            'formula' => null,
            'source_id' => null
        ];
        return $result;
    }

    public function updateStatus($payslipId, $status) {
        parent::update($payslipId, new DefaultRequest(['status' => $status]));
    }


    public function validateImportData(&$data, $validationRules, $headersInfo, $entityKey, $rowCount, &$isUpdate, $updateUsing, &$invalidData, &$willBeUpdated) {

        $dateFormatIndex = getCurrentSite('date_format');
        $systemDateFormats = getDateFormats('std');
        $dateFormat = $systemDateFormats[$dateFormatIndex];

        // check if salary component has a negative value
        foreach ($data as $key => $value) {
            if (str_starts_with($key, 'salary_component') && (!is_numeric($value) || (float)$value < 0) && $headersInfo[$key] !== '') {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . sprintf(__t("Salary component (%s) should be numeric with a positive value"), $headersInfo[$key]));
            }
        }

        if (isset($data['currency_code'])) {
            if (!Currency::where('code', $data['currency_code'])->exists()) {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("Invalid currency code"));
                return false;
            }
        }

        if (!empty($data['posting_date'])) {
            $parsedDate = Carbon::createFromFormat($dateFormat, $data['posting_date']);
            if ($parsedDate->format($dateFormat) !== $data['posting_date']) {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("Invalid date format, required format is ") . $dateFormat);
                return false;
            }

            if (ClosedPeriodRepository::getClosedPeriodsCountInDate($data['posting_date'])) {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("Posting date is in a closed period"));
                return false;
            }
        }


        if (isset($data['start_date']) && isset($data['end_date'])) {
            $parsedStartDate = Carbon::createFromFormat($dateFormat, $data['posting_date']);
            $parsedEndDate = Carbon::createFromFormat($dateFormat, $data['posting_date']);

            if ($parsedStartDate->format($dateFormat) !== $data['posting_date'] || $parsedEndDate->format($dateFormat) !== $data['posting_date']) {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("Invalid date format, required format is ") . $dateFormat);
                return false;
            }
            $data['start_date'] = Formatter::formatForDB($data['start_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
            $data['end_date'] = Formatter::formatForDB($data['end_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
            if ($data['start_date'] > $data['end_date']) {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("Start date is greater than end date"));
                return false;
            }
        }

        if (!empty($data['staff_id'])) {
            $data['staff_id'] = preg_replace('/\s+$/u', '', $data['staff_id']);
            $staffId = $this->staffRepository->findByFullNameOrId($data['staff_id']);

            if ($staffId->count() == 0) {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("The name or code of the employee is not available in the system."));
                return false;
            }

            if ($staffId->count() > 1) {
                $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("Duplicate employee match found for '".$data['staff_id']."'. Please use a unique identifier or employee code."));
                return false;
            }

            $data['staff_id'] = $staffId->first()->id ?? null;
        } else {
            $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . __t("The name or code of the employee is not available in the system."));
            return false;
        }

        if ($records = $this->repo->getPayslipsExactDates($data['start_date'], $data['end_date'], $data['staff_id'])) {
            foreach($records as $record) {

                if (in_array($record->status, [PaySlipStatusUtil::PAID, PaySlipStatusUtil::APPROVED])) {
                    $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount, ': ' . sprintf(__t("You cannot update the payslip #%s for the period from %s to %s for %s (#%s), as its status is either approved or paid."), $record->id, $record->start_date, $record->end_date, $record->staff->full_name, $record->staff->id));
                    return false;
                }

                $willBeUpdated = $isUpdate = true;
                $this->parameters['update_conditions']['id'][$rowCount] = $record->id;
            }
        } else {
            $willBeUpdated = false;
        }

        return true;
    }

    public function validateAllItems($data, $extra_data, $fields)
    {
        if (count($data) > 5000) {
            throw new ImportErrorException([__t("The maximum number of records in the sheet is 5000 records")]);
        }

        return [];
    }

    public function prepareBeforeUpdate(&$item, $rowNumber)
    {
        $item['id'] = $this->parameters['update_conditions']['id'][$rowNumber];
    }

    /**
     * @param \App\Requests\DefaultRequest  $request
     * @return mixed
     */
    function import($request)
    {
        // map request for add()
        $mappedRequest = $this->mapImportRequest($request);
        return $this->add($mappedRequest, [], null, true);
    }

    public function mapImportRequest($request)
    {
        $rq = $request->all();

        $mappedRequest = [
            'posting_date' => $rq['posting_date'],
            'employee' => $rq['staff_id'],
            'status' => PaySlipStatusUtil::GENERATED,
            'staff_id' => $rq['staff_id'],
            'start_date' => $rq['start_date'],
            'end_date' => $rq['end_date'],
            'notes' => $rq['notes'] ?? '',
            'currency' => $rq['currency_code'],
        ];

        $mappedRequest['gross_pay'] = $this->calculateGrossPay($request);
        $mappedRequest['net_pay'] = $this->calculateNetPay($request);
        $mappedRequest['total_deduction'] = $this->calculateTotalDeduction($request);

        // Map payslipComponent
        $mappedRequest['payslipComponent'] = $this->mapPayslipComponents($request);

        return new DefaultRequest($mappedRequest);
    }

    private function mapPayslipComponents($request)
    {
        $components = $this->getSalaryComponentsFromImportRequest($request->all());
        $mappedComponents = [
            'earning' => [],
            'deduction' => []
        ];

        foreach ($components as $component) {
            if ($request->get('salary_component_' . $component->id) == 0) {
                continue;
            }
            $componentData = [
                'salary_component_id' => $component->id,
                'amount' => $request->get('salary_component_' . $component->id),
                'formula' => null, // You may need to adjust this if formulas are imported
                'order' => $component->order ?? 0, // You may need to adjust this based on your requirements
            ];

            if ($component->type === SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING) {
                $mappedComponents['earning'][] = $componentData;
            } else {
                $mappedComponents['deduction'][] = $componentData;
            }
        }

        return $mappedComponents;
    }

    private function calculateGrossPay($request)
    {
        $components = $this->getSalaryComponentsFromImportRequest($request->all());
        $grossPay = 0;
        foreach($components as $component) {
            if ($component->type === SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING) {
                $grossPay += (int)$request->get('salary_component_' . $component->id);
            }
        }
        return $grossPay;
    }

    private function calculateTotalDeduction($request) {
        $components = $this->getSalaryComponentsFromImportRequest($request->all());
        $deduction = 0;
        foreach($components as $component) {
            if($component->type === SalaryComponentTypeUtil::SALARY_COMPONENT_DEDUCTION) {
                $deduction += (int)$request->get('salary_component_'.$component->id);
            }
        }
        return $deduction;
    }

    private function calculateNetPay($request) {
        return $this->calculateGrossPay($request) - $this->calculateTotalDeduction($request);
    }

    /**
     * get salary components from import request
     *
     * @param array $requestData
     */
    private function getSalaryComponentsFromImportRequest(array $requestData)
    {
        $salaryComponentIds = array_filter(array_keys($requestData), function($key) {
            return strpos($key, 'salary_component_') === 0;
        });

        $salaryComponentIds = array_values(array_map(function($item) {
            return str_replace('salary_component_', '', $item);
        }, $salaryComponentIds));
        return $this->salaryComponentRepository->findWhereIn('id', $salaryComponentIds);
    }

    public function getEntitiesForExport($entity)
    {
        /** @var ExportService $exportService */
        $exportService = resolve(ExportService::class);
        $entities = $exportService->getEntities($entity);

        // ignore currency_code field because it doesn't exist in payslips table but, it's required for import.
        $entities['payslip']['fields'] = collect($entities['payslip']['fields'])->reject(function ($field) {
            return $field->key == 'payslips.currency_code';
        });
        $entities['payslip_components'] = [
            'fields' => $this->salaryComponentRepository->mapSalaryComponentsToFields([], false),
            'multiple' => true,
            'db_name' => 'salary_components',
            'label' => 'Salary Components',
        ];


        return $entities;
    }

    public function getImportDownloadSample() {
        return ['path' => '/samples/payslip-sample.csv', 'file_name' => __t('Payslip sample') . '.csv'];
    }

    protected function getSalaryComponents($parameters) {
        if (!empty($parameters['data']['salary_component_ids'])) {
            $salaryComponentIds = $parameters['data']['salary_component_ids'];
            $salaryComponentIds = array_values(array_map(function($item) {
                return str_replace('salary_component_', '', $item);
            }, $salaryComponentIds));
            return $this->salaryComponentRepository->findWhereIn('id', $salaryComponentIds);
        }

        return [];
    }

    public function after_prepare_data($data) {
        $payslips = collect($data)->mapWithKeys(function($payslip) {
            return [$payslip['staff_id'].'_'.$payslip['start_date'].'_'.$payslip['end_date'] => $payslip];
        })->toArray();

        $salaryComponents = $this->getSalaryComponents($this->parameters);

        if (empty($salaryComponents)) {
            return $payslips;
        }

        $payslipConditions = array_map(function ($item) {
            return [
                'start_date' => $item['start_date'],
                'end_date' => $item['end_date'],
                'staff_id' => $item['staff_id']
            ];
        }, $payslips);
        $records = $this->repo->getBulkPayslipsExactDates($payslipConditions)->toArray();

        foreach ($records as $record) {
            $idx = $record['staff_id'].'_'.$record['start_date'].'_'.$record['end_date'];

            $components = $this->payslipComponentRepository->getBulkComponentAmount(
                $record['id'],
                $salaryComponents->pluck('id')->toArray()
            );

            foreach ($salaryComponents as $salaryComponent) {
                $payslips[$idx]['salary_components.' . $salaryComponent->name] = !empty($components[$salaryComponent->id]) ? $components[$salaryComponent->id] : 0;
            }
        }

        return $payslips;
    }

    public function getLabels($data, $label)
    {
        $labels = [];
        $fields = app(MappingService::class)->getExportEntityFields(EntityKeyTypesUtil::PAYSLIP);
        $fields = $fields->mapWithKeys(function ($fieldInfo) {
            return ["{$fieldInfo->key}" => $fieldInfo];
        })->toArray();

        $keys = array_keys($data);
        foreach ($keys as $key) {
            if (isset($fields[$key])) {
                $labels[] = __t($fields[$key]['label']);
            }
        }

        $salaryComponents = $this->getSalaryComponents($this->parameters);
        foreach ($salaryComponents as $salaryComponent) {
            $labels[] = $salaryComponent->name;
        }

        return array_values($labels);
    }


    public function getStaffLatestPayslips($staffId,$currency)
    {
        if (!Permissions::checkPermission(PermissionUtil::VIEW_PAY_RUN)) return ['isVisible'=>false];
        $payslips =$this->repo->getStaffLatestPayslips($staffId , 10 , $currency);
        return $this->formatPayslipsForStaffProfileApi($payslips,$staffId);
    }

    private function formatPayslipsForStaffProfileApi($payslips,$staffId): array
    {
        $count = $this->repo
            ->pushCriteria(new EqualCriteria('staff_id' , $staffId))
            ->pushCriteria(new IsNullCriteria('deleted_at'))
            ->count();
        $viewUrl = null;
        if ($count > 10 ){
            $viewUrl = route('owner.payslips.index' , ['staff[]'=>$staffId]);
        }
        $formattedPayslips = [
            'title'=> __t('Payslips').   ' (' . $count . ')',
            'viewUrl'=>$viewUrl,
            'statuses'=>PayrunStatusUtil::getAll(),
            'items'=>[],
            'isVisible'=>true,
            'canCreate'=> Permissions::checkPermission(PermissionUtil::CREATE_PAY_RUN),
            'createUrl'=>route("owner.payslips.create" , ['staff'=>$staffId])
        ];
        /** @var Payslip $payslip */
        foreach ($payslips as $payslip){
            $fromDate = format_date($payslip->start_date);
            $toDate = format_date($payslip->end_date) ;
            $formattedPayslips['items'][] = [
                'id' => $payslip->id,
                'period'=> $fromDate . ' - ' . $toDate,
                'amount' => format_price($payslip->net_pay, $payslip->payRun?->currency_code),
                'isPartial'=>$payslip->isPartial() ,
                'viewUrl'=>route("owner.payslips.show" , ['payslip'=>$payslip->id]) ,
                'currency'=>'',
                'status'=>[
                    'text'=>__t(PayrunStatusUtil::getStatus($payslip->status)),
                    'value'=>$payslip->status,
                ]
            ];
        }
        return $formattedPayslips;
    }

    /**
     * returns imaginary currency code field for import
     * @return EntityField
     */
    public static function getMockCurrencyCodeField(): EntityField
    {
        return new EntityField([
            'id' => -1,
            'key' => 'payslips.currency_code',
            'entity' => 'payslip',
            'field_type' => 'text',
            'plugin_id' => null,
            'label' => 'Currency Code',
            'db_name' => 'currency_code',
            'default_value' => null,
            'is_filter' => 0,
            'validation_rules' => ['nullable'],
            'on_create' => 0,
            'on_update' => 0,
            'on_import' => 1,
            'on_export' => 1,
            'is_listing' => 1,
            'is_listing_mobile' => 1,
            'order_index' => 0,
            'is_sorting' => 0,
            'sorting_dir' => null,
            'allowed_values' => null,
        ]);
    }
}
