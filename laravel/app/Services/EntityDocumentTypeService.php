<?php

namespace App\Services;

use App\Repositories\EntityDocumentTypeRepository;
use App\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\EntityDocumentTypeUtil;

class EntityDocumentTypeService
{
    public function __construct(protected  EntityDocumentTypeRepository $repo) {}

    public function getEntityDocumentTypes($entityKey)
    {
        $results = $this->repo->where(['entity_key' => $entityKey])->all()->toArray();
        // Sort by display_order (ascending)
        usort($results, function ($a, $b) {
            return $a['display_order'] <=> $b['display_order'];
        });
        return $results;
    }

    public function validateEntityKey($entityKey): bool
    {
        if (in_array($entityKey, $this->getSupportedEntities())) return true;
        throw new \Exception('Entity key is not supported');
    }

    private function getSupportedEntities(): array
    {
        return EntityDocumentTypeUtil::getList();
    }

    public function updateEntityDocumentTypes($entityKey , $data)
    {
        $this->handleDeletedTypes($entityKey , $data);
        $updatedRecords = [];
        $createdRecords = [];
        foreach ($data as $index => &$datum){
            $datum['display_order'] = $index + 1;
            $datum['entity_key'] = $entityKey;
            if (isset($datum['id'])) {
                $updatedRecords[] = $datum;
            }else{
                $createdRecords[] = $datum;
            }
        }
        $this->handleUpdatedTypes($updatedRecords);
        $this->handleCreatedTypes($createdRecords);
    }

    private function getDocumentsByTypes(array $typesIds)
    {
        return $this->repo->getDocumentsByTypesIds($typesIds);
    }

    private function handleDeletedTypes($entityKey , $data)
    {
        $types = $this->getEntityDocumentTypes($entityKey);

        $oldTypesIds = array_column($types, 'id');
        $sentDataIds = array_column($data, 'id');

        $deletedIds = array_diff($oldTypesIds, $sentDataIds);
        $errors=[];
        if (count($deletedIds)) {
            $groupedDocuments = $this->getDocumentsByTypes($deletedIds)->groupBy('entity_document_type_id')->toArray();
            if (count($groupedDocuments)) {
                foreach ($groupedDocuments as $id => $groupOfDocuments) {
                    $errors[] = ['typeId'=>$id , 'documents' => array_column($groupOfDocuments , 'id')];
                }
            }else{
                $this->repo->deleteDocuments($deletedIds);
            }
        }

        if (count($errors)) {
            $firstError = $errors[0];
            $type = array_filter($types ,function ($it) use ($firstError){
                return $it['id'] == $firstError['typeId'];
            });
            $typeName = array_values($type)[0]['name'];
            //@todo : add list of files to exception , need front end help
            $msg = sprintf(__t('You cannot delete %s type because it’s currently in use, Please update or remove all documents using this type before you can delete it'), $typeName);
            throw new \Exception($msg);
        }
    }

    private function handleUpdatedTypes(array $updatedRecords)
    {
        if (!count($updatedRecords))return;
        $this->repo->updateTypes($updatedRecords);
    }

    private function handleCreatedTypes(array $createdRecords)
    {
        if (!count($createdRecords))return;
        $this->repo->saveTypes($createdRecords);
    }


}
