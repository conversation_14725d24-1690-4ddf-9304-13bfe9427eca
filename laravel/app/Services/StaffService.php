<?php

namespace App\Services;

use App\Adapters\InternalHttpAdapter;
use App\Entities\AttendanceCalculationCriteria;
use App\Events\StaffSavedEvent;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Staff\StaffAssignedToContract;
use App\Exceptions\Staff\StaffAssignedToLoan;
use App\Exceptions\Staff\StaffIsAssignedToClient;
use App\Exceptions\Staff\StaffIsDepartmentManager;
use App\Exceptions\Staff\StaffIsDirectManagerToStaff;
use App\Exceptions\Staff\StaffIsInConfigurationApprovalLevel;
use App\Exceptions\Staff\StaffIsInLeaveApplicationApprovalSettings;
use App\Exceptions\Staff\StaffTypeInvalid;
use App\Facades\Branch;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Facades\SystemLimitFactory;
use App\Helpers\FilterOperations;
use App\Jobs\Staff\SendLoginDetails;
use App\Models\Staff;
use App\Helpers\AutoNumber;
use App\Modules\Template\Services\Email\SMTPEmailAddressService;
use App\Queue\Events\EmployeeDeletedEvent;
use App\Repositories\AttendanceRestrictionRepository;
use App\Repositories\ContractRepository;
use App\Repositories\Criteria\SelectCriteria;
use App\Repositories\PayRunRepository;
use App\Repositories\BranchRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\EmploymentLevelRepository;
use App\Repositories\EmploymentTypeRepository;
use App\Repositories\EntityRepository;
use App\Repositories\HolidayListRepository;
use App\Repositories\JournalAccountRepository;
use App\Repositories\LanguageRepository;
use App\Repositories\LeavePolicyRepository;
use App\Repositories\PostRepository;
use App\Repositories\RoleRepository;
use App\Repositories\ShiftRepository;
use App\Repositories\StaffRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Utils\ContractStatusUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityPermissionKeyUtil;
use App\Utils\FilePathUtil;
use App\Utils\MonthDaysUtil;
use App\Utils\PluginUtil;
use App\Utils\PostTypesUtil;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Staff\Util\StaffTypeUtil;
use Izam\Entity\LocalEntityForm\AdditionalFormsHandler;
use Ramsey\Uuid\Uuid;
use League\OAuth2\Server\Exception\OAuthServerException;
use App\Facades\Permissions;
use App\Facades\Staff as FacadesStaff;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use Illuminate\Support\Facades\Schema;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Staff\Util\StaffCitizenshipStatus;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationRematcher;
use App\Repositories\EntityDocumentRepository;
use App\Repositories\ItemStaffRepository;
use App\Requests\DefaultRequest;
use App\Utils\ItemStaffUtil;
use App\Utils\PayrunCriteriaUtil;
use Exception;
use Illuminate\Support\Collection;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;

class StaffService extends BaseService
{
    /**
     * @var string $showRouteName staff show route name
     */
    protected $showRouteName = 'owner.staff.show';

    /**
     * @var StaffRepository $repo
     */
    public $repo;
    private $roleRepo;
    private $empLevelRepo;
    private $empTypeRepo;
    private $deptRepo;
    private $designationRepo;
    private $branchesRepo;
    private $journalAccountsRepo;
    private $leavePolicyRepository;
    private $shiftRepository;
    private $holidayListRepository;
    private $attendanceRestrictionRepo;
    /**
     * @var EntityPermissionService
     */
    private $entityPermissionService;

    private $authUserLeaveApplicationRelatedUsers;

    /**
     * StaffService constructor.
     * @param StaffRepository $repo
     * @param RoleRepository $roleRepo
     * @param EmploymentLevelRepository $empLevelRepo
     * @param EmploymentTypeRepository $empTypeRepo
     * @param DepartmentRepository $deptRepo
     * @param DesignationRepository $designationRepo
     * @param BranchRepository $branchesRepo
     * @param EntityRepository $entityRepo
     * @param CustomDataService $customDataService
     */
    public function __construct(
        StaffRepository $repo,
        RoleRepository $roleRepo,
        EmploymentLevelRepository $empLevelRepo,
        EmploymentTypeRepository $empTypeRepo,
        DepartmentRepository $deptRepo,
        DesignationRepository $designationRepo,
        BranchRepository $branchesRepo,
        JournalAccountRepository $journalAccountRepository,
        EntityRepository $entityRepo,
        LanguageRepository $languageRepository,
        LeavePolicyRepository $leavePolicyRepository,
        ShiftRepository $shiftRepository,
        HolidayListRepository $holidayListRepository,
        CustomDataService $customDataService,
        PostRepository $postRepository,
        EntityPermissionService $entityPermissionService,
        AttendanceRestrictionRepository $attendanceRestrictionRepo,
        private AdditionalFieldsAwareService $additionalFieldsAwareService,
        protected PayRunRepository $payRunRepository
    ) {
        $this->roleRepo = $roleRepo;
        $this->empLevelRepo = $empLevelRepo;
        $this->empTypeRepo = $empTypeRepo;
        $this->deptRepo = $deptRepo;
        $this->designationRepo = $designationRepo;
        $this->branchesRepo = $branchesRepo;
        $this->journalAccountsRepo = $journalAccountRepository;
        $this->languageRepository = $languageRepository;
        $this->leavePolicyRepository = $leavePolicyRepository;
        $this->shiftRepository = $shiftRepository;
        $this->holidayListRepository = $holidayListRepository;
        $this->postRepository = $postRepository;
        $this->attendanceRestrictionRepo = $attendanceRestrictionRepo;
        $this->entityPermissionService = $entityPermissionService;
        parent::__construct($repo, $entityRepo, $customDataService);
        $this->activityLogService = $this->getActivityLogService();
        $this->activityLogService->appendKeyToExceptKeys('photo');
        $this->activityLogService->appendValueToDefaultValuesIfEmpty('Gender', 'None');
    }

    protected function getListingConditions()
    {
        $listingConditions = parent::getListingConditions();
        $authUserID = getAuthOwner('staff_id');
        $accessibleBranchListingCondition = $this->getAccessibleBranchListingCondition([
            "operation" => FilterOperations::IN_FILTER_OPERATION,
            "field" => "id",
        ]);
        if($authUserID != 0 && !empty($accessibleBranchListingCondition)){

            $listingConditions[] = $accessibleBranchListingCondition;
        }
        return $listingConditions;
    }

    public function getFormRelatedRepos()
    {
        $activeCriteria = new CustomFind([['field' => 'active', 'value' => '1']]);
        $statusCriteria = new CustomFind([['field' => 'status', 'value' => '1']]);
        $formRelatedRepos = [
            'roles' => [
                'class' => 'App\Repositories\RoleRepository',
                'columns' => ['id', 'name']
            ],
            'countries' => [
                'class' => 'App\Repositories\CountryRepository',
                'columns' => ['code', is_rtl() ? 'ar_name' : 'country']
            ],
            'languages' => [
                'class' => 'App\Repositories\LanguageRepository',
                'columns' => ['id', is_rtl() ? 'ar_name' : 'name'],
                'criteria' => $activeCriteria,
            ]
        ];
        if (Plugins::pluginActive(PluginUtil::BookingPlugin) || Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN) || Plugins::pluginActive(PluginUtil::NEW_BOOKING_PLUGIN)) {
            $formRelatedRepos += [
                'shifts' => [
                    'class' => 'App\Repositories\ShiftRepository',
                    'columns' => ['id', 'name']
                ]
            ];
        }
        if (Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            $formRelatedRepos += [
                'leave_policies' => [
                    'class' => 'App\Repositories\LeavePolicyRepository',
                    'columns' => ['id', 'name'],
                    'criteria' => $statusCriteria
                ],
                'holiday_lists' => [
                    'class' => 'App\Repositories\HolidayListRepository',
                    'columns' => ['id', 'name']
                ]
            ];

            $formRelatedRepos += [
                'attendance_restrictions' => [
                    'class' => 'App\Repositories\AttendanceRestrictionRepository',
                    'columns' => ['id', 'name'],
                    'criteria' => $statusCriteria
                ]
            ];


        }
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $formRelatedRepos += [
                'departments' => [
                    'class' => 'App\Repositories\DepartmentRepository',
                    'columns' => ['id', 'name'],
                    'criteria' => $activeCriteria,
                ],
                'employment_levels' => [
                    'class' => 'App\Repositories\EmploymentLevelRepository',
                    'columns' => ['id', 'name'],
                    'criteria' => $activeCriteria,
                ],
                'employment_types' => [
                    'class' => 'App\Repositories\EmploymentTypeRepository',
                    'columns' => ['id', 'name'],
                    'criteria' => $activeCriteria,
                ],
            ];
        }
        if (Plugins::pluginActive(PluginUtil::InventoryPlugin)) {
            $formRelatedRepos += [
                'inventories' => [
                    'class' => 'App\Repositories\StoreRepository',
                    'columns' => ['id', 'name'],
                    'criteria' => $activeCriteria,
                ],
            ];
        }
            return $formRelatedRepos;
    }

    public function getFormRelatedData()
    {
        $relatedData = parent::getFormRelatedData();
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $activeCriteria = new CustomFind([['field' => 'active', 'value' => '1']]);
            $designationRepo = resolve('App\Repositories\DesignationRepository');
            $relatedData['designations'] = $designationRepo->resetCriteria()->getByCriteria($activeCriteria)->all();
        }

        $relatedData['months'] = MonthDaysUtil::getMonthList();
        $relatedData['monthDays'] = MonthDaysUtil::getMonthDays();
        $roles = $this->roleRepo->pushCriteria( new CustomFind(
            [
                ['field' => 'type', 'value' => request('type')],
            ]
        ))->list();
        $relatedData['roles'] = $roles;
        $relatedData['code'] = AutoNumber::get_auto_serial(AutoNumber::TYPE_STAFF);
        return $relatedData;
    }

    public function getFormData($id = false)
    {
        $parentData = parent::getFormData($id); // TODO: Change the autogenerated stub
        $defaultAccountId = false;
        $defaultAccountoptions = [];
        if (old('default_account_id')) {
            $defaultAccountId = request()->old('default_account_id');
        } else if($id) {
            $defaultAccountId = $parentData['form_record']->default_account_id;
            // i made this line because relation some times not working well depend on env after so many debuging no solution
            //when call relation apache terminate process and project not work so this a temporary solution
            //$parentData['form_record']->shift = $this->repo->getShifts($id)->pluck('item_id')->toArray();

        }
        if($defaultAccountId) {
            $journal_record = $this->journalAccountsRepo->find($defaultAccountId);
            $defaultAccountString = $journal_record->code . " " . $journal_record->name;
            $defaultAccountoptions = array($journal_record->id => $defaultAccountString);
        }
        $parentData['defaultAccountOptions'] = $defaultAccountoptions;

        $parentData['form'] = $id ?
            $this->additionalFieldsAwareService->getFormData(EntityKeyTypesUtil::STAFF_ENTITY_KEY, $id) :
            $this->additionalFieldsAwareService->getCreateDate(EntityKeyTypesUtil::STAFF_ENTITY_KEY);

            $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
            $parentData['customForms'] = $id ?
                $additionalFormsHandler->getEditForms($id, old()) :
                $additionalFormsHandler->getCreateForms(old());

        if (!$id) {
            $roles = $this->roleRepo->pushCriteria( new CustomFind(
                [
                    ['field' => 'type', 'value' => request('type')],
                    ['field' => 'id', 'operation' => FilterOperations::NOT_EQUAL_FILTER_OPERATION, 'value' => Staff::OWNER_ROLE_ID],
                ]
            ))->list();
        } else {
            if($parentData['form_record']->role_id == Staff::OWNER_ROLE_ID){
                $roles = $this->roleRepo->list();
            }else{
                $roles = $this->roleRepo->pushCriteria( new CustomFind(
                    [
                        ['field' => 'id', 'operation' => FilterOperations::NOT_EQUAL_FILTER_OPERATION, 'value' => Staff::OWNER_ROLE_ID],
                    ]
                ))->list();
            }
        }
        $parentData['related_form_data']['roles'] = $roles;
        $parentData['related_form_data']['employee_roles'] = $this->roleRepo->pushCriteria( new CustomFind(
            [
                ['field' => 'type', 'value' => 'employee']
            ]
        ))->list();

        $parentData['related_form_data']['user_roles'] = $this->roleRepo->pushCriteria( new CustomFind(
            [
                ['field' => 'type', 'value' => 'user'],
                ['field' => 'id', 'operation' => FilterOperations::NOT_EQUAL_FILTER_OPERATION, 'value' => Staff::OWNER_ROLE_ID],
            ]
        ))->list();


        if(Plugins::pluginActive(PluginUtil::HRM_PLUGIN)){
            $staffList = [];
            if ($id) {
                $staff = $this->find($id)->staff_info->direct_manager??[];
                if ($staff) {
                    $staffList[] = [
                        'text' => "#$staff->code $staff->name $staff->last_name ($staff->email_address)",
                        'value' => $staff->id,
                        'data' => [
                            'data-img' => AvatarURLGenerator::generate($staff->name, $staff->id, 30, $staff->photo),
                            'selected' => true
                        ]
                    ];
                }
            }
            $parentData['related_form_data']['staffList'] = $staffList;
        }
        if(Plugins::pluginActive(PluginUtil::NEW_BOOKING_PLUGIN) && !$id){
            $parentData['related_form_data']['default_booking_shift_id'] = \Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'booking_default_shift', null, false) ?? null;
        }
        $parentData['related_form_data']['smtpEmailAddress'] = app(SMTPEmailAddressService::class)->list();

         return $parentData;
    }

    public function getViewData($id)
    {
        $result = parent::getViewData($id);
        $result['show_attendance_mobile_app_tab'] = false;

        $android_url = "https://play.google.com/store/apps/details?id=com.izam.attendance_mobile";
        $ios_url = "https://apps.apple.com/us/app/تسجيل-الحضورمن-دفترة-ess/id1632210042";

        $result['android_url'] = $android_url;
        $result['ios_url'] = $ios_url;
        $result['staff_id'] = $id;

        if ($result['record']->active && Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            if ($result['record']->can_access_system && $result['record']->type == StaffTypeUtil::USER) {
                $result['show_attendance_mobile_app_tab'] = true;
            } else if ($result['record']->type == StaffTypeUtil::EMPLOYEE) {
                $result['show_attendance_mobile_app_tab'] = true;
            }
        }

        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('staff');
        $result['view_templates'] = $viewTemplates;
        $result['form'] = $this->additionalFieldsAwareService->show(EntityKeyTypesUtil::STAFF_ENTITY_KEY, $id);
        $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        $result['appForms'] = $additionalFormsHandler->show($id);
        $result['notesCount'] = $this->postRepository->getPostsCount($id,PostTypesUtil::STAFF_TYPE);
        $permissionsArray = [
            PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG,
            PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG,
            PermissionUtil::CALCULATE_ATTENDANCE_DAY,
            PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET,
            PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS,
            PermissionUtil::VIEW_HIS_OWN_LEAVE_APPLICATIONS,
            PermissionUtil::VIEW_ALL_LEAVE_APPLICATION,
            PermissionUtil::Staff_Add_New_Staffs,
            PermissionUtil::Staff_Edit_Staffs,
        ];
        $canShowAttendanceTab = Permissions::checkPermission($permissionsArray);
        $result['canShowAttendanceTab'] = $canShowAttendanceTab;


        $payrollTabPermissions = [
            PermissionUtil::VIEW_PAY_RUN,
            PermissionUtil::MANAGE_LOANS_AND_INSTALLMENT_SETTINGS,
            PermissionUtil::VIEW_PAYROLL_CONTRACT,
            PermissionUtil::VIEW_HIS_OWN_CONTRACT,
        ];
        $canShowPayrollTab = Permissions::checkPermission($payrollTabPermissions);
        $result['canShowPayrollTab'] = $canShowPayrollTab;
        $result['documentsAlertCount'] = resolve(EntityDocumentRepository::class)->getDocumentAlertsCount(EntityKeyTypesUtil::STAFF_ENTITY_KEY, $id);
        $result['documentsAlertWarningMessage'] = resolve(EntityDocumentService::class)->getDocumentAlertWarningmessage(EntityKeyTypesUtil::STAFF_ENTITY_KEY, $id);
        // i made this line because relation some times not working well depend on env after so many debuging no solution
        //when call relation apache terminate process and project not work so this a temporary solution
        //$result['record']->shift = $this->repo->getShifts($id);
        return $result;
    }

    /**
     * verify if user is valid to log in to mobile or desktop
     */

    public function verify($staff_id)
    {

         $staff = $this->repo->find($staff_id);
         if ($staff) {
             if (! $staff->active) {
                $error = "inactive_user";
                $staff_type = StaffTypeUtil::USER;
                if ($staff->type== StaffTypeUtil::EMPLOYEE) {
                    $error = "inactive_employee";
                    $staff_type = StaffTypeUtil::EMPLOYEE;
                }
                throw new OAuthServerException(sprintf(__t('%s has been deactivated please contact your admin'), $staff_type), 401, $error);
            }
            if (!$staff->can_access_system &&  $staff->type == StaffTypeUtil::USER) {
                throw new OAuthServerException("You are not allowed to access the system please contact your admin", 401, 'user_cannot_access_system');
            }
         }


    }
    public function verifyValidationForAttendance(Staff $staff)
    {

        if (! Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            throw new \Exception(__t("Plugin not active"));
        }


        if (!Permissions::checkPermission(PermissionUtil::TAKE_HIS_OWN_ATTENDANCE)) {
            throw new \Exception(__t("You don’t have the permission to sign your attendance"));
        }

        $this->verify($staff->id);



    }




    /**
     * returns service sort fields
     * @return array
     */
    function getSortFields()
    {
        return [
            'fields' => [
                'name' => [
                    'title' => __t('Name'),
                    'field' => 'name',
                    'direction' => 'DESC'
                ],
                'created' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'created',
                    'direction' => 'ASC'
                ],
                'active' => [
                    'hidden' => true,
                    'title' => __t('Status'),
                    'field' => 'active',
                    'direction' => 'DESC',
                ]
            ],
            'active' => [
                //'disableIDSort' => true,
                'field' => 'name',
                'direction' => 'ASC'
            ]
        ];
    }

    function getFilters()
    {
        $staffList = [];
        $this->swapEntityFilterKeys();
        if (!empty($this->parameters['name']) && is_array($this->parameters['name'])) {
            $staffs = $this->repo->findWhereIn('id', $this->parameters['name']);
            foreach ($staffs as $staff) {
                $staffList[] = FacadesStaff::getStaffOptionFormated($staff) ;
            }
        }
        $roles = $this->roleRepo->list(['id', 'name']);
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $types = $this->empTypeRepo->list(['id', 'name']);
            $levels = $this->empLevelRepo->list(['id', 'name']);
            $depts = $this->deptRepo->list(['id', 'name']);
            $designations = $this->designationRepo->list(['id', 'name']);
        }
        if (Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            $attendanceRestrictionsList = $this->attendanceRestrictionRepo->list(['id','name']);
        }

        $filters = [
            'name[]' => [
                'simple' => true, /* displays the filter outside the toggle */
                'label' => false,
                'after' => '<i class="input-icon fal fa-search"></i></div>',
                'before' => '<div class="form-group-icon  form-group">',
                'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                'param_name' => 'name',
                'attributes' => [
                    'placeholder' => sprintf(__t('Search by %s, %s or %s'),__t('Employee Name'), __t('ID'), __t('Email Address')),
                    'id' => 'staffSelect',
                    'multiple' => true,
                    'data-staff-url' => route('owner.staff.search', ['get_branch_suspended' => 1, 'allow_inactive' => 1]),
                ],
                'div' => 'col-md-8',
                'options' => $staffList,
                'type' => 'selectStaff',
                'filter_options' => [
                    'operation' => FilterOperations::IN_FILTER_OPERATION,
                    'field' => 'id',
                ]
            ],
            'active' => [
                'simple' => true, /* displays the filter outside the toggle */
                'label' => false,
                'attributes' => [
                    'placeholder' => __t('Select').' '.__t('Status'),
                    'id' => 'activeSelect'
                ],
                'div' => 'col-md-4 form-group',
                'inputClass' => 'select-filter form-control',
                'options' => [
                    1 => __t('Active'),
                    0 => __t('Inactive')
                ],
                'type' => 'select',
                'filter_options' => [
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'active'
                ]
            ],
            'type' => [
                'label' => false,
                'attributes' => [
                    'placeholder' => __t('Select').' '.__t('Type'),
                    'id' => 'modeSelect'
                ],
                'div' => 'col-md-4 form-group',
                'inputClass' => 'select-filter form-control',
                'options' => [
                    'all' => __t('All'),
                    'employee' => __t('Employee'),
                    'user' => __t('User')
                ],
                'type' => 'select',
                'filter_options' => [
                    "operation" => FilterOperations::CLOSURE,
                    "field" => "type",
                    'function' => function ($type) {
                        if (in_array($type, [StaffTypeUtil::EMPLOYEE, StaffTypeUtil::USER])) {
                            return [
                                'operation' => FilterOperations::IN_FILTER_OPERATION,
                                'field' => 'type',
                                'value' => $type,
                            ];
                        }
                    }
                ]
            ],
            'role' => [
                'attributes' => [
                    'placeholder' => __t('Select').' '.__t('Role'),
                    'id' => 'roleSelect'
                ],
                'label' => '',
                'div' => 'col-md-4 form-group',
                'inputClass' => 'select-filter form-control',
                'options' => $roles->toArray(),
                'type' => 'select',
                'filter_options' => [
                    'table' => 'roles',
                    'model' => 'role',
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'id'
                ]
            ]
        ];

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $branches = null;
            if(getAuthOwner('staff_id') == 0){
                $branches = Branch::getStaffBranchesSuspended('list', null, true);
            }else{
                $branches = Branch::getStaffBranchesSuspended();
            }

            if($branches->count() > 1){
                $filters = $filters + [
                    'branches[]' => [
                        'param_name' => 'branches',
                        'label' => '',
                        'after' => '</div>',
                        'before' => '<div class="form-group">',
                        'attributes' => [
                            'placeholder' => __t('Select') . ' ' . __t('Branches'),
                            'id' => 'branchSelect',
                            'multiple' => 'multiple'
                        ],
                        'div' => 'col-md-4',
                        'inputClass' => 'select-filter form-control',
                        'options' => $branches,
                        'type' => 'select',
                        'filter_options' => [
                            'table' => 'staffs',
                            'operation' => FilterOperations::IN_FILTER_OPERATION,
                            'field' => 'branch_id',
                        ]
                    ],
                ];
            }
        }

        if(Plugins::pluginActive(PluginUtil::HRM_PLUGIN))
        {
            $filters = $filters +
            [
                'types' => [
                    'label' => '',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Employment Type'),
                        'id' => 'typeSelect'
                    ],
                    'div' => 'col-md-4 form-group',
                    'inputClass' => 'select-filter form-control',
                    'options' => $types->toArray(),
                    'type' => 'select',
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'employment_type',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'employment_type_id'
                    ]
                ],
                'levels' => [
                    'label' => '',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Employment Level'),
                        'id' => 'levelSelect'
                    ],
                    'div' => 'col-md-4 form-group',
                    'inputClass' => 'select-filter form-control',
                    'options' => $levels->toArray(),
                    'type' => 'select',
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'employee_level',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'employment_level_id'
                    ]
                ],
                'depts' => [
                    'label' => '',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Department'),
                        'id' => 'deptSelect'
                    ],
                    'div' => 'col-md-4 form-group',
                    'inputClass' => 'select-filter form-control',
                    'options' => $depts->toArray(),
                    'type' => 'select',
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'department',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'department_id'
                    ]
                ],
                'designations' => [
                    'label' => '',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Designation'),
                        'id' => 'designationSelect'
                    ],
                    'div' => 'col-md-4 form-group',
                    'inputClass' => 'select-filter form-control',
                    'options' => $designations->toArray(),
                    'type' => 'select',
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'designation',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'designation_id'
                    ]
                    ]
            ];

            if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)){
                $filters = $filters + [
                        'attendance_restriction_id' => [
                            'param_name' => 'attendance_restriction_id',
                            'label' => '',
                            'after' => '</div>',
                            'before' => '<div class="form-group">',
                            'attributes' => [
                                'placeholder' => __t('Select') . ' ' . __t('Attendance Restriction'),
                                'id' => 'restrictionSelect'
                            ],
                            'div' => 'col-md-4',
                            'inputClass' => 'select-filter form-control',
                            'options' => $attendanceRestrictionsList,
                            'type' => 'select',
                            'filter_options' => [
                                'table' => 'staffs',
                                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                                'field' => 'attendance_restriction_id',
                            ]
                        ],
                        'attendance_shift_id' => [
                            /**
                             * this field is hidden, it is only used in filtering
                             * an iframe in the attendance shift show page
                             * */
                            'param_name' => 'attendance_shift_id',
                            'label' => '',
                            'after' => '</div>',
                            'before' => '<div class="form-group">',
                            'attributes' => [
                                'placeholder' => __t('Select') . ' ' . __t('Shift'),
                                'id' => 'shiftSelect',
                                'disabled' => 'disabled',
                            ],
                            'div' => 'd-none',
                            'inputClass' => 'select-filter form-control',
                            'options' => [],
                            'type' => 'select',
                            'filter_options' => [
                                'table' => 'staff_info',
                                'model' => 'attendance_shift',
                                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                                'field' => 'attendance_shift_id'
                            ]
                        ]
                    ];
            }

        }
        if($this->hasCustomForm())
        {
            $filters += $this->customDataService->getListingFilters();
        }

        $additionalFieldsFilters = $this->additionalFieldsAwareService->getFilter(EntityKeyTypesUtil::STAFF_ENTITY_KEY) ?? [];
        $filters = array_merge($filters, $additionalFieldsFilters);

        return $filters;
    }

    function filterName($query, $allowInActive = false, bool $getSuspended = false, $selected = [])
    {

        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $staffEntityListRoute = route("owner.entity.list",["entityKey" => "staff"], false);
            $oldStaffListRoute = route("owner.staff.index",[],false);
            $referrerUrl = request()->headers->get('referer');
            $referrerPath = isset($referrerUrl) ? parse_url($referrerUrl)['path'] : "dummy_url";
            $referrerPathWithoutV2 = substr($referrerPath,3);
            $isStaffListingRoute = $referrerPathWithoutV2 == $staffEntityListRoute || $referrerPathWithoutV2 == $oldStaffListRoute;
            $authUser = getAuthOwner();
            if($authUser['staff_id'] == 0 && $isStaffListingRoute){
                return $this->repo->getAutoCompleteResult($query, $allowInActive, null, $selected);
            }else{
                $accessibleBranches = $getSuspended ? Branch::getStaffBranchesSuspended('list',$authUser['staff_id']) : Branch::getStaffBranches('list',$authUser['staff_id']);
                $accessibleBranchIds = $accessibleBranches->keys()->toArray();

                $authHasAccessToAnyStaff = count($accessibleBranchIds) > 0;

                if(!$authHasAccessToAnyStaff){
                    // should get no staff at all
                    return collect([]);
                }

                return $this->repo->getAutoCompleteResult($query, $allowInActive, $accessibleBranchIds, $selected);
            }
        }
        return $this->repo->getAutoCompleteResult($query, $allowInActive, null, $selected);
    }

    /**
     * @param string $query
     * @param int $requestTypeId
     * @return array|mixed
     */
    public function autoCompleteForRequests(string $query, int $requestTypeId)
    {
        $employees = $this->entityPermissionService->getEmployeesForPermission(EntityPermissionKeyUtil::CREATE, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, $requestTypeId);
        if (empty($employees)) {
            return [];
        }
        return $this->repo->getEmployeesAutoCompleteForRequests($query, $employees);
    }

    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $additionalFieldsEntityKey = $this->additionalFieldsAwareService->getEntityKey(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        $staffCustomData = $request[$additionalFieldsEntityKey] ?? null;
        /** handle when hr plugin is disabled */
        $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        $appEntityKeys = $additionalFormsHandler->getAllEntityKeys();

        $appsData = [];

        foreach ($appEntityKeys as $key) {
            $appsData[$key] = $request[$key] ?? null;
        }

        if ($request['citizenship_status']== StaffCitizenshipStatus::RESIDENT) {
            if ($request['residence_expiry_date']) {
                $request['residence_expiry_date'] = db_format_date($request['residence_expiry_date']);
            }
        }else {
            $request['residence_expiry_date'] =null;
            $request['nationality']=null;
        }

        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $excludedFields[] = $additionalFieldsEntityKey;
            $excludedFields[] = array_merge($excludedFields, $appEntityKeys);
            $hr = $this->addHR($request, $excludedFields);
            if ($staffCustomData) {
                $request[$additionalFieldsEntityKey] = $staffCustomData;
            }
            $this->additionalFieldsAwareService->save($request->all(), $hr->id, EntityKeyTypesUtil::STAFF_ENTITY_KEY);

            if (!empty($appsData)) {
                //re-add apps submitted data to request again after it has been removed
                foreach ($appsData as $appEntityKey => $appEntityData) {
                    $request[$appEntityKey] = $appEntityData;
                }
                $additionalFormsHandler->store($hr->id, $request->all());
            }

            return $hr;
        }
//        $this->checkCurrentStaffSiteLimit();
        $neededData = [];
        $request = $this->commonOperationBeforeSave($request, $neededData);

        $baseStaffExcludedFields = array_merge([
            'branches',
            'shift',
            'generated_code',
            'employee_picture_file',
            'employee_picture_file_url',
            'send_credentials',
            $additionalFieldsEntityKey
        ], $appEntityKeys);

        $staff = parent::add($request, $baseStaffExcludedFields);
        if ($staffCustomData) {
            $request[$additionalFieldsEntityKey] = $staffCustomData;
        }
        $this->additionalFieldsAwareService->save($request->all(), $staff->id, EntityKeyTypesUtil::STAFF_ENTITY_KEY);

        if (!empty($appsData)) {
            //re-add apps submitted data to request again after it has been removed
            foreach ($appsData as $appEntityKey => $appEntityData) {
                $request[$appEntityKey] = $appEntityData;
            }
            $additionalFormsHandler->store($staff->id, $request->all());
        }

        if ($staff) {
            return $this->commonOperationAfterSave($staff, $neededData);
        }
    }

    public function addHR($request, array $excludedFields = [])
    {
        if (!Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            return $this->add($request, $excludedFields);
        }
//        $this->checkCurrentStaffSiteLimit();

        return $this->saveHR($request,$excludedFields);
    }

    public function updateEmployeeDefaultMaximumDiscount($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $staff = $this->repo->resetCriteria()->find($id);
        // Check for system limit if the user types will changed
        if ($request['type'] != null) {
            if ($request['type'] != $staff->type) {
                SystemLimitFactory::init($request['type'])->check(getCurrentSite('id'));
            }
        }
        $neededData = [];
        $request = $this->commonOperationBeforeSave($request, $neededData);
        $isSaved = parent::update($id, $request);
        if ($isSaved) {
            return $this->commonOperationAfterSave($staff, $neededData);
        }
    }
    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $staff = $this->repo->resetCriteria()->find($id);

        if (isset($request['type']) && $request['type'] == StaffTypeUtil::EMPLOYEE) {
            $request['branches'] = [];
        }

        // Check for system limit if the user types will changed
        if ($request['type'] != null) {
            if ($request['type'] != $staff->type) {
                SystemLimitFactory::init($request['type'])->check(getCurrentSite('id'));
            }
        }

        if ($request['citizenship_status']== StaffCitizenshipStatus::RESIDENT) {
            if ($request['residence_expiry_date']) {
                $request['residence_expiry_date'] = db_format_date($request['residence_expiry_date']);
            }
        }else {
            $request['residence_expiry_date'] =null;
            $request['nationality']=null;
        }

        $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $this->additionalFieldsAwareService->save($request->all(), $id, EntityKeyTypesUtil::STAFF_ENTITY_KEY, true);

            $additionalFormsHandler->update($id, $request->all());

            return $this->updateHR($id, $request, $excludedFields);
        }
        $neededData = [];
        $request = $this->commonOperationBeforeSave($request, $neededData);
        $baseStaffExcludedFields = ['holiday_lists','generated_code', 'branches', 'shift', 'employee_picture_file','employee_picture_file_url','send_credentials'];
        $newBranches = $request->branches;
        $additionalCallback = function ($result,$oldData) use ($id,$callback, $newBranches){
            $this->checkToForceLogOut($result, $oldData, $newBranches);
            if($callback){
                $callback($result,$oldData);
            }
        };
        $this->additionalFieldsAwareService->save($request->all(), $id, EntityKeyTypesUtil::STAFF_ENTITY_KEY, true);
        $additionalFormsHandler->update($id, $request->all());

        $additionalFieldsEntityKey = $this->additionalFieldsAwareService->getEntityKey(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        $customData = $request[$additionalFieldsEntityKey] ?? null;
        if ($customData) {
            $baseStaffExcludedFields[] = $additionalFieldsEntityKey;
        }

        $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        $appEntityKeys = $additionalFormsHandler->getAllEntityKeys();

        if (!empty($appEntityKeys)) {
            $baseStaffExcludedFields = array_merge($baseStaffExcludedFields, $appEntityKeys);
        }

        $isSaved = parent::update($id, $request, $baseStaffExcludedFields,$additionalCallback);
        if ($isSaved) {
            return $this->commonOperationAfterSave($staff, $neededData);
        }
    }


    public function basicUpdate($id, $request, array $excludedFields = [], Closure $callback = null){
       return  parent::update($id, $request,  $excludedFields, $callback );
    }

    /**
     * Update official IDs for multiple staff members
     *
     * @param array $staffsMappingData Array of staff data with employee_id and official_id
     * @return array Updated staff data
     */
    public function updateStaffOfficialIds(array $staffsMappingData): array
    {
        $updatedStaff = [];
        $staffIds = array_column($staffsMappingData, 'employee_id');
        $officialIds = array_column($staffsMappingData, 'official_id');


        $staffs = $this->repo->findWhereIn('id',$staffIds);
        $existingStaffWithOfficialIds = $this->repo->findWhereIn('official_id',$officialIds);

        foreach ($existingStaffWithOfficialIds as $existingStaff) {
            $staffData = $existingStaff->toArray();
            $staffData['official_id'] = null; // Clear the official ID

            unset($staffData['path']);
            $updateRequest = new DefaultRequest($staffData, []);
            $this->basicUpdate($existingStaff->id, $updateRequest);
        }

        foreach($staffsMappingData as $staffData) {
            $staff = $staffs->where('id',$staffData['employee_id'])->first();
            if (!$staff) continue;

            $staff = $staff->toArray();
            $staff['official_id'] = $staffData['official_id'];

            // Remove path to avoid issues with the update
            unset($staff['path']);

            $updateRequest = new DefaultRequest($staff, []);
            $this->basicUpdate($staffData['employee_id'], $updateRequest);

            $updatedStaff[] = [
                'employee_id' => $staffData['employee_id'],
                'official_id' => $staffData['official_id'],
            ];
        }
        return $updatedStaff;
    }


    public function updateHR($id, $request, array $excludedFields = [])
    {
        if (!Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $newBranches = $request->branches;
            $callback = function ($result,$oldData) use($id, $newBranches) {
                $this->checkToForceLogOut($result, $oldData, $newBranches);
            };
            return $this->update($id,$request, $excludedFields,$callback);
        }
        return $this->saveHR($request,$excludedFields,$id);


    }
    protected function saveHR($request, array $excludedFields = [],$id=null){
        $neededData = [];
        $request = $this->commonOperationBeforeSave($request, $neededData);
        $staffInfo = [
            'birth_date' => $request['birth_date'],
            'gender' => $request['gender'],
            'personal_email' => $request['personal_email'],
            'permanent_address1' => $request['permanent_address1'],
            'permanent_address2' => $request['permanent_address2'],
            'permanent_state' => $request['permanent_state'],
            'permanent_city' => $request['permanent_city'],
            'permanent_postal_code' => $request['permanent_postal_code'],
            'designation_id' => $request['designation_id'],
            'department_id' => $request['department_id'],
            'employment_type_id' => $request['employment_type_id'],
            'employment_level_id' => $request['employment_level_id'],
        ];
        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            $staffInfo['direct_manager_id'] = $request['direct_manager_id'];
        }
        $staffJob = [
            'join_date' => $request['join_date']
        ];


        // todo: refactor this snippet of code with new activity log class for attributes
        // log staff information data in update
        if ($id) {
            $oldStaff = $this->find($id);
            $oldStaffInfo = $oldStaff->staff_info;
            $oldStaffJob = $oldStaff->staff_job;
            // log holiday list
            if (Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
                $oldStaffHolidays = $oldStaff->holiday_lists;
                $oldHolidayList = $oldStaffHolidays ? $oldStaffHolidays->pluck('name', 'id')->toArray() : [];
                $oldHolidayList = $oldHolidayList ? implode(', ', $oldHolidayList) : '';
                $newHolidayList = $request['holiday_lists'];
                if ($newHolidayList) {
                    sort($newHolidayList);
                }
                $newHolidayList = $this->holidayListRepository->find($newHolidayList);
                $newHolidayList = $newHolidayList ? $newHolidayList->pluck('name', 'id')->toArray() : [];
                $newHolidayList = $newHolidayList ? implode(', ', $newHolidayList) : '';
                $this->appendedActivityLogNewData['Holiday List'] = $newHolidayList;
                $this->appendedActivityLogOldData['Holiday List'] = $oldHolidayList;
            }
            // log shift list
            $oldStaffShifts = $oldStaff->shift;
            $oldShiftList = $oldStaffShifts ? $oldStaffShifts->pluck('name', 'id')->toArray() : [];
            $oldShiftList = $oldShiftList ? implode(', ', $oldShiftList) : '';
            $newShiftList = $request['shift'];
            if ($newShiftList) {
                sort($newShiftList);
            }
            $newShiftList = $this->shiftRepository->find($newShiftList);
            $newShiftList = $newShiftList ? $newShiftList->pluck('name', 'id')->toArray() : [];
            $newShiftList = $newShiftList ? implode(', ', $newShiftList) : '';
            $this->appendedActivityLogNewData['Booking Shift'] = $newShiftList;
            $this->appendedActivityLogOldData['Booking Shift'] = $oldShiftList;
            // log accessible branches list
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $oldStaffBranches = $oldStaff->branches;
                $oldBranchList = $oldStaffBranches ? $oldStaffBranches->pluck('name', 'id')->toArray() : [];
                $oldBranchList = $oldBranchList ? implode(', ', $oldBranchList) : '';
                $newBranchList = $request['branches'];
                if ($newBranchList) {
                    sort($newBranchList);
                }
                $newBranchList = $this->branchesRepo->find($newBranchList);
                $newBranchList = $newBranchList ? $newBranchList->pluck('name', 'id')->toArray() : [];
                $newBranchList = $newBranchList ? implode(', ', $newBranchList) : '';
                $this->appendedActivityLogNewData['Accessible Branches'] = $newBranchList;
                $this->appendedActivityLogOldData['Accessible Branches'] = $oldBranchList;
            }
            // log new data
            $this->appendedActivityLogNewData['Birth Date'] = $request['birth_date'];
            $this->appendedActivityLogNewData['Gender'] = $request['gender'];
            $this->appendedActivityLogNewData['Personal Email'] = $request['personal_email'];
            $this->appendedActivityLogNewData['Address'] = $request['permanent_address1'];
            $this->appendedActivityLogNewData['Another Address'] = $request['permanent_address2'];
            $this->appendedActivityLogNewData['State'] = $request['permanent_state'];
            $this->appendedActivityLogNewData['City'] = $request['permanent_city'];
            $this->appendedActivityLogNewData['Postal Code'] = $request['permanent_postal_code'];
            $this->appendedActivityLogNewData['Joining Date'] = $request['join_date'];
            if (!empty($request['fiscal_day'])) {
                $this->appendedActivityLogNewData['Fiscal Date'] = $request['fiscal_day'] . ' - ' . getMonthName($request['fiscal_month'], true);
            }
            $department = $this->deptRepo->find($request['department_id']);
            $this->appendedActivityLogNewData['Department'] = $department ? $department->name : '';
            $designation = $this->designationRepo->find($request['designation_id']);
            $this->appendedActivityLogNewData['Designation'] = $designation ? $designation->name : '';
            $employmentLevel = $this->empLevelRepo->find($request['employment_level_id']);
            $this->appendedActivityLogNewData['Employment Level'] = $employmentLevel ? $employmentLevel->name : '';
            $employmentType = $this->empTypeRepo->find($request['employment_type_id']);
            $this->appendedActivityLogNewData['Employment Type'] = $employmentType? $employmentType->name : '';
            // set new relations
            if ($department) {
                $this->appendedActivityLogRelations[] = new ActivityLogRelationRequest($department->id, EntityKeyTypesUtil::DEPARTMENT_ENTITY_KEY);
            }
            if ($designation) {
                $this->appendedActivityLogRelations[] = new ActivityLogRelationRequest($designation->id, EntityKeyTypesUtil::DESIGNATION_ENTITY_KEY);
            }
            if ($employmentLevel) {
                $this->appendedActivityLogRelations[] = new ActivityLogRelationRequest($employmentLevel->id, EntityKeyTypesUtil::EMPLOYMENT_LEVEL_ENTITY_KEY);
            }
            if ($employmentType) {
                $this->appendedActivityLogRelations[] = new ActivityLogRelationRequest($employmentType->id, EntityKeyTypesUtil::EMPLOYMENT_TYPE_ENTITY_KEY);
            }
            // must be set to detect the difference
            if ($oldStaffInfo) {
                $this->appendedActivityLogOldData['Birth Date'] = formatForView($oldStaffInfo['birth_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
                $this->appendedActivityLogOldData['Gender'] = $oldStaffInfo['gender'];
                $this->appendedActivityLogOldData['Personal Email'] = $oldStaffInfo['personal_email'];
                $this->appendedActivityLogOldData['Address'] = $oldStaffInfo['permanent_address1'];
                $this->appendedActivityLogOldData['Another Address'] = $oldStaffInfo['permanent_address2'];
                $this->appendedActivityLogOldData['State'] = $oldStaffInfo['permanent_state'];
                $this->appendedActivityLogOldData['City'] = $oldStaffInfo['permanent_city'];
                $this->appendedActivityLogOldData['Postal Code'] = $oldStaffInfo['permanent_postal_code'];
                $this->appendedActivityLogOldData['Department'] = $oldStaffInfo->department ? $oldStaffInfo->department->name: '';
                $this->appendedActivityLogOldData['Designation'] = $oldStaffInfo->designation ?$oldStaffInfo->designation->name: '';
                $this->appendedActivityLogOldData['Employment Level'] = $oldStaffInfo->employmentLevel ?$oldStaffInfo->employmentLevel->name: '';
                $this->appendedActivityLogOldData['Employment Type'] = $oldStaffInfo->employmentType ?$oldStaffInfo->employmentType->name: '';
            }
            if ($oldStaffJob) {
                $this->appendedActivityLogOldData['Joining Date'] = formatForView($oldStaffJob['join_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
                $this->appendedActivityLogOldData['Fiscal Date'] = $oldStaffJob['fiscal_day'] . ' - ' . getMonthName($oldStaffJob['fiscal_month'], true);
                if ($request['fiscal-period-value'] != 'custom-fiscal') {
                    $this->appendedActivityLogNewData['Fiscal Date'] =
                        Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN,'fiscal_date_day') . ' - ' . getMonthName(Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN,'fiscal_date_month'), true);
                }
            }
        }

        if (Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            $staffInfo += [
                'attendance_shift_id' => $request['attendance_shift_id'],
                'leave_policy_id' => $request['leave_policy_id'],
                'has_secondary_shift' => 0,
                'secondary_shift_id' => null
            ];

            $multiple_shift_settings = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift');
            if ($multiple_shift_settings) {
                $has_secondary_shift = isset($request['has_secondary_shift'])? 1 : 0;
                $staffInfo['has_secondary_shift'] = $has_secondary_shift;
                $staffInfo['secondary_shift_id'] = isset($request['secondary_shift_id'])? $request['secondary_shift_id'] : null;
            }

            // log staff shift information in case of update
            if ($id) {
                /** Keep Old Data When Settings is Closed While Edit Employee **/
                if (!$multiple_shift_settings) {
	                $staffInfo['has_secondary_shift'] = $oldStaffInfo->has_secondary_shift ?? 0;
	                $staffInfo['secondary_shift_id'] = $oldStaffInfo->secondary_shift_id ?? null;
                }

                $leavePolicy = $this->leavePolicyRepository->find($request['leave_policy_id']);
                $this->appendedActivityLogNewData['Leave Policy'] = $leavePolicy ? $leavePolicy->name : '';
                if ($leavePolicy) {
                    $this->appendedActivityLogRelations[] = new ActivityLogRelationRequest($leavePolicy->id, EntityKeyTypesUtil::LEAVE_POLICY_ENTITY_KEY);
                }

                $shift = $this->shiftRepository->find($request['attendance_shift_id']);
                $this->appendedActivityLogNewData['Attendance Shift'] = $shift ? $shift->name : '';
                if ($shift) {
                    $this->appendedActivityLogRelations[] = new ActivityLogRelationRequest($shift->id, EntityKeyTypesUtil::SHIFT);
                }

                if (Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift')) {
                    $this->appendedActivityLogNewData['Enable the Secondary Attendance Shift'] = isset($request['has_secondary_shift'])? 1 : 0;
                    $secondaryShift = $this->shiftRepository->find($request['secondary_shift_id']);
                    $this->appendedActivityLogNewData['Secondary Attendance Shift'] = $secondaryShift ? $secondaryShift->name : '';
                }

                // must be set to detect the difference
                $this->appendedActivityLogOldData['Leave Policy'] = '';
                if (isset($oldStaffInfo)) {
                    $this->appendedActivityLogOldData['Attendance Shift'] = $oldStaffInfo->attendanceShift ? $oldStaffInfo->attendanceShift->name: '';
                    if (Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift')) {
                        $this->appendedActivityLogOldData['Enable the Secondary Attendance Shift'] = $oldStaffInfo->has_secondary_shift;
                        $this->appendedActivityLogOldData['Secondary Attendance Shift'] = $oldStaffInfo->secondaryShift ? $oldStaffInfo->secondaryShift->name: '';
                    }
                    $this->appendedActivityLogOldData['Leave Policy'] = $oldStaffInfo->leavePolicy ? $oldStaffInfo->leavePolicy->name: '';
                }
            }
        }
        if ($request['fiscal-period-value'] == 'custom-fiscal'){
            $staffJob += ['fiscal_type' => $request['fiscal-period-value'], 'fiscal_day' => $request['fiscal_day'], 'fiscal_month' => $request['fiscal_month']];
        }else{
            $staffJob += ['fiscal_type' => $request['fiscal-period-value'],
             'fiscal_day' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN,'fiscal_date_day'),
             'fiscal_month' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN,'fiscal_date_month')];
        }

        $additionalFieldsEntityKey = $this->additionalFieldsAwareService->getEntityKey(EntityKeyTypesUtil::STAFF_ENTITY_KEY);

        $additionalFormsHandler = new AdditionalFormsHandler(EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        $appEntityKeys = $additionalFormsHandler->getAllEntityKeys();

        $baseStaffExcludedFields = array_merge([
            'branches',
            'generated_code', 'shift',
            'holiday_lists',
            'employee_picture_file',
            'employee_picture_file_url',
            'send_credentials',
            'fiscal-period-value', $additionalFieldsEntityKey
        ], array_keys($staffInfo), array_keys($staffJob), $appEntityKeys);

        if($id === null){
            $staff = parent::add($request, $baseStaffExcludedFields);
            $id = $staff->id;
            if($id !== null)
                $isSaved = true;
        }else{
            $newBranches = $request->branches;

            $callback = function ($result,$oldData) use ($id, $newBranches) {
                $this->checkToForceLogOut($result, $oldData, $newBranches);
            };

            $isSaved = parent::update($id, $request, $baseStaffExcludedFields,$callback);
        }
        if ($isSaved) {
            $staff = $this->repo->resetCriteria()->find($id);
            //Format Dates
            if ($staffInfo['birth_date']) {
                $staffInfo['birth_date'] = db_format_date($staffInfo['birth_date']);
            }
            if ($staffJob['join_date']) {
                $staffJob['join_date'] = db_format_date($staffJob['join_date']);
            }
            $this->repo->saveStaffInfo($staff, $staffInfo);
            $this->repo->saveStaffJob($staff, $staffJob);
            return $this->commonOperationAfterSave($staff, $neededData);
        }
    }

    private function checkToForceLogOut($result, $oldData, $newBranches)
    {
        if($result->role_id == Staff::OWNER_ROLE_ID){
            return;
        }

        $oldBranches = [];

        if (!Plugins::pluginActive(PluginUtil::BranchesPlugin)) $oldData->branches = [];

        foreach ($oldData->branches as $branch) {
            $oldBranches[] = (string) $branch->id;
        }

        sort($oldBranches);
        if (is_array($newBranches)) {
            sort($newBranches);
        }

        if ($result->role_id != $oldData->role_id ||
            $result->branch_id != $oldData->branch_id ||
            $oldBranches != $newBranches
        ) {
            $this->repo->incrementAuthId($oldData->id);
        }
    }

    /**
     * @param int $id
     * @param Closure|null $callback
     * @return bool|mixed
     * @throws StaffAssignedToContract
     * @throws StaffAssignedToLoan
     * @throws StaffIsDepartmentManager
     * @throws StaffIsDirectManagerToStaff
     * @throws StaffIsInLeaveApplicationApprovalSettings
     */
    public function delete($id, Closure $callback = null)
    {
        $user = $this->repo->find($id);

        if (isset($user['role_id']) && ($user['role_id'] == -1) ) {
            throw new Exception("Cannot delete owner user");
        }

        if (Plugins::pluginInstalled(PluginUtil::HRM_PAYROLL_PLUGIN) && $this->repo->getContractsCount($id)) {
            throw new StaffAssignedToContract;
        }
        if (Plugins::pluginInstalled(PluginUtil::HRM_PLUGIN)) {
            $department = $this->repo->isManager($id);
            if ($department  !== null) {
                throw new StaffIsDepartmentManager("<a href='".route('owner.departments.show', ['department' => $department->id])."'>#".$department->id."</a>", $department->name);
            }
        }
        if (Plugins::pluginInstalled(PluginUtil::HRM_PAYROLL_PLUGIN) && $this->repo->hasLoans($id)) {
            throw new StaffAssignedToLoan;
        }

        if (Plugins::pluginInstalled(PluginUtil::HRM_PLUGIN)) {
            $staffInfo = $this->repo->isDirectManager($id);
            if ($staffInfo  !== null) {
                throw new StaffIsDirectManagerToStaff("<a href='".route('owner.staff.show',['staff' => $staffInfo->id])."'>#".$staffInfo->id."</a>", $staffInfo->full_name);
            }
        }

        if (Plugins::pluginInstalled(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            if(Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_can_approve')){
                $employeesIds = explode(",", Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_that_can_approve_ids'));
                if(in_array($id, $employeesIds)){
                    throw new StaffIsInLeaveApplicationApprovalSettings;
                }
            }
            $configurations = $this->repo->hasAssignedAsApproverInConfigurationCycle($id);
            if($configurations){
                throw new StaffIsInConfigurationApprovalLevel($configurations);
            }
        }

        $assignedToClient = $this->repo->isAssignedToClient($id);
        if(!empty($assignedToClient)){
            throw new StaffIsAssignedToClient($assignedToClient->item_id , $assignedToClient->staff->name);
        }

        $additionalCallback = function ($oldData) use($id,$callback){
            $this->repo->incrementAuthId($id);
            if($callback){
                $callback($oldData);
            }
        };
        if (parent::delete($id,$additionalCallback)) {
            setLastUpdatedAt(PluginUtil::StaffPlugin, SettingsUtil::STAFFS_LAST_UPDATED_AT);
            $this->updateCurrentStaffSiteLimit($user);
            dispatch_event_action(new EmployeeDeletedEvent(json_decode($user)));
            return true;
        }
    }

    private function commonOperationBeforeSave($request, &$neededData)
    {
        $currentStaffID = getAuthOwner('staff_id');
        $generatedCode = $request['generated_code'];
        $code = $request['code'];

        if (!isset($request['employee_picture_file_url'])){
            $request['photo'] = null;
        }elseif ($request['employee_picture_file']) {
            $fileName = Uuid::uuid4()->toString() . '.' . $request->employee_picture_file->extension();
            $filePath = FilePathUtil::staffProfilePicPath();
            $request->file('employee_picture_file')->storeAs($filePath, $fileName);
            unset($request->files['employee_picture_file']);
            $request['photo'] = $filePath . '/' . $fileName;
        }

        if (isset($request['branches'])) {
            $neededData['branches'] = $request['branches'];
        }

        if (isset($request['shift'])) {
            $neededData['shift'] = $request['shift'];
        }

        if (isset($request['holiday_lists'])) {
            $neededData['holiday_lists'] = $request['holiday_lists'];
        }

        if($generatedCode === $code)
        {
            //increment autonumber when the user didnt change the code
            AutoNumber::update_auto_serial(AutoNumber::TYPE_STAFF);
        }else{
            //reset the autonumber to be the biggest of the generated code or the user entered code
            AutoNumber::update_last_from_number($code, AutoNumber::TYPE_STAFF);
        }

        $neededData['send_credentials'] = $request['send_credentials'] ?? false;
        $request['added_by'] = $currentStaffID;
        $request['full_name'] = $request['name'] . (isset($request['middle_name']) ? ' ' . $request['middle_name'] : '') . (isset($request['last_name']) ? ' ' . $request['last_name'] : '');
        return $request;
    }

    private function commonOperationAfterSave($staff, $neededData)
    {
        $currentStaffID = getAuthOwner('staff_id');
        $attachItem = ['assigned_by' => $currentStaffID, 'created' => date("Y-m-d H:i:s"), 'modified' => date("Y-m-d H:i:s")];

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            if (isset($neededData['branches'])) {
                $branches = $neededData['branches'];
                $attachArray = [];
                foreach ($branches as $branch) {
                    $attachArray[$branch] = $attachItem;
                }
                $this->repo->sync($staff, 'branches', $attachArray);
            } else {
                //$this->repo->sync($staff, 'branches', []);
            }
        }

        if (Plugins::pluginActive(PluginUtil::BookingPlugin) || Plugins::pluginActive(PluginUtil::NEW_BOOKING_PLUGIN)){
            if (isset($neededData['shift'])) {
                $shifts = $neededData['shift'];
                $attachArray = [];
                foreach ($shifts as $shift) {
                    $attachArray[$shift] = $attachItem;
                }
                $this->repo->sync($staff, 'shift', $attachArray);
            } else {
                $this->repo->sync($staff, 'shift', []);
            }
        }

        if (Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            if (isset($neededData['holiday_lists'])) {
                $holidayLists = $neededData['holiday_lists'];
                $this->repo->sync($staff, 'holiday_lists', $holidayLists);
            } else {
                $this->repo->sync($staff, 'holiday_lists', []);
            }
            $multiCycleApprovalConfigurationRematcher = resolve(MultiCycleApprovalConfigurationRematcher::class);
            $multiCycleApprovalConfigurationRematcher->rematch(configType: MultiCycleApprovalUtil::CONFIG_TYPE_LEAVES, staffId: $staff->id);
            if (Plugins::pluginActive(PluginUtil::REQUESTS_PLUGIN)) {
                $multiCycleApprovalConfigurationRematcher->rematch(configType: MultiCycleApprovalUtil::CONFIG_TYPE_REQUEST, staffId: $staff->id);
            }
        }

        $this->updateCurrentStaffSiteLimit($staff);
        if ($neededData['send_credentials']) {
            $this->dispatchSendLoginDetailsJob($staff->id);
        }

        setLastUpdatedAt(PluginUtil::StaffPlugin, SettingsUtil::STAFFS_LAST_UPDATED_AT);
        event(new StaffSavedEvent($staff));
        return $staff;
    }

    public function sendLoginData($staffID, string $url = null)
    {
        $guzzleAdapter = new InternalHttpAdapter();
        $guzzleAdapter->sendLoginData($staffID,$url);
    }

    /**
     * @param int $id
     * @return void
     */
    public function dispatchSendLoginDetailsJob(int $id): void
    {
        dispatch(new SendLoginDetails($id));
    }

    public function updateCurrentStaffSiteLimit(Staff $staff)
    {
        $subscriptionService = App::make('App\Services\SubscriptionService');
        $subscriptionService->update_current_staff_count($staff);
    }

    public function checkCurrentStaffSiteLimit()
    {
        $subscriptionService = App::make('App\Services\SubscriptionService');
        return $subscriptionService->check_add_staff_limit(getCurrentSite("id"));
    }

    public function activate($id, $request)
    {
        $user = $this->repo->find($id);
        if($user->active){
            return redirect()->back()->with('danger', sprintf(__t('%s Already Activated'), $user->name.' '.$user->last_name.' #'.$user->code));
        }
        else {
            SystemLimitFactory::init($user->type)->check(getCurrentSite('id'));
            $request['active'] = 1;
            $result = parent::update($id, $request);
            $this->restoreStaffSoftDeletedShifts($user);
            $this->updateCurrentStaffSiteLimit($user);
            setLastUpdatedAt(PluginUtil::StaffPlugin, SettingsUtil::STAFFS_LAST_UPDATED_AT);
            return redirect()->back()->with('success', sprintf(__t('%s Activated Successfully'), $user->name.' '.$user->last_name.' #'.$user->code));
        }
    }

    protected $authDeptId = false;

    public function getAuthDepartmentId(){
        if(is_null($this->authDeptId) || !is_bool($this->authDeptId)) return $this->authDeptId;
        $authStaffInfo = $this->repo->getStaffInfo(getAuthOwner('staff_id'));
        $this->authDeptId = $authStaffInfo?->department_id ?? null;
        return $this->authDeptId;
    }

    private function restoreStaffSoftDeletedShifts($user){
        // handle restore soft deleted shifts when staff reactivated
        $relatedShiftsIds = [];
        $shiftService = resolve(ShiftService::class);
        $itemStaffRepository =  resolve(ItemStaffRepository::class);
        $staffSifts = $itemStaffRepository->findByTypeAndStaffId(ItemStaffUtil::SHIFT_ITEM_TYPE, $user->id);
        if($staffSifts){
            foreach($staffSifts as $shift){
                $relatedShiftsIds[] = $shift->item_id;
            }
        }
        if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN) && $user->staff_info) {
            if ($user->staff_info->attendance_shift_id) {
                $relatedShiftsIds[] = $user->staff_info->attendance_shift_id;
            }
        }
        $relatedShiftsIds = array_unique($relatedShiftsIds);
        foreach($relatedShiftsIds as $shiftId){
            $shiftService->undoDeleteShift($shiftId);
        }
    }
    public function deactivate($id, $request)
    {
        $user = $this->repo->find($id);
        if (is_null($user)) {
            throw new EntityNotFoundException(__t('User'));
        }
        if(isset($user['role_id']) && ($user['role_id'] == Staff::OWNER_ROLE_ID)){
            return redirect()->back()->with('danger', sprintf(__t('Cannot Deactivate Owner'), $user->name.' '.$user->last_name.' #'.$user->code));
        }

        if($user->active){
            if (Plugins::pluginInstalled(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
                $configurations = $this->repo->hasAssignedAsApproverInConfigurationCycle($id);
                if($configurations){
                    throw new StaffIsInConfigurationApprovalLevel($configurations);
                }
            }

            $request['active'] = 0;
            $callback = function ($result, $oldData) use ($id) {
                $this->repo->incrementAuthId($id);
            };
            $result = parent::update($id, $request,[],$callback);
            $this->updateCurrentStaffSiteLimit($user);
            setLastUpdatedAt(PluginUtil::StaffPlugin, SettingsUtil::STAFFS_LAST_UPDATED_AT);
            return redirect()->back()->with('success', sprintf(__t('%s Deactivated Successfully'), $user->name.' '.$user->last_name.' #'.$user->code));
        }
        else {
            return redirect()->back()->with('danger', sprintf(__t('%s Already Deactivated'), $user->name.' '.$user->last_name.' #'.$user->code));
        }
    }

    /**
     * @return StaffRepository
     */
    public function getRepo(): StaffRepository
    {
        return $this->repo;
    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['role_id'] = $record->role ? $record->role->name : '';
        if ($record->language_code) {
            $data['language_code'] = $this->languageRepository->find($record->language_code)->name;
        }
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && $record->branch) {
            $data['branch_id'] = $record->branch->name;

        }
        if ($record->country) {
            $data['country_code'] = $record->country ? $record->country->country : '';
        }

        if ($record->nationality) {
            $data['nationality'] = $record->staff_nationality ? $record->staff_nationality->country : '';
        }
        if($record->attendance_restriction_id){
            $data[__t("Attendance Restriction")] = $record->attendanceRestriction->name ?? '';
            unset($data['attendance_restriction_id']);
        }

        $data['Expiry Date of Residence'] =formatForView($record->residence_expiry_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        unset($data['residence_expiry_date']);
        $data['Citizenship Status'] = $record->citizenship_status ;
        unset($data['citizenship_status']);
        $data['smtp_email_address_id'] = $record->smtpEmailAddress ? $record->smtpEmailAddress->name : '';
        return $data;
    }

    /**
     * @param AttendanceCalculationCriteria $criteria
     * @param bool $allowInActive
     * @return array
     */
    public function getAttendanceDayCriteriaStaff(AttendanceCalculationCriteria $criteria, $allowInActive = false)
    {
        $staff = $this->repo->getAttendanceDayCriteriaStaff($criteria, $allowInActive);
        return $staff;
    }
    // TODO: add Fiscal Date method (employeeId, date)

    /**
     * @param $type
     *
     * @throws StaffTypeInvalid
     */
    public function checkStaffType(string $type = null): void
    {
        if (is_null($type)) {
            throw new StaffTypeInvalid();
        }

        if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
            if (!in_array($type, [StaffTypeUtil::USER, StaffTypeUtil::EMPLOYEE])) {
                throw new StaffTypeInvalid();
            }
        } else {
            if (!in_array($type, [StaffTypeUtil::USER])) {
                throw new StaffTypeInvalid();
            }
        }
    }

    function getWithRelations()
    {
        if (Schema::connection('currentSite')->hasTable('le_custom_data_rental_staff')) {
            return ['additionalFields'];
        }
        return [];
    }

    /**
     * returns array of users ids that the authenticated user can list or manage
     *
     * @param  mixed $userId
     * @return array
     */
    function getAuthUserLeaveApplicationRelatedUsers($userId) : array
    {
        $staffIds = [];
        if ($this->authUserLeaveApplicationRelatedUsers){
            return $this->authUserLeaveApplicationRelatedUsers;
        }
        $staffRepository = resolve(StaffRepository::class);
        if(!Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION)){
            return [];
        }
        $direct_managers_can_approve = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'direct_managers_can_approve');
        $assigned_department_managers_can_approve  = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'assigned_department_managers_can_approve');
        $employees_can_approve = Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_can_approve');

        if( $employees_can_approve ){
            $empoyeesThatCanApprove= Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_that_can_approve_ids');
            $employeesThatCanApproveArray = explode(',', $empoyeesThatCanApprove);
            if(in_array($userId , $employeesThatCanApproveArray)){
                return $staffRepository->getAllStaffIds();
            }
        }
        if($direct_managers_can_approve){
            $directManagedStaff = $staffRepository->directManagedStaffbyAuthUser($userId);
            $directManagedStaffIds = $directManagedStaff->pluck('id')->toArray();
            $staffIds = array_merge($staffIds, $directManagedStaffIds);
        }
        if($assigned_department_managers_can_approve){
            $staffDepartmentManagedByUsers = $staffRepository->getStaffByDepartmentManagerId($userId);
            $staffDepartmentManagedByUsersIds = $staffDepartmentManagedByUsers->pluck('id')->toArray();
            $staffIds = array_merge($staffIds, $staffDepartmentManagedByUsersIds);
        }
        $this->authUserLeaveApplicationRelatedUsers = $staffIds;
        return $staffIds;
    }

    public function getStaffCount()
    {
        return $this->repo->getAllStaffIds();
    }

    public function getAccessibleStaffIds($staffId = null, bool $getSuspended = false, $getMap = false) : array {
        $branches = $getSuspended ? Branch::getStaffBranchesSuspended('list',$staffId) : Branch::getStaffBranches('list',$staffId);
        $result = $this->repo->getStaffIdsByWorkingBranchesIds( is_array($branches) ? array_keys($branches) : $branches->keys()->toArray());
        if(!$getMap){
            return $result;
        }
        $map = array();
        foreach ($result as $row) {
            $map[$row] = $row;
        }
        return $map;
    }

    public function getAccessibleStaffIdsFilterMeta(string $field_name = "id") : ?FilterMeta {
        $authUser = getAuthOwner();

        if (!Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            return null;
        }

        $staffIds = $this->getAccessibleStaffIds($authUser['staff_id'],true);

        return new FilterMeta($field_name ,'in',$staffIds);
    }

    private $cachedAccessableStaffMap;
    private $cachedAccessableStaffMapSuspended;

    public function isAccessableStaffId($staffId, bool $includeSuspendedBranches = true): bool
    {
        if (!Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            return true;
        }

        if(isOwner()){
            return true;
        }
        if($includeSuspendedBranches){
            return $this->isAccessableStaffIdSuspended($staffId);
        }
        $branchesArray = Branch::getStaffBranches('list')->keys()->toArray();
        if(isset($this->cachedAccessableStaffMap)){
            return isset($this->cachedAccessableStaffMap[$staffId]);
        }
        $this->cachedAccessableStaffMap = $this->getAccessibleStaffByBranchKey($branchesArray) + $this->getAccessibleStaffByBranches($branchesArray);

        return isset($this->cachedAccessableStaffMap[$staffId]);
    }

    private function isAccessableStaffIdSuspended($staffId) : bool {
        if (!Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            return true;
        }
        $branchesArray = Branch::getStaffBranchesSuspended('list')->keys()->toArray();
        if(isset($this->cachedAccessableStaffMapSuspended)){
            return isset($this->cachedAccessableStaffMapSuspended[$staffId]);
        }
        $this->cachedAccessableStaffMapSuspended =
            $this->getAccessibleStaffByBranchKey($branchesArray)
            + $this->getAccessibleStaffByBranches($branchesArray);
        return isset($this->cachedAccessableStaffMapSuspended[$staffId]);
    }

    function ownerBybassIsAccessibleStaffId($staffId, bool $includeSuspendedBranches = true) {
        if(getAuthOwner("staff_id") == 0){
            return true;
        }
        return $this->isAccessableStaffId($staffId, $includeSuspendedBranches);
    }

    function checkStaffAccessable(\Illuminate\Http\Request | \App\Requests\DefaultRequest $request,string $field_name)
    {
        $staff_id = $request->get($field_name);
        if (isset($staff_id) && !$this->isAccessableStaffId($staff_id,false)) {
            throw \Illuminate\Validation\ValidationException::withMessages([
                $field_name => sprintf(__t('You do not have access to this %s',true),__t('Record',true))
            ]);
        }
    }

    public function getIdsByCriteriaSelection(
        $criteria, $input_staff_ids, $branch_id,
        $department_id, $designation_id, $payroll_frequency,
        string $currency = null, $excludedEmployeeIds = []
    ) : array {
        $payRunRepository = resolve(PayRunRepository::class);

        $employee_ids = [];
        if ($criteria == PayrunCriteriaUtil::EMPLOYEE_SELECTION) {
            $employee_ids = $input_staff_ids;
        } else {
            $employee_ids = $payRunRepository->getIdsByRuleSelection($branch_id, $department_id, $designation_id, $payroll_frequency,$currency);
        }
        $authUser = getAuthOwner();
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $accessableStaffMap = $this->getAccessibleStaffIds($authUser['staff_id'],false,true);
            $employee_ids = array_filter($employee_ids, fn($staff) => isset($accessableStaffMap[$staff]));
        }
        if(!empty($excludedEmployeeIds)){
            $employee_ids = array_diff($employee_ids, $excludedEmployeeIds);
        }

        return $employee_ids;
    }

    public function getStaffByIds(array $staffIds) : Collection {
        return $this->repo->getStaffByIds($staffIds)->map(function ($staff){
            $staff->img = AvatarURLGenerator::generate($staff->name, $staff->id, 30, $staff->photo);
            $staff->text = "#$staff->code $staff->name $staff->last_name " . (!empty($staff->email_address) ? "($staff->email_address)" : "");
            return $staff;
        });
    }

    public function getDefaultStaffOptions(): Collection{
        return $this->repo->getDefaultStaffOptions()->map($this->staffOptionFormatterCallback());
    }

    public function staffOptionFormatterCallback(){
        return function ($staff){
            $item = [
                'id'=> $staff->id,
                'img'=> AvatarURLGenerator::generate($staff->name, $staff->id, 30, $staff->photo),
                'text'=> "#$staff->id $staff->name $staff->last_name " . (!empty($staff->email_address) ? "($staff->email_address)" : "")
            ];
            $item['htmlJson'] = htmlspecialchars(json_encode($item), ENT_QUOTES, 'UTF-8');
            return $item;
        };
    }


    function getHasSecondaryShift($staffId)
    {
        $hasSecondaryShift = false;
        if(Plugins::pluginActive(PluginUtil::HRM_PLUGIN)){
            $staff = $this->repo->find($staffId);
            $hasSecondaryShift = $staff->staff_info->has_secondary_shift ?? 0;
        }
        return ["has_secondary_shift" => $hasSecondaryShift];
    }

    public function getOwnerUser(){
        return $this->repo->getOwnerUser();
    }

    public function checkStaffUnCalculatedLogs($staffId)
    {
        $service = resolve(AttendanceLogAutoCalcService::class);
        $result = $service->checkStaffUnCalculatedAttendanceLogs($staffId);
        if ($result){
           return [
               "message" =>$result ,
               "action" => [
                   "text"=>  __t('Calculate Attendance'),
                   "url"=> route(('owner.attendance_days.calculateAttendanceAuto'))
               ],
           ];
        }
        return null;
    }

        public function getStaffMissingDataCheckList($staffId): array
        {
            $staff = $this->repo
                ->getWithRelations($staffId, [
                    'attendance_shift',
                    'leave_policy',
                    'attendanceLogs',
                ]);


            $checkList = [
                "title"=>sprintf(__t("Complete %s Profile"), $staff->name),
                "list"=>[]
            ];
            if (!$staff->attendance_shift->count()) {
                $checkList["list"][] = $this->generateCheckListItem($staff, 'attendanceShift');
            }
            if (!$staff->leave_policy->count()) {
                $checkList["list"][] = $this->generateCheckListItem($staff, 'policies');
            }
            if (!$staff->holiday_lists->count()) {
                $checkList["list"][] = $this->generateCheckListItem($staff, 'holidays');
            }
            if (!$staff->attendanceLogs->count()) {
                $checkList["list"][] = $this->generateCheckListItem($staff, 'attendanceLogs');
            }
            $activeStaffContract = $staff->contracts()
                ->where('status' , ContractStatusUtil::ACTIVE)
                ->first();
            if ($activeStaffContract){
                $shiftRepo = resolve(ShiftRepository::class);
                $flagsWithNoShifts = $activeStaffContract->hasAttendanceFlagComponentWhichNotContainStaffShiftIdValues();
                if ($flagsWithNoShifts){
                    $flags = $flagsWithNoShifts['attendanceFlags'];
                    $shifts = $flagsWithNoShifts['shifts'];
                    foreach($flags as $id => $flag){
                        $flagFound = false;
                        foreach ($shifts as $shift){
                            $shiftFlags = $shiftRepo->getShiftAttendanceFlagsList($shift->id)->toArray();
                            if (!in_array($id , $shiftFlags)){
                                $text = sprintf(__t('You have used the attendance flag(s) "%s" in the salary components, while they are not selected to employee shift "%s".'),$flag,$shift->name );
                                $item = [
                                    "text" => $text,
                                    "action" => [
                                        "text" => __t("Assign Now"),
                                        "url" => route("owner.attendance_flags.edit" , [$id, 'focus'=>'shifts'])
                                    ],
                                ];
                                $flagFound = true;
                                $checkList["list"][] = $item;
                                break;
                            }
                        }
                        if ($flagFound) break;
                    }
                }
            }
            return $checkList;
        }

        private function generateCheckListItem($staff, $type)
        {
            $message = match ($type) {
                'policies' => sprintf(__t("%s doesn't have a Leave Policy"), $staff->name),
                'holidays' => sprintf(__t("%s doesn't have a Holiday List"), $staff->name),
                'attendanceLogs' => sprintf(__t("%s doesn't have Attendance Logs ,"), $staff->name),
                'contracts' => sprintf(__t("%s doesn't have a Contract ,"), $staff->name),
                default => sprintf(__t("%s doesn't have a Work Shift"), $staff->name)
            };
            $link = match ($type) {
                'policies' => route('owner.staff.edit' , ['staff'=>$staff->id ,'focus'=>'leave_policies']),
                'holidays' =>route('owner.staff.edit' , ['staff'=>$staff->id ,'focus'=>'holidays_list']),
                'attendanceLogs' => route('owner.attendance-mobile-app' , ['staff'=>$staff->id]),
                'contracts' => route('owner.contracts.create' , ['staff'=>$staff->id]),
                default => route('owner.staff.edit' , ['staff'=>$staff->id ,'focus'=>'attendance_shift'])
            };
            $text = match ($type){
                'attendanceLogs' =>__t("Setup Attendance Mobile App"),
                'contracts' =>sprintf(__t("Add New %s") , __t("Contract")),
                default => __t("Assign Now")
            };
            return [
                "text" => $message,
                "action" => [
                    "text" => $text,
                    "url" => $link
                ],
            ];
        }


    public function getStaffShiftsAndPolicies($staffId)
    {
        $shiftsAndPolicies = [];
        $staff = $this->repo->getStaffShiftsAndPolicies($staffId);
        if ($staff->staff_info?->attendanceShift) {
            $shiftsAndPolicies[] = [
                "title" => __t("Attendance Shift"),
                "name" => $staff->staff_info->attendanceShift->name,
                "link" => route("owner.shifts.show",$staff->staff_info->attendanceShift->id),
            ];
        }
        if ($staff->staff_info?->leavePolicy) {
            $shiftsAndPolicies[] = [
                "title" => __t("Leave Policy"),
                "name" => $staff->staff_info?->leavePolicy->name,
                "link" => route("owner.leave_policies.show" ,$staff->staff_info?->leavePolicy->id),
            ];
        }
         if ($staff->attendanceRestriction) {
             $shiftsAndPolicies[] = [
                 "title" => __t("Attendance Restriction"),
                 "name" => $staff->attendanceRestriction->name,
                 "link" => route("owner.attendance_restrictions.show" ,$staff->attendanceRestriction->id ),
             ];
         }
        if ($staff->staff_info?->secondaryShift) {
            $shiftsAndPolicies[] = [
                "title" => __t("Secondary Attendance Shift"),
                "name" => $staff->staff_info->secondaryShift->name,
                "link" => route("owner.shifts.show",$staff->staff_info->secondaryShift->id),
            ];
        }
        if ($staff->holiday_lists->count()) {
            $shiftsAndPolicies[] = [
                "title" => __t("Holiday Lists"),
                "name" => $staff->holiday_lists->first()->name,
                "link" => route("owner.holiday_lists.show",$staff->holiday_lists->first()->id),
            ];
        }
    return $shiftsAndPolicies;

    }
        public function getIdsByMultipleCriteriaSelection(
            $criteriaType, $staffIds = [], array $branches = [],
            array $departments = [], array $designations = [],  $payrollFrequencies = [],
             $currency = null, $excludedEmployeeIds = []
        ) : array {
            $employee_ids = [];

            if ($criteriaType == PayrunCriteriaUtil::EMPLOYEE_SELECTION) {
                $employee_ids = $staffIds;
            } else {
                $employee_ids = $this->payRunRepository->getIdsByMultipleRuleSelection($branches, $departments, $designations, $payrollFrequencies,$currency);
            }
            $authUser = getAuthOwner();
            if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
                $accessableStaffMap = $this->getAccessibleStaffIds($authUser['staff_id'],false,true);
                $employee_ids = array_filter($employee_ids, fn($staff) => isset($accessableStaffMap[$staff]));
            }

            if(!empty($excludedEmployeeIds)){
                $employee_ids = array_diff($employee_ids, $excludedEmployeeIds);
            }

            return $employee_ids;
        }

    public function getAttendanceDayMultipleCriteriaStaff(AttendanceCalculationCriteria $criteria, $allowInActive = false)
    {
        return $this->repo->getAttendanceDayMultipleCriteriaStaff($criteria, $allowInActive);
    }

    public static function getImportingValidationRules()
    {
        return[
            'staff_id' => ["required", 'exists:currentSite.staffs,id,active,1'],
        ];
    }

    public static function getImportingValidationMessages()
    {
        return[
            'staff_id.required' => __t("You should enter an employee that has already been added to the system"),
            'staff_id.exists' => __t("You entered an inactive employee")
        ];
    }


    function beforeExport($item){
        $item->code = '"'.$item->code.'"';
        return $item;
    }

    public function after_prepare_data($staffsData) {
        $data = $staffsData->toArray();
        foreach ($data as $key => $staff) {
            if (!array_key_exists('official_id', $staff)) {
                unset($data[$key]['official_id_type']);
                continue;
            }

            if (empty($staff['official_id'])) {
                $data[$key]['official_id_type'] = '';
            } else {
                $data[$key]['official_id_type'] = \App\Facades\Staff::getStaffOfficialIdName(
                    $staff['country_code'] ?? null,
                    $staff['citizenship_status'] ?? null
                );
            }
        }

        return $data;
    }

    public function getLabels($data, $labels){
        $index = 0;
        foreach ($data   as $key => $value) {
            if($key == 'staffs.official_id') {
                array_splice($labels, $index + 1, 0, __t('Official ID Type'));
                break;
            }
            $index++;
        }
        return $labels;

    }

    function findResidenciesExpiringSoon($limit) {
        $accessibleBranchStaffIds = null;
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            /** @var StaffService $staffService */
            $staffService = resolve(StaffService::class);
            $accessibleBranchStaffIds = $this->getAccessibleStaffIds(null,true);
        }
        return $this->repo->findFirstToExpireContracts($limit,$accessibleBranchStaffIds);
    }

    public function getPayrollChecklist($staffId)
    {
        $staff = $this->repo->find($staffId);
        /** @var ContractRepository $contractRepo */
        $contractRepo = resolve(ContractRepository::class);
        $staffContracts = $staff->contracts;
        $checkList = [
            "title"=>sprintf(__t("Complete %s Profile"), $staff->name),
            "list"=>[]
        ];
        $canAdd = Permissions::checkPermission(\App\Utils\PermissionUtil::ADD_PAYROLL_CONTRACT);
        if (!$staffContracts->count() && $canAdd){
            $checkList["list"][] = $this->generateCheckListItem($staff, 'contracts');
        }
        return $checkList;
    }

    public  function getStaffDataOption(array $staffIds)
    {
        $staffs = $this->repo->findWhereIn('id',$staffIds)->all();
        $options = [];
        if (!empty($staffs)) {
            foreach ($staffs as $staff){
                $staffData = \App\Facades\Staff::getStaffOptionFormated($staff);
                if (!empty($staffData['data']['data-img'])){
                    $staffData['data']['img'] =$staffData['data']['data-img'];
                }
                $options[] = $staffData;
            }
            return $options;
        }
        return $options;
    }

    public function getStaffNameAndPhoto($q, $fromAccessibleBranches = false)
    {
        return $this->repo->getStaffsNameAndPhoto($q, $fromAccessibleBranches);
    }


    public function updateDirectManager($id, $request, $manager = NULL)
    {
        $staff = $this->repo->find($id);
        $this->appendedActivityLogNewData['Direct Manager'] = $manager ? $manager->full_name : '';
        $this->appendedActivityLogOldData['Direct Manager'] = $staff->staff_info?->direct_manager ? $staff->staff_info->direct_manager->full_name : '';
        $staffInfo = [
            'direct_manager_id' => $request->get('direct_manager_id'),
        ];
        $this->repo->saveStaffInfo($staff, $staffInfo);
        $this->logUpdate($staff, $staff);
    }

    private function getAccessibleStaffByBranches(array $branchesArray)
    {
        return $this->repo
            ->pushCriteria(new SelectCriteria('staffs.*'))
            ->pushCriteria(new CustomFind([
                [
                    'operation' => FilterOperations::MULTI_JOIN_IN_OPERATOR,
                    'value' => $branchesArray,
                    'field' => 'ItemStaff.item_id',
                    'joins' => [
                        [
                            'table' => 'item_staffs as ItemStaff',
                            'conditions' => [
                                ['first' => 'staffs.id', 'operation' => '=', 'second' => 'ItemStaff.staff_id'],
                                ['first' => 'ItemStaff.item_type', 'operation' => '=', 'second' => ItemStaffUtil::BRANCH_ITEM_TYPE, 'joinType' => 'value'],
                            ]
                        ],
                    ]
                ]
            ]))
            ->all()
            ->keyBy('id')
            ->all();
    }

    public function getAccessibleStaffByBranchKey(array $branchesArray): array
    {
        return $this->repo
            ->pushCriteria(new CustomFind([['value' => 1, 'field' => 'branch_id', 'operation' => FilterOperations::IN_ARRAY, 'data' => $branchesArray]]))
            ->all()->keyBy('id')->all();
    }

    /**
     * Prepares and remaps filter keys when exporting
     * data from entity listing with filters applied.
     */
    private function swapEntityFilterKeys(): void
    {
        $filterMappingKey = [
            "staff_info_employment_type_id" => 'types',
            "staff_info_employment_level_id" => 'levels',
            "staff_info_department_id" => 'depts',
            "staff_info_designation_id" => 'designations',
        ];
        foreach ($filterMappingKey as $oldKey => $newKey) {
            if (array_key_exists($oldKey, $this->parameters) && !empty($this->parameters[$oldKey])) {
                $this->parameters[$newKey] = $this->parameters[$oldKey];
                unset($this->parameters[$oldKey]);
            }
        }
    }

}
