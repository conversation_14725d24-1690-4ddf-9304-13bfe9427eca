<?php

namespace App\Services;

use App\Exceptions\BasicSalaryComponentCannotBeDeleted;
use App\Exceptions\AttachedToContractException;
use App\Exceptions\ComponentAttachedAsPlaceholder;

use App\Exceptions\AttachedToSalaryStructureException;
use App\Exceptions\ComponentAttachedToContractException;
use App\Exceptions\ComponentAttachedToPayslipException;
use App\Exceptions\ComponentUsedInMudadSettingsException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Payroll\NoSalaryComponentSelectedException;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Helpers\Placeholder\PlaceholderHelper;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\JournalAccountRepository;
use App\Repositories\SalaryComponentRepository;
use App\Helpers\FilterOperations;
use App\Repositories\EntityRepository;
use App\Repositories\PayRollComponentRepository;
use App\Requests\DefaultRequest;
use App\Services\PayrollComponent\PayrollComponentTemplateService;
use App\Utils\ActiveStatusUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\SalaryComponentTypeUtil;
use App\Utils\PluginUtil;
use Closure;
use Exception;
use Izam\Daftra\Injector\SettingRepository;

class SalaryComponentService extends BaseService
{
    var $filters = [];
    var $repo;
    var $journalRepo;


    /**
     * @var $payrollComponentTemplateService PayrollComponentTemplateService
     */
    var $payrollComponentTemplateService;
    var $settingRepository;

    protected $showRouteName = 'owner.salary_components.show';

    public function __construct(SalaryComponentRepository $repo, PayrollComponentTemplateService $payrollComponentTemplateService, JournalAccountRepository $journalRepo, EntityRepository $entityRepo = null, PayrollComponentRepository $payrollComponentRepo = null,SettingRepository $settingRepository)
    {
        $this->journalRepo = $journalRepo;
        parent::__construct($repo, $entityRepo);
        $this->payrollComponentTemplateService = $payrollComponentTemplateService;
        $this->settingRepository = $settingRepository;
    }

    public function getFormRelatedData()
    {
        $defaultAccountoptions = [];
        if (old('default_account_id')) {
            $defaultAccountId = request()->old('default_account_id');
            $journal_record = $this->journalRepo->find($defaultAccountId);
            $defaultAccountString = $journal_record->code . " " . $journal_record->name;
            $defaultAccountoptions = array($journal_record->id => $defaultAccountString);
        }

        $types = SalaryComponentTypeUtil::getSalaryComponentsTypesforView();
        return [
            'comp_types' => $types,
            'comp_acc_options' => $defaultAccountoptions
        ];
    }

    function getBasicSalaryComponent()
    {
        return $this->repo->pushCriteria(new CustomFind([['field' => 'is_basic', 'value' => 1, 'operation' => 1]]))->first();
    }

    public function getFilters()
    {
        if (!empty($this->filters)) {
            return $this->filters;
        } else {

            return $this->filters = [
                'name' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => '',
                    'inputClass' => 'form-control',
                    'div' => 'col-md-8 form-group',
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'before' => '<div class="form-group-icon">',
                    'attributes' => [
                        'placeholder' => sprintf(__t('Search by %s Name'), __t('Salary Component'))],
                    'type' => 'text',
                    'filter_options' => [
                        'operation' => FilterOperations::IN_STRING_FILTER_OPERATION,
                        'field' => 'name'
                    ]
                ],
                'active' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'attributes' => [
                        'placeholder' => __t('All Status'),
                        'id' => 'activeSelect'
                    ],
                    'label' => false,
                    'div' => 'col-md-4 form-group',
                    'options' => ActiveStatusUtil::getStatusList(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'status'
                    ]
                ],
                'type' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => '',
                    'inputClass' => 'select-filter form-control',
                    'div' => 'col-md-4 form-group',
                    'attributes' => ['placeholder' => __t('All Types'), 'data-allow-clear' => 'true'],
                    'name' => 'type',
                    'type' => 'select',
                    'options' => [
                        'earning' => __t('Earning'),
                        'deduction' => __t('Deduction')
                    ],
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'type'
                    ]
                ]
            ];
        }
    }

    public function getSortFields()
    {
        return [
            'fields' => [
                'name' => [
                    'title' => __t('Name'),
                    'field' => 'name',
                    'direction' => 'DESC'
                ],
                'created' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'created',
                    'direction' => 'DESC'
                ],
                'id' => [
                    'title' => __t('ID'),
                    'field' => 'id',
                    'hidden' => true,
                    'direction' => 'DESC'
                ],
            ],
            'active' => [
                'field' => 'id',
                'direction' => 'DESC',
            ]
        ];
    }

    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $payrollComponent = false;
        if(isset($request['payroll_component_id']) && is_numeric($request['payroll_component_id'])) {
            if(!Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
                throw new Exception(__t('Attendance Employee Plugin not active'));
            }
            $request['value_type'] = 'formula';
            $payrollComponentTemplateService = $this->payrollComponentTemplateService;
            $payrollComponent = $payrollComponentTemplateService->getPayrollComponent($request['payroll_component_id']);
            $payrollComponentType = $payrollComponentTemplateService->getTypeForFormula($payrollComponent);
            $formulatedData = $payrollComponentTemplateService->formulateData($payrollComponentType, $request->params);
        }
        if (isset($request['tax_brackets']) && $request['tax_brackets'] == 2) { //this means want calc tax  in net salary 
             $request['is_basic'] = -1;
        }
        if (isset($request['is_reference_only']) && $request['is_reference_only'] == 1) {
            $request['is_reference_only'] = 1;
            $request['default_account_id'] = null;
        } else{
            $request['is_reference_only'] = 0;
        }
        if ($request['value_type'] == 'amount') {
            $request['formula'] = null;
        } else if ($request['value_type'] == 'formula') {
            $request['amount'] = null;
        }

        if(isset($request['comp-name']))
            $request['name'] = $request['comp-name'];

        if(isset($request['comp-description']))
            $request['description'] = $request['comp-description'];

        $data =  parent::add($request, $excludedFields); // TODO: Change the autogenerated stub

        if($payrollComponent) {
            $formulatedData = $payrollComponentTemplateService->handleAttendanceFlag($payrollComponent, $formulatedData, $request,$data->id);
            $payrollComponentTemplateService->assignToObject($formulatedData['result'], $request);
            $data->formula  = $request->formula;
            $data->condition  = $request->condition;
            $data->save();

            $payrollComponentTemplateService->saveSalaryComponentData($data,$request->params,$payrollComponent);
        }

        return $data;
    }

    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        if(isset($request['payroll_component_id']) && is_numeric($request['payroll_component_id'])) {
            $request['value_type'] = 'formula';
            $payrollComponentTemplateService = $this->payrollComponentTemplateService;
            $payrollComponent = $payrollComponentTemplateService->getPayrollComponent($request['payroll_component_id']);
            $payrollComponentType = $payrollComponentTemplateService->getTypeForFormula($payrollComponent);
            $formulatedData = $payrollComponentTemplateService->formulateData($payrollComponentType, $request->params);
            $formulatedData = $payrollComponentTemplateService->handleAttendanceFlag($payrollComponent, $formulatedData, $request,$id);
            $payrollComponentTemplateService->assignToObject($formulatedData['result'], $request);
        }
        if (isset($request['tax_brackets']) && $request['tax_brackets'] == 2) { //this means want calc tax  in net salary 
             $request['is_basic'] = -1;
        }
        if (isset($request['is_reference_only']) && $request['is_reference_only'] == 1) {
            $request['is_reference_only'] = 1;
            $request['default_account_id'] = null;
        } else  {
            $request['is_reference_only'] = 0;
        }
        if ($request['value_type'] == 'amount') {
            $request['formula'] = null;
        } else if ($request['value_type'] == 'formula') {
            $request['amount'] = null;
        }

        if(isset($request['comp-name']))
            $request['name'] = $request['comp-name'];

        $request['description'] = $request['comp-description'] ?? $request['description'];

        $data = parent::update($id, $request, $excludedFields);

        if(isset($payrollComponent) && $payrollComponent) {
            $this->payrollComponentTemplateService->saveSalaryComponentData($data, $request->params, $payrollComponent);
        }

        return $data;
    }

    public function delete($id, Closure $callback = null)
    {
        $salaryComponent = $this->find($id);
        if ($salaryComponent->is_basic)
            throw new BasicSalaryComponentCannotBeDeleted();
        else if ($salaryComponent->contracts()->count())
            throw new ComponentAttachedToContractException($salaryComponent->name);
        else if ($salaryComponent->salaryStructures()->count())
            throw new AttachedToSalaryStructureException($salaryComponent->name);
        else if ($salaryComponent->payslips()->count())
            throw new ComponentAttachedToPayslipException($salaryComponent->name);
        else if (PlaceholderHelper::isUsed(EntityKeyTypesUtil::SALARY_COMPONENT,$id))
            throw new ComponentAttachedAsPlaceholder($salaryComponent->name);
        else if ($this->isComponentsUsedinMudadSettings($id))
            throw new ComponentUsedInMudadSettingsException($salaryComponent->name);
        parent::delete($id);
    }

    public function activate($id)
    {
        $salaryComponent = $this->repo->find($id);
        if (is_null($salaryComponent)) {
            throw new EntityNotFoundException();
        } else {
            $request = new DefaultRequest(['status' => 1]);
            parent::update($id, $request);
        }
    }

    public function deactivate($id)
    {
        $salaryComponent = $this->repo->find($id);
        if (is_null($salaryComponent)) {
            throw new EntityNotFoundException();
        } else if ($this->isComponentsUsedinMudadSettings($id)) {
            throw new ComponentUsedInMudadSettingsException($salaryComponent->name);
        } else {
            $request = new DefaultRequest(['status' => 0]);
            parent::update($id, $request);
        }
    }


    /**
     * {@inheritDoc}
     */
    protected function getListingConditions()
    {
        $parentConditions = parent::getListingConditions();
        $parentConditions[] = [
            "operation" => FilterOperations::NOT_EQUAL_FILTER_OPERATION,
            "field" => "is_excluded",
            "value" => true
        ];
        return $parentConditions;
    }

        /**
     * @array $payslipIds
     * @throws NoPayslipSelectedException
     */
    private function checkSalaryComponentsNotEmpty(array $salaryComponentsIds)
    {
        if (implode(null, $salaryComponentsIds) == null)
            throw new NoSalaryComponentSelectedException();
    }


    public function deleteMany(array $ids, $selectType = null)
    {
        $this->checkSalaryComponentsNotEmpty($ids);
        $deletedCount = 0;
        $unDeletedCount = 0;
        $invalidDeleteData = [];
        foreach ($ids as $id) {
            try {
                $this->delete($id);
                $deletedCount++;
            } catch (Exception $exception) {
                $invalidDeleteData[$id] = $exception->getMessage();
                $unDeletedCount++;
                continue;
            }
        }

        return ['deletedCount' => $deletedCount, 'unDeletedCount' => $unDeletedCount, 'invalidDeleteData' => $invalidDeleteData];
    }
    public function getFilteredRecords()
    {
        $this->repo->pushCriteria(new CustomFind($this->getListingConditions(), []));
        return $this->repo->list();
    }

    public function isComponentsUsedinMudadSettings($id){

        $settings = Settings::getValue(PluginUtil::MUDAD_PLUGIN, \App\Helpers\Settings::MUDAD_SALARY_COMPONENTS);
        if (!$settings) {
            return false;
        }
        $settingComponents = json_decode($settings);
        foreach ($settingComponents as $component) {
            if (in_array($id, $component->salary_components)) {
                return true;
            }
        }
        return false;
    }
}
