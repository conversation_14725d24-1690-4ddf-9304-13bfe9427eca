<?php

namespace App\Services;

use App\Exceptions\PurchaseRequestHasQuotationsForStatus;
use App\Repositories\PurchaseRequestRepository;
use App\Utils\FollowUpItemTypeUtil;
use App\Utils\PurchaseRequestStatusUtil;
use App\Exceptions\WrongPurchaseRequestStatus;
use App\Repositories\EntityRepository;
use App\Repositories\QuotationRequestRepository;
use App\Repositories\FollowUpStatusRepository;
use App\Utils\EntityKeyTypesUtil;

class PurchaseRequestService extends BaseService
{

    public $repo;
    public $quotationRequestRepository;
    public $followUpStatusRepository;


    public function __construct(
        PurchaseRequestRepository $repo,
        QuotationRequestRepository $quotationRequestRepository,
        FollowUpStatusRepository $followUpStatusRepository,
        EntityRepository $entityRepo = null
    ) {
        parent::__construct($repo, $entityRepo);
        $this->repo = $repo;
        $this->quotationRequestRepository = $quotationRequestRepository;
        $this->followUpStatusRepository = $followUpStatusRepository;
    }



    public function updateStatus($id, $status){
        $request = request()->merge(['status' => $status]);
        $request->request->remove('entityKey');    
        if(!in_array(ucfirst($status), PurchaseRequestStatusUtil::statusList())){ 
            throw new WrongPurchaseRequestStatus($status);
        }
  
        if($this->quotationRequestRepository->hasPurchaseRequest($id)){ 
           $quotation = $this->quotationRequestRepository->getFirstQuotationByPurchaseRequest($id);   
           throw new PurchaseRequestHasQuotationsForStatus($quotation->code, route('owner.entity.show', ['entityKey' => EntityKeyTypesUtil::QUOTATION_REQUEST,'id' => $quotation->id]));
        }
       
        $result = parent::update($id, $request);
        return $result;
    }

    public function updateFollowUpStatus($id, $status){
        $request = request()->merge(['follow_up_status_id' => $status]);
        $request->request->remove('entityKey');    
        $followUpKey = FollowUpItemTypeUtil::PURCHASE_REQUEST_TYPE;
        $status = $this->followUpStatusRepository->findWhere(['item_type' => $followUpKey,'id' => $status]);
        if(!$status->isEmpty()){
            $result = parent::update($id, $request);
            return $result; 
        }

    }

    public function  exportBreadCrumbs()
    {
        $breadCrumbs[] = [

            'link' => '/v2/owner/entity/purchase_request/list',
            'title' => __t('Purchase Request')
        ];
        return $breadCrumbs;
    }

}