<?php

namespace App\Services;

use App\DTO\NotificationPreferenceDto;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\DeviceAppNotificationRepository;
use App\Repositories\EntityAppDataRepository;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\FcmDevicesAppUtil;

/**
 * Class DeviceAppNotificationService
 *
 * This service handles notification preferences related to different device applications (e.g., leave app).
 * It interacts with the entity app data repository to store and retrieve push notification preferences for a given staff member.
 */
class DeviceAppNotificationService
{
    
    /**
     * DeviceAppNotificationService constructor.
     *
     * @param DeviceAppNotificationRepository $repo
     * @param EntityAppDataRepository $entityAppDataRepository
     */
    public function __construct(private DeviceAppNotificationRepository $repo, private EntityAppDataRepository $entityAppDataRepository)
    {
    }

    /**
     * Returns the list of notification preferences for the given staff member and app.
     * If preferences have been saved, merges them with default preferences.
     *
     * @param int|string $staffId
     * @param string $appKey
     * @return NotificationPreferenceDto[]
     */
    public function listNotificationPreferences($staffId, $appKey = FcmDevicesAppUtil::LEAVE_APP){
        $result = [];
        $preferences = $this->entityAppDataRepository->pushCriteria(new EqualCriteria('entity_key', EntityKeyTypesUtil::STAFF_ENTITY_KEY))
            ->pushCriteria(new EqualCriteria('entity_id', $staffId))
            ->pushCriteria(new EqualCriteria('app_key', $appKey))
            ->pushCriteria(new EqualCriteria('action_key', FcmDevicesAppUtil::ENTITY_APP_DATA_NOTIFICATION_PREFERENCE_KEY))
            ->first()?->data;

        $allPreferences = FcmDevicesAppUtil::getAllNotificationPreferenceForApp($appKey);
        $preferences = !empty($preferences) ? $this->mergePreferences($allPreferences, json_decode($preferences, true)) : $allPreferences;

        foreach($preferences as $preference){
            $result[] = new NotificationPreferenceDto($preference);            
        }
        return $result;
    }

    /**
     * Merges two sets of preferences. Values from the second array override the first by matching key.
     *
     * @param array $preferencesOne Defaults
     * @param array $preferencesTwo Stored overrides
     * @return array Merged preferences
     */
    private function mergePreferences($preferencesOne, $preferencesTwo){
        $result = [];
        foreach($preferencesOne as $preference){
            $result[$preference['key']] = $preference;
        }
        foreach($preferencesTwo as $preference){
            $result[$preference['key']] = $preference;            
        }
        return array_values($result);
    }

    /**
     * Get a specific notification preference for a given staff and preference key.
     *
     * @param int|string $staffId
     * @param string $appKey
     * @param string $preferenceKey
     * @return NotificationPreferenceDto|null
     */
    public function getSpecificPreference($staffId, $appKey, $preferenceKey){
        $notificationPreferences = $this->listNotificationPreferences($staffId, $appKey);
        if(!empty($notificationPreferences)){
            foreach($notificationPreferences as $notificationPreference){
                if($notificationPreference->getKey() == $preferenceKey){
                    return $notificationPreference;
                }
            }
        }
        return null;
    }

    /**
     * Check whether a specific notification preference is enabled.
     * Defaults to `true` (on) if the preference is not found.
     *
     * @param int|string $staffId
     * @param string $appKey
     * @param string $preferenceKey
     * @return bool
     */
    public function preferenceIsOn($staffId, $appKey, $preferenceKey) : bool{
        $preference = $this->getSpecificPreference($staffId, $appKey, $preferenceKey);
        return empty($preference) || $preference->getValue() == 1;
    }

    /**
     * Updates the value of a specific notification preference and persists the change.
     *
     * @param int|string $staffId
     * @param array $data Must contain 'key' and 'value'
     * @param string $appKey
     * @return void
     */
    public function updateNotificationPreferences($staffId, $data, $appKey = FcmDevicesAppUtil::LEAVE_APP){
        $preferences = $this->listNotificationPreferences($staffId, $appKey);
        /** @var NotificationPreferenceDto $preference */
        foreach ($preferences as $key => $preference) {
            $preference->getKey() == $data['key'] ? $preference->setValue($data['value']) : null;
        }
        $this->entityAppDataRepository->updateOrInsert(
            [
                'entity_key' => EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                'entity_id' => $staffId,
                'app_key' => $appKey,
                'action_key' => FcmDevicesAppUtil::ENTITY_APP_DATA_NOTIFICATION_PREFERENCE_KEY,
            ],
            [
                'data' => json_encode($preferences),
            ]
        );
    }

}