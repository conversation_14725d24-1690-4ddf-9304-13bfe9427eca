<?php

namespace App\Services\MultiCycleApprovalConfiguration;

use App\Facades\Permissions;
use App\Services\PaginationStackTrait;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PermissionUtil;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\Sperator;
use Izam\View\Form\Element\StatusCircle;
use Izam\View\Form\Element\TitleText;
use Izam\View\Form\Element\ViewActions;
use Izam\View\Form\Tab\IframeElement;
use Izam\View\Form\Tab\Services\CreateTabsServices;
use Izam\View\Form\Tab\Tabs;
use Laminas\Form\Element;
use Laminas\Form\Element\Button;

class ShowService
{
    use PaginationStackTrait;

    public function getActions(AppEntityData $data): ViewActions
    {
        $viewActions = new ViewActions('View-Action');
        $editAnchor = new Anchor('edit-action');
        $editAnchor->setLabel(__t('Edit'))
            ->setAttribute('href', route('owner.entity.edit', [
                'entityKey' => EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION,
                'id' => $data->id
            ]))
            ->setOption('icon', 'pencil');

        $deleteBtn = new Button('delete-action');
        $deleteBtn
            ->setOption('message', sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Approval Cycle Configuration')))
            ->setOption('action', route('owner.entity.delete', [
                'entityKey' => EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION,
                'id' => $data->id
            ]))
            ->setOption('theme', 'delete')
            ->setLabel(__t('Delete'))
            ->setOption('icon', 'trash-can');
        $viewActions
            ->add($editAnchor)
            ->add($deleteBtn);

        $value = json_decode($data->value);
        if($value->status == MultiCycleApprovalUtil::STATUS_ACTIVE){
            $deactivateBtn = new Anchor('deactivate-action');
            $deactivateBtn->setLabel(__t('Deactivate'))
            ->setOption('icon', 'close')
            ->setAttributes([
                "data-md-action"=> route('owner.multi_cycle_approval_configuration.update_status',['status' =>MultiCycleApprovalUtil::STATUS_INACTIVE, 'id' =>$data->id]),
                'data-md-message' => __t('Are you sure you want to deactivate this configuration?'),
                "data-md-method" =>"Get",
                'data-md-data' => json_encode([
                    '_method' => 'GET',
                    '_token' => csrf_token(),
                    'status' => MultiCycleApprovalUtil::STATUS_INACTIVE,
                    'id'     => $data->id
                ]),
            ]);

            $viewActions
                ->add($deactivateBtn);
        } else {
            $activateBtn = new Anchor('activate-action');
            $activateBtn->setLabel(__t('Activate'))
                ->setOption('icon', 'check')
                ->setAttribute('href', route('owner.multi_cycle_approval_configuration.update_status',['status' =>MultiCycleApprovalUtil::STATUS_ACTIVE, 'id' =>$data->id]));
            $viewActions
                ->add($activateBtn);
        }
        return $viewActions;
    }

    public function getPageHead(AppEntityData $data): Header
    {
        $pageTitle = new TitleText('page-title');
        $dataFormated = json_decode($data->value);
        $pageTitle
            ->setTitle($dataFormated->name . ' #' . $data->id);

        $statusLabel = MultiCycleApprovalUtil::getStatusText($dataFormated->status);
        $statusClass = MultiCycleApprovalUtil::getStatusClass($dataFormated->status);

        $statusElement = new StatusCircle('status-icon');
        $statusElement
            ->setOption('color', $dataFormated->status == MultiCycleApprovalUtil::STATUS_ACTIVE ? 'active' : 'inactive')
            ->setLabel(__t($statusLabel));

        $header = new Header('page-header');

        $header
            ->addLeft($pageTitle)
            ->addLeft(new Sperator('separator'))
            ->addLeft($statusElement)
            ->addRight($this->getPagination(EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION, $data->id));

        return $header;
    }

    public function getTabs($data, $form, $viewExtraData = []): Tabs
    {
        $dataDecoded = json_decode($data->value);
        $tabs = [
            [
                'type' => Element::class,
                'name' => 'details',
                'label' => __t('Details'),
                'value' => [
                    'viewPath' => 'multi_cycle_approval_configuration/partials/details',
                    'context' => array_merge(['data' => $dataDecoded, 'form' => $form,'viewExtraData' => $viewExtraData])
                ]
            ],
            [
                'type' => IframeElement::class,
                'name' => 'activity-log',
                'label' => __t('Activity Log'),
                'attributes' => [
                    'src' => "/v2/owner/activity_logs/entity/iframe?entity_key=" . \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION . "&entity_id=" . $data->id . "&sort=DESC&layout2022=1",
                ]
            ]
        ];

        return CreateTabsServices::createTabs($tabs);
    }
}
