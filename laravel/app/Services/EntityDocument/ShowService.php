<?php

namespace App\Services\EntityDocument;

use App\Http\Middleware\CheckPermissions;
use App\Services\EntityDocumentService;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PermissionUtil;
use Carbon\Carbon;
use Dom\Entity;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Badge;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\Sperator;
use Izam\View\Form\Element\SplitButtonDropdowns;
use Izam\View\Form\Element\Stack;
use Izam\View\Form\Element\TitleText;
use Izam\View\Form\Element\ViewActions;
use Izam\View\Form\Tab\IframeElement;
use Izam\View\Form\Tab\Services\CreateTabsServices;
use Izam\View\Form\Tab\Tabs;
use Laminas\Form\Element;
use Laminas\Form\Element\Button;

class ShowService
{
    const ENTITY_KEY = EntityKeyTypesUtil::ENTITY_DOCUMENT;
    public function getPageHead(AppEntityData $data): Header
    {
        $pageTitle = new TitleText('page-title');
        $pageTitle
            ->setTitle($data->name)->setSecondaryInfo('#' .$data->id);


        $header = new Header('page-header');

        $header
            ->addLeft($pageTitle)
            ->addLeft(new Sperator('separator'));

        $status = resolve(EntityDocumentService::class)->calculateStatus($data);
        $leftStack = new Stack('left-stack');
        $leftStack->setOption('theme', 'theme2');

        $expireyDate = Carbon::parse($data->expiry_date);
        $expireIn = $expireyDate->diffInDays(today()->subDay()) ;

        $mainStatusBadge = new Badge('left-page-badge');
        $mainStatusBadge->setLabel(EntityDocumentStatusUtil::getStatusTrans($status, $expireIn))
        ->setOption('extra-classes', "text-bg-".EntityDocumentStatusUtil::getStatusCssClass($status));
        $leftStack->add($mainStatusBadge);

        $rightStack = new Stack('right-stack');
        $rightStack->setOption('theme', 'theme3');
        if(check_permission(PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS)){
            $uploadBtnHTML = '<a class="btn btn-success" type="button" href='. route('owner.entity.create' ,['entityKey'=>'entity_document' , 'subEntityKey'=>$data->entity_key,'subEntityId'=> $data->entity_id ,'entity_document_type_id' => $data->entity_document_type_id]).'>
            <i class="mdi mdi-upload me-sm-2 fs-10"></i>'. __t('Upload New').'</a>';
            $uploadBtn = new Element('upload-btn');
            $uploadBtn->setLabel($uploadBtnHTML);
            $rightStack->add($uploadBtn);
        }

        $header->addLeft($leftStack)
        ->addRight($rightStack);
        return $header;
    }

    public function getTabs(AppEntityData $data, $form, $viewExtraData = []): Tabs
    {
        $tabs = [
            [
                'type' => Element::class,
                'name' => 'details',
                'label' => __t('Details'),
                'value' => [
                    'viewPath' => 'entity_documents/partials/details',
                    'context' => [
                        'data' => $data,
                        'form' => $form ,
                        'entityKey'=>self::ENTITY_KEY,
                    ]
                ]
            ],
            [
                'type' => IframeElement::class,
                'name' => 'related-documents',
                'label' => __t('Related Documents'),
                'attributes' => [
                    'src' => sprintf(
                        "/v2/owner/entity/entity_document/list?iframe=1&related_document=1&filter[entity_key]=%s&filter[entity_id]=%s&filter[entity_document_type_id]=%s&sort[created]=DESC",
                        EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                        $data->entity_id,
                        $data->entity_document_type_id
                    )
                ]
                    ],
            [
                'type' => IframeElement::class,
                'name' => 'activity-log',
                'label' => __t('Activity Log'),
                'attributes' => [
                    'src' => sprintf(
                        "/v2/owner/activity_logs/entity/iframe?entity_key=%s&entity_id=%s&sort=DESC&layout2022=1",
                        self::ENTITY_KEY,
                        $data->id
                    )
                ]
            ]
        ];
        return CreateTabsServices::createTabs($tabs);
    }

    public function getActions($data, $viewExtraData = [])
    {
        $viewActions = new ViewActions('View-Action');

        if(check_permission(PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS)){
            $editAnchor = new Anchor('edit-action');
            $editAnchor->setLabel(__t('Edit'))
                ->setAttribute('href', route('owner.entity.edit', [
                    'entityKey' => self::ENTITY_KEY,
                    'id' => $data->id
                ]))
                ->setOption('icon', 'pencil');

            $viewActions->add($editAnchor);

            $deleteBtn = new Button('delete-action');
            $deleteBtn
                ->setOption('message', sprintf(__t("Are you sure you want to delete this %s? \n The document and its attachments will be deleted permanently"), __t("Document")))
                ->setOption('action', route('owner.entity.delete', [
                    'entityKey' => self::ENTITY_KEY,
                    'id' => $data->id
                ]))
                ->setOption('theme', 'delete')
                ->setLabel(__t('Delete'))
                ->setOption('icon', 'trash-can');

            $viewActions->add($deleteBtn);
        }
        return $viewActions;
    }
}