<?php

namespace App\Services;

use App\Modules\LocalEntity\Actions\ListAction;
use App\Modules\LocalEntity\Listing\Parser\FormParser;
use Izam\Daftra\Common\EntityStructure\IEntityStructureGetter;
use App\Exceptions\ProductsBulkUpdateFails;
use App\Facades\Branch;
use App\Helpers\FilterOperations;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Repositories\CategoryRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\PriceListRepository;
use App\Repositories\ProductRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Requests\Products\ProductsBulkUpdateRequest;
use App\Utils\ActionLineMainOperationTypesUtil;
use App\Utils\CategoryTypesUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Illuminate\Http\Request;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Criteria\IsNullCriteria;
use App\Repositories\Criteria\NotEqualCriteria;
use App\Repositories\Criteria\NotInArrayCriteria;
use App\Repositories\Criteria\OrCriteria;
use App\Repositories\Criteria\WhereLikeCriteria;
use App\Repositories\TrackingNumberRepository;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Izam\Daftra\Common\Utils\PluginUtil;
use Illuminate\Support\Collection;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Validator;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Attachment\Service\AttachmentsService;

class ProductService extends BaseService
{

    /**
     * @var ProductRepository
     */
    var $repo;
    var $storeRules = [
        'name' => 'required'
    ];
    var $updateRules = [
        'name' => 'required'
    ];

    var $extraImportFields =[
        'products.attributes'
    ];

    /**
     * @var OfferService
     */
    protected $offerService;

    /**
     * @var ListAction
     */
    protected ListAction $listAction;

    /**
     * @var FormParser
     */
    protected FormParser $formParser;

    /**
     * @var IEntityStructureGetter
     */
    protected IEntityStructureGetter $structureGetter;

    /**
     * ProductService constructor.
     *
     * @param ProductRepository $repo
     * @param PriceListRepository $priceListRepository
     * @param RoleService $roleService
     * @param OfferService $offerService
     * @param ListAction $listAction
     * @param FormParser $formParser
     * @param IEntityStructureGetter $structureGetter
     */
    public function __construct(ProductRepository $repo, PriceListRepository $priceListRepository, RoleService $roleService, OfferService $offerService, ListAction $listAction, FormParser $formParser, IEntityStructureGetter $structureGetter)
    {
        parent::__construct($repo);
        $this->priceListRepository = $priceListRepository;
        $this->offerService = $offerService;
        $this->listAction = $listAction;
        $this->formParser = $formParser;
        $this->structureGetter = $structureGetter;
    }

    function getFormRelatedRepos()
    {
        return $formRelatedRepos = [
            'categories' => ['interface' => 'App\Repositories\CategoryRepository', 'columns' => ['id', 'name']]
        ];
    }

    /**
     * get filters input dynamic data and sets the filter inputs values from parameters
     * @return array
     */
    function getFilters()
    {

        return $filters = [
            'name' => ['type' => 'text', 'filter_options' => ['operation' => FilterOperations::IN_STRING_FILTER_OPERATION, 'field' => 'name', 'model' => 'Product']],
            'created' => ['type' => 'dateRange', 'filter_options' => ['operation' => FilterOperations::DATE_RANGE_FILTER_OPERATION, 'field' => 'created', 'model' => 'Product']],
        ];
    }

    /**
     * returns service sort fields
     * @return array
     */
    function getSortFields()
    {
        return [
            'fields' => [
                'name' => ['title' => 'Name', 'field' => 'name', 'direction' => 'DESC'],
                'created' => ['title' => 'Date of Creation', 'field' => 'created', 'direction' => 'ASC']
            ],
            'active' => ['field' => 'name', 'direction' => 'DESC']
        ];
    }

    function filterName($query, $allowInActive = false)
    {
        return $this->repo->getAutoCompleteResult($query, $allowInActive);
    }

    /**
     * @param Request $request
     * @return array
     * @throws ProductsBulkUpdateFails
     */
    public function bulkUpdateFilters(Request $request): array
    {
        $bulkUpdateRequest = new ProductsBulkUpdateRequest($request->except('_token'));

        if ($bulkUpdateRequest->amount_type === "amount_selection" && $bulkUpdateRequest->amount < 0 && $fails = $this->repo->getProductsLessThan($bulkUpdateRequest->amount, $bulkUpdateRequest->price_list ?? null,$bulkUpdateRequest['categories'] ?? null )) {
            throw new ProductsBulkUpdateFails($fails[0]->name ?? "", $fails[0]->product_code ?? "");
        }

        if ($bulkUpdateRequest->type === "price_list") {
            $products = $this->repo->getProductsByPriceListFilters(
                $bulkUpdateRequest->price_list,
                $bulkUpdateRequest['categories'] ?? [],
                $bulkUpdateRequest['brands'] ?? [],
                $this->repo->getNumberOfProductsInGroupPrice((int)$bulkUpdateRequest->price_list)
            );
        } else {
            $products = $this->repo->getProductsByAmountSelectionFilters($bulkUpdateRequest['categories'] ?? [], $bulkUpdateRequest['brands'] ?? []);
        }
        $products = $this->calculateBulkUpdateChanges($bulkUpdateRequest, $products);
        return compact('products', 'bulkUpdateRequest');
    }

    public function generateSerials($numberOfSerials, $startingSerial) : array {
        $serials = [];
        for ($i = 0; $i <  $numberOfSerials ; $i++) {
            $serials[] = ++$startingSerial;
        }

        /** @var TrackingNumberRepository $trackingNumberRepository */
        $trackingNumberRepository = resolve(TrackingNumberRepository::class);
        $nonUniqueSerials = $trackingNumberRepository->filterNonUniqueSerials($serials);
        if($nonUniqueSerials->isEmpty()){
            return $serials;
        }
        $nonUniqueSerialsMap = $nonUniqueSerials->keyBy('serial')->all();
        return array_merge(
            array_filter($serials,fn($serial) => !isset($nonUniqueSerialsMap[$serial])),
            $this->generateSerials(
                $nonUniqueSerials->count(),
                array_slice($serials, -1)[0]
            )
        );
    }

    public function getLastTrackingSerial(){
        /** @var TrackingNumberRepository $trackingNumberRepository */
        $trackingNumberRepository = resolve(TrackingNumberRepository::class);
        $trackingNumber = $trackingNumberRepository->getLastTrackingSerial();
        return $trackingNumber?->serial ?? "000001";
    }

    public function bulkUpdate(Request $request)
    {
        $bulkUpdateRequest = new ProductsBulkUpdateRequest(json_decode($request->bulkUpdateRequest, true)['params'] ?? []);
        $products = json_decode($request->products);
        if ($request->type === "price_list") {
            $priceListId = intval($bulkUpdateRequest->params['price_list']);
            $priceList = $this->priceListRepository->find($priceListId);
            $result = $this->repo->bulkUpdateForPriceList($products, $priceListId);
            if ($result) {
                foreach ($products as $product) {
                    $activityLogRequest = new ActivityLogRequest(
                        $product->product_price_id,
                        EntityKeyTypesUtil::PRICE_LIST_ITEMS,
                        ActionLineMainOperationTypesUtil::UPDATED_PRODUCT_PRICE_IN_ACTION,
                        $priceList->name,
                        route('owner.price_lists.show', ['price_list' => $priceList->id]),
                        ['Price' =>  $product->new_price, 'Product' => $product->name],
                        ['Price' =>  $product->old_price],
                        [
                            new ActivityLogRelationRequest($priceList->id,EntityKeyTypesUtil::PRICE_LIST),
                            new ActivityLogRelationRequest($product->id,EntityKeyTypesUtil::PRODUCT_ENTITY_KEY)
                        ]
                    );
                    $activityLogRequest->replaceEntityLabel("Price List");
                    $this->getActivityLogService()->addActivity($activityLogRequest);
                }
            }
            return $result;
        } else {
            $result = $this->repo->bulkUpdateForAmountSelection($products);
            if ($result) {
                foreach ($products as $product) {
                    $activityLogRequest = new ActivityLogRequest(
                        $product->id,
                        EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                        ActionLineMainOperationTypesUtil::UPDATE_ACTION,
                        $product->name,
                        getCakeURL(['controller' => 'products', 'action' => 'view/' . $product->id]),
                        ['Price' =>  $product->new_price],
                        ['Price' =>  $product->old_price]
                    );
                    $activityLogRequest->replaceEntityLabel("Product");
                    $this->getActivityLogService()->addActivity($activityLogRequest);
                }
            }
            return $result;
        }
    }

    /**
     * @param ProductsBulkUpdateRequest $request
     * @param array $products
     * @return array
     */
    public function calculateBulkUpdateChanges(ProductsBulkUpdateRequest $request, array $products): array
    {
        foreach ($products as $key => $product) {
            if ($request->amount_type === "percent_selection") {
                if ($request->percent_type == 1) {
                    $product->new_price = $product->old_price + $product->old_price * $request->percent_amount / 100;
                } else {
                    $product->new_price = $product->old_price - $product->old_price * $request->percent_amount / 100;
                }
            } else {
                $product->new_price = $product->old_price + $request->amount;
            }
        }
        return $products;
    }

    /**
     * @return array
     */
    public function getBulkUpdateData(): array
    {
        $productRepo = resolve(ProductRepository::class);
        $categoryRepo = resolve(CategoryRepository::class);
        $priceListRepo = resolve(PriceListRepository::class);
        $priceListRepo->pushCriteria(new CustomFind([
            ['field' => 'status', 'value' => true, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION],
        ]));
        $productRepo->pushCriteria(new CustomFind([
            ['field' => 'brand', 'value' => null, 'operation' => FilterOperations::NOT_NULL_FILTER_OPERATION],
            ['field' => 'brand', 'value' => "", 'operation' => FilterOperations::NOT_EQUAL_FILTER_OPERATION],
        ]));

        $condition =   [
            ['field' => 'name', 'value' => null, 'operation' => FilterOperations::NOT_NULL_FILTER_OPERATION],
            ['field' => 'category_type', 'value' => CategoryTypesUtil::CategoryTypeProduct, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]
        ];
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && !\Settings::getValue(PluginUtil::BranchesPlugin, 'share_products')) {
            $condition[] = ['field' => 'branch_id', 'value' => Branch::getCurrentBranchID(), 'operation' => FilterOperations::EQUAL_FILTER_OPERATION];
        }
        $categoryRepo->pushCriteria(new CustomFind($condition));

        $brands = $productRepo->getBrandsList();
        $categories = $categoryRepo->list([], true);
        $priceLists = $priceListRepo->list([], true);
        return compact('brands', 'categories', 'priceLists');
    }

    public function getDistinctBrands($brandName){
        return $this->repo->searchDistinctBrands($brandName);
    }

    public function getAutoSuggestProducts($productNameOrSku, $activeOnly = 0, $products_only = false, $services_only = false, $for_booking = false){
        return $this->repo->searchAutoSuggestProducts($productNameOrSku, $activeOnly, $products_only, $services_only, $for_booking);
    }

    public function getAutoSuggestProductsV2($params = []){
        $addedRelations = [];
        if(isset($params['get_by_id_only'])){
            $this->repo->disableBranchOnFind();
            if(is_array($params['get_by_id_only'])){
                $this->repo->pushCriteria(new InArrayCriteria('id', $params['get_by_id_only']));
            }else{
                $this->repo->pushCriteria(new EqualCriteria('id', $params['get_by_id_only']));
            }
            return $this->repo->getAutoSuggestProductsV2(null)->map($this->productFormatterCallback());
        }
        $this->repo->enableBranchOnFind();
        foreach ($params as $key => $value) {
            if($key == 'status'){
                if($value == ProductStatusUtil::STATUS_INACTIVE){
                    continue;
                }
                $statusOrWheres = [new IsNullCriteria('status'), new EqualCriteria('status', ProductStatusUtil::STATUS_ACTIVE)];
                if($value == ProductStatusUtil::STATUS_SUSPENDED){
                    $statusOrWheres[] = new EqualCriteria('status', ProductStatusUtil::STATUS_SUSPENDED);
                }
                $this->repo->pushCriteria(new OrCriteria($statusOrWheres));
                continue;
            }
            if($key == 'search') {
                if(!empty($value) && is_string($value) && $value != '__q__'){
                    $this->repo->pushCriteria(new OrCriteria([
                        new WhereLikeCriteria('name', "%$value%"),
                        new WhereLikeCriteria('product_code', "%$value%"),
                        new WhereLikeCriteria('barcode', "%$value%"),
                    ]));
                }
                continue;
            }
            if($key == 'exclude'){
                if(is_array($value)){
                    foreach ($value as $k => $v) {
                        if(is_array($v)){
                            $this->repo->pushCriteria(new NotInArrayCriteria($k, $v));
                        }else{
                            $this->repo->pushCriteria(new NotEqualCriteria($k, $v));
                        }
                    }
                }
                continue;
            }
            if($key == 'added_relations'){
                $addedRelations = $value;
                continue;
            }
            $this->repo->pushCriteria(new EqualCriteria($key, $value));
        }
        return $this->repo->getAutoSuggestProductsV2(50, $addedRelations)->map($this->productFormatterCallback());
    }

    public function productFormatterCallback(){
        return function($product){
            $item = [];
            $product->name = $product->name;
            $item['text'] = $product->name.' #'. (!empty($product->product_code) ? $product->product_code : $product->id);
            $item['name'] = $product->name;
            $item['id'] = $product->id;
            $item['product_code'] = $product->product_code;
            $item['unit_template_id'] = $product->unit_template_id;
            $item['average_price'] = $product->average_price;
            $item['unit_price'] = $product->unit_price;
            if($product->relationLoaded('activeBoms')){
                $item['activeBoms'] = $product->activeBoms;
            }
            if(!empty($product->unitTemplate)){
                $item['units'] = [
                    [
                        "id" => 0,
                        "small_name" => $product->unitTemplate->unit_small_name ?? '',
                        "factor_name" => $product->unitTemplate->main_unit_name ?? '',
                        "unit_name" => $product->unitTemplate->main_unit_name ?? '',
                        "unit_small_name" => $product->unitTemplate->unit_small_name ?? '',
                        "factor" => 1,
                    ]
                ];
                $item['units'] = array_merge(
                    $item['units'],
                    $product->unitTemplate->factors->map(function($factor){
                        return [
                            "id" => $factor->id,
                            "small_name" => $factor->small_name ?? '',
                            "factor_name" => $factor->factor_name ?? '',
                            "factor" => $factor->factor ?? 1,
                            "unit_small_name" => $factor->small_name ?? '',
                            "unit_name" => $factor->factor_name ?? '',
                        ];
                    })->toArray()
                );
            }
            $item['htmlJson'] = htmlspecialchars(json_encode($item), ENT_QUOTES, 'UTF-8');
            return $item;
        };
    }

    public function checkBarcodeExists($barcode , $checkItemBarcode = false){
        return $this->repo->findByBarcode($barcode , $checkItemBarcode);
    }

    public function checkProductCodeExists($code){
        return $this->repo->findByCode($code);
    }

    public function validateImportData($data, $validationRules, $headersInfo, $entityKey, $rowCount, $isUpdate, $updateUsing, &$invalidData, &$willBeUpdated , $customData=null)
    {
        $entityRulesGetter = resolve(EntityRulesGetter::class);
        $validator = new DaftraValidator('product', $entityRulesGetter);
        $validator->setData($data);
        if (!$validator->isValid()) {
            foreach ($validator->getErrors() as $key => $errors) {
                foreach ($errors as $error) {
                    $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount,' : '.$error);
                }
            }
            return false;
        }
        if ($customData){
            $itemGroupId= \request()->entity_id;
            $itemGroupService = resolve(ItemGroupService::class);
            $attributes = [];
            foreach ($customData as $attribute){
                $attributes[]= [
                    'attribute'=>$attribute['attribute'],
                    'option'=>$attribute['value'],
                ];
            }
            $productService = resolve(ProductService::class);
            $product =$productService->checkProductOwnsAttributes($itemGroupId,$attributes);
            if ($product && $product->product_code !== $data['product_code']){
                $invalidData[$rowCount][] = sprintf(__t("The attribute options for the product named '#%s' have already been selected and cannot be repeated ") ,$product->product_code);
                return false;
            }
        }
        return true;
    }

    public function getProductsAveragePrices($productsIds) {
        $products = $this->repo->getProductsAveragePrices($productsIds);
        $result = [];
        foreach($products as $product){
            $result[$product['id']] = $product['average_price'];
        }
        return $result;
    }

    public function checkProductOwnsAttributes($itemGroupId,array $attributes )
    {
       return $this->repo->checkProductOwnsAttributes($itemGroupId  , $attributes);
    }

    public function getProductsWithUnitTemplate(array $productIds) : Collection {
        return $this->repo->getProductsWithUnitTemplate($productIds);
    }


    public function updateProductsBrand($brandData){
        return $this->repo->updateProductsBrand($brandData);
    }

    /**
     * Get products with offers and proper structure
     *
     * @param array $filters Optional filters (e.g., ['hasOffer' => true])
     * @return array
     */
    public function getWithOffers($filters = []): array
    {
        $entity = $this->structureGetter->buildEntity(EntityKeyTypesUtil::PRODUCT_ENTITY_KEY);
        $productsPaginator = $this->listWithStructure($entity);
        $products = $productsPaginator['data'];

        if (!empty($products)) {
            $productIds = array_column($products, 'id');
            $offersByProduct = $this->offerService->getOffersByProductIds($productIds);
            foreach ($products as $product) {
                $product->offers = $offersByProduct[$product->id] ?? [];
            }
            $products = $this->applyFilters($products, $filters);
            $productsPaginator['data'] = $products;
        }

        return $productsPaginator;
    }

    private function applyFilters($products, $filters = []): array
    {
        if (empty($filters)) {
            return $products;
        }
        if (isset($filters['offersOnly']) && $filters['offersOnly'] == 1) {
            return array_filter($products, function ($product) {
                return !empty($product->offers);
            });
        }
        if (isset($filters['activeOnly']) && $filters['activeOnly'] == 1) {
            return array_filter($products, function ($product) {
                return $product->status === ProductStatusUtil::STATUS_ACTIVE;
            });
        }
        return $products;
    }

    /**
     * List entities with structure
     *
     * @param mixed $structure
     * @return array
     */
    protected function listWithStructure($structure): array
    {
        return $this->listAction->handle($structure, 2, $this->formParser)->toArray();
    }

    public function createService($data)
    {
        $validationResult = $this->validateServiceData($data);
        if (!$validationResult['status']) {
            return $validationResult;
        }
        $service = $this->repo->createService($data);
        if(isset($data['photo'])){
            $attachmentService = resolve(AttachmentsService::class);
            $imagesIds = explode(',', $data['photo']);
            if(!empty($imagesIds)){
                $attachmentService->save('product', $service['data']->id, $imagesIds); 
            }
        }
        return $service;
    }

    public function updateService($id, $data)
    {
        $validationResult = $this->validateServiceData($data);
        if (!$validationResult['status']) {
            return $validationResult;
        }
        $service = $this->repo->updateService($id, $data);
        if(isset($data['photo'])){
            $attachmentService = resolve(AttachmentsService::class);
            $imagesIds = explode(',', $data['photo']);
            if(!empty($imagesIds)){
                $attachmentService->save('product', $service['data']->id, $imagesIds); 
            }
        }
        return $service;
    }

    public function deleteService($id)
    {
        return $this->repo->deleteService($id);
    }

    private function validateServiceData($data)
    {
        $validator = Validator::make($data, [
            'name' => 'required',
            'category_ids' => 'array',
            'assigned_staff_ids' => 'array',
            'duration_minutes' => 'required|numeric',
            'unit_price' => 'required|numeric',
            'tax1' => 'numeric',
            'tax2' => 'numeric',
            'photo' => 'numeric|nullable'
        ]);
        if ($validator->fails()) {
            return [
                'status' => false,
                'message' => $validator->errors()->first()
            ];
        }
        return [
            'status' => true
        ];
    }

    public function listServices($filters = [])
    {
        $result = $this->repo->listServices($filters);
        if(count($result) > 0){
            $this->formatService($result);
        }
        return $result;
    }

    public function getService($id)
    {
        $service = $this->repo->find($id);
        if(!$service){
            return null;
        }else{
            $this->formatService([$service]);
        }
        return $service;
    }

    private function formatService($services)
    {
        $attachmentService = resolve(AttachmentsService::class);
        if($services instanceof EloquentCollection){
            $serviceAttachments = $attachmentService->getProductsDefaultImages($services->pluck('id')->toArray());
        }else{
            $serviceAttachments = $attachmentService->getProductsDefaultImages([$services[0]->id]);
        }
        foreach ($services as &$service) {
            $service->price = format_price($service->unit_price);
            $service->photo = $attachmentService->resolveProductDefaultImagePath($service, $serviceAttachments);
            $service->assigned_staffs = $service->assignedStaffs->map(function ($staff) {
                return [
                    'id' => $staff->id,
                    'name' => $staff->name,
                    'avatar' => $staff->photo ? $staff->getImageAttribute() : AvatarURLGenerator::generate($staff->name, $staff->id, 30, null),
                ];
            });
            if(!isset($service->category) && !isset($service->category_id)){
                $service->category_ids = $service->categories->pluck('id')->toArray();
                unset($service->categories);
            }
            $service->attachment = $service->getDefaultAttachment();
            unset($service->assignedStaffs);
            $service->service_form = $service->serviceForm;
            if(!is_null($service->serviceForm)){
                $service->service_form_manage_records_link = '/v2/owner/entity/le_custom_data_service_form_' . $service->serviceForm->id . '/list';
                $service->service_form_manage_printable_templates_link= '/v2/owner/templates/pdf/list/le_custom_data_service_form_' . $service->serviceForm->id;
            }
        }
    }
}
