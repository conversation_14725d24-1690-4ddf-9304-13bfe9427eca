<?php

namespace App\Services;

use App\Helpers\FilterOperations;
use App\Repositories\CategoryRepository;
use App\Utils\CategoryTypesUtil;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CategoryService extends BaseService {

    var $repo;
    var $storeRules = [
        'name' => 'required'
    ];
    var $updateRules = [
        'name' => 'required'
    ];


    function getFormRelatedRepos()
    {
        return [
            'categories' => ['class' => 'App\Repositories\CategoryRepository', 'columns' => ['id', 'name']]
        ];
    }

    /**
     * get filters input dynamic data and sets the filter inputs values from parameters
     * @return array
     */
    function getFilters()
    {

        return  $filters = [
        'name' => [ 'type' => 'text', 'filter_options' => ['operation' => FilterOperations::IN_STRING_FILTER_OPERATION,'field' => 'name', 'model' => 'Category']],
        'created' => ['type' => 'dateRange', 'filter_options' => ['operation' => FilterOperations::DATE_RANGE_FILTER_OPERATION,'field' => 'created', 'model' => 'category'] ],
        ];
    }

    /**
     * returns service sort fields
     * @return array
     */
    function getSortFields()
    {
        return [
        'fields' => [
            'name' => ['type' => 'string','title' => 'Name', 'field' => 'name', 'direction' => 'DESC'],
            'created' => ['type' => 'date','title' => 'Created Date', 'field' => 'created', 'direction' => 'ASC']
        ],
        'active' => ['type' => 'string', 'field' => 'name', 'direction' => 'DESC']
      ];

    }

    public function __construct(CategoryRepository $repo)
    {
        parent::__construct($repo);
    }


    public function searchProductCategories($categoryName){
        return $this->repo->searchProductCategories($categoryName);
    }
    public function findOrCreateByName($categoryName , $branchId = null){
        return $this->repo->findOrCreateByName($categoryName , $branchId);
    }

    public function handleItemGroupCategory($itemGroup)
    {
        $response = $this->repo->findOrCreateByName($itemGroup->category);

        $isNew = $response['isNew'];
        $category = $response['category'];
        $itemsCategories = [];
        foreach ($itemGroup->items as $item){
            $itemsCategories[] = [
                'item_id'=>$item->id ,
                'item_type'=>CategoryTypesUtil::CategoryTypeProduct,
                'branch_id'=> $itemGroup->branch_id,
                'created'=>Carbon::now(),
            ];
        }
        if ($isNew){
            $category->products()->sync($itemsCategories);
        }else{
            $category->products()->attach($itemsCategories);
        }
    }

    public function getCategoriesWithProductsCount()
    {
        return $this->repo->getCategoriesWithProductsCount();
    }
}
