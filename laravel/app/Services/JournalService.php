<?php

namespace App\Services;

use App\Cache\ImportCache;
use App\Exceptions\Import\ImportErrorException;
use App\Facades\Settings;
use App\Helpers\Import\JournalMapper;
use App\Models\EntityField;
use App\Repositories\CostCenterRepository;
use App\Repositories\EntityRepository;
use App\Repositories\JournalAccountRepository;
use App\Repositories\JournalRepository;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;

class JournalService extends BaseService
{
    public bool $importUsingJsonFile = true;

    public function __construct(
        JournalRepository $repo,
        private MappingService $importService,
        EntityRepository $entityRepo = null,
        CustomDataService $customDataService = null,
        private CostCenterRepository $costCenterRepository,
        private JournalAccountRepository $journalAccountRepository,
    ) {
        parent::__construct($repo, $entityRepo);
    }

    public function getImportDownloadSample(): array
    {
        return ['path' => '/samples/journal_sample.csv', 'file_name' => __t('Journal Sample') . '.csv'];
    }

    public function beforeInsert($insertedData, $extraData)
    {
        return JournalMapper::map($insertedData, $extraData);
    }

    public function getImportFieldsLabel()
    {
        $ret = [];
        $importedFields = $this->importService->getImportEntityFields("journal");
        foreach ($importedFields as $field) {
            if ($field instanceof \App\Adapters\EntityFieldAdapterCustomField) {
                continue;
            }

            $ret[$field->id] = $field;
            if ($field->key === 'journals.description') {
                $obj = new EntityField();
                $obj->label = "Currency";
                $obj->key = "journals.currency_code";
                $obj->validation_rules = ["nullable|exists:currencies,code"];
                $obj->allowed_values = [];
                $obj->id = -4;
                $obj->db_name = "currency_code";
                $obj->relation = null;
                $ret[$obj->id] =  $obj;
            } elseif ($field->key === 'journal_transactions.description') {
                $field->db_name = 'transaction_description';
                $ret[$field->id] = $field;

                if (Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::ENABLE_TAGS_IN_JOURNAL)) {
                    $obj = new EntityField();
                    $obj->label = "Tags";
                    $obj->key = "journal_transactions.tags";
                    $obj->validation_rules = ["nullable"];
                    $obj->allowed_values = [];
                    $obj->id = -1;
                    $obj->db_name = "tags";
                    $obj->relation = null;
                    $ret[$obj->id] =  $obj;
                }

                if ('0' !== Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::DISPLAY_COST_CENTERS_IN_JOURNALS)) {
                    $obj = new EntityField();
                    $obj->label = "Cost Center";
                    $obj->key = "journal_transactions.cost_center";
                    $obj->validation_rules = ["nullable|exists:currentSite.cost_centers,id"];
                    $obj->allowed_values = [];
                    $obj->id = -2;
                    $obj->db_name = "cost_center";
                    $obj->relation = null;
                    $ret[$obj->id] =  $obj;
                }
            }

            if ($field->key == "journal_transactions.currency_debit" || $field->key == "journal_transactions.currency_credit") {
                $obj = new EntityField();
                $obj->label = ($field->key == "journal_transactions.currency_debit") ? $field->label: __('Credit ');
                $obj->key = $field->key;
                $obj->validation_rules = ['required|numeric|min:0'];
                $obj->allowed_values = [];
                $obj->id = $field->id;
                $obj->db_name = $field->db_name;
                $obj->relation = null;
                $ret[$obj->id] =  $obj;
            }
            if ($field->key == "journal_transactions.tax_id" && '0' === Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::TAX_IN_JOURNALS)) {
                unset($ret[$field->id]);
            }
        }

        return $ret;
    }

    public function preProcessDataForImportUsingExtraData($data, $extraData)
    {
        if (isset($data['cost_center'])) {
            $oldValue = $data['cost_center'];
            if (!array_key_exists($oldValue, ImportCache::$cacheData['cost_centers'])) {
                $costCenter = $this->costCenterRepository->getCostCenterByCodeORName($data["cost_center"]);
                ImportCache::$cacheData['cost_centers'][$oldValue] = $costCenter;
            }
            $data['cost_center'] = ImportCache::$cacheData['cost_centers'][$oldValue]->id ?? $data['cost_center'];
        }
        if (isset($data['journal_account_id'])) {
            $oldValue = $data['journal_account_id'];
            if (!array_key_exists($oldValue, ImportCache::$cacheData['journal_account_ids'])) {
                $journalAccount = $this->journalAccountRepository->getJournalAccountByCodeOrTranslatedName($data["journal_account_id"]);
                ImportCache::$cacheData['journal_account_ids'][$oldValue] = $journalAccount;
            }
            $data['journal_account_id'] = ImportCache::$cacheData['journal_account_ids'][$oldValue]->id ?? $data['journal_account_id'];
        }
        return $data;
    }

    public function getJsonImportData($request): array
    {
        $jsonImportData = ['Journal' => $request->get('Journal'), 'JournalTransaction' => $request->get('JournalTransaction')];
        if (Settings::getValue(PluginUtil::AccountingPlugin, SettingsUtil::ENABLE_TAGS_IN_JOURNAL)) {
            $jsonImportData['ItemsTag'] = $request->get('ItemsTag', []);
        }
        return $jsonImportData;
    }

    public function validateAllItems($data, $extra_data, $fields)
    {
        $visitedInvoice = [];
        $ignoreErrors = false;
        if (!empty($extra_data['ignore_errors'])) {
            $ignoreErrors = true;
        }

        if (count($data) > 10000) {
            throw new ImportErrorException([__t("The maximum number of records in the sheet is 10000")]);
        }

        $invalidData= [];
        $mappedData = [];

        foreach ($data as  $item) {
            $mappedData[$item["number"]][] = $item;
        }

        $mustEqual = ["date", "currency_code", "number", "description"];


        $first_row = 1;
        if (!$extra_data["import_first_row"]) {
            $first_row++;
        }

        $row = 0;
        foreach ($mappedData as $key =>  $invoice) {
            $values = [];
            if (count($invoice) > 500) {
                $invalidData[($row + $first_row - 1)] = sprintf(__t("The number of transaction lines you can import in each Journal is 500 at row #"), ($row + $first_row -1)) ;
                if (!$ignoreErrors) {
                    throw new ImportErrorException($invalidData);
                }
                $row++;
                continue;
            }
            foreach ($invoice as $item) {
                foreach ($mustEqual as $field) {
                    if (!isset($values[$field])) {
                        $values[$field] = $item[$field];
                    } else if (trim($values[$field]) !== trim($item[$field]) && !isset($visitedInvoice[$key])) {
                        $invalidData[($row + $first_row - 1)] =  sprintf(__t("The %s should be the same for all journal items that have the same journal code, at row #%d."), __t($this->getFieldLabel($field, $fields)), ($row + $first_row - 1));
                        $visitedInvoice[$key] = 1;
                        if (!$ignoreErrors) {
                            throw new ImportErrorException($invalidData);
                        }
                    }
                }
                $row++;
            }
        }
        return $invalidData;
    }

    private function getFieldLabel($field, $fields): string
    {
        foreach ($fields as $item) {
            if ($field === $item->db_name) {
                return $item->label;
            }
        }

        return $field;
    }

    public function getValidationMessages() : array
    {
        return [
            'exists' =>__t('The selected :attribute is not available.'),
            'min' => __t( 'The :attribute must be at least :min.'),
        ];
    }
}
