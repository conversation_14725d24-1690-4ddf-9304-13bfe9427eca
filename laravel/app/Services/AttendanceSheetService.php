<?php

namespace App\Services;

use App\Entities\AttendanceCalculationCriteria;
use App\Entities\AttendanceCalculationMultipleCriteria;
use App\Events\NewDayCalculationEvent;
use App\Exceptions\AttendanceCalculationNoEmployeesException;
use App\Exceptions\AttendanceSheetApproved;
use App\Exceptions\AttendanceSheetCanNotBeDeletedOrUnApprovedException;
use App\Exceptions\EntityNotFoundException;
use App\Facades\Permissions;
use App\Facades\Plugins;
use App\Helpers\FilterOperations;
use App\Facades\Formatter;
use App\Models\Branch;
use App\Repositories\AttendanceSheetRepository;
use App\Repositories\BranchRepository;
use App\Repositories\Criteria\ActiveStaff;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\OrderByCriteria;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\EntityRepository;
use App\Repositories\LeaveTypeRepository;
use App\Repositories\ShiftRepository;
use App\Repositories\StaffRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Requests\AttendanceCalculation\NewDayCalculationEventRequest;
use App\Requests\CreateJobResultRequest;
use App\Requests\DefaultRequest;
use App\Services\AttendanceCalculator\Entities\AttendanceSheet;
use App\Services\Traits\BulkDeleteService;
use App\Utils\AttendanceSheetStatusTypesUtil;
use App\Utils\BranchStatusUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PaySlipStatusUtil;
use App\Utils\PermissionUtil;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use Closure;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\Criteria\CustomFind;
use Ramsey\Uuid\Uuid;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Exceptions\PageAccessNotAllowedException;
use App\Facades\Staff;
use App\Repositories\Criteria\BetweenCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Criteria\OrCriteria;
use App\Utils\EntityFieldUtil;
use Izam\Entity\Repository\EntityViewsLogsRepository;

/**
 * AttendanceSheetService Class attendance sheet service
 * @package App\Services
 * <AUTHOR> Faesal <<EMAIL>>
 */
class AttendanceSheetService extends BaseService
{
    use BulkDeleteService;
    var $filters = [];
    /** @var AttendanceSheetRepository */
    var $repo;
    protected $showRouteName = 'owner.attendance_sheets.show';
    /**
     * @var StaffRepository
     */
    private $staffRepository;
    /**
     * @var ShiftRepository
     */
    private $shiftRepository;
    /**
     * @var DepartmentRepository
     */
    private $departmentRepository;
    /**
     * @var DesignationRepository
     */
    private $designationRepository;
    /**
     * @var BranchRepository
     */
    private $branchRepository;
    /**
     * @var LeaveTypeRepository
     */
    private $leaveTypeRepository;

    /**
     * AttendanceSheetService constructor.
     * @param AttendanceSheetRepository $repo
     * @param StaffRepository $staffRepository
     * @param LeaveTypeRepository $leaveTypeRepository
     * @param ShiftRepository $shiftRepository
     * @param DepartmentRepository $departmentRepository
     * @param DesignationRepository $designationRepository
     * @param BranchRepository $branchRepository
     * @param EntityRepository|null $entityRepo
     */
    public function __construct(
        protected StaffService $staffService,
        AttendanceSheetRepository $repo,
        StaffRepository $staffRepository,
        LeaveTypeRepository $leaveTypeRepository,
        ShiftRepository $shiftRepository,
        DepartmentRepository $departmentRepository,
        DesignationRepository $designationRepository,
        BranchRepository $branchRepository,
        EntityRepository $entityRepo = null ,
    ) {
        parent::__construct($repo, $entityRepo);
        $this->shiftRepository = $shiftRepository;
        $this->departmentRepository = $departmentRepository;
        $this->designationRepository = $designationRepository;
        $this->branchRepository = $branchRepository;
        $this->leaveTypeRepository = $leaveTypeRepository;
        $this->staffRepository = $staffRepository;
    }

    public function getFormData($id = false)
    {
        $data = parent::getFormData($id);

        $staffService = resolve(StaffService::class);
        if(isset($data['form_record']->staff_id) && !$staffService->isAccessableStaffId($data['form_record']->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }

        if (old('employees') || $id) {
            $staffId = old('employees') ? old('employees') : $data['form_record']->staff_id;
            $staffs = $this->staffRepository->find($staffId);
            if (!$staffs) {
                throw new EntityNotFoundException(__t("Employee"));
            }
            foreach ($staffs as $staff) {
                $data['staffOptions'][] = Staff::getStaffOptionFormated($staff) ;;
            }
        }
        return $data;
    }

    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $fromDate = Formatter::formatForDB($request['from_date'], 'date');
        $toDate = Formatter::formatForDB($request['to_date'], 'date');
        // $this->validateNoSheetInSamePeriod($fromDate, $toDate);
        $jobId = Uuid::uuid4()->toString();

        $attendanceCalculationCriteria = new AttendanceCalculationMultipleCriteria(
            $request['employees'] ?? [],
            $request['branches'] ?? [],
            $request['departments'] ?? [],
            $request['designations'] ?? [],
            $request['shifts'] ?? [],
    $request['exclude_criteria'] ?? []
        );

        $staffs = $this->staffRepository->getAttendanceDayMultipleCriteriaStaff($attendanceCalculationCriteria);
        $authUser = getAuthOwner();
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $staffService = resolve(StaffService::class);
            $accessableStaffMap = $staffService->getAccessibleStaffIds($authUser['staff_id'],false,true);
            $staffs = $staffs->filter(fn($staff, $key) => isset($accessableStaffMap[$staff->id]));
        }
        if($staffs->isEmpty())
        {
            throw new AttendanceCalculationNoEmployeesException;
        }

        $calculateRequest = new NewDayCalculationEventRequest(
            $fromDate,
            $toDate,
            $attendanceCalculationCriteria,
            false,
            true,
            $jobId
        );
        event(new NewDayCalculationEvent($calculateRequest));
        return ['jobId' => $jobId, 'from_date' => $request['from_date'], 'to_date' => $request['to_date'], 'name' => $attendanceCalculationCriteria->getEmployeeIds()];
    }

    /**
     * @param $createJobResultRequest CreateJobResultRequest
     * @return null|string
     * @throws \Exception
     */
    function createJobResult($createJobResultRequest){

        return $this->repo->createJobResult($createJobResultRequest);
    }

    /**
     * @param $staffIds
     * @param $fromDate
     * @param $toDate
     * @return
     */
    public function getStaffSheetsInPeriod($staffIds,$fromDate, $toDate)
    {
        return $this->repo->getStaffSheetsInPeriod($staffIds, $fromDate, $toDate);
    }

	public function getAttendanceSheetsFromArray($attendanceSheetsArray) {
		$attenanceSheets = [];
		foreach ($attendanceSheetsArray as $sheet) {
			$attenanceSheets[] = new AttendanceSheet($sheet->id, $sheet->staff_id, $sheet->date_from, $sheet->date_to, $sheet->status, $sheet->fiscal_start_date);
		}
		return $attenanceSheets;
	}

    /**
     * {@inheritDoc}
     */
    public function getSortFields()
    {
        return [
            'fields' => [
                'created' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'created',
                    'direction' => 'DESC'
                ],
                'name' => [
                    'title' => __t('Employees'),
                    'direction' => 'ASC',
                    'field' => 'name',
                    'relation'=> [
                        'join' => ['fromAlias' => 'staffs', 'join' => 'staffs.id', 'alias' => 'attendance_sheets.staff_id'],
                        'tableName' => 'staffs',
                        'tableField' => 'name'
                    ]
                ],
                'auto_id' => [
                    'title' => __t('ID'),
                    'field' => 'auto_id',
                    'hidden' => true,
                    'direction' => 'DESC'
                ],
            ],
            'active' => [
                'field' => 'auto_id',
                'direction' => 'DESC',
            ]
        ];
    }


    /**
     * {@inheritDoc}
     */
    protected function getListingConditions()
    {
        $listingConditions = parent::getListingConditions();
        $authUserID = getAuthOwner('staff_id');

        if(Permissions::isOwnerOrAdmin()){
            return $listingConditions;
        }

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $staffService = resolve(StaffService::class);
            $listingConditions[] = [
                "operation" => FilterOperations::IN_FILTER_OPERATION,
                "field" => "staff_id",
                "value" => $staffService->getAccessibleStaffIds($authUserID,true)
            ];
        }

        if(Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS))  {
            return $listingConditions;
        }

        if (Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE)){
                if($this->staffService->getAuthDepartmentId()){
                $listingConditions[] =[
                    'operation' => FilterOperations::RAW_OPERATION,
                    'field' => 'attendance_sheets.staff_id IN ( select staff_id from staff_info where department_id = ? )',
                    'value' => [$this->staffService->getAuthDepartmentId()],
                ];
                return $listingConditions;
            }
        }

        $listingConditions[] = [
            "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
            "field" => "staff_id",
            "value" => $authUserID
        ];

        return $listingConditions;
    }

    function getWithRelations(){
        $eagerRelations = ['employee', 'staffInfo'];
        if(ifPluginActive(PluginUtil::HRM_PAYROLL_PLUGIN)){
            $eagerRelations[] = 'payslips';
        }
        return $eagerRelations;
    }

    public function listing(): array
    {
        $listingData = parent::listing(); // TODO: Change the autogenerated stub
        if(isset($this->parameters['jobId']))
        {
            $jobData = $this->getJobResult($this->parameters['jobId']);
            if($jobData)
            {
                $listingData['jobData'] = json_decode($jobData->data, true);
                $listingData['staff'] = $this->staffRepository->pushCriteria(new ActiveStaff())->list([], true);
            }
        }
        $listingData['authDeptId'] = $this->staffService->getAuthDepartmentId();

        return $listingData;
    }

    public function listingForUser($staffId, $startDateFrom = null, $endDateFrom = null, $startDateTo = null, $endDateTo = null , $orderByDir = 'desc'){
        $this->repo->pushCriteria(new EqualCriteria('staff_id', $staffId));
        if(!empty($startDateFrom) || !empty($endDateFrom)){
            $this->repo->pushCriteria(new BetweenCriteria(
                'date_from',
                !empty($startDateFrom) ? $this->formatDateForDb($startDateFrom) : null,
                !empty($endDateFrom) ? $this->formatDateForDb($endDateFrom) : null
            ));
        }
        if(!empty($startDateTo) || !empty($endDateTo)){
            $this->repo->pushCriteria(new BetweenCriteria(
                'date_to',
                !empty($startDateTo) ? $this->formatDateForDb($startDateTo) : null,
                !empty($endDateTo) ? $this->formatDateForDb($endDateTo) : null
            ));
        }
        $this->repo->pushCriteria(new OrderByCriteria('created', $orderByDir));
        $attendanceSheets = $this->repo->all(); 

        $attendanceSheetsIds = [];
        foreach($attendanceSheets as $attendanceSheet){
            $attendanceSheetsIds[] = $attendanceSheet->auto_id;
        }
        $entityViewsLogsRepository = resolve(EntityViewsLogsRepository::class);
        $currentStaffIds = getAuthOwner('staff_id');
        $entityViewsLogs = $entityViewsLogsRepository->getEntityViewsLogs(EntityKeyTypesUtil::ATTENDANCE_SHEET, $attendanceSheetsIds, $currentStaffIds);
        foreach($attendanceSheets as &$attendanceSheet){
            $attendanceSheet['read_at'] = $entityViewsLogs[$attendanceSheet->auto_id]['read_at']?? null;
        }
        return $attendanceSheets;
    }

    private function formatDateForDb($value){
        return Formatter::formatForDb($value, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
    }

    public function getAttendanceMobileAppViewData($sheetId){
        $sheet = $this->repo->pushCriteria(new OrCriteria([
            new EqualCriteria('auto_id', $sheetId),
            new EqualCriteria('id', $sheetId),
        ]))->first()->load('attendanceDays')->load('attendanceDays.leaveType')->load('attendanceFlags');

        $takenLeaves = [];
        foreach($sheet->attendanceDays as $day){
            if(!empty($day->leaveType)){
                if(!isset($takenLeaves[$day->leaveType->id])){
                    $takenLeaves[$day->leaveType->id] = [
                        'leave_type_id' => $day->leaveType->id,
                        'count' => 0,
                        'color' => $day->leaveType->color,
                        'name' => $day->leaveType->name,
                    ];
                }
                $takenLeaves[$day->leaveType->id]['count'] += 1;
            }
        }
        /** @var AttendanceReportsService $attendanceReportsService */
        $attendanceReportsService = resolve(AttendanceReportsService::class);
        $leaves = $attendanceReportsService->getStaffAttendanceBalanceReportData($sheet->staff_id , $sheet->date_from, $sheet->date_to);
        foreach ($takenLeaves as $key => $takenLeave) {
            $currentLeave = array_filter($leaves, function($leave) use ($key){
               return $key == $leave['leaveTypeId'];
            });
            $currentLeave = array_values($currentLeave);
            if (!empty($currentLeave)){
                $takenLeaves[$key]['taken'] = $currentLeave[0]['taken'];
                $takenLeaves[$key]['remaining'] = $currentLeave[0]['remaining'];
            }
        }
        $sheet['taken_leaves'] = array_values($takenLeaves);
        return $sheet;
    }

    private function getJobResult($jobId)
    {
        $result = $this->repo->getJobResult($jobId);
        return $result;
    }

    /**
     * {@inheritDoc}
     */
    public function getFormRelatedData()
    {
        return [
            'staff_list' => $this->staffRepository->list(['id', 'name']),
            'shifts' => $this->shiftRepository->list(['id', 'name']),
            'departments' => $this->departmentRepository->pushCriteria(new EqualCriteria('active' , 1))->list(['id', 'name']),
            'designations' => $this->designationRepository->pushCriteria(new EqualCriteria('active' , 1))->list(['id', 'name']),
            'branches' =>
                Plugins::pluginActive(PluginUtil::BranchesPlugin) ? \App\Facades\Branch::getStaffBranches() : []
        ];
    }

    function getFilters()
    {
        $fromDate = $this->parameters['date_from']??null;
        $toDate = $this->parameters['date_to']??null;
        if ($fromDate && $toDate) {
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['date_from'] = $toDate;
                $this->parameters['date_to'] = $fromDate;
            }
        }

        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            $staffList = [];
            if (isset($this->parameters['name']) && !empty($this->parameters['name'])) {
                $staff = $this->staffRepository->find($this->parameters['name']);
                foreach ($staff as $employee)
                {
                    $staffList[]= Staff::getStaffOptionFormated($employee);
                }
            }
            $this->filters = [
                'type' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-6',
                    'options' => AttendanceSheetStatusTypesUtil::getStatusesText(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('All Status'),
                        'id' => 'status'
                    ],
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'status'
                    ]
                ],
                'date_from' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-4',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => false,
                    'attributes' => ['placeholder' => __t('From Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "date_from"
                    ]
                ],
                'date_to' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-4',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => false,
                    'attributes' => ['placeholder' => __t('To Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "date_to"
                    ]
                ],
            ];
            $this->filters['created'] = [
                'simple' => true, /* displays the filter outside the toggle */
                'type' => 'dateRange',
                'div' => 'col-md-4',
                'after' => '</div>',
                'before' => '<div class="form-group form-group-icon">',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'label' => false,
                'inputClass' => 'form-control bg-white',
                'attributes' => ['placeholder' => __t('Date of Creation'),'readonly' => 'readonly'],
                "filter_options" => [
                    "operation" => FilterOperations::DATE_RANGE_FILTER_OPERATION,
                    "field" => "attendance_sheets.created"
                ]
            ];
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $this->filters ['branch_id'] = [
                    'simple' => false, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-4',
                    'options' =>  \App\Facades\Branch::getStaffBranchesSuspended(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('All Branches'),
                        'id' => 'branch_id'
                    ],
                    'filter_options' => [
                        'model' => 'employee',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'branch_id'
                    ]
                ];
            }
            $this->filters ['designation_id'] = [
                'simple' => false, /* displays the filter outside the toggle */
                'label' => false,
                'after' => '</div>',
                'before' => '<div class="form-group">',
                'div' => 'col-md-4',
                'options' => $this->designationRepository->list(['id', 'name']),
                'type' => 'select',
                'inputClass' => 'select-filter',
                'attributes' => [
                    'placeholder' => __t('All Designations'),
                    'id' => 'designation_id'
                ],
                'filter_options' => [
                    'model' => 'staffInfo',
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'designation_id'
                ]
            ];
            $this->filters ['department_id'] = [
                'simple' => false, /* displays the filter outside the toggle */
                'label' => false,
                'after' => '</div>',
                'before' => '<div class="form-group">',
                'div' => 'col-md-4',
                'options' => $this->departmentRepository->list(['id', 'name']),
                'type' => 'select',
                'inputClass' => 'select-filter',
                'attributes' => [
                    'placeholder' => __t('All Departments'),
                    'id' => 'department_id'
                ],
                'filter_options' => [
                    'model' => 'staffInfo',
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'department_id'
                ]
            ];
            $this->filters ['shift_id'] = [
                'simple' => false, /* displays the filter outside the toggle */
                'label' => false,
                'after' => '</div>',
                'before' => '<div class="form-group">',
                'div' => 'col-md-4',
                'options' => $this->shiftRepository->list(['id', 'name']),
                'type' => 'select',
                'inputClass' => 'select-filter',
                'attributes' => [
                    'placeholder' => __t('All Shifts'),
                    'id' => 'shift_id'
                ],
                'filter_options' => [
                    'model' => 'staffInfo',
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'attendance_shift_id'
                ]
            ];
            if (Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS)) {
                $this->filters = array_merge(['name[]' =>[
                    'param_name' => 'name',
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'before' => '<div class="form-group-icon form-group">',
                    'label' => __t('Employee'),
                    'attributes' => [
                        'placeholder' => __t(getEmployeesFieldPlaceholder()),
                        'id' => 'staffSelect',
                        'multiple' => 'multiple',
                        'data-staff-url' => route('owner.staff.search', ['allow_inactive' => true, 'get_branch_suspended' => 1])
                    ],
                    'div' => 'col-md-6',
                    'options' => $staffList,
                    'type' => 'selectStaff',
                    "filter_options" => [
                        "operation" => FilterOperations::IN_FILTER_OPERATION,
                        "field" => "staff_id"
                    ],
                ]], $this->filters);
            }
            return $this->filters;
        }
    }

    /**
     *
     * @param AttendanceSheet[] $sheetsArr
     */
    public function updateSheetsData($sheetsArr)
    {
        /** @var AttendanceSheet[] $sheets */
        $sheets = [];
        foreach ($sheetsArr as $empSheets) {
            /** @var AttendanceSheet $sheet */
            foreach ($empSheets as $sheet) {
                $sheets[$sheet->getId()] = $sheet;
            }
        }
        $sheetsData = $this->repo->getSheetsData(array_keys($sheets));
        foreach ($sheetsData as $sheetData) {
            $sheets[$sheetData->attendance_sheet_id]
                ->setTotalWorkingDays($sheetData->working_days)
                ->setActualWorkingHours($sheetData->actual_working_hours)
                ->setExpectedWorkingHours($sheetData->expected_working_hours)
                ->setTotalAbsentDays($sheetData->absence_days)
                ->setTotalPresentDays($sheetData->present_days)
                ->setTotalLeaves($sheetData->leaves)
                ->setTotalSignInOnly($sheetData->sign_in_only_count)
                ->setTotalSignOutOnly($sheetData->sign_out_only_count)
                ->setTotalDelay($sheetData->total_delay_amount)
                ->setTotalDelayCount($sheetData->total_delay_count)
                ->setTotalEarlyLeave($sheetData->total_early_leave_amount)
                ->setTotalEarlyLeaveCount($sheetData->total_early_leave_count)
                ->setOffWorkingDays($sheetData->off_working_days);
        }
        $this->repo->updateSheetsData($sheets);
        $this->repo->UpdateSheetsFlags($sheets);
        $this->repo->UpdateSheetsLeaves($sheets);
    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['staff_id'] = $record->employee ? $record->employee->name : '';
        return $data;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record): array
    {
        $relations = parent::getActivityLogRelations($record);
        if ($record->employee) {
            $relations[] = new ActivityLogRelationRequest($record->employee->id, EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        }
        return $relations;
    }

    public function toggleStatus($attendanceSheetId){
        $viewData = $this->getViewData($attendanceSheetId);
        $attendanceSheet = $viewData['record'];

        $authDeptId = $this->staffService->getAuthDepartmentId();
        $hasDeptEditPermission = Permissions::checkPermission(PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE);
        $recordDeptId = $attendanceSheet->staffInfo?->department_id ?? null;
        $sameDept = isset($recordDeptId) && isset($authDeptId) && $authDeptId == $recordDeptId;

        if(
            !Permissions::isOwnerOrAdmin()
            && !Permissions::checkPermission(PermissionUtil::CHANGE_ATTENDANCE_SHEET_STATUS)
            && !($sameDept && $hasDeptEditPermission)
        ){
            throw new PageAccessNotAllowedException;
        }
        if ($attendanceSheet->status == AttendanceSheetStatusTypesUtil::APPROVED) {
            // un-approve
            if (Plugins::pluginInstalled(PluginUtil::HRM_PAYROLL_PLUGIN)) {
                $this->validateIfAttendanceSheetAssignedToPaySlip($attendanceSheet);
            }
            $attendanceSheet->status = AttendanceSheetStatusTypesUtil::PENDING;
        } else {
            $attendanceSheet->status = AttendanceSheetStatusTypesUtil::APPROVED;
        }
        return $this->update($attendanceSheet->id, new DefaultRequest(['status' => $attendanceSheet->status]));
    }

    public function updateStatusMulti($status, $attendanceSheetsIds)
    {
        return $this->repo->updateMultiStatus($status, $attendanceSheetsIds);
    }

    public function delete($id, Closure $callback = null)
    {
        $record = $this->repo->find($id);

        $staffService = resolve(StaffService::class);
        if(!Permissions::isOwnerOrAdmin() && !$staffService->isAccessableStaffId($record->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }
        if($record->status == AttendanceSheetStatusTypesUtil::APPROVED){
            throw new AttendanceSheetApproved();
        }
        if(Plugins::pluginActive(PluginUtil::HRM_PAYROLL_PLUGIN)) {
            $this->validateIfAttendanceSheetAssignedToPaySlip($record);
        }
        return parent::delete($id);
    }

    /**
     * validate if attendance sheet assigned to paySlip
     * @param Model $attendanceSheet
     * @throws AttendanceSheetCanNotBeDeletedOrUnApprovedException
     */
    public function validateIfAttendanceSheetAssignedToPaySlip($attendanceSheet)
    {
        $payslips = $attendanceSheet->payslips;
        if ($payslips->count() > 0) {
            throw new AttendanceSheetCanNotBeDeletedOrUnApprovedException($payslips->first());
        }
    }

    /**
     * Bulk undo approval for Attendance Sheets
     * @param array $itemIds List of Attendance Sheet Ids
     * @return array
     */
    public function bulkUndoApproval($itemIds){
        $results = [];
        $idsToChange = [];
        $failedNumber = 0;
        $successNumber = 0;
        $attendanceSheets = $this->repo->pushCriteria(new InArrayCriteria('id', $itemIds))->all(['*'], $this->getWithRelations());
        foreach($attendanceSheets as $attendanceSheet){
            if($attendanceSheet->status == AttendanceSheetStatusTypesUtil::PENDING){
                $results[] = [
                    'status' => false,
                    'id' => $attendanceSheet->auto_id,
                    'message' => sprintf(__t("Attendance Sheet %s is already pending"), $this->getShowAnchor($attendanceSheet)),
                ];
                $failedNumber++;
                continue;
            }
            if(ifPluginActive(PluginUtil::HRM_PAYROLL_PLUGIN)){
                try {
                    $this->validateIfAttendanceSheetAssignedToPaySlip($attendanceSheet);
                } catch (AttendanceSheetCanNotBeDeletedOrUnApprovedException $th) {
                    $results[] = [
                        'status' => false,
                        'id' => $attendanceSheet->auto_id,
                        'message' => $th->getMessage(),
                    ];
                    $failedNumber++;
                    continue;
                }
            }
            $results[] = [
                'status' => true,
                'id' => $attendanceSheet->auto_id,
                'message' => sprintf(__t('Undo approval for Attendance Sheet %s was successful'), $this->getShowAnchor($attendanceSheet)),
            ];
            $idsToChange[] = $attendanceSheet->id;
            $successNumber++;
        }
        $this->updateStatusMulti(AttendanceSheetStatusTypesUtil::PENDING, $idsToChange);
        return [
            'success_number' => $successNumber,
            'failed_number' => $failedNumber,
            'results' => $results,
        ];
    }

    /**
     * Get anchor tag to show attendance sheet
     *
     * @param $attendanceSheet
     * @return string
     */
    public function getShowAnchor($attendanceSheet){
        return "<a class='alert-link' target='_blank' href='".route($this->showRouteName, ['attendance_sheet' => $attendanceSheet->auto_id])."'><span>#". $attendanceSheet->auto_id."</span></a>";
    }

    public function deleteMany(array $ids, string $selectType = null)
    {
        $deletedCount = 0;
        $unDeletedCount = 0;

        foreach ($ids as $key => $value) {
            try {
                $this->delete($value);
                $deletedCount++;
            } catch (AttendanceSheetApproved $exception) {
                $unDeletedCount++;
                continue;
            }
        }

        return ['deletedCount' => $deletedCount, 'unDeletedCount' => $unDeletedCount];
    }

    public function getFilteredRecords()
    {
        $sortFields = $this->listingHelper->getSortFields();
        $customListingFields = [];
        if ($this->hasCustomForm()) {
            $this->repo->addRelationToPagination('customData');
            $customListingFields = $this->customDataService->getListingFields();
        }


        $this->repo->pushCriteria(new CustomFind($this->getListingConditions(), []));
        $data = $this->repo->findWhere([]);
        $this->repo->resetCriteria();
        return $data;

    }

    public function getViewData($id)
    {
        $viewData = parent::getViewData($id);
        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('attendance_sheet');


        $record = $viewData['record'];
        $viewData['authDeptId'] = $this->staffService->getAuthDepartmentId();

        if(!Permissions::isOwnerOrAdmin()){
            $staffService = resolve(StaffService::class);
            if(!$staffService->isAccessableStaffId($record->staff_id, true)){
                throw new NotAccessibleStaffBranchException;
            }


            if(!Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS)){
                $hasOwnViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET);
                $hasDeptViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE);
                $recordDeptId = $record->staffInfo?->department_id ?? null;
                $sameDept = isset($recordDeptId) && isset($viewData['authDeptId']) && $viewData['authDeptId'] == $recordDeptId;
                $recordStaffId = $record['staff_id'] ?? null;
                $authId = getAuthOwner('staff_id');
                $isOwn = isset($recordStaffId) && isset($authId) && $recordStaffId == $authId;
                $hasViewPermission = ($sameDept && $hasDeptViewPermission) || ($isOwn && $hasOwnViewPermission);
                if(!$hasViewPermission){
                    throw new PageAccessNotAllowedException;
                }
            }
        }

        $viewData['view_templates'] = $viewTemplates;
        return $viewData;
    }

    public function getStaffRelatedSheets($staffId)
    {
        $authStaff = getAuthOwner('staff_id');
        $userCanViewAttendanceSheets = $this->userCanViewAttendanceSheets($staffId);
        if (!$userCanViewAttendanceSheets){
            return [];
        }


        $sheets=  $this->repo->getStaffRelatedSheets($staffId);
        $results = [];
        foreach ($sheets as $sheet){
            $formattedSheet = $this->formatFotStaffProfile($sheet);
            $results[]= $formattedSheet;
        }
        return $results;
    }

    private function formatFotStaffProfile($sheet)
    {
        $formattedSheet = [];
        $formattedSheet["id"] = $sheet->id;
        $formattedSheet["from"] = format_date($sheet->date_from);
        $formattedSheet["to"] =format_date($sheet->date_to);
        $formattedSheet["present"] = $sheet->present_days .'/'. $sheet->working_days;
        $formattedSheet["actualWorkingHours"] = $sheet->actual_working_hours .'/'. $sheet->expected_working_hours;
        $formattedSheet["status"] = AttendanceSheetStatusTypesUtil::getStatusesText()[$sheet->status];
        $formattedSheet["statusValue"] = $sheet->status;
        return $formattedSheet;
    }

    private function userCanViewAttendanceSheets($staffId)
    {
        $authStaff = getAuthOwner('staff_id');
        if (
            !Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS)
            &&
            !Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET)
            ||
            (!Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS) && Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET) && $authStaff != $staffId)
        ){
            return false;
        }
        return true;
    }

    protected function getPrimaryKey(): string
    {
        return 'auto_id';
    }
}
