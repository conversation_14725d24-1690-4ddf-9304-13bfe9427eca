<?php

namespace App\Services;

use App\Exceptions\PurchaseRequestHasQuotationsForStatus;
use App\Repositories\PurchaseRequestRepository;
use App\Utils\FollowUpItemTypeUtil;
use App\Utils\PurchaseRequestStatusUtil;
use App\Exceptions\WrongPurchaseRequestStatus;
use App\Repositories\EntityRepository;
use App\Repositories\QuotationRequestRepository;
use App\Repositories\FollowUpStatusRepository;
use App\Utils\EntityKeyTypesUtil;

class QuotationRequestService extends BaseService
{

    public $repo;
    public $quotationRequestRepository;
    public $followUpStatusRepository;


    public function __construct(
        QuotationRequestRepository $quotationRequestRepository,
        FollowUpStatusRepository $followUpStatusRepository,
        EntityRepository $entityRepo = null
    ) {
        parent::__construct($quotationRequestRepository, $entityRepo);
        $this->repo = $quotationRequestRepository;
        $this->followUpStatusRepository = $followUpStatusRepository;
    }



    public function updateFollowUpStatus($id, $status){
        $request = request()->merge(['follow_up_status_id' => $status]);
        $request->request->remove('entityKey');    
        $followUpKey = FollowUpItemTypeUtil::QUOTATION_REQUEST_TYPE;
        $status = $this->followUpStatusRepository->findWhere(['item_type' => $followUpKey,'id' => $status]);
        if(!$status->isEmpty()){
            $result = parent::update($id, $request);
            return $result; 
        }

    }

   public function  exportBreadCrumbs()
    {
        $breadCrumbs[] = [

            'link' => '/v2/owner/entity/quotation_request/list',
            'title' => __t('Quotation Request')
        ];
        return $breadCrumbs;
    }

}