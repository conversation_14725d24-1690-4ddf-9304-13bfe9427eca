<?php

namespace App\Services;
use App\Exceptions\WrongPurchaseRequestStatus;
use App\Repositories\EntityRepository;
use App\Repositories\PurchaseOrderRepository;
use App\Utils\PurchaseQuotationStatusUtil;
use App\Repositories\FollowUpStatusRepository;
use App\Utils\FollowUpItemTypeUtil;

class PurchaseOrderService extends BaseService
{

    public $repo;
    public $quotationRequestRepository;

    public function __construct(
        PurchaseOrderRepository $repo,
        FollowUpStatusRepository $followUpStatusRepository,
        EntityRepository $entityRepo = null
    ) {
        parent::__construct($repo, $entityRepo);
        $this->repo = $repo;
        $this->followUpStatusRepository = $followUpStatusRepository;
    }


    public function updateFollowUpStatus($id, $status){
        $request = request()->merge(['follow_up_status' => $status]);
        $request->request->remove('entityKey');    
        $followUpKey = FollowUpItemTypeUtil::PURCHASE_ORDER_TYPE;
        $status = $this->followUpStatusRepository->findWhere(['item_type' => $followUpKey,'id' => $status]);
        if(!$status->isEmpty()){
            $result = parent::update($id, $request);
            return $result; 
        }

    }

}