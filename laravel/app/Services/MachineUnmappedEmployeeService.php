<?php

namespace App\Services;

use App\Repositories\AttendanceLogRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\InArrayCriteria;
use App\Repositories\Criteria\NotInArrayCriteria;
use App\Repositories\Criteria\OrderByCriteria;
use App\Repositories\Criteria\SelectCriteria;
use App\Repositories\EntityRepository;
use App\Repositories\MachineMappingRepository;
use App\Repositories\MachineRepository;
use App\Repositories\StaffRepository;
use App\Utils\AttendanceMachineMappingUtil;
use Closure;

/**
 * Class MachineUnmappedEmployeeService
 */
class MachineUnmappedEmployeeService extends BaseService
{
    /**
     * @var array filters
     */
    public $filters = [];

    /**
     * @var MachineMappingRepository
     */
    public $repo;

    /**
     * @var string show route name
     */
    protected $showRouteName = null;

    public $machine_id;

    /**
     * @var StaffRepository
     */
    private $staffRepository;

    /**
     * @var MachineRepository
     */
    private $machineRepo;

    /**
     * @var AttendanceLogRepository
     */
    private $attendanceLogRepository;

    /**
     * MachineService constructor.
     */
    public function __construct(MachineMappingRepository $repo,
                                MachineRepository        $machineRepo,
                                StaffRepository          $staffRepository,
                                AttendanceLogRepository  $attendanceLogRepository,
                                EntityRepository         $entityRepo = null)
    {
        $this->staffRepository = $staffRepository;
        $this->attendanceLogRepository = $attendanceLogRepository;
        $this->machineRepo = $machineRepo;
        parent::__construct($repo, $entityRepo);
    }

    /**
     * @return array
     */
    public function getFormRelatedData()
    {
        $relatedData = parent::getFormRelatedData();

        $alreadyMappedIds = $this->repo
            ->pushCriteria(new SelectCriteria('staff_id'))
            ->pushCriteria(new NotInArrayCriteria('staff_id', [
                AttendanceMachineMappingUtil::UNKNOWN,
                AttendanceMachineMappingUtil::IGNORED
            ]))
            ->pushCriteria(new EqualCriteria('machine_id', $this->machine_id))
            ->all()
            ->pluck('staff_id')
            ->toArray();

        $staffs = $this->staffRepository
            ->pushCriteria(new SelectCriteria('id,code,name,middle_name,last_name'))
            ->pushCriteria(new CustomFind(
                [['field' => 'active', 'operation' => '=', 'value' => 1]],
                ['field' => 'name', 'direction' => 'ASC']
            ))
            ->pushCriteria(new NotInArrayCriteria('id', $alreadyMappedIds))
            ->all();

        $relatedData['unMappedEmployees'] = $this->repo
            ->pushCriteria(new InArrayCriteria('staff_id', [AttendanceMachineMappingUtil::UNKNOWN]))
            ->pushCriteria(new EqualCriteria('machine_id', $this->machine_id))
            ->pushCriteria(new OrderByCriteria('machine_employee_name', 'asc'))
            ->all();

        $relatedData['staffs'] = $staffs->keyBy('id')->map(function ($staff) {
            return ($staff->code ? "#$staff->code" : $staff->id) . ' - ' . implode(' ', array_filter([$staff->name, $staff->middle_name, $staff->last_name]));
        });

        $relatedData['staffByNames'] = $staffs->keyBy(function($staff) {
            return strtolower(implode(' ', array_filter([$staff->name, $staff->middle_name, $staff->last_name])));
        })->map(fn($item) => $item->id)->toArray();

        return $relatedData;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record): array
    {
        $relations = parent::getActivityLogRelations($record);

        return $relations;
    }

    /**
     * @param \App\Requests\DefaultRequest|\Illuminate\Http\Request $request
     * @return \Illuminate\Database\Eloquent\Model|mixed|null
     */
    public function sync($request, array $excludedFields = [], Closure $callback = null)
    {
        $data = $request->except(['_token']);

        $result = null;
        $attendanceLogMappingUpdate = [];
        foreach ($data['system_emp_id'] as $k => $v) {
            if (!empty($data['ignore'][$k])) {
                $formattedData['staff_id'] = AttendanceMachineMappingUtil::IGNORED;
            } elseif (!empty($data['system_emp_id'][$k])) {
                $formattedData = [
                    'staff_id' => $data['system_emp_id'][$k],
                ];

                $attendanceLogMappingUpdate[$data['machine_emp_id'][$k]] = $data['system_emp_id'][$k];
            } else {
                continue;
            }

            $result = $this->repo->update($data['machine_map_id'][$k], $formattedData);
        }

        if (count($attendanceLogMappingUpdate)) {
            $this->attendanceLogRepository->mapLogsAfterMachineMapping($this->machine_id, $attendanceLogMappingUpdate);
        }

        return $result;
    }

    /**
     * @return mixed
     */
    public function getMachineName($id)
    {
        return $this->machineRepo->find($id)->name;
    }
}
