<?php

namespace App\Services;

use App\Exceptions\StockRequests\CannotApproveOrRejectNotPendingStockRequest;
use App\Exceptions\StockRequests\CannotUndoApprovalStockRequest;
use App\Exceptions\StockRequests\CannotUndoRejectionStockRequest;
use App\Exceptions\StockRequests\WrongStockRequestStatus;
use App\Models\Owner;
use App\Repositories\EntityRepository;
use App\Repositories\StaffRepository;
use App\Repositories\StockRequestRepository;
use Izam\Daftra\Common\Utils\PermissionUtil;
    use App\Facades\Permissions;
use App\Repositories\StoreRepository;
use Illuminate\Support\Collection;
use Izam\StockRequest\Services\StockRequestService as PackageStockRequestService;
use App\Exceptions\StockRequests\CannotAddNewProduct;
use App\Exceptions\StockRequests\CannotExceedManufactureOrderQuantity;
use App\Facades\Settings;
use app\Models\ManufacturingOrder;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\StockRequestRelationUtil;

class StockRequestService extends BaseService
{

    private $stockRequestRepository;
    public function __construct(StockRequestRepository $stockRequestRepository , EntityRepository $entityRepository)
    {
        parent::__construct($stockRequestRepository, $entityRepository);
        $this->stockRequestRepository = $stockRequestRepository;
    }

    /**
     * @param $stockRequestId
     * @return int
     */
    public function deleteStockRequestItems($stockRequestId)
    {
       return $this->stockRequestRepository->deleteStockRequestItems($stockRequestId);
    }

    public function updateStatus($id, $action, $currentStatus){
        $newStatus = match($action){
            'reject'=> 'rejected',
            'approve'=> 'under_process',
            'undo_approval'=> 'pending',
            'undo_rejection'=> 'pending',
            default=>''
        };
        $updatedData['status'] =  $newStatus;
        if(($action == 'undo_rejection')){
            $updatedData['action_comment'] = '';
        }
        if(($action == 'reject')){
            $updatedData['action_comment'] = request()->get('action_comment');
        }

        if(empty($newStatus)){
            throw new WrongStockRequestStatus($newStatus);
        }

        if(in_array($action, ['reject', 'approve']) && $currentStatus != 'pending'){
            throw new CannotApproveOrRejectNotPendingStockRequest();
        }

        if(($action == 'undo_approval') && ($currentStatus != 'under_process')){
            throw new CannotUndoApprovalStockRequest($newStatus);
        }

        if(($action == 'undo_rejection') && ($currentStatus != 'rejected')){
            throw new CannotUndoRejectionStockRequest($newStatus);

        }
        $updatedData['action_by'] = getAuthOwner('staff_id');
        $result =  $this->stockRequestRepository->update($id, $updatedData);
        return $result;
    }

    public function getOnApproveNotifiableStaffs($entityData){
        $employees[] = $entityData['staff_id'];
        $staffRepository = resolve(StaffRepository::class);
        $notifiable = $staffRepository->findWhereIn('id', $employees);

        if($entityData['staff_id'] == 0){
            $notifiable[] = Owner::find(getCurrentSite('id'));
        }

        return $notifiable;
    }

    public function canViewStockRequestRequisitions($stockRequestId){
        $staffId = getAuthOwner('staff_id');
        if ($staffId == 0){
            return true;
        }
        $hasRequisitionViewPermission = Permissions::checkPermission(PermissionUtil::REQUISITION_VIEW);
        $service = resolve(PackageStockRequestService::class);
        $requisitions = $service->getAllStockRequestRequisitions($stockRequestId);
        $stores = [];
        foreach($requisitions as $requisition){
            $stores[] = $requisition['store_id'];
            if($requisition['to_store_id']){
                $stores[] = $requisition['to_store_id'];
            }
        }

        $stores = array_unique($stores);

        $storeRepository = resolve(StoreRepository::class);
        $userCanViewAllRequisitionsStores = $storeRepository->getStoresUserCanView($stores);

        return $userCanViewAllRequisitionsStores && $hasRequisitionViewPermission;

    }

    protected Collection $viewAccessStockRequests;
    protected $fullAuthStaffData;

    public function getViewAccessStockRequests() : Collection {
        if(!isset($this->viewAccessStockRequests)){
            $authStaffId = getAuthOwner('staff_id');
            if($authStaffId == 0){
                throw new \Exception("getViewAccessStockRequests cannot be used with owner user");
            }
            if(!isset($this->fullAuthStaffData)){
                /** @var StaffRepository $staffRepository */
                $staffRepository = resolve(StaffRepository::class);
                $this->fullAuthStaffData = $staffRepository->find($authStaffId);
            }
            $this->viewAccessStockRequests = $this->stockRequestRepository->getViewAccessStockRequests($this->fullAuthStaffData);
        }
        return $this->viewAccessStockRequests;
    }

    /**
     * Checks if the quantities of stock request items exceed the quantities of their corresponding materials in the manufacturing order
     * @param array $data
     * @throws CannotAddNewProduct
     * @throws CannotExceedManufactureOrderQuantity
     */
    public function checkExceedingManufacturingOrderQuantities($data){
        $allowsExceeding = Settings::getValue(PluginUtil::MANUFACTURING_PLUGIN, \App\Helpers\Settings::EXCEEDING_THE_REQUESTED_QUANTITY_IN_MANUFACTURING_ORDER, null, false);
        /** @var PackageStockRequestService $packageStockRequestService */
        $packageStockRequestService = resolve(PackageStockRequestService::class);
        $manufactureOrderRelation = $packageStockRequestService->getStockRequestPivotRelationByType($data, StockRequestRelationUtil::SOURCE_MANUFACTURE_ORDER);
        if (isset($manufactureOrderRelation) && !$allowsExceeding){
            /** @var ManufacturingOrderService $manufactureOrderService */
            $manufactureOrderService = resolve(ManufacturingOrderService::class);
            $manufactureOrderId = $manufactureOrderRelation['related_model_id'];
            $dataItems = $data['stock_request_items'];
            $materials = $manufactureOrderService->getOrderMaterials($manufactureOrderId);
            foreach ($dataItems as $item){
                if (!array_key_exists($item['product_id'] , $materials)){
                    throw new CannotAddNewProduct();
                }
                if ($item['quantity']['quantity'] > $materials[$item['product_id']]['quantity']){
                    throw new CannotExceedManufactureOrderQuantity();
                }
            }
        }
    }

    public function deleteStockRequestRelations($stockRequestId){
        return $this->stockRequestRepository->deleteStockRequestRelations($stockRequestId);
    }

    public function getRelatedRequisitionsIds($stockRequestId)
    {
        $reqs = $this->stockRequestRepository->getRelatedRequisitions($stockRequestId);
        $reqsIds = [];
        foreach ($reqs as $req){
            $reqsIds[] = $req->related_model_id;
        }
        return $reqsIds;
    }

    public function  exportBreadCrumbs()
    {
        $breadCrumbs[] = [

            'link' => '/v2/owner/entity/stock_request/list',
            'title' => __t('Stock Request')
        ];
        return $breadCrumbs;
    }
}
