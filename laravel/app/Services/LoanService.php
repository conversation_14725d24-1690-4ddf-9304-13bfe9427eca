<?php

namespace App\Services;

use App\Events\Loans\LoanCreatedEvent;
use App\Events\Loans\LoanDeletedEvent;
use App\Events\Loans\LoanUpdatedEvent;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Loans\ExceedMaximumInstallmentsCount;
use App\Facades\Formatter;
use App\Facades\Permissions;
use App\Helpers\FilterOperations;
use App\Models\Entity;
use App\Models\Loan;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\CurrencyRepository;
use App\Repositories\EntityRepository;
use App\Repositories\InstallmentRepository;
use App\Repositories\LoanRepository;
use App\Repositories\PostRepository;
use App\Repositories\StaffRepository;
use App\Repositories\TreasuryRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\InstallmentPeriodUtil;
use App\Utils\InstallmentStatusUtil;
use App\Utils\ItemPermissionUtil;
use App\Utils\LoanPeriodsUtil;
use App\Utils\LoanStatusUtil;
use App\Modules\LocalEntity\Repositories\TagsRepository;
use App\Utils\ItemTagUtil;
use App\Utils\PermissionUtil;
use App\Utils\PostTypesUtil;
use Closure;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Facades\Branch;
use App\Facades\Plugins;
use App\Facades\Staff;
use App\Utils\PluginUtil;
use App\Services\TagService;
use Izam\Daftra\Common\Utils\PostTypeUtil;

/**
 * LoanService Class loan service class
 * @package App\Services
 * <AUTHOR> Mohammed <<EMAIL>>, Mohamed Faesal <<EMAIL>>
 */
class LoanService extends BaseService
{
    /**
     * @var $filters array
     */
    var $filters = [];

    /**
     * @var $repo LoanRepository
     */
    var $repo;

    /**
     * @var $staffRepository StaffRepository
     */
    private $staffRepository;

    /**
     * @var CurrencyRepository
     */
    private $currencyRepository;

    /**
     * @var InstallmentRepository
     */
    private $installmentRepository;

    /**
     * @var TreasuryRepository
     */
    private $treasuryRepository;

    protected $showRouteName = 'owner.loans.show';

    /**
     * LoanService constructor.
     * @param LoanRepository $repo
     * @param StaffRepository $staffRepository
     * @param TreasuryRepository $treasuryRepository
     * @param CurrencyRepository $currencyRepository
     * @param InstallmentRepository $installmentRepository
     * @param EntityRepository|null $entityRepo
     */
    public function __construct(
        LoanRepository $repo,
        StaffRepository $staffRepository,
        TreasuryRepository $treasuryRepository,
        CurrencyRepository $currencyRepository,
        InstallmentRepository $installmentRepository,
        public TagsRepository $tagsRepository,
        public TagService $tagService,
        PostRepository $postRepository,
        EntityRepository $entityRepo = null,
    ) {
        $this->installmentRepository = $installmentRepository;
        $this->staffRepository = $staffRepository;
        parent::__construct($repo, $entityRepo);
        $this->staffRepository = $staffRepository;
        $this->currencyRepository = $currencyRepository;
        $this->treasuryRepository = $treasuryRepository;
        $this->postRepository = $postRepository;
    }

    /**
     * {@inheritDoc}
     */
    public function getFormRelatedData()
    {
        $defaultCurrencyCode = getCurrentSite('currency_code');
        $currencies = $this->currencyRepository->list(['code', 'code'], true);
        $treasuries = $this->treasuryRepository
            ->getAllPermittedTreasuries(ItemPermissionUtil::PERMISSION_WITHDRAW);
        $treasuries = $treasuries ? $treasuries->pluck('name','id')->toArray() : [];
        $data = [
            'currencies' => $currencies,
            'defaultCurrencyCode' => $defaultCurrencyCode,
            'periods' => InstallmentPeriodUtil::getStatusList(),
            'treasuries' => $treasuries,
        ];
        $staffId = old('staff_id');
        if($staffId) {
            $staff = $this->staffRepository->find($staffId);
            if (!$staff) {
                throw new EntityNotFoundException(__t("Employee"));
            }
            $data['staffOptions'] = [
                Staff::getStaffOptionFormated($staff)
            ];
        }
        return $data;
    }

    /**
     * {@inheritDoc}
     */
    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $oldLoan = $this->find($id);
        if(!empty($oldLoan)){
            $staffService = resolve(StaffService::class);
            $staffService->checkStaffAccessable($request,'staff_id');
        }
        $request['application_date'] =
            Formatter::formatForDB($request['application_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['installment_start_date'] =
            Formatter::formatForDB($request['installment_start_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['repay_from_payslip'] = isset($request['repay_from_payslip']) ? 1 : 0;
        $willOverrideInstallments = false;
        if (
            $oldLoan->application_date->toDateString() != $request['application_date'] ||
            $oldLoan->installment_start_date->toDateString() != $request['installment_start_date'] ||
            $oldLoan->amount != $request['amount'] ||
            $oldLoan->installment_period != $request['installment_period'] ||
            $oldLoan->installment_amount != $request['installment_amount']
        ) {
            $willOverrideInstallments = true;
        }
        $additionalCallback = function ($result, $oldData) use ($willOverrideInstallments, $callback) {
            event(new LoanUpdatedEvent($result, $willOverrideInstallments));
            if ($callback) {
                $callback($result, $oldData);
            }
        };
        $tags = $request['tags'];
        unset($request['tags']);
        $result = parent::update($id, $request, $excludedFields, $additionalCallback);
        if($result && !empty($tags)){
            $this->tagService->updateTagsForElement($tags ?? [], $oldLoan->tags->toArray(), $result->id, ItemTagUtil::LOAN, EntityKeyTypesUtil::LOAN_ENTITY_KEY);
        }
        return $result;
    }

    /**
     * {@inheritDoc}
     */
    public function getFormData($id = false)
    {
        $formData = parent::getFormData($id);

        if(isset($formData['form_record']->staff_id)){
            $staffService = resolve(StaffService::class);
            if(!$staffService->isAccessableStaffId($formData['form_record']->staff_id, true)){
                throw new NotAccessibleStaffBranchException;
            }
        }
        if ($id || old('staff_id')) {
            $staffId = old('staff_id') ? old('staff_id') : $formData['form_record']->staff_id;
            $staff = $this->staffRepository->find($staffId);
            $formData['staffOptions'] = [
                Staff::getStaffOptionFormated($staff)
            ];
        }
        return $formData;
    }

    /**
     * {@inheritDoc}
     */
    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        if ($this->calculateInstallmentsCount($request->amount, $request->installment_amount) > 150) {
            throw new ExceedMaximumInstallmentsCount;
        }
        $request['application_date'] =
            Formatter::formatForDB($request['application_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['installment_start_date'] =
            Formatter::formatForDB($request['installment_start_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['repay_from_payslip'] = isset($request['repay_from_payslip']) ? 1 : 0;
        $staff = $this->staffRepository->find($request['staff_id'] ?? null);
        $request['staff_default_account'] = $staff->default_account_id ?? null;
        $additionalCallback = function ($result) use ($callback) {
            event(new LoanCreatedEvent($result));
            if ($callback) {
                $callback($result);
            }
        };
        $tags = $request['tags'];
        unset($request['tags']);
        $result = parent::add($request, $excludedFields, $additionalCallback);
        if ($result && isset($tags)) {
            $this->tagService->insertInTagTable($tags, $result->id, ItemTagUtil::LOAN, EntityKeyTypesUtil::LOAN_ENTITY_KEY);
        }
        return $result;
    }

    /**
     * @param int $loanAmount
     * @param $installmentAmount
     * @return int
     */
    private function calculateInstallmentsCount(int $loanAmount, $installmentAmount): int
    {
        return $loanAmount / $installmentAmount;
    }
    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['treasury_id'] = $record->treasury ? $record->treasury->name : '';
        return $data;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record): array
    {
        $relations = parent::getActivityLogRelations($record);
        if ($record->staff) {
            $relations[] = new ActivityLogRelationRequest($record->staff->id, EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        }
        return $relations;
    }

    function filterName($query, $allowInActive = false)
    {
        return $this->repo->getAutoCompleteResult($query, $allowInActive);
    }

    public function getFilters()
    {
        $loan_repo = $this->repo;
        $this->filters = parent::getFilters();
        $staffList = [];
        if (isset($this->parameters['staff_id']) && !empty($this->parameters['staff_id'])) {
            $staff = $this->staffRepository->find($this->parameters['staff_id']);
            if ($staff)
                $staffList += [$staff->id => "#{$staff->id} {$staff->name}({$staff->email_address})"];
        }

        $this->filters['id'] = [
            'simple' => true,
            'label' => false,
            'after' => '<i class="input-icon fal fa-search"></i></div>',
            'before' => '<div class="form-group-icon form-group">',
            'attributes' => [
                'placeholder' => __t('Filter by') . ' ' . __t('Loans'),
                'id' => 'loansSelect'
            ],
            'div' => 'col-md-4',
            'options' => [],
            'type' => 'select',
            'filter_options' => [
                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                'field' => 'id',
            ]
        ];

        $this->filters['installment_period'] = [
            'simple' => true,
            'label' => false,
            'inputClass' => 'select-filter form-control',
            'div' => 'col-md-4 form-group',
            'attributes' => [
                'placeholder' => sprintf(__t('Select %s'), __t('Installment Period')),
                'data-allow-clear' => 'true'
            ],
            'type' => 'select',
            'options' => LoanPeriodsUtil::getAllPeriods(),
            'filter_options' => [
                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                'field' => 'installment_period'
            ]
        ];

        $this->filters['staff_id'] = [
            'simple' => true,
            'label' => __t('Employee'),
            'after' => '<i class="input-icon fal fa-search"></i></div>',
            'before' => '<div class="form-group-icon form-group">',
            'attributes' => [
                'placeholder' => __t(getEmployeesFieldPlaceholder()),
                'id' => 'staffSelect'
            ],
            'div' => 'col-md-4',
            'options' => $staffList,
            'type' => 'select',
            'filter_options' => [
                'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                'field' => 'staff_id',
            ]
        ];

        $this->filters['next_installment_due_date_from'] = [
            'simple' => true,
            'type' => 'date',
            'before' => '<div class="form-group-icon form-group">',
            'after' => '</div>',
            'label' => false,
            'div' => 'col-md-4',
            'icon' => '<i class="input-icon far fa-calendar-day"></i>',
            'attributes' => ['placeholder' => __t('Next Installment') . __t('-From')],
            "filter_options" => [
                'operation' => FilterOperations::CLOSURE,
                'field' => 'loans.id',
                'function' => function ($value) use ($loan_repo) {
                    $loans = $loan_repo->getLoansForInstallmentDateFilterFrom($value);
                    return [
                        'operation' => FilterOperations::IN_FILTER_OPERATION,
                        'field' => 'loans.id',
                        'value' => $loans->pluck('loan_id')->toArray(),
                    ];
                }
            ],
        ];

        $this->filters['next_installment_due_date_to'] = [
            'simple' => true,
            'type' => 'date',
            'before' => '<div class="form-group-icon form-group">',
            'after' => '</div>',
            'label' => false,
            'div' => 'col-md-4',
            'icon' => '<i class="input-icon far fa-calendar-day"></i>',
            'attributes' => ['placeholder' => __t('Next Installment') . __t('-To')],
            "filter_options" => [
                'operation' => FilterOperations::CLOSURE,
                'field' => 'loans.id',
                'function' => function ($value) use ($loan_repo) {
                    $loans = $loan_repo->getLoansForInstallmentDateFilterTo($value);
                    return [
                        'operation' => FilterOperations::IN_FILTER_OPERATION,
                        'field' => 'loans.id',
                        'value' => $loans->pluck('loan_id')->toArray(),
                    ];
                }
            ]
        ];

        $this->filters['status'] = [
            'simple' => true,
            'label' => false,
            'inputClass' => 'select-filter form-control',
            'div' => 'col-md-4 form-group',
            'attributes' => [
                'placeholder' => sprintf(__t('Select %s'), __t('Status')),
                'data-allow-clear' => 'true'
            ],
            'type' => 'select',
            'options' => LoanStatusUtil::getAllStatuses(),
            "filter_options" => [
                'operation' => FilterOperations::CLOSURE,
                'field' => 'loans.id',
                'function' => function ($value) use ($loan_repo) {
                    $loans = [];
                    switch ($value){
                        case LoanStatusUtil::UNCOMPLETED:
                            $loans = $loan_repo->getLoansWithStatusUncompleted();
                            break;
                        case LoanStatusUtil::COMPLETED:
                            $loans = $loan_repo->getLoansWithStatusCompleted();
                            break;
                        case LoanStatusUtil::OVERDUE:
                            $loans = $loan_repo->getLoansWithStatusOverdue();
                            break;
                    }
                    return [
                        'operation' => FilterOperations::IN_FILTER_OPERATION,
                        'field' => 'loans.id',
                        'value' => $loans->pluck('loanID')->toArray(),
                    ];
                }
            ]
        ];

        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
            $branches = Branch::getStaffBranchesSuspended();
            if(count($branches) > 1){
                $this->filters['branch'] = [
                    'param_name' => 'branch',
                    'simple'=>false,
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'attributes' => [
                        'placeholder' => __t('Select') . ' ' . __t('Branch'),
                        'id' => 'branchSelect',
                    ],
                    'div' => 'col-md-4',
                    'inputClass' => 'select-filter form-control',
                    'options' => $branches,
                    'type' => 'select', 'filter_options' => [
                        'table' => 'staffs',
                        'model' => 'staff',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'branch_id',
                    ]
                ];
            }
        }

        $tagsList = $this->tagsRepository->getTagsByType(ItemTagUtil::LOAN);
        $tagArray = [];
        foreach($tagsList as $tag){
            $tagArray[$tag->id] = $tag->name;
        }
        $this->filters['tags[]'] = [
            'label' => __t('Tags'),
            'simple' => false,
            'param_name' => 'tags',
            'after' => '<i class="input-icon fal fa-search"></i></div>',
            'before' => '<div class="form-group-icon form-group">',
            'attributes' => [
                'data-select' => true,
                'data-placeholder' => sprintf(__t('Select %s'), __t('Tags')),
                'id' => 'tagSelect',
                'multiple' => 'multiple'
            ],
            'div' => 'col-md-4',
            'options' => $tagArray,
            'type' => 'select',
            "filter_options" => [
                'operation' => FilterOperations::CLOSURE,
                'field' => 'loans.id',
                'function' => function ($value) {
                    $tags = $this->tagsRepository->searchItemTags(EntityKeyTypesUtil::LOAN_ENTITY_KEY, $this->parameters['tags']);
                    return [
                        'operation' => FilterOperations::IN_FILTER_OPERATION,
                        'field' => 'loans.id',
                        'value' => $tags->pluck('item_id')->toArray(),
                    ];
                }
            ],
        ];

        return $this->filters;
    }

    public function getSortFields()
    {
        return [
            'fields' => [
                'name' => [
                    'title' => __t('Employees'),
                    'direction' => 'ASC',
                    'field' => 'name',
                    'relation'=> [
                        'join' => ['fromAlias' => 'staffs', 'join' => 'staffs.id', 'alias' => 'loans.staff_id'],
                        'tableName' => 'staffs',
                        'tableField' => 'name'
                    ]
                ],
                'created' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'created',
                    'direction' => 'ASC'
                ]
            ],
            'active' => [
                'field' => 'created',
                'direction' => 'DESC',
            ]
        ];
    }

    /**
     * get employee data with id
     * @param int $employeeId
     * @return array
     */
    public function getEmployeeDataById(int $employeeId)
    {
        $this->staffRepository->pushCriteria(new CustomFind([
            ['field' => 'id', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => $employeeId],
            ['field' => 'active', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => 1],
            ['field' => 'deleted_at', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => null]
        ]));
        $staffList = [];
        $staffAttributes = [];
        $staffObjects = $this->staffRepository->all();
        foreach ($staffObjects as $staff) {
            $staffList[$staff->id] = "#{$staff->id} {$staff->name} ({$staff->email_address})";
            $staffAttributes[$staff->id]['data-img'] = $staff->image ?: getDefaultAccountImage();
        }
        $staffAutoFillData = [
            'staffOptionsAttributes' => $staffAttributes,
            'staffOptions' => $staffList,
        ];
        return $staffAutoFillData;
    }

    /**
     * get view data for an entity
     * @param $id entity id
     * @return array
     * @throws EntityNotFoundException if entity not found
     */
    public function getViewData($id)
    {
        $parent_data = parent::getViewData($id);
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($parent_data['record']->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }

        $parent_data['record'] = $parent_data['record']->load('installments', 'treasury', 'expense');
        $parent_data['notesCount'] = $this->postRepository->getPostsCount($id, PostTypeUtil::LOAN_TYPE);
        $employee = $parent_data['record']->staff;

        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('loan');
        $parent_data['view_templates'] = $viewTemplates;

        return array_merge($parent_data, ['employee' => $employee]);
    }

    /**
     * deletes database record
     * @param int $id record id
     * @param Closure $callback
     * @return mixed
     * @throws EntityNotFoundException
     * @throws \Throwable
     */
    public function delete($id, Closure $callback = null)
    {
        $loan = $this->repo->find($id);

        if (!$loan) {
            throw new EntityNotFoundException(__t($this->mainEntity->label));
        }

        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($loan->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }

        if ($loan->paid_installments_count) {
            throw new \Exception(__t('You cannot delete the loan as there’s one or more paid installment'));
        }

        $status = parent::delete($id, function () use ($loan) {

        });

        if ($status) {
            event(new LoanDeletedEvent($loan));
        }
        return $status;
    }


    public function getListingConditions(){
        $accessibleBranchListingCondition = $this->getAccessibleBranchListingCondition();
        return empty($accessibleBranchListingCondition) ?
            parent::getListingConditions() :
            array_merge(parent::getListingConditions(),[$accessibleBranchListingCondition]);
    }

    public function updateTags($id, $request){
        $oldLoan = $this->find($id);
        $tags = $request['tags'];
        $this->tagService->updateTagsForElement($tags ?? [], $oldLoan->tags->toArray(), $id, ItemTagUtil::LOAN, EntityKeyTypesUtil::LOAN_ENTITY_KEY);
    }

    public function getCloneFormData(int $id)
    {
        $data = $this->getFormData($id);
        $data['form_record']->application_date = date('Y-m-d');
        $data['form_record']->installment_start_date = date('Y-m-d');
        $data['isClone'] = 1;
        return $data;
    }


    public function getWithRelations() {
        return ['paid_installments','staff','installments','unpaid_installments'];
    }


    public function getStaffLatestLoans($staffId)
    {
        if (!Permissions::checkPermission(PermissionUtil::MANAGE_LOANS_AND_INSTALLMENT_SETTINGS)) return ['isVisible'=>false];
        $loans = $this->repo->getStaffLatestLoans($staffId);
        $count = $this->repo
            ->pushCriteria(new EqualCriteria('staff_id' , $staffId))
            ->count();
        $viewUrl = null;
        if ($count > 10 ){
            $viewUrl = route('owner.loans.index' , ['staff_id'=> $staffId]);
        }
        $formattedLoans = $this->formatLatestStaffLoansForStaffProfileApi($loans);
        return [
            'title'=> __t('Loans').   ' (' . $count . ')',
            'items'=>$formattedLoans,
            'viewAllUrl'=>$viewUrl,
            'isVisible'=>true
        ];
    }



    private function formatLatestStaffLoansForStaffProfileApi($loans): array
    {
        $formattedLoans = [];
        /** @var Loan $loan */
        foreach ($loans as $loan) {
            $loan->resolveLoanStatus();
            $nextInstallment = $loan->next_installment();
            $lastInstallment = $loan->installments->last();
            //handle next installment
            $title = '';
            $text = '';
            $status = $loan->resolveLoanStatus();

            if ($nextInstallment && $nextInstallment->status == InstallmentStatusUtil::UNPAID){
                $title = format_date($nextInstallment->due_date);
                $text = $nextInstallment->amount;
            }

            if($loan->unpaid_installments_count == 0 && $lastInstallment->status== InstallmentStatusUtil::PAID ){
                $title = __t('Closed On');
                $text = format_date($lastInstallment->due_date);
            }

            $formattedLoans[] = [
                'id'=>$loan->id,
                'nextInstallment' => [
                    'title'=>$title,
                    'text'=>$text
                ],
                'paidInstallments'=>$loan->paid_installments_count() . ' / ' . $loan->installments->count(),
                'amount'=>[
                    'total'=>format_price($loan->amount,$loan->currency),
                    'currency'=>'',
                    'paid'=>$loan->total_paid_amount()
                ],
                'status'=>[
                    'value'=>$status,
                    'text'=>__t(ucfirst($status))
                ],
                'viewUrl'=>route('owner.loans.show',['loan'=>$loan->id])
            ];
        }
        return $formattedLoans;
    }
}
