<?php

namespace App\Services;


use App\Services\Traits\BulkDeleteService;
use Closure;
use App\Exceptions\Import\ImportErrorException;
use App\Models\Treasury;
use App\Utils\EntityFieldUtil;
use App\Services\ImportService;
use App\Helpers\FilterOperations;
use App\Repositories\EntityRepository;
use App\Utils\BankTransactionsStatusUtil;
use App\Exceptions\EntityNotFoundException;
use App\Repositories\BankTransactionRepository;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Exceptions\ChangeBankTransactionStatusFailedException;
use App\Exceptions\MatchedBankTransactionDataDeleteFailedException;
use App\Repositories\JournalTransactionRepository;
use App\Services\Bank\BankSystemTransactionService;
use Laminas\Form\Element\Date;
use Izam\View\Form\Element\Select;
use Laminas\Form\Element\Hidden;
use Laminas\Form\Element\Number;
use Laminas\Form\Element\Text;


class BankTransactionService extends BaseService
{
    use BulkDeleteService;

    var $repo;

    var $filters = [];
    public $entity;

    public $extraImportFields = ['bank_transactions.type' ,'bank_transactions.bank_id','bank_transactions.amount'];

    public function __construct(
        BankTransactionRepository $repo,
        EntityRepository $entityRepository,
        private JournalTransactionRepository $journalTransactionRepository,
        private BankSystemTransactionService $bankSystemTransactionService,
    )
    {
        $repo->setPaginateWith(['bank', 'systemTransactions']);

        parent::__construct($repo, $entityRepository);
        $this->entity = \App\Utils\EntityKeyTypesUtil::BANK_TRANSACTION;
    }




    public function getSortFields()
    {
        return [
            'fields' => [
                'id' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'id',
                    'direction' => 'DESC'
                ],
                'date' => [
                    'title' => __t('Transaction Date'),
                    'field' => 'date',
                    'direction' => 'DESC'
                ],
//                'id' => [
//                    'title' => __t('ID'),
//                    'field' => 'id',
//                    'hidden' => true,
//                    'direction' => 'DESC'
//                ],
            ],
            'active' => [
                'field' => 'id',
                'direction' => 'DESC',
            ]
        ];
    }

    protected function getListingConditions()
    {
        $conditions = $this->listingHelper->getListingConditions();
        $key = array_search('status', array_column($conditions, 'field'));
        if ($key >= 0 && isset($conditions[$key]) && strtolower($conditions[$key]['value']) == 'all') {
            unset($conditions[$key]);
        }
        return  $conditions;
    }

    protected function getFiltered()
    {
        $filtered = $this->listingHelper->getFiltered();
        if (!empty($filtered['status']) && strtolower($filtered['status']) == 'all') {
            unset($filtered['status']);
        }
        return $filtered;
    }

    function getFilters()
    {
        $filters = $this->bankTransactionFilters();
        if (isset($this->parameters['bank_id']) && $this->parameters['bank_id'] > 0) {
            $filters['bank_id'] = [

                'simple' => false, /* displays the filter outside the toggle */
                'label' => false,
                'type' => 'hidden',
                'value' => $this->parameters['bank_id'],
                'filter_options' => [
                    'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                    'field' => 'bank_id',
                ]

            ];
        }

        return $filters;
    }


    function bankTransactionFilters()
    {

        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            return $this->filters = [

                'reference_id' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'inputClass' => 'form-control',
                    'attributes' => [
                        'placeholder' => __t('Search by') . ' ' . __t('Reference id'),

                    ],
                    'div' => 'col-md-6',
                    'type' => 'text',
                    'filter_options' => [
                        'operation' => FilterOperations::LIKE_BETWEEN_FILTER_OPERATION,
                        'field' => 'reference_id',
                    ]
                ],

                'status' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'inputClass' => 'form-control select-filter',
                    'attributes' => [
                        'placeholder' => __t('Filter by') . ' ' . __t('Transaction Status'),
                        'id' => 'statusSelect'
                    ],
                    'div' => 'col-md-6',
                    'options' => BankTransactionsStatusUtil::getStatusList(),
                    'type' => 'select',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'status',
                    ]
                ],
                'type' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'label' => false,
                    'inputClass' => 'form-control select-filter',
                    'attributes' => [
                        'placeholder' => __t('Filter by') . ' ' . __t('Transaction Type'),
                        'id' => 'typeSelect'
                    ],
                    'div' => 'col-md-6',
                    'options' => null,
                    'options' => ['deposit' => __t('Deposit'), 'withdraw' => __t('Withdraw')],
                    'type' => 'select',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'type',
                    ]
                ],
                'from_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'before' => '<div class="form-group form-group-icon">',
                    'after' => '</div>',
                    'label' => false,
                    'div' => 'col-md-4',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'attributes' => ['placeholder' => sprintf(__t('From ( %s )'), __t('Date'))],
                    "filter_options" => [
                        "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "date"
                    ],
                ],
                'to_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'before' => '<div class="form-group form-group-icon">',
                    'after' => '</div>',
                    'label' => false,
                    'div' => 'col-md-4',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'attributes' => ['placeholder' => sprintf(__t('To ( %s )'), __t('Date'))],
                    "filter_options" => [
                        "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "date"
                    ],
                ],
                'from_amount' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                    'before' => '<div class="form-group form-group-icon">',
                    'after' => '</div>',
                    'label' => false,
                    'div' => 'col-md-4',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'attributes' => ['placeholder' => sprintf(__t('From ( %s )'), __t('Amount'))],
                    "filter_options" => [
                        "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL,
                        "field" => "amount"
                    ],
                ],
                'to_amount' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                    'before' => '<div class="form-group form-group-icon">',
                    'after' => '</div>',
                    'label' => false,
                    'div' => 'col-md-4',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'attributes' => ['placeholder' => sprintf(__t('To ( %s )'), __t('Amount'))],
                    "filter_options" => [
                        "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL,
                        "field" => "amount"
                    ],
                ],

            ];
        }
    }

    /**
     * deletes database record
     * @param int $id record id
     * @param Closure $callback
     * @return mixed
     */
    public function delete($id, Closure $callback = null)
    {
        $this->parameters['tab'] = "bank-transactions";

        $bank_transaction = $this->repo->find($id);
        if ($bank_transaction->status == 'matched') {
            throw new MatchedBankTransactionDataDeleteFailedException();
        }

        return parent::delete($id);
    }



    public function deleteBulk($ids, Closure $callback = null)
    {
        $this->parameters['tab'] = "bank-transactions";
        $mateched = $unMatched = 0;
        $bankId = null;
        if(!empty($ids)){
            $bank_transactions = $this->repo->findWhereIn('id',$ids);
        }else{
            $bank_transactions = $this->repo->all();
        }

        foreach ($bank_transactions as  $bank_transaction) {
            $bankId = $bank_transaction->bank_id;
            if ($bank_transaction->status == 'matched') {
              $mateched++;
            }else{
                parent::delete($bank_transaction->id);
                $unMatched++;
            }
        }

        return ['matched' => $mateched,'unMatched' => $unMatched,'bank_id' => $bankId];
    }




    public static function allowedAction($currentStatus, $action)
    {

        $allowedActions = BankTransactionsStatusUtil::getAllowedActions($currentStatus);
        if (in_array($action, $allowedActions)) {
            return true;
        }

        return false;
    }

    public function change_status($id, $to_status, $action)
    {
        $bank_transaction = $this->repo->find($id);
        if (is_null($bank_transaction)) {
            throw new EntityNotFoundException($this->mainEntity);
        }

        if (!$this->allowedAction($bank_transaction->status, $action)) {
            $message = __t('You can not Change status from ') . __t($bank_transaction->status) . ' : ' . __t(BankTransactionsStatusUtil::EXCLUDED);
            throw new ChangeBankTransactionStatusFailedException($message);
        }

        $bank_transaction->status = $to_status;
        $result = $bank_transaction->save();
        return $result;
    }


    public function validationMessages()
    {
        $jsDateFormat = getDateFormats('moment_js')[getCurrentSite('date_format')];

        $dateFormat = getDateFormats('std')[getCurrentSite('date_format')];
        return [

            'date_format' => sprintf(__t('You Imported Date With Invalid Format, the correct format is ( %s )'), __t($jsDateFormat) . " \" " . date($dateFormat)  . " \" "),
            'min' => [
                'numeric' => __t( 'The :attribute must be greater than or equal :min.'),

            ],
            'unique' =>__t('The :attribute has already been taken.'),
            'numeric' =>__t('The :attribute must be a number.'),
            'required' =>__t('The :attribute field is required.'),



        ];
    }

    public function validateImportData($data, $validationRules, $headersInfo, $entityKey, $rowCount, $isUpdate, $updateUsing, &$invalidData, &$willBeUpdated)
    {
        
        $entityRulesGetter = \App::make(EntityRulesGetter::class);
        $importService = \App::make(ImportService::class);


        $willBeUpdated = false;
        if ($isUpdate) {

            if (($record=$importService->getRecord($entityKey, [$updateUsing => $data[$updateUsing],'bank_id'=>$data['bank_id']])) && !empty($data[$updateUsing]) ) {
              //send id of record to check unique update
               $data['update_data']=[
                $updateUsing=>$record->id

               ];

                $willBeUpdated = true;
            }

        }

        $data['validate_conditions']= $this->updateImportConditions();  //to make unique validation per bank to all site



        $validator = new DaftraValidator($entityKey, $entityRulesGetter);



        $validator->setMessages($this->validationMessages());
        $validator->setData($data);

        if (!$validator->isValid()) {
            foreach ($validator->getErrors() as $key => $errors) {
                foreach ($errors as $error) {
                    $invalidData[$rowCount][] = sprintf(__t('Failed to insert row #%s with error message %s'), $rowCount,' : '.$error);
                }

            }

            return false;
        }

        return true;
    }

    public function preProcessDataForImportUsingExtraData($data, $extraData)
    {

        if (isset($extraData['bank_id'])) {
            $data['bank_id'] = $extraData['bank_id'];
        }

       // to make withdraw amount and deposit amount accept zero to pass required_without laravel validation
        if ($data['withdraw_amount']==0) {
            $data['withdraw_amount']='';
        }
        if ($data['deposit_amount']==0) {
            $data['deposit_amount']='';
        }


        if ($data['withdraw_amount'] > 0) {
            $data['withdraw_amount']=str_replace(",",'',$data['withdraw_amount']);
            $data['amount']=$data['withdraw_amount'];
            $data['type']='withdraw';
        }
        else{
            $data['deposit_amount']=str_replace(",",'',$data['deposit_amount']);

            $data['amount']=$data['deposit_amount'];
            $data['type']='deposit';
        }

        if (empty($data['reference_id'])) {
            $data['reference_id']=null;
        }
        return $data;
    }


    public function getImportDownloadSample()
    {
        $lang = substr(CurrentSiteLang('code2'), 0, 2) == 'ar' ? '-ar' : '';

        return   ['path' => '/samples/bank-statement-sample' . $lang . '.csv', 'file_name' => __t('Bank Statement sample') . '.csv'];
    }

    public function  exportBreadCrumbs()
    {
        $breadCrumbs[] = [

            'link' => '/v2/owner/banks/treasury',
            'title' => __t('Treasuries & Bank Accounts')
        ];
        if (!empty($this->parameters['filters']['bank_id'])) {
            $treasury = Treasury::find($this->parameters['filters']['bank_id']);
            if (!$treasury) {
                throw new EntityNotFoundException(__t("Bank"));
            }

            $breadCrumbs[]   =   [
                'link' => "/owner/treasuries/view/" . $treasury->id . "#bank-transactions",
                'title' => $treasury->name . " #" . $treasury->id
            ];
        }

        return $breadCrumbs;
    }

    public function updateImportConditions()
    {
        $conditions=[];
        if (isset($this->parameters['extraData']['bank_id'])) {
            $conditions['bank_id'] = $this->parameters['extraData']['bank_id'];
        }

        return  $conditions;
    }

    public function validateAllItems($data, $extra_data, $fields)
    {
        //handle  duplicate  data in sheet before save it  to data base to avoid save any data from sheet
        //may need use this function in general
        $invalidData = [];
        if (empty($extra_data['ignore_errors']) && empty($extra_data['update_data'])) {
            foreach ($fields as  $field) {
                if (!empty($field->validation_rules)) {
                    $requireUnique = (is_array($field->validation_rules) ? strpos($field->validation_rules[0], 'unique') !== false : strpos($field->validation_rules, 'unique') !== false);

                    if ($requireUnique) {
                        $fieldKey = $field->db_name;
                        $fieldIds = array_column($data, $fieldKey);
                        $filteredFieldIds = array_filter($fieldIds, fn($value) => is_string($value) || is_int($value));
                        $fieldIdsCounts = array_count_values($filteredFieldIds);
                        $duplicateFieldIdsCounts = array_filter($fieldIdsCounts, function ($count) {
                            return $count > 1;
                        });

                        if (!empty($duplicateFieldIdsCounts)) {
                            $invalidData[] = sprintf(__t("Duplicate %s found: %s in the sheet"), __t(str_replace('_', ' ', $fieldKey)), implode(', ', array_keys($duplicateFieldIdsCounts)));
                        }
                    }
                }
            }
        }
        if (!empty($invalidData)) {
            throw new ImportErrorException($invalidData);
        }

        return $invalidData;
    }

    public function getFilterSpecs(): array
    {
        $specs = [
            'elements' =>
                [
                    [
                        'spec' => [
                            'name' => 'reference_id',
                            'type' => Text::class,
                            'options' => [
                                'label' => __t('Search by') . ' ' . __t('Reference id'),
                                'order' => 1,
                                'advanced' => false,
                            ],
                            'attributes' => [
                                'placeholder' => __t('Search by') . ' ' . __t('Reference id'),
                            ],
                        ]
                    ],
                    [
                        'spec' => [
                            'name' => 'from_date',
                            'type' => Date::class,
                            'options' => [
                                'label' => sprintf(__t('From ( %s )'), __t('Date')),
                                'order' => 2,
                                'advanced' => false,
                                'theme' => 'filter',
                            ],
                            'attributes' => [
                                'placeholder' => sprintf(__t('From ( %s )'), __t('Date'))
                            ],
                        ],
                    ],
                    [
                        'spec' => [
                            'name' => 'to_date',
                            'type' => Date::class,
                            'options' => [
                                'label' => sprintf(__t('To ( %s )'), __t('Date')),
                                'order' => 3,
                                'advanced' => false,
                                'theme' => 'filter',
                            ],
                            'attributes' => [
                                'placeholder' => sprintf(__t('To ( %s )'), __t('Date'))
                            ],
                        ],
                    ],
                    [
                        'spec' => [
                            'name' => 'status',
                            'type' => Select::class,
                            'options' => [
                                'label' => __t('Filter by') . ' ' . __t('Transaction Status'),
                                'order' => 4,
                                'empty_option' => __t('All Statuses'),
                                'value_options' => array_merge(['__clear__' => __t('All Statuses')],BankTransactionsStatusUtil::getStatusList()),
                                'advanced' => true,
                            ],
                            'attributes' => [
                                'placeholder' => __t('Filter by') . ' ' . __t('Transaction Status'),
                            ],
                        ]
                    ],
                    [
                        'spec' => [
                            'name' => 'from_amount',
                            'type' => Number::class,
                            'options' => [
                                'label' => sprintf(__t('From ( %s )'), __t('Amount')),
                                'order' => 5,
                                'advanced' => true,
                            ],
                            'attributes' => [
                                'placeholder' => sprintf(__t('From ( %s )'), __t('Amount')),
                            ],
                        ],
                    ],
                    [
                        'spec' => [
                            'name' => 'to_amount',
                            'type' => Number::class,
                            'options' => [
                                'label' => sprintf(__t('To ( %s )'), __t('Amount')),
                                'order' => 6,
                                'advanced' => true,
                            ],
                            'attributes' => [
                                'placeholder' => sprintf(__t('To ( %s )'), __t('Amount')),
                            ],
                        ],
                    ],
                    [
                        'spec' => [
                            'name' => 'type',
                            'type' => Select::class,
                            'options' => [
                                'label' => __t('Filter by') . ' ' . __t('Transaction Type'),
                                'order' => 7,
                                'empty_option' => __t('All Types'),
                                'value_options' => ['__clear__' => __t('All Types') ,'deposit' => __t('Deposit'), 'withdraw' => __t('Withdraw')],
                                'advanced' => true,
                            ],
                            'attributes' => [
                                'placeholder' => __t('Filter by') . ' ' . __t('Transaction Type'),
                            ],
                        ]
                    ],
                ]
        ];

        if (isset($this->parameters['bank_id']) && $this->parameters['bank_id'] > 0) {
            $specs['elements'][] =
                [
                    'spec' => [
                        'name' => 'bank_id',
                        'type' => Hidden::class,
                    ]
                ];
        }
        if (isset($this->parameters['iframe']) && 1 == $this->parameters['iframe']) {
            $specs['elements'][] =
                [
                    'spec' => [
                        'name' => 'iframe',
                        'type' => Hidden::class,
                    ]
                ];
        }

        return $specs;
    }

    public function unMatch(
        $bank_transaction_id,
        CommonActivityLogService $activityLogService
    ) {
        $bankTransaction = $this->repo->find($bank_transaction_id);
        if(!$bankTransaction) {
            throw new EntityNotFoundException(__t('Bank Transaction'));
        }
        foreach ($bankTransaction->systemTransactions as $systemTransaction) {
            $this->journalTransactionRepository->update($systemTransaction->id, ['bank_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
        }
        if ($bankTransaction->systemTransaction) {
            $this->journalTransactionRepository->update($bankTransaction->systemTransaction->id, ['bank_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
            $systemTransaction = $bankTransaction->systemTransaction;
            foreach ($systemTransaction->bank_transactions as $bankTransaction) {
                $this->repo->update($bankTransaction->id, ['journal_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
            }
        }
        $this->repo->update($bank_transaction_id, ['journal_transaction_id' => null, 'status' => BankTransactionsStatusUtil::NOT_MATCHED]);
        $activityLogRequest = $this->bankSystemTransactionService->createMatchingActivityLog(
            $bankTransaction,
            ['status' => BankTransactionsStatusUtil::NOT_MATCHED],
            ['status' => BankTransactionsStatusUtil::MATCHED],
        );
        $activityLogService->addActivity($activityLogRequest);
        return $bankTransaction;
    }
}
