<?php

namespace App\Services;

use App\Exceptions\EntityCouldntBeCreatedException;
use App\Exceptions\EntityNotFoundException;
use App\Helpers\FilterOperations;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\EntityRepository;
use App\Repositories\MachineRepository;
use App\Repositories\MachineTypeRepository;
use App\Requests\DefaultRequest;
use App\Requests\MachineIntegration\CreateMachineIntegrationRequest;
use App\Requests\MachineIntegration\MachinePullDataRequest;
use App\Responses\MachineSessionResponse;
use App\Services\AttendanceMachineIntegration\MachineIntegrationFactory;
use App\Utils\ActiveStatusUtil;
use App\Utils\AttendanceSessionSourceTypeUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * MachineService Class machine service
 * @package App\Services
 * <AUTHOR> <<EMAIL>>
 */
class MachineService extends BaseService
{
    /**
     * @var array filters
     */
    var $filters = [];

    /**
     * @var MachineRepository
     */
    var $repo;

    /**
     * @var string show route name
     */
    protected $showRouteName = 'owner.machines.show';
    /**
     * @var AttendanceSessionService|null
     */
    protected $attendanceSessionService = null;

    protected $attendanceLogService = null;

    /**
     * @var MachineTypeRepository
     */
    protected $machineTypeRepository;

    /**
     * MachineService constructor.
     * @param MachineRepository $repo
     * @param MachineTypeRepository $machineTypeRepository
     * @param AttendanceSessionService $attendanceSessionService
     * @param AttendanceLogService $attendanceLogService
     * @param EntityRepository|null $entityRepo
     */
    public function __construct(
        MachineRepository $repo,
        MachineTypeRepository $machineTypeRepository ,
        AttendanceSessionService $attendanceSessionService,
        AttendanceLogService $attendanceLogService,
        EntityRepository $entityRepo = null
    ) {
        parent::__construct($repo, $entityRepo);
        $this->machineTypeRepository = $machineTypeRepository;
        $this->attendanceSessionService = $attendanceSessionService;
        $this->attendanceLogService = $attendanceLogService;
        $this->getActivityLogService()->appendKeyToExceptKeys('communication_key');
    }


    public function findByUuid($uuid)
    {
        return $this->repo->findByUuid($uuid);
    }

    /**
     * {@inheritDoc}
     */
    public function getSortFields()
    {
        return [
            'fields' => [
                'created' => ['title' => __t('Date'), 'field' => 'created', 'direction' => 'DESC'],
                'name' => ['title' => __t('Name'), 'field' => 'name', 'direction' => 'DESC'],
                'id' => [
                    'title' => __t('ID'),
                    'field' => 'id',
                    'hidden' => true,
                    'direction' => 'DESC'
                ],
            ],
            'active' => [
                'field' => 'id',
                'direction' => 'DESC',
            ]
        ];
    }

    public function getFormRelatedData()
    {
        return [];
    }

    function getFilters()
    {
        $isNumber = isset($this->parameters['name']) && is_numeric($this->parameters['name']) ? true : false;
        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            return $this->filters = [
                'name' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => false,
                    'type' => 'text',
                    'attributes' => ['placeholder' => __t('Search by Machine Name or Serial Number')],
                    'div' => 'col-md-6',
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'before' => '<div class="form-group-icon  form-group">',
                    'filter_options' => [
                        'operation' => FilterOperations::IN_STRING_FILTER_OPERATION,
                        'field' => $isNumber ? 'serial_number' : 'name'
                    ]
                ],
                'status' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'attributes' => [
                        'placeholder' => __t('All Status'),
                        'id' => 'activeSelect'
                    ],
                    'label' => false,
                    'div' => 'col-md-6 form-group',
                    'options' => ['1' => __t('Active'), '0' => __t('Inactive')],
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'status'
                    ]
                ],
                'key' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'attributes' => [
                        'placeholder' => __t('All Types'),
                        'id' => 'type'
                    ],
                    'label' => false,
                    'div' => 'col-md-6 form-group',
                    'options' => $this->machineTypeRepository->list(['key', 'name']),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'machine_type_key'
                    ]
                ],
            ];
        }

    }

    /**
     * {@inheritDoc}
     * @throws EntityNotFoundException if machine type not sent
     */
    public function getFormData($id = false)
    {
        $formData = parent::getFormData($id);
        if (!$id) {
            $key = $this->parameters['machine_key'] ?? null;
            $machineType = $this->machineTypeRepository->find($key);
            if (!$machineType) {
                throw new EntityNotFoundException('Machine Type');
            }
            $formData['machine_type'] = $machineType;
        }
        return $formData;
    }

    /**
     * @param $machineId
     * @return bool
     * @throws \App\Exceptions\AttendanceMachineIntegration\UnsupportedMachine
     */
    public function testConnection($machineId){

        $machine = $this->repo->pushCriteria(new CustomFind([['field' => 'status', 'value' => ActiveStatusUtil::ACTIVE, 'operation' => 1]]))->findOrFail($machineId);

        $createMachineRequest = new CreateMachineIntegrationRequest($machine->host, $machine->port, $machine->communication_key);
        $machineIntegration = MachineIntegrationFactory::createMachine($machine->machine_type_key, $createMachineRequest);

        return $machineIntegration->testConnection();
    }

    /**
     * @param $machineId
     * @return MachineSessionResponse
     * @throws EntityCouldntBeCreatedException
     * @throws \App\Exceptions\AttendanceMachineIntegration\UnsupportedMachine
     * @throws \App\Exceptions\SessionCannotBeClosedException
     */
    public function pullData($machineId)
    {
        $machine = $this->repo->pushCriteria(new CustomFind([['field' => 'status', 'value' => ActiveStatusUtil::ACTIVE, 'operation' => 1]]))->findOrFail($machineId);
        if(empty($machine->port)) {
            throw new \Exception(__t('Machine must have port number to pull the data from.'));
        }
        $createMachineRequest = new CreateMachineIntegrationRequest($machine->host, $machine->port, $machine->communication_key);
        $session = $this->attendanceSessionService->createSession(
            AttendanceSessionSourceTypeUtil::ATTENDANCE_SESSION_SOURCE_TYPE_MACHINE,
            $machineId,
            $machine->name,
            ""
            );
        if(!$session)
        {
            throw new EntityCouldntBeCreatedException('Session');
        }
        $machineStaffMappings = $this->repo->getMachineMappingsList($machineId);
        $machineIntegration = MachineIntegrationFactory::createMachine($machine->machine_type_key, $createMachineRequest);
        $lastMachineSignDate = !empty($machine->last_sign_date) ? convertFromUtc($machine->last_sign_date) : null;
        try{
            $result = $machineIntegration->pullData(new MachinePullDataRequest($session->id, $lastMachineSignDate, $machineId, $machine->name, $machineStaffMappings));

            //TODO
            /*
             check for empty result
            if empty, new machine request with UDP protocol
            create new integration with the new request (with udp)
            then, call pull data
             * */
        }catch (\Exception $e)
        {
            $this->attendanceSessionService->closeSession($session->id, $session->source_type, $session->source_id);
            $machineIntegration->disconnect();
            throw $e;
        }
        $machineSigns = $result->getMachineSigns();
        $signsCount = count($machineSigns);
        $invalidStaffIds = $result->getInvalidStaffIds();
        $machineUpdateData = ['last_pull_date' => convertToUtc(Carbon::now()->toDateTimeString(), null, false)];
        if(!empty($machineSigns))
        {
            list($lastMachineSignDate, $signsCount) = $this->attendanceLogService->saveMachineSigns($machineSigns);
            if(empty($invalidStaffIds))
            {
                $machineUpdateData['last_sign_date'] = $lastMachineSignDate;
            }
        }
        $this->update($machineId, new DefaultRequest($machineUpdateData));
        $this->attendanceSessionService->closeSession($session->id, $session->source_type, $session->source_id);
        $machineIntegration->disconnect();
        return new MachineSessionResponse($session->id, $invalidStaffIds, $signsCount);

    }

    public function getViewData($id)
    {
        $data =  parent::getViewData($id); // TODO: Change the autogenerated stub
        $data['pulledSignsCount'] = $this->repo->getTotalPulledSigns($id);
        return $data;
    }

}
