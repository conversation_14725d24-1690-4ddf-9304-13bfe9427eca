<?php

namespace App\Services;

use App\Exceptions\Shift\NoShiftSelectedException;
use App\Exceptions\ShiftHasAllocatedShifts;
use App\Exceptions\ShiftHasStaffException;
use App\Exceptions\ShiftNeedWeekDaysException;
use App\Exceptions\ShiftSaveFailedException;
use App\Facades\Plugins;
use App\Repositories\AttendanceFlagsRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\ShiftOffDaysCriteria;
use App\Repositories\ShiftRepository;
use App\Helpers\FilterOperations;
use App\Repositories\EntityRepository;
use App\Requests\DefaultRequest;
use App\Services\AttendanceCalculator\Entities\Shift;
use App\Services\AttendanceCalculator\Entities\ShiftDay;
use App\Utils\ActiveStatusUtil;
use App\Utils\MultipleShiftTypeUtil;
use App\Utils\PluginUtil;
use App\Utils\ShiftTypeUtil;
use App\Utils\ShiftUtils;
use App\Utils\ShiftWeekdayUtil;
use Closure;
use Izam\Daftra\Common\Utils\DayTimeUtil;
use App\Exceptions\DefaultBookingShiftException;
use Carbon\Carbon;
use App\Exceptions\EntityNotFoundException;

use Exception;

class ShiftService extends BaseService {

    var $filters = [];
    /**
     * @var $repo ShiftRepository
     */
    var $repo;
    private $daysList = [];
    protected $showRouteName = 'owner.shifts.show';
    private $attendaceFlagRepository = null;
    public function __construct(ShiftRepository $repo , AttendanceFlagsRepository $attendanceFlagsRepository,EntityRepository $entityRepo = null)
    {
        $this->daysList = ShiftWeekdayUtil::getDaysList();
        $this->attendaceFlagRepository = $attendanceFlagsRepository;
        parent::__construct($repo, $entityRepo);
    }


    public function getFormRelatedData()
    {
        $typesList = $this->getTypesList();
        if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN))
            $attendanceFlags = $this->attendaceFlagRepository->pushCriteria(new CustomFind([['field' => 'status','value' => ActiveStatusUtil::ACTIVE, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]]))->list();
        else
            $attendanceFlags = [];
        return ['isAdvanced' => false, 'shiftDays' => [], 'selectedAttendanceFlags' => [], 'attendanceFlags' => $attendanceFlags,'types' => $typesList, 'daysList' => $this->getDaysList()];
    }

    function getFilters()
    {
        if(!empty($this->filters))
        {
            return $this->filters;
        }else{
            return  $this->filters = [
                'name' =>
                    [
                        'simple' => true, /* displays the filter outside the toggle */
                        'after' => '<i class="input-icon fal fa-search"></i></div>',
                        'before' => '<div class="form-group-icon  form-group">',
                        'label' => false,
                        'div' => 'col-md-12',
                        'attributes' => ['placeholder' => __t('Search By Shift Name')],
                        "type" => "text",
                        "filter_options" => ["operation" => FilterOperations::IN_STRING_FILTER_OPERATION,
                        "field" => "name"
                        ]
                    ],
                'off_days[]' => [
                    'simple' => true,
                    'before' => '<div class=" form-group">',
                    'after' => '</div>',
                    'label' =>'',
                    'param_name' =>'off_days',
                    'div' => 'col-md-4',
                    'attributes' => ['placeholder' => __t('Weekend Days'), 'multiple'=>true],
                    "type" => "select",
                    'value'=>[],
                    'options' => ShiftWeekdayUtil::getDaysListTranslated(),
                    'inputClass' => 'form-control select-filter',
                    'filter_options' => [
                        'operation' => FilterOperations::CRITERIA,
                        'criteria' => new ShiftOffDaysCriteria($this->parameters['off_days'] ?? null),
                    ]
                ],
                'from_time' => [
                    'simple' => true,
                    'type' => 'select',
                    'empty' => 'text',
                    'attributes' => ['placeholder' => __t('Shift Start Time')],
                    'label' => '',
                    'inputClass' => 'select-filter',
                    'div' => 'col-md-4',
                    'before' => '<div class="form-group">',
                    'after' => '</div>',
                    'options' => DayTimeUtil::getDayTimeList(),
                    'filter_options' => [
                        'model' => 'shiftDays',
                        'table' => 'shift_days',
                        'operation' => FilterOperations::BIGGER_THAN_OR_EQUAL,
                        'field' => 'from_time'
                    ]
                ],
                'to_time' => [
                    'simple' => true,
                    'type' => 'select',
                    'empty' => 'text',
                    'attributes' => ['placeholder' => __t('Shift End Time')],
                    'label' => '',
                    'inputClass' => 'select-filter',
                    'div' => 'col-md-4',
                    'before' => '<div class="form-group">',
                    'after' => '</div>',
                    'options' => DayTimeUtil::getDayTimeList(),
                    'filter_options' => [
                        'model' => 'shiftDays',
                        'table' => 'shift_days',
                        'operation' => FilterOperations::SMALLER_THAN_OR_EQUAL,
                        'field' => 'to_time'
                    ]
                ],
            ];
        }

    }

    public function getTypesList()
    {
        return [
            'standard' => __t('Standard'),
            'advanced' => __t('Advanced')
        ];
    }

    function getViewData($id)
    {
        $parentData  = parent::getViewData($id); // TODO: Change the autogenerated stub
        $daysList = $this->getDaysList();
        $shiftDays = [];
        foreach ($parentData['record']->shiftDays as $k => $shiftDay)
        {
            $shiftDays[$shiftDay['weekday']] = $shiftDay;
        }
        if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN))
            $attendanceFlags = implode(', ', $parentData['record']->AttendanceFlags()->pluck('name')->toArray());
        else
            $attendanceFlags = [];
        return $parentData + ['typesList' => $this->getTypesList(), 'daysList' => $daysList, 'shiftDays' => $shiftDays, 'attendanceFlags' => $attendanceFlags];
    }

    function getDaysList()
    {
        $orderdDaysList = [];
        $settings_week_day= BOOKING_FIRST_WEEKDAY;
        $sunday_start_countries = ['DZ', 'AF', 'BH', 'BD', 'EG', 'IL', 'IQ', 'JO', 'KW', 'LY', 'MY', 'MV', 'OM', 'PS', 'QA', 'SQ', 'SD', 'SY', 'YE'];
        if(!in_array(getCurrentSite('country_code'), $sunday_start_countries)){
            $settings_week_day = 'monday';
        }
        //we get the date of the next start weekday
        $nextStartDayDate = date('Y-m-d', strtotime('next '.$settings_week_day));
        for ($i = 0; $i < 7; $i++){
            //then we add i number of days on the start date to get all other week days
            $dayOfNextWeekDay = strtolower(date('l', strtotime($nextStartDayDate ."+$i day")));
            $orderdDaysList[$i] = ['name' => $this->daysList[$dayOfNextWeekDay],'value' => $dayOfNextWeekDay];
        }
        return $orderdDaysList;
    }

    function getDaysListArray()
    {
        return ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
    }

    function formatFormData($shiftData)
    {

        if(isset($shiftData['shift_days']))
        {
            $formattedShiftDays = [];
            if(isset($shiftData['shift_days']['all'])) {
                $allShiftDaysData = $shiftData['shift_days']['all'];
                unset($shiftData['shift_days']['all']);
                foreach ($shiftData['shift_days'] as $weekDayName => $shiftDay) {
                    if(!isset($shiftData['shift_days'][$weekDayName]['weekday']) &&  !isset($shiftData['shift_days'][$weekDayName]['can_work_on_off_days'])) continue;
                    $formattedShiftDays[$weekDayName] = $allShiftDaysData;
                    $formattedShiftDays[$weekDayName]['weekday'] = $weekDayName;
                    $formattedShiftDays[$weekDayName]['can_work_on_off_days'] = isset($shiftData['shift_days'][$weekDayName]['can_work_on_off_days']) ? true:false;
                }
                $shiftData['shift_days'] = $formattedShiftDays;

            }else{
                foreach ($shiftData['shift_days'] as $weekDayName => $shiftDay)
                {
                    foreach ($shiftData['shift_days'] as $weekDayName => $shiftDay) {
                        $formattedShiftDays[$weekDayName] = $shiftDay;
                        if(!isset($formattedShiftDays[$weekDayName]['weekday']))
                        {
                            $formattedShiftDays[$weekDayName]['weekday'] = $weekDayName;
                            $formattedShiftDays[$weekDayName]['can_work_on_off_days'] = isset($shiftData['shift_days'][$weekDayName]['can_work_on_off_days']) ? true:false;

                        }
                        if(isApi() && ifPluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
                            $formattedShiftDays[$weekDayName]['beginning_in'] = $shiftDay['beginning_in'] ?? Carbon::parse($shiftDay['from_time'])->subHour()->toTimeString();
                            $formattedShiftDays[$weekDayName]['ending_in'] = $shiftDay['ending_in'] ?? Carbon::parse($shiftDay['from_time'])->addHour()->toTimeString();
                            $formattedShiftDays[$weekDayName]['beginning_out'] = $shiftDay['beginning_out'] ?? Carbon::parse($shiftDay['to_time'])->subHour()->toTimeString();
                            $formattedShiftDays[$weekDayName]['ending_out'] = $shiftDay['ending_out'] ?? Carbon::parse($shiftDay['to_time'])->addHour()->toTimeString();
                            $formattedShiftDays[$weekDayName]['late_time'] = $shiftDay['late_time'] ?? 15;
                            $formattedShiftDays[$weekDayName]['start_late_time_from_on_duty'] = $shiftDay['start_late_time_from_on_duty'] ?? 0;
                        }
                    }
                }
            }
            $shiftData['shift_days'] = $formattedShiftDays;
        }
        return $shiftData;
    }

    function add($request, array $excludedFields = [], Closure $callback = null)
    {

        if (empty($request['shift_days'])) {
            throw new ShiftNeedWeekDaysException;
        }
        $shift = parent::add($request, $excludedFields); // TODO: Change the autogenerated stub

        if(isset($shift->id))
        {
            $shiftId = $shift->id;
            if($request['shift_days'] )
            {
                foreach ($request['shift_days'] as $shiftDay)
                {
                    $shiftDay['shift_id'] = $shiftId;
                    $this->repo->saveShiftDay($shiftDay);
                }
            }
            /** Removed in Story DAFTRA-1682*/
//            $attendanceFlags = $request->get('attendance_flags');
//            if($attendanceFlags)
//            {
//                $this->repo->saveShiftAttendanceFlags($shiftId, $request->get('attendance_flags'));
//            }
        }else{
            throw new ShiftSaveFailedException();
        }
        return $shift;

    }

    function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $this->disableLogging();
        $oldRecord = $this->find($id);
        if(!$oldRecord) {
            throw new EntityNotFoundException('Shift');
        }
        if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN))
            $oldRecord = $oldRecord->load('shiftDays')->load('AttendanceFlags');
        if (empty($request['shift_days'])) {
            throw new ShiftNeedWeekDaysException;
        }
        $shift =  parent::update($id, $request, $excludedFields); // TODO: Change the autogenerated stub
        if(isset($shift->id)) {
            $shiftId = $shift->id;
            $this->repo->deleteShiftDays($id);
            if(isset($request['shift_days'])) {
                foreach ($request['shift_days'] as $shiftDay) {
                    $shiftDay['shift_id'] = $shiftId;
                    $this->repo->saveShiftDay($shiftDay);
                }
            }

            /** Removed in Story DAFTRA-1682*/
//            $attendanceFlags = $request->get('attendance_flags')?$request->get('attendance_flags'):[];
//            $this->repo->saveShiftAttendanceFlags($shiftId, $attendanceFlags);

            $this->logUpdate(
//                $shift->load('shiftDays')->load('AttendanceFlags'),
                $shift->load('shiftDays'),
                $oldRecord
            );
        }else{

            throw new ShiftSaveFailedException();
        }
        return $shift;
    }

    function getFormData($id = false)
    {
        $parentData  = parent::getFormData($id); // TODO: Change the autogenerated stub
        $shiftDays = [];
        if($id)
        {
            if(old('type', $parentData['form_record']->type) === ShiftTypeUtil::SHIFT_TYPE_ADVANCED)
            {
                $parentData['isAdvanced'] = true;
            }
            if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN))
                $selectedAttendanceFlagList = $this->repo->getShiftAttendanceFlagsList($id);
            else
                $selectedAttendanceFlagList=[];
            $parentData['related_form_data']['selectedAttendanceFlags'] = $selectedAttendanceFlagList;

            $shiftDays = [];
            if(empty(old()))
            {
                foreach ($parentData['form_record']->shiftDays as $k => $shiftDay)
                {
                    $shiftDays[$shiftDay['weekday']] = $shiftDay;
                }
            }

        }
        $parentData['shiftDays'] = old('shift_days', $shiftDays);

        return $parentData;
    }

    public function delete($id, Closure $callback = null)
    {
        $shift = $this->find($id);
        if(!$shift) {
            throw new EntityNotFoundException('Shift');
        }

        if(count($shift->staff) > 0 && !$this->checkIfAllAssigedStaffsInActive($shift->staff))
        {
            throw new ShiftHasStaffException;
        }

        if(count($shift->allocatedShifts) > 0)
        {
            throw new ShiftHasAllocatedShifts;
        }

        if(count($shift->assignedBookingStaff) > 0  && !$this->checkIfAllAssigedStaffsInActive($shift->assignedBookingStaff))
        {
            throw new ShiftHasStaffException;
        }

        if(Plugins::pluginActive(PluginUtil::NEW_BOOKING_PLUGIN) && \Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'booking_default_shift', null, false) == $id)
        {
            throw new DefaultBookingShiftException;
        }

        $this->repo->deleteShiftDays($id);

        /** Removed in Story DAFTRA-1682*/
//        $this->repo->deleteShiftAttendanceFlags($id);

        $result = parent::delete($id); // TODO: Change the autogenerated stub
        return $result;

    }

    private function checkIfAllAssigedStaffsInActive($staffs){
        $result = true;
        foreach($staffs as $staff){
            if($staff->active){
                return false;
            }
        }
        return $result;
    }

    public function getWithRelations()
    {
        return ['activeStaffList'];
    }

    public function listing(): array
    {
        $weekDays = $this->getDaysList();
        return parent::listing(); // TODO: Change the autogenerated stub
    }

    function getRecordUpdatedActivityLogData($recordData, $oldRecordData)
    {
        $weekdays = ShiftWeekdayUtil::getDaysList();
        $newShiftDaysList = [];
        $oldShiftDaysList = [];
        $newAttendanceFlagsList = [];
        $oldAttendanceFlagsList = [];
        foreach ($recordData->shiftDays as $k => $shiftDay) {
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' Start of Sign-In'] = $shiftDay['beginning_in'];
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' End of Sign-In'] = $shiftDay['ending_in'];
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' Start of Sign-Out'] = $shiftDay['beginning_out'];
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' End of Sign-Out'] = $shiftDay['ending_out'];
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' On Duty Time'] = $shiftDay['from_time'];
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' Of Duty Time'] = $shiftDay['to_time'];
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' Late Time'] = $shiftDay['late_time'];
            $newShiftDaysList[$weekdays[$shiftDay['weekday']].' Start Late Time'] = $shiftDay['start_late_time_from_on_duty'] ? 'From On Duty Time' : 'After On Duty Time + Lateness grace period';
        }

        foreach ($oldRecordData->shiftDays as $k => $shiftDay) {
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' Start of Sign-In'] = $shiftDay['beginning_in'];
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' End of Sign-In'] = $shiftDay['ending_in'];
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' Start of Sign-Out'] = $shiftDay['beginning_out'];
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' End of Sign-Out'] = $shiftDay['ending_out'];
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' On Duty Time'] = $shiftDay['from_time'];
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' Of Duty Time'] = $shiftDay['to_time'];
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' Late Time'] = $shiftDay['late_time'];
            $oldShiftDaysList[$weekdays[$shiftDay['weekday']].' Start Late Time'] = $shiftDay['start_late_time_from_on_duty'] ? 'From On Duty Time' : 'After On Duty Time + Lateness grace period';
        }
        $newAttendanceFlagsList = [];
        $oldAttendanceFlagsList = [];
        if(Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN)){
                foreach ($recordData->AttendanceFlags as $k => $attendance_flag) {
                $newAttendanceFlagsList['Attendance Flag'] = $attendance_flag['name'];
            }

            foreach ($oldRecordData->AttendanceFlags as $k => $attendance_flag) {
                $oldAttendanceFlagsList['Attendance Flag'] = $attendance_flag['name'];
            }
        }
        unset($recordData['attendance_flags'], $oldRecordData['attendance_flags'], $recordData['shift_days'], $oldRecordData['shift_days']);
        $recordData['Shift Days'] = $newShiftDaysList;
        $oldRecordData['Shift Days']  = $oldShiftDaysList;
        $recordData['Attendance Flags'] = $newAttendanceFlagsList;
        $oldRecordData['Attendance Flags']  = $oldAttendanceFlagsList;

        $oldRecordData->unsetRelation('shiftDays');
        $recordData->unsetRelation('shiftDays');
        $oldRecordData->unsetRelation('AttendanceFlags');
        $recordData->unsetRelation('AttendanceFlags');
        // TODO: Change the autogenerated stub
        return parent::getRecordUpdatedActivityLogData($recordData, $oldRecordData);
    }

    /**
     * returns service sort fields
     * @return array
     */
    function getSortFields()
    {
        return [
            'fields' => [
                'name' => ['title' => __t('Name'), 'field' => 'name', 'direction' => 'DESC'],
                'created' => ['title' => __t('Date of Creation'), 'field' => 'created', 'direction' => 'ASC'],
                'id' => [
                    'title' => __t('ID'),
                    'field' => 'id',
                    'hidden' => true,
                    'direction' => 'DESC'
                ],
            ],
            'active' => [
                'field' => 'id',
                'direction' => 'DESC',
            ]
        ];

    }

    /**
     * @param $shiftId
     * @param $date
     */
    function isDateShiftDay($shiftId, $date){
        $shiftDays = $this->repo->getShiftDays($shiftId);
        $dateWeekDay = strtolower(date('l', strtotime($date)));
        if($shiftDays)
        {
            foreach ($shiftDays as $k => $shiftDay)
            {
                if($shiftDay->weekday == $dateWeekDay)
                {
                    return true;
                }
            }
        }
        return false;
    }


    public function getShiftWithDaysByStaffId($emp_id, $type = MultipleShiftTypeUtil::PRIMARY)
    {
        if ($type == MultipleShiftTypeUtil::PRIMARY)
            $shift = $this->repo->getPrimaryShiftByStaffId($emp_id);
        else if ($type == MultipleShiftTypeUtil::SECONDARY)
            $shift = $this->repo->getSecondaryShiftByStaffId($emp_id);
        else
            return null;

        if (!$shift) {
            return NULL;
        }

        $shift_days = $this->repo->getShiftDays($shift->id);
        $shift_days = array_map(function ($x) {
            if (Plugins::pluginInstalled(PluginUtil::HRM_ATTENDANCE_PLUGIN))
                return new ShiftDay($x->weekday, $x->from_time, $x->to_time, $x->beginning_in, $x->ending_in, $x->beginning_out, $x->ending_out, $x->late_time, $x->start_late_time_from_on_duty, $x->can_work_on_off_days);
            else
                return new ShiftDay($x->weekday, $x->from_time, $x->to_time);
        }, $shift_days);

        return new Shift($shift->id, $shift->name, $shift_days);
    }

    public function getShiftEmployeesIds($shift_id = null){
        return $this->repo->getShiftEmployeesIds($shift_id);
    }

    /**
     * @param int $id
     * @return array
     */
    public function getCloneData(int $id)
    {
        $data = $this->getFormData($id);
        $data['form_record']->name = $data['form_record']->name . " Clone";
        return $data;
    }

    /**
     * @param $id
     * @param DefaultRequest $request
     * @return mixed
     * @throws ShiftNeedWeekDaysException
     * @throws ShiftSaveFailedException
     */
    public function clone($id, DefaultRequest $request)
    {
        return $this->add($request, ['_token', 'id', '_method']);
    }

    public function undoDeleteShift($shiftId){
        $this->repo->undoDeleteShift($shiftId);
    }

    private function checkShiftsNotEmpty(array $shiftsIds)
    {
        if (implode(null, $shiftsIds) == null)
            throw new NoShiftSelectedException();
    }

    public function deleteMany(array $ids, $selectType = null)
    {
        $this->checkShiftsNotEmpty($ids);
        $deletedCount = 0;
        $unDeletedCount = 0;
        $invalidDeleteData = [];
        foreach ($ids as $id) {
            try {
                $this->delete($id);
                $deletedCount++;
            } catch (Exception $exception) {
                $invalidDeleteData[$id] = $exception->getMessage();
                $unDeletedCount++;
                continue;
            }
        }
        return ['deletedCount' => $deletedCount, 'unDeletedCount' => $unDeletedCount, 'invalidDeleteData' => $invalidDeleteData];
    }
    public function getFilteredRecords()
    {
        $this->repo->pushCriteria(new CustomFind($this->getListingConditions(), []));
        return $this->repo->list();
    }

    function filterName($query)
    {
        return $this->repo->getAutoCompleteResult($query);
    }
}
