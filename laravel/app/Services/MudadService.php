<?php

namespace App\Services;


use App\Facades\Settings;
use App\Services\Mudad\MudadData;
use App\Services\Mudad\MudadFileData;
use App\Services\Mudad\MudadFileParser;
use App\Services\Mudad\MudadRowsMappingDataPipeline;
use App\Utils\PluginUtil;

use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Izam\Daftra\Injector\SettingRepository;
use App\Repositories\EntityAppDataRepository;
use App\Repositories\PayRunRepository;
use App\Repositories\SalaryComponentRepository;
use App\Repositories\StaffRepository;
use App\Requests\DefaultRequest;
use App\Services\Mudad\MudadDataBuilder;
use App\Services\Mudad\MudadEmployeeRow;
use App\Utils\EntityKeyTypesUtil;
use Izam\Aws\S3UploadHandler;
use Izam\Daftra\Common\Utils\EntityAppDataKeysUtil;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;

class MudadService
{
    const MUDAD_FILE_PROCESS = "modad_file_process";
    protected string $basic_salary_component = 'الراتب الأساسي';
    protected string $residency_compensation_component = 'بدل السكن';
    protected string $transportation_compensation_component = 'بدل النقل';

    protected $settingRepository;
    protected MudadDataBuilder $mudadDataBuilder;
    protected EntityAppDataRepository $entityAppDataRepo;
    protected PayRunRepository $payRunRepo;
    protected SalaryComponentRepository $salaryComponentRepo;
    protected FileService $fileService;
    protected StaffRepository $staffRepo;

    public function __construct(SettingRepository $settingRepository,
     EntityAppDataRepository $entityAppDataRepository,
     MudadDataBuilder $mudadDataBuilder,
     PayRunRepository $payRunRepo,
     SalaryComponentRepository $salaryComponentRepo,
     FileService $fileService,
     StaffRepository $staffRepository) {
        $this->settingRepository = $settingRepository;
        $this->entityAppDataRepo = $entityAppDataRepository;
        $this->payRunRepo = $payRunRepo;
        $this->mudadDataBuilder = $mudadDataBuilder;
        $this->salaryComponentRepo = $salaryComponentRepo;
        $this->fileService = $fileService;
        $this->staffRepo = $staffRepository;
    }

    /**
     * Check if Mudad settings are properly configured
     *
     * @return bool
     */
    public function isMudadSettingsSet(): bool
    {
        $settings = Settings::getValue(PluginUtil::MUDAD_PLUGIN, \App\Helpers\Settings::MUDAD_SALARY_COMPONENTS);
        $settingComponents = json_decode($settings);

        if (!$settingComponents) {
            return false;
        }
        foreach ($settingComponents as $component) {
            if (empty($component->salary_components)) {
                return false;
            }
        }
        return true;
    }

    public function uploadMudadFile($file)
    {
        $fileArray = [
            'tmp_name' => $file->getPathname(),
            'name' => $file->getClientOriginalName()
        ];
        $uploadedFile = App(S3UploadHandler::class)->uploadFile($fileArray, SITE_HASH, EntityKeyTypesUtil::PAY_RUN);
        $fileArray['file_id'] = $uploadedFile['file_id']?? null;
        $extension = strtolower($file->getClientOriginalExtension());
        $fileArray['file_type'] = in_array($extension, ['csv']) ? 'CSV' : 'Excel Spreadsheet';
        $fileSizeMB = round($file->getSize() / (1024 * 1024), 2);
        $fileArray['file_size'] = $fileSizeMB > 0 ? $fileSizeMB . ' MB' : '0.01 MB';

        return $fileArray;
    }

    function processMudadFile($payRunId, $file)
    {
        $payRun = $this->payRunRepo->find($payRunId);
        if(empty($payRun)){
            throw new \Exception( sprintf(__t('%s Not Found'), __t('payrun')));

        }

        $fileArray = $this->uploadMudadFile($file);
        $mudadObject = [
            "file_id" => $fileArray['file_id'],
            "step" => 1,
            "file_meta" => [
                "type" => "json",
                "file_name" => $fileArray['name'],
                "uploaded_by" => getAuthOwner('staff_id'),
                "file_type" => $fileArray['file_type'],
                "file_size" => $fileArray['file_size'],
                "uploaded_on" => now()->format('Y-m-d H:i:s'),
                "start_date" => $payRun->start_date,
                "end_date" => $payRun->end_date,
            ]
        ];
        $this->entityAppDataRepo->deleteCritria(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );
        $mudadJSON = json_encode($mudadObject);
        $this->entityAppDataRepo->insertGetId(
            [
                'entity_key' => EntityKeyTypesUtil::PAY_RUN,
                'entity_id' => $payRunId,
                'app_key' => EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
                'data' => $mudadJSON,
                'action_key' => self::MUDAD_FILE_PROCESS
            ]);
        return $this->mudadDataBuilder->build($mudadJSON);

    }

    public function clearMudadFile($payRunId)
    {
        $this->entityAppDataRepo->deleteCritria(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );

        return true;
    }



    public function getMudadData(int $payRunId)
    {
        $result = $this->entityAppDataRepo->getDataByAppAndEntity(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );

        if (empty($result)) {
            return null;
        }

        $mudadData = $this->mudadDataBuilder->build($result->data);
        return $mudadData->getOriginalJsonData();
    }


    public function getMudadSalaryComponents()
    {
        $settings = Settings::getValue(PluginUtil::MUDAD_PLUGIN, \App\Helpers\Settings::MUDAD_SALARY_COMPONENTS );
        if (is_array($settings)) return $settings;
        return json_decode($settings, true);
    }

    public function setMudadSalaryComponents($components)
    {
        return Settings::setValue(PluginUtil::MUDAD_PLUGIN, \App\Helpers\Settings::MUDAD_SALARY_COMPONENTS  ,json_encode($components));
    }

    /**
     * Retrieve and process Mudad statistics for a specific payrun
     *
     * This method fetches Mudad data for the specified payrun, processes it through
     * the Mudad pipeline, and returns statistical information about the payrun's
     * Mudad integration status.
     *
     * The method performs the following steps:
     * 1. Retrieves the stored Mudad data for the payrun from the entity app data repository
     * 2. Builds a MudadData object from the stored data
     * 3. Initializes a new MudadFileData object with primary components from settings
     * 4. Uses MudadFileParser to map employee data from the uploaded file
     * 5. Processes the data through the MudadRowsMappingDataPipeline
     * 6. Returns the processed data as a JSON string
     *
     * @param int $payRunId The ID of the payrun to retrieve Mudad statistics for
     *
     * @return string JSON-encoded string containing the Mudad statistics data
     *
     * @throws \Exception If Mudad data is not found for the specified payrun
     * @throws \Exception If there are issues processing the Mudad data
     */
    public function getMudadStats($payRunId)
    {
        $mudadData = $this->entityAppDataRepo->getDataByAppAndEntity(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );

        $mudadData = $this->mudadDataBuilder->build($mudadData?->data??"");
        if (!$mudadData) {
            throw new \Exception(__t('Mudad data not found for this pay run'));
        }
        $mudadData->setPayRunId($payRunId);

        $mudadFileData = new MudadFileData();
        $mudadComponents = Settings::getValue(PluginUtil::MUDAD_PLUGIN, \App\Helpers\Settings::MUDAD_SALARY_COMPONENTS);
        $mudadComponents = json_decode($mudadComponents);
        $mudadFileData->setPrimaryComponents($mudadComponents);

        $mudadFileParser = new MudadFileParser($mudadData->getFileId());
        $mudadFileData = $mudadFileParser->mapEmployeesData($mudadData, $mudadFileData);
        $pipeline = new MudadRowsMappingDataPipeline();
        $reviewData = $pipeline->process($mudadData, $mudadFileData);
        return json_encode($reviewData->toArray());
    }

    /**
     * Map staff official IDs for Mudad integration
     *
     * This method updates the official IDs for multiple staff members and
     * saves the mapping data in the Mudad configuration for the specified payrun.
     *
     * @param int $payRunId The ID of the payrun to update mapping data for
     * @param array $staffsMappingData Array of staff mapping data. Each element should be an array with:
     *        - 'employee_id' (int): The internal staff/employee ID
     *        - 'official_id' (string): The official ID to be assigned to the staff member
     *
     * @return array The updated Mudad mapping data
     * @throws \Exception If there's an error updating the staff records or mapping data
     */
    public function mapStaffsOfficailId($payRunId, $staffsMappingData)
    {
        $staffService = resolve(StaffService::class);
        $updatedStaff = $staffService->updateStaffOfficialIds($staffsMappingData ?? []);
        return $this->updateMudadMappingData($payRunId, $updatedStaff);

    }


    /**
     * @throws FileNotFoundException
     */
    public function getMudadFileComponents($payRunId)
    {
        $entityAppData = $this->entityAppDataRepo->getDataByAppAndEntity(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );

        $decodeEntityAppData = json_decode($entityAppData->data ?? "", true);
        if (empty($decodeEntityAppData) || empty($decodeEntityAppData['file_id'])) {
            throw new \Exception( sprintf(__t('%s Not Found'), __t('Payrun')));
        }

        if (isset($decodeEntityAppData['mappings'])) {
            $mappings = $decodeEntityAppData['mappings'];
        }else{
            $mudadFileParser = new MudadFileParser($decodeEntityAppData['file_id']);
            $mappings = $mudadFileParser->parse();
        }
        $this->updateStep($payRunId, 2);
        return [
            "file_id"=>$decodeEntityAppData['file_id'],
            'file_data'=> $decodeEntityAppData['file_meta'] ,
            "mappings"=> $mappings
        ];
    }


    public function updateMudadFileComponents($payRunId , $data)
    {
        $entityAppData = $this->entityAppDataRepo->getDataByAppAndEntity(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );
        $decodeEntityAppData = json_decode($entityAppData->data ?? "", true);
        if (empty($decodeEntityAppData) || empty($decodeEntityAppData['file_id'])) {
            throw new \Exception( sprintf(__t('%s Not Found'), __t('Payrun')));
        }

        $decodeEntityAppData['mappings'] = $data['mappings'];
        $this->updateMudadDefaultComponents($data['mappings']);
        if($decodeEntityAppData['step'] < 3){
            $decodeEntityAppData['step'] = 3;
        }
        $entityAppData->update([
            'data' => json_encode($decodeEntityAppData),
        ]);
    }

    private function updateMudadDefaultComponents(array $newMappings)
    {
        $defaultSalaryComponents = Settings::getValue(PluginUtil::MUDAD_PLUGIN , 'mudad_default_headers_components') ?? "{}";
        $mappedComponents = json_decode($defaultSalaryComponents ?? "" , true);

        $additions = $newMappings['earnings'];
        $deductions = $newMappings['deductions'];
        $components = array_merge($additions , $deductions);

        foreach ($components as $component) {
            if (array_key_exists($component['name'] , $mappedComponents)) {
                $mappedComponents[$component['name']] = array_unique(array_merge($mappedComponents[$component['name']] , $component['components']));
            }else{
                $mappedComponents[$component['name']] = $component['components'];
            }
        }

        Settings::setValue(PluginUtil::MUDAD_PLUGIN , 'mudad_default_headers_components' , json_encode($mappedComponents) );;
    }

    public function updateMudadMappingData($payRunId, array $data = [])
    {
        $entityAppData = $this->entityAppDataRepo->getDataByAppAndEntity(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );

        $mudadData = $this->mudadDataBuilder->build($entityAppData->data ?? "");
        $mudadData->setPayRunId($payRunId);

        $mudadFileData = new MudadFileData();
        $mudadComponents = Settings::getValue(PluginUtil::MUDAD_PLUGIN, \App\Helpers\Settings::MUDAD_SALARY_COMPONENTS);
        $mudadComponents = json_decode($mudadComponents);
        $mudadFileData->setPrimaryComponents($mudadComponents);

        $decodeEntityAppData = json_decode($entityAppData->data ?? "", true);
        $mudadFileParser = new MudadFileParser($decodeEntityAppData['file_id']);
        $mudadFileData = $mudadFileParser->mapEmployeesData($mudadData, $mudadFileData);
        $pipeline = new MudadRowsMappingDataPipeline();
        $reviewData = $pipeline->process($mudadData, $mudadFileData);
        // dd($reviewData);
        $emplyeeFound = false;
        $decodeEntityAppData['employeesMapping'] = $decodeEntityAppData['employeesMapping']?? [] ;
        foreach($decodeEntityAppData['employeesMapping'] as  $key => $item){
            if($item['official_id'] == $data['official_id']){
                $decodeEntityAppData['employeesMapping'][$key]['employee_id'] = $data['employee_id'];
                $emplyeeFound = true;
                break;
            }
            if($item['employee_id'] == $data['employee_id']){
               return false;
            }

        }
        foreach($reviewData->getRows() as $employee){
            if( isset($employee->getMappedEmployee()['id']) &&($employee->getMappedEmployee()['id'] ?? "") == $data['employee_id']){
                return false;
            }
        }
        if(!$emplyeeFound){
            $decodeEntityAppData['employeesMapping'][] = $data;
        }

        $entityAppData->update([
            'data' => json_encode($decodeEntityAppData),
        ]);

        $mudadData = $this->mudadDataBuilder->build($entityAppData->data ?? "");
        return $mudadData->getOriginalJsonData();
    }

    public function updateStep($payRunId, $step){

        $entityAppData = $this->entityAppDataRepo->getDataByAppAndEntity(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payRunId
        );

        $decodeEntityAppData = json_decode($entityAppData->data ?? "", true);
        $currentStep = $decodeEntityAppData['step'] ?? 1;
        if($step > $currentStep){
            $decodeEntityAppData['step'] = $step;

        }else{
            return $this->mudadDataBuilder->build($entityAppData->data ?? "");
        }
        $entityAppData->update([
            'data' => json_encode($decodeEntityAppData),
        ]);
        return $this->mudadDataBuilder->build($entityAppData->data ?? "");
    }
    /**
     * @throws FileNotFoundException
     * @throws \Exception
     */
    public function exportPayrunMudadFile($payrunId): array
    {
        $entityAppData= $this->getPayrunAppEntityData($payrunId , true);
        $mudadData = $this->mudadDataBuilder->build($entityAppData->data ?? "");
        $mudadData->setPayRunId($payrunId);

        $mudadFileData = new MudadFileData();
        $mudadComponents = Settings::getValue(PluginUtil::MUDAD_PLUGIN, \App\Helpers\Settings::MUDAD_SALARY_COMPONENTS);
        $mudadComponents = json_decode($mudadComponents);
        $mudadFileData->setPrimaryComponents($mudadComponents);

        $decodeEntityAppData = json_decode($entityAppData->data ?? "", true);
        $mudadFileParser = new MudadFileParser($decodeEntityAppData['file_id']);
        $mudadFileData = $mudadFileParser->mapEmployeesData($mudadData, $mudadFileData);

        if (!count($mudadFileData->getEmployeesRows())){
            //what should we do if now mapping rows ??
        }

        $fileService = resolve(FileService::class);
        $file = $fileService->repo->find($decodeEntityAppData['file_id']);

        $pipeline = new MudadRowsMappingDataPipeline();
        $reviewData = $pipeline->process($mudadData, $mudadFileData);
        $updatedSheet = $mudadFileParser->exportMudadFile($file , $reviewData->getRows());

        if (!isset($decodeEntityAppData['file_finalized'])){
            $decodeEntityAppData['file_finalized'] = true;
            $entityAppData->update(['data' => json_encode($decodeEntityAppData)]);
        }

        return [
            'updatedSheet' => $updatedSheet,
            'fileName' => $decodeEntityAppData['file_meta']['file_name'] ?? 'mudad_file.xlsx'
        ];
    }

    /**
     * @throws \Exception
     */
    private function getPayrunAppEntityData($payrunId , $needsModel = false )
    {
        $entityAppData = $this->entityAppDataRepo->getDataByAppAndEntity(
            EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
            self::MUDAD_FILE_PROCESS,
            EntityKeyTypesUtil::PAY_RUN,
            $payrunId
        );
        $decodeEntityAppData = json_decode($entityAppData->data ?? "", true);

        if (empty($decodeEntityAppData) || empty($decodeEntityAppData['file_id'])) {
            throw new \Exception(sprintf(__t('%s Not Found'), __t('Payrun')));
        }
        if ($needsModel) return $entityAppData;
        return $decodeEntityAppData;
    }


}
