<?php

namespace App\Services;

use App\Exceptions\Import\ImportErrorException;
use App\Facades\Settings;
use App\Helpers\AutoNumber;
use App\Helpers\Import\InvoiceMapper;
use App\Repositories\ClientRepository;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\LimitCriteria;
use App\Repositories\InvoiceRepository;
use App\Repositories\EntityRepository;
use App\Repositories\SitePaymentGatewayRepository;
use App\Repositories\ProductRepository;
use App\Repositories\ShippingOptionRepository;
use App\Repositories\StoreRepository;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use App\Repositories\UnitTemplateRepository;
use App\Utils\ProductTypeUtil;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use App\Cache\ImportCache;
use App\Repositories\Criteria\OrderByCriteria;

/**
 * PriceListService Class Price list service class
 * @package App\Services
 * <AUTHOR> Mohammed <<EMAIL>>
 */
class InvoiceService extends BaseService
{
    /**
     * @var $filters array
     */
    var $filters = [];

    /**
     * @var InvoiceRepository
     */
    var $repo;

    /**
     * @var bool $importUsingJsonFile
     */
    public bool $importUsingJsonFile = true;


    /**
     * @var MappingService $importService
     */
    private $importService;

    public function __construct(
        InvoiceRepository $repo,
        EntityRepository $entityRepo = null,
        MappingService $importService
    )
    {
        parent::__construct($repo, $entityRepo);
        $this->importService = $importService;
    }

    public function getImportDownloadSample()
    {

        return   ['path' => '/samples/invoice-sample.csv', 'file_name' => __t('Invoice Sample') . '.csv'];
    }

    public function invoicesOptionsFormatter(Collection $invoices, $withItems, $productTypes) {
        $templates = [];
        /** @var UnitTemplateRepository */
        $templateRepository = resolve(UnitTemplateRepository::class);
        foreach ($templateRepository->all() as $template) {
            $templates[$template->id] = $template->factors;
            $templates[$template->id]->prepend([
                "id" => 0,
                "factor_name" => $template["main_unit_name"] ?? '',
                "small_name" => $template["unit_small_name"] ?? '',
                "factor" => 1,
                "unit_template_id" => $template['id'],
            ]);
        }
        $processedResults = [];
        $eagerLoadRelations = ['invoiceItems', 'invoiceItems.product', 'invoiceItems'];
        if(ifPluginActive(PluginUtil::MANUFACTURING_PLUGIN)){
            $eagerLoadRelations[] = 'invoiceItems.product.boms';
        }
        $invoices->load($eagerLoadRelations);
        foreach ($invoices as $k => $v) {
            $date = format_date($v->date);
            $row = ['text' => "#{$v->no} - {$date}", 'id' => $v->id];
            if($withItems) {
                //map invoice items
                foreach ($v->invoiceItems as $item) {
                    $product = $item->product;
                    if(empty($product)) {
                        continue;
                    }
                    if(!empty($productTypes) && !in_array($product->type, $productTypes)) {
                        continue;
                    }
                    $unit_factor = 1;
                    if($item->unit_factor) {
                        $unit_factor = $item->unit_factor;
                    }
                    $row['invoice_items'][]= [
                        'id' => $item->id,
                        'item' => $item->item,
                        'product_code' => $item->product->product_code,
                        'product_id' => $item->product_id,
                        'unit_factor_id' => $item->unit_factor_id,
                        'quantity' => $item->quantity,
                        'factor' => $unit_factor,
                        'bom' => $product->boms,
                        'units' => $templates[$product->unit_template_id]??false,
                    ];
                }
            }
            $processedResults['results'][$k] = $row;
        }
        return $processedResults;
    }

    public function getDefaultOptions($type, $withItems, $limit = 50) {
        $invoices = $this->repo->pushCriteria(new EqualCriteria('type', $type))
                               ->pushCriteria(new LimitCriteria($limit))
                               ->pushCriteria(new OrderByCriteria('id', 'desc'))
                               ->all();
        return $this->invoicesOptionsFormatter($invoices, $withItems, [ProductTypeUtil::PRODUCT]);
    }

    public function beforeInsert($insertedData, $extraData)
    {
       return InvoiceMapper::map($insertedData, $extraData);
    }


    public function preProcessDataForImportUsingExtraData($data, $extraData)
    {
        if (!empty($data['client_id'])) {
            $oldValue = $data['client_id'];
            if (array_key_exists($oldValue, ImportCache::$cacheData['clients'])) {
                $data['client_id'] = ImportCache::$cacheData['clients'][$data['client_id']];
            } else {
                $clientRepo = resolve(ClientRepository::class);
                $client = $clientRepo->getAutoCompleteResult($data['client_id']);
                if ($client->count()) {
                    $client = $client->first();
                    $data['client_id'] = $client->id;
                } else {
                    $data['client_id']  = $clientRepo->insertGetId([
                        'is_offline' => 1,
                        'client_number' => AutoNumber::get_auto_serial(AutoNumber::TYPE_CLIENT),
                        'business_name' => $data['client_id'],
                        'staff_id' => getAuthStaff('id'),
                        'site_id' => getCurrentSite('id'),
                        "branch_id" => getCurrentBranchId(),
                        "type" => Settings::getValue(PluginUtil::ClientsPlugin, 'client_type') == 2 ? 2 : 3,
                        'created' => Carbon::now(),
                        'modified' => Carbon::now(),
                    ]);
                    AutoNumber::update_auto_serial(AutoNumber::TYPE_CLIENT);
                }
                ImportCache::$cacheData['clients'][$oldValue] = $data['client_id'];
            }

        }

        if (array_key_exists('item', $data)) {
            $oldValue = $data['item'];
            if (!array_key_exists($oldValue, ImportCache::$cacheData['items'])) {
                $productRepo = resolve(ProductRepository::class);
                $product = $productRepo->getAutoCompleteProduct($data["item"]);
                if (empty($product)) {
                    $product = $productRepo->createWithName($data["item"]);
                }
                ImportCache::$cacheData['items'][$oldValue] = $product;
            }

            if (!array_key_exists($oldValue, ImportCache::$cacheData['product_item_barcodes'])) {
                $productRepo = resolve(ProductRepository::class);
                $productItemBarCode = $productRepo->getAutoCompleteCode($data["item"]);
                if (!empty($productItemBarCode)) {
                    ImportCache::$cacheData['product_item_barcodes'][$oldValue] = $productItemBarCode;
                  //  ImportCache::$cacheData['items'][$oldValue] = $productItemBarCode->product;
                }
            }
        }

        if (!empty($data['shipping_option_id'])) {
            $oldValue = $data['shipping_option_id'];
            if  (array_key_exists($oldValue, ImportCache::$cacheData['shipping_options'])) {
                $data['shipping_option_id'] = ImportCache::$cacheData['shipping_options'][$oldValue];
            } else {
                $shippingRepo = resolve(ShippingOptionRepository::class);
                $shipping = $shippingRepo->getAutoCompleteResult($data['shipping_option_id']);
                if ($shipping->count()) {
                    $data['shipping_option_id'] = $shipping->first()->id;
                } else {
                    $data['shipping_option_id'] = $shippingRepo->insertGetId([
                        'fees' => 0,
                        "status" => 1,
                        'name' => $data['shipping_option_id'],
                        'created' => Carbon::now(),
                        'modified' => Carbon::now(),
                    ]);
                }
                ImportCache::$cacheData['shipping_options'][$oldValue] = $data['shipping_option_id'];
            }
        }

        if (!empty($data['payment_method'])) {
            $paymentRepo = resolve(SitePaymentGatewayRepository::class);

            $payments = [];
            foreach (explode(',', $data['payment_method']) as $paymentMethod) {
                $paymentMethod = trim($paymentMethod);
                $paymentMethod = rtrim($paymentMethod);
                $oldValue = $paymentMethod;
                if (array_key_exists($oldValue, ImportCache::$cacheData['payment_methods'])) {
                    $payments[] = ImportCache::$cacheData['payment_methods'][$oldValue];
                } else {
                    $payment = $paymentRepo->getAutoCompleteResult($paymentMethod);
                    if (empty($payment)) {
                        $paymentGateWay = str_replace(' ', '_', strtolower($paymentMethod));
                        $payment = $paymentRepo->add([
                            "active" => 1,
                            'payment_gateway' => $paymentGateWay,
                            "label" => $paymentMethod,
                            "manually_added" => 1,
                            'site_id' => 0,
                            "branch_id" => getCurrentBranchId(),
                            "category" => "MANUALLY_ADDED",
                            'created' => Carbon::now(),
                            'modified' => Carbon::now(),
                        ]);
                    }
                    $payments[] = $payment->payment_gateway;
                    ImportCache::$cacheData['payment_methods'][$oldValue] = $payment->payment_gateway;
                }

            }
            $data['payment_method'] = implode(',', $payments);
        }
        return $data;
    }


    public function validateAllItems($data, $extra_data, $fields)
    {
        $visitedInvoice = [];
        $ignoreErrors = false;
        if (!empty($extra_data['ignore_errors'])) {
            $ignoreErrors = true;
        }

        if (count($data) > 10000) {
            throw new ImportErrorException([__t("The maximum number of records in the sheet is 10000")]);
        }

        $invalidData= [];
        $mappedData = [];

        foreach ($data as  $item) {
            $mappedData[$item["no"]][] = $item;
        }

        $mustEqual = ["date", "issue_date", "client_id", "shipping_option_id", "shipping_amount", "discount_amount", "discount", "adjustment_label", "adjustment_value", "payment_method", "amount", "currency_code"];


        $first_row = 1;
        if (!$extra_data["import_first_row"]) {
            $first_row++;
        }

        $row = 0;
        foreach ($mappedData as $key =>  $invoice) {
            $values = [];
            if (count($invoice) > 500) {
                $invalidData[($row + $first_row - 1)] = sprintf(__t("The number of items lines you can import in each Invoice is 500 at row #"), ($row + $first_row -1)) ;
                if (!$ignoreErrors) {
                    throw new ImportErrorException($invalidData);
                }
                $row++;
                continue;
            }
            foreach ($invoice as $item) {
                foreach ($mustEqual as $field) {
                    if (!isset($values[$field])) {
                        $values[$field] = $item[$field];
                    } else if (trim($values[$field]) !== trim($item[$field]) && !isset($visitedInvoice[$key])) {
                        $invalidData[($row + $first_row - 1)] =  sprintf(__t("The invoice %s should be the same for all invoice lines that share the same invoice code at row #%d"), __t($this->getFieldLabel($field, $fields)), ($row + $first_row - 1));
                        $visitedInvoice[$key] = 1;
                        if (!$ignoreErrors) {
                            throw new ImportErrorException($invalidData);
                        }
                    }
                }
                $row++;
            }
        }
        return $invalidData;
    }


    public function getCorrectIgnoredIndex($ignoredData, $insertedData)
    {
        $mappings = [];
        foreach ($ignoredData as $rowIndex => $ignoredValue) {
            foreach ($insertedData as  $index => $insertedDataItem) {

                if ($rowIndex <= ($insertedDataItem["Invoice"]["number_row"] + count($insertedData[$index]["InvoiceItem"]) - 1) && !isset($mappings[$rowIndex])) {
                    $mappings[$rowIndex] = $index;
                }
            }
        }

        $ret = [];
        foreach ($mappings as $rowIndex => $ignoredValue) {
            $ret[$ignoredValue] = $ignoredData[$rowIndex];
        }
        return $ret;
    }

    private function getFieldLabel($field, $fields): string
    {
        foreach ($fields as $item) {
            if ($field === $item->db_name) {
                return $item->label;
            }
        }

        return $field;
    }



    public function getImportFieldsLabel()
    {
        $storeRepo = resolve(StoreRepository::class);
        $stores = $storeRepo->getStoresUsersCanUpdate();
        $ret = [];
        $importedFields = $this->importService->getImportEntityFields("invoice");
       foreach ($importedFields as $field) {

           if ($field instanceof \App\Adapters\EntityFieldAdapterCustomField) {
               continue;
           }

           if ($field->key == "invoice_items.store_id" && count($stores) <= 1) {
               continue;
           }

           if (in_array($field->key, ["invoices.discount", "invoices.discount_amount"])) {
               $field->validation_rules = ["nullable", "positive"];
           }

           if ($field->key == "invoice_items.discount") {
                $obj = json_decode(json_encode($field));
               $obj->label = "Discount Item Percentage";
               $field->db_name = "discount_item_amount";
               $obj->key = "invoice_items.discount_percentage";
               $obj->validation_rules = $field->validation_rules = ["nullable", "positive"];
               $obj->allowed_values = $field->allowed_values = [];
               $obj->id = -1;
               $obj->db_name = "discount_item_percentage";
               $obj->relation = null;
               $ret[$obj->id] =  $obj;
           }

           if ($field->key == "invoice_items.quantity" && !Settings::getValue(PluginUtil::InventoryPlugin, 'enable_requisitions')) {
               $obj = json_decode(json_encode($field));
               $obj->label = "Tracking Data";
               $obj->id = -2;
               $obj->key = "invoice_items.tracking_data";
               $obj->field_type = "text";
               $obj->db_name = "tracking_data";
               $obj->validation_rules =  ["nullable"];
               $obj->relation = null;
               $ret[$obj->id] =  $obj;
           }

           if (in_array($field->key, ["invoice_payments.payment_method"])) {
               $field->validation_rules = ["nullable"];
           }
           $ret[$field->id]= $field;
       }
        return $ret;
    }

    public function getJsonImportData($request): array
    {
        return ['Invoice' => $request->get('Invoice'), 'InvoiceItem' => $request->get('InvoiceItem'), 'Payment' => $request->get('Payment')];
    }
}
