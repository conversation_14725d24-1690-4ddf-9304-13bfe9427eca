<?php

namespace App\Services;

use App\Facades\Branch;
use App\Facades\Settings;
use App\Modules\ElectronicInvoice\Repo\SystemETATaxRepository;
use App\Modules\ElectronicInvoice\Repo\SystemETAUnitRepository;
use App\Modules\ElectronicInvoice\Utils\ETAUnitsUtil;
use App\Modules\ElectronicInvoice\Utils\TaxesAndSubUtil;
use App\Modules\KSAElectronicInvoice\Services\ZatcaFileService;
use App\Notifications\ContractWillExpireNotification;
use App\Repositories\BranchRepository;
use App\Repositories\JobRepository;
use App\Repositories\TaxRepository;
use App\Repositories\UnitTemplateRepository;
use App\Requests\DefaultRequest;
use App\Utils\AllowedClientsToAttendUtil;
use App\Utils\PluginUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use App\Facades\Plugins;
use Illuminate\Support\Str;
use Ramsey\Uuid\Uuid;

class SettingService
{
    public function __construct(
        JobRepository $jobRepository, TaxRepository $taxRepository, SystemETATaxRepository $systemETATaxRepository,
        SystemETAUnitRepository $systemETAUnitRepository, UnitTemplateRepository $unitTemplateRepository, BranchRepository $branchRepository
    )
    {
        $this->jobRepo = $jobRepository;
        $this->taxRepository = $taxRepository;
        $this->systemETATaxRepository = $systemETATaxRepository;
        $this->systemETAUnitRepository = $systemETAUnitRepository;
        $this->unitTemplateRepository = $unitTemplateRepository;
        $this->branchRepository = $branchRepository;
    }

    public function saveAttendanceSettings(DefaultRequest $request)
    {
        $secondaryShiftValue = (isset($request['secondary_shift']) && $request['secondary_shift']) ? 1 : 0;
        $directManagersCanApprove = (isset($request['direct_managers_can_approve']) && $request['direct_managers_can_approve']) ? 1 : 0;
        $assignedManagersCanApprove = (isset($request['assigned_department_managers_can_approve']) && $request['assigned_department_managers_can_approve']) ? 1 : 0;
        $employeesCanApprove = (isset($request['employees_can_approve']) && $request['employees_can_approve']) ? 1 : 0;
        $employeesIds="";
        if($employeesCanApprove && isset($request['employees_that_can_approve_ids'])){
            $employeesIds = implode(",", $request['employees_that_can_approve_ids']);
        }
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_day', $request['fiscal_date_day'], false);
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_month', $request['fiscal_date_month'], false);
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'allow_entering_leave_applications_in_past_dates',  $request['allow_entering_leave_applications_in_past_dates'], false);
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift',  $secondaryShiftValue, false);
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'direct_managers_can_approve',  $directManagersCanApprove, false);
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'assigned_department_managers_can_approve',  $assignedManagersCanApprove, false);
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_can_approve',  $employeesCanApprove, false);
        Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_that_can_approve_ids',  $employeesIds);
    }

    public function saveElectronicInvoiceSettings(DefaultRequest $request)
    {

        Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_id', $request['client_id'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_secret', $request['client_secret'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_id_receipt', $request['client_id_receipt'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_secret_receipt', $request['client_secret_receipt'], false);
        if (!empty($request['is_testing'])) {
            Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_id_test', $request['client_id_test'], false);
            Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_secret_test', $request['client_secret_test'], false);
        }
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'activity_type', $request['activity_type'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'item_code_tax', $request['item_code_tax'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'item_code_type', $request['item_code_type'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'is_testing', $request['is_testing'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'use_signature', $request['use_signature'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'invoice_line_coding_type', $request['invoice_line_coding_type'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'gpc_value', $request['gpc_value'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'certificate_name', $request['certificate_name'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'invoice_purchase_order_reference', $request['invoice_purchase_order_reference'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'invoice_purchase_order_description', $request['invoice_purchase_order_description'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'invoice_sales_order_reference', $request['invoice_sales_order_reference'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'invoice_sales_order_description', $request['invoice_sales_order_description'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'proforma_invoice_number', $request['proforma_invoice_number'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'pos_serial_number', $request['pos_serial_number'], false);
        ////////////
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'use_invoice', $request['use_invoice'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'use_receipt', $request['use_receipt'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_id_test_receipt', $request['client_id_test_receipt'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'client_secret_test_receipt', $request['client_secret_test_receipt'], false);
        Settings::setValue(PluginUtil::ETA_PLUGIN, 'is_testing_receipt', $request['is_testing_receipt'], false);
        if (!empty($request['use_signature']) && !empty($request['dongle_pin'])) {
            Settings::setValue(PluginUtil::ETA_PLUGIN, 'dongle_pin', $request['dongle_pin'], false);
        }
        $data = ['taxes' => [], 'templates' => []];
        foreach ($request['taxes'] ?? [] as $tax) {
            $data['taxes'][] = ['system_tax' => $tax['system_tax'], 'eta_tax_type' => $tax['eta_tax_type'], 'eta_tax_sub' => $tax['eta_tax_sub']];
        }

        foreach ($request['templates'] ?? [] as $template) {
            $data['templates'][] = ['template' => $template['template'], 'system_unit' => $template['units'], 'eta_unit' => $template['eta_unit']];
        }


        $this->systemETATaxRepository->truncate();
        $this->systemETATaxRepository->bulkInsert($data['taxes']);

        if (Plugins::pluginActive(PluginUtil::InventoryPlugin) && Settings::getValue(PluginUtil::InventoryPlugin, 'enable_multi_units')) {
            $this->systemETAUnitRepository->truncate();
            $this->systemETAUnitRepository->bulkInsert($data['templates']);
        }

    }

    public function revokeKsaSettings()
    {

        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_otp', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_csr', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'zatca_env', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_requestID', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_binarySecurityToken', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_cert',null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_secret', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_requestID', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_binarySecurityToken', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_cert',null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_secret', null);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_in_progress', 0);
    }

    public function getFiscalDate()
    {
        return ['fiscal_date_day' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_day'), 'fiscal_date_month' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'fiscal_date_month')];
    }

    public function getAttendanceSettings()
    {
        $this->addIfNotCreated(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'direct_managers_can_approve' , 1);
        $this->addIfNotCreated(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'assigned_department_managers_can_approve' , 1);
        return $this->getFiscalDate() + [
            'secondary_shift' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift'),
            'allow_entering_leave_applications_in_past_dates' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'allow_entering_leave_applications_in_past_dates'),
            'direct_managers_can_approve' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'direct_managers_can_approve'),
            'assigned_department_managers_can_approve' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'assigned_department_managers_can_approve'),
            'employees_can_approve' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_can_approve'),
            'employees_that_can_approve_ids' => Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'employees_that_can_approve_ids'),
        ];
    }


    public function saveKSAElectronicSetting( $defaultRequest)
    {
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_common_name', $defaultRequest['csr_common_name'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_serial_number', "1-DAFTRA|2-".$defaultRequest['csr_common_name']."|3-".getCurrentSite("id")."_".Str::uuid()->toString(), false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_organization_identifier', $defaultRequest['csr_organization_identifier'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_organization_unit_name', $defaultRequest['csr_organization_unit_name'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_organization_name', $defaultRequest['csr_organization_name'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'branch_cr', $defaultRequest['branch_cr'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_country_name', $defaultRequest['csr_country_name'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_location_address', $defaultRequest['csr_location_address'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_otp', $defaultRequest['csr_otp'], false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'csr_industry_business_category', $defaultRequest['csr_industry_business_category'], false);
    }

    public function getKSAElectroingSettings()
    {
        return [
            'csr_common_name' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_common_name', null, false) ?? getCurrentSite('business_name'),
            'csr_serial_number' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_serial_number', null, false),
            'csr_organization_identifier' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_organization_identifier', null, false) ??  getCurrentSite('bn1'),
            'csr_organization_unit_name' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_organization_unit_name', null, false) ?? (Plugins::pluginActive(PluginUtil::BranchesPlugin) ? Branch::getCurrentBranch()['name']: ''),
            'csr_organization_name' =>  Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_organization_name', null, false)  ??  getCurrentSite('business_name'),
            'csr_country_name' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_country_name', null, false) ?? getCurrentSite('country_code'),
            'csr_location_address' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_location_address', null, false) ?? getCurrentSite('address1'),
            'csr_industry_business_category' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_industry_business_category', null, false) ?? getCurrentSite("Industry"),
            'csr_otp' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_otp', null, false),
            'branch_cr' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'branch_cr', null, false),
            'have_csr' => !empty(Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_binarySecurityToken', null, false)),
            'env' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'zatca_env', null, false),
            'csr_in_progress' => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'csr_in_progress', null, false),
            'send_automatic' =>  Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'send_automatic_invoices', null, false),
            "multipleBranches" => Plugins::pluginActive(PluginUtil::BranchesPlugin) && count($this->branchRepository->getActiveBranchesList('')) >= 2,
            "use_branch_name" => Settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'use_branch_name', null, false),
        ];
    }


    public function saveGeneralSettingsForEInvoice($request)
    {
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'send_automatic_invoices', $request['save_automatic'] ?? 0, false);
        Settings::setValue(PluginUtil::EINVOICE_PLUGIN, 'use_branch_name', $request['use_branch_name'] ?? 0, false);
    }

    public function getElectronicInvoicesSettings()
    {
        $strJsonFileContents = file_get_contents(Config::get('view.paths')[0].DIRECTORY_SEPARATOR.'electronic_invoice'.DIRECTORY_SEPARATOR.'ActivityCodes.json');
        return [

            'system_taxes_data' => $this->systemETATaxRepository->All(),
            'system_taxes' => $this->taxRepository->list()->toArray(),
            'system_units_data' => $this->systemETAUnitRepository->All(),
            'system_templates' => $this->unitTemplateRepository->getTemplatesWithUnits(),
            'eta_units' => json_decode(ETAUnitsUtil::OPTIONS, true),
            'tax_types' => array_combine(array_keys(TaxesAndSubUtil::TAXES), array_map(function ($item) {
                return __t($item, true);
            }, array_column(TaxesAndSubUtil::TAXES, 'title'))),
            'taxes_data' => array_map(function ($item) {
                $item['sub'] = array_map(function ($sub) {

                    $sub['title'] = __t($sub['title'], true);
                    return $sub;
                }, $item['sub']);
                return $item;
            }, TaxesAndSubUtil::TAXES),
            'activityTypes' =>  json_decode($strJsonFileContents,true),
            'client_id' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id'),
            'client_secret' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret'),
            'client_id_receipt' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id_receipt'),
            'client_secret_receipt' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret_receipt'),
            'client_id_test' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id_test'),
            'client_secret_test' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret_test'),
            'activity_type' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'activity_type'),
            'item_code_tax' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'item_code_tax'),
            'item_code_type' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'item_code_type'),
            'is_testing' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'is_testing'),
            'invoice_line_coding_type' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'invoice_line_coding_type'),
            'gpc_value' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'gpc_value'),
            'use_signature' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'use_signature'),
            'certificate_name' =>  Settings::getValue(PluginUtil::ETA_PLUGIN, 'certificate_name'),
            'pos_serial_number' =>  Settings::getValue(PluginUtil::ETA_PLUGIN, 'pos_serial_number'),
            'invoice_purchase_order_reference' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'invoice_purchase_order_reference'),
            'invoice_purchase_order_description' =>  Settings::getValue(PluginUtil::ETA_PLUGIN, 'invoice_purchase_order_description'),
            'invoice_sales_order_reference' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'invoice_sales_order_reference'),
            'invoice_sales_order_description' =>  Settings::getValue(PluginUtil::ETA_PLUGIN, 'invoice_sales_order_description'),
            'proforma_invoice_number' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'proforma_invoice_number'),
            'use_invoice' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'use_invoice'),
            'use_receipt' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'use_receipt'),
            'client_id_test_receipt' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_id_test_receipt'),
            'client_secret_test_receipt' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'client_secret_test_receipt'),
            'is_testing_receipt' => Settings::getValue(PluginUtil::ETA_PLUGIN, 'is_testing_receipt'),
            'file' => [
                'path' =>  Storage::temporaryUrl(
                    'desktop/Daftra_Digital_Signature.exe', Carbon::now()->addMinutes(env('AWS_LIFE_TIME', 12960000)),
                    ['ResponseContentDisposition' => 'attachment; filename="Daftra Digital Signature.exe"']
                ),
                'name' => (Domain_Name_Only == 'enerpize'?__t('Enerpize Digital Signature'):__t('Daftra Digital Signature')),
                'size' => '55,90 KB'
            ],
        ];
    }

    /**
     * save allowed client to attend options
     * @param DefaultRequest $request
     */
    public function saveAllowedClientToAttendOption(DefaultRequest $request)
    {
        Settings::setValue(PluginUtil::CLIENT_ATTENDANCE_PLUGIN, 'allowed_clients_to_attend_option', $request['client'], false);
    }

    /**
     * get allowed clients to attend option
     * @return string
     */
    public function getAllowedClientsToAttendOption() : string
    {
        $clientOption = Settings::getValue(PluginUtil::CLIENT_ATTENDANCE_PLUGIN, 'allowed_clients_to_attend_option');
        return $clientOption ?? AllowedClientsToAttendUtil::ALL_CLIENTS;
    }

    /**
     * @param DefaultRequest $request
     */
    public function saveToleranceDays(DefaultRequest $request)
    {
        Settings::setValue(PluginUtil::MEMBERSHIP_PLUGIN, 'renewal_tolerance_days', $request['renewal_tolerance_days'], false);
    }

    /**
     * get tolerance days
     * @return int|mixed
     */
    public function getToleranceDays()
    {
        $days = Settings::getValue(PluginUtil::MEMBERSHIP_PLUGIN, 'renewal_tolerance_days');
        if (is_null($days)) {
            $days = 7;
        }
        return $days;
    }

    public function getGenerateDraftInvoicesForSubscriptions(){
        return $this->get(PluginUtil::MEMBERSHIP_PLUGIN, "generate_draft_invoices_for_memberships_subscriptions");
    }

    public function saveGenerateDraftInvoicesForSubscriptions(DefaultRequest $request){
        $this->add(PluginUtil::MEMBERSHIP_PLUGIN, "generate_draft_invoices_for_memberships_subscriptions",$request['generate_draft_invoices_for_memberships_subscriptions']);
    }

    public function saveContractExpiryNotificationDuration(DefaultRequest $request){
        $oldValue = Settings::getValue(PluginUtil::HRM_PAYROLL_PLUGIN,'contract_expiry_notification_duration');
        if($oldValue){
           $diff = $oldValue - $request['duration'];
           $this->jobRepo->updateNotificationJobsAvailableAt($diff,ContractWillExpireNotification::class);
        }
        Settings::setValue(PluginUtil::HRM_PAYROLL_PLUGIN, 'contract_expiry_notification_duration', $request['duration']);
    }

    public function getContractExpiryNotificationDuration(){

        return ['duration' => Settings::getValue(PluginUtil::HRM_PAYROLL_PLUGIN, 'contract_expiry_notification_duration')];

    }

    /**
     * @param int $pluginID
     * @param string $key
     * @param $value
     * @param bool $allowCache
     * @param bool $applyBranches
     */
    public function add(int $pluginID, string $key, $value, bool $allowCache = true, bool $applyBranches = true)
    {
        Settings::setValue($pluginID, $key, $value, $allowCache, $applyBranches);
    }

    /**
     * @param int $pluginID
     * @param string $key
     * @param null $st
     * @param bool $allowCache
     * @param bool $applyBranches
     * @return mixed
     */
    public function get(int $pluginID, string $key, $st = null, bool $allowCache = true, bool $applyBranches = true)
    {
        return Settings::getValue($pluginID, $key, $st, $allowCache, $applyBranches);
    }

    public function addIfNotCreated($pluginId , $key, $value , $allowCache = false)
    {
        $setting = Settings::getValue($pluginId, $key );
        if($setting === null){
            $setting =  Settings::setValue($pluginId, $key, $value , $allowCache);
        }
        return  $setting;
    }

    public function getManufacturingOrderStatusSettings()
    {
        return [
            'manufacturing_order_manual_status' => Settings::getValue(PluginUtil::MANUFACTURING_PLUGIN, 'manufacturing_order_manual_status'),
        ];
    }

    public function getManufacturingGeneralSttings()
    {
        return [
            'exceeding_the_requested_quantity_in_manufacturing_order' => Settings::getValue(PluginUtil::MANUFACTURING_PLUGIN, 'exceeding_the_requested_quantity_in_manufacturing_order'),
            'add_items_to_the_production_plan_that_arent_in_the_sales_order_or_invoice' => Settings::getValue(PluginUtil::MANUFACTURING_PLUGIN, 'add_items_to_the_production_plan_that_arent_in_the_sales_order_or_invoice'),
            'production_plan_can_have_more_quantities_than_a_sales_order_or_invoice' => Settings::getValue(PluginUtil::MANUFACTURING_PLUGIN, 'production_plan_can_have_more_quantities_than_a_sales_order_or_invoice'),
        ];
    }

    public function getBookingGeneralSttings()
    {
        return [
            'booking_service_setting' => Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'booking_service_setting'),
            'default_booking_service' => Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'default_booking_service'),
            'service_employee_setting' => Settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'service_employee_setting')
        ];
    }

    public function saveBookingGeneralSttings($request)
    {
        Settings::setValue(PluginUtil::NEW_BOOKING_PLUGIN, 'booking_service_setting', $request['booking_service_setting'], false);
        Settings::setValue(PluginUtil::NEW_BOOKING_PLUGIN, 'default_booking_service', $request['default_booking_service'], false);
        Settings::setValue(PluginUtil::NEW_BOOKING_PLUGIN, 'service_employee_setting', $request['service_employee_setting'], false);
    }
}
