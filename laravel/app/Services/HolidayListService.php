<?php

namespace App\Services;

use App\Exceptions\HolidayListHasStaffException;
use App\Exceptions\HolidayListNotFoundException;
use App\Facades\Formatter;
use App\Helpers\FilterOperations;
use App\Repositories\Criteria\BetweenCriteria;
use App\Repositories\Criteria\Holidaylist\WithHolidayListNoDaysCriteria;
use App\Repositories\Criteria\LargerThanCriteria;
use App\Repositories\Criteria\LimitCriteria;
use App\Repositories\Criteria\OrderByCriteria;
use App\Repositories\Criteria\WhereHasCriteria;
use App\Repositories\EntityRepository;
use App\Repositories\HolidayListDayRepository;
use App\Repositories\HolidayListRepository;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Utils\ActionLineMainOperationTypesUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use Closure;

class HolidayListService extends BaseService
{
    var $filters = [];
    /**
     * @var HolidayListRepository $repo
     */
    var $repo;

    /**
     * @var string $showRouteName employee type show route name
     */
    protected $showRouteName = 'owner.holiday_lists.show';

    /** @var HolidayListDayRepository */
    protected $holidayListDayRepo;

    public function __construct(HolidayListRepository $repo, EntityRepository $entityRepo,ActivityLogService $activityLogService, HolidayListDayRepository $holidayListDayRepo)
    {
        $this->activityLogService = $activityLogService;
        parent::__construct($repo, $entityRepo);
        $this->holidayListDayRepo = $holidayListDayRepo;
    }

    public function getFormRelatedData()
    {
        return [];
    }

    function getFilters()
    {
        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            $field = 'name';
            $operation = FilterOperations::IN_STRING_FILTER_OPERATION;
            if (isset($this->parameters['name'])) {
                if (is_numeric($this->parameters['name'])) {
                    $field = 'id';
                    $operation = FilterOperations::EQUAL_FILTER_OPERATION;
                }
            }
            return $this->filters = [
                'name' => [
                    'simple' => true,
                    'before' => '<div class="form-group-icon  form-group">',
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'label' => false,
                    'div' => 'col-md-12',
                    'attributes' => ['placeholder' => __t('Search by') . ' ' . __t('List Name') . ' ' . __t('or') . ' ' . __t('ID') ],
                    "type" => "text",
                    "filter_options" => [
                        "operation" => $operation,
                        "field" => $field
                    ]
                ]
            ];
        }
    }

    /**
     * returns service sort fields
     * @return array
     */
    function getSortFields()
    {
        return [
            'fields' => [
                'name' => ['title' => __t('Name'), 'field' => 'name', 'direction' => 'DESC'],
                'created' => ['title' => __t('Date of Creation'), 'field' => 'created', 'direction' => 'ASC'],
                'days_count' => ['title' => sprintf(__t('%s Count'), __t('Days')), 'direction' => 'ASC','field' => 'days_count', 'relation'=> 'days'],
                'id' => [
                    'title' => __t('ID'),
                    'field' => 'id',
                    'hidden' => true,
                    'direction' => 'DESC'
                ],
            ],
            'active' => [
                'field' => 'id',
                'direction' => 'DESC',
            ]
        ];

    }

    function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $neededDataAfterSave = $this->commonOperationsBeforeSave($request);
        $request['can_work'] = isset($request['can_work']);
        $holidayList = parent::add($request, ['date', 'title'], $callback);

        if ($holidayList) {
            $this->commonOperationAfterSave($holidayList, $neededDataAfterSave);
        }
        return $holidayList;
    }

    function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $this->disableLogging();
        $neededDataAfterSave = $this->commonOperationsBeforeSave($request);
        $request['can_work'] = isset($request['can_work']);
        $oldList = $this->find($id);
        if(!$oldList){
            throw new HolidayListNotFoundException();
        }
        $currentDays = [];
        foreach ($oldList->days as $day) {
            $currentDays[$day->title] = Formatter::formatForView($day->date, 'date');
        }
        $oldList = $oldList->getAttributes();
        $oldList['holiday days'] = $currentDays;
        if ($updatedValue = parent::update($id, $request, array_keys($neededDataAfterSave), $callback)) {
            $holidayList = $this->repo->resetCriteria()->find($id);
            $this->commonOperationAfterSave($holidayList, $neededDataAfterSave);
            $holidayList = $this->repo->resetCriteria()->find($id);
            $currentDays = [];
            foreach ($holidayList->days as $day) {
                $currentDays[$day->title] = Formatter::formatForView($day->date, 'date');
            }
            $newData = $holidayList->getAttributes();
            $newData['holiday days'] = $currentDays;
            // log activity
            $activityLogRequest = new ActivityLogRequest(
                $holidayList->id,
                EntityKeyTypesUtil::HOLIDAY_LIST_ENTITY_KEY,
                ActionLineMainOperationTypesUtil::UPDATE_ACTION,
                $holidayList->name,
                route($this->showRouteName, ['holiday_list' => $holidayList->id]),
                $newData,
                $oldList
            );
            $this->activityLogService->addActivity($activityLogRequest);

            return $updatedValue;
        }
    }

    private function commonOperationsBeforeSave($request)
    {
        $newHolidayDays = [];
        //If from api
        if (isset($request['days'])) {
            foreach ($request['days'] as $day) {
                $title = $day['title'] ?? '';
                $date = $day['date'] ?? '';
                if ($title && $date) {
                    $newHolidayDays[] = ['title' => $title, 'date' => Formatter::formatForDB($date, 'date')];
                }
            }
        } else {
            $request['date'] = array_values($request['date']);
            $request['title'] = array_values($request['title']);
            for ($i = 0; $i < max(count($request['date']), count($request['title'])); $i++) {
                $title = $request['title'][$i] ?? '';
                $date = $request['date'][$i] ?? '';
                if ($title && $date) {
                    $newHolidayDays[] = ['title' => $title, 'date' => Formatter::formatForDB($date, 'date')];
                }
            }
        }
        return ['days' => $newHolidayDays];
    }

    private function commonOperationAfterSave($holidayList, $neededData)
    {
        $this->repo->addHolidayDays($holidayList, $neededData['days']);
    }

    public function delete($id, Closure $callback = null)
    {
        $holidayList = $this->find($id);
        if(count($holidayList->staff) > 0)
        {
            throw new HolidayListHasStaffException;
        }
        $this->repo->deleteHolidayDays($holidayList);
        $result = parent::delete($id);
        return $result;

    }

    /**
     * @param $employeeID
     * @param $date
     * @return string|null if there is a holidayListDay in this day return its title else return null
     */
    public function getAttendanceHolidayTitle($employeeID, $date)
    {
        return $this->repo->getAttendanceHolidayTitle($employeeID, $date);
    }

    public function getStaffAttendanceHolidayInRange($employeeId, $from, $to)
    {
        return $this->repo->getStaffHolidaysInRange($employeeId, $from, $to);
    }

    public function getStaffAttendanceHolidayInDate($employeeId, $date)
    {
        return $this->repo->getStaffHolidaysInDate($employeeId, $date);
    }

    public function processCalculatorHolidays($results): array
    {
        $holidaysArray = [];
        if ($results) {
            foreach ($results as $holiday) {
                $holidaysArray[$holiday->date] = $holiday->title;
            }
        }
        return $holidaysArray;
    }

    public function getViewData($id)
    {
        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('holiday_list');

        return array_merge(parent::getViewData($id), ['view_templates' => $viewTemplates]);
    }

    public function getHolidaysForStaff($staffId, $dateFrom = null, $dateTo = null){
        $this->holidayListDayRepo->pushCriteria(new WhereHasCriteria('holidayList.staff', 'staffs.id', '=', $staffId));
        $this->holidayListDayRepo->pushCriteria(new OrderByCriteria('date', 'asc'));
        $this->holidayListDayRepo->pushCriteria(new WithHolidayListNoDaysCriteria());
        if(!empty($dateFrom) || !empty($dateTo)){
            $this->holidayListDayRepo->pushCriteria(new BetweenCriteria(
                'date',
                !empty($dateFrom) ? $this->formatDateForDb($dateFrom) : null,
                !empty($dateTo) ? $this->formatDateForDb($dateTo) : null
            ));
        }else{
            $this->holidayListDayRepo->pushCriteria(new LargerThanCriteria('date', $this->formatDateForDb(date('Y-m-d H:i:s'))));
            $this->holidayListDayRepo->pushCriteria(new LimitCriteria(3));
        }

        return $this->holidayListDayRepo->all();
    }

    private function formatDateForDb($value){
        return Formatter::formatForDb($value, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
    }


    function updateHolidayListFromApi($request)
    {
        $dates = [];
        $titles = [];

        $holidayList = $this->repo->resetCriteria()->find($request['id']);
        foreach ($holidayList->days as $day) {
            $dates[] = Formatter::formatForView($day->date, 'date');
            $titles[] = $day->title;
        }
        foreach ($request['holidaysFromApi'] as $day) {
            if (!empty($day['public']) && $day['public']) {

                $date = Formatter::formatForView($day["date"], 'date');
                if (!in_array($date, $dates)) {//not update old date
                    $dates[] = $date;
                    $titles[] = $day["name"];
                }
            }
        }

        $request->__set('date', $dates);
        $request->__set('title', $titles);
        $this->update($request['id'], $request);
    }
}
