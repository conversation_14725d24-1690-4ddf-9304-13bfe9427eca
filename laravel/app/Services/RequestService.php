<?php

namespace App\Services;

use App\Exceptions\ActionNotAllowed;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\NoRequestTypeAddedException;
use App\Exceptions\RequestApprovedRejectedException;
use App\Exceptions\Requests\CanNotAddOrEditRequest;
use App\Exceptions\Requests\RequestCanNotBeEdit;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Exceptions\Requests\MultiCycleConfigurationAccessException;
use App\Facades\Branch;
use App\Facades\EntityPermission;
use App\Facades\Formatter;
use App\Helpers\FilterOperations;
use App\Modules\LocalEntity\Queue\Events\Request\RequestApproved;
use App\Modules\LocalEntity\Queue\Events\Request\RequestCreated;
use App\Modules\LocalEntity\Queue\Events\Request\RequestRejected;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use App\Modules\Template\Services\Email\DefaultEmailTemplate;
use App\Modules\Template\Services\TemplateService;
use App\Modules\Template\Utils\TemplateTypeUtil;
use App\Notifications\AddRequestNotification;
use App\Notifications\ApproveRequestNotification;
use App\Notifications\RejectRequestNotification;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\EntityRepository;
use App\Repositories\RequestRepository;
use App\Repositories\RequestTypeRepository;
use App\Repositories\StaffRepository;
use App\Requests\Attachments\SaveAttachmentRequest;
use App\Requests\DefaultRequest;
use App\Requests\Notification\NotificationDataRequest;
use App\Requests\Notification\NotificationRequest;
use App\Utils\ActiveStatusUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityPermissionKeyUtil;
use App\Utils\RequestActionUtil;
use App\Utils\Requests\RequestStatusUtil;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Collection;
use App\Services\StaffService;
use App\Facades\Plugins;
use App\Facades\Staff;
use App\Utils\PluginUtil;
use App\Helpers\DynamicPermission\DynamicPermissionHelper;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfiguration;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationMatcher;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalMatchRequest;
use App\Repositories\RoleRepository;
use App\Utils\EntityPermissionCriteriaUtil;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;
use Izam\Daftra\Common\Utils\EntityEmailPreferenceUserTypeUtil;
use Izam\Template\EmailSender\Utils\EmailTemplateReservedKeysUtil;
use Izam\Template\Utils\TemplateListingModeUtil;
use App\Models\Owner;
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationRematcher;
use App\Repositories\Criteria\RequestStatusCriteria;
use Izam\Entity\Forms\EntityCustomFilterFormElments\RequestFilterFormCustomElements;

class RequestService extends BaseService
{
    /**
     * @var RequestRepository
     */
    var $repo;

    /**
     * @var RequestTypeService
     */
    private $requestTypeService;

    /**
     * @var string $showRouteName pay run show route name
     */
    protected $showRouteName = 'owner.requests.show';

    /**
     * @var StaffRepository
     */
    private $staffRepository;
    /**
     * @var RequestTypeRepository
     */
    private $requestTypeRepository;

    /**
     * @var notificationService
     */
    private $notificationService;

    /**
     * @var array
     */
    private $filters;

    /**
     * @var EntityPermissionService
     */
    private $entityPermissionService;

    public function __construct(
        RequestRepository $repo,
        EntityRepository $entityRepo,
        StaffRepository $staffRepository,
        RequestTypeService $requestTypeService,
        NotificationService $notificationService,
        EntityPermissionService $entityPermissionService,
        RequestTypeRepository $requestTypeRepository,
        private AdditionalFieldsAwareService $additionalFieldsAwareService,
        protected MultiCycleApprovalConfigurationService $multiCycleApprovalConfigurationService
    ) {
        $this->staffRepository = $staffRepository;
        $this->notificationService = $notificationService;
        $this->entityPermissionService = $entityPermissionService;
        $this->requestTypeService = $requestTypeService;
        $this->requestTypeRepository = $requestTypeRepository;
        parent::__construct($repo, $entityRepo);
    }

    public function getFormData($id = false)
    {
        $data = parent::getFormData($id);
        $requestType = $this->requestTypeService->getRequestTypeBySlug(request()->request_type);
        if (old('staff_id') || $id || isStaff()) {
            if (isStaff()) {
                $staffId = getAuthOwner('staff_id');
            } else {
                $staffId = old('staff_id') ? old('staff_id') : $data['form_record']->staff_id;
            }
            $staff = $this->staffRepository->find($staffId);
            if (!$staff) {
                throw new EntityNotFoundException(__t("Employee"));
            }
            $data['staffOptions'] = [
                [
                    'text' => "#$staff->code $staff->name $staff->last_name ($staff->email_address)",
                    'value' => $staff->id,
                    'data' => [
                        'data-img' => $staff->image,
                        'selected' => true
                    ]
                ]
            ];
        }
        if ($id) {
            foreach ($data['form_record']->attachments as $attachment) {
                $file = $attachment;
                $data['files'][] = ['url' => $file->url, 'name' => $file->name, 'size' => formatFilesSize($file->file_size),'id' => $file->id];
            }

        }
        $data['request_type_id'] = $this->requestTypeService->getRequestTypeBySlug(request('request_type'))->id ?? null;

        $data['entityKey'] = 'le_request_type_' . $requestType->slug;
        $data['form'] = $this->additionalFieldsAwareService
            ->getFormData('le_request_type_' . $requestType->slug, $id, true);
        return $data;
    }

    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $requestType = $this->requestTypeService->getRequestTypeBySlug(request()->request_type);
        $additionalFieldsEntityKey = $this->additionalFieldsAwareService->getEntityKey('le_request_type_' . $requestType->slug);
        $customData = $request[$additionalFieldsEntityKey] ?? null;
        $this->additionalFieldsAwareService->validateAdditionalFieldsFormData(request()->all(), 'le_request_type_' . $requestType->slug);
        if (!$requestType) {
            throw new EntityNotFoundException(__t($this->requestTypeService->mainEntity->label) ?? "");
        }
        $this->authorizeAddAndEdit($requestType->id, $request->get("staff_id"));
        $request['application_date'] = Formatter::formatForDB($request['application_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['execution_date'] = Formatter::formatForDB($request['execution_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['request_type_id'] = $requestType->id;
        $attachments = [];
        if ($request->get('attachments')) {
            foreach ($request->get('attachments') as $k => $attachment) {
                $attachments[$k] = array_merge($attachment, isset($request->file('attachments')[$k]) ? $request->file('attachments')[$k] : []);
            }
        }
        $this->appendApprovalConfigurationsData($request);
        $additionalCallback = function ($result) use ($callback, $attachments) {
            if ($attachments) {
                $saveAttachmentsRequest = new SaveAttachmentRequest($result->id, $attachments, EntityKeyTypesUtil::REQUEST_ENTITY_KEY);
                $this->saveAttachments($saveAttachmentsRequest);
            }
            if ($callback) {
                $callback($result);
            }
            if ($result) {
                $this->launchRequestCreatedNotification($result);
            }
        };

        if ($customData) {
            $excludedFields[] = $additionalFieldsEntityKey;
        }
        $addedRequest = parent::add($request, array_merge(['attachments', '_token'], $excludedFields), $additionalCallback);
        if ($customData) {
            $request[$additionalFieldsEntityKey] = $customData;
        }
        $this->additionalFieldsAwareService->save($request->all(), $addedRequest->id, 'le_request_type_' . $requestType->slug);

        dispatch_event_action(new RequestCreated($addedRequest));

        return $addedRequest;
    }

    private function appendApprovalConfigurationsData(&$request){
        $multiCycleConfiguration = $this->getMatchedApprovalConfigurations($request);
        if($multiCycleConfiguration){
            $request['approval_data'] = json_encode([
                'current_level'=> 1,
                'approvers' => []
            ]);
            $request['approval_cycle_configuration_id'] = $multiCycleConfiguration->getId();
        }
    }

    private function getMatchedApprovalConfigurations($request){
        $multiCycleApprovalMatchRequest = new MultiCycleApprovalMatchRequest();
        $multiCycleApprovalMatchRequest
            ->setConfigurationType(MultiCycleApprovalUtil::CONFIG_TYPE_REQUEST)
            ->setRequestType($request['request_type_id'] );
        $staffService = resolve(StaffService::class);
        $applicant = $staffService->find($request['staff_id']);
        if($applicant->branch_id){
            $multiCycleApprovalMatchRequest->setBranchId($applicant->branch_id);
        }
        if($applicant->staff_info?->department_id){
            $multiCycleApprovalMatchRequest->setDepartmentId($applicant->staff_info?->department_id);
        }
        return (new MultiCycleApprovalConfigurationMatcher())->match($multiCycleApprovalMatchRequest);
    }

    public function removeRedundantNotifications($record){
        $notificationData = new NotificationDataRequest((int) $record->id);
        $employees = $this->entityPermissionService->getEmployeesForPermission(EntityPermissionKeyUtil::APPROVE_REJECT, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, $record->request_type_id);
        $notifiable = $this->staffRepository->findWhereIn('id', $employees);
        $notificationRequest = new NotificationRequest(
            AddRequestNotification::class,
            $notificationData,
            $notifiable
        );
        $this->notificationService->unNotify($notificationRequest);

    }
    private function launchRequestCreatedNotification($record)
    {
        $employees = [];
        if($record->approval_cycle_configuration_id){
            $approval_data = $record->getApprovalData();
            if($approval_data){
                $employees = $this->multiCycleApprovalConfigurationService->getLevelApprovers($record->approval_cycle_configuration_id, 1, $record->staff_id);
            }
        }else{
            $employees = $this->entityPermissionService->getEmployeesForPermission(EntityPermissionKeyUtil::APPROVE_REJECT, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, $record->request_type_id);
        }
        $notifiable = $this->staffRepository->findWhereIn('id', $employees);
        $notificationData = new NotificationDataRequest((int)$record->id);
        $notificationData->appendData('application_date', $record->application_date);
        $notificationData->appendData('staff_name', $record->staff->full_name ?? "");
        $notificationData->appendData('staff_id', $record->staff_id);
        $notificationData->appendData('request_type', $record->requestType->name ?? "");
        $notificationData->appendData('slug', $record->requestType->slug ?? "");
        $notificationData->appendData('id', $record->id);
        $notificationRequest = new NotificationRequest(
            AddRequestNotification::class,
            $notificationData,
            $notifiable
        );
        $this->notificationService->notify($notificationRequest);
    }

    /**
     * @param int $id
     * @return bool
     * @throws EntityNotFoundException
     * @throws RequestCanNotBeEdit
     */
    public function validateRequestIsEditable(int $id)
    {
        $request = $this->find($id);
        if (!$request) {
            throw new EntityNotFoundException(__t("Request"));
        }
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($request->staff_id,false)){
            throw new CanNotAddOrEditRequest(sprintf(__t('You do not have access to this %s',true),__t('Record',true)));
        }
        if ($request->status != RequestStatusUtil::PENDING) {
            throw new RequestCanNotBeEdit;
        }
        return true;
    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['staff_id'] = $record->staff ? $record->staff->name : '';
        return $data;
    }

    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $requestType = $this->requestTypeService->getRequestTypeBySlug(request()->request_type);
        if (!$requestType) {
            throw new EntityNotFoundException(__t($this->requestTypeService->mainEntity->label) ?? "");
        }
        $this->authorizeAddAndEdit($requestType->id, $request->get("staff_id"));
        $requestEntity = $this->find(request('request'));
        $this->additionalFieldsAwareService->validateAdditionalFieldsFormData(request()->all(), 'le_request_type_' . $requestType->slug );
        if (!$requestEntity) {
            throw new EntityNotFoundException(__t($this->mainEntity->label ?? ""));
        }
        if ($requestEntity->status != RequestStatusUtil::PENDING) {
            throw new RequestCanNotBeEdit;
        }
        $request['application_date'] = Formatter::formatForDB($request['application_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['execution_date'] = Formatter::formatForDB($request['execution_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['request_type_id'] = $requestType->id;
        $attachments = [];
        if ($request->get('attachments')) {
            foreach ($request->get('attachments') as $k => $attachment) {
                $attachments[$k] = array_merge($attachment, isset($request->file('attachments')[$k]) ? $request->file('attachments')[$k] : []);
            }
        }

        $additionalCallback = function ($result, $oldData) use ($callback, $attachments) {
            if ($attachments) {
                $this->updateAttachments($attachments, $oldData->id, EntityKeyTypesUtil::REQUEST_ENTITY_KEY);
            } else {
                $this->deleteAttachments($oldData->attachments);
            }

            if ($callback) {
                $callback($result);
            }

            if ($result) {
                $this->launchRequestCreatedNotification($result);
            }
        };

        $this->additionalFieldsAwareService->save($request->all(), request('request'), 'le_request_type_' . $requestType->slug, true);

        $additionalFieldsEntityKey = $this->additionalFieldsAwareService->getEntityKey('le_request_type_' . $requestType->slug);
        if ($request[$additionalFieldsEntityKey]) {
            $excludedFields[] = $additionalFieldsEntityKey;
        }
        return parent::update(request('request'), $request, array_merge($excludedFields, ['_token', '_method', 'id', 'attachments']), $additionalCallback);
    }

    function getRecordUpdatedActivityLogData($recordData, $oldRecordData)
    {
        $request = parent::getRecordUpdatedActivityLogData($recordData, $oldRecordData);
        if(empty($request)) return $request;
        $newData = $request->getNewData();
        $oldData = $request->getOldData();
        if(!empty($recordData['approval_data']) && is_array($recordData['approval_data'])){
            $recordData->setApprovalData($recordData['approval_data']);
        }
        $newData['status'] = $this->getRequestStatus($recordData);
        $oldData['status'] = $this->getRequestStatus($oldRecordData);
        $request->setNewData($newData);
        $request->setOldData($oldData);
        return $request;
    }

    /**
     * reject a request
     * @param $id
     * @param $request
     * @return mixed
     */
    public function reject($id, $request, $requestModel = null)
    {
        if(empty($requestModel)){
            $requestModel = $this->find($id);
        }
        if(isset($requestModel->approval_cycle_configuration_id)){
            $approval_data = $requestModel->getApprovalData();
            $request['approval_data'] = json_encode([
                'current_level'=> $approval_data->current_level,
                'rejected_by' => getAuthOwner('staff_id'),
                'approvers' => $approval_data->approvers,
            ]);
        }
        $request['rejection_date'] = Formatter::formatForDB($request['rejection_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['status'] = RequestStatusUtil::REJECTED;
        $result = parent::update($id, $request);
        if ($result) {
            $notifiable = $this->getOnFinalApproveOrRejectedNotifiableStaffs($result->toArray());
            $notificationData = new NotificationDataRequest((int)$result->id);
            $notificationData->appendData('rejecter', getLoggedUserDisplayName());
            $notificationData->appendData('staff_id', getAuthOwner('staff_id'));
            $notificationData->appendData('request_type', $result->requestType->name ?? "");
            $notificationData->appendData('slug', $result->requestType->slug ?? "");
            $notificationData->appendData('id', $result->id);
            $notificationRequest = new NotificationRequest(
                RejectRequestNotification::class,
                $notificationData,
                $notifiable
            );
            $this->notificationService->notify($notificationRequest);

            dispatch_event_action(new RequestRejected($result));
        }
        return $result;
    }

    public function getOnApproveNotifiableStaffs($entityData){
        if(!isset($entityData['approval_cycle_configuration_id']) || ($entityData['status'] != RequestStatusUtil::PENDING)){
            return $this->getOnFinalApproveOrRejectedNotifiableStaffs($entityData);
        }

        $approvalData = json_decode($entityData['approval_data']);
        $usersThatcanApproveIds = $this->multiCycleApprovalConfigurationService->getLevelApprovers($entityData['approval_cycle_configuration_id'], $approvalData->current_level, $entityData['staff_id']);
        $notifiable = $this->staffRepository->findWhereIn('id', $usersThatcanApproveIds);
        return $notifiable;
     }

     public function getOnFinalApproveOrRejectedNotifiableStaffs($entityData){
        $employees[] = $entityData['staff_id'];
        if($entityData['staff_id'] == 0){
            return [Owner::find(getCurrentSite('id'))];
        }else{
            return $this->staffRepository->findWhereIn('id', $employees);
        }
    }

    /**
     * approve a request
     * @param $id
     * @return mixed
     */
    public function approve($id, $requestModel = null)
    {
        $request = new DefaultRequest();
        if(empty($requestModel)){
            $requestModel = $this->find($id);
        }
        if(isset($requestModel->approval_cycle_configuration_id)){
            /** @var MultiCycleApprovalConfiguration|null  */
            $configuration = $this->multiCycleApprovalConfigurationService->getConfigurations($requestModel->approval_cycle_configuration_id);
            if(!empty($configuration)){
                $approval_data = $requestModel->getApprovalData();
                $dt = new \DateTime("now");
                $newApproverLevel = ["level" => $approval_data->current_level, "staff_id" => getAuthOwner('staff_id'), "note"=>  $request['rejection_description'] ?? '', "date" => $dt->format("Y-m-d H:i:s")];
                $approvers = $approval_data->approvers;
                $approvers[] = $newApproverLevel;
                $request['approval_data'] = json_encode([
                    'current_level'=> $approval_data->current_level + 1,
                    'approvers' => $approvers,
                ]);
                $configLevelsCount = $configuration->getLevelsCount();
                if($configLevelsCount == $approval_data->current_level){
                    $request['status'] = RequestStatusUtil::APPROVED;
                }
            }
        }else{
            $request['status'] = RequestStatusUtil::APPROVED;
        }
        $result = parent::update($id, $request);
        if ($result) {
            if (getCurrentSite('id') == '2149012') {
                return $result;
            }
            $notifiable = $this->getOnApproveNotifiableStaffs($result->toArray());
            $notificationData = new NotificationDataRequest((int)$result->id);
            $notificationData->appendData('approver', getLoggedUserDisplayName());
            $notificationData->appendData('application_date', $result->application_date);
            $notificationData->appendData('staff_name', $result->staff->full_name ?? "");
            $notificationData->appendData('staff_id', getAuthOwner('staff_id'));
            $notificationData->appendData('request_type', $result->requestType->name ?? "");
            $notificationData->appendData('slug', $result->requestType->slug ?? "");
            $notificationData->appendData('id', $result->id);
            $notificationType = AddRequestNotification::class;
            if($result->status == RequestStatusUtil::APPROVED){
                $notificationType = ApproveRequestNotification::class;
            }
            $notificationRequest = new NotificationRequest(
                $notificationType,
                $notificationData,
                $notifiable
            );
            dispatch_event_action(new RequestApproved($result));
            $this->notificationService->notify($notificationRequest);

        }
        return $result;
    }

    /**
     * undo rejection for a request
     * @param $id
     * @return mixed
     */
    public function undoReject($id, $requestModel = null)
    {
        $request = new DefaultRequest();
        if(empty($requestModel)){
            $requestModel = $this->find($id);
        }
        if(isset($requestModel->approval_cycle_configuration_id)){
            $approval_data = $requestModel->getApprovalData();
            //rejected_by attribute is removed in this step
            $request['approval_data'] = json_encode([
                'current_level'=> $approval_data->current_level,
                'approvers' => $approval_data->approvers,
            ]);
        }

        $request['rejection_date'] = null;
        $request['rejection_description'] = null;
        $request['status'] = RequestStatusUtil::PENDING;
        $request['action'] = RequestActionUtil::OPENED;
        $result = parent::update($id, $request);
        if ($result) {
            $notificationData = new NotificationDataRequest((int)$id);
            $notifiable = $this->getOnFinalApproveOrRejectedNotifiableStaffs($result->toArray());
            $notificationRequest = new NotificationRequest(
                RejectRequestNotification::class,
                $notificationData,
                $notifiable
            );
            $this->notificationService->unNotify($notificationRequest);
        }
        return $result;
    }

    /**
     * undo approval for a request
     * @param $id
     * @return mixed
     */
    public function unApprove($id, $requestModel = null)
    {
        $request = new DefaultRequest();
        if(empty($requestModel)){
            $requestModel = $this->find($id);
        }
        if(isset($requestModel->approval_cycle_configuration_id)){
            $approval_data = $requestModel->getApprovalData();
            $prevLevel = $approval_data->current_level - 1;
            $approvers = array_filter($approval_data->approvers, function ($approver) use ($prevLevel) {
                return $approver->level != $prevLevel;
            });
            $request['approval_data'] = json_encode([
                'current_level'=> $prevLevel,
                'approvers' => $approvers,
            ]);
        }

        $undoFromFinalApprove = $requestModel->status == RequestStatusUtil::APPROVED;

        $request['status'] = RequestStatusUtil::PENDING;
        $request['action'] = RequestActionUtil::OPENED;
        $result = parent::update($id, $request);
        if ($result) {
            $notificationType = null;
            $notifiable = [];
            if($undoFromFinalApprove){
                $notificationType = ApproveRequestNotification::class;
                $notifiable = $this->getOnFinalApproveOrRejectedNotifiableStaffs($result->toArray());
            }else{
                $notificationType = AddRequestNotification::class;
                $notifiable = $this->getOnApproveNotifiableStaffs($requestModel->toArray());
            }

            $notificationData = new NotificationDataRequest((int)$id);
            $notificationRequest = new NotificationRequest(
                $notificationType,
                $notificationData,
                $notifiable
            );
            $this->notificationService->unNotify($notificationRequest);
        }
        return $result;
    }

    /**
     * mark a request as opened
     * @param $id
     * @return mixed
     */
    public function open($id)
    {
        $request = new DefaultRequest();
        $request['action'] = RequestActionUtil::OPENED;
        return parent::update($id, $request);
    }

    /**
     * mark a request as closed
     * @param $id
     * @return mixed
     */
    public function close($id)
    {
        $request = new DefaultRequest();
        $request['action'] = RequestActionUtil::CLOSED;
        return parent::update($id, $request);
    }

    /**
     * @param int $requestTypeId
     * @return bool
     */
    private function authorizeAddAndEdit(int $requestTypeId, $request_staff_id)
    {
        if(isOwner()){
            return true;
        }

        $staffService = resolve(StaffService::class);
        if(
            !isset($request_staff_id)
            || !$staffService->isAccessableStaffId($request_staff_id,false)
        ){
            throw new CanNotAddOrEditRequest(sprintf(__t('You do not have access to this %s',true),__t('Record',true)));
        }

        if(
            !isStaff()
            ||
            !(
                $this->entityPermissionService->CheckEmployeeHasPermission(getAuthOwner('staff_id'), $requestTypeId, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, EntityPermissionKeyUtil::CREATE)
                || $this->entityPermissionService->CheckEmployeeHasPermission(getAuthOwner('staff_id'), $requestTypeId, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, EntityPermissionKeyUtil::CREATE_FOR_OTHERS)
            )
        ){
            throw new CanNotAddOrEditRequest;
        }

        return true;
    }

    /**
     * @param array $recordData
     * @return string
     */
    public function getShowRouteUrl(array $recordData)
    {
        $requestType = $this->requestTypeService->find($recordData['request_type_id']);
        return route($this->showRouteName, [$requestType->slug, $recordData['id']]);
    }

    /**
     * @return Collection
     */
    public function getRequestTypesSummary(): Collection
    {
        $authenticatedUser = getAuthOwner('staff_id');
        if ($authenticatedUser) {
            $request_ids = [];
            $userPermissions = $this->entityPermissionService->getCachedEntityPermissionsForStaff(EntityKeyTypesUtil::REQUEST_ENTITY_KEY, (int)$authenticatedUser);
            foreach ($userPermissions as $userCriteriaPermissions) {
                foreach ($userCriteriaPermissions as $criteriaPermission) {
                    if (!is_null($criteriaPermission) && $criteriaPermission->entity_type == EntityKeyTypesUtil::REQUEST_ENTITY_KEY && in_array($criteriaPermission->key, [EntityPermissionKeyUtil::APPROVE_REJECT, EntityPermissionKeyUtil::VIEW, EntityPermissionKeyUtil::CREATE_FOR_OTHERS, EntityPermissionKeyUtil::CREATE])) {
                        $request_ids[] = $criteriaPermission->entity_id;
                    }
                }
            }
            $request_ids = array_unique($request_ids);
            if (empty($request_ids)) {
                $request_ids = [-1];
            }
            return $this->repo->getAllowedRequestsWithHisOwnCountGroupedByRequestTypeId($authenticatedUser, $request_ids, RequestStatusUtil::PENDING);
        } else {
            $request_ids = $this->requestTypeService->repo->all(['id'])->pluck('id')->toArray();
            return $this->repo->getAllowedRequestsWithHisOwnCountGroupedByRequestTypeId($authenticatedUser, $request_ids, RequestStatusUtil::PENDING);

        }
    }

    public function getRequestTypeByRequestId($requestId) {
        return $this->repo->pushCriteria(new EqualCriteria('id', $requestId))
            ->all(['*'], ['requestType'])->first()->requestType ?? null;
    }

    public function formatRequestTypeTabs(Collection $requestTypeTabs): array
    {
        $requestTypeTabsArray = [];
        $allPendingRequestsCount = 0;
        foreach ($requestTypeTabs as $requestTypeTab) {
            $allPendingRequestsCount += $requestTypeTab->count;
            $requestTypeTabsArray[] = [
                'url' => route('owner.entity.list', ['entityKey' => 'le_request_type_' . $requestTypeTab->slug, 'filter[action]' => 'opened']),
                'icon' => $requestTypeTab->icon_key,
                'title' => $requestTypeTab->name,
                'badge_count' => $requestTypeTab->count,
                'badge_text' => __t(RequestStatusUtil::getStatus(RequestStatusUtil::PENDING)),
                'badge_status' => $requestTypeTab->count ? 'danger' : 'inactive'
            ];
        }
        if (count($requestTypeTabsArray) > 1) {
            $requestTypeTabsArray[] = [
                'url' => route('owner.requests.index', ['request_type' => 'all', 'action' => 'opened']),
                'icon' => 'mdi mdi-clipboard-list-outline',
                'title' => __t('All Requests'),
                'badge_count' => $allPendingRequestsCount,
                'badge_text' => __t(RequestStatusUtil::getStatus(RequestStatusUtil::PENDING)),
                'badge_status' => $allPendingRequestsCount ? 'danger' : 'inactive'
            ];
        }

        return $requestTypeTabsArray;
    }

    /**
     * {@inheritDoc}
     */
    public function getSortFields()
    {
        return [
            'fields' => [
                'execution_date' => ['title' => 'Execution Date', 'field' => 'execution_date', 'direction' => 'DESC'],
                'application_date' => ['title' => 'Application Date', 'field' => 'application_date', 'direction' => 'DESC'],
                'created' => ['title' => 'Date of Creation', 'field' => 'created', 'direction' => 'ASC']
            ],
            'active' => ['field' => 'created', 'direction' => 'DESC']
        ];
    }

    /**
     * {@inheritDoc}
     */
    public function getFilters()
    {
        $applicationFromDate = $this->parameters['application_from_date'] ?? null;
        $applicationToDate = $this->parameters['application_to_date'] ?? null;
        if ($applicationFromDate && $applicationToDate) {
            $applicationFromDateCarbon = Carbon::parse(Formatter::formatForDB($applicationFromDate, 'date'));
            $applicationToDateCarbon = Carbon::parse(Formatter::formatForDB($applicationToDate, 'date'));
            if ($applicationFromDateCarbon->gt($applicationToDateCarbon)) {
                $this->parameters['application_from_date'] = $applicationToDate;
                $this->parameters['application_to_date'] = $applicationFromDate;
            }
        }

        $executionFromDate = $this->parameters['execution_from_date'] ?? null;
        $executionToDate = $this->parameters['execution_to_date'] ?? null;
        if ($executionFromDate && $executionToDate) {
            $executionFromDateCarbon = Carbon::parse(Formatter::formatForDB($executionFromDate, 'date'));
            $executionToDateCarbon = Carbon::parse(Formatter::formatForDB($executionToDate, 'date'));
            if ($executionFromDateCarbon->gt($executionToDateCarbon)) {
                $this->parameters['execution_from_date'] = $executionToDate;
                $this->parameters['execution_to_date'] = $executionFromDate;
            }
        }

        $staffList = [];
        if (isset($this->parameters['name']) && !empty($this->parameters['name'])) {
            $staff = $this->staffRepository->find($this->parameters['name']);
            foreach ($staff as $employee) {
                $staffList []= Staff::getStaffOptionFormated($employee) ;
            }
        }

        if (!isset($this->parameters['action'])) {
            $this->parameters['action'] = RequestActionUtil::OPENED;
        }

        $requestTypes = [];
        $this->requestTypeRepository->pushCriteria(new CustomFind([['field' => 'status', 'value' => '1', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]]));
        $types = $this->requestTypeRepository->all();
        foreach ($types as $requestType) {
            $requestTypes[$requestType->id] = "{$requestType->name} #{$requestType->id}";
        }

        $requestFilterFormCustomElements = resolve(RequestFilterFormCustomElements::class);
        $requestStatsOptions = $requestFilterFormCustomElements->getStatusFilterValueOptions();

        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            $this->filters = [
                'name[]' => [
                    'label' => __t('Employee'),
                    'simple' => true,
                    'param_name' => 'name',
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'before' => '<div class="form-group-icon form-group">',
                    'attributes' => [
                        'select2-dimmed' => false,
                        'data-staff-url' => route('owner.staff.search', ['allow_inactive' => true, 'get_branch_suspended' => 1]),
                        'placeholder' => sprintf(__t('Select %s'), __t('Employee')),
                        'id' => 'staffSelect',
                        'multiple' => 'multiple'
                    ],
                    'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                    'div' => 'col-md-4',
                    'options' => $staffList,
                    'type' => 'selectStaff',
                    "filter_options" => [
                        "operation" => FilterOperations::IN_FILTER_OPERATION,
                        "field" => "staff_id"
                    ],
                ],
                'type' => [
                    'simple' => true,
                    'before' => '<div class="form-group">',
                    'after' => '</div>',
                    'label' => __t('Request Type'),
                    'div' => 'col-md-4',
                    'attributes' => [
                        'placeholder' => sprintf(__t('Filter by %s'), __t('Request Type'))
                    ],
                    'type' => 'select',
                    'options' => $requestTypes,
                    'inputClass' => 'select-filter',
                    "filter_options" => [
                        "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
                        "field" => "request_type_id"
                    ]
                ],
                'active' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'select',
                    'empty' => 'text',
                    'attributes' => ['placeholder' => __t('All Status')],
                    'label' => __t('Status'),
                    'inputClass' => 'select-filter',
                    'div' => 'col-md-4',
                    'before' => '<div class="form-group">',
                    'after' => '</div>',
                    'options' => $requestStatsOptions,
                    'filter_options' => [
                        'operation' => FilterOperations::CRITERIA,
                        'criteria' => new RequestStatusCriteria($this->parameters['active']?? 0),
                    ]
                ],
                'application_from_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-3',
                    'inputClass' => 'form-control',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => sprintf('%s %s', __t('Application Date'), __t('(From)')),
                    'attributes' => ['placeholder' => __t('Application Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "application_date"
                    ]
                ],
                'application_to_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-3',
                    'inputClass' => 'form-control',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => sprintf('%s %s', __t('Application Date'), __t('(To)')),
                    'attributes' => ['placeholder' => __t('Application Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "application_date"
                    ]
                ],
                'execution_from_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-3',
                    'inputClass' => 'form-control',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => sprintf('%s %s', __t('Execution Date'), __t('(From)')),
                    'attributes' => ['placeholder' => __t('Execution Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "execution_date"
                    ]
                ],
                'execution_to_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-3',
                    'inputClass' => 'form-control',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => sprintf('%s %s', __t('Execution Date'), __t('(To)')),
                    'attributes' => ['placeholder' => __t('Execution Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "execution_date"
                    ]
                ],
            ];

            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $branches = Branch::getStaffBranchesSuspended();
                if(count($branches) > 1){
                    $this->filters['branch'] = [
                        'simple' => true, /* displays the filter outside the toggle */
                        'type' => 'select',
                        'empty' => 'text',
                        'label' => '',
                        'inputClass' => 'select-filter',
                        'div' => 'col-md-4',
                        'before' => '<div class="form-group">',
                        'after' => '</div>',
                        'attributes' => [
                            'placeholder' => __t('Select') . ' ' . __t('Branch'),
                            'id' => 'branchSelect',
                        ],
                        'options' => $branches,
                        'filter_options' => [
                            'table' => 'staffs',
                            'model' => 'staff',
                            'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                            'field' => 'branch_id',
                        ]
                    ];
                }
            }

            $this->filters['action'] = [
                'simple' => true,
                'before' => '<div class="  form-group">',
                'after' => '</div>',
                'label' => '',
                'div' => 'col-md-6',
                'attributes' => ['id' => 'action', 'placeholder' => __t('Action')],
                'type' => 'hidden',
                'inputClass' => 'form-control',
                'options' => RequestActionUtil::getAllActions(),
                "filter_options" => ["operation" => FilterOperations::EQUAL_FILTER_OPERATION, "field" => "action"]
            ];

            $entityTypeSlug = request('request_type');
            if ($entityTypeSlug != 'all') {
                unset($this->filters['type']);
                $this->filters['name[]']['div'] = 'col-md-6';
                $this->filters['active']['div'] = 'col-md-6';
            }

            return $this->filters;
        }
    }


    public function getAlltListingConditions()
    {
        return $this->getListingConditions();
    }
    protected function getListingConditions()
    {
        $listingConditions = $this->listingHelper->getListingConditions();
        $authUserID = getAuthOwner('staff_id');
        $entityTypeSlug = request('request_type') ?: 'all';
        $allowedPermissions = [
            EntityPermissionKeyUtil::VIEW,
            EntityPermissionKeyUtil::APPROVE_REJECT,
            EntityPermissionKeyUtil::CREATE_FOR_OTHERS
        ];

        if ($authUserID == 0) {
            if ($entityTypeSlug == 'all') {
                // View All Records
                //Filter with the Active Request Types
                $this->requestTypeService->repo->pushCriteria(new CustomFind([['field' => 'status', 'value' => ActiveStatusUtil::ACTIVE, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]]));
                $requestTypes = $this->requestTypeService->all();
                if (empty($requestTypes))
                    throw new NoRequestTypeAddedException();

                $listingConditions[] = [
                    "operation" => FilterOperations::IN_FILTER_OPERATION,
                    "field" => "request_type_id",
                    "value" => $requestTypes->pluck('id')->toArray()
                ];

                return $listingConditions;
            } else {
                // View All From This Request Type
                $requestType = $this->requestTypeService->getRequestTypeBySlug($entityTypeSlug);
                if (!$requestType)
                    throw new EntityNotFoundException('Request Type');

                $entityID = $requestType->id;

                $listingConditions[] = [
                    "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
                    "field" => "request_type_id",
                    "value" => $entityID
                ];
                return $listingConditions;
            }
        } else {
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)){
                $staffService = resolve(StaffService::class);
                $listingConditions[] = [
                    "operation" => FilterOperations::IN_FILTER_OPERATION,
                    "field" => "requests.staff_id",
                    "value" => $staffService->getAccessibleStaffIds(getAuthOwner("staff_id"),true)
                ];
            }

            if ($entityTypeSlug == 'all') {
                $this->requestTypeService->repo->pushCriteria(new CustomFind([['field' => 'status', 'value' => ActiveStatusUtil::ACTIVE, 'operation' => FilterOperations::EQUAL_FILTER_OPERATION]]));
                $requestTypes = $this->requestTypeService->all();
                if (empty($requestTypes))
                    throw new NoRequestTypeAddedException();

                $allowedRequestTypeIDs = [];
                foreach ($requestTypes as $requestType) {
                    $entityID = $requestType->id;
                    $permissionFlag = false;
                    foreach ($allowedPermissions as $permission) {
                        $check = EntityPermission::checkEmployeeHasPermission($authUserID, $entityID, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, $permission);
                        if ($check) {
                            $permissionFlag = true;
                            break;
                        }
                    }

                    if ($permissionFlag)
                        array_push($allowedRequestTypeIDs, $entityID);
                }

                if (empty($allowedRequestTypeIDs)) {
                    // View His Own Requests From All Types
                    $listingConditions[] = [
                        "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
                        "field" => "staff_id",
                        "value" => $authUserID
                    ];
                } else {
                    // View All Requests From Allowed Request Types + His Own Requests From Allowed Type + His own request based on multi-cycle configuration
                    $multiCycleApprovalConfigurationService = resolve(MultiCycleApprovalConfigurationService::class);
                    $configurationsOrClauseRawSql = $multiCycleApprovalConfigurationService->getStaffApprovalConfigurationRelatedItemsRawSql();
                    $requestsIDs = $this->repo->getAllowedRequestsWithHisOwn($authUserID, $allowedRequestTypeIDs, $configurationsOrClauseRawSql)->pluck('id')->toArray();
                    $listingConditions[] = [
                        "operation" => FilterOperations::IN_FILTER_OPERATION,
                        "field" => "requests.id",
                        "value" => $requestsIDs
                    ];
                }
            } else {
                $requestType = $this->requestTypeService->getRequestTypeBySlug($entityTypeSlug);
                if (!$requestType)
                    throw new EntityNotFoundException('Request Type');

                $entityID = $requestType->id;
                $permissionFlag = false;
                foreach ($allowedPermissions as $permission) {
                    $check = EntityPermission::checkEmployeeHasPermission($authUserID, $entityID, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, $permission);
                    if ($check) {
                        $permissionFlag = true;
                        break; // Break when any permission found true, no need to continue
                    }
                }

                if ($permissionFlag) {
                    // View All From This Request Type
                    $listingConditions[] = [
                        "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
                        "field" => "request_type_id",
                        "value" => $entityID
                    ];
                } else {
                    // View All His Own From This Request Type
                    $listingConditions[] = [
                        "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
                        "field" => "request_type_id",
                        "value" => $entityID
                    ];
                    $listingConditions[] = [
                        "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
                        "field" => "staff_id",
                        "value" => $authUserID
                    ];
                }
            }
            return $listingConditions;
        }
    }

    public function getExtraListingData()
    {
        $authUserID = getAuthOwner('staff_id');
        $requestTypeSlug = request('request_type');
        if ($authUserID == 0) {
            return ['slug' => $requestTypeSlug, 'isOwner' => true];
        } else {
            //Check Can Create/Create For Others
            if ($requestTypeSlug == 'all') {
                $requestTypes = $this->requestTypeService->all();
            } else {
                $requestTypes = $this->requestTypeRepository->getRequestTypeBySlug($requestTypeSlug);
            }
            if (empty($requestTypes))
                throw new NoRequestTypeAddedException();

            $canCreate = false;
            $canCreateForOthers = [];
            if ($requestTypeSlug == 'all') {
                foreach ($requestTypes as $requestType) {
                    $entityID = $requestType->id;
                    $checkForOthers = EntityPermission::checkEmployeeHasPermission($authUserID, $entityID, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, EntityPermissionKeyUtil::CREATE_FOR_OTHERS);
                    $canCreate = ($canCreate) ? true : EntityPermission::checkEmployeeHasPermission($authUserID, $entityID, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, EntityPermissionKeyUtil::CREATE);
                    $canCreate = ($canCreate) ? true : $checkForOthers;
                    $canCreateForOthers[$entityID] = $checkForOthers;
                }
            } else {
                $requestType = $requestTypes;
                $entityID = $requestType->id;
                $checkForOthers = EntityPermission::checkEmployeeHasPermission($authUserID, $entityID, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, EntityPermissionKeyUtil::CREATE_FOR_OTHERS);
                $canCreate = ($canCreate) ? true : EntityPermission::checkEmployeeHasPermission($authUserID, $entityID, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, EntityPermissionKeyUtil::CREATE);
                $canCreate = ($canCreate) ? true : $checkForOthers;
                $canCreateForOthers[$entityID] = $checkForOthers;
            }


            //Check His Own Records
            $ownRecords = $this->repo->findWhere([['staff_id', '=', $authUserID]])->pluck('id')->toArray();
            return [
                'slug' => request('request_type'),
                'checks' => ['canCreate' => $canCreate, 'canCreateForOthers' => $canCreateForOthers, 'ownRecords' => $ownRecords]
            ];
        }

    }

    /**
     * {@inheritDoc}
     */
    public function delete($id, Closure $callback = null)
    {
        $id = request('request');
        $request = $this->repo->find($id);
        if (!$request)
            throw new EntityNotFoundException('Request Type');

        if ($request->status == RequestStatusUtil::REJECTED || $request->status == RequestStatusUtil::APPROVED) {
            throw new RequestApprovedRejectedException();
        } else {
            //Permission Check
            $authUserID = getAuthOwner('staff_id');
            if ($authUserID == 0) {
                $result =  parent::delete($id);
            } else {
                $requestTypeID = $request->request_type_id;
                $canCreateForOthers = EntityPermission::checkEmployeeHasPermission($authUserID, $requestTypeID, EntityKeyTypesUtil::REQUEST_ENTITY_KEY, EntityPermissionKeyUtil::CREATE_FOR_OTHERS);
                $hisOwn = ($request->staff_id == $authUserID) ? true : false;
                if ($hisOwn || $canCreateForOthers)
                    $result =  parent::delete($id);
                else
                    throw new ActionNotAllowed('Delete', 'Request');
            }
            $this->removeRedundantNotifications($request);
            if ($result) {
                $this->additionalFieldsAwareService->delete(
                    $this->additionalFieldsAwareService->getEntityKey('le_request_type_' . $request->request_type_id),
                    $id
                );
            }
            return $result;
        }
    }

    public function getViewData($id)
    {
        $requestID = request('request');
        if (!$requestID) {
            throw new EntityNotFoundException(__t($this->mainEntity->label) ?? "");
        }
        $data = parent::getViewData($requestID);
        $data['approve_reject_my_requests'] = $this->canApproveOrRejectHisRequests($requestID);

        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($data['record']->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }
        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('le_request_type_' . request()->request_type,TemplateTypeUtil::PDF, EntityKeyTypesUtil::REQUEST_ENTITY_KEY);
        $data['view_templates'] = $viewTemplates;

        $requestEmailTemplates = $repo->getEntityViewTemplates(EntityKeyTypesUtil::REQUEST_ENTITY_KEY, TemplateTypeUtil::EMAIL);
        $customEntityTemplates = $repo->getEntityViewTemplates('le_request_type_' . request()->request_type, TemplateTypeUtil::EMAIL);
        $data['email_templates'] = array_merge_recursive($requestEmailTemplates, $customEntityTemplates);

        $data['form'] = $this->additionalFieldsAwareService->show('le_request_type_' . request()->request_type, $requestID);

        return $data;
    }

    /**
     * @param int $id
     * @return bool
     * @throws EntityNotFoundException
     */
    public function canApproveOrRejectHisRequests(int $id): bool
    {
        $request = $this->find($id);
        if (!$request) {
            throw new EntityNotFoundException(__t($this->mainEntity->label) ?? "");
        }
        if ($request->requestType->allow_approve_and_reject_my_requests && $request->staff_id == getAuthOwner('staff_id')) {
            return true;
        }
        return false;
    }

    /**
     * @param int $id
     * @param int|null $staffID
     * @return bool
     */
    public function isRequestOwner(int $id, ?int $staffID = null)
    {
        $request = $this->find($id);
        if (!$request) {
            return false;
        }
        $staffID = $staffID ?? getAuthOwner('staff_id');
        return $request->staff_id == $staffID;
    }

    public function getRequestEmailTemplate($request)
    {
        // get selected email template if exists else get default template for request
        if (!empty($request->requestType->default_request_email_template)) {
            [$templateType, $templateId] = explode('_', $request->requestType->default_request_email_template);
            $template = resolve(TemplateService::class)->findTemplateByIdAndType($templateId, $templateType);
        } else {
            $template = resolve(DefaultEmailTemplate::class)->getTemplate(EmailTemplateReservedKeysUtil::REQUEST_NOTIFICATION_EMAIL_TEMPLATE);
        }

        if (!$template || !$template->isActive() || $template->getListingMode() !== TemplateListingModeUtil::AVAILABLE) {
            throw new \Exception('Template not found');
        }

        return $template;
    }

    public function getRequestEmailsList($request)
    {
        $entityPermissions = resolve(DynamicPermissionHelper::class)->getDataHelper(EntityKeyTypesUtil::REQUEST_TYPE_ENTITY_KEY, $request->requestType->id)['permissions'];
        $approveOrRejectPermission = $entityPermissions[EntityPermissionKeyUtil::APPROVE_REJECT];

        $staffRepo = resolve(StaffRepository::class);
        $roleRepo = resolve(RoleRepository::class);

        $staffIds = [];

        if($request->approval_cycle_configuration_id){
            $approval_data = $request->getApprovalData();
            if($approval_data){
                $staffIds = $this->multiCycleApprovalConfigurationService->getLevelApproversWithApproveOrRejectLeavePermission($request->approval_cycle_configuration_id, $approval_data->current_level, $request->staff_id);
            }
        }else{
            if ($approveOrRejectPermission['type'] === EntityPermissionCriteriaUtil::EVERYONE) {
                $staffIds = $staffRepo->getActiveUsers($approveOrRejectPermission['value']);
            } elseif ($approveOrRejectPermission['type'] === EntityPermissionCriteriaUtil::SPECIFIC_BRANCHES) {
                $staffIds = $staffRepo->getStaffIdsByWorkingBranchesIds($approveOrRejectPermission['value']);
            } elseif ($approveOrRejectPermission['type'] === EntityPermissionCriteriaUtil::SPECIFIC_DEPARTMENTS) {
                foreach ($approveOrRejectPermission['value'] as $departmentId) {
                    $staffIds = array_merge($staffIds, $staffRepo->getStaffByDepartmentId($departmentId)->pluck('id')->toArray());
                }
            } elseif ($approveOrRejectPermission['type'] === EntityPermissionCriteriaUtil::SPECIFIC_DESIGNATIONS) {
                $staffIds = $staffRepo->getStaffIdsByDesignationIds($approveOrRejectPermission['value']);
            } elseif ($approveOrRejectPermission['type'] === EntityPermissionCriteriaUtil::SPECIFIC_EMPLOYEES) {
                $staffIds = $staffRepo->getStaffByIds($approveOrRejectPermission['value'])->pluck('id')->toArray();
            } elseif ($approveOrRejectPermission['type'] === EntityPermissionCriteriaUtil::SPECIFIC_ROLE) {
                $staffIds = $roleRepo->getEmployeeIdsByRole($approveOrRejectPermission['value']);
            }

        }

        $to = [];
        $staffs = $staffRepo->getStaffByIds($staffIds)->toArray();

        foreach ($staffs as $staff) {
            $to[] = [
                'role' => EntityEmailPreferenceUserTypeUtil::getStaff(),
                'id' => $staff['id'],
                'email' => $staff['email_address'],
            ];
        }

        return $to;
    }

    public function getRequestStatus($request){
        if(!$request->approval_cycle_configuration_id || ($request->status != RequestStatusUtil::PENDING)){
            return __t(\App\Utils\Requests\RequestStatusUtil::getStatus($request->status));
        }

        return $this->multiCycleApprovalConfigurationService->getApprovalLevelStatus($request);
    }

    public function checkConfigurationRematch($id){
        $configurationWillRematch = false;
        $request = $this->find($id);
        $matchedApprovalConfigurations = $this->getMatchedApprovalConfigurations($request);
        $configurationWillRematch = ($matchedApprovalConfigurations?->getId() ?? null) != $request->approval_cycle_configuration_id;
        return $configurationWillRematch;
    }
}
