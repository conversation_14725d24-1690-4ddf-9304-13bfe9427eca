<?php

namespace App\Services;
use App\Exceptions\WrongPurchaseRequestStatus;
use App\Repositories\EntityRepository;
use App\Repositories\PurchaseQuotationRepository;
use App\Utils\PurchaseQuotationStatusUtil;
use App\Repositories\FollowUpStatusRepository;
use App\Utils\FollowUpItemTypeUtil;

class PurchaseQuotationService extends BaseService
{

    public $repo;
    public $quotationRequestRepository;

    public function __construct(
        PurchaseQuotationRepository $repo,
        FollowUpStatusRepository $followUpStatusRepository,
        EntityRepository $entityRepo = null
    ) {
        parent::__construct($repo, $entityRepo);
        $this->repo = $repo;
        $this->followUpStatusRepository = $followUpStatusRepository;
    }



    public function updateStatus($id, $status){
        $approvedBy = null;
        if ($status == PurchaseQuotationStatusUtil::Approved) {
            $approvedBy = getAuthOwner()['staff_id'];
        }
        $request = request()->merge(['payment_status' => $status, 'approved_by' => $approvedBy]);
        $request->request->remove('entityKey');
        if(!in_array(ucfirst($status), PurchaseQuotationStatusUtil::statusList())){
            throw new WrongPurchaseRequestStatus($status);
        }

        $result = parent::update($id, $request);
        return $result;
    }

    public function getPurchaseOrder($purchaseQuotationId){

    }

    public function updateFollowUpStatus($id, $status){
        $request = request()->merge(['follow_up_status' => $status]);
        $request->request->remove('entityKey');
        $followUpKey = FollowUpItemTypeUtil::PURCHASE_QUOTATION_TYPE;
        $status = $this->followUpStatusRepository->findWhere(['item_type' => $followUpKey,'id' => $status]);
        if(!$status->isEmpty()){
            $result = parent::update($id, $request);
            return $result;
        }

    }

}
