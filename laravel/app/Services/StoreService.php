<?php

namespace App\Services;

use App\Repositories\StoreRepository;

class StoreService extends BaseService
{
    public function __construct(protected StoreRepository $storeRepository)
    {
        parent::__construct($storeRepository);
    }

    public function getRepo(): StoreRepository
    {
        return $this->storeRepository;
    }

    public function getStoresUsersCanUpdate($query = "", $paginate = true)
    {
        return $this->storeRepository->getStoresUsersCanUpdate($query, $paginate);
    }

    public function getStoresUsersCanCreateInvoice($query = "", $paginate = true)
    {
        return $this->storeRepository->getStoresUsersCanCreateInvoice($query, $paginate);
    }

    public function getAllStores($query = "")
    {
        return $this->storeRepository->getAllStores($query);
    }
}
