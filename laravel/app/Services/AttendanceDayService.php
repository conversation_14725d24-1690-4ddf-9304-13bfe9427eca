<?php

namespace App\Services;

use App\Entities\AttendanceCalculationCriteria;
use App\Entities\AttendanceCalculationMultipleCriteria;
use App\Events\NewDayCalculationEvent;
use App\Exceptions\AttendanceCalculationNoEmployeesException;
use App\Exceptions\AttendanceDayInApprovedSheetException;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\Staff\StaffSecondaryShiftDisabled;
use App\Facades\Staff;
use App\Facades\Formatter;
use App\Facades\Permissions;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Helpers\FilterOperations;
use App\Helpers\Placeholder\PlaceholderHelper;
use App\Repositories\AttendanceDayRepository;
use App\Repositories\AttendancePermissionRepository;
use App\Repositories\AttendanceSheetRepository;
use App\Repositories\BranchRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\Criteria\OrderByCriteria;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\EntityRepository;
use App\Repositories\LeaveTypeRepository;
use App\Repositories\ShiftRepository;
use App\Repositories\StaffRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Requests\ActivityLog\ActivityLogRequest;
use App\Requests\AttendanceCalculation\ManualDayCalculationEventRequest;
use App\Requests\AttendanceCalculation\NewDayCalculationEventRequest;
use App\Requests\AttendanceCalculation\ResetDayRequest;
use App\Requests\DefaultRequest;
use App\Services\AttendanceCalculator\AttendanceDayCalculationInitiator;
use App\Services\AttendanceCalculator\Entities\ApprovedAttendanceDay;
use App\Services\AttendanceCalculator\Entities\AttendanceDay;
use App\Services\AttendanceCalculator\Entities\AttendanceSheet;
use App\Services\AttendanceCalculator\Entities\ManualAttendanceDay;
use App\Services\Traits\BulkDeleteService;
use App\Utils\ActionLineMainOperationTypesUtil;
use App\Utils\AttendanceDayStatusTypesUtil;
use App\Utils\AttendancePermissionTypesUtil;
use App\Utils\AttendanceSheetStatusTypesUtil;
use App\Utils\BranchStatusUtil;
use App\Utils\CalculationTypesUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\MultipleShiftTypeUtil;
use App\Utils\PermissionUtil;
use App\Utils\PluginUtil;
use Illuminate\Support\Arr;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Collection;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Exceptions\PageAccessNotAllowedException;
use App\Facades\Branch;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Repositories\Criteria\AttendanceDayHasInvalidLogCriteria;
use App\Repositories\Criteria\BetweenCriteria;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Config;
use Illuminate\Validation\Rule;
use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;

/**
 * AttendanceDayService Class
 * @package App\Services
 * <AUTHOR> Faesal <<EMAIL>>
 */
class AttendanceDayService extends BaseService
{
    use BulkDeleteService;
    protected $paginateWith = ['attendanceFlags'];
    var $filters = [];
    /**
     * @var AttendanceDayRepository
     */
    var $repo;
    protected $showRouteName = 'owner.attendance_days.show';
    /**
     * @var StaffRepository
     */
    public $staffRepository;
    /**
     * @var LeaveTypeRepository
     */
    public $leaveTypeRepository;
    /**
     * @var ShiftRepository
     */
    public $shiftRepository;
    /**
     * @var DepartmentRepository
     */
    public $departmentRepository;
    /**
     * @var DesignationRepository
     */
    public $designationRepository;
    /**
     * @var BranchRepository
     */
    public $branchRepository;
    /**
     * @var AttendanceSheetService
     */
    public $attendanceSheetService;

    /**
     * @var AttendanceSheetRepository
     */
    private $attendanceSheetRepository;

    /**
     * @var AttendanceFlagsService
     */
    private $attendanceFlagService;

    /**
     * AttendanceDayService constructor.
     * @param AttendanceDayRepository $repo
     * @param StaffRepository $staffRepository
     * @param LeaveTypeRepository $leaveTypeRepository
     * @param ShiftRepository $shiftRepository
     * @param DepartmentRepository $departmentRepository
     * @param DesignationRepository $designationRepository
     * @param BranchRepository $branchRepository
     * @param AttendanceSheetService $attendanceSheetService
     * @param AttendanceSheetRepository $attendanceSheetRepository
     * @param ActivityLogService $activityLogService
     * @param AttendanceFlagsService $attendanceFlagService
     * @param EntityRepository|null $entityRepo
     */
    public function __construct(
        protected AttendancePermissionRepository $attendancePermissionRepository,
        protected StaffService $staffService,
        AttendanceDayRepository $repo,
        StaffRepository $staffRepository,
        LeaveTypeRepository $leaveTypeRepository,
        ShiftRepository $shiftRepository,
        DepartmentRepository $departmentRepository,
        DesignationRepository $designationRepository,
        BranchRepository $branchRepository,
        AttendanceSheetService $attendanceSheetService,
        AttendanceSheetRepository $attendanceSheetRepository,
        ActivityLogService $activityLogService,
        AttendanceFlagsService $attendanceFlagService,
        EntityRepository $entityRepo = null
    ) {
        parent::__construct($repo, $entityRepo);
        $this->shiftRepository = $shiftRepository;
        $this->departmentRepository = $departmentRepository;
        $this->designationRepository = $designationRepository;
        $this->branchRepository = $branchRepository;
        $this->leaveTypeRepository = $leaveTypeRepository;
        $this->staffRepository = $staffRepository;
        $this->activityLogService = $activityLogService;
        $this->attendanceSheetService = $attendanceSheetService;
        $this->applyActivityLogUTCTimeZone = true;
        $this->attendanceSheetRepository = $attendanceSheetRepository;
        $this->attendanceFlagService = $attendanceFlagService;
    }

    /**
     * {@inheritDoc}
     */
    public function getSortFields()
    {
        return [
            'fields' => [
                'date' => ['title' => __t('Date'), 'field' => 'date', 'direction' => 'DESC'],
            ],
            'active' => [
                'field' => 'date',
                'direction' => 'DESC',
            ]
        ];
    }

    function beforeExport($item){
        if(!empty($item->leave_type_id)){
            $item->leave_type_id = $item->leaveType?->name ?? $item->leave_type_id;
        }
        return $item;
    }

    /**
     * {@inheritDoc}
     */
    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $oldEntity = $this->find($id);
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($oldEntity->staff_id, false)){
            throw new NotAccessibleStaffBranchException;
        }
        if ($request['status'] == AttendanceDayStatusTypesUtil::PRESENT) {
            $request['on_duty'] = convertToUtc(
                Formatter::formatForDB($request['on_duty'],  EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER )
            );
            $request['off_duty'] = convertToUtc(
                Formatter::formatForDB($request['off_duty'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER)
            );
            $request['sign_in'] = convertToUtc(
                Formatter::formatForDB($request['sign_in'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER)
            );
            $request['sign_out'] = convertToUtc(
                Formatter::formatForDB($request['sign_out'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER)
            );
            // calculate expected working hours
            $request['expected_working_hours'] = round((float)$request['off_duty']->diffInMinutes($request['on_duty'])/60, 2);
            if ($request['leave_count']) {
                $request['expected_working_hours'] *= (1 - $request['leave_count']);
            } else {
                $request['leave_type_id'] = null;
                $request['leave_count'] = 0;
            }
            // calculate actual working hours
            $request['actual_working_hours'] = round((float)$request['sign_out']->diffInMinutes($request['sign_in'])/60, 2);
            // calculate attendance delay
            $onDuty = $request['on_duty'];
            if (isset($oldEntity->attendancePermission) && $oldEntity->attendancePermission->type == 'delay') {
                $onDuty->addMinutes($oldEntity->attendancePermission->late_time);
            }
            $actualSignIn = $request['sign_in'];
            $attendanceDelay = $onDuty->diffInMinutes($actualSignIn, false);
            $shift = $this->shiftRepository->find($oldEntity->shift_id);
            if($shift){
                $shiftDay = $shift->getShiftDayByDate($oldEntity->date);
                if($shiftDay && !$shiftDay->start_late_time_from_on_duty){
                    $attendanceDelay -= $shiftDay->late_time;
                }
            }
            if($attendanceDelay > 0) {
                $permissions = $this->attendancePermissionRepository->getAttendancePermissionByStaffIdAndDate(
                    $oldEntity->staff_id,
                    $oldEntity->date,
                    AttendancePermissionTypesUtil::DELAY
                );
                foreach ($permissions as $permission) {
                    $attendanceDelay -= $permission->late_time;
                }
            }

            if ($attendanceDelay > 0) {
                $request['attendance_delay'] = $attendanceDelay;
            }else {
                $request['attendance_delay']= null;
            }
            // calculate early leave
            $expectedSignOutTime = $request['off_duty'];
            $actualSignOutTime = $request['sign_out'];
            $earlyLeaveTime = $actualSignOutTime->diffInMinutes($expectedSignOutTime, false);
            if($earlyLeaveTime > 0) {
                $permissions = $this->attendancePermissionRepository->getAttendancePermissionByStaffIdAndDate(
                    $oldEntity->staff_id,
                    $oldEntity->date,
                    AttendancePermissionTypesUtil::EARLY
                );
                foreach ($permissions as $permission) {
                    $earlyLeaveTime -= $permission->early_time;
                }
            }
            if ($earlyLeaveTime > 0) {
                $request['early_leave'] = $earlyLeaveTime;
            }else {
                $request['early_leave'] = null;
            }
        } else if (
            $request['status'] == AttendanceDayStatusTypesUtil::LEAVE ||
            $request['status'] == AttendanceDayStatusTypesUtil::ABSENT
        ) {
            $request['sign_in'] = null;
            $request['sign_out'] = null;
            $request['early_leave'] = null;
            $request['attendance_delay'] = null;
            $request['on_duty'] = $oldEntity->on_duty;
            $request['off_duty'] = $oldEntity->off_duty;
            $request['actual_working_hours'] = 0;
	        $request['expected_working_hours'] = 0;
            if ($request['status'] == AttendanceDayStatusTypesUtil::ABSENT) {
                $request['leave_type_id'] = null;
                $request['leave_count'] = 0;
            }
        }
        $request->params['calculation_type'] = CalculationTypesUtil::MANUAL_CALC;
        $request['beginning_in'] = null;
        $request['beginning_out'] = null;
        $request['ending_in'] = null;
        $request['ending_out'] = null;
        $request['attendance_permission_id'] = null;
        // set activity log label
        $this->activityLogLabel = "#$id";
        $updatedAttendanceDay = parent::update($id, $request, $excludedFields);
        $this->recalculateAttendanceDayFlags($oldEntity);
        if (
            $updatedAttendanceDay && isset($updatedAttendanceDay->attendanceSheet) &&
            $updatedAttendanceDay->attendanceSheet->status == AttendanceSheetStatusTypesUtil::PENDING
        ) {
            // recalculate attendance sheet statistics
            $daySheet = $updatedAttendanceDay->attendanceSheet;
            $attendanceSheet = new AttendanceSheet(
                $daySheet->id,
                $daySheet->staff_id,
                $daySheet->date_from,
                $daySheet->date_to,
                $daySheet->status,
                $daySheet->fiscal_start_date ?? null
            );
            $this->attendanceSheetService->updateSheetsData([$updatedAttendanceDay->staff_id => [$attendanceSheet]]);
        }
        return $updatedAttendanceDay;
    }

    /**
     * @param $attendanceDay
     * @throws \Exception
     * recalculates attendance day flags by removing old flags
     * and calculating new ones
     */
    public function recalculateAttendanceDayFlags($attendanceDay) : void {
        //recalculate AttendanceFlags
        $this->repo->deleteAttendanceDayFlags($attendanceDay->id);
        $attendanceDayFlags = [];
        $shiftAttendanceFlags = $this->attendanceFlagService->getAttendanceFlagsByShiftId($attendanceDay->shift_id);
        foreach($shiftAttendanceFlags as $shiftAttendanceFlag) {
            $result = PlaceholderHelper::evaluate($shiftAttendanceFlag->getCondition(), EntityKeyTypesUtil::ATTENDANCE_DAY, $attendanceDay->id);
            if ($result) {
                $formulaValue = PlaceholderHelper::evaluate($shiftAttendanceFlag->getFormula(), EntityKeyTypesUtil::ATTENDANCE_DAY, $attendanceDay->id);
                $attendanceDayFlags[] = [
                    'attendance_day_id'     =>  $attendanceDay->id,
                    'attendance_flag_id'    =>  $shiftAttendanceFlag->getId(),
                    'value'                 =>  $formulaValue,
                ];
            }
        }
        if($attendanceDayFlags) {
            $this->saveAttendanceDayFlags( $attendanceDayFlags );
        }
    }

    public function delete($id, Closure $callback = null)
    {
        $record = $this->repo->find($id);
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($record->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }
        $daySheet = $record->attendanceSheet;
        if($record && $record->attendanceSheet && $record->attendanceSheet->status === AttendanceSheetStatusTypesUtil::APPROVED ) {
            $route = route('owner.attendance_sheets.show',['attendance_sheet' => $record->attendance_sheet_id]);
            throw new \Exception(sprintf(__t("You cannot delete the attendance day that related to an approved attendance sheet %s"), "<a class='text-decoration-underline' href='".$route."'>{$record->attendanceSheet->auto_id}</a>"));
        }
        $callback = function ($oldData) use ($callback,$daySheet) {
            $this->repo->resetDayLogs($oldData->id);
            if($callback) {
                $callback($oldData);
            }
            if($daySheet) {
//                $eventRequest = new ManualDayCalculationEventRequest(
//                    $oldData->date,
//                    MultipleShiftTypeUtil::PRIMARY,
//                    new AttendanceCalculationCriteria([$oldData->staff_id]),
//                );
//                $attendanceDayCalculationInitiator = new AttendanceDayCalculationInitiator();
//                $attendanceDayCalculationInitiator->calculate($eventRequest);
                $attendanceSheet = new AttendanceSheet(
                    $daySheet->id,
                    $daySheet->staff_id,
                    $daySheet->date_from,
                    $daySheet->date_to,
                    $daySheet->status,
                    $daySheet->fiscal_start_date ?? null
                );
                $this->attendanceSheetService->updateSheetsData([$oldData->staff_id => [$attendanceSheet]]);
            }
        };
        return parent::delete($id, $callback); // TODO: Change the autogenerated stub
    }

    /**
     * @param array $employeeIds
     * @return array
     */
    public function getEmployeeDataByIds(array $employeeIds)
    {
        $this->staffRepository->pushCriteria(new CustomFind([
            ['field' => 'id', 'operation' => FilterOperations::IN_FILTER_OPERATION, 'value' => $employeeIds],
            ['field' => 'active', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => 1],
            ['field' => 'deleted_at', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => null]
        ]));
        $staffList = [];
        $staffAttributes = [];
        $staffObjects = $this->staffRepository->all();
        foreach ($staffObjects as $staff) {
            $staffList[] = Staff::getStaffOptionFormated($staff) ;;
        }
        $staffAutoFillData = [
            'staffOptionsAttributes' => $staffAttributes,
            'staffOptions' => $staffList,
        ];
        return $staffAutoFillData;
    }

    /**
     * @return array
     */
    static function getStatuses() : array
    {
        return [
            AttendanceDayStatusTypesUtil::PRESENT => __t('Present'),
            AttendanceDayStatusTypesUtil::ABSENT => __t('Absent'),
            AttendanceDayStatusTypesUtil::SIGN_IN_ONLY => __t('Sign In Only'),
            AttendanceDayStatusTypesUtil::SIGN_OUT_ONLY => __t('Sign Out Only'),
            AttendanceDayStatusTypesUtil::DAY_OFF => __t('Day Off'),
            AttendanceDayStatusTypesUtil::LEAVE => __t('Leave')
        ];
    }
    /**
     * @return string
     */
    static function getStatus(string $type) : string
    {
        return static::getStatuses()[$type] ?? '';
    }

    /**
     * @return array
     */
    public function leaveTypes() : array
    {
        return $this->leaveTypeRepository->all()->pluck('name', 'id')->toArray();
    }

    /**
     * @param Collection $staffs
     * @param $fromDate
     * @param $toDate
     * @return int
     */
    public function getNumberOfIgnoredDays(Collection $staffs, $fromDate, $toDate)
    {
        $staffsIDS = array_map(function ($staff) {
            return $staff->id;
        }, $staffs->toArray());
        return $this->attendanceSheetRepository->getNumberOfIgnoredDays($staffsIDS, $fromDate, $toDate);
    }

    function getWithRelations(){
        return ['employee', 'staff_info'];
    }

    /**
     * {@inheritDoc}
     */
    protected function getListingConditions()
    {
        $listingConditions = parent::getListingConditions();
        $authUserID = getAuthOwner('staff_id');
        if(Permissions::isOwnerOrAdmin($authUserID)){
            return $listingConditions;
        }
        $accessibleBranchListingCondition = $this->getAccessibleBranchListingCondition();
        if(!empty($accessibleBranchListingCondition))
            $listingConditions[] = $accessibleBranchListingCondition;

        if (
            Permissions::checkSpecificPermissionForStaff(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG, $authUserID)
            || Permissions::checkSpecificPermissionForStaff(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS, $authUserID)
        ){
            return $listingConditions;
        }
        if($this->staffService->getAuthDepartmentId() && Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE)){
            // should show his department only
            $listingConditions[] =[
                'operation' => FilterOperations::RAW_OPERATION,
                'field' => 'attendance_days.staff_id IN ( select staff_id from staff_info where department_id = ? )',
                'value' => [$this->staffService->getAuthDepartmentId()],
            ];
        }else{
            // should show his own only
            $listingConditions[] = [
                "operation" => FilterOperations::EQUAL_FILTER_OPERATION,
                "field" => "staff_id",
                "value" => $authUserID
            ];
        }
        return $listingConditions;
    }

    public function listing() :array{
        $listingData = parent::listing();
        $listingData['authDeptId'] = $this->staffService->getAuthDepartmentId();
        $listingData['invalidLogsByDays'] = $this->repo->getAllInvalidLogsByAttendanceDays($listingData['pagination']);
        return $listingData;
    }

    public function listingForUser($staffId, $dateFrom = null, $dateTo = null , $orderByDir = 'desc'){
        $this->repo->pushCriteria(new EqualCriteria('staff_id', $staffId));
        if(!empty($dateFrom) || !empty($dateTo)){
            $this->repo->pushCriteria(new BetweenCriteria(
                'date',
                !empty($dateFrom) ? Formatter::formatForDb($dateFrom, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : null,
                !empty($dateTo) ? Formatter::formatForDb($dateTo, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : null
            ));
        }
        $this->repo->pushCriteria(new OrderByCriteria('date', $orderByDir ));
        $data = $this->repo->paginate(30);

        return $data;
    }

    /**
     * {@inheritDoc}
     */
    public function getFormData($id = false)
    {
        $data = parent::getFormData($id);
        if ($id) {
            $attendanceDay = $data['form_record'] ?? null;
            if(!$attendanceDay){
                throw new EntityNotFoundException(__t('Attendance Day', true));
            }
            if ($data['form_record']->shift_id) {
                $date = $data['form_record']->date;
                $employeeShift = $this->shiftRepository->find($data['form_record']->shift_id);
                if($employeeShift) {
                    $shiftDay = $employeeShift->getShiftDayByDate($date);
                    if ($shiftDay) {
                        $data['onDutyShift'] = $date . " " . $shiftDay->from_time;
                        $data['offDutyShift'] = $date . " " . $shiftDay->to_time;
                    }
                }
            }
            $staffId = $data['form_record']->staff_id;
            $staff = $this->staffRepository->find($staffId);
            if (!$staff) {
                throw new EntityNotFoundException(__t("Employee"));
            }
            $this->validateAccessibleAttendanceDayForEdit($attendanceDay);
            $data['staffOptions'] = [
                Staff::getStaffOptionFormated($staff)
            ];
        } else {
            $employees = old('employees');
            if (!$employees) {
                $employees = request('name', false);
            }
            if ($employees) {
                $staffId = $employees ? $employees : $data['form_record']->staff_id;
                $staffs = $this->staffRepository->find($staffId);
                if (!$staffs) {
                    throw new EntityNotFoundException(__t("Employee"));
                }

                if(!$staffs instanceof Collection) {
                   $staffs = collect([$staffs]);
                }

                foreach ($staffs as $staff) {
                    $data['staffOptions'][] =Staff::getStaffOptionFormated($staff) ;
                }
            }
        }
        return $data;
    }

    public function validateAccessibleAttendanceDayForEdit($attendanceDay){
        if (Permissions::isOwnerOrAdmin()){
            return;
        }

        if(!$this->staffService->isAccessableStaffId($attendanceDay->staff_id, false)){
            throw new NotAccessibleStaffBranchException;
        }

        if(!Permissions::checkPermission(PermissionUtil::EDIT_ATTENDANCE_DAY)){
            $hasDeptEditPermission = Permissions::checkPermission(PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE);
            $recordDeptId = $attendanceDay->staff_info?->department_id ?? null;
            $authDeptId =$this->staffService->getAuthDepartmentId();
            $sameDept = isset($recordDeptId) && isset($authDeptId) && $authDeptId == $recordDeptId;
            if(!$sameDept || !$hasDeptEditPermission){
                throw new PageAccessNotAllowedException;
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    public function getFormRelatedData()
    {
        return [
            'staff_list' => $this->staffRepository->list(['id', 'name']),
            'leave_type_list' => $this->leaveTypeRepository->list(['id', 'name']),
            'shifts' => $this->shiftRepository->list(['id', 'name']),
            'departments' => $this->departmentRepository->pushCriteria(new EqualCriteria('active' , 1))->list(['id', 'name']),
            'designations' => $this->designationRepository->pushCriteria(new EqualCriteria('active' , 1))->list(['id', 'name']),
            'branches' => Plugins::pluginActive(PluginUtil::BranchesPlugin) ? $this->branchRepository->pushCriteria(new EqualCriteria('status', BranchStatusUtil::ACTIVE))->list(['id', 'name']) : []
        ];
    }

    /**
     * {@inheritDoc}
     */
    function getFilters()
    {
        $fromDate = $this->parameters['from_date']??null;
        $toDate = $this->parameters['to_date']??null;
        if ($fromDate && $toDate) {
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['from_date'] = $toDate;
                $this->parameters['to_date'] = $fromDate;
            }
        }
        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
                $depts = $this->departmentRepository->list(['id', 'name']);
            }
            $staffList = [];
            if (isset($this->parameters['name']) && !empty($this->parameters['name'])) {
                $name = $this->parameters['name'];
                if(!is_array($name)){
                    $name = [$name];
                }
                $staff = $this->staffRepository->find($name);
                foreach ($staff as $employee) {
                    $staffList += [$employee->id => "#{$employee->code} {$employee->name}({$employee->email_address})"];
                }
            }
            $this->filters = [
                'from_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-3',
                    'inputClass' => 'form-control',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => false,
                    'attributes' => ['placeholder' => __t('From Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "date"
                    ]
                ],
                'to_date' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'type' => 'date',
                    'div' => 'col-md-3',
                    'inputClass' => 'form-control',
                    'after' => '</div>',
                    'before' => '<div class="form-group form-group-icon">',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'label' => false,
                    'attributes' => ['placeholder' => __t('To Date')],
                    "filter_options" => [
                        "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                        "field" => "date"
                    ]
                ],
                'contains_invalid_logs' => [
                    'label' => __t('Contains Invalid Logs'),
                    'attributes' => [
                        'id' => 'contains_invalid_logs'
                    ],
                    'div' => 'col-md-3 form-group',
                    'inputClass' => 'form-control',
                    'type' => 'select',
                    'options' => [__t('All'), __t('Yes'), __t('No')],
                    'filter_options' => [
                        'operation' => FilterOperations::CRITERIA,
                        'criteria' => new AttendanceDayHasInvalidLogCriteria($this->parameters['contains_invalid_logs']?? 0),
                    ]
                ],
                'type' => [
                    'simple' => false, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-3',
                    'options' => self::getStatuses(),
                    'type' => 'select',
                    'inputClass' => 'form-control select-filter',
                    'attributes' => [
                        'placeholder' => __t('All Status'),
                        'id' => 'status'
                    ],
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'status'
                    ]
                ],
                'depts' => Plugins::pluginActive(PluginUtil::HRM_PLUGIN) ? [
                    'label' => '',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Department'),
                        'id' => 'deptSelect'
                    ],
                    'div' => 'col-md-4 form-group',
                    'inputClass' => 'select-filter form-control',
                    'options' => $depts->toArray(),
                    'type' => 'select',
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'department',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'department_id'
                    ]
                ] : null,
                'leave_type_id' => [
                    'simple' => request('type') ? true : false,
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group leave_type_div d-none">',
                    'div' => 'col-md-3',
                    'options' => $this->leaveTypes(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('Leave Type'),
                        'id' => 'leave_type'
                    ],
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'leave_type_id',
                    ],
                ],
                'attendance_sheet_id' => [
                    'simple' => false, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-3',
                    'options' => self::getStatuses(),
                    'type' => 'hidden',
                    'inputClass' => '',
                    'attributes' => [
                        'placeholder' => __t('All Status'),
                        'id' => 'attendance_sheet_id'
                    ],
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'attendance_sheet_id'
                    ]
                ]
            ];
            if (Permissions::checkPermission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG) || Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS)) {
                $this->filters = array_merge(['name[]' =>[
                    'param_name' => 'name',
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'before' => '<div class="form-group-icon form-group">',
                    'label' => __t('Employee'),
                    'attributes' => [
                        'placeholder' => __t(getEmployeesFieldPlaceholder()),
                        'id' => 'staffSelect',
                        'multiple' => 'multiple'
                    ],
                    'div' => 'col-md-6',
                    'options' => $staffList,
                    'type' => 'select',
                    "filter_options" => [
                        "operation" => FilterOperations::IN_FILTER_OPERATION,
                        "field" => "staff_id"
                    ],
                ]], $this->filters);
            }
            if(!$this->filters['depts']){
                unset($this->filters['depts']);
            }
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $branches = Branch::getStaffBranchesSuspended();
                if(count($branches) > 1){
                    $this->filters['branch'] = [
                        'simple'=> true,
                        'label' => false,
                        'after' => '</div>',
                        'before' => '<div class="form-group">',
                        'attributes' => [
                            'placeholder' => __t('Select') . ' ' . __t('Branch'),
                            'id' => 'branchSelect',
                        ],
                        'div' => 'col-md-4',
                        'inputClass' => 'select-filter form-control',
                        'options' => $branches,
                        'type' => 'select', 'filter_options' => [
                            'table' => 'staffs',
                            'model' => 'employee',
                            'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                            'field' => 'branch_id',
                        ]
                    ];
                }
            }
            return $this->filters;
        }
    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['leave_type_id'] = $record->leaveType ? $record->leaveType->name : '';
        $data['staff_id'] = $record->employee ? $record->employee->name : '';
        return $data;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record): array
    {
        $relations = parent::getActivityLogRelations($record);
        if ($record->leaveType) {
            $relations[] = new ActivityLogRelationRequest($record->leaveType->id, EntityKeyTypesUtil::LEAVE_TYPE);
        }
        if ($record->employee) {
            $relations[] = new ActivityLogRelationRequest($record->employee->id, EntityKeyTypesUtil::STAFF_ENTITY_KEY);
        }
        if ($record->attendanceSheet) {
            $relations[] = new ActivityLogRelationRequest($record->attendanceSheet->id, EntityKeyTypesUtil::ATTENDANCE_SHEET);
        }
        return $relations;
    }

    public function resetDaysAndLogsViaCalculator(ResetDayRequest $request)
    {
        $resetDaysIDs = $this->repo->getResetDaysIDs(
            $request->getEmployeeId(),
            $request->getFromDate(),
            $request->getToDate(),
            $request->getOverrideManual()
        );
        $this->repo->resetLogs($resetDaysIDs, $request->getEmployeeId(), $request->getFromDate(), $request->getToDate());
        $this->repo->resetDays($resetDaysIDs);
    }

    public function addAttendanceDayFlag( $attendance_day_id, $attendance_flag_id, $value )
    {
        $this->repo->addAttendanceDayFlag( $attendance_day_id, $attendance_flag_id, $value );
    }

    /**
     * save a bulk of attendance days
     * @param $attendanceDays
     */
    public function saveAttendanceDays($attendanceDays)
    {
        $data = [];
        for($i=0; $i<count($attendanceDays); ++$i) {
            /** @var $day AttendanceDay */
            $day = $attendanceDays[ $i ];
            if ($day->isSave()) {
                if ($day->getCalculationType() != CalculationTypesUtil::MANUAL_CALC) {
                    $data[] = $day->getDataForModel();
                } else {
                    $this->repo->updateAttendanceSheetForManualDay($day);
                }
            }
        }
        $this->repo->insertBulk($data);
        foreach ($attendanceDays as $attendanceDay) {
            $activityLogRelations = [];
            if ($attendanceDay->getAttendanceSheetId()) {
                $activityLogRelations = [
                    new ActivityLogRelationRequest(
                        $attendanceDay->getAttendanceSheetId(),
                        EntityKeyTypesUtil::ATTENDANCE_SHEET
                    )
                ];
            }
            $activityLogRequest = new ActivityLogRequest(
                $attendanceDay->getId(),
                EntityKeyTypesUtil::ATTENDANCE_DAY,
                ActionLineMainOperationTypesUtil::CALCULATE_ACTION,
                '#' . $attendanceDay->getId(),
                route('owner.attendance_days.show', ['attendance_day' => $attendanceDay->getId()]),
                [],
                [],
                $activityLogRelations
            );
            $this->activityLogService->addActivity($activityLogRequest);
        }
    }

    public function saveAttendanceDayFlags( $data )
    {
        $this->repo->insertBulkAttendanceDayFlags( $data );
    }

    public function getManualDays( ResetDayRequest $request )
    {
        if( $request->getOverrideManual() )
            return [];

        $manual_days = $this->repo->getManualDays( $request->getEmployeeId(),
            $request->getFromDate(),
            $request->getToDate() );

        $data = [];
        foreach ( $manual_days as $day )
        {
            $data[ $day->staff_id ][ $day->date ] = new ManualAttendanceDay(
                $day->id, $day->staff_id, $day->date, $day->status, $day->day_off_type, $day->shift_id, $day->sign_in, $day->sign_out, $day->on_duty, $day->beginning_in, $day->beginning_out, $day->off_duty, $day->ending_in, $day->ending_out, $day->expected_working_hours, $day->actual_working_hours, $day->leave_type_id, $day->leave_count
            );
        }

        return $data;
    }

    public function getSheetsApprovedAttendanceDays($sheetIds)
    {
        $approvedDays = $this->repo->getSheetsApprovedAttendanceDays($sheetIds);
        $data = [];
        foreach ($approvedDays as $day)
        {
            $data[ $day->staff_id ][ $day->date ] = new ApprovedAttendanceDay(
                $day->id, $day->staff_id, $day->date, $day->status, $day->day_off_type, $day->shift_id, $day->sign_in, $day->sign_out, $day->on_duty, $day->beginning_in, $day->beginning_out, $day->off_duty, $day->ending_in, $day->ending_out, $day->expected_working_hours, $day->actual_working_hours, $day->leave_type_id
            );
        }
        return $data;
    }

    public function getAttendanceDayLeaveTypeStats($from_date, $to_date, $staffId = null){
        $result = $this->repo->getAttendanceDayLeaveTypeStats($from_date, $to_date, $staffId);
        $total = array_sum(Arr::pluck($result, 'count'));
        $result = array_map(function($row) use($total){
          $row->percent = round(($row->count / $total) * 100);
          return $row ;
        }, $result->toArray());
        return ['leaves' => $result, 'total' => $total] ;
    }

    /**
     * @param $calculationRequest
     * @return array
     * @throws AttendanceCalculationNoEmployeesException
     */
    public function prepareAutomaticCalculationData(DefaultRequest $calculationRequest)
    {
        /** Add for override_manual_attendance checkbox **/
        if (Settings::getValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'override_manual_attendance_' . getAuthOwner('staff_id') != $calculationRequest['override_manual_attendance'])) {
            Settings::setValue(PluginUtil::HRM_ATTENDANCE_PLUGIN, 'override_manual_attendance_' . getAuthOwner('staff_id'), $calculationRequest['override_manual_attendance']);
        }
        $fromDate = Formatter::formatForDB($calculationRequest['from_date'], 'date');
        $toDate = Formatter::formatForDB($calculationRequest['to_date'], 'date');
        $attendanceCalculationCriteria = new AttendanceCalculationMultipleCriteria(
            $calculationRequest['employees'] ?? [],
            $calculationRequest['branches'] ?? [],
            $calculationRequest['departments'] ?? [],
            $calculationRequest['designations'] ?? [],
            $calculationRequest['shifts'] ?? [],
                $calculationRequest['exclude_criteria'] ?? []
        );
        $staffs = $this->staffRepository->getAttendanceDayMultipleCriteriaStaff($attendanceCalculationCriteria);
        $authUser = getAuthOwner();
        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $staffService = resolve(StaffService::class);
            $accessableStaffMap = $staffService->getAccessibleStaffIds($authUser['staff_id'],false,true);
            $staffs = $staffs->filter(fn($staff, $key) => isset($accessableStaffMap[$staff->id]));
        }
        if ($staffs->isEmpty()) {
            throw new AttendanceCalculationNoEmployeesException;
        }

        return [
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'staffs' => $staffs,
            'calculationCriteria' => $attendanceCalculationCriteria
        ];
    }

    /**
     * @param DefaultRequest $calculationRequest
     * @return array
     * @throws AttendanceDayInApprovedSheetException
     * @throws EntityNotFoundException
     * @throws StaffSecondaryShiftDisabled
     */
    public function prepareManualCalculationData(DefaultRequest $calculationRequest): array
    {
        // If secondary shift not enabled $calculationRequest['shift_type'] will be null
        $calculationRequest['shift_type'] ??= MultipleShiftTypeUtil::PRIMARY;

        $date = Formatter::formatForDB($calculationRequest['date'], 'date');
        $staff = $this->staffRepository->find($calculationRequest['employee']);
        if (!$staff)
            throw new EntityNotFoundException(__t('Employee'));

        $day_record = $this->repo->findWhere(['date' => $date, 'staff_id' => $staff->id, 'is_secondary' => ($calculationRequest['shift_type'] == MultipleShiftTypeUtil::PRIMARY) ? 0 : 1])->first();
        if ($day_record) {
            $sheet = $day_record->attendanceSheet;
            if ($sheet && $sheet->status == AttendanceSheetStatusTypesUtil::APPROVED)
                throw new AttendanceDayInApprovedSheetException($sheet);
        } else {
            if ($staff->staff_info && !$staff->staff_info->has_secondary_shift && $calculationRequest['shift_type'] == MultipleShiftTypeUtil::SECONDARY)
                throw new StaffSecondaryShiftDisabled($staff);
        }

        if ($day_record)
            return ['status' => false, 'record' => $day_record];
        else
            return ['status' => true, 'criteria' => new AttendanceCalculationCriteria([$staff->id])];
    }

    public function getStaffOptions($staff_id)
    {
        $staffList = [];
        $staff = $this->staffRepository->find($staff_id);
        if ($staff) {
            $staffList[] = Staff::getStaffOptionFormated($staff) ;;
        }

        return $staffList;
    }

    public function checkLogsInDate($employee_ids, $date, $shift_type): array
    {
        return $this->repo->checkLogsInDate($employee_ids, $date, $shift_type)->pluck('log_id')->toArray();
    }

    public function resetLogsInDate($employee_ids, $date, $except_ids = []): int
    {
        return $this->repo->resetLogsInDate($employee_ids, $date, $except_ids);
    }

    public function calculateManualDay(ManualDayCalculationEventRequest $calculateRequest)
    {
        $attendanceDayCalculationInitiator = new AttendanceDayCalculationInitiator();
        $response = $attendanceDayCalculationInitiator->calculate($calculateRequest);

        if ($response)
            return $response->getAttendanceDayIds()[0];
        else
            return null;
    }

    public function getViewData($id)
    {
        $viewData = parent::getViewData($id);
        $record = $viewData['record'];
        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('attendance_day');

        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($record->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }

        $viewData['authDeptId'] = $this->staffService->getAuthDepartmentId();

        if(
            !Permissions::checkPermission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG)
            && !Permissions::checkPermission(PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS)
        ){
            $hasOwnViewPermission = Permissions::checkPermission([PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET]);
            $hasDeptViewPermission = Permissions::checkPermission(PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE);
            $recordDeptId = $record->staff_info?->department_id ?? null;
            $sameDept = isset($recordDeptId) && isset($viewData['authDeptId']) && $viewData['authDeptId'] == $recordDeptId;
            $recordStaffId = $record['staff_id'] ?? null;
            $authId = getAuthOwner('staff_id');
            $isOwn = isset($recordStaffId) && isset($authId) && $recordStaffId == $authId;
            $hasViewPermission = ($sameDept && $hasDeptViewPermission) || ($isOwn && $hasOwnViewPermission);
            if(!$hasViewPermission){
                throw new PageAccessNotAllowedException;
            }
        }
        $viewData['view_templates'] = $viewTemplates;
        return $viewData;
    }

    public function getStaffMonthlyAttendanceDate($staffId , $date)
    {
        $response = [
            "items"=>[],
            "totals"=>[],
        ];
        $canViewAttendanceLogs = $this->userCanViewAttendanceLogs($staffId);
        if (!$canViewAttendanceLogs){
            return $response;
        }

        $results = $this->repo->getStaffMonthlyAttendanceDate($staffId , $date);

        $totalEarlyOut =0;
        $totalLateIn = 0;
        $totalActual = 0;
        $overtime = 0;
        $missingTime = 0;

        if (count($results)){
            $dates = $this->getMonthDates($date);
            foreach ($dates as $day){
                $response['items'][$day]= $this->formatAttendanceDaysData($day , true);
            }
        }

        foreach ($results as $date => $result){
            $day = $result->first();

            $totalEarlyOut += $day->early_leave;
            $totalLateIn += $day->attendance_delay;

            $totalActual += ($day->actual_working_hours * 60);

            $actualExpectedDiff = ($day->actual_working_hours * 60) - ($day->expected_working_hours * 60);
            $expectedActualDiff = ($day->expected_working_hours* 60) - ($day->actual_working_hours* 60);

            if ($actualExpectedDiff > 0) {
                $overtime+= $actualExpectedDiff;
            }

            if ($expectedActualDiff > 0) {
                $missingTime+= $expectedActualDiff;
            }

            $response['items'][$date] = $this->formatAttendanceDaysData($day);
        }
        $response['totals']=[
            "lateIn" =>  $this->formatDuration($totalLateIn),
            "earlyOut" =>  $this->formatDuration($totalEarlyOut),
            "missing" =>  $this->formatDuration($missingTime),
            "overtime" =>  $this->formatDuration($overtime),
            "actual" => $this->formatDuration($totalActual),
        ];
        return $response;
    }

    public function getMonthDates($date) {
        $carbonDate = Carbon::parse($date);
        $startOfMonth = $carbonDate->copy()->startOfMonth();
        $endOfMonth = $carbonDate->copy()->endOfMonth();

        $dates = [];
        for ($date = $startOfMonth; $date->lte($endOfMonth); $date->addDay()) {
            $dates[] = $date->format('Y-m-d');
        }
        return $dates;
    }
    private function formatAttendanceDaysData($dayData = null , $isNotCalculated = false)
    {
        if ($isNotCalculated){
            $dayName =\Illuminate\Support\Carbon::parse($dayData)->format('l');
            return [
                "id" => null,
                "viewUrl" =>null,
                "day" => substr($dayName, 0, 3),
                "status" => 'uncalculated' ,
                "sign_in" => '-',
                "sign_out" => '-',
                "late_in" =>'-' ,
                "early_out" =>'-' ,
                "missing" =>'-' ,
                "overtime" =>'-' ,
                "actual" => '-'
            ];
        }

        $dayName =\Illuminate\Support\Carbon::parse($dayData->date)->format('l');
        $dayLabel = match ($dayData->status){
            AttendanceDayStatusTypesUtil::DAY_OFF => "OFF",
            AttendanceDayStatusTypesUtil::ABSENT => "A",
            AttendanceDayStatusTypesUtil::LEAVE => "L",
            default => null
        };

        if($dayLabel && $dayLabel !== "A"){
            $signIn =$signOut =$lateInTime =$earlyOutTime =$missingHours =$overtimeHours =$actual = $dayLabel;
        }elseif ($dayLabel === "A") {
            $signIn =$signOut =$lateInTime =$earlyOutTime =$overtimeHours =$actual = $dayLabel;
            $missingHours = $dayData->expected_working_hours;

        }else{
            $signIn = '-';
            if ($dayData->sign_in){
                $signIn =convertFromUtc($dayData->sign_in)->format('H:i');
            }
            $signOut = '-';
            if ($dayData->sign_out){
                $signOut =convertFromUtc($dayData->sign_out)->format('H:i');
            }

            $actualExpectedDiff = ($dayData->actual_working_hours * 60) - ($dayData->expected_working_hours*60);
            $expectedActualDiff = ($dayData->expected_working_hours*60) - ($dayData->actual_working_hours*60);

            $lateInTime = $this->formatDuration($dayData->attendance_delay);
            $earlyOutTime = $this->formatDuration($dayData->early_leave);
            $missingHours = ($expectedActualDiff > 0) ? $this->formatDuration($expectedActualDiff) : 0;
            $overtimeHours = ($actualExpectedDiff > 0 ) ? $this->formatDuration($actualExpectedDiff)  : 0;

            $actual = $dayData->actual_working_hours;
        }

        return [
            "id" => $dayData->id,
            "viewUrl" => route("owner.attendance_days.show", ['attendance_day'=>$dayData->id] ),
            "day" => substr($dayName, 0, 3),
            "status" =>$dayData->status ,
            "sign_in" => $signIn,
            "sign_out" => $signOut,
            "late_in" =>$lateInTime ,
            "early_out" =>$earlyOutTime ,
            "missing" =>$missingHours ,
            "overtime" =>$overtimeHours ,
            "actual" => round((float)$actual,2)
        ];
    }

    private function  formatDuration($minutes) {
        $hours = floor($minutes / 60);
        $min = ceil($minutes - ($hours * 60));
        if ($min <= 0  && $hours <= 0) return '-';
        if (!$hours) return  "{$min}m";
        if (!$min) return  "{$hours}h";
        return "{$hours}h {$min}m";
    }

    private function userCanViewAttendanceLogs($staffId)
    {
        if (Permissions::checkPermission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG)) return true;
        $authStaff = getAuthOwner('staff_id');
        if (Permissions::checkPermission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG) && $authStaff == $staffId) return true;
        return  false;
    }

    public function getImportDownloadSample()
    {

        return   ['path' => '/samples/attendance-day-sample.csv', 'file_name' => __t('Attendance Day sample') . '.csv'];
    }


    public function validateImportData($data, $validationRules, $headersInfo, $entityKey, $rowCount, $isUpdate, $updateUsing, &$invalidData, &$willBeUpdated , $customData=null)
    {
        $entityRulesGetter = resolve(EntityRulesGetter::class);
        $validator = new DaftraValidator($entityKey, $entityRulesGetter);
        $dateFormatIndex = getCurrentSite('date_format');
        $dateFormats = getDateFormats('std');
        $defaultDateFormat = $dateFormats[$dateFormatIndex];
        $status = $data['status']?? "";
        $messages = [];
        $customValidationRules = [
            'date' => ['required', 'date_format:'.$defaultDateFormat],
            "leave_type_id" => [
                'nullable',
                'required_if:status,' . AttendanceDayStatusTypesUtil::LEAVE,
                 Rule::exists('currentSite.leave_types', 'id')->whereNull('deleted_at')
            ],
            'status' => [
                'required',
                'in:'.AttendanceDayStatusTypesUtil::ABSENT.','.AttendanceDayStatusTypesUtil::LEAVE.','.AttendanceDayStatusTypesUtil::PRESENT
            ],
            'leave_count' => [
                'nullable',
                // 'required_if:status,leave',
                'numeric',
                'in:0.5,0.25,1',
            ],
            'sign_in' => [
                'required_if:status,present',
                function ($attribute, $value, $fail) use ($data) {
                    if ($value && $data['status'] !== 'present') {
                        $fail(__t("You should remove the sign-in when the status of the attendance day is set to 'leave' or 'absent'"));
                    }
                },
                function ($attribute, $value, $fail) use ($data) {
                    if ($value && $data['leave_count']) {
                        $fail(__t("You should remove the sign-in, as you have already entered the leave count for the attendance day"));
                    }
                },
            ],
            'sign_out' => [
                'required_if:status,present',
                'after_or_equal:sign_in',
                function ($attribute, $value, $fail) use ($data) {
                    if ($value && $data['status'] !== 'present') {
                        $fail(__t("You should remove the sign-out when the status of the attendance day is set to 'leave' or 'absent'"));
                    }
                },
                function ($attribute, $value, $fail) use ($data) {
                    if ($value && $data['leave_count']) {
                        $fail(__t("You should remove the sign-out, as you have already entered the leave count for the attendance day"));
                    }
                },
            ],
        ];
        $customValidationRules = array_merge($customValidationRules, $this->staffService::getImportingValidationRules());

        if (Carbon::hasFormat($data['date'], $defaultDateFormat)) {
            $customValidationRules['date'][]=
            function ($attribute, $value, $fail) use ($data, $defaultDateFormat)  {
                    $input_date = Carbon::createFromFormat($defaultDateFormat, $data['date']);
                    $exists = $this->repo->findWhere([
                        'staff_id'=> $data['staff_id'],
                        "date" => $input_date->format('Y-m-d')
                    ]);
                    if ($exists->count()) {
                        $fail(__t('You cannot import the attendance day in this row, as it is already included in an approved attendance sheet'));
                    }
                };
        }
        if($data['sign_out'] != ''){
            $time_sign_out_array = explode(" ",$data['sign_out']);
            $data['sign_out_time'] = $time_sign_out_array[sizeof($time_sign_out_array)-1];
            $customValidationRules['sign_out_time'] = 'date_format:H:i:s';
        }
        if($data['sign_in'] != ''){
            $time_sign_out_array = explode(" ",$data['sign_in']);
            $data['sign_in_time'] = $time_sign_out_array[sizeof($time_sign_out_array)-1];
            $customValidationRules['sign_in_time'] = 'date_format:H:i:s';
        }

        $messages['date.required']  =  __t("date is required") ;
        $messages['date.date_format'] = sprintf(__t("You Import Date With Invalid Format: right formate is ( %s )"),$defaultDateFormat);
        $messages['leave_type_id.exists'] = __t("You entered an inactive leave type");
        $messages['leave_type_id.required_if'] = __t("You should enter a valid leave type that is already added in the system");
        $messages['status.in'] = __t("You should enter one of the following statues “present , leave or absent“");
        $messages['sign_in.required_if'] =  __t("You cannot leave the sign-in empty as the status for the day is marked as present");
        $messages['sign_out.required_if'] = __t("You cannot leave the sign-out empty as the status for the day is marked as present");
        $messages['sign_out_time.date_format'] = __t("Invalid Sign-out Time Formate");
        $messages['sign_out.after_or_equal'] = __t("Sign out must be greater than Sign In");
        $messages['sign_in_time.date_format'] = __t("Invalid Sign-in Time Formate");
        $messages = array_merge($messages, $this->staffService::getImportingValidationMessages());



        $validator->setData($data);
        $validator->addRules($customValidationRules);

        $validator->setMessages($messages);

        if (!$validator->isValid()) {
            foreach ($validator->getErrors() as $key => $errors) {
                foreach ($errors as $error) {
                    $invalidData[$rowCount][] = sprintf(__t('%s in row #%s'), $error, $rowCount);
                }
            }
            return false;
        }
        return true;
    }

    public function preProcessDataForImportUsingExtraData($data, $extraData)
    {
        $staff = $this->staffRepository->model->where(['code'=>$data['staff_id']])->orWhere(['full_name'=>$data['staff_id']])->first();
        $data['staff_id'] = $staff ? $staff->id : null;
        $leave_type = $this->leaveTypeRepository->model->where(['name' => $data['leave_type_id']])->withTrashed()->first();
        $data['leave_type_id'] = $leave_type ? $leave_type->id : null;

        $data['sign_in']       = $data['sign_in'] ?  $data['date']." ". $data['sign_in'] : $data['sign_in'];
        $data['sign_out']      = $data['sign_out'] ?  $data['date']." ". $data['sign_out'] : $data['sign_out'];

        return $data;
    }

    public function beforeInsert($data)
    {
        $staffIds = [];
        foreach($data as $item) {
            $staffIds[] = $item['staff_id'];
        }

        $staffs = $this->staffRepository->model
            ->with(['staff_info.attendanceShift.shiftDays'])
            ->whereIn('id', $staffIds)
            ->get();

        foreach($data as $key => $item){

            $data[$key]['id'] = $item['staff_id'] .'--'. $item['date'];
            if($item['status'] != AttendanceDayStatusTypesUtil::PRESENT){
                $data[$key]['sign_in']  = null;
                $data[$key]['sign_out'] = null;
            }

            if($item['status'] == AttendanceDayStatusTypesUtil::LEAVE && !$item['leave_count']){
                $data[$key]['leave_count']  = 1;
            }

            $staff = $staffs->find($item['staff_id']);
            $staffAttendanceShift = $staff?->staff_info?->attendanceShift ?? null;
            $dayName = strtolower(Carbon::parse($item['date'])->dayName);
            $shiftDay = $staffAttendanceShift?->shiftDays?->where('weekday',$dayName)->first() ?? null;

            if($shiftDay){
                $data[$key]['on_duty']  = convertToUtc($item['date']." ".$shiftDay['from_time'])?? null;
                $data[$key]['off_duty'] = convertToUtc($item['date']." ".$shiftDay['to_time'])?? null;
                $data[$key]['shift_id'] = $staffAttendanceShift?->id ?? null;
            }else{
                $data[$key]['on_duty']  = $item['sign_in'] ?? null;
                $data[$key]['off_duty'] = $item['sign_out'] ?? null;
            }

        }
        return $data;
    }

}
