<?php

namespace App\Services;

use App\Entities\PayrunEmployeeData;
use App\Exceptions\ClosedPeriodException;
use App\Exceptions\EmployeesNotFoundInRuleCriteria;
use App\Exceptions\EntityNotFoundException;
use App\Exceptions\PayrunGenerationFailedException;
use App\Exceptions\PayrunNotFoundException;
use App\Facades\Branch;
use App\Facades\CakeSession;
use App\Facades\Formatter;
use App\Facades\Plugins;
use App\Facades\Staff;
use App\Helpers\FilterOperations;
use App\Helpers\Placeholder\PayslipPlaceholderHelper;
use App\Helpers\Placeholder\PlaceholderHelper;
use App\Jobs\PayrunCreationStarted;
use App\Models\Payrun;
use App\Models\Payslip;
use App\Repositories\AttendanceSheetRepository;
use App\Repositories\BranchRepository;
use App\Repositories\ClosedPeriodRepository;
use App\Repositories\ContractRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\EntityAppDataRepository;
use App\Repositories\EntityRepository;
use App\Repositories\JournalRepository;
use App\Repositories\PayRunRepository;
use App\Repositories\PaySlipRepository;
use App\Repositories\StaffRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Requests\DefaultRequest;
use App\Requests\Payroll\PayrunValidatorRequest;
use App\TokenGenerator\LaravelTokenGenerator;
use App\Utils\Accounting\JournalUtil;
use App\Validators\PayrollValidator\PayrunValidator;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PayrollFrequencyUtil;
use App\Utils\PayrunCriteriaUtil;
use App\Utils\PayrunStatusUtil;
use App\Utils\PaySlipStatusUtil;
use App\Utils\PluginUtil;
use App\Utils\SalaryComponentValueTypeUtil;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\Common\Utils\EntityAppDataKeysUtil;
use Izam\Daftra\Queue\Tokens\TokenData;

/**
 * PayRunService Class designation service deals with pay run repository @see PayRunRepository
 * @package App\Services
 * <AUTHOR> Faesal <<EMAIL>>, Hossam Mohammed <<EMAIL>>, Muhammad Reda <<EMAIL>>
 */
class PayRunService extends BaseService
{
    /**
     * @var string $showRouteName pay run show route name
     */
    protected $showRouteName = 'owner.payruns.show';

    /**
     * @var DepartmentRepository
     */
    private $departmentRepo;

    /**
     * @var DesignationRepository
     */
    private $designationRepo;

    /**
     * @var StaffRepository
     */
    private $staffRepository;

    /**
     * @var BranchRepository
     */
    private $branchRepo;
    /**
     * @var ContractRepository
     */
    private $contractRepo;

    /**
     * @var PaySlipService $paySlipService pay slip service
     */
    public $paySlipService;
    /**
     * @var PaySlipService
     */
    public $paySlipRepo;

    /**
     * @var JournalRepository
     */
    private $journalRepository;

    /**
     * {{@inheritDoc}}
     * PayRunService constructor.
     * @param PayRunRepository $repo pay run repository
     */
    public function __construct(
        PayRunRepository $repo,
        EntityRepository $entityRepo,
        StaffRepository $staffRepository,
        DepartmentRepository $departmentRepository,
        DesignationRepository $designationRepository,
        BranchRepository $branchRepository,
        PaySlipService $paySlipService,
        PaySlipRepository $paySlipRepository,
        ContractRepository $contractRepository,
        JournalRepository $journalRepository,
        protected AttendanceSheetRepository $attendanceSheetRepository,
    ) {
        $this->staffRepository = $staffRepository;
        $this->departmentRepo = $departmentRepository;
        $this->designationRepo = $designationRepository;
        $this->branchRepo = $branchRepository;
        $this->paySlipService = $paySlipService;
        $this->paySlipRepo = $paySlipRepository;
        $this->contractRepo = $contractRepository;
        $this->journalRepository = $journalRepository;
        parent::__construct($repo, $entityRepo);
    }

    public function getPayslips($id){
        return $this->repo->find($id)->paySlips->all(['id']);
    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['department_id'] = $record->department ? $record->department->name : '';
        return $data;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record) : array
    {
        $relations = parent::getActivityLogRelations($record);
        if ($record->department) {
            $relations[] = new ActivityLogRelationRequest($record->department->id, EntityKeyTypesUtil::DEPARTMENT_ENTITY_KEY);
        }
        return $relations;
    }

    /**
     * {@inheritDoc}
     */
    function getSortFields()
    {
        return [
            'fields' => [
                'name' => [
                    'title' => __t('Name'),
                    'direction' => 'DESC',
                    'field'=>'name',
                ],
                'created' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'created',
                    'direction' => 'DESC'
                ],

            ],
            'active' => [
                'field' => 'created',
                'direction' => 'DESC',
            ]
        ];
    }

    /**
     * {@inheritDoc}
     */
    function getFilters()
    {
        $staffList = [];
        $payrunList = $this->repo->list()->toArray() ?? [];
        if (isset($this->parameters['staff']) && !empty($this->parameters['staff'])) {
            $staffs = $this->staffRepository->findWhereIn('id', $this->parameters['staff']);
            foreach ($staffs as $staff)
                $staffList[] = Staff::getStaffOptionFormated($staff) ;
        }
	    $departmentList = $this->repo->departmentList();

        if (isset($this->parameters['name']) && !empty($this->parameters['name'])) {
            $payruns = $this->repo->filterList('id', $this->parameters['name']);
            foreach ($payruns as $payrun)
                $payrunList += [$payrun->id => $payrun->name];
        }

        $designationList = $this->repo->designationList();


        if (isset($this->parameters['start_from_date']) && !empty($this->parameters['end_to_date'])) {
            $fromDate = $this->parameters['start_from_date'];
            $toDate = $this->parameters['end_to_date'];
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['start_from_date'] = $toDate;
                $this->parameters['end_to_date'] = $fromDate;
            }
        }
        if (isset($this->parameters['posting_from_date']) && !empty($this->parameters['posting_to_date'])) {
            $fromDate = $this->parameters['posting_from_date'];
            $toDate = $this->parameters['posting_to_date'];
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['posting_from_date'] = $toDate;
                $this->parameters['posting_to_date'] = $fromDate;
            }
        }
        if (isset($this->parameters['creation_from_date']) && !empty($this->parameters['creation_to_date'])) {
            $fromDate = $this->parameters['creation_from_date'];
            $toDate = $this->parameters['creation_to_date'];
            $fromDateCarbon = Carbon::parse(Formatter::formatForDB($fromDate, 'date'));
            $toDateCarbon = Carbon::parse(Formatter::formatForDB($toDate, 'date'));
            if ($fromDateCarbon->gt($toDateCarbon)) {
                $this->parameters['creation_from_date'] = $toDate;
                $this->parameters['creation_to_date'] = $fromDate;
            }
        }

        $statuses = PayrunStatusUtil::getAll();
        foreach ($statuses as $key=>$status)
            $statuses[$key]= __t($status);

          $filters = [
              'payrun' =>  [
                  'param_name' => 'payrun',
                  'simple'=>true,
                  'label' => false,
                  'after' => '</div>',
                  'before' => '<div class="form-group">',
                  'attributes' => [
                      'placeholder' => __t('Select') . ' ' . __t('Pay Run'),
                      'id' => 'payrunSelectFilter',
                  ],
                  'div' => 'col-md-4',
                  'inputClass' => 'select-filter form-control',
                  'options' => $payrunList,
                  'type' => 'select',
                  'filter_options' => [
                      'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                      'field' => 'id',
                  ]

              ],
              'staff[]' => [
                  'simple' => true, /* displays the filter outside the toggle */
                  'after' => '</div>',
                  'param_name' => 'staff',
                  'before' => '<div class="form-group">',
                  'label' => __t('Employee'),
                  'inputClass' => 'custom-select form-control',
                  'attributes' => [
                      'placeholder' => __t(getEmployeesFieldPlaceholder()),
                      'id' => 'staffSelect',
                      'multiple' => true,
                      'data-staff-url' => route('owner.staff.search', ['allow_inactive' => true, 'get_branch_suspended' => 1])
                  ],
                  'div' => 'col-md-4',
                  'options' => $staffList,
                  'type' => 'selectStaff',
                  'filter_options' => [
                      'model'=>'staff',
                      'operation' => FilterOperations::IN_FILTER_OPERATION,
                      'field' => 'staff_id',
                  ]
              ],
              'department' => [
                  'param_name' => 'department',
                  'simple'=> true,
                  'label' => false,
                  'after' => '</div>',
                  'before' => '<div class="form-group">',
                  'attributes' => [
                      'placeholder' => __t('Select') . ' ' . __t('Department'),
                      'id' => 'departmentSelect',
                  ],
                  'div' => 'col-md-4',
                  'inputClass' => 'select-filter form-control',
                  'options' => $departmentList,
                  'type' => 'select',
                  'filter_options' => [
                      'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                      'field' => 'department_id',
                  ]

              ],
              'start_from_date' => [
                  'type' => 'date',
                  'before' => '<div class="form-group form-group-icon">',
                  'after' => '</div>',
                  'label' => false,
                  'div' => 'col-md-4',
                  'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                  'attributes' => ['placeholder' => __t('Period') . ' (' . __t('From') . ')'],
                  "filter_options" => [
                      "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                      "field" => "start_date"
                  ]
              ],
              'end_to_date' => [
                  'type' => 'date',
                  'before' => '<div class="form-group form-group-icon">',
                  'after' => '</div>',
                  'label' => false,
                  'div' => 'col-md-4',
                  'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                  'attributes' => ['placeholder' => __t('Period') . ' (' . __t('To') . ')'],
                  "filter_options" => [
                      "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                      "field" => "end_date"
                  ],
              ],
              'designation' => [
                  'param_name' => 'designation',
                  'simple'=> false,
                  'label' => false,
                  'after' => '</div>',
                  'before' => '<div class="form-group">',
                  'attributes' => [
                      'placeholder' => __t('Select') . ' ' . __t('Designation'),
                      'id' => 'designationSelect',
                  ],
                  'div' => 'col-md-4',
                  'inputClass' => 'select-filter form-control',
                  'options' => $designationList,
                  'type' => 'select',
                  'filter_options' => [
                      'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                      'field' => 'designation_id',
                  ]
              ],
              'posting_from_date' => [
                  'type' => 'date',
                  'before' => '<div class="form-group form-group-icon">',
                  'after' => '</div>',
                  'label' => false,
                  'div' => 'col-md-4',
                  'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                  'attributes' => ['placeholder' => __t('Posting Date') . ' (' . __t('From') . ')'],
                  "filter_options" => [
                      "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                      "field" => "posting_date"
                  ],
              ],
              'posting_to_date' => [
                  'type' => 'date',
                  'before' => '<div class="form-group form-group-icon">',
                  'after' => '</div>',
                  'label' => false,
                  'div' => 'col-md-4',
                  'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                  'attributes' => ['placeholder' => __t('Posting Date') . ' (' . __t('To') . ')'],
                  "filter_options" => [
                      "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                      "field" => "posting_date"
                  ],
               ],
              'status' => [
                  'simple' => false, /* displays the filter outside the toggle */
                  'label' => false,
                  'inputClass' => 'select-filter form-control',
                  'div' => 'col-md-4 form-group',
                  'attributes' => ['placeholder' => __t('All Statuses'), 'data-allow-clear' => 'true'],
                  'name' => 'status',
                  'type' => 'select',
                  'options' => $statuses,
                  'filter_options' => [
                      'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                      'field' => 'status'
                  ]
              ],
              'creation_from_date' => [
                  'type' => 'date',
                  'before' => '<div class="form-group form-group-icon">',
                  'after' => '</div>',
                  'label' => false,
                  'div' => 'col-md-4',
                  'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                  'attributes' => ['placeholder' => __t('Date of Creation') . ' (' . __t('From') . ')'],
                  "filter_options" => [
                      "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                      "field" => "created"
                  ],
              ],
              'creation_to_date' => [
                  'type' => 'date',
                  'before' => '<div class="form-group form-group-icon">',
                  'after' => '</div>',
                  'label' => false,
                  'div' => 'col-md-4',
                  'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                  'attributes' => ['placeholder' => __t('Date of Creation') . ' (' . __t('To') . ')'],
                  "filter_options" => [
                      "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                      "field" => "created"
                  ]
              ],
          ];
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin) && Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
                $branches = Branch::getStaffBranchesSuspended();
                if(count($branches) > 1){
                    $filters = $filters + [
                            'branches[]' => [
                                'param_name' => 'branches',
                                'simple'=>false,
                                'label' => false,
                                'after' => '</div>',
                                'before' => '<div class="form-group">',
                                'attributes' => [
                                    'placeholder' => __t('Select') . ' ' . __t('Branches'),
                                    'id' => 'branchSelect',
                                    'multiple' => 'multiple'
                                ],
                                'div' => 'col-md-4',
                                'inputClass' => 'select-filter form-control',
                                'options' => $branches,
                                'type' => 'select',
                                'filter_options' => [
                                    'operation' => FilterOperations::IN_FILTER_OPERATION,
                                    'field' => 'branch_id',
                                ]
                            ],
                        ];
                }
            }


            return $filters;
    }

    function filterName($query)
    {
        $conditions = ['OR' =>[
            ['field' => 'name', 'operation' => FilterOperations::IN_STRING_FILTER_OPERATION, 'value' => $query],
            ['field' => 'id', 'operation' => FilterOperations::IN_STRING_FILTER_OPERATION, 'value' => $query],
        ]
        ];
        $this->repo->pushCriteria(new CustomFind($conditions));
        $results = $this->repo->list(['id', 'name']);
        return $results;
    }

    public function getFormData($id = false)
    {
        $formData = parent::getFormData($id);
        $contractRepository = resolve(ContractRepository::class);
        $departmentRepository = resolve(DepartmentRepository::class);
        $designationRepository = resolve(DesignationRepository::class);
        $departments = $designations = [];
        $activeDepartments = $departmentRepository->getActiveDepartments()->toArray();
        $activeDesignations = $designationRepository->getActiveDesignations()->toArray();

        foreach($activeDepartments as $department)
            $departments[$department['id']] = $department['name'];

        foreach ($activeDesignations as $designation)
            $designations[$designation['id']] = $designation['name'];

        $formData['related_form_data']['currencies'] = $contractRepository->getUniqueCurrencies();
        $formData['related_form_data']['departments'] = $departments;
        $formData['related_form_data']['designations'] = $designations;
        $formData['related_form_data']['payroll_frequency'] = PayrollFrequencyUtil::getPayrollFrequency();

        if (old('employees') || $id) {
            $staffId = old('employees') ? old('employees') : $formData['form_record']->staff_id;
            $staffs = $this->staffRepository->find($staffId);
            if (!$staffs) {
                throw new EntityNotFoundException(__t("Employee"));
            }
            foreach ($staffs as $staff){
                $formData['staffOptions'][] = Staff::getStaffOptionFormated($staff) ;
            }
        }

        if(Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $authUser = getAuthOwner();

            if($authUser['staff_id'] != 0){
                $formData['related_form_data']['branches'] = Branch::getStaffBranches('list', $authUser['staff_id'])->toArray();
            }else{
                $activeBranches = resolve(BranchRepository::class)->getActiveBranches();
                $branches = [];

                foreach ($activeBranches as $branch)
                    $branches[$branch['id']] = $branch['name'];

                $formData['related_form_data']['branches'] = $branches;
                $formData['related_form_data']['is_all_branches'] = true;
            }
        }

        return $formData;
    }

    /**
     * format dates for database format
     * @param $request
     */

    public function formatData($request)
    {
        $request['start_date'] = Formatter::formatForDB($request['start_date'],EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['end_date'] = Formatter::formatForDB($request['end_date'],EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['posting_date'] =  Formatter::formatForDB($request['posting_date'],EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        $request['currency_code'] = $request['currency'];
        $request['branches'] = implode(',', $request['branches'] ?? []);
        $request['departments'] = implode(',', $request['departments'] ?? []);
        $request['designations'] = implode(',', $request['designations'] ?? []);
        $request['payroll_frequencies'] = implode(',', $request['payroll_frequencies'] ?? []);
        unset($request['currency']);
    }

    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $excludedEmployeeIds = null;
        $employee_ids = [];
        /** @var StaffService $staffService */
        $staffService = resolve(StaffService::class);
        $employee_ids = $staffService->getIdsByMultipleCriteriaSelection(
            $request->get('criteria'),
            $request->get('employees') ?? [],
            $request->get('branches') ?? [] ,
            $request->get('departments') ?? [] ,
            $request->get('designations') ?? [],
            $request->get('payroll_frequencies') ?? [] , $request->get('currency'),
            $request->get('exclude_criteria')??[]
        );

        if (empty($employee_ids)) {
            throw new EmployeesNotFoundInRuleCriteria();
        }
        $validateSheetFlag = (isset($request['validate_attendance']))? 1:0;
        $payrunValidatorRequest = new PayrunValidatorRequest(
            $request['posting_date'],
            $request['start_date'],
            $request['end_date'],
            $request['currency'],
            $request['criteria'],
            $employee_ids,
            $validateSheetFlag
        );
        $payrunValidator = new PayrunValidator($payrunValidatorRequest);
        $payrunValidator->setup($this->repo);
        $payrunValidator->validate();

        $this->formatData($request);
        $excludedEmployeeIds = $excludedEmployeeIds ? json_encode(['employees' => $excludedEmployeeIds]) : null;
        $request['exclude_criteria'] = $excludedEmployeeIds;
        $addedPayrun = parent::add($request, array_merge($excludedFields,['employees']), function($record) use($payrunValidatorRequest) {
            $tokenGenerator = new LaravelTokenGenerator(new TokenData());
            $token = $tokenGenerator->generate();
            $this->cacheBackgroundPayrun(['id' => $record->id, 'payrun' => $record, 'status' => PayrunStatusUtil::PROGRESS_STATUS_CREATED, 'validator_request' => $payrunValidatorRequest]);
            $fullVersion = explode('.', PHP_VERSION);
            $version = $fullVersion[0].'.'.$fullVersion[1];
            exec("nohup php{$version} /var/www/html/".LARAVEL_DIR."/artisan pay_run:create-payslips ".getCurrentSite("id")." {$record->id} $token > /dev/null 2>&1 & echo $!", $output, $status);
        });

        $employeeData = $payrunValidatorRequest->getEmployeeData();

        return [
            'addedPayrun' => $addedPayrun,
            'employeeIds' => is_array($employeeData) ? array_keys($employeeData) : $payrunValidatorRequest->getEmployeeIds(),
        ];
    }
    public static function getCachedBackgroundPayrunName($id){
        $siteId = getCurrentSite('id');
        return "{$siteId}_payrun_$id";
    }

    public function cacheBackgroundPayRun($data){
        $id = $data['id'];
        $oldData = Cache::get(self::getCachedBackgroundPayrunName($id), []);
        if(!$oldData) {
            $oldData = [];
        }
        if($data['status'] == PayrunStatusUtil::PROGRESS_STATUS_COMPLETED){
            Cache::forget(self::getCachedBackgroundPayrunName($id));
            return;
        }
        $newData = array_merge($oldData, $data);
        Cache::put(self::getCachedBackgroundPayrunName($id), $newData);
    }
    public function getCachedPayRun($id){
        return Cache::get(self::getCachedBackgroundPayrunName($id));
    }

    /**
     * {@inheritDoc}
     * @todo : Change deletion in for loop to be delete array of ids ya faesal
     */
    public function delete($id, Closure $callback = null)
    {
        $record = $this->repo->find($id);
        if(!$record) {
            throw new EntityNotFoundException($this->mainEntity->label);
        }

        if(!empty($cachedProgress) && $cachedProgress['status'] != PayrunStatusUtil::PROGRESS_STATUS_ERROR && $cachedProgress['status'] != PayrunStatusUtil::PROGRESS_STATUS_COMPLETED){
            throw new \Exception(__t('You cannot delete this payrun while it is still getting generated'));
        }

        $unGeneratedPaySlips = $record->paidCount + $record->approvedCount;
        if(ClosedPeriodRepository::getClosedPeriodsCountInDate($record->posting_date))
        {
            throw new ClosedPeriodException($record->posting_date);
        }
        if($unGeneratedPaySlips>0) {
            throw new \Exception(__t('You cannot delete this pay run as it contains approved or paid payslips'));
        } else {
            foreach ($record->paySlips as $paySlip) {
                $this->paySlipService->delete($paySlip->id);
            }
            return parent::delete($id, $callback);
        }
    }

    /**
     * @param $id
     * @return string
     * @throws PayrunNotFoundException
     */
    public function getCalculatedStatus($id)
    {
        $payrun = $this->find($id);
        if (is_null($payrun))
            throw new PayrunNotFoundException();

        $payslips_statuses = $payrun->payslips()->groupBy('status')->get(['status'])->toArray();
        $statuses = [];

        foreach ($payslips_statuses as $array) {
            $statuses[]=$array['status'];
        }

        if(array_search(PaySlipStatusUtil::GENERATED,$statuses) !== false) {
            return PaySlipStatusUtil::GENERATED;
        }

        if(array_search(PaySlipStatusUtil::APPROVED,$statuses) !== false) {
            return PaySlipStatusUtil::APPROVED;
        }

        if(array_search(PaySlipStatusUtil::PAID,$statuses) !== false) {
            return PaySlipStatusUtil::PAID;
        }

        return PaySlipStatusUtil::GENERATED;
    }

    public function updatePayslipCount($id){
        $count = $this->repo->payslipsCount($id);
        $request = new DefaultRequest();
        $request->payslips_count = $count;
        $result = $this->update($id,$request);
    }

    /**
     * @param $id
     * @param string $status
     * @return mixed
     */
    public function setStatus($id, string $status)
    {
        return $this->update($id, new DefaultRequest(['status' => $status]));
    }

    /**
     * @param array $employeeIds
     * @return array
     */
    public function getEmployeeDataByIds(array $employeeIds)
    {
        $this->staffRepository->pushCriteria(new CustomFind([
            ['field' => 'id', 'operation' => FilterOperations::IN_FILTER_OPERATION, 'value' => $employeeIds],
            ['field' => 'active', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => 1],
            ['field' => 'deleted_at', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => null]
        ]));
        $staffList = [];
        $staffAttributes = [];
        $staffObjects = $this->staffRepository->all();
        foreach ($staffObjects as $staff) {
            $staffList[] = Staff::getStaffOptionFormated($staff) ;;
        }
        $staffAutoFillData = [
            'staffOptionsAttributes' => $staffAttributes,
            'staffOptions' => $staffList,
        ];
        return $staffAutoFillData;
    }

    /**
     * @param array $employeeIds
     * @return array
     */
    public function getExcludedEmployeeDataByIds(array $employeeIds)
    {
        $this->staffRepository->pushCriteria(new CustomFind([
            ['field' => 'id', 'operation' => FilterOperations::IN_FILTER_OPERATION, 'value' => $employeeIds],
            ['field' => 'active', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => 1],
            ['field' => 'deleted_at', 'operation' => FilterOperations::EQUAL_FILTER_OPERATION, 'value' => null]
        ]));
        $staffList = [];
        $staffAttributes = [];
        $staffObjects = $this->staffRepository->all();
        foreach ($staffObjects as $staff) {
            $staffList[] = Staff::getStaffOptionFormated($staff) ;
        }
        $staffAutoFillData = [
            'excludedStaffOptionsAttributes' => $staffAttributes,
            'excludedStaffOptions' => $staffList,
        ];
        return $staffAutoFillData;
    }

    /**
     * @param $date
     * @return mixed
     */
    public function getInactiveClosedPeriodsCountWithinDate($date)
    {
        return $this->repo->getInactiveClosedPeriodsCountWithinDate($date);
    }

    public function generatePayslipData(Payrun $payrun, PayrunValidatorRequest $validatedRequest)
    {
        $payslipsCount = 0;
        foreach ($validatedRequest->getEmployeeData() as $key => $record) {
            /** @var PayrunEmployeeData $record **/
            $contract = $this->contractRepo->find($record->getOperatingContract()[0]['id']);

            $earningComponents = $contract->earningSalaryComponents;
            $deductionComponents = $contract->deductionSalaryComponents;

            $employee_id = $key;
            $employee = $this->staffRepository->find($employee_id);
            $designation = $contract->designation;
            $designationName = null;
            $designationId = null;
            if (isset($designation)){
                $designationName = $designation->name;
                $designationId = $designation->id;
            }

            $department = $employee->department->first();
            $departmentName = null;
            $departmentId = null;
            if (isset($department)){
                $departmentName = $department->name;
                $departmentId = $department->id;
            }

            $branchName = null;
            $branchId = null;
            if(Plugins::pluginActive(PluginUtil::BranchesPlugin))
            {
                $branch = $employee->branch;

                if (isset($branch)){

                    $branchName = $branch->name;
                    $branchId = $branch->id;
                }
            }

            $payslipService = resolve('App\Services\PaySlipService');
            $payslipData = $this->initPayslip(
                $payrun->posting_date,
                $employee_id,
                $payrun->start_date,
                $payrun->end_date,
                $contract->start_date,
                $contract->actual_end_date ?? $contract->end_date,
                $payrun->id,
                $contract->id,
                $designationName,
                $departmentName,
                $branchName,
                $branchId,
                $designationId,
                $departmentId
            );

            $payslipDefaultRequest = new DefaultRequest($payslipData);
            $callback = function ($payslip) use ($payslipService) {
                return $payslipService->automaticAssignInstallmentsToPayslip($payslip);
            };
            $payslip = $payslipService->addFromPayrun($payslipDefaultRequest,[],$callback);

            if ($validatedRequest->getValidateAttendance()) {
                $payslipSheetData = [];
                $payslipSheetData['attendance_sheet_id'] = $record->getAttendanceSheet()->id;
                $this->repo->updatePayslip($payslip->id, $payslipSheetData);
            }

            $totalEarning = 0;
            $totalDeduction = 0;
            $payslipCompArr = [];
            $placeholderService = resolve(PlaceholderService::class);
            PlaceholderHelper::resetMemoization();
            foreach ($earningComponents as $earnComp) {
                if ($earnComp->is_basic == 1){
                    $totalEarning += $earnComp->pivot->amount;

                    $payslipComp = $this->initPayslipComponent(
                        $payslip->id,
                        $earnComp->id,
                        $earnComp->pivot->amount,
                        $earnComp->pivot->formula,
                        $earnComp->pivot->order
                    );
                    $this->repo->insertPayslipComponent($payslipComp);
                    continue;
                }

                $payslipComp = $this->initPayslipComponent(
                    $payslip->id,
                    $earnComp->id,
                    $earnComp->pivot->amount ?? 0,
                    $earnComp->pivot->formula,
                    $earnComp->pivot->order
                );

                $addedComp_id = $this->repo->insertPayslipComponent($payslipComp);

                // Force generate amount for salary component to generate placeholders [by Ashraf for https://izam.atlassian.net/browse/DAFTRA-25380]
            //    if (!is_null($earnComp->pivot->formula)) {
            //        $amount = $placeholderService->evaluate($payslip->id, EntityKeyTypesUtil::PAYSLIP, $earnComp->pivot->formula);
            //        $this->repo->updatePayslipComponentAmount($payslipComp, $amount);
            //        PayslipPlaceholderHelper::$memoizationArray[EntityKeyTypesUtil::PAYSLIP] = null;
            //    }

                $condition = PlaceholderHelper::evaluate($earnComp->condition, EntityKeyTypesUtil::PAYSLIP, $payslip->id);
            //    Log::info('Earning Condition', ['ID' => $earnComp->id, 'Value' => $condition, 'Condition' => $earnComp->condition]);
                if ((!is_null($earnComp->condition) && $condition) || is_null($earnComp->condition)) {
                    if ($earnComp->pivot->value_type == SalaryComponentValueTypeUtil::AMOUNT_VALUE_TYPE) {
                        $totalEarning += $earnComp->pivot->amount;
                        $actualAmount = $earnComp->pivot->amount;
                    } else {
                        $formulaVal = $placeholderService->evaluate($payslip->id, EntityKeyTypesUtil::PAYSLIP, $earnComp->pivot->formula);
                    //    Log::info('Earning Formula', ['ID' => $earnComp->id, 'Value' => $formulaVal, 'Formula' => $earnComp->pivot->formula]);
                        $totalEarning += $formulaVal;
                        $actualAmount = $formulaVal;
                    }

                    $payslipCompArr[$addedComp_id] = [
                        'amount' => $actualAmount
                    ];
                }
            }

            foreach ($deductionComponents as $dedComp) {
                // component will run later .
                if($dedComp['is_basic'] == -1){ continue; }

                $payslipComp = $this->initPayslipComponent(
                    $payslip->id,
                    $dedComp->id,
                    0,
                    $dedComp->pivot->formula,
                    $dedComp->pivot->order
                );
                $addedComp_id = $this->repo->insertPayslipComponent($payslipComp);

                // Force generate amount for salary component to generate placeholders [by Ashraf for https://izam.atlassian.net/browse/DAFTRA-25380]
            //    if (!is_null($dedComp->pivot->formula)) {
            //        $amount = $placeholderService->evaluate($payslip->id, EntityKeyTypesUtil::PAYSLIP, $dedComp->pivot->formula);
            //        $this->repo->updatePayslipComponentAmount($payslipComp, $amount);
            //        PayslipPlaceholderHelper::$memoizationArray[EntityKeyTypesUtil::PAYSLIP] = null;
            //    }

                $condition = PlaceholderHelper::evaluate($dedComp->condition, EntityKeyTypesUtil::PAYSLIP, $payslip->id);
            //    Log::info('Deduction Condition', ['ID' => $dedComp->id, 'Value' => $condition, 'Condition' => $dedComp->condition]);
                if ((!is_null($dedComp->condition) && $condition) || is_null($dedComp->condition)) {
                    if ($dedComp->pivot->value_type == SalaryComponentValueTypeUtil::AMOUNT_VALUE_TYPE) {
                        $totalDeduction += $dedComp->pivot->amount;
                        $actualAmount = $dedComp->pivot->amount;
                    } else {
                        // Reset `PayslipPlaceholderHelper::$memoizationArray[EntityKeyTypesUtil::PAYSLIP]` to `null` before evaluating each deduction formula to avoid using stale values.  
                        // This ensures components are recalculated with the new values.
                        PayslipPlaceholderHelper::$memoizationArray[EntityKeyTypesUtil::PAYSLIP] = null; 
                        $formulaVal = $placeholderService->evaluate($payslip->id, EntityKeyTypesUtil::PAYSLIP, $dedComp->pivot->formula);
                    //    Log::info('Deduction Formula', ['ID' => $dedComp->id, 'Value' => $formulaVal, 'Formula' => $dedComp->pivot->formula]);
                        $totalDeduction += $formulaVal;
                        $actualAmount =      $formulaVal;
                    }

                    $payslipCompArr[$addedComp_id] = [
                        'amount' => $actualAmount
                    ];
                }
            }
            $loansDeductionComponents = $this->paySlipRepo->getLoansComponents($payslip->id);
            if(!empty($loansDeductionComponents)){
                foreach ($loansDeductionComponents as $component){
                    $totalDeduction += $component->amount ?? 0;
                }
            }
            $commissionComponent = $this->paySlipRepo->getCommissionComponent($payslip->id);
            if($commissionComponent) {
                $totalEarning += $commissionComponent->amount;
            }
            $payslipCalculatedData = [
                'gross_pay' => $totalEarning,
                'total_deduction' => $totalDeduction,
                'net_pay' => $totalEarning - $totalDeduction
            ];

            $this->repo->updatePayslip($payslip->id, $payslipCalculatedData);
            $this->repo->updatePayslipComponentsAmount($payslipCompArr);

            if (!is_null($record->getAttendanceSheet()))
                $this->repo->updateSheetStatusToApproved($record->getAttendanceSheet()->id);

            $payslipsCount++;
            $this->runLatestPayslipComponent($payslip , $deductionComponents , $payslipCalculatedData);
        }

        $this->repo->updatePayrunPayslipsCount($payrun->id, $payslipsCount);
    }

    public function progressStatus($id){
        $result = [];
        $payrun = $this->find($id);
        if(empty($payrun)){
            throw new PayrunNotFoundException();
        }
        $payslips = $payrun->paySlips()->with('staff')->get();
        foreach($payslips as $payslip){
            $result[$payslip->staff_id] = [
                'id' => $payslip->id,
                'message' => sprintf(__t('Payslip %s created successfully for Employee %s.'),
                    "<a href='/v2/owner/payslips/{$payslip->id}' target='_blank'>#{$payslip->id}</a>",
                    "<a href='/v2/owner/staff/{$payslip->staff_id}' target='_blank'>{$payslip->staff?->full_name} #{$payslip->staff?->code}</a>",
                ),
                'error' => false
            ];
        }
        $cachedStatus = $this->getCachedPayRun($id);
        if(
            !empty($cachedStatus)
            && $cachedStatus['status'] == PayrunStatusUtil::PROGRESS_STATUS_ERROR
        ) {
            $result = $this->getEmployeesFailedToGeneratePayslips($result, $cachedStatus['validator_request']);
        }
        return $result;
    }

    private function getEmployeesFailedToGeneratePayslips($generatedPayslipsMessages , PayrunValidatorRequest $payrunValidatorRequest){
        $error = new PayrunGenerationFailedException();
        /** @var PayrunEmployeeData $employeeData */
        foreach ($payrunValidatorRequest->getEmployeeData() as $key => $employeeData) {
            if(!isset($generatedPayslipsMessages[$key])){
                $empInfo = $employeeData->getEmployeeInfo();
                $staff = $this->staffRepository->find($empInfo['id']);
                $generatedPayslipsMessages[$key] = [
                    'message' => sprintf(__t('Failed to generate payslip for Employee %s.'),
                        "<a href='/v2/owner/staff/{$staff?->id}' target='_blank'>{$staff?->full_name} #{$staff?->code}</a>",
                    ),
                    'error' => $error->getMessage(),
                ];
            }
        }
        return $generatedPayslipsMessages;
    }


    public function runLatestPayslipComponent($payslip ,$deductionComponents , $payslipCalculatedData){


        $placeholderService = resolve(PlaceholderService::class);

        $placeholderService::removeCacheForEntityValue(EntityKeyTypesUtil::PAYSLIP, $payslip->id);

        $totalDeduction = 0;
        $payslipCompArr = [];

        foreach ($deductionComponents as $dedComp) {
            // component will run later .
            if($dedComp['is_basic'] != -1){ continue; }

            $payslipComp = $this->initPayslipComponent(
                $payslip->id,
                $dedComp->id,
                0,
                $dedComp->pivot->formula,
                $dedComp->pivot->order
            );

            // get old payslip component .
            $addedComp_id = $this->repo->insertPayslipComponent($payslipComp);


            $condition = PlaceholderHelper::evaluate($dedComp->condition, EntityKeyTypesUtil::PAYSLIP, $payslip->id);

            if ((!is_null($dedComp->condition) && $condition) || is_null($dedComp->condition)) {
                if ($dedComp->pivot->value_type == SalaryComponentValueTypeUtil::AMOUNT_VALUE_TYPE) {
                    $totalDeduction += $dedComp->pivot->amount;
                    $actualAmount = $dedComp->pivot->amount;
                } else {
                    $formulaVal = $placeholderService->evaluate($payslip->id, EntityKeyTypesUtil::PAYSLIP, $dedComp->pivot->formula);
                    $totalDeduction += $formulaVal;
                    $actualAmount =      $formulaVal;
                }

                $payslipCompArr[$addedComp_id] = [
                    'amount' => $actualAmount
                ];

            }
        }

        $payslipCalculatedData['total_deduction'] -=  $totalDeduction;
        $payslipCalculatedData['net_pay'] -=  $totalDeduction;

        $this->repo->updatePayslip($payslip->id, $payslipCalculatedData);
        $this->repo->updatePayslipComponentsAmount($payslipCompArr);

    }


    public function initPayslip($posting_date, $employee_id, $payrun_start_date, $payrun_end_date, $contract_start_date, $contract_end_date, $payrun_id, $contract_id, $designation_name = null, $department_name = null, $branch_name = null,$branch_id=null, $designation_id = null, $department_id = null)
    {
        $payrunStartDate = new Carbon($payrun_start_date);
        $payrunEndDate = new Carbon($payrun_end_date);
        $contractStartDate = new Carbon($contract_start_date);
        $contractEndDate = new Carbon($contract_end_date);

        $start_date = $payrunStartDate->format('Y-m-d');
        $end_date = $payrunEndDate->format('Y-m-d');
        $attendanceSheet = null;
        if (Plugins::pluginActive(\Izam\Daftra\Common\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN)) {
            $attendanceSheet = $this->attendanceSheetRepository
                ->pushCriteria(new EqualCriteria('staff_id', $employee_id))
                ->pushCriteria(new EqualCriteria('date_from', $payrun_start_date))
                ->pushCriteria(new EqualCriteria('date_to', $payrun_end_date))
                ->first();
        }

        if ($contractStartDate->gt($payrunStartDate) && empty($attendanceSheet)){
            $start_date = $contractStartDate->format('Y-m-d');
        } else {
            $start_date = $payrunStartDate->format('Y-m-d');
        }

        if ($contractEndDate->gt($payrunEndDate)) {
            $end_date = $payrunEndDate->format('Y-m-d');
        } else {
            $end_date = $contractEndDate->format('Y-m-d');
        }

        return [
            'posting_date' => $posting_date,
            'status' => PaySlipStatusUtil::GENERATED,
            'staff_id' => $employee_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'gross_pay' => 0,
            'total_deduction' => 0,
            'net_pay' => 0,
            'payrun_id' => $payrun_id,
            'designation_name' => $designation_name,
            'department_name' => $department_name,
            'branch_name' => $branch_name,
            'branch_id' => $branch_id,
            'contract_id' => $contract_id,
            'department_id' => $department_id,
            'designation_id' => $designation_id,
        ];
    }

    public function initPayslipComponent($payslip_id, $salary_component_id, $amount, $formula, $order)
    {
        return [
            'payslip_id' => $payslip_id,
            'salary_component_id' => $salary_component_id,
            'amount' => $amount,
            'formula' => $formula,
            'order' => $order
        ];
    }

    public function getViewData($id)
    {
        $payrun = $this->find($id);
        if (!$payrun) {
            throw new EntityNotFoundException($this->mainEntity->label);
        }
        $exportedIDs = $payrun->paySlips->pluck('id')->toArray();

        // get payrun journal
        $payrun_journal = $this->journalRepository->getAutoJournal([
            JournalUtil::AUTO_JOURNAL_TYPE_PAY_RUN
        ], $payrun->id);

        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('payrun');

        $mudadFileFinalized = false;
        if (ifPluginActive(PluginUtil::MUDAD_PLUGIN)){
            /** @var MudadService $mudadService */
            $entityAppDataRepo = resolve(EntityAppDataRepository::class);
            $entityAppData = $entityAppDataRepo->getDataByAppAndEntity(
                EntityAppDataKeysUtil::MUDDAD_SALARY_COMPONENTS,
                'modad_file_process',
                EntityKeyTypesUtil::PAY_RUN,
                $id
            );
            $decodeEntityAppData = json_decode($entityAppData->data ?? "", true);
            $mudadFileFinalized = isset($decodeEntityAppData['file_finalized']);
        }

        return array_merge(parent::getViewData($id), [
            'exportedIDs' => $exportedIDs,
            'payrun_journal' => $payrun_journal,
            'view_templates' => $viewTemplates,
            'mudadFileFinalized' => $mudadFileFinalized,
        ]);
    }

    public function hasSaudiEmployees($payrunId)
    {
        $payrun = $this->find($payrunId);
        if (!$payrun) {
            throw new EntityNotFoundException($this->mainEntity->label);
        }
        return in_array('SA', $payrun->staff->pluck('country_code')->toArray());
    }
}
