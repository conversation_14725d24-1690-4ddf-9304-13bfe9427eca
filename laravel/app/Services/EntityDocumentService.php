<?php

namespace App\Services;

use App\Repositories\EntityDocumentRepository;
use Carbon\Carbon;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;

class EntityDocumentService
{
    public function __construct(protected  EntityDocumentRepository $repo) {}


    public function calculateStatus($document)
    {
        // Default to Valid
        $status = EntityDocumentStatusUtil::VALID;

        // Check if the document is expirable
        $isExpirable = $document->entity_document_type->is_expirable ?? false;
        if ($isExpirable && empty($document->expiry_date)) {
            return EntityDocumentStatusUtil::EXPIRED;
        }
        if (!$isExpirable || empty($document->expiry_date)) {
            return $status;
        }

        $expiryDate = Carbon::parse($document->expiry_date)->startOfDay();
        $today = Carbon::today();
        $in30Days = $today->copy()->addDays(30);

        if ($expiryDate < $today) {
            $status = EntityDocumentStatusUtil::EXPIRED;
        } elseif ($expiryDate <= $in30Days) {
            $status = EntityDocumentStatusUtil::EXPIRING_SOON;
        }

        return $status;
    }

    public function getRequiredAndNotUploadedDocuments($entity_key = null, $entity_id = null){
        return $this->repo->getRequiredAndNotUploadedDocuments($entity_key, $entity_id);
    }

    function getDocumentAlertWarningmessage($entity_key = null, $entity_id = null){

        $missingDocuments = $this->repo->getRequiredAndNotUploadedDocuments($entity_key, $entity_id,true)->count();
        $expiringDocuments = $this->repo->getDocumentsExpiringWithinMonth($entity_key, $entity_id, true)->count();
        $expiredDocuments = $this->repo->getExpiredDocuments($entity_key, $entity_id, true)->count();

        if(!$missingDocuments && !$expiringDocuments){
            return null;

        }

        $missingPart = null;
        if ($missingDocuments == 1) {
            $missingPart = sprintf(__t("%s document is missing"), 1);
        } elseif ($missingDocuments > 1) {
            $missingPart = sprintf(__t("%s documents are missing"), $missingDocuments);
        }

        $expiringPart = null;
        if ($expiringDocuments == 1) {
            $expiringPart = sprintf(__t("%s document is expiring soon"), 1);
        } elseif ($expiringDocuments > 1) {
            $expiringPart = sprintf(__t("%s documents are expiring soon"), $expiringDocuments);
        }

        $expiredPart = null;
        if ($expiredDocuments == 1) {
            $expiredPart = sprintf(__t("%s document is expired"), 1);
        } elseif ($expiredDocuments > 1) {
            $expiredPart = sprintf(__t("%s documents are expired"), $expiredDocuments);
        }

        // Collect all non-null parts
        $parts = array_filter([$missingPart, $expiringPart, $expiredPart]);
        // Prepare message with proper commas and "and"
        $last = array_pop($parts);
        if ($parts) {
            $messageBody = implode(__t(',').' ', $parts) . __t(',').' ' . __t('and ') . $last;
        } else {
            $messageBody = $last;
        }
        $message = sprintf(__t("Attention: %s"), $messageBody);



        return $message;


    }

    public function getDocumentLatestIds(string $entity_key , string $entity_id)
    {
        return $this->repo->getLatestStaffDocumentsIds($entity_id);
    }
}
