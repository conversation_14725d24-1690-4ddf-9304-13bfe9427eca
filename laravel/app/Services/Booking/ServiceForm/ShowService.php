<?php

namespace App\Services\Booking\ServiceForm;

use App\Facades\Permissions;
use App\Services\DefaultShowService;
use App\Services\PaginationStackTrait;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Booking\Utils\ServiceFormUtil;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\ViewActions;
use Laminas\Form\Element\Button;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class ShowService extends DefaultShowService
{
    use PaginationStackTrait;

    const ENTITY_KEY = EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY;

    public function getActions($data)
    {
        $viewActions = new ViewActions('View-Action');

        if (!Permissions::checkPermission(PermissionUtil::MANAGE_BOOKING_SETTINGS)) {
            return $viewActions;
        }

        $editAnchor = new Anchor('edit-action');
        $editAnchor->setLabel(__t('Edit'))
            ->setAttribute('href', route('owner.entity.edit', [
                'entityKey' => self::ENTITY_KEY,
                'id' => $data->id
            ]))
            ->setOption('icon', 'pencil');

        $deleteBtn = new Button('delete-action');
        $deleteBtn
            ->setOption('message', sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Booking Package')))
            ->setOption('action', route('owner.entity.delete', [
                'entityKey' => self::ENTITY_KEY,
                'id' => $data->id
            ]))
            ->setOption('theme', 'delete')
            ->setLabel(__t('Delete'))
            ->setOption('icon', 'trash-can');

        $CustomDataAnchor = new Anchor('custom-data-action');
        $CustomDataAnchor->setLabel(__t('Custom Fields'))
            ->setAttribute('href', route('owner.local_entities.builder',
                [
                    'entityKey' => ServiceFormUtil::getAdditionalPrefix() . $data->id,
                    'redirect' => route('owner.entity.show', [self::ENTITY_KEY, $data->id]),
                    'related_entity' => self::ENTITY_KEY,
                ]
            ))
            ->setOption('icon', 'auto-fix');

        return $viewActions->add($editAnchor)
            ->add($deleteBtn)
            ->add($CustomDataAnchor);

    }
}
