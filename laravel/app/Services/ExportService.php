<?php

namespace App\Services;

use App\Facades\Formatter;
use App\Facades\Plugins;
use App\Models\EntityField;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\EntityRepository;
use App\Utils\CustomForm\CustomFormUtil;
use App\Utils\EntityKeyPluginMappingUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Barryvdh\Debugbar\Facades\Debugbar;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use Mockery\Exception;
use Rollbar\Payload\Level;
use Rollbar\Rollbar;

class ExportService extends BaseService {

    private $mappingService;

    public function __construct(
        EntityRepository $entityRepository,
        MappingService $mappingService,
        private AdditionalFieldsAwareService $additionalFieldsAwareService
    ) {
        $this->mappingService = $mappingService;
        parent::__construct($entityRepository, $entityRepository);
    }

    public function getFields($entity)
    {

        return $this->mappingService->getExportEntityFields($entity);
    }

    private function transformRelatedFieldsToWithRelation($entity, $relatedFields)
    {
        $withData = [];
        foreach ($relatedFields as $relatedEntityDBName => $relatedField) {
            //we define the main entity related to entities
            //so we can eager load it when we get the data
            if ($this->isAdditionalFieldsData($relatedEntityDBName)) {
                $withData[] = 'additionalFields';
            } elseif ($this->isCustomDataEntity($relatedEntityDBName)) {
                $entityTable = str_replace('_custom_data', '', $relatedEntityDBName);
                if(Str::singular($entityTable) === $entity->key)
                {
                    $withData[] = 'customData';
                }else{
                    $withData[] = $entityTable.'.customData';
                }

            }elseif (isset($relatedField['name']) &&  !empty($this->isMultipleDynamicDropdown($relatedField['name']))){
                $relationName = $this->isMultipleDynamicDropdown($relatedField['name'])->first()->reference_entity_key;
                $colName = $this->mappingService->getEntityByKey($relationName)->name_field;
                $withData[] = $relatedEntityDBName . ':' . $colName;
            }else{
                if(!empty($entity->extend)){
                    $relatedEntity = $this->mappingService->getEntityByKey($entity->extend);
                } else {
                    $relatedEntity = $this->mappingService->getEntityByDBName($relatedEntityDBName);
                }
                $withData[] = $relatedEntityDBName . ':' . $relatedEntity->primary_key . ',' . implode(',',$relatedField);
            }
        }
        return $withData;
    }


    public function getExtraSelectedFields($entity)
    {
         switch ($entity) {
             case EntityKeyTypesUtil::PRICE_LIST_ITEMS:
                 $ret = [];
                 $priceListItems = $this->getEntities($entity)["price_list_items"]["fields"];
                 foreach ($priceListItems as $item) {
                     $ret['product_prices.'.$item->db_name] = $item->db_name;
                 }
                 return $ret;
             case EntityKeyTypesUtil::PAYSLIP:
                 return [
                     'payslips.staff_id' => 'staff_id',
                     'payslips.start_date' => 'start_date',
                     'payslips.end_date' => 'end_date',
                 ];
            case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
                return [
                    'staffs.official_id_type' => 'official_id_type'
                ];
             default:
                 return [];
        }
    }
    public function prepareData($entity, $selectedFields, $exportedIDs = [])
    {
        $entity = $this->mappingService->getEntityByKey($entity);

        $repoClass = 'App\Repositories\\'.$entity->repo_name;
        $serviceClass = 'App\Services\\'.$entity->service_name;
        if(!class_exists($repoClass)) {
            $repoClass = $entity->repo_name;
        }
        if(!class_exists($serviceClass)) {
            $serviceClass = $entity->service_name;
        }
        $repo = app()->make($repoClass);
        $service = app()->make($serviceClass);
        $fields = array_keys($selectedFields);
        $findFields = [];
        $relatedFields = [];
        $entityFields = $this->getFields($entity->key);
        foreach ($fields as $field) {
            if (strpos($field,'.') !== false) {
                $fieldParams = explode('.',$field);
                if(!empty($entity->extend)){
                   $fieldParams[0] = $entity->db_name;
                }
                $entityField = $entityFields->where('key', $field)->first();
                if (!empty($entityField) && EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN == $entityField->field_type) {
                    $relatedFields[$fieldParams[1]][] = $fieldParams[0];
                    $relatedFields[$fieldParams[1]]['type'] = EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN;
                    $relatedFields[$fieldParams[1]]['name'] = $field;
                    $entityKey = $this->mappingService->getRelationsByKey([$entityField->key])->first()->reference_entity_key;
                    $relatedFields[$fieldParams[1]]['colName'] = $this->mappingService->getEntityByKey($entityKey)->name_field;
                } elseif ($fieldParams[0] == $entity->db_name) { 
                    $findFields[] = $fieldParams[1];
                }else{
                    $relatedFields[$fieldParams[0]][] = $fieldParams[1];
                }
            }
        }
        $withData = $this->transformRelatedFieldsToWithRelation($entity, $relatedFields);
        $conditions = $service->listingHelper->getListingConditions();
        $conditions = array_merge($conditions, $service->getListingConditions());
        $exportDataCollection = null;
        if(!empty($exportedIDs)){
            $exportDataCollection = $repo->pushCriteria(new CustomFind($conditions))->findWhereIn('id', $exportedIDs, $withData ? ['*'] : $findFields);
        } else {
            $exportDataCollection = $repo->pushCriteria(new CustomFind($conditions))->findWhere([["id", "!=", 0]], $withData ? ['*'] : $findFields);
        }
        foreach ($exportDataCollection as &$item) {

            if(method_exists($service , 'beforeExport')){
                $item = $service->beforeExport($item);
            }

            $item->setRawAttributes(array_replace(array_flip($findFields), $item->getAttributes()), true);
            foreach ($relatedFields as $relatedEntityKey => $relatedEntityFields) {
                if ($this->isAdditionalFieldsData($relatedEntityKey)) {
                    $additionalFieldsData = $this->additionalFieldsAwareService->getAdditionalFieldsValues(str_replace('le_custom_data_', '', $relatedEntityKey), $item->id);
                }
                if (isset($relatedEntityFields['name']) &&  !empty($this->isMultipleDynamicDropdown($relatedEntityFields['name']))) {
                    $item->{$relatedEntityFields['name']} = $item->{$relatedEntityKey} ? implode(',', $item->{$relatedEntityKey}->pluck($relatedEntityFields['colName'])->toArray()) : null;
                    continue;
                }
                foreach ($relatedEntityFields as $relatedEntityField) {
                    $attributeName = $relatedEntityKey . "." . $relatedEntityField;
                    //here we get all entities that is related to the main entity
                    //and assign its values to the main entity as attribute
                    if ($this->isAdditionalFieldsData($relatedEntityKey)) {
                        $item->{$attributeName} = $item->additionalFields ? $additionalFieldsData[$relatedEntityField] : null;
                    } elseif ($this->isCustomDataEntity($relatedEntityKey)) { // check if the entity is custom data entity
                        if(CustomFormUtil::getCustomFormDataTableEntityName($relatedEntityKey) === $entity->key)
                        {
                            //if the custom data entity related directly to the main entity we export
                            //then we can access the custom data directly as any relation
                            $item->{$attributeName} = $item->customData ? $item->customData->{$relatedEntityField} : null;
                        }else{
                            //if the custom data entity related to the main entity through other entity
                            //then we can access the custom data through the entity related to the main entity
                            $customDataEntityTable = CustomFormUtil::getEntityTableNameFromCustomDataTable($relatedEntityKey);
                            $item->{$attributeName} = $item->{$customDataEntityTable} ? ($item->{$customDataEntityTable}->customData ? $item->{$customDataEntityTable}->customData->{$relatedEntityField} : null) : null;
                        }
                    } else {
                        //related entity is normal relations
                        $item->{$attributeName} = $item->{$relatedEntityKey} ? $item->{$relatedEntityKey}->{$relatedEntityField} : null;

                    }
                }
            }
            $item->setRelations([]);
        }

        $exportDataCollection->transform(function ($result) use ($findFields, $relatedFields) {
            $attributes = $result->getAttributes();
            foreach ($attributes as $index => $attribute) {
                $arrayToCheck = $findFields;
                $valueToCheck = $index;
                if (strpos($index, '.') !== false) {
                    $explodedIndex = explode('.', $index);
                    $entityToCheck = $explodedIndex[0];
                    $arrayToCheck = $relatedFields[$entityToCheck] ?? [];
                    $valueToCheck = $explodedIndex[1];
                }
                if (!in_array($valueToCheck, $arrayToCheck) && !in_array($valueToCheck, array_keys($relatedFields)))
                    unset($attributes[$index]);
            }
            $result->setRawAttributes($attributes, true);
            return $result;
        });

        if (method_exists($service, 'after_prepare_data')) {
            return $service->after_prepare_data($exportDataCollection);
        }
        return $exportDataCollection->toArray();
    }
    public function getEntitiesForExport($entity)
    {
        $entityObject = $this->mappingService->getEntityByKey($entity);
        $exportedServiceClass = 'App\Services\\' . $entityObject->service_name;
        if(!class_exists($exportedServiceClass)) {
            $exportedServiceClass = $entityObject->service_name;
        }
        if(class_exists($exportedServiceClass)) {
            $exportedService = App::make($exportedServiceClass);
            if (method_exists($exportedService, 'getEntitiesForExport')) {
                $entities = $exportedService->getEntitiesForExport($entity);
                return $entities;
            }
        }
        return $this->getEntities($entity);
    }

    public function getEntities($entity)
    {

        $fields = $this->getFields($entity);
        $fields =  $fields->where('on_export', 1);;
        if (isset($request['for_sent_entity_fields_only']) && $request['for_sent_entity_fields_only']) {
            $fields = $fields->where('entity', $entity);
        }
        $entities = [];
        foreach ($fields as $field) {
            if (!array_key_exists($field->entity, $entities)) {
                $entities[$field->entity]['label'] = $this->getEntityLabel($field->entity)->label;
                $entities[$field->entity]['db_name'] = $field->entity;
            }
            $entities[$field->entity]['fields'][] = $field;
        }
        return $entities;
    }

    private function isAdditionalFieldsData($entityName)
    {
        return strpos($entityName, 'le_custom_data') !== false;
    }
    private function isMultipleDynamicDropdown($fieldKey)
    {
        return $this->mappingService->getRelationsByKey([$fieldKey]);
    }
    function isCustomDataEntity($entityName)
    {
        return strpos($entityName, 'custom_data') !== false;
    }

    function getEntityLabel($entity_db_name){
        return $this->entityRepository->find($entity_db_name);
    }

    public function prepareCSV($entity, $dataArr, $labels=[])
    {
        $entityObject = $this->mappingService->getEntityByKey($entity);
        $className = 'App\Services\\' . $entityObject->service_name;
        if(!class_exists($className)) {
            $className = $entityObject->service_name;
        }
        $exportedService = App::make($className);

        if (method_exists($exportedService, 'prepareCSV')) {
            $dataArr = $exportedService->prepareCSV($dataArr);
        }
        $entity_name = preg_replace('/\s+/', '_', $entityObject->label);
        $date = date("Y-m-d_H-i-s");
        $filename = "$entity_name-exported-$date.csv";
        $handle = fopen('/tmp/'.$filename, 'w+');
        fprintf($handle, chr(0xEF).chr(0xBB).chr(0xBF)); //header utf-8

        fputcsv($handle,$labels);
        foreach ($dataArr as $key => $value) { //adding "\r" at the end of each field to force it as text
            foreach ($value as &$val) {
                if (is_array($val)) {
                    if (isset($val['text'])) {
                        $val = $val['text'];
                    } elseif(isset($val['number']) && isset($val['currency'])) {
                        $val = !empty($val['number']) ? $val['number'] . ' ' . $val['currency'] : '';
                    } else {
                        $val = implode(',', $val);
                    }

                }
            }
            fputcsv($handle, $value);
        }

        fclose($handle);

        $headers = [
            'Cache-Control'       => 'must-revalidate, post-check=0, pre-check=0',
            'Content-type'        => 'text/csv',
            'Content-Disposition' => 'attachment; filename=$filename',
            'Expires'             => '0',
            'Pragma'              => 'public'
        ];

        return Response::download("/tmp/$filename", $filename, $headers)->deleteFileAfterSend();
    }

    public function getRelations($data)
    {
        $keys = array_keys($data);

        $relations = $this->mappingService->getRelationsByKey($keys);

        $arr = [];
        foreach ($relations as $rel) {
            if(($i = array_search($rel->field_key, $keys)) !== false){
                array_push($arr, [
                    'index' => $i,
                    'entity' => $rel->reference_entity_key,
                    'fk' => $rel->entity ? $rel->field_key : explode('.', $rel->field_key)[1],
                    'conditions' => !is_null($rel->conditions) ? json_decode($rel->conditions, true) : null
                ]);
            }
        }
        return $arr;
    }

    public function injectRelationData($dataArr, $relations)
    {
        $newData = [];
        $entities = [];
        $repos = [];
        $relatedData = [];

        $plugins = EntityKeyPluginMappingUtil::getAll();
        foreach ($dataArr as &$record) {
            foreach ($relations as $rel) {
                $entityKey = $rel['entity'];
                if (isset($plugins[$entityKey])) {
                    if (!Plugins::pluginActive($plugins[$entityKey])) {
                        continue;
                    }
                }
                $entity = $entities[$rel['entity']] ?? $entities[$rel['entity']] = $this->mappingService->getEntityByKey($rel['entity']);
                if (empty($entity->repo_name)) continue; // skip fields with no repo to avoid crashes

                $className =('App\Repositories\\' . $entity->repo_name);
                if(!class_exists($className)) {
                    $className = $entity->repo_name;
                }
                $repo = $repos[$entity->repo_name] ?? $repos[$entity->repo_name] = app()->make($className);
                $index = $rel['index'];
                $keys = array_keys($record);
                $fk_value = $record[$keys[$index]];
                $pk = $entity->primary_key;
                $col_name = $entity->name_field;
                if ($fk_value) {
                    if (isset($relatedData[$rel['entity']][$fk_value])) {
                        $record[$keys[$index]] = $relatedData[$rel['entity']][$fk_value];
                        continue;
                    }
                    $conditions = [['field' => $pk, 'operation' => 'in', 'value' => [$fk_value]]];
                    if (!empty($rel['conditions'])) {
                        foreach ($rel['conditions'] as $key => $condition) {
                            foreach($condition as $val) {
                                $conditions[] = ['field' => $key, 'operation' => $val['operator'], 'value' => $val['value']];
                            }
                        }
                    }
                    $obj = $repo->getByCriteria(new \App\Repositories\Criteria\CustomFind($conditions))->first();
                    if (!is_null($obj)) {
                        $record[$keys[$index]] = $obj->{$col_name};
                        $relatedData[$rel['entity']][$fk_value] = $obj->{$col_name};
                    }
                }
            }
            $newData[] = $record;
        }
        return $newData;
    }

    public function getFieldsLabels($entity, $data)
    {
        $labels = [];
        $fields = $this->mappingService->getExportEntityFields($entity);
        $keys = array_keys($data);
        foreach ($fields as $value) {
            if (in_array($value->key, $keys)){
                array_push($labels, __t($value->label));
            }
        }

        $additionalFields = $this->additionalFieldsAwareService->getImportFields($entity);
        foreach ($additionalFields as $value) {
            if (in_array($value->getKey(), $keys)){
                array_push($labels, __t($value->getLabel()));
            }
        }
        $entity = $this->mappingService->getEntityByKey($entity);
        $serviceClass = 'App\Services\\'.$entity->service_name;
        if(!class_exists($serviceClass)) {
            $serviceClass = $entity->service_name;
        }
        $service = app()->make($serviceClass);

        if (method_exists($service, 'getLabels')) {
            return $service->getLabels($data, $labels);
        }
        return $labels;
    }

    public function formatData($data, $entity)
    {
        $fields = $this->getFields($entity);
        $fieldsInfo = $fields->mapWithKeys(function ($fieldInfo) {
            return ["{$fieldInfo->key}" => $fieldInfo];
        });
        foreach ($fieldsInfo as $key => $value){
            if(strpos($key, Str::plural($entity)) !== false){
                $field = explode('.', $key)[1];
                unset($fieldsInfo[$key]);
                $fieldsInfo[$field] = $value;
            }
        }
        foreach ($data as &$row) {
            foreach ($row as $field => &$value) {
                if (isset($fieldsInfo[$field])) {
                    $entityField = ($fieldsInfo[$field] instanceof EntityField) ? $fieldsInfo[$field] : null;
                    $value = Formatter::formatForView($value, $fieldsInfo[$field]->field_type, $entityField, ['source' => self::class]);
                }
            }
        }
        return $data;
    }

    public function getRelationsIndex($relations,$data)
    {
        $keys = array_keys($data);
        foreach ($relations as &$rel) {
            if(($i = array_search($rel['fk'], $keys)) !== false){
                $rel['index']=$i;
            }
        }
        return $relations;
    }
}
