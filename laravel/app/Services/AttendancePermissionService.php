<?php

namespace App\Services;

use App\Exceptions\AttendancePermissions\CanNotAddAttendancePermissionInApprovedAttendanceSheet;
use App\Exceptions\AttendancePermissions\CanNotEditOrDeleteAttendancePermissionInApprovedAttendanceSheet;
use App\Exceptions\AttendancePermissions\CanNotEditStaffFromLeaveApplication;
use App\Exceptions\AttendancePermissions\CanNotEditLeaveTypeFromLeaveApplication;
use App\Exceptions\AttendancePermissions\CanNotEditDateFromLeaveApplication;
use App\Exceptions\EntityNotFoundException;
use App\Facades\Formatter;
use App\Facades\Plugins;
use App\Helpers\FilterOperations;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Repositories\AttendancePermissionRepository;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\DepartmentRepository;
use App\Repositories\DesignationRepository;
use App\Repositories\EntityRepository;
use App\Repositories\LeaveApplicationRepository;
use App\Repositories\LeaveTypeRepository;
use App\Repositories\ShiftRepository;
use App\Repositories\StaffRepository;
use App\Requests\ActivityLog\ActivityLogRelationRequest;
use App\Services\AttendanceCalculator\Entities\AttendancePermission;
use App\Utils\AttendancePermissionTypesUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use Closure;
use Illuminate\Support\Collection;
use Izam\Daftra\Common\Utils\PluginUtil;
use Illuminate\Support\Facades\DB;
use App\Exceptions\NotAccessibleStaffBranchException;
use App\Facades\Branch;
use App\Facades\Staff;

/**
 * AttendancePermissionService Class attendance permission service
 * @package App\Services
 * <AUTHOR> Faesal <<EMAIL>>
 */
class AttendancePermissionService extends BaseService
{

    var $filters = [];

    /** @var AttendancePermissionRepository */
    var $repo;
    protected $showRouteName = 'owner.attendance_permissions.show';
    /**
     * @var StaffRepository
     */
    private $staffRepository;
    /**
     * @var LeaveTypeRepository
     */
    private $leaveTypeRepository;

    private $leaveApplicationRepository;
    /**
     * AttendancePermissionService constructor.
     * @param AttendancePermissionRepository $repo
     * @param StaffRepository $staffRepository
     * @param LeaveTypeRepository $leaveTypeRepository
     * @param EntityRepository|null $entityRepo
     */
    public function __construct(
        AttendancePermissionRepository $repo,
        StaffRepository $staffRepository,
        LeaveTypeRepository $leaveTypeRepository,
        EntityRepository $entityRepo = null,
        LeaveApplicationRepository $leaveApplicationRepository,
        private DepartmentRepository $deptRepo,
        private DesignationRepository $designationRepo,
        private ShiftRepository $shiftRepo,
    )
    {
        $this->staffRepository = $staffRepository;
        $this->leaveTypeRepository = $leaveTypeRepository;
        $this->leaveApplicationRepository = $leaveApplicationRepository;

        parent::__construct($repo, $entityRepo);
    }

    public function getFormData($id = false)
    {
        $data = parent::getFormData($id);

        $staffService = resolve(StaffService::class);
        if($data['form_record'] && !$staffService->isAccessableStaffId($data['form_record']->staff_id, false)){
            throw new NotAccessibleStaffBranchException;
        }

        if (old('staff_id') || $id) {
            $staffId = old('staff_id') ? old('staff_id') : $data['form_record']->staff_id;
            $staff = $this->staffRepository->find($staffId);
            if (!$staff) {
                throw new EntityNotFoundException(__t("Employee"));
            }
            $data['staffOptions'] = [
                Staff::getStaffOptionFormated($staff)
            ];
        }
        return $data;
    }


    /**
     * {{@inheritDoc}}
     */
    public function add($request, array $excludedFields = [], Closure $callback = null)
    {
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($request['staff_id'], false)){
            throw new NotAccessibleStaffBranchException;
        }

        $request['from_date'] = Formatter::formatForDB($request['from_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);

        if (isset($request['to_date']) && $request['to_date']) {
            $request['to_date'] = Formatter::formatForDB($request['to_date'], EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
        } else {
            $request['to_date'] = $request['from_date'];
        }

        if ($attendanceSheet = $this->repo->isDateIncludedInApprovedAttendanceSheet($request["staff_id"], $request["from_date"], $request["to_date"])) {
            throw new CanNotAddAttendancePermissionInApprovedAttendanceSheet($attendanceSheet["id"], $attendanceSheet["auto_id"]);
        }


        if (isset($request['application_date'])) {
            $request['application_date'] = Formatter::formatForDB($request['application_date'], 'date');
        }
        $request['leave_type_id'] = isset($request['leave_type_id']) ? $request['leave_type_id'] : null;
        $request['late_time'] = isset($request['late_time']) ? $request['late_time'] : null;
        $request['early_time'] = isset($request['early_time']) ? $request['early_time'] : null;
        return parent::add($request, $excludedFields);
    }

    /**
     * {{@inheritDoc}}
     */
    public function update($id, $request, array $excludedFields = [], Closure $callback = null)
    {
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($request['staff_id'], false)){
            throw new NotAccessibleStaffBranchException;
        }

        $request['from_date'] = Formatter::formatForDB($request['from_date'], 'date');
        if (isset($request['application_date'])) {
            $request['application_date'] = Formatter::formatForDB($request['application_date'], 'date');
        }
        if (isset($request['to_date']) && $request['to_date']) {
            $request['to_date'] = Formatter::formatForDB($request['to_date'], 'date');
        } else {
            $request['to_date'] = $request['from_date'];
        }

        $request['leave_type_id'] = isset($request['leave_type_id']) ? $request['leave_type_id'] : null;
        $request['late_time'] = isset($request['late_time']) ? $request['late_time'] : null;
        $request['early_time'] = isset($request['early_time']) ? $request['early_time'] : null;



        $leave_application = DB::connection("currentSite")
                    ->table("leave_applications")
                    ->where("attendance_permission_id", $id)
                    ->first();


        if (isset($leave_application)) {
            $attPerm = $this->repo->find($id);
            if(!$staffService->isAccessableStaffId($attPerm->staff_id, true)){
                throw new NotAccessibleStaffBranchException;
            }
            if(
                $attPerm->from_date != $request['from_date']
                || $attPerm->to_date != $request['to_date']
            ){
                throw new CanNotEditDateFromLeaveApplication($leave_application->id);
            }
            if(
                $attPerm->leave_type_id != $request['leave_type_id']
                || $attPerm->type != $request['type']
            ){
                throw new CanNotEditLeaveTypeFromLeaveApplication($leave_application->id);

            }
            if($attPerm->staff_id != $request['staff_id']){

                throw new CanNotEditStaffFromLeaveApplication($leave_application->id);
            }
        }


        $this->throwUsedAttendancePermissionCannotBeUpdatedOrDeleted($id);

        return parent::update($id, $request, $excludedFields);
    }

    /**
     * @param $id
     * @throws CanNotEditOrDeleteAttendancePermissionInApprovedAttendanceSheet
     */
    public function throwUsedAttendancePermissionCannotBeUpdatedOrDeleted($id){
        $attendancePermission = $this->repo->find($id);
        if (count($attendancePermission->attendanceDays) > 0) {
            throw new CanNotEditOrDeleteAttendancePermissionInApprovedAttendanceSheet();
        }

    }

    public function setLeaveApplicationStatusToPending($id) {

        $leaveApplication = $this->leaveApplicationRepository->getLeaveApplicationByAttendancePermission($id);
        $this->logDelete($this->find($id));
        $recordData = [];
        if($leaveApplication){
            $appEntityShowAction = resolve(AppEntityShowAction::class);
            $entityKey = 'leave_application';
            $recordData =  $appEntityShowAction->handle($entityKey, $leaveApplication->id, 4);
            $recordData =  toArray($recordData);
            $affected =  $this->leaveApplicationRepository->update($leaveApplication->id, ["status" => "pending", "attendance_permission_id" => null, "approved_by" => null, "comment" => null]);

            if ($affected) {
                return $recordData;
            }
        }

        return false;
    }

    /**
     * {@inheritDoc}
     */
    public function delete($id, Closure $callback = null)
    {
        $record = $this->repo->find($id);
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($record->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }
        $this->throwUsedAttendancePermissionCannotBeUpdatedOrDeleted($id);
        parent::delete($id);
    }

    /**
     * get form related data
     * @return array
     */
    public function getFormRelatedData()
    {
        return [
            'leave_type_list' => AttendancePermissionTypesUtil::getOptions(),
            'attendance_permission_staff_relations' => $this->staffRepository->getStaffsFullName([new CustomFind([['field' => 'active', 'value' => 1]])]),
            'attendance_permission_leave_type_relations' => $this->leaveTypeRepository->list(['id', 'name'])
        ];
    }

    function getFilters()
    {
        $staffList = [];
        if (isset($this->parameters['name']) && !empty($this->parameters['name'])) {
            $staff = $this->staffRepository->find($this->parameters['name']);
            if ($staff) {
                $staffList[] = Staff::getStaffOptionFormated($staff) ;;
            }
        }
        if (!empty($this->filters)) {
            return $this->filters;
        } else {
            if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
                $depts = $this->deptRepo->list(['id', 'name']);
                $designations = $this->designationRepo->list(['id', 'name']);
                $shifts = $this->shiftRepo->list(['id', 'name']);
            }
            $leaveTypes = $this->leaveTypeRepository->list(["id", "name"])->toArray();
            $this->filters = [
                'name' => [
                    'param_name' => 'name',
                    'simple' => true, /* displays the filter outside the toggle */
                    'after' => '<i class="input-icon fal fa-search"></i></div>',
                    'before' => '<div class="form-group-icon form-group">',
                    'label' => __t('Employee'),
                    'attributes' => [
                        'placeholder' => __t(getEmployeesFieldPlaceholder()),
                        'id' => 'staffSelect',
                        'multiple' => 'multiple'
                    ],
                    'div' => 'col-md-6',
                    'options' => $staffList,
                    'type' => 'select',
                    "filter_options" => [
                        "operation" => FilterOperations::IN_FILTER_OPERATION,
                        "field" => "staff_id"
                    ],
                ],
                'leave_or_delay' => [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-3',
                    'options' => AttendancePermissionTypesUtil::getOptions(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('All Types'),
                        'id' => 'leave_or_delay'
                    ],
                    'filter_options' => [
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'type'
                    ]
                ],
            ];
            if ($leaveTypes) {
                $this->filters['leave_type_id'] = [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => false,
                    'div' => '',
                    'after' => '</div>',
                    'before' => '<div class="col-md-3 form-group">',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('All Leave Types'),
                        'id' => 'leave_type_id'
                    ],
                    "type" => "select",
                    "filter_options" => ["operation" => FilterOperations::EQUAL_FILTER_OPERATION, "field" => "leave_type_id"],
                    "options" => $leaveTypes
                ];
            }
            $this->filters['from_date'] = [
                'simple' => true, /* displays the filter outside the toggle */
                'type' => 'date',
                'div' => '',
                'after' => '</div>',
                'before' => '<div class="col-md-3 form-group form-group-icon">',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'label' => false,
                'attributes' => ['placeholder' => __t('From Date')],
                "filter_options" => [
                    "operation" => FilterOperations::BIGGER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "from_date"
                ]
            ];
            $this->filters['to_date'] = [
                'simple' => true, /* displays the filter outside the toggle */
                'type' => 'date',
                'div' => '',
                'after' => '</div>',
                'before' => '<div class="col-md-3 form-group form-group-icon">',
                'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                'label' => false,
                'attributes' => ['placeholder' => __t('To Date')],
                "filter_options" => [
                    "operation" => FilterOperations::SMALLER_THAN_OR_EQUAL_DATE_FILTER_OPERATION,
                    "field" => "to_date"
                ]
            ];
            if (Plugins::pluginActive(PluginUtil::HRM_PLUGIN)) {
                $this->filters['depts'] = [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-3',
                    'options' => $depts->toArray(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Department'),
                        'id' => 'deptSelect'
                    ],
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'department',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'department_id'
                    ]
                ];

                $this->filters['designation'] = [
                    'simple' => true, /* displays the filter outside the toggle */
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-3',
                    'options' => $designations->toArray(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Designation'),
                        'id' => 'designationSelect'
                    ],
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'designation',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'designation_id'
                    ]
                ];

                $this->filters['shift'] = [
                    'simple' => false,
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'div' => 'col-md-3',
                    'options' => $shifts->toArray(),
                    'type' => 'select',
                    'inputClass' => 'select-filter',
                    'attributes' => [
                        'placeholder' => __t('Select').' '.__t('Shift'),
                        'id' => 'shiftSelect'
                    ],
                    'filter_options' => [
                        'table' => 'staff_info',
                        'model' => 'shift',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'attendance_shift_id'
                    ]
                ];
            }
            if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $branches = Branch::getStaffBranchesSuspended();
                $this->filters['branch'] = [
                    'simple'=> true,
                    'label' => false,
                    'after' => '</div>',
                    'before' => '<div class="form-group">',
                    'attributes' => [
                        'placeholder' => __t('Select') . ' ' . __t('Branch'),
                        'id' => 'branchSelect',
                    ],
                    'div' => 'col-md-4',
                    'inputClass' => 'select-filter form-control',
                    'options' => $branches,
                    'type' => 'select', 'filter_options' => [
                        'table' => 'staffs',
                        'model' => 'attendance_permission_staff_relation',
                        'operation' => FilterOperations::EQUAL_FILTER_OPERATION,
                        'field' => 'branch_id',
                    ]
                ];
            }
            return $this->filters;
        }

    }

    /**
     * {@inheritDoc}
     */
    protected function wrapActivityLogDataFromIdsToMeaningfulName($record)
    {
        $data = parent::wrapActivityLogDataFromIdsToMeaningfulName($record);
        $data['staff_id'] = $record->attendance_permission_staff_relation ? $record->attendance_permission_staff_relation->name : '';
        $data['leave_type_id'] = $record->attendance_permission_leave_type_relation ? $record->attendance_permission_leave_type_relation->name : '';

        return $data;
    }

    /**
     * {@inheritDoc}
     */
    protected function getActivityLogRelations($record): array
    {
        $relations = parent::getActivityLogRelations($record);
        if ($record->attendance_permission_staff_relation) {
            $relations[] = new ActivityLogRelationRequest($record->attendance_permission_staff_relation->id, 'staff');

        }
        if ($record->attendance_permission_leave_type_relation) {
            $relations[] = new ActivityLogRelationRequest($record->attendance_permission_leave_type_relation->id, 'leave_type');
        }

        if ($record->leaveApplication) {
            $relations[] = new ActivityLogRelationRequest($record->leaveApplication->id, EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY);
        }
        return $relations;
    }

    /**
     * {@inheritDoc}
     */
    public function getSortFields()
    {
        return [
            'fields' => [
                'created' => [
                    'title' => __t('Date of Creation'),
                    'field' => 'created',
                    'direction' => 'DESC'
                ],
                'id' => [
                    'title' => __t('ID'),
                    'field' => 'id',
                    'hidden' => true,
                    'direction' => 'DESC'
                ],
            ],
            'active' => [
                'field' => 'id',
                'direction' => 'DESC',
            ]
        ];
    }

    public function getAttendanceLeavePermissionByStaffIdAndDate($staff_id, $date): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDate($staff_id, $date, [AttendancePermissionTypesUtil::LEAVE, AttendancePermissionTypesUtil::HALF_LEAVE]);

        return $this->processCalculatorLeavePermissions($results);
    }

    public function getAttendanceDayLeavePermissionByStaffIdAndDate($staff_id, $date): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDate($staff_id, $date, [AttendancePermissionTypesUtil::LEAVE]);

        return $this->processCalculatorLeavePermissions($results);
    }

    public function getAttendanceHalfDayLeavePermissionByStaffIdAndDate($staff_id, $date): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDate($staff_id, $date, [AttendancePermissionTypesUtil::HALF_LEAVE]);

        return $this->processCalculatorLeavePermissions($results);
    }

    public function getAttendanceLeavePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo, [AttendancePermissionTypesUtil::LEAVE, AttendancePermissionTypesUtil::HALF_LEAVE]);
        return $this->processCalculatorLeavePermissions($results);
    }

    public function getAttendanceDayLeavePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo, [AttendancePermissionTypesUtil::LEAVE]);
        return $this->processCalculatorLeavePermissions($results);
    }

    public function getAttendanceHalfDayLeavePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo, [AttendancePermissionTypesUtil::HALF_LEAVE]);
        return $this->processCalculatorLeavePermissions($results);
    }

    public function processCalculatorLeavePermissions($results): array
    {
        $leavePermissions = [];
        if ($results) {
            foreach ($results as $attendancePermission) {
                $overrideWeekEnd = false;
                if($attendancePermission->leave_type_id) {
                    $leaveType = $this->leaveTypeRepository->find($attendancePermission->leave_type_id);
                    if($leaveType) {
                        $overrideWeekEnd = $leaveType->override_weekend;
                    }
                }
                $leavePermission = new AttendancePermission(
                    $attendancePermission->id,
                    $attendancePermission->type,
                    $attendancePermission->leave_type_id,
                    null,
                    $attendancePermission->from_date,
                    $attendancePermission->to_date,
                    $attendancePermission->application_date,
                    $attendancePermission->shift_type,
                    $overrideWeekEnd,
                    $attendancePermission->early_time
                );
                $leavePermissions[] = $leavePermission;
            }
        }
        return $leavePermissions;
    }

    public function getAttendanceDelayPermissionByStaffIdAndDate($staff_id, $date): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDate($staff_id, $date, AttendancePermissionTypesUtil::DELAY);
        return $this->processCalculatorDelayPermissions($results);
    }
    public function getAttendanceEarlyLeavePermissionByStaffIdAndDate($staff_id, $date): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDate($staff_id, $date, AttendancePermissionTypesUtil::EARLY);
        return $this->processCalculatorDelayPermissions($results);
    }

    public function getAttendanceDelayPermissionByStaffIdAndDateRange( $staff_id, $dateFrom, $dateTo): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo, AttendancePermissionTypesUtil::DELAY);
        return $this->processCalculatorDelayPermissions($results);
    }

    public function getAttendanceEarlyLeavePermissionByStaffIdAndDateRange( $staff_id, $dateFrom, $dateTo): array
    {
        $results = $this->repo->getAttendancePermissionByStaffIdAndDateRange($staff_id, $dateFrom, $dateTo, AttendancePermissionTypesUtil::EARLY);
        return $this->processCalculatorDelayPermissions($results);
    }

    public function processCalculatorDelayPermissions($results): array
    {
        $delayPermissions = [];
        if ($results) {
            foreach ($results as $attendancePermission) {
                $delayPermission = new AttendancePermission(
                    $attendancePermission->id,
                    $attendancePermission->type,
                    $attendancePermission->leave_type_id,
                    $attendancePermission->late_time,
                    $attendancePermission->from_date,
                    $attendancePermission->to_date,
                    $attendancePermission->application_date,
                    $attendancePermission->shift_type,
                    false,
                    $attendancePermission->early_time
                );
                $delayPermissions[] = $delayPermission;
            }
        }
        return $delayPermissions;
    }

    public function getViewData($id)
    {
        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates('attendance_permission');

        $record = $this->repo->find($id);
        $staffService = resolve(StaffService::class);
        if(!$staffService->isAccessableStaffId($record->staff_id, true)){
            throw new NotAccessibleStaffBranchException;
        }

        return array_merge(parent::getViewData($id), ['view_templates' => $viewTemplates]);
    }

    protected function getListingConditions()
    {
        $listingConditions = parent::getListingConditions();
        $authUserID = getAuthOwner('staff_id');
        if (Plugins::pluginActive(PluginUtil::BranchesPlugin)){
            $staffService = resolve(StaffService::class);
            $listingConditions[] = [
                "operation" => FilterOperations::IN_FILTER_OPERATION,
                "field" => "staff_id",
                "value" => $staffService->getAccessibleStaffIds($authUserID,true)
            ];
        }
        return $listingConditions;
    }

    public function getStaffLatestAttendancePermissions($staffId, $limit = 10)
    {
        $permissions = $this->repo->getStaffLatestAttendancePermissions($staffId , $limit);
        $items = $this->formatPermissions($permissions);
        return [
            'items'=>$items,
            'viewUrl'=>route("owner.attendance_permissions.index",['name'=>$staffId])
        ];
    }

    private function formatPermissions(Collection $permissions) :array
    {
        $formattedPermissions = [];
        foreach ($permissions as $permission){
            $fromDay = format_date($permission->fromDate);
            $toDay = format_date($permission->toDate);

            $fromDayName = __t(date('l', strtotime($permission->fromDate)));
            $toDayName = __t(date('l', strtotime($permission->toDate)));

            $permissionData = [
                'id'=>$permission->id,
                "leaveType"=> $permission->leaveType,
                "startDate"=> $fromDayName . ' ' . $fromDay,
                "endDate"=> $toDayName . ' '. $toDay,
                "viewUrl"=>route("owner.attendance_permissions.show",['attendance_permission'=>$permission->id])
            ];
            $formattedPermissions[] = $permissionData;
        }
        return $formattedPermissions;
    }

}
