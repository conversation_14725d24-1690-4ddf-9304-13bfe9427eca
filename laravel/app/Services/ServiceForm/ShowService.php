<?php

namespace App\Services\ServiceForm;

use App\Services\PaginationStackTrait;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\TitleText;

class ShowService
{
    use PaginationStackTrait;

    public function getPageHead(AppEntityData $data): Header
    {
        $pageTitle = new TitleText('page-title');
        $pageTitle
            ->setTitle(sprintf(__t('Service Form #%s'), $data->id));

        $header = new Header('page-header');

        $pagePagination = $this->getPagination(EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY, $data->id);

        $header
            ->addRight($pagePagination)
            ->addLeft($pageTitle);

        return $header;
    }

}
