<?php

namespace App\Services;

use App\Repositories\EntityDocumentRepository;
use App\Repositories\EntityDocumentTypeRepository;
use App\Repositories\StaffRepository;
use Illuminate\Support\Facades\Storage;
use ZipArchive;
use Exception;
use Carbon\Carbon;
use Izam\Daftra\Common\Utils\EntityDocumentStatusUtil;

/**
 * Class StaffDocumentExportService
 *
 * Exports selected staff documents in different modes:
 * - Export (with metadata CSV)
 * - Download (without metadata, and direct file if only one)
 */
class StaffDocumentExportService
{

    public function __construct(
        protected EntityDocumentTypeRepository $entityDocumentTypeRepository,
        protected EntityDocumentRepository $entityDocumentRepository,
        protected StaffRepository $staffRepository,
        protected EntityDocumentService $entityDocumentService
      )
    {
    }

    /**
     * Exports or downloads staff documents.
     *
     * @param array $documentIds
     * @param bool $withMetadata Whether to include metadata CSV
     * @return string Full path to the file (ZIP or single file)
     * @throws Exception
     */
    public function exportDocuments(array $documentIds, bool $withMetadata = true, $staffId): string
    {
        $missingDocumentTypesIds = [];
        foreach($documentIds as $i => $id){
            if (!is_numeric($id)) {
                $missingDocumentTypesIds[] = explode('_', $id)[1];
                unset($documentIds[$i]);
            }
        }
        $documents = $this->entityDocumentRepository->getDocumentsByIds($documentIds);

        $tempFiles = $this->prepareFiles($documents);
        if (!$withMetadata && empty($tempFiles)) {
            throw new Exception('No document files found.');
        }

        if (!$withMetadata && count($tempFiles) === 1) {
            return $tempFiles[0]['path']; // Return single file path directly
        }

        $zipFilePath = $this->createZipArchivePath();
        $zip = $this->initializeZipArchive($zipFilePath, $tempFiles);

        foreach ($tempFiles as $fileEntry) {
            $zip->addFile($fileEntry['path'], $fileEntry['zip_path']);
        }

        if ($withMetadata) {
            $csvPath = $this->generateMetadataCsv($documents, $missingDocumentTypesIds, $staffId);
            $zip->addFile($csvPath, 'metadata.csv');
        }

        $zip->close();

        // Cleanup temp files
        foreach ($tempFiles as $fileEntry) {
            @unlink($fileEntry['path']);
        }
        if (isset($csvPath)) {
            @unlink($csvPath);
        }

        return $zipFilePath;
    }

    /**
     * Prepares temporary file paths for the given documents' attachments.
     *
     * This method iterates over each document and its associated entity attachments,
     * fetches the file content from the S3 disk, stores it temporarily in the local
     * filesystem, and builds an array of paths suitable for zipping or further processing.
     *     
     * @param iterable $documents
     * @return array [['path' => string, 'zip_path' => string]]
     */
    protected function prepareFiles(iterable $documents): array
    {
        $prepared = [];

        foreach ($documents as $document) {
            foreach ($document->entityAttachment as $attachment) {
                try{
                    if (!$attachment->files) {
                        continue;
                    }
    
                    $fileName = $attachment->files->name;
                    $fileContent = Storage::disk('s3')->get($attachment->files->path);
    
                    $tempFilePath = storage_path('app/' . uniqid() . '_' . basename($fileName));
                    file_put_contents($tempFilePath, $fileContent);
    
                    $prepared[] = [
                        'path' => $tempFilePath,
                        'zip_path' => 'documents/' . $document->name . '/' . $fileName
                    ];
                }catch(Exception $e){
                }
            }
        }

        return $prepared;
    }

    /**
     * Creates a unique path for the ZIP archive.
     *
     * @return string
     */
    protected function createZipArchivePath(): string
    {
        $site_id = getAuthOwner('id');
        $exportName = "employee_documents" . now()->format('Y_m_d_h_i_s') . '_' . $site_id;
        return storage_path('app/' . $exportName . '.zip');
    }

    /**
     * Initializes a new ZIP archive.
     *
     * @param string $zipPath
     * @return ZipArchive
     * @throws Exception
     */
    protected function initializeZipArchive(string $zipPath, $tempFiles): ZipArchive
    {
        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            // Cleanup temp files
            foreach ($tempFiles as $fileEntry) {
                @unlink($fileEntry['path']);
            }
            throw new Exception("Cannot create ZIP file: $zipPath");
        }
        return $zip;
    }

    /**
     * Generates a metadata CSV file for the documents.
     *
     * @param iterable $documents
     * @return string Path to the generated CSV file
     */
    protected function generateMetadataCsv(iterable $documents, $missingDocumentTypesIds = [], $staffId): string
    {
        $csvPath = storage_path('app/metadata_' . uniqid() . '.csv');
        $csvFile = fopen($csvPath, 'w');
        fprintf($csvFile, chr(0xEF).chr(0xBB).chr(0xBF)); //header utf-8
        $staff = $this->staffRepository->find($staffId);
        $staffData = [
            __t('Employee name').': '.$staff->full_name,
            __t('Email').': '.$staff->email_address,
            __t('Phone').': '.$staff->mobile,
            __t('Join Date').': '.($staff->staff_job->join_date ?? '')
        ];
        fputcsv($csvFile, [implode(', ', $staffData)]);

        $headers = [
            __t('Document Name'),
            __t('Document Type'),
            __t('Expiry Date'),
            __t('Status'),
            __t('Uploaded By'),
            __t('Upload Date'),
            __t('Documents folder name')
        ];
        fputcsv($csvFile, $headers);

        if($missingDocumentTypesIds){
            $missingDocumentTypes = $this->entityDocumentTypeRepository->find($missingDocumentTypesIds);
            foreach($missingDocumentTypes as $missingDocumentType){
                $csvRow = [
                    __t('Missing'),
                    $missingDocumentType->name,
                    __t('Missing'),
                    __t('Missing'),
                    __t('Missing'),
                    __t('Missing'),
                    __t('Missing'),
                ];
                fputcsv($csvFile, $csvRow);
            }
        }

        foreach ($documents as $document) {
            $status = $this->entityDocumentService->calculateStatus($document);
            $isExpirable = $document->entity_document_type?->is_expirable ?? 0;
            $expireyDate = Carbon::parse($document->expiry_date);
            $expireIn = $isExpirable ? $expireyDate->diffInDays(Carbon::today()) : null;
            $createdBy = $document->createdBy->full_name ?? '';
            $fileNames = collect($document->entityAttachment)
                ->filter(fn($a) => $a->files)
                ->map(fn($a) => $a->files->name)
                ->toArray();

            $hasFiles = !empty($fileNames);
            $csvRow = [
                $document->name,
                $document->documentType->name,
                $document->expiry_date,
                EntityDocumentStatusUtil::getStatusTrans($status,$expireIn),
                $createdBy,
                $document->modified,
                $hasFiles ? implode(',', $fileNames) : __t('Missing'),
            ];

            fputcsv($csvFile, $csvRow);
        }

        fclose($csvFile);
        return $csvPath;
    }
}
