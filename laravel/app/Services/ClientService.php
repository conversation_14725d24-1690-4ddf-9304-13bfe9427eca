<?php

namespace App\Services;

use App\Exceptions\EntityNotFoundException;
use App\Facades\Plugins;
use App\Jobs\Memberships\MembershipAutomaticStatusJob;
use App\Repositories\ClientRepository;
use App\Repositories\EntityRepository;
use App\Repositories\PackageRepository;
use App\Utils\AllowedClientsToAttendUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\Memberships\MemberShipStatusUtil;
use App\Utils\PluginUtil;
use Illuminate\Support\Collection;
use Izam\Attachment\Service\AttachmentsService;

/**
 * ClientService Class
 * @package App\Services
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class ClientService extends BaseService
{
    /**
     * @var ClientRepository
     */
    public $repo;

    /**
     * @var PackageRepository
     */
    private $packageRepository;

    /**
     * @var SettingService
     */
    private $settingService;

    /**
     * ClientService constructor.
     * @param ClientRepository $clientRepository
     * @param PackageRepository $packageRepository
     * @param EntityRepository $entityRepository
     * @param SettingService $settingService
     */
    public function __construct(ClientRepository $clientRepository, PackageRepository $packageRepository, EntityRepository $entityRepository, SettingService $settingService, protected AttachmentsService $attachmentsService)
    {
        $this->packageRepository = $packageRepository;
        $this->settingService = $settingService;
        parent::__construct($clientRepository, $entityRepository);
    }

    /**
     * filter clients by some criteria
     * @param mixed $query
     * @param bool $allowSuspended
     * @return mixed
     */
    public function filterByName($query, bool $allowSuspended = false)
    {
        return $this->repo->getAutoCompleteResult($query, $allowSuspended);
    }

    public function prepareClientCreditSummary(int $client_id)
    {
        $client = $this->repo->find($client_id);
        if (!$client)
            throw new EntityNotFoundException('Client');

        $clientCreditData = $this->repo->getClientCreditSummaryData($client_id);
        $data = [];
        foreach ($clientCreditData as $key => $creditType){
            if(is_null($creditType->credit_charges_amount_sum))
                unset($clientCreditData[$key]);
            else {
                if (in_array($creditType->credit_types_id, array_keys($data))){
                    $data[$creditType->credit_types_id]->credit_charges_amount_sum += $creditType->credit_charges_amount_sum;
                    $data[$creditType->credit_types_id]->charge_usages_amount_sum += $creditType->charge_usages_amount_sum;
                } else {
                    $data[$creditType->credit_types_id] = $creditType;
                }
            }
        }

        return $data;
    }

    public function getClientMembership($id){
        return $this->repo->getClientMembership($id);
    }

    public function getActivePackagesCount()
    {
        return $this->packageRepository->getCountActivePackages();
    }

    /**
     * @param $client_id
     * @return array
     * @throws EntityNotFoundException
     */
    public function getClientDataForAttendance($client_id)
    {
        if (is_null($client_id))
            throw new EntityNotFoundException('Client');

        $client = $this->find($client_id);
        if (!$client)
            throw new EntityNotFoundException('Client');

        $membershipData = null;
        $client_ids = [];
        if (Plugins::pluginActive(PluginUtil::MEMBERSHIP_PLUGIN)) {
            $client_membership = $client->membership;
            if ($client_membership) {
                $membership_package = $client_membership->package;
                $membershipData = [
                    'membership_status' => MemberShipStatusUtil::getStatus($client_membership->status),
                    'status_class' => MemberShipStatusUtil::getStatusClass($client_membership->status),
                    'membership_join_date' => formatForView($client_membership->join_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                    'membership_expiry_date' => formatForView($client_membership->expiry_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                    'package_name' => $membership_package->name
                ];
                (new MembershipAutomaticStatusJob($client_membership->id))->handle();
            }

            $membership_setting = $this->settingService->getAllowedClientsToAttendOption();
            switch ($membership_setting){
                case AllowedClientsToAttendUtil::ALL_CLIENTS;
                    $client_ids = $this->all()->pluck('id')->toArray();
                    break;
                case AllowedClientsToAttendUtil::CLIENTS_WITH_MEMBERSHIP;
                    $client_ids = $this->repo->getClientsWithMemberships();
                    break;
                case AllowedClientsToAttendUtil::CLIENTS_WITH_ACTIVE_MEMBERSHIP;
                    $client_ids = $this->repo->getClientsWithAcitveMemberships();
                    break;
            }
        }

        return [
            'id' => $client->id,
            'name' => $client->business_name,
            'code' => $client->client_number,
            'photo' => App(AttachmentsService::class)->resolveClientImage($client),
            'membership' => $membershipData,
            'client_ids' => $client_ids
        ];
    }

    /**
     * @param $client_id
     * @return array
     * @throws EntityNotFoundException
     */
    public function getClientDataForReservation($client_id)
    {
        if (is_null($client_id))
            throw new EntityNotFoundException('Client');

        $client = $this->find($client_id);
        if (!$client)
            throw new EntityNotFoundException('Client');

        return [
            'id' => $client->id,
            'name' => $client->business_name,
            'code' => $client->client_number,
            'email' => $client->email,
            'photo' => $client->PhotoPath,
        ];
    }
    public function getSelectedClient($clientConditions)
    {
        $clients = $this->repo->getSelectedClient($clientConditions);
        return $this->formatClientOptions($clients);
    }

    public function getDefaultClientOptions(): Collection{
        return $this->repo->getDefaultClientOptions()->map($this->clientOptionFormatterCallback());
    }

    public function clientOptionFormatterCallback(){
        return function($client){
            $email = !is_null($client['email']) ?  trim($client['email']) : $client['email'];
            $img = $this->attachmentsService->resolveClientImage($client);
            $text = "{$client['business_name']} #{$client['client_number']} " . ( !empty($email) ? "({$email})" : '');
            $item = [
                "value" => $client->id,
                "id" => $client->id,
                "text" => $text,
                "img" => $img ?? '',
            ];
            $item['htmlJson'] = htmlspecialchars(json_encode($item), ENT_QUOTES, 'UTF-8');
            return $item;
        };
    }

    public function formatClientOptions($clients = [])
    {
        $processedResults = [];
        foreach ($clients as $client){
            $email = !is_null($client['email']) ?  trim($client['email']) : $client['email'];
            $img = $this->attachmentsService->resolveClientImage($client);
            if ($email) {
                $processedResults[] = ['img' => $img,'text' => "{$client['business_name']} #{$client['client_number']} ({$email})", 'id' => $client['id']];
            } else {
                $processedResults[] = ['img' => $img,'text' => "{$client['business_name']} #{$client['client_number']}", 'id' => $client['id']];
            }
        }
        return $processedResults;
    }

    public function getClientsForBooking($q, $branchId = null){
        return $this->repo->getClientsWithContactInfo($q, $branchId);
    }
}
