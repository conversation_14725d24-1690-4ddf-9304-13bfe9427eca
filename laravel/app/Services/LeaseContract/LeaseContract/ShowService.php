<?php

namespace App\Services\LeaseContract\LeaseContract;

use App\Facades\Permissions;
use App\Modules\Resource\Services\AdditionalFieldsAwareService;
use App\Services\PaginationStackTrait;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PermissionUtil;
use Izam\Daftra\Common\EntityStructure\AppEntityData;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\Stack;
use Izam\View\Form\Element\TitleText;
use Izam\View\Form\Element\ViewActions;
use Izam\View\Form\Tab\IframeElement;
use Izam\View\Form\Tab\Services\CreateTabsServices;
use Izam\View\Form\Tab\Tabs;
use Laminas\Form\Element;
use Laminas\Form\Element\Button;
use Izam\View\Form\Element\Badge;
use Izam\View\Form\Element\SplitButtonDropdowns;
use App\Utils\PluginUtil;
use App\Facades\Settings;
use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;
use Izam\Daftra\Invoice\Repositories\InvoiceRepository;

class ShowService
{
    use PaginationStackTrait;
    const ENTITY_KEY = EntityKeyTypesUtil::LEASE_CONTRACT;

    public function __construct(private AdditionalFieldsAwareService $additionalFieldsAwareService){}


    public function getPageHead(AppEntityData $data, $viewExtraData = []): Header
    {
        $pageTitle = new TitleText('page-title');
        $title = __t('Lease Contract') .' #' .$data->code;
        $pageTitle->setTitle($title)->setOption('shrink', 'flex-shrink-1');
        $leftStack = new Stack('left-stack');
        $leftStack->setOption('theme', 'theme2');
        if($viewExtraData['selectedFollowUp']){
            $mainStatusBadge = new Badge('left-page-badge');
            $mainStatusBadge->setLabel($viewExtraData['selectedFollowUp']['name'])
            ->setAttribute('style', "background-color:".$viewExtraData['selectedFollowUp']['background']);
            $leftStack->add($mainStatusBadge);
        }

        $rightStack = new Stack('left-Stack');
        $rightStack->setOption('theme', 'theme3');
        $progressPrecentage = $data->total? (($data->total_paid / $data->total) * 100) : 0;

        $totalsProgressBarHtml = '
            <span>
                '.format_price($data->total_paid, $data->currency_code).'
            <span class="text-light-3">/'.format_price($data->total, $data->currency_code).'</span>
            </span>
            <div class="progress mt-1" role="progressbar" aria-label="total-payment-progress" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
            '.($data->total_paid == $data->total? '<div class="progress-bar bg-success" style="width: 100%"></div>': '<div class="progress-bar" style="width: '.$progressPrecentage.'%"></div>') .'
            </div>
        ';
        $totalProgressBar = new Element('total-balance');
        $totalProgressBar->setLabel($totalsProgressBarHtml);

        $rightStack->add($totalProgressBar);

        $header = new Header('page-header');
        $header
            ->setOption('wrapperSize', 'col-6')
            ->addLeft($pageTitle)
            ->addLeft($leftStack)
            ->addRight($rightStack)
            ->addRight($this->getPagination(EntityKeyTypesUtil::LEASE_CONTRACT, $data->id));
        return $header;
    }

    public function getTabs(AppEntityData $data, $form, $viewExtraData = []): Tabs
    {
        $tabs = [];
        $this->addDetailsTab($tabs, $data , $form, $viewExtraData);
        $this->addInstallmentsTab($tabs, $data->id, $viewExtraData);
        $this->addInvoicesTab($tabs, $data->id, $viewExtraData);
        $this->addRefundReceiptsTab($tabs, $data->id, $viewExtraData);
        $this->addCreditNotesTab($tabs, $data->id, $viewExtraData);
        $this->addActivityLogTab($tabs, $data->id, $viewExtraData);
        return CreateTabsServices::createTabs($tabs);
    }

    private function addDetailsTab(array &$tabs, AppEntityData $data, $form, $viewExtraData = [])
    {
        $customFieldsForm = $this->additionalFieldsAwareService->show(EntityKeyTypesUtil::LEASE_CONTRACT, $data->id);
        $tabs[] = [
            'type' => Element::class,
            'name' => 'details',
            'label' => __t('Details'),
            'value' => [
                'viewPath' => 'lease_contracts/partials/details',
                'context' => [
                    'data' => $data,
                    'form' => $form ,
                    'entityKey'=>self::ENTITY_KEY,
                    'isOverdue'=> $viewExtraData['isOverdue'],
                    'customFieldsForm'=>$customFieldsForm
                ]
            ]
        ];
    }

    private function addActivityLogTab(array &$tabs, $id, $viewExtraData = [])
    {
        if(!Permissions::checkPermission(PermissionUtil::View_All_Activity_Logs) && !Permissions::checkPermission(PermissionUtil::View_His_Activity_Logs )) return ;
        $tabs[] = [
            'type' => IframeElement::class,
            'name' => 'activity-log',
            'label' => __t('Activity Log'),
            'attributes' => [
                'src' => sprintf(
                    "/v2/owner/activity_logs/entity/iframe?entity_key=%s&entity_id=%s&sort=DESC&layout2022=1",
                    self::ENTITY_KEY,
                    $id
                )
            ]
        ];
    }

    private function addInstallmentsTab(array &$tabs, $id, $viewExtraData = [])
    {
        if($viewExtraData['installmentsCount']){
            $tabs[] = [
                'type' => IframeElement::class,
                'name' => 'installments',
                'label' => __t('Installments'),
                'attributes' => [
                    'src' => route('owner.entity.list', [
                        'entityKey' => EntityKeyTypesUtil::CONTRACT_INSTALLMENT,
                        'iframe' => 1,
                        'show_row_actions' => 1,
                        'filter[contract_id]' => $id ,
                        "sort[due_date]" => "asc",
                        "hide_page_header"=>1,
                        'redirect_to_entity' => EntityKeyTypesUtil::LEASE_CONTRACT,
                        'redirect_to_entity_id'=> $id,
                    ]),
                ],
            ];
        }
    }

    private function addInvoicesTab(array &$tabs, $id, $viewExtraData = [])
    {

        if(
            ($viewExtraData['hasViewDebitNotesPermission'] && $viewExtraData['debitNotesCount'])
            ||
            ($viewExtraData['hasViewInvoicesPermission'] && $viewExtraData['invoicesCount'])
        ){
            $tabs[] = [
                'type' => IframeElement::class,
                'name' => 'invoices',
                'label' => __t('Invoices'),
                'attributes' => [
                    'src' => sprintf(
                        "/owner/invoices/index?box=1&from_lease_contract_view=1&layout2024&source_type[]=".InvoiceSourceTypesUtil::LEASE_CONTRACT."&source_type[]=".InvoiceSourceTypesUtil::CONTRACT_INSTALLMENT."&source_id=".$id,
                        self::ENTITY_KEY,
                        $id
                    )
                ]
            ];
        }
    }

    private function addRefundReceiptsTab(array &$tabs, $id, $viewExtraData = [])
    {
        if($viewExtraData['hasViewInvoicesPermission'] && $viewExtraData['refundReceiptsCount']){
            $tabs[] = [
                'type' => IframeElement::class,
                'name' => 'refund_receipts',
                'label' => __t('Refund Receipts'),
                'attributes' => [
                    'src' => sprintf(
                        "/owner/invoices/refund?from_lease_contract_view=1&layout2024&box=1&source_type[]=".InvoiceSourceTypesUtil::LEASE_CONTRACT."&source_type[]=".InvoiceSourceTypesUtil::CONTRACT_INSTALLMENT."&source_id=".$id,
                        self::ENTITY_KEY,
                        $id
                    )
                ]
            ];
        }
    }

    private function addCreditNotesTab(array &$tabs, $id, $viewExtraData = [])
    {
        if($viewExtraData['hasViewInvoicesPermission'] && $viewExtraData['creditNotesCount']){
            $tabs[] = [
                'type' => IframeElement::class,
                'name' => 'creditnotes',
                'label' => __t('Credit Notes'),
                'attributes' => [
                    'src' => sprintf(
                        "/owner/invoices/creditnotes?layout2024&from_lease_contract_view=1&box=1&source_type[]=".InvoiceSourceTypesUtil::LEASE_CONTRACT."&source_type[]=".InvoiceSourceTypesUtil::CONTRACT_INSTALLMENT."&source_id=".$id,
                        self::ENTITY_KEY,
                        $id
                    )
                ]
            ];
        }
    }

    public function getActions($data, $viewExtraData)
    {
        $viewActions = new ViewActions('View-Action');

        if ($viewExtraData['hasEditPermission']) {
            $editAnchor = new Anchor('edit-action');
            $editAnchor->setLabel(__t('Edit'))
                ->setAttribute('href', route('owner.entity.edit', [
                    'entityKey' => self::ENTITY_KEY,
                    'id' => $data->id
                ]))
                ->setOption('icon', 'pencil');

            $viewActions->add($editAnchor);
        }

        if ($viewExtraData['hasDeletePermission']) {
            $deleteBtn = new Button('delete-action');
            $deleteBtn
                ->setOption('message', sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Lease Contract')))
                ->setOption('action', route('owner.entity.delete', [
                    'entityKey' => self::ENTITY_KEY,
                    'id' => $data->id
                ]))
                ->setOption('theme', 'delete')
                ->setLabel(__t('Delete'))
                ->setOption('icon', 'trash-can');

            $viewActions->add($deleteBtn);
        }

        if ($viewExtraData['hasAddInvoicePermission']) {
            $addInvoiceAnchor = new Anchor('add-invoice');
            $addInvoiceAnchor->setLabel(__t('Add Invoice'))
                ->setAttribute('href', "/owner/invoices/add?lease_contract_id={$data->id}&currency_code={$data->currency_code}")
                ->setOption('icon', 'file-document');
            $viewActions->add($addInvoiceAnchor);

        }

        if ($viewExtraData['hasEditPermission']) {
            $addInstallments = new Anchor('add-installment-action');
            $addInstallments->setLabel(__t('Add Installment'))
                ->setAttribute('href', route('owner.entity.create', EntityKeyTypesUtil::CONTRACT_INSTALLMENT)."?contract_id=".$data->id)
                ->setOption('icon', 'file-document');

            $viewActions->add($addInstallments);
        }
        if($viewExtraData['hasEditPermission']){
            if($viewExtraData['followUpStatuses']){
                $followUps = [];
                foreach ($viewExtraData['followUpStatuses'] as $i => $fs) {
                    $colorData = $viewExtraData['followUpStatusRepo']->getFollowUpStatusNewColor($fs->color);
                    $followUps[] = [
                        'color' => $colorData['color'] ?? $fs->color,
                        'background-color' => isset($colorData['color']) ? $fs->color : '#FFF',
                        'name' => $fs->name,
                        'link' => '/v2/owner/lease-contracts/' . $data->id . '/change-followup-status/' . $fs->id
                    ];
                    if ($data->status_id == $fs->id) {
                        $followUps[$i]['link'] = '#';
                    }
                }
                $followUps[]=[
                    'color' => 'black',
                    'background-color'=>'',
                    'name'=> __t('Manage statues'),
                    'link'=> '/v2/owner/entity/lease_contract/followup_status'
                ];

                $changeStatusBtn = new SplitButtonDropdowns('change-status-dropdown');
                $changeStatusBtn->setLabel(__t('Change Manual Status'));
                $changeStatusBtn->setOption('icon', 'tooltip-edit');
                foreach ($followUps as $index => $status) {
                    $tempAnchor = new Anchor('change-status-anchor-' . $index);
                    $tempAnchor->setLabel($status['name']);
                    $tempAnchor->setOption('color', $status['background-color']);
                    if (isset($status['icon'])){
                        $tempAnchor->setOption('icon', $status['icon']);
                    }
                    $tempAnchor->setAttribute('href', $status['link']);
                    $changeStatusBtn->add($tempAnchor);
                }
                $viewActions->add($changeStatusBtn);
            }
        }

        $repo = resolve(\Izam\Template\TemplateRepository::class);
        $viewTemplates = $repo->getEntityViewTemplates(EntityKeyTypesUtil::LEASE_CONTRACT);
        if (!empty($viewTemplates['global']) || !empty($viewTemplates['local'])) {
            $printableBtn = new SplitButtonDropdowns('prinatable-dropdown');
            $printableBtn->setLabel('Printables');
            foreach ($viewTemplates['global'] as  $template) {
                $tempAnchor = new Anchor('global-template-'.$template['id']);
                $tempAnchor->setLabel($template['name']);
                $tempAnchor->setAttribute('href', show_printable_template_view_page($template['id'], $data->id, $template['name']).'?template=global');
                $printableBtn->add($tempAnchor);
            }

            foreach ($viewTemplates['local'] as  $template) {
                $tempAnchor = new Anchor('local-template-'.$template['id']);
                $tempAnchor->setLabel($template['name']);
                $tempAnchor->setAttribute('href', show_printable_template_view_page($template['id'], $data->id, $template['name']).'?template=local');
                $printableBtn->add($tempAnchor);
            }
            $viewActions->add($printableBtn);
        }

        return $viewActions;
    }
}
