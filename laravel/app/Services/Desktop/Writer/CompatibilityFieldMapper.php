<?php

namespace App\Services\Desktop\Writer;

use Izam\Daftra\Product\Utils\ProductStatusUtil;
use Izam\Daftra\Store\Utils\StoreStatusUtil;

class CompatibilityFieldMapper
{
    private $appVersion;
    private $appOs;
    private $appName;

    public function map($table, $data)
    {
        if (is_null($this->appVersion)) {
            if ($table == 'products') {
                if (count($data) && property_exists($data[0], 'status')) {
                    $data = array_map(function ($item) {
                        $item->deactivate = $item->status == ProductStatusUtil::SUSPENDED ? ProductStatusUtil::IN_ACTIVE : $item->status;
                        unset($item->status);
                        return $item;
                    }, $data);
                }
                if (count($data) && property_exists($data[0], 'item_group_id')) { // release Mar 2024
                    $data = array_map(function ($item) {
                        unset($item->item_group_id);
                        return $item;
                    }, $data);
                }
            } elseif ($table == 'stores') {
                $data = array_map(function ($item) {
                    $item->active = $item->active == StoreStatusUtil::SUSPEND ? StoreStatusUtil::INACTIVE : $item->active;
                    return $item;
                }, $data);
            } elseif ($table == 'journal_accounts') {
                if (count($data) && property_exists($data[0], 'is_hidden')) { // release Mar 2024
                    $data = array_map(function ($item) {
                        unset($item->is_hidden);
                        return $item;
                    }, $data);
                }
            }
        }
        if ($this->appName == 'EXP') {
            if ($table == 'journal_accounts') {
                if (count($data) && property_exists($data[0], 'is_hidden')) { // release Mar 2024
                    $data = array_map(function ($item) {
                        unset($item->is_hidden);
                        return $item;
                    }, $data);
                }
            } elseif ($table == 'journal_cats') {
                if (count($data) && property_exists($data[0], 'is_hidden')) { // release Mar 2024
                    $data = array_map(function ($item) {
                        unset($item->is_hidden);
                        return $item;
                    }, $data);
                }
            }

        }
        if ($this->appName == 'POS' && $this->appVersion <= 10186 && $table == 'products') {
            if (count($data) && property_exists($data[0], 'brand_id')) {
                $data = array_map(function ($item) {
                    unset($item->brand_id);
                    return $item;
                }, $data);
            }
        }

        if ($this->appName == 'POS' && $table == 'items_categories') {
            if (count($data) && property_exists($data[0], 'display_order')) {
                $data = array_map(function ($item) {
                    unset($item->display_order);
                    return $item;
                }, $data);
            }
        }
        return $data;
    }

    /**
     * @param mixed $appVersion
     */
    public function setAppVersion($appVersion): void
    {
        $this->appVersion = $appVersion ? str_replace('.', '', $appVersion) : $appVersion;
    }

    /**
     * expected values for appOs [android, ios, desktop]
     *
     * @param $appOs
     * @return void
     */
    public function setAppOs($appOs)
    {
        $appOs = strtolower($appOs);
        if (in_array($appOs, ['android', 'ios'])) {
            $this->appOs = 'mobile';
        } else {
            $this->appOs = $appOs;
        }
    }

    /**
     * @param mixed $appName
     * @return CompatibilityFieldMapper
     */
    public function setAppName($appName)
    {
        $this->appName = $appName;
        return $this;
    }

}
