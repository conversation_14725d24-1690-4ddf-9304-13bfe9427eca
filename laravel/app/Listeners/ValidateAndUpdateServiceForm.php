<?php

namespace App\Listeners;

use App\Events\LocalEntityBeforeUpdate;
use Izam\Booking\Utils\ServiceFormUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Helpers\Saver\Formatter\FormatterManger;
use App\Modules\LocalEntity\Helpers\Saver\SaverManager;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use Illuminate\Validation\ValidationException;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class ValidateAndUpdateServiceForm
{
    public function __construct(
        protected AppEntityShowAction $showAction,
        protected AppEntityStructureGetter $structureGetter,
        protected SaverManager $appEntitySaver,
        protected AppEntityMetaRepository $repository,
        protected EntityRulesGetter $entityRulesGetter
    ) {
    }

    public function handle(LocalEntityBeforeUpdate $event): void
    {
        $entityKey = $event->request->get('parent_entity');
        if (EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY != $entityKey) {
            return;
        }
        if (str_starts_with($event->request->get('key'), ServiceFormUtil::getAdditionalPrefix())) {
            $id = str_replace(ServiceFormUtil::getAdditionalPrefix(), '', $event->request->get('key'));
        }
        if (empty($id)) {
            return;
        }
        $data = $event->request->all()[EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY];
        if (empty($data)) {
            return;
        }
        $data['id'] = $id;
        $this->entityRulesGetter->useRelationName = false;
        $validator = new DaftraValidator($entityKey, $this->entityRulesGetter);
        $validator->setData($data);
        if (!$validator->isValid()) {
            throw ValidationException::withMessages($validator->getErrors());
        }

        $formattedRequest = FormatterManger::format($data, $entityKey, $this->repository);
        $structure = $this->structureGetter->buildEntity($entityKey);
        $id = $this->appEntitySaver->save($structure, $formattedRequest);
        $recordData = $this->showAction->handle($entityKey, $id, 2);

        $event->request->merge([
            'key' => ServiceFormUtil::getAdditionalPrefix() . $recordData['id'],
            'status' => 1,
            'name_field' => 'id',
            'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
        ]);
    }
}
