<?php
namespace App\Listeners;

use App\Events\LocalEntityBeforeCreate;
use Izam\Booking\Utils\ServiceFormUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Modules\LocalEntity\Actions\AppEntityShowAction;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\Helpers\Saver\Formatter\FormatterManger;
use App\Modules\LocalEntity\Helpers\Saver\SaverManager;
use App\Modules\LocalEntity\Repositories\AppEntityMetaRepository;
use App\Modules\LocalEntity\Validators\DaftraValidator;
use App\Modules\LocalEntity\Helpers\EntityRulesGetter;
use Illuminate\Validation\ValidationException;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class ValidateAndCreateServiceForm
{
    public function __construct(
        protected AppEntityShowAction $showAction,
        protected AppEntityStructureGetter $structureGetter,
        protected SaverManager $appEntitySaver,
        protected AppEntityMetaRepository $repository,
        protected EntityRulesGetter $entityRulesGetter
    ) {
    }

    public function handle(LocalEntityBeforeCreate $event): void
    {
        $entityKey = $event->request->get('parent_entity');
        if (EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY != $entityKey) {
            return;
        }
        $data = $event->request->all()[EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY];
        if(empty($data)){
            return;
        }
        $this->entityRulesGetter->useRelationName = false;
        $validator = new DaftraValidator($entityKey, $this->entityRulesGetter);
        $validator->setData($data);

        if (!$validator->isValid()) {
            throw ValidationException::withMessages($validator->getErrors());
        }

        $formattedRequest = FormatterManger::format($data, $entityKey, $this->repository);
        $structure = $this->structureGetter->buildEntity($entityKey);
        $id = $this->appEntitySaver->save($structure, $formattedRequest);
        $recordData = $this->showAction->handle($entityKey, $id, 2);

        $event->request->merge([
            'key' => ServiceFormUtil::getAdditionalPrefix() . $recordData['id'],
            'status' => 1,
            'id' => $id,
            'name_field' => 'id',
            'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
        ]);
    }
}
