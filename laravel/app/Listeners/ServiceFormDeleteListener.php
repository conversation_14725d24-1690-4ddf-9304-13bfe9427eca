<?php

namespace App\Listeners;

use App\Events\ServiceFormDeleted;
use App\Modules\LocalEntity\Repositories\LocalEntityFieldRepository;
use App\Modules\LocalEntity\Repositories\LocalEntityRepository;
use Izam\Booking\Utils\ServiceFormUtil;

class ServiceFormDeleteListener
{
    public $connection = 'sync';

    public function __construct(
        private LocalEntityRepository $localEntityRepository,
        private LocalEntityFieldRepository $localEntityFieldRepository,
    )
    {
    }

    public function handle(ServiceFormDeleted $event)
    {
        $entityKey = ServiceFormUtil::getAdditionalPrefix() . $event->getData()['id'];
        $entity = getEntityBuilder()->buildEntity($entityKey);
        $deletedIds = array_map(fn($field) => $field->getId(), $entity->getFields());
        if (empty($deletedIds)) {
            return;
        }
        $this->localEntityFieldRepository->removeFields($deletedIds);
        $this->localEntityFieldRepository->deleteEntity($entityKey);
        $this->localEntityFieldRepository->deleteRelation(['entity' => $entityKey,]);
        $this->localEntityFieldRepository->deleteRelation(['reference_entity_key' => $entityKey]);
        $this->localEntityRepository->dropTable($entityKey);
    }
}
