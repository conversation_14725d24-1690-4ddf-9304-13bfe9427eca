<?php
namespace App\Helpers\BreadCrumbs;

use App\Helpers\BreadCrumbs\Concrete\ContractInstallmant;
use App\Helpers\BreadCrumbs\Concrete\EmailLog;
use App\Helpers\BreadCrumbs\Concrete\EntityDocument;
use App\Helpers\BreadCrumbs\Concrete\GeneralEntityBreadcrumb;
use App\Helpers\BreadCrumbs\Concrete\ItemGroup;
use App\Helpers\BreadCrumbs\Concrete\LeaseContract;
use App\Helpers\BreadCrumbs\Concrete\LeaveApplication;
use App\Helpers\BreadCrumbs\Concrete\ManufacturingOrder;
use App\Helpers\BreadCrumbs\Concrete\ManufacturingOrderIndirectCost;
use App\Helpers\BreadCrumbs\Concrete\RequestType;
use App\Helpers\BreadCrumbs\Concrete\MultiLevelApporvalConfiguration;
use App\Helpers\BreadCrumbs\Concrete\WorkflowType;
use App\Helpers\BreadCrumbs\Concrete\Vehicle;
use App\Helpers\BreadCrumbs\Concrete\Workstation;
use Illuminate\Support\Str;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

/**
 * creates
 * Class BreadCrumbsFactory
 * @package App\Helpers\BreadCrumbs
 * <AUTHOR> azzam <<EMAIL>>
 */
class BreadCrumbsFactory{

    private function __construct()
    {
    }

    static function create($repo, $url, $entity = null)
    {
        $specialBreadCrumbs = [
            'workflow_type' => WorkflowType::class,
            EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => ManufacturingOrderIndirectCost::class,
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => ManufacturingOrder::class,
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => MultiLevelApporvalConfiguration::class,
            EntityKeyTypesUtil::EMAIL_TEMPLATE_LOG => EmailLog::class,
            EntityKeyTypesUtil::VEHICLE => Vehicle::class,
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => ContractInstallmant::class,
            EntityKeyTypesUtil::LEASE_CONTRACT => LeaseContract::class,
            EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => Workstation::class,
            EntityKeyTypesUtil::REQUEST_TYPE_ENTITY_KEY => RequestType::class,
            EntityKeyTypesUtil::ENTITY_DOCUMENT => EntityDocument::class,

            EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY => Workstation::class,
            EntityKeyTypesUtil::REQUEST_TYPE_ENTITY_KEY => RequestType::class,

        ];

        if ('LocalEntityDatum' === $repo->getEntitySingularName()) {
            $key = $entity->getEntityKey();
            if (isset($specialBreadCrumbs[$key])) {
                $className = $specialBreadCrumbs[$key];
                return new $className($repo, $url, $entity);
            }
        }

        if(
        class_exists('App\\Helpers\\BreadCrumbs\\Concrete\\'.Str::studly($repo->getEntitySingularName()))
        ){
            $class_name = '\\App\\Helpers\\BreadCrumbs\\Concrete\\'.Str::studly($repo->getEntitySingularName());
        } else if ( class_exists('App\\Modules\\LocalEntity\\Helpers\\BreadCrumbs\\Concrete\\'.Str::studly($repo->getEntitySingularName()))) {
            $class_name = '\\App\\Modules\\LocalEntity\\Helpers\\BreadCrumbs\\Concrete\\'.Str::studly($repo->getEntitySingularName());
        }else{
            $class_name = '\\App\\Helpers\\BreadCrumbs\\Concrete\\Ordinary';
        }
        return new $class_name($repo,$url, $entity);
    }

}


?>
