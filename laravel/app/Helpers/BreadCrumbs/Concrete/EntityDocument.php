<?php

namespace App\Helpers\BreadCrumbs\Concrete;

use App\Modules\LocalEntity\Helpers\BreadCrumbs\Concrete\LocalEntityDatum;
use App\Utils\EntityKeyTypesUtil;
use Izam\Entity\Repository\DynamicRepo;
use Izam\Rental\Repository\LeaseContractRepository;

class EntityDocument extends LocalEntityDatum
{

    public function getParents()
    {
        $document = $this->data;
        $entity_id  = $document?->entity_id ?? (request()->subEntityId ?? null);
        $entity_key = $document?->entity_key ?? (request()->subEntityKey ?? null);
        if(in_array($this->pageData['type'] , [self::BC_ADD_PAGE_TYPE , self::BC_EDIT_PAGE_TYPE , self::BC_VIEW_PAGE_TYPE])) {
            $breadcrumbs = $this->resolveParentEntityBreadcrumbs($document , $entity_id , $entity_key);
            if ($this->pageData['type'] == self::BC_EDIT_PAGE_TYPE){
                $breadcrumbs[] =  [
                    'title' => sprintf(__t('Document #%s'), $document->id),
                    'link' => route('owner.entity.show',[EntityKeyTypesUtil::ENTITY_DOCUMENT,$document->id])
                ];
            }
            return $breadcrumbs ;
        }
        else {
            return [
                [
                    'title' => __t("Documents"),
                    'link'  => $entity_id? route('owner.staff.show',[$entity_id]). '#documents':'#'
                ],

            ];
        };


    }


    /**
     * @throws \Exception
     */
    public function resolveParentEntityBreadcrumbs($document , string $entity_id, string $entity_key): array
    {
        $defaultBreadcrumbs = [
            [
                'title' => __t("Documents"),
                'link'  => $entity_id? route('owner.staff.show',[$entity_id]) . '#documents':'#'
            ]
        ];

        /** @var DynamicRepo $dynamicRepo */
        $dynamicRepo = resolve(DynamicRepo::class);
        $model = $dynamicRepo->getByEntityKey($entity_key , ['id'=> $entity_id]);

        if (!empty($model)) {
            return match ($entity_key) {
                EntityKeyTypesUtil::STAFF_ENTITY_KEY => array_merge($this->getStaffDocumentsBreadCrumbs($model[0], $document) , $defaultBreadcrumbs),
                default => $defaultBreadcrumbs
            };
        }
        return $defaultBreadcrumbs;
    }

    private function getStaffDocumentsBreadCrumbs($staff): array
    {
        return [
            [
                'title' => __t("Employees"),
                'link'  => route('owner.entity.list',[ 'entityKey' =>'staff' ])
            ],
            [
                'title' => $staff['full_name'] . ' #' . $staff['code'],
                'link' =>route('owner.staff.show',['staff'=>$staff['id']])
            ],
        ];
    }


}
