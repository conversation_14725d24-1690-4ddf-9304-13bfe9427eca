<?php

declare(strict_types=1);

namespace App\Helpers\BreadCrumbs\Concrete;

use App\Modules\LocalEntity\Helpers\BreadCrumbs\Concrete\LocalEntityDatum;
use App\Services\JournalAccountService;
use App\Utils\EntityKeyTypesUtil;
use Illuminate\Support\Str;

class MultiLevelApporvalConfiguration extends LocalEntityDatum
{
    public function getListingTitle()
    {
        $listingTitle = [];
        $listingTitle['title'] = __t('Approval Configurations');
        $listingTitle['link'] = route('owner.entity.list' ,  [
            'entityKey'=>$this->entity->getEntityKey(),
            'reset'=>1
        ]);

        return $listingTitle;
    }

    public function getViewTitle()
    {
        $viewTitle = parent::getViewTitle(); // TODO: Change the autogenerated stub
        if(isset($this->data)) {
            $value = json_decode($this->data->value, true);
            if(isset($value['name'])) {
                $viewTitle['title'] = $value['name'] . ' #' . $this->data->id;
            }
        }
        return $viewTitle;
    }

    public function getParents()
    {
        $parents = parent::getParents();
        array_unshift($parents,
            ['link' => route('owner.attendance_settings'), 'title' => __t('Attendance Settings')],
        );
        return $parents;
    }

}
