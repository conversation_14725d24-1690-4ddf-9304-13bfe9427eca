<?php

use App\Adapters\InternalHttpAdapter;
use App\Facades\Branch;
use App\Facades\CakeSession;
use App\Facades\Formatter;
use App\Facades\Permissions;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Helpers\QueueConnectionConfigurationGetter;
use App\Helpers\uCal;
use App\Helpers\uCalNew;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Utils\CreditChargeStatusUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\ItemStaffUtil;
use App\Utils\Memberships\MemberShipStatusUtil;
use App\Utils\PluginUtil;
use App\Utils\SharedModelsUtil;
use App\Utils\SiteStatusUtil;
use Carbon\Carbon;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Symfony\Component\Process\Process;
use Izam\Daftra\Common\Component\Sidebar;
use Illuminate\Support\Str;
use App\Models\Site;
use App\Models\Staff;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use App\Facades\CurrencyConverter;
use Izam\Daftra\Common\Utils\EntityEmailPrefrenceEntityKeyUtil;
use Izam\Template\Models\EmailLog;

if (!function_exists('getStringFromArrayRecursively')) {
    function getStringFromArrayRecursively($array, $keysString = '')
    {
        $result = '';
        if (is_array($array)) {
            foreach ($array as $key => $value) {
                $result .= getStringFromArrayRecursively($value, !empty($keysString) ? "{$keysString}.{$key}" : "{$key}");
            }
        } else {
            $result .= '<b>' . $keysString . ':</b> ' . $array . ', &nbsp;';
        }
        return $result;
    }
}


if (!function_exists('getActionUrl')) {
    /**
     * @param $url
     * @param $recordData
     * @return string
     */
    function getActionUrl($url, $recordData)
    {
        if(isset($url['callback'])){
            return $url['callback']($recordData);
        }
        $parameters = [];
        if (isset($url['parameters'])) {
            //setting paramter values
            foreach ($url['parameters'] as $placeHolder => $parameter) {
                if (isset($parameter['value'])) {
                    $parameterValue = $parameter['value'];
                } else if (isset($parameter['model']) && !empty($parameter['model'])) {
                    $parameterValue = $recordData->{$parameter['model']}->{$parameter['field']};
                } else {
                    $parameterValue = $recordData->{$parameter['field']};
                }
                $parameters[$placeHolder] = $parameterValue;
            }
        }
        if (isset($url['query'])) {
            $parameters = array_merge($parameters, $url['query']);
        }
        $url = route($url['name'], $parameters);
        return $url;
    }
}

if (!function_exists('isLoggedAsAdmin')) {
    function isLoggedAsAdmin($field = null) {
        if(!$field){
            return !empty($_SESSION['LOGGED_AS_ADMIN']);
        }else{
            return isset($_SESSION['LOGGED_AS_ADMIN'][$field]);
        }
    }
}

if (!function_exists('getEntityOrLocalEntity')) {
    function getEntityOrLocalEntity(string $value) {
        if (substr($value, 0, 3)=== 'le_') {
            return app()->make(\App\Modules\LocalEntity\Repositories\LocalEntityRepository::class)->find($value);
        }
        return app()->make(\App\Repositories\EntityRepository::class)->find($value);
    }
}

if (!function_exists('getCountriesCode')) {
    function getCountriesCode()  {
        return json_decode(file_get_contents(resource_path('utils/countries_code.json')), true);
    }
}
if (!function_exists('getIcons')) {
    function getIcons()  {
        return json_decode(file_get_contents(resource_path('utils/icons.json')), true);
    }
}

if (!function_exists('getDropDownColors')) {
    function getDropDownColors()  {
        return array(
            'white' => array('background' => 'white', 'color' => 'black'),
            'silver' => array('background' => 'silver', 'color' => 'white'),
            'gray' => array('background' => 'gray', 'color' => 'white'),
            'black' => array('background' => 'black', 'color' => 'white'),
            'navy' => array('background' => 'navy', 'color' => 'white'),
            'blue' => array('background' => 'blue', 'color' => 'white'),
            'teal' => array('background' => 'teal', 'color' => 'white'),
            'aqua' => array('background' => 'aqua', 'color' => 'white'),
            'green' => array('background' => 'green', 'color' => 'white'),
            'lime' => array('background' => 'lime', 'color' => 'white'),
            'chartreuse' => array('background' => 'chartreuse', 'color' => 'white'),
            'olive' => array('background' => 'olive', 'color' => 'white'),
            'yellow' => array('background' => 'yellow', 'color' => 'black'),
            'gold' => array('background' => 'gold', 'color' => 'white'),
            'orange' => array('background' => 'orange', 'color' => 'white'),
            'red' => array('background' => 'red', 'color' => 'white'),
            'maroon' => array('background' => 'maroon', 'color' => 'white'),
            'fuchsia' => array('background' => 'fuchsia', 'color' => 'white'),
            'purple' => array('background' => 'purple', 'color' => 'white'),
            'indigo' => array('background' => 'indigo', 'color' => 'white'),
        );
    }
}

if (!function_exists('getCountryPhoneCode')) {

    function getCountryPhoneCode($countryCode)  {
        $result = Cache::get('countries_phone_codes');
        if(!$result) {
            $result = json_decode(file_get_contents(resource_path('utils/country_code_to_phone.json')), true);
            Cache::set('countries_phone_codes', $result);
        }
        return isset($result[$countryCode]) ? $result[$countryCode] : null;
    }
}

if (!function_exists('getRowAction')) {
    /**
     * evaluate listing row action using the record data and the action data
     * @param $action data
     * @param $data record data
     * @param $newTab should action open in new tab
     * @return string the action url
     */
    function getRowAction($action, $data, $entityKey=false, $pageTitle = null, $newTab = false)
    {
        if (isset($action['check'])) {
            //a check function to check if the action should be shown
            $check = $action['check']($data);
            if (!$check) {
                return null;
            }
        }

        $url = getUrlAction($action, $data);
        $href= $url;
        $url = $url ? "href = '$url'" : '';
        $isIzamView = !empty($_GET['izamView']);

        $attributes = isset($action['attributes']) ? arrayToHtmlAttributes($action['attributes'], false) : '';
        if (isset($action['class'])) {
            $class = $action['class'];
        } else {
            $class = '';
        }

        if (isset($action['icon'])) {
            $icon = $action['icon'];
        } else {
            $icon = '';
        }
        $promptText = '';

        if (isset($action['prompt']) && !$isIzamView) {
            $prompt = $action['prompt'];

            $prompt['message'] = sprintf(__t($prompt['message']), __t($pageTitle??''));

            if($entityKey)
                $promptText = 'data-toggle="modal" data-target="#promptModal'.$entityKey.'" data-prompt="true" data-prompt-message="' . $prompt['message'] . '" ';
            else
                $promptText = 'data-toggle="modal" data-target="#promptModal" data-prompt="true" data-prompt-message="' . $prompt['message'] . '" ';
            if (isset($prompt['header'])) $promptText .= ' data-prompt-header="' . $prompt['header'] . '" ';
            if (isset($prompt['method'])) $promptText .= ' data-prompt-method="' . $prompt['method'] . '" ';
            if(!isset($_GET['crm'])){
                if (isset($prompt['global'])) $promptText .= ' data-prompt-global="' . $prompt['global'] . '" ';
            }
        }elseif(isset($action['prompt']) && $isIzamView){
            $prompt = $action['prompt'];
            $promptText = 'data-bs-toggle="modal" data-bs-target="#modalDelete" data-prompt="true" data-md-message="' . $prompt['message'] . '" ';
            if (isset($prompt['method'])) $promptText .= ' data-md-method="POST" ';
            $data = [
                '_method' => $prompt['method'],
                '_token' => csrf_token()
            ];
            $jsonData = htmlspecialchars(json_encode($data, JSON_HEX_QUOT | JSON_HEX_APOS));
            $promptText .= 'data-md-data="' . $jsonData . '"';
            $promptText .= ' data-md-action="' . $href . '" ';
        }

        $target = '';
        if(!isset($action['prompt']) && $newTab){
            $target = 'target="_blank"';
        }

        $title = __t($action['title']);
        $html = <<<EOD
                    <a $promptText $url class="$class dropdown-item" $attributes $target >$icon $title</a>
EOD;
        return $html;
    }
}


if (!function_exists('getIzamRowAction')) {
    /**
     * evaluate listing row action using the record data and the action data
     * @param $action array
     * @param $data record data
     * @return string the action url
     */
    function getIzamRowAction($action, $data, $entityKey=false, $pageTitle = null)
    {
        if (isset($action['check'])) {
            //a check function to check if the action should be shown
            $check = $action['check']($data);
            if (!$check) {
                return null;
            }
        }
        $url = getUrlAction($action, $data);
        $href = $url ? "href = '$url'" : '';
        if (isset($action['action']) && 'destroy' == $action['action']) {
            return "<button class='dropdown-item' type='button'
                        data-bs-target='" . $action['data']['target'] . "'
                        data-bs-toggle='" . $action['data']['toggle'] . "'
                        data-md-message='" . sprintf(__t($action['data']['message']), __t($entityKey??'')). "'
                        data-md-action='" . $url . "'
                        data-md-method='" . $action['data']['method'] . "'
                        data-md-data='" . $action['data']['data'] . "'>
                    <i class='mdi mdi-delete text-danger'></i>
                    <span>" . __t($action['title']) . "</span>
                </button>";
        }

        $attributes = isset($action['attributes']) ? arrayToHtmlAttributes($action['attributes'], false) : '';
        if (isset($action['class'])) {
            $class = $action['class'];
        } else {
            $class = '';
        }
        if (isset($action['icon'])) {
            $icon = $action['icon'];
        } else {
            $icon = '';
        }
        $promptText = '';
        if (isset($action['prompt'])) {
            $prompt = $action['prompt'];
            $prompt['message'] = sprintf(__t($prompt['message']), __t($pageTitle??''));
            // @TODO: revisit entities that delete with cake modal, ex : purchase order
            $dataAttributes = [
                '_method' => $prompt['method'] ?? "DELETE",
                '_token' => csrf_token()
            ];
            if (isset($_GET['iframe']) && isset($_GET['redirect_to_entity']) && isset($_GET['redirect_to_entity_id']) ){
                $dataAttributes['redirect_to_entity']= $_GET['redirect_to_entity'];
                $dataAttributes['redirect_to_entity_id']= $_GET['redirect_to_entity_id'];
            }
            $jsonData = htmlspecialchars(json_encode($dataAttributes, JSON_HEX_QUOT | JSON_HEX_APOS));
            $promptText = 'data-md-action="'.getUrlAction($action, $data).'"  data-md-method="POST" data-md-message="'.$prompt['message'].'" data-md-data='. "'" .$jsonData ."'"  ;
            if (isset($prompt['header'])) $promptText .= ' data-prompt-header="' . $prompt['header'] . '" ';
            if (isset($prompt['method'])) $promptText .= ' data-prompt-method="' . $prompt['method'] . '" ';
        }
        $params = '';
        if (isset($_GET['iframe'])){
            $params = 'target=_blank';
        }
        $title = __t($action['title']);
        $html = <<<EOD
            <li><a $promptText $href class="$class dropdown-item" $attributes $params> $icon  <span>$title</span> </a></li>
EOD;
        return $html;
    }
}

function getUrlAction($action, $data) {
    if(isset($action['link']) && !isset($action['url'])){
        $url = $action['link'];
        $url = preg_replace_callback('/\%(\w+)/', function($matches) use($data){
            $key = $matches[1];
            if($data->$key){
                return $data->$key;
            }
        }, $url);
    }else {
        $url = isset($action['url']) ? getActionUrl($action['url'], $data) : '';
    }
    if (($action['title'] == 'View' || $action['title'] == 'Edit') && !empty($url)) {
        if (isset($_GET['iframe']) &&  $action['title'] == 'Edit'){
            request()->merge(['backUrl' => urlencode(request()->headers->get('referer'))]);
        }
        $queryParamsString = http_build_query(request()->except(['iframe','hide_actions']));
        if (strpos($url,'?' ) !== false) {
            $url .= '&';
        } else {
            $url .='?';
        }
        $url .= $queryParamsString;
    }
    return $url;
}
//Auth Functions
if (!function_exists('getCountyByIP')) {
    function getCountyByIP()
    {
//        if (isset($_GET['country'])) {
//            return $_GET['country'];
//        }
//        $CountryIp = ClassRegistry::init(array('class' => 'CountryIp'));
//        $currentIP = sprintf('%u', ip2long($_SERVER['REMOTE_ADDR']));
//        $code = $CountryIp->field('code', array("'$currentIP' BETWEEN `start` AND `end`"));
//        return $code;
        return 'EG';
    }
}
if (!function_exists('getCurrentSite')) {
    /**
     * Get Current Site Data
     *
     * @param null|string $field
     * @return ArrayObject|string
     */
    function getCurrentSite($field = null)
    {
        $currentSite = CakeSession::read('CurrentSite');
        if (!$currentSite) {
            return false;
        }
        if (!isset($currentSite['language_code_code3']) and $field == 'language_code_code3') {
            if ($currentSite['language_code'] == 0) {
                return Site_Lang;
            } else {
                /** @var \App\Repositories\LanguageRepository $languageRepo */
                $languageRepo = app()->make('\App\Repositories\LanguageRepository');
                $site_lang_code = $languageRepo->get_language_code($currentSite['language_code'], 'code3');
                return $site_lang_code;
            }
        }
        if ($field == 'country_code' && $currentSite[$field] == "") {
            return getCountyByIP();
        }
        if (!empty($field) && isset($currentSite[$field])) {
            return $currentSite[$field];
        } elseif (!empty($field) && !isset($currentSite[$field])) {
            return '';
        }
        return $currentSite;
    }
}
if (!function_exists('getCustomDomain')) {
    function getCustomDomain()
    {
        $customDomainRepository = resolve(\App\Repositories\CustomDomainRepository::class);
        $customDomain = $customDomainRepository->getCustomDomainBySiteId(getCurrentSite('id'));
        if (!empty($customDomain)) {
            return $_SESSION['CurrentSite']['custom_domain'] = $customDomain['domain'];
        }
        return null;
    }
}

if (!function_exists('getAuthOwner')) {
    /**
     * @param String $field The field required from owner data
     * @return mixed <ul>
     *  <li> If $field is not empty and found in user data return its value, else if it is not found in owner data return empty string.<br />
     *    <li> If $field is empty return the whole owner data<br />
     *  <li> If no owner is logged in return false
     * </ul>
     */
    function getAuthOwner($field = null)
    {
        $owner = CakeSession::read('OWNER');
        /**
         * 'cli' refers to cron job
         */
        if (!is_null($owner)) {
            if (isset($owner["staff_id"]) && $owner["staff_id"] != 0) {
                $owner["activity_log_actor_type"] = \App\Utils\ActivityLogActorTypesUtil::STAFF;
            } else {
                $owner["activity_log_actor_type"] = \App\Utils\ActivityLogActorTypesUtil::OWNER;
            }
        } else if (PHP_SAPI == 'cli' ) {
            $owner = getCurrentSite();
            $owner["staff_id"] = -2;
            $owner["activity_log_actor_type"] = \App\Utils\ActivityLogActorTypesUtil::CRON_JOB;
        } else if (isApi()) {
            $owner = getCurrentSite();
            $owner["staff_id"] = empty(getAuthStaff('id')) ? -1  : getAuthStaff('id') ;
            $owner["activity_log_actor_type"] = \App\Utils\ActivityLogActorTypesUtil::API;
        }
        if (!$owner) {
            return false;
        }
        if (!empty($field) && isset($owner[$field])) {
            return $owner[$field];
        } elseif (!empty($field) && !isset($owner[$field])) {
            return '';
        }
        return $owner;
    }
}
if (!function_exists('getAuthStaff')) {
    /**
     * @param String $field The field required from staff data
     * @return mixed <ul>
     *  <li> If $field is not empty and found in user data return its value, else if it is not found in staff data return empty string.<br />
     *    <li> If $field is empty return the whole staff data<br />
     *  <li> If no staff is logged in return false
     * </ul>
     */
    function getAuthStaff($field = null)
    {
        $staff = CakeSession::read('STAFF');
        if (defined('TRUE_REST') && TRUE_REST) {
            $staff = getAuthOwner($field);
            $staff['id'] = -1;
        }
        if (!$staff) {
            return false;
        }
        if (!empty($field) && isset($staff[$field])) {
            return $staff[$field];
        } elseif (!empty($field) && !isset($staff[$field])) {
            return '';
        }
        return $staff;
    }
}

if (!function_exists('isStaff')) {
    /**
     * @return bool
     */
    function isStaff()
    {
        return getAuthOwner('staff_id') !== 0 && getAuthOwner('staff_id') !== null;
    }
}

if (!function_exists('isOwner')) {
    /**
     * @return bool
     */
    function isOwner()
    {
        return (getAuthOwner('staff_id') == Staff::OWNER_STAFF_ID) || (getAuthStaff('role_id') == Staff::OWNER_ROLE_ID);
    }
}

if (!function_exists('getAuthClient')) {
    function getAuthClient($field = null)
    {
        $client = CakeSession::read('CLIENT');
        if (!$client) {
            return false;
        }
        if (!empty($field) && isset($client[$field])) {
            return $client[$field];
        } elseif (!empty($field) && !isset($client[$field])) {
            return '';
        }
        return $client;
    }
}
//Date Functions

if (!function_exists('translateCarbonAgo')) {
    function translateCarbonAgo($carbonObject)
    {
        $time  = explode(' ',$carbonObject);
        if((int)$time[0] > 2 && (int)$time[0] < 10 ){
            $msg = str_replace($time[0],'%s',$carbonObject);
            $translated = __t($msg);
            $translatedWitNumber = sprintf($translated,(int)$time[0]);

        }elseif($time[0] == 2)
            $translatedWitNumber = __t($carbonObject);
        else{
            $msg = str_replace($time[0],'%d',$carbonObject);
            $translated = __t($msg);
            $translatedWitNumber = sprintf($translated,(int)$time[0]);
        }
        return $translatedWitNumber;
    }
}

if (!function_exists('format_date')) {
    /**
     * Format given date to owner date
     * @param $date
     * @param boolean $default_format set to false will not get current site date_format and will return date separated with -
     * @param bool $isDateTime whether return datetime or only date
     * @return string formatted with owner date setting
     */
    function format_date($date, $default_format = false, $isDateTime = false, $seconds = false, $enableArabicFormat = true,$apply_24_Format = false)
    {
        if ($default_format === false) {
            $default_format = getCurrentSite('date_format');
        }
        $default_format = $default_format != '' ? $default_format : 0;

        $date_foramts = getDateFormats('ICU');
        if ($enableArabicFormat && isset($date_foramts[$default_format]['is_hijiri'])&& $date_foramts[$default_format]['is_hijiri']) {
            if ($isDateTime) {
                $date_foramts[$default_format]['ICU_Format'] .= " H:i";
            }
            if (in_array(getCurrentSite('id'), ['2568501', '2234602'])) {
                $dateFormatter = new uCalNew();
            } else {
                $dateFormatter = new uCal();
            }
            $dateFormatter->setLang(is_rtl() ? 'ar' : 'en');
            $new = $dateFormatter->date($date_foramts[$default_format]['ICU_Format'], strtotime($date), 1);
            return $new;
        } else {
            $months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
            foreach ($months as $key => $month) {
                $new_months[$month] = $enableArabicFormat ? __t($month) : $month;
            }
            foreach ($months as $key => $month) {
                $partOfMonth = substr($month, 0, 3);
                $new_months[substr($month, 0, 3)] = $enableArabicFormat ? __t($partOfMonth) : $partOfMonth;
            }
            if (!(strtotime($date))) {
                return '';
            }
            if ($default_format === false) {
                $default_format = getCurrentSite('date_format');
            }
            $date_foramts = getDateFormats('std');
            if ($isDateTime) {
                if($seconds){
                    $date_foramts[$default_format] .= " H:i:s";
                }elseif($apply_24_Format){
                    $date_foramts[$default_format] .= " h:i a";
                }else{
                    $date_foramts[$default_format] .= " H:i";
                }
            }
            $new = str_replace(array_keys($new_months), array_values($new_months), date($date_foramts[$default_format], strtotime($date)));
            return $new;
        }
    }
}
if (!function_exists('getDateFormats')) {
    function getDateFormats($list = false)
    {
        $formats = [
            '%d/%m/%Y' => ['is_hijiri' => false, 'ICU_Format' => 'dd/MM/y', 'js' => 'dd/mm/yy', 'new_js' => 'dd/mm/yyyy', 'moment_js' => 'DD/MM/YYYY', 'std' => 'd/m/Y', 'mysql' => '%d/%m/%Y', 'human' => 'dd/mm/yyyy', 'explode' => '1', 'delimiter' => '/'],
            '%d-%m-%Y' => ['is_hijiri' => false, 'ICU_Format' => 'dd-MM-y', 'js' => 'dd-mm-yy', 'new_js' => 'dd-mm-yyyy', 'moment_js' => 'DD-MM-YYYY', 'std' => 'd-m-Y', 'mysql' => '%d-%m-%Y', 'human' => 'dd-mm-yyyy', 'explode' => '1', 'delimiter' => '-'],
            '%d.%m.%Y' => ['is_hijiri' => false, 'ICU_Format' => 'dd.MM.y', 'js' => 'dd.mm.yy', 'new_js' => 'dd.mm.yyyy', 'moment_js' => 'DD.MM.YYYY', 'std' => 'd.m.Y', 'mysql' => '%d.%m.%Y', 'human' => 'dd.mm.yyyy', 'explode' => '1', 'delimiter' => '.'],
            '%m/%d/%Y' => ['is_hijiri' => false, 'ICU_Format' => 'MM/dd/y', 'js' => 'mm/dd/yy', 'new_js' => 'mm/dd/yyyy', 'moment_js' => 'MM/DD/YYYY', 'std' => 'm/d/Y', 'mysql' => '%m/%d/%Y', 'human' => 'mm/dd/yyyy', 'explode' => '1', 'delimiter' => '/'],
            '%m-%d-%Y' => ['is_hijiri' => false, 'ICU_Format' => 'MM-dd-y', 'js' => 'mm-dd-yy', 'new_js' => 'mm-dd-yyyy', 'moment_js' => 'MM-DD-YYYY', 'std' => 'm-d-Y', 'mysql' => '%m-%d-%Y', 'human' => 'mm-dd-yyyy', 'explode' => '1', 'delimiter' => '-'],
            '%Y/%m/%d' => ['is_hijiri' => false, 'ICU_Format' => 'y/MM/dd', 'js' => 'yy/mm/dd', 'new_js' => 'yyyy/mm/dd', 'moment_js' => 'YYYY/MM/DD', 'std' => 'Y/m/d', 'mysql' => '%Y/%m/%d', 'human' => 'yyyy/mm/dd', 'explode' => '1', 'delimiter' => '/'],
            '%Y-%m-%d' => ['is_hijiri' => false, 'ICU_Format' => 'y-MM-dd', 'js' => 'yy-mm-dd', 'new_js' => 'yyyy-mm-dd', 'moment_js' => 'YYYY-MM-DD', 'std' => 'Y-m-d', 'mysql' => '%Y-%m-%d', 'human' => 'yyyy-mm-dd', 'explode' => '1', 'delimiter' => '-'],
            '%d %B %Y' => ['is_hijiri' => false, 'ICU_Format' => 'dd MMMM y', 'js' => 'dd MM yy', 'new_js' => 'dd MM yyyy', 'moment_js' => 'DD MMMM YYYY', 'std' => 'd F Y', 'mysql' => '%d %M %Y', 'human' => 'dd Month yyyy', 'explode' => '3', 'delimiter' => ' '],
            '%B %d %Y' => ['is_hijiri' => false, 'ICU_Format' => 'MMMM dd y', 'js' => 'MM dd yy', 'new_js' => 'MM dd yyyy', 'moment_js' => 'MMMM DD YYYY', 'std' => 'F d Y', 'mysql' => '%M %d %Y', 'human' => 'Month dd yyyy', 'explode' => '3', 'delimiter' => ' '],
            '%d %b %Y' => ['is_hijiri' => false, 'ICU_Format' => 'dd MMM y', 'js' => 'dd M yy', 'new_js' => 'dd M yyyy', 'moment_js' => 'DD MMM YYYY', 'std' => 'd M Y', 'mysql' => '%d %b %Y', 'human' => 'dd Mon yyyy', 'explode' => '3', 'delimiter' => ' '],
            '%b %d %Y' => ['is_hijiri' => false, 'ICU_Format' => 'MMM dd y', 'js' => 'M dd yy', 'new_js' => 'M dd yyyy', 'moment_js' => 'MMM DD YYYY', 'std' => 'M d Y', 'mysql' => '%b %d %Y', 'human' => 'Mon dd yyyy', 'explode' => '3', 'delimiter' => ' '],
        ];
        if (getCurrentSite('country_code') == 'SA') {
            foreach ($formats as $key => $value) {
                $value['is_hijiri'] = true;
                $formats['hijiri_' . $key] = $value;
            }
        }
        $retFormats = [];
        if ($list === true) {
            $date = date('y-m-d');
            $i = 0;
            foreach ($formats as $f => $jsFormat) {
                $retFormats[] = ($jsFormat['is_hijiri'] ? __t('Umm-Alqura') : '')
                    . ' ' . $jsFormat['human'] . ' (' . format_date($date, $i++) . ')';
            }
        } elseif ($list === false || $list == 'js') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['js'];
            }
        } elseif ($list == 'std') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['std'];
            }
        } elseif ($list == 'mysql') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['mysql'];
            }
        } elseif ($list == 'explode') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['explode'];
            }
        } elseif ($list == 'delimiter') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['delimiter'];
            }
        } elseif ($list == 'moment_js') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['moment_js'];
            }
        } elseif ($list == 'new_js') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['new_js'];
            }
        } elseif ($list == 'is_hijiri') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = $format['is_hijiri'];
            }
        } elseif ($list == 'ICU') {
            $retFormats = [];
            foreach ($formats as $format) {
                $retFormats[] = ['ICU_Format' => $format['is_hijiri'] ? $format['std'] : $format['ICU_Format'], 'is_hijiri' => $format['is_hijiri']];
            }
        } else {
            $retFormats = array_keys($formats);
            foreach ($retFormats as &$format)
                $format = str_replace('hijiri_', '', $format);
        }
        return $retFormats;
    }
}
if (!function_exists('dateInMysqlFormat')) {
    /**
     * @param $date
     * @return bool
     * checks if date in mysql format
     */
    function dateInMysqlFormat($date)
    {
        $format = 'Y-m-d';
        $d = DateTime::createFromFormat($format, $date);
        // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
        return $d && $d->format($format) === $date;
    }
}
if (!function_exists('db_format_date')) {
    function db_format_date($date = false, $format = false, $enableHijiriFormat = false)
    {
        $date = (string) $date;
        if(dateInMysqlFormat($date))
        {
            return $date;
        }
        $cformat = $format;
        $ar_months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
        $en_months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $en_months_short = ['Jan', 'Feb', 'March', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Sept'];
        $numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        if (preg_match('/(19|2[0-9]])\d\d-(0|1)\d-\d\d/', $date)) {
            return $date;
        }
        if (preg_match('/(19|2[0-9])\d\d-(0|1)\d-\d\d \d\d:\d\d:\d\d/', $date)) {
            return preg_replace('/\s+\d\d:\d\d:\d\d/', '', $date);
        }
        if (!is_numeric($format)) {
            $cformat = getCurrentSite('date_format');
        }
        $formats = getDateFormats('mysql');
        $isHijiriFormats = getDateFormats('is_hijiri');
        $format = $formats[$cformat];
        //Check if is hijiri
        if ($enableHijiriFormat && $isHijiriFormats[$cformat]) {
            $dateDelimiters = getDateFormats('delimiter');
            $dateFormatsStd = getDateFormats('std');
            if (in_array(getCurrentSite('id'), ['2568501', '2234602'])) {
                $uCalConverter = new uCalNew();
            } else {
                $uCalConverter = new uCal();
            }
            //Replace months short name & full name with its number
            $date = str_replace(array_values($uCalConverter->uM['ar']), $numbers, $date);
            $date = str_replace(array_values($uCalConverter->uF['ar']), $numbers, $date);
            $date = str_replace(array_values($uCalConverter->uM['en']), $numbers, $date);
            $date = str_replace(array_values($uCalConverter->uF['en']), $numbers, $date);
            //Extract Date components based on the delimiter
            $dateArr = explode($dateDelimiters[$cformat], $date);
            //Extract Format components based on the delimiter
            $dateFormat = explode($dateDelimiters[$cformat], $dateFormatsStd[$cformat]);
            //Combine the two arrays together by Assign Each date component to the appropriate element in the format
            $dateArr = array_combine($dateFormat, $dateArr);
            //Assign Each date component to the appropriate variable based on the format
            $day = $month = $year = '';
            foreach ($dateArr as $formatElement => $dateValue) {
                if (strpos(strtolower($formatElement), 'd') !== false)
                    $day = intval($dateValue);
                elseif (strpos(strtolower($formatElement), 'y') !== false)
                    $year = intval($dateValue);
                else
                    $month = intval($dateValue);
            }
            //Get Georgian Date from the hijiri
            $date = $uCalConverter->u2g($day, $month, $year);
            //Construct and return the date as y-m-d format which is the mysql date format
            return $date['year'] . '-' . str_pad($date['month'], 2, '0', STR_PAD_LEFT) . '-' . str_pad($date['day'], 2, '0', STR_PAD_LEFT);
        } elseif ($cformat == 9 or $cformat == 10) {
            $date = str_replace($ar_months, $en_months_short, $date);
        } else {
            $date = str_replace($ar_months, $en_months, $date);
        }
        $query = 'SELECT STR_TO_DATE("' . $date . '", "' . $format . '") AS date';
        $dbDate = DB::select($query);
        if (!preg_match("/(19|2[0-9])\d\d-\d\d-\d\d(\s*\d\d:\d\d:\d\d)?/i", $dbDate[0]->date)) {

            return false;
        }
        if ($dbDate) {
            return date('Y-m-d', strtotime($dbDate[0]->date));
        }
        return false;
    }
}
if (!function_exists('db_format_date_time')) {
    function db_format_date_time($date = false)
    {
        $datetime = trim($date );
        $datetime = preg_replace('/\s+/', ' ', $datetime );
        $datetime = explode(' ',$datetime);

        $time=end($datetime);
        $timeM = "";
        if(in_array($time, ['am','pm','AM','PM'])){
            $timeM = array_pop($datetime);
            $time=end($datetime);
        }

        $date=str_replace($time, '', $date);

        $date=db_format_date( trim($date));
        if($date==false){
            return false;
        }

        if($timeM !== ''){
            $parsed = Carbon::parse("{$date} {$time} {$timeM}")->toDateTimeString();
            $dateArr = explode(' ',$parsed);
            $time=end($dateArr);
        }

        if(preg_match('/\d\d?:\d\d:\d\d/', $time))  $time.='';
        else if(preg_match('/\d\d?:\d\d/', $time))  $time=$time.':00';
        else if(preg_match('/\d\d?/', $time))  $time=$time.':00:00';
        else if(!preg_match('/\d\d?:\d\d:\d\d/', $time))  $time='00:00:00';
        return $date.' '.$time;
    }
}
if (!function_exists('explode2string')) {
    function explode2string($array, $explode, $delimiter)
    {
        if (count($array) < $explode) {
            return false;
        }
        $output = "";
        for ($i = 0; $i < $explode; $i++) {
            if ($i == ($explode - 1)) {
                $output .= $array[$i];
            } else {
                $output .= $array[$i] . $delimiter;
            }
        }
        return trim($output);
    }
}
//Language Functions
if (!function_exists('is_rtl')) {
    function is_rtl($lang = null)
    {
        if (empty($lang)) $lang = CurrentSiteLang();
        return $lang === 'ara';
    }
}
if (!function_exists('CurrentSiteLang')) {
    function CurrentSiteLang($code_count = 'code3')
    {
        $session = CakeSession::get();
        $site = getCurrentSite();
        if (isset($_GET['ar'])) {
            $site['language_code_code3'] = "ara";
            $session->write('CurrentSite', $site);
            return "ara";
        }
        if (isset($_GET['en'])) {
            $site['language_code_code3'] = "eng";
            $session->write('CurrentSite', $site);
            return "eng";
        }
        if (request()->wantsJson() && request()->headers->get('lang')) {
            $site['language_code_code3'] = "eng";
            $session->write('CurrentSite', $site);
            return request()->headers->get('lang');
        }
        $client = getAuthClient();
        $staff = getAuthStaff();
        // If Client is login we set language to client language
        if ($client && isset($client['language_code']) && $client['language_code'] != "") {
            $language_code = $client['language_code'];
            $language_code_code3 = $client['language_code_code3'] ?? null;
            $level = 'Client';
            // If Staff is login we set language to staff language
        } else if ($staff && isset($staff['language_code']) && $staff['language_code'] != "") {
            $language_code = $staff['language_code'];
            $language_code_code3 = isset($staff['language_code_code3']) ? $staff['language_code_code3'] : null;
            $level = 'Staff';
            // Else we set language to current site language
        } else {
            $language_code = $site['language_code'];
            $language_code_code3 = isset($site['language_code_code3']) ? $site['language_code_code3'] : null;
            $level = 'CurrentSite';
        }
        if ($language_code == 0) {
            return (defined('Site_Lang' )? Site_Lang : '');
        } else {
            /** @var \App\Repositories\LanguageRepository $languageRepo */
            $languageRepo = app()->make('\App\Repositories\LanguageRepository');
            $language_code_code3 = $languageRepo->get_language_code($language_code, $code_count);
        }
        if (!empty($language_code_code3)) {
            return $language_code_code3;
        } else {
            return 'eng';
        }
    }
}
if (!function_exists('currency_name')) {
    function currency_name()
    {
        return  substr(CurrentSiteLang('code2'), 0, 2) == 'ar' ? 'ar_name' : 'name';
    }
}
//Price Functions
if (!function_exists('format_price_simple')) {
    /**
     * transform number to its related currency form
     * @param $price the price to format
     * @param bool $code currency code three letters i.e USD,EGP
     * @param bool $round
     * @return float|int|null|string|string[] the formatted number
     */
    function format_price_simple($price, $code = false, $round = true)
    {
        if (($price < 0.0000001 && $price > 0) || ($price > -0.0000001 && $price < 0))
            $price = 0;
        if (!$code)
            $code = getCurrentSite('currency_code');

        $lang = CurrentSiteLang('code2');
        $org_price = $price;
        if (!empty($code)) {
            $is_negative = 0;
            if ($price + 0 < -0.004) {
                $is_negative = 1;
                $price = $price * -1;
            }
            $code = strtoupper($code);
            if (!$number_formats = Cache::get($lang . '_number_formats')) {
                clearstatcache('currencies.php');
                $currencies = (include 'currencies.php'); //fix
                Cache::put($lang . '_number_formats', $number_formats, 60 * 24 * 7);
                Cache::put($lang . '_currencies', $currencies, 60 * 24 * 7);

            } else {
                $currencies = Cache::get($lang . '_currencies');
            }
            if ($code == 'EUR') {
                $country = getCurrentSite('country_code');
                if (isset($number_formats[$code . '-' . $country]) && $number_formats[$code . '-' . $country]) //fix
                    $code = $code . '-' . $country;
            }
            if (!isset($number_formats[$code]))  //fix
                $number_formats[$code] = [2, '.', ','];
            if ($code == 'INR')
                $price = in_number_format($price);
            else if (isset($number_formats[$code]) && $number_formats[$code]) {
                if ($number_formats[$code][0] == 0 && abs(floor($org_price * 10) - $org_price * 10) >= 0.05)
                    $number_formats[$code][0] = 2;
                if ($number_formats[$code][0] == 0 && abs(floor($org_price) - $org_price) >= 0.005)
                    $number_formats[$code][0] = 1;
                if (!$round) {
                    $oprice = doubleval($org_price);
                    $fractions_count = (strlen($oprice) - strlen(floor($oprice)) - 1);
                    if ($fractions_count > $number_formats[$code][0])
                        $number_formats[$code][0] = $fractions_count;
                    if ($number_formats[$code][0] > 6) $number_formats[$code][0] = 6;
                }
                $price = number_format($price, $number_formats[$code][0], $number_formats[$code][1], $number_formats[$code][2]);
                $x2 = $number_formats[$code][1];
                if ($x2 == '.') {
                    $x2 = '\\.';
                }
                $fractionappearing = settings::getValue(PluginUtil::InvoicesPlugin, 'invoice_fraction_appearing');
                if ((($org_price > 999.99 || $org_price < -999.99) && ($fractionappearing == "0" || empty($fractionappearing))) || $fractionappearing == "2") {
                    $price = preg_replace('/' . $x2 . '00$/', '', $price);
                }
            }
            //Replace Space with non-break Space
            $price = htmlspecialchars(($is_negative ? '-' : '') . str_replace(' ', ' ', $price));
            if ($is_negative && CurrentSiteLang() == 'ara') //fix
                $price = preg_replace('(-[\d\.,\$]+)', '<span dir="ltr">$0</span>', $price);
            return $price;
        }
        return number_format($price, 2);
    }
}
if (!function_exists('format_price')) {
    /**
     * same as format price simple but displays currency code with the price
     * @param $price
     * @param bool $code
     * @param bool $round
     * @return float|int|null|string|string[]
     */
    function format_price($price, $code = false, $round = true)
    {
        if (($price < 0.0000001 && $price > 0) || ($price > -0.0000001 && $price < 0))
            $price = 0;
        $lang = CurrentSiteLang('code2');
        if (!$code)
            $code = getCurrentSite('currency_code');
        $org_price = $price;
        if (!empty($code)) {
            $is_negative = 0;
            if ($price + 0 < -0.004) {
                $is_negative = 1;
                $price = $price * -1;
            }
            $code = strtoupper($code);
            if (!$number_formats = Cache::get($lang . '_number_formats')) {
                clearstatcache('currencies.php');
                $currencies = (include 'currencies.php'); //fix
                Cache::put($lang . '_number_formats', $number_formats, 60 * 24 * 7);
                Cache::put($lang . '_currencies', $currencies, 60 * 24 * 7);
            } else {
                $currencies = Cache::get($lang . '_currencies');
            }
            if ($code == 'EUR') {
                $country = getCurrentSite('country_code');
                if (isset($number_formats[$code . '-' . $country]) && $number_formats[$code . '-' . $country])
                    $code = $code . '-' . $country;
            }
            if ($code == 'INR')
                $price = in_number_format($price);
            else {
                if (!isset($number_formats[$code])) {
                    $number_formats[$code] = [2, '.', ','];
                    // added to handle un excpected $code like __c.
                    $currencies[$code] = str_replace('%0.2f', '%s', $currencies[$code] ?? '');
                }
                if ($number_formats[$code][0] == 0 && abs(floor($org_price * 10) - $org_price * 10) >= 0.05)
                    $number_formats[$code][0] = 2;
                if ($number_formats[$code][0] == 0 && abs(floor($org_price) - $org_price) >= 0.005)
                    $number_formats[$code][0] = 1;
                if (!$round) {
                    $oprice = doubleval($org_price);
                    $fractions_count = (strlen($oprice) - strlen(floor($oprice)) - 1);
                    if ($fractions_count > $number_formats[$code][0])
                        $number_formats[$code][0] = $fractions_count;
                    if ($number_formats[$code][0] > 6) $number_formats[$code][0] = 6;
                }
                $price = number_format($price, $number_formats[$code][0], $number_formats[$code][1], $number_formats[$code][2]);
                $x2 = $number_formats[$code][1];
                if ($x2 == '.')
                    $x2 = '\\.';
                $fractionappearing = settings::getValue(PluginUtil::InvoicesPlugin, 'invoice_fraction_appearing');
                if ((($org_price > 999.99 || $org_price < -999.99) && ($fractionappearing == "0" || empty($fractionappearing))) || $fractionappearing == "2") {
                    $price = preg_replace('/' . $x2 . '00$/', '', $price);
                }
            }
            $format = $currencies[$code];
            //Replace Space with non-break Space
            $price = htmlspecialchars(($is_negative ? '-' : '') . str_replace(' ', ' ', sprintf($format, $price)));
            if ($is_negative && CurrentSiteLang() == 'ara')
                $price = preg_replace('(-[\d\.,\$]+)', '<span dir="ltr">$0</span>', $price);
            return $price;
        }
        return number_format($price, 2);
    }
}
if (!function_exists('get_currency_formatted')) {
    /**
     * get currency formatted
     * @param string|null $code
     * @return float|int|string|string[]|null
     */
    function get_currency_formatted(string $code = null)
    {
        if (is_null($code)) {
            $code = getCurrentSite('currency_code');
        }
        $lang = CurrentSiteLang('code2');

        $code = strtoupper($code);
        if (!Cache::get($lang . '_number_formats')) {
            clearstatcache('currencies.php');
            $currencies = (include 'currencies.php');
            Cache::put($lang . '_currencies', $currencies, 60 * 24 * 7);
        } else {
            $currencies = Cache::get($lang . '_currencies');
        }
        $format = $currencies[$code];
        return trim(str_replace('%s', '', $format));
    }
}
if (!function_exists('in_number_format')) {
    function in_number_format($num)
    {
        $explrestunits = "";
        $num = preg_replace('/,+/', '', $num);
        $words = explode(".", $num);
        $des = "00";
        if (count($words) <= 2) {
            $num = $words[0];
            if (count($words) >= 2) {
                $des = $words[1];
            }
            if (strlen($des) < 2) {
                $des = "$des" . '0';
            } else {
                $des = substr($des, 0, 2);
            }
        }
        if (strlen($num) > 3) {
            $lastthree = substr($num, strlen($num) - 3, strlen($num));
            $restunits = substr($num, 0, strlen($num) - 3); // extracts the last three digits
            $restunits = (strlen($restunits) % 2 == 1) ? "0" . $restunits : $restunits; // explodes the remaining digits in 2's formats, adds a zero in the beginning to maintain the 2's grouping.
            $expunit = str_split($restunits, 2);
            for ($i = 0; $i < sizeof($expunit); $i++) {
                // creates each of the 2's group and adds a comma to the end
                if ($i == 0) {
                    $explrestunits .= (int)$expunit[$i] . ","; // if is first value , convert into integer
                } else {
                    $explrestunits .= $expunit[$i] . ",";
                }
            }
            $thecash = $explrestunits . $lastthree;
        } else {
            $thecash = $num;
        }
        return "$thecash" . (intval($des) > 0 ? '.' . $des : ''); // writes the final format where $currency is the currency symbol.
    }
}
if (!function_exists('format_number')) {
    /**
     * formats number used in qunatities
     * @param $number
     * @param bool $code
     * @param int $max_precision
     * @return int|string
     */
    function format_number($number, $code = false, $max_precision = 6)
    {
        $isNegative = $number < 0;
        if (($number < 0.0000001 && $number > 0) || ($number > -0.0000001 && $number < 0))
            $number = 0;
        $lang = CurrentSiteLang('code2');
        if (!$code)
            $code = getCurrentSite('currency_code');
        if (!$number_formats = Cache::get($lang . '_number_formats')) {
            $currencies = (include 'currencies.php'); //fix
            Cache::put($lang . '_number_formats', $number_formats, 60 * 24 * 7);
            Cache::put($lang . '_currencies', $currencies, 60 * 24 * 7);
        } else {
            $currencies = Cache::get($lang . '_currencies');
        }
        if ($code == 'EUR') {
            $country = getCurrentSite('country_code');
            if (isset($number_formats[$code . '-' . $country]) && $number_formats[$code . '-' . $country])
                $code = $code . '-' . $country;
        }
        if (!isset($number_formats[$code]))
            $number_formats[$code] = [2, '.', ','];
        $thousands_separator = empty(trim($number_formats[$code][2])) ?  "\u{00A0}" : $number_formats[$code][2];
        if (isset($number_formats[$code]) && $number_formats[$code]) {
            $number =  rtrim(rtrim(rtrim(number_format(floatval($number), $max_precision, $number_formats[$code][1], $thousands_separator), "0"), ','), '.');
        }
        if($isNegative) {
            $negativeFormat = Settings::getValue(0, 'negative_currency_formats');
            if($negativeFormat === \App\Helpers\Settings::NEGATIVE_CURRENCY_FORMAT_AMOUNT_WITH_PARENTHESIS) {
                $number = '(' . str_replace('-', '', $number) . ')';
            }
        }
        return $number;
    }
}
//View Functions
if (!function_exists('get_menus')) {
    function get_menus($type, $staff_rule = 0, $force_menu = 0)
    {
        $cacheBaseName = 'menu_' . getCurrentSite('id');
        $cacheKeyName = 'laravel_' . CurrentSiteLang();
        $branchAppend = '';
        if (Plugins::PluginActive(PluginUtil::BranchesPlugin)) {
            $branchAppend = '_' . Branch::getCurrentBranchID();
        }
        $cacheKeyName .= (!is_null($staff_rule) && $staff_rule > 0 ? 'staff_' . $staff_rule : $type) . $branchAppend;
        $cachedMenu = Cache::get($cacheBaseName);
        if ($cachedMenu && isset($cachedMenu[$cacheKeyName]) && strlen($cachedMenu[$cacheKeyName])) {
            return $cachedMenu[$cacheKeyName];
        } else {
            $restClient = new InternalHttpAdapter();
            try {
                $menu = $restClient->getMenu($force_menu);
                if ($menu) {
                    $renderedMenu = View::make('partials/layout/render_sidemenu', ['menus' => $menu])->render();
                    $cachedMenu = Cache::get($cacheBaseName);
                    $cachedMenu[$cacheKeyName] = $renderedMenu;
                    Cache::put($cacheBaseName, $cachedMenu, 3600 * 24 * 7);
                    return $renderedMenu;
                }
            } catch (RequestException $exception) {
                if ($exception->getCode() != 401) {
                    Log::error('MenuAPIError', ['exception' => $exception]);
                }
                redirect()->to(CAKE_BASE_URL)->send();
            } catch (\Exception $exception) {
                Log::error('MenuError', ['exception' => $exception]);
                redirect()->to(CAKE_BASE_URL)->send();
            }
        }
        return '';
    }
}
if (!function_exists('get_menus_data')) {
    function get_menus_data($view,$type, $staff_rule = 0, $force_menu = 0)
    {
        $langId = getCurrentSite('language_code');

        $cacheBaseName = 'menu_' . getCurrentSite('id').'_data';
        $branchAppend = '';
        if (Plugins::PluginActive(PluginUtil::BranchesPlugin)) {
            $branchAppend = '_' . Branch::getCurrentBranchID();
        }
        $cacheKeyName = $langId . '_' .  (!is_null($staff_rule) && $staff_rule > 0 ? 'staff_' . $staff_rule : $type) . $branchAppend;
        $cachedMenu = Cache::get($cacheBaseName);

        if ($cachedMenu) {
            if (isset($cachedMenu[$cacheKeyName]) && count($cachedMenu[$cacheKeyName])) {
                return $cachedMenu[$cacheKeyName];
            }
        }

        $restClient = new InternalHttpAdapter();

        $menu = $restClient->getMenuData($force_menu);
        return ($menu);
    }
}
if (!function_exists('get_subscription_countdown')) {

    function get_subscription_countdown()
    {
        $subdomain = str_replace(Beta_Domain, Domain_Short_Name, $_SERVER['HTTP_HOST']);
        $site = collect(DB::table('sites')->where('subdomain', $subdomain)->first())->toArray();

        $free_plan = collect(DB::table('plans')->first())->toArray();

        $subscription_countdown = Sidebar::subscriptionCountdown($site, $free_plan);

        return View::make('partials/layout/subscription_countdown', ['subscription_countdown' => $subscription_countdown])->render();
    }
}
if (!function_exists('get_subscription_countdown_data')) {
// this function for new layout izam-view
    function get_subscription_countdown_data()
    {
        $ordinal_host = request()->getHttpHost();
        $siteDomain = str_replace(Beta_Domain, Domain_Short_Name, $ordinal_host);

        /** @var \App\Repositories\SiteRepository  $siteRepo */
        $siteRepo = app()->make('App\Repositories\SiteRepository');
        $site = $siteRepo->findBySubdomain($siteDomain);
//        $subdomain = str_replace(Beta_Domain, Domain_Short_Name, $_SERVER['HTTP_HOST']);
//        $site = collect(DB::table('sites')->where('subdomain', $subdomain)->first())->toArray();

        $free_plan = collect(DB::table('plans')->first())->toArray();

        $subscription_countdown = Sidebar::subscriptionCountdown($site->toArray(), $free_plan);
        return $subscription_countdown;

    }
}
if (!function_exists('delete_menus')) {
    function delete_menus()
    {
        // delete menu data from the cache used in izam view
        $cacheBaseName = 'menu_' . getCurrentSite('id') . '_data';
        Cache::forget($cacheBaseName);

        $cacheBaseName = 'menu_' . getCurrentSite('id');
        Cache::forget($cacheBaseName);

        \Izam\Daftra\Cache\PortalCache::clear();
    }
}

if (!function_exists('getStatusForCharge')) {
    function getStatusForCharge($charge)
    {
        if (!is_null($charge) && $charge->status == CreditChargeStatusUtil::SUSPENDED) {
            return CreditChargeStatusUtil::SUSPENDED;
        }


        $expiryDate = new Carbon($charge->expiry_date);

        if ($expiryDate->lessThan(Carbon::today())) {
            return CreditChargeStatusUtil::EXPIRED;
        }
        return CreditChargeStatusUtil::AVAILABLE;
    }
}

if (!function_exists('resizeImage')) {
    /**
     * resize
     * @param string $imagePath
     * @param array $queryParameters
     * @return string
     */
    function resizeImage(string $imagePath, array $queryParameters = [])
    {
        if (filter_var($imagePath, FILTER_VALIDATE_URL) === false) {
            if (!empty($imagePath) && $imagePath[0] != '/') {
                $imagePath = '/'.$imagePath;
            }
        }

        $imagePath = 'resizescript/' . base64_encode($imagePath);

        return CAKE_BASE_URL . $imagePath . '?' . http_build_query($queryParameters);
    }
}


if (!function_exists('time2str'))
{
    /**
     * format date to be written as meaningful text (i.e: Today, Yesterday, Monday , 07 May ..etc)
     * @param $date
     * @return array|mixed|string|null
     */
    function time2str($date) {
        $months = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
        foreach ($months as $key => $month) {
            $new_months[$month] = __t($month, true);
        }
        foreach ($months as $key => $month) {
            $new_months[substr($month, 0, 3)] = __t($month, true);
        }
        $ts = strtotime($date);
        $date=date("Y-m-d", $ts);
        $current_year = date("Y");
        $ts_year = date("Y", $ts);

        if ($date == date('Y-m-d'))
            return __t('Today', true);
        else
            if ($date == date('Y-m-d', strtotime('Yesterday')))
                return __t('Yesterday', true);
            else
                if ($date > date('Y-m-d', strtotime('-7 Days')))
                    return __t(date('l', $ts), true);
                else
                    if ($current_year != $ts_year) {
                        return str_replace(array_keys($new_months), array_values($new_months), date('d M Y', $ts));
                    }
        return str_replace(array_keys($new_months), array_values($new_months), date('d M', $ts));
    }
}

if (!function_exists('time2strReservation'))
{
    /**
     * format date to be written as meaningful text (i.e: Today, Yesterday, Monday , 07 May ..etc)
     * @param $date
     * @return array|mixed|string|null
     */
    function time2strReservation($date) {
        $months = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
        foreach ($months as $key => $month) {
            $new_months[$month] = __t($month, true);
        }
        foreach ($months as $key => $month) {
            $new_months[substr($month, 0, 3)] = __t($month, true);
        }
        $ts = strtotime($date);
        $date=date("Y-m-d", $ts);
        $current_year = date("Y");
        $ts_year = date("Y", $ts);

        if ($date == date('Y-m-d'))
            return __t('Today', true);
        else
            if ($date == date('Y-m-d', strtotime('Yesterday')))
                return __t('Yesterday', true);
            else
                if ($date == date('Y-m-d', strtotime('Tomorrow')))
                    return __t('Tomorrow', true);
                else
                    if ($current_year != $ts_year) {
                        return __t(date('l', $ts), true) . ', ' . str_replace(array_keys($new_months), array_values($new_months), date('d M Y', $ts));
                    }
        return __t(date('l', $ts), true) . ', ' . str_replace(array_keys($new_months), array_values($new_months), date('d M', $ts));
    }
}

function getPeriodStringToReservation($startDate, $endDate, $type)
{
    $startDateDay = time2strReservation(convertFromUtc($startDate));
    $endDateDay = time2strReservation(convertFromUtc($endDate));

    if ($startDateDay != $endDateDay) {
        if ($type == 'days') {
            return $startDateDay . __t(' to ', true) . $endDateDay;
        } else {
            return $startDateDay . ' (' . date('H:i', strtotime($startDate)) . ')' . __t(' to ', true) . $endDateDay . ' (' . date('H:i', strtotime($endDate)) . ')';
        }
    } else {
        return $startDateDay . ' (' . date('H:i', strtotime($startDate)) . __t(' to ', true) . date('H:i', strtotime($endDate)) . ')';
    }
}

/**
 * formats listing cell value based on its type
 * @param $typeData
 * @param $value
 * @return string
 */
function formatListingField($typeData, $value)
{
    switch ($typeData['name']) {
        case LISTING_FIELD_TYPE_DATE:
            $value = format_date($value);
            break;
    }
    return $value;
}

/**
 * convert php array to html attributes
 * @param array $array
 * @return stringform
 */
function arrayToHtmlAttributes($array = [], $echo = true)
{
    if($echo){
        foreach ($array as $k => $v) {
            echo " $k=\"$v\" ";
        }
    }else{
        $text = '';
        foreach ($array as $k => $v) {
            $text .= " $k=\"$v\" ";
        }
        return $text;
    }

}

/**
 * transforms HumanResources to this Human Resources
 * @param $string
 * @return null|string|string[]
 */
function humanize($string)
{
    $string = ucfirst(str_replace("_"," ",$string));
    return implode(' ', array_filter(preg_split('/(?=[A-Z])/',$string)));
}

// Only you need to add in your controller.
function bladeCompile($value, array $args = [])
{
    $generated = \Blade::compileString($value);
    ob_start() and extract($args, EXTR_SKIP);
    // We'll include the view contents for parsing within a catcher
    // so we can avoid any WSOD errors. If an exception occurs we
    // will throw it out to the exception handler.
    try {
        eval('?>' . $generated);
    }
        // If we caught an exception, we'll silently flush the output
        // buffer so that no partially rendered views get thrown out
        // to the client and confuse the user with junk.
    catch (\Exception $e) {
        ob_get_clean();
        throw $e;
    }
    $content = ob_get_clean();
    return $content;
}

/**
 * Function that gets the url if the parameter was string it will be returned as it is
 * if it was array it will parse it as Router::url()
 *
 * Eg:
 * ```
 *  getCakeURL([
 *        'controller' => 'invoices',
 *        'action' => 'index',
 *        'prefix' => 'owner',
 *        '?' => 'box=1',
 *        'ext' => 'pdf',
 *        5
 *  ]);
 * ```
 *
 * output will be: ``CAKE_BASE_URL/owner/invoices/index/5?box=1.pdf``
 *
 * @param string|array $url
 * @return string
 */
function getCakeURL($url)
{
    $finalURL = '';
    $prefix = 'owner';
    $controller = '';
    $action = 'index';
    $params = [];
    $queryParams = '';
    $ext = '';
    if (is_array($url)) {
        $finalURL = CAKE_BASE_URL;
        foreach ($url as $key => $value) {
            switch (strval($key)) {
                case 'prefix':
                    $prefix = $value;
                    break;
                case 'controller':
                    $controller = $value;
                    break;
                case 'action':
                    $action = $value;
                    break;
                case 'ext':
                    $ext = '.' . $value;
                    break;
                case '?':
                    $queryParams = '?';
                    if (is_array($value))
                        $queryParams .= http_build_query($value, null, '&');
                    elseif (is_string($value))
                        $queryParams .= $value;
                    break;
                default:
                    $params[] = $value;
            }
        }
        $relativeURL = $prefix . '/' . $controller . '/' . $action . ($params ? '/' . implode('/', $params) : '') . $queryParams . $ext;
        $relativeURL = str_replace('//','/',$relativeURL);
        return $finalURL . ltrim($relativeURL,'/');
    } else if (is_string($url)) {
        return $url;
    }
    return $finalURL;
}

/**
 * returns the current site database name
 */
function getCurrentSiteDatabaseName()
{
    return Config::get('database.connections.currentSite.database');
}

/**
 * # Check if the item has records on the database or not
 * @param int   $item_id        the item you want to check if it has transactions or not
 * @param int   $type           the item type currently it supports 1 => branches and 2 => staff
 * @param array $excludedTables exclude table from the process
 * @return bool whether the item has records or not or the type is not 1 or 2
 */
function hasTransactions($item_id, $type, array $excludedTables = [])
{
    $db_config = json_decode(getCurrentSite('db_config'), true);
    $excludedTables = array_merge(['action_lines'], $excludedTables);
    $extraColumns = array();
    switch ($type) {
        case 1:
            $field = 'branch_id';
            $excludedTables = array_merge($excludedTables, ['branches', 'settings']);
            if (!Plugins::PluginActive(PluginUtil::BranchesPlugin))
                return false;
            break;
        case 2:
            $field = 'staff_id';
            $excludedTables = array_merge($excludedTables, ['staffs','staff_info','staff_job','staff_holiday_lists','staffs_custom_data']);
            // excluding shifts and branches from item_staffs table
            $extraTableConditions = [
                'item_staffs' => " AND item_type NOT IN (".ItemStaffUtil::SHIFT_ITEM_TYPE.",".ItemStaffUtil::BRANCH_ITEM_TYPE.")"
            ];
            /** */
            $extraColumns = [
                'departments' => 'manager_id',
                'staff_holiday_lists' => 'staff_id'
            ];
            break;
        default:
            return false;
    }

    $extraColumnsArray = array_merge(array_values($extraColumns), [$field]);
    $extraColumnsString = implode("','", $extraColumnsArray);

    $excludedTablesString = implode("','", $excludedTables);
    $currentSiteConnection = DB::connection('currentSite');

    $sofDeleteTables = $currentSiteConnection->select("SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('deleted_at') AND TABLE_SCHEMA='{$db_config['database']}' AND TABLE_NAME NOT IN ('{$excludedTablesString}')");
    $sofDeleteTables = array_map(function ($item) {
        return $item->TABLE_NAME;
    }, $sofDeleteTables);

    $tables = $currentSiteConnection->select("SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('{$extraColumnsString}') AND TABLE_SCHEMA='{$db_config['database']}' AND TABLE_NAME NOT IN ('{$excludedTablesString}')");
    foreach ($tables as $table) {
        $tableName = $table->TABLE_NAME;

        $checkField = $field;
        if (in_array($tableName,array_keys($extraColumns))) {
            $checkField = $extraColumns[$tableName];
        }

        $softDeleteCondition = in_array($tableName, $sofDeleteTables) ? "AND deleted_at IS NULL" : '';
        $extraTableCondition = $extraTableConditions[$tableName]?? '';

        $itemCount = $currentSiteConnection->select("SELECT COUNT(*) as itemCount FROM `{$tableName}` WHERE `{$checkField}` = {$item_id} {$softDeleteCondition} {$extraTableCondition}");
        if ($itemCount[0]->itemCount > 0) {
            return true;
        }
    }
    return false;
}
function translate_plugin_group_title($object) {
    if (App::getLocale() == "eng") {
        return $object->en_title;
    }
    return $object->ar_title;

}


function __t($key,$return=true)
{
    $lf = Config::get('localeFile');
    $locale = App::getLocale();
    if (is_array($key)) {
        Log::warning('Unexpected Behaviour', ['translation_key' => $key]);
        return implode(',', $key);
    } else if (__("$lf.$key") !== "$lf.$key") {

        return __("$lf.$key");
    } else if(__($locale.'-messages.'.$key) !== $locale.'-messages.'.$key) {
        return __($locale.'-messages.'.$key);
    }
    return $key;
}

/**
 * get month name using month number
 * @param $index
 * @param bool $disableArabic
 * @return array|bool|mixed|string|null
 */
function getMonthName($index, $disableArabic = false)
{
    if($index < 1) {
        return false;
    }
    $months = ['January','February','March','April','May','June','July','August','September','October','November','December'];
    return $disableArabic ? $months[$index-1] : __t($months[$index-1]);
}

if (!function_exists('getErrorFieldName')) {
    /**
     * @param $fieldName
     * gets field name compatiable with larave error bag keys
     * i.e if field name data[name] it will return data.name if name it will return name if names[] it will return name
     * @return string
     */
    function getErrorFieldName($fieldName)
    {
        if (preg_match_all('/\[(.+?)\]/', $fieldName, $m)) {
            //matches any input name with bracket names like data[name]
            preg_match('/([^\[]*)/', $fieldName, $modelName);

            $modelName = $modelName[1];
            return $modelName . '.' . implode('.', $m[1]);
        } else {
            return str_replace('[]', '', $fieldName);
        }
    }
}
if (!function_exists('getFileName')) {
    function getFileName($value)
    {
        $result = explode('/', $value);
        preg_match('/([^\?]*)/', end($result), $m);
        return $m[1];
    }
}

if (!function_exists('appendTextToAttributeName')) {

    //appends text to the name attribute of html input
    function appendTextToAttributeName($name, $textToAppend)
    {
        if(strpos($name,'[]') !== false )
        {
            return $name . $textToAppend;
        }
        else if (strpos($name, '[') !== false) {
            //handles if the name in the format of data[field1][field2]
            return preg_replace('/\[([^\[]*)\]$/', '[$1' . $textToAppend . ']', $name);
        } else {
            //handles the simple case
            return $name . $textToAppend;
        }
    }
}
function checkRoute($route)
{
    if ($route[0] === "/") {
        $route = substr($route, 1);
    }
    $routes = \Route::getRoutes()->getRoutes();
    foreach ($routes as $r) {
        /** @var \Route $r */
        if ($r->uri == $route) {
            return true;
        }
    }
    return false;
}

function formatForView($value, $type, $field = null, $options = [])
{
    return Formatter::formatForView($value, $type, $field, $options);
}

//This function was created to format date only for input
//Cause of this is Hijri date cannot be parsed in date picker it must be a normal date format
function formatDateForInput($value)
{
  return format_date($value, false,false,false,false);
}

if (!function_exists('isApi')) {

    function isApi()
    {
        return request()->wantsJson() OR Config::get('IS_API');
    }
}
if (!function_exists('getLoggedUserDisplayName')) {

    function getLoggedUserDisplayName()
    {
        $owner = getAuthOwner();
        if($owner)
        {
            if($owner['staff_id'] === 0)
            {
                return $owner['first_name'] . ' ' .$owner['last_name'];
            }
	        $owner = getAuthStaff();
			// To Fix commandline commands
	        if (!$owner) return "";
	        return $owner['name'] . ' ' .$owner['last_name'];
        }
    }
}

if(!function_exists('convertToUtc'))
{
    /**
     * @param $mysqlDateTime
     * @param null $timezone
     * @param bool $carbonOutput
     * @return Carbon|string object
     * converts from timezone (default site timezone) to utc
     */
    function convertToUtc($mysqlDateTime, $timezone = null, $carbonOutput = true)
    {
        if($timezone === null)
            $timezone = Config::get('app.timezone');

        $date = Carbon::createFromFormat('Y-m-d H:i:s', $mysqlDateTime, $timezone);
        $date->setTimezone('UTC');

        if($carbonOutput)
            return $date;
        else
            return $date->toDateTimeString();
    }
}

if(!function_exists('convertFromUtc'))
{
    /**
     * @param $mysqlDateTime
     * @param null $timezone
     * @param true $carbonOutput
     * @return Carbon|string object
     * converts from UTC (default site timezone) to timezone
     */
    function convertFromUtc($mysqlDateTime, $timezone = null, $carbonOutput = true)
    {
        if($timezone === null)
        {
            $timezone = Config::get('app.timezone');
        }
        $date = Carbon::createFromFormat('Y-m-d H:i:s', $mysqlDateTime, 'UTC');
        $date->setTimezone($timezone);
        if($carbonOutput)
        {
            return $date;
        }else{
            return $date->toDateTimeString();
        }
    }
}

if(!function_exists('isOwnerOrSuperAdmin'))
{
    function isOwnerOrSuperAdmin()
    {
        $owner = getAuthOwner();
        return $owner['staff_id'] == 0 OR $owner['is_super_admin'];
    }
}

if(!function_exists('getCdnAssetsUrl'))
{
    function getCdnAssetsUrl()
    {
        return env('CDN_ASSETS_URL', 'https://cdn.daftra.com/assets/');
    }
}

if(!function_exists('implodeWithQuotes'))
{
    function implodeWithQuotes($glue, $array)
    {
        return '"' . implode('"'.$glue . '"', $array) .'"';
    }
}

if (!function_exists('composerPackageInstalled')) {
    function composerPackageInstalled($packageName) {
        $process = new Process([]);
        $projectDirectory = base_path();
        $process->setCommandLine("cd {$projectDirectory};composer show -i")->run();
        return strpos($process->getOutput(), 'doctrine/dbal') !== false;
    }
}

if(!function_exists('swap'))
{
    function swap(&$x, &$y)
    {
        $temp = $x;
        $x = $y;
        $y= $temp;
    }
}

if(!function_exists('getCurrentSiteSubdomain'))
{
    function getCurrentSiteSubdomain()
    {
        $siteData = getCurrentSite();
        $subdomain = $siteData['subdomain'];
        if($siteData['beta_version'] && env('APP_ENV') !== 'local')
        {
            $subdomain = str_ireplace(Domain_Short_Name, Beta_Domain, $subdomain);
        }
        return $subdomain;

    }
}

if (!function_exists('get_currency_formatted')) {
    /**
     * get currency formatted
     * @param string|null $code
     * @return float|int|string|string[]|null
     */
    function get_currency_formatted(string $code = null)
    {
        if (is_null($code)) {
            $code = getCurrentSite('currency_code');
        }
        $lang = CurrentSiteLang('code2');
        $code = strtoupper($code);
        if (!Cache::get($lang . '_number_formats')) {
            clearstatcache('currencies.php');
            $currencies = (include 'currencies.php');
            Cache::put($lang . '_currencies', $currencies, 60 * 24 * 7);
        } else {
            $currencies = Cache::get($lang . '_currencies');
        }
        $format = $currencies[$code];
        return trim(str_replace('%s', '', $format));
    }
}

if (!function_exists('isStaff')) {
    /**
     * @return bool
     */
    function isStaff()
    {
        return getAuthOwner('staff_id') !== 0 && getAuthOwner('staff_id') !== null;
    }
}

if (!function_exists('isOwner')) {
    /**
     * @return bool
     */
    function isOwner()
    {
        return getAuthOwner('staff_id') == 0;
    }
}

if (!function_exists('isClient')) {
    /**
     * @return bool
     */
    function isClient()
    {
        $authID = getAuthClient('id');
        return $authID !== null && $authID != 0;
    }
}
if (!function_exists('getAuthenticatedUserType')) {
    /**
     * @return bool
     */
    function getAuthenticatedUserType()
    {
       if(isClient()){
           return 'client';
       }elseif(isStaff()){
           return 'staff';
       }elseif(isOwner()){
           return 'owner';
       }else{
           return 'zombie';
       }
    }
}
if(!function_exists('getDefaultAccountImage'))
{
    /**
     * get default account image path
     * @return string
     */
    function getDefaultAccountImage() : string
    {
        return '';
    }
}


if (!function_exists('getSiteHash')) {
    /**
     * get file size formatted
     * @return string
     */
    function getSiteHash(): string
    {
        return dechex(crc32(getCurrentSite('id')));
    }
}

if (!function_exists('arrayWrap')) {
    /**
     * @param $value
     * @return array
     */
    function arrayWrap($value): array
    {
        return is_array($value) ? $value : [$value];
    }
}

if (!function_exists('databaseReConnect')) {
    /**
     * @param array $databaseConfig
     * @param int $siteID
     * @return void
     */
    function databaseReConnect(array $databaseConfig, int $siteID)
    {
        Config::set('SITE_HASH', $siteID ? dechex(crc32($siteID)) : '');
        \App\Helpers\CurrentSiteConfigurationSetter::set($databaseConfig);
    }
}

if (!function_exists('userType')) {
    /**
     * @param  string  $type
     *
     * @return string
     */
    function userType(string $type)
    {
        if ($type === \Izam\Daftra\Staff\Util\StaffTypeUtil::USER) {
            return __t('User');
        } elseif ($type === \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE) {
            return __t('Employee');
        }
    }
}

if (!function_exists('isSiteSuspended')) {
    /**
     * @return bool
     */
    function isSiteSuspended(): bool
    {
        $site = getCurrentSite();

        $tolerant = intval($site['tolerant']);
        $expiryDateWithTolerant = Carbon::parse($site['expiry_date'])->addDays($tolerant)->format('Y-m-d');
        $expiry = $site['plan_id'] != 1 && $expiryDateWithTolerant < date("Y-m-d");
        $siteStatusStopped = ($site['status'] == SiteStatusUtil::STOPPED && $site['plan_id'] == 1);

        return $expiry || $siteStatusStopped;
    }
}




function getJavascriptVariableName($string) {
    return str_replace('-', '_', $string);
}

function dottedStringToArray(&$arr, $path, $value, $separator='.') {
    $keys = explode($separator, $path);
    foreach ($keys as $key) {
        $arr = &$arr[$key];
    }
    $arr = $value;
}


function getLaravelValidationRuleParts($ruleString) {
    $ruleParts = explode(':',$ruleString);
    $ruleName = array_shift($ruleParts);
    $params = $ruleParts;
    return ['name' => $ruleName, 'params' => $params];
}


function array_depth(array $array) {
    $max_depth = 1;
    foreach ($array as $value) {
        if (is_array($value)) {
            $depth = array_depth($value) + 1;
            if ($depth > $max_depth) {
                $max_depth = $depth;
            }
        }
    }

    return $max_depth;
}

function prefixString($string, $prefix = "", $prefixChar = '.') {
    if(empty($prefix)) {
        return $string;
    } else {
        return $prefix.$prefixChar.$string;
    }
}

if (!function_exists('get_real_ip')) {
    /**
     * This function gets real ip in case we are using a load balancer or third party proxy
     * in case there are no forwards ips we use the default ip from laravel request
     */
    function get_real_ip()
    {
        foreach (array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR') as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        return request()->ip();
    }
}
if (!function_exists('toArray')) {
    /**
     * This function gets real ip in case we are using a load balancer or third party proxy
     * in case there are no forwards ips we use the default ip from laravel request
     */
    function toArray(&$value)
    {
        if(is_array($value)) {
            foreach ($value as $k => $v) {
                $value[$k] = toArray($v);
            }
        }
        if(is_object($value)) {
            if(method_exists($value, 'toArray')) {
                $value = $value->toArray();
            } else {
                $value = (array) $value;
                $value = toArray($value);
            }
        }
        return $value;
    }
}
if (!function_exists('flashLimitExceededMessage')) {

    /**
     * @param array $result
     * @param string $layout
     */
    function flashLimitExceededMessage(array $result, string $layout = 'site_limits')
    {
        unset($_SESSION['Message']['flash']);
        $_SESSION['Message']['flash'] = [
            'result' => $result,
            'layout' => $layout,
        ];
    }
}

if(!function_exists('arrayToCollectionObject')) {
    function arrayToCollectionObject($array) {
        return collect($array)->map(function($item) {
            return (object) toArray($item);
        });
    }
}
if(!function_exists('array_undot')) {

     function array_undot($dottedArray, $initialArray = [])
    {
        $dottedArray = Arr::dot($dottedArray);
        foreach ($dottedArray as $key => $value) {
            Arr::set($initialArray, $key, $value);
        }
        return $initialArray;
    }
}

if(!function_exists('getMainBranch')) {
    function getMainBranch($field = null) {
        if (ifPluginActive(PluginUtil::BranchesPlugin)) {
            $mainBranchID = settings::getValue(PluginUtil::BranchesPlugin, 'main_branch') ?? 1;
            if ($mainBranchID) {
                $mainBranch = \App\Facades\Branch::getBranch($mainBranchID);
                return ($field && isset($mainBranch[$field])) ? $mainBranch[$field] : $mainBranch;
            }
        }
        return null;
    }
}

if(!function_exists('getCurrentBranchID')) {
    function getCurrentBranchID(): int
    {
        return (int) \App\Facades\Branch::getCurrentBranchID();
    }
}

if (!function_exists('flashMessageIntoCake')) {
    function flashMessageIntoCake($msg, $type = 'Sucmessage')
    {
        unset($_SESSION['Message']['flash']);

        $_SESSION['Message']['flash'] = [
            'message' => $msg,
            'element' => 'default',
            'params' => [
                'class' => $type
            ]
        ];
    }
}

if (!function_exists('getDefaultImage')) {
    function getDefaultImage($type) : string
    {
        switch ($type) {
            case EntityKeyTypesUtil::STAFF_ENTITY_KEY:
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY:
                return getCdnAssetsUrl() . "img/avatar/avatar_light_32px.svg";
            default :
                return "";
        }
    }
}

if(!function_exists('singular')) {
    function singular($word) {
        return Str::singular($word);
    }
}

if(!function_exists('setFlashMessage')) {
    function setFlashMessage($url, $message, $type = 'success') {
        if (strpos($url, '/v2') !== false) {
            \Session::flash($type, $message);
        } else {
            $type = ($type == 'success') ? 'Sucmessage' : 'Errormessage';
            flashMessageIntoCake($message, $type);
        }
    }
}

if(!function_exists('getRedirectBackUrl')) {
    function getRedirectBackUrl() {
        return request()->headers->get('referer') ?? getCakeURL('/');
    }
}


if (!function_exists('setLastUpdatedAt')) {
    /**
     * @param int $plugin
     * @param string $key
     *
     * @return void
     */
    function setLastUpdatedAt($plugin, $key)
    {
        $milliseconds = round(microtime(true) * 1000);
        Settings::setValue($plugin, $key, $milliseconds, false, false);
    }
}

if(!function_exists('getCakeSession')) {
    function getCakeSession($key)
    {
        return CakeSession::read($key) ? CakeSession::read($key) : false;
    }
}

if(!function_exists('getStaffBranches')) {
    function getStaffBranches()
    {
        return Branch::getStaffBranchesIDs();
    }
}

if(!function_exists('getStaffBranchesIDs')) {
    function getStaffBranchesIDs()
    {
        return Branch::getStaffBranchesIDs();
    }
}

if(!function_exists('getStaffBranchesSuspended')) {
    function getStaffBranchesSuspended()
    {
        return Branch::getStaffBranchesSuspended()->toArray();
    }
}

function getPdo($name = 'currentSite')
{
    return DB::connection($name)->getPdo();
}
if (!function_exists('settingsGetValue')) {
    function settingsGetValue($plugin_id, $key, $st = null, $allowCache = true, $applyBranches = true)
    {
        Settings::getValue($plugin_id, $key, $st, $allowCache, $applyBranches);
    }
}

if(!function_exists('get_default_currency')) {
    function get_default_currency() {
        $default_currency=Settings::getValue(PluginUtil::ExpensesPlugin, "journals_local_currency_code");
        if(empty($default_currency)) {
            $default_currency =  getCurrentSite('currency_code');
            Settings::setValue(PluginUtil::ExpensesPlugin, "journals_local_currency_code", $default_currency);
        }
        return $default_currency;
    }
}
if(!function_exists('ifPluginActive')) {
    function ifPluginActive($pluginId) {
        return Plugins::pluginActive($pluginId);
    }
}

if(!function_exists('standardizeLangName')) {
    /**
     * @param $langName
     * @return string
     * return first two character of language name
     */
    function standardizeLangName($langName) {
        return substr($langName,"0", "2");
    }
}

//if(!function_exists('flashMessageIntoCake')) {
//    function flashMessageIntoCake($msg, $type = 'Sucmessage') {
//        unset($_SESSION['Message']['flash']);
//        $_SESSION['Message']['flash'] = [
//            'message' => $msg,
//            'element' => 'default',
//            'params' => [
//                'class' => $type
//            ]
//        ];
//    }
//}

if(!function_exists('showMobileApps')) {
    function showMobileApps() {
        if (QR_ENABLED && !getAuthClient() ) {
            //for Enerpize : If the attendance plugin and pos plugin deactivated so that the menu item that called “Enerpize Mobile Apps” will not appear.
            if(Site_Full_name != 'Daftra')
            {
                return Plugins::pluginActive(PluginUtil::PosPlugin) &&  Plugins::pluginActive(PluginUtil::HRM_ATTENDANCE_PLUGIN);

            }
            return true;
        }

        return false;
    }
}

if (!function_exists('getCurrentPlugin')) {
    function getCurrentPlugin($only_external = false) {
        return Plugins::getCurrentPlugins($only_external);
    }
}
if(!function_exists('non_english')) {
    function non_english($string) {

        if(strlen($string) != mb_strlen($string, 'utf-8'))
        {
            return true;
        }
        else {
            return false;
        }
    }
}
function switchToRedis(): void
{

    if (useRedis()) {
        ini_set('session.gc_maxlifetime', 86400);
        ini_set('session.save_handler', 'redis');
        ini_set('session.save_path', REDIS_SERVER);
        if (isset($_COOKIE['CakeCookie']['User_id'])) {

            ini_set('session.gc_maxlifetime', 2630000);
        }
        setRedisCookieFlag();
    }


}
function useRedis(): bool
{
    if ((defined('USEREDIS') && USEREDIS )) {
        return true;
    }else{
        return false;
    }
}

function setRedisCookieFlag(){
    if (useRedis() && !isset($_COOKIE['useRedis'])) {
        setcookie('useRedis', '1', strtotime("+1 month"), "/");
    }
}

function getRedisConfig($key, $default_value)
{

    if (!useRedis() || php_sapi_name() == 'cli') {
        return $default_value;
    }

    setRedisCookieFlag();

    if ($key == 'SESSION_DRIVER') {
        return 'redis';
    }

    if ($key == 'SESSION_LIFETIME') {
        if (isset($_COOKIE['CakeCookie']['User_id'])) {
            return 2630000/60;
        } else {
            return 86400/60;
        }
    }

    if ($key == 'SESSION_CONNECTION') {

        return 'redis_session';
    }

    return $default_value;

}
function getRedisRealUrl(){
    return str_replace("?auth","?password",REDIS_SERVER);
}

if(!function_exists('getEntityBuilder')) {
    function getEntityBuilder(): \Izam\Daftra\Common\EntityStructure\AbstractEntityStructureGetter {
        return resolve(AppEntityStructureGetter::class);
    }
}

function showAppSettingsToggleButton($app, $installedAppsIds): bool
{
    return (is_array($app->getSettings()) && !empty($app->getSettings()) && in_array($app->id, $installedAppsIds));
}

function output_separated_trans($text)
{
    $arr = explode(' ', trim($text));
    $out = '';
    foreach ($arr as $e) {
        $out .=__t(__t("$e") . " ");
    }
    return __t($out);
}

if(!function_exists('getAuthOwnerName')) {
    function getAuthOwnerName() {
        $owner = getAuthOwner();
        if (($owner['first_name'] or $owner['last_name'])) {
            $username = "{$owner['first_name']} {$owner['last_name']}";
        } else if (!empty($owner['business_name'])) {
            $username = $owner['business_name'];
        }

        return $username ?? '';
    }
}

if(!function_exists('checkResourceBasedPermissions')) {
    function checkResourceBasedPermissions($checkers, $data) {
        $permissionManager = app("App\Modules\LocalEntity\Permissions\ResourceBasedPermissionsManager");
        return $permissionManager->check($checkers, $data);
    }
}

if(!function_exists('checkResourceBasedPermissions')) {
    function checkResourceBasedPermissions($checkers, $data) {
        $permissionManager = app("App\Modules\LocalEntity\Permissions\ResourceBasedPermissionsManager");
        return $permissionManager->check($checkers, $data);
    }
}

if(!function_exists('getAccessTokenExpiration')) {
    function getAccessTokenExpiration() {
        return (new \DateTimeImmutable())->add(new \DateInterval('P3Y'));
    }
}
if(!function_exists('setStatusInfo')) {
    function setStatusInfo()
    {
        if (!CACHE_DISABLE) {
            $memcache_obj = memcache_connect(CACHED_SERVER, 11211, 3);
            if ($memcache_obj) {
                memcache_set($memcache_obj, "pid_" . getmypid() . "_" . CURRENT_SERVER_NAME, $_SERVER['HTTP_HOST'], 0, 8600);
                memcache_set($memcache_obj, "ip_" . getmypid() . "_" . CURRENT_SERVER_NAME, $_SERVER['REMOTE_ADDR'], 0, 8600);
                memcache_set($memcache_obj, "pid_" . getmypid() . "_" . CURRENT_SERVER_NAME . "_" . dechex(crc32($_SERVER['REMOTE_ADDR'])), $_SERVER['HTTP_HOST'], 0, 8600);
                memcache_close($memcache_obj);
            }
        }
    }
}

if (!function_exists('isWorkFlowTypeEntity')) {
    function isWorkFlowTypeEntity($entityKey) {
        return str_contains($entityKey, 'workflow-type-entity');
    }
}

if(!function_exists('resolveClientImage')) {
    function resolveClientImage($client, $size = 78, $ignoreResizer = false)
    {
        if ($client->photo) {
            return '/files/' . getSiteHash() . '/photos/' . $client->photo;
        }
        $clientImageAws = (resolve(\Izam\Attachment\Service\AttachmentsService::class))->getAttachmentsByEntityKeyEntityId(\Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::CLIENT_ENTITY_KEY, $client->id)->first();
        if ($clientImageAws) {
            return AvatarURLGenerator::generate($client->business_name, $client->id, $size, $clientImageAws['path'], false, $ignoreResizer);
        }
        return AvatarURLGenerator::generate($client->business_name, $client->id, $size, null, false, $ignoreResizer);
    }
}

if(!function_exists('getStaffBranchesIDsSuspended')) {
    function getStaffBranchesIDsSuspended()
    {
        return Branch::getStaffBranchesIDsSuspended();
    }
}


if(!function_exists('check_permission')) {
    function check_permission($permission)
    {
        return Permissions::checkPermission($permission);
    }
}


if (!function_exists('getIzamDatabaseConfig')) {
    function getIzamDatabaseConfig($name = 'default')
    {
        if ($name == 'queue_database') {
            $name = "queue_server";
        }
        return  config()['database']['connections'][$name] ?? [];
    }
}


if (!function_exists('getQueueDatabaseConfig')) {
    function getQueueDatabaseConfig()
    {
        return QueueConnectionConfigurationGetter::get();
    }
}

if (!function_exists('getSiteHashWithId')) {
    function getSiteHashWithId($siteID = null)
    {
        return dechex(crc32($siteID));
    }
}

if (!function_exists('isOwnerStaffRole')) {
    function isOwnerStaffRole($roleId) {
        return ($roleId ?? 0) == Staff::OWNER_ROLE_ID;
    }
}
if (!function_exists('currencyConverter')) {
    /**
     * get file size formatted
     * @return string
     */
    function currencyConverter($from, $to , $date): string
    {
        return CurrencyConverter::convert($from, $to, $date);
    }
}

if (!function_exists('isModelShareable')) {
    function isModelShareable($modelName) {
        return isset(SharedModelsUtil::$sharedModels[$modelName]) ? Settings::getValue(PluginUtil::BranchesPlugin ,SharedModelsUtil::$sharedModels[$modelName]['settingsKey']) : false;
    }
}

if (!function_exists('formatSubdomain')) {
    function formatSubdomain(string $basicSubdomain) : string {
        return $basicSubdomain . '/v2';
    }
}

if (!function_exists('showEmailPreferences')) {
    function showEmailPreferences () : bool
    {
        $showEmailPreferences = false;
        foreach (EntityEmailPrefrenceEntityKeyUtil::getEntitiesPlugin() as $plugin) {
            if (ifPluginActive($plugin)) {
                $showEmailPreferences = true;
                break;
            }
        }
        if ($showEmailPreferences) {
            $staffEmail = getAuthStaff('email_address');
            $hasRecivedeMail = EmailLog::where('to', 'like', "%$staffEmail%")
                ->whereIn('entity_key', EntityEmailPrefrenceEntityKeyUtil::getList())
                ->exists();
            if (!$hasRecivedeMail) {   
                $showEmailPreferences = false;
            }
        }
        return $showEmailPreferences;
    }
}