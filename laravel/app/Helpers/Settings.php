<?php
/**
 * Created by PhpStorm.
 * User: bassel
 * Date: 21/03/19
 * Time: 4:33 PM
 */

namespace App\Helpers;

use App\Repositories\SettingsRepository;
use App\Utils\PluginUtil;
use Illuminate\Support\Facades\Cache;
use App\Facades\Branch;
use App\Facades\Plugins;

class Settings
{
    private $settingsRepository;
    const CANCEL_ACCOUNTS_ROUTING = 3;
    const AUTOMATIC_ACCOUNTS_ROUTING = 1;
    const MANUAL_ACCOUNTS_ROUTING = 2;
    const DEFAULT_ACCOUNTS_ROUTING = self::AUTOMATIC_ACCOUNTS_ROUTING;
    const OPTION_INVOICING_METHOD_BOTH = '0';
    const OPTION_INVOICING_METHOD_PRINT = '1';
    const OPTION_INVOICING_METHOD_EMAIL = '2';
    const OPTION_SOLD_TYPE_BOTH = '0';
    const OPTION_SOLD_TYPE_PRODUCTS = '1';
    const OPTION_SOLD_TYPE_SERVICES = '2';
    const OPTION_SOLD_TYPE_BUNDLES = '3';
    const OPTION_BUNDLE_PACK = '1';
    const OPTION_BUNDLE_COMPOUND = '2';
    const OPTION_UNIT_DISPLAY_DEFAULT = '1';
    const OPTION_UNIT_DISPLAY_SELL = '2';
    const OPTION_UNIT_DISPLAY_BUY = '3';
    const OPTION_Show_Default_Information = '0';
    const OPTION_Show_Custom_Information = '1';
    const OPTION_CLIENT_DASHBOARD_TYPE_ALL_DETAILS = '0';
    const OPTION_CLIENT_DASHBOARD_TYPE_BAILING_DETAILS = '1';
    const OPTION_CLIENT_DASHBOARD_TYPE_SHARED_INFO = '2';
    const OPTION_CLIENT_TYPE_BOTH = '0';
    const OPTION_CLIENT_TYPE_INDIVIDUAL = '2';
    const OPTION_CLIENT_TYPE_BUSINESS = '3';
    const OPTION_PRINT_WEB_BROWSER = 0;
    const OPTION_PRINT_PDF = 1;
    const OPTION_DISCOUNT_TOTAL = 1;
    const OPTION_DISCOUNT_ITEM = 2;
    const OPTION_DISCOUNT_BOTH = 3;
    const OPTION_FRACTION_AUTO = 0;
    const OPTION_FRACTION_SHOW_ALWAYS = 1;
    const OPTION_FRACTION_HIDE_ALWAYS = 2;
    const BOOKING_CLIENT_PAYMENT_DISABLED = 1;
    const BOOKING_CLIENT_PAYMENT_ENABLED = 2;
    const BOOKING_CLIENT_PAYMENT_OPTIONAL = 3;

    const STAFF_PAYROLL_ACCOUNT_AUTO = 1;
    const STAFF_PAYROLL_ACCOUNT_SPECIFY = 2;

    const ACCOUNT_ROUTING_OPTION_AUTO = 1;
    const ACCOUNT_ROUTING_OPTION_SPECIFY = 2;
    const COMMISSION_SHEET_AUTO_PAY = 'commission_sheet_auto_pay';

    const CALCULATE_STOCKTAKING_BASED_ON_STOCKTAKING_DATE ='calculate_stocktaking_to_stocktaking_date';
    const NEGATIVE_CURRENCY_FORMAT_AMOUNT_ONLY = 'negative_currency_format_currency_only';

    const NEGATIVE_CURRENCY_FORMAT_AMOUNT_WITH_PARENTHESIS = 'negative_currency_format_currency_with_parenthesis';
    const EXCEEDING_THE_REQUESTED_QUANTITY_IN_MANUFACTURING_ORDER = 'exceeding_the_requested_quantity_in_manufacturing_order';


    const MUDAD_SALARY_COMPONENTS = 'mudad_salary_components';


    var $bookingClientPaymentOptions = [
        self::BOOKING_CLIENT_PAYMENT_DISABLED => 'Disabled',
        self::BOOKING_CLIENT_PAYMENT_ENABLED => 'Enabled',
        self::BOOKING_CLIENT_PAYMENT_OPTIONAL => 'Optional',
    ];
    //default barcode image type value code-128
    const PRODUCT_IMAGE_TYPE = 'C128';

    public function __construct(SettingsRepository $settingsRepository)
    {
        $this->settingsRepository = $settingsRepository;
    }

    private function getStructure()
    {
        $autoAccountRoutingText = __t('Automatic Account Routing');
        $manualAccountRoutingText = __t('Specify');
        $cancelRoutingText = __t('Cancel Account Routing');
        //Import the barcode type
//        App::import('Vendor', 'barcode_gen/bootstrap');
//        $generatorJPG = new Picqer\Barcode\BarcodeGeneratorJPG();
        $structure = [
            0 => [
                'cash_flow_report' => ['default' => json_encode(\Izam\Daftra\Common\Settings\CashFlowReportStructure::getDefaultReport())],
                'negative_currency_formats' => [
                    'options' => [
                        self::NEGATIVE_CURRENCY_FORMAT_AMOUNT_ONLY => __t('-Amount', true),
                        self::NEGATIVE_CURRENCY_FORMAT_AMOUNT_WITH_PARENTHESIS => __t('(Amount)', true),
                    ]
                    , 'default' => self::NEGATIVE_CURRENCY_FORMAT_AMOUNT_ONLY
                ],
            ],
            PluginUtil::COMMISSION_PLUGIN => [
                self::COMMISSION_SHEET_AUTO_PAY => ['default' => 1],
            ],

            PluginUtil::ReportsPlugin => [
                'profit_report' => ['default' => NULL],
            ],
            PluginUtil::InvoicesPlugin => [
                'initial_invoice_custom_fields' => ['default' => NULL],
                'disable_estimate_module' => ['default' => 0],
                'invoicing_method' => [
                    'options' => [
                        self::OPTION_INVOICING_METHOD_PRINT => __t('Print (Hard Copy)'),
                        self::OPTION_INVOICING_METHOD_EMAIL => __t('Electronically via Email'),
                        self::OPTION_INVOICING_METHOD_BOTH => __t('Both'),
                    ],
                    'default' => 0
                ],
                'discount_option' => [
                    'options' => [
                        self::OPTION_DISCOUNT_TOTAL => __t('Total Discount'),
                        self::OPTION_DISCOUNT_ITEM => __t('Item Discount'),
                        self::OPTION_DISCOUNT_BOTH => __t('Both'),
                    ]
                    ,
                    'default' => 0
                ],
                'preferred_invoicing_method' => [
                    'options' => [
                        self::OPTION_INVOICING_METHOD_PRINT => __t('Print (Hard Copy)'),
                        self::OPTION_INVOICING_METHOD_EMAIL => __t('Electronically via Email'),
                        self::OPTION_INVOICING_METHOD_BOTH => __t('Both'),
                    ],
                    'default' => 0
                ],
                'sold_item_type' => [
                    'options' => [
                        self::OPTION_SOLD_TYPE_BOTH => __t('Services & Products'),
                        self::OPTION_SOLD_TYPE_PRODUCTS => __t('Products Only'),
                        self::OPTION_SOLD_TYPE_SERVICES => __t('Services Only')
                    ],
                    'default' => 0
                ],
                'invoice_print_method' => [
                    'options' => [
                        self::OPTION_PRINT_WEB_BROWSER => __t('Browser'),
                        self::OPTION_PRINT_PDF => __t('PDF')
                    ],
                    'default' => 0],
                'invoice_fraction_appearing' => [
                    'options' => [
                        self::OPTION_FRACTION_AUTO => __t('Auto'),
                        self::OPTION_FRACTION_SHOW_ALWAYS => __t('Show Always'),
                        self::OPTION_FRACTION_HIDE_ALWAYS => __t('Hide Always'),
                    ],
                    'default' => 0
                ],
            ],
            PluginUtil::InventoryPlugin => [
                'product_code' => ['default' => 0],
                'next_po_number' => ['default' => 1],
                'shipping_billing' => [
                    'options' => [
                        self::OPTION_Show_Default_Information => __t('Show Default Information'),
                        self::OPTION_Show_Custom_Information => __t('Show Custom Information')
                    ],
                    'default' => 0
                ],
                'calculation_method' => [
                    'options' => [
                        1 => __t('Variable Cost'),
                        2 => __t('Fixed Cost'),
                    ]
                ],
                'po_billing_address' => [],
                'po_shipping_address' => [],
                'out_of_stock' => [],
                'update_product_prices' => [],
                'disable_overdraft' => [],
                'enable_multi_units' => [],
                'enable_bundles' => [],
                'default_raw_store' => [],
                'advanced_pricing_options' => [],
                'bundle_type' => [
                    'options' => [
                        self::OPTION_BUNDLE_PACK => __t('Pack'),
                        self::OPTION_BUNDLE_COMPOUND => __t('Compound')
                    ],
                    'default' => self::OPTION_BUNDLE_PACK
                ],
                'default_display_unit' => [
                    'options' => [
                        self::OPTION_UNIT_DISPLAY_DEFAULT => __t('Default'),
                        self::OPTION_UNIT_DISPLAY_SELL => __t('Sell'),
                        self::OPTION_UNIT_DISPLAY_BUY => __t('Buy')
                    ]
                    , 'default' => self::OPTION_UNIT_DISPLAY_DEFAULT
                ],
                'stock_transactions_default_journal_account_id' => [],
                self::CALCULATE_STOCKTAKING_BASED_ON_STOCKTAKING_DATE => [
                    'default' => false
                ]

            ],
            PluginUtil::WebsiteFrontPlugin => [
                'website_front_menu' => ['default' => '{"sections":[{"title":"Gallery Categories","actions":[{"title":"list gallery categories","url":"/owner/website_front/media/index/1"},{"title":"add gallery categoriey","url":"/owner/website_front/media/add/1"}]},{"title":"Galleries","actions":[{"title":"list galleries","url":"/owner/website_front/media/index/2"},{"title":"add gallery","url":"/owner/website_front/media/add/2"}]},{"title":"Gallery Photo","actions":[{"title":"list gallery photos","url":"/owner/website_front/media/index/3"},{"title":"add gallery photo","url":"/owner/website_front/media/add/3"}]}]}'],
                'website_front_media_config' => ['default' => ''],
                'website_front_nav_menu_links' => ['default' => '']
            ],
            PluginUtil::ClientsPlugin => [
//				'plugin_data_type' => 'json',
//				'plugin_data_field_name' => 'clients_plugin_extra_fields',
                'photo' => ['default' => 0],
                'birth_date' => ['default' => 0],
                'gender' => ['default' => 0],
                'map_location' => ['default' => 0],
                'starting_balance' => ['default' => 0],
                'client_disable_online_access' => ['default' => 1],
                'client_test' => ['default' => 1], //for test purpose
                //booking
                'client_permission_view_appointment' => ['default' => 0],
                'client_permission_disable_booking' => ['default' => 0],
                'client_permission_disable_cancel_booking' => ['default' => 0],
                'client_permission_register' => ['default' => 0],
                'client_permission_view_profile' => ['default' => 0],
                'client_permission_edit_profile' => ['default' => 0],
                'client_permission_estimates' => ['default' => 0],
                'client_permission_invoices' => ['default' => 0],
                'client_permission_posts' => ['default' => 0],
                'enable_client_credit_limit' => ['default' => 0],
                'client_credit_period' => ['default' => 0],
                'client_permission_work_orders' => ['default' => 0],
//                'prevent_client_edit_profile' => array('default' => 0),
//                'client_dashboard_type' => array(
//                    'options' => array(
//                        self::OPTION_CLIENT_DASHBOARD_TYPE_ALL_DETAILS => __t('All Details'),
//                        self::OPTION_CLIENT_DASHBOARD_TYPE_BAILING_DETAILS => __t('Billing Details Only'),
//                        self::OPTION_CLIENT_DASHBOARD_TYPE_SHARED_INFO  => __t('Shared Informations Only')
//                    )
//                    , 'default' => 1),
                'client_type' => [
                    'options' => [
                        self::OPTION_CLIENT_TYPE_BOTH => __t('Both'),
                        self::OPTION_CLIENT_TYPE_BUSINESS => __t('Business Only'),
                        self::OPTION_CLIENT_TYPE_INDIVIDUAL => __t('Individual Only')
                    ],
                    'default' => 0
                ],
            ],
            PluginUtil::BookingPlugin => [
                'booking_time_divider' => ['default' => 15],
                'booking_client_payment' => [
                    'options' => [
                        self::BOOKING_CLIENT_PAYMENT_DISABLED => __t('Disabled'),
                        self::BOOKING_CLIENT_PAYMENT_ENABLED => __t('Enabled'),
                        self::BOOKING_CLIENT_PAYMENT_OPTIONAL => __t('Optional'),
                    ],
                    'default' => self::BOOKING_CLIENT_PAYMENT_DISABLED
                ],
            ],
            PluginUtil::AccountingPlugin => [
                'disable_invoice_auto_journal' => ['default' => 0],
                'disable_expense_auto_journal' => ['default' => 0],
                'enable_multi_journal_accounts' => ['default' => 0],
                'clients_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'sales_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                        self::CANCEL_ACCOUNTS_ROUTING => $cancelRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'returns_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                        self::CANCEL_ACCOUNTS_ROUTING => $cancelRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'suppliers_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'purchases_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                        self::CANCEL_ACCOUNTS_ROUTING => $cancelRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'purchase_returns_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                        self::CANCEL_ACCOUNTS_ROUTING => $cancelRoutingText,
                    ], 'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'stores_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'banks_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
                'treasuries_accounts_routing' => [
                    'options' => [
                        self::AUTOMATIC_ACCOUNTS_ROUTING => $autoAccountRoutingText,
                        self::MANUAL_ACCOUNTS_ROUTING => $manualAccountRoutingText,
                    ],
                    'default' => self::DEFAULT_ACCOUNTS_ROUTING,
                ],
            ],
            PluginUtil::ProductsPlugin => [
                'barcode_type' => [
                    'options' => [
                        'C128' => __t('Default Code 128'),
                        'EAN13' => __t('EAN 13')
                    ]
                    , 'default' => 'EAN13']
            ],
            PluginUtil::BranchesPlugin => [
                'share_clients' => ['default' => false],
                'share_suppliers' => ['default' => false],
                'share_products' => ['default' => false],
                'main_branch' => ['default' => 1],
                'specify_accounts_branches' => ['default' => 0],
            ],
            PluginUtil::HRM_ATTENDANCE_PLUGIN => [
                'fiscal_date_day' => ['default' => 1],
                'fiscal_date_month' => ['default' => 1],

            ],
            PluginUtil::HRM_PAYROLL_PLUGIN => [
                'staff_account_routing' => [
                    'options' => self::staffPayrollAccountSelectionOptions(),
                    'default' => self::STAFF_PAYROLL_ACCOUNT_AUTO
                ],
            ],
            PluginUtil::MUDAD_PLUGIN => [
                'mudad_salary_components'=> [
                    'default'=> json_encode([
                        ['name' => 'الراتب الأساسي', 'salary_components' => []],
                        ['name' => 'بدل السكن', 'salary_components' => []],
                        ['name' => 'بدل النقل', 'salary_components' => []],
                    ])
                ],
                'mudad_default_headers_components'=> [
                    'default'=> json_encode([])
                ],
            ],
            PluginUtil::HRM_CHEQUE_CYCLE_PLUGIN =>[
                'payable_notes_account_routing' => [
                    'options' => self::accountRoutingOptions(),
                    'default' => self::ACCOUNT_ROUTING_OPTION_AUTO
                ],
                'receivable_notes_account_routing' => [
                    'options' => self::accountRoutingOptions(),
                    'default' => self::ACCOUNT_ROUTING_OPTION_AUTO
                ],
            ]
        ];
        return $structure;
    }

	public static function isCli() {
		return php_sapi_name() === 'cli';
	}

    private function getPluginValues($plugin_id, $allowCache = true, $applyBranches = true)
    {
        $data = $this->getCache($plugin_id);
        if ($allowCache && !self::isCli() && !empty($data) && is_array($data)) {
	        return $data;
        }
        $data = [];
        $st = $this->getStructure();
        if (!$applyBranches || $plugin_id == PluginUtil::BranchesPlugin) {
            $this->settingsRepository->disableBranchOnFind();
        } else {
            $this->settingsRepository->enableBranchOnFind();
        }
        $result = $this->settingsRepository->findWhere([['plugin_id', $plugin_id]]);
        foreach ($result as $row) {
            if (isset($st[$plugin_id]['plugin_data_type'])) {
                $data = json_decode($row['value']);
            } else {
                $data[$row['key']] = $row['value'];
            }
        }
        if ($applyBranches){
            $this->cache($plugin_id, $data);
        }
        return $data;
    }

    /**
     * get value
     * @param $plugin_id
     * @param $key
     * @param null $st get data from pre-defined structure
     * @param bool $allowCache
     * @param bool $applyBranches
     * @return mixed|null
     */
    public function getValue($plugin_id, $key, $st = null, $allowCache = true, $applyBranches = true)
    {
        if ($st === null) {
            $st = $this->getStructure();
        }
        $tmp = $this->getPluginValues($plugin_id, $allowCache, $applyBranches);
        if (isset($tmp[$key])) {
            return $tmp[$key];
        } else if (isset($st[$plugin_id][$key]['default'])) {
            return $st[$plugin_id][$key]['default'];
        } else {
            if ($applyBranches && PluginUtil::BranchesPlugin != $plugin_id && Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                $mainBranchID = settings::getValue(PluginUtil::BranchesPlugin, 'main_branch');
                $currentBranchID = Branch::getCurrentBranchID();
                if ($mainBranchID != $currentBranchID) {
                    $result = $this->settingsRepository->findWhere([['plugin_id', $plugin_id], ['key', $key], ['branch_id', $mainBranchID]])->last();
                    return empty($result) ? null : $result['value'];
                }
            }
        }
        return null;
    }

    public function setValue($plugin_id, $key, $value, $allowCache = true, $applyBranches = true)
    {
        if (!$applyBranches || $plugin_id == PluginUtil::BranchesPlugin) {
            $this->settingsRepository->disableBranchOnFind();
        } else {
            $this->settingsRepository->enableBranchOnFind();
        }

        $data = $this->settingsRepository
            ->findWhere([['plugin_id', $plugin_id], ['key', $key]], ['*'], $applyBranches)
            ->first();

        if (empty($data)) {
            $this->settingsRepository->add(['plugin_id' => $plugin_id, 'key' => $key, 'value' => $value]);
        } else {
            $this->settingsRepository->update($data['id'], ['value' => $value]);
        }
        $this->removeCache($plugin_id);
    }

    //Cache Functions
    private function getCache($plugin_id)
    {
        return Cache::get($this->getCacheFileName($plugin_id));
    }

    private function getCacheFileName($plugin_id)
    {
        $site_id = getCurrentSite('id');
        $branch_id_string = $plugin_id != PluginUtil::BranchesPlugin ? '_' . Branch::getCurrentBranchID() : '';
        return "plugin_values_" . $site_id . '_' . $plugin_id . $branch_id_string;
    }

    private function cache($plugin_id, $data)
    {
        Cache::put($this->getCacheFileName($plugin_id), $data, 3600 * 24 * 7);
    }

    private function removeCache($plugin_id)
    {
        Cache::forget($this->getCacheFileName($plugin_id));
    }

    public static function staffPayrollAccountSelectionOptions()
    {
        return [
            self::STAFF_PAYROLL_ACCOUNT_AUTO => __t('Auto'),
            self::STAFF_PAYROLL_ACCOUNT_SPECIFY => __t('Specify')
        ];
    }

    public static function accountRoutingOptions()
    {
        return [
            self::ACCOUNT_ROUTING_OPTION_AUTO => __t('Auto'),
            self::ACCOUNT_ROUTING_OPTION_SPECIFY => __t('Specify')
        ];
    }
}
