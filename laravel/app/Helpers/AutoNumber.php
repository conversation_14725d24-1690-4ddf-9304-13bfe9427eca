<?php
namespace App\Helpers;
use App\Facades\Plugins;
use App\Facades\Settings;
use App\Models\PurchaseRequest;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\QueryCriteria;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaBuilders\Criteria\SortCriteria;
use App\Modules\LocalEntity\AppEntities\Listing\CriteriaExecutors\DefaultCriteriaExecutor;
use App\Modules\LocalEntity\Listing\FilterHelper;
use App\Modules\LocalEntity\Prototype\Filters\FilterMeta;
use App\Repositories\Criteria\CustomFind;
use App\Repositories\Criteria\JoinCriteria;
use App\Facades\Branch;
use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\ProductRepository;
use App\Repositories\PurchaseRequestRepository;
use App\Repositories\QuotationRequestRepository;
use App\Repositories\StockRequestRepository;
use App\Utils\PluginUtil;
use App\Utils\PurchaseInvoiceTypesUtil;
use Illuminate\Support\Facades\DB;
use Izam\AutoNumber\AutoNumberAbstract;
use Izam\Daftra\Common\EntityStructure\Entity;
use Izam\Daftra\Common\EntityStructure\IEntityStructureGetter;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Rental\Repository\LeaseContractRepository;

class AutoNumber extends AutoNumberAbstract {
	const ContinousOverallPrefixes = 0;
	const SeparatedForPrefixes = 1;
	const ResetWhenPrefixChanges = 2;

	CONST TYPE_CLIENT = 0;
	CONST TYPE_INVOICE = 1;
	CONST TYPE_ESTIMATE = 2;
	CONST TYPE_REFUND_RECEIPT = 3;
	CONST TYPE_CREDIT_NOTE = 4;
	CONST TYPE_PURCHASE_INVOICE = 5;
	CONST TYPE_PURCHASE_REFUND = 6;
	CONST TYPE_WORK_ORDER = 7;
	CONST TYPE_SUPPLIER = 8;
	CONST TYPE_JOURNAL = 9;
	CONST TYPE_EXPENSE = 10;
	CONST TYPE_INCOME = 11;
	CONST TYPE_REQUISITION_INBOUND = 12;
	CONST TYPE_REQUISITION_OUTBOUND = 13;
	CONST TYPE_BOOKING = 14;
	CONST TYPE_REQUISITION_TRANSFER = 15;
    CONST TYPE_BRANCHES = 16;
    CONST TYPE_STOCKTAKING = 17;
    CONST TYPE_PRODUCT = 18;
    CONST TYPE_TEMPINVOICE=19;
    const TYPE_PAYROLL_CONTRACT = 20;
    const TYPE_PURCHASE_REQUEST = 21;
    const TYPE_QUOTATION_REQUEST = 22;
	const TYPE_PURCHASE_QUOTATION = 23;
	const TYPE_PURCHASE_ORDER = 24;
	const TYPE_RESERVATION_ORDER = 26;
	const TYPE_ASSET = 27;
	const TYPE_INVOICE_PAYMENT = 28;
	const TYPE_REFUND_PAYMENT = 29;
	const TYPE_PURCHASE_INVOICE_PAYMENT = 30;
	const TYPE_PURCHASE_REFUND_PAYMENT = 31;
    const TYPE_STOCK_REQUEST = 33;
	const TYPE_PURCHASE_DEBIT_NOTE = 36;

    const TYPE_PRODUCTION_ROUTING = 37;
    const TYPE_WORKSTATION = 38;
    const TYPE_BOM = 45;
    const TYPE_MANUFACTURING_ORDER = 46;
	const TYPE_PRODUCTION_PLAN = 48;
    const TYPE_STAFF = 49;
    const TYPE_LEASE_CONTRACT = 51;
    const TYPE_MILEAGE = 50;

    const TYPE_ADVANCE_PAYMENT_INVOICE = 53;

    static $globalAutoNumberTypes = [
        self::TYPE_BRANCHES,
        self::TYPE_PAYROLL_CONTRACT,
        self::TYPE_PRODUCTION_ROUTING,
        self::TYPE_WORKSTATION,
        self::TYPE_STAFF,
    ];

    static $options = [
		self::TYPE_CLIENT => ['label'=>"Client", 'plugin'=> PluginUtil::ClientsPlugin],
		self::TYPE_INVOICE => ['label'=>"Invoice", 'plugin'=> PluginUtil::InvoicesPlugin],
		self::TYPE_ESTIMATE => ['label'=>"Estimate", 'plugin'=> PluginUtil::InvoicesPlugin],
		self::TYPE_REFUND_RECEIPT => ['label'=>"Refund Receipt", 'plugin'=> PluginUtil::InvoicesPlugin],
		self::TYPE_CREDIT_NOTE => ['label'=>"Credit Note", 'plugin'=> PluginUtil::InvoicesPlugin],
		self::TYPE_BOOKING => ['label'=>"Booking", 'plugin'=> PluginUtil::InvoicesPlugin],
		self::TYPE_PURCHASE_INVOICE => ['label'=>"Purchase Invoice", 'plugin'=> PluginUtil::InventoryPlugin],
		self::TYPE_PURCHASE_REFUND => ['label'=>"Purchase Refund", 'plugin'=> PluginUtil::InventoryPlugin],
		self::TYPE_WORK_ORDER => ['label'=>"Work Order", 'plugin'=> PluginUtil::WorkOrderPlugin],
		self::TYPE_SUPPLIER => ['label'=>"Supplier", 'plugin'=>null],
		self::TYPE_JOURNAL => ['label'=>"Journal", 'plugin'=> PluginUtil::AccountingPlugin],
		self::TYPE_EXPENSE => ['label'=>"Expense", 'plugin'=> PluginUtil::AccountingPlugin],
		self::TYPE_INCOME => ['label'=>"Income", 'plugin'=> PluginUtil::AccountingPlugin],
		self::TYPE_REQUISITION_INBOUND => ['label'=>"Inbound Requisition", 'plugin'=> PluginUtil::InventoryPlugin],
		self::TYPE_REQUISITION_OUTBOUND => ['label'=>"Outbound Requisition", 'plugin'=> PluginUtil::InventoryPlugin],
		self::TYPE_REQUISITION_TRANSFER => ['label'=>"Transfer Requisition", 'plugin'=> PluginUtil::InventoryPlugin],
		self::TYPE_BRANCHES => ['label'=>"Branches", 'plugin'=> PluginUtil::BranchesPlugin],
        self::TYPE_STOCKTAKING => ['label'=>"Stocktakings", 'plugin'=> PluginUtil::InventoryPlugin],
		self::TYPE_PRODUCT => ['label'=>"Products", 'plugin'=> PluginUtil::InventoryPlugin],
		self::TYPE_TEMPINVOICE => ['label'=>"Temp Invoice", 'plugin'=> PluginUtil::InvoicesPlugin],
        self::TYPE_PAYROLL_CONTRACT => ['label' => 'Contracts', 'plugin' => PluginUtil::HRM_PAYROLL_PLUGIN],
        self::TYPE_PURCHASE_REQUEST => ['label' => 'Purchase Request', 'plugin' => PluginUtil::AutoNumberPlugin],
        self::TYPE_QUOTATION_REQUEST => ['label' => 'Quotation Request', 'plugin' => PluginUtil::AutoNumberPlugin],
        self::TYPE_PURCHASE_QUOTATION => ['label' => 'Purchase Quotation', 'plugin' => PluginUtil::AutoNumberPlugin],
        self::TYPE_PURCHASE_ORDER => ['label' => 'Purchase Order', 'plugin' => PluginUtil::AutoNumberPlugin],
        self::TYPE_RESERVATION_ORDER => ['label' => 'RENTAL PLUGIN', 'plugin' => PluginUtil::RENTAL_PLUGIN],
        self::TYPE_ASSET => ['label' => 'Asset', 'plugin' => PluginUtil::AccountingPlugin],
        self::TYPE_INVOICE_PAYMENT => ['label' => 'Invoice Payment', 'plugin' => PluginUtil::InvoicesPlugin],
        self::TYPE_REFUND_PAYMENT => ['label' => 'Refund Payment', 'plugin' => PluginUtil::InvoicesPlugin],
        self::TYPE_PURCHASE_INVOICE_PAYMENT => ['label' => 'Purchase Invoice Payment', 'plugin' => PluginUtil::InventoryPlugin],
        self::TYPE_PURCHASE_REFUND_PAYMENT => ['label' => 'Purchase Refund Payment', 'plugin' => PluginUtil::InventoryPlugin],
		self::TYPE_STOCK_REQUEST => ['label' => 'Stock Requests', 'plugin' => PluginUtil::InventoryPlugin],
		self::TYPE_PURCHASE_DEBIT_NOTE => ['label' => 'Purchase Credit Note', 'plugin' => PluginUtil::InventoryPlugin],
		self::TYPE_PRODUCTION_ROUTING => ['label' => 'Production Routings', 'plugin' => PluginUtil::MANUFACTURING_PLUGIN],
		self::TYPE_WORKSTATION => ['label' => 'Workstation', 'plugin' => PluginUtil::MANUFACTURING_PLUGIN],
		self::TYPE_BOM => ['label' => 'Bill of Materials', 'plugin' => PluginUtil::MANUFACTURING_PLUGIN],
        self::TYPE_MANUFACTURING_ORDER => ['label' => 'Manufacturing Order', 'plugin' => PluginUtil::MANUFACTURING_PLUGIN],
        self::TYPE_PRODUCTION_PLAN => ['label' => 'Production Plan', 'plugin' => PluginUtil::MANUFACTURING_PLUGIN],
        self::TYPE_LEASE_CONTRACT => ['label'=>"Lease Contracts", 'plugin'=> PluginUtil::LEASE_CONTRACT_PLUGIN],
        self::TYPE_STAFF => ['label'=>"Employees", 'plugin'=> PluginUtil::StaffPlugin],
        self::TYPE_MILEAGE => ['label'=>"Mileage", 'plugin'=> PluginUtil::MILEAGE_PLUGIN],

    ];

	static $fields = [
        self::TYPE_CLIENT => ['entityKey'=> EntityKeyTypesUtil::CLIENT_ENTITY_KEY, 'model'=>"Client", 'field' => 'client_number', 'conditions'=> []],
        self::TYPE_INVOICE => ['entityKey'=> EntityKeyTypesUtil::INVOICE_ENTITY_KEY, 'model'=>"Invoice", 'field' => 'no', 'conditions'=> ['type' => 0]],
        self::TYPE_ESTIMATE => ['entityKey'=> EntityKeyTypesUtil::ESTIMATE, 'model'=>"Invoice", 'field' => 'no', 'conditions'=> ['type' => 3]],
        self::TYPE_REFUND_RECEIPT => ['entityKey'=> EntityKeyTypesUtil::REFUND_RECEIPT, 'model'=>"Invoice", 'field' => 'no', 'conditions'=> ['type' => 6]],
        self::TYPE_CREDIT_NOTE => ['entityKey'=> EntityKeyTypesUtil::CREDIT_NOTE, 'model'=>"Invoice", 'field' => 'no', 'conditions'=> ['type' => 5]],
        self::TYPE_BOOKING => ['entityKey'=> EntityKeyTypesUtil::BOOKING, 'model'=>"Invoice", 'field' => 'no', 'conditions'=> ['type' => 8]],
        self::TYPE_PURCHASE_INVOICE => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_INVOICE, 'model'=>"PurchaseOrder", 'field' => 'no', 'conditions'=> ['type' => PurchaseInvoiceTypesUtil::PURCHASE_INVOICE]],
        self::TYPE_PURCHASE_REFUND => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_REFUND, 'model'=>"PurchaseOrder", 'field' => 'no', 'conditions'=> ['type' => 6]],
        self::TYPE_WORK_ORDER => ['entityKey'=> EntityKeyTypesUtil::WORK_ORDER, 'model'=>"WorkOrder", 'field' => 'number', 'conditions'=> []],
        self::TYPE_SUPPLIER => ['entityKey'=> EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY, 'model'=>"Supplier", 'field' => 'supplier_number', 'conditions'=> []],
        self::TYPE_JOURNAL => ['entityKey'=> EntityKeyTypesUtil::JOURNAL, 'model'=>"Journal", 'field' => 'number', 'conditions'=> []],
        self::TYPE_EXPENSE => ['entityKey'=> EntityKeyTypesUtil::EXPENSE, 'model'=>"Expense", 'field' => 'expense_number', 'conditions'=> ['is_income' => 0]],
        self::TYPE_INCOME => ['entityKey'=> EntityKeyTypesUtil::INCOME, 'model'=>"Expense", 'field' => 'expense_number', 'conditions'=> ['is_income' => 1]],
        self::TYPE_REQUISITION_INBOUND => ['entityKey'=> EntityKeyTypesUtil::REQUISITION, 'model'=>"Requisition", 'field' => 'number', 'conditions'=> ['type' => 1]],
        self::TYPE_REQUISITION_OUTBOUND => ['entityKey'=> EntityKeyTypesUtil::REQUISITION,'model'=>"Requisition", 'field' => 'number', 'conditions'=> ['type' => 2]],
        self::TYPE_REQUISITION_TRANSFER => ['entityKey'=> EntityKeyTypesUtil::REQUISITION, 'model'=>"Requisition", 'field' => 'number', 'conditions'=> ['type' => 3]],
        self::TYPE_BRANCHES => ['entityKey'=> EntityKeyTypesUtil::BRANCH_ENTITY_KEY, 'model'=>"Branch", 'field' => 'code', 'conditions'=> []],
        self::TYPE_STOCKTAKING => ['entityKey'=> EntityKeyTypesUtil::STOCKTAKING, 'model'=>"StockTaking", 'field' => 'number', 'conditions'=> []],
        self::TYPE_PRODUCT => ['entityKey'=> EntityKeyTypesUtil::PRODUCT_ENTITY_KEY, 'model'=>"Product", 'field' => 'product_code', 'conditions'=> []],
        self::TYPE_TEMPINVOICE => ['entityKey'=> EntityKeyTypesUtil::INVOICE_TEMPLATE_ENTITY_KEY, 'model'=>"Invoice", 'field' => 'no', 'conditions'=> ['type' => 9]],
        self::TYPE_PAYROLL_CONTRACT => ['entityKey'=> EntityKeyTypesUtil::PAYROLL_CONTRACT, 'model'=>"Payroll", 'field' => 'code', 'conditions'=> []],
        self::TYPE_PURCHASE_REQUEST => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_REQUEST, 'model'=>"PurchaseRequest", 'field' => 'code', 'conditions'=> []],
        self::TYPE_QUOTATION_REQUEST => ['entityKey'=> EntityKeyTypesUtil::QUOTATION_REQUEST, 'model'=>"QuotationRequest", 'field' => 'code', 'conditions'=> []],
        self::TYPE_PURCHASE_QUOTATION => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_QUOTATION, 'model'=>"PurchaseOrder", 'field' => 'no', 'conditions'=> ['type' => PurchaseInvoiceTypesUtil::PURCHASE_QUOTATION]],
        self::TYPE_PURCHASE_ORDER => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_ORDER, 'model'=>"PurchaseOrder", 'field' => 'no', 'conditions'=> ['type' => PurchaseInvoiceTypesUtil::PURCHASE_ORDER]],
        self::TYPE_RESERVATION_ORDER => ['entityKey'=> EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER, 'model'=>"App\Modules\Rental\Models\ReservationOrder", 'field' => 'code', 'conditions'=> []],
        self::TYPE_ASSET => ['entityKey'=> EntityKeyTypesUtil::ASSET, 'model'=>"Asset", 'field' => 'code', 'conditions'=> []],
		self::TYPE_INVOICE_PAYMENT => ['entityKey'=> EntityKeyTypesUtil::INVOICE_PAYMENT_ENTITY_KEY, 'model' => "InvoicePayment", 'field' => 'code', 'conditions' => ['Invoice.type' => 0]],
		self::TYPE_REFUND_PAYMENT => ['entityKey'=> EntityKeyTypesUtil::INVOICE_PAYMENT_ENTITY_KEY, 'model' => "InvoicePayment", 'field' => 'code', 'conditions' => ['Invoice.type' => 6]],
        self::TYPE_PURCHASE_INVOICE_PAYMENT => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_ORDER_PAYMENT_ENTITY_KEY, 'model' => "PurchaseOrderPayment", 'field' => 'code', 'conditions' => ['PurchaseOrder.type' => 0]],
        self::TYPE_PURCHASE_REFUND_PAYMENT => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_ORDER_PAYMENT_ENTITY_KEY, 'model' => "PurchaseOrderPayment", 'field' => 'code', 'conditions' => ['PurchaseOrder.type' => 6]],
		self::TYPE_STOCK_REQUEST => ['entityKey'=> EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY, 'model' => "StockRequest", 'field' => 'code', 'conditions' => ['type' => self::TYPE_STOCK_REQUEST]],
		self::TYPE_PURCHASE_DEBIT_NOTE => ['entityKey'=> EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE, 'model' => "PurchaseOrder", 'field' => 'code', 'conditions' => ['type' => PurchaseInvoiceTypesUtil::CREDIT_NOTE]],
		self::TYPE_PRODUCTION_ROUTING => ['entityKey'=> EntityKeyTypesUtil::PRODUCTION_ROUTING_ENTITY_KEY, 'model' => "ProductionRouting", 'field' => 'code'],
		self::TYPE_WORKSTATION => ['entityKey'=> EntityKeyTypesUtil::WORKSTATION_ENTITY_KEY, 'model' => "Workstation", 'field' => 'code'],
		self::TYPE_BOM => ['entityKey'=> EntityKeyTypesUtil::BOM_ENTITY_KEY, 'model' => "Bom", 'field' => 'code'],
		self::TYPE_MANUFACTURING_ORDER => ['entityKey'=> EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY, 'model' => "ManufacturingOrder", 'field' => 'code'],
		self::TYPE_PRODUCTION_PLAN => ['entityKey' => EntityKeyTypesUtil::PRODUCTION_PLAN, 'model' => "ProductionPlan", 'field' => 'code'],
        self::TYPE_STAFF => ['entityKey' => EntityKeyTypesUtil::STAFF_ENTITY_KEY, 'model'=>"Staff", 'field' => 'code', 'conditions'=> []],
        self::TYPE_LEASE_CONTRACT => ['entityKey' => EntityKeyTypesUtil::LEASE_CONTRACT, 'model'=>"LeaseContract", 'field' => 'code', 'conditions'=> []],
        self::TYPE_MILEAGE => ['entityKey'=> EntityKeyTypesUtil::EXPENSE, 'model'=>"Expense", 'field' => 'expense_number', 'conditions'=> ['is_mileage' => 1]],
    ];


    public static function getAutoNumberField(string $entityKey)
    {
        $temp = array_filter(static::$fields, function ($field) use ($entityKey) {
            return $field['entityKey'] === $entityKey;
        });
        $autoNumberDetails = array_shift($temp);
        return $autoNumberDetails['field'] ?? null;
    }

	/**
	 * Attempt to update number from user input
	 * @param string $number
	 * @param int $type
	 * @param int $branch_id
	 */
	public static function update_last_from_number($number, $type, $branch_id = 0) {
		$auto_number = self::get_auto_serial($type, 0, true);
		$prefix_len = strlen($auto_number[0]);
		if(substr($number, 0, $prefix_len) == $auto_number[0] &&
				self::validate(substr($number, $prefix_len), null, $type) && substr($number, $prefix_len)>=$auto_number[1]){
			self::set_last_serial([$auto_number[0], substr($number, $prefix_len)], $type);
		}
	}

	/**
	 * Validate custom number to match the pattern
	 * @param string $last_number
	 * @param int $type
	 * @param int $branch_id
	 * @return boolean
	 */
	public static function validate($last_number, $pattern, $type=null, $branch_id = 0) {
		if(empty($pattern)){
			$record = Settings::getValue(PluginUtil::AutoNumberPlugin, "{$type}-{$branch_id}", null, false);
            if(empty($record)){
//                            notify_admin_fetal_error("AutoNumberError");
                return false;
            }
			$decoded = json_decode($record, true);
			preg_match_all('/[0-9]*[cCxXd]/',$decoded["number_pattern"],$pattern_parts);
		}else{
			preg_match_all('/[0-9]*[cCxXd]/',$pattern,$pattern_parts);
		}
		$pattern_parts = $pattern_parts[0];
		//split last number by the pattern except for last pattern to handle overflow
		$last_number_parts = [];
		for($i=0; $i<count($pattern_parts)-1; $i++) {
			$count = intval(substr($pattern_parts[$i], 0, -1)) ?: 1;
			$type = substr($pattern_parts[$i], -1);
			$last_number_parts[]= [$count, $type, substr($last_number, 0, $count)];
			$last_number = substr($last_number, $count);
		}
		//handle last pattern
		$last_pattern = end($pattern_parts);
		$last_pcount = intval(substr($last_pattern, 0, -1)) ?: 1;
		$last_ptype = substr($last_pattern, -1);
		$last_number_parts[]= [$last_pcount, $last_ptype, $last_number];
		foreach ($last_number_parts as $last_number_part) {
			if(strlen($last_number_part[2])<$last_number_part[0]) return false;
			switch ($last_number_part[1]) {
				case "x": $pattern = "/^[0-9a-f]+$/"; break;
				case "X": $pattern = "/^[0-9A-F]+$/"; break;
				case "c": $pattern = "/^[a-z]+$/"; break;
				case "C": $pattern = "/^[A-Z]+$/"; break;
				case "d": $pattern = "/^[0-9]+$/"; break;
			}
			if(!preg_match($pattern, $last_number_part[2])) return false;
		}
		return true;
	}
    protected static function getOldSerialQuery(\Izam\Daftra\Common\EntityStructure\Entity $entity, $field) {
        $criteria = new QueryCriteria();
        $criteria->setConnection($entity->getConnection())
            ->setTable($entity->getTable());
        $criteria->addSort(new SortCriteria($field->getName(),'DESC'));
        if($entity->isExtendedEntity()) {
            $filterMeta = FilterHelper::processFilters($entity->getExtendedData()->getFilters());
            $criteria->setWheres($filterMeta);
        }
        $executor = new DefaultCriteriaExecutor();
        return $executor->process($criteria)->first();
    }
	/**
	 * Create rule and return next number based on old rules
	 * @param int $type
	 * @return int old serial
	 */
	public static function _get_old_serial($type) {
        if($entityData = self::getTypeEntityData($type)) {
            if($entityData) {
                $field = $entityData['field'];
                /**
                 * @var Entity $entityRecord
                 */
                $entityRecord = $entityData['entity'];
                $result = self::getOldSerialQuery($entityRecord, $field);
                if($result) {
                    $return =  $result->{$field->getName()};
                } else {
                    $return = 1;
                }
                $return = sprintf("%06d", $return);
            }
        } else {
            switch ($type) {
                case self::TYPE_CLIENT:
                    $field = 'client_number';
                    $repo = app()->make('App\Repositories\ClientRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first();
                    $return = intval($lastRecord->{$field}) + 1;
                    break;
                case self::TYPE_INVOICE:
                case self::TYPE_REFUND_RECEIPT:
                case self::TYPE_CREDIT_NOTE:
                    $site_id = getAuthOwner('id');
                    $field = 'next_invoice_number';
                    $repo = app()->make('App\Repositories\SiteRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => 'id', 'value' => $site_id, 'direction' => 'DESC']));
                    $lastRecord = $repo->first();
                    $return = intval($lastRecord->{$field});
                    break;
                case self::TYPE_ESTIMATE:
                    $field = 'no';
                    $repo = app()->make('App\Repositories\InvoiceRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'type', 'value' => 3]], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first();
                    $invoiceNo = self::_get_old_serial(self::TYPE_INVOICE);
                    $pad = strlen($invoiceNo);
                    $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    break;
                case self::TYPE_BOOKING:
                    $field = 'no';
                    $repo = app()->make('App\Repositories\InvoiceRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'type', 'value' => 8]], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first();
                    $invoiceNo = self::_get_old_serial(self::TYPE_INVOICE);
                    $pad = 6;
					if($lastRecord){
						$return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
					}else{
						$return = sprintf("%0{$pad}d", 1);
					}
                    break;
                case self::TYPE_PURCHASE_REQUEST:
                case self::TYPE_QUOTATION_REQUEST:
				case self::TYPE_STOCK_REQUEST:

                    $field = 'code';
                    $repos = [
                        self::TYPE_PURCHASE_REQUEST => PurchaseRequestRepository::class,
                        self::TYPE_QUOTATION_REQUEST => QuotationRequestRepository::class,
						self::TYPE_STOCK_REQUEST => StockRequestRepository::class,
                    ];
                    $repo = app()->make($repos[$type]);
                    $repo->pushCriteria(new CustomFind([
                        ['field' => $field, 'direction' => 'DESC'],
                    ]));

                    $repo->pushCriteria(
                        new EqualCriteria('branch_id', Branch::getCurrentBranchID())
                    );
                    $lastRecord = $repo->first();
                    $pad = 6;
                    if (!empty($lastRecord)) {
                        $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    } else {
                        return sprintf("%06d", 1);
                    }

                    break;
                case self::TYPE_TEMPINVOICE:
                    $field = 'no';
                    $repo = app()->make('App\Repositories\InvoiceRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'type', 'value' => 9]], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first();
                    $invoiceNo = self::_get_old_serial(self::TYPE_INVOICE);
                    $pad = strlen($invoiceNo);
                    $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    break;
                case self::TYPE_PURCHASE_INVOICE:
                case self::TYPE_PURCHASE_REFUND:
                    $return = Settings::getValue(PluginUtil::InventoryPlugin, 'next_po_number');
                    break;
                case self::TYPE_WORK_ORDER:
                    $field = 'number';
                    $repo = app()->make('App\Repositories\WorkOrderRepository');
                    $next_number  = Settings::getValue(PluginUtil::WorkOrderPlugin, 'next_workorder_number');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = max([$next_number,intval($next_number->{$field}+1)]) ;
                    break;
                case self::TYPE_SUPPLIER:
                    $field = 'supplier_number';
                    $repo = app()->make('App\Repositories\SupplierRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_JOURNAL:
                    $field = 'id';
                    $repo = app()->make('App\Repositories\JournalRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_EXPENSE:
                    $field = 'id';
                    $repo = app()->make('App\Repositories\ExpenseRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'is_income', 'value' => 0 ]],['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_INCOME:
                    $field = 'id';
                    $repo = app()->make('App\Repositories\ExpenseRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'is_income', 'value' => 1 ]],['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_MILEAGE:
                    $field = 'id';
                    $repo = app()->make('App\Repositories\ExpenseRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'is_mileage', 'value' => 1 ]],['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_REQUISITION_INBOUND:
                    $field = 'id';
                    $repo = app()->make('App\Repositories\RequisitionRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'type', 'value' => 1 ]],['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_REQUISITION_OUTBOUND:
                    $field = 'id';
                    $repo = app()->make('App\Repositories\RequisitionRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'type', 'value' => 2 ]],['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_REQUISITION_TRANSFER:
                    $field = 'id';
                    $repo = app()->make('App\Repositories\RequisitionRepository');
                    $repo->pushCriteria(new CustomFind([['field' => 'type', 'value' => 3 ]],['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_BRANCHES:
                    $return = '00001';
                    if (ifPluginActive(PluginUtil::BranchesPlugin)) {
                        $field = 'id';
                        $repo = app()->make('App\Repositories\BranchRepository');
                        $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                        $lastRecord = $repo->first() ;
                        $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    }
                    break;
                case self::TYPE_STOCKTAKING:
                    $field = 'number';
                    $repo = app()->make('App\Repositories\StockTakingRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_PRODUCT:
                    $field = 'product_code';
                    $repo = app()->make(ProductRepository::class);
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_PAYROLL_CONTRACT:
                    $field = 'code';
                    $repo = app()->make('App\Repositories\ContractRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    break;
                case self::TYPE_RESERVATION_ORDER:
                    $field = 'order_number';
                    $repo = app()->make('App\Modules\Rental\Repositories\ReservationOrderRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $repo->pushCriteria(new EqualCriteria("is_temp", 0));
                    $lastRecord = $repo->first();
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : "000001";
                    break;
				case self::TYPE_ASSET:
					$field = 'code';
					$repo = app()->make('App\Repositories\AssetRepository');
					$repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
					$lastRecord = $repo->first() ;
					$return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
					break;
				case self::TYPE_INVOICE_PAYMENT:
					$field = 'code';
					$repo = app()->make('App\Repositories\InvoicePaymentRepository');
					$repo->pushCriteria(new CustomFind(
						['operation' => FilterOperations::MULTI_JOIN,['joins' =>
							[
								['table' => 'invoices', 'conditions' =>
									[
										['first' => 'invoices.id', 'operation' => '=', 'second' => 'invoice_payments.invoice_id', 'joinType' => 'field'],
										['first' => 'invoices.type', 'operation' => '=', 'second' => 0, 'joinType' => 'value']
									]
								]
							]
						]], ['field' => $field, 'direction' => 'DESC']));
					$lastRecord = $repo->first() ;
					$return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
					break;
				case self::TYPE_REFUND_PAYMENT:
					$field = 'code';
					$repo = app()->make('App\Repositories\InvoicePaymentRepository');
					$repo->pushCriteria(new CustomFind(
						['operation' => FilterOperations::MULTI_JOIN,['joins' =>
							[
								['table' => 'invoices', 'conditions' =>
									[
										['first' => 'invoices.id', 'operation' => '=', 'second' => 'invoice_payments.invoice_id', 'joinType' => 'field'],
										['first' => 'invoices.type', 'operation' => '=', 'second' => 6, 'joinType' => 'value']
									]
								]
							]
						]], ['field' => $field, 'direction' => 'DESC']));
					$lastRecord = $repo->first() ;
					$return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
					break;
				case self::TYPE_PURCHASE_INVOICE_PAYMENT:
					$field = 'code';
					$repo = app()->make('App\Repositories\PurchaseOrderPaymentRepository');
					$repo->pushCriteria(new CustomFind(
						['operation' => FilterOperations::MULTI_JOIN,['joins' =>
							[
								['table' => 'purchase_orders', 'conditions' =>
									[
										['first' => 'purchase_orders.id', 'operation' => '=', 'second' => 'purchase_order_payments.purchase_order_id', 'joinType' => 'field'],
										['first' => 'purchase_orders.type', 'operation' => '=', 'second' => 0, 'joinType' => 'value']
									]
								]
							]
						]], ['field' => $field, 'direction' => 'DESC']));
					$lastRecord = $repo->first() ;
					$return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
					break;
				case self::TYPE_PURCHASE_REFUND_PAYMENT:
					$field = 'code';
					$repo = app()->make('App\Repositories\PurchaseOrderPaymentRepository');
					$repo->pushCriteria(new CustomFind(
						['operation' => FilterOperations::MULTI_JOIN,['joins' =>
							[
								['table' => 'purchase_orders', 'conditions' =>
									[
										['first' => 'purchase_orders.id', 'operation' => '=', 'second' => 'purchase_order_payments.purchase_order_id', 'joinType' => 'field'],
										['first' => 'purchase_orders.type', 'operation' => '=', 'second' => 6, 'joinType' => 'value']
									]
								]
							]
						]], ['field' => $field, 'direction' => 'DESC']));
					$lastRecord = $repo->first() ;
					$return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
					break;
                case self::TYPE_PRODUCTION_ROUTING:
                    $field = 'code';
                    $repo = app()->make('App\Repositories\ProductionRoutingRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $pad = 6;
                    if (!empty($lastRecord)) {
                        $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    } else {
                        return sprintf("%06d", 1);
                    }
                    break;
                case self::TYPE_WORKSTATION:
                    $field = 'code';
                    $repo = app()->make('App\Repositories\WorkstationRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $pad = 6;
                    if (!empty($lastRecord)) {
                        $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    } else {
                        return sprintf("%06d", 1);
                    }
                    break;
                case self::TYPE_BOM:
                    $field = 'code';
                    $repo = app()->make('App\Repositories\MaterialBillRepository');
                    $repo->pushCriteria(new CustomFind([
                        ['field' => $field, 'direction' => 'DESC'],
                    ]));
                    if (Plugins::pluginActive(PluginUtil::BranchesPlugin)) {
                        $repo->pushCriteria(
                            new EqualCriteria('branch_id', Branch::getCurrentBranchID())
                        );
                    }
                    $lastRecord = $repo->first();
                    $pad = 6;
                    if (!empty($lastRecord)) {
                        $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    } else {
                        return sprintf("%06d", 1);
                    }
                    break;

                case self::TYPE_PRODUCTION_PLAN:
                    $field = 'code';
					/** @var \App\Repositories\ProductionPlanRepository $repo */
                    $repo = app()->make('App\Repositories\ProductionPlanRepository');
                    $repo->pushCriteria(new CustomFind([
                        ['field' => $field, 'direction' => 'DESC'],
                    ]));
                    $lastRecord = $repo->first();
                    $pad = 6;
                    if (!empty($lastRecord)) {
                        $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    } else {
                        return sprintf("%06d", 1);
                    }
                    break;

                case self::TYPE_MANUFACTURING_ORDER:
                    $field = 'code';
                    $repo = app()->make('App\Repositories\ManufacturingOrderRepository');
                    $repo->pushCriteria(new CustomFind([
                        ['field' => $field, 'direction' => 'DESC'],
                    ]));
                    $repo->pushCriteria(
                        new EqualCriteria('branch_id', Branch::getCurrentBranchID())
                    );
                    $lastRecord = $repo->first();
                    $pad = 6;
                    if (!empty($lastRecord)) {
                        $return = sprintf("%0{$pad}d", intval(intval($lastRecord->{$field}) + 1));
                    } else {
                        return sprintf("%06d", 1);
                    }
                    break;
                case self::TYPE_STAFF:
                    $field = 'code';
                    $repo = app()->make('App\Repositories\StaffRepository');
                    $repo->pushCriteria(new CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    // $return = sprintf("%06d", $return);
                    break;
                case self::TYPE_LEASE_CONTRACT:
                    $field = 'code';
                    $repo = app()->make(LeaseContractRepository::class);
                    //USE SHARED CRITERIA WHEN U USE SHARED REPO
                    $repo->pushCriteria(new \Izam\Database\Repositories\Criteria\CustomFind([], ['field' => $field, 'direction' => 'DESC']));
                    $lastRecord = $repo->first() ;
                    $return = $lastRecord ? intval($lastRecord->{$field}) + 1 : 1;
                    $return = sprintf("%06d", $return);
                    break;



            }

        }
        if(!isset($return)){
		    return 0;
        }
		$count = strlen($return);
		self::set_rule($type, 0, "{$count}d");
		self::set_last_serial(['', sprintf("%{$count}d",intval($return)-1)], $type);
		return $return;
	}

	/**
	 * Get a new serial to use
	 * @param int $type
	 * @param int $branch_id
	 * @param bool $arr if true return array of prefix and number
	 * @return string|false new serial or false if no rule
	 */
	public static function get_auto_serial($type, $branch_id = 0, $arr = false){
		$autonumber = self::_get_auto_serial($type, $branch_id);
		return $autonumber!== false?($arr?$autonumber:$autonumber[0].$autonumber[1]):($arr?['', self::_get_old_serial($type)]:self::_get_old_serial($type));
	}

	/**
	 * Get a new serial to use and set it as used
	 * @param int $type
	 * @param int $branch_id
	 * @return string|false new serial or false if no rule
	 */
	public static function update_auto_serial($type, $branch_id = 0){
		$autonumber = self::_get_auto_serial($type, $branch_id);
		if($autonumber===false) return self::_get_old_serial($type);
		self::set_last_serial($autonumber, $type, $branch_id);
		return $autonumber[0].$autonumber[1];
	}

	/**
	 * Set the new starting point for next serials
	 * @param array $autonumber array that contain the <b>prefix</b> and the <b>number</b><br>Example <b>["db_", "123"]</b>
	 * @param int $type
	 * @param int $branch_id
	 * @return boolean saved or not
	 */
	public static function set_last_serial(array $autonumber, $type, $branch_id = 0) {
        $settingPlugin = self::getAutoNumberTypePlugin($type);
        $record = Settings::getValue($settingPlugin, "{$type}-{$branch_id}", null, false);
        if(empty($record)) return false;
		$decoded = json_decode($record, true);
		$added = false;
		foreach ($decoded["prev_prefixes"] as $key => $value) {
			if($value["prefix"] === $autonumber[0]){
				$decoded["prev_prefixes"][$key]["last_number"] = $autonumber[1];
				$decoded["prev_prefixes"][$key]["last_used"] = time();
				$added = TRUE;
				break;
			}
		}
		if(!$added){
			$decoded["prev_prefixes"][] = ["prefix"=>$autonumber[0], "last_number"=>$autonumber[1], "last_used"=>time()];
		}
		Settings::setValue($settingPlugin, "{$type}-{$branch_id}", json_encode($decoded));
		return true;
	}

	/**
	 * Get an array of prefix and number
	 * @param int $type Rule Type
	 * @param int $branch_id Branch id
	 * @param array $forced_rule Custom rule to test
	 * @return array|false array of prefix and number
	 */
	public static function _get_auto_serial($type, $branch_id, $forced_rule = null) {
        $settingPlugin = self::getAutoNumberTypePlugin($type);
        $record = Settings::getValue($settingPlugin, "{$type}-{$branch_id}", null, false);
		if(empty($record)) {
			if(empty($forced_rule)) return false;
			else $decoded = ["prev_prefixes"=>[]];
		} else $decoded = json_decode($record, true);

		if(!empty($forced_rule)) $decoded = array_merge ($decoded, $forced_rule);
		$prefix = self::eval_prefix($decoded["prefix_format"]);
		/** @var $last_number last number generated in that prefix */
		$last_number = false;
		/** @var $last_used_number last number generated in all prefixes */
		$last_used_number = ["prefix"=>"", "last_number"=>"", "last_used"=>0];
		foreach ($decoded["prev_prefixes"] as $key => $value) {
			if($value["prefix"] === $prefix){
				$last_number = $value["last_number"];
			}
			$last_used_number = $value["last_used"] > $last_used_number["last_used"] ? $value : $last_used_number;
		}
		if($last_number === false){
			switch ($decoded["prefix_serial_option"]) {
				case self::ContinousOverallPrefixes:
					return array($prefix, $last_used_number["last_number"]===""?self::_generate_first($decoded["number_pattern"]):self::_increment($decoded["number_pattern"],$last_used_number["last_number"]));
				case self::ResetWhenPrefixChanges:
				case self::SeparatedForPrefixes:
					return array($prefix, self::_generate_first($decoded["number_pattern"]));
			}
		} else {
			switch ($decoded["prefix_serial_option"]) {
				case self::ContinousOverallPrefixes:
					return array($prefix, self::_increment($decoded["number_pattern"],$last_used_number["last_number"]));
				case self::ResetWhenPrefixChanges:
					return array($prefix, $last_used_number["prefix"]!==$prefix?self::_generate_first($decoded["number_pattern"]):self::_increment($decoded["number_pattern"],$last_used_number["last_number"]));
				case self::SeparatedForPrefixes:
					return array($prefix, self::_increment($decoded["number_pattern"],$last_number));
			}
			return array($prefix, self::_increment($decoded["number_pattern"],$last_number));
		}
	}

	/**
	 * Evaluate Prefix
	 * @param string $format
	 * @param int $branch_id
	 * @return string
	 */
	public static function eval_prefix($format) {
		//evaluate prefix
		$prefix_format = $format;
		$prefix_placeholders = self::_get_placeholders();
		$prefix_rep = str_replace($prefix_placeholders[0], $prefix_placeholders[1], $prefix_format);
		preg_match_all("/\%([dDjnNwWzmMyY])/", $prefix_rep, $date_placeholders, PREG_PATTERN_ORDER);
		if(!empty($date_placeholders)) $prefix = str_replace($date_placeholders[0], explode("|", date(implode("|", $date_placeholders[1]))), $prefix_rep);
		else $prefix = $prefix_rep;
		return $prefix;
	}

	/**
	 * Increment a number block.
	 *
	 * Handles splitting the pattern and match it to the number
	 * @param string $pattern
	 * @param string $last_number
	 * @return string the number
	 */
	private static function _increment($pattern, $last_number){
		//split pattern
		preg_match_all('/[0-9]*[a-z]/i',$pattern,$pattern_parts);
		$pattern_parts = $pattern_parts[0];
		//split last number by the pattern except for last pattern to handle overflow
		$last_number_parts = [];
		for($i=0; $i<count($pattern_parts)-1; $i++) {
			$count = intval(substr($pattern_parts[$i], 0, -1)) ?: 1;
			$type = substr($pattern_parts[$i], -1);
			$last_number_parts[]= [$count, $type, substr($last_number, 0, $count)];
			$last_number = substr($last_number, $count);
		}
		//handle last pattern
		$last_pattern = end($pattern_parts);
		$last_pcount = intval(substr($last_pattern, 0, -1)) ?: 1;
		$last_ptype = substr($last_pattern, -1);
		$last_number_parts[]= [$last_pcount, $last_ptype, $last_number];
		//clone in case of overflow
		$last_number_parts_clone = $last_number_parts;
		$parts_count = count($last_number_parts);
		$overflow = false;
		for($i=$parts_count-1; $i>=0; $i--){
			list($last_number_parts[$i][2], $overflow) = self::_increment_native_block($last_number_parts[$i]);
			if(!$overflow) break;
		}
		if($overflow){
			list($last_number_parts_clone[$parts_count-1][2], $overflow) = self::_increment_native_block($last_number_parts_clone[$parts_count-1], true);
			$last_number_parts = $last_number_parts_clone;
		}
		return implode("", array_map(function($block){return $block[2];}, $last_number_parts));
	}

	/**
	 * Increment a number block.
	 *
	 * This function handles detecting the overflow in the block and optionally remove the overflow
	 * @param array $block [block_supposed_length, block_type, actual_number]
	 * @param boolean $ignore_overflow if set to <b>true</b> the overflow will be kept in the result
	 * @return array [<b>string</b> result, <b>boolean</b> overflow]
	 */
	private static function _increment_native_block(array $block, $ignore_overflow=false){
		if(strlen($block[2])>$block[0]||$ignore_overflow){
			return [self::_increment_native_partial($block), false];
		} else {
			$temp = self::_increment_native_partial($block);
			$overflow = strlen($temp)>$block[0];
			if($overflow) $temp = substr ($temp, 1);
			return [$temp, $overflow];
		}
	}

	/**
	 * Increment a number block.
	 *
	 * This is the function that handles the adding and formatting the result
	 * @param array $block [block_supposed_length, block_type, actual_number]
	 * @return string the result
	 */
	private static function _increment_native_partial(array $block){
		switch ($block[1]) {
			case "x":
			case "X":
				$temp = hexdec($block[2]);
				return sprintf("%0{$block[0]}{$block[1]}", ++$temp);
			case "d":
				//return str_pad($block[2], $block[0], "0", STR_PAD_LEFT);
				$temp = intval($block[2]);
				return sprintf("%0{$block[0]}d", ++$temp);
			case "c":
				$min = "a";
				$max = "z";
                if (empty($block[2])){
                    return $min;
                }
				break;
			case "C":
				$min = "A";
				$max = "Z";
                if (empty($block[2])){
                    return $min;
                }
				break;
		}
		$number = str_split($block[2]);
		$carry = 1;
		for ($i = count($number)-1; $i >=0; $i--) {
			$temp = ord($number[$i])+$carry;
			if($temp>ord($max)) $number[$i]=$min;
			else {
				$carry = 0;
                $number[$i]=chr($temp);
				break;
			}
		}
		if($carry) array_unshift ($number,$min);
		return implode("", $number);
	}

	/**
	 * Generate first item
	 * @param string $pattern pattern
	 * @return string the result
	 */
	private static function _generate_first($pattern){
		$number = "";
		preg_match_all('/[0-9]*[cCxXd]/',$pattern,$pattern_parts);
		$pattern_parts = $pattern_parts[0];
		$pattern_count = count($pattern_parts);
		foreach ($pattern_parts as $key => $value) {
			$count = intval(substr($value, 0, -1)) ?: 1;
			$type = substr($value, -1);
			switch ($type) {
				case "c":
					$number .= str_repeat('a', $count);
					break;
				case "C":
					$number .= str_repeat('A', $count);
					break;
				case "x":
				case "X":
				case "d":
					if($key==($pattern_count-1)) $number .= sprintf("%0{$count}d", 1);
					else $number .= str_repeat('0', $count);
					break;
			}
		}
		return $number;
	}

	/**
	 * Get placeholders for replace
	 * @return array [<b>array</b> keys to replace, <b>array</b> values to replace with]
	 */
	private static function _get_placeholders(){
	    $currentBranch = Branch::getCurrentBranch();
		return $currentBranch ? [
		    ["%branch_id","%branch_name","%branch_code"],
            [$currentBranch['id'],$currentBranch['name'],$currentBranch['code']]
        ] : [["%branch_id","%branch_name","%branch_code"],['','','']];
	}

    /**
     * Set/Create a rule
     * @param int $type
     * @param int $branch_id
     * @param string $number_pattern
     * @param string $prefix_format default <b>empty string</b>
     * @param int $prefix_serial_option default <b>ContinuousOverallPrefixes</b>
     * @param bool $require_unique
     * @return true|array true if succeed, array of errors on failure
     */
	public static function set_rule($type, $branch_id = 0, $number_pattern = "", $prefix_format = '', $prefix_serial_option=self::ContinousOverallPrefixes, $require_unique = false){
        $settingPlugin = self::getAutoNumberTypePlugin($type);
        $errors = [];
		if(!is_numeric($prefix_serial_option)||$prefix_serial_option<0 || $prefix_serial_option>2) $errors[] = __t("Invalid Sequence Option", true);
		if(0== preg_match('/^([0-9]*[cCxXd])+$/', $number_pattern)) $errors[] = __t("Invalid Number Pattern", true);
		if(!empty($errors)) return $errors;
		$record = settings::getValue($settingPlugin, "{$type}-{$branch_id}", null, false);
		if(empty($record)) {
			settings::setValue($settingPlugin, "{$type}-{$branch_id}", json_encode([
				"prefix_format"=>$prefix_format,
				"number_pattern"=>$number_pattern,
				"prefix_serial_option"=>$prefix_serial_option,
				"prev_prefixes"=>[],
                "require_unique" => $require_unique
			]));
		}else{
			$decoded = json_decode($record, true);
			$decoded["prefix_format"] = $prefix_format;
			$decoded["number_pattern"] = $number_pattern;
			$decoded["prefix_serial_option"] = $prefix_serial_option;
            $decoded["require_unique"] = $require_unique;
			Settings::setValue($settingPlugin, "{$type}-{$branch_id}", json_encode($decoded));
		}
		return true;
	}

	/**
	 * Generate auto serial from rule
	 * @param array $forced_rule Rule to use for generation
	 * @return array AutoNumber
	 */
	public static function get_auto_serial_using_rule($forced_rule) {
		return self::_get_auto_serial($forced_rule['type'], $forced_rule['branch_id']?:0, $forced_rule);
	}

	/**
	 * Get next number for all previous prefixes.
	 *
	 * Use only if the rule is using separated option
	 * @param int $type Rule Type
	 * @param int $branch_id Branch id
	 * @return type
	 */
	public static function get_next_number_for_all_prefixes($type, $branch_id=0) {
        $settingPlugin = self::getAutoNumberTypePlugin($type);
		$record = Settings::getValue($settingPlugin, "{$type}-{$branch_id}", null, false);
		$decoded = json_decode($record, true);
		$result = [];
		foreach ($decoded["prev_prefixes"] as $key => $value) {
			$result[$value["prefix"]] = self::_increment($decoded["number_pattern"],$value["last_number"]);
		}
		return $result;
	}

	public static function get_rule($type, $branch_id=0){
        $settingPlugin = self::getAutoNumberTypePlugin($type);
        return json_decode(Settings::getValue($settingPlugin, "{$type}-{$branch_id}", null, false), true);
    }

	public static function set_validate($type) {
        $Model = GetObjectOrLoadModel(self::$fields[$type]['model']);
        if (!isset($Model->validate[self::$fields[$type]['field']]))
            $Model->validate[self::$fields[$type]['field']] = [];
        $Model->validate[self::$fields[$type]['field']][] = ['rule' => ['checkUniqueAutoNumber', $type], 'allowEmpty' => true, 'message' => sprintf(__t('%s already exists', true),self::$fields[$type]['field'])];
    }

    static function getAutoNumberTypePlugin($type)
    {
        if(in_array($type, self::$globalAutoNumberTypes))
        {
            return PluginUtil::BranchesPlugin;
        }else{
            return PluginUtil::AutoNumberPlugin;
        }
    }

    protected static function getEntityStructureGetter()
    {
        return resolve(AppEntityStructureGetter::class);
    }

}
