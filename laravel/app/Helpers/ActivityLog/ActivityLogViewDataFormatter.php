<?php

namespace App\Helpers\ActivityLog;

use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityFieldUtil;
use App\Facades\Formatter;

class ActivityLogViewDataFormatter {
    public static function getFormattedData($activityLog){
        $formattedData = self::getFormattedDataUsingHelper($activityLog);
        /** as it was defined in view */
        $resultData = [];
        foreach ($formattedData as $entity) {
            $label = $entity['key']??'';
            $applyUTCTimeZone = isset($entity['utc_timezone']) ? $entity['utc_timezone']: false;
            $newValue = $entity['newVal'] ?? '';
            $oldValue = $entity['oldVal'] ?? '';
            if (is_array($newValue) || is_array($oldValue)) {
                $newValue = is_array($newValue) ? implode(',', $newValue): $newValue;
                $oldValue = is_array($oldValue) ? implode(',', $oldValue): $oldValue;
            } else {
                $result = json_decode($newValue, true);
                if (is_array($result)) {
                    $result = array_filter($result);
                    $newValue = getStringFromArrayRecursively($result);
                }
                $result = json_decode($oldValue, true);
                if (is_array($result)) {
                    $result = array_filter($result);
                    $oldValue = getStringFromArrayRecursively($result);
                }
            }
            if (trim($newValue) != '' && isset($entity['newUrl']) && !empty($entity['newUrl'])) {
                $newValue = '<a href="'. $entity['newUrl'] . '" class="text-decoration-underline font-weight-bold" target="_blank">'. $newValue . ' </a>';
            }
            if (trim($oldValue) != '' && isset($entity['oldUrl']) && !empty($entity['oldUrl'])) {
                $oldValue = '<a href="'. $entity['oldUrl'] . '" class="text-decoration-underline font-weight-bold" target="_blank">'. $oldValue . ' </a>';
            }
            $fieldType = $entity['field_type'] ?? null;
            if (
                ($fieldType == EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER ||
                $fieldType == EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC) &&
                $applyUTCTimeZone
            ) {
                if (trim($newValue) != ''){
                    $dbFormat = Formatter::formatForDB($newValue,  EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER );
                    $newValue = $dbFormat ? convertFromUtc($dbFormat, null, false) : $newValue;
                }
                if (trim($oldValue) != ''){
                    $dbFormat = Formatter::formatForDB($oldValue,  EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER );
                    $oldValue = $dbFormat ? convertFromUtc($dbFormat, null, false) : $oldValue;
                }
            }
            $resultData[] = ['key'=> $entity['key'] ?? null, 'field_type' => $fieldType, 'label' => $label, 'newValue' => $newValue, 'oldValue' => $oldValue];
        }

        return $resultData;
    }

    private static function getFormattedDataUsingHelper($activityLog){
        return match ($activityLog->entity_key) {
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => ManufacturingOrderOperationActivityLogViewDataFormatter::format($activityLog),
            EntityKeyTypesUtil::PURCHASE_ORDER => PurchaseOrderActivityLogViewDataFormatter::format($activityLog),
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => LeaveApplicationActivityLogViewDataFormatter::format($activityLog),
            EntityKeyTypesUtil::REQUEST_ENTITY_KEY => LeaveApplicationActivityLogViewDataFormatter::format($activityLog),
            default => AbstractActivityLogViewFormatter::format($activityLog),
        };
    }
}
