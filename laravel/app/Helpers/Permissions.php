<?php
/**
 * Created by PhpStorm.
 * User: bassel
 * Date: 21/03/19
 * Time: 4:33 PM
 */

namespace App\Helpers;

use App\Repositories\PermissionRepository;
use App\Repositories\StaffRepository;
use Izam\Daftra\Common\Utils\PermissionEmployeeTypeUtil;

/**
 * Class Permissions used to check staff permissions
 * @package App\Helpers
 * <AUTHOR> Azzam <<EMAIL>>
 */
class Permissions
{
    /**
     * @var StaffRepository
     */
    private $staffRepo;
    /**
     * @var PermissionRepository
     */
    private $PermissionRepository;

    private static array $staffCache;

    function __construct(StaffRepository $staffRepository, PermissionRepository $permissionRepository)
    {
        $this->staffRepo = $staffRepository;
        $this->PermissionRepository = $permissionRepository;
    }

    /**
     * @param int|array $tocheck single permission or array of permissions to check
     * @param int $staff_id optional parameter if it was sent then the function check the permission for this user, else it checks for the current user
     * @param bool $isAnd
     * @return bool whether the staff has the permission or not if array of permissions is sent then the function checks if the user has any of them
     */
    function checkPermission($tocheck, $staff_id = null, $isAnd = false)
    {
        $staff_id = $staff_id ?? getAuthOwner('staff_id');
        return self::isOwnerOrAdmin($staff_id) || self::checkSpecificPermissionForStaff($tocheck, $staff_id, $isAnd);
    }

    /**
     * check specific permission for staff not an admin or master
     * @param int|array $toCheck single permission or array of permissions to check
     * @param int $staff_id optional parameter if it was sent then the function check the permission for this user, else it checks for the current user
     * @param bool $isAnd
     * @return bool whether the staff has the permission or not if array of permissions is sent then the function checks if the user has any of them
     */
    function checkSpecificPermissionForStaff($toCheck, $staff_id, $isAnd = false)
    {
        $plist = $this->getStaffPermissions($staff_id);

        if (empty($toCheck)) {
            return true;
        }

        $toCheckArray = arrayWrap($toCheck);

        if ($isAnd) {
            foreach ($toCheckArray as $permission) {
                if (!in_array($permission, $plist)) {
                    return false;
                }
            }
            return true;

        } else {
            foreach ($toCheckArray as $permission) {
                if (in_array($permission, $plist)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param int $staff_id optional parameter if it was sent then the function check the permission for this user, else it checks for the current user
     * @return bool whether the staff has the permission or not if array of permissions is sent then the function checks if the user has any of them
     */
    function isOwnerOrAdmin($staff_id = null)
    {
        if (!empty($staff_id)) { //Getting $RoleData and $is_super_admin variables
            if ($staff_id == -1 or $staff_id == -2) {
                $is_super_admin = true;
            } else {
                if (!isset(self::$staffCache[$staff_id]['staff'])) {
                    self::$staffCache[$staff_id]['staff'] = $this->staffRepo->find($staff_id);
                }
                $staff_member = self::$staffCache[$staff_id]['staff'];
                if (!$staff_member) {
                    return false;
                }
                $role = $staff_member->role;
                $is_super_admin = ($role->is_super_admin  ?? false && $staff_member->type == "user");
            }
        } else {
            $is_super_admin = getAuthOwner('is_super_admin');
        }
        if ($is_super_admin == 1 || (defined('TRUE_REST') && TRUE_REST)) {
            return true;
        }
        return false;
    }

    /**
     * return staff permissions
     * @param int $staff_id optional parameter if it was sent then the function check the permission for this user, else it checks for the current user
     * @return array
     */
    function getStaffPermissions($staff_id): array
    {
        if (!isset(self::$staffCache[$staff_id]['staff'])) {
            self::$staffCache[$staff_id]['staff'] = $this->staffRepo->find($staff_id);
        }
        $staff_member = self::$staffCache[$staff_id]['staff'];
        if (!$staff_member || $staff_member->role_id == "-1") {
            return [];
        }
        $role = $staff_member->role;
        $plist = [];
        $ids = [];
        if ($staff_member->type == "employee" && empty($role)) {
            $plist = PermissionEmployeeTypeUtil::ALLOWED_PERMISSIONS;
        } else {
            if (!$role) {
                return [];
            }
            foreach ($role->roles_permissions as $permission) {
                $ids[] = $permission->permission_id;
            }
            if (!isset(self::$staffCache[$staff_id]['permissions'])) {
                self::$staffCache[$staff_id]['permissions'] = $this->PermissionRepository->find($ids);
            }
            $permissions = self::$staffCache[$staff_id]['permissions'];
            foreach ($permissions as $k => $permission) {
                if ($permission->deprecated) {
                    $plist[] = $permission->replaced_by;
                }
                $plist[] = $permission->id;
            }
        }

        if ($staff_member->type == "employee") {
            $plist = array_filter($plist, function ($item) {
                return in_array($item, PermissionEmployeeTypeUtil::ALLOWED_PERMISSIONS);
            });
        }

        return $plist;
    }

}

