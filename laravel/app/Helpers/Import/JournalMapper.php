<?php

namespace App\Helpers\Import;

use Illuminate\Support\Arr;

class JournalMapper
{
    private static $map = [];

    public static function map($journals, $extraData)
    {
        $row_index = 1;
        if (!isset($extraData["import_first_row"])) {
            $row_index++;
        }
        $ret = [];
        foreach ($journals as $journal) {
            self::$map[$journal["number"]][] = $journal;
        }

        foreach (self::$map as $no => $journal) {
            $ret[$no]["Journal"] =
                Arr::only($journal[0], [
                    "date",
                    "currency_code",
                    "number",
                    "hidden_number",
                    "description"
                ]);

            if (!empty($extraData['auto_generate_nums'])) {
                unset($ret[$no]["Journal"]["number"]);
                unset($ret[$no]["Journal"]["hidden_number"]);
            }


            if (empty($ret[$no]["Journal"]["currency_code"])) {
                $ret[$no]["Journal"]["currency_code"] = get_default_currency();
            }

            $ret[$no]["Journal"]["draft"] =  ($extraData['status'] == "draft")  ? 1 : 0;
            $ret[$no]["Journal"]["number_row"] = $row_index;

            foreach ($journal as $journalTransaction) {
                $item =  [
                    "journal_account_id" => $journalTransaction["journal_account_id"],
                    "description" =>  $journalTransaction["transaction_description"] ?? "",
                    "cost_center" => $journalTransaction['cost_center'] ?? null,
                    "tax_id" => $journalTransaction['tax_id'] ?? null,
                    "currency_debit" => $journalTransaction['currency_debit'] ?? null,
                    "currency_credit" => $journalTransaction['currency_credit'] ?? null
                ];

                $ret[$no]["JournalTransaction"][] = $item;
                $row_index++;
            }

            foreach ($journal as $journalTransaction) {
                $item =  [
                    "tags" => explode(',', $journalTransaction["tags"] ?? ""),
                ];

                $ret[$no]["ItemsTag"][] = $item;
            }
        }
        return array_values($ret);
    }
}
