<?php
/**
 * Created by PhpStorm.
 * User: bassel
 * Date: 21/03/19
 * Time: 4:33 PM
 */

namespace App\Helpers;

use App\Facades\Settings;
use App\Utils\PluginUtil;
use App\Repositories\SiteRepository;
use Illuminate\Support\Facades\Hash;
use App\Repositories\StaffRepository;
use Illuminate\Support\Facades\Storage;
use App\Facades\Permissions;
use App\Utils\PermissionUtil;
use Izam\Daftra\Staff\Util\StaffCitizenshipStatus;
use Izam\Daftra\Common\Services\AvatarURLGenerator;


class Staff
{
    static array $cachedStaff;
    private $staffRepository;
    public function __construct(private StaffRepository $staffRepo)
    {
        $this->staffRepository = $staffRepo;
    }

    public function getStaffDetails($id)
    {
        if (isset(self::$cachedStaff[$id])) {
            return self::$cachedStaff[$id];
        }
        $staffData = [];
        if (0 == $id) {
            $site = getCurrentSite();
            $staffData['id'] = $id;
            $staffData['name'] = $site['first_name']. ' '.$site['last_name'];
            $staffData['code'] = '';
            $staffData['img'] = $site['site_logo_full_path'];
            if (empty($site['site_logo'])) {
                $staffData['img'] = false;
                $staffData['image_alt'] =ucfirst(mb_substr($site['first_name'], 0, 1)) .' '. ucfirst(mb_substr($site['last_name'], 0, 1));
            }
        } else {
            $staff = $this->staffRepo->find($id);
            if (!$staff) {
                return $staffData;
            }
            $staffData['id'] = $id;
            $staffData['name'] = $staff->full_name;
            $staffData['code'] = $staff->country_code;
            $staffData['img'] = false;
            $staffData['image_alt'] =$staff->initialsName();
            //get image from s3

            if (!empty($staff->getPathAttribute())) {
                $staffData['img'] = Storage::temporaryUrl($staff->getPathAttribute(), \Carbon\Carbon::now()->addMinutes(env('AWS_LIFE_TIME', 1)));
            }





        }



        self::$cachedStaff[$id] = $staffData;
        return $staffData;
    }

    public function getStaffOptionFormated(\App\Models\Staff $staff){
        $code = !empty($staff->code) ? $staff->code : $staff->id;
        $text = "#{$code} {$staff->full_name}";
        if ($staff->designation->first()) {
            $text .= " ({$staff->designation->first()->name})";
        }
        return [
            'value'        => $staff->id,
            'text'      => $text,
            'data' => [
                'data-img' => AvatarURLGenerator::generate($staff->full_name, $staff->id, 30, $staff->photo),
                'selected' => true
            ]
        ];
    }

    public static function getStaffOfficialIdName($countryCode, $citizenshipStatus){
        if(empty($countryCode) || empty($citizenshipStatus)){
            return __t('Official ID Number');
        }
        $isCitizen = $citizenshipStatus == StaffCitizenshipStatus::CITIZEN;
        switch (strtoupper($countryCode)) {
            case 'EG':
                return $isCitizen ?  __t('National ID Number') : __t('Residency Number') ;
            case 'SA':
            case 'AE':
                return  __t('Haweya ID');
            case 'JO':
                return $isCitizen ? __t('National Number')  : __t('Residency Number/Security Number');
            default:
                return  __t('Official ID Number');
        }
    }


}

