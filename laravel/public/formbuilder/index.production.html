<script src="/v2/formbuilder/runtime-es2015.d41594834650671b265e.js" type="module"></script><script src="/v2/formbuilder/runtime-es5.d41594834650671b265e.js" nomodule defer></script><script src="/v2/formbuilder/polyfills-es5.82ce86300cad4d2e0def.js" nomodule defer></script><script src="/v2/formbuilder/polyfills-es2015.7d48e6b26d37d7ca52a8.js" type="module"></script><script src="/v2/formbuilder/scripts.c8a5935fa6b6b8684aac.js" defer></script><script src="/v2/formbuilder/main-es2015.60139c64d4442986b675.js" type="module"></script><script src="/v2/formbuilder/main-es5.60139c64d4442986b675.js" nomodule defer></script><link rel="stylesheet" href="/v2/formbuilder/styles.d40b407813afdced02a8.css"><script>
  function getParameterByName(name, url = window.location.href) {
    name = name.replace(/[\[\]]/g, '\\$&');
    var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
      results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, ' '));
  }
  window.redirectUrl = getParameterByName('redirect');

</script>
<base href="/v2/owner/local_entities/builder/"><app-routes></app-routes>

