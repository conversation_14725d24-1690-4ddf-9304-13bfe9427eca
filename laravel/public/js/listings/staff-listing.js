// staffSearchUrl is echoed inside a script of many pages using laravel echo statement {{ }}
// Blade's {{ }} echo statements are automatically sent through PHP's htmlspecialchars function to prevent XSS attacks.
// that is why we need the following statment to separate url parameters correctly
// bad solution ? yes .. just a workaround
staffSearchUrl = staffSearchUrl.replace("&amp;", "&")
if ($('html').is(':lang(ara)')) {
    $('#staffSelect').each(function () {
        $(this).select2(
            {
                theme: 'bootstrap4',
                width: '100%',
                language: "ar",
                placeholder: function () { $(this).data('placeholder'); },
                allowClear: true,
                // minimumResultsForSearch: -1,
                minimumInputLength: 1,
                closeOnSelect: false,
                templateResult: setEmpImg,
                templateSelection: getEmpCurrentImg($(this)),
                ajax: {
                    url: staffSearchUrl,
                    dataType: 'json',
                    delay: 400 // Additional AJAX parameters go here; see the end of this chapter for the full code of this example
                } // matcher: matchCustom
            });
    })
} else {
    $('#staffSelect').each(function () {
        $(this).select2(
            {
                theme: 'bootstrap4',
                width: '100%',
                placeholder: function () { $(this).data('placeholder'); },
                allowClear: true,
                closeOnSelect: false,
                // minimumResultsForSearch: -1,
                minimumInputLength: 1,
                templateResult: setEmpImg,
                templateSelection: getEmpCurrentImg($(this)),
                ajax: {
                    url: staffSearchUrl,
                    dataType: 'json',
                    delay: 400 // Additional AJAX parameters go here; see the end of this chapter for the full code of this example
                } // matcher: matchCustom
            });
    })
}

function setEmpImg(item) {
    if (!item.id) { return item.text; }
    var avatarQueryParams = new URLSearchParams({
        n: (item.text.trim()).indexOf('#') == 0 ? (item.text.trim()).split(' ')[1] : item.text.trim(),
        id: item.id,
        s: 78,
    });
    var img = item.img != '' && item.img != null ? item.img : (typeof base64 !== 'undefined' ? '/avatar.php?q=' + base64.encode(avatarQueryParams.toString()) : 'https://cdn.daftra.com/assets/imgs/account-def-md.png');
    var $item = $('<span><img src="' + img + '" class="avatar-xs mr-2" />' + item.text + '</span>');
    return $item;
}

function getEmpCurrentImg($elm) {
    var singularSelectClass = 'mt-n1';
    if ($elm.attr('multiple')) {
        singularSelectClass = '';
    }
    return function setEmpCurrentImg(item, data) {
        if (!item.id) { return item.text; }
        var avatarQueryParams = new URLSearchParams({
            n: (item.text.trim()).indexOf('#') == 0 ? (item.text.trim()).split(' ')[1] : item.text.trim(),
            id: item.id,
            s: 78,
        });
        var img = item.img != '' && item.img != null && !item.img.includes('account-def-md.png') ? item.img : (typeof base64 !== 'undefined' ? '/avatar.php?q=' + base64.encode(avatarQueryParams.toString()) : 'https://cdn.daftra.com/assets/imgs/account-def-md.png');
        if (item.element?.dataset?.img) {
            img = item.element.dataset.img;
        }
        if (typeof item.img != 'undefined' && item.img != '' && item.img != null) {
            img = item.img;
        } else if (typeof item.img == 'undefined' && $(item.element).data('img') && item.img != null) {
            if (!$(item.element).data('img').includes('account-def-md.png')) {
                img = $(item.element).data('img');
            }
        }
        var $item = $('<span><img src="' + img + '" class="avatar-xs mr-2 " ' + singularSelectClass + ' />' + item.text + '</span>');
        return $item;
    }
}