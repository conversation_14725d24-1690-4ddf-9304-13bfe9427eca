(this["webpackJsonpchart-of-accounts"]=this["webpackJsonpchart-of-accounts"]||[]).push([[0],{102:function(t,e,a){},126:function(t,e){},152:function(t,e,a){},153:function(t,e,a){var n={"./ar.json":154,"./en.json":155};function c(t){var e=o(t);return a(e)}function o(t){if(!a.o(n,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return n[t]}c.keys=function(){return Object.keys(n)},c.resolve=o,t.exports=c,c.id=153},154:function(t){t.exports=JSON.parse('{"Chart Of Accounts":"\u062f\u0644\u064a\u0644 \u0627\u0644\u062d\u0633\u0627\u0628\u0627\u062a","Chart of Accounts":"\u062f\u0644\u064a\u0644 \u0627\u0644\u062d\u0633\u0627\u0628\u0627\u062a","New Journal":"\u0642\u064a\u062f \u062c\u062f\u064a\u062f","Edit":"\u062a\u0639\u062f\u064a\u0644","Add Account":"\u0623\u0636\u0641 \u062d\u0633\u0627\u0628","Edit Account":"\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u062d\u0633\u0627\u0628","Delete Account":"\u062d\u0630\u0641 \u0627\u0644\u062d\u0633\u0627\u0628","Search":"\u0625\u0628\u062d\u062b","Delete":"\u062d\u0630\u0641","Credit":"\u062f\u0627\u0626\u0646","Debit":"\u0645\u062f\u064a\u0646","Assign Cost Centers":"\u062a\u0639\u064a\u064a\u0646 \u0645\u0631\u0627\u0643\u0632 \u0627\u0644\u062a\u0643\u0644\u0641\u0629","Loading...":"\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0645\u064a\u0644","All Branches":"\u0643\u0644 \u0627\u0644\u0641\u0631\u0648\u0639","Journals Branch":"\u0641\u0631\u0639 \u0627\u0644\u0642\u064a\u0648\u062f","Details":"\u0627\u0644\u062a\u0641\u0627\u0635\u064a\u0644","View":"\u0639\u0631\u0636","This Account is Empty":"\u0647\u0630\u0627 \u0627\u0644\u062d\u0633\u0627\u0628 \u0641\u0627\u0631\u063a","Hide":"\u0627\u062e\u0641\u0627\u0621","Unhide":"\u0627\u0638\u0647\u0627\u0631","Show Hidden":"\u0625\u0638\u0647\u0627\u0631 \u0627\u0644\u062d\u0633\u0628\u0627\u062a \u0627\u0644\u0645\u062e\u0641\u064a\u0629"}')},155:function(t){t.exports=JSON.parse('{"Chart Of Accounts":"Chart of Accounts","Chart of Accounts":"Chart of Accounts","New Journal":"New Journal","Edit":"Edit","Add Account":"Add Account","Edit Account":"Edit Account","Delete Account":"Delete Account","Search":"Search","Delete":"Delete","Credit":"Credit","Debit":"Debit","Assign Cost Centers":"Assign Cost Centers","Loading...":"Loading...","All Branches":"All Branches","Journals Branch":"Journals Branch","View":"View","Details":"Details","This Account is Empty":"This Account is Empty","Hide":"Hide","Unhide":"Unhide","Show Hidden":"Show Hidden"}')},31:function(t,e,a){"use strict";var n=a(88),c=a.n(n).a.create();c.interceptors.request.use((function(t){return t})),e.a=c},5:function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var n={BASE_URL:"/",LINK_PREFIX:"/v2/owner/chart-of-accounts",child_limit:100}},64:function(t,e,a){"use strict";a.r(e),a.d(e,"store",(function(){return ht}));var n=a(0),c=a.n(n),o=a(10),r=a.n(o),i=(a(76),a(17)),s=a(19),d=a(20),l=a(24),u=a(23),p=a(22),h=(a(102),a(2)),m=a(93),f=a(190),g=a(63),E=a(189),b=a(15),_=a(40),A=a(185),O=a(29),C=a(7),v=a(31),j=a(14),N=a(89),y=a(5),w=a(6),S=a.n(w),T=Object(b.a)((function(t){return{iconContainer:{"& .close":{opacity:.3}},group:{marginLeft:7,paddingLeft:18,borderLeft:"1px dashed ".concat(Object(_.b)(t.palette.text.primary,.4))}}}))((function(t){return c.a.createElement(E.a,Object.assign({},t,{className:"".concat("1"==t.isHidden?"hidden-account":""),title:"#".concat(t.code," ").concat(t.label)}))}));Object(A.a)({root:{height:264,flexGrow:1,maxWidth:400}});function D(t){return c.a.createElement("i",{className:"fas fa-folder-open"})}function R(t){return c.a.createElement("i",{className:"far fa-file"})}function k(t){return c.a.createElement("i",{className:"fas fa-folder"})}function I(t){return c.a.createElement("i",{className:"fas fa-spinner fa-spin"})}var U=function(t){Object(u.a)(a,t);var e=Object(p.a)(a);function a(t){var n;return Object(s.a)(this,a),(n=e.call(this,t)).rootId=-1,n.handleNodeClick=function(t){window.innerWidth<=991&&t.target.classList.contains("MuiTreeItem-label")&&document.querySelector(".chart-of-accounts-col-9-filters-row").scrollIntoView()},n.renderTree=function(t){var e,a=t.code+"-"+t.id;switch(t.account_type){case"cat":a=t.id,e=n.props.expandedAccounts[t.id]?c.a.createElement(D,null):c.a.createElement(k,null);break;case"account":e=c.a.createElement(R,null);break;case"temp":e=c.a.createElement(I,null)}if(t.has_children&&0==t.children.length&&(t.children=[{id:"temp",name:"",account_type:"temp",code:t.code+"-child"}]),"1"!=t.isHidden||0!=n.props.showHiddenAccounts)return c.a.createElement(T,{label:t.name,nodeId:a,icon:e,key:t.code+"-"+t.id,code:t.code,isHidden:t.isHidden,onClick:n.handleNodeClick},Array.isArray(t.children)?t.children.map((function(t){return n.renderTree(t)})):null)},n.props.getCatAccounts(-1),n}return Object(d.a)(a,[{key:"componentDidMount",value:function(){}},{key:"getAccountTreeItemData",value:function(t,e){return{id:t.id,code:t.code,name:t.name,isHidden:t.is_hidden,children:[],account_type:e,has_children:!!t.has_children}}},{key:"getTreeItemChildren",value:function(t){var e=this,a=[];return this.props.parentCats[t.id]&&this.props.parentCats[t.id].forEach((function(n){var c=e.getAccountTreeItemData(n,"cat");n.has_children&&(c.children=e.getTreeItemChildren(n)),y.a.child_limit<n.child_count&&c.children.push(e.getAccountTreeItemData({id:"list-"+n.id,name:"View All",code:t.code+"-list",isHidden:n.is_hidden},"list")),a.push(c)})),this.props.parentAccounts[t.id]&&this.props.parentAccounts[t.id].forEach((function(t){a.push(e.getAccountTreeItemData(t,"account"))})),a=a.sort((function(t,e){return t.code<e.code?-1:t.code>e.code?1:0}))}},{key:"treeItemClicked",value:function(t,e){var a="I"===t.target.tagName,n=!1,c=!1;-1===e.indexOf("-")?n=e:c=e.split("-")[1],n&&this.props.cats[n]?(this.props.getCatAccounts(n),a?this.props.expandedAccounts[n]?this.props.setExpandedStatus(n,!1):this.props.setExpandedStatus(n,!0):this.props.history.push("/cats/"+n)):this.props.accounts[c]?this.props.history.push("/accounts/"+c):this.props.history.push("/cats/"+n)}},{key:"loadSearchAccounts",value:function(t,e){v.a.get("".concat(y.a.BASE_URL,"api2/journal_accounts/json_find?q=").concat(t,"&cat_name=1&with_cat=1")).then((function(t){var a={};if(t.data){for(var n in t.data){var c=t.data[n],o={label:c.name,value:c.id,cat:c.cat};a[c.cats]?a[c.cats].push(o):a[c.cats]=[o]}var r=[];for(var i in a)r.push({label:i,options:a[i]});e(r)}else e([])})).catch((function(t){alert("error getting accounts if this error comes again please notify support")}))}},{key:"openAccount",value:function(t){t&&(t.cat?this.props.history.push("/cats/"+t.value):this.props.history.push("/accounts/"+t.value))}},{key:"setAccountsBranch",value:function(t){v.a.get("".concat(y.a.BASE_URL,"api2/branches/set_report_account_branch/").concat(t.value)).then((function(){window.location.href=window.location.href})).catch((function(t){alert("You are not authorized to change branch")}))}},{key:"render",value:function(){var t=this,e=[];try{e=this.props.branches.map((function(t){return{value:t.id,label:t.name}}))}catch(d){e=[]}var a={};this.props.parentCats[this.rootId]&&this.props.parentCats[this.rootId].forEach((function(e){var n=t.getAccountTreeItemData(e,"cat");n.children=t.getTreeItemChildren(e),a[e.code]=n}));var n=[];if(a)for(var o in a)n.push(this.renderTree(a[o]));var r=Object.keys(this.props.expandedAccounts).map((function(e){if(t.props.expandedAccounts[e])return e})),i=window.reactInit.displayAccountBranch?c.a.createElement("div",{className:"chart-of-accounts-col-9-filters-col"},c.a.createElement(g.a,{options:e,placeholder:"Select Branch",onChange:function(e){return t.setAccountsBranch(e)},value:window.reactInit.accountsBranch})):"",s={menu:function(t){t.width;var e=Object(m.a)(t,["width"]);return Object(h.a)(Object(h.a)({},e),{},{width:"auto",maxWidth:"70vw",whiteSpace:"nowrap"})}};return c.a.createElement("div",{className:"chart-of-accounts-col chart-of-accounts-col-3"},c.a.createElement("div",{className:"chart-of-accounts-search-wrapper"},c.a.createElement("div",{className:"chart-of-accounts-col-3-filters-container"},c.a.createElement("div",{className:"chart-of-accounts-col-3-filters-row"},c.a.createElement("div",{className:"chart-of-accounts-col-3-filters-col"},c.a.createElement(N.a,{placeholder:S.a.get("Search"),styles:s,cacheOptions:!0,loadOptions:this.loadSearchAccounts,onChange:function(e){return t.openAccount(e)},isClearable:!0})))),i),c.a.createElement("div",{className:"chart-of-accounts-col-3-body-container"},c.a.createElement(f.a,{expanded:r,selected:-1!=this.props.opendAccount.id?this.props.opendAccount.id.toString():"",onNodeSelect:function(e,a){return t.treeItemClicked(e,a)}},n)))}}]),a}(c.a.Component);var L=Object(O.b)((function(t){return{branches:t.accountsReducer.branches,parentAccounts:t.accountsReducer.parentAccounts,parentCats:t.accountsReducer.parentCats,accounts:t.accountsReducer.accounts,cats:t.accountsReducer.cats,expanded:t.accountsReducer.expanded,expandedAccounts:t.accountsReducer.expandedAccounts,opendAccount:t.accountsReducer.openedAccount,showHiddenAccounts:t.accountsReducer.showHiddenAccounts}}),(function(t){return{getCatAccounts:function(e){t(Object(C.getChildAccounts)(e,1))},setExpandedStatus:function(e,a){t(Object(C.setExpandedStatus)(e,a))}}}))(Object(j.f)(U)),B=a(21);function x(t){var e=window.AppFormatPrice(t,window.reactInit.countryCode,window.reactInit.currency);return"-0.00"!=e&&"-0.000"!=e&&"-0"!=e||(e=e.replace("-","")),e}function P(t){var e,a;return 0==t.type?(e=S.a.get("Credit"),a=t.total_credit-t.total_debit):(e=S.a.get("Debit"),a=t.total_debit-t.total_credit),{amountText:e,amount:a=function(t){return t>-.01&&t<0&&(t=0),window.AppFormatPrice(t,window.reactInit.countryCode,window.reactInit.currency,!0)}(a)}}var H=a(187),G=a(186),J=a(188),F=a(91),M=a.n(F),W=function(t){Object(u.a)(a,t);var e=Object(p.a)(a);function a(t){var n;Object(s.a)(this,a),(n=e.call(this,t)).handleLoad=function(){n.setState({loadingFrame:!1})},n.state={loadingFrame:!0};var c=Object(l.a)(n);return window.onmessage=function(t){try{r.a.findDOMNode(c).querySelector("iframe").style.height=t.data.height}catch(t){}},n}return Object(d.a)(a,[{key:"render",value:function(){var t=this;return c.a.createElement(H.a,{maxWidth:"lg",fullWidth:!0,onClose:function(){return t.props.handleClose()},"aria-labelledby":"simple-dialog-title",open:this.props.open},c.a.createElement(G.a,{id:"simple-dialog-title"},this.props.modalTitle?S.a.get(this.props.modalTitle):"",c.a.createElement(J.a,{"aria-label":"close",style:{position:"absolute",right:0,top:0},onClick:function(){return t.props.handleClose()}},c.a.createElement(M.a,null))),this.state.loadingFrame&&c.a.createElement("div",{style:{textAlign:"center",fontWeight:"bold"}},S.a.get("Loading...")),c.a.createElement("iframe",{onLoad:this.handleLoad,width:"1000",height:"1000",style:{minHeight:"570px"},src:this.props.url,title:""}))}}]),a}(c.a.Component);function q(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"v2/owner/accounting/").concat(t.id,"/account-details/")+(e?1:""),className:"dropdown-item view-action"},S.a.get("View"))}var X=a(139),z=function(t){Object(u.a)(a,t);var e=Object(p.a)(a);function a(t){var n;return Object(s.a)(this,a),(n=e.call(this,t)).scrollSpinner=function(){},n.handlePaginationClick=function(){document.getElementById("chart-of-accounts-child-board").scrollTop=0,n.setState({loadingItems:!0,oldPage:n.props.pagination[n.getParentCatId()].currentPage})},n.state={dialogOpened:!1,iframeUrl:"",modalTitle:"",loadingItems:"",oldPage:""},n.handleClose=n.handleClose.bind(Object(l.a)(n)),n}return Object(d.a)(a,[{key:"openAddAccount",value:function(t,e,a){return t.preventDefault(),this.setState(Object(h.a)(Object(h.a)({},this.state),{},{dialogOpened:!0,iframeUrl:e,modalTitle:a})),!1}},{key:"handleClose",value:function(){this.setState(Object(h.a)(Object(h.a)({},this.state),{},{dialogOpened:!1}))}},{key:"componentDidMount",value:function(){var t=this.props.match.params.cat_id?this.props.match.params.cat_id:-1;this.props.setOpenedAccount(t)}},{key:"getCatListItem",value:function(t){var e=this,a="",n=0;this.props.ignoreCalcCats||(0==t.type?(a=S.a.get("Credit"),n=t.total_credit-t.total_debit):(a=S.a.get("Debit"),n=t.total_debit-t.total_credit));var o=t.deletable?c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/journal_cats/delete/").concat(t.id),onClick:function(a){e.openAddAccount(a,"".concat(y.a.BASE_URL,"owner/journal_cats/delete/").concat(t.id,"?iframe=1&folderid=").concat(t.journal_cat_id),"Delete Account")},className:"delete-action dropdown-item"},S.a.get("Delete")):"",r=t.can_be_hidden?c.a.createElement("a",{href:"".concat(y.a.BASE_URL,"owner/journal_cats/hide/").concat(t.id),className:"hide-action dropdown-item"},S.a.get("Hide")):"",i="1"==t.is_hidden?c.a.createElement("a",{target:"_self",href:"".concat(y.a.BASE_URL,"owner/journal_cats/unhide/").concat(t.id),className:"unhide-action dropdown-item"}," ",S.a.get("Unhide")," "):"";return c.a.createElement("tr",{className:" chart-of-accounts-col-9-body-container-table-row",key:t.code},c.a.createElement("td",{className:" ".concat("1"==t.is_hidden?"hidden-account":""," border-0")},c.a.createElement(B.b,{onClick:function(){return e.props.setExpandedStatus(t.id,!0)},to:"/cats/"+t.id},c.a.createElement("div",{className:"chart-of-accounts-col-9-body-container-table-item"},c.a.createElement("div",{className:"item-container"},c.a.createElement("i",{className:"icon fas fa-folder mr-3"}),c.a.createElement("div",{className:"details"},c.a.createElement("p",{className:"name"},t.name),c.a.createElement("p",{className:"id"},"#",t.code)))))),c.a.createElement("td",{className:"border-0 text-right"},c.a.createElement(B.b,{onClick:function(){return e.props.setExpandedStatus(t.id,!0)},to:"/cats/"+t.id},c.a.createElement("div",{className:"credit-wrap text-right"},c.a.createElement("div",{className:"credit-container"},c.a.createElement("p",{className:"cost"},n?x(n):""),c.a.createElement("p",{className:"type"},a))))),c.a.createElement("td",{className:"border-0 text-right",width:"50"},c.a.createElement("div",{className:"dropdown"},c.a.createElement("a",{className:"btn btn-sm btn-secondary dropdown-toggle",href:"#",role:"button","data-toggle":"dropdown","aria-haspopup":"true","aria-expanded":"false"},c.a.createElement("i",{className:"mdi mdi-dots-horizontal"})),c.a.createElement("div",{className:"dropdown-menu dropdown-menu-right"},c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/journal_cats/edit/").concat(t.id),onClick:function(a){e.openAddAccount(a,"".concat(y.a.BASE_URL,"owner/journal_cats/edit/").concat(t.id,"?iframe=1&folderid=").concat(t.journal_cat_id),"Edit Account")},className:"edit-action dropdown-item"},S.a.get("Edit")),o,r,i))))}},{key:"getAccountListItem",value:function(t){var e=this,a="",n=0;0==t.type?(a=S.a.get("Credit"),n=t.total_credit-t.total_debit):(a=S.a.get("Debit"),n=t.total_debit-t.total_credit);var o=window.reactInit.showCostCenters?c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/cost_centers/assign_cost_centers/").concat(t.id),className:"edit-action dropdown-item"},S.a.get("Assign Cost Centers")):"",r=t.deletable?c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/journal_accounts/delete/").concat(t.id),onClick:function(a){e.openAddAccount(a,"".concat(y.a.BASE_URL,"owner/journal_accounts/delete/").concat(t.id,"?iframe=1&folderid=").concat(t.journal_cat_id),"Delete Account")},className:"delete-action dropdown-item"},S.a.get("Delete")):"",i=t.can_be_hidden?c.a.createElement("a",{href:"".concat(y.a.BASE_URL,"owner/journal_accounts/hide/").concat(t.id),className:"hide-action dropdown-item"},S.a.get("Hide")):"",s="1"==t.is_hidden?c.a.createElement("a",{target:"_self",href:"".concat(y.a.BASE_URL,"owner/journal_accounts/unhide/").concat(t.id),className:"unhide-action dropdown-item"}," ",S.a.get("Unhide")," "):"";return c.a.createElement("tr",{className:"chart-of-accounts-col-9-body-container-table-row",key:t.code},c.a.createElement("td",{className:" ".concat("1"==t.is_hidden?"hidden-account":""," border-0")},c.a.createElement(B.b,{to:"/accounts/"+t.id},c.a.createElement("div",{className:"chart-of-accounts-col-9-body-container-table-item"},c.a.createElement("div",{className:"item-container"},c.a.createElement("i",{className:"icon far fa-file mr-3"}),c.a.createElement("div",{className:"details"},c.a.createElement("p",{className:"name"},t.name),c.a.createElement("p",{className:"id"},"#",t.code)))))),c.a.createElement("td",{className:"border-0 text-right"},c.a.createElement(B.b,{to:"/accounts/"+t.id},c.a.createElement("div",{className:"credit-wrap text-right"},c.a.createElement("div",{className:"credit-container"},c.a.createElement("p",{className:"cost"},x(n)),c.a.createElement("p",{className:"type"},a))))),c.a.createElement("td",{className:"border-0 text-right",width:"50"},c.a.createElement("div",{className:"dropdown"},c.a.createElement("a",{className:"btn btn-sm btn-secondary dropdown-toggle",href:"#",role:"button","data-toggle":"dropdown","aria-haspopup":"true","aria-expanded":"false"},c.a.createElement("i",{className:"mdi mdi-dots-horizontal"})),c.a.createElement("div",{className:"dropdown-menu dropdown-menu-right"},q(t,!1),c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/journal_accounts/edit/").concat(t.id,"?folderid=").concat(t.journal_cat_id),onClick:function(a){e.openAddAccount(a,"".concat(y.a.BASE_URL,"owner/journal_accounts/edit/").concat(t.id,"?iframe=1&folderid=").concat(t.journal_cat_id),"Edit Account")},className:"edit-action dropdown-item"},S.a.get("Edit")),o,r,i,s))))}},{key:"componentDidUpdate",value:function(t){var e=this.getParentCatId();this.props.pagination[this.getParentCatId()]&&this.props.pagination[this.getParentCatId()].currentPage!=this.state.oldPage&&this.setState({loadingItems:!1,oldPage:this.props.pagination[this.getParentCatId()].currentPage}),this.props.setOpenedAccount(e);var a=this.props.cats[e];a&&a.journal_cat_id&&(this.props.parentCats[a.journal_cat_id]?this.props.expandParents([a.parent_cat_ids.split(","),a.journal_cat_id]):this.props.loadAccountParents(a.parent_cat_ids.split(","),a.id))}},{key:"getParentCatId",value:function(){return this.props.match.params.cat_id?this.props.match.params.cat_id:-1}},{key:"render",value:function(){var t=this,e=X.parse(this.props.location.search),a=this.getParentCatId(),n=parseInt(e.page?e.page:1),o="",r="";if(this.props.pagination&&this.props.pagination[a]&&0!=this.props.pagination[a].pagesCount){var i=this.props.pagination[a];n<i.pagesCount&&(o=c.a.createElement(B.b,{onClick:this.handlePaginationClick,to:"/cats/".concat(a,"?page=").concat(n+1),className:"chart-nav chart-next-page"},c.a.createElement("i",{className:"fa fa-angle-right"}))),n>1&&n-1<i.pagesCount&&(r=c.a.createElement(B.b,{onClick:this.handlePaginationClick,to:"/cats/".concat(a,"?page=").concat(n-1),className:"chart-nav chart-prev-page"},c.a.createElement("i",{className:"fa fa-angle-left"})))}this.props.getCatAccounts(a,n),this.props.generateBreadCrumbs(a);var s=void 0!==this.props.parentCats[a],d="",l=this.props.parentCats[a]?this.props.parentCats[a]:[],u=this.props.parentAccounts[a]?this.props.parentAccounts[a]:[],p=[];return s?s&&0===l.length&&0===u.length&&(d=c.a.createElement("div",null,S.a.get("This Account is Empty"))):c.a.createElement("div",null,S.a.get("Loading...")),l.forEach((function(e){("1"!==e.is_hidden||t.props.showHiddenAccounts)&&p.push(t.getCatListItem(e))})),u.forEach((function(e){("1"!==e.is_hidden||t.props.showHiddenAccounts)&&p.push(t.getAccountListItem(e))})),c.a.createElement("div",{id:"chart-of-accounts-child-board",className:"chart-of-accounts-col-9-body-container"},c.a.createElement(W,{parentId:a,url:this.state.iframeUrl,modalTitle:this.state.modalTitle,open:this.state.dialogOpened,handleClose:this.handleClose}),c.a.createElement("div",{style:{position:"relative"}},c.a.createElement("table",{className:"list-table table table-hover not-clickable chart-of-accounts-col-9-body-container-table"},c.a.createElement("tbody",null,p)),this.state.loadingItems||!s?c.a.createElement("div",{className:"load-data"},c.a.createElement("div",{className:"sk-chase"},c.a.createElement("div",{className:"sk-chase-dot"}),c.a.createElement("div",{className:"sk-chase-dot"}),c.a.createElement("div",{className:"sk-chase-dot"}),c.a.createElement("div",{className:"sk-chase-dot"}),c.a.createElement("div",{className:"sk-chase-dot"}),c.a.createElement("div",{className:"sk-chase-dot"}))):""),d,c.a.createElement("a",{href:"".concat(y.a.BASE_URL,"owner/journal_cats/add?folderid=").concat(a),onClick:function(e){t.openAddAccount(e,"".concat(y.a.BASE_URL,"owner/journal_cats/add?iframe=1&folderid=").concat(a),"Add Account")},className:"add-account btn btn-secondary font-weight-bold"},c.a.createElement("i",{className:"far fa-plus-circle text-success mr-2"}),c.a.createElement("span",null,S.a.get("Add Account"))),c.a.createElement("div",{className:"pagination-items"},o,this.props.pagination[a]&&1!=this.props.pagination[a].pagesCount&&0!=this.props.pagination[a].pagesCount&&c.a.createElement("span",null,c.a.createElement("span",null," ",this.props.pagination[a].currentPage," ")," ","/ ",c.a.createElement("span",null," ",this.props.pagination[a].pagesCount," ")),r))}}]),a}(c.a.Component),V=Object(j.f)(z);var K=Object(O.b)((function(t){return{parentAccounts:t.accountsReducer.parentAccounts,parentCats:t.accountsReducer.parentCats,accounts:t.accountsReducer.accounts,cats:t.accountsReducer.cats,pagination:t.accountsReducer.pagination,showHiddenAccounts:t.accountsReducer.showHiddenAccounts,ignoreCalcCats:t.accountsReducer.ignoreCalcCats}}),(function(t){return{getCatAccounts:function(e,a){t(Object(C.getChildAccounts)(e,a))},getTreeAccounts:function(){t(Object(C.getTreeAccounts)())},setOpenedAccount:function(e){t(Object(C.setOpenedAccount)(e,!0))},loadAccountParents:function(e,a){t(Object(C.loadAccountParents)(e,a))},expandParents:function(e){t(Object(C.expandParents)(e))},generateBreadCrumbs:function(e){t(Object(C.generateBreadCrumbs)(e,!0))},setExpandedStatus:function(e){t(Object(C.setExpandedStatus)(e,!0))}}}))(V),Y=function(t){Object(u.a)(a,t);var e=Object(p.a)(a);function a(){return Object(s.a)(this,a),e.apply(this,arguments)}return Object(d.a)(a,[{key:"render",value:function(){return c.a.createElement(j.c,null,c.a.createElement(j.a,{path:"/cats/:cat_id"},c.a.createElement(K,null)),c.a.createElement(j.a,{path:"/cats"},c.a.createElement(K,null)),c.a.createElement(j.a,{path:"/"},c.a.createElement(K,null)))}}]),a}(c.a.Component),$=function(t){Object(u.a)(a,t);var e=Object(p.a)(a);function a(t){var n;Object(s.a)(this,a),(n=e.call(this,t)).firstMessage=!0,n.resetIframe=function(){n.setState({random:n.state.random+1})},n.handleLoad=function(){var t=Object(l.a)(n);n.setState({loadingFrame:!0});var e=document.getElementById("iframe_id"),a=document.createElement("style");if(a.textContent="\n        button.popup-chat { display: none !important }\n        .modal-dialog {padding-top: 65px !important}\n        #main-content {margin: 0}\n        #main-nav, #main-content .header {display: none}\n        @media (min-width: 768px) {\n          .modal-dialog {\n              width: 750px;\n          }\n        }\n      ",e.contentDocument){var c=e.contentDocument.body.querySelector(".confirm-action > .btn:last-of-type");c&&c.addEventListener("click",(function(){t.resetIframe()}));var o=e.contentDocument.body.querySelector(".confirm-action > .btn:first-of-type");o&&o.addEventListener("click",(function(){t.setState({showAfterLoading:!0})})),e.contentDocument.body.querySelector("#flashMessage.Sucmessage")&&(t.resetIframe(),t.setState({showDeletePopup:!1,showAfterLoading:!1}))}try{e.contentDocument.head.appendChild(a)}catch(r){console.log(r)}};var c=Object(l.a)(n);return n.state={loadingFrame:!0,random:0,showDeletePopup:!1,iframeDeleteUrl:null,showAfterLoading:!1},window.onmessage=function(t){try{if("url_changed"===t.data.type){var e=t.data.ajaxUrl,a=e.match(/page\:([0-9]+)/)[1],n=e.split("?")[1],o="?page=".concat(a,"&").concat(n,"&is_ajax=1&load_scripts=1");this.firstMessage?this.firstMessage=!1:window.history.pushState("","","".concat(o))}else{r.a.findDOMNode(c).style.height="calc(100% - 50px) !important"}}catch(t){console.log(t)}}.bind(Object(l.a)(n)),n}return Object(d.a)(a,[{key:"componentDidMount",value:function(){this.handleLoad(),window.innerWidth}},{key:"render",value:function(){var t=this.props.match.params.account_id;try{var e=window.location.href.split("?")[1];if(void 0!=e)var a="?".concat(e)}catch(n){}if(void 0===a||"undefined"===typeof a)a="?page=1&date_from=&date_to=&is_ajax=1&load_scripts=1";return this.currentParams=a,this.props.generateBreadCrumbs(t),c.a.createElement(c.a.Fragment,null,this.state.showAfterLoading&&c.a.createElement("div",{className:"loader"},c.a.createElement("div",{className:"lds-ring"},c.a.createElement("div",null),c.a.createElement("div",null),c.a.createElement("div",null),c.a.createElement("div",null))),c.a.createElement("iframe",{key:this.state.random,id:"iframe_id",src:"".concat(y.a.BASE_URL,"owner/journal_accounts/account_transactions/").concat(t).concat(a),title:"",style:{height:"100%"},onLoad:this.handleLoad}))}},{key:"componentDidUpdate",value:function(t,e,a){var n=this.props.match.params.account_id;this.props.accounts[n]||this.props.getAccountData(n),this.props.setOpenedAccount(n);var c=this.props.accounts[n];c&&c.journal_cat_id&&(this.props.parentCats[c.journal_cat_id]?this.props.expandParents([c.parent_cat_ids.split(","),c.journal_cat_id]):this.props.loadAccountParents(c.parent_cat_ids.split(",")))}}]),a}(c.a.Component);var Q=Object(j.f)($),Z=Object(O.b)((function(t){return{accounts:t.accountsReducer.accounts,parentCats:t.accountsReducer.parentCats}}),(function(t){return{setOpenedAccount:function(e){t(Object(C.setOpenedAccount)(e,!1))},getAccountData:function(e){t(Object(C.getAccountData)(e))},generateBreadCrumbs:function(e){t(Object(C.generateBreadCrumbs)(e,!1))},loadAccountParents:function(e,a){t(Object(C.loadAccountParents)(e,a))},expandParents:function(e){t(Object(C.expandParents)(e))}}}))(Q),tt=(a(152),function(t){Object(u.a)(a,t);var e=Object(p.a)(a);function a(t){var n;return Object(s.a)(this,a),(n=e.call(this,t)).state={dialogOpened:!1,iframeUrl:"",modalTitle:"",loadingItems:"",oldPage:""},n.handleClose=n.handleClose.bind(Object(l.a)(n)),n}return Object(d.a)(a,[{key:"openAddAccount",value:function(t,e,a){return t.preventDefault(),this.setState(Object(h.a)(Object(h.a)({},this.state),{},{dialogOpened:!0,iframeUrl:e,modalTitle:a})),!1}},{key:"handleClose",value:function(){this.setState(Object(h.a)(Object(h.a)({},this.state),{},{dialogOpened:!1}))}},{key:"componentDidUpdate",value:function(t){this.props.loadingRoutingData||this.props.routingData||-1==this.props.currentOpenedAccount.id?this.props.routingData&&!this.props.loadingRoutingData&&window.initTooltips():this.props.getRoutingData(this.props.currentOpenedAccount.id,this.props.currentOpenedAccount.is_cat)}},{key:"getRoutingLink",value:function(t){var e,a="";return t.name&&t.code&&(a=c.a.createElement("span",null,c.a.createElement("a",{href:t.link,class:"text-decoration-underline",target:"_blank"},t.name," #",t.code))),t.entityTypeName&&(e=c.a.createElement("span",{className:"tip-circle lazy-tip","data-title":t.toolTipKey},c.a.createElement("i",{className:"fas fa-question-circle"}))),c.a.createElement("span",null,t.entityTypeName,e,a)}},{key:"render",value:function(){var t,e,a,n=this,o=this.props.currentOpenedAccount,r={},i=o.is_cat?"journal_cats":"journal_accounts";t="",-1!=o.id&&(o.is_cat?(r=this.props.cats[o.id]?this.props.cats[o.id]:{},t=c.a.createElement("div",{className:"action-buttons d-inline-block"},c.a.createElement("div",{className:"btn-group ml-3",role:"group","aria-label":"padination"},c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/reports/journal_transactions?show_net=&report_type=transaction&journal_cat_id=").concat(r.id,"&journal_account_id=&currency=-1&date_from=&date_to="),className:"btn btn-secondary"},c.a.createElement("i",{className:"fas fa-chart-pie"}))))):(r=this.props.accounts[o.id]?this.props.accounts[o.id]:{},t=c.a.createElement("div",{className:"action-buttons d-inline-block"},c.a.createElement("div",{className:"btn-group mx-3",role:"group","aria-label":"padination"},c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/reports/journal_transactions?show_net=&report_type=transaction&journal_cat_id=&journal_account_id=").concat(r.id,"&currency=-1&date_from=&date_to="),className:"btn btn-secondary"},c.a.createElement("i",{className:"fas fa-chart-pie"}))),c.a.createElement("a",{href:"".concat(y.a.BASE_URL,"owner/journals/add?journal_account_id=").concat(r.id),id:"add-link",target:"_blank","data-href":"/owner/journals/add",className:"btn btn-lg btn-success btn-addon button add-new-btn"},c.a.createElement("span",null,c.a.createElement("i",{className:"fa fa-plus"}),S.a.get("New Journal")))),a=window.reactInit.showCostCenters?c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/cost_centers/assign_cost_centers/").concat(r.id),className:"edit-action dropdown-item"}," ",S.a.get("Assign Cost Centers")," "):""),e=r.deletable?c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/").concat(i,"/delete/").concat(r.id),onClick:function(t){n.openAddAccount(t,"".concat(y.a.BASE_URL,"owner/").concat(i,"/delete/").concat(r.id,"?iframe=1&folderid=").concat(r.journal_cat_id),"Delete Account")},className:"delete-action dropdown-item"}," ",S.a.get("Delete")," "):"");var s={amount:"",amountText:""};r.name?(s=P(r),document.title=S.a.get("Chart of Accounts")+": "+r.name):r={name:S.a.get("Chart Of Accounts"),code:""};var d=c.a.createElement("a",{target:"_blank",href:"".concat(y.a.BASE_URL,"owner/").concat(i,"/edit/").concat(r.id,"?folderid=").concat(r.journal_cat_id),onClick:function(t){n.openAddAccount(t,"".concat(y.a.BASE_URL,"owner/").concat(i,"/edit/").concat(r.id,"?iframe=1&folderid=").concat(r.journal_cat_id),"Edit Account")},className:"edit-action dropdown-item"}," ",S.a.get("Edit")," "),l="1"==r.is_hidden?c.a.createElement("a",{target:"_self",href:"".concat(y.a.BASE_URL,"owner/").concat(i,"/unhide/").concat(r.id,"?folderid=").concat(r.journal_cat_id),className:"unhide-action dropdown-item"}," ",S.a.get("Unhide")," "):"",u=r.can_be_hidden?c.a.createElement("a",{target:"_self",href:"".concat(y.a.BASE_URL,"owner/").concat(i,"/hide/").concat(r.id,"?folderid=").concat(r.journal_cat_id),className:"hide-action dropdown-item"}," ",S.a.get("Hide")," "):"",p=[];this.props.routingData&&(p.push(this.getRoutingLink(this.props.routingData.systemRouting)),this.props.routingData.manualRouting.forEach((function(t){p.push(n.getRoutingLink(t))})));var h=window.reactInit.displayShowHiddenAccounts?c.a.createElement("div",{className:"form-check px-3"},c.a.createElement("input",{className:"form-check-input",type:"checkbox",role:"switch",id:"showHidden",onClick:function(){n.props.toggleShowHiddenAccounts()}}),c.a.createElement("label",{className:"form-check-label",htmlFor:"showHidden"},S.a.get("Show Hidden"))):"";return c.a.createElement("div",null,c.a.createElement("div",{className:"pages-head",style:{zIndex:1}},c.a.createElement(W,{url:this.state.iframeUrl,modalTitle:this.state.modalTitle,open:this.state.dialogOpened,handleClose:this.handleClose}),c.a.createElement("div",{className:"container-fluid"},c.a.createElement("div",{className:"row align-items-center"},c.a.createElement("div",{className:"col-sm-6"},c.a.createElement("div",{className:"row align-items-center"},c.a.createElement("div",{className:"col-sm-12 head-info"},c.a.createElement("div",{className:"pages-head-title"},c.a.createElement("h2",null,r.name," ",c.a.createElement("span",{className:"fs-14"},r.code?"#"+r.code:""),r.code&&c.a.createElement("div",{className:"btn-group dropdown"},c.a.createElement("div",{className:"dropdown-menu",style:{position:"absolute",transform:"translate3d(-107x, 35px, 0px)",top:"0px",left:"0px"}},q(r,o.is_cat),d,l,u,a,e),c.a.createElement("button",{type:"button",className:"btn dropdown-toggle dropdown-toggle-split px-2","data-toggle":"dropdown","aria-haspopup":"true","aria-expanded":"true"},c.a.createElement("span",{className:"sr-only"},"Toggle Dropdown"),c.a.createElement("i",{className:"fas fa-cog fs-12",style:{color:"#369!important"}})))),c.a.createElement("div",{className:"route-links"},p))))),c.a.createElement("div",{className:"col-sm-6 text-right"},h,c.a.createElement("div",{className:"credit-wrap text-right d-inline-block align-middle"},c.a.createElement("div",{className:"credit-container px-2 credit-overdue",style:{borderColor:s.amount.startsWith("-")?"#13b272":"#EB2121"}},c.a.createElement("p",{className:"cost"},s.amount),c.a.createElement("p",{className:"type"},s.amountText))),t)))))}}]),a}(c.a.Component));var et=Object(O.b)((function(t){var e;return e=t.accountsReducer.openedAccount.is_cat?t.accountsReducer.catsRoutingData[t.accountsReducer.openedAccount.id]:t.accountsReducer.accountsRoutingData[t.accountsReducer.openedAccount.id],{currentOpenedAccount:t.accountsReducer.openedAccount,accounts:t.accountsReducer.accounts,cats:t.accountsReducer.cats,routingData:e,loadingRoutingData:t.accountsReducer.loadingRoutingData}}),(function(t){return{getRoutingData:function(e,a){t(Object(C.getRoutingData)(e,a))},toggleShowHiddenAccounts:function(){t(Object(C.toggleShowHiddenAccounts)())}}}))(Object(j.f)(tt)),at=a(153)("./".concat(window.reactInit.lang,".json")),nt=function(t){Object(u.a)(a,t);var e=Object(p.a)(a);function a(t){var n;Object(s.a)(this,a),(n=e.call(this,t)).state={initDone:!1};var c=Object(l.a)(n);return window.addEventListener("bread-crumbs-clicked",(function(t){var e=String(t.detail).replaceAll("/v2/owner/chart-of-accounts","");c.props.history.push(e)}),!1),window.reactInit.branchActive&&n.props.getBranches(),n}return Object(d.a)(a,[{key:"componentDidMount",value:function(){var t=this;this.loadLocales().then((function(){t.setState({initDone:!0})}))}},{key:"loadLocales",value:function(){return S.a.init({currentLocale:window.reactInit.lang,locales:Object(i.a)({},window.reactInit.lang,at)})}},{key:"setJournalsBranch",value:function(t){v.a.get("".concat(y.a.BASE_URL,"api2/branches/set_report_transactions_branch/").concat(t.value)).then((function(){window.location.href=window.location.href})).catch((function(t){alert("You are not authorized to change branch")}))}},{key:"render",value:function(){var t=this,e=[];try{e=this.props.branches.map((function(t){return{value:t.id,label:t.name}}))}catch(o){e=[]}var a=window.reactInit.displayTransactionBranch?c.a.createElement("div",{className:"chart-of-accounts-col-9-filters-col"},c.a.createElement("label",{style:{display:"inline"}},S.a.get("Journals Branch")),c.a.createElement(g.a,{options:e,placeholder:"Select Branch",onChange:function(e){return t.setJournalsBranch(e)},value:window.reactInit.transactionBranch})):"",n=""!==a?c.a.createElement("div",{className:"chart-of-accounts-col-9-filters-container"},c.a.createElement("div",{className:"chart-of-accounts-col-9-filters-row"},a)):"";return this.state.initDone&&c.a.createElement("div",null,c.a.createElement(et,null),c.a.createElement("div",{className:"chart-of-accounts-container"},c.a.createElement("div",{className:"chart-of-accounts-row"},c.a.createElement(L,null),c.a.createElement("div",{className:"chart-of-accounts-col chart-of-accounts-col-9"},n,c.a.createElement(j.c,null,c.a.createElement(j.a,{path:"/cats"},c.a.createElement(Y,null)),c.a.createElement(j.a,{path:"/accounts/:account_id"},c.a.createElement(Z,null)),c.a.createElement(j.a,{path:"/"},c.a.createElement(Y,null)))))))}}]),a}(c.a.Component);var ct=Object(O.b)((function(t){return{branches:t.accountsReducer.branches}}),(function(t){return{getBranches:function(){t(Object(C.getBranches)())}}}))(Object(j.f)(nt));Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));var ot=a(42),rt=a(44);function it(t){return t.name+" #".concat(t.code)}function st(t,e,a,n){if(-1!=t){var c,o,r,i=[];if(e?(c=a[t])&&i.push({link:"/v2/owner/chart-of-accounts/cats/".concat(t),title:it(c)}):(c=n[t])&&i.push({link:"/v2/owner/chart-of-accounts/accounts/".concat(t),title:it(c)}),c)for(;void 0!==(o=a[c.journal_cat_id])&&(r=o,(0!==Object.keys(r).length||r.constructor!==Object)&&o.id);)i.unshift({link:"/v2/owner/chart-of-accounts/cats/".concat(o.id),title:it(o)}),c=o;i.unshift({link:"/v2/owner/chart-of-accounts/cats/-1",title:S.a.get("Chart of Accounts")}),window.updateBreadCrumbs(i)}else window.updateBreadCrumbs([{link:"/v2/owner/chart-of-accounts/cats/-1",title:S.a.get("Chart of Accounts")}])}var dt=a(7).GET_TREE_ACCOUNTS,lt={showHiddenAccounts:!1,pagination:{},branches:[],treeAccounts:{},parentCats:{},parentAccounts:{},accounts:{},cats:{},expandedAccounts:{},openedAccount:{is_cat:!0,id:-1},fetchingAccountParents:{},loadingAccounts:{},loadingRoutingData:!1,accountsRoutingData:{},catsRoutingData:{},ignoreCalculateAccounts:!1},ut=Object(ot.c)({accountsReducer:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:lt,e=arguments.length>1?arguments[1]:void 0;switch(e.type){case C.LOADING_ROUTING_DATA:return Object(h.a)(Object(h.a)({},t),{},{loadingRoutingData:!0});case C.SET_ROUTING_DATA:return e.payload.isCat?Object(h.a)(Object(h.a)({},t),{},{loadingRoutingData:!1,catsRoutingData:Object(h.a)(Object(h.a)({},t.catsRoutingData),{},Object(i.a)({},e.payload.accountId,e.payload.data))}):Object(h.a)(Object(h.a)({},t),{},{loadingRoutingData:!1,accountsRoutingData:Object(h.a)(Object(h.a)({},t.accountsRoutingData),{},Object(i.a)({},e.payload.accountId,e.payload.data))});case dt:return Object(h.a)(Object(h.a)({},t),{},{treeAccounts:{}});case C.SET_CHILD_ACCOUNTS:var a=e.payload.parent_id,n=e.payload.child_accounts.map((function(t){return t.JournalAccount}));return Object(h.a)(Object(h.a)({},t),{},{parentAccounts:Object(h.a)(Object(h.a)({},t.parentAccounts),{},Object(i.a)({},a,n)),cats:Object(h.a)(Object(h.a)({},t.cats),{},Object(i.a)({},a,Object(h.a)(Object(h.a)({},t.cats[a]),{},{children_loaded:!0})))});case C.SET_CHILD_CATS:a=e.payload.parent_id,n=e.payload.child_cats.map((function(t){return t.JournalCat}));return Object(h.a)(Object(h.a)({},t),{},{parentCats:Object(h.a)(Object(h.a)({},t.parentCats),{},Object(i.a)({},a,n)),cats:Object(h.a)(Object(h.a)({},t.cats),{},Object(i.a)({},a,Object(h.a)(Object(h.a)({},t.cats[a]),{},{children_loaded:!0})))});case C.SET_ACCOUNTS:var c=e.payload,o={};for(var r in c)o[c[r].JournalAccount.id]=c[r].JournalAccount;return Object(h.a)(Object(h.a)({},t),{},{accounts:Object(h.a)(Object(h.a)({},t.accounts),o)});case C.SET_CATS:var s=e.payload,d={};for(var l in s)d[s[l].JournalCat.id]=s[l].JournalCat;return Object(h.a)(Object(h.a)({},t),{},{cats:Object(h.a)(Object(h.a)({},t.cats),d)});case C.SET_IGNORE_CALC_CATS:var u=e.payload;return Object(h.a)(Object(h.a)({},t),{},{ignoreCalcCats:u});case C.SET_OPENED_ACCOUNT:return Object(h.a)(Object(h.a)({},t),{},{openedAccount:Object(h.a)({},{is_cat:e.payload.is_cat,id:parseInt(e.payload.account_id)})});case C.SET_ACCOUNT_DATA:return Object(h.a)(Object(h.a)({},t),{},{accounts:Object(h.a)(Object(h.a)({},t.accounts),{},Object(i.a)({},e.payload.id,Object(h.a)({},e.payload.data)))});case C.SET_CAT_DATA:return Object(h.a)(Object(h.a)({},t),{},{cats:Object(h.a)(Object(h.a)({},t.cats),{},Object(i.a)({},e.payload.id,Object(h.a)({},e.payload.data)))});case C.TOGGLE_SHOW_HIDDEN_ACCOUNTS:return Object(h.a)(Object(h.a)({},t),{},{showHiddenAccounts:!t.showHiddenAccounts});case C.LOADING_CHILD_ACCOUNTS:return Object(h.a)(Object(h.a)({},t),{},{loadingAccounts:Object(h.a)(Object(h.a)({},t.loadingAccounts),{},Object(i.a)({},e.payload.cat,e.payload.page))});case C.SET_ACCOUNT_PARENTS:var p={},m={},f={},g={};for(var E in e.payload.cats){for(var b in p=Object(h.a)(Object(h.a)({},p),{},Object(i.a)({},E,Object(rt.a)(e.payload.cats[E]))),e.payload.cats[E]){var _=e.payload.cats[E][b];f[_.id]=Object(h.a)({},_)}for(var A in m=Object(h.a)(Object(h.a)({},m),{},Object(i.a)({},E,Object(rt.a)(e.payload.accounts[E]))),e.payload.accounts[E]){var O=e.payload.accounts[E][A];g[O.id]=Object(h.a)({},O)}}return Object(h.a)(Object(h.a)({},t),{},{parentAccounts:Object(h.a)(Object(h.a)({},t.parentAccounts),m),parentCats:Object(h.a)(Object(h.a)({},t.parentCats),p),cats:Object(h.a)(Object(h.a)({},t.cats),f),accounts:Object(h.a)(Object(h.a)({},t.accounts),g)});case C.EXPAND_PARENTS:var v={};return e.payload.forEach((function(t){v[t]=!0})),Object(h.a)(Object(h.a)({},t),{},{expandedAccounts:Object(h.a)(Object(h.a)({},t.expandedAccounts),v)});case C.SET_BRANCHES:var j=e.payload.filter((function(t){if(1==t.Branch.status||3==t.Branch.status)return t.Branch})),N=j.map((function(t){return t.Branch}));return Object(h.a)(Object(h.a)({},t),{},{branches:Object(rt.a)(N)});case C.SET_EXPANDED_STATUS:return Object(h.a)(Object(h.a)({},t),{},{expandedAccounts:Object(h.a)(Object(h.a)({},t.expandedAccounts),{},Object(i.a)({},e.payload.account_id,e.payload.status))});case C.GENERATE_BREAD_CRUMBS:return st(e.payload.account_id,e.payload.is_cat,t.cats,t.accounts),t;case C.FETCHING_ACCOUNT_PARENTS:return Object(h.a)(Object(h.a)({},t),{},{fetchingAccountParents:Object(h.a)(Object(h.a)({},t.fetchingAccountParents),{},Object(i.a)({},e.payload.join(","),!0))});case C.SET_PAGINATION:return Object(h.a)(Object(h.a)({},t),{},{pagination:Object(h.a)(Object(h.a)({},t.pagination),{},Object(i.a)({},e.payload.catId,e.payload.pagination))});default:return t}}}),pt=a(92),ht=Object(ot.d)(ut,Object(ot.a)(pt.a));r.a.render(c.a.createElement(B.a,{basename:y.a.LINK_PREFIX},c.a.createElement(O.a,{store:ht},c.a.createElement(ct,null))),document.getElementById("root")),"serviceWorker"in navigator&&navigator.serviceWorker.ready.then((function(t){t.unregister()})).catch((function(t){console.error(t.message)}))},7:function(t,e,a){"use strict";a.r(e),a.d(e,"GET_TREE_ACCOUNTS",(function(){return d})),a.d(e,"GET_ACCOUNTS",(function(){return l})),a.d(e,"SET_CHILD_ACCOUNTS",(function(){return u})),a.d(e,"SET_CHILD_CATS",(function(){return p})),a.d(e,"SET_ACCOUNTS",(function(){return h})),a.d(e,"SET_CATS",(function(){return m})),a.d(e,"SET_IGNORE_CALC_CATS",(function(){return f})),a.d(e,"SET_OPENED_ACCOUNT",(function(){return g})),a.d(e,"SET_CAT_DATA",(function(){return E})),a.d(e,"SET_ACCOUNT_DATA",(function(){return b})),a.d(e,"LOADING_CHILD_ACCOUNTS",(function(){return _})),a.d(e,"EXPAND_PARENTS",(function(){return A})),a.d(e,"SET_ACCOUNT_PARENTS",(function(){return O})),a.d(e,"SET_BRANCHES",(function(){return C})),a.d(e,"SET_EXPANDED_STATUS",(function(){return v})),a.d(e,"GENERATE_BREAD_CRUMBS",(function(){return j})),a.d(e,"FETCHING_ACCOUNT_PARENTS",(function(){return N})),a.d(e,"SET_PAGINATION",(function(){return y})),a.d(e,"SET_ROUTING_DATA",(function(){return w})),a.d(e,"LOADING_ROUTING_DATA",(function(){return S})),a.d(e,"TOGGLE_SHOW_HIDDEN_ACCOUNTS",(function(){return T})),a.d(e,"getTreeAccounts",(function(){return D})),a.d(e,"getAccounts",(function(){return R})),a.d(e,"loadingRoutingData",(function(){return k})),a.d(e,"setRoutingData",(function(){return I})),a.d(e,"setChildAccounts",(function(){return U})),a.d(e,"setChildCats",(function(){return L})),a.d(e,"setCats",(function(){return B})),a.d(e,"setCaluclateCats",(function(){return x})),a.d(e,"setAccounts",(function(){return P})),a.d(e,"setOpenedAccount",(function(){return H})),a.d(e,"fetchChildren",(function(){return G})),a.d(e,"loadingChildAccounts",(function(){return J})),a.d(e,"setCatData",(function(){return F})),a.d(e,"setAccountData",(function(){return M})),a.d(e,"fetchAccountData",(function(){return W})),a.d(e,"getAccountData",(function(){return q})),a.d(e,"expandParents",(function(){return X})),a.d(e,"fetchAccountParents",(function(){return z})),a.d(e,"toggleShowHiddenAccounts",(function(){return V})),a.d(e,"loadAccountParents",(function(){return K})),a.d(e,"setBranches",(function(){return Y})),a.d(e,"getRoutingData",(function(){return $})),a.d(e,"getBranches",(function(){return Q})),a.d(e,"setAccountParents",(function(){return Z})),a.d(e,"setExpandedStatus",(function(){return tt})),a.d(e,"setPagination",(function(){return et})),a.d(e,"getChildAccounts",(function(){return at})),a.d(e,"generateBreadCrumbs",(function(){return nt}));var n=a(44),c=a(31),o=a(64),r=a(5),i=a(6),s=a.n(i),d="GET_TREE_ACCOUNTS",l="GET_ACCOUNTS",u="SET_CHILD_ACCOUNTS",p="SET_CHILD_CATS",h="SET_ACCOUNTS",m="SET_CATS",f="SET_IGNORE_CALC_CATS",g="SET_OPENED_ACCOUNT",E="SET_CAT_DATA",b="SET_ACCOUNT_DATA",_="LOADING_CHILD_ACCOUNTS",A="EXPAND_PARENTS",O="SET_ACCOUNT_PARENTS",C="SET_BRANCHES",v="SET_EXPANDED_STATUS",j="GENERATE_BREAD_CRUMBS",N="FETCHING_ACCOUNT_PARENTS",y="SET_PAGINATION",w="SET_ROUTING_DATA",S="LOADING_ROUTING_DATA",T="TOGGLE_SHOW_HIDDEN_ACCOUNTS";function D(){return{type:d}}function R(){return{type:l}}function k(){return{type:S}}function I(t,e){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return{type:w,payload:{accountId:t,isCat:a,data:e}}}function U(t,e){return{type:u,payload:{parent_id:t,child_accounts:e}}}function L(t,e){return{type:p,payload:{parent_id:t,child_cats:e}}}function B(t){return{type:m,payload:t}}function x(t){return{type:f,payload:t}}function P(t){return{type:h,payload:t}}function H(t,e){return{type:g,payload:{is_cat:e,account_id:t}}}function G(t,e){return c.a.get(r.a.BASE_URL+"api2/journals/accounts/".concat(t,"?page=").concat(e))}function J(t,e){return{type:_,payload:{cat:t,page:e}}}function F(t,e){return{type:E,payload:{id:t,data:e}}}function M(t,e){return{type:b,payload:{id:t,data:e}}}function W(t){return c.a.get("".concat(r.a.BASE_URL,"api2/journal_accounts/").concat(t))}function q(t){return function(e){return W(t).then((function(a){e(M(t,a.data.data.JournalAccount))}))}}function X(t){return{type:A,payload:t}}function z(t){return{type:N,payload:t}}function V(){return{type:T}}function K(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=o.store.getState().accountsReducer;return a.fetchingAccountParents[t.join(",")]?{type:null}:function(a){return a(z(t)),c.a.get("".concat(r.a.BASE_URL,"api2/journals/accounts/"),{params:{account_ids:t}}).then((function(c){a(Z({accounts:c.data.accounts,cats:c.data.cats,parents:c.data.parents}));var o=e?[].concat(Object(n.a)(t),[e]):t;a(X(o))}))}}function Y(t){return{type:C,payload:t}}function $(t,e){return function(a){a(k()),c.a.get("".concat(r.a.BASE_URL,"v2/api/accounting/").concat(t,"/account-details?is_cat=").concat(e?1:0)).then((function(n){a(I(t,n.data,e))}))}}function Q(){return function(t){c.a.get("".concat(r.a.BASE_URL,"api2/branches/")).then((function(e){var a=[];try{e.data.data.length>0&&(a=[{Branch:{id:-1,name:s.a.get("All Branches"),status:1}}].concat(Object(n.a)(e.data.data)))}catch(c){}t(Y(a))}))}}function Z(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{accounts:{},cats:{},parents:[]};return{type:O,payload:{accounts:t.accounts,cats:t.cats,parents:t.parents}}}function tt(t,e){return{type:v,payload:{account_id:t,status:e}}}function et(t,e){return{type:y,payload:{catId:t,pagination:e}}}function at(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=o.store.getState().accountsReducer;return a.loadingAccounts[t]==e?{type:null}:function(a){return a(J(t,e)),G(t,e).then((function(e){a(U(t,e.data.accounts)),a(L(t,e.data.cats)),a(B(e.data.cats)),a(x(e.data.ignoreCalculateAccounts)),a(P(e.data.accounts)),a(F(t,!!e.data.parent_account&&e.data.parent_account.JournalCat)),a(et(t,e.data.pagination))}))}}function nt(t,e){return{type:j,payload:{account_id:t,is_cat:e}}}},76:function(t,e,a){},97:function(t,e,a){t.exports=a(64)}},[[97,1,2]]]);
//# sourceMappingURL=main.a2fed7bc.chunk.js.map