/*
object-assign
(c) <PERSON><PERSON> Sorhus
@license MIT
*/

/*!
 * cookie
 * Copyright(c) 2012-2014 <PERSON>
 * Copyright(c) 2015 <PERSON>
 * MIT Licensed
 */

/*!
 * escape-html
 * Copyright(c) 2012-2013 <PERSON><PERSON>
 * Copyright(c) 2015 <PERSON>
 * Copyright(c) 2015 <PERSON><PERSON><PERSON> "<PERSON>" Gu
 * MIT Licensed
 */

/**
 * A better abstraction over CSS.
 *
 * @copyright Ole<PERSON> (Slobodskoi) / Isonen 2014-present
 * @website https://github.com/cssinjs/jss
 * @license MIT
 */

/** @license React v0.19.1
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v16.13.1
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v16.13.1
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
