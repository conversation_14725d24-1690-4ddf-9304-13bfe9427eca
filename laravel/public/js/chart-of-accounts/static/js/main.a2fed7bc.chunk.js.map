{"version": 3, "sources": ["locals sync /^/.//.*/.json$", "utils/app-axios.js", "config/config.js", "components/accounts-tree/accounts-tree.js", "utils/functions.js", "utils/account-type-util.js", "components/add-account/add-account.js", "functions.js", "components/cat-children-board/cat-children-board.js", "components/accounts-board/accounts-board.js", "components/account-transactions/account-transactions.js", "components/page-head/page-head.js", "App.js", "serviceWorker.js", "utils/bread-crumbs-util.js", "reducers/accounts.js", "reducers/index.js", "index.js", "actions/accounts.js"], "names": ["map", "webpackContext", "req", "id", "webpackContextResolve", "__webpack_require__", "o", "e", "Error", "code", "keys", "Object", "resolve", "module", "exports", "appAxios", "axios", "create", "interceptors", "request", "use", "config", "APP_CONFIG", "StyledTreeItem", "with<PERSON><PERSON><PERSON>", "theme", "iconContainer", "opacity", "group", "marginLeft", "paddingLeft", "borderLeft", "fade", "palette", "text", "primary", "props", "TreeItem", "className", "isHidden", "title", "label", "makeStyles", "root", "height", "flexGrow", "max<PERSON><PERSON><PERSON>", "FolderOpened", "FileIcon", "Folder", "Loading", "AccountsTree", "rootId", "handleNodeClick", "window", "innerWidth", "target", "classList", "contains", "document", "querySelector", "scrollIntoView", "renderTree", "nodes", "icon", "nodeId", "account_type", "expandedAccounts", "has_children", "children", "length", "name", "key", "onClick", "Array", "isArray", "node", "getCatAccounts", "account", "is_hidden", "childTreeItems", "this", "parentCats", "for<PERSON>ach", "cat", "childData", "getAccountTreeItemData", "getTreeItemChildren", "child_limit", "child_count", "push", "parentAccounts", "sort", "a", "b", "event", "itemId", "expand", "tagName", "catId", "accountId", "indexOf", "split", "cats", "setExpandedStatus", "history", "accounts", "inputValue", "callback", "get", "BASE_URL", "then", "data", "options", "record", "option", "value", "formattedOptions", "catch", "err", "alert", "branch", "location", "href", "branchesOptions", "branches", "treeAccounts", "primaryCat", "treeAccountItem", "tree", "primaryAccount", "accountBranch", "reactInit", "displayAccountBranch", "placeholder", "onChange", "setAccountsBranch", "accountsBranch", "accountsDropdownStyles", "menu", "width", "css", "whiteSpace", "intl", "styles", "cacheOptions", "loadOptions", "loadSearchAccounts", "openAccount", "isClearable", "TreeView", "expanded", "selected", "opendAccount", "toString", "onNodeSelect", "treeItemClicked", "React", "Component", "connect", "state", "accountsReducer", "openedAccount", "showHiddenAccounts", "dispatch", "cat_id", "getChildAccounts", "status", "with<PERSON><PERSON><PERSON>", "formatPrice", "amount", "formatted", "AppFormatPrice", "countryCode", "currency", "replace", "getAccountAmountData", "amountText", "type", "total_credit", "total_debit", "formatPriceWithCurrency", "AddAccount", "handleLoad", "setState", "loadingFrame", "$this", "onmessage", "ReactDOM", "findDOMNode", "style", "Dialog", "fullWidth", "onClose", "handleClose", "aria-<PERSON>by", "open", "DialogTitle", "modalTitle", "IconButton", "aria-label", "position", "right", "top", "textAlign", "fontWeight", "onLoad", "minHeight", "src", "url", "getAccountDetailsLink", "isCat", "qs", "require", "CatChildrenBoard", "scrollSpinner", "handlePaginationClick", "getElementById", "scrollTop", "loadingItems", "oldPage", "pagination", "getParentCatId", "currentPage", "dialogOpened", "iframeUrl", "bind", "preventDefault", "parentCatId", "match", "params", "setOpenedAccount", "child", "ignoreCalcCats", "deleteLink", "deletable", "openAddAccount", "journal_cat_id", "hideLink", "can_be_hidden", "unhideLink", "to", "role", "data-toggle", "aria-haspopup", "aria-expanded", "costCenterLink", "showCostCenters", "oldprops", "parent", "expandParents", "parent_cat_ids", "loadAccountParents", "parsed", "parse", "search", "page", "parseInt", "nextNav", "prevNav", "pagesCount", "pagingData", "generateBreadCrumbs", "loaded", "undefined", "emptyMessage", "childCats", "childAccounts", "childListItems", "getCatListItem", "getAccountListItem", "parentId", "comp", "getTreeAccounts", "account_parents", "account_id", "AccountsBoard", "path", "AccountTransactions", "firstMessage", "resetIframe", "random", "that", "iframe", "createElement", "textContent", "contentDocument", "noButton", "body", "addEventListener", "yesButton", "showAfterLoading", "showDeletePopup", "head", "append<PERSON><PERSON><PERSON>", "console", "log", "iframeDeleteUrl", "ajaxUrl", "iframeParams", "pushState", "currentParams", "prevProps", "prevState", "snapshot", "getAccountData", "PageHead", "loadingRoutingData", "routingData", "currentOpenedAccount", "initTooltips", "getRoutingData", "is_cat", "toolTip", "link", "class", "entityTypeName", "data-title", "toolTipKey", "reportLink", "currentOpenedAccountData", "urlPrefix", "data-href", "amountData", "editBtn", "unhideBtn", "hideBtn", "routingLinks", "getRoutingLink", "systemRouting", "manualRouting", "item", "showHiddenAccountsCheckbox", "displayShowHiddenAccounts", "toggleShowHiddenAccounts", "htmlFor", "zIndex", "transform", "left", "color", "startsWith", "catsRoutingData", "accountsRoutingData", "localFile", "lang", "App", "initDone", "self", "String", "detail", "replaceAll", "branchActive", "getBranches", "loadLocales", "init", "currentLocale", "locales", "transactionBranch", "displayTransactionBranch", "display", "setJournalsBranch", "branchFilters", "Boolean", "hostname", "getBreadCrumbTitle", "generateBreadcrumbs", "openedAccountId", "parentAccount", "obj", "bc", "constructor", "unshift", "updateBreadCrumbs", "GET_TREE_ACCOUNTS", "initialState", "fetchingAccountParents", "loadingAccounts", "ignoreCalculateAccounts", "combineReducers", "action", "LOADING_ROUTING_DATA", "SET_ROUTING_DATA", "payload", "SET_CHILD_ACCOUNTS", "parent_id", "childs", "child_accounts", "JournalAccount", "children_loaded", "SET_CHILD_CATS", "child_cats", "JournalCat", "SET_ACCOUNTS", "newAccounts", "accountsMappedById", "prop", "SET_CATS", "newCats", "catsMappedById", "SET_IGNORE_CALC_CATS", "SET_OPENED_ACCOUNT", "SET_ACCOUNT_DATA", "SET_CAT_DATA", "TOGGLE_SHOW_HIDDEN_ACCOUNTS", "LOADING_CHILD_ACCOUNTS", "SET_ACCOUNT_PARENTS", "newParentCats", "newParentAccounts", "newCatsToAdd", "newAccountsToAdd", "<PERSON><PERSON><PERSON>", "EXPAND_PARENTS", "parentsToExpand", "el", "SET_BRANCHES", "filter", "Branch", "branchesFlat", "SET_EXPANDED_STATUS", "GENERATE_BREAD_CRUMBS", "FETCHING_ACCOUNT_PARENTS", "join", "SET_PAGINATION", "store", "createStore", "rootReducer", "applyMiddleware", "thunk", "render", "basename", "LINK_PREFIX", "navigator", "serviceWorker", "ready", "registration", "unregister", "error", "message", "GET_ACCOUNTS", "getAccounts", "setRoutingData", "accountData", "setChildAccounts", "setChildCats", "setCats", "setCaluclateCats", "ignoreCalculateCats", "setAccounts", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingChildAccounts", "setCatData", "cat_data", "setAccountData", "account_data", "fetchAccountData", "res", "parent_ids", "fetchAccountParents", "parentIds", "accountParents", "addAccountToExpanded", "accountsRed", "getState", "account_ids", "setAccountParents", "parents", "setBranches", "branchesWithAll", "expandedStatus", "setPagination", "parent_account"], "mappings": "iLAAA,IAAIA,EAAM,CACT,YAAa,IACb,YAAa,KAId,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOG,EAAoBF,GAE5B,SAASC,EAAsBF,GAC9B,IAAIG,EAAoBC,EAAEN,EAAKE,GAAM,CACpC,IAAIK,EAAI,IAAIC,MAAM,uBAAyBN,EAAM,KAEjD,MADAK,EAAEE,KAAO,mBACHF,EAEP,OAAOP,EAAIE,GAEZD,EAAeS,KAAO,WACrB,OAAOC,OAAOD,KAAKV,IAEpBC,EAAeW,QAAUR,EACzBS,EAAOC,QAAUb,EACjBA,EAAeE,GAAK,K,+iECvBpB,YAEMY,EAFN,OAEiBC,EAAMC,SAIvBF,EAASG,aAAaC,QAAQC,KAAI,SAAUC,GAE1C,OAAOA,KAGMN,O,+BCXf,sCAAMO,EAAa,CAKjBA,SAAsB,IACtBA,YAAyB,8BAG3BA,YAAyB,M,oTCOnBC,EAAiBC,aAAW,SAACC,GAAD,MAAY,CAC5CC,cAAe,CACb,WAAY,CACVC,QAAS,KAGbC,MAAO,CACLC,WAAY,EACZC,YAAa,GACbC,WAAW,cAAD,OAAgBC,YAAKP,EAAMQ,QAAQC,KAAKC,QAAS,SATxCX,EAWnB,SAACY,GACH,OAAO,kBAACC,EAAA,EAAD,iBAAcD,EAAd,CAAqBE,UAAS,UAAuB,KAAlBF,EAAMG,SAAkB,iBAAmB,IAAMC,MAAK,WAAMJ,EAAM3B,KAAZ,YAAoB2B,EAAMK,aAW1GC,YAAW,CAC3BC,KAAM,CACJC,OAAQ,IACRC,SAAU,EACVC,SAAU,OAQd,SAASC,EAAaX,GACpB,OAAO,uBAAGE,UAAU,uBAGtB,SAASU,EAASZ,GAChB,OAAO,uBAAGE,UAAU,gBAGtB,SAASW,EAAOb,GACd,OAAO,uBAAGE,UAAU,kBAGtB,SAASY,EAAQd,GACf,OAAO,uBAAGE,UAAU,2B,IAEhBa,E,kDAEJ,WAAYf,GAAQ,IAAD,8BACjB,cAAMA,IAFRgB,QAAU,EACS,EAMnBC,gBAAkB,SAAC9C,GAEf+C,OAAOC,YAAc,KACrBhD,EAAEiD,OAAOC,UAAUC,SAAS,sBAE5BC,SACGC,cAAc,wCACdC,kBAbY,EAiBnBC,WAAa,SAACC,GACZ,IAAIC,EACAC,EAASF,EAAMtD,KAAO,IAAKsD,EAAM5D,GAErC,OAAQ4D,EAAMG,cACZ,IAAK,MACHD,EAASF,EAAM5D,GAGb6D,EADE,EAAK5B,MAAM+B,iBAAiBJ,EAAM5D,IAC7B,kBAAC4C,EAAD,MAEA,kBAACE,EAAD,MAET,MACF,IAAK,UACHe,EAAO,kBAAChB,EAAD,MACP,MACF,IAAK,OACHgB,EAAO,kBAACd,EAAD,MAaX,GAVIa,EAAMK,cAAyC,GAAzBL,EAAMM,SAASC,SACvCP,EAAMM,SAAW,CACf,CACElE,GAAI,OACJoE,KAAM,GACNL,aAAc,OACdzD,KAAMsD,EAAMtD,KAAO,YAIJ,KAAlBsD,EAAMxB,UAAuD,GAApC,EAAKH,MAAL,mBAG5B,OACE,kBAACb,EAAD,CACEkB,MAAOsB,EAAMQ,KACbN,OAAQA,EACRD,KAAMA,EACNQ,IAAKT,EAAMtD,KAAO,IAAKsD,EAAM5D,GAC7BM,KAAMsD,EAAMtD,KACZ8B,SAAUwB,EAAMxB,SAChBkC,QAAS,EAAKpB,iBAEbqB,MAAMC,QAAQZ,EAAMM,UACjBN,EAAMM,SAASrE,KAAI,SAAC4E,GAAD,OAAU,EAAKd,WAAWc,MAC7C,OA7DR,EAAKxC,MAAMyC,gBAAgB,GAFV,E,gHAoEIC,EAASZ,GAC9B,MAAO,CACL/D,GAAI2E,EAAQ3E,GACZM,KAAMqE,EAAQrE,KACd8D,KAAMO,EAAQP,KACdhC,SAAUuC,EAAQC,UAClBV,SAAU,GACVH,aAAcA,EACdE,eAAcU,EAAQV,gB,0CAINU,GAAU,IAAD,OACvBE,EAAiB,GAuCrB,OAtCIC,KAAK7C,MAAM8C,WAAWJ,EAAQ3E,KAChC8E,KAAK7C,MAAM8C,WAAWJ,EAAQ3E,IAAIgF,SAAQ,SAACC,GAEzC,IAAIC,EAAY,EAAKC,uBAAuBF,EAAK,OAC7CA,EAAIhB,eACNiB,EAAUhB,SAAW,EAAKkB,oBAAoBH,IAE5C9D,IAAWkE,YAAcJ,EAAIK,aAC/BJ,EAAUhB,SAASqB,KACjB,EAAKJ,uBACH,CACEnF,GAAI,QAAUiF,EAAIjF,GAClBoE,KAAM,WACN9D,KAAMqE,EAAQrE,KAAO,QACrB8B,SAAU6C,EAAIL,WAEhB,SAINC,EAAeU,KAAKL,MAGpBJ,KAAK7C,MAAMuD,eAAeb,EAAQ3E,KACpC8E,KAAK7C,MAAMuD,eAAeb,EAAQ3E,IAAIgF,SAAQ,SAACL,GAC7CE,EAAeU,KAAK,EAAKJ,uBAAuBR,EAAS,eAG7DE,EAAiBA,EAAeY,MAAM,SAACC,EAAEC,GACvC,OAAKD,EAAEpF,KAAOqF,EAAErF,MACN,EAELoF,EAAEpF,KAAOqF,EAAErF,KACP,EAEF,O,sCAMKsF,EAAOC,GACrB,IACMC,EAAqB,MADXF,EAAMvC,OAAO0C,QAEzBC,GAAQ,EACRC,GAAY,GACY,IAAzBJ,EAAOK,QAAQ,KAChBF,EAAQH,EAERI,EAAYJ,EAAOM,MAAM,KAAK,GAE5BH,GAASlB,KAAK7C,MAAMmE,KAAKJ,IAC3BlB,KAAK7C,MAAMyC,eAAesB,GACtBF,EACEhB,KAAK7C,MAAM+B,iBAAiBgC,GAC9BlB,KAAK7C,MAAMoE,kBAAkBL,GAAO,GAEpClB,KAAK7C,MAAMoE,kBAAkBL,GAAO,GAGtClB,KAAK7C,MAAMqE,QAAQf,KAAK,SAAWS,IAE5BlB,KAAK7C,MAAMsE,SAASN,GAC7BnB,KAAK7C,MAAMqE,QAAQf,KAAK,aAAeU,GAEvCnB,KAAK7C,MAAMqE,QAAQf,KAAK,SAAWS,K,yCAIpBQ,EAAYC,GAC7B7F,IACG8F,IADH,UAEOvF,IAAWwF,SAFlB,6CAE+DH,EAF/D,2BAIGI,MAAK,SAACC,GACL,IAAMC,EAAU,GAChB,GAAID,EAAKA,KAAM,CACb,IAAK,IAAMxC,KAAOwC,EAAKA,KAAM,CAC3B,IAAME,EAASF,EAAKA,KAAKxC,GACnB2C,EAAS,CAAE1E,MAAOyE,EAAO3C,KAAM6C,MAAOF,EAAO/G,GAAIiF,IAAK8B,EAAO9B,KAC/D6B,EAAQC,EAAOX,MACjBU,EAAQC,EAAOX,MAAMb,KAAKyB,GAE1BF,EAAQC,EAAOX,MAAQ,CAACY,GAG5B,IAAME,EAAmB,GACzB,IAAK,IAAMzF,KAASqF,EAClBI,EAAiB3B,KAAK,CAAEjD,MAAOb,EAAOqF,QAASA,EAAQrF,KAEzDgF,EAASS,QAETT,EAAS,OAGZU,OAAM,SAACC,GACNC,MACE,+E,kCAKIJ,GACPA,IACIA,EAAMhC,IAGTH,KAAK7C,MAAMqE,QAAQf,KAAK,SAAW0B,EAAMA,OAFzCnC,KAAK7C,MAAMqE,QAAQf,KAAK,aAAe0B,EAAMA,U,wCAOjCK,GAChB1G,IACG8F,IADH,UAEOvF,IAAWwF,SAFlB,mDAEqEW,EAAOL,QAEzEL,MAAK,WACJzD,OAAOoE,SAASC,KAAOrE,OAAOoE,SAASC,QAExCL,OAAM,SAACC,GACNC,MAAM,gD,+BAIF,IAAD,OACHI,EAAkB,GACtB,IACIA,EAAkB3C,KAAK7C,MAAMyF,SAAS7H,KAAI,SAACyH,GAC3C,MAAO,CAAEL,MAAOK,EAAOtH,GAAIsC,MAAOgF,EAAOlD,SAE5C,MAAOhE,GACNqH,EAAkB,GAGpB,IAAME,EAAe,GACjB7C,KAAK7C,MAAM8C,WAAWD,KAAK7B,SAC7B6B,KAAK7C,MAAM8C,WAAWD,KAAK7B,QAAQ+B,SAAQ,SAAC4C,GAC1C,IAAIC,EAAkB,EAAK1C,uBAAuByC,EAAY,OAC9DC,EAAgB3D,SAAW,EAAKkB,oBAAoBwC,GACpDD,EAAaC,EAAWtH,MAAQuH,KAGpC,IAAIC,EAAO,GACX,GAAIH,EACF,IAAK,IAAMI,KAAkBJ,EAC3BG,EAAKvC,KAAKT,KAAKnB,WAAWgE,EAAaI,KAG3C,IAAM/D,EAAmBxD,OAAOD,KAAKuE,KAAK7C,MAAM+B,kBAAkBnE,KAChE,SAACwE,GACC,GAAI,EAAKpC,MAAM+B,iBAAiBK,GAC9B,OAAOA,KAIP2D,EAAgB7E,OAAO8E,UAAUC,qBACrC,yBAAK/F,UAAU,uCACb,kBAAC,IAAD,CACE2E,QAASW,EACTU,YAAa,gBACbC,SAAU,SAACnB,GAAD,OAAW,EAAKoB,kBAAkBpB,IAC5CA,MAAO9D,OAAO8E,UAAUK,kBAI5B,GAEIC,EAAyB,CAC7BC,KAAM,cAAGC,MAAH,IAAaC,EAAb,4CAAC,eACFA,GADC,IAEJD,MAAO,OACP9F,SAAU,OACVgG,WAAY,aAGhB,OACE,yBAAKxG,UAAU,iDACb,yBAAKA,UAAU,oCACb,yBAAKA,UAAU,6CACb,yBAAKA,UAAU,uCACb,yBAAKA,UAAU,uCAIb,kBAAC,IAAD,CACEgG,YAAaS,IAAKlC,IAAI,UACtBmC,OAAQN,EACRO,cAAY,EACZC,YAAajE,KAAKkE,mBAClBZ,SAAU,SAACnB,GAAD,OAAW,EAAKgC,YAAYhC,IACtCiC,aAAa,OAMpBlB,GAEH,yBAAK7F,UAAU,0CACb,kBAACgH,EAAA,EAAD,CAGEC,SAAUpF,EACVqF,UAAyC,GAA/BvE,KAAK7C,MAAMqH,aAAatJ,GAAW8E,KAAK7C,MAAMqH,aAAatJ,GAAGuJ,WAAa,GACrFC,aAAc,SAAC5D,EAAOqB,GAAR,OAAkB,EAAKwC,gBAAgB7D,EAAOqB,KAE3Da,S,GAnSc4B,IAAMC,WAmUlBC,mBAxBf,SAAyBC,GACvB,MAAO,CACLnC,SAAUmC,EAAMC,gBAAgBpC,SAChClC,eAAgBqE,EAAMC,gBAAgBtE,eACtCT,WAAY8E,EAAMC,gBAAgB/E,WAClCwB,SAAUsD,EAAMC,gBAAgBvD,SAChCH,KAAMyD,EAAMC,gBAAgB1D,KAC5BgD,SAAUS,EAAMC,gBAAgBV,SAChCpF,iBAAkB6F,EAAMC,gBAAgB9F,iBACxCsF,aAAcO,EAAMC,gBAAgBC,cACpCC,mBAAoBH,EAAMC,gBAAgBE,uBAI9C,SAA4BC,GAC1B,MAAO,CACLvF,eAAgB,SAACwF,GACfD,EAASE,2BAAiBD,EAAQ,KAEpC7D,kBAAmB,SAAC6D,EAAQE,GAC1BH,EAAS5D,4BAAkB6D,EAAQE,QAI1BR,CAGbS,YAAWrH,I,QCxYN,SAASsH,EAAYC,GAE1B,IAAIC,EAAYrH,OAAOsH,eAAeF,EAAQpH,OAAO8E,UAAUyC,YAAavH,OAAO8E,UAAU0C,UAE7F,MADc,SAAXH,GAA+B,UAAXA,GAAgC,MAAXA,IAAiBA,EAAYA,EAAUI,QAAQ,IAAI,KACxFJ,ECEF,SAASK,EAAqBlG,GACnC,IAAImG,EACAP,EASJ,OAdiC,GAM7B5F,EAAQoG,MACVD,EAAalC,IAAKlC,IAAI,UACtB6D,EAAS5F,EAAQqG,aAAerG,EAAQsG,cAExCH,EAAalC,IAAKlC,IAAI,SACtB6D,EAAS5F,EAAQsG,YAActG,EAAQqG,cAGlC,CACLF,aACAP,OAHFA,EDTK,SAAiCA,GAItC,OAHGA,GAAU,KAAQA,EAAS,IAC5BA,EAAS,GAEJpH,OAAOsH,eACZF,EACApH,OAAO8E,UAAUyC,YACjBvH,OAAO8E,UAAU0C,UACjB,GCCOO,CAAwBX,I,gDCPdY,E,kDACnB,WAAYlJ,GAAQ,IAAD,uBACjB,cAAMA,IAaRmJ,WAAa,WACX,EAAKC,SAAS,CAAEC,cAAc,KAb9B,EAAKzB,MAAQ,CACXyB,cAAc,GAEhB,IAAIC,EAAK,eALQ,OAMjBpI,OAAOqI,UAAY,SAAUpL,GAC3B,IACcqL,IAASC,YAAYH,GAC7B9H,cAAc,UAAUkI,MAAMlJ,OAASrC,EAAEyG,KAAKpE,OAClD,MAAOrC,MAVM,E,qDAiBT,IAAD,OACP,OACE,kBAACwL,EAAA,EAAD,CACEjJ,SAAU,KACVkJ,WAAW,EACXC,QAAS,kBAAM,EAAK7J,MAAM8J,eAC1BC,kBAAgB,sBAChBC,KAAMnH,KAAK7C,MAAMgK,MAEjB,kBAACC,EAAA,EAAD,CAAalM,GAAG,uBACb8E,KAAK7C,MAAMkK,WAAavD,IAAKlC,IAAI5B,KAAK7C,MAAMkK,YAAc,GAE3D,kBAACC,EAAA,EAAD,CACEC,aAAW,QACXV,MAAO,CAAEW,SAAU,WAAYC,MAAO,EAAGC,IAAK,GAC9ClI,QAAS,kBAAM,EAAKrC,MAAM8J,gBAE1B,kBAAC,IAAD,QAGHjH,KAAK+E,MAAMyB,cACV,yBAAKK,MAAO,CAAEc,UAAW,SAAUC,WAAY,SAC5C9D,IAAKlC,IAAI,eAGd,4BACEiG,OAAQ7H,KAAKsG,WACb3C,MAAO,OACPhG,OAAQ,OACRkJ,MAAO,CAACiB,UAAW,SACnBC,IAAK/H,KAAK7C,MAAM6K,IAChBzK,MAAM,U,GAjDwBqH,IAAMC,WCLvC,SAASoD,EAAsBpI,GAAyB,IAAhBqI,EAAe,wDAC1D,OAAQ,uBACJ3J,OAAQ,SACRmE,KAAM,UAAGrG,IAAWwF,SAAd,+BAA6ChC,EAAQ3E,GAArD,sBAA4EgN,EAAQ,EAAI,IAC9F7K,UAAU,6BAETyG,IAAKlC,IAAI,SCWlB,IAAMuG,EAAKC,EAAQ,KAEbC,E,kDACJ,WAAYlL,GAAQ,IAAD,8BACjB,cAAMA,IAoCRmL,cAAgB,aArCG,EA+TnBC,sBAAwB,WACV7J,SAAS8J,eAAe,iCAC9BC,UAAY,EAClB,EAAKlC,SAAS,CACZmC,cAAc,EACdC,QAAS,EAAKxL,MAAMyL,WAAW,EAAKC,kBAAkBC,eAlUxD,EAAK/D,MAAQ,CACXgE,cAAc,EACdC,UAAW,GACX3B,WAAY,GACZqB,aAAc,GACdC,QAAS,IAEX,EAAK1B,YAAc,EAAKA,YAAYgC,KAAjB,gBATF,E,2DAYJnI,EAAOkH,EAAKX,GAQzB,OAPAvG,EAAMoI,iBACNlJ,KAAKuG,SAAL,2BACKvG,KAAK+E,OADV,IAEEgE,cAAc,EACdC,UAAWhB,EACXX,WAAYA,MAEP,I,oCAIPrH,KAAKuG,SAAL,2BACKvG,KAAK+E,OADV,IAEEgE,cAAc,O,0CAIhB,IAAMI,EAAcnJ,KAAK7C,MAAMiM,MAAMC,OAAOjE,OACxCpF,KAAK7C,MAAMiM,MAAMC,OAAOjE,QACvB,EACLpF,KAAK7C,MAAMmM,iBAAiBH,K,qCAafI,GAAQ,IAAD,OAChBvD,EAAa,GACbP,EAAS,EACTzF,KAAK7C,MAAMqM,iBHtEgB,GGuE3BD,EAAMtD,MACRD,EAAalC,IAAKlC,IAAI,UACtB6D,EAAS8D,EAAMrD,aAAeqD,EAAMpD,cAEpCH,EAAalC,IAAKlC,IAAI,SACtB6D,EAAS8D,EAAMpD,YAAcoD,EAAMrD,eAErC,IAAMuD,EAAaF,EAAMG,UACvB,uBACEnL,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,qCAAqD0H,EAAMrO,IAC/DsE,QAAS,SAAClE,GACR,EAAKqO,eACHrO,EADF,UAEKe,IAAWwF,SAFhB,qCAEqD0H,EAAMrO,GAF3D,8BAEmFqO,EAAMK,gBACvF,mBAGJvM,UAAU,+BAETyG,IAAKlC,IAAI,WAGZ,GAEIiI,EAAWN,EAAMO,cACnB,uBACIpH,KAAI,UAAKrG,IAAWwF,SAAhB,mCAAmD0H,EAAMrO,IAC7DmC,UAAU,6BAEXyG,IAAKlC,IAAI,SAEZ,GACEmI,EAAgC,KAAnBR,EAAMzJ,UACrB,uBACIvB,OAAQ,QACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,qCAAqD0H,EAAMrO,IAC/DmC,UAAU,+BAEX,IAAKyG,IAAKlC,IAAI,UAAW,KAE5B,GACJ,OACE,wBACEvE,UAAS,oDACTkC,IAAKgK,EAAM/N,MAEX,wBAAI6B,UAAS,WAAyB,KAAnBkM,EAAMzJ,UAAmB,iBAAmB,GAAlD,cACX,kBAAC,IAAD,CACEN,QAAS,kBAAM,EAAKrC,MAAMoE,kBAAkBgI,EAAMrO,IAAI,IACtD8O,GAAI,SAAWT,EAAMrO,IAErB,yBAAKmC,UAAU,qDACb,yBAAKA,UAAU,kBACb,uBAAGA,UAAU,4BACb,yBAAKA,UAAU,WACb,uBAAGA,UAAU,QAAQkM,EAAMjK,MAC3B,uBAAGjC,UAAU,MAAb,IAAoBkM,EAAM/N,WAMpC,wBAAI6B,UAAU,uBACZ,kBAAC,IAAD,CACEmC,QAAS,kBAAM,EAAKrC,MAAMoE,kBAAkBgI,EAAMrO,IAAI,IACtD8O,GAAI,SAAWT,EAAMrO,IAErB,yBAAKmC,UAAU,0BACb,yBAAKA,UAAU,oBACb,uBAAGA,UAAU,QAAQoI,EAASD,EAAYC,GAAU,IACpD,uBAAGpI,UAAU,QAAQ2I,OAK7B,wBAAI3I,UAAU,sBAAsBsG,MAAM,MACxC,yBAAKtG,UAAU,YACb,uBACEA,UAAU,2CACVqF,KAAK,IACLuH,KAAK,SACLC,cAAY,WACZC,gBAAc,OACdC,gBAAc,SAEd,uBAAG/M,UAAU,6BAEf,yBAAKA,UAAU,qCACb,uBACEkB,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,mCAAmD0H,EAAMrO,IAC7DsE,QAAS,SAAClE,GACR,EAAKqO,eACHrO,EADF,UAEKe,IAAWwF,SAFhB,mCAEmD0H,EAAMrO,GAFzD,8BAEiFqO,EAAMK,gBACrF,iBAGJvM,UAAU,6BAETyG,IAAKlC,IAAI,SAEX6H,EACAI,EACAE,Q,yCAQMR,GAAQ,IAAD,OACpBvD,EAAa,GACbP,EAAS,EH1LkB,GG2L3B8D,EAAMtD,MACRD,EAAalC,IAAKlC,IAAI,UACtB6D,EAAS8D,EAAMrD,aAAeqD,EAAMpD,cAEpCH,EAAalC,IAAKlC,IAAI,SACtB6D,EAAS8D,EAAMpD,YAAcoD,EAAMrD,cAErC,IAAMmE,EAAiBhM,OAAO8E,UAAUmH,gBACtC,uBACE/L,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,kDAAkE0H,EAAMrO,IAC5EmC,UAAU,6BAETyG,IAAKlC,IAAI,wBAGZ,GAEI6H,EAAaF,EAAMG,UACvB,uBACEnL,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,yCAAyD0H,EAAMrO,IACnEsE,QAAS,SAAClE,GACR,EAAKqO,eACHrO,EADF,UAEKe,IAAWwF,SAFhB,yCAEyD0H,EAAMrO,GAF/D,8BAEuFqO,EAAMK,gBAC3F,mBAGJvM,UAAU,+BAETyG,IAAKlC,IAAI,WAGZ,GAEIiI,EAAWN,EAAMO,cACnB,uBACIpH,KAAI,UAAKrG,IAAWwF,SAAhB,uCAAuD0H,EAAMrO,IACjEmC,UAAU,6BAEXyG,IAAKlC,IAAI,SAEZ,GAEEmI,EAAgC,KAAnBR,EAAMzJ,UACrB,uBACIvB,OAAQ,QACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,yCAAyD0H,EAAMrO,IACnEmC,UAAU,+BAEX,IAAKyG,IAAKlC,IAAI,UAAW,KAE5B,GACJ,OACE,wBACEvE,UAAS,mDACTkC,IAAKgK,EAAM/N,MAEX,wBAAI6B,UAAS,WAAyB,KAAnBkM,EAAMzJ,UAAmB,iBAAmB,GAAlD,cACX,kBAAC,IAAD,CAAMkK,GAAI,aAAeT,EAAMrO,IAC7B,yBAAKmC,UAAU,qDACb,yBAAKA,UAAU,kBACb,uBAAGA,UAAU,0BACb,yBAAKA,UAAU,WACb,uBAAGA,UAAU,QAAQkM,EAAMjK,MAC3B,uBAAGjC,UAAU,MAAb,IAAoBkM,EAAM/N,WAMpC,wBAAI6B,UAAU,uBACZ,kBAAC,IAAD,CAAM2M,GAAI,aAAeT,EAAMrO,IAC7B,yBAAKmC,UAAU,0BACb,yBAAKA,UAAU,oBACb,uBAAGA,UAAU,QAAQmI,EAAYC,IACjC,uBAAGpI,UAAU,QAAQ2I,OAK7B,wBAAI3I,UAAU,sBAAsBsG,MAAM,MACxC,yBAAKtG,UAAU,YACb,uBACEA,UAAU,2CACVqF,KAAK,IACLuH,KAAK,SACLC,cAAY,WACZC,gBAAc,OACdC,gBAAc,SAEd,uBAAG/M,UAAU,6BAEf,yBAAKA,UAAU,qCACX4K,EAAsBsB,GAAO,GAE/B,uBACEhL,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,uCAAuD0H,EAAMrO,GAA7D,qBAA4EqO,EAAMK,gBACtFpK,QAAS,SAAClE,GACR,EAAKqO,eACHrO,EADF,UAEKe,IAAWwF,SAFhB,uCAEuD0H,EAAMrO,GAF7D,8BAEqFqO,EAAMK,gBACzF,iBAGJvM,UAAU,6BAETyG,IAAKlC,IAAI,SAEXyI,EACAZ,EACAI,EACAE,Q,yCAQMQ,GACjB,IAAMpB,EAAcnJ,KAAK6I,iBAEvB7I,KAAK7C,MAAMyL,WAAW5I,KAAK6I,mBAC3B7I,KAAK7C,MAAMyL,WAAW5I,KAAK6I,kBAAkBC,aAC3C9I,KAAK+E,MAAM4D,SAEb3I,KAAKuG,SAAS,CACZmC,cAAc,EACdC,QAAS3I,KAAK7C,MAAMyL,WAAW5I,KAAK6I,kBAAkBC,cAG1D9I,KAAK7C,MAAMmM,iBAAiBH,GAC5B,IAAMqB,EAASxK,KAAK7C,MAAMmE,KAAK6H,GAC3BqB,GAAUA,EAAOZ,iBACC5J,KAAK7C,MAAM8C,WAAWuK,EAAOZ,gBAO/C5J,KAAK7C,MAAMsN,cAAc,CACvBD,EAAOE,eAAerJ,MAAM,KAC5BmJ,EAAOZ,iBAPT5J,KAAK7C,MAAMwN,mBACTH,EAAOE,eAAerJ,MAAM,KAC5BmJ,EAAOtP,O,uCAsBb,OAAO8E,KAAK7C,MAAMiM,MAAMC,OAAOjE,OAASpF,KAAK7C,MAAMiM,MAAMC,OAAOjE,QAAU,I,+BAGlE,IAAD,OACDwF,EAASzC,EAAG0C,MAAM7K,KAAK7C,MAAMsF,SAASqI,QACtC3B,EAAcnJ,KAAK6I,iBACrBkC,EAAOC,SAASJ,EAAOG,KAAOH,EAAOG,KAAO,GAC5CE,EAAU,GACZC,EAAU,GACZ,GAAIlL,KAAK7C,MAAMyL,YAAc5I,KAAK7C,MAAMyL,WAAWO,IACI,GAAjDnJ,KAAK7C,MAAMyL,WAAWO,GAAagC,WAAiB,CACtD,IAAMC,EAAapL,KAAK7C,MAAMyL,WAAWO,GACrC4B,EAAOK,EAAWD,aACpBF,EACE,kBAAC,IAAD,CACEzL,QAASQ,KAAKuI,sBACdyB,GAAE,gBAAWb,EAAX,iBAA+B4B,EAAO,GACxC1N,UAAU,6BAEV,uBAAGA,UAAU,wBAKf0N,EAAO,GAAKA,EAAO,EAAIK,EAAWD,aACpCD,EACE,kBAAC,IAAD,CACE1L,QAASQ,KAAKuI,sBACdyB,GAAE,gBAAWb,EAAX,iBAA+B4B,EAAO,GACxC1N,UAAU,6BAEV,uBAAGA,UAAU,uBAOvB2C,KAAK7C,MAAMyC,eAAeuJ,EAAa4B,GACvC/K,KAAK7C,MAAMkO,oBAAoBlC,GAE/B,IAAMmC,OAAgDC,IAAvCvL,KAAK7C,MAAM8C,WAAWkJ,GACjCqC,EAAe,GAEbC,EAAYzL,KAAK7C,MAAM8C,WAAWkJ,GACpCnJ,KAAK7C,MAAM8C,WAAWkJ,GACtB,GACEuC,EAAgB1L,KAAK7C,MAAMuD,eAAeyI,GAC5CnJ,KAAK7C,MAAMuD,eAAeyI,GAC1B,GACEwC,EAAiB,GAkBvB,OAjBKL,EAEMA,GAA+B,IAArBG,EAAUpM,QAAyC,IAAzBqM,EAAcrM,SAC3DmM,EAAe,6BAAM1H,IAAKlC,IAAI,2BAFb,6BAAMkC,IAAKlC,IAAI,eAIlC6J,EAAUvL,SAAQ,SAACqJ,IACM,MAApBA,EAAMzJ,WAAsB,EAAK3C,MAAM+H,qBAG1CyG,EAAelL,KAAK,EAAKmL,eAAerC,OAE1CmC,EAAcxL,SAAQ,SAACqJ,IACE,MAApBA,EAAMzJ,WAAsB,EAAK3C,MAAM+H,qBAG1CyG,EAAelL,KAAK,EAAKoL,mBAAmBtC,OAG5C,yBAAMrO,GAAG,gCAAgCmC,UAAU,0CACjD,kBAAC,EAAD,CACEyO,SAAU3C,EACVnB,IAAKhI,KAAK+E,MAAMiE,UAChB3B,WAAYrH,KAAK+E,MAAMsC,WACvBF,KAAMnH,KAAK+E,MAAMgE,aACjB9B,YAAajH,KAAKiH,cAEpB,yBAAKJ,MAAO,CAAEW,SAAU,aACtB,2BAAOnK,UAAU,2FACf,+BAAQsO,IAER3L,KAAK+E,MAAM2D,eAAiB4C,EAC5B,yBAAKjO,UAAU,aACb,yBAAKA,UAAU,YACb,yBAAKA,UAAU,iBACf,yBAAKA,UAAU,iBACf,yBAAKA,UAAU,iBACf,yBAAKA,UAAU,iBACf,yBAAKA,UAAU,iBACf,yBAAKA,UAAU,mBAInB,IAIHmO,EACD,uBACE9I,KAAI,UAAKrG,IAAWwF,SAAhB,2CAA2DsH,GAC/D3J,QAAS,SAAClE,GACR,EAAKqO,eACHrO,EADF,UAEKe,IAAWwF,SAFhB,oDAEoEsH,GAClE,gBAGJ9L,UAAU,kDAEV,uBAAGA,UAAU,yCACb,8BAAOyG,IAAKlC,IAAI,iBAElB,yBAAKvE,UAAU,oBACZ4N,EACAjL,KAAK7C,MAAMyL,WAAWO,IAC4B,GAAjDnJ,KAAK7C,MAAMyL,WAAWO,GAAagC,YACc,GAAjDnL,KAAK7C,MAAMyL,WAAWO,GAAagC,YACjC,8BACE,kCAAQnL,KAAK7C,MAAMyL,WAAWO,GAAaL,YAA3C,KAAgE,IADlE,KAEI,kCAAQ9I,KAAK7C,MAAMyL,WAAWO,GAAagC,WAA3C,MAGPD,Q,GApcoBtG,IAAMC,WA0c/BkH,EAAOxG,YAAW8C,GAuCTvD,mBAtCf,SAAyBC,GACvB,MAAO,CACLrE,eAAgBqE,EAAMC,gBAAgBtE,eACtCT,WAAY8E,EAAMC,gBAAgB/E,WAClCwB,SAAUsD,EAAMC,gBAAgBvD,SAChCH,KAAMyD,EAAMC,gBAAgB1D,KAC5BsH,WAAY7D,EAAMC,gBAAgB4D,WAClC1D,mBAAoBH,EAAMC,gBAAgBE,mBAC1CsE,eAAezE,EAAMC,gBAAgBwE,mBAIzC,SAA4BrE,GAC1B,MAAO,CACLvF,eAAgB,SAACwF,EAAQ2F,GACvB5F,EAASE,2BAAiBD,EAAQ2F,KAEpCiB,gBAAiB,WACf7G,EAAS6G,8BAEX1C,iBAAkB,SAAClE,GACjBD,EAASmE,2BAAiBlE,GAAQ,KAEpCuF,mBAAoB,SAACsB,EAAiBC,GACpC/G,EAASwF,6BAAmBsB,EAAiBC,KAE/CzB,cAAe,SAACwB,GACd9G,EAASsF,wBAAcwB,KAEzBZ,oBAAqB,SAACa,GACpB/G,EAASkG,8BAAoBa,GAAY,KAE3C3K,kBAAmB,SAACL,GAClBiE,EAAS5D,4BAAkBL,GAAO,QAKzB4D,CAA6CiH,GC7e7CI,E,uKAfX,OACE,kBAAC,IAAD,KACE,kBAAC,IAAD,CAAOC,KAAK,iBACV,kBAAC,EAAD,OAEF,kBAAC,IAAD,CAAOA,KAAK,SACV,kBAAC,EAAD,OAEF,kBAAC,IAAD,CAAOA,KAAK,KACV,kBAAC,EAAD,Y,GAXkBxH,IAAMC,WCI5BwH,E,kDACJ,WAAYlP,GAAQ,IAAD,uBACjB,cAAMA,IAiCRmP,cAAe,EAlCI,EAoCnBC,YAAc,WACZ,EAAKhG,SAAS,CAACiG,OAAQ,EAAKzH,MAAMyH,OAAS,KArC1B,EAwCnBlG,WAAa,WACX,IAAImG,EAAI,eACR,EAAKlG,SAAS,CAAEC,cAAc,IAC9B,IAAIkG,EAAUhO,SAAS8J,eAAe,aAClC3B,EAAQnI,SAASiO,cAAc,SAcnC,GAbE9F,EAAM+F,YAAN,2UAaCF,EAAOG,gBAAiB,CACzB,IAAIC,EAAWJ,EAAOG,gBAAgBE,KAAKpO,cAAc,uCACtDmO,GAAUA,EAASE,iBAAiB,SAAS,WAAcP,EAAKF,iBAEnE,IAAIU,EAAYP,EAAOG,gBAAgBE,KAAKpO,cAAc,wCACvDsO,GAAWA,EAAUD,iBAAiB,SAAS,WAAcP,EAAKlG,SAAS,CAAC2G,kBAAkB,OAE/ER,EAAOG,gBAAgBE,KAAKpO,cAAc,8BAE1D8N,EAAKF,cACLE,EAAKlG,SAAS,CAAC4G,iBAAiB,EAAOD,kBAAkB,KAK7D,IACER,EAAOG,gBAAgBO,KAAKC,YAAYxG,GACzC,MAAOvL,GACNgS,QAAQC,IAAIjS,KA1Ed,IAAImL,EAAK,eAFQ,OAGjB,EAAK1B,MAAQ,CACXyB,cAAc,EACdgG,OAAQ,EACRW,iBAAiB,EACjBK,gBAAiB,KACjBN,kBAAkB,GAEpB7O,OAAOqI,UAAa,SAAUpL,GAC5B,IACE,GAAmB,gBAAhBA,EAAEyG,KAAKkE,KAAwB,CAChC,IAAM+B,EAAM1M,EAAEyG,KAAK0L,QACb1C,EAAO/C,EAAIoB,MAAM,kBAAkB,GACnCC,EAASrB,EAAI3G,MAAM,KAAK,GACxBqM,EAAY,gBAAa3C,EAAb,YAAqB1B,EAArB,6BACdrJ,KAAKsM,aAGPtM,KAAKsM,cAAe,EAFpBjO,OAAOmD,QAAQmM,UAAU,GAAI,GAA7B,UAAqCD,QAIlC,CACO/G,IAASC,YAAYH,GAC7BI,MAAMlJ,OAAQ,gCAIpB,MAAOrC,GACPgS,QAAQC,IAAIjS,KAEd2N,KArBkB,gBAVH,E,gEAiFjBjJ,KAAKsG,aACDjI,OAAOC,a,+BAcX,IAAM6C,EAAYnB,KAAK7C,MAAMiM,MAAMC,OAAO6C,WAC1C,IACE,IACM7C,EADMhL,OAAOoE,SAASC,KACTrB,MAAM,KAAK,GAC9B,QAAakK,GAAVlC,EACD,IAAIqE,EAAY,WAAQrE,GAE3B,MAAM/N,IAEP,QAAqBiQ,IAAjBmC,GAAsD,qBAAjBA,EACnCA,EAAe,uDAIrB,OAFA1N,KAAK4N,cAAgBF,EACrB1N,KAAK7C,MAAMkO,oBAAoBlK,GAE7B,oCASGnB,KAAK+E,MAAMmI,kBAAoB,yBAAK7P,UAAU,UAAS,yBAAKA,UAAU,YAAW,8BAAW,8BAAW,8BAAW,gCAUnH,4BACEkC,IAAKS,KAAK+E,MAAMyH,OAChBtR,GAAG,YACH6M,IAAG,UAAK1L,IAAWwF,SAAhB,uDAAuEV,GAAvE,OAAmFuM,GACtFnQ,MAAM,GACNsJ,MAAO,CAAElJ,OAAQ,QACjBkK,OAAQ7H,KAAKsG,gB,yCAMFuH,EAAWC,EAAWC,GACvC,IAAM5M,EAAYnB,KAAK7C,MAAMiM,MAAMC,OAAO6C,WACrClM,KAAK7C,MAAMsE,SAASN,IACvBnB,KAAK7C,MAAM6Q,eAAe7M,GAE5BnB,KAAK7C,MAAMmM,iBAAiBnI,GAE5B,IAAMqJ,EAASxK,KAAK7C,MAAMsE,SAASN,GAC/BqJ,GAAUA,EAAOZ,iBACC5J,KAAK7C,MAAM8C,WAAWuK,EAAOZ,gBAI/C5J,KAAK7C,MAAMsN,cAAc,CACvBD,EAAOE,eAAerJ,MAAM,KAC5BmJ,EAAOZ,iBAJT5J,KAAK7C,MAAMwN,mBAAmBH,EAAOE,eAAerJ,MAAM,W,GA1JhCuD,IAAMC,WA+LxC,IAAMkH,EAAOxG,YAAW8G,GACTvH,eA3Bf,SAAyBC,GACvB,MAAO,CACLtD,SAAUsD,EAAMC,gBAAgBvD,SAChCxB,WAAY8E,EAAMC,gBAAgB/E,eAItC,SAA4BkF,GAC1B,MAAO,CACLmE,iBAAkB,SAAC4C,GACjB/G,EAASmE,2BAAiB4C,GAAY,KAExC8B,eAAgB,SAAC9B,GACf/G,EAAS6I,yBAAe9B,KAE1Bb,oBAAqB,SAACa,GACpB/G,EAASkG,8BAAoBa,GAAY,KAE3CvB,mBAAoB,SAACsB,EAAiBC,GACpC/G,EAASwF,6BAAmBsB,EAAiBC,KAE/CzB,cAAe,SAACwB,GACd9G,EAASsF,wBAAcwB,QAKdnH,CAA6CiH,GCrMtDkC,I,yDACJ,WAAY9Q,GAAQ,IAAD,8BACjB,cAAMA,IACD4H,MAAQ,CACXgE,cAAc,EACdC,UAAW,GACX3B,WAAY,GACZqB,aAAc,GACdC,QAAS,IAEX,EAAK1B,YAAc,EAAKA,YAAYgC,KAAjB,gBATF,E,2DAYJnI,EAAOkH,EAAKX,GAQzB,OAPAvG,EAAMoI,iBACNlJ,KAAKuG,SAAL,2BACKvG,KAAK+E,OADV,IAEEgE,cAAc,EACdC,UAAWhB,EACXX,WAAYA,MAEP,I,oCAIPrH,KAAKuG,SAAL,2BACKvG,KAAK+E,OADV,IAEEgE,cAAc,O,yCAICwB,GACbvK,KAAK7C,MAAM+Q,oBAAuBlO,KAAK7C,MAAMgR,cAAsD,GAAvCnO,KAAK7C,MAAMiR,qBAAqBlT,GAEtF8E,KAAK7C,MAAMgR,cAAgBnO,KAAK7C,MAAM+Q,oBAC9C7P,OAAOgQ,eAFPrO,KAAK7C,MAAMmR,eAAetO,KAAK7C,MAAMiR,qBAAqBlT,GAAI8E,KAAK7C,MAAMiR,qBAAqBG,U,qCAMnFJ,GACb,IAIIK,EAJAC,EAAO,GAUX,OATGN,EAAY7O,MAAQ6O,EAAY3S,OACjCiT,EAAQ,8BAAM,uBAAG/L,KAAMyL,EAAYM,KAAMC,MAAM,4BAA4BnQ,OAAO,UAAU4P,EAAY7O,KAA1F,KAAkG6O,EAAY3S,QAG3H2S,EAAYQ,iBACbH,EAAW,0BAAMnR,UAAU,sBAAsBuR,aAAYT,EAAYU,YAAY,uBAC7ExR,UAAU,6BAIlB,8BACC8Q,EAAYQ,eACZH,EACAC,K,+BAMK,IACJK,EACArF,EAGAY,EALG,OAMD+D,EAAuBpO,KAAK7C,MAAMiR,qBACpCW,EAA2B,GAC3BC,EAAaZ,EAAqBG,OAAS,eAAiB,mBAChEO,EAAa,IACmB,GAA5BV,EAAqBlT,KACnBkT,EAAqBG,QACvBQ,EAA2B/O,KAAK7C,MAAMmE,KAAK8M,EAAqBlT,IAC5D8E,KAAK7C,MAAMmE,KAAK8M,EAAqBlT,IACrC,GACJ4T,EACE,yBAAKzR,UAAU,iCACb,yBACEA,UAAU,iBACV4M,KAAK,QACL1C,aAAW,cAEX,uBACEhJ,OAAO,SACPmE,KAAI,UAAKrG,IAAWwF,SAAhB,+FAA+GkN,EAAyB7T,GAAxI,wDACJmC,UAAU,qBAEV,uBAAGA,UAAU,0BAMrB0R,EAA2B/O,KAAK7C,MAAMsE,SAAS2M,EAAqBlT,IAChE8E,KAAK7C,MAAMsE,SAAS2M,EAAqBlT,IACzC,GACJ4T,EACE,yBAAKzR,UAAU,iCACb,yBACEA,UAAU,iBACV4M,KAAK,QACL1C,aAAW,cAEX,uBACEhJ,OAAO,SACPmE,KAAI,UAAKrG,IAAWwF,SAAhB,mHAAmIkN,EAAyB7T,GAA5J,oCACJmC,UAAU,qBAEV,uBAAGA,UAAU,uBAGjB,uBACEqF,KAAI,UAAKrG,IAAWwF,SAAhB,iDAAiEkN,EAAyB7T,IAC9FA,GAAG,WACHqD,OAAO,SACP0Q,YAAU,sBACV5R,UAAU,uDAEV,8BACE,uBAAGA,UAAU,eACZyG,IAAKlC,IAAI,kBAKlByI,EAAiBhM,OAAO8E,UAAUmH,gBAChC,uBACE/L,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,kDAAkEkN,EAAyB7T,IAC/FmC,UAAU,6BAET,IAAKyG,IAAKlC,IAAI,uBAAwB,KAGzC,IAGJ6H,EAAasF,EAAyBrF,UAClC,uBACInL,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,iBAAiCmN,EAAjC,mBAAqDD,EAAyB7T,IAClFsE,QAAS,SAAClE,GACR,EAAKqO,eACDrO,EADJ,UAEOe,IAAWwF,SAFlB,iBAEmCmN,EAFnC,mBAEuDD,EAAyB7T,GAFhF,8BAEwG6T,EAAyBnF,gBAC7H,mBAGNvM,UAAU,+BAEX,IAAKyG,IAAKlC,IAAI,UAAW,KAG5B,IAGN,IAAIsN,EAAa,CAAEzJ,OAAQ,GAAIO,WAAY,IACtC+I,EAAyBzP,MAM5B4P,EAAanJ,EAAqBgJ,GAClCrQ,SAASnB,MAAQuG,IAAKlC,IAAI,qBAAuB,KAAOmN,EAAyBzP,MANjFyP,EAA2B,CACzBzP,KAAMwE,IAAKlC,IAAI,qBACfpG,KAAM,IAQV,IAAM2T,EACJ,uBACE5Q,OAAQ,SACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,iBAAiCmN,EAAjC,iBAAmDD,EAAyB7T,GAA5E,qBAA2F6T,EAAyBnF,gBACxHpK,QAAS,SAAClE,GACR,EAAKqO,eACHrO,EADF,UAEKe,IAAWwF,SAFhB,iBAEiCmN,EAFjC,iBAEmDD,EAAyB7T,GAF5E,8BAEoG6T,EAAyBnF,gBAC3H,iBAGJvM,UAAU,6BAET,IAAKyG,IAAKlC,IAAI,QAAS,KAGtBwN,EAAkD,KAAtCL,EAAyBjP,UACvC,uBACIvB,OAAQ,QACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,iBAAiCmN,EAAjC,mBAAqDD,EAAyB7T,GAA9E,qBAA6F6T,EAAyBnF,gBAC1HvM,UAAU,+BAEX,IAAKyG,IAAKlC,IAAI,UAAW,KAE5B,GACEyN,EAAUN,EAAyBjF,cACvC,uBACEvL,OAAQ,QACRmE,KAAI,UAAKrG,IAAWwF,SAAhB,iBAAiCmN,EAAjC,iBAAmDD,EAAyB7T,GAA5E,qBAA2F6T,EAAyBnF,gBACxHvM,UAAU,6BAET,IAAKyG,IAAKlC,IAAI,QAAS,KAExB,GACA0N,EAAe,GAChBtP,KAAK7C,MAAMgR,cACZmB,EAAa7O,KAAKT,KAAKuP,eAAevP,KAAK7C,MAAMgR,YAAYqB,gBAC7DxP,KAAK7C,MAAMgR,YAAYsB,cAAcvP,SAAQ,SAACwP,GAC5CJ,EAAa7O,KAAK,EAAK8O,eAAeG,QAG1C,IAAMC,EAA6BtR,OAAO8E,UAAUyM,0BAA6B,yBAAKvS,UAAU,mBAC9F,2BAAOA,UAAU,mBAAmB4I,KAAK,WAAWgE,KAAK,SAAS/O,GAAG,aAAasE,QAAS,WACzF,EAAKrC,MAAM0S,8BAEb,2BAAOxS,UAAU,mBAAmByS,QAAQ,cAAchM,IAAKlC,IAAI,iBAC3D,GACV,OACE,6BACE,yBAAKvE,UAAU,aAAawJ,MAAO,CAAEkJ,OAAQ,IAC7C,kBAAC,EAAD,CACE/H,IAAKhI,KAAK+E,MAAMiE,UAChB3B,WAAYrH,KAAK+E,MAAMsC,WACvBF,KAAMnH,KAAK+E,MAAMgE,aACjB9B,YAAajH,KAAKiH,cAElB,yBAAK5J,UAAU,mBACb,yBAAKA,UAAU,0BACb,yBAAKA,UAAU,YACb,yBAAKA,UAAU,0BACb,yBAAKA,UAAU,uBACb,yBAAKA,UAAU,oBACb,4BACG0R,EAAyBzP,KAAM,IAChC,0BAAMjC,UAAU,SACb0R,EAAyBvT,KACtB,IAAMuT,EAAyBvT,KAC/B,IAGLuT,EAAyBvT,MAC1B,yBAAK6B,UAAU,sBACb,yBAAKA,UAAU,gBAAiBwJ,MAAO,CAACW,SAAU,WAAYwI,UAAW,gCAAiCtI,IAAK,MAAOuI,KAAM,QACxHhI,EAAsB8G,EAA0BX,EAAqBG,QACtEY,EACAC,EACAC,EACAhF,EACAZ,GAEH,4BAAQxD,KAAK,SAAS5I,UAAU,iDAAiD6M,cAAY,WAAWC,gBAAc,OAAOC,gBAAc,QACvI,0BAAM/M,UAAU,WAAhB,mBACA,uBAAGA,UAAU,mBAAmBwJ,MAAO,CAACqJ,MAAO,uBAKvD,yBAAK7S,UAAU,eACViS,OAMb,yBAAKjS,UAAU,uBACZsS,EACD,yBAAKtS,UAAU,sDACb,yBACIA,UAAU,uCACVwJ,MAAO,CACP,YAAgBqI,EAAWzJ,OAAO0K,WAAW,KAEzC,UADA,YAIN,uBAAG9S,UAAU,QAAQ6R,EAAWzJ,QAChC,uBAAGpI,UAAU,QAAQ6R,EAAWlJ,cAGnC8I,W,GArRMlK,IAAMC,YAyTdC,oBAff,SAAyBC,GACvB,IAAIoJ,EAMJ,OAJGA,EADApJ,EAAMC,gBAAgBC,cAAcsJ,OACtBxJ,EAAMC,gBAAgBoL,gBAAgBrL,EAAMC,gBAAgBC,cAAc/J,IAE1E6J,EAAMC,gBAAgBqL,oBAAoBtL,EAAMC,gBAAgBC,cAAc/J,IAExF,CACLkT,qBAAsBrJ,EAAMC,gBAAgBC,cAC5CxD,SAAUsD,EAAMC,gBAAgBvD,SAChCH,KAAMyD,EAAMC,gBAAgB1D,KAC5B6M,YAAaA,EACbD,mBAAoBnJ,EAAMC,gBAAgBkJ,uBAvB9C,SAA4B/I,GAC1B,MAAO,CACLmJ,eAAgB,SAACpN,EAAMqN,GACrBpJ,EAASmJ,yBAAepN,EAAOqN,KAEjCsB,yBAA0B,WACtB1K,EAAS0K,0CAoBF/K,CAA4CS,YAAW0I,KCvShEqC,GAAYlI,OAAQ,YAAY/J,OAAO8E,UAAUoN,KAA9B,UAEnBC,G,kDAEJ,WAAYrT,GAAQ,IAAD,uBACjB,cAAMA,IAFR4H,MAAQ,CAAE0L,UAAU,GAGlB,IAAIC,EAAI,eAFS,OAGjBrS,OAAO2O,iBACL,wBACA,SAAU1R,GACR,IAAMiD,EAASoS,OAAOrV,EAAEsV,QAAQC,WAAW,8BAA+B,IAC1EH,EAAKvT,MAAMqE,QAAQf,KAAKlC,MAE1B,GAECF,OAAO8E,UAAU2N,cAClB,EAAK3T,MAAM4T,cAZI,E,gEAgBE,IAAD,OAClB/Q,KAAKgR,cAAclP,MAAK,WACtB,EAAKyE,SAAS,CAAEkK,UAAU,S,oCAK5B,OAAO3M,IAAKmN,KAAK,CACfC,cAAe7S,OAAO8E,UAAUoN,KAChCY,QAAQ,eACL9S,OAAO8E,UAAUoN,KAAOD,Q,wCAKb9N,GAChB1G,IACG8F,IADH,UAEOvF,IAAWwF,SAFlB,wDAE0EW,EAAOL,QAE9EL,MAAK,WACJzD,OAAOoE,SAASC,KAAOrE,OAAOoE,SAASC,QAExCL,OAAM,SAACC,GACNC,MAAM,gD,+BAGF,IAAD,OACHI,EAAkB,GACtB,IACEA,EAAkB3C,KAAK7C,MAAMyF,SAAS7H,KAAI,SAACyH,GACzC,MAAO,CAAEL,MAAOK,EAAOtH,GAAIsC,MAAOgF,EAAOlD,SAE3C,MAAOhE,GACPqH,EAAkB,GAEpB,IAAMyO,EAAoB/S,OAAO8E,UAAUkO,yBACzC,yBAAKhU,UAAU,uCACb,2BAAOwJ,MAAO,CAAEyK,QAAS,WAAaxN,IAAKlC,IAAI,oBAC/C,kBAAC,IAAD,CACEI,QAASW,EACTU,YAAa,gBACbC,SAAU,SAACnB,GAAD,OAAW,EAAKoP,kBAAkBpP,IAC5CA,MAAO9D,OAAO8E,UAAUiO,qBAI5B,GAGII,EACkB,KAAtBJ,EACE,yBAAK/T,UAAU,6CACb,yBAAKA,UAAU,uCACZ+T,IAIL,GAEJ,OACEpR,KAAK+E,MAAM0L,UACT,6BACE,kBAAC,GAAD,MACA,yBAAKpT,UAAU,+BACb,yBAAKA,UAAU,yBACb,kBAAC,EAAD,MACA,yBAAKA,UAAU,iDACZmU,EACD,kBAAC,IAAD,KACE,kBAAC,IAAD,CAAOpF,KAAK,SACV,kBAAC,EAAD,OAEF,kBAAC,IAAD,CAAOA,KAAM,yBACX,kBAAC,EAAD,OAEF,kBAAC,IAAD,CAAOA,KAAK,KACV,kBAAC,EAAD,gB,GA/FFxH,IAAMC,WAwHTC,oBAbf,SAAyBC,GACvB,MAAO,CACLnC,SAAUmC,EAAMC,gBAAgBpC,aAIpC,SAA4BuC,GAC1B,MAAO,CACL4L,YAAa,WACX5L,EAAS4L,6BAIAjM,CAA6CS,YAAWiL,KCzInDiB,QACW,cAA7BpT,OAAOoE,SAASiP,UAEe,UAA7BrT,OAAOoE,SAASiP,UAEhBrT,OAAOoE,SAASiP,SAAStI,MACvB,2D,sBCfN,SAASuI,GAAmB9R,GAC1B,OAAQA,EAAQP,KAAR,YAAoBO,EAAQrE,MAG/B,SAASoW,GAAoBC,EAAiB3J,EAAO5G,EAAMG,GAChE,IAAwB,GAApBoQ,EAAJ,CAMA,IACI5M,EAeA6M,EAiBiBC,EAjCjBC,EAAK,GAiBT,GAfI9J,GACFjD,EAAgB3D,EAAKuQ,KAEnBG,EAAGvR,KAAK,CAAEgO,KAAK,oCAAD,OAAsCoD,GAAmBtU,MAAOoU,GAAmB1M,MAGnGA,EAAgBxD,EAASoQ,KAEvBG,EAAGvR,KAAK,CACNgO,KAAK,wCAAD,OAA0CoD,GAC9CtU,MAAOoU,GAAmB1M,KAK5BA,EACF,UAAgEsG,KAAxDuG,EAAgBxQ,EAAK2D,EAAc2E,mBAexBmI,EAdCD,GAea,IAA5BpW,OAAOD,KAAKsW,GAAK1S,QAAgB0S,EAAIE,cAAgBvW,SAfnBoW,EAAc5W,KAGnD8W,EAAGE,QAAQ,CACTzD,KAAK,oCAAD,OAAsCqD,EAAc5W,IACxDqC,MAAOoU,GAAmBG,KAE5B7M,EAAgB6M,EAGpBE,EAAGE,QAAQ,CAAEzD,KAAM,sCAAuClR,MAAOuG,IAAKlC,IAAI,uBAC1EvD,OAAO8T,kBAAkBH,QAnCvB3T,OAAO8T,kBAAkB,CACvB,CAAE1D,KAAM,sCAAuClR,MAAOuG,IAAKlC,IAAI,wB,ICY7DwQ,GAAsBhK,EAAQ,GAA9BgK,kBAEFC,GAAe,CACnBnN,oBAAoB,EACpB0D,WAAY,GACZhG,SAAU,GACVC,aAAc,GACd5C,WAAY,GACZS,eAAgB,GAChBe,SAAU,GACVH,KAAM,GACNpC,iBAAkB,GAClB+F,cAAe,CAAEsJ,QAAQ,EAAMrT,IAAK,GACpCoX,uBAAwB,GACxBC,gBAAiB,GACjBrE,oBAAoB,EACpBmC,oBAAqB,GACrBD,gBAAgB,GAChBoC,yBAAyB,GCtCZC,gBAAgB,CAC7BzN,gBDwC6B,WAAmC,IAAlCD,EAAiC,uDAAzBsN,GAAcK,EAAW,uCAC/D,OAAQA,EAAOzM,MACb,KAAK0M,uBACH,OAAO,2BACF5N,GADL,IAEEmJ,oBAAoB,IAGxB,KAAK0E,mBACH,OAAGF,EAAOG,QAAQ3K,MACT,2BACFnD,GADL,IAEEmJ,oBAAoB,EACpBkC,gBAAgB,2BACXrL,EAAMqL,iBADI,kBAEZsC,EAAOG,QAAQ1R,UAAYuR,EAAOG,QAAQ9Q,SAIxC,2BACFgD,GADL,IAEEmJ,oBAAoB,EACpBmC,oBAAoB,2BACftL,EAAMsL,qBADQ,kBAEhBqC,EAAOG,QAAQ1R,UAAYuR,EAAOG,QAAQ9Q,SAKnD,KAAKqQ,GACH,OAAO,2BACFrN,GADL,IAEElC,aAAc,KAGlB,KAAKiQ,qBACH,IAAIhH,EAAW4G,EAAOG,QAAQE,UAC1BC,EAASN,EAAOG,QAAQI,eAAelY,KAAI,SAACwO,GAC9C,OAAOA,EAAM2J,kBAEf,OAAO,2BACFnO,GADL,IAEErE,eAAe,2BACVqE,EAAMrE,gBADG,kBAEXoL,EAAWkH,IAEd1R,KAAK,2BACAyD,EAAMzD,MADP,kBAEDwK,EAFC,2BAEe/G,EAAMzD,KAAKwK,IAF1B,IAEqCqH,iBAAiB,QAI9D,KAAKC,iBACCtH,EAAW4G,EAAOG,QAAQE,UAC1BC,EAASN,EAAOG,QAAQQ,WAAWtY,KAAI,SAACwO,GAC1C,OAAOA,EAAM+J,cAGf,OAAO,2BACFvO,GADL,IAEE9E,WAAW,2BACN8E,EAAM9E,YADD,kBAEP6L,EAAWkH,IAEd1R,KAAK,2BACAyD,EAAMzD,MADP,kBAEDwK,EAFC,2BAEe/G,EAAMzD,KAAKwK,IAF1B,IAEqCqH,iBAAiB,QAI9D,KAAKI,eACH,IAAMC,EAAcd,EAAOG,QACvBY,EAAqB,GACzB,IAAK,IAAIC,KAAQF,EACfC,EAAmBD,EAAYE,GAAMR,eAAehY,IAClDsY,EAAYE,GAAMR,eAEtB,OAAO,2BACFnO,GADL,IAEEtD,SAAS,2BAAMsD,EAAMtD,UAAagS,KAGtC,KAAKE,WACH,IAAMC,EAAUlB,EAAOG,QACnBgB,EAAiB,GACrB,IAAK,IAAIH,KAAQE,EACfC,EAAeD,EAAQF,GAAMJ,WAAWpY,IAAM0Y,EAAQF,GAAMJ,WAE9D,OAAO,2BACFvO,GADL,IAEEzD,KAAK,2BAAMyD,EAAMzD,MAASuS,KAG9B,KAAKC,uBACH,IAAMtK,EAAiBkJ,EAAOG,QAC9B,OAAO,2BACF9N,GADL,IAEEyE,eAAgBA,IAGpB,KAAKuK,qBACH,OAAO,2BACFhP,GADL,IAEEE,cAAc,eACT,CACDsJ,OAAQmE,EAAOG,QAAQtE,OACvBrT,GAAI8P,SAAS0H,EAAOG,QAAQ3G,gBAKpC,KAAK8H,mBACH,OAAO,2BACFjP,GADL,IAEEtD,SAAS,2BACJsD,EAAMtD,UADH,kBAELiR,EAAOG,QAAQ3X,GAFV,eAEoBwX,EAAOG,QAAQ9Q,UAG/C,KAAKkS,eACH,OAAO,2BACFlP,GADL,IAEEzD,KAAK,2BACAyD,EAAMzD,MADP,kBAEDoR,EAAOG,QAAQ3X,GAFd,eAEwBwX,EAAOG,QAAQ9Q,UAI/C,KAAKmS,8BACD,OAAO,2BACAnP,GADP,IAEIG,oBAAqBH,EAAMG,qBAGnC,KAAKiP,yBACH,OAAO,2BACFpP,GADL,IAEEwN,gBAAgB,2BACXxN,EAAMwN,iBADI,kBAEZG,EAAOG,QAAQ1S,IAAMuS,EAAOG,QAAQ9H,SAI3C,KAAKqJ,sBACH,IAAIC,EAAgB,GAChBC,EAAoB,GACpBC,EAAe,GACfC,EAAmB,GACvB,IAAK,IAAIrU,KAAOuS,EAAOG,QAAQvR,KAAM,CAKnC,IAAK,IAAImT,KAJTJ,EAAa,2BACRA,GADQ,kBAEVlU,EAFU,aAEAuS,EAAOG,QAAQvR,KAAKnB,MAEZuS,EAAOG,QAAQvR,KAAKnB,GAAM,CAC7C,IAAIoJ,EAAQmJ,EAAOG,QAAQvR,KAAKnB,GAAKsU,GACrCF,EAAahL,EAAMrO,IAAnB,eAA8BqO,GAMhC,IAAK,IAAIkL,KAJTH,EAAiB,2BACZA,GADY,kBAEdnU,EAFc,aAEJuS,EAAOG,QAAQpR,SAAStB,MAEhBuS,EAAOG,QAAQpR,SAAStB,GAAM,CACjD,IAAIoJ,EAAQmJ,EAAOG,QAAQpR,SAAStB,GAAKsU,GACzCD,EAAiBjL,EAAMrO,IAAvB,eAAkCqO,IAGtC,OAAO,2BACFxE,GADL,IAEErE,eAAe,2BACVqE,EAAMrE,gBACN4T,GAELrU,WAAW,2BACN8E,EAAM9E,YACNoU,GAEL/S,KAAK,2BACAyD,EAAMzD,MACNiT,GAEL9S,SAAS,2BACJsD,EAAMtD,UACN+S,KAIT,KAAKE,iBACH,IAAIC,EAAkB,GAItB,OAHAjC,EAAOG,QAAQ3S,SAAQ,SAAC0U,GACtBD,EAAgBC,IAAM,KAEjB,2BACF7P,GADL,IAEE7F,iBAAiB,2BAAM6F,EAAM7F,kBAAqByV,KAGtD,KAAKE,eACH,IAAMjS,EAAW8P,EAAOG,QAAQiC,QAAO,SAACtS,GACtC,GAA4B,GAAxBA,EAAOuS,OAAOzP,QAAuC,GAAxB9C,EAAOuS,OAAOzP,OAC7C,OAAO9C,EAAOuS,UAGZC,EAAepS,EAAS7H,KAAI,SAAAyH,GAAM,OAAIA,EAAOuS,UACnD,OAAO,2BACFhQ,GADL,IAEEnC,SAAS,aAAKoS,KAGlB,KAAKC,sBACH,OAAO,2BACFlQ,GADL,IAEE7F,iBAAiB,2BACZ6F,EAAM7F,kBADK,kBAEbwT,EAAOG,QAAQ3G,WAAawG,EAAOG,QAAQvN,WAIlD,KAAK4P,wBAOH,OANAtD,GACEc,EAAOG,QAAQ3G,WACfwG,EAAOG,QAAQtE,OACfxJ,EAAMzD,KACNyD,EAAMtD,UAEDsD,EACT,KAAKoQ,2BAEH,OAAO,2BACFpQ,GADL,IAEEuN,uBAAuB,2BAClBvN,EAAMuN,wBADW,kBAEnBI,EAAOG,QAAQuC,KAAK,MAAO,MAGlC,KAAKC,iBACH,OAAO,2BACFtQ,GADL,IAEE6D,WAAW,2BACN7D,EAAM6D,YADD,kBAEP8J,EAAOG,QAAQ3R,MAAQwR,EAAOG,QAAQjK,eAG7C,QACE,OAAO7D,M,SElRAuQ,GAAQC,aAAYC,GAAaC,aAAgBC,OAC9D/O,IAASgP,OACP,kBAAC,IAAD,CAAQC,SAAUvZ,IAAWwZ,aAC3B,kBAAC,IAAD,CAAUP,MAAOA,IACf,kBAAC,GAAD,QAGJ5W,SAAS8J,eAAe,SJgHpB,kBAAmBsN,WACrBA,UAAUC,cAAcC,MACrBlU,MAAK,SAAAmU,GACJA,EAAaC,gBAEd7T,OAAM,SAAA8T,GACL7I,QAAQ6I,MAAMA,EAAMC,a,g7EKrIfhE,EAAoB,oBACpBiE,EAAe,eACfvD,EAAqB,qBACrBM,EAAiB,iBACjBG,EAAe,eACfI,EAAW,WACXG,EAAuB,uBACvBC,EAAqB,qBACrBE,EAAe,eACfD,EAAmB,mBACnBG,EAAyB,yBACzBO,EAAiB,iBACjBN,EAAsB,sBACtBS,EAAe,eACfI,EAAsB,sBACtBC,EAAwB,wBACxBC,EAA2B,2BAC3BE,EAAiB,iBACjBzC,EAAmB,mBACnBD,EAAuB,uBAEvBuB,EAA8B,8BAEpC,SAASlI,IACd,MAAO,CAAE/F,KAAMmM,GAEV,SAASkE,IACd,MAAO,CAAErQ,KAAMoQ,GAGV,SAASnI,IACd,MAAO,CAACjI,KAAM0M,GAGT,SAAS4D,EAAepV,EAAUqV,GAA6B,IAAhBtO,EAAe,wDACnE,MAAO,CACLjC,KAAM2M,EACNC,QAAS,CAAC1R,YAAW+G,QAAOnG,KAAMyU,IAI/B,SAASC,EAAiB3K,EAAUJ,GACzC,MAAO,CACLzF,KAAM6M,EACND,QAAS,CAAEE,UAAWjH,EAAUmH,eAAgBvH,IAI7C,SAASgL,EAAa5K,EAAUL,GACrC,MAAO,CACLxF,KAAMmN,EACNP,QAAS,CAAEE,UAAWjH,EAAUuH,WAAY5H,IAIzC,SAASkL,EAAQrV,GACtB,MAAO,CAAE2E,KAAM0N,EAAUd,QAASvR,GAG7B,SAASsV,EAAiBC,GAC/B,MAAO,CAAE5Q,KAAM6N,EAAsBjB,QAASgE,GAGzC,SAASC,EAAYrV,GAC1B,MAAO,CAAEwE,KAAMsN,EAAcV,QAASpR,GAGjC,SAAS6H,EAAiB4C,EAAYhE,GAC3C,MAAO,CACLjC,KAAM8N,EACNlB,QAAS,CAAEtE,OAAQrG,EAAOgE,WAAYA,IAInC,SAAS6K,EAAchE,EAAWhI,GACvC,OAAOjP,IAAS8F,IACdvF,IAAWwF,SAAX,iCAAgDkR,EAAhD,iBAAkEhI,IAI/D,SAASiM,EAAqB9V,EAAO6J,GAC1C,MAAO,CACL9E,KAAMkO,EACNtB,QAAS,CAAE1S,IAAKe,EAAO6J,KAAMA,IAI1B,SAASkM,EAAW7R,EAAQ8R,GACjC,MAAO,CACLjR,KAAMgO,EACNpB,QAAS,CAAE3X,GAAIkK,EAAQrD,KAAMmV,IAI1B,SAASC,EAAejL,EAAYkL,GACzC,MAAO,CACLnR,KAAM+N,EACNnB,QAAS,CAAE3X,GAAIgR,EAAYnK,KAAMqV,IAI9B,SAASC,EAAiBnL,GAC/B,OAAOpQ,IAAS8F,IAAT,UACFvF,IAAWwF,SADT,iCAC0CqK,IAI5C,SAAS8B,EAAe9B,GAC7B,OAAO,SAAC/G,GACN,OAAOkS,EAAiBnL,GAAYpK,MAAK,SAACwV,GACxCnS,EAASgS,EAAejL,EAAYoL,EAAIvV,KAAKA,KAAKmR,qBAKjD,SAASzI,EAAc8M,GAC5B,MAAO,CAAEtR,KAAMyO,EAAgB7B,QAAS0E,GAEnC,SAASC,EAAoBC,GAClC,MAAO,CAAExR,KAAMkP,EAA0BtC,QAAS4E,GAG7C,SAAS5H,IACd,MAAO,CAAE5J,KAAMiO,GAEV,SAASvJ,IAGb,IAFD+M,EAEA,uDAFiB,GACjBC,EACA,uDADuB,KAEjBC,EAActC,QAAMuC,WAAW7S,gBACrC,OAAI4S,EAAYtF,uBAAuBoF,EAAetC,KAAK,MAClD,CAAEnP,KAAM,MAEV,SAACd,GAEN,OADAA,EAASqS,EAAoBE,IACtB5b,IACJ8F,IADI,UACGvF,IAAWwF,SADd,2BACiD,CACpDwH,OAAQ,CACNyO,YAAaJ,KAGhB5V,MAAK,SAACwV,GACLnS,EACE4S,EAAkB,CAChBtW,SAAU6V,EAAIvV,KAAKN,SACnBH,KAAMgW,EAAIvV,KAAKT,KACf0W,QAASV,EAAIvV,KAAKiW,WAGtB,IAAMrD,EAAkBgD,EAAoB,sBACpCD,GADoC,CACpBC,IACpBD,EACJvS,EAASsF,EAAckK,QAKxB,SAASsD,EAAYrV,GAC1B,MAAO,CAAEqD,KAAM4O,EAAchC,QAASjQ,GAIjC,SAAS0L,EAAenN,EAAW+G,GACxC,OAAO,SAAC/C,GACNA,EAAS+I,KACTpS,IAAS8F,IAAT,UAAgBvF,IAAWwF,SAA3B,6BAAwDV,EAAxD,mCAA4F+G,EAAQ,EAAI,IAAKpG,MAAK,SAACwV,GACjHnS,EAASoR,EAAepV,EAAWmW,EAAIvV,KAAMmG,QAK5C,SAAS6I,IACd,OAAO,SAAC5L,GACNrJ,IAAS8F,IAAT,UAAgBvF,IAAWwF,SAA3B,mBAAqDC,MAAK,SAACwV,GACzD,IAAIY,EAAkB,GACtB,IACKZ,EAAIvV,KAAKA,KAAK1C,OAAS,IACxB6Y,EAAe,CACb,CAAEnD,OAAQ,CAAE7Z,IAAK,EAAGoE,KAAMwE,IAAKlC,IAAI,gBAAiB0D,OAAQ,KAD/C,mBAEVgS,EAAIvV,KAAKA,QAGhB,MAAOzG,IAGT6J,EAAS8S,EAAYC,QAKpB,SAASH,IAEb,IADDhW,EACA,uDADO,CAAEN,SAAU,GAAIH,KAAM,GAAI0W,QAAS,IAE1C,MAAO,CACL/R,KAAMmO,EACNvB,QAAS,CACPpR,SAAUM,EAAKN,SACfH,KAAMS,EAAKT,KACX0W,QAASjW,EAAKiW,UAMb,SAASzW,GAAkBL,EAAOiX,GACvC,MAAO,CACLlS,KAAMgP,EACNpC,QAAS,CAAE3G,WAAYhL,EAAOoE,OAAQ6S,IAInC,SAASC,GAAclX,EAAOkK,GACnC,MAAO,CACLnF,KAAMoP,EACNxC,QAAS,CAAE3R,MAAOA,EAAO0H,WAAYwC,IAIlC,SAAS/F,GAAiBnE,GAAkB,IAAX6J,EAAU,uDAAH,EACvC6M,EAActC,QAAMuC,WAAW7S,gBACrC,OAAI4S,EAAYrF,gBAAgBrR,IAAU6J,EACjC,CAAE9E,KAAM,MAEV,SAACd,GAEN,OADAA,EAAS6R,EAAqB9V,EAAO6J,IAC9BgM,EAAc7V,EAAO6J,GAAMjJ,MAAK,SAACwV,GACtCnS,EAASsR,EAAiBvV,EAAOoW,EAAIvV,KAAKN,WAC1C0D,EAASuR,EAAaxV,EAAOoW,EAAIvV,KAAKT,OACtC6D,EAASwR,EAAQW,EAAIvV,KAAKT,OAC1B6D,EAASyR,EAAiBU,EAAIvV,KAAKyQ,0BACnCrN,EAAS2R,EAAYQ,EAAIvV,KAAKN,WAC9B0D,EACE8R,EACE/V,IACAoW,EAAIvV,KAAKsW,gBAAiBf,EAAIvV,KAAKsW,eAAe/E,aAGtDnO,EAASiT,GAAclX,EAAOoW,EAAIvV,KAAK6G,iBAKtC,SAASyC,GAAoBlK,EAAW+G,GAC7C,MAAO,CACLjC,KAAMiP,EACNrC,QAAS,CAAE3G,WAAY/K,EAAWoN,OAAQrG,M", "file": "static/js/main.a2fed7bc.chunk.js", "sourcesContent": ["var map = {\n\t\"./ar.json\": 154,\n\t\"./en.json\": 155\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 153;", "import axios from \"axios\";\r\n// Add a request interceptor\r\nconst appAxios = axios.create();\r\n// appAxios.defaults.headers.common[\"APIKEY\"] =\r\n//   \"1fd260ae35b5f0e78a6f3747efe455cadbccb0df\";\r\n// Add a request interceptor\r\nappAxios.interceptors.request.use(function (config) {\r\n  // Do something before request is sent\r\n  return config;\r\n});\r\n\r\nexport default appAxios;\r\n", "const APP_CONFIG = {};\nif (process.env.NODE_ENV === \"development\") {\n  APP_CONFIG.BASE_URL = \"https://oidev.daftra.local/\";\n  APP_CONFIG.LINK_PREFIX = \"/v2/owner/chart-of-accounts\";\n} else {\n  APP_CONFIG.BASE_URL = \"/\";\n  APP_CONFIG.LINK_PREFIX = \"/v2/owner/chart-of-accounts\";\n}\n\nAPP_CONFIG.child_limit = 100;\n\nexport { APP_CONFIG };\n", "import React from \"react\";\r\nimport TreeView from \"@material-ui/lab/TreeView\";\r\nimport Select from \"react-select\";\r\n\r\nimport TreeItem from \"@material-ui/lab/TreeItem\";\r\nimport PropTypes from \"prop-types\";\r\nimport { makeStyles, fade, withStyles } from \"@material-ui/core/styles\";\r\nimport Typography from \"@material-ui/core/Typography\";\r\nimport { connect } from \"react-redux\";\r\nimport { getChildAccounts, setExpandedStatus } from \"../../actions/accounts\";\r\nimport appAxios from \"../../utils/app-axios\";\r\nimport { withRouter } from \"react-router-dom\";\r\nimport AsyncSelect from \"react-select/async\";\r\nimport { APP_CONFIG } from \"../../config/config\";\r\nimport intl from \"react-intl-universal\";\r\n\r\nconst StyledTreeItem = withStyles((theme) => ({\r\n  iconContainer: {\r\n    \"& .close\": {\r\n      opacity: 0.3,\r\n    },\r\n  },\r\n  group: {\r\n    marginLeft: 7,\r\n    paddingLeft: 18,\r\n    borderLeft: `1px dashed ${fade(theme.palette.text.primary, 0.4)}`,\r\n  },\r\n}))((props) => {\r\n  return <TreeItem {...props} className={`${props.isHidden == \"1\" ? 'hidden-account' : ''}`} title={`#${props.code} ${props.label}`} />;\r\n});\r\n\r\nStyledTreeItem.propTypes = {\r\n  bgColor: PropTypes.string,\r\n  color: PropTypes.string,\r\n  // labelIcon: PropTypes.elementType.opt,\r\n  labelInfo: PropTypes.string,\r\n  // labelText: PropTypes.string.isRequired,\r\n};\r\n\r\nconst useStyles = makeStyles({\r\n  root: {\r\n    height: 264,\r\n    flexGrow: 1,\r\n    maxWidth: 400,\r\n  },\r\n});\r\n\r\nfunction EmptyIcon(props) {\r\n  return <i></i>;\r\n}\r\n\r\nfunction FolderOpened(props) {\r\n  return <i className=\"fas fa-folder-open\"></i>;\r\n}\r\n\r\nfunction FileIcon(props) {\r\n  return <i className=\"far fa-file\"></i>;\r\n}\r\n\r\nfunction Folder(props) {\r\n  return <i className=\"fas fa-folder\"></i>;\r\n}\r\n\r\nfunction Loading(props) {\r\n  return <i className=\"fas fa-spinner fa-spin\"></i>;\r\n}\r\nclass AccountsTree extends React.Component {\r\n  rootId = -1;\r\n  constructor(props) {\r\n    super(props);\r\n    this.props.getCatAccounts(-1);\r\n  }\r\n  componentDidMount() {}\r\n\r\n  handleNodeClick = (e) => {\r\n    if (\r\n      window.innerWidth <= 991 &&\r\n      e.target.classList.contains(\"MuiTreeItem-label\")\r\n    ) {\r\n      document\r\n        .querySelector(\".chart-of-accounts-col-9-filters-row\")\r\n        .scrollIntoView();\r\n    }\r\n  };\r\n\r\n  renderTree = (nodes) => {\r\n    let icon;\r\n    let nodeId = nodes.code + \"-\" +nodes.id;\r\n\r\n    switch (nodes.account_type) {\r\n      case \"cat\":\r\n        nodeId = nodes.id;\r\n        // icon = nodes.has_children ? EmptyIcon : Folder;\r\n        if (this.props.expandedAccounts[nodes.id]) {\r\n          icon = <FolderOpened />;\r\n        } else {\r\n          icon = <Folder />;\r\n        }\r\n        break;\r\n      case \"account\":\r\n        icon = <FileIcon />;\r\n        break;\r\n      case \"temp\":\r\n        icon = <Loading />;\r\n        break;\r\n    }\r\n    if (nodes.has_children && nodes.children.length == 0) {\r\n      nodes.children = [\r\n        {\r\n          id: \"temp\",\r\n          name: \"\",\r\n          account_type: \"temp\",\r\n          code: nodes.code + \"-child\",\r\n        },\r\n      ];\r\n    }\r\n    if(nodes.isHidden == \"1\" && this.props['showHiddenAccounts'] == false) {\r\n      return ;\r\n    }\r\n    return (\r\n      <StyledTreeItem\r\n        label={nodes.name}\r\n        nodeId={nodeId}\r\n        icon={icon}\r\n        key={nodes.code + \"-\" +nodes.id}\r\n        code={nodes.code}\r\n        isHidden={nodes.isHidden}\r\n        onClick={this.handleNodeClick}\r\n      >\r\n        {Array.isArray(nodes.children)\r\n          ? nodes.children.map((node) => this.renderTree(node))\r\n          : null}\r\n      </StyledTreeItem>\r\n    );\r\n  };\r\n\r\n  getAccountTreeItemData(account, account_type) {\r\n    return {\r\n      id: account.id,\r\n      code: account.code,\r\n      name: account.name,\r\n      isHidden: account.is_hidden,\r\n      children: [],\r\n      account_type: account_type,\r\n      has_children: account.has_children ? true : false,\r\n    };\r\n  }\r\n\r\n  getTreeItemChildren(account) {\r\n    let childTreeItems = [];\r\n    if (this.props.parentCats[account.id]) {\r\n      this.props.parentCats[account.id].forEach((cat) => {\r\n\r\n        let childData = this.getAccountTreeItemData(cat, \"cat\");\r\n        if (cat.has_children) {\r\n          childData.children = this.getTreeItemChildren(cat);\r\n        }\r\n        if (APP_CONFIG.child_limit < cat.child_count) {\r\n          childData.children.push(\r\n            this.getAccountTreeItemData(\r\n              {\r\n                id: \"list-\" + cat.id,\r\n                name: \"View All\",\r\n                code: account.code + \"-list\",\r\n                isHidden: cat.is_hidden\r\n              },\r\n              \"list\"\r\n            )\r\n          );\r\n        }\r\n        childTreeItems.push(childData);\r\n      });\r\n    }\r\n    if (this.props.parentAccounts[account.id]) {\r\n      this.props.parentAccounts[account.id].forEach((account) => {\r\n        childTreeItems.push(this.getAccountTreeItemData(account, \"account\"));\r\n      });\r\n    }\r\n    childTreeItems = childTreeItems.sort( (a,b) => {\r\n      if ( a.code < b.code ){\r\n        return -1;\r\n      }\r\n      if ( a.code > b.code ){\r\n        return 1;\r\n      }\r\n      return 0;\r\n    } );\r\n\r\n    return childTreeItems;\r\n  }\r\n\r\n  treeItemClicked(event, itemId) {\r\n    const tagName = event.target.tagName;\r\n    const expand = tagName === \"I\";\r\n    let catId = false;\r\n    let accountId = false;\r\n    if(itemId.indexOf('-') === -1) {\r\n      catId = itemId;\r\n    } else {\r\n      accountId = itemId.split('-')[1];\r\n    }\r\n    if (catId && this.props.cats[catId]) {\r\n      this.props.getCatAccounts(catId);\r\n      if (expand) {\r\n        if (this.props.expandedAccounts[catId]) {\r\n          this.props.setExpandedStatus(catId, false);\r\n        } else {\r\n          this.props.setExpandedStatus(catId, true);\r\n        }\r\n      } else {\r\n        this.props.history.push(\"/cats/\" + catId);\r\n      }\r\n    } else if (this.props.accounts[accountId]) {\r\n      this.props.history.push(\"/accounts/\" + accountId);\r\n    } else {\r\n      this.props.history.push(\"/cats/\" + catId);\r\n    }\r\n  }\r\n\r\n  loadSearchAccounts(inputValue, callback) {\r\n    appAxios\r\n      .get(\r\n        `${APP_CONFIG.BASE_URL}api2/journal_accounts/json_find?q=${inputValue}&cat_name=1&with_cat=1`\r\n      )\r\n      .then((data) => {\r\n        const options = {};\r\n        if (data.data) {\r\n          for (const key in data.data) {\r\n            const record = data.data[key];\r\n            const option = { label: record.name, value: record.id, cat: record.cat };\r\n            if (options[record.cats]) {\r\n              options[record.cats].push(option);\r\n            } else {\r\n              options[record.cats] = [option];\r\n            }\r\n          }\r\n          const formattedOptions = [];\r\n          for (const group in options) {\r\n            formattedOptions.push({ label: group, options: options[group] });\r\n          }\r\n          callback(formattedOptions);\r\n        } else {\r\n          callback([]);\r\n        }\r\n      })\r\n      .catch((err) => {\r\n        alert(\r\n          \"error getting accounts if this error comes again please notify support\"\r\n        );\r\n      });\r\n  }\r\n\r\n  openAccount(value) {\r\n    if(value) {\r\n      if (!value.cat) {\r\n        this.props.history.push(\"/accounts/\" + value.value);\r\n      } else {\r\n        this.props.history.push(\"/cats/\" + value.value);\r\n      }\r\n    }\r\n  }\r\n\r\n  setAccountsBranch(branch) {\r\n    appAxios\r\n      .get(\r\n        `${APP_CONFIG.BASE_URL}api2/branches/set_report_account_branch/${branch.value}`\r\n      )\r\n      .then(() => {\r\n        window.location.href = window.location.href;\r\n      })\r\n      .catch((err) => {\r\n        alert(\"You are not authorized to change branch\");\r\n      });\r\n  }\r\n\r\n  render() {\r\n    let branchesOptions = [];\r\n    try {\r\n        branchesOptions = this.props.branches.map((branch) => {\r\n        return { value: branch.id, label: branch.name };\r\n      });\r\n    }catch (e) {\r\n      branchesOptions = [];\r\n    }\r\n\r\n    const treeAccounts = {};\r\n    if (this.props.parentCats[this.rootId]) {\r\n      this.props.parentCats[this.rootId].forEach((primaryCat) => {\r\n        let treeAccountItem = this.getAccountTreeItemData(primaryCat, \"cat\");\r\n        treeAccountItem.children = this.getTreeItemChildren(primaryCat);\r\n        treeAccounts[primaryCat.code] = treeAccountItem;\r\n      });\r\n    }\r\n    let tree = [];\r\n    if (treeAccounts) {\r\n      for (const primaryAccount in treeAccounts) {\r\n        tree.push(this.renderTree(treeAccounts[primaryAccount]));\r\n      }\r\n    }\r\n    const expandedAccounts = Object.keys(this.props.expandedAccounts).map(\r\n      (key) => {\r\n        if (this.props.expandedAccounts[key]) {\r\n          return key;\r\n        }\r\n      }\r\n    );\r\n    const accountBranch = window.reactInit.displayAccountBranch ? (\r\n      <div className=\"chart-of-accounts-col-9-filters-col\">\r\n        <Select\r\n          options={branchesOptions}\r\n          placeholder={\"Select Branch\"}\r\n          onChange={(value) => this.setAccountsBranch(value)}\r\n          value={window.reactInit.accountsBranch}\r\n        />\r\n      </div>\r\n    ) : (\r\n      \"\"\r\n    );\r\n    const accountsDropdownStyles = {\r\n      menu: ({ width, ...css }) => ({\r\n        ...css,\r\n        width: 'auto',\r\n        maxWidth: '70vw',\r\n        whiteSpace: 'nowrap',\r\n      })\r\n    };    \r\n    return (\r\n      <div className=\"chart-of-accounts-col chart-of-accounts-col-3\">\r\n        <div className=\"chart-of-accounts-search-wrapper\">\r\n          <div className=\"chart-of-accounts-col-3-filters-container\">\r\n            <div className=\"chart-of-accounts-col-3-filters-row\">\r\n              <div className=\"chart-of-accounts-col-3-filters-col\">\r\n                {/*<select className=\"form-control\">*/}\r\n                {/*<option value=\"1\">Filter Value</option>*/}\r\n                {/*</select>*/}\r\n                <AsyncSelect\r\n                  placeholder={intl.get(\"Search\")}\r\n                  styles={accountsDropdownStyles}\r\n                  cacheOptions\r\n                  loadOptions={this.loadSearchAccounts}\r\n                  onChange={(value) => this.openAccount(value)}\r\n                  isClearable={true}\r\n                />\r\n                {/* <ContextMenu /> */}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          {accountBranch}\r\n        </div>\r\n        <div className=\"chart-of-accounts-col-3-body-container\">\r\n          <TreeView\r\n            // defaultCollapseIcon={<FolderOpened />}\r\n            // defaultExpandIcon={<Folder />}\r\n            expanded={expandedAccounts}\r\n            selected={this.props.opendAccount.id != -1 ? this.props.opendAccount.id.toString() : ''}\r\n            onNodeSelect={(event, value) => this.treeItemClicked(event, value)}\r\n          >\r\n            {tree}\r\n          </TreeView>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n\r\nfunction mapStateToProps(state) {\r\n  return {\r\n    branches: state.accountsReducer.branches,\r\n    parentAccounts: state.accountsReducer.parentAccounts,\r\n    parentCats: state.accountsReducer.parentCats,\r\n    accounts: state.accountsReducer.accounts,\r\n    cats: state.accountsReducer.cats,\r\n    expanded: state.accountsReducer.expanded,\r\n    expandedAccounts: state.accountsReducer.expandedAccounts,\r\n    opendAccount: state.accountsReducer.openedAccount,\r\n    showHiddenAccounts: state.accountsReducer.showHiddenAccounts,\r\n  };\r\n}\r\n\r\nfunction mapDispatchToProps(dispatch) {\r\n  return {\r\n    getCatAccounts: (cat_id) => {\r\n      dispatch(getChildAccounts(cat_id, 1));\r\n    },\r\n    setExpandedStatus: (cat_id, status) => {\r\n      dispatch(setExpandedStatus(cat_id, status));\r\n    },\r\n  };\r\n}\r\nexport default connect(\r\n  mapStateToProps,\r\n  mapDispatchToProps\r\n)(withRouter(AccountsTree));\r\n", "export function formatPrice(amount) {\n\n  let formatted = window.AppFormatPrice(amount, window.reactInit.countryCode, window.reactInit.currency);\n  if(formatted=='-0.00'||formatted=='-0.000'||formatted=='-0') formatted = formatted.replace('-','');\n  return formatted;\n}\n\nexport function formatPriceWithCurrency(amount) {\n  if(amount > -0.01 && amount < 0.000) {\n    amount = 0;\n  }\n  return window.AppFormatPrice(\n    amount,\n    window.reactInit.countryCode,\n    window.reactInit.currency,\n    true\n  );\n}\n", "import { formatPrice, formatPriceWithCurrency } from \"./functions\";\nimport intl from \"react-intl-universal\";\n\nexport const ACCOUNT_TYPE_CREDIT = 0;\nexport const ACCOUNT_TYPE_DEBIT = 1;\n\nexport function getAccountAmountData(account) {\n  let amountText;\n  let amount;\n  if (account.type == ACCOUNT_TYPE_CREDIT) {\n    amountText = intl.get(\"Credit\");\n    amount = account.total_credit - account.total_debit;\n  } else {\n    amountText = intl.get(\"Debit\");\n    amount = account.total_debit - account.total_credit;\n  }\n  amount = formatPriceWithCurrency(amount);\n  return {\n    amountText,\n    amount,\n  };\n}\n", "import React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { APP_CONFIG } from \"../../config/config\";\nimport Dialog from \"@material-ui/core/Dialog\";\nimport DialogTitle from \"@material-ui/core/DialogTitle\";\nimport IconButton from \"@material-ui/core/IconButton\";\nimport CloseIcon from \"@material-ui/icons/Close\";\nimport intl from \"react-intl-universal\";\n\nexport default class AddAccount extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      loadingFrame: true,\n    };\n    let $this = this;\n    window.onmessage = function (e) {\n      try {\n        const obj = ReactDOM.findDOMNode($this);\n        obj.querySelector(\"iframe\").style.height = e.data.height;\n      } catch (e) {}\n    };\n  }\n\n  handleLoad = () => {\n    this.setState({ loadingFrame: false });\n  };\n  render() {\n    return (\n      <Dialog\n        maxWidth={\"lg\"}\n        fullWidth={true}\n        onClose={() => this.props.handleClose()}\n        aria-labelledby=\"simple-dialog-title\"\n        open={this.props.open}\n      >\n        <DialogTitle id=\"simple-dialog-title\">\n          {this.props.modalTitle ? intl.get(this.props.modalTitle) : \"\"}\n\n          <IconButton\n            aria-label=\"close\"\n            style={{ position: \"absolute\", right: 0, top: 0 }}\n            onClick={() => this.props.handleClose()}\n          >\n            <CloseIcon />\n          </IconButton>\n        </DialogTitle>\n        {this.state.loadingFrame && (\n          <div style={{ textAlign: \"center\", fontWeight: \"bold\" }}>\n            {intl.get(\"Loading...\")}\n          </div>\n        )}\n        <iframe\n          onLoad={this.handleLoad}\n          width={\"1000\"}\n          height={\"1000\"}\n          style={{minHeight: '570px'}}\n          src={this.props.url}\n          title=\"\"\n        ></iframe>\n      </Dialog>\n    );\n  }\n}\n", "import {APP_CONFIG} from \"./config/config\";\nimport intl from \"react-intl-universal\";\nimport React from \"react\";\n\nexport function getAccountDetailsLink(account, isCat = false) {\n    return (<a\n        target={\"_blank\"}\n        href={`${APP_CONFIG.BASE_URL}v2/owner/accounting/${account.id}/account-details/`+(isCat ? 1 : '')}\n        className=\"dropdown-item view-action\"\n    >\n        {intl.get(\"View\")}\n    </a>);\n}", "import React from \"react\";\n\nimport { <PERSON>, withRouter } from \"react-router-dom\";\nimport {\n  expandParents,\n  generateBreadCrumbs,\n  getChildAccounts,\n  getTreeAccounts,\n  loadAccountParents,\n  setExpandedStatus,\n  setOpenedAccount,\n  getRoutingData\n} from \"../../actions/accounts\";\nimport { connect } from \"react-redux\";\nimport { ACCOUNT_TYPE_CREDIT } from \"../../utils/account-type-util\";\nimport { APP_CONFIG } from \"../../config/config\";\nimport intl from \"react-intl-universal\";\nimport { formatPrice } from \"../../utils/functions\";\nimport AddAccount from \"../add-account/add-account\";\nimport {getAccountDetailsLink} from \"../../functions\";\n\nconst qs = require(\"query-string\");\n\nclass CatChildrenBoard extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      dialogOpened: false,\n      iframeUrl: \"\",\n      modalTitle: \"\",\n      loadingItems: \"\",\n      oldPage: \"\",\n    };\n    this.handleClose = this.handleClose.bind(this);\n  }\n\n  openAddAccount(event, url, modalTitle) {\n    event.preventDefault();\n    this.setState({\n      ...this.state,\n      dialogOpened: true,\n      iframeUrl: url,\n      modalTitle: modalTitle,\n    });\n    return false;\n  }\n\n  handleClose() {\n    this.setState({\n      ...this.state,\n      dialogOpened: false,\n    });\n  }\n  componentDidMount() {\n    const parentCatId = this.props.match.params.cat_id\n      ? this.props.match.params.cat_id\n      : -1;\n    this.props.setOpenedAccount(parentCatId);\n    // document.querySelector('.chart-of-accounts-col-9-body-container').addEventListener('scroll', this.scrollSpinner)\n  }\n\n  scrollSpinner = () => {\n    // document.querySelector('.load-data .sk-chase').style.position = 'fixed';\n    // if(window.width < 991 ) {\n    //   document.querySelector('.load-data .sk-chase').style.left = '50%';\n    // } else {\n    //   document.querySelector('.load-data .sk-chase').style.left = '30%';\n    // }\n  };\n\n  getCatListItem(child) {\n    let amountText = \"\";\n    let amount = 0;\n    if(!this.props.ignoreCalcCats) {\n    if (child.type == ACCOUNT_TYPE_CREDIT) {\n      amountText = intl.get(\"Credit\");\n      amount = child.total_credit - child.total_debit;\n    } else {\n      amountText = intl.get(\"Debit\");\n      amount = child.total_debit - child.total_credit;\n    }}\n    const deleteLink = child.deletable ? (\n      <a\n        target={\"_blank\"}\n        href={`${APP_CONFIG.BASE_URL}owner/journal_cats/delete/${child.id}`}\n        onClick={(e) => {\n          this.openAddAccount(\n            e,\n            `${APP_CONFIG.BASE_URL}owner/journal_cats/delete/${child.id}?iframe=1&folderid=${child.journal_cat_id}`,\n            \"Delete Account\"\n          );\n        }}\n        className=\"delete-action dropdown-item\"\n      >\n        {intl.get('Delete')}\n      </a>\n    ) : (\n      \"\"\n    );\n    const hideLink = child.can_be_hidden ? (\n        <a\n            href={`${APP_CONFIG.BASE_URL}owner/journal_cats/hide/${child.id}`}\n            className=\"hide-action dropdown-item\"\n        >\n          {intl.get(\"Hide\")}\n        </a>\n    ) : \"\";\n    const unhideLink = child.is_hidden == \"1\" ? (\n        <a\n            target={\"_self\"}\n            href={`${APP_CONFIG.BASE_URL}owner/journal_cats/unhide/${child.id}`}\n            className=\"unhide-action dropdown-item\"\n        >\n          {\" \"}{intl.get(\"Unhide\")}{\" \"}\n        </a>\n    ) : \"\";\n    return (\n      <tr\n        className={` chart-of-accounts-col-9-body-container-table-row`}\n        key={child.code}\n      >\n        <td className={` ${child.is_hidden == \"1\" ? 'hidden-account' : '' } border-0`}>\n          <Link\n            onClick={() => this.props.setExpandedStatus(child.id, true)}\n            to={\"/cats/\" + child.id}\n          >\n            <div className=\"chart-of-accounts-col-9-body-container-table-item\">\n              <div className=\"item-container\">\n                <i className=\"icon fas fa-folder mr-3\"></i>\n                <div className=\"details\">\n                  <p className=\"name\">{child.name}</p>\n                  <p className=\"id\">#{child.code}</p>\n                </div>\n              </div>\n            </div>\n          </Link>\n        </td>\n        <td className=\"border-0 text-right\">\n          <Link\n            onClick={() => this.props.setExpandedStatus(child.id, true)}\n            to={\"/cats/\" + child.id}\n          >\n            <div className=\"credit-wrap text-right\">\n              <div className=\"credit-container\">\n                <p className=\"cost\">{amount ? formatPrice(amount) : ''}</p>\n                <p className=\"type\">{amountText}</p>\n              </div>\n            </div>\n          </Link>\n        </td>\n        <td className=\"border-0 text-right\" width=\"50\">\n          <div className=\"dropdown\">\n            <a\n              className=\"btn btn-sm btn-secondary dropdown-toggle\"\n              href=\"#\"\n              role=\"button\"\n              data-toggle=\"dropdown\"\n              aria-haspopup=\"true\"\n              aria-expanded=\"false\"\n            >\n              <i className=\"mdi mdi-dots-horizontal\"></i>\n            </a>\n            <div className=\"dropdown-menu dropdown-menu-right\">\n              <a\n                target={\"_blank\"}\n                href={`${APP_CONFIG.BASE_URL}owner/journal_cats/edit/${child.id}`}\n                onClick={(e) => {\n                  this.openAddAccount(\n                    e,\n                    `${APP_CONFIG.BASE_URL}owner/journal_cats/edit/${child.id}?iframe=1&folderid=${child.journal_cat_id}`,\n                    \"Edit Account\"\n                  );\n                }}\n                className=\"edit-action dropdown-item\"\n              >\n                {intl.get(\"Edit\")}\n              </a>\n              {deleteLink}\n              {hideLink}\n              {unhideLink}\n            </div>\n          </div>\n        </td>\n      </tr>\n    );\n  }\n\n  getAccountListItem(child) {\n    let amountText = \"\";\n    let amount = 0;\n    if (child.type == ACCOUNT_TYPE_CREDIT) {\n      amountText = intl.get(\"Credit\");\n      amount = child.total_credit - child.total_debit;\n    } else {\n      amountText = intl.get(\"Debit\");\n      amount = child.total_debit - child.total_credit;\n    }\n    const costCenterLink = window.reactInit.showCostCenters ? (\n      <a\n        target={\"_blank\"}\n        href={`${APP_CONFIG.BASE_URL}owner/cost_centers/assign_cost_centers/${child.id}`}\n        className=\"edit-action dropdown-item\"\n      >\n        {intl.get(\"Assign Cost Centers\")}\n      </a>\n    ) : (\n      \"\"\n    );\n    const deleteLink = child.deletable ? (\n      <a\n        target={\"_blank\"}\n        href={`${APP_CONFIG.BASE_URL}owner/journal_accounts/delete/${child.id}`}\n        onClick={(e) => {\n          this.openAddAccount(\n            e,\n            `${APP_CONFIG.BASE_URL}owner/journal_accounts/delete/${child.id}?iframe=1&folderid=${child.journal_cat_id}`,\n            \"Delete Account\"\n          );\n        }}\n        className=\"delete-action dropdown-item\"\n      >\n        {intl.get(\"Delete\")}\n      </a>\n    ) : (\n      \"\"\n    );\n    const hideLink = child.can_be_hidden ? (\n        <a\n            href={`${APP_CONFIG.BASE_URL}owner/journal_accounts/hide/${child.id}`}\n            className=\"hide-action dropdown-item\"\n        >\n          {intl.get(\"Hide\")}\n        </a>\n    ) : \"\";\n\n    const unhideLink = child.is_hidden == \"1\" ? (\n        <a\n            target={\"_self\"}\n            href={`${APP_CONFIG.BASE_URL}owner/journal_accounts/unhide/${child.id}`}\n            className=\"unhide-action dropdown-item\"\n        >\n          {\" \"}{intl.get(\"Unhide\")}{\" \"}\n        </a>\n    ) : \"\";\n    return (\n      <tr\n        className={`chart-of-accounts-col-9-body-container-table-row`}\n        key={child.code}\n      >\n        <td className={` ${child.is_hidden == \"1\" ? 'hidden-account' : '' } border-0`}>\n          <Link to={\"/accounts/\" + child.id}>\n            <div className=\"chart-of-accounts-col-9-body-container-table-item\">\n              <div className=\"item-container\">\n                <i className=\"icon far fa-file mr-3\"></i>\n                <div className=\"details\">\n                  <p className=\"name\">{child.name}</p>\n                  <p className=\"id\">#{child.code}</p>\n                </div>\n              </div>\n            </div>\n          </Link>\n        </td>\n        <td className=\"border-0 text-right\">\n          <Link to={\"/accounts/\" + child.id}>\n            <div className=\"credit-wrap text-right\">\n              <div className=\"credit-container\">\n                <p className=\"cost\">{formatPrice(amount)}</p>\n                <p className=\"type\">{amountText}</p>\n              </div>\n            </div>\n          </Link>\n        </td>\n        <td className=\"border-0 text-right\" width=\"50\">\n          <div className=\"dropdown\">\n            <a\n              className=\"btn btn-sm btn-secondary dropdown-toggle\"\n              href=\"#\"\n              role=\"button\"\n              data-toggle=\"dropdown\"\n              aria-haspopup=\"true\"\n              aria-expanded=\"false\"\n            >\n              <i className=\"mdi mdi-dots-horizontal\"></i>\n            </a>\n            <div className=\"dropdown-menu dropdown-menu-right\">\n               {getAccountDetailsLink(child, false)}\n\n              <a\n                target={\"_blank\"}\n                href={`${APP_CONFIG.BASE_URL}owner/journal_accounts/edit/${child.id}?folderid=${child.journal_cat_id}`}\n                onClick={(e) => {\n                  this.openAddAccount(\n                    e,\n                    `${APP_CONFIG.BASE_URL}owner/journal_accounts/edit/${child.id}?iframe=1&folderid=${child.journal_cat_id}`,\n                    \"Edit Account\"\n                  );\n                }}\n                className=\"edit-action dropdown-item\"\n              >\n                {intl.get(\"Edit\")}\n              </a>\n              {costCenterLink}\n              {deleteLink}\n              {hideLink}\n              {unhideLink}\n            </div>\n          </div>\n        </td>\n      </tr>\n    );\n  }\n\n  componentDidUpdate(oldprops) {\n    const parentCatId = this.getParentCatId();\n    if (\n      this.props.pagination[this.getParentCatId()] &&\n      this.props.pagination[this.getParentCatId()].currentPage !=\n        this.state.oldPage\n    ) {\n      this.setState({\n        loadingItems: false,\n        oldPage: this.props.pagination[this.getParentCatId()].currentPage,\n      });\n    }\n    this.props.setOpenedAccount(parentCatId);\n    const parent = this.props.cats[parentCatId];\n    if (parent && parent.journal_cat_id) {\n      const grandParent = this.props.parentCats[parent.journal_cat_id];\n      if (!grandParent) {\n        this.props.loadAccountParents(\n          parent.parent_cat_ids.split(\",\"),\n          parent.id\n        );\n      } else {\n        this.props.expandParents([\n          parent.parent_cat_ids.split(\",\"),\n          parent.journal_cat_id,\n        ]);\n      }\n    }\n    // this.props.setExpandedStatus(parentCatId);\n  }\n\n  handlePaginationClick = () => {\n    var myDiv = document.getElementById('chart-of-accounts-child-board');\n    myDiv.scrollTop = 0;\n    this.setState({\n      loadingItems: true,\n      oldPage: this.props.pagination[this.getParentCatId()].currentPage,\n    });\n  };\n\n  getParentCatId() {\n    return this.props.match.params.cat_id ? this.props.match.params.cat_id : -1;\n  }\n\n  render() {\n    const parsed = qs.parse(this.props.location.search);\n    const parentCatId = this.getParentCatId();\n    let page = parseInt(parsed.page ? parsed.page : 1);\n    let nextNav = \"\",\n      prevNav = \"\";\n    if (this.props.pagination && this.props.pagination[parentCatId]) {\n      if (this.props.pagination[parentCatId].pagesCount != 0) {\n        const pagingData = this.props.pagination[parentCatId];\n        if (page < pagingData.pagesCount) {\n          nextNav = (\n            <Link\n              onClick={this.handlePaginationClick}\n              to={`/cats/${parentCatId}?page=${page + 1}`}\n              className=\"chart-nav chart-next-page\"\n            >\n              <i className=\"fa fa-angle-right\"></i>\n              {/* next page */}\n            </Link>\n          );\n        }\n        if (page > 1 && page - 1 < pagingData.pagesCount) {\n          prevNav = (\n            <Link\n              onClick={this.handlePaginationClick}\n              to={`/cats/${parentCatId}?page=${page - 1}`}\n              className=\"chart-nav chart-prev-page\"\n            >\n              <i className=\"fa fa-angle-left\"></i>\n              {/* prev page */}\n            </Link>\n          );\n        }\n      }\n    }\n    this.props.getCatAccounts(parentCatId, page);\n    this.props.generateBreadCrumbs(parentCatId);\n\n    const loaded = this.props.parentCats[parentCatId] !== undefined;\n    let emptyMessage = \"\";\n    let loadingMessage = \"\";\n    const childCats = this.props.parentCats[parentCatId]\n      ? this.props.parentCats[parentCatId]\n      : [];\n    const childAccounts = this.props.parentAccounts[parentCatId]\n      ? this.props.parentAccounts[parentCatId]\n      : [];\n    const childListItems = [];\n    if (!loaded) {\n      loadingMessage = <div>{intl.get(\"Loading...\")}</div>;\n    } else if (loaded && childCats.length === 0 && childAccounts.length === 0) {\n      emptyMessage = <div>{intl.get(\"This Account is Empty\")}</div>;\n    }\n    childCats.forEach((child) => {\n      if(child.is_hidden === \"1\" && !this.props.showHiddenAccounts) {\n        return;\n      }\n      childListItems.push(this.getCatListItem(child));\n    });\n    childAccounts.forEach((child) => {\n      if(child.is_hidden === \"1\" && !this.props.showHiddenAccounts) {\n        return;\n      }\n      childListItems.push(this.getAccountListItem(child));\n    });\n    return (\n      <div  id=\"chart-of-accounts-child-board\" className=\"chart-of-accounts-col-9-body-container\">\n        <AddAccount\n          parentId={parentCatId}\n          url={this.state.iframeUrl}\n          modalTitle={this.state.modalTitle}\n          open={this.state.dialogOpened}\n          handleClose={this.handleClose}\n        />\n        <div style={{ position: \"relative\" }}>\n          <table className=\"list-table table table-hover not-clickable chart-of-accounts-col-9-body-container-table\">\n            <tbody>{childListItems}</tbody>\n          </table>\n          {(this.state.loadingItems || !loaded) ? (\n            <div className=\"load-data\">\n              <div className=\"sk-chase\">\n                <div className=\"sk-chase-dot\"></div>\n                <div className=\"sk-chase-dot\"></div>\n                <div className=\"sk-chase-dot\"></div>\n                <div className=\"sk-chase-dot\"></div>\n                <div className=\"sk-chase-dot\"></div>\n                <div className=\"sk-chase-dot\"></div>\n              </div>\n            </div>\n          ) : (\n            \"\"\n          )}\n        </div>\n\n        {emptyMessage}\n        <a\n          href={`${APP_CONFIG.BASE_URL}owner/journal_cats/add?folderid=${parentCatId}`}\n          onClick={(e) => {\n            this.openAddAccount(\n              e,\n              `${APP_CONFIG.BASE_URL}owner/journal_cats/add?iframe=1&folderid=${parentCatId}`,\n              \"Add Account\"\n            );\n          }}\n          className=\"add-account btn btn-secondary font-weight-bold\"\n        >\n          <i className=\"far fa-plus-circle text-success mr-2\"></i>\n          <span>{intl.get(\"Add Account\")}</span>\n        </a>\n        <div className=\"pagination-items\">\n          {nextNav}\n          {this.props.pagination[parentCatId] &&\n            this.props.pagination[parentCatId].pagesCount != 1 &&\n            this.props.pagination[parentCatId].pagesCount != 0 && (\n              <span>\n                <span> {this.props.pagination[parentCatId].currentPage} </span>{\" \"}\n                / <span> {this.props.pagination[parentCatId].pagesCount} </span>\n              </span>\n            )}\n          {prevNav}\n        </div>\n      </div>\n    );\n  }\n}\nconst comp = withRouter(CatChildrenBoard);\nfunction mapStateToProps(state) {\n  return {\n    parentAccounts: state.accountsReducer.parentAccounts,\n    parentCats: state.accountsReducer.parentCats,\n    accounts: state.accountsReducer.accounts,\n    cats: state.accountsReducer.cats,\n    pagination: state.accountsReducer.pagination,\n    showHiddenAccounts: state.accountsReducer.showHiddenAccounts,\n    ignoreCalcCats:state.accountsReducer.ignoreCalcCats,\n  };\n}\n\nfunction mapDispatchToProps(dispatch) {\n  return {\n    getCatAccounts: (cat_id, page) => {\n      dispatch(getChildAccounts(cat_id, page));\n    },\n    getTreeAccounts: () => {\n      dispatch(getTreeAccounts());\n    },\n    setOpenedAccount: (cat_id) => {\n      dispatch(setOpenedAccount(cat_id, true));\n    },\n    loadAccountParents: (account_parents, account_id) => {\n      dispatch(loadAccountParents(account_parents, account_id));\n    },\n    expandParents: (account_parents) => {\n      dispatch(expandParents(account_parents));\n    },\n    generateBreadCrumbs: (account_id) => {\n      dispatch(generateBreadCrumbs(account_id, true));\n    },\n    setExpandedStatus: (catId) => {\n      dispatch(setExpandedStatus(catId, true));\n    }\n\n  };\n}\nexport default connect(mapStateToProps, mapDispatchToProps)(comp);\n", "import React from \"react\";\r\nimport CatC<PERSON>drenBoard from \"../cat-children-board/cat-children-board\";\r\nimport {\r\n  BrowserRouter as Router,\r\n  Switch,\r\n  Route,\r\n  withRouter,\r\n  Link,\r\n} from \"react-router-dom\";\r\n\r\nclass AccountsBoard extends React.Component {\r\n  render() {\r\n    return (\r\n      <Switch>\r\n        <Route path=\"/cats/:cat_id\">\r\n          <CatChildrenBoard />\r\n        </Route>\r\n        <Route path=\"/cats\">\r\n          <CatChildrenBoard />\r\n        </Route>\r\n        <Route path=\"/\">\r\n          <CatChildrenBoard />\r\n        </Route>\r\n      </Switch>\r\n    );\r\n  }\r\n}\r\nexport default AccountsBoard;\r\n", "import React from \"react\";\nimport ReactDOM from \"react-dom\";\n\nimport { withRouter } from \"react-router-dom\";\nimport {\n  expandParents,\n  generateBreadCrumbs,\n  getAccountData,\n  loadAccountParents,\n  setOpenedAccount,\n} from \"../../actions/accounts\";\nimport { connect } from \"react-redux\";\nimport { APP_CONFIG } from \"../../config/config\";\nimport intl from \"react-intl-universal\";\nclass AccountTransactions extends React.Component {\n  constructor(props) {\n    super(props);\n    let $this = this;\n    this.state = {\n      loadingFrame: true,\n      random: 0,\n      showDeletePopup: false,\n      iframeDeleteUrl: null,\n      showAfterLoading: false\n    };\n    window.onmessage =  function (e) {\n      try {\n        if(e.data.type === 'url_changed') {\n          const url = e.data.ajaxUrl;\n          const page = url.match(/page\\:([0-9]+)/)[1];\n          const params = url.split('?')[1];\n          const iframeParams =  `?page=${page}&${params}&is_ajax=1&load_scripts=1`;\n          if(!this.firstMessage) {\n            window.history.pushState(\"\", \"\",  `${iframeParams}`);\n          } else {\n            this.firstMessage = false;\n          }\n        } else {\n          const obj = ReactDOM.findDOMNode($this);\n          obj.style.height= \"calc(100% - 50px) !important\";\n          // obj.style.flex = \"1 0 0\";\n        }\n\n      } catch (e) {\n        console.log(e);\n      }\n    }.bind(this);\n  }\n\n  firstMessage = true;\n\n  resetIframe = () => {\n    this.setState({random: this.state.random + 1});\n  }\n\n  handleLoad = () => {\n    let that = this;\n    this.setState({ loadingFrame: true });\n    let iframe =  document.getElementById(\"iframe_id\");\n    var style = document.createElement('style');\n      style.textContent =\n      `\n        button.popup-chat { display: none !important }\n        .modal-dialog {padding-top: 65px !important}\n        #main-content {margin: 0}\n        #main-nav, #main-content .header {display: none}\n        @media (min-width: 768px) {\n          .modal-dialog {\n              width: 750px;\n          }\n        }\n      `\n    ;\n    if(iframe.contentDocument) {\n      let noButton = iframe.contentDocument.body.querySelector('.confirm-action > .btn:last-of-type');\n      if(noButton) noButton.addEventListener('click', function () { that.resetIframe() });\n\n      let yesButton = iframe.contentDocument.body.querySelector('.confirm-action > .btn:first-of-type');\n      if(yesButton) yesButton.addEventListener('click', function () { that.setState({showAfterLoading: true}) });\n\n      let iframeFlash = iframe.contentDocument.body.querySelector('#flashMessage.Sucmessage');\n      if(iframeFlash) {\n        that.resetIframe();\n        that.setState({showDeletePopup: false, showAfterLoading: false});\n      }\n\n    }\n\n    try{\n      iframe.contentDocument.head.appendChild(style);\n    }catch (e) {\n      console.log(e);\n    }\n  };\n\n  componentDidMount() {\n    this.handleLoad();\n    if (window.innerWidth <= 991) {\n      // setInterval(() => {\n      //   document.querySelector(\"iframe\").style.height =\n      //     document.querySelector(\"iframe\").contentWindow.document.body\n      //       .offsetHeight +\n      //     100 +\n      //     \"px\";\n      // }, 5000);\n    } else {\n      // document.querySelector(\"iframe\").style.height = \"576px\";\n    }\n  }\n\n  render() {\n    const accountId = this.props.match.params.account_id;\n    try {\n      const url = window.location.href;\n      const params = url.split('?')[1];\n      if(params != undefined) {\n        var iframeParams =  `?${params}`;\n      } else {}\n    }catch(e) {\n    }\n    if((iframeParams === undefined || typeof iframeParams === 'undefined')) {\n      var iframeParams = \"?page=1&date_from=&date_to=&is_ajax=1&load_scripts=1\"\n    }\n    this.currentParams = iframeParams;\n    this.props.generateBreadCrumbs(accountId);\n    return (\n      <>\n        {/* {this.state.loadingFrame && (\n          <div style={{ textAlign: \"center\", fontWeight: \"bold\" }}>\n            {intl.get(\"Loading...\")}\n          </div>\n        )} */}\n        {/* {this.state.loadingFrame  && <div style={{position: 'absolute', top:0, left: 0,right: 0, width: '100%', height: '100%', background: 'rgba(255,255,255,1)', justifyContent: 'center', display: 'flex', alignItems: 'center'}}>Loading...</div>} */}\n\n        {/* Show PopUp for delete  */}\n        {this.state.showAfterLoading && <div className=\"loader\"><div className=\"lds-ring\"><div></div><div></div><div></div><div></div></div></div>}\n        {/* {this.state.showDeletePopup && <div className={this.state.showAfterLoading ? 'delete-popup show' : 'delete-popup'}>\n          <iframe \n            id=\"deleteIframePopup\" \n            onLoad={this.handleDeletePopupLoad} \n            src={`${APP_CONFIG.BASE_URL}owner/journals/delete/${this.state.iframeDeleteUrl && this.state.iframeDeleteUrl.split('/delete/')[1]}?box=1`} \n            title=\"\" \n            style={{ height: \"100%\" }} \n          />\n        </div>} */}\n        <iframe\n          key={this.state.random}\n          id=\"iframe_id\"\n          src={`${APP_CONFIG.BASE_URL}owner/journal_accounts/account_transactions/${accountId}${iframeParams}`}\n          title=\"\"\n          style={{ height: \"100%\" }}\n          onLoad={this.handleLoad}\n        ></iframe>\n      </>\n    );\n  }\n\n  componentDidUpdate(prevProps, prevState, snapshot) {\n    const accountId = this.props.match.params.account_id;\n    if (!this.props.accounts[accountId]) {\n      this.props.getAccountData(accountId);\n    }\n    this.props.setOpenedAccount(accountId);\n\n    const parent = this.props.accounts[accountId];\n    if (parent && parent.journal_cat_id) {\n      const grandParent = this.props.parentCats[parent.journal_cat_id];\n      if (!grandParent) {\n        this.props.loadAccountParents(parent.parent_cat_ids.split(\",\"));\n      } else {\n        this.props.expandParents([\n          parent.parent_cat_ids.split(\",\"),\n          parent.journal_cat_id,\n        ]);\n      }\n    }\n  }\n}\n\nfunction mapStateToProps(state) {\n  return {\n    accounts: state.accountsReducer.accounts,\n    parentCats: state.accountsReducer.parentCats,\n  };\n}\n\nfunction mapDispatchToProps(dispatch) {\n  return {\n    setOpenedAccount: (account_id) => {\n      dispatch(setOpenedAccount(account_id, false));\n    },\n    getAccountData: (account_id) => {\n      dispatch(getAccountData(account_id));\n    },\n    generateBreadCrumbs: (account_id) => {\n      dispatch(generateBreadCrumbs(account_id, false));\n    },\n    loadAccountParents: (account_parents, account_id) => {\n      dispatch(loadAccountParents(account_parents, account_id));\n    },\n    expandParents: (account_parents) => {\n      dispatch(expandParents(account_parents));\n    },\n  };\n}\nconst comp = withRouter(AccountTransactions);\nexport default connect(mapStateToProps, mapDispatchToProps)(comp);\n", "import React from \"react\";\nimport { withRouter } from \"react-router-dom\";\nimport { connect } from \"react-redux\";\nimport { getAccountAmountData } from \"../../utils/account-type-util\";\nimport { APP_CONFIG } from \"../../config/config\";\nimport intl from \"react-intl-universal\";\nimport AddAccount from \"../add-account/add-account\";\nimport {getRoutingData, toggleShowHiddenAccounts} from '../../actions/accounts';\nimport {getAccountDetailsLink} from \"../../functions\";\nclass PageHead extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      dialogOpened: false,\n      iframeUrl: \"\",\n      modalTitle: \"\",\n      loadingItems: \"\",\n      oldPage: \"\",\n    };\n    this.handleClose = this.handleClose.bind(this);\n  }\n\n  openAddAccount(event, url, modalTitle) {\n    event.preventDefault();\n    this.setState({\n      ...this.state,\n      dialogOpened: true,\n      iframeUrl: url,\n      modalTitle: modalTitle,\n    });\n    return false;\n  }\n\n  handleClose() {\n    this.setState({\n      ...this.state,\n      dialogOpened: false,\n    });\n  }\n\n  componentDidUpdate(oldprops) {\n    if(!this.props.loadingRoutingData && !this.props.routingData && this.props.currentOpenedAccount.id != -1) {\n      this.props.getRoutingData(this.props.currentOpenedAccount.id, this.props.currentOpenedAccount.is_cat);\n    } else if(this.props.routingData && !this.props.loadingRoutingData) {\n      window.initTooltips();\n    }\n  }\n\n  getRoutingLink(routingData) {\n    let link = '';\n    if(routingData.name && routingData.code) {\n      link = (<span><a href={routingData.link} class=\"text-decoration-underline\" target=\"_blank\">{routingData.name} #{routingData.code}</a></span>) \n    }\n    let toolTip;\n    if(routingData.entityTypeName) {\n      toolTip = (<span className=\"tip-circle lazy-tip\" data-title={routingData.toolTipKey}><i\n              className=\"fas fa-question-circle\"></i></span>\n      );\n    }\n    return (\n      <span>\n      {routingData.entityTypeName}\n      {toolTip}\n      {link}\n      </span>\n    );\n  }\n\n\n  render() {\n    let reportLink;\n    let deleteLink;\n    let hideLink;\n    let unhideLink;\n    let costCenterLink;\n    const currentOpenedAccount = this.props.currentOpenedAccount;\n    let currentOpenedAccountData = {};\n    let urlPrefix = (currentOpenedAccount.is_cat ? 'journal_cats' : 'journal_accounts');\n    reportLink = \"\";\n    if (currentOpenedAccount.id != -1) {\n      if (currentOpenedAccount.is_cat) {\n        currentOpenedAccountData = this.props.cats[currentOpenedAccount.id]\n          ? this.props.cats[currentOpenedAccount.id]\n          : {};\n        reportLink = (\n          <div className=\"action-buttons d-inline-block\">\n            <div\n              className=\"btn-group ml-3\"\n              role=\"group\"\n              aria-label=\"padination\"\n            >\n              <a\n                target=\"_blank\"\n                href={`${APP_CONFIG.BASE_URL}owner/reports/journal_transactions?show_net=&report_type=transaction&journal_cat_id=${currentOpenedAccountData.id}&journal_account_id=&currency=-1&date_from=&date_to=`}\n                className=\"btn btn-secondary\"\n              >\n                <i className=\"fas fa-chart-pie\"></i>\n              </a>\n            </div>\n          </div>\n        );\n      } else {\n        currentOpenedAccountData = this.props.accounts[currentOpenedAccount.id]\n          ? this.props.accounts[currentOpenedAccount.id]\n          : {};\n        reportLink = (\n          <div className=\"action-buttons d-inline-block\">\n            <div\n              className=\"btn-group mx-3\"\n              role=\"group\"\n              aria-label=\"padination\"\n            >\n              <a\n                target=\"_blank\"\n                href={`${APP_CONFIG.BASE_URL}owner/reports/journal_transactions?show_net=&report_type=transaction&journal_cat_id=&journal_account_id=${currentOpenedAccountData.id}&currency=-1&date_from=&date_to=`}\n                className=\"btn btn-secondary\"\n              >\n                <i className=\"fas fa-chart-pie\"></i>\n              </a>\n            </div>\n            <a\n              href={`${APP_CONFIG.BASE_URL}owner/journals/add?journal_account_id=${currentOpenedAccountData.id}`}\n              id=\"add-link\"\n              target=\"_blank\"\n              data-href=\"/owner/journals/add\"\n              className=\"btn btn-lg btn-success btn-addon button add-new-btn\"\n            >\n              <span>\n                <i className=\"fa fa-plus\"></i>\n                {intl.get(\"New Journal\")}\n              </span>\n            </a>\n          </div>\n        );\n        costCenterLink = window.reactInit.showCostCenters ? (\n          <a\n            target={\"_blank\"}\n            href={`${APP_CONFIG.BASE_URL}owner/cost_centers/assign_cost_centers/${currentOpenedAccountData.id}`}\n            className=\"edit-action dropdown-item\"\n          >\n            {\" \"}{intl.get(\"Assign Cost Centers\")}{\" \"}\n          </a>\n        ) : (\n          \"\"\n        );\n      }\n      deleteLink = currentOpenedAccountData.deletable ? (\n          <a\n              target={\"_blank\"}\n              href={`${APP_CONFIG.BASE_URL}owner/${urlPrefix}/delete/${currentOpenedAccountData.id}`}\n              onClick={(e) => {\n                this.openAddAccount(\n                    e,\n                    `${APP_CONFIG.BASE_URL}owner/${urlPrefix}/delete/${currentOpenedAccountData.id}?iframe=1&folderid=${currentOpenedAccountData.journal_cat_id}`,\n                    \"Delete Account\"\n                );\n              }}\n              className=\"delete-action dropdown-item\"\n          >\n            {\" \"}{intl.get(\"Delete\")}{\" \"}\n          </a>\n      ) : (\n          \"\"\n      );\n    }\n    let amountData = { amount: \"\", amountText: \"\" };\n    if (!currentOpenedAccountData.name) {\n      currentOpenedAccountData = {\n        name: intl.get(\"Chart Of Accounts\"),\n        code: \"\",\n      };\n    } else {\n      amountData = getAccountAmountData(currentOpenedAccountData);\n      document.title = intl.get(\"Chart of Accounts\") + \": \" + currentOpenedAccountData.name;\n    }\n    // const\n\n    const editBtn = (\n      <a\n        target={\"_blank\"}\n        href={`${APP_CONFIG.BASE_URL}owner/${urlPrefix}/edit/${currentOpenedAccountData.id}?folderid=${currentOpenedAccountData.journal_cat_id}`}\n        onClick={(e) => {\n          this.openAddAccount(\n            e,\n            `${APP_CONFIG.BASE_URL}owner/${urlPrefix}/edit/${currentOpenedAccountData.id}?iframe=1&folderid=${currentOpenedAccountData.journal_cat_id}`,\n            \"Edit Account\"\n          );\n        }}\n        className=\"edit-action dropdown-item\"\n      >\n        {\" \"}{intl.get(\"Edit\")}{\" \"}\n      </a>\n    )\n    const unhideBtn = currentOpenedAccountData.is_hidden == \"1\" ? (\n        <a\n            target={\"_self\"}\n            href={`${APP_CONFIG.BASE_URL}owner/${urlPrefix}/unhide/${currentOpenedAccountData.id}?folderid=${currentOpenedAccountData.journal_cat_id}`}\n            className=\"unhide-action dropdown-item\"\n        >\n          {\" \"}{intl.get(\"Unhide\")}{\" \"}\n        </a>\n    ) : \"\";\n    const hideBtn = currentOpenedAccountData.can_be_hidden ? (\n      <a\n        target={\"_self\"}\n        href={`${APP_CONFIG.BASE_URL}owner/${urlPrefix}/hide/${currentOpenedAccountData.id}?folderid=${currentOpenedAccountData.journal_cat_id}`}\n        className=\"hide-action dropdown-item\"\n      >\n        {\" \"}{intl.get(\"Hide\")}{\" \"}\n      </a>\n    ) : \"\";\n    let routingLinks = [];\n    if(this.props.routingData) {\n      routingLinks.push(this.getRoutingLink(this.props.routingData.systemRouting));\n      this.props.routingData.manualRouting.forEach((item) => {\n        routingLinks.push(this.getRoutingLink(item));\n      })\n    }\n    const showHiddenAccountsCheckbox = window.reactInit.displayShowHiddenAccounts ? (<div className=\"form-check px-3\">\n      <input className=\"form-check-input\" type=\"checkbox\" role=\"switch\" id=\"showHidden\" onClick={() => {\n        this.props.toggleShowHiddenAccounts();\n      }}/>\n      <label className=\"form-check-label\" htmlFor=\"showHidden\">{intl.get(\"Show Hidden\")}</label>\n    </div>) : '';\n    return (\n      <div>\n        <div className=\"pages-head\" style={{ zIndex: 1 }}>\n        <AddAccount\n          url={this.state.iframeUrl}\n          modalTitle={this.state.modalTitle}\n          open={this.state.dialogOpened}\n          handleClose={this.handleClose}\n        />\n          <div className=\"container-fluid\">\n            <div className=\"row align-items-center\">\n              <div className=\"col-sm-6\">\n                <div className=\"row align-items-center\">\n                  <div className=\"col-sm-12 head-info\">\n                    <div className=\"pages-head-title\">\n                      <h2>\n                        {currentOpenedAccountData.name}{\" \"}\n                        <span className=\"fs-14\">\n                          {currentOpenedAccountData.code\n                            ? \"#\" + currentOpenedAccountData.code\n                            : \"\"}\n                        </span>\n\n                        {currentOpenedAccountData.code && \n                        <div className=\"btn-group dropdown\">\n                          <div className=\"dropdown-menu\"  style={{position: 'absolute', transform: 'translate3d(-107x, 35px, 0px)', top: '0px', left: '0px'}}>\n                             {getAccountDetailsLink(currentOpenedAccountData, currentOpenedAccount.is_cat)}\n                            {editBtn}\n                            {unhideBtn}\n                            {hideBtn}\n                            {costCenterLink}\n                            {deleteLink}\n                          </div>\n                          <button type=\"button\" className=\"btn dropdown-toggle dropdown-toggle-split px-2\" data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"true\">\n                              <span className=\"sr-only\">Toggle Dropdown</span>\n                              <i className=\"fas fa-cog fs-12\" style={{color: '#369!important'}}></i>\n                          </button>\n                        </div>\n                        }\n                      </h2>\n                      <div className=\"route-links\">\n                          {routingLinks}  \n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <div className=\"col-sm-6 text-right\">\n                {showHiddenAccountsCheckbox}\n                <div className=\"credit-wrap text-right d-inline-block align-middle\">\n                  <div\n                      className=\"credit-container px-2 credit-overdue\"\n                      style={{\n                      \"borderColor\": !amountData.amount.startsWith(\"-\")\n                        ? \"#EB2121\"\n                        : \"#13b272\",\n                    }}\n                  >\n                    <p className=\"cost\">{amountData.amount}</p>\n                    <p className=\"type\">{amountData.amountText}</p>\n                  </div>\n                </div>\n                {reportLink}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n\nfunction mapDispatchToProps(dispatch) {\n  return {\n    getRoutingData: (catId,is_cat) => {\n      dispatch(getRoutingData(catId, is_cat))\n    },\n    toggleShowHiddenAccounts: () => {\n        dispatch(toggleShowHiddenAccounts());\n    }\n  };\n}\n\nfunction mapStateToProps(state) {\n  let routingData;\n  if(state.accountsReducer.openedAccount.is_cat) {\n     routingData = state.accountsReducer.catsRoutingData[state.accountsReducer.openedAccount.id];\n  } else {\n     routingData = state.accountsReducer.accountsRoutingData[state.accountsReducer.openedAccount.id];\n  }\n  return {\n    currentOpenedAccount: state.accountsReducer.openedAccount,\n    accounts: state.accountsReducer.accounts,\n    cats: state.accountsReducer.cats,\n    routingData: routingData,\n    loadingRoutingData: state.accountsReducer.loadingRoutingData\n  };\n}\nexport default connect(mapStateToProps,mapDispatchToProps)(withRouter(PageHead));\n", "import React from \"react\";\n\nimport \"./App.css\";\nimport AccountsTree from \"./components/accounts-tree/accounts-tree\";\nimport AccountsBoard from \"./components/accounts-board/accounts-board\";\nimport AccountTransactions from \"./components/account-transactions/account-transactions\";\nimport \"./assets/styles/style.css\";\nimport {\n  BrowserRouter as Router,\n  Switch,\n  Route,\n  Link,\n  useRouteMatch,\n  useParams,\n  withRouter,\n} from \"react-router-dom\";\nimport PageHead from \"./components/page-head/page-head\";\nimport {\n  getBranches,\n  getChildAccounts,\n  getTreeAccounts,\n} from \"./actions/accounts\";\nimport { connect } from \"react-redux\";\nimport Select from \"react-select\";\nimport appAxios from \"./utils/app-axios\";\nimport { APP_CONFIG } from \"./config/config\";\nimport intl from \"react-intl-universal\";\nconst localFile = require(`./locals/${window.reactInit.lang}.json`);\n\nclass App extends React.Component {\n  state = { initDone: false };\n  constructor(props) {\n    super(props);\n    let self = this;\n    window.addEventListener(\n      \"bread-crumbs-clicked\",\n      function (e) {\n        const target = String(e.detail).replaceAll('/v2/owner/chart-of-accounts', '');\n        self.props.history.push(target); // \n      },\n      false\n    );\n    if(window.reactInit.branchActive) {\n      this.props.getBranches();\n    }\n  }\n\n  componentDidMount() {\n    this.loadLocales().then(() => {\n      this.setState({ initDone: true });\n    });\n  }\n\n  loadLocales() {\n    return intl.init({\n      currentLocale: window.reactInit.lang,\n      locales: {\n        [window.reactInit.lang]: localFile,\n      },\n    });\n  }\n\n  setJournalsBranch(branch) {\n    appAxios\n      .get(\n        `${APP_CONFIG.BASE_URL}api2/branches/set_report_transactions_branch/${branch.value}`\n      )\n      .then(() => {\n        window.location.href = window.location.href;\n      })\n      .catch((err) => {\n        alert(\"You are not authorized to change branch\");\n      });\n  }\n  render() {\n    let branchesOptions = [];\n    try{\n      branchesOptions = this.props.branches.map((branch) => {\n        return { value: branch.id, label: branch.name };\n      });\n    } catch (e) {\n      branchesOptions = [];\n    }\n    const transactionBranch = window.reactInit.displayTransactionBranch ? (\n      <div className=\"chart-of-accounts-col-9-filters-col\">\n        <label style={{ display: \"inline\" }}>{intl.get(\"Journals Branch\")}</label>\n        <Select\n          options={branchesOptions}\n          placeholder={\"Select Branch\"}\n          onChange={(value) => this.setJournalsBranch(value)}\n          value={window.reactInit.transactionBranch}\n        />\n      </div>\n    ) : (\n      \"\"\n    );\n\n    const branchFilters =\n      transactionBranch !== \"\" ? (\n        <div className=\"chart-of-accounts-col-9-filters-container\">\n          <div className=\"chart-of-accounts-col-9-filters-row\">\n            {transactionBranch}\n          </div>\n        </div>\n      ) : (\n        \"\"\n      );\n    return (\n      this.state.initDone && (\n        <div>\n          <PageHead />\n          <div className=\"chart-of-accounts-container\">\n            <div className=\"chart-of-accounts-row\">\n              <AccountsTree />\n              <div className=\"chart-of-accounts-col chart-of-accounts-col-9\">\n                {branchFilters}\n                <Switch>\n                  <Route path=\"/cats\">\n                    <AccountsBoard />\n                  </Route>\n                  <Route path={\"/accounts/:account_id\"}>\n                    <AccountTransactions />\n                  </Route>\n                  <Route path=\"/\">\n                    <AccountsBoard />\n                  </Route>\n                </Switch>\n              </div>\n            </div>\n          </div>\n        </div>\n      )\n    );\n  }\n}\n\nfunction mapStateToProps(state) {\n  return {\n    branches: state.accountsReducer.branches,\n  };\n}\n\nfunction mapDispatchToProps(dispatch) {\n  return {\n    getBranches: () => {\n      dispatch(getBranches());\n    },\n  };\n}\nexport default connect(mapStateToProps, mapDispatchToProps)(withRouter(App));\n", "// This optional code is used to register a service worker.\n// register() is not called by default.\n\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on subsequent visits to a page, after all the\n// existing tabs open on the page have been closed, since previously cached\n// resources are updated in the background.\n\n// To learn more about the benefits of this model and instructions on how to\n// opt-in, read https://bit.ly/CRA-PWA\n\nconst isLocalhost = Boolean(\n  window.location.hostname === 'localhost' ||\n    // [::1] is the IPv6 localhost address.\n    window.location.hostname === '[::1]' ||\n    // *********/8 are considered localhost for IPv4.\n    window.location.hostname.match(\n      /^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/\n    )\n);\n\nexport function register(config) {\n  if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {\n    // The URL constructor is available in all browsers that support SW.\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\n    if (publicUrl.origin !== window.location.origin) {\n      // Our service worker won't work if PUBLIC_URL is on a different origin\n      // from what our page is served on. This might happen if a CDN is used to\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\n      return;\n    }\n\n    window.addEventListener('load', () => {\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\n\n      if (isLocalhost) {\n        // This is running on localhost. Let's check if a service worker still exists or not.\n        checkValidServiceWorker(swUrl, config);\n\n        // Add some additional logging to localhost, pointing developers to the\n        // service worker/PWA documentation.\n        navigator.serviceWorker.ready.then(() => {\n          console.log(\n            'This web app is being served cache-first by a service ' +\n              'worker. To learn more, visit https://bit.ly/CRA-PWA'\n          );\n        });\n      } else {\n        // Is not localhost. Just register service worker\n        registerValidSW(swUrl, config);\n      }\n    });\n  }\n}\n\nfunction registerValidSW(swUrl, config) {\n  navigator.serviceWorker\n    .register(swUrl)\n    .then(registration => {\n      registration.onupdatefound = () => {\n        const installingWorker = registration.installing;\n        if (installingWorker == null) {\n          return;\n        }\n        installingWorker.onstatechange = () => {\n          if (installingWorker.state === 'installed') {\n            if (navigator.serviceWorker.controller) {\n              // At this point, the updated precached content has been fetched,\n              // but the previous service worker will still serve the older\n              // content until all client tabs are closed.\n              console.log(\n                'New content is available and will be used when all ' +\n                  'tabs for this page are closed. See https://bit.ly/CRA-PWA.'\n              );\n\n              // Execute callback\n              if (config && config.onUpdate) {\n                config.onUpdate(registration);\n              }\n            } else {\n              // At this point, everything has been precached.\n              // It's the perfect time to display a\n              // \"Content is cached for offline use.\" message.\n              console.log('Content is cached for offline use.');\n\n              // Execute callback\n              if (config && config.onSuccess) {\n                config.onSuccess(registration);\n              }\n            }\n          }\n        };\n      };\n    })\n    .catch(error => {\n      console.error('Error during service worker registration:', error);\n    });\n}\n\nfunction checkValidServiceWorker(swUrl, config) {\n  // Check if the service worker can be found. If it can't reload the page.\n  fetch(swUrl, {\n    headers: { 'Service-Worker': 'script' },\n  })\n    .then(response => {\n      // Ensure service worker exists, and that we really are getting a JS file.\n      const contentType = response.headers.get('content-type');\n      if (\n        response.status === 404 ||\n        (contentType != null && contentType.indexOf('javascript') === -1)\n      ) {\n        // No service worker found. Probably a different app. Reload the page.\n        navigator.serviceWorker.ready.then(registration => {\n          registration.unregister().then(() => {\n            window.location.reload();\n          });\n        });\n      } else {\n        // Service worker found. Proceed as normal.\n        registerValidSW(swUrl, config);\n      }\n    })\n    .catch(() => {\n      console.log(\n        'No internet connection found. App is running in offline mode.'\n      );\n    });\n}\n\nexport function unregister() {\n  if ('serviceWorker' in navigator) {\n    navigator.serviceWorker.ready\n      .then(registration => {\n        registration.unregister();\n      })\n      .catch(error => {\n        console.error(error.message);\n      });\n  }\n}\n", "import { APP_CONFIG } from \"../config/config\";\nimport intl from \"react-intl-universal\";\n\nfunction getBreadCrumbTitle(account) {\n  return  account.name + ` #${account.code}`;\n}\n\nexport function generateBreadcrumbs(openedAccountId, isCat, cats, accounts) {\n  if (openedAccountId == -1) {\n    window.updateBreadCrumbs([\n      { link: \"/v2/owner/chart-of-accounts/cats/-1\", title: intl.get('Chart of Accounts') },\n    ]);\n    return;\n  }\n  let bc = [];\n  let openedAccount;\n  if (isCat) {\n    openedAccount = cats[openedAccountId];\n    if (openedAccount) {\n      bc.push({ link: `/v2/owner/chart-of-accounts/cats/${openedAccountId}`, title: getBreadCrumbTitle(openedAccount) });\n    }\n  } else {\n    openedAccount = accounts[openedAccountId];\n    if (openedAccount) {\n      bc.push({\n        link: `/v2/owner/chart-of-accounts/accounts/${openedAccountId}`,\n        title: getBreadCrumbTitle(openedAccount),\n      });\n    }\n  }\n  let parentAccount;\n  if (openedAccount) {\n    while ((parentAccount = cats[openedAccount.journal_cat_id]) !== undefined) {\n      if (isEmptyObject(parentAccount) || !parentAccount.id) {\n        break;\n      }\n      bc.unshift({\n        link: `/v2/owner/chart-of-accounts/cats/${parentAccount.id}`,\n        title: getBreadCrumbTitle(parentAccount),\n      });\n      openedAccount = parentAccount;\n    }\n  }\n  bc.unshift({ link: \"/v2/owner/chart-of-accounts/cats/-1\", title: intl.get(\"Chart of Accounts\") });\n  window.updateBreadCrumbs(bc);\n}\n\nfunction isEmptyObject(obj) {\n  return Object.keys(obj).length === 0 && obj.constructor === Object;\n}\n", "import {\r\n  EXPAND_PARENTS,\r\n  FETCHING_ACCOUNT_PARENTS,\r\n  GENERATE_BREAD_CRUMBS,\r\n  LOADING_CHILD_ACCOUNTS,\r\n  SET_ACCOUNT_DATA,\r\n  SET_ACCOUNT_PARENTS,\r\n  SET_ACCOUNTS,\r\n  SET_BRANCHES,\r\n  SET_CAT_DATA,\r\n  SET_CATS,\r\n  SET_IGNORE_CALC_CATS,\r\n  SET_CHILD_ACCOUNTS,\r\n  SET_CHILD_CATS,\r\n  SET_EXPANDED_STATUS,\r\n  SET_OPENED_ACCOUNT,\r\n  SET_PAGINATION,\r\n  LOADING_ROUTING_DATA,\r\n  SET_ROUTING_DATA, TOGGLE_SHOW_HIDDEN_ACCOUNTS,\r\n} from \"../actions/accounts\";\r\nimport { generateBreadcrumbs } from \"../utils/bread-crumbs-util\";\r\n\r\nconst { GET_TREE_ACCOUNTS } = require(\"../actions/accounts\");\r\n\r\nconst initialState = {\r\n  showHiddenAccounts: false,\r\n  pagination: {},\r\n  branches: [],\r\n  treeAccounts: {},\r\n  parentCats: {},\r\n  parentAccounts: {},\r\n  accounts: {},\r\n  cats: {},\r\n  expandedAccounts: {},\r\n  openedAccount: { is_cat: true, id: -1 },\r\n  fetchingAccountParents: {},\r\n  loadingAccounts: {},\r\n  loadingRoutingData: false,\r\n  accountsRoutingData: {},\r\n  catsRoutingData:{},\r\n  ignoreCalculateAccounts: false,\r\n};\r\n\r\nexport const accountsReducer = (state = initialState, action) => {\r\n  switch (action.type) {\r\n    case LOADING_ROUTING_DATA:\r\n      return {\r\n        ...state,\r\n        loadingRoutingData: true\r\n      };\r\n      break;\r\n    case SET_ROUTING_DATA:\r\n      if(action.payload.isCat) {\r\n        return {\r\n          ...state,\r\n          loadingRoutingData: false,\r\n          catsRoutingData: {\r\n            ...state.catsRoutingData,\r\n            [action.payload.accountId]: action.payload.data\r\n          }\r\n        }\r\n      } else {\r\n        return {\r\n          ...state,\r\n          loadingRoutingData: false,\r\n          accountsRoutingData: {\r\n            ...state.accountsRoutingData,\r\n            [action.payload.accountId]: action.payload.data\r\n          }\r\n        }\r\n      }\r\n      break;\r\n    case GET_TREE_ACCOUNTS:\r\n      return {\r\n        ...state,\r\n        treeAccounts: {},\r\n      };\r\n      break;\r\n    case SET_CHILD_ACCOUNTS:\r\n      var parentId = action.payload.parent_id;\r\n      var childs = action.payload.child_accounts.map((child) => {\r\n        return child.JournalAccount;\r\n      });\r\n      return {\r\n        ...state,\r\n        parentAccounts: {\r\n          ...state.parentAccounts,\r\n          [parentId]: childs,\r\n        },\r\n        cats: {\r\n          ...state.cats,\r\n          [parentId]: { ...state.cats[parentId], children_loaded: true },\r\n        },\r\n      };\r\n      break;\r\n    case SET_CHILD_CATS:\r\n      var parentId = action.payload.parent_id;\r\n      var childs = action.payload.child_cats.map((child) => {\r\n        return child.JournalCat;\r\n      });\r\n\r\n      return {\r\n        ...state,\r\n        parentCats: {\r\n          ...state.parentCats,\r\n          [parentId]: childs,\r\n        },\r\n        cats: {\r\n          ...state.cats,\r\n          [parentId]: { ...state.cats[parentId], children_loaded: true },\r\n        },\r\n      };\r\n      break;\r\n    case SET_ACCOUNTS:\r\n      const newAccounts = action.payload;\r\n      let accountsMappedById = {};\r\n      for (let prop in newAccounts) {\r\n        accountsMappedById[newAccounts[prop].JournalAccount.id] =\r\n          newAccounts[prop].JournalAccount;\r\n      }\r\n      return {\r\n        ...state,\r\n        accounts: { ...state.accounts, ...accountsMappedById },\r\n      };\r\n      break;\r\n    case SET_CATS:\r\n      const newCats = action.payload;\r\n      let catsMappedById = {};\r\n      for (let prop in newCats) {\r\n        catsMappedById[newCats[prop].JournalCat.id] = newCats[prop].JournalCat;\r\n      }\r\n      return {\r\n        ...state,\r\n        cats: { ...state.cats, ...catsMappedById },\r\n      };\r\n      break;\r\n    case SET_IGNORE_CALC_CATS:\r\n      const ignoreCalcCats = action.payload;\r\n      return {\r\n        ...state,\r\n        ignoreCalcCats: ignoreCalcCats,\r\n      };\r\n      break;\r\n    case SET_OPENED_ACCOUNT:\r\n      return {\r\n        ...state,\r\n        openedAccount: {\r\n          ...{\r\n            is_cat: action.payload.is_cat,\r\n            id: parseInt(action.payload.account_id),\r\n          },\r\n        },\r\n      };\r\n      break;\r\n    case SET_ACCOUNT_DATA:\r\n      return {\r\n        ...state,\r\n        accounts: {\r\n          ...state.accounts,\r\n          [action.payload.id]: { ...action.payload.data },\r\n        },\r\n      };\r\n    case SET_CAT_DATA:\r\n      return {\r\n        ...state,\r\n        cats: {\r\n          ...state.cats,\r\n          [action.payload.id]: { ...action.payload.data },\r\n        },\r\n      };\r\n      break;\r\n    case TOGGLE_SHOW_HIDDEN_ACCOUNTS:\r\n        return {\r\n            ...state,\r\n            showHiddenAccounts: !state.showHiddenAccounts,\r\n        };\r\n        break;\r\n    case LOADING_CHILD_ACCOUNTS:\r\n      return {\r\n        ...state,\r\n        loadingAccounts: {\r\n          ...state.loadingAccounts,\r\n          [action.payload.cat]: action.payload.page,\r\n        },\r\n      };\r\n      break;\r\n    case SET_ACCOUNT_PARENTS:\r\n      let newParentCats = {};\r\n      let newParentAccounts = {};\r\n      let newCatsToAdd = {};\r\n      let newAccountsToAdd = {};\r\n      for (let cat in action.payload.cats) {\r\n        newParentCats = {\r\n          ...newParentCats,\r\n          [cat]: [...action.payload.cats[cat]],\r\n        };\r\n        for (let childKey in action.payload.cats[cat]) {\r\n          let child = action.payload.cats[cat][childKey];\r\n          newCatsToAdd[child.id] = { ...child };\r\n        }\r\n        newParentAccounts = {\r\n          ...newParentAccounts,\r\n          [cat]: [...action.payload.accounts[cat]],\r\n        };\r\n        for (let childKey in action.payload.accounts[cat]) {\r\n          let child = action.payload.accounts[cat][childKey];\r\n          newAccountsToAdd[child.id] = { ...child };\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        parentAccounts: {\r\n          ...state.parentAccounts,\r\n          ...newParentAccounts,\r\n        },\r\n        parentCats: {\r\n          ...state.parentCats,\r\n          ...newParentCats,\r\n        },\r\n        cats: {\r\n          ...state.cats,\r\n          ...newCatsToAdd,\r\n        },\r\n        accounts: {\r\n          ...state.accounts,\r\n          ...newAccountsToAdd,\r\n        },\r\n      };\r\n      break;\r\n    case EXPAND_PARENTS:\r\n      let parentsToExpand = {};\r\n      action.payload.forEach((el) => {\r\n        parentsToExpand[el] = true;\r\n      });\r\n      return {\r\n        ...state,\r\n        expandedAccounts: { ...state.expandedAccounts, ...parentsToExpand },\r\n      };\r\n      break;\r\n    case SET_BRANCHES:\r\n      const branches = action.payload.filter((branch) => {\r\n        if (branch.Branch.status == 1 || branch.Branch.status == 3) {\r\n          return branch.Branch;\r\n        }\r\n      });\r\n      const branchesFlat = branches.map(branch => branch.Branch);\r\n      return {\r\n        ...state,\r\n        branches: [...branchesFlat],\r\n      };\r\n      break;\r\n    case SET_EXPANDED_STATUS:\r\n      return {\r\n        ...state,\r\n        expandedAccounts: {\r\n          ...state.expandedAccounts,\r\n          [action.payload.account_id]: action.payload.status,\r\n        },\r\n      };\r\n      break;\r\n    case GENERATE_BREAD_CRUMBS:\r\n      generateBreadcrumbs(\r\n        action.payload.account_id,\r\n        action.payload.is_cat,\r\n        state.cats,\r\n        state.accounts\r\n      );\r\n      return state;\r\n    case FETCHING_ACCOUNT_PARENTS:\r\n      // action.payload\r\n      return {\r\n        ...state,\r\n        fetchingAccountParents: {\r\n          ...state.fetchingAccountParents,\r\n          [action.payload.join(\",\")]: true,\r\n        },\r\n      };\r\n    case SET_PAGINATION:\r\n      return {\r\n        ...state,\r\n        pagination: {\r\n          ...state.pagination,\r\n          [action.payload.catId]: action.payload.pagination,\r\n        },\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n", "import { combineReducers } from \"redux\";\r\nimport { accountsReducer } from \"./accounts\";\r\nexport default combineReducers({\r\n  accountsReducer,\r\n});\r\n", "import React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport * as serviceWorker from \"./serviceWorker\";\nimport { Provider } from \"react-redux\";\nimport { applyMiddleware, createStore } from \"redux\";\nimport rootReducer from \"./reducers\";\nimport thunk from \"redux-thunk\";\nimport { BrowserRouter as Router } from \"react-router-dom\";\nimport { APP_CONFIG } from \"./config/config\";\nimport \"./index.css\";\nexport const store = createStore(rootReducer, applyMiddleware(thunk));\nReactDOM.render(\n  <Router basename={APP_CONFIG.LINK_PREFIX}>\n    <Provider store={store}>\n      <App />\n    </Provider>\n  </Router>,\n  document.getElementById(\"root\")\n);\n\nserviceWorker.unregister();\n", "import appAxios from \"../utils/app-axios\";\r\nimport { store } from \"../index\";\r\nimport { APP_CONFIG } from \"../config/config\";\r\nimport intl from \"react-intl-universal\";\r\nexport const GET_TREE_ACCOUNTS = \"GET_TREE_ACCOUNTS\";\r\nexport const GET_ACCOUNTS = \"GET_ACCOUNTS\";\r\nexport const SET_CHILD_ACCOUNTS = \"SET_CHILD_ACCOUNTS\";\r\nexport const SET_CHILD_CATS = \"SET_CHILD_CATS\";\r\nexport const SET_ACCOUNTS = \"SET_ACCOUNTS\";\r\nexport const SET_CATS = \"SET_CATS\";\r\nexport const SET_IGNORE_CALC_CATS = \"SET_IGNORE_CALC_CATS\";\r\nexport const SET_OPENED_ACCOUNT = \"SET_OPENED_ACCOUNT\";\r\nexport const SET_CAT_DATA = \"SET_CAT_DATA\";\r\nexport const SET_ACCOUNT_DATA = \"SET_ACCOUNT_DATA\";\r\nexport const LOADING_CHILD_ACCOUNTS = \"LOADING_CHILD_ACCOUNTS\";\r\nexport const EXPAND_PARENTS = \"EXPAND_PARENTS\";\r\nexport const SET_ACCOUNT_PARENTS = \"SET_ACCOUNT_PARENTS\";\r\nexport const SET_BRANCHES = \"SET_BRANCHES\";\r\nexport const SET_EXPANDED_STATUS = \"SET_EXPANDED_STATUS\";\r\nexport const GENERATE_BREAD_CRUMBS = \"GENERATE_BREAD_CRUMBS\";\r\nexport const FETCHING_ACCOUNT_PARENTS = \"FETCHING_ACCOUNT_PARENTS\";\r\nexport const SET_PAGINATION = \"SET_PAGINATION\";\r\nexport const SET_ROUTING_DATA = 'SET_ROUTING_DATA';\r\nexport const LOADING_ROUTING_DATA = 'LOADING_ROUTING_DATA';\r\n\r\nexport const TOGGLE_SHOW_HIDDEN_ACCOUNTS = \"TOGGLE_SHOW_HIDDEN_ACCOUNTS\";\r\n\r\nexport function getTreeAccounts() {\r\n  return { type: GET_TREE_ACCOUNTS };\r\n}\r\nexport function getAccounts() {\r\n  return { type: GET_ACCOUNTS };\r\n}\r\n\r\nexport function loadingRoutingData() {\r\n  return {type: LOADING_ROUTING_DATA};\r\n}\r\n\r\nexport function setRoutingData(accountId,accountData, isCat = false) {\r\n  return {\r\n    type: SET_ROUTING_DATA,\r\n    payload: {accountId, isCat, data: accountData}\r\n  };\r\n}\r\n\r\nexport function setChildAccounts(parentId, childAccounts) {\r\n  return {\r\n    type: SET_CHILD_ACCOUNTS,\r\n    payload: { parent_id: parentId, child_accounts: childAccounts },\r\n  };\r\n}\r\n\r\nexport function setChildCats(parentId, childCats) {\r\n  return {\r\n    type: SET_CHILD_CATS,\r\n    payload: { parent_id: parentId, child_cats: childCats },\r\n  };\r\n}\r\n\r\nexport function setCats(cats) {\r\n  return { type: SET_CATS, payload: cats };\r\n}\r\n\r\nexport function setCaluclateCats(ignoreCalculateCats) {\r\n  return { type: SET_IGNORE_CALC_CATS, payload: ignoreCalculateCats };\r\n}\r\n\r\nexport function setAccounts(accounts) {\r\n  return { type: SET_ACCOUNTS, payload: accounts };\r\n}\r\n\r\nexport function setOpenedAccount(account_id, isCat) {\r\n  return {\r\n    type: SET_OPENED_ACCOUNT,\r\n    payload: { is_cat: isCat, account_id: account_id },\r\n  };\r\n}\r\n\r\nexport function fetchChildren(parent_id, page) {\r\n  return appAxios.get(\r\n    APP_CONFIG.BASE_URL + `api2/journals/accounts/${parent_id}?page=${page}`\r\n  );\r\n}\r\n\r\nexport function loadingChildAccounts(catId, page) {\r\n  return {\r\n    type: LOADING_CHILD_ACCOUNTS,\r\n    payload: { cat: catId, page: page },\r\n  };\r\n}\r\n\r\nexport function setCatData(cat_id, cat_data) {\r\n  return {\r\n    type: SET_CAT_DATA,\r\n    payload: { id: cat_id, data: cat_data },\r\n  };\r\n}\r\n\r\nexport function setAccountData(account_id, account_data) {\r\n  return {\r\n    type: SET_ACCOUNT_DATA,\r\n    payload: { id: account_id, data: account_data },\r\n  };\r\n}\r\n\r\nexport function fetchAccountData(account_id) {\r\n  return appAxios.get(\r\n    `${APP_CONFIG.BASE_URL}api2/journal_accounts/${account_id}`\r\n  );\r\n}\r\n\r\nexport function getAccountData(account_id) {\r\n  return (dispatch) => {\r\n    return fetchAccountData(account_id).then((res) => {\r\n      dispatch(setAccountData(account_id, res.data.data.JournalAccount));\r\n    });\r\n  };\r\n}\r\n\r\nexport function expandParents(parent_ids) {\r\n  return { type: EXPAND_PARENTS, payload: parent_ids };\r\n}\r\nexport function fetchAccountParents(parentIds) {\r\n  return { type: FETCHING_ACCOUNT_PARENTS, payload: parentIds };\r\n}\r\n\r\nexport function toggleShowHiddenAccounts() {\r\n  return { type: TOGGLE_SHOW_HIDDEN_ACCOUNTS };\r\n}\r\nexport function loadAccountParents(\r\n  accountParents = [],\r\n  addAccountToExpanded = null\r\n) {\r\n  const accountsRed = store.getState().accountsReducer;\r\n  if (accountsRed.fetchingAccountParents[accountParents.join(\",\")]) {\r\n    return { type: null };\r\n  }\r\n  return (dispatch) => {\r\n    dispatch(fetchAccountParents(accountParents));\r\n    return appAxios\r\n      .get(`${APP_CONFIG.BASE_URL}api2/journals/accounts/`, {\r\n        params: {\r\n          account_ids: accountParents,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        dispatch(\r\n          setAccountParents({\r\n            accounts: res.data.accounts,\r\n            cats: res.data.cats,\r\n            parents: res.data.parents,\r\n          })\r\n        );\r\n        const parentsToExpand = addAccountToExpanded\r\n          ? [...accountParents, addAccountToExpanded]\r\n          : accountParents;\r\n        dispatch(expandParents(parentsToExpand));\r\n      });\r\n  };\r\n}\r\n\r\nexport function setBranches(branches) {\r\n  return { type: SET_BRANCHES, payload: branches };\r\n}\r\n\r\n\r\nexport function getRoutingData(accountId, isCat) {\r\n  return (dispatch) => {\r\n    dispatch(loadingRoutingData());\r\n    appAxios.get(`${APP_CONFIG.BASE_URL}v2/api/accounting/${accountId}/account-details?is_cat=${isCat ? 1 : 0}`).then((res) => {\r\n      dispatch(setRoutingData(accountId, res.data, isCat));\r\n    });\r\n  }\r\n}\r\n\r\nexport function getBranches() {\r\n  return (dispatch) => {\r\n    appAxios.get(`${APP_CONFIG.BASE_URL}api2/branches/`).then((res) => {\r\n      let branchesWithAll = [];\r\n      try {\r\n        if(res.data.data.length > 0) {\r\n          branchesWithAll = [\r\n            { Branch: { id: -1, name: intl.get(\"All Branches\"), status: 1 } },\r\n            ...res.data.data,\r\n          ];\r\n        }\r\n      } catch (e) {\r\n        const branchesWithAll = [];\r\n      }\r\n      dispatch(setBranches(branchesWithAll));\r\n    });\r\n  };\r\n}\r\n\r\nexport function setAccountParents(\r\n  data = { accounts: {}, cats: {}, parents: [] }\r\n) {\r\n  return {\r\n    type: SET_ACCOUNT_PARENTS,\r\n    payload: {\r\n      accounts: data.accounts,\r\n      cats: data.cats,\r\n      parents: data.parents,\r\n    },\r\n  };\r\n}\r\n//unused\r\n\r\nexport function setExpandedStatus(catId, expandedStatus) {\r\n  return {\r\n    type: SET_EXPANDED_STATUS,\r\n    payload: { account_id: catId, status: expandedStatus },\r\n  };\r\n}\r\n\r\nexport function setPagination(catId, pagingData) {\r\n  return {\r\n    type: SET_PAGINATION,\r\n    payload: { catId: catId, pagination: pagingData },\r\n  };\r\n}\r\n\r\nexport function getChildAccounts(catId, page = 1) {\r\n  const accountsRed = store.getState().accountsReducer;\r\n  if (accountsRed.loadingAccounts[catId] == page) {\r\n    return { type: null };\r\n  }\r\n  return (dispatch) => {\r\n    dispatch(loadingChildAccounts(catId, page));\r\n    return fetchChildren(catId, page).then((res) => {\r\n      dispatch(setChildAccounts(catId, res.data.accounts));\r\n      dispatch(setChildCats(catId, res.data.cats));\r\n      dispatch(setCats(res.data.cats));\r\n      dispatch(setCaluclateCats(res.data.ignoreCalculateAccounts));\r\n      dispatch(setAccounts(res.data.accounts));\r\n      dispatch(\r\n        setCatData(\r\n          catId,\r\n          res.data.parent_account ? res.data.parent_account.JournalCat : false\r\n        )\r\n      );\r\n      dispatch(setPagination(catId, res.data.pagination));\r\n    });\r\n  };\r\n}\r\n\r\nexport function generateBreadCrumbs(accountId, isCat) {\r\n  return {\r\n    type: GENERATE_BREAD_CRUMBS,\r\n    payload: { account_id: accountId, is_cat: isCat },\r\n  };\r\n}\r\n"], "sourceRoot": ""}