{"proceed": "Proceed", "next": "Next", "cancel": "Cancel", "how-to-get-file-from-mudad": "How to Get File from Mudad", "upload-mudad-file": "File To Import", "instructions": "Help & Instructions:", "instructions-1": "Login to your Mudad System using your credentials", "instructions-2": "Navigate to Payroll managment system (Image 1)", "instructions-3": "Click on Create Payroll (Image 2)", "instructions-4": "Choose the Month of the payroll (Image 3)", "instructions-5": "Click on Upload payrun File (Image 4)", "instructions-6": "Click on Download Sample (Image 5)", "instructions-7": "Upload the Downloaded sample on Daftara (Image 6)", "instructions-note": "For detailed instructions, please refer to the Mudad Integration Guide in the Help Center or contact support", "drop-file-here": "Drop File here or", "select-from-device": "select from your device", "supported-formats": "Supported Formats: xlsx", "drop-here": "Drop here", "choose-file": "Choose <PERSON>", "successfully-attached": "File successfully attached", "file-information": "Attached Mudad File", "file-type": "File Type", "uploaded-by": "Uploaded By", "period": "Period", "uploading": "Uploading...", "file-size": "File size", "uploaded-on": "Uploaded on", "step-1-label": "Attach <PERSON>", "step-1-description": "Upload Mudad Monthly Sheet to initiate the payroll processing workflow.", "step-2-label": "Map Columns", "step-2-description": "Only editable Mudad fields are shown. Fields marked as system-protected in the file are excluded.", "step-3-label": "Review", "step-3-description": "Review how Mudad file data maps to Daftra payroll records.", "step-4-label": "Generate", "step-4-description": "Final review the output file and extract it in the required Mudad format.", "coming-soon": "Soon", "toast-uploading-file": "Uploading file...", "toast-file-upload-success": "File {{name}} uploaded successfully", "toast-file-upload-error": "Failed to upload file", "toast-invalid-file-type": "Invalid file type", "toast-invalid-file-size": "File size must be less than 10 MB", "toast-loading": "Loading ...", "toast-loaded-success": "Loaded successfully", "toast-save-success": "Saved successfully", "toast-load-error": "Failed to load", "toast-save-error": "Failed to save", "toast-deleting-file": "Deleting file...", "toast-delete-file-success": "File deleted successfully", "toast-delete-file-error": "Failed to delete file", "how-to-upload-file-to-mudad": "How to Upload File to Mudad", "download-final-file": "Download Final File", "mudad-standard-fields": "Mudad Standard Fields", "daftra-salary-components": "Daftra Salary Components", "enerpize-salary-components": "Enerpize Salary Components", "salary-components": "Salary Components", "select-components": "Select components...", "error-fetch-mappings": "An error occurred while fetching current mappings", "no-results-found": "No results found", "search-placeholder": "Please enter 1 keyword or more to search", "mapping-summary": "Mapping Summary", "error-cant-proceed": "Can't proceed due to several errors.", "employee-mapping": "Employee Mapping", "mudad-salary": "Mudad Salary", "additions": "Additions", "earnings": "Earnings", "deductions": "Deductions", "number-abbreviation": "No.", "employee-on-mudad": "Employee on Mudad", "hawey-id-on-mudad": "Hawey ID on Mudad", "daftra-employee": "Daftra Employee", "enerpize-employee": "Enerpize Employee", "employee": "Employee", "net-salary": "Net Salary", "not-mapped": "Not Mapped", "refresh-table": "Refresh Table", "something-went-wrong": "Something went wrong", "we-encountered-an-error-while-loading-the-data-please-try-again-later": "We encountered an error while loading the data. Please try again later.", "toast-success": "Successfully saved", "users-found-in-mudad-file": "Users Found in Mudad File", "mapped-users": "Mapped Users", "unmapped-users": "Unmapped Users", "toast-employee-already-mapped": "Employee already mapped", "toast-please-fix-errors": "Fix all the errors first to be able to proceed", "review-and-extract": "Review & Extract", "modad-file-exported-successfully": "Modad File Exported Successfully", "updating-official-id": "Updating Official ID", "you-are-about-to-make-the-following-changes-to-hawey-ids": "You are about to make the following changes to Haweya IDs:", "you-are-about-to-update-the-hawey-id-for": "You are about to update the Haweya ID for"}