function addBomOptions($bomSelect, boms) {
    let defaultId = 0;
    $bomSelect[0].selectize.setValue([]);
    $bomSelect[0].selectize.clearOptions();    // Clears all options from dropdown
    $bomSelect[0].selectize.clear(true);       // Clears the current selection
    $bomSelect[0].selectize.renderCache = {};  // Clear the render cache to fully reset

    boms.forEach(function(item) {
        defaultId = item.is_default == 1 ? item.id : defaultId;
        let text = item.name;
        if(item.code) {
            text = '#' + item.code + ' ' + text;
        }
        $bomSelect[0].selectize.addOption(
            {
                id: item.id,
                text: text
            }
        );
    });
    if(defaultId) {
        $bomSelect[0].selectize.addItem(defaultId);
    }
}

function addUnitFactorOptions($unitFactorSelect, factors) {
    $unitFactorSelect[0].selectize.clearOptions();
    factors.forEach(function(item) {
        $unitFactorSelect[0].selectize.addOption(
            {
                id: item.id,
                text: item.small_name
            }
        );
    });
    if(factors[0]){
        $unitFactorSelect[0].selectize.setValue(factors[0].id)
    }
}
var gSelectedItems = [];
let selectProductChanged = function($row, $bomSelect, $unitFactorSelect, selectedItem) {
    gSelectedItems.push(selectedItem);
    $bomSelect[0].selectize.clearOptions();
    $unitFactorSelect[0].selectize.clearOptions();
    let bom = selectedItem.activeBoms;
    if(!selectedItem.activeBoms && _bomOptions[selectedItem.id]) {
        bom = [];
        let boms = _bomOptions[selectedItem.id]
        boms.forEach(function(item) {
            bom.push({id: item.value, name: item.text, is_default: item.is_default});
        });
    }
    if(selectedItem && bom && bom.length) {
        addBomOptions($bomSelect, bom);
    }
    let units = selectedItem.units;
    if(!selectedItem.units && _unitFactors[selectedItem.id]) {
        units = [];
        let factors = _unitFactors[selectedItem.id];
        factors.forEach(function(item) {
            units.push({id: item.value, small_name: item.text});
        });
    }
    if(selectedItem && units && units.length) {
        $row.find('.unit-factor-span').removeClass('d-none');
        $unitFactorSelect[0].selectize.clearOptions();
        addUnitFactorOptions($unitFactorSelect, units);
        if(selectedItem.unit_factor_id !== undefined) {
            $unitFactorSelect[0].selectize.addItem(selectedItem.unit_factor_id);
        }
    } else {
        $row.find('.unit-factor-span').addClass('d-none');
        $unitFactorSelect[0].selectize.clearOptions();
    }
    if(selectedItem && selectedItem.invoice_item_id) {
        $row.find('.invoice_item_id').val(selectedItem.invoice_item_id);
    }
}

let initProductionPlanItemSelectize = function(productSelector, bomSelector, unitFactorSelector) {
    let $bomSelect = $(bomSelector).selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single,{
            valueField: "id",
            labelField: "text",
        })
    );
    let $unitFactorSelect = $(unitFactorSelector).selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single,{
            valueField: "id",
            labelField: "text",
        })
    );
    let productSearchCurrentRequest = null;
    let $productSelect = $(productSelector).selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single, {
            valueField: "id",
            labelField: "text",
            onChange: function(value) {
                if (!value.length) return;  // If no value is selected, return early
                var selectedItem = this.options[value];
                $parentRow = $(productSelector).parents('tr');
                selectProductChanged($parentRow, $bomSelect, $unitFactorSelect, selectedItem);
            },
            onInitialize: function () {
            },
            load: function (query, callback) {
                var url = '/v2/owner/products/ajax_products_filter_v2?search=__q__&status=0&exclude[type]=2&added_relations[]=activeBoms';
                this.clearOptions();
                if (!query.length) return callback();
                if(productSearchCurrentRequest) {
                    productSearchCurrentRequest.abort();
                }
                productSearchCurrentRequest = $.ajax({
                    url: url.replaceAll('__q__', encodeURIComponent(query)),
                    type: "GET",
                    dataType: 'json',
                    error: function () {
                        callback();
                    },
                    success: function (response) {
                        if (response) {
                            callback(response);
                        } else {
                            callback();
                        }
                    },
                });
            },
            plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single.plugins, {
                'template': {
                    'name': 'user'
                }
            }),
            render: {
                option: function (data, escape) {
                    return '<div title="'+ escape(data.text) +'" class="option"><span class="thumb-text-group"><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
                },
                item: function (data, escape) {
                    return '<div title="'+ escape(data.text) +'" class="item"><span class="thumb-text-group"><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
                },
            },
        }
    ));
}

$(function() {
    $('.save-as-draft').on("click", function(e){
        let formJqueryElement = $('#productionPlanForm');
        e.preventDefault()
        let autoNumberJqueryElement = $(`[name="code[code]"]`);
        let oldAutoNumberValue = autoNumberJqueryElement.val();
        formJqueryElement.append('<input type="hidden" name="is_draft_entity" value="1" />');
        formJqueryElement.append('<input type="hidden" name="is_draft" value="1" />');
        formJqueryElement.submit();
        if(formJqueryElement.appValidator) {
            let errorsCount = Object.keys(formJqueryElement.appValidator.errors).length
            if(errorsCount){
                formJqueryElement.find(`input[name="is_draft_entity"]`).remove()
                formJqueryElement.find(`input[name="is_draft"]`).remove()
                autoNumberJqueryElement.val(oldAutoNumberValue)
            }
        }
    });
})


$(document).ready(function() {
    var $input = $('#source_id');
    var sourceIdCurrentRequest = null;  // Store the current AJAX request
    let sourceIdValue = $('#source_id').val();

    let $selectizeSourceId = $input.selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter, {
        valueField: "id",
        labelField: "text",
        persist: false,
        onInitialize: function () {
            let $this = this;
            $('#source_type').change(function () {
                $this.setValue([]);
                $this.clearOptions();    // Clears all options from dropdown
                $this.clear(true);       // Clears the current selection
                $this.renderCache = {};  // Clear the render cache to fully reset
                if($(this).val() == '__clear__' || $(this).val() == '') {
                    $this.disable();
                    $input.removeAttr('data-form-rules');
                } else {
                    $this.enable();
                    $input.attr('data-form-rules', 'required');
                    if($(this).val() == 'sales_order'){
                        $this.addOption(_salesOrderDefaultOptions);
                    }else{
                        $this.addOption(_invoiceDefaultOptions);
                    }
                }
            });
            if(sourceIdValue == '' || sourceIdValue == '__clear__') {
                $('#source_type').change();
            }
        },
        load: function (query, callback) {
            var url = '/v2/owner/invoices/search';
            this.clearOptions();
            let sourceType= $('#source_type').val();
            if(sourceType == 'invoice') {
                sourceType = 0;
            } else {
                sourceType = 12;
            }
            if (sourceIdCurrentRequest) {
                sourceIdCurrentRequest.abort();
            }
            if (!query.length) return callback();
            sourceIdCurrentRequest = $.ajax({
                url: url + '?with_items=1&q=' + encodeURIComponent(query) + '&type='+sourceType + '&product_types[]=1',
                type: "GET",
                dataType: 'json',
                error: function () {
                    callback();
                },
                success: function (response) {
                    if (response.results) {
                        callback(response.results);
                    } else {
                        callback();
                    }
                },
            });
        },
        onChange: function(value) {
            $('.subform-cell-remove').click();
            let selectedInvoice = this.options[value];
            if(selectedInvoice && selectedInvoice.invoice_items) {
                selectedInvoice.invoice_items.forEach(function (item) {
                    $('#addRowBtn').click();
                    //set values
                    let $row = $('[data-materials-subform] table tbody tr:last');
                    $row.find('select.select-product')[0].selectize.removeOption(item.product_id); //as if the option is already loaded from the backend the extra data we see beneath will be ignored
                    $row.find('select.select-product')[0].selectize.addOption({
                        id: item.product_id,
                        text: `# ${item.product_code} ${item.item}`,
                        invoice_item_id: item.id,
                        activeBoms: item.activeBoms,
                        units: item.units,
                        unit_factor_id: item.unit_factor_id
                    });
                    $row.find('select.select-product')[0].selectize.setValue(item.product_id);
                    let factor = 1;
                    if(item.factor) {
                        factor = item.factor;
                    }
                    let quantity = item.quantity / factor;
                    $row.find('input.item_quantity').val(quantity);
                    $row.find('select.select-product').trigger('change');
                });
            }
        },
        plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter.plugins, {
            'template': {
                'name': 'user'
            }
        }),
        render: {
            option: function (data, escape) {
                return '<div title="'+ escape(data.text) +'" class="option"><span class="thumb-text-group"><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
            },
            item: function (data, escape) {
                return '<div title="'+ escape(data.text) +'" class="item"><span class="thumb-text-group"><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
            },
        },
    }));
    if($('#source_type').attr('readonly') == "") {
        $('#source_type').change();
    } else {
        $selectizeSourceId[0].selectize.lock();
    }

    $('#client_id').selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter, {
        valueField: "id",
        labelField: "text",
        load: function (query, callback) {
            var url = '/v2/owner/clients/filter?allow_suspended=0&term=__q__&_type=query&q=__q__&fields[]=phone1';
            this.clearOptions();
            if (!query.length) return callback();
            $.ajax({
                url: url.replaceAll('__q__', encodeURIComponent(query)),
                type: "GET",
                dataType: 'json',
                error: function () {
                    callback();
                },
                success: function (response) {
                    if(Array.isArray(response)) {
                        response = {results: response};
                    }
                    if (response.results) {
                        callback(response.results);
                    } else {
                        callback();
                    }
                },
            });
        },
        score: function(search) {
            var score = this.getScoreFunction(search);
            return function(item) {
                let orgScore = score(item);
                if(item.phone1 && item.phone1.includes(search)) {
                    return orgScore + 1;
                }
                return orgScore;
            };
        },
        plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter.plugins, {
            'template': {
                'name': 'user'
            }
        }),
        render: {
            option: function (data, escape) {
                return '<div title="'+ escape(data.text) +'" class="option">' +
                    '<span class="thumb-text-group">' +
                    '<span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span>' +
                '<span class="thumb-text"><p>' + escape(data.text) + '</p>' +
                '</span>' +
                '</span>' +
                '</div>';
            },
            item: function (data, escape) {
                return '<div title="'+ escape(data.text) +'" class="item">' +
                    '<span class="thumb-text-group">' +
                    '<span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span>' +
                '<span class="thumb-text">' +
                '<p>' + escape(data.text) + '</p>' +
                '</span>' +
                '</span></div>';
            },
        },
    }));

    $('#employees').selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter, {
        valueField: "id",
        labelField: "text",
        load: function (query, callback) {
            var url = '/v2/owner/staff/search?_type=query&q=__q__&fields[]=middle_name';
            this.clearUnselectedOptions();
            if (!query.length) return callback();
            $.ajax({
                url: url.replaceAll('__q__', encodeURIComponent(query)),
                type: "GET",
                dataType: 'json',
                error: function () {
                    callback();
                },
                success: function (response) {
                    if(Array.isArray(response)) {
                        response = {results: response};
                    }
                    if (response.results) {
                        callback(response.results);
                    } else {
                        callback();
                    }
                },
            });
        },
        score: function(search) {
            var score = this.getScoreFunction(search);
            return function(item) {
                let orgScore = score(item);
                if(item.middle_name && item.middle_name.includes(search)) {
                    return orgScore + 1;
                }
                return orgScore;
            };
        },
        plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.singleFilter.plugins, {
            'template': {
                'name': 'user'
            }
        }),
        render: {
            option: function (data, escape) {
                return '<div title="'+ escape(data.text) +'" class="option">' +
                    '<span class="thumb-text-group">' +
                    '<span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span>' +
                '<span class="thumb-text"><p>' + escape(data.text) + '</p>' +
                '</span>' +
                '</span>' +
                '</div>';
            },
            item: function (data, escape) {
                return '<div title="'+ escape(data.text) +'" class="item">' +
                    '<span class="thumb-text-group">' +
                    '<span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span>' +
                '<span class="thumb-text">' +
                '<p>' + escape(data.text) + '</p>' +
                '</span>' +
                '</span></div>';
            },
        },
    }));

    $(document).on('change', '.unit-factor-select', function (e) {
        var $tr = $(this).closest('tr')
        var $productInputSelect = $tr.find("select[name$='[product_id]']")
        var selectedProductId = $productInputSelect.val();
        var unitFactorId = $(this).val();
        if (unitFactorId !== '0'  && !unitFactorId){
            $tr.find(`.unit-factor-input-js`).val(null);
            $tr.find(`.unit-small-name-input-js`).val(null);
            $tr.find(`.unit-name-input-js`).val(null);
            return;
        }
        var unitFactorData =null
        if (_unitFactors[selectedProductId]){
            unitFactorData = _unitFactors[selectedProductId].filter((item) => item.value == unitFactorId)[0]
        }else {
            unitFactorData= {};
            var selectedProductData  = gSelectedItems.filter((product)=>{ return product.id == selectedProductId})[0]
            if (!selectedProductData) return;;
            var units = selectedProductData.units;
            var selectedUnit = units.filter((un) => un.id == unitFactorId )[0];
            if (!selectedUnit) return;
            unitFactorData.factor = selectedUnit.factor;
            unitFactorData.text = selectedUnit.small_name;
            unitFactorData.unit_name = selectedUnit.factor_name;
        }
        if (!unitFactorData) return;
        $tr.find(`.unit-factor-input-js`).val(unitFactorData.factor);
        $tr.find(`.unit-small-name-input-js`).val(unitFactorData.text);
        $tr.find(`.unit-name-input-js`).val(unitFactorData.unit_name);
    });

});
