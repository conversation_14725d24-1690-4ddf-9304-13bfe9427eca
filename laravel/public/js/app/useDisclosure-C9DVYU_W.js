import{h,r as l,aB as D,aC as M,aD as z,Y as I,d as b,R as c,l as L,a4 as x,a2 as N,u as w,aE as g,s as V,aF as O,n as R,e as C,aG as k,aH as B,o as F}from"./i18n-DJdGDBdH.js";let r=typeof document<"u"&&window.visualViewport;function H(){let t=h(),[e,a]=l.useState(()=>t?{width:0,height:0}:m());return l.useEffect(()=>{let i=()=>{a(n=>{let d=m();return d.width===n.width&&d.height===n.height?n:d})};return r?r.addEventListener("resize",i):window.addEventListener("resize",i),()=>{r?r.removeEventListener("resize",i):window.removeEventListener("resize",i)}},[]),e}function m(){return{width:r&&(r==null?void 0:r.width)||window.innerWidth,height:r&&(r==null?void 0:r.height)||window.innerHeight}}function U(t,e,a){let{overlayProps:i,underlayProps:n}=D({...t,isOpen:e.isOpen,onClose:e.close},a);return M({isDisabled:!e.isOpen}),z(),l.useEffect(()=>{if(e.isOpen&&a.current)return I([a.current],{shouldUseInert:!0})},[e.isOpen,a]),{modalProps:b(i),underlayProps:n}}const W=l.createContext(null),v=l.createContext(null),T=l.forwardRef(function(e,a){if(l.useContext(v))return c.createElement(E,{...e,modalRef:a},e.children);let{isDismissable:n,isKeyboardDismissDisabled:d,isOpen:o,defaultOpen:s,onOpenChange:$,children:f,isEntering:u,isExiting:p,UNSTABLE_portalContainer:y,shouldCloseOnInteractOutside:P,...S}=e;return c.createElement(A,{isDismissable:n,isKeyboardDismissDisabled:d,isOpen:o,defaultOpen:s,onOpenChange:$,isEntering:u,isExiting:p,UNSTABLE_portalContainer:y,shouldCloseOnInteractOutside:P},c.createElement(E,{...S,modalRef:a},f))});function j(t,e){[t,e]=L(t,e,W);let a=l.useContext(x),i=N(t),n=t.isOpen!=null||t.defaultOpen!=null||!a?i:a,d=w(e),o=l.useRef(null),s=g(d,n.isOpen),$=g(o,n.isOpen),f=s||$||t.isExiting||!1,u=h();return!n.isOpen&&!f||u?null:c.createElement(G,{...t,state:n,isExiting:f,overlayRef:d,modalRef:o})}const A=l.forwardRef(j);function G({UNSTABLE_portalContainer:t,...e}){let a=e.modalRef,{state:i}=e,{modalProps:n,underlayProps:d}=U(e,i,a),o=O(e.overlayRef)||e.isEntering||!1,s=R({...e,defaultClassName:"react-aria-ModalOverlay",values:{isEntering:o,isExiting:e.isExiting,state:i}}),$=H(),f={...s.style,"--visual-viewport-height":$.height+"px"};return c.createElement(B,{isExiting:e.isExiting,portalContainer:t},c.createElement("div",{...b(C(e,{global:!0}),d),...s,style:f,ref:e.overlayRef,"data-entering":o||void 0,"data-exiting":e.isExiting||void 0},c.createElement(F,{values:[[v,{modalProps:n,modalRef:a,isExiting:e.isExiting,isDismissable:e.isDismissable}],[x,i]]},s.children)))}function E(t){let{modalProps:e,modalRef:a,isExiting:i,isDismissable:n}=l.useContext(v),d=l.useContext(x),o=l.useMemo(()=>V(t.modalRef,a),[t.modalRef,a]),s=w(o),$=O(s),f=R({...t,defaultClassName:"react-aria-Modal",values:{isEntering:$,isExiting:i,state:d}});return c.createElement("div",{...b(C(t,{global:!0}),e),...f,ref:s,"data-entering":$||void 0,"data-exiting":i||void 0},n&&c.createElement(k,{onDismiss:d.close}),f.children)}const Y=(t=!1)=>{const[e,a]=l.useState(t),i=l.useCallback(()=>a(!0),[]),n=l.useCallback(()=>a(!1),[]);return{isOpen:e,onOpen:i,onClose:n}};export{A as $,T as a,Y as u};
