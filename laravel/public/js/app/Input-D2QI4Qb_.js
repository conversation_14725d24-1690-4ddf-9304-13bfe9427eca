import{f as V,b as F,e as M,r as c,a6 as S,d as m,R as x,y as D,l as R,w as B,m as L,n as O}from"./i18n-DJdGDBdH.js";import{w as T,B as q,$ as k,C as w}from"./Select-XdalPTad.js";function j(e,a){let{inputElementType:i="input",isDisabled:o=!1,isRequired:t=!1,isReadOnly:d=!1,type:r="text",validationBehavior:l="aria"}=e,[n,s]=V(e.value,e.defaultValue||"",e.onChange),{focusableProps:C}=F(e,a),b=T({...e,value:n}),{isInvalid:f,validationErrors:v,validationDetails:P}=b.displayValidation,{labelProps:h,fieldProps:g,descriptionProps:y,errorMessageProps:I}=q({...e,isInvalid:f,errorMessage:e.errorMessage||v}),p=M(e,{labelable:!0});const E={type:r,pattern:e.pattern};let[H]=c.useState(n);var $;return k(a,($=e.defaultValue)!==null&&$!==void 0?$:H,s),w(e,b,a),c.useEffect(()=>{if(a.current instanceof S(a.current).HTMLTextAreaElement){let u=a.current;Object.defineProperty(u,"defaultValue",{get:()=>u.value,set:()=>{},configurable:!0})}},[a]),{labelProps:h,inputProps:m(p,i==="input"?E:void 0,{disabled:o,readOnly:d,required:t&&l==="native","aria-required":t&&l==="aria"||void 0,"aria-invalid":f||void 0,"aria-errormessage":e["aria-errormessage"],"aria-activedescendant":e["aria-activedescendant"],"aria-autocomplete":e["aria-autocomplete"],"aria-haspopup":e["aria-haspopup"],"aria-controls":e["aria-controls"],value:n,onChange:u=>s(u.target.value),autoComplete:e.autoComplete,autoCapitalize:e.autoCapitalize,maxLength:e.maxLength,minLength:e.minLength,name:e.name,form:e.form,placeholder:e.placeholder,inputMode:e.inputMode,autoCorrect:e.autoCorrect,spellCheck:e.spellCheck,[parseInt(x.version,10)>=17?"enterKeyHint":"enterkeyhint"]:e.enterKeyHint,onCopy:e.onCopy,onCut:e.onCut,onPaste:e.onPaste,onCompositionEnd:e.onCompositionEnd,onCompositionStart:e.onCompositionStart,onCompositionUpdate:e.onCompositionUpdate,onSelect:e.onSelect,onBeforeInput:e.onBeforeInput,onInput:e.onInput,...C,...g}),descriptionProps:y,errorMessageProps:I,isInvalid:f,validationErrors:v,validationDetails:P}}const A=c.createContext({}),z=c.createContext({});let K=e=>{let{onHoverStart:a,onHoverChange:i,onHoverEnd:o,...t}=e;return t};const N=D(function(a,i){[a,i]=R(a,i,z);let{hoverProps:o,isHovered:t}=B(a),{isFocused:d,isFocusVisible:r,focusProps:l}=L({isTextInput:!0,autoFocus:a.autoFocus}),n=!!a["aria-invalid"]&&a["aria-invalid"]!=="false",s=O({...a,values:{isHovered:t,isFocused:d,isFocusVisible:r,isDisabled:a.disabled||!1,isInvalid:n},defaultClassName:"react-aria-Input"});return x.createElement("input",{...m(K(a),l,o),...s,ref:i,"data-focused":d||void 0,"data-disabled":a.disabled||void 0,"data-hovered":t||void 0,"data-focus-visible":r||void 0,"data-invalid":n||void 0})});export{j as $,z as a,A as b,N as c};
