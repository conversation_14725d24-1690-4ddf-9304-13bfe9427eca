(function(){"use strict";try{if(typeof document<"u"){var t=document.createElement("style");t.appendChild(document.createTextNode(`/*! tailwindcss v4.1.13 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial}}}#container-react{color-scheme:light dark;color:#ffffffde;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:#242424;font-family:Roboto,Segoe UI,Helvetica,Tahoma,Open Sans,arial,serif;font-size:16px;font-weight:400;line-height:1.5}#container-react *,#container-react :before,#container-react :after{box-sizing:border-box}@layer theme{:root,:host{--tw-font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--tw-font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--tw-color-green-600:oklch(62.7% .194 149.214);--tw-color-blue-600:oklch(54.6% .245 262.881);--tw-color-slate-500:oklch(55.4% .046 257.417);--tw-color-slate-700:oklch(37.2% .044 257.287);--tw-color-gray-100:oklch(96.7% .003 264.542);--tw-color-gray-200:oklch(92.8% .006 264.531);--tw-color-gray-300:oklch(87.2% .01 258.338);--tw-color-gray-400:oklch(70.7% .022 261.325);--tw-color-gray-500:oklch(55.1% .027 264.364);--tw-color-gray-600:oklch(44.6% .03 256.802);--tw-color-gray-900:oklch(21% .034 264.665);--tw-color-black:#000;--tw-color-white:#fff;--tw-spacing:.25rem;--tw-container-md:28rem;--tw-text-xs:.75rem;--tw-text-xs--line-height:calc(1/.75);--tw-text-sm:.875rem;--tw-text-sm--line-height:calc(1.25/.875);--tw-text-base:14px;--tw-text-base--line-height: 1.5 ;--tw-text-lg:1.125rem;--tw-text-lg--line-height:calc(1.75/1.125);--tw-text-xl:1.25rem;--tw-text-xl--line-height:calc(1.75/1.25);--tw-font-weight-normal:400;--tw-font-weight-medium:500;--tw-font-weight-semibold:600;--tw-font-weight-bold:700;--tw-tracking-wider:.05em;--tw-leading-normal:1.25;--tw-radius-xs:2px;--tw-radius-sm:.25rem;--tw-radius-2xl:1rem;--tw-ease-in:cubic-bezier(.4,0,1,1);--tw-ease-out:cubic-bezier(0,0,.2,1);--tw-ease-in-out:cubic-bezier(.4,0,.2,1);--tw-animate-spin:spin 1s linear infinite;--tw-blur-xs:4px;--tw-default-transition-duration:.15s;--tw-default-transition-timing-function:cubic-bezier(.4,0,.2,1);--tw-default-font-family:var(--tw-font-sans);--tw-default-mono-font-family:var(--tw-font-mono);--tw-text-display-xl:2rem;--tw-text-display-rg:1.25rem;--tw-text-annotation:12px;--tw-color-light-gray-4:#9ea1ba;--tw-color-light-gray-1:#f6f9fc;--tw-color-primary-black:#202124;--tw-color-danger-1:#eb2121;--tw-color-success-2:#0c744a;--tw-color-info-1:#1877f2}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--tw-default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--tw-default-font-feature-settings,normal);font-variation-settings:var(--tw-default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--tw-default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--tw-default-mono-font-feature-settings,normal);font-variation-settings:var(--tw-default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}::-webkit-calendar-picker-indicator{line-height:1}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}}@layer components;@layer utilities{.tw\\:pointer-events-none{pointer-events:none}.tw\\:absolute{position:absolute}.tw\\:fixed{position:fixed}.tw\\:relative{position:relative}.tw\\:inset-0{inset:calc(var(--tw-spacing)*0)}.tw\\:inset-y-0{inset-block:calc(var(--tw-spacing)*0)}.tw\\:start-0{inset-inline-start:calc(var(--tw-spacing)*0)}.tw\\:end-2{inset-inline-end:calc(var(--tw-spacing)*2)}.tw\\:top-1\\/2{top:50%}.tw\\:right-\\[24px\\]{right:24px}.tw\\:left-\\[24px\\]{left:24px}.tw\\:left-\\[32px\\]{left:32px}.tw\\:left-\\[calc\\(100\\%-32px\\)\\]{left:calc(100% - 32px)}.tw\\:z-10{z-index:10}.tw\\:z-50{z-index:50}.tw\\:m-0\\!{margin:calc(var(--tw-spacing)*0)!important}.tw\\:mx-auto{margin-inline:auto}.tw\\:my-0{margin-block:calc(var(--tw-spacing)*0)}.tw\\:mt-3{margin-top:calc(var(--tw-spacing)*3)}.tw\\:mt-6{margin-top:calc(var(--tw-spacing)*6)}.tw\\:mb-0\\!{margin-bottom:calc(var(--tw-spacing)*0)!important}.tw\\:mb-\\[8px\\]\\!{margin-bottom:8px!important}.tw\\:flex{display:flex}.tw\\:flex\\!{display:flex!important}.tw\\:grid{display:grid}.tw\\:inline-flex{display:inline-flex}.tw\\:size-20{width:calc(var(--tw-spacing)*20);height:calc(var(--tw-spacing)*20)}.tw\\:size-full{width:100%;height:100%}.tw\\:h-5{height:calc(var(--tw-spacing)*5)}.tw\\:h-6{height:calc(var(--tw-spacing)*6)}.tw\\:h-12{height:calc(var(--tw-spacing)*12)}.tw\\:h-\\[14px\\]{height:14px}.tw\\:h-\\[22px\\]{height:22px}.tw\\:h-\\[32px\\]{height:32px}.tw\\:h-\\[66px\\]{height:66px}.tw\\:h-auto{height:auto}.tw\\:h-full{height:100%}.tw\\:max-h-60{max-height:calc(var(--tw-spacing)*60)}.tw\\:max-h-\\[34px\\]{max-height:34px}.tw\\:max-h-\\[38px\\]{max-height:38px}.tw\\:max-h-\\[42px\\]{max-height:42px}.tw\\:min-h-\\[34px\\]{min-height:34px}.tw\\:min-h-\\[38px\\]{min-height:38px}.tw\\:min-h-\\[42px\\]{min-height:42px}.tw\\:min-h-\\[400px\\]{min-height:400px}.tw\\:min-h-full{min-height:100%}.tw\\:min-h-screen{min-height:100vh}.tw\\:w-5{width:calc(var(--tw-spacing)*5)}.tw\\:w-6{width:calc(var(--tw-spacing)*6)}.tw\\:w-12{width:calc(var(--tw-spacing)*12)}.tw\\:w-40{width:calc(var(--tw-spacing)*40)}.tw\\:w-72{width:calc(var(--tw-spacing)*72)}.tw\\:w-\\[2px\\]{width:2px}.tw\\:w-\\[14px\\]{width:14px}.tw\\:w-\\[40px\\]{width:40px}.tw\\:w-\\[45px\\]{width:45px}.tw\\:w-\\[calc\\(var\\(--trigger-width\\)\\+36px\\)\\]{width:calc(var(--trigger-width) + 36px)}.tw\\:w-full{width:100%}.tw\\:w-max{width:max-content}.tw\\:max-w-\\[85vw\\]{max-width:85vw}.tw\\:max-w-md{max-width:var(--tw-container-md)}.tw\\:min-w-\\[170px\\]{min-width:170px}.tw\\:min-w-\\[var\\(--trigger-width\\)\\]{min-width:var(--trigger-width)}.tw\\:min-w-full{min-width:100%}.tw\\:flex-1{flex:1}.tw\\:-translate-x-1\\/2{--tw-translate-x: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:translate-x-0{--tw-translate-x:calc(var(--tw-spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:-translate-y-1\\/2{--tw-translate-y: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:rotate-180{rotate:180deg}.tw\\:transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.tw\\:animate-spin{animation:var(--tw-animate-spin)}.tw\\:cursor-default{cursor:default}.tw\\:cursor-pointer{cursor:pointer}.tw\\:grid-cols-\\[repeat\\(auto-fit\\,minmax\\(340px\\,1fr\\)\\)\\]{grid-template-columns:repeat(auto-fit,minmax(340px,1fr))}.tw\\:flex-col{flex-direction:column}.tw\\:flex-wrap{flex-wrap:wrap}.tw\\:items-center{align-items:center}.tw\\:items-stretch{align-items:stretch}.tw\\:justify-between{justify-content:space-between}.tw\\:justify-center{justify-content:center}.tw\\:justify-end{justify-content:flex-end}.tw\\:justify-start{justify-content:flex-start}.tw\\:gap-2{gap:calc(var(--tw-spacing)*2)}.tw\\:gap-4{gap:calc(var(--tw-spacing)*4)}.tw\\:gap-5{gap:calc(var(--tw-spacing)*5)}.tw\\:gap-6{gap:calc(var(--tw-spacing)*6)}.tw\\:gap-8{gap:calc(var(--tw-spacing)*8)}.tw\\:gap-\\[5px\\]{gap:5px}.tw\\:gap-\\[14px\\]{gap:14px}.tw\\:gap-\\[30px\\]{gap:30px}:where(.tw\\:divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}:where(.tw\\:divide-gray-200>:not(:last-child)){border-color:var(--tw-color-gray-200)}.tw\\:truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.tw\\:overflow-auto{overflow:auto}.tw\\:overflow-hidden{overflow:hidden}.tw\\:overflow-y-auto{overflow-y:auto}.tw\\:rounded{border-radius:.25rem}.tw\\:rounded-2xl{border-radius:var(--tw-radius-2xl)}.tw\\:rounded-\\[2px\\]{border-radius:2px}.tw\\:rounded-full{border-radius:3.40282e38px}.tw\\:rounded-sm{border-radius:var(--tw-radius-sm)}.tw\\:rounded-xs{border-radius:var(--tw-radius-xs)}.tw\\:border{border-style:var(--tw-border-style);border-width:1px}.tw\\:border-0{border-style:var(--tw-border-style);border-width:0}.tw\\:border-\\[1px\\]{border-style:var(--tw-border-style);border-width:1px}.tw\\:border-s-0{border-inline-start-style:var(--tw-border-style);border-inline-start-width:0}.tw\\:border-t-3{border-top-style:var(--tw-border-style);border-top-width:3px}.tw\\:border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.tw\\:border-b\\!{border-bottom-style:var(--tw-border-style)!important;border-bottom-width:1px!important}.tw\\:border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.tw\\:border-\\[\\#1EA702\\]\\!{border-color:#1ea702!important}.tw\\:border-\\[\\#3A3E63\\]{border-color:#3a3e63}.tw\\:border-\\[\\#3A3E63\\]\\!{border-color:#3a3e63!important}.tw\\:border-\\[\\#2356C2\\]\\!{border-color:#2356c2!important}.tw\\:border-\\[\\#DBE3EB\\]\\!{border-color:#dbe3eb!important}.tw\\:border-\\[\\#DF0000\\]\\!{border-color:#df0000!important}.tw\\:border-\\[\\#E4EBF2\\]{border-color:#e4ebf2}.tw\\:border-\\[\\#F88E00\\]\\!{border-color:#f88e00!important}.tw\\:border-\\[\\#e4ebf2\\]{border-color:#e4ebf2}.tw\\:border-blue-600{border-color:var(--tw-color-blue-600)}.tw\\:border-gray-200{border-color:var(--tw-color-gray-200)}.tw\\:border-info-1{border-color:var(--tw-color-info-1)}.tw\\:bg-\\[\\#A8ACC2\\]{background-color:#a8acc2}.tw\\:bg-\\[\\#E4EBF2\\]{background-color:#e4ebf2}.tw\\:bg-\\[\\#E4EBF2\\]\\!{background-color:#e4ebf2!important}.tw\\:bg-\\[\\#F0FAF7\\]\\!{background-color:#f0faf7!important}.tw\\:bg-\\[\\#F6F9FC\\]{background-color:#f6f9fc}.tw\\:bg-\\[\\#F8FAFF\\]\\!{background-color:#f8faff!important}.tw\\:bg-\\[\\#FDF1F0\\]\\!{background-color:#fdf1f0!important}.tw\\:bg-\\[\\#FFF9F0\\]\\!{background-color:#fff9f0!important}.tw\\:bg-\\[\\#FFFFFF\\]{background-color:#fff}.tw\\:bg-\\[\\#e4ebf2\\]{background-color:#e4ebf2}.tw\\:bg-\\[\\#eb2121\\]{background-color:#eb2121}.tw\\:bg-\\[\\#fff\\]{background-color:#fff}.tw\\:bg-black\\/25{background-color:var(--tw-color-black)}@supports (color:color-mix(in lab,red,red)){.tw\\:bg-black\\/25{background-color:color-mix(in oklab,var(--tw-color-black)25%,transparent)}}.tw\\:bg-gray-100{background-color:var(--tw-color-gray-100)}.tw\\:bg-transparent{background-color:#0000}.tw\\:bg-white{background-color:var(--tw-color-white)}.tw\\:bg-clip-padding{background-clip:padding-box}.tw\\:object-cover{object-fit:cover}.tw\\:p-1{padding:calc(var(--tw-spacing)*1)}.tw\\:p-4{padding:calc(var(--tw-spacing)*4)}.tw\\:p-6{padding:calc(var(--tw-spacing)*6)}.tw\\:px-3{padding-inline:calc(var(--tw-spacing)*3)}.tw\\:px-4{padding-inline:calc(var(--tw-spacing)*4)}.tw\\:px-5{padding-inline:calc(var(--tw-spacing)*5)}.tw\\:px-\\[16px\\]{padding-inline:16px}.tw\\:px-\\[70px\\]\\!{padding-inline:70px!important}.tw\\:py-1{padding-block:calc(var(--tw-spacing)*1)}.tw\\:py-2{padding-block:calc(var(--tw-spacing)*2)}.tw\\:py-3{padding-block:calc(var(--tw-spacing)*3)}.tw\\:py-5{padding-block:calc(var(--tw-spacing)*5)}.tw\\:py-6{padding-block:calc(var(--tw-spacing)*6)}.tw\\:py-10{padding-block:calc(var(--tw-spacing)*10)}.tw\\:py-12{padding-block:calc(var(--tw-spacing)*12)}.tw\\:py-\\[7\\.5px\\]{padding-block:7.5px}.tw\\:py-\\[8px\\]{padding-block:8px}.tw\\:py-\\[9px\\]{padding-block:9px}.tw\\:py-\\[10px\\]{padding-block:10px}.tw\\:py-\\[27\\.5px\\]{padding-block:27.5px}.tw\\:ps-0{padding-inline-start:calc(var(--tw-spacing)*0)}.tw\\:ps-4{padding-inline-start:calc(var(--tw-spacing)*4)}.tw\\:ps-\\[12px\\]{padding-inline-start:12px}.tw\\:ps-\\[40px\\]{padding-inline-start:40px}.tw\\:pe-0{padding-inline-end:calc(var(--tw-spacing)*0)}.tw\\:pe-4{padding-inline-end:calc(var(--tw-spacing)*4)}.tw\\:pe-\\[16px\\]{padding-inline-end:16px}.tw\\:pr-\\[20px\\]{padding-right:20px}.tw\\:pb-4{padding-bottom:calc(var(--tw-spacing)*4)}.tw\\:pb-5{padding-bottom:calc(var(--tw-spacing)*5)}.tw\\:pl-\\[70px\\]{padding-left:70px}.tw\\:text-center{text-align:center}.tw\\:text-left{text-align:left}.tw\\:text-start{text-align:start}.tw\\:align-middle{vertical-align:middle}.tw\\:text-base{font-size:var(--tw-text-base);line-height:var(--tw-leading,var(--tw-text-base--line-height))}.tw\\:text-lg{font-size:var(--tw-text-lg);line-height:var(--tw-leading,var(--tw-text-lg--line-height))}.tw\\:text-lg\\!{font-size:var(--tw-text-lg)!important;line-height:var(--tw-leading,var(--tw-text-lg--line-height))!important}.tw\\:text-sm{font-size:var(--tw-text-sm);line-height:var(--tw-leading,var(--tw-text-sm--line-height))}.tw\\:text-xl\\!{font-size:var(--tw-text-xl)!important;line-height:var(--tw-leading,var(--tw-text-xl--line-height))!important}.tw\\:text-xs{font-size:var(--tw-text-xs);line-height:var(--tw-leading,var(--tw-text-xs--line-height))}.tw\\:text-\\[14px\\]{font-size:14px}.tw\\:text-\\[14px\\]\\!{font-size:14px!important}.tw\\:text-\\[18px\\]{font-size:18px}.tw\\:text-\\[20px\\]{font-size:20px}.tw\\:text-annotation{font-size:var(--tw-text-annotation)}.tw\\:text-display-rg{font-size:var(--tw-text-display-rg)}.tw\\:text-display-xl\\!{font-size:var(--tw-text-display-xl)!important}.tw\\:leading-4\\!{--tw-leading:calc(var(--tw-spacing)*4)!important;line-height:calc(var(--tw-spacing)*4)!important}.tw\\:leading-6{--tw-leading:calc(var(--tw-spacing)*6);line-height:calc(var(--tw-spacing)*6)}.tw\\:leading-\\[18px\\]\\!{--tw-leading:18px!important;line-height:18px!important}.tw\\:leading-normal{--tw-leading:var(--tw-leading-normal);line-height:var(--tw-leading-normal)}.tw\\:font-bold{--tw-font-weight:var(--tw-font-weight-bold);font-weight:var(--tw-font-weight-bold)}.tw\\:font-medium{--tw-font-weight:var(--tw-font-weight-medium);font-weight:var(--tw-font-weight-medium)}.tw\\:font-medium\\!{--tw-font-weight:var(--tw-font-weight-medium)!important;font-weight:var(--tw-font-weight-medium)!important}.tw\\:font-normal{--tw-font-weight:var(--tw-font-weight-normal);font-weight:var(--tw-font-weight-normal)}.tw\\:font-semibold{--tw-font-weight:var(--tw-font-weight-semibold);font-weight:var(--tw-font-weight-semibold)}.tw\\:tracking-wider{--tw-tracking:var(--tw-tracking-wider);letter-spacing:var(--tw-tracking-wider)}.tw\\:whitespace-nowrap{white-space:nowrap}.tw\\:text-\\[\\#1A2B50\\]{color:#1a2b50}.tw\\:text-\\[\\#1EA702\\]\\!{color:#1ea702!important}.tw\\:text-\\[\\#3A3E63\\]{color:#3a3e63}.tw\\:text-\\[\\#3A3E63\\]\\!{color:#3a3e63!important}.tw\\:text-\\[\\#4e5381\\]{color:#4e5381}.tw\\:text-\\[\\#2356C2\\]\\!{color:#2356c2!important}.tw\\:text-\\[\\#75799D\\]{color:#75799d}.tw\\:text-\\[\\#202124\\]{color:#202124}.tw\\:text-\\[\\#DF0000\\]\\!{color:#df0000!important}.tw\\:text-\\[\\#F88E00\\]\\!{color:#f88e00!important}.tw\\:text-black{color:var(--tw-color-black)}.tw\\:text-black\\!{color:var(--tw-color-black)!important}.tw\\:text-danger-1{color:var(--tw-color-danger-1)}.tw\\:text-gray-300{color:var(--tw-color-gray-300)}.tw\\:text-gray-400{color:var(--tw-color-gray-400)}.tw\\:text-gray-500{color:var(--tw-color-gray-500)}.tw\\:text-gray-600{color:var(--tw-color-gray-600)}.tw\\:text-gray-900{color:var(--tw-color-gray-900)}.tw\\:text-green-600{color:var(--tw-color-green-600)}.tw\\:text-light-gray-4{color:var(--tw-color-light-gray-4)}.tw\\:text-primary-black{color:var(--tw-color-primary-black)}.tw\\:text-slate-500{color:var(--tw-color-slate-500)}.tw\\:text-slate-700{color:var(--tw-color-slate-700)}.tw\\:text-white{color:var(--tw-color-white)}.tw\\:antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.tw\\:shadow-\\[0px_3px_6px_0px_rgba\\(0\\,0\\,0\\,0\\.16\\)\\]{--tw-shadow:0px 3px 6px 0px var(--tw-shadow-color,#00000029);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:ring-1{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:ring-black,.tw\\:ring-black\\/5{--tw-ring-color:var(--tw-color-black)}@supports (color:color-mix(in lab,red,red)){.tw\\:ring-black\\/5{--tw-ring-color:color-mix(in oklab,var(--tw-color-black)5%,transparent)}}.tw\\:outline-hidden{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.tw\\:outline-hidden{outline-offset:2px;outline:2px solid #0000}}.tw\\:backdrop-blur-xs{--tw-backdrop-blur:blur(var(--tw-blur-xs));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.tw\\:transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--tw-default-transition-timing-function));transition-duration:var(--tw-duration,var(--tw-default-transition-duration))}.tw\\:transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--tw-default-transition-timing-function));transition-duration:var(--tw-duration,var(--tw-default-transition-duration))}.tw\\:duration-200{--tw-duration:.2s;transition-duration:.2s}.tw\\:duration-300{--tw-duration:.3s;transition-duration:.3s}.tw\\:ease-in{--tw-ease:var(--tw-ease-in);transition-timing-function:var(--tw-ease-in)}.tw\\:ease-in-out{--tw-ease:var(--tw-ease-in-out);transition-timing-function:var(--tw-ease-in-out)}.tw\\:ease-out{--tw-ease:var(--tw-ease-out);transition-timing-function:var(--tw-ease-out)}.tw\\:animate-in{--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial;animation-name:enter;animation-duration:.15s}.tw\\:animate-out{--tw-exit-opacity:initial;--tw-exit-scale:initial;--tw-exit-rotate:initial;--tw-exit-translate-x:initial;--tw-exit-translate-y:initial;animation-name:exit;animation-duration:.15s}.tw\\:outline-none{--tw-outline-style:none;outline-style:none}.tw\\:select-none{-webkit-user-select:none;user-select:none}.tw\\:duration-200{animation-duration:.2s}.tw\\:duration-300{animation-duration:.3s}.tw\\:ease-in{animation-timing-function:cubic-bezier(.4,0,1,1)}.tw\\:ease-in-out{animation-timing-function:cubic-bezier(.4,0,.2,1)}.tw\\:ease-out{animation-timing-function:cubic-bezier(0,0,.2,1)}.tw\\:fade-in{--tw-enter-opacity:0}.tw\\:fade-out{--tw-exit-opacity:0}.tw\\:zoom-in-95{--tw-enter-scale:.95}.tw\\:zoom-out-95{--tw-exit-scale:.95}.tw\\:group-invalid\\:border-danger-1:is(:where(.tw\\:group):where([data-rac])[data-invalid] *),.tw\\:group-invalid\\:border-danger-1:is(:where(.tw\\:group):where(:not([data-rac])):invalid *){border-color:var(--tw-color-danger-1)}.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where([data-rac])[data-focused] *){color:var(--tw-color-white)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where([data-rac])[data-focused] *){color:color-mix(in oklab,var(--tw-color-white)80%,transparent)}}.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where(:not([data-rac])):focus *){color:var(--tw-color-white)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where(:not([data-rac])):focus *){color:color-mix(in oklab,var(--tw-color-white)80%,transparent)}}.tw\\:group-focus-visible\\:ring-2:is(:where(.tw\\:group):where([data-rac])[data-focus-visible] *),.tw\\:group-focus-visible\\:ring-2:is(:where(.tw\\:group):where(:not([data-rac])):focus-visible *){--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:group-disabled\\:cursor-not-allowed:is(:where(.tw\\:group):where([data-rac])[data-disabled] *),.tw\\:group-disabled\\:cursor-not-allowed:is(:where(.tw\\:group):where(:not([data-rac])):disabled *){cursor:not-allowed}.tw\\:group-disabled\\:bg-\\[\\#A8ACC2\\]\\/30:is(:where(.tw\\:group):where([data-rac])[data-disabled] *),.tw\\:group-disabled\\:bg-\\[\\#A8ACC2\\]\\/30:is(:where(.tw\\:group):where(:not([data-rac])):disabled *){background-color:#a8acc24d}.tw\\:group-disabled\\:bg-light-gray-1:is(:where(.tw\\:group):where([data-rac])[data-disabled] *),.tw\\:group-disabled\\:bg-light-gray-1:is(:where(.tw\\:group):where(:not([data-rac])):disabled *){background-color:var(--tw-color-light-gray-1)}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:static:is(:where(.tw\\:group\\/input-group)[data-group=true] *){position:static}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:min-h-full\\!:is(:where(.tw\\:group\\/input-group)[data-group=true] *){min-height:100%!important}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:rounded-none:is(:where(.tw\\:group\\/input-group)[data-group=true] *){border-radius:0}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:border-none:is(:where(.tw\\:group\\/input-group)[data-group=true] *){--tw-border-style:none;border-style:none}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *){--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:group-data-\\[pressed\\=true\\]\\:border-info-1:is(:where(.tw\\:group)[data-pressed=true] *){border-color:var(--tw-color-info-1)}.tw\\:group-data-\\[selected\\=true\\]\\:text-white\\/80:is(:where(.tw\\:group)[data-selected=true] *){color:var(--tw-color-white)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-data-\\[selected\\=true\\]\\:text-white\\/80:is(:where(.tw\\:group)[data-selected=true] *){color:color-mix(in oklab,var(--tw-color-white)80%,transparent)}}.tw\\:group-data-\\[success\\=true\\]\\:border-success-2:is(:where(.tw\\:group)[data-success=true] *){border-color:var(--tw-color-success-2)}.tw\\:group-data-\\[success\\=true\\]\\:text-success-2:is(:where(.tw\\:group)[data-success=true] *){color:var(--tw-color-success-2)}.tw\\:group-selected\\:translate-x-\\[18px\\]:is(:where(.tw\\:group)[data-selected] *){--tw-translate-x:18px;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:group-selected\\:bg-\\[\\#80C342\\]:is(:where(.tw\\:group)[data-selected] *){background-color:#80c342}.tw\\:group-disabled\\:group-selected\\:bg-\\[\\#DBF4C7\\]:is(:where(.tw\\:group):where([data-rac])[data-disabled] *):is(:where(.tw\\:group)[data-selected] *),.tw\\:group-disabled\\:group-selected\\:bg-\\[\\#DBF4C7\\]:is(:where(.tw\\:group):where(:not([data-rac])):disabled *):is(:where(.tw\\:group)[data-selected] *){background-color:#dbf4c7}.tw\\:peer-data-\\[select\\=true\\]\\:pe-2:is(:where(.tw\\:peer)[data-select=true]~*){padding-inline-end:calc(var(--tw-spacing)*2)}.tw\\:placeholder\\:text-light-gray-4::placeholder{color:var(--tw-color-light-gray-4)}.tw\\:focus-within\\:border-info-1:where([data-rac])[data-focus-within],.tw\\:focus-within\\:border-info-1:where(:not([data-rac])):focus-within{border-color:var(--tw-color-info-1)}.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-focus-within],.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):focus-within,.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-focus-within],.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):focus-within{border-color:var(--tw-color-danger-1)}.tw\\:group-data-\\[success\\=true\\]\\:focus-within\\:border-success-2:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-focus-within],.tw\\:group-data-\\[success\\=true\\]\\:focus-within\\:border-success-2:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):focus-within{border-color:var(--tw-color-success-2)}.tw\\:hover\\:bg-gray-100:where([data-rac])[data-hovered]{background-color:var(--tw-color-gray-100)}@media (hover:hover){.tw\\:hover\\:bg-gray-100:where(:not([data-rac])):hover{background-color:var(--tw-color-gray-100)}}.tw\\:hover\\:bg-light-gray-1:where([data-rac])[data-hovered]{background-color:var(--tw-color-light-gray-1)}@media (hover:hover){.tw\\:hover\\:bg-light-gray-1:where(:not([data-rac])):hover{background-color:var(--tw-color-light-gray-1)}}.tw\\:hover\\:ring-2:where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:hover\\:ring-2:where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:hover\\:ring-\\[\\#D8E8FD\\]:where([data-rac])[data-hovered]{--tw-ring-color:#d8e8fd}@media (hover:hover){.tw\\:hover\\:ring-\\[\\#D8E8FD\\]:where(:not([data-rac])):hover{--tw-ring-color:#d8e8fd}}.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-hovered]{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-hovered]{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):hover{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):hover{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}}.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-hovered]{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-hovered]{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):hover{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):hover{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}}.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where([data-rac])[data-disabled] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where([data-rac])[data-disabled] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where(:not([data-rac])):disabled *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where(:not([data-rac])):disabled *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-2:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-2:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-color:var(--tw-color-success-2)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-color:color-mix(in oklab,var(--tw-color-success-2)50%,transparent)}}@media (hover:hover){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-color:var(--tw-color-success-2)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-color:color-mix(in oklab,var(--tw-color-success-2)50%,transparent)}}}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:group-data-\\[success\\=true\\]\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:group-data-\\[success\\=true\\]\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:focus\\:text-info-1:where([data-rac])[data-focused],.tw\\:focus\\:text-info-1:where(:not([data-rac])):focus{color:var(--tw-color-info-1)}.tw\\:focus\\:outline-none:where([data-rac])[data-focused],.tw\\:focus\\:outline-none:where(:not([data-rac])):focus{--tw-outline-style:none;outline-style:none}.tw\\:disabled\\:cursor-not-allowed:where([data-rac])[data-disabled],.tw\\:disabled\\:cursor-not-allowed:where(:not([data-rac])):disabled{cursor:not-allowed}.tw\\:disabled\\:bg-transparent:where([data-rac])[data-disabled],.tw\\:disabled\\:bg-transparent:where(:not([data-rac])):disabled{background-color:#0000}.tw\\:disabled\\:text-light-gray-4:where([data-rac])[data-disabled],.tw\\:disabled\\:text-light-gray-4:where(:not([data-rac])):disabled{color:var(--tw-color-light-gray-4)}.tw\\:disabled\\:opacity-50:where([data-rac])[data-disabled],.tw\\:disabled\\:opacity-50:where(:not([data-rac])):disabled{opacity:.5}.tw\\:data-\\[placement\\=bottom\\]\\:slide-in-from-top-2[data-placement=bottom]{--tw-enter-translate-y:-2}.tw\\:data-\\[placement\\=top\\]\\:slide-in-from-bottom-2[data-placement=top]{--tw-enter-translate-y:2}.tw\\:data-\\[selected\\=true\\]\\:text-info-1[data-selected=true]{color:var(--tw-color-info-1)}@media (min-width:576px){.tw\\:min-\\[576px\\]\\:max-w-\\[940px\\]{max-width:940px}}@media (min-width:768px){.tw\\:min-\\[768px\\]\\:max-w-\\[950px\\]{max-width:950px}}@media (min-width:992px){.tw\\:min-\\[992px\\]\\:max-w-\\[960px\\]{max-width:960px}}@media (min-width:1200px){.tw\\:min-\\[1200px\\]\\:max-w-\\[1140px\\]{max-width:1140px}}@media (min-width:40rem){.tw\\:sm\\:flex-row{flex-direction:row}.tw\\:sm\\:items-center{align-items:center}}.tw\\:rtl\\:translate-x-\\[-34px\\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){--tw-translate-x:-34px;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:rtl\\:flex-row-reverse:where(:dir(rtl),[dir=rtl],[dir=rtl] *){flex-direction:row-reverse}.tw\\:entering\\:animate-in[data-entering]{--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial;animation-name:enter;animation-duration:.15s}.tw\\:entering\\:fade-in[data-entering]{--tw-enter-opacity:0}.tw\\:entering\\:zoom-in-95[data-entering]{--tw-enter-scale:.95}.tw\\:exiting\\:animate-out[data-exiting]{--tw-exit-opacity:initial;--tw-exit-scale:initial;--tw-exit-rotate:initial;--tw-exit-translate-x:initial;--tw-exit-translate-y:initial;animation-name:exit;animation-duration:.15s}.tw\\:exiting\\:fade-out[data-exiting]{--tw-exit-opacity:0}.tw\\:exiting\\:zoom-out-95[data-exiting]{--tw-exit-scale:.95}.tw\\:\\[\\&\\:has\\(\\[data-select\\]\\)\\]\\:pe-\\[34px\\]:has([data-select]){padding-inline-end:34px}.tw\\:\\[\\&\\>input\\:not\\(\\:first-child\\)\\]\\:ps-2>input:not(:first-child){padding-inline-start:calc(var(--tw-spacing)*2)}.tw\\:\\[\\&\\>input\\:not\\(\\:last-child\\)\\]\\:pe-2>input:not(:last-child){padding-inline-end:calc(var(--tw-spacing)*2)}.tw\\:\\[\\&\\>textarea\\:not\\(\\:first-child\\)\\]\\:ps-2>textarea:not(:first-child){padding-inline-start:calc(var(--tw-spacing)*2)}.tw\\:\\[\\&\\>textarea\\:not\\(\\:last-child\\)\\]\\:pe-2>textarea:not(:last-child){padding-inline-end:calc(var(--tw-spacing)*2)}}.react-aria-Menu{max-height:inherit;box-sizing:border-box;background-color:#fff;outline:none;min-width:150px;padding:2px;overflow:auto}.react-aria-MenuItem{cursor:default;forced-color-adjust:none;cursor:pointer;border-radius:6px;outline:none;grid-template-areas:"label kbd""desc kbd";align-items:center;column-gap:20px;margin:2px;padding:.286rem .571rem;font-size:1.072rem;display:grid;position:relative}.search-input{background-color:#fff;border-color:#e4ebf2}.search-input:focus{outline:0;border-color:#1877f2!important}table thead tr th{color:#75799d;background:#f6f9fc}.text-bg-danger{background-color:#eb2121}.text-bg-success{background-color:#13b272}.btn-dots{color:#202124;background-color:#e4ebf2;width:46px;height:46px}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}
@charset "UTF-8";:root{--fc-now-indicator-color: green !important;--fc-daygrid-event-dot-width: 8px;font-family:roboto,sans-serif,system-ui,Avenir,Helvetica,Arial;line-height:1.5;font-weight:400;color-scheme:light dark;color:#ffffffde;background-color:#242424;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}a{font-weight:500;color:#646cff;text-decoration:inherit}a:hover{color:#535bf2}body{margin:0;display:flex;place-items:center;min-width:320px;min-height:100vh}h1{font-size:3.2em;line-height:1.1}button{border-radius:8px;border:1px solid transparent;padding:.6em 1.2em;font-size:1em;font-weight:500;font-family:inherit;background-color:#1a1a1a;cursor:pointer;transition:border-color .25s}button:hover{border-color:#646cff}button:focus,button:focus-visible{outline:4px auto -webkit-focus-ring-color}.fc .fc-timegrid-slot-label:before{--fc-now-indicator-color: green !important}.fc-timegrid-slot{height:70px!important}.tox .tox-statusbar{display:none!important}@media (prefers-color-scheme: light){:root{color:#213547;background-color:#fff}a:hover{color:#747bff}button{background-color:#f9f9f9}}@keyframes mymodal-slide{0%{transform:translate(100%)}to{transform:translate(0)}}.react-datepicker__navigation-icon:before,.react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow{border-color:#ccc;border-style:solid;border-width:3px 3px 0 0;content:"";display:block;height:9px;position:absolute;top:6px;width:9px}.react-datepicker__sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.react-datepicker-wrapper{display:inline-block;padding:0;border:0}.react-datepicker{font-family:Helvetica Neue,helvetica,arial,sans-serif;font-size:.8rem;background-color:#fff;color:#000;border:1px solid #aeaeae;border-radius:.3rem;display:inline-block;position:relative;line-height:initial}.react-datepicker--time-only .react-datepicker__time-container{border-left:0}.react-datepicker--time-only .react-datepicker__time,.react-datepicker--time-only .react-datepicker__time-box{border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker-popper{z-index:1;line-height:0}.react-datepicker-popper .react-datepicker__triangle{stroke:#aeaeae}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle{fill:#f0f0f0;color:#f0f0f0}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle{fill:#fff;color:#fff}.react-datepicker__header{text-align:center;background-color:#f0f0f0;border-bottom:1px solid #aeaeae;border-top-left-radius:.3rem;padding:8px 0;position:relative}.react-datepicker__header--time{padding-bottom:8px;padding-left:5px;padding-right:5px}.react-datepicker__header--time:not(.react-datepicker__header--time--only){border-top-left-radius:0}.react-datepicker__header:not(.react-datepicker__header--has-time-select){border-top-right-radius:.3rem}.react-datepicker__year-dropdown-container--select,.react-datepicker__month-dropdown-container--select,.react-datepicker__month-year-dropdown-container--select,.react-datepicker__year-dropdown-container--scroll,.react-datepicker__month-dropdown-container--scroll,.react-datepicker__month-year-dropdown-container--scroll{display:inline-block;margin:0 15px}.react-datepicker__current-month,.react-datepicker-time__header,.react-datepicker-year-header{margin-top:0;color:#000;font-weight:700;font-size:.944rem}h2.react-datepicker__current-month{padding:0;margin:0}.react-datepicker-time__header{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.react-datepicker__navigation{align-items:center;background:none;display:flex;justify-content:center;text-align:center;cursor:pointer;position:absolute;top:2px;padding:0;border:none;z-index:1;height:32px;width:32px;text-indent:-999em;overflow:hidden}.react-datepicker__navigation--previous{left:2px}.react-datepicker__navigation--next{right:2px}.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button){right:85px}.react-datepicker__navigation--years{position:relative;top:0;display:block;margin-left:auto;margin-right:auto}.react-datepicker__navigation--years-previous{top:4px}.react-datepicker__navigation--years-upcoming{top:-4px}.react-datepicker__navigation:hover *:before{border-color:#a6a6a6}.react-datepicker__navigation-icon{position:relative;top:-1px;font-size:20px;width:0}.react-datepicker__navigation-icon--next{left:-2px}.react-datepicker__navigation-icon--next:before{transform:rotate(45deg);left:-7px}.react-datepicker__navigation-icon--previous{right:-2px}.react-datepicker__navigation-icon--previous:before{transform:rotate(225deg);right:-7px}.react-datepicker__month-container{float:left}.react-datepicker__year{margin:.4rem;text-align:center}.react-datepicker__year-wrapper{display:flex;flex-wrap:wrap;max-width:180px}.react-datepicker__year .react-datepicker__year-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__month{margin:.4rem;text-align:center}.react-datepicker__month .react-datepicker__month-text,.react-datepicker__month .react-datepicker__quarter-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__input-time-container{clear:both;width:100%;float:left;margin:5px 0 10px 15px;text-align:left}.react-datepicker__input-time-container .react-datepicker-time__caption,.react-datepicker__input-time-container .react-datepicker-time__input-container{display:inline-block}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input{display:inline-block;margin-left:10px}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input{width:auto}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button,.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]{-moz-appearance:textfield}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter{margin-left:5px;display:inline-block}.react-datepicker__time-container{float:right;border-left:1px solid #aeaeae;width:85px}.react-datepicker__time-container--with-today-button{display:inline;border:1px solid #aeaeae;border-radius:.3rem;position:absolute;right:-87px;top:0}.react-datepicker__time-container .react-datepicker__time{position:relative;background:#fff;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{width:85px;overflow-x:hidden;margin:0 auto;text-align:center;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list{list-style:none;margin:0;height:calc(195px + .85rem);overflow-y:scroll;padding-right:0;padding-left:0;width:100%;box-sizing:content-box}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item{height:30px;padding:5px 10px;white-space:nowrap}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover{cursor:pointer;background-color:#f0f0f0}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected{background-color:#216ba5;color:#fff;font-weight:700}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover{background-color:#216ba5}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled{color:#ccc}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover{cursor:default;background-color:transparent}.react-datepicker__week-number{color:#ccc;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__week-number.react-datepicker__week-number--clickable{cursor:pointer}.react-datepicker__week-number.react-datepicker__week-number--clickable:not(.react-datepicker__week-number--selected):hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__week-number--selected{border-radius:.3rem;background-color:#216ba5;color:#fff}.react-datepicker__week-number--selected:hover{background-color:#1d5d90}.react-datepicker__day-names{white-space:nowrap;margin-bottom:-8px}.react-datepicker__week{white-space:nowrap}.react-datepicker__day-name,.react-datepicker__day,.react-datepicker__time-name{color:#000;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__day,.react-datepicker__month-text,.react-datepicker__quarter-text,.react-datepicker__year-text{cursor:pointer}.react-datepicker__day:not([aria-disabled=true]):hover,.react-datepicker__month-text:not([aria-disabled=true]):hover,.react-datepicker__quarter-text:not([aria-disabled=true]):hover,.react-datepicker__year-text:not([aria-disabled=true]):hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__day--today,.react-datepicker__month-text--today,.react-datepicker__quarter-text--today,.react-datepicker__year-text--today{font-weight:700}.react-datepicker__day--highlighted,.react-datepicker__month-text--highlighted,.react-datepicker__quarter-text--highlighted,.react-datepicker__year-text--highlighted{border-radius:.3rem;background-color:#3dcc4a;color:#fff}.react-datepicker__day--highlighted:not([aria-disabled=true]):hover,.react-datepicker__month-text--highlighted:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--highlighted:not([aria-disabled=true]):hover,.react-datepicker__year-text--highlighted:not([aria-disabled=true]):hover{background-color:#32be3f}.react-datepicker__day--highlighted-custom-1,.react-datepicker__month-text--highlighted-custom-1,.react-datepicker__quarter-text--highlighted-custom-1,.react-datepicker__year-text--highlighted-custom-1{color:#f0f}.react-datepicker__day--highlighted-custom-2,.react-datepicker__month-text--highlighted-custom-2,.react-datepicker__quarter-text--highlighted-custom-2,.react-datepicker__year-text--highlighted-custom-2{color:green}.react-datepicker__day--holidays,.react-datepicker__month-text--holidays,.react-datepicker__quarter-text--holidays,.react-datepicker__year-text--holidays{position:relative;border-radius:.3rem;background-color:#ff6803;color:#fff}.react-datepicker__day--holidays .overlay,.react-datepicker__month-text--holidays .overlay,.react-datepicker__quarter-text--holidays .overlay,.react-datepicker__year-text--holidays .overlay{position:absolute;bottom:100%;left:50%;transform:translate(-50%);background-color:#333;color:#fff;padding:4px;border-radius:4px;white-space:nowrap;visibility:hidden;opacity:0;transition:visibility 0s,opacity .3s ease-in-out}.react-datepicker__day--holidays:not([aria-disabled=true]):hover,.react-datepicker__month-text--holidays:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--holidays:not([aria-disabled=true]):hover,.react-datepicker__year-text--holidays:not([aria-disabled=true]):hover{background-color:#cf5300}.react-datepicker__day--holidays:hover .overlay,.react-datepicker__month-text--holidays:hover .overlay,.react-datepicker__quarter-text--holidays:hover .overlay,.react-datepicker__year-text--holidays:hover .overlay{visibility:visible;opacity:1}.react-datepicker__day--selected,.react-datepicker__day--in-selecting-range,.react-datepicker__day--in-range,.react-datepicker__month-text--selected,.react-datepicker__month-text--in-selecting-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--selected,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--selected,.react-datepicker__year-text--in-selecting-range,.react-datepicker__year-text--in-range{border-radius:.3rem;background-color:#216ba5;color:#fff}.react-datepicker__day--selected:not([aria-disabled=true]):hover,.react-datepicker__day--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__day--in-range:not([aria-disabled=true]):hover,.react-datepicker__month-text--selected:not([aria-disabled=true]):hover,.react-datepicker__month-text--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__month-text--in-range:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--selected:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--in-range:not([aria-disabled=true]):hover,.react-datepicker__year-text--selected:not([aria-disabled=true]):hover,.react-datepicker__year-text--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__year-text--in-range:not([aria-disabled=true]):hover{background-color:#1d5d90}.react-datepicker__day--keyboard-selected,.react-datepicker__month-text--keyboard-selected,.react-datepicker__quarter-text--keyboard-selected,.react-datepicker__year-text--keyboard-selected{border-radius:.3rem;background-color:#bad9f1;color:#000}.react-datepicker__day--keyboard-selected:not([aria-disabled=true]):hover,.react-datepicker__month-text--keyboard-selected:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--keyboard-selected:not([aria-disabled=true]):hover,.react-datepicker__year-text--keyboard-selected:not([aria-disabled=true]):hover{background-color:#1d5d90}.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range){background-color:#216ba580}.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range){background-color:#f0f0f0;color:#000}.react-datepicker__day--disabled,.react-datepicker__month-text--disabled,.react-datepicker__quarter-text--disabled,.react-datepicker__year-text--disabled{cursor:default;color:#ccc}.react-datepicker__day--disabled .overlay,.react-datepicker__month-text--disabled .overlay,.react-datepicker__quarter-text--disabled .overlay,.react-datepicker__year-text--disabled .overlay{position:absolute;bottom:70%;left:50%;transform:translate(-50%);background-color:#333;color:#fff;padding:4px;border-radius:4px;white-space:nowrap;visibility:hidden;opacity:0;transition:visibility 0s,opacity .3s ease-in-out}.react-datepicker__input-container{position:relative;display:inline-block;width:100%}.react-datepicker__input-container .react-datepicker__calendar-icon{position:absolute;padding:.5rem;box-sizing:content-box}.react-datepicker__view-calendar-icon input{padding:6px 10px 5px 25px}.react-datepicker__year-read-view,.react-datepicker__month-read-view,.react-datepicker__month-year-read-view{border:1px solid transparent;border-radius:.3rem;position:relative}.react-datepicker__year-read-view:hover,.react-datepicker__month-read-view:hover,.react-datepicker__month-year-read-view:hover{cursor:pointer}.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow{border-top-color:#b3b3b3}.react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow{transform:rotate(135deg);right:-16px;top:0}.react-datepicker__year-dropdown,.react-datepicker__month-dropdown,.react-datepicker__month-year-dropdown{background-color:#f0f0f0;position:absolute;width:50%;left:25%;top:30px;z-index:1;text-align:center;border-radius:.3rem;border:1px solid #aeaeae}.react-datepicker__year-dropdown:hover,.react-datepicker__month-dropdown:hover,.react-datepicker__month-year-dropdown:hover{cursor:pointer}.react-datepicker__year-dropdown--scrollable,.react-datepicker__month-dropdown--scrollable,.react-datepicker__month-year-dropdown--scrollable{height:150px;overflow-y:scroll}.react-datepicker__year-option,.react-datepicker__month-option,.react-datepicker__month-year-option{line-height:20px;width:100%;display:block;margin-left:auto;margin-right:auto}.react-datepicker__year-option:first-of-type,.react-datepicker__month-option:first-of-type,.react-datepicker__month-year-option:first-of-type{border-top-left-radius:.3rem;border-top-right-radius:.3rem}.react-datepicker__year-option:last-of-type,.react-datepicker__month-option:last-of-type,.react-datepicker__month-year-option:last-of-type{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker__year-option:hover,.react-datepicker__month-option:hover,.react-datepicker__month-year-option:hover{background-color:#ccc}.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming{border-bottom-color:#b3b3b3}.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous{border-top-color:#b3b3b3}.react-datepicker__year-option--selected,.react-datepicker__month-option--selected,.react-datepicker__month-year-option--selected{position:absolute;left:15px}.react-datepicker__close-icon{cursor:pointer;background-color:transparent;border:0;outline:0;padding:0 6px 0 0;position:absolute;top:0;right:0;height:100%;display:table-cell;vertical-align:middle}.react-datepicker__close-icon:after{cursor:pointer;background-color:#216ba5;color:#fff;border-radius:50%;height:16px;width:16px;padding:2px;font-size:12px;line-height:1;text-align:center;display:table-cell;vertical-align:middle;content:"×"}.react-datepicker__close-icon--disabled{cursor:default}.react-datepicker__close-icon--disabled:after{cursor:default;background-color:#ccc}.react-datepicker__today-button{background:#f0f0f0;border-top:1px solid #aeaeae;cursor:pointer;text-align:center;font-weight:700;padding:5px 0;clear:left}.react-datepicker__portal{position:fixed;width:100vw;height:100vh;background-color:#000c;left:0;top:0;justify-content:center;align-items:center;display:flex;z-index:2147483647}.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__time-name{width:3rem;line-height:3rem}@media (max-width: 400px),(max-height: 550px){.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__time-name{width:2rem;line-height:2rem}}.react-datepicker__portal .react-datepicker__current-month,.react-datepicker__portal .react-datepicker-time__header{font-size:1.44rem}.react-datepicker__children-container{width:13.8rem;margin:.4rem;padding-right:.2rem;padding-left:.2rem;height:auto}.react-datepicker__aria-live{position:absolute;clip-path:circle(0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;width:1px;white-space:nowrap}.react-datepicker__calendar-icon{width:1em;height:1em;vertical-align:-.125em}`)),document.head.appendChild(t)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();
import{r as v,ax as Fs,j as o,O as Vt,B as oe,R as G,G as Le,H as Es,Q as Ss,c as Is,a as zs}from"./i18n-DJdGDBdH.js";import{aN as As,aO as Ds,aP as Ts,aQ as Rs,g as Nt,a as Ut,u as Lt,s as we,f as Wt,m as mn,n as yt,aR as Os,aS as $s,j as ar,L as on,B as W,I as H,S as x,l as M,_ as et,aT as kt,aU as Bs,a9 as ht,aV as oi,a4 as vr,ac as ze,a0 as Ie,aW as dt,N as Ht,av as bt,C as Ce,J as Ps,aX as Ms,v as he,r as se,aY as Zs,G as Ot,ao as vn,ap as Xe,p as yr,aZ as Vs,a_ as Ns,z as qt,a3 as _e,H as ne,a7 as gt,ag as re,D as tt,t as rt,E as Ke,w as nt,a1 as ot,x as it,y as st,a$ as Us,b0 as ii,b1 as si,b2 as ai,b3 as yn,b4 as Ls,Z as $e,b5 as Hn,b6 as Ws,b7 as Hs,b8 as qs,am as Ks,an as qn,b9 as ci,ba as Qs,bb as Ys,bc as Gs,bd as Js,M as Dt,be as Xs,bf as ea,ab as li,bg as ta,b as ra,bh as na,bi as oa,bj as ia,bk as sa,bl as aa,bm as ca,bn as la,bo as Mr,aF as Kn,q as Gt,bp as Qn,aG as Yn,aq as ua,aw as da,bq as ui,br as di,bs as pi,bt as fi,a2 as pa,bu as fa,aI as ha,bv as ga,aJ as xa,aK as ma,aL as va,aM as ya}from"./custom-btn-DPxZMJXD.js";import{u as Ze}from"./useDisclosure-C9DVYU_W.js";function ba(e,t,r,n,i){const[s,a]=v.useState(()=>i&&r?r(e).matches:n?n(e).matches:t);return Ts(()=>{if(!r)return;const c=r(e),l=()=>{a(c.matches)};return l(),c.addEventListener("change",l),()=>{c.removeEventListener("change",l)}},[e,r]),s}const ja={...Fs},hi=ja.useSyncExternalStore;function wa(e,t,r,n,i){const s=v.useCallback(()=>t,[t]),a=v.useMemo(()=>{if(i&&r)return()=>r(e).matches;if(n!==null){const{matches:f}=n(e);return()=>f}return s},[s,e,n,i,r]),[c,l]=v.useMemo(()=>{if(r===null)return[s,()=>()=>{}];const f=r(e);return[()=>f.matches,u=>(f.addEventListener("change",u),()=>{f.removeEventListener("change",u)})]},[s,r,e]);return hi(l,c,a)}function gi(e={}){const{themeId:t}=e;return function(n,i={}){let s=As();s&&t&&(s=s[t]||s);const a=typeof window<"u"&&typeof window.matchMedia<"u",{defaultMatches:c=!1,matchMedia:l=a?window.matchMedia:null,ssrMatchMedia:p=null,noSsr:f=!1}=Ds({name:"MuiUseMediaQuery",props:i,theme:s});let u=typeof n=="function"?n(s):n;return u=u.replace(/^@media( ?)/m,""),u.includes("print")&&console.warn(["MUI: You have provided a `print` query to the `useMediaQuery` hook.","Using the print media query to modify print styles can lead to unexpected results.","Consider using the `displayPrint` field in the `sx` prop instead.","More information about `displayPrint` on our docs: https://mui.com/system/display/#display-in-print."].join(`
`)),(hi!==void 0?wa:ba)(u,c,l,p,f)}}gi();const Ae=gi({themeId:Rs}),xi=v.createContext();function _a(e){return Nt("MuiTable",e)}Ut("MuiTable",["root","stickyHeader"]);const ka=e=>{const{classes:t,stickyHeader:r}=e;return Wt({root:["root",r&&"stickyHeader"]},_a,t)},Ca=we("table",{name:"MuiTable",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.stickyHeader&&t.stickyHeader]}})(mn(({theme:e})=>({display:"table",width:"100%",borderCollapse:"collapse",borderSpacing:0,"& caption":{...e.typography.body2,padding:e.spacing(2),color:(e.vars||e).palette.text.secondary,textAlign:"left",captionSide:"bottom"},variants:[{props:({ownerState:t})=>t.stickyHeader,style:{borderCollapse:"separate"}}]}))),Gn="table",mi=v.forwardRef(function(t,r){const n=Lt({props:t,name:"MuiTable"}),{className:i,component:s=Gn,padding:a="normal",size:c="medium",stickyHeader:l=!1,...p}=n,f={...n,component:s,padding:a,size:c,stickyHeader:l},u=ka(f),d=v.useMemo(()=>({padding:a,size:c,stickyHeader:l}),[a,c,l]);return o.jsx(xi.Provider,{value:d,children:o.jsx(Ca,{as:s,role:s===Gn?null:"table",ref:r,className:Vt(u.root,i),ownerState:f,...p})})}),br=v.createContext();function Fa(e){return Nt("MuiTableBody",e)}Ut("MuiTableBody",["root"]);const Ea=e=>{const{classes:t}=e;return Wt({root:["root"]},Fa,t)},Sa=we("tbody",{name:"MuiTableBody",slot:"Root",overridesResolver:(e,t)=>t.root})({display:"table-row-group"}),Ia={variant:"body"},Jn="tbody",vi=v.forwardRef(function(t,r){const n=Lt({props:t,name:"MuiTableBody"}),{className:i,component:s=Jn,...a}=n,c={...n,component:s},l=Ea(c);return o.jsx(br.Provider,{value:Ia,children:o.jsx(Sa,{className:Vt(l.root,i),as:s,ref:r,role:s===Jn?null:"rowgroup",ownerState:c,...a})})});function za(e){return Nt("MuiTableCell",e)}const Aa=Ut("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),Da=e=>{const{classes:t,variant:r,align:n,padding:i,size:s,stickyHeader:a}=e,c={root:["root",r,a&&"stickyHeader",n!=="inherit"&&`align${yt(n)}`,i!=="normal"&&`padding${yt(i)}`,`size${yt(s)}`]};return Wt(c,za,t)},Ta=we("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,t[r.variant],t[`size${yt(r.size)}`],r.padding!=="normal"&&t[`padding${yt(r.padding)}`],r.align!=="inherit"&&t[`align${yt(r.align)}`],r.stickyHeader&&t.stickyHeader]}})(mn(({theme:e})=>({...e.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:e.vars?`1px solid ${e.vars.palette.TableCell.border}`:`1px solid
    ${e.palette.mode==="light"?Os(ar(e.palette.divider,1),.88):$s(ar(e.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(e.vars||e).palette.text.primary,lineHeight:e.typography.pxToRem(24),fontWeight:e.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(e.vars||e).palette.text.primary}},{props:{variant:"footer"},style:{color:(e.vars||e).palette.text.secondary,lineHeight:e.typography.pxToRem(21),fontSize:e.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${Aa.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:({ownerState:t})=>t.stickyHeader,style:{position:"sticky",top:0,zIndex:2,backgroundColor:(e.vars||e).palette.background.default}}]}))),qe=v.forwardRef(function(t,r){const n=Lt({props:t,name:"MuiTableCell"}),{align:i="inherit",className:s,component:a,padding:c,scope:l,size:p,sortDirection:f,variant:u,...d}=n,g=v.useContext(xi),b=v.useContext(br),_=b&&b.variant==="head";let k;a?k=a:k=_?"th":"td";let A=l;k==="td"?A=void 0:!A&&_&&(A="col");const y=u||b&&b.variant,T={...n,align:i,component:k,padding:c||(g&&g.padding?g.padding:"normal"),size:p||(g&&g.size?g.size:"medium"),sortDirection:f,stickyHeader:y==="head"&&g&&g.stickyHeader,variant:y},j=Da(T);let w=null;return f&&(w=f==="asc"?"ascending":"descending"),o.jsx(Ta,{as:k,ref:r,className:Vt(j.root,s),"aria-sort":w,scope:A,ownerState:T,...d})});function Ra(e){return Nt("MuiTableHead",e)}Ut("MuiTableHead",["root"]);const Oa=e=>{const{classes:t}=e;return Wt({root:["root"]},Ra,t)},$a=we("thead",{name:"MuiTableHead",slot:"Root",overridesResolver:(e,t)=>t.root})({display:"table-header-group"}),Ba={variant:"head"},Xn="thead",yi=v.forwardRef(function(t,r){const n=Lt({props:t,name:"MuiTableHead"}),{className:i,component:s=Xn,...a}=n,c={...n,component:s},l=Oa(c);return o.jsx(br.Provider,{value:Ba,children:o.jsx($a,{as:s,className:Vt(l.root,i),ref:r,role:s===Xn?null:"rowgroup",ownerState:c,...a})})});function Pa(e){return Nt("MuiTableRow",e)}const eo=Ut("MuiTableRow",["root","selected","hover","head","footer"]),Ma=e=>{const{classes:t,selected:r,hover:n,head:i,footer:s}=e;return Wt({root:["root",r&&"selected",n&&"hover",i&&"head",s&&"footer"]},Pa,t)},Za=we("tr",{name:"MuiTableRow",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.root,r.head&&t.head,r.footer&&t.footer]}})(mn(({theme:e})=>({color:"inherit",display:"table-row",verticalAlign:"middle",outline:0,[`&.${eo.hover}:hover`]:{backgroundColor:(e.vars||e).palette.action.hover},[`&.${eo.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:ar(e.palette.primary.main,e.palette.action.selectedOpacity),"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:ar(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity)}}}))),to="tr",Tt=v.forwardRef(function(t,r){const n=Lt({props:t,name:"MuiTableRow"}),{className:i,component:s=to,hover:a=!1,selected:c=!1,...l}=n,p=v.useContext(br),f={...n,component:s,hover:a,selected:c,head:p&&p.variant==="head",footer:p&&p.variant==="footer"},u=Ma(f);return o.jsx(Za,{as:s,ref:r,className:Vt(u.root,i),role:s===to?null:"row",ownerState:f,...l})}),Va="/",Na=()=>o.jsx(on,{href:Va,underline:"none",children:o.jsx(W,{sx:{width:"60px",height:"53px",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center","&:hover":{backgroundColor:"#414570"}},children:o.jsx(H,{icon:"mdi:apps",width:"24",height:"24",color:"#FFF"})})}),Ua=()=>o.jsx("header",{children:o.jsx(x,{direction:"row",justifyContent:"space-between",sx:{height:"54px",backgroundColor:"#4E5381",borderBottom:"1px solid #FFF"},children:o.jsx(Na,{})})}),La=()=>{const e=Ae("(min-width:993px)"),{t}=oe();return o.jsx(x,{flexDirection:"row",alignItems:"center",justifyContent:"space-between",sx:{width:"100%",height:"60px",padding:"0px 18px",color:"#FFF",backgroundColor:"#4E5381"},children:o.jsxs(x,{flexDirection:"row",alignItems:"center",gap:"15px",children:[o.jsx(H,{icon:"mdi:calendar",width:"24",height:"24",color:"#FFF"}),e&&o.jsx(M,{variant:"body2",fontSize:"16px",fontWeight:700,lineHeight:1,children:t("core:bookings")})]})})},Wa=({activeTabIndex:e,setActiveTabIndex:t,userPermissions:r})=>{const n=Ae("(min-width:993px)"),{t:i}=oe(),s=[{id:"sidebar-item-0",icon:"mdi:cog",label:i("core:bookingWorkflow.label"),description:i("core:bookingWorkflow.description"),permissionNo:806},{id:"sidebar-item-1",icon:"mdi:invoice-text-multiple",label:i("core:serviceManagement.label"),description:i("core:serviceManagement.description"),permissionNo:27},{id:"sidebar-item-2",icon:"mdi:flag",label:i("core:bookingPackages.label"),description:i("core:bookingPackages.description"),permissionNo:806},{id:"sidebar-item-3",icon:"mdi:calendar",label:i("core:shiftRoaster.label"),description:i("core:shiftRoaster.description"),permissionNo:806}];return o.jsx(W,{children:s.map((a,c)=>{var l;return o.jsx(x,{children:((r==null?void 0:r.is_super_admin)||((l=r==null?void 0:r.permissions)==null?void 0:l.includes(a.permissionNo)))&&o.jsxs(x,{flexDirection:"row",sx:{width:"100%",padding:n?"22px 15px":"15px",backgroundColor:e===c?"#F6F9FC":"#FFF",opacity:e===c?1:.7,transition:"opacity 0.3s",borderBottom:e===c?"2px dashed #DBE3EB":"2px dashed #E4EBF2",gap:"15px",cursor:"pointer","&:hover":{opacity:1}},onClick:()=>t(c),children:[o.jsx(W,{children:o.jsx(H,{icon:a.icon,width:"24",height:"24",color:e===c?"#1877F2":"#373B50"})}),n&&o.jsxs(x,{flexDirection:"column",children:[o.jsx(M,{variant:"body2",fontSize:"16px",fontWeight:500,color:e===c?"#1877F2":"#373B50",children:a.label}),o.jsx(M,{variant:"body2",fontSize:"12px",color:"#75799D",fontWeight:400,children:a.description})]})]})},a.id)})})},Ha=({activeTabIndex:e,setActiveTabIndex:t,userPermissions:r})=>{const n=Ae("(min-width:993px)");return o.jsxs(x,{sx:{width:`${n?"300px":"auto"}`,minWidth:`${n?"300px":"auto"}`,height:"100%",backgroundColor:"#FFF"},direction:"column",children:[o.jsx(La,{}),o.jsx(Wa,{activeTabIndex:e,setActiveTabIndex:t,userPermissions:r})]})},jr=e=>{const{children:t,title:r,subtitle:n}=e,i=Ae("(min-width:993px)");return o.jsxs(x,{sx:{overflow:"hidden",height:"60px",minHeight:"60px",backgroundColor:"#FFF",position:"sticky",top:0,left:0,right:0,zIndex:1,boxShadow:"0px 2px 6px 0px rgba(78, 83, 129, 0.20)",paddingInlineStart:"20px"},direction:"row",justifyContent:"space-between",children:[o.jsxs(x,{justifyContent:"center",children:[o.jsx(M,{variant:"h6",fontSize:"16px",fontWeight:700,sx:{color:"#202124"},children:r}),i&&o.jsx(M,{variant:"body2",fontSize:"12px",fontWeight:400,sx:{color:"#75799D"},children:n})]}),t]})},ro=({value:e,title:t,description:r,selected:n,onChange:i,children:s=null})=>o.jsxs(W,{onClick:()=>i(e),sx:{width:"100%",maxWidth:"705px",border:`1px solid ${n?"#1877F2":"#DBE3EB"}`,borderRadius:0,padding:"20px",cursor:"pointer",display:"flex",alignItems:"flex-start",gap:"10px",backgroundColor:n?"#F6F9FC":"#FFFFFF",transition:"border 0.2s, background-color 0.2s",color:n?"#1877F2":"#202124"},children:[o.jsx(Bs,{checked:n,value:e,onChange:()=>i(e)}),o.jsxs(W,{children:[o.jsx(M,{fontSize:"16px",mb:"4px",textTransform:"capitalize",children:t}),o.jsx(M,{variant:"body2",fontSize:"14px",color:"#75799D",children:r}),n&&s]})]}),bi=we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",width:"160px",transition:"all 0.2s ease",color:"#213242",backgroundColor:"#FFF",border:"1px solid #E4EBF2",borderRadius:"2px",fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",width:"100%",paddingInlineEnd:"35px !important",paddingBlock:"10px !important"},"&:hover":{backgroundColor:"#E4EBF2"},".MuiSelect-icon":{color:"#213242"}}));we(kt)(()=>({"& .MuiInputBase-root":{fontSize:"1rem",height:"40px",width:"100%",color:"#213242",backgroundColor:"#FFF",border:"1px solid #E4EBF2",borderRadius:"2px",paddingRight:"35px"},"& .MuiOutlinedInput-notchedOutline":{border:"none"},"&:hover .MuiInputBase-root":{backgroundColor:"#E4EBF2"},"& .MuiAutocomplete-endAdornment":{color:"#213242"}}));const qa=({selectedServiceId:e,onChange:t,servicesData:r})=>{const{t:n}=oe(),[i,s]=v.useState(""),[a]=ht(i,500),{data:c,isLoading:l}=oi(a),[p,f]=v.useState(r==null?void 0:r.find(d=>d.id===Number(e))),u=c??[];return o.jsx(vr,{size:{xs:12},children:o.jsx(kt,{options:u,getOptionLabel:d=>d?d.name:"",filterOptions:d=>d,value:p,onChange:(d,g)=>{f(g),t(g==null?void 0:g.id)},onInputChange:(d,g)=>s(g),loading:l,sx:{pointerEvents:"auto",width:"400px"},renderOption:(d,g)=>v.createElement(x,{...d,component:"li",key:g.id,flexDirection:"row",alignItems:"center",gap:1,sx:{pointerEvents:"auto"}},o.jsx("img",{src:g.avatar,width:"32",height:"32",alt:"service"}),o.jsx(x,{flexDirection:"column",children:o.jsx(M,{component:"p",children:g.name})})),renderInput:d=>o.jsx(ze,{...d.InputProps,inputProps:d.inputProps,placeholder:n("core:select-service"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"5px",padding:"8px 8px 8px 14px !important",backgroundColor:"#fff"},startAdornment:p?o.jsx(dt,{src:p.avatar,alt:p.name,sx:{width:32,height:32,marginInlineEnd:"10px",borderRadius:"0px"}}):null,endAdornment:o.jsx(Ie,{})})})})};var Kt=e=>e.type==="checkbox",ut=e=>e instanceof Date,Ee=e=>e==null;const ji=e=>typeof e=="object";var ve=e=>!Ee(e)&&!Array.isArray(e)&&ji(e)&&!ut(e),wi=e=>ve(e)&&e.target?Kt(e.target)?e.target.checked:e.target.value:e,Ka=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,_i=(e,t)=>e.has(Ka(t)),Qa=e=>{const t=e.constructor&&e.constructor.prototype;return ve(t)&&t.hasOwnProperty("isPrototypeOf")},bn=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function pe(e){let t;const r=Array.isArray(e),n=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(bn&&(e instanceof Blob||n))&&(r||ve(e)))if(t=r?[]:Object.create(Object.getPrototypeOf(e)),!r&&!Qa(e))t=e;else for(const i in e)e.hasOwnProperty(i)&&(t[i]=pe(e[i]));else return e;return t}var wr=e=>/^\w*$/.test(e),fe=e=>e===void 0,_r=e=>Array.isArray(e)?e.filter(Boolean):[],jn=e=>_r(e.replace(/["|']|\]/g,"").split(/\.|\[/)),P=(e,t,r)=>{if(!t||!ve(e))return r;const n=(wr(t)?[t]:jn(t)).reduce((i,s)=>Ee(i)?i:i[s],e);return fe(n)||n===e?fe(e[t])?r:e[t]:n},Re=e=>typeof e=="boolean",ae=(e,t,r)=>{let n=-1;const i=wr(t)?[t]:jn(t),s=i.length,a=s-1;for(;++n<s;){const c=i[n];let l=r;if(n!==a){const p=e[c];l=ve(p)||Array.isArray(p)?p:isNaN(+i[n+1])?{}:[]}if(c==="__proto__"||c==="constructor"||c==="prototype")return;e[c]=l,e=e[c]}};const cr={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Be={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},We={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},ki=G.createContext(null);ki.displayName="HookFormContext";const kr=()=>G.useContext(ki);var Ci=(e,t,r,n=!0)=>{const i={defaultValues:t._defaultValues};for(const s in e)Object.defineProperty(i,s,{get:()=>{const a=s;return t._proxyFormState[a]!==Be.all&&(t._proxyFormState[a]=!n||Be.all),r&&(r[a]=!0),e[a]}});return i};const Cr=typeof window<"u"?G.useLayoutEffect:G.useEffect;function Ya(e){const t=kr(),{control:r=t.control,disabled:n,name:i,exact:s}=e||{},[a,c]=G.useState(r._formState),l=G.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return Cr(()=>r._subscribe({name:i,formState:l.current,exact:s,callback:p=>{!n&&c({...r._formState,...p})}}),[i,n,s]),G.useEffect(()=>{l.current.isValid&&r._setValid(!0)},[r]),G.useMemo(()=>Ci(a,r,l.current,!1),[a,r])}var Ne=e=>typeof e=="string",Fi=(e,t,r,n,i)=>Ne(e)?(n&&t.watch.add(e),P(r,e,i)):Array.isArray(e)?e.map(s=>(n&&t.watch.add(s),P(r,s))):(n&&(t.watchAll=!0),r),sn=e=>Ee(e)||!ji(e);function He(e,t,r=new WeakSet){if(sn(e)||sn(t))return e===t;if(ut(e)&&ut(t))return e.getTime()===t.getTime();const n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const s of n){const a=e[s];if(!i.includes(s))return!1;if(s!=="ref"){const c=t[s];if(ut(a)&&ut(c)||ve(a)&&ve(c)||Array.isArray(a)&&Array.isArray(c)?!He(a,c,r):a!==c)return!1}}return!0}function Ei(e){const t=kr(),{control:r=t.control,name:n,defaultValue:i,disabled:s,exact:a,compute:c}=e||{},l=G.useRef(i),p=G.useRef(c),f=G.useRef(void 0);p.current=c;const u=G.useMemo(()=>r._getWatch(n,l.current),[r,n]),[d,g]=G.useState(p.current?p.current(u):u);return Cr(()=>r._subscribe({name:n,formState:{values:!0},exact:a,callback:b=>{if(!s){const _=Fi(n,r._names,b.values||r._formValues,!1,l.current);if(p.current){const k=p.current(_);He(k,f.current)||(g(k),f.current=k)}else g(_)}}}),[r,s,n,a]),G.useEffect(()=>r._removeUnmounted()),d}function Ga(e){const t=kr(),{name:r,disabled:n,control:i=t.control,shouldUnregister:s,defaultValue:a}=e,c=_i(i._names.array,r),l=G.useMemo(()=>P(i._formValues,r,P(i._defaultValues,r,a)),[i,r,a]),p=Ei({control:i,name:r,defaultValue:l,exact:!0}),f=Ya({control:i,name:r,exact:!0}),u=G.useRef(e),d=G.useRef(i.register(r,{...e.rules,value:p,...Re(e.disabled)?{disabled:e.disabled}:{}}));u.current=e;const g=G.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!P(f.errors,r)},isDirty:{enumerable:!0,get:()=>!!P(f.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!P(f.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!P(f.validatingFields,r)},error:{enumerable:!0,get:()=>P(f.errors,r)}}),[f,r]),b=G.useCallback(y=>d.current.onChange({target:{value:wi(y),name:r},type:cr.CHANGE}),[r]),_=G.useCallback(()=>d.current.onBlur({target:{value:P(i._formValues,r),name:r},type:cr.BLUR}),[r,i._formValues]),k=G.useCallback(y=>{const T=P(i._fields,r);T&&y&&(T._f.ref={focus:()=>y.focus&&y.focus(),select:()=>y.select&&y.select(),setCustomValidity:j=>y.setCustomValidity(j),reportValidity:()=>y.reportValidity()})},[i._fields,r]),A=G.useMemo(()=>({name:r,value:p,...Re(n)||f.disabled?{disabled:f.disabled||n}:{},onChange:b,onBlur:_,ref:k}),[r,n,f.disabled,b,_,k,p]);return G.useEffect(()=>{const y=i._options.shouldUnregister||s;i.register(r,{...u.current.rules,...Re(u.current.disabled)?{disabled:u.current.disabled}:{}});const T=(j,w)=>{const C=P(i._fields,j);C&&C._f&&(C._f.mount=w)};if(T(r,!0),y){const j=pe(P(i._options.defaultValues,r));ae(i._defaultValues,r,j),fe(P(i._formValues,r))&&ae(i._formValues,r,j)}return!c&&i.register(r),()=>{(c?y&&!i._state.action:y)?i.unregister(r):T(r,!1)}},[r,i,c,s]),G.useEffect(()=>{i._setDisabledField({disabled:n,name:r})},[n,r,i]),G.useMemo(()=>({field:A,formState:f,fieldState:g}),[A,f,g])}const Ue=e=>e.render(Ga(e));var wn=(e,t,r,n,i)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:i||!0}}:{},Se=e=>Array.isArray(e)?e:[e],no=()=>{let e=[];return{get observers(){return e},next:i=>{for(const s of e)s.next&&s.next(i)},subscribe:i=>(e.push(i),{unsubscribe:()=>{e=e.filter(s=>s!==i)}}),unsubscribe:()=>{e=[]}}},Fe=e=>ve(e)&&!Object.keys(e).length,_n=e=>e.type==="file",Me=e=>typeof e=="function",lr=e=>{if(!bn)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Si=e=>e.type==="select-multiple",kn=e=>e.type==="radio",Ja=e=>kn(e)||Kt(e),Zr=e=>lr(e)&&e.isConnected;function Xa(e,t){const r=t.slice(0,-1).length;let n=0;for(;n<r;)e=fe(e)?n++:e[t[n++]];return e}function ec(e){for(const t in e)if(e.hasOwnProperty(t)&&!fe(e[t]))return!1;return!0}function me(e,t){const r=Array.isArray(t)?t:wr(t)?[t]:jn(t),n=r.length===1?e:Xa(e,r),i=r.length-1,s=r[i];return n&&delete n[s],i!==0&&(ve(n)&&Fe(n)||Array.isArray(n)&&ec(n))&&me(e,r.slice(0,-1)),e}var Ii=e=>{for(const t in e)if(Me(e[t]))return!0;return!1};function ur(e,t={}){const r=Array.isArray(e);if(ve(e)||r)for(const n in e)Array.isArray(e[n])||ve(e[n])&&!Ii(e[n])?(t[n]=Array.isArray(e[n])?[]:{},ur(e[n],t[n])):Ee(e[n])||(t[n]=!0);return t}function zi(e,t,r){const n=Array.isArray(e);if(ve(e)||n)for(const i in e)Array.isArray(e[i])||ve(e[i])&&!Ii(e[i])?fe(t)||sn(r[i])?r[i]=Array.isArray(e[i])?ur(e[i],[]):{...ur(e[i])}:zi(e[i],Ee(t)?{}:t[i],r[i]):r[i]=!He(e[i],t[i]);return r}var Et=(e,t)=>zi(e,t,ur(t));const oo={value:!1,isValid:!1},io={value:!0,isValid:!0};var Ai=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!fe(e[0].attributes.value)?fe(e[0].value)||e[0].value===""?io:{value:e[0].value,isValid:!0}:io:oo}return oo},Di=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>fe(e)?e:t?e===""?NaN:e&&+e:r&&Ne(e)?new Date(e):n?n(e):e;const so={isValid:!1,value:null};var Ti=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,so):so;function ao(e){const t=e.ref;return _n(t)?t.files:kn(t)?Ti(e.refs).value:Si(t)?[...t.selectedOptions].map(({value:r})=>r):Kt(t)?Ai(e.refs).value:Di(fe(t.value)?e.ref.value:t.value,e)}var tc=(e,t,r,n)=>{const i={};for(const s of e){const a=P(t,s);a&&ae(i,s,a._f)}return{criteriaMode:r,names:[...e],fields:i,shouldUseNativeValidation:n}},dr=e=>e instanceof RegExp,St=e=>fe(e)?e:dr(e)?e.source:ve(e)?dr(e.value)?e.value.source:e.value:e,mt=e=>({isOnSubmit:!e||e===Be.onSubmit,isOnBlur:e===Be.onBlur,isOnChange:e===Be.onChange,isOnAll:e===Be.all,isOnTouch:e===Be.onTouched});const co="AsyncFunction";var rc=e=>!!e&&!!e.validate&&!!(Me(e.validate)&&e.validate.constructor.name===co||ve(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===co)),nc=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),an=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(n=>e.startsWith(n)&&/^\.\w+/.test(e.slice(n.length))));const jt=(e,t,r,n)=>{for(const i of r||Object.keys(e)){const s=P(e,i);if(s){const{_f:a,...c}=s;if(a){if(a.refs&&a.refs[0]&&t(a.refs[0],i)&&!n)return!0;if(a.ref&&t(a.ref,a.name)&&!n)return!0;if(jt(c,t))break}else if(ve(c)&&jt(c,t))break}}};function lo(e,t,r){const n=P(e,r);if(n||wr(r))return{error:n,name:r};const i=r.split(".");for(;i.length;){const s=i.join("."),a=P(t,s),c=P(e,s);if(a&&!Array.isArray(a)&&r!==s)return{name:r};if(c&&c.type)return{name:s,error:c};if(c&&c.root&&c.root.type)return{name:`${s}.root`,error:c.root};i.pop()}return{name:r}}var oc=(e,t,r,n)=>{r(e);const{name:i,...s}=e;return Fe(s)||Object.keys(s).length>=Object.keys(t).length||Object.keys(s).find(a=>t[a]===(!n||Be.all))},ic=(e,t,r)=>!e||!t||e===t||Se(e).some(n=>n&&(r?n===t:n.startsWith(t)||t.startsWith(n))),sc=(e,t,r,n,i)=>i.isOnAll?!1:!r&&i.isOnTouch?!(t||e):(r?n.isOnBlur:i.isOnBlur)?!e:(r?n.isOnChange:i.isOnChange)?e:!0,ac=(e,t)=>!_r(P(e,t)).length&&me(e,t),Ri=(e,t,r)=>{const n=Se(P(e,r));return ae(n,"root",t[r]),ae(e,r,n),e},or=e=>Ne(e);function uo(e,t,r="validate"){if(or(e)||Array.isArray(e)&&e.every(or)||Re(e)&&!e)return{type:r,message:or(e)?e:"",ref:t}}var xt=e=>ve(e)&&!dr(e)?e:{value:e,message:""},cn=async(e,t,r,n,i,s)=>{const{ref:a,refs:c,required:l,maxLength:p,minLength:f,min:u,max:d,pattern:g,validate:b,name:_,valueAsNumber:k,mount:A}=e._f,y=P(r,_);if(!A||t.has(_))return{};const T=c?c[0]:a,j=I=>{i&&T.reportValidity&&(T.setCustomValidity(Re(I)?"":I||""),T.reportValidity())},w={},C=kn(a),$=Kt(a),R=C||$,z=(k||_n(a))&&fe(a.value)&&fe(y)||lr(a)&&a.value===""||y===""||Array.isArray(y)&&!y.length,F=wn.bind(null,_,n,w),Z=(I,D,U,X=We.maxLength,O=We.minLength)=>{const N=I?D:U;w[_]={type:I?X:O,message:N,ref:a,...F(I?X:O,N)}};if(s?!Array.isArray(y)||!y.length:l&&(!R&&(z||Ee(y))||Re(y)&&!y||$&&!Ai(c).isValid||C&&!Ti(c).isValid)){const{value:I,message:D}=or(l)?{value:!!l,message:l}:xt(l);if(I&&(w[_]={type:We.required,message:D,ref:T,...F(We.required,D)},!n))return j(D),w}if(!z&&(!Ee(u)||!Ee(d))){let I,D;const U=xt(d),X=xt(u);if(!Ee(y)&&!isNaN(y)){const O=a.valueAsNumber||y&&+y;Ee(U.value)||(I=O>U.value),Ee(X.value)||(D=O<X.value)}else{const O=a.valueAsDate||new Date(y),N=J=>new Date(new Date().toDateString()+" "+J),le=a.type=="time",ee=a.type=="week";Ne(U.value)&&y&&(I=le?N(y)>N(U.value):ee?y>U.value:O>new Date(U.value)),Ne(X.value)&&y&&(D=le?N(y)<N(X.value):ee?y<X.value:O<new Date(X.value))}if((I||D)&&(Z(!!I,U.message,X.message,We.max,We.min),!n))return j(w[_].message),w}if((p||f)&&!z&&(Ne(y)||s&&Array.isArray(y))){const I=xt(p),D=xt(f),U=!Ee(I.value)&&y.length>+I.value,X=!Ee(D.value)&&y.length<+D.value;if((U||X)&&(Z(U,I.message,D.message),!n))return j(w[_].message),w}if(g&&!z&&Ne(y)){const{value:I,message:D}=xt(g);if(dr(I)&&!y.match(I)&&(w[_]={type:We.pattern,message:D,ref:a,...F(We.pattern,D)},!n))return j(D),w}if(b){if(Me(b)){const I=await b(y,r),D=uo(I,T);if(D&&(w[_]={...D,...F(We.validate,D.message)},!n))return j(D.message),w}else if(ve(b)){let I={};for(const D in b){if(!Fe(I)&&!n)break;const U=uo(await b[D](y,r),T,D);U&&(I={...U,...F(D,U.message)},j(U.message),n&&(w[_]=I))}if(!Fe(I)&&(w[_]={ref:T,...I},!n))return w}}return j(!0),w};const cc={mode:Be.onSubmit,reValidateMode:Be.onChange,shouldFocusError:!0};function lc(e={}){let t={...cc,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:Me(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},n={},i=ve(t.defaultValues)||ve(t.values)?pe(t.defaultValues||t.values)||{}:{},s=t.shouldUnregister?{}:pe(i),a={action:!1,mount:!1,watch:!1},c={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},l,p=0;const f={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let u={...f};const d={array:no(),state:no()},g=t.criteriaMode===Be.all,b=h=>m=>{clearTimeout(p),p=setTimeout(h,m)},_=async h=>{if(!t.disabled&&(f.isValid||u.isValid||h)){const m=t.resolver?Fe((await $()).errors):await z(n,!0);m!==r.isValid&&d.state.next({isValid:m})}},k=(h,m)=>{!t.disabled&&(f.isValidating||f.validatingFields||u.isValidating||u.validatingFields)&&((h||Array.from(c.mount)).forEach(E=>{E&&(m?ae(r.validatingFields,E,m):me(r.validatingFields,E))}),d.state.next({validatingFields:r.validatingFields,isValidating:!Fe(r.validatingFields)}))},A=(h,m=[],E,L,V=!0,B=!0)=>{if(L&&E&&!t.disabled){if(a.action=!0,B&&Array.isArray(P(n,h))){const Q=E(P(n,h),L.argA,L.argB);V&&ae(n,h,Q)}if(B&&Array.isArray(P(r.errors,h))){const Q=E(P(r.errors,h),L.argA,L.argB);V&&ae(r.errors,h,Q),ac(r.errors,h)}if((f.touchedFields||u.touchedFields)&&B&&Array.isArray(P(r.touchedFields,h))){const Q=E(P(r.touchedFields,h),L.argA,L.argB);V&&ae(r.touchedFields,h,Q)}(f.dirtyFields||u.dirtyFields)&&(r.dirtyFields=Et(i,s)),d.state.next({name:h,isDirty:Z(h,m),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else ae(s,h,m)},y=(h,m)=>{ae(r.errors,h,m),d.state.next({errors:r.errors})},T=h=>{r.errors=h,d.state.next({errors:r.errors,isValid:!1})},j=(h,m,E,L)=>{const V=P(n,h);if(V){const B=P(s,h,fe(E)?P(i,h):E);fe(B)||L&&L.defaultChecked||m?ae(s,h,m?B:ao(V._f)):U(h,B),a.mount&&_()}},w=(h,m,E,L,V)=>{let B=!1,Q=!1;const ce={name:h};if(!t.disabled){if(!E||L){(f.isDirty||u.isDirty)&&(Q=r.isDirty,r.isDirty=ce.isDirty=Z(),B=Q!==ce.isDirty);const ue=He(P(i,h),m);Q=!!P(r.dirtyFields,h),ue?me(r.dirtyFields,h):ae(r.dirtyFields,h,!0),ce.dirtyFields=r.dirtyFields,B=B||(f.dirtyFields||u.dirtyFields)&&Q!==!ue}if(E){const ue=P(r.touchedFields,h);ue||(ae(r.touchedFields,h,E),ce.touchedFields=r.touchedFields,B=B||(f.touchedFields||u.touchedFields)&&ue!==E)}B&&V&&d.state.next(ce)}return B?ce:{}},C=(h,m,E,L)=>{const V=P(r.errors,h),B=(f.isValid||u.isValid)&&Re(m)&&r.isValid!==m;if(t.delayError&&E?(l=b(()=>y(h,E)),l(t.delayError)):(clearTimeout(p),l=null,E?ae(r.errors,h,E):me(r.errors,h)),(E?!He(V,E):V)||!Fe(L)||B){const Q={...L,...B&&Re(m)?{isValid:m}:{},errors:r.errors,name:h};r={...r,...Q},d.state.next(Q)}},$=async h=>{k(h,!0);const m=await t.resolver(s,t.context,tc(h||c.mount,n,t.criteriaMode,t.shouldUseNativeValidation));return k(h),m},R=async h=>{const{errors:m}=await $(h);if(h)for(const E of h){const L=P(m,E);L?ae(r.errors,E,L):me(r.errors,E)}else r.errors=m;return m},z=async(h,m,E={valid:!0})=>{for(const L in h){const V=h[L];if(V){const{_f:B,...Q}=V;if(B){const ce=c.array.has(B.name),ue=V._f&&rc(V._f);ue&&f.validatingFields&&k([L],!0);const Oe=await cn(V,c.disabled,s,g,t.shouldUseNativeValidation&&!m,ce);if(ue&&f.validatingFields&&k([L]),Oe[B.name]&&(E.valid=!1,m))break;!m&&(P(Oe,B.name)?ce?Ri(r.errors,Oe,B.name):ae(r.errors,B.name,Oe[B.name]):me(r.errors,B.name))}!Fe(Q)&&await z(Q,m,E)}}return E.valid},F=()=>{for(const h of c.unMount){const m=P(n,h);m&&(m._f.refs?m._f.refs.every(E=>!Zr(E)):!Zr(m._f.ref))&&ke(h)}c.unMount=new Set},Z=(h,m)=>!t.disabled&&(h&&m&&ae(s,h,m),!He(J(),i)),I=(h,m,E)=>Fi(h,c,{...a.mount?s:fe(m)?i:Ne(h)?{[h]:m}:m},E,m),D=h=>_r(P(a.mount?s:i,h,t.shouldUnregister?P(i,h,[]):[])),U=(h,m,E={})=>{const L=P(n,h);let V=m;if(L){const B=L._f;B&&(!B.disabled&&ae(s,h,Di(m,B)),V=lr(B.ref)&&Ee(m)?"":m,Si(B.ref)?[...B.ref.options].forEach(Q=>Q.selected=V.includes(Q.value)):B.refs?Kt(B.ref)?B.refs.forEach(Q=>{(!Q.defaultChecked||!Q.disabled)&&(Array.isArray(V)?Q.checked=!!V.find(ce=>ce===Q.value):Q.checked=V===Q.value||!!V)}):B.refs.forEach(Q=>Q.checked=Q.value===V):_n(B.ref)?B.ref.value="":(B.ref.value=V,B.ref.type||d.state.next({name:h,values:pe(s)})))}(E.shouldDirty||E.shouldTouch)&&w(h,V,E.shouldTouch,E.shouldDirty,!0),E.shouldValidate&&ee(h)},X=(h,m,E)=>{for(const L in m){if(!m.hasOwnProperty(L))return;const V=m[L],B=h+"."+L,Q=P(n,B);(c.array.has(h)||ve(V)||Q&&!Q._f)&&!ut(V)?X(B,V,E):U(B,V,E)}},O=(h,m,E={})=>{const L=P(n,h),V=c.array.has(h),B=pe(m);ae(s,h,B),V?(d.array.next({name:h,values:pe(s)}),(f.isDirty||f.dirtyFields||u.isDirty||u.dirtyFields)&&E.shouldDirty&&d.state.next({name:h,dirtyFields:Et(i,s),isDirty:Z(h,B)})):L&&!L._f&&!Ee(B)?X(h,B,E):U(h,B,E),an(h,c)&&d.state.next({...r,name:h}),d.state.next({name:a.mount?h:void 0,values:pe(s)})},N=async h=>{a.mount=!0;const m=h.target;let E=m.name,L=!0;const V=P(n,E),B=ue=>{L=Number.isNaN(ue)||ut(ue)&&isNaN(ue.getTime())||He(ue,P(s,E,ue))},Q=mt(t.mode),ce=mt(t.reValidateMode);if(V){let ue,Oe;const Yt=m.type?ao(V._f):wi(h),Qe=h.type===cr.BLUR||h.type===cr.FOCUS_OUT,_s=!nc(V._f)&&!t.resolver&&!P(r.errors,E)&&!V._f.deps||sc(Qe,P(r.touchedFields,E),r.isSubmitted,ce,Q),Br=an(E,c,Qe);ae(s,E,Yt),Qe?(!m||!m.readOnly)&&(V._f.onBlur&&V._f.onBlur(h),l&&l(0)):V._f.onChange&&V._f.onChange(h);const Pr=w(E,Yt,Qe),ks=!Fe(Pr)||Br;if(!Qe&&d.state.next({name:E,type:h.type,values:pe(s)}),_s)return(f.isValid||u.isValid)&&(t.mode==="onBlur"?Qe&&_():Qe||_()),ks&&d.state.next({name:E,...Br?{}:Pr});if(!Qe&&Br&&d.state.next({...r}),t.resolver){const{errors:Ln}=await $([E]);if(B(Yt),L){const Cs=lo(r.errors,n,E),Wn=lo(Ln,n,Cs.name||E);ue=Wn.error,E=Wn.name,Oe=Fe(Ln)}}else k([E],!0),ue=(await cn(V,c.disabled,s,g,t.shouldUseNativeValidation))[E],k([E]),B(Yt),L&&(ue?Oe=!1:(f.isValid||u.isValid)&&(Oe=await z(n,!0)));L&&(V._f.deps&&ee(V._f.deps),C(E,Oe,ue,Pr))}},le=(h,m)=>{if(P(r.errors,m)&&h.focus)return h.focus(),1},ee=async(h,m={})=>{let E,L;const V=Se(h);if(t.resolver){const B=await R(fe(h)?h:V);E=Fe(B),L=h?!V.some(Q=>P(B,Q)):E}else h?(L=(await Promise.all(V.map(async B=>{const Q=P(n,B);return await z(Q&&Q._f?{[B]:Q}:Q)}))).every(Boolean),!(!L&&!r.isValid)&&_()):L=E=await z(n);return d.state.next({...!Ne(h)||(f.isValid||u.isValid)&&E!==r.isValid?{}:{name:h},...t.resolver||!h?{isValid:E}:{},errors:r.errors}),m.shouldFocus&&!L&&jt(n,le,h?V:c.mount),L},J=h=>{const m={...a.mount?s:i};return fe(h)?m:Ne(h)?P(m,h):h.map(E=>P(m,E))},te=(h,m)=>({invalid:!!P((m||r).errors,h),isDirty:!!P((m||r).dirtyFields,h),error:P((m||r).errors,h),isValidating:!!P(r.validatingFields,h),isTouched:!!P((m||r).touchedFields,h)}),Y=h=>{h&&Se(h).forEach(m=>me(r.errors,m)),d.state.next({errors:h?r.errors:{}})},K=(h,m,E)=>{const L=(P(n,h,{_f:{}})._f||{}).ref,V=P(r.errors,h)||{},{ref:B,message:Q,type:ce,...ue}=V;ae(r.errors,h,{...ue,...m,ref:L}),d.state.next({name:h,errors:r.errors,isValid:!1}),E&&E.shouldFocus&&L&&L.focus&&L.focus()},ie=(h,m)=>Me(h)?d.state.subscribe({next:E=>"values"in E&&h(I(void 0,m),E)}):I(h,m,!0),je=h=>d.state.subscribe({next:m=>{ic(h.name,m.name,h.exact)&&oc(m,h.formState||f,ws,h.reRenderRoot)&&h.callback({values:{...s},...r,...m,defaultValues:i})}}).unsubscribe,Te=h=>(a.mount=!0,u={...u,...h.formState},je({...h,formState:u})),ke=(h,m={})=>{for(const E of h?Se(h):c.mount)c.mount.delete(E),c.array.delete(E),m.keepValue||(me(n,E),me(s,E)),!m.keepError&&me(r.errors,E),!m.keepDirty&&me(r.dirtyFields,E),!m.keepTouched&&me(r.touchedFields,E),!m.keepIsValidating&&me(r.validatingFields,E),!t.shouldUnregister&&!m.keepDefaultValue&&me(i,E);d.state.next({values:pe(s)}),d.state.next({...r,...m.keepDirty?{isDirty:Z()}:{}}),!m.keepIsValid&&_()},Mn=({disabled:h,name:m})=>{(Re(h)&&a.mount||h||c.disabled.has(m))&&(h?c.disabled.add(m):c.disabled.delete(m))},Or=(h,m={})=>{let E=P(n,h);const L=Re(m.disabled)||Re(t.disabled);return ae(n,h,{...E||{},_f:{...E&&E._f?E._f:{ref:{name:h}},name:h,mount:!0,...m}}),c.mount.add(h),E?Mn({disabled:Re(m.disabled)?m.disabled:t.disabled,name:h}):j(h,!0,m.value),{...L?{disabled:m.disabled||t.disabled}:{},...t.progressive?{required:!!m.required,min:St(m.min),max:St(m.max),minLength:St(m.minLength),maxLength:St(m.maxLength),pattern:St(m.pattern)}:{},name:h,onChange:N,onBlur:N,ref:V=>{if(V){Or(h,m),E=P(n,h);const B=fe(V.value)&&V.querySelectorAll&&V.querySelectorAll("input,select,textarea")[0]||V,Q=Ja(B),ce=E._f.refs||[];if(Q?ce.find(ue=>ue===B):B===E._f.ref)return;ae(n,h,{_f:{...E._f,...Q?{refs:[...ce.filter(Zr),B,...Array.isArray(P(i,h))?[{}]:[]],ref:{type:B.type,name:h}}:{ref:B}}}),j(h,!1,void 0,B)}else E=P(n,h,{}),E._f&&(E._f.mount=!1),(t.shouldUnregister||m.shouldUnregister)&&!(_i(c.array,h)&&a.action)&&c.unMount.add(h)}}},$r=()=>t.shouldFocusError&&jt(n,le,c.mount),ys=h=>{Re(h)&&(d.state.next({disabled:h}),jt(n,(m,E)=>{const L=P(n,E);L&&(m.disabled=L._f.disabled||h,Array.isArray(L._f.refs)&&L._f.refs.forEach(V=>{V.disabled=L._f.disabled||h}))},0,!1))},Zn=(h,m)=>async E=>{let L;E&&(E.preventDefault&&E.preventDefault(),E.persist&&E.persist());let V=pe(s);if(d.state.next({isSubmitting:!0}),t.resolver){const{errors:B,values:Q}=await $();r.errors=B,V=pe(Q)}else await z(n);if(c.disabled.size)for(const B of c.disabled)me(V,B);if(me(r.errors,"root"),Fe(r.errors)){d.state.next({errors:{}});try{await h(V,E)}catch(B){L=B}}else m&&await m({...r.errors},E),$r(),setTimeout($r);if(d.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Fe(r.errors)&&!L,submitCount:r.submitCount+1,errors:r.errors}),L)throw L},bs=(h,m={})=>{P(n,h)&&(fe(m.defaultValue)?O(h,pe(P(i,h))):(O(h,m.defaultValue),ae(i,h,pe(m.defaultValue))),m.keepTouched||me(r.touchedFields,h),m.keepDirty||(me(r.dirtyFields,h),r.isDirty=m.defaultValue?Z(h,pe(P(i,h))):Z()),m.keepError||(me(r.errors,h),f.isValid&&_()),d.state.next({...r}))},Vn=(h,m={})=>{const E=h?pe(h):i,L=pe(E),V=Fe(h),B=V?i:L;if(m.keepDefaultValues||(i=E),!m.keepValues){if(m.keepDirtyValues){const Q=new Set([...c.mount,...Object.keys(Et(i,s))]);for(const ce of Array.from(Q))P(r.dirtyFields,ce)?ae(B,ce,P(s,ce)):O(ce,P(B,ce))}else{if(bn&&fe(h))for(const Q of c.mount){const ce=P(n,Q);if(ce&&ce._f){const ue=Array.isArray(ce._f.refs)?ce._f.refs[0]:ce._f.ref;if(lr(ue)){const Oe=ue.closest("form");if(Oe){Oe.reset();break}}}}if(m.keepFieldsRef)for(const Q of c.mount)O(Q,P(B,Q));else n={}}s=t.shouldUnregister?m.keepDefaultValues?pe(i):{}:pe(B),d.array.next({values:{...B}}),d.state.next({values:{...B}})}c={mount:m.keepDirtyValues?c.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},a.mount=!f.isValid||!!m.keepIsValid||!!m.keepDirtyValues,a.watch=!!t.shouldUnregister,d.state.next({submitCount:m.keepSubmitCount?r.submitCount:0,isDirty:V?!1:m.keepDirty?r.isDirty:!!(m.keepDefaultValues&&!He(h,i)),isSubmitted:m.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:V?{}:m.keepDirtyValues?m.keepDefaultValues&&s?Et(i,s):r.dirtyFields:m.keepDefaultValues&&h?Et(i,h):m.keepDirty?r.dirtyFields:{},touchedFields:m.keepTouched?r.touchedFields:{},errors:m.keepErrors?r.errors:{},isSubmitSuccessful:m.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1,defaultValues:i})},Nn=(h,m)=>Vn(Me(h)?h(s):h,m),js=(h,m={})=>{const E=P(n,h),L=E&&E._f;if(L){const V=L.refs?L.refs[0]:L.ref;V.focus&&(V.focus(),m.shouldSelect&&Me(V.select)&&V.select())}},ws=h=>{r={...r,...h}},Un={control:{register:Or,unregister:ke,getFieldState:te,handleSubmit:Zn,setError:K,_subscribe:je,_runSchema:$,_focusError:$r,_getWatch:I,_getDirty:Z,_setValid:_,_setFieldArray:A,_setDisabledField:Mn,_setErrors:T,_getFieldArray:D,_reset:Vn,_resetDefaultValues:()=>Me(t.defaultValues)&&t.defaultValues().then(h=>{Nn(h,t.resetOptions),d.state.next({isLoading:!1})}),_removeUnmounted:F,_disableForm:ys,_subjects:d,_proxyFormState:f,get _fields(){return n},get _formValues(){return s},get _state(){return a},set _state(h){a=h},get _defaultValues(){return i},get _names(){return c},set _names(h){c=h},get _formState(){return r},get _options(){return t},set _options(h){t={...t,...h}}},subscribe:Te,trigger:ee,register:Or,handleSubmit:Zn,watch:ie,setValue:O,getValues:J,reset:Nn,resetField:bs,clearErrors:Y,unregister:ke,setError:K,setFocus:js,getFieldState:te};return{...Un,formControl:Un}}var Ye=()=>{if(typeof crypto<"u"&&crypto.randomUUID)return crypto.randomUUID();const e=typeof performance>"u"?Date.now():performance.now()*1e3;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const r=(Math.random()*16+e)%16|0;return(t=="x"?r:r&3|8).toString(16)})},Vr=(e,t,r={})=>r.shouldFocus||fe(r.shouldFocus)?r.focusName||`${e}.${fe(r.focusIndex)?t:r.focusIndex}.`:"",Nr=(e,t)=>[...e,...Se(t)],Ur=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Lr(e,t,r){return[...e.slice(0,t),...Se(r),...e.slice(t)]}var Wr=(e,t,r)=>Array.isArray(e)?(fe(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Hr=(e,t)=>[...Se(t),...Se(e)];function uc(e,t){let r=0;const n=[...e];for(const i of t)n.splice(i-r,1),r++;return _r(n).length?n:[]}var qr=(e,t)=>fe(t)?[]:uc(e,Se(t).sort((r,n)=>r-n)),Kr=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},po=(e,t,r)=>(e[t]=r,e);function dc(e){const t=kr(),{control:r=t.control,name:n,keyName:i="id",shouldUnregister:s,rules:a}=e,[c,l]=G.useState(r._getFieldArray(n)),p=G.useRef(r._getFieldArray(n).map(Ye)),f=G.useRef(c),u=G.useRef(!1);f.current=c,r._names.array.add(n),G.useMemo(()=>a&&r.register(n,a),[r,a,n]),Cr(()=>r._subjects.array.subscribe({next:({values:w,name:C})=>{if(C===n||!C){const $=P(w,n);Array.isArray($)&&(l($),p.current=$.map(Ye))}}}).unsubscribe,[r,n]);const d=G.useCallback(w=>{u.current=!0,r._setFieldArray(n,w)},[r,n]),g=(w,C)=>{const $=Se(pe(w)),R=Nr(r._getFieldArray(n),$);r._names.focus=Vr(n,R.length-1,C),p.current=Nr(p.current,$.map(Ye)),d(R),l(R),r._setFieldArray(n,R,Nr,{argA:Ur(w)})},b=(w,C)=>{const $=Se(pe(w)),R=Hr(r._getFieldArray(n),$);r._names.focus=Vr(n,0,C),p.current=Hr(p.current,$.map(Ye)),d(R),l(R),r._setFieldArray(n,R,Hr,{argA:Ur(w)})},_=w=>{const C=qr(r._getFieldArray(n),w);p.current=qr(p.current,w),d(C),l(C),!Array.isArray(P(r._fields,n))&&ae(r._fields,n,void 0),r._setFieldArray(n,C,qr,{argA:w})},k=(w,C,$)=>{const R=Se(pe(C)),z=Lr(r._getFieldArray(n),w,R);r._names.focus=Vr(n,w,$),p.current=Lr(p.current,w,R.map(Ye)),d(z),l(z),r._setFieldArray(n,z,Lr,{argA:w,argB:Ur(C)})},A=(w,C)=>{const $=r._getFieldArray(n);Kr($,w,C),Kr(p.current,w,C),d($),l($),r._setFieldArray(n,$,Kr,{argA:w,argB:C},!1)},y=(w,C)=>{const $=r._getFieldArray(n);Wr($,w,C),Wr(p.current,w,C),d($),l($),r._setFieldArray(n,$,Wr,{argA:w,argB:C},!1)},T=(w,C)=>{const $=pe(C),R=po(r._getFieldArray(n),w,$);p.current=[...R].map((z,F)=>!z||F===w?Ye():p.current[F]),d(R),l([...R]),r._setFieldArray(n,R,po,{argA:w,argB:$},!0,!1)},j=w=>{const C=Se(pe(w));p.current=C.map(Ye),d([...C]),l([...C]),r._setFieldArray(n,[...C],$=>$,{},!0,!1)};return G.useEffect(()=>{if(r._state.action=!1,an(n,r._names)&&r._subjects.state.next({...r._formState}),u.current&&(!mt(r._options.mode).isOnSubmit||r._formState.isSubmitted)&&!mt(r._options.reValidateMode).isOnSubmit)if(r._options.resolver)r._runSchema([n]).then(w=>{const C=P(w.errors,n),$=P(r._formState.errors,n);($?!C&&$.type||C&&($.type!==C.type||$.message!==C.message):C&&C.type)&&(C?ae(r._formState.errors,n,C):me(r._formState.errors,n),r._subjects.state.next({errors:r._formState.errors}))});else{const w=P(r._fields,n);w&&w._f&&!(mt(r._options.reValidateMode).isOnSubmit&&mt(r._options.mode).isOnSubmit)&&cn(w,r._names.disabled,r._formValues,r._options.criteriaMode===Be.all,r._options.shouldUseNativeValidation,!0).then(C=>!Fe(C)&&r._subjects.state.next({errors:Ri(r._formState.errors,C,n)}))}r._subjects.state.next({name:n,values:pe(r._formValues)}),r._names.focus&&jt(r._fields,(w,C)=>{if(r._names.focus&&C.startsWith(r._names.focus)&&w.focus)return w.focus(),1}),r._names.focus="",r._setValid(),u.current=!1},[c,n,r]),G.useEffect(()=>(!P(r._formValues,n)&&r._setFieldArray(n),()=>{const w=(C,$)=>{const R=P(r._fields,C);R&&R._f&&(R._f.mount=$)};r._options.shouldUnregister||s?r.unregister(n):w(n,!1)}),[n,r,i,s]),{swap:G.useCallback(A,[d,n,r]),move:G.useCallback(y,[d,n,r]),prepend:G.useCallback(b,[d,n,r]),append:G.useCallback(g,[d,n,r]),remove:G.useCallback(_,[d,n,r]),insert:G.useCallback(k,[d,n,r]),update:G.useCallback(T,[d,n,r]),replace:G.useCallback(j,[d,n,r]),fields:G.useMemo(()=>c.map((w,C)=>({...w,[i]:p.current[C]||Ye()})),[c,i])}}function Fr(e={}){const t=G.useRef(void 0),r=G.useRef(void 0),[n,i]=G.useState({isDirty:!1,isValidating:!1,isLoading:Me(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Me(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:n},e.defaultValues&&!Me(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:a,...c}=lc(e);t.current={...c,formState:n}}const s=t.current.control;return s._options=e,Cr(()=>{const a=s._subscribe({formState:s._proxyFormState,callback:()=>i({...s._formState}),reRenderRoot:!0});return i(c=>({...c,isReady:!0})),s._formState.isReady=!0,a},[s]),G.useEffect(()=>s._disableForm(e.disabled),[s,e.disabled]),G.useEffect(()=>{e.mode&&(s._options.mode=e.mode),e.reValidateMode&&(s._options.reValidateMode=e.reValidateMode)},[s,e.mode,e.reValidateMode]),G.useEffect(()=>{e.errors&&(s._setErrors(e.errors),s._focusError())},[s,e.errors]),G.useEffect(()=>{e.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,e.shouldUnregister]),G.useEffect(()=>{if(s._proxyFormState.isDirty){const a=s._getDirty();a!==n.isDirty&&s._subjects.state.next({isDirty:a})}},[s,n.isDirty]),G.useEffect(()=>{e.values&&!He(e.values,r.current)?(s._reset(e.values,{keepFieldsRef:!0,...s._options.resetOptions}),r.current=e.values,i(a=>({...a}))):s._resetDefaultValues()},[s,e.values]),G.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),t.current.formState=Ci(n,s),t.current}function S(e,t,r){function n(c,l){var p;Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(p=c._zod).traits??(p.traits=new Set),c._zod.traits.add(e),t(c,l);for(const f in a.prototype)f in c||Object.defineProperty(c,f,{value:a.prototype[f].bind(c)});c._zod.constr=a,c._zod.def=l}const i=(r==null?void 0:r.Parent)??Object;class s extends i{}Object.defineProperty(s,"name",{value:e});function a(c){var l;const p=r!=null&&r.Parent?new s:this;n(p,c),(l=p._zod).deferred??(l.deferred=[]);for(const f of p._zod.deferred)f();return p}return Object.defineProperty(a,"init",{value:n}),Object.defineProperty(a,Symbol.hasInstance,{value:c=>{var l,p;return r!=null&&r.Parent&&c instanceof r.Parent?!0:(p=(l=c==null?void 0:c._zod)==null?void 0:l.traits)==null?void 0:p.has(e)}}),Object.defineProperty(a,"name",{value:e}),a}class wt extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class Oi extends Error{constructor(t){super(`Encountered unidirectional transform during encode: ${t}`),this.name="ZodEncodeError"}}const $i={};function pt(e){return $i}function pc(e){const t=Object.values(e).filter(n=>typeof n=="number");return Object.entries(e).filter(([n,i])=>t.indexOf(+n)===-1).map(([n,i])=>i)}function ln(e,t){return typeof t=="bigint"?t.toString():t}function Cn(e){return{get value(){{const t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function Fn(e){return e==null}function En(e){const t=e.startsWith("^")?1:0,r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function fc(e,t){const r=(e.toString().split(".")[1]||"").length,n=t.toString();let i=(n.split(".")[1]||"").length;if(i===0&&/\d?e-\d?/.test(n)){const l=n.match(/\d?e-(\d?)/);l!=null&&l[1]&&(i=Number.parseInt(l[1]))}const s=r>i?r:i,a=Number.parseInt(e.toFixed(s).replace(".","")),c=Number.parseInt(t.toFixed(s).replace(".",""));return a%c/10**s}const fo=Symbol("evaluating");function de(e,t,r){let n;Object.defineProperty(e,t,{get(){if(n!==fo)return n===void 0&&(n=fo,n=r()),n},set(i){Object.defineProperty(e,t,{value:i})},configurable:!0})}function hc(e){return Object.create(Object.getPrototypeOf(e),Object.getOwnPropertyDescriptors(e))}function at(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function Ct(...e){const t={};for(const r of e){const n=Object.getOwnPropertyDescriptors(r);Object.assign(t,n)}return Object.defineProperties({},t)}function ho(e){return JSON.stringify(e)}const Bi="captureStackTrace"in Error?Error.captureStackTrace:(...e)=>{};function pr(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}const gc=Cn(()=>{var e;if(typeof navigator<"u"&&((e=navigator==null?void 0:navigator.userAgent)!=null&&e.includes("Cloudflare")))return!1;try{const t=Function;return new t(""),!0}catch{return!1}});function $t(e){if(pr(e)===!1)return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(pr(r)===!1||Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")===!1)}function Pi(e){return $t(e)?{...e}:Array.isArray(e)?[...e]:e}const xc=new Set(["string","number","symbol"]);function Er(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ct(e,t,r){const n=new e._zod.constr(t??e._zod.def);return(!t||r!=null&&r.parent)&&(n._zod.parent=e),n}function q(e){const t=e;if(!t)return{};if(typeof t=="string")return{error:()=>t};if((t==null?void 0:t.message)!==void 0){if((t==null?void 0:t.error)!==void 0)throw new Error("Cannot specify both `message` and `error` params");t.error=t.message}return delete t.message,typeof t.error=="string"?{...t,error:()=>t.error}:t}function mc(e){return Object.keys(e).filter(t=>e[t]._zod.optin==="optional"&&e[t]._zod.optout==="optional")}const vc={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function yc(e,t){const r=e._zod.def,n=Ct(e._zod.def,{get shape(){const i={};for(const s in t){if(!(s in r.shape))throw new Error(`Unrecognized key: "${s}"`);t[s]&&(i[s]=r.shape[s])}return at(this,"shape",i),i},checks:[]});return ct(e,n)}function bc(e,t){const r=e._zod.def,n=Ct(e._zod.def,{get shape(){const i={...e._zod.def.shape};for(const s in t){if(!(s in r.shape))throw new Error(`Unrecognized key: "${s}"`);t[s]&&delete i[s]}return at(this,"shape",i),i},checks:[]});return ct(e,n)}function jc(e,t){if(!$t(t))throw new Error("Invalid input to extend: expected a plain object");const r=e._zod.def.checks;if(r&&r.length>0)throw new Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");const i=Ct(e._zod.def,{get shape(){const s={...e._zod.def.shape,...t};return at(this,"shape",s),s},checks:[]});return ct(e,i)}function wc(e,t){if(!$t(t))throw new Error("Invalid input to safeExtend: expected a plain object");const r={...e._zod.def,get shape(){const n={...e._zod.def.shape,...t};return at(this,"shape",n),n},checks:e._zod.def.checks};return ct(e,r)}function _c(e,t){const r=Ct(e._zod.def,{get shape(){const n={...e._zod.def.shape,...t._zod.def.shape};return at(this,"shape",n),n},get catchall(){return t._zod.def.catchall},checks:[]});return ct(e,r)}function kc(e,t,r){const n=Ct(t._zod.def,{get shape(){const i=t._zod.def.shape,s={...i};if(r)for(const a in r){if(!(a in i))throw new Error(`Unrecognized key: "${a}"`);r[a]&&(s[a]=e?new e({type:"optional",innerType:i[a]}):i[a])}else for(const a in i)s[a]=e?new e({type:"optional",innerType:i[a]}):i[a];return at(this,"shape",s),s},checks:[]});return ct(t,n)}function Cc(e,t,r){const n=Ct(t._zod.def,{get shape(){const i=t._zod.def.shape,s={...i};if(r)for(const a in r){if(!(a in s))throw new Error(`Unrecognized key: "${a}"`);r[a]&&(s[a]=new e({type:"nonoptional",innerType:i[a]}))}else for(const a in i)s[a]=new e({type:"nonoptional",innerType:i[a]});return at(this,"shape",s),s},checks:[]});return ct(t,n)}function vt(e,t=0){var r;if(e.aborted===!0)return!0;for(let n=t;n<e.issues.length;n++)if(((r=e.issues[n])==null?void 0:r.continue)!==!0)return!0;return!1}function Mi(e,t){return t.map(r=>{var n;return(n=r).path??(n.path=[]),r.path.unshift(e),r})}function Jt(e){return typeof e=="string"?e:e==null?void 0:e.message}function ft(e,t,r){var i,s,a,c,l,p;const n={...e,path:e.path??[]};if(!e.message){const f=Jt((a=(s=(i=e.inst)==null?void 0:i._zod.def)==null?void 0:s.error)==null?void 0:a.call(s,e))??Jt((c=t==null?void 0:t.error)==null?void 0:c.call(t,e))??Jt((l=r.customError)==null?void 0:l.call(r,e))??Jt((p=r.localeError)==null?void 0:p.call(r,e))??"Invalid input";n.message=f}return delete n.inst,delete n.continue,t!=null&&t.reportInput||delete n.input,n}function Sn(e){return Array.isArray(e)?"array":typeof e=="string"?"string":"unknown"}function Bt(...e){const[t,r,n]=e;return typeof t=="string"?{message:t,code:"custom",input:r,inst:n}:{...t}}const Zi=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),e.message=JSON.stringify(t,ln,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},In=S("$ZodError",Zi),Sr=S("$ZodError",Zi,{Parent:Error});function Fc(e,t=r=>r.message){const r={},n=[];for(const i of e.issues)i.path.length>0?(r[i.path[0]]=r[i.path[0]]||[],r[i.path[0]].push(t(i))):n.push(t(i));return{formErrors:n,fieldErrors:r}}function Ec(e,t){const r=t||function(s){return s.message},n={_errors:[]},i=s=>{for(const a of s.issues)if(a.code==="invalid_union"&&a.errors.length)a.errors.map(c=>i({issues:c}));else if(a.code==="invalid_key")i({issues:a.issues});else if(a.code==="invalid_element")i({issues:a.issues});else if(a.path.length===0)n._errors.push(r(a));else{let c=n,l=0;for(;l<a.path.length;){const p=a.path[l];l===a.path.length-1?(c[p]=c[p]||{_errors:[]},c[p]._errors.push(r(a))):c[p]=c[p]||{_errors:[]},c=c[p],l++}}};return i(e),n}const Ir=e=>(t,r,n,i)=>{const s=n?Object.assign(n,{async:!1}):{async:!1},a=t._zod.run({value:r,issues:[]},s);if(a instanceof Promise)throw new wt;if(a.issues.length){const c=new((i==null?void 0:i.Err)??e)(a.issues.map(l=>ft(l,s,pt())));throw Bi(c,i==null?void 0:i.callee),c}return a.value},Sc=Ir(Sr),zr=e=>async(t,r,n,i)=>{const s=n?Object.assign(n,{async:!0}):{async:!0};let a=t._zod.run({value:r,issues:[]},s);if(a instanceof Promise&&(a=await a),a.issues.length){const c=new((i==null?void 0:i.Err)??e)(a.issues.map(l=>ft(l,s,pt())));throw Bi(c,i==null?void 0:i.callee),c}return a.value},Ic=zr(Sr),Ar=e=>(t,r,n)=>{const i=n?{...n,async:!1}:{async:!1},s=t._zod.run({value:r,issues:[]},i);if(s instanceof Promise)throw new wt;return s.issues.length?{success:!1,error:new(e??In)(s.issues.map(a=>ft(a,i,pt())))}:{success:!0,data:s.value}},zc=Ar(Sr),Dr=e=>async(t,r,n)=>{const i=n?Object.assign(n,{async:!0}):{async:!0};let s=t._zod.run({value:r,issues:[]},i);return s instanceof Promise&&(s=await s),s.issues.length?{success:!1,error:new e(s.issues.map(a=>ft(a,i,pt())))}:{success:!0,data:s.value}},Ac=Dr(Sr),Dc=e=>(t,r,n)=>{const i=n?Object.assign(n,{direction:"backward"}):{direction:"backward"};return Ir(e)(t,r,i)},Tc=e=>(t,r,n)=>Ir(e)(t,r,n),Rc=e=>async(t,r,n)=>{const i=n?Object.assign(n,{direction:"backward"}):{direction:"backward"};return zr(e)(t,r,i)},Oc=e=>async(t,r,n)=>zr(e)(t,r,n),$c=e=>(t,r,n)=>{const i=n?Object.assign(n,{direction:"backward"}):{direction:"backward"};return Ar(e)(t,r,i)},Bc=e=>(t,r,n)=>Ar(e)(t,r,n),Pc=e=>async(t,r,n)=>{const i=n?Object.assign(n,{direction:"backward"}):{direction:"backward"};return Dr(e)(t,r,i)},Mc=e=>async(t,r,n)=>Dr(e)(t,r,n),Zc=/^[cC][^\s-]{8,}$/,Vc=/^[0-9a-z]+$/,Nc=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,Uc=/^[0-9a-vA-V]{20}$/,Lc=/^[A-Za-z0-9]{27}$/,Wc=/^[a-zA-Z0-9_-]{21}$/,Hc=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,qc=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,go=e=>e?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,Kc=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,Qc="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function Yc(){return new RegExp(Qc,"u")}const Gc=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Jc=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/,Xc=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,el=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,tl=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,Vi=/^[A-Za-z0-9_-]*$/,rl=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,nl=/^\+(?:[0-9]){6,14}[0-9]$/,Ni="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",ol=new RegExp(`^${Ni}$`);function Ui(e){const t="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof e.precision=="number"?e.precision===-1?`${t}`:e.precision===0?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}function il(e){return new RegExp(`^${Ui(e)}$`)}function sl(e){const t=Ui({precision:e.precision}),r=["Z"];e.local&&r.push(""),e.offset&&r.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");const n=`${t}(?:${r.join("|")})`;return new RegExp(`^${Ni}T(?:${n})$`)}const al=e=>{const t=e?`[\\s\\S]{${(e==null?void 0:e.minimum)??0},${(e==null?void 0:e.maximum)??""}}`:"[\\s\\S]*";return new RegExp(`^${t}$`)},cl=/^-?\d+$/,ll=/^-?\d+(?:\.\d+)?/,ul=/^[^A-Z]*$/,dl=/^[^a-z]*$/,De=S("$ZodCheck",(e,t)=>{var r;e._zod??(e._zod={}),e._zod.def=t,(r=e._zod).onattach??(r.onattach=[])}),Li={number:"number",bigint:"bigint",object:"date"},Wi=S("$ZodCheckLessThan",(e,t)=>{De.init(e,t);const r=Li[typeof t.value];e._zod.onattach.push(n=>{const i=n._zod.bag,s=(t.inclusive?i.maximum:i.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<s&&(t.inclusive?i.maximum=t.value:i.exclusiveMaximum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value<=t.value:n.value<t.value)||n.issues.push({origin:r,code:"too_big",maximum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),Hi=S("$ZodCheckGreaterThan",(e,t)=>{De.init(e,t);const r=Li[typeof t.value];e._zod.onattach.push(n=>{const i=n._zod.bag,s=(t.inclusive?i.minimum:i.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>s&&(t.inclusive?i.minimum=t.value:i.exclusiveMinimum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value>=t.value:n.value>t.value)||n.issues.push({origin:r,code:"too_small",minimum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),pl=S("$ZodCheckMultipleOf",(e,t)=>{De.init(e,t),e._zod.onattach.push(r=>{var n;(n=r._zod.bag).multipleOf??(n.multipleOf=t.value)}),e._zod.check=r=>{if(typeof r.value!=typeof t.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof r.value=="bigint"?r.value%t.value===BigInt(0):fc(r.value,t.value)===0)||r.issues.push({origin:typeof r.value,code:"not_multiple_of",divisor:t.value,input:r.value,inst:e,continue:!t.abort})}}),fl=S("$ZodCheckNumberFormat",(e,t)=>{var a;De.init(e,t),t.format=t.format||"float64";const r=(a=t.format)==null?void 0:a.includes("int"),n=r?"int":"number",[i,s]=vc[t.format];e._zod.onattach.push(c=>{const l=c._zod.bag;l.format=t.format,l.minimum=i,l.maximum=s,r&&(l.pattern=cl)}),e._zod.check=c=>{const l=c.value;if(r){if(!Number.isInteger(l)){c.issues.push({expected:n,format:t.format,code:"invalid_type",continue:!1,input:l,inst:e});return}if(!Number.isSafeInteger(l)){l>0?c.issues.push({input:l,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort}):c.issues.push({input:l,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort});return}}l<i&&c.issues.push({origin:"number",input:l,code:"too_small",minimum:i,inclusive:!0,inst:e,continue:!t.abort}),l>s&&c.issues.push({origin:"number",input:l,code:"too_big",maximum:s,inst:e})}}),hl=S("$ZodCheckMaxLength",(e,t)=>{var r;De.init(e,t),(r=e._zod.def).when??(r.when=n=>{const i=n.value;return!Fn(i)&&i.length!==void 0}),e._zod.onattach.push(n=>{const i=n._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<i&&(n._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{const i=n.value;if(i.length<=t.maximum)return;const a=Sn(i);n.issues.push({origin:a,code:"too_big",maximum:t.maximum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),gl=S("$ZodCheckMinLength",(e,t)=>{var r;De.init(e,t),(r=e._zod.def).when??(r.when=n=>{const i=n.value;return!Fn(i)&&i.length!==void 0}),e._zod.onattach.push(n=>{const i=n._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>i&&(n._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{const i=n.value;if(i.length>=t.minimum)return;const a=Sn(i);n.issues.push({origin:a,code:"too_small",minimum:t.minimum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),xl=S("$ZodCheckLengthEquals",(e,t)=>{var r;De.init(e,t),(r=e._zod.def).when??(r.when=n=>{const i=n.value;return!Fn(i)&&i.length!==void 0}),e._zod.onattach.push(n=>{const i=n._zod.bag;i.minimum=t.length,i.maximum=t.length,i.length=t.length}),e._zod.check=n=>{const i=n.value,s=i.length;if(s===t.length)return;const a=Sn(i),c=s>t.length;n.issues.push({origin:a,...c?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),Tr=S("$ZodCheckStringFormat",(e,t)=>{var r,n;De.init(e,t),e._zod.onattach.push(i=>{const s=i._zod.bag;s.format=t.format,t.pattern&&(s.patterns??(s.patterns=new Set),s.patterns.add(t.pattern))}),t.pattern?(r=e._zod).check??(r.check=i=>{t.pattern.lastIndex=0,!t.pattern.test(i.value)&&i.issues.push({origin:"string",code:"invalid_format",format:t.format,input:i.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(n=e._zod).check??(n.check=()=>{})}),ml=S("$ZodCheckRegex",(e,t)=>{Tr.init(e,t),e._zod.check=r=>{t.pattern.lastIndex=0,!t.pattern.test(r.value)&&r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),vl=S("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=ul),Tr.init(e,t)}),yl=S("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=dl),Tr.init(e,t)}),bl=S("$ZodCheckIncludes",(e,t)=>{De.init(e,t);const r=Er(t.includes),n=new RegExp(typeof t.position=="number"?`^.{${t.position}}${r}`:r);t.pattern=n,e._zod.onattach.push(i=>{const s=i._zod.bag;s.patterns??(s.patterns=new Set),s.patterns.add(n)}),e._zod.check=i=>{i.value.includes(t.includes,t.position)||i.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:i.value,inst:e,continue:!t.abort})}}),jl=S("$ZodCheckStartsWith",(e,t)=>{De.init(e,t);const r=new RegExp(`^${Er(t.prefix)}.*`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const i=n._zod.bag;i.patterns??(i.patterns=new Set),i.patterns.add(r)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),wl=S("$ZodCheckEndsWith",(e,t)=>{De.init(e,t);const r=new RegExp(`.*${Er(t.suffix)}$`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const i=n._zod.bag;i.patterns??(i.patterns=new Set),i.patterns.add(r)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),_l=S("$ZodCheckOverwrite",(e,t)=>{De.init(e,t),e._zod.check=r=>{r.value=t.tx(r.value)}});class kl{constructor(t=[]){this.content=[],this.indent=0,this&&(this.args=t)}indented(t){this.indent+=1,t(this),this.indent-=1}write(t){if(typeof t=="function"){t(this,{execution:"sync"}),t(this,{execution:"async"});return}const n=t.split(`
`).filter(a=>a),i=Math.min(...n.map(a=>a.length-a.trimStart().length)),s=n.map(a=>a.slice(i)).map(a=>" ".repeat(this.indent*2)+a);for(const a of s)this.content.push(a)}compile(){const t=Function,r=this==null?void 0:this.args,i=[...((this==null?void 0:this.content)??[""]).map(s=>`  ${s}`)];return new t(...r,i.join(`
`))}}const Cl={major:4,minor:1,patch:8},ye=S("$ZodType",(e,t)=>{var i;var r;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=Cl;const n=[...e._zod.def.checks??[]];e._zod.traits.has("$ZodCheck")&&n.unshift(e);for(const s of n)for(const a of s._zod.onattach)a(e);if(n.length===0)(r=e._zod).deferred??(r.deferred=[]),(i=e._zod.deferred)==null||i.push(()=>{e._zod.run=e._zod.parse});else{const s=(c,l,p)=>{let f=vt(c),u;for(const d of l){if(d._zod.def.when){if(!d._zod.def.when(c))continue}else if(f)continue;const g=c.issues.length,b=d._zod.check(c);if(b instanceof Promise&&(p==null?void 0:p.async)===!1)throw new wt;if(u||b instanceof Promise)u=(u??Promise.resolve()).then(async()=>{await b,c.issues.length!==g&&(f||(f=vt(c,g)))});else{if(c.issues.length===g)continue;f||(f=vt(c,g))}}return u?u.then(()=>c):c},a=(c,l,p)=>{if(vt(c))return c.aborted=!0,c;const f=s(l,n,p);if(f instanceof Promise){if(p.async===!1)throw new wt;return f.then(u=>e._zod.parse(u,p))}return e._zod.parse(f,p)};e._zod.run=(c,l)=>{if(l.skipChecks)return e._zod.parse(c,l);if(l.direction==="backward"){const f=e._zod.parse({value:c.value,issues:[]},{...l,skipChecks:!0});return f instanceof Promise?f.then(u=>a(u,c,l)):a(f,c,l)}const p=e._zod.parse(c,l);if(p instanceof Promise){if(l.async===!1)throw new wt;return p.then(f=>s(f,n,l))}return s(p,n,l)}}e["~standard"]={validate:s=>{var a;try{const c=zc(e,s);return c.success?{value:c.data}:{issues:(a=c.error)==null?void 0:a.issues}}catch{return Ac(e,s).then(l=>{var p;return l.success?{value:l.data}:{issues:(p=l.error)==null?void 0:p.issues}})}},vendor:"zod",version:1}}),zn=S("$ZodString",(e,t)=>{var r;ye.init(e,t),e._zod.pattern=[...((r=e==null?void 0:e._zod.bag)==null?void 0:r.patterns)??[]].pop()??al(e._zod.bag),e._zod.parse=(n,i)=>{if(t.coerce)try{n.value=String(n.value)}catch{}return typeof n.value=="string"||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),ge=S("$ZodStringFormat",(e,t)=>{Tr.init(e,t),zn.init(e,t)}),Fl=S("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=qc),ge.init(e,t)}),El=S("$ZodUUID",(e,t)=>{if(t.version){const n={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(n===void 0)throw new Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=go(n))}else t.pattern??(t.pattern=go());ge.init(e,t)}),Sl=S("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=Kc),ge.init(e,t)}),Il=S("$ZodURL",(e,t)=>{ge.init(e,t),e._zod.check=r=>{try{const n=r.value.trim(),i=new URL(n);t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(i.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:rl.source,input:r.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(i.protocol.endsWith(":")?i.protocol.slice(0,-1):i.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:r.value,inst:e,continue:!t.abort})),t.normalize?r.value=i.href:r.value=n;return}catch{r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:e,continue:!t.abort})}}}),zl=S("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=Yc()),ge.init(e,t)}),Al=S("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=Wc),ge.init(e,t)}),Dl=S("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=Zc),ge.init(e,t)}),Tl=S("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=Vc),ge.init(e,t)}),Rl=S("$ZodULID",(e,t)=>{t.pattern??(t.pattern=Nc),ge.init(e,t)}),Ol=S("$ZodXID",(e,t)=>{t.pattern??(t.pattern=Uc),ge.init(e,t)}),$l=S("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=Lc),ge.init(e,t)}),Bl=S("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=sl(t)),ge.init(e,t)}),Pl=S("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=ol),ge.init(e,t)}),Ml=S("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=il(t)),ge.init(e,t)}),Zl=S("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=Hc),ge.init(e,t)}),Vl=S("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=Gc),ge.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv4"})}),Nl=S("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=Jc),ge.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv6"}),e._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:e,continue:!t.abort})}}}),Ul=S("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=Xc),ge.init(e,t)}),Ll=S("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=el),ge.init(e,t),e._zod.check=r=>{const n=r.value.split("/");try{if(n.length!==2)throw new Error;const[i,s]=n;if(!s)throw new Error;const a=Number(s);if(`${a}`!==s)throw new Error;if(a<0||a>128)throw new Error;new URL(`http://[${i}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:e,continue:!t.abort})}}});function qi(e){if(e==="")return!0;if(e.length%4!==0)return!1;try{return atob(e),!0}catch{return!1}}const Wl=S("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=tl),ge.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64"}),e._zod.check=r=>{qi(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:e,continue:!t.abort})}});function Hl(e){if(!Vi.test(e))return!1;const t=e.replace(/[-_]/g,n=>n==="-"?"+":"/"),r=t.padEnd(Math.ceil(t.length/4)*4,"=");return qi(r)}const ql=S("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=Vi),ge.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64url"}),e._zod.check=r=>{Hl(r.value)||r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:e,continue:!t.abort})}}),Kl=S("$ZodE164",(e,t)=>{t.pattern??(t.pattern=nl),ge.init(e,t)});function Ql(e,t=null){try{const r=e.split(".");if(r.length!==3)return!1;const[n]=r;if(!n)return!1;const i=JSON.parse(atob(n));return!("typ"in i&&(i==null?void 0:i.typ)!=="JWT"||!i.alg||t&&(!("alg"in i)||i.alg!==t))}catch{return!1}}const Yl=S("$ZodJWT",(e,t)=>{ge.init(e,t),e._zod.check=r=>{Ql(r.value,t.alg)||r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:e,continue:!t.abort})}}),Ki=S("$ZodNumber",(e,t)=>{ye.init(e,t),e._zod.pattern=e._zod.bag.pattern??ll,e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=Number(r.value)}catch{}const i=r.value;if(typeof i=="number"&&!Number.isNaN(i)&&Number.isFinite(i))return r;const s=typeof i=="number"?Number.isNaN(i)?"NaN":Number.isFinite(i)?void 0:"Infinity":void 0;return r.issues.push({expected:"number",code:"invalid_type",input:i,inst:e,...s?{received:s}:{}}),r}}),Gl=S("$ZodNumber",(e,t)=>{fl.init(e,t),Ki.init(e,t)}),Jl=S("$ZodAny",(e,t)=>{ye.init(e,t),e._zod.parse=r=>r}),Xl=S("$ZodUnknown",(e,t)=>{ye.init(e,t),e._zod.parse=r=>r}),eu=S("$ZodNever",(e,t)=>{ye.init(e,t),e._zod.parse=(r,n)=>(r.issues.push({expected:"never",code:"invalid_type",input:r.value,inst:e}),r)});function xo(e,t,r){e.issues.length&&t.issues.push(...Mi(r,e.issues)),t.value[r]=e.value}const tu=S("$ZodArray",(e,t)=>{ye.init(e,t),e._zod.parse=(r,n)=>{const i=r.value;if(!Array.isArray(i))return r.issues.push({expected:"array",code:"invalid_type",input:i,inst:e}),r;r.value=Array(i.length);const s=[];for(let a=0;a<i.length;a++){const c=i[a],l=t.element._zod.run({value:c,issues:[]},n);l instanceof Promise?s.push(l.then(p=>xo(p,r,a))):xo(l,r,a)}return s.length?Promise.all(s).then(()=>r):r}});function fr(e,t,r,n){e.issues.length&&t.issues.push(...Mi(r,e.issues)),e.value===void 0?r in n&&(t.value[r]=void 0):t.value[r]=e.value}function Qi(e){var n,i,s,a;const t=Object.keys(e.shape);for(const c of t)if(!((a=(s=(i=(n=e.shape)==null?void 0:n[c])==null?void 0:i._zod)==null?void 0:s.traits)!=null&&a.has("$ZodType")))throw new Error(`Invalid element at key "${c}": expected a Zod schema`);const r=mc(e.shape);return{...e,keys:t,keySet:new Set(t),numKeys:t.length,optionalKeys:new Set(r)}}function Yi(e,t,r,n,i,s){const a=[],c=i.keySet,l=i.catchall._zod,p=l.def.type;for(const f of Object.keys(t)){if(c.has(f))continue;if(p==="never"){a.push(f);continue}const u=l.run({value:t[f],issues:[]},n);u instanceof Promise?e.push(u.then(d=>fr(d,r,f,t))):fr(u,r,f,t)}return a.length&&r.issues.push({code:"unrecognized_keys",keys:a,input:t,inst:s}),e.length?Promise.all(e).then(()=>r):r}const ru=S("$ZodObject",(e,t)=>{ye.init(e,t);const r=Cn(()=>Qi(t));de(e._zod,"propValues",()=>{const a=t.shape,c={};for(const l in a){const p=a[l]._zod;if(p.values){c[l]??(c[l]=new Set);for(const f of p.values)c[l].add(f)}}return c});const n=pr,i=t.catchall;let s;e._zod.parse=(a,c)=>{s??(s=r.value);const l=a.value;if(!n(l))return a.issues.push({expected:"object",code:"invalid_type",input:l,inst:e}),a;a.value={};const p=[],f=s.shape;for(const u of s.keys){const g=f[u]._zod.run({value:l[u],issues:[]},c);g instanceof Promise?p.push(g.then(b=>fr(b,a,u,l))):fr(g,a,u,l)}return i?Yi(p,l,a,c,r.value,e):p.length?Promise.all(p).then(()=>a):a}}),nu=S("$ZodObjectJIT",(e,t)=>{ru.init(e,t);const r=e._zod.parse,n=Cn(()=>Qi(t)),i=d=>{const g=new kl(["shape","payload","ctx"]),b=n.value,_=T=>{const j=ho(T);return`shape[${j}]._zod.run({ value: input[${j}], issues: [] }, ctx)`};g.write("const input = payload.value;");const k=Object.create(null);let A=0;for(const T of b.keys)k[T]=`key_${A++}`;g.write("const newResult = {};");for(const T of b.keys){const j=k[T],w=ho(T);g.write(`const ${j} = ${_(T)};`),g.write(`
        if (${j}.issues.length) {
          payload.issues = payload.issues.concat(${j}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${w}, ...iss.path] : [${w}]
          })));
        }
        
        
        if (${j}.value === undefined) {
          if (${w} in input) {
            newResult[${w}] = undefined;
          }
        } else {
          newResult[${w}] = ${j}.value;
        }
        
      `)}g.write("payload.value = newResult;"),g.write("return payload;");const y=g.compile();return(T,j)=>y(d,T,j)};let s;const a=pr,c=!$i.jitless,p=c&&gc.value,f=t.catchall;let u;e._zod.parse=(d,g)=>{u??(u=n.value);const b=d.value;return a(b)?c&&p&&(g==null?void 0:g.async)===!1&&g.jitless!==!0?(s||(s=i(t.shape)),d=s(d,g),f?Yi([],b,d,g,u,e):d):r(d,g):(d.issues.push({expected:"object",code:"invalid_type",input:b,inst:e}),d)}});function mo(e,t,r,n){for(const s of e)if(s.issues.length===0)return t.value=s.value,t;const i=e.filter(s=>!vt(s));return i.length===1?(t.value=i[0].value,i[0]):(t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(s=>s.issues.map(a=>ft(a,n,pt())))}),t)}const ou=S("$ZodUnion",(e,t)=>{ye.init(e,t),de(e._zod,"optin",()=>t.options.some(i=>i._zod.optin==="optional")?"optional":void 0),de(e._zod,"optout",()=>t.options.some(i=>i._zod.optout==="optional")?"optional":void 0),de(e._zod,"values",()=>{if(t.options.every(i=>i._zod.values))return new Set(t.options.flatMap(i=>Array.from(i._zod.values)))}),de(e._zod,"pattern",()=>{if(t.options.every(i=>i._zod.pattern)){const i=t.options.map(s=>s._zod.pattern);return new RegExp(`^(${i.map(s=>En(s.source)).join("|")})$`)}});const r=t.options.length===1,n=t.options[0]._zod.run;e._zod.parse=(i,s)=>{if(r)return n(i,s);let a=!1;const c=[];for(const l of t.options){const p=l._zod.run({value:i.value,issues:[]},s);if(p instanceof Promise)c.push(p),a=!0;else{if(p.issues.length===0)return p;c.push(p)}}return a?Promise.all(c).then(l=>mo(l,i,e,s)):mo(c,i,e,s)}}),iu=S("$ZodIntersection",(e,t)=>{ye.init(e,t),e._zod.parse=(r,n)=>{const i=r.value,s=t.left._zod.run({value:i,issues:[]},n),a=t.right._zod.run({value:i,issues:[]},n);return s instanceof Promise||a instanceof Promise?Promise.all([s,a]).then(([l,p])=>vo(r,l,p)):vo(r,s,a)}});function un(e,t){if(e===t)return{valid:!0,data:e};if(e instanceof Date&&t instanceof Date&&+e==+t)return{valid:!0,data:e};if($t(e)&&$t(t)){const r=Object.keys(t),n=Object.keys(e).filter(s=>r.indexOf(s)!==-1),i={...e,...t};for(const s of n){const a=un(e[s],t[s]);if(!a.valid)return{valid:!1,mergeErrorPath:[s,...a.mergeErrorPath]};i[s]=a.data}return{valid:!0,data:i}}if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return{valid:!1,mergeErrorPath:[]};const r=[];for(let n=0;n<e.length;n++){const i=e[n],s=t[n],a=un(i,s);if(!a.valid)return{valid:!1,mergeErrorPath:[n,...a.mergeErrorPath]};r.push(a.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}function vo(e,t,r){if(t.issues.length&&e.issues.push(...t.issues),r.issues.length&&e.issues.push(...r.issues),vt(e))return e;const n=un(t.value,r.value);if(!n.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(n.mergeErrorPath)}`);return e.value=n.data,e}const su=S("$ZodEnum",(e,t)=>{ye.init(e,t);const r=pc(t.entries),n=new Set(r);e._zod.values=n,e._zod.pattern=new RegExp(`^(${r.filter(i=>xc.has(typeof i)).map(i=>typeof i=="string"?Er(i):i.toString()).join("|")})$`),e._zod.parse=(i,s)=>{const a=i.value;return n.has(a)||i.issues.push({code:"invalid_value",values:r,input:a,inst:e}),i}}),au=S("$ZodTransform",(e,t)=>{ye.init(e,t),e._zod.parse=(r,n)=>{if(n.direction==="backward")throw new Oi(e.constructor.name);const i=t.transform(r.value,r);if(n.async)return(i instanceof Promise?i:Promise.resolve(i)).then(a=>(r.value=a,r));if(i instanceof Promise)throw new wt;return r.value=i,r}});function yo(e,t){return e.issues.length&&t===void 0?{issues:[],value:void 0}:e}const cu=S("$ZodOptional",(e,t)=>{ye.init(e,t),e._zod.optin="optional",e._zod.optout="optional",de(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),de(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${En(r.source)})?$`):void 0}),e._zod.parse=(r,n)=>{if(t.innerType._zod.optin==="optional"){const i=t.innerType._zod.run(r,n);return i instanceof Promise?i.then(s=>yo(s,r.value)):yo(i,r.value)}return r.value===void 0?r:t.innerType._zod.run(r,n)}}),lu=S("$ZodNullable",(e,t)=>{ye.init(e,t),de(e._zod,"optin",()=>t.innerType._zod.optin),de(e._zod,"optout",()=>t.innerType._zod.optout),de(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${En(r.source)}|null)$`):void 0}),de(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(r,n)=>r.value===null?r:t.innerType._zod.run(r,n)}),uu=S("$ZodDefault",(e,t)=>{ye.init(e,t),e._zod.optin="optional",de(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{if(n.direction==="backward")return t.innerType._zod.run(r,n);if(r.value===void 0)return r.value=t.defaultValue,r;const i=t.innerType._zod.run(r,n);return i instanceof Promise?i.then(s=>bo(s,t)):bo(i,t)}});function bo(e,t){return e.value===void 0&&(e.value=t.defaultValue),e}const du=S("$ZodPrefault",(e,t)=>{ye.init(e,t),e._zod.optin="optional",de(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>(n.direction==="backward"||r.value===void 0&&(r.value=t.defaultValue),t.innerType._zod.run(r,n))}),pu=S("$ZodNonOptional",(e,t)=>{ye.init(e,t),de(e._zod,"values",()=>{const r=t.innerType._zod.values;return r?new Set([...r].filter(n=>n!==void 0)):void 0}),e._zod.parse=(r,n)=>{const i=t.innerType._zod.run(r,n);return i instanceof Promise?i.then(s=>jo(s,e)):jo(i,e)}});function jo(e,t){return!e.issues.length&&e.value===void 0&&e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}const fu=S("$ZodCatch",(e,t)=>{ye.init(e,t),de(e._zod,"optin",()=>t.innerType._zod.optin),de(e._zod,"optout",()=>t.innerType._zod.optout),de(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{if(n.direction==="backward")return t.innerType._zod.run(r,n);const i=t.innerType._zod.run(r,n);return i instanceof Promise?i.then(s=>(r.value=s.value,s.issues.length&&(r.value=t.catchValue({...r,error:{issues:s.issues.map(a=>ft(a,n,pt()))},input:r.value}),r.issues=[]),r)):(r.value=i.value,i.issues.length&&(r.value=t.catchValue({...r,error:{issues:i.issues.map(s=>ft(s,n,pt()))},input:r.value}),r.issues=[]),r)}}),hu=S("$ZodPipe",(e,t)=>{ye.init(e,t),de(e._zod,"values",()=>t.in._zod.values),de(e._zod,"optin",()=>t.in._zod.optin),de(e._zod,"optout",()=>t.out._zod.optout),de(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(r,n)=>{if(n.direction==="backward"){const s=t.out._zod.run(r,n);return s instanceof Promise?s.then(a=>Xt(a,t.in,n)):Xt(s,t.in,n)}const i=t.in._zod.run(r,n);return i instanceof Promise?i.then(s=>Xt(s,t.out,n)):Xt(i,t.out,n)}});function Xt(e,t,r){return e.issues.length?(e.aborted=!0,e):t._zod.run({value:e.value,issues:e.issues},r)}const gu=S("$ZodReadonly",(e,t)=>{ye.init(e,t),de(e._zod,"propValues",()=>t.innerType._zod.propValues),de(e._zod,"values",()=>t.innerType._zod.values),de(e._zod,"optin",()=>t.innerType._zod.optin),de(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(r,n)=>{if(n.direction==="backward")return t.innerType._zod.run(r,n);const i=t.innerType._zod.run(r,n);return i instanceof Promise?i.then(wo):wo(i)}});function wo(e){return e.value=Object.freeze(e.value),e}const xu=S("$ZodCustom",(e,t)=>{De.init(e,t),ye.init(e,t),e._zod.parse=(r,n)=>r,e._zod.check=r=>{const n=r.value,i=t.fn(n);if(i instanceof Promise)return i.then(s=>_o(s,r,n,e));_o(i,r,n,e)}});function _o(e,t,r,n){if(!e){const i={code:"custom",input:r,inst:n,path:[...n._zod.def.path??[]],continue:!n._zod.def.abort};n._zod.def.params&&(i.params=n._zod.def.params),t.issues.push(Bt(i))}}class mu{constructor(){this._map=new WeakMap,this._idmap=new Map}add(t,...r){const n=r[0];if(this._map.set(t,n),n&&typeof n=="object"&&"id"in n){if(this._idmap.has(n.id))throw new Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,t)}return this}clear(){return this._map=new WeakMap,this._idmap=new Map,this}remove(t){const r=this._map.get(t);return r&&typeof r=="object"&&"id"in r&&this._idmap.delete(r.id),this._map.delete(t),this}get(t){const r=t._zod.parent;if(r){const n={...this.get(r)??{}};delete n.id;const i={...n,...this._map.get(t)};return Object.keys(i).length?i:void 0}return this._map.get(t)}has(t){return this._map.has(t)}}function vu(){return new mu}const er=vu();function yu(e,t){return new e({type:"string",...q(t)})}function bu(e,t){return new e({type:"string",coerce:!0,...q(t)})}function ju(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,...q(t)})}function ko(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...q(t)})}function wu(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...q(t)})}function _u(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...q(t)})}function ku(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...q(t)})}function Cu(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...q(t)})}function Fu(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,...q(t)})}function Eu(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...q(t)})}function Su(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...q(t)})}function Iu(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...q(t)})}function zu(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...q(t)})}function Au(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...q(t)})}function Du(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...q(t)})}function Tu(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...q(t)})}function Ru(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...q(t)})}function Ou(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...q(t)})}function $u(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...q(t)})}function Bu(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...q(t)})}function Pu(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...q(t)})}function Mu(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...q(t)})}function Zu(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...q(t)})}function Vu(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...q(t)})}function Nu(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...q(t)})}function Uu(e,t){return new e({type:"string",format:"date",check:"string_format",...q(t)})}function Lu(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,...q(t)})}function Wu(e,t){return new e({type:"string",format:"duration",check:"string_format",...q(t)})}function Hu(e,t){return new e({type:"number",checks:[],...q(t)})}function qu(e,t){return new e({type:"number",coerce:!0,checks:[],...q(t)})}function Ku(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...q(t)})}function Qu(e){return new e({type:"any"})}function Yu(e){return new e({type:"unknown"})}function Gu(e,t){return new e({type:"never",...q(t)})}function Co(e,t){return new Wi({check:"less_than",...q(t),value:e,inclusive:!1})}function Qr(e,t){return new Wi({check:"less_than",...q(t),value:e,inclusive:!0})}function Fo(e,t){return new Hi({check:"greater_than",...q(t),value:e,inclusive:!1})}function Yr(e,t){return new Hi({check:"greater_than",...q(t),value:e,inclusive:!0})}function Eo(e,t){return new pl({check:"multiple_of",...q(t),value:e})}function Gi(e,t){return new hl({check:"max_length",...q(t),maximum:e})}function hr(e,t){return new gl({check:"min_length",...q(t),minimum:e})}function Ji(e,t){return new xl({check:"length_equals",...q(t),length:e})}function Ju(e,t){return new ml({check:"string_format",format:"regex",...q(t),pattern:e})}function Xu(e){return new vl({check:"string_format",format:"lowercase",...q(e)})}function ed(e){return new yl({check:"string_format",format:"uppercase",...q(e)})}function td(e,t){return new bl({check:"string_format",format:"includes",...q(t),includes:e})}function rd(e,t){return new jl({check:"string_format",format:"starts_with",...q(t),prefix:e})}function nd(e,t){return new wl({check:"string_format",format:"ends_with",...q(t),suffix:e})}function Qt(e){return new _l({check:"overwrite",tx:e})}function od(e){return Qt(t=>t.normalize(e))}function id(){return Qt(e=>e.trim())}function sd(){return Qt(e=>e.toLowerCase())}function ad(){return Qt(e=>e.toUpperCase())}function cd(e,t,r){return new e({type:"array",element:t,...q(r)})}function ld(e,t,r){return new e({type:"custom",check:"custom",fn:t,...q(r)})}function ud(e){const t=dd(r=>(r.addIssue=n=>{if(typeof n=="string")r.issues.push(Bt(n,r.value,t._zod.def));else{const i=n;i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=r.value),i.inst??(i.inst=t),i.continue??(i.continue=!t._zod.def.abort),r.issues.push(Bt(i))}},e(r.value,r)));return t}function dd(e,t){const r=new De({check:"custom",...q(t)});return r._zod.check=e,r}const pd=S("ZodISODateTime",(e,t)=>{Bl.init(e,t),xe.init(e,t)});function fd(e){return Nu(pd,e)}const hd=S("ZodISODate",(e,t)=>{Pl.init(e,t),xe.init(e,t)});function gd(e){return Uu(hd,e)}const xd=S("ZodISOTime",(e,t)=>{Ml.init(e,t),xe.init(e,t)});function md(e){return Lu(xd,e)}const vd=S("ZodISODuration",(e,t)=>{Zl.init(e,t),xe.init(e,t)});function yd(e){return Wu(vd,e)}const bd=(e,t)=>{In.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:r=>Ec(e,r)},flatten:{value:r=>Fc(e,r)},addIssue:{value:r=>{e.issues.push(r),e.message=JSON.stringify(e.issues,ln,2)}},addIssues:{value:r=>{e.issues.push(...r),e.message=JSON.stringify(e.issues,ln,2)}},isEmpty:{get(){return e.issues.length===0}}})},Pe=S("ZodError",bd,{Parent:Error}),jd=Ir(Pe),wd=zr(Pe),_d=Ar(Pe),kd=Dr(Pe),Cd=Dc(Pe),Fd=Tc(Pe),Ed=Rc(Pe),Sd=Oc(Pe),Id=$c(Pe),zd=Bc(Pe),Ad=Pc(Pe),Dd=Mc(Pe),be=S("ZodType",(e,t)=>(ye.init(e,t),e.def=t,e.type=t.type,Object.defineProperty(e,"_def",{value:t}),e.check=(...r)=>e.clone({...t,checks:[...t.checks??[],...r.map(n=>typeof n=="function"?{_zod:{check:n,def:{check:"custom"},onattach:[]}}:n)]}),e.clone=(r,n)=>ct(e,r,n),e.brand=()=>e,e.register=((r,n)=>(r.add(e,n),e)),e.parse=(r,n)=>jd(e,r,n,{callee:e.parse}),e.safeParse=(r,n)=>_d(e,r,n),e.parseAsync=async(r,n)=>wd(e,r,n,{callee:e.parseAsync}),e.safeParseAsync=async(r,n)=>kd(e,r,n),e.spa=e.safeParseAsync,e.encode=(r,n)=>Cd(e,r,n),e.decode=(r,n)=>Fd(e,r,n),e.encodeAsync=async(r,n)=>Ed(e,r,n),e.decodeAsync=async(r,n)=>Sd(e,r,n),e.safeEncode=(r,n)=>Id(e,r,n),e.safeDecode=(r,n)=>zd(e,r,n),e.safeEncodeAsync=async(r,n)=>Ad(e,r,n),e.safeDecodeAsync=async(r,n)=>Dd(e,r,n),e.refine=(r,n)=>e.check(wp(r,n)),e.superRefine=r=>e.check(_p(r)),e.overwrite=r=>e.check(Qt(r)),e.optional=()=>Ao(e),e.nullable=()=>Do(e),e.nullish=()=>Ao(Do(e)),e.nonoptional=r=>gp(e,r),e.array=()=>gr(e),e.or=r=>sr([e,r]),e.and=r=>sp(e,r),e.transform=r=>To(e,lp(r)),e.default=r=>pp(e,r),e.prefault=r=>hp(e,r),e.catch=r=>mp(e,r),e.pipe=r=>To(e,r),e.readonly=()=>bp(e),e.describe=r=>{const n=e.clone();return er.add(n,{description:r}),n},Object.defineProperty(e,"description",{get(){var r;return(r=er.get(e))==null?void 0:r.description},configurable:!0}),e.meta=(...r)=>{if(r.length===0)return er.get(e);const n=e.clone();return er.add(n,r[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),Xi=S("_ZodString",(e,t)=>{zn.init(e,t),be.init(e,t);const r=e._zod.bag;e.format=r.format??null,e.minLength=r.minimum??null,e.maxLength=r.maximum??null,e.regex=(...n)=>e.check(Ju(...n)),e.includes=(...n)=>e.check(td(...n)),e.startsWith=(...n)=>e.check(rd(...n)),e.endsWith=(...n)=>e.check(nd(...n)),e.min=(...n)=>e.check(hr(...n)),e.max=(...n)=>e.check(Gi(...n)),e.length=(...n)=>e.check(Ji(...n)),e.nonempty=(...n)=>e.check(hr(1,...n)),e.lowercase=n=>e.check(Xu(n)),e.uppercase=n=>e.check(ed(n)),e.trim=()=>e.check(id()),e.normalize=(...n)=>e.check(od(...n)),e.toLowerCase=()=>e.check(sd()),e.toUpperCase=()=>e.check(ad())}),es=S("ZodString",(e,t)=>{zn.init(e,t),Xi.init(e,t),e.email=r=>e.check(ju(Td,r)),e.url=r=>e.check(Fu(Rd,r)),e.jwt=r=>e.check(Vu(Qd,r)),e.emoji=r=>e.check(Eu(Od,r)),e.guid=r=>e.check(ko(So,r)),e.uuid=r=>e.check(wu(tr,r)),e.uuidv4=r=>e.check(_u(tr,r)),e.uuidv6=r=>e.check(ku(tr,r)),e.uuidv7=r=>e.check(Cu(tr,r)),e.nanoid=r=>e.check(Su($d,r)),e.guid=r=>e.check(ko(So,r)),e.cuid=r=>e.check(Iu(Bd,r)),e.cuid2=r=>e.check(zu(Pd,r)),e.ulid=r=>e.check(Au(Md,r)),e.base64=r=>e.check(Pu(Hd,r)),e.base64url=r=>e.check(Mu(qd,r)),e.xid=r=>e.check(Du(Zd,r)),e.ksuid=r=>e.check(Tu(Vd,r)),e.ipv4=r=>e.check(Ru(Nd,r)),e.ipv6=r=>e.check(Ou(Ud,r)),e.cidrv4=r=>e.check($u(Ld,r)),e.cidrv6=r=>e.check(Bu(Wd,r)),e.e164=r=>e.check(Zu(Kd,r)),e.datetime=r=>e.check(fd(r)),e.date=r=>e.check(gd(r)),e.time=r=>e.check(md(r)),e.duration=r=>e.check(yd(r))});function Ve(e){return yu(es,e)}const xe=S("ZodStringFormat",(e,t)=>{ge.init(e,t),Xi.init(e,t)}),Td=S("ZodEmail",(e,t)=>{Sl.init(e,t),xe.init(e,t)}),So=S("ZodGUID",(e,t)=>{Fl.init(e,t),xe.init(e,t)}),tr=S("ZodUUID",(e,t)=>{El.init(e,t),xe.init(e,t)}),Rd=S("ZodURL",(e,t)=>{Il.init(e,t),xe.init(e,t)}),Od=S("ZodEmoji",(e,t)=>{zl.init(e,t),xe.init(e,t)}),$d=S("ZodNanoID",(e,t)=>{Al.init(e,t),xe.init(e,t)}),Bd=S("ZodCUID",(e,t)=>{Dl.init(e,t),xe.init(e,t)}),Pd=S("ZodCUID2",(e,t)=>{Tl.init(e,t),xe.init(e,t)}),Md=S("ZodULID",(e,t)=>{Rl.init(e,t),xe.init(e,t)}),Zd=S("ZodXID",(e,t)=>{Ol.init(e,t),xe.init(e,t)}),Vd=S("ZodKSUID",(e,t)=>{$l.init(e,t),xe.init(e,t)}),Nd=S("ZodIPv4",(e,t)=>{Vl.init(e,t),xe.init(e,t)}),Ud=S("ZodIPv6",(e,t)=>{Nl.init(e,t),xe.init(e,t)}),Ld=S("ZodCIDRv4",(e,t)=>{Ul.init(e,t),xe.init(e,t)}),Wd=S("ZodCIDRv6",(e,t)=>{Ll.init(e,t),xe.init(e,t)}),Hd=S("ZodBase64",(e,t)=>{Wl.init(e,t),xe.init(e,t)}),qd=S("ZodBase64URL",(e,t)=>{ql.init(e,t),xe.init(e,t)}),Kd=S("ZodE164",(e,t)=>{Kl.init(e,t),xe.init(e,t)}),Qd=S("ZodJWT",(e,t)=>{Yl.init(e,t),xe.init(e,t)}),An=S("ZodNumber",(e,t)=>{Ki.init(e,t),be.init(e,t),e.gt=(n,i)=>e.check(Fo(n,i)),e.gte=(n,i)=>e.check(Yr(n,i)),e.min=(n,i)=>e.check(Yr(n,i)),e.lt=(n,i)=>e.check(Co(n,i)),e.lte=(n,i)=>e.check(Qr(n,i)),e.max=(n,i)=>e.check(Qr(n,i)),e.int=n=>e.check(Io(n)),e.safe=n=>e.check(Io(n)),e.positive=n=>e.check(Fo(0,n)),e.nonnegative=n=>e.check(Yr(0,n)),e.negative=n=>e.check(Co(0,n)),e.nonpositive=n=>e.check(Qr(0,n)),e.multipleOf=(n,i)=>e.check(Eo(n,i)),e.step=(n,i)=>e.check(Eo(n,i)),e.finite=()=>e;const r=e._zod.bag;e.minValue=Math.max(r.minimum??Number.NEGATIVE_INFINITY,r.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(r.maximum??Number.POSITIVE_INFINITY,r.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(r.format??"").includes("int")||Number.isSafeInteger(r.multipleOf??.5),e.isFinite=!0,e.format=r.format??null});function ir(e){return Hu(An,e)}const Yd=S("ZodNumberFormat",(e,t)=>{Gl.init(e,t),An.init(e,t)});function Io(e){return Ku(Yd,e)}const Gd=S("ZodAny",(e,t)=>{Jl.init(e,t),be.init(e,t)});function Jd(){return Qu(Gd)}const Xd=S("ZodUnknown",(e,t)=>{Xl.init(e,t),be.init(e,t)});function zo(){return Yu(Xd)}const ep=S("ZodNever",(e,t)=>{eu.init(e,t),be.init(e,t)});function tp(e){return Gu(ep,e)}const rp=S("ZodArray",(e,t)=>{tu.init(e,t),be.init(e,t),e.element=t.element,e.min=(r,n)=>e.check(hr(r,n)),e.nonempty=r=>e.check(hr(1,r)),e.max=(r,n)=>e.check(Gi(r,n)),e.length=(r,n)=>e.check(Ji(r,n)),e.unwrap=()=>e.element});function gr(e,t){return cd(rp,e,t)}const np=S("ZodObject",(e,t)=>{nu.init(e,t),be.init(e,t),de(e,"shape",()=>t.shape),e.keyof=()=>ap(Object.keys(e._zod.def.shape)),e.catchall=r=>e.clone({...e._zod.def,catchall:r}),e.passthrough=()=>e.clone({...e._zod.def,catchall:zo()}),e.loose=()=>e.clone({...e._zod.def,catchall:zo()}),e.strict=()=>e.clone({...e._zod.def,catchall:tp()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=r=>jc(e,r),e.safeExtend=r=>wc(e,r),e.merge=r=>_c(e,r),e.pick=r=>yc(e,r),e.omit=r=>bc(e,r),e.partial=(...r)=>kc(ts,e,r[0]),e.required=(...r)=>Cc(rs,e,r[0])});function Pt(e,t){const r={type:"object",get shape(){return at(this,"shape",e?hc(e):{}),this.shape},...q(t)};return new np(r)}const op=S("ZodUnion",(e,t)=>{ou.init(e,t),be.init(e,t),e.options=t.options});function sr(e,t){return new op({type:"union",options:e,...q(t)})}const ip=S("ZodIntersection",(e,t)=>{iu.init(e,t),be.init(e,t)});function sp(e,t){return new ip({type:"intersection",left:e,right:t})}const dn=S("ZodEnum",(e,t)=>{su.init(e,t),be.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);const r=new Set(Object.keys(t.entries));e.extract=(n,i)=>{const s={};for(const a of n)if(r.has(a))s[a]=t.entries[a];else throw new Error(`Key ${a} not found in enum`);return new dn({...t,checks:[],...q(i),entries:s})},e.exclude=(n,i)=>{const s={...t.entries};for(const a of n)if(r.has(a))delete s[a];else throw new Error(`Key ${a} not found in enum`);return new dn({...t,checks:[],...q(i),entries:s})}});function ap(e,t){const r=Array.isArray(e)?Object.fromEntries(e.map(n=>[n,n])):e;return new dn({type:"enum",entries:r,...q(t)})}const cp=S("ZodTransform",(e,t)=>{au.init(e,t),be.init(e,t),e._zod.parse=(r,n)=>{if(n.direction==="backward")throw new Oi(e.constructor.name);r.addIssue=s=>{if(typeof s=="string")r.issues.push(Bt(s,r.value,t));else{const a=s;a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=r.value),a.inst??(a.inst=e),r.issues.push(Bt(a))}};const i=t.transform(r.value,r);return i instanceof Promise?i.then(s=>(r.value=s,r)):(r.value=i,r)}});function lp(e){return new cp({type:"transform",transform:e})}const ts=S("ZodOptional",(e,t)=>{cu.init(e,t),be.init(e,t),e.unwrap=()=>e._zod.def.innerType});function Ao(e){return new ts({type:"optional",innerType:e})}const up=S("ZodNullable",(e,t)=>{lu.init(e,t),be.init(e,t),e.unwrap=()=>e._zod.def.innerType});function Do(e){return new up({type:"nullable",innerType:e})}const dp=S("ZodDefault",(e,t)=>{uu.init(e,t),be.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function pp(e,t){return new dp({type:"default",innerType:e,get defaultValue(){return typeof t=="function"?t():Pi(t)}})}const fp=S("ZodPrefault",(e,t)=>{du.init(e,t),be.init(e,t),e.unwrap=()=>e._zod.def.innerType});function hp(e,t){return new fp({type:"prefault",innerType:e,get defaultValue(){return typeof t=="function"?t():Pi(t)}})}const rs=S("ZodNonOptional",(e,t)=>{pu.init(e,t),be.init(e,t),e.unwrap=()=>e._zod.def.innerType});function gp(e,t){return new rs({type:"nonoptional",innerType:e,...q(t)})}const xp=S("ZodCatch",(e,t)=>{fu.init(e,t),be.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function mp(e,t){return new xp({type:"catch",innerType:e,catchValue:typeof t=="function"?t:()=>t})}const vp=S("ZodPipe",(e,t)=>{hu.init(e,t),be.init(e,t),e.in=t.in,e.out=t.out});function To(e,t){return new vp({type:"pipe",in:e,out:t})}const yp=S("ZodReadonly",(e,t)=>{gu.init(e,t),be.init(e,t),e.unwrap=()=>e._zod.def.innerType});function bp(e){return new yp({type:"readonly",innerType:e})}const jp=S("ZodCustom",(e,t)=>{xu.init(e,t),be.init(e,t)});function wp(e,t={}){return ld(jp,e,t)}function _p(e){return ud(e)}function Ro(e){return bu(es,e)}function Je(e){return qu(An,e)}const Oo=(e,t,r)=>{if(e&&"reportValidity"in e){const n=P(r,t);e.setCustomValidity(n&&n.message||""),e.reportValidity()}},pn=(e,t)=>{for(const r in t.fields){const n=t.fields[r];n&&n.ref&&"reportValidity"in n.ref?Oo(n.ref,r,e):n&&n.refs&&n.refs.forEach(i=>Oo(i,r,e))}},$o=(e,t)=>{t.shouldUseNativeValidation&&pn(e,t);const r={};for(const n in e){const i=P(t.fields,n),s=Object.assign(e[n]||{},{ref:i&&i.ref});if(kp(t.names||Object.keys(e),n)){const a=Object.assign({},P(r,n));ae(a,"root",s),ae(r,n,a)}else ae(r,n,s)}return r},kp=(e,t)=>{const r=Bo(t);return e.some(n=>Bo(n).match(`^${r}\\.\\d+`))};function Bo(e){return e.replace(/\]|\[/g,"")}function Po(e,t){try{var r=e()}catch(n){return t(n)}return r&&r.then?r.then(void 0,t):r}function Cp(e,t){for(var r={};e.length;){var n=e[0],i=n.code,s=n.message,a=n.path.join(".");if(!r[a])if("unionErrors"in n){var c=n.unionErrors[0].errors[0];r[a]={message:c.message,type:c.code}}else r[a]={message:s,type:i};if("unionErrors"in n&&n.unionErrors.forEach(function(f){return f.errors.forEach(function(u){return e.push(u)})}),t){var l=r[a].types,p=l&&l[n.code];r[a]=wn(a,t,r,i,p?[].concat(p,n.message):n.message)}e.shift()}return r}function Fp(e,t){for(var r={};e.length;){var n=e[0],i=n.code,s=n.message,a=n.path.join(".");if(!r[a])if(n.code==="invalid_union"&&n.errors.length>0){var c=n.errors[0][0];r[a]={message:c.message,type:c.code}}else r[a]={message:s,type:i};if(n.code==="invalid_union"&&n.errors.forEach(function(f){return f.forEach(function(u){return e.push(u)})}),t){var l=r[a].types,p=l&&l[n.code];r[a]=wn(a,t,r,i,p?[].concat(p,n.message):n.message)}e.shift()}return r}function Rr(e,t,r){if(r===void 0&&(r={}),(function(n){return"_def"in n&&typeof n._def=="object"&&"typeName"in n._def})(e))return function(n,i,s){try{return Promise.resolve(Po(function(){return Promise.resolve(e[r.mode==="sync"?"parse":"parseAsync"](n,t)).then(function(a){return s.shouldUseNativeValidation&&pn({},s),{errors:{},values:r.raw?Object.assign({},n):a}})},function(a){if((function(c){return Array.isArray(c==null?void 0:c.issues)})(a))return{values:{},errors:$o(Cp(a.errors,!s.shouldUseNativeValidation&&s.criteriaMode==="all"),s)};throw a}))}catch(a){return Promise.reject(a)}};if((function(n){return"_zod"in n&&typeof n._zod=="object"})(e))return function(n,i,s){try{return Promise.resolve(Po(function(){return Promise.resolve((r.mode==="sync"?Sc:Ic)(e,n,t)).then(function(a){return s.shouldUseNativeValidation&&pn({},s),{errors:{},values:r.raw?Object.assign({},n):a}})},function(a){if((function(c){return c instanceof In})(a))return{values:{},errors:$o(Fp(a.issues,!s.shouldUseNativeValidation&&s.criteriaMode==="all"),s)};throw a}))}catch(a){return Promise.reject(a)}};throw new Error("Invalid input: not a Zod schema")}function Ep({sx:e}={}){return o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",style:e,children:o.jsx("path",{d:"M9.16732 26.6667C7.14065 26.6667 5.41176 25.9689 3.98065 24.5733C2.54954 23.1689 1.83398 21.4578 1.83398 19.44C1.83398 17.7067 2.35398 16.16 3.39398 14.8C4.44287 13.44 5.81176 12.5733 7.50065 12.2C8.06065 10.1556 9.17176 8.50223 10.834 7.24001C12.5051 5.9689 14.394 5.33334 16.5007 5.33334C19.1051 5.33334 21.3095 6.24001 23.114 8.05334C24.9273 9.85779 25.834 12.0622 25.834 14.6667C27.3718 14.8445 28.6429 15.5111 29.6473 16.6667C30.6607 17.8045 31.1673 19.1378 31.1673 20.6667C31.1673 22.3378 30.5851 23.7556 29.4207 24.92C28.2562 26.0845 26.8384 26.6667 25.1673 26.6667H17.834C17.1051 26.6667 16.4784 26.4045 15.954 25.88C15.4295 25.3645 15.1673 24.7378 15.1673 24V17.1333L13.034 19.2L11.1673 17.3333L16.5007 12L21.834 17.3333L19.9673 19.2L17.834 17.1333V24H25.1673C26.1007 24 26.8873 23.6756 27.5273 23.0267C28.1762 22.3867 28.5007 21.6 28.5007 20.6667C28.5007 19.7333 28.1762 18.9467 27.5273 18.3067C26.8873 17.6578 26.1007 17.3333 25.1673 17.3333H23.1673V14.6667C23.1673 12.8267 22.5184 11.2533 21.2207 9.94668C19.9229 8.6489 18.3495 8.00001 16.5007 8.00001C14.6607 8.00001 13.0873 8.6489 11.7807 9.94668C10.4829 11.2533 9.83398 12.8267 9.83398 14.6667H9.16732C7.87843 14.6667 6.78065 15.1245 5.87398 16.04C4.95843 16.9467 4.50065 18.0445 4.50065 19.3333C4.50065 20.6222 4.95843 21.7333 5.87398 22.6667C6.78065 23.5556 7.87843 24 9.16732 24H12.5007V26.6667",fill:"#1877F2"})})}const Sp=Ht(o.jsx("path",{d:"M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"}),"Download"),Ip=Ht(o.jsx("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit"),zp=Ht(o.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete"),Ap=(e,t=2,r=!1)=>{if(e===0)return"0 B";const n=1024,i=t<0?0:t,s=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],a=Math.floor(Math.log(e)/Math.log(n)),c=Number.parseFloat((e/n**a).toFixed(i)),l=s[a];return r?{size:c,unit:l}:`${c} ${l}`},Dp=({name:e,size:t,url:r,isImage:n=!0,onDelete:i,onEdit:s})=>{const a=t?Ap(t,2,!0):null;return o.jsxs(W,{display:"flex",alignItems:"center",p:2,borderRadius:1,gap:2,sx:{width:"100%",height:80},children:[n?o.jsx(W,{component:"img",src:r,alt:e,loading:"lazy",sx:{width:64,height:64,objectFit:"cover",borderRadius:1,border:"1px solid #ddd"}}):o.jsx(W,{display:"flex",alignItems:"center",justifyContent:"center",width:64,height:64,border:"1px solid #ddd",borderRadius:1,children:o.jsx("i",{className:"mdi mdi-file",style:{fontSize:24}})}),o.jsxs(W,{flex:1,children:[o.jsx(M,{variant:"body2",noWrap:!0,sx:{fontWeight:500},children:e}),o.jsx(M,{variant:"caption",color:"#9b9eb8",children:a?`${a.size} ${a.unit}`:"-"})]}),o.jsxs(W,{display:"flex",gap:1,children:[o.jsx(bt,{title:"Download",children:o.jsx(Ce,{href:r,target:"_blank",download:!0,size:"small",color:"primary",children:o.jsx(Sp,{})})}),o.jsx(bt,{title:"Edit",children:o.jsx(Ce,{size:"small",color:"inherit",onClick:s,children:o.jsx(Ip,{})})}),o.jsx(bt,{title:"Delete",children:o.jsx(Ce,{size:"small",color:"error",onClick:i,children:o.jsx(zp,{})})})]})]})},Tp=({handleEditClick:e})=>{const{t}=oe();return o.jsxs(W,{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center",gap:1,py:2,onClick:e,children:[o.jsx(Ep,{}),o.jsxs(M,{variant:"body2",color:"#75799D",fontSize:16,fontWeight:400,children:[t("core:drop-file-here")," ",o.jsx("span",{style:{color:"#1877F2"},children:t("core:select-from-device")})]})]})};function Dn({setImageId:e,url:t,initData:r}){var _;const{t:n}=oe(),{enqueueSnackbar:i}=Ps(),{mutate:s,isPending:a}=Ms(),[c,l]=v.useState({file:null,url:null}),p=v.useRef(null),f=()=>{var k;(k=p.current)==null||k.click()};v.useEffect(()=>{var k;console.log("initData value",r,c.file),r!=null&&r.file&&!c.file&&(console.log("initallll",r.file.name),l({file:r.file,url:(k=r.file)!=null&&k.path?`${window.location.origin}/s3/${r.file.path}`:null}))},[r,c.file]),v.useEffect(()=>{console.log("uploadFile init",c)},[c]);const u=async k=>{s({url:t,file:k},{onSuccess:A=>{console.log("upload onSuccess",k.name),l({file:k,url:A.url}),e(A.id)},onError:()=>{i(n("core:toast-file-upload-error"),{variant:"error"})}})},d=k=>k.name.endsWith(".jpg")||k.name.endsWith(".jpeg")||k.name.endsWith(".png"),g=k=>k.size<=10*1024*1024,b=k=>{if(!d(k))return i(n("core:toast-invalid-file-type"),{variant:"error"});if(!g(k))return i(n("core:toast-invalid-file-size"),{variant:"error"});console.log("oop",k),u(k)};return o.jsx(o.Fragment,{children:o.jsxs(W,{position:"relative",sx:{width:"100%",height:"80px"},children:[a&&o.jsx(W,{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",sx:{backgroundColor:"rgba(255,255,255,0.7)",zIndex:10},children:o.jsxs(W,{display:"flex",flexDirection:"column",alignItems:"center",gap:1,children:[o.jsx(he,{}),o.jsx(M,{variant:"body2",color:"#75799D",children:n("core:uploading")})]})}),o.jsx("input",{ref:p,type:"file",accept:"image/png, image/jpg, image/jpeg",hidden:!0,onChange:k=>{var A;return((A=k.target.files)==null?void 0:A[0])&&b(k.target.files[0])}}),o.jsx(se,{fullWidth:!0,variant:"outlined",sx:{minHeight:80,width:"100%",borderStyle:"dashed",borderColor:"#DBE3EB",borderRadius:1,":hover":{backgroundColor:"#F5F7FA",borderColor:"#1877F2"}},component:"div",children:c.file?o.jsx(Dp,{name:c.file.name,size:c.file.size||((_=c.file)==null?void 0:_.file_size),url:c.url,isImage:!0,onDelete:()=>l({file:null,url:null}),onEdit:f}):o.jsx(Tp,{handleEditClick:f})})]})})}const Tn=Ht(o.jsx("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close");function ns({value:e,onChange:t}){const{t:r}=oe(),[n,i]=v.useState([]),[s,a]=v.useState(""),[c]=ht(s,500),{data:l,isLoading:p}=Zs(c),f=l??[];return v.useEffect(()=>{const u=f.filter(d=>e.includes(d.id));i(d=>d.length===u.length&&d.every((_,k)=>_.id===u[k].id)?d:u)},[f,e]),o.jsx(kt,{multiple:!0,options:f,getOptionLabel:u=>{var d,g,b;return typeof u=="string"?u:((d=u.full_name)==null?void 0:d.trim())||((g=u.email_address)==null?void 0:g.trim())||((b=u.code)==null?void 0:b.toString())||""},isOptionEqualToValue:(u,d)=>u.id===d.id,filterOptions:u=>u,value:n,onChange:(u,d)=>{const g=d.map(b=>b.id);t(g),i(d)},onInputChange:(u,d)=>a(d),renderOption:(u,d)=>v.createElement(x,{...u,component:"li",key:d.id,flexDirection:"row",alignItems:"center",gap:1,sx:{pointerEvents:"auto"}},o.jsx("img",{src:d.photo,width:"42",height:"42",alt:"staff"}),o.jsxs(x,{flexDirection:"column",children:[o.jsxs(M,{component:"p",children:[d.full_name," ",o.jsxs(M,{component:"span",color:"#9b9eb8",fontSize:"0.75rem",children:["#",d.code]})]}),d.email_address&&o.jsx(M,{component:"p",color:"#9b9eb8",fontSize:"0.75rem",children:d.email_address})]})),renderTags:(u,d)=>u.map((g,b)=>o.jsxs(x,{flexDirection:"row",sx:{color:"#444",height:"auto",alignItems:"center",padding:0,backgroundColor:"#F6F9FC",gap:"8px",marginInlineEnd:"8px"},...d({index:b}),children:[g.photo&&o.jsx(dt,{src:g.photo,sx:{width:"30px",height:"30px",borderRadius:"2px"}}),o.jsxs(x,{flexDirection:"row",alignItems:"center",gap:"5px",children:[o.jsx(M,{variant:"body2",children:g.full_name}),o.jsxs(M,{variant:"caption",noWrap:!0,children:["(",g.email_address,")"]})]}),o.jsx(Ce,{size:"small",sx:{p:"2px",color:"#75799D",borderRadius:0},onClick:_=>{_.stopPropagation();const k=n.filter(A=>A.id!==g.id);i(k),t(k.map(A=>A.id))},children:o.jsx(Tn,{fontSize:"small"})})]},g.id)),renderInput:u=>o.jsx(ze,{...u.InputProps,inputProps:u.inputProps,placeholder:r("core:selectEmployee"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"5px",padding:"8px 8px 8px 14px !important",backgroundColor:"#fff",gap:1},endAdornment:o.jsx(Ie,{})}),sx:{width:"100%",borderRadius:0,gap:"8px"}})}const Rp=({value:e,onChange:t})=>{const[r,n]=v.useState(e),{t:i}=oe();v.useEffect(()=>{n(e)},[e]);const s=Number.parseFloat(r),c=!Number.isNaN(s)?Ot(s):Ot(0);return o.jsxs(vr,{size:{xs:12},children:[o.jsxs(vn,{shrink:!0,children:[i("core:duration")," ",o.jsx(Xe,{children:"*"})]}),o.jsx(ze,{type:"text",fullWidth:!0,required:!0,inputProps:{inputMode:"decimal",pattern:"[0-9]*[.,]?[0-9]*",onFocus:l=>l.target.select()},value:r,onChange:l=>{const p=l.target.value;n(p);const f=Number.parseFloat(p);Number.isNaN(f)?t(0):t(f)},sx:{border:"1px solid #E4EBF2",borderRadius:"5px",padding:"10px"},endAdornment:o.jsxs(x,{flexDirection:"row",gap:1,alignItems:"center",children:[o.jsxs(M,{sx:{whiteSpace:"nowrap",fontSize:14,color:"#75799D"},children:["= ",c]}),o.jsx(H,{icon:"mdi:clock-outline",width:20,height:20,color:"#202124"})]})})]})},Op=({value:e,onChange:t,currency:r})=>{const{t:n}=oe(),[i,s]=v.useState(e.toString());return v.useEffect(()=>{s(e.toString())},[e]),o.jsxs(vr,{size:{xs:12},children:[o.jsxs(vn,{shrink:!0,children:[n("core:price")," ",o.jsx(Xe,{children:"*"})]}),o.jsx(ze,{fullWidth:!0,required:!0,inputProps:{inputMode:"decimal",pattern:"[0-9]*[.,]?[0-9]*",onFocus:a=>a.target.select()},value:i,onChange:a=>{const c=a.target.value;s(c);const l=Number.parseFloat(c);Number.isNaN(l)?t(0):t(l)},sx:{border:"1px solid #E4EBF2",borderRadius:"5px",padding:"10px"},endAdornment:o.jsxs(x,{flexDirection:"row",gap:1,alignItems:"center",children:[o.jsx(M,{sx:{whiteSpace:"nowrap",fontSize:14,color:"#75799D"},children:r}),o.jsx(H,{icon:"mdi:cash",width:24,height:24,color:"#202124"})]})})]})},Gr=we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",width:"100%",height:"50px",transition:"all 0.2s ease",color:"#213242",backgroundColor:"#FFF",border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",flex:1,paddingInlineEnd:"35px !important"},"&:hover":{backgroundColor:"#E4EBF2"},".MuiSelect-icon":{color:"#213242"}})),$p=Pt({name:Ve().min(1,"Name is required"),duration_minutes:Je().min(1,"Duration is required"),unit_price:Je().min(1,"Price is required"),tax1:Je().optional(),tax2:Je().optional(),photo:Je().optional(),category_ids:gr(Jd()).optional(),assigned_staff_ids:gr(ir()).optional()}),Bp=({categoriesList:e,taxes:t,initialData:r,selectedCategoryId:n,onClose:i})=>{var $,R,z;const{control:s,handleSubmit:a,formState:{errors:c},setValue:l,register:p,reset:f,watch:u}=Fr({resolver:Rr($p),defaultValues:{name:"",duration_minutes:60,unit_price:0,tax1:null,tax2:null,photo:null,category_ids:n?[n]:[],assigned_staff_ids:[]}}),{t:d}=oe(),g=Le(),{data:b}=yr(),_=b==null?void 0:b.default_currency_formatted,{mutate:k}=Vs(),{mutate:A}=Ns(),[y,T]=v.useState(null),[j,w]=v.useState(!1);v.useEffect(()=>{var F,Z;f(r?{name:r.name,duration_minutes:r.duration_minutes,unit_price:r.unit_price,tax1:r.tax1,tax2:r.tax2,photo:(F=r.attachment)==null?void 0:F.id,category_ids:r.category_ids,assigned_staff_ids:(Z=r==null?void 0:r.assigned_staffs)==null?void 0:Z.map(I=>I.id)}:{name:"",duration_minutes:60,unit_price:0,tax1:null,tax2:null,photo:null,category_ids:[],assigned_staff_ids:[]}),n&&!r&&l("category_ids",[n])},[r,f,n,l]);const C=async(F,Z)=>{w(!0);const I={...F,photo:y};try{r?await A({serviceId:r==null?void 0:r.id,input:I},{onSuccess:async D=>{re(D.message),g.invalidateQueries({queryKey:["categories"]}),g.invalidateQueries({queryKey:["categoriesWithServices"]}),Z==="close"?i():f({name:"",duration_minutes:60,unit_price:0,tax1:null,tax2:null,photo:null,category_ids:[],assigned_staff_ids:[]}),w(!1)},onError:D=>{w(!1),re(D.message,{variant:"error"})}}):await k({input:I},{onSuccess:async D=>{re(D.message),g.invalidateQueries({queryKey:["categoriesWithServices"]}),Z==="close"?i():f({name:"",duration_minutes:60,unit_price:0,tax1:null,tax2:null,photo:null,category_ids:n?[n]:[],assigned_staff_ids:[]}),w(!1)},onError:D=>{w(!1),re(D.message,{variant:"error"})}})}catch(D){re(D.message,{variant:"error"})}};return o.jsx(W,{px:"20px",sx:{height:"100%",paddingBottom:"100px",overflowY:"auto"},children:o.jsxs("form",{children:[o.jsx(qt,{children:o.jsxs(x,{gap:2,children:[o.jsxs(x,{flexDirection:"row",gap:2,children:[o.jsxs(x,{width:"50%",children:[o.jsxs(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:[d("core:name")," ",o.jsx(Xe,{children:"*"})]}),o.jsx(ze,{...p("name"),autoFocus:!0,placeholder:d("core:egSummerService"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",height:"50px",backgroundColor:"#fff"}}),c.name&&o.jsx(M,{color:"error",fontSize:12,children:($=c.name.message)==null?void 0:$.toString()})]}),o.jsxs(x,{width:"50%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:d("core:category")}),o.jsx(Gr,{...p("category_ids"),inputProps:{"aria-label":"Without label","aria-hidden":!1},multiple:!0,"aria-placeholder":"Select Category","aria-hidden":!1,IconComponent:Ie,displayEmpty:!0,value:u("category_ids")||[],onChange:F=>{const Z=Number(F.target.value[F.target.value.length-1]),I=u("category_ids")||[];I.includes(Z)?l("category_ids",I.filter(D=>D!==Z)):l("category_ids",[...I,Z])},MenuProps:{disableAutoFocusItem:!0,disableScrollLock:!0,PaperProps:{style:{pointerEvents:"auto"}},PopoverClasses:{root:"no-focus-enforce"}},children:(e==null?void 0:e.length)>0?e.map((F,Z)=>{var I,D,U;return o.jsx(ne,{value:(I=F==null?void 0:F.CategoriesCategory)==null?void 0:I.id,sx:{height:"70px"},children:(D=F==null?void 0:F.CategoriesCategory)==null?void 0:D.name},((U=F==null?void 0:F.CategoriesCategory)==null?void 0:U.id)||Z)}):o.jsx(ne,{value:"",disabled:!0,sx:{height:"30px",display:"flex"},children:d("core:noCategoriesAdded")})})]})]}),o.jsxs(x,{flexDirection:"row",gap:2,children:[o.jsxs(x,{width:"50%",children:[o.jsx(Ue,{control:s,name:"duration_minutes",render:({field:F})=>o.jsx(Rp,{value:F.value,onChange:F.onChange})}),c.duration_minutes&&o.jsx(M,{color:"error",fontSize:12,children:(R=c.duration_minutes.message)==null?void 0:R.toString()})]}),o.jsxs(x,{width:"50%",children:[o.jsx(Ue,{control:s,name:"unit_price",render:({field:F})=>o.jsx(Op,{value:F.value,onChange:F.onChange,currency:_})}),c.unit_price&&o.jsx(M,{color:"error",fontSize:12,children:(z=c.unit_price.message)==null?void 0:z.toString()})]})]}),o.jsxs(x,{width:"100%",children:[o.jsxs(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:[d("core:assignedEmployees")," "]}),o.jsx(Ue,{control:s,name:"assigned_staff_ids",render:({field:F})=>o.jsx(ns,{value:F.value,onChange:F.onChange})})]}),(t==null?void 0:t.length)&&o.jsxs(x,{flexDirection:"row",gap:2,children:[o.jsxs(x,{width:"50%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:d("core:tax1")}),o.jsxs(Gr,{...p("tax1"),inputProps:{"aria-label":"Without label"},IconComponent:Ie,MenuProps:{disableScrollLock:!0},displayEmpty:!0,children:[o.jsx(ne,{value:null,children:d("core:none")}),t==null?void 0:t.map((F,Z)=>{var I,D,U;return o.jsx(ne,{value:(I=F==null?void 0:F.Tax)==null?void 0:I.id,sx:{height:"70px"},children:(D=F==null?void 0:F.Tax)==null?void 0:D.name},`taxData-${((U=F==null?void 0:F.Tax)==null?void 0:U.id)||Z}`)})]})]}),(t==null?void 0:t.length)>1&&o.jsxs(x,{width:"50%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:d("core:tax2")}),o.jsxs(Gr,{...p("tax2"),inputProps:{"aria-label":"Without label"},IconComponent:Ie,MenuProps:{disableScrollLock:!0},displayEmpty:!0,children:[o.jsx(ne,{value:null,children:d("core:none")}),t==null?void 0:t.map((F,Z)=>{var I,D,U;return o.jsx(ne,{value:(I=F==null?void 0:F.Tax)==null?void 0:I.id,sx:{height:"70px"},children:(D=F==null?void 0:F.Tax)==null?void 0:D.name},`taxData-${((U=F==null?void 0:F.Tax)==null?void 0:U.id)||Z}`)})]})]})]}),o.jsxs(x,{children:[o.jsx(M,{sx:{fontSize:"16px",color:"#5F6368"},mb:1,children:d("core:image")}),o.jsx(Dn,{setImageId:T,url:"/v2/api/entity/upload_file/product/products.product_images",initData:r?{url:r==null?void 0:r.photo,file:r==null?void 0:r.attachment}:null})]})]})}),o.jsx(gt,{children:o.jsx(W,{sx:{padding:"20px",position:"absolute",bottom:0,left:0,right:0,backgroundColor:"#FFF"},children:o.jsxs(x,{flexDirection:"row",gap:2,alignItems:"center",children:[o.jsx(se,{type:"button",onClick:()=>{a(F=>C(F,"close"))()},disabled:j,sx:{width:"100%",height:"60px",backgroundColor:"#1877F2",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",fontWeight:600,gap:"10px",opacity:j?.7:1},children:d("core:saveClose")}),!r&&o.jsx(se,{onClick:()=>{a(F=>C(F,"addMore"))()},disabled:j,sx:{width:"100%",height:"60px",backgroundColor:"#13B272",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",fontWeight:600,gap:"10px",opacity:j?.7:1},children:d("core:saveAddMore")})]})})})]})})},os=({initialData:e=null,selectedCategoryId:t,onClose:r})=>{const{data:n}=ii(),{data:i}=si();return o.jsx(Bp,{onClose:r,categoriesList:i,taxes:n,initialData:e,selectedCategoryId:t})},Pp=({serviceId:e,...t})=>{const{data:r}=Us(e);return v.useEffect(()=>{console.log("serviceId",r)},[r]),o.jsx(os,{initialData:r,...t})};function Mp({onClose:e,...t}){const{t:r}=oe();return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:t.serviceId?r("core:serviceManagement.edit"):r("core:serviceManagement.add")}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:t.serviceId?o.jsx(Pp,{serviceId:t.serviceId,onClose:e,...t}):o.jsx(os,{onClose:e,...t})})]})}function is(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Mp,{...e})})})})}const Zp=({userPermissions:e,formChanged:t,setFormChanged:r})=>{const{t:n}=oe(),i=Ae("(min-width:993px)"),{data:s}=ai(),[a,c]=v.useState((s==null?void 0:s.service_employee_setting)||"one_employee"),[l,p]=v.useState((s==null?void 0:s.booking_service_setting)||"multiple_services"),[f,u]=v.useState((s==null?void 0:s.default_booking_service)||null),{data:d}=yn(),[g,b]=v.useState(!1),{mutate:_}=Ls(),{isOpen:k,onOpen:A,onClose:y}=Ze();v.useEffect(()=>{if(!s)return;const j=a!==s.service_employee_setting||l!==s.booking_service_setting||l==="default_service"&&f!==s.default_booking_service;r(j)},[a,l,f,s]),v.useEffect(()=>{const j=w=>{t&&(w.preventDefault(),w.returnValue="")};return window.addEventListener("beforeunload",j),()=>window.removeEventListener("beforeunload",j)},[t]);const T=async()=>{b(!0),await _({input:{service_employee_setting:a,booking_service_setting:l,default_booking_service:l==="default_service"&&f?f:null}},{onSuccess:j=>{b(!1),re(j==null?void 0:j.message),r(!1)},onError:j=>{b(!1),re(j.message,{variant:"error"})}})};return o.jsxs(x,{sx:{flex:1,height:"100%"},children:[o.jsx(jr,{title:n("core:bookingWorkflow.label"),subtitle:n("core:bookingWorkflow.head-description"),children:o.jsx(se,{variant:"contained",sx:{backgroundColor:"#13B272",minHeight:"100%",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center","&:hover":{backgroundColor:"#0C744A"},pointerEvents:g?"none":"auto",opacity:g?.8:1},onClick:T,children:g?o.jsx($e,{size:"20px"}):o.jsxs(o.Fragment,{children:[o.jsx(H,{icon:"mdi:content-save",width:"24",height:"24",color:"#FFF",style:{marginInlineEnd:"6px"}}),n("core:bookingWorkflow.save")]})})}),o.jsx(x,{sx:{width:"100%",height:"100%",backgroundColor:"#E4EBF2",position:"relative",padding:i?"30px":"10px",overflowY:"auto"},direction:"column",children:o.jsxs(W,{sx:{width:"100%",backgroundColor:"#FFF",padding:"34px 30px"},children:[o.jsx(M,{variant:"h6",fontSize:"18px",fontWeight:500,color:"#373B50",mb:2,children:n("core:bookingWorkflow.staffTitle")}),o.jsx(Hn,{value:a,onChange:j=>c(j.target.value),children:[{value:"one_employee",title:n("core:bookingWorkflow.staffTypes.one_employee.title"),desc:n("core:bookingWorkflow.staffTypes.one_employee.desc")},{value:"multiple_employees",title:n("core:bookingWorkflow.staffTypes.multiple_employees.title"),desc:n("core:bookingWorkflow.staffTypes.multiple_employees.desc")},{value:"no_employee",title:n("core:bookingWorkflow.staffTypes.no_employee.title"),desc:n("core:bookingWorkflow.staffTypes.no_employee.desc")}].map(j=>o.jsx(W,{children:o.jsx(ro,{value:j.value,title:j.title,description:j.desc,selected:a===j.value,onChange:c})},j.value))}),o.jsx(W,{sx:{width:"100%",height:"1px",border:"1px dashed #E4EBF2",marginBlock:"30px"}}),o.jsxs(W,{children:[o.jsx(M,{variant:"h6",fontSize:"18px",fontWeight:500,color:"#373B50",mb:2,children:n("core:bookingWorkflow.serviceTitle")}),o.jsx(Hn,{value:l,onChange:j=>p(j.target.value),children:[{value:"multiple_services",title:n("core:bookingWorkflow.serviceTypes.multiple_services.title"),desc:n("core:bookingWorkflow.serviceTypes.multiple_services.desc")},{value:"one_service",title:n("core:bookingWorkflow.serviceTypes.one_service.title"),desc:n("core:bookingWorkflow.serviceTypes.one_service.desc")},{value:"default_service",title:n("core:bookingWorkflow.serviceTypes.default_service.title"),desc:n("core:bookingWorkflow.serviceTypes.default_service.desc")}].map(j=>{var w;return o.jsx(W,{children:o.jsx(ro,{value:j.value,title:j.title,description:j.desc,selected:l===j.value,onChange:p,children:j.value==="default_service"&&d&&l==="default_service"&&o.jsxs(x,{flexDirection:"row",alignItems:"center",gap:1,mt:2,children:[o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(qa,{selectedServiceId:f,onChange:u,servicesData:d})}),((e==null?void 0:e.is_super_admin)||((w=e==null?void 0:e.permissions)==null?void 0:w.includes(26)))&&o.jsxs(Ce,{sx:{backgroundColor:"#4E5381",height:"50px",padding:"0 16px",borderRadius:0,color:"#FFF",fontSize:"14px","&:hover":{backgroundColor:"#414570"}},onClick:()=>A(),children:[o.jsx(H,{icon:"mdi:plus",width:"24",height:"24",color:"#FFF",style:{marginInlineEnd:"6px"}}),n("core:bookingWorkflow.addNewService")]})]})})},j.value)})})]})]})}),o.jsx(is,{open:k,onClose:y,serviceId:null,selectedCategoryId:null})]})};we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",color:"#213242",width:"100%",border:"1px solid #E4EBF2",borderRadius:"5px",padding:"0px",backgroundColor:"#FFF",fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",width:"100%",paddingInlineEnd:"35px !important"},".MuiSelect-icon":{color:"#213242"}}));const Vp=[{label:"Everyone",value:"everyone"},{label:"None",value:"none"},{label:"Specific Department(s)",value:"specific-departments"},{label:"Specific Designation(s)",value:"specific-designations"},{label:"Specific Employee(s)",value:"specific-employees"},{label:"Specific Role(s)",value:"specific-roles"}],Np=we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",color:"#213242",width:"100%",border:"1px solid #E4EBF2",padding:"0px",backgroundColor:"#FFF",borderRadius:0,fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",width:"100%",paddingInlineEnd:"35px !important"},".MuiSelect-icon":{color:"#213242"}}));function rr({label:e,name:t,apiUrl:r,permissionsReqData:n,setPermissionsReqData:i,defaultType:s="everyone",defaultValues:a=[]}){var T,j,w,C,$;const[c,l]=v.useState(""),[p]=ht(c,500),[f,u]=v.useState(((T=n[t])==null?void 0:T.type)||s),{data:d,isLoading:g}=Ws(`/v2/api/local_entities/permissions/criteria_type_data?type=${(j=n[t])==null?void 0:j.type}&q=${p}`),b=(d==null?void 0:d.data)??[],[_,k]=v.useState(((w=n[t])==null?void 0:w.data)||[]),A=f!=="everyone"&&f!=="none",y=v.useRef(!1);return v.useEffect(()=>{var R,z;y.current||(u(((R=n[t])==null?void 0:R.type)||s),k(((z=n[t])==null?void 0:z.data)||[]),y.current=!0)},[n,t,s]),v.useEffect(()=>{var F,Z;const R=((F=n[t])==null?void 0:F.type)||s,z=((Z=n[t])==null?void 0:Z.data)||[];u(R),k(z)},[(C=n[t])==null?void 0:C.type,($=n[t])==null?void 0:$.data]),o.jsxs("div",{style:{marginBottom:"16px"},children:[o.jsx(vn,{shrink:!0,children:e}),o.jsx(Np,{value:f,onChange:R=>{u(R.target.value),i(z=>({...z,[t]:{...z[t],type:R.target.value,value:[]}})),k([])},IconComponent:Ie,children:Vp.map(R=>o.jsx(ne,{value:R.value,children:R.label},R.value))}),A&&o.jsx(kt,{multiple:!0,options:b,getOptionLabel:R=>R,isOptionEqualToValue:(R,z)=>R.id===z.id,filterOptions:R=>R,value:_,onChange:(R,z)=>{const F=z.map(Z=>Z.id);i(Z=>({...Z,[t]:{...Z[t],value:F}})),console.log("selectedEmployees",z),k(z)},onInputChange:(R,z)=>l(z),renderOption:(R,z)=>v.createElement(x,{...R,component:"li",key:z.id,flexDirection:"row",alignItems:"center",gap:1,sx:{pointerEvents:"auto"}},z.img&&o.jsx("img",{src:z.img,width:"42",height:"42",alt:"staff"}),o.jsx(x,{flexDirection:"column",children:o.jsxs(M,{component:"p",children:[z.text," "]})})),renderTags:(R,z)=>R.map((F,Z)=>o.jsxs(x,{flexDirection:"row",sx:{color:"#444",height:"auto",alignItems:"center",padding:0,backgroundColor:"#F6F9FC",gap:"8px",marginInlineEnd:"8px"},...z({index:Z}),children:[F.img&&o.jsx(dt,{src:F.img,sx:{width:"30px",height:"30px",borderRadius:"2px"}}),o.jsx(x,{flexDirection:"row",alignItems:"center",gap:"5px",children:o.jsx(M,{variant:"body2",children:F.text})}),o.jsx(Ce,{size:"small",sx:{p:"2px",color:"#75799D",borderRadius:0},children:o.jsx(Tn,{fontSize:"small"})})]},F.id)),renderInput:R=>o.jsx(ze,{...R.InputProps,inputProps:R.inputProps,placeholder:"Select",fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"5px",padding:"8px 8px 8px 14px !important",backgroundColor:"#fff",gap:1,borderStartEndRadius:0},endAdornment:o.jsx(Ie,{})}),sx:{width:"100%",gap:"8px",borderRadius:0}})]})}const Up=({url:e,builderUrl:t,onCancel:r})=>{const{t:n}=oe(),[i,s]=v.useState(!0),a=v.useRef(null),c=()=>{const l=a.current;if(l!=null&&l.contentDocument){const p=document.createElement("style");p.innerHTML=`
                     body {
                        background-color: #FFF !important;
                    } 
                      .pages-head  {
                          display: none !important;
                      }
                    form{
                    pointer-events: none;
                    }
                    `,l.contentDocument.head.appendChild(p)}setTimeout(()=>{s(!1)},300)};return o.jsxs(x,{sx:{width:"100%",height:"100%",paddingBottom:"100px"},children:[e?o.jsxs(o.Fragment,{children:[i&&o.jsx(he,{}),o.jsx("iframe",{src:`${e}v2/owner/entity/le_custom_data_service_form_${e}/create?iframe=1`,ref:a,style:{width:"100%",height:"100%",border:"0px",outline:"none"},onLoad:c})]}):o.jsxs(x,{sx:{width:"100%",height:"290px",p:2,border:"1px dashed #1877F2",backgroundColor:"E4EBF2"},children:[o.jsx(x,{sx:{backgroundColor:"#F6F9FC",padding:"12px",fontSize:"15px",color:"#373B50",fontWeight:600},children:"Section Name"}),o.jsx(x,{sx:{width:"100%",height:"100%",backgroundColor:"#FFF",alignItems:"center",justifyContent:"center"},children:o.jsxs(W,{children:[o.jsxs(M,{fontWeight:500,color:"#75799D",children:[n("core:sectionEmpty"),"  "]}),o.jsxs(M,{fontSize:"13px",color:"#75799D",children:[n("core:startAddingFields"),o.jsxs(M,{component:"span",color:"#202124",fontSize:"13px",children:["“",n("core:openFormBuilder"),"”"]}),"."]}),o.jsxs(se,{variant:"text",type:"link",href:t,target:"_blank",sx:{marginTop:"10px",color:"#13B272",padding:0,fontSize:"14px",fontWeight:400,"&:hover":{backgroundColor:"transparent"}},children:[o.jsx(H,{icon:"mdi:plus",width:"20",height:"20",color:"#13B272",style:{marginInlineEnd:"3px"}}),n("core:openFormBuilder")]})]})})]}),o.jsx(W,{sx:{position:"absolute",bottom:"20px",left:0,right:0,backgroundColor:"#FFF"},children:o.jsxs(x,{flexDirection:"row",gap:2,alignItems:"center",children:[o.jsx(se,{onClick:()=>{r()},sx:{width:"100%",height:"60px",backgroundColor:"#E4EBF2",color:"#373B50",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",gap:"10px"},children:n("core:cancel_capital")}),o.jsxs(se,{type:"link",href:t,target:"_blank",sx:{width:"100%",height:"60px",backgroundColor:"#1877F2",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",gap:"10px","&:hover":{color:"#FFF"}},children:[o.jsx(H,{icon:"mdi:format-list-group-plus",width:"24",height:"24"}),n("core:openFormBuilder")]})]})})]})},Jr="/",Mo=we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",width:"100%",height:"50px",transition:"all 0.2s ease",color:"#213242",backgroundColor:"#FFF",border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",flex:1,paddingInlineEnd:"35px !important"},"&:hover":{backgroundColor:"#E4EBF2"},".MuiSelect-icon":{color:"#213242"}}));function Zo(e){const{children:t,value:r,index:n,...i}=e;return r!==n?null:o.jsx(W,{role:"tabpanel",hidden:r!==n,id:`simple-tabpanel-${n}`,"aria-labelledby":`simple-tab-${n}`,position:"relative",flex:1,display:"flex",width:"100%",height:"calc(100% - 100px)",...i,children:t})}const Lp=({initialData:e,selectedService:t,onClose:r})=>{var C,$,R,z,F,Z,I;const{t:n}=oe(),i=Pt({label:Ve().min(1,n("core:nameRequired")),key:Ro().min(1,n("core:keyRequired")),status:Je().min(1,n("core:statusRequired")),description:Ro().optional(),desscription:Ve().optional(),filling_time:Je().min(1,n("core:fillingTimeRequired"))}),{control:s,handleSubmit:a,formState:{errors:c},setValue:l,register:p,reset:f}=Fr({resolver:Rr(i),defaultValues:{label:"",status:1,key:"",description:"",filling_time:1}}),{mutate:u}=Hs(),[d,g]=v.useState(0),[b,_]=v.useState(null),k=(D,U)=>{g(U)},A=Le(),{mutate:y}=qs(),[T,j]=v.useState({create:{type:"everyone",value:[]},update:{type:"everyone",value:[]},view:{type:"everyone",value:[]},delete:{type:"everyone",value:[]}});v.useEffect(()=>{var D;(D=t==null?void 0:t.service_form)!=null&&D.id?(f({label:e.label,status:e.status,key:e.key,description:e.description,filling_time:t==null?void 0:t.service_form.filling_time}),j((e==null?void 0:e.entity_permissions)||{create:{type:"everyone",value:[]},update:{type:"everyone",value:[]},view:{type:"everyone",value:[]},delete:{type:"everyone",value:[]}})):f({label:"",status:1,key:"",description:"",filling_time:1})},[e,f]);const w=async D=>{var X,O;const U={...D,permissions:T,service_form:{service_id:t==null?void 0:t.id,filling_time:D.filling_time}};try{(X=t==null?void 0:t.service_form)!=null&&X.id?await y({serviceFormId:(O=t==null?void 0:t.service_form)==null?void 0:O.id,input:U},{onSuccess:async N=>{re(N.message),A.invalidateQueries({queryKey:["services"]}),g(1)},onError:N=>{re(N.message,{variant:"error"})}}):await u({input:U},{onSuccess:async N=>{var le;re(N.message),A.invalidateQueries({queryKey:["services"]}),_((le=N==null?void 0:N.data)==null?void 0:le.targert_url),g(1)},onError:N=>{re(N.message,{variant:"error"})}})}catch(N){re(N.message,{variant:"error"})}};return o.jsxs(W,{px:"20px",sx:{height:"100%",width:"100%",overflowY:d===0?"auto":"hidden",backgroundColor:"#FFF"},children:[o.jsxs(Ks,{value:d,onChange:k,sx:{borderBottom:"1px solid  #DBE3EB",marginBottom:"20px",marginTop:"32px"},children:[o.jsx(qn,{sx:{textTransform:"none",fontWeight:400,fontSize:"1rem",paddingLeft:"0px",paddingRight:"0px",marginInlineEnd:"20px","&.Mui-selected:focus":{outline:"none",boxShadow:"none"}},id:"simple-tab-0","aria-controls":"simple-tabpanel-0",label:n("core:formInformation")}),o.jsx(qn,{sx:{textTransform:"none",fontWeight:400,fontSize:"1rem",paddingLeft:"0px",paddingRight:"0px",marginInlineEnd:"20px","&.Mui-selected:focus":{outline:"none",boxShadow:"none"}},id:"simple-tab-1","aria-controls":"simple-tabpanel-1",label:n("core:formFields"),disabled:!(b||(C=t==null?void 0:t.service_form)!=null&&C.id)})]}),o.jsx(Zo,{value:d,index:0,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(W,{width:"100%",children:o.jsxs("form",{onSubmit:a(w),style:{width:"100%",height:"100%"},children:[o.jsxs(x,{gap:2,children:[o.jsxs(x,{flexDirection:"row",gap:2,children:[o.jsxs(x,{width:"50%",children:[o.jsxs(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:[n("core:name")," ",o.jsx(Xe,{children:"*"})]}),o.jsx(ze,{...p("label"),placeholder:n("core:name"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",height:"50px",backgroundColor:"#fff"}}),c.label&&o.jsx(M,{color:"error",fontSize:12,children:($=c.label.message)==null?void 0:$.toString()})]}),o.jsxs(x,{width:"50%",children:[o.jsxs(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:[n("core:key")," ",o.jsx(Xe,{children:"*"})]}),o.jsx(ze,{...p("key"),placeholder:n("core:key"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",height:"50px",backgroundColor:"#fff"}}),c.key&&o.jsx(M,{color:"error",fontSize:12,children:(R=c.key.message)==null?void 0:R.toString()})]})]}),o.jsxs(x,{flexDirection:"row",gap:2,children:[o.jsxs(x,{width:"50%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:n("core:formFillingTime")}),o.jsx(Ue,{name:"filling_time",control:s,render:({field:D})=>o.jsxs(Mo,{value:D.value,onChange:U=>D.onChange(U.target.value),IconComponent:Ie,MenuProps:{disableScrollLock:!0},displayEmpty:!0,children:[o.jsx(ne,{value:1,sx:{height:"70px"},children:n("core:duringBooking")}),o.jsx(ne,{value:2,sx:{height:"70px"},children:n("core:duringService")})]})})]}),o.jsxs(x,{width:"50%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:n("core:status")}),o.jsx(Ue,{name:"status",control:s,render:({field:D})=>o.jsxs(Mo,{value:D.value,onChange:U=>D.onChange(U.target.value),IconComponent:Ie,MenuProps:{disableScrollLock:!0},displayEmpty:!0,children:[o.jsx(ne,{value:1,sx:{height:"70px"},children:n("core:active")}),o.jsx(ne,{value:0,sx:{height:"70px"},children:n("core:inactive")})]})})]})]}),o.jsx(x,{flexDirection:"row",gap:2,children:o.jsxs(x,{width:"100%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:n("core:description")}),o.jsx(Ue,{control:s,name:n("core:description"),render:({field:D})=>o.jsx(ci,{minRows:3,placeholder:n("core:placeholder.description"),style:{width:"100%",padding:"10px",border:"1px solid #E4EBF2",color:"#212B36",borderRadius:"5px",fontFamily:"roboto",fontSize:"1rem",backgroundColor:"#FFF"},value:D.value,onChange:D.onChange})})]})}),o.jsx(W,{sx:{width:"100%",height:1,marginBlock:"32px",border:"1px dashed #E4EBF2"}}),o.jsx(M,{fontSize:18,color:"#202124",fontWeight:600,mb:3,children:n("core:permissions")}),o.jsx(rr,{label:n("core:addRecord"),name:"create",apiUrl:"/api/permissions/add",permissionsReqData:T,setPermissionsReqData:j}),o.jsx(rr,{label:n("core:updateRecord"),name:"update",apiUrl:"/api/permissions/update",permissionsReqData:T,setPermissionsReqData:j}),o.jsx(rr,{label:n("core:viewRecord"),name:"view",apiUrl:"/api/permissions/view",permissionsReqData:T,setPermissionsReqData:j}),o.jsx(rr,{label:n("core:deleteRecord"),name:"delete",apiUrl:"/api/permissions/delete",permissionsReqData:T,setPermissionsReqData:j})]}),o.jsx(W,{sx:{padding:"20px 0px",position:"sticky",zIndex:1e3,bottom:0,left:0,right:0,backgroundColor:"#FFF"},children:o.jsxs(x,{flexDirection:"row",gap:2,alignItems:"center",children:[o.jsx(se,{onClick:()=>{r()},sx:{width:"100%",height:"60px",backgroundColor:"#E4EBF2",color:"#373B50",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",gap:"10px"},children:n("core:cancel")}),o.jsx(se,{type:"submit",sx:{width:"100%",height:"60px",backgroundColor:"#13B272",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",gap:"10px"},children:n("core:next")})]})})]})})})}),o.jsx(Zo,{value:d,index:1,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Up,{url:(z=t==null?void 0:t.service_form)!=null&&z.id?`${Jr}v2/owner/entity/le_custom_data_service_form_${(F=t==null?void 0:t.service_form)==null?void 0:F.id}/create?iframe=1`:null,builderUrl:b||`${Jr}v2/owner/local_entities/builder/le_custom_data_service_form_${(Z=t==null?void 0:t.service_form)==null?void 0:Z.id}?redirect=${Jr}v2%2Fowner%2Fentity%2Fservice_form%2F${(I=t==null?void 0:t.service_form)==null?void 0:I.id}%2Fshow&related_entity=service_form`,onCancel:r})})})]})},ss=({initialData:e=null,selectedService:t,onClose:r})=>o.jsx(Lp,{onClose:r,initialData:e,selectedService:t}),Wp=({serviceId:e,selectedService:t,...r})=>{var i;const{data:n}=Qs((i=t==null?void 0:t.service_form)==null?void 0:i.id);return o.jsx(ss,{initialData:n,selectedService:t,...r})};function Hp({onClose:e,...t}){var n,i,s,a;const{t:r}=oe();return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:t.serviceId?o.jsxs(x,{children:[o.jsx(M,{color:"#202124",fontSize:"20px",fontWeight:500,pt:"24px",children:`${(n=t==null?void 0:t.selectedService)==null?void 0:n.name}  ${r("core:serviceForm")}`}),o.jsxs(M,{fontSize:"14px",fontWeight:400,color:"#75799D",children:["What additional information do you need to gather when booking ",(i=t==null?void 0:t.selectedService)==null?void 0:i.name,"?"]})," "]}):r("core:serviceForm")}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:(a=(s=t.selectedService)==null?void 0:s.service_form)!=null&&a.id?o.jsx(Wp,{serviceId:t.serviceId,selectedService:t.selectedService,onClose:e,...t}):o.jsx(ss,{onClose:e,...t})})]})}function qp(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Hp,{...e})})})})}const Kp=({openFilters:e,clearAllFilters:t,filterAssignedEmployees:r,filterStatus:n,handleServicesSearch:i,filtersApplied:s})=>{const{t:a}=oe(),[c,l]=v.useState(""),p=Ae("(min-width:993px)"),[f]=ht(c,500);return v.useEffect(()=>{i(f)},[f,i]),o.jsxs(x,{flexDirection:"row",alignItems:"center",justifyContent:"space-between",sx:{marginBottom:"20px"},children:[o.jsxs(x,{flexDirection:"row",alignItems:"center",height:"42px",sx:{border:"1px solid #E4EBF2",borderRadius:"2px",width:"70%"},children:[o.jsx(ze,{placeholder:"Search",fullWidth:!0,startAdornment:o.jsx(H,{icon:"mdi:magnify",width:24,height:24,color:"#9EA1BA",style:{marginInlineEnd:"8px"}}),inputProps:{"aria-label":"search"},sx:{border:"1px solid #E4EBF2",padding:"0px 16px",height:"42px",backgroundColor:"#fff"},value:c,onChange:u=>l(u.target.value),onKeyDown:u=>{u.key==="Enter"&&(u.preventDefault(),i(c))}}),o.jsxs(x,{flexDirection:"row",gap:1,alignItems:"center",sx:{height:"100%",paddingInline:p?"24px":"10px",borderStart:"1px solid #E4EBF2",cursor:"pointer",color:"#75799D",fontSize:"14px","&:hover":{backgroundColor:"#E4EBF2"}},onClick:()=>{e()},children:[o.jsx(H,{icon:"mdi:tune",width:24,color:"#75799D"}),p?a("core:filter"):""," ",s>0?o.jsx(x,{alignItems:"center",justifyContent:"center",sx:{backgroundColor:"#1877F2",borderRadius:"50%",padding:"5px",color:"#FFF",fontSize:14,width:"20px",height:"20px",fontWeight:500},children:s}):null]})]}),o.jsxs(x,{flexDirection:"row",alignItems:"center",justifyContent:"flex-end",height:"42px",sx:{borderRadius:"2px",width:"30%",gap:2},children:[o.jsx(se,{sx:{display:p?"flex":"none",alignItems:"center",gap:1,height:"100%",paddingInline:p?"24px":"10px",border:"1px solid #E4EBF2",cursor:"pointer",color:"#75799D",fontSize:"14px","&:hover":{backgroundColor:"#E4EBF2"}},onClick:()=>{l(""),t()},children:a("core:clearAll")}),o.jsx(x,{flexDirection:"row",gap:1,alignItems:"center",sx:{height:"100%",paddingInline:p?"24px":"10px",border:"1px solid #E4EBF2",cursor:"pointer",backgroundColor:"#E4EBF2",color:"#202124",fontSize:"14px","&:hover":{backgroundColor:"#B0C2D4"}},onClick:()=>{i(c)},children:a("core:search")})]})]})},Qp=we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",width:"100%",height:"50px",transition:"all 0.2s ease",color:"#213242",backgroundColor:"#FFF",border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",flex:1,paddingInlineEnd:"35px !important"},"&:hover":{backgroundColor:"#E4EBF2"},".MuiSelect-icon":{color:"#213242"}})),Yp=({onClose:e,filterAssignedEmployees:t,setFilterAssignedEmployees:r,filterStatus:n,setFilterStatus:i,filtersApplied:s})=>{const{t:a}=oe();return o.jsxs(x,{sx:{width:"100%",height:"100%",p:"20px"},gap:"30px",children:[o.jsxs(x,{width:"100%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:a("core:assignedEmployees")}),o.jsx(ns,{value:t,onChange:r})]}),o.jsxs(x,{width:"100%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:a("core:status")}),o.jsxs(Qp,{inputProps:{"aria-label":"Without label"},IconComponent:Ie,MenuProps:{disableScrollLock:!0},value:n,onChange:c=>i(c.target.value),children:[o.jsx(ne,{value:"-1",sx:{height:"70px"},children:a("core:all")}),o.jsx(ne,{value:"0",sx:{height:"70px"},children:a("core:active")}),o.jsx(ne,{value:"1",sx:{height:"70px"},children:a("core:inactive")})]})]}),o.jsx(gt,{children:o.jsx(W,{sx:{padding:"20px",position:"absolute",bottom:0,width:"100%",backgroundColor:"#fff",borderTop:"1px solid #E4EBF2",left:0,right:0},children:o.jsxs(x,{direction:"row",justifyContent:"space-between",alignItems:"center",gap:"10px",children:[o.jsxs(M,{variant:"body2",sx:{color:"#5F6368",fontSize:"14px"},children:[s||0," ",a("core:filters_applied")]}),o.jsx(se,{variant:"outlined",sx:{color:"#1877F2",fontWeight:600,border:0,"&:hover":{backgroundColor:"#E4EBF2"}},onClick:()=>{r([]),i(-1)},children:a("core:clear_all")})]})})})]})},Gp=({onClose:e,filterAssignedEmployees:t,setFilterAssignedEmployees:r,filterStatus:n,setFilterStatus:i,filtersApplied:s})=>o.jsx(Yp,{onClose:e,filterAssignedEmployees:t,setFilterAssignedEmployees:r,filterStatus:n,setFilterStatus:i,filtersApplied:s});function Jp({onClose:e,...t}){return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:"Services Filters"}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Gp,{onClose:e,...t})})]})}function Xp(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Jp,{...e})})})})}const ef=Pt({name:Ve().min(1,"Name is required"),parent_id:Je().nullable().optional(),description:Ve().optional()}),tf=({categoriesList:e,initialData:t,onClose:r,onOpenServiceDrawer:n,setSelectedCategoryId:i})=>{var A;const{control:s,handleSubmit:a,formState:{errors:c},setValue:l,register:p,reset:f}=Fr({resolver:Rr(ef),defaultValues:{name:"",parent_id:null,description:""}}),u=Le(),{t:d}=oe(),{mutate:g}=Ys(),[b,_]=v.useState(null);v.useEffect(()=>{f(t?{name:t.name,parent_id:t.parent_id,description:t.description}:{name:"",parent_id:null,description:""})},[t,f]);const k=async y=>{const T={...y,category_type:1,attachments:b};try{await g({input:T},{onSuccess:async j=>{var w,C,$;console.log("response?.Category?.id",(w=j==null?void 0:j.Category)==null?void 0:w.id),i(($=(C=j==null?void 0:j.Category)==null?void 0:C.id)==null?void 0:$.toString()),n(),re((j==null?void 0:j.message)||"Category added successfully"),u.invalidateQueries({queryKey:["categories"]}),r()},onError:j=>{re(j.message,{variant:"error"})}})}catch(j){re(j.message,{variant:"error"})}};return o.jsx(W,{px:"20px",sx:{height:"100%",paddingBottom:"100px",overflowY:"auto"},children:o.jsxs("form",{onSubmit:a(k),children:[o.jsx(qt,{children:o.jsxs(x,{gap:2,children:[o.jsx(x,{flexDirection:"row",gap:2,children:o.jsxs(x,{width:"100%",children:[o.jsxs(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:[d("core:name")," ",o.jsx(Xe,{children:"*"})]}),o.jsx(ze,{...p("name"),autoFocus:!0,placeholder:d("core:egSummerCategory"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",height:"50px",backgroundColor:"#fff"}}),c.name&&o.jsx(M,{color:"error",fontSize:12,children:(A=c.name.message)==null?void 0:A.toString()})]})}),o.jsxs(x,{children:[o.jsx(M,{sx:{fontSize:"16px",color:"#5F6368"},mb:1,children:d("core:image")}),o.jsx(Dn,{setImageId:_,url:"/v2/api/entity/upload_file/category/categories.image"})]}),o.jsx(x,{flexDirection:"row",gap:2,children:o.jsxs(x,{width:"100%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:d("core:description")}),o.jsx(Ue,{control:s,name:"description",render:({field:y})=>o.jsx(ci,{minRows:3,placeholder:d("core:placeholder.description"),style:{width:"100%",padding:"10px",border:"1px solid #E4EBF2",color:"#212B36",borderRadius:"5px",fontFamily:"roboto",fontSize:"1rem",backgroundColor:"#FFF"},value:y.value,onChange:y.onChange})})]})})]})}),o.jsx(gt,{children:o.jsx(W,{sx:{padding:"20px",position:"absolute",bottom:0,left:0,right:0,backgroundColor:"#FFF"},children:o.jsxs(x,{flexDirection:"row",gap:2,alignItems:"center",children:[o.jsx(se,{onClick:()=>{r()},sx:{width:"100%",height:"60px",backgroundColor:"#E4EBF2",color:"#373B50",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",gap:"10px"},children:d("core:cancel_capital")}),o.jsx(se,{type:"submit",sx:{width:"100%",height:"60px",backgroundColor:"#13B272",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",fontWeight:600,gap:"10px"},children:d("core:save")})]})})})]})})},as=({initialData:e=null,onClose:t,onOpenServiceDrawer:r,setSelectedCategoryId:n})=>{const{data:i}=ii(),{data:s}=si();return o.jsx(tf,{onClose:t,categoriesList:s,taxes:i,initialData:e,onOpenServiceDrawer:r,setSelectedCategoryId:n})},rf=({categoryId:e,...t})=>o.jsx(as,{initialData:{},...t});function nf({onClose:e,...t}){return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:t.categoryId?"Edit Category":"Add New Category"}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:t.categoryId?o.jsx(rf,{categoryId:t.categoryId,onClose:e,...t}):o.jsx(as,{onClose:e,...t})})]})}function of(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(nf,{...e})})})})}function sf(e){if(Array.isArray(e))return e}function af(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,s,a,c=[],l=!0,p=!1;try{if(s=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=s.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(f){p=!0,i=f}finally{try{if(!l&&r.return!=null&&(a=r.return(),Object(a)!==a))return}finally{if(p)throw i}}return c}}function fn(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function cs(e,t){if(e){if(typeof e=="string")return fn(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fn(e,t):void 0}}function cf(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rn(e,t){return sf(e)||af(e,t)||cs(e,t)||cf()}var Xr={},It={},Vo;function ls(){if(Vo)return It;Vo=1,Object.defineProperty(It,"__esModule",{value:!0}),It.bind=void 0;function e(t,r){var n=r.type,i=r.listener,s=r.options;return t.addEventListener(n,i,s),function(){t.removeEventListener(n,i,s)}}return It.bind=e,It}var lt={},No;function lf(){if(No)return lt;No=1;var e=lt&&lt.__assign||function(){return e=Object.assign||function(s){for(var a,c=1,l=arguments.length;c<l;c++){a=arguments[c];for(var p in a)Object.prototype.hasOwnProperty.call(a,p)&&(s[p]=a[p])}return s},e.apply(this,arguments)};Object.defineProperty(lt,"__esModule",{value:!0}),lt.bindAll=void 0;var t=ls();function r(s){if(!(typeof s>"u"))return typeof s=="boolean"?{capture:s}:s}function n(s,a){if(a==null)return s;var c=e(e({},s),{options:e(e({},r(a)),r(s.options))});return c}function i(s,a,c){var l=a.map(function(p){var f=n(p,c);return(0,t.bind)(s,f)});return function(){l.forEach(function(f){return f()})}}return lt.bindAll=i,lt}var Uo;function uf(){return Uo||(Uo=1,(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.bindAll=e.bind=void 0;var t=ls();Object.defineProperty(e,"bind",{enumerable:!0,get:function(){return t.bind}});var r=lf();Object.defineProperty(e,"bindAll",{enumerable:!0,get:function(){return r.bindAll}})})(Xr)),Xr}var _t=uf(),us="data-pdnd-honey-pot";function ds(e){return e instanceof Element&&e.hasAttribute(us)}function ps(e){var t=document.elementsFromPoint(e.x,e.y),r=Rn(t,2),n=r[0],i=r[1];return n?ds(n)?i??null:n:null}function Mt(e){"@babel/helpers - typeof";return Mt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mt(e)}function df(e,t){if(Mt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function pf(e){var t=df(e,"string");return Mt(t)=="symbol"?t:t+""}function On(e,t,r){return(t=pf(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ff=2147483647;function Lo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lo(Object(r),!0).forEach(function(n){On(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lo(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}var Zt=2,Ho=Zt/2;function hf(e){return{x:Math.floor(e.x),y:Math.floor(e.y)}}function gf(e){return{x:e.x-Ho,y:e.y-Ho}}function xf(e){return{x:Math.max(e.x,0),y:Math.max(e.y,0)}}function mf(e){return{x:Math.min(e.x,window.innerWidth-Zt),y:Math.min(e.y,window.innerHeight-Zt)}}function qo(e){var t=e.client,r=mf(xf(gf(hf(t))));return DOMRect.fromRect({x:r.x,y:r.y,width:Zt,height:Zt})}function Ko(e){var t=e.clientRect;return{left:"".concat(t.left,"px"),top:"".concat(t.top,"px"),width:"".concat(t.width,"px"),height:"".concat(t.height,"px")}}function vf(e){var t=e.client,r=e.clientRect;return t.x>=r.x&&t.x<=r.x+r.width&&t.y>=r.y&&t.y<=r.y+r.height}function yf(e){var t=e.initial,r=document.createElement("div");r.setAttribute(us,"true");var n=qo({client:t});Object.assign(r.style,Wo(Wo({backgroundColor:"transparent",position:"fixed",padding:0,margin:0,boxSizing:"border-box"},Ko({clientRect:n})),{},{pointerEvents:"auto",zIndex:ff})),document.body.appendChild(r);var i=_t.bind(window,{type:"pointermove",listener:function(a){var c={x:a.clientX,y:a.clientY};n=qo({client:c}),Object.assign(r.style,Ko({clientRect:n}))},options:{capture:!0}});return function(a){var c=a.current;if(i(),vf({client:c,clientRect:n})){r.remove();return}function l(){p(),r.remove()}var p=_t.bindAll(window,[{type:"pointerdown",listener:l},{type:"pointermove",listener:l},{type:"focusin",listener:l},{type:"focusout",listener:l},{type:"dragstart",listener:l},{type:"dragenter",listener:l},{type:"dragover",listener:l}],{capture:!0})}}function bf(){var e=null;function t(){return e=null,_t.bind(window,{type:"pointermove",listener:function(i){e={x:i.clientX,y:i.clientY}},options:{capture:!0}})}function r(){var n=null;return function(s){var a=s.eventName,c=s.payload;if(a==="onDragStart"){var l=c.location.initial.input,p=e??{x:l.clientX,y:l.clientY};n=yf({initial:p})}if(a==="onDrop"){var f,u=c.location.current.input;(f=n)===null||f===void 0||f({current:{x:u.clientX,y:u.clientY}}),n=null,e=null}}}return{bindEvents:t,getOnPostDispatch:r}}function jf(e){if(Array.isArray(e))return fn(e)}function wf(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _f(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fs(e){return jf(e)||wf(e)||cs(e)||_f()}function Ft(e){var t=null;return function(){if(!t){for(var n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];var a=e.apply(this,i);t={result:a}}return t.result}}var kf=Ft(function(){return navigator.userAgent.includes("Firefox")}),$n=Ft(function(){var t=navigator,r=t.userAgent;return r.includes("AppleWebKit")&&!r.includes("Chrome")}),hn={isLeavingWindow:Symbol("leaving"),isEnteringWindow:Symbol("entering")};function Cf(e){var t=e.dragLeave;return $n()?t.hasOwnProperty(hn.isLeavingWindow):!1}(function(){if(typeof window>"u"||!$n())return;function t(){return{enterCount:0,isOverWindow:!1}}var r=t();function n(){r=t()}_t.bindAll(window,[{type:"dragstart",listener:function(){r.enterCount=0,r.isOverWindow=!0}},{type:"drop",listener:n},{type:"dragend",listener:n},{type:"dragenter",listener:function(s){!r.isOverWindow&&r.enterCount===0&&(s[hn.isEnteringWindow]=!0),r.isOverWindow=!0,r.enterCount++}},{type:"dragleave",listener:function(s){r.enterCount--,r.isOverWindow&&r.enterCount===0&&(s[hn.isLeavingWindow]=!0,r.isOverWindow=!1)}}],{capture:!0})})();function Ff(e){return"nodeName"in e}function Ef(e){return Ff(e)&&e.ownerDocument!==document}function Sf(e){var t=e.dragLeave,r=t.type,n=t.relatedTarget;return r!=="dragleave"?!1:$n()?Cf({dragLeave:t}):n==null?!0:kf()?Ef(n):n instanceof HTMLIFrameElement}function If(e){var t=e.onDragEnd;return[{type:"pointermove",listener:(function(){var r=0;return function(){if(r<20){r++;return}t()}})()},{type:"pointerdown",listener:t}]}function Rt(e){return{altKey:e.altKey,button:e.button,buttons:e.buttons,ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey,clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY}}var zf=function(t){var r=[],n=null,i=function(){for(var a=arguments.length,c=new Array(a),l=0;l<a;l++)c[l]=arguments[l];r=c,!n&&(n=requestAnimationFrame(function(){n=null,t.apply(void 0,r)}))};return i.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},i},en=zf(function(e){return e()}),nr=(function(){var e=null;function t(n){var i=requestAnimationFrame(function(){e=null,n()});e={frameId:i,fn:n}}function r(){e&&(cancelAnimationFrame(e.frameId),e.fn(),e=null)}return{schedule:t,flush:r}})();function Af(e){var t=e.source,r=e.initial,n=e.dispatchEvent,i={dropTargets:[]};function s(c){n(c),i={dropTargets:c.payload.location.current.dropTargets}}var a={start:function(l){var p=l.nativeSetDragImage,f={current:r,previous:i,initial:r};s({eventName:"onGenerateDragPreview",payload:{source:t,location:f,nativeSetDragImage:p}}),nr.schedule(function(){s({eventName:"onDragStart",payload:{source:t,location:f}})})},dragUpdate:function(l){var p=l.current;nr.flush(),en.cancel(),s({eventName:"onDropTargetChange",payload:{source:t,location:{initial:r,previous:i,current:p}}})},drag:function(l){var p=l.current;en(function(){nr.flush();var f={initial:r,previous:i,current:p};s({eventName:"onDrag",payload:{source:t,location:f}})})},drop:function(l){var p=l.current,f=l.updatedSourcePayload;nr.flush(),en.cancel(),s({eventName:"onDrop",payload:{source:f??t,location:{current:p,previous:i,initial:r}}})}};return a}var gn={isActive:!1};function hs(){return!gn.isActive}function Df(e){return e.dataTransfer?e.dataTransfer.setDragImage.bind(e.dataTransfer):null}function Tf(e){var t=e.current,r=e.next;if(t.length!==r.length)return!0;for(var n=0;n<t.length;n++)if(t[n].element!==r[n].element)return!0;return!1}function Rf(e){var t=e.event,r=e.dragType,n=e.getDropTargetsOver,i=e.dispatchEvent;if(!hs())return;var s=Of({event:t,dragType:r,getDropTargetsOver:n});gn.isActive=!0;var a={current:s};tn({event:t,current:s.dropTargets});var c=Af({source:r.payload,dispatchEvent:i,initial:s});function l(g){var b=Tf({current:a.current.dropTargets,next:g.dropTargets});a.current=g,b&&c.dragUpdate({current:a.current})}function p(g){var b=Rt(g),_=ds(g.target)?ps({x:b.clientX,y:b.clientY}):g.target,k=n({target:_,input:b,source:r.payload,current:a.current.dropTargets});k.length&&(g.preventDefault(),tn({event:g,current:k})),l({dropTargets:k,input:b})}function f(){a.current.dropTargets.length&&l({dropTargets:[],input:a.current.input}),c.drop({current:a.current,updatedSourcePayload:null}),u()}function u(){gn.isActive=!1,d()}var d=_t.bindAll(window,[{type:"dragover",listener:function(b){p(b),c.drag({current:a.current})}},{type:"dragenter",listener:p},{type:"dragleave",listener:function(b){Sf({dragLeave:b})&&(l({input:a.current.input,dropTargets:[]}),r.startedFrom==="external"&&f())}},{type:"drop",listener:function(b){if(a.current={dropTargets:a.current.dropTargets,input:Rt(b)},!a.current.dropTargets.length){f();return}b.preventDefault(),tn({event:b,current:a.current.dropTargets}),c.drop({current:a.current,updatedSourcePayload:r.type==="external"?r.getDropPayload(b):null}),u()}},{type:"dragend",listener:function(b){a.current={dropTargets:a.current.dropTargets,input:Rt(b)},f()}}].concat(fs(If({onDragEnd:f}))),{capture:!0});c.start({nativeSetDragImage:Df(t)})}function tn(e){var t,r=e.event,n=e.current,i=(t=n[0])===null||t===void 0?void 0:t.dropEffect;i!=null&&r.dataTransfer&&(r.dataTransfer.dropEffect=i)}function Of(e){var t=e.event,r=e.dragType,n=e.getDropTargetsOver,i=Rt(t);if(r.startedFrom==="external")return{input:i,dropTargets:[]};var s=n({input:i,source:r.payload,target:t.target,current:[]});return{input:i,dropTargets:s}}var Qo={canStart:hs,start:Rf},xn=new Map;function $f(e){var t=e.typeKey,r=e.mount,n=xn.get(t);if(n)return n.usageCount++,n;var i={typeKey:t,unmount:r(),usageCount:1};return xn.set(t,i),i}function Bf(e){var t=$f(e);return function(){t.usageCount--,!(t.usageCount>0)&&(t.unmount(),xn.delete(e.typeKey))}}function Bn(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(){t.forEach(function(i){return i()})}}function gs(e,t){var r=t.attribute,n=t.value;return e.setAttribute(r,n),function(){return e.removeAttribute(r)}}function Yo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ge(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yo(Object(r),!0).forEach(function(n){On(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yo(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rn(e,t){var r=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Pf(e))||t){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(p){throw p},f:i}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var p=r.next();return a=p.done,p},e:function(p){c=!0,s=p},f:function(){try{a||r.return==null||r.return()}finally{if(c)throw s}}}}function Pf(e,t){if(e){if(typeof e=="string")return Go(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Go(e,t):void 0}}function Go(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nn(e){return e.slice(0).reverse()}function Mf(e){var t=e.typeKey,r=e.defaultDropEffect,n=new WeakMap,i="data-drop-target-for-".concat(t),s="[".concat(i,"]");function a(g){return n.set(g.element,g),function(){return n.delete(g.element)}}function c(g){var b=Bn(gs(g.element,{attribute:i,value:"true"}),a(g));return Ft(b)}function l(g){var b,_,k,A,y=g.source,T=g.target,j=g.input,w=g.result,C=w===void 0?[]:w;if(T==null)return C;if(!(T instanceof Element))return T instanceof Node?l({source:y,target:T.parentElement,input:j,result:C}):C;var $=T.closest(s);if($==null)return C;var R=n.get($);if(R==null)return C;var z={input:j,source:y,element:R.element};if(R.canDrop&&!R.canDrop(z))return l({source:y,target:R.element.parentElement,input:j,result:C});var F=(b=(_=R.getData)===null||_===void 0?void 0:_.call(R,z))!==null&&b!==void 0?b:{},Z=(k=(A=R.getDropEffect)===null||A===void 0?void 0:A.call(R,z))!==null&&k!==void 0?k:r,I={data:F,element:R.element,dropEffect:Z,isActiveDueToStickiness:!1};return l({source:y,target:R.element.parentElement,input:j,result:[].concat(fs(C),[I])})}function p(g){var b=g.eventName,_=g.payload,k=rn(_.location.current.dropTargets),A;try{for(k.s();!(A=k.n()).done;){var y,T=A.value,j=n.get(T.element),w=Ge(Ge({},_),{},{self:T});j==null||(y=j[b])===null||y===void 0||y.call(j,w)}}catch(C){k.e(C)}finally{k.f()}}var f={onGenerateDragPreview:p,onDrag:p,onDragStart:p,onDrop:p,onDropTargetChange:function(b){var _=b.payload,k=new Set(_.location.current.dropTargets.map(function(N){return N.element})),A=new Set,y=rn(_.location.previous.dropTargets),T;try{for(y.s();!(T=y.n()).done;){var j,w=T.value;A.add(w.element);var C=n.get(w.element),$=k.has(w.element),R=Ge(Ge({},_),{},{self:w});if(C==null||(j=C.onDropTargetChange)===null||j===void 0||j.call(C,R),!$){var z;C==null||(z=C.onDragLeave)===null||z===void 0||z.call(C,R)}}}catch(N){y.e(N)}finally{y.f()}var F=rn(_.location.current.dropTargets),Z;try{for(F.s();!(Z=F.n()).done;){var I,D,U=Z.value;if(!A.has(U.element)){var X=Ge(Ge({},_),{},{self:U}),O=n.get(U.element);O==null||(I=O.onDropTargetChange)===null||I===void 0||I.call(O,X),O==null||(D=O.onDragEnter)===null||D===void 0||D.call(O,X)}}}catch(N){F.e(N)}finally{F.f()}}};function u(g){f[g.eventName](g)}function d(g){var b=g.source,_=g.target,k=g.input,A=g.current,y=l({source:b,target:_,input:k});if(y.length>=A.length)return y;for(var T=nn(A),j=nn(y),w=[],C=0;C<T.length;C++){var $,R=T[C],z=j[C];if(z!=null){w.push(z);continue}var F=w[C-1],Z=T[C-1];if((F==null?void 0:F.element)!==(Z==null?void 0:Z.element))break;var I=n.get(R.element);if(!I)break;var D={input:k,source:b,element:I.element};if(I.canDrop&&!I.canDrop(D)||!(($=I.getIsSticky)!==null&&$!==void 0&&$.call(I,D)))break;w.push(Ge(Ge({},R),{},{isActiveDueToStickiness:!0}))}return nn(w)}return{dropTargetForConsumers:c,getIsOver:d,dispatchEvent:u}}function Zf(e,t){var r=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Vf(e))||t){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(p){throw p},f:i}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var p=r.next();return a=p.done,p},e:function(p){c=!0,s=p},f:function(){try{a||r.return==null||r.return()}finally{if(c)throw s}}}}function Vf(e,t){if(e){if(typeof e=="string")return Jo(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Jo(e,t):void 0}}function Jo(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function Xo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Nf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xo(Object(r),!0).forEach(function(n){On(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xo(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Uf(){var e=new Set,t=null;function r(s){t&&(!s.canMonitor||s.canMonitor(t.canMonitorArgs))&&t.active.add(s)}function n(s){var a=Nf({},s);e.add(a),r(a);function c(){e.delete(a),t&&t.active.delete(a)}return Ft(c)}function i(s){var a=s.eventName,c=s.payload;if(a==="onGenerateDragPreview"){t={canMonitorArgs:{initial:c.location.initial,source:c.source},active:new Set};var l=Zf(e),p;try{for(l.s();!(p=l.n()).done;){var f=p.value;r(f)}}catch(k){l.e(k)}finally{l.f()}}if(t){for(var u=Array.from(t.active),d=0,g=u;d<g.length;d++){var b=g[d];if(t.active.has(b)){var _;(_=b[a])===null||_===void 0||_.call(b,c)}}a==="onDrop"&&(t.active.clear(),t=null)}}return{dispatchEvent:i,monitorForConsumers:n}}function Lf(e){var t=e.typeKey,r=e.mount,n=e.dispatchEventToSource,i=e.onPostDispatch,s=e.defaultDropEffect,a=Uf(),c=Mf({typeKey:t,defaultDropEffect:s});function l(u){n==null||n(u),c.dispatchEvent(u),a.dispatchEvent(u),i==null||i(u)}function p(u){var d=u.event,g=u.dragType;Qo.start({event:d,dragType:g,getDropTargetsOver:c.getIsOver,dispatchEvent:l})}function f(){function u(){var d={canStart:Qo.canStart,start:p};return r(d)}return Bf({typeKey:t,mount:u})}return{registerUsage:f,dropTarget:c.dropTargetForConsumers,monitor:a.monitorForConsumers}}var Wf=Ft(function(){return navigator.userAgent.toLocaleLowerCase().includes("android")}),Hf="pdnd:android-fallback",ei="text/plain",qf="text/uri-list",Kf="application/vnd.pdnd",xr=new WeakMap;function Qf(e){return xr.set(e.element,e),function(){xr.delete(e.element)}}var ti=bf(),xs=Lf({typeKey:"element",defaultDropEffect:"move",mount:function(t){return Bn(ti.bindEvents(),_t.bind(document,{type:"dragstart",listener:function(n){var i,s,a,c,l,p;if(t.canStart(n)&&!n.defaultPrevented&&n.dataTransfer){var f=n.target;if(f instanceof HTMLElement){var u=xr.get(f);if(u){var d=Rt(n),g={element:u.element,dragHandle:(i=u.dragHandle)!==null&&i!==void 0?i:null,input:d};if(u.canDrag&&!u.canDrag(g)){n.preventDefault();return}if(u.dragHandle){var b=ps({x:d.clientX,y:d.clientY});if(!u.dragHandle.contains(b)){n.preventDefault();return}}var _=(s=(a=u.getInitialDataForExternal)===null||a===void 0?void 0:a.call(u,g))!==null&&s!==void 0?s:null;if(_)for(var k=0,A=Object.entries(_);k<A.length;k++){var y=Rn(A[k],2),T=y[0],j=y[1];n.dataTransfer.setData(T,j??"")}Wf()&&!n.dataTransfer.types.includes(ei)&&!n.dataTransfer.types.includes(qf)&&n.dataTransfer.setData(ei,Hf),n.dataTransfer.setData(Kf,"");var w={element:u.element,dragHandle:(c=u.dragHandle)!==null&&c!==void 0?c:null,data:(l=(p=u.getInitialData)===null||p===void 0?void 0:p.call(u,g))!==null&&l!==void 0?l:{}},C={type:"element",payload:w,startedFrom:"internal"};t.start({event:n,dragType:C})}}}}}))},dispatchEventToSource:function(t){var r,n,i=t.eventName,s=t.payload;(r=xr.get(s.source.element))===null||r===void 0||(n=r[i])===null||n===void 0||n.call(r,s)},onPostDispatch:ti.getOnPostDispatch()}),mr=xs.dropTarget;function Pn(e){var t=Bn(xs.registerUsage(),Qf(e),gs(e.element,{attribute:"draggable",value:"true"}));return Ft(t)}function Yf(e){var t=e.list,r=e.startIndex,n=e.finishIndex;if(r===-1||n===-1)return Array.from(t);var i=Array.from(t),s=i.splice(r,1),a=Rn(s,1),c=a[0];return i.splice(n,0,c),i}const Gf=Ht(o.jsx("path",{d:"M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2m-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2m3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1s3.1 1.39 3.1 3.1z"}),"Lock");function Jf({categoriesList:e,categoriesWithServices:t,servicesList:r,onOpenCategoryDrawer:n,setSelectedCategoryId:i}){const{t:s}=oe(),[a,c]=v.useState(e),[l,p]=v.useState(null),f=v.useRef({}),{mutate:u}=Gs(),d=Le(),g=Ae("(min-width:993px)");v.useEffect(()=>{c(e)},[e]);const b=v.useCallback(_=>k=>{f.current[_]=k},[]);return v.useEffect(()=>{const _=[];for(const k of a){const A=f.current[k.id];A&&(_.push(mr({element:A,getData:()=>({id:k.id}),onDragEnter:()=>p(k.id),onDragLeave:()=>p(y=>y===k.id?null:y),onDrop:({source:y,location:T})=>{var I,D,U,X,O;p(null),console.log("DROP source:",y),console.log("DROP location:",T,(D=(I=T==null?void 0:T.current)==null?void 0:I.dropTargets[0])==null?void 0:D.data.id);const j=(U=y==null?void 0:y.data)==null?void 0:U.id,w=(O=(X=T==null?void 0:T.current)==null?void 0:X.dropTargets[0])==null?void 0:O.data.id;if(!j||!w||j===w)return;const C=a.filter(N=>!N.locked),$=C.findIndex(N=>N.id===j),R=C.findIndex(N=>N.id===w);if($===-1||R===-1)return;const z=Yf({list:C,startIndex:$,finishIndex:R}),F=[...a.filter(N=>N.locked),...z],Z=[];for(const N of F)Z.push(Number(N.id));try{u({input:Z},{onSuccess:async N=>{re(N.message),d.invalidateQueries({queryKey:["categoriesWithServices"]})},onError:N=>{re(N.message,{variant:"error"})}})}catch(N){re(N.message,{variant:"error"})}c(F)}})),k.locked||_.push(Pn({element:A,getInitialData:()=>({id:k.id})})))}return()=>_.forEach(k=>k())},[a]),o.jsxs(W,{sx:{width:g?200:"100%",display:"flex",flexDirection:"column"},children:[o.jsxs(x,{flexDirection:"row",sx:{px:g?2:0,py:1,border:"1px solid #1877F2",backgroundColor:"#F6F9FC",justifyContent:"space-between",alignItems:"center",padding:g?"16px 15px":"12px 10px",transition:"all 0.15s ease-in-out"},children:[o.jsxs(x,{direction:"row",alignItems:"center",gap:1,children:[o.jsx(H,{icon:"mdi:lock",width:"20px",color:"#75799D",opacity:.5}),o.jsx(M,{color:"#1877F2",children:s("core:all-categories")})]}),o.jsxs(M,{variant:"body2",color:"#1877F2",children:["(",(r==null?void 0:r.length)||0,")"]})]}),a.map(_=>{var A,y;const k=l===_.id;return o.jsx(W,{children:((A=t[_==null?void 0:_.id])==null?void 0:A.length)&&o.jsxs(x,{ref:b(_.id),flexDirection:"row",sx:{px:g?2:0,py:1,border:k?"2px dashed #3f51b5":"1px solid #DBE3EB",backgroundColor:k?"#e8f0ff":"#FFF",justifyContent:"space-between",alignItems:"center",cursor:_.locked?"default":"grab",padding:g?"16px 15px":"12px 10px",transition:"all 0.15s ease-in-out"},children:[o.jsxs(x,{direction:"row",alignItems:"center",gap:1,children:[!_.locked&&g&&o.jsx(H,{icon:"mdi:drag-vertical",width:"20px",color:"#75799D"}),_.locked&&o.jsx(Gf,{fontSize:"small",color:"primary"}),o.jsx(M,{color:_.locked?"primary":"text.primary",fontWeight:_.locked?600:400,children:_.name})]}),o.jsxs(M,{variant:"body2",color:_.locked?"primary":"text.secondary",children:["(",((y=t[_==null?void 0:_.id])==null?void 0:y.length)||0,")"]})]})},`cat${_.id}`)}),o.jsxs(W,{sx:{px:2,py:1.5,width:"130px",backgroundColor:"#E4EBF2",borderRadius:1,display:"flex",alignItems:"center",gap:1,cursor:"pointer"},onClick:()=>{i(null),n()},children:[o.jsx(H,{icon:"mdi:plus",width:"22px",style:{color:"#17B26A"}}),o.jsx(M,{color:"#202124",fontWeight:500,children:s("core:add-new")})]})]})}const Xf=({onClose:e,serviceId:t,selectedService:r,onOpenServiceDrawer:n,onOpenServiceFormDrawer:i,userPermissions:s})=>{var k,A,y,T,j,w;const{t:a}=oe(),[c,l]=v.useState(null),p=!!c,f=C=>{l(C.currentTarget)},u=()=>{l(null)},d=Ae("(min-width:993px)"),g=Le(),{mutate:b}=Js({onSuccess:C=>{g.invalidateQueries({queryKey:["services"]}),re((C==null?void 0:C.message)||"Service Deleted successfully",{variant:"success"}),e()},onError:C=>{const $=C==null?void 0:C.message;re($,{variant:"error"})}}),_=async()=>{try{b({serviceId:t},{onSuccess:()=>{g.invalidateQueries({queryKey:["categoriesWithServices"]})},onError:C=>{console.log("error",C)}})}catch(C){console.log("error",C)}};return o.jsx(o.Fragment,{children:o.jsxs(W,{sx:{position:d?"relative":"absolute",top:d?0:"10px",right:d?0:"10px"},children:[o.jsx(Ce,{"aria-label":"close",sx:{width:40,height:40,backgroundColor:"#E4EBF2",borderRadius:0,color:"#75799D","&:hover":{backgroundColor:"#E4EBF2"}},disableRipple:!0,onClick:f,children:o.jsx(H,{icon:"mdi:dots-horizontal",width:32,color:"#202124"})}),o.jsxs(Dt,{id:"basic-menu",anchorEl:c,open:p,onClose:u,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{pointerEvents:"auto"},children:[(r==null?void 0:r.service_form_manage_records_link)&&o.jsxs(ne,{sx:{minWidth:180,height:50,color:"#75799D"},component:on,href:r==null?void 0:r.service_form_manage_records_link,target:"_blank",disableRipple:!0,children:[o.jsx(H,{icon:"mdi:folder-multiple",width:22,color:"#1877F2",style:{marginInlineEnd:5}}),a("core:manageRecords")]},"opt2"),((s==null?void 0:s.is_super_admin)||((k=s==null?void 0:s.permissions)==null?void 0:k.includes(806)))&&o.jsxs(ne,{sx:{minWidth:180,height:50,color:"#75799D"},onClick:()=>{i(),u()},disableRipple:!0,children:[o.jsx(H,{icon:"mdi:format-list-group-plus",width:22,color:"#4E5381",style:{marginInlineEnd:5}}),(A=r==null?void 0:r.service_form)!=null&&A.id?a("core:relatedForms"):a("core:addForm")]},"opt3"),(r==null?void 0:r.service_form_manage_printable_templates_link)&&o.jsxs(ne,{sx:{minWidth:180,height:50,color:"#75799D"},component:on,href:r==null?void 0:r.service_form_manage_printable_templates_link,target:"_blank",disableRipple:!0,children:[o.jsx(H,{icon:"mdi:printer",width:22,color:"#13B272",style:{marginInlineEnd:5}}),a("core:managePrintableTemplates")]},"opt4"),((s==null?void 0:s.is_super_admin)||((y=s==null?void 0:s.permissions)==null?void 0:y.includes(29))||((T=s==null?void 0:s.permissions)==null?void 0:T.includes(30)))&&o.jsxs(ne,{sx:{minWidth:180,height:50,color:"#75799D"},onClick:()=>{n(),u()},disableRipple:!0,children:[o.jsx(H,{icon:"mdi:edit",width:22,color:"#1877F2",style:{marginInlineEnd:5}}),a("core:edit")]},"opt5"),((s==null?void 0:s.is_super_admin)||((j=s==null?void 0:s.permissions)==null?void 0:j.includes(29))||((w=s==null?void 0:s.permissions)==null?void 0:w.includes(30)))&&o.jsxs(ne,{sx:{minWidth:180,height:50,color:"#75799D"},onClick:()=>{_(),u()},disableRipple:!0,children:[o.jsx(H,{icon:"mdi:delete",width:22,color:"#ec2121",style:{marginInlineEnd:5}}),a("core:delete")]})]},"opt1")]})})},ms="/v2/js/app/no-records-BIaJTni3.svg",eh=({handleAddService:e})=>{const{t}=oe();return o.jsxs(x,{sx:{width:"100%",height:"100%"},alignItems:"center",justifyContent:"center",flexDirection:"row",gap:"30px",children:[o.jsx(W,{component:"img",src:ms,alt:"No  Records Added Yet",sx:{width:105,height:105}}),o.jsxs(W,{children:[o.jsx(M,{variant:"h6",fontSize:"16px",fontWeight:500,color:"#75799D",children:t("core:no-records-added")}),o.jsx(M,{component:"p",fontSize:"13px",fontWeight:400,color:"#75799D",children:t("core:no-records-subtext-prefix")}),o.jsxs(se,{variant:"text",sx:{marginTop:"10px",color:"#13B272",padding:0,fontSize:"14px",fontWeight:400,"&:hover":{backgroundColor:"transparent"}},onClick:e,children:[o.jsx(H,{icon:"mdi:plus",width:"20",height:"20",color:"#13B272",style:{marginInlineEnd:"3px"}}),t("core:add-service")]})]})]})},th=({searchTerm:e,matches:t,onOpenCategoryDrawer:r,setSelectedCategoryId:n,setSelectedServiceId:i,setSelectedService:s,onOpenServiceDrawer:a,userPermissions:c,onChangeStatus:l,loadingRowId:p,onOpenServiceFormDrawer:f})=>{var C,$,R,z,F;const{data:u}=Xs(e),[d,g]=v.useState(null),[b,_]=v.useState(u==null?void 0:u.servicesByCategory),{mutate:k}=ea(),A=Le(),{data:y}=yr(),T=(y==null?void 0:y.default_currency_formatted)||"",j=({draggedId:Z,targetId:I,targetCategoryId:D,sourceCategoryId:U})=>{const X={...u.servicesByCategory},O=[...X[U]||[]],N=[...X[D]||[]],le=O.findIndex(K=>K.id===Z);if(le===-1)return;const[ee]=O.splice(le,1);if(U===D){const K=N.findIndex(je=>je.id===I);if(K===-1)return;N.splice(le,1);const ie=le<K?K-1:K;N.splice(ie,0,ee)}else if(I){const K=N.findIndex(ie=>ie.id===I);K===-1?N.push(ee):N.splice(K,0,ee)}else N.push(ee);X[U]=O,X[D]=N;const J=O.map(K=>K.id),te=N.map(K=>K.id);let Y={};U===D?Y={[D]:te}:Y={[U]:J,[D]:te};try{k({input:Y},{onSuccess:async K=>{re(K.message),A.invalidateQueries({queryKey:["categoriesWithServices"]})},onError:K=>{re(K.message,{variant:"error"})}})}catch(K){re(K.message,{variant:"error"})}},{t:w}=oe();return o.jsxs(x,{flexDirection:t?"row":"column",gap:2,sx:{height:((C=u==null?void 0:u.servicesList)==null?void 0:C.length)>0?"auto":"100%"},children:[o.jsx(Jf,{categoriesList:u==null?void 0:u.categories,categoriesWithServices:u==null?void 0:u.servicesByCategory,servicesList:u==null?void 0:u.servicesList,onOpenCategoryDrawer:r,setSelectedCategoryId:n}),o.jsxs(x,{width:t?"calc(100% - 200px)":"100%",height:(($=u==null?void 0:u.servicesList)==null?void 0:$.length)>0?"auto":"100%",flexDirection:"column",gap:"16px",children:[((R=u==null?void 0:u.servicesList)==null?void 0:R.length)>=0&&o.jsxs(o.Fragment,{children:[(z=u==null?void 0:u.categories)==null?void 0:z.map(Z=>{var I,D,U,X;return o.jsx(W,{sx:{display:((I=u==null?void 0:u.servicesByCategory[Z.id])==null?void 0:I.length)>0?"block":"none"},ref:O=>{O&&mr({element:O,onDragEnter:({source:N})=>{var le;((le=N==null?void 0:N.data)==null?void 0:le.type)==="SERVICE_ITEM"&&(O.style.border="2px dashed #1976d2")},onDragLeave:()=>{O.style.border="2px dashed transparent"},onDrop:({source:N,location:le})=>{var K;if(le.current.dropTargets.some(ie=>{var je;return((je=ie.data)==null?void 0:je.type)==="SERVICE_ITEM"})||((K=N==null?void 0:N.data)==null?void 0:K.type)!=="SERVICE_ITEM")return;const ee=N.data.categoryId,J=Z.id;if(ee===J)return;if(((u==null?void 0:u.servicesByCategory[J])||[]).some(ie=>ie.id===N.data.id)){re("This service already exists in the target category.",{variant:"error"});return}j({draggedId:N.data.id,targetId:null,targetCategoryId:J,sourceCategoryId:ee})}})},children:((D=u==null?void 0:u.servicesByCategory[Z.id])==null?void 0:D.length)>0?o.jsxs(x,{gap:2,children:[o.jsxs(x,{flexDirection:"row",justifyContent:"space-between",mt:1,children:[o.jsx(M,{color:"#373B50",fontSize:"18px",fontWeight:500,children:Z.name}),((c==null?void 0:c.is_super_admin)||((U=c==null?void 0:c.permissions)==null?void 0:U.includes(26)))&&o.jsxs(x,{flexDirection:"row",alignItems:"center",sx:{fontSize:14,color:"#373B50",fontWeight:500,cursor:"pointer"},onClick:()=>{i(null),s(null),n(Z.id),a()},children:[o.jsx(H,{icon:"mdi:plus",width:"20px",color:"#13B272",style:{marginInlineEnd:"8px"}}),w("core:add-service")]})]}),(X=(u==null?void 0:u.servicesByCategory[Z.id])||[])==null?void 0:X.map(O=>{var N,le,ee;return o.jsxs(x,{flexDirection:t?"row":"column",justifyContent:"space-between",p:"10px",sx:{position:"relative",borderRadius:"2px",backgroundColor:d===O.id?"#F0F9FF":"#FFF",boxShadow:d===O.id?"0 0 0 2px #1976d2 inset":"0px 4px 16px 0px rgba(0, 0, 0, 0.04)",cursor:"grab",border:d===O.id?"2px dashed #3f51b5":"1px solid #DBE3EB",transition:"all 0.2s ease-in-out"},ref:J=>{J&&(Pn({element:J,getInitialData:()=>({type:"SERVICE_ITEM",id:O.id,categoryId:Z.id})}),mr({element:J,onDragEnter:()=>{g(O.id)},onDragLeave:()=>{g(null)},onDrop:({source:te})=>{var Y;if(g(null),((Y=te==null?void 0:te.data)==null?void 0:Y.type)==="SERVICE_ITEM"&&te.data.id!==O.id){const K=te.data.categoryId,ie=Z.id;if(K!==ie&&((u==null?void 0:u.servicesByCategory[ie])||[]).some(ke=>ke.id===te.data.id)){re("This service already exists in the target category.",{variant:"error"});return}j({draggedId:te.data.id,targetId:O.id,targetCategoryId:ie,sourceCategoryId:K})}}}))},children:[o.jsx(x,{children:o.jsxs(x,{flexDirection:"row",gap:"12px",children:[t&&o.jsx(x,{sx:{alignItems:"center",height:"100%",justifyContent:"center",cursor:"grab"},children:o.jsx(H,{icon:"mdi:drag-vertical",width:"20px",height:"20px",color:"#75799D"})}),t&&o.jsx(x,{sx:{width:"62px",height:"62px",alignItems:"center",justifyContent:"center",borderRadius:"2px",overflow:"hidden"},children:O.photo?o.jsx("img",{src:O.photo,alt:"",style:{width:"100%",minHeight:"100%"}}):o.jsx(x,{sx:{width:"62px",height:"62px",alignItems:"center",justifyContent:"center",backgroundColor:"#F6F9FC"},children:o.jsx(H,{icon:"mdi:file-image",width:"25px",color:"#E4EBF2"})})}),o.jsxs(x,{sx:{flex:1,minWidth:0,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:350},children:[o.jsx(M,{sx:{fontSize:t?"18px":"16px",color:"#202124"},noWrap:!0,children:O.name}),o.jsxs(x,{flexDirection:"row",alignItems:"center",gap:1,mt:"10px",children:[O.duration_minutes&&o.jsx(M,{sx:{fontSize:"16px",color:"#4E5381"},children:Ot(O.duration_minutes)}),t&&((N=O==null?void 0:O.assigned_staffs)==null?void 0:N.length)>0&&(O==null?void 0:O.assigned_staffs.map((J,te)=>o.jsx(bt,{placement:"top",slotProps:{tooltip:{sx:{backgroundColor:"#202124",borderRadius:"2px",color:"#FFF",fontSize:"14px",padding:"5px 7px",fontWeight:400}},arrow:{sx:{color:"#202124"}}},title:J==null?void 0:J.name,arrow:!0,children:o.jsx(W,{sx:{width:"30px",height:"30px"},children:o.jsx("img",{src:J.avatar,alt:"",style:{width:"100%",minHeight:"100%"}})})},`staff-${te}-${J.id}`))),t&&((le=O==null?void 0:O.assigned_staffs)==null?void 0:le.length)===1&&o.jsx(M,{sx:{fontSize:"16px",color:"#4E5381"},children:O==null?void 0:O.assigned_staffs[0].name})]})]})]})}),o.jsxs(x,{flexDirection:t?"row":"column",alignItems:t?"center":"flex-start",marginTop:t?0:"10px",children:[o.jsxs(M,{sx:{color:"#202124",fontSize:t?"24px":"18px",fontWeight:600},children:[(ee=O==null?void 0:O.unit_price)==null?void 0:ee.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),T]}),o.jsxs(x,{flexDirection:"row",alignItems:"center",sx:{marginInlineEnd:"10px",marginInlineStart:t?0:"-10px"},children:[o.jsxs(W,{sx:{position:"relative",backgroundColor:"rgba(255,255,255,0.7)"},children:[o.jsx(li,{checked:!O.status,onChange:J=>{l(O.id,J.target.checked?0:1)},disabled:p===O.id}),p===O.id&&o.jsx($e,{size:15,sx:{position:"absolute",top:"30%",left:"40%",transform:"translate(-40%, -40%)"}})," "]}),o.jsx(M,{color:"#202124",children:O.status===0||O.status==="0"?w("core:active"):w("core:inactive")})]}),o.jsx(Xf,{onClose:()=>{},serviceId:O.id,selectedService:O,onOpenServiceDrawer:()=>{i(O.id),s(O),n(null),a()},onOpenServiceFormDrawer:()=>{i(O.id),s(O),n(null),f()},userPermissions:c})]})]},O.id)})]}):null},`category-${Z==null?void 0:Z.id}`)})," "]}),((F=u==null?void 0:u.servicesList)==null?void 0:F.length)===0&&o.jsx(W,{sx:{border:"1px solid  #DBE3EB",width:"100%",height:"100%"},children:o.jsx(eh,{handleAddService:()=>{i(null),s(null),n(null),a()}})})]})]})},rh=({userPermissions:e})=>{var te;const{t}=oe(),r=Ae("(min-width:993px)"),[n,i]=v.useState(null),[s,a]=v.useState(null),[c,l]=v.useState(null),[p,f]=v.useState(null),[u,d]=v.useState([]),[g,b]=v.useState(-1),[_,k]=v.useState(0),[A,y]=v.useState(""),[T,j]=v.useState(""),w=Le(),{isOpen:C,onOpen:$,onClose:R}=Ze(),{isOpen:z,onOpen:F,onClose:Z}=Ze(),{isOpen:I,onOpen:D,onClose:U}=Ze(),{isOpen:X,onOpen:O,onClose:N}=Ze(),{mutate:le}=ta({onSuccess:Y=>{i(null),w.invalidateQueries({queryKey:["servicesList"]}),re((Y==null?void 0:Y.message)||"Service Updated successfully",{variant:"success"})},onError:Y=>{i(null);const K=Y==null?void 0:Y.message;re(K,{variant:"error"})}}),ee=async(Y,K)=>{i(Y);try{await le({serviceId:Y,status:K},{onSuccess:()=>{w.invalidateQueries({queryKey:["categoriesWithServices"]})},onError:ie=>{console.log("error",ie)}})}catch(ie){console.log("error",ie)}},J=Y=>{j(Y);const K=new URLSearchParams;let ie=0;if(Y&&K.append("search",Y),g!==void 0&&g!==-1&&(K.append("status",g),ie++),Array.isArray(u)&&u.length>0){for(const Te of u)K.append("assigned_staffs[]",Te);ie++}k(ie);const je=K.toString();y(`?${je}`)};return o.jsxs(x,{sx:{flex:1},children:[o.jsx(jr,{title:t("core:serviceManagement.label"),subtitle:t("core:serviceManagement.subtitle"),children:((e==null?void 0:e.is_super_admin)||((te=e==null?void 0:e.permissions)==null?void 0:te.includes(26)))&&o.jsxs(se,{variant:"contained",sx:{backgroundColor:"#13B272",minHeight:"100%",width:"177px",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center","&:hover":{backgroundColor:"#0C744A"}},onClick:()=>{a(null),l(null),f(null),$()},children:[o.jsx(H,{icon:"mdi:plus",width:"24",height:"24",color:"#FFF",style:{marginInlineEnd:"6px"}}),t("core:add-service")]})}),o.jsx(x,{sx:{flex:1,minWidth:r?"980px":"unset",backgroundColor:"#E4EBF2",position:"relative",padding:r?"30px":"10px",overflowY:"auto"},direction:"column",children:o.jsxs(x,{sx:{width:"100%",backgroundColor:"#FFF",padding:r?"34px 30px":"10px",height:"100%"},children:[o.jsx(Kp,{openFilters:D,clearAllFilters:()=>{d([]),b(-1),y(""),j(""),k(0)},filterAssignedEmployees:u,filterStatus:g,handleServicesSearch:J,filtersApplied:_}),o.jsx(W,{position:"relative",flex:1,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(th,{searchTerm:A,matches:r,onOpenCategoryDrawer:O,setSelectedCategoryId:f,setSelectedServiceId:a,setSelectedService:l,onOpenServiceDrawer:$,userPermissions:e,onChangeStatus:ee,loadingRowId:n,onOpenServiceFormDrawer:F})})})]})}),o.jsx(is,{open:C,onClose:R,serviceId:s,selectedCategoryId:p}),o.jsx(of,{open:X,onClose:N,categoryId:p,onOpenServiceDrawer:$,setSelectedCategoryId:f}),o.jsx(qp,{open:z,onClose:Z,serviceId:s,selectedService:c}),I&&o.jsx(Xp,{open:I,onClose:async()=>{await U(),await J(T)},filterAssignedEmployees:u,setFilterAssignedEmployees:d,filterStatus:g,setFilterStatus:b,filtersApplied:_})]})},nh=({handleClick:e})=>{const t=ra();return o.jsxs(se,{color:"secondary",onClick:e,sx:{width:"93px",height:"40px",backgroundColor:"#EDF0F3",borderColor:"#FFF",boxShadow:"none",color:"#202124",fontWeight:500,outline:"none",border:"1px solid #E4EBF2",fontSize:t.typography.pxToRem(16),display:"flex",alignItems:"center",justifyContent:"center",textTransform:"none","&:hover":{backgroundColor:"#B0C2D4"}},children:[o.jsx(H,{icon:"mdi:plus",color:"#17B26A",fontSize:t.typography.pxToRem(24),style:{marginInlineEnd:8}})," ","Add"]})},oh=({selectedServiceId:e,onChange:t,servicesData:r,error:n,helperText:i})=>{const{t:s}=oe(),[a,c]=v.useState(""),[l]=ht(a,500),{data:p,isLoading:f}=oi(l),[u,d]=v.useState(r==null?void 0:r.find(b=>b.id===Number(e))),g=p??[];return o.jsxs(vr,{size:{xs:12},children:[o.jsx(kt,{options:g,getOptionLabel:b=>b?b.name:"",filterOptions:b=>b,value:(r==null?void 0:r.find(b=>b.id===e))??null,onChange:(b,_)=>{d(_),t(_)},onInputChange:(b,_)=>c(_),loading:f,sx:{pointerEvents:"auto",width:"100%",maxWidth:"100%",minWidth:"220px"},renderOption:(b,_)=>v.createElement(x,{...b,component:"li",key:_.id,flexDirection:"row",alignItems:"center",gap:1,sx:{pointerEvents:"auto",minWidth:"220px",maxWidth:"100%"}},o.jsx("img",{src:_.avatar,width:"32",height:"32",alt:"service"}),o.jsx(x,{flexDirection:"column",children:o.jsx(M,{component:"p",children:_.name})})),renderInput:b=>o.jsx(ze,{...b.InputProps,inputProps:b.inputProps,placeholder:s("core:select-service"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"5px",padding:"8px 8px 8px 14px !important",backgroundColor:"#fff"},error:!0,startAdornment:u?o.jsx(dt,{src:u.avatar,alt:u.name,sx:{width:32,height:32,marginInlineEnd:"10px",borderRadius:"0px"}}):null,endAdornment:o.jsx(Ie,{})})}),n&&o.jsx(M,{color:"error",fontSize:12,mx:"14px",children:i})]})},zt=we(qe)(({theme:e})=>({padding:0,minHeight:50,backgroundColor:"#FFF9DB",border:"1px solid #DBE3EB"})),At=we(qe)(({theme:e})=>({border:"1px solid #DBE3EB",color:"#75799D",backgroundColor:"#F6F9FC",padding:"10px 16px",fontWeight:400,fontSize:"14px"})),ri=we(na)(({theme:e})=>({height:"100%",border:0,outline:"none",minHeight:"50px",color:"#202124",input:{padding:"14px 16px"},fieldset:{border:0},"&:hover":{backgroundColor:"#fff5c5"}})),ih=we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",width:"100%",height:"50px",transition:"all 0.2s ease",color:"#213242",backgroundColor:"#FFF",border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",flex:1,paddingInlineEnd:"35px !important"},"&:hover":{backgroundColor:"#E4EBF2"},".MuiSelect-icon":{color:"#213242"}})),sh=({initialData:e,onClose:t})=>{var N,le;const{t:r}=oe(),[n,i]=v.useState(e==null?void 0:e.booking_package_photo[0]),s=Pt({name:Ve().min(1,r("core:required")),status:Ve().min(1,r("core:required")),booking_package_services:gr(Pt({service_id:sr([Ve().min(1,r("core:required")),ir()]).transform(ee=>String(ee)),price:sr([Ve().min(1,r("core:required")),ir()]).transform(ee=>String(ee)).refine(ee=>/^\d+(\.\d{1,2})?$/.test(ee),{message:r("core:must_be_number")}),duration:sr([Ve().min(1,r("core:required")),ir()]).transform(ee=>String(ee)).refine(ee=>/^\d+(\.\d{1,2})?$/.test(ee),{message:r("core:must_be_number")})})).min(1,r("core:at_least_one_service"))}),{control:a,handleSubmit:c,formState:{errors:l},setValue:p,register:f,reset:u}=Fr({resolver:Rr(s),defaultValues:{name:"",status:"1",booking_package_services:[{service_id:"",duration:"",price:""}]}}),{data:d}=yr(),g=d==null?void 0:d.default_currency_formatted,{data:b}=yn(),_=Le(),{mutate:k}=oa(),{mutate:A}=ia(),{fields:y,append:T,remove:j,move:w}=dc({control:a,name:"booking_package_services"}),C=v.useRef([]),[$,R]=v.useState((N=e==null?void 0:e.booking_package_photo[0])==null?void 0:N.file_id),[z,F]=v.useState(0),[Z,I]=v.useState(0),[D,U]=v.useState(!1);v.useEffect(()=>{y.forEach((ee,J)=>{const te=C.current[J];if(!te)return;const Y=Pn({element:te,getInitialData:()=>({index:J})}),K=mr({element:te,getData:()=>({index:J}),onDrop:({source:ie})=>{ie.data.index!==J&&w(ie.data.index,J)}});return()=>{Y(),K()}})},[y,w]);const X=Ei({control:a,name:"booking_package_services"});v.useEffect(()=>{const ee=X==null?void 0:X.reduce((te,Y)=>{const K=Number.parseFloat((Y==null?void 0:Y.price)||"0");return te+(Number.isNaN(K)?0:K)},0),J=X==null?void 0:X.reduce((te,Y)=>{const K=Number.parseFloat((Y==null?void 0:Y.duration)||"0");return te+(Number.isNaN(K)?0:K)},0);F(ee),I(J)},[X]),v.useEffect(()=>{u(e?{name:e.name,status:"1",booking_package_services:e.booking_package_services}:{name:"",status:"1",booking_package_services:[{service_id:"",duration:"",price:""}]})},[e,u]);const O=async(ee,J)=>{U(!0);const te={...ee,total_price:z,photo:$,id:e==null?void 0:e.id};try{e?await A({packageId:e==null?void 0:e.id,input:te},{onSuccess:async Y=>{re(Y.message),_.invalidateQueries({queryKey:["packages"]}),J==="close"?t():(i(null),u({name:"",status:"1",booking_package_services:[{service_id:"",duration:"",price:""}]}),R(null)),U(!1)},onError:Y=>{U(!1),re(Y.message,{variant:"error"})}}):await k({input:te},{onSuccess:async Y=>{re(Y.message),_.invalidateQueries({queryKey:["packages"]}),console.log("submitType",J),J==="close"?t():(i(null),u({name:"",status:"1",booking_package_services:[{service_id:"",duration:"",price:""}]}),R(null)),U(!1)},onError:Y=>{U(!1),re(Y.message,{variant:"error"})}})}catch(Y){re(Y.message,{variant:"error"})}};return o.jsx(W,{px:"20px",sx:{height:"100%",paddingBottom:"100px",overflowY:"auto"},children:o.jsxs("form",{children:[o.jsxs(qt,{children:[o.jsxs(x,{flexDirection:"row",gap:2,children:[o.jsxs(x,{width:"50%",children:[o.jsxs(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:[r("core:name")," ",o.jsx(Xe,{children:"*"})]}),o.jsx(ze,{...f("name"),autoFocus:!0,placeholder:r("core:bookingPackages.namePlaceholder"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",height:"50px",backgroundColor:"#fff"}}),l.name&&o.jsx(M,{color:"error",fontSize:12,children:l.name.message.toString()})]}),o.jsxs(x,{width:"50%",children:[o.jsxs(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:[r("core:status")," ",o.jsx(Xe,{children:"*"})]}),o.jsxs(ih,{...f("status"),inputProps:{"aria-label":"Without label"},IconComponent:Ie,MenuProps:{disableScrollLock:!0,PaperProps:{style:{pointerEvents:"auto"}}},defaultValue:"1",children:[o.jsx(ne,{value:"1",sx:{height:"70px"},children:r("core:active")}),o.jsx(ne,{value:"0",sx:{height:"70px"},children:r("core:inactive")})]})]})]}),o.jsx(M,{sx:{fontSize:"16px",color:"#5F6368"},mb:1,mt:2,children:r("core:image")}),o.jsx(Dn,{setImageId:R,url:"/v2/api/entity/upload_file/booking_package/booking_packages.photo",initData:n}),o.jsx(W,{pt:2,width:"100%",children:o.jsxs(W,{sx:{width:"100%"},children:[o.jsx(M,{sx:{fontSize:"16px",color:"#5F6368"},mb:1,children:r("core:servicesList")}),o.jsxs(x,{children:[o.jsxs(mi,{sx:{height:1,tableLayout:"fixed"},"aria-label":"simple table",children:[o.jsx(yi,{children:o.jsxs(Tt,{children:[o.jsx(At,{sx:{width:"50px",borderInlineEnd:0}}),o.jsx(At,{sx:{width:"40%",paddingInlineStart:"0px",borderInlineStart:0},children:r("core:service")}),o.jsx(At,{sx:{width:"30%"},children:r("core:duration")}),o.jsx(At,{sx:{width:"30%"},children:r("core:price")}),o.jsx(At,{sx:{width:"50px"}})]})}),o.jsxs(vi,{children:[y.map((ee,J)=>o.jsxs(Tt,{ref:te=>C.current[J]=te,children:[o.jsx(zt,{sx:{width:"50px"},children:o.jsx(W,{sx:{height:"100%",color:"#75799D",width:"50px",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,cursor:"move",backgroundColor:"#F6F9FC","&:hover":{backgroundColor:"#e4ebf2"}},children:o.jsx(H,{icon:"mdi:drag-vertical",width:"24"})})}),o.jsx(zt,{sx:{width:"40%%",overflow:"hidden"},children:o.jsx(Ue,{name:`booking_package_services.${J}.service_id`,control:a,render:({field:te})=>{var Y,K,ie,je,Te;return o.jsx(oh,{selectedServiceId:te.value,onChange:ke=>{te.onChange((ke==null?void 0:ke.id)??""),ke!=null&&ke.unit_price&&p(`booking_package_services.${J}.price`,ke.unit_price),ke!=null&&ke.duration_minutes&&p(`booking_package_services.${J}.duration`,ke.duration_minutes)},servicesData:b,error:!!((K=(Y=l.booking_package_services)==null?void 0:Y[J])!=null&&K.service_id),helperText:(Te=(je=(ie=l.booking_package_services)==null?void 0:ie[J])==null?void 0:je.service_id)==null?void 0:Te.message})}})}),o.jsx(zt,{sx:{width:"30%"},children:o.jsx(Ue,{name:`booking_package_services.${J}.duration`,control:a,render:({field:te})=>{var Y,K,ie,je,Te;return o.jsx(ri,{...te,placeholder:r("core:duration"),error:!!((K=(Y=l.booking_package_services)==null?void 0:Y[J])!=null&&K.duration),helperText:(Te=(je=(ie=l.booking_package_services)==null?void 0:ie[J])==null?void 0:je.duration)==null?void 0:Te.message,fullWidth:!0})}})}),o.jsx(zt,{sx:{width:"30%"},children:o.jsx(Ue,{name:`booking_package_services.${J}.price`,control:a,render:({field:te})=>{var Y,K,ie,je,Te;return o.jsx(ri,{...te,placeholder:r("core:price"),error:!!((K=(Y=l.booking_package_services)==null?void 0:Y[J])!=null&&K.price),helperText:(Te=(je=(ie=l.booking_package_services)==null?void 0:ie[J])==null?void 0:je.price)==null?void 0:Te.message,fullWidth:!0})}})}),o.jsx(zt,{sx:{width:"50px"},children:o.jsx(Ce,{onClick:()=>j(J),sx:{height:"100%",color:"red",width:"50px",backgroundColor:"#F6F9FC",borderRadius:0,"&:hover":{backgroundColor:"red",color:"#FFF"}},children:o.jsx(H,{icon:"mdi:trash-can",width:"24"})})})]},ee.id)),o.jsxs(Tt,{children:[o.jsx(qe,{colSpan:2,sx:{padding:0},children:o.jsx(W,{children:o.jsxs(x,{justifyContent:"space-between",flexDirection:"row",alignItems:"center",children:[o.jsx(nh,{handleClick:()=>T({service_id:"",duration:"",price:""})}),o.jsx(x,{alignItems:"center",justifyContent:"center",sx:{border:"1px solid #DBE3EB",height:"40px",padding:"0px 18px",backgroundColor:"#F6F9FC"},children:o.jsx(M,{children:r("core:total")})})]})})}),o.jsx(qe,{sx:{padding:0},children:o.jsx(x,{flexDirection:"row",alignItems:"center",justifyContent:"flex-start",sx:{border:"1px solid #DBE3EB",height:"40px",padding:"0px 12px",backgroundColor:"#F6F9FC",color:"#75799D",fontSize:"16px"},children:Ot(Z)})}),o.jsx(qe,{sx:{padding:0},colSpan:2,children:o.jsxs(x,{flexDirection:"row",alignItems:"center",justifyContent:"flex-start",sx:{border:"1px solid #DBE3EB",height:"40px",padding:"0px 12px",backgroundColor:"#F6F9FC",color:"#202124",fontSize:"16px"},children:[z.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),g]})})]})]})]}),((le=l.booking_package_services)==null?void 0:le.message)&&o.jsx(M,{color:"error",children:l.booking_package_services.message.toString()})]})]})})]}),o.jsx(gt,{children:o.jsx(W,{sx:{padding:"20px",position:"absolute",bottom:0,left:0,right:0,backgroundColor:"#FFF"},children:o.jsxs(x,{flexDirection:"row",gap:2,alignItems:"center",children:[o.jsx(se,{type:"button",onClick:()=>{c(ee=>O(ee,"close"))()},disabled:D,sx:{width:"100%",height:"60px",backgroundColor:"#1877F2",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",fontWeight:600,gap:"10px",opacity:D?.7:1},children:r("core:saveClose")}),!e&&o.jsx(se,{onClick:()=>{c(ee=>O(ee,"addMore"))()},disabled:D,sx:{width:"100%",height:"60px",backgroundColor:"#13B272",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"1rem",fontWeight:600,gap:"10px",opacity:D?.7:1},children:r("core:saveAddMore")})]})})})]})})},vs=({initialData:e=null,onClose:t})=>o.jsx(sh,{onClose:t,initialData:e}),ah=({packageId:e,...t})=>{const{data:r}=sa(e);return o.jsx(vs,{initialData:r,...t})};function ch({onClose:e,...t}){const{t:r}=oe();return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:t.packageId?r("core:bookingPackages.edit"):r("core:bookingPackages.add")}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:t.packageId?o.jsx(ah,{packageId:t.packageId,onClose:e,...t}):o.jsx(vs,{onClose:e,...t})})]})}function lh(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(ch,{...e})})})})}function uh({value:e,onChange:t}){const{t:r}=oe(),[n,i]=v.useState([]),[s,a]=v.useState(""),[c]=ht(s,500),{data:l}=yn(c),p=l??[];return v.useEffect(()=>{e&&i(p.filter(f=>e.includes(f.id)))},[]),o.jsx(kt,{multiple:!0,options:p,getOptionLabel:f=>{var u;return typeof f=="string"?f:((u=f.name)==null?void 0:u.trim())||""},isOptionEqualToValue:(f,u)=>f.id===u.id,filterOptions:f=>f,value:n,onChange:(f,u)=>{const d=u.map(g=>g.id);t(d),i(u)},onInputChange:(f,u)=>a(u),renderOption:(f,u)=>v.createElement(x,{...f,component:"li",key:`service-${u.id}`,flexDirection:"row",alignItems:"center",gap:1,sx:{pointerEvents:"auto"}},o.jsx("img",{src:u.avatar,width:"42",height:"42",alt:"staff"}),o.jsx(x,{flexDirection:"column",children:o.jsxs(M,{component:"p",children:[u.name," "]})})),renderTags:(f,u)=>f.map((d,g)=>o.jsxs(x,{flexDirection:"row",sx:{color:"#444",height:"auto",alignItems:"center",padding:0,backgroundColor:"#F6F9FC",gap:"8px",marginInlineEnd:"8px"},...u({index:g}),children:[d.avatar&&o.jsx(dt,{src:d.avatar,sx:{width:"30px",height:"30px",borderRadius:"2px"}}),o.jsx(x,{flexDirection:"row",alignItems:"center",gap:"5px",children:o.jsx(M,{variant:"body2",sx:{textOverflow:"ellipsis",wordWrap:"break-word",overflow:"hidden",maxWidth:"200px",whiteSpace:"nowrap"},children:d.name})}),o.jsx(Ce,{size:"small",sx:{p:"2px",color:"#75799D",borderRadius:0},onClick:b=>{b.stopPropagation();const _=n.filter(k=>k.id!==d.id);i(_),t(_.map(k=>k.id))},children:o.jsx(Tn,{fontSize:"small"})})]},d.id)),renderInput:f=>o.jsx(ze,{...f.InputProps,inputProps:f.inputProps,placeholder:r("core:select-service"),fullWidth:!0,sx:{border:"1px solid #E4EBF2",borderRadius:"5px",padding:"8px 8px 8px 14px !important",backgroundColor:"#fff",gap:1},endAdornment:o.jsx(Ie,{})}),sx:{width:"100%",borderRadius:0,gap:"8px"}})}const dh=we(et)(()=>({outline:"none",fontSize:"1rem",maxHeight:"100%",width:"100%",height:"50px",transition:"all 0.2s ease",color:"#213242",backgroundColor:"#FFF",border:"1px solid #E4EBF2",borderRadius:"2px",padding:"0px 16px",fieldset:{display:"none"},".MuiSelect-select, .MuiNativeSelect-select":{color:"#213242",display:"inline-block",flex:1,paddingInlineEnd:"35px !important"},"&:hover":{backgroundColor:"#E4EBF2"},".MuiSelect-icon":{color:"#213242"}})),ph=({onClose:e,filterSelectedServices:t,setFilterSelectedServices:r,filterStatus:n,setFilterStatus:i,filtersApplied:s})=>{const{t:a}=oe();return o.jsxs(x,{sx:{width:"100%",height:"100%",p:"20px"},gap:"30px",children:[o.jsxs(x,{width:"100%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:a("core:services")}),o.jsx(uh,{value:t,onChange:r})]}),o.jsxs(x,{width:"100%",children:[o.jsx(_e,{sx:{color:"#5F6368",fontSize:"16px",marginBottom:"8px"},children:a("core:status")}),o.jsxs(dh,{inputProps:{"aria-label":"Without label"},IconComponent:Ie,MenuProps:{disableScrollLock:!0},value:n,onChange:c=>i(c.target.value),children:[o.jsx(ne,{value:2,sx:{height:"70px"},children:a("core:all")}),o.jsx(ne,{value:1,sx:{height:"70px"},children:a("core:active")}),o.jsx(ne,{value:0,sx:{height:"70px"},children:a("core:inactive")})]})]}),o.jsx(gt,{children:o.jsx(W,{sx:{padding:"20px",position:"absolute",bottom:0,width:"100%",backgroundColor:"#fff",borderTop:"1px solid #E4EBF2",left:0,right:0},children:o.jsxs(x,{direction:"row",justifyContent:"space-between",alignItems:"center",gap:"10px",children:[o.jsxs(M,{variant:"body2",sx:{color:"#5F6368",fontSize:"14px"},children:[s||0," ",a("core:filters_applied")]}),o.jsx(se,{variant:"outlined",sx:{color:"#1877F2",fontWeight:600,border:0,"&:hover":{backgroundColor:"#E4EBF2"}},onClick:()=>{r([]),i(2)},children:a("core:clear_all")})]})})})]})},fh=({onClose:e,filterSelectedServices:t,setFilterSelectedServices:r,filterStatus:n,setFilterStatus:i,filtersApplied:s})=>o.jsx(ph,{onClose:e,filterSelectedServices:t,setFilterSelectedServices:r,filterStatus:n,setFilterStatus:i,filtersApplied:s});function hh({onClose:e,...t}){const{t:r}=oe();return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:r("core:packages_filters")}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(fh,{onClose:e,...t})})]})}function gh(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(hh,{...e})})})})}const xh=({openFilters:e,setSearchTerm:t,clearAllFilters:r,searchTerm:n,handlePackagesSearch:i,filtersApplied:s})=>{const a=Ae("(min-width:993px)"),{t:c}=oe();return o.jsxs(x,{flexDirection:"row",alignItems:"center",justifyContent:"space-between",sx:{marginBottom:"20px"},children:[o.jsxs(x,{flexDirection:"row",alignItems:"center",height:"42px",sx:{border:"1px solid #E4EBF2",borderRadius:"2px",width:"70%"},children:[o.jsx(ze,{placeholder:c("core:search"),fullWidth:!0,startAdornment:o.jsx(H,{icon:"mdi:magnify",width:24,height:24,color:"#9EA1BA",style:{marginInlineEnd:"8px"}}),inputProps:{"aria-label":"search"},sx:{border:"1px solid #E4EBF2",padding:"0px 16px",height:"42px",backgroundColor:"#fff"},value:n,onChange:l=>t(l.target.value)}),o.jsxs(x,{flexDirection:"row",gap:1,alignItems:"center",sx:{height:"100%",paddingInline:a?"24px":"10px",borderStart:"1px solid #E4EBF2",cursor:"pointer",color:"#75799D",fontSize:"14px","&:hover":{backgroundColor:"#E4EBF2"}},onClick:()=>{e()},children:[o.jsx(H,{icon:"mdi:tune",width:24,color:"#75799D"}),a?c("core:filter"):"",s>0?o.jsx(x,{alignItems:"center",justifyContent:"center",sx:{backgroundColor:"#1877F2",borderRadius:"50%",padding:"5px",color:"#FFF",fontSize:14,width:"20px",height:"20px",fontWeight:500},children:s}):null]})]}),o.jsxs(x,{flexDirection:"row",alignItems:"center",justifyContent:"flex-end",height:"42px",sx:{borderRadius:"2px",width:"30%",gap:2},children:[o.jsx(se,{sx:{display:a?"flex":"none",alignItems:"center",gap:1,height:"100%",paddingInline:a?"24px":"10px",border:"1px solid #E4EBF2",cursor:"pointer",color:"#75799D",fontSize:"14px","&:hover":{backgroundColor:"#E4EBF2"}},onClick:()=>{t(""),r()},children:c("core:clearAll")}),o.jsx(x,{flexDirection:"row",gap:1,alignItems:"center",sx:{height:"100%",paddingInline:a?"24px":"10px",border:"1px solid #E4EBF2",cursor:"pointer",backgroundColor:"#E4EBF2",color:"#202124",fontSize:"14px","&:hover":{backgroundColor:"#B0C2D4"}},onClick:()=>{i(n)},children:c("core:search")})]})]})},mh=({onClose:e,packageId:t,onOpenPackageDrawer:r})=>{const n=Ae("(min-width:993px)"),{t:i}=oe(),[s,a]=v.useState(null),c=!!s,l=g=>{a(g.currentTarget)},p=()=>{a(null)},f=Le(),{mutate:u}=aa({onSuccess:g=>{f.invalidateQueries({queryKey:["packages"]}),re((g==null?void 0:g.message)||"Package Deleted successfully",{variant:"success"}),e()},onError:g=>{const b=g==null?void 0:g.message;re(b,{variant:"error"})}}),d=async()=>{try{u({packageId:t},{onError:g=>{console.log("error",g)}})}catch(g){console.log("error",g)}};return o.jsx(o.Fragment,{children:o.jsxs(W,{sx:{position:n?"relative":"absolute",top:n?0:"20px",right:n?0:"20px"},children:[o.jsx(Ce,{"aria-label":"close",sx:{width:40,height:40,backgroundColor:"#E4EBF2",borderRadius:0,color:"#75799D","&:hover":{backgroundColor:"#E4EBF2"}},disableRipple:!0,onClick:l,children:o.jsx(H,{icon:"mdi:dots-horizontal",width:32,color:"#202124"})}),o.jsxs(Dt,{id:"basic-menu",anchorEl:s,open:c,onClose:p,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{pointerEvents:"auto"},children:[o.jsxs(ne,{sx:{minWidth:180,height:50},onClick:()=>{r(),p()},disableRipple:!0,children:[o.jsx(H,{icon:"mdi:edit",width:22,color:"#75799D",style:{marginInlineEnd:5}}),i("core:edit")]}),o.jsxs(ne,{sx:{minWidth:180,height:50},onClick:()=>{d(),p()},disableRipple:!0,children:[o.jsx(H,{icon:"mdi:delete",width:22,color:"#ec2121",style:{marginInlineEnd:5}}),i("core:delete")]})]})]})})},vh=({handleAddPackage:e})=>{const{t}=oe();return o.jsxs(x,{sx:{width:"100%",height:"100%"},alignItems:"center",justifyContent:"center",flexDirection:"row",gap:"30px",children:[o.jsx(W,{component:"img",src:ms,alt:"No  Records Added Yet",sx:{width:105,height:105}}),o.jsxs(W,{children:[o.jsx(M,{variant:"h6",fontSize:"16px",fontWeight:500,color:"#75799D",children:t("core:no-records-added")}),o.jsxs(M,{component:"p",fontSize:"13px",fontWeight:400,color:"#75799D",children:[t("core:no-records-subtext-prefix")," ",o.jsx(M,{fontSize:"13px",fontWeight:400,color:"#202124",component:"span",children:`“${t("core:add-button-label")}”`}),"."]}),o.jsxs(se,{variant:"text",sx:{marginTop:"10px",color:"#13B272",padding:0,fontSize:"14px",fontWeight:400,"&:hover":{backgroundColor:"transparent"}},onClick:()=>{e()},children:[o.jsx(H,{icon:"mdi:plus",width:"20",height:"20",color:"#13B272",style:{marginInlineEnd:"3px"}}),t("core:bookingPackages.addPackage")]})]})]})},yh=({currency:e,loadingRowId:t,onChangeStatus:r,onOpenPackageDrawer:n,setSelectedPackageId:i,calculateTotalDuration:s,searchTerm:a})=>{var f,u;const{t:c}=oe(),l=Ae("(min-width:993px)"),{data:p}=ca(a);return((f=p==null?void 0:p.data)==null?void 0:f.length)===0?o.jsx(W,{sx:{border:"1px solid  #DBE3EB",width:"100%",height:"100%"},children:o.jsx(vh,{handleAddPackage:()=>{i(null),n()}})}):o.jsx(x,{width:"100%",flexDirection:"column",gap:"16px",children:(u=p==null?void 0:p.data)==null?void 0:u.map(d=>{var g,b,_,k,A;return o.jsxs(x,{flexDirection:l?"row":"column",justifyContent:"space-between",p:"20px",sx:{border:"1px solid #E4EBF2",borderRadius:"2px",backgroundColor:"#FFF",boxShadow:"0px 4px 16px 0px rgba(0, 0, 0, 0.04)",position:"relative"},children:[o.jsxs(x,{children:[o.jsxs(x,{flexDirection:"row",gap:"12px",sx:{paddingInlineEnd:l?"0px":"80px"},children:[o.jsx(x,{sx:{width:"50px",height:"50px",alignItems:"center",justifyContent:"center",borderRadius:"2px",overflow:"hidden"},children:d.photo?o.jsx("img",{src:d.photo,alt:"",style:{width:"100%",minHeight:"100%"}}):o.jsx(x,{sx:{width:"50px",height:"50px",alignItems:"center",justifyContent:"center",backgroundColor:"#F6F9FC"},children:o.jsx(H,{icon:"mdi:file-image",width:"25px",color:"#E4EBF2"})})}),o.jsxs(x,{children:[o.jsx(M,{sx:{fontSize:"18px",color:"#202124"},children:d.name}),o.jsx(M,{sx:{fontSize:"16px",color:"#4E5381"},children:Ot(s(d))})]})]}),o.jsxs(x,{flexDirection:"row",gap:"5px",mt:1,flexWrap:"wrap",children:[(g=d==null?void 0:d.booking_package_services)==null?void 0:g.slice(0,4).map((y,T)=>{var j;return o.jsxs(M,{color:"#4E5381",sx:{textOverflow:"ellipsis",wordWrap:"break-word",overflow:"hidden",maxWidth:"200px",whiteSpace:"nowrap"},children:[(j=y==null?void 0:y.service)==null?void 0:j.name," ",T<3&&T<(d==null?void 0:d.booking_package_services.length)-1&&" - "]},y.name)}),((b=d==null?void 0:d.booking_package_services)==null?void 0:b.length)>4&&o.jsx(bt,{placement:"top",slotProps:{tooltip:{sx:{backgroundColor:"#202124",borderRadius:"2px",color:"#FFF",fontSize:"14px",padding:"5px 7px",fontWeight:400}},arrow:{sx:{color:"#202124"}}},title:(_=d==null?void 0:d.booking_package_services)==null?void 0:_.slice(4).map(y=>{var T,j,w,C;return o.jsx(x,{gap:"8px",children:o.jsxs(x,{flexDirection:"row",alignItems:"center",flexWrap:"wrap",gap:"8px",children:[o.jsx(x,{sx:{width:"20px",height:"20px",alignItems:"center",justifyContent:"center",backgroundColor:"#F6F9FC"},children:(T=y==null?void 0:y.service)!=null&&T.photo?o.jsx("img",{src:(j=y==null?void 0:y.service)==null?void 0:j.photo,alt:"",style:{width:"100%",height:"100%",objectFit:"cover"}}):o.jsx(H,{icon:"mdi:file-image",width:"15px",color:"#E4EBF2"})}),o.jsx(M,{fontSize:14,children:(w=y==null?void 0:y.service)==null?void 0:w.name})]})},(C=y==null?void 0:y.service)==null?void 0:C.name)}),arrow:!0,children:o.jsxs(W,{sx:{color:"#1877F2",cursor:"pointer"},children:[" ","+",(k=d==null?void 0:d.booking_package_services)==null?void 0:k.slice(4).length]})})]})]}),o.jsxs(x,{flexDirection:"row",alignItems:"center",sx:{justifyContent:l?"flex-end":"space-between",width:l?"auto":"100%"},children:[o.jsxs(M,{sx:{color:"#202124",fontSize:"24px",fontWeight:600},children:[(A=d==null?void 0:d.total_price)==null?void 0:A.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),e]}),o.jsxs(x,{flexDirection:"row",alignItems:"center",sx:{marginInlineEnd:l?"10px":0},children:[o.jsxs(W,{sx:{position:"relative",backgroundColor:"rgba(255,255,255,0.7)"},children:[o.jsx(li,{checked:d.status,onChange:y=>{r(d.id,y.target.checked?1:0)},disabled:t===d.id}),t===d.id&&o.jsx($e,{size:15,sx:{position:"absolute",top:"30%",left:"40%",transform:"translate(-40%, -40%)"}})," "]}),o.jsx(M,{color:"#202124",children:d.status?c("core:active"):c("core:inactive")})]}),o.jsx(mh,{onClose:()=>{},packageId:d.id,onOpenPackageDrawer:()=>{i(d.id),n()}})]})]},d.id)})})},bh=()=>{const{t:e}=oe(),{data:t}=yr(),r=(t==null?void 0:t.default_currency_formatted)||"",n=Ae("(min-width:993px)"),[i,s]=v.useState(null),[a,c]=v.useState(null),[l,p]=v.useState([]),[f,u]=v.useState(2),[d,g]=v.useState(0),[b,_]=v.useState(""),[k]=ht(b,500),A=Le(),{isOpen:y,onOpen:T,onClose:j}=Ze(),{isOpen:w,onOpen:C,onClose:$}=Ze(),{mutate:R}=la({onSuccess:I=>{s(null),A.invalidateQueries({queryKey:["packages"]}),re((I==null?void 0:I.message)||"Package Updated successfully",{variant:"success"})},onError:I=>{s(null);const D=I==null?void 0:I.message;re(D,{variant:"error"})}}),z=async(I,D)=>{s(I);try{await R({packageId:I,status:D},{onError:U=>{console.log("error",U)}})}catch(U){console.log("error",U)}},F=I=>{const D=new URLSearchParams;let U=0;if(I&&D.append("name",I),f!==void 0&&f!==2&&(D.append("status",f),U++),Array.isArray(l)&&l.length>0){for(const O of l)D.append("services[]",O);U++}g(U);const X=D.toString();_(`?${X}`)};function Z(I){return I.booking_package_services.reduce((D,U)=>D+U.duration,0)}return o.jsxs(x,{sx:{flex:1},children:[o.jsx(jr,{title:e("core:bookingPackages.label"),subtitle:e("core:bookingPackages.subtitle"),children:o.jsxs(se,{variant:"contained",sx:{backgroundColor:"#13B272",height:"100%",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center","&:hover":{backgroundColor:"#0C744A"}},onClick:()=>{c(null),T()},children:[o.jsx(H,{icon:"mdi:plus",width:"24",height:"24",color:"#FFF",style:{marginInlineEnd:"6px"}}),e("core:bookingPackages.addPackage")]})}),o.jsx(x,{sx:{flex:1,minWidth:n?"980px":"unset",backgroundColor:"#E4EBF2",position:"relative",padding:n?"30px":"10px",overflowY:"auto"},direction:"column",children:o.jsxs(x,{sx:{width:"100%",backgroundColor:"#FFF",padding:n?"34px 30px":"10px",minHeight:"100%"},children:[o.jsx(xh,{openFilters:C,setSearchTerm:_,searchTerm:b,clearAllFilters:()=>{p([]),u(2),_(""),g(0)},handlePackagesSearch:F,filtersApplied:d}),o.jsx(W,{position:"relative",flex:1,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(yh,{currency:r,loadingRowId:i,onChangeStatus:z,onOpenPackageDrawer:T,setSelectedPackageId:c,calculateTotalDuration:Z,searchTerm:k})})})]})}),o.jsx(lh,{open:y,onClose:j,packageId:a}),o.jsx(gh,{open:w,onClose:()=>{F(""),$()},filterSelectedServices:l,setFilterSelectedServices:p,filterStatus:f,setFilterStatus:u,filtersApplied:d})]})},ni=we(Ce)(()=>({display:"flex",alignItems:"center",justifyContent:"center !important",height:"54px",width:"54px",borderRadius:0,backgroundColor:"#F6F9FC",color:"#213242","&:hover":{backgroundColor:"#E4EBF2"}})),jh=[{id:7,name:"Moaz Seyam",avatar:"https://randomuser.me/api/portraits/men/32.jpg",shifts:{"2025-08-30":[{label:"10am - 5pm",type:"work"}],"2025-08-31":[{label:"On Leave",type:"leave"}],"2025-09-01":[{label:"Morning 8am - 12pm",type:"work"},{label:"Afternoon 1pm - 5pm",type:"work"}]}},{id:11,name:"Jimmy Carter",avatar:"",shifts:{"2025-08-30":[{label:"Blocked 3pm - 7pm",type:"blocked"},{label:"3pm - 7pm",type:"work"}],"2025-09-01":[{label:"Holiday",type:"holiday"}],"2025-09-05":[{label:"Day Off",type:"off"}]}}];function wh({onOpenSetRegularShiftDrawer:e,onOpenAddBlockTime:t}){const[r,n]=v.useState(Mr(new Date(2025,7,30),{weekStartsOn:6})),i=Array.from({length:7},(z,F)=>{const Z=Kn(r,F);return{date:Gt(Z,"yyyy-MM-dd"),label:Gt(Z,"EEE, d MMM")}});`${Gt(r,"d MMM")}${Gt(Kn(r,6),"d MMM, yyyy")}`;const s=we(({className:z,...F})=>o.jsx(bt,{...F,classes:{popper:z}}))(()=>({[`& .${Qn.tooltip}`]:{backgroundColor:"#000",color:"#fff",fontSize:13,padding:"6px 10px",display:"flex",alignItems:"center",gap:"6px"},[`& .${Qn.arrow}`]:{color:"#000"}})),a=(z,F)=>{alert(`➕ Add shift for employee ${z} on ${F}`)},c=v.useRef(null),[l,p]=v.useState(new Date),f=z=>{p(z),n(Mr(z??new Date,{weekStartsOn:6}))},[u,d]=v.useState({anchorEl:null,empId:null}),g=(z,F)=>{d({anchorEl:z.currentTarget,empId:F})},b=()=>{d({anchorEl:null,empId:null})},_=z=>{b(),e(z)},k=(z,F)=>{t(z,F),j()},[A,y]=v.useState({anchorEl:null,empId:null,date:null}),T=(z,F,Z)=>{y({anchorEl:z.currentTarget,empId:F,date:Z})},j=()=>{y({anchorEl:null,empId:null,date:null})},[w,C]=v.useState(null),$=z=>{C(z.currentTarget)},R=()=>{C(null)};return o.jsxs(W,{children:[o.jsxs(x,{direction:"row",justifyContent:"space-between",mb:"12px",width:"100%",children:[o.jsxs(x,{direction:"row",spacing:.5,alignItems:"center",sx:{flex:1},children:[o.jsx(ni,{onClick:()=>n(Yn(r,-1)),disableRipple:!0,children:o.jsx(H,{icon:"mdi:chevron-double-left",width:20,height:20})}),o.jsx(W,{width:"100%",children:o.jsx(ua,{ref:c,selected:l,onChange:f,dateFormat:"EEEE, dd MMMM, yyyy"})}),o.jsx(da,{variant:"contained",onClick:()=>n(Mr(new Date,{weekStartsOn:6})),disableRipple:!0,sx:{flexShrink:0},children:"This Week"}),o.jsx(ni,{onClick:()=>n(Yn(r,1)),disableRipple:!0,children:o.jsx(H,{icon:"mdi:chevron-double-right",width:20,height:20})})]}),o.jsxs(x,{sx:{ml:1},children:[o.jsx(se,{variant:"contained",onClick:$,sx:{borderRadius:0,backgroundColor:"#1877F2",color:"#FFFFFF",textTransform:"none",height:"54px",boxShadow:"none"},endIcon:o.jsx(H,{icon:"mdi:chevron-down",width:20,height:20}),startIcon:o.jsx(H,{icon:"mdi:plus",width:20,height:20}),children:"Add"}),o.jsxs(Dt,{anchorEl:w,open:!!w,onClose:R,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},slotProps:{paper:{sx:{boxShadow:"none",border:"1px solid #E4EBF2",borderRadius:"6px",mt:1}}},children:[o.jsxs(ne,{onClick:R,sx:{gap:1},children:[o.jsx(H,{icon:"mdi:calendar-remove",color:"#1877F2",width:20,height:20}),"Add Day Off"]}),o.jsxs(ne,{onClick:R,sx:{gap:1},children:[o.jsx(H,{icon:"mdi:clock-time-four",color:"#1877F2",width:20,height:20}),"Update Day Shift"]}),o.jsxs(ne,{onClick:R,sx:{gap:1},children:[o.jsx(H,{icon:"mdi:account",color:"#1877F2",width:20,height:20}),"Assign Employee"]})]})]})]}),o.jsxs(mi,{size:"small",sx:{height:1,"& td, & th":{border:"1px solid #E4EBF2"}},children:[o.jsx(yi,{children:o.jsxs(Tt,{sx:{bgcolor:"#F6F9FC"},children:[o.jsx(qe,{sx:{p:"16px 14px"},children:"Employees"}),i.map(z=>o.jsx(qe,{align:"center",children:z.label},z.date))]})}),o.jsx(vi,{children:jh.map(z=>o.jsxs(Tt,{children:[o.jsx(qe,{sx:{p:"16px 14px"},children:o.jsxs(x,{direction:"row",spacing:1,alignItems:"center",sx:{position:"relative","&:hover .menu-btn":{opacity:1}},children:[z.avatar?o.jsx(dt,{src:z.avatar,sx:{width:"40px",height:"40px",borderRadius:"2px"}}):o.jsx(dt,{sx:{width:"40px",height:"40px",borderRadius:"2px"},children:z.name.charAt(0)}),o.jsx(M,{children:z.name}),o.jsx(Ce,{size:"small",className:"menu-btn",sx:{ml:"auto",opacity:u.anchorEl&&u.empId===z.id?1:0},onClick:F=>g(F,z.id),children:o.jsx(H,{icon:"mdi:dots-vertical"})}),o.jsxs(Dt,{anchorEl:u.anchorEl,open:!!(u.anchorEl&&u.empId===z.id),onClose:b,children:[o.jsxs(ne,{onClick:()=>_(z.id),sx:{gap:1},children:[o.jsx(H,{icon:"mdi:cog",width:20,height:20,color:"#1877F2"}),"Set Regular Shift"]}),o.jsxs(ne,{onClick:b,sx:{gap:1},children:[o.jsx(H,{icon:"mdi:account-remove",width:20,height:20,color:"#EC2121"}),"Remove Employee"]})]})]})}),i.map(F=>{const Z=z.shifts[F.date]||[];return o.jsxs(qe,{align:"center",sx:{whiteSpace:"pre-line",position:"relative",p:"2px","&:hover .add-btn":{opacity:1},verticalAlign:"top"},children:[Z.length>0?o.jsxs(x,{alignItems:"center",spacing:.5,sx:{height:"100%"},children:[Z.map((I,D)=>o.jsx(s,{title:o.jsxs(x,{direction:"row",alignItems:"center",spacing:1,children:[o.jsx(H,{icon:"mdi:flag-variant",fontSize:16}),o.jsx("span",{children:I.label})]}),arrow:!0,placement:"bottom",children:o.jsx(W,{component:"span",sx:{backgroundColor:I.type==="work"?"#E6F2E6":I.type==="blocked"?"#FDF2E7":I.type==="leave"?"#FCF4F4":I.type==="off"?"#FAFBFD":"#FDF2E7",borderRadius:"2px",width:"100%",p:"12px 8px",color:"#202124",opacity:I.type==="off"?.25:1},children:I.type==="blocked"?o.jsxs(o.Fragment,{children:[o.jsx(M,{variant:"body2",sx:{fontWeight:600,lineHeight:1.2},children:"Blocked"}),o.jsx(M,{variant:"body2",sx:{lineHeight:1.2,color:"#75799D",mt:.5},children:I.label.replace("Blocked ","")})]}):I.label})})),o.jsx(Ce,{size:"small",onClick:I=>T(I,z.id,F.date),sx:{opacity:A.empId===z.id&&A.date===F.date?1:0,p:"8px",width:"100%",flex:1,borderRadius:"2px",backgroundColor:"#1877F2","&:hover":{backgroundColor:"#0052BD",opacity:1}},children:o.jsx(H,{icon:"mdi:plus",width:20,height:20,color:"#FFFFFF"})})]}):o.jsx(W,{sx:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},onClick:I=>T(I,z.id,F.date),children:o.jsx(Ce,{size:"small",className:"add-btn",sx:{opacity:A.empId===z.id&&A.date===F.date?1:0,p:"8px",width:"100%",height:"100%",borderRadius:"2px",backgroundColor:"#1877F2","&:hover":{backgroundColor:"#0052BD",opacity:1}},children:o.jsx(H,{icon:"mdi:plus",width:20,height:20,color:"#FFFFFF"})})}),o.jsxs(Dt,{anchorEl:A.anchorEl,open:!!A.anchorEl,onClose:j,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},slotProps:{paper:{sx:{boxShadow:"none"}}},children:[o.jsxs(ne,{sx:{gap:1},onClick:()=>{a(A.empId,A.date),j()},children:[o.jsx(H,{icon:"mdi:clock-time-four",width:20,height:20,color:"#1877F2"}),"Update Day Shift"]}),o.jsxs(ne,{sx:{gap:1},onClick:()=>{k(A.empId,A.date)},children:[o.jsx(H,{icon:"mdi:calendar-remove",width:20,height:20,color:"#1877F2"}),"Add Block Time"]})]})]},F.date)})]},z.id))})]})]})}function _h({onClose:e}){var u;const{data:t,isLoading:r}=ui(),n=di(),{data:i}=pi(),[s,a]=v.useState(""),[c,l]=v.useState(null);v.useEffect(()=>{i!=null&&i.shift_id&&a(String(i==null?void 0:i.shift_id))},[i]);const p=d=>{a(d.target.value),l(null)},f=d=>{if(d.preventDefault(),!s){l("Default booking shift is required");return}n.mutate({input:{shift_id:s}},{onSuccess:()=>{e()}})};return o.jsxs("form",{onSubmit:f,style:{height:"100%",display:"flex",justifyContent:"space-between",flexDirection:"column"},children:[o.jsx(qt,{children:o.jsxs(x,{gap:"12px",display:"flex",p:"20px",children:[o.jsx(M,{sx:{color:"#373B50",fontSize:"16px"},children:"Default Booking Shift"}),o.jsxs(x,{display:"flex",flexDirection:"row",alignItems:"center",gap:2,children:[r?o.jsx($e,{size:28}):o.jsx(bi,{value:s,onChange:p,error:!!c,sx:{flexGrow:"1",height:"50px"},children:(u=t==null?void 0:t.data)==null?void 0:u.map(d=>{const g=d.shift_days.map(_=>_.weekday.charAt(0).toUpperCase()+_.weekday.slice(1,3)).join(", "),b=d.shift_days.length>0?`${d.shift_days[0].from_time.slice(0,5)} - ${d.shift_days[0].to_time.slice(0,5)}`:"";return o.jsx(ne,{value:String(d.id),children:o.jsxs(W,{sx:{flexDirection:"row",justifyContent:"space-between",alignItems:"center",width:"100%",display:"flex"},children:[o.jsx(M,{sx:{fontSize:"16px",color:"#202124"},children:d.name}),o.jsxs(M,{sx:{fontSize:"14px",color:"#7F8895"},children:[g," (",b,")"]})]})},d.id)})}),o.jsxs(Ce,{"aria-label":"manage",sx:{backgroundColor:"#E4EBF2",borderRadius:0,color:"#202124",fontSize:"14px","&:hover":{backgroundColor:"#E4EBF2"},display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",height:"50px",paddingBlock:"0"},disableRipple:!0,children:[o.jsx(H,{icon:"mdi:cog",width:16}),"Manage Shifts",o.jsx(H,{icon:"mdi:open-in-new",width:16})]})]}),c&&o.jsx(fi,{error:!0,children:c}),o.jsx(M,{sx:{color:"#7F8895",fontSize:"14px"},children:"Set a default shift for new employees or users. Current schedules remain intact."})]})}),o.jsx(gt,{children:o.jsxs(x,{display:"flex",flexDirection:"row",justifyContent:"space-between",borderTop:"1px solid #eee",p:"20px",gap:2,children:[o.jsx(se,{type:"button",sx:{width:"100%",height:"60px",backgroundColor:"#E4EBF2",color:"#373B50",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"16px",fontWeight:600,gap:"10px",textTransform:"uppercase"},onClick:e,children:"Discard"}),o.jsx(se,{type:"submit",sx:{width:"100%",height:"60px",backgroundColor:"#13B272",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"16px",fontWeight:600,gap:"10px",textTransform:"uppercase"},disabled:n.isPending,children:"Save"})]})})]})}const kh=({onClose:e,onOpenDefaultShiftDrawer:t})=>o.jsx(_h,{onClose:e});function Ch({onClose:e,...t}){return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:"Manage Default Shift"}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(kh,{onClose:e,...t})})]})}function Fh(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Ch,{...e})})})})}function Eh({onClose:e,employeeId:t}){var k;const{data:r,isLoading:n}=ui(),i=di(),{data:s}=pi(),{data:a}=pa(),[c,l]=v.useState(""),[p,f]=v.useState(null),[u,d]=v.useState(null);v.useEffect(()=>{s!=null&&s.shift_id&&l(String(s==null?void 0:s.shift_id))},[s]),v.useEffect(()=>{if(console.log("selectedEmpolyee",u,t),a&&t){const A=a.find(y=>y.id===t);A&&d(A)}else t||d(null)},[a,t]);const g=A=>{l(A.target.value),f(null)},b=A=>{d(A)},_=A=>{if(A.preventDefault(),!c){f("Default booking shift is required");return}i.mutate({input:{shift_id:c,employee_id:(u==null?void 0:u.id)??null}},{onSuccess:()=>{e()}})};return o.jsxs("form",{onSubmit:_,style:{height:"100%",display:"flex",justifyContent:"space-between",flexDirection:"column"},children:[o.jsx(qt,{children:o.jsxs(x,{gap:"12px",display:"flex",p:"20px",children:[o.jsx(W,{mt:2,children:o.jsx(fa,{selectedEmpolyee:u,onChange:b,showEmail:!0})}),o.jsx(M,{sx:{color:"#373B50",fontSize:"16px",mt:2},children:"Assigned Default Shift"}),o.jsxs(x,{display:"flex",flexDirection:"row",alignItems:"center",gap:2,children:[n?o.jsx($e,{size:28}):o.jsx(bi,{value:c,onChange:g,error:!!p,sx:{flexGrow:"1",height:"50px"},children:(k=r==null?void 0:r.data)==null?void 0:k.map(A=>{const y=A.shift_days.map(j=>j.weekday.charAt(0).toUpperCase()+j.weekday.slice(1,3)).join(", "),T=A.shift_days.length>0?`${A.shift_days[0].from_time.slice(0,5)} - ${A.shift_days[0].to_time.slice(0,5)}`:"";return o.jsx(ne,{value:String(A.id),children:o.jsxs(W,{sx:{flexDirection:"row",justifyContent:"space-between",alignItems:"center",width:"100%",display:"flex"},children:[o.jsx(M,{sx:{fontSize:"16px",color:"#202124"},children:A.name}),o.jsxs(M,{sx:{fontSize:"14px",color:"#7F8895"},children:[y," (",T,")"]})]})},A.id)})}),o.jsx(Ce,{"aria-label":"manage",sx:{backgroundColor:"#E4EBF2",borderRadius:0,color:"#202124",fontSize:"14px","&:hover":{backgroundColor:"#E4EBF2"},display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",height:"50px",paddingBlock:"0",paddingInline:"24px"},disableRipple:!0,children:"Add New Shift"})]}),p&&o.jsx(fi,{error:!0,children:p})]})}),o.jsx(gt,{children:o.jsxs(x,{display:"flex",flexDirection:"row",justifyContent:"space-between",borderTop:"1px solid #eee",p:"20px",gap:2,children:[o.jsx(se,{type:"button",sx:{width:"100%",height:"60px",backgroundColor:"#E4EBF2",color:"#373B50",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"16px",fontWeight:600,gap:"10px",textTransform:"uppercase"},onClick:e,children:"Discard"}),o.jsx(se,{type:"submit",sx:{width:"100%",height:"60px",backgroundColor:"#13B272",color:"#FFF",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:0,border:"none",fontSize:"16px",fontWeight:600,gap:"10px",textTransform:"uppercase"},disabled:i.isPending,children:"Save"})]})})]})}const Sh=({onClose:e,employeeId:t})=>o.jsx(Eh,{onClose:e,employeeId:t??null});function Ih({onClose:e,...t}){return o.jsxs(o.Fragment,{children:[o.jsxs(nt,{children:[o.jsx(ot,{children:"Set Regular Shift"}),o.jsx(it,{children:o.jsx(x,{direction:"row",gap:"2px",children:o.jsx(st,{handleClose:e})})})]}),o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Sh,{onClose:e,...t})})]})}function zh(e){const t=v.useCallback(r=>o.jsx(tt,{onClose:e.onClose,...r}),[e.onClose]);return o.jsx(rt,{open:e.open,onClose:e.onClose,children:o.jsx(Ke,{fallbackRender:t,children:o.jsx(v.Suspense,{fallback:o.jsx(he,{}),children:o.jsx(Ih,{...e})})})})}const Ah=({userPermissions:e,formChanged:t,setFormChanged:r})=>{const{t:n}=oe(),i=Ae("(min-width:993px)"),{data:s}=ai(),[a,c]=v.useState((s==null?void 0:s.service_employee_setting)||"one_employee"),[l,p]=v.useState((s==null?void 0:s.booking_service_setting)||"multiple_services"),[f,u]=v.useState(!1),[d,g]=v.useState(null);Ze();const{isOpen:b,onOpen:_,onClose:k}=Ze(),{isOpen:A,onOpen:y,onClose:T}=Ze(),{isOpen:j,onOpen:w,onClose:C}=Ze();return o.jsxs(x,{sx:{width:"100%",height:"100%"},children:[o.jsx(jr,{title:n("core:shiftRoaster.label"),subtitle:n("core:shiftRoaster.subtitle"),children:o.jsx(se,{variant:"contained",sx:{backgroundColor:"#E4EBF2",color:"#373B50",height:"100%",paddingInlineStart:"24px",paddingInlineEnd:"28px",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:f?"none":"auto",opacity:f?.8:1},onClick:()=>{_()},children:f?o.jsx($e,{size:"20px"}):o.jsxs(o.Fragment,{children:[o.jsx(H,{icon:"mdi:cog",width:"24",height:"24",color:"#373B50",style:{marginInlineEnd:"6px"}}),n("core:shiftRoaster.defaultShift")]})})}),o.jsx(x,{sx:{width:"100%",height:"100%",backgroundColor:"#E4EBF2",position:"relative",padding:i?"30px":"10px",overflowY:"auto"},children:o.jsxs(W,{sx:{width:"100%",backgroundColor:"#FFF",padding:"34px 30px"},children:[o.jsxs(x,{direction:"row",children:[o.jsx(se,{variant:"contained",sx:{backgroundColor:"#E4EBF2",color:"#373B50",height:"100%",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:f?"none":"auto",opacity:f?.8:1,marginInlineEnd:"6px"},children:f?o.jsx($e,{size:"20px"}):o.jsxs(o.Fragment,{children:[o.jsx(H,{icon:"mdi:calendar-remove",width:"24",height:"24",color:"#1877F2",style:{marginInlineEnd:"6px"}}),"Add Day Off"]})}),o.jsx(se,{variant:"contained",sx:{backgroundColor:"#E4EBF2",color:"#373B50",height:"100%",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:f?"none":"auto",opacity:f?.8:1,marginInlineEnd:"6px"},children:f?o.jsx($e,{size:"20px"}):o.jsxs(o.Fragment,{children:[o.jsx(H,{icon:"mdi:clock-time-four",width:"24",height:"24",color:"#1877F2",style:{marginInlineEnd:"6px"}}),"Update Day Shift"]})}),o.jsx(se,{variant:"contained",sx:{backgroundColor:"#E4EBF2",color:"#373B50",height:"100%",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:f?"none":"auto",opacity:f?.8:1,marginInlineEnd:"6px"},children:f?o.jsx($e,{size:"20px"}):o.jsxs(o.Fragment,{children:[o.jsx(H,{icon:"mdi:account",width:"24",height:"24",color:"#1877F2",style:{marginInlineEnd:"6px"}}),"Assign Employee"]})}),o.jsx(se,{variant:"contained",sx:{backgroundColor:"#E4EBF2",color:"#373B50",height:"100%",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:f?"none":"auto",opacity:f?.8:1,marginInlineEnd:"6px"},onClick:()=>{y()},children:f?o.jsx($e,{size:"20px"}):o.jsxs(o.Fragment,{children:[o.jsx(H,{icon:"mdi:cog",width:"24",height:"24",color:"#1877F2",style:{marginInlineEnd:"6px"}}),"Set Regular Shift"]})}),o.jsx(se,{variant:"contained",sx:{backgroundColor:"#E4EBF2",color:"#373B50",height:"100%",borderRadius:0,border:"none",outline:"none",display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:f?"none":"auto",opacity:f?.8:1,marginInlineEnd:"6px"},children:f?o.jsx($e,{size:"20px"}):o.jsxs(o.Fragment,{children:[o.jsx(H,{icon:"mdi:account-remove",width:"24",height:"24",color:"#EC2121",style:{marginInlineEnd:"6px"}}),"Remove Employee"]})})]}),o.jsx(wh,{onOpenSetRegularShiftDrawer:$=>{g($),y()},onOpenAddBlockTime:($,R)=>{g($),w()}})]})}),o.jsx(Fh,{onClose:k,onOpenDefaultShiftDrawer:_,open:b}),o.jsx(zh,{onClose:()=>{T(),g(null)},onSetRegularShiftDrawer:y,open:A,employeeId:d}),o.jsx(ha,{open:j,onClose:()=>{C(),g(null)},initialData:d?{staff_id:d}:void 0})]})};function Dh(){const e=window.IS_RTL?"ara":"eng";Es({lang:e});const[t,r]=v.useState(0),{data:n}=ga(),[i,s]=v.useState(!1),{t:a}=oe();v.useEffect(()=>{const l=()=>{const p=window.location.hash.replace("#",""),f=Number.parseInt(p,10);Number.isNaN(f)||r(f)};return l(),window.addEventListener("hashchange",l),()=>{window.removeEventListener("hashchange",l)}},[]);const c=l=>{if(t===0&&i)if(window.confirm(a("core:confirmLeavePage")))s(!1);else return;r(l),window.location.hash=String(l)};return o.jsxs(x,{width:"fit-content",minWidth:"100%",minHeight:"100%",children:[o.jsx(Ua,{}),o.jsxs(x,{direction:"row",sx:{flex:1},children:[o.jsx(Ha,{activeTabIndex:t,setActiveTabIndex:c,userPermissions:n}),o.jsx(W,{sx:{display:"flex",flex:1,minHeight:"100%",position:"relative"},children:o.jsxs(v.Suspense,{fallback:o.jsx(he,{}),children:[t===0&&o.jsx(Zp,{userPermissions:n,formChanged:i,setFormChanged:s}),t===1&&o.jsx(rh,{userPermissions:n}),t===2&&o.jsx(bh,{}),t===3&&o.jsx(Ah,{userPermissions:n,formChanged:i,setFormChanged:s})]})})]})]})}const Th=new Ss;function Rh({resetErrorBoundary:e}){return o.jsx(ya,{resetErrorBoundary:e})}Is.createRoot(document.getElementById("root")).render(o.jsx(v.StrictMode,{children:o.jsx(zs,{client:Th,children:o.jsx(Ke,{fallbackRender:Rh,children:o.jsx(v.Suspense,{fallback:o.jsx(x,{sx:{padding:"100px",alignItems:"center",justifyContent:"center"},children:o.jsx($e,{})}),children:o.jsx(xa,{children:o.jsx(ma,{children:o.jsx(va,{children:o.jsx(Dh,{})})})})})})})}));
