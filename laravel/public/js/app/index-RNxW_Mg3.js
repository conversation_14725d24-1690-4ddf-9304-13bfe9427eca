import{M as ne,N as bt,G as Ie,j as N,O as Ne,R as n,P as Re,I as ze}from"./i18n-DJdGDBdH.js";const Ae=async()=>await fetch("/v2/api/site/info").then(r=>r.json()),sr=()=>bt({queryKey:["site-info"],queryFn:Ae}),_e=async t=>(await fetch(`/v2/api/token/me?token=${t}`).then(e=>e.json())).data,nr=t=>bt({queryKey:["me-public",t],queryFn:()=>_e(t)}),je=async()=>await fetch("/v2/api/staff/permissions").then(r=>r.json()),ar=()=>bt({queryKey:["my-permissions"],queryFn:je}),Be=async()=>await fetch("/v2/api/email-prefrence/subscription-email").then(r=>r.json()),ir=()=>bt({queryKey:["subscriptions"],queryFn:Be}),Le=async t=>await fetch(`/v2/api/email-prefrence/token-subscription-email?token=${t}`).then(e=>e.json()),lr=t=>bt({queryKey:["subscriptions",t],queryFn:()=>Le(t)}),Pe=async({status:t,entity:r})=>await fetch(`/v2/api/email-prefrence/subscription-email/${r}`,{headers:{"Content-Type":"application/json"},body:JSON.stringify({status:Number(t).toString()}),method:"PATCH"}).then(s=>s.json()),cr=t=>ne({mutationFn:Pe,...t}),De=async({status:t,entity:r,token:e})=>await(await fetch(`/v2/api/email-prefrence/token-subscription-email/${r}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e,status:Number(t).toString()})})).json(),dr=t=>ne({mutationFn:De,...t}),Fe=async({queryKey:t})=>{const[,r]=t;return await fetch(`/v2/api/entity/staff/${r}/1`).then(s=>s.json())},ur=t=>bt({queryKey:["employee",t],queryFn:Fe}),Oe=async({queryKey:t})=>{const[,r,e]=t;return await fetch(`/v2/api/templates/staff/${r}${e?`?lang=${e}`:""}`).then(d=>d.json())},mr=({id:t,lang:r})=>bt({queryKey:["employee-documents",t,r],queryFn:Oe}),He=async({queryKey:t})=>{const[,r,e,s,d]=t;return await fetch(`/v2/api/template/generate_url?request_branch_id=${s}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:e.toString(),template_id:r.toString(),template_scope:d})}).then(a=>a.json())},pr=()=>{const t=Ie();return{excuteQuery:async({templateId:e,userId:s,branchId:d,scope:i})=>await t.fetchQuery({queryKey:["employee-documents",e,s,d,i],queryFn:He})}};function fr({children:t}){return N.jsx("div",{className:"tw:bg-[#E4EBF2]",children:t})}const $t="-",Ve=t=>{const r=Ge(t),{conflictingClassGroups:e,conflictingClassGroupModifiers:s}=t;return{getClassGroupId:a=>{const y=a.split($t);return y[0]===""&&y.length!==1&&y.shift(),ae(y,r)||$e(a)},getConflictingClassGroupIds:(a,y)=>{const m=e[a]||[];return y&&s[a]?[...m,...s[a]]:m}}},ae=(t,r)=>{var a;if(t.length===0)return r.classGroupId;const e=t[0],s=r.nextPart.get(e),d=s?ae(t.slice(1),s):void 0;if(d)return d;if(r.validators.length===0)return;const i=t.join($t);return(a=r.validators.find(({validator:y})=>y(i)))==null?void 0:a.classGroupId},Jt=/^\[(.+)\]$/,$e=t=>{if(Jt.test(t)){const r=Jt.exec(t)[1],e=r==null?void 0:r.substring(0,r.indexOf(":"));if(e)return"arbitrary.."+e}},Ge=t=>{const{theme:r,classGroups:e}=t,s={nextPart:new Map,validators:[]};for(const d in e)Dt(e[d],s,d,r);return s},Dt=(t,r,e,s)=>{t.forEach(d=>{if(typeof d=="string"){const i=d===""?r:te(r,d);i.classGroupId=e;return}if(typeof d=="function"){if(Ye(d)){Dt(d(s),r,e,s);return}r.validators.push({validator:d,classGroupId:e});return}Object.entries(d).forEach(([i,a])=>{Dt(a,te(r,i),e,s)})})},te=(t,r)=>{let e=t;return r.split($t).forEach(s=>{e.nextPart.has(s)||e.nextPart.set(s,{nextPart:new Map,validators:[]}),e=e.nextPart.get(s)}),e},Ye=t=>t.isThemeGetter,qe=t=>{if(t<1)return{get:()=>{},set:()=>{}};let r=0,e=new Map,s=new Map;const d=(i,a)=>{e.set(i,a),r++,r>t&&(r=0,s=e,e=new Map)};return{get(i){let a=e.get(i);if(a!==void 0)return a;if((a=s.get(i))!==void 0)return d(i,a),a},set(i,a){e.has(i)?e.set(i,a):d(i,a)}}},Ft="!",Ot=":",Ue=Ot.length,We=t=>{const{prefix:r,experimentalParseClassName:e}=t;let s=d=>{const i=[];let a=0,y=0,m=0,p;for(let C=0;C<d.length;C++){let L=d[C];if(a===0&&y===0){if(L===Ot){i.push(d.slice(m,C)),m=C+Ue;continue}if(L==="/"){p=C;continue}}L==="["?a++:L==="]"?a--:L==="("?y++:L===")"&&y--}const k=i.length===0?d:d.substring(m),o=Ke(k),I=o!==k,B=p&&p>m?p-m:void 0;return{modifiers:i,hasImportantModifier:I,baseClassName:o,maybePostfixModifierPosition:B}};if(r){const d=r+Ot,i=s;s=a=>a.startsWith(d)?i(a.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(e){const d=s;s=i=>e({className:i,parseClassName:d})}return s},Ke=t=>t.endsWith(Ft)?t.substring(0,t.length-1):t.startsWith(Ft)?t.substring(1):t,Xe=t=>{const r=Object.fromEntries(t.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const d=[];let i=[];return s.forEach(a=>{a[0]==="["||r[a]?(d.push(...i.sort(),a),i=[]):i.push(a)}),d.push(...i.sort()),d}},Ze=t=>({cache:qe(t.cacheSize),parseClassName:We(t),sortModifiers:Xe(t),...Ve(t)}),Qe=/\s+/,Je=(t,r)=>{const{parseClassName:e,getClassGroupId:s,getConflictingClassGroupIds:d,sortModifiers:i}=r,a=[],y=t.trim().split(Qe);let m="";for(let p=y.length-1;p>=0;p-=1){const k=y[p],{isExternal:o,modifiers:I,hasImportantModifier:B,baseClassName:C,maybePostfixModifierPosition:L}=e(k);if(o){m=k+(m.length>0?" "+m:m);continue}let O=!!L,v=s(O?C.substring(0,L):C);if(!v){if(!O){m=k+(m.length>0?" "+m:m);continue}if(v=s(C),!v){m=k+(m.length>0?" "+m:m);continue}O=!1}const nt=i(I).join(":"),Y=B?nt+Ft:nt,W=Y+v;if(a.includes(W))continue;a.push(W);const tt=d(v,O);for(let A=0;A<tt.length;++A){const K=tt[A];a.push(Y+K)}m=k+(m.length>0?" "+m:m)}return m};function to(){let t=0,r,e,s="";for(;t<arguments.length;)(r=arguments[t++])&&(e=ie(r))&&(s&&(s+=" "),s+=e);return s}const ie=t=>{if(typeof t=="string")return t;let r,e="";for(let s=0;s<t.length;s++)t[s]&&(r=ie(t[s]))&&(e&&(e+=" "),e+=r);return e};function eo(t,...r){let e,s,d,i=a;function a(m){const p=r.reduce((k,o)=>o(k),t());return e=Ze(p),s=e.cache.get,d=e.cache.set,i=y,y(m)}function y(m){const p=s(m);if(p)return p;const k=Je(m,e);return d(m,k),k}return function(){return i(to.apply(null,arguments))}}const R=t=>{const r=e=>e[t]||[];return r.isThemeGetter=!0,r},le=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ce=/^\((?:(\w[\w-]*):)?(.+)\)$/i,oo=/^\d+\/\d+$/,ro=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,so=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,no=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,ao=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,io=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,kt=t=>oo.test(t),b=t=>!!t&&!Number.isNaN(Number(t)),pt=t=>!!t&&Number.isInteger(Number(t)),Lt=t=>t.endsWith("%")&&b(t.slice(0,-1)),lt=t=>ro.test(t),lo=()=>!0,co=t=>so.test(t)&&!no.test(t),de=()=>!1,uo=t=>ao.test(t),mo=t=>io.test(t),po=t=>!l(t)&&!c(t),fo=t=>Ct(t,pe,de),l=t=>le.test(t),ht=t=>Ct(t,fe,co),Pt=t=>Ct(t,wo,b),ee=t=>Ct(t,ue,de),go=t=>Ct(t,me,mo),Rt=t=>Ct(t,ge,uo),c=t=>ce.test(t),Tt=t=>Et(t,fe),ho=t=>Et(t,vo),oe=t=>Et(t,ue),bo=t=>Et(t,pe),yo=t=>Et(t,me),zt=t=>Et(t,ge,!0),Ct=(t,r,e)=>{const s=le.exec(t);return s?s[1]?r(s[1]):e(s[2]):!1},Et=(t,r,e=!1)=>{const s=ce.exec(t);return s?s[1]?r(s[1]):e:!1},ue=t=>t==="position"||t==="percentage",me=t=>t==="image"||t==="url",pe=t=>t==="length"||t==="size"||t==="bg-size",fe=t=>t==="length",wo=t=>t==="number",vo=t=>t==="family-name",ge=t=>t==="shadow",xo=()=>{const t=R("color"),r=R("font"),e=R("text"),s=R("font-weight"),d=R("tracking"),i=R("leading"),a=R("breakpoint"),y=R("container"),m=R("spacing"),p=R("radius"),k=R("shadow"),o=R("inset-shadow"),I=R("text-shadow"),B=R("drop-shadow"),C=R("blur"),L=R("perspective"),O=R("aspect"),v=R("ease"),nt=R("animate"),Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],tt=()=>[...W(),c,l],A=()=>["auto","hidden","clip","visible","scroll"],K=()=>["auto","contain","none"],f=()=>[c,l,m],H=()=>[kt,"full","auto",...f()],ft=()=>[pt,"none","subgrid",c,l],yt=()=>["auto",{span:["full",pt,c,l]},pt,c,l],et=()=>[pt,"auto",c,l],V=()=>["auto","min","max","fr",c,l],ct=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],q=()=>["start","end","center","stretch","center-safe","end-safe"],w=()=>["auto",...f()],T=()=>[kt,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...f()],u=()=>[t,c,l],ot=()=>[...W(),oe,ee,{position:[c,l]}],D=()=>["no-repeat",{repeat:["","x","y","space","round"]}],rt=()=>["auto","cover","contain",bo,fo,{size:[c,l]}],dt=()=>[Lt,Tt,ht],g=()=>["","none","full",p,c,l],x=()=>["",b,Tt,ht],E=()=>["solid","dashed","dotted","double"],P=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],M=()=>[b,Lt,oe,ee],S=()=>["","none",C,c,l],X=()=>["none",b,c,l],at=()=>["none",b,c,l],ut=()=>[b,c,l],Z=()=>[kt,"full",...f()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[lt],breakpoint:[lt],color:[lo],container:[lt],"drop-shadow":[lt],ease:["in","out","in-out"],font:[po],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[lt],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[lt],shadow:[lt],spacing:["px",b],text:[lt],"text-shadow":[lt],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",kt,l,c,O]}],container:["container"],columns:[{columns:[b,l,c,y]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:tt()}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:K()}],"overscroll-x":[{"overscroll-x":K()}],"overscroll-y":[{"overscroll-y":K()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:H()}],"inset-x":[{"inset-x":H()}],"inset-y":[{"inset-y":H()}],start:[{start:H()}],end:[{end:H()}],top:[{top:H()}],right:[{right:H()}],bottom:[{bottom:H()}],left:[{left:H()}],visibility:["visible","invisible","collapse"],z:[{z:[pt,"auto",c,l]}],basis:[{basis:[kt,"full","auto",y,...f()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[b,kt,"auto","initial","none",l]}],grow:[{grow:["",b,c,l]}],shrink:[{shrink:["",b,c,l]}],order:[{order:[pt,"first","last","none",c,l]}],"grid-cols":[{"grid-cols":ft()}],"col-start-end":[{col:yt()}],"col-start":[{"col-start":et()}],"col-end":[{"col-end":et()}],"grid-rows":[{"grid-rows":ft()}],"row-start-end":[{row:yt()}],"row-start":[{"row-start":et()}],"row-end":[{"row-end":et()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":V()}],"auto-rows":[{"auto-rows":V()}],gap:[{gap:f()}],"gap-x":[{"gap-x":f()}],"gap-y":[{"gap-y":f()}],"justify-content":[{justify:[...ct(),"normal"]}],"justify-items":[{"justify-items":[...q(),"normal"]}],"justify-self":[{"justify-self":["auto",...q()]}],"align-content":[{content:["normal",...ct()]}],"align-items":[{items:[...q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...q(),{baseline:["","last"]}]}],"place-content":[{"place-content":ct()}],"place-items":[{"place-items":[...q(),"baseline"]}],"place-self":[{"place-self":["auto",...q()]}],p:[{p:f()}],px:[{px:f()}],py:[{py:f()}],ps:[{ps:f()}],pe:[{pe:f()}],pt:[{pt:f()}],pr:[{pr:f()}],pb:[{pb:f()}],pl:[{pl:f()}],m:[{m:w()}],mx:[{mx:w()}],my:[{my:w()}],ms:[{ms:w()}],me:[{me:w()}],mt:[{mt:w()}],mr:[{mr:w()}],mb:[{mb:w()}],ml:[{ml:w()}],"space-x":[{"space-x":f()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":f()}],"space-y-reverse":["space-y-reverse"],size:[{size:T()}],w:[{w:[y,"screen",...T()]}],"min-w":[{"min-w":[y,"screen","none",...T()]}],"max-w":[{"max-w":[y,"screen","none","prose",{screen:[a]},...T()]}],h:[{h:["screen","lh",...T()]}],"min-h":[{"min-h":["screen","lh","none",...T()]}],"max-h":[{"max-h":["screen","lh",...T()]}],"font-size":[{text:["base",e,Tt,ht]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,c,Pt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Lt,l]}],"font-family":[{font:[ho,l,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,c,l]}],"line-clamp":[{"line-clamp":[b,"none",c,Pt]}],leading:[{leading:[i,...f()]}],"list-image":[{"list-image":["none",c,l]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",c,l]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:u()}],"text-color":[{text:u()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...E(),"wavy"]}],"text-decoration-thickness":[{decoration:[b,"from-font","auto",c,ht]}],"text-decoration-color":[{decoration:u()}],"underline-offset":[{"underline-offset":[b,"auto",c,l]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:f()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",c,l]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",c,l]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ot()}],"bg-repeat":[{bg:D()}],"bg-size":[{bg:rt()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},pt,c,l],radial:["",c,l],conic:[pt,c,l]},yo,go]}],"bg-color":[{bg:u()}],"gradient-from-pos":[{from:dt()}],"gradient-via-pos":[{via:dt()}],"gradient-to-pos":[{to:dt()}],"gradient-from":[{from:u()}],"gradient-via":[{via:u()}],"gradient-to":[{to:u()}],rounded:[{rounded:g()}],"rounded-s":[{"rounded-s":g()}],"rounded-e":[{"rounded-e":g()}],"rounded-t":[{"rounded-t":g()}],"rounded-r":[{"rounded-r":g()}],"rounded-b":[{"rounded-b":g()}],"rounded-l":[{"rounded-l":g()}],"rounded-ss":[{"rounded-ss":g()}],"rounded-se":[{"rounded-se":g()}],"rounded-ee":[{"rounded-ee":g()}],"rounded-es":[{"rounded-es":g()}],"rounded-tl":[{"rounded-tl":g()}],"rounded-tr":[{"rounded-tr":g()}],"rounded-br":[{"rounded-br":g()}],"rounded-bl":[{"rounded-bl":g()}],"border-w":[{border:x()}],"border-w-x":[{"border-x":x()}],"border-w-y":[{"border-y":x()}],"border-w-s":[{"border-s":x()}],"border-w-e":[{"border-e":x()}],"border-w-t":[{"border-t":x()}],"border-w-r":[{"border-r":x()}],"border-w-b":[{"border-b":x()}],"border-w-l":[{"border-l":x()}],"divide-x":[{"divide-x":x()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":x()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...E(),"hidden","none"]}],"divide-style":[{divide:[...E(),"hidden","none"]}],"border-color":[{border:u()}],"border-color-x":[{"border-x":u()}],"border-color-y":[{"border-y":u()}],"border-color-s":[{"border-s":u()}],"border-color-e":[{"border-e":u()}],"border-color-t":[{"border-t":u()}],"border-color-r":[{"border-r":u()}],"border-color-b":[{"border-b":u()}],"border-color-l":[{"border-l":u()}],"divide-color":[{divide:u()}],"outline-style":[{outline:[...E(),"none","hidden"]}],"outline-offset":[{"outline-offset":[b,c,l]}],"outline-w":[{outline:["",b,Tt,ht]}],"outline-color":[{outline:u()}],shadow:[{shadow:["","none",k,zt,Rt]}],"shadow-color":[{shadow:u()}],"inset-shadow":[{"inset-shadow":["none",o,zt,Rt]}],"inset-shadow-color":[{"inset-shadow":u()}],"ring-w":[{ring:x()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:u()}],"ring-offset-w":[{"ring-offset":[b,ht]}],"ring-offset-color":[{"ring-offset":u()}],"inset-ring-w":[{"inset-ring":x()}],"inset-ring-color":[{"inset-ring":u()}],"text-shadow":[{"text-shadow":["none",I,zt,Rt]}],"text-shadow-color":[{"text-shadow":u()}],opacity:[{opacity:[b,c,l]}],"mix-blend":[{"mix-blend":[...P(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":P()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[b]}],"mask-image-linear-from-pos":[{"mask-linear-from":M()}],"mask-image-linear-to-pos":[{"mask-linear-to":M()}],"mask-image-linear-from-color":[{"mask-linear-from":u()}],"mask-image-linear-to-color":[{"mask-linear-to":u()}],"mask-image-t-from-pos":[{"mask-t-from":M()}],"mask-image-t-to-pos":[{"mask-t-to":M()}],"mask-image-t-from-color":[{"mask-t-from":u()}],"mask-image-t-to-color":[{"mask-t-to":u()}],"mask-image-r-from-pos":[{"mask-r-from":M()}],"mask-image-r-to-pos":[{"mask-r-to":M()}],"mask-image-r-from-color":[{"mask-r-from":u()}],"mask-image-r-to-color":[{"mask-r-to":u()}],"mask-image-b-from-pos":[{"mask-b-from":M()}],"mask-image-b-to-pos":[{"mask-b-to":M()}],"mask-image-b-from-color":[{"mask-b-from":u()}],"mask-image-b-to-color":[{"mask-b-to":u()}],"mask-image-l-from-pos":[{"mask-l-from":M()}],"mask-image-l-to-pos":[{"mask-l-to":M()}],"mask-image-l-from-color":[{"mask-l-from":u()}],"mask-image-l-to-color":[{"mask-l-to":u()}],"mask-image-x-from-pos":[{"mask-x-from":M()}],"mask-image-x-to-pos":[{"mask-x-to":M()}],"mask-image-x-from-color":[{"mask-x-from":u()}],"mask-image-x-to-color":[{"mask-x-to":u()}],"mask-image-y-from-pos":[{"mask-y-from":M()}],"mask-image-y-to-pos":[{"mask-y-to":M()}],"mask-image-y-from-color":[{"mask-y-from":u()}],"mask-image-y-to-color":[{"mask-y-to":u()}],"mask-image-radial":[{"mask-radial":[c,l]}],"mask-image-radial-from-pos":[{"mask-radial-from":M()}],"mask-image-radial-to-pos":[{"mask-radial-to":M()}],"mask-image-radial-from-color":[{"mask-radial-from":u()}],"mask-image-radial-to-color":[{"mask-radial-to":u()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":W()}],"mask-image-conic-pos":[{"mask-conic":[b]}],"mask-image-conic-from-pos":[{"mask-conic-from":M()}],"mask-image-conic-to-pos":[{"mask-conic-to":M()}],"mask-image-conic-from-color":[{"mask-conic-from":u()}],"mask-image-conic-to-color":[{"mask-conic-to":u()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ot()}],"mask-repeat":[{mask:D()}],"mask-size":[{mask:rt()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",c,l]}],filter:[{filter:["","none",c,l]}],blur:[{blur:S()}],brightness:[{brightness:[b,c,l]}],contrast:[{contrast:[b,c,l]}],"drop-shadow":[{"drop-shadow":["","none",B,zt,Rt]}],"drop-shadow-color":[{"drop-shadow":u()}],grayscale:[{grayscale:["",b,c,l]}],"hue-rotate":[{"hue-rotate":[b,c,l]}],invert:[{invert:["",b,c,l]}],saturate:[{saturate:[b,c,l]}],sepia:[{sepia:["",b,c,l]}],"backdrop-filter":[{"backdrop-filter":["","none",c,l]}],"backdrop-blur":[{"backdrop-blur":S()}],"backdrop-brightness":[{"backdrop-brightness":[b,c,l]}],"backdrop-contrast":[{"backdrop-contrast":[b,c,l]}],"backdrop-grayscale":[{"backdrop-grayscale":["",b,c,l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[b,c,l]}],"backdrop-invert":[{"backdrop-invert":["",b,c,l]}],"backdrop-opacity":[{"backdrop-opacity":[b,c,l]}],"backdrop-saturate":[{"backdrop-saturate":[b,c,l]}],"backdrop-sepia":[{"backdrop-sepia":["",b,c,l]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":f()}],"border-spacing-x":[{"border-spacing-x":f()}],"border-spacing-y":[{"border-spacing-y":f()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",c,l]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[b,"initial",c,l]}],ease:[{ease:["linear","initial",v,c,l]}],delay:[{delay:[b,c,l]}],animate:[{animate:["none",nt,c,l]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[L,c,l]}],"perspective-origin":[{"perspective-origin":tt()}],rotate:[{rotate:X()}],"rotate-x":[{"rotate-x":X()}],"rotate-y":[{"rotate-y":X()}],"rotate-z":[{"rotate-z":X()}],scale:[{scale:at()}],"scale-x":[{"scale-x":at()}],"scale-y":[{"scale-y":at()}],"scale-z":[{"scale-z":at()}],"scale-3d":["scale-3d"],skew:[{skew:ut()}],"skew-x":[{"skew-x":ut()}],"skew-y":[{"skew-y":ut()}],transform:[{transform:[c,l,"","none","gpu","cpu"]}],"transform-origin":[{origin:tt()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Z()}],"translate-x":[{"translate-x":Z()}],"translate-y":[{"translate-y":Z()}],"translate-z":[{"translate-z":Z()}],"translate-none":["translate-none"],accent:[{accent:u()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:u()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",c,l]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":f()}],"scroll-mx":[{"scroll-mx":f()}],"scroll-my":[{"scroll-my":f()}],"scroll-ms":[{"scroll-ms":f()}],"scroll-me":[{"scroll-me":f()}],"scroll-mt":[{"scroll-mt":f()}],"scroll-mr":[{"scroll-mr":f()}],"scroll-mb":[{"scroll-mb":f()}],"scroll-ml":[{"scroll-ml":f()}],"scroll-p":[{"scroll-p":f()}],"scroll-px":[{"scroll-px":f()}],"scroll-py":[{"scroll-py":f()}],"scroll-ps":[{"scroll-ps":f()}],"scroll-pe":[{"scroll-pe":f()}],"scroll-pt":[{"scroll-pt":f()}],"scroll-pr":[{"scroll-pr":f()}],"scroll-pb":[{"scroll-pb":f()}],"scroll-pl":[{"scroll-pl":f()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",c,l]}],fill:[{fill:["none",...u()]}],"stroke-w":[{stroke:[b,Tt,ht,Pt]}],stroke:[{stroke:["none",...u()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},ko=eo(xo);function Ht(...t){return ko(Ne(t))}function Co(t){if(typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],e=document.createElement("style");e.type="text/css",r.appendChild(e),e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}const Eo=t=>{switch(t){case"success":return Mo;case"info":return No;case"warning":return Io;case"error":return Ro;default:return null}},So=Array(12).fill(0),To=({visible:t,className:r})=>n.createElement("div",{className:["sonner-loading-wrapper",r].filter(Boolean).join(" "),"data-visible":t},n.createElement("div",{className:"sonner-spinner"},So.map((e,s)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${s}`})))),Mo=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Io=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),No=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Ro=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),zo=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),Ao=()=>{const[t,r]=n.useState(document.hidden);return n.useEffect(()=>{const e=()=>{r(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),t};let Vt=1;class _o{constructor(){this.subscribe=r=>(this.subscribers.push(r),()=>{const e=this.subscribers.indexOf(r);this.subscribers.splice(e,1)}),this.publish=r=>{this.subscribers.forEach(e=>e(r))},this.addToast=r=>{this.publish(r),this.toasts=[...this.toasts,r]},this.create=r=>{var e;const{message:s,...d}=r,i=typeof(r==null?void 0:r.id)=="number"||((e=r.id)==null?void 0:e.length)>0?r.id:Vt++,a=this.toasts.find(m=>m.id===i),y=r.dismissible===void 0?!0:r.dismissible;return this.dismissedToasts.has(i)&&this.dismissedToasts.delete(i),a?this.toasts=this.toasts.map(m=>m.id===i?(this.publish({...m,...r,id:i,title:s}),{...m,...r,id:i,dismissible:y,title:s}):m):this.addToast({title:s,...d,dismissible:y,id:i}),i},this.dismiss=r=>(r?(this.dismissedToasts.add(r),requestAnimationFrame(()=>this.subscribers.forEach(e=>e({id:r,dismiss:!0})))):this.toasts.forEach(e=>{this.subscribers.forEach(s=>s({id:e.id,dismiss:!0}))}),r),this.message=(r,e)=>this.create({...e,message:r}),this.error=(r,e)=>this.create({...e,message:r,type:"error"}),this.success=(r,e)=>this.create({...e,type:"success",message:r}),this.info=(r,e)=>this.create({...e,type:"info",message:r}),this.warning=(r,e)=>this.create({...e,type:"warning",message:r}),this.loading=(r,e)=>this.create({...e,type:"loading",message:r}),this.promise=(r,e)=>{if(!e)return;let s;e.loading!==void 0&&(s=this.create({...e,promise:r,type:"loading",message:e.loading,description:typeof e.description!="function"?e.description:void 0}));const d=Promise.resolve(r instanceof Function?r():r);let i=s!==void 0,a;const y=d.then(async p=>{if(a=["resolve",p],n.isValidElement(p))i=!1,this.create({id:s,type:"default",message:p});else if(Bo(p)&&!p.ok){i=!1;const o=typeof e.error=="function"?await e.error(`HTTP error! status: ${p.status}`):e.error,I=typeof e.description=="function"?await e.description(`HTTP error! status: ${p.status}`):e.description,C=typeof o=="object"&&!n.isValidElement(o)?o:{message:o};this.create({id:s,type:"error",description:I,...C})}else if(p instanceof Error){i=!1;const o=typeof e.error=="function"?await e.error(p):e.error,I=typeof e.description=="function"?await e.description(p):e.description,C=typeof o=="object"&&!n.isValidElement(o)?o:{message:o};this.create({id:s,type:"error",description:I,...C})}else if(e.success!==void 0){i=!1;const o=typeof e.success=="function"?await e.success(p):e.success,I=typeof e.description=="function"?await e.description(p):e.description,C=typeof o=="object"&&!n.isValidElement(o)?o:{message:o};this.create({id:s,type:"success",description:I,...C})}}).catch(async p=>{if(a=["reject",p],e.error!==void 0){i=!1;const k=typeof e.error=="function"?await e.error(p):e.error,o=typeof e.description=="function"?await e.description(p):e.description,B=typeof k=="object"&&!n.isValidElement(k)?k:{message:k};this.create({id:s,type:"error",description:o,...B})}}).finally(()=>{i&&(this.dismiss(s),s=void 0),e.finally==null||e.finally.call(e)}),m=()=>new Promise((p,k)=>y.then(()=>a[0]==="reject"?k(a[1]):p(a[1])).catch(k));return typeof s!="string"&&typeof s!="number"?{unwrap:m}:Object.assign(s,{unwrap:m})},this.custom=(r,e)=>{const s=(e==null?void 0:e.id)||Vt++;return this.create({jsx:r(s),id:s,...e}),s},this.getActiveToasts=()=>this.toasts.filter(r=>!this.dismissedToasts.has(r.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const F=new _o,jo=(t,r)=>{const e=(r==null?void 0:r.id)||Vt++;return F.addToast({title:t,...r,id:e}),e},Bo=t=>t&&typeof t=="object"&&"ok"in t&&typeof t.ok=="boolean"&&"status"in t&&typeof t.status=="number",Lo=jo,Po=()=>F.toasts,Do=()=>F.getActiveToasts(),gr=Object.assign(Lo,{success:F.success,info:F.info,warning:F.warning,error:F.error,custom:F.custom,message:F.message,promise:F.promise,dismiss:F.dismiss,loading:F.loading},{getHistory:Po,getToasts:Do});Co("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function At(t){return t.label!==void 0}const Fo=3,Oo="24px",Ho="16px",re=4e3,Vo=356,$o=14,Go=45,Yo=200;function st(...t){return t.filter(Boolean).join(" ")}function qo(t){const[r,e]=t.split("-"),s=[];return r&&s.push(r),e&&s.push(e),s}const Uo=t=>{var r,e,s,d,i,a,y,m,p;const{invert:k,toast:o,unstyled:I,interacting:B,setHeights:C,visibleToasts:L,heights:O,index:v,toasts:nt,expanded:Y,removeToast:W,defaultRichColors:tt,closeButton:A,style:K,cancelButtonStyle:f,actionButtonStyle:H,className:ft="",descriptionClassName:yt="",duration:et,position:V,gap:ct,expandByDefault:q,classNames:w,icons:T,closeButtonAriaLabel:u="Close toast"}=t,[ot,D]=n.useState(null),[rt,dt]=n.useState(null),[g,x]=n.useState(!1),[E,P]=n.useState(!1),[M,S]=n.useState(!1),[X,at]=n.useState(!1),[ut,Z]=n.useState(!1),[he,_t]=n.useState(0),[be,Gt]=n.useState(0),St=n.useRef(o.duration||et||re),Yt=n.useRef(null),it=n.useRef(null),ye=v===0,we=v+1<=L,$=o.type,wt=o.dismissible!==!1,ve=o.className||"",xe=o.descriptionClassName||"",Mt=n.useMemo(()=>O.findIndex(h=>h.toastId===o.id)||0,[O,o.id]),ke=n.useMemo(()=>{var h;return(h=o.closeButton)!=null?h:A},[o.closeButton,A]),qt=n.useMemo(()=>o.duration||et||re,[o.duration,et]),jt=n.useRef(0),vt=n.useRef(0),Ut=n.useRef(0),xt=n.useRef(null),[Ce,Ee]=V.split("-"),Wt=n.useMemo(()=>O.reduce((h,z,j)=>j>=Mt?h:h+z.height,0),[O,Mt]),Kt=Ao(),Se=o.invert||k,Bt=$==="loading";vt.current=n.useMemo(()=>Mt*ct+Wt,[Mt,Wt]),n.useEffect(()=>{St.current=qt},[qt]),n.useEffect(()=>{x(!0)},[]),n.useEffect(()=>{const h=it.current;if(h){const z=h.getBoundingClientRect().height;return Gt(z),C(j=>[{toastId:o.id,height:z,position:o.position},...j]),()=>C(j=>j.filter(G=>G.toastId!==o.id))}},[C,o.id]),n.useLayoutEffect(()=>{if(!g)return;const h=it.current,z=h.style.height;h.style.height="auto";const j=h.getBoundingClientRect().height;h.style.height=z,Gt(j),C(G=>G.find(_=>_.toastId===o.id)?G.map(_=>_.toastId===o.id?{..._,height:j}:_):[{toastId:o.id,height:j,position:o.position},...G])},[g,o.title,o.description,C,o.id,o.jsx,o.action,o.cancel]);const mt=n.useCallback(()=>{P(!0),_t(vt.current),C(h=>h.filter(z=>z.toastId!==o.id)),setTimeout(()=>{W(o)},Yo)},[o,W,C,vt]);n.useEffect(()=>{if(o.promise&&$==="loading"||o.duration===1/0||o.type==="loading")return;let h;return Y||B||Kt?(()=>{if(Ut.current<jt.current){const G=new Date().getTime()-jt.current;St.current=St.current-G}Ut.current=new Date().getTime()})():(()=>{St.current!==1/0&&(jt.current=new Date().getTime(),h=setTimeout(()=>{o.onAutoClose==null||o.onAutoClose.call(o,o),mt()},St.current))})(),()=>clearTimeout(h)},[Y,B,o,$,Kt,mt]),n.useEffect(()=>{o.delete&&(mt(),o.onDismiss==null||o.onDismiss.call(o,o))},[mt,o.delete]);function Te(){var h;if(T!=null&&T.loading){var z;return n.createElement("div",{className:st(w==null?void 0:w.loader,o==null||(z=o.classNames)==null?void 0:z.loader,"sonner-loader"),"data-visible":$==="loading"},T.loading)}return n.createElement(To,{className:st(w==null?void 0:w.loader,o==null||(h=o.classNames)==null?void 0:h.loader),visible:$==="loading"})}const Me=o.icon||(T==null?void 0:T[$])||Eo($);var Xt,Zt;return n.createElement("li",{tabIndex:0,ref:it,className:st(ft,ve,w==null?void 0:w.toast,o==null||(r=o.classNames)==null?void 0:r.toast,w==null?void 0:w.default,w==null?void 0:w[$],o==null||(e=o.classNames)==null?void 0:e[$]),"data-sonner-toast":"","data-rich-colors":(Xt=o.richColors)!=null?Xt:tt,"data-styled":!(o.jsx||o.unstyled||I),"data-mounted":g,"data-promise":!!o.promise,"data-swiped":ut,"data-removed":E,"data-visible":we,"data-y-position":Ce,"data-x-position":Ee,"data-index":v,"data-front":ye,"data-swiping":M,"data-dismissible":wt,"data-type":$,"data-invert":Se,"data-swipe-out":X,"data-swipe-direction":rt,"data-expanded":!!(Y||q&&g),"data-testid":o.testId,style:{"--index":v,"--toasts-before":v,"--z-index":nt.length-v,"--offset":`${E?he:vt.current}px`,"--initial-height":q?"auto":`${be}px`,...K,...o.style},onDragEnd:()=>{S(!1),D(null),xt.current=null},onPointerDown:h=>{h.button!==2&&(Bt||!wt||(Yt.current=new Date,_t(vt.current),h.target.setPointerCapture(h.pointerId),h.target.tagName!=="BUTTON"&&(S(!0),xt.current={x:h.clientX,y:h.clientY})))},onPointerUp:()=>{var h,z,j;if(X||!wt)return;xt.current=null;const G=Number(((h=it.current)==null?void 0:h.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),It=Number(((z=it.current)==null?void 0:z.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),_=new Date().getTime()-((j=Yt.current)==null?void 0:j.getTime()),U=ot==="x"?G:It,Nt=Math.abs(U)/_;if(Math.abs(U)>=Go||Nt>.11){_t(vt.current),o.onDismiss==null||o.onDismiss.call(o,o),dt(ot==="x"?G>0?"right":"left":It>0?"down":"up"),mt(),at(!0);return}else{var Q,J;(Q=it.current)==null||Q.style.setProperty("--swipe-amount-x","0px"),(J=it.current)==null||J.style.setProperty("--swipe-amount-y","0px")}Z(!1),S(!1),D(null)},onPointerMove:h=>{var z,j,G;if(!xt.current||!wt||((z=window.getSelection())==null?void 0:z.toString().length)>0)return;const _=h.clientY-xt.current.y,U=h.clientX-xt.current.x;var Nt;const Q=(Nt=t.swipeDirections)!=null?Nt:qo(V);!ot&&(Math.abs(U)>1||Math.abs(_)>1)&&D(Math.abs(U)>Math.abs(_)?"x":"y");let J={x:0,y:0};const Qt=gt=>1/(1.5+Math.abs(gt)/20);if(ot==="y"){if(Q.includes("top")||Q.includes("bottom"))if(Q.includes("top")&&_<0||Q.includes("bottom")&&_>0)J.y=_;else{const gt=_*Qt(_);J.y=Math.abs(gt)<Math.abs(_)?gt:_}}else if(ot==="x"&&(Q.includes("left")||Q.includes("right")))if(Q.includes("left")&&U<0||Q.includes("right")&&U>0)J.x=U;else{const gt=U*Qt(U);J.x=Math.abs(gt)<Math.abs(U)?gt:U}(Math.abs(J.x)>0||Math.abs(J.y)>0)&&Z(!0),(j=it.current)==null||j.style.setProperty("--swipe-amount-x",`${J.x}px`),(G=it.current)==null||G.style.setProperty("--swipe-amount-y",`${J.y}px`)}},ke&&!o.jsx&&$!=="loading"?n.createElement("button",{"aria-label":u,"data-disabled":Bt,"data-close-button":!0,onClick:Bt||!wt?()=>{}:()=>{mt(),o.onDismiss==null||o.onDismiss.call(o,o)},className:st(w==null?void 0:w.closeButton,o==null||(s=o.classNames)==null?void 0:s.closeButton)},(Zt=T==null?void 0:T.close)!=null?Zt:zo):null,($||o.icon||o.promise)&&o.icon!==null&&((T==null?void 0:T[$])!==null||o.icon)?n.createElement("div",{"data-icon":"",className:st(w==null?void 0:w.icon,o==null||(d=o.classNames)==null?void 0:d.icon)},o.promise||o.type==="loading"&&!o.icon?o.icon||Te():null,o.type!=="loading"?Me:null):null,n.createElement("div",{"data-content":"",className:st(w==null?void 0:w.content,o==null||(i=o.classNames)==null?void 0:i.content)},n.createElement("div",{"data-title":"",className:st(w==null?void 0:w.title,o==null||(a=o.classNames)==null?void 0:a.title)},o.jsx?o.jsx:typeof o.title=="function"?o.title():o.title),o.description?n.createElement("div",{"data-description":"",className:st(yt,xe,w==null?void 0:w.description,o==null||(y=o.classNames)==null?void 0:y.description)},typeof o.description=="function"?o.description():o.description):null),n.isValidElement(o.cancel)?o.cancel:o.cancel&&At(o.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:o.cancelButtonStyle||f,onClick:h=>{At(o.cancel)&&wt&&(o.cancel.onClick==null||o.cancel.onClick.call(o.cancel,h),mt())},className:st(w==null?void 0:w.cancelButton,o==null||(m=o.classNames)==null?void 0:m.cancelButton)},o.cancel.label):null,n.isValidElement(o.action)?o.action:o.action&&At(o.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:o.actionButtonStyle||H,onClick:h=>{At(o.action)&&(o.action.onClick==null||o.action.onClick.call(o.action,h),!h.defaultPrevented&&mt())},className:st(w==null?void 0:w.actionButton,o==null||(p=o.classNames)==null?void 0:p.actionButton)},o.action.label):null)};function se(){if(typeof window>"u"||typeof document>"u")return"ltr";const t=document.documentElement.getAttribute("dir");return t==="auto"||!t?window.getComputedStyle(document.documentElement).direction:t}function Wo(t,r){const e={};return[t,r].forEach((s,d)=>{const i=d===1,a=i?"--mobile-offset":"--offset",y=i?Ho:Oo;function m(p){["top","right","bottom","left"].forEach(k=>{e[`${a}-${k}`]=typeof p=="number"?`${p}px`:p})}typeof s=="number"||typeof s=="string"?m(s):typeof s=="object"?["top","right","bottom","left"].forEach(p=>{s[p]===void 0?e[`${a}-${p}`]=y:e[`${a}-${p}`]=typeof s[p]=="number"?`${s[p]}px`:s[p]}):m(y)}),e}const Ko=n.forwardRef(function(r,e){const{id:s,invert:d,position:i="bottom-right",hotkey:a=["altKey","KeyT"],expand:y,closeButton:m,className:p,offset:k,mobileOffset:o,theme:I="light",richColors:B,duration:C,style:L,visibleToasts:O=Fo,toastOptions:v,dir:nt=se(),gap:Y=$o,icons:W,containerAriaLabel:tt="Notifications"}=r,[A,K]=n.useState([]),f=n.useMemo(()=>s?A.filter(g=>g.toasterId===s):A.filter(g=>!g.toasterId),[A,s]),H=n.useMemo(()=>Array.from(new Set([i].concat(f.filter(g=>g.position).map(g=>g.position)))),[f,i]),[ft,yt]=n.useState([]),[et,V]=n.useState(!1),[ct,q]=n.useState(!1),[w,T]=n.useState(I!=="system"?I:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),u=n.useRef(null),ot=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),D=n.useRef(null),rt=n.useRef(!1),dt=n.useCallback(g=>{K(x=>{var E;return(E=x.find(P=>P.id===g.id))!=null&&E.delete||F.dismiss(g.id),x.filter(({id:P})=>P!==g.id)})},[]);return n.useEffect(()=>F.subscribe(g=>{if(g.dismiss){requestAnimationFrame(()=>{K(x=>x.map(E=>E.id===g.id?{...E,delete:!0}:E))});return}setTimeout(()=>{Re.flushSync(()=>{K(x=>{const E=x.findIndex(P=>P.id===g.id);return E!==-1?[...x.slice(0,E),{...x[E],...g},...x.slice(E+1)]:[g,...x]})})})}),[A]),n.useEffect(()=>{if(I!=="system"){T(I);return}if(I==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?T("dark"):T("light")),typeof window>"u")return;const g=window.matchMedia("(prefers-color-scheme: dark)");try{g.addEventListener("change",({matches:x})=>{T(x?"dark":"light")})}catch{g.addListener(({matches:E})=>{try{T(E?"dark":"light")}catch(P){console.error(P)}})}},[I]),n.useEffect(()=>{A.length<=1&&V(!1)},[A]),n.useEffect(()=>{const g=x=>{var E;if(a.every(S=>x[S]||x.code===S)){var M;V(!0),(M=u.current)==null||M.focus()}x.code==="Escape"&&(document.activeElement===u.current||(E=u.current)!=null&&E.contains(document.activeElement))&&V(!1)};return document.addEventListener("keydown",g),()=>document.removeEventListener("keydown",g)},[a]),n.useEffect(()=>{if(u.current)return()=>{D.current&&(D.current.focus({preventScroll:!0}),D.current=null,rt.current=!1)}},[u.current]),n.createElement("section",{ref:e,"aria-label":`${tt} ${ot}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},H.map((g,x)=>{var E;const[P,M]=g.split("-");return f.length?n.createElement("ol",{key:g,dir:nt==="auto"?se():nt,tabIndex:-1,ref:u,className:p,"data-sonner-toaster":!0,"data-sonner-theme":w,"data-y-position":P,"data-x-position":M,style:{"--front-toast-height":`${((E=ft[0])==null?void 0:E.height)||0}px`,"--width":`${Vo}px`,"--gap":`${Y}px`,...L,...Wo(k,o)},onBlur:S=>{rt.current&&!S.currentTarget.contains(S.relatedTarget)&&(rt.current=!1,D.current&&(D.current.focus({preventScroll:!0}),D.current=null))},onFocus:S=>{S.target instanceof HTMLElement&&S.target.dataset.dismissible==="false"||rt.current||(rt.current=!0,D.current=S.relatedTarget)},onMouseEnter:()=>V(!0),onMouseMove:()=>V(!0),onMouseLeave:()=>{ct||V(!1)},onDragEnd:()=>V(!1),onPointerDown:S=>{S.target instanceof HTMLElement&&S.target.dataset.dismissible==="false"||q(!0)},onPointerUp:()=>q(!1)},f.filter(S=>!S.position&&x===0||S.position===g).map((S,X)=>{var at,ut;return n.createElement(Uo,{key:S.id,icons:W,index:X,toast:S,defaultRichColors:B,duration:(at=v==null?void 0:v.duration)!=null?at:C,className:v==null?void 0:v.className,descriptionClassName:v==null?void 0:v.descriptionClassName,invert:d,visibleToasts:O,closeButton:(ut=v==null?void 0:v.closeButton)!=null?ut:m,interacting:ct,position:g,style:v==null?void 0:v.style,unstyled:v==null?void 0:v.unstyled,classNames:v==null?void 0:v.classNames,cancelButtonStyle:v==null?void 0:v.cancelButtonStyle,actionButtonStyle:v==null?void 0:v.actionButtonStyle,closeButtonAriaLabel:v==null?void 0:v.closeButtonAriaLabel,removeToast:dt,toasts:f.filter(Z=>Z.position==S.position),heights:ft.filter(Z=>Z.position==S.position),setHeights:yt,expandByDefault:y,gap:Y,expanded:et,swipeDirections:r.swipeDirections})})):null}))});function Xo({className:t}){return N.jsx("svg",{width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:N.jsx("path",{d:"M22.166 8.07005L20.521 6.42505L13.999 12.947L7.47801 6.42505L5.83301 8.07005L12.355 14.592L5.83301 21.113L7.47801 22.758L14 16.236L20.522 22.758L22.167 21.113L15.645 14.591L22.166 8.07005Z",fill:"currentColor"})})}function Zo({className:t}){return N.jsx("svg",{width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:N.jsx("path",{d:"M13.6705 2.5C13.0528 2.50281 12.4613 2.75018 12.0255 3.188L2.69253 12.522C2.47331 12.7362 2.29912 12.9921 2.18019 13.2746C2.06126 13.5571 2 13.8605 2 14.167C2 14.4735 2.06126 14.7769 2.18019 15.0594C2.29912 15.3419 2.47331 15.5978 2.69253 15.812L12.0255 25.145C12.2397 25.3642 12.4956 25.5384 12.7781 25.6573C13.0606 25.7763 13.364 25.8375 13.6705 25.8375C13.977 25.8375 14.2804 25.7763 14.5629 25.6573C14.8454 25.5384 15.1013 25.3642 15.3155 25.145L24.6485 15.812C24.8677 15.5978 25.0419 15.3419 25.1609 15.0594C25.2798 14.7769 25.341 14.4735 25.341 14.167C25.341 13.8605 25.2798 13.5571 25.1609 13.2746C25.0419 12.9921 24.8677 12.7362 24.6485 12.522L15.3155 3.188C14.8797 2.75018 14.6208 2.50281 13.6705 2.5ZM12.836 8.66601H15.169V15.666H12.836V8.66601ZM12.836 17.999H15.169V20.333H12.836V17.999Z",fill:"#DF0000"})})}function Qo({className:t}){return N.jsx("svg",{width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:N.jsx("path",{d:"M15.166 11H12.833V8.66601H15.166M15.166 20.333H12.833V13.333H15.166M14 2.83301C11.6925 2.83281 9.43669 3.5169 7.51793 4.79877C5.59917 6.08063 4.10362 7.9027 3.22042 10.0345C2.33722 12.1664 2.10603 14.5123 2.55609 16.7755C3.00615 19.0388 4.11725 21.1177 5.74887 22.7495C7.38049 24.3812 9.45935 25.4925 11.7225 25.9428C13.9858 26.393 16.3316 26.162 18.4636 25.279C20.5955 24.396 22.4177 22.9006 23.6997 20.9819C24.9818 19.0633 25.666 16.8076 25.666 14.5C25.666 11.4059 24.437 8.4385 22.2492 6.25055C20.0614 4.06259 17.0941 2.83327 14 2.83301Z",fill:"#2356C2"})})}function Jo({className:t}){return N.jsx("svg",{className:Ht("animate-spin",t),width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:N.jsx("path",{d:"M14 2.83301C11.6925 2.83281 9.43669 3.5169 7.51793 4.79877C5.59917 6.08063 4.10362 7.9027 3.22042 10.0345C2.33722 12.1664 2.10603 14.5123 2.55609 16.7755C3.00615 18.9088 4.11725 20.9877 5.74887 22.7495C7.38049 24.3812 9.45935 25.4925 11.7225 25.9428C13.9858 26.393 16.3316 26.162 18.4636 25.279C20.5955 24.396 22.4177 22.9006 23.6997 20.9819C24.9818 19.0633 25.666 16.8076 25.666 14.5",stroke:"#3A3E63",strokeWidth:"2",strokeLinecap:"round"})})}function tr({className:t}){return N.jsx("svg",{width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:N.jsx("path",{d:"M14 2.703C11.6925 2.70281 9.43669 3.38689 7.51793 4.66876C5.59917 5.95063 4.10362 7.77269 3.22042 9.90454C2.33722 12.0364 2.10603 14.3823 2.55609 16.6455C3.00615 18.9088 4.11725 20.9877 5.74887 22.6195C7.38049 24.2512 9.45935 25.3625 11.7225 25.8128C13.9858 26.263 16.3316 26.032 18.4636 25.149C20.5955 24.266 22.4177 22.7706 23.6997 20.8519C24.9818 18.9333 25.666 16.6776 25.666 14.37C25.6573 11.2786 24.4255 8.31623 22.2396 6.13016C20.0537 3.94409 17.0915 2.71197 14 2.703ZM11.666 20.203L5.83304 14.37L7.47804 12.725L11.666 16.903L20.521 8.048L22.166 9.703L11.666 20.203Z",fill:"#1EA702"})})}function er({className:t}){return N.jsx("svg",{width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:N.jsx("path",{d:"M14.003 2.83301C13.3853 2.83582 12.7938 3.08319 12.358 3.52101L3.02504 12.855C2.80583 13.0692 2.63164 13.3251 2.51271 13.6076C2.39378 13.8901 2.33252 14.1935 2.33252 14.5C2.33252 14.8065 2.39378 15.1099 2.51271 15.3924C2.63164 15.6749 2.80583 15.9308 3.02504 16.145L12.358 25.478C12.5723 25.6972 12.8281 25.8714 13.1106 25.9903C13.3931 26.1093 13.6965 26.1705 14.003 26.1705C14.3096 26.1705 14.613 26.1093 14.8955 25.9903C15.178 25.8714 15.4338 25.6972 15.648 25.478L24.981 16.145C25.2003 15.9308 25.3745 15.6749 25.4934 15.3924C25.6123 15.1099 25.6736 14.8065 25.6736 14.5C25.6736 14.1935 25.6123 13.8901 25.4934 13.6076C25.3745 13.3251 25.2003 13.0692 24.981 12.855L15.648 3.52101C15.2122 3.08319 14.6208 2.83582 14.003 2.83301ZM12.836 8.66601H15.169V15.666H12.836V8.66601ZM12.836 17.999H15.169V20.333H12.836V17.999Z",fill:"#F88E00"})})}function hr(){const{isRTL:t}=ze();return N.jsx(Ko,{position:t?"bottom-left":"bottom-right",dir:t?"rtl":"ltr",offset:t?{right:-20}:{left:-20},closeButton:!0,icons:{error:N.jsx(Zo,{}),success:N.jsx(tr,{}),info:N.jsx(Qo,{}),warning:N.jsx(er,{}),loading:N.jsx(Jo,{}),close:N.jsx(Xo,{})},toastOptions:{unstyled:!0,classNames:{error:"tw:bg-[#FDF1F0]! tw:text-[#DF0000]! tw:border-[#DF0000]! tw:px-[70px]!",success:"tw:bg-[#F0FAF7]! tw:text-[#1EA702]! tw:border-[#1EA702]! tw:px-[70px]!",info:"tw:bg-[#F8FAFF]! tw:text-[#2356C2]! tw:border-[#2356C2]! tw:px-[70px]!",warning:"tw:bg-[#FFF9F0]! tw:text-[#F88E00]! tw:border-[#F88E00]! tw:px-[70px]!",loading:"tw:bg-[#E4EBF2]! tw:text-[#3A3E63]! tw:border-[#3A3E63]! tw:px-[70px]!",toast:"tw:bg-[#E4EBF2] tw:text-[#3A3E63] tw:w-max tw:max-w-[85vw] tw:border-t-3 tw:border-[#3A3E63] tw:shadow-[0px_3px_6px_0px_rgba(0,0,0,0.16)] tw:pl-[70px] tw:pr-[20px] tw:py-[27.5px]",icon:Ht("tw:absolute tw:top-1/2",t?"tw:left-[calc(100%-32px)] tw:-translate-x-1/2 tw:-translate-y-1/2":"tw:left-[32px] tw:-translate-x-1/2 tw:-translate-y-1/2"),closeButton:Ht("tw:absolute tw:top-1/2",t?"tw:left-[24px]":"tw:right-[24px]","tw:-translate-y-1/2 tw:cursor-pointer"),title:"tw:text-[20px] tw:font-bold tw:font-roboto",description:"tw:font-roboto"}}})}export{hr as C,cr as a,sr as b,Ht as c,ir as d,fr as e,dr as f,nr as g,ur as h,pr as i,mr as j,ar as k,gr as t,lr as u};
