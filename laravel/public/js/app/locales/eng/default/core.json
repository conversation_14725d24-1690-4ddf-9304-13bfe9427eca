{"manage-email-preferences": "Manage E-mail Preferences", "something-went-wrong": "Something went wrong.", "status": "Status", "date-time": "Date & Time", "action": "action", "comment": "comment", "save": "save", "checkout": "CHECKOUT", "employee": "Employee", "label": "Label", "start-date": "Start Date", "start-time": "Start Time", "duration-minutes": "Duration (Minutes)", "frequency": "Frequency", "doesnt-repeat": "Doesn't Repeat", "every-day": "Every Day", "every-week": "Every Week", "every-month": "Every Month", "custom": "Custom", "every": "Every", "unit": "Unit", "day": "Day", "week": "Week", "month": "Month", "ends": "Ends", "never": "Never", "on-specific-date": "On specific date", "after": "After", "occurrences": "occurrences", "select-client": "Select Client", "add-service": "Add Service", "add-new-client": "Add New Client", "select-service": "Select Service", "edit-service": "Edit Service", "edit-booking": "Edit Booking", "add-client": "add client", "profile": "Profile", "follow-up": "Follow Up", "activity-log": "Activity Log", "search-clients": "Search clients by name, code, email, or phone number", "search-by-service": "Search by service", "duration": "Duration", "minutes": "Minutes", "price": "Price", "discount": "Discount", "total": "Total:", "today": "Today", "show-weekends": "Show Weekends", "show-cancelled-bookings": "Show Cancelled Bookings", "clear-client-filter": "Clear Client Filter", "auto-height-iframe": "Auto-height Iframe", "booking-client": "Booking Client", "client-profile": "client profile", "edit": "Edit", "delete": "Delete", "close": "Close", "staff": "Employees", "error-illustration": "Error illustration", "try-again": "Try Again", "payslip": "Pay Slips", "leave_application": "Leave Applications", "request": "Requests", "subscribed": "Subscribed", "unsubscribed": "Unsubscribed", "no-active-plugins": "No active plugins", "search-email-groups": "Search email groups...", "search-placeholder": "Search...", "email-groups": "Email Groups", "success-resubscribed": "You have successfully resubscribed to this Email. You will continue receiving {{entity}} from the E-mails groups list.", "success-unsubscribed": "You have successfully unsubscribed from this Email. You will no longer receive {{entity}} from the E-mails groups list.", "unsubscribe-date": "Unsubscribe Date", "actions": "Actions", "subscribe": "Subscribe", "unsubscribe": "Unsubscribe", "no-email-groups-match-your-search": "No email groups match your search.", "search-documents-by-name": "Search documents by name...", "print": "Print", "send-email": "Send Email", "language": "Language", "active": "Active", "inactive": "Inactive", "contracts": "Contracts", "loans": "Loans", "payslips": "Payslips", "local": "Local", "english-abbreviation": "EN", "arabic-abbreviation": "AR", "no-document-found": "No document found for this template.", "unsubscribe-from-this-email": "Unsubscribe from this email?", "are-you-sure-you-want-to-unsubscribe-from-this-email": "Are you sure you want to unsubscribe from this email?", "cancel": "Cancel", "no-contracts-found": "No Contract available for this employee", "no-loans-found": "No Loan available for this employee", "no-payslips-found": "No Payslip available for this employee", "no-staff-found": "Not available for this employee", "no-local-found": "No Local available for this employee", "documents-and-emails": "Documents & Emails", "choose-a-document-to-send-via-email-or-print-for": "Choose a document to send via email or print for", "unsubscribe-from-type": "Unsubscribe from {{type}}", "system-will-stop-sending-emails-for-payslips-to-email": "The system will stop sending emails for payslips to {{email}}", "system-will-stop-sending-emails-for-leave-applications-to-email": "The system will stop sending emails for leave applications to {{email}}", "system-will-stop-sending-emails-for-requests-to-email": "The system will stop sending emails for requests to {{email}}", "showing-items": "Showing {{start}}-{{end}} of {{total}}", "page-of": "Page {{current}} of {{total}}", "go-to-previous-page": "Go to previous page", "go-to-next-page": "Go to next page", "filter-by-status": "Filter by Status", "search-and-filter": "Search & Filter", "hide": "<PERSON>de", "clear-all": "Clear All", "search": "Search", "sort": "Sort", "search-by-email-group": "Search by Email Group", "success": "Success", "no-documents-available": "No Documents Available", "no-documents-available-description": "There are no documents available for this employee at the moment. Documents may be added by administrators or through the system workflow.", "no-documents-found": "No Documents Found", "no-documents-found-description": "No documents match your search for \"{{search}}\". Try adjusting your search terms or browse all available documents.", "toast-uploading-file": "Uploading file...", "toast-file-upload-success": "File uploaded successfully", "toast-file-upload-error": "Failed to upload file", "toast-invalid-file-type": "Invalid file type", "toast-invalid-file-size": "File size must be less than 10 MB", "toast-loading": "Loading ...", "toast-loaded-success": "Loaded successfully", "toast-save-success": "Saved successfully", "toast-load-error": "Failed to load", "toast-save-error": "Failed to save", "toast-deleting-file": "Deleting file...", "toast-delete-file-success": "File deleted successfully", "toast-delete-file-error": "Failed to delete file", "drop-file-here": "Drop file here or", "select-from-device": "select from your device", "supported-formats": "Supported Formats: xlsx", "drop-here": "Drop here", "choose-file": "Choose <PERSON>", "successfully-attached": "File successfully attached", "file-information": "Attached Mudad File", "file-type": "File Type", "uploaded-by": "Uploaded By", "period": "Period", "uploading": "Uploading...", "file-size": "File size", "uploaded-on": "Uploaded on", "bookings": "Bookings", "bookingWorkflow": {"label": "Booking Workflow", "description": "Set up booking workflows and assign employees with ease.", "head-description": "Choose how many services can be booked at once and how staff assignments should work.", "staffTitle": "How should staff be assigned to bookings?", "staffTypes": {"one_employee": {"title": "Per booking", "desc": "One staff member will handle all services in a booking."}, "multiple_employees": {"title": "Per service", "desc": "Each service in a booking can have a different staff member."}, "no_employee": {"title": "No staff assignment", "desc": "Bookings will be handled automatically by staff members."}}, "serviceTitle": "How many services should be included in each booking?", "serviceTypes": {"multiple_services": {"title": "Multiple services", "desc": "Allow booking more than one service or a package per appointment."}, "one_service": {"title": "Single service", "desc": "Limit each booking to just one selected service."}, "default_service": {"title": "Default service", "desc": "Automatically assign a predefined service to every booking."}}, "addNewService": "Add New Service", "save": "Save"}, "serviceManagement": {"label": "Service Management", "description": "Quick add and manage services with key details in one place.", "subtitle": "Set durations and pricing, assign employees, and configure booking forms and staff permissions.", "edit": "Edit Service", "add": "Add New Service"}, "bookingPackages": {"label": "Booking Packages", "description": "Create and organize booking packages with assigned services.", "subtitle": "Group services into a package with bundled deal, apply special pricing, or ensure certain services are booked together.", "addPackage": "Add Package", "namePlaceholder": "e.g. Summer Package", "edit": "Edit Package", "add": "Add New Package"}, "shiftRoaster": {"label": "Shift Roaster", "description": "Schedule shifts and manage employee availability for bookings.", "subtitle": "Schedule shifts and manage employee availability for bookings.", "defaultShift": "Default <PERSON>"}, "clearAll": "Clear All", "filter": "Filter", "services": "Services", "all": "All", "clear_all": "CLEAR ALL", "filters_applied": " Filters Applied", "packages_filters": "Packages Filters", "required": "Required", "must_be_number": "Must be a number", "at_least_one_service": "At least one service is required", "name": "Name", "image": "Image", "servicesList": "List of Services", "service": "Service", "saveClose": "SAVE & CLOSE", "saveAddMore": "SAVE & ADD MORE", "mustBeNumber": "Must be a number", "atLeastOneService": "At least one service is required", "relatedForms": "Related Forms", "addForm": "Add Form", "none": "None", "tax1": "Tax1", "tax2": "Tax2", "assignedEmployees": "Assigned Employees", "category": "Category", "selectEmployee": "Select Employee", "egSummerService": "e.g. Summer Service", "egSummerCategory": "e.g. Summer Category", "key": "Key", "description": "Description", "formInformation": "Form Information", "formFields": "Form Fields", "formFillingTime": "Form Filling Time", "duringBooking": "During Booking", "duringService": "During Service", "permissions": "Permissions", "addRecord": "Add Record", "updateRecord": "Update Record", "viewRecord": "View Record", "deleteRecord": "Delete Record", "next": "Next", "placeholder": {"description": "Write your description here..."}, "nameRequired": "Name is required", "keyRequired": "Key is required", "statusRequired": "Status is required", "fillingTimeRequired": "Filling time is required", "serviceForm": "Service Form", "no-records-added": "No Records Added Yet!", "no-records-subtext-prefix": "Use this wizard to quickly add services by clicking", "no-records-subtext-prefix-service": " Use this wizard to quickly add new services.", "add-button-label": "Add", "all-categories": "All Categories", "add-new": "Add New", "unassigned": "Unassigned", "sectionName": "Section Name", "sectionEmpty": "This section is currently empty.", "startAddingFields": "Start adding custom fields by clicking", "openFormBuilder": "Open form Builder", "cancel_capital": "CANCEL", "noCategoriesAdded": "No categories added", "manageRecords": "Manage Records", "managePrintableTemplates": "Manage Printable Templates", "new-client": "New Client", "no-results-found": "No results found", "confirmLeavePage": "You have unsaved changes. Are you sure you want to leave?", "switch-to-classic-mode": "Switch to Classic Mode", "add-block-time": "Add Block Time"}