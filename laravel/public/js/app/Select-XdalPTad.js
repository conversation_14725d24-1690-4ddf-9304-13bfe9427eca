import{r as d,k as ee,a7 as Ce,a8 as Z,a9 as di,aa as st,ab as Be,ac as ci,$ as ut,_ as dt,ad as ie,d as z,Z as ct,g as G,V as fi,ae,W as vi,T as ft,af as Ke,ag as Ne,ah as hi,i as vt,U as Me,ai as bi,e as H,aj as pi,R as $,ak as gi,w as ht,al as je,a0 as yi,am as $i,S as bt,an as pt,ao as mi,ap as He,aq as xi,ar as Si,h as gt,as as yt,at as Di,f as $t,a2 as Pi,l as se,au as Ci,u as mt,n as J,o as Fe,q as Ki,m as xt,av as Ei,A as St,aw as wi,z as Dt,v as ki,a3 as Bi,p as Pt,a4 as Mi,a5 as Fi}from"./i18n-DJdGDBdH.js";const Ai=d.createContext(null),ar=d.createContext(null);function qe(i,e){const t=d.useRef(!0),l=d.useRef(null);ee(()=>(t.current=!0,()=>{t.current=!1}),[]),ee(()=>{t.current?t.current=!1:(!l.current||e.some((n,r)=>!Object.is(n,l[r])))&&i(),l.current=e},e)}function Ri(i,e){const t=[];for(;i&&i!==document.documentElement;)Ce(i,e)&&t.push(i),i=i.parentElement;return t}let Ii=0;const ye=new Map;function zi(i){let[e,t]=d.useState();return ee(()=>{if(!i)return;let l=ye.get(i);if(l)t(l.element.id);else{let n=`react-aria-description-${Ii++}`;t(n);let r=document.createElement("div");r.id=n,r.style.display="none",r.textContent=i,document.body.appendChild(r),l={refCount:0,element:r},ye.set(i,l)}return l.refCount++,()=>{l&&--l.refCount===0&&(l.element.remove(),ye.delete(i))}},[i]),{"aria-describedby":i?e:void 0}}function re(i,e,t,l){let n=Z(t),r=t==null;d.useEffect(()=>{if(r||!i.current)return;let o=i.current;return o.addEventListener(e,n,l),()=>{o.removeEventListener(e,n,l)}},[i,e,l,r,n])}function Ct(i,e){let t=We(i,e,"left"),l=We(i,e,"top"),n=e.offsetWidth,r=e.offsetHeight,o=i.scrollLeft,a=i.scrollTop,{borderTopWidth:s,borderLeftWidth:f,scrollPaddingTop:u,scrollPaddingRight:c,scrollPaddingBottom:v,scrollPaddingLeft:h}=getComputedStyle(i),g=o+parseInt(f,10),x=a+parseInt(s,10),y=g+i.clientWidth,m=x+i.clientHeight,S=parseInt(u,10)||0,D=parseInt(v,10)||0,C=parseInt(c,10)||0,M=parseInt(h,10)||0;t<=o+M?o=t-parseInt(f,10)-M:t+n>y-C&&(o+=t+n-y+C),l<=x+S?a=l-parseInt(s,10)-S:l+r>m-D&&(a+=l+r-m+D),i.scrollLeft=o,i.scrollTop=a}function We(i,e,t){const l=t==="left"?"offsetLeft":"offsetTop";let n=0;for(;e.offsetParent&&(n+=e[l],e.offsetParent!==i);){if(e.offsetParent.contains(i)){n-=i[l];break}e=e.offsetParent}return n}function Ze(i,e){if(i&&document.contains(i)){let o=document.scrollingElement||document.documentElement;if(window.getComputedStyle(o).overflow==="hidden"){let s=Ri(i);for(let f of s)Ct(f,i)}else{var t;let{left:s,top:f}=i.getBoundingClientRect();i==null||(t=i.scrollIntoView)===null||t===void 0||t.call(i,{block:"nearest"});let{left:u,top:c}=i.getBoundingClientRect();if(Math.abs(s-u)>1||Math.abs(f-c)>1){var l,n,r;e==null||(n=e.containingElement)===null||n===void 0||(l=n.scrollIntoView)===null||l===void 0||l.call(n,{block:"center",inline:"center"}),(r=i.scrollIntoView)===null||r===void 0||r.call(i,{block:"nearest"})}}}}function Li(i,e,t){let l=Z(()=>{t&&t(e)});d.useEffect(()=>{var n;let r=i==null||(n=i.current)===null||n===void 0?void 0:n.form;return r==null||r.addEventListener("reset",l),()=>{r==null||r.removeEventListener("reset",l)}},[i,l])}function Ti(i,e){let{collection:t,onLoadMore:l,scrollOffset:n=1}=i,r=d.useRef(null),o=Z(a=>{for(let s of a)s.isIntersecting&&l&&l()});ee(()=>(e.current&&(r.current=new IntersectionObserver(o,{root:di(e==null?void 0:e.current),rootMargin:`0px ${100*n}% ${100*n}% ${100*n}%`}),r.current.observe(e.current)),()=>{r.current&&r.current.disconnect()}),[t,o,e,n])}function Vi(i){const e=d.version.split(".");return parseInt(e[0],10)>=19?i:i?"true":void 0}const Oi="react-aria-clear-focus",Ui="react-aria-focus";function Q(i){return st()?i.metaKey:i.ctrlKey}let $e=new Map;function Ae(i){let{locale:e}=Be(),t=e+(i?Object.entries(i).sort((n,r)=>n[0]<r[0]?-1:1).join():"");if($e.has(t))return $e.get(t);let l=new Intl.Collator(e,i);return $e.set(t,l),l}const Ni=500;function Kt(i){let{isDisabled:e,onLongPressStart:t,onLongPressEnd:l,onLongPress:n,threshold:r=Ni,accessibilityDescription:o}=i;const a=d.useRef(void 0);let{addGlobalListener:s,removeGlobalListener:f}=ci(),{pressProps:u}=ut({isDisabled:e,onPressStart(v){if(v.continuePropagation(),(v.pointerType==="mouse"||v.pointerType==="touch")&&(t&&t({...v,type:"longpressstart"}),a.current=setTimeout(()=>{v.target.dispatchEvent(new PointerEvent("pointercancel",{bubbles:!0})),dt(v.target).activeElement!==v.target&&ie(v.target),n&&n({...v,type:"longpress"}),a.current=void 0},r),v.pointerType==="touch")){let h=g=>{g.preventDefault()};s(v.target,"contextmenu",h,{once:!0}),s(window,"pointerup",()=>{setTimeout(()=>{f(v.target,"contextmenu",h)},30)},{once:!0})}},onPressEnd(v){a.current&&clearTimeout(a.current),l&&(v.pointerType==="mouse"||v.pointerType==="touch")&&l({...v,type:"longpressend"})}}),c=zi(n&&!e?o:void 0);return{longPressProps:z(u,c)}}function Et(i){let e=Hi(dt(i));e!==i&&(e&&ji(e,i),i&&wt(i,e))}function ji(i,e){i.dispatchEvent(new FocusEvent("blur",{relatedTarget:e})),i.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:e}))}function wt(i,e){i.dispatchEvent(new FocusEvent("focus",{relatedTarget:e})),i.dispatchEvent(new FocusEvent("focusin",{bubbles:!0,relatedTarget:e}))}function Hi(i){let e=ct(i),t=e==null?void 0:e.getAttribute("aria-activedescendant");return t&&i.getElementById(t)||e}const Re=new WeakMap;function qi(i){return typeof i=="string"?i.replace(/\s*/g,""):""+i}function Wi(i,e){let t=Re.get(i);if(!t)throw new Error("Unknown list");return`${t.id}-option-${qi(e)}`}function kt(i){let{id:e,label:t,"aria-labelledby":l,"aria-label":n,labelElementType:r="label"}=i;e=G(e);let o=G(),a={};t&&(l=l?`${o} ${l}`:o,a={id:o,htmlFor:r==="label"?e:void 0});let s=fi({id:e,"aria-label":n,"aria-labelledby":l});return{labelProps:a,fieldProps:s}}function Zi(i){let{description:e,errorMessage:t,isInvalid:l,validationState:n}=i,{labelProps:r,fieldProps:o}=kt(i),a=ae([!!e,!!t,l,n]),s=ae([!!e,!!t,l,n]);return o=z(o,{"aria-describedby":[a,s,i["aria-describedby"]].filter(Boolean).join(" ")||void 0}),{labelProps:r,fieldProps:o,descriptionProps:{id:a},errorMessageProps:{id:s}}}function Ee(i){return vi()?i.altKey:i.ctrlKey}function oe(i,e){var t,l;let n=`[data-key="${CSS.escape(String(e))}"]`,r=(t=i.current)===null||t===void 0?void 0:t.dataset.collection;return r&&(n=`[data-collection="${CSS.escape(r)}"]${n}`),(l=i.current)===null||l===void 0?void 0:l.querySelector(n)}const Bt=new WeakMap;function Gi(i){let e=G();return Bt.set(i,e),e}function Yi(i){return Bt.get(i)}const Xi=1e3;function Mt(i){let{keyboardDelegate:e,selectionManager:t,onTypeSelect:l}=i,n=d.useRef({search:"",timeout:void 0}).current,r=o=>{let a=Ji(o.key);if(!(!a||o.ctrlKey||o.metaKey||!o.currentTarget.contains(o.target)||n.search.length===0&&a===" ")){if(a===" "&&n.search.trim().length>0&&(o.preventDefault(),"continuePropagation"in o||o.stopPropagation()),n.search+=a,e.getKeyForSearch!=null){let s=e.getKeyForSearch(n.search,t.focusedKey);s==null&&(s=e.getKeyForSearch(n.search)),s!=null&&(t.setFocusedKey(s),l&&l(s))}clearTimeout(n.timeout),n.timeout=setTimeout(()=>{n.search=""},Xi)}};return{typeSelectProps:{onKeyDownCapture:e.getKeyForSearch?r:void 0}}}function Ji(i){return i.length===1||!/^[A-Z]/i.test(i)?i:""}function Qi(i){let{selectionManager:e,keyboardDelegate:t,ref:l,autoFocus:n=!1,shouldFocusWrap:r=!1,disallowEmptySelection:o=!1,disallowSelectAll:a=!1,escapeKeyBehavior:s="clearSelection",selectOnFocus:f=e.selectionBehavior==="replace",disallowTypeAhead:u=!1,shouldUseVirtualFocus:c,allowsTabNavigation:v=!1,isVirtualized:h,scrollRef:g=l,linkBehavior:x="action"}=i,{direction:y}=Be(),m=ft(),S=p=>{var w;if(p.altKey&&p.key==="Tab"&&p.preventDefault(),!(!((w=l.current)===null||w===void 0)&&w.contains(p.target)))return;const b=(P,Y)=>{if(P!=null){if(e.isLink(P)&&x==="selection"&&f&&!Ee(p)){vt.flushSync(()=>{e.setFocusedKey(P,Y)});let X=oe(l,P),Ue=e.getItemProps(P);X&&m.open(X,p,Ue.href,Ue.routerOptions);return}if(e.setFocusedKey(P,Y),e.isLink(P)&&x==="override")return;p.shiftKey&&e.selectionMode==="multiple"?e.extendSelection(P):f&&!Ee(p)&&e.replaceSelection(P)}};switch(p.key){case"ArrowDown":if(t.getKeyBelow){var R,O,V;let P=e.focusedKey!=null?(R=t.getKeyBelow)===null||R===void 0?void 0:R.call(t,e.focusedKey):(O=t.getFirstKey)===null||O===void 0?void 0:O.call(t);P==null&&r&&(P=(V=t.getFirstKey)===null||V===void 0?void 0:V.call(t,e.focusedKey)),P!=null&&(p.preventDefault(),b(P))}break;case"ArrowUp":if(t.getKeyAbove){var j,W,ce;let P=e.focusedKey!=null?(j=t.getKeyAbove)===null||j===void 0?void 0:j.call(t,e.focusedKey):(W=t.getLastKey)===null||W===void 0?void 0:W.call(t);P==null&&r&&(P=(ce=t.getLastKey)===null||ce===void 0?void 0:ce.call(t,e.focusedKey)),P!=null&&(p.preventDefault(),b(P))}break;case"ArrowLeft":if(t.getKeyLeftOf){var fe,ve,he;let P=e.focusedKey!=null?(fe=t.getKeyLeftOf)===null||fe===void 0?void 0:fe.call(t,e.focusedKey):null;P==null&&r&&(P=y==="rtl"?(ve=t.getFirstKey)===null||ve===void 0?void 0:ve.call(t,e.focusedKey):(he=t.getLastKey)===null||he===void 0?void 0:he.call(t,e.focusedKey)),P!=null&&(p.preventDefault(),b(P,y==="rtl"?"first":"last"))}break;case"ArrowRight":if(t.getKeyRightOf){var be,pe,ge;let P=e.focusedKey!=null?(be=t.getKeyRightOf)===null||be===void 0?void 0:be.call(t,e.focusedKey):null;P==null&&r&&(P=y==="rtl"?(pe=t.getLastKey)===null||pe===void 0?void 0:pe.call(t,e.focusedKey):(ge=t.getFirstKey)===null||ge===void 0?void 0:ge.call(t,e.focusedKey)),P!=null&&(p.preventDefault(),b(P,y==="rtl"?"last":"first"))}break;case"Home":if(t.getFirstKey){if(e.focusedKey===null&&p.shiftKey)return;p.preventDefault();let P=t.getFirstKey(e.focusedKey,Q(p));e.setFocusedKey(P),P!=null&&(Q(p)&&p.shiftKey&&e.selectionMode==="multiple"?e.extendSelection(P):f&&e.replaceSelection(P))}break;case"End":if(t.getLastKey){if(e.focusedKey===null&&p.shiftKey)return;p.preventDefault();let P=t.getLastKey(e.focusedKey,Q(p));e.setFocusedKey(P),P!=null&&(Q(p)&&p.shiftKey&&e.selectionMode==="multiple"?e.extendSelection(P):f&&e.replaceSelection(P))}break;case"PageDown":if(t.getKeyPageBelow&&e.focusedKey!=null){let P=t.getKeyPageBelow(e.focusedKey);P!=null&&(p.preventDefault(),b(P))}break;case"PageUp":if(t.getKeyPageAbove&&e.focusedKey!=null){let P=t.getKeyPageAbove(e.focusedKey);P!=null&&(p.preventDefault(),b(P))}break;case"a":Q(p)&&e.selectionMode==="multiple"&&a!==!0&&(p.preventDefault(),e.selectAll());break;case"Escape":s==="clearSelection"&&!o&&e.selectedKeys.size!==0&&(p.stopPropagation(),p.preventDefault(),e.clearSelection());break;case"Tab":if(!v){if(p.shiftKey)l.current.focus();else{let P=hi(l.current,{tabbable:!0}),Y,X;do X=P.lastChild(),X&&(Y=X);while(X);Y&&!Y.contains(document.activeElement)&&ie(Y)}break}}},D=d.useRef({top:0,left:0});re(g,"scroll",h?void 0:()=>{var p,w,b,R;D.current={top:(b=(p=g.current)===null||p===void 0?void 0:p.scrollTop)!==null&&b!==void 0?b:0,left:(R=(w=g.current)===null||w===void 0?void 0:w.scrollLeft)!==null&&R!==void 0?R:0}});let C=p=>{if(e.isFocused){p.currentTarget.contains(p.target)||e.setFocused(!1);return}if(p.currentTarget.contains(p.target)){if(e.setFocused(!0),e.focusedKey==null){var w,b;let V=W=>{W!=null&&(e.setFocusedKey(W),f&&!e.isSelected(W)&&e.replaceSelection(W))},j=p.relatedTarget;var R,O;j&&p.currentTarget.compareDocumentPosition(j)&Node.DOCUMENT_POSITION_FOLLOWING?V((R=e.lastSelectedKey)!==null&&R!==void 0?R:(w=t.getLastKey)===null||w===void 0?void 0:w.call(t)):V((O=e.firstSelectedKey)!==null&&O!==void 0?O:(b=t.getFirstKey)===null||b===void 0?void 0:b.call(t))}else!h&&g.current&&(g.current.scrollTop=D.current.top,g.current.scrollLeft=D.current.left);if(e.focusedKey!=null&&g.current){let V=oe(l,e.focusedKey);V instanceof HTMLElement&&(!V.contains(document.activeElement)&&!c&&ie(V),Ne()==="keyboard"&&Ze(V,{containingElement:l.current}))}}},M=p=>{p.currentTarget.contains(p.relatedTarget)||e.setFocused(!1)},E=d.useRef(!1);re(l,Ui,c?p=>{let{detail:w}=p;p.stopPropagation(),e.setFocused(!0),(w==null?void 0:w.focusStrategy)==="first"&&(E.current=!0)}:void 0);let k=Z(()=>{var p,w;let b=(w=(p=t.getFirstKey)===null||p===void 0?void 0:p.call(t))!==null&&w!==void 0?w:null;if(b==null){let R=ct();Et(l.current),wt(R,null),e.collection.size>0&&(E.current=!1)}else e.setFocusedKey(b),E.current=!1});qe(()=>{E.current&&k()},[e.collection,k]);let F=Z(()=>{e.collection.size>0&&(E.current=!1)});qe(()=>{F()},[e.focusedKey,F]),re(l,Oi,c?p=>{var w;p.stopPropagation(),e.setFocused(!1),!((w=p.detail)===null||w===void 0)&&w.clearFocusKey&&e.setFocusedKey(null)}:void 0);const B=d.useRef(n),K=d.useRef(!1);d.useEffect(()=>{if(B.current){var p,w;let O=null;var b;n==="first"&&(O=(b=(p=t.getFirstKey)===null||p===void 0?void 0:p.call(t))!==null&&b!==void 0?b:null);var R;n==="last"&&(O=(R=(w=t.getLastKey)===null||w===void 0?void 0:w.call(t))!==null&&R!==void 0?R:null);let V=e.selectedKeys;if(V.size){for(let j of V)if(e.canSelectItem(j)){O=j;break}}e.setFocused(!0),e.setFocusedKey(O),O==null&&!c&&l.current&&Ke(l.current),e.collection.size>0&&(B.current=!1,K.current=!0)}});let I=d.useRef(e.focusedKey),A=d.useRef(null);d.useEffect(()=>{if(e.isFocused&&e.focusedKey!=null&&(e.focusedKey!==I.current||K.current)&&g.current&&l.current){let p=Ne(),w=oe(l,e.focusedKey);if(!(w instanceof HTMLElement))return;(p==="keyboard"||K.current)&&(A.current&&cancelAnimationFrame(A.current),A.current=requestAnimationFrame(()=>{g.current&&(Ct(g.current,w),p!=="virtual"&&Ze(w,{containingElement:l.current}))}))}!c&&e.isFocused&&e.focusedKey==null&&I.current!=null&&l.current&&Ke(l.current),I.current=e.focusedKey,K.current=!1}),d.useEffect(()=>()=>{A.current&&cancelAnimationFrame(A.current)},[]),re(l,"react-aria-focus-scope-restore",p=>{p.preventDefault(),e.setFocused(!0)});let L={onKeyDown:S,onFocus:C,onBlur:M,onMouseDown(p){g.current===p.target&&p.preventDefault()}},{typeSelectProps:T}=Mt({keyboardDelegate:t,selectionManager:e});u||(L=z(T,L));let U;c||(U=e.focusedKey==null?0:-1);let q=Gi(e.collection);return{collectionProps:z(L,{tabIndex:U,"data-collection":q})}}function _i(i){let{id:e,selectionManager:t,key:l,ref:n,shouldSelectOnPressUp:r,shouldUseVirtualFocus:o,focus:a,isDisabled:s,onAction:f,allowsDifferentPressOrigin:u,linkBehavior:c="action"}=i,v=ft();e=G(e);let h=b=>{if(b.pointerType==="keyboard"&&Ee(b))t.toggleSelection(l);else{if(t.selectionMode==="none")return;if(t.isLink(l)){if(c==="selection"&&n.current){let R=t.getItemProps(l);v.open(n.current,b,R.href,R.routerOptions),t.setSelectedKeys(t.selectedKeys);return}else if(c==="override"||c==="none")return}t.selectionMode==="single"?t.isSelected(l)&&!t.disallowEmptySelection?t.toggleSelection(l):t.replaceSelection(l):b&&b.shiftKey?t.extendSelection(l):t.selectionBehavior==="toggle"||b&&(Q(b)||b.pointerType==="touch"||b.pointerType==="virtual")?t.toggleSelection(l):t.replaceSelection(l)}};d.useEffect(()=>{l===t.focusedKey&&t.isFocused&&(o?Et(n.current):a?a():document.activeElement!==n.current&&n.current&&Ke(n.current))},[n,l,t.focusedKey,t.childFocusStrategy,t.isFocused,o]),s=s||t.isDisabled(l);let g={};!o&&!s?g={tabIndex:l===t.focusedKey?0:-1,onFocus(b){b.target===n.current&&t.setFocusedKey(l)}}:s&&(g.onMouseDown=b=>{b.preventDefault()});let x=t.isLink(l)&&c==="override",y=t.isLink(l)&&c!=="selection"&&c!=="none",m=!s&&t.canSelectItem(l)&&!x,S=(f||y)&&!s,D=S&&(t.selectionBehavior==="replace"?!m:!m||t.isEmpty),C=S&&m&&t.selectionBehavior==="replace",M=D||C,E=d.useRef(null),k=M&&m,F=d.useRef(!1),B=d.useRef(!1),K=t.getItemProps(l),I=b=>{f&&f(),y&&n.current&&v.open(n.current,b,K.href,K.routerOptions)},A={ref:n};if(r?(A.onPressStart=b=>{E.current=b.pointerType,F.current=k,b.pointerType==="keyboard"&&(!M||Ye())&&h(b)},u?(A.onPressUp=D?void 0:b=>{b.pointerType==="mouse"&&m&&h(b)},A.onPress=D?I:b=>{b.pointerType!=="keyboard"&&b.pointerType!=="mouse"&&m&&h(b)}):A.onPress=b=>{if(D||C&&b.pointerType!=="mouse"){if(b.pointerType==="keyboard"&&!Ge())return;I(b)}else b.pointerType!=="keyboard"&&m&&h(b)}):(A.onPressStart=b=>{E.current=b.pointerType,F.current=k,B.current=D,m&&(b.pointerType==="mouse"&&!D||b.pointerType==="keyboard"&&(!S||Ye()))&&h(b)},A.onPress=b=>{(b.pointerType==="touch"||b.pointerType==="pen"||b.pointerType==="virtual"||b.pointerType==="keyboard"&&M&&Ge()||b.pointerType==="mouse"&&B.current)&&(M?I(b):m&&h(b))}),g["data-collection"]=Yi(t.collection),g["data-key"]=l,A.preventFocusOnPress=o,o&&(A=z(A,{onPressStart(b){b.pointerType!=="touch"&&(t.setFocused(!0),t.setFocusedKey(l))},onPress(b){b.pointerType==="touch"&&(t.setFocused(!0),t.setFocusedKey(l))}})),K)for(let b of["onPressStart","onPressEnd","onPressChange","onPress","onPressUp","onClick"])K[b]&&(A[b]=Me(A[b],K[b]));let{pressProps:L,isPressed:T}=ut(A),U=C?b=>{E.current==="mouse"&&(b.stopPropagation(),b.preventDefault(),I(b))}:void 0,{longPressProps:q}=Kt({isDisabled:!k,onLongPress(b){b.pointerType==="touch"&&(h(b),t.setSelectionBehavior("toggle"))}}),p=b=>{E.current==="touch"&&F.current&&b.preventDefault()},w=c!=="none"&&t.isLink(l)?b=>{bi.isOpening||b.preventDefault()}:void 0;return{itemProps:z(g,m||D||o&&!s?L:{},k?q:{},{onDoubleClick:U,onDragStartCapture:p,onClick:w,id:e},o?{onMouseDown:b=>b.preventDefault()}:void 0),isPressed:T,isSelected:t.isSelected(l),isFocused:t.isFocused&&t.focusedKey===l,isDisabled:s,allowsSelection:m,hasAction:M}}function Ge(){let i=window.event;return(i==null?void 0:i.key)==="Enter"}function Ye(){let i=window.event;return(i==null?void 0:i.key)===" "||(i==null?void 0:i.code)==="Space"}class Xe{getItemRect(e){let t=this.ref.current;if(!t)return null;let l=e!=null?oe(this.ref,e):null;if(!l)return null;let n=t.getBoundingClientRect(),r=l.getBoundingClientRect();return{x:r.left-n.left+t.scrollLeft,y:r.top-n.top+t.scrollTop,width:r.width,height:r.height}}getContentSize(){let e=this.ref.current;var t,l;return{width:(t=e==null?void 0:e.scrollWidth)!==null&&t!==void 0?t:0,height:(l=e==null?void 0:e.scrollHeight)!==null&&l!==void 0?l:0}}getVisibleRect(){let e=this.ref.current;var t,l,n,r;return{x:(t=e==null?void 0:e.scrollLeft)!==null&&t!==void 0?t:0,y:(l=e==null?void 0:e.scrollTop)!==null&&l!==void 0?l:0,width:(n=e==null?void 0:e.offsetWidth)!==null&&n!==void 0?n:0,height:(r=e==null?void 0:e.offsetHeight)!==null&&r!==void 0?r:0}}constructor(e){this.ref=e}}class Ie{isDisabled(e){var t;return this.disabledBehavior==="all"&&(((t=e.props)===null||t===void 0?void 0:t.isDisabled)||this.disabledKeys.has(e.key))}findNextNonDisabled(e,t){let l=e;for(;l!=null;){let n=this.collection.getItem(l);if((n==null?void 0:n.type)==="item"&&!this.isDisabled(n))return l;l=t(l)}return null}getNextKey(e){let t=e;return t=this.collection.getKeyAfter(t),this.findNextNonDisabled(t,l=>this.collection.getKeyAfter(l))}getPreviousKey(e){let t=e;return t=this.collection.getKeyBefore(t),this.findNextNonDisabled(t,l=>this.collection.getKeyBefore(l))}findKey(e,t,l){let n=e,r=this.layoutDelegate.getItemRect(n);if(!r||n==null)return null;let o=r;do{if(n=t(n),n==null)break;r=this.layoutDelegate.getItemRect(n)}while(r&&l(o,r)&&n!=null);return n}isSameRow(e,t){return e.y===t.y||e.x!==t.x}isSameColumn(e,t){return e.x===t.x||e.y!==t.y}getKeyBelow(e){return this.layout==="grid"&&this.orientation==="vertical"?this.findKey(e,t=>this.getNextKey(t),this.isSameRow):this.getNextKey(e)}getKeyAbove(e){return this.layout==="grid"&&this.orientation==="vertical"?this.findKey(e,t=>this.getPreviousKey(t),this.isSameRow):this.getPreviousKey(e)}getNextColumn(e,t){return t?this.getPreviousKey(e):this.getNextKey(e)}getKeyRightOf(e){let t=this.direction==="ltr"?"getKeyRightOf":"getKeyLeftOf";return this.layoutDelegate[t]?(e=this.layoutDelegate[t](e),this.findNextNonDisabled(e,l=>this.layoutDelegate[t](l))):this.layout==="grid"?this.orientation==="vertical"?this.getNextColumn(e,this.direction==="rtl"):this.findKey(e,l=>this.getNextColumn(l,this.direction==="rtl"),this.isSameColumn):this.orientation==="horizontal"?this.getNextColumn(e,this.direction==="rtl"):null}getKeyLeftOf(e){let t=this.direction==="ltr"?"getKeyLeftOf":"getKeyRightOf";return this.layoutDelegate[t]?(e=this.layoutDelegate[t](e),this.findNextNonDisabled(e,l=>this.layoutDelegate[t](l))):this.layout==="grid"?this.orientation==="vertical"?this.getNextColumn(e,this.direction==="ltr"):this.findKey(e,l=>this.getNextColumn(l,this.direction==="ltr"),this.isSameColumn):this.orientation==="horizontal"?this.getNextColumn(e,this.direction==="ltr"):null}getFirstKey(){let e=this.collection.getFirstKey();return this.findNextNonDisabled(e,t=>this.collection.getKeyAfter(t))}getLastKey(){let e=this.collection.getLastKey();return this.findNextNonDisabled(e,t=>this.collection.getKeyBefore(t))}getKeyPageAbove(e){let t=this.ref.current,l=this.layoutDelegate.getItemRect(e);if(!l)return null;if(t&&!Ce(t))return this.getFirstKey();let n=e;if(this.orientation==="horizontal"){let r=Math.max(0,l.x+l.width-this.layoutDelegate.getVisibleRect().width);for(;l&&l.x>r&&n!=null;)n=this.getKeyAbove(n),l=n==null?null:this.layoutDelegate.getItemRect(n)}else{let r=Math.max(0,l.y+l.height-this.layoutDelegate.getVisibleRect().height);for(;l&&l.y>r&&n!=null;)n=this.getKeyAbove(n),l=n==null?null:this.layoutDelegate.getItemRect(n)}return n??this.getFirstKey()}getKeyPageBelow(e){let t=this.ref.current,l=this.layoutDelegate.getItemRect(e);if(!l)return null;if(t&&!Ce(t))return this.getLastKey();let n=e;if(this.orientation==="horizontal"){let r=Math.min(this.layoutDelegate.getContentSize().width,l.y-l.width+this.layoutDelegate.getVisibleRect().width);for(;l&&l.x<r&&n!=null;)n=this.getKeyBelow(n),l=n==null?null:this.layoutDelegate.getItemRect(n)}else{let r=Math.min(this.layoutDelegate.getContentSize().height,l.y-l.height+this.layoutDelegate.getVisibleRect().height);for(;l&&l.y<r&&n!=null;)n=this.getKeyBelow(n),l=n==null?null:this.layoutDelegate.getItemRect(n)}return n??this.getLastKey()}getKeyForSearch(e,t){if(!this.collator)return null;let l=this.collection,n=t||this.getFirstKey();for(;n!=null;){let r=l.getItem(n);if(!r)return null;let o=r.textValue.slice(0,e.length);if(r.textValue&&this.collator.compare(o,e)===0)return n;n=this.getNextKey(n)}return null}constructor(...e){if(e.length===1){let t=e[0];this.collection=t.collection,this.ref=t.ref,this.collator=t.collator,this.disabledKeys=t.disabledKeys||new Set,this.disabledBehavior=t.disabledBehavior||"all",this.orientation=t.orientation||"vertical",this.direction=t.direction,this.layout=t.layout||"stack",this.layoutDelegate=t.layoutDelegate||new Xe(t.ref)}else this.collection=e[0],this.disabledKeys=e[1],this.ref=e[2],this.collator=e[3],this.layout="stack",this.orientation="vertical",this.disabledBehavior="all",this.layoutDelegate=new Xe(this.ref);this.layout==="stack"&&this.orientation==="vertical"&&(this.getKeyLeftOf=void 0,this.getKeyRightOf=void 0)}}function en(i){let{selectionManager:e,collection:t,disabledKeys:l,ref:n,keyboardDelegate:r,layoutDelegate:o}=i,a=Ae({usage:"search",sensitivity:"base"}),s=e.disabledBehavior,f=d.useMemo(()=>r||new Ie({collection:t,disabledKeys:l,disabledBehavior:s,ref:n,collator:a,layoutDelegate:o}),[r,o,t,l,n,a,s]),{collectionProps:u}=Qi({...i,ref:n,selectionManager:e,keyboardDelegate:f});return{listProps:u}}function tn(i,e,t){let l=H(i,{labelable:!0}),n=i.selectionBehavior||"toggle",r=i.linkBehavior||(n==="replace"?"action":"override");n==="toggle"&&r==="action"&&(r="override");let{listProps:o}=en({...i,ref:t,selectionManager:e.selectionManager,collection:e.collection,disabledKeys:e.disabledKeys,linkBehavior:r}),{focusWithinProps:a}=pi({onFocusWithin:i.onFocus,onBlurWithin:i.onBlur,onFocusWithinChange:i.onFocusChange}),s=G(i.id);Re.set(e,{id:s,shouldUseVirtualFocus:i.shouldUseVirtualFocus,shouldSelectOnPressUp:i.shouldSelectOnPressUp,shouldFocusOnHover:i.shouldFocusOnHover,isVirtualized:i.isVirtualized,onAction:i.onAction,linkBehavior:r});let{labelProps:f,fieldProps:u}=kt({...i,id:s,labelElementType:"span"});return{labelProps:f,listBoxProps:z(l,a,e.selectionManager.selectionMode==="multiple"?{"aria-multiselectable":"true"}:{},{role:"listbox",...z(u,o)})}}class ln{build(e,t){return this.context=t,Je(()=>this.iterateCollection(e))}*iterateCollection(e){let{children:t,items:l}=e;if($.isValidElement(t)&&t.type===$.Fragment)yield*this.iterateCollection({children:t.props.children,items:l});else if(typeof t=="function"){if(!l)throw new Error("props.children was a function but props.items is missing");let n=0;for(let r of l)yield*this.getFullNode({value:r,index:n},{renderer:t}),n++}else{let n=[];$.Children.forEach(t,o=>{o&&n.push(o)});let r=0;for(let o of n){let a=this.getFullNode({element:o,index:r},{});for(let s of a)r++,yield s}}}getKey(e,t,l,n){if(e.key!=null)return e.key;if(t.type==="cell"&&t.key!=null)return`${n}${t.key}`;let r=t.value;if(r!=null){var o;let a=(o=r.key)!==null&&o!==void 0?o:r.id;if(a==null)throw new Error("No key found for item");return a}return n?`${n}.${t.index}`:`$.${t.index}`}getChildState(e,t){return{renderer:t.renderer||e.renderer}}*getFullNode(e,t,l,n){if($.isValidElement(e.element)&&e.element.type===$.Fragment){let y=[];$.Children.forEach(e.element.props.children,S=>{y.push(S)});var r;let m=(r=e.index)!==null&&r!==void 0?r:0;for(const S of y)yield*this.getFullNode({element:S,index:m++},t,l,n);return}let o=e.element;if(!o&&e.value&&t&&t.renderer){let y=this.cache.get(e.value);if(y&&(!y.shouldInvalidate||!y.shouldInvalidate(this.context))){y.index=e.index,y.parentKey=n?n.key:null,yield y;return}o=t.renderer(e.value)}if($.isValidElement(o)){let y=o.type;if(typeof y!="function"&&typeof y.getCollectionNode!="function"){let C=o.type;throw new Error(`Unknown element <${C}> in collection.`)}let m=y.getCollectionNode(o.props,this.context);var a;let S=(a=e.index)!==null&&a!==void 0?a:0,D=m.next();for(;!D.done&&D.value;){let C=D.value;e.index=S;var s;let M=(s=C.key)!==null&&s!==void 0?s:null;M==null&&(M=C.element?null:this.getKey(o,e,t,l));let k=[...this.getFullNode({...C,key:M,index:S,wrapper:nn(e.wrapper,C.wrapper)},this.getChildState(t,C),l?`${l}${o.key}`:o.key,n)];for(let F of k){var f,u;F.value=(u=(f=C.value)!==null&&f!==void 0?f:e.value)!==null&&u!==void 0?u:null,F.value&&this.cache.set(F.value,F);var c;if(e.type&&F.type!==e.type)throw new Error(`Unsupported type <${me(F.type)}> in <${me((c=n==null?void 0:n.type)!==null&&c!==void 0?c:"unknown parent type")}>. Only <${me(e.type)}> is supported.`);S++,yield F}D=m.next(k)}return}if(e.key==null||e.type==null)return;let v=this;var h,g;let x={type:e.type,props:e.props,key:e.key,parentKey:n?n.key:null,value:(h=e.value)!==null&&h!==void 0?h:null,level:n?n.level+1:0,index:e.index,rendered:e.rendered,textValue:(g=e.textValue)!==null&&g!==void 0?g:"","aria-label":e["aria-label"],wrapper:e.wrapper,shouldInvalidate:e.shouldInvalidate,hasChildNodes:e.hasChildNodes||!1,childNodes:Je(function*(){if(!e.hasChildNodes||!e.childNodes)return;let y=0;for(let m of e.childNodes()){m.key!=null&&(m.key=`${x.key}${m.key}`);let S=v.getFullNode({...m,index:y},v.getChildState(t,m),x.key,x);for(let D of S)y++,yield D}})};yield x}constructor(){this.cache=new WeakMap}}function Je(i){let e=[],t=null;return{*[Symbol.iterator](){for(let l of e)yield l;t||(t=i());for(let l of t)e.push(l),yield l}}}function nn(i,e){if(i&&e)return t=>i(e(t));if(i)return i;if(e)return e}function me(i){return i[0].toUpperCase()+i.slice(1)}function rn(i,e,t){let l=d.useMemo(()=>new ln,[]),{children:n,items:r,collection:o}=i;return d.useMemo(()=>{if(o)return o;let s=l.build({children:n,items:r},t);return e(s)},[l,n,r,o,t,e])}function Ft(i,e){return typeof e.getChildren=="function"?e.getChildren(i.key):i.childNodes}function on(i){return an(i)}function an(i,e){for(let t of i)return t}function xe(i,e,t){if(e.parentKey===t.parentKey)return e.index-t.index;let l=[...Qe(i,e),e],n=[...Qe(i,t),t],r=l.slice(0,n.length).findIndex((o,a)=>o!==n[a]);return r!==-1?(e=l[r],t=n[r],e.index-t.index):l.findIndex(o=>o===t)>=0?1:(n.findIndex(o=>o===e)>=0,-1)}function Qe(i,e){let t=[],l=e;for(;(l==null?void 0:l.parentKey)!=null;)l=i.getItem(l.parentKey),l&&t.unshift(l);return t}const _e=new WeakMap;function sn(i){let e=_e.get(i);if(e!=null)return e;let t=0,l=n=>{for(let r of n)r.type==="section"?l(Ft(r,i)):r.type==="item"&&t++};return l(i),_e.set(i,t),t}function un(i,e,t){var l,n;let{key:r}=i,o=Re.get(e);var a;let s=(a=i.isDisabled)!==null&&a!==void 0?a:e.selectionManager.isDisabled(r);var f;let u=(f=i.isSelected)!==null&&f!==void 0?f:e.selectionManager.isSelected(r);var c;let v=(c=i.shouldSelectOnPressUp)!==null&&c!==void 0?c:o==null?void 0:o.shouldSelectOnPressUp;var h;let g=(h=i.shouldFocusOnHover)!==null&&h!==void 0?h:o==null?void 0:o.shouldFocusOnHover;var x;let y=(x=i.shouldUseVirtualFocus)!==null&&x!==void 0?x:o==null?void 0:o.shouldUseVirtualFocus;var m;let S=(m=i.isVirtualized)!==null&&m!==void 0?m:o==null?void 0:o.isVirtualized,D=ae(),C=ae(),M={role:"option","aria-disabled":s||void 0,"aria-selected":e.selectionManager.selectionMode!=="none"?u:void 0};st()&&gi()||(M["aria-label"]=i["aria-label"],M["aria-labelledby"]=D,M["aria-describedby"]=C);let E=e.collection.getItem(r);if(S){let p=Number(E==null?void 0:E.index);M["aria-posinset"]=Number.isNaN(p)?void 0:p+1,M["aria-setsize"]=sn(e.collection)}let k=o!=null&&o.onAction?()=>{var p;return o==null||(p=o.onAction)===null||p===void 0?void 0:p.call(o,r)}:void 0,F=Wi(e,r),{itemProps:B,isPressed:K,isFocused:I,hasAction:A,allowsSelection:L}=_i({selectionManager:e.selectionManager,key:r,ref:t,shouldSelectOnPressUp:v,allowsDifferentPressOrigin:v&&g,isVirtualized:S,shouldUseVirtualFocus:y,isDisabled:s,onAction:k||!(E==null||(l=E.props)===null||l===void 0)&&l.onAction?Me(E==null||(n=E.props)===null||n===void 0?void 0:n.onAction,k):void 0,linkBehavior:o==null?void 0:o.linkBehavior,id:F}),{hoverProps:T}=ht({isDisabled:s||!g,onHoverStart(){je()||(e.selectionManager.setFocused(!0),e.selectionManager.setFocusedKey(r))}}),U=H(E==null?void 0:E.props);delete U.id;let q=yi(E==null?void 0:E.props);return{optionProps:{...M,...z(U,B,T,q),id:F},labelProps:{id:D},descriptionProps:{id:C},isFocused:I,isFocusVisible:I&&e.selectionManager.isFocused&&je(),isSelected:u,isDisabled:s,isPressed:K,allowsSelection:L,hasAction:A}}function dn(i){let{heading:e,"aria-label":t}=i,l=G();return{itemProps:{role:"presentation"},headingProps:e?{id:l,role:"presentation"}:{},groupProps:{role:"group","aria-label":t,"aria-labelledby":e?l:void 0}}}const At={badInput:!1,customError:!1,patternMismatch:!1,rangeOverflow:!1,rangeUnderflow:!1,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!0},Rt={...At,customError:!0,valid:!1},le={isInvalid:!1,validationDetails:At,validationErrors:[]},cn=d.createContext({}),et="__formValidationState"+Date.now();function fn(i){if(i[et]){let{realtimeValidation:e,displayValidation:t,updateValidation:l,resetValidation:n,commitValidation:r}=i[et];return{realtimeValidation:e,displayValidation:t,updateValidation:l,resetValidation:n,commitValidation:r}}return vn(i)}function vn(i){let{isInvalid:e,validationState:t,name:l,value:n,builtinValidation:r,validate:o,validationBehavior:a="aria"}=i;t&&(e||(e=t==="invalid"));let s=e!==void 0?{isInvalid:e,validationErrors:[],validationDetails:Rt}:null,f=d.useMemo(()=>{if(!o||n==null)return null;let K=hn(o,n);return tt(K)},[o,n]);r!=null&&r.validationDetails.valid&&(r=void 0);let u=d.useContext(cn),c=d.useMemo(()=>l?Array.isArray(l)?l.flatMap(K=>we(u[K])):we(u[l]):[],[u,l]),[v,h]=d.useState(u),[g,x]=d.useState(!1);u!==v&&(h(u),x(!1));let y=d.useMemo(()=>tt(g?[]:c),[g,c]),m=d.useRef(le),[S,D]=d.useState(le),C=d.useRef(le),M=()=>{if(!E)return;k(!1);let K=f||r||m.current;Se(K,C.current)||(C.current=K,D(K))},[E,k]=d.useState(!1);return d.useEffect(M),{realtimeValidation:s||y||f||r||le,displayValidation:a==="native"?s||y||S:s||y||f||r||S,updateValidation(K){a==="aria"&&!Se(S,K)?D(K):m.current=K},resetValidation(){let K=le;Se(K,C.current)||(C.current=K,D(K)),a==="native"&&k(!1),x(!0)},commitValidation(){a==="native"&&k(!0),x(!0)}}}function we(i){return i?Array.isArray(i)?i:[i]:[]}function hn(i,e){if(typeof i=="function"){let t=i(e);if(t&&typeof t!="boolean")return we(t)}return[]}function tt(i){return i.length?{isInvalid:!0,validationErrors:i,validationDetails:Rt}:null}function Se(i,e){return i===e?!0:!!i&&!!e&&i.isInvalid===e.isInvalid&&i.validationErrors.length===e.validationErrors.length&&i.validationErrors.every((t,l)=>t===e.validationErrors[l])&&Object.entries(i.validationDetails).every(([t,l])=>e.validationDetails[t]===l)}var It={};It={longPressMessage:"اضغط مطولاً أو اضغط على Alt + السهم لأسفل لفتح القائمة"};var zt={};zt={longPressMessage:"Натиснете продължително или натиснете Alt+ стрелка надолу, за да отворите менюто"};var Lt={};Lt={longPressMessage:"Dlouhým stiskem nebo stisknutím kláves Alt + šipka dolů otevřete nabídku"};var Tt={};Tt={longPressMessage:"Langt tryk eller tryk på Alt + pil ned for at åbne menuen"};var Vt={};Vt={longPressMessage:"Drücken Sie lange oder drücken Sie Alt + Nach-unten, um das Menü zu öffnen"};var Ot={};Ot={longPressMessage:"Πιέστε παρατεταμένα ή πατήστε Alt + κάτω βέλος για να ανοίξετε το μενού"};var Ut={};Ut={longPressMessage:"Long press or press Alt + ArrowDown to open menu"};var Nt={};Nt={longPressMessage:"Mantenga pulsado o pulse Alt + flecha abajo para abrir el menú"};var jt={};jt={longPressMessage:"Menüü avamiseks vajutage pikalt või vajutage klahve Alt + allanool"};var Ht={};Ht={longPressMessage:"Avaa valikko painamalla pohjassa tai näppäinyhdistelmällä Alt + Alanuoli"};var qt={};qt={longPressMessage:"Appuyez de manière prolongée ou appuyez sur Alt + Flèche vers le bas pour ouvrir le menu."};var Wt={};Wt={longPressMessage:"לחץ לחיצה ארוכה או הקש Alt + ArrowDown כדי לפתוח את התפריט"};var Zt={};Zt={longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dolje za otvaranje izbornika"};var Gt={};Gt={longPressMessage:"Nyomja meg hosszan, vagy nyomja meg az Alt + lefele nyíl gombot a menü megnyitásához"};var Yt={};Yt={longPressMessage:"Premere a lungo o premere Alt + Freccia giù per aprire il menu"};var Xt={};Xt={longPressMessage:"長押しまたは Alt+下矢印キーでメニューを開く"};var Jt={};Jt={longPressMessage:"길게 누르거나 Alt + 아래쪽 화살표를 눌러 메뉴 열기"};var Qt={};Qt={longPressMessage:"Norėdami atidaryti meniu, nuspaudę palaikykite arba paspauskite „Alt + ArrowDown“."};var _t={};_t={longPressMessage:"Lai atvērtu izvēlni, turiet nospiestu vai nospiediet taustiņu kombināciju Alt + lejupvērstā bultiņa"};var el={};el={longPressMessage:"Langt trykk eller trykk Alt + PilNed for å åpne menyen"};var tl={};tl={longPressMessage:"Druk lang op Alt + pijl-omlaag of druk op Alt om het menu te openen"};var ll={};ll={longPressMessage:"Naciśnij i przytrzymaj lub naciśnij klawisze Alt + Strzałka w dół, aby otworzyć menu"};var il={};il={longPressMessage:"Pressione e segure ou pressione Alt + Seta para baixo para abrir o menu"};var nl={};nl={longPressMessage:"Prima continuamente ou prima Alt + Seta Para Baixo para abrir o menu"};var rl={};rl={longPressMessage:"Apăsați lung sau apăsați pe Alt + săgeată în jos pentru a deschide meniul"};var ol={};ol={longPressMessage:"Нажмите и удерживайте или нажмите Alt + Стрелка вниз, чтобы открыть меню"};var al={};al={longPressMessage:"Ponuku otvoríte dlhým stlačením alebo stlačením klávesu Alt + klávesu so šípkou nadol"};var sl={};sl={longPressMessage:"Za odprtje menija pritisnite in držite gumb ali pritisnite Alt+puščica navzdol"};var ul={};ul={longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dole da otvorite meni"};var dl={};dl={longPressMessage:"Håll nedtryckt eller tryck på Alt + pil nedåt för att öppna menyn"};var cl={};cl={longPressMessage:"Menüyü açmak için uzun basın veya Alt + Aşağı Ok tuşuna basın"};var fl={};fl={longPressMessage:"Довго або звичайно натисніть комбінацію клавіш Alt і стрілка вниз, щоб відкрити меню"};var vl={};vl={longPressMessage:"长按或按 Alt + 向下方向键以打开菜单"};var hl={};hl={longPressMessage:"長按或按 Alt+向下鍵以開啟功能表"};var bl={};bl={"ar-AE":It,"bg-BG":zt,"cs-CZ":Lt,"da-DK":Tt,"de-DE":Vt,"el-GR":Ot,"en-US":Ut,"es-ES":Nt,"et-EE":jt,"fi-FI":Ht,"fr-FR":qt,"he-IL":Wt,"hr-HR":Zt,"hu-HU":Gt,"it-IT":Yt,"ja-JP":Xt,"ko-KR":Jt,"lt-LT":Qt,"lv-LV":_t,"nb-NO":el,"nl-NL":tl,"pl-PL":ll,"pt-BR":il,"pt-PT":nl,"ro-RO":rl,"ru-RU":ol,"sk-SK":al,"sl-SI":sl,"sr-SP":ul,"sv-SE":dl,"tr-TR":cl,"uk-UA":fl,"zh-CN":vl,"zh-TW":hl};function bn(i){return i&&i.__esModule?i.default:i}function pn(i,e,t){let{type:l="menu",isDisabled:n,trigger:r="press"}=i,o=G(),{triggerProps:a,overlayProps:s}=$i({type:l},e,t),f=h=>{if(!n&&!(r==="longPress"&&!h.altKey)&&t&&t.current)switch(h.key){case"Enter":case" ":if(r==="longPress"||h.isDefaultPrevented())return;case"ArrowDown":"continuePropagation"in h||h.stopPropagation(),h.preventDefault(),e.toggle("first");break;case"ArrowUp":"continuePropagation"in h||h.stopPropagation(),h.preventDefault(),e.toggle("last");break;default:"continuePropagation"in h&&h.continuePropagation()}},u=bt(bn(bl),"@react-aria/menu"),{longPressProps:c}=Kt({isDisabled:n||r!=="longPress",accessibilityDescription:u.format("longPressMessage"),onLongPressStart(){e.close()},onLongPress(){e.open("first")}}),v={preventFocusOnPress:!0,onPressStart(h){h.pointerType!=="touch"&&h.pointerType!=="keyboard"&&!n&&(ie(h.target),e.open(h.pointerType==="virtual"?"first":null))},onPress(h){h.pointerType==="touch"&&!n&&(ie(h.target),e.toggle())}};return delete a.onPress,{menuTriggerProps:{...a,...r==="press"?v:c,id:o,onKeyDown:f},menuProps:{...s,"aria-labelledby":o,autoFocus:e.focusStrategy||!0,onClose:e.close}}}function gn(i,e,t){let{validationBehavior:l,focus:n}=i;ee(()=>{if(l==="native"&&(t!=null&&t.current)&&!t.current.disabled){let f=e.realtimeValidation.isInvalid?e.realtimeValidation.validationErrors.join(" ")||"Invalid value.":"";t.current.setCustomValidity(f),t.current.hasAttribute("title")||(t.current.title=""),e.realtimeValidation.isInvalid||e.updateValidation($n(t.current))}});let r=d.useRef(!1),o=Z(()=>{r.current||e.resetValidation()}),a=Z(f=>{var u;e.displayValidation.isInvalid||e.commitValidation();let c=t==null||(u=t.current)===null||u===void 0?void 0:u.form;if(!f.defaultPrevented&&t&&c&&mn(c)===t.current){var v;n?n():(v=t.current)===null||v===void 0||v.focus(),pt("keyboard")}f.preventDefault()}),s=Z(()=>{e.commitValidation()});d.useEffect(()=>{let f=t==null?void 0:t.current;if(!f)return;let u=f.form,c=u==null?void 0:u.reset;return u&&(u.reset=()=>{r.current=!window.event||window.event.type==="message"&&window.event.target instanceof MessagePort,c==null||c.call(u),r.current=!1}),f.addEventListener("invalid",a),f.addEventListener("change",s),u==null||u.addEventListener("reset",o),()=>{f.removeEventListener("invalid",a),f.removeEventListener("change",s),u==null||u.removeEventListener("reset",o),u&&(u.reset=c)}},[t,a,s,o,l])}function yn(i){let e=i.validity;return{badInput:e.badInput,customError:e.customError,patternMismatch:e.patternMismatch,rangeOverflow:e.rangeOverflow,rangeUnderflow:e.rangeUnderflow,stepMismatch:e.stepMismatch,tooLong:e.tooLong,tooShort:e.tooShort,typeMismatch:e.typeMismatch,valueMissing:e.valueMissing,valid:e.valid}}function $n(i){return{isInvalid:!i.validity.valid,validationDetails:yn(i),validationErrors:i.validationMessage?[i.validationMessage]:[]}}function mn(i){for(let e=0;e<i.elements.length;e++){let t=i.elements[e];if(!t.validity.valid)return t}return null}class pl{*[Symbol.iterator](){let e=this.firstChild;for(;e;)yield e,e=e.nextSibling}get firstChild(){return this._firstChild}set firstChild(e){this._firstChild=e,this.ownerDocument.markDirty(this)}get lastChild(){return this._lastChild}set lastChild(e){this._lastChild=e,this.ownerDocument.markDirty(this)}get previousSibling(){return this._previousSibling}set previousSibling(e){this._previousSibling=e,this.ownerDocument.markDirty(this)}get nextSibling(){return this._nextSibling}set nextSibling(e){this._nextSibling=e,this.ownerDocument.markDirty(this)}get parentNode(){return this._parentNode}set parentNode(e){this._parentNode=e,this.ownerDocument.markDirty(this)}get isConnected(){var e;return((e=this.parentNode)===null||e===void 0?void 0:e.isConnected)||!1}invalidateChildIndices(e){(this._minInvalidChildIndex==null||!this._minInvalidChildIndex.isConnected||e.index<this._minInvalidChildIndex.index)&&(this._minInvalidChildIndex=e,this.ownerDocument.markDirty(this))}updateChildIndices(){let e=this._minInvalidChildIndex;for(;e;)e.index=e.previousSibling?e.previousSibling.index+1:0,e=e.nextSibling;this._minInvalidChildIndex=null}appendChild(e){e.parentNode&&e.parentNode.removeChild(e),this.firstChild==null&&(this.firstChild=e),this.lastChild?(this.lastChild.nextSibling=e,e.index=this.lastChild.index+1,e.previousSibling=this.lastChild):(e.previousSibling=null,e.index=0),e.parentNode=this,e.nextSibling=null,this.lastChild=e,this.ownerDocument.markDirty(this),this.isConnected&&this.ownerDocument.queueUpdate()}insertBefore(e,t){if(t==null)return this.appendChild(e);e.parentNode&&e.parentNode.removeChild(e),e.nextSibling=t,e.previousSibling=t.previousSibling,e.index=t.index-1,this.firstChild===t?this.firstChild=e:t.previousSibling&&(t.previousSibling.nextSibling=e),t.previousSibling=e,e.parentNode=t.parentNode,this.invalidateChildIndices(e),this.isConnected&&this.ownerDocument.queueUpdate()}removeChild(e){e.parentNode!==this||!this.ownerDocument.isMounted||(this._minInvalidChildIndex===e&&(this._minInvalidChildIndex=null),e.nextSibling&&(this.invalidateChildIndices(e.nextSibling),e.nextSibling.previousSibling=e.previousSibling),e.previousSibling&&(e.previousSibling.nextSibling=e.nextSibling),this.firstChild===e&&(this.firstChild=e.nextSibling),this.lastChild===e&&(this.lastChild=e.previousSibling),e.parentNode=null,e.nextSibling=null,e.previousSibling=null,e.index=0,this.ownerDocument.markDirty(e),this.isConnected&&this.ownerDocument.queueUpdate())}addEventListener(){}removeEventListener(){}get previousVisibleSibling(){let e=this.previousSibling;for(;e&&e.isHidden;)e=e.previousSibling;return e}get nextVisibleSibling(){let e=this.nextSibling;for(;e&&e.isHidden;)e=e.nextSibling;return e}get firstVisibleChild(){let e=this.firstChild;for(;e&&e.isHidden;)e=e.nextSibling;return e}get lastVisibleChild(){let e=this.lastChild;for(;e&&e.isHidden;)e=e.previousSibling;return e}constructor(e){this._firstChild=null,this._lastChild=null,this._previousSibling=null,this._nextSibling=null,this._parentNode=null,this._minInvalidChildIndex=null,this.ownerDocument=e}}class _ extends pl{get index(){return this._index}set index(e){this._index=e,this.ownerDocument.markDirty(this)}get level(){var e;return this.parentNode instanceof _?this.parentNode.level+(((e=this.node)===null||e===void 0?void 0:e.type)==="item"?1:0):0}getMutableNode(){return this.node==null?null:(this.isMutated||(this.node=this.node.clone(),this.isMutated=!0),this.ownerDocument.markDirty(this),this.node)}updateNode(){var e,t,l,n,r,o,a,s;let f=this.nextVisibleSibling,u=this.getMutableNode();if(u!=null){u.index=this.index,u.level=this.level;var c;u.parentKey=this.parentNode instanceof _&&(c=(e=this.parentNode.node)===null||e===void 0?void 0:e.key)!==null&&c!==void 0?c:null;var v;u.prevKey=(v=(l=this.previousVisibleSibling)===null||l===void 0||(t=l.node)===null||t===void 0?void 0:t.key)!==null&&v!==void 0?v:null;var h;u.nextKey=(h=f==null||(n=f.node)===null||n===void 0?void 0:n.key)!==null&&h!==void 0?h:null,u.hasChildNodes=!!this.firstChild;var g;u.firstChildKey=(g=(o=this.firstVisibleChild)===null||o===void 0||(r=o.node)===null||r===void 0?void 0:r.key)!==null&&g!==void 0?g:null;var x;if(u.lastChildKey=(x=(s=this.lastVisibleChild)===null||s===void 0||(a=s.node)===null||a===void 0?void 0:a.key)!==null&&x!==void 0?x:null,(u.colSpan!=null||u.colIndex!=null)&&f){var y,m;let S=((y=u.colIndex)!==null&&y!==void 0?y:u.index)+((m=u.colSpan)!==null&&m!==void 0?m:1);if(f.node!=null&&S!==f.node.colIndex){let D=f.getMutableNode();D.colIndex=S}}}}setProps(e,t,l,n,r){let o,{value:a,textValue:s,id:f,...u}=e;if(this.node==null?(o=new l(f??`react-aria-${++this.ownerDocument.nodeId}`),this.node=o):o=this.getMutableNode(),u.ref=t,o.props=u,o.rendered=n,o.render=r,o.value=a,o.textValue=s||(typeof u.children=="string"?u.children:"")||e["aria-label"]||"",f!=null&&f!==o.key)throw new Error("Cannot change the id of an item");u.colSpan!=null&&(o.colSpan=u.colSpan),this.isConnected&&this.ownerDocument.queueUpdate()}get style(){let e=this;return{get display(){return e.isHidden?"none":""},set display(t){let l=t==="none";if(e.isHidden!==l){var n,r;(((n=e.parentNode)===null||n===void 0?void 0:n.firstVisibleChild)===e||((r=e.parentNode)===null||r===void 0?void 0:r.lastVisibleChild)===e)&&e.ownerDocument.markDirty(e.parentNode);let o=e.previousVisibleSibling,a=e.nextVisibleSibling;o&&e.ownerDocument.markDirty(o),a&&e.ownerDocument.markDirty(a),e.isHidden=l,e.ownerDocument.markDirty(e)}}}}hasAttribute(){}setAttribute(){}setAttributeNS(){}removeAttribute(){}constructor(e,t){super(t),this.nodeType=8,this.isMutated=!0,this._index=0,this.isHidden=!1,this.node=null}}class xn extends pl{get isConnected(){return this.isMounted}createElement(e){return new _(e,this)}getMutableCollection(){return this.nextCollection||(this.nextCollection=this.collection.clone()),this.nextCollection}markDirty(e){this.dirtyNodes.add(e)}addNode(e){if(e.isHidden||e.node==null)return;let t=this.getMutableCollection();if(!t.getItem(e.node.key))for(let l of e)this.addNode(l);t.addNode(e.node)}removeNode(e){if(e.node==null)return;for(let l of e)this.removeNode(l);this.getMutableCollection().removeNode(e.node.key)}getCollection(){return this.inSubscription?this.collection.clone():(this.queuedRender=!1,this.updateCollection(),this.collection)}updateCollection(){for(let a of this.dirtyNodes)a instanceof _&&(!a.isConnected||a.isHidden)?this.removeNode(a):a.updateChildIndices();for(let a of this.dirtyNodes)a instanceof _&&(a.isConnected&&!a.isHidden&&(a.updateNode(),this.addNode(a)),a.isMutated=!1);if(this.dirtyNodes.clear(),this.nextCollection){var e,t,l,n,r,o;this.nextCollection.commit((r=(t=this.firstVisibleChild)===null||t===void 0||(e=t.node)===null||e===void 0?void 0:e.key)!==null&&r!==void 0?r:null,(o=(n=this.lastVisibleChild)===null||n===void 0||(l=n.node)===null||l===void 0?void 0:l.key)!==null&&o!==void 0?o:null,this.isSSR),this.isSSR||(this.collection=this.nextCollection,this.nextCollection=null)}}queueUpdate(){if(!(this.dirtyNodes.size===0||this.queuedRender)){this.queuedRender=!0,this.inSubscription=!0;for(let e of this.subscriptions)e();this.inSubscription=!1}}subscribe(e){return this.subscriptions.add(e),()=>this.subscriptions.delete(e)}resetAfterSSR(){this.isSSR&&(this.isSSR=!1,this.firstChild=null,this.lastChild=null,this.nodeId=0)}constructor(e){super(null),this.nodeType=11,this.ownerDocument=this,this.dirtyNodes=new Set,this.isSSR=!1,this.nodeId=0,this.nodesByProps=new WeakMap,this.isMounted=!0,this.nextCollection=null,this.subscriptions=new Set,this.queuedRender=!1,this.inSubscription=!1,this.collection=e,this.nextCollection=e}}function gl(i){let{children:e,items:t,idScope:l,addIdAndValue:n,dependencies:r=[]}=i,o=d.useMemo(()=>new WeakMap,r);return d.useMemo(()=>{if(t&&typeof e=="function"){let f=[];for(let u of t){let c=o.get(u);if(!c){c=e(u);var a,s;let v=(s=(a=c.props.id)!==null&&a!==void 0?a:u.key)!==null&&s!==void 0?s:u.id;if(v==null)throw new Error("Could not determine key for item");l&&(v=l+":"+v),c=d.cloneElement(c,n?{key:v,id:v,value:u}:{key:v}),o.set(u,c)}f.push(c)}return f}else if(typeof e!="function")return e},[e,t,o,l,n])}var De={exports:{}},Pe={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lt;function Sn(){if(lt)return Pe;lt=1;var i=mi();function e(c,v){return c===v&&(c!==0||1/c===1/v)||c!==c&&v!==v}var t=typeof Object.is=="function"?Object.is:e,l=i.useState,n=i.useEffect,r=i.useLayoutEffect,o=i.useDebugValue;function a(c,v){var h=v(),g=l({inst:{value:h,getSnapshot:v}}),x=g[0].inst,y=g[1];return r(function(){x.value=h,x.getSnapshot=v,s(x)&&y({inst:x})},[c,h,v]),n(function(){return s(x)&&y({inst:x}),c(function(){s(x)&&y({inst:x})})},[c]),o(h),h}function s(c){var v=c.getSnapshot;c=c.value;try{var h=v();return!t(c,h)}catch{return!0}}function f(c,v){return v()}var u=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?f:a;return Pe.useSyncExternalStore=i.useSyncExternalStore!==void 0?i.useSyncExternalStore:u,Pe}var it;function Dn(){return it||(it=1,De.exports=Sn()),De.exports}var Pn=Dn();const yl=d.createContext(!1),ne=d.createContext(null);function $l(i){if(d.useContext(ne))return i.content;let{collection:t,document:l}=wn(i.createCollection);return $.createElement($.Fragment,null,$.createElement(xi,null,$.createElement(ne.Provider,{value:l},i.content)),$.createElement(Cn,{render:i.children,collection:t}))}function Cn({collection:i,render:e}){return e(i)}function Kn(i,e,t){let l=gt(),n=d.useRef(l);n.current=l;let r=d.useCallback(()=>n.current?t():e(),[e,t]);return Pn.useSyncExternalStore(i,r)}const En=typeof $.useSyncExternalStore=="function"?$.useSyncExternalStore:Kn;function wn(i){let[e]=d.useState(()=>new xn((i==null?void 0:i())||new Si)),t=d.useCallback(o=>e.subscribe(o),[e]),l=d.useCallback(()=>{let o=e.getCollection();return e.isSSR&&e.resetAfterSSR(),o},[e]),n=d.useCallback(()=>(e.isSSR=!0,e.getCollection()),[e]),r=En(t,l,n);return ee(()=>(e.isMounted=!0,()=>{e.isMounted=!1}),[e]),{collection:r,document:e}}const ke=d.createContext(null);function kn(i){var e;return e=class extends yt{},e.type=i,e}function Bn(i,e,t,l,n,r){typeof i=="string"&&(i=kn(i));let o=d.useCallback(s=>{s==null||s.setProps(e,t,i,l,r)},[e,t,l,r,i]),a=d.useContext(ke);if(a){let s=a.ownerDocument.nodesByProps.get(e);return s||(s=a.ownerDocument.createElement(i.type),s.setProps(e,t,i,l,r),a.appendChild(s),a.ownerDocument.updateCollection(),a.ownerDocument.nodesByProps.set(e,s)),n?$.createElement(ke.Provider,{value:s},n):null}return $.createElement(i.type,{ref:o},n)}function ml(i,e){let t=({node:n})=>e(n.props,n.props.ref,n),l=d.forwardRef((n,r)=>{let o=d.useContext(He);if(!d.useContext(yl)){if(e.length>=3)throw new Error(e.name+" cannot be rendered outside a collection.");return e(n,r)}return Bn(i,n,r,"children"in n?n.children:null,null,s=>$.createElement(He.Provider,{value:o},$.createElement(t,{node:s})))});return l.displayName=e.name,l}function Mn(i){return gl({...i,addIdAndValue:!0})}const nt=d.createContext(null);function Fn(i){let e=d.useContext(nt),t=((e==null?void 0:e.dependencies)||[]).concat(i.dependencies),l=i.idScope||(e==null?void 0:e.idScope),n=Mn({...i,idScope:l,dependencies:t});return d.useContext(ne)&&(n=$.createElement(An,null,n)),e=d.useMemo(()=>({dependencies:t,idScope:l}),[l,...t]),$.createElement(nt.Provider,{value:e},n)}function An({children:i}){let e=d.useContext(ne),t=d.useMemo(()=>$.createElement(ne.Provider,{value:null},$.createElement(yl.Provider,{value:!0},i)),[i]);return gt()?$.createElement(ke.Provider,{value:e},t):vt.createPortal(t,e)}const Rn=d.createContext(null),In={CollectionRoot({collection:i,renderDropIndicator:e}){return rt(i,null,e)},CollectionBranch({collection:i,parent:e,renderDropIndicator:t}){return rt(i,e,t)}};function rt(i,e,t){return gl({items:e?i.getChildren(e.key):i,dependencies:[t],children(l){let n=l.render(l);return!t||l.type!=="item"?n:$.createElement($.Fragment,null,t({type:"item",key:l.key,dropPosition:"before"}),n,zn(i,l,t))}})}function zn(i,e,t){let l=e.key,n=i.getKeyAfter(l),r=n!=null?i.getItem(n):null;for(;r!=null&&r.type!=="item";)n=i.getKeyAfter(r.key),r=n!=null?i.getItem(n):null;let o=e.nextKey!=null?i.getItem(e.nextKey):null;for(;o!=null&&o.type!=="item";)o=o.nextKey!=null?i.getItem(o.nextKey):null;let a=[];if(o==null){let s=e;for(;s&&(!r||s.parentKey!==r.parentKey&&r.level<s.level);){let f=t({type:"item",key:s.key,dropPosition:"after"});d.isValidElement(f)&&a.push(d.cloneElement(f,{key:`${s.key}-after`})),s=s.parentKey!=null?i.getItem(s.parentKey):null}}return a}const ze=d.createContext(In),Le=new WeakMap;function Ln(i,e,t){let{keyboardDelegate:l,isDisabled:n,isRequired:r,name:o,form:a,validationBehavior:s="aria"}=i,f=Ae({usage:"search",sensitivity:"base"}),u=d.useMemo(()=>l||new Ie(e.collection,e.disabledKeys,t,f),[l,e.collection,e.disabledKeys,f,t]),{menuTriggerProps:c,menuProps:v}=pn({isDisabled:n,type:"listbox"},e,t),h=B=>{switch(B.key){case"ArrowLeft":{var K,I;B.preventDefault();let T=e.selectedKey!=null?(K=u.getKeyAbove)===null||K===void 0?void 0:K.call(u,e.selectedKey):(I=u.getFirstKey)===null||I===void 0?void 0:I.call(u);T&&e.setSelectedKey(T);break}case"ArrowRight":{var A,L;B.preventDefault();let T=e.selectedKey!=null?(A=u.getKeyBelow)===null||A===void 0?void 0:A.call(u,e.selectedKey):(L=u.getFirstKey)===null||L===void 0?void 0:L.call(u);T&&e.setSelectedKey(T);break}}},{typeSelectProps:g}=Mt({keyboardDelegate:u,selectionManager:e.selectionManager,onTypeSelect(B){e.setSelectedKey(B)}}),{isInvalid:x,validationErrors:y,validationDetails:m}=e.displayValidation,{labelProps:S,fieldProps:D,descriptionProps:C,errorMessageProps:M}=Zi({...i,labelElementType:"span",isInvalid:x,errorMessage:i.errorMessage||y});g.onKeyDown=g.onKeyDownCapture,delete g.onKeyDownCapture;let E=H(i,{labelable:!0}),k=z(g,c,D),F=G();return Le.set(e,{isDisabled:n,isRequired:r,name:o,form:a,validationBehavior:s}),{labelProps:{...S,onClick:()=>{if(!i.isDisabled){var B;(B=t.current)===null||B===void 0||B.focus(),pt("keyboard")}}},triggerProps:z(E,{...k,isDisabled:n,onKeyDown:Me(k.onKeyDown,h,i.onKeyDown),onKeyUp:i.onKeyUp,"aria-labelledby":[F,k["aria-labelledby"],k["aria-label"]&&!k["aria-labelledby"]?k.id:null].filter(Boolean).join(" "),onFocus(B){e.isFocused||(i.onFocus&&i.onFocus(B),i.onFocusChange&&i.onFocusChange(!0),e.setFocused(!0))},onBlur(B){e.isOpen||(i.onBlur&&i.onBlur(B),i.onFocusChange&&i.onFocusChange(!1),e.setFocused(!1))}}),valueProps:{id:F},menuProps:{...v,autoFocus:e.focusStrategy||!0,shouldSelectOnPressUp:!0,shouldFocusOnHover:!0,disallowEmptySelection:!0,linkBehavior:"selection",onBlur:B=>{B.currentTarget.contains(B.relatedTarget)||(i.onBlur&&i.onBlur(B),i.onFocusChange&&i.onFocusChange(!1),e.setFocused(!1))},"aria-labelledby":[D["aria-labelledby"],k["aria-label"]&&!D["aria-labelledby"]?k.id:null].filter(Boolean).join(" ")},descriptionProps:C,errorMessageProps:M,isInvalid:x,validationErrors:y,validationDetails:m,hiddenSelectProps:{isDisabled:n,name:o,label:i.label,state:e,triggerRef:t,form:a}}}function Tn(i,e,t){let l=Le.get(e)||{},{autoComplete:n,name:r=l.name,form:o=l.form,isDisabled:a=l.isDisabled}=i,{validationBehavior:s,isRequired:f}=l,{visuallyHiddenProps:u}=Di({style:{position:"fixed",top:0,left:0}});Li(i.selectRef,e.defaultSelectedKey,e.setSelectedKey),gn({validationBehavior:s,focus:()=>{var h;return(h=t.current)===null||h===void 0?void 0:h.focus()}},e,i.selectRef);let c=d.useCallback(h=>e.setSelectedKey(h.currentTarget.value),[e.setSelectedKey]);var v;return{containerProps:{...u,"aria-hidden":!0,"data-react-aria-prevent-focus":!0,"data-a11y-ignore":"aria-hidden-focus"},inputProps:{style:{display:"none"}},selectProps:{tabIndex:-1,autoComplete:n,disabled:a,required:s==="native"&&f,name:r,form:o,value:(v=e.selectedKey)!==null&&v!==void 0?v:"",onChange:c,onInput:c}}}function Vn(i){let{state:e,triggerRef:t,label:l,name:n,form:r,isDisabled:o}=i,a=d.useRef(null),s=d.useRef(null),{containerProps:f,selectProps:u}=Tn({...i,selectRef:e.collection.size<=300?a:s},e,t);if(e.collection.size<=300)return $.createElement("div",{...f,"data-testid":"hidden-select-container"},$.createElement("label",null,l,$.createElement("select",{...u,ref:a},$.createElement("option",null),[...e.collection.getKeys()].map(v=>{let h=e.collection.getItem(v);if(h&&h.type==="item")return $.createElement("option",{key:h.key,value:h.key},h.textValue)}))));if(n){let v=Le.get(e)||{},{validationBehavior:h}=v;var c;let g={type:"hidden",autoComplete:u.autoComplete,name:n,form:r,disabled:o,value:(c=e.selectedKey)!==null&&c!==void 0?c:""};return h==="native"?$.createElement("input",{...g,ref:s,style:{display:"none"},type:"text",required:u.required,onChange:()=>{}}):$.createElement("input",{...g,ref:s})}return null}const xl=d.createContext({}),ue=d.createContext({});class ot{*[Symbol.iterator](){yield*this.iterable}get size(){return this._size}getKeys(){return this.keyMap.keys()}getKeyBefore(e){let t=this.keyMap.get(e);var l;return t&&(l=t.prevKey)!==null&&l!==void 0?l:null}getKeyAfter(e){let t=this.keyMap.get(e);var l;return t&&(l=t.nextKey)!==null&&l!==void 0?l:null}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(e){var t;return(t=this.keyMap.get(e))!==null&&t!==void 0?t:null}at(e){const t=[...this.getKeys()];return this.getItem(t[e])}getChildren(e){let t=this.keyMap.get(e);return(t==null?void 0:t.childNodes)||[]}constructor(e){this.keyMap=new Map,this.firstKey=null,this.lastKey=null,this.iterable=e;let t=a=>{if(this.keyMap.set(a.key,a),a.childNodes&&a.type==="section")for(let s of a.childNodes)t(s)};for(let a of e)t(a);let l=null,n=0,r=0;for(let[a,s]of this.keyMap)l?(l.nextKey=a,s.prevKey=l.key):(this.firstKey=a,s.prevKey=void 0),s.type==="item"&&(s.index=n++),(s.type==="section"||s.type==="item")&&r++,l=s,l.nextKey=void 0;this._size=r;var o;this.lastKey=(o=l==null?void 0:l.key)!==null&&o!==void 0?o:null}}class N extends Set{constructor(e,t,l){super(e),e instanceof N?(this.anchorKey=t??e.anchorKey,this.currentKey=l??e.currentKey):(this.anchorKey=t??null,this.currentKey=l??null)}}function On(i,e){if(i.size!==e.size)return!1;for(let t of i)if(!e.has(t))return!1;return!0}function Un(i){let{selectionMode:e="none",disallowEmptySelection:t=!1,allowDuplicateSelectionEvents:l,selectionBehavior:n="toggle",disabledBehavior:r="all"}=i,o=d.useRef(!1),[,a]=d.useState(!1),s=d.useRef(null),f=d.useRef(null),[,u]=d.useState(null),c=d.useMemo(()=>at(i.selectedKeys),[i.selectedKeys]),v=d.useMemo(()=>at(i.defaultSelectedKeys,new N),[i.defaultSelectedKeys]),[h,g]=$t(c,v,i.onSelectionChange),x=d.useMemo(()=>i.disabledKeys?new Set(i.disabledKeys):new Set,[i.disabledKeys]),[y,m]=d.useState(n);n==="replace"&&y==="toggle"&&typeof h=="object"&&h.size===0&&m("replace");let S=d.useRef(n);return d.useEffect(()=>{n!==S.current&&(m(n),S.current=n)},[n]),{selectionMode:e,disallowEmptySelection:t,selectionBehavior:y,setSelectionBehavior:m,get isFocused(){return o.current},setFocused(D){o.current=D,a(D)},get focusedKey(){return s.current},get childFocusStrategy(){return f.current},setFocusedKey(D,C="first"){s.current=D,f.current=C,u(D)},selectedKeys:h,setSelectedKeys(D){(l||!On(D,h))&&g(D)},disabledKeys:x,disabledBehavior:r}}function at(i,e){return i?i==="all"?"all":new N(i):e}class Te{get selectionMode(){return this.state.selectionMode}get disallowEmptySelection(){return this.state.disallowEmptySelection}get selectionBehavior(){return this.state.selectionBehavior}setSelectionBehavior(e){this.state.setSelectionBehavior(e)}get isFocused(){return this.state.isFocused}setFocused(e){this.state.setFocused(e)}get focusedKey(){return this.state.focusedKey}get childFocusStrategy(){return this.state.childFocusStrategy}setFocusedKey(e,t){(e==null||this.collection.getItem(e))&&this.state.setFocusedKey(e,t)}get selectedKeys(){return this.state.selectedKeys==="all"?new Set(this.getSelectAllKeys()):this.state.selectedKeys}get rawSelection(){return this.state.selectedKeys}isSelected(e){if(this.state.selectionMode==="none")return!1;let t=this.getKey(e);return t==null?!1:this.state.selectedKeys==="all"?this.canSelectItem(t):this.state.selectedKeys.has(t)}get isEmpty(){return this.state.selectedKeys!=="all"&&this.state.selectedKeys.size===0}get isSelectAll(){if(this.isEmpty)return!1;if(this.state.selectedKeys==="all")return!0;if(this._isSelectAll!=null)return this._isSelectAll;let e=this.getSelectAllKeys(),t=this.state.selectedKeys;return this._isSelectAll=e.every(l=>t.has(l)),this._isSelectAll}get firstSelectedKey(){let e=null;for(let l of this.state.selectedKeys){let n=this.collection.getItem(l);(!e||n&&xe(this.collection,n,e)<0)&&(e=n)}var t;return(t=e==null?void 0:e.key)!==null&&t!==void 0?t:null}get lastSelectedKey(){let e=null;for(let l of this.state.selectedKeys){let n=this.collection.getItem(l);(!e||n&&xe(this.collection,n,e)>0)&&(e=n)}var t;return(t=e==null?void 0:e.key)!==null&&t!==void 0?t:null}get disabledKeys(){return this.state.disabledKeys}get disabledBehavior(){return this.state.disabledBehavior}extendSelection(e){if(this.selectionMode==="none")return;if(this.selectionMode==="single"){this.replaceSelection(e);return}let t=this.getKey(e);if(t==null)return;let l;if(this.state.selectedKeys==="all")l=new N([t],t,t);else{let o=this.state.selectedKeys;var n;let a=(n=o.anchorKey)!==null&&n!==void 0?n:t;l=new N(o,a,t);var r;for(let s of this.getKeyRange(a,(r=o.currentKey)!==null&&r!==void 0?r:t))l.delete(s);for(let s of this.getKeyRange(t,a))this.canSelectItem(s)&&l.add(s)}this.state.setSelectedKeys(l)}getKeyRange(e,t){let l=this.collection.getItem(e),n=this.collection.getItem(t);return l&&n?xe(this.collection,l,n)<=0?this.getKeyRangeInternal(e,t):this.getKeyRangeInternal(t,e):[]}getKeyRangeInternal(e,t){var l;if(!((l=this.layoutDelegate)===null||l===void 0)&&l.getKeyRange)return this.layoutDelegate.getKeyRange(e,t);let n=[],r=e;for(;r!=null;){let o=this.collection.getItem(r);if(o&&(o.type==="item"||o.type==="cell"&&this.allowsCellSelection)&&n.push(r),r===t)return n;r=this.collection.getKeyAfter(r)}return[]}getKey(e){let t=this.collection.getItem(e);if(!t||t.type==="cell"&&this.allowsCellSelection)return e;for(;t&&t.type!=="item"&&t.parentKey!=null;)t=this.collection.getItem(t.parentKey);return!t||t.type!=="item"?null:t.key}toggleSelection(e){if(this.selectionMode==="none")return;if(this.selectionMode==="single"&&!this.isSelected(e)){this.replaceSelection(e);return}let t=this.getKey(e);if(t==null)return;let l=new N(this.state.selectedKeys==="all"?this.getSelectAllKeys():this.state.selectedKeys);l.has(t)?l.delete(t):this.canSelectItem(t)&&(l.add(t),l.anchorKey=t,l.currentKey=t),!(this.disallowEmptySelection&&l.size===0)&&this.state.setSelectedKeys(l)}replaceSelection(e){if(this.selectionMode==="none")return;let t=this.getKey(e);if(t==null)return;let l=this.canSelectItem(t)?new N([t],t,t):new N;this.state.setSelectedKeys(l)}setSelectedKeys(e){if(this.selectionMode==="none")return;let t=new N;for(let l of e){let n=this.getKey(l);if(n!=null&&(t.add(n),this.selectionMode==="single"))break}this.state.setSelectedKeys(t)}getSelectAllKeys(){let e=[],t=l=>{for(;l!=null;){if(this.canSelectItem(l)){var n;let o=this.collection.getItem(l);(o==null?void 0:o.type)==="item"&&e.push(l);var r;o!=null&&o.hasChildNodes&&(this.allowsCellSelection||o.type!=="item")&&t((r=(n=on(Ft(o,this.collection)))===null||n===void 0?void 0:n.key)!==null&&r!==void 0?r:null)}l=this.collection.getKeyAfter(l)}};return t(this.collection.getFirstKey()),e}selectAll(){!this.isSelectAll&&this.selectionMode==="multiple"&&this.state.setSelectedKeys("all")}clearSelection(){!this.disallowEmptySelection&&(this.state.selectedKeys==="all"||this.state.selectedKeys.size>0)&&this.state.setSelectedKeys(new N)}toggleSelectAll(){this.isSelectAll?this.clearSelection():this.selectAll()}select(e,t){this.selectionMode!=="none"&&(this.selectionMode==="single"?this.isSelected(e)&&!this.disallowEmptySelection?this.toggleSelection(e):this.replaceSelection(e):this.selectionBehavior==="toggle"||t&&(t.pointerType==="touch"||t.pointerType==="virtual")?this.toggleSelection(e):this.replaceSelection(e))}isSelectionEqual(e){if(e===this.state.selectedKeys)return!0;let t=this.selectedKeys;if(e.size!==t.size)return!1;for(let l of e)if(!t.has(l))return!1;for(let l of t)if(!e.has(l))return!1;return!0}canSelectItem(e){var t;if(this.state.selectionMode==="none"||this.state.disabledKeys.has(e))return!1;let l=this.collection.getItem(e);return!(!l||!(l==null||(t=l.props)===null||t===void 0)&&t.isDisabled||l.type==="cell"&&!this.allowsCellSelection)}isDisabled(e){var t,l;return this.state.disabledBehavior==="all"&&(this.state.disabledKeys.has(e)||!!(!((l=this.collection.getItem(e))===null||l===void 0||(t=l.props)===null||t===void 0)&&t.isDisabled))}isLink(e){var t,l;return!!(!((l=this.collection.getItem(e))===null||l===void 0||(t=l.props)===null||t===void 0)&&t.href)}getItemProps(e){var t;return(t=this.collection.getItem(e))===null||t===void 0?void 0:t.props}withCollection(e){return new Te(e,this.state,{allowsCellSelection:this.allowsCellSelection,layoutDelegate:this.layoutDelegate||void 0})}constructor(e,t,l){this.collection=e,this.state=t;var n;this.allowsCellSelection=(n=l==null?void 0:l.allowsCellSelection)!==null&&n!==void 0?n:!1,this._isSelectAll=null,this.layoutDelegate=(l==null?void 0:l.layoutDelegate)||null}}function Sl(i){let{filter:e,layoutDelegate:t}=i,l=Un(i),n=d.useMemo(()=>i.disabledKeys?new Set(i.disabledKeys):new Set,[i.disabledKeys]),r=d.useCallback(f=>e?new ot(e(f)):new ot(f),[e]),o=d.useMemo(()=>({suppressTextValueWarning:i.suppressTextValueWarning}),[i.suppressTextValueWarning]),a=rn(i,r,o),s=d.useMemo(()=>new Te(a,l,{layoutDelegate:t}),[a,l,t]);return Dl(a,s),{collection:a,disabledKeys:n,selectionManager:s}}function Nn(i,e){let t=d.useMemo(()=>e?i.collection.filter(e):i.collection,[i.collection,e]),l=i.selectionManager.withCollection(t);return Dl(t,l),{collection:t,selectionManager:l,disabledKeys:i.disabledKeys}}function Dl(i,e){const t=d.useRef(null);d.useEffect(()=>{if(e.focusedKey!=null&&!i.getItem(e.focusedKey)&&t.current){const u=t.current.getItem(e.focusedKey),c=[...t.current.getKeys()].map(m=>{const S=t.current.getItem(m);return(S==null?void 0:S.type)==="item"?S:null}).filter(m=>m!==null),v=[...i.getKeys()].map(m=>{const S=i.getItem(m);return(S==null?void 0:S.type)==="item"?S:null}).filter(m=>m!==null);var l,n;const h=((l=c==null?void 0:c.length)!==null&&l!==void 0?l:0)-((n=v==null?void 0:v.length)!==null&&n!==void 0?n:0);var r,o,a;let g=Math.min(h>1?Math.max(((r=u==null?void 0:u.index)!==null&&r!==void 0?r:0)-h+1,0):(o=u==null?void 0:u.index)!==null&&o!==void 0?o:0,((a=v==null?void 0:v.length)!==null&&a!==void 0?a:0)-1),x=null,y=!1;for(;g>=0;){if(!e.isDisabled(v[g].key)){x=v[g];break}if(g<v.length-1&&!y)g++;else{y=!0;var s,f;g>((s=u==null?void 0:u.index)!==null&&s!==void 0?s:0)&&(g=(f=u==null?void 0:u.index)!==null&&f!==void 0?f:0),g--}}e.setFocusedKey(x?x.key:null)}t.current=i},[i,e])}function jn(i){var e;let[t,l]=$t(i.selectedKey,(e=i.defaultSelectedKey)!==null&&e!==void 0?e:null,i.onSelectionChange),n=d.useMemo(()=>t!=null?[t]:[],[t]),{collection:r,disabledKeys:o,selectionManager:a}=Sl({...i,selectionMode:"single",disallowEmptySelection:!0,allowDuplicateSelectionEvents:!0,selectedKeys:n,onSelectionChange:f=>{if(f==="all")return;var u;let c=(u=f.values().next().value)!==null&&u!==void 0?u:null;c===t&&i.onSelectionChange&&i.onSelectionChange(c),l(c)}}),s=t!=null?r.getItem(t):null;return{collection:r,disabledKeys:o,selectionManager:a,selectedKey:t,setSelectedKey:l,selectedItem:s}}function Hn(i){let e=Pi(i),[t,l]=d.useState(null),n=jn({...i,onSelectionChange:u=>{i.onSelectionChange!=null&&i.onSelectionChange(u),e.close(),r.commitValidation()}}),r=fn({...i,value:n.selectedKey}),[o,a]=d.useState(!1),[s]=d.useState(n.selectedKey);var f;return{...r,...n,...e,defaultSelectedKey:(f=i.defaultSelectedKey)!==null&&f!==void 0?f:s,focusStrategy:t,open(u=null){n.collection.size!==0&&(l(u),e.open())},toggle(u=null){n.collection.size!==0&&(l(u),e.toggle())},isFocused:o,setFocused:a}}const qn=d.createContext(null),Wn=d.createContext(null);var Pl={};Pl={colorSwatchPicker:"تغييرات الألوان",dropzoneLabel:"DropZone",selectPlaceholder:"حدد عنصرًا",tableResizer:"أداة تغيير الحجم"};var Cl={};Cl={colorSwatchPicker:"Цветови мостри",dropzoneLabel:"DropZone",selectPlaceholder:"Изберете предмет",tableResizer:"Преоразмерител"};var Kl={};Kl={colorSwatchPicker:"Vzorky barev",dropzoneLabel:"Místo pro přetažení",selectPlaceholder:"Vyberte položku",tableResizer:"Změna velikosti"};var El={};El={colorSwatchPicker:"Farveprøver",dropzoneLabel:"DropZone",selectPlaceholder:"Vælg et element",tableResizer:"Størrelsesændring"};var wl={};wl={colorSwatchPicker:"Farbfelder",dropzoneLabel:"Ablegebereich",selectPlaceholder:"Element wählen",tableResizer:"Größenanpassung"};var kl={};kl={colorSwatchPicker:"Χρωματικά δείγματα",dropzoneLabel:"DropZone",selectPlaceholder:"Επιλέξτε ένα αντικείμενο",tableResizer:"Αλλαγή μεγέθους"};var Bl={};Bl={selectPlaceholder:"Select an item",tableResizer:"Resizer",dropzoneLabel:"DropZone",colorSwatchPicker:"Color swatches"};var Ml={};Ml={colorSwatchPicker:"Muestras de colores",dropzoneLabel:"DropZone",selectPlaceholder:"Seleccionar un artículo",tableResizer:"Cambiador de tamaño"};var Fl={};Fl={colorSwatchPicker:"Värvinäidised",dropzoneLabel:"DropZone",selectPlaceholder:"Valige üksus",tableResizer:"Suuruse muutja"};var Al={};Al={colorSwatchPicker:"Värimallit",dropzoneLabel:"DropZone",selectPlaceholder:"Valitse kohde",tableResizer:"Koon muuttaja"};var Rl={};Rl={colorSwatchPicker:"Échantillons de couleurs",dropzoneLabel:"DropZone",selectPlaceholder:"Sélectionner un élément",tableResizer:"Redimensionneur"};var Il={};Il={colorSwatchPicker:"דוגמיות צבע",dropzoneLabel:"DropZone",selectPlaceholder:"בחר פריט",tableResizer:"שינוי גודל"};var zl={};zl={colorSwatchPicker:"Uzorci boja",dropzoneLabel:"Zona spuštanja",selectPlaceholder:"Odaberite stavku",tableResizer:"Promjena veličine"};var Ll={};Ll={colorSwatchPicker:"Színtárak",dropzoneLabel:"DropZone",selectPlaceholder:"Válasszon ki egy elemet",tableResizer:"Átméretező"};var Tl={};Tl={colorSwatchPicker:"Campioni di colore",dropzoneLabel:"Zona di rilascio",selectPlaceholder:"Seleziona un elemento",tableResizer:"Ridimensionamento"};var Vl={};Vl={colorSwatchPicker:"カラースウォッチ",dropzoneLabel:"ドロップゾーン",selectPlaceholder:"項目を選択",tableResizer:"サイズ変更ツール"};var Ol={};Ol={colorSwatchPicker:"색상 견본",dropzoneLabel:"드롭 영역",selectPlaceholder:"항목 선택",tableResizer:"크기 조정기"};var Ul={};Ul={colorSwatchPicker:"Spalvų pavyzdžiai",dropzoneLabel:"„DropZone“",selectPlaceholder:"Pasirinkite elementą",tableResizer:"Dydžio keitiklis"};var Nl={};Nl={colorSwatchPicker:"Krāsu paraugi",dropzoneLabel:"DropZone",selectPlaceholder:"Izvēlēties vienumu",tableResizer:"Izmēra mainītājs"};var jl={};jl={colorSwatchPicker:"Fargekart",dropzoneLabel:"Droppsone",selectPlaceholder:"Velg et element",tableResizer:"Størrelsesendrer"};var Hl={};Hl={colorSwatchPicker:"kleurstalen",dropzoneLabel:"DropZone",selectPlaceholder:"Selecteer een item",tableResizer:"Resizer"};var ql={};ql={colorSwatchPicker:"Próbki kolorów",dropzoneLabel:"Strefa upuszczania",selectPlaceholder:"Wybierz element",tableResizer:"Zmiana rozmiaru"};var Wl={};Wl={colorSwatchPicker:"Amostras de cores",dropzoneLabel:"DropZone",selectPlaceholder:"Selecione um item",tableResizer:"Redimensionador"};var Zl={};Zl={colorSwatchPicker:"Amostras de cores",dropzoneLabel:"DropZone",selectPlaceholder:"Selecione um item",tableResizer:"Redimensionador"};var Gl={};Gl={colorSwatchPicker:"Specimene de culoare",dropzoneLabel:"Zonă de plasare",selectPlaceholder:"Selectați un element",tableResizer:"Instrument de redimensionare"};var Yl={};Yl={colorSwatchPicker:"Цветовые образцы",dropzoneLabel:"DropZone",selectPlaceholder:"Выберите элемент",tableResizer:"Средство изменения размера"};var Xl={};Xl={colorSwatchPicker:"Vzorkovníky farieb",dropzoneLabel:"DropZone",selectPlaceholder:"Vyberte položku",tableResizer:"Nástroj na zmenu veľkosti"};var Jl={};Jl={colorSwatchPicker:"Barvne palete",dropzoneLabel:"DropZone",selectPlaceholder:"Izberite element",tableResizer:"Spreminjanje velikosti"};var Ql={};Ql={colorSwatchPicker:"Uzorci boje",dropzoneLabel:"DropZone",selectPlaceholder:"Izaberite stavku",tableResizer:"Promena veličine"};var _l={};_l={colorSwatchPicker:"Färgrutor",dropzoneLabel:"DropZone",selectPlaceholder:"Välj en artikel",tableResizer:"Storleksändrare"};var ei={};ei={colorSwatchPicker:"Renk örnekleri",dropzoneLabel:"Bırakma Bölgesi",selectPlaceholder:"Bir öğe seçin",tableResizer:"Yeniden boyutlandırıcı"};var ti={};ti={colorSwatchPicker:"Зразки кольорів",dropzoneLabel:"DropZone",selectPlaceholder:"Виберіть елемент",tableResizer:"Засіб змінення розміру"};var li={};li={colorSwatchPicker:"颜色色板",dropzoneLabel:"放置区域",selectPlaceholder:"选择一个项目",tableResizer:"尺寸调整器"};var ii={};ii={colorSwatchPicker:"色票",dropzoneLabel:"放置區",selectPlaceholder:"選取項目",tableResizer:"大小調整器"};var ni={};ni={"ar-AE":Pl,"bg-BG":Cl,"cs-CZ":Kl,"da-DK":El,"de-DE":wl,"el-GR":kl,"en-US":Bl,"es-ES":Ml,"et-EE":Fl,"fi-FI":Al,"fr-FR":Rl,"he-IL":Il,"hr-HR":zl,"hu-HU":Ll,"it-IT":Tl,"ja-JP":Vl,"ko-KR":Ol,"lt-LT":Ul,"lv-LV":Nl,"nb-NO":jl,"nl-NL":Hl,"pl-PL":ql,"pt-BR":Wl,"pt-PT":Zl,"ro-RO":Gl,"ru-RU":Yl,"sk-SK":Xl,"sl-SI":Jl,"sr-SP":Ql,"sv-SE":_l,"tr-TR":ei,"uk-UA":ti,"zh-CN":li,"zh-TW":ii};const de=d.createContext({}),ri=d.createContext(null),Zn=d.forwardRef(function(e,t){let{render:l}=d.useContext(ri);return $.createElement($.Fragment,null,l(e,t))});function oi(i,e){var t;let l=i==null?void 0:i.renderDropIndicator,n=i==null||(t=i.isVirtualDragging)===null||t===void 0?void 0:t.call(i),r=d.useCallback(o=>{if(n||e!=null&&e.isDropTarget(o))return l?l(o):$.createElement(Zn,{target:o})},[e==null?void 0:e.target,n,l]);return i!=null&&i.useDropIndicator?r:void 0}function Gn(i,e,t){var l,n;let r=i.focusedKey,o=null;if(!(e==null||(l=e.isVirtualDragging)===null||l===void 0)&&l.call(e)&&(t==null||(n=t.target)===null||n===void 0?void 0:n.type)==="item"&&(o=t.target.key,t.target.dropPosition==="after")){let c=t.collection.getKeyAfter(o),v=null;if(c!=null){var a,s;let h=(s=(a=t.collection.getItem(o))===null||a===void 0?void 0:a.level)!==null&&s!==void 0?s:0;for(;c;){let g=t.collection.getItem(c);if(!g)break;if(g.type!=="item"){c=t.collection.getKeyAfter(c);continue}var f;if(((f=g.level)!==null&&f!==void 0?f:0)<=h)break;v=c,c=t.collection.getKeyAfter(c)}}var u;o=(u=c??v)!==null&&u!==void 0?u:o}return d.useMemo(()=>new Set([r,o].filter(c=>c!=null)),[r,o])}const Yn=d.createContext({}),Xn=d.createContext({});class Jn extends yt{filter(e,t){let l=t.getItem(this.prevKey);if(l&&l.type!=="separator"){let n=this.clone();return t.addDescendants(n,e),n}return null}}Jn.type="separator";const Ve=d.createContext(null),te=d.createContext(null),sr=d.forwardRef(function(e,t){[e,t]=se(e,t,Ve);let l=d.useContext(te);return l?$.createElement(ai,{state:l,props:e,listBoxRef:t}):$.createElement($l,{content:$.createElement(Fn,e)},n=>$.createElement(Qn,{props:e,listBoxRef:t,collection:n}))});function Qn({props:i,listBoxRef:e,collection:t}){i={...i,collection:t,children:null,items:null};let{layoutDelegate:l}=d.useContext(ze),n=Sl({...i,layoutDelegate:l});return $.createElement(ai,{state:n,props:i,listBoxRef:e})}function ai({state:i,props:e,listBoxRef:t}){[e,t]=se(e,t,Ai);let{dragAndDropHooks:l,layout:n="stack",orientation:r="vertical",filter:o}=e,a=Nn(i,o),{collection:s,selectionManager:f}=a,u=!!(l!=null&&l.useDraggableCollectionState),c=!!(l!=null&&l.useDroppableCollectionState),{direction:v}=Be(),{disabledBehavior:h,disabledKeys:g}=f,x=Ae({usage:"search",sensitivity:"base"}),{isVirtualized:y,layoutDelegate:m,dropTargetDelegate:S,CollectionRoot:D}=d.useContext(ze),C=d.useMemo(()=>e.keyboardDelegate||new Ie({collection:s,collator:x,ref:t,disabledKeys:g,disabledBehavior:h,layout:n,orientation:r,direction:v,layoutDelegate:m}),[s,x,t,h,g,r,v,e.keyboardDelegate,n,m]),{listBoxProps:M}=tn({...e,shouldSelectOnPressUp:u||e.shouldSelectOnPressUp,keyboardDelegate:C,isVirtualized:y},a,t);d.useRef(u),d.useRef(c),d.useEffect(()=>{},[u,c]);let E,k,F,B=!1,K=null,I=d.useRef(null);if(u&&l){E=l.useDraggableCollectionState({collection:s,selectionManager:f,preview:l.renderDragPreview?I:void 0}),l.useDraggableCollection({},E,t);let R=l.DragPreview;K=l.renderDragPreview?$.createElement(R,{ref:I},l.renderDragPreview):null}if(c&&l){k=l.useDroppableCollectionState({collection:s,selectionManager:f});let R=l.dropTargetDelegate||S||new l.ListDropTargetDelegate(s,t,{orientation:r,layout:n,direction:v});F=l.useDroppableCollection({keyboardDelegate:C,dropTargetDelegate:R},k,t),B=k.isDropTarget({type:"root"})}let{focusProps:A,isFocused:L,isFocusVisible:T}=xt(),U=a.collection.size===0,q={isDropTarget:B,isEmpty:U,isFocused:L,isFocusVisible:T,layout:e.layout||"stack",state:a},p=J({className:e.className,style:e.style,defaultClassName:"react-aria-ListBox",values:q}),w=null;U&&e.renderEmptyState&&(w=$.createElement("div",{role:"option",style:{display:"contents"}},e.renderEmptyState(q)));let b=H(e,{global:!0});return $.createElement(Ei,null,$.createElement("div",{...z(b,p,M,A,F==null?void 0:F.collectionProps),ref:t,slot:e.slot||void 0,onScroll:e.onScroll,"data-drop-target":B||void 0,"data-empty":U||void 0,"data-focused":L||void 0,"data-focus-visible":T||void 0,"data-layout":e.layout||"stack","data-orientation":e.orientation||"vertical"},$.createElement(Fe,{values:[[Ve,e],[te,a],[de,{dragAndDropHooks:l,dragState:E,dropState:k}],[Xn,{elementType:"div"}],[ri,{render:er}],[Rn,{name:"ListBoxSection",render:_n}]]},$.createElement(D,{collection:s,scrollRef:t,persistedKeys:Gn(f,l,k),renderDropIndicator:oi(l,k)})),w,K))}function _n(i,e,t,l="react-aria-ListBoxSection"){let n=d.useContext(te),{dragAndDropHooks:r,dropState:o}=d.useContext(de),{CollectionBranch:a}=d.useContext(ze),[s,f]=St();var u;let{headingProps:c,groupProps:v}=dn({heading:f,"aria-label":(u=i["aria-label"])!==null&&u!==void 0?u:void 0}),h=J({defaultClassName:l,className:i.className,style:i.style,values:{}}),g=H(i,{global:!0});return delete g.id,$.createElement("section",{...z(g,h,v),ref:e},$.createElement(Yn.Provider,{value:{...c,ref:s}},$.createElement(a,{collection:n.collection,parent:t,renderDropIndicator:oi(r,o)})))}const ur=ml(Ci,function(e,t,l){let n=mt(t),r=d.useContext(te),{dragAndDropHooks:o,dragState:a,dropState:s}=d.useContext(de),{optionProps:f,labelProps:u,descriptionProps:c,...v}=un({key:l.key,"aria-label":e==null?void 0:e["aria-label"]},r,n),{hoverProps:h,isHovered:g}=ht({isDisabled:!v.allowsSelection&&!v.hasAction,onHoverStart:l.props.onHoverStart,onHoverChange:l.props.onHoverChange,onHoverEnd:l.props.onHoverEnd}),x=null;a&&o&&(x=o.useDraggableItem({key:l.key},a));let y=null;s&&o&&(y=o.useDroppableItem({target:{type:"item",key:l.key,dropPosition:"on"}},s,n));let m=a&&a.isDragging(l.key),S=J({...e,id:void 0,children:e.children,defaultClassName:"react-aria-ListBoxItem",values:{...v,isHovered:g,selectionMode:r.selectionManager.selectionMode,selectionBehavior:r.selectionManager.selectionBehavior,allowsDragging:!!a,isDragging:m,isDropTarget:y==null?void 0:y.isDropTarget}});d.useEffect(()=>{l.textValue},[l.textValue]);let D=e.href?"a":"div",C=H(e,{global:!0});return delete C.id,delete C.onClick,$.createElement(D,{...z(C,S,f,h,x==null?void 0:x.dragProps,y==null?void 0:y.dropProps),ref:n,"data-allows-dragging":!!a||void 0,"data-selected":v.isSelected||void 0,"data-disabled":v.isDisabled||void 0,"data-hovered":g||void 0,"data-focused":v.isFocused||void 0,"data-focus-visible":v.isFocusVisible||void 0,"data-pressed":v.isPressed||void 0,"data-dragging":m||void 0,"data-drop-target":(y==null?void 0:y.isDropTarget)||void 0,"data-selection-mode":r.selectionManager.selectionMode==="none"?void 0:r.selectionManager.selectionMode},$.createElement(Fe,{values:[[ue,{slots:{[Ki]:u,label:u,description:c}}]]},S.children))});function er(i,e){e=mt(e);let{dragAndDropHooks:t,dropState:l}=d.useContext(de),{dropIndicatorProps:n,isHidden:r,isDropTarget:o}=t.useDropIndicator(i,l,e);return r?null:$.createElement(lr,{...i,dropIndicatorProps:n,isDropTarget:o,ref:e})}function tr(i,e){let{dropIndicatorProps:t,isDropTarget:l,...n}=i,r=J({...n,defaultClassName:"react-aria-DropIndicator",values:{isDropTarget:l}});return $.createElement("div",{...t,...r,role:"option",ref:e,"data-drop-target":l||void 0})}const lr=d.forwardRef(tr);ml(wi,function(e,t,l){let n=d.useContext(te),{isLoading:r,onLoadMore:o,scrollOffset:a,...s}=e,f=d.useRef(null),u=d.useMemo(()=>({onLoadMore:o,collection:n==null?void 0:n.collection,sentinelRef:f,scrollOffset:a}),[o,a,n==null?void 0:n.collection]);Ti(u,f);let c=J({...s,id:void 0,children:l.rendered,defaultClassName:"react-aria-ListBoxLoadingIndicator",values:null}),v={tabIndex:-1};return $.createElement($.Fragment,null,$.createElement("div",{style:{position:"relative",width:0,height:0},inert:Vi(!0)},$.createElement("div",{"data-testid":"loadMoreSentinel",ref:f,style:{position:"absolute",height:1,width:1}})),r&&c.children&&$.createElement("div",{...z(H(e,{global:!0}),v),...c,role:"option",ref:t},c.children))});function ir(i){return i&&i.__esModule?i.default:i}const Oe=d.createContext(null),si=d.createContext(null),dr=d.forwardRef(function(e,t){[e,t]=se(e,t,Oe);let{children:l,isDisabled:n=!1,isInvalid:r=!1,isRequired:o=!1}=e,a=d.useMemo(()=>typeof l=="function"?l({isOpen:!1,isDisabled:n,isInvalid:r,isRequired:o,isFocused:!1,isFocusVisible:!1,defaultChildren:null}):l,[l,n,r,o]);return $.createElement($l,{content:a},s=>$.createElement(rr,{props:e,collection:s,selectRef:t}))}),nr=[xl,Pt,ue];function rr({props:i,selectRef:e,collection:t}){let{validationBehavior:l}=Dt(Wn)||{};var n,r;let o=(r=(n=i.validationBehavior)!==null&&n!==void 0?n:l)!==null&&r!==void 0?r:"native",a=Hn({...i,collection:t,children:void 0,validationBehavior:o}),{isFocusVisible:s,focusProps:f}=xt({within:!0}),u=d.useRef(null),[c,v]=St(!i["aria-label"]&&!i["aria-labelledby"]),{labelProps:h,triggerProps:g,valueProps:x,menuProps:y,descriptionProps:m,errorMessageProps:S,hiddenSelectProps:D,...C}=Ln({...ki(i),label:v,validationBehavior:o},a,u),[M,E]=d.useState(null),k=d.useCallback(()=>{u.current&&E(u.current.offsetWidth+"px")},[u]);Bi({ref:u,onResize:k});let F=d.useMemo(()=>({isOpen:a.isOpen,isFocused:a.isFocused,isFocusVisible:s,isDisabled:i.isDisabled||!1,isInvalid:C.isInvalid||!1,isRequired:i.isRequired||!1}),[a.isOpen,a.isFocused,s,i.isDisabled,C.isInvalid,i.isRequired]),B=J({...i,values:F,defaultClassName:"react-aria-Select"}),K=H(i,{global:!0});delete K.id;let I=d.useRef(null);return $.createElement(Fe,{values:[[Oe,i],[si,a],[ui,x],[xl,{...h,ref:c,elementType:"span"}],[Pt,{...g,ref:u,isPressed:a.isOpen,autoFocus:i.autoFocus}],[Mi,a],[Fi,{trigger:"Select",triggerRef:u,scrollRef:I,placement:"bottom start",style:{"--trigger-width":M},"aria-labelledby":y["aria-labelledby"],clearContexts:nr}],[Ve,{...y,ref:I}],[te,a],[ue,{slots:{description:m,errorMessage:S}}],[qn,C]]},$.createElement("div",{...z(K,B,f),ref:e,slot:i.slot||void 0,"data-focused":a.isFocused||void 0,"data-focus-visible":s||void 0,"data-open":a.isOpen||void 0,"data-disabled":i.isDisabled||void 0,"data-invalid":C.isInvalid||void 0,"data-required":i.isRequired||void 0},B.children,$.createElement(Vn,{...D,autoComplete:i.autoComplete})))}const ui=d.createContext(null),cr=d.forwardRef(function(e,t){var l,n;[e,t]=se(e,t,ui);let r=d.useContext(si),{placeholder:o}=Dt(Oe),a=r.selectedKey!=null?r.collection.getItem(r.selectedKey):null,s=a==null?void 0:a.props.children;typeof s=="function"&&(s=s({isHovered:!1,isPressed:!1,isSelected:!1,isFocused:!1,isFocusVisible:!1,isDisabled:!1,selectionMode:"single",selectionBehavior:"toggle"}));let f=bt(ir(ni),"react-aria-components");var u,c,v;let h=J({...e,defaultChildren:(u=s??o)!==null&&u!==void 0?u:f.format("selectPlaceholder"),defaultClassName:"react-aria-SelectValue",values:{selectedItem:(c=(l=r.selectedItem)===null||l===void 0?void 0:l.value)!==null&&c!==void 0?c:null,selectedText:(v=(n=r.selectedItem)===null||n===void 0?void 0:n.textValue)!==null&&v!==void 0?v:null,isPlaceholder:!a}}),g=H(e,{global:!0});return $.createElement("span",{ref:t,...g,...h,"data-placeholder":!a||void 0},$.createElement(ue.Provider,{value:void 0},h.children))});export{Li as $,te as A,Zi as B,gn as C,re as a,Wn as b,ar as c,xl as d,ue as e,qn as f,dr as g,cr as h,sr as i,ur as j,si as k,Ae as l,pn as m,Re as n,Ie as o,Qi as p,et as q,Ft as r,sn as s,wt as t,Wi as u,jn as v,fn as w,ot as x,Ve as y,$l as z};
