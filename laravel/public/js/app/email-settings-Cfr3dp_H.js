import{j as t,$ as B,b as ce,d as S,e as D,f as O,r as o,g as M,h as oe,i as de,k as ue,l as k,R as $,m as R,n as _,o as V,p as z,q as we,s as q,t as pe,u as xe,v as H,w as fe,x as ge,y as be,z as he,A as me,B as C,C as E,D as ve,E as $e,F as ye,G as A,H as G,I as U,J as je}from"./i18n-DJdGDBdH.js";import{c as v,u as Ce,C as K,a as Pe,b as W,d as Se,e as Z,t as F,f as Ne,g as Ee}from"./index-RNxW_Mg3.js";import{$ as ke,a as De,b as Fe,c as _e,d as Le,e as Me,f as Re,g as Ve,h as Be,i as Te,j as Ie,k as Oe}from"./Select-XdalPTad.js";import{$ as ze,a as qe,b as He,c as Ae}from"./Input-D2QI4Qb_.js";import{$ as Ge,a as Ue,u as Ke}from"./useDisclosure-C9DVYU_W.js";function Q(){return t.jsx("div",{className:"tw:fixed tw:inset-0 tw:bg-[#E4EBF2] tw:bg-opacity-75 tw:flex tw:items-center tw:justify-center tw:z-50",children:t.jsx("div",{className:"tw:animate-spin tw:rounded-full tw:h-12 tw:w-12 tw:border-b-2 tw:border-blue-600"})})}function We({children:a}){return t.jsx("h1",{className:"tw:text-display-xl! tw:font-bold tw:text-black! tw:mb-0! tw:text-start",children:a})}function J({children:a}){return t.jsx("div",{className:"tw:flex tw:flex-col tw:gap-6 tw:min-h-screen tw:antialiased tw:py-10 tw:px-5 tw:min-[576px]:max-w-[940px] tw:min-[768px]:max-w-[950px] tw:min-[992px]:max-w-[960px] tw:min-[1200px]:max-w-[1140px] tw:mx-auto",children:a})}function Ze(a,e,s){let{isDisabled:r=!1,isReadOnly:l=!1,value:n,name:d,form:i,children:c,"aria-label":w,"aria-labelledby":p,validationState:x="valid",isInvalid:b,onPressStart:u,onPressEnd:f,onPressChange:h,onPress:g,onPressUp:y,onClick:m}=a,P=N=>{N.stopPropagation(),e.setSelected(N.target.checked)},{pressProps:j,isPressed:se}=B({onPressStart:u,onPressEnd:f,onPressChange:h,onPress:g,onPressUp:y,onClick:m,isDisabled:r}),{pressProps:ae,isPressed:re}=B({onPressStart:u,onPressEnd:f,onPressChange:h,onPressUp:y,onClick:m,onPress(N){var L;g==null||g(N),e.toggle(),(L=s.current)===null||L===void 0||L.focus()},isDisabled:r||l}),{focusableProps:ne}=ce(a,s),ie=S(j,ne),le=D(a,{labelable:!0});return ke(s,e.defaultSelected,e.setSelected),{labelProps:S(ae,{onClick:N=>N.preventDefault()}),inputProps:S(le,{"aria-invalid":b||x==="invalid"||void 0,"aria-errormessage":a["aria-errormessage"],"aria-controls":a["aria-controls"],"aria-readonly":l||void 0,onChange:P,disabled:r,...n==null?{}:{value:n},name:d,form:i,type:"checkbox",...ie}),isSelected:e.isSelected,isPressed:se||re,isDisabled:r,isReadOnly:l,isInvalid:b||x==="invalid"}}function Qe(a={}){let{isReadOnly:e}=a,[s,r]=O(a.isSelected,a.defaultSelected||!1,a.onChange),[l]=o.useState(s);function n(c){e||r(c)}function d(){e||r(!s)}var i;return{isSelected:s,defaultSelected:(i=a.defaultSelected)!==null&&i!==void 0?i:l,setSelected:n,toggle:d}}function Je(a,e,s){let{isDisabled:r}=a,l=M(),n=M(),i=!oe()&&"onbeforematch"in document.body,c=o.useRef(null),w=o.useCallback(()=>{c.current=requestAnimationFrame(()=>{s.current&&s.current.setAttribute("hidden","until-found")}),de.flushSync(()=>{e.toggle()})},[s,e]);return De(s,"beforematch",i?w:null),ue(()=>{c.current&&cancelAnimationFrame(c.current),i&&s.current&&!r&&(e.isExpanded?s.current.removeAttribute("hidden"):s.current.setAttribute("hidden","until-found"))},[r,s,e.isExpanded,i]),o.useEffect(()=>()=>{c.current&&cancelAnimationFrame(c.current)},[]),{buttonProps:{id:l,"aria-expanded":e.isExpanded,"aria-controls":n,onPress:p=>{!r&&p.pointerType!=="keyboard"&&e.toggle()},isDisabled:r,onPressStart(p){p.pointerType==="keyboard"&&!r&&e.toggle()}},panelProps:{id:n,role:"group","aria-labelledby":l,"aria-hidden":!e.isExpanded,hidden:i?!0:!e.isExpanded}}}function Xe(a,e,s){let{labelProps:r,inputProps:l,isSelected:n,isPressed:d,isDisabled:i,isReadOnly:c}=Ze(a,e,s);return{labelProps:r,inputProps:{...l,role:"switch",checked:n},isSelected:n,isPressed:d,isDisabled:i,isReadOnly:c}}function Ye(a){let[e,s]=O(a.isExpanded,a.defaultExpanded||!1,a.onExpandedChange);const r=o.useCallback(()=>{s(!0)},[s]),l=o.useCallback(()=>{s(!1)},[s]),n=o.useCallback(()=>{s(!e)},[s,e]);return{isExpanded:e,setExpanded:s,expand:r,collapse:l,toggle:n}}const et=o.createContext(null),tt=o.createContext(null),st=o.createContext(null),X=o.createContext(null),at=o.forwardRef(function(e,s){[e,s]=k(e,s,tt);let r=o.useContext(et),{id:l,...n}=e,d=M();l||(l=d);let i=r?r.expandedKeys.has(l):e.isExpanded,c=Ye({...e,isExpanded:i,onExpandedChange(y){var m;r&&r.toggleKey(l),(m=e.onExpandedChange)===null||m===void 0||m.call(e,y)}}),w=$.useRef(null),p=e.isDisabled||(r==null?void 0:r.isDisabled)||!1,{buttonProps:x,panelProps:b}=Je({...e,isExpanded:i,isDisabled:p},c,w),{isFocusVisible:u,focusProps:f}=R({within:!0}),h=_({...e,id:void 0,defaultClassName:"react-aria-Disclosure",values:{isExpanded:c.isExpanded,isDisabled:p,isFocusVisibleWithin:u,state:c}}),g=D(n,{global:!0});return $.createElement(V,{values:[[z,{slots:{[we]:{},trigger:x}}],[X,{panelProps:b,panelRef:w}],[st,c]]},$.createElement("div",{...S(g,h,f),ref:s,"data-expanded":c.isExpanded||void 0,"data-disabled":p||void 0,"data-focus-visible-within":u||void 0},h.children))}),rt=o.forwardRef(function(e,s){let{role:r="group"}=e,{panelProps:l,panelRef:n}=o.useContext(X),{isFocusVisible:d,focusProps:i}=R({within:!0}),c=_({...e,defaultClassName:"react-aria-DisclosurePanel",values:{isFocusVisibleWithin:d}}),w=D(e,{global:!0});return $.createElement("div",{...S(w,c,l,i),ref:q(s,n),role:r,"data-focus-visible-within":d||void 0},$.createElement(V,{values:[[z,null]]},e.children))}),nt=o.forwardRef(function(e,s){[e,s]=k(e,s,pe);let{children:r,level:l=3,className:n,...d}=e,i=`h${l}`;return $.createElement(i,{...d,ref:s,className:n??"react-aria-Heading"},r)}),it=o.createContext(null),lt=o.forwardRef(function(e,s){let{inputRef:r=null,...l}=e;[e,s]=k(l,s,it);let n=xe(q(r,e.inputRef!==void 0?e.inputRef:null)),d=Qe(e),{labelProps:i,inputProps:c,isSelected:w,isDisabled:p,isReadOnly:x,isPressed:b}=Xe({...H(e),children:typeof e.children=="function"?!0:e.children},d,n),{isFocused:u,isFocusVisible:f,focusProps:h}=R(),g=e.isDisabled||e.isReadOnly,{hoverProps:y,isHovered:m}=fe({...e,isDisabled:g}),P=_({...e,defaultClassName:"react-aria-Switch",values:{isSelected:w,isPressed:b,isHovered:m,isFocused:u,isFocusVisible:f,isDisabled:p,isReadOnly:x,state:d}}),j=D(e,{global:!0});return delete j.id,delete j.onClick,$.createElement("label",{...S(j,i,y,P),ref:s,slot:e.slot||void 0,"data-selected":w||void 0,"data-pressed":b||void 0,"data-hovered":m||void 0,"data-focused":u||void 0,"data-focus-visible":f||void 0,"data-disabled":p||void 0,"data-readonly":x||void 0},$.createElement(ge,{elementType:"span"},$.createElement("input",{...S(c,h),ref:n})),P.children)}),ct=o.createContext({}),ot=o.createContext(null),dt=be(function(e,s){[e,s]=k(e,s,ot);let{validationBehavior:r}=he(Fe)||{};var l,n;let d=(n=(l=e.validationBehavior)!==null&&l!==void 0?l:r)!==null&&n!==void 0?n:"native",i=o.useRef(null);[e,i]=k(e,i,_e);let[c,w]=me(!e["aria-label"]&&!e["aria-labelledby"]),[p,x]=o.useState("input"),{labelProps:b,inputProps:u,descriptionProps:f,errorMessageProps:h,...g}=ze({...H(e),inputElementType:p,label:w,validationBehavior:d},i),y=o.useCallback(j=>{i.current=j,j&&x(j instanceof HTMLTextAreaElement?"textarea":"input")},[i]),m=_({...e,values:{isDisabled:e.isDisabled||!1,isInvalid:g.isInvalid,isReadOnly:e.isReadOnly||!1,isRequired:e.isRequired||!1},defaultClassName:"react-aria-TextField"}),P=D(e,{global:!0});return delete P.id,$.createElement("div",{...P,...m,ref:s,slot:e.slot||void 0,"data-disabled":e.isDisabled||void 0,"data-invalid":g.isInvalid||void 0,"data-readonly":e.isReadOnly||void 0,"data-required":e.isRequired||void 0},$.createElement(V,{values:[[Le,{...b,ref:c}],[qe,{...u,ref:y}],[ct,{...u,ref:y}],[He,{role:"presentation",isInvalid:g.isInvalid,isDisabled:e.isDisabled||!1}],[Me,{slots:{description:f,errorMessage:h}}],[Re,g]]},m.children))});function ut(){const{t:a}=C();return t.jsx("div",{className:"tw:w-full tw:h-full tw:bg-white tw:flex tw:items-center tw:justify-center tw:min-h-[400px]",children:t.jsx("div",{className:"tw:text-center",children:t.jsx("p",{className:"tw:text-gray-500 tw:text-lg",children:a("core:no-active-plugins")})})})}const wt=({label:a="Toggle",value:e,onChange:s,disabled:r=!1,className:l=""})=>t.jsxs(lt,{className:v("tw:group tw:relative tw:flex! tw:items-center tw:gap-2 tw:font-normal tw:text-annotation tw:text-primary-black tw:leading-normal tw:disabled:cursor-not-allowed","tw:rounded-sm tw:px-4 tw:py-3",l),isSelected:e,onChange:s,isDisabled:r,children:[t.jsx("div",{className:"tw:flex tw:h-[22px] tw:w-[40px] tw:rounded-full tw:bg-[#A8ACC2] tw:bg-clip-padding tw:p-1 tw:outline-hidden tw:ring-black tw:transition tw:duration-200 tw:ease-in-out tw:group-focus-visible:ring-2 tw:group-disabled:bg-[#A8ACC2]/30 tw:group-selected:bg-[#80C342] tw:group-disabled:group-selected:bg-[#DBF4C7] tw:rtl:flex-row-reverse",children:t.jsx("span",{className:"tw:h-[14px] tw:w-[14px] tw:translate-x-0 tw:transform tw:rounded-full tw:bg-white tw:transition tw:duration-200 tw:ease-in-out tw:group-selected:translate-x-[18px]"})}),a]});function Y({children:a,className:e="",autoFocus:s,isDisabled:r,name:l,invalid:n=!1,success:d=!1}){return t.jsx(dt,{autoFocus:s,isDisabled:r,name:l,className:v("tw:group tw:flex tw:w-full tw:flex-col tw:gap-2",e),isInvalid:n,"data-success":d,children:a})}function ee({children:a,className:e="",size:s="md"}){const r={sm:"tw:min-h-[34px] tw:max-h-[34px]",md:"tw:min-h-[38px] tw:max-h-[38px]",lg:"tw:min-h-[42px] tw:max-h-[42px]"};return t.jsx("div",{className:v("tw:group/input-group tw:relative tw:flex tw:w-full tw:items-stretch tw:overflow-hidden tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1","tw:border-[#E4EBF2]","tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1","tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2","tw:[&>input:not(:first-child)]:ps-2 tw:[&>input:not(:last-child)]:pe-2","tw:[&>textarea:not(:first-child)]:ps-2 tw:[&>textarea:not(:last-child)]:pe-2","tw:hover:ring-2 tw:hover:ring-[#D8E8FD]","tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50","tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50","tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1","tw:group-disabled:hover:ring-0","tw:[&:has([data-select])]:pe-[34px]",r[s],e),"data-group":"true",children:a})}function pt({className:a="",onChange:e,placeholder:s,type:r,value:l,disabled:n,name:d,autoFocus:i,size:c="md"}){const w={sm:"tw:min-h-[34px]",md:"tw:min-h-[38px]",lg:"tw:min-h-[42px]"};return t.jsx(Ae,{className:v("tw:flex-1 tw:border-0 tw:bg-transparent tw:px-4 tw:text-base tw:text-primary-black tw:leading-normal tw:outline-none tw:placeholder:text-light-gray-4 tw:group-disabled:cursor-not-allowed","tw:disabled:bg-transparent tw:disabled:text-light-gray-4","tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1","tw:border-[#E4EBF2]","tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1","tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2","tw:hover:ring-2 tw:hover:ring-[#D8E8FD]","tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50","tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50","tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1","tw:group-disabled:hover:ring-0","tw:group-data-[group=true]/input-group:min-h-full! tw:group-data-[group=true]/input-group:rounded-none tw:group-data-[group=true]/input-group:border-none tw:group-data-[group=true]/input-group:ring-0 tw:group-data-[group=true]/input-group:group-data-[success=true]:hover:ring-0 tw:group-data-[group=true]/input-group:hover:ring-0",w[c],a),onChange:e?p=>e(p.target.value):void 0,placeholder:s,type:r,value:l,disabled:n,name:d,autoFocus:i})}function xt({children:a,className:e=""}){return t.jsx("span",{className:v("tw:flex tw:min-h-full tw:items-center tw:ps-4 tw:pe-0 tw:text-light-gray-4",e),children:a})}const ft=()=>t.jsx("svg",{role:"img",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-label":"search icon",children:t.jsx("path",{d:"M9.5 3C11.2239 3 12.8772 3.68482 14.0962 4.90381C15.3152 6.12279 16 7.77609 16 9.5C16 11.11 15.41 12.59 14.44 13.73L14.71 14H15.5L20.5 19L19 20.5L14 15.5V14.71L13.73 14.44C12.59 15.41 11.11 16 9.5 16C7.77609 16 6.12279 15.3152 4.90381 14.0962C3.68482 12.8772 3 11.2239 3 9.5C3 7.77609 3.68482 6.12279 4.90381 4.90381C6.12279 3.68482 7.77609 3 9.5 3ZM9.5 5C7 5 5 7 5 9.5C5 12 7 14 9.5 14C12 14 14 12 14 9.5C14 7 12 5 9.5 5Z",fill:"currentColor"})});function gt({onChange:a,value:e}){const{t:s}=C();return t.jsx(Y,{children:t.jsxs(ee,{size:"md",children:[t.jsx(xt,{children:t.jsx(ft,{})}),t.jsx(pt,{className:"tw:text-[14px]!",onChange:a,placeholder:s("core:search-by-email-group"),value:e})]})})}const bt=()=>o.useContext(Oe);function ht({children:a,selectedKey:e,onSelectionChange:s,isDisabled:r=!1,name:l,autoFocus:n,onOpenChange:d,className:i="",placeholder:c}){return t.jsx(Ve,{selectedKey:e,onSelectionChange:w=>s==null?void 0:s(w),isDisabled:r,name:l,autoFocus:n,onOpenChange:d,className:v("tw:peer tw:relative tw:flex tw:flex-1 tw:group-data-[group=true]/input-group:static",i),"data-select":"true",placeholder:c,children:a})}function mt({selectedKey:a,isOpen:e=!1,className:s=""}){const r=bt();return console.log(r),t.jsxs(E,{className:v("tw:min-h-[38px] tw:w-full tw:flex-1 tw:border-0 tw:bg-transparent tw:px-4 tw:text-base tw:text-primary-black tw:leading-normal tw:outline-none tw:group-disabled:cursor-not-allowed","tw:disabled:bg-transparent tw:disabled:text-light-gray-4","tw:relative tw:flex tw:items-center","tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1 tw:group-data-[pressed=true]:border-info-1","tw:border-[#E4EBF2]","tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1","tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2","tw:hover:ring-2 tw:hover:ring-[#D8E8FD]","tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50","tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50","tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1","tw:group-disabled:hover:ring-0","tw:group-data-[group=true]/input-group:static tw:group-data-[group=true]/input-group:rounded-none tw:group-data-[group=true]/input-group:border-none tw:group-data-[group=true]/input-group:ring-0 tw:group-data-[group=true]/input-group:group-data-[success=true]:hover:ring-0 tw:group-data-[group=true]/input-group:hover:ring-0",s),children:[t.jsx(Be,{className:v("tw:flex-1 tw:truncate tw:text-start",!a&&"tw:text-light-gray-4")}),t.jsx("span",{className:v("tw:-translate-y-1/2 tw:absolute tw:end-2 tw:top-1/2 tw:h-6 tw:w-6 tw:text-light-gray-4 tw:transition-transform tw:duration-200",e&&"tw:rotate-180"),children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:t.jsx("path",{d:"M8 10L12 14L16 10L17.4 11.4L12 16.8L6.6 11.4L8 10Z",fill:"currentColor"})})})]})}function vt({children:a,className:e=""}){return t.jsx(ve,{offset:2,crossOffset:-1,placement:"bottom start",className:v("tw:flex tw:w-[calc(var(--trigger-width)+36px)] tw:flex-col tw:rounded-xs tw:border tw:bg-white tw:text-base tw:shadow-lg tw:ring-1 tw:ring-black/5","tw:entering:fade-in tw:entering:zoom-in-95 tw:entering:animate-in","tw:exiting:fade-out tw:exiting:zoom-out-95 tw:exiting:animate-out","tw:data-[placement=top]:slide-in-from-bottom-2","tw:data-[placement=bottom]:slide-in-from-top-2","tw:border-[1px] tw:border-info-1","tw:rtl:translate-x-[-34px]",e),children:t.jsx(Te,{className:"tw:max-h-60 tw:flex-1 tw:overflow-auto tw:p-1 tw:outline-none",selectionBehavior:"toggle",selectionMode:"multiple",children:a})})}function T({item:a,...e}){return t.jsx(Ie,{...e,id:a.id,textValue:a.textValue||a.name,isDisabled:a.isDisabled,className:v("tw:group tw:flex tw:cursor-default tw:select-none tw:items-center tw:gap-2 tw:rounded-xs tw:px-3 tw:py-2 tw:text-primary-black tw:outline-none","tw:hover:bg-light-gray-1 tw:focus:text-info-1","tw:disabled:cursor-not-allowed tw:disabled:opacity-50","tw:data-[selected=true]:text-info-1"),children:t.jsxs("div",{className:"tw:flex tw:flex-1 tw:flex-col",children:[t.jsx("span",{className:"tw:truncate tw:font-normal",children:a.name}),a.subLabel&&t.jsx("span",{className:"tw:truncate tw:text-light-gray-4 tw:text-xs tw:group-focus:text-white/80 tw:group-data-[selected=true]:text-white/80",children:a.subLabel})]})})}function $t({onChange:a,value:e}){const{t:s}=C();return t.jsx(Y,{children:t.jsx(ee,{size:"md",children:t.jsxs(ht,{onSelectionChange:a,selectedKey:e,placeholder:s("core:filter-by-status"),children:[t.jsx(mt,{className:"tw:text-[14px]!",selectedKey:e}),t.jsxs(vt,{children:[t.jsx(T,{item:{id:"1",name:s("core:subscribed")}}),t.jsx(T,{item:{id:"0",name:s("core:unsubscribed")}})]})]})})})}function yt({searchTerm:a,status:e,onSearch:s,onClear:r}){const{t:l}=C(),[n,d]=o.useState(a),[i,c]=o.useState(e),[,w]=o.useTransition();o.useEffect(()=>{d(a),c(e)},[a,e]);const p=()=>{w(()=>{console.log("searchTerm",n),console.log("status",i),s==null||s(n,i)})},x=()=>{w(()=>{d(""),c(""),r==null||r()})};return t.jsxs(at,{defaultExpanded:!0,children:[t.jsxs("div",{className:"tw:flex tw:justify-between tw:items-center tw:bg-[#F6F9FC] tw:text-[#202124] tw:font-medium tw:text-[14px]! tw:leading-4! tw:px-4 tw:py-[9px] tw:w-full",children:[l("core:search-and-filter"),t.jsxs("div",{className:"tw:flex tw:items-center tw:gap-[14px] tw:text-[#75799D]",children:[t.jsx("div",{className:"tw:w-[2px] tw:h-5 tw:bg-[#FFFFFF]"}),t.jsxs(E,{slot:"trigger",className:"tw:flex tw:gap-[5px] tw:items-center",children:[t.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M14.325 4.50831L13.1417 3.33331L10.5 5.97498L7.85838 3.33331L6.67505 4.50831L10.5 8.33331M6.67505 15.4916L7.85838 16.6666L10.5 14.025L13.1417 16.6666L14.3167 15.4916L10.5 11.6666L6.67505 15.4916Z",fill:"#75799D"})}),l("core:hide")]})]})]}),t.jsx(rt,{children:t.jsxs("div",{className:"tw:bg-[#FFFFFF] tw:px-4 tw:py-5 tw:flex tw:justify-between tw:items-center tw:flex-wrap tw:gap-5",children:[t.jsxs("div",{className:"tw:flex tw:gap-4 tw:flex-wrap",children:[t.jsx("div",{className:"tw:flex-1 tw:w-72",children:t.jsx(gt,{onChange:d,value:n})}),t.jsx("div",{className:"tw:flex-1 tw:w-72",children:t.jsx($t,{onChange:c,value:i})})]}),t.jsxs("div",{className:"tw:flex",children:[t.jsx(E,{className:"tw:text-[#75799D] tw:font-medium tw:text-[14px]! tw:leading-[18px]! tw:px-4 tw:py-[10px]",onPress:x,children:l("core:clear-all")}),t.jsx(E,{className:"tw:bg-[#E4EBF2] tw:text-[#202124] tw:rounded-[2px] tw:font-medium tw:text-[14px]! tw:leading-[18px]! tw:px-4 tw:py-[10px]",onPress:p,children:l("core:search")})]})]})})]})}function te({subscriptionsData:a,handleToggleSubscription:e,searchTerm:s,status:r,onSearch:l,onClear:n,isAtleaseOneActivePlugin:d}){const{t:i}=C(),[c,w]=o.useState("asc"),p=()=>{w(c==="asc"?"desc":"asc")},x={payslip:i("core:payslip"),leave_application:i("core:leave_application"),request:i("core:request")},b=o.useMemo(()=>{if(c==="asc")return a.sort((u,f)=>x[u.entity_key].localeCompare(x[f.entity_key]));if(c==="desc")return a.sort((u,f)=>x[f.entity_key].localeCompare(x[u.entity_key]))},[a,c]);return d?t.jsxs("div",{className:"tw:flex tw:flex-col tw:gap-6",children:[t.jsx(yt,{searchTerm:s,status:r,onSearch:(u,f)=>{l(u,f)},onClear:()=>{n()}}),t.jsx("div",{className:"tw:overflow-auto",children:t.jsxs("table",{className:"tw:min-w-full tw:divide-y tw:divide-gray-200",children:[t.jsx("thead",{children:t.jsxs("tr",{className:"tw:border-b! tw:border-[#DBE3EB]!",children:[t.jsx("th",{scope:"col",className:"tw:px-[16px] tw:py-[10px] tw:text-start tw:font-normal tw:text-[14px] tw:leading-4!",children:i("core:email-groups")}),t.jsx("th",{scope:"col",className:"tw:px-[16px] tw:py-[10px] tw:text-start tw:font-normal tw:text-[14px] tw:leading-4!",children:i("core:unsubscribe-date")}),t.jsx("th",{scope:"col",className:"tw:px-[16px] tw:py-[10px] tw:text-start tw:font-normal tw:text-[14px] tw:leading-4!",children:i("core:status")}),t.jsx("th",{scope:"col",className:"tw:w-[45px] tw:px-[16px] tw:py-[7.5px] tw:text-start tw:font-normal tw:text-[14px] tw:tracking-wider tw:leading-4!",children:t.jsxs(E,{slot:"trigger",className:"tw:flex tw:gap-[5px] tw:items-center",onClick:p,children:[i("core:sort"),t.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("g",{"clip-path":"url(#clip0_167_2147)",children:t.jsx("path",{d:"M15.0367 17.4848L11.8017 14.2498H14.2277V6.16078H11.8017L15.0367 2.92578L18.2717 6.16078H15.8457V14.2488H18.2717M2.0957 15.8668V14.2498H10.1837V15.8678M2.0957 11.0138V9.39678H7.7577V11.0148M2.0957 6.16078V4.54378H5.3307V6.16078H2.0957Z",fill:"#75799D"})}),t.jsx("defs",{children:t.jsx("clipPath",{id:"clip0_167_2147",children:t.jsx("rect",{width:"19",height:"19",fill:"white",transform:"translate(0.5 0.5)"})})})]})]})})]})}),t.jsx("tbody",{className:"tw:bg-white tw:divide-y tw:divide-gray-200",children:b.length===0?t.jsx("tr",{children:t.jsx("td",{colSpan:4,className:"tw:text-center tw:text-sm tw:text-gray-500 tw:py-6",children:i("core:no-email-groups-match-your-search")})}):b.map(u=>t.jsxs("tr",{children:[t.jsx("td",{className:"tw:px-[16px] tw:py-[8px] tw:whitespace-nowrap tw:text-[14px] tw:font-medium tw:text-[#202124] tw:text-start",children:x[u.entity_key]}),t.jsx("td",{className:"tw:px-[16px] tw:py-[8px] tw:whitespace-nowrap tw:text-sm tw:text-gray-600",children:u.status==="0"?u.modified:"-"}),t.jsx("td",{className:"tw:flex tw:whitespace-nowrap tw:text-sm tw:py-[9px] tw:min-w-[170px]",children:t.jsx(wt,{label:u.status==="1"?i("core:subscribed"):i("core:unsubscribed"),value:u.status==="1",onChange:()=>e({entity:u})})}),t.jsx("td",{className:"tw:px-[16px] tw:py-[8px] tw:whitespace-nowrap tw:text-left tw:text-sm tw:font-medium tw:relative"})]},u.entity_key))})]})})]}):t.jsx(ut,{})}function jt({publicRouteParams:a,handleToggleSubscription:e}){const{t:s}=C(),{data:r}=Ce(a.token),l={payslip:s("core:payslip"),leave_application:s("core:leave_application"),request:s("core:request")},[n,d]=o.useState(""),[i,c]=o.useState(""),w=o.useMemo(()=>r.entities.filter(f=>(f.entity_key.toLowerCase().includes(n.toLowerCase())||l[f.entity_key].toLowerCase().includes(n.toLowerCase()))&&(i===""||f.status===i)),[r.entities,n,i]),p=(u,f)=>{d(u),c(f)},x=()=>{d(""),c("")},b=r.entities.length>0;return t.jsx(te,{subscriptionsData:w,handleToggleSubscription:e,searchTerm:n,status:i,onSearch:p,onClear:x,isAtleaseOneActivePlugin:b})}const I=({children:a,className:e,...s})=>t.jsx(E,{...s,className:v("tw:px-3 tw:py-2 tw:rounded tw:text-white tw:text-sm tw:font-medium tw:focus:outline-none",e),children:a}),Ct=({isOpen:a,onClose:e,onUnsubscribe:s,email:r,entity:l})=>{const{t:n}=C(),d=p=>{p||e()},i=()=>{s(),e()},c={payslip:n("core:payslips"),leave_application:n("core:leave_application"),request:n("core:request")},w={payslip:n("core:system-will-stop-sending-emails-for-payslips-to-email",{email:r}),leave_application:n("core:system-will-stop-sending-emails-for-leave-applications-to-email",{email:r}),request:n("core:system-will-stop-sending-emails-for-requests-to-email",{email:r})};return a?t.jsx($e,{isOpen:a,onOpenChange:d,children:t.jsx(Ge,{className:({isEntering:p,isExiting:x})=>`
          tw:fixed tw:inset-0 tw:z-10 tw:overflow-y-auto tw:bg-black/25 tw:flex tw:min-h-full tw:items-center tw:justify-center tw:p-4 tw:text-center tw:backdrop-blur-xs
          ${p?"tw:animate-in tw:fade-in tw:duration-300 tw:ease-out":""}
          ${x?"tw:animate-out tw:fade-out tw:duration-200 tw:ease-in":""}
        `,children:t.jsx(Ue,{className:({isEntering:p,isExiting:x})=>`
            tw:w-full tw:max-w-md tw:overflow-hidden tw:rounded-2xl tw:bg-white tw:p-6 tw:text-left tw:align-middle tw:shadow-xl
            ${p?"tw:animate-in tw:zoom-in-95 tw:ease-out tw:duration-300":""}
            ${x?"tw:animate-out tw:zoom-out-95 tw:ease-in tw:duration-200":""}
          `,children:t.jsxs(ye,{role:"alertdialog",className:"tw:outline-hidden tw:relative",children:[t.jsx(nt,{slot:"title",className:"tw:text-xxl tw:font-semibold tw:leading-6 tw:my-0 tw:text-slate-700 tw:text-start",children:n("core:unsubscribe-from-type",{type:c[l]})}),t.jsx("p",{className:"tw:mt-3 tw:text-slate-500 tw:text-start",children:w[l]}),t.jsxs("div",{className:"tw:mt-6 tw:flex tw:justify-end tw:gap-2",children:[t.jsx(I,{className:"tw:bg-[#e4ebf2] tw:text-black",onPress:e,children:n("core:cancel")}),t.jsx(I,{className:"tw:bg-[#eb2121] tw:text-white",onPress:i,children:n("core:unsubscribe")})]})]})})})}):null},Pt="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Generator:%20Adobe%20Illustrator%2028.0.0,%20SVG%20Export%20Plug-In%20.%20SVG%20Version:%206.00%20Build%200)%20--%3e%3csvg%20version='1.1'%20id='Layer_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20x='0px'%20y='0px'%20viewBox='0%200%201920%20701.2'%20style='enable-background:new%200%200%201920%20701.2;'%20xml:space='preserve'%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23006BFF;}%20.st1{fill:%231D2D3E;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M147.7,191.2h-34.9v118.4h36.6c65.5,0,109,15.5,109,78.7v1.1c0,63.8-43.5,75.8-109,75.8h-36.6h-8H-4.7v121.3%20h150.2c142.3,0,233.3-84.7,233.3-198.8v-1.1C378.7,272.6,288.9,191.2,147.7,191.2'/%3e%3cpath%20class='st1'%20d='M632.6,453.4v-16c-12.2-5.1-29.5-9-47.4-9c-35.2,0-55.7,17.3-55.7,42.9v1.3c0,23.7,17.9,37.8,42.9,37.8%20C607.7,510.4,632.6,487.9,632.6,453.4%20M412.4,479.6v-1.3c0-71.7,54.4-108.9,135.7-108.9c32,0,63.4,6.4,83.2,13.4v-5.8%20c0-35.9-22.4-56.3-69.2-56.3c-36.5,0-64.7,7-95.4,19.2l-25-85.8c39.1-16,80.7-26.9,140.2-26.9c62.1,0,104.4,14.7,131.9,42.3%20c26.2,25.6,37.1,61.5,37.1,110.1v199.1H630.7V543c-23.7,26.2-56.3,43.5-101.8,43.5C463,586.5,412.4,548.8,412.4,479.6'/%3e%3cpath%20class='st1'%20d='M820.6,337.5h-40.3V244h40.3v-9.6c0-39.7,9-69.1,29.4-89.6c21.1-21.1,49.3-30.1,90.3-30.1%20c37.1,0,60.2,3.8,79.4,9.6v85.8c-14.1-4.5-28.2-7-45.5-7c-22.4,0-34.6,10.2-34.6,32.7v8.3h79.4v93.5h-77.5v249.1h-121V337.5z'/%3e%3cpath%20class='st1'%20d='M1244.3,482.8c16,0,31.4-4.5,46.1-11.5v95.4c-21.7,12.2-50.6,19.8-84.5,19.8c-73,0-116.5-32-116.5-114.6V337.5%20h-41l-0.3-93.5h41l0.2-73.6l121.7-25l-0.3,98.6l81,0v93.5l-80.7,0v110.8C1211,472.6,1221.9,482.8,1244.3,482.8z'/%3e%3cpath%20class='st1'%20d='M1320.9,244h121.6v65.9c19.8-47.4,51.9-78.1,109.5-75.6v128.1h-10.2c-63.4,0-99.3,36.5-99.3,116.5v107.6%20h-121.6V244z'/%3e%3cpath%20class='st1'%20d='M1801.6,453.4v-16c-12.2-5.1-29.5-9-47.4-9c-35.2,0-55.7,17.3-55.7,42.9v1.3c0,23.7,17.9,37.8,42.9,37.8%20C1776.6,510.4,1801.6,487.9,1801.6,453.4%20M1581.3,479.6v-1.3c0-71.7,54.4-108.9,135.7-108.9c32,0,63.4,6.4,83.2,13.4v-5.8%20c0-35.9-22.4-56.3-69.2-56.3c-36.5,0-64.7,7-95.4,19.2l-25-85.8c39.1-16,80.7-26.9,140.2-26.9c62.1,0,104.4,14.7,131.9,42.3%20c26.2,25.6,37.1,61.5,37.1,110.1v199.1h-120.4V543c-23.7,26.2-56.3,43.5-101.8,43.5C1631.9,586.5,1581.3,548.8,1581.3,479.6'/%3e%3c/g%3e%3c/svg%3e",St="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Generator:%20Adobe%20Illustrator%2028.0.0,%20SVG%20Export%20Plug-In%20.%20SVG%20Version:%206.00%20Build%200)%20--%3e%3csvg%20version='1.1'%20id='Layer_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20x='0px'%20y='0px'%20viewBox='0%200%201920%20701.2'%20style='enable-background:new%200%200%201920%20701.2;'%20xml:space='preserve'%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23182D3F;}%20.st1{fill:%23006BFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M1475.7,287.2c-8.5-19-21.4-36.9-38.6-53.7c-16.9-16.7-36.9-29.9-58.9-38.7c-21.4-8.5-45.3-12.8-71.9-12.8%20c-51.9,0-95.5,17.2-130.7,51.5c-34.3,33.3-51.4,73.4-51.4,120.2c0,17,2.5,34,7.4,50.3h-116.9V276.6l-122.5,25.2v39.1%20c0,5-0.1,9.5-0.2,13.8v49.4H737.4l0.1-0.1H538.4V227.6l-122.5,25.5l0.9,272.4c-4.4,31.1-24.6,50.2-60.6,57.5l52.1,88.7%20c81.8-24.5,124.1-73.2,127-146.1h416.9l62.3-60.5v60.5h291.8c24.4,0.3,48.6-4.3,71.1-13.6c22.1-8.5,42.2-21.3,59.1-37.8%20c15.9-15.8,29.1-34.1,38.9-54.3c8.7-19.5,13-41.6,13-66.2C1488.5,328.1,1484.2,305.9,1475.7,287.2z%20M1343.2,392.1%20c-9.1,8.4-21.4,12.6-37,12.6c-15.4,0-27.7-4.2-37-12.6c-9.6-8.3-14.4-21.1-14.4-38.5c0-17.2,4.8-30.1,14.4-38.7%20c9.5-8.6,21.8-12.9,37-12.9c15.4,0,27.7,4.2,37,12.9c9.3,8.4,13.9,21.3,13.9,38.7C1357.2,371,1352.5,383.8,1343.2,392.1z'/%3e%3cpath%20class='st0'%20d='M184.2,181.9c-95,0-172,76.9-172,172c0,95,77,171.9,172,171.9c95,0,172-76.9,172-171.9%20C356.2,258.9,279.1,181.9,184.2,181.9z%20M184.2,404.2c-27.8,0-50.4-22.6-50.4-50.4c0-27.8,22.6-50.4,50.4-50.4%20c27.8,0,50.4,22.6,50.4,50.4C234.5,381.7,212,404.2,184.2,404.2z'/%3e%3cpath%20class='st1'%20d='M1671.2,129h-35.1v118.8h36.8c65.7,0,109.4,15.5,109.4,79v1.1c0,64-43.6,76-109.4,76h-36.8h-8h-110v121.8H1669%20c142.8,0,234.1-85,234.1-199.5v-1.1C1903.1,210.6,1812.9,129,1671.2,129'/%3e%3cpath%20class='st0'%20d='M1022.2,108.9c-11.4-11.7-27.2-18.2-43.5-18c-16.3-0.3-32,6.2-43.2,18c-11.7,11.3-18.2,27-18,43.2%20c-0.3,16.4,6.2,32.2,18,43.5c11.3,11.8,26.9,18.3,43.2,18c16.4,0.3,32.1-6.2,43.5-18c11.6-11.5,18-27.2,17.7-43.5%20C1040.1,135.9,1033.7,120.2,1022.2,108.9z'/%3e%3cpath%20class='st0'%20d='M1349.5,47.5c-11.4-11.7-27.2-18.2-43.5-18c-16.3-0.3-32,6.2-43.2,18c-11.7,11.3-18.2,27-18,43.2%20c-0.3,16.4,6.2,32.2,18,43.5c11.3,11.8,26.9,18.3,43.2,18c16.4,0.3,32.1-6.2,43.5-18c11.6-11.5,18-27.2,17.7-43.5%20C1367.5,74.5,1361.1,58.9,1349.5,47.5z'/%3e%3cpath%20class='st0'%20d='M892.5,108.9c-11.4-11.7-27.2-18.2-43.5-18c-16.3-0.3-32,6.2-43.2,18c-11.7,11.3-18.2,27-18,43.2%20c-0.3,16.4,6.2,32.2,18,43.5c11.3,11.8,26.9,18.3,43.2,18c16.4,0.3,32.1-6.2,43.5-18c11.6-11.5,18-27.2,17.7-43.5%20C910.4,135.9,904,120.2,892.5,108.9z'/%3e%3cpath%20class='st0'%20d='M292.5,48.8c-11.4-11.7-27.2-18.2-43.5-18c-16.3-0.3-32,6.2-43.2,18c-11.7,11.3-18.2,27-18,43.2%20c-0.3,16.4,6.2,32.2,18,43.5c11.3,11.8,26.9,18.3,43.2,18c16.4,0.3,32.1-6.2,43.5-18c11.6-11.5,18-27.2,17.7-43.5%20C310.5,75.8,304.1,60.2,292.5,48.8z'/%3e%3cpath%20class='st0'%20d='M162.8,48.8c-11.4-11.7-27.2-18.2-43.5-18c-16.3-0.3-32,6.2-43.2,18c-11.7,11.3-18.2,27-18,43.2%20c-0.3,16.4,6.2,32.2,18,43.5c11.3,11.8,26.9,18.3,43.2,18c16.4,0.3,32.1-6.2,43.5-18c11.6-11.5,18-27.2,17.7-43.5%20C180.8,75.8,174.4,60.2,162.8,48.8z'/%3e%3c/g%3e%3c/svg%3e";function Nt(){return t.jsx("div",{className:"tw:flex tw:justify-between tw:items-center tw:gap-4 tw:flex-wrap tw:h-[66px] tw:bg-[#FFFFFF] tw:px-4 tw:py-2",children:t.jsx("div",{className:"tw:flex tw:flex-col tw:gap-6 tw:antialiased tw:min-[576px]:max-w-[940px] tw:min-[768px]:max-w-[950px] tw:min-[992px]:max-w-[960px] tw:min-[1200px]:max-w-[1140px]"})})}function Et(){const a=A(),{mutate:e}=Pe(),{data:s}=W(),{data:r}=Se(),{t:l}=C();G({lang:s==null?void 0:s.language});const n={payslip:l("core:payslip"),leave_application:l("core:leave_application"),request:l("core:request")},d=({entity:h})=>{const g=h.status!=="1";e({status:g,entity:h.entity_key},{onSuccess:()=>{a.invalidateQueries({queryKey:["subscriptions"]}),g?F.success(l("core:success"),{description:l("core:success-resubscribed",{entity:n[h.entity_key]})}):F.success(l("core:success"),{description:l("core:success-unsubscribed",{entity:n[h.entity_key]})})}})},[i,c]=o.useState(""),[w,p]=o.useState(""),x=o.useMemo(()=>r.entities.filter(g=>(g.entity_key.toLowerCase().includes(i.toLowerCase())||n[g.entity_key].toLowerCase().includes(i.toLowerCase()))&&(w===""||g.status===w)),[r.entities,i,w]),b=(h,g)=>{c(h),p(g)},u=()=>{c(""),p("")},f=r.entities.length>0;return t.jsxs("div",{children:[t.jsx(Nt,{}),t.jsx(Z,{children:t.jsx(J,{children:t.jsx(te,{subscriptionsData:x,handleToggleSubscription:d,searchTerm:i,status:w,onSearch:b,onClear:u,isAtleaseOneActivePlugin:f})})})]})}function kt({publicRouteParams:a}){const{t:e}=C(),{currentLang:s}=U(),{isOpen:r,onOpen:l,onClose:n}=Ke(),d=A(),{mutate:i}=Ne(),{data:c}=W(),{data:w}=Ee(a.token);G({lang:c==null?void 0:c.language});const p={payslip:e("core:payslip"),leave_application:e("core:leave_application"),request:e("core:request")},x=({entity:u})=>{const f=u.status!=="1";i({status:f,entity:u.entity_key,token:a.token},{onSuccess:()=>{d.invalidateQueries({queryKey:["subscriptions"]}),f?F.success(e("core:success"),{description:e("core:success-resubscribed",{entity:p[u.entity_key]})}):F.success(e("core:success"),{description:e("core:success-unsubscribed",{entity:p[u.entity_key]})})}})};o.useEffect(()=>{a&&l()},[]);const b=()=>{x({entity:{entity_key:a.entity_key,status:a.status==="1"?"0":"1"}})};return t.jsxs(t.Fragment,{children:[t.jsx(Z,{children:t.jsxs(J,{children:[t.jsxs("div",{className:"tw:flex tw:justify-between tw:items-center tw:gap-4 tw:flex-wrap",children:[t.jsx("a",{href:"/",className:"tw:flex tw:justify-between tw:items-center",children:t.jsx("img",{src:s==="ara"?St:Pt,alt:"logo",className:"tw:w-40 tw:h-auto cursor-pointer"})}),t.jsx("p",{className:"tw:text-display-rg tw:font-bold tw:text-black! tw:mb-0! tw:text-start",children:w.email_address})]}),t.jsx(We,{children:e("core:manage-email-preferences")}),t.jsx(jt,{publicRouteParams:a,handleToggleSubscription:x})]})}),t.jsx(Ct,{isOpen:r,onClose:n,onUnsubscribe:b,email:w.email_address,entity:a.entity_key})]})}function Rt(){return t.jsxs(t.Fragment,{children:[t.jsx(o.Suspense,{fallback:t.jsx(Q,{}),children:t.jsx(Et,{})}),t.jsx(K,{})]})}function Vt({publicRouteParams:a}){const e=o.useRef(null),{isRTL:s}=U();return t.jsxs(je,{getContainer:()=>e.current,children:[t.jsx("div",{dir:s?"rtl":"ltr",ref:e,children:t.jsx(o.Suspense,{fallback:t.jsx(Q,{}),children:t.jsx(kt,{publicRouteParams:a})})}),t.jsx(K,{})]})}export{Rt as E,Vt as a};
