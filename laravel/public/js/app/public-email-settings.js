(function(){"use strict";try{if(typeof document<"u"){var t=document.createElement("style");t.appendChild(document.createTextNode(`/*! tailwindcss v4.1.13 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial}}}#container-react{color-scheme:light dark;color:#ffffffde;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:#242424;font-family:Roboto,Segoe UI,Helvetica,Tahoma,Open Sans,arial,serif;font-size:16px;font-weight:400;line-height:1.5}#container-react *,#container-react :before,#container-react :after{box-sizing:border-box}@layer theme{:root,:host{--tw-font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--tw-font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--tw-color-green-600:oklch(62.7% .194 149.214);--tw-color-blue-600:oklch(54.6% .245 262.881);--tw-color-slate-500:oklch(55.4% .046 257.417);--tw-color-slate-700:oklch(37.2% .044 257.287);--tw-color-gray-100:oklch(96.7% .003 264.542);--tw-color-gray-200:oklch(92.8% .006 264.531);--tw-color-gray-300:oklch(87.2% .01 258.338);--tw-color-gray-400:oklch(70.7% .022 261.325);--tw-color-gray-500:oklch(55.1% .027 264.364);--tw-color-gray-600:oklch(44.6% .03 256.802);--tw-color-gray-900:oklch(21% .034 264.665);--tw-color-black:#000;--tw-color-white:#fff;--tw-spacing:.25rem;--tw-container-md:28rem;--tw-text-xs:.75rem;--tw-text-xs--line-height:calc(1/.75);--tw-text-sm:.875rem;--tw-text-sm--line-height:calc(1.25/.875);--tw-text-base:14px;--tw-text-base--line-height: 1.5 ;--tw-text-lg:1.125rem;--tw-text-lg--line-height:calc(1.75/1.125);--tw-text-xl:1.25rem;--tw-text-xl--line-height:calc(1.75/1.25);--tw-font-weight-normal:400;--tw-font-weight-medium:500;--tw-font-weight-semibold:600;--tw-font-weight-bold:700;--tw-tracking-wider:.05em;--tw-leading-normal:1.25;--tw-radius-xs:2px;--tw-radius-sm:.25rem;--tw-radius-2xl:1rem;--tw-ease-in:cubic-bezier(.4,0,1,1);--tw-ease-out:cubic-bezier(0,0,.2,1);--tw-ease-in-out:cubic-bezier(.4,0,.2,1);--tw-animate-spin:spin 1s linear infinite;--tw-blur-xs:4px;--tw-default-transition-duration:.15s;--tw-default-transition-timing-function:cubic-bezier(.4,0,.2,1);--tw-default-font-family:var(--tw-font-sans);--tw-default-mono-font-family:var(--tw-font-mono);--tw-text-display-xl:2rem;--tw-text-display-rg:1.25rem;--tw-text-annotation:12px;--tw-color-light-gray-4:#9ea1ba;--tw-color-light-gray-1:#f6f9fc;--tw-color-primary-black:#202124;--tw-color-danger-1:#eb2121;--tw-color-success-2:#0c744a;--tw-color-info-1:#1877f2}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--tw-default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--tw-default-font-feature-settings,normal);font-variation-settings:var(--tw-default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--tw-default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--tw-default-mono-font-feature-settings,normal);font-variation-settings:var(--tw-default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}::-webkit-calendar-picker-indicator{line-height:1}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}}@layer components;@layer utilities{.tw\\:pointer-events-none{pointer-events:none}.tw\\:absolute{position:absolute}.tw\\:fixed{position:fixed}.tw\\:relative{position:relative}.tw\\:inset-0{inset:calc(var(--tw-spacing)*0)}.tw\\:inset-y-0{inset-block:calc(var(--tw-spacing)*0)}.tw\\:start-0{inset-inline-start:calc(var(--tw-spacing)*0)}.tw\\:end-2{inset-inline-end:calc(var(--tw-spacing)*2)}.tw\\:top-1\\/2{top:50%}.tw\\:right-\\[24px\\]{right:24px}.tw\\:left-\\[24px\\]{left:24px}.tw\\:left-\\[32px\\]{left:32px}.tw\\:left-\\[calc\\(100\\%-32px\\)\\]{left:calc(100% - 32px)}.tw\\:z-10{z-index:10}.tw\\:z-50{z-index:50}.tw\\:m-0\\!{margin:calc(var(--tw-spacing)*0)!important}.tw\\:mx-auto{margin-inline:auto}.tw\\:my-0{margin-block:calc(var(--tw-spacing)*0)}.tw\\:mt-3{margin-top:calc(var(--tw-spacing)*3)}.tw\\:mt-6{margin-top:calc(var(--tw-spacing)*6)}.tw\\:mb-0\\!{margin-bottom:calc(var(--tw-spacing)*0)!important}.tw\\:mb-\\[8px\\]\\!{margin-bottom:8px!important}.tw\\:flex{display:flex}.tw\\:flex\\!{display:flex!important}.tw\\:grid{display:grid}.tw\\:inline-flex{display:inline-flex}.tw\\:size-20{width:calc(var(--tw-spacing)*20);height:calc(var(--tw-spacing)*20)}.tw\\:size-full{width:100%;height:100%}.tw\\:h-5{height:calc(var(--tw-spacing)*5)}.tw\\:h-6{height:calc(var(--tw-spacing)*6)}.tw\\:h-12{height:calc(var(--tw-spacing)*12)}.tw\\:h-\\[14px\\]{height:14px}.tw\\:h-\\[22px\\]{height:22px}.tw\\:h-\\[32px\\]{height:32px}.tw\\:h-\\[66px\\]{height:66px}.tw\\:h-auto{height:auto}.tw\\:h-full{height:100%}.tw\\:max-h-60{max-height:calc(var(--tw-spacing)*60)}.tw\\:max-h-\\[34px\\]{max-height:34px}.tw\\:max-h-\\[38px\\]{max-height:38px}.tw\\:max-h-\\[42px\\]{max-height:42px}.tw\\:min-h-\\[34px\\]{min-height:34px}.tw\\:min-h-\\[38px\\]{min-height:38px}.tw\\:min-h-\\[42px\\]{min-height:42px}.tw\\:min-h-\\[400px\\]{min-height:400px}.tw\\:min-h-full{min-height:100%}.tw\\:min-h-screen{min-height:100vh}.tw\\:w-5{width:calc(var(--tw-spacing)*5)}.tw\\:w-6{width:calc(var(--tw-spacing)*6)}.tw\\:w-12{width:calc(var(--tw-spacing)*12)}.tw\\:w-40{width:calc(var(--tw-spacing)*40)}.tw\\:w-72{width:calc(var(--tw-spacing)*72)}.tw\\:w-\\[2px\\]{width:2px}.tw\\:w-\\[14px\\]{width:14px}.tw\\:w-\\[40px\\]{width:40px}.tw\\:w-\\[45px\\]{width:45px}.tw\\:w-\\[calc\\(var\\(--trigger-width\\)\\+36px\\)\\]{width:calc(var(--trigger-width) + 36px)}.tw\\:w-full{width:100%}.tw\\:w-max{width:max-content}.tw\\:max-w-\\[85vw\\]{max-width:85vw}.tw\\:max-w-md{max-width:var(--tw-container-md)}.tw\\:min-w-\\[170px\\]{min-width:170px}.tw\\:min-w-\\[var\\(--trigger-width\\)\\]{min-width:var(--trigger-width)}.tw\\:min-w-full{min-width:100%}.tw\\:flex-1{flex:1}.tw\\:-translate-x-1\\/2{--tw-translate-x: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:translate-x-0{--tw-translate-x:calc(var(--tw-spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:-translate-y-1\\/2{--tw-translate-y: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:rotate-180{rotate:180deg}.tw\\:transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.tw\\:animate-spin{animation:var(--tw-animate-spin)}.tw\\:cursor-default{cursor:default}.tw\\:cursor-pointer{cursor:pointer}.tw\\:grid-cols-\\[repeat\\(auto-fit\\,minmax\\(340px\\,1fr\\)\\)\\]{grid-template-columns:repeat(auto-fit,minmax(340px,1fr))}.tw\\:flex-col{flex-direction:column}.tw\\:flex-wrap{flex-wrap:wrap}.tw\\:items-center{align-items:center}.tw\\:items-stretch{align-items:stretch}.tw\\:justify-between{justify-content:space-between}.tw\\:justify-center{justify-content:center}.tw\\:justify-end{justify-content:flex-end}.tw\\:justify-start{justify-content:flex-start}.tw\\:gap-2{gap:calc(var(--tw-spacing)*2)}.tw\\:gap-4{gap:calc(var(--tw-spacing)*4)}.tw\\:gap-5{gap:calc(var(--tw-spacing)*5)}.tw\\:gap-6{gap:calc(var(--tw-spacing)*6)}.tw\\:gap-8{gap:calc(var(--tw-spacing)*8)}.tw\\:gap-\\[5px\\]{gap:5px}.tw\\:gap-\\[14px\\]{gap:14px}.tw\\:gap-\\[30px\\]{gap:30px}:where(.tw\\:divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}:where(.tw\\:divide-gray-200>:not(:last-child)){border-color:var(--tw-color-gray-200)}.tw\\:truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.tw\\:overflow-auto{overflow:auto}.tw\\:overflow-hidden{overflow:hidden}.tw\\:overflow-y-auto{overflow-y:auto}.tw\\:rounded{border-radius:.25rem}.tw\\:rounded-2xl{border-radius:var(--tw-radius-2xl)}.tw\\:rounded-\\[2px\\]{border-radius:2px}.tw\\:rounded-full{border-radius:3.40282e38px}.tw\\:rounded-sm{border-radius:var(--tw-radius-sm)}.tw\\:rounded-xs{border-radius:var(--tw-radius-xs)}.tw\\:border{border-style:var(--tw-border-style);border-width:1px}.tw\\:border-0{border-style:var(--tw-border-style);border-width:0}.tw\\:border-\\[1px\\]{border-style:var(--tw-border-style);border-width:1px}.tw\\:border-s-0{border-inline-start-style:var(--tw-border-style);border-inline-start-width:0}.tw\\:border-t-3{border-top-style:var(--tw-border-style);border-top-width:3px}.tw\\:border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.tw\\:border-b\\!{border-bottom-style:var(--tw-border-style)!important;border-bottom-width:1px!important}.tw\\:border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.tw\\:border-\\[\\#1EA702\\]\\!{border-color:#1ea702!important}.tw\\:border-\\[\\#3A3E63\\]{border-color:#3a3e63}.tw\\:border-\\[\\#3A3E63\\]\\!{border-color:#3a3e63!important}.tw\\:border-\\[\\#2356C2\\]\\!{border-color:#2356c2!important}.tw\\:border-\\[\\#DBE3EB\\]\\!{border-color:#dbe3eb!important}.tw\\:border-\\[\\#DF0000\\]\\!{border-color:#df0000!important}.tw\\:border-\\[\\#E4EBF2\\]{border-color:#e4ebf2}.tw\\:border-\\[\\#F88E00\\]\\!{border-color:#f88e00!important}.tw\\:border-\\[\\#e4ebf2\\]{border-color:#e4ebf2}.tw\\:border-blue-600{border-color:var(--tw-color-blue-600)}.tw\\:border-gray-200{border-color:var(--tw-color-gray-200)}.tw\\:border-info-1{border-color:var(--tw-color-info-1)}.tw\\:bg-\\[\\#A8ACC2\\]{background-color:#a8acc2}.tw\\:bg-\\[\\#E4EBF2\\]{background-color:#e4ebf2}.tw\\:bg-\\[\\#E4EBF2\\]\\!{background-color:#e4ebf2!important}.tw\\:bg-\\[\\#F0FAF7\\]\\!{background-color:#f0faf7!important}.tw\\:bg-\\[\\#F6F9FC\\]{background-color:#f6f9fc}.tw\\:bg-\\[\\#F8FAFF\\]\\!{background-color:#f8faff!important}.tw\\:bg-\\[\\#FDF1F0\\]\\!{background-color:#fdf1f0!important}.tw\\:bg-\\[\\#FFF9F0\\]\\!{background-color:#fff9f0!important}.tw\\:bg-\\[\\#FFFFFF\\]{background-color:#fff}.tw\\:bg-\\[\\#e4ebf2\\]{background-color:#e4ebf2}.tw\\:bg-\\[\\#eb2121\\]{background-color:#eb2121}.tw\\:bg-\\[\\#fff\\]{background-color:#fff}.tw\\:bg-black\\/25{background-color:var(--tw-color-black)}@supports (color:color-mix(in lab,red,red)){.tw\\:bg-black\\/25{background-color:color-mix(in oklab,var(--tw-color-black)25%,transparent)}}.tw\\:bg-gray-100{background-color:var(--tw-color-gray-100)}.tw\\:bg-transparent{background-color:#0000}.tw\\:bg-white{background-color:var(--tw-color-white)}.tw\\:bg-clip-padding{background-clip:padding-box}.tw\\:object-cover{object-fit:cover}.tw\\:p-1{padding:calc(var(--tw-spacing)*1)}.tw\\:p-4{padding:calc(var(--tw-spacing)*4)}.tw\\:p-6{padding:calc(var(--tw-spacing)*6)}.tw\\:px-3{padding-inline:calc(var(--tw-spacing)*3)}.tw\\:px-4{padding-inline:calc(var(--tw-spacing)*4)}.tw\\:px-5{padding-inline:calc(var(--tw-spacing)*5)}.tw\\:px-\\[16px\\]{padding-inline:16px}.tw\\:px-\\[70px\\]\\!{padding-inline:70px!important}.tw\\:py-1{padding-block:calc(var(--tw-spacing)*1)}.tw\\:py-2{padding-block:calc(var(--tw-spacing)*2)}.tw\\:py-3{padding-block:calc(var(--tw-spacing)*3)}.tw\\:py-5{padding-block:calc(var(--tw-spacing)*5)}.tw\\:py-6{padding-block:calc(var(--tw-spacing)*6)}.tw\\:py-10{padding-block:calc(var(--tw-spacing)*10)}.tw\\:py-12{padding-block:calc(var(--tw-spacing)*12)}.tw\\:py-\\[7\\.5px\\]{padding-block:7.5px}.tw\\:py-\\[8px\\]{padding-block:8px}.tw\\:py-\\[9px\\]{padding-block:9px}.tw\\:py-\\[10px\\]{padding-block:10px}.tw\\:py-\\[27\\.5px\\]{padding-block:27.5px}.tw\\:ps-0{padding-inline-start:calc(var(--tw-spacing)*0)}.tw\\:ps-4{padding-inline-start:calc(var(--tw-spacing)*4)}.tw\\:ps-\\[12px\\]{padding-inline-start:12px}.tw\\:ps-\\[40px\\]{padding-inline-start:40px}.tw\\:pe-0{padding-inline-end:calc(var(--tw-spacing)*0)}.tw\\:pe-4{padding-inline-end:calc(var(--tw-spacing)*4)}.tw\\:pe-\\[16px\\]{padding-inline-end:16px}.tw\\:pr-\\[20px\\]{padding-right:20px}.tw\\:pb-4{padding-bottom:calc(var(--tw-spacing)*4)}.tw\\:pb-5{padding-bottom:calc(var(--tw-spacing)*5)}.tw\\:pl-\\[70px\\]{padding-left:70px}.tw\\:text-center{text-align:center}.tw\\:text-left{text-align:left}.tw\\:text-start{text-align:start}.tw\\:align-middle{vertical-align:middle}.tw\\:text-base{font-size:var(--tw-text-base);line-height:var(--tw-leading,var(--tw-text-base--line-height))}.tw\\:text-lg{font-size:var(--tw-text-lg);line-height:var(--tw-leading,var(--tw-text-lg--line-height))}.tw\\:text-lg\\!{font-size:var(--tw-text-lg)!important;line-height:var(--tw-leading,var(--tw-text-lg--line-height))!important}.tw\\:text-sm{font-size:var(--tw-text-sm);line-height:var(--tw-leading,var(--tw-text-sm--line-height))}.tw\\:text-xl\\!{font-size:var(--tw-text-xl)!important;line-height:var(--tw-leading,var(--tw-text-xl--line-height))!important}.tw\\:text-xs{font-size:var(--tw-text-xs);line-height:var(--tw-leading,var(--tw-text-xs--line-height))}.tw\\:text-\\[14px\\]{font-size:14px}.tw\\:text-\\[14px\\]\\!{font-size:14px!important}.tw\\:text-\\[18px\\]{font-size:18px}.tw\\:text-\\[20px\\]{font-size:20px}.tw\\:text-annotation{font-size:var(--tw-text-annotation)}.tw\\:text-display-rg{font-size:var(--tw-text-display-rg)}.tw\\:text-display-xl\\!{font-size:var(--tw-text-display-xl)!important}.tw\\:leading-4\\!{--tw-leading:calc(var(--tw-spacing)*4)!important;line-height:calc(var(--tw-spacing)*4)!important}.tw\\:leading-6{--tw-leading:calc(var(--tw-spacing)*6);line-height:calc(var(--tw-spacing)*6)}.tw\\:leading-\\[18px\\]\\!{--tw-leading:18px!important;line-height:18px!important}.tw\\:leading-normal{--tw-leading:var(--tw-leading-normal);line-height:var(--tw-leading-normal)}.tw\\:font-bold{--tw-font-weight:var(--tw-font-weight-bold);font-weight:var(--tw-font-weight-bold)}.tw\\:font-medium{--tw-font-weight:var(--tw-font-weight-medium);font-weight:var(--tw-font-weight-medium)}.tw\\:font-medium\\!{--tw-font-weight:var(--tw-font-weight-medium)!important;font-weight:var(--tw-font-weight-medium)!important}.tw\\:font-normal{--tw-font-weight:var(--tw-font-weight-normal);font-weight:var(--tw-font-weight-normal)}.tw\\:font-semibold{--tw-font-weight:var(--tw-font-weight-semibold);font-weight:var(--tw-font-weight-semibold)}.tw\\:tracking-wider{--tw-tracking:var(--tw-tracking-wider);letter-spacing:var(--tw-tracking-wider)}.tw\\:whitespace-nowrap{white-space:nowrap}.tw\\:text-\\[\\#1A2B50\\]{color:#1a2b50}.tw\\:text-\\[\\#1EA702\\]\\!{color:#1ea702!important}.tw\\:text-\\[\\#3A3E63\\]{color:#3a3e63}.tw\\:text-\\[\\#3A3E63\\]\\!{color:#3a3e63!important}.tw\\:text-\\[\\#4e5381\\]{color:#4e5381}.tw\\:text-\\[\\#2356C2\\]\\!{color:#2356c2!important}.tw\\:text-\\[\\#75799D\\]{color:#75799d}.tw\\:text-\\[\\#202124\\]{color:#202124}.tw\\:text-\\[\\#DF0000\\]\\!{color:#df0000!important}.tw\\:text-\\[\\#F88E00\\]\\!{color:#f88e00!important}.tw\\:text-black{color:var(--tw-color-black)}.tw\\:text-black\\!{color:var(--tw-color-black)!important}.tw\\:text-danger-1{color:var(--tw-color-danger-1)}.tw\\:text-gray-300{color:var(--tw-color-gray-300)}.tw\\:text-gray-400{color:var(--tw-color-gray-400)}.tw\\:text-gray-500{color:var(--tw-color-gray-500)}.tw\\:text-gray-600{color:var(--tw-color-gray-600)}.tw\\:text-gray-900{color:var(--tw-color-gray-900)}.tw\\:text-green-600{color:var(--tw-color-green-600)}.tw\\:text-light-gray-4{color:var(--tw-color-light-gray-4)}.tw\\:text-primary-black{color:var(--tw-color-primary-black)}.tw\\:text-slate-500{color:var(--tw-color-slate-500)}.tw\\:text-slate-700{color:var(--tw-color-slate-700)}.tw\\:text-white{color:var(--tw-color-white)}.tw\\:antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.tw\\:shadow-\\[0px_3px_6px_0px_rgba\\(0\\,0\\,0\\,0\\.16\\)\\]{--tw-shadow:0px 3px 6px 0px var(--tw-shadow-color,#00000029);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:ring-1{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:ring-black,.tw\\:ring-black\\/5{--tw-ring-color:var(--tw-color-black)}@supports (color:color-mix(in lab,red,red)){.tw\\:ring-black\\/5{--tw-ring-color:color-mix(in oklab,var(--tw-color-black)5%,transparent)}}.tw\\:outline-hidden{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.tw\\:outline-hidden{outline-offset:2px;outline:2px solid #0000}}.tw\\:backdrop-blur-xs{--tw-backdrop-blur:blur(var(--tw-blur-xs));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.tw\\:transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--tw-default-transition-timing-function));transition-duration:var(--tw-duration,var(--tw-default-transition-duration))}.tw\\:transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--tw-default-transition-timing-function));transition-duration:var(--tw-duration,var(--tw-default-transition-duration))}.tw\\:duration-200{--tw-duration:.2s;transition-duration:.2s}.tw\\:duration-300{--tw-duration:.3s;transition-duration:.3s}.tw\\:ease-in{--tw-ease:var(--tw-ease-in);transition-timing-function:var(--tw-ease-in)}.tw\\:ease-in-out{--tw-ease:var(--tw-ease-in-out);transition-timing-function:var(--tw-ease-in-out)}.tw\\:ease-out{--tw-ease:var(--tw-ease-out);transition-timing-function:var(--tw-ease-out)}.tw\\:animate-in{--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial;animation-name:enter;animation-duration:.15s}.tw\\:animate-out{--tw-exit-opacity:initial;--tw-exit-scale:initial;--tw-exit-rotate:initial;--tw-exit-translate-x:initial;--tw-exit-translate-y:initial;animation-name:exit;animation-duration:.15s}.tw\\:outline-none{--tw-outline-style:none;outline-style:none}.tw\\:select-none{-webkit-user-select:none;user-select:none}.tw\\:duration-200{animation-duration:.2s}.tw\\:duration-300{animation-duration:.3s}.tw\\:ease-in{animation-timing-function:cubic-bezier(.4,0,1,1)}.tw\\:ease-in-out{animation-timing-function:cubic-bezier(.4,0,.2,1)}.tw\\:ease-out{animation-timing-function:cubic-bezier(0,0,.2,1)}.tw\\:fade-in{--tw-enter-opacity:0}.tw\\:fade-out{--tw-exit-opacity:0}.tw\\:zoom-in-95{--tw-enter-scale:.95}.tw\\:zoom-out-95{--tw-exit-scale:.95}.tw\\:group-invalid\\:border-danger-1:is(:where(.tw\\:group):where([data-rac])[data-invalid] *),.tw\\:group-invalid\\:border-danger-1:is(:where(.tw\\:group):where(:not([data-rac])):invalid *){border-color:var(--tw-color-danger-1)}.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where([data-rac])[data-focused] *){color:var(--tw-color-white)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where([data-rac])[data-focused] *){color:color-mix(in oklab,var(--tw-color-white)80%,transparent)}}.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where(:not([data-rac])):focus *){color:var(--tw-color-white)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-focus\\:text-white\\/80:is(:where(.tw\\:group):where(:not([data-rac])):focus *){color:color-mix(in oklab,var(--tw-color-white)80%,transparent)}}.tw\\:group-focus-visible\\:ring-2:is(:where(.tw\\:group):where([data-rac])[data-focus-visible] *),.tw\\:group-focus-visible\\:ring-2:is(:where(.tw\\:group):where(:not([data-rac])):focus-visible *){--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:group-disabled\\:cursor-not-allowed:is(:where(.tw\\:group):where([data-rac])[data-disabled] *),.tw\\:group-disabled\\:cursor-not-allowed:is(:where(.tw\\:group):where(:not([data-rac])):disabled *){cursor:not-allowed}.tw\\:group-disabled\\:bg-\\[\\#A8ACC2\\]\\/30:is(:where(.tw\\:group):where([data-rac])[data-disabled] *),.tw\\:group-disabled\\:bg-\\[\\#A8ACC2\\]\\/30:is(:where(.tw\\:group):where(:not([data-rac])):disabled *){background-color:#a8acc24d}.tw\\:group-disabled\\:bg-light-gray-1:is(:where(.tw\\:group):where([data-rac])[data-disabled] *),.tw\\:group-disabled\\:bg-light-gray-1:is(:where(.tw\\:group):where(:not([data-rac])):disabled *){background-color:var(--tw-color-light-gray-1)}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:static:is(:where(.tw\\:group\\/input-group)[data-group=true] *){position:static}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:min-h-full\\!:is(:where(.tw\\:group\\/input-group)[data-group=true] *){min-height:100%!important}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:rounded-none:is(:where(.tw\\:group\\/input-group)[data-group=true] *){border-radius:0}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:border-none:is(:where(.tw\\:group\\/input-group)[data-group=true] *){--tw-border-style:none;border-style:none}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *){--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tw\\:group-data-\\[pressed\\=true\\]\\:border-info-1:is(:where(.tw\\:group)[data-pressed=true] *){border-color:var(--tw-color-info-1)}.tw\\:group-data-\\[selected\\=true\\]\\:text-white\\/80:is(:where(.tw\\:group)[data-selected=true] *){color:var(--tw-color-white)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-data-\\[selected\\=true\\]\\:text-white\\/80:is(:where(.tw\\:group)[data-selected=true] *){color:color-mix(in oklab,var(--tw-color-white)80%,transparent)}}.tw\\:group-data-\\[success\\=true\\]\\:border-success-2:is(:where(.tw\\:group)[data-success=true] *){border-color:var(--tw-color-success-2)}.tw\\:group-data-\\[success\\=true\\]\\:text-success-2:is(:where(.tw\\:group)[data-success=true] *){color:var(--tw-color-success-2)}.tw\\:group-selected\\:translate-x-\\[18px\\]:is(:where(.tw\\:group)[data-selected] *){--tw-translate-x:18px;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:group-selected\\:bg-\\[\\#80C342\\]:is(:where(.tw\\:group)[data-selected] *){background-color:#80c342}.tw\\:group-disabled\\:group-selected\\:bg-\\[\\#DBF4C7\\]:is(:where(.tw\\:group):where([data-rac])[data-disabled] *):is(:where(.tw\\:group)[data-selected] *),.tw\\:group-disabled\\:group-selected\\:bg-\\[\\#DBF4C7\\]:is(:where(.tw\\:group):where(:not([data-rac])):disabled *):is(:where(.tw\\:group)[data-selected] *){background-color:#dbf4c7}.tw\\:peer-data-\\[select\\=true\\]\\:pe-2:is(:where(.tw\\:peer)[data-select=true]~*){padding-inline-end:calc(var(--tw-spacing)*2)}.tw\\:placeholder\\:text-light-gray-4::placeholder{color:var(--tw-color-light-gray-4)}.tw\\:focus-within\\:border-info-1:where([data-rac])[data-focus-within],.tw\\:focus-within\\:border-info-1:where(:not([data-rac])):focus-within{border-color:var(--tw-color-info-1)}.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-focus-within],.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):focus-within,.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-focus-within],.tw\\:group-invalid\\:focus-within\\:border-danger-1:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):focus-within{border-color:var(--tw-color-danger-1)}.tw\\:group-data-\\[success\\=true\\]\\:focus-within\\:border-success-2:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-focus-within],.tw\\:group-data-\\[success\\=true\\]\\:focus-within\\:border-success-2:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):focus-within{border-color:var(--tw-color-success-2)}.tw\\:hover\\:bg-gray-100:where([data-rac])[data-hovered]{background-color:var(--tw-color-gray-100)}@media (hover:hover){.tw\\:hover\\:bg-gray-100:where(:not([data-rac])):hover{background-color:var(--tw-color-gray-100)}}.tw\\:hover\\:bg-light-gray-1:where([data-rac])[data-hovered]{background-color:var(--tw-color-light-gray-1)}@media (hover:hover){.tw\\:hover\\:bg-light-gray-1:where(:not([data-rac])):hover{background-color:var(--tw-color-light-gray-1)}}.tw\\:hover\\:ring-2:where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:hover\\:ring-2:where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:hover\\:ring-\\[\\#D8E8FD\\]:where([data-rac])[data-hovered]{--tw-ring-color:#d8e8fd}@media (hover:hover){.tw\\:hover\\:ring-\\[\\#D8E8FD\\]:where(:not([data-rac])):hover{--tw-ring-color:#d8e8fd}}.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-2:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-hovered]{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where([data-rac])[data-hovered]{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):hover{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where([data-rac])[data-invalid] *):where(:not([data-rac])):hover{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}}.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-hovered]{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where([data-rac])[data-hovered]{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}@media (hover:hover){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):hover{--tw-ring-color:var(--tw-color-danger-1)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-invalid\\:hover\\:ring-danger-1\\/50:is(:where(.tw\\:group):where(:not([data-rac])):invalid *):where(:not([data-rac])):hover{--tw-ring-color:color-mix(in oklab,var(--tw-color-danger-1)50%,transparent)}}}.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where([data-rac])[data-disabled] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where([data-rac])[data-disabled] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where(:not([data-rac])):disabled *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-disabled\\:hover\\:ring-0:is(:where(.tw\\:group):where(:not([data-rac])):disabled *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-2:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-2:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-color:var(--tw-color-success-2)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-color:color-mix(in oklab,var(--tw-color-success-2)50%,transparent)}}@media (hover:hover){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-color:var(--tw-color-success-2)}@supports (color:color-mix(in lab,red,red)){.tw\\:group-data-\\[success\\=true\\]\\:hover\\:ring-success-2\\/50:is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-color:color-mix(in oklab,var(--tw-color-success-2)50%,transparent)}}}.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:group-data-\\[success\\=true\\]\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):is(:where(.tw\\:group)[data-success=true] *):where([data-rac])[data-hovered]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.tw\\:group-data-\\[group\\=true\\]\\/input-group\\:group-data-\\[success\\=true\\]\\:hover\\:ring-0:is(:where(.tw\\:group\\/input-group)[data-group=true] *):is(:where(.tw\\:group)[data-success=true] *):where(:not([data-rac])):hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.tw\\:focus\\:text-info-1:where([data-rac])[data-focused],.tw\\:focus\\:text-info-1:where(:not([data-rac])):focus{color:var(--tw-color-info-1)}.tw\\:focus\\:outline-none:where([data-rac])[data-focused],.tw\\:focus\\:outline-none:where(:not([data-rac])):focus{--tw-outline-style:none;outline-style:none}.tw\\:disabled\\:cursor-not-allowed:where([data-rac])[data-disabled],.tw\\:disabled\\:cursor-not-allowed:where(:not([data-rac])):disabled{cursor:not-allowed}.tw\\:disabled\\:bg-transparent:where([data-rac])[data-disabled],.tw\\:disabled\\:bg-transparent:where(:not([data-rac])):disabled{background-color:#0000}.tw\\:disabled\\:text-light-gray-4:where([data-rac])[data-disabled],.tw\\:disabled\\:text-light-gray-4:where(:not([data-rac])):disabled{color:var(--tw-color-light-gray-4)}.tw\\:disabled\\:opacity-50:where([data-rac])[data-disabled],.tw\\:disabled\\:opacity-50:where(:not([data-rac])):disabled{opacity:.5}.tw\\:data-\\[placement\\=bottom\\]\\:slide-in-from-top-2[data-placement=bottom]{--tw-enter-translate-y:-2}.tw\\:data-\\[placement\\=top\\]\\:slide-in-from-bottom-2[data-placement=top]{--tw-enter-translate-y:2}.tw\\:data-\\[selected\\=true\\]\\:text-info-1[data-selected=true]{color:var(--tw-color-info-1)}@media (min-width:576px){.tw\\:min-\\[576px\\]\\:max-w-\\[940px\\]{max-width:940px}}@media (min-width:768px){.tw\\:min-\\[768px\\]\\:max-w-\\[950px\\]{max-width:950px}}@media (min-width:992px){.tw\\:min-\\[992px\\]\\:max-w-\\[960px\\]{max-width:960px}}@media (min-width:1200px){.tw\\:min-\\[1200px\\]\\:max-w-\\[1140px\\]{max-width:1140px}}@media (min-width:40rem){.tw\\:sm\\:flex-row{flex-direction:row}.tw\\:sm\\:items-center{align-items:center}}.tw\\:rtl\\:translate-x-\\[-34px\\]:where(:dir(rtl),[dir=rtl],[dir=rtl] *){--tw-translate-x:-34px;translate:var(--tw-translate-x)var(--tw-translate-y)}.tw\\:rtl\\:flex-row-reverse:where(:dir(rtl),[dir=rtl],[dir=rtl] *){flex-direction:row-reverse}.tw\\:entering\\:animate-in[data-entering]{--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial;animation-name:enter;animation-duration:.15s}.tw\\:entering\\:fade-in[data-entering]{--tw-enter-opacity:0}.tw\\:entering\\:zoom-in-95[data-entering]{--tw-enter-scale:.95}.tw\\:exiting\\:animate-out[data-exiting]{--tw-exit-opacity:initial;--tw-exit-scale:initial;--tw-exit-rotate:initial;--tw-exit-translate-x:initial;--tw-exit-translate-y:initial;animation-name:exit;animation-duration:.15s}.tw\\:exiting\\:fade-out[data-exiting]{--tw-exit-opacity:0}.tw\\:exiting\\:zoom-out-95[data-exiting]{--tw-exit-scale:.95}.tw\\:\\[\\&\\:has\\(\\[data-select\\]\\)\\]\\:pe-\\[34px\\]:has([data-select]){padding-inline-end:34px}.tw\\:\\[\\&\\>input\\:not\\(\\:first-child\\)\\]\\:ps-2>input:not(:first-child){padding-inline-start:calc(var(--tw-spacing)*2)}.tw\\:\\[\\&\\>input\\:not\\(\\:last-child\\)\\]\\:pe-2>input:not(:last-child){padding-inline-end:calc(var(--tw-spacing)*2)}.tw\\:\\[\\&\\>textarea\\:not\\(\\:first-child\\)\\]\\:ps-2>textarea:not(:first-child){padding-inline-start:calc(var(--tw-spacing)*2)}.tw\\:\\[\\&\\>textarea\\:not\\(\\:last-child\\)\\]\\:pe-2>textarea:not(:last-child){padding-inline-end:calc(var(--tw-spacing)*2)}}.react-aria-Menu{max-height:inherit;box-sizing:border-box;background-color:#fff;outline:none;min-width:150px;padding:2px;overflow:auto}.react-aria-MenuItem{cursor:default;forced-color-adjust:none;cursor:pointer;border-radius:6px;outline:none;grid-template-areas:"label kbd""desc kbd";align-items:center;column-gap:20px;margin:2px;padding:.286rem .571rem;font-size:1.072rem;display:grid;position:relative}.search-input{background-color:#fff;border-color:#e4ebf2}.search-input:focus{outline:0;border-color:#1877f2!important}table thead tr th{color:#75799d;background:#f6f9fc}.text-bg-danger{background-color:#eb2121}.text-bg-success{background-color:#13b272}.btn-dots{color:#202124;background-color:#e4ebf2;width:46px;height:46px}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}
@charset "UTF-8";:root{--fc-now-indicator-color: green !important;--fc-daygrid-event-dot-width: 8px;font-family:roboto,sans-serif,system-ui,Avenir,Helvetica,Arial;line-height:1.5;font-weight:400;color-scheme:light dark;color:#ffffffde;background-color:#242424;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}a{font-weight:500;color:#646cff;text-decoration:inherit}a:hover{color:#535bf2}body{margin:0;display:flex;place-items:center;min-width:320px;min-height:100vh}h1{font-size:3.2em;line-height:1.1}button{border-radius:8px;border:1px solid transparent;padding:.6em 1.2em;font-size:1em;font-weight:500;font-family:inherit;background-color:#1a1a1a;cursor:pointer;transition:border-color .25s}button:hover{border-color:#646cff}button:focus,button:focus-visible{outline:4px auto -webkit-focus-ring-color}.fc .fc-timegrid-slot-label:before{--fc-now-indicator-color: green !important}.fc-timegrid-slot{height:70px!important}.tox .tox-statusbar{display:none!important}@media (prefers-color-scheme: light){:root{color:#213547;background-color:#fff}a:hover{color:#747bff}button{background-color:#f9f9f9}}@keyframes mymodal-slide{0%{transform:translate(100%)}to{transform:translate(0)}}.react-datepicker__navigation-icon:before,.react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow{border-color:#ccc;border-style:solid;border-width:3px 3px 0 0;content:"";display:block;height:9px;position:absolute;top:6px;width:9px}.react-datepicker__sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.react-datepicker-wrapper{display:inline-block;padding:0;border:0}.react-datepicker{font-family:Helvetica Neue,helvetica,arial,sans-serif;font-size:.8rem;background-color:#fff;color:#000;border:1px solid #aeaeae;border-radius:.3rem;display:inline-block;position:relative;line-height:initial}.react-datepicker--time-only .react-datepicker__time-container{border-left:0}.react-datepicker--time-only .react-datepicker__time,.react-datepicker--time-only .react-datepicker__time-box{border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker-popper{z-index:1;line-height:0}.react-datepicker-popper .react-datepicker__triangle{stroke:#aeaeae}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle{fill:#f0f0f0;color:#f0f0f0}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle{fill:#fff;color:#fff}.react-datepicker__header{text-align:center;background-color:#f0f0f0;border-bottom:1px solid #aeaeae;border-top-left-radius:.3rem;padding:8px 0;position:relative}.react-datepicker__header--time{padding-bottom:8px;padding-left:5px;padding-right:5px}.react-datepicker__header--time:not(.react-datepicker__header--time--only){border-top-left-radius:0}.react-datepicker__header:not(.react-datepicker__header--has-time-select){border-top-right-radius:.3rem}.react-datepicker__year-dropdown-container--select,.react-datepicker__month-dropdown-container--select,.react-datepicker__month-year-dropdown-container--select,.react-datepicker__year-dropdown-container--scroll,.react-datepicker__month-dropdown-container--scroll,.react-datepicker__month-year-dropdown-container--scroll{display:inline-block;margin:0 15px}.react-datepicker__current-month,.react-datepicker-time__header,.react-datepicker-year-header{margin-top:0;color:#000;font-weight:700;font-size:.944rem}h2.react-datepicker__current-month{padding:0;margin:0}.react-datepicker-time__header{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.react-datepicker__navigation{align-items:center;background:none;display:flex;justify-content:center;text-align:center;cursor:pointer;position:absolute;top:2px;padding:0;border:none;z-index:1;height:32px;width:32px;text-indent:-999em;overflow:hidden}.react-datepicker__navigation--previous{left:2px}.react-datepicker__navigation--next{right:2px}.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button){right:85px}.react-datepicker__navigation--years{position:relative;top:0;display:block;margin-left:auto;margin-right:auto}.react-datepicker__navigation--years-previous{top:4px}.react-datepicker__navigation--years-upcoming{top:-4px}.react-datepicker__navigation:hover *:before{border-color:#a6a6a6}.react-datepicker__navigation-icon{position:relative;top:-1px;font-size:20px;width:0}.react-datepicker__navigation-icon--next{left:-2px}.react-datepicker__navigation-icon--next:before{transform:rotate(45deg);left:-7px}.react-datepicker__navigation-icon--previous{right:-2px}.react-datepicker__navigation-icon--previous:before{transform:rotate(225deg);right:-7px}.react-datepicker__month-container{float:left}.react-datepicker__year{margin:.4rem;text-align:center}.react-datepicker__year-wrapper{display:flex;flex-wrap:wrap;max-width:180px}.react-datepicker__year .react-datepicker__year-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__month{margin:.4rem;text-align:center}.react-datepicker__month .react-datepicker__month-text,.react-datepicker__month .react-datepicker__quarter-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__input-time-container{clear:both;width:100%;float:left;margin:5px 0 10px 15px;text-align:left}.react-datepicker__input-time-container .react-datepicker-time__caption,.react-datepicker__input-time-container .react-datepicker-time__input-container{display:inline-block}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input{display:inline-block;margin-left:10px}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input{width:auto}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button,.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]{-moz-appearance:textfield}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter{margin-left:5px;display:inline-block}.react-datepicker__time-container{float:right;border-left:1px solid #aeaeae;width:85px}.react-datepicker__time-container--with-today-button{display:inline;border:1px solid #aeaeae;border-radius:.3rem;position:absolute;right:-87px;top:0}.react-datepicker__time-container .react-datepicker__time{position:relative;background:#fff;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{width:85px;overflow-x:hidden;margin:0 auto;text-align:center;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list{list-style:none;margin:0;height:calc(195px + .85rem);overflow-y:scroll;padding-right:0;padding-left:0;width:100%;box-sizing:content-box}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item{height:30px;padding:5px 10px;white-space:nowrap}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover{cursor:pointer;background-color:#f0f0f0}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected{background-color:#216ba5;color:#fff;font-weight:700}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover{background-color:#216ba5}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled{color:#ccc}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover{cursor:default;background-color:transparent}.react-datepicker__week-number{color:#ccc;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__week-number.react-datepicker__week-number--clickable{cursor:pointer}.react-datepicker__week-number.react-datepicker__week-number--clickable:not(.react-datepicker__week-number--selected):hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__week-number--selected{border-radius:.3rem;background-color:#216ba5;color:#fff}.react-datepicker__week-number--selected:hover{background-color:#1d5d90}.react-datepicker__day-names{white-space:nowrap;margin-bottom:-8px}.react-datepicker__week{white-space:nowrap}.react-datepicker__day-name,.react-datepicker__day,.react-datepicker__time-name{color:#000;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__day,.react-datepicker__month-text,.react-datepicker__quarter-text,.react-datepicker__year-text{cursor:pointer}.react-datepicker__day:not([aria-disabled=true]):hover,.react-datepicker__month-text:not([aria-disabled=true]):hover,.react-datepicker__quarter-text:not([aria-disabled=true]):hover,.react-datepicker__year-text:not([aria-disabled=true]):hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__day--today,.react-datepicker__month-text--today,.react-datepicker__quarter-text--today,.react-datepicker__year-text--today{font-weight:700}.react-datepicker__day--highlighted,.react-datepicker__month-text--highlighted,.react-datepicker__quarter-text--highlighted,.react-datepicker__year-text--highlighted{border-radius:.3rem;background-color:#3dcc4a;color:#fff}.react-datepicker__day--highlighted:not([aria-disabled=true]):hover,.react-datepicker__month-text--highlighted:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--highlighted:not([aria-disabled=true]):hover,.react-datepicker__year-text--highlighted:not([aria-disabled=true]):hover{background-color:#32be3f}.react-datepicker__day--highlighted-custom-1,.react-datepicker__month-text--highlighted-custom-1,.react-datepicker__quarter-text--highlighted-custom-1,.react-datepicker__year-text--highlighted-custom-1{color:#f0f}.react-datepicker__day--highlighted-custom-2,.react-datepicker__month-text--highlighted-custom-2,.react-datepicker__quarter-text--highlighted-custom-2,.react-datepicker__year-text--highlighted-custom-2{color:green}.react-datepicker__day--holidays,.react-datepicker__month-text--holidays,.react-datepicker__quarter-text--holidays,.react-datepicker__year-text--holidays{position:relative;border-radius:.3rem;background-color:#ff6803;color:#fff}.react-datepicker__day--holidays .overlay,.react-datepicker__month-text--holidays .overlay,.react-datepicker__quarter-text--holidays .overlay,.react-datepicker__year-text--holidays .overlay{position:absolute;bottom:100%;left:50%;transform:translate(-50%);background-color:#333;color:#fff;padding:4px;border-radius:4px;white-space:nowrap;visibility:hidden;opacity:0;transition:visibility 0s,opacity .3s ease-in-out}.react-datepicker__day--holidays:not([aria-disabled=true]):hover,.react-datepicker__month-text--holidays:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--holidays:not([aria-disabled=true]):hover,.react-datepicker__year-text--holidays:not([aria-disabled=true]):hover{background-color:#cf5300}.react-datepicker__day--holidays:hover .overlay,.react-datepicker__month-text--holidays:hover .overlay,.react-datepicker__quarter-text--holidays:hover .overlay,.react-datepicker__year-text--holidays:hover .overlay{visibility:visible;opacity:1}.react-datepicker__day--selected,.react-datepicker__day--in-selecting-range,.react-datepicker__day--in-range,.react-datepicker__month-text--selected,.react-datepicker__month-text--in-selecting-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--selected,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--selected,.react-datepicker__year-text--in-selecting-range,.react-datepicker__year-text--in-range{border-radius:.3rem;background-color:#216ba5;color:#fff}.react-datepicker__day--selected:not([aria-disabled=true]):hover,.react-datepicker__day--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__day--in-range:not([aria-disabled=true]):hover,.react-datepicker__month-text--selected:not([aria-disabled=true]):hover,.react-datepicker__month-text--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__month-text--in-range:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--selected:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--in-range:not([aria-disabled=true]):hover,.react-datepicker__year-text--selected:not([aria-disabled=true]):hover,.react-datepicker__year-text--in-selecting-range:not([aria-disabled=true]):hover,.react-datepicker__year-text--in-range:not([aria-disabled=true]):hover{background-color:#1d5d90}.react-datepicker__day--keyboard-selected,.react-datepicker__month-text--keyboard-selected,.react-datepicker__quarter-text--keyboard-selected,.react-datepicker__year-text--keyboard-selected{border-radius:.3rem;background-color:#bad9f1;color:#000}.react-datepicker__day--keyboard-selected:not([aria-disabled=true]):hover,.react-datepicker__month-text--keyboard-selected:not([aria-disabled=true]):hover,.react-datepicker__quarter-text--keyboard-selected:not([aria-disabled=true]):hover,.react-datepicker__year-text--keyboard-selected:not([aria-disabled=true]):hover{background-color:#1d5d90}.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range){background-color:#216ba580}.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range){background-color:#f0f0f0;color:#000}.react-datepicker__day--disabled,.react-datepicker__month-text--disabled,.react-datepicker__quarter-text--disabled,.react-datepicker__year-text--disabled{cursor:default;color:#ccc}.react-datepicker__day--disabled .overlay,.react-datepicker__month-text--disabled .overlay,.react-datepicker__quarter-text--disabled .overlay,.react-datepicker__year-text--disabled .overlay{position:absolute;bottom:70%;left:50%;transform:translate(-50%);background-color:#333;color:#fff;padding:4px;border-radius:4px;white-space:nowrap;visibility:hidden;opacity:0;transition:visibility 0s,opacity .3s ease-in-out}.react-datepicker__input-container{position:relative;display:inline-block;width:100%}.react-datepicker__input-container .react-datepicker__calendar-icon{position:absolute;padding:.5rem;box-sizing:content-box}.react-datepicker__view-calendar-icon input{padding:6px 10px 5px 25px}.react-datepicker__year-read-view,.react-datepicker__month-read-view,.react-datepicker__month-year-read-view{border:1px solid transparent;border-radius:.3rem;position:relative}.react-datepicker__year-read-view:hover,.react-datepicker__month-read-view:hover,.react-datepicker__month-year-read-view:hover{cursor:pointer}.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow{border-top-color:#b3b3b3}.react-datepicker__year-read-view--down-arrow,.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow{transform:rotate(135deg);right:-16px;top:0}.react-datepicker__year-dropdown,.react-datepicker__month-dropdown,.react-datepicker__month-year-dropdown{background-color:#f0f0f0;position:absolute;width:50%;left:25%;top:30px;z-index:1;text-align:center;border-radius:.3rem;border:1px solid #aeaeae}.react-datepicker__year-dropdown:hover,.react-datepicker__month-dropdown:hover,.react-datepicker__month-year-dropdown:hover{cursor:pointer}.react-datepicker__year-dropdown--scrollable,.react-datepicker__month-dropdown--scrollable,.react-datepicker__month-year-dropdown--scrollable{height:150px;overflow-y:scroll}.react-datepicker__year-option,.react-datepicker__month-option,.react-datepicker__month-year-option{line-height:20px;width:100%;display:block;margin-left:auto;margin-right:auto}.react-datepicker__year-option:first-of-type,.react-datepicker__month-option:first-of-type,.react-datepicker__month-year-option:first-of-type{border-top-left-radius:.3rem;border-top-right-radius:.3rem}.react-datepicker__year-option:last-of-type,.react-datepicker__month-option:last-of-type,.react-datepicker__month-year-option:last-of-type{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker__year-option:hover,.react-datepicker__month-option:hover,.react-datepicker__month-year-option:hover{background-color:#ccc}.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming{border-bottom-color:#b3b3b3}.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous{border-top-color:#b3b3b3}.react-datepicker__year-option--selected,.react-datepicker__month-option--selected,.react-datepicker__month-year-option--selected{position:absolute;left:15px}.react-datepicker__close-icon{cursor:pointer;background-color:transparent;border:0;outline:0;padding:0 6px 0 0;position:absolute;top:0;right:0;height:100%;display:table-cell;vertical-align:middle}.react-datepicker__close-icon:after{cursor:pointer;background-color:#216ba5;color:#fff;border-radius:50%;height:16px;width:16px;padding:2px;font-size:12px;line-height:1;text-align:center;display:table-cell;vertical-align:middle;content:"×"}.react-datepicker__close-icon--disabled{cursor:default}.react-datepicker__close-icon--disabled:after{cursor:default;background-color:#ccc}.react-datepicker__today-button{background:#f0f0f0;border-top:1px solid #aeaeae;cursor:pointer;text-align:center;font-weight:700;padding:5px 0;clear:left}.react-datepicker__portal{position:fixed;width:100vw;height:100vh;background-color:#000c;left:0;top:0;justify-content:center;align-items:center;display:flex;z-index:2147483647}.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__time-name{width:3rem;line-height:3rem}@media (max-width: 400px),(max-height: 550px){.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__time-name{width:2rem;line-height:2rem}}.react-datepicker__portal .react-datepicker__current-month,.react-datepicker__portal .react-datepicker-time__header{font-size:1.44rem}.react-datepicker__children-container{width:13.8rem;margin:.4rem;padding-right:.2rem;padding-left:.2rem;height:auto}.react-datepicker__aria-live{position:absolute;clip-path:circle(0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;width:1px;white-space:nowrap}.react-datepicker__calendar-icon{width:1em;height:1em;vertical-align:-.125em}`)),document.head.appendChild(t)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();
import{Q as r,c as o,j as t,r as n,a as s}from"./i18n-DJdGDBdH.js";import{a as i}from"./email-settings-Cfr3dp_H.js";import"./index-RNxW_Mg3.js";import"./Select-XdalPTad.js";import"./Input-D2QI4Qb_.js";import"./useDisclosure-C9DVYU_W.js";const a=new r,e=new URLSearchParams(window.location.search),c=e.get("token"),m=e.get("entity_key"),l=e.get("status");o.createRoot(document.getElementById("container-react")).render(t.jsx(n.StrictMode,{children:t.jsx(s,{client:a,children:t.jsx(i,{publicRouteParams:{token:c,entity_key:m,status:l}})})}));
