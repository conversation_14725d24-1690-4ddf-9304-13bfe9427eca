var iv=l=>{throw TypeError(l)};var Xc=(l,n,i)=>n.has(l)||iv("Cannot "+i);var T=(l,n,i)=>(Xc(l,n,"read from private field"),i?i.call(l):n.get(l)),se=(l,n,i)=>n.has(l)?iv("Cannot add the same private member more than once"):n instanceof WeakSet?n.add(l):n.set(l,i),I=(l,n,i,u)=>(Xc(l,n,"write to private field"),u?u.call(l,i):n.set(l,i),i),me=(l,n,i)=>(Xc(l,n,"access private method"),i);var as=(l,n,i,u)=>({set _(s){I(l,n,s,i)},get _(){return T(l,n,u)}});function iS(l,n){for(var i=0;i<n.length;i++){const u=n[i];if(typeof u!="string"&&!Array.isArray(u)){for(const s in u)if(s!=="default"&&!(s in l)){const f=Object.getOwnPropertyDescriptor(u,s);f&&Object.defineProperty(l,s,f.get?f:{enumerable:!0,get:()=>u[s]})}}}return Object.freeze(Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}))}var Y$=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function By(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var Fc={exports:{}},mr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rv;function rS(){if(rv)return mr;rv=1;var l=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function i(u,s,f){var d=null;if(f!==void 0&&(d=""+f),s.key!==void 0&&(d=""+s.key),"key"in s){f={};for(var h in s)h!=="key"&&(f[h]=s[h])}else f=s;return s=f.ref,{$$typeof:l,type:u,key:d,ref:s!==void 0?s:null,props:f}}return mr.Fragment=n,mr.jsx=i,mr.jsxs=i,mr}var uv;function uS(){return uv||(uv=1,Fc.exports=rS()),Fc.exports}var sS=uS(),Zc={exports:{}},ye={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sv;function oS(){if(sv)return ye;sv=1;var l=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),b=Symbol.iterator;function $(x){return x===null||typeof x!="object"?null:(x=b&&x[b]||x["@@iterator"],typeof x=="function"?x:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},O=Object.assign,R={};function N(x,H,J){this.props=x,this.context=H,this.refs=R,this.updater=J||E}N.prototype.isReactComponent={},N.prototype.setState=function(x,H){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,H,"setState")},N.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function P(){}P.prototype=N.prototype;function K(x,H,J){this.props=x,this.context=H,this.refs=R,this.updater=J||E}var k=K.prototype=new P;k.constructor=K,O(k,N.prototype),k.isPureReactComponent=!0;var B=Array.isArray,V={H:null,A:null,T:null,S:null,V:null},ee=Object.prototype.hasOwnProperty;function re(x,H,J,Z,ue,Se){return J=Se.ref,{$$typeof:l,type:x,key:H,ref:J!==void 0?J:null,props:Se}}function te(x,H){return re(x.type,H,void 0,void 0,void 0,x.props)}function ae(x){return typeof x=="object"&&x!==null&&x.$$typeof===l}function F(x){var H={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(J){return H[J]})}var U=/\/+/g;function X(x,H){return typeof x=="object"&&x!==null&&x.key!=null?F(""+x.key):H.toString(36)}function le(){}function Y(x){switch(x.status){case"fulfilled":return x.value;case"rejected":throw x.reason;default:switch(typeof x.status=="string"?x.then(le,le):(x.status="pending",x.then(function(H){x.status==="pending"&&(x.status="fulfilled",x.value=H)},function(H){x.status==="pending"&&(x.status="rejected",x.reason=H)})),x.status){case"fulfilled":return x.value;case"rejected":throw x.reason}}throw x}function pe(x,H,J,Z,ue){var Se=typeof x;(Se==="undefined"||Se==="boolean")&&(x=null);var ge=!1;if(x===null)ge=!0;else switch(Se){case"bigint":case"string":case"number":ge=!0;break;case"object":switch(x.$$typeof){case l:case n:ge=!0;break;case m:return ge=x._init,pe(ge(x._payload),H,J,Z,ue)}}if(ge)return ue=ue(x),ge=Z===""?"."+X(x,0):Z,B(ue)?(J="",ge!=null&&(J=ge.replace(U,"$&/")+"/"),pe(ue,H,J,"",function(mn){return mn})):ue!=null&&(ae(ue)&&(ue=te(ue,J+(ue.key==null||x&&x.key===ue.key?"":(""+ue.key).replace(U,"$&/")+"/")+ge)),H.push(ue)),1;ge=0;var ut=Z===""?".":Z+":";if(B(x))for(var Ue=0;Ue<x.length;Ue++)Z=x[Ue],Se=ut+X(Z,Ue),ge+=pe(Z,H,J,Se,ue);else if(Ue=$(x),typeof Ue=="function")for(x=Ue.call(x),Ue=0;!(Z=x.next()).done;)Z=Z.value,Se=ut+X(Z,Ue++),ge+=pe(Z,H,J,Se,ue);else if(Se==="object"){if(typeof x.then=="function")return pe(Y(x),H,J,Z,ue);throw H=String(x),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.")}return ge}function C(x,H,J){if(x==null)return x;var Z=[],ue=0;return pe(x,Z,"","",function(Se){return H.call(J,Se,ue++)}),Z}function G(x){if(x._status===-1){var H=x._result;H=H(),H.then(function(J){(x._status===0||x._status===-1)&&(x._status=1,x._result=J)},function(J){(x._status===0||x._status===-1)&&(x._status=2,x._result=J)}),x._status===-1&&(x._status=0,x._result=H)}if(x._status===1)return x._result.default;throw x._result}var W=typeof reportError=="function"?reportError:function(x){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var H=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof x=="object"&&x!==null&&typeof x.message=="string"?String(x.message):String(x),error:x});if(!window.dispatchEvent(H))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",x);return}console.error(x)};function oe(){}return ye.Children={map:C,forEach:function(x,H,J){C(x,function(){H.apply(this,arguments)},J)},count:function(x){var H=0;return C(x,function(){H++}),H},toArray:function(x){return C(x,function(H){return H})||[]},only:function(x){if(!ae(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},ye.Component=N,ye.Fragment=i,ye.Profiler=s,ye.PureComponent=K,ye.StrictMode=u,ye.Suspense=v,ye.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=V,ye.__COMPILER_RUNTIME={__proto__:null,c:function(x){return V.H.useMemoCache(x)}},ye.cache=function(x){return function(){return x.apply(null,arguments)}},ye.cloneElement=function(x,H,J){if(x==null)throw Error("The argument must be a React element, but you passed "+x+".");var Z=O({},x.props),ue=x.key,Se=void 0;if(H!=null)for(ge in H.ref!==void 0&&(Se=void 0),H.key!==void 0&&(ue=""+H.key),H)!ee.call(H,ge)||ge==="key"||ge==="__self"||ge==="__source"||ge==="ref"&&H.ref===void 0||(Z[ge]=H[ge]);var ge=arguments.length-2;if(ge===1)Z.children=J;else if(1<ge){for(var ut=Array(ge),Ue=0;Ue<ge;Ue++)ut[Ue]=arguments[Ue+2];Z.children=ut}return re(x.type,ue,void 0,void 0,Se,Z)},ye.createContext=function(x){return x={$$typeof:d,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null},x.Provider=x,x.Consumer={$$typeof:f,_context:x},x},ye.createElement=function(x,H,J){var Z,ue={},Se=null;if(H!=null)for(Z in H.key!==void 0&&(Se=""+H.key),H)ee.call(H,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(ue[Z]=H[Z]);var ge=arguments.length-2;if(ge===1)ue.children=J;else if(1<ge){for(var ut=Array(ge),Ue=0;Ue<ge;Ue++)ut[Ue]=arguments[Ue+2];ue.children=ut}if(x&&x.defaultProps)for(Z in ge=x.defaultProps,ge)ue[Z]===void 0&&(ue[Z]=ge[Z]);return re(x,Se,void 0,void 0,null,ue)},ye.createRef=function(){return{current:null}},ye.forwardRef=function(x){return{$$typeof:h,render:x}},ye.isValidElement=ae,ye.lazy=function(x){return{$$typeof:m,_payload:{_status:-1,_result:x},_init:G}},ye.memo=function(x,H){return{$$typeof:p,type:x,compare:H===void 0?null:H}},ye.startTransition=function(x){var H=V.T,J={};V.T=J;try{var Z=x(),ue=V.S;ue!==null&&ue(J,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(oe,W)}catch(Se){W(Se)}finally{V.T=H}},ye.unstable_useCacheRefresh=function(){return V.H.useCacheRefresh()},ye.use=function(x){return V.H.use(x)},ye.useActionState=function(x,H,J){return V.H.useActionState(x,H,J)},ye.useCallback=function(x,H){return V.H.useCallback(x,H)},ye.useContext=function(x){return V.H.useContext(x)},ye.useDebugValue=function(){},ye.useDeferredValue=function(x,H){return V.H.useDeferredValue(x,H)},ye.useEffect=function(x,H,J){var Z=V.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(x,H)},ye.useId=function(){return V.H.useId()},ye.useImperativeHandle=function(x,H,J){return V.H.useImperativeHandle(x,H,J)},ye.useInsertionEffect=function(x,H){return V.H.useInsertionEffect(x,H)},ye.useLayoutEffect=function(x,H){return V.H.useLayoutEffect(x,H)},ye.useMemo=function(x,H){return V.H.useMemo(x,H)},ye.useOptimistic=function(x,H){return V.H.useOptimistic(x,H)},ye.useReducer=function(x,H,J){return V.H.useReducer(x,H,J)},ye.useRef=function(x){return V.H.useRef(x)},ye.useState=function(x){return V.H.useState(x)},ye.useSyncExternalStore=function(x,H,J){return V.H.useSyncExternalStore(x,H,J)},ye.useTransition=function(){return V.H.useTransition()},ye.version="19.1.1",ye}var ov;function Yf(){return ov||(ov=1,Zc.exports=oS()),Zc.exports}var M=Yf();const ce=By(M),V$=iS({__proto__:null,default:ce},[M]);var Jc={exports:{}},br={},Wc={exports:{}},Ic={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cv;function cS(){return cv||(cv=1,(function(l){function n(C,G){var W=C.length;C.push(G);e:for(;0<W;){var oe=W-1>>>1,x=C[oe];if(0<s(x,G))C[oe]=G,C[W]=x,W=oe;else break e}}function i(C){return C.length===0?null:C[0]}function u(C){if(C.length===0)return null;var G=C[0],W=C.pop();if(W!==G){C[0]=W;e:for(var oe=0,x=C.length,H=x>>>1;oe<H;){var J=2*(oe+1)-1,Z=C[J],ue=J+1,Se=C[ue];if(0>s(Z,W))ue<x&&0>s(Se,Z)?(C[oe]=Se,C[ue]=W,oe=ue):(C[oe]=Z,C[J]=W,oe=J);else if(ue<x&&0>s(Se,W))C[oe]=Se,C[ue]=W,oe=ue;else break e}}return G}function s(C,G){var W=C.sortIndex-G.sortIndex;return W!==0?W:C.id-G.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;l.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();l.unstable_now=function(){return d.now()-h}}var v=[],p=[],m=1,b=null,$=3,E=!1,O=!1,R=!1,N=!1,P=typeof setTimeout=="function"?setTimeout:null,K=typeof clearTimeout=="function"?clearTimeout:null,k=typeof setImmediate<"u"?setImmediate:null;function B(C){for(var G=i(p);G!==null;){if(G.callback===null)u(p);else if(G.startTime<=C)u(p),G.sortIndex=G.expirationTime,n(v,G);else break;G=i(p)}}function V(C){if(R=!1,B(C),!O)if(i(v)!==null)O=!0,ee||(ee=!0,X());else{var G=i(p);G!==null&&pe(V,G.startTime-C)}}var ee=!1,re=-1,te=5,ae=-1;function F(){return N?!0:!(l.unstable_now()-ae<te)}function U(){if(N=!1,ee){var C=l.unstable_now();ae=C;var G=!0;try{e:{O=!1,R&&(R=!1,K(re),re=-1),E=!0;var W=$;try{t:{for(B(C),b=i(v);b!==null&&!(b.expirationTime>C&&F());){var oe=b.callback;if(typeof oe=="function"){b.callback=null,$=b.priorityLevel;var x=oe(b.expirationTime<=C);if(C=l.unstable_now(),typeof x=="function"){b.callback=x,B(C),G=!0;break t}b===i(v)&&u(v),B(C)}else u(v);b=i(v)}if(b!==null)G=!0;else{var H=i(p);H!==null&&pe(V,H.startTime-C),G=!1}}break e}finally{b=null,$=W,E=!1}G=void 0}}finally{G?X():ee=!1}}}var X;if(typeof k=="function")X=function(){k(U)};else if(typeof MessageChannel<"u"){var le=new MessageChannel,Y=le.port2;le.port1.onmessage=U,X=function(){Y.postMessage(null)}}else X=function(){P(U,0)};function pe(C,G){re=P(function(){C(l.unstable_now())},G)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(C){C.callback=null},l.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):te=0<C?Math.floor(1e3/C):5},l.unstable_getCurrentPriorityLevel=function(){return $},l.unstable_next=function(C){switch($){case 1:case 2:case 3:var G=3;break;default:G=$}var W=$;$=G;try{return C()}finally{$=W}},l.unstable_requestPaint=function(){N=!0},l.unstable_runWithPriority=function(C,G){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var W=$;$=C;try{return G()}finally{$=W}},l.unstable_scheduleCallback=function(C,G,W){var oe=l.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?oe+W:oe):W=oe,C){case 1:var x=-1;break;case 2:x=250;break;case 5:x=1073741823;break;case 4:x=1e4;break;default:x=5e3}return x=W+x,C={id:m++,callback:G,priorityLevel:C,startTime:W,expirationTime:x,sortIndex:-1},W>oe?(C.sortIndex=W,n(p,C),i(v)===null&&C===i(p)&&(R?(K(re),re=-1):R=!0,pe(V,W-oe))):(C.sortIndex=x,n(v,C),O||E||(O=!0,ee||(ee=!0,X()))),C},l.unstable_shouldYield=F,l.unstable_wrapCallback=function(C){var G=$;return function(){var W=$;$=G;try{return C.apply(this,arguments)}finally{$=W}}}})(Ic)),Ic}var fv;function fS(){return fv||(fv=1,Wc.exports=cS()),Wc.exports}var ef={exports:{}},ht={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dv;function dS(){if(dv)return ht;dv=1;var l=Yf();function n(v){var p="https://react.dev/errors/"+v;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var m=2;m<arguments.length;m++)p+="&args[]="+encodeURIComponent(arguments[m])}return"Minified React error #"+v+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var u={d:{f:i,r:function(){throw Error(n(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(v,p,m){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:b==null?null:""+b,children:v,containerInfo:p,implementation:m}}var d=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(v,p){if(v==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return ht.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,ht.createPortal=function(v,p){var m=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(n(299));return f(v,p,null,m)},ht.flushSync=function(v){var p=d.T,m=u.p;try{if(d.T=null,u.p=2,v)return v()}finally{d.T=p,u.p=m,u.d.f()}},ht.preconnect=function(v,p){typeof v=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,u.d.C(v,p))},ht.prefetchDNS=function(v){typeof v=="string"&&u.d.D(v)},ht.preinit=function(v,p){if(typeof v=="string"&&p&&typeof p.as=="string"){var m=p.as,b=h(m,p.crossOrigin),$=typeof p.integrity=="string"?p.integrity:void 0,E=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;m==="style"?u.d.S(v,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:b,integrity:$,fetchPriority:E}):m==="script"&&u.d.X(v,{crossOrigin:b,integrity:$,fetchPriority:E,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},ht.preinitModule=function(v,p){if(typeof v=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var m=h(p.as,p.crossOrigin);u.d.M(v,{crossOrigin:m,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&u.d.M(v)},ht.preload=function(v,p){if(typeof v=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var m=p.as,b=h(m,p.crossOrigin);u.d.L(v,m,{crossOrigin:b,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},ht.preloadModule=function(v,p){if(typeof v=="string")if(p){var m=h(p.as,p.crossOrigin);u.d.m(v,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:m,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else u.d.m(v)},ht.requestFormReset=function(v){u.d.r(v)},ht.unstable_batchedUpdates=function(v,p){return v(p)},ht.useFormState=function(v,p,m){return d.H.useFormState(v,p,m)},ht.useFormStatus=function(){return d.H.useHostTransitionStatus()},ht.version="19.1.1",ht}var hv;function Qy(){if(hv)return ef.exports;hv=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(n){console.error(n)}}return l(),ef.exports=dS(),ef.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pv;function hS(){if(pv)return br;pv=1;var l=fS(),n=Yf(),i=Qy();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(u(188))}function v(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(u(188));return t!==e?null:e}for(var a=e,r=t;;){var o=a.return;if(o===null)break;var c=o.alternate;if(c===null){if(r=o.return,r!==null){a=r;continue}break}if(o.child===c.child){for(c=o.child;c;){if(c===a)return h(o),e;if(c===r)return h(o),t;c=c.sibling}throw Error(u(188))}if(a.return!==r.return)a=o,r=c;else{for(var g=!1,y=o.child;y;){if(y===a){g=!0,a=o,r=c;break}if(y===r){g=!0,r=o,a=c;break}y=y.sibling}if(!g){for(y=c.child;y;){if(y===a){g=!0,a=c,r=o;break}if(y===r){g=!0,r=c,a=o;break}y=y.sibling}if(!g)throw Error(u(189))}}if(a.alternate!==r)throw Error(u(190))}if(a.tag!==3)throw Error(u(188));return a.stateNode.current===a?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var m=Object.assign,b=Symbol.for("react.element"),$=Symbol.for("react.transitional.element"),E=Symbol.for("react.portal"),O=Symbol.for("react.fragment"),R=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),P=Symbol.for("react.provider"),K=Symbol.for("react.consumer"),k=Symbol.for("react.context"),B=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),ee=Symbol.for("react.suspense_list"),re=Symbol.for("react.memo"),te=Symbol.for("react.lazy"),ae=Symbol.for("react.activity"),F=Symbol.for("react.memo_cache_sentinel"),U=Symbol.iterator;function X(e){return e===null||typeof e!="object"?null:(e=U&&e[U]||e["@@iterator"],typeof e=="function"?e:null)}var le=Symbol.for("react.client.reference");function Y(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===le?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case O:return"Fragment";case N:return"Profiler";case R:return"StrictMode";case V:return"Suspense";case ee:return"SuspenseList";case ae:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case E:return"Portal";case k:return(e.displayName||"Context")+".Provider";case K:return(e._context.displayName||"Context")+".Consumer";case B:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case re:return t=e.displayName||null,t!==null?t:Y(e.type)||"Memo";case te:t=e._payload,e=e._init;try{return Y(e(t))}catch{}}return null}var pe=Array.isArray,C=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W={pending:!1,data:null,method:null,action:null},oe=[],x=-1;function H(e){return{current:e}}function J(e){0>x||(e.current=oe[x],oe[x]=null,x--)}function Z(e,t){x++,oe[x]=e.current,e.current=t}var ue=H(null),Se=H(null),ge=H(null),ut=H(null);function Ue(e,t){switch(Z(ge,t),Z(Se,e),Z(ue,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Lg(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Lg(t),e=_g(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(ue),Z(ue,e)}function mn(){J(ue),J(Se),J(ge)}function Ds(e){e.memoizedState!==null&&Z(ut,e);var t=ue.current,a=_g(t,e.type);t!==a&&(Z(Se,e),Z(ue,a))}function Qr(e){Se.current===e&&(J(ue),J(Se)),ut.current===e&&(J(ut),hr._currentValue=W)}var Ls=Object.prototype.hasOwnProperty,_s=l.unstable_scheduleCallback,zs=l.unstable_cancelCallback,H0=l.unstable_shouldYield,j0=l.unstable_requestPaint,ln=l.unstable_now,K0=l.unstable_getCurrentPriorityLevel,hd=l.unstable_ImmediatePriority,pd=l.unstable_UserBlockingPriority,Pr=l.unstable_NormalPriority,q0=l.unstable_LowPriority,gd=l.unstable_IdlePriority,B0=l.log,Q0=l.unstable_setDisableYieldValue,Ei=null,At=null;function Qn(e){if(typeof B0=="function"&&Q0(e),At&&typeof At.setStrictMode=="function")try{At.setStrictMode(Ei,e)}catch{}}var Mt=Math.clz32?Math.clz32:G0,P0=Math.log,k0=Math.LN2;function G0(e){return e>>>=0,e===0?32:31-(P0(e)/k0|0)|0}var kr=256,Gr=4194304;function wa(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Yr(e,t,a){var r=e.pendingLanes;if(r===0)return 0;var o=0,c=e.suspendedLanes,g=e.pingedLanes;e=e.warmLanes;var y=r&134217727;return y!==0?(r=y&~c,r!==0?o=wa(r):(g&=y,g!==0?o=wa(g):a||(a=y&~e,a!==0&&(o=wa(a))))):(y=r&~c,y!==0?o=wa(y):g!==0?o=wa(g):a||(a=r&~e,a!==0&&(o=wa(a)))),o===0?0:t!==0&&t!==o&&(t&c)===0&&(c=o&-o,a=t&-t,c>=a||c===32&&(a&4194048)!==0)?t:o}function $i(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Y0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vd(){var e=kr;return kr<<=1,(kr&4194048)===0&&(kr=256),e}function yd(){var e=Gr;return Gr<<=1,(Gr&62914560)===0&&(Gr=4194304),e}function Us(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Ti(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function V0(e,t,a,r,o,c){var g=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var y=e.entanglements,S=e.expirationTimes,L=e.hiddenUpdates;for(a=g&~a;0<a;){var j=31-Mt(a),Q=1<<j;y[j]=0,S[j]=-1;var _=L[j];if(_!==null)for(L[j]=null,j=0;j<_.length;j++){var z=_[j];z!==null&&(z.lane&=-536870913)}a&=~Q}r!==0&&md(e,r,0),c!==0&&o===0&&e.tag!==0&&(e.suspendedLanes|=c&~(g&~t))}function md(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-Mt(t);e.entangledLanes|=t,e.entanglements[r]=e.entanglements[r]|1073741824|a&4194090}function bd(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var r=31-Mt(a),o=1<<r;o&t|e[r]&t&&(e[r]|=t),a&=~o}}function Hs(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function js(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Sd(){var e=G.p;return e!==0?e:(e=window.event,e===void 0?32:Ig(e.type))}function X0(e,t){var a=G.p;try{return G.p=e,t()}finally{G.p=a}}var Pn=Math.random().toString(36).slice(2),ft="__reactFiber$"+Pn,bt="__reactProps$"+Pn,cl="__reactContainer$"+Pn,Ks="__reactEvents$"+Pn,F0="__reactListeners$"+Pn,Z0="__reactHandles$"+Pn,Ed="__reactResources$"+Pn,xi="__reactMarker$"+Pn;function qs(e){delete e[ft],delete e[bt],delete e[Ks],delete e[F0],delete e[Z0]}function fl(e){var t=e[ft];if(t)return t;for(var a=e.parentNode;a;){if(t=a[cl]||a[ft]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=jg(e);e!==null;){if(a=e[ft])return a;e=jg(e)}return t}e=a,a=e.parentNode}return null}function dl(e){if(e=e[ft]||e[cl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Oi(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function hl(e){var t=e[Ed];return t||(t=e[Ed]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function tt(e){e[xi]=!0}var $d=new Set,Td={};function Ra(e,t){pl(e,t),pl(e+"Capture",t)}function pl(e,t){for(Td[e]=t,e=0;e<t.length;e++)$d.add(t[e])}var J0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),xd={},Od={};function W0(e){return Ls.call(Od,e)?!0:Ls.call(xd,e)?!1:J0.test(e)?Od[e]=!0:(xd[e]=!0,!1)}function Vr(e,t,a){if(W0(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var r=t.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Xr(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function bn(e,t,a,r){if(r===null)e.removeAttribute(a);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+r)}}var Bs,Cd;function gl(e){if(Bs===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);Bs=t&&t[1]||"",Cd=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Bs+e+Cd}var Qs=!1;function Ps(e,t){if(!e||Qs)return"";Qs=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(z){var _=z}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(z){_=z}e.call(Q.prototype)}}else{try{throw Error()}catch(z){_=z}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(z){if(z&&_&&typeof z.stack=="string")return[z.stack,_.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=r.DetermineComponentFrameRoot(),g=c[0],y=c[1];if(g&&y){var S=g.split(`
`),L=y.split(`
`);for(o=r=0;r<S.length&&!S[r].includes("DetermineComponentFrameRoot");)r++;for(;o<L.length&&!L[o].includes("DetermineComponentFrameRoot");)o++;if(r===S.length||o===L.length)for(r=S.length-1,o=L.length-1;1<=r&&0<=o&&S[r]!==L[o];)o--;for(;1<=r&&0<=o;r--,o--)if(S[r]!==L[o]){if(r!==1||o!==1)do if(r--,o--,0>o||S[r]!==L[o]){var j=`
`+S[r].replace(" at new "," at ");return e.displayName&&j.includes("<anonymous>")&&(j=j.replace("<anonymous>",e.displayName)),j}while(1<=r&&0<=o);break}}}finally{Qs=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?gl(a):""}function I0(e){switch(e.tag){case 26:case 27:case 5:return gl(e.type);case 16:return gl("Lazy");case 13:return gl("Suspense");case 19:return gl("SuspenseList");case 0:case 15:return Ps(e.type,!1);case 11:return Ps(e.type.render,!1);case 1:return Ps(e.type,!0);case 31:return gl("Activity");default:return""}}function wd(e){try{var t="";do t+=I0(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Kt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Rd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function eb(e){var t=Rd(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var o=a.get,c=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(g){r=""+g,c.call(this,g)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return r},setValue:function(g){r=""+g},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Fr(e){e._valueTracker||(e._valueTracker=eb(e))}function Ad(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),r="";return e&&(r=Rd(e)?e.checked?"true":"false":e.value),e=r,e!==a?(t.setValue(e),!0):!1}function Zr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var tb=/[\n"\\]/g;function qt(e){return e.replace(tb,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function ks(e,t,a,r,o,c,g,y){e.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.type=g:e.removeAttribute("type"),t!=null?g==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Kt(t)):e.value!==""+Kt(t)&&(e.value=""+Kt(t)):g!=="submit"&&g!=="reset"||e.removeAttribute("value"),t!=null?Gs(e,g,Kt(t)):a!=null?Gs(e,g,Kt(a)):r!=null&&e.removeAttribute("value"),o==null&&c!=null&&(e.defaultChecked=!!c),o!=null&&(e.checked=o&&typeof o!="function"&&typeof o!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+Kt(y):e.removeAttribute("name")}function Md(e,t,a,r,o,c,g,y){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;a=a!=null?""+Kt(a):"",t=t!=null?""+Kt(t):a,y||t===e.value||(e.value=t),e.defaultValue=t}r=r??o,r=typeof r!="function"&&typeof r!="symbol"&&!!r,e.checked=y?e.checked:!!r,e.defaultChecked=!!r,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.name=g)}function Gs(e,t,a){t==="number"&&Zr(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function vl(e,t,a,r){if(e=e.options,t){t={};for(var o=0;o<a.length;o++)t["$"+a[o]]=!0;for(a=0;a<e.length;a++)o=t.hasOwnProperty("$"+e[a].value),e[a].selected!==o&&(e[a].selected=o),o&&r&&(e[a].defaultSelected=!0)}else{for(a=""+Kt(a),t=null,o=0;o<e.length;o++){if(e[o].value===a){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Nd(e,t,a){if(t!=null&&(t=""+Kt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Kt(a):""}function Dd(e,t,a,r){if(t==null){if(r!=null){if(a!=null)throw Error(u(92));if(pe(r)){if(1<r.length)throw Error(u(93));r=r[0]}a=r}a==null&&(a=""),t=a}a=Kt(t),e.defaultValue=a,r=e.textContent,r===a&&r!==""&&r!==null&&(e.value=r)}function yl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var nb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ld(e,t,a){var r=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?r?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":r?e.setProperty(t,a):typeof a!="number"||a===0||nb.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function _d(e,t,a){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,a!=null){for(var r in a)!a.hasOwnProperty(r)||t!=null&&t.hasOwnProperty(r)||(r.indexOf("--")===0?e.setProperty(r,""):r==="float"?e.cssFloat="":e[r]="");for(var o in t)r=t[o],t.hasOwnProperty(o)&&a[o]!==r&&Ld(e,o,r)}else for(var c in t)t.hasOwnProperty(c)&&Ld(e,c,t[c])}function Ys(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ab=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),lb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Jr(e){return lb.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Vs=null;function Xs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ml=null,bl=null;function zd(e){var t=dl(e);if(t&&(e=t.stateNode)){var a=e[bt]||null;e:switch(e=t.stateNode,t.type){case"input":if(ks(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+qt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var r=a[t];if(r!==e&&r.form===e.form){var o=r[bt]||null;if(!o)throw Error(u(90));ks(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<a.length;t++)r=a[t],r.form===e.form&&Ad(r)}break e;case"textarea":Nd(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&vl(e,!!a.multiple,t,!1)}}}var Fs=!1;function Ud(e,t,a){if(Fs)return e(t,a);Fs=!0;try{var r=e(t);return r}finally{if(Fs=!1,(ml!==null||bl!==null)&&(Uu(),ml&&(t=ml,e=bl,bl=ml=null,zd(t),e)))for(t=0;t<e.length;t++)zd(e[t])}}function Ci(e,t){var a=e.stateNode;if(a===null)return null;var r=a[bt]||null;if(r===null)return null;a=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(u(231,t,typeof a));return a}var Sn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Zs=!1;if(Sn)try{var wi={};Object.defineProperty(wi,"passive",{get:function(){Zs=!0}}),window.addEventListener("test",wi,wi),window.removeEventListener("test",wi,wi)}catch{Zs=!1}var kn=null,Js=null,Wr=null;function Hd(){if(Wr)return Wr;var e,t=Js,a=t.length,r,o="value"in kn?kn.value:kn.textContent,c=o.length;for(e=0;e<a&&t[e]===o[e];e++);var g=a-e;for(r=1;r<=g&&t[a-r]===o[c-r];r++);return Wr=o.slice(e,1<r?1-r:void 0)}function Ir(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function eu(){return!0}function jd(){return!1}function St(e){function t(a,r,o,c,g){this._reactName=a,this._targetInst=o,this.type=r,this.nativeEvent=c,this.target=g,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(a=e[y],this[y]=a?a(c):c[y]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?eu:jd,this.isPropagationStopped=jd,this}return m(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=eu)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=eu)},persist:function(){},isPersistent:eu}),t}var Aa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},tu=St(Aa),Ri=m({},Aa,{view:0,detail:0}),ib=St(Ri),Ws,Is,Ai,nu=m({},Ri,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:to,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ai&&(Ai&&e.type==="mousemove"?(Ws=e.screenX-Ai.screenX,Is=e.screenY-Ai.screenY):Is=Ws=0,Ai=e),Ws)},movementY:function(e){return"movementY"in e?e.movementY:Is}}),Kd=St(nu),rb=m({},nu,{dataTransfer:0}),ub=St(rb),sb=m({},Ri,{relatedTarget:0}),eo=St(sb),ob=m({},Aa,{animationName:0,elapsedTime:0,pseudoElement:0}),cb=St(ob),fb=m({},Aa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),db=St(fb),hb=m({},Aa,{data:0}),qd=St(hb),pb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},gb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},vb={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function yb(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=vb[e])?!!t[e]:!1}function to(){return yb}var mb=m({},Ri,{key:function(e){if(e.key){var t=pb[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ir(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?gb[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:to,charCode:function(e){return e.type==="keypress"?Ir(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ir(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),bb=St(mb),Sb=m({},nu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Bd=St(Sb),Eb=m({},Ri,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:to}),$b=St(Eb),Tb=m({},Aa,{propertyName:0,elapsedTime:0,pseudoElement:0}),xb=St(Tb),Ob=m({},nu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Cb=St(Ob),wb=m({},Aa,{newState:0,oldState:0}),Rb=St(wb),Ab=[9,13,27,32],no=Sn&&"CompositionEvent"in window,Mi=null;Sn&&"documentMode"in document&&(Mi=document.documentMode);var Mb=Sn&&"TextEvent"in window&&!Mi,Qd=Sn&&(!no||Mi&&8<Mi&&11>=Mi),Pd=" ",kd=!1;function Gd(e,t){switch(e){case"keyup":return Ab.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sl=!1;function Nb(e,t){switch(e){case"compositionend":return Yd(t);case"keypress":return t.which!==32?null:(kd=!0,Pd);case"textInput":return e=t.data,e===Pd&&kd?null:e;default:return null}}function Db(e,t){if(Sl)return e==="compositionend"||!no&&Gd(e,t)?(e=Hd(),Wr=Js=kn=null,Sl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Qd&&t.locale!=="ko"?null:t.data;default:return null}}var Lb={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Lb[e.type]:t==="textarea"}function Xd(e,t,a,r){ml?bl?bl.push(r):bl=[r]:ml=r,t=Qu(t,"onChange"),0<t.length&&(a=new tu("onChange","change",null,a,r),e.push({event:a,listeners:t}))}var Ni=null,Di=null;function _b(e){Rg(e,0)}function au(e){var t=Oi(e);if(Ad(t))return e}function Fd(e,t){if(e==="change")return t}var Zd=!1;if(Sn){var ao;if(Sn){var lo="oninput"in document;if(!lo){var Jd=document.createElement("div");Jd.setAttribute("oninput","return;"),lo=typeof Jd.oninput=="function"}ao=lo}else ao=!1;Zd=ao&&(!document.documentMode||9<document.documentMode)}function Wd(){Ni&&(Ni.detachEvent("onpropertychange",Id),Di=Ni=null)}function Id(e){if(e.propertyName==="value"&&au(Di)){var t=[];Xd(t,Di,e,Xs(e)),Ud(_b,t)}}function zb(e,t,a){e==="focusin"?(Wd(),Ni=t,Di=a,Ni.attachEvent("onpropertychange",Id)):e==="focusout"&&Wd()}function Ub(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return au(Di)}function Hb(e,t){if(e==="click")return au(t)}function jb(e,t){if(e==="input"||e==="change")return au(t)}function Kb(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Nt=typeof Object.is=="function"?Object.is:Kb;function Li(e,t){if(Nt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),r=Object.keys(t);if(a.length!==r.length)return!1;for(r=0;r<a.length;r++){var o=a[r];if(!Ls.call(t,o)||!Nt(e[o],t[o]))return!1}return!0}function eh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function th(e,t){var a=eh(e);e=0;for(var r;a;){if(a.nodeType===3){if(r=e+a.textContent.length,e<=t&&r>=t)return{node:a,offset:t-e};e=r}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=eh(a)}}function nh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?nh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ah(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Zr(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Zr(e.document)}return t}function io(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var qb=Sn&&"documentMode"in document&&11>=document.documentMode,El=null,ro=null,_i=null,uo=!1;function lh(e,t,a){var r=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;uo||El==null||El!==Zr(r)||(r=El,"selectionStart"in r&&io(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),_i&&Li(_i,r)||(_i=r,r=Qu(ro,"onSelect"),0<r.length&&(t=new tu("onSelect","select",null,t,a),e.push({event:t,listeners:r}),t.target=El)))}function Ma(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var $l={animationend:Ma("Animation","AnimationEnd"),animationiteration:Ma("Animation","AnimationIteration"),animationstart:Ma("Animation","AnimationStart"),transitionrun:Ma("Transition","TransitionRun"),transitionstart:Ma("Transition","TransitionStart"),transitioncancel:Ma("Transition","TransitionCancel"),transitionend:Ma("Transition","TransitionEnd")},so={},ih={};Sn&&(ih=document.createElement("div").style,"AnimationEvent"in window||(delete $l.animationend.animation,delete $l.animationiteration.animation,delete $l.animationstart.animation),"TransitionEvent"in window||delete $l.transitionend.transition);function Na(e){if(so[e])return so[e];if(!$l[e])return e;var t=$l[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in ih)return so[e]=t[a];return e}var rh=Na("animationend"),uh=Na("animationiteration"),sh=Na("animationstart"),Bb=Na("transitionrun"),Qb=Na("transitionstart"),Pb=Na("transitioncancel"),oh=Na("transitionend"),ch=new Map,oo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");oo.push("scrollEnd");function It(e,t){ch.set(e,t),Ra(t,[e])}var fh=new WeakMap;function Bt(e,t){if(typeof e=="object"&&e!==null){var a=fh.get(e);return a!==void 0?a:(t={value:e,source:t,stack:wd(t)},fh.set(e,t),t)}return{value:e,source:t,stack:wd(t)}}var Qt=[],Tl=0,co=0;function lu(){for(var e=Tl,t=co=Tl=0;t<e;){var a=Qt[t];Qt[t++]=null;var r=Qt[t];Qt[t++]=null;var o=Qt[t];Qt[t++]=null;var c=Qt[t];if(Qt[t++]=null,r!==null&&o!==null){var g=r.pending;g===null?o.next=o:(o.next=g.next,g.next=o),r.pending=o}c!==0&&dh(a,o,c)}}function iu(e,t,a,r){Qt[Tl++]=e,Qt[Tl++]=t,Qt[Tl++]=a,Qt[Tl++]=r,co|=r,e.lanes|=r,e=e.alternate,e!==null&&(e.lanes|=r)}function fo(e,t,a,r){return iu(e,t,a,r),ru(e)}function xl(e,t){return iu(e,null,null,t),ru(e)}function dh(e,t,a){e.lanes|=a;var r=e.alternate;r!==null&&(r.lanes|=a);for(var o=!1,c=e.return;c!==null;)c.childLanes|=a,r=c.alternate,r!==null&&(r.childLanes|=a),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(o=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,o&&t!==null&&(o=31-Mt(a),e=c.hiddenUpdates,r=e[o],r===null?e[o]=[t]:r.push(t),t.lane=a|536870912),c):null}function ru(e){if(50<ir)throw ir=0,mc=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Ol={};function kb(e,t,a,r){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dt(e,t,a,r){return new kb(e,t,a,r)}function ho(e){return e=e.prototype,!(!e||!e.isReactComponent)}function En(e,t){var a=e.alternate;return a===null?(a=Dt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function hh(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function uu(e,t,a,r,o,c){var g=0;if(r=e,typeof e=="function")ho(e)&&(g=1);else if(typeof e=="string")g=Y1(e,a,ue.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ae:return e=Dt(31,a,t,o),e.elementType=ae,e.lanes=c,e;case O:return Da(a.children,o,c,t);case R:g=8,o|=24;break;case N:return e=Dt(12,a,t,o|2),e.elementType=N,e.lanes=c,e;case V:return e=Dt(13,a,t,o),e.elementType=V,e.lanes=c,e;case ee:return e=Dt(19,a,t,o),e.elementType=ee,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case P:case k:g=10;break e;case K:g=9;break e;case B:g=11;break e;case re:g=14;break e;case te:g=16,r=null;break e}g=29,a=Error(u(130,e===null?"null":typeof e,"")),r=null}return t=Dt(g,a,t,o),t.elementType=e,t.type=r,t.lanes=c,t}function Da(e,t,a,r){return e=Dt(7,e,r,t),e.lanes=a,e}function po(e,t,a){return e=Dt(6,e,null,t),e.lanes=a,e}function go(e,t,a){return t=Dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Cl=[],wl=0,su=null,ou=0,Pt=[],kt=0,La=null,$n=1,Tn="";function _a(e,t){Cl[wl++]=ou,Cl[wl++]=su,su=e,ou=t}function ph(e,t,a){Pt[kt++]=$n,Pt[kt++]=Tn,Pt[kt++]=La,La=e;var r=$n;e=Tn;var o=32-Mt(r)-1;r&=~(1<<o),a+=1;var c=32-Mt(t)+o;if(30<c){var g=o-o%5;c=(r&(1<<g)-1).toString(32),r>>=g,o-=g,$n=1<<32-Mt(t)+o|a<<o|r,Tn=c+e}else $n=1<<c|a<<o|r,Tn=e}function vo(e){e.return!==null&&(_a(e,1),ph(e,1,0))}function yo(e){for(;e===su;)su=Cl[--wl],Cl[wl]=null,ou=Cl[--wl],Cl[wl]=null;for(;e===La;)La=Pt[--kt],Pt[kt]=null,Tn=Pt[--kt],Pt[kt]=null,$n=Pt[--kt],Pt[kt]=null}var gt=null,Qe=null,we=!1,za=null,rn=!1,mo=Error(u(519));function Ua(e){var t=Error(u(418,""));throw Hi(Bt(t,e)),mo}function gh(e){var t=e.stateNode,a=e.type,r=e.memoizedProps;switch(t[ft]=e,t[bt]=r,a){case"dialog":Te("cancel",t),Te("close",t);break;case"iframe":case"object":case"embed":Te("load",t);break;case"video":case"audio":for(a=0;a<ur.length;a++)Te(ur[a],t);break;case"source":Te("error",t);break;case"img":case"image":case"link":Te("error",t),Te("load",t);break;case"details":Te("toggle",t);break;case"input":Te("invalid",t),Md(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),Fr(t);break;case"select":Te("invalid",t);break;case"textarea":Te("invalid",t),Dd(t,r.value,r.defaultValue,r.children),Fr(t)}a=r.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||r.suppressHydrationWarning===!0||Dg(t.textContent,a)?(r.popover!=null&&(Te("beforetoggle",t),Te("toggle",t)),r.onScroll!=null&&Te("scroll",t),r.onScrollEnd!=null&&Te("scrollend",t),r.onClick!=null&&(t.onclick=Pu),t=!0):t=!1,t||Ua(e)}function vh(e){for(gt=e.return;gt;)switch(gt.tag){case 5:case 13:rn=!1;return;case 27:case 3:rn=!0;return;default:gt=gt.return}}function zi(e){if(e!==gt)return!1;if(!we)return vh(e),we=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||_c(e.type,e.memoizedProps)),a=!a),a&&Qe&&Ua(e),vh(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Qe=tn(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Qe=null}}else t===27?(t=Qe,ra(e.type)?(e=jc,jc=null,Qe=e):Qe=t):Qe=gt?tn(e.stateNode.nextSibling):null;return!0}function Ui(){Qe=gt=null,we=!1}function yh(){var e=za;return e!==null&&(Tt===null?Tt=e:Tt.push.apply(Tt,e),za=null),e}function Hi(e){za===null?za=[e]:za.push(e)}var bo=H(null),Ha=null,xn=null;function Gn(e,t,a){Z(bo,t._currentValue),t._currentValue=a}function On(e){e._currentValue=bo.current,J(bo)}function So(e,t,a){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===a)break;e=e.return}}function Eo(e,t,a,r){var o=e.child;for(o!==null&&(o.return=e);o!==null;){var c=o.dependencies;if(c!==null){var g=o.child;c=c.firstContext;e:for(;c!==null;){var y=c;c=o;for(var S=0;S<t.length;S++)if(y.context===t[S]){c.lanes|=a,y=c.alternate,y!==null&&(y.lanes|=a),So(c.return,a,e),r||(g=null);break e}c=y.next}}else if(o.tag===18){if(g=o.return,g===null)throw Error(u(341));g.lanes|=a,c=g.alternate,c!==null&&(c.lanes|=a),So(g,a,e),g=null}else g=o.child;if(g!==null)g.return=o;else for(g=o;g!==null;){if(g===e){g=null;break}if(o=g.sibling,o!==null){o.return=g.return,g=o;break}g=g.return}o=g}}function ji(e,t,a,r){e=null;for(var o=t,c=!1;o!==null;){if(!c){if((o.flags&524288)!==0)c=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var g=o.alternate;if(g===null)throw Error(u(387));if(g=g.memoizedProps,g!==null){var y=o.type;Nt(o.pendingProps.value,g.value)||(e!==null?e.push(y):e=[y])}}else if(o===ut.current){if(g=o.alternate,g===null)throw Error(u(387));g.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(e!==null?e.push(hr):e=[hr])}o=o.return}e!==null&&Eo(t,e,a,r),t.flags|=262144}function cu(e){for(e=e.firstContext;e!==null;){if(!Nt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ja(e){Ha=e,xn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function dt(e){return mh(Ha,e)}function fu(e,t){return Ha===null&&ja(e),mh(e,t)}function mh(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},xn===null){if(e===null)throw Error(u(308));xn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else xn=xn.next=t;return a}var Gb=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,r){e.push(r)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},Yb=l.unstable_scheduleCallback,Vb=l.unstable_NormalPriority,We={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function $o(){return{controller:new Gb,data:new Map,refCount:0}}function Ki(e){e.refCount--,e.refCount===0&&Yb(Vb,function(){e.controller.abort()})}var qi=null,To=0,Rl=0,Al=null;function Xb(e,t){if(qi===null){var a=qi=[];To=0,Rl=Oc(),Al={status:"pending",value:void 0,then:function(r){a.push(r)}}}return To++,t.then(bh,bh),t}function bh(){if(--To===0&&qi!==null){Al!==null&&(Al.status="fulfilled");var e=qi;qi=null,Rl=0,Al=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Fb(e,t){var a=[],r={status:"pending",value:null,reason:null,then:function(o){a.push(o)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var o=0;o<a.length;o++)(0,a[o])(t)},function(o){for(r.status="rejected",r.reason=o,o=0;o<a.length;o++)(0,a[o])(void 0)}),r}var Sh=C.S;C.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Xb(e,t),Sh!==null&&Sh(e,t)};var Ka=H(null);function xo(){var e=Ka.current;return e!==null?e:He.pooledCache}function du(e,t){t===null?Z(Ka,Ka.current):Z(Ka,t.pool)}function Eh(){var e=xo();return e===null?null:{parent:We._currentValue,pool:e}}var Bi=Error(u(460)),$h=Error(u(474)),hu=Error(u(542)),Oo={then:function(){}};function Th(e){return e=e.status,e==="fulfilled"||e==="rejected"}function pu(){}function xh(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(pu,pu),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Ch(e),e;default:if(typeof t.status=="string")t.then(pu,pu);else{if(e=He,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(r){if(t.status==="pending"){var o=t;o.status="fulfilled",o.value=r}},function(r){if(t.status==="pending"){var o=t;o.status="rejected",o.reason=r}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Ch(e),e}throw Qi=t,Bi}}var Qi=null;function Oh(){if(Qi===null)throw Error(u(459));var e=Qi;return Qi=null,e}function Ch(e){if(e===Bi||e===hu)throw Error(u(483))}var Yn=!1;function Co(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function wo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Vn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Xn(e,t,a){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(Ae&2)!==0){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=ru(e),dh(e,null,a),t}return iu(e,r,t,a),ru(e)}function Pi(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var r=t.lanes;r&=e.pendingLanes,a|=r,t.lanes=a,bd(e,a)}}function Ro(e,t){var a=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,a===r)){var o=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var g={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?o=c=g:c=c.next=g,a=a.next}while(a!==null);c===null?o=c=t:c=c.next=t}else o=c=t;a={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:c,shared:r.shared,callbacks:r.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Ao=!1;function ki(){if(Ao){var e=Al;if(e!==null)throw e}}function Gi(e,t,a,r){Ao=!1;var o=e.updateQueue;Yn=!1;var c=o.firstBaseUpdate,g=o.lastBaseUpdate,y=o.shared.pending;if(y!==null){o.shared.pending=null;var S=y,L=S.next;S.next=null,g===null?c=L:g.next=L,g=S;var j=e.alternate;j!==null&&(j=j.updateQueue,y=j.lastBaseUpdate,y!==g&&(y===null?j.firstBaseUpdate=L:y.next=L,j.lastBaseUpdate=S))}if(c!==null){var Q=o.baseState;g=0,j=L=S=null,y=c;do{var _=y.lane&-536870913,z=_!==y.lane;if(z?(Oe&_)===_:(r&_)===_){_!==0&&_===Rl&&(Ao=!0),j!==null&&(j=j.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var he=e,fe=y;_=t;var Le=a;switch(fe.tag){case 1:if(he=fe.payload,typeof he=="function"){Q=he.call(Le,Q,_);break e}Q=he;break e;case 3:he.flags=he.flags&-65537|128;case 0:if(he=fe.payload,_=typeof he=="function"?he.call(Le,Q,_):he,_==null)break e;Q=m({},Q,_);break e;case 2:Yn=!0}}_=y.callback,_!==null&&(e.flags|=64,z&&(e.flags|=8192),z=o.callbacks,z===null?o.callbacks=[_]:z.push(_))}else z={lane:_,tag:y.tag,payload:y.payload,callback:y.callback,next:null},j===null?(L=j=z,S=Q):j=j.next=z,g|=_;if(y=y.next,y===null){if(y=o.shared.pending,y===null)break;z=y,y=z.next,z.next=null,o.lastBaseUpdate=z,o.shared.pending=null}}while(!0);j===null&&(S=Q),o.baseState=S,o.firstBaseUpdate=L,o.lastBaseUpdate=j,c===null&&(o.shared.lanes=0),na|=g,e.lanes=g,e.memoizedState=Q}}function wh(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function Rh(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)wh(a[e],t)}var Ml=H(null),gu=H(0);function Ah(e,t){e=Dn,Z(gu,e),Z(Ml,t),Dn=e|t.baseLanes}function Mo(){Z(gu,Dn),Z(Ml,Ml.current)}function No(){Dn=gu.current,J(Ml),J(gu)}var Fn=0,be=null,Ne=null,Fe=null,vu=!1,Nl=!1,qa=!1,yu=0,Yi=0,Dl=null,Zb=0;function Ge(){throw Error(u(321))}function Do(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Nt(e[a],t[a]))return!1;return!0}function Lo(e,t,a,r,o,c){return Fn=c,be=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,C.H=e===null||e.memoizedState===null?dp:hp,qa=!1,c=a(r,o),qa=!1,Nl&&(c=Nh(t,a,r,o)),Mh(e),c}function Mh(e){C.H=Tu;var t=Ne!==null&&Ne.next!==null;if(Fn=0,Fe=Ne=be=null,vu=!1,Yi=0,Dl=null,t)throw Error(u(300));e===null||nt||(e=e.dependencies,e!==null&&cu(e)&&(nt=!0))}function Nh(e,t,a,r){be=e;var o=0;do{if(Nl&&(Dl=null),Yi=0,Nl=!1,25<=o)throw Error(u(301));if(o+=1,Fe=Ne=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}C.H=a1,c=t(a,r)}while(Nl);return c}function Jb(){var e=C.H,t=e.useState()[0];return t=typeof t.then=="function"?Vi(t):t,e=e.useState()[0],(Ne!==null?Ne.memoizedState:null)!==e&&(be.flags|=1024),t}function _o(){var e=yu!==0;return yu=0,e}function zo(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Uo(e){if(vu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}vu=!1}Fn=0,Fe=Ne=be=null,Nl=!1,Yi=yu=0,Dl=null}function Et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Fe===null?be.memoizedState=Fe=e:Fe=Fe.next=e,Fe}function Ze(){if(Ne===null){var e=be.alternate;e=e!==null?e.memoizedState:null}else e=Ne.next;var t=Fe===null?be.memoizedState:Fe.next;if(t!==null)Fe=t,Ne=e;else{if(e===null)throw be.alternate===null?Error(u(467)):Error(u(310));Ne=e,e={memoizedState:Ne.memoizedState,baseState:Ne.baseState,baseQueue:Ne.baseQueue,queue:Ne.queue,next:null},Fe===null?be.memoizedState=Fe=e:Fe=Fe.next=e}return Fe}function Ho(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Vi(e){var t=Yi;return Yi+=1,Dl===null&&(Dl=[]),e=xh(Dl,e,t),t=be,(Fe===null?t.memoizedState:Fe.next)===null&&(t=t.alternate,C.H=t===null||t.memoizedState===null?dp:hp),e}function mu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Vi(e);if(e.$$typeof===k)return dt(e)}throw Error(u(438,String(e)))}function jo(e){var t=null,a=be.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var r=be.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(t={data:r.data.map(function(o){return o.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Ho(),be.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),r=0;r<e;r++)a[r]=F;return t.index++,a}function Cn(e,t){return typeof t=="function"?t(e):t}function bu(e){var t=Ze();return Ko(t,Ne,e)}function Ko(e,t,a){var r=e.queue;if(r===null)throw Error(u(311));r.lastRenderedReducer=a;var o=e.baseQueue,c=r.pending;if(c!==null){if(o!==null){var g=o.next;o.next=c.next,c.next=g}t.baseQueue=o=c,r.pending=null}if(c=e.baseState,o===null)e.memoizedState=c;else{t=o.next;var y=g=null,S=null,L=t,j=!1;do{var Q=L.lane&-536870913;if(Q!==L.lane?(Oe&Q)===Q:(Fn&Q)===Q){var _=L.revertLane;if(_===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null}),Q===Rl&&(j=!0);else if((Fn&_)===_){L=L.next,_===Rl&&(j=!0);continue}else Q={lane:0,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},S===null?(y=S=Q,g=c):S=S.next=Q,be.lanes|=_,na|=_;Q=L.action,qa&&a(c,Q),c=L.hasEagerState?L.eagerState:a(c,Q)}else _={lane:Q,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},S===null?(y=S=_,g=c):S=S.next=_,be.lanes|=Q,na|=Q;L=L.next}while(L!==null&&L!==t);if(S===null?g=c:S.next=y,!Nt(c,e.memoizedState)&&(nt=!0,j&&(a=Al,a!==null)))throw a;e.memoizedState=c,e.baseState=g,e.baseQueue=S,r.lastRenderedState=c}return o===null&&(r.lanes=0),[e.memoizedState,r.dispatch]}function qo(e){var t=Ze(),a=t.queue;if(a===null)throw Error(u(311));a.lastRenderedReducer=e;var r=a.dispatch,o=a.pending,c=t.memoizedState;if(o!==null){a.pending=null;var g=o=o.next;do c=e(c,g.action),g=g.next;while(g!==o);Nt(c,t.memoizedState)||(nt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),a.lastRenderedState=c}return[c,r]}function Dh(e,t,a){var r=be,o=Ze(),c=we;if(c){if(a===void 0)throw Error(u(407));a=a()}else a=t();var g=!Nt((Ne||o).memoizedState,a);g&&(o.memoizedState=a,nt=!0),o=o.queue;var y=zh.bind(null,r,o,e);if(Xi(2048,8,y,[e]),o.getSnapshot!==t||g||Fe!==null&&Fe.memoizedState.tag&1){if(r.flags|=2048,Ll(9,Su(),_h.bind(null,r,o,a,t),null),He===null)throw Error(u(349));c||(Fn&124)!==0||Lh(r,t,a)}return a}function Lh(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=be.updateQueue,t===null?(t=Ho(),be.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function _h(e,t,a,r){t.value=a,t.getSnapshot=r,Uh(t)&&Hh(e)}function zh(e,t,a){return a(function(){Uh(t)&&Hh(e)})}function Uh(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Nt(e,a)}catch{return!0}}function Hh(e){var t=xl(e,2);t!==null&&Ht(t,e,2)}function Bo(e){var t=Et();if(typeof e=="function"){var a=e;if(e=a(),qa){Qn(!0);try{a()}finally{Qn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Cn,lastRenderedState:e},t}function jh(e,t,a,r){return e.baseState=a,Ko(e,Ne,typeof r=="function"?r:Cn)}function Wb(e,t,a,r,o){if($u(e))throw Error(u(485));if(e=t.action,e!==null){var c={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){c.listeners.push(g)}};C.T!==null?a(!0):c.isTransition=!1,r(c),a=t.pending,a===null?(c.next=t.pending=c,Kh(t,c)):(c.next=a.next,t.pending=a.next=c)}}function Kh(e,t){var a=t.action,r=t.payload,o=e.state;if(t.isTransition){var c=C.T,g={};C.T=g;try{var y=a(o,r),S=C.S;S!==null&&S(g,y),qh(e,t,y)}catch(L){Qo(e,t,L)}finally{C.T=c}}else try{c=a(o,r),qh(e,t,c)}catch(L){Qo(e,t,L)}}function qh(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(r){Bh(e,t,r)},function(r){return Qo(e,t,r)}):Bh(e,t,a)}function Bh(e,t,a){t.status="fulfilled",t.value=a,Qh(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Kh(e,a)))}function Qo(e,t,a){var r=e.pending;if(e.pending=null,r!==null){r=r.next;do t.status="rejected",t.reason=a,Qh(t),t=t.next;while(t!==r)}e.action=null}function Qh(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Ph(e,t){return t}function kh(e,t){if(we){var a=He.formState;if(a!==null){e:{var r=be;if(we){if(Qe){t:{for(var o=Qe,c=rn;o.nodeType!==8;){if(!c){o=null;break t}if(o=tn(o.nextSibling),o===null){o=null;break t}}c=o.data,o=c==="F!"||c==="F"?o:null}if(o){Qe=tn(o.nextSibling),r=o.data==="F!";break e}}Ua(r)}r=!1}r&&(t=a[0])}}return a=Et(),a.memoizedState=a.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ph,lastRenderedState:t},a.queue=r,a=op.bind(null,be,r),r.dispatch=a,r=Bo(!1),c=Vo.bind(null,be,!1,r.queue),r=Et(),o={state:t,dispatch:null,action:e,pending:null},r.queue=o,a=Wb.bind(null,be,o,c,a),o.dispatch=a,r.memoizedState=e,[t,a,!1]}function Gh(e){var t=Ze();return Yh(t,Ne,e)}function Yh(e,t,a){if(t=Ko(e,t,Ph)[0],e=bu(Cn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var r=Vi(t)}catch(g){throw g===Bi?hu:g}else r=t;t=Ze();var o=t.queue,c=o.dispatch;return a!==t.memoizedState&&(be.flags|=2048,Ll(9,Su(),Ib.bind(null,o,a),null)),[r,c,e]}function Ib(e,t){e.action=t}function Vh(e){var t=Ze(),a=Ne;if(a!==null)return Yh(t,a,e);Ze(),t=t.memoizedState,a=Ze();var r=a.queue.dispatch;return a.memoizedState=e,[t,r,!1]}function Ll(e,t,a,r){return e={tag:e,create:a,deps:r,inst:t,next:null},t=be.updateQueue,t===null&&(t=Ho(),be.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(r=a.next,a.next=e,e.next=r,t.lastEffect=e),e}function Su(){return{destroy:void 0,resource:void 0}}function Xh(){return Ze().memoizedState}function Eu(e,t,a,r){var o=Et();r=r===void 0?null:r,be.flags|=e,o.memoizedState=Ll(1|t,Su(),a,r)}function Xi(e,t,a,r){var o=Ze();r=r===void 0?null:r;var c=o.memoizedState.inst;Ne!==null&&r!==null&&Do(r,Ne.memoizedState.deps)?o.memoizedState=Ll(t,c,a,r):(be.flags|=e,o.memoizedState=Ll(1|t,c,a,r))}function Fh(e,t){Eu(8390656,8,e,t)}function Zh(e,t){Xi(2048,8,e,t)}function Jh(e,t){return Xi(4,2,e,t)}function Wh(e,t){return Xi(4,4,e,t)}function Ih(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ep(e,t,a){a=a!=null?a.concat([e]):null,Xi(4,4,Ih.bind(null,t,e),a)}function Po(){}function tp(e,t){var a=Ze();t=t===void 0?null:t;var r=a.memoizedState;return t!==null&&Do(t,r[1])?r[0]:(a.memoizedState=[e,t],e)}function np(e,t){var a=Ze();t=t===void 0?null:t;var r=a.memoizedState;if(t!==null&&Do(t,r[1]))return r[0];if(r=e(),qa){Qn(!0);try{e()}finally{Qn(!1)}}return a.memoizedState=[r,t],r}function ko(e,t,a){return a===void 0||(Fn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=ig(),be.lanes|=e,na|=e,a)}function ap(e,t,a,r){return Nt(a,t)?a:Ml.current!==null?(e=ko(e,a,r),Nt(e,t)||(nt=!0),e):(Fn&42)===0?(nt=!0,e.memoizedState=a):(e=ig(),be.lanes|=e,na|=e,t)}function lp(e,t,a,r,o){var c=G.p;G.p=c!==0&&8>c?c:8;var g=C.T,y={};C.T=y,Vo(e,!1,t,a);try{var S=o(),L=C.S;if(L!==null&&L(y,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var j=Fb(S,r);Fi(e,t,j,Ut(e))}else Fi(e,t,r,Ut(e))}catch(Q){Fi(e,t,{then:function(){},status:"rejected",reason:Q},Ut())}finally{G.p=c,C.T=g}}function e1(){}function Go(e,t,a,r){if(e.tag!==5)throw Error(u(476));var o=ip(e).queue;lp(e,o,t,W,a===null?e1:function(){return rp(e),a(r)})}function ip(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:W,baseState:W,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Cn,lastRenderedState:W},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Cn,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function rp(e){var t=ip(e).next.queue;Fi(e,t,{},Ut())}function Yo(){return dt(hr)}function up(){return Ze().memoizedState}function sp(){return Ze().memoizedState}function t1(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Ut();e=Vn(a);var r=Xn(t,e,a);r!==null&&(Ht(r,t,a),Pi(r,t,a)),t={cache:$o()},e.payload=t;return}t=t.return}}function n1(e,t,a){var r=Ut();a={lane:r,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},$u(e)?cp(t,a):(a=fo(e,t,a,r),a!==null&&(Ht(a,e,r),fp(a,t,r)))}function op(e,t,a){var r=Ut();Fi(e,t,a,r)}function Fi(e,t,a,r){var o={lane:r,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if($u(e))cp(t,o);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var g=t.lastRenderedState,y=c(g,a);if(o.hasEagerState=!0,o.eagerState=y,Nt(y,g))return iu(e,t,o,0),He===null&&lu(),!1}catch{}finally{}if(a=fo(e,t,o,r),a!==null)return Ht(a,e,r),fp(a,t,r),!0}return!1}function Vo(e,t,a,r){if(r={lane:2,revertLane:Oc(),action:r,hasEagerState:!1,eagerState:null,next:null},$u(e)){if(t)throw Error(u(479))}else t=fo(e,a,r,2),t!==null&&Ht(t,e,2)}function $u(e){var t=e.alternate;return e===be||t!==null&&t===be}function cp(e,t){Nl=vu=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function fp(e,t,a){if((a&4194048)!==0){var r=t.lanes;r&=e.pendingLanes,a|=r,t.lanes=a,bd(e,a)}}var Tu={readContext:dt,use:mu,useCallback:Ge,useContext:Ge,useEffect:Ge,useImperativeHandle:Ge,useLayoutEffect:Ge,useInsertionEffect:Ge,useMemo:Ge,useReducer:Ge,useRef:Ge,useState:Ge,useDebugValue:Ge,useDeferredValue:Ge,useTransition:Ge,useSyncExternalStore:Ge,useId:Ge,useHostTransitionStatus:Ge,useFormState:Ge,useActionState:Ge,useOptimistic:Ge,useMemoCache:Ge,useCacheRefresh:Ge},dp={readContext:dt,use:mu,useCallback:function(e,t){return Et().memoizedState=[e,t===void 0?null:t],e},useContext:dt,useEffect:Fh,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Eu(4194308,4,Ih.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Eu(4194308,4,e,t)},useInsertionEffect:function(e,t){Eu(4,2,e,t)},useMemo:function(e,t){var a=Et();t=t===void 0?null:t;var r=e();if(qa){Qn(!0);try{e()}finally{Qn(!1)}}return a.memoizedState=[r,t],r},useReducer:function(e,t,a){var r=Et();if(a!==void 0){var o=a(t);if(qa){Qn(!0);try{a(t)}finally{Qn(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=n1.bind(null,be,e),[r.memoizedState,e]},useRef:function(e){var t=Et();return e={current:e},t.memoizedState=e},useState:function(e){e=Bo(e);var t=e.queue,a=op.bind(null,be,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Po,useDeferredValue:function(e,t){var a=Et();return ko(a,e,t)},useTransition:function(){var e=Bo(!1);return e=lp.bind(null,be,e.queue,!0,!1),Et().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var r=be,o=Et();if(we){if(a===void 0)throw Error(u(407));a=a()}else{if(a=t(),He===null)throw Error(u(349));(Oe&124)!==0||Lh(r,t,a)}o.memoizedState=a;var c={value:a,getSnapshot:t};return o.queue=c,Fh(zh.bind(null,r,c,e),[e]),r.flags|=2048,Ll(9,Su(),_h.bind(null,r,c,a,t),null),a},useId:function(){var e=Et(),t=He.identifierPrefix;if(we){var a=Tn,r=$n;a=(r&~(1<<32-Mt(r)-1)).toString(32)+a,t="«"+t+"R"+a,a=yu++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Zb++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Yo,useFormState:kh,useActionState:kh,useOptimistic:function(e){var t=Et();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Vo.bind(null,be,!0,a),a.dispatch=t,[e,t]},useMemoCache:jo,useCacheRefresh:function(){return Et().memoizedState=t1.bind(null,be)}},hp={readContext:dt,use:mu,useCallback:tp,useContext:dt,useEffect:Zh,useImperativeHandle:ep,useInsertionEffect:Jh,useLayoutEffect:Wh,useMemo:np,useReducer:bu,useRef:Xh,useState:function(){return bu(Cn)},useDebugValue:Po,useDeferredValue:function(e,t){var a=Ze();return ap(a,Ne.memoizedState,e,t)},useTransition:function(){var e=bu(Cn)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:Vi(e),t]},useSyncExternalStore:Dh,useId:up,useHostTransitionStatus:Yo,useFormState:Gh,useActionState:Gh,useOptimistic:function(e,t){var a=Ze();return jh(a,Ne,e,t)},useMemoCache:jo,useCacheRefresh:sp},a1={readContext:dt,use:mu,useCallback:tp,useContext:dt,useEffect:Zh,useImperativeHandle:ep,useInsertionEffect:Jh,useLayoutEffect:Wh,useMemo:np,useReducer:qo,useRef:Xh,useState:function(){return qo(Cn)},useDebugValue:Po,useDeferredValue:function(e,t){var a=Ze();return Ne===null?ko(a,e,t):ap(a,Ne.memoizedState,e,t)},useTransition:function(){var e=qo(Cn)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:Vi(e),t]},useSyncExternalStore:Dh,useId:up,useHostTransitionStatus:Yo,useFormState:Vh,useActionState:Vh,useOptimistic:function(e,t){var a=Ze();return Ne!==null?jh(a,Ne,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:jo,useCacheRefresh:sp},_l=null,Zi=0;function xu(e){var t=Zi;return Zi+=1,_l===null&&(_l=[]),xh(_l,e,t)}function Ji(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ou(e,t){throw t.$$typeof===b?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function pp(e){var t=e._init;return t(e._payload)}function gp(e){function t(A,w){if(e){var D=A.deletions;D===null?(A.deletions=[w],A.flags|=16):D.push(w)}}function a(A,w){if(!e)return null;for(;w!==null;)t(A,w),w=w.sibling;return null}function r(A){for(var w=new Map;A!==null;)A.key!==null?w.set(A.key,A):w.set(A.index,A),A=A.sibling;return w}function o(A,w){return A=En(A,w),A.index=0,A.sibling=null,A}function c(A,w,D){return A.index=D,e?(D=A.alternate,D!==null?(D=D.index,D<w?(A.flags|=67108866,w):D):(A.flags|=67108866,w)):(A.flags|=1048576,w)}function g(A){return e&&A.alternate===null&&(A.flags|=67108866),A}function y(A,w,D,q){return w===null||w.tag!==6?(w=po(D,A.mode,q),w.return=A,w):(w=o(w,D),w.return=A,w)}function S(A,w,D,q){var ne=D.type;return ne===O?j(A,w,D.props.children,q,D.key):w!==null&&(w.elementType===ne||typeof ne=="object"&&ne!==null&&ne.$$typeof===te&&pp(ne)===w.type)?(w=o(w,D.props),Ji(w,D),w.return=A,w):(w=uu(D.type,D.key,D.props,null,A.mode,q),Ji(w,D),w.return=A,w)}function L(A,w,D,q){return w===null||w.tag!==4||w.stateNode.containerInfo!==D.containerInfo||w.stateNode.implementation!==D.implementation?(w=go(D,A.mode,q),w.return=A,w):(w=o(w,D.children||[]),w.return=A,w)}function j(A,w,D,q,ne){return w===null||w.tag!==7?(w=Da(D,A.mode,q,ne),w.return=A,w):(w=o(w,D),w.return=A,w)}function Q(A,w,D){if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return w=po(""+w,A.mode,D),w.return=A,w;if(typeof w=="object"&&w!==null){switch(w.$$typeof){case $:return D=uu(w.type,w.key,w.props,null,A.mode,D),Ji(D,w),D.return=A,D;case E:return w=go(w,A.mode,D),w.return=A,w;case te:var q=w._init;return w=q(w._payload),Q(A,w,D)}if(pe(w)||X(w))return w=Da(w,A.mode,D,null),w.return=A,w;if(typeof w.then=="function")return Q(A,xu(w),D);if(w.$$typeof===k)return Q(A,fu(A,w),D);Ou(A,w)}return null}function _(A,w,D,q){var ne=w!==null?w.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return ne!==null?null:y(A,w,""+D,q);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case $:return D.key===ne?S(A,w,D,q):null;case E:return D.key===ne?L(A,w,D,q):null;case te:return ne=D._init,D=ne(D._payload),_(A,w,D,q)}if(pe(D)||X(D))return ne!==null?null:j(A,w,D,q,null);if(typeof D.then=="function")return _(A,w,xu(D),q);if(D.$$typeof===k)return _(A,w,fu(A,D),q);Ou(A,D)}return null}function z(A,w,D,q,ne){if(typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint")return A=A.get(D)||null,y(w,A,""+q,ne);if(typeof q=="object"&&q!==null){switch(q.$$typeof){case $:return A=A.get(q.key===null?D:q.key)||null,S(w,A,q,ne);case E:return A=A.get(q.key===null?D:q.key)||null,L(w,A,q,ne);case te:var Ee=q._init;return q=Ee(q._payload),z(A,w,D,q,ne)}if(pe(q)||X(q))return A=A.get(D)||null,j(w,A,q,ne,null);if(typeof q.then=="function")return z(A,w,D,xu(q),ne);if(q.$$typeof===k)return z(A,w,D,fu(w,q),ne);Ou(w,q)}return null}function he(A,w,D,q){for(var ne=null,Ee=null,ie=w,de=w=0,lt=null;ie!==null&&de<D.length;de++){ie.index>de?(lt=ie,ie=null):lt=ie.sibling;var Ce=_(A,ie,D[de],q);if(Ce===null){ie===null&&(ie=lt);break}e&&ie&&Ce.alternate===null&&t(A,ie),w=c(Ce,w,de),Ee===null?ne=Ce:Ee.sibling=Ce,Ee=Ce,ie=lt}if(de===D.length)return a(A,ie),we&&_a(A,de),ne;if(ie===null){for(;de<D.length;de++)ie=Q(A,D[de],q),ie!==null&&(w=c(ie,w,de),Ee===null?ne=ie:Ee.sibling=ie,Ee=ie);return we&&_a(A,de),ne}for(ie=r(ie);de<D.length;de++)lt=z(ie,A,de,D[de],q),lt!==null&&(e&&lt.alternate!==null&&ie.delete(lt.key===null?de:lt.key),w=c(lt,w,de),Ee===null?ne=lt:Ee.sibling=lt,Ee=lt);return e&&ie.forEach(function(fa){return t(A,fa)}),we&&_a(A,de),ne}function fe(A,w,D,q){if(D==null)throw Error(u(151));for(var ne=null,Ee=null,ie=w,de=w=0,lt=null,Ce=D.next();ie!==null&&!Ce.done;de++,Ce=D.next()){ie.index>de?(lt=ie,ie=null):lt=ie.sibling;var fa=_(A,ie,Ce.value,q);if(fa===null){ie===null&&(ie=lt);break}e&&ie&&fa.alternate===null&&t(A,ie),w=c(fa,w,de),Ee===null?ne=fa:Ee.sibling=fa,Ee=fa,ie=lt}if(Ce.done)return a(A,ie),we&&_a(A,de),ne;if(ie===null){for(;!Ce.done;de++,Ce=D.next())Ce=Q(A,Ce.value,q),Ce!==null&&(w=c(Ce,w,de),Ee===null?ne=Ce:Ee.sibling=Ce,Ee=Ce);return we&&_a(A,de),ne}for(ie=r(ie);!Ce.done;de++,Ce=D.next())Ce=z(ie,A,de,Ce.value,q),Ce!==null&&(e&&Ce.alternate!==null&&ie.delete(Ce.key===null?de:Ce.key),w=c(Ce,w,de),Ee===null?ne=Ce:Ee.sibling=Ce,Ee=Ce);return e&&ie.forEach(function(lS){return t(A,lS)}),we&&_a(A,de),ne}function Le(A,w,D,q){if(typeof D=="object"&&D!==null&&D.type===O&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case $:e:{for(var ne=D.key;w!==null;){if(w.key===ne){if(ne=D.type,ne===O){if(w.tag===7){a(A,w.sibling),q=o(w,D.props.children),q.return=A,A=q;break e}}else if(w.elementType===ne||typeof ne=="object"&&ne!==null&&ne.$$typeof===te&&pp(ne)===w.type){a(A,w.sibling),q=o(w,D.props),Ji(q,D),q.return=A,A=q;break e}a(A,w);break}else t(A,w);w=w.sibling}D.type===O?(q=Da(D.props.children,A.mode,q,D.key),q.return=A,A=q):(q=uu(D.type,D.key,D.props,null,A.mode,q),Ji(q,D),q.return=A,A=q)}return g(A);case E:e:{for(ne=D.key;w!==null;){if(w.key===ne)if(w.tag===4&&w.stateNode.containerInfo===D.containerInfo&&w.stateNode.implementation===D.implementation){a(A,w.sibling),q=o(w,D.children||[]),q.return=A,A=q;break e}else{a(A,w);break}else t(A,w);w=w.sibling}q=go(D,A.mode,q),q.return=A,A=q}return g(A);case te:return ne=D._init,D=ne(D._payload),Le(A,w,D,q)}if(pe(D))return he(A,w,D,q);if(X(D)){if(ne=X(D),typeof ne!="function")throw Error(u(150));return D=ne.call(D),fe(A,w,D,q)}if(typeof D.then=="function")return Le(A,w,xu(D),q);if(D.$$typeof===k)return Le(A,w,fu(A,D),q);Ou(A,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,w!==null&&w.tag===6?(a(A,w.sibling),q=o(w,D),q.return=A,A=q):(a(A,w),q=po(D,A.mode,q),q.return=A,A=q),g(A)):a(A,w)}return function(A,w,D,q){try{Zi=0;var ne=Le(A,w,D,q);return _l=null,ne}catch(ie){if(ie===Bi||ie===hu)throw ie;var Ee=Dt(29,ie,null,A.mode);return Ee.lanes=q,Ee.return=A,Ee}finally{}}}var zl=gp(!0),vp=gp(!1),Gt=H(null),un=null;function Zn(e){var t=e.alternate;Z(Ie,Ie.current&1),Z(Gt,e),un===null&&(t===null||Ml.current!==null||t.memoizedState!==null)&&(un=e)}function yp(e){if(e.tag===22){if(Z(Ie,Ie.current),Z(Gt,e),un===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(un=e)}}else Jn()}function Jn(){Z(Ie,Ie.current),Z(Gt,Gt.current)}function wn(e){J(Gt),un===e&&(un=null),J(Ie)}var Ie=H(0);function Cu(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Hc(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Xo(e,t,a,r){t=e.memoizedState,a=a(r,t),a=a==null?t:m({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Fo={enqueueSetState:function(e,t,a){e=e._reactInternals;var r=Ut(),o=Vn(r);o.payload=t,a!=null&&(o.callback=a),t=Xn(e,o,r),t!==null&&(Ht(t,e,r),Pi(t,e,r))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var r=Ut(),o=Vn(r);o.tag=1,o.payload=t,a!=null&&(o.callback=a),t=Xn(e,o,r),t!==null&&(Ht(t,e,r),Pi(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Ut(),r=Vn(a);r.tag=2,t!=null&&(r.callback=t),t=Xn(e,r,a),t!==null&&(Ht(t,e,a),Pi(t,e,a))}};function mp(e,t,a,r,o,c,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,c,g):t.prototype&&t.prototype.isPureReactComponent?!Li(a,r)||!Li(o,c):!0}function bp(e,t,a,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,r),t.state!==e&&Fo.enqueueReplaceState(t,t.state,null)}function Ba(e,t){var a=t;if("ref"in t){a={};for(var r in t)r!=="ref"&&(a[r]=t[r])}if(e=e.defaultProps){a===t&&(a=m({},a));for(var o in e)a[o]===void 0&&(a[o]=e[o])}return a}var wu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Sp(e){wu(e)}function Ep(e){console.error(e)}function $p(e){wu(e)}function Ru(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(r){setTimeout(function(){throw r})}}function Tp(e,t,a){try{var r=e.onCaughtError;r(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function Zo(e,t,a){return a=Vn(a),a.tag=3,a.payload={element:null},a.callback=function(){Ru(e,t)},a}function xp(e){return e=Vn(e),e.tag=3,e}function Op(e,t,a,r){var o=a.type.getDerivedStateFromError;if(typeof o=="function"){var c=r.value;e.payload=function(){return o(c)},e.callback=function(){Tp(t,a,r)}}var g=a.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(e.callback=function(){Tp(t,a,r),typeof o!="function"&&(aa===null?aa=new Set([this]):aa.add(this));var y=r.stack;this.componentDidCatch(r.value,{componentStack:y!==null?y:""})})}function l1(e,t,a,r,o){if(a.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(t=a.alternate,t!==null&&ji(t,a,o,!0),a=Gt.current,a!==null){switch(a.tag){case 13:return un===null?Sc():a.alternate===null&&Pe===0&&(Pe=3),a.flags&=-257,a.flags|=65536,a.lanes=o,r===Oo?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([r]):t.add(r),$c(e,r,o)),!1;case 22:return a.flags|=65536,r===Oo?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([r]):a.add(r)),$c(e,r,o)),!1}throw Error(u(435,a.tag))}return $c(e,r,o),Sc(),!1}if(we)return t=Gt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=o,r!==mo&&(e=Error(u(422),{cause:r}),Hi(Bt(e,a)))):(r!==mo&&(t=Error(u(423),{cause:r}),Hi(Bt(t,a))),e=e.current.alternate,e.flags|=65536,o&=-o,e.lanes|=o,r=Bt(r,a),o=Zo(e.stateNode,r,o),Ro(e,o),Pe!==4&&(Pe=2)),!1;var c=Error(u(520),{cause:r});if(c=Bt(c,a),lr===null?lr=[c]:lr.push(c),Pe!==4&&(Pe=2),t===null)return!0;r=Bt(r,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=o&-o,a.lanes|=e,e=Zo(a.stateNode,r,e),Ro(a,e),!1;case 1:if(t=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(aa===null||!aa.has(c))))return a.flags|=65536,o&=-o,a.lanes|=o,o=xp(o),Op(o,e,a,r),Ro(a,o),!1}a=a.return}while(a!==null);return!1}var Cp=Error(u(461)),nt=!1;function st(e,t,a,r){t.child=e===null?vp(t,null,a,r):zl(t,e.child,a,r)}function wp(e,t,a,r,o){a=a.render;var c=t.ref;if("ref"in r){var g={};for(var y in r)y!=="ref"&&(g[y]=r[y])}else g=r;return ja(t),r=Lo(e,t,a,g,c,o),y=_o(),e!==null&&!nt?(zo(e,t,o),Rn(e,t,o)):(we&&y&&vo(t),t.flags|=1,st(e,t,r,o),t.child)}function Rp(e,t,a,r,o){if(e===null){var c=a.type;return typeof c=="function"&&!ho(c)&&c.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=c,Ap(e,t,c,r,o)):(e=uu(a.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!lc(e,o)){var g=c.memoizedProps;if(a=a.compare,a=a!==null?a:Li,a(g,r)&&e.ref===t.ref)return Rn(e,t,o)}return t.flags|=1,e=En(c,r),e.ref=t.ref,e.return=t,t.child=e}function Ap(e,t,a,r,o){if(e!==null){var c=e.memoizedProps;if(Li(c,r)&&e.ref===t.ref)if(nt=!1,t.pendingProps=r=c,lc(e,o))(e.flags&131072)!==0&&(nt=!0);else return t.lanes=e.lanes,Rn(e,t,o)}return Jo(e,t,a,r,o)}function Mp(e,t,a){var r=t.pendingProps,o=r.children,c=e!==null?e.memoizedState:null;if(r.mode==="hidden"){if((t.flags&128)!==0){if(r=c!==null?c.baseLanes|a:a,e!==null){for(o=t.child=e.child,c=0;o!==null;)c=c|o.lanes|o.childLanes,o=o.sibling;t.childLanes=c&~r}else t.childLanes=0,t.child=null;return Np(e,t,r,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&du(t,c!==null?c.cachePool:null),c!==null?Ah(t,c):Mo(),yp(t);else return t.lanes=t.childLanes=536870912,Np(e,t,c!==null?c.baseLanes|a:a,a)}else c!==null?(du(t,c.cachePool),Ah(t,c),Jn(),t.memoizedState=null):(e!==null&&du(t,null),Mo(),Jn());return st(e,t,o,a),t.child}function Np(e,t,a,r){var o=xo();return o=o===null?null:{parent:We._currentValue,pool:o},t.memoizedState={baseLanes:a,cachePool:o},e!==null&&du(t,null),Mo(),yp(t),e!==null&&ji(e,t,r,!0),null}function Au(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(u(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function Jo(e,t,a,r,o){return ja(t),a=Lo(e,t,a,r,void 0,o),r=_o(),e!==null&&!nt?(zo(e,t,o),Rn(e,t,o)):(we&&r&&vo(t),t.flags|=1,st(e,t,a,o),t.child)}function Dp(e,t,a,r,o,c){return ja(t),t.updateQueue=null,a=Nh(t,r,a,o),Mh(e),r=_o(),e!==null&&!nt?(zo(e,t,c),Rn(e,t,c)):(we&&r&&vo(t),t.flags|=1,st(e,t,a,c),t.child)}function Lp(e,t,a,r,o){if(ja(t),t.stateNode===null){var c=Ol,g=a.contextType;typeof g=="object"&&g!==null&&(c=dt(g)),c=new a(r,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=Fo,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=r,c.state=t.memoizedState,c.refs={},Co(t),g=a.contextType,c.context=typeof g=="object"&&g!==null?dt(g):Ol,c.state=t.memoizedState,g=a.getDerivedStateFromProps,typeof g=="function"&&(Xo(t,a,g,r),c.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(g=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),g!==c.state&&Fo.enqueueReplaceState(c,c.state,null),Gi(t,r,c,o),ki(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),r=!0}else if(e===null){c=t.stateNode;var y=t.memoizedProps,S=Ba(a,y);c.props=S;var L=c.context,j=a.contextType;g=Ol,typeof j=="object"&&j!==null&&(g=dt(j));var Q=a.getDerivedStateFromProps;j=typeof Q=="function"||typeof c.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,j||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y||L!==g)&&bp(t,c,r,g),Yn=!1;var _=t.memoizedState;c.state=_,Gi(t,r,c,o),ki(),L=t.memoizedState,y||_!==L||Yn?(typeof Q=="function"&&(Xo(t,a,Q,r),L=t.memoizedState),(S=Yn||mp(t,a,S,r,_,L,g))?(j||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=L),c.props=r,c.state=L,c.context=g,r=S):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{c=t.stateNode,wo(e,t),g=t.memoizedProps,j=Ba(a,g),c.props=j,Q=t.pendingProps,_=c.context,L=a.contextType,S=Ol,typeof L=="object"&&L!==null&&(S=dt(L)),y=a.getDerivedStateFromProps,(L=typeof y=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(g!==Q||_!==S)&&bp(t,c,r,S),Yn=!1,_=t.memoizedState,c.state=_,Gi(t,r,c,o),ki();var z=t.memoizedState;g!==Q||_!==z||Yn||e!==null&&e.dependencies!==null&&cu(e.dependencies)?(typeof y=="function"&&(Xo(t,a,y,r),z=t.memoizedState),(j=Yn||mp(t,a,j,r,_,z,S)||e!==null&&e.dependencies!==null&&cu(e.dependencies))?(L||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(r,z,S),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(r,z,S)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||g===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=z),c.props=r,c.state=z,c.context=S,r=j):(typeof c.componentDidUpdate!="function"||g===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),r=!1)}return c=r,Au(e,t),r=(t.flags&128)!==0,c||r?(c=t.stateNode,a=r&&typeof a.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&r?(t.child=zl(t,e.child,null,o),t.child=zl(t,null,a,o)):st(e,t,a,o),t.memoizedState=c.state,e=t.child):e=Rn(e,t,o),e}function _p(e,t,a,r){return Ui(),t.flags|=256,st(e,t,a,r),t.child}var Wo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Io(e){return{baseLanes:e,cachePool:Eh()}}function ec(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Yt),e}function zp(e,t,a){var r=t.pendingProps,o=!1,c=(t.flags&128)!==0,g;if((g=c)||(g=e!==null&&e.memoizedState===null?!1:(Ie.current&2)!==0),g&&(o=!0,t.flags&=-129),g=(t.flags&32)!==0,t.flags&=-33,e===null){if(we){if(o?Zn(t):Jn(),we){var y=Qe,S;if(S=y){e:{for(S=y,y=rn;S.nodeType!==8;){if(!y){y=null;break e}if(S=tn(S.nextSibling),S===null){y=null;break e}}y=S}y!==null?(t.memoizedState={dehydrated:y,treeContext:La!==null?{id:$n,overflow:Tn}:null,retryLane:536870912,hydrationErrors:null},S=Dt(18,null,null,0),S.stateNode=y,S.return=t,t.child=S,gt=t,Qe=null,S=!0):S=!1}S||Ua(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return Hc(y)?t.lanes=32:t.lanes=536870912,null;wn(t)}return y=r.children,r=r.fallback,o?(Jn(),o=t.mode,y=Mu({mode:"hidden",children:y},o),r=Da(r,o,a,null),y.return=t,r.return=t,y.sibling=r,t.child=y,o=t.child,o.memoizedState=Io(a),o.childLanes=ec(e,g,a),t.memoizedState=Wo,r):(Zn(t),tc(t,y))}if(S=e.memoizedState,S!==null&&(y=S.dehydrated,y!==null)){if(c)t.flags&256?(Zn(t),t.flags&=-257,t=nc(e,t,a)):t.memoizedState!==null?(Jn(),t.child=e.child,t.flags|=128,t=null):(Jn(),o=r.fallback,y=t.mode,r=Mu({mode:"visible",children:r.children},y),o=Da(o,y,a,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,zl(t,e.child,null,a),r=t.child,r.memoizedState=Io(a),r.childLanes=ec(e,g,a),t.memoizedState=Wo,t=o);else if(Zn(t),Hc(y)){if(g=y.nextSibling&&y.nextSibling.dataset,g)var L=g.dgst;g=L,r=Error(u(419)),r.stack="",r.digest=g,Hi({value:r,source:null,stack:null}),t=nc(e,t,a)}else if(nt||ji(e,t,a,!1),g=(a&e.childLanes)!==0,nt||g){if(g=He,g!==null&&(r=a&-a,r=(r&42)!==0?1:Hs(r),r=(r&(g.suspendedLanes|a))!==0?0:r,r!==0&&r!==S.retryLane))throw S.retryLane=r,xl(e,r),Ht(g,e,r),Cp;y.data==="$?"||Sc(),t=nc(e,t,a)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=S.treeContext,Qe=tn(y.nextSibling),gt=t,we=!0,za=null,rn=!1,e!==null&&(Pt[kt++]=$n,Pt[kt++]=Tn,Pt[kt++]=La,$n=e.id,Tn=e.overflow,La=t),t=tc(t,r.children),t.flags|=4096);return t}return o?(Jn(),o=r.fallback,y=t.mode,S=e.child,L=S.sibling,r=En(S,{mode:"hidden",children:r.children}),r.subtreeFlags=S.subtreeFlags&65011712,L!==null?o=En(L,o):(o=Da(o,y,a,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,y=e.child.memoizedState,y===null?y=Io(a):(S=y.cachePool,S!==null?(L=We._currentValue,S=S.parent!==L?{parent:L,pool:L}:S):S=Eh(),y={baseLanes:y.baseLanes|a,cachePool:S}),o.memoizedState=y,o.childLanes=ec(e,g,a),t.memoizedState=Wo,r):(Zn(t),a=e.child,e=a.sibling,a=En(a,{mode:"visible",children:r.children}),a.return=t,a.sibling=null,e!==null&&(g=t.deletions,g===null?(t.deletions=[e],t.flags|=16):g.push(e)),t.child=a,t.memoizedState=null,a)}function tc(e,t){return t=Mu({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Mu(e,t){return e=Dt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function nc(e,t,a){return zl(t,e.child,null,a),e=tc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Up(e,t,a){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),So(e.return,t,a)}function ac(e,t,a,r,o){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:a,tailMode:o}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=r,c.tail=a,c.tailMode=o)}function Hp(e,t,a){var r=t.pendingProps,o=r.revealOrder,c=r.tail;if(st(e,t,r.children,a),r=Ie.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Up(e,a,t);else if(e.tag===19)Up(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(Z(Ie,r),o){case"forwards":for(a=t.child,o=null;a!==null;)e=a.alternate,e!==null&&Cu(e)===null&&(o=a),a=a.sibling;a=o,a===null?(o=t.child,t.child=null):(o=a.sibling,a.sibling=null),ac(t,!1,o,a,c);break;case"backwards":for(a=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Cu(e)===null){t.child=o;break}e=o.sibling,o.sibling=a,a=o,o=e}ac(t,!0,a,null,c);break;case"together":ac(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Rn(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),na|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(ji(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,a=En(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=En(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function lc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&cu(e)))}function i1(e,t,a){switch(t.tag){case 3:Ue(t,t.stateNode.containerInfo),Gn(t,We,e.memoizedState.cache),Ui();break;case 27:case 5:Ds(t);break;case 4:Ue(t,t.stateNode.containerInfo);break;case 10:Gn(t,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(r!==null)return r.dehydrated!==null?(Zn(t),t.flags|=128,null):(a&t.child.childLanes)!==0?zp(e,t,a):(Zn(t),e=Rn(e,t,a),e!==null?e.sibling:null);Zn(t);break;case 19:var o=(e.flags&128)!==0;if(r=(a&t.childLanes)!==0,r||(ji(e,t,a,!1),r=(a&t.childLanes)!==0),o){if(r)return Hp(e,t,a);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Z(Ie,Ie.current),r)break;return null;case 22:case 23:return t.lanes=0,Mp(e,t,a);case 24:Gn(t,We,e.memoizedState.cache)}return Rn(e,t,a)}function jp(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)nt=!0;else{if(!lc(e,a)&&(t.flags&128)===0)return nt=!1,i1(e,t,a);nt=(e.flags&131072)!==0}else nt=!1,we&&(t.flags&1048576)!==0&&ph(t,ou,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,o=r._init;if(r=o(r._payload),t.type=r,typeof r=="function")ho(r)?(e=Ba(r,e),t.tag=1,t=Lp(null,t,r,e,a)):(t.tag=0,t=Jo(null,t,r,e,a));else{if(r!=null){if(o=r.$$typeof,o===B){t.tag=11,t=wp(null,t,r,e,a);break e}else if(o===re){t.tag=14,t=Rp(null,t,r,e,a);break e}}throw t=Y(r)||r,Error(u(306,t,""))}}return t;case 0:return Jo(e,t,t.type,t.pendingProps,a);case 1:return r=t.type,o=Ba(r,t.pendingProps),Lp(e,t,r,o,a);case 3:e:{if(Ue(t,t.stateNode.containerInfo),e===null)throw Error(u(387));r=t.pendingProps;var c=t.memoizedState;o=c.element,wo(e,t),Gi(t,r,null,a);var g=t.memoizedState;if(r=g.cache,Gn(t,We,r),r!==c.cache&&Eo(t,[We],a,!0),ki(),r=g.element,c.isDehydrated)if(c={element:r,isDehydrated:!1,cache:g.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=_p(e,t,r,a);break e}else if(r!==o){o=Bt(Error(u(424)),t),Hi(o),t=_p(e,t,r,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Qe=tn(e.firstChild),gt=t,we=!0,za=null,rn=!0,a=vp(t,null,r,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Ui(),r===o){t=Rn(e,t,a);break e}st(e,t,r,a)}t=t.child}return t;case 26:return Au(e,t),e===null?(a=Qg(t.type,null,t.pendingProps,null))?t.memoizedState=a:we||(a=t.type,e=t.pendingProps,r=ku(ge.current).createElement(a),r[ft]=t,r[bt]=e,ct(r,a,e),tt(r),t.stateNode=r):t.memoizedState=Qg(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ds(t),e===null&&we&&(r=t.stateNode=Kg(t.type,t.pendingProps,ge.current),gt=t,rn=!0,o=Qe,ra(t.type)?(jc=o,Qe=tn(r.firstChild)):Qe=o),st(e,t,t.pendingProps.children,a),Au(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&we&&((o=r=Qe)&&(r=L1(r,t.type,t.pendingProps,rn),r!==null?(t.stateNode=r,gt=t,Qe=tn(r.firstChild),rn=!1,o=!0):o=!1),o||Ua(t)),Ds(t),o=t.type,c=t.pendingProps,g=e!==null?e.memoizedProps:null,r=c.children,_c(o,c)?r=null:g!==null&&_c(o,g)&&(t.flags|=32),t.memoizedState!==null&&(o=Lo(e,t,Jb,null,null,a),hr._currentValue=o),Au(e,t),st(e,t,r,a),t.child;case 6:return e===null&&we&&((e=a=Qe)&&(a=_1(a,t.pendingProps,rn),a!==null?(t.stateNode=a,gt=t,Qe=null,e=!0):e=!1),e||Ua(t)),null;case 13:return zp(e,t,a);case 4:return Ue(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=zl(t,null,r,a):st(e,t,r,a),t.child;case 11:return wp(e,t,t.type,t.pendingProps,a);case 7:return st(e,t,t.pendingProps,a),t.child;case 8:return st(e,t,t.pendingProps.children,a),t.child;case 12:return st(e,t,t.pendingProps.children,a),t.child;case 10:return r=t.pendingProps,Gn(t,t.type,r.value),st(e,t,r.children,a),t.child;case 9:return o=t.type._context,r=t.pendingProps.children,ja(t),o=dt(o),r=r(o),t.flags|=1,st(e,t,r,a),t.child;case 14:return Rp(e,t,t.type,t.pendingProps,a);case 15:return Ap(e,t,t.type,t.pendingProps,a);case 19:return Hp(e,t,a);case 31:return r=t.pendingProps,a=t.mode,r={mode:r.mode,children:r.children},e===null?(a=Mu(r,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=En(e.child,r),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Mp(e,t,a);case 24:return ja(t),r=dt(We),e===null?(o=xo(),o===null&&(o=He,c=$o(),o.pooledCache=c,c.refCount++,c!==null&&(o.pooledCacheLanes|=a),o=c),t.memoizedState={parent:r,cache:o},Co(t),Gn(t,We,o)):((e.lanes&a)!==0&&(wo(e,t),Gi(t,null,null,a),ki()),o=e.memoizedState,c=t.memoizedState,o.parent!==r?(o={parent:r,cache:r},t.memoizedState=o,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=o),Gn(t,We,r)):(r=c.cache,Gn(t,We,r),r!==o.cache&&Eo(t,[We],a,!0))),st(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function An(e){e.flags|=4}function Kp(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Vg(t)){if(t=Gt.current,t!==null&&((Oe&4194048)===Oe?un!==null:(Oe&62914560)!==Oe&&(Oe&536870912)===0||t!==un))throw Qi=Oo,$h;e.flags|=8192}}function Nu(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?yd():536870912,e.lanes|=t,Kl|=t)}function Wi(e,t){if(!we)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var r=null;a!==null;)a.alternate!==null&&(r=a),a=a.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Be(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,r=0;if(t)for(var o=e.child;o!==null;)a|=o.lanes|o.childLanes,r|=o.subtreeFlags&65011712,r|=o.flags&65011712,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)a|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=a,t}function r1(e,t,a){var r=t.pendingProps;switch(yo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Be(t),null;case 1:return Be(t),null;case 3:return a=t.stateNode,r=null,e!==null&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),On(We),mn(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(zi(t)?An(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,yh())),Be(t),null;case 26:return a=t.memoizedState,e===null?(An(t),a!==null?(Be(t),Kp(t,a)):(Be(t),t.flags&=-16777217)):a?a!==e.memoizedState?(An(t),Be(t),Kp(t,a)):(Be(t),t.flags&=-16777217):(e.memoizedProps!==r&&An(t),Be(t),t.flags&=-16777217),null;case 27:Qr(t),a=ge.current;var o=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==r&&An(t);else{if(!r){if(t.stateNode===null)throw Error(u(166));return Be(t),null}e=ue.current,zi(t)?gh(t):(e=Kg(o,r,a),t.stateNode=e,An(t))}return Be(t),null;case 5:if(Qr(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==r&&An(t);else{if(!r){if(t.stateNode===null)throw Error(u(166));return Be(t),null}if(e=ue.current,zi(t))gh(t);else{switch(o=ku(ge.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof r.is=="string"?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e=typeof r.is=="string"?o.createElement(a,{is:r.is}):o.createElement(a)}}e[ft]=t,e[bt]=r;e:for(o=t.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;o.sibling===null;){if(o.return===null||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(ct(e,a,r),a){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&An(t)}}return Be(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==r&&An(t);else{if(typeof r!="string"&&t.stateNode===null)throw Error(u(166));if(e=ge.current,zi(t)){if(e=t.stateNode,a=t.memoizedProps,r=null,o=gt,o!==null)switch(o.tag){case 27:case 5:r=o.memoizedProps}e[ft]=t,e=!!(e.nodeValue===a||r!==null&&r.suppressHydrationWarning===!0||Dg(e.nodeValue,a)),e||Ua(t)}else e=ku(e).createTextNode(r),e[ft]=t,t.stateNode=e}return Be(t),null;case 13:if(r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(o=zi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(u(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(u(317));o[ft]=t}else Ui(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Be(t),o=!1}else o=yh(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=o),o=!0;if(!o)return t.flags&256?(wn(t),t):(wn(t),null)}if(wn(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=r!==null,e=e!==null&&e.memoizedState!==null,a){r=t.child,o=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(o=r.alternate.memoizedState.cachePool.pool);var c=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(c=r.memoizedState.cachePool.pool),c!==o&&(r.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Nu(t,t.updateQueue),Be(t),null;case 4:return mn(),e===null&&Ac(t.stateNode.containerInfo),Be(t),null;case 10:return On(t.type),Be(t),null;case 19:if(J(Ie),o=t.memoizedState,o===null)return Be(t),null;if(r=(t.flags&128)!==0,c=o.rendering,c===null)if(r)Wi(o,!1);else{if(Pe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Cu(e),c!==null){for(t.flags|=128,Wi(o,!1),e=c.updateQueue,t.updateQueue=e,Nu(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)hh(a,e),a=a.sibling;return Z(Ie,Ie.current&1|2),t.child}e=e.sibling}o.tail!==null&&ln()>_u&&(t.flags|=128,r=!0,Wi(o,!1),t.lanes=4194304)}else{if(!r)if(e=Cu(c),e!==null){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,Nu(t,e),Wi(o,!0),o.tail===null&&o.tailMode==="hidden"&&!c.alternate&&!we)return Be(t),null}else 2*ln()-o.renderingStartTime>_u&&a!==536870912&&(t.flags|=128,r=!0,Wi(o,!1),t.lanes=4194304);o.isBackwards?(c.sibling=t.child,t.child=c):(e=o.last,e!==null?e.sibling=c:t.child=c,o.last=c)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ln(),t.sibling=null,e=Ie.current,Z(Ie,r?e&1|2:e&1),t):(Be(t),null);case 22:case 23:return wn(t),No(),r=t.memoizedState!==null,e!==null?e.memoizedState!==null!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?(a&536870912)!==0&&(t.flags&128)===0&&(Be(t),t.subtreeFlags&6&&(t.flags|=8192)):Be(t),a=t.updateQueue,a!==null&&Nu(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),r=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(r=t.memoizedState.cachePool.pool),r!==a&&(t.flags|=2048),e!==null&&J(Ka),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),On(We),Be(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function u1(e,t){switch(yo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return On(We),mn(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Qr(t),null;case 13:if(wn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));Ui()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(Ie),null;case 4:return mn(),null;case 10:return On(t.type),null;case 22:case 23:return wn(t),No(),e!==null&&J(Ka),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return On(We),null;case 25:return null;default:return null}}function qp(e,t){switch(yo(t),t.tag){case 3:On(We),mn();break;case 26:case 27:case 5:Qr(t);break;case 4:mn();break;case 13:wn(t);break;case 19:J(Ie);break;case 10:On(t.type);break;case 22:case 23:wn(t),No(),e!==null&&J(Ka);break;case 24:On(We)}}function Ii(e,t){try{var a=t.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var o=r.next;a=o;do{if((a.tag&e)===e){r=void 0;var c=a.create,g=a.inst;r=c(),g.destroy=r}a=a.next}while(a!==o)}}catch(y){ze(t,t.return,y)}}function Wn(e,t,a){try{var r=t.updateQueue,o=r!==null?r.lastEffect:null;if(o!==null){var c=o.next;r=c;do{if((r.tag&e)===e){var g=r.inst,y=g.destroy;if(y!==void 0){g.destroy=void 0,o=t;var S=a,L=y;try{L()}catch(j){ze(o,S,j)}}}r=r.next}while(r!==c)}}catch(j){ze(t,t.return,j)}}function Bp(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Rh(t,a)}catch(r){ze(e,e.return,r)}}}function Qp(e,t,a){a.props=Ba(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(r){ze(e,t,r)}}function er(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;case 30:r=e.stateNode;break;default:r=e.stateNode}typeof a=="function"?e.refCleanup=a(r):a.current=r}}catch(o){ze(e,t,o)}}function sn(e,t){var a=e.ref,r=e.refCleanup;if(a!==null)if(typeof r=="function")try{r()}catch(o){ze(e,t,o)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(o){ze(e,t,o)}else a.current=null}function Pp(e){var t=e.type,a=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&r.focus();break e;case"img":a.src?r.src=a.src:a.srcSet&&(r.srcset=a.srcSet)}}catch(o){ze(e,e.return,o)}}function ic(e,t,a){try{var r=e.stateNode;R1(r,e.type,a,t),r[bt]=t}catch(o){ze(e,e.return,o)}}function kp(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ra(e.type)||e.tag===4}function rc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||kp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ra(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function uc(e,t,a){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Pu));else if(r!==4&&(r===27&&ra(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(uc(e,t,a),e=e.sibling;e!==null;)uc(e,t,a),e=e.sibling}function Du(e,t,a){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(r!==4&&(r===27&&ra(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Du(e,t,a),e=e.sibling;e!==null;)Du(e,t,a),e=e.sibling}function Gp(e){var t=e.stateNode,a=e.memoizedProps;try{for(var r=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);ct(t,r,a),t[ft]=e,t[bt]=a}catch(c){ze(e,e.return,c)}}var Mn=!1,Ye=!1,sc=!1,Yp=typeof WeakSet=="function"?WeakSet:Set,at=null;function s1(e,t){if(e=e.containerInfo,Dc=Zu,e=ah(e),io(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var r=a.getSelection&&a.getSelection();if(r&&r.rangeCount!==0){a=r.anchorNode;var o=r.anchorOffset,c=r.focusNode;r=r.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break e}var g=0,y=-1,S=-1,L=0,j=0,Q=e,_=null;t:for(;;){for(var z;Q!==a||o!==0&&Q.nodeType!==3||(y=g+o),Q!==c||r!==0&&Q.nodeType!==3||(S=g+r),Q.nodeType===3&&(g+=Q.nodeValue.length),(z=Q.firstChild)!==null;)_=Q,Q=z;for(;;){if(Q===e)break t;if(_===a&&++L===o&&(y=g),_===c&&++j===r&&(S=g),(z=Q.nextSibling)!==null)break;Q=_,_=Q.parentNode}Q=z}a=y===-1||S===-1?null:{start:y,end:S}}else a=null}a=a||{start:0,end:0}}else a=null;for(Lc={focusedElem:e,selectionRange:a},Zu=!1,at=t;at!==null;)if(t=at,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,at=e;else for(;at!==null;){switch(t=at,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,a=t,o=c.memoizedProps,c=c.memoizedState,r=a.stateNode;try{var he=Ba(a.type,o,a.elementType===a.type);e=r.getSnapshotBeforeUpdate(he,c),r.__reactInternalSnapshotBeforeUpdate=e}catch(fe){ze(a,a.return,fe)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Uc(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Uc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,at=e;break}at=t.return}}function Vp(e,t,a){var r=a.flags;switch(a.tag){case 0:case 11:case 15:In(e,a),r&4&&Ii(5,a);break;case 1:if(In(e,a),r&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(g){ze(a,a.return,g)}else{var o=Ba(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(g){ze(a,a.return,g)}}r&64&&Bp(a),r&512&&er(a,a.return);break;case 3:if(In(e,a),r&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Rh(e,t)}catch(g){ze(a,a.return,g)}}break;case 27:t===null&&r&4&&Gp(a);case 26:case 5:In(e,a),t===null&&r&4&&Pp(a),r&512&&er(a,a.return);break;case 12:In(e,a);break;case 13:In(e,a),r&4&&Zp(e,a),r&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=y1.bind(null,a),z1(e,a))));break;case 22:if(r=a.memoizedState!==null||Mn,!r){t=t!==null&&t.memoizedState!==null||Ye,o=Mn;var c=Ye;Mn=r,(Ye=t)&&!c?ea(e,a,(a.subtreeFlags&8772)!==0):In(e,a),Mn=o,Ye=c}break;case 30:break;default:In(e,a)}}function Xp(e){var t=e.alternate;t!==null&&(e.alternate=null,Xp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&qs(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var qe=null,$t=!1;function Nn(e,t,a){for(a=a.child;a!==null;)Fp(e,t,a),a=a.sibling}function Fp(e,t,a){if(At&&typeof At.onCommitFiberUnmount=="function")try{At.onCommitFiberUnmount(Ei,a)}catch{}switch(a.tag){case 26:Ye||sn(a,t),Nn(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ye||sn(a,t);var r=qe,o=$t;ra(a.type)&&(qe=a.stateNode,$t=!1),Nn(e,t,a),or(a.stateNode),qe=r,$t=o;break;case 5:Ye||sn(a,t);case 6:if(r=qe,o=$t,qe=null,Nn(e,t,a),qe=r,$t=o,qe!==null)if($t)try{(qe.nodeType===9?qe.body:qe.nodeName==="HTML"?qe.ownerDocument.body:qe).removeChild(a.stateNode)}catch(c){ze(a,t,c)}else try{qe.removeChild(a.stateNode)}catch(c){ze(a,t,c)}break;case 18:qe!==null&&($t?(e=qe,Hg(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),yr(e)):Hg(qe,a.stateNode));break;case 4:r=qe,o=$t,qe=a.stateNode.containerInfo,$t=!0,Nn(e,t,a),qe=r,$t=o;break;case 0:case 11:case 14:case 15:Ye||Wn(2,a,t),Ye||Wn(4,a,t),Nn(e,t,a);break;case 1:Ye||(sn(a,t),r=a.stateNode,typeof r.componentWillUnmount=="function"&&Qp(a,t,r)),Nn(e,t,a);break;case 21:Nn(e,t,a);break;case 22:Ye=(r=Ye)||a.memoizedState!==null,Nn(e,t,a),Ye=r;break;default:Nn(e,t,a)}}function Zp(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{yr(e)}catch(a){ze(t,t.return,a)}}function o1(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Yp),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Yp),t;default:throw Error(u(435,e.tag))}}function oc(e,t){var a=o1(e);t.forEach(function(r){var o=m1.bind(null,e,r);a.has(r)||(a.add(r),r.then(o,o))})}function Lt(e,t){var a=t.deletions;if(a!==null)for(var r=0;r<a.length;r++){var o=a[r],c=e,g=t,y=g;e:for(;y!==null;){switch(y.tag){case 27:if(ra(y.type)){qe=y.stateNode,$t=!1;break e}break;case 5:qe=y.stateNode,$t=!1;break e;case 3:case 4:qe=y.stateNode.containerInfo,$t=!0;break e}y=y.return}if(qe===null)throw Error(u(160));Fp(c,g,o),qe=null,$t=!1,c=o.alternate,c!==null&&(c.return=null),o.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Jp(t,e),t=t.sibling}var en=null;function Jp(e,t){var a=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Lt(t,e),_t(e),r&4&&(Wn(3,e,e.return),Ii(3,e),Wn(5,e,e.return));break;case 1:Lt(t,e),_t(e),r&512&&(Ye||a===null||sn(a,a.return)),r&64&&Mn&&(e=e.updateQueue,e!==null&&(r=e.callbacks,r!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?r:a.concat(r))));break;case 26:var o=en;if(Lt(t,e),_t(e),r&512&&(Ye||a===null||sn(a,a.return)),r&4){var c=a!==null?a.memoizedState:null;if(r=e.memoizedState,a===null)if(r===null)if(e.stateNode===null){e:{r=e.type,a=e.memoizedProps,o=o.ownerDocument||o;t:switch(r){case"title":c=o.getElementsByTagName("title")[0],(!c||c[xi]||c[ft]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=o.createElement(r),o.head.insertBefore(c,o.querySelector("head > title"))),ct(c,r,a),c[ft]=e,tt(c),r=c;break e;case"link":var g=Gg("link","href",o).get(r+(a.href||""));if(g){for(var y=0;y<g.length;y++)if(c=g[y],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){g.splice(y,1);break t}}c=o.createElement(r),ct(c,r,a),o.head.appendChild(c);break;case"meta":if(g=Gg("meta","content",o).get(r+(a.content||""))){for(y=0;y<g.length;y++)if(c=g[y],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){g.splice(y,1);break t}}c=o.createElement(r),ct(c,r,a),o.head.appendChild(c);break;default:throw Error(u(468,r))}c[ft]=e,tt(c),r=c}e.stateNode=r}else Yg(o,e.type,e.stateNode);else e.stateNode=kg(o,r,e.memoizedProps);else c!==r?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,r===null?Yg(o,e.type,e.stateNode):kg(o,r,e.memoizedProps)):r===null&&e.stateNode!==null&&ic(e,e.memoizedProps,a.memoizedProps)}break;case 27:Lt(t,e),_t(e),r&512&&(Ye||a===null||sn(a,a.return)),a!==null&&r&4&&ic(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Lt(t,e),_t(e),r&512&&(Ye||a===null||sn(a,a.return)),e.flags&32){o=e.stateNode;try{yl(o,"")}catch(z){ze(e,e.return,z)}}r&4&&e.stateNode!=null&&(o=e.memoizedProps,ic(e,o,a!==null?a.memoizedProps:o)),r&1024&&(sc=!0);break;case 6:if(Lt(t,e),_t(e),r&4){if(e.stateNode===null)throw Error(u(162));r=e.memoizedProps,a=e.stateNode;try{a.nodeValue=r}catch(z){ze(e,e.return,z)}}break;case 3:if(Vu=null,o=en,en=Gu(t.containerInfo),Lt(t,e),en=o,_t(e),r&4&&a!==null&&a.memoizedState.isDehydrated)try{yr(t.containerInfo)}catch(z){ze(e,e.return,z)}sc&&(sc=!1,Wp(e));break;case 4:r=en,en=Gu(e.stateNode.containerInfo),Lt(t,e),_t(e),en=r;break;case 12:Lt(t,e),_t(e);break;case 13:Lt(t,e),_t(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(gc=ln()),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,oc(e,r)));break;case 22:o=e.memoizedState!==null;var S=a!==null&&a.memoizedState!==null,L=Mn,j=Ye;if(Mn=L||o,Ye=j||S,Lt(t,e),Ye=j,Mn=L,_t(e),r&8192)e:for(t=e.stateNode,t._visibility=o?t._visibility&-2:t._visibility|1,o&&(a===null||S||Mn||Ye||Qa(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){S=a=t;try{if(c=S.stateNode,o)g=c.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{y=S.stateNode;var Q=S.memoizedProps.style,_=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;y.style.display=_==null||typeof _=="boolean"?"":(""+_).trim()}}catch(z){ze(S,S.return,z)}}}else if(t.tag===6){if(a===null){S=t;try{S.stateNode.nodeValue=o?"":S.memoizedProps}catch(z){ze(S,S.return,z)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}r&4&&(r=e.updateQueue,r!==null&&(a=r.retryQueue,a!==null&&(r.retryQueue=null,oc(e,a))));break;case 19:Lt(t,e),_t(e),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,oc(e,r)));break;case 30:break;case 21:break;default:Lt(t,e),_t(e)}}function _t(e){var t=e.flags;if(t&2){try{for(var a,r=e.return;r!==null;){if(kp(r)){a=r;break}r=r.return}if(a==null)throw Error(u(160));switch(a.tag){case 27:var o=a.stateNode,c=rc(e);Du(e,c,o);break;case 5:var g=a.stateNode;a.flags&32&&(yl(g,""),a.flags&=-33);var y=rc(e);Du(e,y,g);break;case 3:case 4:var S=a.stateNode.containerInfo,L=rc(e);uc(e,L,S);break;default:throw Error(u(161))}}catch(j){ze(e,e.return,j)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Wp(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Wp(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function In(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Vp(e,t.alternate,t),t=t.sibling}function Qa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Wn(4,t,t.return),Qa(t);break;case 1:sn(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Qp(t,t.return,a),Qa(t);break;case 27:or(t.stateNode);case 26:case 5:sn(t,t.return),Qa(t);break;case 22:t.memoizedState===null&&Qa(t);break;case 30:Qa(t);break;default:Qa(t)}e=e.sibling}}function ea(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var r=t.alternate,o=e,c=t,g=c.flags;switch(c.tag){case 0:case 11:case 15:ea(o,c,a),Ii(4,c);break;case 1:if(ea(o,c,a),r=c,o=r.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(L){ze(r,r.return,L)}if(r=c,o=r.updateQueue,o!==null){var y=r.stateNode;try{var S=o.shared.hiddenCallbacks;if(S!==null)for(o.shared.hiddenCallbacks=null,o=0;o<S.length;o++)wh(S[o],y)}catch(L){ze(r,r.return,L)}}a&&g&64&&Bp(c),er(c,c.return);break;case 27:Gp(c);case 26:case 5:ea(o,c,a),a&&r===null&&g&4&&Pp(c),er(c,c.return);break;case 12:ea(o,c,a);break;case 13:ea(o,c,a),a&&g&4&&Zp(o,c);break;case 22:c.memoizedState===null&&ea(o,c,a),er(c,c.return);break;case 30:break;default:ea(o,c,a)}t=t.sibling}}function cc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Ki(a))}function fc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ki(e))}function on(e,t,a,r){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ip(e,t,a,r),t=t.sibling}function Ip(e,t,a,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:on(e,t,a,r),o&2048&&Ii(9,t);break;case 1:on(e,t,a,r);break;case 3:on(e,t,a,r),o&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ki(e)));break;case 12:if(o&2048){on(e,t,a,r),e=t.stateNode;try{var c=t.memoizedProps,g=c.id,y=c.onPostCommit;typeof y=="function"&&y(g,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){ze(t,t.return,S)}}else on(e,t,a,r);break;case 13:on(e,t,a,r);break;case 23:break;case 22:c=t.stateNode,g=t.alternate,t.memoizedState!==null?c._visibility&2?on(e,t,a,r):tr(e,t):c._visibility&2?on(e,t,a,r):(c._visibility|=2,Ul(e,t,a,r,(t.subtreeFlags&10256)!==0)),o&2048&&cc(g,t);break;case 24:on(e,t,a,r),o&2048&&fc(t.alternate,t);break;default:on(e,t,a,r)}}function Ul(e,t,a,r,o){for(o=o&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,g=t,y=a,S=r,L=g.flags;switch(g.tag){case 0:case 11:case 15:Ul(c,g,y,S,o),Ii(8,g);break;case 23:break;case 22:var j=g.stateNode;g.memoizedState!==null?j._visibility&2?Ul(c,g,y,S,o):tr(c,g):(j._visibility|=2,Ul(c,g,y,S,o)),o&&L&2048&&cc(g.alternate,g);break;case 24:Ul(c,g,y,S,o),o&&L&2048&&fc(g.alternate,g);break;default:Ul(c,g,y,S,o)}t=t.sibling}}function tr(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,r=t,o=r.flags;switch(r.tag){case 22:tr(a,r),o&2048&&cc(r.alternate,r);break;case 24:tr(a,r),o&2048&&fc(r.alternate,r);break;default:tr(a,r)}t=t.sibling}}var nr=8192;function Hl(e){if(e.subtreeFlags&nr)for(e=e.child;e!==null;)eg(e),e=e.sibling}function eg(e){switch(e.tag){case 26:Hl(e),e.flags&nr&&e.memoizedState!==null&&X1(en,e.memoizedState,e.memoizedProps);break;case 5:Hl(e);break;case 3:case 4:var t=en;en=Gu(e.stateNode.containerInfo),Hl(e),en=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=nr,nr=16777216,Hl(e),nr=t):Hl(e));break;default:Hl(e)}}function tg(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ar(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var r=t[a];at=r,ag(r,e)}tg(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ng(e),e=e.sibling}function ng(e){switch(e.tag){case 0:case 11:case 15:ar(e),e.flags&2048&&Wn(9,e,e.return);break;case 3:ar(e);break;case 12:ar(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Lu(e)):ar(e);break;default:ar(e)}}function Lu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var r=t[a];at=r,ag(r,e)}tg(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Wn(8,t,t.return),Lu(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Lu(t));break;default:Lu(t)}e=e.sibling}}function ag(e,t){for(;at!==null;){var a=at;switch(a.tag){case 0:case 11:case 15:Wn(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var r=a.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Ki(a.memoizedState.cache)}if(r=a.child,r!==null)r.return=a,at=r;else e:for(a=e;at!==null;){r=at;var o=r.sibling,c=r.return;if(Xp(r),r===a){at=null;break e}if(o!==null){o.return=c,at=o;break e}at=c}}}var c1={getCacheForType:function(e){var t=dt(We),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},f1=typeof WeakMap=="function"?WeakMap:Map,Ae=0,He=null,$e=null,Oe=0,Me=0,zt=null,ta=!1,jl=!1,dc=!1,Dn=0,Pe=0,na=0,Pa=0,hc=0,Yt=0,Kl=0,lr=null,Tt=null,pc=!1,gc=0,_u=1/0,zu=null,aa=null,ot=0,la=null,ql=null,Bl=0,vc=0,yc=null,lg=null,ir=0,mc=null;function Ut(){if((Ae&2)!==0&&Oe!==0)return Oe&-Oe;if(C.T!==null){var e=Rl;return e!==0?e:Oc()}return Sd()}function ig(){Yt===0&&(Yt=(Oe&536870912)===0||we?vd():536870912);var e=Gt.current;return e!==null&&(e.flags|=32),Yt}function Ht(e,t,a){(e===He&&(Me===2||Me===9)||e.cancelPendingCommit!==null)&&(Ql(e,0),ia(e,Oe,Yt,!1)),Ti(e,a),((Ae&2)===0||e!==He)&&(e===He&&((Ae&2)===0&&(Pa|=a),Pe===4&&ia(e,Oe,Yt,!1)),cn(e))}function rg(e,t,a){if((Ae&6)!==0)throw Error(u(327));var r=!a&&(t&124)===0&&(t&e.expiredLanes)===0||$i(e,t),o=r?p1(e,t):Ec(e,t,!0),c=r;do{if(o===0){jl&&!r&&ia(e,t,0,!1);break}else{if(a=e.current.alternate,c&&!d1(a)){o=Ec(e,t,!1),c=!1;continue}if(o===2){if(c=t,e.errorRecoveryDisabledLanes&c)var g=0;else g=e.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){t=g;e:{var y=e;o=lr;var S=y.current.memoizedState.isDehydrated;if(S&&(Ql(y,g).flags|=256),g=Ec(y,g,!1),g!==2){if(dc&&!S){y.errorRecoveryDisabledLanes|=c,Pa|=c,o=4;break e}c=Tt,Tt=o,c!==null&&(Tt===null?Tt=c:Tt.push.apply(Tt,c))}o=g}if(c=!1,o!==2)continue}}if(o===1){Ql(e,0),ia(e,t,0,!0);break}e:{switch(r=e,c=o,c){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:ia(r,t,Yt,!ta);break e;case 2:Tt=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(o=gc+300-ln(),10<o)){if(ia(r,t,Yt,!ta),Yr(r,0,!0)!==0)break e;r.timeoutHandle=zg(ug.bind(null,r,a,Tt,zu,pc,t,Yt,Pa,Kl,ta,c,2,-0,0),o);break e}ug(r,a,Tt,zu,pc,t,Yt,Pa,Kl,ta,c,0,-0,0)}}break}while(!0);cn(e)}function ug(e,t,a,r,o,c,g,y,S,L,j,Q,_,z){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(dr={stylesheets:null,count:0,unsuspend:V1},eg(t),Q=F1(),Q!==null)){e.cancelPendingCommit=Q(pg.bind(null,e,t,c,a,r,o,g,y,S,j,1,_,z)),ia(e,c,g,!L);return}pg(e,t,c,a,r,o,g,y,S)}function d1(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var r=0;r<a.length;r++){var o=a[r],c=o.getSnapshot;o=o.value;try{if(!Nt(c(),o))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ia(e,t,a,r){t&=~hc,t&=~Pa,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var c=31-Mt(o),g=1<<c;r[c]=-1,o&=~g}a!==0&&md(e,a,t)}function Uu(){return(Ae&6)===0?(rr(0),!1):!0}function bc(){if($e!==null){if(Me===0)var e=$e.return;else e=$e,xn=Ha=null,Uo(e),_l=null,Zi=0,e=$e;for(;e!==null;)qp(e.alternate,e),e=e.return;$e=null}}function Ql(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,M1(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),bc(),He=e,$e=a=En(e.current,null),Oe=t,Me=0,zt=null,ta=!1,jl=$i(e,t),dc=!1,Kl=Yt=hc=Pa=na=Pe=0,Tt=lr=null,pc=!1,(t&8)!==0&&(t|=t&32);var r=e.entangledLanes;if(r!==0)for(e=e.entanglements,r&=t;0<r;){var o=31-Mt(r),c=1<<o;t|=e[o],r&=~c}return Dn=t,lu(),a}function sg(e,t){be=null,C.H=Tu,t===Bi||t===hu?(t=Oh(),Me=3):t===$h?(t=Oh(),Me=4):Me=t===Cp?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,zt=t,$e===null&&(Pe=1,Ru(e,Bt(t,e.current)))}function og(){var e=C.H;return C.H=Tu,e===null?Tu:e}function cg(){var e=C.A;return C.A=c1,e}function Sc(){Pe=4,ta||(Oe&4194048)!==Oe&&Gt.current!==null||(jl=!0),(na&134217727)===0&&(Pa&134217727)===0||He===null||ia(He,Oe,Yt,!1)}function Ec(e,t,a){var r=Ae;Ae|=2;var o=og(),c=cg();(He!==e||Oe!==t)&&(zu=null,Ql(e,t)),t=!1;var g=Pe;e:do try{if(Me!==0&&$e!==null){var y=$e,S=zt;switch(Me){case 8:bc(),g=6;break e;case 3:case 2:case 9:case 6:Gt.current===null&&(t=!0);var L=Me;if(Me=0,zt=null,Pl(e,y,S,L),a&&jl){g=0;break e}break;default:L=Me,Me=0,zt=null,Pl(e,y,S,L)}}h1(),g=Pe;break}catch(j){sg(e,j)}while(!0);return t&&e.shellSuspendCounter++,xn=Ha=null,Ae=r,C.H=o,C.A=c,$e===null&&(He=null,Oe=0,lu()),g}function h1(){for(;$e!==null;)fg($e)}function p1(e,t){var a=Ae;Ae|=2;var r=og(),o=cg();He!==e||Oe!==t?(zu=null,_u=ln()+500,Ql(e,t)):jl=$i(e,t);e:do try{if(Me!==0&&$e!==null){t=$e;var c=zt;t:switch(Me){case 1:Me=0,zt=null,Pl(e,t,c,1);break;case 2:case 9:if(Th(c)){Me=0,zt=null,dg(t);break}t=function(){Me!==2&&Me!==9||He!==e||(Me=7),cn(e)},c.then(t,t);break e;case 3:Me=7;break e;case 4:Me=5;break e;case 7:Th(c)?(Me=0,zt=null,dg(t)):(Me=0,zt=null,Pl(e,t,c,7));break;case 5:var g=null;switch($e.tag){case 26:g=$e.memoizedState;case 5:case 27:var y=$e;if(!g||Vg(g)){Me=0,zt=null;var S=y.sibling;if(S!==null)$e=S;else{var L=y.return;L!==null?($e=L,Hu(L)):$e=null}break t}}Me=0,zt=null,Pl(e,t,c,5);break;case 6:Me=0,zt=null,Pl(e,t,c,6);break;case 8:bc(),Pe=6;break e;default:throw Error(u(462))}}g1();break}catch(j){sg(e,j)}while(!0);return xn=Ha=null,C.H=r,C.A=o,Ae=a,$e!==null?0:(He=null,Oe=0,lu(),Pe)}function g1(){for(;$e!==null&&!H0();)fg($e)}function fg(e){var t=jp(e.alternate,e,Dn);e.memoizedProps=e.pendingProps,t===null?Hu(e):$e=t}function dg(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=Dp(a,t,t.pendingProps,t.type,void 0,Oe);break;case 11:t=Dp(a,t,t.pendingProps,t.type.render,t.ref,Oe);break;case 5:Uo(t);default:qp(a,t),t=$e=hh(t,Dn),t=jp(a,t,Dn)}e.memoizedProps=e.pendingProps,t===null?Hu(e):$e=t}function Pl(e,t,a,r){xn=Ha=null,Uo(t),_l=null,Zi=0;var o=t.return;try{if(l1(e,o,t,a,Oe)){Pe=1,Ru(e,Bt(a,e.current)),$e=null;return}}catch(c){if(o!==null)throw $e=o,c;Pe=1,Ru(e,Bt(a,e.current)),$e=null;return}t.flags&32768?(we||r===1?e=!0:jl||(Oe&536870912)!==0?e=!1:(ta=e=!0,(r===2||r===9||r===3||r===6)&&(r=Gt.current,r!==null&&r.tag===13&&(r.flags|=16384))),hg(t,e)):Hu(t)}function Hu(e){var t=e;do{if((t.flags&32768)!==0){hg(t,ta);return}e=t.return;var a=r1(t.alternate,t,Dn);if(a!==null){$e=a;return}if(t=t.sibling,t!==null){$e=t;return}$e=t=e}while(t!==null);Pe===0&&(Pe=5)}function hg(e,t){do{var a=u1(e.alternate,e);if(a!==null){a.flags&=32767,$e=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){$e=e;return}$e=e=a}while(e!==null);Pe=6,$e=null}function pg(e,t,a,r,o,c,g,y,S){e.cancelPendingCommit=null;do ju();while(ot!==0);if((Ae&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(c=t.lanes|t.childLanes,c|=co,V0(e,a,c,g,y,S),e===He&&($e=He=null,Oe=0),ql=t,la=e,Bl=a,vc=c,yc=o,lg=r,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,b1(Pr,function(){return bg(),null})):(e.callbackNode=null,e.callbackPriority=0),r=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||r){r=C.T,C.T=null,o=G.p,G.p=2,g=Ae,Ae|=4;try{s1(e,t,a)}finally{Ae=g,G.p=o,C.T=r}}ot=1,gg(),vg(),yg()}}function gg(){if(ot===1){ot=0;var e=la,t=ql,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=C.T,C.T=null;var r=G.p;G.p=2;var o=Ae;Ae|=4;try{Jp(t,e);var c=Lc,g=ah(e.containerInfo),y=c.focusedElem,S=c.selectionRange;if(g!==y&&y&&y.ownerDocument&&nh(y.ownerDocument.documentElement,y)){if(S!==null&&io(y)){var L=S.start,j=S.end;if(j===void 0&&(j=L),"selectionStart"in y)y.selectionStart=L,y.selectionEnd=Math.min(j,y.value.length);else{var Q=y.ownerDocument||document,_=Q&&Q.defaultView||window;if(_.getSelection){var z=_.getSelection(),he=y.textContent.length,fe=Math.min(S.start,he),Le=S.end===void 0?fe:Math.min(S.end,he);!z.extend&&fe>Le&&(g=Le,Le=fe,fe=g);var A=th(y,fe),w=th(y,Le);if(A&&w&&(z.rangeCount!==1||z.anchorNode!==A.node||z.anchorOffset!==A.offset||z.focusNode!==w.node||z.focusOffset!==w.offset)){var D=Q.createRange();D.setStart(A.node,A.offset),z.removeAllRanges(),fe>Le?(z.addRange(D),z.extend(w.node,w.offset)):(D.setEnd(w.node,w.offset),z.addRange(D))}}}}for(Q=[],z=y;z=z.parentNode;)z.nodeType===1&&Q.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<Q.length;y++){var q=Q[y];q.element.scrollLeft=q.left,q.element.scrollTop=q.top}}Zu=!!Dc,Lc=Dc=null}finally{Ae=o,G.p=r,C.T=a}}e.current=t,ot=2}}function vg(){if(ot===2){ot=0;var e=la,t=ql,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=C.T,C.T=null;var r=G.p;G.p=2;var o=Ae;Ae|=4;try{Vp(e,t.alternate,t)}finally{Ae=o,G.p=r,C.T=a}}ot=3}}function yg(){if(ot===4||ot===3){ot=0,j0();var e=la,t=ql,a=Bl,r=lg;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ot=5:(ot=0,ql=la=null,mg(e,e.pendingLanes));var o=e.pendingLanes;if(o===0&&(aa=null),js(a),t=t.stateNode,At&&typeof At.onCommitFiberRoot=="function")try{At.onCommitFiberRoot(Ei,t,void 0,(t.current.flags&128)===128)}catch{}if(r!==null){t=C.T,o=G.p,G.p=2,C.T=null;try{for(var c=e.onRecoverableError,g=0;g<r.length;g++){var y=r[g];c(y.value,{componentStack:y.stack})}}finally{C.T=t,G.p=o}}(Bl&3)!==0&&ju(),cn(e),o=e.pendingLanes,(a&4194090)!==0&&(o&42)!==0?e===mc?ir++:(ir=0,mc=e):ir=0,rr(0)}}function mg(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ki(t)))}function ju(e){return gg(),vg(),yg(),bg()}function bg(){if(ot!==5)return!1;var e=la,t=vc;vc=0;var a=js(Bl),r=C.T,o=G.p;try{G.p=32>a?32:a,C.T=null,a=yc,yc=null;var c=la,g=Bl;if(ot=0,ql=la=null,Bl=0,(Ae&6)!==0)throw Error(u(331));var y=Ae;if(Ae|=4,ng(c.current),Ip(c,c.current,g,a),Ae=y,rr(0,!1),At&&typeof At.onPostCommitFiberRoot=="function")try{At.onPostCommitFiberRoot(Ei,c)}catch{}return!0}finally{G.p=o,C.T=r,mg(e,t)}}function Sg(e,t,a){t=Bt(a,t),t=Zo(e.stateNode,t,2),e=Xn(e,t,2),e!==null&&(Ti(e,2),cn(e))}function ze(e,t,a){if(e.tag===3)Sg(e,e,a);else for(;t!==null;){if(t.tag===3){Sg(t,e,a);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(aa===null||!aa.has(r))){e=Bt(a,e),a=xp(2),r=Xn(t,a,2),r!==null&&(Op(a,r,t,e),Ti(r,2),cn(r));break}}t=t.return}}function $c(e,t,a){var r=e.pingCache;if(r===null){r=e.pingCache=new f1;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(a)||(dc=!0,o.add(a),e=v1.bind(null,e,t,a),t.then(e,e))}function v1(e,t,a){var r=e.pingCache;r!==null&&r.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,He===e&&(Oe&a)===a&&(Pe===4||Pe===3&&(Oe&62914560)===Oe&&300>ln()-gc?(Ae&2)===0&&Ql(e,0):hc|=a,Kl===Oe&&(Kl=0)),cn(e)}function Eg(e,t){t===0&&(t=yd()),e=xl(e,t),e!==null&&(Ti(e,t),cn(e))}function y1(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Eg(e,a)}function m1(e,t){var a=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(a=o.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(u(314))}r!==null&&r.delete(t),Eg(e,a)}function b1(e,t){return _s(e,t)}var Ku=null,kl=null,Tc=!1,qu=!1,xc=!1,ka=0;function cn(e){e!==kl&&e.next===null&&(kl===null?Ku=kl=e:kl=kl.next=e),qu=!0,Tc||(Tc=!0,E1())}function rr(e,t){if(!xc&&qu){xc=!0;do for(var a=!1,r=Ku;r!==null;){if(e!==0){var o=r.pendingLanes;if(o===0)var c=0;else{var g=r.suspendedLanes,y=r.pingedLanes;c=(1<<31-Mt(42|e)+1)-1,c&=o&~(g&~y),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,Og(r,c))}else c=Oe,c=Yr(r,r===He?c:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(c&3)===0||$i(r,c)||(a=!0,Og(r,c));r=r.next}while(a);xc=!1}}function S1(){$g()}function $g(){qu=Tc=!1;var e=0;ka!==0&&(A1()&&(e=ka),ka=0);for(var t=ln(),a=null,r=Ku;r!==null;){var o=r.next,c=Tg(r,t);c===0?(r.next=null,a===null?Ku=o:a.next=o,o===null&&(kl=a)):(a=r,(e!==0||(c&3)!==0)&&(qu=!0)),r=o}rr(e)}function Tg(e,t){for(var a=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var g=31-Mt(c),y=1<<g,S=o[g];S===-1?((y&a)===0||(y&r)!==0)&&(o[g]=Y0(y,t)):S<=t&&(e.expiredLanes|=y),c&=~y}if(t=He,a=Oe,a=Yr(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r=e.callbackNode,a===0||e===t&&(Me===2||Me===9)||e.cancelPendingCommit!==null)return r!==null&&r!==null&&zs(r),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||$i(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(r!==null&&zs(r),js(a)){case 2:case 8:a=pd;break;case 32:a=Pr;break;case 268435456:a=gd;break;default:a=Pr}return r=xg.bind(null,e),a=_s(a,r),e.callbackPriority=t,e.callbackNode=a,t}return r!==null&&r!==null&&zs(r),e.callbackPriority=2,e.callbackNode=null,2}function xg(e,t){if(ot!==0&&ot!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(ju()&&e.callbackNode!==a)return null;var r=Oe;return r=Yr(e,e===He?r:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r===0?null:(rg(e,r,t),Tg(e,ln()),e.callbackNode!=null&&e.callbackNode===a?xg.bind(null,e):null)}function Og(e,t){if(ju())return null;rg(e,t,!0)}function E1(){N1(function(){(Ae&6)!==0?_s(hd,S1):$g()})}function Oc(){return ka===0&&(ka=vd()),ka}function Cg(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Jr(""+e)}function wg(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function $1(e,t,a,r,o){if(t==="submit"&&a&&a.stateNode===o){var c=Cg((o[bt]||null).action),g=r.submitter;g&&(t=(t=g[bt]||null)?Cg(t.formAction):g.getAttribute("formAction"),t!==null&&(c=t,g=null));var y=new tu("action","action",null,r,o);e.push({event:y,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(ka!==0){var S=g?wg(o,g):new FormData(o);Go(a,{pending:!0,data:S,method:o.method,action:c},null,S)}}else typeof c=="function"&&(y.preventDefault(),S=g?wg(o,g):new FormData(o),Go(a,{pending:!0,data:S,method:o.method,action:c},c,S))},currentTarget:o}]})}}for(var Cc=0;Cc<oo.length;Cc++){var wc=oo[Cc],T1=wc.toLowerCase(),x1=wc[0].toUpperCase()+wc.slice(1);It(T1,"on"+x1)}It(rh,"onAnimationEnd"),It(uh,"onAnimationIteration"),It(sh,"onAnimationStart"),It("dblclick","onDoubleClick"),It("focusin","onFocus"),It("focusout","onBlur"),It(Bb,"onTransitionRun"),It(Qb,"onTransitionStart"),It(Pb,"onTransitionCancel"),It(oh,"onTransitionEnd"),pl("onMouseEnter",["mouseout","mouseover"]),pl("onMouseLeave",["mouseout","mouseover"]),pl("onPointerEnter",["pointerout","pointerover"]),pl("onPointerLeave",["pointerout","pointerover"]),Ra("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ra("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ra("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ra("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ra("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ra("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ur="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),O1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ur));function Rg(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var r=e[a],o=r.event;r=r.listeners;e:{var c=void 0;if(t)for(var g=r.length-1;0<=g;g--){var y=r[g],S=y.instance,L=y.currentTarget;if(y=y.listener,S!==c&&o.isPropagationStopped())break e;c=y,o.currentTarget=L;try{c(o)}catch(j){wu(j)}o.currentTarget=null,c=S}else for(g=0;g<r.length;g++){if(y=r[g],S=y.instance,L=y.currentTarget,y=y.listener,S!==c&&o.isPropagationStopped())break e;c=y,o.currentTarget=L;try{c(o)}catch(j){wu(j)}o.currentTarget=null,c=S}}}}function Te(e,t){var a=t[Ks];a===void 0&&(a=t[Ks]=new Set);var r=e+"__bubble";a.has(r)||(Ag(t,e,2,!1),a.add(r))}function Rc(e,t,a){var r=0;t&&(r|=4),Ag(a,e,r,t)}var Bu="_reactListening"+Math.random().toString(36).slice(2);function Ac(e){if(!e[Bu]){e[Bu]=!0,$d.forEach(function(a){a!=="selectionchange"&&(O1.has(a)||Rc(a,!1,e),Rc(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Bu]||(t[Bu]=!0,Rc("selectionchange",!1,t))}}function Ag(e,t,a,r){switch(Ig(t)){case 2:var o=W1;break;case 8:o=I1;break;default:o=Pc}a=o.bind(null,t,a,e),o=void 0,!Zs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,a,{capture:!0,passive:o}):e.addEventListener(t,a,!0):o!==void 0?e.addEventListener(t,a,{passive:o}):e.addEventListener(t,a,!1)}function Mc(e,t,a,r,o){var c=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var g=r.tag;if(g===3||g===4){var y=r.stateNode.containerInfo;if(y===o)break;if(g===4)for(g=r.return;g!==null;){var S=g.tag;if((S===3||S===4)&&g.stateNode.containerInfo===o)return;g=g.return}for(;y!==null;){if(g=fl(y),g===null)return;if(S=g.tag,S===5||S===6||S===26||S===27){r=c=g;continue e}y=y.parentNode}}r=r.return}Ud(function(){var L=c,j=Xs(a),Q=[];e:{var _=ch.get(e);if(_!==void 0){var z=tu,he=e;switch(e){case"keypress":if(Ir(a)===0)break e;case"keydown":case"keyup":z=bb;break;case"focusin":he="focus",z=eo;break;case"focusout":he="blur",z=eo;break;case"beforeblur":case"afterblur":z=eo;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=Kd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=ub;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=$b;break;case rh:case uh:case sh:z=cb;break;case oh:z=xb;break;case"scroll":case"scrollend":z=ib;break;case"wheel":z=Cb;break;case"copy":case"cut":case"paste":z=db;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=Bd;break;case"toggle":case"beforetoggle":z=Rb}var fe=(t&4)!==0,Le=!fe&&(e==="scroll"||e==="scrollend"),A=fe?_!==null?_+"Capture":null:_;fe=[];for(var w=L,D;w!==null;){var q=w;if(D=q.stateNode,q=q.tag,q!==5&&q!==26&&q!==27||D===null||A===null||(q=Ci(w,A),q!=null&&fe.push(sr(w,q,D))),Le)break;w=w.return}0<fe.length&&(_=new z(_,he,null,a,j),Q.push({event:_,listeners:fe}))}}if((t&7)===0){e:{if(_=e==="mouseover"||e==="pointerover",z=e==="mouseout"||e==="pointerout",_&&a!==Vs&&(he=a.relatedTarget||a.fromElement)&&(fl(he)||he[cl]))break e;if((z||_)&&(_=j.window===j?j:(_=j.ownerDocument)?_.defaultView||_.parentWindow:window,z?(he=a.relatedTarget||a.toElement,z=L,he=he?fl(he):null,he!==null&&(Le=f(he),fe=he.tag,he!==Le||fe!==5&&fe!==27&&fe!==6)&&(he=null)):(z=null,he=L),z!==he)){if(fe=Kd,q="onMouseLeave",A="onMouseEnter",w="mouse",(e==="pointerout"||e==="pointerover")&&(fe=Bd,q="onPointerLeave",A="onPointerEnter",w="pointer"),Le=z==null?_:Oi(z),D=he==null?_:Oi(he),_=new fe(q,w+"leave",z,a,j),_.target=Le,_.relatedTarget=D,q=null,fl(j)===L&&(fe=new fe(A,w+"enter",he,a,j),fe.target=D,fe.relatedTarget=Le,q=fe),Le=q,z&&he)t:{for(fe=z,A=he,w=0,D=fe;D;D=Gl(D))w++;for(D=0,q=A;q;q=Gl(q))D++;for(;0<w-D;)fe=Gl(fe),w--;for(;0<D-w;)A=Gl(A),D--;for(;w--;){if(fe===A||A!==null&&fe===A.alternate)break t;fe=Gl(fe),A=Gl(A)}fe=null}else fe=null;z!==null&&Mg(Q,_,z,fe,!1),he!==null&&Le!==null&&Mg(Q,Le,he,fe,!0)}}e:{if(_=L?Oi(L):window,z=_.nodeName&&_.nodeName.toLowerCase(),z==="select"||z==="input"&&_.type==="file")var ne=Fd;else if(Vd(_))if(Zd)ne=jb;else{ne=Ub;var Ee=zb}else z=_.nodeName,!z||z.toLowerCase()!=="input"||_.type!=="checkbox"&&_.type!=="radio"?L&&Ys(L.elementType)&&(ne=Fd):ne=Hb;if(ne&&(ne=ne(e,L))){Xd(Q,ne,a,j);break e}Ee&&Ee(e,_,L),e==="focusout"&&L&&_.type==="number"&&L.memoizedProps.value!=null&&Gs(_,"number",_.value)}switch(Ee=L?Oi(L):window,e){case"focusin":(Vd(Ee)||Ee.contentEditable==="true")&&(El=Ee,ro=L,_i=null);break;case"focusout":_i=ro=El=null;break;case"mousedown":uo=!0;break;case"contextmenu":case"mouseup":case"dragend":uo=!1,lh(Q,a,j);break;case"selectionchange":if(qb)break;case"keydown":case"keyup":lh(Q,a,j)}var ie;if(no)e:{switch(e){case"compositionstart":var de="onCompositionStart";break e;case"compositionend":de="onCompositionEnd";break e;case"compositionupdate":de="onCompositionUpdate";break e}de=void 0}else Sl?Gd(e,a)&&(de="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(de="onCompositionStart");de&&(Qd&&a.locale!=="ko"&&(Sl||de!=="onCompositionStart"?de==="onCompositionEnd"&&Sl&&(ie=Hd()):(kn=j,Js="value"in kn?kn.value:kn.textContent,Sl=!0)),Ee=Qu(L,de),0<Ee.length&&(de=new qd(de,e,null,a,j),Q.push({event:de,listeners:Ee}),ie?de.data=ie:(ie=Yd(a),ie!==null&&(de.data=ie)))),(ie=Mb?Nb(e,a):Db(e,a))&&(de=Qu(L,"onBeforeInput"),0<de.length&&(Ee=new qd("onBeforeInput","beforeinput",null,a,j),Q.push({event:Ee,listeners:de}),Ee.data=ie)),$1(Q,e,L,a,j)}Rg(Q,t)})}function sr(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Qu(e,t){for(var a=t+"Capture",r=[];e!==null;){var o=e,c=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||c===null||(o=Ci(e,a),o!=null&&r.unshift(sr(e,o,c)),o=Ci(e,t),o!=null&&r.push(sr(e,o,c))),e.tag===3)return r;e=e.return}return[]}function Gl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Mg(e,t,a,r,o){for(var c=t._reactName,g=[];a!==null&&a!==r;){var y=a,S=y.alternate,L=y.stateNode;if(y=y.tag,S!==null&&S===r)break;y!==5&&y!==26&&y!==27||L===null||(S=L,o?(L=Ci(a,c),L!=null&&g.unshift(sr(a,L,S))):o||(L=Ci(a,c),L!=null&&g.push(sr(a,L,S)))),a=a.return}g.length!==0&&e.push({event:t,listeners:g})}var C1=/\r\n?/g,w1=/\u0000|\uFFFD/g;function Ng(e){return(typeof e=="string"?e:""+e).replace(C1,`
`).replace(w1,"")}function Dg(e,t){return t=Ng(t),Ng(e)===t}function Pu(){}function De(e,t,a,r,o,c){switch(a){case"children":typeof r=="string"?t==="body"||t==="textarea"&&r===""||yl(e,r):(typeof r=="number"||typeof r=="bigint")&&t!=="body"&&yl(e,""+r);break;case"className":Xr(e,"class",r);break;case"tabIndex":Xr(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":Xr(e,a,r);break;case"style":_d(e,r,c);break;case"data":if(t!=="object"){Xr(e,"data",r);break}case"src":case"href":if(r===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(a);break}r=Jr(""+r),e.setAttribute(a,r);break;case"action":case"formAction":if(typeof r=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(t!=="input"&&De(e,t,"name",o.name,o,null),De(e,t,"formEncType",o.formEncType,o,null),De(e,t,"formMethod",o.formMethod,o,null),De(e,t,"formTarget",o.formTarget,o,null)):(De(e,t,"encType",o.encType,o,null),De(e,t,"method",o.method,o,null),De(e,t,"target",o.target,o,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(a);break}r=Jr(""+r),e.setAttribute(a,r);break;case"onClick":r!=null&&(e.onclick=Pu);break;case"onScroll":r!=null&&Te("scroll",e);break;case"onScrollEnd":r!=null&&Te("scrollend",e);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(u(61));if(a=r.__html,a!=null){if(o.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"multiple":e.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":e.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){e.removeAttribute("xlink:href");break}a=Jr(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(a,""+r):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":r===!0?e.setAttribute(a,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(a,r):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?e.setAttribute(a,r):e.removeAttribute(a);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?e.removeAttribute(a):e.setAttribute(a,r);break;case"popover":Te("beforetoggle",e),Te("toggle",e),Vr(e,"popover",r);break;case"xlinkActuate":bn(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":bn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":bn(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":bn(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":bn(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":bn(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":bn(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":bn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":bn(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":Vr(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=ab.get(a)||a,Vr(e,a,r))}}function Nc(e,t,a,r,o,c){switch(a){case"style":_d(e,r,c);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(u(61));if(a=r.__html,a!=null){if(o.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"children":typeof r=="string"?yl(e,r):(typeof r=="number"||typeof r=="bigint")&&yl(e,""+r);break;case"onScroll":r!=null&&Te("scroll",e);break;case"onScrollEnd":r!=null&&Te("scrollend",e);break;case"onClick":r!=null&&(e.onclick=Pu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Td.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(o=a.endsWith("Capture"),t=a.slice(2,o?a.length-7:void 0),c=e[bt]||null,c=c!=null?c[a]:null,typeof c=="function"&&e.removeEventListener(t,c,o),typeof r=="function")){typeof c!="function"&&c!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,r,o);break e}a in e?e[a]=r:r===!0?e.setAttribute(a,""):Vr(e,a,r)}}}function ct(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Te("error",e),Te("load",e);var r=!1,o=!1,c;for(c in a)if(a.hasOwnProperty(c)){var g=a[c];if(g!=null)switch(c){case"src":r=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:De(e,t,c,g,a,null)}}o&&De(e,t,"srcSet",a.srcSet,a,null),r&&De(e,t,"src",a.src,a,null);return;case"input":Te("invalid",e);var y=c=g=o=null,S=null,L=null;for(r in a)if(a.hasOwnProperty(r)){var j=a[r];if(j!=null)switch(r){case"name":o=j;break;case"type":g=j;break;case"checked":S=j;break;case"defaultChecked":L=j;break;case"value":c=j;break;case"defaultValue":y=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(u(137,t));break;default:De(e,t,r,j,a,null)}}Md(e,c,y,S,L,g,o,!1),Fr(e);return;case"select":Te("invalid",e),r=g=c=null;for(o in a)if(a.hasOwnProperty(o)&&(y=a[o],y!=null))switch(o){case"value":c=y;break;case"defaultValue":g=y;break;case"multiple":r=y;default:De(e,t,o,y,a,null)}t=c,a=g,e.multiple=!!r,t!=null?vl(e,!!r,t,!1):a!=null&&vl(e,!!r,a,!0);return;case"textarea":Te("invalid",e),c=o=r=null;for(g in a)if(a.hasOwnProperty(g)&&(y=a[g],y!=null))switch(g){case"value":r=y;break;case"defaultValue":o=y;break;case"children":c=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(u(91));break;default:De(e,t,g,y,a,null)}Dd(e,r,o,c),Fr(e);return;case"option":for(S in a)if(a.hasOwnProperty(S)&&(r=a[S],r!=null))switch(S){case"selected":e.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:De(e,t,S,r,a,null)}return;case"dialog":Te("beforetoggle",e),Te("toggle",e),Te("cancel",e),Te("close",e);break;case"iframe":case"object":Te("load",e);break;case"video":case"audio":for(r=0;r<ur.length;r++)Te(ur[r],e);break;case"image":Te("error",e),Te("load",e);break;case"details":Te("toggle",e);break;case"embed":case"source":case"link":Te("error",e),Te("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(L in a)if(a.hasOwnProperty(L)&&(r=a[L],r!=null))switch(L){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:De(e,t,L,r,a,null)}return;default:if(Ys(t)){for(j in a)a.hasOwnProperty(j)&&(r=a[j],r!==void 0&&Nc(e,t,j,r,a,void 0));return}}for(y in a)a.hasOwnProperty(y)&&(r=a[y],r!=null&&De(e,t,y,r,a,null))}function R1(e,t,a,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,c=null,g=null,y=null,S=null,L=null,j=null;for(z in a){var Q=a[z];if(a.hasOwnProperty(z)&&Q!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":S=Q;default:r.hasOwnProperty(z)||De(e,t,z,null,r,Q)}}for(var _ in r){var z=r[_];if(Q=a[_],r.hasOwnProperty(_)&&(z!=null||Q!=null))switch(_){case"type":c=z;break;case"name":o=z;break;case"checked":L=z;break;case"defaultChecked":j=z;break;case"value":g=z;break;case"defaultValue":y=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(u(137,t));break;default:z!==Q&&De(e,t,_,z,r,Q)}}ks(e,g,y,S,L,j,c,o);return;case"select":z=g=y=_=null;for(c in a)if(S=a[c],a.hasOwnProperty(c)&&S!=null)switch(c){case"value":break;case"multiple":z=S;default:r.hasOwnProperty(c)||De(e,t,c,null,r,S)}for(o in r)if(c=r[o],S=a[o],r.hasOwnProperty(o)&&(c!=null||S!=null))switch(o){case"value":_=c;break;case"defaultValue":y=c;break;case"multiple":g=c;default:c!==S&&De(e,t,o,c,r,S)}t=y,a=g,r=z,_!=null?vl(e,!!a,_,!1):!!r!=!!a&&(t!=null?vl(e,!!a,t,!0):vl(e,!!a,a?[]:"",!1));return;case"textarea":z=_=null;for(y in a)if(o=a[y],a.hasOwnProperty(y)&&o!=null&&!r.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:De(e,t,y,null,r,o)}for(g in r)if(o=r[g],c=a[g],r.hasOwnProperty(g)&&(o!=null||c!=null))switch(g){case"value":_=o;break;case"defaultValue":z=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(u(91));break;default:o!==c&&De(e,t,g,o,r,c)}Nd(e,_,z);return;case"option":for(var he in a)if(_=a[he],a.hasOwnProperty(he)&&_!=null&&!r.hasOwnProperty(he))switch(he){case"selected":e.selected=!1;break;default:De(e,t,he,null,r,_)}for(S in r)if(_=r[S],z=a[S],r.hasOwnProperty(S)&&_!==z&&(_!=null||z!=null))switch(S){case"selected":e.selected=_&&typeof _!="function"&&typeof _!="symbol";break;default:De(e,t,S,_,r,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var fe in a)_=a[fe],a.hasOwnProperty(fe)&&_!=null&&!r.hasOwnProperty(fe)&&De(e,t,fe,null,r,_);for(L in r)if(_=r[L],z=a[L],r.hasOwnProperty(L)&&_!==z&&(_!=null||z!=null))switch(L){case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(u(137,t));break;default:De(e,t,L,_,r,z)}return;default:if(Ys(t)){for(var Le in a)_=a[Le],a.hasOwnProperty(Le)&&_!==void 0&&!r.hasOwnProperty(Le)&&Nc(e,t,Le,void 0,r,_);for(j in r)_=r[j],z=a[j],!r.hasOwnProperty(j)||_===z||_===void 0&&z===void 0||Nc(e,t,j,_,r,z);return}}for(var A in a)_=a[A],a.hasOwnProperty(A)&&_!=null&&!r.hasOwnProperty(A)&&De(e,t,A,null,r,_);for(Q in r)_=r[Q],z=a[Q],!r.hasOwnProperty(Q)||_===z||_==null&&z==null||De(e,t,Q,_,r,z)}var Dc=null,Lc=null;function ku(e){return e.nodeType===9?e:e.ownerDocument}function Lg(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function _g(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function _c(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var zc=null;function A1(){var e=window.event;return e&&e.type==="popstate"?e===zc?!1:(zc=e,!0):(zc=null,!1)}var zg=typeof setTimeout=="function"?setTimeout:void 0,M1=typeof clearTimeout=="function"?clearTimeout:void 0,Ug=typeof Promise=="function"?Promise:void 0,N1=typeof queueMicrotask=="function"?queueMicrotask:typeof Ug<"u"?function(e){return Ug.resolve(null).then(e).catch(D1)}:zg;function D1(e){setTimeout(function(){throw e})}function ra(e){return e==="head"}function Hg(e,t){var a=t,r=0,o=0;do{var c=a.nextSibling;if(e.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<r&&8>r){a=r;var g=e.ownerDocument;if(a&1&&or(g.documentElement),a&2&&or(g.body),a&4)for(a=g.head,or(a),g=a.firstChild;g;){var y=g.nextSibling,S=g.nodeName;g[xi]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&g.rel.toLowerCase()==="stylesheet"||a.removeChild(g),g=y}}if(o===0){e.removeChild(c),yr(t);return}o--}else a==="$"||a==="$?"||a==="$!"?o++:r=a.charCodeAt(0)-48;else r=0;a=c}while(a);yr(t)}function Uc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Uc(a),qs(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function L1(e,t,a,r){for(;e.nodeType===1;){var o=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(r){if(!e[xi])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==o.rel||e.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||e.getAttribute("title")!==(o.title==null?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(o.src==null?null:o.src)||e.getAttribute("type")!==(o.type==null?null:o.type)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=o.name==null?null:""+o.name;if(o.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=tn(e.nextSibling),e===null)break}return null}function _1(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=tn(e.nextSibling),e===null))return null;return e}function Hc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function z1(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var r=function(){t(),a.removeEventListener("DOMContentLoaded",r)};a.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}function tn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var jc=null;function jg(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Kg(e,t,a){switch(t=ku(a),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function or(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);qs(e)}var Vt=new Map,qg=new Set;function Gu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Ln=G.d;G.d={f:U1,r:H1,D:j1,C:K1,L:q1,m:B1,X:P1,S:Q1,M:k1};function U1(){var e=Ln.f(),t=Uu();return e||t}function H1(e){var t=dl(e);t!==null&&t.tag===5&&t.type==="form"?rp(t):Ln.r(e)}var Yl=typeof document>"u"?null:document;function Bg(e,t,a){var r=Yl;if(r&&typeof t=="string"&&t){var o=qt(t);o='link[rel="'+e+'"][href="'+o+'"]',typeof a=="string"&&(o+='[crossorigin="'+a+'"]'),qg.has(o)||(qg.add(o),e={rel:e,crossOrigin:a,href:t},r.querySelector(o)===null&&(t=r.createElement("link"),ct(t,"link",e),tt(t),r.head.appendChild(t)))}}function j1(e){Ln.D(e),Bg("dns-prefetch",e,null)}function K1(e,t){Ln.C(e,t),Bg("preconnect",e,t)}function q1(e,t,a){Ln.L(e,t,a);var r=Yl;if(r&&e&&t){var o='link[rel="preload"][as="'+qt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(o+='[imagesrcset="'+qt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(o+='[imagesizes="'+qt(a.imageSizes)+'"]')):o+='[href="'+qt(e)+'"]';var c=o;switch(t){case"style":c=Vl(e);break;case"script":c=Xl(e)}Vt.has(c)||(e=m({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Vt.set(c,e),r.querySelector(o)!==null||t==="style"&&r.querySelector(cr(c))||t==="script"&&r.querySelector(fr(c))||(t=r.createElement("link"),ct(t,"link",e),tt(t),r.head.appendChild(t)))}}function B1(e,t){Ln.m(e,t);var a=Yl;if(a&&e){var r=t&&typeof t.as=="string"?t.as:"script",o='link[rel="modulepreload"][as="'+qt(r)+'"][href="'+qt(e)+'"]',c=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Xl(e)}if(!Vt.has(c)&&(e=m({rel:"modulepreload",href:e},t),Vt.set(c,e),a.querySelector(o)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(fr(c)))return}r=a.createElement("link"),ct(r,"link",e),tt(r),a.head.appendChild(r)}}}function Q1(e,t,a){Ln.S(e,t,a);var r=Yl;if(r&&e){var o=hl(r).hoistableStyles,c=Vl(e);t=t||"default";var g=o.get(c);if(!g){var y={loading:0,preload:null};if(g=r.querySelector(cr(c)))y.loading=5;else{e=m({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Vt.get(c))&&Kc(e,a);var S=g=r.createElement("link");tt(S),ct(S,"link",e),S._p=new Promise(function(L,j){S.onload=L,S.onerror=j}),S.addEventListener("load",function(){y.loading|=1}),S.addEventListener("error",function(){y.loading|=2}),y.loading|=4,Yu(g,t,r)}g={type:"stylesheet",instance:g,count:1,state:y},o.set(c,g)}}}function P1(e,t){Ln.X(e,t);var a=Yl;if(a&&e){var r=hl(a).hoistableScripts,o=Xl(e),c=r.get(o);c||(c=a.querySelector(fr(o)),c||(e=m({src:e,async:!0},t),(t=Vt.get(o))&&qc(e,t),c=a.createElement("script"),tt(c),ct(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function k1(e,t){Ln.M(e,t);var a=Yl;if(a&&e){var r=hl(a).hoistableScripts,o=Xl(e),c=r.get(o);c||(c=a.querySelector(fr(o)),c||(e=m({src:e,async:!0,type:"module"},t),(t=Vt.get(o))&&qc(e,t),c=a.createElement("script"),tt(c),ct(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function Qg(e,t,a,r){var o=(o=ge.current)?Gu(o):null;if(!o)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Vl(a.href),a=hl(o).hoistableStyles,r=a.get(t),r||(r={type:"style",instance:null,count:0,state:null},a.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Vl(a.href);var c=hl(o).hoistableStyles,g=c.get(e);if(g||(o=o.ownerDocument||o,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,g),(c=o.querySelector(cr(e)))&&!c._p&&(g.instance=c,g.state.loading=5),Vt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Vt.set(e,a),c||G1(o,e,a,g.state))),t&&r===null)throw Error(u(528,""));return g}if(t&&r!==null)throw Error(u(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Xl(a),a=hl(o).hoistableScripts,r=a.get(t),r||(r={type:"script",instance:null,count:0,state:null},a.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function Vl(e){return'href="'+qt(e)+'"'}function cr(e){return'link[rel="stylesheet"]['+e+"]"}function Pg(e){return m({},e,{"data-precedence":e.precedence,precedence:null})}function G1(e,t,a,r){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?r.loading=1:(t=e.createElement("link"),r.preload=t,t.addEventListener("load",function(){return r.loading|=1}),t.addEventListener("error",function(){return r.loading|=2}),ct(t,"link",a),tt(t),e.head.appendChild(t))}function Xl(e){return'[src="'+qt(e)+'"]'}function fr(e){return"script[async]"+e}function kg(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+qt(a.href)+'"]');if(r)return t.instance=r,tt(r),r;var o=m({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return r=(e.ownerDocument||e).createElement("style"),tt(r),ct(r,"style",o),Yu(r,a.precedence,e),t.instance=r;case"stylesheet":o=Vl(a.href);var c=e.querySelector(cr(o));if(c)return t.state.loading|=4,t.instance=c,tt(c),c;r=Pg(a),(o=Vt.get(o))&&Kc(r,o),c=(e.ownerDocument||e).createElement("link"),tt(c);var g=c;return g._p=new Promise(function(y,S){g.onload=y,g.onerror=S}),ct(c,"link",r),t.state.loading|=4,Yu(c,a.precedence,e),t.instance=c;case"script":return c=Xl(a.src),(o=e.querySelector(fr(c)))?(t.instance=o,tt(o),o):(r=a,(o=Vt.get(c))&&(r=m({},a),qc(r,o)),e=e.ownerDocument||e,o=e.createElement("script"),tt(o),ct(o,"link",r),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(r=t.instance,t.state.loading|=4,Yu(r,a.precedence,e));return t.instance}function Yu(e,t,a){for(var r=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,c=o,g=0;g<r.length;g++){var y=r[g];if(y.dataset.precedence===t)c=y;else if(c!==o)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Kc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function qc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Vu=null;function Gg(e,t,a){if(Vu===null){var r=new Map,o=Vu=new Map;o.set(a,r)}else o=Vu,r=o.get(a),r||(r=new Map,o.set(a,r));if(r.has(e))return r;for(r.set(e,null),a=a.getElementsByTagName(e),o=0;o<a.length;o++){var c=a[o];if(!(c[xi]||c[ft]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var g=c.getAttribute(t)||"";g=e+g;var y=r.get(g);y?y.push(c):r.set(g,[c])}}return r}function Yg(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Y1(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Vg(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var dr=null;function V1(){}function X1(e,t,a){if(dr===null)throw Error(u(475));var r=dr;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var o=Vl(a.href),c=e.querySelector(cr(o));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(r.count++,r=Xu.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=c,tt(c);return}c=e.ownerDocument||e,a=Pg(a),(o=Vt.get(o))&&Kc(a,o),c=c.createElement("link"),tt(c);var g=c;g._p=new Promise(function(y,S){g.onload=y,g.onerror=S}),ct(c,"link",a),t.instance=c}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(r.count++,t=Xu.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}function F1(){if(dr===null)throw Error(u(475));var e=dr;return e.stylesheets&&e.count===0&&Bc(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Bc(e,e.stylesheets),e.unsuspend){var r=e.unsuspend;e.unsuspend=null,r()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Xu(){if(this.count--,this.count===0){if(this.stylesheets)Bc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Fu=null;function Bc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Fu=new Map,t.forEach(Z1,e),Fu=null,Xu.call(e))}function Z1(e,t){if(!(t.state.loading&4)){var a=Fu.get(e);if(a)var r=a.get(null);else{a=new Map,Fu.set(e,a);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<o.length;c++){var g=o[c];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(a.set(g.dataset.precedence,g),r=g)}r&&a.set(null,r)}o=t.instance,g=o.getAttribute("data-precedence"),c=a.get(g)||r,c===r&&a.set(null,o),a.set(g,o),this.count++,r=Xu.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),c?c.parentNode.insertBefore(o,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(o,e.firstChild)),t.state.loading|=4}}var hr={$$typeof:k,Provider:null,Consumer:null,_currentValue:W,_currentValue2:W,_threadCount:0};function J1(e,t,a,r,o,c,g,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Us(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Us(0),this.hiddenUpdates=Us(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=c,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function Xg(e,t,a,r,o,c,g,y,S,L,j,Q){return e=new J1(e,t,a,g,y,S,L,Q),t=1,c===!0&&(t|=24),c=Dt(3,null,null,t),e.current=c,c.stateNode=e,t=$o(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:r,isDehydrated:a,cache:t},Co(c),e}function Fg(e){return e?(e=Ol,e):Ol}function Zg(e,t,a,r,o,c){o=Fg(o),r.context===null?r.context=o:r.pendingContext=o,r=Vn(t),r.payload={element:a},c=c===void 0?null:c,c!==null&&(r.callback=c),a=Xn(e,r,t),a!==null&&(Ht(a,e,t),Pi(a,e,t))}function Jg(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Qc(e,t){Jg(e,t),(e=e.alternate)&&Jg(e,t)}function Wg(e){if(e.tag===13){var t=xl(e,67108864);t!==null&&Ht(t,e,67108864),Qc(e,67108864)}}var Zu=!0;function W1(e,t,a,r){var o=C.T;C.T=null;var c=G.p;try{G.p=2,Pc(e,t,a,r)}finally{G.p=c,C.T=o}}function I1(e,t,a,r){var o=C.T;C.T=null;var c=G.p;try{G.p=8,Pc(e,t,a,r)}finally{G.p=c,C.T=o}}function Pc(e,t,a,r){if(Zu){var o=kc(r);if(o===null)Mc(e,t,r,Ju,a),ev(e,r);else if(tS(o,e,t,a,r))r.stopPropagation();else if(ev(e,r),t&4&&-1<eS.indexOf(e)){for(;o!==null;){var c=dl(o);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var g=wa(c.pendingLanes);if(g!==0){var y=c;for(y.pendingLanes|=2,y.entangledLanes|=2;g;){var S=1<<31-Mt(g);y.entanglements[1]|=S,g&=~S}cn(c),(Ae&6)===0&&(_u=ln()+500,rr(0))}}break;case 13:y=xl(c,2),y!==null&&Ht(y,c,2),Uu(),Qc(c,2)}if(c=kc(r),c===null&&Mc(e,t,r,Ju,a),c===o)break;o=c}o!==null&&r.stopPropagation()}else Mc(e,t,r,null,a)}}function kc(e){return e=Xs(e),Gc(e)}var Ju=null;function Gc(e){if(Ju=null,e=fl(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=d(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ju=e,null}function Ig(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(K0()){case hd:return 2;case pd:return 8;case Pr:case q0:return 32;case gd:return 268435456;default:return 32}default:return 32}}var Yc=!1,ua=null,sa=null,oa=null,pr=new Map,gr=new Map,ca=[],eS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ev(e,t){switch(e){case"focusin":case"focusout":ua=null;break;case"dragenter":case"dragleave":sa=null;break;case"mouseover":case"mouseout":oa=null;break;case"pointerover":case"pointerout":pr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gr.delete(t.pointerId)}}function vr(e,t,a,r,o,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:a,eventSystemFlags:r,nativeEvent:c,targetContainers:[o]},t!==null&&(t=dl(t),t!==null&&Wg(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function tS(e,t,a,r,o){switch(t){case"focusin":return ua=vr(ua,e,t,a,r,o),!0;case"dragenter":return sa=vr(sa,e,t,a,r,o),!0;case"mouseover":return oa=vr(oa,e,t,a,r,o),!0;case"pointerover":var c=o.pointerId;return pr.set(c,vr(pr.get(c)||null,e,t,a,r,o)),!0;case"gotpointercapture":return c=o.pointerId,gr.set(c,vr(gr.get(c)||null,e,t,a,r,o)),!0}return!1}function tv(e){var t=fl(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=d(a),t!==null){e.blockedOn=t,X0(e.priority,function(){if(a.tag===13){var r=Ut();r=Hs(r);var o=xl(a,r);o!==null&&Ht(o,a,r),Qc(a,r)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Wu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=kc(e.nativeEvent);if(a===null){a=e.nativeEvent;var r=new a.constructor(a.type,a);Vs=r,a.target.dispatchEvent(r),Vs=null}else return t=dl(a),t!==null&&Wg(t),e.blockedOn=a,!1;t.shift()}return!0}function nv(e,t,a){Wu(e)&&a.delete(t)}function nS(){Yc=!1,ua!==null&&Wu(ua)&&(ua=null),sa!==null&&Wu(sa)&&(sa=null),oa!==null&&Wu(oa)&&(oa=null),pr.forEach(nv),gr.forEach(nv)}function Iu(e,t){e.blockedOn===t&&(e.blockedOn=null,Yc||(Yc=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,nS)))}var es=null;function av(e){es!==e&&(es=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){es===e&&(es=null);for(var t=0;t<e.length;t+=3){var a=e[t],r=e[t+1],o=e[t+2];if(typeof r!="function"){if(Gc(r||a)===null)continue;break}var c=dl(a);c!==null&&(e.splice(t,3),t-=3,Go(c,{pending:!0,data:o,method:a.method,action:r},r,o))}}))}function yr(e){function t(S){return Iu(S,e)}ua!==null&&Iu(ua,e),sa!==null&&Iu(sa,e),oa!==null&&Iu(oa,e),pr.forEach(t),gr.forEach(t);for(var a=0;a<ca.length;a++){var r=ca[a];r.blockedOn===e&&(r.blockedOn=null)}for(;0<ca.length&&(a=ca[0],a.blockedOn===null);)tv(a),a.blockedOn===null&&ca.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(r=0;r<a.length;r+=3){var o=a[r],c=a[r+1],g=o[bt]||null;if(typeof c=="function")g||av(a);else if(g){var y=null;if(c&&c.hasAttribute("formAction")){if(o=c,g=c[bt]||null)y=g.formAction;else if(Gc(o)!==null)continue}else y=g.action;typeof y=="function"?a[r+1]=y:(a.splice(r,3),r-=3),av(a)}}}function Vc(e){this._internalRoot=e}ts.prototype.render=Vc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var a=t.current,r=Ut();Zg(a,r,e,t,null,null)},ts.prototype.unmount=Vc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Zg(e.current,2,null,e,null,null),Uu(),t[cl]=null}};function ts(e){this._internalRoot=e}ts.prototype.unstable_scheduleHydration=function(e){if(e){var t=Sd();e={blockedOn:null,target:e,priority:t};for(var a=0;a<ca.length&&t!==0&&t<ca[a].priority;a++);ca.splice(a,0,e),a===0&&tv(e)}};var lv=n.version;if(lv!=="19.1.1")throw Error(u(527,lv,"19.1.1"));G.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=v(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var aS={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:C,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ns=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ns.isDisabled&&ns.supportsFiber)try{Ei=ns.inject(aS),At=ns}catch{}}return br.createRoot=function(e,t){if(!s(e))throw Error(u(299));var a=!1,r="",o=Sp,c=Ep,g=$p,y=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onUncaughtError!==void 0&&(o=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(g=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=Xg(e,1,!1,null,null,a,r,o,c,g,y,null),e[cl]=t.current,Ac(e),new Vc(t)},br.hydrateRoot=function(e,t,a){if(!s(e))throw Error(u(299));var r=!1,o="",c=Sp,g=Ep,y=$p,S=null,L=null;return a!=null&&(a.unstable_strictMode===!0&&(r=!0),a.identifierPrefix!==void 0&&(o=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(g=a.onCaughtError),a.onRecoverableError!==void 0&&(y=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(S=a.unstable_transitionCallbacks),a.formState!==void 0&&(L=a.formState)),t=Xg(e,1,!0,t,a??null,r,o,c,g,y,S,L),t.context=Fg(null),a=t.current,r=Ut(),r=Hs(r),o=Vn(r),o.callback=null,Xn(a,o,r),a=r,t.current.lanes=a,Ti(t,a),cn(t),e[cl]=t.current,Ac(e),new ts(t)},br.version="19.1.1",br}var gv;function pS(){if(gv)return Jc.exports;gv=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(n){console.error(n)}}return l(),Jc.exports=hS(),Jc.exports}var X$=pS(),Si=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(l){return this.listeners.add(l),this.onSubscribe(),()=>{this.listeners.delete(l),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},gS={setTimeout:(l,n)=>setTimeout(l,n),clearTimeout:l=>clearTimeout(l),setInterval:(l,n)=>setInterval(l,n),clearInterval:l=>clearInterval(l)},pa,Gf,My,vS=(My=class{constructor(){se(this,pa,gS);se(this,Gf,!1)}setTimeoutProvider(l){I(this,pa,l)}setTimeout(l,n){return T(this,pa).setTimeout(l,n)}clearTimeout(l){T(this,pa).clearTimeout(l)}setInterval(l,n){return T(this,pa).setInterval(l,n)}clearInterval(l){T(this,pa).clearInterval(l)}},pa=new WeakMap,Gf=new WeakMap,My),Ya=new vS;function yS(l){setTimeout(l,0)}var il=typeof window>"u"||"Deno"in globalThis;function mt(){}function mS(l,n){return typeof l=="function"?l(n):l}function cf(l){return typeof l=="number"&&l>=0&&l!==1/0}function Py(l,n){return Math.max(l+(n||0)-Date.now(),0)}function Oa(l,n){return typeof l=="function"?l(n):l}function Ft(l,n){return typeof l=="function"?l(n):l}function vv(l,n){const{type:i="all",exact:u,fetchStatus:s,predicate:f,queryKey:d,stale:h}=l;if(d){if(u){if(n.queryHash!==Vf(d,n.options))return!1}else if(!Ar(n.queryKey,d))return!1}if(i!=="all"){const v=n.isActive();if(i==="active"&&!v||i==="inactive"&&v)return!1}return!(typeof h=="boolean"&&n.isStale()!==h||s&&s!==n.state.fetchStatus||f&&!f(n))}function yv(l,n){const{exact:i,status:u,predicate:s,mutationKey:f}=l;if(f){if(!n.options.mutationKey)return!1;if(i){if(rl(n.options.mutationKey)!==rl(f))return!1}else if(!Ar(n.options.mutationKey,f))return!1}return!(u&&n.state.status!==u||s&&!s(n))}function Vf(l,n){return((n==null?void 0:n.queryKeyHashFn)||rl)(l)}function rl(l){return JSON.stringify(l,(n,i)=>ff(i)?Object.keys(i).sort().reduce((u,s)=>(u[s]=i[s],u),{}):i)}function Ar(l,n){return l===n?!0:typeof l!=typeof n?!1:l&&n&&typeof l=="object"&&typeof n=="object"?Object.keys(n).every(i=>Ar(l[i],n[i])):!1}var bS=Object.prototype.hasOwnProperty;function ky(l,n){if(l===n)return l;const i=mv(l)&&mv(n);if(!i&&!(ff(l)&&ff(n)))return n;const s=(i?l:Object.keys(l)).length,f=i?n:Object.keys(n),d=f.length,h=i?new Array(d):{};let v=0;for(let p=0;p<d;p++){const m=i?p:f[p],b=l[m],$=n[m];if(b===$){h[m]=b,(i?p<s:bS.call(l,m))&&v++;continue}if(b===null||$===null||typeof b!="object"||typeof $!="object"){h[m]=$;continue}const E=ky(b,$);h[m]=E,E===b&&v++}return s===d&&v===s?l:h}function ps(l,n){if(!n||Object.keys(l).length!==Object.keys(n).length)return!1;for(const i in l)if(l[i]!==n[i])return!1;return!0}function mv(l){return Array.isArray(l)&&l.length===Object.keys(l).length}function ff(l){if(!bv(l))return!1;const n=l.constructor;if(n===void 0)return!0;const i=n.prototype;return!(!bv(i)||!i.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(l)!==Object.prototype)}function bv(l){return Object.prototype.toString.call(l)==="[object Object]"}function SS(l){return new Promise(n=>{Ya.setTimeout(n,l)})}function df(l,n,i){return typeof i.structuralSharing=="function"?i.structuralSharing(l,n):i.structuralSharing!==!1?ky(l,n):n}function ES(l,n,i=0){const u=[...l,n];return i&&u.length>i?u.slice(1):u}function $S(l,n,i=0){const u=[n,...l];return i&&u.length>i?u.slice(0,-1):u}var Xf=Symbol();function Gy(l,n){return!l.queryFn&&(n!=null&&n.initialPromise)?()=>n.initialPromise:!l.queryFn||l.queryFn===Xf?()=>Promise.reject(new Error(`Missing queryFn: '${l.queryHash}'`)):l.queryFn}function Yy(l,n){return typeof l=="function"?l(...n):!!l}var Fa,ga,ai,Ny,TS=(Ny=class extends Si{constructor(){super();se(this,Fa);se(this,ga);se(this,ai);I(this,ai,n=>{if(!il&&window.addEventListener){const i=()=>n();return window.addEventListener("visibilitychange",i,!1),()=>{window.removeEventListener("visibilitychange",i)}}})}onSubscribe(){T(this,ga)||this.setEventListener(T(this,ai))}onUnsubscribe(){var n;this.hasListeners()||((n=T(this,ga))==null||n.call(this),I(this,ga,void 0))}setEventListener(n){var i;I(this,ai,n),(i=T(this,ga))==null||i.call(this),I(this,ga,n(u=>{typeof u=="boolean"?this.setFocused(u):this.onFocus()}))}setFocused(n){T(this,Fa)!==n&&(I(this,Fa,n),this.onFocus())}onFocus(){const n=this.isFocused();this.listeners.forEach(i=>{i(n)})}isFocused(){var n;return typeof T(this,Fa)=="boolean"?T(this,Fa):((n=globalThis.document)==null?void 0:n.visibilityState)!=="hidden"}},Fa=new WeakMap,ga=new WeakMap,ai=new WeakMap,Ny),Ff=new TS;function hf(){let l,n;const i=new Promise((s,f)=>{l=s,n=f});i.status="pending",i.catch(()=>{});function u(s){Object.assign(i,s),delete i.resolve,delete i.reject}return i.resolve=s=>{u({status:"fulfilled",value:s}),l(s)},i.reject=s=>{u({status:"rejected",reason:s}),n(s)},i}var xS=yS;function OS(){let l=[],n=0,i=h=>{h()},u=h=>{h()},s=xS;const f=h=>{n?l.push(h):s(()=>{i(h)})},d=()=>{const h=l;l=[],h.length&&s(()=>{u(()=>{h.forEach(v=>{i(v)})})})};return{batch:h=>{let v;n++;try{v=h()}finally{n--,n||d()}return v},batchCalls:h=>(...v)=>{f(()=>{h(...v)})},schedule:f,setNotifyFunction:h=>{i=h},setBatchNotifyFunction:h=>{u=h},setScheduler:h=>{s=h}}}var et=OS(),li,va,ii,Dy,CS=(Dy=class extends Si{constructor(){super();se(this,li,!0);se(this,va);se(this,ii);I(this,ii,n=>{if(!il&&window.addEventListener){const i=()=>n(!0),u=()=>n(!1);return window.addEventListener("online",i,!1),window.addEventListener("offline",u,!1),()=>{window.removeEventListener("online",i),window.removeEventListener("offline",u)}}})}onSubscribe(){T(this,va)||this.setEventListener(T(this,ii))}onUnsubscribe(){var n;this.hasListeners()||((n=T(this,va))==null||n.call(this),I(this,va,void 0))}setEventListener(n){var i;I(this,ii,n),(i=T(this,va))==null||i.call(this),I(this,va,n(this.setOnline.bind(this)))}setOnline(n){T(this,li)!==n&&(I(this,li,n),this.listeners.forEach(u=>{u(n)}))}isOnline(){return T(this,li)}},li=new WeakMap,va=new WeakMap,ii=new WeakMap,Dy),gs=new CS;function wS(l){return Math.min(1e3*2**l,3e4)}function Vy(l){return(l??"online")==="online"?gs.isOnline():!0}var pf=class extends Error{constructor(l){super("CancelledError"),this.revert=l==null?void 0:l.revert,this.silent=l==null?void 0:l.silent}};function Xy(l){let n=!1,i=0,u;const s=hf(),f=()=>s.status!=="pending",d=R=>{var N;if(!f()){const P=new pf(R);$(P),(N=l.onCancel)==null||N.call(l,P)}},h=()=>{n=!0},v=()=>{n=!1},p=()=>Ff.isFocused()&&(l.networkMode==="always"||gs.isOnline())&&l.canRun(),m=()=>Vy(l.networkMode)&&l.canRun(),b=R=>{f()||(u==null||u(),s.resolve(R))},$=R=>{f()||(u==null||u(),s.reject(R))},E=()=>new Promise(R=>{var N;u=P=>{(f()||p())&&R(P)},(N=l.onPause)==null||N.call(l)}).then(()=>{var R;u=void 0,f()||(R=l.onContinue)==null||R.call(l)}),O=()=>{if(f())return;let R;const N=i===0?l.initialPromise:void 0;try{R=N??l.fn()}catch(P){R=Promise.reject(P)}Promise.resolve(R).then(b).catch(P=>{var ee;if(f())return;const K=l.retry??(il?0:3),k=l.retryDelay??wS,B=typeof k=="function"?k(i,P):k,V=K===!0||typeof K=="number"&&i<K||typeof K=="function"&&K(i,P);if(n||!V){$(P);return}i++,(ee=l.onFail)==null||ee.call(l,i,P),SS(B).then(()=>p()?void 0:E()).then(()=>{n?$(P):O()})})};return{promise:s,status:()=>s.status,cancel:d,continue:()=>(u==null||u(),s),cancelRetry:h,continueRetry:v,canStart:m,start:()=>(m()?O():E().then(O),s)}}var Za,Ly,Fy=(Ly=class{constructor(){se(this,Za)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),cf(this.gcTime)&&I(this,Za,Ya.setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(l){this.gcTime=Math.max(this.gcTime||0,l??(il?1/0:300*1e3))}clearGcTimeout(){T(this,Za)&&(Ya.clearTimeout(T(this,Za)),I(this,Za,void 0))}},Za=new WeakMap,Ly),Ja,ri,Xt,Wa,rt,Ur,Ia,nn,_n,_y,RS=(_y=class extends Fy{constructor(n){super();se(this,nn);se(this,Ja);se(this,ri);se(this,Xt);se(this,Wa);se(this,rt);se(this,Ur);se(this,Ia);I(this,Ia,!1),I(this,Ur,n.defaultOptions),this.setOptions(n.options),this.observers=[],I(this,Wa,n.client),I(this,Xt,T(this,Wa).getQueryCache()),this.queryKey=n.queryKey,this.queryHash=n.queryHash,I(this,Ja,Sv(this.options)),this.state=n.state??T(this,Ja),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var n;return(n=T(this,rt))==null?void 0:n.promise}setOptions(n){if(this.options={...T(this,Ur),...n},this.updateGcTime(this.options.gcTime),this.state&&this.state.data===void 0){const i=Sv(this.options);i.data!==void 0&&(this.setData(i.data,{updatedAt:i.dataUpdatedAt,manual:!0}),I(this,Ja,i))}}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&T(this,Xt).remove(this)}setData(n,i){const u=df(this.state.data,n,this.options);return me(this,nn,_n).call(this,{data:u,type:"success",dataUpdatedAt:i==null?void 0:i.updatedAt,manual:i==null?void 0:i.manual}),u}setState(n,i){me(this,nn,_n).call(this,{type:"setState",state:n,setStateOptions:i})}cancel(n){var u,s;const i=(u=T(this,rt))==null?void 0:u.promise;return(s=T(this,rt))==null||s.cancel(n),i?i.then(mt).catch(mt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(T(this,Ja))}isActive(){return this.observers.some(n=>Ft(n.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Xf||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(n=>Oa(n.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(n=>n.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(n=0){return this.state.data===void 0?!0:n==="static"?!1:this.state.isInvalidated?!0:!Py(this.state.dataUpdatedAt,n)}onFocus(){var i;const n=this.observers.find(u=>u.shouldFetchOnWindowFocus());n==null||n.refetch({cancelRefetch:!1}),(i=T(this,rt))==null||i.continue()}onOnline(){var i;const n=this.observers.find(u=>u.shouldFetchOnReconnect());n==null||n.refetch({cancelRefetch:!1}),(i=T(this,rt))==null||i.continue()}addObserver(n){this.observers.includes(n)||(this.observers.push(n),this.clearGcTimeout(),T(this,Xt).notify({type:"observerAdded",query:this,observer:n}))}removeObserver(n){this.observers.includes(n)&&(this.observers=this.observers.filter(i=>i!==n),this.observers.length||(T(this,rt)&&(T(this,Ia)?T(this,rt).cancel({revert:!0}):T(this,rt).cancelRetry()),this.scheduleGc()),T(this,Xt).notify({type:"observerRemoved",query:this,observer:n}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||me(this,nn,_n).call(this,{type:"invalidate"})}async fetch(n,i){var v,p,m,b,$,E,O,R,N,P,K,k;if(this.state.fetchStatus!=="idle"&&((v=T(this,rt))==null?void 0:v.status())!=="rejected"){if(this.state.data!==void 0&&(i!=null&&i.cancelRefetch))this.cancel({silent:!0});else if(T(this,rt))return T(this,rt).continueRetry(),T(this,rt).promise}if(n&&this.setOptions(n),!this.options.queryFn){const B=this.observers.find(V=>V.options.queryFn);B&&this.setOptions(B.options)}const u=new AbortController,s=B=>{Object.defineProperty(B,"signal",{enumerable:!0,get:()=>(I(this,Ia,!0),u.signal)})},f=()=>{const B=Gy(this.options,i),ee=(()=>{const re={client:T(this,Wa),queryKey:this.queryKey,meta:this.meta};return s(re),re})();return I(this,Ia,!1),this.options.persister?this.options.persister(B,ee,this):B(ee)},h=(()=>{const B={fetchOptions:i,options:this.options,queryKey:this.queryKey,client:T(this,Wa),state:this.state,fetchFn:f};return s(B),B})();(p=this.options.behavior)==null||p.onFetch(h,this),I(this,ri,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((m=h.fetchOptions)==null?void 0:m.meta))&&me(this,nn,_n).call(this,{type:"fetch",meta:(b=h.fetchOptions)==null?void 0:b.meta}),I(this,rt,Xy({initialPromise:i==null?void 0:i.initialPromise,fn:h.fetchFn,onCancel:B=>{B instanceof pf&&B.revert&&this.setState({...T(this,ri),fetchStatus:"idle"}),u.abort()},onFail:(B,V)=>{me(this,nn,_n).call(this,{type:"failed",failureCount:B,error:V})},onPause:()=>{me(this,nn,_n).call(this,{type:"pause"})},onContinue:()=>{me(this,nn,_n).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0}));try{const B=await T(this,rt).start();if(B===void 0)throw new Error(`${this.queryHash} data is undefined`);return this.setData(B),(E=($=T(this,Xt).config).onSuccess)==null||E.call($,B,this),(R=(O=T(this,Xt).config).onSettled)==null||R.call(O,B,this.state.error,this),B}catch(B){if(B instanceof pf){if(B.silent)return T(this,rt).promise;if(B.revert){if(this.state.data===void 0)throw B;return this.state.data}}throw me(this,nn,_n).call(this,{type:"error",error:B}),(P=(N=T(this,Xt).config).onError)==null||P.call(N,B,this),(k=(K=T(this,Xt).config).onSettled)==null||k.call(K,this.state.data,B,this),B}finally{this.scheduleGc()}}},Ja=new WeakMap,ri=new WeakMap,Xt=new WeakMap,Wa=new WeakMap,rt=new WeakMap,Ur=new WeakMap,Ia=new WeakMap,nn=new WeakSet,_n=function(n){const i=u=>{switch(n.type){case"failed":return{...u,fetchFailureCount:n.failureCount,fetchFailureReason:n.error};case"pause":return{...u,fetchStatus:"paused"};case"continue":return{...u,fetchStatus:"fetching"};case"fetch":return{...u,...Zy(u.data,this.options),fetchMeta:n.meta??null};case"success":const s={...u,data:n.data,dataUpdateCount:u.dataUpdateCount+1,dataUpdatedAt:n.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!n.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return I(this,ri,n.manual?s:void 0),s;case"error":const f=n.error;return{...u,error:f,errorUpdateCount:u.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:u.fetchFailureCount+1,fetchFailureReason:f,fetchStatus:"idle",status:"error"};case"invalidate":return{...u,isInvalidated:!0};case"setState":return{...u,...n.state}}};this.state=i(this.state),et.batch(()=>{this.observers.forEach(u=>{u.onQueryUpdate()}),T(this,Xt).notify({query:this,type:"updated",action:n})})},_y);function Zy(l,n){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Vy(n.networkMode)?"fetching":"paused",...l===void 0&&{error:null,status:"pending"}}}function Sv(l){const n=typeof l.initialData=="function"?l.initialData():l.initialData,i=n!==void 0,u=i?typeof l.initialDataUpdatedAt=="function"?l.initialDataUpdatedAt():l.initialDataUpdatedAt:0;return{data:n,dataUpdateCount:0,dataUpdatedAt:i?u??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}var Ot,xe,Hr,vt,el,ui,zn,ya,jr,si,oi,tl,nl,ma,ci,Re,Tr,gf,vf,yf,mf,bf,Sf,Ef,Jy,zy,AS=(zy=class extends Si{constructor(n,i){super();se(this,Re);se(this,Ot);se(this,xe);se(this,Hr);se(this,vt);se(this,el);se(this,ui);se(this,zn);se(this,ya);se(this,jr);se(this,si);se(this,oi);se(this,tl);se(this,nl);se(this,ma);se(this,ci,new Set);this.options=i,I(this,Ot,n),I(this,ya,null),I(this,zn,hf()),this.bindMethods(),this.setOptions(i)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(T(this,xe).addObserver(this),Ev(T(this,xe),this.options)?me(this,Re,Tr).call(this):this.updateResult(),me(this,Re,mf).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return $f(T(this,xe),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return $f(T(this,xe),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,me(this,Re,bf).call(this),me(this,Re,Sf).call(this),T(this,xe).removeObserver(this)}setOptions(n){const i=this.options,u=T(this,xe);if(this.options=T(this,Ot).defaultQueryOptions(n),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Ft(this.options.enabled,T(this,xe))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");me(this,Re,Ef).call(this),T(this,xe).setOptions(this.options),i._defaulted&&!ps(this.options,i)&&T(this,Ot).getQueryCache().notify({type:"observerOptionsUpdated",query:T(this,xe),observer:this});const s=this.hasListeners();s&&$v(T(this,xe),u,this.options,i)&&me(this,Re,Tr).call(this),this.updateResult(),s&&(T(this,xe)!==u||Ft(this.options.enabled,T(this,xe))!==Ft(i.enabled,T(this,xe))||Oa(this.options.staleTime,T(this,xe))!==Oa(i.staleTime,T(this,xe)))&&me(this,Re,gf).call(this);const f=me(this,Re,vf).call(this);s&&(T(this,xe)!==u||Ft(this.options.enabled,T(this,xe))!==Ft(i.enabled,T(this,xe))||f!==T(this,ma))&&me(this,Re,yf).call(this,f)}getOptimisticResult(n){const i=T(this,Ot).getQueryCache().build(T(this,Ot),n),u=this.createResult(i,n);return NS(this,u)&&(I(this,vt,u),I(this,ui,this.options),I(this,el,T(this,xe).state)),u}getCurrentResult(){return T(this,vt)}trackResult(n,i){return new Proxy(n,{get:(u,s)=>(this.trackProp(s),i==null||i(s),s==="promise"&&!this.options.experimental_prefetchInRender&&T(this,zn).status==="pending"&&T(this,zn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),Reflect.get(u,s))})}trackProp(n){T(this,ci).add(n)}getCurrentQuery(){return T(this,xe)}refetch({...n}={}){return this.fetch({...n})}fetchOptimistic(n){const i=T(this,Ot).defaultQueryOptions(n),u=T(this,Ot).getQueryCache().build(T(this,Ot),i);return u.fetch().then(()=>this.createResult(u,i))}fetch(n){return me(this,Re,Tr).call(this,{...n,cancelRefetch:n.cancelRefetch??!0}).then(()=>(this.updateResult(),T(this,vt)))}createResult(n,i){var ae;const u=T(this,xe),s=this.options,f=T(this,vt),d=T(this,el),h=T(this,ui),p=n!==u?n.state:T(this,Hr),{state:m}=n;let b={...m},$=!1,E;if(i._optimisticResults){const F=this.hasListeners(),U=!F&&Ev(n,i),X=F&&$v(n,u,i,s);(U||X)&&(b={...b,...Zy(m.data,n.options)}),i._optimisticResults==="isRestoring"&&(b.fetchStatus="idle")}let{error:O,errorUpdatedAt:R,status:N}=b;E=b.data;let P=!1;if(i.placeholderData!==void 0&&E===void 0&&N==="pending"){let F;f!=null&&f.isPlaceholderData&&i.placeholderData===(h==null?void 0:h.placeholderData)?(F=f.data,P=!0):F=typeof i.placeholderData=="function"?i.placeholderData((ae=T(this,oi))==null?void 0:ae.state.data,T(this,oi)):i.placeholderData,F!==void 0&&(N="success",E=df(f==null?void 0:f.data,F,i),$=!0)}if(i.select&&E!==void 0&&!P)if(f&&E===(d==null?void 0:d.data)&&i.select===T(this,jr))E=T(this,si);else try{I(this,jr,i.select),E=i.select(E),E=df(f==null?void 0:f.data,E,i),I(this,si,E),I(this,ya,null)}catch(F){I(this,ya,F)}T(this,ya)&&(O=T(this,ya),E=T(this,si),R=Date.now(),N="error");const K=b.fetchStatus==="fetching",k=N==="pending",B=N==="error",V=k&&K,ee=E!==void 0,te={status:N,fetchStatus:b.fetchStatus,isPending:k,isSuccess:N==="success",isError:B,isInitialLoading:V,isLoading:V,data:E,dataUpdatedAt:b.dataUpdatedAt,error:O,errorUpdatedAt:R,failureCount:b.fetchFailureCount,failureReason:b.fetchFailureReason,errorUpdateCount:b.errorUpdateCount,isFetched:b.dataUpdateCount>0||b.errorUpdateCount>0,isFetchedAfterMount:b.dataUpdateCount>p.dataUpdateCount||b.errorUpdateCount>p.errorUpdateCount,isFetching:K,isRefetching:K&&!k,isLoadingError:B&&!ee,isPaused:b.fetchStatus==="paused",isPlaceholderData:$,isRefetchError:B&&ee,isStale:Zf(n,i),refetch:this.refetch,promise:T(this,zn),isEnabled:Ft(i.enabled,n)!==!1};if(this.options.experimental_prefetchInRender){const F=le=>{te.status==="error"?le.reject(te.error):te.data!==void 0&&le.resolve(te.data)},U=()=>{const le=I(this,zn,te.promise=hf());F(le)},X=T(this,zn);switch(X.status){case"pending":n.queryHash===u.queryHash&&F(X);break;case"fulfilled":(te.status==="error"||te.data!==X.value)&&U();break;case"rejected":(te.status!=="error"||te.error!==X.reason)&&U();break}}return te}updateResult(){const n=T(this,vt),i=this.createResult(T(this,xe),this.options);if(I(this,el,T(this,xe).state),I(this,ui,this.options),T(this,el).data!==void 0&&I(this,oi,T(this,xe)),ps(i,n))return;I(this,vt,i);const u=()=>{if(!n)return!0;const{notifyOnChangeProps:s}=this.options,f=typeof s=="function"?s():s;if(f==="all"||!f&&!T(this,ci).size)return!0;const d=new Set(f??T(this,ci));return this.options.throwOnError&&d.add("error"),Object.keys(T(this,vt)).some(h=>{const v=h;return T(this,vt)[v]!==n[v]&&d.has(v)})};me(this,Re,Jy).call(this,{listeners:u()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&me(this,Re,mf).call(this)}},Ot=new WeakMap,xe=new WeakMap,Hr=new WeakMap,vt=new WeakMap,el=new WeakMap,ui=new WeakMap,zn=new WeakMap,ya=new WeakMap,jr=new WeakMap,si=new WeakMap,oi=new WeakMap,tl=new WeakMap,nl=new WeakMap,ma=new WeakMap,ci=new WeakMap,Re=new WeakSet,Tr=function(n){me(this,Re,Ef).call(this);let i=T(this,xe).fetch(this.options,n);return n!=null&&n.throwOnError||(i=i.catch(mt)),i},gf=function(){me(this,Re,bf).call(this);const n=Oa(this.options.staleTime,T(this,xe));if(il||T(this,vt).isStale||!cf(n))return;const u=Py(T(this,vt).dataUpdatedAt,n)+1;I(this,tl,Ya.setTimeout(()=>{T(this,vt).isStale||this.updateResult()},u))},vf=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(T(this,xe)):this.options.refetchInterval)??!1},yf=function(n){me(this,Re,Sf).call(this),I(this,ma,n),!(il||Ft(this.options.enabled,T(this,xe))===!1||!cf(T(this,ma))||T(this,ma)===0)&&I(this,nl,Ya.setInterval(()=>{(this.options.refetchIntervalInBackground||Ff.isFocused())&&me(this,Re,Tr).call(this)},T(this,ma)))},mf=function(){me(this,Re,gf).call(this),me(this,Re,yf).call(this,me(this,Re,vf).call(this))},bf=function(){T(this,tl)&&(Ya.clearTimeout(T(this,tl)),I(this,tl,void 0))},Sf=function(){T(this,nl)&&(Ya.clearInterval(T(this,nl)),I(this,nl,void 0))},Ef=function(){const n=T(this,Ot).getQueryCache().build(T(this,Ot),this.options);if(n===T(this,xe))return;const i=T(this,xe);I(this,xe,n),I(this,Hr,n.state),this.hasListeners()&&(i==null||i.removeObserver(this),n.addObserver(this))},Jy=function(n){et.batch(()=>{n.listeners&&this.listeners.forEach(i=>{i(T(this,vt))}),T(this,Ot).getQueryCache().notify({query:T(this,xe),type:"observerResultsUpdated"})})},zy);function MS(l,n){return Ft(n.enabled,l)!==!1&&l.state.data===void 0&&!(l.state.status==="error"&&n.retryOnMount===!1)}function Ev(l,n){return MS(l,n)||l.state.data!==void 0&&$f(l,n,n.refetchOnMount)}function $f(l,n,i){if(Ft(n.enabled,l)!==!1&&Oa(n.staleTime,l)!=="static"){const u=typeof i=="function"?i(l):i;return u==="always"||u!==!1&&Zf(l,n)}return!1}function $v(l,n,i,u){return(l!==n||Ft(u.enabled,l)===!1)&&(!i.suspense||l.state.status!=="error")&&Zf(l,i)}function Zf(l,n){return Ft(n.enabled,l)!==!1&&l.isStaleByTime(Oa(n.staleTime,l))}function NS(l,n){return!ps(l.getCurrentResult(),n)}function Tv(l){return{onFetch:(n,i)=>{var m,b,$,E,O;const u=n.options,s=($=(b=(m=n.fetchOptions)==null?void 0:m.meta)==null?void 0:b.fetchMore)==null?void 0:$.direction,f=((E=n.state.data)==null?void 0:E.pages)||[],d=((O=n.state.data)==null?void 0:O.pageParams)||[];let h={pages:[],pageParams:[]},v=0;const p=async()=>{let R=!1;const N=k=>{Object.defineProperty(k,"signal",{enumerable:!0,get:()=>(n.signal.aborted?R=!0:n.signal.addEventListener("abort",()=>{R=!0}),n.signal)})},P=Gy(n.options,n.fetchOptions),K=async(k,B,V)=>{if(R)return Promise.reject();if(B==null&&k.pages.length)return Promise.resolve(k);const re=(()=>{const U={client:n.client,queryKey:n.queryKey,pageParam:B,direction:V?"backward":"forward",meta:n.options.meta};return N(U),U})(),te=await P(re),{maxPages:ae}=n.options,F=V?$S:ES;return{pages:F(k.pages,te,ae),pageParams:F(k.pageParams,B,ae)}};if(s&&f.length){const k=s==="backward",B=k?DS:xv,V={pages:f,pageParams:d},ee=B(u,V);h=await K(V,ee,k)}else{const k=l??f.length;do{const B=v===0?d[0]??u.initialPageParam:xv(u,h);if(v>0&&B==null)break;h=await K(h,B),v++}while(v<k)}return h};n.options.persister?n.fetchFn=()=>{var R,N;return(N=(R=n.options).persister)==null?void 0:N.call(R,p,{client:n.client,queryKey:n.queryKey,meta:n.options.meta,signal:n.signal},i)}:n.fetchFn=p}}}function xv(l,{pages:n,pageParams:i}){const u=n.length-1;return n.length>0?l.getNextPageParam(n[u],n,i[u],i):void 0}function DS(l,{pages:n,pageParams:i}){var u;return n.length>0?(u=l.getPreviousPageParam)==null?void 0:u.call(l,n[0],n,i[0],i):void 0}var Kr,hn,yt,al,pn,da,Uy,LS=(Uy=class extends Fy{constructor(n){super();se(this,pn);se(this,Kr);se(this,hn);se(this,yt);se(this,al);I(this,Kr,n.client),this.mutationId=n.mutationId,I(this,yt,n.mutationCache),I(this,hn,[]),this.state=n.state||Wy(),this.setOptions(n.options),this.scheduleGc()}setOptions(n){this.options=n,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(n){T(this,hn).includes(n)||(T(this,hn).push(n),this.clearGcTimeout(),T(this,yt).notify({type:"observerAdded",mutation:this,observer:n}))}removeObserver(n){I(this,hn,T(this,hn).filter(i=>i!==n)),this.scheduleGc(),T(this,yt).notify({type:"observerRemoved",mutation:this,observer:n})}optionalRemove(){T(this,hn).length||(this.state.status==="pending"?this.scheduleGc():T(this,yt).remove(this))}continue(){var n;return((n=T(this,al))==null?void 0:n.continue())??this.execute(this.state.variables)}async execute(n){var d,h,v,p,m,b,$,E,O,R,N,P,K,k,B,V,ee,re,te,ae;const i=()=>{me(this,pn,da).call(this,{type:"continue"})},u={client:T(this,Kr),meta:this.options.meta,mutationKey:this.options.mutationKey};I(this,al,Xy({fn:()=>this.options.mutationFn?this.options.mutationFn(n,u):Promise.reject(new Error("No mutationFn found")),onFail:(F,U)=>{me(this,pn,da).call(this,{type:"failed",failureCount:F,error:U})},onPause:()=>{me(this,pn,da).call(this,{type:"pause"})},onContinue:i,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>T(this,yt).canRun(this)}));const s=this.state.status==="pending",f=!T(this,al).canStart();try{if(s)i();else{me(this,pn,da).call(this,{type:"pending",variables:n,isPaused:f}),await((h=(d=T(this,yt).config).onMutate)==null?void 0:h.call(d,n,this,u));const U=await((p=(v=this.options).onMutate)==null?void 0:p.call(v,n,u));U!==this.state.context&&me(this,pn,da).call(this,{type:"pending",context:U,variables:n,isPaused:f})}const F=await T(this,al).start();return await((b=(m=T(this,yt).config).onSuccess)==null?void 0:b.call(m,F,n,this.state.context,this,u)),await((E=($=this.options).onSuccess)==null?void 0:E.call($,F,n,this.state.context,u)),await((R=(O=T(this,yt).config).onSettled)==null?void 0:R.call(O,F,null,this.state.variables,this.state.context,this,u)),await((P=(N=this.options).onSettled)==null?void 0:P.call(N,F,null,n,this.state.context,u)),me(this,pn,da).call(this,{type:"success",data:F}),F}catch(F){try{throw await((k=(K=T(this,yt).config).onError)==null?void 0:k.call(K,F,n,this.state.context,this,u)),await((V=(B=this.options).onError)==null?void 0:V.call(B,F,n,this.state.context,u)),await((re=(ee=T(this,yt).config).onSettled)==null?void 0:re.call(ee,void 0,F,this.state.variables,this.state.context,this,u)),await((ae=(te=this.options).onSettled)==null?void 0:ae.call(te,void 0,F,n,this.state.context,u)),F}finally{me(this,pn,da).call(this,{type:"error",error:F})}}finally{T(this,yt).runNext(this)}}},Kr=new WeakMap,hn=new WeakMap,yt=new WeakMap,al=new WeakMap,pn=new WeakSet,da=function(n){const i=u=>{switch(n.type){case"failed":return{...u,failureCount:n.failureCount,failureReason:n.error};case"pause":return{...u,isPaused:!0};case"continue":return{...u,isPaused:!1};case"pending":return{...u,context:n.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:n.isPaused,status:"pending",variables:n.variables,submittedAt:Date.now()};case"success":return{...u,data:n.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...u,data:void 0,error:n.error,failureCount:u.failureCount+1,failureReason:n.error,isPaused:!1,status:"error"}}};this.state=i(this.state),et.batch(()=>{T(this,hn).forEach(u=>{u.onMutationUpdate(n)}),T(this,yt).notify({mutation:this,type:"updated",action:n})})},Uy);function Wy(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Un,an,qr,Hy,_S=(Hy=class extends Si{constructor(n={}){super();se(this,Un);se(this,an);se(this,qr);this.config=n,I(this,Un,new Set),I(this,an,new Map),I(this,qr,0)}build(n,i,u){const s=new LS({client:n,mutationCache:this,mutationId:++as(this,qr)._,options:n.defaultMutationOptions(i),state:u});return this.add(s),s}add(n){T(this,Un).add(n);const i=ls(n);if(typeof i=="string"){const u=T(this,an).get(i);u?u.push(n):T(this,an).set(i,[n])}this.notify({type:"added",mutation:n})}remove(n){if(T(this,Un).delete(n)){const i=ls(n);if(typeof i=="string"){const u=T(this,an).get(i);if(u)if(u.length>1){const s=u.indexOf(n);s!==-1&&u.splice(s,1)}else u[0]===n&&T(this,an).delete(i)}}this.notify({type:"removed",mutation:n})}canRun(n){const i=ls(n);if(typeof i=="string"){const u=T(this,an).get(i),s=u==null?void 0:u.find(f=>f.state.status==="pending");return!s||s===n}else return!0}runNext(n){var u;const i=ls(n);if(typeof i=="string"){const s=(u=T(this,an).get(i))==null?void 0:u.find(f=>f!==n&&f.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}else return Promise.resolve()}clear(){et.batch(()=>{T(this,Un).forEach(n=>{this.notify({type:"removed",mutation:n})}),T(this,Un).clear(),T(this,an).clear()})}getAll(){return Array.from(T(this,Un))}find(n){const i={exact:!0,...n};return this.getAll().find(u=>yv(i,u))}findAll(n={}){return this.getAll().filter(i=>yv(n,i))}notify(n){et.batch(()=>{this.listeners.forEach(i=>{i(n)})})}resumePausedMutations(){const n=this.getAll().filter(i=>i.state.isPaused);return et.batch(()=>Promise.all(n.map(i=>i.continue().catch(mt))))}},Un=new WeakMap,an=new WeakMap,qr=new WeakMap,Hy);function ls(l){var n;return(n=l.options.scope)==null?void 0:n.id}var Hn,ba,Ct,jn,qn,ds,Tf,jy,zS=(jy=class extends Si{constructor(i,u){super();se(this,qn);se(this,Hn);se(this,ba);se(this,Ct);se(this,jn);I(this,Hn,i),this.setOptions(u),this.bindMethods(),me(this,qn,ds).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(i){var s;const u=this.options;this.options=T(this,Hn).defaultMutationOptions(i),ps(this.options,u)||T(this,Hn).getMutationCache().notify({type:"observerOptionsUpdated",mutation:T(this,Ct),observer:this}),u!=null&&u.mutationKey&&this.options.mutationKey&&rl(u.mutationKey)!==rl(this.options.mutationKey)?this.reset():((s=T(this,Ct))==null?void 0:s.state.status)==="pending"&&T(this,Ct).setOptions(this.options)}onUnsubscribe(){var i;this.hasListeners()||(i=T(this,Ct))==null||i.removeObserver(this)}onMutationUpdate(i){me(this,qn,ds).call(this),me(this,qn,Tf).call(this,i)}getCurrentResult(){return T(this,ba)}reset(){var i;(i=T(this,Ct))==null||i.removeObserver(this),I(this,Ct,void 0),me(this,qn,ds).call(this),me(this,qn,Tf).call(this)}mutate(i,u){var s;return I(this,jn,u),(s=T(this,Ct))==null||s.removeObserver(this),I(this,Ct,T(this,Hn).getMutationCache().build(T(this,Hn),this.options)),T(this,Ct).addObserver(this),T(this,Ct).execute(i)}},Hn=new WeakMap,ba=new WeakMap,Ct=new WeakMap,jn=new WeakMap,qn=new WeakSet,ds=function(){var u;const i=((u=T(this,Ct))==null?void 0:u.state)??Wy();I(this,ba,{...i,isPending:i.status==="pending",isSuccess:i.status==="success",isError:i.status==="error",isIdle:i.status==="idle",mutate:this.mutate,reset:this.reset})},Tf=function(i){et.batch(()=>{var u,s,f,d,h,v,p,m;if(T(this,jn)&&this.hasListeners()){const b=T(this,ba).variables,$=T(this,ba).context,E={client:T(this,Hn),meta:this.options.meta,mutationKey:this.options.mutationKey};(i==null?void 0:i.type)==="success"?((s=(u=T(this,jn)).onSuccess)==null||s.call(u,i.data,b,$,E),(d=(f=T(this,jn)).onSettled)==null||d.call(f,i.data,null,b,$,E)):(i==null?void 0:i.type)==="error"&&((v=(h=T(this,jn)).onError)==null||v.call(h,i.error,b,$,E),(m=(p=T(this,jn)).onSettled)==null||m.call(p,void 0,i.error,b,$,E))}this.listeners.forEach(b=>{b(T(this,ba))})})},jy),gn,Ky,US=(Ky=class extends Si{constructor(n={}){super();se(this,gn);this.config=n,I(this,gn,new Map)}build(n,i,u){const s=i.queryKey,f=i.queryHash??Vf(s,i);let d=this.get(f);return d||(d=new RS({client:n,queryKey:s,queryHash:f,options:n.defaultQueryOptions(i),state:u,defaultOptions:n.getQueryDefaults(s)}),this.add(d)),d}add(n){T(this,gn).has(n.queryHash)||(T(this,gn).set(n.queryHash,n),this.notify({type:"added",query:n}))}remove(n){const i=T(this,gn).get(n.queryHash);i&&(n.destroy(),i===n&&T(this,gn).delete(n.queryHash),this.notify({type:"removed",query:n}))}clear(){et.batch(()=>{this.getAll().forEach(n=>{this.remove(n)})})}get(n){return T(this,gn).get(n)}getAll(){return[...T(this,gn).values()]}find(n){const i={exact:!0,...n};return this.getAll().find(u=>vv(i,u))}findAll(n={}){const i=this.getAll();return Object.keys(n).length>0?i.filter(u=>vv(n,u)):i}notify(n){et.batch(()=>{this.listeners.forEach(i=>{i(n)})})}onFocus(){et.batch(()=>{this.getAll().forEach(n=>{n.onFocus()})})}onOnline(){et.batch(()=>{this.getAll().forEach(n=>{n.onOnline()})})}},gn=new WeakMap,Ky),ke,Sa,Ea,fi,di,$a,hi,pi,qy,Z$=(qy=class{constructor(l={}){se(this,ke);se(this,Sa);se(this,Ea);se(this,fi);se(this,di);se(this,$a);se(this,hi);se(this,pi);I(this,ke,l.queryCache||new US),I(this,Sa,l.mutationCache||new _S),I(this,Ea,l.defaultOptions||{}),I(this,fi,new Map),I(this,di,new Map),I(this,$a,0)}mount(){as(this,$a)._++,T(this,$a)===1&&(I(this,hi,Ff.subscribe(async l=>{l&&(await this.resumePausedMutations(),T(this,ke).onFocus())})),I(this,pi,gs.subscribe(async l=>{l&&(await this.resumePausedMutations(),T(this,ke).onOnline())})))}unmount(){var l,n;as(this,$a)._--,T(this,$a)===0&&((l=T(this,hi))==null||l.call(this),I(this,hi,void 0),(n=T(this,pi))==null||n.call(this),I(this,pi,void 0))}isFetching(l){return T(this,ke).findAll({...l,fetchStatus:"fetching"}).length}isMutating(l){return T(this,Sa).findAll({...l,status:"pending"}).length}getQueryData(l){var i;const n=this.defaultQueryOptions({queryKey:l});return(i=T(this,ke).get(n.queryHash))==null?void 0:i.state.data}ensureQueryData(l){const n=this.defaultQueryOptions(l),i=T(this,ke).build(this,n),u=i.state.data;return u===void 0?this.fetchQuery(l):(l.revalidateIfStale&&i.isStaleByTime(Oa(n.staleTime,i))&&this.prefetchQuery(n),Promise.resolve(u))}getQueriesData(l){return T(this,ke).findAll(l).map(({queryKey:n,state:i})=>{const u=i.data;return[n,u]})}setQueryData(l,n,i){const u=this.defaultQueryOptions({queryKey:l}),s=T(this,ke).get(u.queryHash),f=s==null?void 0:s.state.data,d=mS(n,f);if(d!==void 0)return T(this,ke).build(this,u).setData(d,{...i,manual:!0})}setQueriesData(l,n,i){return et.batch(()=>T(this,ke).findAll(l).map(({queryKey:u})=>[u,this.setQueryData(u,n,i)]))}getQueryState(l){var i;const n=this.defaultQueryOptions({queryKey:l});return(i=T(this,ke).get(n.queryHash))==null?void 0:i.state}removeQueries(l){const n=T(this,ke);et.batch(()=>{n.findAll(l).forEach(i=>{n.remove(i)})})}resetQueries(l,n){const i=T(this,ke);return et.batch(()=>(i.findAll(l).forEach(u=>{u.reset()}),this.refetchQueries({type:"active",...l},n)))}cancelQueries(l,n={}){const i={revert:!0,...n},u=et.batch(()=>T(this,ke).findAll(l).map(s=>s.cancel(i)));return Promise.all(u).then(mt).catch(mt)}invalidateQueries(l,n={}){return et.batch(()=>(T(this,ke).findAll(l).forEach(i=>{i.invalidate()}),(l==null?void 0:l.refetchType)==="none"?Promise.resolve():this.refetchQueries({...l,type:(l==null?void 0:l.refetchType)??(l==null?void 0:l.type)??"active"},n)))}refetchQueries(l,n={}){const i={...n,cancelRefetch:n.cancelRefetch??!0},u=et.batch(()=>T(this,ke).findAll(l).filter(s=>!s.isDisabled()&&!s.isStatic()).map(s=>{let f=s.fetch(void 0,i);return i.throwOnError||(f=f.catch(mt)),s.state.fetchStatus==="paused"?Promise.resolve():f}));return Promise.all(u).then(mt)}fetchQuery(l){const n=this.defaultQueryOptions(l);n.retry===void 0&&(n.retry=!1);const i=T(this,ke).build(this,n);return i.isStaleByTime(Oa(n.staleTime,i))?i.fetch(n):Promise.resolve(i.state.data)}prefetchQuery(l){return this.fetchQuery(l).then(mt).catch(mt)}fetchInfiniteQuery(l){return l.behavior=Tv(l.pages),this.fetchQuery(l)}prefetchInfiniteQuery(l){return this.fetchInfiniteQuery(l).then(mt).catch(mt)}ensureInfiniteQueryData(l){return l.behavior=Tv(l.pages),this.ensureQueryData(l)}resumePausedMutations(){return gs.isOnline()?T(this,Sa).resumePausedMutations():Promise.resolve()}getQueryCache(){return T(this,ke)}getMutationCache(){return T(this,Sa)}getDefaultOptions(){return T(this,Ea)}setDefaultOptions(l){I(this,Ea,l)}setQueryDefaults(l,n){T(this,fi).set(rl(l),{queryKey:l,defaultOptions:n})}getQueryDefaults(l){const n=[...T(this,fi).values()],i={};return n.forEach(u=>{Ar(l,u.queryKey)&&Object.assign(i,u.defaultOptions)}),i}setMutationDefaults(l,n){T(this,di).set(rl(l),{mutationKey:l,defaultOptions:n})}getMutationDefaults(l){const n=[...T(this,di).values()],i={};return n.forEach(u=>{Ar(l,u.mutationKey)&&Object.assign(i,u.defaultOptions)}),i}defaultQueryOptions(l){if(l._defaulted)return l;const n={...T(this,Ea).queries,...this.getQueryDefaults(l.queryKey),...l,_defaulted:!0};return n.queryHash||(n.queryHash=Vf(n.queryKey,n)),n.refetchOnReconnect===void 0&&(n.refetchOnReconnect=n.networkMode!=="always"),n.throwOnError===void 0&&(n.throwOnError=!!n.suspense),!n.networkMode&&n.persister&&(n.networkMode="offlineFirst"),n.queryFn===Xf&&(n.enabled=!1),n}defaultMutationOptions(l){return l!=null&&l._defaulted?l:{...T(this,Ea).mutations,...(l==null?void 0:l.mutationKey)&&this.getMutationDefaults(l.mutationKey),...l,_defaulted:!0}}clear(){T(this,ke).clear(),T(this,Sa).clear()}},ke=new WeakMap,Sa=new WeakMap,Ea=new WeakMap,fi=new WeakMap,di=new WeakMap,$a=new WeakMap,hi=new WeakMap,pi=new WeakMap,qy),Iy=M.createContext(void 0),em=l=>{const n=M.useContext(Iy);if(!n)throw new Error("No QueryClient set, use QueryClientProvider to set one");return n},J$=({client:l,children:n})=>(M.useEffect(()=>(l.mount(),()=>{l.unmount()}),[l]),sS.jsx(Iy.Provider,{value:l,children:n})),tm=M.createContext(!1),HS=()=>M.useContext(tm);tm.Provider;function jS(){let l=!1;return{clearReset:()=>{l=!1},reset:()=>{l=!0},isReset:()=>l}}var KS=M.createContext(jS()),qS=()=>M.useContext(KS),BS=(l,n)=>{(l.suspense||l.throwOnError||l.experimental_prefetchInRender)&&(n.isReset()||(l.retryOnMount=!1))},QS=l=>{M.useEffect(()=>{l.clearReset()},[l])},PS=({result:l,errorResetBoundary:n,throwOnError:i,query:u,suspense:s})=>l.isError&&!n.isReset()&&!l.isFetching&&u&&(s&&l.data===void 0||Yy(i,[l.error,u])),kS=(l,n)=>n.state.data===void 0,GS=l=>{if(l.suspense){const i=s=>s==="static"?s:Math.max(s??1e3,1e3),u=l.staleTime;l.staleTime=typeof u=="function"?(...s)=>i(u(...s)):i(u),typeof l.gcTime=="number"&&(l.gcTime=Math.max(l.gcTime,1e3))}},YS=(l,n)=>l.isLoading&&l.isFetching&&!n,VS=(l,n)=>(l==null?void 0:l.suspense)&&n.isPending,Ov=(l,n,i)=>n.fetchOptimistic(l).catch(()=>{i.clearReset()});function XS(l,n,i){var b,$,E,O,R;const u=HS(),s=qS(),f=em(),d=f.defaultQueryOptions(l);($=(b=f.getDefaultOptions().queries)==null?void 0:b._experimental_beforeQuery)==null||$.call(b,d),d._optimisticResults=u?"isRestoring":"optimistic",GS(d),BS(d,s),QS(s);const h=!f.getQueryCache().get(d.queryHash),[v]=M.useState(()=>new n(f,d)),p=v.getOptimisticResult(d),m=!u&&l.subscribed!==!1;if(M.useSyncExternalStore(M.useCallback(N=>{const P=m?v.subscribe(et.batchCalls(N)):mt;return v.updateResult(),P},[v,m]),()=>v.getCurrentResult(),()=>v.getCurrentResult()),M.useEffect(()=>{v.setOptions(d)},[d,v]),VS(d,p))throw Ov(d,v,s);if(PS({result:p,errorResetBoundary:s,throwOnError:d.throwOnError,query:f.getQueryCache().get(d.queryHash),suspense:d.suspense}))throw p.error;if((O=(E=f.getDefaultOptions().queries)==null?void 0:E._experimental_afterQuery)==null||O.call(E,d,p),d.experimental_prefetchInRender&&!il&&YS(p,u)){const N=h?Ov(d,v,s):(R=f.getQueryCache().get(d.queryHash))==null?void 0:R.promise;N==null||N.catch(mt).finally(()=>{v.updateResult()})}return d.notifyOnChangeProps?p:v.trackResult(p)}function W$(l,n){return XS({...l,enabled:!0,suspense:!0,throwOnError:kS,placeholderData:void 0},AS)}function I$(l,n){const i=em(),[u]=M.useState(()=>new zS(i,l));M.useEffect(()=>{u.setOptions(l)},[u,l]);const s=M.useSyncExternalStore(M.useCallback(d=>u.subscribe(et.batchCalls(d)),[u]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),f=M.useCallback((d,h)=>{u.mutate(d,h).catch(mt)},[u]);if(s.error&&Yy(u.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:f,mutateAsync:s.mutate}}const FS=(l,n,i,u)=>{var f,d,h,v;const s=[i,{code:n,...u||{}}];if((d=(f=l==null?void 0:l.services)==null?void 0:f.logger)!=null&&d.forward)return l.services.logger.forward(s,"warn","react-i18next::",!0);ll(s[0])&&(s[0]=`react-i18next:: ${s[0]}`),(v=(h=l==null?void 0:l.services)==null?void 0:h.logger)!=null&&v.warn?l.services.logger.warn(...s):console!=null&&console.warn&&console.warn(...s)},Cv={},xf=(l,n,i,u)=>{ll(i)&&Cv[i]||(ll(i)&&(Cv[i]=new Date),FS(l,n,i,u))},nm=(l,n)=>()=>{if(l.isInitialized)n();else{const i=()=>{setTimeout(()=>{l.off("initialized",i)},0),n()};l.on("initialized",i)}},Of=(l,n,i)=>{l.loadNamespaces(n,nm(l,i))},wv=(l,n,i,u)=>{if(ll(i)&&(i=[i]),l.options.preload&&l.options.preload.indexOf(n)>-1)return Of(l,i,u);i.forEach(s=>{l.options.ns.indexOf(s)<0&&l.options.ns.push(s)}),l.loadLanguages(n,nm(l,u))},ZS=(l,n,i={})=>!n.languages||!n.languages.length?(xf(n,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:n.languages}),!0):n.hasLoadedNamespace(l,{lng:i.lng,precheck:(u,s)=>{if(i.bindI18n&&i.bindI18n.indexOf("languageChanging")>-1&&u.services.backendConnector.backend&&u.isLanguageChangingTo&&!s(u.isLanguageChangingTo,l))return!1}}),ll=l=>typeof l=="string",JS=l=>typeof l=="object"&&l!==null,WS=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,IS={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},eE=l=>IS[l],tE=l=>l.replace(WS,eE);let Cf={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:tE};const nE=(l={})=>{Cf={...Cf,...l}},aE=()=>Cf;let am;const lE=l=>{am=l},iE=()=>am,rE={type:"3rdParty",init(l){nE(l.options.react),lE(l)}},uE=M.createContext();class sE{constructor(){this.usedNamespaces={}}addUsedNamespaces(n){n.forEach(i=>{this.usedNamespaces[i]||(this.usedNamespaces[i]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const oE=(l,n)=>{const i=M.useRef();return M.useEffect(()=>{i.current=l},[l,n]),i.current},lm=(l,n,i,u)=>l.getFixedT(n,i,u),cE=(l,n,i,u)=>M.useCallback(lm(l,n,i,u),[l,n,i,u]),fE=(l,n={})=>{var B,V,ee,re;const{i18n:i}=n,{i18n:u,defaultNS:s}=M.useContext(uE)||{},f=i||u||iE();if(f&&!f.reportNamespaces&&(f.reportNamespaces=new sE),!f){xf(f,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const te=(F,U)=>ll(U)?U:JS(U)&&ll(U.defaultValue)?U.defaultValue:Array.isArray(F)?F[F.length-1]:F,ae=[te,{},!1];return ae.t=te,ae.i18n={},ae.ready=!1,ae}(B=f.options.react)!=null&&B.wait&&xf(f,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const d={...aE(),...f.options.react,...n},{useSuspense:h,keyPrefix:v}=d;let p=s||((V=f.options)==null?void 0:V.defaultNS);p=ll(p)?[p]:p||["translation"],(re=(ee=f.reportNamespaces).addUsedNamespaces)==null||re.call(ee,p);const m=(f.isInitialized||f.initializedStoreOnce)&&p.every(te=>ZS(te,f,d)),b=cE(f,n.lng||null,d.nsMode==="fallback"?p:p[0],v),$=()=>b,E=()=>lm(f,n.lng||null,d.nsMode==="fallback"?p:p[0],v),[O,R]=M.useState($);let N=p.join();n.lng&&(N=`${n.lng}${N}`);const P=oE(N),K=M.useRef(!0);M.useEffect(()=>{const{bindI18n:te,bindI18nStore:ae}=d;K.current=!0,!m&&!h&&(n.lng?wv(f,n.lng,p,()=>{K.current&&R(E)}):Of(f,p,()=>{K.current&&R(E)})),m&&P&&P!==N&&K.current&&R(E);const F=()=>{K.current&&R(E)};return te&&(f==null||f.on(te,F)),ae&&(f==null||f.store.on(ae,F)),()=>{K.current=!1,f&&te&&(te==null||te.split(" ").forEach(U=>f.off(U,F))),ae&&f&&ae.split(" ").forEach(U=>f.store.off(U,F))}},[f,N]),M.useEffect(()=>{K.current&&m&&R($)},[f,v,m]);const k=[O,f,m];if(k.t=O,k.i18n=f,k.ready=m,m||!m&&!h)return k;throw new Promise(te=>{n.lng?wv(f,n.lng,p,()=>te()):Of(f,p,()=>te())})};M.createContext(null);M.createContext(null);M.createContext(null);M.createContext(null);M.createContext(null);const dE=M.createContext({}),Ke=typeof document<"u"?ce.useLayoutEffect:()=>{};var tf;const hE=(tf=ce.useInsertionEffect)!==null&&tf!==void 0?tf:Ke;function Zt(l){const n=M.useRef(null);return hE(()=>{n.current=l},[l]),M.useCallback((...i)=>{const u=n.current;return u==null?void 0:u(...i)},[])}function pE(l){let[n,i]=M.useState(l),u=M.useRef(null),s=Zt(()=>{if(!u.current)return;let d=u.current.next();if(d.done){u.current=null;return}n===d.value?s():i(d.value)});Ke(()=>{u.current&&s()});let f=Zt(d=>{u.current=d(n),s()});return[n,f]}const im={prefix:String(Math.round(Math.random()*1e10)),current:0},rm=ce.createContext(im),gE=ce.createContext(!1);let nf=new WeakMap;function vE(l=!1){let n=M.useContext(rm),i=M.useRef(null);if(i.current===null&&!l){var u,s;let f=(s=ce.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)===null||s===void 0||(u=s.ReactCurrentOwner)===null||u===void 0?void 0:u.current;if(f){let d=nf.get(f);d==null?nf.set(f,{id:n.current,state:f.memoizedState}):f.memoizedState!==d.state&&(n.current=d.id,nf.delete(f))}i.current=++n.current}return i.current}function yE(l){let n=M.useContext(rm),i=vE(!!l),u=`react-aria${n.prefix}`;return l||`${u}-${i}`}function mE(l){let n=ce.useId(),[i]=M.useState(Jf()),u=i?"react-aria":`react-aria${im.prefix}`;return l||`${u}-${n}`}const bE=typeof ce.useId=="function"?mE:yE;function SE(){return!1}function EE(){return!0}function $E(l){return()=>{}}function Jf(){return typeof ce.useSyncExternalStore=="function"?ce.useSyncExternalStore($E,SE,EE):M.useContext(gE)}let TE=!!(typeof window<"u"&&window.document&&window.document.createElement),ei=new Map,xr;typeof FinalizationRegistry<"u"&&(xr=new FinalizationRegistry(l=>{ei.delete(l)}));function gi(l){let[n,i]=M.useState(l),u=M.useRef(null),s=bE(n),f=M.useRef(null);if(xr&&xr.register(f,s),TE){const d=ei.get(s);d&&!d.includes(u)?d.push(u):ei.set(s,[u])}return Ke(()=>{let d=s;return()=>{xr&&xr.unregister(f),ei.delete(d)}},[s]),M.useEffect(()=>{let d=u.current;return d&&i(d),()=>{d&&(u.current=null)}}),s}function xE(l,n){if(l===n)return l;let i=ei.get(l);if(i)return i.forEach(s=>s.current=n),n;let u=ei.get(n);return u?(u.forEach(s=>s.current=l),l):n}function OE(l=[]){let n=gi(),[i,u]=pE(n),s=M.useCallback(()=>{u(function*(){yield n,yield document.getElementById(n)?n:void 0})},[n,u]);return Ke(s,[n,s,...l]),i}function Mr(...l){return(...n)=>{for(let i of l)typeof i=="function"&&i(...n)}}const _e=l=>{var n;return(n=l==null?void 0:l.ownerDocument)!==null&&n!==void 0?n:document},Jt=l=>l&&"window"in l&&l.window===l?l:_e(l).defaultView||window;function CE(l){return l!==null&&typeof l=="object"&&"nodeType"in l&&typeof l.nodeType=="number"}function wE(l){return CE(l)&&l.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in l}let RE=!1;function Os(){return RE}function wt(l,n){if(!Os())return n&&l?l.contains(n):!1;if(!l||!n)return!1;let i=n;for(;i!==null;){if(i===l)return!0;i.tagName==="SLOT"&&i.assignedSlot?i=i.assignedSlot.parentNode:wE(i)?i=i.host:i=i.parentNode}return!1}const Rt=(l=document)=>{var n;if(!Os())return l.activeElement;let i=l.activeElement;for(;i&&"shadowRoot"in i&&(!((n=i.shadowRoot)===null||n===void 0)&&n.activeElement);)i=i.shadowRoot.activeElement;return i};function Xe(l){return Os()&&l.target.shadowRoot&&l.composedPath?l.composedPath()[0]:l.target}class AE{get currentNode(){return this._currentNode}set currentNode(n){if(!wt(this.root,n))throw new Error("Cannot set currentNode to a node that is not contained by the root node.");const i=[];let u=n,s=n;for(this._currentNode=n;u&&u!==this.root;)if(u.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const d=u,h=this._doc.createTreeWalker(d,this.whatToShow,{acceptNode:this._acceptNode});i.push(h),h.currentNode=s,this._currentSetFor.add(h),u=s=d.host}else u=u.parentNode;const f=this._doc.createTreeWalker(this.root,this.whatToShow,{acceptNode:this._acceptNode});i.push(f),f.currentNode=s,this._currentSetFor.add(f),this._walkerStack=i}get doc(){return this._doc}firstChild(){let n=this.currentNode,i=this.nextNode();return wt(n,i)?(i&&(this.currentNode=i),i):(this.currentNode=n,null)}lastChild(){let i=this._walkerStack[0].lastChild();return i&&(this.currentNode=i),i}nextNode(){const n=this._walkerStack[0].nextNode();if(n){if(n.shadowRoot){var i;let s;if(typeof this.filter=="function"?s=this.filter(n):!((i=this.filter)===null||i===void 0)&&i.acceptNode&&(s=this.filter.acceptNode(n)),s===NodeFilter.FILTER_ACCEPT)return this.currentNode=n,n;let f=this.nextNode();return f&&(this.currentNode=f),f}return n&&(this.currentNode=n),n}else if(this._walkerStack.length>1){this._walkerStack.shift();let u=this.nextNode();return u&&(this.currentNode=u),u}else return null}previousNode(){const n=this._walkerStack[0];if(n.currentNode===n.root){if(this._currentSetFor.has(n))if(this._currentSetFor.delete(n),this._walkerStack.length>1){this._walkerStack.shift();let s=this.previousNode();return s&&(this.currentNode=s),s}else return null;return null}const i=n.previousNode();if(i){if(i.shadowRoot){var u;let f;if(typeof this.filter=="function"?f=this.filter(i):!((u=this.filter)===null||u===void 0)&&u.acceptNode&&(f=this.filter.acceptNode(i)),f===NodeFilter.FILTER_ACCEPT)return i&&(this.currentNode=i),i;let d=this.lastChild();return d&&(this.currentNode=d),d}return i&&(this.currentNode=i),i}else if(this._walkerStack.length>1){this._walkerStack.shift();let s=this.previousNode();return s&&(this.currentNode=s),s}else return null}nextSibling(){return null}previousSibling(){return null}parentNode(){return null}constructor(n,i,u,s){this._walkerStack=[],this._currentSetFor=new Set,this._acceptNode=d=>{if(d.nodeType===Node.ELEMENT_NODE){const v=d.shadowRoot;if(v){const p=this._doc.createTreeWalker(v,this.whatToShow,{acceptNode:this._acceptNode});return this._walkerStack.unshift(p),NodeFilter.FILTER_ACCEPT}else{var h;if(typeof this.filter=="function")return this.filter(d);if(!((h=this.filter)===null||h===void 0)&&h.acceptNode)return this.filter.acceptNode(d);if(this.filter===null)return NodeFilter.FILTER_ACCEPT}}return NodeFilter.FILTER_SKIP},this._doc=n,this.root=i,this.filter=s??null,this.whatToShow=u??NodeFilter.SHOW_ALL,this._currentNode=i,this._walkerStack.unshift(n.createTreeWalker(i,u,this._acceptNode));const f=i.shadowRoot;if(f){const d=this._doc.createTreeWalker(f,this.whatToShow,{acceptNode:this._acceptNode});this._walkerStack.unshift(d)}}}function ME(l,n,i,u){return Os()?new AE(l,n,i,u):l.createTreeWalker(n,i,u)}function um(l){var n,i,u="";if(typeof l=="string"||typeof l=="number")u+=l;else if(typeof l=="object")if(Array.isArray(l)){var s=l.length;for(n=0;n<s;n++)l[n]&&(i=um(l[n]))&&(u&&(u+=" "),u+=i)}else for(i in l)l[i]&&(u&&(u+=" "),u+=i);return u}function NE(){for(var l,n,i=0,u="",s=arguments.length;i<s;i++)(l=arguments[i])&&(n=um(l))&&(u&&(u+=" "),u+=n);return u}function jt(...l){let n={...l[0]};for(let i=1;i<l.length;i++){let u=l[i];for(let s in u){let f=n[s],d=u[s];typeof f=="function"&&typeof d=="function"&&s[0]==="o"&&s[1]==="n"&&s.charCodeAt(2)>=65&&s.charCodeAt(2)<=90?n[s]=Mr(f,d):(s==="className"||s==="UNSAFE_className")&&typeof f=="string"&&typeof d=="string"?n[s]=NE(f,d):s==="id"&&f&&d?n.id=xE(f,d):n[s]=d!==void 0?d:f}}return n}function DE(...l){return l.length===1&&l[0]?l[0]:n=>{let i=!1;const u=l.map(s=>{const f=Rv(s,n);return i||(i=typeof f=="function"),f});if(i)return()=>{u.forEach((s,f)=>{typeof s=="function"?s():Rv(l[f],null)})}}}function Rv(l,n){if(typeof l=="function")return l(n);l!=null&&(l.current=n)}const LE=new Set(["id"]),_E=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),zE=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),UE=new Set(["dir","lang","hidden","inert","translate"]),Av=new Set(["onClick","onAuxClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut","onGotPointerCapture","onLostPointerCapture","onScroll","onWheel","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionCancel","onTransitionEnd","onTransitionRun","onTransitionStart"]),HE=/^(data-.*)$/;function Br(l,n={}){let{labelable:i,isLink:u,global:s,events:f=s,propNames:d}=n,h={};for(const v in l)Object.prototype.hasOwnProperty.call(l,v)&&(LE.has(v)||i&&_E.has(v)||u&&zE.has(v)||s&&UE.has(v)||f&&Av.has(v)||v.endsWith("Capture")&&Av.has(v.slice(0,-7))||d!=null&&d.has(v)||HE.test(v))&&(h[v]=l[v]);return h}function vi(l){if(jE())l.focus({preventScroll:!0});else{let n=KE(l);l.focus(),qE(n)}}let is=null;function jE(){if(is==null){is=!1;try{document.createElement("div").focus({get preventScroll(){return is=!0,!0}})}catch{}}return is}function KE(l){let n=l.parentNode,i=[],u=document.scrollingElement||document.documentElement;for(;n instanceof HTMLElement&&n!==u;)(n.offsetHeight<n.scrollHeight||n.offsetWidth<n.scrollWidth)&&i.push({element:n,scrollTop:n.scrollTop,scrollLeft:n.scrollLeft}),n=n.parentNode;return u instanceof HTMLElement&&i.push({element:u,scrollTop:u.scrollTop,scrollLeft:u.scrollLeft}),i}function qE(l){for(let{element:n,scrollTop:i,scrollLeft:u}of l)n.scrollTop=i,n.scrollLeft=u}function Cs(l){var n;if(typeof window>"u"||window.navigator==null)return!1;let i=(n=window.navigator.userAgentData)===null||n===void 0?void 0:n.brands;return Array.isArray(i)&&i.some(u=>l.test(u.brand))||l.test(window.navigator.userAgent)}function Wf(l){var n;return typeof window<"u"&&window.navigator!=null?l.test(((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.platform)||window.navigator.platform):!1}function Bn(l){let n=null;return()=>(n==null&&(n=l()),n)}const yi=Bn(function(){return Wf(/^Mac/i)}),BE=Bn(function(){return Wf(/^iPhone/i)}),sm=Bn(function(){return Wf(/^iPad/i)||yi()&&navigator.maxTouchPoints>1}),ws=Bn(function(){return BE()||sm()}),e4=Bn(function(){return yi()||ws()}),om=Bn(function(){return Cs(/AppleWebKit/i)&&!cm()}),cm=Bn(function(){return Cs(/Chrome/i)}),If=Bn(function(){return Cs(/Android/i)}),QE=Bn(function(){return Cs(/Firefox/i)}),PE=M.createContext({isNative:!0,open:VE,useHref:l=>l});function kE(){return M.useContext(PE)}function GE(l,n){let i=l.getAttribute("target");return(!i||i==="_self")&&l.origin===location.origin&&!l.hasAttribute("download")&&!n.metaKey&&!n.ctrlKey&&!n.altKey&&!n.shiftKey}function mi(l,n,i=!0){var u,s;let{metaKey:f,ctrlKey:d,altKey:h,shiftKey:v}=n;QE()&&(!((s=window.event)===null||s===void 0||(u=s.type)===null||u===void 0)&&u.startsWith("key"))&&l.target==="_blank"&&(yi()?f=!0:d=!0);let p=om()&&yi()&&!sm()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:f,ctrlKey:d,altKey:h,shiftKey:v}):new MouseEvent("click",{metaKey:f,ctrlKey:d,altKey:h,shiftKey:v,bubbles:!0,cancelable:!0});mi.isOpening=i,vi(l),l.dispatchEvent(p),mi.isOpening=!1}mi.isOpening=!1;function YE(l,n){if(l instanceof HTMLAnchorElement)n(l);else if(l.hasAttribute("data-href")){let i=document.createElement("a");i.href=l.getAttribute("data-href"),l.hasAttribute("data-target")&&(i.target=l.getAttribute("data-target")),l.hasAttribute("data-rel")&&(i.rel=l.getAttribute("data-rel")),l.hasAttribute("data-download")&&(i.download=l.getAttribute("data-download")),l.hasAttribute("data-ping")&&(i.ping=l.getAttribute("data-ping")),l.hasAttribute("data-referrer-policy")&&(i.referrerPolicy=l.getAttribute("data-referrer-policy")),l.appendChild(i),n(i),l.removeChild(i)}}function VE(l,n){YE(l,i=>mi(i,n))}function t4(l){let n=kE();var i;const u=n.useHref((i=l==null?void 0:l.href)!==null&&i!==void 0?i:"");return{href:l!=null&&l.href?u:void 0,target:l==null?void 0:l.target,rel:l==null?void 0:l.rel,download:l==null?void 0:l.download,ping:l==null?void 0:l.ping,referrerPolicy:l==null?void 0:l.referrerPolicy}}function n4(l,n,i,u){!n.isNative&&l.currentTarget instanceof HTMLAnchorElement&&l.currentTarget.href&&!l.isDefaultPrevented()&&GE(l.currentTarget,l)&&i&&(l.preventDefault(),n.open(l.currentTarget,l,i,u))}let ha=new Map,wf=new Set;function Mv(){if(typeof window>"u")return;function l(u){return"propertyName"in u}let n=u=>{if(!l(u)||!u.target)return;let s=ha.get(u.target);s||(s=new Set,ha.set(u.target,s),u.target.addEventListener("transitioncancel",i,{once:!0})),s.add(u.propertyName)},i=u=>{if(!l(u)||!u.target)return;let s=ha.get(u.target);if(s&&(s.delete(u.propertyName),s.size===0&&(u.target.removeEventListener("transitioncancel",i),ha.delete(u.target)),ha.size===0)){for(let f of wf)f();wf.clear()}};document.body.addEventListener("transitionrun",n),document.body.addEventListener("transitionend",i)}typeof document<"u"&&(document.readyState!=="loading"?Mv():document.addEventListener("DOMContentLoaded",Mv));function XE(){for(const[l]of ha)"isConnected"in l&&!l.isConnected&&ha.delete(l)}function fm(l){requestAnimationFrame(()=>{XE(),ha.size===0?l():wf.add(l)})}function ed(){let l=M.useRef(new Map),n=M.useCallback((s,f,d,h)=>{let v=h!=null&&h.once?(...p)=>{l.current.delete(d),d(...p)}:d;l.current.set(d,{type:f,eventTarget:s,fn:v,options:h}),s.addEventListener(f,v,h)},[]),i=M.useCallback((s,f,d,h)=>{var v;let p=((v=l.current.get(d))===null||v===void 0?void 0:v.fn)||d;s.removeEventListener(f,p,h),l.current.delete(d)},[]),u=M.useCallback(()=>{l.current.forEach((s,f)=>{i(s.eventTarget,s.type,f,s.options)})},[i]);return M.useEffect(()=>u,[u]),{addGlobalListener:n,removeGlobalListener:i,removeAllGlobalListeners:u}}function FE(l,n){let{id:i,"aria-label":u,"aria-labelledby":s}=l;return i=gi(i),s&&u?s=[...new Set([i,...s.trim().split(/\s+/)])].join(" "):s&&(s=s.trim().split(/\s+/).join(" ")),!u&&!s&&n&&(u=n),{id:i,"aria-label":u,"aria-labelledby":s}}function dm(l){const n=M.useRef(null),i=M.useRef(void 0),u=M.useCallback(s=>{if(typeof l=="function"){const f=l,d=f(s);return()=>{typeof d=="function"?d():f(null)}}else if(l)return l.current=s,()=>{l.current=null}},[l]);return M.useMemo(()=>({get current(){return n.current},set current(s){n.current=s,i.current&&(i.current(),i.current=void 0),s!=null&&(i.current=u(s))}}),[u])}function ZE(){return typeof window.ResizeObserver<"u"}function Rf(l){const{ref:n,box:i,onResize:u}=l;M.useEffect(()=>{let s=n==null?void 0:n.current;if(s)if(ZE()){const f=new window.ResizeObserver(d=>{d.length&&u()});return f.observe(s,{box:i}),()=>{s&&f.unobserve(s)}}else return window.addEventListener("resize",u,!1),()=>{window.removeEventListener("resize",u,!1)}},[u,n,i])}function td(l,n){Ke(()=>{if(l&&l.ref&&n)return l.ref.current=n.current,()=>{l.ref&&(l.ref.current=null)}})}function Nv(l,n){if(!l)return!1;let i=window.getComputedStyle(l),u=/(auto|scroll)/.test(i.overflow+i.overflowX+i.overflowY);return u&&n&&(u=l.scrollHeight!==l.clientHeight||l.scrollWidth!==l.clientWidth),u}function hm(l,n){let i=l;for(Nv(i,n)&&(i=i.parentElement);i&&!Nv(i,n);)i=i.parentElement;return i||document.scrollingElement||document.documentElement}function pm(l){return l.pointerType===""&&l.isTrusted?!0:If()&&l.pointerType?l.type==="click"&&l.buttons===1:l.detail===0&&!l.pointerType}function JE(l){return!If()&&l.width===0&&l.height===0||l.width===1&&l.height===1&&l.pressure===0&&l.detail===0&&l.pointerType==="mouse"}var gm=Qy();const WE=By(gm);function IE(l,n=!0){let[i,u]=M.useState(!0),s=i&&n;return Ke(()=>{if(s&&l.current&&"getAnimations"in l.current)for(let f of l.current.getAnimations())f instanceof CSSTransition&&f.cancel()},[l,s]),vm(l,s,M.useCallback(()=>u(!1),[])),s}function e2(l,n){let[i,u]=M.useState(n?"open":"closed");switch(i){case"open":n||u("exiting");break;case"closed":case"exiting":n&&u("open");break}let s=i==="exiting";return vm(l,s,M.useCallback(()=>{u(f=>f==="exiting"?"closed":f)},[])),s}function vm(l,n,i){Ke(()=>{if(n&&l.current){if(!("getAnimations"in l.current)){i();return}let u=l.current.getAnimations();if(u.length===0){i();return}let s=!1;return Promise.all(u.map(f=>f.finished)).then(()=>{s||gm.flushSync(()=>{i()})}).catch(()=>{}),()=>{s=!0}}},[l,n,i])}const t2=typeof Element<"u"&&"checkVisibility"in Element.prototype;function n2(l){const n=Jt(l);if(!(l instanceof n.HTMLElement)&&!(l instanceof n.SVGElement))return!1;let{display:i,visibility:u}=l.style,s=i!=="none"&&u!=="hidden"&&u!=="collapse";if(s){const{getComputedStyle:f}=l.ownerDocument.defaultView;let{display:d,visibility:h}=f(l);s=d!=="none"&&h!=="hidden"&&h!=="collapse"}return s}function a2(l,n){return!l.hasAttribute("hidden")&&!l.hasAttribute("data-react-aria-prevent-focus")&&(l.nodeName==="DETAILS"&&n&&n.nodeName!=="SUMMARY"?l.hasAttribute("open"):!0)}function nd(l,n){return t2?l.checkVisibility()&&!l.closest("[data-react-aria-prevent-focus]"):l.nodeName!=="#comment"&&n2(l)&&a2(l,n)&&(!l.parentElement||nd(l.parentElement,l))}const ad=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])',"permission"],l2=ad.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";ad.push('[tabindex]:not([tabindex="-1"]):not([disabled])');const i2=ad.join(':not([hidden]):not([tabindex="-1"]),');function ym(l){return l.matches(l2)&&nd(l)&&!mm(l)}function r2(l){return l.matches(i2)&&nd(l)&&!mm(l)}function mm(l){let n=l;for(;n!=null;){if(n instanceof n.ownerDocument.defaultView.HTMLElement&&n.inert)return!0;n=n.parentElement}return!1}function u2(l,n,i){let[u,s]=M.useState(l||n),f=M.useRef(l!==void 0),d=l!==void 0;M.useEffect(()=>{f.current,f.current=d},[d]);let h=d?l:u,v=M.useCallback((p,...m)=>{let b=($,...E)=>{i&&(Object.is(h,$)||i($,...E)),d||(h=$)};typeof p=="function"?s((E,...O)=>{let R=p(d?h:E,...O);return b(R,...m),d?E:R}):(d||s(p),b(p,...m))},[d,h,i]);return[h,v]}function Af(l,n=-1/0,i=1/0){return Math.min(Math.max(l,n),i)}const Mf=Symbol("default");function bm({values:l,children:n}){for(let[i,u]of l)n=ce.createElement(i.Provider,{value:u},n);return n}function ld(l){let{className:n,style:i,children:u,defaultClassName:s,defaultChildren:f,defaultStyle:d,values:h}=l;return M.useMemo(()=>{let v,p,m;return typeof n=="function"?v=n({...h,defaultClassName:s}):v=n,typeof i=="function"?p=i({...h,defaultStyle:d||{}}):p=i,typeof u=="function"?m=u({...h,defaultChildren:f}):u==null?m=f:m=u,{className:v??s,style:p||d?{...d,...p}:void 0,children:m??f,"data-rac":""}},[n,i,u,s,f,d,h])}function s2(l,n){let i=M.useContext(l);if(n===null)return null;if(i&&typeof i=="object"&&"slots"in i&&i.slots){let u=n||Mf;if(!i.slots[u]){let s=new Intl.ListFormat().format(Object.keys(i.slots).map(d=>`"${d}"`)),f=n?`Invalid slot "${n}".`:"A slot prop is required.";throw new Error(`${f} Valid slot names are ${s}.`)}return i.slots[u]}return i}function id(l,n,i){let u=s2(i,l.slot)||{},{ref:s,...f}=u,d=dm(M.useMemo(()=>DE(n,s),[n,s])),h=jt(f,l);return"style"in f&&f.style&&"style"in l&&l.style&&(typeof f.style=="function"||typeof l.style=="function"?h.style=v=>{let p=typeof f.style=="function"?f.style(v):f.style,m={...v.defaultStyle,...p},b=typeof l.style=="function"?l.style({...v,defaultStyle:m}):l.style;return{...m,...b}}:h.style={...f.style,...l.style}),[h,d]}function a4(l=!0){let[n,i]=M.useState(l),u=M.useRef(!1),s=M.useCallback(f=>{u.current=!0,i(!!f)},[]);return Ke(()=>{u.current||i(!1)},[]),[s,n]}function l4(l){const n=/^(data-.*)$/;let i={};for(const u in l)n.test(u)||(i[u]=l[u]);return i}const Sm=7e3;let fn=null;function Dv(l,n="assertive",i=Sm){fn?fn.announce(l,n,i):(fn=new o2,(typeof IS_REACT_ACT_ENVIRONMENT=="boolean"?IS_REACT_ACT_ENVIRONMENT:typeof jest<"u")?fn.announce(l,n,i):setTimeout(()=>{fn!=null&&fn.isAttached()&&(fn==null||fn.announce(l,n,i))},100))}class o2{isAttached(){var n;return(n=this.node)===null||n===void 0?void 0:n.isConnected}createLog(n){let i=document.createElement("div");return i.setAttribute("role","log"),i.setAttribute("aria-live",n),i.setAttribute("aria-relevant","additions"),i}destroy(){this.node&&(document.body.removeChild(this.node),this.node=null)}announce(n,i="assertive",u=Sm){var s,f;if(!this.node)return;let d=document.createElement("div");typeof n=="object"?(d.setAttribute("role","img"),d.setAttribute("aria-labelledby",n["aria-labelledby"])):d.textContent=n,i==="assertive"?(s=this.assertiveLog)===null||s===void 0||s.appendChild(d):(f=this.politeLog)===null||f===void 0||f.appendChild(d),n!==""&&setTimeout(()=>{d.remove()},u)}clear(n){this.node&&((!n||n==="assertive")&&this.assertiveLog&&(this.assertiveLog.innerHTML=""),(!n||n==="polite")&&this.politeLog&&(this.politeLog.innerHTML=""))}constructor(){this.node=null,this.assertiveLog=null,this.politeLog=null,typeof document<"u"&&(this.node=document.createElement("div"),this.node.dataset.liveAnnouncer="true",Object.assign(this.node.style,{border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"}),this.assertiveLog=this.createLog("assertive"),this.node.appendChild(this.assertiveLog),this.politeLog=this.createLog("polite"),this.node.appendChild(this.politeLog),document.body.prepend(this.node))}}const vn={top:"top",bottom:"top",left:"left",right:"left"},vs={top:"bottom",bottom:"top",left:"right",right:"left"},c2={top:"left",left:"top"},Nf={top:"height",left:"width"},Em={width:"totalWidth",height:"totalHeight"},rs={};let it=typeof document<"u"?window.visualViewport:null;function Lv(l){let n=0,i=0,u=0,s=0,f=0,d=0,h={};var v;let p=((v=it==null?void 0:it.scale)!==null&&v!==void 0?v:1)>1;if(l.tagName==="BODY"){let O=document.documentElement;u=O.clientWidth,s=O.clientHeight;var m;n=(m=it==null?void 0:it.width)!==null&&m!==void 0?m:u;var b;i=(b=it==null?void 0:it.height)!==null&&b!==void 0?b:s,h.top=O.scrollTop||l.scrollTop,h.left=O.scrollLeft||l.scrollLeft,it&&(f=it.offsetTop,d=it.offsetLeft)}else({width:n,height:i,top:f,left:d}=ti(l,!1)),h.top=l.scrollTop,h.left=l.scrollLeft,u=n,s=i;if(om()&&(l.tagName==="BODY"||l.tagName==="HTML")&&p){h.top=0,h.left=0;var $;f=($=it==null?void 0:it.pageTop)!==null&&$!==void 0?$:0;var E;d=(E=it==null?void 0:it.pageLeft)!==null&&E!==void 0?E:0}return{width:n,height:i,totalWidth:u,totalHeight:s,scroll:h,top:f,left:d}}function f2(l){return{top:l.scrollTop,left:l.scrollLeft,width:l.scrollWidth,height:l.scrollHeight}}function _v(l,n,i,u,s,f,d){var h;let v=(h=s.scroll[l])!==null&&h!==void 0?h:0,p=u[Nf[l]],m=u.scroll[vn[l]]+f,b=p+u.scroll[vn[l]]-f,$=n-v+d[l]-u[vn[l]],E=n-v+i+d[l]-u[vn[l]];return $<m?m-$:E>b?Math.max(b-E,m-$):0}function d2(l){let n=window.getComputedStyle(l);return{top:parseInt(n.marginTop,10)||0,bottom:parseInt(n.marginBottom,10)||0,left:parseInt(n.marginLeft,10)||0,right:parseInt(n.marginRight,10)||0}}function zv(l){if(rs[l])return rs[l];let[n,i]=l.split(" "),u=vn[n]||"right",s=c2[u];vn[i]||(i="center");let f=Nf[u],d=Nf[s];return rs[l]={placement:n,crossPlacement:i,axis:u,crossAxis:s,size:f,crossSize:d},rs[l]}function af(l,n,i,u,s,f,d,h,v,p){let{placement:m,crossPlacement:b,axis:$,crossAxis:E,size:O,crossSize:R}=u,N={};var P;N[E]=(P=l[E])!==null&&P!==void 0?P:0;var K,k,B,V;b==="center"?N[E]+=(((K=l[R])!==null&&K!==void 0?K:0)-((k=i[R])!==null&&k!==void 0?k:0))/2:b!==E&&(N[E]+=((B=l[R])!==null&&B!==void 0?B:0)-((V=i[R])!==null&&V!==void 0?V:0)),N[E]+=f;const ee=l[E]-i[R]+v+p,re=l[E]+l[R]-v-p;if(N[E]=Af(N[E],ee,re),m===$){const te=h?d[O]:n[Em[O]];N[vs[$]]=Math.floor(te-l[$]+s)}else N[$]=Math.floor(l[$]+l[O]+s);return N}function h2(l,n,i,u,s,f,d,h){const v=u?i.height:n[Em.height];var p;let m=l.top!=null?i.top+l.top:i.top+(v-((p=l.bottom)!==null&&p!==void 0?p:0)-d);var b,$,E,O,R,N;let P=h!=="top"?Math.max(0,n.height+n.top+((b=n.scroll.top)!==null&&b!==void 0?b:0)-m-((($=s.top)!==null&&$!==void 0?$:0)+((E=s.bottom)!==null&&E!==void 0?E:0)+f)):Math.max(0,m+d-(n.top+((O=n.scroll.top)!==null&&O!==void 0?O:0))-(((R=s.top)!==null&&R!==void 0?R:0)+((N=s.bottom)!==null&&N!==void 0?N:0)+f));return Math.min(n.height-f*2,P)}function Uv(l,n,i,u,s,f){let{placement:d,axis:h,size:v}=f;var p,m;if(d===h)return Math.max(0,i[h]-l[h]-((p=l.scroll[h])!==null&&p!==void 0?p:0)+n[h]-((m=u[h])!==null&&m!==void 0?m:0)-u[vs[h]]-s);var b;return Math.max(0,l[v]+l[h]+l.scroll[h]-n[h]-i[h]-i[v]-((b=u[h])!==null&&b!==void 0?b:0)-u[vs[h]]-s)}function p2(l,n,i,u,s,f,d,h,v,p,m,b,$,E,O,R){let N=zv(l),{size:P,crossAxis:K,crossSize:k,placement:B,crossPlacement:V}=N,ee=af(n,h,i,N,m,b,p,$,O,R),re=m,te=Uv(h,p,n,s,f+m,N);if(d&&u[P]>te){let ut=zv(`${vs[B]} ${V}`),Ue=af(n,h,i,ut,m,b,p,$,O,R);Uv(h,p,n,s,f+m,ut)>te&&(N=ut,ee=Ue,re=m)}let ae="bottom";N.axis==="top"?N.placement==="top"?ae="top":N.placement==="bottom"&&(ae="bottom"):N.crossAxis==="top"&&(N.crossPlacement==="top"?ae="bottom":N.crossPlacement==="bottom"&&(ae="top"));let F=_v(K,ee[K],i[k],h,v,f,p);ee[K]+=F;let U=h2(ee,h,p,$,s,f,i.height,ae);E&&E<U&&(U=E),i.height=Math.min(i.height,U),ee=af(n,h,i,N,re,b,p,$,O,R),F=_v(K,ee[K],i[k],h,v,f,p),ee[K]+=F;let X={},le=n[K]-ee[K]-s[vn[K]],Y=le+.5*n[k];const pe=O/2+R;var C,G,W,oe;const x=vn[K]==="left"?((C=s.left)!==null&&C!==void 0?C:0)+((G=s.right)!==null&&G!==void 0?G:0):((W=s.top)!==null&&W!==void 0?W:0)+((oe=s.bottom)!==null&&oe!==void 0?oe:0),H=i[k]-x-O/2-R,J=n[K]+O/2-(ee[K]+s[vn[K]]),Z=n[K]+n[k]-O/2-(ee[K]+s[vn[K]]),ue=Af(Y,J,Z);X[K]=Af(ue,pe,H),{placement:B,crossPlacement:V}=N,O?le=X[K]:V==="right"?le+=n[k]:V==="center"&&(le+=n[k]/2);let Se=B==="left"||B==="top"?i[P]:0,ge={x:B==="top"||B==="bottom"?le:Se,y:B==="left"||B==="right"?le:Se};return{position:ee,maxHeight:U,arrowOffsetLeft:X.left,arrowOffsetTop:X.top,placement:B,triggerAnchorPoint:ge}}function g2(l){let{placement:n,targetNode:i,overlayNode:u,scrollNode:s,padding:f,shouldFlip:d,boundaryElement:h,offset:v,crossOffset:p,maxHeight:m,arrowSize:b=0,arrowBoundaryOffset:$=0}=l,E=u instanceof HTMLElement?v2(u):document.documentElement,O=E===document.documentElement;const R=window.getComputedStyle(E).position;let N=!!R&&R!=="static",P=O?ti(i,!1):Hv(i,E,!1);if(!O){let{marginTop:X,marginLeft:le}=window.getComputedStyle(i);P.top+=parseInt(X,10)||0,P.left+=parseInt(le,10)||0}let K=ti(u,!0),k=d2(u);var B,V;K.width+=((B=k.left)!==null&&B!==void 0?B:0)+((V=k.right)!==null&&V!==void 0?V:0);var ee,re;K.height+=((ee=k.top)!==null&&ee!==void 0?ee:0)+((re=k.bottom)!==null&&re!==void 0?re:0);let te=f2(s),ae=Lv(h),F=Lv(E),U=h.tagName==="BODY"?ti(E,!1):Hv(E,h,!1);return E.tagName==="HTML"&&h.tagName==="BODY"&&(F.scroll.top=0,F.scroll.left=0),p2(n,P,K,te,k,f,d,ae,F,U,v,p,N,m,b,$)}function rd(l,n){let{top:i,left:u,width:s,height:f}=l.getBoundingClientRect();return n&&l instanceof l.ownerDocument.defaultView.HTMLElement&&(s=l.offsetWidth,f=l.offsetHeight),{top:i,left:u,width:s,height:f}}function ti(l,n){let{top:i,left:u,width:s,height:f}=rd(l,n),{scrollTop:d,scrollLeft:h,clientTop:v,clientLeft:p}=document.documentElement;return{top:i+d-v,left:u+h-p,width:s,height:f}}function Hv(l,n,i){let u=window.getComputedStyle(l),s;if(u.position==="fixed")s=rd(l,i);else{s=ti(l,i);let f=ti(n,i),d=window.getComputedStyle(n);f.top+=(parseInt(d.borderTopWidth,10)||0)-n.scrollTop,f.left+=(parseInt(d.borderLeftWidth,10)||0)-n.scrollLeft,s.top-=f.top,s.left-=f.left}return s.top-=parseInt(u.marginTop,10)||0,s.left-=parseInt(u.marginLeft,10)||0,s}function v2(l){let n=l.offsetParent;if(n&&n===document.body&&window.getComputedStyle(n).position==="static"&&!jv(n)&&(n=document.documentElement),n==null)for(n=l.parentElement;n&&!jv(n);)n=n.parentElement;return n||document.documentElement}function jv(l){let n=window.getComputedStyle(l);return n.transform!=="none"||/transform|perspective/.test(n.willChange)||n.filter!=="none"||n.contain==="paint"||"backdropFilter"in n&&n.backdropFilter!=="none"||"WebkitBackdropFilter"in n&&n.WebkitBackdropFilter!=="none"}const $m=new WeakMap;function y2(l){let{triggerRef:n,isOpen:i,onClose:u}=l;M.useEffect(()=>{if(!i||u===null)return;let s=f=>{let d=f.target;if(!n.current||d instanceof Node&&!d.contains(n.current)||f.target instanceof HTMLInputElement||f.target instanceof HTMLTextAreaElement)return;let h=u||$m.get(n.current);h&&h()};return window.addEventListener("scroll",s,!0),()=>{window.removeEventListener("scroll",s,!0)}},[i,u,n])}const m2=new Set(["Arab","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),b2=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function S2(l){if(Intl.Locale){let i=new Intl.Locale(l).maximize(),u=typeof i.getTextInfo=="function"?i.getTextInfo():i.textInfo;if(u)return u.direction==="rtl";if(i.script)return m2.has(i.script)}let n=l.split("-")[0];return b2.has(n)}const E2=Symbol.for("react-aria.i18n.locale");function Tm(){let l=typeof window<"u"&&window[E2]||typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([l])}catch{l="en-US"}return{locale:l,direction:S2(l)?"rtl":"ltr"}}let Df=Tm(),Or=new Set;function Kv(){Df=Tm();for(let l of Or)l(Df)}function $2(){let l=Jf(),[n,i]=M.useState(Df);return M.useEffect(()=>(Or.size===0&&window.addEventListener("languagechange",Kv),Or.add(i),()=>{Or.delete(i),Or.size===0&&window.removeEventListener("languagechange",Kv)}),[]),l?{locale:"en-US",direction:"ltr"}:n}const T2=ce.createContext(null);function ud(){let l=$2();return M.useContext(T2)||l}const x2=Symbol.for("react-aria.i18n.locale"),O2=Symbol.for("react-aria.i18n.strings");let Fl;class Rs{getStringForLocale(n,i){let s=this.getStringsForLocale(i)[n];if(!s)throw new Error(`Could not find intl message ${n} in ${i} locale`);return s}getStringsForLocale(n){let i=this.strings[n];return i||(i=C2(n,this.strings,this.defaultLocale),this.strings[n]=i),i}static getGlobalDictionaryForPackage(n){if(typeof window>"u")return null;let i=window[x2];if(Fl===void 0){let s=window[O2];if(!s)return null;Fl={};for(let f in s)Fl[f]=new Rs({[i]:s[f]},i)}let u=Fl==null?void 0:Fl[n];if(!u)throw new Error(`Strings for package "${n}" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);return u}constructor(n,i="en-US"){this.strings=Object.fromEntries(Object.entries(n).filter(([,u])=>u)),this.defaultLocale=i}}function C2(l,n,i="en-US"){if(n[l])return n[l];let u=w2(l);if(n[u])return n[u];for(let s in n)if(s.startsWith(u+"-"))return n[s];return n[i]}function w2(l){return Intl.Locale?new Intl.Locale(l).language:l.split("-")[0]}const qv=new Map,Bv=new Map;class R2{format(n,i){let u=this.strings.getStringForLocale(n,this.locale);return typeof u=="function"?u(i,this):u}plural(n,i,u="cardinal"){let s=i["="+n];if(s)return typeof s=="function"?s():s;let f=this.locale+":"+u,d=qv.get(f);d||(d=new Intl.PluralRules(this.locale,{type:u}),qv.set(f,d));let h=d.select(n);return s=i[h]||i.other,typeof s=="function"?s():s}number(n){let i=Bv.get(this.locale);return i||(i=new Intl.NumberFormat(this.locale),Bv.set(this.locale,i)),i.format(n)}select(n,i){let u=n[i]||n.other;return typeof u=="function"?u():u}constructor(n,i){this.locale=n,this.strings=i}}const Qv=new WeakMap;function A2(l){let n=Qv.get(l);return n||(n=new Rs(l),Qv.set(l,n)),n}function M2(l,n){return n&&Rs.getGlobalDictionaryForPackage(n)||A2(l)}function N2(l,n){let{locale:i}=ud(),u=M2(l,n);return M.useMemo(()=>new R2(i,u),[i,u])}function D2(l,n){if(n.has(l))throw new TypeError("Cannot initialize the same private elements twice on an object")}function L2(l,n,i){D2(l,n),n.set(l,i)}let Ve=typeof document<"u"?window.visualViewport:null;function _2(l){let{direction:n}=ud(),{arrowSize:i,targetRef:u,overlayRef:s,arrowRef:f,scrollRef:d=s,placement:h="bottom",containerPadding:v=12,shouldFlip:p=!0,boundaryElement:m=typeof document<"u"?document.body:null,offset:b=0,crossOffset:$=0,shouldUpdatePosition:E=!0,isOpen:O=!0,onClose:R,maxHeight:N,arrowBoundaryOffset:P=0}=l,[K,k]=M.useState(null),B=[E,h,s.current,u.current,f==null?void 0:f.current,d.current,v,p,m,b,$,O,n,N,P,i],V=M.useRef(Ve==null?void 0:Ve.scale);M.useEffect(()=>{O&&(V.current=Ve==null?void 0:Ve.scale)},[O]);let ee=M.useCallback(()=>{if(E===!1||!O||!s.current||!u.current||!m||(Ve==null?void 0:Ve.scale)!==V.current)return;let X=null;if(d.current&&d.current.contains(document.activeElement)){var le;let x=(le=document.activeElement)===null||le===void 0?void 0:le.getBoundingClientRect(),H=d.current.getBoundingClientRect();var Y;if(X={type:"top",offset:((Y=x==null?void 0:x.top)!==null&&Y!==void 0?Y:0)-H.top},X.offset>H.height/2){X.type="bottom";var pe;X.offset=((pe=x==null?void 0:x.bottom)!==null&&pe!==void 0?pe:0)-H.bottom}}let C=s.current;if(!N&&s.current){var G;C.style.top="0px",C.style.bottom="";var W;C.style.maxHeight=((W=(G=window.visualViewport)===null||G===void 0?void 0:G.height)!==null&&W!==void 0?W:window.innerHeight)+"px"}let oe=g2({placement:U2(h,n),overlayNode:s.current,targetNode:u.current,scrollNode:d.current||s.current,padding:v,shouldFlip:p,boundaryElement:m,offset:b,crossOffset:$,maxHeight:N,arrowSize:i??(f!=null&&f.current?rd(f.current,!0).width:0),arrowBoundaryOffset:P});if(oe.position){if(C.style.top="",C.style.bottom="",C.style.left="",C.style.right="",Object.keys(oe.position).forEach(x=>C.style[x]=oe.position[x]+"px"),C.style.maxHeight=oe.maxHeight!=null?oe.maxHeight+"px":"",X&&document.activeElement&&d.current){let x=document.activeElement.getBoundingClientRect(),H=d.current.getBoundingClientRect(),J=x[X.type]-H[X.type];d.current.scrollTop+=J-X.offset}k(oe)}},B);Ke(ee,B),z2(ee),Rf({ref:s,onResize:ee}),Rf({ref:u,onResize:ee});let re=M.useRef(!1);Ke(()=>{let X,le=()=>{re.current=!0,clearTimeout(X),X=setTimeout(()=>{re.current=!1},500),ee()},Y=()=>{re.current&&le()};return Ve==null||Ve.addEventListener("resize",le),Ve==null||Ve.addEventListener("scroll",Y),()=>{Ve==null||Ve.removeEventListener("resize",le),Ve==null||Ve.removeEventListener("scroll",Y)}},[ee]);let te=M.useCallback(()=>{re.current||R==null||R()},[R,re]);y2({triggerRef:u,isOpen:O,onClose:R&&te});var ae,F,U;return{overlayProps:{style:{position:K?"absolute":"fixed",top:K?void 0:0,left:K?void 0:0,zIndex:1e5,...K==null?void 0:K.position,maxHeight:(ae=K==null?void 0:K.maxHeight)!==null&&ae!==void 0?ae:"100vh"}},placement:(F=K==null?void 0:K.placement)!==null&&F!==void 0?F:null,triggerAnchorPoint:(U=K==null?void 0:K.triggerAnchorPoint)!==null&&U!==void 0?U:null,arrowProps:{"aria-hidden":"true",role:"presentation",style:{left:K==null?void 0:K.arrowOffsetLeft,top:K==null?void 0:K.arrowOffsetTop}},updatePosition:ee}}function z2(l){Ke(()=>(window.addEventListener("resize",l,!1),()=>{window.removeEventListener("resize",l,!1)}),[l])}function U2(l,n){return n==="rtl"?l.replace("start","right").replace("end","left"):l.replace("start","left").replace("end","right")}function sd(l){let n=l;return n.nativeEvent=l,n.isDefaultPrevented=()=>n.defaultPrevented,n.isPropagationStopped=()=>n.cancelBubble,n.persist=()=>{},n}function xm(l,n){Object.defineProperty(l,"target",{value:n}),Object.defineProperty(l,"currentTarget",{value:n})}function Om(l){let n=M.useRef({isFocused:!1,observer:null});Ke(()=>{const u=n.current;return()=>{u.observer&&(u.observer.disconnect(),u.observer=null)}},[]);let i=Zt(u=>{l==null||l(u)});return M.useCallback(u=>{if(u.target instanceof HTMLButtonElement||u.target instanceof HTMLInputElement||u.target instanceof HTMLTextAreaElement||u.target instanceof HTMLSelectElement){n.current.isFocused=!0;let s=u.target,f=d=>{if(n.current.isFocused=!1,s.disabled){let h=sd(d);i(h)}n.current.observer&&(n.current.observer.disconnect(),n.current.observer=null)};s.addEventListener("focusout",f,{once:!0}),n.current.observer=new MutationObserver(()=>{if(n.current.isFocused&&s.disabled){var d;(d=n.current.observer)===null||d===void 0||d.disconnect();let h=s===document.activeElement?null:document.activeElement;s.dispatchEvent(new FocusEvent("blur",{relatedTarget:h})),s.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:h}))}}),n.current.observer.observe(s,{attributes:!0,attributeFilter:["disabled"]})}},[i])}let ys=!1;function H2(l){for(;l&&!ym(l);)l=l.parentElement;let n=Jt(l),i=n.document.activeElement;if(!i||i===l)return;ys=!0;let u=!1,s=m=>{(m.target===i||u)&&m.stopImmediatePropagation()},f=m=>{(m.target===i||u)&&(m.stopImmediatePropagation(),!l&&!u&&(u=!0,vi(i),v()))},d=m=>{(m.target===l||u)&&m.stopImmediatePropagation()},h=m=>{(m.target===l||u)&&(m.stopImmediatePropagation(),u||(u=!0,vi(i),v()))};n.addEventListener("blur",s,!0),n.addEventListener("focusout",f,!0),n.addEventListener("focusin",h,!0),n.addEventListener("focus",d,!0);let v=()=>{cancelAnimationFrame(p),n.removeEventListener("blur",s,!0),n.removeEventListener("focusout",f,!0),n.removeEventListener("focusin",h,!0),n.removeEventListener("focus",d,!0),ys=!1,u=!1},p=requestAnimationFrame(v);return v}let Il="default",Lf="",hs=new WeakMap;function j2(l){if(ws()){if(Il==="default"){const n=_e(l);Lf=n.documentElement.style.webkitUserSelect,n.documentElement.style.webkitUserSelect="none"}Il="disabled"}else if(l instanceof HTMLElement||l instanceof SVGElement){let n="userSelect"in l.style?"userSelect":"webkitUserSelect";hs.set(l,l.style[n]),l.style[n]="none"}}function Pv(l){if(ws()){if(Il!=="disabled")return;Il="restoring",setTimeout(()=>{fm(()=>{if(Il==="restoring"){const n=_e(l);n.documentElement.style.webkitUserSelect==="none"&&(n.documentElement.style.webkitUserSelect=Lf||""),Lf="",Il="default"}})},300)}else if((l instanceof HTMLElement||l instanceof SVGElement)&&l&&hs.has(l)){let n=hs.get(l),i="userSelect"in l.style?"userSelect":"webkitUserSelect";l.style[i]==="none"&&(l.style[i]=n),l.getAttribute("style")===""&&l.removeAttribute("style"),hs.delete(l)}}const Nr=ce.createContext({register:()=>{}});Nr.displayName="PressResponderContext";function K2(l,n){return n.get?n.get.call(l):n.value}function Cm(l,n,i){if(!n.has(l))throw new TypeError("attempted to "+i+" private field on non-instance");return n.get(l)}function q2(l,n){var i=Cm(l,n,"get");return K2(l,i)}function B2(l,n,i){if(n.set)n.set.call(l,i);else{if(!n.writable)throw new TypeError("attempted to set read only private field");n.value=i}}function kv(l,n,i){var u=Cm(l,n,"set");return B2(l,u,i),i}function Q2(l){let n=M.useContext(Nr);if(n){let{register:i,...u}=n;l=jt(u,l),i()}return td(n,l.ref),l}var us=new WeakMap;class ss{continuePropagation(){kv(this,us,!1)}get shouldStopPropagation(){return q2(this,us)}constructor(n,i,u,s){L2(this,us,{writable:!0,value:void 0}),kv(this,us,!0);var f;let d=(f=s==null?void 0:s.target)!==null&&f!==void 0?f:u.currentTarget;const h=d==null?void 0:d.getBoundingClientRect();let v,p=0,m,b=null;u.clientX!=null&&u.clientY!=null&&(m=u.clientX,b=u.clientY),h&&(m!=null&&b!=null?(v=m-h.left,p=b-h.top):(v=h.width/2,p=h.height/2)),this.type=n,this.pointerType=i,this.target=u.currentTarget,this.shiftKey=u.shiftKey,this.metaKey=u.metaKey,this.ctrlKey=u.ctrlKey,this.altKey=u.altKey,this.x=v,this.y=p}}const Gv=Symbol("linkClicked"),Yv="react-aria-pressable-style",Vv="data-react-aria-pressable";function P2(l){let{onPress:n,onPressChange:i,onPressStart:u,onPressEnd:s,onPressUp:f,onClick:d,isDisabled:h,isPressed:v,preventFocusOnPress:p,shouldCancelOnPointerExit:m,allowTextSelectionOnPress:b,ref:$,...E}=Q2(l),[O,R]=M.useState(!1),N=M.useRef({isPressed:!1,ignoreEmulatedMouseEvents:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null,disposables:[]}),{addGlobalListener:P,removeAllGlobalListeners:K}=ed(),k=Zt((U,X)=>{let le=N.current;if(h||le.didFirePressStart)return!1;let Y=!0;if(le.isTriggeringEvent=!0,u){let pe=new ss("pressstart",X,U);u(pe),Y=pe.shouldStopPropagation}return i&&i(!0),le.isTriggeringEvent=!1,le.didFirePressStart=!0,R(!0),Y}),B=Zt((U,X,le=!0)=>{let Y=N.current;if(!Y.didFirePressStart)return!1;Y.didFirePressStart=!1,Y.isTriggeringEvent=!0;let pe=!0;if(s){let C=new ss("pressend",X,U);s(C),pe=C.shouldStopPropagation}if(i&&i(!1),R(!1),n&&le&&!h){let C=new ss("press",X,U);n(C),pe&&(pe=C.shouldStopPropagation)}return Y.isTriggeringEvent=!1,pe}),V=Zt((U,X)=>{let le=N.current;if(h)return!1;if(f){le.isTriggeringEvent=!0;let Y=new ss("pressup",X,U);return f(Y),le.isTriggeringEvent=!1,Y.shouldStopPropagation}return!0}),ee=Zt(U=>{let X=N.current;if(X.isPressed&&X.target){X.didFirePressStart&&X.pointerType!=null&&B(Ga(X.target,U),X.pointerType,!1),X.isPressed=!1,X.isOverTarget=!1,X.activePointerId=null,X.pointerType=null,K(),b||Pv(X.target);for(let le of X.disposables)le();X.disposables=[]}}),re=Zt(U=>{m&&ee(U)}),te=Zt(U=>{d==null||d(U)}),ae=Zt((U,X)=>{if(d){let le=new MouseEvent("click",U);xm(le,X),d(sd(le))}}),F=M.useMemo(()=>{let U=N.current,X={onKeyDown(Y){if(lf(Y.nativeEvent,Y.currentTarget)&&wt(Y.currentTarget,Xe(Y.nativeEvent))){var pe;Xv(Xe(Y.nativeEvent),Y.key)&&Y.preventDefault();let C=!0;if(!U.isPressed&&!Y.repeat){U.target=Y.currentTarget,U.isPressed=!0,U.pointerType="keyboard",C=k(Y,"keyboard");let G=Y.currentTarget,W=oe=>{lf(oe,G)&&!oe.repeat&&wt(G,Xe(oe))&&U.target&&V(Ga(U.target,oe),"keyboard")};P(_e(Y.currentTarget),"keyup",Mr(W,le),!0)}C&&Y.stopPropagation(),Y.metaKey&&yi()&&((pe=U.metaKeyEvents)===null||pe===void 0||pe.set(Y.key,Y.nativeEvent))}else Y.key==="Meta"&&(U.metaKeyEvents=new Map)},onClick(Y){if(!(Y&&!wt(Y.currentTarget,Xe(Y.nativeEvent)))&&Y&&Y.button===0&&!U.isTriggeringEvent&&!mi.isOpening){let pe=!0;if(h&&Y.preventDefault(),!U.ignoreEmulatedMouseEvents&&!U.isPressed&&(U.pointerType==="virtual"||pm(Y.nativeEvent))){let C=k(Y,"virtual"),G=V(Y,"virtual"),W=B(Y,"virtual");te(Y),pe=C&&G&&W}else if(U.isPressed&&U.pointerType!=="keyboard"){let C=U.pointerType||Y.nativeEvent.pointerType||"virtual",G=V(Ga(Y.currentTarget,Y),C),W=B(Ga(Y.currentTarget,Y),C,!0);pe=G&&W,U.isOverTarget=!1,te(Y),ee(Y)}U.ignoreEmulatedMouseEvents=!1,pe&&Y.stopPropagation()}}},le=Y=>{var pe;if(U.isPressed&&U.target&&lf(Y,U.target)){var C;Xv(Xe(Y),Y.key)&&Y.preventDefault();let W=Xe(Y),oe=wt(U.target,Xe(Y));B(Ga(U.target,Y),"keyboard",oe),oe&&ae(Y,U.target),K(),Y.key!=="Enter"&&od(U.target)&&wt(U.target,W)&&!Y[Gv]&&(Y[Gv]=!0,mi(U.target,Y,!1)),U.isPressed=!1,(C=U.metaKeyEvents)===null||C===void 0||C.delete(Y.key)}else if(Y.key==="Meta"&&(!((pe=U.metaKeyEvents)===null||pe===void 0)&&pe.size)){var G;let W=U.metaKeyEvents;U.metaKeyEvents=void 0;for(let oe of W.values())(G=U.target)===null||G===void 0||G.dispatchEvent(new KeyboardEvent("keyup",oe))}};if(typeof PointerEvent<"u"){X.onPointerDown=C=>{if(C.button!==0||!wt(C.currentTarget,Xe(C.nativeEvent)))return;if(JE(C.nativeEvent)){U.pointerType="virtual";return}U.pointerType=C.pointerType;let G=!0;if(!U.isPressed){U.isPressed=!0,U.isOverTarget=!0,U.activePointerId=C.pointerId,U.target=C.currentTarget,b||j2(U.target),G=k(C,U.pointerType);let W=Xe(C.nativeEvent);"releasePointerCapture"in W&&W.releasePointerCapture(C.pointerId),P(_e(C.currentTarget),"pointerup",Y,!1),P(_e(C.currentTarget),"pointercancel",pe,!1)}G&&C.stopPropagation()},X.onMouseDown=C=>{if(wt(C.currentTarget,Xe(C.nativeEvent))&&C.button===0){if(p){let G=H2(C.target);G&&U.disposables.push(G)}C.stopPropagation()}},X.onPointerUp=C=>{!wt(C.currentTarget,Xe(C.nativeEvent))||U.pointerType==="virtual"||C.button===0&&!U.isPressed&&V(C,U.pointerType||C.pointerType)},X.onPointerEnter=C=>{C.pointerId===U.activePointerId&&U.target&&!U.isOverTarget&&U.pointerType!=null&&(U.isOverTarget=!0,k(Ga(U.target,C),U.pointerType))},X.onPointerLeave=C=>{C.pointerId===U.activePointerId&&U.target&&U.isOverTarget&&U.pointerType!=null&&(U.isOverTarget=!1,B(Ga(U.target,C),U.pointerType,!1),re(C))};let Y=C=>{if(C.pointerId===U.activePointerId&&U.isPressed&&C.button===0&&U.target){if(wt(U.target,Xe(C))&&U.pointerType!=null){let G=!1,W=setTimeout(()=>{U.isPressed&&U.target instanceof HTMLElement&&(G?ee(C):(vi(U.target),U.target.click()))},80);P(C.currentTarget,"click",()=>G=!0,!0),U.disposables.push(()=>clearTimeout(W))}else ee(C);U.isOverTarget=!1}},pe=C=>{ee(C)};X.onDragStart=C=>{wt(C.currentTarget,Xe(C.nativeEvent))&&ee(C)}}return X},[P,h,p,K,b,ee,re,B,k,V,te,ae]);return M.useEffect(()=>{if(!$)return;const U=_e($.current);if(!U||!U.head||U.getElementById(Yv))return;const X=U.createElement("style");X.id=Yv,X.textContent=`
@layer {
  [${Vv}] {
    touch-action: pan-x pan-y pinch-zoom;
  }
}
    `.trim(),U.head.prepend(X)},[$]),M.useEffect(()=>{let U=N.current;return()=>{var X;b||Pv((X=U.target)!==null&&X!==void 0?X:void 0);for(let le of U.disposables)le();U.disposables=[]}},[b]),{isPressed:v||O,pressProps:jt(E,F,{[Vv]:!0})}}function od(l){return l.tagName==="A"&&l.hasAttribute("href")}function lf(l,n){const{key:i,code:u}=l,s=n,f=s.getAttribute("role");return(i==="Enter"||i===" "||i==="Spacebar"||u==="Space")&&!(s instanceof Jt(s).HTMLInputElement&&!wm(s,i)||s instanceof Jt(s).HTMLTextAreaElement||s.isContentEditable)&&!((f==="link"||!f&&od(s))&&i!=="Enter")}function Ga(l,n){let i=n.clientX,u=n.clientY;return{currentTarget:l,shiftKey:n.shiftKey,ctrlKey:n.ctrlKey,metaKey:n.metaKey,altKey:n.altKey,clientX:i,clientY:u}}function k2(l){return l instanceof HTMLInputElement?!1:l instanceof HTMLButtonElement?l.type!=="submit"&&l.type!=="reset":!od(l)}function Xv(l,n){return l instanceof HTMLInputElement?!wm(l,n):k2(l)}const G2=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function wm(l,n){return l.type==="checkbox"||l.type==="radio"?n===" ":G2.has(l.type)}let ol=null,_f=new Set,wr=new Map,ul=!1,zf=!1;const Y2={Tab:!0,Escape:!0};function As(l,n){for(let i of _f)i(l,n)}function V2(l){return!(l.metaKey||!yi()&&l.altKey||l.ctrlKey||l.key==="Control"||l.key==="Shift"||l.key==="Meta")}function ms(l){ul=!0,V2(l)&&(ol="keyboard",As("keyboard",l))}function ni(l){ol="pointer",(l.type==="mousedown"||l.type==="pointerdown")&&(ul=!0,As("pointer",l))}function Rm(l){pm(l)&&(ul=!0,ol="virtual")}function Am(l){l.target===window||l.target===document||ys||!l.isTrusted||(!ul&&!zf&&(ol="virtual",As("virtual",l)),ul=!1,zf=!1)}function Mm(){ys||(ul=!1,zf=!0)}function Uf(l){if(typeof window>"u"||typeof document>"u"||wr.get(Jt(l)))return;const n=Jt(l),i=_e(l);let u=n.HTMLElement.prototype.focus;n.HTMLElement.prototype.focus=function(){ul=!0,u.apply(this,arguments)},i.addEventListener("keydown",ms,!0),i.addEventListener("keyup",ms,!0),i.addEventListener("click",Rm,!0),n.addEventListener("focus",Am,!0),n.addEventListener("blur",Mm,!1),typeof PointerEvent<"u"&&(i.addEventListener("pointerdown",ni,!0),i.addEventListener("pointermove",ni,!0),i.addEventListener("pointerup",ni,!0)),n.addEventListener("beforeunload",()=>{Nm(l)},{once:!0}),wr.set(n,{focus:u})}const Nm=(l,n)=>{const i=Jt(l),u=_e(l);n&&u.removeEventListener("DOMContentLoaded",n),wr.has(i)&&(i.HTMLElement.prototype.focus=wr.get(i).focus,u.removeEventListener("keydown",ms,!0),u.removeEventListener("keyup",ms,!0),u.removeEventListener("click",Rm,!0),i.removeEventListener("focus",Am,!0),i.removeEventListener("blur",Mm,!1),typeof PointerEvent<"u"&&(u.removeEventListener("pointerdown",ni,!0),u.removeEventListener("pointermove",ni,!0),u.removeEventListener("pointerup",ni,!0)),wr.delete(i))};function X2(l){const n=_e(l);let i;return n.readyState!=="loading"?Uf(l):(i=()=>{Uf(l)},n.addEventListener("DOMContentLoaded",i)),()=>Nm(l,i)}typeof document<"u"&&X2();function Dm(){return ol!=="pointer"}function Lm(){return ol}function i4(l){ol=l,As(l,null)}const F2=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Z2(l,n,i){let u=_e(i==null?void 0:i.target);const s=typeof window<"u"?Jt(i==null?void 0:i.target).HTMLInputElement:HTMLInputElement,f=typeof window<"u"?Jt(i==null?void 0:i.target).HTMLTextAreaElement:HTMLTextAreaElement,d=typeof window<"u"?Jt(i==null?void 0:i.target).HTMLElement:HTMLElement,h=typeof window<"u"?Jt(i==null?void 0:i.target).KeyboardEvent:KeyboardEvent;return l=l||u.activeElement instanceof s&&!F2.has(u.activeElement.type)||u.activeElement instanceof f||u.activeElement instanceof d&&u.activeElement.isContentEditable,!(l&&n==="keyboard"&&i instanceof h&&!Y2[i.key])}function J2(l,n,i){Uf(),M.useEffect(()=>{let u=(s,f)=>{Z2(!!(i!=null&&i.isTextInput),s,f)&&l(Dm())};return _f.add(u),()=>{_f.delete(u)}},n)}function Dr(l){const n=_e(l),i=Rt(n);if(Lm()==="virtual"){let u=i;fm(()=>{Rt(n)===u&&l.isConnected&&vi(l)})}else vi(l)}function _m(l){let{isDisabled:n,onFocus:i,onBlur:u,onFocusChange:s}=l;const f=M.useCallback(v=>{if(v.target===v.currentTarget)return u&&u(v),s&&s(!1),!0},[u,s]),d=Om(f),h=M.useCallback(v=>{const p=_e(v.target),m=p?Rt(p):Rt();v.target===v.currentTarget&&m===Xe(v.nativeEvent)&&(i&&i(v),s&&s(!0),d(v))},[s,i,d]);return{focusProps:{onFocus:!n&&(i||s||u)?h:void 0,onBlur:!n&&(u||s)?f:void 0}}}function Fv(l){if(!l)return;let n=!0;return i=>{let u={...i,preventDefault(){i.preventDefault()},isDefaultPrevented(){return i.isDefaultPrevented()},stopPropagation(){n=!0},continuePropagation(){n=!1},isPropagationStopped(){return n}};l(u),n&&i.stopPropagation()}}function W2(l){return{keyboardProps:l.isDisabled?{}:{onKeyDown:Fv(l.onKeyDown),onKeyUp:Fv(l.onKeyUp)}}}let I2=ce.createContext(null);function e3(l){let n=M.useContext(I2)||{};td(n,l);let{ref:i,...u}=n;return u}function t3(l,n){let{focusProps:i}=_m(l),{keyboardProps:u}=W2(l),s=jt(i,u),f=e3(n),d=l.isDisabled?{}:f,h=M.useRef(l.autoFocus);M.useEffect(()=>{h.current&&n.current&&Dr(n.current),h.current=!1},[n]);let v=l.excludeFromTabOrder?-1:0;return l.isDisabled&&(v=void 0),{focusableProps:jt({...s,tabIndex:v},d)}}const n3=ce.forwardRef(({children:l,...n},i)=>{let u=M.useRef(!1),s=M.useContext(Nr);i=dm(i||(s==null?void 0:s.ref));let f=jt(s||{},{...n,ref:i,register(){u.current=!0,s&&s.register()}});return td(s,i),M.useEffect(()=>{u.current||(u.current=!0)},[]),ce.createElement(Nr.Provider,{value:f},l)});function a3({children:l}){let n=M.useMemo(()=>({register:()=>{}}),[]);return ce.createElement(Nr.Provider,{value:n},l)}function cd(l){let{isDisabled:n,onBlurWithin:i,onFocusWithin:u,onFocusWithinChange:s}=l,f=M.useRef({isFocusWithin:!1}),{addGlobalListener:d,removeAllGlobalListeners:h}=ed(),v=M.useCallback(b=>{b.currentTarget.contains(b.target)&&f.current.isFocusWithin&&!b.currentTarget.contains(b.relatedTarget)&&(f.current.isFocusWithin=!1,h(),i&&i(b),s&&s(!1))},[i,s,f,h]),p=Om(v),m=M.useCallback(b=>{if(!b.currentTarget.contains(b.target))return;const $=_e(b.target),E=Rt($);if(!f.current.isFocusWithin&&E===Xe(b.nativeEvent)){u&&u(b),s&&s(!0),f.current.isFocusWithin=!0,p(b);let O=b.currentTarget;d($,"focus",R=>{if(f.current.isFocusWithin&&!wt(O,R.target)){let N=new $.defaultView.FocusEvent("blur",{relatedTarget:R.target});xm(N,O);let P=sd(N);v(P)}},{capture:!0})}},[u,s,p,d,v]);return n?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:m,onBlur:v}}}let Hf=!1,os=0;function l3(){Hf=!0,setTimeout(()=>{Hf=!1},50)}function Zv(l){l.pointerType==="touch"&&l3()}function i3(){if(!(typeof document>"u"))return os===0&&typeof PointerEvent<"u"&&document.addEventListener("pointerup",Zv),os++,()=>{os--,!(os>0)&&typeof PointerEvent<"u"&&document.removeEventListener("pointerup",Zv)}}function r3(l){let{onHoverStart:n,onHoverChange:i,onHoverEnd:u,isDisabled:s}=l,[f,d]=M.useState(!1),h=M.useRef({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;M.useEffect(i3,[]);let{addGlobalListener:v,removeAllGlobalListeners:p}=ed(),{hoverProps:m,triggerHoverEnd:b}=M.useMemo(()=>{let $=(R,N)=>{if(h.pointerType=N,s||N==="touch"||h.isHovered||!R.currentTarget.contains(R.target))return;h.isHovered=!0;let P=R.currentTarget;h.target=P,v(_e(R.target),"pointerover",K=>{h.isHovered&&h.target&&!wt(h.target,K.target)&&E(K,K.pointerType)},{capture:!0}),n&&n({type:"hoverstart",target:P,pointerType:N}),i&&i(!0),d(!0)},E=(R,N)=>{let P=h.target;h.pointerType="",h.target=null,!(N==="touch"||!h.isHovered||!P)&&(h.isHovered=!1,p(),u&&u({type:"hoverend",target:P,pointerType:N}),i&&i(!1),d(!1))},O={};return typeof PointerEvent<"u"&&(O.onPointerEnter=R=>{Hf&&R.pointerType==="mouse"||$(R,R.pointerType)},O.onPointerLeave=R=>{!s&&R.currentTarget.contains(R.target)&&E(R,R.pointerType)}),{hoverProps:O,triggerHoverEnd:E}},[n,i,u,s,h,v,p]);return M.useEffect(()=>{s&&b({currentTarget:h.target},h.pointerType)},[s]),{hoverProps:m,isHovered:f}}function u3(l){let{ref:n,onInteractOutside:i,isDisabled:u,onInteractOutsideStart:s}=l,f=M.useRef({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),d=Zt(v=>{i&&Jv(v,n)&&(s&&s(v),f.current.isPointerDown=!0)}),h=Zt(v=>{i&&i(v)});M.useEffect(()=>{let v=f.current;if(u)return;const p=n.current,m=_e(p);if(typeof PointerEvent<"u"){let b=$=>{v.isPointerDown&&Jv($,n)&&h($),v.isPointerDown=!1};return m.addEventListener("pointerdown",d,!0),m.addEventListener("click",b,!0),()=>{m.removeEventListener("pointerdown",d,!0),m.removeEventListener("click",b,!0)}}},[n,u,d,h])}function Jv(l,n){if(l.button>0)return!1;if(l.target){const i=l.target.ownerDocument;if(!i||!i.documentElement.contains(l.target)||l.target.closest("[data-react-aria-top-layer]"))return!1}return n.current?!l.composedPath().includes(n.current):!1}const Wv=ce.createContext(null),jf="react-aria-focus-scope-restore";let je=null;function s3(l){let{children:n,contain:i,restoreFocus:u,autoFocus:s}=l,f=M.useRef(null),d=M.useRef(null),h=M.useRef([]),{parentNode:v}=M.useContext(Wv)||{},p=M.useMemo(()=>new qf({scopeRef:h}),[h]);Ke(()=>{let $=v||Je.root;if(Je.getTreeNode($.scopeRef)&&je&&!bs(je,$.scopeRef)){let E=Je.getTreeNode(je);E&&($=E)}$.addChild(p),Je.addNode(p)},[p,v]),Ke(()=>{let $=Je.getTreeNode(h);$&&($.contain=!!i)},[i]),Ke(()=>{var $;let E=($=f.current)===null||$===void 0?void 0:$.nextSibling,O=[],R=N=>N.stopPropagation();for(;E&&E!==d.current;)O.push(E),E.addEventListener(jf,R),E=E.nextSibling;return h.current=O,()=>{for(let N of O)N.removeEventListener(jf,R)}},[n]),p3(h,u,i),f3(h,i),g3(h,u,i),h3(h,s),M.useEffect(()=>{const $=Rt(_e(h.current?h.current[0]:void 0));let E=null;if(Wt($,h.current)){for(let O of Je.traverse())O.scopeRef&&Wt($,O.scopeRef.current)&&(E=O);E===Je.getTreeNode(h)&&(je=E.scopeRef)}},[h]),Ke(()=>()=>{var $,E,O;let R=(O=(E=Je.getTreeNode(h))===null||E===void 0||($=E.parent)===null||$===void 0?void 0:$.scopeRef)!==null&&O!==void 0?O:null;(h===je||bs(h,je))&&(!R||Je.getTreeNode(R))&&(je=R),Je.removeTreeNode(h)},[h]);let m=M.useMemo(()=>o3(h),[]),b=M.useMemo(()=>({focusManager:m,parentNode:p}),[p,m]);return ce.createElement(Wv.Provider,{value:b},ce.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:f}),n,ce.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:d}))}function o3(l){return{focusNext(n={}){let i=l.current,{from:u,tabbable:s,wrap:f,accept:d}=n;var h;let v=u||Rt(_e((h=i[0])!==null&&h!==void 0?h:void 0)),p=i[0].previousElementSibling,m=Va(i),b=xa(m,{tabbable:s,accept:d},i);b.currentNode=Wt(v,i)?v:p;let $=b.nextNode();return!$&&f&&(b.currentNode=p,$=b.nextNode()),$&&Kn($,!0),$},focusPrevious(n={}){let i=l.current,{from:u,tabbable:s,wrap:f,accept:d}=n;var h;let v=u||Rt(_e((h=i[0])!==null&&h!==void 0?h:void 0)),p=i[i.length-1].nextElementSibling,m=Va(i),b=xa(m,{tabbable:s,accept:d},i);b.currentNode=Wt(v,i)?v:p;let $=b.previousNode();return!$&&f&&(b.currentNode=p,$=b.previousNode()),$&&Kn($,!0),$},focusFirst(n={}){let i=l.current,{tabbable:u,accept:s}=n,f=Va(i),d=xa(f,{tabbable:u,accept:s},i);d.currentNode=i[0].previousElementSibling;let h=d.nextNode();return h&&Kn(h,!0),h},focusLast(n={}){let i=l.current,{tabbable:u,accept:s}=n,f=Va(i),d=xa(f,{tabbable:u,accept:s},i);d.currentNode=i[i.length-1].nextElementSibling;let h=d.previousNode();return h&&Kn(h,!0),h}}}function Va(l){return l[0].parentElement}function Cr(l){let n=Je.getTreeNode(je);for(;n&&n.scopeRef!==l;){if(n.contain)return!1;n=n.parent}return!0}function c3(l){if(l.checked)return!0;let n=[];if(!l.form)n=[..._e(l).querySelectorAll(`input[type="radio"][name="${CSS.escape(l.name)}"]`)].filter(f=>!f.form);else{var i,u;let f=(u=l.form)===null||u===void 0||(i=u.elements)===null||i===void 0?void 0:i.namedItem(l.name);n=[...f??[]]}return n?!n.some(f=>f.checked):!1}function f3(l,n){let i=M.useRef(void 0),u=M.useRef(void 0);Ke(()=>{let s=l.current;if(!n){u.current&&(cancelAnimationFrame(u.current),u.current=void 0);return}const f=_e(s?s[0]:void 0);let d=p=>{if(p.key!=="Tab"||p.altKey||p.ctrlKey||p.metaKey||!Cr(l)||p.isComposing)return;let m=Rt(f),b=l.current;if(!b||!Wt(m,b))return;let $=Va(b),E=xa($,{tabbable:!0},b);if(!m)return;E.currentNode=m;let O=p.shiftKey?E.previousNode():E.nextNode();O||(E.currentNode=p.shiftKey?b[b.length-1].nextElementSibling:b[0].previousElementSibling,O=p.shiftKey?E.previousNode():E.nextNode()),p.preventDefault(),O&&Kn(O,!0)},h=p=>{(!je||bs(je,l))&&Wt(Xe(p),l.current)?(je=l,i.current=Xe(p)):Cr(l)&&!Ta(Xe(p),l)?i.current?i.current.focus():je&&je.current&&Kf(je.current):Cr(l)&&(i.current=Xe(p))},v=p=>{u.current&&cancelAnimationFrame(u.current),u.current=requestAnimationFrame(()=>{let m=Lm(),b=(m==="virtual"||m===null)&&If()&&cm(),$=Rt(f);if(!b&&$&&Cr(l)&&!Ta($,l)){je=l;let O=Xe(p);if(O&&O.isConnected){var E;i.current=O,(E=i.current)===null||E===void 0||E.focus()}else je.current&&Kf(je.current)}})};return f.addEventListener("keydown",d,!1),f.addEventListener("focusin",h,!1),s==null||s.forEach(p=>p.addEventListener("focusin",h,!1)),s==null||s.forEach(p=>p.addEventListener("focusout",v,!1)),()=>{f.removeEventListener("keydown",d,!1),f.removeEventListener("focusin",h,!1),s==null||s.forEach(p=>p.removeEventListener("focusin",h,!1)),s==null||s.forEach(p=>p.removeEventListener("focusout",v,!1))}},[l,n]),Ke(()=>()=>{u.current&&cancelAnimationFrame(u.current)},[u])}function zm(l){return Ta(l)}function Wt(l,n){return!l||!n?!1:n.some(i=>i.contains(l))}function Ta(l,n=null){if(l instanceof Element&&l.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:i}of Je.traverse(Je.getTreeNode(n)))if(i&&Wt(l,i.current))return!0;return!1}function d3(l){return Ta(l,je)}function bs(l,n){var i;let u=(i=Je.getTreeNode(n))===null||i===void 0?void 0:i.parent;for(;u;){if(u.scopeRef===l)return!0;u=u.parent}return!1}function Kn(l,n=!1){if(l!=null&&!n)try{Dr(l)}catch{}else if(l!=null)try{l.focus()}catch{}}function Um(l,n=!0){let i=l[0].previousElementSibling,u=Va(l),s=xa(u,{tabbable:n},l);s.currentNode=i;let f=s.nextNode();return n&&!f&&(u=Va(l),s=xa(u,{tabbable:!1},l),s.currentNode=i,f=s.nextNode()),f}function Kf(l,n=!0){Kn(Um(l,n))}function h3(l,n){const i=ce.useRef(n);M.useEffect(()=>{if(i.current){je=l;const u=_e(l.current?l.current[0]:void 0);!Wt(Rt(u),je.current)&&l.current&&Kf(l.current)}i.current=!1},[l])}function p3(l,n,i){Ke(()=>{if(n||i)return;let u=l.current;const s=_e(u?u[0]:void 0);let f=d=>{let h=Xe(d);Wt(h,l.current)?je=l:zm(h)||(je=null)};return s.addEventListener("focusin",f,!1),u==null||u.forEach(d=>d.addEventListener("focusin",f,!1)),()=>{s.removeEventListener("focusin",f,!1),u==null||u.forEach(d=>d.removeEventListener("focusin",f,!1))}},[l,n,i])}function Iv(l){let n=Je.getTreeNode(je);for(;n&&n.scopeRef!==l;){if(n.nodeToRestore)return!1;n=n.parent}return(n==null?void 0:n.scopeRef)===l}function g3(l,n,i){const u=M.useRef(typeof document<"u"?Rt(_e(l.current?l.current[0]:void 0)):null);Ke(()=>{let s=l.current;const f=_e(s?s[0]:void 0);if(!n||i)return;let d=()=>{(!je||bs(je,l))&&Wt(Rt(f),l.current)&&(je=l)};return f.addEventListener("focusin",d,!1),s==null||s.forEach(h=>h.addEventListener("focusin",d,!1)),()=>{f.removeEventListener("focusin",d,!1),s==null||s.forEach(h=>h.removeEventListener("focusin",d,!1))}},[l,i]),Ke(()=>{const s=_e(l.current?l.current[0]:void 0);if(!n)return;let f=d=>{if(d.key!=="Tab"||d.altKey||d.ctrlKey||d.metaKey||!Cr(l)||d.isComposing)return;let h=s.activeElement;if(!Ta(h,l)||!Iv(l))return;let v=Je.getTreeNode(l);if(!v)return;let p=v.nodeToRestore,m=xa(s.body,{tabbable:!0});m.currentNode=h;let b=d.shiftKey?m.previousNode():m.nextNode();if((!p||!p.isConnected||p===s.body)&&(p=void 0,v.nodeToRestore=void 0),(!b||!Ta(b,l))&&p){m.currentNode=p;do b=d.shiftKey?m.previousNode():m.nextNode();while(Ta(b,l));d.preventDefault(),d.stopPropagation(),b?Kn(b,!0):zm(p)?Kn(p,!0):h.blur()}};return i||s.addEventListener("keydown",f,!0),()=>{i||s.removeEventListener("keydown",f,!0)}},[l,n,i]),Ke(()=>{const s=_e(l.current?l.current[0]:void 0);if(!n)return;let f=Je.getTreeNode(l);if(f){var d;return f.nodeToRestore=(d=u.current)!==null&&d!==void 0?d:void 0,()=>{let h=Je.getTreeNode(l);if(!h)return;let v=h.nodeToRestore,p=Rt(s);if(n&&v&&(p&&Ta(p,l)||p===s.body&&Iv(l))){let m=Je.clone();requestAnimationFrame(()=>{if(s.activeElement===s.body){let b=m.getTreeNode(l);for(;b;){if(b.nodeToRestore&&b.nodeToRestore.isConnected){ey(b.nodeToRestore);return}b=b.parent}for(b=m.getTreeNode(l);b;){if(b.scopeRef&&b.scopeRef.current&&Je.getTreeNode(b.scopeRef)){let $=Um(b.scopeRef.current,!0);ey($);return}b=b.parent}}})}}}},[l,n])}function ey(l){l.dispatchEvent(new CustomEvent(jf,{bubbles:!0,cancelable:!0}))&&Kn(l)}function xa(l,n,i){let u=n!=null&&n.tabbable?r2:ym,s=(l==null?void 0:l.nodeType)===Node.ELEMENT_NODE?l:null,f=_e(s),d=ME(f,l||f,NodeFilter.SHOW_ELEMENT,{acceptNode(h){var v;return!(n==null||(v=n.from)===null||v===void 0)&&v.contains(h)||n!=null&&n.tabbable&&h.tagName==="INPUT"&&h.getAttribute("type")==="radio"&&(!c3(h)||d.currentNode.tagName==="INPUT"&&d.currentNode.type==="radio"&&d.currentNode.name===h.name)?NodeFilter.FILTER_REJECT:u(h)&&(!i||Wt(h,i))&&(!(n!=null&&n.accept)||n.accept(h))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return n!=null&&n.from&&(d.currentNode=n.from),d}class fd{get size(){return this.fastMap.size}getTreeNode(n){return this.fastMap.get(n)}addTreeNode(n,i,u){let s=this.fastMap.get(i??null);if(!s)return;let f=new qf({scopeRef:n});s.addChild(f),f.parent=s,this.fastMap.set(n,f),u&&(f.nodeToRestore=u)}addNode(n){this.fastMap.set(n.scopeRef,n)}removeTreeNode(n){if(n===null)return;let i=this.fastMap.get(n);if(!i)return;let u=i.parent;for(let f of this.traverse())f!==i&&i.nodeToRestore&&f.nodeToRestore&&i.scopeRef&&i.scopeRef.current&&Wt(f.nodeToRestore,i.scopeRef.current)&&(f.nodeToRestore=i.nodeToRestore);let s=i.children;u&&(u.removeChild(i),s.size>0&&s.forEach(f=>u&&u.addChild(f))),this.fastMap.delete(i.scopeRef)}*traverse(n=this.root){if(n.scopeRef!=null&&(yield n),n.children.size>0)for(let i of n.children)yield*this.traverse(i)}clone(){var n;let i=new fd;var u;for(let s of this.traverse())i.addTreeNode(s.scopeRef,(u=(n=s.parent)===null||n===void 0?void 0:n.scopeRef)!==null&&u!==void 0?u:null,s.nodeToRestore);return i}constructor(){this.fastMap=new Map,this.root=new qf({scopeRef:null}),this.fastMap.set(null,this.root)}}class qf{addChild(n){this.children.add(n),n.parent=this}removeChild(n){this.children.delete(n),n.parent=void 0}constructor(n){this.children=new Set,this.contain=!1,this.scopeRef=n.scopeRef}}let Je=new fd;function v3(l={}){let{autoFocus:n=!1,isTextInput:i,within:u}=l,s=M.useRef({isFocused:!1,isFocusVisible:n||Dm()}),[f,d]=M.useState(!1),[h,v]=M.useState(()=>s.current.isFocused&&s.current.isFocusVisible),p=M.useCallback(()=>v(s.current.isFocused&&s.current.isFocusVisible),[]),m=M.useCallback(E=>{s.current.isFocused=E,d(E),p()},[p]);J2(E=>{s.current.isFocusVisible=E,p()},[],{isTextInput:i});let{focusProps:b}=_m({isDisabled:u,onFocusChange:m}),{focusWithinProps:$}=cd({isDisabled:!u,onFocusWithinChange:m});return{isFocused:f,isFocusVisible:h,focusProps:u?$:b}}const dn=[];function y3(l,n){let{onClose:i,shouldCloseOnBlur:u,isOpen:s,isDismissable:f=!1,isKeyboardDismissDisabled:d=!1,shouldCloseOnInteractOutside:h}=l;M.useEffect(()=>{if(s&&!dn.includes(n))return dn.push(n),()=>{let O=dn.indexOf(n);O>=0&&dn.splice(O,1)}},[s,n]);let v=()=>{dn[dn.length-1]===n&&i&&i()},p=O=>{(!h||h(O.target))&&dn[dn.length-1]===n&&(O.stopPropagation(),O.preventDefault())},m=O=>{(!h||h(O.target))&&(dn[dn.length-1]===n&&(O.stopPropagation(),O.preventDefault()),v())},b=O=>{O.key==="Escape"&&!d&&!O.nativeEvent.isComposing&&(O.stopPropagation(),O.preventDefault(),v())};u3({ref:n,onInteractOutside:f&&s?m:void 0,onInteractOutsideStart:p});let{focusWithinProps:$}=cd({isDisabled:!u,onBlurWithin:O=>{!O.relatedTarget||d3(O.relatedTarget)||(!h||h(O.relatedTarget))&&(i==null||i())}}),E=O=>{O.target===O.currentTarget&&O.preventDefault()};return{overlayProps:{onKeyDown:b,...$},underlayProps:{onPointerDown:E}}}function m3(l,n,i){let{type:u}=l,{isOpen:s}=n;M.useEffect(()=>{i&&i.current&&$m.set(i.current,n.close)});let f;u==="menu"?f=!0:u==="listbox"&&(f="listbox");let d=gi();return{triggerProps:{"aria-haspopup":f,"aria-expanded":s,"aria-controls":s?d:void 0,onPress:n.toggle},overlayProps:{id:d}}}const rf=typeof document<"u"&&window.visualViewport,b3=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);let cs=0,uf;function S3(l={}){let{isDisabled:n}=l;Ke(()=>{if(!n)return cs++,cs===1&&(ws()?uf=$3():uf=E3()),()=>{cs--,cs===0&&uf()}},[n])}function E3(){let l=window.innerWidth-document.documentElement.clientWidth;return Mr(l>0&&("scrollbarGutter"in document.documentElement.style?Xa(document.documentElement,"scrollbarGutter","stable"):Xa(document.documentElement,"paddingRight",`${l}px`)),Xa(document.documentElement,"overflow","hidden"))}function $3(){let l,n,i=p=>{l=hm(p.target,!0),!(l===document.documentElement&&l===document.body)&&l instanceof HTMLElement&&window.getComputedStyle(l).overscrollBehavior==="auto"&&(n=Xa(l,"overscrollBehavior","contain"))},u=p=>{if(!l||l===document.documentElement||l===document.body){p.preventDefault();return}l.scrollHeight===l.clientHeight&&l.scrollWidth===l.clientWidth&&p.preventDefault()},s=()=>{n&&n()},f=p=>{let m=p.target;T3(m)&&(h(),m.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{m.style.transform="",rf&&(rf.height<window.innerHeight?requestAnimationFrame(()=>{ty(m)}):rf.addEventListener("resize",()=>ty(m),{once:!0}))}))},d=null,h=()=>{if(d)return;let p=()=>{window.scrollTo(0,0)},m=window.pageXOffset,b=window.pageYOffset;d=Mr(Sr(window,"scroll",p),Xa(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),Xa(document.documentElement,"overflow","hidden"),Xa(document.body,"marginTop",`-${b}px`),()=>{window.scrollTo(m,b)}),window.scrollTo(0,0)},v=Mr(Sr(document,"touchstart",i,{passive:!1,capture:!0}),Sr(document,"touchmove",u,{passive:!1,capture:!0}),Sr(document,"touchend",s,{passive:!1,capture:!0}),Sr(document,"focus",f,!0));return()=>{n==null||n(),d==null||d(),v()}}function Xa(l,n,i){let u=l.style[n];return l.style[n]=i,()=>{l.style[n]=u}}function Sr(l,n,i,u){return l.addEventListener(n,i,u),()=>{l.removeEventListener(n,i,u)}}function ty(l){let n=document.scrollingElement||document.documentElement,i=l;for(;i&&i!==n;){let u=hm(i);if(u!==document.documentElement&&u!==document.body&&u!==i){let s=u.getBoundingClientRect().top,f=i.getBoundingClientRect().top;f>s+i.clientHeight&&(u.scrollTop+=f-s)}i=u.parentElement}}function T3(l){return l instanceof HTMLInputElement&&!b3.has(l.type)||l instanceof HTMLTextAreaElement||l instanceof HTMLElement&&l.isContentEditable}const Hm=M.createContext({});function r4(l){let{getContainer:n}=l,{getContainer:i}=jm();return ce.createElement(Hm.Provider,{value:{getContainer:n===null?void 0:n??i}},l.children)}function jm(){var l;return(l=M.useContext(Hm))!==null&&l!==void 0?l:{}}var Km={};Km={dismiss:"تجاهل"};var qm={};qm={dismiss:"Отхвърляне"};var Bm={};Bm={dismiss:"Odstranit"};var Qm={};Qm={dismiss:"Luk"};var Pm={};Pm={dismiss:"Schließen"};var km={};km={dismiss:"Απόρριψη"};var Gm={};Gm={dismiss:"Dismiss"};var Ym={};Ym={dismiss:"Descartar"};var Vm={};Vm={dismiss:"Lõpeta"};var Xm={};Xm={dismiss:"Hylkää"};var Fm={};Fm={dismiss:"Rejeter"};var Zm={};Zm={dismiss:"התעלם"};var Jm={};Jm={dismiss:"Odbaci"};var Wm={};Wm={dismiss:"Elutasítás"};var Im={};Im={dismiss:"Ignora"};var e0={};e0={dismiss:"閉じる"};var t0={};t0={dismiss:"무시"};var n0={};n0={dismiss:"Atmesti"};var a0={};a0={dismiss:"Nerādīt"};var l0={};l0={dismiss:"Lukk"};var i0={};i0={dismiss:"Negeren"};var r0={};r0={dismiss:"Zignoruj"};var u0={};u0={dismiss:"Descartar"};var s0={};s0={dismiss:"Dispensar"};var o0={};o0={dismiss:"Revocare"};var c0={};c0={dismiss:"Пропустить"};var f0={};f0={dismiss:"Zrušiť"};var d0={};d0={dismiss:"Opusti"};var h0={};h0={dismiss:"Odbaci"};var p0={};p0={dismiss:"Avvisa"};var g0={};g0={dismiss:"Kapat"};var v0={};v0={dismiss:"Скасувати"};var y0={};y0={dismiss:"取消"};var m0={};m0={dismiss:"關閉"};var b0={};b0={"ar-AE":Km,"bg-BG":qm,"cs-CZ":Bm,"da-DK":Qm,"de-DE":Pm,"el-GR":km,"en-US":Gm,"es-ES":Ym,"et-EE":Vm,"fi-FI":Xm,"fr-FR":Fm,"he-IL":Zm,"hr-HR":Jm,"hu-HU":Wm,"it-IT":Im,"ja-JP":e0,"ko-KR":t0,"lt-LT":n0,"lv-LV":a0,"nb-NO":l0,"nl-NL":i0,"pl-PL":r0,"pt-BR":u0,"pt-PT":s0,"ro-RO":o0,"ru-RU":c0,"sk-SK":f0,"sl-SI":d0,"sr-SP":h0,"sv-SE":p0,"tr-TR":g0,"uk-UA":v0,"zh-CN":y0,"zh-TW":m0};const ny={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function x3(l={}){let{style:n,isFocusable:i}=l,[u,s]=M.useState(!1),{focusWithinProps:f}=cd({isDisabled:!i,onFocusWithinChange:h=>s(h)}),d=M.useMemo(()=>u?n:n?{...ny,...n}:ny,[u]);return{visuallyHiddenProps:{...f,style:d}}}function O3(l){let{children:n,elementType:i="div",isFocusable:u,style:s,...f}=l,{visuallyHiddenProps:d}=x3(l);return ce.createElement(i,jt(f,d),n)}function C3(l){return l&&l.__esModule?l.default:l}function ay(l){let{onDismiss:n,...i}=l,u=N2(C3(b0),"@react-aria/overlays"),s=FE(i,u.format("dismiss")),f=()=>{n&&n()};return ce.createElement(O3,null,ce.createElement("button",{...s,tabIndex:-1,onClick:f,style:{width:1,height:1}}))}const w3=typeof HTMLElement<"u"&&"inert"in HTMLElement.prototype;let Er=new WeakMap,xt=[];function R3(l,n){let i=Jt(l==null?void 0:l[0]),u=n instanceof i.Element?{root:n}:n;var s;let f=(s=u==null?void 0:u.root)!==null&&s!==void 0?s:document.body,d=(u==null?void 0:u.shouldUseInert)&&w3,h=new Set(l),v=new Set,p=R=>d&&R instanceof i.HTMLElement?R.inert:R.getAttribute("aria-hidden")==="true",m=(R,N)=>{d&&R instanceof i.HTMLElement?R.inert=N:N?R.setAttribute("aria-hidden","true"):(R.removeAttribute("aria-hidden"),R instanceof i.HTMLElement&&(R.inert=!1))},b=R=>{for(let k of R.querySelectorAll("[data-live-announcer], [data-react-aria-top-layer]"))h.add(k);let N=k=>{if(v.has(k)||h.has(k)||k.parentElement&&v.has(k.parentElement)&&k.parentElement.getAttribute("role")!=="row")return NodeFilter.FILTER_REJECT;for(let B of h)if(k.contains(B))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},P=document.createTreeWalker(R,NodeFilter.SHOW_ELEMENT,{acceptNode:N}),K=N(R);if(K===NodeFilter.FILTER_ACCEPT&&$(R),K!==NodeFilter.FILTER_REJECT){let k=P.nextNode();for(;k!=null;)$(k),k=P.nextNode()}},$=R=>{var N;let P=(N=Er.get(R))!==null&&N!==void 0?N:0;p(R)&&P===0||(P===0&&m(R,!0),v.add(R),Er.set(R,P+1))};xt.length&&xt[xt.length-1].disconnect(),b(f);let E=new MutationObserver(R=>{for(let N of R)if(N.type==="childList"&&![...h,...v].some(P=>P.contains(N.target)))for(let P of N.addedNodes)(P instanceof HTMLElement||P instanceof SVGElement)&&(P.dataset.liveAnnouncer==="true"||P.dataset.reactAriaTopLayer==="true")?h.add(P):P instanceof Element&&b(P)});E.observe(f,{childList:!0,subtree:!0});let O={visibleNodes:h,hiddenNodes:v,observe(){E.observe(f,{childList:!0,subtree:!0})},disconnect(){E.disconnect()}};return xt.push(O),()=>{E.disconnect();for(let R of v){let N=Er.get(R);N!=null&&(N===1?(m(R,!1),Er.delete(R)):Er.set(R,N-1))}O===xt[xt.length-1]?(xt.pop(),xt.length&&xt[xt.length-1].observe()):xt.splice(xt.indexOf(O),1)}}function A3(l){let n=xt[xt.length-1];if(n&&!n.visibleNodes.has(l))return n.visibleNodes.add(l),()=>{n.visibleNodes.delete(l)}}function M3(l,n){let{triggerRef:i,popoverRef:u,groupRef:s,isNonModal:f,isKeyboardDismissDisabled:d,shouldCloseOnInteractOutside:h,...v}=l,p=v.trigger==="SubmenuTrigger",{overlayProps:m,underlayProps:b}=y3({isOpen:n.isOpen,onClose:n.close,shouldCloseOnBlur:!0,isDismissable:!f||p,isKeyboardDismissDisabled:d,shouldCloseOnInteractOutside:h},s??u),{overlayProps:$,arrowProps:E,placement:O,triggerAnchorPoint:R}=_2({...v,targetRef:i,overlayRef:u,isOpen:n.isOpen,onClose:f&&!p?n.close:null});return S3({isDisabled:f||!n.isOpen}),M.useEffect(()=>{if(n.isOpen&&u.current){var N,P;return f?A3((N=s==null?void 0:s.current)!==null&&N!==void 0?N:u.current):R3([(P=s==null?void 0:s.current)!==null&&P!==void 0?P:u.current],{shouldUseInert:!0})}},[f,n.isOpen,u,s]),{popoverProps:jt(m,$),arrowProps:E,underlayProps:b,placement:O,triggerAnchorPoint:R}}const S0=ce.createContext(null);function ly(l){let n=Jf(),{portalContainer:i=n?null:document.body,isExiting:u}=l,[s,f]=M.useState(!1),d=M.useMemo(()=>({contain:s,setContain:f}),[s,f]),{getContainer:h}=jm();if(!l.portalContainer&&h&&(i=h()),!i)return null;let v=l.children;return l.disableFocusManagement||(v=ce.createElement(s3,{restoreFocus:!0,contain:(l.shouldContainFocus||s)&&!u},v)),v=ce.createElement(S0.Provider,{value:d},ce.createElement(a3,null,v)),WE.createPortal(v,i)}function N3(){let l=M.useContext(S0),n=l==null?void 0:l.setContain;Ke(()=>{n==null||n(!0)},[n])}class Ms{get childNodes(){throw new Error("childNodes is not supported")}clone(){let n=new this.constructor(this.key);return n.value=this.value,n.level=this.level,n.hasChildNodes=this.hasChildNodes,n.rendered=this.rendered,n.textValue=this.textValue,n["aria-label"]=this["aria-label"],n.index=this.index,n.parentKey=this.parentKey,n.prevKey=this.prevKey,n.nextKey=this.nextKey,n.firstChildKey=this.firstChildKey,n.lastChildKey=this.lastChildKey,n.props=this.props,n.render=this.render,n.colSpan=this.colSpan,n.colIndex=this.colIndex,n}filter(n,i,u){let s=this.clone();return i.addDescendants(s,n),s}constructor(n){this.value=null,this.level=0,this.hasChildNodes=!1,this.rendered=null,this.textValue="",this["aria-label"]=void 0,this.index=0,this.parentKey=null,this.prevKey=null,this.nextKey=null,this.firstChildKey=null,this.lastChildKey=null,this.props={},this.colSpan=null,this.colIndex=null,this.type=this.constructor.type,this.key=n}}class E0 extends Ms{filter(n,i,u){let[s,f]=$0(n,i,this.firstChildKey,u),d=this.clone();return d.firstChildKey=s,d.lastChildKey=f,d}}class D3 extends Ms{}D3.type="header";class L3 extends Ms{}L3.type="loader";class _3 extends E0{filter(n,i,u){if(u(this.textValue,this)){let s=this.clone();return i.addDescendants(s,n),s}return null}}_3.type="item";class z3 extends E0{filter(n,i,u){let s=super.filter(n,i,u);if(s&&s.lastChildKey!==null){let f=n.getItem(s.lastChildKey);if(f&&f.type!=="header")return s}return null}}z3.type="section";class u4{get size(){return this.itemCount}getKeys(){return this.keyMap.keys()}*[Symbol.iterator](){let n=this.firstKey!=null?this.keyMap.get(this.firstKey):void 0;for(;n;)yield n,n=n.nextKey!=null?this.keyMap.get(n.nextKey):void 0}getChildren(n){let i=this.keyMap;return{*[Symbol.iterator](){let u=i.get(n),s=(u==null?void 0:u.firstChildKey)!=null?i.get(u.firstChildKey):null;for(;s;)yield s,s=s.nextKey!=null?i.get(s.nextKey):void 0}}}getKeyBefore(n){let i=this.keyMap.get(n);if(!i)return null;if(i.prevKey!=null){for(i=this.keyMap.get(i.prevKey);i&&i.type!=="item"&&i.lastChildKey!=null;)i=this.keyMap.get(i.lastChildKey);var u;return(u=i==null?void 0:i.key)!==null&&u!==void 0?u:null}return i.parentKey}getKeyAfter(n){let i=this.keyMap.get(n);if(!i)return null;if(i.type!=="item"&&i.firstChildKey!=null)return i.firstChildKey;for(;i;){if(i.nextKey!=null)return i.nextKey;if(i.parentKey!=null)i=this.keyMap.get(i.parentKey);else return null}return null}getFirstKey(){return this.firstKey}getLastKey(){let n=this.lastKey!=null?this.keyMap.get(this.lastKey):null;for(;(n==null?void 0:n.lastChildKey)!=null;)n=this.keyMap.get(n.lastChildKey);var i;return(i=n==null?void 0:n.key)!==null&&i!==void 0?i:null}getItem(n){var i;return(i=this.keyMap.get(n))!==null&&i!==void 0?i:null}at(){throw new Error("Not implemented")}clone(){let n=this.constructor,i=new n;return i.keyMap=new Map(this.keyMap),i.firstKey=this.firstKey,i.lastKey=this.lastKey,i.itemCount=this.itemCount,i}addNode(n){if(this.frozen)throw new Error("Cannot add a node to a frozen collection");n.type==="item"&&this.keyMap.get(n.key)==null&&this.itemCount++,this.keyMap.set(n.key,n)}addDescendants(n,i){this.addNode(n);let u=i.getChildren(n.key);for(let s of u)this.addDescendants(s,i)}removeNode(n){if(this.frozen)throw new Error("Cannot remove a node to a frozen collection");let i=this.keyMap.get(n);i!=null&&i.type==="item"&&this.itemCount--,this.keyMap.delete(n)}commit(n,i,u=!1){if(this.frozen)throw new Error("Cannot commit a frozen collection");this.firstKey=n,this.lastKey=i,this.frozen=!u}filter(n){let i=new this.constructor,[u,s]=$0(this,i,this.firstKey,n);return i==null||i.commit(u,s),i}constructor(){this.keyMap=new Map,this.firstKey=null,this.lastKey=null,this.frozen=!1,this.itemCount=0}}function $0(l,n,i,u){if(i==null)return[null,null];let s=null,f=null,d=l.getItem(i);for(;d!=null;){let p=d.filter(l,n,u);p!=null&&(p.nextKey=null,f&&(p.prevKey=f.key,f.nextKey=p.key),s==null&&(s=p),n.addNode(p),f=p),d=d.nextKey?l.getItem(d.nextKey):null}if(f&&f.type==="separator"){let p=f.prevKey;n.removeNode(f.key),p?(f=n.getItem(p),f.nextKey=null):f=null}var h,v;return[(h=s==null?void 0:s.key)!==null&&h!==void 0?h:null,(v=f==null?void 0:f.key)!==null&&v!==void 0?v:null]}if(typeof HTMLTemplateElement<"u"){const l=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild").get;Object.defineProperty(HTMLTemplateElement.prototype,"firstChild",{configurable:!0,enumerable:!0,get:function(){return this.dataset.reactAriaHidden?this.content.firstChild:l.call(this)}})}const Ss=M.createContext(!1);function s4(l){if(M.useContext(Ss))return ce.createElement(ce.Fragment,null,l.children);let i=ce.createElement(Ss.Provider,{value:!0},l.children);return ce.createElement("template",{"data-react-aria-hidden":!0},i)}function U3(l){let n=(i,u)=>M.useContext(Ss)?null:l(i,u);return n.displayName=l.displayName||l.name,M.forwardRef(n)}function H3(){return M.useContext(Ss)}function j3(l,n){let{elementType:i="button",isDisabled:u,onPress:s,onPressStart:f,onPressEnd:d,onPressUp:h,onPressChange:v,preventFocusOnPress:p,allowFocusWhenDisabled:m,onClick:b,href:$,target:E,rel:O,type:R="button"}=l,N;i==="button"?N={type:R,disabled:u,form:l.form,formAction:l.formAction,formEncType:l.formEncType,formMethod:l.formMethod,formNoValidate:l.formNoValidate,formTarget:l.formTarget,name:l.name,value:l.value}:N={role:"button",href:i==="a"&&!u?$:void 0,target:i==="a"?E:void 0,type:i==="input"?R:void 0,disabled:i==="input"?u:void 0,"aria-disabled":!u||i==="input"?void 0:u,rel:i==="a"?O:void 0};let{pressProps:P,isPressed:K}=P2({onPressStart:f,onPressEnd:d,onPressChange:v,onPress:s,onPressUp:h,onClick:b,isDisabled:u,preventFocusOnPress:p,ref:n}),{focusableProps:k}=t3(l,n);m&&(k.tabIndex=u?-1:k.tabIndex);let B=jt(k,P,Br(l,{labelable:!0}));return{isPressed:K,buttonProps:jt(N,B,{"aria-haspopup":l["aria-haspopup"],"aria-expanded":l["aria-expanded"],"aria-controls":l["aria-controls"],"aria-pressed":l["aria-pressed"],"aria-current":l["aria-current"],"aria-disabled":l["aria-disabled"]})}}function K3(l,n){let{role:i="dialog"}=l,u=OE();u=l["aria-label"]?void 0:u;let s=M.useRef(!1);return M.useEffect(()=>{if(n.current&&!n.current.contains(document.activeElement)){Dr(n.current);let f=setTimeout(()=>{(document.activeElement===n.current||document.activeElement===document.body)&&(s.current=!0,n.current&&(n.current.blur(),Dr(n.current)),s.current=!1)},500);return()=>{clearTimeout(f)}}},[n]),N3(),{dialogProps:{...Br(l,{labelable:!0}),role:i,tabIndex:-1,"aria-labelledby":l["aria-labelledby"]||u,onBlur:f=>{s.current&&f.stopPropagation()}},titleProps:{id:u}}}const q3=M.createContext(null),T0=M.createContext({}),o4=U3(function(n,i){[n,i]=id(n,i,T0),n=B3(n);let u=n,{isPending:s}=u,{buttonProps:f,isPressed:d}=j3(n,i),{focusProps:h,isFocused:v,isFocusVisible:p}=v3(n),{hoverProps:m,isHovered:b}=r3({...n,isDisabled:n.isDisabled||s}),$={isHovered:b,isPressed:(u.isPressed||d)&&!s,isFocused:v,isFocusVisible:p,isDisabled:n.isDisabled||!1,isPending:s??!1},E=ld({...n,values:$,defaultClassName:"react-aria-Button"}),O=gi(f.id),R=gi(),N=f["aria-labelledby"];s&&(N?N=`${N} ${R}`:f["aria-label"]&&(N=`${O} ${R}`));let P=M.useRef(s);M.useEffect(()=>{let k={"aria-labelledby":N||O};(!P.current&&v&&s||P.current&&v&&!s)&&Dv(k,"assertive"),P.current=s},[s,v,N,O]);let K=Br(n,{global:!0});return delete K.onClick,ce.createElement("button",{...jt(K,E,f,h,m),type:f.type==="submit"&&s?"button":f.type,id:O,ref:i,"aria-labelledby":N,slot:n.slot||void 0,"aria-disabled":s?"true":f["aria-disabled"],"data-disabled":n.isDisabled||void 0,"data-pressed":$.isPressed||void 0,"data-hovered":b||void 0,"data-focused":v||void 0,"data-pending":s||void 0,"data-focus-visible":p||void 0},ce.createElement(q3.Provider,{value:{id:R}},E.children))});function B3(l){return l.isPending&&(l.onPress=void 0,l.onPressStart=void 0,l.onPressEnd=void 0,l.onPressChange=void 0,l.onPressUp=void 0,l.onKeyDown=void 0,l.onKeyUp=void 0,l.onClick=void 0,l.href=void 0),l}function x0(l){let[n,i]=u2(l.isOpen,l.defaultOpen||!1,l.onOpenChange);const u=M.useCallback(()=>{i(!0)},[i]),s=M.useCallback(()=>{i(!1)},[i]),f=M.useCallback(()=>{i(!n)},[i,n]);return{isOpen:n,setOpen:i,open:u,close:s,toggle:f}}function Q3(l){let n=x0(l),[i,u]=M.useState(null),[s,f]=M.useState([]),d=()=>{f([]),n.close()};return{focusStrategy:i,...n,open(p=null){u(p),n.open()},toggle(p=null){u(p),n.toggle()},close(){d()},expandedKeysStack:s,openSubmenu:(p,m)=>{f(b=>m>b.length?b:[...b.slice(0,m),p])},closeSubmenu:(p,m)=>{f(b=>b[m]===p?b.slice(0,m):b)}}}const P3=M.createContext({placement:"bottom"}),O0=M.createContext(null),iy=M.createContext(null),c4=M.forwardRef(function(n,i){[n,i]=id(n,i,O0);let u=M.useContext(dd),s=x0(n),f=n.isOpen!=null||n.defaultOpen!=null||!u?s:u,d=e2(i,f.isOpen)||n.isExiting||!1,h=H3(),{direction:v}=ud();if(h){let p=n.children;return typeof p=="function"&&(p=p({trigger:n.trigger||null,placement:"bottom",isEntering:!1,isExiting:!1,defaultChildren:null})),ce.createElement(ce.Fragment,null,p)}return f&&!f.isOpen&&!d?null:ce.createElement(k3,{...n,triggerRef:n.triggerRef,state:f,popoverRef:i,isExiting:d,dir:v})});function k3({state:l,isExiting:n,UNSTABLE_portalContainer:i,clearContexts:u,...s}){let f=M.useRef(null),d=M.useRef(null),h=M.useContext(iy),v=h&&s.trigger==="SubmenuTrigger";var p;let{popoverProps:m,underlayProps:b,arrowProps:$,placement:E,triggerAnchorPoint:O}=M3({...s,offset:(p=s.offset)!==null&&p!==void 0?p:8,arrowRef:f,groupRef:v?h:d},l),R=s.popoverRef,N=IE(R,!!E)||s.isEntering||!1,P=ld({...s,defaultClassName:"react-aria-Popover",values:{trigger:s.trigger||null,placement:E,isEntering:N,isExiting:n}}),K=!s.isNonModal||s.trigger==="SubmenuTrigger",[k,B]=M.useState(!1);Ke(()=>{R.current&&B(K&&!R.current.querySelector("[role=dialog]"))},[R,K]),M.useEffect(()=>{k&&s.trigger!=="SubmenuTrigger"&&R.current&&!R.current.contains(document.activeElement)&&Dr(R.current)},[k,R,s.trigger]);let V=M.useMemo(()=>{let ae=P.children;if(u)for(let F of u)ae=ce.createElement(F.Provider,{value:null},ae);return ae},[P.children,u]),ee={...m.style,"--trigger-anchor-point":O?`${O.x}px ${O.y}px`:void 0,...P.style},re=ce.createElement("div",{...jt(Br(s,{global:!0}),m),...P,role:k?"dialog":void 0,tabIndex:k?-1:void 0,"aria-label":s["aria-label"],"aria-labelledby":s["aria-labelledby"],ref:R,slot:s.slot||void 0,style:ee,dir:s.dir,"data-trigger":s.trigger,"data-placement":E,"data-entering":N||void 0,"data-exiting":n||void 0},!s.isNonModal&&ce.createElement(ay,{onDismiss:l.close}),ce.createElement(P3.Provider,{value:{...$,placement:E,ref:f}},V),ce.createElement(ay,{onDismiss:l.close}));if(!v)return ce.createElement(ly,{...s,shouldContainFocus:k,isExiting:n,portalContainer:i},!s.isNonModal&&l.isOpen&&ce.createElement("div",{"data-testid":"underlay",...b,style:{position:"fixed",inset:0}}),ce.createElement("div",{ref:d,style:{display:"contents"}},ce.createElement(iy.Provider,{value:d},re)));var te;return ce.createElement(ly,{...s,shouldContainFocus:k,isExiting:n,portalContainer:(te=i??(h==null?void 0:h.current))!==null&&te!==void 0?te:void 0},re)}const G3=M.createContext(null);class Y3 extends Ms{filter(n,i,u){let s=n.getItem(this.firstChildKey);if(s&&u(s.textValue,this)){let f=this.clone();return i.addDescendants(f,n),f}return null}}Y3.type="submenutrigger";const C0=M.createContext(null),dd=M.createContext(null);function f4(l){let n=Q3(l),i=M.useRef(null),{triggerProps:u,overlayProps:s}=m3({type:"dialog"},n,i),[f,d]=M.useState(null),h=M.useCallback(()=>{i.current&&d(i.current.offsetWidth+"px")},[i]);return Rf({ref:i,onResize:h}),u.id=gi(),s["aria-labelledby"]=u.id,ce.createElement(bm,{values:[[dd,n],[G3,n],[C0,s],[O0,{trigger:"DialogTrigger",triggerRef:i,"aria-labelledby":s["aria-labelledby"],style:{"--trigger-width":f}}]]},ce.createElement(n3,{...u,ref:i,isPressed:n.isOpen},l.children))}const d4=M.forwardRef(function(n,i){let u=n["aria-labelledby"];[n,i]=id(n,i,C0);let{dialogProps:s,titleProps:f}=K3({...n,"aria-labelledby":u},i),d=M.useContext(dd);!s["aria-label"]&&!s["aria-labelledby"]&&n["aria-labelledby"]&&(s["aria-labelledby"]=n["aria-labelledby"]);let h=ld({defaultClassName:"react-aria-Dialog",className:n.className,style:n.style,children:n.children,values:{close:(d==null?void 0:d.close)||(()=>{})}}),v=Br(n,{global:!0});return ce.createElement("section",{...jt(v,h,s),ref:i,slot:n.slot||void 0},ce.createElement(bm,{values:[[dE,{slots:{[Mf]:{},title:{...f,level:2}}}],[T0,{slots:{[Mf]:{},close:{onPress:()=>d==null?void 0:d.close()}}}]]},h.children))});function V3(){const{i18n:l,t:n}=fE();return{onChangeLang:M.useCallback(u=>{l.changeLanguage(u)},[l]),translate:(u,s)=>n(u,s),currentLang:l.language,isRTL:l.language==="ara"}}const h4=({lang:l})=>{const{currentLang:n,onChangeLang:i}=V3();M.useEffect(()=>{l&&n!==l&&i(l)},[l])},ve=l=>typeof l=="string",$r=()=>{let l,n;const i=new Promise((u,s)=>{l=u,n=s});return i.resolve=l,i.reject=n,i},ry=l=>l==null?"":""+l,X3=(l,n,i)=>{l.forEach(u=>{n[u]&&(i[u]=n[u])})},F3=/###/g,uy=l=>l&&l.indexOf("###")>-1?l.replace(F3,"."):l,sy=l=>!l||ve(l),Rr=(l,n,i)=>{const u=ve(n)?n.split("."):n;let s=0;for(;s<u.length-1;){if(sy(l))return{};const f=uy(u[s]);!l[f]&&i&&(l[f]=new i),Object.prototype.hasOwnProperty.call(l,f)?l=l[f]:l={},++s}return sy(l)?{}:{obj:l,k:uy(u[s])}},oy=(l,n,i)=>{const{obj:u,k:s}=Rr(l,n,Object);if(u!==void 0||n.length===1){u[s]=i;return}let f=n[n.length-1],d=n.slice(0,n.length-1),h=Rr(l,d,Object);for(;h.obj===void 0&&d.length;)f=`${d[d.length-1]}.${f}`,d=d.slice(0,d.length-1),h=Rr(l,d,Object),h!=null&&h.obj&&typeof h.obj[`${h.k}.${f}`]<"u"&&(h.obj=void 0);h.obj[`${h.k}.${f}`]=i},Z3=(l,n,i,u)=>{const{obj:s,k:f}=Rr(l,n,Object);s[f]=s[f]||[],s[f].push(i)},Es=(l,n)=>{const{obj:i,k:u}=Rr(l,n);if(i&&Object.prototype.hasOwnProperty.call(i,u))return i[u]},J3=(l,n,i)=>{const u=Es(l,i);return u!==void 0?u:Es(n,i)},w0=(l,n,i)=>{for(const u in n)u!=="__proto__"&&u!=="constructor"&&(u in l?ve(l[u])||l[u]instanceof String||ve(n[u])||n[u]instanceof String?i&&(l[u]=n[u]):w0(l[u],n[u],i):l[u]=n[u]);return l},Zl=l=>l.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var W3={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const I3=l=>ve(l)?l.replace(/[&<>"'\/]/g,n=>W3[n]):l;class e${constructor(n){this.capacity=n,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(n){const i=this.regExpMap.get(n);if(i!==void 0)return i;const u=new RegExp(n);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(n,u),this.regExpQueue.push(n),u}}const t$=[" ",",","?","!",";"],n$=new e$(20),a$=(l,n,i)=>{n=n||"",i=i||"";const u=t$.filter(d=>n.indexOf(d)<0&&i.indexOf(d)<0);if(u.length===0)return!0;const s=n$.getRegExp(`(${u.map(d=>d==="?"?"\\?":d).join("|")})`);let f=!s.test(l);if(!f){const d=l.indexOf(i);d>0&&!s.test(l.substring(0,d))&&(f=!0)}return f},Bf=(l,n,i=".")=>{if(!l)return;if(l[n])return Object.prototype.hasOwnProperty.call(l,n)?l[n]:void 0;const u=n.split(i);let s=l;for(let f=0;f<u.length;){if(!s||typeof s!="object")return;let d,h="";for(let v=f;v<u.length;++v)if(v!==f&&(h+=i),h+=u[v],d=s[h],d!==void 0){if(["string","number","boolean"].indexOf(typeof d)>-1&&v<u.length-1)continue;f+=v-f+1;break}s=d}return s},Lr=l=>l==null?void 0:l.replace("_","-"),l$={type:"logger",log(l){this.output("log",l)},warn(l){this.output("warn",l)},error(l){this.output("error",l)},output(l,n){var i,u;(u=(i=console==null?void 0:console[l])==null?void 0:i.apply)==null||u.call(i,console,n)}};class $s{constructor(n,i={}){this.init(n,i)}init(n,i={}){this.prefix=i.prefix||"i18next:",this.logger=n||l$,this.options=i,this.debug=i.debug}log(...n){return this.forward(n,"log","",!0)}warn(...n){return this.forward(n,"warn","",!0)}error(...n){return this.forward(n,"error","")}deprecate(...n){return this.forward(n,"warn","WARNING DEPRECATED: ",!0)}forward(n,i,u,s){return s&&!this.debug?null:(ve(n[0])&&(n[0]=`${u}${this.prefix} ${n[0]}`),this.logger[i](n))}create(n){return new $s(this.logger,{prefix:`${this.prefix}:${n}:`,...this.options})}clone(n){return n=n||this.options,n.prefix=n.prefix||this.prefix,new $s(this.logger,n)}}var yn=new $s;class Ns{constructor(){this.observers={}}on(n,i){return n.split(" ").forEach(u=>{this.observers[u]||(this.observers[u]=new Map);const s=this.observers[u].get(i)||0;this.observers[u].set(i,s+1)}),this}off(n,i){if(this.observers[n]){if(!i){delete this.observers[n];return}this.observers[n].delete(i)}}emit(n,...i){this.observers[n]&&Array.from(this.observers[n].entries()).forEach(([s,f])=>{for(let d=0;d<f;d++)s(...i)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([s,f])=>{for(let d=0;d<f;d++)s.apply(s,[n,...i])})}}class cy extends Ns{constructor(n,i={ns:["translation"],defaultNS:"translation"}){super(),this.data=n||{},this.options=i,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(n){this.options.ns.indexOf(n)<0&&this.options.ns.push(n)}removeNamespaces(n){const i=this.options.ns.indexOf(n);i>-1&&this.options.ns.splice(i,1)}getResource(n,i,u,s={}){var p,m;const f=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,d=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let h;n.indexOf(".")>-1?h=n.split("."):(h=[n,i],u&&(Array.isArray(u)?h.push(...u):ve(u)&&f?h.push(...u.split(f)):h.push(u)));const v=Es(this.data,h);return!v&&!i&&!u&&n.indexOf(".")>-1&&(n=h[0],i=h[1],u=h.slice(2).join(".")),v||!d||!ve(u)?v:Bf((m=(p=this.data)==null?void 0:p[n])==null?void 0:m[i],u,f)}addResource(n,i,u,s,f={silent:!1}){const d=f.keySeparator!==void 0?f.keySeparator:this.options.keySeparator;let h=[n,i];u&&(h=h.concat(d?u.split(d):u)),n.indexOf(".")>-1&&(h=n.split("."),s=i,i=h[1]),this.addNamespaces(i),oy(this.data,h,s),f.silent||this.emit("added",n,i,u,s)}addResources(n,i,u,s={silent:!1}){for(const f in u)(ve(u[f])||Array.isArray(u[f]))&&this.addResource(n,i,f,u[f],{silent:!0});s.silent||this.emit("added",n,i,u)}addResourceBundle(n,i,u,s,f,d={silent:!1,skipCopy:!1}){let h=[n,i];n.indexOf(".")>-1&&(h=n.split("."),s=u,u=i,i=h[1]),this.addNamespaces(i);let v=Es(this.data,h)||{};d.skipCopy||(u=JSON.parse(JSON.stringify(u))),s?w0(v,u,f):v={...v,...u},oy(this.data,h,v),d.silent||this.emit("added",n,i,u)}removeResourceBundle(n,i){this.hasResourceBundle(n,i)&&delete this.data[n][i],this.removeNamespaces(i),this.emit("removed",n,i)}hasResourceBundle(n,i){return this.getResource(n,i)!==void 0}getResourceBundle(n,i){return i||(i=this.options.defaultNS),this.getResource(n,i)}getDataByLanguage(n){return this.data[n]}hasLanguageSomeTranslations(n){const i=this.getDataByLanguage(n);return!!(i&&Object.keys(i)||[]).find(s=>i[s]&&Object.keys(i[s]).length>0)}toJSON(){return this.data}}var R0={processors:{},addPostProcessor(l){this.processors[l.name]=l},handle(l,n,i,u,s){return l.forEach(f=>{var d;n=((d=this.processors[f])==null?void 0:d.process(n,i,u,s))??n}),n}};const A0=Symbol("i18next/PATH_KEY");function i$(){const l=[],n=Object.create(null);let i;return n.get=(u,s)=>{var f;return(f=i==null?void 0:i.revoke)==null||f.call(i),s===A0?l:(l.push(s),i=Proxy.revocable(u,n),i.proxy)},Proxy.revocable(Object.create(null),n).proxy}function Qf(l,n){const{[A0]:i}=l(i$());return i.join((n==null?void 0:n.keySeparator)??".")}const fy={},dy=l=>!ve(l)&&typeof l!="boolean"&&typeof l!="number";class Ts extends Ns{constructor(n,i={}){super(),X3(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],n,this),this.options=i,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=yn.create("translator")}changeLanguage(n){n&&(this.language=n)}exists(n,i={interpolation:{}}){const u={...i};if(n==null)return!1;const s=this.resolve(n,u);return(s==null?void 0:s.res)!==void 0}extractFromKey(n,i){let u=i.nsSeparator!==void 0?i.nsSeparator:this.options.nsSeparator;u===void 0&&(u=":");const s=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let f=i.ns||this.options.defaultNS||[];const d=u&&n.indexOf(u)>-1,h=!this.options.userDefinedKeySeparator&&!i.keySeparator&&!this.options.userDefinedNsSeparator&&!i.nsSeparator&&!a$(n,u,s);if(d&&!h){const v=n.match(this.interpolator.nestingRegexp);if(v&&v.length>0)return{key:n,namespaces:ve(f)?[f]:f};const p=n.split(u);(u!==s||u===s&&this.options.ns.indexOf(p[0])>-1)&&(f=p.shift()),n=p.join(s)}return{key:n,namespaces:ve(f)?[f]:f}}translate(n,i,u){let s=typeof i=="object"?{...i}:i;if(typeof s!="object"&&this.options.overloadTranslationOptionHandler&&(s=this.options.overloadTranslationOptionHandler(arguments)),typeof s=="object"&&(s={...s}),s||(s={}),n==null)return"";typeof n=="function"&&(n=Qf(n,{...this.options,...s})),Array.isArray(n)||(n=[String(n)]);const f=s.returnDetails!==void 0?s.returnDetails:this.options.returnDetails,d=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,{key:h,namespaces:v}=this.extractFromKey(n[n.length-1],s),p=v[v.length-1];let m=s.nsSeparator!==void 0?s.nsSeparator:this.options.nsSeparator;m===void 0&&(m=":");const b=s.lng||this.language,$=s.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((b==null?void 0:b.toLowerCase())==="cimode")return $?f?{res:`${p}${m}${h}`,usedKey:h,exactUsedKey:h,usedLng:b,usedNS:p,usedParams:this.getUsedParamsDetails(s)}:`${p}${m}${h}`:f?{res:h,usedKey:h,exactUsedKey:h,usedLng:b,usedNS:p,usedParams:this.getUsedParamsDetails(s)}:h;const E=this.resolve(n,s);let O=E==null?void 0:E.res;const R=(E==null?void 0:E.usedKey)||h,N=(E==null?void 0:E.exactUsedKey)||h,P=["[object Number]","[object Function]","[object RegExp]"],K=s.joinArrays!==void 0?s.joinArrays:this.options.joinArrays,k=!this.i18nFormat||this.i18nFormat.handleAsObject,B=s.count!==void 0&&!ve(s.count),V=Ts.hasDefaultValue(s),ee=B?this.pluralResolver.getSuffix(b,s.count,s):"",re=s.ordinal&&B?this.pluralResolver.getSuffix(b,s.count,{ordinal:!1}):"",te=B&&!s.ordinal&&s.count===0,ae=te&&s[`defaultValue${this.options.pluralSeparator}zero`]||s[`defaultValue${ee}`]||s[`defaultValue${re}`]||s.defaultValue;let F=O;k&&!O&&V&&(F=ae);const U=dy(F),X=Object.prototype.toString.apply(F);if(k&&F&&U&&P.indexOf(X)<0&&!(ve(K)&&Array.isArray(F))){if(!s.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const le=this.options.returnedObjectHandler?this.options.returnedObjectHandler(R,F,{...s,ns:v}):`key '${h} (${this.language})' returned an object instead of string.`;return f?(E.res=le,E.usedParams=this.getUsedParamsDetails(s),E):le}if(d){const le=Array.isArray(F),Y=le?[]:{},pe=le?N:R;for(const C in F)if(Object.prototype.hasOwnProperty.call(F,C)){const G=`${pe}${d}${C}`;V&&!O?Y[C]=this.translate(G,{...s,defaultValue:dy(ae)?ae[C]:void 0,joinArrays:!1,ns:v}):Y[C]=this.translate(G,{...s,joinArrays:!1,ns:v}),Y[C]===G&&(Y[C]=F[C])}O=Y}}else if(k&&ve(K)&&Array.isArray(O))O=O.join(K),O&&(O=this.extendTranslation(O,n,s,u));else{let le=!1,Y=!1;!this.isValidLookup(O)&&V&&(le=!0,O=ae),this.isValidLookup(O)||(Y=!0,O=h);const C=(s.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&Y?void 0:O,G=V&&ae!==O&&this.options.updateMissing;if(Y||le||G){if(this.logger.log(G?"updateKey":"missingKey",b,p,h,G?ae:O),d){const H=this.resolve(h,{...s,keySeparator:!1});H&&H.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let W=[];const oe=this.languageUtils.getFallbackCodes(this.options.fallbackLng,s.lng||this.language);if(this.options.saveMissingTo==="fallback"&&oe&&oe[0])for(let H=0;H<oe.length;H++)W.push(oe[H]);else this.options.saveMissingTo==="all"?W=this.languageUtils.toResolveHierarchy(s.lng||this.language):W.push(s.lng||this.language);const x=(H,J,Z)=>{var Se;const ue=V&&Z!==O?Z:C;this.options.missingKeyHandler?this.options.missingKeyHandler(H,p,J,ue,G,s):(Se=this.backendConnector)!=null&&Se.saveMissing&&this.backendConnector.saveMissing(H,p,J,ue,G,s),this.emit("missingKey",H,p,J,O)};this.options.saveMissing&&(this.options.saveMissingPlurals&&B?W.forEach(H=>{const J=this.pluralResolver.getSuffixes(H,s);te&&s[`defaultValue${this.options.pluralSeparator}zero`]&&J.indexOf(`${this.options.pluralSeparator}zero`)<0&&J.push(`${this.options.pluralSeparator}zero`),J.forEach(Z=>{x([H],h+Z,s[`defaultValue${Z}`]||ae)})}):x(W,h,ae))}O=this.extendTranslation(O,n,s,E,u),Y&&O===h&&this.options.appendNamespaceToMissingKey&&(O=`${p}${m}${h}`),(Y||le)&&this.options.parseMissingKeyHandler&&(O=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${p}${m}${h}`:h,le?O:void 0,s))}return f?(E.res=O,E.usedParams=this.getUsedParamsDetails(s),E):O}extendTranslation(n,i,u,s,f){var v,p;if((v=this.i18nFormat)!=null&&v.parse)n=this.i18nFormat.parse(n,{...this.options.interpolation.defaultVariables,...u},u.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!u.skipInterpolation){u.interpolation&&this.interpolator.init({...u,interpolation:{...this.options.interpolation,...u.interpolation}});const m=ve(n)&&(((p=u==null?void 0:u.interpolation)==null?void 0:p.skipOnVariables)!==void 0?u.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let b;if(m){const E=n.match(this.interpolator.nestingRegexp);b=E&&E.length}let $=u.replace&&!ve(u.replace)?u.replace:u;if(this.options.interpolation.defaultVariables&&($={...this.options.interpolation.defaultVariables,...$}),n=this.interpolator.interpolate(n,$,u.lng||this.language||s.usedLng,u),m){const E=n.match(this.interpolator.nestingRegexp),O=E&&E.length;b<O&&(u.nest=!1)}!u.lng&&s&&s.res&&(u.lng=this.language||s.usedLng),u.nest!==!1&&(n=this.interpolator.nest(n,(...E)=>(f==null?void 0:f[0])===E[0]&&!u.context?(this.logger.warn(`It seems you are nesting recursively key: ${E[0]} in key: ${i[0]}`),null):this.translate(...E,i),u)),u.interpolation&&this.interpolator.reset()}const d=u.postProcess||this.options.postProcess,h=ve(d)?[d]:d;return n!=null&&(h!=null&&h.length)&&u.applyPostProcessor!==!1&&(n=R0.handle(h,n,i,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(u)},...u}:u,this)),n}resolve(n,i={}){let u,s,f,d,h;return ve(n)&&(n=[n]),n.forEach(v=>{if(this.isValidLookup(u))return;const p=this.extractFromKey(v,i),m=p.key;s=m;let b=p.namespaces;this.options.fallbackNS&&(b=b.concat(this.options.fallbackNS));const $=i.count!==void 0&&!ve(i.count),E=$&&!i.ordinal&&i.count===0,O=i.context!==void 0&&(ve(i.context)||typeof i.context=="number")&&i.context!=="",R=i.lngs?i.lngs:this.languageUtils.toResolveHierarchy(i.lng||this.language,i.fallbackLng);b.forEach(N=>{var P,K;this.isValidLookup(u)||(h=N,!fy[`${R[0]}-${N}`]&&((P=this.utils)!=null&&P.hasLoadedNamespace)&&!((K=this.utils)!=null&&K.hasLoadedNamespace(h))&&(fy[`${R[0]}-${N}`]=!0,this.logger.warn(`key "${s}" for languages "${R.join(", ")}" won't get resolved as namespace "${h}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),R.forEach(k=>{var ee;if(this.isValidLookup(u))return;d=k;const B=[m];if((ee=this.i18nFormat)!=null&&ee.addLookupKeys)this.i18nFormat.addLookupKeys(B,m,k,N,i);else{let re;$&&(re=this.pluralResolver.getSuffix(k,i.count,i));const te=`${this.options.pluralSeparator}zero`,ae=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if($&&(i.ordinal&&re.indexOf(ae)===0&&B.push(m+re.replace(ae,this.options.pluralSeparator)),B.push(m+re),E&&B.push(m+te)),O){const F=`${m}${this.options.contextSeparator||"_"}${i.context}`;B.push(F),$&&(i.ordinal&&re.indexOf(ae)===0&&B.push(F+re.replace(ae,this.options.pluralSeparator)),B.push(F+re),E&&B.push(F+te))}}let V;for(;V=B.pop();)this.isValidLookup(u)||(f=V,u=this.getResource(k,N,V,i))}))})}),{res:u,usedKey:s,exactUsedKey:f,usedLng:d,usedNS:h}}isValidLookup(n){return n!==void 0&&!(!this.options.returnNull&&n===null)&&!(!this.options.returnEmptyString&&n==="")}getResource(n,i,u,s={}){var f;return(f=this.i18nFormat)!=null&&f.getResource?this.i18nFormat.getResource(n,i,u,s):this.resourceStore.getResource(n,i,u,s)}getUsedParamsDetails(n={}){const i=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],u=n.replace&&!ve(n.replace);let s=u?n.replace:n;if(u&&typeof n.count<"u"&&(s.count=n.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!u){s={...s};for(const f of i)delete s[f]}return s}static hasDefaultValue(n){const i="defaultValue";for(const u in n)if(Object.prototype.hasOwnProperty.call(n,u)&&i===u.substring(0,i.length)&&n[u]!==void 0)return!0;return!1}}class hy{constructor(n){this.options=n,this.supportedLngs=this.options.supportedLngs||!1,this.logger=yn.create("languageUtils")}getScriptPartFromCode(n){if(n=Lr(n),!n||n.indexOf("-")<0)return null;const i=n.split("-");return i.length===2||(i.pop(),i[i.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(i.join("-"))}getLanguagePartFromCode(n){if(n=Lr(n),!n||n.indexOf("-")<0)return n;const i=n.split("-");return this.formatLanguageCode(i[0])}formatLanguageCode(n){if(ve(n)&&n.indexOf("-")>-1){let i;try{i=Intl.getCanonicalLocales(n)[0]}catch{}return i&&this.options.lowerCaseLng&&(i=i.toLowerCase()),i||(this.options.lowerCaseLng?n.toLowerCase():n)}return this.options.cleanCode||this.options.lowerCaseLng?n.toLowerCase():n}isSupportedCode(n){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(n=this.getLanguagePartFromCode(n)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(n)>-1}getBestMatchFromCodes(n){if(!n)return null;let i;return n.forEach(u=>{if(i)return;const s=this.formatLanguageCode(u);(!this.options.supportedLngs||this.isSupportedCode(s))&&(i=s)}),!i&&this.options.supportedLngs&&n.forEach(u=>{if(i)return;const s=this.getScriptPartFromCode(u);if(this.isSupportedCode(s))return i=s;const f=this.getLanguagePartFromCode(u);if(this.isSupportedCode(f))return i=f;i=this.options.supportedLngs.find(d=>{if(d===f)return d;if(!(d.indexOf("-")<0&&f.indexOf("-")<0)&&(d.indexOf("-")>0&&f.indexOf("-")<0&&d.substring(0,d.indexOf("-"))===f||d.indexOf(f)===0&&f.length>1))return d})}),i||(i=this.getFallbackCodes(this.options.fallbackLng)[0]),i}getFallbackCodes(n,i){if(!n)return[];if(typeof n=="function"&&(n=n(i)),ve(n)&&(n=[n]),Array.isArray(n))return n;if(!i)return n.default||[];let u=n[i];return u||(u=n[this.getScriptPartFromCode(i)]),u||(u=n[this.formatLanguageCode(i)]),u||(u=n[this.getLanguagePartFromCode(i)]),u||(u=n.default),u||[]}toResolveHierarchy(n,i){const u=this.getFallbackCodes((i===!1?[]:i)||this.options.fallbackLng||[],n),s=[],f=d=>{d&&(this.isSupportedCode(d)?s.push(d):this.logger.warn(`rejecting language code not found in supportedLngs: ${d}`))};return ve(n)&&(n.indexOf("-")>-1||n.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&f(this.formatLanguageCode(n)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&f(this.getScriptPartFromCode(n)),this.options.load!=="currentOnly"&&f(this.getLanguagePartFromCode(n))):ve(n)&&f(this.formatLanguageCode(n)),u.forEach(d=>{s.indexOf(d)<0&&f(this.formatLanguageCode(d))}),s}}const py={zero:0,one:1,two:2,few:3,many:4,other:5},gy={select:l=>l===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class r${constructor(n,i={}){this.languageUtils=n,this.options=i,this.logger=yn.create("pluralResolver"),this.pluralRulesCache={}}addRule(n,i){this.rules[n]=i}clearCache(){this.pluralRulesCache={}}getRule(n,i={}){const u=Lr(n==="dev"?"en":n),s=i.ordinal?"ordinal":"cardinal",f=JSON.stringify({cleanedCode:u,type:s});if(f in this.pluralRulesCache)return this.pluralRulesCache[f];let d;try{d=new Intl.PluralRules(u,{type:s})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),gy;if(!n.match(/-|_/))return gy;const v=this.languageUtils.getLanguagePartFromCode(n);d=this.getRule(v,i)}return this.pluralRulesCache[f]=d,d}needsPlural(n,i={}){let u=this.getRule(n,i);return u||(u=this.getRule("dev",i)),(u==null?void 0:u.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(n,i,u={}){return this.getSuffixes(n,u).map(s=>`${i}${s}`)}getSuffixes(n,i={}){let u=this.getRule(n,i);return u||(u=this.getRule("dev",i)),u?u.resolvedOptions().pluralCategories.sort((s,f)=>py[s]-py[f]).map(s=>`${this.options.prepend}${i.ordinal?`ordinal${this.options.prepend}`:""}${s}`):[]}getSuffix(n,i,u={}){const s=this.getRule(n,u);return s?`${this.options.prepend}${u.ordinal?`ordinal${this.options.prepend}`:""}${s.select(i)}`:(this.logger.warn(`no plural rule found for: ${n}`),this.getSuffix("dev",i,u))}}const vy=(l,n,i,u=".",s=!0)=>{let f=J3(l,n,i);return!f&&s&&ve(i)&&(f=Bf(l,i,u),f===void 0&&(f=Bf(n,i,u))),f},sf=l=>l.replace(/\$/g,"$$$$");class u${constructor(n={}){var i;this.logger=yn.create("interpolator"),this.options=n,this.format=((i=n==null?void 0:n.interpolation)==null?void 0:i.format)||(u=>u),this.init(n)}init(n={}){n.interpolation||(n.interpolation={escapeValue:!0});const{escape:i,escapeValue:u,useRawValueToEscape:s,prefix:f,prefixEscaped:d,suffix:h,suffixEscaped:v,formatSeparator:p,unescapeSuffix:m,unescapePrefix:b,nestingPrefix:$,nestingPrefixEscaped:E,nestingSuffix:O,nestingSuffixEscaped:R,nestingOptionsSeparator:N,maxReplaces:P,alwaysFormat:K}=n.interpolation;this.escape=i!==void 0?i:I3,this.escapeValue=u!==void 0?u:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=f?Zl(f):d||"{{",this.suffix=h?Zl(h):v||"}}",this.formatSeparator=p||",",this.unescapePrefix=m?"":b||"-",this.unescapeSuffix=this.unescapePrefix?"":m||"",this.nestingPrefix=$?Zl($):E||Zl("$t("),this.nestingSuffix=O?Zl(O):R||Zl(")"),this.nestingOptionsSeparator=N||",",this.maxReplaces=P||1e3,this.alwaysFormat=K!==void 0?K:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const n=(i,u)=>(i==null?void 0:i.source)===u?(i.lastIndex=0,i):new RegExp(u,"g");this.regexp=n(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=n(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=n(this.nestingRegexp,`${this.nestingPrefix}((?:[^()"']+|"[^"]*"|'[^']*'|\\((?:[^()]|"[^"]*"|'[^']*')*\\))*?)${this.nestingSuffix}`)}interpolate(n,i,u,s){var E;let f,d,h;const v=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},p=O=>{if(O.indexOf(this.formatSeparator)<0){const K=vy(i,v,O,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(K,void 0,u,{...s,...i,interpolationkey:O}):K}const R=O.split(this.formatSeparator),N=R.shift().trim(),P=R.join(this.formatSeparator).trim();return this.format(vy(i,v,N,this.options.keySeparator,this.options.ignoreJSONStructure),P,u,{...s,...i,interpolationkey:N})};this.resetRegExp();const m=(s==null?void 0:s.missingInterpolationHandler)||this.options.missingInterpolationHandler,b=((E=s==null?void 0:s.interpolation)==null?void 0:E.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:O=>sf(O)},{regex:this.regexp,safeValue:O=>this.escapeValue?sf(this.escape(O)):sf(O)}].forEach(O=>{for(h=0;f=O.regex.exec(n);){const R=f[1].trim();if(d=p(R),d===void 0)if(typeof m=="function"){const P=m(n,f,s);d=ve(P)?P:""}else if(s&&Object.prototype.hasOwnProperty.call(s,R))d="";else if(b){d=f[0];continue}else this.logger.warn(`missed to pass in variable ${R} for interpolating ${n}`),d="";else!ve(d)&&!this.useRawValueToEscape&&(d=ry(d));const N=O.safeValue(d);if(n=n.replace(f[0],N),b?(O.regex.lastIndex+=d.length,O.regex.lastIndex-=f[0].length):O.regex.lastIndex=0,h++,h>=this.maxReplaces)break}}),n}nest(n,i,u={}){let s,f,d;const h=(v,p)=>{const m=this.nestingOptionsSeparator;if(v.indexOf(m)<0)return v;const b=v.split(new RegExp(`${m}[ ]*{`));let $=`{${b[1]}`;v=b[0],$=this.interpolate($,d);const E=$.match(/'/g),O=$.match(/"/g);(((E==null?void 0:E.length)??0)%2===0&&!O||O.length%2!==0)&&($=$.replace(/'/g,'"'));try{d=JSON.parse($),p&&(d={...p,...d})}catch(R){return this.logger.warn(`failed parsing options string in nesting for key ${v}`,R),`${v}${m}${$}`}return d.defaultValue&&d.defaultValue.indexOf(this.prefix)>-1&&delete d.defaultValue,v};for(;s=this.nestingRegexp.exec(n);){let v=[];d={...u},d=d.replace&&!ve(d.replace)?d.replace:d,d.applyPostProcessor=!1,delete d.defaultValue;const p=/{.*}/.test(s[1])?s[1].lastIndexOf("}")+1:s[1].indexOf(this.formatSeparator);if(p!==-1&&(v=s[1].slice(p).split(this.formatSeparator).map(m=>m.trim()).filter(Boolean),s[1]=s[1].slice(0,p)),f=i(h.call(this,s[1].trim(),d),d),f&&s[0]===n&&!ve(f))return f;ve(f)||(f=ry(f)),f||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${n}`),f=""),v.length&&(f=v.reduce((m,b)=>this.format(m,b,u.lng,{...u,interpolationkey:s[1].trim()}),f.trim())),n=n.replace(s[0],f),this.regexp.lastIndex=0}return n}}const s$=l=>{let n=l.toLowerCase().trim();const i={};if(l.indexOf("(")>-1){const u=l.split("(");n=u[0].toLowerCase().trim();const s=u[1].substring(0,u[1].length-1);n==="currency"&&s.indexOf(":")<0?i.currency||(i.currency=s.trim()):n==="relativetime"&&s.indexOf(":")<0?i.range||(i.range=s.trim()):s.split(";").forEach(d=>{if(d){const[h,...v]=d.split(":"),p=v.join(":").trim().replace(/^'+|'+$/g,""),m=h.trim();i[m]||(i[m]=p),p==="false"&&(i[m]=!1),p==="true"&&(i[m]=!0),isNaN(p)||(i[m]=parseInt(p,10))}})}return{formatName:n,formatOptions:i}},yy=l=>{const n={};return(i,u,s)=>{let f=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(f={...f,[s.interpolationkey]:void 0});const d=u+JSON.stringify(f);let h=n[d];return h||(h=l(Lr(u),s),n[d]=h),h(i)}},o$=l=>(n,i,u)=>l(Lr(i),u)(n);class c${constructor(n={}){this.logger=yn.create("formatter"),this.options=n,this.init(n)}init(n,i={interpolation:{}}){this.formatSeparator=i.interpolation.formatSeparator||",";const u=i.cacheInBuiltFormats?yy:o$;this.formats={number:u((s,f)=>{const d=new Intl.NumberFormat(s,{...f});return h=>d.format(h)}),currency:u((s,f)=>{const d=new Intl.NumberFormat(s,{...f,style:"currency"});return h=>d.format(h)}),datetime:u((s,f)=>{const d=new Intl.DateTimeFormat(s,{...f});return h=>d.format(h)}),relativetime:u((s,f)=>{const d=new Intl.RelativeTimeFormat(s,{...f});return h=>d.format(h,f.range||"day")}),list:u((s,f)=>{const d=new Intl.ListFormat(s,{...f});return h=>d.format(h)})}}add(n,i){this.formats[n.toLowerCase().trim()]=i}addCached(n,i){this.formats[n.toLowerCase().trim()]=yy(i)}format(n,i,u,s={}){const f=i.split(this.formatSeparator);if(f.length>1&&f[0].indexOf("(")>1&&f[0].indexOf(")")<0&&f.find(h=>h.indexOf(")")>-1)){const h=f.findIndex(v=>v.indexOf(")")>-1);f[0]=[f[0],...f.splice(1,h)].join(this.formatSeparator)}return f.reduce((h,v)=>{var b;const{formatName:p,formatOptions:m}=s$(v);if(this.formats[p]){let $=h;try{const E=((b=s==null?void 0:s.formatParams)==null?void 0:b[s.interpolationkey])||{},O=E.locale||E.lng||s.locale||s.lng||u;$=this.formats[p](h,O,{...m,...s,...E})}catch(E){this.logger.warn(E)}return $}else this.logger.warn(`there was no format function for ${p}`);return h},n)}}const f$=(l,n)=>{l.pending[n]!==void 0&&(delete l.pending[n],l.pendingCount--)};class d$ extends Ns{constructor(n,i,u,s={}){var f,d;super(),this.backend=n,this.store=i,this.services=u,this.languageUtils=u.languageUtils,this.options=s,this.logger=yn.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],(d=(f=this.backend)==null?void 0:f.init)==null||d.call(f,u,s.backend,s)}queueLoad(n,i,u,s){const f={},d={},h={},v={};return n.forEach(p=>{let m=!0;i.forEach(b=>{const $=`${p}|${b}`;!u.reload&&this.store.hasResourceBundle(p,b)?this.state[$]=2:this.state[$]<0||(this.state[$]===1?d[$]===void 0&&(d[$]=!0):(this.state[$]=1,m=!1,d[$]===void 0&&(d[$]=!0),f[$]===void 0&&(f[$]=!0),v[b]===void 0&&(v[b]=!0)))}),m||(h[p]=!0)}),(Object.keys(f).length||Object.keys(d).length)&&this.queue.push({pending:d,pendingCount:Object.keys(d).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(f),pending:Object.keys(d),toLoadLanguages:Object.keys(h),toLoadNamespaces:Object.keys(v)}}loaded(n,i,u){const s=n.split("|"),f=s[0],d=s[1];i&&this.emit("failedLoading",f,d,i),!i&&u&&this.store.addResourceBundle(f,d,u,void 0,void 0,{skipCopy:!0}),this.state[n]=i?-1:2,i&&u&&(this.state[n]=0);const h={};this.queue.forEach(v=>{Z3(v.loaded,[f],d),f$(v,n),i&&v.errors.push(i),v.pendingCount===0&&!v.done&&(Object.keys(v.loaded).forEach(p=>{h[p]||(h[p]={});const m=v.loaded[p];m.length&&m.forEach(b=>{h[p][b]===void 0&&(h[p][b]=!0)})}),v.done=!0,v.errors.length?v.callback(v.errors):v.callback())}),this.emit("loaded",h),this.queue=this.queue.filter(v=>!v.done)}read(n,i,u,s=0,f=this.retryTimeout,d){if(!n.length)return d(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:n,ns:i,fcName:u,tried:s,wait:f,callback:d});return}this.readingCalls++;const h=(p,m)=>{if(this.readingCalls--,this.waitingReads.length>0){const b=this.waitingReads.shift();this.read(b.lng,b.ns,b.fcName,b.tried,b.wait,b.callback)}if(p&&m&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,n,i,u,s+1,f*2,d)},f);return}d(p,m)},v=this.backend[u].bind(this.backend);if(v.length===2){try{const p=v(n,i);p&&typeof p.then=="function"?p.then(m=>h(null,m)).catch(h):h(null,p)}catch(p){h(p)}return}return v(n,i,h)}prepareLoading(n,i,u={},s){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();ve(n)&&(n=this.languageUtils.toResolveHierarchy(n)),ve(i)&&(i=[i]);const f=this.queueLoad(n,i,u,s);if(!f.toLoad.length)return f.pending.length||s(),null;f.toLoad.forEach(d=>{this.loadOne(d)})}load(n,i,u){this.prepareLoading(n,i,{},u)}reload(n,i,u){this.prepareLoading(n,i,{reload:!0},u)}loadOne(n,i=""){const u=n.split("|"),s=u[0],f=u[1];this.read(s,f,"read",void 0,void 0,(d,h)=>{d&&this.logger.warn(`${i}loading namespace ${f} for language ${s} failed`,d),!d&&h&&this.logger.log(`${i}loaded namespace ${f} for language ${s}`,h),this.loaded(n,d,h)})}saveMissing(n,i,u,s,f,d={},h=()=>{}){var v,p,m,b,$;if((p=(v=this.services)==null?void 0:v.utils)!=null&&p.hasLoadedNamespace&&!((b=(m=this.services)==null?void 0:m.utils)!=null&&b.hasLoadedNamespace(i))){this.logger.warn(`did not save key "${u}" as the namespace "${i}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(u==null||u==="")){if(($=this.backend)!=null&&$.create){const E={...d,isUpdate:f},O=this.backend.create.bind(this.backend);if(O.length<6)try{let R;O.length===5?R=O(n,i,u,s,E):R=O(n,i,u,s),R&&typeof R.then=="function"?R.then(N=>h(null,N)).catch(h):h(null,R)}catch(R){h(R)}else O(n,i,u,s,h,E)}!n||!n[0]||this.store.addResource(n[0],i,u,s)}}}const my=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:l=>{let n={};if(typeof l[1]=="object"&&(n=l[1]),ve(l[1])&&(n.defaultValue=l[1]),ve(l[2])&&(n.tDescription=l[2]),typeof l[2]=="object"||typeof l[3]=="object"){const i=l[3]||l[2];Object.keys(i).forEach(u=>{n[u]=i[u]})}return n},interpolation:{escapeValue:!0,format:l=>l,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),by=l=>{var n,i;return ve(l.ns)&&(l.ns=[l.ns]),ve(l.fallbackLng)&&(l.fallbackLng=[l.fallbackLng]),ve(l.fallbackNS)&&(l.fallbackNS=[l.fallbackNS]),((i=(n=l.supportedLngs)==null?void 0:n.indexOf)==null?void 0:i.call(n,"cimode"))<0&&(l.supportedLngs=l.supportedLngs.concat(["cimode"])),typeof l.initImmediate=="boolean"&&(l.initAsync=l.initImmediate),l},fs=()=>{},h$=l=>{Object.getOwnPropertyNames(Object.getPrototypeOf(l)).forEach(i=>{typeof l[i]=="function"&&(l[i]=l[i].bind(l))})};class _r extends Ns{constructor(n={},i){if(super(),this.options=by(n),this.services={},this.logger=yn,this.modules={external:[]},h$(this),i&&!this.isInitialized&&!n.isClone){if(!this.options.initAsync)return this.init(n,i),this;setTimeout(()=>{this.init(n,i)},0)}}init(n={},i){this.isInitializing=!0,typeof n=="function"&&(i=n,n={}),n.defaultNS==null&&n.ns&&(ve(n.ns)?n.defaultNS=n.ns:n.ns.indexOf("translation")<0&&(n.defaultNS=n.ns[0]));const u=my();this.options={...u,...this.options,...by(n)},this.options.interpolation={...u.interpolation,...this.options.interpolation},n.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=n.keySeparator),n.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=n.nsSeparator);const s=p=>p?typeof p=="function"?new p:p:null;if(!this.options.isClone){this.modules.logger?yn.init(s(this.modules.logger),this.options):yn.init(null,this.options);let p;this.modules.formatter?p=this.modules.formatter:p=c$;const m=new hy(this.options);this.store=new cy(this.options.resources,this.options);const b=this.services;b.logger=yn,b.resourceStore=this.store,b.languageUtils=m,b.pluralResolver=new r$(m,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==u.interpolation.format&&this.logger.deprecate("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),p&&(!this.options.interpolation.format||this.options.interpolation.format===u.interpolation.format)&&(b.formatter=s(p),b.formatter.init&&b.formatter.init(b,this.options),this.options.interpolation.format=b.formatter.format.bind(b.formatter)),b.interpolator=new u$(this.options),b.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},b.backendConnector=new d$(s(this.modules.backend),b.resourceStore,b,this.options),b.backendConnector.on("*",(E,...O)=>{this.emit(E,...O)}),this.modules.languageDetector&&(b.languageDetector=s(this.modules.languageDetector),b.languageDetector.init&&b.languageDetector.init(b,this.options.detection,this.options)),this.modules.i18nFormat&&(b.i18nFormat=s(this.modules.i18nFormat),b.i18nFormat.init&&b.i18nFormat.init(this)),this.translator=new Ts(this.services,this.options),this.translator.on("*",(E,...O)=>{this.emit(E,...O)}),this.modules.external.forEach(E=>{E.init&&E.init(this)})}if(this.format=this.options.interpolation.format,i||(i=fs),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const p=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);p.length>0&&p[0]!=="dev"&&(this.options.lng=p[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(p=>{this[p]=(...m)=>this.store[p](...m)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(p=>{this[p]=(...m)=>(this.store[p](...m),this)});const h=$r(),v=()=>{const p=(m,b)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),h.resolve(b),i(m,b)};if(this.languages&&!this.isInitialized)return p(null,this.t.bind(this));this.changeLanguage(this.options.lng,p)};return this.options.resources||!this.options.initAsync?v():setTimeout(v,0),h}loadResources(n,i=fs){var f,d;let u=i;const s=ve(n)?n:this.language;if(typeof n=="function"&&(u=n),!this.options.resources||this.options.partialBundledLanguages){if((s==null?void 0:s.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return u();const h=[],v=p=>{if(!p||p==="cimode")return;this.services.languageUtils.toResolveHierarchy(p).forEach(b=>{b!=="cimode"&&h.indexOf(b)<0&&h.push(b)})};s?v(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(m=>v(m)),(d=(f=this.options.preload)==null?void 0:f.forEach)==null||d.call(f,p=>v(p)),this.services.backendConnector.load(h,this.options.ns,p=>{!p&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),u(p)})}else u(null)}reloadResources(n,i,u){const s=$r();return typeof n=="function"&&(u=n,n=void 0),typeof i=="function"&&(u=i,i=void 0),n||(n=this.languages),i||(i=this.options.ns),u||(u=fs),this.services.backendConnector.reload(n,i,f=>{s.resolve(),u(f)}),s}use(n){if(!n)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!n.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return n.type==="backend"&&(this.modules.backend=n),(n.type==="logger"||n.log&&n.warn&&n.error)&&(this.modules.logger=n),n.type==="languageDetector"&&(this.modules.languageDetector=n),n.type==="i18nFormat"&&(this.modules.i18nFormat=n),n.type==="postProcessor"&&R0.addPostProcessor(n),n.type==="formatter"&&(this.modules.formatter=n),n.type==="3rdParty"&&this.modules.external.push(n),this}setResolvedLanguage(n){if(!(!n||!this.languages)&&!(["cimode","dev"].indexOf(n)>-1)){for(let i=0;i<this.languages.length;i++){const u=this.languages[i];if(!(["cimode","dev"].indexOf(u)>-1)&&this.store.hasLanguageSomeTranslations(u)){this.resolvedLanguage=u;break}}!this.resolvedLanguage&&this.languages.indexOf(n)<0&&this.store.hasLanguageSomeTranslations(n)&&(this.resolvedLanguage=n,this.languages.unshift(n))}}changeLanguage(n,i){this.isLanguageChangingTo=n;const u=$r();this.emit("languageChanging",n);const s=h=>{this.language=h,this.languages=this.services.languageUtils.toResolveHierarchy(h),this.resolvedLanguage=void 0,this.setResolvedLanguage(h)},f=(h,v)=>{v?this.isLanguageChangingTo===n&&(s(v),this.translator.changeLanguage(v),this.isLanguageChangingTo=void 0,this.emit("languageChanged",v),this.logger.log("languageChanged",v)):this.isLanguageChangingTo=void 0,u.resolve((...p)=>this.t(...p)),i&&i(h,(...p)=>this.t(...p))},d=h=>{var m,b;!n&&!h&&this.services.languageDetector&&(h=[]);const v=ve(h)?h:h&&h[0],p=this.store.hasLanguageSomeTranslations(v)?v:this.services.languageUtils.getBestMatchFromCodes(ve(h)?[h]:h);p&&(this.language||s(p),this.translator.language||this.translator.changeLanguage(p),(b=(m=this.services.languageDetector)==null?void 0:m.cacheUserLanguage)==null||b.call(m,p)),this.loadResources(p,$=>{f($,p)})};return!n&&this.services.languageDetector&&!this.services.languageDetector.async?d(this.services.languageDetector.detect()):!n&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(d):this.services.languageDetector.detect(d):d(n),u}getFixedT(n,i,u){const s=(f,d,...h)=>{let v;typeof d!="object"?v=this.options.overloadTranslationOptionHandler([f,d].concat(h)):v={...d},v.lng=v.lng||s.lng,v.lngs=v.lngs||s.lngs,v.ns=v.ns||s.ns,v.keyPrefix!==""&&(v.keyPrefix=v.keyPrefix||u||s.keyPrefix);const p=this.options.keySeparator||".";let m;return v.keyPrefix&&Array.isArray(f)?m=f.map(b=>(typeof b=="function"&&(b=Qf(b,{...this.options,...d})),`${v.keyPrefix}${p}${b}`)):(typeof f=="function"&&(f=Qf(f,{...this.options,...d})),m=v.keyPrefix?`${v.keyPrefix}${p}${f}`:f),this.t(m,v)};return ve(n)?s.lng=n:s.lngs=n,s.ns=i,s.keyPrefix=u,s}t(...n){var i;return(i=this.translator)==null?void 0:i.translate(...n)}exists(...n){var i;return(i=this.translator)==null?void 0:i.exists(...n)}setDefaultNamespace(n){this.options.defaultNS=n}hasLoadedNamespace(n,i={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const u=i.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,f=this.languages[this.languages.length-1];if(u.toLowerCase()==="cimode")return!0;const d=(h,v)=>{const p=this.services.backendConnector.state[`${h}|${v}`];return p===-1||p===0||p===2};if(i.precheck){const h=i.precheck(this,d);if(h!==void 0)return h}return!!(this.hasResourceBundle(u,n)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||d(u,n)&&(!s||d(f,n)))}loadNamespaces(n,i){const u=$r();return this.options.ns?(ve(n)&&(n=[n]),n.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{u.resolve(),i&&i(s)}),u):(i&&i(),Promise.resolve())}loadLanguages(n,i){const u=$r();ve(n)&&(n=[n]);const s=this.options.preload||[],f=n.filter(d=>s.indexOf(d)<0&&this.services.languageUtils.isSupportedCode(d));return f.length?(this.options.preload=s.concat(f),this.loadResources(d=>{u.resolve(),i&&i(d)}),u):(i&&i(),Promise.resolve())}dir(n){var s,f;if(n||(n=this.resolvedLanguage||(((s=this.languages)==null?void 0:s.length)>0?this.languages[0]:this.language)),!n)return"rtl";try{const d=new Intl.Locale(n);if(d&&d.getTextInfo){const h=d.getTextInfo();if(h&&h.direction)return h.direction}}catch{}const i=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],u=((f=this.services)==null?void 0:f.languageUtils)||new hy(my());return n.toLowerCase().indexOf("-latn")>1?"ltr":i.indexOf(u.getLanguagePartFromCode(n))>-1||n.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(n={},i){return new _r(n,i)}cloneInstance(n={},i=fs){const u=n.forkResourceStore;u&&delete n.forkResourceStore;const s={...this.options,...n,isClone:!0},f=new _r(s);if((n.debug!==void 0||n.prefix!==void 0)&&(f.logger=f.logger.clone(n)),["store","services","language"].forEach(h=>{f[h]=this[h]}),f.services={...this.services},f.services.utils={hasLoadedNamespace:f.hasLoadedNamespace.bind(f)},u){const h=Object.keys(this.store.data).reduce((v,p)=>(v[p]={...this.store.data[p]},v[p]=Object.keys(v[p]).reduce((m,b)=>(m[b]={...v[p][b]},m),v[p]),v),{});f.store=new cy(h,s),f.services.resourceStore=f.store}return f.translator=new Ts(f.services,s),f.translator.on("*",(h,...v)=>{f.emit(h,...v)}),f.init(s,i),f.translator.options=s,f.translator.backendConnector.services.utils={hasLoadedNamespace:f.hasLoadedNamespace.bind(f)},f}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const pt=_r.createInstance();pt.createInstance=_r.createInstance;pt.createInstance;pt.dir;pt.init;pt.loadResources;pt.reloadResources;pt.use;pt.changeLanguage;pt.getFixedT;pt.t;pt.exists;pt.setDefaultNamespace;pt.hasLoadedNamespace;pt.loadNamespaces;pt.loadLanguages;function Pf(l){"@babel/helpers - typeof";return Pf=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Pf(l)}function M0(){return typeof XMLHttpRequest=="function"||(typeof XMLHttpRequest>"u"?"undefined":Pf(XMLHttpRequest))==="object"}function p$(l){return!!l&&typeof l.then=="function"}function g$(l){return p$(l)?l:Promise.resolve(l)}const v$="modulepreload",y$=function(l){return"/v2/js/app/"+l},Sy={},m$=function(n,i,u){let s=Promise.resolve();if(i&&i.length>0){let d=function(p){return Promise.all(p.map(m=>Promise.resolve(m).then(b=>({status:"fulfilled",value:b}),b=>({status:"rejected",reason:b}))))};document.getElementsByTagName("link");const h=document.querySelector("meta[property=csp-nonce]"),v=(h==null?void 0:h.nonce)||(h==null?void 0:h.getAttribute("nonce"));s=d(i.map(p=>{if(p=y$(p),p in Sy)return;Sy[p]=!0;const m=p.endsWith(".css"),b=m?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${b}`))return;const $=document.createElement("link");if($.rel=m?"stylesheet":v$,m||($.as="script"),$.crossOrigin="",$.href=p,v&&$.setAttribute("nonce",v),document.head.appendChild($),m)return new Promise((E,O)=>{$.addEventListener("load",E),$.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${p}`)))})}))}function f(d){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=d,window.dispatchEvent(h),!h.defaultPrevented)throw d}return s.then(d=>{for(const h of d||[])h.status==="rejected"&&f(h.reason);return n().catch(f)})};function Ey(l,n){var i=Object.keys(l);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(l);n&&(u=u.filter(function(s){return Object.getOwnPropertyDescriptor(l,s).enumerable})),i.push.apply(i,u)}return i}function $y(l){for(var n=1;n<arguments.length;n++){var i=arguments[n]!=null?arguments[n]:{};n%2?Ey(Object(i),!0).forEach(function(u){b$(l,u,i[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(i)):Ey(Object(i)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(i,u))})}return l}function b$(l,n,i){return(n=S$(n))in l?Object.defineProperty(l,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):l[n]=i,l}function S$(l){var n=E$(l,"string");return sl(n)=="symbol"?n:n+""}function E$(l,n){if(sl(l)!="object"||!l)return l;var i=l[Symbol.toPrimitive];if(i!==void 0){var u=i.call(l,n);if(sl(u)!="object")return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(l)}function sl(l){"@babel/helpers - typeof";return sl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},sl(l)}var Ca=typeof fetch=="function"?fetch:void 0;typeof global<"u"&&global.fetch?Ca=global.fetch:typeof window<"u"&&window.fetch&&(Ca=window.fetch);var zr;M0()&&(typeof global<"u"&&global.XMLHttpRequest?zr=global.XMLHttpRequest:typeof window<"u"&&window.XMLHttpRequest&&(zr=window.XMLHttpRequest));var xs;typeof ActiveXObject=="function"&&(typeof global<"u"&&global.ActiveXObject?xs=global.ActiveXObject:typeof window<"u"&&window.ActiveXObject&&(xs=window.ActiveXObject));typeof Ca!="function"&&(Ca=void 0);if(!Ca&&!zr&&!xs)try{m$(()=>import("./browser-ponyfill-L_L3IBtS.js").then(l=>l.b),[]).then(function(l){Ca=l.default}).catch(function(){})}catch{}var kf=function(n,i){if(i&&sl(i)==="object"){var u="";for(var s in i)u+="&"+encodeURIComponent(s)+"="+encodeURIComponent(i[s]);if(!u)return n;n=n+(n.indexOf("?")!==-1?"&":"?")+u.slice(1)}return n},Ty=function(n,i,u,s){var f=function(v){if(!v.ok)return u(v.statusText||"Error",{status:v.status});v.text().then(function(p){u(null,{status:v.status,data:p})}).catch(u)};if(s){var d=s(n,i);if(d instanceof Promise){d.then(f).catch(u);return}}typeof fetch=="function"?fetch(n,i).then(f).catch(u):Ca(n,i).then(f).catch(u)},xy=!1,$$=function(n,i,u,s){n.queryStringParams&&(i=kf(i,n.queryStringParams));var f=$y({},typeof n.customHeaders=="function"?n.customHeaders():n.customHeaders);typeof window>"u"&&typeof global<"u"&&typeof global.process<"u"&&global.process.versions&&global.process.versions.node&&(f["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),u&&(f["Content-Type"]="application/json");var d=typeof n.requestOptions=="function"?n.requestOptions(u):n.requestOptions,h=$y({method:u?"POST":"GET",body:u?n.stringify(u):void 0,headers:f},xy?{}:d),v=typeof n.alternateFetch=="function"&&n.alternateFetch.length>=1?n.alternateFetch:void 0;try{Ty(i,h,s,v)}catch(p){if(!d||Object.keys(d).length===0||!p.message||p.message.indexOf("not implemented")<0)return s(p);try{Object.keys(d).forEach(function(m){delete h[m]}),Ty(i,h,s,v),xy=!0}catch(m){s(m)}}},T$=function(n,i,u,s){u&&sl(u)==="object"&&(u=kf("",u).slice(1)),n.queryStringParams&&(i=kf(i,n.queryStringParams));try{var f=zr?new zr:new xs("MSXML2.XMLHTTP.3.0");f.open(u?"POST":"GET",i,1),n.crossDomain||f.setRequestHeader("X-Requested-With","XMLHttpRequest"),f.withCredentials=!!n.withCredentials,u&&f.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),f.overrideMimeType&&f.overrideMimeType("application/json");var d=n.customHeaders;if(d=typeof d=="function"?d():d,d)for(var h in d)f.setRequestHeader(h,d[h]);f.onreadystatechange=function(){f.readyState>3&&s(f.status>=400?f.statusText:null,{status:f.status,data:f.responseText})},f.send(u)}catch(v){console&&console.log(v)}},x$=function(n,i,u,s){if(typeof u=="function"&&(s=u,u=void 0),s=s||function(){},Ca&&i.indexOf("file:")!==0)return $$(n,i,u,s);if(M0()||typeof ActiveXObject=="function")return T$(n,i,u,s);s(new Error("No fetch and no xhr implementation found!"))};function bi(l){"@babel/helpers - typeof";return bi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},bi(l)}function Oy(l,n){var i=Object.keys(l);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(l);n&&(u=u.filter(function(s){return Object.getOwnPropertyDescriptor(l,s).enumerable})),i.push.apply(i,u)}return i}function of(l){for(var n=1;n<arguments.length;n++){var i=arguments[n]!=null?arguments[n]:{};n%2?Oy(Object(i),!0).forEach(function(u){N0(l,u,i[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(i)):Oy(Object(i)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(i,u))})}return l}function O$(l,n){if(!(l instanceof n))throw new TypeError("Cannot call a class as a function")}function C$(l,n){for(var i=0;i<n.length;i++){var u=n[i];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(l,D0(u.key),u)}}function w$(l,n,i){return n&&C$(l.prototype,n),Object.defineProperty(l,"prototype",{writable:!1}),l}function N0(l,n,i){return(n=D0(n))in l?Object.defineProperty(l,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):l[n]=i,l}function D0(l){var n=R$(l,"string");return bi(n)=="symbol"?n:n+""}function R$(l,n){if(bi(l)!="object"||!l)return l;var i=l[Symbol.toPrimitive];if(i!==void 0){var u=i.call(l,n);if(bi(u)!="object")return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(l)}var A$=function(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(i){return JSON.parse(i)},stringify:JSON.stringify,parsePayload:function(i,u,s){return N0({},u,s||"")},parseLoadPayload:function(i,u){},request:x$,reloadInterval:typeof window<"u"?!1:3600*1e3,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}},L0=(function(){function l(n){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};O$(this,l),this.services=n,this.options=i,this.allOptions=u,this.type="backend",this.init(n,i,u)}return w$(l,[{key:"init",value:function(i){var u=this,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.services=i,this.options=of(of(of({},A$()),this.options||{}),s),this.allOptions=f,this.services&&this.options.reloadInterval){var d=setInterval(function(){return u.reload()},this.options.reloadInterval);bi(d)==="object"&&typeof d.unref=="function"&&d.unref()}}},{key:"readMulti",value:function(i,u,s){this._readAny(i,i,u,u,s)}},{key:"read",value:function(i,u,s){this._readAny([i],i,[u],u,s)}},{key:"_readAny",value:function(i,u,s,f,d){var h=this,v=this.options.loadPath;typeof this.options.loadPath=="function"&&(v=this.options.loadPath(i,s)),v=g$(v),v.then(function(p){if(!p)return d(null,{});var m=h.services.interpolator.interpolate(p,{lng:i.join("+"),ns:s.join("+")});h.loadUrl(m,d,u,f)})}},{key:"loadUrl",value:function(i,u,s,f){var d=this,h=typeof s=="string"?[s]:s,v=typeof f=="string"?[f]:f,p=this.options.parseLoadPayload(h,v);this.options.request(this.options,i,p,function(m,b){if(b&&(b.status>=500&&b.status<600||!b.status))return u("failed loading "+i+"; status code: "+b.status,!0);if(b&&b.status>=400&&b.status<500)return u("failed loading "+i+"; status code: "+b.status,!1);if(!b&&m&&m.message){var $=m.message.toLowerCase(),E=["failed","fetch","network","load"].find(function(N){return $.indexOf(N)>-1});if(E)return u("failed loading "+i+": "+m.message,!0)}if(m)return u(m,!1);var O,R;try{typeof b.data=="string"?O=d.options.parse(b.data,s,f):O=b.data}catch{R="failed parsing "+i+" to json"}if(R)return u(R,!1);u(null,O)})}},{key:"create",value:function(i,u,s,f,d){var h=this;if(this.options.addPath){typeof i=="string"&&(i=[i]);var v=this.options.parsePayload(u,s,f),p=0,m=[],b=[];i.forEach(function($){var E=h.options.addPath;typeof h.options.addPath=="function"&&(E=h.options.addPath($,u));var O=h.services.interpolator.interpolate(E,{lng:$,ns:u});h.options.request(h.options,O,v,function(R,N){p+=1,m.push(R),b.push(N),p===i.length&&typeof d=="function"&&d(m,b)})})}}},{key:"reload",value:function(){var i=this,u=this.services,s=u.backendConnector,f=u.languageUtils,d=u.logger,h=s.language;if(!(h&&h.toLowerCase()==="cimode")){var v=[],p=function(b){var $=f.toResolveHierarchy(b);$.forEach(function(E){v.indexOf(E)<0&&v.push(E)})};p(h),this.allOptions.preload&&this.allOptions.preload.forEach(function(m){return p(m)}),v.forEach(function(m){i.allOptions.ns.forEach(function(b){s.read(m,b,"read",null,null,function($,E){$&&d.warn("loading namespace ".concat(b," for language ").concat(m," failed"),$),!$&&E&&d.log("loaded namespace ".concat(b," for language ").concat(m),E),s.loaded("".concat(m,"|").concat(b),$,E)})})})}}}])})();L0.type="backend";const{slice:M$,forEach:N$}=[];function D$(l){return N$.call(M$.call(arguments,1),n=>{if(n)for(const i in n)l[i]===void 0&&(l[i]=n[i])}),l}function L$(l){return typeof l!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(i=>i.test(l))}const Cy=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,_$=function(l,n){const u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},s=encodeURIComponent(n);let f=`${l}=${s}`;if(u.maxAge>0){const d=u.maxAge-0;if(Number.isNaN(d))throw new Error("maxAge should be a Number");f+=`; Max-Age=${Math.floor(d)}`}if(u.domain){if(!Cy.test(u.domain))throw new TypeError("option domain is invalid");f+=`; Domain=${u.domain}`}if(u.path){if(!Cy.test(u.path))throw new TypeError("option path is invalid");f+=`; Path=${u.path}`}if(u.expires){if(typeof u.expires.toUTCString!="function")throw new TypeError("option expires is invalid");f+=`; Expires=${u.expires.toUTCString()}`}if(u.httpOnly&&(f+="; HttpOnly"),u.secure&&(f+="; Secure"),u.sameSite)switch(typeof u.sameSite=="string"?u.sameSite.toLowerCase():u.sameSite){case!0:f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"strict":f+="; SameSite=Strict";break;case"none":f+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return u.partitioned&&(f+="; Partitioned"),f},wy={create(l,n,i,u){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};i&&(s.expires=new Date,s.expires.setTime(s.expires.getTime()+i*60*1e3)),u&&(s.domain=u),document.cookie=_$(l,n,s)},read(l){const n=`${l}=`,i=document.cookie.split(";");for(let u=0;u<i.length;u++){let s=i[u];for(;s.charAt(0)===" ";)s=s.substring(1,s.length);if(s.indexOf(n)===0)return s.substring(n.length,s.length)}return null},remove(l,n){this.create(l,"",-1,n)}};var z$={name:"cookie",lookup(l){let{lookupCookie:n}=l;if(n&&typeof document<"u")return wy.read(n)||void 0},cacheUserLanguage(l,n){let{lookupCookie:i,cookieMinutes:u,cookieDomain:s,cookieOptions:f}=n;i&&typeof document<"u"&&wy.create(i,l,u,s,f)}},U$={name:"querystring",lookup(l){var u;let{lookupQuerystring:n}=l,i;if(typeof window<"u"){let{search:s}=window.location;!window.location.search&&((u=window.location.hash)==null?void 0:u.indexOf("?"))>-1&&(s=window.location.hash.substring(window.location.hash.indexOf("?")));const d=s.substring(1).split("&");for(let h=0;h<d.length;h++){const v=d[h].indexOf("=");v>0&&d[h].substring(0,v)===n&&(i=d[h].substring(v+1))}}return i}},H$={name:"hash",lookup(l){var s;let{lookupHash:n,lookupFromHashIndex:i}=l,u;if(typeof window<"u"){const{hash:f}=window.location;if(f&&f.length>2){const d=f.substring(1);if(n){const h=d.split("&");for(let v=0;v<h.length;v++){const p=h[v].indexOf("=");p>0&&h[v].substring(0,p)===n&&(u=h[v].substring(p+1))}}if(u)return u;if(!u&&i>-1){const h=f.match(/\/([a-zA-Z-]*)/g);return Array.isArray(h)?(s=h[typeof i=="number"?i:0])==null?void 0:s.replace("/",""):void 0}}}return u}};let Jl=null;const Ry=()=>{if(Jl!==null)return Jl;try{if(Jl=typeof window<"u"&&window.localStorage!==null,!Jl)return!1;const l="i18next.translate.boo";window.localStorage.setItem(l,"foo"),window.localStorage.removeItem(l)}catch{Jl=!1}return Jl};var j$={name:"localStorage",lookup(l){let{lookupLocalStorage:n}=l;if(n&&Ry())return window.localStorage.getItem(n)||void 0},cacheUserLanguage(l,n){let{lookupLocalStorage:i}=n;i&&Ry()&&window.localStorage.setItem(i,l)}};let Wl=null;const Ay=()=>{if(Wl!==null)return Wl;try{if(Wl=typeof window<"u"&&window.sessionStorage!==null,!Wl)return!1;const l="i18next.translate.boo";window.sessionStorage.setItem(l,"foo"),window.sessionStorage.removeItem(l)}catch{Wl=!1}return Wl};var K$={name:"sessionStorage",lookup(l){let{lookupSessionStorage:n}=l;if(n&&Ay())return window.sessionStorage.getItem(n)||void 0},cacheUserLanguage(l,n){let{lookupSessionStorage:i}=n;i&&Ay()&&window.sessionStorage.setItem(i,l)}},q$={name:"navigator",lookup(l){const n=[];if(typeof navigator<"u"){const{languages:i,userLanguage:u,language:s}=navigator;if(i)for(let f=0;f<i.length;f++)n.push(i[f]);u&&n.push(u),s&&n.push(s)}return n.length>0?n:void 0}},B$={name:"htmlTag",lookup(l){let{htmlTag:n}=l,i;const u=n||(typeof document<"u"?document.documentElement:null);return u&&typeof u.getAttribute=="function"&&(i=u.getAttribute("lang")),i}},Q$={name:"path",lookup(l){var s;let{lookupFromPathIndex:n}=l;if(typeof window>"u")return;const i=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(i)?(s=i[typeof n=="number"?n:0])==null?void 0:s.replace("/",""):void 0}},P$={name:"subdomain",lookup(l){var s,f;let{lookupFromSubdomainIndex:n}=l;const i=typeof n=="number"?n+1:1,u=typeof window<"u"&&((f=(s=window.location)==null?void 0:s.hostname)==null?void 0:f.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(u)return u[i]}};let _0=!1;try{document.cookie,_0=!0}catch{}const z0=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];_0||z0.splice(1,1);const k$=()=>({order:z0,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:l=>l});class U0{constructor(n){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(n,i)}init(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=n,this.options=D$(i,this.options||{},k$()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=s=>s.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=u,this.addDetector(z$),this.addDetector(U$),this.addDetector(j$),this.addDetector(K$),this.addDetector(q$),this.addDetector(B$),this.addDetector(Q$),this.addDetector(P$),this.addDetector(H$)}addDetector(n){return this.detectors[n.name]=n,this}detect(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,i=[];return n.forEach(u=>{if(this.detectors[u]){let s=this.detectors[u].lookup(this.options);s&&typeof s=="string"&&(s=[s]),s&&(i=i.concat(s))}}),i=i.filter(u=>u!=null&&!L$(u)).map(u=>this.options.convertDetectedLanguage(u)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?i:i.length>0?i[0]:null}cacheUserLanguage(n){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;i&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(n)>-1||i.forEach(u=>{this.detectors[u]&&this.detectors[u].cacheUserLanguage(n,this.options)}))}}U0.type="languageDetector";pt.use(L0).use(U0).use(rE).init({backend:{loadPath:(l,n)=>{const i=l[0],u=n[0],s=i.split("-")[0];if(i===s)return`/v2/js/app/locales/${s}/default/${u}.json`;const d=i.split("-")[1];return`/v2/js/app/locales/${s}/${d}/${u}.json`}},fallbackLng:l=>l&&l.includes("-")?l.split("-")[0]:"ara",ns:["core"],defaultNS:"core",interpolation:{escapeValue:!1}});export{P2 as $,a4 as A,fE as B,o4 as C,c4 as D,f4 as E,d4 as F,em as G,h4 as H,V3 as I,r4 as J,Y$ as K,j3 as L,I$ as M,W$ as N,NE as O,WE as P,Z$ as Q,ce as R,N2 as S,kE as T,Mr as U,FE as V,e4 as W,Dv as X,R3 as Y,Rt as Z,_e as _,J$ as a,t4 as a0,n4 as a1,x0 as a2,Rf as a3,dd as a4,O0 as a5,Jt as a6,Nv as a7,Zt as a8,hm as a9,By as aA,y3 as aB,S3 as aC,N3 as aD,e2 as aE,IE as aF,ay as aG,ly as aH,yi as aa,ud as ab,ed as ac,vi as ad,OE as ae,Dr as af,Lm as ag,xa as ah,mi as ai,cd as aj,om as ak,Dm as al,m3 as am,i4 as an,Yf as ao,I2 as ap,s4 as aq,u4 as ar,Ms as as,x3 as at,_3 as au,s3 as av,L3 as aw,V$ as ax,XS as ay,AS as az,t3 as b,X$ as c,jt as d,Br as e,u2 as f,gi as g,Jf as h,gm as i,sS as j,Ke as k,id as l,v3 as m,ld as n,bm as o,T0 as p,Mf as q,M as r,DE as s,dE as t,dm as u,l4 as v,r3 as w,O3 as x,U3 as y,s2 as z};
