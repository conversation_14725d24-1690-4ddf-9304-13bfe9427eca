<?php
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Utils\PermissionUtil;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MachineController;

Route::post('/machines/{machine:uuid}/iclock/cdata', [MachineController::class, 'zktMachineEndpoint'])
    ->withoutMiddleware(['web'])
    ->name('machine.iclock.endpoint');

Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function () {
    Route::resource('/holiday_lists', 'HolidayListController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/holiday_lists/assign_employees/{holiday_list_id}', 'HolidayListController@assignEmployees')
        ->name('holiday_lists.assign_employees') ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::post('/holiday_lists/assign_employees', 'HolidayListController@saveAssignEmployees')
        ->name('holiday_lists.save_assign_employees')->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);;
    Route::get('/holiday_list/get_public_holidays', 'HolidayListController@getPublicHolidays')
        ->name('holiday_lists.get_public_holidays') ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/leave_policies/unique', 'LeavePolicyController@unique')
        ->name('leave_policies.unique') ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);;
    Route::resource('/leave_policies', 'LeavePolicyController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/leave_policies/activate/{id}', 'LeavePolicyController@activate')
        ->name('leave_policies.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/leave_policies/deactivate/{id}', 'LeavePolicyController@deactivate')
        ->name('leave_policies.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/leave_policies/assign_employees/{leave_policy_id}', 'LeavePolicyController@assignEmployees')
        ->name('leave_policies.assign_employees')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::post('/leave_policies/assign_employees', 'LeavePolicyController@saveAssignEmployees')
        ->name('leave_policies.save_assign_employees')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    Route::get('/allocated_shifts/search/','AllocatedShiftController@ajaxNameFilter')->name('allocated_shifts.search');
    /** allocated shifts */
    Route::Resource('/allocated_shifts', 'AllocatedShiftController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    /**** Attendance Flags ****/
    Route::resource('attendance_flags', 'AttendanceFlagsController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    Route::post('attendance_flags/{id}', 'AttendanceFlagsController@update')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    Route::get('attendance_flags/{attendance_flag}/activate', 'AttendanceFlagsController@activate')
        ->name('attendance_flags.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('attendance_flags/{attendance_flag}/deactivate', 'AttendanceFlagsController@deactivate')
        ->name('attendance_flags.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/attendance_settings', function () {
        return view('attendance/settings');
    })->name('attendance_settings')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    /*** leave types */
    Route::get('/leave_types/unique', 'LeaveTypeController@unique')
        ->name('leave_types.unique');
    Route::resource('/leave_types', 'LeaveTypeController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/attendance_settings/basic', 'SettingController@attendance_basic_settings')
        ->name('attendance_settings.basic')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::post('/attendance_settings/basic', 'SettingController@attendance_basic_settings')
        ->name('attendance_settings.basic')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    /** attendance permission routes */
    Route::Resource('/attendance_permissions', 'AttendancePermissionController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_PERMISSION);

    /** attendance sessions **/
    Route::get('/attendance_sessions', 'AttendanceSessionController@index')
        ->name('attendance_sessions.index')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE);
    Route::get('/attendance_sessions/open', 'AttendanceSessionController@open')
        ->name('attendance_sessions.open')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE);
    Route::get('/attendance_sessions/{attendance_session}', 'AttendanceSessionController@show')
        ->name('attendance_sessions.show')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE);
    Route::get('/attendance_sessions/{attendance_session}/close', 'AttendanceSessionController@close')
        ->name('attendance_sessions.close')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE);
    Route::delete('/attendance_sessions/{attendance_session}', 'AttendanceSessionController@destroy')
        ->name('attendance_sessions.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE);

    /** attendance logs **/
    Route::get('/attendance_logs', 'AttendanceLogController@index')
        ->name('attendance_logs.index')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE]));
    Route::get('/attendance_logs/get_all_logs_for_attendance_day/{attendance_day_id}', 'AttendanceLogController@getAllLogsForAttendanceDay')
        ->name('attendance_logs.get_all_logs_for_attendance_day')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG]));
    Route::get('/attendance_logs/{id}', 'AttendanceLogController@show')
        ->name('attendance_logs.show')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE]));
    Route::delete('/attendance_logs/{attendance_log}', 'AttendanceLogController@destroy')
        ->name('attendance_logs.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_ATTENDANCE_LOG);
    Route::delete('/attendance_logs/bulk/delete', 'AttendanceLogController@bulkDelete')
        ->name('attendance_logs.bulk_delete')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_ATTENDANCE_LOG);
    Route::post('/attendance_logs/sign/{session_id?}', 'AttendanceLogController@sign')
        ->name('attendance_logs.sign')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE, PermissionUtil::TAKE_HIS_OWN_ATTENDANCE]));
    Route::post('/attendance_logs/destroyMulti', 'AttendanceLogController@destroyMulti')
        ->name('attendance_logs.destroyMulti')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_ATTENDANCE_LOG);
    Route::get('/attendance_logs/last_sign/{session_id}/{staff_id}', 'AttendanceLogController@getStaffLastSessionSign')
        ->name('attendance_logs.last_sign');

    /** attendance permission routes */
    Route::get('/attendance_logs/last_sign/{staff_id}/{session_id?}', 'AttendanceLogController@getStaffLastSessionSign')
        ->name('attendance_logs.last_sign')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_HIS_OWN_ATTENDANCE);

    /** attendance day routes */
    Route::get('/attendance_days', 'AttendanceDayController@index')
        ->name('attendance_days.index')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE, PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET, PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG]));
    Route::get('/attendance_days/{attendance_day}/edit', 'AttendanceDayController@edit')
        ->name('attendance_days.edit')
        ->middleware('checkPermissions:' . PermissionUtil::EDIT_ATTENDANCE_DAY . ',' . PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE);
    Route::put('/attendance_days/{attendance_day}', 'AttendanceDayController@update')
        ->name('attendance_days.update')
        ->middleware('checkPermissions:' . PermissionUtil::EDIT_ATTENDANCE_DAY . ',' . PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE);
    Route::get('/attendance_days/{attendance_day}', 'AttendanceDayController@show')
        ->name('attendance_days.show')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE, PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG]));
    Route::delete('/attendance_days/{attendance_day}', 'AttendanceDayController@destroy')
        ->name('attendance_days.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::CALCULATE_ATTENDANCE_DAY);
    Route::delete('/attendance_days/bulk/delete', 'AttendanceDayController@bulkDelete')
        ->name('attendance_days.bulk_delete')
        ->middleware('checkPermissions:' . PermissionUtil::CALCULATE_ATTENDANCE_DAY);

    Route::get('/attendance_days/calculate/create', 'AttendanceDayCalculationController@create')
        ->name('attendance_days.calculation.create')
        ->middleware(['checkPermissions:' . PermissionUtil::CALCULATE_ATTENDANCE_DAY, "checkFreeTrail"]);
    Route::post('/attendance_days/calculate', 'AttendanceDayCalculationController@store')
        ->name('attendance_days.calculation.store')
        ->middleware(['checkPermissions:' . PermissionUtil::CALCULATE_ATTENDANCE_DAY, "checkFreeTrail"]);

    /**** Manual Attendance Day ****/
    Route::get('/attendance_days/calculation/manual/create', 'AttendanceDayCalculationController@createManual')
        ->name('attendance_days.calculation.create_manual')
        ->middleware(['checkPermissions:' . PermissionUtil::CALCULATE_ATTENDANCE_DAY, "checkFreeTrail"]);
    Route::post('/attendance_days/calculation/manual', 'AttendanceDayCalculationController@storeManual')
        ->name('attendance_days.calculation.store_manual')
        ->middleware(['checkPermissions:' . PermissionUtil::CALCULATE_ATTENDANCE_DAY, "checkFreeTrail"]);

    /** Attendance Sheet Routes */
    Route::get('/attendance_sheets', 'AttendanceSheetController@index')
        ->name('attendance_sheets.index')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET, PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS]));
    Route::get('/attendance_sheets/create', 'AttendanceSheetController@create')
        ->name('attendance_sheets.create')
        ->middleware(['checkPermissions:' . PermissionUtil::CREATE_ATTENDANCE_SHEET, "checkFreeTrail"]);
    Route::post('/attendance_sheets/change_status/{status}', 'AttendanceSheetController@changeMultiStatus')
        ->name('attendance_sheets.change_multi_status')
        ->middleware('checkPermissions:' . PermissionUtil::CHANGE_ATTENDANCE_SHEET_STATUS . ',' . PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE);
    Route::get('/attendance_sheets/{attendance_sheet}/change_status', 'AttendanceSheetController@changeStatus')
        ->name('attendance_sheets.change_status')
        ->middleware('checkPermissions:' . PermissionUtil::CHANGE_ATTENDANCE_SHEET_STATUS . ',' . PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE);
    Route::get('/attendance_sheets/{attendance_sheet}', 'AttendanceSheetController@show')
        ->name('attendance_sheets.show')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_HIS_DEPARTMENT_ATTENDANCE, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_SHEET, PermissionUtil::VIEW_OTHER_ATTENDANCE_SHEETS]));
    Route::post('/attendance_sheets', 'AttendanceSheetController@store')
        ->name('attendance_sheets.store')
        ->middleware(['checkPermissions:' . PermissionUtil::CREATE_ATTENDANCE_SHEET, "checkFreeTrail"]);
    Route::delete('/attendance_sheets/{attendance_sheet}', 'AttendanceSheetController@destroy')
        ->name('attendance_sheets.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_ATTENDANCE_SHEET);
    Route::delete('/attendance_sheets/bulk/delete', 'AttendanceSheetController@bulkDelete')
        ->name('attendance_sheets.bulk_delete')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_ATTENDANCE_SHEET);
    Route::post('/attendance_sheets/destroyMulti', 'AttendanceSheetController@destroyMulti')
        ->name('attendance_sheets.destroyMulti')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_ATTENDANCE_SHEET);

    /**** Machine ****/
    Route::Resource('/machines', 'MachineController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/machines/{machine}/activate', 'MachineController@activate')
        ->name('machines.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/machines/{machine}/deactivate', 'MachineController@deactivate')
        ->name('machines.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/machines/{machine}/test_connection', 'MachineController@testConnection')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS)
        ->name('machines.test');

    Route::get('/machines/{machine}/pull', 'MachineController@pullData')
        ->middleware('checkPermissions:' . PermissionUtil::PULL_MACHINE_ATTENDANCE_LOG)
        ->name('machines.pull');

    /**** Machine Mappings ****/
    Route::resource('machines.mappings', 'MachineMappingController', ['except' => ['show']])
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    Route::get('/machines/{machine}/unmapped_employee/create', 'MachineUnmappedEmployeeController@create')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS)
        ->name('machines.unmapped_employee.create');
    Route::post('/machines/{machine}/unmapped_employee/store', 'MachineUnmappedEmployeeController@store')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS)
        ->name('machines.unmapped_employee.store');

    Route::post('/machines/{machine}/iclock/cdata', 'MachineController@zktMachineEndpoint');

    /**** Reports ****/
    Route::group(['prefix' => 'reports/attendance', 'as' => 'reports.attendance.'], function () {
        Route::get('shift', 'AttendanceReportsController@shiftReportForm')
            ->name('shift')
            ->middleware('checkPermissions:' . PermissionUtil::VIEW_ATTENDANCE_REPORT);
        Route::post('shift', 'AttendanceReportsController@shiftReportData')
            ->name('shift.result')
            ->middleware('checkPermissions:' . PermissionUtil::VIEW_ATTENDANCE_REPORT);

        Route::get('attendance_multiple', 'AttendanceReportsController@attendanceReportMultipleForm')
            ->name('attendance_multiple')
            ->middleware('checkPermissions:' . PermissionUtil::VIEW_ATTENDANCE_REPORT);

        Route::get('attendance_multiple/export/excel', 'AttendanceReportsController@attendanceMultipleExportExcel')
            ->name('attendanceMultipleExportExcel')
            ->middleware('checkPermissions:' . PermissionUtil::VIEW_ATTENDANCE_REPORT);

        Route::get('attendance', 'AttendanceReportsController@attendanceReportSingleForm')
            ->name('attendance')
            ->middleware('checkPermissions:' . PermissionUtil::VIEW_ATTENDANCE_REPORT);

        Route::get('leave-balance', 'AttendanceReportsController@leaveBalanceAttendance')
            ->name('leave-balance')
            ->middleware('checkPermissions:' . PermissionUtil::VIEW_ATTENDANCE_REPORT);

        Route::get('attendance_balance_for_auth', 'AttendanceReportsController@leaveBalanceAttendanceForAuthStaff')
            ->name('attendance_balance_for_auth');

        Route::get('leave-balance-export', 'AttendanceReportsController@leaveBalanceAttendanceExport')
            ->name('leave-balance-export')
            ->middleware('checkPermissions:' . PermissionUtil::VIEW_ATTENDANCE_REPORT);

        Route::get('flags', 'AttendanceReportsController@flags')
            ->name('flags-summary')
            ->middleware('checkPermissions:' . implode(',', [PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG, PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG]));
    });


    /**** Attendance restrictions routes ****/
    Route::Resource('/attendance_restrictions', 'AttendanceRestrictionController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    Route::get('/attendance_restrictions/{attendance_restrictions}/activate', 'AttendanceRestrictionController@activate')
        ->name('attendance_restrictions.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    Route::get('/attendance_restrictions/{attendance_restrictions}/deactivate', 'AttendanceRestrictionController@deactivate')
        ->name('attendance_restrictions.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/attendance_restrictions/assign_employees/{attendance_restriction_id}', 'AttendanceRestrictionController@assignEmployees')
        ->name('attendance_restrictions.assign_employees')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);;
    Route::post('/attendance_restrictions/assign_employees', 'AttendanceRestrictionController@saveAssignEmployees')
        ->name('attendance_restrictions.save_assign_employees')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);;

    Route::post('/attendance_days/calc_auto_attendance', 'AttendanceDayCalculationController@calculateAttendanceAuto')
    ->name('attendance_days.calculateAttendanceAuto')
    ->middleware('checkPermissions:' . PermissionUtil::CALCULATE_ATTENDANCE_DAY);

     /** staff_leave_type_credit **/
    Route::get('/staff_leave_type_credit/{staff_id}/{fiscal_year}', 'StaffLeaveTypeCreditController@showByFisaclYear')
        ->name('staff_leave_type_credit.show_by_fiscal_year');
    Route::put('/staff_leave_type_credit', 'StaffLeaveTypeCreditController@updateLeaveTypesCredit')
        ->name('staff_leave_type_credit.update_leave_types_credit')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::CALCULATE_ATTENDANCE_DAY]));


    Route::get('/update_multi_cycle_configuration_status', 'MultiCycleApprovalConfigurationController@updateStatus')
        ->name('multi_cycle_approval_configuration.update_status')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

});

Route::group(['prefix' => 'api', 'as' => 'api.', 'middleware' => ['isApi', 'isActiveSite']], function ($router) {
    Route::post('/attendance_logs/sign/{session_id?}', 'AttendanceLogController@sign')
        ->name('attendance_logs.sign')
        ->middleware('checkPermissions:' . implode(',', [PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE, PermissionUtil::TAKE_HIS_OWN_ATTENDANCE]));

});
