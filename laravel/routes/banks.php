<?php


use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function () {
    Route::get('/banks/{id}/system_transactions', 'Bank\SystemTransactionsController@bank_transactions')->name('bank_transactions.system_transactions_listing');
    Route::get('/banks/{id}/match/{bank_transaction_id?}', 'BankTransactionsController@match')->name('banks.match');
    Route::get('/bank_transactions/{id}/unmatch', 'BankTransactionsController@unMatch')->name('bank_transactions.unmatch');
    Route::get('/bank_transactions/{id}/unmatch_system_transaction', 'BankTransactionsController@unMatchSystemTransaction')->name('system_transactions.unmatch');
    Route::get('/banks/{entityKey}', 'Bank\BanksListingController@index');
});
Route::group(['prefix' => 'api/', 'as' => 'api.', 'middleware' => 'isApi'], function () {
    Route::get('bank_transactions/{id}/match_transactions/{bank_transaction_id?}','BankTransactionsController@getBankTransactionsForMatch');
    Route::get('system_transactions/{id}/match_transactions/{system_transaction_id?}','BankTransactionsController@getBankSystemTransactionsForMatch');
    Route::post('bank_transactions/{bank_transaction_id}/match_transactions','BankTransactionsController@matchBankTransactionToSystemTransactions');
    Route::post('system_transactions/{system_transaction_id}/match_transactions','BankTransactionsController@matchSystemTransactionToBankTransactions');
    Route::get('bank_transactions/{id}/system_transactions','BankTransactionsController@getBankTransactionSystemTransactions');
    Route::get('system_transactions/{id}/bank_transactions/{bank_id}','BankTransactionsController@getSystemTransactionBankTransaction');
    Route::get('bank_transactions/{account_id}/last_saved_transaction/{expense_id}','BankTransactionsController@getAccountLastSavedTransaction');
});
