<?php

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Models\Limitation;
use App\Models\Plan;
use App\Models\PlanLimitation;
use App\Models\Site;
use App\Models\PortalPackage;
use App\Models\SiteLimitation;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StaffController;
use App\IzamViews\IzamView;
use App\Http\Controllers;
use App\Services\Traits\DefaultIzamViewData;
use App\Http\Controllers\MobileAppController;
use App\Http\Controllers\S3TempFilesManagerController;
use Aws\S3\S3Client;
use Illuminate\Support\Carbon;
use App\Http\Controllers\S3UploaderController;
use App\Http\Controllers\S3UploaderCustomController;
use Izam\Aws\Aws;
use Izam\Aws\UploaderHandler;
use Izam\Daftra\Common\Utils\PluginUtil;
use App\Facades\Plugins;
use Izam\Aws\S3UploadHandler;

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: *');
header('Access-Control-Allow-Headers: *');

Route::get('/temp', 'TempController@Temp');
Route::get('/cache/{operation}', 'TempController@cache');
Route::get('/temp2', 'TempController@Temp2');
Route::get('/translate/{word}', function ($word) {
    dd(__t($word));
});


Route::get('/', function () {
    return \Illuminate\Support\Facades\Redirect::to(CAKE_BASE_URL);
});

class IzamViewData
{
    use DefaultIzamViewData;

    public function __construct()
    {
        $this->setDefaultViewData();
    }
}


Route::get("test-fatoora", function () {

    resolve(\App\Modules\KSAElectronicInvoice\Services\FatooraExecutor::class)->testFatoora();
});


Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function () {

    Route::get('move/custom/{entity}', [S3UploaderCustomController::class, 'moveS3']);
    Route::get('/move/custom_all', [S3UploaderCustomController::class, 'moveAll']);
    Route::post('/testS3Uploader', function (Request $request) {
        $response = App(S3UploadHandler::class)->uploadFile($_FILES['file'], SITE_HASH, 'expense');
        return response()->json($response);
    })
        ->name('testS3Uploader');
    Route::view('testS3Uploader', 'testS3Uploader');


    Route::get('/mileage/mileage_rates', 'MileageRatesController@index')
        ->name('mileage.mileage_rates')
        ->middleware(['checkPermissions:' . PermissionUtil::Edit_General_Settings, 'isPluginActive:' . PluginUtil::MILEAGE_PLUGIN]);



    Route::post('/mileage_rates/store', 'MileageRatesController@store')
        ->name('mileage_rates.store')
        ->middleware(['checkPermissions:' . PermissionUtil::Edit_General_Settings, 'isPluginActive:' . PluginUtil::MILEAGE_PLUGIN]);



    Route::delete('/mileage_rates/destroy/{id}', 'MileageRatesController@destroy')
        ->name('mileage_rates.destroy')
        ->middleware(['checkPermissions:' . PermissionUtil::Edit_General_Settings, 'isPluginActive:' . PluginUtil::MILEAGE_PLUGIN]);



    Route::get('/mileages/get_mileage_rate', 'MileageRatesController@apiGetMilageRate')
        ->name('mileages.GetMilageRate');


    Route::get('/move/s3/{entity}', [S3UploaderController::class, 'moveS3']);
    Route::get('/move_s3/all', [S3UploaderController::class, 'moveForAll']);

    Route::get('/employee-attendance-widgets', function () {
        new IzamViewData();
        $izamView = new IzamView('staff/widgets/employee-attendance-widgets');
        return $izamView;
    });

    Route::get('/components/index', function () {
        new IzamViewData();
        $izamView = new IzamView('components/index');
        return $izamView;
    });

    Route::get('/hrm', function () {
        new IzamViewData();
        $izamView = new IzamView('hrm/index');
        return $izamView;
    })->name('setup.hrm.wizard');

    Route::get('/select-industry/index', function () {
        new IzamViewData();
        $izamView = new IzamView('select-industry/index');
        return $izamView;
    });


    Route::get('/support-channels/index', function () {
        new IzamViewData();
        $izamView = new IzamView('support-channels/index');
        return $izamView;
    });

    Route::get('/support-channels/sales-form', function () {
        new IzamViewData();
        $izamView = new IzamView('support-channels/sales-form');
        return $izamView;
    });

    Route::get('/support-channels/issues-form', function () {
        new IzamViewData();
        $izamView = new IzamView('support-channels/issues-form');
        return $izamView;
    });

    Route::get('/support-channels/know-how', function () {
        new IzamViewData();
        $izamView = new IzamView('support-channels/know-how');
        return $izamView;
    });



    Route::get('/support-channels/contact-info', function () {
        new IzamViewData();
        $izamView = new IzamView('support-channels/contact-info');
        return $izamView;
    });
    Route::get('/attendance/index', function () {
        new IzamViewData();
        $izamView = new IzamView('attendance/index');
        return $izamView;
    });
    Route::get('/item-groups/index', function () {
        new IzamViewData();
        $izamView = new IzamView('item-groups/index');
        return $izamView;
    });
    Route::get('/item-groups/view', function () {
        new IzamViewData();
        $izamView = new IzamView('item-groups/view');
        return $izamView;
    });
    Route::get('/item-groups/add', function () {
        new IzamViewData();
        $izamView = new IzamView('item-groups/add');
        return $izamView;
    });

    Route::get('/test/rollbar_log', 'Test\TestController@test_rollbar');
    Route::get('/test/generate_loyalty_points/{invoice_id}', 'Test\TestController@generateLoyaltyPoints');
    Route::get('uploader', FakeS3Controller::class);
    Route::get('requestHeaders', function () {
        dd(Request()->headers->all());
    });
});


Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function () {
    Route::get('/aws', function () {

        $s3 =  new S3Client([
            'region' => 'us-east-2',
            'version' => 'latest'
        ]);

        $cmd = $s3->getCommand('GetObject', [
            //'Bucket' => AWS_BUCKET,
            'Key'    => 'files/9ed5a5c2/expense/4b981f14-fad0-4809-8c31-8b53d4c5c097.png'
        ]);

        $request = $s3->createPresignedRequest($cmd, Carbon::now()->addMinutes(50));

        $url =  (string) $request->getUri();

        dd($s3, $cmd, $request, $url);
    });

    Route::get('/aws_with_bucket', function () {

        $s3 =  new S3Client([
            'region' => 'us-east-2',
            'version' => 'latest'
        ]);

        $cmd = $s3->getCommand('GetObject', [
            'Bucket' => 'daftra-clients-data',
            'Key'    => 'files/9ed5a5c2/journal/31e0dabf-949b-40ab-97fb-5ada80111e58.png'
        ]);

        $request = $s3->createPresignedRequest($cmd, Carbon::now()->addMinutes(50));

        $url =  (string) $request->getUri();
        echo "<img src='$url' height='50%' width='50%'><br>";


        $cmd = $s3->getCommand('GetObject', [
            'Bucket' => 'daftra-clients-data',
            'Key'    => 'files/9ed5a5c2/rental_reservation_order/0c8899ef-7724-4e5a-8542-c1e78e9eaf0e.jpg'
        ]);

        $request = $s3->createPresignedRequest($cmd, Carbon::now()->addMinutes(50));

        $url =  (string) $request->getUri();
        echo "<img src='$url' height='50%' width='50%'><br>";
    });

    Route::get('/aws_buckets', function () {

        $s3 =  new S3Client([
            'region' => 'us-east-2',
            'version' => 'latest'
        ]);
        dd($s3->listBuckets());
    });

    Route::get('updateTmpS3', [S3TempFilesManagerController::class, 'updateTempFiles']);
    Route::get('getUsedTempFiles', [S3TempFilesManagerController::class, 'getUsedTempFiles']);
    Route::get('getUnUsedFiles', [S3TempFilesManagerController::class, 'getUnUsedFiles']);

    Route::get('ListFilesS3', FakeFiles3Controller::class);
});

Route::get('/suspended-site', function () {
    return view('suspend');
})->name('suspended-site')->middleware('redirectActiveSite');

Route::get('/suspended-free-site', function () {
    return view('suspend-free');
})->name('suspended-free-site')->middleware('redirectActiveSite');
Route::group(['prefix' => 'client', 'as' => 'client.', 'middleware' => ['isClient', 'isActiveSite']], function ($router) {

    Route::group(['prefix' => 'site/notifications', 'as' => 'notifications'], function ($router) {
        Route::get('/', 'NotificationController@index')->name('.index');
        Route::get('/{id}/read', 'NotificationController@markAsRead')->name('.markRead');
        Route::get('/{id}/dismiss', 'NotificationController@dismiss')->name('.dismiss');
    });
    Route::group(['prefix' => 'site/updates'], function ($router) {
        Route::get('/', 'SystemUpdateController@index');
    });
});

Route::get('/qr/refresh', 'Auth\QRCodeController@refresh');
Route::get('/qr/staff-refresh/{staff_id}', [StaffController::class, 'refresh']);

Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function ($router) {

    Route::get('/mudad/generate/{id}', function ($id) {
        return view('mudad/generate', ['id' => $id]);
    })->name('mudad.generate');

    Route::get('/settings/share-social-media/{settingsKey}', 'Settings\SocialMediaController')->name('client.shareSocial');
    Route::post('/settings/client-share-social-media/save', 'Settings\SocialMediaController@storeSettings')->name('client.shareSocial.save');
    Route::get('/printable_templates/placeholders', function () {
        new IzamViewData();
        $izamView = new IzamView('printable_templates/placeholders');
        return $izamView;
    });

    Route::get('/printable_templates/create', function () {
        new IzamViewData();
        $izamView = new IzamView('printable_templates/create');
        return $izamView;
    });

    Route::get('/mobile-apps', function () {
        return view('mobile-apps/grid');
    })->name('mobile-apps');
    Route::get('/mobile-apps',  [MobileAppController::class, 'daftraMobilesApps'])->name('mobile-apps');

    Route::get('/mobile-apps/attendance/{employee_id?}', [MobileAppController::class, 'attendanceMobilesApps'])->name('attendance-mobile-app');

    Route::get('/mobile-apps/general', [MobileAppController::class, 'generalMobilesApps'])->name('general-mobile-app');
    Route::get('/mobile-apps/pos', [MobileAppController::class, 'posMobilesApps'])->name('pos-mobile-app');
    Route::get('/mobile-apps/expenses', [MobileAppController::class, 'expenseMobilesApps'])->name('expense-mobile-app');
    Route::get('/mobile-apps/items-stocktaking', [MobileAppController::class, 'itemsStocktakingMobilesApps'])->name('items-stocktaking-mobile-app');




    Route::get('/qr/show', 'Auth\QRCodeController@show')->name('qr-access');

    Route::get('tools/auto-number/invoice', 'FixTools\AutoNumberController@fix_invoices');
    Route::get('tools/auto-number/invoice-range', 'FixTools\AutoNumberController@fix_invoices_in_range');
    Route::get('tools/auto-number/purchase-invoice-range', 'FixTools\AutoNumberController@fix_purchase_invoices_in_range');
    Route::get('tools/auto-number/fix_journals_in_range/{start_id}/{correct_start_id}/{branch_id}', 'FixTools\AutoNumberController@fix_journals_in_range');
    Route::get('tools/auto-number/invoice-duplicated', 'FixTools\AutoNumberController@fixDuplicatedAutoNumbersAndExportCsv');

    Route::get('tools/all-sites/reapply-plugin/{plugin_id}', 'FixTools\ReapplyPluginInstallController@installPlugin');
    Route::get('tools/all-sites/reapply-plugin-site/{site_id}/{plugin_id}', 'FixTools\ReapplyPluginInstallController@install_per_site');

    Route::get('tools/workflow-change-integer-column-name-to-string', 'FixTools\WorkflowController@changeIntegerColumnNameToString');
    Route::get('tools/remove-unnecessary-fields-from-employee-custom-form', 'FixTools\StaffController@removeUnncessaryFieldsFromEmployeeCustomForm');
    Route::get('tools/add-has-many-relation', 'FixTools\LocalEntity@addBelongsToRelation');
    Route::get('tools/fix-workflow-types-without-entities/{id}', 'FixTools\WorkflowController@fixWorkflowTypeEntityStructure');
    Route::get('tools/add-has-many-relation', 'FixTools\LocalEntity@addBelongsToRelation');

    /** plugin manger **/
    Route::get('/plugin-manager/{page_id}', 'PluginMangerController@view')->where(['page_id' => '[1|2]+']);

    Route::get('/apps-manager/{type?}', 'AppManager\IndexController')
        ->name('apps-manager.index');

    Route::get('/apps-manager/activate/{id}/{status}', 'AppManager\AppManagerController@activate')->name('app_manager.activate');

    Route::group(['prefix' => 'apps-manager/reviews'], function () {
        Route::get('/{appId}', 'AppManager\\AppReviewController@show')->name('app_manager.review.show');
        Route::post('/{appId}', 'AppManager\\AppReviewController@updateAppReview')->name('app_manager.review.update');
    });

    Route::get('/apps-manager/install/{id}', 'AppManager\AppManagerController@install')
        ->name('app_manager.install');

    Route::get('/apps-manager/uninstall/{id}', 'AppManager\AppManagerController@uninstall')
        ->name('app_manager.uninstall');

    Route::get('/apps-manager/settings/{appId}', [App\Http\Controllers\AppManager\AppSettingsController::class, 'edit'])->name('apps-manager.settings');
    Route::post('/apps-manager/settings/{appId}', [App\Http\Controllers\AppManager\AppSettingsController::class, 'update']);

    Route::get('/apps-manager/send/{itemId}/{buttonId}', '\App\Http\Controllers\AppManager\ManualTriggerController')
        ->name('apps-manager.manual-trigger');

    Route::get('/ajax/apps-manager/send/{itemId}/{buttonId}', '\App\Http\Controllers\AppManager\ManualTriggerController@ajax')
        ->name('apps-manager.manual-trigger.ajax');

    /** client search */
    Route::get('/clients/filter', 'ClientController@ajaxNameFilter')->name('clients.filter');

    //stores
    Route::get('/store/search_user_can_update', 'StoreController@getStoresUsersCanUpdate')->name('store.search_user_can_update');
    Route::get('/store/list_stores', 'StoreController@getAllStores')->name('store.list.all');
    /** Production Routing */
    Route::get('/production_routing/search_sub_production_routing', 'ProductionRoutingController@searchProductionRouting')->name('production.routing.search');
    Route::get('/production_routing/search_main_production_routing', 'ProductionRoutingController@searchMainProductionRouting')->name('main.production.routing.search');
    Route::get('/production_routing/get_sub_production_routing_by_main_route', 'ProductionRoutingController@getSubProductionRoutingByMainRoute')->name('main.production.routing.sub.routings');

    Route::get('/workstations/search_workstations', 'WorkstationController@searchWorkstations')->name('workstations.search');

    /**** staff ****/
    Route::get('/staff/settings', 'StaffController@settings')->name('staff.settings')->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::get('/staff/organizational-chart', 'StaffOrganizationalChartController')->name('staff.organizational_chart')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs . ',' . PermissionUtil::MANAGE_HRM_SYSTEM . ',' . PermissionUtil::VIEW_ONLY_DEPARTMENT_CHART);
    Route::get('/test', 'Test\\TestController@index')->name('test.index');
    Route::get('/testcache', 'Test\\TestController@index')->name('test.index');
    Route::get('/staff/search', 'StaffController@ajaxNameFilter')->name('staff.search');
    Route::get('/staff/users/search', 'StaffController@getStaffUsersOnly')->name('staff.users.search');
    Route::get('/staff/users/all/search', 'StaffController@getAllUsers')->name('staff.allUsers.search');
    Route::get('/staff/searchForRequests', 'StaffController@searchForRequests')->name('staff.searchForRequests');
    Route::get('/staff/ajaxHasSecondaryShift', 'StaffController@ajaxHasSecondaryShift')->name('staff.ajaxHasSecondaryShift');

    /** Staff Treasury */
    Route::get('/staff_treasuries', 'StaffTreasuryController@index')->name('staff_treasuries.index')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::get('/staff_treasuries/create', 'StaffTreasuryController@create')->name('staff_treasuries.create')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::post('/staff_treasuries', 'StaffTreasuryController@store')->name('staff_treasuries.store')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::get('/staff_treasuries/{staff}/edit', 'StaffTreasuryController@edit')->name('staff_treasuries.edit')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::put('/staff_treasuries/{staff}/edit', 'StaffTreasuryController@update')->name('staff_treasuries.update')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::delete('/staff_treasuries/{staff}', 'StaffTreasuryController@destroy')->name('staff_treasuries.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    /** Staff Maximum discount */
    Route::get('/staff_maximum_discounts', 'StaffMaximumDiscountController@index')->name('staff_maximum_discounts.index')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::get('/staff_maximum_discounts/create', 'StaffMaximumDiscountController@create')->name('staff_maximum_discounts.create')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::post('/staff_maximum_discounts', 'StaffMaximumDiscountController@store')->name('staff_maximum_discounts.store')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::get('/staff_maximum_discounts/{staff}/edit', 'StaffMaximumDiscountController@edit')->name('staff_maximum_discounts.edit')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::put('/staff_maximum_discounts/{staff}/edit', 'StaffMaximumDiscountController@update')->name('staff_maximum_discounts.update')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    Route::delete('/staff_maximum_discounts/{staff}', 'StaffMaximumDiscountController@destroy')->name('staff_maximum_discounts.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);
    // finance settings
    Route::get('/finance_settings', function () {
        return view('finance/settings');
    })->name('finance_settings')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/finance_settings/general-settings', 'FinanceSettingsController@generalSettings')
        ->name('finance.general.settings')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);

    Route::post('/finance_settings/update-general-settings', 'FinanceSettingsController@updateGeneralSettings')
        ->name('update.finance.general.settings')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);

    Route::get('/staff/', 'StaffController@index')
        ->name('staff.index')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Add_New_Staffs . ',' . PermissionUtil::Staff_Edit_Staffs . ',' . PermissionUtil::Staff_View_Staffs);
    Route::get('/staff/create/{type}', 'StaffController@create')
        ->name('staff.create')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Add_New_Staffs);
    Route::post('/staff', 'StaffController@store')
        ->name('staff.store')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Add_New_Staffs);
    Route::get('/staff/{staff}', 'StaffController@show')
        ->name('staff.show')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Add_New_Staffs . ',' . PermissionUtil::Staff_Edit_Staffs . ',' . PermissionUtil::Staff_View_Staffs);
    Route::get('/staff/{staff}/edit', 'StaffController@edit')
        ->name('staff.edit')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::put('/staff/{staff}', 'StaffController@update')
        ->name('staff.update')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::delete('/staff/{staff}', 'StaffController@destroy')
        ->name('staff.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::get('/staff/delete/{Product}/', 'StaffController@delete')
        ->name('staff.delete.get')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::post('/staff/delete/', 'StaffController@delete')
        ->name('staff.delete')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::delete('/staff', 'StaffController@destroyMany')
        ->name('staff.manyDelete')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::get('/staff/sendLoginDetails/{staff}', 'StaffController@sendLoginDetails')
        ->name('staff.sendLoginDetails')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::get('/staff/changePassword/{staff}', 'StaffController@changePassword')
        ->name('staff.changePassword')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::get('/staff/activate/{staff}', 'StaffController@activate')
        ->name('staff.activate')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    Route::get('/staff/deactivate/{staff}', 'StaffController@deactivate')
        ->name('staff.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);
    /**
     * Inventory Settings - Default Store Id
     * */
    Route::group(['prefix' => 'inventory/settings', 'as' => 'inventory_settings', 'middleware' => ['checkPermissions:' . PermissionUtil::Edit_General_Settings]], function ($router) {
        Route::group(['prefix' => '/default_warehouses', 'as' => '.default_warehouses'], function ($router) {
            Route::get('/create', 'StaffInventoryController@create')->name('.create');
            Route::post('/', 'StaffInventoryController@store')->name('.store');
            Route::get('/{id}', 'StaffInventoryController@show')->name('.show');

            Route::get('/{id}/edit', 'StaffInventoryController@edit')->name('.edit');
            Route::put('/{id}', 'StaffInventoryController@update')->name('.update');
            Route::get('', 'StaffInventoryController@index')->name('.index');
            Route::delete('/{id}', 'StaffInventoryController@destroy')->name('.destroy');
        });
        Route::get('/', 'StaffInventoryController@settings')->name('settings');
    });
    Route::group(['prefix' => 'site/notifications', 'as' => 'notifications'], function ($router) {
        Route::get('/', 'NotificationController@index')->name('.index');
        Route::get('/{id}/read', 'NotificationController@markAsRead')->name('.markRead');
        Route::get('/{id}/dismiss', 'NotificationController@dismiss')->name('.dismiss');
        Route::get('/dismiss_everything', 'NotificationController@dismissEverything')->name('.dismiss-everything');
    });

    Route::group(['prefix' => 'site/updates'], function ($router) {
        Route::get('/', 'SystemUpdateController@index');
    });

    /**** Employment Levels ****/
    Route::Resource('/employment_levels', 'EmploymentLevelController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);
    Route::get('/employment_levels/{employment_level}/activate', 'EmploymentLevelController@activate')
        ->name('employment_levels.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);
    Route::get('/employment_levels/{employment_level}/deactivate', 'EmploymentLevelController@deactivate')
        ->name('employment_levels.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);

    /**** Employment Types ****/
    Route::Resource('/employment_types', 'EmploymentTypeController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);
    Route::get('/employment_types/{employment_type}/activate', 'EmploymentTypeController@activate')
        ->name('employment_types.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);
    Route::get('/employment_types/{employment_type}/deactivate', 'EmploymentTypeController@deactivate')
        ->name('employment_types.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);

    /**** Designations ****/
    Route::Resource('/designations', 'DesignationController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);
    Route::get('/designations/{designation}/activate', 'DesignationController@activate')
        ->name('designations.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);
    Route::get('/designations/{designation}/deactivate', 'DesignationController@deactivate')
        ->name('designations.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);

    /**** Departments ****/
    Route::get('/departments/search', 'DepartmentController@ajaxNameFilter')->name('departments.search');
    Route::get('/departments/unique', 'DepartmentController@unique')
        ->name('departments.unique');
    Route::get('/departments/{department}/activate', 'DepartmentController@activate')
        ->name('departments.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);
    Route::get('/departments/{department}/deactivate', 'DepartmentController@deactivate')
        ->name('departments.deactivate');
    Route::resource('/departments', 'DepartmentController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM);

    /**** Import ****/
    Route::get('/import/{entity_key}', 'ImportController@importForm')->name('import');
    Route::post('/import/{entity_key}/step2', 'ImportController@importMap')->name('importMap');
    Route::post('/import/{entity_key}/step3', 'ImportController@import')->name('import.submit');
    Route::get('/import/{entity_key}/step2', function ($entityKey) {
        $params = ['entity_key' => $entityKey];
        request()->has('entity_id') ? $params['entity_id'] = request()->entity_id : null;
        return redirect()->route('owner.import', $params)->with('danger', __t('You need to upload file first'));
    });
    Route::get('/import/{entity_key}/step3', function ($entityKey) {
        $params = ['entity_key' => $entityKey];
        request()->has('entity_id') ? $params['entity_id'] = request()->entity_id : null;
        return redirect()->route('owner.import', $params)->with('danger', __t('You need to upload file first'));
    });


    /**** Activity Log ****/
    Route::get('/activity_logs', 'ActivityLogController@index')->name('activity_logs.index');
    Route::get('/activity_logs/entity', 'ActivityLogController@getEntityActivityLogView')->name('activity_logs.entity');
    Route::get('/activity_logs/entity/iframe', 'ActivityLogController@getEntityActivityLogIframe')->name('activity_logs.entity.iframe');

    /**** Shifts ****/
    Route::get('/shifts/unique', 'ShiftController@unique')
        ->name('shifts.unique');

    Route::get('shifts/search', 'ShiftController@ajaxNameFilter')->name('shifts.search');

    Route::resource('/shifts', 'ShiftController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS . ',' . PermissionUtil::Edit_General_Settings);

    Route::get('shifts/{shift}/clone', 'ShiftController@cloneView')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS . ',' . PermissionUtil::Edit_General_Settings)
        ->name('shifts.cloneView');

    Route::post('shifts/{shift}/clone', 'ShiftController@clone')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS . ',' . PermissionUtil::Edit_General_Settings)
        ->name('shifts.clone');
    Route::get('/shifts/assign_employees/{shift_id}', 'ShiftController@assignEmployees')
        ->name('shifts.assign_employees')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::post('/shifts/assign_employees', 'ShiftController@saveAssignEmployees')
        ->name('shifts.save_assign_employees')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);
    Route::post('/shifts/destroyMulti', 'ShiftController@destroyMulti')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS)
        ->name('shifts.destroyMulti');
    /** TimeTracking */
    Route::get('/time_tracking/settings', function () {
        return view('employee_hour_rates.settings');
    })->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::resource('/time_tracking/employee_hour_rates', 'EmployeeTimeTrackingController')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/tester', 'SalaryComponentController@getCreateforModal');

    Route::get('/sales_settings', 'Sales\SettingsController')
        ->name('sales_settings')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/pos/settings', function () {
        return view('pos/settings');
    })->name('pos.settings')->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/work-order/settings', function () {
        return view('work_order/settings');
    })->name('work_order.settings')->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/workflow-types/{id}/activate', 'WorkFlowTypeController@activate')
        ->name('workflow_types.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_WORKFLOW_SETTINGS);

    Route::get('/workflow-types/{id}/deactivate', 'WorkFlowTypeController@deactivate')
        ->name('workflow_types.deactivate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_WORKFLOW_SETTINGS);

    Route::get('/products/settings', function () {
        return view('products.owner.settings');
    })->name('products.settings')->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/clients/settings', 'Client\ClientSettingsController')
        ->name('clients.settings')
        ->middleware('checkPermissions:' . PermissionUtil::EDIT_CLIENT_Settings);

    Route::get('/suppliers/settings', 'Supplier\SupplierSettingsController')
        ->name('suppliers.settings')
        ->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/purchase-invoices/settings', function () {
        return view('purchase_orders.settings');
    })->name('purchase_invoices.settings')->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/accounting/settings', function () {
        return view('accounting.settings');
    })->name('accounting.settings')->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings . ',' . PermissionUtil::VIEW_COST_CENTERS . ',' . PermissionUtil::VIEW_CLOSED_PERIODS);

    //Assets
    Route::get('/asset_deprecations/view/{id}', 'AssetDeprecationsController@show')->name('asset_deprecations.show');


    //Copy Excel routes
    Route::get('/form/excel/{entityKey}', 'ExcelForms\ExcelFormBuilderController@draw');
    Route::post('/form/excel/{entityKey}', 'ExcelForms\ExcelFormBuilderController@draw');

    Route::get('/purchase_requests/update-status/{id}/{status}', 'PurchaseRequestController@updateStatus')->name('purchase_requests.updateStatus');
    Route::get('/purchase_requests/change-status/{id}/{status}', 'PurchaseRequestController@updateFollowUpStatus')->name('purchase_requests.updateFollowUpStatus');
    Route::get('/purchase_quotations/update-status/{id}/{status}', 'PurchaseQuotationController@updateStatus')->name('purchase_quotations.updateStatus');
    Route::get('/purchase_quotations/change-status/{id}/{status}', 'PurchaseQuotationController@updateFollowUpStatus')->name('purchase_quotations.updateFollowUpStatus');
    Route::get('/quotation_requests/change-status/{id}/{status}', 'QuotationRequestController@updateFollowUpStatus')->name('quotation_requests.updateFollowUpStatus');
    Route::get('/purchase_orders/change-status/{id}/{status}', 'PurchaseOrderController@updateFollowUpStatus')->name('purchase_orders.updateFollowUpStatus');

    // stock requests
    Route::get('/stock_request/update-status/{id}/{action}', 'StockRequestController@updateStatus')->name('stock_request.updateStatus');

    //Purchase request reports
    Route::get('/purchase_request/{id}/report', 'PurchaseRequestReportsController@index')->name('purchase_requests.report');
    Route::get('/purchase_request/{id}/pdf', 'PurchaseRequestReportsController@exportPdf')->name('purchase_requests.export');
    Route::get('/purchase_request/{id}/excel', 'PurchaseRequestReportsController@exportCsv')->name('purchase_requests.exportCsv');

    //Client loyalty routes
    Route::post('/client_loyalty/settings', 'ClientLoyaltyController@saveSetting')->name('client_loyalty.saveSetting')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_LOYALTY_RULES);
    Route::get('/client_loyalty/settings', 'ClientLoyaltyController@settings')->name('client_loyalty.settings')->middleware('checkPermissions:' . PermissionUtil::MANAGE_LOYALTY_RULES);
    Route::get('/client_loyalty/change-rule-status', 'ClientLoyaltyController@changeStatus')->name('client_loyalty.changeStatus')->middleware('checkPermissions:' . PermissionUtil::MANAGE_LOYALTY_RULES);
    Route::get('/client_loyalty/currency-convertor', 'ClientLoyaltyController@convertCurrency')->name('client_loyalty.convertCurrency')->middleware('checkPermissions:' . implode(',', [PermissionUtil::REDEEM_LOYALTY_POINTS, PermissionUtil::MANAGE_LOYALTY_RULES]));
    Route::get('/client_loyalty/get-points', 'ClientLoyaltyController@getPoints')->name('client_loyalty.getPoints')->middleware('checkPermissions:' . implode(',', [PermissionUtil::REDEEM_LOYALTY_POINTS, PermissionUtil::MANAGE_LOYALTY_RULES]));


    Route::get('/lease-contracts/{id}/change-followup-status/{statusId}', 'LeaseContractController@updateFollowupStatus')
        ->name('change.lease.contract.followup.status')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_RESERVATION_ORDERS);

    // Route::delete('entity/lease_contract/{id}', 'LeaseContractController@destroy')
    // ->name('lease.contract.delete')
    // ->middleware('checkPermissions:' . PermissionUtil::DELETE_RESERVATION_ORDERS);
    /* Banking Reconciliation start */

    Route::group([
        'prefix' => 'banking',
        'as' => 'banking.'
    ], function () {
        Route::group([
            'prefix' => 'reconciliation',
            'as' => 'reconciliation.',
        ], function () {
            Route::get(
                'pull/{provider}',
                'Bank\BankingReconciliationController@pull'
            )->name('pull');

            Route::get(
                'pull-results/{hash}',
                'Bank\BankingReconciliationController@pullResults'
            )->name('pull-results');

            Route::get(
                'connect/{provider}',
                'Bank\BankingReconciliationController@connect'
            )->name('connect');

            Route::match(
                ['get', 'post'],
                'connect/{provider}/callback',
                'Bank\BankingReconciliationController@connectionCallback'
            )->name('callback')->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);

            Route::get(
                'connect/{provider}/disconnect',
                'Bank\BankingReconciliationController@disconnect'
            )->name('callback');
        });

        Route::get('autocomplete', 'Bank\BanksListingController@autocomplete');
    });

    /* Banking Reconciliation end */

    Route::group([
        'prefix' => 'settings',
        'as' => 'settings.'
    ], function () {
        /* Custom domains start */
        Route::group([
            'as' => 'custom-domains.',
            'prefix' => 'custom-domains'
        ], function () {
            Route::get('manage', [
                Controllers\Settings\CustomDomainsController::class,
                'manage'
            ])->name('manage');

            Route::post('update-single-domain', [
                Controllers\Settings\CustomDomainsController::class,
                'updateSingleDomain'
            ])->name('update-single-domain');

            Route::get(
                'list',
                [
                    Controllers\Settings\CustomDomainsController::class,
                    'index'
                ]
            )->name('list');

            Route::post(
                'store',
                [
                    Controllers\Settings\CustomDomainsController::class,
                    'store'
                ]
            )->name('store');

            Route::delete(
                'destroy/{id}',
                [
                    Controllers\Settings\CustomDomainsController::class,
                    'destroy'
                ]
            )->name('destroy');

            Route::get(
                '/{id}/update-status',
                [
                    Controllers\Settings\CustomDomainsController::class,
                    'updateStatus'
                ]
            )->name('update-status');
        });
        /* Custom domains end */
    });
});

Route::get('/search-industry', 'SystemIndustryController@search_industries')->name('search-industry');

Route::get('update-limitations', function () {
    if ($this->app->environment(['local', 'staging', 'webtesting'])) {
        // Update current site plan & package
        $plan_id = 11;
        $package_id = 213;

        Site::query()
            ->find(getCurrentSite('id'))
            ->update([
                'plan_id' => $plan_id,
                'package_id' => $package_id,
            ]);

        // Insert site limitations
        $current_site = getCurrentSite();
        Limitation::query()->pluck('name')->map(function ($name) use ($current_site) {
            SiteLimitation::query()->updateOrCreate(
                [
                    'site_id' => $current_site['id'],
                    'name' => $name,
                ],
                [
                    'max_value' => '1000000',
                    'created' => now(),
                    'modified' => now(),
                ]
            );
        });

        echo "Site limitations added!";
    }
});

Route::get('translation', function () {
    $curr_labels = [];
    $js_lang_labels = [
        'Quantity',
        'Unit Price',
        'Today',
        'Last 7 days',
        'Date Range',
        'Specific Date',
        'All Dates Before',
        'All Dates After',
        'Start date',
        'End date',
        'Next',
        'Prev',
        'Edit',
        'Delete',
        'Last week',
        'Last month',
        'Month to date',
        'Last year',
        'Year to date',
        'Reset',
        'Yes',
        'No',
        'Update',
        'Close',
        'Add',
        'Edit',
        'Remove',
        'Issue days must be less than subscirption period',
        'Value must be between [0-100]',
        'Only :value characters allowed',
        'Value must be less than or equal to :value',
        'Value must be less than :value',
        'Either label or value required',
        'Valid date required',
        'Valid number required',
        'Invalid number of days',
        'Are you sure you want to delete?',
        'Really delete this entry?',
        'Positive number required',
        'Characters left',
        'Required',
        'Please enter a valid email',
        'Please enter a valid number',
        'error',
        'type invoice no',
        'type client name',
        'Please select the required terms',
        'Please select project',
        'Please enter a positive time value',
        'Valid email required',
        'Upload',
        'Full Name / Business Name ',
        'Full Name',
        'Client Details',
        'Business Name',
        'Active',
        'Activated',
        'Deactivated',
        'Not Active',
        'All',
        'Select All',
        'Unselect All',
        'Please choose at least one field',
        // Validations
        'This value seems to be invalid.',
        'Please enter correct date using this format %s.',
        'This value should be a valid email.',
        'This value should be a valid url.',
        'This value should be a valid number.',
        'This value should be a valid integer.',
        'This value should be digits.',
        'This value should be alphanumeric.',
        'This value should not be blank.',
        'This is a required field and could not be empty.',
        'This value seems to be invalid.',
        'This value should be greater than or equal to %s.',
        'This value should be lower than or equal to ',
        'This value should be lower than or equal to %s.',
        'This value should be between %s and %s.',
        'This value is too short. It should have %s characters or more.',
        'This value is too long. It should have %s characters or fewer.',
        'This value length is invalid. It should be between %s and %s characters long.',
        'You must select at least %s choices.',
        'You must select %s choices or fewer.',
        'You must select between %s and %s choices.',
        'This value should be the same.',
        'It\'s not a valid VAT Identification Number.',
        'Please wait, it may take a few moments to process your request',
        'Connection Failed !',
        'This Field is Required',
        'View all variables',
        'No variables found',
        'Page',
        'of',
        'Go to',
        'You cannot exceed 150 cheque in one cheque book',
        'Select Credit Type',
        'You cannot select the item twice',
        'Related Plugins',
        'No Requests Added Yet',
        'Captured',
        'The system cannot detect your IP address Please check Your Settings',
        'The system cannot detect your Location Please check Your Settings',
        'You should open the camera in your device',
        'Drop file here or',
        'select from your computer',
        'Choose file',
        'Max file size %d %s',
        'Allowed file types: (%s)',
        'MB',
        'KB',
        'File Size',
        'You should open the camera in your device',
        'Please select items first by clicking on the small left box',
        'You cannot exceed 200',
        'Abort',
        'Year',
        'Month',
        'Week',
        'Edit/Delete Holiday',
        'Add Holiday',
        'Error...',
        'Holiday Lists Updated Successfully',
        "Your file is still uploading. Please keep this page open until it's done."
    ];
    foreach ($js_lang_labels as $key) {
        $curr_labels[($key)] = __t($key);
    }
    $contents = 'var translate_languages = ' . json_encode($curr_labels);
    request()->headers->set('X-Requested-With', 'XMLHttpRequest');
    return new Response(
        $contents,
        200,
        [
            'Content-Type' => 'text/javascript',
        ]
    );
});

Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function () {

    Route::get('bank_transactions', 'BankTransactionController@index')
        ->name('bank_transactions.index');

    Route::get('bank_transactions/{id}', 'BankTransactionController@show')
        ->name('bank_transactions.show');

    Route::get('bank_transactions/{id}/match', 'BankTransactionController@match')
        ->name('bank_transactions.match');

    Route::get('bank_transactions/{id}/un-match', 'BankTransactionController@unMatch')
        ->name('bank_transactions.un_match');

    Route::get('bank_transactions/{id}/exclude', 'BankTransactionController@exclude')
        ->name('bank_transactions.exclude');

    Route::get('bank_transactions/{id}/undo-exclusion ', 'BankTransactionController@undoExclusion ')
        ->name('bank_transactions.undo_exclusion');


    Route::delete('/bank_transactions/{bank_transaction}', 'BankTransactionController@destroy')
        ->name('bank_transactions.destroy');

    Route::get('/bank_transactions_delete_all', 'BankTransactionController@destroyAll');
    Route::get('/bank_transactions_unmatch_all', 'BankTransactionController@unmatchAll');

    Route::get('bank_transactions/{bank_id}/currency_code', 'BankTransactionController@getBankCurrency')
        ->name('bank_transactions.getBankCurrency');

    Route::get('memcache_benchmark/{count?}/{stringLength?}', 'MemcacheBenchmarkController');

    Route::get('/crm', 'CrmController@index')
        ->name('crm.index');

    Route::get('/products/ajaxBrandFilter', 'ProductController@ajaxBrandFilter')->name('products.ajaxBrandFilter');

    Route::get('/products/getProductsAveragePrices', 'ProductController@getProductsAveragePrices')->name('products.getProductsAveragePrices');

    Route::get('/categories/ajaxProductCategoryFilter', 'CategoryController@ajaxProductCategoryFilter')->name('categories.ajaxProductCategoryFilter');

    Route::get('/products/ajaxProductsFilter', 'ProductController@ajaxProductsFilter')->name('products.ajaxProductsFilter');

    Route::get('/products/ajax_products_filter_v2', 'ProductController@ajaxProductsFilterV2')->name('products.ajaxProductsFilterV2');

    Route::get('/products/generate_serials', 'ProductController@generateSerials')->name('products.generateSerials');

    Route::delete('/item_groups/destroy/{id}', 'ItemGroupController@destroy')->name('item_groups.destroy');

    Route::get('/jordanian-einvoice-settings', [Controllers\JordanianEInvoiceController::class, 'settings'])->name('jordanian.einvoice.settings.list');
    Route::get('/jordanian-einvoice-settings/general-settings', [Controllers\JordanianEInvoiceController::class, 'generalSettings'])->name('jordanian.einvoice.general.settings');
    Route::post('/jordanian-einvoice-settings/general-settings', [Controllers\JordanianEInvoiceController::class, 'updateGeneralSettings'])->name('jordanian_e_invoice.general_settings.store');
    Route::get('/jordanian-e-invoice/{id}/send', [Controllers\JordanianEInvoiceController::class, 'sendJordanianEInvoice'])->name('jordanian_e_invoice.send');

    Route::resource('order-sources', 'OrderSourcesController', ['names' => [
        'index'   => 'order_sources.index',
        'store'   => 'order_sources.store',
        'destroy' => 'order_sources.destroy',
    ]])->middleware('checkPermissions:' . PermissionUtil::Edit_General_Settings);

    Route::get('/document_types/manage/{entityKey}', [Controllers\EntityDocumentTypesController::class , 'view'])->name('manage.entity.documents');
    Route::put('/document_types/update/{entityKey}', [Controllers\EntityDocumentTypesController::class , 'update'])->name('update.entity.documents');
    Route::get('/document_types/export_documents/{withMetadata}/{staffId}', [Controllers\EntityDocumentTypesController::class, 'exportDocuments'])
    ->name('documents.export');
});
Route::get('/test-beta-consts', [\App\Http\Controllers\Test\TestController::class, 'get_beta_constants']);

