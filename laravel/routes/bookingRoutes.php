<?php
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Utils\PermissionUtil;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function () {
    Route::get('/new-booking', 'BookingController@index');
    Route::get('/booking_settings', 'Booking\BookingSettingsController@index')->name('booking_settings.index');
});

Route::group(['prefix' => 'api', 'middleware' => 'isApi'], function () {
    Route::get('/booking/init', 'BookingController@bookingInit')
        ->name('booking_settings.api');
    Route::get('/booking/services', 'BookingController@getServicesAndPackages')
        ->name('booking_services.api');
    Route::get('/bookings', 'BookingController@getBookings')
        ->name('bookings_list.api');
    Route::get('/bookings/client/{id}', 'BookingController@getBookings')
        ->name('client_bookings.api');
    Route::get('/bookings/{id}', 'BookingController@getBooking')
        ->name('bookings_show.api');
    Route::post('/bookings', 'BookingController@createBooking')
        ->name('booking_create.api');
    Route::post('booking/validate_booking_service', 'BookingController@validateBookingService')
        ->name('validate_booking_service.api');
    Route::get('bookings/calender/{date?}/{type?}/{staff_id?}', 'BookingController@bookingCalendarData')
        ->name('booking_calender.api');
    Route::put('bookings/{id}', 'BookingController@updateBooking')
        ->name('booking_update.api');
    Route::patch('bookings/{id}/status', 'BookingController@updateBookingStatus')
        ->name('booking_update_status.api');
    Route::delete('bookings/{id}', 'BookingController@deleteBooking')
        ->name('booking_delete.api');
    Route::post('/block_times', 'BlockTimeController@createBlockTime')
        ->name('block_times.api');
    Route::put('/block_times/{id}', 'BlockTimeController@updateBlockTime')
        ->name('block_times_update.api');
    Route::get('/block_times/{id}', 'BlockTimeController@getBlockTime')
        ->name('block_times_show.api');
    Route::delete('/block_times/{id}', 'BlockTimeController@deleteBlockTime')
        ->name('block_times_delete.api');
    Route::get('/bookings/{id}/pos_data', 'BookingController@getBookingPos')
        ->name('booking_pos_data.api');
    Route::get('/booking/settings', 'Booking\BookingSettingsController@getSettings')
        ->name('get_booking_settings.api');
    Route::put('/booking/settings', 'Booking\BookingSettingsController@updateSettings')
        ->name('booking_settings_store.api');
    Route::get('/booking_packages', 'Booking\BookingPackageController@apiList')
        ->name('booking_packages_list.api');
    Route::get('/booking/shift_roaster', 'BookingController@getShiftRoaster')
        ->name('booking_shift_roaster.api');
    Route::put('/default_booking_shift', 'BookingController@setBookingDefaultShift')
        ->name('booking_default_shift.api');
    Route::get('/default_booking_shift', 'BookingController@getBookingDefaultShift')
        ->name('booking_default_shift.api');
    Route::post('/day_shift', 'UpdatedShiftDayController@updateShiftDay')
        ->name('updated_shift_day.api');
    Route::post('/off_days', 'OffDayController@createOffDay')
        ->name('off_days.api');
    Route::put('/off_days/{id}', 'OffDayController@updateOffDay')
        ->name('off_days_update.api');
    Route::delete('/off_days/{id}', 'OffDayController@deleteOffDay')
        ->name('off_days_delete.api');
    Route::get('/off_days/{id}', 'OffDayController@getOffDay')
        ->name('off_days_show.api');
    Route::post('/remove_from_roaster', 'BookingController@apiRemoveStaffBookingShift')
        ->name('staffs_booking_shift_delete.api');
    Route::post('/assign_booking_shift', 'BookingController@apiAssignStaffBookingShift')
        ->name('staffs_booking_shift_assign.api');

});
