<?php
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Controllers\MudadController;
use App\Utils\PermissionUtil;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function () {

    Route::prefix('payroll')->group(function () {
        Route::prefix('settings')->middleware('checkPermissions:'.PermissionUtil::MANAGE_PAYROLL_SETTINGS)->controller('PayrollSettingsController')->group(function () {
            Route::get('/', 'index')->name('payroll.settings');
            Route::post('/', 'store')->name('payroll.settings.store');
        });
    });

    /** Salary Components**/
    Route::get('payroll_settings', 'ContractController@settings')->name('contract.settings') ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);
    Route::get('payroll_settings/notifications', 'SettingController@payroll_notifications_settings')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
    ->name('payroll_settings.notifications');
    Route::post('payroll_settings/notifications', 'SettingController@store_payroll_notifications_settings')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('payroll_settings.notifications.store');
    Route::resource('salary_components', 'SalaryComponentController')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);
    Route::get('salary_components/{salary_component}/activate', 'SalaryComponentController@activate')
        ->name('salary_components.activate')->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);
    Route::get('salary_components/{salary_component}/deactivate', 'SalaryComponentController@deactivate')
        ->name('salary_components.deactivate')->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);

    Route::post('/salary_components/destroyMulti', 'SalaryComponentController@destroyMulti')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('salary_components.destroyMulti');

    /**** Salary Structure Resource ****/
    Route::get('salary_structures/{salary_structure}/components', 'SalaryStructureController@components')->name('salary_structures.components')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);
    Route::resource('salary_structures', 'SalaryStructureController')->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);
    Route::get('salary_structures/{salary_structure}/activate', 'SalaryStructureController@activate')->name('salary_structures.activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);
    Route::get('salary_structures/{salary_structure}/deactivate', 'SalaryStructureController@deactivate')
        ->name('salary_structures.deactivate')->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);

    /**** Contracts Resource ****/
//    Route::resource('contracts', 'ContractController')->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS);

    Route::get('contracts', 'ContractController@index')
        // ->middleware('checkPermissions:' . PermissionUtil::VIEW_PAYROLL_CONTRACT)
        ->name('contracts.index');

    Route::get('contracts/create', 'ContractController@create')
        ->middleware(['checkPermissions:' . PermissionUtil::ADD_PAYROLL_CONTRACT, "checkFreeTrail"])
        ->name('contracts.create');

    Route::get('contracts/{id}/edit', 'ContractController@edit')
        ->middleware([ "checkFreeTrail"])
        ->name('contracts.edit');

    Route::get('contracts/{id}', 'ContractController@show')
        // ->middleware('checkPermissions:' . PermissionUtil::VIEW_PAYROLL_CONTRACT)
        ->name('contracts.show');

    Route::post('contracts/', 'ContractController@store')
        ->middleware(['checkPermissions:' . PermissionUtil::ADD_PAYROLL_CONTRACT, "checkFreeTrail"])
        ->name('contracts.store');

    Route::post('assign_salary_components_to_contracts', 'ContractController@assignSalaryComponents')
        ->middleware(['checkPermissions:' . implode(',', [PermissionUtil::EDIT_DELETE_PAYROLL_CONTRACTS, PermissionUtil::EDIT_DELETE_HIS_OWN_PAYROLL_CONTRACTS])])
        ->name('contracts.assign_salary_components');

    Route::post('assign_salary_component_to_contract', 'ContractController@assignSalaryComponent')
        ->name('contracts.assign_one_contract_salary_component');


    Route::post('remove_salary_component_from_contracts', 'ContractController@removeSalaryComponents')
        ->middleware(['checkPermissions:' . implode(',', [PermissionUtil::EDIT_DELETE_PAYROLL_CONTRACTS, PermissionUtil::EDIT_DELETE_HIS_OWN_PAYROLL_CONTRACTS])])
        ->name('contracts.remove_salary_components');

    Route::post('remove_salary_component_from_contract', 'ContractController@removeSalaryComponent')
        ->name('contracts.remove_salary_component_from_one_contract');


    Route::put('contracts/{id}', 'ContractController@update')
        ->middleware(["checkFreeTrail"])
        ->name('contracts.update');

    Route::delete('contracts/{id}', 'ContractController@destroy')
        // ->middleware('checkPermissions:' . PermissionUtil::EDIT_DELETE_PAYROLL_CONTRACTS)
        ->name('contracts.destroy');


    Route::get('contracts/{id}/draft', 'ContractController@setAsDraft')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.draft');

    Route::get('contracts/{id}/cancel', 'ContractController@cancelView')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.cancel.show');

    Route::post('contracts/{id}/cancel', 'ContractController@setAsCancelled')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.cancel');

    Route::get('contracts/{id}/terminate', 'ContractController@terminateView')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.terminate.show');

    Route::post('contracts/{id}/terminate', 'ContractController@setAsTerminated')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.terminate');

    Route::get('contracts/{id}/activate', 'ContractController@activate')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.activate');

    Route::get('contracts/{id}/supersede', 'ContractController@supersedeView')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.supersede.show');

    Route::post('contracts/{id}/supersede', 'ContractController@supersede')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS)
        ->name('contracts.supersede');

    Route::get('contracts/{id}/renew', 'ContractController@renewView')
        ->middleware('checkPermissions:' . PermissionUtil::EDIT_DELETE_PAYROLL_CONTRACTS)
        ->name('contracts.renew.show');

    Route::post('contracts/{id}/renew', 'ContractController@renew')
        ->middleware('checkPermissions:' . PermissionUtil::EDIT_DELETE_PAYROLL_CONTRACTS)
        ->name('contracts.renew');

    /** Pay Slip Routes */
    Route::post('/payslips/destroyMulti', 'PaySlipController@destroyMulti')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_PAYSLIPS)
        ->name('payslips.destroyMulti');


    /** Pay run payments Routes */
    Route::get('/payruns/payments/calculate_total_net', 'PayRunPaymentController@calculateTotalNet')
        ->middleware('checkPermissions:' . PermissionUtil::PAY_PAYSLIPS)
        ->name('payruns.payments.calculate-total-net');
    Route::get('/payruns/{payrun}/payments', 'PayRunPaymentController@payments')
        ->middleware('checkPermissions:' . PermissionUtil::VIEW_PAY_RUN)
        ->name('payrun_payments.index');
    Route::get('/payruns/{payrun}/payments/create', 'PayRunPaymentController@add')
        ->middleware('checkPermissions:' . PermissionUtil::PAY_PAYSLIPS)
        ->name('payruns.payments.create');
    Route::post('/payruns/payments', 'PayRunPaymentController@store')
        ->middleware('checkPermissions:' . PermissionUtil::PAY_PAYSLIPS)
        ->name('payruns.payments.store');
    Route::get('/payruns/{payrun}/payments/{payrun_payment_id}', 'PayRunPaymentController@profile')
        ->middleware('checkPermissions:' . PermissionUtil::VIEW_PAY_RUN)
        ->name('payrun_payments.show');

    Route::delete('/payruns/{payrun}/payments/{payrun_payment_id}', 'PayRunPaymentController@remove')
        ->middleware('checkPermissions:' . PermissionUtil::DELETE_PAY_RUN_PAYMENTS)
        ->name('payruns.payments.destroy');
    /** Pay run Routes */
    Route::get('/payruns/fix_wtihout_journals', 'PayRunController@fixPayRunsWithoutPayment');
    Route::get('/payruns/fix_payruns', 'PayRunController@fixPayRunsJournals');
    Route::Resource('/payruns', 'PayRunController');
    Route::get('/payrun/search', 'PayRunController@ajaxNameFilter')->name('payruns.search');

    Route::get('/payruns/{id}/progress_status', 'PayRunController@progressStatus')
        ->name('payrun.progress_status');

    /* payrun payments route*/
    Route::get('payruns/{payrun}/payslips/approve-all','PaySlipController@approveAll')->middleware('checkPermissions:' . PermissionUtil::APPROVE_PAYSLIPS)->name('payslips.approve-all');
    Route::get('payslips/approve','PaySlipController@approve')->middleware('checkPermissions:' . PermissionUtil::APPROVE_PAYSLIPS)->name('payslips.payslip.approve');
    Route::get('payslips/un-approve','PaySlipController@unApprove')->middleware('checkPermissions:' . PermissionUtil::APPROVE_PAYSLIPS)->name('payslips.payslip.un-approve');
    Route::Resource('/payslips', 'PaySlipController');

    /** Create Manual Payslip From Payrun **/
    Route::get('/payruns/{payrun_id}/payslips/create', 'PaySlipController@create')
        ->middleware('checkPermissions:' . PermissionUtil::CREATE_PAY_RUN)
        ->name('payruns.payslips.create');

    Route::get('/payslips/{payslip_id}/clone', 'PaySlipController@clone')
        ->middleware('checkPermissions:' . PermissionUtil::CREATE_PAY_RUN)
        ->name('payslips.clone');

    Route::post('/payslips/print-multiple', "Payroll\PrintMultiplePayslipsController")
        ->name('print.multiple');


    Route::get('/mudad/settings' , [MudadController::class , 'settings'] )->name('mudad.settings');
    Route::post('/mudad/save-settings' , [MudadController::class , 'updateSettings'] )->name('mudad.store.settings');
    Route::get('/mudad/extract-payrun/{id}' , [MudadController::class , 'getExtractPayrunForm'] )->name('mudad.extract.payrun.form');
    Route::get('/mudad/generate-payrun/{id}' , [MudadController::class , 'getGeneratePayrunForm'] )->name('mudad.generate.payrun.form');

});

Route::group(['prefix' => 'api', 'as' => 'api.', 'middleware' => [ 'isApi', 'isActiveSite']], function () {

    Route::get('/mudad/get-data/{payRunId}' , [MudadController::class , 'getMudadData'] )->name('mudad.get.data');
    Route::post('/mudad/save-file/{payRunId}' , [MudadController::class , 'saveMudadFile'] )->name('mudad.save.file');
    Route::post('/mudad/clear-file/{payRunId}' , [MudadController::class , 'clearMudadFile'] )->name('mudad.clear.file');
    Route::get('/mudad/get-file-components/{payrunId}' , [MudadController::class , 'getMudadFileComponents'] )->name('mudad.get.file.components');
    Route::post('/mudad/update-file-components/{payrunId}' , [MudadController::class , 'updateMudadFileComponents'] )->name('mudad.update.file.headers');
    Route::get('/mudad/get-stats/{payRunId}' , [MudadController::class , 'getMudadStats'] )->name('mudad.get.stats');
    Route::post('/mudad/update-staff-official-id/{payRunId}' , [MudadController::class , 'mapStaffOfficailId'] )->name('mudad.update.staff.official.id');
    Route::post('/mudad/save-third-step/{payRunId}' , [MudadController::class , 'saveThirdStep'] )->name('mudad.save.third.step');
    Route::get('/mudad/export-mudad-payrun/{payRunId}' , [MudadController::class , 'exportPayrunMudadFile'] )->name('mudad.export.payrun.file');;

});
