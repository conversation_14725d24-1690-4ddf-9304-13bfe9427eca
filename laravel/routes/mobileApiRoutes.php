<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AttendanceController;
use App\Http\Controllers\LeaveApplicationController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\API\MobileAppRatingsController;
use App\Utils\PermissionUtil;

Route::group(['middleware' => ['SetRequestLanguage', 'isActiveSiteJsonResponse', 'auth:api']], function () {
    Route::get('api_app_versions', 'ApiController');
    Route::get('/me', 'API\MeController')->middleware('auth:api');
    Route::get('/accessible_branches', 'API\BranchController@getAccessibleBranches')->middleware('auth:api');
    Route::post('/logout', 'API\LogoutController')->middleware('auth:api');
    Route::post('/pushNotification', 'API\LeaveAppNotificationController')->middleware('auth:api');

    //stocktaking route
    Route::put('stocktaking_record/{id}', 'StocktakingRecordController@update')->middleware('isApi');
    Route::get('/fetch_products_balance', 'API\StocktakingController@fetchProductsBalance')->name('fetchProductsBalance');
    Route::post('/sync_stocktaking_records/{stocktaking_id?}', 'API\StocktakingController@syncStocktakingRecords');

    //end stocktaking route
    Route::post('/mobile-app/submit-rating', [
        MobileAppRatingsController::class,
        'store'
    ])->name('api.mobile_app.rating');
    Route::get('log_request', 'API\RequestLoggerController')->name('log.request');
});

Route::group(['middleware' => ['isActiveSite', 'isApi']], function () {
    Route::post('/session/mobile-app/submit-rating', [
        MobileAppRatingsController::class,
        'store'
    ])->name('api.mobile_app.session.rating');
});
Route::group(['middleware' => ['isActiveSiteJsonResponse', 'double-oauth:api']], function () {
    Route::get('/query/{module}/{action}', 'API\QueryController');
});

Route::group(['middleware' => ['SetRequestLanguage', 'isActiveSiteJsonResponse', 'auth:api']], function () {
    /** notification settings routes */
    Route::get('/own_notification_preferences', 'API\LeaveAppNotificationController@listNotificationPreferences');
    Route::post('/own_notification_preferences', 'API\LeaveAppNotificationController@updateNotificationPreferences');
    /** end notification settings routes */

    /**attendance routes */
    Route::get('/attendance', [AttendanceController::class,'getUserWithAttendanceRestrictions']);
    Route::post('/attendance/take', [AttendanceController::class,'take']);
    /** end attendance routes */

      /**leave application routes */
      Route::get('/leave_application/getFullData', [LeaveApplicationController::class,'apiGetFullData']);
      Route::get('/leave_application/get_status_filter_options', [LeaveApplicationController::class,'apiGetStatusFilterOptions']);
      Route::get('/leave_application/getLeaveTypes', [LeaveApplicationController::class,'apiGetLeaveTypes']);
      Route::post('/leave_application/updateStatus', [LeaveApplicationController::class,'apiUpdateStatus']);
      Route::post('/leave_application/undoRejection', [LeaveApplicationController::class,'apiUndoRejection']);
      Route::get('/leave_application/getEmployeeLeaveBalance', [LeaveApplicationController::class,'apiGetEmployeeLeaveBalance']);
      Route::get('/staff/searchEmployees', [StaffController::class,'ajaxNameFilter']);
       /** end leave application routes */
    /** attendance days routes */
    Route::get('/my_attendance_days', 'API\AttendanceDaysController@ownIndex');
    /** end attendance days routes */
    /** attendance sheets routes */
    Route::get('/my_attendance_sheets', 'API\AttendanceSheetsController@ownIndex');
    Route::get('/my_attendance_sheets/{id}', 'API\AttendanceSheetsController@ownShow');
    /** end attendance sheets routes */

    Route::post('/language/change', 'API\LeaveAppNotificationController@changeLanguage');
    /** payslips routes */
    Route::get('/own_payslips', 'API\PayslipsController@ownIndex');
    Route::get('/own_payslips/{id}', 'API\PayslipsController@ownShow');
    /** end payslips routes */

    /** holiday routes */
    Route::get('/holidays', 'HolidayListController@authHolidaysApi');
    /** end holiday routes */
});

Route::group(['middleware' => ['SetRequestLanguage', 'isActiveSiteJsonResponse']], function () {
    Route::post('/forget-password', 'API\ForgetPasswordController');
    Route::get('/generate-access-token/{id}', 'API\GenerateAccessToken')/*->middleware('auth:api')*/;
    Route::get('/public-key', 'API\PublicKey')/*->middleware('auth:api')*/;
    Route::post('/qr-login', 'API\QRLoginController');
});
