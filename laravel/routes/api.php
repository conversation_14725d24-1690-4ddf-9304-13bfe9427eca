<?php

use App\Http\Controllers\API\SettingController;
use App\Http\Controllers\API\StoreController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\EmployeeSearchController;
use App\Http\Controllers\LeaveApplicationController;
use App\Http\Controllers\OrderSourcesController;
use App\Http\Controllers\SQLUpdatesController;
use App\Http\Controllers\API\HrmWizardController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\StaffLeaveTypeCreditController;
use App\Utils\PermissionUtil;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductController;

Route::group(['middleware' => 'double-oauth:api'], function () {
    Route::get('/apps-manager/{appId}/{entityKey}/{entityId}/{lock}', 'AppManager\\LockEntityRecordController');
    Route::get('/{table}/get-deleted', 'API\\DeletedRecordsController');
    Route::get('/staff/permissions', 'API\\StaffPermissionsController');
});

Route::get('/site/info', 'API\\MeController@siteInfo');

Route::group(['middleware' => 'tamaraAuth'], function() {
    Route::post('/tamaraWebhook/{id}', 'TamraWebhooksController@index');
});

Route::group(['middleware' => 'tamaraAuth'], function() {
    Route::post('/tamaraWebhook', 'TamraWebhooksController@index');
});

Route::group(['middleware' => 'tabbyAuth'], function() {
    Route::post('/tabbyWebhook', 'TabbyWebhooksController@index');
});

Route::group(['middleware' => 'isApi'], function () {
    Route::get('/app-manager/activate/{id}/{status}', 'AppManager\\AppManagerController@activate');
    Route::get('currencies/list', 'CurrencyController@getList')->name('api.currencies.get_list');
    Route::get('/convert_currency', 'CurrencyController@convertCurrency')->name('api.currencies.convert_currency');

    $entities = [
        [
            'routeName' => 'departments',
            'paramName' => 'department',
            'controllerName' => 'DepartmentController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM,
        ],
        [
            'routeName' => 'shifts',
            'paramName' => 'shift',
            'controllerName' => 'ShiftController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS . ',' . PermissionUtil::Edit_General_Settings
        ],
        [
            'routeName' => 'leave_types',
            'paramName' => 'leave_type',
            'controllerName' => 'LeaveTypeController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
        ],
        [
            'routeName' => 'holiday_lists',
            'paramName' => 'holiday_list',
            'controllerName' => 'HolidayListController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
        ],
        [
            'routeName' => 'attendance_restrictions',
            'paramName' => 'attendance_restriction',
            'controllerName' => 'AttendanceRestrictionController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS,
        ],
        [
            'routeName' => 'attendance_permissions',
            'paramName' => 'attendance_permission',
            'controllerName' => 'AttendancePermissionController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_PERMISSION,
        ],
        [
            'routeName' => 'salary_components',
            'paramName' => 'salary_components',
            'controllerName' => 'SalaryComponentController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_PAYROLL_SETTINGS,
        ],
        [
            'routeName' => 'credit_charges',
            'paramName' => 'credit_charge',
            'controllerName' => 'CreditChargeController',
            'middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_CREDIT_CHARGES,
        ],
        [
            'routeName' => 'stocktaking_records',
            'paramName' => 'stocktaking_record',
            'controllerName' => 'StocktakingRecordController',
            'middleware' => [],
        ]
    ];

    Route::get('/products', [ProductController::class, 'apiIndex'])->name('products.index');
    foreach ($entities as $entity) {
        Route::group(['prefix' => $entity['routeName'], 'as' => 'api.'.$entity['routeName'].'.', 'middleware' => $entity['middleware']], function () use ($entity) {
            Route::post('/', $entity['controllerName'].'@store')->name('store');
            Route::get('/', $entity['controllerName'].'@index')->name('index');
            Route::put('/{'.$entity['paramName'].'}', $entity['controllerName'].'@update')->name('update');
            Route::delete('/{'.$entity['paramName'].'}', $entity['controllerName'].'@destroy')->name('destroy');
            Route::get('/{'.$entity['paramName'].'}', $entity['controllerName'].'@show')->name('show');
        });
    }

    /**
     * HRM wizard api requests
     */
    Route::group(['prefix' => 'departments', 'as' => 'api.departments.'],function() {
        Route::post('/update_or_create', 'DepartmentController@updateOrCreate')->name('updateOrCreate');
        Route::post("/delete/{id}","DepartmentController@destroy")->name("destroy");

    });


    Route::group(['prefix' => 'designations', 'as' => 'api.designations.','middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM],function() {
        Route::post('/update_or_create', 'DesignationController@updateOrCreate')->name('updateOrCreate');
        Route::post("/delete/{id}","DesignationController@destroy")->name("destroy");
        Route::get('/ai/get_designations_by_department/{name}', "DesignationController@getByDepartmentName")->name('getByDepartments');

    });



    Route::group(['prefix' => 'employment_levels', 'as' => 'api.employment_levels.','middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM],function() {
        Route::post('/update_or_create', 'EmploymentLevelController@updateOrCreate')->name('updateOrCreate');
        Route::post("/delete/{id}","EmploymentLevelController@destroy")->name("destroy");
    });

    Route::group(['prefix' => 'employment_types', 'as' => 'api.employment_types.','middleware' => 'checkPermissions:' . PermissionUtil::MANAGE_HRM_SYSTEM],function() {
        Route::post('/update_or_create', 'EmploymentTypeController@updateOrCreate')->name('updateOrCreate');
        Route::post("/delete/{id}","EmploymentTypeController@destroy")->name("destroy");
    });

    Route::group(['prefix' => 'holiday_lists', 'as' => 'api.holiday_lists.'],function() {
        Route::post("/delete/{id}","HolidayListController@destroy")->name("destroy");

    });

    Route::group(['prefix' => 'leave_types', 'as' => 'api.leave_types.'],function() {
        Route::post("/delete/{id}","LeaveTypeController@destroy")->name("destroy");

    });

    Route::group(['prefix' => 'attendance_restrictions', 'as' => 'api.attendance_restrictions.'],function() {
        Route::post("/delete/{id}","AttendanceRestrictionController@destroy")->name("destroy");

    });

    /**
     * HRM wizard api request end
     */

    /******************* Shop Front Default Route Theme ************************/
    Route::post('owner/shop_front/default_template/{template_id}', "ShopFrontTemplatesController@defaultTemplate")
        ->name('shop_front.default_template');

    Route::get('/attendance_sessions', 'AttendanceSessionController@index')
        ->name('attendance_sessions.index')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE);
    Route::get('/attendance_sessions/{attendance_session}', 'AttendanceSessionController@show')
        ->name('attendance_sessions.show')
        ->middleware('checkPermissions:' . PermissionUtil::TAKE_EMPLOYEES_ATTENDANCE);


    Route::get('/', 'AttendanceLogController@index')
        ->middleware('checkPermissions:'.implode(',',[PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG,PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG]))
        ->name('index');

    Route::post('/credit_charges/suspend', 'CreditChargeController@suspend')->name('api.credit_charges.suspend');
    Route::get('placeholders/{entity_key}/{mode}', 'PlaceholderController@list')->name('api.placeholders.list');
    Route::post('placeholders/label/{entity_key}/{mode}', 'PlaceholderController@label')->name('payslips.labelEquation');
    Route::get('placeholders/preview/{entity_key}/{id}', 'PlaceholderController@preview')->name('placeholders.preview');

    /** Credit Charge Generated from invoice payment */
    Route::post('invoice/{invoice}/suspend-charges', 'CreditChargeController@suspendCreditCharges')->name('api.credit_charges.suspend');
    Route::post('invoice/{invoice}/generate-charges', 'CreditChargeController@generateCreditCharges')->name('api.credit_charges.payment.store');
    /** pay installment from invoice payment */
    Route::post(
        'invoice/{invoice}/pay-agreement-installment',
        'InvoiceAgreements\AgreementInstallmentController@payInstallment'
    )->name('api.agreement.installment.store');


    Route::post('/commissions/calculate', '\App\Http\Controllers\Commissions\CommissionController@calculate')->name('api.commissions.calculate');
    Route::get('/commissions/calculate/{invoice_id}', '\App\Http\Controllers\Commissions\CommissionController@testCalculate')->name('api.commissions.testcalculate');

    /** Delete Agreement For Invoice */
    Route::group(["namespace" => "InvoiceAgreements"], function () {
        Route::post('/invoice_installment_agreements/deleteInvoiceAgreementForInvoice', "InvoiceInstallmentAgreementController@deleteInvoiceAgreementForInvoice")
            ->name('api.deleteInvoiceAgreementForInvoice');
    });

    /** PlaceHolder Evaluation Route*/
    Route::post('/payslips/evaluateEquation', 'PlaceholderController@evaluate')->name('payslips.evaluateEquation');
    Route::post('/payslips/evaluateEquationAdd', 'PlaceholderController@evaluateInAddPayslip')->name('payslips.evaluateEquationAdd');

    Route::post('/{entity_key}/evaluateEquation', 'PlaceholderController@evaluate')->name('api.entity.evaluateEquation');
    Route::post('/evaluateEquationByValues', 'PlaceholderController@evaluateByValues')->name('api.entity.evaluateEquationByValues');

    Route::group(['as'=>'api.'],function($router){

        Route::get('import/records/{entity}/{id}/{last?}', 'ImportController@importingRecordsApi');
        Route::get('/client_attendance_logs/client', 'ClientController@getClientDataForAttendance')->name('client_attendance_logs.clientData');
        Route::get('/client_attendance_logs/logEdit', 'ClientAttendanceLogController@getLogData')->name('client_attendance_logs.logEdit');
        Route::get('/clients/get_with_contact_info', 'ClientController@getClientsWithContactInfo');

        Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isActiveSite']], function ($router) {
            Route::group(['prefix' => 'notifications', 'as' => 'notifications.'],function($router) {
                    Route::post('/insertCakeNotification','NotificationController@insertCakeNotification');
                    Route::delete('/deleteCakeNotification','NotificationController@deleteCakeNotification');
                    }
                );
            }
        );
        Route::group(['prefix' => 'owner', 'as' => 'owner.', 'middleware' => ['isOwner', 'isActiveSite']], function ($router) {
            Route::group(['prefix' => 'notifications', 'as' => 'notifications.'],function($router) {
                Route::get('/', 'NotificationController@list')->name('list');
                Route::get('/count', 'NotificationController@count')->name('count');
                // route installation
                Route::put('/updateCakeNotification','NotificationController@updateCakeNotification');
                Route::get('/dismiss/{id}', 'NotificationController@dismiss')->name('dismiss');
                Route::get('/dismiss_all/{type}', 'NotificationController@dismissType')->name('dismissType');
            });
        });
        Route::group(['prefix' => 'client', 'as' => 'client.', 'middleware' => ['isClient', 'isActiveSite']], function ($router) {
            Route::group(['prefix' => 'notifications', 'as' => 'notifications.'],function($router) {
                Route::get('/', 'NotificationController@list')->name('list');
                Route::get('/count', 'NotificationController@count')->name('count');
                Route::get('/dismiss/{id}', 'NotificationController@dismiss')->name('dismiss');
                Route::get('/dismiss_all/{type}', 'NotificationController@dismissType')->name('dismissType');
            });
        });
    });

    /* delete sales commissions api*/
    Route::post("/commissions/delete","Commissions\CommissionController@deleteManyByApi")->name("api.commissions.destroy");

    /** Update Plugin Widgets Route **/
    Route::post("/widget/updatePluginWidgets","DashboardWidgetController@updatePluginWidgets")->name("api.widget.updatePluginWidgets");

    /** Credit Type Api Store Route **/
    Route::post('/credit_type', 'CreditTypeController@store')->name('api.credit_type.store');

    /** Package Api Store Route **/
    Route::post('/packages', 'PackageController@store')->name('api.packages.store');

    /** Dynamic Payslip Content Routes **/
    Route::get('/payslips/create/getLoansContent', 'PaySlipController@getLoansPartial')->name('api.payslips.getLoansContent');
    Route::get('/payslips/create/getCommissionsContent', 'PaySlipController@getCommissionsPartial')->name('api.payslips.getCommissionsContent');

    Route::get('/get-order-sources', [OrderSourcesController::class, 'getOrderSourcesDataAndSettings']);

    Route::post('/loans', 'LoanController@store')->name('api.loans.store');

    // Route for storing a new staff member, requiring 'Staff_Add_New_Staffs' permission
    Route::post('/staff', 'StaffController@store')
        ->name('staff.store')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Add_New_Staffs);
    // Route for updating an existing staff member, requiring 'Staff_Edit_Staffs' permission
    Route::put('/staff/{staff}', 'StaffController@update')
        ->name('staff.update')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);

    Route::DELETE('/staff/{staff}', 'StaffController@destroy')
        ->name('staff.destroy')
        ->middleware('checkPermissions:' . PermissionUtil::Staff_Edit_Staffs);


    Route::group(['prefix' => 'hrm-wizard', 'as' => 'hrm-wizard.'], function ($router) {
        Route::get('/initial', [HrmWizardController::class,'initial'])->name('initial');
        Route::get('/initial-attendance', [HrmWizardController::class,'initialAttendance'])->name('initialAttendance');
        Route::post('/update-state', [HrmWizardController::class,'updateState'])->name('update_state');

        Route::post('/attendance_settings/basic', 'SettingController@attendance_basic_settings')
        ->name('attendance_settings.basic')
        ->middleware('checkPermissions:' . PermissionUtil::MANAGE_ATTENDANCE_SETTINGS);

    });
    Route::get('/staff-check-un-calculated-logs/{staffId}', 'StaffController@apiGetCheckUnCalculatedLogs')->name('api.staff.get.unCalculatedLogs');
    Route::get('/get-staff-pending-leave-application/{staffId}', [StaffController::class, 'apiGetStaffPendingLeaveApplications']);
    Route::get('/get-staff-leave-balance/{staffId}', [StaffController::class, 'apiGetStaffLeaveBalance']);
    Route::get('/get-staff-shifts-policies/{staffId}', [StaffController::class, 'apiGetShiftAndPolicies']);
    Route::get('/get-staff-attendance-sheets/{staffId}', [StaffController::class, 'apiGetLatestAttendanceSheets']);
    Route::get('/get-staff-attendance-logs/{staffId}', [StaffController::class, 'apiGetLatestAttendanceLogs']);
    Route::get('/get-staff-monthly-attendance/{staffId}', [StaffController::class, 'apiGetStaffMonthlyAttendance']);
    Route::post('/update-staff-leave-application-status', [LeaveApplicationController::class,'apiUpdateStatus']);
    Route::get('/get-staff-latest-attendance-permissions/{staffId}', [StaffController::class,'apiGetLatestAttendancePermissions']);
    Route::get('/get-staff-latest-contracts/{staffId}', [StaffController::class,'apiGetLatestStaffContracts']);
    Route::get('/get-staff-contracts-details/{staffId}', [StaffController::class,'apiGetLatestStaffContractDetails']);
    Route::get('/get-staff-latest-loans/{staffId}', [StaffController::class,'apiGetLatestStaffLoans']);
    Route::get('/get-staff-latest-payslips/{staffId}', [StaffController::class,'apiGetLatestStaffPayslips']);
    Route::get('/get-payroll-currencies', [ContractController::class,'apiGetPayrollCurrencies']);
    Route::get('/get-payroll-checklist/{staffId}', [StaffController::class,'apiGetStaffPayrollCheckList']);
    Route::post('staff/change-direct-manager', [StaffController::class, 'apiUpdateDirectManager'])->middleware('checkPermissions:' . implode(',',[PermissionUtil::Staff_Edit_Staffs, PermissionUtil::MANAGE_HRM_SYSTEM]));
    Route::get('/staff-check-list/{staffId}', 'StaffController@apiGetStaffCheckList')->name('api.staff.get.checklist');
    Route::get('/staff_leave_type_credit/get_fiscal_year_filter_values/{staff_id}', [StaffLeaveTypeCreditController::class,'getFiscalYearFilterValues']);
    Route::get('/staff_leave_type_credit/get_staff_leave_credit_by_year/{staff_id}/{fiscal_year}', [StaffLeaveTypeCreditController::class,'getStaffLeaveCreditByYear']);
    Route::get('/staffs/get_staff_name_and_photo/{getFromAccessibleBranches?}', 'StaffController@getStaffsNameAndPhoto');
    Route::get('/products/ajaxProductsFilter', 'ProductController@ajaxProductsFilter')->name('api.products.ajaxProductsFilter');
    Route::get('/services', 'ProductController@apiListServices')->name('api.products.listServices');
    Route::post('/services', 'ProductController@apiCreateService')->name('api.products.createService');
    Route::put('/services/{id}', 'ProductController@apiUpdateService')->name('api.products.updateService');
    Route::delete('/services/{id}', [ProductController::class, 'apiDeleteService']);
    Route::get('/services/{id}', 'ProductController@apiGetService')->name('api.products.getService');
    Route::get('/employees/search', [EmployeeSearchController::class, 'search'])
        ->name('api.employees.search');
    Route::get('/categories/categories_with_products_count', 'CategoryController@apiGetCategoriesWithProductsCount')->name('api.categories.getCategoriesWithProductsCount');
});

Route::middleware('auth:api')->namespace('API')->group(function () {
    Route::get('/get-sql-updates', [SQLUpdatesController::class, 'getAll'])->withoutMiddleware('auth:api');
    Route::apiResource('branches', 'BranchController')->only('index');
    Route::get('stores/accessible', [StoreController::class, 'getAccessibleStores'])->middleware('isActiveSiteJsonResponse');
    Route::apiResource('stores', 'StoreController')->only('index');
    /** Default site Taxes Route **/
    Route::get('/default-taxes', [SettingController::class,'getDefaultTaxes']);

    Route::get('/get-order-source-settings', [OrderSourcesController::class, 'getOrderSourcesSettings']);

});
Route::apiResource('credit_usages', 'CreditUsageController')->middleware('checkPermissions:' . PermissionUtil::MANAGE_CREDIT_USAGE);

Route::get('/test/rollbar_log/api', 'Test\TestController@test_rollbar');

Route::get('token/me', 'API\\MeController@viewByToken')->middleware(['isValidToken', 'isActiveSite']);
