<?php
if(!defined('CAKE_BASE_URL'))
{
    define('CAKE_BASE_URL',env('CAKE_BASE_URL', sprintf(
        "%s://%s/",
        isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off' ? 'https' : 'http',
        isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : ''
    )));
    define('Cookie_Life', 3600000);
    define('CSV_SEPARATOR', ',');
    define('NL', "\n");
    define('DEFAULT_LANGUAGE', PHP_SAPI == 'cli' ? 'eng' : 41);
    define('DEFAULT_CURRENCY', 'AUD');
    define('DEFAULT_COUNTRY', 'AU');
    define('AJAX_CLIENT_COUNT', 15);
    define('UserModel', 'Site');
    define('UserTable', 'sites');
    define('OWNER_ROLE', 1);
    define('CLIENT_ROLE', 2);
    define('NBSP', mb_convert_encoding("\x20\x2F", 'UTF-8', 'UTF-16BE'));
    define('JOURNAL_TYPE_DEBITOR', 1); //da2en
    define('JOURNAL_TYPE_CREEDITOR', 0); //mdeen
    if (PHP_SAPI == 'cli') {
        define('SITE_HASH', '');
        define('DOCUMENT_SIZE', '');
        define('DOCUMENT_SIZE_MB', '0MB');
        define('LOGO_SIZE', 0);
        define('LOGO_SIZE_MB', '0MB');
        define('TERMS_SIZE', 0);
        define('TERMS_SIZE_MB', '0MB');
        define('ATTACHMENT_SIZE', 0);
        define('ATTACHMENT_SIZE_MB', '0MB');
    }

    define('JOURNAL_ACCOUNT_TYPE_ACCOUNT', '1');
    define('JOURNAL_ACCOUNT_TYPE_CAT', '0');
    define('STATS_CREATE_INSTANT', 10);
    define('STATS_CREATE_INSTANT_ERROR', 15);
    define('STATS_LOG_IN', 20);
    define('STATS_OPEN_INVOICE_CREATION_PAGE', 25);
    define('STATS_INVOICE_VALIDATION_ERROR', 26);
    define('STATS_CREATE_INVOICE', 30);
    define('STATS_SEND_INVOICE', 40);
    define('STATS_REMOVE_INVOICE', 50);
    define('STATS_CREATE_CLIENT', 60);
    define('STATS_CREATE_CLIENT_ERROR', 65);
    define('STATS_CLIENT_LOGIN', 70);
    define('STATS_CLIENT_PAYMENT', 80);
    define('STATS_REMOVE_CLIENT', 90);
    define('STATS_CREATE_QUOTE', 100);
    define('STATS_OPEN_QUOTE_CREATION_PAGE', 105);
    define('STATS_QUOTE_VALIDATION_ERROR', 106);
    define('STATS_CREATE_RECURRING_INVOICE', 110);
    define('STATS_OPEN_RECURRING_CREATION_PAGE', 115);
    define('STATS_RECURRING_VALIDATION_ERROR', 116);
    define('STATS_OPEN_PLANS_PAGE', 120);
    define('STATS_OPEN_PAYMENT_PAGE', 130);
    define('STATS_ERROR_INVOICE_LIMITATION_REACHED', 140);
    define('STATS_ERROR_ESTIMATE_LIMITATION_REACHED', 150);
    define('STATS_ERROR_RECURRING_LIMITATION_REACHED', 160);
    define('STATS_ERROR_CLIENT_LIMITATION_REACHED', 170);
    define('STATS_ERROR_TEMPLATE_LIMITATION_REACHED', 180);
    define('STATS_WHATSAPP_BUTTON_CLICK', 200);
    define('STATS_MOVED_TO_BETA_ACTION', 201);
    define('STATS_MOVED_TO_LIVE_ACTION', 202);
    define('RESET_ACCOUNT_DATA_SUCCESS', 259);

    define('ENQUIRY_TYPE_GENERAL_HELP', 5);
    define('ENQUIRY_TYPE_INVOICE_LAYOUT', 10);
    define('ENQUIRY_TYPE_PAYMENT_GATEWAY', 15);
    define('ENQUIRY_TYPE_SYSTEM_CUSTOMIZATION', 20);

    define('GENDER_NOT_SELECTED', 0);
    define('GENDER_MALE', 1);
    define('GENDER_FEMALE', 2);

    define('DEFAULT_LATITUDE', 28.7917334);
    define('DEFAULT_LANGITUDE', 33.2990182);


    define('MINOVERPAID', 0.03);
// A Hack to force browser to reload css when changed please increment this value when change any css file
    define('CSS_VERSION',133);

// A Hack to force browser to reload javascript when changed please increment this value when change any javascript file
    define('JAVASCRIPT_VERSION',1177);


    define('HAS_TRANSACTION_TYPE_BRANCHES',1);
    define('HAS_TRANSACTION_TYPE_STAFF',2);


    define('LISTING_FIELD_TYPE_DATE', 'listing_field_type_date');
    define('ACCOUNTS_BRANCH_KEY', 'accounts_branch');
    define('BRANCH_TRANSACTIONS_KEY', 'branch_transactions');
    include dirname(dirname(dirname(__FILE__))) . '/configure.php';
}

?>
