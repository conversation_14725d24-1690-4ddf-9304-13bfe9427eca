<?php

namespace Database\Factories;

use App\Models\EntityAttachment;
use App\Models\EntityDocument;
use App\Models\File;
use App\Models\Staff;
use Illuminate\Database\Eloquent\Factories\Factory;

class EntityAttachmentFactory extends Factory
{
    protected $model = EntityAttachment::class;

    public function definition()
    {
        return [
            'entity_id' => EntityDocument::factory(),
            'entity_key' => 'entity_document',
            'file_id' => File::factory(),
            'sort_index' => $this->faker->numberBetween(0, 10),
            'is_default' => 0,
            'deleted_at' => null,
            'entity_field_key' => 'entity_documents.documents',
        ];
    }

    public function forStaff($staffId)
    {
        return $this->state([
            'entity_id' => $staffId,
        ]);
    }

    public function forFile($fileId)
    {
        return $this->state([
            'file_id' => $fileId,
        ]);
    }

    public function defaultAttachment()
    {
        return $this->state([
            'is_default' => 1,
            'sort_index' => 0,
        ]);
    }

    public function document()
    {
        return $this->state([
            'entity_field_key' => 'entity_documents.documents',
            'sort_index' => $this->faker->numberBetween(0, 5),
        ]);
    }

    public function photo()
    {
        return $this->state([
            'entity_field_key' =>  'entity_documents.documents',
            'is_default' => $this->faker->boolean(20), // 20% chance of being default photo
        ]);
    }

    public function certificate()
    {
        return $this->state([
            'entity_field_key' =>  'entity_documents.documents',
            'sort_index' => $this->faker->numberBetween(0, 3),
        ]);
    }

    public function identification()
    {
        return $this->state([
            'entity_field_key' =>  'entity_documents.documents',
            'is_default' => $this->faker->boolean(30), // 30% chance of being default ID
        ]);
    }

    public function contract()
    {
        return $this->state([
            'entity_field_key' =>  'entity_documents.documents',
            'is_default' => 1, // Contracts are typically default documents
            'sort_index' => 0,
        ]);
    }

    public function reference()
    {
        return $this->state([
            'entity_field_key' =>  'entity_documents.documents',
            'sort_index' => $this->faker->numberBetween(0, 2),
        ]);
    }
}
