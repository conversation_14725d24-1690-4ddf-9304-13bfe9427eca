<?php

namespace Database\Factories;

use App\Models\StaffInfo;
use Illuminate\Database\Eloquent\Factories\Factory;

class StaffInfoFactory extends Factory
{
    protected $model = StaffInfo::class;

    public function definition()
    {
        return [
            'staff_id' => null, // Will be set by the seeder
            'department_id' => $this->faker->numberBetween(1, 10),
            'designation_id' => $this->faker->numberBetween(1, 25),
            'employment_type_id' => $this->faker->numberBetween(1, 8),
            'employment_level_id' => $this->faker->numberBetween(1, 10),
            'gender' => $this->faker->randomElement(['Male', 'Female']),
            'birth_date' => $this->faker->date('Y-m-d', '-22 years'),
            'personal_email' => $this->faker->unique()->safeEmail,
            'created' => now(),
            'modified' => now(),
        ];
    }
}