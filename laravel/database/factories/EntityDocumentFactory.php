<?php

namespace Database\Factories;

use App\Models\EntityDocument;
use App\Models\EntityDocumentType;
use App\Models\Staff;
use Illuminate\Database\Eloquent\Factories\Factory;

class EntityDocumentFactory extends Factory
{
    protected $model = EntityDocument::class;

    public function definition()
    {
        return [
            'entity_key' => 'staff',
            'entity_id' => Staff::factory(),
            'name' => $this->faker->words(3, true),
            'expiry_date' => $this->faker->optional(0.4)->dateTimeBetween('now', '+5 years')?->format('Y-m-d'), // 40% chance of having expiry
            'entity_document_type_id' => EntityDocumentType::inRandomOrder()->value('id') ?? EntityDocumentType::factory() ,
            'staff_id' => function (array $attributes) {
                return $attributes['entity_id']; // Same as entity_id for staff documents
            },
            'notes' => $this->faker->optional(0.3)->sentence(), // 30% chance of having notes
            'created' => now(),
            'modified' => now(),
        ];
    }

    public function forStaff($staffId)
    {
        return $this->state([
            'entity_id' => $staffId,
            'staff_id' => $staffId,
        ]);
    }

    public function forDocumentType($documentTypeId)
    {
        return $this->state([
            'entity_document_type_id' => $documentTypeId,
        ]);
    }

    public function withExpiry($expiryDate = null)
    {
        return $this->state([
            'expiry_date' => $expiryDate ?: $this->faker->dateTimeBetween('now', '+2 years')->format('Y-m-d'),
        ]);
    }

    public function expired()
    {
        return $this->state([
            'expiry_date' => $this->faker->dateTimeBetween('-1 year', '-1 day')->format('Y-m-d'),
        ]);
    }

    public function nationalId()
    {
        return $this->state([
            'name' => 'National ID Document',
            'expiry_date' => $this->faker->dateTimeBetween('+1 year', '+10 years')->format('Y-m-d'),
            'notes' => 'Government issued national identification document',
        ]);
    }

    public function passport()
    {
        return $this->state([
            'name' => 'Passport Document',
            'expiry_date' => $this->faker->dateTimeBetween('+6 months', '+10 years')->format('Y-m-d'),
            'notes' => 'International travel passport',
        ]);
    }

    public function contract()
    {
        return $this->state([
            'name' => 'Employment Contract',
            'expiry_date' => null, // Contracts typically don't expire
            'notes' => 'Official employment agreement and terms',
        ]);
    }
}
