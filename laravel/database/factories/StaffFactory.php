<?php

namespace Database\Factories;

use App\Models\Staff;
use Illuminate\Database\Eloquent\Factories\Factory;

class StaffFactory extends Factory
{
    protected $model = Staff::class;

    public function definition()
    {
        return [
            'added_by' => 1,
            'role_id' => $this->faker->numberBetween(1, 5),
            'code' => 'EMP' . $this->faker->unique()->numberBetween(1000, 9999),
            'type' => 'employee',
            'name' => $this->faker->firstName,
            'middle_name' => $this->faker->optional()->firstName,
            'last_name' => $this->faker->lastName,
            'full_name' => function (array $attributes) {
                return trim($attributes['name'] . ' ' . ($attributes['middle_name'] ?? '') . ' ' . $attributes['last_name']);
            },
            'can_access_system' => $this->faker->boolean(80),
            'home_phone' => $this->faker->phoneNumber,
            'business_Phone' => $this->faker->phoneNumber,
            'mobile' => $this->faker->phoneNumber,
            'email_address' => $this->faker->unique()->safeEmail,
            'active' => 1,
            'deleted' => 0,
            'branch_id' => $this->faker->numberBetween(1, 8),
            'citizenship_status' => $this->faker->randomElement(['citizen', 'resident']),
            'nationality' => $this->faker->randomElement(['EGY', 'SAU', 'UAE', 'KWT']),
            'country_code' => $this->faker->randomElement(['EGY', 'SAU', 'UAE', 'KWT']),
            'created' => now(),
            'modified' => now(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => 1,
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => 0,
            ];
        });
    }

    public function manager()
    {
        return $this->state(function (array $attributes) {
            return [
                'role_id' => 2, // Manager role
                'can_access_system' => 1,
            ];
        });
    }
}