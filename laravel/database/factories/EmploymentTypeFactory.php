<?php

namespace Database\Factories;

use App\Models\EmploymentType;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmploymentTypeFactory extends Factory
{
    protected $model = EmploymentType::class;

    public function definition()
    {
        return [
            'name' => $this->faker->randomElement([
                'Full-Time',
                'Part-Time', 
                'Contract',
                'Temporary',
                'Intern',
                'Freelance',
                'Consultant',
                'Seasonal'
            ]),
            'description' => $this->faker->sentence,
            'active' => 1,
            'created' => now(),
            'modified' => now(),
        ];
    }
}