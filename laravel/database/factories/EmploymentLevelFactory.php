<?php

namespace Database\Factories;

use App\Models\EmploymentLevel;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmploymentLevelFactory extends Factory
{
    protected $model = EmploymentLevel::class;

    public function definition()
    {
        return [
            'name' => $this->faker->randomElement([
                'Entry Level',
                'Junior Level',
                'Mid Level', 
                'Senior Level',
                'Lead Level',
                'Principal Level',
                'Director Level',
                'Vice President',
                'Executive Level',
                'C-Level'
            ]),
            'description' => $this->faker->sentence,
            'active' => 1,
            'created' => now(),
            'modified' => now(),
        ];
    }
}