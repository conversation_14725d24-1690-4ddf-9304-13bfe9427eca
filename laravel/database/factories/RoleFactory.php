<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;

class RoleFactory extends Factory
{
    protected $model = Role::class;

    public function definition()
    {
        return [
            'name' => $this->faker->jobTitle,
            'type' => 'user',
            'modified' => now(),
        ];
    }

    public function superAdmin()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_super_admin' => true,
                'name' => 'Super Administrator',
            ];
        });
    }
}
