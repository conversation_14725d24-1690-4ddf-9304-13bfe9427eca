<?php

namespace Database\Factories;

use App\Models\Designation;
use Illuminate\Database\Eloquent\Factories\Factory;

class DesignationFactory extends Factory
{
    protected $model = Designation::class;

    public function definition()
    {
        return [
            'name' => $this->faker->jobTitle,
            'description' => $this->faker->sentence,
            'role_id' => $this->faker->numberBetween(1, 5),
            'department_id' => $this->faker->numberBetween(1, 10),
            'active' => 1,
            'created' => now(),
            'modified' => now(),
        ];
    }
}