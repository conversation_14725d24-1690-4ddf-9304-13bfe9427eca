<?php

namespace Database\Factories;

use App\Models\File;
use Illuminate\Database\Eloquent\Factories\Factory;

class FileFactory extends Factory
{
    protected $model = File::class;

    public function definition()
    {
        $files = [
            ['path'=>'files/eed8e9a/staff/file-1.pdf' , 'mime_type'=>'pdf'],
            ['path'=>'files/eed8e9a/staff/file-2.xlsx','mime_type'=>'xlsx'],
            ['path'=>'files/eed8e9a/staff/file-3.png', 'mime_type'=>'png'],
            ['path'=>'files/eed8e9a/staff/file-4.png', 'mime_type'=>'png'],
        ];


        $selectedFile = $this->faker->randomElement($files);
        $type = $selectedFile['mime_type'];
        $path = $selectedFile['path'];

        return [
            'name' => $this->faker->words(3, true).'.' . $type,
            'is_temp' => false,
            'path' => $path,
            'file_size' => $this->faker->numberBetween(1024, 5242880), // 1KB to 5MB
            'last_access' => $this->faker->dateTimeThisYear,
            'storage_service_provider' => $this->faker->randomElement(['local', 's3', 'gcp_storage']),
            'mime_type' => $type,
            'entity_key' => 'staff',
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function document()
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => $this->faker->randomElement(['contract', 'id_copy', 'certificate', 'resume']) . '_' . $this->faker->uuid.'.' .$attributes['mime_type'],
                'mime_type' => $attributes['mime_type'],
                'path' => $attributes['path'],
                'file_size' => $this->faker->numberBetween(51200, 2097152), // 50KB to 2MB for documents
            ];
        });
    }

    public function image()
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'photo_' . $this->faker->uuid . $attributes['mime_type'],
                'path' => $attributes['path'],
                'mime_type' =>$attributes['mime_type'],
                'file_size' => $this->faker->numberBetween(102400, 1048576), // 100KB to 1MB for images
            ];
        });
    }
}
