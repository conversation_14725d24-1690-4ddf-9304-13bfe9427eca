<?php

namespace Database\Factories;

use App\Models\Department;
use Illuminate\Database\Eloquent\Factories\Factory;

class DepartmentFactory extends Factory
{
    protected $model = Department::class;

    public function definition()
    {
        return [
            'name' => $this->faker->randomElement([
                'Human Resources',
                'Information Technology',
                'Sales',
                'Marketing',
                'Finance',
                'Operations',
                'Customer Service',
                'Research and Development',
                'Legal',
                'Procurement'
            ]),
            'description' => $this->faker->sentence,
            'abbr' => strtoupper($this->faker->lexify('???')),
            'manager_id' => null,
            'active' => 1,
            'created' => now(),
            'modified' => now(),
        ];
    }
}