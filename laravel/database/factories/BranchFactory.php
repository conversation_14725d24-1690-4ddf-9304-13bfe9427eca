<?php

namespace Database\Factories;

use App\Models\Branch;
use Illuminate\Database\Eloquent\Factories\Factory;

class BranchFactory extends Factory
{
    protected $model = Branch::class;

    public function definition()
    {
        return [
            'code' => 'BR' . $this->faker->unique()->numberBetween(1000, 9999),
            'name' => $this->faker->company . ' Branch',
            'phone1' => $this->faker->phoneNumber,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'country_code' => $this->faker->randomElement(['EGY', 'SAU', 'UAE', 'KWT']),
            'status' => 1,
            'created' => now(),
            'modified' => now(),
        ];
    }
}
