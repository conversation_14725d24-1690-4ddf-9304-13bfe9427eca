<?php

namespace Database\Factories;

use App\Models\StaffJob;
use Illuminate\Database\Eloquent\Factories\Factory;

class StaffJobFactory extends Factory
{
    protected $model = StaffJob::class;

    public function definition()
    {
        return [
            'staff_id' => null, // Will be set by the seeder
            'join_date' => $this->faker->dateTimeBetween('-5 years', '-1 month'),
            'exit_date' => null,
            'exit_reason' => null,
            'created' => now(),
            'modified' => now(),
        ];
    }

    public function permanent()
    {
        return $this->state(function (array $attributes) {
            return [
                'join_date' => $this->faker->dateTimeBetween('-3 years', '-6 months'),
            ];
        });
    }

    public function exited()
    {
        return $this->state(function (array $attributes) {
            return [
                'exit_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
                'exit_reason' => $this->faker->randomElement([
                    'Resignation',
                    'Termination',
                    'Contract End',
                    'Better Opportunity',
                    'Personal Reasons'
                ]),
            ];
        });
    }
}
