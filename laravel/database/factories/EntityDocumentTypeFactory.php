<?php

namespace Database\Factories;

use App\Models\EntityDocumentType;
use Illuminate\Database\Eloquent\Factories\Factory;

class EntityDocumentTypeFactory extends Factory
{
    protected $model = EntityDocumentType::class;

    public function definition()
    {
        return [
            'entity_key' => 'staff',
            'name' => $this->faker->randomElement([
                'National ID',
                'Passport',
                'Employment Contract',
                'Resume/CV',
                'Educational Certificate',
                'Professional Certificate',
                'Medical Certificate',
                'Background Check',
                'Reference Letter',
                'Bank Account Details'
            ]),
            'is_required' => $this->faker->boolean(30), // 30% chance of being required
            'is_expirable' => $this->faker->boolean(40), // 40% chance of being expirable
            'display_order' => $this->faker->numberBetween(1, 100),
            'created' => now(),
            'modified' => now(),
        ];
    }

    public function required()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_required' => true,
                'name' => $this->faker->randomElement([
                    'National ID',
                    'Employment Contract',
                    'Resume/CV',
                ]),
            ];
        });
    }

    public function expirable()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_expirable' => true,
                'name' => $this->faker->randomElement([
                    'Passport',
                    'Work Permit',
                    'Medical Certificate',
                    'Professional License',
                ]),
            ];
        });
    }

    public function nationalId()
    {
        return $this->state([
            'name' => 'National ID',
            'is_required' => true,
            'is_expirable' => true,
            'display_order' => 1,
        ]);
    }

    public function passport()
    {
        return $this->state([
            'name' => 'Passport',
            'is_required' => false,
            'is_expirable' => true,
            'display_order' => 2,
        ]);
    }

    public function employmentContract()
    {
        return $this->state([
            'name' => 'Employment Contract',
            'is_required' => true,
            'is_expirable' => false,
            'display_order' => 3,
        ]);
    }
}
