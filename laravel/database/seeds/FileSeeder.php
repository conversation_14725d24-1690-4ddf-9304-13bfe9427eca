<?php

namespace Database\Seeders;

use App\Models\File;
use Database\Seeders\AbstractSiteSeeder;

class FileSeeder extends AbstractSiteSeeder
{
    protected $plugin = null;

    public function insertData()
    {
        $uploadedDocs = [
            ['path'=>'files/eed8e9a/staff/file-1.pdf' , 'mime_type'=>'pdf'],
            ['path'=>'files/eed8e9a/staff/file-2.xlsx','mime_type'=>'xlsx'],
        ];
        $uploadedImages = [
            ['path'=>'files/eed8e9a/staff/file-3.png', 'mime_type'=>'png'],
            ['path'=>'files/eed8e9a/staff/file-4.png', 'mime_type'=>'png'],
        ];
        File::factory()
            ->count(count($uploadedDocs))
            ->sequence(
                fn ($sequence) => ['path' => $uploadedDocs[$sequence->index]['path'], 'mime_type' => $uploadedDocs[$sequence->index]['mime_type']]
            )
            ->document()
            ->create();
        File::factory()
            ->count(count($uploadedImages))
            ->sequence(
                fn ($sequence) => ['path' => $uploadedImages[$sequence->index]['path'], 'mime_type' => $uploadedImages[$sequence->index]['mime_type']]
            )
            ->image()
            ->create();

    }
}
