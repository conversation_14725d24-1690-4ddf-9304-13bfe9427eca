<?php
namespace Database\Seeders;
use App\Providers\CurrentSiteDatabaseProvider;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    private $siteSeeders = [

        // HRM comprehensive seeder (includes all HRM dependencies)
        HRMSeeder::class,

        // Individual HRM seeders (if you want to run them separately)
        // BranchSeeder::class,
        // RoleSeeder::class,
        // EmploymentTypeSeeder::class,
        // EmploymentLevelSeeder::class,
        // DepartmentSeeder::class,
        // DesignationSeeder::class,
        // StaffFactorySeeder::class,

        // Legacy staff seeder (complex data)
//        StaffSeeder::class,

        // Business domain seeders
        LeaseContractsSeeder::class,
        \Database\Seeders\EntityAttachmentSeeder::class,

        // System configuration seeders
        ApprovalCycleConfigurationSeeder::class,
    ];

    public function run()
    {
        //  $this->call(CategoryTableSeeder::class);
        //site Seeders
        $sites = explode(",", env("DB_SEED_TEST_SITES_IDS"));
        foreach($sites as $site_id){
            $site = \App\Models\Site::find($site_id);
            if(!$site){
                continue;
            }
            $databaseConfig = json_decode($site->db_config, true);
            CurrentSiteDatabaseProvider::resetDefaultConnection($site);
            databaseReConnect($databaseConfig, $site->id);
            foreach ($this->siteSeeders as $siteSeeder){
                $this->call($siteSeeder);
            }
        }

    }
}
