<?php

namespace Database\Seeders;

use App\Models\EntityDocument;
use App\Models\EntityDocumentType;
use App\Models\Staff;
use Database\Seeders\AbstractSiteSeeder;

class EntityDocumentSeeder extends AbstractSiteSeeder
{
    protected $plugin = null;

    public function insertData()
    {
        // Get existing staff and document types
        $staffs = Staff::where('active', 1)->where('deleted', 0)->take(50)->get();
        $documentTypes = EntityDocumentType::where('entity_key', 'staff')->get();

        if ($staffs->isEmpty() || $documentTypes->isEmpty()) {
            // Create some basic data if none exists
            $staffs = Staff::factory()->count(10)->create([
                'active' => 1,
                'deleted' => 0,
                'type' => 'employee',
            ]);
        }

        // Ensure each staff has required documents
        $requiredDocTypes = $documentTypes->where('is_required', true);

        foreach ($staffs as $staff) {
            // Create required documents for each staff
            foreach ($requiredDocTypes as $docType) {
                EntityDocument::factory()
                    ->forStaff($staff->id)
                    ->forDocumentType($docType->id)
                    ->create([
                        'name' => $docType->name . ' - ' . $staff->name,
                        'expiry_date' => $docType->is_expirable 
                            ? fake()->dateTimeBetween('now', '+5 years')->format('Y-m-d')
                            : null,
                    ]);
            }

            // Create some optional documents for each staff (random selection)
            $optionalDocTypes = $documentTypes->where('is_required', false)->shuffle()->take(rand(2, 5));

            foreach ($optionalDocTypes as $docType) {
                EntityDocument::factory()
                    ->forStaff($staff->id)
                    ->forDocumentType($docType->id)
                    ->create([
                        'name' => $docType->name . ' - ' . $staff->name,
                        'expiry_date' => $docType->is_expirable 
                            ? fake()->dateTimeBetween('now', '+3 years')->format('Y-m-d')
                            : null,
                    ]);
            }
        }

        // Create some expired documents for testing
        EntityDocument::factory()
            ->count(10)
            ->expired()
            ->create();

        // Create some documents with specific states
        EntityDocument::factory()
            ->count(15)
            ->nationalId()
            ->create();

        EntityDocument::factory()
            ->count(10)
            ->passport()
            ->create();

        EntityDocument::factory()
            ->count(20)
            ->contract()
            ->create();
    }
}
