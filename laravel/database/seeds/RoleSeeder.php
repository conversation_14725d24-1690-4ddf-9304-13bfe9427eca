<?php

namespace Database\Seeders;

use App\Models\Role;
use Izam\Daftra\Common\Utils\PluginUtil;

class RoleSeeder extends AbstractSiteSeeder
{
    protected $plugin = PluginUtil::HRM_PLUGIN;

    public function insertData()
    {
        // Create one super admin
        Role::factory()->superAdmin()->create();
        
        // Create regular roles
        Role::factory()->count(12)->create();
    }
}