<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\PluginUtil;

class StaffSeeder extends AbstractSiteSeeder
{
    protected $plugin = PluginUtil::HRM_PLUGIN;

    public function insertData()
    {
        // Clear existing data
        DB::connection('currentSite')->statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::connection('currentSite')->table('staff_info')->truncate();
        DB::connection('currentSite')->table('staffs')->truncate();
        DB::connection('currentSite')->table('departments')->truncate();
        DB::connection('currentSite')->table('designations')->truncate();
        DB::connection('currentSite')->table('branches')->truncate();
        DB::connection('currentSite')->statement('SET FOREIGN_KEY_CHECKS=1;');

        // Insert branches first
        $this->seedBranches();

        // Insert departments
        $this->seedDepartments();

        // Insert designations
        $this->seedDesignations();

        // Insert staff and staff_info
        $this->seedStaff();

        echo "Successfully seeded 1000 staff members with related data!\n";
    }

    private function seedBranches()
    {
        $branches = [
            ['id' => 1, 'code' => 'HQ001', 'name' => 'Headquarters - Cairo', 'phone1' => '+20225551000', 'city' => 'Cairo', 'state' => 'Cairo', 'country_code' => 'EGY', 'status' => 1],
            ['id' => 2, 'code' => 'ALX001', 'name' => 'Alexandria Branch', 'phone1' => '+20335552000', 'city' => 'Alexandria', 'state' => 'Alexandria', 'country_code' => 'EGY', 'status' => 1],
            ['id' => 3, 'code' => 'GIZ001', 'name' => 'Giza Branch', 'phone1' => '+20235553000', 'city' => 'Giza', 'state' => 'Giza', 'country_code' => 'EGY', 'status' => 1],
            ['id' => 4, 'code' => 'RYD001', 'name' => 'Riyadh Branch', 'phone1' => '+966115554000', 'city' => 'Riyadh', 'state' => 'Riyadh', 'country_code' => 'SAU', 'status' => 1],
            ['id' => 5, 'code' => 'JED001', 'name' => 'Jeddah Branch', 'phone1' => '+966125555000', 'city' => 'Jeddah', 'state' => 'Makkah', 'country_code' => 'SAU', 'status' => 1],
            ['id' => 6, 'code' => 'DXB001', 'name' => 'Dubai Branch', 'phone1' => '+971445556000', 'city' => 'Dubai', 'state' => 'Dubai', 'country_code' => 'UAE', 'status' => 1],
            ['id' => 7, 'code' => 'AUH001', 'name' => 'Abu Dhabi Branch', 'phone1' => '+971265557000', 'city' => 'Abu Dhabi', 'state' => 'Abu Dhabi', 'country_code' => 'UAE', 'status' => 1],
            ['id' => 8, 'code' => 'KWT001', 'name' => 'Kuwait Branch', 'phone1' => '+96522258000', 'city' => 'Kuwait City', 'state' => 'Kuwait', 'country_code' => 'KWT', 'status' => 1],
        ];

        foreach ($branches as $branch) {
            $branch['created'] = Carbon::now();
            $branch['modified'] = Carbon::now();
            DB::connection('currentSite')->table('branches')->insert($branch);
        }
    }

    private function seedDepartments()
    {
        $departments = [
            ['id' => 1, 'name' => 'Human Resources', 'description' => 'HR Department managing employee relations', 'abbr' => 'HR'],
            ['id' => 2, 'name' => 'Information Technology', 'description' => 'IT Department managing technology infrastructure', 'abbr' => 'IT'],
            ['id' => 3, 'name' => 'Sales', 'description' => 'Sales Department managing client relationships', 'abbr' => 'SALES'],
            ['id' => 4, 'name' => 'Marketing', 'description' => 'Marketing Department managing brand and promotion', 'abbr' => 'MKT'],
            ['id' => 5, 'name' => 'Finance', 'description' => 'Finance Department managing company finances', 'abbr' => 'FIN'],
            ['id' => 6, 'name' => 'Operations', 'description' => 'Operations Department managing daily operations', 'abbr' => 'OPS'],
            ['id' => 7, 'name' => 'Customer Service', 'description' => 'Customer Service Department managing client support', 'abbr' => 'CS'],
            ['id' => 8, 'name' => 'Research and Development', 'description' => 'R&D Department managing innovation', 'abbr' => 'RND'],
            ['id' => 9, 'name' => 'Legal', 'description' => 'Legal Department managing compliance and contracts', 'abbr' => 'LEGAL'],
            ['id' => 10, 'name' => 'Procurement', 'description' => 'Procurement Department managing purchasing', 'abbr' => 'PROC'],
        ];

        foreach ($departments as $department) {
            $department['active'] = 1;
            $department['created'] = Carbon::now();
            $department['modified'] = Carbon::now();
            DB::connection('currentSite')->table('departments')->insert($department);
        }
    }

    private function seedDesignations()
    {
        $designations = [
            ['id' => 1, 'name' => 'CEO', 'description' => 'Chief Executive Officer', 'role_id' => 1, 'department_id' => null],
            ['id' => 2, 'name' => 'CTO', 'description' => 'Chief Technology Officer', 'role_id' => 1, 'department_id' => 2],
            ['id' => 3, 'name' => 'CFO', 'description' => 'Chief Financial Officer', 'role_id' => 1, 'department_id' => 5],
            ['id' => 4, 'name' => 'HR Manager', 'description' => 'Human Resources Manager', 'role_id' => 2, 'department_id' => 1],
            ['id' => 5, 'name' => 'IT Manager', 'description' => 'Information Technology Manager', 'role_id' => 2, 'department_id' => 2],
            ['id' => 6, 'name' => 'Sales Manager', 'description' => 'Sales Department Manager', 'role_id' => 2, 'department_id' => 3],
            ['id' => 7, 'name' => 'Marketing Manager', 'description' => 'Marketing Department Manager', 'role_id' => 2, 'department_id' => 4],
            ['id' => 8, 'name' => 'Operations Manager', 'description' => 'Operations Department Manager', 'role_id' => 2, 'department_id' => 6],
            ['id' => 9, 'name' => 'Senior Developer', 'description' => 'Senior Software Developer', 'role_id' => 3, 'department_id' => 2],
            ['id' => 10, 'name' => 'Software Developer', 'description' => 'Software Developer', 'role_id' => 3, 'department_id' => 2],
            ['id' => 11, 'name' => 'Junior Developer', 'description' => 'Junior Software Developer', 'role_id' => 4, 'department_id' => 2],
            ['id' => 12, 'name' => 'System Administrator', 'description' => 'System Administrator', 'role_id' => 3, 'department_id' => 2],
            ['id' => 13, 'name' => 'Business Analyst', 'description' => 'Business Analyst', 'role_id' => 3, 'department_id' => 2],
            ['id' => 14, 'name' => 'Sales Executive', 'description' => 'Sales Executive', 'role_id' => 4, 'department_id' => 3],
            ['id' => 15, 'name' => 'Sales Representative', 'description' => 'Sales Representative', 'role_id' => 4, 'department_id' => 3],
            ['id' => 16, 'name' => 'Marketing Specialist', 'description' => 'Marketing Specialist', 'role_id' => 4, 'department_id' => 4],
            ['id' => 17, 'name' => 'Content Creator', 'description' => 'Content Creator', 'role_id' => 4, 'department_id' => 4],
            ['id' => 18, 'name' => 'Accountant', 'description' => 'Accountant', 'role_id' => 4, 'department_id' => 5],
            ['id' => 19, 'name' => 'Financial Analyst', 'description' => 'Financial Analyst', 'role_id' => 3, 'department_id' => 5],
            ['id' => 20, 'name' => 'Customer Support', 'description' => 'Customer Support Representative', 'role_id' => 4, 'department_id' => 7],
            ['id' => 21, 'name' => 'Team Lead', 'description' => 'Team Lead', 'role_id' => 3, 'department_id' => null],
            ['id' => 22, 'name' => 'Intern', 'description' => 'Intern', 'role_id' => 5, 'department_id' => null],
            ['id' => 23, 'name' => 'Project Manager', 'description' => 'Project Manager', 'role_id' => 3, 'department_id' => null],
            ['id' => 24, 'name' => 'Quality Assurance', 'description' => 'Quality Assurance Specialist', 'role_id' => 4, 'department_id' => 2],
            ['id' => 25, 'name' => 'DevOps Engineer', 'description' => 'DevOps Engineer', 'role_id' => 3, 'department_id' => 2],
        ];

        foreach ($designations as $designation) {
            $designation['active'] = 1;
            $designation['created'] = Carbon::now();
            $designation['modified'] = Carbon::now();
            DB::connection('currentSite')->table('designations')->insert($designation);
        }
    }

    private function seedStaff()
    {
        // Arabic/Middle Eastern names
        $arabicFirstNames = [
            'Ahmed', 'Mohamed', 'Omar', 'Ali', 'Hassan', 'Mahmoud', 'Khaled', 'Youssef', 'Amr', 'Tamer',
            'Sherif', 'Karim', 'Hany', 'Sami', 'Basel', 'Tariq', 'Faisal', 'Abdullah', 'Saad', 'Bandar',
            'Rashid', 'Younis', 'Kareem', 'Ziad', 'Wael', 'Emad', 'Hesham', 'Osama', 'Reda', 'Ashraf',
            'Fatima', 'Aisha', 'Nour', 'Sara', 'Mona', 'Layla', 'Yasmin', 'Mariam', 'Dina', 'Rana',
            'Noura', 'Amira', 'Hala', 'Salma', 'Mai', 'Lina', 'Nada', 'Maha', 'Reema', 'Sarah'
        ];

        $arabicMiddleNames = [
            'Mohamed', 'Ali', 'Hassan', 'Omar', 'Ahmed', 'Mahmoud', 'Said', 'Gamal', 'Nasser', 'Abdel',
            'Fahad', 'Sultan', 'Khalid', 'Saleh', 'Majed', 'Saud', 'Abdullah', 'Mohammed', 'Tarek', 'Walid',
            'Magdy', 'Yousry', 'Sherif', 'Samir', 'Adel', 'Essam', 'Ashraf', 'Fady', 'Mohsen', 'Hossam'
        ];

        $arabicLastNames = [
            'Hassan', 'Omar', 'Rahman', 'Youssef', 'Ibrahim', 'Khalil', 'Farouk', 'Mansour', 'Salim', 'Kamal',
            'Al-Rashid', 'Al-Otaibi', 'Al-Harbi', 'Abdeen', 'Al-Mazrouei', 'Al-Zahra', 'Qureshi', 'Mansouri',
            'Elnagar', 'Shahin', 'Fouad', 'Zaki', 'Amin', 'Mostafa', 'Soliman', 'Fahmy', 'Gaber', 'Bakr',
            'Helmy', 'Hegazy', 'Nour', 'Darwish', 'Salah', 'Lotfy', 'Sayed', 'Abdo', 'Rashad', 'Gomaa',
            'Ismail', 'Al-Qahtani', 'Al-Mutairi', 'Al-Dosari', 'Al-Shammari'
        ];

        // International names
        $internationalFirstNames = [
            'John', 'David', 'Robert', 'Michael', 'James', 'William', 'Christopher', 'Daniel', 'Matthew', 'Anthony',
            'Jennifer', 'Maria', 'Sarah', 'Jessica', 'Lisa', 'Ashley', 'Emily', 'Amanda', 'Stephanie', 'Nicole'
        ];

        $internationalMiddleNames = [
            'Michael', 'James', 'Robert', 'William', 'David', 'Richard', 'Thomas', 'Christopher', 'Daniel', 'Paul',
            'Anne', 'Marie', 'Lynn', 'Rose', 'Grace', 'Elizabeth', 'Catherine', 'Michelle', 'Nicole', 'Elena'
        ];

        $internationalLastNames = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez',
            'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'
        ];

        $phoneCountryCodes = [
            'EGY' => '+20',
            'SAU' => '+966',
            'UAE' => '+971',
            'KWT' => '+965'
        ];

        $mobileCountryCodes = [
            'EGY' => '+2010',
            'SAU' => '+9665',
            'UAE' => '+9715',
            'KWT' => '+9655'
        ];

        $countries = ['EGY', 'SAU', 'UAE', 'KWT'];
        $nationalities = ['EGY', 'SAU', 'UAE', 'KWT', 'USA', 'GBR', 'CAN', 'ESP', 'FRA', 'DEU'];

        $employmentTypes = ['full-time', 'part-time', 'contract', 'intern'];
        $genders = ['Male', 'Female'];

        $staffData = [];
        $staffInfoData = [];

        for ($i = 1; $i <= 1000; $i++) {
            // Determine if this should be an Arabic or international name (80% Arabic, 20% international)
            $isArabic = $i <= 800;

            if ($isArabic) {
                $firstName = $arabicFirstNames[array_rand($arabicFirstNames)];
                $middleName = rand(0, 10) < 8 ? $arabicMiddleNames[array_rand($arabicMiddleNames)] : null; // 80% have middle name
                $lastName = $arabicLastNames[array_rand($arabicLastNames)];
                $nationality = $countries[array_rand($countries)];
                $citizenship = 'citizen';
            } else {
                $firstName = $internationalFirstNames[array_rand($internationalFirstNames)];
                $middleName = rand(0, 10) < 7 ? $internationalMiddleNames[array_rand($internationalMiddleNames)] : null; // 70% have middle name
                $lastName = $internationalLastNames[array_rand($internationalLastNames)];
                $nationality = $nationalities[array_rand($nationalities)];
                $citizenship = in_array($nationality, $countries) ? 'citizen' : 'resident';
            }

            $fullName = trim($firstName . ' ' . ($middleName ? $middleName . ' ' : '') . $lastName);

            // Generate employee code with different patterns for testing
            $codePatterns = [
                'EMP' . str_pad($i, 3, '0', STR_PAD_LEFT),           // EMP001, EMP002
                'STF' . str_pad($i, 4, '0', STR_PAD_LEFT),           // STF0001, STF0002
                'E' . $i,                                             // E1, E2
                ($i <= 50) ? 'INT' . str_pad($i - 40, 3, '0', STR_PAD_LEFT) : 'EMP' . str_pad($i, 3, '0', STR_PAD_LEFT), // Some interns
                ($i > 900) ? 'KSA' . str_pad($i - 900, 3, '0', STR_PAD_LEFT) : 'EMP' . str_pad($i, 3, '0', STR_PAD_LEFT), // Some KSA codes
            ];
            $code = $codePatterns[array_rand($codePatterns)];

            // Assign country and branch based on nationality/pattern
            $countryCode = 'EGY';
            $branchId = 1;

            if ($nationality === 'SAU' || strpos($code, 'KSA') === 0) {
                $countryCode = 'SAU';
                $branchId = rand(4, 5); // Riyadh or Jeddah
            } elseif ($nationality === 'UAE') {
                $countryCode = 'UAE';
                $branchId = rand(6, 7); // Dubai or Abu Dhabi
            } elseif ($nationality === 'KWT') {
                $countryCode = 'KWT';
                $branchId = 8; // Kuwait
            } else {
                $branchId = rand(1, 3); // Cairo, Alexandria, or Giza
            }

            // Generate phone numbers
            $phoneBase = $phoneCountryCodes[$countryCode];
            $mobileBase = $mobileCountryCodes[$countryCode];
            $uniqueNumber = str_pad($i, 7, '0', STR_PAD_LEFT);

            $homePhone = $phoneBase . '2255' . substr($uniqueNumber, -4);
            $businessPhone = $phoneBase . '2255' . substr($uniqueNumber, -4);
            $mobile = $mobileBase . '0' . substr($uniqueNumber, -6);

            // Generate email
            $email = strtolower($firstName . '.' . $lastName . '@company.com');
            $email = str_replace(' ', '', $email); // Remove any spaces

            $staffData[] = [
                'id' => $i,
                'added_by' => 1,
                'role_id' => rand(1, 5),
                'code' => $code,
                'type' => 'employee',
                'name' => $firstName,
                'middle_name' => $middleName,
                'last_name' => $lastName,
                'full_name' => $fullName,
                'can_access_system' => 1,
                'home_phone' => $homePhone,
                'business_Phone' => $businessPhone,
                'mobile' => $mobile,
                'email_address' => $email,
                'active' => rand(0, 10) > 0 ? 1 : 0, // 90% active, 10% inactive
                'deleted' => 0,
                'created' => Carbon::now()->subDays(rand(1, 365)),
                'branch_id' => $branchId,
                'citizenship_status' => $citizenship,
                'nationality' => $nationality,
                'country_code' => $countryCode,
            ];

            $staffInfoData[] = [
                'staff_id' => $i,
                'department_id' => rand(1, 10),
                'designation_id' => rand(1, 25),
                'employment_type_id' => rand(1, 4),
                'employment_level_id' => rand(1, 5),
                'gender' => $genders[array_rand($genders)],
                'birth_date' => Carbon::now()->subYears(rand(22, 60))->format('Y-m-d'),
                'personal_email' => 'personal.' . $email,
                'created' => Carbon::now(),
                'modified' => Carbon::now(),
            ];
        }

        // Insert in batches for better performance
        $batchSize = 100;
        $staffBatches = array_chunk($staffData, $batchSize);
        $staffInfoBatches = array_chunk($staffInfoData, $batchSize);

        foreach ($staffBatches as $batch) {
            DB::connection('currentSite')->table('staffs')->insert($batch);
        }

        foreach ($staffInfoBatches as $batch) {
            DB::connection('currentSite')->table('staff_info')->insert($batch);
        }
    }
}
