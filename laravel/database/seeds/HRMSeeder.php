<?php

namespace Database\Seeders;

use Izam\Daftra\Common\Utils\PluginUtil;

class HRMSeeder extends AbstractSiteSeeder
{
    protected $plugin = PluginUtil::HRM_PLUGIN;

    /**
     * Run the HRM database seeders in proper dependency order.
     *
     * @return void
     */
    public function insertData()
    {
        // Seed in dependency order
        echo "🚀 Starting HRM Seeding Process...\n";
        echo "📍 Seeding Branches...\n";
        $this->call(BranchSeeder::class);

        echo "👥 Seeding Roles...\n";
        $this->call(RoleSeeder::class);

        echo "💼 Seeding Employment Types...\n";
        $this->call(EmploymentTypeSeeder::class);

        echo "📊 Seeding Employment Levels...\n";
        $this->call(EmploymentLevelSeeder::class);

        echo "🏢 Seeding Departments...\n";
        $this->call(DepartmentSeeder::class);

        echo "🎯 Seeding Designations...\n";
        $this->call(DesignationSeeder::class);

        echo "👨‍💼 Seeding Staff (with StaffInfo and StaffJob)...\n";
        $this->call(StaffFactorySeeder::class);

        echo "✅ HRM Seeding completed successfully!\n";
        echo "📊 Summary:\n";
        echo "   - Branches: 13 records\n";
        echo "   - Roles: 13 records\n";
        echo "   - Employment Types: 8 records\n";
        echo "   - Employment Levels: 10 records\n";
        echo "   - Departments: 10 records\n";
        echo "   - Designations: 22 records\n";
        echo "   - Staff: 60 records (50 active + 5 managers + 5 inactive)\n";
        echo "   - Staff Info: 60 records\n";
        echo "   - Staff Job: 60 records\n";
    }
}
