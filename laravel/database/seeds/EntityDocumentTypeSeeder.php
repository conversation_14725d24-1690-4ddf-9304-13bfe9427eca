<?php

namespace Database\Seeders;

use App\Models\EntityDocumentType;
use Database\Seeders\AbstractSiteSeeder;

class EntityDocumentTypeSeeder extends AbstractSiteSeeder
{
    protected $plugin = null;

    public function insertData()
    {
        // Create essential staff document types
        $documentTypes = [
            [
                'entity_key' => 'staff',
                'name' => 'National ID',
                'is_required' => true,
                'is_expirable' => true,
                'display_order' => 1,
            ],
            [
                'entity_key' => 'staff', 
                'name' => 'Passport',
                'is_required' => false,
                'is_expirable' => true,
                'display_order' => 2,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Employment Contract',
                'is_required' => true,
                'is_expirable' => false,
                'display_order' => 3,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Resume/CV',
                'is_required' => true,
                'is_expirable' => false,
                'display_order' => 4,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Educational Certificate',
                'is_required' => false,
                'is_expirable' => false,
                'display_order' => 5,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Professional Certificate',
                'is_required' => false,
                'is_expirable' => true,
                'display_order' => 6,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Medical Certificate',
                'is_required' => false,
                'is_expirable' => true,
                'display_order' => 7,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Background Check',
                'is_required' => false,
                'is_expirable' => true,
                'display_order' => 8,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Reference Letter',
                'is_required' => false,
                'is_expirable' => false,
                'display_order' => 9,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Bank Account Details',
                'is_required' => false,
                'is_expirable' => false,
                'display_order' => 10,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Work Permit',
                'is_required' => false,
                'is_expirable' => true,
                'display_order' => 11,
            ],
            [
                'entity_key' => 'staff',
                'name' => 'Emergency Contact Form',
                'is_required' => false,
                'is_expirable' => false,
                'display_order' => 12,
            ],
        ];

        foreach ($documentTypes as $documentType) {
            EntityDocumentType::create(array_merge($documentType, [
                'created' => now(),
                'modified' => now(),
            ]));
        }

        // Create additional random document types using factory
        EntityDocumentType::factory()
            ->count(5)
            ->create();
    }
}
