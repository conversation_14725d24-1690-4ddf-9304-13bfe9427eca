<?php

namespace Database\Seeders;

use App\Models\EntityAttachment;
use App\Models\EntityDocument;
use App\Models\File;
use App\Models\Staff;
use Database\Seeders\AbstractSiteSeeder;

class EntityAttachmentSeeder extends AbstractSiteSeeder
{
    protected $plugin = null;

    public function insertData()
    {
        // Get existing staff, files, and entity documents
        $staffs = Staff::where('active', 1)->where('deleted', 0)->take(50)->get();
        $files = File::where('entity_key', 'staff')->get();
        $documents = EntityDocument::with('staff')->get();

        if ($staffs->isEmpty()) {
            $staffs = Staff::factory()->count(10)->create([
                'active' => 1,
                'deleted' => 0,
                'type' => 'employee',
            ]);
        }

        if ($files->isEmpty()) {
            $files = File::factory()->count(100)->create(['entity_key' => 'staff']);
        }

        // Attach files to staff members for different document categories
        foreach ($staffs as $staff) {
            // Attach identification documents (1-2 files per staff)
            $identificationFiles = $files->shuffle()->take(rand(1, 2));
            foreach ($identificationFiles as $file) {
                EntityAttachment::factory()
                    ->forStaff($staff->id)
                    ->forFile($file->id)
                    ->identification()
                    ->create();
            }

            // Attach contract documents (1 file per staff)
            $contractFile = $files->shuffle()->first();
            EntityAttachment::factory()
                ->forStaff($staff->id)
                ->forFile($contractFile->id)
                ->contract()
                ->create();

            // Attach photo documents (1 file per staff, usually default)
            $photoFile = $files->where('mime_type', 'LIKE', 'image%')->shuffle()->first();
            if ($photoFile) {
                EntityAttachment::factory()
                    ->forStaff($staff->id)
                    ->forFile($photoFile->id)
                    ->photo()
                    ->defaultAttachment()
                    ->create();
            }

            // Attach certificates (0-3 files per staff)
            $certificateFiles = $files->shuffle()->take(rand(0, 3));
            foreach ($certificateFiles as $index => $file) {
                EntityAttachment::factory()
                    ->forStaff($staff->id)
                    ->forFile($file->id)
                    ->certificate()
                    ->create(['sort_index' => $index]);
            }

            // Attach reference documents (0-2 files per staff)
            $referenceFiles = $files->shuffle()->take(rand(0, 2));
            foreach ($referenceFiles as $index => $file) {
                EntityAttachment::factory()
                    ->forStaff($staff->id)
                    ->forFile($file->id)
                    ->reference()
                    ->create(['sort_index' => $index]);
            }

            // Attach general documents (2-5 files per staff)
            $generalFiles = $files->shuffle()->take(rand(2, 5));
            foreach ($generalFiles as $index => $file) {
                EntityAttachment::factory()
                    ->forStaff($staff->id)
                    ->forFile($file->id)
                    ->document()
                    ->create(['sort_index' => $index]);
            }
        }

        // Create some additional random attachments
        EntityAttachment::factory()
            ->count(50)
            ->create();

        // Create some default attachments for testing
        EntityAttachment::factory()
            ->count(20)
            ->defaultAttachment()
            ->create();
    }
}
