<?php

namespace Database\Seeders;

use App\Models\Staff;
use App\Models\StaffInfo;
use App\Models\StaffJob;
use Izam\Daftra\Common\Utils\PluginUtil;

class StaffFactorySeeder extends AbstractSiteSeeder
{
    protected $plugin = PluginUtil::HRM_PLUGIN;

    public function insertData()
    {
        // Create active staff with info and job records
        Staff::factory()
            ->active()
            ->count(1500)
            ->create()
            ->each(function ($staff) {
                // Create StaffInfo for each staff
                StaffInfo::factory()
                    ->create(['staff_id' => $staff->id]);

                // Create StaffJob for each staff
                StaffJob::factory()
                    ->create(['staff_id' => $staff->id]);
            });

        // Create some managers
        Staff::factory()
            ->manager()
            ->count(5)
            ->create()
            ->each(function ($staff) {
                StaffInfo::factory()
                    ->create(['staff_id' => $staff->id]);

                StaffJob::factory()
                    ->permanent()
                    ->create(['staff_id' => $staff->id]);
            });

        // Create inactive/exited staff
        Staff::factory()
            ->inactive()
            ->count(5)
            ->create()
            ->each(function ($staff) {
                StaffInfo::factory()
                    ->create(['staff_id' => $staff->id]);

                StaffJob::factory()
                    ->exited()
                    ->create(['staff_id' => $staff->id]);
            });
    }
}
