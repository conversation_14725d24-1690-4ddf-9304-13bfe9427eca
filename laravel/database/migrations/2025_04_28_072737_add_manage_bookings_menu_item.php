<?php

use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Izam\Daftra\Common\Utils\PermissionUtil;

class AddManageBookingsMenuItem extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $parent_id = DB::table(Config::get('menu.table'))->where('key_name', 'new_booking')->first()->id;
        DB::table(Config::get('menu.table'))->insert([
            [
                'key_name' => 'manage_bookings',
                'title' => 'Manage Bookings',
                'title_vars' => '',
                'url' => '/v2/owner/new-booking',
                'quicklink' => 1,
                'class' => 'calendar',
                'icon-class' => '',
                'plugin' => PluginUtil::NEW_BOOKING_PLUGIN,
                'check_plugins' => '',
                'showif' => PermissionUtil::MANAGE_BOOKING_SETTINGS,
                'and_showif' => '',
                'parent_id' => $parent_id,
                'parent_only' => null,
                'display_order' => 10,
                'version' => \App\Utils\MenuItemUtil::VERSION_TYPE_COMMON
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table(Config::get('menu.table'))->where('key_name', 'manage_bookings')->delete();
    }
}
