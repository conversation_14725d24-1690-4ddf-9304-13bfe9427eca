<?php

use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class addRequisitionItemAccountingCols extends Migration
{   
    private $fields;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

            $this->fields = [
                [
                    'key' => 'requisition_items.account_id',
                    'entity' => EntityKeyTypesUtil::REQUISITION_ITEM,
                    'plugin_id' => '',
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'label' => 'Account Id',
                    'db_name' => 'account_id',
                    'on_import' => 0,
                    'on_export' => 0,
                ],
                [
                    'key' => 'requisition_items.cost_center_id',
                    'entity' => EntityKeyTypesUtil::REQUISITION_ITEM,
                    'plugin_id' => '',
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'label' => 'Cost Center Id',
                    'db_name' => 'cost_center_id',
                    'on_import' => 0,
                    'on_export' => 0,
                ]
            ];

        foreach ($this->fields as $field) {
            DB::table('entity_fields')->insert($field);
        }

        DB::table('field_relations')->insert([
            [
                'entity' => EntityKeyTypesUtil::REQUISITION_ITEM,
                'name' => 'journal_account',
                'type' => 'belongsTo',
                'field_key' => 'requisition_items.account_id',
                'reference_field_key' => 'journal_accounts.id',
                'reference_entity_key' => EntityKeyTypesUtil::JOURNAL_ACCOUNT,
            ],
            [
                'entity' => EntityKeyTypesUtil::REQUISITION_ITEM,
                'name' => 'cost_center',
                'type' => 'belongsTo',
                'field_key' => 'requisition_items.cost_center_id',
                'reference_field_key' => 'cost_centers.id',
                'reference_entity_key' => EntityKeyTypesUtil::COST_CENTER,
            ],
        ]);

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')->whereIn('key', [
            'requisition_items.account_id',
            'requisition_items.cost_center_id'
        ])->delete();

        DB::table('entity_fields')->whereIn('key', [
            'requisition_items.account_id',
            'requisition_items.cost_center_id'
        ])->delete();
    }
}
