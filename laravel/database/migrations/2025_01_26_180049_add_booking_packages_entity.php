
<?php

use App\Database\EntityMigration;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use App\Utils\EntityFieldRelationUtil;
use App\Repositories\ProductRepository;
use Izam\Daftra\Common\Utils\ProductStatusUtil;

class AddBookingPackagesEntity extends EntityMigration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entities')->insert([
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'connection' => 'currentSite',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Booking Packages',
                'db_name' => 'booking_packages',
                'name_field' => 'name',
                'type' => 'form'
            ],
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                'connection' => 'currentSite',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Booking Package Services',
                'db_name' => 'booking_package_services',
                'name_field' => 'id',
                'type' => 'subform'
            ],
        ]);

        DB::table('entity_fields')->insert([
            // booking_packages fields
            [
                'key' => 'booking_packages.id',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'ID',
                'db_name' => 'id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 1,
                'is_listing' => 1,
                'is_listing_mobile' => 1,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_packages.name',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Name',
                'db_name' => 'name',
                'default_value' => null,
                'is_filter' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'is_listing' => 1,
                'is_listing_mobile' => 1,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_packages.status',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Status',
                'db_name' => 'status',
                'default_value' => '1',
                'is_filter' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'is_listing' => 1,
                'is_listing_mobile' => 1,
                'allowed_values' => json_encode([
                    "1" => "Active",
                    "0" => "Inactive"
                ]),
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_packages.total_price',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Total Price',
                'db_name' => 'total_price',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 1,
                'is_listing_mobile' => 1,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_packages.booking_package_services',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Services',
                'db_name' => 'booking_package_services',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => 'required|array|min:1',
                'options' => null
            ],
            [
                'key' => 'booking_packages.branch_id',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_BRANCH,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Branch',
                'db_name' => 'branch_id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_packages.created',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Created At',
                'db_name' => 'created',
                'default_value' => 'current_timestamp',
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_packages.modified',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Modified At',
                'db_name' => 'modified',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],

            // booking_packages_items fields
            [
                'key' => 'booking_package_services.id',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'ID',
                'db_name' => 'id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_package_services.booking_package_id',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Booking Package',
                'db_name' => 'booking_package_id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'booking_package_services.service_id',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Service',
                'db_name' => 'service_id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => 'required|active_product',
                'options' => json_encode([
                    'property' => 'name',
                    'field_type' => 'auto_suggest',
                    'identifier' => 'id',
                    "find_method" =>  ['name' => 'getSearchAutoSuggestActviceServices'],
                    'target_class' => ProductRepository::class,
                    'auto_suggest_url' => '/v2/owner/products/ajaxProductsFilter?active_only=1&services_only=1&term=d&_type=query&q=__q__',
                    "is_single" => true,
                    'target_entity' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                    "conditions" => [
                        [
                            'col' => 'status',
                            'value' => ProductStatusUtil::STATUS_ACTIVE,
                        ],
                        [
                            'col' => 'type',
                            'value' => 2,
                        ]
                    ],
                    'filter' => [
                        'order' => 1,
                        "label" => "Filter by Service",
                    ],
                    "default" => [
                        "cols" => [
                            "id",
                            "name",
                            "product_code",
                        ],
                        "search" => [
                            [
                                "col" => "name",
                                "operator" => "like"
                            ],
                            [
                                "col" => "product_code",
                                "operator" => "like"
                            ]
                        ],
                    ]
                ]),
            ],
            [
                'key' => 'booking_package_services.price',
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Selling Price',
                'db_name' => 'price',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
        ]);

        DB::table('field_relations')->insert(
            [
                [
                    'name' => 'booking_package_services',
                    'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                    'field_key' => 'booking_packages.booking_package_services',
                    'type' => EntityFieldRelationUtil::HAS_MANY_TYPE,
                    'reference_entity_key' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                    'reference_field_key' => 'booking_package_services.booking_package_id'
                ],
                [
                    'name' => 'service',
                    'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                    'field_key' => 'booking_package_services.service_id',
                    'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
                    'reference_entity_key' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                    'reference_field_key' => 'products.id'
                ]
            ]
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entities')->whereIn('key', [
            EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
            EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
        ])->delete();

        DB::table('entity_fields')->whereIn('entity', [
            EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
            EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
        ])->delete();

        DB::table('field_relations')->whereIn('entity', [
            EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
            EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
        ])->delete();
    }
}