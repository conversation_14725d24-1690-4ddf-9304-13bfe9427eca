<?php

use App\Database\EntityMigration;
use Illuminate\Support\Facades\DB;
use App\Repositories\ProductRepository;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ProductOption;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use App\Utils\EntityFieldUtil;

class BookingPacakgesEntityFieldsEdits extends EntityMigration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')->where('key', 'booking_packages.booking_package_services')->update([
            'options' => json_encode([
                "total" => [
                    "price"
                ]
            ])
        ]);
        DB::table('entity_fields')->where('key', 'booking_packages.total_price')->update([
            'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN,
            'validation_rules' => null
        ]);
        
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
