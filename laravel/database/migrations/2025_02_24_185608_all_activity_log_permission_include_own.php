<?php

use App\Utils\PermissionUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AllActivityLogPermissionIncludeOwn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('permissions')->where('id', PermissionUtil::View_All_Activity_Logs)->update([
            'already_include' => PermissionUtil::View_His_Activity_Logs
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('permissions')->where('id', PermissionUtil::View_All_Activity_Logs)->update([
            'already_include' => null
        ]);
    }
}
