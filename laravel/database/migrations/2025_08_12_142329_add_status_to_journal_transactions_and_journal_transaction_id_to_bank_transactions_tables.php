<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;
use Izam\Daftra\Common\Utils\PluginUtil;

class AddStatusToJournalTransactionsAndJournalTransactionIdToBankTransactionsTables extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `journal_transactions` ADD `status` ENUM('not_matched', 'matched', 'excluded') NOT NULL DEFAULT 'not_matched';",
                "add_status_to_journal_transactions"
            ),
            new SqlUpdateEntity(
                "UPDATE `journal_transactions` SET `status` = 'matched' WHERE bank_transaction_id is not null;",
                "change_status_of_journal_transactions"
            ),
            new SqlUpdateEntity(
                "ALTER TABLE `bank_transactions` ADD `journal_transaction_id` INT(11) NULL DEFAULT NULL AFTER `bank_id`, ADD CONSTRAINT `journal_transaction_id_bank_transactions_fk` FOREIGN KEY (`journal_transaction_id`) REFERENCES `journal_transactions` (`id`);",
                "add_journal_transaction_id_to_bank_transactions_table"
            ),
        ];
        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `journal_transactions` DROP `status`;",
                "drop_status_from_journal_transactions"
            ),
            new SqlUpdateEntity(
                "ALTER TABLE `bank_transactions` DROP FOREIGN KEY `journal_transaction_id_bank_transactions_fk`, DROP `journal_transaction_id`;",
                "drop_journal_transaction_id_from_bank_transactions_table"
            ),
        ];
    }
}
