<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class UpdateStockRequestEntityRepoService extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update stock_request entity with proper repository and service names
        DB::table('entities')
            ->where('key', EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY)
            ->update([
                'repo_name' => 'StockRequestRepository',
                'service_name' => 'StockRequestService'
            ]);

        // Set stock_requests.items field on_export to 0
        DB::table('entity_fields')
            ->where('key', 'stock_requests.items')
            ->where('entity', 'stock_request')
            ->update(['on_export' => 0]);

        // Set stock_requests.attachments field on_export to 0
        DB::table('entity_fields')
            ->where('key', 'stock_requests.attachments')
            ->where('entity', 'stock_request')
            ->update(['on_export' => 0]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert stock_requests.items field on_export to 1
        DB::table('entity_fields')
            ->where('key', 'stock_requests.items')
            ->where('entity', 'stock_request')
            ->update(['on_export' => 1]);

        // Revert stock_requests.attachments field on_export to 1
        DB::table('entity_fields')
            ->where('key', 'stock_requests.attachments')
            ->where('entity', 'stock_request')
            ->update(['on_export' => 1]);

        // Revert the repository and service names to null
        DB::table('entities')
            ->where('key', EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY)
            ->update([
                'repo_name' => null,
                'service_name' => null
            ]);
    }
}