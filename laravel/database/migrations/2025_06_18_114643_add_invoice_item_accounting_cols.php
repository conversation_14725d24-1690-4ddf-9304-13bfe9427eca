<?php

use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class addInvoiceItemAccountingCols extends Migration
{   
    private $fields;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

            $this->fields = [
                [
                    'key' => 'invoice_items.sales_account_id',
                    'entity' => EntityKeyTypesUtil::INVOICE_ITEM_ENTITY_KEY,
                    'plugin_id' => PluginUtil::SalesPlugin,
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'label' => 'Account Id',
                    'db_name' => 'sales_account_id',
                    'on_import' => 0,
                    'on_export' => 0,
                ],
                [
                    'key' => 'invoice_items.cost_center_id',
                    'entity' => EntityKeyTypesUtil::INVOICE_ITEM_ENTITY_KEY,
                    'plugin_id' => PluginUtil::SalesPlugin,
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'label' => 'Cost center Id',
                    'db_name' => 'cost_center_id',
                    'on_import' => 0,
                    'on_export' => 0,
                ]
            ];

        foreach ($this->fields as $field) {
            DB::table('entity_fields')->insert($field);
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')->whereIn('key', [
            'invoice_items.sales_account_id',
            'invoice_items.cost_center_id'
        ])->delete();

    }
}
