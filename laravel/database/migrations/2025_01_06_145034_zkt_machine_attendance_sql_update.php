<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;
use App\Utils\PluginUtil;

class ZktMachineAttendanceSqlUpdate extends SqlUpdateMigration
{
    public function __construct() {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `attendance_logs`
                    CHANGE `status` `status` ENUM('draft','sign_in','sign_out','repeated_sign_in','repeated_sign_out','invalid', 'unknown_employee') NOT NULL DEFAULT 'draft',
                    ADD COLUMN `staff_machine_id` INT NULL DEFAULT NULL AFTER `status`,
                    ADD INDEX(`staff_machine_id`);

                CREATE TABLE IF NOT EXISTS `machine_employee_mappings_v2` (
                    `id` INT NOT NULL AUTO_INCREMENT ,
                    PRIMARY KEY (`id`) ,
                    `machine_id` INT NOT NULL ,
                    `staff_id` INT NOT NULL ,
                    `machine_employee_name` VA<PERSON>HA<PERSON>(255) NULL DEFAULT NULL,
                    `staff_machine_id` INT NOT NULL ,
                    INDEX(`staff_machine_id`),
                    INDEX (`machine_id`),
                    INDEX (`staff_id`)
                ) ENGINE = InnoDB;

                INSERT INTO `machine_employee_mappings_v2` (`id`, `machine_id`, `staff_id`, `staff_machine_id`) select `id`, `machine_id`, `staff_id`, `staff_machine_id` from `machine_employee_mappings`;
                DROP TABLE `machine_employee_mappings`;
                RENAME TABLE `machine_employee_mappings_v2` TO `machine_employee_mappings`;",

                "add_zkt_columns_to_attendance_logs_and_machine_mapping",
                PluginUtil::HRM_ATTENDANCE_PLUGIN
            ),
            /** this was done in a separate sql to avoid errors when the foreign key does not exist */
            new SqlUpdateEntity(
                "ALTER TABLE `attendance_logs`
                    DROP CONSTRAINT `attendance_logs_staffs_fk`;",
                "remove_attendance_logs_staff_id_fk",
                PluginUtil::HRM_ATTENDANCE_PLUGIN
            ),
            new SqlUpdateEntity(
                "ALTER TABLE `attendance_logs`
                    DROP INDEX `attendance_logs_staffs_fk`;",
                "remove_attendance_logs_staff_id_repeated_index",
                PluginUtil::HRM_ATTENDANCE_PLUGIN
            ),
            new SqlUpdateEntity(
                "ALTER TABLE `attendance_logs`
                    ADD INDEX(`staff_id`);",
                "add_attendance_logs_staff_id_index",
                PluginUtil::HRM_ATTENDANCE_PLUGIN
            )
        ];
        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `attendance_logs`
                    CHANGE `status` `status` ENUM('draft','sign_in','sign_out','repeated_sign_in','repeated_sign_out','invalid') NOT NULL DEFAULT 'draft',
                    DROP COLUMN `staff_machine_id`;
                AlTER TABLE `machine_employee_mappings` DROP COLUMN `machine_employee_name`;
                ",
                "add_zkt_columns_to_attendance_logs_and_machine_mapping_reverse",
                PluginUtil::HRM_ATTENDANCE_PLUGIN
            ),
        ];
    }
}
