<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityFieldUtil;


class AddUuidToMachinesEntity extends Migration
{
    public function up()
    {
        DB::table('entity_fields')->insert([
            'key' => 'machines.uuid',
            'entity' => EntityKeyTypesUtil::MACHINE,
            'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN,
            'plugin_id' => \App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN,
            'label' => 'UUID',
            'db_name' => 'uuid',
            'default_value' => null,
            'is_filter' => 0,
            'on_create' => 0,
            'on_update' => 0,
            'on_import' => 0,
            'on_export' => 0,
            'is_listing' => 0,
            'is_listing_mobile' => 0,
            'allowed_values' => null,
        ]);
    }

    public function down()
    {
        DB::table('entity_fields')->where('key', 'machines.uuid')->delete();
    }
}
