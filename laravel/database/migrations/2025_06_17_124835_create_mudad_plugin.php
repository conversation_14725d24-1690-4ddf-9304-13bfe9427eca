<?php

use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateMudadPlugin extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::transaction(function () {
            DB::table('plugins')->insert([
                    [
                        'id' => PluginUtil::MUDAD_PLUGIN,
                        'name' => 'Mudad',
                        'description' => "Easily generate payroll runs and export them to Mudad to stay compliant with KSA regulations and streamline your Saudi employee payroll process.",
                        'ar_description' => "أنشئ كشوفات الرواتب بسهولة وصدّرها إلى مدد للامتثال لأنظمة المملكة وتبسيط إدارة رواتب موظفيك في السعودية.",
                        'active' => 1 ,
                        'display_order' => 800,
                        'created' => Carbon::now()->toDateTimeString(),
                        'modified' => Carbon::now()->toDateTimeString() ,
                        'is_external' => 1,
                        'beta_version' => 1,
                        'group_id'=>6,
                        'plugin_key' => 'mudad_integration'
                    ]
                ]
            );
            DB::table('plugin_dependencies')->insert([
                [
                    'plugin_id' => PluginUtil::MUDAD_PLUGIN,
                    'parent_plugin_id' => PluginUtil::HRM_PAYROLL_PLUGIN
                ]
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::transaction(function () {
            DB::table('plugins')->where('id', PluginUtil::MUDAD_PLUGIN)->delete();
            DB::table('plugin_dependencies')->where('plugin_id', PluginUtil::MUDAD_PLUGIN)->delete();
        });
    }
}
