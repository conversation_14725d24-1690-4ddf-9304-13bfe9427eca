<?php

use App\Utils\EntityFieldRelationUtil;
use App\Utils\EntityKeyTypesUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EditEntityDocumentFilterFieldsAndFieldRelation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')->where('entity', 'entity_document')
        ->update(['is_sorting' => 0, 'is_filter' => 0]);

        DB::table('entity_fields')->whereIn('key', ['entity_documents.expiry_date', 'entity_documents.created'])
        ->update(['is_sorting' => 1]);

        DB::table('field_relations')->insert([
            'name' => 'entity_document_type',
            'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
            'field_key' => 'entity_documents.entity_document_type_id',
            'reference_field_key' => 'entity_document_types.id',
            'reference_entity_key' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
            'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
            'conditions' => null,
        ]);

        DB::table('field_relations')->insert([
            'name' => 'created_by',
            'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
            'field_key' => 'entity_documents.staff_id',
            'reference_field_key' => 'staffs.id',
            'reference_entity_key' => EntityKeyTypesUtil::STAFF_ENTITY_KEY,
            'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
            'conditions' => null,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')->whereIn('key', ['entity_documents.expiry_date'])
        ->update(['is_filter' => 0]);

        DB::table('field_relations')->where([
            'name' => 'entity_document_type',
            'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
            'field_key' => 'entity_documents.entity_document_type_id',
            'reference_field_key' => 'entity_document_types.id',
            'reference_entity_key' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
            'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
        ])->delete();
    }
}
