<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;
use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOfficialIdToStaffsTableSqlUpdate extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `staffs` ADD `official_id` VARCHAR(255) NULL DEFAULT NULL AFTER `nationality`;",
                'add_official_id_to_staffs',
                PluginUtil::StaffPlugin
            ),
            new SqlUpdateEntity("ALTER TABLE `staffs` ADD UNIQUE KEY `unique_official_id` (`official_id`) ;",
            'add_unique_official_id_to_staffs_table',PluginUtil::StaffPlugin),

        ];
        $this->reverseQuery = [
            new SqlUpdateEntity("ALTER TABLE `staffs` DROP INDEX `unique_official_id`;", 'add_unique_official_id_to_staffs_table_reverse',PluginUtil::StaffPlugin),
            new \App\Entities\SqlUpdateEntity(
                "ALTER TABLE `staffs` DROP `official_id`;",
                'add_official_id_to_staffs_reverse'
            ),

        ];
    }
}
