<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddSalesCostJournalAccountIdToPurchaseInvoiceItems extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `purchase_order_items`
                 ADD `sales_cost_account_id` INT DEFAULT NULL AFTER `product_id`",
                'add_sales_cost_account_id_to_invoice_items'
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `purchase_order_items`
                 DROP COLUMN `sales_cost_account_id`",
                'revert_sales_cost_account_id_from_invoice_items'
            ),
        ];
    }
}