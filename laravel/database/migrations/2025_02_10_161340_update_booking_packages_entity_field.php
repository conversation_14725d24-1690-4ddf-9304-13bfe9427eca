<?php

use App\Database\EntityMigration;
use Illuminate\Support\Facades\DB;
use App\Utils\EntityFieldUtil;

class UpdateBookingPackagesEntityField extends EntityMigration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')->where('key', 'booking_packages.name')->update([
            'validation_rules' => 'required|max:80',
        ]);
        DB::table('entity_fields')->where('key', 'booking_packages.status')->update([
            'validation_rules' => 'required|in:0,1',
        ]);
        DB::table('entity_fields')->where('key', 'booking_packages.total_price')->update([
            'validation_rules' => 'required|numeric|gte:0',
            'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRICE,
        ]);
        DB::table('entity_fields')->where('key', 'booking_package_services.price')->update([
            'validation_rules' => 'required|numeric|gte:0',
            'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRICE,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        
    }
}