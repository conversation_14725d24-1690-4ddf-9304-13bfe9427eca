<?php

use App\Database\EntityMigration;
use Illuminate\Support\Facades\DB;
use App\Repositories\ProductRepository;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ProductOption;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class UpdateBookingPackageServiceIdEntityFieldOptions extends EntityMigration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')->where('key', 'booking_package_services.service_id')->update([
            'options' => json_encode([
                'property' => 'name',
                'field_type' => 'auto_suggest',
                'identifier' => 'id',
                "find_method" =>  ['name' => 'getSearchAutoSuggestBookingServices'],
                'target_class' => ProductRepository::class,
                'auto_suggest_url' => '/v2/owner/products/ajaxProductsFilter?active_only=1&services_only=1&for_booking=1&term=d&_type=query&q=__q__',
                "is_single" => true,
                'entityKey' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                "template" => ProductOption::class,
                "empty_option" => false,
                "conditions" => [
                    [
                        'col' => 'status',
                        'value' => ProductStatusUtil::STATUS_ACTIVE,
                    ],
                    [
                        'col' => 'type',
                        'value' => 2,
                    ]
                ],
                'filter' => [
                    'order' => 1,
                    "label" => "Filter by Service",
                ],
                "default" => [
                    "cols" => [
                        "id",
                        "name",
                        "product_code",
                    ],
                    "search" => [
                        [
                            "col" => "name",
                            "operator" => "like"
                        ],
                        [
                            "col" => "product_code",
                            "operator" => "like"
                        ]
                    ],
                ]
            ])
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
