<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddCostCenterIdToPurchaseInvoiceItems extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `purchase_order_items`
                 ADD `cost_center_id` INT DEFAULT NULL AFTER `product_id`",
                'add_cost_center_id_to_purchase_order_items'
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `purchase_order_items`
                 DROP COLUMN `cost_center_id`",
                'revert_add_cost_center_id_to_purchase_order_items'
            ),
        ];
    }
}
