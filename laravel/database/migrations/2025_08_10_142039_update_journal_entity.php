<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use Izam\Daftra\Common\Utils\PluginUtil;

class UpdateJournalEntity extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entities')->where('key', EntityKeyTypesUtil::JOURNAL)->update(['plugin_id' => PluginUtil::AccountingPlugin]);
        DB::table('entities')->where('key', EntityKeyTypesUtil::JOURNAL_ACCOUNT)->update(['plugin_id' => PluginUtil::AccountingPlugin]);
        DB::table('entities')->where('key', EntityKeyTypesUtil::JOURNAL_CAT)->update(['plugin_id' => PluginUtil::AccountingPlugin]);
        DB::table('entities')->where('key', EntityKeyTypesUtil::JOURNAL_TRANSACTION)->update(['plugin_id' => PluginUtil::AccountingPlugin]);
        DB::table('entities')->where('key', EntityKeyTypesUtil::JOURNAL_ACCOUNT_ROUTE)->update(['plugin_id' => PluginUtil::AccountingPlugin]);
        DB::table('entities')->where('key', EntityKeyTypesUtil::JOURNAL_TRANSACTION_LOG)->update(['plugin_id' => PluginUtil::AccountingPlugin]);

        DB::table('entities')->where('key', EntityKeyTypesUtil::JOURNAL_ACCOUNT)->update(['service_name' => 'JournalAccountService']);
        DB::table('entities')->where('key', EntityKeyTypesUtil::COST_CENTER_TRANSACTION)->update(['service_name' => 'CostCenterTransactionService', 'repo_name' => 'CostCenterTransactionRepository']);

        DB::table('entity_fields')->whereIn('entity', ['journal', 'journal_transaction'])->update(['on_import' => 0]);

        DB::table('entity_fields')->where('key', 'journals.number')->update(['validation_rules' => 'required']);
        DB::table('entity_fields')->where('key', 'journals.currency_code')->update(['validation_rules' => 'required|exists:currencies,code']);
        DB::table('entity_fields')->where('key', 'journals.date')->update(['field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE, 'validation_rules' => 'required|date']);
        DB::table('entity_fields')->where('key', 'journals.description')->update(['label' => 'Journal Description', 'validation_rules' => 'nullable']);

        DB::table('entity_fields')->where('key', 'journal_transactions.journal_account_id')->update(['validation_rules' => 'required']);

        DB::table('entity_fields')->whereIn('key', [
            'journal_transactions.currency_debit',
            'journal_transactions.currency_credit',
        ])->update(['validation_rules' => 'required|numeric|min:0']);

        DB::table('entity_fields')->whereIn('key', [
            'journal_transactions.description',
            'journal_transactions.tags',
            'journal_transactions.tax_id',
        ])->update(['validation_rules' => 'nullable']);

        DB::table('entity_relations')->insert([
            [
                'entity_key' => EntityKeyTypesUtil::JOURNAL,
                'related_entity_key' => EntityKeyTypesUtil::JOURNAL_TRANSACTION,
                'related_entity_field_key' => 'journal_transactions.journal_id',
            ],
            [
                'entity_key' => EntityKeyTypesUtil::JOURNAL_TRANSACTION,
                'related_entity_key' => EntityKeyTypesUtil::TAX_ENTITY_KEY,
                'related_entity_field_key' => 'taxes.id',
            ],
            [
                'entity_key' => EntityKeyTypesUtil::JOURNAL_TRANSACTION,
                'related_entity_key' => EntityKeyTypesUtil::COST_CENTER,
                'related_entity_field_key' => 'cost_centers.id',
            ]
        ]);

        DB::table('entity_fields')->whereIn('key', [
            'journals.number',
            'journals.date',
            'journals.description',
            'journal_transactions.journal_account_id',
            'journal_transactions.currency_debit',
            'journal_transactions.currency_credit',
            'journal_transactions.description',
            'journal_transactions.tax_id',
        ])->update(['on_import' => 1]);

        DB::table('entity_fields')->where('key', 'journals.number')->update(['label' => 'Journal Code']);
        DB::table('entity_fields')->where('key', 'journals.date')->update(['label' => 'Journal Date']);
        DB::table('entity_fields')->where('key', 'journals.currency_code')->update(['label' => 'Currency']);
        DB::table('entity_fields')->where('key', 'journal_transactions.journal_account_id')->update(['label' => 'Account']);
        DB::table('entity_fields')->where('key', 'journal_transactions.currency_debit')->update(['label' => 'Debit']);
        DB::table('entity_fields')->where('key', 'journal_transactions.currency_credit')->update(['label' => 'Credit']);
        DB::table('entity_fields')->where('key', 'journal_transactions.description')->update(['label' => 'Transaction Description']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_relations')->where('entity_key', EntityKeyTypesUtil::JOURNAL)->where('related_entity_key', EntityKeyTypesUtil::JOURNAL_TRANSACTION)->delete();
        DB::table('entity_relations')->where('entity_key', EntityKeyTypesUtil::JOURNAL_TRANSACTION)->where('related_entity_key', EntityKeyTypesUtil::TAX_ENTITY_KEY)->delete();
        DB::table('entity_relations')->where('entity_key', EntityKeyTypesUtil::JOURNAL_TRANSACTION)->where('related_entity_key', EntityKeyTypesUtil::COST_CENTER)->delete();
    }
}
