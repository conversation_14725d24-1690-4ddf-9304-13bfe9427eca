<?php

use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
class CreateEntityDocumentTypesEntityMappings extends Migration
{
    private $entities;
    private $fields;


    public function __construct()
    {
        $this->entities = [
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'connection' => 'currentSite',
                'plugin_id' => null,
                'label' => 'Entity Document Type',
                'db_name' => 'entity_document_types',
                'name_field' => 'name',
                'type' => 'form'
            ],
        ];

        $this->fields = [
            [
                'key' => 'entity_document_types.id',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => null ,
                'label' => 'ID',
                'db_name' => 'id',
                'default_value' => null,
                'is_filter' => '0',
                'order_index' => 0,
                'validation_rules' => null,
                'on_create' => '1',
                'on_update' => '1',
                'on_import' => '0',
                'on_export' => '1',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_document_types.name',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT,
                'plugin_id' => null,
                'label' => 'Name',
                'is_sorting' => 1,
                'db_name' => 'name',
                'default_value' => null,
                'is_filter' => '1',
                'validation_rules' => 'string|required|unique|max:255',
                'on_create' => '1',
                'order_index' => 0,
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '1',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_document_types.entity_key',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN,
                'plugin_id' => null,
                'label' => 'Entity',
                'db_name' => 'entity_key',
                'order_index' => 0,
                'default_value' => null,
                'is_filter' => '1',
                'validation_rules' => 'required',
                'on_create' => '1',
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '1',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_document_types.is_required',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_CHECKBOX,
                'plugin_id' => null,
                'label' => 'Is Required',
                'db_name' => 'is_required',
                'default_value' =>  "1",
                'is_filter' => '0',
                'order_index' => 0,
                'validation_rules' => null,
                'on_create' => '1',
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '1',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_document_types.is_expirable',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_CHECKBOX,
                'plugin_id' => null,
                'label' => 'Is Expirable',
                'db_name' => 'is_expirable',
                'default_value' => '0',
                'is_filter' => '0',
                'validation_rules' => null,
                'on_create' => '1',
                'order_index' => 0,
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_document_types.display_order',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'plugin_id' => null,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN,
                'validation_rules' => null,
                'label' => 'Display Order',
                'db_name' => 'display_order',
                'is_listing' => 0,
                'is_filter' => 0,
                'is_sorting' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'default_value' => null,
                'allowed_values' => null,
                'options' => null,
            ],
            [
                'key' => 'entity_document_types.created',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER,
                'plugin_id' =>null,
                'label' => 'Date Of Creation',
                'is_sorting' => 1,
                'db_name' => 'created',
                'default_value' => null,
                'is_filter' => '0',
                'validation_rules' => null,
                'on_create' => '0',
                'on_update' => '0',
                'on_import' => '0',
                'order_index' => 0,
                'on_export' => '0',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_document_types.modified',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT_TYPE,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER,
                'plugin_id' =>null,
                'label' => 'Modified At',
                'db_name' => 'modified',
                'default_value' => null,
                'is_filter' => '0',
                'validation_rules' => null,
                'on_create' => '0',
                'on_update' => '0',
                'order_index' => 0,
                'on_import' => '0',
                'on_export' => '0',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],

        ];

    }


    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::transaction(function () {
            foreach ($this->entities as $entity) {
                DB::table('entities')->insert($entity);
            }
            foreach ($this->fields as $field) {
                DB::table('entity_fields')->insert($field);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::transaction(function () {
            DB::table('entity_fields')->whereIn('key', array_map(fn($field) => $field['key'], $this->fields))->delete();
            DB::table('entities')->whereIn('key', array_map(fn($entity) => $entity['key'], $this->entities))->delete();
        });
    }
}
