<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class AddMenuItemForShiftManagmentForNewBookingPlugin extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $parent_id = DB::table(Config::get('menu.table'))->where('key_name', 'staffs')->select('id')->first()->id;
        DB::table(Config::get('menu.table'))->insert([
            'key_name' => 'manage_shifts-under-staff-for-new-booking-plugin',
            'title' => 'Shifts Management',
            'title_vars' => '',
            'url' => '/v2/owner/shifts',
            'quicklink' => 1,
            'class' => 'poll-box',
            'icon-class' => '',
            'plugin' => \App\Utils\PluginUtil::NEW_BOOKING_PLUGIN,
            'check_plugins' => \App\Utils\PluginUtil::NEW_BOOKING_PLUGIN,
            'showif' => \App\Utils\PermissionUtil::MANAGE_ATTENDANCE_SETTINGS.','.\App\Utils\PermissionUtil::Edit_General_Settings,
            'and_showif' => '',
            'parent_id' => $parent_id,
            'parent_only' => null,
            'display_order' => 40,
            'version' => \App\Utils\MenuItemUtil::VERSION_TYPE_COMMON
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table(Config::get('menu.table'))->where('key_name', 'manage_shifts-under-staff-for-new-booking-plugin')->delete();
    }
}
