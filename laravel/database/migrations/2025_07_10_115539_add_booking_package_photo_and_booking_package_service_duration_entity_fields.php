<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use App\Utils\PluginUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\EntityFieldRelationUtil;

class AddBookingPackagePhotoAndBookingPackageServiceDurationEntityFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')->insert([
            [
                'key' => 'booking_packages.photo',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Photo',
                'db_name' => 'photo',
                'default_value' => null,
                'is_filter' => 0,
                'validation_rules' => 'nullable|file|max:5120|mimes:jpg,png,jpeg',
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE,
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'description' => 'Booking Package Photo',
                'options' => json_encode([
                    "attributes" => [
                        "fieldKey" => "booking_packages.photo",
                        "entityKey" => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY
                    ],
                ])
            ],
            [
                'key' => 'booking_package_services.duration',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Duration',
                'db_name' => 'duration',
                'default_value' => null,
                'is_filter' => 0,
                'validation_rules' => 'required|numeric',
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_SERVICE_ENTITY_KEY,
                'description' => 'Booking Package Service Duration',
                'options' => null
            ],
        ]);

        DB::table('field_relations')->insert(
            [
                'name' => 'booking_package_photo',
                'type' => EntityFieldRelationUtil::HAS_MANY_TYPE,
                'field_key' => 'booking_packages.photo',
                'reference_field_key' => 'entity_attachments.entity_id',
                'reference_entity_key' => EntityKeyTypesUtil::ENTITY_ATTACHMENT,
                'entity' => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                'conditions' => json_encode([
                    "entity_key" => [
                        [
                            "value" => EntityKeyTypesUtil::BOOKING_PACKAGE_ENTITY_KEY,
                            "operator" => "EQUAL",
                        ],
                    ],
                    "deleted_at" => [
                        [
                            "value" => null,
                            "operator" => "IS_NULL",
                        ],
                    ],
                ]),
            ]
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')
            ->whereIn('key', ['booking_packages.photo', 'booking_package_services.duration'])
            ->delete();
        DB::table('field_relations')
            ->where('name', '=', 'booking_package_photo')
            ->delete();
    }
}
