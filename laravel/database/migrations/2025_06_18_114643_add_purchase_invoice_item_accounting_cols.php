<?php

use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class addPurchaseInvoiceItemAccountingCols extends Migration
{   
    private $fields;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

            $this->fields = [
                [
                    'key' => 'purchase_order_items.sales_cost_account_id',
                    'entity' => EntityKeyTypesUtil::PURCHASE_INVOICE_ITEM,
                    'plugin_id' => '',
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'label' => 'Sales Cost Account Id',
                    'db_name' => 'sales_cost_account_id',
                    'on_import' => 0,
                    'on_export' => 0,
                ],
                [
                    'key' => 'purchase_order_items.cost_center_id',
                    'entity' => EntityKeyTypesUtil::PURCHASE_INVOICE_ITEM,
                    'plugin_id' => '',
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                    'label' => 'Cost Center Id',
                    'db_name' => 'cost_center_id',
                    'on_import' => 0,
                    'on_export' => 0,
                ]
            ];

        foreach ($this->fields as $field) {
            DB::table('entity_fields')->insert($field);
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')->whereIn('key', [
            'purchase_order_items.sales_cost_account_id',
            'purchase_order_items.cost_center_id'
        ])->delete();

    }
}
