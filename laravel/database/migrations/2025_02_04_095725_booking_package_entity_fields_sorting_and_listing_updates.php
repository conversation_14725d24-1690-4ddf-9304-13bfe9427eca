<?php

use App\Database\EntityMigration;
use Illuminate\Support\Facades\DB;
use App\Repositories\ProductRepository;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ProductOption;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class BookingPackageEntityFieldsSortingAndListingUpdates extends EntityMigration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')->where('key', 'booking_packages.id')->update([
            'is_listing' => 0,
            'is_listing_mobile' => 0,
        ]);
        DB::table('entity_fields')->where('key', 'booking_package_services.service_id')->update([
            'options' => json_encode([
                'property' => 'name',
                'field_type' => 'auto_suggest',
                'identifier' => 'id',
                "find_method" =>  ['name' => 'getSearchAutoSuggestActviceServices'],
                'target_class' => ProductRepository::class,
                'auto_suggest_url' => '/v2/owner/products/ajaxProductsFilter?active_only=1&services_only=1&term=d&_type=query&q=__q__',
                "is_single" => true,
                'entityKey' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                "template" => ProductOption::class,
                "empty_option" => false,
                "conditions" => [
                    [
                        'col' => 'status',
                        'value' => ProductStatusUtil::STATUS_ACTIVE,
                    ],
                    [
                        'col' => 'type',
                        'value' => 2,
                    ]
                ],
                'filter' => [
                    'order' => 1,
                    "label" => "Filter by Service",
                ],
                "default" => [
                    "cols" => [
                        "id",
                        "name",
                        "product_code",
                    ],
                    "search" => [
                        [
                            "col" => "name",
                            "operator" => "like"
                        ],
                        [
                            "col" => "product_code",
                            "operator" => "like"
                        ]
                    ],
                ]
            ])
        ]);
        DB::table('entity_fields')->whereIn('key', ['booking_packages.name', 'booking_packages.created'])->update([
            'is_sorting' => 1
        ]);
    }

    /**
     * Reverse the migrations.s
     *
     * @return void
     */
    public function down()
    {
        //
    }
}