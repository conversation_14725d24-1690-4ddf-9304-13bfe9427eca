<?php

use App\Database\EntityMigration;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use App\Utils\EntityFieldRelationUtil;

class CreateServiceAssginedStaffsEntity extends EntityMigration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entities')->insert([
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::SERVICE_ENTITY_KEY,
                'connection' => 'currentSite',
                'plugin_id' => PluginUtil::ProductsPlugin,
                'label' => 'Service',
                'db_name' => 'products',
                'name_field' => 'name',
                'type' => 'form',
                'extend' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                'extend_options' => json_encode([
                    'conditions' => [
                        'type' => [
                            [
                                'value' => 2,
                                'operator' => 'EQUAL'
                            ]
                        ]
                    ]
                ])
            ],
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY,
                'connection' => 'currentSite',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Assigned Employees',
                'db_name' => 'staff_services',
                'name_field' => 'id',
                'type' => 'subform',
                'extend' => null,
                'extend_options' => null
            ]
        ]);

        DB::table('entity_fields')->insert([
            [
                'key' => 'service_assigned_staffs.id',
                'entity' => EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'ID',
                'db_name' => 'id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'service_assigned_staffs.service_id',
                'entity' => EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Service',
                'db_name' => 'service_id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ],
            [
                'key' => 'service_assigned_staffs.staff_id',
                'entity' => EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Staff',
                'db_name' => 'staff_id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => 'required',
                'options' => json_encode([
                    "filter" => [
                        "size" => 3,
                        "label" => "Search by Employee Name, Code, Email Address or Mobile",
                        "order" => 1
                    ],
                    "property" => "name",
                    "template" => "Izam\\Daftra\\Common\\Formatter\\AutoSuggest\\Entity\\StaffOption",
                    "multiple" => true,
                    "field_type" => "auto_suggest",
                    "identifier" => "id",
                    "find_method" => [
                        "name" => "getByEntityKey",
                        "params" => ["staff"]
                    ],
                    "target_class" => "Izam\\Entity\\Repository\\DynamicRepo",
                    "label_generator" => "Izam\\Daftra\\Common\\Formatter\\Staff",
                    "auto_suggest_url" => "/v2/api/entity/staff/filter-auto-suggest?filter[or][id][like]=__q__&filter[or][code][like]=__q__&filter[or][full_name][like]=__q__&filter[or][email_address][like]=__q__&filter[or][mobile][like]=__q__",
                    "option_attributes" => [
                        "data-data" => "Izam\\Daftra\\Common\\Formatter\\StaffAvatar"
                    ]
                ]),
            ],
            [
                'key' => 'service_assigned_staffs.branch_id',
                'entity' => EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_BRANCH,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Branch',
                'db_name' => 'branch_id',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ]
        ]);

        DB::table('entity_fields')->insert([
            [
                'key' => 'service.service_assigned_staffs',
                'entity' => EntityKeyTypesUtil::SERVICE_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Assigned Employees',
                'db_name' => 'staff_services',
                'default_value' => null,
                'is_filter' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'is_listing' => 1,
                'is_listing_mobile' => 0,
                'allowed_values' => null,
                'validation_rules' => null,
                "options" => null,
            ]
        ]);

        DB::table('field_relations')->insert(
            [
                [
                    'name' => 'service_assigned_staffs',
                    'entity' => EntityKeyTypesUtil::SERVICE_ENTITY_KEY,
                    'field_key' => 'service.service_assigned_staffs',
                    'type' => EntityFieldRelationUtil::HAS_MANY_TYPE,
                    'reference_entity_key' => EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY,
                    'reference_field_key' => 'service_assigned_staffs.service_id'
                ],
                [
                    'name' => 'staff',
                    'entity' => EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY,
                    'field_key' => 'service_assigned_staffs.staff_id',
                    'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
                    'reference_entity_key' => EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                    'reference_field_key' => 'staffs.id'
                ]
            ]
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entities')->whereIn('key', [EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY, EntityKeyTypesUtil::SERVICE_ENTITY_KEY])->delete();
        DB::table('entity_fields')->whereIn('entity', [EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY, EntityKeyTypesUtil::SERVICE_ENTITY_KEY])->delete();
        DB::table('field_relations')->whereIn('entity', [EntityKeyTypesUtil::SERVICE_ASSIGNED_STAFFS_ENTITY_KEY, EntityKeyTypesUtil::SERVICE_ENTITY_KEY])->delete();
    }
}
