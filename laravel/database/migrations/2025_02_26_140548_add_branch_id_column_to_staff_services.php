<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddBranchIdColumnToStaffServices extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `staff_services` ADD COLUMN `branch_id` int(11) DEFAULT '1'",
                "add_branch_id_column_to_staff_services",
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `staff_services` DROP COLUMN `branch_id`;",
                "remove_branch_id_column_from_staff_services",
            ),
        ];
    }
}
