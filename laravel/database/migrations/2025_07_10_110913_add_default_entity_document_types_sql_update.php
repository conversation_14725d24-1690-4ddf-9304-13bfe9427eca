<?php

use App\Entities\SqlUpdateEntity;
use App\Utils\PluginUtil;
use Izam\Daftra\Common\Models\SqlUpdate;

class AddDefaultEntityDocumentTypesSqlUpdate extends \App\Database\SqlUpdateMigration
{
    public function __construct()
    {
        if ('daftra' == Domain_Name_Only)
        {
           $sqlUpdate= new SqlUpdateEntity(
               "INSERT INTO entity_document_types (id, entity_key, name, is_required, is_expirable, display_order)
                       VALUES
                        (1,'staff' , 'بطاقة الهوية' , 1 , 1 , 1),
                        (2,'staff' , 'جواز السفر' , 1 , 1 , 2),
                        (3,'staff' , 'تصريح الإقامة / التأشيرة' , 1 , 0 , 3),
                        (4,'staff' , 'رخصة القيادة' , 1 , 1 , 4),
                        (5,'staff' , 'الشهادة الأكاديمية / المؤهل الدراسي' , 1 , 0 , 5),
                        (6,'staff' , 'شهادة اللياقة الطبية' , 1 , 0 , 6),
                        (7,'staff' , 'صورة شخصية (نمط الهوية)' , 1 , 0 , 7);",
               'add_default_entity_document_types' , PluginUtil::HRM_PLUGIN
           );
        }else{
            $sqlUpdate= new SqlUpdateEntity(
                "INSERT INTO entity_document_types (entity_key, name, is_required, is_expirable, display_order)
                       VALUES
                        (1,'staff' ,'Official ID', 1 , 1 , 1),
                        (2,'staff' ,'Passport', 1 , 1 , 2),
                        (3,'staff' ,'Residency Permit / Visa', 1 , 0 , 3),
                        (4,'staff' ,'Driver’s License', 1 , 1 , 4),
                        (5,'staff' ,'Academic Certificate / Degree', 1 , 0 , 5),
                        (6,'staff' ,'Medical Fitness Certificate', 1 , 0 , 6),
                        (7,'staff' , 'Personal Photo (ID-style)' , 1 , 0 , 7 );",
                'add_default_entity_document_types' , PluginUtil::HRM_PLUGIN
            );
        }
        echo Domain_Name_Only;
        $this->query = [
            $sqlUpdate
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "DELETE FROM `entity_document_types` WHERE `id` <= 7;",
                'add_default_entity_document_types_reverse',
                PluginUtil::HRM_PLUGIN
            ),
        ];
    }
}
