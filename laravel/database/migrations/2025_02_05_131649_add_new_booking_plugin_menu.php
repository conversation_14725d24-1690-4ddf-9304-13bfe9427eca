<?php

use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Izam\Daftra\Common\Utils\PermissionUtil;

class AddNewBookingPluginMenu extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table(Config::get('menu.table'))->insert([
            [
                'key_name' => 'new_booking',
                'title' => 'New Booking',
                'title_vars' => '',
                'url' => '#',
                'quicklink' => 1,
                'class' => 'calendar',
                'icon-class' => '',
                'plugin' => PluginUtil::NEW_BOOKING_PLUGIN,
                'check_plugins' => '',
                'showif' => '',
                'and_showif' => '',
                'parent_id' => 0,
                'parent_only' => null,
                'display_order' => 201,
                'version' => \App\Utils\MenuItemUtil::VERSION_TYPE_COMMON
            ]
        ]);
        $parent_id = DB::getPdo()->lastInsertId();
        DB::table(Config::get('menu.table'))->insert([
            [
                'key_name' => 'booking_settings',
                'title' => 'Booking Settings',
                'title_vars' => '',
                'url' => '/v2/owner/booking_settings',
                'quicklink' => 1,
                'class' => 'calendar',
                'icon-class' => '',
                'plugin' => PluginUtil::NEW_BOOKING_PLUGIN,
                'check_plugins' => '',
                'showif' => PermissionUtil::MANAGE_BOOKING_SETTINGS,
                'and_showif' => '',
                'parent_id' => $parent_id,
                'parent_only' => null,
                'display_order' => 11,
                'version' => \App\Utils\MenuItemUtil::VERSION_TYPE_COMMON
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}