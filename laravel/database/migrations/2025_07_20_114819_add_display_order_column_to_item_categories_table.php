<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddDisplayOrderColumnToItemCategoriesTable extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `items_categories` ADD COLUMN `display_order` INT(11) NOT NULL DEFAULT 0 AFTER `category_id`;",
                "add_display_order_column_to_item_categories_table"
            ),
        ];
        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `items_categories` DROP COLUMN `display_order`;",
                "drop_display_order_column_from_item_categories_table"
            ),
        ];
    }
}
