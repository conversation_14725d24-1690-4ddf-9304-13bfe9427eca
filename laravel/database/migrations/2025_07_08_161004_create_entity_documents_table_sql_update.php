<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class CreateEntityDocumentsTableSqlUpdate extends SqlUpdateMigration
{

    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "CREATE TABLE IF NOT EXISTS `entity_documents` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `entity_key` VARCHAR(255) NOT NULL,
                        `entity_id` int(11) NOT NULL,
                        `name` varchar(255) NOT NULL,
                        `expiry_date` DATE NULL,
                        `entity_document_type_id` int(11) default false,
                        `notes` TEXT NULL DEFAULT NULL ,
                        `staff_id` int(11) NULL DEFAULT NULL,
                        `created`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX(`entity_id`),
                        INDEX(`entity_key`),
                        FOREIGN KEY entity_document_type_fk (entity_document_type_id) REFERENCES entity_document_types (id) ON DELETE SET NULL,
                        PRIMARY KEY (`id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;",
                'create_entity_documents_table_sql_update',
            ),

        ];
        $this->reverseQuery = [
            new SqlUpdateEntity("DROP TABLE IF EXISTS `entity_document_types`;", "create_entity_documents_table_sql_update_reverse", PluginUtil::HRM_ATTENDANCE_PLUGIN),
        ];
    }
}
