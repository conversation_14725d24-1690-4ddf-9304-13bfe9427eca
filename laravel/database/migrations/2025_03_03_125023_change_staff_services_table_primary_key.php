<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class ChangeStaffServicesTablePrimaryKey extends SqlUpdateMigration
{

    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `staff_services` DROP PRIMARY KEY, ADD PRIMARY KEY (`staff_id`, `service_id`, `branch_id`) USING BTREE;",
                "edit_service_form_primary_key",
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `staff_services` DROP PRIMARY KEY, ADD PRIMARY KEY (`staff_id`, `service_id`) USING BTREE;",
                "edit_service_form_primary_key_reverse",
            ),
        ];
    }
}
