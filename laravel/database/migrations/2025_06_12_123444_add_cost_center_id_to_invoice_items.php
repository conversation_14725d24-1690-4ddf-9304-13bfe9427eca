<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddCostCenterIdToInvoiceItems extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `invoice_items`
                 ADD `cost_center_id` INT DEFAULT NULL AFTER `product_id`",
                'add_cost_center_id_to_invoice_items'
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `invoice_items`
                 DROP COLUMN `cost_center_id`",
                'reverse_add_cost_center_id_to_invoice_items'
            ),
        ];
    }
}