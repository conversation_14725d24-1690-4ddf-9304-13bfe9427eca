<?php
use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;
use Izam\Daftra\Common\Utils\PluginUtil;

class AddUuidToMachinesSqlupdate extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `machines`
                ADD `uuid` VARCHAR(36) NULL AFTER `id`,
                ADD UNIQUE INDEX `idx_machines_uuid` (`uuid`);
                UPDATE `machines` SET `uuid` = UUID() WHERE `uuid` IS NULL;
                ALTER TABLE `machines`
                MODIFY COLUMN `uuid` VARCHAR(36) NOT NULL",
                'add_uuid_to_machines',
                PluginUtil::HRM_ATTENDANCE_PLUGIN
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `machines`
                DROP INDEX `idx_machines_uuid`,
                DROP COLUMN `uuid`",
                'add_uuid_to_machines_reverse',
                PluginUtil::HRM_ATTENDANCE_PLUGIN
            ),
        ];
    }
}
