<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddJournalAccountIdToRequisitionItems extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `requisition_items`
                 ADD `account_id` INT DEFAULT NULL AFTER `product_id`",
                'add_account_id_to_requisition_items'
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `requisition_items`
                 DROP COLUMN `account_id`",
                'revert_add_account_id_to_requisition_items'
            ),
        ];
    }
}