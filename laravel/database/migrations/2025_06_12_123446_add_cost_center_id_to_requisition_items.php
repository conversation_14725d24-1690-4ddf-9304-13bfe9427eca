<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddCostCenterIdToRequisitionItems extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `requisition_items`
                 ADD `cost_center_id` INT DEFAULT NULL AFTER `product_id`",
                'add_cost_center_id_to_requisition_items'
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `requisition_items`
                 DROP COLUMN `cost_center_id`",
                'revert_cost_center_id_from_requisition_items'
            ),
        ];
    }
}