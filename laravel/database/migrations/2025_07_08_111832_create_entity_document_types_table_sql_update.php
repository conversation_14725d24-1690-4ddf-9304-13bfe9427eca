<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class CreateEntityDocumentTypesTableSqlUpdate extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "CREATE TABLE IF NOT EXISTS `entity_document_types` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `entity_key` VARCHAR(255) NOT NULL,
                        `name` varchar(255) NOT NULL,
                        `is_required` boolean default false,
                        `is_expirable` boolean default false,
                        `display_order` int(11) DEFAULT 1,
                        `created`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX `idx_entity_key` (`entity_key`),
                        PRIMARY KEY (`id`)
                    )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;",
                'create_entity_document_types_table_sql_update',
            ),

        ];
        $this->reverseQuery = [
            new SqlUpdateEntity("DROP TABLE IF EXISTS `entity_document_types`;", "create_entity_document_types_table_sql_update_reverse"),
        ];
    }
}
