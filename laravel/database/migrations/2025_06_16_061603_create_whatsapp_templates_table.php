<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWhatsappTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('whatsapp_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('language');
            $table->string('category');
            $table->timestamp('created', 0)->nullable();
            $table->timestamp('modified', 0)->nullable();
        });

        Schema::create('whatsapp_template_components', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('whatsapp_template_id');
            $table->string('type');
            $table->string('text');
            $table->timestamp('created', 0)->nullable();
            $table->timestamp('modified', 0)->nullable();

            $table->foreign('whatsapp_template_id')->references('id')->on('whatsapp_templates')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('whatsapp_template_components');
        Schema::dropIfExists('whatsapp_templates');
    }
}
