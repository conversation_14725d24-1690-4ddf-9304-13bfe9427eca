<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class UpdateQuotationRequestEntityRepoService extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update quotation_request entity with proper repository and service names
        DB::table('entities')
            ->where('key', EntityKeyTypesUtil::QUOTATION_REQUEST)
            ->update([
                'repo_name' => 'QuotationRequestRepository',
                'service_name' => 'QuotationRequestService'
            ]);

        // Add field relation for quotation_request to purchase_request
        DB::table('field_relations')->insert([
            'name' => 'quotation_request_purchase',
            'type' => 'belongsTo',
            'field_key' => 'quotation_requests.purchase_request_id',
            'reference_field_key' => 'purchase_requests.id',
            'reference_entity_key' => 'purchase_request',
            'entity' => 'quotation_request',
            'conditions' => null
        ]);

        // Set purchase_requests.items field on_export to 0
        DB::table('entity_fields')
            ->where('key', 'purchase_requests.items')
            ->where('entity', 'purchase_request')
            ->update(['on_export' => 0]);

        // Set quotation_requests.items field on_export to 0
        DB::table('entity_fields')
            ->where('key', 'quotation_requests.items')
            ->where('entity', 'quotation_request')
            ->update(['on_export' => 0]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert purchase_requests.items field on_export to 1
        DB::table('entity_fields')
            ->where('key', 'purchase_requests.items')
            ->where('entity', 'purchase_request')
            ->update(['on_export' => 1]);

        // Revert quotation_requests.items field on_export to 1
        DB::table('entity_fields')
            ->where('key', 'quotation_requests.items')
            ->where('entity', 'quotation_request')
            ->update(['on_export' => 1]);


        // Remove the field relation
        DB::table('field_relations')
            ->where('name', 'quotation_request_purchase')
            ->delete();

        // Revert the repository and service names to null
        DB::table('entities')
            ->where('key', EntityKeyTypesUtil::QUOTATION_REQUEST)
            ->update([
                'repo_name' => null,
                'service_name' => null
            ]);
    }
}