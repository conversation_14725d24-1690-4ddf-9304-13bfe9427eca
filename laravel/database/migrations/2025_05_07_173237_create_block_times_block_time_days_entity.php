<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ProductOption;
use App\Repositories\StaffRepository;
use Izam\Daftra\Common\Utils\Entity\EntityFieldRelationUtil;

class CreateBlockTimesBlockTimeDaysEntity extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create block_times entity
        DB::table('entities')->insert([
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'connection' => 'currentSite',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Block Times',
                'db_name' => 'block_times',
                'name_field' => 'id',
            ],
        ]);

        // Create block_time_days entity
        DB::table('entities')->insert([
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::BLOCK_TIME_DAY_ENTITY_KEY,
                'connection' => 'currentSite',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Block Time Days',
                'db_name' => 'block_time_days',
                'name_field' => 'id',
            ],
        ]);

        // Create block_times entity fields
        DB::table('entity_fields')->insert([
            [
                'key' => 'block_times.id',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'ID',
                'db_name' => 'id',
                'is_filter' => 0,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => NULL,
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.staff_id',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Staff',
                'db_name' => 'staff_id',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required',
                'description' => NULL,
                'options' => json_encode([
                    "filter" => [
                        "label" => "Filter by Staff",
                        "order" => 1
                    ],
                    "default" => [
                        "cols" => ["id", "name", "email"],
                        "search" => [
                            ["col" => "name", "operator" => "like"],
                            ["col" => "email", "operator" => "like"],
                        ],
                    ],
                    "property" => "name",
                    "entityKey" => EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                    "template" => ProductOption::class,
                    "auto_suggest_url" => '/v2/owner/staff/ajaxStaffFilter?term=d&_type=query&q=__q__',
                    "is_single" => true,
                    "field_type" => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                    "identifier" => "id",
                    "find_method" => [
                        "name" => "getSearchAutoSuggestStaff"
                    ],
                    "empty_option" => false,
                    "target_class" => StaffRepository::class,
                ])
            ],
            [
                'key' => 'block_times.label',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Label',
                'db_name' => 'label',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'nullable|string|max:255',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.start_date',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Start Date',
                'db_name' => 'start_date',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required|date',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.start_time',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TIME_PICKER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Start Time',
                'db_name' => 'start_time',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required|date_format:H:i',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.duration',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Duration (minutes)',
                'db_name' => 'duration',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required|integer|min:1',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.frequency_unit',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Frequency Unit',
                'db_name' => 'frequency_unit',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => json_encode([
                    ['day' => 'Day'],
                    ['week' => 'Week'],
                    ['month' => 'Month']
                ]),
                'default_value' => NULL,
                'validation_rules' => 'required_unless:frequency_interval,0|in:day,week,month',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.frequency_interval',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Frequency Interval',
                'db_name' => 'frequency_interval',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required|integer|min:0',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.weekdays',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_JSON,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Weekdays',
                'db_name' => 'weekdays',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => json_encode([
                    ['monday' => 'Monday'],
                    ['tuesday' => 'Tuesday'],
                    ['wednesday' => 'Wednesday'],
                    ['thursday' => 'Thursday'],
                    ['friday' => 'Friday'],
                    ['saturday' => 'Saturday'],
                    ['sunday' => 'Sunday']
                ]),
                'default_value' => NULL,
                'validation_rules' => 'nullable|array',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.end_date',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'End Date',
                'db_name' => 'end_date',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'date|after:start_date',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.occurrences',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Occurrences',
                'db_name' => 'occurrences',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'integer|min:1',
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_times.notes',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXTAREA,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Notes',
                'db_name' => 'notes',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'nullable|string',
                'description' => NULL,
                'options' => NULL
            ],
        ]);

        // Create block_time_days entity fields
        DB::table('entity_fields')->insert([
            [
                'key' => 'block_time_days.id',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_DAY_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'ID',
                'db_name' => 'id',
                'is_filter' => 0,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => NULL,
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'block_time_days.block_time_id',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_DAY_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Block Time',
                'db_name' => 'block_time_id',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required|exists:block_times,id',
                'description' => NULL,
                'options' => json_encode([
                    "filter" => [
                        "label" => "Filter by Block Time",
                        "order" => 1
                    ],
                    "default" => [
                        "cols" => ["id", "label", "start_date"],
                        "search" => [
                            ["col" => "label", "operator" => "like"],
                            ["col" => "start_date", "operator" => "like"],
                        ],
                    ],
                    "property" => "label",
                    "entityKey" => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                    "template" => ProductOption::class,
                    "auto_suggest_url" => '/v2/owner/block-times/ajaxBlockTimesFilter?term=d&_type=query&q=__q__',
                    "is_single" => true,
                    "field_type" => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                    "identifier" => "id",
                    "empty_option" => false,
                ])
            ],
            [
                'key' => 'block_time_days.block_time_day',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_DAY_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Block Time Day',
                'db_name' => 'block_time_day',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required|date',
                'description' => NULL,
                'options' => NULL
            ],
        ]);

        // Create field relations
        DB::table('field_relations')->insert([
            [
                'name' => 'block_time_staff',
                'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
                'field_key' => 'block_times.staff_id',
                'reference_field_key' => 'staff.id',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                'reference_entity_key' => EntityKeyTypesUtil::STAFF_ENTITY_KEY,
            ],
            [
                'name' => 'block_time_day_block_time',
                'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
                'field_key' => 'block_time_days.block_time_id',
                'reference_field_key' => 'block_times.id',
                'entity' => EntityKeyTypesUtil::BLOCK_TIME_DAY_ENTITY_KEY,
                'reference_entity_key' => EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove field relations
        DB::table('field_relations')
            ->whereIn('name', ['block_time_staff', 'block_time_day_block_time'])
            ->delete();

        // Remove entity fields
        DB::table('entity_fields')
            ->whereIn('entity', [
                EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                EntityKeyTypesUtil::BLOCK_TIME_DAY_ENTITY_KEY
            ])
            ->delete();

        // Remove entities
        DB::table('entities')
            ->whereIn('key', [
                EntityKeyTypesUtil::BLOCK_TIME_ENTITY_KEY,
                EntityKeyTypesUtil::BLOCK_TIME_DAY_ENTITY_KEY
            ])
            ->delete();
    }
}
