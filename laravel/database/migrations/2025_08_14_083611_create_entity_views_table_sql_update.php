<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class CreateEntityViewsTableSqlUpdate extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity("
            CREATE TABLE IF NOT EXISTS `entity_views` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `entity_key` varchar(100) NOT NULL,
                `entity_id` int(11) NOT NULL,
                `staff_id` varchar(100) NOT NULL,
                `read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                INDEX `idx_entity_key_id` (`entity_key`, `entity_id`),
                INDEX `idx_staff_id` (`staff_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
            ", "create_entity_views_table")
        ];

        $this->reverseQuery = [
          new SqlUpdateEntity("DROP TABLE IF EXISTS `entity_views`;", "create_entity_views_table_reverse")
        ];
    }
}
