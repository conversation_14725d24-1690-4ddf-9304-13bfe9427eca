<?php

use App\Utils\PluginUtil;
use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\PermissionUtil;

class NewBookingPlugin extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('plugins')->insert([
            [
                'id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'name' => 'Booking',
                'description' => "Book appointments and services for customers with adjusted shifts for assigned employees. Block appointments that are not automatically available within the employees' shifts. View schedules in daily, weekly, and monthly calendar formats. Manage employee breaks, generate commissions for services performed, and process booking fees through the point-of-sale system.",
                'ar_description' => "حجز المواعيد والخدمات للعملاء مع تعديل الورديات للموظفين المعنيين. حظر المواعيد غير المتاحة تلقائيًا ضمن ورديات الموظفين. عرض الجداول الزمنية بتنسيقات التقويم اليومية والأسبوعية والشهرية. إدارة استراحات الموظفين، إضافة العمولات عن الخدمات التي يتم تنفيذها، ودفع رسوم الحجز من خلال نظام نقاط البيع",
                'active' => 0,
                'display_order' => 20,
                'created' => Carbon::now()->toDateTimeString(),
                'modified' => Carbon::now()->toDateTimeString(),
                'is_external' => 1,
                'beta_version' => 1,
                'group_id' => 4,
                'plugin_key' => 'new_booking'
            ]
        ]);

        DB::table('plugin_dependencies')->insert([
            ['plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'parent_plugin_id' => PluginUtil::SalesPlugin],
            ['plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'parent_plugin_id' => PluginUtil::ClientsPlugin],
            ['plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'parent_plugin_id' => PluginUtil::PosPlugin],
            ['plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'parent_plugin_id' => PluginUtil::StaffPlugin]
        ]);

        DB::table('permissions')->insert([
            [ 'id' => PermissionUtil::ADD_NEW_BOOKING,'permission_name' => 'Add New Booking', 'display_order' => '100', 'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'already_include' => null, 'is_beta' => 1],
            [ 'id' => PermissionUtil::VIEW_ALL_BOOKINGS,'permission_name' => 'View All Bookings', 'display_order' => '100', 'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'already_include' => PermissionUtil::VIEW_HIS_OWN_BOOKINGS, 'is_beta' => 1],
            [ 'id' => PermissionUtil::VIEW_HIS_OWN_BOOKINGS,'permission_name' => 'View His Own Bookings', 'display_order' => '100', 'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'already_include' => null, 'is_beta' => 1],
            [ 'id' => PermissionUtil::EDIT_DELETE_ALL_BOOKINGS,'permission_name' => 'Edit / Delete All Bookings', 'display_order' => '100', 'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'already_include' => PermissionUtil::EDIT_DELETE_HIS_OWN_BOOKINGS, 'is_beta' => 1],
            [ 'id' => PermissionUtil::EDIT_DELETE_HIS_OWN_BOOKINGS,'permission_name' => 'Edit/Delete His own Bookings', 'display_order' => '100', 'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'already_include' => null, 'is_beta' => 1],
            [ 'id' => PermissionUtil::MANAGE_EMPLOYEES_BREAKS,'permission_name' => 'Manage Employees Breaks', 'display_order' => '100', 'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'already_include' => null, 'is_beta' => 1],
            [ 'id' => PermissionUtil::MANAGE_BOOKING_SETTINGS,'permission_name' => 'Manage Booking Settings', 'display_order' => '100', 'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN, 'already_include' => null, 'is_beta' => 1],
        ]
    );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('permissions')->where('plugin_id', PluginUtil::NEW_BOOKING_PLUGIN)->delete();
        DB::table('plugin_dependencies')->where('plugin_id', PluginUtil::NEW_BOOKING_PLUGIN)->delete();
        DB::table('plugins')->where('id', PluginUtil::NEW_BOOKING_PLUGIN)->delete();
    }
}
