<?php

use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class ChangeNewBookingPluginLabel extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('plugins')->where('id', PluginUtil::NEW_BOOKING_PLUGIN)->update(['name' => 'Bookings Management']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
