<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateEntityDocumentsAttachmentsValidationRules extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')
            ->where('key', 'entity_documents.documents')
            ->update([
                'validation_rules' => "required|file|max:25600|safe_file"
            ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')
            ->where('key', 'entity_documents.documents')
            ->update([
                'validation_rules' => "required"
            ]);
    }
}
