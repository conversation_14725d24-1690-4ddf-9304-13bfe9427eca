<?php

use App\Utils\PermissionUtil;
use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddEmployeeDocumentsPermissions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->down();
        DB::table('permissions')->insert(
            [
                [
                    'id' => PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS,
                    'plugin_id' => PluginUtil::StaffPlugin,
                    'display_order' => 902 ,
                    'is_beta' => 1,
                    'permission_name' => 'Manage Employee Documents',
                    'already_include' => PermissionUtil::VIEW_EMPLOYEE_DOCUMENTS .','. PermissionUtil::Staff_View_Staffs
                ],
                [
                    'id' => PermissionUtil::VIEW_EMPLOYEE_DOCUMENTS,
                    'plugin_id' => PluginUtil::StaffPlugin,
                    'display_order' => 903,
                    'is_beta' => 1,
                    'permission_name' => 'View Employee Documents',
                    'already_include' => PermissionUtil::Staff_View_Staffs
                ],

            ]
        );

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('permissions')->whereIn('id',
            [
                PermissionUtil::VIEW_EMPLOYEE_DOCUMENTS,
                PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS
            ]
        )->delete();
    }
}
