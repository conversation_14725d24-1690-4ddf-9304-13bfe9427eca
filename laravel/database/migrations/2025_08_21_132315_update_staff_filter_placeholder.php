<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateStaffFilterPlaceholder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('entity_fields')
            ->whereIn('key', [
                'staffs.id',
                'leave_applications.staff_id',
                'contracts.staff_id',
            ])->update([
                'options->filter->label'=>"Search by Name, Code, E-mail, Department, Branch, country...etc",
                'options->beta_only->filter->label'=>"Search by Name, Code, E-mail, Department, Branch, country...etc",
            ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')
            ->whereIn('key', [
                'staffs.id',
                'leave_applications.staff_id',
                'contracts.staff_id',
            ])->update([
                'options->filter->label'=>"Search by Employee Name, ID or Email Address",
                'options->beta_only->filter->label'=>"Search by Employee Name, ID or Email Address",

            ]);
    }
}
