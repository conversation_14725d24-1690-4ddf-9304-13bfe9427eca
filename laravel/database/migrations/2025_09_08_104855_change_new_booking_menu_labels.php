<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class ChangeNewBookingMenuLabels extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table(Config::get('menu.table'))->where('key_name', 'new_booking')->update([
            'title' => 'Bookings'
        ]);
        DB::table(Config::get('menu.table'))->where('key_name', 'manage_bookings')->update([
            'icon-class' => 'mdi mdi-rocket'
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table(Config::get('menu.table'))->where('key_name', 'new_booking')->update([
            'title' => 'New Booking'
        ]);
        DB::table(Config::get('menu.table'))->where('key_name', 'manage_bookings')->update([
            'icon-class' => ''
        ]);
    }
}
