<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ProductOption;
use App\Repositories\ProductRepository;
use Izam\Booking\Utils\ServiceFormsFillingTimeUtil;
use Izam\Daftra\Common\Utils\Entity\EntityFieldRelationUtil;

class CreateServiceFormsEntityAndEntityFieldsMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        DB::table('entities')->insert([
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'connection' => 'currentSite',
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Service Forms',
                'db_name' => 'service_forms',
                'name_field' => 'id',
            ],
        ]);

        DB::table('entity_fields')->insert([
            [
                'key' => 'service_forms.id',
                'entity' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'ID',
                'db_name' => 'id',
                'is_filter' => 0,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 0,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => NULL,
                'description' => NULL,
                'options' => NULL
            ],
            [
                'key' => 'service_forms.service_id',
                'entity' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Service',
                'db_name' => 'service_id',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 1,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => NULL,
                'default_value' => NULL,
                'validation_rules' => 'required|unique',
                'description' => NULL,
                'options' => json_encode(
                    [
                        "filter" => [
                            "label" => "Filter by Service",
                            "order" => 1,
                            "size" => 3,
                        ],
                        "default" => [
                            "cols" => ["id", "name", "product_code"],
                            "search" => [
                                ["col" => "name", "operator" => "like"],
                                ["col" => "product_code", "operator" => "like"],
                            ],
                        ],
                        "sort" => [
                            "key" => "service_form_product.name",
                            "label" => "Name",
                            "order" => 2
                        ],
                        "property" => "name",
                        "entityKey" => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                        "template" => ProductOption::class,
                        "auto_suggest_url" => '/v2/owner/products/ajaxProductsFilter?active_only=1&services_only=1&term=d&_type=query&q=__q__',
                        "is_single" => true,
                        "conditions" => [
                            ["col" => "status", "value" => 0],
                            ["col" => "type", "value" => 2],
                        ],
                        "field_type" => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_SUGGEST,
                        "identifier" => "id",
                        "find_method" => [
                            "name" => "getSearchAutoSuggestActviceServices"
                        ],
                        "empty_option" => false,
                        "target_class" => ProductRepository::class,
                    ]
                )
            ],
            [
                'key' => 'service_forms.filling_time',
                'entity' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Form Filling Time',
                'db_name' => 'filling_time',
                'is_filter' => 1,
                'is_listing' => 1,
                'is_sorting' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 1,
                'on_export' => 1,
                'allowed_values' => json_encode(ServiceFormsFillingTimeUtil::getList()),
                'default_value' => ServiceFormsFillingTimeUtil::getDefaultValueOption(),
                'validation_rules' => 'required',
                'description' => null,
                'options' => json_encode([
                    "util_class" => ServiceFormsFillingTimeUtil::class,
                    "filter" => [
                        "order" => 2,
                        "size" => 3,
                    ],
                ]),
            ],
            [
                'key' => 'service_forms.created',
                'entity' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TIMESTAMP,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Date of Creation',
                'db_name' => 'created',
                'is_filter' => 0,
                'is_listing' => 0,
                'is_sorting' => 1,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 1,
                'allowed_values' => null,
                'default_value' => null,
                'validation_rules' => null,
                'description' => null,
                'options' => null,
            ],
            [
                'key' => 'service_forms.modified',
                'entity' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TIMESTAMP,
                'plugin_id' => PluginUtil::NEW_BOOKING_PLUGIN,
                'label' => 'Date of Modification',
                'db_name' => 'modified',
                'is_filter' => 0,
                'is_listing' => 0,
                'is_sorting' => 0,
                'on_create' => 0,
                'on_update' => 0,
                'on_import' => 0,
                'on_export' => 1,
                'allowed_values' => null,
                'default_value' => null,
                'validation_rules' => null,
                'description' => null,
                'options' => null,
            ],
        ]);

        DB::table('field_relations')->insert([
            [
                'name' => 'service_form_product',
                'type' => EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE,
                'field_key' => 'service_forms.service_id',
                'reference_field_key' => 'products.id',
                'entity' => EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY,
                'reference_entity_key' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
            ],
        ]);

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entities')
            ->where('key', EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY)
            ->delete();

        DB::table('entity_fields')
            ->where('entity', EntityKeyTypesUtil::SERVICE_FORM_ENTITY_KEY)
            ->delete();

        DB::table('field_relations')
            ->where('name', 'service_form_product')
            ->delete();
    }
}
