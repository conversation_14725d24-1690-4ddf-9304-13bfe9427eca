<?php

use App\Utils\EntityFieldRelationUtil;
use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateEntityDocumentsEntityMappings extends Migration
{
    private $entities;
    private $fields;
    private $relations;


    public function __construct()
    {
        $this->entities = [
            [
                'primary_key' => 'id',
                'key' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'connection' => 'currentSite',
                'plugin_id' => null,
                'label' => 'Documents',
                'db_name' => 'entity_documents',
                'name_field' => 'name',
                'type' => 'form'
            ],
        ];

        $this->fields = [
            [
                'key' => 'entity_documents.id',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_PRIMARY_KEY,
                'plugin_id' => null ,
                'label' => 'ID',
                'db_name' => 'id',
                'default_value' => null,
                'is_filter' => '0',
                'order_index' => 0,
                'validation_rules' => null,
                'on_create' => '1',
                'on_update' => '1',
                'on_import' => '0',
                'on_export' => '1',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_documents.name',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT,
                'plugin_id' => null,
                'label' => 'Name',
                'is_sorting' => 1,
                'db_name' => 'name',
                'default_value' => null,
                'is_filter' => '1',
                'validation_rules' => 'required',
                'on_create' => '1',
                'order_index' => 0,
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '1',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_documents.entity_key',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN,
                'plugin_id' => null,
                'label' => 'Entity',
                'db_name' => 'entity_key',
                'order_index' => 0,
                'default_value' => null,
                'is_filter' => '1',
                'validation_rules' => 'required',
                'on_create' => '1',
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '1',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_documents.entity_id',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_HIDDEN,
                'plugin_id' => null,
                'label' => 'Entity',
                'db_name' => 'entity_id',
                'default_value' =>  null,
                'is_filter' => '1',
                'order_index' => 0,
                'validation_rules' => null,
                'on_create' => '1',
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '1',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_documents.expiry_date',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE,
                'plugin_id' => null,
                'label' => 'Expiry Date',
                'db_name' => 'expiry_date',
                'default_value' => null,
                'is_filter' => '1',
                'validation_rules' => 'sometimes|nullable|date',
                'on_create' => '1',
                'order_index' => 0,
                'on_update' => '1',
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_documents.entity_document_type_id',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'plugin_id' => null,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN,
                'validation_rules' => 'required',
                'label' => 'Document Type',
                'db_name' => 'entity_document_type_id',
                'is_listing' => 0,
                'is_filter' => 1,
                'is_sorting' => 0,
                'on_create' => 1,
                'on_update' => 1,
                'on_import' => 0,
                'on_export' => 0,
                'default_value' => null,
                'allowed_values' => null,
                'options' => null,
            ],[
                'key' => 'entity_documents.documents',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE,
                'plugin_id' => null,
                'label' => 'Documents',
                'db_name' => 'documents',
                'default_value' => null,
                'is_filter' => '0',
                'validation_rules' => 'required',
                'on_create' => '1',
                'on_update' => '1',
                'on_import' => '1',
                'order_index' => 0,
                'on_export' => 0,
                'is_listing' => 0,
                'allowed_values' => null,
                'options' => json_encode([
                    "attributes" => [
                        "fieldKey" => "entity_documents.documents",
                        "entityKey" => EntityKeyTypesUtil::ENTITY_DOCUMENT
                    ]
                ])
            ],
            [
                'key' => 'entity_documents.notes',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXTAREA,
                'plugin_id' => null,
                'label' => 'Notes',
                'db_name' => 'notes',
                'default_value' => null,
                'is_filter' => '0',
                'validation_rules' => 'nullable',
                'on_create' => '1',
                'on_update' => '1',
                'order_index' => 0,
                'on_import' => '1',
                'on_export' => '1',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],[
                'key' => 'entity_documents.staff_id',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF,
                'plugin_id' => null,
                'label' => 'Uploaded By',
                'db_name' => 'staff_id',
                'default_value' => null,
                'is_filter' => '0',
                'validation_rules' => null,
                'on_create' => '1',
                'on_update' =>  0,
                'order_index' => 0,
                'on_import' =>  0,
                'on_export' =>  0,
                'is_listing' => 0,
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_documents.created',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER,
                'plugin_id' =>null,
                'label' => 'Date Of Creation',
                'is_sorting' => 1,
                'db_name' => 'created',
                'default_value' => null,
                'is_filter' => '0',
                'validation_rules' => null,
                'on_create' => '0',
                'on_update' => '0',
                'on_import' => '0',
                'order_index' => 0,
                'on_export' => '0',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],
            [
                'key' => 'entity_documents.modified',
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER,
                'plugin_id' =>null,
                'label' => 'Modified At',
                'db_name' => 'modified',
                'default_value' => null,
                'is_filter' => '0',
                'validation_rules' => null,
                'on_create' => '0',
                'on_update' => '0',
                'order_index' => 0,
                'on_import' => '0',
                'on_export' => '0',
                'is_listing' => '0',
                'allowed_values' => null,
                'options' => null
            ],
        ];

        $this->relations = [
            [
                'name' => 'documents',
                'type' => EntityFieldRelationUtil::HAS_MANY_TYPE,
                'field_key' => 'entity_documents.documents',
                'reference_field_key' => 'entity_attachments.entity_id',
                'reference_entity_key' => EntityKeyTypesUtil::ENTITY_ATTACHMENT,
                'entity' => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                'conditions' => json_encode([
                    "entity_key" => [
                        [
                            "value" => EntityKeyTypesUtil::ENTITY_DOCUMENT,
                            "operator" => "EQUAL",
                        ],
                    ],
                    "deleted_at" => [
                        [
                            "value" => null,
                            "operator" => "IS_NULL",
                        ],
                    ],
                ]),
            ],
        ];

    }


    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::transaction(function () {
            foreach ($this->entities as $entity) {
                DB::table('entities')->insert($entity);
            }
            foreach ($this->fields as $field) {
                DB::table('entity_fields')->insert($field);
            }
            foreach ($this->relations as $relation) {
                DB::table('field_relations')->insert($relation);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::transaction(function () {
            DB::table('field_relations')->whereIn('field_key', array_map(fn($relation) => $relation['field_key'], $this->relations))->delete();
            DB::table('entity_fields')->whereIn('key', array_map(fn($field) => $field['key'], $this->fields))->delete();
            DB::table('entities')->whereIn('key', array_map(fn($entity) => $entity['key'], $this->entities))->delete();
        });
    }
}
