<?php

use App\Database\SqlUpdateMigration;
use App\Entities\SqlUpdateEntity;

class AddSalesJournalAccountIdToInvoiceItems extends SqlUpdateMigration
{
    public function __construct()
    {
        $this->query = [
            new SqlUpdateEntity(
                "ALTER TABLE `invoice_items`
                 ADD `sales_account_id` INT DEFAULT NULL AFTER `product_id`",
                'add_sales_account_id_to_invoice_items_global'
            ),
        ];

        $this->reverseQuery = [
            new SqlUpdateEntity(
                "ALTER TABLE `invoice_items`
                 DROP COLUMN `sales_account_id`",
                'revert_sales_account_id_from_invoice_items'
            ),
        ];
    }
}