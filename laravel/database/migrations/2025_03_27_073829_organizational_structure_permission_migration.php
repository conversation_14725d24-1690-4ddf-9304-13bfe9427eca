<?php

use Illuminate\Database\Migrations\Migration;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Utils\MenuItemUtil;

class OrganizationalStructurePermissionMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        DB::table('permissions')->insert(
            [
                [
                    'id' => PermissionUtil::VIEW_ONLY_DEPARTMENT_CHART,
                    'plugin_id' => PluginUtil::HRM_PLUGIN,
                    'display_order' => 110,
                    'permission_name' => 'View Only Department Chart',
                    'already_include' => NULL,
                    'is_beta' => 1,
                ],
            ]
        );

        DB::table(Config::get('menu.table'))->insert(
            [
                [
                    'key_name' => 'staff-organizational-chart',
                    'title' => 'Organizational Chart',
                    'title_vars' => NULL,
                    'url' => '/v2/owner/staff/organizational-chart',
                    'quicklink' => 1,
                    'class' => NULL,
                    'icon-class' => NULL,
                    'plugin' => PluginUtil::HRM_PLUGIN,
                    'check_plugins' => NULL,
                    'showif' => PermissionUtil::VIEW_ONLY_DEPARTMENT_CHART.','.PermissionUtil::Staff_Edit_Staffs.','.PermissionUtil::MANAGE_HRM_SYSTEM,
                    'and_showif' => NULL,
                    'parent_id' => 113,
                    'parent_only' => null,
                    'display_order' => 100,
                    'version' => MenuItemUtil::VERSION_TYPE_COMMON
                ],
            ]
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('permissions')
            ->where('id', PermissionUtil::VIEW_ONLY_DEPARTMENT_CHART)
            ->delete();

        DB::table(Config::get('menu.table'))
            ->where('key_name', 'staff-organizational-chart')
            ->delete();
    }
}
