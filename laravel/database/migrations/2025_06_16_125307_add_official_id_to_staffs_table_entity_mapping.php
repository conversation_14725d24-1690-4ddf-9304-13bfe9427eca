<?php

use App\Utils\EntityFieldUtil;
use App\Utils\EntityKeyTypesUtil;
use App\Utils\PluginUtil;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddOfficialIdToStaffsTableEntityMapping extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

            DB::table('entity_fields')->insert([
                [
                    'key' => 'staffs.official_id',
                    'entity' => EntityKeyTypesUtil::STAFF_ENTITY_KEY,
                    'field_type' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT,
                    'plugin_id' => PluginUtil::StaffPlugin,
                    'label' => 'Official ID Number',
                    'db_name' => 'official_id',
                    'default_value' => null,
                    'is_filter' => '0',
                    'validation_rules' => 'nullable|regex:/^[A-Za-z0-9-]+$/',
                    'on_create' => '1',
                    'on_update' => '1',
                    'on_import' => '0',
                    'on_export' => '1',
                    'is_listing' => '0',
                    'allowed_values' => null
                ],


            ]);

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('entity_fields')->where('key', 'staffs.official_id')->delete();

    }
}
