{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "conflict": {"symfony/translation": "v5.3.8", "symfony/mailer": "v5.3.8", "symfony/notifier": "v5.3.8", "symfony/messenger": "v5.3.8"}, "require": {"php": "^8.0.0", "ext-json": "*", "ext-memcached": "*", "ext-pdo": "*", "aws/aws-sdk-php": "3.214.*", "daftra/validator": "*@dev", "doctrine/dbal": "^2.9", "facade/ignition": "^2.5", "fakerphp/faker": "^1.20", "fideloper/proxy": "^4.4", "google/auth": "^1.26", "izam/attachment": "*@dev", "izam/autonumber": "*@dev", "izam/aws": "*@dev", "izam/booking": "*@dev", "izam/block-time": "*@dev", "izam/daftra-activity-log": "*@dev", "izam/daftra-app-manager": "*@dev", "izam/daftra-common": "*@dev", "izam/daftra-diagnostics": "*@dev", "izam/daftra-dynamic-permissions": "*@dev", "izam/daftra-expense": "*@dev", "izam/daftra-invoice": "*@dev", "izam/daftra-journal": "*@dev", "izam/daftra-js-injector": "*@dev", "izam/daftra-jwt": "*@dev", "izam/daftra-product": "*@dev", "izam/daftra-purchase-order": "*@dev", "izam/daftra-staff": "*@dev", "izam/daftra-store": "*@dev", "izam/database": "*@dev", "izam/queue-visualiser": "*@dev", "izam/dynamic-list": "*@dev", "izam/forms": "*@dev", "izam/izam-recurring-profiles": "*@dev", "izam/izam-view": "*@dev", "izam/limitation": "*@dev", "izam/navigation": "*@dev", "izam/payslip": "*@dev", "izam/portal-auth": "*@dev", "izam/queue": "*@dev", "izam/rental": "*@dev", "izam/template": "*@dev", "laravel/framework": "^8.5", "laravel/passport": "^10.1", "laravel/tinker": "^2.5", "laravelcollective/html": "^6.2", "lcobucci/jwt": "4.1.5", "league/flysystem-aws-s3-v3": "1.0.29", "maatwebsite/excel": "^3.1", "num-num/ubl-invoice": "^1.13", "php-amqplib/php-amqplib": "^2.11", "predis/predis": "^2.0", "rollbar/rollbar-laravel": "^7.0", "symfony/options-resolver": "5.4.x-dev", "izam/izam-tafket": "*@dev", "izam/logging": "*@dev", "izam/system-industry": "*@dev", "tomorrow-ideas/plaid-sdk-php": "^1.0", "izam/daftra-portal": "*@dev", "izam/stock-request": "*@dev", "izam/zkt-attendance-machine": "*@dev", "izam/hrm": "*@dev", "izam/daftra-cache": "*@dev", "izam/fcm": "*@dev", "izam/daftra-manufacturing-order": "*@dev", "izam/daftra-client": "*@dev", "jurosh/pdf-merge": "^2.1", "izam/auto-reminder": "*@dev", "izam/daftra-advance-payment": "*@dev", "izam/item-permission": "*@dev", "izam/daftra-e-invoice": "*@dev"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.2", "filp/whoops": "^2.0", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "repositories": [{"type": "path", "url": "../izam-packages/izam-system-industry"}, {"type": "path", "url": "../izam-packages/izam-template"}, {"type": "path", "url": "../izam-packages/daftra-journal"}, {"type": "path", "url": "../izam-packages/daftra-diagnostics"}, {"type": "path", "url": "../izam-packages/izam-database"}, {"type": "path", "url": "../izam-packages/queue-visualiser"}, {"type": "path", "url": "../izam-packages/daftra-js-injector"}, {"type": "path", "url": "../izam-packages/daftra-jwt"}, {"type": "path", "url": "../izam-packages/daftra-staff"}, {"type": "path", "url": "../izam-packages/daftra-product"}, {"type": "path", "url": "../izam-packages/daftra-store"}, {"type": "path", "url": "../izam-packages/daftra-purchase-order"}, {"type": "path", "url": "../izam-packages/queue"}, {"type": "path", "url": "../izam-packages/daftra-common"}, {"type": "path", "url": "../izam-packages/izam-forms"}, {"type": "path", "url": "../izam-packages/izam-limitation"}, {"type": "path", "url": "../izam-packages/daftra-dynamic-permissions"}, {"type": "path", "url": "../izam-packages/daftra-portal"}, {"type": "path", "url": "../izam-packages/daftra-journal"}, {"type": "path", "url": "../izam-packages/izam-autonumber"}, {"type": "path", "url": "../izam-packages/daftra-activity-log"}, {"type": "path", "url": "../izam-packages/daftra-expense"}, {"type": "path", "url": "../izam-packages/daftra-invoice"}, {"type": "path", "url": "../izam-packages/izam-view"}, {"type": "path", "url": "../izam-packages/izam-attachment"}, {"type": "path", "url": "../izam-packages/izam-rental"}, {"type": "path", "url": "../izam-packages/izam-payslip"}, {"type": "path", "url": "../izam-packages/izam-entity"}, {"type": "path", "url": "../izam-packages/izam-currency"}, {"type": "path", "url": "../izam-packages/daftra-app-manager"}, {"type": "path", "url": "../izam-packages/daftra-validator"}, {"type": "path", "url": "../izam-packages/izam-aws"}, {"type": "path", "url": "../izam-packages/izam-dynamic-list"}, {"type": "path", "url": "../izam-packages/izam-recurring-profiles"}, {"type": "path", "url": "../izam-packages/izam-branch"}, {"type": "path", "url": "../izam-packages/izam-navigation"}, {"type": "path", "url": "../izam-packages/izam-tafket"}, {"type": "path", "url": "../izam-packages/izam-logging"}, {"type": "path", "url": "../izam-packages/izam-portal-auth"}, {"type": "path", "url": "../izam-packages/daftra-cache"}, {"type": "path", "url": "../izam-packages/izam-stock-request"}, {"type": "path", "url": "../izam-packages/izam-zkt-attendance-machine"}, {"type": "path", "url": "../izam-packages/izam-hrm"}, {"type": "path", "url": "../izam-packages/izam-fcm"}, {"type": "path", "url": "../izam-packages/daftra-manufacturing-order"}, {"type": "path", "url": "../izam-packages/daftra-client"}, {"type": "path", "url": "../izam-packages/izam-auto-reminder"}, {"type": "path", "url": "../izam-packages/izam-item-permission"}, {"type": "path", "url": "../izam-packages/daftra-e-invoice"}, {"type": "path", "url": "../izam-packages/daftra-advance-payment"}, {"type": "path", "url": "../izam-packages/izam-booking"}, {"type": "path", "url": "../izam-packages/izam-block-time"}], "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": ["rollbar/rollbar-laravel", "laravel/telescope"]}}, "autoload": {"files": ["app/Helpers/helpers.php", "app/Modules/LocalEntity/Helpers/le_functions.php", "app/Modules/Template/Helpers/template_functions.php", "app/Modules/Rental/Helpers/rental_functions.php"], "psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories", "app/Modules"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}