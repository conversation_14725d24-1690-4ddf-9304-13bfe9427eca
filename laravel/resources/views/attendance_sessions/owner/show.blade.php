
@php
$sourceTypeList = \App\Utils\AttendanceSessionSourceTypeUtil::getSourceTypeList();
$logRoute = route('owner.attendance_logs.index', ['session_id' => $record->id]);
$staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
@endphp
@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}" />
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/index/index.min.css?v={{CSS_VERSION}}" />
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/activity_log/activity_log.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/attendance/attendance.min.css?v={{CSS_VERSION}}"/>
@endpush
@section('content')
    @if(isset($signs_count) || isset($invalid_staff_ids))
        @if($sign_count)
            <div class="alert alert-icon alert-bordered alert-dismissible border-success alert-success">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="d-flex">
                    <div class="alert-icon">
                        <i class="far fa-lg fa-check-circle text-success mr-3"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-0"><strong>({{$sign_count}}) {{__t('Attendance log(s) created successfully')}}.</strong></p>
                    </div>
                </div>
            </div>
        @endif
        @if(!empty($invalid_staff_ids))
            <div class="alert alert-icon alert-bordered alert-dismissible border-danger alert-danger">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="d-flex">
                    <div class="alert-icon">
                        <i class="far fa-lg fa-exclamation-circle text-danger mr-3"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-0"><strong>({{count($invalid_staff_ids)}}) {{__t('Attendance log(s) failed')}}.</strong></p>
                    </div>
                    <div class="alert-collapse-btn">
                        <button type="button" class="btn btn-sm alert-link p-0" data-toggle="collapse" data-target="#errorsCollapse" aria-expanded="false" aria-controls="errorsCollapse">
                            {{sprintf(__t('View %s'), __t('Logs'))}}
                        </button>
                    </div>
                </div>
                <div class="collapse mt-1" id="errorsCollapse">
                    <div class="d-flex">
                        <div class="alert-icon">
                            <i class="far fa-lg fa-exclamation-circle text-danger mr-3 invisible"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="simplebar-scroll" data-simplebar data-simplebar-auto-hide="false">
                                @foreach($invalid_staff_ids as $employeeId )
                                    <p class="mb-0">
                                        {{sprintf(__t("Couldn't pull the signs for “#%s“ in machine as there’s error in mapping"), $employeeId)}}
                                    </p>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @endif
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-6">
                    <div class="pages-head-title">

                        <h2>
                            #{{$record->id}}
                            <span class="status status-{{$record->status == 'open' ? 'active' : 'inactive'}}"><i
                                        class="cir"></i>{{$statusList[$record->status]}}</span>
                        </h2>
                    </div>
                </div>
                <div class="col-sm-6">

                    <div class="pages-head-btns action-buttons">
                        @if($record->status === \App\Utils\AttendanceSessionStatusUtil::ATTENDANCE_SESSION_STATUS_OPEN)
                        <a href="#" data-toggle="modal" data-target="#takeAttendance"  class="btn btn-icon btn-success responsive">
                            <i class="mdi mdi-gesture-tap"></i>
                            <span>{{__t('Sign')}}</span>
                        </a>
                        <a href="{{route('owner.attendance_sessions.close', ['attendance_session' => $record->id])}}" class="btn btn-icon btn-danger responsive">
                            <i class="mdi mdi-close-circle-outline"></i>
                            <span>{{__t('Close')}}</span>
                        </a>
                        @endif
                        @include('partials.show.record_nav', ['removeWrapperDiv' => true,'url' => 'owner.attendance_sessions.show', 'name' => 'attendance_session'])
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">

                @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Attendance Session'), 'href' => route('owner.attendance_sessions.destroy', ['attendance_session' => $record->id])])

            </div>
        </div>
        <div class="card-body p-1 p-lg-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">

                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab"
                           aria-controls="details" aria-selected="true">{{__t('Details')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="session-logs-tab" data-toggle="tab" href="#session-logs" role="tab"
                           aria-controls="session-logs" aria-selected="true">{{__t('Session Log')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab"
                           aria-controls="activity-log" aria-selected="false">{{__t('Activity Log')}}</a>
                    </li>
                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">
                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel" aria-labelledby="details">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{sprintf(__t('%s Details'), __t('Attendance Session'))}}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">


                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Session ID")}}}</p>
                                        <p>#{{$record->id}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Session Time")}}}</p>
                                        <p>{{formatForView($record->open_time, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC) .(!empty($record->close_time) ? ' - '.formatForView($record->close_time, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC) : '--')}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Source Type")}}}</p>
                                        <p>{{$record->source_name . (!empty($record->source_method) ? ', ' .$record->source_method : '')}}</p>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Signs Count")}}}</p>
                                        <p>{{$record->signs_count}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Status")}}}</p>
                                        <p>{{$statusList[$record->status]}}</p>
                                    </div>
                                </div>

                                <div class="w-100"></div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">

                        <input type="text" value="attendance_session"

                               id="entity_key" hidden/>
                        <input type="hidden" value="{{$record->id}}" id="entity_id"/>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{__t('Activity Log')}}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                            @include('partials.layout.loader')
                            <!-- activity Log will be here -->
                        </div>
                    </div>
                    <div class="tab-pane p-3 fade" id="session-logs" role="tabpanel" aria-labelledby="session-logs-tab">
                        @include('partials.layout.loader')
                    </div>


                </div>
            </div>

        </div>
    </div>

    @include('partials.modals.create_delete_modal')


    <div class="modal fade" id="takeAttendance" tabindex="-1" role="dialog" aria-labelledby="takeAttendanceLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="takeAttendanceLabel">{{ sprintf(__t('Attendance Session %s'), format_date(convertFromUtc($record->open_time)->toDateTimeString())) }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div id="takemy-body" class="modal-body pt-4 pb-4">
                    <form method="post" action="{{route('owner.attendance_logs.sign', ['session_id' => $record->id])}}">
                        @csrf
                        <input type="hidden" name="source_name" value="{{getLoggedUserDisplayName()}}" />
                        <input type="hidden" name="source_method" value="">
                        <input type="hidden" name="source_id" value="{{getAuthOwner('staff_id')}}">
                        <input type="hidden" name="source_type" value="{{\App\Utils\AttendanceLogSourceTypeUtil::ATTENDANCE_LOG_SOURCE_TYPE_SUPERVISOR}}">
                        <input type="hidden" name="session_id" value="{{$record->id}}">
                    <div class='d-flex align-items-center flex-column justify-content-center'>
                        <div class="col-md-12 form-group">
                            <label for="emp-name">{{__t('Employee')}}</label><span class="required">*</span>
                            <select name="staff_id" class="profile-dropdown custom-select" id="emp-name" data-placeholder="{{ $staffInputPlaceholder }}"  required>
                            </select>
                        </div>
                        <div class="col-md-12 mb-3">
                            <div id="session-time-wrap" class="session-time-wrap d-none">

                            </div>
                        </div>
                        <button type="submit" class="btn btn-lg btn-dark-blue pr-4 pl-4 btn-sign disabled" disabled>{{__t('Sign')}}</button>
                    </div>
                    </form>
                </div>
                <div class="modal-footer last-sign d-none" data-simplebar data-simplebar-auto-hide="false">
                    <div id="lastSigns">
                    @include('partials.form.input_template', [
                        'label' => __t('Signs'),
                        'inputClass' => 'last-sign-input form-control bg-white',
                        'div' => ' form-group  w-100',
                        'name' => 'lastSignInput',
                        'value' => '1/4/2019 - 08:01:12',
                        'attributes' => ['disabled' => 'disabled'],
                        'type' => 'text',
                    ])
                    {{-- use same partial with empty label for reset of signs --}}
                </div>
                </div>
            </div>
        </div>
    </div>


    <!-- end page contents -->
@endsection
@push('scripts')
    <script>
        sessionId = "{{$record->id}}";
        logsRoute = "{{ $logRoute }}"
    </script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_countdown.js"></script>
    <script src="{{asset('/js/attendance/attendance-log-sessions/start-session.js')}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>

    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/index/index.js"></script>
@endpush
