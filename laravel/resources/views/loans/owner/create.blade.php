@extends('layouts.owner')
@php
        if (isset($form_record->id) && !isset($isClone)){
            $isUpdate = true;
            $formRoute = route('owner.loans.update', ['loan' => $form_record->id]);
        } else {
            $formRoute = route('owner.loans.store');
            $isUpdate = false;
        }
        if (!empty(old()) && !isset($staffOptionsAttributes)) {
            $staffOptionsAttributes = old('staffOptionsAttributes') ?? null;
        }
        if (!empty(old()) && !isset($staffOptions)) {
            $staffOptions = old('staffOptions') ?? null;
        }
        $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
@endphp
<style>
    .rtl .text-big-number.form-control {
        min-height: 56px;
    }
</style>
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css"/>
    @endpush

    {!! Form::model($form_record,['class' => 'form-validate','method' => 'post','url' => $formRoute]) !!}
    @if($isUpdate)
        {{ Form::hidden('id', $form_record->id) }}
        {{ Form::hidden('_method', 'PUT') }}
    @endif
    @include('partials.form.page-head', [
     'actions' => [
        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
        ['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attr' => ['key' => 'value']]
     ]])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Details'), __t('Loan'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @php
                    $jsFormats = getDateFormats('new_js');
                    $siteFormat = getCurrentSite('date_format');
                    $siteFormat = strtoupper($jsFormats[$siteFormat]);
                @endphp

                @include('partials.form.input_template',[
                    'label' => __t('Employee'),
                    'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                    'div' => 'col-md-6 form-group',
                    'name' => 'staff_id',
                    'attributes' => ['select2-dimmed' => ($form_record && !isset($isClone)) ? 'true' : 'false', 'placeholder' => $staffInputPlaceholder],
                    'type' => 'selectStaff',
                    'optionsAttributes' => $staffOptionsAttributes ?? null,
                    'options' => $staffOptions ?? null,
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Application Date'),
                    'inputClass' => 'form-control',
                    'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'value' => isset($form_record->application_date) ? formatForView($form_record->application_date, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('application_date'), \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                    'name' => 'application_date',
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])
                <div class="col-md-6 form-group">
                    <label for="amount" class="control-label">{{__t('Amount')}}<span class="required">*</span>
                    </label>
                    <div class="input-group form-group mb-0">
                        @include('partials.form.input_template', [
                                'label' => '',
                                'div' => '',
                                'type' => 'text',
                                'name' => 'amount',
                                'value' => ($form_record)? $form_record->amount:null,
                                'inputClass' => 'form-control text-big-number input-group-prepend',
                                'attributes'=> [
                                    'id'=>'amount',
                                    'required' => 'required',
                                    'step' => 'any',
                                    'min' => 1,
                                    'placeholder' => '0.00',
                                    'data-parsley-type' => "number",
                                    'data-parsley-errors-container' => '#ammount-errors',
                                    'step' => '0.0000000001'
                                ],
                            ])
                        @include('partials.form.input_template', [
                                'label' => '',
                                'div' => 'currency select-stretched form-group mb-0',
                                'type' => 'select',
                                'name' => 'currency',
                                'value' => ($form_record)? $form_record->currency:$related_form_data['defaultCurrencyCode'],
                                'options'=> $related_form_data['currencies'],
                                'attributes'=> ['placeholder' => __t('Please Select'), 'required' => 'required', 'id' => 'currency','data-parsley-errors-container' => '#ammount-errors'],
                            ])
                    </div>
                    <div id="ammount-errors" class="invalid-messages"></div>
                </div>

                <div class="col-md-6 form-group">
                    <label for="amount" class="control-label">{{__t('Installment Amount')}}<span
                            class="required">*</span>
                    </label>
                    <div class="input-group form-group mb-0">
                        @include('partials.form.input_template', [
                                'label' => '',
                                'div' => '',
                                'type' => 'text',
                                'name' => 'installment_amount',
                                'value' => ($form_record)? $form_record->installment_amount:null,
                                'inputClass' => 'form-control text-big-number input-group-prepend',
                                'attributes'=> [
                                    'id'=>'installment-amount',
                                    'step' => 'any',
                                    'min' => 1,
                                    'required' => true,
                                    'placeholder' => '0.00',
                                    'data-parsley-lte' => '#amount',
                                    'data-parsley-type' => "number",
                                    'data-parsley-error-message' => __t('The installment amount should be less than or equal the Loan Amount'),
                                    'data-parsley-errors-container' => '#installment_amount_errors',
                                    'step' => '0.0000000001'
                                ],
                            ])
                        <div class="currency select-stretched form-group mb-0 border px-3 d-flex align-items-center justify-content-center border-input text-inactive">
                            <span id="currency_code" class="font-weight-bold">CURRENCY</span>
                        </div>
                        <div class="currency select-stretched form-group mb-0 border px-3 d-flex align-items-center justify-content-center border-input text-inactive">
                            <div class="text-center">
                                <span class="d-block font-weight-bold">{{__t("Installment Counts")}}</span>
                                <span id="installment-count" class="font-weight-bold">0</span>
                            </div>
                        </div>
                    </div>
                    <div id="installment_amount_errors" class="invalid-messages"></div>
                </div>

                @include('partials.form.input_template', [
                    'options' => $related_form_data['periods'],
                    'label' => __t('Period of Installments'),
                    'inputClass' => 'select form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'installment_period',
                    'type' => 'select',
                    'attributes' => ['required' => 'required', 'placeholder' => sprintf(__t("Select %s"), __t('Period'))],
                ])
                @include('partials.form.input_template', [
                    'label' => __t('Installment Start Date'),
                    'inputClass' => 'form-control',
                    'attributes' => ['required' => 'required','placeholder' => $siteFormat],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'name' => 'installment_start_date',
                    'value' => isset($form_record->installment_start_date) ? formatForView($form_record->installment_start_date, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('installment_start_date'), \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])
                @include('partials.form.input_template', [
                    'options' => $related_form_data['treasuries'],
                    'label' => __t('Treasury Account'),
                    'inputClass' => 'select form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'treasury_id',
                    'type' => 'select',
                    'attributes' => ['required' => 'required','placeholder' => sprintf(__t("Select %s"), __t('Treasury'))],
                ])

                <div class="col-md-6 form-group form-group-icon reverse">
                    <div class="position-relative">
                        <div class="input-group">
                            <label>&nbsp; </label>
                            <div class="input-group-prepend w-100">
                                <div class="input-group-text w-100 text-wrap">
                                    @include('partials.form.input_template', [
                                        'label' => __t('Repayment From Payslip'),
                                        'labelClass' => 'custom-control-label',
                                        'inputClass' => 'custom-control-input',
                                        'div' => 'custom-control custom-checkbox p-0 w-100',
                                        'name' => 'repay_from_payslip',
                                        'checked' => ($form_record ? $form_record->repay_from_payslip : false),
                                        'type' => 'checkbox'
                                     ])
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @include('partials.form.input_template', [
                    'label' => __t('Note'),
                    'inputClass' => 'form-control',
                    'attributes' => ['rows' => 3],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'name' => 'notes',
                    'type' => 'textArea'
                ])
                @if(isset($form_record->tags))
                    @foreach($form_record->tags as $tag)
                        @php
                            $tags[] = $tag->name;
                            $tagsOptions[] = [
                                "text" => $tag->name,
                                "value" => $tag->name
                            ];
                        @endphp
                    @endforeach
                @endif
                @include('partials.form.input_template', [
                    'label' => __t('Tags'),
                    'inputClass' => 'form-control custom-select',
                    'div' => 'col-md-6 form-group',
                    'name' => 'tags[]',
                    'type' => 'native_select',
                    'value' => $tags ?? [],
                    'options' => $tagsOptions ?? [],
                    'attributes' => [
                        'multiple' => true,
                        'data-tags-input' => "true",
                        'placeholder' => __t("Tags")
                    ]
                ])
                <div class="col-md-6"></div>
            </div>
        </div>
    </div>
    {{csrf_field()}}
    {!! Form::close() !!}
    @push('scripts')
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
        <script src="{{asset('js/loans/form.js')}}"></script>
        <script>
            $(document).ready(function() {
                var oldTags = <?= json_encode($tags ?? []) ?>;
                var tagsSelect2Options = {
                    theme: 'bootstrap4',
                    width: '100%',
                    tags: true,
                    placeholder: function () {
                        return $(this).data('placeholder');
                    },
                    minimumResultsForSearch: 0,
                };
                if ($('html').is(':lang(ara)')) {
                    tagsSelect2Options['language'] = "ar";
                }
                $.ajax({
                    url: '<?php echo getCakeURL(array('controller' => 'tags', 'action' => 'get_tags')) ?>',
                    method: 'get',
                    dataType: 'json',
                    success: function(data) {
                        console.log($('[data-tags-input]'));
                        if (data && data.tags && data.tags.length) {
                            data.tags.forEach(function(tag) {
                                if(!oldTags.includes(tag)){
                                    var newOption = new Option(tag, tag);
                                    $('[data-tags-input]').append(newOption);
                                }
                            })
                        }
                    },
                    complete: function(){
                        $('[data-tags-input]').select2(tagsSelect2Options);
                    }
                })
            });
        </script>
    @endpush
@endsection
