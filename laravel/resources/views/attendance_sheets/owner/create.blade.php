@extends('layouts.owner')
@php
$staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
@endphp
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}"/>
    @endpush
    {!! Form::model($form_record,['class' => 'form-validate','method' => 'post','url' => route('owner.attendance_sheets.store')]) !!}
    @include('partials.form.page-head', [
     'actions' => [
        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
        ['title' => 'Create', 'type' => 'submit', 'name' => '', 'class' => 'init-page-loader btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attr' => ['key' => 'value']]
     ]])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{__t('Attendance Sheet Details')}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @php
                    $jsFormats = getDateFormats('new_js');
                    $siteFormat = getCurrentSite('date_format');
                    $siteFormat = strtoupper($jsFormats[$siteFormat]);
                    $excludedStaffOptionsAttributes = old('excludedStaffOptionsAttributes') ?? null;
                    $excludedStaffOptions = old('excludedStaffOptions') ?? null;
                @endphp
                @include('partials.form.input_template', [
                    'label' => __t('From'),
                    'inputClass' => 'form-control',
                    'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'name' => 'from_date',
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])
                @include('partials.form.input_template', [
                    'label' => __t('To'),
                    'inputClass' => 'form-control',
                    'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'name' => 'to_date',
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])

                <div class="col-md-12">
                    <hr class="pt-2 pb-2 mb-1">
                </div>

                <div class="col-md-12 form-group">
                    <label>{{__t('Choose Criteria')}}</label>
                </div>

                <div class="col-md-6 form-group order-6 order-lg-1">
                    <div class="position-relative form-group">
                        <div class="input-group">
                            <div class="input-group-prepend w-100">
                                <div class="input-group-text w-100">
                                    @include('partials.form.input_template', [
                                        'label' => __t('Rule Selection'),
                                        'labelClass' => 'custom-control-label',
                                        'inputClass' => 'custom-control-input',
                                        'div' => 'custom-control custom-checkbox p-0 w-100',
                                        'name' => 'criteria',
                                        'id' => 'rule_selection',
                                        'value' => 'rule_selection',
                                        'attributes' => ['onchange' => 'handleCriteria(this);'],
                                        'type' => 'radio',
                                        'checked' => 1,
                                     ])
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="rule-inputs">
                        @if (\App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::BranchesPlugin))
                            @include('partials.form.input_template', [
                                'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Branches')) , 'multiple'=>true],
                                'options' => $related_form_data['branches'],
                                'label' => __t('Branch'),
                                'inputClass' => 'select form-control',
                                'div' => 'form-group',
                                'name' => 'branches[]',
                                'type' => 'select'
                            ])
                        @endif
                        @include('partials.form.input_template', [
                            'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Departments')) , 'multiple'=>true ],
                            'options' => $related_form_data['departments'],
                            'label' => __t('Department'),
                            'inputClass' => 'select form-control',
                            'div' => 'form-group',
                            'name' => 'departments[]',
                            'type' => 'select'
                        ])
                        @include('partials.form.input_template', [
                            'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Designations')) , 'multiple'=>true ],
                            'options' => $related_form_data['designations'],
                            'label' => __t('Designation'),
                            'inputClass' => 'select form-control',
                            'div' => 'form-group',
                            'name' => 'designations[]',
                            'type' => 'select'
                        ])
                        @include('partials.form.input_template', [
                            'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Shifts')) , 'multiple'=>true ],
                            'options' => $related_form_data['shifts'],
                            'label' => __t('Shift'),
                            'inputClass' => 'select form-control',
                            'div' => 'form-group mb-0',
                            'name' => 'shifts[]',
                            'type' => 'select'
                        ])
                        @include('partials.form.input_template',[
                           'label' => __t('Excluded Employees'),
                           'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                           'div' => 'form-group',
                           'name' => 'exclude_criteria[]',
                           'attributes' => [ 'placeholder' => $staffInputPlaceholder, 'multiple' => 'multiple', 'disabled' => 'disabled'],
                           'type' => 'selectStaff',
                           'optionsAttributes' => $excludedStaffOptionsAttributes,
                           'options' => $excludedStaffOptions,
                        ])
                    </div>

                </div>

                <div class="col-md-6 form-group order-1 order-lg-6">
                    <div class="position-relative form-group">
                        <div class="input-group">
                            <div class="input-group-prepend w-100">
                                <div class="input-group-text w-100">
                                    @include('partials.form.input_template', [
                                        'label' => __t('Employees Selection'),
                                        'labelClass' => 'custom-control-label',
                                        'inputClass' => 'custom-control-input',
                                        'div' => 'custom-control custom-checkbox p-0 w-100',
                                        'name' => 'criteria',
                                        'attributes' => ['onchange' => 'handleCriteria(this);'],
                                        'id' => 'emp_selection',
                                        'value' => 'emp_selection',
                                        'type' => 'radio',
                                        'checked' => 0,
                                     ])
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="emp-inputs" class="disabled">

                        @include('partials.form.input_template',[
                            'label' => __t('Employees'),
                            'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                            'div' => 'form-group',
                            'name' => 'employees[]',
                            'attributes' => [ 'placeholder' => $staffInputPlaceholder, 'multiple' => 'multiple', 'disabled' => 'disabled'],
                            'type' => 'selectStaff',
                            'options' => $staffOptions ?? []
                        ])
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{csrf_field()}}
    {!! Form::close() !!}
    @push('scripts')
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
        <script src="{{asset('js/attendance/attendance-day/create.js')}}"></script>
    @endpush
@endsection
