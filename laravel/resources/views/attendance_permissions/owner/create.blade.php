@extends('layouts.owner')
@php
    $actions = [
        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
    ];
    if (isset($form_record->id)){
        $isUpdate = true;
        $formRoute = route('owner.attendance_permissions.update', ['attendance_permission' => $form_record->id]);
        $actions[] = ['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attr' => ['key' => 'value']];
    } else {
        $formRoute = route('owner.attendance_permissions.store');
        $isUpdate = false;
        $actions[] = [
            'title' => 'Save',
            'template' => 'multiple',
            'type' => 'button',
            'name' => '',
            'class' => 'btn-icon btn-success ml-sm-2',
            'value' => '',
            'icon' => '<i class="mdi mdi-content-save-outline"></i>',
            'id' => 'saveBtn',
            'attr' => ['key' => 'value'],
            'actions' => [
                [
                    'title' => __t('Save & Add New'),
                    'url' => 'javascript:void(0);',
                    'attributes' => [
                        'id' => 'saveAndAddBtn'
                    ]
                ]
            ]
        ];
    }
    $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
@endphp
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}"/>

    @endpush
    {!! Form::model($form_record,['id' => 'mainForm', 'class' => 'form-validate','method' => 'post','url' => $formRoute]) !!}
    @if($isUpdate)
        {{ Form::hidden('id', $form_record->id) }}
        {{ Form::hidden('_method', 'PUT') }}
    @endif
    @include('partials.form.page-head', [
     'actions' => $actions
    ])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Information'), __t('Attendance Permission'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @include('partials.form.input_template',[
                    'label' => __t('Employee'),
                    'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                    'div' => 'col-md-6 form-group',
                    'name' => 'staff_id',
                    'id' => 'staff_id',
                    'attributes' => ['placeholder' =>  $staffInputPlaceholder,'required' => true],
                    'type' => 'selectStaff',
                    'options' => isset($staffOptions) ? $staffOptions : [],
                    'value' => isset($form_record->staff_id) ? $form_record->staff_id: old('staff_id', ''),
                ])
                <div class="col-md-6 form-group form-group-icon reverse">
                    <label for="single-date" class="control-label">{{__t('Date')}}:</label>
                    <span class="required">*</span>
                    <div class="input-group mob-block">
                        <div>
                            <div class="input-group-prepend dropdown single-range-dropdown">
                                <button class="btn dropdown-toggle single-range-btn" style="height: 38px;" type="button" data-toggle="dropdown"
                                        aria-haspopup="true" aria-expanded="false"
                                        data-value="{{ ( old() && ( !isset(old()['to_date']) || !old()['to_date']) ) || (isset($form_record->from_date) && $form_record->from_date == $form_record->to_date)  ? 'single' : 'range'}}">
                                    {{( old() &&  ( !isset(old()['to_date']) || !old()['to_date']) ) || (isset($form_record->from_date) && $form_record->from_date == $form_record->to_date)  ? __t('Single') : __t('Range')}} <span class="caret"></span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" data-value="single" href="javascript:void(0);">{{__t('Single')}}</a>
                                    <a class="dropdown-item" data-value="range" href="javascript:void(0);">{{__t('Range')}}</a>
                                </div>
                            </div>
                            <div class="invalid-message filled backend-error"> <span></span> </div>
                        </div>
                        @include('partials.form.input_template', [
                            'label' => __t(''),
                            'inputClass' => 'form-control',
                            'name' => 'from_date',
                            'div' => 'form-group mb-0 position-relative single_date',
                            'type' => 'date',
                            'value' => isset($form_record->from_date) ? formatForView($form_record->from_date, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : null,
                            'attributes' => ['data-parsley-errors-container' => '#date-errors', 'required' => 'required', 'id' => 'from_date', 'placeholder' => __t('From Date')],
                            'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                        ])
                        @include('partials.form.input_template', [
                            'label' => __t(''),
                            'inputClass' => 'form-control ',
                            'div' => 'form-group mb-0 d-znone position-relative single_date_2',
                            'name' => 'to_date',
                            'attributes' => ['data-parsley-errors-container' => '#date-errors', 'id' => 'to_date', 'placeholder' => __t('To Date')],
                            'value' => isset($form_record->to_date) ? formatForView($form_record->to_date, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : null,
                            'type' => 'date',
                            'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                        ])
                    </div>
                    <div id="date-errors" class="overflow-hidden" style="height: 30px"></div>
                </div>
                <div class="col-md-6">
                    @include('partials.form.input_template', [
                        'options' => $related_form_data['leave_type_list'],
                        'label' => __t('Type'),
                        'attributes' => ['required' => 'required', 'onchange' => 'handleTypes(this);','disableAlphabeticSorting'=>true, 'id' => 'permission-type'],
                        'inputClass' => 'form-control',
                        'div' => 'form-group',
                        'name' => 'type',
                        'type' => 'select'
                    ])
                    @include('partials.form.input_template', [
                        'options' => $related_form_data['attendance_permission_leave_type_relations'],
                        'label' => __t('Leave Type'),
                        'attributes' => ['placeholder' => __t("Please select"), 'required' => 'required'],
                        'inputClass' => 'form-control',
                        'div' => 'form-group leave_type',
                        'name' => 'leave_type_id',
                        'type' => 'select'
                    ])
                    @include('partials.form.input_template', [
                        'label' => __t('Late Time'),
                        'inputClass' => 'form-control input-minutes',
                        'div' => 'form-group form-group-placeholder late_time d-none',
                        'name' => 'late_time',
                        'type' => 'text',
                        'icon' => '<small class="input-placeholder">'. __t('minutes') .'</small>',
                        'attributes' => ['min' => '0'],
                        'value' => $form_record->late_time ?? 0
                    ])
                    @include('partials.form.input_template', [
                        'label' => __t('Early Time'),
                        'inputClass' => 'form-control input-minutes',
                        'div' => 'form-group form-group-placeholder early_time d-none',
                        'name' => 'early_time',
                        'type' => 'text',
                        'icon' => '<small class="input-placeholder">'. __t('minutes') .'</small>',
                        'attributes' => ['min' => '0'],
                        'value' => $form_record->early_time ?? 0
                    ])
                </div>
                @include('partials.form.input_template', [
                    'label' => __t('Note'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'note',
                    'type' => 'textArea',
                    'attributes' => ['placeholder' => __t('Enter Note Text Here'), 'rows' => '5', 'style' => 'height:120px']
                ])
                @include('partials.form.input_template', [
                    'label' => __t('Date of Application'),
                    'inputClass' => 'form-control',
                    'attributes' => ['placeholder' => __t('Date of Application')],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'name' => 'application_date',
                    'type' => 'date',
                    'value' => isset($form_record->application_date) ? formatForView($form_record->application_date, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : null,
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])

                @php
                    $shift_type_attr = [
                        'options' => \App\Utils\MultipleShiftTypeUtil::getTypes(),
                        'label' => __t('Shift Type'),
                        'inputClass' => 'select form-control',
                        'div' => 'col-md-6 form-group',
                        'name' => 'shift_type',
                        'type' => 'select',
                        'attributes' => [
                            'required' => 'required'
                        ]
                    ];

                    $showDiv = false;
                    $multi_shift_enabled = \App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift');
                    if($multi_shift_enabled) {
                        $showDiv = true;
                    } elseif ($isUpdate && !$multi_shift_enabled) {
                        $showDiv = true;
                        $shift_type_attr['attributes']['select2-dimmed'] = 'true';
                    }
                @endphp
                @if ($showDiv)
                    @include('partials.form.input_template', $shift_type_attr)
                @endif
                <div class="col-md-6 form-group">
                    @include('partials.new_s3_file_uploader', [
                        'recordAttachments' =>  $form_record->attachments ?? old('attachments') ?? [],
                        'name' => 'attachments',
                        'entityKey' => 'attendance_permission',
                        'fieldKey' => 'attendance_permissions.attachment',
                        'options' => [
                            'multiple' => true,
                            'sortable' => true,
                            'mimes' => []
                        ]
                    ])
                </div>
            </div>
        </div>
    </div>
    {{csrf_field()}}
    {!! Form::close() !!}
    @push('scripts')
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_inputmask.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
        <script src="{{asset('/js/attendance/attendance-permissions/attendance-permissions-form.js')}}"></script>
    @endpush
@endsection
