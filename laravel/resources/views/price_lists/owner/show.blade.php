@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css"/>
@endpush
@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-9">
                    <div class="pages-head-title">
                        <h2 class="d-inline-flex mw-100 align-items-center">
                            @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::Add_Edit_Price_List))
                            <span class="mw-100 overflow-hidden d-inline-flex align-items-center">
                                <input type="text" class="form-control editable-text editable-title" id="editable-title" size="1" value="{{$record->name}}" data-autosize-input='{ "space": 10 }'>
                                <label for="editable-title" class="editable-text-icon"><i class="fas fa-pen"></i></label>
                            </span>
                            @else
                                {{$record->name}}
                            @endif
                            @if($record->status)
                                <span class="status status-active"><i class="cir"></i>{{__t('Active')}}</span>
                            @else
                                <span class="status status-inactive"><i class="cir"></i>{{__t('Inactive')}}</span>
                            @endif
                        </h2>
                    </div>
                    <small class="text-danger"><span id="editable-title-error"></span></small>
                </div>
                <div class="col-sm-3">
                    <div class="action-buttons">
                        <div class="btn-group" role="group" aria-label="padination">
                            @include('partials.show.record_nav', ['url' => 'owner.price_lists.show', 'name' => 'price_list'])
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="card border-white mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-multi-line btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::Add_Edit_Price_List))
                <a data-toggle="modal" data-target="#addItemModal" href="#" class="btn btn-light">
                    <i class="fas fa-plus mr-1"></i>{{__t('Add Item')}}
                </a>
                <a href="{{route('owner.import', ['entity_id' => $record->id, 'entity_key' => \App\Utils\EntityKeyTypesUtil::PRICE_LIST_ITEMS, 'update_existing' => 1])}}" class="btn btn-light">
                    <i class="fas fa-file-import mr-1"></i> {{__t('Import')}}
                </a>
                @endif
                <a href="{{route('owner.viewExport', ['from_entity' => 'price_list', 'entity' => 'price_list_items', 'filters' => ['price_list_id' => $record->id]])}}"
                   class="btn btn-light" target="_blank">
                    <i class="fas fa-file-export mr-1"></i> {{__t('Export')}}
                </a>
                @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::Add_Edit_Price_List))
                <a href="{{route('owner.price_lists.clone', ['price_list' => $record->id])}}" class="btn btn-light" target="_blank">
                    <i class="fas fa-copy mr-1"></i> {{__t('Clone')}}
                </a>
                @endif
                <a id='pdf_report' href="{{getCakeURL(['controller' => 'reports', 'action' => 'report/price_list.pdf', '?' => ['group_price_id' => $record->id, 'show_report'=>1]])}}"
                   class="btn btn-light" target="_blank">
                    <i class="fas fa-file-pdf mr-1"></i> {{__t('PDF')}}
                </a>
                @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::Delete_Price_List))
                    @include('partials.modals.create_delete_modal', [
                        'btn' => true,
                        'label' => __t('Price List'),
                        'href' => route('owner.price_lists.destroy', ['price_list' => $record->id])
                        ])
                @endif
                @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::Add_Edit_Price_List))
                    @if($record->status)
                        <a href="{{route('owner.price_lists.deactivate', ['price_list' => $record->id])}}"
                           class="btn btn-light"><i class="fas fa-comment-alt-edit mr-1"></i>{{__t('Mark as Inactive')}}</a>
                    @else
                        <a href="{{route('owner.price_lists.activate', ['price_list' => $record->id])}}"
                           class="btn btn-light"><i class="fas fa-comment-alt-edit mr-1"></i>{{__t('Mark as Active')}}</a>
                    @endif
                @endif

                <a data-toggle="modal" data-target="#activityLogModal" href="#" class="btn btn-light">
                    <i class="fas fa mr-1"></i>{{__t('Activity Log')}}
                </a>
                @include('partials.templates.printables_dropdown', ['view_templates' => $view_templates, 'id' => $record->id])
            </div>
        </div>
        <div class="card-body p-3">
            @php $resetRoute = '"'.route('owner.price_list_items.index', ['price_list_id' => $record->id, 'reset' => 1]).'"';  @endphp
            @include('partials.load_grid', [
                'disableLoadGridSorting' => true,
                'gridTitle' => '',
                'gridUrl' => route('owner.price_list_items.index', ['price_list_id' => $record->id]),
                'autoLoad' => true,
                'afterLoad' => [
                    'initPdfUrlListener()',
                    'initProductSearch();',
                    'initSelect2Filters();',
                    'updateResetRoute('.$resetRoute.');'
                ]
            ])
        </div>
    </div>

    @include('partials.modals.create_delete_modal')


    <!-- Start of add item popup -->
    <div class="modal fade" id="addItemModal" tabindex="-1" role="dialog" aria-labelledby="addItemModalLabel"
         style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header w-100 pl-4">
                    <h5 class="modal-title fs-14 lh-25" id="addItemModalLabel">{{__t('Add Item to the List')}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div id="popupInputDiv" class="form-group">
                    <div class="modal-body px-4">
                        @include('partials.form.input_template', [
                            'id' => 'popup_itemSelect',
                            'label' => __t('Item'),
                            'name' => 'item',
                            'inputClass' => 'form-control',
                            'div' => 'form-group',
                            'type' => 'select',
                            'options'=> [],
                            'attributes'=> ['disableAlphabeticSorting' => 'true','placeholder' => __t('Select Item'), 'required' => 'required'],
                        ])
                        @include('partials.form.input_template', [
                            'id' => 'popup_price',
                            'label' => __t('Selling Price'),
                            'name' => 'selling_price',
                            'inputClass' => 'form-control',
                            'div' => 'form-group',
                            'type' => 'number',
                            'attributes' => ['step' => 0.1 , 'required' => 'required'],
                        ])
                    </div>
                    <div class="modal-footer px-4">
                        <button id="popupSubmit" type="button" class="btn btn-block btn-dark-blue font-weight-bold">{{__t('Save & Continue')}}</button>
                        <button id="popupCancel" type="button" class="btn btn-outline-input font-weight-bold pl-4 pr-4 text-dark-blue"
                                data-dismiss="modal">{{__t('Cancel')}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- end of add item popup -->


    <!-- Start of activity popup -->
    <div  style="display: none;" class="modal fade" id="activityLogModal" tabindex="-1" role="dialog" aria-labelledby="activityLogModal">
        <div class="modal-dialog modal-lg" role="dialog" >
            <div class="modal-content">
                <div class="modal-header w-100 pl-4">
                    <h5 class="modal-title fs-14 lh-25" >{{ __t('Activity Log') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>

                <div class="modal-body">

                    <input type = "text" value = "price_list" id = "entity_key" hidden />
                    <input type = "hidden" value = "{{$record->id}}" id = "entity_id" />

                    <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                    </div>
                </div>

            </div>
        </div>
    </div>



    <!-- end of activity item popup -->
    <!-- end page contents -->
@endsection
@push('styles')
    <script src="{!! getCdnAssetsUrl() !!}js/layout/plugin_onscan.js"></script>
    <script>
        let csrf = '{{ csrf_token() }}';
        var productSearchUrl = "{{route('owner.product.search')}}";
        var priceUpdateUrl = "{{route('owner.price_list_items.update', ['price_list_item' => 'id'])}}";
        var nameUpdateUrl = "{{route('owner.price_lists.update', ['price_list' => $record->id])}}";
        var gridUrl = '{{route('owner.price_list_items.index', ['price_list_id' => $record->id])}}';
        var gridId = '#ajax-grid-content';
        var autoloadPrices = $.parseJSON('{!! json_encode($products_prices) !!}');
        var addItemUrl = '{{route('owner.price_list_items.addItem', ['price_list_id' => $record->id])}}';
        var pdfReportURL= "";

    </script>
    <script src="{{asset('js/listings/price_list_items-listing.js?v='. JAVASCRIPT_VERSION)}}"></script>
@endpush
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    <script>
        loadActivityLogTab();
    </script>

@endpush
