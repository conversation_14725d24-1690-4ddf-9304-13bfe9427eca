@extends('layouts.owner', ['containerClass' => 'l-container'])

@php
use Izam\Forms\Element\Separator;
use App\Services\LeaveTypeService;
use App\Services\LeaveApplicationService;
use App\Utils\AttendancePermissionTypesUtil;
    use Carbon\Carbon;
    use Illuminate\Support\Facades\Storage;
    $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
    $form->prepare();
    $row = new \Izam\Forms\View\Helper\Form\FormRow();
    $sep = new Separator('', []);
    $formRoute = route('owner.entity.store',  ['entityKey' => $entityKey]);
    $attachments_ids = [];
    $attachments_objects = null;
    $attachments_value = $form->get("attachments")->getAttributes()['data-izam1-forms1-value'] ?? null ;
    if($attachments_value){
       $attachments_objects = json_decode($attachments_value, true);
       foreach($attachments_objects as $key => $obj){
         if(isset($obj['id'])){
            $attachments_ids[] = $obj['id'];
         }
         if(isset($obj['file_size'])){
            $attachments_objects[$key]['size'] = $obj['file_size'];
         }
         if($errors->any()){
            if(isset($obj['path'])){
                $attachments_objects[$key]['url'] = Storage::temporaryUrl(
                  $obj['path'], Carbon::now()->addMinutes(env('AWS_LIFE_TIME', 1)),
                  ['ResponseContentDisposition' => 'attachment; filename="' . utf8_encode($obj['name']) . '"']
                );
            }
         }
       }
       $attachments_value = json_encode($attachments_objects);
    }
   $leaveApplicationStaffSelection = [];
   $isUpdate = false;
   if ($form->get("staff_id")->getValue()){
      $isUpdate = true;
      $leaveApplicationStaff = App\Models\Staff::find($form->get("staff_id")->getValue());
      $text =  "#" .$leaveApplicationStaff->code . ' '. $leaveApplicationStaff->name . ' ' . $leaveApplicationStaff->last_name;
      $text .= $leaveApplicationStaff->email_address ? "({$leaveApplicationStaff->email_address})" : '';
      $leaveApplicationStaffSelection = ! $leaveApplicationStaff ? ['id'=> null,'img' => null, 'text'=> null] : [
         'img' => Izam\Daftra\Common\Services\AvatarURLGenerator::generate($leaveApplicationStaff->name, $leaveApplicationStaff->id, 30, $leaveApplicationStaff->photo),
         'text' => $text,
         'id' => $leaveApplicationStaff->id
      ];
   }
$leaveTypeService = resolve(LeaveTypeService::class);
$allLeaveTypes = [];
if($form->get('id')->getValue())
    $allLeaveTypes = $leaveTypeService->getUserAvaialableLeaveTypes($form->get('staff_id')->getValue())['leave_types'];
else
    $allLeaveTypes = $leaveTypeService->repo->all()->toArray();

$leaveApplicationService = resolve(LeaveApplicationService::class);
@endphp
@section('content')

{!! \Izam\Forms\IzamFormView::filterForm()->openTag($form) !!}
@csrf

@if ($form->get('id')->getValue())
{!! $row($form->get('_method')) !!}
@endif
{!! $row($form->get('id')) !!}

<style>
   @media (min-width: 576px) {
      .wrap-leave-balance .selectize-control {
         max-width :calc(100% - 150px);
      }
    }
   @media (max-width: 575.98px) {
      .wrap-leave-balance .selectize-control {
         max-width :calc(100% - 100px);
      }
      .wrap-leave-balance .leave-balance-btn {
         padding: 11px 6px;
         width: 100px;
      }
    }
</style>

<div class="pages-head" style="top: 45px;">
   <div class="l-container">
      <div class="l-flex-row l-flex--align-center">
         <div class="l-flex-col-lg-12 u-text-align-end">
            <div class="l-btn-list-h l-btn-list-h--inline">
               <a href="/v2/owner/entity/leave_application/list" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-secondary u-text-color-action u-text-hover-color-action">
                  <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                  <span class="ui-btn-inner-content">
                     <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-close-thick"></i>
                     <span class="ui-btn-inner-text">{{__t('Cancel')}}</span>
                  </span>
               </a>
               <button class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white" type="submit">
                  <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                  <span class="ui-btn-inner-content">
                     <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-content-save"></i>
                     <span class="ui-btn-inner-text">{{__t('Save')}}</span>
                  </span>
               </button>
            </div>
         </div>
      </div>
   </div>
</div>

<div class="l-create-card-boxz">

      <div class="ui-card">
         <div class="ui-card-header">
            <h3 class="ui-card-header-title">{{__t('Leave Application Information')}}</h3>
         </div>
         <div class="ui-card-content ui-card-content-box--spacing-bottom-0">
            <div class="ui-card-content-box ui-card-content-box--spacing-bottom-0">
               <div class="l-flex-row l-flex-row--spacing-8">
                @if(\App\Facades\Permissions::checkPermission([\Izam\Daftra\Common\Utils\PermissionUtil::MANAGE_ATTENDANCE_PERMISSION]))
                  <div class="l-flex-col-lg-6">
                     <div class="l-input-box">
                        <label for="name" class="l-input-label text-muted font-weight-bold">{{__t('Employee')}}<span class="u-text-color-danger">&nbsp;*</span></label>

                        @error('staff_id')
                        <div class="flatpickr-wrapper is-invalid-box">
                           @enderror
                           <div class="l-flex l-flex--align-center wrap-leave-balance">
                           <select name="staff_id" id="staff_id" placeholder="<?= $staffInputPlaceholder  ?>" class="l-input ui-input" data-app-form-validate="required" data-app-form-select="true" data-app-form-select-template="client-employee" data-app-form-select-options='{"ajax": { "url": "/v2/owner/staff/search?allow_inactive=0&term=d&_type=query&q=__q__" } }'>
                              @if(!$form->get("staff_id")->getValue())
                              <option value=""></option>
                              @else
                              <option value="{{$leaveApplicationStaffSelection['id']}}" data-data='{{ json_encode($leaveApplicationStaffSelection) }}' selected> {{$leaveApplicationStaffSelection['text']}}</option>
                              @endif

                              <option value=""></option>
                           </select>
                           <button type="button" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-secondary u-text-color-black u-text-hover-color-black leave-balance-btn" id="balance-modal" data-bs-toggle="modal" data-bs-target="#leaveBalanceModal" disabled>
                              {{__t('Check balance')}}
                           </button>
                           </div>
                           @error('staff_id')
                           <ul class="ui-input-errors"><li>{{ $message }}</li></ul>
                        </div>
                        @enderror

                     </div>
                  </div>
                  @else
                  <?php
                     $aws = new Izam\Aws\Aws;
                     $staff_image = $user['photo'] ? $aws->getPermanentUrl($user['photo']) : \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($username, $user["staff_id"], 30, null);

                     $userData = [
                        "id" => getAuthOwner("staff_id"),
                        "img" => $staff_image,
                        "text" => $username
                     ];
                  ?>
                  <!-- auto staff -->
                  <div class="l-flex-col-lg-6">
                     <div class="l-input-box">
                        <label for="name" class="l-input-label text-muted font-weight-bold">{{__t('Employee')}}<span class="u-text-color-danger">&nbsp;*</span></label>
                        <div class="l-flex l-flex--align-center">
                           <select readonly name="staff_id" id="staff_id" class="l-input ui-input" data-app-form-select="true" data-app-form-select-template="client-employee">
                              <option value="{{ getAuthOwner('staff_id') }}" data-data='{{ json_encode($userData) }}' selected></option>
                           </select>
                           <button type="button" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-secondary u-text-color-black u-text-hover-color-black leave-balance-btn" id="balance-modal" data-bs-toggle="modal" data-bs-target="#leaveBalanceModal" disabled>
                              {{__t('Leave Balance')}}
                           </button>
                        </div>
                     </div>
                  </div>

                  @endif
                  <div class="l-flex-col-lg-2">
                     <div class="l-input-box l-input-box--spacing-16">
                        <label for="name" class="l-input-label text-muted font-weight-bold">{{__t('Days')}}<span class="u-text-color-danger">&nbsp;*</span></label>

                        @error('days')
                        <div class="flatpickr-wrapper is-invalid-box">
                           @enderror
                           <input class="l-input ui-input" autocomplete="off"
                           value="<?= $form->get("days")->getValue(); ?>"
                           id="leave_days"
                           type="number" name="days" placeholder=""
                           data-app-form-validate="required"
                           />
                           @error('days')
                           <ul class="ui-input-errors">
                              <li>{{ $message }}</li>
                           </ul>
                        </div>
                        @enderror

                     </div>
                  </div>
                  <div class="l-flex-col-lg-2">
                     <div class="l-input-box l-input-box--spacing-16">
                        <label for="name"
                        class="l-input-label text-muted font-weight-bold">{{__t('Start Date')}} <span
                        class="u-text-color-danger">&nbsp;*</span></label>
                        <div class="l-input-w-icon-box l-input-w-icon-box--align-end ">
                           <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon @error('date_from') is-invalid @enderror"></i>
                           @error('date_from')
                           <div class="flatpickr-wrapper is-invalid-box">
                              @enderror
                              <input class="l-input ui-input" autocomplete="off"
                              value="<?= $form->get("date_from")->getValue(); ?>"
                              id="from_date"
                              type="text" name="date_from" placeholder=""
                              data-app-form-validate="required"
                              data-app-form-date="true" />
                              @error('date_from')
                              <ul class="ui-input-errors"><li>{{ $message }}</li></ul>
                           </div>
                           @enderror

                        </div>
                     </div>
                  </div>

                  <div class="l-flex-col-lg-2">
                     <div class="l-input-box l-input-box--spacing-16">
                        <label for="name"
                        class="l-input-label text-muted font-weight-bold">{{__t('End Date')}} <span
                        class="u-text-color-danger">&nbsp;*</span></label>
                        <div class="l-input-w-icon-box l-input-w-icon-box--align-end ">
                           <i class="mdi mdi-calendar-today l-icon l-icon--size-24 u-text-color-default ui-icon ui-icon--size-24 ui-input-icon @error('date_to') is-invalid @enderror"></i>
                           @error('date_to')
                           <div class="flatpickr-wrapper is-invalid-box">
                              @enderror
                              <input class="l-input ui-input" autocomplete="off"
                              value="<?= $form->get("date_to")->getValue(); ?>"
                              id="date_to_input"
                              type="text" name="date_to" placeholder=""
                              data-app-form-validate="required"
                              data-app-form-date="true" />
                              @error('date_to')
                              <ul class="ui-input-errors"><li>{{ $message }}</li></ul>
                           </div>
                           @enderror
                        </div>
                     </div>
                  </div>

                  <div class="col-md-6">
                     @include('partials.form.input_template', [
                         'options' => AttendancePermissionTypesUtil::getOptions(),
                         'label' => __t('Type'),
                         'attributes' => ['required' => 'required', 'onchange' => 'handleTypes(this);','disableAlphabeticSorting'=>true, 'id' => 'permission-type'],
                         'inputClass' => 'form-control',
                         'div' => 'form-group',
                         'name' => 'type',
                         'type' => 'select',
                         'value' => $form->get("type")->getValue()?? 0
                     ])

                    <div class="leave_type l-flex l-flex--wrap">
                        <div class="l-flex-col-lg-6 l-flex-col-12">
                           <div class="l-input-box l-input-box--spacing-16">
                              <label for="name" class="l-input-label text-muted font-weight-bold">{{__t('Leave Type')}}<span class="u-text-color-danger">&nbsp;*</span></label>

                              @error('leave_type_id')
                              <div class="flatpickr-wrapper is-invalid-box">
                                 @enderror

                                 <select name="leave_type_id" id="leave_type_id" class="l-input ui-input" data-app-form-validate="required">

                                    @if(!$form->get("leave_type_id")->getValue())
                                    <option value=""></option>
                                    @else
                                    <option value="<?php echo $form->get("leave_type_id")->getValue() ?>" selected> <?= $form->get("leave_type_id")->getValueOptions()[0]['label'] ? substr($form->get("leave_type_id")->getValueOptions()[0]['label'], 1) : '' ?> </option>
                                    @endif

                                    @foreach($allLeaveTypes as $leaveType)
                                       <option value="{{$leaveType['id']}}"> {{$leaveType['name']}}</option>
                                    @endforeach

                                 </select>

                                 @error('leave_type_id')
                                 <ul class="ui-input-errors">
                                    <li>{{ $message }}</li>
                                 </ul>
                              </div>
                              @enderror

                           </div>
                        </div>
                        <div class="l-flex-col-lg-6 l-flex-col-12">
                           <div class="l-input-box l-input-box--spacing-16">
                              <label for="name" class="l-input-label text-muted font-weight-bold d-md-block d-none">&nbsp;</label>
                              <div class="l-input-placeholder ui-input-placeholder">
                                 <span class="u-text-color-default">{{__t('Leave Balance')}}: &nbsp;</span>
                                 <span class="u-text-color-black" id="leave_balance"></span>
                              </div>
                           </div>
                        </div>
                  </div>
                     @include('partials.form.input_template', [
                         'label' => __t('Late Time'),
                         'inputClass' => 'form-control input-minutes',
                         'div' => 'form-group form-group-placeholder late_time d-none',
                         'name' => 'late_time',
                         'type' => 'text',
                         'icon' => '<small class="input-placeholder">'. __t('minutes') .'</small>',
                         'attributes' => ['min' => '0'],
                         'value' => $form->get("late_time")->getValue()?? null
                     ])
                     @include('partials.form.input_template', [
                         'label' => __t('Early Time'),
                         'inputClass' => 'form-control input-minutes',
                         'div' => 'form-group form-group-placeholder early_time d-none',
                         'name' => 'early_time',
                         'type' => 'text',
                         'icon' => '<small class="input-placeholder">'. __t('minutes') .'</small>',
                         'attributes' => ['min' => '0'],
                         'value' => $form->get("early_time")->getValue()?? null
                     ])
                 </div>
                  @php
                        $shift_type_attr = [
                           'options' => \App\Utils\MultipleShiftTypeUtil::getTypes(),
                           'label' => __t('Shift Type'),
                           'inputClass' => 'select form-control',
                           'div' => 'col-md-6 form-group',
                           'name' => 'shift_type',
                           'type' => 'select',
                           'attributes' => [
                              'required' => 'required'
                           ],
                           'value' => $form->getData()['shift_type'] ?? ''
                        ];

                        $multi_shift_enabled = \App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift');
                        $showDiv = $leaveApplicationService->showShiftTypeInputInForm($form->getData(), $isUpdate);

                  @endphp
                   <span id="shiftTypeInputContainer" style="display: none;"></span>
                  @if ($showDiv)
                        @include('partials.form.input_template', $shift_type_attr)
                  @endif
                  <div class="l-flex-col-lg-6">
                        @include('partials.form.input_template', [
                        'label' => __t('description'),
                        'inputClass' => 'form-control',
                        'div' => 'form-group',
                        'name' => 'description',
                        'type' => 'textArea',
                        'attributes' => [
                        'rows' => '4',
                        ],
                        'value' => $form->get('description')->getValue() ?? ''
                        ])
                  </div>

                     <div class="l-flex-col-lg-12">
                        <div class="l-input-box ">
                           <label class="l-input-label text-muted font-weight-bold">{{__t('Attachments')}}</label>
                           <input type="hidden" name="attachments" id="attachments" data-app-form-uploader="images" data-app-form-uploader-options='{"lockFormSubmitWhileUploading":true, "uploadUrl": "\/v2\/api\/entity\/upload_file\/leave_application\/leave_applications.attachments","rules":{"max": 5242880,"ext": ["pdf","doc","docx","xls","xlsx","csv","jpg","png","gif","zip","jpeg","rar","bmp", "ppt", "pptx", "mdb", "accdb", "pst", "pub", "vsd", "vsdx", "one"]},"multiple": true, "sortable" : true ,  "fileItemsWrapperClass": "l-uploader-file-items ui-uploader-file-items", "fileItemsWrapperActiveClass": "l-uploader-file-items l-uploader-file-items--spacing-top ui-uploader-file-items"}' value="<?= join(",", $attachments_ids) ?>" data-app-form-uploader-value='<?= $attachments_value ?>' />
                           <template data-app-form-uploader-input-template="images">
                              <div class="l-uploader ui-uploader" tabindex="0" data-app-keyboard-support="true" data-app-keyboard-support-options='{"keypress": {"keyCode": "13", "action": "click"}}' data-app-form-uploader-input-drop-area="true" data-app-form-uploader-input-btn="true">
                                 <div class="ui-uploader-drop-area l-uploader-drop-area">
                                    <span class="ui-uploader-drop-area-icon l-uploader-drop-area-icon">
                                       <svg xmlns="http://www.w3.org/2000/svg" width="33.002" height="44.003" viewBox="0 0 33.002 44.003">
                                          <path d="M33-47.27a2.369,2.369,0,0,0-.6-1.459L23.98-57.146a2.37,2.37,0,0,0-1.459-.6H22v11H33ZM21.314-44a2.069,2.069,0,0,1-2.063-2.063V-57.75H2.063A2.063,2.063,0,0,0,0-55.687V-15.81a2.063,2.063,0,0,0,2.063,2.063H30.939A2.063,2.063,0,0,0,33-15.81V-44ZM9.672-42.624A4.125,4.125,0,0,1,13.8-38.5a4.125,4.125,0,0,1-4.125,4.125A4.125,4.125,0,0,1,5.547-38.5,4.125,4.125,0,0,1,9.672-42.624ZM27.548-22h-22l.042-4.167,3.4-3.4a.983.983,0,0,1,1.417.042l3.4,3.4,8.9-8.9a1.031,1.031,0,0,1,1.459,0l3.4,3.4Z" transform="translate(0 57.75)" fill="#E4EBF2" />
                                       </svg>
                                    </span>
                                    <span class="ui-uploader-drop-area-text l-uploader-drop-area-text">
                                       <i class="ui-uploader-drop-area-text-upload-icon fal fa-cloud-upload ui-icon ui-icon--size-20 u-text-color-primary"></i><span>&nbsp;{{__t('Drop image here or')}}&nbsp;</span><span class="u-text-color-primary">{{__t('select from your computer')}}</span>
                                    </span>
                                 </div>
                              </div>
                           </template>
                           <template data-app-form-uploader-progress-template="images">
                              <div class="l-uploader-file-item ui-uploader-file-item l-uploader-file-item--multiple ui-uploader-file-item--multiple">
                                 <div class="l-uploader-file-item-icon-container ui-uploader-file-item-icon-container">
                                    <i class="ui-uploader-file-item-icon l-uploader-file-item-icon mdi mdi-file"></i>
                                 </div>
                                 <span class="l-uploader-file-item-error ui-uploader-file-item-error" data-app-form-uploader-error-container="true" style="display: none;" title="__name__ __error__">__error__</span>
                                 <div class="l-uploader-file-item-progress ui-uploader-file-item-progress" data-app-form-uploader-no-error-container="true" title="__name__">
                                    <div class="l-uploader-file-item-progress-active-bar ui-uploader-file-item-progress-active-bar" style="width: __progress__%;">__progress__%</div>
                                 </div>
                                 <div class="l-uploader-file-item-actions">
                                    <i class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-close-thick u-text-color-red" tabindex="0" data-app-keyboard-support="true" data-app-keyboard-support-options='{"keypress": {"keyCode": "13", "action": "click"}}' title="Cancel" data-app-form-uploader-cancel-btn="true"></i>
                                 </div>
                              </div>
                           </template>
                           <template data-app-form-uploader-value-template="images">
                              <div class="l-uploader-file-item ui-uploader-file-item l-uploader-file-item--multiple ui-uploader-file-item--multiple">
                                 <div class="l-uploader-file-item-img-container ui-uploader-file-item-img-container" data-app-form-uploader-img-container="true" style="display: none;">
                                    <img class="ui-uploader-file-item-img l-uploader-file-item-img" data-app-lightbox="__url__" src="__url__" />
                                 </div>
                                 <div class="l-uploader-file-item-icon-container ui-uploader-file-item-icon-container" data-app-form-uploader-icon-container="true">
                                    <i class="ui-uploader-file-item-icon l-uploader-file-item-icon __icon__"></i>
                                 </div>
                                 <span class="l-uploader-file-item-name ui-uploader-file-item-name" title="__name__">__name__</span>
                                 <span class="l-uploader-file-item-size ui-uploader-file-item-size" title="__size__">__size__</span>
                                 <div class="l-uploader-file-item-actions">
                                    <a class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-download u-text-color-primary" href="__url__" target="_blank" download title="Download"></a>
                                    <i class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-pencil u-text-color-action" tabindex="0" data-app-keyboard-support="true" data-app-keyboard-support-options='{"keypress": {"keyCode": "13", "action": "click"}}' title="Edit" data-app-form-uploader-edit-btn="true"></i>
                                    <i class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-trash-can u-text-color-red" tabindex="0" data-app-keyboard-support="true" data-app-keyboard-support-options='{"keypress": {"keyCode": "13", "action": "click"}}' title="Delete" data-app-form-uploader-delete-btn="true"></i>
                                 </div>
                              </div>
                           </template>
                        </div>
                        <div class="ui-uploader-text l-uploader-text">
                           <span><?= sprintf(__t('Max file size %d %s'), 5, __t('MB')) ?></span> <br />
                           <span><?= sprintf(__t('Allowed file types: (%s)'), 'pdf,doc,docx,xls,xlsx,csv,jpg,png,gif,zip,jpeg') ?></span>
                        </div>
                        <br>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>



   {!! \Izam\Forms\IzamFormView::filterForm()->closeTag() !!}
   @include('leave_applications.owner.leave_balance_modal', ['showEmployeeInput' => false])
   @endsection
   @push('scripts')
   @include('LocalEntity::create.init_js')

   <script>
      // console.log(USER.staff_id);
      var leaves_balance_data = null

      function getTypeBasedCredit(data, type = null) {
         if (!data) {
            return 0
         }

         let int_type = null
         try {
            int_type = parseInt(type)
         } catch (error) {
            return 0
         }

         return data['leaves'][int_type] ? data['leaves'][int_type]['remaining'] : 0
      }

      function changeAvailableBalance(staff_element_id = "#staff_id", callback = null) {
         let staff_id = $(staff_element_id).val();
         let from_date = $("#date_from").attr('value');
         let to_date = $("#date_to_input").val();
         let leave_type_id = $("#leave_type_id").val() ?? null;
         let leave_balance_modal_btn = $("#balance-modal");

         $.ajax({

            url: `/v2/owner/reports/attendance/attendance_balance_for_auth${ !! staff_id ? "?staff_id=" + staff_id : ""}`,

            type: "GET",
            success: function(result) {
               leaves_balance_data = JSON.parse(result)
               if(staff_id && leaves_balance_data){
                  leave_balance_modal_btn.removeAttr('disabled')
               }else{
                  leave_balance_modal_btn.attr('disabled', 'disabled')
               }
               let current_balance_value = getTypeBasedCredit(leaves_balance_data, leave_type_id)
               $("#leave_balance").text(current_balance_value + "");
               if(callback){
                  callback();
               }
            }
         });
      }
      function getAvailableLeaveTypes(staff_element_id = "#staff_id", callback = null) {
         let staff_id = $(staff_element_id).val();

        if(!staff_id) return ;
        $.ajax({

            url: `/v2/owner/entity/leave_application/get-user-available-leave-types/`+ staff_id,
            type: "GET",
            success: function(result) {
                let leaveTypeDropdown = $("#leave_type_id");
                leaveTypeDropdown.html('<option value=""></option>');

                if (result && result.leave_types) {
                    result.leave_types.forEach(function(leaveType) {
                        leaveTypeDropdown.append(`<option value="${leaveType.id}">${leaveType.name}</option>`);
                    });
                }
            }
        });
      }
      let shiftTypeInput = `@include('partials.form.input_template', $shift_type_attr)`

      function showShiftTypeInput() {
         let staff_id = $('#staff_id').val();
         $.ajax({
            url: `{{route('owner.staff.ajaxHasSecondaryShift')}}?staff_id=${staff_id}`,
            type: "GET",
            success: function(result) {
               if(result.has_secondary_shift && {{$multi_shift_enabled ?? 0}}){
                  $(shiftTypeInput).insertAfter('#shiftTypeInputContainer')
               }else{
                  $('#shift_type').parent().remove()
               }
            }
         });
      }

      $("#leave_type_id").on("change", function(e) {
         changeAvailableBalance()
      })
      $("#staff_id").on("change", function(e) {
         changeAvailableBalance()
         getAvailableLeaveTypes()

         @if($multi_shift_enabled)
            showShiftTypeInput()
         @endif
      })

      $("#balance-modal").on("click", function(e) {
        $("#leaveBalanceModal").modal("show");
      });

      $(".close-leave-balance-modal").on("click", function(e) {
         $("#leaveBalanceModal").modal("hide");
      })

      $(document).ready(function() {
         changeAvailableBalance()
      })

      function calcEndDate(){
         let days = $('#leave_days').val()
         let fromDate = $('#from_date').val()
         let dateFormat = "<?=getDateFormats('moment_js')[getCurrentSite('date_format')]?>"
         if(days && fromDate){
            var day = moment(fromDate);
            day.add('days', days-1)
            $('#date_to_input').get(0)._flatpickr.setDate(moment(day, dateFormat).toDate())
         }
      }

      $('#leave_days').on('input', function(){
         calcEndDate()
      });
      $('#from_date').on('change', function(){
         calcEndDate()
      });

      $('#date_to_input').on('change', function(){
         let endDate = $('#date_to_input').val()
         let fromDate = $('#from_date').val()
         var duration = moment.duration(moment(new Date(endDate)).diff(moment(new Date(fromDate))));
         $('#leave_days').val(duration.asDays() + 1)
      });

      $(".template-client-employee div[data-value=44] span")


      function handleTypes(selector) {
         let value = $(selector).val(),
            arraySelectors,
            arraySelectorsHide;
         if( (value == '<?=AttendancePermissionTypesUtil::LEAVE?>')  || (value == '<?=AttendancePermissionTypesUtil::HALF_LEAVE?>')){
            arraySelectors = [$('.leave_type')];
            $.each( arraySelectors, function( i, arraySelector ){
                  arraySelector.removeClass('d-none');
                  arraySelector.find('span.required').remove();
            });
            arraySelectorsHide = [$('.late_time'), $('.early_time')];
            $.each( arraySelectorsHide, function( i, arraySelector ){
               hideInput(arraySelector)
            });
         } else if(value == '<?=AttendancePermissionTypesUtil::DELAY?>') {
            arraySelectors = [$('.late_time')];
            $.each( arraySelectors, function( i, arraySelector ){
               showInput(arraySelector)
            });
            arraySelectorsHide = [$('.leave_type'), $('.early_time')];
            $.each( arraySelectorsHide, function( i, arraySelector ){
               hideInput(arraySelector)
            });
         } else {
            arraySelectors = [$('.early_time')];
            $.each( arraySelectors, function( i, arraySelector ){
               showInput(arraySelector)
            });
            arraySelectorsHide = [$('.leave_type'), $('.late_time')];
            $.each( arraySelectorsHide, function( i, arraySelector ){
               hideInput(arraySelector)
            });
         }
      }

      function showInput(element){
         element.removeClass('d-none');
         element.find('span.required').remove();
         element.find('label').append('<span class="required">*</span>');
         element.find('select').attr('required', 'required');
         element.find('input').attr('required', 'required');
      }

      function hideInput(element){
         element.addClass('d-none');
         element.find('span.required').remove();
         element.find('select').removeAttr('required');
         element.find('input').removeAttr('required');
         element.find('input').val(null)
         try{
            element.find('select')[0].selectize.clear()
         }catch(e){}
      }
      handleTypes($('#permission-type'));
   </script>

   @endpush




   <!-- -------------------------------------------------------------------------------------------------------------------------------- -->
