@extends('layouts.owner')
@php
    $type = request('type');

    $listingRowActions = [
        'view' => [
            'title' => __t('View'),
            'class' => 'view-action',
            'url' => [
                'name' => "owner.tracking_numbers.$type.show",
                'parameters' => [
                    'id' => [
                        'field' => 'id'
                    ]
                ]
            ]
        ]
    ];

    $allClass = !isset($filtered['available']) && !isset($filtered['out_of_stock']) ? 'success disabled text-white': 'light';
    $availableClass = isset($filtered['available']) ? 'success disabled text-white': 'light';
    $outOfStockClass = isset($filtered['out_of_stock']) ? 'success disabled text-white': 'light';

    $listingActions = [
        'all' => [
            'class' => "btn btn-{$allClass} border mr-0 responsive btn-text-mobile",
            'url' => [
                'name' => 'javascript:void(0);',
                'value' => route('owner.tracking_numbers.index',['type' => $type, 'product' => $_GET['product']??null])
            ],
            'title' => __t('All')
        ],
        'available' => [
            'class' => "btn btn-{$availableClass} border mr-0 responsive btn-text-mobile",
            'url' => [
                'name' => 'javascript:void(0);',
                'value' => route('owner.tracking_numbers.index',['type' => $type, 'available' => 0, 'product' => $_GET['product']??null])
            ],
            'title' => __t('Available')
        ],
        'out_of_stock' => [
            'class' => "btn btn-{$outOfStockClass} border mr-0 responsive btn-text-mobile",
            'url' => [
                'name' => 'javascript:void(0);',
                'value' => route('owner.tracking_numbers.index',['type' => $type, 'out_of_stock' => 0, 'product' => $_GET['product']??null])
            ],
            'title' => __t('Out Of Stock')
        ]
    ];

    if(isset($_GET['iframe']) && $_GET['iframe']) {
        $listingActions = [
            'all' => [
            'class' => 'lh-1 my-2 responsive text-dark-blue text-decoration-underline',
            'url' => [
                'name' => 'javascript:void(0);',
                'value' => route('owner.tracking_numbers.index',['type' => $type, 'product' => $_GET['product']??null])
            ],
            'title' => __t('View All')
        ]];
    }

    $fields = [];
    $fields[] = [
        'label' => __t('Product'),
        'class' => 'd-none d-md-table-cell',
        'template' => '<strong>{{$record->product->name??""}}</strong>#{{$record->product->product_code??"" }}',
    ];

    switch ($type) {
        case App\Utils\TrackingTypeUtil::SERIAL:
            $fields[] = [
                'label' => __t('Serial Number'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'serial'
            ];
            $fields[] = [
                'label' => __t('Quantity'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'quantity'
            ];
            break;
        case App\Utils\TrackingTypeUtil::LOT:
            $fields[] = [
                'label' => __t('Lot Number'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'lot'
            ];
            $fields[] = [
                'label' => __t('Quantity'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'quantity'
            ];
            break;
        case App\Utils\TrackingTypeUtil::EXPIRY_DATE:
            $fields[] = [
                'type' => 'date',
                'label' => __t('Expiry Date'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'expiry_date'
            ];
            $fields[] = [
                'label' => __t('Status'),
                'class' => 'd-none d-md-table-cell',
                'template' => '<?php $diff = Carbon\Carbon::now()->diffInDays(Carbon\Carbon::parse($record->expiry_date), false); ?>
                        <span class="status status-{{($diff < 0)? \'danger\' : (($diff >= 0 && $diff <=30)? \'warning\':\'inactive\')}}">
                            <i class="cir"></i>{{($diff < 0)? __t(\'Expired\') : sprintf(__t(\'Expire in (%s days)\'), $diff)}}
                        </span>'
            ];
            $fields[] = [
                'label' => __t('Quantity'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'quantity'
            ];
            break;
        case App\Utils\TrackingTypeUtil::LOT_AND_EXPIRY_DATE:
            $fields[] = [
                'label' => __t('Lot Number'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'lot'
            ];
            $fields[] = [
                'type' => 'date',
                'label' => __t('Expiry Date'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'expiry_date'
            ];
            $fields[] = [
                'label' => __t('Status'),
                'class' => 'd-none d-md-table-cell',
                'template' => '<?php $diff = Carbon\Carbon::now()->diffInDays(Carbon\Carbon::parse($record->expiry_date), false); ?>
                        <span class="status status-{{($diff < 0)? \'danger\' : (($diff >= 0 && $diff <=30)? \'warning\':\'inactive\')}}">
                            <i class="cir"></i>{{($diff < 0)? __t(\'Expired\') : sprintf(__t(\'Expire in (%s days)\'), $diff)}}
                        </span>'
            ];
            $fields[] = [
                'label' => __t('Quantity'),
                'class' => 'd-none d-md-table-cell',
                'field' => 'quantity'
            ];
            break;
    }

    $fields[] = [
        'label' => __t('Warehouse'),
        'class' => 'd-none d-md-table-cell',
        'template' => '<strong>{{$record->store->name}}</strong>',
    ];
@endphp

@section('content')
    {{-- contains multi actions section pagination section and buttons section (actions section) $listingActions array of listing actions --}}
    @include('partials.listing.page-head', [
        'listing_actions' => $listingActions
    ])

    @include('partials.listing.filter_form', [
        'filters' => $filters,
        'filtered' => $filtered,
        'resetRoute' => route('owner.tracking_numbers.index',['type' => $type, 'available' => 0, 'reset' => 1])
    ])

    @include('partials.listing.basic_listing', [
        'row_actions' => $listingRowActions,
        'multi_listing_actions' => false,
        'data' => $pagination,
        'fields' => $fields
    ])
@endsection

@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script>
        var productSearchUrl = "{{route('owner.product.search')}}";
        var serialSearchUrl = "{{route('owner.serial.search')}}";
        var lotSearchUrl = "{{route('owner.lot.search')}}";
    </script>
    <script src="{{asset('js/listings/tracking-numbers-listing.js?v' . CSS_VERSION)}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/index/index.js"></script>
@endpush
