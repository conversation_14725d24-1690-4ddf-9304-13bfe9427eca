@php
    $message = $type = false;
    $mapMessages = [
        'Errormessage' => 'danger',
        'Sucmessage'   => 'success',
        'warningMessage'  => 'warning',
        'info'         => 'info'
    ];
    $customMessages = [];
    if(isset($_SESSION['Message']['secondaryMessage'])){
            $class = isset($_SESSION['Message']['secondaryMessage']['params']['class']) ? $_SESSION['Message']['secondaryMessage']['params']['class'] : "Sucmessage";
            $type = isset($mapMessages[$class]) ? $mapMessages[$class] : "Errormessage";
            $message = $_SESSION['Message']['secondaryMessage']['message'];
            unset($_SESSION['Message']);
        }
    if(isset($_SESSION['Message']['CustomError']['message'])) {
        $customMessages = $_SESSION['Message']['CustomError']['message'][0];
        unset($_SESSION['Message']);

    }

@endphp
@if ($type)
        <div class="alert alert-icon alert-bordered alert-dismissible border-{{$type}} alert-{{$type}}">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <div class="d-flex">
                <div class="alert-icon">
                    <i class="far fa-lg fa-exclamation-circle text-{{$type}} mr-3"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="mb-0"><strong>{!! $message !!}</strong></p>
                </div>
            </div>
        </div>
@endif
@foreach($customMessages as $message)
    <div class="alert alert-icon alert-bordered alert-dismissible border-danger alert-danger">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
        <div class="d-flex">
            <div class="alert-icon">
                <i class="far fa-lg fa-exclamation-circle text-danger mr-3"></i>
            </div>
            <div class="flex-grow-1">
                <p class="mb-0"><strong>{!! $message !!}</strong></p>
            </div>
        </div>
    </div>
@endforeach

@if(isset($_SESSION['Message']))
    @foreach($_SESSION['Message'] as $i => $message)
        <div class="alert alert-icon alert-bordered alert-dismissible border-{{$message['type']}} alert-{{$message['type']}}">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">×</span>
            </button>
            <div class="d-flex">
                <div class="alert-icon">
                    <i class="far fa-lg fa-check-circle text-{{$message['type']}} mr-3"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="mb-0"><strong>{!! $message['message'] !!}</strong></p>
                </div>
            </div>
        </div>
        @php unset($_SESSION['Message'][$i]); @endphp
    @endforeach
@endif
