@php
    $pageTitle = __t('Attendance Settings');
    $generalBreadCrumbs = [['title' => $pageTitle]];
    $links = [
        [
            'title' => 'Holiday Lists',
            'url' => route('owner.holiday_lists.index'),
            'icon' => 'mdi mdi-beach'
        ],
        [
            'title' => 'Attendance Flags',
            'url' => route('owner.attendance_flags.index'),
            'icon' => 'mdi mdi-flag-outline'
        ],
        [
            'title' => 'Leave Types',
            'url' => route('owner.leave_types.index'),
            'icon' => 'mdi mdi-reminder'
        ],
        [
            'title' => 'Machines',
            'url' => route('owner.machines.index'),
            'icon' => 'mdi mdi-progress-upload fa-rotate-90'
        ],
        [
            'title' => 'Leave Policies',
            'url' => route('owner.leave_policies.index'),
            'icon' => 'mdi mdi-progress-wrench fa-rotate-90'
        ],
        [
            'title' => 'Approval Configurations',
            'url' => route("owner.entity.list",['entityKey' => \App\Utils\EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION,'reset'=>1]),
            'icon' => 'mdi mdi-wrench-check'
        ],
        [
            'title' => 'Basic Settings',
            'url' => route('owner.attendance_settings.basic'),
            'icon' => 'mdi mdi-cogs'
        ],
        [
            'title' => 'Attendance Restrictions',
            'url' => route('owner.attendance_restrictions.index'),
            'icon' => 'mdi mdi-account-lock'
        ],
        [
            'title' => 'Printable Templates',
            'url' => route('owner.manage_printable_templates_for_group', Izam\Template\Utils\PrintableTemplatesGroupUtil::ATTENDANCE_TYPE),
            'icon' => 'fal fa-receipt'
        ],

    ];
@endphp
@extends('layouts.owner')
@section('content')
    <!-- start page contents -->

    @include('partials.layout.settings_grid',['links' => $links])

    <!-- end page contents -->
@endsection
