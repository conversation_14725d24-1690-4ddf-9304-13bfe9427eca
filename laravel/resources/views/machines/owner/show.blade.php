@extends('layouts.owner')
@inject('Permissions', 'App\Facades\Permissions')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/index/index.min.css?v={{CSS_VERSION}}" />
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}" />
@endpush
@php
    $canPullFromMachine = $Permissions::checkPermission(\App\Utils\PermissionUtil::PULL_MACHINE_ATTENDANCE_LOG) && $record->status === \App\Utils\ActiveStatusUtil::ACTIVE;

@endphp
@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="pages-head-title">
                        <h2>
                            {{$record->name}}
                            @if($record->status)
                                <span class="status status-active"><i class="cir"></i>{{__t('Active')}}</span>
                            @else
                                <span class="status status-inactive"><i class="cir"></i>{{__t('Inactive')}}</span>
                            @endif
                        </h2>
                    </div>
                </div>
                <div class="col-md-6">
                    @include('partials.show.record_nav', ['url' => 'owner.machines.show', 'name' => 'machine'])
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-multi-line btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                <a href="{{route('owner.machines.edit', ['machine' => $record->id])}}" class="btn btn-light">
                    <i class="mdi mdi-pencil-outline mr-1"></i> {{__t('Edit')}}
                </a>

                @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Machine'), 'href' => route('owner.machines.destroy', ['machine' => $record->id])])

                @if ($record->host && $record->port)
                <a href="#"  id="testConnectionBtn" class=" {{!$canPullFromMachine ? 'disabled' : ''}} btn btn-light"><i class="mdi mdi-flash-red-eye mr-1"></i>{{__t('Test Connection')}}</a>
                <a href="{{route('owner.machines.pull', ['machine' => $record->id])}}" class=" {{!$canPullFromMachine ? 'disabled' : ''}} init-page-loader btn btn-light"><i class="mdi mdi-package-down mr-1"></i>{{__t('Pull')}}</a>
                @endif

                <a href="{{route('owner.machines.mappings.index', ['machine' => $record->id])}}" class="btn btn-light"><i class="mdi mdi-repeat mr-1"></i>{{__t('Mapping')}}</a>
                @if($haveUnmappedUsers)
                <a href="{{route('owner.machines.unmapped_employee.create', ['machine' => $record->id])}}" class="btn btn-light"><i class="mdi mdi-repeat mr-1"></i>{{__t('Unmapped')}}</a>
                @endif
                @if($record->status)
                    <a href="{{route('owner.machines.deactivate', ['machine' => $record->id])}}"
                       class="btn btn-light"><i class="fas fa-comment-alt-edit mr-1"></i>{{__t('Mark as Inactive')}}</a>
                @else
                    <a href="{{route('owner.machines.activate', ['machine' => $record->id])}}"
                       class="btn btn-light"><i class="fas fa-comment-alt-edit mr-1"></i>{{__t('Mark as Active')}}</a>
                @endif
            </div>
        </div>
        <div class="card-body p-1 p-lg-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab" aria-controls="details" aria-selected="true">{{__t('Details')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="session-list-tab" data-toggle="tab" href="#session-list" role="tab" aria-controls="session-list" aria-selected="false">{{__t('Session List')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab" aria-controls="activity-log" aria-selected="false">{{__t('Activity Log')}}</a>
                    </li>
                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">
                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel" aria-labelledby="details">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{sprintf(__t('%s Details'), __t('Machine'))}}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Name")}}}</p>
                                        <p>{{$record->name}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Status")}}}</p>
                                        <p>{{$record->status ? __t('Active') : __t('Inactive')}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Type")}}}</p>
                                        <p>{{$record->machineType->name}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Serial Number")}}}</p>
                                        <p>{{$record->serial_number ?$record->serial_number: '--'}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Host Name")}}}</p>
                                        <p>{{$record->host ?: '--'}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Port")}}}</p>
                                        <p>{{$record->port ?$record->port: '--'}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Last Pull Date")}}}</p>
                                        <p>
                                            {{
                                            $record->last_pull_date ?
                                            formatForView(
                                                convertFromUtc($record->last_pull_date),
                                                \App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER
                                            ) :
                                            '--'
                                            }}
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Total Pulled Signs")}}}</p>
                                        <p>
                                            {{$pulledSignsCount}}
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Webhook URL")}}}</p>

                                        @php
                                            $url = str_replace('/iclock/cdata', '', route('machine.iclock.endpoint', $record->uuid))
                                            @endphp
                                        <div class="input-group input-group-sm mb-3 w-50-l" dir="ltr">
                                            <input type="text" class="form-control ltr text-left"
                                                   dir="ltr"
                                                   value="{{$url}}"
                                                   readonly>
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-info"
                                                        onclick="navigator.clipboard.writeText('{{$url}}')">
                                                    <i class="fa fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-100"></div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane p-3 fade" id="session-list" role="tabpanel" aria-labelledby="session-list-tab">
                        @include('partials.load_grid', ['gridTitle' => '', 'gridUrl' => route('owner.attendance_sessions.index', [
                                'source_type' => \App\Utils\AttendanceSessionSourceTypeUtil::ATTENDANCE_SESSION_SOURCE_TYPE_MACHINE,
                                'source_id' => $record->id
                            ])
                        ])
                    </div>
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">
                        <input type = "text" value = "machine"

                               id = "entity_key" hidden />
                        <input type = "hidden" value = "{{$record->id}}" id = "entity_id" />
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{__t('Activity Log')}}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    @include('partials.modals.create_delete_modal')

    <!-- Start of test connection -->
    <div class="modal fade" id="testConnectionModal" tabindex="-1" role="dialog" aria-labelledby="testConnectionLabel"
         style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-" role="document">
            <div class="modal-content">
                <div class="modal-header bg-transparent border-0 position-absolute w-100" style="z-index: 9;">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div id="prompt-body" class="modal-body">
                    <div class="text-center pt-3 dataDiv">
                        <i class="mdi mdi-flash display-4 text-success"></i>
                        <i class="mdi mdi-flash-off display-4 text-danger"></i>
                        <p></p>
                    </div>
                </div>
                <form method="post" id="row-action-form" name="row-action-form" action="">
                    {{@csrf_field()}}
                    <input type="hidden" name="_method" id="machine-type-method" value=""/>
                    <div class="modal-footer">
                        <a href="#" id="pullLogBtn" class=" init-page-loader btn btn-success">{{__t('Pull Data')}}</a>
                        {{--<button type="submit" class="btn btn-danger" disabled="disabled">{{__t('Pull Data')}}</button>--}}
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{{__t('Cancel')}}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- end of test connection -->

    <!-- end page contents -->
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
{{--    <script src="{!! getCdnAssetsUrl() !!}js/index/index.js"></script>--}}
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
{{--    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>--}}
    <script>
        var recordId = '{{$record->id}}';
    </script>
    <script src="{{asset('/js/attendance/machine/show.js')}}"></script>
    <script>
        // Modals
        $(document).ready(function () {
            promptHeaderEl = $('#promptModalLabelDelete');
            promptBodyEl = $('#prompt-body-delete');
            promptMethodEl = $('#prompt-method-delete');
            propmptFormEl = $('#row-action-form-delete');
            $('a[data-prompt-delete="true"]').on('click', function (event) {
                event.preventDefault();
                message = $(this).data('prompt-message');
                requestMethod = $(this).data('prompt-method');
                header = $(this).data('prompt-header');
                action = $(this).attr('href');
                promptHeaderEl.text(header);
                promptBodyEl.text(message);
                promptMethodEl.val(requestMethod);
                propmptFormEl.attr('action',action);
                propmptFormEl.attr('method', 'post');
            });
        });
    </script>
@endpush
