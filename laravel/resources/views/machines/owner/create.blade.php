@extends('layouts.owner')
@php
    if (isset($form_record->id)) {
        $isUpdate = true;
        $formRoute = route('owner.machines.update', ['machine' => $form_record->id]);
    } else {
        $formRoute = route('owner.machines.store');
        $isUpdate = false;
    }
@endphp
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}"/>

    @endpush
    {!! Form::model($form_record,['class' => 'form-validate','method' => 'post','url' => $formRoute]) !!}
    @if($isUpdate)
        {{ Form::hidden('id', $form_record->id) }}
        {{ Form::hidden('_method', 'PUT') }}
    @endif
    @include('partials.form.page-head', [
     'actions' => [
        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
        ['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-primary ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attr' => ['key' => 'value']]
     ]])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Information'), __t('Machine'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @include('partials.form.input_template', [
                    'label' => __t('Name'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'name',
                    'type' => 'text',
                    'attributes' => ['required' => 'required']
                ])

                @include('partials.form.input_template', [
                    'label' => 'Status',
                    'inputClass' => 'form-control select',
                    'div' => 'col-md-6 form-group',
                    'name' => 'status',
                    'type' => 'select',
                    'options' => [1 => __t('Active'), 0 => __t('Inactive')],
                    'defaultValue' => $form_record ? (int) $form_record->status : 1,
                    'attributes' => ['required' => 'required', 'placeholder' => sprintf(__t("Select a %s"), __t('Status'))]
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Serial Number'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'serial_number',
                    'type' => 'text'
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Host Name'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'host',
                    'type' => 'text',
                    'attributes' => ['id' => 'host', 'placeholder' => sprintf(__t('e.g: %s'), 'dns.website.com or *************')]
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Port'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'port',
                    'type' => 'number',
                    'attributes' => ['placeholder' => sprintf(__t('e.g: %s'), '4022')]
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Communication Key'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'communication_key',
                    'type' => 'text'
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Machine Type'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'value' => isset($machine_type) ? $machine_type->name : $form_record->machineType->name,
                    'type' => 'text',
                    'attributes' => ['disabled' => 'disabled']
                ])
                @if (isset($machine_type))
                    <input type="hidden" name="machine_type_key" value="{{$machine_type->key}}" />
                @endif
            </div>
        </div>
    </div>
    {{csrf_field()}}
    {!! Form::close() !!}
    @push('scripts')
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
    @endpush
@endsection
