@php

    // Set default values and validate parameters
    use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;
    $id = isset($id) ? str_replace('[]','',$id) : 'StaffID';
    $inputClass = isset($inputClass) ? $inputClass : 'ui-input l-input l-input-small selectized';
    $inputClass.= ' custom-select';
    $inputClass = str_replace('staff-dropdown', '', $inputClass);
    $placeholder = isset($placeholder) ? $placeholder : __('Search by Name, Code, E-mail, Department, Branch, country...etc');
    $emptyOption = isset($emptyOption) ? (bool)$emptyOption : false;
    $customOption = isset($customOption) ? $customOption : null;
    $label = isset($label) ? $label : null;
    $name = isset($name) ? $name : null;
    $div = isset($div) ? $div : null;
    $value = isset($value) ? $value : null;
    $multiple = isset($attributes['multiple']) && (bool)$attributes['multiple'];
    // Validate required parameters
    if (!$name) {
        throw new InvalidArgumentException('Staff smart search element requires a "name" parameter');
    }
    if ($multiple) $name.= '[]';

    // Process default value - fetch staff data if ID provided
    $defaultOptions = [];
    if ($value) {
        // Load the SmartEmployeeSearchService to get staff data
        try {
            $selectedOptions = SmartEmployeeSearchService::getFormattedRecordById($value);
            if (!empty($selectedOptions)) {
                foreach ($selectedOptions as $selectedOption) {
                    $defaultOptions[] = [
                        'id' => $selectedOption['id'],
                        'text' => $selectedOption['text'],
                        'img' => $selectedOption['img']
                    ];
                }

            }
        } catch (Exception $e) {
            // If service fails, create basic option with just ID
            $defaultOptions[] = [
                'id' => $value,
                'text' => 'Staff ID: ' . $value,
                'img' => null
            ];
        }
    }

    // Generate empty option HTML if needed
    $emptyOptionHtml = ($emptyOption && !$multiple)
        ? '<option value=""' . ($multiple ? ' selected' : '') . '>' . __('Select Staff') . '</option>'
        : '';


@endphp

<style>
    .compat .selectize-control.form-control .selectize-dropdown {
        background: #fff !important;
    }
    .compat .selectize-control.form-control.multi .selectize-input {
        border-color: #ced4da !important;
            min-height: 38px;
    }
</style>
<div class="compat">

    <?php if ($div): ?>
    <div >
        <?php endif; ?>

        <?php if ($label): ?>
        <label for="<?= $id ?>"><?= $label ?></label>
        <?php endif; ?>

        <select id="<?= $id ?>" <?= $multiple ? 'multiple' : '' ?> name="<?= $name ?>" class="<?= $inputClass ?>">
            <?= $emptyOptionHtml ?>
        </select>

        <?php if ($div): ?>
    </div>
    <?php endif; ?>

</div>
<script>
    $(document).ready(function () {
        // Configuration
        const CONFIG = {
            elementId: '<?= $id ?>',
            searchUrl: '/v2/owner/staff/search?allow_inactive=1&term=q&_type=query&q=',
            customOption: <?= $customOption ? json_encode($customOption) : 'null' ?>,
            defaultOption: <?= $defaultOptions ? json_encode($defaultOptions) : '[]' ?>,
            placeholder: '<?= $placeholder ?>',
            allowEmptyOption: <?= $emptyOption ? 'true' : 'false' ?>
        };
        /**
         * Handles the search results and maintains custom persistent options
         * @param {Array} searchResults - Results from AJAX call
         * @param {Function} callback - Selectize callback function
         */
        function handleSearchResults(searchResults, callback) {
            const results = [];

            // Always add custom persistent option first if it exists
            if (CONFIG.customOption) {
                results.push(CONFIG.customOption);
            }

            // Add search results if available
            if (searchResults && searchResults.length > 0) {
                results.push(...searchResults);
            }

            callback(results);
        }

        /**
         * Creates the HTML template for an option/item
         * @param {Object} item - The item data
         * @param {Function} escape - Selectize escape function
         * @param {string} type - 'option' or 'item'
         * @returns {string} HTML template
         */
        function createTemplate(item, escape, type) {
            const className = type === 'option' ? 'option' : 'item';
            const imageSize = type === 'option' ? '30px' : '20px';

            if (!item.img) {
                return '<div class="' + className + '">' +
                    '<span>' + escape(item.text) + '</span>' +
                    '</div>';
            }

             return '<div title="' + escape(item.text) + '" class="' + className + ' option ui-select-template-client-employee"><div class="d-flex align-items-center"><span class="l-flex-col--shrink-0"><span class="l-user-img l-user-img--inline l-user-img--size-24 ui-user-img"><img src="' + escape(item.img) + '" /></span> &nbsp;&nbsp;</span><span>' + escape(item.text) + '</span></div></div>';
        }

        // Initialize Selectize
        const selectizeInstance = $('#' + CONFIG.elementId).selectize({
            valueField: 'id',
            labelField: 'text',
            searchField: 'text',
            loadThrottle: 300,
            allowEmptyOption: CONFIG.allowEmptyOption,
            placeholder: CONFIG.placeholder || false,
            plugins: [
    'multiple-plus',
    'clear-button',
    'option-deselect',
    'multiple-remove-button',
    'clear-option',
    'search-helper-text',
    { name: 'icon', options: { class: 'mdi mdi-magnify' } }
],
            render: {
                option: function(item, escape) {
                    return createTemplate(item, escape, 'option');
                },
                item: function(item, escape) {
                    return createTemplate(item, escape, 'item');
                }
            },

            load: function(query, callback) {
                if (!query.length) {
                    this.clearUnselectedOptions();
                    return callback();
                }

                const self = this;

                $.ajax({
                    url: CONFIG.searchUrl + encodeURIComponent(query),
                    type: 'GET',
                    dataType: 'json',
                    timeout: 10000, // 10 second timeout

                    error: function() {
                        self.clearUnselectedOptions();
                        handleSearchResults([], callback);
                    },

                    success: function(response) {
                        self.clearUnselectedOptions();
                        if (!response.results) {
                            self.refreshOptions();
                        }
                        handleSearchResults(response.results, callback);
                    }
                });
            }
        });

        // Get Selectize instance
        const selectize = selectizeInstance[0] ? selectizeInstance[0].selectize : null;

        if (selectize) {
            // Add custom persistent option on initialization
            if (CONFIG.customOption) {
                selectize.addOption(CONFIG.customOption);
            }

            // Set default value if provided
            if (CONFIG.defaultOption.length) {

                CONFIG.defaultOption.forEach(option => {
                    selectize.addOption(option);
                })
                const ids = CONFIG.defaultOption.map(option => option.id);
                selectize.setValue(ids);
            }
        }

    });
</script>
