<span class="d-md-inline-block mob-trigger-show d-none">
    <div class="btn-group">
        <div class="dropdown">
            <button type="button" class="btn btn-secondary dropdown-toggle top_head_actions" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="far fa-ellipsis-v d-md-none d-inline-block"></i><span class="d-none d-md-inline-block">{{__t('Actions')}} <i class="ml-2 caret"></i></span>
            </button>
            <div class="dropdown-menu" id="listing-select-actions" x-placement="bottom-start" style="position: absolute; transform: translate3d(0px, 38px, 0px); top: 0px; left: 0px; will-change: transform;">
                @foreach($multi_listing_actions as $k => $action)
                    @if(!empty($action['rendered']))
                        {!! $action['rendered'] !!}
                    @elseif(isset($action['prompt']))
                        <a
                            data-toggle="modal"
                            data-target="#promptModalMultiDelete"
                            data-prompt-multi="true"
                            data-prompt-header="{{$action['prompt']['header']}}"
                            data-prompt-message="{{$action['prompt']['message']}}"
                            data-prompt-method="{{$action['prompt']['method']}}"
                            data-href="{{$action['url']['name']}}?{{http_build_query(request()->query())}}"
                            class="GoSubmit multi-action-item dropdown-item"
                        >

                            {!!  $action['icon']?:'' !!}{{__t($action['title'])}}
                        </a>
                    @elseif(isset($action['bulk']))
                        <a 
                            href="#" 
                            data="{{$action['bulk']['data']}}" 
                            class="GoBulk multi-action-item dropdown-item {{$action['bulk']['ajax_bulk']}}"
                            rel="{{$action['bulk']['action']}}"
                        >
                            {{$action['bulk']['title']}}
                        </a>
                    @else
                        <a href="{{$action['url']['name']}}" data-method="{{$action['method']}}" class="GoSubmit multi-action-item dropdown-item" onclick="{{isset($action['onclick'])?$action['onclick']:''}}">{!!  $action['icon']?:'' !!}{{__t($action['title'])}}</a>
                    @endif
                @endforeach
            </div>


        </div>
    </div>
</span>
@push('scripts')
<script>
    var filterUrl = "{!! http_build_query($filtered, '', '&') !!}";
    $('.multi-action-item').on('click', function (e) {
        e.preventDefault();

        multiActionUrl = $(this).attr('href');
        requestMethod = $(this).data('method');
        if(multiActionUrl)
        {
            if(filterUrl)
            {
                if(multiActionUrl.includes('?') === false)
                {
                    multiActionUrl += '?' + filterUrl;
                }else{
                    multiActionUrl += '&' + filterUrl;
                }
            }

            if(requestMethod)
            {
                $('#multiActionMethod').removeAttr('disabled');
                $('#multiActionMethod').val(requestMethod);
            }else{
                $('#multiActionMethod').attr('disabled','disabled');
            }
            if ($(this).hasClass('ajax_bulk'))
            {
                if ($('#index_action_select_type').length) {
                    var rowCheckboxes = $('.check-input');
                    for (var rowCheckbox of rowCheckboxes) {
                        rowCheckbox.name = '';
                    }
                    $('<input>').attr({
                        type: 'hidden',
                        value: 'all',
                        name: 'id'
                    }).appendTo($('#MultiSelectForm'));
                }
                if (!$(this).hasClass('GoBulk') && ($('.check-input:checked').length > 0 || $('input[id="select_all_chk"]').attr('value') == 'all')) {
                    $('#MultiSelectForm').attr('action', multiActionUrl);
                    $('#MultiSelectForm').submit();
                }
            } else {
                $('#MultiSelectForm').attr('action', multiActionUrl);
                $('#MultiSelectForm').submit();
            }
        }
    })

    function addMultiIDsURL(element)
    { 
        idsArr = [];
        objectsArr = $('input[name="id[]"]:checked');
        objectsArr.each(function() {
            value = $(this).val();
            idsArr.push(value);
        });

        uncheckedArr = $('input[name="id[]"]:not(:checked)')
        if(uncheckedArr.length > 0)
            $('#index_action_select_type').remove();

        $(element).attr('href', function() { // Comma separated

            var append_status = this.href.indexOf("?") > -1 ? '&' : '?';

            if($('#index_action_select_type').prop('checked')){
                return this.href + `${append_status}ids=all`;
            }

            if(objectsArr.length == 0){
                return this.href + `${append_status}ids=none`;
            }

            return this.href + `${append_status}ids=` + (idsArr + "");
        });
    }


    function addMultiIDsURLDiffName(element)
    { 
        idsArr = [];
        objectsArr = $('input[name="ids[]"]:checked');
        objectsArr.each(function() {
            value = $(this).val();
            idsArr.push(value);
        });

        uncheckedArr = $('input[name="ids[]"]:not(:checked)')
        if(uncheckedArr.length > 0)
            $('#index_action_select_type').remove();

        $(element).attr('href', function() { // Comma separated

            var append_status = this.href.indexOf("?") > -1 ? '&' : '?';

            if($('#index_action_select_type').prop('checked')){
                return this.href + `${append_status}ids=all`;
            }

            if(objectsArr.length == 0){
                return this.href + `${append_status}ids=none`;
            }

            return this.href + `${append_status}ids=` + (idsArr + "");
        });
    }

    function addMultiIDsDeleteURL(element)
    {
        idsArr = [];
        objectsArr = $('input[name="id[]"]:checked');
        objectsArr.each(function() {
            value = $(this).val();
            idsArr.push(value);
        });

        uncheckedArr = $('input[name="id[]"]:not(:checked)')
        if(uncheckedArr.length > 0)
            $('#index_action_select_type').remove();

        form = $(element).parents('.modal').find('#row-action-formMultiDelete');
        form.attr('action', function() {
           var append_status = this.action.indexOf("?") > -1 ? '&' : '?'
            if($('#index_action_select_type').prop('checked')){
                return this.action +append_status+ 'ids=all';
            }

            if(objectsArr.length == 0){
                return this.action +append_status+ 'ids=none';
            }
            else if(objectsArr.length > 0){
                return this.action +append_status+  'ids='+(idsArr+"");
            }
            else{
                return this.action;
            }
        });
    }


</script>
@endpush
