<div class="form-group">
    @php
        $options= [];
        if (Permissions::checkPermission(MANAGE_DRAFT_JOURNALS)) {
            $options['draft'] = __t("Draft", true);
        }
        if (Permissions::checkPermission(MANAGE_ALL_JOURNALS) || Permissions::checkPermission(MANAGE_OWN_JOURNALS)) {
            $options['issued'] = __t("Issued", true);
        }
    @endphp
    @include('partials.form.input_template', ['attributes' => ['required' => 'required', 'placeholder' => __t('Journal Status')], 'options' => $options,'label' => 'Journal Status','inputClass' => 'select form-control','name' => 'extraData[status]', 'type' => 'select'])
</div>

<div class="form-group">
    @include('partials.form.input_template', ['label' => __t('Auto-generate Journal Numbers'),'labelClass' => 'custom-control-label','inputClass' => 'custom-control-input','div' => 'custom-control custom-checkbox p-0','name' => 'extraData[auto_generate_nums]', 'type' => 'checkbox', 'value' => 1])
</div>
