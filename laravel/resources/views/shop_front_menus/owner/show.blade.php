@extends('layouts.owner')
@inject('Plugins', 'App\Facades\Plugins')
@inject('PluginUtil', 'App\Utils\PluginUtil')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/activity_log/activity_log.min.css"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/staff/notes.min.css"/>
@endpush

@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-6">
                    <div class="pages-head-title">
                        <h2>{{$record->name}} {{$record->last_name}} <span style="margin-right: 10px"
                                                                           class="status status-{{$record->active ? 'active' : 'inactive'}}"><i
                                    class="cir"></i>{{$record->active ? __t('Active') : __t('Inactive')}}</span></h2>
                        <small class="text-muted">#{{$record->id}}</small>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="action-buttons">
                        @include('partials.show.record_nav', ['url' => 'owner.staff.show', 'name' => 'staff'])
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header p-3 bg-white">
            <div class="row">
                <div
                    class="col-xl-8 col-lg-8 col-12 d-flex flex-wrap justify-content-center justify-content-sm-start text-center text-sm-left">
                    <div class="flex-shrink-1">
                        <div class="media text-center">
                            <img
                                src="{{$record->photo ? $record->image : getCdnAssetsUrl() . 'imgs/account-def-lg.png'}}"
                                class="img-fluid avatar-lg" alt="name">
                        </div>
                    </div>
                    <div class="avatar px-4 pt-3 pt-sm-0">
                        <h4>{{$record->name}} {{$record->middle_name}} {{$record->last_name}}</h4>
                        <small class="text-muted">#{{$record->id}}</small>
                    </div>
                </div>
                <div class="col-12 d-lg-none">
                    <br>
                </div>
                <div class="col-xl-4 col-lg-4 col-12">
                    @if($record->mobile)
                        <a href="tel:{{$record->mobile}}" class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-cellphone"></i>{{$record->mobile}}</a>
                    @endif
                    @if($record->email_address)
                        <a href="mailto:{{$record->email_address}}" class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-email-variant"></i>{{$record->email_address}}</a>
                    @endif
                    @if($record->can_access_system)
                        <a href="{{getCakeURL(['controller' => 'staffs', 'action' => 'login_as', $record->id])}}"
                           class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-account-box"></i>{{sprintf(__t('Login as %s'), $record->full_name ?: '')}}
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="card border-white mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-multi-line btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                <a href="{{route('owner.staff.edit',['id' => $record->id])}}" class="btn btn-light"><i
                        class="mdi mdi-pencil-outline mr-1"></i>{{__t('Edit')}}</a>
                @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Employee'), 'href' => route('owner.staff.destroy', ['id' => $record])])
                <a href="{{getCakeURL(['controller' => 'posts', 'action' => 'post', 5, $record->id])}}"
                   class="btn btn-light"><i
                        class="mdi mdi-note-text mr-1"></i>{{sprintf('%s %s / %s', __t('Add'), __t('Note(s)'), __t('Attachment(s)')) }}
                </a>

                @if($record->active)
                    <a href="{{ route('owner.staff.deactivate', ['id' => $record->id]) }}" class="btn btn-light"><i
                            class="fas fa-comment-alt-edit mr-1"></i>{{ __t('Mark as Inactive') }}</a>
                @else
                    <a href="{{ route('owner.staff.activate', ['id' => $record->id]) }}" class="btn btn-light"><i
                            class="fas fa-comment-alt-edit mr-1"></i>{{ __t('Mark as Active') }}</a>
                @endif
                @if($record->can_access_system)
                    <a href="{{ route('owner.staff.sendLoginDetails', ['id' => $record->id]) }}" class="btn btn-light"><i class="mdi mdi-page-next-outline text-purple mr-1"></i>{{ __t('Send Login Details') }}</a>
                    <a href="{{ route('owner.staff.changePassword', ['id' => $record->id]) }}" class="btn btn-light"><i class="mdi mdi-lock-reset text-orange mr-1"></i>{{ __t('Change Password') }}</a>
                @endif
            </div>
        </div>
        <div class="card-body p-1 p-lg-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab"
                           aria-controls="details" aria-selected="true">{{ __t('Details') }}</a>
                    </li>
                    @if($notesCount)
                    <li class="nav-item">
                        <a class="nav-link" id="NotesBlock-tab" data-toggle="tab" href="#notes-attachments"
                           role="tab" aria-controls="notes-attachments" aria-selected="false"

                           data-notes-url="{{getCakeURL([
                           'controller' => 'posts',
                           'action' => 'list_post',
                           \App\Utils\PostTypesUtil::STAFF_TYPE,
                           $record->id
                           ])}}">
                            {{ sprintf('%s / %s', __t('Note(s)'), __t('Attachment(s)')) }} ({{$notesCount}})
                        </a>
                    </li>
                    @endif
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab"
                           aria-controls="activity-log" aria-selected="false">{{ __t('Activity Log') }}</a>
                    </li>

                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">

                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Employee Information') }}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Mobile Number') }}:</p>
                                        <p>{{$record->mobile}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Phone Number') }}:</p>
                                        <p>{{$record->home_phone}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Address') }}:</p>
                                        <p>
                                            @if($record->address1)
                                                {{$record->address1}}<br>
                                            @endif
                                            @if($record->address2)
                                                {{$record->address2}}<br>
                                            @endif
                                            {{(!empty($record->city))? $record->city.', ':''}}{{(!empty($record->state))? $record->state.', ':''}} {{is_rtl() ? $record->country->ar_name : $record->country->country}}
                                            <span>({{$record->postal_code}})</span>
                                        </p>
                                    </div>
                                </div>
                                @if(isset($record->note))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Notes') }}:</p>
                                            <p>{{$record->note}}</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Account Information') }}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Email') }}:</p>
                                        <p>{{$record->email_address}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Role') }}:</p>
                                        <p>{{$record->role? $record->role->name:'--'}}</p>
                                    </div>
                                </div>
                                @if($Plugins::pluginActive($PluginUtil::BranchesPlugin))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Accessible Branches') }}:</p>
                                            <p>{{$record->branches->implode('name', ', ')}}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Branch') }}:</p>
                                            <p>{{$record->branch->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(Plugins::pluginActive(\App\Utils\PluginUtil::BookingPlugin) && isset($record->shift) && $record->shift->count() > 0)
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Booking Shift') }}:</p>
                                            <p>{{$record->shift->implode('name', ', ')}}</p>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Display Language') }}:</p>
                                        <p>{{ (!is_null($record->language))? (is_rtl()? $record->language->ar_name : $record->language->name):'' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if(isset($customViewFields) && !empty($customViewFields))
                            <div class="panel">
                                @include('partials.custom_forms.view_fields', ['customViewFields' => $customViewFields])
                            </div>
                        @endif
                    </div>
                    <div class="tab-pane p-3 fade" id="account-information" role="tabpanel"
                         aria-labelledby="account-information-tab">

                        <div class="p-3">
                            <div class="row">

                            </div>
                        </div>
                    </div>
                    @if($notesCount)
                    <div class="tab-pane p-3 fade" id="notes-attachments" role="tabpanel"
                         aria-labelledby="NotesBlock-tab">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ sprintf('%s / %s', __t('Note(s)'), __t('Attachment(s)')) }}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="notes-attachments-content">
                        @include('partials.layout.loader')
                        <!-- notes & attachments will be here -->
                        </div>
                    </div>
                    @endif
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">
                        <input type="hidden" value="{{\App\Utils\EntityKeyTypesUtil::STAFF_ENTITY_KEY}}"
                               id="entity_key"/>
                        <input type="hidden" value="{{$record->id}}" id="entity_id"/>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Activity Log') }}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="notesDeletePromptModal" tabindex="-1" role="dialog" aria-labelledby="notesDeletePromptModalLabel"
                 style="display: none;" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-transparent border-0 position-absolute w-100" style="z-index: 9;">
                            <h5 class="modal-title d-none" id="notesDeletePromptModalLabel"></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body text-center">
                            <i class="mdi mdi-trash-can-outline display-4 text-danger"></i>
                            <div id="notes-delete-prompt-body">
                                {{sprintf(__t('Are you sure you want to delete this %s ?'),__t('Note') )}}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <form method = 'post' id = 'deleteNoteForm' action="">
                                <input type ='hidden' name="data[Post][action]" value="delete">
                                <button type="submit" name = 'submit_btn' value='yes'  class="btn btn-danger notes-delete-btn">{{__t('Yes')}}</button>
                            </form>
                            <button type="button" class="btn btn-secondary"
                                    data-dismiss="modal">{{__t('No')}}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="notes-alert" class="alert alert-icon alert-bordered alert-dismissible border-success alert-success d-none">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="d-flex">
                    <div class="alert-icon">
                        <i class="far fa-lg fa-check-circle text-success mr-3"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-0"><strong id="notes-msg"></strong></p>
                    </div>
                </div>
            </div>

            @include('partials.modals.create_delete_modal')

        </div>
    </div>
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script>
        $(function () {
            promptHeaderEl = $('#promptModalLabel');
            promptBodyEl = $('#prompt-body');
            promptMethodEl = $('#prompt-method');
            propmptFormEl = $('#row-action-form');
            $('a[data-prompt="true"]').on('click', function (event) {
                event.preventDefault();
                message = $(this).data('prompt-message');
                requestMethod = $(this).data('prompt-method');
                header = $(this).data('prompt-header');
                action = $(this).attr('href');
                promptHeaderEl.text(header);
                promptBodyEl.text(message);
                promptMethodEl.val(requestMethod);
                propmptFormEl.attr('action', action);
                propmptFormEl.attr('method', 'post');
            });

        });
    </script>
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    @if($notesCount)
    <script src="{{asset('/js/notes/ajax-notes.js')}}"></script>
    @endif
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
@endpush
