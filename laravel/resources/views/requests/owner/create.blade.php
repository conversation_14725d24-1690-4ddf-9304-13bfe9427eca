{{-- deprecated --}}
@extends('layouts.owner')
@php
    use App\Utils\EntityFieldUtil;
    // $form->prepare();
    $row = new \Izam\Forms\View\Helper\Form\FormRow();
    $listRoute = route('owner.entity.list', ['entityKey' => 'le_request_type_' . request()->request_type, 'filter[action]' => 'opened']);
    if (isset($form_record->id)){
        $isUpdate = true;
        $formRoute = route('owner.requests.update', ['request_type' => $form_record->requestType->slug ?? null , 'request' => $form_record->id]);
    } else {
        $formRoute = route('owner.requests.store',request()->request_type);
        $isUpdate = false;
    }
    if (!empty(old()) && !isset($staffOptionsAttributes)) {
        $staffOptionsAttributes = old('staffOptionsAttributes') ?? null;
    }
    if (!empty(old()) && !isset($staffOptions)) {
        $staffOptions = old('staffOptions') ?? null;
    }
    $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
@endphp
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css"/>
    @endpush

    {!! Form::model($form_record,['files' => true,'class' => 'form-validate','method' => 'post','url' => $formRoute]) !!}
    @if($isUpdate)
        {{ Form::hidden('id', $form_record->id) }}
        {{ Form::hidden('_method', 'PUT') }}
    @endif
    @include('partials.form.page-head', [
     'actions' => [
        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attributes' => ['key' => 'value','href' => $listRoute],'template' => "link"],
        ['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attributes' => ['key' => 'value']]
     ]])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Information'), __t('Request'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @php
                    $jsFormats = getDateFormats('new_js');
                    $siteFormat = getCurrentSite('date_format');
                    $siteFormat = strtoupper($jsFormats[$siteFormat]);
                    $permissionService = resolve(\App\Services\EntityPermissionService::class)
                @endphp

              @if((isStaff() && EntityPermission::checkEmployeeHasPermission(getAuthOwner('staff_id'),$request_type_id,\App\Utils\EntityKeyTypesUtil::REQUEST_ENTITY_KEY,\App\Utils\EntityPermissionKeyUtil::CREATE_FOR_OTHERS)) || isOwner())
                    @include('partials.form.input_template',[
                    'label' => __t('Employee'),
                    'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                    'div' => 'col-md-6 form-group',
                    'name' => 'staff_id',
                    'id' => 'staff_id',
                    'attributes' => ['placeholder' => $staffInputPlaceholder ,'required' => true, 'data-staff-url' => route('owner.staff.searchForRequests') . "?request_type= $request_type_id&limit=10"],
                    'type' => 'selectStaff',
                    'options' => isset($staffOptions) ? $staffOptions : [],
                    'value' => isset($form_record->staff_id) ? $form_record->staff_id: old('staff_id', ''),
                ])
                    <div class="col-md-6"></div>
              @else
                    @if(!$isUpdate)
                    <input type="hidden" id="staff_id" name="staff_id" value="{{getAuthStaff('id')}}">
                    @endif
              @endif


                @include('partials.form.input_template', [
                    'label' => __t('Date of Application'),
                    'inputClass' => 'form-control',
                    'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'value' => isset($form_record->application_date) ? formatForView($form_record->application_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('application_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                    'name' => 'application_date',
                    'id' => 'application_date',
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])
                @include('partials.form.input_template', [
                    'label' => __t('Execution Date'),
                    'inputClass' => 'form-control',
                    'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'value' => isset($form_record->execution_date) ? formatForView($form_record->execution_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('execution_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                    'name' => 'execution_date',
                    'id' => 'execution_date',
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])
                @include('partials.form.input_template', [
                    'label' => __t('Description'),
                    'inputClass' => 'form-control',
                    'attributes' => ['required' => 'required','rows' => '5', 'style' => 'height:120px'],
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'value' => isset($form_record->description) ? $form_record->description : old('description'),
                    'name' => 'description',
                    'id' => 'description',
                    'type' => 'textArea',
                ])

                <div class="col-md-6">
                    @include('partials.form.input_template', [
                        'label' => __t("Attachments"),
                        'inputClass' => 'form-control',
                        'div' => 'form-group mb-0',
                        'id' => 'attachments',
                        'name' => 'attachments',
                        'value' => isset($files) ? $files : null,
                        'attributes' => [
                            'accepts' => 'application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint,text/plain,application/pdf,image/*|rar|zip',
                            'multiple' => true,
                            'max_file_size' => 5
                             ],
                        'type' => 'file',
                        'after' => '<small class="form-text text-muted">'.sprintf(__t('Max file size %d %s'),5,__t("MB")).' </br>'. sprintf(__t('Allowed file types: (%s)'), implode(',', ['png,jpg,gif,bmp,rar,zip and all office files'])) . '</small>',


                    ])
                </div>
            </div>
        </div>
    </div>
    @include('partials/custom_forms/form_fields', ['header' => __t("Request More Information")])
    {{csrf_field()}}
    {!! Form::close() !!}
    @push('scripts')
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
    @endpush
@endsection
