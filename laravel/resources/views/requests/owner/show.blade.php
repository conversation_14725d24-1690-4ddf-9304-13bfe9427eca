@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css"/>
    <link rel="stylesheet" href="/v2/css/history-status.css">
@endpush
@php
use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;
use App\Modules\LocalEntity\Services\MobileActivityLog\MobileActivityLogCreatorFactory;
$authId = getAuthOwner('staff_id');
$requestService = resolve(\App\Services\RequestService::class);
$multiCycleApprovalConfigurationService = resolve(MultiCycleApprovalConfigurationService::class);
$matchedConfigurationLabel = $multiCycleApprovalConfigurationService->getConfigurationLabel($record);

$mobileLogCreator = MobileActivityLogCreatorFactory::create(\App\Utils\EntityKeyTypesUtil::REQUEST_ENTITY_KEY);
$logs = $mobileLogCreator->create($record);

if(isset($record->approval_cycle_configuration_id)){
    $canApproveMultiCycle = $multiCycleApprovalConfigurationService->isRequestApprover(getAuthOwner('staff_id'), $record);
    $canUndoMultiCycle =  $multiCycleApprovalConfigurationService->isLastApprover(getAuthOwner('staff_id'), $record);
}else{
    $canUndoMultiCycle = $canApproveMultiCycle = ( \App\Facades\EntityPermission::checkEmployeeHasPermission(getAuthOwner('staff_id'),$record->request_type_id,\App\Utils\EntityKeyTypesUtil::REQUEST_ENTITY_KEY,\App\Utils\EntityPermissionKeyUtil::APPROVE_REJECT) && ($record->staff_id == getAuthOwner('staff_id') && $approve_reject_my_requests || $record->staff_id !== (int) getAuthOwner('staff_id') ) );
}

if($authId)
    $canApprove = \App\Facades\EntityPermission::checkEmployeeHasPermission($authId,$record->requestType->id,\App\Utils\EntityKeyTypesUtil::REQUEST_ENTITY_KEY,\App\Utils\EntityPermissionKeyUtil::APPROVE_REJECT);
else
    $canApprove = true;
@endphp
@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-9">
                    <div class="pages-head-title">
                        <h2 class="d-flex align-items-center">
                            <div>
                                <div class="d-inline-block mr-2">
                                    <span class="fs-24"><i class="{{$record->requestType->icon_key ?? ""}}"></i></span>
                                </div>
                                {{$record->requestType->name . " ".__t("Request")}} #{{$record->id}}
                                <div class="d-inline-block">
                                <span
                                    class="ml-2 status {{\App\Utils\Requests\RequestStatusUtil::getStatusClass($record->status)}}">
                                    <?php
                                        $requestService = resolve(\App\Services\RequestService::class);
                                        $main_status = $requestService->getRequestStatus($record);
                                    ?>
                                    <i class="cir"></i><span
                                        class="status-text">{{$main_status}}</span>
                                </span>
                                </div>
                            </div>
                        </h2>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="action-buttons">
                        <div class="btn-group" role="group" aria-label="padination">
                            @if(($record->status == \App\Utils\Requests\RequestStatusUtil::APPROVED || $record->status == \App\Utils\Requests\RequestStatusUtil::REJECTED ) && $canApprove)
                                @if($record->action == \App\Utils\Requests\RequestActionsUtil::OPENED)
                                    <a href="{{route('owner.requests.close',[request('request_type'),$record->id])}}"
                                       class="btn btn-danger mr-2">{{__t("Close")}}</a>
                                @elseif($record->action == \App\Utils\Requests\RequestActionsUtil::CLOSED)
                                    <a href="{{route('owner.requests.open',[request('request_type'),$record->id])}}"
                                       class="btn btn-success mr-2">{{__t("Reopen")}}</a>
                                @endif
                            @endif
                            <div class="col-sm-6">
                                @include('partials.show.record_nav', ['url' => 'owner.requests.show', 'name' => 'request','params' => ['request_type' => request('request_type')]])
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="card border-white mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                @if( $record->staff_id == getAuthOwner('staff_id') || EntityPermission::checkEmployeeHasPermission(getAuthOwner('staff_id'),$record->request_type_id,\App\Utils\EntityKeyTypesUtil::REQUEST_ENTITY_KEY,\App\Utils\EntityPermissionKeyUtil::CREATE_FOR_OTHERS))
                    @if($record->status == \App\Utils\Requests\RequestStatusUtil::PENDING)

                    @endif
                        <a href="{{route('owner.requests.edit', [request('request_type') , $record->id])}}"
                           class="btn btn-light">
                            <i class="mdi mdi-pencil-outline mr-1"></i> {{__t('Edit')}}
                        </a>
                    @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Request'), 'href' => route('owner.requests.destroy', [request('request_type') , $record->id])])
                @endif
                @include('partials.templates.printables_dropdown', ['view_templates' => $view_templates, 'id' => $record->id])
                @include('partials.templates.send_email_templates_btn', ['view_templates' => $email_templates, 'id' => $record->id])
            </div>
        </div>

        <div class="card-body">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab"
                           aria-controls="details"
                           aria-selected="true">{{sprintf(__t("%s Information"),__t("Request"))}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab"
                           aria-controls="activity-log" aria-selected="false">{{__t('Activity Log')}}</a>
                    </li>
                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">
                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel"
                         aria-labelledby="details-tab">
                        <div class="card bg-light p-3">
                            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center">
                                <div class="w-auto w-sm-100 mb-md-0 mb-3">
                                    <div class="media d-flex align-items-center view-action">
                                        <div class="avatar">
                                            <img
                                                src="{{isset($record->staff->image) && $record->staff->image ? $record->staff->image : \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($record->deletedStaff->name, $record->deletedStaff->id, 30, null)}}"
                                                class="mr-2 avatar-sm rounded" width="30px" alt="name" style="border-radius: 2px;">
                                        </div>
                                        <div class="media-body">
                                            <h3 class="m-0 lh-1 fs-16 font-weight-bold">{{$record->deletedStaff->full_name ?? ""}}</h3>
                                            @if($record->deletedStaff->deleted_at == null)
                                                <a href="{{route('owner.staff.show',$record->staff_id)}}" target="_blank">
                                                    <small class="media-desc text-decoration-underline">#{{$record->staff?->code ??$record->staff_id}}</small>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="w-auto w-sm-100 mb-md-0 mb-3">
                                    <div class="d-flex align-items-center justify-content-md-center">
                                        <div class="p-2 lh-1">
                                            <p class="lh-1 mb-2 text-muted font-weight-medium">{{__t("Application Date")}}</p>
                                            <span>{{formatForView($record->application_date,\App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</span>
                                        </div>
                                        <span class="separator-line mx-2"></span>
                                        <div class="p-2 lh-1">
                                            <p class="lh-1 mb-2 text-muted font-weight-medium">{{__t("Execution Date")}}</p>
                                            <span>{{formatForView($record->execution_date,\App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-auto w-sm-100 mb-md-0 mb-3">
                                        <div class="d-flex align-items-center flex-wrap">
                                        @if($canUndoMultiCycle)
                                            @if(!empty($record->getApprovalData()) && (count($record->getApprovalData()->approvers) > 0 && $record->status == \App\Utils\Requests\RequestStatusUtil::PENDING ) || $record->status == \App\Utils\Requests\RequestStatusUtil::APPROVED)
                                                <a href="#" id="undoApprovalBtn" data-request-id="{{$record->id}}" data-request-type="{{request('request_type')}}"
                                                class="btn btn-secondary btn-icon mr-2 mb-2"><i
                                                        class="fas fa-undo"></i>{{__t("Undo Approval")}}</a>
                                            @elseif($record->status == \App\Utils\Requests\RequestStatusUtil::REJECTED)
                                                <a href="#" id="undoRejectionBtn" data-request-id="{{$record->id}}" data-request-type="{{request('request_type')}}"
                                                class="btn btn-secondary btn-icon mr-2 mb-2"><i
                                                        class="fas fa-undo"></i>{{__t("Undo Rejection")}}</a>
                                            @endif
                                        @endif
                                        @if($canApproveMultiCycle)
                                            @if($record->status == \App\Utils\Requests\RequestStatusUtil::PENDING)
                                                <a href="{{route('owner.requests.reject',[request('request_type'),$record->id])}}"
                                                   class="btn btn-danger mr-2 mb-2 btn-icon"><i
                                                        class="far fa-save"></i>{{__t("Reject")}}</a>
                                                <a href="{{route('owner.requests.approve',[request('request_type'),$record->id])}}"
                                                   class="btn btn-success mr-2 mb-2 btn-icon"><i
                                                        class="fas fa-check-circle"></i>{{__t("Approve")}}</a>
                                            @endif
                                        @endif
                                        </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-md-3 p-4 card bg-light">
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="mb-4">
                                        <p class="text-muted font-weight-medium mb-1">{{__t('Description')}}</p>
                                        <p class="pre">{{$record->description}}</p>
                                    </div>
                                </div>
                                <div class="col-md-1"></div>
                                <div class="col-md-6 pl-md-0">
                                    <div class="d-flex">
                                        <div class="separator-line d-none d-md-inline-block separator-line-xl"></div>
                                        @if(count($record->attachments))
                                            <div class="pl-md-3 mb-4">
                                                <p class="text-muted font-weight-medium mb-1">{{{__t("Attachments")}}}</p>
                                                    @foreach($record->attachments as $file)
                                                        @include('partials.file_preview', ['alt' => $file->name, 'name' => mb_substr($file->name, 0, 15), 'url' => $file->url, 'size' => formatFilesSize($file->file_size)])
                                                    @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if($record->status == \App\Utils\Requests\RequestStatusUtil::REJECTED)
                            <div class="mt-md-3 p-4 border-left border-danger rounded-sm bg-white">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted font-weight-medium mb-1">{{{__t("Rejection Reason")}}}</p>
                                            <p>{{$record->rejection_description}}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted font-weight-medium mb-1">{{{__t("Rejection Date")}}}</p>
                                            <p>{{formatForView($record->rejection_date,\App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        @endif

                            @if(isset($matchedConfigurationLabel) )
                                <div class="panel mt-3">
                                    <div class="panel-head">
                                        <h3>{{__t("Approval Configuration")}}</h3>
                                    </div>
                                </div>
                                <div class="p-3">
                                    <span class="text-decoration-underline"><?= $matchedConfigurationLabel?></span>
                                </div>
                            @endif
                            <?php if(count($logs)):?>
                                <div class="panel mt-3">
                                    <div class="panel-head">
                                        <h3>{{__t("Request History")}}</h3>
                                    </div>
                                </div>

                                <div class="p-3">
                                    <ul class="history-list">
                                        <?php foreach($logs as $log):
                                            $log = $log->jsonSerialize();
                                            if(!isset($log['title'])) continue;
                                        ?>
                                            <li class="history-list-item">
                                                <div class="history-list-item_icon item-approved"><img src="<?= $log['icon'] ?? ""?>" alt=""></div>
                                                <div class="history-list-item_content">
                                                    <p><?= $log['date'] ?? "" ?> - <?= $log['time']  ?? "" ?></p>
                                                    <h4><?=  $log['title'] ?></h4>
                                                    <?php if(isset($log['oldData']) && $log['newData'] &&  $log['oldData']['status'] !=  $log['newData']['status']) :?>
                                                    <div class="status-list l-flex l-flex--align-center l-flex--wrap">
                                                        <div class="item-status item-status-stroke"><?= $log['oldData']['status'] ?? ""?></div>
                                                        <i class="item-status-arrow mdi mdi-arrow-right"></i>
                                                        <div class="item-status "><?= $log['newData']['status'] ?? ""?></div>
                                                    </div>
                                                    <?php endif?>
                                                </div>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif ?>
                        <div class="panel mt-5">
                            @isset($form)
                                @include('partials.custom_forms.view_fields', [ 'header' =>  $record->requestType->name . __t(" More Information")])
                            @endisset
                        </div>
                    </div>
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">
                        <input type="hidden" value="{{\App\Utils\EntityKeyTypesUtil::REQUEST_ENTITY_KEY}}"
                               id="entity_key"/>
                        <input type="hidden" value="{{$record->id}}" id="entity_id"/>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{__t('Activity Log')}}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    @include('partials.modals.create_delete_modal')

    <!-- Modal for configuration rematch warning -->
    <div class="modal fade" id="configurationRematchModal" tabindex="-1" role="dialog" aria-labelledby="configurationRematchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="configurationRematchModalLabel">{{__t('Undo Action')}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>{{__t("Undoing this action will trigger the system to re-evaluate the leave application against the latest approval configurations. It may result in the request being reassigned to another configuration, and you might lose access to manage it further. Do you want to proceed?")}}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{__t('Cancel')}}</button>
                    <a href="#" id="confirmUndoBtn" class="btn btn-danger">{{__t('Confirm')}}</a>
                </div>
            </div>
        </div>
    </div>
    <!-- end page contents -->
@endsection
@push('scripts')
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
    <script>
        $(document).ready(function() {
            // Handle undo approval button click
            $('#undoApprovalBtn, #undoRejectionBtn').on('click', function(e) {
                e.preventDefault();

                const requestId = $(this).data('request-id');
                const requestType = $(this).data('request-type');
                const isApproval = $(this).attr('id') === 'undoApprovalBtn';
                const actionType = isApproval ? 'un-approve' : 'undo-reject';

                // Make AJAX request to check if configuration will rematch
                $.ajax({
                    url: '/v2/owner/request_types/' + requestType + '/requests/' + requestId + '/check-configuration-rematch',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.configurationWillRematch) {
                            // Show modal with warning if configuration will rematch
                            $('#configurationRematchModalLabel').text(isApproval ? "{{__t('Undo Approval')}}" : "{{__t('Undo Rejection')}}");
                            $('#confirmUndoBtn').attr('href', '/v2/owner/request_types/' + requestType + '/requests/' + requestId + '/' + actionType);
                            $('#configurationRematchModal').modal('show');
                        } else {
                            // Directly redirect if no rematch needed
                            window.location.href = '/v2/owner/request_types/' + requestType + '/requests/' + requestId + '/' + actionType;
                        }
                    },
                    error: function() {
                        // In case of error, default to showing the modal
                        $('#configurationRematchModalLabel').text(isApproval ? "{{__t('Undo Approval')}}" : "{{__t('Undo Rejection')}}");
                        $('#confirmUndoBtn').attr('href', '/v2/owner/request_types/' + requestType + '/requests/' + requestId + '/' + actionType);
                        $('#configurationRematchModal').modal('show');
                    }
                });
            });
        });
    </script>
@endpush

