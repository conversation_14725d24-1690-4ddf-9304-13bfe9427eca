@extends('layouts.owner')
@inject('SalaryComponentTypeUtil', 'App\Utils\SalaryComponentTypeUtil')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/index/index.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/forms/sortable_dynamic_rows.min.css?v={{CSS_VERSION}}"/>

    <style>
        .sortable-dynamic-rows-row.is-auto .form-group-textarea-formula{
            display: none;
        }

        .sortable-dynamic-rows-row:not(.is-auto) .form-group-textarea-auto{
            display: none;
        }
    </style>

@endpush
@php
    use App\Utils\EntityFieldUtil;
    use Izam\Daftra\Staff\Services\SmartEmployeeSearchService;

    $route = Route::currentRouteName();

    $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());

    $actionName = explode('.',$route);

    $actionName = (isset($actionName[2])) ? $actionName[2]:  null;

    $old = old();

    $selectedStaff = request()->staff ?? null;
    if ($selectedStaff){
        $staffService = resolve(\App\Services\StaffService::class);
         $staff = $staffService->find($selectedStaff);
        if ($staff) {
            $staffOptions[] = \App\Facades\Staff::getStaffOptionFormated($staff) ;
        }
    }
if($old)
{
    $salaryComponents = old('contractComponent');
    $basicComponentAmt = old('contractComponent')['earning']['basic']['amount'];
    unset($salaryComponents['earning']['basic']);
}else{
    $basicComponentAmt = $basicComp->amount;

}
    if(!$form_record)
    {
        $route = route('owner.contracts.store');
        $method = 'POST';
    }else{
        $route = route('owner.contracts.update', ['id' => $form_record->id]);
        $method = 'PUT';
    }

    if(isset($action) && is_array($action)){
        $route = $action['route'];
        $method = $action['method'];
    }
@endphp
@section('content')
    {!! Form::model($form_record,['files' => true,'class' => 'form-validate','method' => $method,'url' => $route]) !!}
    @if(!$form_record || $actionName == 'renew' || $actionName == 'supersede'  )
        <input name="is_draft" id="isDraftInput" type="hidden" value="0"/>
    @endif
    @include('partials.form.page-head', [
        'title' => '',
        'actions' => [
            [
                'title' => __t('Cancel'),
                'type' => 'button',
                'name' => 'cancel',
                'class' => 'btn-icon btn-secondary cancel-btn',
                'value' => '',
                'icon' => '<i class="mdi mdi-close"></i>',
                'id' => 'cancel',
                'attr' => ['key' => 'value']
            ],
            [
                'template' =>( !$form_record || $actionName == 'renew' || $actionName == 'supersede' ) ? 'multiple' : '',
                'title' => __t('Save'),
                'type' => 'submit',
                'name' => '',
                'class' => 'btn-icon btn-success ml-sm-2',
                'value' => '',
                'icon' => '<i class="mdi mdi-content-save-outline"></i>',
                'id' =>'submitBtn',
                'attr' => ['key' => 'value'],
                'actions' =>
                    [
                        [
                        'title' => __t('Save as Draft'),
                        'url' => '#',
                        'attributes' => ['id' => 'saveAsDraftBtn']
                        ]
                    ]
            ]
        ]
    ])

    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Information'), __t('Contract'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                {{--@include('partials.form.input_template', [--}}
                {{--'label' => __t('Employee'),--}}
                {{--'inputClass' => 'form-control ',--}}
                {{--'div' => 'col-md-6 form-group',--}}
                {{--'name' => 'staff_id',--}}
                {{--'value' => isset($form_record->staff_id) ? $form_record->staff_id: old('staff_id', ''),--}}
                {{--'type' => 'native_select',--}}
                {{--'options' => $related_form_data['staffOptions'],--}}
                {{--'attributes' => ['placeholder' => sprintf(__t('Select %s') , __t('Employee')), 'required' => 'required'],--}}
                {{--])--}}
                @include('partials.form.input_template', [
                        'label' => __t('Employee'),
                        'inputClass' => 'form-control staff-dropdown',
                        'div' => 'col-md-6 form-group',
                        'name' => 'staff_id',
                        'options' => isset($staffOptions) ? $staffOptions : [],
                        'value' =>$selectedStaff ?? ($form_record ? $form_record->staff_id: old('staff_id', '')),
                        'type' => 'selectStaff',
                        'attributes' => [
                            'select2-dimmed' => $form_record ? "true" : "false",
                            'data-staff-url' => route('owner.staff.search', ['fields' => ['staff_job.join_date', 'staff_info.employment_level_id', 'staff_info.designation_id']]),
                            'placeholder' => $staffInputPlaceholder,
                            'required' => 'required'
                        ],
                    ])

                {{--                {{dd(isset($form_record->code) ? $form_record->code : $related_form_data['autoNumber'])}}--}}
                @include('partials.form.input_template', [
                    'label' => __t('Code'),
                    'name' => 'code',
                    'value' => old('code', isset($form_record->code) ? $form_record->code : $related_form_data['autoNumber'] ),
                    'inputClass' => 'form-control bg-white',
                    'div' => 'form-group col-md-6',
                    'type' => 'text',
                    'attributes' => ['maxlength' => 10, 'required' => 'required'],
                ])
                <input type="hidden" name="generated_code" value="{{$related_form_data['autoNumber']}}" />

                @include('partials.form.input_template', [
                    'label' => __t('Designation'),
                    'name' => 'designation_id',
                    'value' => isset($form_record->designation_id) ? $form_record->designation_id: old('designation_id', ''),
                    'options' => $related_form_data['designationOptions'],
                    'inputClass' => 'form-control',
                    'div' => 'form-group col-md-6',
                    'type' => 'native_select',
                    'attributes' => ['data-allow-clear' => 'true','placeholder' => sprintf(__t('Select %s') , __t('Designation'))],
                ])
                @include('partials.form.input_template', [
                    'label' => __t('Employment Level'),
                    'name' => 'employment_level_id',
                    'options' => $related_form_data['contract_employment_levels'],
                    'inputClass' => 'form-control',
                    'div' => 'form-group col-md-6',
                    'type' => 'select',
                    'attributes' => ['placeholder' => sprintf(__t('Select %s') , __t('Employment Level'))],
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Master Contract'),
                    'name' => 'master_contract_id',
                    'options' => $contractOptions,
                    'value' => isset($form_record->master_contract_id) ? $form_record->master_contract_id: old('master_contract_id', ''),
                    'inputClass' => 'form-control',
                    'div' => 'form-group col-md-6',
                    'type' => 'native_select',
                    'attributes' => ['data-allow-clear' => 'true','placeholder' => sprintf(__t('Select %s') , __t('Master Contract'))],
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Description'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'description',
                    'type' => 'textArea',
                    'attributes' => ['rows' => '2','autoResize' => true, 'placeholder' => sprintf(__t('Enter %s'), __t('description'))]
                ])
            </div>
        </div>
        <hr class="mt-1 mb-1 spacer">
        <div class="card-body">
            <div class="panel">
                <div class="panel-body">
                    <div class="row">
                        @include('partials.form.input_template', [
                            'label' => __t('Start Date'),
                            'inputClass' => 'form-control',
                            'div' => 'col-md-6 form-group form-group-icon reverse',
                            'name' => 'start_date',
                            'value' => isset($form_record->start_date) ? formatForView($form_record->start_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('start_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                            'type' => 'date',
                            'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                            'attributes' => ['required' => 'required']
                        ])
                        <div class="col-md-6 form-group form-group-icon reverse">
                            <div class="position-relative">
                                <div class="input-group">
                                    <label class="d-none d-md-block">&nbsp; </label>
                                    <div class="input-group-prepend w-100">
                                        <div class="input-group-text w-100 text-wrap">
                                            <input type="hidden" name="renew" value="0">
                                            @include('partials.form.input_template', [
                                                'label' => __t('Renew Contract Automatically'),
                                                'labelClass' => 'custom-control-label',
                                                'inputClass' =>'custom-control-input disabled',
                                                'div' => 'custom-control custom-checkbox p-0 w-100',
                                                'value' => 1,
                                                'name' => 'renew',
                                                'checked' => old('renew', (!empty($form_record) ? $form_record->renew : 0)),
                                                'type' => 'checkbox',
                                                ])
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-12"></div>

                        <div class="col-md-6">
                            <div class="position-relative">
                                <div class="input-group form-group">
                                    <div class="input-group-prepend w-100">
                                        <div class="input-group-text w-100">
                                            @include('partials.form.input_template', [
                                                'label' => __t('Duration'),
                                                'labelClass' => 'custom-control-label',
                                                'inputClass' => 'custom-control-input',
                                                'div' => 'custom-control custom-radio p-0 w-100',
                                                'name' => 'duration_radio',
                                                'id' => 'duration_radio',
                                                'value' => 'duration_radio',
                                                'attributes' => ['onchange' => 'handleDurationRadios(this);', 'required' => 'required'],
                                                'type' => 'radio',
                                                'checked' => 1,
                                             ])
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" id="duration-inputs">
                                <div class="form-row">
                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'name' => 'duration_value',
                                        'inputClass' => 'form-control',
                                        'div' => 'col-md-6 form-group mb-3 mb-md-3',
                                        'type' => 'number',
                                        'value' => old('duration_value', (!empty($form_record) ? $form_record->duration_value : 1)),
                                        'attributes' => ['max' => 999, 'min' => 1,'data-parsley-errors-container' => '#duration-errors', 'placeholder' => __t('Duration'), 'required' => 'required'],
                                    ])
                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'name' => 'duration_unit',
                                        'value' => old('duration_unit', (isset($form_record->duration_unit) ? $form_record->duration_unit : \App\Utils\ContractDurationUnitsUtil::YEAR)),
                                        'options' => \App\Utils\ContractDurationUnitsUtil::getDurationUnits(),
                                        'inputClass' => 'form-control',
                                        'div' => 'col-md-6 form-group mb-0',
                                        'type' => 'select',
                                        'attributes' => ['data-parsley-errors-container' => '#duration-errors', 'required' => 'required', 'disableAlphabeticSorting' => true],
                                    ])
                                    <div class="col-md-12">
                                        <div id="duration-errors" class="invalid-messages"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="position-relative">
                                <div class="input-group form-group">
                                    <div class="input-group-prepend w-100">
                                        <div class="input-group-text w-100">
                                            @include('partials.form.input_template', [
                                                'label' => __t('End Date'),
                                                'labelClass' => 'custom-control-label',
                                                'inputClass' => 'custom-control-input',
                                                'div' => 'custom-control custom-radio p-0 w-100',
                                                'name' => 'duration_radio',
                                                'id' => 'enddate_radio',
                                                'value' => 'enddate_radio',
                                                'attributes' => ['onchange' => 'handleDurationRadios(this);', 'required' => 'required'],
                                                'type' => 'radio',
                                                'checked' => (($form_record && empty($form_record->duration_value)) ? 1 : false),
                                             ])
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group form-group-icon reverse" id="enddate-inputs">
                                @include('partials.form.input_template', [
                                    'label' => __t(''),
                                    'inputClass' => 'form-control',
                                    'div' => '',
                                    'name' => 'end_date',
                                    'value' => isset($form_record->end_date) ? formatForView($form_record->end_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('end_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                                    'type' => 'date',
                                    'attributes' => [],
                                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                                ])
                            </div>
                        </div>


                        @include('partials.form.input_template', [
                            'label' => __t('Join Date'),
                            'inputClass' => 'form-control',
                            'div' => 'col-md-6 form-group form-group-icon reverse',
                            'name' => 'join_date',
                            'value' => isset($form_record->join_date) ? formatForView($form_record->join_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('join_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                            'type' => 'date',
                            'attributes' => ['required' => 'required'],
                            'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                        ])

                        @include('partials.form.input_template', [
                            'label' => __t('Probation End Date'),
                            'inputClass' => 'form-control',
                            'div' => 'col-md-6 form-group form-group-icon reverse',
                            'name' => 'probation_end_date',
                            'value' => isset($form_record->probation_end_date) ? formatForView($form_record->probation_end_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('probation_end_date'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                            'type' => 'date',
                            'attributes' => ['required' => 'required'],
                            'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                        ])
                    </div>
                </div>
            </div>
        </div>
        <hr class="mt-1 mb-1 spacer">
        <div class="card-body">
            <div class="panel">
                <div class="panel-body">
                    <div class="row">
                        @include('partials.form.input_template', [
                            'label' => __t('Contract Sign Date'),
                            'inputClass' => 'form-control',
                            'div' => 'col-md-6 form-group form-group-icon reverse',
                            'name' => 'contract_sign_date',
                            'value' => isset($form_record->contract_sign_date) ? formatForView($form_record->contract_sign_date, EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView(old('contract_sign_date', date('Y-m-d')), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                            'type' => 'date',
                            'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                        ])
                        <div class="w-100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">
            <h6>{{__t('Salary Information')}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @php
                    @endphp
                @include('partials.form.input_template', [
                    'label' => __t('Currency'),
                    'name' => 'currency_code',
                    'options' => $related_form_data['currenciesList'],
                    'value' => old('currency_code', ($form_record ? $form_record->currency_code : getCurrentSite('currency_code'))),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group col-md-6',
                    'type' => 'select',
                    'attributes' => ['required' => 'required' ,'placeholder' => sprintf(__t('Select %s') , __t('Currency'))],
                ])
                @if(count($related_form_data['contract_salary_structures']))
                    @include('partials.form.input_template', [
                        'label' => __t('Salary Structure'),
                        'name' => 'salary_structure_id',
                        'options' => $related_form_data['contract_salary_structures'],
                        'inputClass' => 'form-control',
                        'div' => 'col-md-6 form-group col-md-6',
                        'type' => 'select',
                        'attributes' => ['placeholder' => sprintf(__t('Select %s') , __t('Salary Structure'))],
                    ])
                @endif
                @include('partials.form.input_template', [
                    'label' => __t('Payroll Frequency'),
                    'name' => 'payroll_frequency',
                    'options' => \App\Utils\PayrollFrequencyUtil::getPayrollFrequency(),
                    'defaultValue' => \App\Utils\PayrollFrequencyUtil::MONTHLY,
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group col-md-6',
                    'type' => 'select',
                    'attributes' => ['placeholder' => sprintf(__t('Select %s') , __t('Payroll Frequency')), 'required' => 'required'],
                ])

            </div>
        </div>
        <div class="card-body">
            <div class="panel">
                <div class="panel-head mb-2">
                    <h3>{{__t('Earning')}}</h3>
                </div>
                <div class="panel-body">
                    <div class="sortable-dynamic-rows-wrapper">
                        <table id="multiple-table" class="earning-table sortable-dynamic-rows" >
                            <thead>
                            <tr class="sortable-dynamic-rows-header">
                                <th class=" reorder grey" width="50"></th>
                                <th class="d-none order-col d-none order-col">0</th>
                                <th class="p-normal" width="250">{{__t('Component')}}</th>
                                <th class="p-normal">{{__t('Formula')}}</th>
                                <th class="p-normal" width="150">{{__t('Amount')}}</th>
                                <th class=" grey" width="50"></th>
                            </tr>
                            <tr class="sortable-dynamic-rows-row sortable-dynamic-rows-fixed-row sortable-dynamic-rows-row-enable-inputs">
                                <th class="grey text-hide b-0 p-0" hidden="hidden">0</th>
                                <th class=" reorder sortable-dynamic-rows-btn-wrap grey"><i class="mdi mdi-24px mdi-lock"></i></th>
                                <th class="d-none order-col d-none order-col">
                                    <input name="contractComponent[earning][basic][order]" type="hidden" class="order-col" value="-1">
                                </th>

                                <th class="p-normal">
                                    {{$basicComp->name}}
                                </th>
                                <th style="background-color: #e9ecef; border-bottom: 1px solid #ffffff;"></th>
                                <th>
                                    <input type="hidden" name="contractComponent[earning][basic][salary_component_id]" value="{{$basicComp->id}}"/>
                                    @include('partials.form.input_template', [
                                             'label' => '',
                                             'value' => $basicComponentAmt,
                                             'inputClass' => 'form-control',
                                             'name' => 'contractComponent[earning][basic][amount]',
                                             'id' => 'basicAmount',
                                             'div' => 'form-group mb-0',
                                             'type' => 'number',
                                             'attributes' => ["max" => 99999999999, 'placeholder' => sprintf(__t('Enter %s'), __t('amount')), 'min' => '0', 'step' => '0.0000000001', 'required' => 'required']
                                         ])
                                </th>
                                <th class=" sortable-dynamic-rows-btn-wrap grey">
                                    <button type="button" class="btn btn-icon remove-row float-none" disabled><i class="mdi mdi-key text-inactive"></i>{{__t('Primary')}}</button>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr role="row" class="sortable-dynamic-rows-row itemRow movable">
                                <td class=" sortable-dynamic-rows-btn-wrap sortable-dynamic-rows-reorder-btn grey"><i class="drag-icon mdi mdi-drag-vertical ui-sortable-handle"></i></td>
                                <td class="d-none order-col"><input id="contractComponent[earning][0][order]" value="0" name="contractComponent[earning][0][order]" type="hidden" class="item_name order-input order-col" ></td>

                                <td>
                                    @include('partials.form.input_template', [
                                    'label' => '',
                                    'name' => 'contractComponent[earning][0][salary_component_id]',
                                    'inputClass' => 'select-earning item_name comp_id form-control',
                                    'id' => '',
                                    'div' => 'form-group mb-0',
                                    'type' => 'native_select',
                                    'options' => $related_form_data['earComp'],
                                    'attributes' => [
                                        'original-name' => 'salary_component_id',
                                        'data-dropdown-auto-width' => false,
                                        'data-fixed-button' => true,
                                        'data-fixed-button-icon' => "far fa-plus-circle text-success fs-14 mr-2",
                                        'data-fixed-button-title' => __t('Add New Earning Component'),
                                        'data-fixed-button-callback' => "initModal",
                                        'placeholder' => sprintf(__t('Select %s'), __t('Component')),
                                        'required' => 'required',
                                        'data-type' => $SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING
                                        ]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'name' => 'contractComponent[earning][0][formula]',
                                        'id' => '',
                                        'div' => 'form-group mb-0 form-group-textarea-formula',
                                        'inputClass' => 'form-control item_name formula-input',
                                        'type' => 'formula',
                                        'attributes' => [
                                            'original-name' => 'formula',
                                            'placeholder' => sprintf(__t('Enter %s'), __t('Formula')),
                                            'data-placeholder_url' => route('api.placeholders.list', ['entity_key' => 'payslip', 'mode'=>\App\Utils\FieldTypeModesUtil::EQUATION])
                                        ]
                                    ])

                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'id' => '',
                                        'name' => 'contractComponent[earning][0][auto_text]',
                                        'div' => 'form-group mb-0 form-group-textarea-auto',
                                        'inputClass' => 'form-control item_name textarea-auto',
                                        'type' => 'text',
                                        'attributes' => [
                                         'readonly' => true,
                                        ]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'inputClass' => 'item_name form-control amount-input',
                                        'name' => 'contractComponent[earning][0][amount]',
                                        'id' => '',
                                        'div' => 'form-group mb-0',
                                        'type' => 'number',
                                        'attributes' => ["max" => 99999999999, 'original-name' => 'amount', 'placeholder' => sprintf(__t('Enter %s'), __t('Amount')), 'min' => '0' , 'step' => '0.0000000001']
                                    ])
                                </td>
                                <td class=" sortable-dynamic-rows-btn-wrap grey">
                                    <button type="button" class="btn btn-icon remove-row float-none"><i class="far fa-minus-circle"></i>{{__t('Remove')}}</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="btn-group add-row dropdown" role="group">
                        <button type="button" class="btn btn-secondary addRow font-weight-bold" data-table-id="multiple-table"><i class="far fa-plus-circle text-success mr-2"></i>{{__t('Add')}}</button>
                    </div>
                </div>
                <div id="datatable-errors" class="invalid-messages"></div>
            </div>
        </div>
        <hr class="mt-1 mb-1 spacer">
        <div class="card-body">
            <div class="panel">
                <div class="panel-head mb-2">
                    <h3>{{__t('Deduction')}}</h3>
                </div>
                <div class="panel-body">
                    <div class="sortable-dynamic-rows-wrapper">
                        <table id="multiple-table2" class="deduction-table sortable-dynamic-rows" >
                            <thead>
                            <tr class="sortable-dynamic-rows-header">
                                <th class=" reorder grey"></th>
                                <th class="d-none order-col d-none order-col">0</th>
                                <th class="p-normal">{{__t('Component')}}</th>
                                <th class="p-normal">{{__t('Formula')}}</th>
                                <th class="p-normal">{{__t('Amount')}}</th>
                                <th class=" grey"></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr role="row" class=" sortable-dynamic-rows-row itemRow movable">
                                <td class=" sortable-dynamic-rows-btn-wrap sortable-dynamic-rows-reorder-btn grey"><i class="drag-icon mdi mdi-drag-vertical ui-sortable-handle"></i></td>
                                <td class="d-none order-col">
                                    <input id="contractComponent[deduction][0][order]" value="0" name="contractComponent[deduction][0][order]" type="hidden" class="item_name order-input order-col" >
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                    'label' => '',
                                    'name' => 'contractComponent[deduction][0][salary_component_id]',
                                    'inputClass' => 'select-deduction item_name comp_id  form-control',
                                    'id' => '',
                                    'div' => 'form-group mb-0',
                                    'type' => 'native_select',
                                    'options' => $related_form_data['dedComp'],
                                    'attributes' => [
                                        'original-name' => 'salary_component_id',
                                        'data-dropdown-auto-width' => true,
                                        'data-fixed-button' => true,
                                        'data-fixed-button-icon' => "far fa-plus-circle text-success fs-14 mr-2",
                                        'data-fixed-button-title' => __t('Add New Deduction Component'),
                                        'data-fixed-button-callback' => "initModal",
                                        'placeholder' => sprintf(__t('Select %s'), __t('Component')),
                                        'required' => 'required',
                                        'data-type' => $SalaryComponentTypeUtil::SALARY_COMPONENT_DEDUCTION
                                        ]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'name' => 'contractComponent[deduction][0][formula]',
                                        'div' => 'form-group mb-0 form-group-textarea-formula',
                                        'value' => 55,
                                        'inputClass' => 'item_name form-control formula-input',
                                        'type' => 'formula',
                                        'attributes' => [
                                            'original-name' => 'formula',
                                            'defaultPadding' => false,
                                            'defaultBtnText' => false,
                                            'placeholder' => sprintf(__t('Enter %s'), __t('Formula')),
                                            'data-placeholder_url' => route('api.placeholders.list', ['entity_key' => 'payslip', 'mode'=>\App\Utils\FieldTypeModesUtil::EQUATION])
                                        ]
                                    ])

                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'id' => '',
                                        'div' => 'form-group mb-0 form-group-textarea-auto',
                                        'inputClass' => 'form-control item_name textarea-auto',
                                        'name' => 'contractComponent[deduction][0][auto_text]',
                                        'type' => 'text',
                                        'attributes' => [
                                            'readonly' => true,
                                        ]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'inputClass' => 'item_name form-control amount-input',
                                        'name' => 'contractComponent[deduction][0][amount]',
                                        'div' => 'form-group mb-0',
                                        'type' => 'number',
                                        'attributes' => ["max" => 99999999999, 'original-name' => 'amount', 'placeholder' => sprintf(__t('Enter %s'), __t('Amount')), 'min' => '0' , 'step' => '0.0000000001']
                                    ])
                                </td>
                                <td class=" sortable-dynamic-rows-btn-wrap grey">
                                    <button type="button" class="btn btn-icon remove-row float-none"><i class="far fa-minus-circle"></i>{{__t('Remove')}}</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="btn-group add-row dropdown" role="group">
                        <button type="button" class="btn btn-secondary addDed font-weight-bold" data-table-id="multiple-table2"><i class="far fa-plus-circle text-success mr-2"></i>{{__t('Add')}}</button>
                    </div>
                </div>
                <div id="datatable-errors2" class="invalid-messages"></div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">
            <h6>{{__t('Attachments')}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @include('partials.form.input_template', [
                    'label' => '',
                    'inputClass' => 'form-control',
                    'div' => 'col-md-12 form-group mb-0',
                    'name' => 'attachments',
                    'getFieldErrorNameClosure' => function() use($errors)
                    {
                        $errorsArray = $errors->toArray();
                        foreach ($errorsArray as $k => $error)
                        {
                            if(strpos($k,'attachment') !== false)
                            {
                                return $k;
                            }
                        }
                        return null;
                    },
                    'value' => isset($files) ? $files : null,
                    'attributes' => [
                        'accepts' => 'application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint,text/plain,application/pdf,image/*|rar|zip',
                        'multiple' => true,
                        'max_file_size' => 25
                         ],
                    'type' => 'file',
                    'after' => '<small class="form-text text-muted">'.__t('Max file size').' 25MB </br>'. sprintf(__t('Allowed file types: (%s)'), implode(',', ['png,jpg,gif,bmp,rar,zip and all office files'])) . '</small>',

                ])

            </div>
        </div>
    </div>

    @isset($action_label)
        <div class="card mb-3">
            <div class="card-header">
                <h6>{{__t($action_label)}}</h6>
            </div>
            <div class="card-body">
                <div class="form-row">

                    @include('partials.form.input_template', [

                        'label' => __t('Date'),
                        'inputClass' => 'form-control '.(isset($form_record) && $form_record->status == \App\Utils\ContractStatusUtil::SUPERSEDED ? ' disabled ' : ""),
                        'name' => 'status_date',
                        'div' => 'col-md-6 form-group mb-lg-0 form-group-icon reverse',
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                        "value" => formatForView(old('status_date', ($form_record->status_date ?: '')), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                        'attributes' => ['required' => 'required'],
                    ])

                    @include('partials.form.input_template', [
                        'label' => __t('Reason'),
                        'inputClass' => 'form-control',
                        'name' => 'status_reason',
                        'div' => 'col-md-6 form-group mb-lg-0',
                        'type' => 'textArea',
                        'attributes' => ['rows' => '2', 'placeholder' => __t('Enter Reason')],
                    ])

                </div>
            </div>
        </div>
    @endisset


    @include('partials/custom_forms/form_fields', ['header' => __t("Contract More Information")])

    {{csrf_field()}}
    {!! Form::close() !!}

    {!! Form::model('',['class' => 'form-validate','method' => 'POST', 'url' => route($routesPrefix.'salary_components.store'), 'name' => 'on_the_fly_form', 'id' => 'on_the_fly_form']) !!}
    <div class="modal fade" id="add_salary_component_modal" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-xl modal-dialog-scrollable modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                            data-target="#add_salary_component_modal" onclick="closeModal(this);">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    @include('salary_components.owner.form')
                </div>
                <div class="modal-footer">
                    <button type="button" id="submitBtn" class="btn btn-success" aria-label="Submit" onclick="submitAjax()">{{__t('Save')}}
                    </button>
                </div>
            </div>
        </div>
    </div>
    {!! Form::close() !!}
    <!-- end page contents -->
@endsection

@push('scripts')

    <script>
        const DURATION_UNIT_MONTH = "{{\App\Utils\ContractDurationUnitsUtil::MONTH}}";
        const DURATION_UNIT_YEAR = "{{\App\Utils\ContractDurationUnitsUtil::YEAR}}";
        const COMP_TYPE_EARNING = "{{\App\Utils\SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING}}";
        const COMP_TYPE_DEDUCTION = "{{\App\Utils\SalaryComponentTypeUtil::SALARY_COMPONENT_DEDUCTION}}";
        const formErrors = @json($errors->toArray());
        var staffContracts = @json($related_form_data['staffContracts']);
            @if(isset($salaryComponents))
        var salaryComponents = @json($salaryComponents);
            @endif
        let csrf = '{{ csrf_token() }}';
        let compRoute = '{{ route('api.salary_components.store') }}';
        var isEdit = "{{$form_record ? true : false}}";
    </script>
    <script src="{{asset('/js/payroll/contract/create.js')}}"></script>
    <script src="{{asset('/js/payroll/contract/add_comp_on_the_fly.js?v=' . JAVASCRIPT_VERSION)}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_jqueryui.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_jqueryui_touch.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/dynamic_rows/dynamic_rows.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/dynamic_rows/sortable_dynamic_rows.js"></script>
    <script src="{{asset('/js/payroll/salary-comp/create.js')}}"></script>
    <script>
        $(function () {
            setFormError('.form-validate', formErrors);
        })
    </script>
@endpush
