@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/index/index.min.css?v={{CSS_VERSION}}" />
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css?v={{CSS_VERSION}}" />
@endpush
@section('content')
    @php $paySlipCounts = isset($record->paySlips) ? $record->paySlips->count() : 0;@endphp
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="pages-head-title">
                        <h2>
                            {{$record->name}} #{{$record->id}}
                            @if($record->status == \App\Utils\PayrunStatusUtil::GENERATED)
                                <span class="status status-inactive">
                                    <i class="cir"></i><span class="status-text">{{__t('Generated')}}</span>
                                </span>
                            @elseif($record->status == \App\Utils\PayrunStatusUtil::APPROVED)
                                <span class="status status-orange"><i class="cir"></i>{{__t('Approved')}}</span>
                            @else
                                <span class="status status-success"><i class="cir"></i>{{__t('Paid')}}</span>
                            @endif
                        </h2>
                    </div>
                </div>
                <div class="col-sm-8">
                    <div class="action-buttons">
                        @if (\App\Facades\Permissions::checkPermission([\App\Utils\PermissionUtil::CREATE_PAY_RUN, \App\Utils\PermissionUtil::VIEW_PAY_RUN]) && ifPluginActive(\App\Utils\PluginUtil::MUDAD_PLUGIN))
                        <a href="{{route('owner.mudad.generate.payrun.form',$record->id)}}" class="btn btn-icon btn-secondary">
                            <i class="mudad-icon">
                                @include('partials.icons.mudad-icon')
                            </i>
                            {{__t('Generate Mudad Payrun')}}
                        </a>
                        @endif
                        @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::CREATE_PAY_RUN))
                            <a href="{{route('owner.payruns.payslips.create',$record->id)}}" class="btn btn-icon btn-primary ml-sm-2"><i class="mdi mdi-plus"></i>{{sprintf(__t('New %s'), __t('PaySlip'))}}</a>
                        @endif
                        @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::PAY_PAYSLIPS) && $record->approvedCount > 0)
                            <a href="{{route('owner.payruns.payments.create',$record)}}" class="btn btn-icon btn-success ml-sm-2"><i class="mdi mdi-cash"></i>{{__t('Pay')}}</a>
                        @endif
                        @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::APPROVE_PAYSLIPS) && $record->generatedCount > 0)
                            <a href="{{route('owner.payslips.approve-all',$record)}}" class="btn btn-icon btn-orange ml-sm-2"><i class="mdi mdi-check-all"></i>{{__t('Approve All')}}</a>
                        @endif
                        @php
                            if (request()->has('payrun')) {
                                unset($query['payrun']);
                            }
                        @endphp
                        @include('partials.show.record_nav', ['url' => 'owner.payruns.show', 'name' => 'payrun', 'removeWrapperDiv'=>true])
                    </div>
                </div>
                @if(\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::VIEW_ALL_JOURNALS) && \App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::AccountingPlugin) && isset($payrun_journal))
                    <div class="col-sm-12">
                        <span><a href="{{ getCakeURL(['controller' => 'journals', 'action' => 'view', $payrun_journal->id]) }}" class="text-decoration-underline" target="_blank">{{__t('Journal')}} #{{ $payrun_journal->number }}</a></span>
                    </div>
                @endif
            </div>
        </div>
    </div>
    @if($mudadFileFinalized)
        <div class="alert alert-icon alert-bordered alert-dismissible border-success alert-success">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">×</span>
            </button>
            <div class="d-flex">
                <div class="alert-icon">
                    <i class="far fa-lg fa-exclamation-circle text-success mr-3"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="mb-0">
                        <strong>
                                <?= __t('This Payrun was finalized') ?>
                            <a class="btn  btn-success responsive d-inline-block mr-1 ml-2 loadCalcAttendance"
                               href="{{ route('api.mudad.export.payrun.file' ,$record->id) }}"><?= __t("Download the file again") ?></a>
                        </strong>
                    </p>
                </div>
            </div>
        </div>
    @endif

    <div class="card mb-3">
        @if (\App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::DELETE_PAY_RUN))
            <div class="card-header bg-faded">
                <div class="card-header-responsive"></div>
                <div class="btn-group btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                    @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Pay Run'), 'href' => route('owner.payruns.destroy', ['payrun' => $record->id])])
                    @if(App\Facades\Permissions::checkPermission(\App\Utils\Export\ExportPermissionsUtil::getPermissions(\App\Utils\EntityKeyTypesUtil::PAYSLIP)))
                        <a href="{{route('owner.viewExport',['entity'=> \App\Utils\EntityKeyTypesUtil::PAYSLIP, 'from_entity_id' => $record->id, 'from_entity' => \App\Utils\EntityKeyTypesUtil::PAY_RUN, 'ids'=> implode(',', $exportedIDs)])}}" class="btn btn-light"><i class="mdi mdi-export mr-1"></i>{{__t('Export')}} {{__t('PaySlips')}}</a>
                    @endif
                    @if( $mudadFileFinalized )
                        <a href="{{ route('api.mudad.export.payrun.file' , $record->id) }}" class="btn btn-light">
                            <i class="mdi mdi-export mr-1"></i>
                            {{ __t('Extract to Mudad') }}
                        </a>
                    @endif
                    @include('partials.templates.printables_dropdown', ['view_templates' => $view_templates, 'id' => $record->id])
                </div>
            </div>
        @endif
        <div class="card-body p-1 p-lg-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab" aria-controls="details" aria-selected="true">{{__t('Details')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="pay-slips-tab" data-toggle="tab" href="#pay-slips" role="tab" aria-controls="pay-slips" aria-selected="false">{{__t('Pay Slips')}} ({{$paySlipCounts}})</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="payments-tab" data-toggle="tab" href="#payments"
                           role="tab" aria-controls="payments"
                           aria-selected="false">{{__t('Payments')}} </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab" aria-controls="activity-log" aria-selected="false">{{__t('Activity Log')}}</a>
                    </li>
                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">
                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel" aria-labelledby="details">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{__t('Pay Run Details')}}</h3>
                            </div>
                        </div>
                        <div class="px-3 pt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Posting Date")}}}:</p>
                                        <p>
                                            {{\App\Facades\Formatter::formatForView($record->posting_date, \App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-4">
                                        <div class="progress-wrap progress-wrap-md">
                                            <small class="first"><strong>{{$record->approvedCount}}</strong> {{__t('Approved')}} / <strong>{{$record->paidCount}}</strong> {{__t('Paid')}}</small>
                                            <div class="progress progress-md">
                                                <div class="progress-bar bg-orange" role="progressbar" style="width: {{$record->approvedCount?($record->approvedCount/$paySlipCounts)*100:0}}%;" aria-valuenow="250" aria-valuemin="0" aria-valuemax="100"></div>
                                                <div class="progress-bar bg-success" role="progressbar" style="width: {{$record->paidCount ?($record->paidCount/$paySlipCounts)*100:0}}%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <small class="last">{{$record->generatedCount}} {{__t('Generated')}}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Start Date")}}}:</p>
                                        <p>
                                            {{\App\Facades\Formatter::formatForView($record->start_date, \App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("End Date")}}}:</p>
                                        <p>
                                            {{\App\Facades\Formatter::formatForView($record->end_date, \App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{{__t("Validate Attendance")}}}:</p>
                                        <p>{{$record->validate_attendance ? __t('Yes') : __t('No')}}</p>
                                    </div>
                                </div>
                                <div class="w-100"></div>
                            </div>
                        </div>
                        <hr class="spacer">
                        <div class="px-3 pt-3">
                            <div class="row">
                                @if ( $record->criteria == 'employee_selection')
                                    <div class="col-md-6">
                                        <div class="">
                                            <p class="text-muted mb-1">{{__t('Employees')}}:</p>
                                            @foreach ($record->paySlips as $paySlip)
                                                <p><a href="{{route('owner.staff.show', ['staff' => $paySlip->staff->id])}}" target="_blank"> {{ $paySlip->staff->full_name . ' #' . $paySlip->staff->code }}</a></p>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    @if(\App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::BranchesPlugin))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{{__t("Branch")}}}:</p>
                                                <p>{{ $record->branches_names }}</p>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{{__t("Department")}}}</p>
                                            <p>{{ $record->departments_names }}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{{__t("Designation")}}}:</p>
                                            <p>{{ $record->designations_names }}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{{__t("Payroll Frequency") }}}:</p>
                                            <p>{{ $record->payroll_frequency_names }}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="">
                                            <p class="text-muted mb-1">{{__t('Excluded Employees')}}:</p>
                                            @php $excludedEmployees = $record->getExcludedEmployees(); @endphp
                                            @if($excludedEmployees->count()>0)
                                                @foreach ($excludedEmployees as $excludedEmployee)
                                                    <p><a href="{{route('owner.staff.show', ['staff' => $excludedEmployee->id])}}" target="_blank"> {{ $excludedEmployee->full_name . ' #' . $excludedEmployee->id }}</a></p>
                                                @endforeach
                                            @else
                                                <p>{{__t('None')}}</p>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                    </div>
                    <div class="tab-pane p-3 fade" id="pay-slips" role="tabpanel" aria-labelledby="pay-slips-tab">
                        @include('partials.load_grid', [
                            'gridTitle' => '',
                            'gridUrl' => route('owner.payslips.index', ['payrun' => $record->id])
                        ])
                    </div>
                    <div class="tab-pane p-3 fade show " id="payments" role="tabpanel"
                         aria-labelledby="payments-tab">
                        @include('partials.load_grid', [
                            'gridId' => 'payments-grid',
                            'gridTitle' => __t('Payments'),
                            'gridTableWrapClass' => 'text-nowrap',
                            'gridUrl' => route('owner.payrun_payments.index', ['payrun' => $record->id])
                        ])
                    </div>
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">
                        <input type = "text" value = "payrun"

                               id = "entity_key" hidden />
                        <input type = "hidden" value = "{{$record->id}}" id = "entity_id" />
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{__t('Activity Log')}}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    @include('partials.modals.create_delete_modal')

    <!-- end page contents -->
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
@endpush
