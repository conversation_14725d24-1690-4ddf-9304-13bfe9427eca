@extends('layouts.owner')
@inject('PayrunCriteriaUtil', 'App\Utils\PayrunCriteriaUtil')
@push('styles')
@endpush
@section('content')

    @php
        if(!$form_record) {
             $route = route('owner.payruns.store');
             $method = 'POST';
        }else{
             $route = route('owner.payruns.update', ['id' => $form_record->id]);
            $method = 'PUT';
        }
        $staffOptionsAttributes = old('staffOptionsAttributes') ?? null;
        $excludedStaffOptionsAttributes = old('excludedStaffOptionsAttributes') ?? null;
        $excludedStaffOptions = old('excludedStaffOptions') ?? null;
        $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
    @endphp

    <form action="{{$route}}" class="form-validate" method="POST">
        {{method_field($method)}}
        @csrf
        <div class="pages-head">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="pages-head-title">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="action-buttons">
                            <button type="button" class="btn btn-icon btn-secondary cancel-btn"><i class="mdi mdi-close"></i>{{__t('Cancel')}}</button>
                            <button type="submit" class="btn btn-icon btn-success ml-sm-2"><i class="mdi mdi-content-save-outline"></i>{{__t('Save')}}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mb-3">
            <div class="card-header">
                <h6>{{__t('Pay Run Information')}}</h6>
            </div>
            <div class="card-body">
                <div class="form-row">

                    @include('partials.form.input_template', [
                        'label' => __t('Name'),
                        'inputClass' => 'form-control',
                        'div' => 'col-md-6 form-group',
                        'name' => 'name',
                        'type' => 'text',
                        'attributes' => ['required' => 'required']
                    ])

                    @php
                        $jsFormats = getDateFormats('new_js');
                        $siteFormat = getCurrentSite('date_format');
                        $siteFormat = strtoupper($jsFormats[$siteFormat]);
                    @endphp

                    @include('partials.form.input_template', [
                        'label' => __t('Posting Date'),
                        'inputClass' => 'form-control',
                        'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                        'div' => 'col-md-6 form-group form-group-icon reverse',
                        'name' => 'posting_date',
                        'value' => formatForView(\Carbon\Carbon::today(),\App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_PICKER),
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                    ])

                    @include('partials.form.input_template', [
                        'label' => __t('Start Date'),
                        'inputClass' => 'form-control',
                        'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                        'div' => 'col-md-6 form-group form-group-icon reverse mb-3 mb-lg-0',
                        'name' => 'start_date',
                        'value' => '',
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                    ])
                    @include('partials.form.input_template', [
                        'label' => __t('End Date'),
                        'inputClass' => 'form-control',
                        'attributes' => ['required' => 'required', 'placeholder' => $siteFormat],
                        'div' => 'col-md-6 form-group form-group-icon reverse',
                        'name' => 'end_date',
                        'value' => '',
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                    ])

                    @if(count($related_form_data['currencies']) > 1)
                        @include('partials.form.input_template',[
                            'label' => __t('Currency'),
                            'defaultValue'=> 0,
                            'attributes' => ['required' => 'required'],
                            'options' => $related_form_data['currencies'],
                            'inputClass' => 'select form-control',
                            'div' => 'col-md-6 form-group mb-0',
                            'name' => 'currency',
                            'id' => 'type',
                            'type' => 'select'
                        ])
                    @else
                        <input type="hidden" value="{{reset($related_form_data['currencies'])}}" name="currency">
                    @endif
                    @if (\App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN))
                        <div class="col-md-6 form-group form-group-icon">
                            <label>&nbsp;</label>
                            <div class="position-relative">
                                <div class="input-group">
                                    <div class="input-group-prepend w-100">
                                        <div class="input-group-text w-100 text-wrap">
                                            @include('partials.form.input_template', [
                                                'label' => __t('Validate Attendance'),
                                                'labelClass' => 'custom-control-label',
                                                'inputClass' => 'custom-control-input',
                                                'div' => 'custom-control custom-checkbox p-0 w-100',
                                                'name' => 'validate_attendance',
                                                'type' => 'checkbox',
                                                'value' => 1,
                                                'checked' => 1
                                             ])
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                    <div class="col-md-12">
                        <hr class="spacer">
                    </div>

                    <div class="col-md-6 form-group order-6 order-lg-1">
                        <div class="position-relative form-group">
                            <div class="input-group">
                                <div class="input-group-prepend w-100">
                                    <div class="input-group-text w-100">
                                        @include('partials.form.input_template', [
                                            'label' => __t('Rule Selection'),
                                            'labelClass' => 'custom-control-label',
                                            'inputClass' => 'custom-control-input',
                                            'div' => 'custom-control custom-checkbox p-0 w-100',
                                            'name' => 'criteria',
                                            'id' => 'rule_selection',
                                            'value' => $PayrunCriteriaUtil::RULE_SELECTION,
                                            'attributes' => ['onchange' => 'handleCriteria(this);'],
                                            'type' => 'radio',
                                            'checked' => isset($staffOptionsAttributes) ? 0 : 1,
                                         ])
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="rule-inputs">
                            @if (\App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::BranchesPlugin))
                                @include('partials.form.input_template', [
                                    'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Branches')), 'data-allow-clear' => 'true','multiple'=> true],
                                    'options' => $related_form_data['branches'],
                                    'label' => __t('Branch'),
                                    'inputClass' => 'select form-control',
                                    'value' => '',
                                    'div' => 'form-group',
                                    'name' => 'branches[]',
                                    'type' => 'select'
                                ])
                            @endif
                            @include('partials.form.input_template', [
                                'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Departments')), 'data-allow-clear' => 'true','multiple'=> true],
                                'options' => $related_form_data['departments'],
                                'label' => __t('Department'),
                                'value' => '',
                                'inputClass' => 'select form-control',
                                'div' => 'form-group',
                                'name' => 'departments[]',
                                'type' => 'select'
                            ])
                            @include('partials.form.input_template', [
                                'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Designations')), 'data-allow-clear' => 'true','multiple'=> true],
                                'options' => $related_form_data['designations'],
                                'value' => '',
                                'label' => __t('Designation'),
                                'inputClass' => 'select form-control',
                                'div' => 'form-group',
                                'name' => 'designations[]',
                                'type' => 'select'
                            ])
                            @include('partials.form.input_template', [
                                'attributes' => ['placeholder' => __t("All Payroll Frequencies"),'multiple'=> true],
                                'options' => $related_form_data['payroll_frequency'],
                                'label' => __t('Payroll Frequency'),
                                'inputClass' => 'select form-control',
                                'div' => 'form-group mb-0',
                                'name' => 'payroll_frequencies[]',
                                'type' => 'select'
                            ])
                             @include('partials.form.input_template',[
                                'label' => __t('Excluded Employees'),
                                'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                                'div' => 'form-group',
                                'name' => 'exclude_criteria[]',
                                'attributes' => ['placeholder' => $staffInputPlaceholder, 'multiple' => 'multiple', 'disabled' => 'disabled'],
                                'type' => 'selectStaff',
                                'optionsAttributes' => $excludedStaffOptionsAttributes,
                                'options' => $excludedStaffOptions,
                            ])
                        </div>

                    </div>

                    <div class="col-md-6 form-group order-1 order-lg-6">
                        <div class="position-relative form-group">
                            <div class="input-group">
                                <div class="input-group-prepend w-100">
                                    <div class="input-group-text w-100">
                                        @include('partials.form.input_template', [
                                            'label' => __t('Employees Selection'),
                                            'labelClass' => 'custom-control-label',
                                            'inputClass' => 'custom-control-input',
                                            'div' => 'custom-control custom-checkbox p-0 w-100',
                                            'name' => 'criteria',
                                            'attributes' => ['onchange' => 'handleCriteria(this);'],
                                            'id' => 'emp_selection',
                                            'value' => $PayrunCriteriaUtil::EMPLOYEE_SELECTION,
                                            'type' => 'radio',
                                            'checked' => isset($staffOptionsAttributes) ? 1 : 0,
                                         ])
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="emp-inputs" class="disabled">
                            @include('partials.form.input_template',[
                                'label' => __t('Employees'),
                                'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                                'div' => 'form-group',
                                'name' => 'employees[]',
                                'attributes' => ['placeholder' =>  $staffInputPlaceholder, 'multiple' => 'multiple', 'disabled' => 'disabled'],
                                'type' => 'selectStaff',
                                'optionsAttributes' => $staffOptionsAttributes,
                                'options' => $staffOptions ?? [],
                            ])
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <!-- end page contents -->
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
    <script src="{{asset('js/payroll/payrun/create.js')}}"></script>
@endpush
