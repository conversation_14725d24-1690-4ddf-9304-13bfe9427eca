@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/layout/plugin_manager.min.css?v={{CSS_VERSION}}">
@endpush
@section('content')
{!! Form::model('',['class' => 'form-validate','method' => 'post','url' => getCakeURL(['action' => 'plugin_manager/1',  'prefix' => 'owner/owners'])]) !!}
<input type="hidden" name="data[blank][name]" value="1">
<div class="pages-head">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-6">
                @if($showExternalAppsButton)
                <a class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-primary u-text-color-white u-text-hover-color-white" href="{{ route('owner.apps-manager.index') }}">
                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                    <span class="ui-btn-inner-content">
                        <i class="" style="height: 20px;"></i>
                        <span class="ui-btn-inner-text hidden">{{ __t('Manage External Apps') }}</span>
                    </span>
                </a>
                @endif
            </div>
            <div class="col-6">
                <div class="action-buttons">
                    @if(request('page_id') == 1)
                        <button class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white" type="submit">
                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                            <span class="ui-btn-inner-content">
                                <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-content-save"></i>
                                <span class="ui-btn-inner-text">{{ __t('Save') }}</span>
                            </span>
                        </button>
                    @else
                        <a class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white" href="/">
                            <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                            <span class="ui-btn-inner-content">
                                <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-content-save"></i>
                                <span class="ui-btn-inner-text">{{ __t('Save') }}</span>
                            </span>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@if(request('page_id') == 1)
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <h6>{{ __t('Account Information') }}</h6>
                    </div>
                    <div class="card-body pb-0">
                        <div class="row">
                            <div class="col-lg-4 form-group">
                                @include('partials.form.input_template', [
                                    'label' => __t('You Sell'),
                                    'name' => 'data[sold_item_type]',
                                    'inputClass' => 'form-control',
                                    'value' => $setting_data->where('key', 'sold_item_type')->first()->value??0,
                                    'div' => false,
                                    'type' => 'native_select',
                                    'options' => [
                                        0 => ['value' => 0, 'data' => [], 'text' => __t('Services & Products')],
                                        1 => ['value' => 1, 'data' => [], 'text' => __t('Products Only')],
                                        2 => ['value' => 2, 'data' => [], 'text' => __t('Services Only')],
                                    ],
                                    'attributes' => [
                                        'required' => 'required',
                                        'data-select' => true
                                    ]
                                ])
                            </div>
                            <div class="col-lg-4 form-group">
                                @include('partials.form.input_template', [
                                    'label' => __t('Invoicing Method'),
                                    'name' => 'data[invoicing_method]',
                                    'inputClass' => 'form-control',
                                    'div' => false,
                                    'value' => $setting_data->where('key', 'invoicing_method')->first()->value??0,
                                    'type' => 'native_select',
                                    'options' => [
                                        0 => ['value' => 0, 'data' => [], 'text' => __t('Both')],
                                        1 => ['value' => 1, 'data' => [], 'text' => __t('Print (Hard Copy)')],
                                        2 => ['value' => 2, 'data' => [], 'text' => __t('Electronically via Email')],
                                    ],
                                    'attributes' => [
                                        'required' => 'required',
                                        'data-select' => true
                                    ]
                                ])
                            </div>

                            <div class="col-lg-4 form-group">
                                @include('partials.form.input_template', [
                                    'label' => __t('Client Type'),
                                    'name' => 'data[client_type]',
                                    'inputClass' => 'form-control',
                                    'value' => $setting_data->where('key', 'client_type')->first()->value??0,
                                    'div' => false,
                                    'type' => 'native_select',
                                    'options' => [
                                        0 => ['value' => 0, 'data' => [], 'text' => __t('Both')],
                                        1 => ['value' => 3, 'data' => [], 'text' => __t('Business Only')],
                                        2 => ['value' => 2, 'data' => [], 'text' => __t('Individual Only')],
                                    ],
                                    'attributes' => [
                                        'required' => 'required',
                                        'data-select' => true
                                    ]
                                ])
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
{!! Form::close() !!}

<div class="plugin-manager-content">
    <div class="row">
        @foreach ($groups as $index => &$plugin_group)
        <div class="col-lg-6">
            <div class="plugin-group pb-4" data-plugin-group-id="{{$plugin_group->id}}">
                <div class="card border-white plugin-group-card">
                    <div class="card-header plugin-group-header">
                        <h5 @if($plugin_group->color) style="color: {{$plugin_group->color}}" @endif>{{translate_plugin_group_title($plugin_group)}}</h5>
                    </div>
                    <div class="card-body plugin-group-body">
                        <div class="row">
                            @foreach (&$plugin_group->plugins as &$plugin)
                                @php

                                    $appsomo_plans=['License Tier 1','License Tier 2','License Tier 3','License Tier 4','License Tier 5'];

                                                                if(in_array($plan_name,$appsomo_plans) && $plugin->id==87){
                                continue;
                            }
                                /* new booking @todo uncomment when booking is required to be shown by business
                                 * if($plugin->id == \App\Utils\PluginUtil::BookingPlugin && !in_array(\App\Utils\PluginUtil::BookingPlugin, $active_plugins)){
                                    continue;
                                }
                                if($plugin->id == \App\Utils\PluginUtil::NEW_BOOKING_PLUGIN && !in_array(\App\Utils\PluginUtil::NEW_BOOKING_PLUGIN, $active_plugins) && in_array(\App\Utils\PluginUtil::BookingPlugin, $active_plugins)){
                                    continue;
                                }*/
                            @endphp
                            <div class="plugin-item-col col-lg-12">
                                    @php
                                        $plugin->related_plugins = $plugin->dependencies ?$plugin->dependencies->pluck('parent_plugin_id')->toArray():[];
                                        if(app()->getLocale() !== 'eng') {
                                                $plugin->description = $plugin->ar_description;
                                        }

                                    @endphp
                                <div class="plugin-item {{ in_array($plugin->id, $active_plugins) ? 'active' : '' }}" data-plugin-id="{{$plugin->id}}">
                                    <div class="form-row align-items-md-center">
                                        <div class="col-8 col-md-7 col-xl-8">
                                            <h6 class="plugin-item-content">
                                                <span class="plugin-item-content-name">
                                                    <span class="mr-2">
                                                        <i class="plugin-item-icon {{\App\Utils\PluginUtil::getClassOfPlugin($plugin->id)['class']}}"></i>
                                                    </span>
                                                    <span class="plugin-item-name">{{__t($plugin->name)}}</span>
                                                </span>
                                                <a href="#" data-toggle="modal" data-target="#modal-plugin-details" class="plugin-item-show-details ml-md-2">{{__t('Show details')}}</a>
                                            </h6>
                                        </div>
                                        <div class="col-4 col-md-5 col-xl-4">
                                            <div class="text-right">
                                                <div class="plugin-item-checkbox-wrap">
                                                    <label class="plugin-item-status pr-2" for="{{$plugin->id}}">{!! in_array($plugin->id, $active_plugins)  ? __t('Activated') : __t('Deactivated') !!}</label>
                                                    <div class="custom-control custom-control-inline mr-0 custom-switch">
                                                        <input type="checkbox" class="custom-control-input plugin-item-checkbox" {{ in_array($plugin->id, $active_plugins)  ? 'checked' : '' }} data-plugin-checkbox-id="{{$plugin->id}}" id="{{$plugin->id}}" name="{{$plugin->id}}">
                                                        <label class="custom-control-label" for="{{$plugin->id}}"></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>


<div class="modal fade" id="modal-plugin-details" tabindex="-1" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content modal-plugin-content modal-lg" id="modal-plugin-content">
        </div>
    </div>
</div>
@endsection
@push('scripts')
    <script>
        var plugins = {!! json_encode($groups) !!}
        var update_plugin_status = '{{getCakeURL(['action' => 'update_plugin', 'prefix' => 'owner/owners'])}}';
    </script>
    <script src="{!! getCdnAssetsUrl() !!}js/layout/plugin_manager.js?v={{JAVASCRIPT_VERSION}}"></script>
@endpush
