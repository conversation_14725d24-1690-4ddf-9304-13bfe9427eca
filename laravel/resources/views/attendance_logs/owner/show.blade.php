@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css?v={{CSS_VERSION}}" />
@endpush

@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-9">
                    <div class="pages-head-title">
                        <h2 class="fs-16 font-weight-medium">
                            #{{$record->id}}
                            @if($record->status)
                                <span class="status status-{{ \App\Utils\AttendanceLogStatusUtil::getStatusColor($record->status) }}"><i class="cir"></i> <span class="status-text">{{ __(\App\Utils\AttendanceLogStatusUtil::getStatusList()[$record->status]) }}</span></span>
                            @endif
                        </h2>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="action-buttons">
                        @include('partials.show.record_nav', ['removeWrapperDiv' => true,'url' => 'owner.attendance_logs.show', 'name' => 'id'])
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="card border-white mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
            @php
                $hasDeptEditPermission = \App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::EDIT_HIS_DEPARTMENT_ATTENDANCE);
                $hasDeletePermission = \App\Facades\Permissions::checkPermission(\App\Utils\PermissionUtil::DELETE_ATTENDANCE_LOG);
                $recordDeptId = $record->staff_info?->department_id ?? null;
                $sameDept = isset($recordDeptId) && isset($authDeptId) && $authDeptId == $recordDeptId;
            @endphp
            @if($hasDeletePermission || ($sameDept && $hasDeptEditPermission))
                @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Type'), 'href' => route('owner.attendance_logs.destroy', ['attendance_log' => $record->id])])
            @endif
            </div>
        </div>

        <div class="card-body p-1 p-lg-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab"
                           aria-controls="details" aria-selected="true">{{ __t('Details') }}</a>
                    </li>
                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">
                    <div class="tab-pane fade show active" id="details" role="tabpanel"
                         aria-labelledby="details-tab">
                        <div class="px-3 pt-3">
                            <div class="panel">
                                <div class="panel-head">
                                    <h3>{{ __t('Attendance Log Information') }}</h3>
                                </div>
                            </div>
                            <div class="px-3 pt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{__t('Employee')}}:</p>
                                            <div class="d-flex lh-1 align-items-center">
                                                @if(isset($record->attendance_log_staff_relation->image) && $record->attendance_log_staff_relation->image)
                                                    <img src="{{ $record->attendance_log_staff_relation->image }}" class="img-cover mr-2 w-48px h-48px rounded-10px" width="48" height="48" style="border-radius: 2px !important;">
                                                @else
                                                    <img src="{{ \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($record->attendance_log_staff_relation->name, $record->attendance_log_staff_relation->id, 48, null) }}" class="img-cover mr-2 w-48px h-48px rounded-10px" width="48" height="48" style="border-radius: 2px !important;">
                                                @endif
                                                <div>
                                                    <h5 class="font-weight-bold fs-16 lh-1 mb-1">{{$record->attendance_log_staff_relation->name}}</h5>
                                                @if($record->staff_id == -1)
                                                    <a href="" target="_blank" class="text-inactive font-weight-medium fs-12 text-decoration-underline">#{{$record->attendance_log_staff_relation->code}}</a>
                                                @else()
                                                    <a href="{{route('owner.staff.show', ['staff' => $record->staff_id])}}" target="_blank" class="text-inactive font-weight-medium fs-12 text-decoration-underline">#{{$record->attendance_log_staff_relation->code}}</a>
                                                @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{__t('Sign')}}:</p>
                                            <p class="mb-0">{{formatForView($record->time, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC)}}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{__t('Source')}}:</p>
                                            <p class="mb-0">{{$record->source_name}}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{__t('Session ID')}}:</p>
                                            <p class="mb-0">{{$record->session_id}}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{__t('Created Date')}}:</p>
                                            <p class="mb-0">{{formatForView($record->created, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC)}}</p>
                                        </div>
                                    </div>
                                    @if($record->attendance_restriction_log && $record->attendance_restriction_log->ip)
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{__t('IP')}}:</p>
                                            <p class="mb-0">{{$record->attendance_restriction_log->ip}}</p>
                                        </div>
                                    </div>
                                    @endif
                                    @if($record->attendance_restriction_log && $record->attendance_restriction_log->image)
                                    <div class="col-md-6">
                                        <div class="mb-4 lightgallery">
                                            <p class="text-muted mb-1">{{__t('Sign Photo')}}:</p>
                                            <div class="d-inline-block mb-1">
                                                <div class="pointer" data-src="{{$record->attendance_restriction_log->image}}">
                                                    <img src="{{$record->attendance_restriction_log->image}}" class="img-cover w-120px h-120px" width="120" height="120">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                    @if($record->attendance_restriction_log && $record->attendance_restriction_log->location)
                                    <div class="col-md-6">
                                        <div class="row">
                                        <?php
                                            $locationLabel = $record->getLocationLabel();
                                                if(!empty($locationLabel)){
                                            ?>
                                                    <div class="col-md-12">
                                                        <div class="mb-4">
                                                            <p class="text-muted mb-1">{{__t('Label')}}:</p>
                                                            <p class="pre mb-0">{{$locationLabel}}</p>
                                                        </div>
                                                    </div>
                                            <?php
                                                }
                                            ?>
                                            <div class="col-md-12">
                                                <div class="mb-4">
                                                    <p class="text-muted mb-1">{{__t('Location')}}:</p>
                                                    @include('partials.form.input_template', [
                                                        'label' => false,
                                                        'div' => 'form-group',
                                                        'initJavascript' => true,
                                                        'name' => 'map',
                                                        'value' => implode(',', [$record->attendance_restriction_log->location_long, $record->attendance_restriction_log->location_lat]),
                                                        'type' => 'map',
                                                        'attributes' => [
                                                            'viewOnly' => true,
                                                            'required' => 'required',
                                                        ]
                                                    ])
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>

    @include('partials.modals.create_delete_modal')

    <!-- end page contents -->
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{{asset('/js/attendance/attendance_flags/activity_log.js')}}"></script>
@endpush
