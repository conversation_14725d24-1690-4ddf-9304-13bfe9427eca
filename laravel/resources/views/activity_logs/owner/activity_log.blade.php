<link rel="stylesheet" type="text/css" href="/css/timeline_v818.css" />

    <form action="" style="background-color:#e4ebf2; padding: 20px;">
        <div class="activity-log activity-log-loaded mb-3">
            @php
                $lastTimestamp = '';
                $user = getAuthOwner();
            @endphp
            <div id="timeline_content">
                <div class="timeline-wrapper mobile-timeline-wrapper">
                    @if (count($pagination))
                        @foreach($items as $k => $activityLog)
                        @if(date('Y-m-d', strtotime(convertFromUtc($activityLog->created))) != $lastTimestamp)
                            <div class="date-label">
                                <p>{{time2str(convertFromUtc($activityLog->created))}}</p>
                            </div>
                            @endif
                            <!-- activityLogIds
                            actionLineIds -->
                            @if($activityLog->type == 'activity_log')
                                @include('activity_logs.owner.partials.activity_log_item', [
                                    'activityLog' => $activityLogMapByIds[$activityLog->id]
                                ])
                            @elseif(isset($actionSringsMapById[$activityLog->id]))
                                @include('activity_logs.owner.partials.action_line_item', [
                                    'activityLog' => $actionSringsMapById[$activityLog->id],
                                    'user' => $user,
                                ])
                            @endif

                        @php
                            $lastTimestamp = date('Y-m-d', strtotime(convertFromUtc($activityLog->created)  ));
                        @endphp
                    @endforeach

                    @else
                        <center><b>{{__t('No Logs Found')}}</b></center>
                    @endif
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
        <!-- end page contents -->
    </form>
@php $currentPage = $pagination->currentPage();@endphp
<input type="hidden" value="{{$currentPage - 1}}" data-disable='{{$currentPage == 1}}' id="page-left" />
<input type="hidden" value="{{$currentPage + 1}}" data-disable='{{$currentPage == $pagination->lastPage()}}'
       data-last="{{$pagination->lastPage()}}" id="page-right" />

