@extends('layouts.owner')
@php
    $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
@endphp
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css"/>
    @endpush
    {!! Form::model(null, ['class' => 'form-validate', 'method' => 'POST', 'url' => route('owner.attendance_days.calculation.store_manual')]) !!}

    @include('partials.form.page-head', [
        'actions' => [
            [
                'title' => 'Cancel',
                'type' => 'button',
                'name' => '',
                'class' => 'cancel-btn btn-icon btn-secondary',
                'value' => '',
                'icon' => '<i class="mdi mdi-close"></i>',
                'id' =>'',
                'attr' => ['key' => 'value']
            ],
            [
                'title' => 'Next',
                'type' => 'submit',
                'name' => '',
                'class' => 'btn-icon btn-success ml-sm-2',
                'value' => '',
                'icon' => '<i class="fa fa-save"></i>',
                'id' => '',
                'attr' => ['key' => 'value']
            ]
        ]
    ])

    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Information'), __t('Attendance Day'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @php
                    $jsFormats = getDateFormats('new_js');
                    $siteFormat = getCurrentSite('date_format');
                    $siteFormat = strtoupper($jsFormats[$siteFormat]);
                @endphp

                @include('partials.form.input_template',[
                    'label' => __t('Employee'),
                    'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                    'div' => 'col-md-6 form-group',
                    'name' => 'employee',
                    'type' => 'selectStaff',
                    'options' => $staffOptions,
                    'value' => old('employee', null),
                    'attributes' => [
                        'original-name' => 'employee',
                        'required' => 'required',
                        'placeholder' => $staffInputPlaceholder
                    ],
                ])

                @include('partials.form.input_template', [
                    'label' => __t('Date'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group form-group-icon reverse',
                    'name' => 'date',
                    'value' => old('date', formatForView(today(), \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)),
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                    'attributes' => [
                        'original-name' => 'date',
                        'required' => 'required',
                        'placeholder' => $siteFormat
                    ],
                ])

                @if (\App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift'))
                    @include('partials.form.input_template', [
                        'options' => \App\Utils\MultipleShiftTypeUtil::getTypes(),
                        'label' => __t('Shift Type'),
                        'inputClass' => 'select form-control',
                        'div' => 'col-md-6 form-group',
                        'name' => 'shift_type',
                        'type' => 'select',
                        'value' => old('shift_type', today()),
                        'attributes' => [
                            'original-name' => 'shift_type',
                            'required' => 'required'
                        ]
                    ])
                @endif
            </div>
        </div>
    </div>

    {{csrf_field()}}
    {!! Form::close() !!}

    @push('scripts')
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
    @endpush
@endsection
