@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}" />
@endpush
@section('content')
@php
    $oldValues =[];
    if (old('system_emp_id') !== null){
        foreach (old('system_emp_id') as $k => $v){
            $oldValues[] = [
            'system_emp_id' => old('system_emp_id')[$k],
            'machine_emp_id' => old('machine_emp_id')[$k]
            ];
        }
    }
@endphp

{!!
    Form::model($form_record ?? [], [
    'method' => 'post',
    'url' => route('owner.machines.unmapped_employee.store',['machine' => $machine_id]),
    'class' => 'form-validate'
    ])
!!}

@include('partials.form.page-head', [
    'title' => '',
    'actions' => [
        [
            'title' => __t('Cancel'),
            'type' => 'button',
            'name' => 'cancel',
            'class' => 'btn-icon btn-secondary cancel-btn',
            'value' => '',
            'icon' => '<i class="mdi mdi-close"></i>',
            'id' => 'cancel',
            'attr' => ['key' => 'value']
        ],
        [
            'title' => __t('Save'),
            'type' => 'submit',
            'name' => '',
            'class' => 'btn-icon btn-success ml-sm-2',
            'value' => '',
            'icon' => '<i class="mdi mdi-content-save-outline"></i>',
            'id' =>'',
            'attr' => ['key' => 'value']
        ]
    ]
])

<div class="card mb-3">
    <div class="card-header">
        <h6>{{__t('Machine Unmapped Employees')}}</h6>
    </div>
    <div class="card-body">

        <div class="panel">
            <div class="panel-body">
                <div class="multiple-table">
                    <table id="" class="table table-bordered border-0 display nowrap" style="width: 100%">
                        <thead>
                            <tr>
                                <th>{{__t('Machine User Pin')}}</th>
                                <th>{{__t('Machine User Name')}}</th>
                                <th>{{__t('System Employee')}}</th>
                                <th>{{__t('Ignore')}}</th>
                            </tr>
                        </thead>
                        <tbody class="border-top">
                            @foreach($related_form_data['unMappedEmployees'] as $index=>$unMappedEmployees)
                            <tr>
                                <td>
                                    <span>{{$unMappedEmployees->staff_machine_id}}</span>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'machine_map_id['.$index.']',
                                        'id' => '',
                                        'inputClass' => 'form-control disabled',
                                        'div' => 'form-group mb-0',
                                        'type' => 'hidden',
                                        'attributes' => ['placeholder' => '', 'min' => 1],
                                        'value' => $unMappedEmployees->id
                                    ])
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'machine_emp_id['.$index.']',
                                        'id' => "machine_emp_id_".$index,
                                        'inputClass' => 'form-control disabled',
                                        'div' => 'form-group mb-0',
                                        'type' => 'hidden',
                                        'attributes' => ['placeholder' => __t('Enter Employee ID'), 'min' => 1],
                                        'value' => $unMappedEmployees->staff_machine_id
                                    ])
                                </td>
                                <td>
                                    <span>{{$unMappedEmployees->machine_employee_name}}</span>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'machine_emp_name['.$index.']',
                                        'id' => "machine_emp_name_".$index,
                                        'inputClass' => 'form-control disabled',
                                        'div' => 'form-group mb-0',
                                        'type' => 'hidden',
                                        'attributes' => ['placeholder' => __t('Enter Employee Name')],
                                        'value' => $unMappedEmployees->machine_employee_name
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template',[
                                        'label' => false,
                                        'name' => 'system_emp_id['.$index.']',
                                        'id' => 'system_emp_id_'.$index,
                                        'inputClass' => 'form-control',
                                        'div' => 'form-group mb-0',
                                        'type' => 'select',
                                        'options' => $related_form_data['staffs'],
                                        'attributes' => ['placeholder' => sprintf(__t('Select a %s'),__t('Employee'))],
                                        'value' => old("system_emp_id.$index", $related_form_data['staffByNames'][strtolower($unMappedEmployees->machine_employee_name)] ?? ''),
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'ignore['.$index.']',
                                        'id' => '',
                                        'inputClass' => 'form-control',
                                        'div' => 'form-group mb-0',
                                        'type' => 'checkbox',
                                        'options' => [0=>'No', 1=>'Yes'],
                                        'attributes' => ['placeholder' => __t('Select an option')],
                                        'value' => 1,
                                        'checked' => (bool) old("ignore.$index", false),
                                    ])
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>
<!-- end page contents -->
@endsection
@push('scripts')
<script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
<script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
<script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
<script src="{{asset('/js/attendance/machine/mapping/create.js')}}"></script>
@endpush
