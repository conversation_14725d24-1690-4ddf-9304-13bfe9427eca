@extends('layouts.owner')
@php
    $multiListingActions = [];

    $listingRowActions = [
        'edit' => [
            'class' => 'edit-action',
            'icon' => '',
            'title' => __t('Edit'),
            'url' => [
                'name' => 'owner.machines.mappings.edit',
                'parameters' => [
                    'machine' => ['value' => $machine_id],
                    'mapping' => ['field' => 'id']
                ]
            ]
        ],
        'delete' => [
            'class' => 'delete-action',
            'prompt' => [
                'method' => 'DELETE',
                'message' => sprintf(__t('Are you sure you want to delete this %s?'), __t('Machine Employee Mapping')),
                'header' => __t('Delete')
            ],
            'icon' => '',
            'title' => __t('Delete'),
            'url' => [
                'name' => 'owner.machines.mappings.destroy',
                'parameters' => [
                    'machine' => ['value' => $machine_id],
                    'mapping' => ['field' => 'id']
                ]
            ]
        ]
    ];

    $listingActions = [
        'search' => [
            'class' => 'btn btn-icon btn-success responsive d-inline-block d-md-none',
            'url' => [
                'name' => 'javascript:void(0);',
                'value' => 'javascript:void(0);'
            ],
            'icon' => '<i class="mdi mdi-magnify"></i>',
            'title' => '',
            'attributes' => [
                'data-toggle' => 'collapse',
                'data-target' => '#collapseSearch',
                'aria-expanded' => 'true',
                'aria-controls' => 'collapseSearch'
            ]
        ],
        'add' => [
            'class' => 'btn btn-icon btn-success responsive',
            'url' => [
                'name' => route('owner.machines.mappings.create', ['machine' => $machine_id]),
                'value' => route('owner.machines.mappings.create', ['machine' => $machine_id])
            ],
            'icon' => '<i class="mdi mdi-plus"></i>',
            'title' => sprintf(__t('New %s'), __t('Machine Employee Mapping'))
        ]
    ];
@endphp

@section('content')
    {{-- contains multi actions section pagination section and buttons section (actions section) $listingActions array of listing actions --}}
    @include('partials.listing.page-head', ['listing_actions' => $listingActions, 'multi_listing_actions' => $multiListingActions])
    @include('partials.listing.filter_form', ['filters' => $filters, 'filtered' => $filtered])
    @include('partials.listing.basic_listing', [
        'row_actions' => isset($listingRowActions)?$listingRowActions:[],
        'data' => $pagination,
        'tableClass' => 'not-clickable',
        'fields' => [
            [
                'label' => __t('Employee'),
                'template' => '<strong>{{$record->employee_name}}</strong>{{" #" .$record->employee->id}}'
            ],
            [
                'label' => __t('Machine Employee ID'),
                'field' => 'staff_machine_id'
            ]
        ]
    ])
@endsection

@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/index/index.js"></script>
    <script> var staffSearchUrl = "{{route('owner.staff.search')}}"; </script>
    <script src="{{asset('js/attendance/attendance-log/listing.js')}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
@endpush
