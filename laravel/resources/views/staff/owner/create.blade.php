@extends('layouts.owner')
@inject('Plugins', 'App\Facades\Plugins')
@inject('PluginUtil', 'App\Utils\PluginUtil')
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}"/>
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}js/forms/plugin_jqueryui.js"/>
    @endpush
    @php
        use App\Models\Staff;
        if(!isset($form_record) || !isset($form_record->id)) {
            $route = route($routesPrefix.'staff.store');
            $method = 'post';
            $isAdd = true;
        } else {
            $route = route($routesPrefix.'staff.update',['staff' => $form_record->id]);
            $method = 'put';
            $isAdd = false;
        }
        $isOwnerUser = !$isAdd && ($form_record->role_id == Staff::OWNER_ROLE_ID);
    @endphp
    {!! Form::model($form_record,['class' => 'form-validate','method' => $method,'url' => $route, 'enctype' => 'multipart/form-data']) !!}
    @include('partials.form.page-head', ['title' => '',
	 'actions' => [
		['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'btn-icon btn-secondary cancel-btn', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
		['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attr' => ['key' => 'value']]
	 ]])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{__t('Employee Information')}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','data-parsley-errors-messages-disabled'=>"",'required' => 'required', ],'label' => 'First Name','inputClass' => 'form-control','name' => 'name', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template',['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Surname','inputClass' => 'form-control','name' => 'last_name', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => ['data-parsley-errors-messages-disabled'=>""],'label' => 'Middle Name','inputClass' => 'form-control','name' => 'middle_name', 'type' => 'text'])
                </div>
                @include('partials.form.input_template', [
                    'label' => __t('Employee Picture'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'employee_picture_file',
                    'attributes' => [
                        'accepts' => 'image/*'
                    ],
                    'type' => 'avatar',
                    'value' => ($form_record)? $form_record->image:null,
                ])
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => ['rows' => '4','autoResize' => true,'style' => 'height: 114px'],'label' => 'Notes','inputClass' => ' form-control','name' => 'note', 'type' => 'textArea'])
                </div>
                <div class="col-md-12">
                    <hr class="pt-2 pb-2 spacer">
                </div>
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Mobile Number','inputClass' => 'form-control','name' => 'mobile', 'type' => $isOwnerUser?'text':'number'])
                </div>
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Phone Number','inputClass' => 'form-control','name' => 'home_phone', 'type' => $isOwnerUser?'text':'number'])
                </div>
                <div class="col-md-12">
                    <hr class="pt-2 pb-2 spacer">
                </div>
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Address Line 1','inputClass' => 'form-control','name' => 'address1', 'type' => 'text'])
                </div>
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Address Line 2','inputClass' => 'form-control','name' => 'address2', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'City','inputClass' => 'form-control','name' => 'city', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'State','inputClass' => 'form-control','name' => 'state', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Postal Code','inputClass' => 'form-control','name' => 'postal_code', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','empty' => __t('Please Select'),'data-parsley-errors-messages-disabled'=>"",'required' => 'required'], 'options' => $related_form_data['countries']->toArray(),'value' => old('country_code') ?: ((!$isAdd && $form_record->country_code) ? $form_record->country_code : getCurrentSite('country_code')),'label' => 'Country','inputClass' => 'select form-control','name' => 'country_code', 'type' => 'select'])
                </div>

                <div class="col-md-4">
                    @include('partials.form.input_template', [
                        'attributes' => [
                            'onchange' => 'handleChangecitizenshipStatus(this);',
                            'empty' => __t('Please Select')
                        ],
                            'options' => \Izam\Daftra\Staff\Util\StaffCitizenshipStatus::getStatusList(),
                            'label' => 'Citizenship Status',
                            'inputClass' => 'select form-control',
                            'name' => 'citizenship_status',
                            'type' => 'select',
                            'value' => old('citizenship_status') ?: ((!$isAdd && $form_record->citizenship_status) ? $form_record->citizenship_status : ''),


                    ])

                </div>

                <div class="col-md-4 toglecitizenshipStatus">
                    @include('partials.form.input_template', [
                        'attributes' => [
                            $isOwnerUser?'disabled':'' => 'disabled',
                            'empty' => __t('Please Select')
                        ],
                        'options' => $related_form_data['countries']->toArray(),
                        'value' => old('country_code') ?: ((!$isAdd && $form_record->nationality) ? $form_record->nationality : ''),
                        'label' => 'nationality',
                        'inputClass' => 'select form-control ',
                        'name' => 'nationality',
                        'type' => 'select'
                    ])
                </div>
                <div class="col-md-4 toglecitizenshipStatus">
                    @include('partials.form.input_template', [
                        'attributes' => [
                        ],
                        'value' => (!$isAdd && $form_record->residence_expiry_date ) ? formatForView($form_record->residence_expiry_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : '',
                        'div' => 'form-group form-group-icon',
                        'label' => 'Expiry Date of Residence',
                        'inputClass' => 'form-control bg-white ',
                        'name' => 'residence_expiry_date',
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                    ])
                </div>

            </div>
        </div>
    </div>
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Information'), __t('Account'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @if ($isAdd)
                    <div class="col-md-12">
                        @include('partials.form.input_template', ['label' => [], 'name' => 'type', 'type' => 'hidden', 'value' => 'user'])
                    </div>
                @endif

                @if (!$isAdd)
                        <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','data-parsley-errors-messages-disabled'=>"",'required' => ''], 'options' => ['user' => __t('User'), 'employee' => __t('Employee')],'label' => 'Employee Mode','inputClass' => 'select form-control','name' => 'type', 'type' => 'select', 'value' => request('type')])
                        </div>
                   {{-- @if ($form_record->type === \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE)
                        <div class="col-md-6">

                            @include('partials.form.input_template', ['label' => [], 'name' => 'type', 'type' => 'hidden'])
                        </div>
                    @else
                        <div class="col-md-12">
                            @include('partials.form.input_template', ['label' => [], 'name' => 'type', 'type' => 'hidden', 'value' => 'user'])
                        </div>
                    @endif--}}
                @endif

                @include('partials.form.input_template', [
                    'label' => __t('Code'),
                    'name' => 'code',
                    'value' => old('code', isset($form_record->code) ? $form_record->code : $related_form_data['code'] ),
                    'inputClass' => 'form-control bg-white',
                    'div' => 'form-group col-md-6',
                    'type' => 'text',
                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled', $isOwnerUser?'':'required' => 'required'],
                ])
                <input type="hidden" name="generated_code" value="{{$related_form_data['code']}}" />

                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Email Address','inputClass' => 'form-control','name' => 'email_address', 'type' => 'email'])
                </div>

                @if($isAdd)
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','data-parsley-errors-messages-disabled'=>"",'required' => 'required'], 'options' => [1 => __t('Active'), 0 => __t('Inactive')],'label' => 'Status','inputClass' => 'select form-control','name' => 'active', 'type' => 'select', 'value' => 1])
                    </div>
                @endif
                <div class="col-md-12"></div>
            </div>
            <div class="form-row form-group">
                <div class="col-md-6 form-group mb-lg-0">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','onchange' => 'handleChange(this);'],'label' => __t('Allow Access to The System'),'labelClass' => 'custom-control-label','inputClass' => 'custom-control-input','div' => 'custom-control custom-checkbox p-0','name' => 'can_access_system', 'type' => 'checkbox', 'value' => 1, 'checked' => $isAdd ? true : $form_record->can_access_system])
                </div>
                <?php if(!$isOwnerUser): ?>
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['label' => __t('Send Credentials to Employee Email'),'labelClass' => 'custom-control-label','inputClass' => 'custom-control-input','div' => 'custom-control custom-checkbox ml-md-2 mr-md-2 p-0 send_credentials','name' => 'send_credentials', 'type' => 'checkbox', 'value' => 1])
                    </div>
                <?php endif; ?>
            </div>
            <div class="form-row">
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','placeholder' => sprintf(__t('Select %s'),__t('Role')),'data-parsley-errors-messages-disabled'=>""], 'options' => $related_form_data['roles']->toArray(),'div' => 'form-group role-toggle', 'label' => 'Role','inputClass' => 'select form-control','name' => 'role_id', 'type' => 'select'])
                </div>
                @if($related_form_data['languages']->count() > 1)
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled', 'placeholder' => __t('Display Language'),'data-parsley-errors-messages-disabled'=>""], 'options' => $related_form_data['languages']->toArray(),'div' => 'form-group lang-toggle', 'label' => 'Display Language','inputClass' => 'select form-control','name' => 'language_code', 'type' => 'select',"value" => $form_record->language_code ?? getCurrentSite("language_code")])
                    </div>
                @endif
            </div>
            <div class="col-md-12">
                <hr class="pt-0 pb-0 spacer">
            </div>
            <div class="form-row">
                @if($Plugins::pluginActive($PluginUtil::BookingPlugin) || $Plugins::pluginActive($PluginUtil::NEW_BOOKING_PLUGIN))
                    <div class="col-md-6">
                        @include('partials.form.input_template', [
                            'attributes' => [
                                'placeholder' => sprintf(__t('Select %s'),__t('Shift')),
                                'multiple' => true
                            ], 
                            'options' => $related_form_data['shifts']->toArray(),
                            'label' => 'Booking Shift',
                            'inputClass' => 'select form-control',
                            'name' => 'shift[]', 
                            'type' => 'select', 
                            'value' => ($isAdd && !empty($related_form_data['default_booking_shift_id'])) ? [(int) $related_form_data['default_booking_shift_id']] : null])
                    </div>
                @endif
                @if($Plugins::pluginActive($PluginUtil::BranchesPlugin))
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','required' => 'required', 'placeholder' => sprintf(__t('Select %s'),__t('Branch')),'data-parsley-errors-messages-disabled'=>""], 'options' => $allStaffBranches,'value' => (!$isAdd) ? $form_record->branch_id : '','div' => 'form-group', 'label' => 'Branch','inputClass' => 'select form-control','name' => 'branch_id', 'type' => 'select'])
                    </div>

                    <div class="col-md-6" id="branches_dev">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled', 'id' => 'branches','placeholder' => sprintf(__t('Select %s'),__t('Accessible Branches')),'multiple' => true, 'data-parsley-errors-messages-disabled'=>"",'required' => 'required'], 'options' => $allStaffBranches,'div' => 'form-group role-toggle','label' => 'Accessible Branches','inputClass' => 'form-control select2-multiple','name' => 'branches[]','id' => 'branches', 'type' => 'select'])
                    </div>
                @endif
            </div>
            @include('partials/custom_forms/form_fields', ['header' => __t("Staff More Information")])
        </div>
    </div>

    {!! Form::close() !!}
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
    <script>

       let user_roles = @json($related_form_data['user_roles']);
       let employee_roles = @json($related_form_data['employee_roles']);
        const can_access_system_checkbox = "input[name='can_access_system']";
        const user_type_select = "select[name='type']";
        const user_type_input = "input[name='type']";

{{--        @if(request('type') === \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE || (isset($form_record->type) && $form_record->type === \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE))--}}
{{--            disable_allow_access_checkbox(can_access_system_checkbox)--}}
{{--        @endif--}}


            $(user_type_select).on('change', function () {
                if (this.value === '{{ \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE }}') {
                    $("#role_id").empty().trigger('change');
                    var newOption = new Option('<?= sprintf(__("Select %s"), __("Role")) ?>', '', false, false);
                    $('#role_id').append(newOption);
                    for (const role in employee_roles) {
                        var newOption = new Option(employee_roles[role], role, false, false);
                        $('#role_id').append(newOption);
                    }
                    $('#role_id').trigger('change')
                    $("#branches_dev").css('display', 'none');
                } else {
                    $("#role_id").empty().trigger('change')
                    for (const role in user_roles) {
                        var newOption = new Option(user_roles[role], role, false, false);
                        $('#role_id').append(newOption);
                    }
                    $('#role_id').val(<?= $form_record->role_id??null ?>);
                    $('#role_id').trigger('change');
                    $("#branches_dev").css('display', 'block');
                }

                toggleRoleRquired();
                $(user_type_input).val(this.value);
            });

        function disable_allow_access_checkbox(checkbox_id) {
            $(checkbox_id).prop("checked", false);
            $(checkbox_id).attr("disabled", true);
        }

        function handleChangecitizenshipStatus(citizenshipStatus) {
            $(document).ready(function () {
                if (citizenshipStatus.value == 'resident') {
                    $('.toglecitizenshipStatus').show();
                } else {
                    $('.toglecitizenshipStatus').hide();
                }
            });
        }


        function handleChange(checkbox) {
            toggleRoleRquired();
            if (checkbox.checked) {
                document.getElementById("email_address").setAttribute("required", "required");
                if(document.getElementById("role_id") && $("#type").val() != "employee") {
                    document.getElementById("role_id").setAttribute("required", "required");
                }
                if(document.getElementById("language_code")) {
                    document.getElementById("language_code").setAttribute("required", "required");
                }
                var branchElement = document.getElementById("branches");
                if (branchElement)
                    branchElement.setAttribute("required", "required");
                $('label[for="email_address"] span').remove();
                if(document.getElementById("role_id")) {
                    $('label[for="role_id"] span').remove();
                }
                if(document.getElementById("language_code")) {
                    $('label[for="language_code"] span').remove();
                }
                $('label[for="email_address"]').append('<span class="required">*</span>');
                if(document.getElementById("role_id") && $("#type").val() != "employee") {
                    $('label[for="role_id"]').append('<span class="required">*</span>');
                }
                if(document.getElementById("language_code")) {
                    $('label[for="language_code"]').append('<span class="required">*</span>');
                }
                $('.role-toggle').show();
                $('.lang-toggle').show();
                $('.send_credentials').show();
            } else {
                document.getElementById("email_address").removeAttribute("required");
                if(document.getElementById("role_id")) {
                    document.getElementById("role_id").removeAttribute("required");
                }
                if(document.getElementById("language_code")) {
                    document.getElementById("language_code").removeAttribute("required");
                }
                var branchElement = document.getElementById("branches");
                if (branchElement)
                    branchElement.removeAttribute("required");
                $("#branches_dev").css('display', 'none');
                $('label[for="email_address"] span').remove();
                if(document.getElementById("role_id")) {
                    $('label[for="role_id"] span').remove();
                }
                if(document.getElementById("language_code")) {
                    $('label[for="language_code"] span').remove();
                }
                $('.role-toggle').hide();
                $('.lang-toggle').hide();
                $('.send_credentials').hide();
            }
        }

        $(document).ready(function () {
            @php if(request('type') == "employee" || (!$isAdd)) { @endphp
                handleChange(document.getElementById('can_access_system'));
                toggleRoleRquired();
            @php } @endphp

            handleChangecitizenshipStatus(document.getElementById('citizenship_status'))

        });

       function toggleRoleRquired()
       {
           if ($("#type").val() == "employee") {
               $("#role_id").removeAttr('required');
               $("#branches").removeAttr('required').trigger('change');
               $("#branches_dev").css('display', 'none');
               $('label[for="role_id"] span.required').remove();
               $("#role_id").attr("data-placeholder", '<?= __t("Employee")?>');
               $("#role_id").attr("placeholder", '<?= __t("Employee")?>');
               if ($("#role_id").data("select2")) {
                   $("#role_id").data("select2").selection.placeholder.text = '<?= __t("Employee")?>';
               }
               $("#select2-role_id-container .select2-selection__placeholder").first().text('<?= __t("Employee")?>');
           } else {
               if ($(can_access_system_checkbox) && $(can_access_system_checkbox).is(":checked")) {
                   $("#branches_dev").css('display', 'block');
                   $("#branches").attr('required', 'required').trigger('change');
                   $("#role_id").attr('required', 'required');
                   $('label[for="role_id"] span.required').remove();
                   $('label[for="role_id"]').append('<span class="required">*</span>');
                   $("#select2-role_id-container .select2-selection__placeholder").first().text('<?= sprintf(__t('Select %s'),__t('Role')) ?>');
               }

           }
       }
    </script>
@endpush
