
<?php
use Izam\Daftra\Staff\Util\StaffCitizenshipStatus;
use App\Utils\PermissionUtil;
$isOwnerUser = isset($record['role_id']) && ($record['role_id'] == \App\Models\Staff::OWNER_ROLE_ID);
?>
@extends('layouts.owner')
@inject('Plugins', 'App\Facades\Plugins')
@inject('PluginUtil', 'App\Utils\PluginUtil')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/activity_log/activity_log.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/staff/notes.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/staff/show.min.css?v={{CSS_VERSION}}"/>
@endpush

@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-6">
                    <div class="pages-head-title">
                        <h2>{{$record->name}} {{$record->last_name}} #{{$record->code ?? $record->id}} <span style="margin-right: 10px"
                                                                           class="status status-{{$record->active ? 'active' : 'inactive'}}"><i
                                    class="cir"></i>{{$record->active ? __t('Active') : __t('Inactive')}}</span></h2>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="action-buttons">
                        <div class="d-flex justify-content-end">
                            @include('partials.related_forms_add_btn')
                            @include('partials.show.record_nav', ['url' => 'owner.staff.show', 'name' => 'staff'])
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header p-3 bg-white">
            <div class="row">
                <div
                    class="col-xl-8 col-lg-8 col-12 d-flex flex-wrap justify-content-center justify-content-sm-start text-center text-sm-left">
                    <div class="flex-shrink-1">
                        <div class="media text-center">
                            <img
                                src="{{ \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($record->name, $record->id, 120, !empty($record->photo) ? $record->photo : null) }}"
                                class="img-fluid avatar-lg" width="120" height="120" alt="name">
                        </div>
                    </div>
                    <div class="avatar px-4 pt-3 pt-sm-0">
                        <h4>{{$record->name}} {{$record->middle_name}} {{$record->last_name}} <span class="fs-14 text-black-50">({{ userType($record->type) }})</span></h4>
                        <small class="text-muted">#{{$record->code ?? $record->id}}</small>
                    </div>
                </div>
                <div class="col-12 d-lg-none">
                    <br>
                </div>
                <div class="col-xl-4 col-lg-4 col-12">
                    @if($record->mobile)
                        <a href="tel:{{$record->mobile}}" class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-cellphone"></i>{{$record->mobile}}</a>
                    @endif
                    @if($record->email_address)
                        <a href="mailto:{{$record->email_address}}" class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-email-variant"></i>{{$record->email_address}}</a>
                    @endif
                    @if($record->can_access_system && isOwnerOrSuperAdmin() && !$isOwnerUser)
                        <a href="{{getCakeURL(['controller' => 'staffs', 'action' => 'login_as', $record->id])}}"
                           class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-account-box"></i>{{sprintf(__t('Login as %s'), $record->name ?: '')}}
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="card border-white mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                <a href="{{route('owner.staff.edit',['staff' => $record->id])}}" class="btn btn-light"><i
                        class="mdi mdi-pencil-outline mr-1"></i>{{__t('Edit')}}</a>
                @if(!$isOwnerUser)
                    @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Employee'), 'href' => route('owner.staff.destroy', ['staff' => $record])])
                @endif
                <a href="{{getCakeURL(['controller' => 'posts', 'action' => 'post', 5, $record->id])}}"
                   class="btn btn-light"><i
                        class="mdi mdi-note-text mr-1"></i>{{sprintf('%s %s / %s', __t('Add'), __t('Note(s)'), __t('Attachment(s)')) }}
                </a>
                @if(!$isOwnerUser)
                    @if($record->active)
                        <a href="{{ route('owner.staff.deactivate', ['staff' => $record->id]) }}" class="btn btn-light"><i
                                class="fas fa-comment-alt-edit mr-1"></i>{{ __t('Mark as Inactive') }}</a>
                    @else
                        <a href="{{ route('owner.staff.activate', ['staff' => $record->id]) }}" class="btn btn-light"><i
                                class="fas fa-comment-alt-edit mr-1"></i>{{ __t('Mark as Active') }}</a>
                    @endif

                    @if($record->can_access_system)
                        <a href="{{ route('owner.staff.sendLoginDetails', ['staff' => $record->id]) }}" class="btn btn-light"><i class="mdi mdi-page-next-outline mr-1"></i>{{ __t('Send Login Details') }}</a>
                    @endif
                @endif
                @if($isOwnerUser && ((getAuthStaff() && getAuthStaff('role_id') == \App\Models\Staff::OWNER_ROLE_ID)|| (getAuthOwner('staff_id') == 0)))
                    <a href="{{ getCakeURL([
                        'controller' => 'owners',
                        'action' => 'change_password'
                    ]) }}" class="btn btn-light"><i class="mdi mdi-lock-reset mr-1"></i>{{ __t('Change Password') }}</a>
                @elseif($record->can_access_system)
                    <a href="{{ route('owner.staff.changePassword', ['staff' => $record->id]) }}" class="btn btn-light"><i class="mdi mdi-lock-reset mr-1"></i>{{ __t('Change Password') }}</a>
                @endif
                @if (
                 \App\Facades\Permissions::checkPermission([PermissionUtil::MANAGE_HRM_SYSTEM, PermissionUtil::Staff_Add_New_Staffs, PermissionUtil::Staff_Edit_Staffs, PermissionUtil::Staff_View_Staffs]))
                  <a role="button" class="btn btn-light" id="btn-employee-documents"><i
                    class="mdi mdi-file-document-multiple mr-1"></i>{{ __t('Documents & Emails') }}</a>
                @endif
            </div>
        </div>
        <div class="card-body p-1 p-lg-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab"
                           aria-controls="details" aria-selected="true">{{ __t('Details') }}</a>
                    </li>
                    @if($notesCount)
                    <li class="nav-item">
                        <a class="nav-link" id="NotesBlock-tab" data-toggle="tab" href="#notes-attachments"
                           role="tab" aria-controls="notes-attachments" aria-selected="false"

                           data-notes-url="{{getCakeURL([
                           'controller' => 'posts',
                           'action' => 'list_post',
                           \App\Utils\PostTypesUtil::STAFF_TYPE,
                           $record->id
                           ])}}">
                            {{ sprintf('%s / %s', __t('Note(s)'), __t('Attachment(s)')) }} ({{$notesCount}})
                        </a>
                    </li>
                    @endif
                    @php
                        $tabsViewHelper = new \Izam\Forms\Tab\Helper\TabsViewHelper();
                    @endphp

                        @foreach($tabs as $tab)
                        {!! $tabsViewHelper->renderTabHeader($tab) !!}
                        @endforeach
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab"
                           aria-controls="activity-log" aria-selected="false">{{ __t('Activity Log') }}</a>
                    </li>

                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">

                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Employee Information') }}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Mobile Number') }}:</p>
                                        <p>{{$record->mobile}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Phone Number') }}:</p>
                                        <p>{{$record->home_phone}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Address') }}:</p>
                                        <p>
                                            @if($record->address1)
                                                {{$record->address1}}<br>
                                            @endif
                                            @if($record->address2)
                                                {{$record->address2}}<br>
                                            @endif
                                            {{(!empty($record->city))? $record->city.', ':''}}{{(!empty($record->state))? $record->state.', ':''}} {{is_rtl() ? ($record->country ? $record->country->ar_name : "") : $record->country->country}}
                                            @if($record->postal_code)
                                             <span>({{$record->postal_code}})</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                @if(isset($record->note))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Notes') }}:</p>
                                            <p>{{$record->note}}</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Account Information') }}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Email') }}:</p>
                                        <p>{{$record->email_address}}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Role') }}:</p>
                                        <p>{{$record->role? $record->role->name:'--'}}</p>
                                    </div>
                                </div>
                                @if($Plugins::pluginActive($PluginUtil::BranchesPlugin))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Accessible Branches') }}:</p>
                                            <p>{{$isOwnerUser? implode(', ', $allStaffBranches->toArray()): $record->branches->implode('name', ', ')}}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Branch') }}:</p>
                                            <p>{{$record->branch?->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(Plugins::pluginActive(\App\Utils\PluginUtil::BookingPlugin) && isset($record->shift) && $record->shift->count() > 0)
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Booking Shift') }}:</p>
                                            <p>{{$record->shift->implode('name', ', ')}}</p>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <p class="text-muted mb-1">{{ __t('Display Language') }}:</p>
                                        <p>{{ (!is_null($record->language))? (is_rtl()? $record->language->ar_name : $record->language->name):'' }}</p>
                                    </div>
                                </div>

                                @if (!is_null($record->citizenship_status))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Citizenship Status') }}:</p>
                                            <p>{{StaffCitizenshipStatus::getStatus($record->citizenship_status)}}</p>
                                        </div>
                                    </div>

                                        @if ($record->citizenship_status ==StaffCitizenshipStatus::RESIDENT )

                                            @if($record->staff_nationality)
                                                <div class="col-md-6">
                                                    <div class="mb-4">
                                                        <p class="text-muted mb-1">{{ __t('Nationality') }}:</p>
                                                        <p>{{ $record->staff_nationality ?(is_rtl() ? $record->staff_nationality->ar_name : $record->staff_nationality->country):''}}</p>
                                                    </div>
                                                </div>
                                            @endif
                                            @if($record->residence_expiry_date)
                                                <div class="col-md-6">
                                                    <div class="mb-4">
                                                        <p class="text-muted mb-1">{{ __t('Expiry Date of Residence') }}:</p>
                                                        <p>{{formatForView($record->residence_expiry_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</p>

                                                    </div>
                                                </div>
                                            @endif



                                        @endif
                                @endif
                            </div>
                            </div>
                        @if((!empty($customViewFields)) || isset($form) || isset($appForms))
                            <div class="panel">
                                @include('partials.custom_forms.view_fields', [
                                    'customViewFields' => $customViewFields ?? [],
                                    'header' => __t("Staff More Information")
                                ])
                            </div>
                        @endif
                    </div>
                    @foreach($tabs as $tab)
                        {!! $tabsViewHelper->renderTabDetails($tab) !!}
                    @endforeach
                    <div class="tab-pane p-3 fade" id="account-information" role="tabpanel"
                         aria-labelledby="account-information-tab">

                        <div class="p-3">
                            <div class="row">

                            </div>
                        </div>
                    </div>
                    @if($notesCount)
                    <div class="tab-pane p-3 fade" id="notes-attachments" role="tabpanel"
                         aria-labelledby="NotesBlock-tab">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ sprintf('%s / %s', __t('Note(s)'), __t('Attachment(s)')) }}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="notes-attachments-content">
                        @include('partials.layout.loader')
                        <!-- notes & attachments will be here -->
                        </div>
                    </div>
                    @endif
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">
                        <input type="hidden" value="{{\App\Utils\EntityKeyTypesUtil::STAFF_ENTITY_KEY}}"
                               id="entity_key"/>
                        <input type="hidden" value="{{$record->id}}" id="entity_id"/>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Activity Log') }}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="notesDeletePromptModal" tabindex="-1" role="dialog" aria-labelledby="notesDeletePromptModalLabel"
                 style="display: none;" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-transparent border-0 position-absolute w-100" style="z-index: 9;">
                            <h5 class="modal-title d-none" id="notesDeletePromptModalLabel"></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body text-center">
                            <i class="mdi mdi-trash-can-outline display-4 text-danger"></i>
                            <div id="notes-delete-prompt-body">
                                {{sprintf(__t('Are you sure you want to delete this %s ?'),__t('Note') )}}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <form method = 'post' id = 'deleteNoteForm' action="">
                                <input type ='hidden' name="data[Post][action]" value="delete">
                                <button type="submit" name = 'submit_btn' value='yes'  class="btn btn-danger notes-delete-btn">{{__t('Yes')}}</button>
                            </form>
                            <button type="button" class="btn btn-secondary"
                                    data-dismiss="modal">{{__t('No')}}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="notes-alert" class="alert alert-icon alert-bordered alert-dismissible border-success alert-success d-none">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="d-flex">
                    <div class="alert-icon">
                        <i class="far fa-lg fa-check-circle text-success mr-3"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-0"><strong id="notes-msg"></strong></p>
                    </div>
                </div>
            </div>

            @include('partials.modals.create_delete_modal')

        </div>
    </div>
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script>
        $(function () {
            promptHeaderEl = $('#promptModalLabel');
            promptBodyEl = $('#prompt-body');
            promptMethodEl = $('#prompt-method');
            propmptFormEl = $('#row-action-form');
            $('a[data-prompt="true"]').on('click', function (event) {
                event.preventDefault();
                message = $(this).data('prompt-message');
                requestMethod = $(this).data('prompt-method');
                header = $(this).data('prompt-header');
                action = $(this).attr('href');
                promptHeaderEl.text(header);
                promptBodyEl.text(message);
                promptMethodEl.val(requestMethod);
                propmptFormEl.attr('action', action);
                propmptFormEl.attr('method', 'post');
            });

        });
    </script>
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    @if($notesCount)
    <script src="{{asset('/js/notes/ajax-notes.js')}}"></script>
    @endif
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script>
         function getStaffIdFromCurrentUrl() {
            try {
                const url = new URL(window.location.href);
                const parts = url.pathname.split("/");
                const staffIndex = parts.indexOf("staff");
                if (staffIndex !== -1 && parts[staffIndex + 1]) {
                    return parts[staffIndex + 1];
                }
                return null;
            } catch (err) {
                console.error("Failed to parse URL", err);
                return null;
            }
        }

        const staffId = getStaffIdFromCurrentUrl();
        console.log("Staff ID:", staffId);

        const getEmployeeDocuments = async () => {
            const response = await fetch(
                `/v2/api/templates/staff/${staffId}`,
            ).then((res) => res.json());
            return response;
        };

        const generateModalHtmlContent = (entitiesLength) => {
            return `
                        <style>
                            .extended-width {
                                width: ${entitiesLength > 2 ? '1140px' : '900px'};
                                max-width: 90%;
                            }
                            @media only screen and (max-width: 993px) {
                                .extended-width {
                                    max-width: 100%;
                                }
                            }
                        <\/style>
                        <div id="container-react" class="h-100 w-100" style="overflow-y: auto;"></div>
                        <script type="module" src=\"{{ asset('js/app/main-employee-documents.js?time=${Date.now()}') }}\" async data-time=${Date.now()}><\/script>
                    `;
        };

        async function employeeDocumentsModalHtmlContent(){
            try {
                // Fetch fresh data each time the modal is opened
                const data = await getEmployeeDocuments();
                const entitiesWithTemplates = data.entities.filter(
                    (entity) => Array.isArray(entity.templates) && entity.templates.length > 0
                );
                const entitiesLength = entitiesWithTemplates?.length || 0;

                // Generate fresh HTML content with current data
                const modalHtmlContent = generateModalHtmlContent(entitiesLength);

                IzamModal.addHtmlModal(modalHtmlContent, "", "", false, true, entitiesLength <= 1 ? false : true);
                $('html').addClass('overflow-hidden');
            } catch (error) {
                console.error('Error loading employee documents:', error);
                // Fallback: show modal with basic content
                const fallbackContent = generateModalHtmlContent(0);
                IzamModal.addHtmlModal(fallbackContent, "", "", false, true, false);
                $('html').addClass('overflow-hidden');
            }
        }
        $("body").on("click","#btn-employee-documents", employeeDocumentsModalHtmlContent);
    </script>
@endpush
