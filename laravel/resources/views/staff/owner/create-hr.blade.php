@extends('layouts.owner')
@inject('Plugins', 'App\Facades\Plugins')
@inject('PluginUtil', 'App\Utils\PluginUtil')
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}"/>
    @endpush
    @php
        use App\Models\Staff;

        if(!isset($form_record) || !isset($form_record->id)) {
            $route = route($routesPrefix.'staff.store');
            $method = 'post';
            $isAdd = true;
        } else {
            $route = route($routesPrefix.'staff.update',['staff' => $form_record->id]);
            $method = 'put';
            $isAdd = false;
        }
        $isOwnerUser = !$isAdd && ($form_record->role_id == Staff::OWNER_ROLE_ID);
        if (!$isAdd && $form_record->staff_job)
            $fiscal_check = ($form_record->staff_job->fiscal_type == 'custom-fiscal') ? true:false;

        if($isOwnerUser){
            $related_form_data['user_roles'][-1] = "Owner";
        }
    @endphp
    {!! Form::model($form_record,['class' => 'form-validate','method' => $method,'url' => $route, 'enctype' => 'multipart/form-data']) !!}
    @include('partials.form.page-head', ['title' => '',
     'actions' => [
        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
        ['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attr' => ['key' => 'value']]
     ]])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{__t('General Information')}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','required' => 'required'],'label' => 'First Name','inputClass' => 'form-control','name' => 'name', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Surname','inputClass' => 'form-control','name' => 'last_name', 'type' => 'text'])
                </div>
                <div class="col-md-4">
                    @include('partials.form.input_template', ['label' => 'Middle Name','inputClass' => 'form-control','name' => 'middle_name', 'type' => 'text'])
                </div>
                @include('partials.form.input_template', [
                    'label' => __t('Employee Picture'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'employee_picture_file',
                    'attributes' => [
                        'accepts' => 'image/*'
                    ],
                    'type' => 'avatar',
                    'value' => ($form_record)? $form_record->image:null,
                ])
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => ['rows' => '4','style' => 'height: 114px'],'label' => 'Notes','inputClass' => ' form-control','name' => 'note', 'type' => 'textArea'])
                </div>
            </div>
        </div>
        <hr class="mt-3 mb-3 spacer">
        <div class="card-body">
            <div class="form-row">

                @if ($isAdd)
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => ['data-parsley-errors-messages-disabled'=>"",'required' => '', 'disabled' => true], 'options' => [\Izam\Daftra\Staff\Util\StaffTypeUtil::USER => __t('User'), \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE => __t('Employee')],'label' => 'Employee Mode','inputClass' => 'select form-control','name' => 'type', 'type' => 'select', 'value' => request('type')])
                        @include('partials.form.input_template', ['label' => [], 'name' => 'type', 'type' => 'hidden', 'value' => request('type')])
                    </div>
                @endif

                @if (!$isAdd)
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled', 'data-parsley-errors-messages-disabled'=>"",'required' => ''], 'options' => [\Izam\Daftra\Staff\Util\StaffTypeUtil::USER => __t('User'), \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE => __t('Employee')],'label' => 'Employee Mode','inputClass' => 'select form-control','name' => 'type', 'type' => 'select', 'value' => request('type')])
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => [], 'name' => 'type', 'type' => 'hidden', 'value' => request('type')])
                    </div>
                @endif

                @include('partials.form.input_template', [
                    'label' => __t('Code'),
                    'name' => 'code',
                    'value' => old('code', isset($form_record->code) ? $form_record->code : $related_form_data['code'] ),
                    'inputClass' => 'form-control bg-white',
                    'div' => 'form-group col-md-6',
                    'type' => 'text',
                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled', $isOwnerUser?'':'required' => 'required'],
                ])
                <input type="hidden" name="generated_code" value="{{$related_form_data['code']}}" />

                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],'label' => 'Email Address','inputClass' => 'form-control','name' => 'email_address', 'type' => 'email'])
                </div>

                @if($isAdd)
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => ['required' => 'required'], 'options' => [1 => __t('Active'), 0 => __t('Inactive')],'label' => 'Status','inputClass' => 'select form-control','name' => 'active', 'type' => 'select', 'value' => 1])
                    </div>
                @endif

                <div class="col-md-12"></div>

                <div class="col-md-12">
                    <div class="form-group">
                        <div class="row">
                            <div class="col-md-6 ">
                                @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','onchange' => 'handleChange(this);'],'label' => __t('Allow Access to The System'),'labelClass' => 'custom-control-label','inputClass' => 'custom-control-input','div' => 'custom-control custom-checkbox p-0','name' => 'can_access_system', 'type' => 'checkbox', 'value' => 1, 'checked' => $isAdd ? true : $form_record->can_access_system])
                            </div>
                            <?php if(!$isOwnerUser): ?>
                                <div class="col-md-6 ">
                                    @include('partials.form.input_template', ['label' => __t('Send Credentials to Employee Email'),'labelClass' => 'custom-control-label','inputClass' => 'custom-control-input','div' => 'custom-control custom-checkbox p-0 send_credentials','name' => 'send_credentials', 'type' => 'checkbox', 'value' => 1])
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                @if($related_form_data['languages']->count() > 1)
                    <div class="col-md-6">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled', 'placeholder' => __t('Display Language'),'required' => 'required'], 'options' => $related_form_data['languages']->toArray(),'div' => 'form-group role-toggle','label' => 'Display Language','inputClass' => 'select form-control','name' => 'language_code', 'type' => 'select',"value" => $form_record->language_code ?? getCurrentSite("language_code")])
                    </div>
                @endif
                <div class="col-md-6">
                    @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','placeholder' => sprintf(__t('Select %s'),__t('Role')), 'required' => 'required'], 'options' => $related_form_data['roles']->toArray(),'div' => 'form-group role-toggle', 'label' => 'Role','inputClass' => 'select form-control','name' => 'role_id', 'type' => 'select'])
                </div>
                @if(count($related_form_data['smtpEmailAddress']))
                    <div class="col-md-6" id="smtp_email_address_div">
                        @include('partials.form.input_template', ['attributes' => ['placeholder' => 'System Default'], 'options' => $related_form_data['smtpEmailAddress'], 'div' => 'form-group', 'label' => 'Email Sender Account','inputClass' => 'select form-control','name' => 'smtp_email_address_id', 'type' => 'select'])
                    </div>
                @endif
                @if($Plugins::pluginActive($PluginUtil::BranchesPlugin) )
                    <div class="col-md-6" id="branches_dev">
                        @include('partials.form.input_template', ['attributes' => [$isOwnerUser?'disabled':'' => 'disabled','id' => 'branches','placeholder' => sprintf(__t('Select %s'),__t('Accessible Branches')),'multiple' => true, 'required' => 'required'], 'options' => $allStaffBranches, 'value' => $isOwnerUser? array_keys($allStaffBranches->toArray()):(isset($form_record->branches)? $form_record->branches->pluck('id')->toArray():''),'div' => 'form-group role-toggle', 'label' => 'Accessible Branches','inputClass' => 'form-control select2-multiple','name' => 'branches[]', 'id' => 'branches', 'type' => 'select'])
                    </div>
                @endif

                <div class="col-md-12">
                    <div class="panel">
                        <div class="panel-head">
                            <h3>{{sprintf(__t('%s Information'),__t('Employee'))}}</h3>
                        </div>
                    </div>
                    <div class="py-3 col-md-12">
                        <div class="row">
                            <div class="w-100">
                                <div class="panel">
                                    <div class="drag-list">
                                        <div class="accordion" id="emp-info">
                                            <div class="drag-item active-item">
                                                <div class="drag-item-header" id="personal_info_heading">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-10">
                                                            <button class="drag-trigger btn-block collapsed text-left"
                                                                    type="button" data-toggle="collapse"
                                                                    data-target="#personal_info" aria-expanded="true"
                                                                    aria-controls="personal_info">
                                                                <i class="mdi mdi-chevron-right position-relative"
                                                                   style="top:-1px"></i>{{sprintf(__t('%s Information'),__t('Personal'))}}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="personal_info" class="collapse show"
                                                     aria-labelledby="personal_info_heading">
                                                    <div class="card-body">
                                                        <div class="form-row">
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'required' => 'required',
                                                                        'birth' => true,
                                                                    ],
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? formatDateForInput($form_record->staff_info->birth_date) : '',
                                                                    'div' => 'form-group form-group-icon',
                                                                    'label' => 'Date of Birth',
                                                                    'inputClass' => 'form-control bg-white',
                                                                    'name' => 'birth_date',
                                                                    'type' => 'date',
                                                                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'placeholder' => sprintf(__t('Select %s'),__t('Gender'))
                                                                    ],
                                                                    'options' => ['Male' => __t('Male'), 'Female' => __t('Female')],
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->gender : '',
                                                                    'label' => 'Gender',
                                                                    'inputClass' => 'select form-control',
                                                                    'name' => 'gender',
                                                                    'type' => 'select'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'onchange' => 'getOfficialIdName();',
                                                                        $isOwnerUser?'disabled':'' => 'disabled',
                                                                        'required' => 'required',
                                                                        'empty' => __t('Please Select')
                                                                    ],
                                                                    'options' => $related_form_data['countries']->toArray(),
                                                                    'value' => old('country_code') ?: ((!$isAdd && $form_record->country_code) ? $form_record->country_code : getCurrentSite('country_code')),
                                                                    'label' => 'Country',
                                                                    'inputClass' => 'select form-control',
                                                                    'name' => 'country_code',
                                                                    'type' => 'select'
                                                                ])
                                                            </div>


                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'onchange' => 'handleChangecitizenshipStatus(this);',
                                                                        'empty' => __t('Please Select'),
                                                                        "disableAlphabeticSorting" =>true
                                                                    ],
                                                                        'options' => \Izam\Daftra\Staff\Util\StaffCitizenshipStatus::getStatusList(),
                                                                        'label' => 'Citizenship Status',
                                                                        'inputClass' => 'select form-control',
                                                                        'name' => 'citizenship_status',
                                                                        'type' => 'select',
                                                                        'value' => old('citizenship_status') ?: ((!$isAdd && $form_record->citizenship_status) ? $form_record->citizenship_status : ''),


                                                                ])

                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'label' => !$isAdd ? App\Facades\Staff::getStaffOfficialIdName($form_record->country_code, $form_record->citizenship_status) : 'Official ID Number',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'official_id',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>

                                                            <div class="col-md-6 toglecitizenshipStatus">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'empty' => __t('Please Select')
                                                                    ],
                                                                    'options' => $related_form_data['countries']->toArray(),
                                                                    'value' => old('country_code') ?: ((!$isAdd && $form_record->nationality) ? $form_record->nationality : ''),
                                                                    'label' => 'nationality',
                                                                    'inputClass' => 'select form-control ',
                                                                    'name' => 'nationality',
                                                                    'type' => 'select'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6 toglecitizenshipStatus">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                    ],
                                                                    'value' => (!$isAdd && $form_record->residence_expiry_date ) ? formatForView($form_record->residence_expiry_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : '',
                                                                    'div' => 'form-group form-group-icon',
                                                                    'label' => 'Expiry Date of Residence',
                                                                    'inputClass' => 'form-control bg-white ',
                                                                    'name' => 'residence_expiry_date',
                                                                    'type' => 'date',
                                                                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                                                                ])
                                                            </div>




                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="drag-item active-item">
                                                <div class="drag-item-header" id="contact_info_heading">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-10">
                                                            <button class="drag-trigger btn-block collapsed text-left"
                                                                    type="button" data-toggle="collapse"
                                                                    data-target="#contact_info" aria-expanded="true"
                                                                    aria-controls="contact_info">
                                                                <i class="mdi mdi-chevron-right position-relative"
                                                                   style="top:-1px"></i>{{sprintf(__t('%s Information'),__t('Contact'))}}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="contact_info" class="collapse show"
                                                     aria-labelledby="contact_info_heading">
                                                    <div class="card-body">
                                                        <div class="form-row">
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],
                                                                    'label' => 'Mobile Number',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'mobile',
                                                                    'type' => $isOwnerUser?'text':'number'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],
                                                                    'label' => 'Phone Number',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'home_phone',
                                                                    'type' => $isOwnerUser?'text':'number'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->personal_email : '',
                                                                    'label' => 'Personal Email',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'personal_email',
                                                                    'type' => 'email'
                                                                ])
                                                            </div>
                                                            <div class="w-100"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="drag-item active-item">
                                                <div class="drag-item-header" id="present_address_heading">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-10">
                                                            <button class="drag-trigger btn-block collapsed text-left"
                                                                    type="button" data-toggle="collapse"
                                                                    data-target="#present_address" aria-expanded="true"
                                                                    aria-controls="present_address">
                                                                <i class="mdi mdi-chevron-right position-relative"
                                                                   style="top:-1px"></i>{{__t('Present Address')}}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="present_address" class="collapse show"
                                                     aria-labelledby="present_address_heading">
                                                    <div class="card-body">
                                                        <div class="form-row">
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],
                                                                    'label' => 'Address Line 1',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'address1',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],
                                                                    'label' => 'Address Line 2',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'address2',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],
                                                                    'label' => 'City',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'city',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],
                                                                    'label' => 'State',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'state',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [$isOwnerUser?'disabled':'' => 'disabled'],
                                                                    'label' => 'Postal Code',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'postal_code',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="w-100"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="drag-item active-item">
                                                <div class="drag-item-header" id="permanent_address_heading">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-12">
                                                            <button class="drag-trigger btn-block collapsed text-left"
                                                                    type="button" data-toggle="collapse"
                                                                    data-target="#permanent_address"
                                                                    aria-expanded="true"
                                                                    aria-controls="permanent_address">
                                                                <i class="mdi mdi-chevron-right position-relative"
                                                                   style="top:-1px"></i>{{__t('Permanent Address')}}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="permanent_address" class="collapse show"
                                                     aria-labelledby="permanent_address_heading">
                                                    <div class="card-body">
                                                        <div class="form-row">
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->permanent_address1 : '',
                                                                     'label' => 'Address Line 1',
                                                                     'inputClass' => 'form-control',
                                                                     'name' => 'permanent_address1',
                                                                     'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->permanent_address2 : '',
                                                                    'label' => 'Address Line 2',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'permanent_address2',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->permanent_city : '',
                                                                    'label' => 'City',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'permanent_city',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->permanent_state : '',
                                                                    'label' => 'State',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'permanent_state',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->permanent_postal_code : '',
                                                                    'label' => 'Postal Code',
                                                                    'inputClass' => 'form-control',
                                                                    'name' => 'permanent_postal_code',
                                                                    'type' => 'text'
                                                                ])
                                                            </div>
                                                            <div class="w-100"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="pt-2 pb-2"></div>
                </div>
                <div class="col-md-12">
                    <div class="panel">
                        <div class="panel-head">
                            <h3>{{sprintf(__t('%s Information'), __t('Work'))}}</h3>
                        </div>
                    </div>
                    <div class="py-3 col-md-12">
                        <div class="row">
                            <div class="w-100">
                                <div class="panel">
                                    <div class="drag-list">
                                        <div class="accordion" id="work-info">
                                            <div class="drag-item active-item">
                                                <div class="drag-item-header" id="job_info_heading">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-10">
                                                            <button class="drag-trigger btn-block collapsed text-left"
                                                                    type="button" data-toggle="collapse"
                                                                    data-target="#job_info" aria-expanded="true"
                                                                    aria-controls="job_info">
                                                                <i class="mdi mdi-chevron-right position-relative"
                                                                   style="top:-1px"></i>{{sprintf(__t('%s Information'), __t('Job'))}}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="job_info" class="collapse show"
                                                     aria-labelledby="job_info_heading">
                                                    <div class="card-body">
                                                        <div class="form-row">

                                                            <div class="col-md-6">



                                                                @php
                                                                    $designationOptions = [];
                                                                    $designations = $related_form_data['designations'];
                                                                    foreach ($designations as $designation) {
                                                                        $designationOptions[$designation->id] = $designation->name;
                                                                    }
                                                                @endphp
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'disableAlphabeticSorting' => true,
                                                                        'placeholder' => sprintf(__t('Select %s'),__t('Designation')),
                                                                        'data-parsley-errors-messages-disabled'=>""
                                                                    ],
                                                                    'options' => $designationOptions,
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->designation_id : '',
                                                                    'div' => 'form-group',
                                                                    'label' => 'Designation',
                                                                    'inputClass' => 'select form-control',
                                                                    'name' => 'designation_id',
                                                                    'type' => 'select'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'placeholder' => sprintf(__t('Select %s'),__t('Department')),
                                                                        'data-parsley-errors-messages-disabled'=>""
                                                                    ],
                                                                    'options' => $related_form_data['departments']->toArray(),
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->department_id : '',
                                                                    'div' => 'form-group',
                                                                    'label' => 'Department',
                                                                    'inputClass' => 'select form-control',
                                                                    'name' => 'department_id',
                                                                    'type' => 'select'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'placeholder' => sprintf(__t('Select %s'),__t('Employment Type')),
                                                                        'data-parsley-errors-messages-disabled'=>""
                                                                    ],
                                                                    'options' => $related_form_data['employment_types']->toArray(),
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->employment_type_id : '',
                                                                    'div' => 'form-group',
                                                                    'label' => 'Employment Type',
                                                                    'inputClass' => 'select form-control',
                                                                    'name' => 'employment_type_id',
                                                                    'type' => 'select'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'placeholder' => sprintf(__t('Select %s'),__t('Employment Level')),
                                                                        'data-parsley-errors-messages-disabled'=>""
                                                                    ],
                                                                    'options' => $related_form_data['employment_levels']->toArray(),
                                                                    'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->employment_level_id : '',
                                                                    'div' => 'form-group',
                                                                    'label' => 'Employment Level',
                                                                    'inputClass' => 'select form-control',
                                                                    'name' => 'employment_level_id',
                                                                    'type' => 'select'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                @include('partials.form.input_template', [
                                                                    'attributes' => [
                                                                        'required' => 'required',
                                                                    ],
                                                                    'div' => 'form-group form-group-icon',
                                                                    'label' => 'Join Date',
                                                                    'value' => (!$isAdd && $form_record->staff_job) ? formatDateForInput($form_record->staff_job->join_date) : '',
                                                                    'inputClass' => 'form-control bg-white',
                                                                    'name' => 'join_date',
                                                                    'type' => 'date',
                                                                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                                                                ])
                                                            </div>
                                                            @if($Plugins::pluginActive($PluginUtil::BranchesPlugin))
                                                                <div class="col-md-6">
                                                                    @include('partials.form.input_template', [
                                                                        'attributes' => [
                                                                            $isOwnerUser?'disabled':'' => 'disabled',
                                                                            'required' => 'required',
                                                                            'placeholder' => sprintf(__t('Select %s'),__t('Branch'))
                                                                        ],
                                                                        'options' => $allStaffBranches,
                                                                        'value' => (!$isAdd) ? $form_record->branch_id : '',
                                                                        'div' => 'form-group',
                                                                        'label' => 'Branch',
                                                                        'inputClass' => 'select form-control',
                                                                        'name' => 'branch_id',
                                                                        'type' => 'select'
                                                                    ])
                                                                </div>
                                                            @endif
                                                            @if($Plugins::pluginActive($PluginUtil::BookingPlugin) || $Plugins::pluginActive($PluginUtil::NEW_BOOKING_PLUGIN))
                                                                <div class="col-md-6">
                                                                    @include('partials.form.input_template', [
                                                                        'attributes' => ['placeholder' => sprintf(__t('Select %s'),__t('Shift')),'multiple' => true],
                                                                        'options' => $related_form_data['shifts']->toArray(),
                                                                        'label' => 'Booking Shift',
                                                                        'inputClass' => 'select form-control',
                                                                        'name' => 'shift[]',
                                                                        'type' => 'select',
                                                                        'value' => ($isAdd && !empty($related_form_data['default_booking_shift_id']))
                                                                            ? [(int) $related_form_data['default_booking_shift_id']]
                                                                            : null
                                                                    ])
                                                                </div>
                                                            @endif
                                                            @if(Plugins::pluginActive(PluginUtil::HRM_PLUGIN))
                                                                <div class="col-md-6">
                                                                    @include('partials.form.input_template', [
                                                                        'options' => $related_form_data['staffList'] ?? [],
                                                                        'label' => __t('Direct Manager'),
                                                                        'inputClass' => 'select form-control',
                                                                        'div' => 'form-group',
                                                                        'name' => 'direct_manager_id',
                                                                        'type' => 'selectStaff',
                                                                        'attributes' => ["data-staff-url"=>route('owner.staff.allUsers.search',['selected' => $form_record->id ?? null]),
                                                                            'placeholder' => sprintf(__t("Select %s"), __t('Employee'))],
                                                                    ])
                                                                </div>
                                                            @endif

                                                            @if(Plugins::pluginActive(PluginUtil::AccountingPlugin) && Plugins::pluginActive(PluginUtil::HRM_PAYROLL_PLUGIN) && Settings::getValue(PluginUtil::HRM_PAYROLL_PLUGIN, 'staff_account_routing') == App\Helpers\Settings::STAFF_PAYROLL_ACCOUNT_SPECIFY)
                                                                <div class="col-md-12">
                                                                    <div class="form-group mb-0">
                                                                        <label class="control-label" for="default_account_id">{{__t('Payroll Default Account')}}</label>
                                                                    </div>
                                                                </div>
                                                                @include('partials.accounting.journal_account_select', [
                                                                    'label' => '',
                                                                    'name' => 'default_account_id',
                                                                    'id' => 'default_account_id',
                                                                    'options' => ($form_record && $form_record->default_account_id)? $defaultAccountOptions:[],
                                                                    'inputClass' => 'custom-select form-control',
                                                                    'div' => 'form-group col-md-6',
                                                                    'value' => (old('default_account_id', ($form_record && $form_record->default_account_id ? $form_record->default_account_id : null))),
                                                                    'attributes' => ['disableAlphabeticSorting' => 'true', 'placeholder' => sprintf(__t('Select %s'), __t('Default Account'))],
                                                                ])
                                                            @endif
                                                            @if($Plugins::pluginActive($PluginUtil::HRM_PAYROLL_PLUGIN))
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label for="fiscal-period-label">{{ __t('Fiscal Year Start Day') }}</label>
                                                                        <span class="tip-circle tip" title="{{__t('This will be used to calculate the annual employee regular leaves credit')}}"><i class="fas fa-question-circle"></i></span>
                                                                        <div class="panel-head panel-head-input mb-3">
                                                                            <div class="custom-control custom-radio">
                                                                                <input type="radio"
                                                                                       id="fiscal-period-amount-1"
                                                                                       name="fiscal-period-value"
                                                                                       class="custom-control-input"
                                                                                       value="default-fiscal" {{ (!$isAdd && $form_record->staff_job) ? ($fiscal_check) ? '':'checked' :'checked' }}>
                                                                                <label class="custom-control-label"
                                                                                       for="fiscal-period-amount-1">{{ __t('Use Default Fiscal Date') }}</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="panel-head panel-head-input mb-3">
                                                                            <div class="custom-control custom-radio">
                                                                                <input type="radio"
                                                                                       id="fiscal-period-amount-2"
                                                                                       name="fiscal-period-value"
                                                                                       class="custom-control-input"
                                                                                       value="custom-fiscal" {{ (!$isAdd && $form_record->staff_job) ? ($fiscal_check) ? 'checked':'' :'' }}>
                                                                                <label class="custom-control-label"
                                                                                       for="fiscal-period-amount-2">{{ __t('Custom Fiscal Date') }}</label>
                                                                            </div>
                                                                        </div>
                                                                        <div
                                                                            class="check-block {{ (!$isAdd && $form_record->staff_job) ? ($fiscal_check) ? '':'disabled' :'disabled' }}"
                                                                            id="show-custom-fiscal">
                                                                            <div class="form-group">
                                                                                <div class="position-relative">
                                                                                    <div class="row">
                                                                                        @php $fiscal_date_month = (!$isAdd && $form_record->staff_job && $fiscal_check) ? $form_record->staff_job->fiscal_month : 1; @endphp
                                                                                        {{-- Fiscal Month --}}
                                                                                        @include('partials.form.input_template', [
                                                                                            'attributes' => [
                                                                                                'disableAlphabeticSorting' => true,
                                                                                                'required' => (!$isAdd && $form_record->staff_job) ? ($fiscal_check) ? 'required' : false:false,
                                                                                                'placeholder' => __t('Select Month')
                                                                                            ],
                                                                                            'options' => $related_form_data['months'],
                                                                                            'label' => __t('Month'),
                                                                                            'value' => (!$isAdd && $form_record->staff_job) ? $form_record->staff_job->fiscal_month : '',
                                                                                            'div' => 'col-12 col-md-6 pr-md-1 form-group mb-md-0',
                                                                                            'inputClass' => 'form-control',
                                                                                            'name' => 'fiscal_month',
                                                                                            'id' => 'fiscal_month',
                                                                                            'type' => 'select'
                                                                                        ])
                                                                                        {{-- Fiscal Day --}}
                                                                                        @include('partials.form.input_template', [
                                                                                            'attributes' => [
                                                                                                'disableAlphabeticSorting' => true,
                                                                                                'required' => (!$isAdd && $form_record->staff_job) ? ($fiscal_check) ?'required' : false: false,
                                                                                                'placeholder' => __t('Select Day')
                                                                                            ],
                                                                                            'options' => array_combine(range(1, $related_form_data['monthDays'][$fiscal_date_month]??1), range(1, $related_form_data['monthDays'][$fiscal_date_month]??1)),
                                                                                            'label' => __t('Day'),
                                                                                            'value' => (!$isAdd && $form_record->staff_job) ? $form_record->staff_job->fiscal_day : '',
                                                                                            'div' => 'col-12 col-md-6 pl-md-1 form-group mb-0',
                                                                                            'inputClass' => 'form-control',
                                                                                            'name' => 'fiscal_day',
                                                                                            'id' => 'fiscal_day',
                                                                                            'type' => 'select'
                                                                                        ])
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @if($Plugins::pluginActive($PluginUtil::HRM_ATTENDANCE_PLUGIN))
                                                <div class="drag-item active-item">
                                                    <div class="drag-item-header" id="attendance_info_heading">
                                                        <div class="row align-items-center">
                                                            <div class="col-md-10">
                                                                <button
                                                                    class="drag-trigger btn-block collapsed text-left"
                                                                    type="button" data-toggle="collapse"
                                                                    data-target="#attendance_info" aria-expanded="true"
                                                                    aria-controls="attendance_info">
                                                                    <i class="mdi mdi-chevron-right position-relative"
                                                                       style="top:-1px"></i>{{__t('Attendance Information')}}
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="attendance_info" class="collapse show"
                                                         aria-labelledby="attendance_info_heading">
                                                        <div class="card-body">
                                                            <div class="form-row">
                                                                <div class="col-md-6">
                                                                    @include('partials.form.input_template', [
                                                                        'options' => $related_form_data['shifts']->toArray(),
                                                                        'label' => __t('Attendance Shift'),
                                                                        'inputClass' => 'select form-control',
                                                                        'name' => 'attendance_shift_id',
                                                                        'id' => 'attendance_shift_id',
                                                                        'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->attendance_shift_id : '',
                                                                        'type' => 'select',
                                                                        'attributes' => [
                                                                            'original-name' => 'attendance_shift_id',
                                                                            'placeholder' => sprintf(__t('Select %s'), __t('Attendance Shift')),
                                                                            'id'=>'attendance_shift_select'
                                                                        ]
                                                                    ])

                                                                    @if (\App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift'))
                                                                        <div class="form-group">
                                                                            <div class="w-100 panel-head panel-head-input">
                                                                                @include('partials.form.input_template', [
                                                                                    'label' => __t('Enable the Secondary Attendance Shift'),
                                                                                    'labelClass' => 'custom-control-label',
                                                                                    'inputClass' => 'custom-control-input secondary_shift',
                                                                                    'div' => 'custom-control custom-checkbox p-0 w-100',
                                                                                    'name' => 'has_secondary_shift',
                                                                                    'id' => 'secondary_shift_checkbox',
                                                                                    'value' => 'secondary_shift_checked',
                                                                                    'type' => 'checkbox',
                                                                                    'checked' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->has_secondary_shift : false,
                                                                                 ])
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                                @if (\App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift'))
                                                                    <div class="col-md-6" data-opacity="true" data-if="[{'.secondary_shift':{'type':'equal', 'value':'secondary_shift_checked'}}]">
                                                                        @include('partials.form.input_template', [
                                                                            'options' => $related_form_data['shifts']->toArray(),
                                                                            'label' => __t('Secondary Attendance Shift'),
                                                                            'inputClass' => 'select form-control',
                                                                            'name' => 'secondary_shift_id',
                                                                            'id' => 'secondary_shift_id',
                                                                            'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->secondary_shift_id : '',
                                                                            'type' => 'select',
                                                                            'attributes' => [
                                                                                'original-name' => 'secondary_shift_id',
                                                                                'placeholder' => sprintf(__t('Select %s'), __t('Secondary Attendance Shift'))
                                                                            ]
                                                                        ])
                                                                    </div>
                                                                @endif
                                                                <div class="col-md-6">
                                                                    @include('partials.form.input_template', [
                                                                        'attributes' => [
                                                                            'placeholder' => sprintf(__t('Select %s'),__t('Leave Policy')),
                                                                             'id'=>'leave_policies_select'
                                                                        ],
                                                                        'options' => $related_form_data['leave_policies']->toArray(),
                                                                        'label' => 'Leave Policy',
                                                                        'inputClass' => 'select form-control',
                                                                        'name' => 'leave_policy_id',
                                                                        'value' => (!$isAdd && $form_record->staff_info) ? $form_record->staff_info->leave_policy_id : '',
                                                                        'type' => 'select'
                                                                    ])
                                                                </div>
                                                                <div class="col-md-6" id="holiday_lists_col">
                                                                    @include('partials.form.input_template', [
                                                                        'attributes' => [
                                                                            'placeholder' => sprintf(__t('Select %s'),__t('Holiday List')),
                                                                            'multiple' => true,
                                                                            'id'=>'holiday_lists_select'
                                                                        ],
                                                                        'options' => $related_form_data['holiday_lists']->toArray(),
                                                                        'label' => 'Holiday Lists',
                                                                        'inputClass' => 'select form-control',
                                                                        'name' => 'holiday_lists[]',
                                                                        'type' => 'select'
                                                                    ])
                                                                </div>
                                                                <div class="col-md-6">
                                                                @php
                                                                    $attendanceOptions = $related_form_data['attendance_restrictions']->toArray();
                                                                    foreach($attendanceOptions as $key => &$value){
                                                                        $value = $value."#".$key;
                                                                    }
                                                                @endphp
                                                                @include('partials.form.input_template', [
                                                                        'options' => $attendanceOptions,
                                                                        'label' => __t('Attendance Restrictions'),
                                                                        'inputClass' => 'select form-control',
                                                                        'name' => 'attendance_restriction_id',
                                                                        'id' => 'attendance_restriction_id',
                                                                        'value' => (!$isAdd && $form_record->attendance_restriction_id) ? $form_record->attendance_restriction_id : '',
                                                                        'type' => 'select',
                                                                        'attributes' => [
                                                                            'original-name' => 'attendance_restriction_id',
                                                                            'placeholder' => sprintf(__t('Select %s'), __t('Attendance Restriction'))
                                                                        ]
                                                                    ])
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('partials/custom_forms/form_fields', ['header' => __t("Staff More Information")])
    {!! Form::close() !!}
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/layout/plugin_if.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/layout/plugin_tooltips.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
    <script>


        let user_roles = @json($related_form_data['user_roles']);
        let employee_roles = @json($related_form_data['employee_roles']);

        const can_access_system_checkbox = "input[name='can_access_system']";
        const user_type_select = "select[name='type']";
        const user_type_input = "input[name='type']";

{{--        @if(request('type') === \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE || (isset($form_record->type) && $form_record->type === \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE))--}}
{{--            disable_allow_access_checkbox(can_access_system_checkbox)--}}
{{--        @endif--}}

        $(user_type_select).on('change', function () {
            if (this.value === '{{ \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE }}') {
                $("#role_id").empty().trigger('change');
                var newOption = new Option('<?= __("Employee") ?>', '', false, false);
                $('#role_id').append(newOption);
                for (const role in employee_roles) {
                    var newOption = new Option(employee_roles[role], role, false, false);
                    $('#role_id').append(newOption);
                }
                $('#role_id').val(<?= $form_record->role_id??null ?>);
                $('#role_id').trigger('change')
            } else {
                $("#role_id").empty().trigger('change');
                var newOption = new Option('<?= sprintf(__("Select %s"), __("Role")) ?>', '', false, false);
                $('#role_id').append(newOption);
                for (const role in user_roles) {
                    var newOption = new Option(user_roles[role], role, false, false);
                    $('#role_id').append(newOption);
                }
                $('#role_id').val(<?= $form_record->role_id??null ?>);
                $('#role_id').trigger('change');
            }

            $(user_type_input).val(this.value);
        });

        function disable_allow_access_checkbox(checkbox_id) {
            $(checkbox_id).prop("checked", false);
            $(checkbox_id).attr("disabled", true);
        }

        //accordion active status
        $('.drag-trigger').click(function () {
            $(this).closest('.drag-item').toggleClass('active-item');
        });

        $(document).ready(function () {
            //radio activate block
            $('input[type="radio"]').click(function () {
                var checkvalue = $(this).val();
                $("div.check-block").addClass('disabled');
                $("#show-" + checkvalue).removeClass('disabled');
            });
        });

        function handleChange(checkbox) {
            $(document).ready(function () {
                toggleRoleRquired();
                if (checkbox.checked == true) {
                    document.getElementById("email_address").setAttribute("required", "required");
                    if(document.getElementById("language_code")){
                        document.getElementById("language_code").setAttribute("required", "required");
                    }
                    if(document.getElementById("role_id") && $("#type").val() != "employee") {
                        document.getElementById("role_id").setAttribute("required", "required");
                    }
                    var branchElement = document.getElementById("branches");
                    if (branchElement && $("#type").val() != "employee")
                        branchElement.setAttribute("required", "required");
                    $('label[for="email_address"] span.required').remove();
                    $('label[for="email_address"]').append('<span class="required">*</span>');
                    $('.role-toggle').show();
                    $('.send_credentials').show();
                } else {
                    document.getElementById("email_address").removeAttribute("required");
                    $("#email_address").parsley()._destroyUI();
                    if(document.getElementById("language_code")) {
                        document.getElementById("language_code").removeAttribute("required");
                        $("#language_code").parsley()._destroyUI();
                    }
                    if(document.getElementById("role_id")) {
                         document.getElementById("role_id").removeAttribute("required");
                         $("#role_id").parsley()._destroyUI();
                    }
                    var branchElement = document.getElementById("branches");
                    if (branchElement) {
                        branchElement.removeAttribute("required");
                        $("#branches").parsley()._destroyUI();
                    }
                    $("#branches_dev").css('display', 'none');
                    $('label[for="email_address"] span.required').remove();
                    $('.role-toggle').hide();
                    $('.send_credentials').hide();
                }
            });
        }
        function handleChangecitizenshipStatus(citizenshipStatus) {
            $(document).ready(function () {
                if (citizenshipStatus.value == 'resident') {
                    $('.toglecitizenshipStatus').show();
                } else {
                    $('.toglecitizenshipStatus').hide();
                }
                getOfficialIdName()
            });
        }

        function  getOfficialIdName(){
            var countryCode = $('#country_code').val();
            var citizenshipStatus = $('#citizenship_status').val();

            if (!countryCode || !citizenshipStatus) {
                $('label[for="official_id"]').text('{{ __t("Official ID Number") }}');
                return;
            }

            var isCitizen = citizenshipStatus == 'citizen';
            var label = '{{ __t("Official ID Number") }}';

            switch (countryCode.toUpperCase()) {
                case 'EG':
                    label = isCitizen ? '{{ __t("National ID Number") }}' : '{{ __t("Residency Number") }}';
                    break;
                case 'SA':
                case 'AE':
                    label = '{{ __t("Haweya ID") }}';
                    break;
                case 'JO':
                    label = isCitizen ? '{{ __t("National Number") }}' : '{{ __t("Residency Number/Security Number") }}';
                    break;
            }

            $('label[for="official_id"]').text(label);
        }

        function toggleRoleRquired()
        {
            if ($("#type").val() == "employee") {
                $("#role_id").removeAttr('required');
                $('label[for="role_id"] span.required').remove();
                $("#role_id").attr("data-placeholder", '<?= __t("Employee")?>');
                $("#role_id").attr("placeholder", '<?= __t("Employee")?>');
                if ($("#role_id").data("select2")) {
                    $("#role_id").data("select2").selection.placeholder.text = '<?= __t("Employee")?>';
                }
                $("#select2-role_id-container .select2-selection__placeholder").first().text('<?= __t("Employee")?>');
                $("#branches").removeAttr('required').trigger('change');
                $("#branches_dev").css('display', 'none');
                $("#smtp_email_address_div").css('display', 'none');
                var branchElement = document.getElementById("branches");
                if (branchElement) {
                    $('form.form-validate').parsley().destroy();
                    $("#branches").prop('required', false);
                    $("#branches").attr('data-parsley-required', 'false');
                    $('form.form-validate').parsley().reset();
                }
            } else {
                if ($(can_access_system_checkbox) && $(can_access_system_checkbox).is(":checked")) {
                    $("#branches_dev").css('display', 'block');
                    $("#branches").attr('required', 'required').trigger('change');
                }
                $("#role_id").attr('required', 'required');
                $('label[for="role_id"] span.required').remove();
                $('label[for="role_id"]').append('<span class="required">*</span>');
                $("#select2-role_id-container .select2-selection__placeholder").first().text('<?= sprintf(__t('Select %s'),__t('Role')) ?>');

            }
        }

        monthDays = @json($related_form_data['monthDays']);
        $(function () {
            $fiscalDateMonth = $('#fiscal_month');
            $fiscalDateDay = $('#fiscal_day');

            function setMonthDays() {
                selectedDay = 1;
                daysOptions = [];
                fisicalMonth = $fiscalDateMonth.val();
                selectedMonthDays = monthDays[fisicalMonth];
                $fiscalDateDay.empty();
                for (i = 1; i <= selectedMonthDays; i++) {
                    var newOption = new Option(i, i, false, false);
                    $fiscalDateDay.append(newOption).trigger('change');
                }
                $fiscalDateDay.val(selectedDay);
            }

            $fiscalDateMonth.change(setMonthDays);
            setMonthDays();
        })


        $('#fiscal-period-amount-1').change(function () {
            if ($(this).is(':checked')) {
                $('#fiscal_month').removeAttr('required').parsley()._destroyUI();
                $('#fiscal_day').removeAttr('required').parsley()._destroyUI();
            }
        });

        $('#fiscal-period-amount-2').change(function () {
            if ($(this).is(':checked')) {
                $('#fiscal_month').attr('required', true);
                $('#fiscal_day').attr('required', true);
            }
        });

        $('#secondary_shift_checkbox').change(function () {
            if ($(this).is(':checked')) {
                // $('#attendance_shift_id').attr('required', 'required');
                $('label[for="attendance_shift_id"]').append('<span class="required">*</span>');
            } else {
                // $('#attendance_shift_id').removeAttr('required');
                $('label[for="attendance_shift_id"] span.required').remove();
                $('#secondary_shift_id').val('').trigger('change');
            }
        });

        $(document).ready(function () {
            @php if(request('type') == "employee" || (!$isAdd)) { @endphp
                $("#type").trigger("change");
                handleChange(document.getElementById('can_access_system'));

            @php } @endphp
            $("#type").change(function (event) {

                toggleRoleRquired();
            });

            handleChangecitizenshipStatus(document.getElementById('citizenship_status'))
            /**
             // this part will be disabled for a while.
            $('#designation_id').change(function () {
                let role_id = $('#designation_id option:selected').attr('role_id');
                let department_id = $('#designation_id option:selected').attr('department_id');
                let employment_type_id = $('#designation_id option:selected').attr('employment_type_id');
                let employment_level_id = $('#designation_id option:selected').attr('employment_level_id');
                $('#role_id').val(role_id).trigger('change');
                $('#department_id').val(department_id).trigger('change');
                $('#employment_type_id').val(employment_type_id).trigger('change');
                $('#employment_level_id').val(employment_level_id).trigger('change');
            })
             */
        });
    </script>
    <script>
        $(document).ready(function() {
        $(document).ready(function() {
            <?php if($isOwnerUser): ?>
                const buttons = document.querySelectorAll('.avatar-uploader-actions button');
                buttons.forEach(button => {
                    button.disabled = true;
                });
            <?php endif; ?>

            var urlParams = new URLSearchParams(window.location.search);
            var focusField = urlParams.get('focus');
            if(focusField){
                setTimeout(function () {
                    if (focusField === 'holidays_list') {
                        $('html, body').animate({ scrollTop: $('#holiday_lists_select').closest('.form-group').offset().top - 250 }, 500);
                        $('#holiday_lists_select').select2('focus');
                        $('#holiday_lists_select')[0].focus()
                    } else if (focusField === 'leave_policies') {
                        $('html, body').animate({ scrollTop: $('#leave_policies_select').closest('.form-group').offset().top - 250 }, 500);
                        $('#leave_policies_select').select2('focus');
                        $('#leave_policies_select')[0].focus()
                    } else if (focusField === 'attendance_shift') {
                        $('html, body').animate({ scrollTop: $('#attendance_shift_select').closest('.form-group').offset().top - 250 }, 500);
                        $('#attendance_shift_select').select2('focus');
                        $('#attendance_shift_select')[0].focus()
                    }
                }, 1000);
            }
        });
    </script>
@endpush
