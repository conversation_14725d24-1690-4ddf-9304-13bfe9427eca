<?php
use Izam\Daftra\Staff\Util\StaffCitizenshipStatus;
use App\Utils\PermissionUtil;
if (\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('contracts')) {
    $url = route('owner.contracts.index', ['staff[]' => $record->id, 'iframe' => 1]);
} else {
    $url = "/v2/owner/entity/contract/list?iframe=1&hide_page_header=1&hide_filters=1&filter[staff_id][in][]=$record->id";
}
if($documentsAlertWarningMessage){

    CakeSession::flashMessage($documentsAlertWarningMessage, 'warningMessage', 'secondaryMessage');
}
?>
@extends('layouts.owner')
@inject('Plugins', 'App\Facades\Plugins')
@inject('PluginUtil', 'App\Utils\PluginUtil')
@inject('ShiftWeekdayUtil', 'App\Utils\ShiftWeekdayUtil')
@inject('Permissions', 'App\Helpers\Permissions')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/forms/plugin_jqueryui.css"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/index/index.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/activity_log/activity_log.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/staff/notes.min.css?v={{CSS_VERSION}}"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/entities/staff/show.min.css?v={{CSS_VERSION}}"/>
    <style>
        html{
            overflow-y: auto !important;
        }
    </style>
@endpush
@php
    $show_contact_info_tab = (
        !empty($record->mobile) ||
        !empty($record->home_phone) ||
        isset($record->staff_info) && !empty($record->staff_info->personal_email)
    )? true:false;


    if(Plugins::pluginActive(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN)){
        $show_attendance_info_tab = (
            (isset($record->holiday_lists) && $record->holiday_lists->count() > 0) ||
            isset($record->staff_info->attendanceShift) ||
            isset($record->staff_info->leavePolicy) || isset($record->attendance_restriction_id)
        )? true:false;
    }

    $show_exit_info_tab = null;
    if($record->staff_job){
        $show_exit_info_tab = (
            !empty($record->staff_job->exit_date) ||
            !empty($record->staff_job->exit_reason)
        )? true:false;
    }
    $isOwnerUser = isset($record['role_id']) && ($record['role_id'] == \App\Models\Staff::OWNER_ROLE_ID);
@endphp
@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-6">
                    <div class="pages-head-title">
                        <h2>{{$record->name}} {{$record->last_name}} #{{$record->code ?? $record->id}} <span style="margin-right: 10px"
                                                                           class="status status-{{$record->active ? 'active' : 'inactive'}}"><i
                                    class="cir"></i>{{$record->active ? __t('Active') : __t('Inactive')}}</span></h2>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="action-buttons">
                        <div class="d-flex justify-content-end">
                            @include('partials.related_forms_add_btn')
                            @include('partials.show.record_nav', ['url' => 'owner.staff.show', 'name' => 'staff'])
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header p-3 bg-white">
            <div class="row">
                <div
                    class="col-xl-8 col-lg-8 col-12 d-flex flex-wrap justify-content-center justify-content-sm-start text-center text-sm-left">
                    <div class="flex-shrink-1">
                        <div class="media text-center">
                            <img
                                src="{{ \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($record->name, $record->id, 120, !empty($record->photo) ? $record->photo : null) }}"
                                class="img-fluid avatar-lg" alt="name">
                        </div>
                    </div>
                    <div class="avatar px-4 pt-3 pt-sm-0">
                        <h4>{{$record->name}} {{$record->middle_name}} {{$record->last_name}} <span class="fs-14 text-black-50">({{ userType($record->type) }})</span></h4>
                        <small class="text-muted">#{{$record->code ?? $record->id}}</small>
                    </div>
                </div>
                <div class="col-12 d-lg-none">
                    <br>
                </div>
                <div class="col-xl-4 col-lg-4 col-12">
                    @if($record->mobile)
                        <a href="tel:{{$record->mobile}}" class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-cellphone"></i>{{$record->mobile}}</a>
                    @endif
                    @if($record->email_address)
                        <a href="mailto:{{$record->email_address}}" class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-email-variant"></i>{{$record->email_address}}</a>
                    @endif
                    @if($record->can_access_system && isOwnerOrSuperAdmin() && !$isOwnerUser)
                        <a href="{{getCakeURL(['controller' => 'staffs', 'action' => 'login_as', $record->id])}}"
                           class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-account-box"></i>{{sprintf(__t('Login as %s'), $record->name)}}</a>
                    @endif

                    @if($show_attendance_mobile_app_tab)
                    <a href="/v2/owner/mobile-apps/attendance/{{$record->id}}"
                           class="btn btn-icon btn-dark-blue btn-block"><i
                                class="mdi mdi-cellphone-settings"></i>{{__t('Setup Attendance Mobile App')}}</a>
                    @endif

                </div>
            </div>
        </div>
    </div>

    <div class="card border-white mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-multi-line btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                <a href="{{route('owner.staff.edit',['staff' => $record->id])}}" class="btn btn-light"><i
                        class="mdi mdi-pencil-outline mr-1"></i>{{__t('Edit')}}</a>
                @if(!$isOwnerUser)
                    @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Employee'), 'href' => route('owner.staff.destroy', ['staff' => $record])])
                @endif
                <a href="{{getCakeURL(['controller' => 'posts', 'action' => 'post', 5, $record->id])}}"
                   class="btn btn-light"><i
                        class="mdi mdi-note-text mr-1"></i>{{sprintf('%s %s / %s', __t('Add'), __t('Note'), __t('Attachment')) }}
                </a>
                @if($isOwnerUser && ((getAuthStaff() && getAuthStaff('role_id') == \App\Models\Staff::OWNER_ROLE_ID)|| (getAuthOwner('staff_id') == 0)))
                    <a href="{{ getCakeURL([
                        'controller' => 'owners',
                        'action' => 'change_password'
                    ]) }}" class="btn btn-light"><i class="mdi mdi-lock-reset mr-1"></i>{{ __t('Change Password') }}</a>
                @elseif($record->can_access_system)
                    <a href="{{ route('owner.staff.changePassword', ['staff' => $record->id]) }}" class="btn btn-light"><i class="mdi mdi-lock-reset mr-1"></i>{{ __t('Change Password') }}</a>
                @endif
                <?php
                  //documents and emails
                  $canCreateDocsEmails = $Permissions->checkPermission([PermissionUtil::MANAGE_HRM_SYSTEM, PermissionUtil::Staff_Add_New_Staffs, PermissionUtil::Staff_Edit_Staffs]);

                  //entity documnets
                  $permissions = App\Utils\EntityDocumentPermissionsUtil::getEntityDocumentPermissions('staff');
                  $canCreate = $permissions['create'];

                  $canBeDeactivated = !$isOwnerUser && $record->active ;
                  $canBeActivated = !$isOwnerUser && !$record->active ;
                  $canAccessSystem = !$isOwnerUser && $record->can_access_system ;
                ?>
                @if($canCreateDocsEmails || $canCreate || $canBeDeactivated|| $canBeActivated|| $canAccessSystem)
                    <button type="button" class="btn btn-light dropdown-toggle d-flex align-items-center" data-toggle="dropdown" aria-expanded="false">
                        <span><?= __t("More Options") ?></span>
                        <i class="mdi mdi-chevron-down ms-2"></i>
                    </button>
                @endif
                <div class="dropdown-menu more-options-dropdown">
                    @if($canCreate)
                        <a class="dropdown-item" href="{{ route('owner.entity.create' ,['entityKey'=>'entity_document' , 'subEntityKey'=>'staff','subEntityId'=> $record->id ]) }}" class="btn btn-light"><i  class="mdi mdi-upload mr-1"></i>{{ __t('Upload New Document') }}</a>
                    @endif
                    @if ($canCreateDocsEmails)
                        <a class="dropdown-item"  role="button" class="btn btn-light" id="btn-employee-documents">
                            <i class="mdi mdi-file-document-multiple mr-1"></i>{{ __t('Documents & Emails') }}
                        </a>
                    @endif
                    @if ($canBeDeactivated)
                         <a class="dropdown-item" href="{{ route('owner.staff.deactivate', ['staff' => $record->id]) }}" class="btn btn-light">
                             <i  class="fas fa-comment-alt-edit mr-1"></i>{{ __t('Mark as Inactive') }}
                         </a>
                    @endif
                    @if ($canBeActivated)
                         <a class="dropdown-item" href="{{ route('owner.staff.activate', ['staff' => $record->id]) }}" class="btn btn-light">
                            <i class="fas fa-comment-alt-edit mr-1"></i>{{ __t('Mark as Active') }}
                         </a>
                    @endif
                    @if ($canAccessSystem)
                        <a class="dropdown-item" href="{{ route('owner.staff.sendLoginDetails', ['staff' => $record->id]) }}" class="btn btn-light"><i class="mdi mdi-page-next-outline mr-1"></i>{{ __t('Send Login Details') }}</a>
                    @endif
                </div>
                @include('partials.templates.printables_dropdown', ['view_templates' => $view_templates, 'id' => $record->id])
            </div>
        </div>
        @php
            $tabsViewHelper = new \Izam\Forms\Tab\Helper\TabsViewHelper();
        @endphp
        <div class="card-body p-1 p-lg-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab"
                           aria-controls="details" aria-selected="true">{{ __t('Details') }}</a>
                    </li>
                    @if($Plugins::pluginActive($PluginUtil::HRM_PAYROLL_PLUGIN) && ($Permissions->checkPermission(\App\Utils\PermissionUtil::VIEW_PAYROLL_CONTRACT,getAuthOwner('staff_id')) ||$Permissions->checkPermission(\App\Utils\PermissionUtil::VIEW_HIS_OWN_CONTRACT,getAuthOwner('staff_id'))) &&  $record->contracts()->count())
                    <li class="nav-item">
                            <a class="nav-link" id="contracts-tab" data-toggle="tab" href="#contracts"
                               role="tab" aria-controls="contracts"
                               aria-selected="false">{{ __t('Contracts') }}</a>
                        </li>
                    @endif
                    @if($canShowAttendanceTab)
                        <li class="nav-item">
                            <a class="nav-link"  id="attendance-tab" data-toggle="tab" href="#attendance" role="tab"
                               aria-controls="attendance" aria-selected="false">{{ __t('Attendance') }}</a>
                        </li>
                    @endif
                    @if($canShowPayrollTab)
                        <li class="nav-item">
                            <a class="nav-link"  id="payroll-tab" data-toggle="tab" href="#payroll" role="tab"
                               aria-controls="payroll" aria-selected="false">{{ __t('Payroll') }}</a>
                        </li>
                    @endif
                    @if($Permissions->checkPermission([PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS, PermissionUtil::VIEW_EMPLOYEE_DOCUMENTS]))
                    <li class="nav-item">
                        <a class="nav-link"  id="documents-tab" data-toggle="tab" href="#documents" role="tab"
                            aria-controls="documents" aria-selected="false">{{ __t('Documents') }}
                            @if($documentsAlertCount > 0)
                                <span class="badge badge-danger rounded-pill ml-2">{{ $documentsAlertCount }}</span>
                            @endif
                        </a>
                    </li>
                    @endif
                    @if($notesCount)
                    <li class="nav-item">
                        <a class="nav-link" id="NotesBlock-tab" data-toggle="tab" href="#notes-attachments"
                           role="tab" aria-controls="notes-attachments" aria-selected="false"
                           data-notes-url="{{getCakeURL([
                            'controller' => 'posts',
                            'action' => 'list_post',
                            \App\Utils\PostTypesUtil::STAFF_TYPE,
                            $record->id
                        ])}}">
                        {{ sprintf('%s / %s', __t('Note(s)'), __t('Attachment(s)')) }} ({{$notesCount}})
                        </a>
                    </li>
                    @endif
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab"
                           aria-controls="activity-log" aria-selected="false">{{ __t('Activity Log') }}</a>
                    </li>
                    @if($show_attendance_mobile_app_tab)
                    <!-- <li class="nav-item"  >
                        <a class="nav-link"  id="setup-attendance-tab" data-toggle="tab" href="#setup-attendance" role="tab"
                           aria-controls="setup-attendance" aria-selected="false">{{ __t('Setup attendance mobile app') }}</a>
                    </li> -->
                    @endif
                    @foreach($tabs as $tab)
                        {!! $tabsViewHelper->renderTabHeader($tab) !!}
                    @endforeach

                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">

                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel"
                         aria-labelledby="details-tab">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('General Information') }}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                @if(isset($record->email_address))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Email') }}:</p>
                                            <p>{{$record->email_address}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(isset($record->role))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Role') }}:</p>
                                            <p>{{$record->role->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if($Plugins::pluginActive($PluginUtil::BranchesPlugin) && ($isOwnerUser || (count($record->branches) > 0)))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Accessible Branches') }}:</p>
                                            <p>{{$isOwnerUser? implode(  ', ', $allStaffBranches->toArray()) :$record->branches->implode('name', ', ')}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(isset($record->language_code))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Display Language') }}:</p>
                                            <p>{{is_rtl() ? $record->language->ar_name : $record->language->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @isset($record->note)
                                    <div class="col-md-12">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Notes') }}:</p>
                                            <p>{{$record->note}}</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Personal Information') }}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                @isset($record->staff_info)
                                    @if(!empty($record->staff_info->birth_date))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Date of Birth') }}:</p>
                                                <p>{{formatForView($record->staff_info->birth_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(!empty($record->staff_info->gender))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Gender') }}:</p>
                                                <p>{{__t($record->staff_info->gender)}}</p>
                                            </div>
                                        </div>
                                    @endif
                                @endisset
                                @if (!is_null($record->country))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Country') }}:</p>
                                            <p>{{is_rtl() ? $record->country->ar_name : $record->country->country}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if (!is_null($record->citizenship_status))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Citizenship Status') }}:</p>
                                            <p>{{StaffCitizenshipStatus::getStatus($record->citizenship_status)}}</p>
                                        </div>
                                    </div>

                                    @if ($record->citizenship_status ==StaffCitizenshipStatus::RESIDENT )

                                        @if($record->staff_nationality)
                                            <div class="col-md-6">
                                                <div class="mb-4">
                                                    <p class="text-muted mb-1">{{ __t('Nationality') }}:</p>
                                                    <p>{{ $record->staff_nationality ?(is_rtl() ? $record->staff_nationality->ar_name : $record->staff_nationality->country):''}}</p>
                                                </div>
                                            </div>
                                        @endif
                                        @if($record->residence_expiry_date)
                                            <div class="col-md-6">
                                                <div class="mb-4">
                                                    <p class="text-muted mb-1">{{ __t('Expiry Date of Residence') }}:</p>
                                                    <p>{{formatForView($record->residence_expiry_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</p>

                                                </div>
                                            </div>
                                        @endif




                                    @endif



                                @endif
                                 @if($record->official_id)
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ App\Facades\Staff::getStaffOfficialIdName($record->country_code, $record->citizenship_status) }}:</p>
                                            <p>{{$record->official_id}}</p>

                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        @if($show_contact_info_tab)
                            <div class="panel">
                                <div class="panel-head">
                                    <h3>{{ __t('Contact Information') }}</h3>
                                </div>
                            </div>
                            <div class="p-3">
                                <div class="row">
                                    @if(!empty($record->mobile))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Mobile Number') }}:</p>
                                                <p>{{$record->mobile}}</p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(!empty($record->home_phone))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Phone Number') }}:</p>
                                                <p>{{$record->home_phone}}</p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(isset($record->staff_info) && !empty($record->staff_info->personal_email))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Personal Email') }}:</p>
                                                <p>{{$record->staff_info->personal_email}}</p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Address') }}</h3>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="p-3">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Present Address') }}:</p>
                                                <p>
                                                    @if(!empty($record->address1))
                                                        {{$record->address1}}<br>
                                                    @endif
                                                    @if(!empty($record->address2))
                                                        {{$record->address2}}<br>
                                                    @endif
                                                    {{(!empty($record->city))? $record->city.', ':''}}{{!empty($record->state)? $record->state.', ':''}}{{is_rtl() ? $record->country->ar_name ?? '' : $record->country->country ?? ''}}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                @if(isset($record->staff_info))
                                    <div class="p-3">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-4">
                                                    <p class="text-muted mb-1">{{ __t('Permanent Address') }}:</p>
                                                    <p>
                                                        @if(!empty($record->staff_info->permanent_address1))
                                                            {{$record->staff_info->permanent_address1}}<br>
                                                        @endif
                                                        @if(!empty($record->staff_info->permanent_address2))
                                                            {{$record->staff_info->permanent_address2}}<br>
                                                        @endif
                                                        {{(!empty($record->staff_info->permanent_city))? $record->staff_info->permanent_city.', ':''}}{{(!empty($record->staff_info->permanent_state))? $record->staff_info->permanent_state.', ':''}}{{is_rtl() ? $record->country->ar_name ?? '' : $record->country->country}}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Job Information') }}</h3>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="row">
                                @if(isset($record->staff_info->designation))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Designation') }}:</p>
                                            <p>{{$record->staff_info->designation->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(isset($record->staff_info->department))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Department') }}:</p>
                                            <p>{{$record->staff_info->department->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(isset($record->staff_info->employmentType))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Employment Type') }}:</p>
                                            <p>{{$record->staff_info->employmentType->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(isset($record->staff_info->employmentLevel))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Employment Level') }}:</p>
                                            <p>{{$record->staff_info->employmentLevel->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(isset($record->staff_job) && !empty($record->staff_job->join_date))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Join Date') }}:</p>
                                            <p>{{formatForView($record->staff_job->join_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if($Plugins::pluginActive($PluginUtil::BranchesPlugin))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Branch') }}:</p>
                                            <p>{{$record->branch?->name}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(Plugins::pluginActive(\App\Utils\PluginUtil::BookingPlugin) && isset($record->shift) && $record->shift->count() > 0)
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Booking Shift') }}:</p>
                                            <p>{{$record->shift->implode('name', ', ')}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if(
                                    isset($record->staff_job) && ($record->staff_job->fiscal_type != 'default-fiscal' && !is_null($record->staff_job->fiscal_type)) &&
                                    !empty($record->staff_job->fiscal_day) && !empty($record->staff_job->fiscal_month)
                                )
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Fiscal Date') }}:</p>
                                            <p>{{$record->staff_job->fiscal_day.' '.getMonthName($record->staff_job->fiscal_month)}}</p>
                                        </div>
                                    </div>
                                @else
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Fiscal Date') }}:</p>
                                            <p>{{$fiscal_date_day.' '.getMonthName($fiscal_date_month)}}</p>
                                        </div>
                                    </div>
                                @endif
                                @if($record->managedDepartment)
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Assigned Manager To') }}:</p>
                                            <b><a href="{{route('owner.departments.show', ['department' => $record->managedDepartment->id])}}" target="_blank">{{$record->managedDepartment->name}}</a></b>
                                        </div>
                                    </div>
                                @endif

                                @if(
                                Plugins::pluginActive(PluginUtil::AccountingPlugin) &&
                                Plugins::pluginActive(PluginUtil::HRM_PAYROLL_PLUGIN) &&
                                 Settings::getValue(PluginUtil::HRM_PAYROLL_PLUGIN, 'staff_account_routing') == App\Helpers\Settings::STAFF_PAYROLL_ACCOUNT_SPECIFY
                                 )

                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Payroll Default Account') }}:</p>
                                                <p>{{$record->defaultAccount ? $record->defaultAccount->name : '--'}}</p>
                                            </div>
                                        </div>
                                    @endif

                                    @if(Plugins::pluginActive(\App\Utils\PluginUtil::HRM_PLUGIN) && isset($record->staff_info->direct_manager))
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <p class="text-muted mb-1">{{ __t('Direct Manager') }}:</p>
                                            @include('partials.staff.show', ['record' => $record->staff_info->direct_manager])
                                         </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        @if(isset($show_attendance_info_tab) && $show_attendance_info_tab)
                            <div class="panel">
                                <div class="panel-head">
                                    <h3>{{ __t('Attendance Information') }}</h3>
                                </div>
                            </div>
                            <div class="p-3">
                                <div class="row">
                                    @if(isset($record->staff_info->attendanceShift))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Attendance Shift') }}:</p>
                                                <p>{{$record->staff_info->attendanceShift->name}}</p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(isset($record->staff_info) && $record->staff_info->has_secondary_shift && isset($record->staff_info->secondaryShift))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Secondary Attendance Shift') }}:</p>
                                                <p>{{$record->staff_info->secondaryShift->name}}</p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(isset($record->holiday_lists) && $record->holiday_lists->count() > 0)
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Holiday Lists') }}:</p>
                                                <p>{{$record->holiday_lists->implode('name', ', ')}}</p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(isset($record->staff_info->leavePolicy))
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Leave Policy') }}:</p>
                                                <p>{{$record->staff_info->leavePolicy->name}}</p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(isset($record->attendance_restriction_id) && $record->attendanceRestriction)
                                        <div class="col-md-6">
                                            <div class="mb-4">
                                                <p class="text-muted mb-1">{{ __t('Attendance Restrictions') }}:</p>
                                                <p>{{$record->attendanceRestriction->name}}#{{$record->attendance_restriction_id}}</p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                        @if(isset($customViewFields) || isset($form) || isset($appForms))
                            <div class="panel">
                                @include('partials.custom_forms.view_fields', [ 'header' => __t("Staff More Information") ])
                            </div>
                        @endif
                    </div>
                    <div class="tab-pane bg-transparent fade" id="attendance" role="tabpanel" aria-labelledby="attendance-tab">
                       <div id="root"></div>
                    </div>

                    <div class="tab-pane bg-transparent fade" id="payroll" role="tabpanel" aria-labelledby="payroll-tab">
                       <!-- <iframe src="/v2/owner/employee-attendance-widgets" id="employee-attendance-widgets"></iframe> -->
                       <div id="root-payroll"></div>
                    </div>
                    <div class="tab-pane bg-transparent fade" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                        <?php
                            $documentListIframeSrc = '/v2/owner/entity/entity_document/list?iframe=1&filter[entity_key]=staff&filter[entity_id]='.$record->id.'&show_filters=1&filter[get_unique]=1';
                            $entityDocumentTypeId = $_GET['entity_document_type_id'] ?? 0;
                            if($entityDocumentTypeId){
                                $documentListIframeSrc .= "&filter[entity_document_type_id]=" . intval($entityDocumentTypeId);
                            }
                        ?>
                        <iframe id="documents-iframe" style="height: 600px" src="<?=$documentListIframeSrc?>"></iframe>
                        <div id="root-documents"></div>
                    </div>
                    @foreach($tabs as $tab)
                        {!! $tabsViewHelper->renderTabDetails($tab) !!}
                    @endforeach
                    @if(\App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::HRM_PAYROLL_PLUGIN))
                        <div class="tab-pane p-3 fade" id="contracts" role="tabpanel" aria-labelledby="contracts-tab">
                            <iframe style="height: 600px" src="{{$url}}"> </iframe>
                        </div>
                    @endif
                    @if($notesCount)
                    <div class="tab-pane p-3 fade" id="notes-attachments" role="tabpanel"
                         aria-labelledby="NotesBlock-tab">
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ sprintf('%s / %s', __t('Note(s)'), __t('Attachment(s)')) }}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="notes-attachments-content">
                        @include('partials.layout.loader')
                        <!-- notes & attachments will be here -->
                        </div>
                    </div>
                    @endif
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">
                        <input type="hidden" value="{{\App\Utils\EntityKeyTypesUtil::STAFF_ENTITY_KEY}}"
                               id="entity_key"/>
                        <input type="hidden" value="{{$record->id}}" id="entity_id"/>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{ __t('Activity Log') }}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                        </div>
                    </div>
                    @if($show_attendance_mobile_app_tab)
                    <div class="tab-pane p-3 fade" id="setup-attendance" role="tabpanel" aria-labelledby="setup-attendance-tab">
                       @include('staff/owner/attendance')
                    </div>
                    @endif
                </div>
            </div>


            <div class="modal fade" id="notesDeletePromptModal" tabindex="-1" role="dialog" aria-labelledby="notesDeletePromptModalLabel"
                 style="display: none;" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-transparent border-0 position-absolute w-100" style="z-index: 9;">
                            <h5 class="modal-title d-none" id="notesDeletePromptModalLabel"></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body text-center">
                            <i class="mdi mdi-trash-can-outline display-4 text-danger"></i>
                            <div id="notes-delete-prompt-body">
                                {{sprintf(__t('Are you sure you want to delete this %s ?'),__t('Note') )}}
                            </div>
                        </div>

                            <input type="hidden" name="_method" id="notes-delete-prompt-method" value="DELETE"/>
                            <div class="modal-footer">
                                <form method = 'post' id = 'deleteNoteForm' action="">
                                <input type ='hidden' name="data[Post][action]" value="delete">
                                <button type="submit" name = 'submit_btn' value='yes'  class="btn btn-danger notes-delete-btn">{{__t('Yes')}}</button>
                                </form>
                                <button type="button" class="btn btn-secondary"
                                        data-dismiss="modal">{{__t('No')}}</button>
                            </div>

                    </div>
                </div>
            </div>
            <div id="notes-alert" class="alert alert-icon alert-bordered alert-dismissible border-success alert-success d-none">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="d-flex">
                    <div class="alert-icon">
                        <i class="far fa-lg fa-check-circle text-success mr-3"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-0"><strong id="notes-msg"></strong></p>
                    </div>
                </div>
            </div>
            @include('partials.modals.create_delete_modal')

        </div>
    </div>
@endsection

@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_jqueryui.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_jqueryui_touch.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script>
        $(document).ready(function () {
            var $documentsTab = $("#documents-tab");
            var $iframe = $("#documents-iframe");

            function fixSelectizeOnly() {
                // Send message to iframe without reloading
                if ($iframe[0] && $iframe[0].contentWindow) {
                    $iframe[0].contentWindow.postMessage({ action: 'resizeSelectize' }, '*');
                }
            }

            $documentsTab.on("shown.bs.tab", fixSelectizeOnly);
        });
        var byt = $("body").width();

        $(function () {

            promptHeaderEl = $('#promptModalLabel');
            promptBodyEl = $('#prompt-body');
            promptMethodEl = $('#prompt-method');
            propmptFormEl = $('#row-action-form');
            $('a[data-prompt="true"]').on('click', function (event) {
                event.preventDefault();
                message = $(this).data('prompt-message');
                requestMethod = $(this).data('prompt-method');
                header = $(this).data('prompt-header');
                action = $(this).attr('href');
                promptHeaderEl.text(header);
                promptBodyEl.text(message);
                promptMethodEl.val(requestMethod);
                propmptFormEl.attr('action', action);
                propmptFormEl.attr('method', 'post');
            });

        });

        $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
    if ($(e.target).attr('id') === 'documents-tab') {

    }
});
    </script>
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    @if($notesCount)
    <script src="{{asset('/js/notes/ajax-notes.js')}}"></script>
    @endif
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script>
        var scriptLoaded = false;
        var scriptLoaded2 = false;
        $(function() {
            $("#attendance-tab").on('click', function(){
                if(!scriptLoaded){
                scriptLoaded = true;
                var scriptTag = document.createElement("script");
                scriptTag.type = "module";
                scriptTag.crossOrigin = true;
                scriptTag.src = "/employee-attendance-widgets/index.js";

                $("head").append(scriptTag);

                var scriptTag2 = document.createElement("script");
                scriptTag2.type = "module";
                scriptTag2.crossOrigin = true;
                scriptTag2.src = "/employee-attendance-widgets/main.js";
                $("head").append(scriptTag2);
                $("head").append('<link rel="stylesheet" crossorigin href="/employee-attendance-widgets/plugin.css">');
                }
            })

            $("#payroll-tab").on('click', function(){
                if(!scriptLoaded2){
                    scriptLoaded2 = true;
                var scriptTag3 = document.createElement("script");
                scriptTag3.type = "module";
                scriptTag3.crossOrigin = true;
                scriptTag3.src = "/empolyees-payroll/index.js";

                $("head").append(scriptTag3);

                var scriptTag4 = document.createElement("script");
                scriptTag4.type = "module";
                scriptTag4.crossOrigin = true;
                scriptTag4.src = "/empolyees-payroll/main2.js";
                $("head").append(scriptTag4);
                $("head").append('<link rel="stylesheet" crossorigin href="/empolyees-payroll/plugin.css">');
                }
            })
        })
    </script>
    <script>
        function getStaffIdFromCurrentUrl() {
            try {
                const url = new URL(window.location.href);
                const parts = url.pathname.split("/");
                const staffIndex = parts.indexOf("staff");
                if (staffIndex !== -1 && parts[staffIndex + 1]) {
                    return parts[staffIndex + 1];
                }
                return null;
            } catch (err) {
                console.error("Failed to parse URL", err);
                return null;
            }
        }

        const staffId = getStaffIdFromCurrentUrl();
        console.log("Staff ID:", staffId);

        const getEmployeeDocuments = async () => {
            const response = await fetch(
                `/v2/api/templates/staff/${staffId}`,
            ).then((res) => res.json());
            return response;
        };

        const generateModalHtmlContent = (entitiesLength) => {
            return `
                        <style>
                            .extended-width {
                                width: ${entitiesLength > 2 ? '1140px' : '900px'};
                                max-width: 90%;
                            }
                            @media only screen and (max-width: 993px) {
                                .extended-width {
                                    max-width: 100%;
                                }
                            }
                        <\/style>
                        <div id="container-react" class="h-100 w-100" style="overflow-y: auto;"></div>
                        <script type="module" src=\"{{ asset('js/app/main-employee-documents.js?time=${Date.now()}') }}\" async data-time=${Date.now()}><\/script>
                    `;
        };

        async function employeeDocumentsModalHtmlContent(){
            try {
                // Fetch fresh data each time the modal is opened
                const data = await getEmployeeDocuments();
                const entitiesWithTemplates = data.entities.filter(
                    (entity) => Array.isArray(entity.templates) && entity.templates.length > 0
                );
                const entitiesLength = entitiesWithTemplates?.length || 0;

                // Generate fresh HTML content with current data
                const modalHtmlContent = generateModalHtmlContent(entitiesLength);

                IzamModal.addHtmlModal(modalHtmlContent, "", "", false, true, entitiesLength <= 1 ? false : true);
                $('html').addClass('overflow-hidden');
            } catch (error) {
                console.error('Error loading employee documents:', error);
                // Fallback: show modal with basic content
                const fallbackContent = generateModalHtmlContent(0);
                IzamModal.addHtmlModal(fallbackContent, "", "", false, true, false);
                $('html').addClass('overflow-hidden');
            }
        }
        $("body").on("click","#btn-employee-documents", employeeDocumentsModalHtmlContent);
    </script>

@endpush
