@php
    use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
    use App\Modules\Template\Utils\TemplateTypeUtil;

        $pageTitle = __t('Employees Settings');
        $generalBreadCrumbs = [['title' => $pageTitle]];
        $links = [
            [
                'title' => 'Employee Custom Fields',
                'url' => $customFieldsUrl,
                'icon' => 'mdi mdi-cogs'
            ],
            [
                'title' => 'Related Forms',
                'url' => route('owner.local_entities.index', ['parent_entity' => EntityKeyTypesUtil::STAFF_ENTITY_KEY]),
                'icon' => 'mdi mdi-pencil-ruler'
            ],
            [
                'title' => 'Printable Templates',
                'url' => route('owner.templates.list_entity_templates',  [ TemplateTypeUtil::PDF, EntityKeyTypesUtil::STAFF_ENTITY_KEY]),
                'icon' => 'mdi mdi-receipt'
            ],
            [
                'title' => 'Email Templates',
                'url' => route('owner.templates.list_entity_templates',  [ TemplateTypeUtil::EMAIL, EntityKeyTypesUtil::STAFF_ENTITY_KEY]),
                'icon' => 'mdi mdi-email'
            ],
        ];
        if ( ifPluginActive(\App\Utils\PluginUtil::HRM_PLUGIN) && check_permission(\App\Utils\PermissionUtil::MANAGE_HRM_SYSTEM) && IS_PC) {
            $links[] = [
                'title' => 'HRM Setup Wizard',
                'url' => route('owner.setup.hrm.wizard'),
                'icon' => 'mdi mdi-cog-outline'
            ];
        }
        if ( ifPluginActive(\App\Utils\PluginUtil::HRM_PLUGIN) && check_permission(\App\Utils\PermissionUtil::MANAGE_HRM_SYSTEM) ) {
        $links[] = [
            'title' => 'Documents Management',
            'url' => route('owner.manage.entity.documents', ['entityKey' => \App\Utils\EntityKeyTypesUtil::STAFF_ENTITY_KEY]),
            'icon' => 'mdi mdi-file-document'
        ];
    }


@endphp
@extends('layouts.owner')
@section('content')
    <!-- start page contents -->

    @include('partials.layout.settings_grid',['links' => $links])

    <!-- end page contents -->
@endsection
