<!--

@php
    $items = [
        (object) [
            'id' => 1,
            'type' => 'Annual Leave',
            'period' => '1 Day',
            'start_date' => '2021-01-01',
            'end_date' => '2021-12-01',
            'staff_id' => 1,
            'status' => 'pending',
            'staffs' => (object) [
                'id' => 1,
                'image' => null,
                'name' => '<PERSON>',
                'last_name' => 'Doe',
                'code' => '15',
            ]
        ],
        (object) [
            'id' => 1,
            'type' => 'Sick Leave',
            'period' => '5 min',
            'start_date' => '2021-01-01',
            'end_date' => '2021-12-01',
            'staff_id' => 1,
            'status' => 'rejected',
            'staffs' => (object) [
                'id' => 1,
                'image' => null,
                'name' => 'Sam',
                'last_name' => 'Smith',
                'code' => '16',
            ]
        ],
        (object) [
            'id' => 1,
            'type' => 'Annual Leave',
            'period' => '5 Days',
            'start_date' => '2021-01-01',
            'end_date' => '2021-12-01',
            'staff_id' => 1,
            'status' => 'approved',
            'staffs' => (object) [
                'id' => 1,
                'image' => null,
                'name' => 'Ben',
                'last_name' => 'Jones',
                'code' => '17',
            ]
        ],
        (object) [
            'id' => 1,
            'type' => 'Leave Without Pay',
            'period' => '1 Day',
            'start_date' => '2021-01-01',
            'end_date' => '2021-12-01',
            'staff_id' => 1,
            'status' => 'approved',
            'staffs' => (object) [
                'id' => 1,
                'image' => null,
                'name' => 'Albert',
                'last_name' => 'Falk',
                'code' => '18',
            ]
        ],
        (object) [
            'id' => 1,
            'type' => 'Other',
            'period' => '1 Hour',
            'start_date' => '2021-01-01',
            'end_date' => '2021-12-01',
            'staff_id' => 1,
            'status' => 'approved',
            'staffs' => (object) [
                'id' => 1,
                'image' => null,
                'name' => 'George',
                'last_name' => 'Goe',
                'code' => '19',
            ]
        ],
    ];
@endphp

<link href="{!! getCdnAssetsUrl() !!}css/widgets/widget_first_contracts_to_expire.min.css?v={{CSS_VERSION}}" rel="stylesheet">

@if(false)
    <div class="first-contracts-to-expire-widget-no-data-wrap">
        <div class="first-contracts-to-expire-widget-no-data-body">
            <p>{{__t('No Leave Applications Added Yet')}}</p>
            @if(false)
                <a target="_blank" href="{{route('owner.contracts.create')}}" class="btn btn-dark-blue">{{__t('Add Leave Application')}}</a>
            @endif
        </div>
    </div>
@else
    <div class="first-contracts-to-expire-widget-container">
        @foreach ($items as $item)
        <div class="first-contracts-to-expire-widget-item" onclick="window.location.href='/v2/owner/entity/leave_application/{{$item->id}}/show'">
            <div class="first-contracts-to-expire-widget-item-row">
                <div class="first-contracts-to-expire-widget-item-col first-contracts-to-expire-widget-item-col-6">
                    <div>
                        <div class="first-contracts-to-expire-widget-item-avatar">
                            @if($item->staffs->image)
                                <img src="{{ resizeImage($item->staffs->image, ["w" => 30, "h" => 30, "c" => 1])}}" class="mr-2" style="border-radius: 2px !important;" />
                                @else
                                <img src="{{ \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($item->staffs->name, $item->staff_id, 30, null) }}" class="mr-2" style="border-radius: 2px !important;" />
                            @endif
                            <div>
                                <h6>

                                    {{ucfirst($item->staffs->name)}} {{ucfirst($item->staffs->last_name)}}
                                    <span class="ml-1 id">#{{$item->staffs->code}}</span>
                                </h6>
                                <span class="id">{{$item->type}} ({{$item->period}})</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="mb-0 text-muted fs-12"><span class="text-dark font-weight-medium">From:</span> {{formatForView($item->start_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</p>
                            <p class="mb-0 text-muted fs-12"><span class="text-dark font-weight-medium">To:</span> {{formatForView($item->end_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</p>
                        </div>
                    </div>
                </div>
                <div class="first-contracts-to-expire-widget-item-col first-contracts-to-expire-widget-item-col-6">
                    <div class="w-100 text-right">
                        <div class="compat">
                            <span class="badge badge-md bg-{{$item->status === 'pending' ? 'primary' : ($item->status === 'rejected' ? 'danger' : 'success')}}">{{ucfirst(__t($item->status))}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
@endif
-->


<iframe onload="this.style.height=(this.contentWindow.document.body.scrollHeight+20)+'px';"
src="/v2/owner/entity/leave_application/list?per_page=5&iframe=1&dashboard_widget=1&show_headers=0" frameborder="0" data-app-iframe="true" width="100%" scrolling="no"></iframe>
