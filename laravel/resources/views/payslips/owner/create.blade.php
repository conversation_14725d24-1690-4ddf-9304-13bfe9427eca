@extends('layouts.owner')
@inject('EntityKeyTypesUtil', 'App\Utils\EntityKeyTypesUtil')
@inject('EntityFieldUtil', 'App\Utils\EntityFieldUtil')
@inject('PaySlipStatusUtil', 'App\Utils\PaySlipStatusUtil')
@inject('SalaryComponentTypeUtil', 'App\Utils\SalaryComponentTypeUtil')

@pushonce('styles:multiple_rows')
<link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/forms/sortable_dynamic_rows.min.css?v={{CSS_VERSION}}"/>
<style>
    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice{
        max-width: none;
    }
    .sortable-dynamic-rows-row.is-auto .form-group-textarea-formula{
        display: none;
    }

    .sortable-dynamic-rows-row:not(.is-auto) .form-group-textarea-auto{
        display: none;
    }
</style>
@endpushonce

@php
    $posting_date_value = null;
    $start_date_value = null;
    $end_date_value = null;
    if (isset($form_record) && $form_record) {
        $posting_date_value = $form_record->posting_date;
        $start_date_value = $form_record->start_date;
        $end_date_value = $form_record->end_date;
    } else {
        if (isset($related_form_data['payrun_data'])) {
            $posting_date_value = $related_form_data['payrun_data']['posting_date'];
            $start_date_value = $related_form_data['payrun_data']['start_date'];
            $end_date_value = $related_form_data['payrun_data']['end_date'];
        }
    }

    $basicComponentAmt = $related_form_data['basicComp']->amount ?? 0;

    if (isset($form_record->id)){
        $method = 'PUT';
        $action = route('owner.payslips.update', ['payslip' => $form_record->id]);
    } else {
        $method = 'POST';
        $action = route('owner.payslips.store');
    }

    $old = old();
    if($old){
        $salaryComponents = old('payslipComponent');
        $basicComponentAmt = old('payslipComponent')['earning']['basic']['amount'];
        unset($salaryComponents['earning']['basic']);
    } else if(empty($basicComponentAmt)){
        $basicComponentAmt = $related_form_data['basicComp']->amount ?? 0;
    }

    $selectedStaff = request()->staff ?? null;
    if ($selectedStaff){
        $staffService = resolve(\App\Services\StaffService::class);
         $staff = $staffService->find($selectedStaff);
        if ($staff) {
            $staffData = \App\Facades\Staff::getStaffOptionFormated($staff);
            if (!empty($staffData['data']['data-img'])){
               $staffData['data']['img'] =$staffData['data']['data-img'];
            }
            $staffOptions[] = $staffData;
        }
    }
    $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
@endphp

@section('content')
    {!! Form::model('', ['class' => 'form-validate error-handler', 'method' => $method, 'url' => $action]) !!}

    @include('partials.form.page-head', [
        'title' => '',
        'actions' => [
            [
                'title' => __t('Cancel'),
                'type' => 'button',
                'name' => 'cancel',
                'class' => 'btn-icon btn-secondary cancel-btn',
                'value' => '',
                'icon' => '<i class="mdi mdi-close"></i>',
                'id' => 'cancel',
                'attr' => ['key' => 'value']
            ],
            [
                'title' => __t('Save'),
                'type' => 'submit',
                'name' => '',
                'class' => 'btn-icon btn-success ml-sm-2',
                'value' => '',
                'icon' => '<i class="mdi mdi-content-save-outline"></i>',
                'id' =>'',
                'attr' => ['key' => 'value']
            ]
        ]
    ])

    <div class="card mb-3">
        <div class="card-header">
            <h6>{{__t('Payslip')}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @if (isset($related_form_data['payrun_data']))
                    <input type="hidden" name="payrun_id" value="{{$related_form_data['payrun_data']['id']}}"/>
                @endif

                @php
                    $staff_select_options = [
                        'div' => 'col-md-6 form-group',
                        'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                        'label' => __t('Employee'),
                        'name' => 'employee',
                        'id' => 'employee',
                        'type' => 'selectStaff',
                        'options' => $staffOptions ?? [],
                        'value' => (isset($form_record) && $form_record) ? $form_record->staff->id : ($selectedStaff ?? null),
                        'attributes' => [
                            'original-name' => 'employee',
                            'data-allow-clear' => false,
                            'placeholder' => $staffInputPlaceholder
                        ],
                    ];

                    if (isset($form_record) && $form_record) {
                        $staff_select_options['attributes']['disabled'] = 'disabled';
                    } else {
                        $staff_select_options['attributes']['required'] = 'required';
                    }
                @endphp
                @include('partials.form.input_template', $staff_select_options)

                @php
                    $posting_date_options = [
                        'div' => 'col-md-6 form-group form-group-icon',
                        'label' => __t('Posting Date'),
                        'inputClass' => 'form-control',
                        'name' => 'posting_date',
                        'value' => isset($related_form_data['clone_data']) ? formatForView($related_form_data['clone_data']['posting_date'], \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView($posting_date_value, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                        'attributes' => [
                            'original-name' => 'posting_date',
                            'placeholder' => sprintf(__t('Select %s'), __t('Posting Date'))
                        ]
                    ];

                    if ($posting_date_value)
                        $posting_date_options['attributes']['disabled'] = 'disabled';
                    else
                        $posting_date_options['attributes']['required'] = 'required';
                @endphp
                @include('partials.form.input_template', $posting_date_options)

                @if(isset($form_record) && $form_record)
                    @include('partials.form.input_template', [
                        'div' => 'col-md-6 form-group',
                        'inputClass' => 'select form-control',
                        'label' => __t('Status'),
                        'type' => 'select',
                        'options' => $PaySlipStatusUtil::getAll(),
                        'value'=> $form_record->status,
                        'name' => 'status',
                        'attributes' => ['disabled' => 'disabled', 'data-allow-clear' => false]
                    ])
                    @if(isset($form_record->designation_name))
                        @include('partials.form.input_template', [
                            'div' => 'col-md-6 form-group',
                            'inputClass' => 'form-control',
                            'label' => __t('Designation'),
                            'value' => $form_record->designation_name,
                            'type' => 'text',
                            'attributes' => ['disabled' => 'disabled']
                        ])
                    @endif
                    @if(isset($form_record->department_name))
                        @include('partials.form.input_template', [
                            'div' => 'col-md-6 form-group',
                            'inputClass' => 'form-control',
                            'label' => __t('Department'),
                            'value' => $form_record->department_name,
                            'type' => 'text',
                            'attributes' => ['disabled' => 'disabled']
                        ])
                    @endif
                    @if(isset($form_record->branch_name))
                        @include('partials.form.input_template', [
                            'div' => 'col-md-6 form-group',
                            'inputClass' => 'form-control',
                            'label' => __t('Branch'),
                            'value' => $form_record->branch_name,
                            'type' => 'text',
                            'attributes' => ['disabled' => 'disabled']
                        ])
                    @endif
                @endif
            </div>
            <div class="form-row">
                @php
                    $start_date_options = [
                        'div' => 'col-md-6 form-group form-group-icon',
                        'label' => __t('Start Date'),
                        'inputClass' => 'form-control',
                        'name' => 'start_date',
                        'value' => isset($related_form_data['clone_data']) ? formatForView($related_form_data['clone_data']['start_date'], \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView($start_date_value, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                        'attributes' => [
                            'original-name' => 'start_date',
                            'placeholder' => sprintf(__t('Select %s'), __t('Start Date'))
                        ]
                    ];

                    $end_date_options = [
                        'div' => 'col-md-6 form-group form-group-icon',
                        'label' => __t('End Date'),
                        'inputClass' => 'form-control',
                        'name' => 'end_date',
                        'value' => isset($related_form_data['clone_data']) ? formatForView($related_form_data['clone_data']['end_date'], \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : formatForView($end_date_value, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                        'type' => 'date',
                        'icon' => '<i class="input-icon far fa-calendar-day"></i>',
                        'attributes' => [
                            'original-name' => 'end_date',
                            'placeholder' => sprintf(__t('Select %s'), __t('End Date'))
                        ]
                    ];

                    if ($start_date_value)
                        $start_date_options['attributes']['disabled'] = 'disabled';
                    else
                        $start_date_options['attributes']['required'] = 'required';

                    if ($end_date_value)
                        $end_date_options['attributes']['disabled'] = 'disabled';
                    else
                        $end_date_options['attributes']['required'] = 'required';
                @endphp
                @include('partials.form.input_template', $start_date_options)
                @include('partials.form.input_template', $end_date_options)

                @if(!$form_record)
                    @include('partials.form.input_template', [
                        'div' => 'col-md-6 form-group mb-0',
                        'inputClass' => 'form-control',
                        'label' => __t('Currency'),
                        'name' => 'currency',
                        'id' => 'currency',
                        'value' => (isset($related_form_data['payrun_data'])) ? $related_form_data['payrun_data']['currency'] : getCurrentSite('currency_code'),
                        'options' => $related_form_data['currencies'],
                        'type' => 'select',
                        'attributes' => [
                            'original-name' => 'currency',
                            'required' => !isset($related_form_data['payrun_data']),
                            'placeholder' => sprintf(__t('Select %s'), __t('Currency')),
                            'disabled'=> isset($related_form_data['payrun_data']),
                        ],
                    ])
                @endif
            </div>
        </div>
        <hr class="mt-1 mb-1 spacer">

        <div class="card-body">
            <div class="form-group">
                <div class="panel">
                    <div class="panel-head mb-2">
                        <h3>{{__t('Earning')}}</h3>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="sortable-dynamic-rows-wrapper">
                        <table id="multiple-table" class="earning-table sortable-dynamic-rows">
                            <thead>
                            <tr class="sortable-dynamic-rows-header">
                                <th class="grey text-hide b-0 p-0" hidden="hidden">0</th>
                                <th class="reorder grey" width="50"></th>
                                <th class="d-none order-col d-none order-col">0</th>
                                <th class="p-normal" width="250">{{__t('Component')}}</th>
                                <th class="p-normal">{{__t('Formula')}}</th>
                                <th class="p-normal" width="150">{{__t('Amount')}}</th>
                                <th class=" grey" width="50"></th>
                            </tr>
                            <tr class="sortable-dynamic-rows-row sortable-dynamic-rows-fixed-row sortable-dynamic-rows-row-enable-inputs">
                                <th class="grey text-hide b-0 p-0" hidden="hidden">0</th>
                                <th class="reorder sortable-dynamic-rows-btn-wrap grey"><i
                                        class="mdi mdi-24px mdi-lock"></i></th>
                                <th class="d-none order-col d-none order-col">
                                    <input required value="-1" name="payslipComponent[earning][basic][order]"
                                           type="hidden" class="order-col" value="-1">
                                </th>
                                <th class="p-normal">
                                    {{$related_form_data['basicComp']->name ?? __t('Basic')}}
                                    <input type="hidden"
                                           name="payslipComponent[earning][basic][salary_component_id]"
                                           value="{{$related_form_data['basicComp']->id ?? ''}}"/>
                                </th>
                                <th style="background-color: #e9ecef; border-bottom: 1px solid #ffffff;"></th>
                                <th>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'value' => old('payslipComponent[earning][basic][amount]') ?? $basicComponentAmt,
                                        'inputClass' => 'form-control basic-amount',
                                        'name' => 'payslipComponent[earning][basic][amount]',
                                        'div' => 'form-group mb-0',
                                        'type' => 'number',
                                        'attributes' => [
                                            'placeholder' => sprintf(__t('Enter %s'), __t('Amount')),
                                            'min' => '0',
                                            'required' => true,
                                            'max' => 99999999999,
                                            'step' => 0.0000000001
                                        ]
                                    ])
                                </th>
                                <th class="sortable-dynamic-rows-btn-wrap grey">
                                    <button type="button" class="btn btn-icon remove-row reCalc float-none" disabled><i
                                            class="mdi mdi-key text-inactive"></i>{{__t('Primary')}}</button>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="itemRow movable sortable-dynamic-rows-row">
                                <td class="sortable-dynamic-rows-btn-wrap sortable-dynamic-rows-reorder-btn grey"><i
                                        class="drag-icon mdi mdi-drag-vertical ui-sortable-handle"></i></td>
                                <td class="d-none order-col"><input id="payslipComponent[earning][0][order]"
                                                                    value="0"
                                                                    name="payslipComponent[earning][0][order]"
                                                                    type="hidden"
                                                                    class="order-input order-col">
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'payslipComponent[earning][0][salary_component_id]',
                                        'inputClass' => 'item_name comp_id form-control select-earning',
                                        'div' => 'form-group mb-0',
                                        'type' => 'native_select',
                                        'options' => $related_form_data['earningOptions'],
                                        'attributes' => [
                                        'original-name' => 'salary_component_id',
                                        'data-dropdown-auto-width' => true,
                                        'data-fixed-button' => true,
                                        'data-fixed-button-icon' => "far fa-plus-circle text-success fs-14 mr-2",
                                        'data-fixed-button-title' => __t('Add New Earning Component'),
                                        'data-fixed-button-callback' => "initModal",
                                        'placeholder' => sprintf(__t('Select %s'), __t('Component')), 'required' =>
                                        'required', 'data-type' => $SalaryComponentTypeUtil::SALARY_COMPONENT_EARNING]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'payslipComponent[earning][0][formula]',
                                        'div' => 'form-group mb-0 disabled form-group-textarea-formula',
                                        'inputClass' => 'item_name form-control formula-input',
                                        'type' => 'formula',
                                        'attributes' => [
                                        'original-name' => 'formula',
                                        'placeholder' => sprintf(__t('Enter %s'), __t('Formula')),
                                        'data-placeholder_url' => route('api.placeholders.list', ['entity_key' => 'payslip',
                                        'mode'=>\App\Utils\FieldTypeModesUtil::EQUATION])
                                    ]
                                    ])

                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'id' => '',
                                        'div' => 'form-group disabled mb-0 form-group-textarea-auto',
                                        'inputClass' => 'form-control item_name textarea-auto',
                                        'type' => 'text',
                                        'attributes' => [
                                            'readonly' => true,
                                        ]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template',[
                                        'label' => false,
                                        'inputClass' => 'item_name form-control amount-input',
                                        'name' => 'payslipComponent[earning][0][amount]',
                                        'div' => 'form-group mb-0',
                                        'type' => 'number',
                                        'attributes' => [
                                            'original-name' => 'amount',
                                            'placeholder' => sprintf(__t('Enter %s'), __t('Amount')),
                                            'min' => '0',
                                            'max' => 99999999999,
                                            'step' => 0.0000000001
                                        ]
                                    ])
                                </td>
                                <td class="sortable-dynamic-rows-btn-wrap grey">
                                    <button type="button" class="btn btn-icon remove-row reCalc float-none"><i
                                            class="far fa-minus-circle"></i>{{__t('Remove')}}</button>
                                </td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class="btn-group add-row dropdown" role="group">
                        <button type="button" class="btn btn-secondary addRow font-weight-bold" data-table-id="multiple-table"><i
                                class="far fa-plus-circle text-success mr-2"></i>{{__t('Add')}}</button>
                    </div>
                </div>
            </div>
        </div>
        <hr class="mt-1 mb-1 spacer">

        <div class="card-body">
            <div class="form-group">
                <div class="panel">
                    <div class="panel-head mb-2">
                        <h3>{{__t('Deduction')}}</h3>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="sortable-dynamic-rows-wrapper">
                        <table id="multiple-table2" class="deduction-table sortable-dynamic-rows">
                            <thead>
                            <tr class="sortable-dynamic-rows-header">
                                <th class="grey text-hide b-0 p-0" hidden="hidden">0</th>
                                <th class="reorder grey" width="50"></th>
                                <th class="d-none order-col d-none order-col">0</th>
                                <th class="p-normal" width="250">{{__t('Component')}}</th>
                                <th class="p-normal">{{__t('Formula')}}</th>
                                <th class="p-normal" width="150">{{__t('Amount')}}</th>
                                <th class=" grey" width="111"></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="itemRow movable sortable-dynamic-rows-row">
                                <td class="sortable-dynamic-rows-btn-wrap sortable-dynamic-rows-reorder-btn grey"><i
                                        class="drag-icon mdi mdi-drag-vertical ui-sortable-handle"></i></td>
                                <td class="d-none order-col"><input id="payslipComponent[deduction][0][order]"
                                                                    value="0"
                                                                    name="payslipComponent[deduction][0][order]"
                                                                    type="hidden"
                                                                    class="order-input order-col"></td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'payslipComponent[deduction][0][salary_component_id]',
                                        'inputClass' => 'item_name comp_id form-control select-deduction',
                                        'div' => 'form-group mb-0',
                                        'type' => 'native_select',
                                        'options' => $related_form_data['deductionOptions'],
                                        'attributes' => [
                                        'original-name' => 'salary_component_id',
                                        'data-dropdown-auto-width' => true,
                                        'data-fixed-button' => true,
                                        'data-fixed-button-icon' => "far fa-plus-circle text-success fs-14 mr-2",
                                        'data-fixed-button-title' => __t('Add New Deduction Component'),
                                        'data-fixed-button-callback' => "initModal",
                                        'placeholder' => sprintf(__t('Select %s'), __t('Component')), 'required' =>
                                        'required', 'data-type' => $SalaryComponentTypeUtil::SALARY_COMPONENT_DEDUCTION]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template', [
                                        'label' => false,
                                        'name' => 'payslipComponent[deduction][0][formula]',
                                        'div' => 'form-group mb-0 disabled form-group-textarea-formula',
                                        'inputClass' => 'item_name form-control formula-input',
                                        'type' => 'formula',
                                        'attributes' => [
                                            'original-name' => 'formula',
                                            'defaultPadding' => false,
                                            'defaultBtnText' => false,
                                            'placeholder' => sprintf(__t('Enter %s'), __t('Formula')),
                                            'data-placeholder_url' => route('api.placeholders.list', ['entity_key' => 'payslip',
                                            'mode'=>\App\Utils\FieldTypeModesUtil::EQUATION])
                                        ]
                                    ])

                                    @include('partials.form.input_template', [
                                        'label' => '',
                                        'id' => '',
                                        'div' => 'form-group disabled mb-0 form-group-textarea-auto',
                                        'inputClass' => 'form-control item_name textarea-auto',
                                        'type' => 'text',
                                        'attributes' => [
                                            'readonly' => true,
                                        ]
                                    ])
                                </td>
                                <td>
                                    @include('partials.form.input_template',[
                                        'label' => false,
                                        'inputClass' => 'item_name form-control amount-input',
                                        'name' => 'payslipComponent[deduction][0][amount]',
                                        'div' => 'form-group mb-0',
                                        'type' => 'number',
                                        'attributes' => [
                                        'original-name' => 'amount',
                                        'placeholder' => sprintf(__t('Enter %s'), __t('Amount')), 'min' => '0', 'step' => 0.0000000001]
                                    ])
                                </td>
                                <td class="sortable-dynamic-rows-btn-wrap grey">
                                    <button type="button" class="btn btn-icon remove-row reCalc float-none"><i
                                            class="far fa-minus-circle"></i>{{__t('Remove')}}</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="btn-group add-row dropdown" role="group">
                        <button type="button" class="btn btn-secondary addDed font-weight-bold" data-table-id="multiple-table2"><i
                                class="far fa-plus-circle text-success mr-2"></i>{{__t('Add')}}</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="loans_dev">
            @if(isset($related_form_data['loans']) && count($related_form_data['loans']))
                <hr class="mt-1 mb-1 spacer">
                @include('partials.payslips.loans_dev', ['loans' => $related_form_data['loans']])
            @endif
        </div>

        <div id="commissions_dev">
            @if(isset($related_form_data['commissionSheetsOptions']) && count($related_form_data['commissionSheetsOptions']))
                <hr class="mt-1 mb-1 spacer">
                @include('partials.payslips.commissions_dev', [
                    'commissionSheetsOptions' => $related_form_data['commissionSheetsOptions'],
                    'selectedSheetsIDs' => $related_form_data['selectedSheetsIDs']
                ])
            @endif
        </div>

        <hr class="mt-1 mb-1 spacer">
        <div class="card-body">
            <div class="row">
                @include('partials.form.input_template', [
                    'label' => __t('Gross Pay'),
                    'div' => 'col-md-6 form-group',
                    'type' => 'text',
                    'name' => 'gross_pay',
                    'value'=> '',
                    'id'=> 'gross_pay',
                    'inputClass'=> 'form-control bg-secondary font-weight-bold',
                    'attributes' => [
                        'readonly' => 'readonly'
                    ]
                ])
                @include('partials.form.input_template', [
                    'label' => __t('Total Deduction'),
                    'div' => 'col-md-6 form-group',
                    'type' => 'text',
                    'name' => 'total_deduction',
                    'value'=> '',
                    'id'=> 'total_deduction',
                    'inputClass'=> 'form-control bg-secondary font-weight-bold',
                    'attributes' => [
                        'readonly' => 'readonly'
                    ]
                ])
            </div>
        </div>
        <hr class="mt-1 mb-1 spacer">
        <div class="card-body">
            <div class="row">
                @include('partials.form.input_template', [
                    'label' => __t('Net Pay'),
                    'div' => 'col-md-6 form-group',
                    'type' => 'text',
                    'name' => 'net_pay',
                    'value'=> '',
                    'id'=> 'net_pay',
                    'after'=> '<p class="text-muted">'.__t('Gross Pay').' - '.__t('Total Deduction').'</p>',
                    'inputClass'=> 'form-control bg-secondary font-weight-bold',
                    'attributes' => [
                        'readonly' => 'readonly'
                    ]
                ])
            </div>
        </div>

        <hr class="mt-1 mb-1 spacer">
        <div class="card-body">
            <div class="row">
                @include('partials.form.input_template', [
                    'label' => __t('Notes'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-12 form-group',
                    'name' => 'notes',
                    'type' => 'textArea',
                    'value' => (isset($form_record) && $form_record) ? $form_record->notes : old('notes'),
                    'attributes' => ['rows' => '5','autoResize' => true, 'style' => 'height:120px']
                ])
            </div>
        </div>
        <div class="card-body">
            <div class="form-group">
            @include('partials.new_s3_file_uploader', [
                'recordAttachments' =>  $form_record->attachments ?? old('attachments') ?? [],
                'name' => 'attachments',
                'entityKey' => 'payslip',
                'fieldKey' => 'payslips.attachment',
                'options' => [
                    'multiple' => true,
                    'sortable' => true,
                    'mimes' => []
                ]
            ])
            </div>
        </div>

        {{csrf_field()}}
        {!! Form::close() !!}

        {!! Form::model('',['class' => 'form-validate','method' => 'POST', 'url' => route($routesPrefix.'salary_components.store'), 'name' => 'on_the_fly_form', 'id' => 'on_the_fly_form']) !!}
        <div class="modal fade" id="add_salary_component_modal" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-xl modal-dialog-scrollable modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                                data-target="#add_salary_component_modal" onclick="closeModal(this);">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        @include('salary_components.owner.form')
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="submitBtn" class="btn btn-success" aria-label="Submit"
                                onclick="submitAjax()">{{__t('Save')}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {!! Form::close() !!}
    </div>

    {{--  <!-- end page contents -->  --}}
@endsection
@push('scripts')
    <script>
        let csrf = '{{ csrf_token() }}';
        let compRoute = '{{ route('api.salary_components.store') }}';
        let evalRoute = '{{ route('payslips.evaluateEquation') }}';
        let evalRouteAdd = '{{ route('payslips.evaluateEquationAdd') }}';
        let loansContentRoute = '{{ route('api.payslips.getLoansContent') }}';
        let CommissionsContentRoute = '{{ route('api.payslips.getCommissionsContent') }}';
        var isEdit = "{{$form_record ? true : false}}";
        const payslip_id = {{(isset($form_record) && $form_record) ? $form_record->id : 0}};
        const entity_key = '{{$EntityKeyTypesUtil::PAYSLIP}}';
        const formErrors = @json($errors->toArray());
        @if($salaryComponents)
        var salaryComponents = @json($salaryComponents);
        @endif
        $(function () {
            setFormError('.error-handler', formErrors);
        })
    </script>

    <script src="{{asset('/js/payroll/payslip/add_comp_on_the_fly.js?v=' . JAVASCRIPT_VERSION)}}"></script>
    <script src="{{asset('/js/payroll/payslip/create.js?v='.JAVASCRIPT_VERSION)}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/dynamic_rows/dynamic_rows.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/dynamic_rows/sortable_dynamic_rows.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_jqueryui.js"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_jqueryui_touch.js"></script>
    <script src="{{asset('/js/payroll/salary-comp/create.js?v='.JAVASCRIPT_VERSION)}}"></script>
    @if (Plugins::pluginActive(PluginUtil::COMMISSION_PLUGIN))
        <script src="{{asset('/js/payroll/payslip/commission.js')}}"></script>
    @endif
    <script>
        var singularSelectClass = 'mt-n1';
        @if(isset($attributes['multiple']))
            singularSelectClass = '';
        @endif
        function setEmpCurrentImg (item , data) {
            if (!item.id) { return item.text; }
            var avatarQueryParams = new URLSearchParams({
                n: (item.text.trim()).indexOf('#') == 0 ? (item.text.trim()).split(' ')[1] : item.text.trim(),
                id: item.id,
                s: 78,
            });
            var img = item.img != '' && item.img != null && !item.img.includes('account-def-md.png') ? item.img : (typeof base64 !== 'undefined' ? '/avatar.php?q=' + base64.encode(avatarQueryParams.toString()) : 'https://cdn.daftra.com/assets/imgs/account-def-md.png');
            if(item.element?.dataset?.img){
                img = item.element.dataset.img;
            }
            if (typeof item.img != 'undefined' && item.img != '' && item.img != null) {
                img = item.img;
            } else if (typeof item.img == 'undefined' && $(item.element).data('img') && item.img != null) {
                if (!$(item.element).data('img').includes('account-def-md.png')) {
                    img = $(item.element).data('img');
                }
            }
            var $item = $('<span><img src="' + img + '" class="avatar-xs mr-2 " ' + singularSelectClass + ' />' + item.text + '</span>');
            return $item;
        }
    </script>
@endpush
