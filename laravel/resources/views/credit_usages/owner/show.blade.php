@extends('layouts.owner')
@push('styles')
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/show/show.min.css"/>
    <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/index/index.min.css"/>
@endpush
@section('content')
    <div class="pages-head">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-sm-9">
                    <div class="pages-head-title">
                        <h2>
                            {{__t('Credit Usage')}} #{{$record->id}}
                            @if($record->invoice_id)
                                <small class="d-block mt-1 text-inactive">
                                  <a target="_blank" class="text-decoration-underline" href="/owner/invoices/view/{{$record->invoice_id }}">  <span class="font-weight-normal fs-14">{{__t('Invoice')}} <span class="">#{{$record->invoice->no}}</span></span>  </a>
                                </small>
                            @endif
                        </h2>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="action-buttons">
                        <div class="btn-group" role="group" aria-label="padination">
                            @include('partials.show.record_nav', ['url' => 'owner.credit_usages.show', 'name' => 'credit_usage'])
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="card border-white mb-3">
        <div class="card-header bg-faded">
            <div class="card-header-responsive"></div>
            <div class="btn-group btn-group-responsive btn-group-sm" role="group" aria-label="item-actions-group">
                <a href="{{route('owner.credit_usages.edit', ['credit_usage' => $record->id])}}" class="btn btn-light">
                    <i class="mdi mdi-pencil-outline mr-1"></i> {{__t('Edit')}}
                </a>
                @include('partials.modals.create_delete_modal', ['btn' => true, 'label' => __t('Credit Usage'), 'href' => route('owner.credit_usages.destroy', ['credit_usage' => $record->id])])
                @include('partials.templates.printables_dropdown', ['view_templates' => $view_templates, 'id' => $record->id])
            </div>
        </div>
        <div class="card-body py-3">
            <div id="item-content-responsive">
                <ul class="nav nav-tabs responsive" id="item-nav-tabs-responsive" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="details-tab" data-toggle="tab" href="#details" role="tab"
                           aria-controls="details" aria-selected="true">{{sprintf(__t("%s Information"),__t('Usage'))}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="activity-log-tab" data-toggle="tab" href="#activity-log" role="tab"
                           aria-controls="activity-log" aria-selected="false">{{__t('Activity Log')}}</a>
                    </li>
                </ul>
                <div class="tab-content responsive" id="item-nav-tabs-content-responsive">
                    <div class="tab-pane p-3 fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="pb-2 h-100">
                                    <div class="card h-100 bg-light p-3 d-block">
                                        <div class="d-flex align-items-center">
                                            @if($record->client->photo)
                                                <img src="{{$record->client->photoPath}}" alt="" class="w-75px h-75px" style="border-radius: 2px !important;">
                                                @else
                                                <img src="{{ \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($record->client->business_name, $record->client_id, 75, null) }}" alt="" class="w-75px h-75px" style="border-radius: 2px !important;">
                                            @endif
                                            <div class="flex-fill lh-1 pl-3">
                                                <span class="fs-16 font-weight-bold d-block">{{$record->client->business_name}}<a href="{{getCakeURL(["controller" => "clients", "action" => "view/" . $record->client_id])}}" class="font-weight-normal fs-12 text-decoration-underline ml-2" target="_blank">#{{$record->client->client_number}}</a></span>
                                                <a href="{{getCakeURL(["controller" => "clients", "action" => "view/" . $record->client_id])}}" class="btn btn-sm mt-2 btn-icon btn-secondary btn-block"><i class="mdi mdi-account-box"></i>{{__t('View Profile')}}</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="pb-2 h-100">
                                    <div class="card h-100 bg-light p-3 d-block">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="text-muted mb-1">{{{__t("Usage Amount")}}}:</p>
                                                <p class="lh-1"><span class="d-block fs-22 font-weight-bold">{{$record->usedAmount}}</span><span class="fs-12">{{$record->creditType->unit}}</span></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="text-muted mb-1">{{{__t("Credit Type")}}}:</p>
                                                <p class="lh-16 mb-0 fs-14 font-weight-bold">{{$record->creditType->name}}<a href="{{route('owner.credit_types.show', ['credit_type' => $record->creditType->id])}}" class="font-weight-normal fs-12 text-decoration-underline ml-2" target="_blank">#{{$record->creditType->id}}</a></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="pb-2 h-100">
                                    <div class="card h-100 bg-light p-3 d-block">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="text-muted mb-1">{{{__t("Usage Date")}}}:</p>
                                                <p class="lh-1"><span class="d-block fs-20 font-weight-bold">{{\App\Facades\Formatter::formatForView($record->usage_date, App\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE)}}</span></p>
                                            </div>
                                            @if(!empty($record->addedBy))
                                                <div class="col-md-6">
                                                    <p class="text-muted mb-1">{{{__t("Added By")}}}:</p>
                                                    <div class="lh-1 mb-2">
                                                        <div class="avatar d-flex align-items-center">
                                                            <img src="{{isset($record->addedBy->image) && $record->addedBy->image ? $record->addedBy->image : getCdnAssetsUrl() . "imgs/account-def-md.png"}}" class="mr-1 avatar-xs rounded" width="30" alt="name">
                                                            <div class="lh-16 mb-0 fs-14 font-weight-bold"><div class="d-flex flex-wrap"><span class="mr-2">{{$record->addedBy->full_name??""}}</span><a href="{{route('owner.staff.show', ['staff' => $record->addedBy->id])}}" class="font-weight-normal fs-12 text-decoration-underline" target="_blank">#{{ $record->addedBy?->code ??($record->addedBy->id??"")}}</a></div></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="py-2 h-100">
                                    <div class="table-responsive">
                                        <table class="list-table table table-md table-hover not-clickable bg-white">
                                            <thead>
                                            <tr>
                                                <th class="w-50"> {{__t('Credit Charge')}} </th>
                                                <th class="w-50"> {{__t('Consumed')}} </th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @foreach($record->charges as $item)
                                                <tr>
                                                    <td><h6 class="table-heading d-inline-block font-weight-bold"><a href="#" class="text-decoration-underline">{{$item->creditType->unit}}</a></h6>
                                                        <a href="{{route('owner.credit_charges.show', ['credit_charge' => $item->id])}}" class="text-decoration-underline fs-12"><span class="ml-2">#{{$item->id}}</span></a></td>
                                                    <td><strong>{{round($item->pivot->amount, 2)}}</strong> {{$item->creditType->unit}}</td>
                                                </tr>
                                            @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="py-2 h-100">
                                    <div class="card h-100 bg-light p-3 d-block">
                                        <p class="text-muted mb-1">{{{__t("Description")}}}:</p>
                                        <p class="pre mb-0">{{$record->description}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane p-3 fade" id="activity-log" role="tabpanel" aria-labelledby="activity-log-tab">
                        <input type="hidden" value="{{\App\Utils\EntityKeyTypesUtil::CREDIT_USAGE_ENTITY_KEY}}"
                               id="entity_key"/>
                        <input type="hidden" value="{{$record->id}}" id="entity_id"/>
                        <div class="panel">
                            <div class="panel-head">
                                <h3>{{__t('Activity Log')}}</h3>
                            </div>
                        </div>
                        <div class="pt-3 pb-3" id="activity-log-content">
                        @include('partials.layout.loader')
                        <!-- activity Log will be here -->
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    @include('partials.modals.create_delete_modal')

    <!-- end page contents -->
@endsection
@push('scripts')
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_actions.js"></script>
    <script src="{{asset('/js/activity-log/activity-log-for-entity.js')}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show_responsive_tabs_hashes.js?v={{JAVASCRIPT_VERSION}}"></script>
    <script src="{!! getCdnAssetsUrl() !!}js/show/show.js"></script>
@endpush
