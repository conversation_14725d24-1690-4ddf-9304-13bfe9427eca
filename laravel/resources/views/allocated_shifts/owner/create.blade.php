@extends('layouts.owner')
@php

    $staffInputPlaceholder = __t(getEmployeesFieldPlaceholder());
    if (isset($form_record->id)){
        $isUpdate = true;
        $formRoute = route('owner.allocated_shifts.update', ['allocated_shift' => $form_record->id]);
    } else {
        $formRoute = route('owner.allocated_shifts.store');
        $isUpdate = false;
    }
@endphp
@section('content')
    @push('styles')
        <link rel="stylesheet" href="{!! getCdnAssetsUrl() !!}css/create/create.min.css?v={{CSS_VERSION}}"/>

    @endpush
    @php
        $excludedStaffOptionsAttributes = old('excludedStaffOptionsAttributes') ?? $related_form_data['excludedStaffOptionsAttributes'] ??  null;
        $excludedStaffOptions = old('excludedStaffOptions') ?? $related_form_data['excludedStaffOptions'] ??  null;
    @endphp
    {!! Form::model($form_record,['class' => 'form-validate','method' => 'post','url' => $formRoute]) !!}
    @if($isUpdate)
        {{ Form::hidden('id', $form_record->id) }}
        {{ Form::hidden('_method', 'PUT') }}
    @endif
    @include('partials.form.page-head', [
     'actions' => [
        ['title' => 'Cancel', 'type' => 'button', 'name' => '', 'class' => 'cancel-btn btn-icon btn-secondary', 'value' => '', 'icon' => '<i class="mdi mdi-close"></i>', 'id' =>'', 'attr' => ['key' => 'value']],
        ['title' => 'Save', 'type' => 'submit', 'name' => '', 'class' => 'btn-icon btn-success ml-sm-2', 'value' => '', 'icon' => '<i class="mdi mdi-content-save-outline"></i>', 'id' =>'', 'attr' => ['key' => 'value']]
     ]])
    @CSRF
    <div class="card mb-3">
        <div class="card-header">
            <h6>{{sprintf(__t('%s Information'), __t('Allocated Shifts'))}}</h6>
        </div>
        <div class="card-body">
            <div class="form-row">
                @include('partials.form.input_template', [
                    'attributes' => ['required' => 'required'],
                    'label' => __t('Name'),
                    'inputClass' => 'form-control',
                    'div' => 'col-md-6 form-group',
                    'name' => 'name',
                    'type' => 'text'
                ])
                <div class="w-100"></div>
                @include('partials.form.input_template', [
                    'attributes' => ['required' => 'required'],
                    'div' => 'col-md-6 form-group form-group-icon',
                    'label' => __t('Start Date'),
                    'inputClass' => 'form-control',
                    'name' => 'start_date',
                    'value' => isset($form_record->start_date) ? formatForView($form_record->start_date, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : null,
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])
                @include('partials.form.input_template', [
                    'attributes' => ['required' => 'required'],
                    'div' => 'col-md-6 form-group form-group-icon',
                    'label' => __t('End Date'),
                    'inputClass' => 'form-control',
                    'name' => 'end_date',
                    'value' => isset($form_record->end_date) ? formatForView($form_record->end_date, \Izam\Daftra\Common\Utils\EntityFieldUtil::ENTITY_FIELD_TYPE_DATE) : null,
                    'type' => 'date',
                    'icon' => '<i class="input-icon far fa-calendar-day"></i>'
                ])
                @include('partials.form.input_template', [
                    'attributes' => ['required' => 'required','placeholder' => sprintf(__t("Select a %s"), __t('Shift'))],
                    'options' => $related_form_data['shifts'],
                    'label' => __t('To-Be Assigned Shift'),
                    'inputClass' => 'select form-control',
                    'div' => 'col-md-6  form-group',
                    'name' => 'shift_id',
                    'type' => 'select'
                ])

                @php
                    $shift_type_attr = [
                        'options' => \App\Utils\MultipleShiftTypeUtil::getTypes(),
                        'label' => __t('Shift Type'),
                        'inputClass' => 'select form-control',
                        'div' => 'col-md-6 form-group',
                        'name' => 'shift_type',
                        'type' => 'select',
                        'attributes' => [
                            'required' => 'required'
                        ]
                    ];

                    $showDiv = false;
                    $multi_shift_enabled = \App\Facades\Settings::getValue(\App\Utils\PluginUtil::HRM_ATTENDANCE_PLUGIN, 'secondary_shift');
                    if($multi_shift_enabled) {
                        $showDiv = true;
                    } elseif ($isUpdate && !$multi_shift_enabled) {
                        $showDiv = true;
                        $shift_type_attr['attributes']['select2-dimmed'] = 'true';
                    }
                @endphp
                @if ($showDiv)
                    @include('partials.form.input_template', $shift_type_attr)
                @endif

                <div class="col-md-12">
                    <hr class="spacer">
                </div>
                <div class="col-md-12 form-group">
                    <label>{{__t('Choose Criteria')}}</label>
                </div>

                <div class="col-md-6 form-group order-6 order-lg-1">
                    <div class="position-relative form-group">
                        <div class="input-group">
                            <div class="input-group-prepend w-100">
                                <div class="input-group-text w-100">
                                    @include('partials.form.input_template', [
                                        'label' => __t('Rule Selection'),
                                        'labelClass' => 'custom-control-label',
                                        'inputClass' => 'custom-control-input',
                                        'div' => 'custom-control custom-checkbox p-0 w-100',
                                        'name' => 'criteria',
                                        'id' => 'rule_selection',
                                        'value' => \App\Utils\AllocatedShiftCriteriaUtil::RULE_SELECTION,
                                        'attributes' => ['onchange' => 'handleCriteria(this);'],
                                        'type' => 'radio',
                                        'checked' => 1,
                                     ])
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="rule-inputs">
                        @php
                            $branchesAttributesArray = [
                                'placeholder' => $related_form_data['canSeeAllBranches'] ? sprintf(__t("All %s"), __t('Branches')) : sprintf(__t("Select %s"), __t('Branch'))
                            ];
                            if(!$related_form_data['canSeeAllBranches']){
                                $branchesAttributesArray['required'] = 'required';
                            }

                        @endphp
                        @if (\App\Facades\Plugins::pluginActive(\App\Utils\PluginUtil::BranchesPlugin))
                            @include('partials.form.input_template', [
                                'attributes' => $branchesAttributesArray,
                                'options' => $related_form_data['branches'],
                                'label' => __t('Branch'),
                                'inputClass' => 'select form-control',
                                'div' => 'form-group',
                                'name' => 'criteria_branch_id',
                                'type' => 'select'
                            ])
                        @endif
                        @include('partials.form.input_template', [
                            'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Departments'))],
                            'options' => $related_form_data['departments'],
                            'label' => __t('Department'),
                            'inputClass' => 'select form-control',
                            'div' => 'form-group',
                            'name' => 'criteria_department_id',
                            'type' => 'select'
                        ])
                        @include('partials.form.input_template', [
                            'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Designations'))],
                            'options' => $related_form_data['designations'],
                            'label' => __t('Designation'),
                            'inputClass' => 'select form-control',
                            'div' => 'form-group',
                            'name' => 'criteria_designation_id',
                            'type' => 'select'
                        ])
                        @include('partials.form.input_template', [
                            'attributes' => ['placeholder' => sprintf(__t("All %s"), __t('Shifts'))],
                            'options' => $related_form_data['shifts'],
                            'label' => __t('Shift'),
                            'inputClass' => 'select form-control',
                            'div' => 'form-group mb-0',
                            'name' => 'criteria_shift_id',
                            'type' => 'select'
                        ])
                        @include('partials.form.input_template',[
                         'label' => __t('Excluded Employees'),
                         'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                         'div' => 'form-group',
                         'name' => 'exclude_criteria[]',
                         'attributes' => ['placeholder' =>  $staffInputPlaceholder , 'multiple' => 'multiple', 'disabled' => 'disabled'],
                         'type' => 'selectStaff',
                         'optionsAttributes' => $excludedStaffOptionsAttributes,
                         'options' => $excludedStaffOptions,
                      ])
                    </div>

                </div>

                <div class="col-md-6 form-group order-1 order-lg-6">
                    <div class="position-relative form-group">
                        <div class="input-group">
                            <div class="input-group-prepend w-100">
                                <div class="input-group-text w-100">
                                    @include('partials.form.input_template', [
                                        'label' => __t('Employees Selection'),
                                        'labelClass' => 'custom-control-label',
                                        'inputClass' => 'custom-control-input',
                                        'div' => 'custom-control custom-checkbox p-0 w-100',
                                        'name' => 'criteria',
                                        'attributes' => ['onchange' => 'handleCriteria(this);'],
                                        'id' => 'employee_selection',
                                        'value' => \App\Utils\AllocatedShiftCriteriaUtil::EMPLOYEE_SELECTION,
                                        'type' => 'radio',
                                        'checked' => 0,
                                     ])
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="emp-inputs" class="disabled">

                        @include('partials.form.input_template',[
                            'label' => __t('Employees'),
                            'inputClass' => 'custom-select no-capitalize form-control staff-dropdown',
                            'div' => 'form-group',
                            'name' => 'employees[]',
                            'attributes' => ['placeholder' =>  $staffInputPlaceholder , 'multiple' => 'multiple', 'disabled' => 'disabled'],
                            'type' => 'selectStaff',
                            'options' => session('employees') ?? $related_form_data['staffOptions'] ?? [],
                        ])
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{csrf_field()}}
    {!! Form::close() !!}
    @push('scripts')
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_parsley.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/plugin_select2.js"></script>
        <script src="{!! getCdnAssetsUrl() !!}js/forms/forms.js"></script>
        <script src="{{asset('js/attendance/attendance-day/create.js')}}"></script>
    @endpush
@endsection
