<?php

namespace App\Exception;

use Izam\Daftra\Common\Exception\AppException;

class CategoryAssignedException extends AppException
{
    public function __construct($name, $link, string $message = "", int $code = 0, ?Throwable $previous = null)
    {
        if(!IS_REST) {
            $validationError = sprintf(__('You cannot delete the category as it\'s already assigned to %s', true), "<a href='".$link."'>$name</a>");
        } else {
            $validationError = sprintf(__('You cannot delete the category as it\'s already assigned to %s', true), "$name");
        }
        if(empty($message)) {
            $message = $validationError;
        }
        parent::__construct($message, $code, $previous);
    }

}