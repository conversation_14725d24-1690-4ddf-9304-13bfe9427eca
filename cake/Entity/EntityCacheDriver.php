<?php


namespace App\Entity;


use Izam\Daftra\Common\EntityStructure\CacheDriverInterface;

class EntityCacheDriver implements CacheDriverInterface
{

    private $siteId;
    public function __construct($siteId)
    {
        $this->siteId = $siteId;
    }

    public function write($key, $value)
    {
        if ($key == "__entity_cache") {
            $debugCachKey = '__debug_entity_cache';
            $debugCache = self::read($debugCachKey) ? self::read($debugCachKey): [];
            $difference = null;
            if (is_array($value) && is_array($debugCache)) {
                $difference = array_values(array_diff(array_keys($value), array_keys($debugCache)));
            }
            if (!empty($difference)) {
                $debugCache[$difference[0]] = "CAKE";
                \Cache::write("__{$this->siteId}__".$debugCachKey, $debugCache);
            }
        }
        \Cache::write("__{$this->siteId}__".$key, $value);
        return $this;
    }

    public function read($key)
    {
        return \Cache::read("__{$this->siteId}__".$key);
    }

    public function delete($key)
    {
        \Cache::delete("__{$this->siteId}__".$key);
        return $this;
    }
}