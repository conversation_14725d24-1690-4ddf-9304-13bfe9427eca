<?php


namespace App\Entity;


use App\Repositories\EntityRepository;
use Izam\Daftra\Common\EntityStructure\AbstractEntityStructureGetter;
use Izam\Daftra\Common\EntityStructure\EntityAndRelationCache;

class EntityStructureGetter extends AbstractEntityStructureGetter
{

    private $entityRepository;

    public function __construct()
    {
        $this->entityRepository = new EntityRepository();
        EntityAndRelationCache::setCacheDriver(new EntityCacheDriver(getCurrentSite('id')));
        EntityAndRelationCache::loadFromCache();
    }

    protected function getEntity($entityKey)
    {
        $entities = $this->entityRepository->getAllEntities();
        return isset($entities[$entityKey]) ? $entities[$entityKey] : null;
    }

    public function getChildrenKeys($entityRecord)
    {
        $children = $this->entityRepository->getChildrenEntities($entityRecord->key);
        return array_keys($children);
    }

    /**
     * @param $entityKey
     */
    protected function getEntityFields($entityKey)
    {
        return $this->entityRepository->getEntityFields($entityKey);
    }

    protected function getAllRelation($entityKey)
    {
        return $this->entityRepository->getEntityRelations($entityKey);
    }
}
