<?php

namespace App\Cache;

use Izam\Daftra\Common\EntityStructure\CacheDriverInterface;

class CacheDriver implements CacheDriverInterface
{
    public function write($key, $value)
    {
        \Cache::write($key, $value);
    }

    public function read($key)
    {
        return \Cache::read($key);
    }

    public function delete($key)
    {
        \Cache::delete($key);
    }

    public function config()
    {
        return \Cache::config();
    }
}