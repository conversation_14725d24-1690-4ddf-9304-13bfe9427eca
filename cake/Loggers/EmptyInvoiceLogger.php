<?php

	namespace App\Loggers;

	use Rollbar\Payload\Level;
	use Rollbar\Rollbar;

	class EmptyInvoiceLogger {
		private static $instance;
		private $controller;
		private $invoice_data;
		private function __construct($controller) {
			$this->controller = $controller;
		}

		public static function getInstance($model) {
			if (self::$instance === null) {
				self::$instance = new self($model);
			}
			return self::$instance;
		}

		public function setInvoiceData($invoice_data = null): void {
			$this->invoice_data = $invoice_data;
		}

		private function isEmptyInvoice(): bool {
			return empty($this->invoice_data['InvoiceItem']) || (empty(reset($this->invoice_data['InvoiceItem'])['product_id']) && (reset($this->invoice_data['InvoiceItem'])['item'] === ''));
		}

		private function reportToRollbar($exception = null): void {
			$data = [
				'invoice_data' => [$this->invoice_data],
				'debug_back_trace' => debug_backtrace(),
				'get' => $_GET,
				'post' => $_POST,
			];
			Rollbar::log(Level::INFO, $exception ?? "Empty Invoice Detected", ['debug_data' => $data]);
		}

		private function showValidationError() : void {
			if(IS_REST) {
				$this->controller->cakeError("error400", ["message"=>__('Invoice is empty', true), "validation_errors"=> ['Invoice' => ['InvoiceItem' => __('Invoice is empty')]]]);
			}
			$this->controller->flashMessage(__('Invoice is empty', true));
			$this->controller->redirect($this->controller->referer());
		}

		public function skipEmptyInvoices() : void {
			try {
				if ($this->isEmptyInvoice()) {
//					$this->reportToRollbar();
					$this->showValidationError();
				}
			} catch (\Throwable $throwable) {
				$this->reportToRollbar("EmptyInvoiceLoggerException : " . $throwable->getMessage());
			}
		}
	}