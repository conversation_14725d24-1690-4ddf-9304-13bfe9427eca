<?php

namespace App\Mapper;



use App\Utils\NotificationTypesUtil;
use Ramsey\Uuid\Uuid;

/**
 * CakeNotificationKeyMapper Class that convert response to an array
 * @package App\Mapper
 * <AUTHOR> <<EMAIL>>
 */
class NotificationMapper
{


    public static $triggerTypeMapper = [
        0 => "App\\Models\\Client",
        1 => "App\\Models\\Owner",
        2 => "App\\Models\\Staff"
    ];
    public static $actionMapper = [
        1 => 'Added',
        2 => 'Updated',
        3 => 'Deleted',
        4 => 'Done',
        5 => 'Dismissed'
    ];



    /**
     * @param $userType
     * @param $userIds
     * @return array
     */
    public static function mapNotificationType($userType, $userIds)
    {
        $mappedNotification = ['notifiable_id'=>[]];
        // mapping notifiableId
        if($userType === 0){
            $mappedNotification['notifiable_type']= "\App\Models\Staff";
        }elseif ($userType ===1){
            $mappedNotification['notifiable_type']="\App\Models\Client";
        }
        if(is_array($userIds)){
            foreach ($userIds as $userId){
                if($userId === 0 && $userType === 0){
                    $mappedNotification['notifiable_type']="\App\Models\Owner";
                    $mappedNotification['notifiable_id'][] = getCurrentSite('id');
                    continue;
                }
                $mappedNotification['notifiable_id'][] = $userIds;
            }
        }elseif(!is_null($userIds)){
            if( $userIds === 0 && $userType === 0){
                $mappedNotification['notifiable_type']="\App\Models\Owner";
            }
            $mappedNotification['notifiable_id'][] = $userIds;
        }
        return $mappedNotification;

    }

    public static function mapNotificationKeys($keys){
        $notificationKeysArr = [];
        if(!is_array($keys) && !is_null($keys)){
            return NotificationTypesUtil::getLaravelClassNamespaceWithCakeKey($keys);
        }
        if(is_array($keys)){
            foreach ($keys as $key){
                if(!is_null($key)){
                    $notificationKeysArr[] = NotificationTypesUtil::getLaravelClassNamespaceWithCakeKey($key);
                }

            }
        }
        return $notificationKeysArr;
    }

    public static function generateNotificationObject($notification2Obj)
    {

        $mappedNotification = new \stdClass();
        if (isset($notification2Obj->id))
            $mappedNotification->id = $notification2Obj->id;
        else
            $mappedNotification->id = Uuid::uuid4()->toString();
        if (!isset($notification2Obj->created))
            $mappedNotification->created_at = date('Y-m-d H:i:s');//Carbon::parse($notification2Obj->created);
        $mappedNotification->type = \App\Utils\NotificationTypesUtil::getLaravelClassNamespaceWithCakeKey($notification2Obj->notification_key);
        $extra_data = null;
        if(isset($notification2Obj->params)){
            $extra_data = json_decode($notification2Obj->params);
        }
        if (!$extra_data) {
            $extra_data = new \stdClass();
        }

        $extra_data->reference_name = \App\Utils\NotificationTypesUtil::getLaravelEntityNameWithCakeKey($notification2Obj->notification_key);
        $extra_data->reference_id = $notification2Obj->ref_id;

        // Handle trigger
        if (isset($notification2Obj->trigger_id) && $notification2Obj->trigger_id) {
            $extra_data->trigger_id = $notification2Obj->trigger_id;
        }

        if (isset($notification2Obj->trigger_type) && $notification2Obj->trigger_type) {
            $extra_data->trigger_type = self::$triggerTypeMapper[$notification2Obj->trigger_type];
            if ($extra_data->trigger_type == static::$triggerTypeMapper[1]) {
                $extra_data->trigger_id = getCurrentSite('id');
            }
        }

        // action

        if (isset($notification2Obj->action) && $notification2Obj->action) {
            $extra_data->action = self::$actionMapper[$notification2Obj->action];
        }


        // Client
        if ($notification2Obj->user_type == 1) {
            $mappedNotification->notifiable_id = $notification2Obj->user_id;
            $mappedNotification->notifiable_type = static::$triggerTypeMapper[0];
        } // Owner
        elseif ($notification2Obj->user_type == 0 && $notification2Obj->user_id == 0) {
            $mappedNotification->notifiable_id = getCurrentSite('id');
            $mappedNotification->notifiable_type = static::$triggerTypeMapper[1];
        } // Staff
        elseif ($notification2Obj->user_type == 0 && $notification2Obj->user_id > 0) {
            $mappedNotification->notifiable_id = $notification2Obj->user_id;
            $mappedNotification->notifiable_type = static::$triggerTypeMapper[2];
        }

        // read at

        if (in_array($notification2Obj->status, [1, 2])) {
            $mappedNotification->read_at = date('Y-m-d H:i:s');//Carbon::parse($notification2Obj->date);
        }

        // dismissed at
        if (in_array($notification2Obj->status, [3, 2])) {
            $extra_data->dismissed_at = date('Y-m-d H:i:s');//Carbon::parse($notification2Obj->date);
        }

        $mappedNotification->data = json_encode($extra_data);


        if (!isset($notification2Obj->updated_at)) {
            $mappedNotification->updated_at  = date('Y-m-d H:i:s');
        }

        return $mappedNotification;

    }
}
