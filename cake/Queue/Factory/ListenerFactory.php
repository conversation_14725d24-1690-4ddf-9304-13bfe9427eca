<?php


namespace App\Queue\Factory;


use Izam\Daftra\Common\Queue\DaftraListener;
use Izam\Daftra\Common\Queue\EventEnvUtil;
use Izam\Daftra\Common\Queue\EventListenerMapper;
use Izam\Daftra\Common\Queue\EventTypeUtil;
use Izam\Daftra\Common\Queue\ListenerStatusUtil;
use Izam\Daftra\Common\Queue\ListenerTypeUtil;

class ListenerFactory
{

    public static function getInstance($listener)
    {
        $meta = EventListenerMapper::getMetaForListener($listener);
        $listenerObject = new DaftraListener( $listener);
        $listenerObject->setTries(0)->setStatus(ListenerStatusUtil::CREATED)
            ->setSiteId(getCurrentSite('id'))->setRuntimePlatform($meta['platform'])
            ->setRunTimeEnv(isset($meta['runtime_env'])?$meta['runtime_env']:null);

        switch ($listener) {
            case ListenerTypeUtil::SAVE_JOURNAL_LISTENER:
                if
                (
                    IS_REST2 ||
                    (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') ||
                    !ifPluginActive(AccountingPlugin) ||
                    !check_permission(VIEW_ALL_JOURNALS)
                ) {
                    $listenerObject->setRunTimeEnv(EventEnvUtil::BACKGROUND);
                } else {
                    $listenerObject->setRunTimeEnv(EventEnvUtil::FOREGROUND);
                }
                break;
        }
        return $listenerObject;
    }

    public static function canRunJournalOnBackground() {
        return (IS_REST2 ||
            (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') ||
            !ifPluginActive(AccountingPlugin) ||
            !check_permission(VIEW_ALL_JOURNALS));
    }

}
