.flatpickr-calendar {
  border-radius: 0 2px;
  border: 1px solid $primary;
  margin: -3px 0 0 0;
  width: auto !important;
  box-shadow: none;
  &::before,
  &::after {
    display: none;
  }
  &.rangeMode {
    margin: -3px 0 0 0;
  }
  .flatpickr-rContainer {
    margin-left: auto;
    margin-right: auto;
  }
  .flatpickr-months {
    .flatpickr-prev-month,
    .flatpickr-next-month {
      width: 55px;
      height: 55px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      @include media-max(993px) {
        width: 36px;
      }
      svg {
        width: 12px;
        height: 12px;
        path {
          stroke: $black;
          stroke-width: 2px;
        }
      }
      &:hover {
        svg {
          path {
            fill: $primary;
            stroke: $primary;
          }
        }
      }
    }
    .flatpickr-month {
      height: 55px;
      .flatpickr-current-month {
        padding-top: 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        gap: 10px;
        span.cur-month {
          background: none;
        }
        .flatpickr-monthDropdown-months {
          font-weight: 500;
          font-size: 18px;
          // -webkit-appearance: none;
          // -moz-appearance: none;
          padding: 7px 0;
          border-radius: 0;
          // text-align: right;
          margin-right: 20px;
          position: relative;
          @include media-max(993px) {
            margin-right: 10px;
          }
          &::after {
            display: block;
            content: "";
            position: absolute;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid rgba(57, 57, 57, 0.6);
            width: 20px;
            height: 20px;
          }
          &:hover {
            background: none;
            color: $primary;
          }
        }
        .numInputWrapper {
          background: none;
          margin: 0 24px;
          &:hover,
          &:focus {
            input.cur-year {
              color: $primary;
            }
          }
          span {
            top: 50%;
            transform: translateY(-50%) rotate(90deg);
            width: 24px;
            height: 24px;
            &::before,
            &::after {
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            }
            &.arrowUp {
              right: -24px;
            }
            &.arrowDown {
              left: -24px;
            }
          }
          // width: 80px;
          input.cur-year {
            font-weight: 500;
            font-size: 18px;
            padding: 7px 6px;
            border-radius: 0;
            -moz-appearance: textfield;
            text-align: center;
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }
            &:hover,
            &:focus {
              color: $primary;
            }
          }
        }
      }
    }
  }
  .flatpickr-innerContainer {
    .flatpickr-rContainer {
      .flatpickr-weekdays {
        height: 50px;
        @include media-max(993px) {
          height: 36px;
        }
        .flatpickr-weekdaycontainer {
          gap: 2px;
          @include media-max(993px) {
            gap: 5px;
          }
          .flatpickr-weekday {
            color: $primary;
            font-size: 14px;
            font-weight: 500;
            width: 55px;
            max-width: 55px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            @include media-max(993px) {
              width: 38px;
              max-width: 38px;
              height: 36px;
              line-height: 36px;
            }
          }
        }
      }
      .flatpickr-days {
        width: 100%;
        min-width: 100%;
        max-width: 399px;
        @include media-max(993px) {
          max-width: 301px;
        }
        .dayContainer {
          width: 100%;
          min-width: 100%;
          max-width: 399px;
          gap: 2px;
          padding-bottom: 30px;
          @include media-max(993px) {
            max-width: 301px;
            gap: 5px;
          }
          .flatpickr-day {
            border-radius: 0;
            color: #444444;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid $secondary;
            width: 55px;
            max-width: 55px;
            height: 50px;
            line-height: 50px;
            @include media-max(993px) {
              width: 38px;
              max-width: 38px;
              height: 36px;
              line-height: 36px;
            }
            &.flatpickr-disabled,
            &.flatpickr-disabled:hover,
            &.prevMonthDay,
            &.nextMonthDay,
            &.notAllowed,
            &.notAllowed.prevMonthDay,
            &.notAllowed.nextMonthDay {
              font-size: 14px;
              font-weight: 400;
              color: $light-5;
              border: 1px solid transparent;
            }
            &.selected,
            &.startRange,
            &.endRange,
            &.selected.inRange,
            &.startRange.inRange,
            &.endRange.inRange,
            &.selected:focus,
            &.startRange:focus,
            &.endRange:focus,
            &.selected:hover,
            &.startRange:hover,
            &.endRange:hover,
            &.selected.prevMonthDay,
            &.startRange.prevMonthDay,
            &.endRange.prevMonthDay,
            &.selected.nextMonthDay,
            &.startRange.nextMonthDay,
            &.endRange.nextMonthDay {
              background: $primary;
              border-color: $primary;
              color: $white;
            }
            &.inRange,
            &:hover {
              background: $light-6;
              border-color: $light-6;
              box-shadow: none;
            }
            &.endRange,
            &.endRange:hover,
            &.endRange:focus {
              background: #37a592;
              border-color: #37a592;
              box-shadow: none;
            }
            &.today {
              color: $primary;
              &.selected,
              &.startRange,
              &.endRange {
                color: $white;
              }
            }
          }
        }
      }
    }
  }
  .flatpickr-time {
    border-color: $secondary;
    // background: $primary;
    background: #1877f2;
    input:hover,
    .flatpickr-am-pm:hover,
    input:focus,
    .flatpickr-am-pm:focus {
      background: #0052bd;
      // background: #E5F0FD;
    }
    .numInputWrapper {
      &:hover {
        background: #0052bd;
        // background: #E5F0FD;
      }
      span {
        &.arrowUp {
          border-color: $white;
          border-bottom: none;
          &::after {
            border-bottom-color: $white;
          }
        }
        &.arrowDown {
          border-color: $white;
          &::after {
            border-top-color: $white;
          }
        }
      }
    }
    input {
      color: $white;
    }
    .flatpickr-time-separator {
      color: $white;
    }
  }
}

.flatpickr-calendar.flatpickr-has-predefined-ranges {
  width: auto !important;
  &.open {
    display: grid;
    z-index: 1501;
  }
  .flatpickr-predefined-ranges {
    grid-column: 1;
    grid-row: 1 / span 3;
    border-right: 1px solid $primary;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.16);
    @include media-max(993px) {
      grid-column: 1;
      grid-row: 4;
    }
    .nav-item {
      .nav-link.btn-link {
        padding: 16px;
        color: $black;
        border-radius: 0;
        justify-content: flex-start;
        white-space: nowrap;
        border: 0;
        font-size: 16px;
        font-weight: 500;
        line-height: 1;
        border-bottom: 1px solid $secondary;
        &.active,
        &:hover:not(.active) {
          background: #e5f0fd;
        }
      }
    }
  }
  .flatpickr-months {
    position: relative;
    grid-column: 2;
    grid-row: 1;
    @include media-max(993px) {
      grid-column: 1;
      grid-row: 1;
    }
    .flatpickr-month + .flatpickr-month {
      position: relative;
      @include media-max(993px) {
        display: none;
      }
      &::before {
        content: "";
        width: 1px;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: $secondary;
      }
    }
  }
  .flatpickr-innerContainer {
    overflow: visible;
    grid-column: 2;
    grid-row: 2;
    padding: 0 20px;
    @include media-max(993px) {
      grid-column: 1;
      grid-row: 2;
      padding: 0;
    }
    .flatpickr-rContainer {
      .flatpickr-weekdays {
        gap: 30px;
        .flatpickr-weekdaycontainer + .flatpickr-weekdaycontainer {
          position: relative;
          @include media-max(993px) {
            display: none;
          }
          &::before {
            content: "";
            width: 1px;
            height: 100%;
            position: absolute;
            left: -15px;
            top: 0;
            background: $secondary;
          }
        }
      }
      .flatpickr-days {
        gap: 30px;
        .dayContainer {
          width: calc(50% - 15px);
          min-width: calc(50% - 15px);
          @include media-max(993px) {
            width: 100%;
            min-width: 100%;
          }
        }
        .dayContainer + .dayContainer {
          box-shadow: none;
          position: relative;
          @include media-max(993px) {
            display: none;
          }
          &::before {
            content: "";
            width: 1px;
            height: 100%;
            position: absolute;
            left: -15px;
            top: 0;
            background: $secondary;
          }
        }
      }
    }
  }
  .flatpickr-time {
    grid-column: 2;
    grid-row: 3;
    @include media-max(993px) {
      grid-column: 1;
      grid-row: 3;
    }
  }
}

/**
 * Hijri Calendar
 */

:root {
  --flatpickr-hijri-date-color: #3cc031;
  --flatpickr-hijri-date-selected: #f8d4b4;
}
.flatpickr-hijri-month-name {
  color: var(--flatpickr-hijri-date-color);
}
.flatpickr-hijri-date-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  -ms-flex-pack: space-evenly;
  justify-content: space-evenly;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  line-height: 0.9em;
  height: 100%;
}
.flatpickr-hijri-date-hijri {
  /*background-color: #80cbc4;*/
  font-size: 12px;
  color: var(--flatpickr-hijri-date-color);
}
.flatpickr-hijri-date-not-allowed {
  color: rgba(57, 57, 57, 0.3) !important;
}
.flatpickr-hijri-date-selected {
  color: var(--flatpickr-hijri-date-selected) !important;
}
.flatpickr-hijri-actions {
  height: 40px;
  max-height: 0px;
  visibility: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.06);
  padding: 0 8px;
}
.flatpickr-hijri-actions.darkTheme {
  color: #fff;
  fill: #fff;
}
.flatpickr-hijri-actions.visible {
  max-height: 40px;
  visibility: visible;
}
.flatpickr-hijri-switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 17px;
}
.flatpickr-hijri-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
label.flatpickr-hijri-switch {
  margin-left: 8px;
}
.flatpickr-hijri-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.flatpickr-hijri-slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 2px;
  background-color: #fff;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
input:checked + .flatpickr-hijri-slider {
  background-color: var(--flatpickr-hijri-date-color);
}
input:focus + .flatpickr-hijri-slider {
  -webkit-box-shadow: 0 0 1px var(--flatpickr-hijri-date-color);
  box-shadow: 0 0 1px var(--flatpickr-hijri-date-color);
}
input:checked + .flatpickr-hijri-slider:before {
  -webkit-transform: translateX(13px);
  -ms-transform: translateX(13px);
  transform: translateX(13px);
}
