<?php


namespace App\TokenGenerator;


use Izam\Daftra\Common\Auth\AuthUserTypeUtil;
use Izam\Daftra\Queue\Tokens\ITokenGenerator;
use Izam\Daftra\Queue\Tokens\TokenData;

class CakeTokenGenerator implements ITokenGenerator
{
    /**
     * @var TokenData $tokenData
     */
    private $tokenData;

    /**
     * CakeTokenGenerator constructor.
     * @param TokenData $tokenData
     */
    public function __construct($tokenData)
    {
       $this->tokenData = $tokenData;
    }

    public function generate()
    {

        $id = null; $type = null; $branch_id = getCurrentBranch('id');
        if (getAuthClient('id')) {
            $id = getAuthClient('id');
            $type = AuthUserTypeUtil::CLIENT;
        } else if (getAuthStaff('id') && getAuthStaff('id') != -1) {
            $id = getAuthStaff('id');
            $type = AuthUserTypeUtil::STAFF;
        } else if (getAuthOwner('id')) {
            $id = getAuthOwner('id');
            $type = AuthUserTypeUtil::OWNER;
        } else {
            $id = getCurrentSite('id');
            $type = AuthUserTypeUtil::OWNER;
        }
        $this->tokenData->setValues($id, $type, $branch_id);

        if (empty($id)) {
            return  null;
        }
        return jwt_get_token($this->tokenData->toArray());
    }
}
