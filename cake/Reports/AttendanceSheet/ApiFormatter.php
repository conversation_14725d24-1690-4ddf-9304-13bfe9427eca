<?php

namespace App\Reports\AttendanceSheet;
use Izam\Daftra\Common\Utils\PermissionUtil;

class ApiFormatter
{
    public function __construct(protected array|null $reportData)
    {

    }

    public  function formatApiResponse($params)
    {
        $staffId= $params['employee'];
        $userCanViewAttendanceLogs = $this->userCanViewAttendanceLogs($staffId);
        if (!$userCanViewAttendanceLogs ){
            die(json_encode([])) ;
        }
        //data is grouped by currency
        foreach ($this->reportData as $currency => $report_datum){
            $totals = $report_datum['total_fields'];
            $groupedFields = array_pop($report_datum['data'])['group_fields_raw_values'];
            $presentDays = explode('/',$groupedFields['present']);
            $presentDaysPercentage = $totals['present'];

            $absentDays = explode('/',$groupedFields['absent']);
            $absentDaysPercentage = $totals['absent'];

            $leaves = explode('/',$groupedFields['leave_type_id']);
            $totalLeavesPercentage = $totals['leave_type_id'];

            $actualWorkingHours = $groupedFields['actual_working_hours'];
            $expectedHours  = $groupedFields['expected_working_hours'];

            $missingHours = $expectedHours-$actualWorkingHours;
            if ($missingHours <= 0){
                $missingHours = 0;
                $missingHoursPercentage  =  0;
            }else{
                $missingHoursPercentage  =  ($missingHours / ($expectedHours ?: 1)) * 100;
            }
            $actualWorkingHoursPercentage  =  ($actualWorkingHours / ($expectedHours ?: 1)) * 100;

            $lateLogins = explode('/',$groupedFields['attendance_delay']);
            $lateLoginsPercentage = +$totals['attendance_delay'];

            $earlyLogins = explode('/',$groupedFields['early_logins']);
            $earlyLoginsPercentage = +$totals['early_logins'];

            $overTime = explode('/',$groupedFields['overtime']);
            $overTimePercentage = +$totals['overtime'];

            $overAllMissingHours = explode('/',$groupedFields['missing_hours']);
            $overAllMissingHoursPercentage = +$totals['missing_hours'];
            $data = [
                "presentDays" => [
                    "title" => __("Present Days",true),
                    "count" => +$presentDays[0],
                    "percentage" => round(+$presentDaysPercentage, 2),
                    "percentageText" => __("Of Contracted Workdays",true)
                ],
                "takenLeaves" => [
                    "title" => __("Taken Leaves",true),
                    "count" => +$leaves[0],
                    "percentage" => round(+$totalLeavesPercentage , 2),
                    "percentageText" => __("Of Contracted Workdays",true)
                ],
                "absence" => [
                    "title" => __("Absence",true),
                    "count" => +$absentDays[0],
                    "percentage" =>round(+$absentDaysPercentage , 2),
                    "percentageText" => __("Of Contracted Workdays",true)
                ],
                "workedHours" => [
                    "title" => __('Total Working Hours',true),
                    "count" => round($actualWorkingHours ,2),
                    "percentage" => round($actualWorkingHoursPercentage ,2),
                    "percentageText" =>  __("Of Contracted Hours",true)
                ],
                "missingHours" => [
                    "title" => __('Missing Hours',true),
                    "count" => round($missingHours ,2) ,
                    "percentage" => round($missingHoursPercentage , 2),
                    "percentageText" =>  __("Of Contracted Hours",true)
                ],
                "earlyAndLateLogins" => [
                    "title" => __("Early vs Late Login",true),
                    "early" => [
                        "count" => +$earlyLogins[0],
                        "percentage" => round($earlyLoginsPercentage , 2)
                    ],
                    "late" => [
                        "count" => +$lateLogins[0],
                        "percentage" => round($lateLoginsPercentage , 2)
                    ]
                ],
                "overTimeAndMissingHours" => [
                    "title" => __("Daily Overtime vs Missing H.",true),
                    "overtime" => [
                        "count" => +$overTime[0],
                        "percentage" =>  round($overTimePercentage , 2)
                    ],
                    "missingHours" => [
                        "count" => +$overAllMissingHours[0],
                        "percentage" => round($overAllMissingHoursPercentage , 2)
                    ]
                ],
                "overAllAttendance" => [
                    "present" =>  round(+$totals["present"] , 2 ),
                    "leaves" => +$totals["leave_type_id"],
                    "absence" => +$totals["absent"],
                    "missing" => round( 100 - ($totals["present"] + $totals["leave_type_id"] + $totals["absent"]) , 2)
                ]
            ];

            die(json_encode($data)) ;
        }
    }

    private function userCanViewAttendanceLogs($staffId)
    {
        if (check_permission(PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG)) return true;
        $authStaff = getAuthOwner('staff_id');
        if (check_permission(PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG) && $authStaff == $staffId) return true;
        return false;
    }
}