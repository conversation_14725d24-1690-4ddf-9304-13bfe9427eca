<?php

namespace App\Reports\Payslips;

use Izam\Daftra\Common\Utils\PermissionUtil;

class ApiFormatter
{
    protected ?array $report_data;


    public function __construct(protected array|null $reportData)
    {
        $this->report_data = $reportData;
    }

    public function formatApiResponse($params)
    {
        if (empty($this->report_data)) {
            die(json_encode([
                'message'=>__t('No data match the supplied filters'),
                'items'=>[]
            ]));
        }
        $groupedBy = $params['group_by'];
        if (!check_permission(PermissionUtil::VIEW_PAY_RUN) || $groupedBy != 'Monthly') return die(json_encode([ 'isVisible' => false ]));
        //this block only target is to get actual working hours of employee
        unset($params['currency']);
        $params['group_by'] = "Employee";
        $actualWorkingHours = 0;
        $attendanceSheetReport = \ReportV2\ReportFactory::create('attendance_sheets', $params);
        $attendanceSheetReportData = $attendanceSheetReport->get_report_data();
        if (!empty($attendanceSheetReportData)){
            $empReportData =array_pop($attendanceSheetReportData)['data'];
            $empReportDataGroupedFields = array_pop($empReportData)['group_fields_raw_values'];
            $actualWorkingHours = $empReportDataGroupedFields['actual_working_hours'];
        }
        //data is grouped by currency
        $chartValues = 0;
        foreach ($this->report_data as $currency => $report_datum) {
            $response = [];
            $totals = $report_datum['raw_values'];
            $groupedFields = $report_datum['data'];

            $grossSalary=  $totals['gross_pay'] ;
            $netSalary= $totals['net_pay'];
            $monthsCount = count($groupedFields) ?? 1 ;

            $averageGrossSalary = $grossSalary / $monthsCount;
            $averageNetSalary = $netSalary / $monthsCount;

            $response['statistics']=[
                'averageGrossSalary'=> [
                    'text'=> __t('Average Gross Salary',true),
                    'value'=>format_price( $averageGrossSalary, $currency)
                ],
                'averageNetSalary'=> [
                    'text'=> __t('Average Net Salary',true),
                    'value'=>format_price( $averageNetSalary, $currency)
                ],
                'netSalaries'=>[
                    'text'=> __t('Total Net Salary',true),
                    'value'=>format_price( $netSalary, $currency)
                ],
            ];

            if ($actualWorkingHours){
                $response['statistics']['averageCostPerWorkingHours'] = [
                    'text'=> __t('Average Cost / Working Hours',true),
                    'value'=> format_price( ($netSalary / $actualWorkingHours) , $currency)
                ];
            }
            $response['isVisible'] = true;
            $response['components'] = [];
            foreach ($groupedFields as $gKey => $gValue) {
                $monthName = date("M Y", strtotime($gKey));
                $response['months'][] = $monthName;
                $monthData = $gValue['group_fields'];
                foreach ($monthData as $mKey => $mValue) {
                    if ($mValue && $mKey == "net_pay") $chartValues += 1;
                    if ($mKey === 'gross_pay') continue;

                    if ($mKey === 'net_pay') {
                        $mKey = __t('Total',true);
                        $response['nets'][] = $mValue;
                    }

                    if ($mKey == 'Basic ') {
                        $mKey = __t('Basic Salary Component',true);
                    }

                    if (!isset($response['components'][$mKey])) {
                        $response['components'][$mKey] = [
                            [
                                'key' => __t('Component' ,true),
                                'value' => $mKey
                            ],
                            [
                                'key' => $monthName,
                                'value' => $mValue
                            ]
                        ];
                    } else {
                        $response['components'][$mKey][] = [
                            'key' => $monthName,
                            'value' => $mValue
                        ];
                    }
                }
            }
            foreach ($totals as $key=>$total){
                if ($key === 'gross_pay') continue;
                if ($key === 'net_pay') $key = __t('Total',true);
                if ($key === 'Basic ') $key = __t('Basic Salary Component',true);

                $response['components'][$key][] =  [
                    'key' => __t('Total',true),
                    'value' => $total
                ];
            }
            $response['chartIsVisible'] = $chartValues > 1;
            die(json_encode($response));
        }
    }
}