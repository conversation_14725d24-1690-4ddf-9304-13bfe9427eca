<?php

namespace App\Reports\CashFlow;

use Izam\Daftra\Common\Settings\CashFlowReportStructure;

class MultipleAccountCalculator implements RowCalculator
{
    public function __construct(protected $accountingSystem)
    {
    }

    public function getTotal($row, $options = []) {
        $total = 0;
        foreach ($row['accounts'] as $account) {
            $amounts = AccountAmountsGetter::getAmounts($account, $options, $this->accountingSystem);
            $total += CashFlowCalculator::calculateTotal($account['calculation_type'], $amounts['amounts'], $amounts['amounts_before']);
        }
        return $total;
    }

}