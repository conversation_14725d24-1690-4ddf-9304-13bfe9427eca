<?php

namespace App\Reports\CashFlow;

use Izam\Daftra\Common\Settings\CashFlowReportStructure;

class AccountAmountsGetter
{
    private static $journalModel;

    private static $journalCatModel;

    public static function getAmounts($account, $options, $accountingSystem) {
        $row = $account;
        $amountsBefore = [];
        self::$journalModel = GetObjectOrLoadModel('Journal');
        self::$journalCatModel = GetObjectOrLoadModel('JournalCat');
        $account = self::setAccountId($row, $accountingSystem);
        if ($row['is_main']) {
            $accountId = self::getCatId($account['entity_key'], $account['account_id']);
            $amounts = self::$journalModel->report_caculate_cat_total($accountId, $options);
            if (in_array($account['calculation_type'], [CashFlowReportStructure::CALCULATION_TYPE_INCREASE_IN_DEBT, CashFlowReportStructure::CALCULATION_TYPE_INCREASE_IN_CREDIT])) {
                $amountsBefore = self::$journalModel->report_caculate_cat_total($accountId, ['date_to' => $options['date_from']], false);
            }
        } else {
            $accountId = self::getAccountId($account['entity_key'], $account['account_id']);
            $amounts = self::$journalModel->report_caculate_account_total($accountId, $options);

            if (in_array($account['calculation_type'], [CashFlowReportStructure::CALCULATION_TYPE_INCREASE_IN_DEBT, CashFlowReportStructure::CALCULATION_TYPE_INCREASE_IN_CREDIT])) {
                $amountsBefore = self::$journalModel->report_caculate_account_total($accountId, ['date_to' => $options['date_from']], false);
            }
        }
        return ['amounts' => $amounts, 'amounts_before' => $amountsBefore];
    }


    private static function getCatId($entityKey, $catId)
    {
        if (isset($entityKey) && !isset($catId)) {
            $journalCat = self::$journalCatModel->find('first', ['conditions' => ['JournalCat.entity_type' => $entityKey, 'JournalCat.entity_id IS NULL']]);
            $catId = $journalCat['JournalCat']['id'];
        }

        return $catId;
    }
    private static function getAccountId($entityKey, $accountId)
    {
        if (isset($entityKey) && !isset($accountId)) {
            $journalAccount = self::$journalCatModel->get_auto_account(['entity_type' => $entityKey, 'entity_id' => 0]);
            $accountId = $journalAccount['JournalCat']['id'];
        }

        return $accountId;
    }

    private static function setAccountId($account, $accountingSystem)
    {

        $entityKey = isset($account['entity_key']) ? $account['entity_key'] : null;
        if($accountingSystem === 'english' && isset($account['english_accounting_entity_key'])) {
            $entityKey = $account['english_accounting_entity_key'];
        }

        if (isset($entityKey) && !isset($account['account_id'])) {
            $journalCat = self::$journalCatModel->find('first', ['conditions' => ['JournalCat.entity_type' => $entityKey, 'JournalCat.entity_id IS NULL']]);
            $account['account_id'] = $journalCat['JournalCat']['id'];
        }

        return $account;
    }
}