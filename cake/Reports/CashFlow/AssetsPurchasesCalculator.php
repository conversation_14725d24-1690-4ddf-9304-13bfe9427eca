<?php

namespace App\Reports\CashFlow;
\App::import('Vendor','CurrencyConverter', array('file' => 'CurrencyConverter.php'));
class AssetsPurchasesCalculator implements RowCalculator
{

    public function getTotal($row, $options = [])
    {
        $total = 0;
        $assetModel = GetObjectOrLoadModel('Asset');
        $assets = $assetModel->find('all', [
            'conditions' => [
                'Asset.branch_id' => isset($options['branch_id']) ? $options['branch_id'] : 1,
                'Asset.purchase_date >=' => $assetModel->formatDate($options['date_from']),
                'Asset.purchase_date <=' => $assetModel->formatDate($options['date_to']),
            ]
        ]);
        $default_currency = $assetModel->get_default_currency();
        foreach ($assets as $asset) {
            $assetValue = !empty($asset['Asset']['purchase_value_without_tax']) ? $asset['Asset']['purchase_value_without_tax'] : $asset['Asset']['asset_purchase_value'];
            if($default_currency != $asset['Asset']['asset_currency']){
                $assetValue *= \CurrencyConverter::index($asset['Asset']['asset_currency'], $default_currency, $asset['Asset']['purchase_date']);
            }
            $total += $assetValue;
        }
        return -$total;
    }
}