<?php

namespace App\Reports\CashFlow;

use App\Helpers\TaxesHelper;

class AssetsSellingCalculator implements RowCalculator
{

    public function getTotal($row, $options = [])
    {
            $total = 0;
            $assetOperationModel = GetObjectOrLoadModel('AssetOperation');
            $assetOperations = $assetOperationModel->find('all', [
                'conditions' => [
                    'Asset.branch_id' => isset($options['branch_id']) ? $options['branch_id'] : 1,
                    'AssetOperation.type' => \AssetOperation::TYPE_SELL,
                    'AssetOperation.date >=' => $assetOperationModel->formatDate($options['date_from']),
                    'AssetOperation.date <=' => $assetOperationModel->formatDate($options['date_to']),
                ]
            ]);

            $default_currency = $assetOperationModel->get_default_currency();
            foreach ($assetOperations as $assetOperation) {
                $taxes = [];
                if ($assetOperation['AssetOperation']['tax_1_data']) {
                    $taxes[] = json_decode($assetOperation['AssetOperation']['tax_1_data'], true);
                }

                if ($assetOperation['AssetOperation']['tax_2_data']) {
                    $taxes[] = json_decode($assetOperation['AssetOperation']['tax_2_data'], true);
                }

                $amountWithTaxes = TaxesHelper::calculateAmountWithoutTaxes($assetOperation['AssetOperation']['value'], $taxes);
                if($assetOperation['Asset']['asset_currency'] != $default_currency){
                    $amountWithTaxes *= \CurrencyConverter::index($assetOperation['Asset']['asset_currency'], $default_currency, $assetOperation['AssetOperation']['date']); 
                }
                $total += $amountWithTaxes;
            }
            return $total;
    }
}