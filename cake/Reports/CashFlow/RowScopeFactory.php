<?php

namespace App\Reports\CashFlow;
use Izam\Daftra\Common\Settings\CashFlowReportStructure;

\App::import('Vendor', 'settings');

//manages the classes responsible for row calculation in the cash flow report
class RowScopeFactory
{
    public static function getScopeCalculator($row): RowCalculator {
        switch ($row['scope']) {
            case CashFlowReportStructure::SCOPE_SINGLE_ACCOUNT:
                $accountingSystem = \settings::getValue(0, 'current_journal_accounting_system');
                return new SingleAccountCalculator($accountingSystem);
            case CashFlowReportStructure::SCOPE_MULTIPLE_ACCOUNTS:
                $accountingSystem = \settings::getValue(0, 'current_journal_accounting_system');
                return new MultipleAccountCalculator($accountingSystem);
            case CashFlowReportStructure::SCOPE_MANUAL:
                return self::getManualScopeCalculator($row);
            default:
                throw new \Exception('Invalid row scope');
        }
    }

    public static function getManualScopeCalculator($row): RowCalculator {
        switch ($row['key']) {
            case 'assets_selling':
                return new AssetsSellingCalculator();
            case 'assets_purchases':
                return new AssetsPurchasesCalculator();
            default:
                throw new \Exception('Invalid row key');
        }
    }
}