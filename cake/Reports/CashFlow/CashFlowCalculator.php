<?php

namespace App\Reports\CashFlow;

use Izam\Daftra\Common\Settings\CashFlowReportStructure;

class CashFlowCalculator
{
    public static function calculateTotal($calculationType, $amounts, $amountsBefore = null)
    {
        switch ($calculationType) {
            case CashFlowReportStructure::CALCULATION_TYPE_CREDIT_IN_PERIOD:
                $total = $amounts['total_credit'] - $amounts['total_debit'];
                break;
            case CashFlowReportStructure::CALCULATION_TYPE_DEBIT_IN_PERIOD:
                $total = $amounts['total_debit'] - $amounts['total_credit'];
                break;
            case CashFlowReportStructure::CALCULATION_TYPE_INCREASE_IN_DEBT:
                $total = $amounts['total_debit'] - $amounts['total_credit'];
                $totalBefore = $amountsBefore['total_debit'] - $amountsBefore['total_credit'];
                $total = $total - $totalBefore;
                break;
            case CashFlowReportStructure::CALCULATION_TYPE_INCREASE_IN_CREDIT:
                $total = $amounts['total_credit'] - $amounts['total_debit'];
                $totalBefore = $amountsBefore['total_credit'] - $amountsBefore['total_debit'];
                $total = $total - $totalBefore;
                break;
            default:
                $total = 0;
                break;
        }
        return $total;
    }
}