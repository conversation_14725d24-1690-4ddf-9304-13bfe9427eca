<?php

namespace App\Reports\CashFlow;

use Izam\Daftra\Common\Settings\CashFlowReportStructure;

class SingleAccountCalculator implements RowCalculator
{
    public function __construct(protected $accountingSystem)
    {
    }

    public function getTotal($row, $options = []) {
        $amounts = AccountAmountsGetter::getAmounts($row, $options, $this->accountingSystem);
        $total = CashFlowCalculator::calculateTotal($row['calculation_type'], $amounts['amounts'], $amounts['amounts_before']);
        return $total;
    }

}