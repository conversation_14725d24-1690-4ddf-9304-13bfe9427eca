<?php

namespace App\Reports\CashFlow;

use App\Helpers\TaxesHelper;
use Izam\Daftra\Common\Settings\CashFlowReportStructure;
use Izam\Daftra\Common\Utils\JournalUtil;
use Izam\Daftra\Common\Utils\PluginUtil;

\App::import('Vendor', 'settings');

class CashFlowReport
{
    public function get($filters): array
    {
        if(!isset($filters['date_from'])) {
            return [];
        }
        $data = $this->getData();
        $options = $filters;
        $reportTotal = 0;
        $accountingSystem = \settings::getValue(0, 'current_journal_accounting_system');
        $isAccountingSystemEnglish = $accountingSystem === 'english';
        if(!ifPluginActive(PluginUtil::BranchesPlugin)) {
            $options['branch_id'] = getMainBranch('id');
        }
        foreach ($data['sections'] as &$section) {
            $sectionTotal = 0;
            foreach ($section['rows'] as $k => &$row) {
                if(!$row['show']) {
                    unset($section['rows'][$k]);
                    continue;
                }
                $rowCalculator = RowScopeFactory::getScopeCalculator($row);
                $rowTotal = $rowCalculator->getTotal($row, $options);
                $row['total'] = $rowTotal;
                $sectionTotal += $rowTotal;
            }
            $section['total'] = $sectionTotal;
            $reportTotal += $sectionTotal;
        }
        $data['total'] = $reportTotal;
        return $data;
    }

    private function getData()
    {
        $report = \settings::getValue(0, 'cash_flow_report');
        return json_decode($report, true);
    }
}

