<?php

namespace App\Formatter;

class PostFormatter
{
    public function format($data, $actions, $statuses)
    {
        return [
            'share_with_partner' => ('0' == $data['share_with_partner']) ? 'No' : 'Yes',
            'date' => $data['date'],
            'body' => $data['body'],
            'action' => isset($actions[$data['action_id']]) ? $actions[$data['action_id']] : $data['action_id'],
            'status' => isset($statuses[$data['status_id']]) ? $statuses[$data['status_id']] : $statuses['status_id'],
        ];
    }
}
