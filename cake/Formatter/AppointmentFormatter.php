<?php

namespace App\Formatter;

require_once APP . 'models' . DS . 'client_appointment.php';

class AppointmentFormatter
{
    public function format($data, $actions, $assignedStaff)
    {
        $clientAppointment = GetObjectOrLoadModel('ClientAppointment');
        $statuses = $clientAppointment->getStatuses();

        $data['share_with_partner'] = ('0' == $data['share_with_partner']) ? 'No' : 'Yes';
        $data['status'] = isset($statuses[$data['status']]) ? $statuses[$data['status']]: $data['status'];
        $data['action'] = isset($actions[$data['action_id']]) ? $actions[$data['action_id']] : $data['action_id'];
        $data['staff'] = $this->formatAssignedStaffs($assignedStaff);

        unset($data['item_type'], $data['action_id'], $data['branch_id']);

        return $data;
    }

    private function formatAssignedStaffs($assignedStaffs)
    {
        $fullNames = [];
        foreach ($assignedStaffs as $assignedStaff) {
            $fullNames[] = $assignedStaff['Staff']['full_name'];
        }
        
        return $fullNames;
    }
}
