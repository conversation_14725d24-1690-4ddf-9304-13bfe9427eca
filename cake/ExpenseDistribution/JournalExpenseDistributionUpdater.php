<?php

namespace App\ExpenseDistribution;

use Izam\Daftra\Expense\Services\DistributedItem;
use Izam\Daftra\Expense\Services\ExpenseDistributionRepo;
use Izam\Daftra\Expense\Services\ExpenseDistributor;
use Izam\Daftra\Expense\Services\ItemDistributor;

class JournalExpenseDistributionUpdater
{
    /**
     * @param $data
     * @param $oldData
     * @return array[]
     *get transactions that its amount have changed or deleted
     */
    public static function getUpdatedJournalTransactions($data, $oldData) {
        $updatedTransactionIds = [];
        $deletedTransactionIds = [];
        foreach ($oldData['JournalTransaction'] as $oldTransaction) {
            $found = false;
            foreach ($data['JournalTransaction'] as $transaction) {
                if($oldTransaction['id'] !== $transaction['id']) continue;
                $found = true;
                if(abs($oldTransaction['currency_debit']  - $transaction['currency_debit']) < 0.01) continue;
                $updatedTransactionIds[] = $transaction['id'];
            }
            if(!$found) {
                $deletedTransactionIds[] = $oldTransaction['id'];
            }
        }
        return ['updated' => $updatedTransactionIds, 'deleted' => $deletedTransactionIds];
    }

    //not used bcs of performance and business issue in journals
    public static function update($data, $oldData) {
        return;
        if(!$oldData) return;

        $result = self::getUpdatedJournalTransactions($data, $oldData);
        $updatedTransactionIds = $result['updated'];
        $deletedTransactionIds = $result['deleted'];
        $ExpenseDistribution = GetObjectOrLoadModel('ExpenseDistribution');
        $distributionsOfUpdatedTransactions = [];
        $distributionsOfDeletedTransactions = [];
        if($updatedTransactionIds) {
            $distributionsOfUpdatedTransactions = $ExpenseDistribution->find('all', ['conditions' => ['ExpenseDistribution.id in (select expense_distribution_id from expense_distribution_items where transaction_id in ('.implode($updatedTransactionIds).'))']]);
        }
        if($deletedTransactionIds) {
            $distributionsOfDeletedTransactions = $ExpenseDistribution->find('all', ['conditions' => ['ExpenseDistribution.id in (select expense_distribution_id from expense_distribution_items where transaction_id in ('.implode($deletedTransactionIds).'))']]);;
        }
        $distributionsToUpdate = [];
        $distributionsToDelete = [];
        //check if updated transaction is related to manual expense distribution and delete it
        foreach ($distributionsOfUpdatedTransactions as $distributionsOfUpdatedTransaction) {
            $toBeDeleted = false;
            foreach ($distributionsOfUpdatedTransaction['ExpenseDistributionItem'] as $item) {
                if(in_array($item['transaction_id'], $updatedTransactionIds) && $item['distribution_type'] === \Izam\Daftra\Common\Utils\ExpenseDistributionTypeUtil::MANUAL) {
                    $distributionsToDelete[] = $distributionsOfUpdatedTransaction;
                    $toBeDeleted = true;
                    break;
                }
            }
            if(!$toBeDeleted) {
                $distributionsToUpdate[] = $distributionsOfUpdatedTransaction;
            }
        }

        //check if the transaction related to an expense distribution that has only distribution item
        //and delete the whole expense distribution
        foreach ($distributionsOfDeletedTransactions as $distributionsOfDeletedTransaction) {
            if(count($distributionsOfDeletedTransaction['ExpenseDistributionItem']) === 1) {
                $distributionsToDelete[] = $distributionsOfDeletedTransaction;
            } else {
                $distributionsToUpdate[] = $distributionsOfDeletedTransaction;
            }
        }

        /**
         * delete the expense distribution requisition
         */
        $Requisition = GetObjectOrLoadModel('Requisition');
        foreach ($distributionsToDelete as $distribution) {
            $ExpenseDistribution->delete_with_related($distribution['ExpenseDistribution']['id']);
        }
        foreach ($distributionsToUpdate as $distribution) {
            $repo = new ExpenseDistributionRepo(getPDO());
            $currencyConverter = new \CurrencyConverter();
            $expenseDistributionItem = new ItemDistributor($repo, $currencyConverter);
            $expenseDistributor = new ExpenseDistributor($expenseDistributionItem);
            $requisition = $Requisition->getRequisition($distribution['ExpenseDistribution']['requisition_id']);
            $requisition['requisition_items'] = $requisition['RequisitionItem'];
            $expenseDistributor->distribute($requisition, $distribution['ExpenseDistributionItem'], $Requisition->get_default_currency(), []);

            if (!in_array($requisition['Requisition']['status'], [\Requisition::STATUS_ACCEPTED, \Requisition::STATUS_MODIFIED])) {
                continue;
            }
            \StockTransaction::updateForRequisition($requisition);
            $Requisition->update_journals($requisition, false);
        }
    }
}