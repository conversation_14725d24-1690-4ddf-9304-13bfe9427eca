<?php

use Izam\Daftra\Client\Exceptions\CanNotDeleteClientException;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Invoice\Utils\InvoiceTypeUtil;
use Izam\ScoreCalculator\Services\SiteScoreCalculator;
use App\Helpers\CurrencyHelper;
use Izam\Limitation\Utils\LimitationUtil;
App::import('Vendor', 'settings');

/**
 * @property Site $Site
 * @property Invoice $Invoice
 * @property InvoicePayment $InvoicePayment
 */
class Client extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
    var $name = 'Client';
    var $filters = array();
    var $CountryList=array();
    var $displayField = 'business_name';
    var $belongsTo = array(
        'Staff' => array('className' => 'Staff', 'foreignKey' => 'staff_id'),
    );
    var $hasMany = [
        'ClientDetail' => ['className' => 'ClientDetail', 'foreignKey' => 'client_id', 'order' => 'id asc'],
        'InvoicePayment' => ['className' => 'InvoicePayment', 'foreignKey' => 'client_id', 'order' => 'id asc'],
	    'Address' => ['className' => 'Address', 'foreignKey' => 'entity_id', 'fields' => ['id','address1','address2','city','state','postal_code','country_code'], 'conditions'=> ['Address.entity_type' => 'Client']],
        'ItemsTag' => array(
            'className' => 'ItemsTag',
            'foreignKey' => 'item_id',
            'conditions' => array('ItemsTag.item_type' => 2)
        )
    ];

    public static $INDIVIDUAL_CLIENT_TYPE = 2;
    public static $BUSINESS_CLIENT_TYPE = 3;

    var $hasOne = [];
    var $alt_title = 'Time-sheet';
    var $import = array(
        'email' => array('format' => 'email', 'unique_list'=>true,'is_unique' => true, 'required' => false, 'title' => 'Email', 'match' => array('E-mail', 'Email', 'email_address','Client_Email')),
        'client_number' => array('unique_list'=>true, 'is_unique' => true, 'title' => 'Client Number', 'match' => array('client_number', 'clientnumber', 'client_no')),
        'business_name' => array('title' => 'Business Name','is_unique' => false,'unique_list'=>false, 'required' => true,  'match' => array('business_name', 'businessname', 'name')),
        'first_name' => array('title' => 'First Name', 'match' => array('first_name', 'fname', 'firstname')),
        'last_name' => array('title' => 'Last Name', 'match' => array('last_name', 'lanem', 'lastname')),
        'phone1' => array('title' => 'Telephone','unique_list'=>true, 'match' => array('phone1', 'tel', 'telephone', 'phone')),
        'phone2' => array('title' => 'Mobile', 'unique_list'=>true,'match' => array('mob', 'mobile')),
        'address1' => array('title' => 'Street Address 1', 'match' => array('address', 'address1', 'street')),
        'address2' => array('title' => 'Street Address 2', 'match' => array('address', 'address2', 'street')),
        'city' => array('title' => 'City', 'match' => array('city')),
        'state' => array('title' => 'State', 'match' => array('state')),
        'postal_code' => array('title' => 'Postal Code', 'match' => array('PostalCode','postal_code','zip_code', 'zip code','zipcode', 'code')),
        'country_code' => array('format' => 'country_code', 'title' => 'Country', 'match' => array('country_code', 'Country')),
        'starting_balance' => array( 'title' => 'Opening Balance', 'match' => array('starting_balance', 'open_balance','balance')),
        'starting_balance_date' => array( 'title' => 'Opening Balance Date', 'match' => array('starting_balance_date', 'open_balance_date','balance_date','balance date')),
        'type' => array( 'title' => 'Type', 'match' => array('type', 'Type')),
        'gender' => array( 'title' => 'Client Gender', 'match' => array('gender', 'Gender')),
        'group_price_id' => array( 'title' => 'Price Group', 'match' => array('group_price_id', 'Price Group')),
    );



		
	  var $actsAs = array(
              'journal' => array(
                    'is_account'=>true,
                   // 'is_journal'=>true,
                 ),'image'=>array(
                     'photo' => array('width' => 0, 'height' => 0, 'aspect_required' => false,'extensions' => array('jpeg','jpg', 'png', 'gif'), 'folder' => 'files')
                     ),
				'tag' => array(

					   ),
				 'customform' => [
					 'custom_model_name' => 'ClientsCustomData',
					 'custom_data_table_name' => 'clients_custom_data'
				 ],
                 'notification' => [

                 ]
          );
	  
	  function getSortFields($sorted_by = null) {
		
				
        return array(
			
		array('title' => __('Name',true), 'field' => 'business_name'),
		array('title' => __('Created Date',true), 'field' => 'created', 'default' => 'desc'),
		array('title' => __('Client Number',true), 'field' => 'client_number'),
		array('title' => __('Status',true), 'field' => 'follow_up_status'),
		'default_order' => $sorted_by
		);
    }
	  
    function __construct($id = false, $table = null, $ds = null) {
        $this->Country=GetObjectOrLoadModel('Country');
        $this->Site=GetObjectOrLoadModel('Site');
        $realpath=('files' . DS . SITE_HASH . DS . 'photos' . DS);
        $dir_path = WWW_ROOT . DS . $realpath;
        // warning suppress
        if (!file_exists($dir_path)) {
            @ mkdir($dir_path);
        }
        // end warning suppress
        $this->actsAs['image'] = array('photo' => array('width' => 0, 'height' => 0, 'aspect_required' => false,'extensions' => array('jpeg','jpg', 'png', 'gif'), 'folder' => 'files' . DS . SITE_HASH . DS . 'photos' . DS));
        parent::__construct($id, $table, $ds);
        $this->import['client_number']['is_unique'] = json_decode(settings::getValue(AutoNumberPlugin, "0-0"), true)['require_unique'];
        $this->validate = array(
            'password' => array(
                'rule' => array('minLength', 6), 'allowEmpty' => true, 'message' => __('The password should be 6 characters at least', true)
            ),
            'confirm_password' => array('rule' => 'checkPasswd', 'allowEmpty' => true, 'message' => __('Please enter confirm password', true)),
            'site_id' => array('rule' => 'notempty', 'message' => __('Required', true)),
            'business_name' => array('rule' => 'notEmpty', 'message' => __('Required', true)),
            'email' => array(
                array('rule' => 'isRequiredEmail', 'message' => __('The Email Address is required when the invoicing method for the user is sending it via email', true)),
                'validEmail' => array(
                    'rule' => array('validateEmail'),
                    'message' => __('Invalid Email', true)
                ),
                array('rule' => 'isFieldUnique', 'allowEmpty' => true, 'message' => __('Email already exists', true))
            ),'phone2' => array(
                array('rule' => 'isFieldUnique', 'allowEmpty' => true, 'message' => __('Phone already exists', true))
            ),
            'country_code' => array('rule' => 'checkCountry', 'allowEmpty' => true, 'message' => __('Invalid country', true)),
            'language_code' => array('rule' => 'checkLanguage', 'allowEmpty' => true, 'message' => __('Invalid language', true)),
            'default_currency_code' => array('rule' => 'checkCurrency', 'allowEmpty' => true, 'message' => __('Invalid currency', true)),
            'link' => array('rule' => 'url', 'allowEmpty' => true, 'message' => __('Invalid Link', true))
        );
        if (ifPluginActive(WebsiteFrontPlugin)) {
            $this->validate['email'][0]['message'] = __('Email is required', true); 
            $this->validate['password'] = [
                ['rule' => array('minLength', 6), 'allowEmpty' => false, 'message' => __('The password should be 6 characters at least', true)],
            ];
            $this->validate['confirm_password'] = [
                ['rule' => 'notempty', 'allowEmpty' => false, 'message' => __('Confirm Password should not be empty', true)],
                ['rule' => 'checkPasswd', 'allowEmpty' => false, 'message' => __('Please enter confirm password', true)]
            ];
        }

        $client_settings = settings::getPluginValues(ClientsPlugin);
        if ($client_settings['client_credit_period'] == "1") {
            $this->import['credit_period'] = array('format' => 'number', 'title' => 'Credit Period Limit', 'match' => array('credit_period', 'Credit Period Limit'));
        }
        if ($client_settings['enable_client_credit_limit'] == "1") {
            $this->import['credit_limit'] =  array('format' => 'number', 'title' => 'Credit Limit', 'match' => array('credit_limit', 'Credit Limit'));
        }

    }

    // Custom validation method
    function validateEmail($check) {
        $email = array_values($check)[0];
        return (!empty($email))?filter_var($email, FILTER_VALIDATE_EMAIL) !== false:true;
    }

    public function before_import_save($data, $updateField)
    {
        if (isset($data['Client']['id'])) {
            if (isset($data['Client']['type'])) {
                $data['Client']['type'] = ($data['Client']['type'] == 1 || $data['Client']['type'] == 'Individual') ? 2 : (($data['Client']['type'] == 2 || $data['Client']['type'] == 'Business') ? 3 : null);
            }
            return $data;
        }
        if (!isset($data['Client']['type'])) {
            $data['Client']['type'] = settings::getValue(PluginUtil::ClientsPlugin, 'client_type');
            if ($data['Client']['type'] == "0") {
                $data['Client']['type'] = ($data['Client']['bn1'] || $data['Client']['bn2']) ? 3 : 2;
            }
            return $data;
        }
        $data['Client']['type'] = ($data['Client']['type'] == 1 || $data['Client']['type'] == 'Individual') ? 2 : (($data['Client']['type'] == 2 || $data['Client']['type'] == 'Business') ? 3 : null);
        return $data;
    }

  function import_view_link($id) {
    $row=$this->find('first',array('conditions'=>array('Client.id'=>$id)))    ;
    return '<a target="_blank" href="'.Router::url(array('controller'=>'clients','action'=>'view',$id)).'" >'.$row['Client']['business_name'].' #'.$id.'</a>';
    }

    function get_import_data() {
        $site=getCurrentSite();
        if(!empty($site['bn1_label']))
            $this->import['bn1']=[ 'title' => $site['bn1_label'], 'match' => array('title' => $site['bn1_label'])];
        if(!empty($site['bn2_label']))
            $this->import['bn2']=['title' => $site['bn2_label'], 'match' => array('title' => $site['bn2_label'])];

        $new_import = $this->import;
        $custom_fields = $this->_load_custom_fields();
        foreach ($custom_fields['fields'] as $f) {
            $new_import["field_{$f['CustomFormField']['id']}"] = array('is_unique' => false, 'required' => false, 'title' => $f['CustomFormField']['label'], 'match' => array($f['CustomFormField']['label'], strtolower($f['CustomFormField']['label']), str_replace(' ', '', $f['CustomFormField']['label']), strtolower(str_replace(' ', '', $f['CustomFormField']['label']))));
        }
        $new_import['category'] = [
            "title" => "Category",
            "match" => ["cat", 'category']
        ];
        $additionalFormsHandler =
            new \App\Services\LocalEntityForm\AdditionalFormsHandler(
                EntityKeyTypesUtil::CLIENT_ENTITY_KEY
            );

        return array_merge($new_import, $additionalFormsHandler->getImportFields());
    }

	
	function get_gender_list(){
		return array(
			GENDER_NOT_SELECTED => __("Not selected",true),
			GENDER_MALE => __("Male",true),
			GENDER_FEMALE => __("Female",true)
		);
	}
	
    function get_statement_data ( $client_id, $date_from = null, $date_to = null) {
		$journal_translate=__('Journal',true);
       $journal_account_id=$this->get_journal_id($client_id);
       if($journal_account_id==0){
           $journal_account_id='NULL';
       }
       $summaries = $this->query('SELECT I.currency_code, SUM(I.summary_total) as `total`, SUM(I.summary_paid) as `paid`, SUM(I.summary_total)-SUM(I.summary_paid) as unpaid FROM invoices as I WHERE (I.draft IS NULL OR I.draft = 0  ) AND I.type=0 AND I.client_id = ' . intval($client_id) . $this->getBranchCondition('I') . ' GROUP BY I.currency_code ORDER BY I.currency_code');

        if($date_from && $date_to) {
            $query = "
                    SELECT id,created ,0 as my_order,'invoice',`date`, `no`, summary_total as `amount`, currency_code, 1 as `is_invoice`,NULL as description FROM invoices WHERE (invoices.draft IS NULL OR invoices.draft = 0  ) AND invoices.type=0 AND client_id = :client_id AND invoices.`date` BETWEEN '$date_from' AND '$date_to' " . $this->getBranchCondition('invoices') . "
                    UNION SELECT IP.id,IP.created,1 as my_order , 'payment',IP.`date` as `date`,`I`.no as `no`, if(IP.amount < 0 AND I.type = 0, IP.amount, IP.amount) as `amount`, IP.currency_code as currency_code, 0 as `is_invoice`,NULL as description FROM invoice_payments as IP JOIN (invoices as I) ON (I.id = IP.invoice_id) WHERE (I.draft IS NULL OR I.draft = 0  ) AND I.type in (0,6) AND I.client_id = :client_id AND IP.status = 1 and payment_method != 'client_credit'  AND IP.`date` BETWEEN '$date_from' AND '$date_to' " . $this->getBranchCondition('IP') . "
                    UNION select id,created,2 as my_order , 'Payment',`date`, id, amount as `amount`, currency_code as currency_code, 0 as `is_invoice`,NULL as description from invoice_payments where client_id=:client_id and payment_method <> 'client_credit' and payment_method <> 'starting_balance' and status=1  AND `date` BETWEEN '$date_from' AND '$date_to' " . $this->getBranchCondition('invoice_payments') . "
                    UNION select id,created,2 as my_order , 'StartingBalance',`date`, id, amount as `amount`, currency_code as currency_code, 1 as `is_invoice`,NULL as description from invoice_payments where client_id=:client_id and payment_method = 'starting_balance' and status=1  AND `date` BETWEEN '$date_from' AND '$date_to'" . $this->getBranchCondition('invoice_payments') . "
                    UNION SELECT id,created,1 as my_order , 'Refund Receipt',`date`, `no`, summary_total as `amount`, currency_code, 0 as `is_invoice`,NULL as description FROM invoices WHERE (invoices.draft IS NULL OR invoices.draft = 0  ) AND invoices.type=6 AND client_id = :client_id  AND `date` BETWEEN '$date_from' AND '$date_to'" . $this->getBranchCondition('invoices') . "
                    UNION SELECT JT.id, J.date,1 AS my_order,'Journal',J.`date`,JT.`journal_id`,IF(ROUND(JT.currency_credit-JT.currency_debit,2)<0,ROUND(JT.currency_credit - JT.currency_debit,2)*-1,ROUND(JT.currency_credit - JT.currency_debit,2)) AS `amount`,JT.currency_code,IF(JT.currency_credit-JT.currency_debit<0,1,0) AS `is_invoice`,IF(JT.alter_description!='',JT.alter_description,IF(JT.description='',concat('{$journal_translate} #',J.`number`),JT.description)) as description FROM `journal_transactions` AS `JT`  LEFT JOIN `journals` AS `J` ON(`JT`.`journal_id` = `J`.`id`) WHERE ((J.entity_type not in('invoice','invoice_payment','refund_receipt','credit_note', 'year_opening_balance','year_closing_balance')) OR (J.entity_type in ('invoice','refund_receipt','credit_note') and J.entity_id not in (select id from invoices where client_id = $client_id) ) ) and JT.journal_account_id={$journal_account_id}  AND J.`date` BETWEEN '$date_from' AND '$date_to'" . $this->getBranchCondition('J') . "
                    UNION SELECT id,created,1 as my_order,'Credit Note',`date`, `no`, summary_total as `amount`, currency_code, 0 as `is_invoice`,NULL as description FROM invoices WHERE (invoices.draft IS NULL OR invoices.draft = 0  ) AND invoices.type=5 AND client_id = :client_id  AND `date` BETWEEN '$date_from' AND '$date_to'" . $this->getBranchCondition('invoices') . "
                    UNION SELECT JT.id, J.date, 1 AS my_order, 'Journal', J.`date`, JT.`journal_id`, ABS(ROUND(JT.currency_credit - JT.currency_debit, 2)) AS `amount`, JT.currency_code, (JT.currency_credit - JT.currency_debit < 0) AS `is_invoice`, COALESCE(NULLIF(JT.alter_description, ''), IF(JT.description = '', CONCAT('Journal #', J.`number`), JT.description)) AS description FROM journal_transactions JT LEFT JOIN journals J ON J.id = JT.journal_id WHERE JT.journal_account_id = {$journal_account_id} AND JT.subkey like '%-client-1'  AND J.`date` BETWEEN '$date_from' AND '$date_to'
                    ORDER BY `date` ASC,`created` ASC,`my_order` ASC, currency_code ASC";
        } else {
            $query = "
                    SELECT id,created ,0 as my_order,'invoice',`date`, `no`, summary_total as `amount`, currency_code, 1 as `is_invoice`,NULL as description , NULL AS code FROM invoices WHERE (invoices.draft IS NULL OR invoices.draft = 0  ) AND invoices.type=0 AND client_id = :client_id" . $this->getBranchCondition('invoices') . "
                    UNION SELECT IP.id,IP.created,1 as my_order , 'payment',IP.`date` as `date`,`I`.no as `no`, if(IP.amount < 0 AND I.type = 0, IP.amount, IP.amount) as `amount`, IP.currency_code as currency_code, 0 as `is_invoice`,NULL as description , IP.code FROM invoice_payments as IP JOIN (invoices as I) ON (I.id = IP.invoice_id) WHERE (I.draft IS NULL OR I.draft = 0  ) AND I.type in (0,6,16) AND I.client_id = :client_id AND IP.status = 1 and payment_method != 'client_credit'" . $this->getBranchCondition('IP') . "
                    UNION select id,created,2 as my_order , 'Payment',`date`, id, amount as `amount`, currency_code as currency_code, 0 as `is_invoice`,NULL as description , invoice_payments.code from invoice_payments where client_id=:client_id and payment_method <> 'client_credit' and payment_method <> 'starting_balance' and status=1" . $this->getBranchCondition('invoice_payments') . "
                    UNION select id,created,2 as my_order , 'StartingBalance',`date`, id, amount as `amount`, currency_code as currency_code, 1 as `is_invoice`,NULL as description, NULL AS code from invoice_payments where client_id=:client_id and payment_method = 'starting_balance' and status=1" . $this->getBranchCondition('invoice_payments') . "
                    UNION SELECT id,created,1 as my_order , 'Refund Receipt',`date`, `no`, summary_total as `amount`, currency_code, 0 as `is_invoice`,NULL as description , NULL AS code FROM invoices WHERE (invoices.draft IS NULL OR invoices.draft = 0  ) AND invoices.type=6 AND client_id = :client_id " . $this->getBranchCondition('invoices') . "
                    UNION SELECT JT.id,J.date,1 AS my_order,'Journal',J.`date`,JT.`journal_id`,IF(ROUND(JT.currency_credit-JT.currency_debit,2)<0,ROUND(JT.currency_credit - JT.currency_debit,2)*-1,ROUND(JT.currency_credit - JT.currency_debit,2)) AS `amount`,JT.currency_code,IF(JT.currency_credit-JT.currency_debit<0,1,0) AS `is_invoice`,IF(JT.alter_description!='',JT.alter_description,IF(JT.description='',concat('{$journal_translate} #',J.`number`),JT.description)) as description , NULL AS code FROM `journal_transactions` AS `JT`  LEFT JOIN `journals` AS `J` ON(`JT`.`journal_id` = `J`.`id`) WHERE (J.draft IS NULL OR J.draft = 0 ) AND ((J.entity_type not in('invoice','invoice_payment','refund_receipt','credit_note', 'year_opening_balance','year_closing_balance')) OR (J.entity_type in ('invoice','refund_receipt','credit_note') and J.entity_id not in (select id from invoices where client_id = $client_id) ) ) and JT.journal_account_id={$journal_account_id}" . $this->getBranchCondition('J') . "
                    UNION SELECT JT.id, J.date, 1 AS my_order, 'Journal', J.`date`, JT.`journal_id`, ABS(ROUND(JT.currency_credit - JT.currency_debit, 2)) AS `amount`, JT.currency_code, (JT.currency_credit - JT.currency_debit < 0) AS `is_invoice`, COALESCE(NULLIF(JT.alter_description, ''), IF(JT.description = '', CONCAT('Journal #', J.`number`), JT.description)) AS description , NULL AS code FROM journal_transactions JT LEFT JOIN journals J ON J.id = JT.journal_id WHERE JT.journal_account_id = {$journal_account_id} AND JT.subkey like '%-client-1' 
                    UNION SELECT id,created,1 as my_order,'Credit Note',`date`, `no`, summary_total as `amount`, currency_code, 0 as `is_invoice`,NULL as description ,NULL AS code FROM invoices WHERE (invoices.draft IS NULL OR invoices.draft = 0  ) AND invoices.type=5 AND client_id = :client_id" . $this->getBranchCondition('invoices') . "
                    ORDER BY `date` ASC,`created` ASC,`my_order` ASC, currency_code ASC";
        }

//UNION SELECT IP.id,IP.created,2 as my_order , 'Refund Payment',IP.`date` as `date`,`I`.no as `no`, ROUND(IP.amount, 2) as `amount`, IP.currency_code as currency_code, 0 as `is_invoice`,NULL as description FROM invoice_payments as IP JOIN (invoices as I) ON (I.id = IP.invoice_id) WHERE (I.draft IS NULL OR I.draft = 0  ) AND I.type=6 AND I.client_id = :client_id
        ini_set('memory_limit', '5G');
        $detailedSummaries = $this->query(str_replace(':client_id', $client_id, $query));
        //removed bcs it makes difference between this report and clientBalance report
//        foreach ($detailedSummaries as &$summary) {
//            $summary[0]['amount'] = round($summary[0]['amount'], 2);
//        }

//        $all_cj=$this->get_client_journal_rows($client_id);
//       // print_pre($all_cj);
//        foreach($all_cj as $cj){
//         $detailedSummaries[][0]=array('no'=>$cj['JournalTransaction']['id'],'id'=>$cj['JournalTransaction']['id'],'created'=>$cj['JournalTransaction']['created'],'my_order'=>5
//             ,'invoice'=>'Journal','amount'=>$cj['JournalTransaction']['debit']==0?$cj['JournalTransaction']['credit']:$cj['JournalTransaction']['debit'],'currency_code'=>$cj['JournalTransaction']['currency_code'],'is_invoice'=>$cj['JournalTransaction']['debit']==0?1:0,
//             'date'=>$cj['JournalTransaction']['created'])   ;
//        }
//        [0] => Array
//                (
//                    [id] => 2562
//                    [created] => 2018-01-03 14:24:37
//                    [my_order] => 2
//                    [invoice] => Payment
//                    [date] => 2018-01-03 00:00:00
//                    [no] => 2562
//                    [amount] => 100.00
//                    [currency_code] => EGP
//                    [is_invoice] => 0
//                )
        
        foreach($detailedSummaries as $row){
        $clist[$row[0]['currency_code']]=$row[0]['currency_code'];    
        }
        
        $cj_list=$this->get_client_journals($client_id);
        foreach($cj_list as $cj){
       $clist[$cj[0]['cc']]=$cj[0]['cc'];     
       $new_cjs[$cj[0]['cc']]=$cj[0]['total_cn'];
         
        }
        
          //echo "<pre>";
      //  print_r($clist);
     //   die();
        foreach ( $summaries as $k => $v){
         //   print_r($v);
            // warning suppress
            $summaries[$k][0]['amount'] = $summaries[$k][0]['amount'] ?? 0;
            $total_refunds=$this->get_refund_total($client_id, $v['I']['currency_code']);
            
            $summaries[$k][0]['refunds']=$total_refunds;
            
            $summaries[$k][0]['client_credit'] = $summaries[$k][0]['amount'] - $summaries[$k][0]['paid']-$total_refunds ;
        }
     if(empty($summaries)){
         
       $i=0;  
       
      //elwan
      
     foreach($clist as $c){

           $total_refunds=$this->get_refund_total($client_id, $c);
            $summaries[$i]['I']['currency_code']=$c;
            $summaries[$i][0]['refunds']=$total_refunds;
          
                    //elwan
            $summaries[$i][0]['total']=0;
            $summaries[$i][0]['paid']=0;
            $summaries[$i][0]['unpaid']=0;
            $summaries[$i][0]['client_credit']=0;
             $i++;
            }   
     }else{
         $ok=false;
       foreach($clist as $c){
         foreach ( $summaries as $k => $v){
         if($summaries[$k]['I']['currency_code']==$c)    {
          $ok=true;
         }
       
         }
           if($ok!=true){
               $last_key=array_pop(array_keys($summaries))+1;   
               $i=$last_key;
           $total_refunds=$this->get_refund_total($client_id, $c);
            $summaries[$i]['I']['currency_code']=$c;
            $summaries[$i][0]['refunds']=$total_refunds;
            $summaries[$i][0]['total']=0;
            $summaries[$i][0]['paid']=0;
            $summaries[$i][0]['unpaid']=0;
            $summaries[$i][0]['client_credit']=0;
             //  die();
         }
         $ok=false;
       }  
     }


        $refunds=array();
        foreach ($detailedSummaries as $row){
          //$totalAmounts[$row[0]['currency_code']]=0;
                
            if ($row[0]['is_invoice']){ 
               if (!isset($totalAmounts[$row[0]['currency_code']])) {
                   $totalAmounts[$row[0]['currency_code']] = 0;
               }
               if(isset($row[0]['invoice']) && $row[0]['invoice']=="StartingBalance"){
                   $row[0]['amount']=$row[0]['amount']*-1;
               }
               $totalAmounts[$row[0]['currency_code']] += $row[0]['amount'];

            }else if (  $row[0]['invoice'] == "Credit Note"){
                $creditNotes[$row[0]['currency_code']]  += $row[0]['amount'];
            }else if (  $row[0]['invoice'] == "Credit Note"){
                $creditNotes[$row[0]['currency_code']]  += $row[0]['amount'];
            }else if (  $row[0]['invoice'] == "Refund Receipt"){
               
                $refunds[$row[0]['currency_code']]  += $row[0]['amount'];
            }else if (  $row[0]['invoice'] == "Refund Payment"){
                
                $refunds[$row[0]['currency_code']]  += $row[0]['amount'];
            }else{
               if (!isset($paidAmounts[$row[0]['currency_code']])) {
                   $paidAmounts[$row[0]['currency_code']] = 0;
               }
               if(isset($row[0]['invoice']) && $row[0]['invoice'] == 'payment' && $row[0]['amount'] < 0) {
                $totalAmounts[$row[0]['currency_code']] += abs($row[0]['amount']);
              } else {
                $paidAmounts[$row[0]['currency_code']] += $row[0]['amount'];
              }
               // if($row[0]['invoice']!='Refund Receipt'){
              //  }
            }
       }
//       foreach($new_cjs as $cj_key=>$cj_entry){
//       if($cj_entry<0){
//       $totalAmounts[$cj_key]= $totalAmounts[$cj_key]+($cj_entry*-1);
//       }else{
//         $paidAmounts[$cj_key] = $paidAmounts[$cj_key]+$cj_entry;   
//       }
//       }

       foreach ($totalAmounts as $curr => $amount) {
            $creditNotes[$curr] = $creditNotes[$curr] ?? 0;
            $paidAmounts[$curr] = $paidAmounts[$curr] ?? 0;
            $refunds[$curr] = $refunds[$curr] ?? 0;
            $clientCredits[$curr] = $clientCredits[$curr] ?? 0;
            $clientCredits[$curr] += ($amount -$creditNotes[$curr]- $paidAmounts[$curr]-$refunds[$curr]);//, $curr) . ' <span class="summary-currency">' . $curr . '</span>';
            
        }
        foreach($refunds as $curr=>$value){
            if($value==0){
           unset($refunds[$curr]); 
            }
        }
        return compact('summaries','detailedSummaries' , 'totalAmounts','paidAmounts' , 'clientCredits' , 'creditNotes','refunds');
    }
    function afterFind($results, $primary = false) {
        parent::afterFind($results, $primary);
        foreach ($results as $key => $val) {
            if (isset($val[$this->alias]['country_code']) and $this->recursive > -1) {
                if(!isset($this->CountryList[$val[$this->alias]['country_code']]) && !empty($val[$this->alias]['country_code'])) {
                    $this->CountryList[$val[$this->alias]['country_code']] = $this->FindCountry($val[$this->alias]['country_code']);

                }
                if(!empty($val[$this->alias]['country_code'])){
                    $results[$key]['Country'] = $this->CountryList[$val[$this->alias]['country_code']];
                }

            }
        }
        return $results;
    }

    function FindCountry($code = "") {
        return getCountry($code);
    }

    function hasMembershipOrCreditCharge($client_id){
	      return ($this->hasCreditCharge($client_id) || $this->hasMembership($client_id));
    }

    function hasCreditCharge ($client_id )
    {
        if(ifPluginActive(CREDIT_PLUGIN)){
            $creditCharge = GetObjectOrLoadModel ('CreditCharge') ;
            return $creditCharge->find ( 'count' , ['recursive' => -1 , 'conditions' => ['CreditCharge.client_id' => $client_id ,'CreditCharge.deleted_at IS NULL'] ]) > 0;
        }else{
            return false;
        }
    }
    function hasMembership ($client_id )
    {
        if(ifPluginActive(MEMBERSHIP_PLUGIN)){
            $membership = GetObjectOrLoadModel ('Membership') ;
            return $membership->find ( 'count' , ['recursive' => -1 , 'conditions' => ['Membership.client_id' => $client_id,'Membership.deleted_at IS NULL' ] ]) > 0;
        }else{
            return false;
        }
    }

    /**
     *
     * @param integer $site_id
     * @return integer $client_number
     */
    public function get_next_clientno($site_id = false) {
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }
        $last_client = $this->find('first', array('conditions' => array('Client.site_id' => getCurrentSite('id')), 'order' => 'Client.client_number DESC', 'fields' => 'Client.client_number', 'recursive' => -1));
        return intval($last_client[$this->name]['client_number']) + 1;
    }

    /**
     *
     * @param integer $client_id
     * @return language code, 'en' by default
     */
    public function get_client_language($client_id = false) {
        if (!$client_id) {
            return DEFAULT_LANGUAGE;
        }

        $client = $this->findById($client_id);

        if (!empty($client) && !empty($client[$this->name]['language_code']) && GetObjectOrLoadModel('Language')->check_language_code($client[$this->name]['language_code'])) {
            return $client[$this->name]['language_code'];
        } elseif (!empty($client) && empty($client[$this->name]['language_code'])) {
            $this->loadModel('Site');
            return $this->Site->get_site_language($client[$this->name]['site_id']);
        }

        return DEFAULT_LANGUAGE;
    }

    /**
     * This function is used for both site owner and clients
     * @param Array $data Login data email and password
     * @return Array Return array with three keys
     * 			<ul>
     * 				<li>success: a boolean to check if login was successful</li>
     * 				<li>type: Login type either 'owner' or 'client'</li>
     * 				<li>user: Logged in user data</li>
     * 			</ul>
     */
    function login_system($data) {
        $this->applyBranch = ['onFind' => false, 'onSave' => false];
        
        if (ifPluginActive(StaffPlugin)) {
            $conditions = [];
            //Login as owner didn't return a value, check for the email in Staff
            $conditions['email_address'] = $data['email'];
            $conditions['deleted_at'] = null;
         //  $conditions['active'] = 1;
       //    $conditions['can_access_system'] = 1;
            $Staff = GetObjectOrLoadModel('Staff');
            $Role = GetObjectOrLoadModel('Role');
            $StaffRow = $Staff->find('first', array('applyBranchFind'=>false,'conditions' => $conditions, 'recursive' => -1));
            if (!empty($StaffRow)) {
                if(isset($data['password_already_hashed'])){
                    $password = $data['password'];
                    unset($data['password_already_hashed']);
                }else{
                    $password = HashPassword($data['password']);
                }
                if ($StaffRow['Staff']['password'] == $password) {
                    if($StaffRow['Staff']['active'] == 0){
                        return array('success' => false,'errorMessage'=> __('User has been deactivated', true));
                    }
                    if($StaffRow['Staff']['can_access_system'] == 0){
                        return array('success' => false,'errorMessage'=> __('User not allowed to access the system', true));
                    }

					$Staff->id=$StaffRow['Staff']['id'];
                    $user['Staff']['id'] = $StaffRow['Staff']['id'];
                    $user['applyBranchSave']=false;
                    $user['Staff']['last_ip'] = get_real_ip();
                    $user['Staff']['last_login'] = date('Y-m-d h:i:s');
                    $Staff->id = $user['Staff']['id'];
                    $Staff->save($user, false, array('id', 'last_ip', 'last_login'));
                    $this->loadModel('Site');
                    $user = $this->Site->find('first', array('conditions' => array('Site.id' => getCurrentSite('id')), 'recursive' => -1));
                    $StaffRow = $Staff->getStaffWithRoleForSession($StaffRow['Staff']['id']);
                    return array('is_super_admin' => $StaffRow['Staff']['Role']['is_super_admin'], 'is_staff' => $StaffRow['Staff']['id'], 'type' => 'owner', 'user' => $user['Site'], 'staff' => $StaffRow['Staff'], 'success' => true, 'role_id'=> isset($StaffRow['Staff']['role_id'])?$StaffRow['Staff']['role_id']:null);
                }
            }
            $conditions['email'] = isset($conditions['email_address']) ? $conditions['email_address'] : $conditions['email'];
            // unset($conditions['email_address']);
            // unset($conditions['active']);
            // unset($conditions['deleted_at']);
        }
        
        //Check if the email belongs the current site owner, login as owner
        $conditions = ['email' => low($data['email']),  'subdomain' => getCurrentSite('subdomain')];
        if (low($data['email']) == low($_SESSION['CurrentSite']['email'])) {
            //$conditions['Site.id'] = getCurrentSite('id');
            $this->loadModel('Site');
            $user = $this->Site->find('first', array('conditions' => $conditions, 'recursive' => -1));
            if (!empty($user)) {
                $user_pass = is_string($data['password']) ? $data['password'] : '';
                $password =  HashPassword($user_pass);
                if ($user['Site']['password'] == $password) {
                    $user['Site']['last_ip'] = get_real_ip();
                    $user['Site']['last_login'] = date('Y-m-d h:i:s');
                    $this->Site->save($user, false, array('id', 'last_ip', 'last_login'));
                    return array('is_super_admin' => 1, 'is_staff' => 0, 'type' => 'owner', 'user' => $user['Site'], 'success' => true);
                } else {
                    $this->loadModel('User');
                    $superUser = $this->User->findById($user['Site']['user_id']);
                    if($superUser['User']['password'] == $password ) {
                        $user['Site']['last_ip'] = get_real_ip();
                        $user['Site']['last_login'] = date('Y-m-d h:i:s');
                        $this->Site->save($user, false, array('id', 'last_ip', 'last_login'));
                        return array('is_super_admin' => 1, 'is_staff' => 0, 'type' => 'owner', 'user' => $user['Site'], 'success' => true);
                    }
                }
                //    return array('success' => false);
            }
        }

        //Login as owner didn't return a value, check for the email in clients
        $conditions['suspend'] = 0;
        unset($conditions['subdomain']);
        $user = $this->find('first', array('applyBranchFind'=>false,'conditions' => $conditions, 'recursive' => -1));
        $client_disable_online_access= settings::getValue(ClientsPlugin, 'client_disable_online_access');
        
        if (!empty($user) && $client_disable_online_access != '1' ) {
            
            $password = HashPassword($data['password']);
            
            if ($user[$this->name]['password'] == $password) {
                
                $user['Client']['last_ip'] = get_real_ip();
                $user['Client']['last_login'] = date('Y-m-d h:i:s');
                $this->save($user, false, array('id', 'last_ip', 'last_login'));
                return array('type' => 'client', 'user' => $user[$this->name], 'client' => $user[$this->name], 'success' => true);
            }
        }

        //No user has been found, login is not successful
        return array('success' => false,'errorMessage'=>'Invalid email or password');
    }

    public function beforeSave($options = array()) {
        parent::beforeSave($options);
        if (!empty($this->data[$this->name]['password'])) {
			if($this->data[$this->name]['id']){
				$client = $this->findById($this->data[$this->name]['id']);

				if($this->data[$this->name]['password'] == $client['Client']['password']){
					return true;
				}
			}
            $this->data[$this->name]['password'] = HashPassword($this->data[$this->name]['password']);
        }
		App::import('Sanitize');
		foreach($this->data['Client'] as $k => &$v)
		{
		    if($k === 'email')
		        continue;
            $v = $this->strip_tags_old($v);
		}
        return true;
    }

    /**
     * php5.6 strip tags behavior
     * @param $val
     * @return string|null
     */
    function strip_tags_old($val): ?string
    {
        if (is_array($val)) {
            return null;
        } else {
            return strip_tags($val);
        }
    }

    private function getAutoNumber()
    {
        $rule = AutoNumber::get_rule(\AutoNumber::TYPE_CLIENT);

        $number = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_CLIENT);
        
        if (!isset($rule['require_unique']) || $rule['require_unique'] != '1') {
            return $number;
        }

        $client = $this->find('first', ['conditions' => ['client_number' => $number]]);

        while ($client) {
            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_CLIENT);
            $number = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_CLIENT);
            $client = $this->find('first', ['conditions' => ['client_number' => $number]]);
        }

        return $number;
    }

	/**
     *
     * @param array $data
     * @return array
     */
    function saveClient($data) {
        $site_id = getCurrentSite('id');
		
		App::import('Vendor', 'AutoNumber');
        \AutoNumber::set_validate(\AutoNumber::TYPE_CLIENT);
		//set number if new client and he didn't change or number is empty
		if(empty($data[$this->alias]['client_number'])||(empty($data[$this->alias]['id'])&&$data[$this->alias]['client_number']==$data[$this->alias]['default_client_number'])){
			$data[$this->alias]['default_client_number'] = $data[$this->alias]['client_number'] = $this->getAutoNumber();
			$generated_number = true;
		}

      //  if (!empty($data[$this->alias]['generate_password']) || (empty($data[$this->alias]['password']) && empty($data[$this->alias]['id']))) {
         //   $data[$this->alias]['password'] = $data[$this->alias]['confirm_password'] = substr(base64_encode(HashPassword(time() . mt_rand())), 0, 7);
        //}
                

        if (!empty($data[$this->alias]['id']) && empty($data[$this->alias]['password'])) {
            unset($data[$this->alias]['password']);
            unset($data[$this->alias]['confirm_password']);
        }

        if (!empty($data[$this->alias]['password'])) {
            $data[$this->alias]['password_view'] = $data[$this->alias]['password'];
        }

        $this->loadModel('Category');
        if(isset($data['Client']['category']) && !empty($data['Client']['category'])){
            $categoryCriteria = [
                'name' => $data['Client']['category'],
                'description' => $data['Client']['category'],
                'category_type' => 4,
            ];
            $categoryData = ['Category' => $categoryCriteria ];
            $this->Category->disableBranchFind();
            $category = $this->Category->find('first', ['conditions' => $categoryCriteria]);
            $category_id = $category['Category']['id'];
            if (empty($category_id)) {
                $this->Category->save($categoryData);
                $category_id = $this->Category->getLastInsertID();
            }
            $data['Client']['category_id'] = $category_id;
        }

        $this->set($data);
		
        if ($this->validates()) {
			
            if ($this->validationErrors) {
				
                return array('status' => false);
            }
            $data[$this->alias]['site_id'] = $site_id;

            if (empty($data[$this->alias]['id'])) {
                $this->create();
            }
            if (!empty($data[$this->alias]['id'])) {
                $this->ClientDetail->deleteAll(array('ClientDetail.client_id' => $data[$this->alias]['id']), false);
            }

            //Handle Insurance
            if (ifPluginActive(INSURANCE_PLUGIN)) {
                if (!empty($data[$this->alias]['insurance_agent_class'])) {
                    $clientInsuranceData = [
                        'ClientInsuranceClass' => [
                            'insurance_agent_class_id' => $data[$this->alias]['insurance_agent_class'],
                            'insurance_number' => $data[$this->alias]['insurance_number']
                        ]
                    ];
                    unset($data[$this->alias]['insurance_agent'], $data[$this->alias]['insurance_agent_class'], $data[$this->alias]['insurance_number']);
                } else {
                    $this->loadModel('ClientInsuranceClass');
                    $this->ClientInsuranceClass->deleteAll(['ClientInsuranceClass.client_id' => $data[$this->alias]['id']]);
                }
            }
			$data = $this->removeEmptyClientDetails($data);
            if ($r = $this->saveall($data, array('validate' => false))) {
				if(!empty($generated_number)) \AutoNumber::update_auto_serial(\AutoNumber::TYPE_CLIENT);
				elseif($data[$this->alias]['client_number']!=$data[$this->alias]['default_client_number']) \AutoNumber::update_last_from_number($data[$this->alias]['client_number'],\AutoNumber::TYPE_CLIENT);
                debug($data);
//				$this->update_auto_accounts($data);
                if (empty($data[$this->alias]['id'])) {
                    $data[$this->alias]['id'] = $this->getLastInsertID();
                }
                if ($clientInsuranceData) {
                    $this->loadModel('ClientInsuranceClass');
                    $clientInsuranceData['ClientInsuranceClass']['client_id'] = $data[$this->alias]['id'];
                    $this->ClientInsuranceClass->deleteAll(array('ClientInsuranceClass.client_id' => $data[$this->alias]['id']), false);
                    $this->ClientInsuranceClass->save($clientInsuranceData);
                }
                return array('status' => true, 'data' => $data[$this->alias]);
            }else{
				return $this->validationErrors;
			}
        }
        return array('status' => false);
    }
	
	//*
	private function removeEmptyClientDetails($clientData)
	{
		foreach($clientData['ClientDetail'] as $k => $clientDetail)
		{
			if(
					empty($clientDetail['first_name']) &&
					empty($clientDetail['last_name']) &&
					empty($clientDetail['email']) &&
					empty($clientDetail['home_phone']) &&
					empty($clientDetail['mobile']) 
					){
				unset($clientData['ClientDetail'][$k]);
			}
		}
		if(empty($clientData['ClientDetail'])){
			unset($clientData['ClientDetail']);
		}
		return $clientData;
	}

    /**
     * Check if a field in current model is unique over current site
     * @param array $param an array with one entry as <code> $field => $value </code>
     * @return boolean true if the field is unqiue, false otherwise
     */
    function isFieldUnique($param) {
        $site_id = getAuthClient('site_id');
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }

        list($field, $value)  =  [key($param), current($param)];
        $conditions = array();
        if (!empty($this->data[$this->alias]['id'])) {
            $conditions[$this->alias . '.id <>'] = $this->data[$this->alias]['id'];
        }

        $conditions[$this->alias . '.site_id'] = $site_id;
        $conditions[$this->alias . ".{$field}"] = $value;
        //		debug($param);
        //		debug($this->hasAny($conditions));

        return !($this->hasAny($conditions));
    }

    function isRequiredEmail() {

        if (empty($this->data[$this->alias]['is_offline']) && empty($this->data[$this->alias]['email'])) {
            return false;
        }
        return true;
    }

    /**
     * Check password confirmation
     * @param array $data
     */
    function checkPasswd($data) {
        if (!empty($this->data[$this->name]['password'])) {

            if ($this->data[$this->name]['password'] != $data['confirm_password']) {
                $this->invalidate('password', __('Passwords not match', true));
                return false;
            }
        }
        return true;
    }
	
	/**
	 * check if the staff has the permissions needed
	 * @return bool
	 */
    //  function getClientsList($site_id = false, $otherConditions = array()) {
    //   if (!check_permission(Clients_View_All_Clients) and check_permission(Clients_View_his_own_Clients)) {
    //       $staff_id = getAuthOwner('staff_id');
    //       $otherConditions[] = '(Client.staff_id = ' . $staff_id . '  OR  Client.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . 1 . ' AND item_staffs.staff_id=' . $staff_id . ' )) ';
    //   } else if (!check_permission(Clients_View_All_Clients) && !check_permission(Clients_View_his_own_Clients))
    //        return array();
    //     $clients = $this->find('list', array('conditions' => $otherConditions, 'fields' => "$this->alias.id, $this->alias.business_name", 'order' => "$this->alias.business_name", 'recursive' => -1));
    //    return $clients;
    //  }
    function global_need_clients() {
        $permissions = [
            Add_New_Expenses, Edit_Delete_all_expenses, Edit_delete_his_own_expenses,
            Add_New_Incomes, Edit_Delete_all_incomes, Edit_delete_his_own_incomes
        ];
        return check_permission($permissions);
    }

    function getClientsList($site_id = false, $otherConditions = array(), $limit = 30, $order = false, $includes = false) {
	    if (!$this->global_need_clients()) {
		    if (!check_permission(Clients_View_All_Clients) and check_permission(Clients_View_his_own_Clients)) {
			    $staff_id = getAuthOwner('staff_id');
			    $otherConditions[] = '(Client.staff_id = ' . $staff_id . '  OR  Client.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . 1 . ' AND item_staffs.staff_id=' . $staff_id . ' ) ' . $this->getPosDefaultClient() . ') ';
		    } else if (!check_permission([Clients_View_All_Clients, Clients_View_his_own_Clients]))
			    return array();
	    }

        if ($order == false) {
            $order = "$this->alias.business_name";
        }

        $fields = "$this->alias.client_number, $this->alias.business_name, $this->alias.id";
        $clientList = [];
        $clients = $this->find(
            'list',
            array(
                'limit' => $limit,
                'conditions' => $otherConditions,
                'fields' => $fields,
                'order' => $order, 'recursive' => -1
            ));

        foreach ($clients as $clientId => $client) {
            $code = key($client);
            $clientList[$clientId] = $client[$code] . ' #' . $code;
        }
        if (is_array($includes) && count($includes) > 0) {
            $clients = $this->find(
                'list',
                array(
                    'limit' => $limit,
                    'conditions' => array('Client.id' => $includes),
                    'fields' => $fields,
                    'order' => $order, 'recursive' => -1
                ));
            foreach ($clients as $clientId => $client) {
                $code = key($client);
                $clientList[$clientId] = $client[$code] . ' #' . $code;
            }
        }

        return $clientList;
    }

    function getClientCount($otherConditions = array()) {
        if (!$this->global_need_clients() and !check_permission(Clients_View_All_Clients) and check_permission(Clients_View_his_own_Clients)) {
            $staff_id = getAuthOwner('staff_id');
            $otherConditions[] = '(Client.staff_id = ' . $staff_id . '  OR  Client.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . 1 . ' AND item_staffs.staff_id=' . $staff_id . ' )) ';
        } else if (!$this->global_need_clients() and !check_permission(Clients_View_All_Clients) && !check_permission(Clients_View_his_own_Clients))
            return 0;

        $count = $this->find('count', array('conditions' => $otherConditions));
        return $count;
    }

    function reload_session($client = false) {
        if (empty($client[$this->name])) {
            $client = $this->findById(getAuthClient('id'));
        }
        $session = new CakeSession;
        $client[$this->name]['client_type'] = $client[$this->name]['type'];
        $client[$this->name]['type'] = 'client';
        return $session->write('CLIENT', $client[$this->name]);
    }

    function ChangePassword($data, $user = null) {
        $this->applyBranch = ['onFind' => false, 'onSave' => false];
        $user = $this->findById(getAuthClient('id'));
        if (empty($data[$this->name]['old_password'])) {
            $this->validationErrors['old_password'] = __('Required', true);
        }
        if (empty($data[$this->name]['password'])) {
            $this->validationErrors['password'] = __('Required', true);
        }
        if (empty($data[$this->name]['psswrd'])) {
            $this->validationErrors['psswrd'] = __('Required', true);
        }
        if ($this->validationErrors) {
            return false;
        }
        if (HashPassword($data[$this->name]['old_password']) != $user[$this->name]['password']) {
            $this->validationErrors['old_password'] = __('Current Password incorrect', true);
            return false;
        }
        if ($data[$this->name]['password'] != $data[$this->name]['psswrd']) {
            $this->validationErrors['psswrd'] = __('Not match', true);
            return false;
        }
        
        $data[$this->name]['id'] = getAuthClient('id');
        $this->applyBranch= ['onFind' => false, 'onSave' => false];
        if ($this->save($data)) {
            return true;
        }
        return false;
    }

    function ChangeEmail($data) {
        $this->applyBranch= ['onFind' => false, 'onSave' => false];
        $user = $this->findById(getAuthClient('id'));

        $client = array();
        $client[$this->name]['id'] = $user[$this->name]['id'];
        $client[$this->name]['email'] = $data[$this->name]['email'];

        if (empty($data[$this->name]['password'])) {
            $this->validationErrors['password'] = __('Required', true);
            return false;
        }

        if (HashPassword($data[$this->name]['password']) != $user[$this->name]['password']) {
            $this->validationErrors['password'] = __('The password is incorrect', true);
            return false;
        }
        if ($this->save($client)) {
            return true;
        }
        return false;
    }

    //------------------------------------
    function check_code_confirmation($data) {




            if (empty($data['email']) || empty($data['code'])) {
                return false;
            }

            $conditions = array();
            $result = array();
            $user = array();
            $conditions['email'] = $data['email'];
            $Staffs = GetObjectOrLoadModel('Staff');
            $user = $Staffs->find('first', array('conditions' => array('Staff.email_address' => $data['email'])));

            $this->loadModel('Site');
            if ($data['email'] == getCurrentSite('email')) {
                $user = $this->Site->find('first', array('conditions' => $conditions, 'recursive' => -1));
                $result = $user['Site'];
                $result['type'] = 'owner';
                $result['staff'] = false;
            } elseif ($user['Staff']['email_address'] == $data['email']) {
                $result = $user['Staff'];
                $result['type'] = 'owner';
                $result['staff'] = true;
            } else {
                $conditions['suspend'] = 0;
                $conditions['site_id'] = getCurrentSite('id');
                $user = $this->find('first', array('conditions' => $conditions, 'recursive' => -1));
                $result = $user['Client'];
                $result['type'] = 'client';
            }
            if (empty($user)) {
                return false;
            }
            $new_code = hash_string($result['first_name'] . $result['email'] . $result['id']);

            if ($new_code == $data['code']) {
                $_SESSION['user_data'] = $result;
                $_SESSION['confirm_code'] = $new_code;
                return true;
            }


        return true;
    }

    //-----------------------------------
    function saveConfirmPassword($data) {

        if (empty($data[$this->name]['password'])) {
            $this->validationErrors['password'] = __('Required', true);
            return false;
        }
        if ($data[$this->name]['password'] != $data[$this->name]['paswrd']) {
            $this->validationErrors['paswrd'] = __('Passwords do not match', true);
            return false;
        }

        $user = $_SESSION['user_data'];

        $modelName = "Client";
        if ($user['type'] == 'owner' and $user['staff'] == 0) {
            $modelName = "Site";
        } elseif ($user['type'] == 'owner' and $user['staff'] == 1) {
            $modelName = "Staff";
        }

        $new_user = array();
        $new_user[$modelName]['id'] = (int)$user['id'];
        $new_user[$modelName]['password'] = $data[$this->name]['password'];
        $new_user[$modelName]['email'] = $user['email'];
        if ($modelName == $this->name) {
            $this->id = $new_user[$modelName]['id'];
            $result = $this->save($new_user,false);
        } elseif ($modelName == "Staff") {
            //$new_user[$modelName]['email_address'] = $new_user[$modelName]['email'];
            unset($new_user[$modelName]['email']);

            $Staffs = GetObjectOrLoadModel('Staff');
            $Staffs->id = $new_user[$modelName]['id'];
            $result = $Staffs->save($new_user,false);
        } else {
            $Staffs = GetObjectOrLoadModel('Staff');
            $Staffs->id = $new_user[$modelName]['id'];
            $model = GetObjectOrLoadModel($modelName);
            $model->id = $new_user[$modelName]['id'];
            $result = $model->save($new_user,false);
            if ($modelName == "Site") {
                $this->loadModel('Staff');
                $this->Staff->changeOwnerUserPassword($data[$this->name]['password']);
            }
        }
        if ($result) {
            unset($_SESSION['user_data']);
            unset($_SESSION['confirm_code']);
            return true;
        }
        return false;
    }

    //----------------------------------

    function validateForgot($data) {
		
        $get_plugin_array = get_plugin_array();
        if (empty($data[$this->name]['email'])) {
            $this->validationErrors['email'] = __('Email is required', true);
        }
	
		if (!empty($this->validationErrors)) {
            return array('success' => false);
        }

        $conditions = array();
        $conditions['lower(email)'] = is_string($data[$this->name]['email']) ? strtolower($data[$this->name]['email']) : $data[$this->name]['email'];


            //$conditions['active'] = 1;
            $this->loadModel('Site');
            $user = $this->Site->find('first', array('conditions' => array('lower(Site.subdomain)'=>getCurrentSite('subdomain'),'lower(Site.email)' => strtolower($data[$this->name]['email'])), 'recursive' => -1));

            if (!empty($user)) {
                $type = 'owner';
                return array('staff' => false, 'type' => $type, 'user' => $user['Site'], 'success' => true);
            }
            // return array('success' => false);


        if (in_array(StaffPlugin, $get_plugin_array)) {

            $conditions['lower(email_address)'] = strtolower($data[$this->name]['email']);
            unset($conditions['lower(email)']);
            unset($conditions['Site.id']);
            $conditions['active'] = 1;
            $Staff = GetObjectOrLoadModel('Staff');

            $StaffRow = $Staff->find('first', array('applyBranchFind'=>false,'conditions' => $conditions, 'recursive' => -1));

            if (!empty($StaffRow)) {
                return array('staff' => true, 'type' => 'owner', 'user' => $StaffRow['Staff'], 'success' => true);
            }

            $conditions['lower(email)'] = isset($conditions['lower(email_address)']) ? $conditions['lower(email_address)'] : $conditions['lower(email)'];
            unset($conditions['lower(email_address)']);
            unset($conditions['active']);
        }

        $conditions['suspend'] = 0;
        $conditions['site_id'] = getCurrentSite('id');
        $user = $this->find('first', array('applyBranchFind'=>false,'conditions' => $conditions, 'recursive' => -1));

        if (!empty($user)) {
            $type = 'client';
            return array('type' => $type, 'user' => $user['Client'], 'success' => true);
        } else {
            $this->validationErrors['email'] = __('Email not found', true);
        }

        return array('success' => false);
    }

    //------------------------------------------
    function checkCaptcha($data) {
        
        return (low($data['security_code']) == low($_SESSION['security_code'])) && !empty($data['security_code']);
    }

    //-----------------------------------
    function getFilters() {
		$ItemsTag = GetObjectOrLoadModel('ItemsTag');
        $filters = array(
            'id' => array('div_class' => 'full-width', 'input_type' => 'advanced_client', 'more-options' => false, 'empty' => __('Any Client', true), 'label' => __('Client', true)),
            'category' => array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker'), 'more-options' => true),
            'created' => array('more-options' => true, 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true)),
            'address' => array('type' => 'like', 'more-options' => true),
            'name' => array('type' => 'like'),
            'postal_code' => array('more-options' => true),
            'country_code' => array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker '), 'more-options' => true, 'label' => __('Country', true), 'empty' => __('[Any Country]', true)),
            'tags' => array('div_class' => 'full-width', 'input_type' => 'tags-multiselect', 'more-options' => true, 'empty' => __('Any tags', true), 'label' => __('Tag', true), 'tag_type' => $ItemsTag::model_name_to_tag_type($this->name)),			
        );

        $client_settings=settings::getPluginValues(ClientsPlugin);


        $plguin = get_plugin_array();
        if (ifPluginActive(StaffPlugin) && check_permission(Clients_View_All_Clients)) {
            $filters['staff_id'] = array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker'), 'more-options' => true, 'label' => __('Added By', true));
        }
        if (ifPluginActive(FollowupPlugin)) {
            $filters['follow_up_status'] = array('div_class' => 'full-width', 'more-options' => true, 'label' => __('Status', true));
        }

            if (empty($client_settings['client_type'])) {
                $filters['type'] = array('div_class' => 'full-width', 'more-options' => true, 'label' => __('Client Type', true));
            }

        if(!empty($client_settings['national_id'])&&$client_settings['national_id'] == 1) {
            $filters['national_id'] = array('type' => 'like', 'options' => array('type' => 'text'), 'more-options' => true, 'label' => __('National ID', true));
        }


        return $filters;
    }

    function getCountryList($conditions = array()) {
        $this->Country=GetObjectOrLoadModel('Country');
        $Countrylist = array();
        $rows = $this->find('all', array('fields' => 'DISTINCT country_code', 'conditions' => $conditions, 'recursive' => -1));
        foreach ($rows as $row) {
            $Countrylist[] = $row[$this->alias]['country_code'];
        }
        
        return $this->Country->getCountryList('code', array('Country.code' => $Countrylist));
        //return $this->Country->find('list', array('fields' => 'code, country', 'conditions' => array('Country.code' => $Countrylist)));
    }

    function getClientEmails($id) {
        $this->recursive = -1;
        $main_email = $this->read(null, $id);
        //debug($main_email);
        $emails[] = $main_email['Client']['first_name'] . ' ' . $main_email['Client']['last_name'] . " <" . $main_email['Client']['email'] . ">";
        $rows = $this->ClientDetail->find('all', array('conditions' => array('ClientDetail.client_id' => $id)));
        foreach ($rows as $row) {
            $emails[] = $row['ClientDetail']['first_name'] . ' ' . $row['ClientDetail']['last_name'] . " <" . $row['ClientDetail']['email'] . ">";
        }

        return $emails;
    }
    function getAllCredit($client_id,$currency_code="")
    {
        $this->loadModel('Invoice');
        $this->Invoice->applyBranch['onFind'] = false;
        $this->loadModel('InvoicePayment');
        $this->InvoicePayment->applyBranch['onFind'] = false;

        $client_id=intval($client_id);
        if($client_id==0){
        return [];
        }
        $this->loadModel('Journal');

        $entity_details['entity_type']='client';
        $entity_details['entity_id']=$client_id;
        $r=$this->Journal->get_auto_account($entity_details);
        if(!empty($currency_code)){
            $more_con=" and currency_code='".$currency_code."'";
        }
        // warning suppress
        $more_con = $more_con ?? '';
        //$client_currencies = $this->InvoicePayment->query("SELECT currency_code FROM `C` where client_id={$client_id} {$more_con} group by currency_code;");
        $union = [];
        $union[] = "SELECT distinct currency_code FROM `invoices` as `C` where client_id={$client_id} {$more_con}  union SELECT distinct currency_code FROM  invoice_payments as `C` where client_id={$client_id} {$more_con} ";
        if (!empty($r['JournalAccount']['id'])) {
        $union[]="union SELECT distinct currency_code FROM `journal_transactions` as `C` WHERE `journal_account_id`={$r['JournalAccount']['id']} {$more_con}";
        $union[]="union SELECT distinct currency_code FROM  expenses as `C` where journal_account_id={$r['JournalAccount']['id']} {$more_con};";
        }
        $client_currencies = $this->query(implode(' ',$union),false);


        foreach ($client_currencies as $key => $currency) {

            $credit = $this->client_credit($client_id, $currency[0]['currency_code']);

            if ($credit > MINIAUTOPAY) {

                $client_currencies[$currency[0]['currency_code']] = $credit;
            }

            unset($client_currencies[$key]);
        }

        return $client_currencies;
    }
    function getOverDue($id, $currency_code = '') {
        $overduelist = array();
        $more_conditions = array();
        $more_invoice_payment_conditions=array();
        if ($currency_code != '') {
            $more_conditions['Invoice.currency_code'] = $currency_code;
            $more_invoice_payment_conditions['InvoicePayment.currency_code'] = $currency_code;
        }
        $starting_balance=$this->InvoicePayment->find('all',array('conditions'=>$more_invoice_payment_conditions+array('InvoicePayment.client_id'=>$id,'InvoicePayment.payment_method'=>'starting_balance')));
        foreach($starting_balance as $b){
        $c=$this->client_credit($id,$b['InvoicePayment']['currency_code']);;    
        if(round($c*-1,9) > 0.00001){
        $overduelist[$b['InvoicePayment']['currency_code']]=$c*-1;
        }
        }
        
        $cj_list=$this->get_client_journals($id);
      
        foreach($cj_list as $cj){
         $c=$this->client_credit($id,$cj[0]['cc']);   
         //To prevent wrong precision comparsion in really high precision values 
         if(round($c*-1,9) > 0.00001){  
        $overduelist[$cj[0]['cc']]=$c*-1;
        }
        }        
        $overdueInvoices = $this->InvoicePayment->Invoice->find('all', array('recursive' => -1, 'conditions' => $more_conditions + array('Invoice.draft <> 1', 'Invoice.type' => [0,16], 'Invoice.client_id' => $id, 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) < CURDATE()', 'OR' => array('Invoice.payment_status NOT ' => array(2,3), 'Invoice.payment_status' => null)), 'fields' => 'Invoice.id, Invoice.no, Invoice.date, Invoice.client_business_name, Invoice.payment_status, Invoice.summary_unpaid, Invoice.currency_code', 'order' => 'Invoice.date DESC'));
        foreach ($overdueInvoices as $odue) {
            $this->loadModel('Invoice');
          //     $refunds=$this->Invoice->get_refund_total($odue['Invoice']['id']);   
            $overduelist[$odue['Invoice']['currency_code']] = $overduelist[$odue['Invoice']['currency_code']] + $odue['Invoice']['summary_unpaid'];
        }

        return $overduelist;
    }

    function getOverDueCount($id, $currency_code = '') {

        $more_conditions = array();
        if ($currency_code != '') {
            $more_conditions['Invoice.currency_code'] = $currency_code;
        }

        $overdueInvoices = $this->InvoicePayment->Invoice->find('count', array('recursive' => -1, 'conditions' => $more_conditions + array('Invoice.draft <> 1', 'Invoice.type' => 0, 'Invoice.client_id' => $id, 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) < CURDATE()', 'OR' => array('Invoice.payment_status !=' => 2, 'Invoice.payment_status' => null)),));
        return $overdueInvoices;
    }

    function getUnpaidCount($id, $currency_code = '') {

        $more_conditions = array();
        if ($currency_code != '') {
            $more_conditions['Invoice.currency_code'] = $currency_code;
        }

        $overdueInvoices = $this->InvoicePayment->Invoice->find('count', array('recursive' => -1, 'conditions' => $more_conditions + array('Invoice.draft <> 1', 'Invoice.type' => 0, 'Invoice.client_id' => $id, 'OR' => array('Invoice.payment_status NOT ' => array(2,3), 'Invoice.payment_status' => null)),));
        return $overdueInvoices;
    }
    

    function getUnpaid($id, $currency_code = '',$overdue_only=false) {
        $more_conditions = array();
        $more_invoice_payment_conditions=array();
        $Openlist = array();
        if ($currency_code != '') {
            $more_conditions['Invoice.currency_code'] = $currency_code;
            $more_invoice_payment_conditions['InvoicePayment.currency_code'] = $currency_code;
        }
         $starting_balance=$this->InvoicePayment->find('all',array('conditions'=>$more_invoice_payment_conditions+array('InvoicePayment.client_id'=>$id,'InvoicePayment.payment_method'=>'starting_balance')));
        
         foreach($starting_balance as $b){
        $c=round($this->client_credit($id,$b['InvoicePayment']['currency_code']),2);
        if($c<0)    
        $Openlist[$b['InvoicePayment']['currency_code']]=$c*-1;
        }
        
        $cj_list=$this->get_client_journals($id);
      
        foreach($cj_list as $cj){
         $c=round($this->client_credit($id,$cj[0]['cc']),2);
         if($c<0){    
        $Openlist[$cj[0]['cc']]=$c*-1;
        }
        }
//         print_r($Openlist);
//        die(); 
        $OpenInvoices = $this->InvoicePayment->Invoice->find('all', array('recursive' => -1, 'conditions' => $more_conditions + array('Invoice.draft <> 1', 'Invoice.type' => [0,16], 'Invoice.client_id' => $id, 'OR' => array('Invoice.payment_status NOT' => array(2,3), 'Invoice.payment_status' => null)), 'fields' => 'Invoice.id, Invoice.no, Invoice.date, Invoice.client_business_name, Invoice.payment_status, Invoice.summary_unpaid,Invoice.summary_total, Invoice.currency_code', 'order' => 'Invoice.date DESC'));
        foreach ($OpenInvoices as $oinvoice) {
                $this->loadModel('Invoice');
               //$refunds=$this->Invoice->get_refund_total($oinvoice['Invoice']['id']);
            $Openlist[$oinvoice['Invoice']['currency_code']] = $Openlist[$oinvoice['Invoice']['currency_code']] + $oinvoice['Invoice']['summary_unpaid'];
        }

        return $Openlist;
    }

    function before_import($data) {

		App::import('Vendor', 'auto_number');
        if (empty($data[$this->alias]['id']) && empty($data[$this->alias]['client_number'])) {
            $data[$this->alias]['client_number'] = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_CLIENT);
			$data[$this->alias]['_is_number_generated'] = true;
        }
        if (empty($data[$this->alias]['email'])) {
            $data[$this->alias]['is_offline'] = 1;
        }

        if (!empty($data[$this->alias]['bn1'])) {
            $data[$this->alias]['bn1_label'] = getCurrentSite('bn1_label');
        }

        if (!empty($data[$this->alias]['bn2'])) {
            $data[$this->alias]['bn2_label'] = getCurrentSite('bn2_label');
        }
        if (trim($data[$this->alias]['group_price_id']) != "") {
            $GroupPrice = GetObjectOrLoadModel('GroupPrice');
            $find_project = $GroupPrice->getGroupPriceID($data[$this->alias]['group_price_id']);

            if (isset($find_project['id'])) {
                $data[$this->alias]['group_price_id'] = $find_project['id'];
            } else {
                $data[$this->alias]['group_price_id'] = "";
            }
        } else {
            $data[$this->alias]['group_price_id'] = "";
        }
     
        $have_custom_fields = false;
        $have_le_fields = false;
        foreach ($data['Client'] as $k => $v) {
            $fieldKey = explode('.', $k);
            if (strpos($fieldKey[0], "field_") !== false) {
                $data['clients_custom_data'][$k] = $v;
                $have_custom_fields = true;
                unset($data['Client'][$k]);
            }

            if (strpos ($k , '.') !== false) {
                $appEntityKey = $fieldKey[0];
                $fieldDbName = $fieldKey[1];
                $data[$appEntityKey][$fieldDbName] = $v;
                $have_le_fields = true;
            }
        }

        if ($have_custom_fields) {
            $data['CustomTable'] = 'clients_custom_data';
            $data['CustomTableId'] = 'client_id';
        }

        if ($have_le_fields) {
            $data['entity_key'] = EntityKeyTypesUtil::CLIENT_ENTITY_KEY;
            $additionalFormsHandler =
                new \App\Services\LocalEntityForm\AdditionalFormsHandler(
                    EntityKeyTypesUtil::CLIENT_ENTITY_KEY
                );
            if ($data['Client']['id']) {
                $data[$additionalFormsHandler->getCustomEntityKey()]['id'] = $additionalFormsHandler->getCustomDataRecord($data['Client']['id'])->id;
            }
            return array_merge($data, $additionalFormsHandler->formatForImport($data));
        }
        if ($data['Client']['category'] && is_string($data['Client']['category'])) {
            $this->loadModel('Category');
            $categoryCriteria = [
                'name' => $data['Client']['category'],
                'description' => $data['Client']['category'],
                'category_type' => 4,
            ];
            $categoryData = ['Category' => $categoryCriteria ];
            $this->Category->disableBranchFind();
            $category = $this->Category->find('first', ['conditions' => $categoryCriteria]);
            $category_id = $category['Category']['id'];
            if (empty($category_id)) {
                $this->Category->save($categoryData);
                $category_id = $this->Category->getLastInsertID();
            }
            $data['Client']['category_id'] = $category_id;
        }

        return $data;
    }

    function check_import($data) {
        $site = getCurrentSite();
        $this->loadModel('Site');
        if (checkIfLimitExistsV2($site['id'], LimitationUtil::CLIENTS_COUNT)) {
            $result = checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::CLIENTS_COUNT);
            if($result['status']) {
                return ['status' => true];
            }else{
                return ['status' => false, 'message' => sprintf(__($result['message'], true), __($result['title'], true))];
            }
        }else{
            $result = $this->Site->check_add_client_limit($site['id']);
        }
        if (!isset($data['entity_key'])) {
            return $result;
        }

        $additionalFormsHandler =
            new \App\Services\LocalEntityForm\AdditionalFormsHandler(
                $data['entity_key']
            );

        $isValid = $additionalFormsHandler->validate($data);

        if (!$isValid) {
            $errors = $additionalFormsHandler->getValidationErrors();

            $_errors = [];

            foreach ($errors as $key => $value) {
                $_errors[] = $key . ' '. implode(', ', $value);
            }

            return ['message' => implode(', ', $_errors)];
        }

        return $result;
    }

	function after_import_save($data) {
        $this->loadModel('ItemStaff');
        $addedClientId = $this->id;
		if(!empty($data[$this->alias]['starting_balance'])){

            // used to check client currency . 
            $client = $this->find('first', array('conditions' => array('Client.id' => $this->id), 'recursive' => -1));
            $currency = getCurrentSite('currency_code');
            if($client['Client']['default_currency_code']) $currency = $client['Client']['default_currency_code'];
 
            //  $data[$this->alias]['starting_balance'] casted to float as a failsafe solution for php8
		$res=$this->addOpenBalance($this->id,(float)$data[$this->alias]['starting_balance'] * -1, getAuthOwner('staff_id'), $currency,$data[$this->alias]['starting_balance_date']);

		}
		App::import('Vendor', 'auto_number');
		if(!empty($data[$this->alias]['_is_number_generated'])) \AutoNumber::update_auto_serial(\AutoNumber::TYPE_CLIENT);
		elseif(!empty($data[$this->alias]['client_number'])) \AutoNumber::update_last_from_number($data[$this->alias]['client_number'], \AutoNumber::TYPE_CLIENT);

        /**
         * Assign the client to the current staff id
         * @requested
         */
        if(getAuthOwner('staff_id') !== 0 && !isSuperAdmin()) {
            $this->ItemStaff->assign_staff_members([getAuthOwner('staff_id')],$addedClientId , 1);
        }

	}


    function deleteOpenBalance($id)
    {

        $this->loadModel('InvoicePayment');
        $current_alies=$this->InvoicePayment->alias;
        $this->InvoicePayment->alias='InvoicePayment';
        $this->loadModel('Invoice');
        $invoicePayment = $this->InvoicePayment->find(array('InvoicePayment.payment_method' => 'starting_balance','InvoicePayment.id' => $id));
        if (empty($invoicePayment)) {
            $this->InvoicePayment->alias=$current_alies;
          return  array('status' => false);
        }
        $result=$this->InvoicePayment->delete($id);
        if($result){
         $this->InvoicePayment->alias=$current_alies;
        $this->add_actionline(ACTION_DELETE_CLIENT_OPENING_BALANCE, array('primary_id' => $invoicePayment['InvoicePayment']['id'], 'secondary_id' => $invoicePayment['InvoicePayment']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['InvoicePayment']['status'],'param2' => $invoicePayment['Client']['business_name'],'param4' => $invoicePayment['Client']['client_number']));
        $this->adjust_and_pay($invoicePayment['InvoicePayment']['client_id'],$invoicePayment['InvoicePayment']['currency_code']);
        return array('status' => true,'data'=>array('client_id'=>$invoicePayment['InvoicePayment']['client_id']));
        }else{
            $this->InvoicePayment->alias=$current_alies;
            return array('status' => false);
        }
    }

    function addOpenBalance($client_id,$balance,$staff_id,$currency_code,$date = null)
    {
        $this->loadModel('InvoicePayment');
        $this->loadModel('Invoice');
        $row = $this->InvoicePayment->find(array('InvoicePayment.payment_method' => 'starting_balance','InvoicePayment.currency_code' => $currency_code,'InvoicePayment.client_id' => $client_id));
        $data['InvoicePayment']['client_id'] = $client_id;
        $data['InvoicePayment']['amount'] = $balance;
        $data['InvoicePayment']['status'] = 1;
        $data['InvoicePayment']['payment_method'] = 'starting_balance';
        $data['InvoicePayment']['staff_id'] = $staff_id;
        $data['InvoicePayment']['added_by'] = 0;
        $data['InvoicePayment']['date'] = empty($date) ? date('Y-m-d') : $this->formatDate($date);
        if(empty($data['InvoicePayment']['date'])||strtotime($data['InvoicePayment']['date'])<strtotime('1970-01-02')) $data['InvoicePayment']['date']= date('Y-m-d') ;
        $data['InvoicePayment']['manual_payment'] = 1;
        $data['InvoicePayment']['currency_code'] = $currency_code;
        $this->InvoicePayment->validationErrors=[];
        if ($row) {
            $data['InvoicePayment']['id'] = $row['InvoicePayment']['id'];
            return $this->InvoicePayment->editPayment($data);
        } else {
            return $this->ownerAddPayment($data);
        }
    }
    function getHash($clientid) {
        $client = $this->read(NULL, $clientid);
        return md5($client['Client']['id'] . '#' . $client['Client']['client_number'] . '#' . $client['Client']['created']);
    }

    function getCategoriesList($numeric_index = true,$get_only_used=false) {
        $ret = array();
        $ret2 = array();
        
        $this->loadModel('Category');
        $query = 'SELECT  categories.name FROM categories WHERE';
        if ($get_only_used) {
            $query .= ' categories.name COLLATE utf8_unicode_ci IN   (SELECT  `category` FROM `clients` WHERE category IS NOT NULL AND category <>"" GROUP BY `category` ORDER BY `category`) AND ';
        }

        $query .= ' categories.category_type = 4 ORDER BY categories.id ASC';
        $list = $this->Category->query($query);
        foreach ($list as $l)
            if (!empty($l['categories']['name']) && !in_array($l['categories']['name'], $ret)) {
                $ret[] = $l['categories']['name'];
                $ret2[$l['categories']['name']] = $l['categories']['name'];
            }
        if ($numeric_index) {
            return $ret;
        }
        return $ret2;
    }

    function getCategoriesListWithIds($get_only_used = false) {
        $ret = array();

        $this->loadModel('Category');
        $query = 'SELECT `name`, `id` FROM categories WHERE';
        if ($get_only_used) {
            $query .= ' categories.name COLLATE utf8_unicode_ci IN   (SELECT  `category` FROM `clients` WHERE category IS NOT NULL AND category <>"" GROUP BY `category` ORDER BY `category`) AND ';
        }
        $query .= ' category_type = 4  ORDER BY id ASC';
        
        $list = $this->Category->query($query);

        foreach ($list as $l) {
            if (!empty($l['categories']['name']) && !in_array($l['categories']['name'], $ret)) {
                $ret[$l['categories']['id']] = $l['categories']['name'];
            }
        }
        return $ret;
    }

    function ownerAddPayment($data) {

        $client = $this->find('first', array('conditions' => array('Client.id' => $data['InvoicePayment']['client_id']), 'recursive' => -1));

        if (empty($data['InvoicePayment']['amount']) || !is_numeric($data['InvoicePayment']['amount'])) {
            $this->InvoicePayment->validationErrors['amount'] = __('Invalid amount', true);
        }

        if (isset($data['InvoicePayment']['status']) && $data['InvoicePayment']['status'] == '') {
            $this->InvoicePayment->validationErrors['status'] = __('Required', true);
        } elseif (isset($data['InvoicePayment']['status']) && $data['InvoicePayment']['status'] != '') {
            $statuses = InvoicePayment::getPaymentStatus();
            if (!isset($statuses[$data['InvoicePayment']['status']])) {
                $this->InvoicePayment->validationErrors['status'] = __('Invalid status', true);
            }
        }

        if (empty($data['InvoicePayment']['payment_method'])) {
            $this->InvoicePayment->validationErrors['payment_method'] = __('Required', true);
        }
        if (empty($data['InvoicePayment']['staff_id']) and $data['InvoicePayment']['staff_id'] != 0) {
            $this->InvoicePayment->validationErrors['staff_id'] = __('Required', true);
        }
		$empty_date_message = $this->validateIsOpenedPeriod($data['InvoicePayment']['date']);
//		debug($empty_date_message);
        if ( $empty_date_message !== true) {
//			debug($empty_date_message);
            $this->InvoicePayment->validationErrors['date'] = $empty_date_message;
        }
//				die(debug($data));

		
//        else {
//            $methods = InvoicePayment::getPaymentMethods();
//            if (!isset($methods[$data['InvoicePayment']['payment_method']])) {
//                $this->InvoicePayment->validationErrors['payment_method'] = __('Payment method not supported', true);
//            }
//        }

        if (strlen($data['InvoicePayment']['notes']) > 500) {
            $this->InvoicePayment->validationErrors['notes'] = __('Only 500 characters allowed', true);
        }

        if ($data['InvoicePayment']['client_pay'] != 1) {
            $fields = array('email', 'first_name', 'last_name', 'address1', 'address2', 'city', 'state', 'postal_code', 'country_code');

            foreach ($fields as $field) {
                $data['InvoicePayment'][$field] = $client['Client'][$field];
            }
            $data['InvoicePayment']['added_by'] = 1;
        }else{
        $data['InvoicePayment']['added_by'] = 0;
        }
        //  $data['InvoicePayment']['currency_code'] = $client['Client']['default_currency_code'];
        if (empty($data['InvoicePayment']['date'])) {
            $data['InvoicePayment']['date'] = date('Y-m-d');
        } else {
            $data['InvoicePayment']['date'] = $this->formatDate($data['InvoicePayment']['date']);
        }
        if (empty($data['InvoicePayment']['ip'])) {
            $data['InvoicePayment']['ip'] = get_real_ip();
        }
        App::import('Vendor', 'AutoNumber');
        $data['InvoicePayment']['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);

        if($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && ($data['InvoicePayment']['payment_method'] == "tamara")) {
            $tamaraFactory = new \App\Services\PaymentGateways\Tamara\IntegrationFactory();
            $phoneNumber = $_POST['tamaraPhone'];
            if (isset($_POST['data']['InvoicePayment']['phone1']) && !isset($_POST['tamaraPhone'])){
                $phoneNumber = $_POST['data']['InvoicePayment']['phone1'];
            }
            $client_data = $this->find("first", ['conditions' =>['Client.id'=>$data["InvoicePayment"]["client_id"]]]);
            $response =  $tamaraFactory->createCheckout($client_data, $data, $phoneNumber);

            if ($response['response']['order_id']){
                $paymentData['transaction_id'] = $response['response']['order_id'];
                $paymentData['status'] = PAYMENT_STATUS_PENDING;
                $paymentData['email'] = $client_data['Client']['email'];
                $paymentData['payment_method'] = 'tamara';
                $paymentData['client_id'] = $client_data['Client']['id'];
                $paymentData['amount'] = $data['InvoicePayment']['amount'];
                $paymentData['currency_code'] = $data['InvoicePayment']['currency_code'];
                $paymentData['first_name'] = $client_data['Client']['first_name'];
                $paymentData['last_name'] = $client_data['Client']['last_name'];
                $paymentData['address1'] = $client_data['Client']['address1'];
                $paymentData['address2'] = $client_data['Client']['address2'];
                $paymentData['city'] = $client_data['Client']['city'];
                $paymentData['state'] = $client_data['Client']['state'];
                $paymentData['postal_code'] = $client_data['Client']['postal_code'];
                $paymentData['country_code'] = $client_data['Client']['country_code'];
                $paymentData['phone1'] = $client_data['Client']['phone1'];
                $paymentData['source'] = 'owner_add_payment';
                $paymentData['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);;
                $this->InvoicePayment->create();
                $this->InvoicePayment->alias = 'InvoicePayment';
                $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
                return array('status' => true, 'out' => $response['out'], 'url' => $response['url'], 'payment_id' => $paymentData['id']);
            } else {
                return array('status' => false, 'out' => false, 'error_message' => $response['error_message']);
            }

        }else if($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && ($data['InvoicePayment']['payment_method'] == "tabby")) {
            $paymentClass = getPaymentClass($data['InvoicePayment']['payment_method']);
            $paymentClassString = $paymentClass;
            $PaymentGateway = ClassRegistry::init('SitePaymentGateway');
            $pg = $PaymentGateway->find(array('payment_gateway' => $data['InvoicePayment']['payment_method']), null, null, -1);
            if (!empty($pg['SitePaymentGateway']['manually_added']))
                $paymentClass = getPaymentClass('offline');
            else if (!class_exists($paymentClass))
                $paymentClass = getPaymentClass('offline');
            $paymentMethod = new $paymentClass;

            $this->InvoicePayment->save($data);
            $order_id = $this->InvoicePayment->getLastInsertID();

            if ($paymentMethod->isOut()) {
                /* @var $PaymentGateway SitePaymentGateway */
                $userOptions = $pg['SitePaymentGateway'];
                $paymentData = $data['InvoicePayment'];

                if (isset($_GET['source']) && $_GET['source'] == 'website_front') {
                    $paymentData['source'] = 'website_front';
                } elseif (!empty($_GET['webfront_reservation'])) {
                    $paymentData['source'] = 'webfront_reservation';
                } else {
                    $paymentData['source'] = 'client_add_payment';
                }
                $invoice = $this->find('first', ['conditions' => ['Invoice.id' => $paymentData['invoice_id']], 'recursive' => 1]);
                $paymentData['invoice'] = $invoice['Invoice'];
                $paymentData['invoiceItems'] = $invoice['InvoiceItem'];
                $paymentData['client'] = $invoice['Client'];

                $paymentData['return'] = Router::url(array('controller' => 'invoice_payments', 'action' => 'view', $order_id), true);
                $paymentData['notify_url'] = Router::url("/invoice_payments/payment_ipn/$order_id", true);
                $paymentData['source'] = "client";

                if (isset($data['_Token']['key'])) {
                    $paymentData['_token_key'] = $data['_Token']['key'];
                }
                $paymentData['id'] = $order_id;
                $paymentData['source'] = "client_add_payment_credit";
                $url = $paymentMethod->getPaymentURL($userOptions, $paymentData);

                if (isset($url['web_url'])) {
                    if (method_exists($paymentMethod, 'getTransactionRef')) { // todo implement in all payment methods using interface

                        $updatedData = ['transaction_id' => $paymentMethod->getTransactionRef()];

                        if ($data['InvoicePayment']['payment_method'] == 'tabby') {
                            $updatedData['status'] = 2;
                        }

                        $this->InvoicePayment->save($updatedData, ['validate' => false, 'fieldList' => null]);
                    }

                    return array('status' => true, 'url' => $url, 'data' => $data, 'out' => true);
                } elseif (in_array($paymentClassString,['PayTabsPayment2','PayPalPaymentV2', 'TapPayment', 'PaymobPayment', "PayTabsPayment"])) {
                    $data['errorMsg'] = $paymentMethod->errorMsg ?? '' . (!empty($paymentMethod->rejectedFields)  ?'rejected fields: '.implode(', ', $paymentMethod->rejectedFields) : '');
                }
                if (isset($url['status']) && !$url['status']){

                    $data['errorMsg'] = $url['errorMsg'];
                }
                return array('status' => false, 'url' => false, 'data' => $data,'validationErrors'=>$this->InvoicePayment->validationErrors);
            } else {

                $result = $paymentMethod->processResult($data);
                $data['InvoicePayment']['status'] = $result['data']['status'];
                if (empty($data['InvoicePayment']['date'])) {
                    $data['InvoicePayment']['date'] = date('Y-m-d');
                } else {
                    $data['InvoicePayment']['date'] = $this->formatDate($data['InvoicePayment']['date']);
                }
                if(!empty($result['data']['transaction_id'])){
                    $data['InvoicePayment']['transaction_id'] = $result['data']['transaction_id'];
                }
                unset($data['InvoicePayment']['attachment']);
                $this->InvoicePayment->save($data, false, array('id', 'status', 'transaction_id'));
                if(!$result['data']['status']){
                    return array('status' => false, 'out' => false, 'data' => $result);
                }
                return array('status' => true, 'out' => false, 'data' => $data);
            }

            $tamaraFactory = new \App\Services\PaymentGateways\Tamara\IntegrationFactory();
            $phoneNumber = $_POST['tamaraPhone'];
            if (isset($_POST['data']['InvoicePayment']['phone1']) && !isset($_POST['tamaraPhone'])){
                $phoneNumber = $_POST['data']['InvoicePayment']['phone1'];
            }
            $client_data = $this->find("first", ['conditions' =>['Client.id'=>$data["InvoicePayment"]["client_id"]]]);
            $response =  $tamaraFactory->createCheckout($client_data, $data, $phoneNumber);

            if ($response['response']['order_id']){
                $paymentData['transaction_id'] = $response['response']['order_id'];
                $paymentData['status'] = PAYMENT_STATUS_PENDING;
                $paymentData['email'] = $client_data['Client']['email'];
                $paymentData['payment_method'] = 'tamara';
                $paymentData['client_id'] = $client_data['Client']['id'];
                $paymentData['amount'] = $data['InvoicePayment']['amount'];
                $paymentData['currency_code'] = $data['InvoicePayment']['currency_code'];
                $paymentData['first_name'] = $client_data['Client']['first_name'];
                $paymentData['last_name'] = $client_data['Client']['last_name'];
                $paymentData['address1'] = $client_data['Client']['address1'];
                $paymentData['address2'] = $client_data['Client']['address2'];
                $paymentData['city'] = $client_data['Client']['city'];
                $paymentData['state'] = $client_data['Client']['state'];
                $paymentData['postal_code'] = $client_data['Client']['postal_code'];
                $paymentData['country_code'] = $client_data['Client']['country_code'];
                $paymentData['phone1'] = $client_data['Client']['phone1'];
                $paymentData['source'] = 'owner_add_payment';
                $paymentData['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);;
                $this->InvoicePayment->create();
                $this->InvoicePayment->alias = 'InvoicePayment';
                $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
                return array('status' => true, 'out' => $response['out'], 'url' => $response['url'], 'payment_id' => $paymentData['id']);
            } else {
                return array('status' => false, 'out' => false, 'error_message' => $response['error_message']);
            }

        }else if ($data['InvoicePayment']['client_pay'] == 1) {
            $data['InvoicePayment']['status'] = 0;
            $this->set($data);
            $data=$this->InvoicePayment->SetTreasuryForPayment($data);
            if ($this->InvoicePayment->save($data)) {
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
                $order_id = $this->InvoicePayment->getLastInsertID();
                $data['InvoicePayment']['id'] = $data['InvoicePayment']['order_id'] = $order_id;



                $paymentClass = getPaymentClass($data['InvoicePayment']['payment_method']);
//			App::import('Vendor', 'PaypalPayment', array('file' => 'payments/PaypalPayment.php'));
                $PaymentGateway = GetObjectOrLoadModel('SitePaymentGateway');
                $pg = $PaymentGateway->find(array('payment_gateway' => $data['InvoicePayment']['payment_method']), null, null, -1);

                if (!empty($pg['SitePaymentGateway']['manually_added']))
                    $paymentClass = getPaymentClass('offline');
                else if (!class_exists($paymentClass))
                    $paymentClass = getPaymentClass('offline');
                $paymentMethod = new $paymentClass;

                if ($paymentMethod->isOut()) {

                    /* @var $PaymentGateway SitePaymentGateway */

                    $userOptions = $pg['SitePaymentGateway'];

                    $paymentData = $data['InvoicePayment'];
                    $paymentData['return'] = Router::url(array('controller' => 'invoice_payments', 'action' => 'view', $order_id), true);
                    $paymentData['notify_url'] = Router::url("/invoice_payments/payment_ipn/$order_id", true);
                    $url = $paymentMethod->getPaymentURL($userOptions, $paymentData);

                    if ($url) {
                        return array('status' => true, 'url' => $url, 'data' => $data, 'out' => true);
                    }
                } else {
                    $result = $paymentMethod->processResult($data);
                    $data['InvoicePayment']['status'] = $result['data']['status'];
                    if (empty($data['InvoicePayment']['date'])) {
                        $data['InvoicePayment']['date'] = date('Y-m-d');
                    } else {
                        $data['InvoicePayment']['date'] = $this->formatDate($data['InvoicePayment']['date']);
                    }
                    if (!empty($result['data']['transaction_id'])) {
                        $data['InvoicePayment']['transaction_id'] = $result['data']['transaction_id'];
                    }
                    $this->InvoicePayment->save($data, false, array('id', 'status', 'transaction_id'));
                    if($result['data']['status']){
                        if($data['InvoicePayment']['payment_method'] == 'stripe' && $result['data']['status'] == PAYMENT_STATUS_PENDING){
                            return array('status' => true, 'out' => false, 'client_secret' => $result['data']['client_secret'], 'public_key' => $result['data']['public_key']);
                        }
                        return array('payment_id' => $this->InvoicePayment->id, 'status' => true, 'out' => false, 'data' => $data);
                    }else{
                        return array('payment_id' => $this->InvoicePayment->id, 'status' => false, 'out' => false, 'error_message' => $result['errorMsg'] ?? $result['data']['errorMsg']);
                    }
                }
            } else {
                
            }
        }
	
	    if ($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && $data['InvoicePayment']['payment_method'] == "paytabs") {
		    App::import('Vendor', 'PayTabsPayment', array('file' => 'payments/PayTabsPayment.php'));
		    $PaymentGateway = ClassRegistry::init('SitePaymentGateway');
		    $pg = $PaymentGateway->find(array('payment_gateway' => $data['InvoicePayment']['payment_method']), null, null, -1);
		    $userOptions = $pg['SitePaymentGateway'];
		    $paymentData = $data;
		    $client = $this->find('first', array('conditions' => array('Client.id' => $paymentData['client_id'])));
		
		    $paymentData['status'] = 0;
		    $paymentData['email'] = $client['Client']['email'];
		    $paymentData['source'] = 'owner_add_client_credit';
		    $this->InvoicePayment->create();
		    $this->InvoicePayment->alias = 'InvoicePayment';
		    $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
		    $paymentData['id'] = $this->InvoicePayment->id;
		    $paymentData['client'] = $client['Client'];
		
		    $paymentMethod = new PayTabsPayment();
		    $url = $paymentMethod->getPaymentURL($userOptions, $paymentData);
		    if ($url) {
			    return array('out' => true, 'url' => $url);
		    } else {
                if (!empty($paymentMethod->rejectedFields))
                    $errorMessage = "Please update client information before proceeding to payment. (" . implode(',', $paymentMethod->rejectedFields) . ")";
                else
                    $errorMessage = "Error Creating Paytabs Payment Page. " . $paymentMethod->errorMsg;
                
			    return array('status' => false, 'error_message' => $errorMessage, 'payment_id' => $paymentData['id']);
		    }
	    }
	    
        if($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && in_array($data['InvoicePayment']['payment_method'],['paytabs2','paymob','securepay','tap','paypalV2','square', 'paymob2', 'stripe'])){
            $paymentData = $data['InvoicePayment'];
            $client = $this->find('first', array('conditions' => array('Client.id' => $paymentData['client_id'])));

            $paymentData['status'] = 0;
            $paymentData['email'] = $client['Client']['email'];
            $paymentData['source'] = 'owner_add_client_credit';
            $this->InvoicePayment->create();
            $this->InvoicePayment->alias = 'InvoicePayment';
            $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
            $paymentData['id'] = $this->InvoicePayment->id;
            $paymentData['client'] = $client['Client'];

            $paymentClass = getPaymentClass($data['InvoicePayment']['payment_method']);
            $paymentClassString = $paymentClass;

            $PaymentGateway = ClassRegistry::init('SitePaymentGateway');
            $pg = $PaymentGateway->find(array('payment_gateway' => $data['InvoicePayment']['payment_method']), null, null, -1);
            if (!empty($pg['SitePaymentGateway']['manually_added']))
            $paymentClass = getPaymentClass('offline');
            else if (!class_exists($paymentClass))
                $paymentClass = getPaymentClass('offline');
            $paymentMethod = new $paymentClass;
            if ($paymentMethod->isOut()) {
                $userOptions = $pg['SitePaymentGateway'];
                $url = $paymentMethod->getPaymentURL($userOptions, $paymentData);
                if ($url) {
                    if (method_exists($paymentMethod, 'getTransactionRef')) { // todo implement in all payment methods using interface
                        $this->InvoicePayment->save(['transaction_id' => $paymentMethod->getTransactionRef()], array('validate' => false, 'fieldList' => null));
                    }
                    return array('status' => true, 'url' => $url, 'out' => true);
                }
                return array('status' => false, 'error_message' => $paymentMethod->errorMsg);
            } else {
                $result = $paymentMethod->processResult($paymentData);
                $paymentData['status'] = $result['data']['status'];
                if (empty($paymentData['date'])) {
                    $paymentData['date'] = date('Y-m-d');
                } else {
                    $paymentData['date'] = $this->formatDate($paymentData['date']);
                }
                if (!empty($result['data']['transaction_id'])) {
                    $paymentData['transaction_id'] = $result['data']['transaction_id'];
                }
                $this->InvoicePayment->save($paymentData, false, array('id', 'status', 'transaction_id'));
                if (!$result['data']['status']) {
                    return array('status' => false, 'out' => false, 'error_message' => $paymentMethod->errorMsg);
                }
                if($data['InvoicePayment']['payment_method'] == 'stripe' && $result['data']['status'] == PAYMENT_STATUS_PENDING){
                    return array('status' => true, 'out' => false, 'client_secret' => $result['data']['client_secret'], 'public_key' => $result['data']['public_key']);
                }
                return array('status' => true, 'out' => false);
            }
        }

        if (empty($this->InvoicePayment->validationErrors)) {
            $this->InvoicePayment->create(false);

            // append client phone1 , phone2
            if ($data['InvoicePayment']['client_pay'] == 0) {
                $fields = array('first_name', 'last_name', 'address1', 'address2', 'city', 'state', 'postal_code', 'country_code');

                foreach ($fields as $field) {
                    $data['InvoicePayment'][$field] = $client['Client'][$field];
                }

                $data["InvoicePayment"]["phone1"] = $client["Client"]["phone1"];
            }
            $data["InvoicePayment"]["phone2"] = $client["Client"]["phone2"];

            $this->loadModel("Invoice");

            $first_unpaid_invoice=$unpaid_invoice = $this->Invoice->find('first', array('order'=>'Invoice.date asc,Invoice.id asc','conditions' => array('currency_code'=>$data['InvoicePayment']['currency_code'],'payment_status!=2', 'Invoice.type' => 0, 'Invoice.client_id' => $client['Client']['id'], 'Invoice.draft <> 1')));
            
            if($data['InvoicePayment']['payment_method']!="starting_balance" && $$first_unpaid_invoice && $data["InvoicePayment"]['amount']<=$first_unpaid_invoice['Invoice']['summary_unpaid']){
        
                    $data["InvoicePayment"]["invoice_id"]=$first_unpaid_invoice['Invoice']['id'];
                    unset($data["InvoicePayment"]["client_id"]);
                    $update_invoice=true;
        
            }

            $data=$this->InvoicePayment->SetTreasuryForPayment($data);
            if ($row = $this->InvoicePayment->save($data, array('validate' => false))) {
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
                if(isset($update_invoice)){
                    $this->Invoice->updateInvoicePayments($data["InvoicePayment"]["invoice_id"]);
                }
                return array('convert_to_invoice_payment'=>isset($update_invoice),'payment_id' => $this->InvoicePayment->id, 'status' => true,'data'=>$data);
            } else {
            return array('convert_to_invoice_payment'=>false,'status' => false, 'here' => $this->InvoicePayment->validationErrors);
            }
        }
        
		if(!empty($this->InvoicePayment->validationErrors)){
			return ['errors' => $this->InvoicePayment->validationErrors];
		}
        
    }

    function balance_due($client_id, $currency_code) {
        if(empty($client_id)||empty($currency_code)) return;
        $this->loadModel("Invoice");
        $this->Invoice->recursive = -1;

        $total_amount = $this->Invoice->find('first', array('fields' => 'sum(summary_total) as total_amount', 'conditions' => array('Invoice.type=0', 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1', 'Invoice.currency_code' => $currency_code)));
        $total_cn = $this->Invoice->find('first', array('fields' => 'sum(summary_total) as total_cn', 'conditions' => array('Invoice.type' => Invoice::Credit_Note, 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1', 'Invoice.currency_code' => $currency_code)));
        $total_payment = $this->InvoicePayment->query("SELECT sum(IF(amount<0,amount*-1,amount)) as total_paid FROM `invoice_payments` WHERE status=1 and (client_id={$client_id} or invoice_id in (select id from invoices where client_id={$client_id} and type  IN( 0 ,6) and draft <>1)) and payment_method <> 'client_credit' and currency_code='{$currency_code}'", false);

        return $total_amount[0]['total_amount'] - $total_cn[0]['total_cn'] - $total_payment[0][0]['total_paid'];
    }
    
    function overpaid($client_id, $currency_code) {
        $more_conditions=array();
    if(Domain_Short_Name=="onlineinvoices.com"){
     $more_conditions['Invoice.date > ']="2018-06-11";
    }
        $this->loadModel("Invoice");
        $this->loadModel("InvoicePayment");
        $this->Invoice->recursive = -1;
        $total_amount = $this->Invoice->find('first', array('fields' => 'sum(summary_total-summary_paid-COALESCE(summary_refund,0)) as total_amount', 'conditions' => $more_conditions+array('Invoice.payment_status in ('.INVOICE_STATUS_PAID.','.INVOICE_STATUS_REFUNDED.')','Invoice.type=0', 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1', 'Invoice.currency_code' => $currency_code)));
        return $total_amount[0]['total_amount']*-1;
    }

    function overpaid_local_currency($client_id) {
        $more_conditions=array();
        if(Domain_Short_Name=="onlineinvoices.com"){
            $more_conditions['Invoice.date > ']="2018-06-11";
        }
        $this->loadModel("Invoice");
        $this->loadModel("InvoicePayment");
        $this->Invoice->recursive = -1;
        $journalJoin = [
            [
                'table' => 'journals',
                'alias' => 'Journal',
                'type' => 'left',
                'conditions' => [
                    'Journal.entity_type = "invoice" AND Journal.entity_id = Invoice.id'
                ]
            ]
        ];
        $total_amount = $this->Invoice->find('first', array('joins' => $journalJoin, 'fields' => ['sum((summary_total-summary_paid-COALESCE(summary_refund,0)) * Journal.currency_rate) as total_amount'], 'conditions' => $more_conditions+array('Invoice.payment_status in ('.INVOICE_STATUS_PAID.','.INVOICE_STATUS_REFUNDED.')','Invoice.type=0', 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1', 'abs(summary_total-summary_paid-COALESCE(summary_refund,0)) >'.MINOVERPAID)));
        return $total_amount[0]['total_amount']*-1;

    }



    function client_credit($client_id, $currency_code) {
        if(empty($client_id) || empty($currency_code)){
            return 0;
        }
        	//starting_balance
        $this->loadModel("InvoicePayment");
        $this->loadModel("Invoice");
        $this->InvoicePayment->alias='InvoicePayment';
        $this->Invoice->recursive = -1;

        // Starting Balance
        $this->InvoicePayment->applyBranch['onFind']= false;
        $total_sb = $this->InvoicePayment->find('first', array('fields' => 'amount', 'conditions' => array('InvoicePayment.payment_method'=>'starting_balance','InvoicePayment.currency_code'=>$currency_code,'InvoicePayment.client_id' =>$client_id)));
        
     
        // Credit Note
        $this->Invoice->applyBranch= ['onFind' => false, 'onSave' => false];
        $total_cn = $this->Invoice->find('first', array('fields' => 'sum(summary_total) as total_cn', 'conditions' => array('Invoice.type' => Invoice::Credit_Note, 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1', 'Invoice.currency_code' => $currency_code)));
        // Client Credit
        $total_credit = $this->InvoicePayment->find('first', array('fields' => 'sum(amount) as total_amount', 'conditions' => array('InvoicePayment.payment_method <>'=>'starting_balance','InvoicePayment.status=1', 'InvoicePayment.client_id' => $client_id, 'InvoicePayment.currency_code' => $currency_code)));
        // Payment Using Client Credit 
//        $total_payment = $this->InvoicePayment->query("SELECT sum(IF(amount<0,amount*-1,amount)) as total_paid FROM `invoice_payments` WHERE status=1 and (client_id={$client_id} or invoice_id in (select id from invoices where client_id={$client_id} and type in(0,16) and draft <>1)) and payment_method='client_credit' and currency_code='{$currency_code}'", false);
        // First query: sum payments where client_id matches
        $total_payment_client = $this->InvoicePayment->query("
            SELECT sum(IF(amount<0, amount*-1, amount)) as total_paid
            FROM `invoice_payments`
            WHERE status=1
            AND client_id={$client_id}
            AND payment_method='client_credit'
            AND currency_code='{$currency_code}'
            ", false);
    // Second query: sum payments where invoice_id matches invoices of the specified client_id
        $total_payment_invoice = $this->InvoicePayment->query("
            SELECT sum(IF(amount<0, amount*-1, amount)) as total_paid
            FROM `invoice_payments`
            WHERE status=1
            AND invoice_id IN (
                SELECT id
                FROM invoices
                WHERE client_id={$client_id}
                AND type IN (0, 16)
                AND draft <> 1
            )
            AND payment_method='client_credit'
            AND currency_code='{$currency_code}'
            ", false);
        $total_payment = $total_payment_client[0][0]['total_paid'] + $total_payment_invoice[0][0]['total_paid'];

        // Get Client Journals
        $jt=$this->get_client_journals($client_id, $currency_code);
        // Get Over Paid Invoices
        $flag = settings::getValue(InvoicesPlugin, 'ignore_overpaid_invoices');
        if(empty($flag))
        {
        $overpaid=$this->overpaid($client_id, $currency_code);
        }

        $refund_credit=$this->InvoicePayment->query("SELECT
                    SUM(IF(amount < 0, amount * -1, amount)) AS total_refund
                        FROM
                            invoice_payments AS IP
                        JOIN(invoices AS I)
                        ON
                            (I.id = IP.invoice_id)
                        WHERE
                        (
                            I.draft IS NULL OR I.draft = 0
                        ) AND I.type IN(6) AND I.client_id={$client_id} AND IP.status = 1 AND IP.payment_method = 'client_credit' AND IP.currency_code='{$currency_code}';");


        // warning suppress
        $total_rr[0]['total_rr'] = $total_rr[0]['total_rr'] ?? 0;
        $jt[0][0]['total_cn'] = $jt[0][0]['total_cn'] ?? 0;
        $total_sb['InvoicePayment']['amount'] = $total_sb['InvoicePayment']['amount'] ?? 0;

        $cc = $total_rr[0]['total_rr']
            +$overpaid
            +$jt[0][0]['total_cn']
            +$total_cn[0]['total_cn']
            +$total_credit[0]['total_amount']
            -($total_payment*1)
            +($total_sb['InvoicePayment']['amount']*1)
            +($refund_credit[0][0]['total_refund']);
        return  $cc;
    }

    public function clients_credit_local_currency($client_ids, $allClients = false) {

        if(empty($client_ids)){
            return 0;
        }
        //starting_balance
        $this->loadModel("InvoicePayment");
        $this->loadModel("Invoice");
        $this->InvoicePayment->alias='InvoicePayment';
        $this->Invoice->recursive = -1;

        $this->InvoicePayment->applyBranch['onFind']= false;
        $journalJoin = [
            [
                'table' => 'journals',
                'alias' => 'Journal',
                'type' => 'left',
                'conditions' => [
                    'Journal.entity_type = "invoice_payment" AND Journal.entity_id = InvoicePayment.id'
                ]
            ]
        ];
        $clientPayments = [];
        $conditions = array('InvoicePayment.payment_method'=>'starting_balance','InvoicePayment.client_id' =>$client_ids);
        if($allClients) {
            unset($conditions['InvoicePayment.client_id']);
        }
        $total_sb = $this->InvoicePayment->find('all', ['group' => ['InvoicePayment.client_id'],'joins' => $journalJoin, 'fields' => ['InvoicePayment.client_id', '(`amount` * `Journal`.`currency_rate`) as total'], 'conditions' => $conditions]);
        foreach ($total_sb as $item) {
            $clientPayments[$item["InvoicePayment"]['client_id']] = $item;
        }
        // Credit Note
        $this->Invoice->applyBranch= ['onFind' => false, 'onSave' => false];
        $journalJoin = [
            [
                'table' => 'journals',
                'alias' => 'Journal',
                'type' => 'left',
                'conditions' => [
                    'Journal.entity_type = "credit_note" AND Journal.entity_id = Invoice.id'
                ]
            ]
        ];
        $conditions = array('Invoice.type' => Invoice::Credit_Note, 'Invoice.client_id' => $client_ids, 'Invoice.draft <> 1');
        if($allClients) {
            unset($conditions['Invoice.client_id']);
        }
        $total_cn = $this->Invoice->find('all', array('group' => ['Invoice.client_id'], 'joins' => $journalJoin, 'fields' => ['Invoice.client_id', 'sum(`Journal`.`currency_rate` * `summary_total`) as total_cn'], 'conditions' => $conditions));
        $clientCreditNotes = [];
        foreach ($total_cn as $item) {
            $clientCreditNotes[$item["Invoice"]['client_id']] = $item;
        }
        // Client Credit
        $journalJoin = [
            [
                'table' => 'journals',
                'alias' => 'Journal',
                'type' => 'left',
                'conditions' => [
                    'Journal.entity_type = "invoice_payment" AND Journal.entity_id = InvoicePayment.id'
                ]
            ]
        ];
        $conditions = array('InvoicePayment.payment_method <>'=>'starting_balance','InvoicePayment.status=1', 'InvoicePayment.client_id' => $client_ids);
        if($allClients) {
            unset($conditions['InvoicePayment.client_id']);
        }
        $total_credit = $this->InvoicePayment->find('all', array('group' => ['InvoicePayment.client_id'], 'joins' => $journalJoin, 'fields' => ['InvoicePayment.client_id', 'sum(`amount` * `Journal`.`currency_rate`) as total_amount'], 'conditions' => $conditions));
        $clientTotalCredits = [];
        foreach ($total_credit as $item) {
            $clientTotalCredits[$item["InvoicePayment"]['client_id']] = $item;
        }

        $query = "SELECT I.client_id,sum(amount * COALESCE(NULLIF(Journals.currency_rate, ''), 1)) as total_paid FROM `invoice_payments`
                    LEFT JOIN invoices I on I.id = invoice_payments.invoice_id
                    LEFT JOIN journals Journals on I.id = Journals.entity_id and Journals.entity_type = 'invoice'
                    WHERE status=1 ";
        if(!$allClients) {
            $query .= "and I.client_id in (" . implode(',', $client_ids) . ") ";
        }
        $query .= "and payment_method='client_credit' group by I.client_id";
        $total_payment = $this->InvoicePayment->query($query, false);
        $clientsTotalPayments = [];
        foreach ($total_payment as $item) {
            $clientsTotalPayments[$item['I']['client_id']] = $item;
        }

        $flag = settings::getValue(InvoicesPlugin, 'ignore_overpaid_invoices');
        if(empty($flag))
        {
            $more_conditions=array();
            if(Domain_Short_Name=="onlineinvoices.com"){
                $more_conditions['Invoice.date > ']="2018-06-11";
            }
            $this->Invoice->recursive = -1;
            $journalJoin = [
                [
                    'table' => 'journals',
                    'alias' => 'Journal',
                    'type' => 'left',
                    'conditions' => [
                        'Journal.entity_type = "invoice" AND Journal.entity_id = Invoice.id'
                    ]
                ]
            ];
            $conditions = array(
                'Invoice.payment_status in ('.INVOICE_STATUS_PAID.','.INVOICE_STATUS_REFUNDED.')',
                'Invoice.type=0',
                'Invoice.client_id' => $client_ids,
                'Invoice.draft <> 1',
                'abs(summary_total-summary_paid-COALESCE(summary_refund,0)) >'.MINOVERPAID);
            if($allClients) {
                unset($conditions['Invoice.client_id']);
            }
            $total_amount = $this->Invoice->find('all', array(
                'group' => ['Invoice.client_id'],
                'joins' => $journalJoin,
                'fields' => ['Invoice.client_id', 'sum((summary_total-summary_paid-COALESCE(summary_refund,0)) * Journal.currency_rate) as total_amount'],
                'conditions' => $more_conditions+$conditions
            ));
            $clientsTotalAmount = [];
            foreach ($total_amount as $item) {
                $clientsTotalAmount[$item['Invoice']['client_id']] = $item;
            }
        }

        $results = [];
        // Payment Using Client Credit
        foreach ($client_ids as $client_id) {

            // Get Client Journals
            $jt=$this->get_client_journals_local_currency($client_id);
            // Get Over Paid Invoices
            $cc=
                    (isset($clientsTotalAmount[$client_id]) ? $clientsTotalAmount[$client_id][0]['total_amount']*-1: 0) +
                    ($jt[0][0]['total_cn']?:0) +
                    (isset($clientCreditNotes[$client_id]) ? $clientCreditNotes[$client_id][0]['total_cn']:0) +
                    (isset($clientTotalCredits[$client_id])?$clientTotalCredits[$client_id][0]['total_amount']:0) -
                    ((isset($clientsTotalPayments[$client_id])?$clientsTotalPayments[$client_id][0]['total_paid']:0)*1)+
                    ((isset($clientPayments[$client_id])?$clientPayments[$client_id]['0']['total']:0)*1);
            $results[$client_id] = $cc;
        }
        return $results;

    }

    public function client_credit_local_currency($client_id) {
        if(empty($client_id)){
            return 0;
        }
        //starting_balance
        $this->loadModel("InvoicePayment");
        $this->loadModel("Invoice");
        $this->InvoicePayment->alias='InvoicePayment';
        $this->Invoice->recursive = -1;

        // Starting Balance
        $this->InvoicePayment->applyBranch['onFind']= false;
        $journalJoin = [
                [
                    'table' => 'journals',
                    'alias' => 'Journal',
                    'type' => 'left',
                    'conditions' => [
                        'Journal.entity_type = "invoice_payment" AND Journal.entity_id = InvoicePayment.id'
                    ]
                ]
        ];
        $total_sb = $this->InvoicePayment->find('first', array('joins' => $journalJoin, 'fields' => ['(`amount` * `Journal`.`currency_rate`) as total'], 'conditions' => array('InvoicePayment.payment_method'=>'starting_balance','InvoicePayment.client_id' =>$client_id)));


        // Credit Note
        $this->Invoice->applyBranch= ['onFind' => false, 'onSave' => false];
        $journalJoin = [
            [
                'table' => 'journals',
                'alias' => 'Journal',
                'type' => 'left',
                'conditions' => [
                    'Journal.entity_type = "credit_note" AND Journal.entity_id = Invoice.id'
                ]
            ]
        ];
        $total_cn = $this->Invoice->find('first', array('joins' => $journalJoin, 'fields' => ['sum(`Journal`.`currency_rate` * `summary_total`) as total_cn'], 'conditions' => array('Invoice.type' => Invoice::Credit_Note, 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1')));
        // Client Credit
        $journalJoin = [
            [
                'table' => 'journals',
                'alias' => 'Journal',
                'type' => 'left',
                'conditions' => [
                    'Journal.entity_type = "invoice_payment" AND Journal.entity_id = InvoicePayment.id'
                ]
            ]
        ];
        $total_credit = $this->InvoicePayment->find('first', array('joins' => $journalJoin, 'fields' => ['sum(`amount` * `Journal`.`currency_rate`) as total_amount'], 'conditions' => array('InvoicePayment.payment_method <>'=>'starting_balance','InvoicePayment.status=1', 'InvoicePayment.client_id' => $client_id)));
        // Payment Using Client Credit

        $query = "SELECT sum(IF(amount<0,amount*-1,amount) * J.currency_rate) as total_paid FROM `invoice_payments`
                    LEFT JOIN journals J on J.entity_id = invoice_payments.id and J.entity_type = 'invoice_payment' 
                    WHERE status=1 and (client_id={$client_id} or invoice_id in (select id from invoices where client_id={$client_id} and type=0 and draft <>1)) and payment_method='client_credit'";
        $total_payment = $this->InvoicePayment->query($query, false);
        // Get Client Journals
        $jt=$this->get_client_journals_local_currency($client_id);
        // Get Over Paid Invoices
        $flag = settings::getValue(InvoicesPlugin, 'ignore_overpaid_invoices');
        if(empty($flag)) {
            $overpaid=$this->overpaid_local_currency($client_id);
        }


        $cc=$overpaid+$jt[0][0]['total_cn']+$total_cn[0]['total_cn'] + $total_credit[0]['total_amount'] - ($total_payment[0][0]['total_paid']*1)+($total_sb['0']['total']*1);
        return  $cc;
    }

    function pay_invoices_from_credit($client_id, $force = false)
    {
        $this->loadModel("InvoicePayment");
        $this->loadModel("Invoice");
        $this->Invoice->applyBranch['onFind'] = false;
        $this->InvoicePayment->applyBranch['onFind'] = false;

        $this->InvoicePayment->alias='InvoicePayment';
        $this->Invoice->recursive = -1;


        if (!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && $force == false) {
            return false;
        }

        $this->loadModel("Invoice");

        $this->Invoice->recursive = -1;
        $xunpaid_invoice = $this->Invoice->find('all', array('recursive' => -1, 'fields' => 'DISTINCT `Invoice`.`currency_code`', 'conditions' => array('payment_status!=2', 'Invoice.type' => [ Invoice::Invoice, Invoice::DEBIT_NOTE], 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1')));
        foreach ($xunpaid_invoice as $xinvoice) {
            $client_credit = $this->client_credit($client_id, $xinvoice['Invoice']['currency_code']);

            //Check if the client credit is less than the minimum or bigger than the invoice unpaid amount
            $conditions = array('Invoice.currency_code' => $xinvoice['Invoice']['currency_code'], 'payment_status!=2', 'Invoice.type' =>[ Invoice::Invoice, Invoice::DEBIT_NOTE], 'Invoice.client_id' => $client_id, 'Invoice.draft <> 1', 'Invoice.summary_unpaid <> 0');
            if($client_credit < MINIAUTOPAY) {
                $conditions[] = 'abs(Invoice.summary_unpaid - '.$client_credit.') <= ' . MINIAUTOPAY;
            }
            $unpaid_invoice = $this->Invoice->find('all', array('order' => 'Invoice.date asc,Invoice.id asc', 'conditions' => $conditions));
            foreach ($unpaid_invoice as $invoice) {
                $return = $this->pay_invoice_from_credit($invoice['Invoice']['id'], $force);
                // Escape condition to stop the loop if the client balance is already depleted from the payment, no need to loop over all rest invoices
                if (!$return['status'] && round($return['m'], 2) <= MINIAUTOPAY) {
                    break;
                }
            }
        }
    }

    function pay_invoice_from_credit($invoice_id,$force=false) {
        
        if(!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && $force==false){
            return false;
        }
        $owner = getAuthOwner();
        $this->loadModel("Invoice");
        $this->loadModel("InvoicePayment");
        $this->Invoice->applyBranch['onFind'] = false;
        $this->InvoicePayment->applyBranch['onFind'] = false;
        $invoice = $this->Invoice->find('first',array('callbacks' => true,'recursive' => -1,'conditions'=>array('Invoice.id'=>$invoice_id)));
        $client_balance = $this->client_credit($invoice['Invoice']['client_id'], $invoice['Invoice']['currency_code']);

        /**
         * Ignore the minimum autopay limit in case the invoice unpaid is more less than the current client balance
         * @As discussed with Eng @Muhammed Azzam
         */
        if ($client_balance == 0 || ($client_balance < MINIAUTOPAY && abs($client_balance-$invoice['Invoice']['summary_unpaid']) > MINIAUTOPAY)) {
            return array('status' => false,'m'=>$client_balance);
        }

        if ($invoice['Invoice']['summary_unpaid'] == 0) {
            return array('status' => false,'m'=>$client_balance);
        }
        if ($client_balance > $invoice['Invoice']['summary_unpaid']) {
            $amount = $invoice['Invoice']['summary_unpaid'];
        } else {
            $amount = $client_balance;
        }
        $data = array('InvoicePayment' => array());
        $data['InvoicePayment']['invoice_id'] = $invoice_id;
        $data['InvoicePayment']['staff_id'] = $owner['staff_id'];
        $data['InvoicePayment']['status'] = PAYMENT_STATUS_COMPLETED;
        $data['InvoicePayment']['payment_method'] = $old_overpaid_payment_data['InvoicePayment']['payment_method'] ?? 'client_credit';
        $data['InvoicePayment']['date'] = $old_overpaid_payment_data['InvoicePayment']['date'] ?? (date("Y-m-d"));
        $data['InvoicePayment']['amount'] = $amount;
        $data['InvoicePayment']['added_by'] = 1;
        $data['InvoicePayment']['currency_code'] = $invoice['Invoice']['currency_code'];
        if(Settings::getValue(0, 'round_invoices', null, false, false)){
            $round_to = CurrencyHelper::getFraction($invoice['Invoice']['currency_code']);
            $data['InvoicePayment']['amount'] = round($data['InvoicePayment']['amount'], $round_to);
        }
        $result = $this->Invoice->ownerAddPayment($data);
        
        if ($result['status']) {
            $invoicePayment = $this->InvoicePayment->read(null, $result['payment_id']);
            $this->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array('source'=>array('final_currency_code'=>$invoicePayment['InvoicePayment']['currency_code'],'currency_code'=>$invoice['Invoice']['currency_code'],'client_balance'=>$client_balance),'staff_id'=>-2,'primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => empty($invoicePayment['Invoice']['draft']) ? $invoicePayment['Invoice']['payment_status'] : -1, 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));
            return $result;
        }
    }


 function adjust_and_pay($client_id, $currency_code="")
    {
        $this->loadModel('Journal');
        Journal::$called_back[$this->name][$client_id]=true;

        if(IS_REST || !settings::getValue(InvoicesPlugin, 'automatic_pay_invoices')){
            return;
        }
        
        $this->loadModel("Invoice");
       if($currency_code==""){
        $all=$this->Invoice->find('all',array('recursive' => -1,'fields'=>'DISTINCT `Invoice`.`currency_code`','conditions'=>array('Invoice.client_id'=>$client_id))) ;

       foreach($all as $current_currency_code){

        $this->adjust_balance($client_id, $current_currency_code['Invoice']['currency_code']);
       }
       }else{
        $this->adjust_balance($client_id, $currency_code);
       }

        //This condition was added based on @Mr Azzam note
        if (ifPluginActive(PosPlugin) && settings::getValue(PosPlugin, 'pos_default_client') == $client_id){
            return false;
        }

        $this->pay_invoices_from_credit($client_id);
    }
    function adjust_balance($client_id, $currency_code) {

        $client_credit = $this->client_credit($client_id, $currency_code);

        $this->loadModel("InvoicePayment");

        while (($client_credit)<(MINIAUTOPAY*-1)){

            $client_credit = $this->client_credit($client_id, $currency_code);
            $this->InvoicePayment->applyBranch['onFind']= false;
                $ipcount = $this->InvoicePayment->find('count', array('recursive' => 1,'order' => 'InvoicePayment.id desc,InvoicePayment.invoice_id desc', 'conditions' => array(
                    'Invoice.client_id' => $client_id,
                    'InvoicePayment.payment_method' => 'client_credit',
                    'InvoicePayment.currency_code' => $currency_code,
                    'InvoicePayment.id not in (select invoice_payment_id from client_credit_distributions)'
                )));
            if($ipcount==0){
                break;
            }
            if (($client_credit)<(MINIAUTOPAY*-1)) {

                $this->InvoicePayment->alias = 'InvoicePayment';
                $last_row = $this->InvoicePayment->find('all', array('limit'=>1,'recursive' => 1,'order' => 'InvoicePayment.id desc,InvoicePayment.invoice_id desc', 'conditions' => array('Invoice.client_id' => $client_id, 'InvoicePayment.payment_method' => 'client_credit', 'InvoicePayment.currency_code' => $currency_code)));

                $this->InvoicePayment->delete($last_row[0]['InvoicePayment']['id']);
                $this->after_delete_payment($last_row[0], ACTION_DELETE_INVOICE_PAYMENT,$client_credit);

               
            }
        }
    }

    /**
     * this function called when payment deleted by the system
     * @param $invoicePayment  array of invoice payment
     * @param $action action line key
     * @param null $client_credit current client credit
     */
    function after_delete_payment($invoicePayment, $action, $client_credit=null) {
        if (!$invoicePayment['InvoicePayment']['client_id']) {
        $this->InvoicePayment->Invoice->updateInvoicePayments($invoicePayment['InvoicePayment']['invoice_id']);
        $invoice = $this->InvoicePayment->Invoice->read(null, $invoicePayment['InvoicePayment']['invoice_id']);
        $invoicePayment['Invoice'] = $invoice['Invoice'];
		App::import('Vendor', 'notification_2');
        NotificationV2::delete_notificationbyref($invoicePayment['Invoice']['id'], NotificationV2::NOTI_CLIENT_PENDING_PAYMENT);
        }
        $this->add_actionline($action, array('source'=>array('client_credit'=>$client_credit),'staff_id'=>-2,'primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));
    }
    
    
    public function get_journals($data)
    {
    
        if(empty($data['Client']['id'])) $data['Client']['id']= $this->id;
         if(!isset($data['Client']['starting_balance'])) 
            return false;
         
        $journal['Journal']['date']=$data['Client']['created'];
        $journal['Journal']['entity_type']='client';
        $description=$journal['Journal']['description']= __('Client',true).' #'.$data['Client']['client_number'].' '. __('Opening Balance',true);
        $journal['Journal']['entity_id']=$data['Client']['id'];
        $currency_code=$journal['Journal']['currency_code']=!empty($data['Client']['default_currency_code'])?$data['Client']['default_currency_code']:getCurrentSite('currency_code'); 
        if(empty($data['Client']['starting_balance'])) return $journal;
        
        $amount=$data['Client']['starting_balance'];
        
        $journal['JournalTransaction'][]=
            array(
                    'subkey'=>'client', 
                    'currency_debit'=>$amount,
                    'currency_code'=>$currency_code,
                    'description'=>$description,
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'client','entity_id'=>$data['Client']['id'])
                );  
         $journal['JournalTransaction'][]=
            array(
                    'subkey'=>'opening_balance', 
                    'currency_credit'=>$amount,
                    'currency_code'=>$currency_code,
                    'description'=>$description,
                    'auto_account'=>array('type'=>'static' , 'entity_type'=> 'opening_balance','entity_id'=>0)
                );
        
        return $journal;
    }
    

    /** @throws CanNotDeleteClientException */
    function check_deletion ( $id ) {
//        return false ; 
        $staff_id = getAuthOwner ( 'staff_id' );
        $this->loadModel ( 'Invoice' );
        $this->loadModel ( 'Expense' );
        $this->loadModel ( 'FollowUpReminder' );
        $this->loadModel ( 'Post' );
        $this->loadModel ( 'InvoicePayment' );
        
        $this->Invoice->recursive = -1 ; 
        $this->Expense->recursive = -1 ; 
        $this->FollowUpReminder->recursive = -1 ; 
        $this->Post->recursive = -1 ; 
        $this->InvoicePayment->recursive = -1 ; 
        //invoices and payments
        if (!check_permission(Invoices_Edit_All_Invoices))
        {
            //invoices
            $invoice_count = $this->Invoice->find ( 'count' , ['applyBranchFind' => false, 'conditions' => ['staff_id <> ' => $staff_id , 'client_id' => $id , 'type <> ' => $this->Invoice::TEMPINVOICE]] );
            if ( $invoice_count > 0 ){
                throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('invoices', true)));
            }
            //payments
            if ( !check_permission(Invoices_Add_Payments_to_All))
            {
                $payment_count = $this->InvoicePayment->find ( 'count' , ['applyBranchFind' => false,'conditions' => ['staff_id <> ' => $staff_id, 'client_id' => $id ]] );
                if ( $payment_count > 0 ){
                    throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('payment', true)));
                }
            }
        }
        $invoice_count = $this->Invoice->find ( 'count' , ['applyBranchFind' => false, 'conditions' => ['client_id' => $id ,'type <> ' => $this->Invoice::TEMPINVOICE]] );
        if($invoice_count > 0){
            throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('invoices', true)));
        }
        $payment_count = $this->InvoicePayment->find ( 'count' , ['applyBranchFind' => false,'conditions' => ['client_id' => $id ]] );
        if( $payment_count > 0){
            throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('payments', true)));
        }
        //Checks other invoice related items.
        $invoices = $this->Invoice->find ( 'list' , ['applyBranchFind' => false, 'conditions' => ['client_id' => $id]]);
        if ( !empty($invoices ) ){
            if ( !$this->Invoice->check_deletion(array_keys ( $invoices ) ) ){
                throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('invoices', true)));
            }
        }
        //appointments and notes
        if (!check_permission(Edit_Delete_All_Notes_Attachments))
        {
            //appointments
            $reminder_count = $this->FollowUpReminder->find ( 'count' , ['applyBranchFind' => false, 'conditions' => ['staff_id <> ' => $staff_id, 'item_id' => $id, 'item_type' => Post::INVOICE_TYPE ]] );
            if ( $reminder_count > 0 ){
                throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('reminder', true)));
            }
            //notes
            $post_count = $this->Post->find ( 'count' , ['applyBranchFind' => false, 'conditions' => ['staff_id <> ' => $staff_id, 'item_id' => $id, 'item_type' => Post::INVOICE_TYPE ]] );
            if ( $post_count > 0 ){
                throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('post', true)));
            }
        }
        //Incomes
        if (!check_permission(Edit_Delete_all_incomes))
        {
            $income_count = $this->Expense->find ( 'count' , ['applyBranchFind' => false, 'conditions' => ['staff_id <> ' => $staff_id, 'client_id' => $id , 'is_income' => 1 ]] );
            if ( $income_count > 0 ){
                throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('income', true)));
            }
        }
        //Expenses
        if (!check_permission(Edit_Delete_all_expenses))
        {
            $expense_count = $this->Expense->find ( 'count' , ['applyBranchFind' => false, 'conditions' => ['staff_id <> ' => $staff_id, 'client_id' => $id , 'is_income' => 0 ]] );
            if ( $expense_count > 0 ){
                throw new CanNotDeleteClientException(sprintf(__("You can't delete this account as it has %s", true), __('expense', true)));
            }
        }
        return true ;
    }
    
    function delete_with_related ( $id )
    {

        //invoices
        $this->loadModel ( 'Invoice' );
        //$this->Invoice->recursive =-1 ;
        $invoices = $this->Invoice->find ( 'list' ,['recursive'=> -1,'conditions' => ['client_id' => $id ] ]);
        if ( !empty ( $invoices ) ) {
            $invoices_keys = array_keys ( $invoices ) ;
            $this->Invoice->delete_with_related ( $invoices_keys )  ;
        }
        
        
        //payments
        $this->loadModel ( 'InvoicePayment' );
        //$this->InvoicePayment->recursive =-1 ;
        $payments = $this->InvoicePayment->find ( 'list' ,['recursive'=> -1,'conditions' => ['client_id' => $id ] ]);
        if ( !empty ( $payments)){
            $payments_keys = array_keys ( $payments ) ;
            foreach ( $payments_keys as $k   ){
                $this->InvoicePayment->delete ( $k ) ;
            }
            
//            $transaction[] = "DELETE FROM invoice_payments where id in ( ".implode(',', $payments_keys)." );" ;
        }
        
        //notes
        $this->loadModel ( 'Post' );
        //$this->Post->recursive =-1 ;
        $posts = $this->Post->find ( 'list' ,['recursive'=> -1,'conditions' => ['item_id' => $id , 'item_type' => Post::CLIENT_TYPE ] ]);
        if ( !empty ( $posts)){
            $posts_keys = array_keys ( $posts ) ;
            $this->Post->delete_with_files ( $posts_keys );
        }
        
        //appointments
        $this->loadModel ( 'FollowUpReminder' );
        //$this->FollowUpReminder->recursive =-1 ;
        $reminders = $this->FollowUpReminder->find ( 'list' ,['recursive'=> -1,'conditions' => ['item_id' => $id , 'item_type' => Post::CLIENT_TYPE ] ]);
        if ( !empty ( $reminders)){
            $reminders_keys = array_keys ( $reminders ) ;
            foreach ( $reminders_keys as $k   ){
                $this->FollowUpReminder->delete ( $k ) ;
            }
//            $this->FollowUpReminder->deleteAll (['FollowUpReminder.id' => $reminders_keys ] ) ;
//            $transaction[] = "DELETE FROM follow_up_reminders where id in ( ".implode(',', $reminders_keys)." );" ;
        }
        
        //expenses
        $this->loadModel ( 'Expense' );
        //$this->Expense->recursive =-1 ;
        $expenses = $this->Expense->find ( 'list' ,['recursive'=> -1,'conditions' => ['client_id' => $id ] ]);
        if ( !empty ( $expenses)){
            $expenses_keys = array_keys ( $expenses ) ;
            foreach ( $expenses_keys as $k   ){
                $this->Expense->delete ( $k ) ;
            }
//            $this->Expense->deleteAll  ( ['Expense.id' => $expenses_keys ] ) ;
//            $transaction[] = "DELETE FROM expenses where id in ( ".implode(',', $expenses_keys)." );" ;
        }

//        if ( is_array($id)){
//            $where = " in (".implode( ',' , $id )." ) " ;
//        }else {
//            $where = " = ".intval($id ) ;
//        }
//        $transaction[] = "DELETE FROM clients where id $where ;";
//        $this->deleteAll ( ['Client.id' => $id]) ;
//        foreach ( $expenses_keys as $k   ){
//            $this->delete ( $k ) ;
//        }
        if ( is_array($id)){
            foreach ( $id as $k   ){
                $this->delete ( $k ) ;
            }
        }else {
            $this->delete ( $id ) ;
        }

//        $transaction[] =" COMMIT;";
//        print_r ( $transaction ) ;die;
//        foreach ( $transaction as $q  ) {
//                $temp = $this->query($q ) ;
//            }
        return TRUE; 

//        return $this->query ($transaction ); 
    }
    
   function get_refund_total($id,$currency_code){
              $this->loadModel ( 'Invoice' );
        if(empty($id)){
            return 0;    
        }
        $total=0;
        $old_recursive=$this->Invoice->recursive;
        $this->Invoice->recursive=-1;
        $rows=$this->Invoice->find('all',array('fields'=>'summary_unpaid','conditions'=>array('Invoice.currency_code'=>$currency_code,'Invoice.client_id'=>$id,'Invoice.type'=>Invoice::Refund_Receipt)));
      
        foreach($rows as $row){
        $total +=($row['Invoice']['summary_unpaid']) ;
        }
       
        $this->Invoice->recursive=$old_recursive;
        
        return $total;
    }
    function get_invoices_count ( $id , $type = 'all'  ) {
        $this->loadModel ( 'Invoice') ; 
        $this->Invoice->recursive=-1;
        $conditions ['client_id'] = $id ;
        $conditions['type'] = $type ;
        if ( $type ==='all') {
             unset ( $conditions['type']);
        }
        return $this->Invoice->find ( 'count' ,['applyBranchFind' => false, 'conditions'=> $conditions]);
    }
    function get_payment_count ( $id  ) {
        $this->loadModel ( 'InvoicePayment' );
        $this->InvoicePayment->recursive =-1 ;
        return  $this->InvoicePayment->find ( 'count' ,['conditions' => ['client_id' => $id ] ]);
    }
    function get_appointment_count ( $id ) {
        $this->loadModel ( 'FollowUpReminder' );
        $this->loadModel ( 'Post' );
        $this->FollowUpReminder->recursive =-1 ;
        return  $this->FollowUpReminder->find ( 'count' ,['conditions' => ['item_id' => $id , 'item_type' => Post::CLIENT_TYPE ] ]);
    }
    function get_note_count ( $id ){
        $this->loadModel ( 'Post' );
        $this->Post->recursive =-1 ;
        return $this->Post->find ( 'count' ,['conditions' => ['item_id' => $id , 'item_type' => Post::CLIENT_TYPE ] ]);
    }
    function get_expense_count ( $id , $is_income = false  ) {
        $this->loadModel ( 'Expense' );
        $this->Expense->recursive =-1 ;
        return  $this->Expense->find ( 'count' ,['conditions' => ['client_id' => $id,'is_income'=>$is_income ] ]);
    }
    function get_client_journals($client_id,$currency_code=""){
                $more_journals=array();
                $this->loadModel('Journal');
        $this->Journal->applyBranch['onFind']= false;
                $this->loadModel('JournalTransaction');
        $this->JournalTransaction->applyBranch['onFind']= false;
                $entity_details['entity_type']='client';
                $entity_details['entity_id']=$client_id;
                $r=$this->Journal->get_auto_account($entity_details);   
                  if($currency_code!=""){
                $more_journals['JournalTransaction.currency_code']=$currency_code;
                }
                $more_journals['Journal.draft'] = '0';
                $JournalTransaction=$this->JournalTransaction->find('all',array('group'=>'JournalTransaction.currency_code','fields' => 'sum(JournalTransaction.currency_credit-JournalTransaction.currency_debit) as total_cn,concat(JournalTransaction.currency_code,\'\') as cc','conditions' => $more_journals+array("Journal.entity_type not in( 'invoice','debit_note','invoice_payment','refund_receipt','credit_note', 'year_opening_balance','year_closing_balance')",'JournalTransaction.journal_account_id'=>$r['JournalAccount']['id'])));
               
                /** @var array $JournalTransaction2 return client journal transactions under invoices owned by other clients */
                $JournalTransaction2 = $this->JournalTransaction->query("SELECT sum(JournalTransaction.currency_credit - JournalTransaction.currency_debit) as total_cn, concat(JournalTransaction.currency_code, '') as cc FROM `journal_transactions` AS `JournalTransaction` LEFT JOIN `journals` AS `Journal` ON (`JournalTransaction`.`journal_id` = `Journal`.`id`) LEFT JOIN `invoices` ON (`invoices`.`id` = `Journal`.`entity_id`) LEFT JOIN `journal_accounts` AS `JournalAccount` ON (`JournalTransaction`.`journal_account_id` = `JournalAccount`.`id`) WHERE `JournalTransaction`.`currency_code` = :currency_code AND `Journal`.`draft` = 0 AND `Journal`.`entity_type` in ('invoice', 'debit_note', 'refund_receipt', 'credit_note') AND `JournalTransaction`.`journal_account_id` = :journal_account_id AND `invoices`.`client_id` <> :client_id AND `invoices`.`id` = Journal.entity_id GROUP BY `JournalTransaction`.`currency_code`", ['currency_code' => $currency_code, 'journal_account_id' => $r['JournalAccount']['id'], 'client_id' => $client_id]);
                // if (!empty($JournalTransaction) && empty($JournalTransaction2)) { $journalTransaction will be used normally }
        
                if (empty($JournalTransaction) && !empty($JournalTransaction2)) {
                    $JournalTransaction = $JournalTransaction2;
                } elseif (!empty($JournalTransaction) && !empty($JournalTransaction2)) {
                    foreach ($JournalTransaction as $key => $journalTrans) {
                        foreach ($JournalTransaction2 as $journalTrans2) {
                            if ($journalTrans['0']['cc'] == $journalTrans2['0']['cc']) {
                                $JournalTransaction[$key]['0']['total_cn'] += $journalTrans2['0']['total_cn'];
                            }
                        }
                    }
                }
                return $JournalTransaction;
    }

    function get_client_journals_local_currency($client_id){
        $this->loadModel('Journal');
        $this->Journal->applyBranch['onFind']= false;
        $this->loadModel('JournalTransaction');
        $this->JournalTransaction->applyBranch['onFind']= false;
        $entity_details['entity_type']='client';
        $entity_details['entity_id']=$client_id;
        $r=$this->Journal->get_auto_account($entity_details);
        $JournalTransaction=$this->JournalTransaction->find('all',array('fields' => 'sum(JournalTransaction.credit-JournalTransaction.debit) as total_cn','conditions' => array("Journal.entity_type not in('invoice','debit_note','invoice_payment','refund_receipt','credit_note', 'year_opening_balance','year_closing_balance')",'JournalTransaction.journal_account_id'=>$r['JournalAccount']['id'],'Journal.draft' => '0')));
        return $JournalTransaction;
    }

    function get_client_journal_rows($client_id,$currency_code=""){
                $more_journals=array();
                $this->loadModel('Journal');
                $this->loadModel('JournalTransaction');  
                $entity_details['entity_type']='client';
                $entity_details['entity_id']=$client_id;
                $r=$this->Journal->get_auto_account($entity_details);   
                  if($currency_code!=""){
                $more_journals['JournalTransaction.currency_code']=$currency_code;
                }
                $JournalTransaction=$this->JournalTransaction->find('all',array('conditions' => $more_journals+array('Journal.is_automatic=0','JournalTransaction.journal_account_id'=>$r['JournalAccount']['id'])));
                return $JournalTransaction;
    }
    function get_client_journals_count($client_id,$currency_code=""){
                $more_journals=array();
                $this->loadModel('Journal');
                $this->loadModel('JournalTransaction');  
                $entity_details['entity_type']='client';
                $entity_details['entity_id']=$client_id;
                $r=$this->Journal->get_auto_account($entity_details);    
                if($currency_code!=""){
                $more_journals['JournalTransaction.currency_code']=$currency_code;
                }
                $JournalTransaction=$this->JournalTransaction->find('count',array('conditions' => $more_journals+array('Journal.is_automatic=0','JournalTransaction.journal_account_id'=>$r['JournalAccount']['id'])));
                return $JournalTransaction;
    }
    
    function get_journal_id($client_id){
                   $this->loadModel('Journal');
                
                $entity_details['entity_type']='client';
                $entity_details['entity_id']=$client_id;
                $r=$this->Journal->get_auto_account($entity_details);  
                return intval($r['JournalAccount']['id']);
        
    }
    
	function get_assigned_staff($client_id,$get_staff_id = false)
	{
		$this->loadModel('ItemStaff');
		$this->loadModel('Post');
        $assigned = $this->ItemStaff->find('list',array('fields' => ['staff_id', 'staff_id'],'conditions' => array('ItemStaff.item_id' => $client_id,'ItemStaff.item_type' => Post::CLIENT_TYPE)));
		if(!$assigned && $get_staff_id){
			$client = $this->findById($client_id);
			$assigned = [$client['Client']['staff_id']];
		}
		return $assigned;
	}

	function deleteAble($client_id){
		if(ifPluginActive(AccountingPlugin)){
			$JournalAccount = GetObjectOrLoadModel('JournalAccount');
            $Journal = GetObjectOrLoadModel('Journal');
			$journal_account = $Journal->get_auto_account(['entity_type' => 'client', 'entity_id' => $client_id ]);
			if($journal_account){
                $deleteAbleResult =  $JournalAccount->deletAble($journal_account, false);
                if($deleteAbleResult == false && $JournalAccount->hasRoutes($journal_account))
                {
                    //in this case the journal account belongs to more than one entity
                    //so we will just stop at deleteing the current entity
                    return true;
                }else if($deleteAbleResult == false){
                    return false;
                }
			}
		}
		return true;
	}
	
	
	/**
	 * 
	 * @param type $client_data
	 * @return boolean true of client register false if it couldn't
	 */
	function register($client_data)
	{
		
		$this->create();
		$this->set($client_data);
        $this->ignoreCustomForm = true;
        if($this->validates()){
            return $this->save($client_data,false);
        }else{
            return false;
        }

	}
	/**
	 * 
	 * @param type $result the result returned from the client->login_system
	 * set client session
	 */
	function writeClientSession($result)
	{

		$session = new CakeSession;
		$cookie = new CookieComponent;
		$result['user']['type'] = $result['type'];
		$session_name = strtoupper($result['type']);
		$session->write($session_name, $result['user']);
		$cookie->write('User_level', "2", false, Cookie_Life);
		$cookie->write('User_id', $result['is_staff'], false, Cookie_Life);
		$cookie->write('User_hash', md5($_SERVER['HTTP_USER_AGENT'] . $result['is_staff']), false, Cookie_Life);
		$client = getAuthClient();
		$this->add_actionline(ACTION_CLIENT_LOGIN, array('staff_id' => -1, 'primary_id' => $client['id'], 'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number']));
		$this->add_stats(STATS_CLIENT_LOGIN, array($result['user']['id']));
	}

	function getClientMenu()
    {
        $menus = (include APP . 'client-menus.php');
    }
	
	
	function afterDelete()
    {
        $this->deleteRelated($this->id);
        setLastUpdatedAt(PluginUtil::ClientsPlugin, SettingsUtil::CLIENTS_LAST_UPDATED_AT);
    }

    function deleteRelated($clientId)
    {

        $this->loadModel('FollowUpReminder');
        $this->loadModel('ItemStaff');
        $this->FollowUpReminder->deletePartnerFollowUps($clientId, FollowUpReminder::CLIENT_PARTNER_TYPE);
        $this->ItemStaff->deleteItemStaffs($clientId, ItemStaff::CLIENT_ITEM_TYPE);

    }

    function afterSave($created)
    {
        setLastUpdatedAt(PluginUtil::ClientsPlugin, SettingsUtil::CLIENTS_LAST_UPDATED_AT);
    }

	/**
	 * This function attempts to get the last address used for a client by searching through his last invoice, if no match was found it returns the first non empty address otherwise it returns an empty address
	 * @param $client_id
	 * @param $client_addresses
	 */
	function getClientsDefaultAddress($client_ids, $client_addresses) {

        $ret = [];
		$InvoiceModel = GetObjectOrLoadModel('Invoice');
		$last_invoices = $InvoiceModel->find('all', ['applyBranchFind' => false,  'recursive' => -1, 'conditions' => [
			'Invoice.client_id' => $client_ids,
			'NOT' => [
				'Invoice.client_business_name' => 'POS Client'
			],
		],  'fields' => 'max(Invoice.id) as `invoice_id`',    'group' => 'Invoice.client_id']);

        $ids = [];
        foreach ($last_invoices as $id) {
            $ids [] = $id[0]['invoice_id'];
        }

        $last_invoices = $InvoiceModel->find('all', ['conditions' => ['Invoice.id' => $ids], 'applyBranchFind' => false,  'recursive' => -1]);
        foreach ($last_invoices as $last_invoice) {
            $is_found = false;
            $user_client_address = $client_addresses[$last_invoice['Invoice']['client_id']];
            // If There is an Invoice for That Client && Address Not Empty In the Invoice && The Address in the invoice matches one of his addresses
            if (!empty($last_invoice) && !$this->isAddressEmpty($this->mapAddressFromInvoiceAddress($last_invoice))) {
                $last_client_address_from_invoice = $this->mapAddressFromInvoiceAddress($last_invoice);
                foreach ($user_client_address as $client_address) {
                    if ($this->isSameAddress($client_address, $last_client_address_from_invoice)) {
                         $ret[$last_invoice['Invoice']['client_id']] = $client_address;
                         $is_found = true;
                         break;
                    }
                }
            }
            if ($is_found) {
                continue;
            }
            // No match was found or client is new (No Previous Invoice Addresses) Return the first Non Empty address you find
            foreach ($user_client_address as $client_address) {
                if (!$this->isAddressEmpty($client_address)) {
                    $ret[$last_invoice['Invoice']['client_id']] = $client_address;
                    $is_found = true;
                    break;
                }
            }
            if ($is_found) {
                continue;
            }
            // No address found for client - return the empty address that was previously added in api_get_clients
            $ret[$last_invoice['Invoice']['client_id']]=  $user_client_address[0];
        }
        return $ret;
	}

	/**
	 * Checks if the Client address is empty by comparing every field (address1, address2, city, state, postal_code) and returning false if even 1 field only is not empty.
	 * @param $address
	 * @return bool
	 */
	function isAddressEmpty($address) {
		if (empty($address)) {
			return true;
		}
		// Testing Normal Address Fields
		$fields = ['address1', 'address2', 'city', 'state', 'postal_code'];
		foreach ($fields as $field) {
			if (!empty($address[$field])) {
				return false;
			}
		}
		// Checking Invoice Address Fields
		$fields = ['client_address1', 'client_address2', 'client_city', 'client_state', 'client_postal_code'];
		foreach ($fields as $field) {
			if (!empty($address[$field])) {
				return false;
			}
		}
		// All Fields are Empty
		return true;
	}

	/**
	 * Checks if the $address1 is the same address as $address2 by comparing every field (address1, address2, city, state, postal_code) in both addresses
	 * @param $address1
	 * @param $address2
	 * @return bool
	 */
	function isSameAddress($address1, $address2) {
		return $address1['id'] == $address2['id'];
	}

	/**
	 * this function maps the invoice address fields ['Invoice']["client_address1"] ... etc and returns an address array mapped with the normal address fields "address1", "address2" ...etc
	 * @param $invoice
	 * @return array
	 */
	private function mapAddressFromInvoiceAddress($invoice) {
		return [
			'id' => $invoice['Invoice']['address_id'] ?: 0,
			'address1' => $invoice['Invoice']['client_address1'],
			'address2' => $invoice['Invoice']['client_address2'],
			'city' => $invoice['Invoice']['client_city'],
			'state' => $invoice['Invoice']['client_state'],
			'postal_code' => $invoice['Invoice']['client_postal_code']
		];
	}

    /**
     * @param $value
     * @return array
     */
    function getSearchClientConditions($value):array
    {
        return  ["CONCAT(Client.id,Client.client_number) = '$value' ", "CONCAT(Client.first_name,' ',Client.last_name) like '$value%'", "CONCAT(Client.last_name,' ',Client.first_name) like '$value%'", "CONCAT(Client.last_name,' ',Client.first_name) like '% $value%'", "Client.business_name like '$value%'", "Client.business_name like '% $value%'", "Client.email like '$value%'", "Client.phone1 like '$value%'", "Client.phone2 like '$value%'", "Client.client_number like '$value%'"];
    }

    public function getPosDefaultClient():string
    {
        $posDefaultClient = settings::getValue(PosPlugin, 'pos_default_client');
        if($posDefaultClient) {
            return " OR  Client.id = $posDefaultClient ";
        }
        return '';
    }

    function processClients($clients)
    {
        if (empty($clients)) {
            return [];
        }
        $enable_multiple_addresses = settings::getValue(ClientsPlugin,'multiple_addresses');
        $ClientModel = GetObjectOrLoadModel('Client');
        $tags = GetObjectOrLoadModel('Tag')->find('list', array('fields' => array('id', 'name')));
        $this->loadModel('Post');
        $clientsIds = [];
        $clientsAddress = [];
        for ($i = 0, $iMax = count($clients); $i < $iMax; $i++) {
            $clients[$i]['Client']['addresses'] = $clients[$i]['Address'] ?: [];
            $clients[$i]['Client']['tags'] = $clients[$i]['ItemsTag'];
            unset($clients[$i]['Staff']);
            unset($clients[$i]['Country']);
            unset($clients[$i]['Address']);
            unset($clients[$i]['ClientDetail']);
            unset($clients[$i]['InvoicePayment']);
            unset($clients[$i]['CustomModel']);
            // This is the main address added to addresses Array to save all addresses in one place
            array_unshift($clients[$i]['Client']['addresses'], [
                'id' => 0,
                'address1' => $clients[$i]['Client']['address1'],
                'address2' => $clients[$i]['Client']['address2'],
                'city' => $clients[$i]['Client']['city'],
                'state' => $clients[$i]['Client']['state'],
                'postal_code' => $clients[$i]['Client']['postal_code'],
                'country_code' => $clients[$i]['Client']['country_code'],
            ]);
            if ($enable_multiple_addresses) {
                $clientsIds[] = $clients[$i]['Client']['id'];
                $clientsAddress[ $clients[$i]['Client']['id']] = $clients[$i]['Client']['addresses'];
            } else {
                $clients[$i]['Client']['default_address'] = false;
            }
            // Loop Over client tags and add the tag name
            foreach ($clients[$i]['Client']['tags'] as &$tag) {
                $tag['name'] = $tags[$tag['tag_id']];
            }

            if(!empty($clients[$i]['AttachmentsByFieldKey'])){
                $attachment = \Izam\Aws\Aws::getPermanentUrl($clients[$i]['AttachmentsByFieldKey'][0]['path']);
                $clients[$i]['Client']['photo_full_path'] = $attachment;
                $clients[$i]['Client']['photo_thumb_full_path'] = $attachment;
            }
        }

        if (!empty($clientsIds)) {
            $addresses = $ClientModel->getClientsDefaultAddress($clientsIds, $clientsAddress);
        }

        for ($i = 0, $iMax = count($clients); $i < $iMax; $i++) {
            if (!empty($addresses[$clients[$i]['Client']['id']])) {
                $clients[$i]['Client']['default_address']= $addresses[$clients[$i]['Client']['id']];
            } else {
                $clients[$i]['Client']['default_address'] = [
                    'id' => 0,
                    'address1' => $clients[$i]['Client']['address1'],
                    'address2' => $clients[$i]['Client']['address2'],
                    'city' => $clients[$i]['Client']['city'],
                    'state' => $clients[$i]['Client']['state'],
                    'postal_code' => $clients[$i]['Client']['postal_code'],
                    'country_code' => $clients[$i]['Client']['country_code'],
                ];
            }
        }
        return $clients;
    }

    private function getBranchCondition($table) {
        if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)) {
            if (getCakeSession(BRANCH_TRANSACTIONS_KEY) && getCakeSession(BRANCH_TRANSACTIONS_KEY) !== '-1') {
                return ' AND ' . $table . '.branch_id IN (' . getCakeSession(BRANCH_TRANSACTIONS_KEY) . ') ';
            } else if (getCakeSession(BRANCH_TRANSACTIONS_KEY) === '-1') {
                return ' AND ' . $table . '.branch_id IN (' . implode(',', array_keys(getStaffBranchesSuspended())) . ') ';
            }
        }
        return '';
    }

    public function formatClientPhoneForSocialMediaShare($countryCode, $phone) {
        $CountryCodeList = new \Izam\View\Form\Element\CountryCodeList();
        $codeList = $CountryCodeList->list();

        $phoneCodeList = array_column($codeList, 'phone', 'code');
        $phoneCode = $phoneCodeList[$countryCode] ?? null;

        if ($phoneCode === null) {
            $defaultCountryCode = getCurrentSite('country_code');
            $phoneCode = $phoneCodeList[$defaultCountryCode] ?? null;
        }

        if (str_starts_with($phone, '00')) {
            $phone = substr($phone, 2);
        } elseif ($phone[0] !== '+') {
            if (str_starts_with($phone, ltrim($phoneCode, '+'))) {
                $phone = '+' . $phone;
            }
            if ($phone[0] !== '+') {
                $phone = ltrim($phone, '0');
                $phone = $phoneCode . $phone;
            }
        }

        return $phone;
    }

    public function getClientTotalBalance($clientId, $currencyCode) {
        $OpenInvoices = $this->getUnpaid($clientId, $currencyCode);
        $Creditlist = $this->getAllCredit($clientId, $currencyCode);
        return array_values($OpenInvoices)[0] - array_values($Creditlist)[0];
    }

     /**
     * Resets country-related fields and default currency code for all clients after industry cloning.
     *
     * This method updates all client records by setting the following fields:
     * - default_currency_code
     * - country_code
     * - secondary_country_code
     *
     * The values are taken from the currently authenticated owner's profile.
     *
     * @return void
     */
    public function resetClientLocationAndCurrencyAfterClone() {
        // Get the authenticated owner's country and currency codes
        $ownerCurrencyCode = getAuthOwner('currency_code');
        $ownerCountryCode  = getAuthOwner('country_code');

        // Proceed only if a currency code is available
        if (!empty($ownerCurrencyCode)) {
            $this->updateAll(
                array(
                    'Client.default_currency_code'   => "'" . $ownerCurrencyCode . "'",
                    'Client.country_code'            => "'" . $ownerCountryCode . "'",
                    'Client.secondary_country_code'  => "'" . $ownerCountryCode . "'",
                )
            );
        }
    }

    function get_sales_order_count($clientId){

        $this->loadModel('Invoice');
        $conditions = ['Invoice.client_id' => $clientId, 'Invoice.type' => InvoiceTypeUtil::SALES_ORDER];

        if (isOwner()) {
            return $this->Invoice->find('count', ['conditions' => $conditions]);
        }

        $permissions = [SALES_ORDER_VIEW_ALL_SALES_ORDER, SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER];
        if (!check_permission($permissions)) {
            return 0;
        }

        $hasPermission = !check_permission(SALES_ORDER_VIEW_ALL_SALES_ORDER) && check_permission(SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER);
        if ($hasPermission) {
            $staffId = getAuthOwner('staff_id');
            $conditions['Invoice.staff_id'] = $staffId;
        }
        return $this->Invoice->find('count', ['conditions' => $conditions]);
    }

}

