<?php

use Izam\Daftra\Journal\Services\AutoAccountFinder;
use Izam\Daftra\Journal\Services\JournalAccountsBalanceCalculator;

use GuzzleHttp\Client as GuzzleHttpClient;
use GuzzleHttp\Cookie\CookieJar as GuzzleHttpCookieJar;
use GuzzleHttp\Exception\ClientException;
use App\Utils\BankProviderUtil;

class Treasury extends AppModel {

    var $applyBranch = ['onFind' => false, 'onSave' => true];
    const TYPE_BANK = 1;
    const TYPE_PERSONAL = 2;
    const TYPE_TREASURY = 3;
    
    var $actsAs = array(
              'journal' => array(
                    'is_account'=>true,
                 ),
          );
    
    var $name = 'Treasury';
    var $filters = array();
    var $belongsTo = [];
    // var $hasMany = array(
    //     'StockTransaction' => array('className' => 'StockTransaction', 'foreignKey' => 'store_id', 'order' => 'id asc', 'dependant' => true),
    // );
    var $hasOne = [] ;  
    var $validate = [];
    function __construct($id = false, $table = null, $ds = null) {
        parent::__construct($id, $table, $ds);
        $this->validate = ['name' => ['rule' => 'notempty' , 'message' => __('Required', true)  ]
            ];
    }

    /**
     * Trigger the required events before delete the model.
     *
     * @param bool $cascade
     *
     * @return bool
     */
    public function beforeDelete($cascade = true)
    {
        parent::beforeDelete($cascade);
        $treasury = $this->findById($this->id);
        $metainfo = json_decode(
            $treasury['Treasury']['metainfo'] ?? '{}',
            true
        );

        if (isset($metainfo['callback']['connection_id'])) {
            $this->syncDisconnectProvider(
                metainfo: $metainfo,
                siteDomain: getCurrentSite('subdomain')
            );
        }

        return true;
    }

    function get_types ( ){
        return [
            Treasury::TYPE_TREASURY => __("Treasury",true),
            Treasury::TYPE_BANK => __("Bank Account" , true),
            // Treasury::TYPE_PERSONAL => __("Personal" , true),
            
        ] ;
    }
    function get_primary () {
        $this->loadModel ( 'Staff');
        $staff_id = getAuthOwner('staff_id') ;
        $staff = $this->Staff->find('first' , ['conditions' => ['Staff.id' => $staff_id]] ) ;
        $this->loadModel ( 'ItemPermission');
        $hisWithDrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW);

        $hisDepositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT);

        if ( empty ($staff)){
            $default_treasury_id = NULL ; 
        }else {
            $default_treasury_id = $staff['Staff']['default_treasury_id'];

        }

        if ( empty  ( $default_treasury_id ) || !(in_array($default_treasury_id, array_keys($hisDepositTreasuries)) || in_array($default_treasury_id, array_keys($hisWithDrawTreasuries))) )
        {

            $temp =$this->find('first' , ['conditions' => ['is_primary' => 1]]);
            if ( empty ( $temp ) ) {
                $default_treasury_id = $this->find ( 'first',['conditions' => ['Treasury.active' => 1]] )['Treasury']['id'];
                if ( empty ( $default_treasury_id ) ) {
                    $default_treasury_id = 1; 
                }
            }else {
                $default_treasury_id = $temp['Treasury']['id'];
            }
        }

        if(empty($default_treasury_id)) $default_treasury_id=1; 
        return $default_treasury_id;
    }

    function getPrimaryForStaff($type, $staff_id = null)
    {
        $this->loadModel('Staff');
        if (empty($staff_id)) {
            $staff_id = getAuthOwner('staff_id');
        }
        $this->loadModel('ItemPermission');
        if ($type == ItemPermission::PERMISSION_WITHDRAW) {
            $hisTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY, ItemPermission::PERMISSION_WITHDRAW, $staff_id);
        } else {
            $hisTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY, ItemPermission::PERMISSION_DEPOSIT, $staff_id);
        }

        return $this->find('first', ['conditions' => ['Treasury.active' => 1, 'Treasury.id in ('. implode(',', array_keys($hisTreasuries)). ')']])['Treasury']['id'] ?? 1;
    }

    public function beforeSave($options = array()) {
        parent::beforeSave($options);
        if ( empty ( $this->data['Treasury']['active'])|| $this->data['Treasury']['active']=="")
        {
            $this->data['Treasury']['active'] = 0 ;
        }
        if ( empty ( $this->data['Treasury']['type'] ) || !in_array($this->data['Treasury']['type'], array_keys ($this->get_types ( ) ) )  ) {
            $this->data['Treasury']['type'] = Treasury::TYPE_TREASURY;
        }
        if ( empty ( $this->data['Treasury']['type_name']))
        {
            $this->data['Treasury']['type_name'] = null ; 
        }
        if ( empty ( $this->data['Treasury']['type_account_number']))
        {
            $this->data['Treasury']['type_account_number'] = null ; 
        }
        
        if($this->data['Treasury']['type']==Treasury::TYPE_BANK)
        {
            $this->loadModel('Journal');
            Journal::$auto_accounts['treasury']['next_journal_cat_type']= 'banks';
        }
        return true;
    }
    function get_list ( $active = 1 , $include_treasury_id = null,$check_permission=true) {
        $conditions = [] ; 
        if ( $active == 1) {
            $conditions['active'] = 1;
        }
        if ( !check_permission(CHANGE_DEFAULT_TREASURY) && $check_permission  ){
            $conditions['Treasury.id'] = $this->get_primary() ;
        }
        $treasuries = $this->find('list' ,['conditions'=> $conditions] ) ;

        if ( !empty ( $include_treasury_id )  )
        {
             $included = $this->find ( 'first' , ['fields' =>'id,name' ,   'conditions' => ['Treasury.id' => $include_treasury_id]]);
             
             if ( !check_permission(CHANGE_DEFAULT_TREASURY)  ){
                 $treasuries = [$included['Treasury']['id'] => $included['Treasury']['name'] ] ;
             }else {
                 $treasuries[$included['Treasury']['id']] =  $included['Treasury']['name'];
             }
        }
//        echo $include_treasury_id ; 
        return $treasuries;
    }
//    function get_balance ( $id ) {
//        if ( empty ( $id ) ) {
//            return  0 ; 
//        }
//        $this->loadModel ('JournalAccount');
//        $journal_account_id = $this->JournalAccount->find ( 'first' , ['recursive' => -1 ,  'conditions' => ['entity_id' => $id , 'entity_type' => 'treasury'] ]);
        
//        $this->loadModel ( 'Expense');
//        $this->loadModel ( 'InvoicePayment');
//        $this->loadModel ( 'PurchaseOrderPayment');
//        $this->loadModel ( 'TreasuryTransfer');
//        
//        //Expenses / Incomes
//        $expense_sum = $this->Expense->find ( 'first' , ['fields' => 'sum(amount) as am' , 'conditions' =>  [ 'treasury_id' => $id ,  'is_income <> 1'  ] ])[0]['am'] ; 
//        $income_sum = $this->Expense->find ( 'first' , ['fields' => 'sum(amount) as am' , 'conditions' =>  [ 'treasury_id' => $id ,  'is_income' => 1  ] ])[0]['am'] ; 
//        
//        //Payments
//        $i_payment  = $this->InvoicePayment->find ( 'first' , ['fields' => 'sum(amount) as am' , 'conditions' => ['treasury_id' => $id ,'amount' , 'status' => PAYMENT_STATUS_COMPLETED] ])[0]['am'];
//        $po_payment  = $this->PurchaseOrderPayment->find ( 'first' , ['fields' => 'sum(amount) as am' , 'conditions' => ['treasury_id' => $id ,'amount' , 'status' => PAYMENT_STATUS_COMPLETED] ])[0]['am'];
//        
//        //Transfers
//        $to_transfers = $this->TreasuryTransfer->find ( 'first' , ['fields' => 'sum(balance) as am' , 'conditions' => ['treasury_id' => $id] ])[0]['am'];
////        $from_transfers = $this->TreasuryTransfer->find ( 'first' , ['fields' => 'sum(balance) as am' , 'conditions' => ['from_treasury_id' => $id] ])[0]['am'];
//        
//        return  $i_payment + $income_sum +$to_transfers  - $expense_sum -  $po_payment ;//- $from_transfers ; 
//    }
    /**
     * Deletes the transfer with its transactions
     * @param int $id
     */
    function delete_transfer ( $id ) {

        $this->loadModel ( 'TreasuryTransfer');
        $this->loadModel ( 'TreasuryTransferTransaction');
        if ( $this->TreasuryTransferTransaction->deleteAll ( ['TreasuryTransferTransaction.treasury_transfer_id' => $id ]))
        {
             $this->TreasuryTransfer->delete ($id);
             $this->TreasuryTransfer->delete_auto_journals($id);
             return true;
        }else {
            return false ;
        }
    }
    /**
     * Function to transfer balance 
     * @param int $from_treasury_id
     * @param int $to_treasury_id
     * @param double $balance
     * @param string $from_currency_code
     * @param string $to_currency_code
     * @param double $currency_rate
     * @param string $transfer_date
     * @param string $treasury_notes
     * @param int $transfer_id
     * @return boolean
     */
    function transfer_balance ($from_treasury_id ,$to_treasury_id  ,  $balance , $from_currency_code ,$to_currency_code ,  $currency_rate = null , $transfer_date = null , $treasury_notes = null , $transfer_id = null   ){
        $validation_errors = [] ;
        if ( empty ( $from_currency_code ) ){
            $validation_errors['from_currency_code'] = __("Required" , true );
        } 
        if ( empty ( $to_currency_code ) ){
            $validation_errors['to_currency_code'] = __("Required" , true );
        }
        if ( empty ( $from_treasury_id ) )
        {
            $validation_errors['from_treasury_id'] = __("Required" , true );
        }
        if ( empty ( $to_treasury_id ) )
        {
            $validation_errors['to_treasury_id'] = __("Required" , true );
        }
        if ( empty ( $balance ) )
        {
            $validation_errors['balance'] = __("Required" , true );
        }
        if ( !empty ($validation_errors) ) {
            return $validation_errors ; 
        }
        if ($from_treasury_id == $to_treasury_id && $from_currency_code ==$to_currency_code )
        {
            return false ; 
        }
        if ( $balance <= 0 )
        {
            return false ;
        }
        if ( empty ( $currency_rate) )
        {
            App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
            $currency_rate = CurrencyConverter::index($from_currency_code, $to_currency_code, empty($transfer_date) ?  date("Y-m-d H:i:s") : $transfer_date);
        }
        $from_treasury = $this->findById ( $from_treasury_id ) ;
        $to_treasury = $this->findById ( $to_treasury_id ) ;
        
        if ( empty ( $from_treasury )|| empty ($to_treasury ))
        {
            return -1 ; 
        }
        $transfer_date_formated = date("Y-m-d H:i:s");
        if ( !empty ( $transfer_date ) ){
            $transfer_date_formated = $this->formatDateTime($transfer_date ) ;
        }
        
        $this->loadModel ('TreasuryTransfer' );
        $this->loadModel ('TreasuryTransferTransaction' );
        
        if ( empty ( $transfer_id )){
            $this->TreasuryTransfer->create() ; 
        }else {
            $treasury_transfer = $this->TreasuryTransfer->findById ( $transfer_id);
            $this->TreasuryTransfer->id = $transfer_id;
        }
        
        $transfer_data = ['TreasuryTransfer' => [
            'from_currency_code' => $from_currency_code,
            'notes' => $treasury_notes,
            'to_currency_code' => $to_currency_code,
            'balance' => $balance,
            'transfer_date' => $transfer_date_formated,
            'staff_id' =>getAuthOwner('staff_id') ,
            'from_treasury_id' => $from_treasury_id ,
            'to_treasury_id' => $to_treasury_id ,
            'currency_rate' => $currency_rate ]];
        $this->TreasuryTransfer->save ( $transfer_data );

        $treasury_transfer_id = $this->TreasuryTransfer->id ; 
        
        if ( empty ( $transfer_id))
        {
            $this->TreasuryTransferTransaction->create() ; 
        }else {
            $this->TreasuryTransferTransaction->id = $this->TreasuryTransferTransaction->find ( 'first' ,['recursive' => -1 ,'fields' => 'id' ,  'conditions' =>[ 'treasury_transfer_id'=>$transfer_id , 'treasury_id' =>$treasury_transfer['TreasuryTransfer']['from_treasury_id'] ] ] )['TreasuryTransferTransaction']['id'];
            
        }
        
        $transfer_transaction_data_from = ['TreasuryTransferTransaction' => ['treasury_transfer_id' => $treasury_transfer_id , 'treasury_id' => $from_treasury_id , 'balance' => $balance*-1 , 'currency_code' => $from_currency_code]];
        $this->TreasuryTransferTransaction->save ( $transfer_transaction_data_from );
        
        if ( empty ( $transfer_id))
        {
            $this->TreasuryTransferTransaction->create() ; 
        }else {
            $this->TreasuryTransferTransaction->id = $this->TreasuryTransferTransaction->find ( 'first' ,['recursive' => -1 ,'fields' => 'id' ,  'conditions' =>[ 'treasury_transfer_id'=>$transfer_id , 'treasury_id' =>$treasury_transfer['TreasuryTransfer']['to_treasury_id'] ] ] )['TreasuryTransferTransaction']['id'];
            
        } 
        $transfer_transaction_data_to = ['TreasuryTransferTransaction' => ['treasury_transfer_id' =>$treasury_transfer_id ,'treasury_id' => $to_treasury_id , 'balance' => ($balance*$currency_rate )  , 'currency_code' => $to_currency_code]];
        return ($this->TreasuryTransferTransaction->save ( $transfer_transaction_data_to )?$this->TreasuryTransfer->id :false );
    }
    /**
     * returns every treasury with its currencies list 
     * @param int $treasury_id
     * @param Array $all_currencies all the currencies to be used instead of getting it twice .
     * @return Array every treasury with its currencies list 
     */
    function get_treasury_currencies ($treasury_id = null , $all_currencies = []  ) {
        $where = '';

        if ( !empty ( $treasury_id ) ){
            $where .= " and ja.entity_id=$treasury_id";
            $routeWhere  = " and jar.entity_id=$treasury_id";
        }
        $groupBy = " group by ja.id";
        $routeGroupBy = " group by jar.id";
        $q = "select GROUP_CONCAT(DISTINCT(currency_code)) as currency_group , ja.entity_id from  journal_transactions jt "
                . "inner join  journal_accounts ja on jt.journal_account_id = ja.id and ja.entity_type='treasury' $where $groupBy "
                . "union 
                    select GROUP_CONCAT(DISTINCT(currency_code)) as currency_group , jar.entity_id from  journal_transactions jt 
                    inner join  journal_account_routes jar on jt.journal_account_id = jar.account_id and jar.entity_type='treasury' $routeWhere 
                    $routeGroupBy";

        $treasury_currencies =$this->flat_query_results($q);
        $treasurieIds = [];
        foreach($treasury_currencies as $k => $data)
        {
            $treasurieIds[] = $data['entity_id'];
        }

        $treasuriesWithNoTransactions = $this->find('list', ['conditions' => ['NOT' => ['Treasury.id' => $treasurieIds]]]);
        $treasuriesWithNoTransactionsCurrencies = [];
        $defaultCurrency = $this->get_default_currency();
        foreach ($treasuriesWithNoTransactions as $k => $treasury)
        {
            $treasuriesWithNoTransactionsCurrencies[$k][$defaultCurrency] = $defaultCurrency;
        }

        $ret = [] ;
        if ( empty ($all_currencies ) ) {
            $this->loadModel ( 'Currency' ) ; 
            $all_currencies = $this->Currency->getCurrencyList ( );
        }
        foreach ( $treasury_currencies as $tc ) {
            $tc_curr = explode(',',$tc['currency_group']); 
            foreach ( $tc_curr  as $tt ) {
                $ret[$tc['entity_id']][$tt] = $all_currencies[$tt];
            }
        }
        return $ret + $treasuriesWithNoTransactionsCurrencies;
    }
    /**
     * gets the currency balances for each treasury in the $treasuries_list
     * @param Array $treasuries_list list of the treasuries that you want to get the currency balances for
     * @return Array returns every treasury in the treasuries list with currency and its balance for each currency in the treasury 
     */
    function get_currency_balances ( $treasuries_list){
        $this->loadModel ('JournalAccount');
        $this->loadModel ('Journal');
        $ret = []  ; 
        foreach ( $treasuries_list as $k => $l ) {
            $journal_account_id = $this->Journal->get_auto_account(['entity_id' => $k,'entity_type' => 'treasury']);//$this->JournalAccount->find ( 'first' , ['fields' => 'id' ,  'recursive' => -1  ,  'conditions' => ['entity_id' => $k , 'entity_type' => 'treasury'] ]);
            $ret[$k] = $this->Journal->calculate_account_currency($journal_account_id['JournalAccount']['id'], null, false, true);
        }
        return $ret ; 
    }
    /**
     * gets total balance for each treasury in treasuries list 
     * @param Array $treasuries_list
     * @return Array
     */
		function get_total_balance($treasuries_list)
		{
			$this->loadModel('Journal');
			$this->loadModel('JournalAccount');
			$this->loadModel('JournalTransaction');
			foreach ($treasuries_list as $k => $l) {
				$journal_account = $this->Journal->get_auto_account(['entity_id' => $k, 'entity_type' => 'treasury']);
				$ret[$k] = $this->Journal->recaculate_account_total($journal_account['JournalAccount']['id'])['net'];
			}
			return $ret;
		}


    function convert_treasury_currency_to_local($treasury)
    {
        $treasury_id = $treasury['Treasury']['id'];
        $this->loadModel('JournalAccount');
        $this->loadModel('Journal');
        $journal_account_id = $this->Journal->get_auto_account(['entity_id' => $treasury_id, 'entity_type' => 'treasury']);
        $accountBalance=[];
        if ($journal_account_id) {
            $journal_account_id=  is_array($journal_account_id)?$journal_account_id['JournalAccount']['id']:$journal_account_id;
            $request = new \App\Services\Journal\JournalAccountBalanceCalculatorRequest([$journal_account_id]);
            if(ifPluginActive(BranchesPlugin)) {
                $request->setBranchId(getActiveAndSuspendedBranchIds());
            }
            $calculator = new JournalAccountsBalanceCalculator(getPDO());
            $accountBalance = $calculator->calculateGroupByCurrency($request);
        }
        $data = array_pop($accountBalance);
        $sum_local_cur = 0;
        $sum_treasury_currency_value = 0;
        $system_currency =  $this->get_default_currency();
        $treasury_currency = $treasury['Treasury']['currency_code'];
        foreach ($data as $key => $v) {
            $data[$key] = $v['total_debit'] - $v['total_credit'];
            $currencyValue = $v['currency_debit'] - $v['currency_credit'];
            $sum_local_cur += $data[$key];
            $treasury_currency_value = 0;
            if (!empty($treasury_currency) && $system_currency !=$treasury_currency) {
                $rate_val = CurrencyConverter::index($key, $treasury_currency);
                $treasury_currency_value = $currencyValue * $rate_val;
            }else{
                $treasury_currency_value = $data[$key];
                
            }
            $sum_treasury_currency_value += $treasury_currency_value;
            $data[$key] = [$key => $currencyValue, 'to_local' => $data[$key],'treasury_currency_value'=>$treasury_currency_value];
        }
        
        return ['treausry_currencies'=>$data,'sum_local_cur'=>$sum_local_cur,'sum_treasury_currency_value'=>$sum_treasury_currency_value];
    }

    /**
     * @return bool
     */
    public function hasChequeBooks()
    {
        $count = $this->query("SELECT COUNT(bank_id) as hasBooks FROM cheque_books WHERE bank_id = $this->id",false);
        $result = array_shift(array_shift(array_shift($count)));
        return !!(int)$result;
    }
    public function hasLoans()
    {
        $row = $this->query("SELECT COUNT(treasury_id) as hasloan FROM loans WHERE deleted_at is null and treasury_id = $this->id",false);
        debug($row);
        if(!isset($row[0])){
            return false;
        }elseif ($row[0][0]['hasloan']>0){
            return  true;
        }else{
            return false;
        }

    }
    function get_banks_list( $active = 1) {
        $conditions['type'] = 1;

        if ($active == 1) {
            $conditions['active'] = 1;
        }

        if (!check_permission(CHANGE_DEFAULT_TREASURY)) {
            $conditions['Treasury.id'] = $this->get_primary() ;
        }

        return $this->find('list', ['conditions'=> $conditions]) ;
    }

    
    function can_update_treasury_currency($treasury)
    {
        if ($treasury['Treasury']['type'] != Treasury::TYPE_BANK) {
           return false;
        }

        if (empty($treasury['Treasury']['currency_code'])) { 
            return true;
        } else {
            // The Currency will be editable normally if there’s no System transaction takes place for the bank 
            //and if there’s at least one transaction to the bank so that the field cant be editable 
            $this->loadModel('JournalAccount');
            $ja = $this->JournalAccount->find('first', ['recursive' => -1, 'fields' => 'id', 'conditions' => ['entity_type' => 'treasury', 'entity_id' => $treasury['Treasury']['id']]]);

            if ($ja) {
                $account_id = $ja['JournalAccount']['id'];
                $this->loadModel('JournalTransaction');
                $transaction = $this->JournalTransaction->find('first', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['journal_account_id' => $account_id]]);
                if ($transaction) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * this function to list treasury permission_level with its entity 
     * ex 
     * (permission_withdraw or permission_deposit) with staff or branch or role 
     * 
     */
    public function  treasury_permissions( $item_id = null)
    {
        $item_type = ItemPermission::ITEM_TYPE_TREASURY;
        $this->loadModel('ItemPermission');
        $ItemPermissions = $this->ItemPermission->find('all', ['conditions' => ['item_id' => $item_id, 'item_type' => $item_type]]);

        $permission_withdraw = [];
        $permission_deposit = [];

        foreach ($ItemPermissions as $permission) {
            $record=$this->ItemPermission->getPermissionGroupRecord($permission['ItemPermission']['group_type'],$permission['ItemPermission']['group_id']);
            if ($permission['ItemPermission']['permission_level'] == ItemPermission::PERMISSION_WITHDRAW) {
                $permission_withdraw['group_type'] = $permission['ItemPermission']['group_type'];
                $permission_withdraw['entities'][] = $record;
            } else {
                $permission_deposit['group_type'] = $permission['ItemPermission']['group_type'];
                $permission_deposit['entities'][] = $record;
            }
        }
        $permissions = ['permission_withdraw' => $permission_withdraw, 'permission_deposit' => $permission_deposit];
        return   $permissions;
    }


    function getFilters() {
        
        $filters = array(
            'name' => array(),
            'type' => array(),
            'active' => array(),
            'currency_code' => array(),
        );

        return $filters;
    }

    /**
     * Sync banking reconciliation data.
     *
     * @param array $bankingSession
     * @param string $provider
     *
     * @return mixed
     */
    public function syncBankingReconciliationCustomerId(mixed $bankingSession)
    {
        if (!isset($bankingSession['connect']['customer'])) {
            return false;
        }

        $bankingReconciliation = settings::getValue(
            ExpensesPlugin,
            'banking_reconciliations_meta_info'
        );

        $data = [];

        if ($bankingReconciliation) {
            $data = json_decode(
                $bankingReconciliation,
                true
            );
        }

        $data[
            $bankingSession['provider']
        ] = $bankingSession['connect']['customer'];

        settings::setValue(
            ExpensesPlugin,
            "banking_reconciliations_meta_info",
            json_encode($data)
        );

        return $data;
    }

    /**
     * Handle logic to properly set the metainfo column data.
     * note that if $metainfo variable is not null, this means
     * that we are editing an existing treasury.
     *
     * @param CakeSession $session
     * @param array $data
     * @param array|null $metainfo
     *
     * @return array
     */
    public function handleBankReconciliations(
        CakeSession $session,
        array $data,
        ?array $metainfo,
    ) {
        $bankingSession = $session->read('BankingReconciliations');

        // if the user is not trying to re-connect to another provider
        // via the edit page set the banking session to the default
        // metainfo column data
        if (!$bankingSession && $metainfo) {
            $bankingSession = $metainfo;
        }

        // Check for session conflict
        // This is to fix the case: when the user connect to a provider
        // and then opens a new tab/window and try to connect to another provider
        if (isset($bankingSession['provider_internal_bank_id'])
            && $bankingSession['provider_internal_bank_id'] != $data['portal_provider_internal_bank_id']
        ) {
            return [
                'status' => 'ERR',
                'error' => null,
                'errors' => [
                    'The provider connection session is expired, please try again'
                ]
            ];
        }

        $bankIntegratoinInfo = [];
        if ((int) $data['portal_bank_id'] > 0) {
            $bankIntegratoinInfo['portal_bank_id'] = $data['portal_bank_id'];
        }

        if ((int) $data['portal_bank_provider_id'] > 0) {
            $bankIntegratoinInfo['portal_bank_provider_id'] = $data['portal_bank_provider_id'];
        }

        $this->loadModel('Bank');
        $portalBank = $this->Bank->find('first' , [
            'conditions' => [
                'Bank.id' => $bankIntegratoinInfo['portal_bank_id'],
                'Bank.bank_status' => 'enabled',
            ]
        ]);

        $bankIntegratoinInfo['portal_bank'] = $portalBank['Bank'];

        // Meaning that the user selected a bank without connecting
        // to a specific provider, or when he enter a free-text bank
        if (!$bankingSession) {
            return [
                'data' => [
                    'bank' => $bankIntegratoinInfo
                ]
            ];
        }

        $bankingSession['bank'] = $bankIntegratoinInfo;

        $this->syncBankingReconciliationCustomerId(
            $bankingSession
        );

        $this->Bank->syncSelectedAndConnectedBanks(
            bankId: (int) $data['portal_bank_id'] ?? 0,
            bankPorviderId: (int) $data['portal_bank_provider_id'] ?? 0,
        );

        $session->write('BankingReconciliations', null);

        return [
            'status' => 'OK',
            'data' => $bankingSession
        ];
    }

    /**
     * Sync the disconnection process for a given treasury on delete event.
     *
     * @param array $metainfo
     * @param string $siteDomain
     *
     * @return void
     */
    public function syncDisconnectProvider(
        array $metainfo,
        string $siteDomain,
    ): void {

        /**
         * @see https://stackoverflow.com/questions/********/guzzle-hangs-apache-when-passing-phpsessid-session-cookie
         */
        session_write_close();

        $cookieJar = GuzzleHttpCookieJar::fromArray([
            'XSRF-TOKEN' => $_COOKIE['XSRF-TOKEN'],
            'OISystem' => $_COOKIE['OISystem'],
            'laravel_session' => $_COOKIE['laravel_session'],
            'portal_language' => $_COOKIE['portal_language'],
        ], $siteDomain);

        $client = new GuzzleHttpClient([
            'headers' => [
                'content-type' => 'application/json',
                'accept' => 'application/json'
            ],
            'cookies' => $cookieJar,
        ]);

        try {
            $request = $client->get(sprintf(
                "https://%s/v2/owner/banking/reconciliation/connect/%s/disconnect",
                $siteDomain,
                $metainfo['provider'],
            ), [
                'query' => [
                    'connection-id' => $metainfo['callback']['connection_id']
                ],
                'verify' => false,
            ]);
        } catch (ClientException $e) {
            \Rollbar\Rollbar::error('Disconnect Bank Provider Error', ['exception' => $e,]);
        }
    }

    public function getSelectedProviders(array $metainfo)
    {
        // dd($metainfo);
        if (isset($metainfo['bank']['portal_bank_id']) == false) {
            return false;
        }
        $this->loadModel('Bank');
        $providers = $portalBank = $this->Bank->find('first' , [
            'conditions' => [
                'Bank.id' => $metainfo['bank']['portal_bank_id'],
                'Bank.bank_status' => 'enabled',
            ]
        ]);

        foreach ($providers['BankProvidersToBank'] as &$provider) {
            $provider['provider_name'] = BankProviderUtil::PROVIDERS[
                $provider['bank_provider']
            ];
        }

        return $providers['BankProvidersToBank'] ?? null;
    }
}

