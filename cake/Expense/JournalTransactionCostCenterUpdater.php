<?php

namespace App\Expense;

class JournalTransactionCostCenterUpdater
{
    public static function update($journal, $data, $filterByType, $accounts)
    {

        $journalObj = GetObjectOrLoadModel('Journal');
        $journal_id = $journal['Journal']['id'];
        $default_currency = $journalObj->get_default_currency();
        //If is null please don't proceed
        if(!is_array($data['JournalTransaction'])) {
            return false;
        }
        $transactions = self::filterEmptyJournalTransactions($data['JournalTransaction']);
        if(empty($transactions)) {
            return false;
        }
        $journalTransactions = self::filterJournalTransactionsByType($journal['JournalTransaction'], $filterByType);
        /**
         * To preserve the original order for journal transactions in the expenses so we have to exclude the taxes out
         */
        $journalTransactions = array_values(array_filter($journalTransactions, function($transaction) {
            return !str_starts_with($transaction['subkey'], 'tax_');
        }));
 
        usort($journalTransactions, function($a, $b) {
            return $a['display_order'] - $b['display_order'];
        });

        $costCenter = GetObjectOrLoadModel('CostCenter');
        $costCenterTransactionObj = GetObjectOrLoadModel('CostCenterTransaction');
        foreach ($data['JournalTransaction'] as $transactionIndex => $transaction) {
            $costCenterTransaction = $costCenterTransactionObj->find('first', ['conditions' => ['CostCenterTransaction.journal_id' => $journal_id,'CostCenterTransaction.is_auto' => false,'CostCenterTransaction.cost_center_id' => $accounts[$transactionIndex]['cost_center'], 'CostCenterTransaction.journal_transaction_id' => $journalTransactions[$transactionIndex]['id']]]);
            if($costCenterTransaction && count($costCenterTransaction) > 1 && is_numeric($transaction['cost_center'])) {
                $costCenterTransactionObj->deleteAll(['CostCenterTransaction.journal_id' => $journal_id, 'CostCenterTransaction.journal_transaction_id' => $journalTransactions[$transactionIndex]['id']]);
                $costCenterTransaction = false;
            }

            if(isset($transaction['cost_center']) && is_numeric($transaction['cost_center']) && ($transaction['cost_center'] != $accounts[$transactionIndex]['cost_center'] || !$costCenterTransaction)) {
                $account_id = $journalTransactions[$transactionIndex]['journal_account_id'] == "default" ? "" : $journalTransactions[$transactionIndex]['journal_account_id'];
                if(!$costCenterTransaction) {
                    $costCenterTransaction = self::createCostCenterTransactionRecord($journal_id, $account_id, $filterByType, $transaction, $journalTransactions[$transactionIndex], $default_currency);
                    $costCenter->save_manual_cost_transactions($costCenterTransaction);
                } else {
                    $costCenterTransactionObj->update($costCenterTransaction['CostCenterTransaction']['id'], ['cost_center_id' => $transaction['cost_center']]);
                }
            } elseif(empty($transaction['cost_center'])) {
                $costCenterTransactionObj->deleteAll(['CostCenterTransaction.journal_id' => $journal_id,'CostCenterTransaction.is_auto' => false, 'CostCenterTransaction.journal_transaction_id' => $journalTransactions[$transactionIndex]['id']]);
            }
        }


    }

    public static function save($expenseId, $data, $type, $filterByType)
    {

        $journal = GetObjectOrLoadModel('Journal');
        $expense = GetObjectOrLoadModel('Expense');
        $default_currency = $journal->get_default_currency();
        //If is null please don't proceed
        if(!is_array($data['JournalTransaction'])) {
            return false;
        }
        $transactions = self::filterEmptyJournalTransactions($data['JournalTransaction'], $type);
        if(empty($transactions)) {
            return false;
        }
        $journal_id = $expense->get_journal_id($expenseId, $type);
        $journal->recursive = 3;
        $journal = $journal->read(null, $journal_id);
        $journalTransactions = self::filterJournalTransactionsByType($journal['JournalTransaction'], $filterByType);
        /**
         * To preserve the original order for journal transactions in the expenses so we have to exclude the taxes out
         */
        $journalTransactions = array_values(array_filter($journalTransactions, function($transaction) {
            return !str_starts_with($transaction['subkey'], 'tax_');
        }));
 
        usort($journalTransactions, function($a, $b) {
            return $a['display_order'] - $b['display_order'];
        });
        
        $costCenter = GetObjectOrLoadModel('CostCenter');

        foreach ($data['JournalTransaction'] as $transactionIndex => $transaction) {
            if(isset($transaction['cost_center']) && is_numeric($transaction['cost_center'])) {
                $account_id = $journalTransactions[$transactionIndex]['journal_account_id'] == "default" ? "" : $journalTransactions[$transactionIndex]['journal_account_id'];
                $costCenterTransaction = self::createCostCenterTransactionRecord($journal_id, $account_id, $filterByType, $transaction, $journalTransactions[$transactionIndex], $default_currency);
                $costCenter->save_manual_cost_transactions($costCenterTransaction);
            }
        }


    }

    public static function createCostCenterTransactionRecord($journal_id, $account_id, $type, $transaction, $journalTransaction, $default_currency)
    {
        $specific_cost_center = [];
        $cost_center_data = [];
        $specific_cost_center['cost_center_id'] = $transaction['cost_center'];
        if($type != "credit") {
            $specific_cost_center['debit'] = $journalTransaction['debit'];
            $specific_cost_center['currency_debit'] = $journalTransaction['currency_debit'];
            $specific_cost_center['credit'] = 0;
            $specific_cost_center['currency_credit'] = 0;
            $specific_cost_center['type'] = 'debit';

        } else {
            $specific_cost_center['credit'] = $journalTransaction['credit'];
            $specific_cost_center['currency_credit'] = $journalTransaction['currency_credit'];
            $specific_cost_center['debit'] = 0;
            $specific_cost_center['currency_debit'] = 0;
            $specific_cost_center['type'] = 'credit';
        }
        $specific_cost_center['percentage'] = 100;
        $specific_cost_center['journal_account_id'] = $account_id;
        $specific_cost_center['account_id'] = $account_id;
        $specific_cost_center['is_auto'] = false;
        $specific_cost_center['rate'] = $journalTransaction['currency_rate'];
        $specific_cost_center['currency_code'] = $journalTransaction['currency_code'];
        $specific_cost_center['journal_transaction_id'] = $journalTransaction['id'];
        $specific_cost_center['journal_id'] = $journal_id;
        $cost_center_data[0]['CostCenterTransaction'] = $specific_cost_center;

        return $cost_center_data;
    }

    public static function filterEmptyJournalTransactions($transactions)
    {
        if (!$transactions) {
            return [];
        }
        return array_filter($transactions, function ($transaction) {
            return !empty($transaction['journal_account_id']) && !empty($transaction['amount']);
        });
    }

    public static function filterJournalTransactionsByType($transactions, $filterByType)
    {
        if (!$transactions) {
            return [];
        }
        return array_values(array_filter($transactions, function ($transaction) use ($filterByType) {
            return $transaction[$filterByType] > 0;
        }));
    }

}
