<?php

namespace App\Validators\StockTransaction;

    use Requisition;
    use Izam\Daftra\Common\Utils\SettingsUtil;;


    class ProductBalanceValidator
	{
		static public $itemTypes = [
			'invoice' => ['model' => 'Invoice', 'itemModel' => 'InvoiceItem'],
			'purchase_order' => ['model' => 'PurchaseOrder', 'itemModel' => 'PurchaseOrderItem'],
			'requisition' => ['model' => 'Requisition', 'itemModel' => 'RequisitionItem'],
		];

		private $modelName;
		private $itemModelName;

		public function __construct($itemType)
		{
			\App::import('Vendor', 'settings');
			$this->modelName = self::$itemTypes[$itemType]['model'];
			$this->itemModelName = self::$itemTypes[$itemType]['itemModel'];
		}

		/**
		 * @param $newData
		 * @param $mode
		 * @param array $oldData
		 * @return bool|array
		 * validates if this product have available balance to finish the transaction
		 * if yes return true else return the index of the faulty product
		 */
		function validate(&$newData, $mode, $oldData = [])
		{
			$invalidItemIndeces = [];
			$StockTransaction = GetObjectOrLoadModel('StockTransaction');
			$Product = GetObjectOrLoadModel('Product');

			$sum_new_products = $this->sumProducts($newData);
		
			if ($oldData) {
				foreach ($oldData[$this->itemModelName] as $k) {
					$old_products[$k['product_id']] = $k;
				}

				// Sum Old Products
				$sum_old_products = $this->sumProducts($oldData);
			}
            $is_requisition = $this->modelName === "Requisition";
            if($is_requisition &&  in_array($newData[$this->modelName]['order_type'], [Requisition::ORDER_TYPE_PURCHASE_ORDER, Requisition::ORDER_TYPE_PURCHASE_REFUND])) {
                $enable_requisitions = \settings::getValue(InventoryPlugin, 'enable_requisitions_po');
            } else {
                $enable_requisitions = \settings::getValue(InventoryPlugin, 'enable_requisitions');
                $validateStockByAvailableQty = $this->isStockValidationByAvailableQty();
            }
			if ((!$enable_requisitions || $is_requisition || $validateStockByAvailableQty) && \settings::getValue(InventoryPlugin, 'disable_overdraft') && ifPluginActive(InventoryPlugin)) {
				$totaledBundleItems = [];
				if ($mode == 'add') {
					$productIds = [];
					foreach ($newData[$this->itemModelName] as $item) {
						$productIds[] = $item['product_id'];
					}
					$productIds = array_unique($productIds);
        			$products = $Product->find('list', ['conditions' => ['Product.id' => $productIds], 'fields' => ['Product.id', 'Product.raw_store_id'], 'recursive' => -1]);

					foreach ($newData[$this->itemModelName] as $i=> $item) {
						$store_id = ($item['store_id'] ?: $newData[$this->modelName]['store_id']);
						$bundleItems = $StockTransaction->getBundleItems($item['product_id']);
						foreach ($bundleItems as $bundleItem) {
							$found = false;
							foreach ($totaledBundleItems as $key => $totaledBundleItem) {
								if (isset($totaledBundleItem['product_id']) && $totaledBundleItem['product_id'] === $bundleItem['ProductBundle']['product_id'] && $totaledBundleItem['store_id'] === $products[$item['product_id']]) {
									$totaledBundleItems[$key]['quantity'] += $item['quantity'] * $bundleItem['ProductBundle']['quantity'];
									$found = true;
									break;
								}
							}
							if (!isset($found) || !$found) {
								$bundleProduct = GetObjectOrLoadModel('Product')->findById($bundleItem['ProductBundle']['product_id']);
								$totaledBundleItems[] = [
									'item' => $bundleProduct['Product']['name'] ?? null,
									'product_id' => $bundleItem['ProductBundle']['product_id'],
									'org_name' => $bundleProduct['Product']['name'],
									'quantity' => ($item['quantity'] - ($oldData['RequisitionItem'][$i]['quantity'] ?? 0))*$bundleItem['ProductBundle']['quantity'] ,
									'store_id' => $products[$item['product_id']] ?? $store_id,
									'mode' => 'deduct',
								];
							}
						}
					}
				}
				$newData[$this->itemModelName] = array_merge($newData[$this->itemModelName], $totaledBundleItems);
				$sum_new_products = $this->sumProducts($newData);
				foreach ($newData[$this->itemModelName] as $i=> $item) {
                    $store_id = ($item['store_id'] ?: $newData[$this->modelName]['store_id']);
					if (empty($oldData) || $this->isOldDataPending($oldData) ||
                        (!isset($sum_old_products[$item['product_id']][$store_id]))
                    ) {
						$old_quantity = 0;
						/**
						 * If the user select the stock validation by available qty and the requisition is created as pending by default
						 * Then we need to add its quantity
						 */
						if($validateStockByAvailableQty && $newData[$this->modelName]['id'] != '' && !$this->checkIftDraft($newData)){ 
						   $old_quantity += $sum_new_products[$item['product_id']][$store_id];	
						}
					} else {
						$old_quantity = $sum_old_products[$item['product_id']][$store_id];
					}
                    if (!$StockTransaction->check_balance_open($item['product_id'], $store_id, $sum_new_products[$item['product_id']][$store_id], $old_quantity, $item['mode'] ?? $mode)) {
                        $invalidItemIndeces[] = $i;
                    }

				}
			}
			return empty($invalidItemIndeces) ? true : $invalidItemIndeces;
		}

		private function checkIftDraft($data){
			return isset($data[$this->modelName]['draft']) && $data[$this->modelName]['draft'] == 1 || ( str_contains($data[$this->modelName]['no'] , 'draft-') && str_contains($data[$this->modelName]['hidden_no'] , 'draft-'));
		}

		/**
		 * @param $data - $this->data
		 * @return array - returns the summed array of [productId => total_quantity]
		 */
		private function sumProducts($data){
			$summed_products = [];
			foreach ($data[$this->itemModelName] as $item) {
                $storeId = isset($item['store_id']) ? $item['store_id'] : $data[$this->modelName]['store_id'];
				if (isset($summed_products[$item['product_id']][$storeId])) {
					$summed_products[$item['product_id']][$storeId] += $item['quantity'];
				} else {
					$summed_products[$item['product_id']][$storeId] = $item['quantity'];
				}
			}
			return $summed_products;
		}

		/**
		 * This is used to indicate whnether the Operation of the Old Data was done on a Pending Stock or Not
		 * @param $oldData
		 * @return bool
		 */
		private function isOldDataPending($oldData) {
			switch ($this->modelName) {
				case 'Requisition':
                    if ($this->isStockValidationByAvailableQty()) {
                        return false;
                    }
					return $oldData[$this->modelName]['status'] == Requisition::STATUS_PENDING;
				case 'PurchaseOrder':
				case 'Invoice':
					return $oldData[$this->modelName]['draft'] == 1;
			}
			return false;
		}

        /*
         * This is to check if stock validation by available qty is enabled and validation by quantity basically means we consider pending stock transactions as transactions that affected the stock quantity
         */
        private function isStockValidationByAvailableQty() {
            return \settings::getValue(InventoryPlugin, SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS) && \settings::getValue(InventoryPlugin, SettingsUtil::VALIDATE_STOCK_OF_PRODUCTS_BY_AVAILABLE);
        }
	}
