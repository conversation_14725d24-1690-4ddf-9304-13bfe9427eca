<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:21 م
 */

namespace App\Validators;


use App\Services\TrackStock\InvoiceTrackingItemConverter;
use App\Services\TrackStock\PurchaseOrderTrackingItemConverter;
use App\Services\TrackStock\RequisitionTrackingItemConverter;
use App\Services\TrackStock\TrackingItem;
use Izam\Daftra\Common\Utils\PermissionUtil;
use App\Utils\TrackStockUtil;
use Requisition;
use Set;

/**
 * Class TrackStockValidator
 * @package App\Validators\TrackStock
 * this class validates any data contains tracking number data
 * based on their model and transaction type
 */
class CanChangeToPurchaseQuotation
{

    /**
     * @param $data
     * @param $id
     * @return array|bool[]
     */
    public static function validate($data, $id)
    {
        if (empty($id)) {
            return true;
        }
        $Model = GetObjectOrLoadModel("QuotationRequest");
        $Model->recursive = 2;
        $quotationRequest = $Model->findBYId($id);
        if (empty($quotationRequest["QuotationRequest"]["id"])) {
            return true;
        }

        $purchaseRequest = $quotationRequest["PurchaseRequest"];


        if (empty($purchaseRequest["id"])) {
            return true;
        }

        $approvePermsForPurchases = check_permission(PermissionUtil::APPROVE_OR_REJECT_PURCHASE_REQUESTS) && check_permission(PermissionUtil::APPROVE_OR_REJECT_PURCHASE_QUOTATIONS);

        foreach ($data["PurchaseOrderItem"] as  $item) {
            $result = self::getItemInfoFromDataBase($item['product_id'], $purchaseRequest["PurchaseRequestItem"]);
            if (!$result && !($approvePermsForPurchases)) {
                return ['message' => __('You cannot select an item not selected in the purchase request item', true)." <a target='_blank' href='/v2/owner/entity/purchase_request/".$purchaseRequest["id"]."/show'>#".$purchaseRequest["code"]."</a>", 'status' => false];
            }
            if ($result['quantity'] < $item['quantity'] && !($approvePermsForPurchases)) {
                return ['message' => __('The Items quantity cannot exceed the Items quantity of the purchase request item', true)." <a target='_blank' href='/v2/owner/entity/purchase_request/".$purchaseRequest["id"]."/show'>#".$purchaseRequest["code"]."</a>", 'status' => false];
            }
        }
        return true;
    }

    private static function getItemInfoFromDataBase($item_id, $items)
    {
        $index = array_search($item_id, array_column($items, "product_id"));

        if ($index !== false) {
            return $items[$index];
        }
       return false;
    }
}
