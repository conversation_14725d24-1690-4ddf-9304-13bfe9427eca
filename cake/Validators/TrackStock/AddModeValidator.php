<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:47 م
 */

namespace App\Validators\TrackStock;
use App\Validators\TrackStock\Rules\Available;
use App\Validators\TrackStock\Rules\Required;
use App\Validators\TrackStock\Rules\Unique;
use App\Validators\TrackStock\Rules\UniqueOverTransaction;

/**
 * Class AddModeValidator
 * @package App\Validators\TrackStock
 * validate serial or lot not empty and unique if serial
 */
class AddModeValidator extends TrackStockModeValidatorAbstract
{


    public function getValidationRules()
    {
        $rules = [
            Required::class,
            UniqueOverTransaction::class,
            Unique::class,
        ];
        return $rules;
    }
}