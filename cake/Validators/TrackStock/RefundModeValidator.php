<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:47 م
 */

namespace App\Validators\TrackStock;
use App\Validators\TrackStock\Rules\Exist;
use App\Validators\TrackStock\Rules\Required;
use App\Validators\TrackStock\Rules\Unique;
use App\Validators\TrackStock\Rules\UniqueOverTransaction;

/**
 * Class RefundModeValidator
 * @package App\Validators\TrackStock
 * validate that this item exists
 */
class RefundModeValidator extends TrackStockModeValidatorAbstract
{

    public function getValidationRules()
    {
        \App::import('Vendor', 'settings');
        $allowNegative = \Settings::getValue(ProductTracking, \Settings::ALLOW_NEGATIVE_TRACKING);
        $rules = [
            Required::class,
            Exist::class,
            UniqueOverTransaction::class,
          //  Unique::class
            ];
        if($allowNegative) {
            unset($rules[1]);
        }
        return $rules;
    }
}
