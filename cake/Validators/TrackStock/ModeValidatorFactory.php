<?php

namespace App\Validators\TrackStock;

use App\Utils\TrackStockUtil;

class ModeValidatorFactory
{
    public static function getValidator($mode, $type, $data)
    {
        switch ($mode)
        {
            case TrackStockUtil::MODE_ADD:
                if ($type === TrackStockUtil::REQUISITION && $data['Requisition']['order_type'] == \Requisition::ORDER_TYPE_STOCKTAKING_IN) {
                    return new StockingAddModeValidator();
                }
                $validator = new AddModeValidator();
                break;
            case TrackStockUtil::MODE_DEDUCT:
            case TrackStockUtil::MODE_TRANSFER:
                $transactionDate = null;
                if($type === TrackStockUtil::INVOICE) {
                    $transactionDate = isset($data['Invoice']['date']) ? $data['Invoice']['date'] : format_date(date('Y-m-d'));
                }
                if($type === TrackStockUtil::REQUISITION) {
                    if($data['Requisition']['order_type'] == \Requisition::ORDER_TYPE_INVOICE) {
                        $Invoice = GetObjectOrLoadModel('Invoice');
                        $Req = GetObjectOrLoadModel('Requisition');
                        $data = $Req->getRequisition($data['Requisition']['id']);
                        $invoiceRecord = $Invoice->getInvoice($data['Requisition']['order_id']);
                        $transactionDate = $Invoice->formatDate($invoiceRecord['Invoice']['date']) ? $Invoice->formatDate($invoiceRecord['Invoice']['date']) : $invoiceRecord['Invoice']['date'];
                    } else {
                        $transactionDate = $data['Requisition']['date'];
                        if($data['Requisition']['order_type'] == \Requisition::ORDER_TYPE_STOCKTAKING_OUT){
                           return new StockingDeductModeValidator($transactionDate);
                        }
                    }
                }
                $validator = new DeductModeValidator($transactionDate, $type);
                break;
            case TrackStockUtil::MODE_REFUND:
                $validator = new RefundModeValidator();
                break;
            case TrackStockUtil::MODE_CREDIT:
                $validator = new CreditModeValidator();
                break;
        }
        return $validator;
    }
}