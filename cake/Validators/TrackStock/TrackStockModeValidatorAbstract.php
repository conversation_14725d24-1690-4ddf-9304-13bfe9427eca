<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 26/11/19
 * Time: 11:49 ص
 */

namespace App\Validators\TrackStock;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;
use App\Validators\TrackStock\Rules\Subscriptable;

abstract class TrackStockModeValidatorAbstract implements TrackStockModeValidatorInterface
{


    /**
     * @param TrackingItem $trackingItems
     * @param bool $isDelete
     * @param bool $isSubscription
     * @return array|mixed
     */
    public function validate($trackingItems, $isDelete = false, $isSubscription = false, $validationMode = null, $validateAll = true)
    {
        $rules = $this->getValidationRules();
        if($isSubscription)
        {
            $rules = [Subscriptable::class] + $rules;
        }
        $errorMessages = [];

        $refErrors = [];
        foreach ($rules as $rule)
        {
            if(!is_object($rule)) {
                $ruleObject = new $rule();
            } else {
                $ruleObject = $rule;
            }
            if($isDelete && in_array($ruleObject->name, ['required', 'exist']))
            {
                continue;
            }
            if($isDelete && isset($validationMode) && $validationMode == TrackStockUtil::MODE_DEDUCT && $ruleObject->name == 'available'){
                continue;
            }
            foreach ($trackingItems as $trackingItem)
            {
                if($ruleObject->canValidate($trackingItem))
                {
                    if(!$ruleObject->valid($trackingItem))
                    {
                        if(!isset($refErrors[$trackingItem->referenceId]))
                        {
                            $refErrors[$trackingItem->referenceId] = true;
                            $errorMessages[$ruleObject->name][$trackingItem->referenceId] = $ruleObject->getError();
                        }
                    }
                }
            }
            if(!isset($errorMessages[$ruleObject->name]) && $validateAll)
            {
                $allValid = $ruleObject->allValid();
                if($allValid !== true)
                {
                    $errorMessages[$ruleObject->name] = $allValid + ($errorMessages[$ruleObject->name] ?:[]);
                }
            }
        }
//        die(debug($errorMessages));

        return empty($errorMessages) ?:$errorMessages;
    }

}
