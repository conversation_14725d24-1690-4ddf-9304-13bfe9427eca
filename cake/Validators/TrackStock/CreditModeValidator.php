<?php
	namespace App\Validators\TrackStock;

	use App\Validators\TrackStock\Rules\Exist;
	use App\Validators\TrackStock\Rules\Required;
	use App\Validators\TrackStock\Rules\Unique;
	use App\Validators\TrackStock\Rules\UniqueOverTransaction;

	/**
	 * Class RefundModeValidator
	 * @package App\Validators\TrackStock
	 * validate that this item exists
	 */
	class CreditModeValidator extends TrackStockModeValidatorAbstract
	{
		public function getValidationRules()
		{
			return [
				Required::class,
				UniqueOverTransaction::class,
				Unique::class,
			];
		}
	}
