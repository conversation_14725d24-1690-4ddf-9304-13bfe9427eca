<?php

namespace App\Validators\TrackStock;
use App\Validators\TrackStock\Rules\StockingExist;
use App\Validators\TrackStock\Rules\Expired;
use App\Validators\TrackStock\Rules\StockingAvailable;
use App\Validators\TrackStock\Rules\StockingRequired;
use App\Validators\TrackStock\Rules\UniqueOverTransaction;

/**
 * Class DeductModeValidator
 * @package App\Validators\TrackStock
 * validate that the item exists and available
 */
class StockingDeductModeValidator  extends DeductModeValidator
{

    public function getValidationRules()
    {
        \App::import('Vendor', 'settings');
        $allowNegative = \Settings::getValue(ProductTracking, \Settings::ALLOW_NEGATIVE_TRACKING);
        $rules = [
            StockingExist::class,
            UniqueOverTransaction::class,
            StockingAvailable::class
        ];
        if($allowNegative) {
            $rules = [
                UniqueOverTransaction::class,
            ];
        }
        $rules[] = new Expired($this->transactionDate);
        return $rules;
    }
}