<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 15/12/19
 * Time: 10:31 ص
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;

/**
 * Class UniqueOverTransaction
 * @package App\Validators\TrackStock\Rules
 * validates that the same tracking number isnt used in two rows in the same transaction
 */
class UniqueOverTransaction extends TrackStockValidationRuleAbstract
{
    public $name = 'Unique over transaction';
    private $trackingNumbers = [];
    function canValidate(TrackingItem $trackingItem)
    {
        return $trackingItem->trackingType && $trackingItem->trackingType === TrackStockUtil::TYPE_SERIAL;
    }

    function valid(TrackingItem $trackingItem)
    {
        $id = strtolower($trackingItem->getIdentifier());
        $this->trackingItem = $trackingItem;
        if(
            isset($this->trackingNumbers[$id]) &&
            $this->trackingNumbers[$id] === $trackingItem->productId
        )
        {
            return false;
        }else{
            $this->trackingNumbers[$id] = $trackingItem->productId;
            return true;
        }
    }


    public function getError()
    {
        return sprintf(__('Same tracking number for product %s can not be duplicated in same transaction.',true), $this->trackingItem->getIdentifier());
    }
}