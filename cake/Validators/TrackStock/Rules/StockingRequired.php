<?php

namespace App\Validators\TrackStock\Rules;

class StockingRequired extends Required
{

    /**
     * @return bool|true
     * here we check on the products that has serials to see if the amount of serials for the product
     * is equal to the quantity of the product
     */
    function allValid()
    {
        $errors = [];
        foreach ($this->serialQuantities as $refId => $refQuantity) {
            if ($this->enteredSerials[$refId] != $refQuantity) {
                $errors[$refId] = sprintf(__('The number of serial number(s) must be equal to the entered quantity for product %s', true), $this->serialsProduct[$refId]['Product']['name']);
            }
        }
        return empty($errors) ? true : $errors;
    }

    public function getError()
    {
        return  sprintf( __t('The %s number value cannot be left empty  for  %s') ,__t(($this->trackingItem->trackingType) . " numbers") , $this->trackingItem->getProduct()['Product']['name']);
    }
}
