<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 03:38 م
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;

class Expired extends TrackStockValidationRuleAbstract
{

    public function __construct(protected $transactionDate)
    {
    }

    public $name = 'expired';
    function canValidate(TrackingItem $trackingItem)
    {

        return $this->transactionDate && $trackingItem->trackingType && in_array($trackingItem->trackingType, [TrackStockUtil::TYPE_EXPIRY, TrackStockUtil::TYPE_LOT_EXPIRY]);
    }

    function valid(TrackingItem $trackingItem)
    {
        $Invoice = GetObjectOrLoadModel('Invoice');
        $this->trackingItem = $trackingItem;
        $expiryDate = $trackingItem->expiryDate;
        $stopSellingExpiredItems = \Settings::getValue(ProductTracking, \Settings::STOP_SELLING_EXPIRED_TRACKING_ITEMS);
        $result = $expiryDate >= $Invoice->formatDate($this->transactionDate);
        if(!$result && !$stopSellingExpiredItems) {
//            CustomValidationFlash([__('The tracking data you have entered has already expired', true)]);
            return true;
        }
        return $result;
    }


    public function getError()
    {
        return sprintf(__('The tracking data you have entered has already expired for product %s', true),$this->trackingItem->getProduct()['Product']['name'] );
    }
}
