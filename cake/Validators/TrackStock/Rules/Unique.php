<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 03:39 م
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;

class Unique extends TrackStockValidationRuleAbstract
{
    public $name = 'unique';
    private $revokedSerial;
    private $serials = [];
    function canValidate(TrackingItem $trackingItem)
    {
        return $trackingItem->trackingType === TrackStockUtil::TYPE_SERIAL;
    }

    function valid(TrackingItem $trackingItem)
    {
        $this->trackingItem = $trackingItem;
        $serial = null;
        if (!is_array($trackingItem->serial)) {
            $serial = strtolower($trackingItem->serial);
        }
        $result = (
            (!isset($this->serials[$serial]) && $trackingItem->getTrackingBalance() <= 0) ||
            $trackingItem->quantity == 0 //incase of an edit and enters the same serial the qty will be 0
        );
        $this->serials[$serial] = $serial;
        if(!$result)
        {
            $this->revokedSerial = $serial;
        }
        return $result;
    }

    public function getError()
    {
        return sprintf(__('The serial number %s for product %s already exist',true), $this->trackingItem->getIdentifier(),
            $this->trackingItem->getProduct()['Product']['name']
            );
    }
}