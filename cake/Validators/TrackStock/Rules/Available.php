<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 03:39 م
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;

class Available extends TrackStockValidationRuleAbstract
{

    private $id = '';
    public $name = 'available';
    /**
     * @var array used to store the balance of the tracking number being validated
     * //used when we add same tracking number on more than one row.
     */
    private $qtys = [];
    /**
     * @param TrackingItem $trackingItem
     * @return boolean
     */
    public function canValidate(TrackingItem $trackingItem)
    {
        return $trackingItem->trackingType && $trackingItem->trackingType !== TrackStockUtil::QUANTITY_ONLY;
    }

    /**
     * @param TrackingItem $trackingItem
     * @return mixed
     */
    public function valid(TrackingItem $trackingItem)
    {
        $this->trackingItem = $trackingItem;
        $id = strtolower($trackingItem->getIdentifier()).'_'.$trackingItem->productId;
        if(isset($this->qtys[$id]))
        {
            $balance = $this->qtys[$id];
        }else{
            $balance = $trackingItem->getBalance();
        }
        if($balance >= $trackingItem->quantity){
            $this->qtys[$id] = (float) $balance - (float) $trackingItem->quantity;
            return true;
        }
        $this->id = $id;
        return false;

    }

    public function getError()
    {
        return sprintf(
            __('%s for product %s has no available quantity in the stock %s',true),
            $this->trackingItem->getIdentifier(),
            $this->trackingItem->getProduct()['Product']['name'],
            $this->trackingItem->getStore()['Store']['name']
        );
    }
}