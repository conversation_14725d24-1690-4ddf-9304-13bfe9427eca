<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 03:39 م
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;

class Exist extends TrackStockValidationRuleAbstract
{
    public $name = 'exist';
    /**
     * @param TrackingItem $trackingItem
     * @return boolean
     */
    public function canValidate(TrackingItem $trackingItem)
    {
        return $trackingItem->trackingType && $trackingItem->trackingType !== TrackStockUtil::QUANTITY_ONLY;
    }

    /**
     * @param TrackingItem $trackingItem
     * @return mixed
     */
    public function valid(TrackingItem $trackingItem)
    {
        $this->trackingItem = $trackingItem;
        $result = $trackingItem->exists();
        if(!$result)
        {
            $this->revokedSerial = $trackingItem->serial;
        }
        return $result;
    }

    public function getError()
    {
        return sprintf(__("%s for product %s not found in store %s",true),
            $this->trackingItem->getIdentifier(),
            $this->trackingItem->getProduct()['Product']['name'],
            $this->trackingItem->getStore()['Store']['name']
        );
    }
}