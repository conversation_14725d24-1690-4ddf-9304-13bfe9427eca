<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 03:38 م
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;

class Required extends TrackStockValidationRuleAbstract
{

    public $name = 'required';
    protected $serialQuantities = [];
    protected $enteredSerials = [];
    protected $trackingItems = [];
    protected $serialsProduct = [];
    function canValidate(TrackingItem $trackingItem)
    {
        return $trackingItem->trackingType && $trackingItem->trackingType !== TrackStockUtil::QUANTITY_ONLY;

    }

    function valid(TrackingItem $trackingItem)
    {
        $Model = GetObjectOrLoadModel('CostCenter');
        $this->trackingItem = $trackingItem;
        switch ($trackingItem->trackingType)
        {
            case TrackStockUtil::TYPE_EXPIRY:
                return !empty($Model->formatDate($trackingItem->expiryDate));
                break;
            case TrackStockUtil::TYPE_SERIAL:
                if(!isset($this->serialsProduct[$trackingItem->referenceId]))
                {
                    $this->serialsProduct[$trackingItem->referenceId] = $trackingItem->getProduct();
                }
                $this->serialQuantities[$trackingItem->referenceId] = $trackingItem->refQuantity;
                isset($this->enteredSerials[$trackingItem->referenceId]) ? $this->enteredSerials[$trackingItem->referenceId]++: $this->enteredSerials[$trackingItem->referenceId] = 1;

                return !empty($trackingItem->serial);
                break;
            case TrackStockUtil::TYPE_LOT:
                return !empty($trackingItem->lot);
                break;
            case TrackStockUtil::TYPE_LOT_EXPIRY:
                return (!empty($trackingItem->lot) && !empty($Model->formatDate($trackingItem->expiryDate)));
                break;
        }
        return false;
    }

    /**
     * @return bool|true
     * here we check on the products that has serials to see if the amount of serials for the product
     * is equal to the quantity of the product
     */
    function allValid()
    {
        $errors = [];
        foreach ($this->serialQuantities as $refId => $refQuantity) {
            if($this->enteredSerials[$refId] != $refQuantity)
            {
                $errors[$refId] = sprintf(__('The number of serial number(s) must be equal to the entered quantity for product %s', true), $this->serialsProduct[$refId]['Product']['name']);
            }
        }
        return empty($errors)?true:$errors;
    }

    public function getError()
    {
        return sprintf(__('The number of serial number(s) must be equal to the entered quantity for product %s', true), $this->trackingItem->getProduct()['Product']['name']);
    }
}
