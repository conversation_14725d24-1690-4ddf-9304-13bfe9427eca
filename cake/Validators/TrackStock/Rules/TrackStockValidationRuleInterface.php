<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 26/11/19
 * Time: 11:43 ص
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;

interface TrackStockValidationRuleInterface
{
    /**
     * @param TrackingItem $trackingItem
     * @return boolean
     */
    public function canValidate(TrackingItem $trackingItem);

    /**
     * @param TrackingItem $trackingItem
     * @return mixed
     */
    public function valid(TrackingItem $trackingItem);

    /**
     * @return true
     */
    public function allValid();

    /**
     * @return string
     */
    public function getError();


}