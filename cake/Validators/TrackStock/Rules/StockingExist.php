<?php

namespace App\Validators\TrackStock\Rules;


class StockingExist  extends Exist
{
    public function getError()
    {
        $type=$this->trackingItem->trackingType;
        $trackingValue=  $this->trackingItem->{lcfirst(\Inflector::camelize($type))} ;
        if ($type=='expiry_date') {
            $trackingValue=format_date($trackingValue);
        }elseif ($type=='lot_and_expiry_date') {
            $trackingValue='#'.$this->trackingItem->lot.' '.format_date($this->trackingItem->expiryDate);
        }else{
            $trackingValue='#'.$trackingValue;
        }
        return sprintf(
            __t("The %s %s for product %s is not available in store %s", true),
            __t( str_replace('_', ' ', $type)) ,
            $trackingValue,
            $this->trackingItem->getProduct()['Product']['name'],
            $this->trackingItem->getStore()['Store']['name']
        );
    }
}
