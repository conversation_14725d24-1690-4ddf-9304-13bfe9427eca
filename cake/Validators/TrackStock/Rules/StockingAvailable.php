<?php

namespace App\Validators\TrackStock\Rules;

class StockingAvailable  extends Available
{

    /**
     * @var array used to store the balance of the tracking number being validated
     * //used when we add same tracking number on more than one row.
     */

    public function getError()
    {
        $type=$this->trackingItem->trackingType;
        $trackingValue=  $this->trackingItem->{lcfirst(\Inflector::camelize($type))} ;
        if ($type=='expiry_date') {
            $trackingValue=format_date($trackingValue);
        }elseif ($type=='lot_and_expiry_date') {
            $trackingValue='#'.$this->trackingItem->lot.' '.format_date($this->trackingItem->expiryDate);
        }else{
            $trackingValue='#'.$trackingValue;
        }

        return  sprintf(
            __('No available quantity for %s %s in the stock %s', true),
            __t(str_replace('_', ' ', $type)),
            $trackingValue,
            $this->trackingItem->getStore()['Store']['name']
        );
    }
}
