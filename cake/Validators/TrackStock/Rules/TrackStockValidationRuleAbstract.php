<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 26/11/19
 * Time: 11:45 ص
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;

abstract class TrackStockValidationRuleAbstract implements TrackStockValidationRuleInterface
{
    public $name = '';
    /**
     * @var TrackingItem
     */
    protected $trackingItem  = null;
    public function allValid()
    {
        return true;
    }

    public function getError()
    {
        return $this->name.' error';
    }

}