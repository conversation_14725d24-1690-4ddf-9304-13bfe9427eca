<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 03:39 م
 */

namespace App\Validators\TrackStock\Rules;


use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;

class Subscriptable extends TrackStockValidationRuleAbstract
{

    private $id = '';
    public $name = 'Subscriptable';

    public function canValidate(TrackingItem $trackingItem)
    {
        return $trackingItem->trackingType && $trackingItem->trackingType !== TrackStockUtil::QUANTITY_ONLY;
    }

    /**
     * @param TrackingItem $trackingItem
     * @return mixed
     */
    public function valid(TrackingItem $trackingItem)
    {
        $this->trackingItem = $trackingItem;
        return false;

    }

    public function getError()
    {
        return sprintf(
            __("Product %s is tracked and can't be used in a subscription",true),
            $this->trackingItem->getProduct()['Product']['name']
        );
    }
}