<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:21 م
 */

namespace App\Validators\TrackStock;


use App\Services\TrackStock\InvoiceTrackingItemConverter;
use App\Services\TrackStock\PurchaseOrderTrackingItemConverter;
use App\Services\TrackStock\RequisitionTrackingItemConverter;
use App\Services\TrackStock\TrackingItem;
use App\Utils\TrackStockUtil;
use Requisition;
use Set;

/**
 * Class TrackStockValidator
 * @package App\Validators\TrackStock
 * this class validates any data contains tracking number data
 * based on their model and transaction type
 */
class TrackStockValidator
{

    /**
     * @param $data
     * @param $type
     * @param $validationMode
     * @param bool $isDelete used on delete to remove the items quantity and disable required rule
     * @param bool $isSubscription
     * @return bool | or sets validation errors for the data related model
     */
    public static function validate($data, $type, $validationMode, $isDelete = false, $isSubscription = false)
    {
        $ProductModel = GetObjectOrLoadModel('Product');
    	$ItemType = TrackStockUtil::getItemTypeFromParent($type);
        $products_has_lot_or_serial_tracking = $ProductModel->find('count',
		    [
		    	'recursive' => -1,
			    'conditions' => [
			    	'Product.id' => Set::extract("$ItemType.{n}.product_id",$data),
				    'NOT' => ['Product.tracking_type' => TrackStockUtil::QUANTITY_ONLY]
			    ]
		    ]);
        if(!ifPluginActive(PRODUCT_TRACKING_PLUGIN) || empty($products_has_lot_or_serial_tracking) || !$type || !$validationMode || (isset($data['Requisition']) && $data['Requisition']['status'] == Requisition::STATUS_CANCELLED))
        {
            return true;
        }
        $modesValidators = [
            TrackStockUtil::MODE_ADD => AddModeValidator::class,
            TrackStockUtil::MODE_DEDUCT => DeductModeValidator::class,
            TrackStockUtil::MODE_REFUND => RefundModeValidator::class,
	        TrackStockUtil::MODE_CREDIT => CreditModeValidator::class,
            TrackStockUtil::MODE_TRANSFER => DeductModeValidator::class,
        ];

        \App::import('Vendor', 'settings');
        $enable_requisitions = \settings::getValue(InventoryPlugin, $type==TrackStockUtil::PURCHASE_ORDER?'enable_requisitions_po':'enable_requisitions');
        $convertersMapping = [
            TrackStockUtil::REQUISITION => RequisitionTrackingItemConverter::class,
        ];
        if(!$enable_requisitions)
        {
            $convertersMapping += [
                TrackStockUtil::INVOICE => InvoiceTrackingItemConverter::class,
                TrackStockUtil::PURCHASE_ORDER => PurchaseOrderTrackingItemConverter::class,
            ];
        }
        if(!isset($convertersMapping[$type]))
        {
            return true;
        }


        $converter = new $convertersMapping [$type]();
        $trackingItems = $converter->convert($data, GetObjectOrLoadModel('Product'));
        if($isDelete)
        {
            foreach ($trackingItems as $trackingItem)
            {
                $trackingItem->quantity = 0;
            }
        }

        /**
         * @var TrackStockModeValidatorAbstract $modeValidator
         */
        $modeValidator = ModeValidatorFactory::getValidator($validationMode, $type, $data);
        if($converter->isEdit())
        {
            $editedTrackings = $converter->editedTrackings;
            foreach ($editedTrackings as $k => $editedTracking)
            {
                if($k == '' || empty($editedTracking->hasTrackingNumber()))
                {
                    unset($editedTrackings[$k]);
                    continue;
                }
                /**
                 * @var $editedTracking TrackingItem
                 */
                if(isset($trackingItems[$k]))
                {
                    /**
                     * @var $trackingItem TrackingItem
                     */
                    $trackingItem = $trackingItems[$k];
                    if(($trackingItem->quantity < $editedTracking->quantity))
                    {
                        if(in_array($validationMode, [TrackStockUtil::MODE_ADD, TrackStockUtil::MODE_DEDUCT, TrackStockUtil::MODE_REFUND]))
                        {
                            //here we have lowered the quantity of the tracking item
                            //and in this case we have to inverse the validation mode for this item
                            $editedTracking->quantity = (float) $editedTracking->quantity - (float) $trackingItem->quantity ;
                            $trackingItem->quantity = 0; //we set the tracking item to 0 to pass the unique and required validation
                        }else if($validationMode === TrackStockUtil::MODE_TRANSFER)
                        {
                            //here we have lowered the quantity of the tracking item
                            //and in this case we have to validate on the to store
                            //that we can deduct from it the amount removed from the transfer
                            $editedTracking->quantity = $editedTracking->quantity - $trackingItem->quantity;
                            $editedTracking->storeId = $converter->toStoreId;
                            $trackingItem->quantity = 0;//we set the tracking item to 0 to pass the unique and required validation
                        }
                    } else if($trackingItem->quantity > $editedTracking->quantity){
                        //here the validation mode will stay the same because
                        //we just have increased or didnt change the quantity
                        //of the tracking number
                        $trackingItem->quantity -= $editedTracking->quantity;//to fix transfer edit when increase qty
                        $editedTracking->quantity = 0;//we set the tracking item to 0 to pass the unique and required validation
                    }else{
                        //here we didnt change the qty of the item so no validation needed
                        $trackingItem->quantity = 0;
                        $editedTracking->quantity = 0;//we set the tracking item to 0 to pass the unique and required validation
                    }
                }else if($validationMode === TrackStockUtil::MODE_TRANSFER){
                    //if we removed an item from transfer we have to validate
                    //the removal of this item from the to store
                    $editedTracking->storeId = $converter->toStoreId;
                }
            }
        }
        $result = $modeValidator->validate($trackingItems, $isDelete, $isSubscription, $validationMode);
        if($result !== true)
        {
            $converter->setErrors($result);
            return $result;
        }
        if(!empty($editedTrackings))
        {
            $editValidationMode = false;
            if(in_array($validationMode, [TrackStockUtil::MODE_ADD, TrackStockUtil::MODE_TRANSFER , TrackStockUtil::MODE_REFUND]))
            {
                /**
                 * if we are here this means that we have lowered the qty of an item
                 * in an add or transfer operation so we must validate that the subtraction
                 * of the difference of the two qtys
                 * in the add mode we validate the difference from the original transaction store
                 * but in the transfer mode we validate the difference from the transaction to store
                 */
                $editValidationMode = TrackStockUtil::MODE_DEDUCT;
            }else if($validationMode === TrackStockUtil::MODE_DEDUCT)
            {
                $editValidationMode = TrackStockUtil::MODE_ADD;
            }
            // Why do we validate "ADD" mode when there is $IsDelete = true ? in case of serials this will cause the Exists rule to return false as serials can only be 1 quantity
	        // I have Edited this to match my thinking if you have better solutions to this or at least explain why are we doing this let me know
            if($editValidationMode && !($editValidationMode === TrackStockUtil::MODE_ADD && $isDelete))
            {
                $modeValidator = new $modesValidators[$editValidationMode];
                $result = $modeValidator->validate($editedTrackings, $isDelete, $isSubscription, validateAll: false);

                if($result !== true)
                {
                    $converter->setErrors($result);
                }
            }
        }

        return $result;
    }
}
