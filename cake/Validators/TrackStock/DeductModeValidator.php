<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:47 م
 */

namespace App\Validators\TrackStock;

use App\Utils\TrackStockUtil;
use App\Validators\TrackStock\Rules\Available;
use App\Validators\TrackStock\Rules\Exist;
use App\Validators\TrackStock\Rules\Expired;
use App\Validators\TrackStock\Rules\Required;
use App\Validators\TrackStock\Rules\UniqueOverTransaction;

/**
 * Class DeductModeValidator
 * @package App\Validators\TrackStock
 * validate that the item exists and available
 */
class DeductModeValidator extends TrackStockModeValidatorAbstract
{
    public function __construct(protected $transactionDate = null, private $type = '')
    {

    }

    public function getValidationRules()
    {
        \App::import('Vendor', 'settings');
        $allowNegative = \Settings::getValue(ProductTracking, \Settings::ALLOW_NEGATIVE_TRACKING);
        $rules = [
            Required::class,
            Exist::class,
            UniqueOverTransaction::class,
            Available::class
        ];
        if($allowNegative) {
            unset($rules[1], $rules[3]);
        }
        if($this->type != TrackStockUtil::REQUISITION) {
            $rules[] = new Expired($this->transactionDate);
        }
        debug($rules);
        return $rules;
    }
}