<?php

namespace App\Validators\TrackStock;
use App\Validators\TrackStock\Rules\StockingRequired;
use App\Validators\TrackStock\Rules\StockingUnique;
use App\Validators\TrackStock\Rules\UniqueOverTransaction;

/**
 * Class AddModeValidator
 * @package App\Validators\TrackStock
 * validate serial or lot not empty and unique if serial
 */
class StockingAddModeValidator  extends AddModeValidator
{


    public function getValidationRules()
    {
        $rules = [
            UniqueOverTransaction::class,
            StockingUnique::class,
        ];
        return $rules;
    }
}