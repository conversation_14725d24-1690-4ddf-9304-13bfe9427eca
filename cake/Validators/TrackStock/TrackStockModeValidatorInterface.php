<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 26/11/19
 * Time: 11:48 ص
 */

namespace App\Validators\TrackStock;


use App\Services\TrackStock\TrackingItem;

interface TrackStockModeValidatorInterface
{
    public function getValidationRules();

    /**
     * @param TrackingItem $trackingItems[]
     * @return mixed
     */

    /**
     * @param TrackingItem $trackingItems
     * @return mixed
     */
    public function validate( $trackingItems);
}