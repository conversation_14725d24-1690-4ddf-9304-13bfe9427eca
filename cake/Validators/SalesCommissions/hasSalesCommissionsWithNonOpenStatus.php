<?php

namespace App\Validators\SalesCommissions;

class hasSalesCommissionsWithNonOpenStatus
{
    const messages = [
        "edit" => "You cannot edit in the transaction that included in an approved ,rejected or paid commission sheet",
        "delete" => "You cannot delete the transaction that included in an approved ,rejected or paid commission sheet",
        "update_payment" => "You cannot delete or update in payments of the transactions that included in an approved ,rejected or paid commission sheet"
    ];

    private $controller;
    private $messageKey;
    private $id;

    /**
     * hasSalesCommissionsWithNonOpenStatus constructor.
     * @param $id
     * @param $controller
     * @param $messageKey
     */
    public function __construct($id, $controller, $messageKey)
    {
        $this->controller = $controller;
        $this->messageKey = $messageKey;
        $this->id = $id;
    }

    public function validate()
    {
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        if (ifPluginActive(COMMISSION_PLUGIN) && $invoiceModel->hasSalesCommissionsWithNonOpenStatus($this->id)) {
            if (IS_REST) {
                $this->controller->cakeError('error404', array('message' => __(self::messages[$this->messageKey], true)));
            }
            $this->controller->flashMessage(__(self::messages[$this->messageKey], true), 'Errormessage', 'secondaryMessage');
            $this->controller->redirect($this->controller->referer(array('action' => 'index'), true));
        }
    }
}