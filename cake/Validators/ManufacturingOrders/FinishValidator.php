<?php

namespace App\Validators\ManufacturingOrders;

use App\Utils\TrackStockUtil;
use App\Validators\TrackStock\TrackStockValidator;
use Izam\Daftra\Common\Utils\ManufacturingOrderStatusUtil;
use \StockValidationComponent;
use Izam\Daftra\Common\Utils\StoreStatusUtil;

class FinishValidator {
    public static function validate($ClosedPeriod, array $order, $requisitionData, $inputData) : bool {
        if(!self::storesAreActive($order, $inputData)){
            return false;
        }
        if(!self::validateSerialsEqualQuantities($order, $inputData)){
            return false;
        }

        if(!self::validateAssetInService($order)){
            return false;
        }

        foreach($requisitionData as $requisition) {
            if(!self::requisitionIsWithinOpenedFinancialPeriod($ClosedPeriod, $requisition)){
                CustomValidationFlash([
                    sprintf(__('You can not add, edit, or delete a transaction in this date %s within a closed period', true), $requisition['Requisition']['date'])
                ]);
                return false;
            }

            $validationResult = TrackStockValidator::validate(
                $requisition,
                TrackStockUtil::REQUISITION,
                TrackStockUtil::getRequisitionTypeValidationMode($requisition['Requisition']['order_type'])
            );
            
            if($validationResult !== true){
                return false;
            }
        }

        return true;
    }

    public static function validateUndoCompletion(StockValidationComponent $StockValidation, $ClosedPeriod, array $requisitions, array $order){
        $session = new \CakeSession;

        foreach($requisitions as $requisition) {
            if(!self::requisitionIsWithinOpenedFinancialPeriod($ClosedPeriod, $requisition)){
                CustomValidationFlash([
                    sprintf(__('You can not add, edit, or delete a transaction in this date %s within a closed period', true), $requisition['Requisition']['date'])
                ]);
                return false;
            }
            $trackStockValidatorResult = TrackStockValidator::validate(
                $requisition,
                TrackStockUtil::REQUISITION,
                TrackStockUtil::getRequisitionTypeValidationMode($requisition['Requisition']['order_type'])
            );

            if($trackStockValidatorResult !== true){
                $error = $session->check('Message.CustomError')? $session->read('Message.CustomError')['message']:null;
                if(!empty($error)){
                    $session->write('Message.CustomError', ['message' => $error[0]]);
                }
                CustomValidationFlash($error);
                return false;
            }

            $mode = $requisition['Requisition']['type'] == \Requisition::TYPE_INBOUND ? 'deduct' :'add_delete';

            $storeBalanceValidation = $StockValidation->validateQuantityAvailable("requisition", $requisition, $mode);
            if(!$storeBalanceValidation){
                $error = $session->check('Message.secondaryMessage')? $session->read('Message.secondaryMessage')['message']:null;
                if(!empty($error)){
                    $session->write('Message.CustomError', ['message' => [$error]]);
                }
                CustomValidationFlash($error);
                return false;
            }
        }
        return true;
    }

    public static function requisitionIsWithinOpenedFinancialPeriod($ClosedPeriod, $requisition) : bool {
        if(ifPluginActive(AccountingPlugin)){
            return $ClosedPeriod->is_opened_date($requisition['Requisition']['date']);
        }
        return true;
    }

    private static function validateSerialsEqualQuantities($order, $inputData) : bool {
        if(
            $order['Product']['tracking_type'] == TrackStockUtil::TYPE_SERIAL
            && (
                empty($inputData['main_product_serials'])
                || count($inputData['main_product_serials']) != $order['ManufacturingOrder']['quantity']
            )
        ){
            CustomValidationFlash([
                sprintf(__('The number of entered serial numbers for %s should equal the quantity specified in the Manufacturing Order.', true), __('the main product',true))
            ]);
            return false;
        }

        $scrapsMap = [];
        $scrapHasSerial = false;
        foreach ($order['ManufacturingOrderScrap'] as $orderScrap) {
            if(
                $orderScrap['Product']['tracking_type'] == TrackStockUtil::TYPE_SERIAL
            ){
                $scrapHasSerial = true;
            }
            $scrapsMap[$orderScrap['id']] = $orderScrap;
        }
        if(!empty($inputData['scraps'])){
            foreach ($inputData['scraps'] as $scrapItem) {
                if(
                    !empty($scrapsMap[$scrapItem['id']])
                    && $scrapsMap[$scrapItem['id']]['Product']['tracking_type'] == TrackStockUtil::TYPE_SERIAL
                    && (
                        empty($scrapItem['serials'])
                        || count($scrapItem['serials']) != $scrapsMap[$scrapItem['id']]['quantity']
                    )
                ){
                    $productName = $scrapsMap[$scrapItem['id']]['Product']['name'] . ' #' . $scrapsMap[$scrapItem['id']]['Product']['product_code'];
                    CustomValidationFlash([
                        sprintf(__('The number of entered serial numbers for %s should equal the quantity specified in the Manufacturing Order.', true), __('the scrap item',true) . ' ' . $productName)
                    ]);
                    return false;                            
                }
            }
        }elseif(empty($inputData['scraps']) && $scrapHasSerial){
            CustomValidationFlash([
                sprintf(__('The number of entered serial numbers for %s should equal the quantity specified in the Manufacturing Order.', true), __('the scrap item',true))
            ]);
            return false;
        }
        return true;
    }

    private static function validateAssetInService($order) : bool {
        $operations = $order['ManufacturingOrderOperation'];
        $AssetModel = GetObjectOrLoadModel('Asset');
        $WorkstationModel = GetObjectOrLoadModel('Workstation');
        foreach ($operations as $operation) {
            if(!empty($operation['workstation_data'])){
                $workstationData = json_decode($operation['workstation_data'], true);
                if(!empty($workstationData['asset'])){
                    $asset = $AssetModel->findById($workstationData['asset']['id']);
                    $workstation = $WorkstationModel->findById($operation['workstation_id']);
                    if($asset['Asset']['asset_status'] != \Asset::STATUS_INSERVICE){
                        CustomValidationFlash([
                            sprintf(__('The asset you selected in %s is not in service, so you need to update the manufacturing operations', true), $workstation['Workstation']['name'] . ' #' . $workstation['Workstation']['code'])
                        ]);
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private static function storesAreActive($order, $inputData) : bool {
        $StoreModel = GetObjectOrLoadModel('Store');    
        $mainStore = $StoreModel->find('first', [
            'conditions' => [
                'Store.id' => $inputData['main_store_id'],
                'Store.active' => StoreStatusUtil::ACTIVE
            ]
        ]);
        if($mainStore == false){
            CustomValidationFlash([
                sprintf(
                    __('The selected warehouse for %s must be active', true),
                    __("the main product", true)
                )
            ]);
            return false;
        }
        if(!empty($order['ManufacturingOrderScrap']) && $inputData['scrap_store_id'] != $inputData['main_store_id']) {
            $scrapStore = $StoreModel->find('first', [
                'conditions' => [
                    'Store.id' => $inputData['scrap_store_id'],
                    'Store.active' => StoreStatusUtil::ACTIVE
                ]
            ]);
            if($scrapStore == false){
                CustomValidationFlash([
                    sprintf(
                        __('The selected warehouse for %s must be active', true),
                        __("the scrap item", true)
                    )
                ]);
                return false;
            }
        }

        return true;
    }
}