<?php

namespace App\Validators\EInvoice;

class JordanInvoiceValidator
{
    public static function getDeleteMessage($count) {
        $message = sprintf(__("Failed to delete (%s) Invoices According to the instructions of Tax Authority, it is forbidden to delete or update the invoice after it has been issued in accordance with the requirements of the electronic invoice.", true), $count);
        return $message;
    }


    public static function getInvoiceMessage($invoice) {
        $addRefundLink = "<a href='".\Router::url(['action' => 'add_refund', $invoice['Invoice']['id']])."'>".__('Refund Receipt', true) . "</a>";
        $addCreditNoteLink = "<a href='".\Router::url(['action' => 'add_creditnote', $invoice['Invoice']['id']])."'>".__('Credit Note', true) . "</a>";
        $message = sprintf(__("According to the instructions of the Tax Authority, it is forbidden to delete or update the invoice after it has been issued in accordance with the requirements of the electronic invoice, but you can create a %s or %s to Cancel the Invoice", true), $addRefundLink, $addCreditNoteLink);
        return $message;
    }


}
