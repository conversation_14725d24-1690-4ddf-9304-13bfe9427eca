<?php

namespace App\Validators\EInvoice;

class ESaudiInvoiceValidator
{
    public static function getDeleteMessage($count) {
        $message = sprintf(__("Failed to delete (%s) Invoices According to the instructions of the Zakat and Income Authority, it is forbidden to delete or update the invoice after it has been issued in accordance with the requirements of the electronic invoice.", true), $count);
        return $message;
    }

    public static function getInvoiceMessage($invoice) {
        $addRefundLink = "<a href='".\Router::url(['action' => 'add_refund', $invoice['Invoice']['id']])."'>".__('Refund Receipt', true) . "</a>";
        $addCreditNoteLink = "<a href='".\Router::url(['action' => 'add_creditnote', $invoice['Invoice']['id']])."'>".__('Credit Note', true) . "</a>";
        $message = sprintf(__("According to the instructions of the Zakat and Income Authority, it is forbidden to delete or update the invoice after it has been issued in accordance with the requirements of the electronic invoice, but you can create a %s or %s to Cancel the Invoice", true), $addRefundLink, $addCreditNoteLink);
        if ($invoice['Invoice']['type'] == \Invoice::ADVANCE_PAYMENT){
            $message = sprintf(__t("According to the instructions of the Zakat and Income Authority, it is forbidden to delete or update the invoice after it has been issued in accordance with the requirements of the electronic invoice, but you can create a %s to Cancel the Invoice"), $addCreditNoteLink);
        }
        return $message;
    }

    public static function getRefundMessage($invoice) {
        $message = __("According to the instructions of the Zakat and Income Authority, it is forbidden to delete or update the invoice after it has been issued in accordance with the requirements of the electronic invoice", true);
        return $message;
    }

    public static function getCreditNoteMessage($invoice) {
        $message = __("According to the instructions of the Zakat and Income Authority, it is forbidden to delete or update the invoice after it has been issued in accordance with the requirements of the electronic invoice", true);
        return $message;
    }

    public static function valid($invoice) {
        if (getenv('APP_ENV') === 'local') {
            return true;
        }
        if((isOwner() || isLoggedAsAdmin()) && $_GET['force']) {
            return true;
        }
        return !ifPluginActive(EINVOICE_SA_PLUGIN) || $invoice['Invoice']['draft'] === 1 || $invoice['Invoice']['draft'] === '1';
    }

    public static function getWorkOrderMessage() {
        return __(" You cannot delete the work order as there are related Issued Invoices", true);
    }
}
