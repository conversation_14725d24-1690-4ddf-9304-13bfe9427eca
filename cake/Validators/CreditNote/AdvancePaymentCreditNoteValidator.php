<?php

namespace App\Validators\CreditNote;

use Izam\Daftra\Invoice\Services\AdvancePaymentService;

class AdvancePaymentCreditNoteValidator
{
    private $advancePaymentService;
    private array $errors = [];
    private $baseInvoice;

    public function __construct()
    {
        $this->advancePaymentService = resolve(AdvancePaymentService::class);
    }

    private function setBaseInvoice($invoice): void
    {
        $this->baseInvoice = $invoice;
    }

    public static function validate(array $creditNoteData, array $totals, array $baseInvoice): array
    {
        $validator = new AdvancePaymentCreditNoteValidator();
        $validator->setBaseInvoice($baseInvoice);

        $clientValid = $validator->validateClient($creditNoteData);
        $currencyValid = $validator->validateCurrency($creditNoteData);
        $amountValid = $validator->validationAmount($creditNoteData, $totals);

        $isValid = $clientValid && $currencyValid && $amountValid;

        return [
            'isValid' => $isValid,
            'errors'  => $validator->errors
        ];
    }

    private function validationAmount($creditNoteData, array $totals): bool
    {
        $totalInvoice = (int)$totals['invoiceTotal'];

        $unsettledPayments = $this->advancePaymentService->getAdvancedPaymentData([$creditNoteData['subscription_id']])[0];
        $unsettledAmount = $unsettledPayments['unSettledAmount'];
        if ($totalInvoice > $unsettledAmount) {
            $this->errors['invoiceTotal'] = __(
                'The credit note’s value can’t be bigger than the advance payment invoice.',
                true
            );

            return false;
        }

        return true;
    }

    private function validateCurrency($creditNoteData): bool
    {
        $isValid = $creditNoteData['currency_code'] == $this->baseInvoice['currency_code'];
        if (!$isValid) {
            $this->errors['currency_code'] = __(
                'The currency and client on the credit note should be the same as the advance payment invoice.',
                true
            );
        }

        return $isValid;
    }

    private function validateClient($creditNoteData): bool
    {
        $isValid = $creditNoteData['client_id'] == $this->baseInvoice['client_id'];
        if (!$isValid) {
            $this->errors['client_id'] = __(
                'The client on the credit note should be the same as the advance payment invoice.',
                true
            );
        }

        return $isValid;
    }
}
