<?php

namespace App\Recovery;

class DeletedInvoiceRecovery
{
    /**
     * @var \AppModel
     */
    private $InvoiceItem;

    public function __construct()
    {
        $this->InvoiceItem = GetObjectOrLoadModel('InvoiceItem');
        $this->InvoicePayment = GetObjectOrLoadModel('InvoicePayment');

    }


    private function generateInvoiceFromActionLine($actionLine) {
        $PosShift = GetObjectOrLoadModel('PosShift');
        $invoiceShift = $PosShift->find('first', [
            'applyBranchFind' => false,
            'conditions' => [
                'PosShift.staff_id' => $actionLine['staff_id'],
                'PosShift.branch_id' => $actionLine['branch_id'],
                'PosShift.created < "'.$actionLine['created'].'"' ],
            'order' => ['PosShift.created DESC']
        ]);

        $invoice['Invoice'] = [
            'id' => $actionLine['primary_id'],
            'is_offline' => true,
            'client_id' => $actionLine['secondary_id'],
            'summary_total' => $actionLine['param1'],
            'payment_status' => $actionLine['param2'],
            'summary_paid' => $actionLine['param3'],
            'no' => $actionLine['param4'],
            'pos_shift_id' => $invoiceShift['PosShift']['id'],
            'staff_id' => $actionLine['staff_id'],
            'branch_id' => $actionLine['branch_id'],
            'sales_person_id' => $actionLine['staff_id'],
            'date' => $actionLine['created'],
            'created' => $actionLine['created'],
            'modified' => $actionLine['modified'],
            'extra_details' => json_encode([ 'unique_id' => 'invoice-fix'.'-'.uniqid() ])
        ];
        return $invoice;
    }

    private function generateInvoiceItemsFromActionLines($stockTransactionsActionLines) {
        $invoiceItems = [];
        foreach ($stockTransactionsActionLines as $stockTransactionsActionLine) {

            $matchingInvoiceItem = $this->InvoiceItem->find('first', [
                'conditions' => [
                    'InvoiceItem.product_id' => $stockTransactionsActionLine['ActionLine']['secondary_id'],
                    'InvoiceItem.quantity' => abs($stockTransactionsActionLine['ActionLine']['param1'])
                ],
                'order' => ['abs(DATEDIFF("'.$stockTransactionsActionLine['ActionLine']['created'].'", InvoiceItem.created)) ASC']
            ]);
            if ($matchingInvoiceItem) {
                unset($matchingInvoiceItem['InvoiceItem']['id'], $matchingInvoiceItem['InvoiceItem']['invoice_id']);
                $invoiceItems[] = $matchingInvoiceItem['InvoiceItem'];
            }
        }
        return $invoiceItems;
    }

    private function generatePayment($actionLine, $invoice) {

        $prevPayment = $this->InvoicePayment->find('first', [
            'applyBranchFind' => false,
            'conditions' => [
                'InvoicePayment.staff_id' => $actionLine['staff_id'],
            ],
            'order' => ['abs(DATEDIFF("'.$actionLine['created'].'", InvoicePayment.created)) ASC']
        ]);

        $treasury_id = $prevPayment['InvoicePayment']['treasury_id'];
        $amount = $invoice['Invoice']['summary_total'];
        $payment['InvoicePayment'] = array(
            'treasury_id' => $treasury_id,
            'manual_payment' => 1,
            'currency_code' => $invoice['Invoice']['currency_code'],
            'invoice_id' => $invoice['Invoice']['id'],
            'amount' => $amount,
            'status' => 1,
            'date' => $actionLine['created'],
            'payment_method' => $prevPayment['InvoicePayment']['payment_method'],
            'added_by' => 1,
            'staff_id' => $actionLine['staff_id'],
            'branch_id' => $actionLine['branch_id'],
        );
        return $payment;
    }


    public function recover(){
        $Invoice = GetObjectOrLoadModel('Invoice');
        $Client = GetObjectOrLoadModel('Client');
        $InvoicePayment = GetObjectOrLoadModel('InvoicePayment');
        $ActionLine = GetObjectOrLoadModel('ActionLine');
        $StockTransaction = GetObjectOrLoadModel('StockTransaction');
        $Req = GetObjectOrLoadModel('Requisition');
        define('IGNORE_POS_CHECK', true);
        \ActionLine::disableActionLines();
        $deletedInvoicesActionLinesQuery = "SELECT * FROM `action_lines` WHERE `action_key` = 1 and primary_id not in (select id from invoices ) and primary_id not in (select primary_id FROM `action_lines` WHERE `action_key` = 5) and source like '%api2%';";
        $results = $ActionLine->query($deletedInvoicesActionLinesQuery);
        $i = 0;
        $notfoundClientCounter = 0;
        foreach ($results as $actionLine) {
            $actionLine = $actionLine['action_lines'];
            $invoice = $this->generateInvoiceFromActionLine($actionLine);

            $client = $Client->find('first', ['applyBranchFind' => false, 'conditions' => ['Client.id' => $actionLine['secondary_id']]]);
            if (!$client) {
                $notfoundClientCounter++;
                continue;
            }
            $invoiceReq = $Req->find('all', [
                'applyBranchFind' => false,
                'conditions' => [
                    'Requisition.order_id' => $invoice['Invoice']['id'],
                    'Requisition.order_type' => \Requisition::ORDER_TYPE_INVOICE
                ]]);
            dump([
                'ActionLine.primary_id' => $invoice['Invoice']['id'],
                'ActionLine.action_key' => ACTION_TRANSACTION_INVOICE_ADDED
            ]);
            $stockTransactionsActionLines = $ActionLine->find('all', ['applyBranchFind' => false, 'conditions' => [
                'ActionLine.primary_id' => $invoice['Invoice']['id'],
                'ActionLine.action_key' => ACTION_TRANSACTION_INVOICE_ADDED
            ]]);
            if ($invoiceReq) {

            } else if ($stockTransactionsActionLines) {
                $invoice['InvoiceItem'] = $this->generateInvoiceItemsFromActionLines($stockTransactionsActionLines);
                $invoice['Invoice']['currency_code'] = $stockTransactionsActionLines[0]['ActionLine']['param7'];
                dump('invoice from stock transaction',$invoice);
            } else {
                $matchInvoice = $Invoice->find('first', [
                    'applyBranchFind' => false,
                    'conditions' => ['Invoice.summary_total' => $invoice['Invoice']['summary_total']],
                    'order' => ['abs(DATEDIFF("' . $actionLine['created'] . '", Invoice.date)) ASC']
                ]);
                if ($matchInvoice) {
                    $matchInvoice = $Invoice->getInvoice($matchInvoice['Invoice']['id']);
                }
                if (!$matchInvoice) {
                    //get nearest invoice by value and adjust its invoice items to match
                    $matchInvoice = $Invoice->find('first', [
                        'applyBranchFind' => false,
                        'order' => ['abs(Invoice.summary_total - ' . $invoice['Invoice']['summary_total'] . ') ASC', 'abs(DATEDIFF("' . $actionLine['created'] . '", Invoice.date)) ASC']
                    ]);
                    $diff = $invoice['Invoice']['summary_total'] - $matchInvoice['Invoice']['summary_total'];
                    dump($matchInvoice);
                    $firstInvoiceItem = $matchInvoice['InvoiceItem'][0];
                    if ($diff > 0) {
                        $firstInvoiceItem['unit_price'] += $diff / $firstInvoiceItem['quantity'];
                    } else {
                        $firstInvoiceItem['unit_price'] -= $diff / $firstInvoiceItem['quantity'];
                    }
                    $matchInvoice['InvoiceItem'][0] = $firstInvoiceItem;
                    dump('artificial');
                } else {
                    dump('original');
                }
                if ($matchInvoice) {
                    $invoice = ['Invoice' => $matchInvoice['Invoice'], 'InvoiceItem' => $matchInvoice['InvoiceItem']];
                }
            }
            if ($invoice) {
                foreach ($invoice['InvoiceItem'] as &$invoiceItem) {
                    $invoiceItem['invoice_id'] = $actionLine['primary_id'];
                    unset($invoiceItem['id']);
                }
                unset($invoiceItem);
                dump($invoice);
                $result = $Invoice->addInvoice($invoice);
                $payment = $this->generatePayment($actionLine, $result['data']);
                if ($result['status']) {
                    $re_read_invoice = $Invoice->getInvoice($result['data']['Invoice']['id']);
                    $Invoice->ownerAddPayment($payment);
                    if ($stockTransactionsActionLines) {
                        \StockTransaction::updateForInvoice($re_read_invoice);
                        foreach ($stockTransactionsActionLines as $stockTransactionsActionLine) {
                            $ActionLine->deleteAll([
                                'ActionLine.action_key' => ACTION_TRANSACTION_INVOICE_DELETED,
                                'ActionLine.primary_id' => $stockTransactionsActionLine['ActionLine']['primary_id']
                            ]);
                        }
                    }
                } else {
                    dump('Invoice Save Failed');
                    dump($invoice);
                    dump($Invoice->validationErrors);
                }
            }
        }
        echo "not found clients counter: ".$notfoundClientCounter;
        die('matching count:'. $i);
        die(dump($results));
    }
}