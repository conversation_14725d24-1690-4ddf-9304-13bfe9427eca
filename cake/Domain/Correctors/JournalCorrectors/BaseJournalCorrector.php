<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 02/03/20
 * Time: 11:31 ص
 */

namespace App\Domain\Correctors\JournalCorrectors;


class BaseJournalCorrector implements JournalCorrectorInterface
{
    protected $Model;
    protected $JournalModel;
    protected $entityType;
    public function __construct($entityType, $JournalModel, $Model)
    {
        $this->entityType = $entityType;
        $this->Model = $Model;
        $this->JournalModel = $JournalModel;
    }

    function correct($recordToCorrect)
    {
        $this->Model->id = $recordToCorrect[$this->Model->alias]['id'];
        if(!$result = $this->Model->save($recordToCorrect, false)) {
            debug('fail');

        }
        return $result;
    }

    function getRecordsToCorrect()
    {
        $entityIds = $this->JournalModel->find('list', ['conditions' => ['entity_type' => $this->entityType] ,'fields' => ['entity_id', 'entity_id']]);
        $entitiesWithNoJournals = $this->Model->find('all', ['conditions' => ['NOT' => [$this->Model->alias.'.id' => $entityIds]]]);
        return $entitiesWithNoJournals;
    }
}