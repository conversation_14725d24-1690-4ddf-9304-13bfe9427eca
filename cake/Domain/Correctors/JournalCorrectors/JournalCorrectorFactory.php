<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 02/03/20
 * Time: 11:27 ص
 */

namespace App\Domain\Correctors\JournalCorrectors;


class JournalCorrectorFactory
{
    private function __construct()
    {
    }

    /**
     * @param $entityType
     * @param $Model
     * @return JournalCorrectorInterface
     */
    static function create($entityType, $journalModel,$model) {
        $mapping = [
            'invoice_payment' => InvoicePaymentJournalCorrector::class
        ];
        $class = null;
        if(isset($mapping[$entityType])) {
            return new $mapping[$entityType]($entityType, $journalModel, $model);
        } else {
            return new BaseJournalCorrector($entityType, $journalModel, $model);
        }
    }
}