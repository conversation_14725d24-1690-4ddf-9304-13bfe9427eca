<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 02/03/20
 * Time: 11:29 ص
 */

namespace App\Domain\Correctors\JournalCorrectors;


class InvoicePaymentJournalCorrector extends BaseJournalCorrector
{
    public function getRecordsToCorrect()
    {
        \App::import('Vendor', 'settings');

        if (ifPluginActive(PosPlugin)) {
            $pos_accounting_settings = \settings::getValue(PosPlugin, "pos_accounting_settings");
            if (empty($pos_accounting_settings) && !empty($data['Invoice']['pos_shift_id'])) {
                return false;
            }
        }

        $disable_invoice_auto_journals = \settings::getValue(AccountingPlugin, "disable_invoice_auto_journal"  );
        if($disable_invoice_auto_journals){
            return false;
        }

        $entityIds = $this->JournalModel->find('list', ['conditions' => ['entity_type' => $this->entityType] ,'fields' => ['entity_id', 'entity_id']]);
        $entitiesWithNoJournals = $this->Model->find('all', ['conditions' => [
            'NOT' => [
                'InvoicePayment.id' => $entityIds,
                'InvoicePayment.payment_method' => 'client_credit'
            ],
            'InvoicePayment.amount IS NOT NULL',
            'InvoicePayment.status' => PAYMENT_STATUS_COMPLETED,
            'Invoice.draft' => 0,
            'OR' => ['InvoicePayment.client_id IS NOT NULL', 'InvoicePayment.invoice_id IS NOT NULL']
        ]]);
        return $entitiesWithNoJournals;
    }
}