<?php


namespace App\Domain\Correctors\RequsitionCorrectors;

/**
 * Class CreateAutoRequisitionsCorrector
 * @package App\Domain\Correctors\RequsitionCorrectors
 * this class handles creation of auto requisition for entities that was supposed to have auto requisition
 * but for some reason it dosen't have
 */
class CreateAutoRequisitionsCorrector
{
    /**
     * @var \AppModel
     */
    private $Requisition;
    public function __construct()
    {
        $this->Requisition = GetObjectOrLoadModel('Requisition');
    }

    public function correct() {
        $this->createInvoiceRequisitions();;
    }

    public function createInvoiceRequisitions() {
        \App::import('Vendor', 'settings');
        $enable_requisitions = \settings::getValue(InventoryPlugin, 'enable_requisitions');
        GetObjectOrLoadModel('StockTransaction');
        if($enable_requisitions) {

            $invoiceRequisitionTypes = [
                \Invoice::Invoice => \Requisition::ORDER_TYPE_INVOICE,
                \Invoice::Refund_Receipt => \Requisition::ORDER_TYPE_INVOICE_REFUND,
                \Invoice::Credit_Note => \Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE
            ];

            $invoiceStockTransactionSourceTypes = [
                \StockTransaction::SOURCE_INVOICE,
                \StockTransaction::SOURCE_CN,
                \StockTransaction::SOURCE_RR,
            ];
            $Invoice = GetObjectOrLoadModel('Invoice');
            $invoicesThatHaveNotRequisitionsSql = "SELECT id
                    FROM invoices
                    WHERE id NOT IN
                        (SELECT order_id
                         FROM requisitions
                         WHERE order_type IN (".implode(',',$invoiceRequisitionTypes)."))
                      AND id IN
                        (SELECT invoice_id
                         FROM invoice_items
                         WHERE product_id IN
                             (SELECT id
                              FROM products
                              WHERE track_stock = 1))
                      AND id NOT IN
                        (SELECT order_id
                         FROM stock_transactions
                         WHERE source_type IN (".implode(",", $invoiceStockTransactionSourceTypes)."))";
            $invoiceIds = $Invoice->query($invoicesThatHaveNotRequisitionsSql);
            if (!$invoiceIds) {
                return;
            }
            $invoiceIds = array_map(function ($el) {
                return $el['invoices']['id'];
            }, $invoiceIds);
            $invoices = $Invoice->find('all', ['conditions' => ['Invoice.id' => $invoiceIds]]);
            $pos_accounting_settings = \settings::getValue(PosPlugin, "pos_accounting_settings");

            foreach ($invoices as $invoice) {
                $createRequisition = true;
                if (ifPluginActive(PosPlugin)) {
                    if (empty($pos_accounting_settings) && !empty($invoice['Invoice']['pos_shift_id'])) {
                        $createRequisition = false;
                    }
                }
                if ($createRequisition) {
                    $this->Requisition->updateForOrder($invoice, $invoiceRequisitionTypes[$invoice['Invoice']['type']], false , false);
                }
            }
        }
    }
}