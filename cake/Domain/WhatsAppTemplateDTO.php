<?php

namespace App\Domain;
class WhatsAppTemplateDTO
{
    /**
     * @var int|null
     */
    public $id;

    /**
     * @var string
     */
    public $name;

    /**
     * @var string
     */
    public $language;

    /**
     * @var string
     */
    public $category;

    /**
     * @var WhatsAppTemplateComponentDTO[]
     */
    public array $components = [];

    public function __construct($id, $name, $language, $category, array $components = [])
    {
        $this->id = $id;
        $this->name = $name;
        $this->language = $language;
        $this->category = $category;
        $this->components = $components;
    }
}
