<?php


namespace App\Domain\JournalRoute\Savers;


use App\Domain\JournalRoute\Util;

class RouteSaverFactory
{
    /**
     * @param $transactionType
     * @return IRouteSaver|bool
     */
    public static function getSaver($transactionType) {
        $defaultSaver = new DefaultSaver();
        switch ($transactionType) {
            case Util::TRANSACTION_TYPE_INVOICE:
            case Util::TRANSACTION_TYPE_CREDIT_NOTE:
            case Util::TRANSACTION_TYPE_DEBIT_NOTE:    
            case Util::TRANSACTION_TYPE_REFUND_RECEIPT:
            case Util::TRANSACTION_TYPE_PURCHASE_ORDER:
            case Util::TRANSACTION_TYPE_PURCHASE_REFUND:
            case Util::TRANSACTION_TYPE_PURCHASE_DEBIT_NOTE:    
            case Util::TRANSACTION_TYPE_PURCHASE_CREDIT_NOTE:  
                return new TransactionRouteSaver($defaultSaver, $transactionType);
                break;
            case Util::TRANSACTION_TYPE_CLIENT:
            case Util::TRANSACTION_TYPE_SUPPLIER:
            case Util::TRANSACTION_TYPE_STORE:
            case Util::TRANSACTION_TYPE_TREASURY:
            case Util::TRANSACTION_TYPE_TREASURY:
            case Util::ASSET_DEPRECATION_ACCOUNT_ENTITY_TYPE:
            case Util::ASSET_DEPRECATION_EXPENSE_ACCOUNT_ENTITY_TYPE:    
            case Util::ASSET_RE_EVALUATION_ACCOUNT:    
            case Util::ASSET_WRITE_OFF_ACCOUNT:    
                return new AccountRouteSaver($defaultSaver, $transactionType);
                break;
        }
        return false;
    }

    public static function getSupportedFixedTransactions() {
        return Util::getSupportedFixedTransactions();
    }
}