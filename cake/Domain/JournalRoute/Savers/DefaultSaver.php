<?php


namespace App\Domain\JournalRoute\Savers;

use App\Domain\JournalRoute\Util;
use Izam\ManufacturingOrder\Services\ManufacturingOrderService;

class DefaultSaver implements IRouteSaver
{
    public function save($data, $entityId) {
        if(!empty($data) && !empty($entityId)) {
            $data['entity_id'] = $entityId;
            $JAR = GetObjectOrLoadModel('JournalAccountRoute');
            $JAR->saveRoutedAccount(['JournalAccountRoute' => $data]);
            if(ifPluginActive(MANUFACTURING_PLUGIN) && $data['is_transaction'] == 0 && $data['transaction_type'] == Util::ASSET_DEPRECATION_EXPENSE_ACCOUNT_ENTITY_TYPE){
                /** @var ManufacturingOrderService */
                $manufacturingOrderService = resolve(ManufacturingOrderService::class);
                $manufacturingOrderService->updateAssetDeprAccForManufacturing($data['entity_id']);
            }
        }
    }
}