<?php


namespace App\Domain\JournalRoute\Savers;


use App\Domain\JournalRoute\Util;

abstract class AbstractTransactionRouteSaver implements IRouteSaver
{
    /**
     * @var DefaultSaver
     */
    protected $defaultSaver;

    public function __construct($defaultSaver)
    {
        $this->defaultSaver = $defaultSaver;
    }

    protected abstract function getTransactionType();


    public function save($data, $entityId)
    {
        $data['is_transaction'] = 1;
        $data['transaction_type'] = $this->getTransactionType();
        $this->defaultSaver->save($data, $entityId);
    }
}