<?php


namespace App\Domain\JournalRoute\Savers;


use App\Domain\JournalRoute\Util;

class TransactionRouteSaver implements IRouteSaver
{
    /**
     * @var DefaultSaver
     */
    protected $defaultSaver;
    protected $transactionType;

    public function __construct($defaultSaver, $transactionType = null)
    {
        $this->transactionType =  $transactionType;
        $this->defaultSaver = $defaultSaver;
    }

    public function getTransactionType() {
        return $this->transactionType;
    }

    public function save($data, $entityId)
    {
        $data['is_transaction'] = 1;
        $data['transaction_type'] = $this->getTransactionType();

        $this->defaultSaver->save($data, $entityId);
        $this->updateJournals($data, $entityId);
    }

    public function updateJournals($data, $entityId) {
        $Model = null;
        $modelData = [];
        switch ($data['transaction_type']) {
            case Util::TRANSACTION_TYPE_INVOICE:
            case Util::TRANSACTION_TYPE_CREDIT_NOTE:
            case Util::TRANSACTION_TYPE_REFUND_RECEIPT:
            case Util::TRANSACTION_TYPE_DEBIT_NOTE:
                $Model = GetObjectOrLoadModel('Invoice');
                $modelData = $Model->getInvoice($entityId);
                break;
            case Util::TRANSACTION_TYPE_PURCHASE_ORDER:
            case Util::TRANSACTION_TYPE_PURCHASE_REFUND:
            case Util::TRANSACTION_TYPE_PURCHASE_DEBIT_NOTE:
                $Model = GetObjectOrLoadModel('PurchaseOrder');
                $modelData = $Model->getPurchaseOrder($entityId);
                break;
        }
        if($Model && $modelData) {
            $Model->update_journals($modelData);
        }
    }
}
