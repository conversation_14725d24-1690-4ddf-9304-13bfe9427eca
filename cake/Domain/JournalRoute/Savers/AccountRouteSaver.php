<?php


namespace App\Domain\JournalRoute\Savers;


class AccountRouteSaver implements IRouteSaver
{
    /**
     * @var DefaultSaver
     */
    protected $defaultSaver;
    protected $transactionType;

    public function __construct($defaultSaver, $transactionType = null)
    {
        $this->transactionType =  $transactionType;
        $this->defaultSaver = $defaultSaver;
    }

    public function getTransactionType() {
        return $this->transactionType;
    }

    public function save($data, $entityId)
    {
        $data['is_transaction'] = 0;
        $data['transaction_type'] = $this->getTransactionType();
        $this->defaultSaver->save($data, $entityId);
    }
}
