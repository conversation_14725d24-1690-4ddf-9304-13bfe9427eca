<?php


namespace App\Domain\JournalRoute;

use Izam\Daftra\Common\Utils\JournalAccountRouteUtil;

class Util extends JournalAccountRouteUtil
{
    const TRANSACTION_TYPE_INVOICE = 'invoice';
    const TRANSACTION_TYPE_REFUND_RECEIPT = 'refund_receipt';
    const TRANSACTION_TYPE_CREDIT_NOTE = 'credit_note';
    const TRANSACTION_TYPE_DEBIT_NOTE = 'debit_note';

    const TRANSACTION_TYPE_PURCHASE_ORDER = 'purchase_order';
    const TRANSACTION_TYPE_PURCHASE_REFUND = 'purchase_refund';
    const TRANSACTION_TYPE_PURCHASE_DEBIT_NOTE = 'purchase_debit_note';
    const TRANSACTION_TYPE_PURCHASE_CREDIT_NOTE = 'purchase_credit_note';

    const TRANSACTION_TYPE_CLIENT = 'client';

    const TRANSACTION_TYPE_SUPPLIER = 'supplier';

    const TRANSACTION_TYPE_STORE = 'store';

    const TRANSACTION_TYPE_TREASURY = 'treasury';
    const ASSET_DEPRECATION_ACCOUNT_ENTITY_TYPE = 'asset_deprecation';
    const ASSET_DEPRECATION_EXPENSE_ACCOUNT_ENTITY_TYPE = 'asset_deprecation_expense';
    const ASSET_RE_EVALUATION_ACCOUNT = 're_evaluate';
    const ASSET_WRITE_OFF_ACCOUNT = 'write_off';

}