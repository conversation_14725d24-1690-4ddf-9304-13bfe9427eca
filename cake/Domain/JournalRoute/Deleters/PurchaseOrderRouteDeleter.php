<?php


namespace App\Domain\JournalRoute\Deleters;


class PurchaseOrderRouteDeleter
{
    public function delete($journal) {
        $JAR = GetObjectOrLoadModel('JournalAccountRoute');
        foreach ($journal['JournalTransaction'] as $transaction) {
            $conditions = ['JournalAccountRoute.account_id' => $transaction['journal_account_id'], 'JournalAccountRoute.entity_type' => ['purchases', 'sales_cost'], 'JournalAccountRoute.entity_id' => $journal['Journal']['entity_id']];
            $JAR->deleteAll($conditions);
        }
    }
}