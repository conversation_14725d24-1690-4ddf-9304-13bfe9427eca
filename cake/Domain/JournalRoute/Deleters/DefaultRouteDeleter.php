<?php


namespace App\Domain\JournalRoute\Deleters;


class DefaultRouteDeleter
{
    public function delete($journal) {
        $JAR = GetObjectOrLoadModel('JournalAccountRoute');
        foreach ($journal['JournalTransaction'] as $transaction) {
            $conditions = ['JournalAccountRoute.account_id' => $transaction['journal_account_id'], 'entity_type' => DeleterFactory::$supportedTypes[$journal['Journal']['entity_type']], 'entity_id' => $journal['Journal']['entity_id']];
            $JAR->deleteAll($conditions);
        }
    }
}