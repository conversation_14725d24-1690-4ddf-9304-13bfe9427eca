<?php


namespace App\Domain\JournalRoute\Deleters;

/**
 * Class DeleterFactory
 * @package App\Domain\JournalRoute\Deleters
 * responsible for deleting transaction routes
 */
class DeleterFactory
{

    public static $supportedTypes = [
            \Journal::JOURNAL_ENTITY_TYPE_INVOICE =>\Journal::SALES_ACCOUNT_ENTITY_TYPE,
            \Journal::JOURNAL_ENTITY_TYPE_REFUND_RECIEPT => \Journal::RETURNS_ACCOUNT_ENTITY_TYPE,
            \Journal::JOURNAL_ENTITY_TYPE_PURCHASE_REFUND => \Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE,
            \Journal::JOURNAL_ENTITY_TYPE_PURCHASE_ORDER => \Journal::PURCHASES_ACCOUNT_ENTITY_TYPE,

        ];

    public static $entityTypesModels = [
        \Journal::SALES_ACCOUNT_ENTITY_TYPE => 'Invoice',
        \Journal::RETURNS_ACCOUNT_ENTITY_TYPE => 'Invoice',
        \Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE => 'PurchaseOrder',
        \Journal::PURCHASES_ACCOUNT_ENTITY_TYPE => 'PurchaseOrder'
    ];

    public static function getJournalRouteRemovers($journalType) {
        if(in_array($journalType, array_keys(self::$supportedTypes))) {
            switch ($journalType) {
                case 'purchase_order':
                    return new PurchaseOrderRouteDeleter();
                    break;
                default:
                    return new DefaultRouteDeleter();
            }
        }
        return false;
    }

    public static function correctFailedDelete() {
        $Jar = GetObjectOrLoadModel('JournalAccountRoute');
        $jars = $Jar->find('all');
        $failedDeleteCounter = 0;
        foreach ($jars as $jar) {
            if(!isset(self::$entityTypesModels[$jar['JournalAccountRoute']['entity_type']])) {
                continue;
            }
            $modelName = self::$entityTypesModels[$jar['JournalAccountRoute']['entity_type']];
            $Model = GetObjectOrLoadModel($modelName);
            $record = $Model->findById($jar['JournalAccountRoute']['entity_id']);
            if(!$record) {
                $Jar->delete($jar['JournalAccountRoute']['id']);
                $failedDeleteCounter++;
            }
        }
    }

}