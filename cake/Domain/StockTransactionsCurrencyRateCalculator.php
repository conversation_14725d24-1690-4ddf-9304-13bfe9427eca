<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 18/11/19
 * Time: 10:18 ص
 */

namespace App\Domain;


class StockTransactionsCurrencyRateCalculator
{
    public static function calculate()
    {
        \App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
        $StockTransaction = GetObjectOrLoadModel('StockTransaction');
        $JournalTransaction = GetObjectOrLoadModel('JournalTransaction');
        $Site = GetObjectOrLoadModel('Site');

        $defaultCurrency = $StockTransaction->get_default_currency();
        $StockTransaction->recursive = -1;
        $stockTransactions = $StockTransaction->find('all', ['order' => ['StockTransaction.date' => 'ASC'],'conditions' => ['NOT' => ['StockTransaction.currency_code' => $defaultCurrency]]]);
        $datesCurrencyRates = [];
        $stockTransactionsCurrencyRates = [];
        if($stockTransactions)
        {
            $firstDate = $stockTransactions[0]['StockTransaction']['date'];
            $lastDate = end($stockTransactions)['StockTransaction']['date'];
            $journalTransactionCurrencyRates = $JournalTransaction->find('all', [
                'conditions' => [
                    'NOT' => ['Journal.currency_code' => $defaultCurrency],'Journal.date >=' => $firstDate, 'Journal.date <=' =>$lastDate
                    ],
                    'group' => ['Journal.date', 'Journal.currency_code'],
                    'fields' => ['Journal.date', 'JournalTransaction.currency_rate', 'JournalTransaction.currency_code'],

            ]);

            foreach ($journalTransactionCurrencyRates as  $journalTransactionCurrencyRate)
            {
              $currencyCode =$journalTransactionCurrencyRate['JournalTransaction']['currency_code'];
              $date = $journalTransactionCurrencyRate['Journal']['date'];
              $datesCurrencyRates[$date][$currencyCode] = $journalTransactionCurrencyRate['JournalTransaction']['currency_rate'];
            }

            foreach ($stockTransactions as &$transaction)
            {
                $currencyCode = $transaction['StockTransaction']['currency_code'];
                if($transaction['StockTransaction']['date'] === '0000-00-00 00:00:00')
                {
                    $date = '0000-00-00';
                }else{
                    $date = date('Y-m-d', strtotime($transaction['StockTransaction']['date']));
                }

                if(isset($datesCurrencyRates[$date][$currencyCode]))
                {
                    $currencyRate = $datesCurrencyRates[$date][$currencyCode];
                }else{
                    $currencyRate = \CurrencyConverter::index($currencyCode, $defaultCurrency, $date);
                    $datesCurrencyRates[$date][$currencyCode] = $currencyRate;
                }
                $stockTransactionsCurrencyRates[$currencyRate][] = $transaction['StockTransaction']['id'];
            }

            foreach ($stockTransactionsCurrencyRates as $currencyRate => $transactionIds)
            {
                $query = "UPDATE stock_transactions set currency_rate = $currencyRate where id in (" . implode(',', $transactionIds) . ")";
                $StockTransaction->query($query);
            }
        }
    }
}