<?php


namespace App\Transformers;


class ServiceModelDataTransformer
{
    /**
     * @param [] $data
     * @param string $modelName
     * @return array
     */
    public static function transform($data, $modelName) {
        if(isset($data[$modelName])) {
            $modelData = $data[$modelName];
            unset($data[$modelName]);
            $data = array_merge($modelData, $data);
        }
        return $data;
    }
}