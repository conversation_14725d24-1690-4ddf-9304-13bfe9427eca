<?php


namespace App\Auth;


use Izam\Daftra\Common\Auth\IAuthRepository;

class AuthRepository implements IAuthRepository
{
    /**
     * @var \AppModel
     */
    private $Site;
    /**
     * @var \AppModel
     */
    private $Client;
    /**
     * @var \AppModel
     */
    private $Staff;

    public function __construct()
    {
    }

    public function findSite($siteId)
    {
        $this->Site = GetObjectOrLoadModel('Site');
        $site = $this->Site->findById($siteId);
        if (empty($site)) {
            throw new \Exception();
        }
        return $site;
    }

    public function getCurrentSite()
    {
        $site = getCurrentSite();
        if (!$site) {
            throw new \Exception();
        }
        return $site;
    }

    public function findStaff($staffId)
    {
        $this->Staff = GetObjectOrLoadModel('Staff');
        $staff = $this->Staff->findById($staffId);
        if (empty($staff)) {
            throw new \Exception();
        }
        return $staff;
    }

    public function findClient($clientId)
    {
        $this->Client = GetObjectOrLoadModel('Client');
        $client = $this->Client->findById($clientId);
        if (empty($client)) {
            throw new \Exception();
        }
        return $client;
    }

    public function revokeAllAccessTokensForUser($type, $id)
    {
        $accessTokenModel = GetObjectOrLoadModel('CurrentSiteOauthAccessToken');
        $refreshTokenModel = GetObjectOrLoadModel('CurrentSiteOauthRefreshToken');

        $tokens = $accessTokenModel->find('all', [
            'conditions' => [
                'provider' => $type,
                'user_id' => $id,
                'revoked' => 0
            ], 'fields' => 'id'
        ]);

        if (empty($tokens)) {
            return;
        }

        $ids = [];
        
        foreach ($tokens as $token) {
            $ids[] = "'" . $token['CurrentSiteOauthAccessToken']['id'] . "'";
        }

        $accessTokenModel->updateAll(
            ['revoked' => 1],
            ['id IN ('. implode(',', $ids). ')']
        );

        $refreshTokenModel->updateAll(
            ['revoked' => 1],
            ['access_token_id IN ('. implode(',', $ids). ')']
        );
    }
}
