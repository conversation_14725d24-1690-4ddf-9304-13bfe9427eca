<?php

namespace App\Auth\Strategy;

use App\Auth\Strategy\Interfaces\StrategyInterface;
use Izam\Daftra\Cache\PortalCache;

class ApiKeyAuth implements StrategyInterface
{
    private $apiKeyRecord;
    public function authenticate($headers, $params = [])
    {
        $cacheName = "api_keys";
        $apiKeys = PortalCache::get($cacheName, function() use ($cacheName) {
            $api_key_model = GetObjectOrLoadModel('ApiKey');
            $apiKeys = $api_key_model->find('all', [
                'conditions' => [
                    'ApiKey.site_id' => getCurrentSite('id'),
                ]
            ]);
            return $apiKeys;
        });
        $apiKeys = collect($apiKeys);
        $client = getAuthClient();
        $owner = getAuthOwner();
        $this->apiKeyRecord = !empty($headers['HTTP_APIKEY']) && $apiKeys->where('ApiKey.key', $headers['HTTP_APIKEY'])->first();
        // Whitelist get menu action if logged in as client
        if (!empty($client) && ($params['prefix'] == 'client' || $params['action'] == 'api_get_layout')) {
            return true;
        }
        // Logged in as $owner
        if (!empty($owner)) {
            return true;
        }
        // Check API_KEY exist and valid
        if ($this->apiKeyRecord) {
            return true;
        }
        return false;
    }

    public function checkRoles($url){
        $apiKeyRecord = $this->apiKeyRecord;
        if($apiKeyRecord['ApiRole'] == null){
            return true;
        }
        if($apiKeyRecord['ApiRole']['role_type'] == 0){ // 1 => only allow , 0 => only blocks
            $blockedURLs = json_decode($apiKeyRecord['ApiRole']['urls']);
            if(!empty($blockedURLs)){
                foreach ($blockedURLs as $blockedURL) {
                    $exactBlockMatch = substr($blockedURL, -1) === "/";
                    $blockedURL = ($exactBlockMatch) ? substr($blockedURL, 0, -1) : $blockedURL;
                    if ($exactBlockMatch && $url != $blockedURL) continue;
                    if (strpos($url, $blockedURL) !== false || strpos($url, str_replace('/*', '', $blockedURL)) !== false) {
                        return false;
                    }
                }
            }
            return true;
        }else{
            $allowedURLs = json_decode($apiKeyRecord['ApiRole']['urls']);
            if(!empty($allowedURLs)){
                foreach ($allowedURLs as $allowedURL) {
                    $exactAllowMatch = substr($allowedURL, -1) === "/";
                    $allowedURL = ($exactAllowMatch) ? substr($allowedURL, 0, -1) : $allowedURL;
                    if ($exactAllowMatch && $url != $allowedURL) continue;
                    if (strpos($url, $allowedURL) !== false || strpos($url, str_replace('/*', '', $allowedURL)) !== false) {
                        return true;
                    }
                }
                return false;
            }
            return true;
        }
    }
}
