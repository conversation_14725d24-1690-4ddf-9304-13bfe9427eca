<?php

namespace App\Auth\Strategy;

use App\Auth\Strategy\Interfaces\StrategyInterface;
use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Portal\Auth\Exception\InvalidScopeException;
use Izam\Portal\Auth\Interfaces\IRequestedPermissionInterface;
use Roll<PERSON>\Rollbar;

class OAuth implements StrategyInterface, IRequestedPermissionInterface
{
    private ?string $permission;

    public function authenticate($headers, $params = [])
    {
        $jwt = str_replace('Bearer ', '', $headers['Authorization']);

        if (false == $this->authenticateViaPortal($jwt)) {
            return $this->authenticateViaCurrentSite($jwt);
        }

        return true;
    }

    public function setRequestedPermission(?string $permission): void
    {
        $this->permission = $permission;
    }

    private function authenticateViaPortal($jwt)
    {
        $publicKey = file_get_contents(PORTAL_OAUTH_PUBLIC_KEY_PATH);
        if (!$publicKey) {
            Rollbar::error('Oauth public key not found on server ' . CURRENT_SERVER_NAME, [
                'CURRENT_SERVER_NAME' => CURRENT_SERVER_NAME,
                'path' => PORTAL_OAUTH_PUBLIC_KEY_PATH
            ]);
        }
        $decodedToken = AuthHelper::decodeToken($jwt, $publicKey);

        if (!$decodedToken) {
            return false;
        }

        if (
            !isset($decodedToken['jti']) ||
            !isset($decodedToken['app_id']) ||
            !isset($decodedToken['site_id']) ||
            $decodedToken['site_id'] != getCurrentSite('id')
        ) {
            return false;
        }

        $oAuthAccessTokenModel = GetObjectOrLoadModel('OauthAccessToken');

        $oAuthAccessToken = $oAuthAccessTokenModel->find('first', [
            'conditions' => [
                'id' => $decodedToken['jti'],
                'revoked'=> 0
            ]
        ]);
        if (empty($oAuthAccessToken)) {
            return false;
        }

        $appModel = GetObjectOrLoadModel('AppKey');

        $result = $appModel->find('first', [
            'conditions' => ['id' => $decodedToken['app_id']]
        ]);
        $app = $result['AppKey'];
        if (!$app) {
            return false;
        }

        define('APP_INFO', $app);

        $appSiteModel = GetObjectOrLoadModel('AppSite');

        if (
            empty($appSiteModel->find('count', [
                'conditions' => [
                    'app_id' => $app['id'],
                    'site_id' => $decodedToken['site_id']]
                ]))
        ) {
            return false;
        }

        /* if app is zapier do not check for scopes */
        if ($decodedToken['aud'] == 1) {
            return true;
        }

        /* The access token was created before 24/02/2025. ignore scope validation */
        if ($decodedToken['iat'] < strtotime('2025-02-24')) {
            return true;
        }

        $tokenScopes = $decodedToken['scopes'];

        if (!$this->validateScope($tokenScopes)) {
            throw new InvalidScopeException();
        }

        return true;
    }

    private function authenticateViaCurrentSite($jwt)
    {
        $publicKey = file_get_contents(SITE_OAUTH_PUBLIC_KEY_PATH);

        $decodedToken = AuthHelper::decodeToken($jwt, $publicKey);

        if (!$decodedToken) {
            return false;
        }

        $oAuthAccessTokenModel = GetObjectOrLoadModel('CurrentSiteOauthAccessToken');

        $oAuthAccessToken = $oAuthAccessTokenModel->find('first', [
            'conditions' => [
                'id' => $decodedToken['jti'],
                'revoked' => 0
            ]
        ]);

        if ([] == $oAuthAccessToken) {
            return false;
        }

        return true;
    }

    private function validateScope(array $accessTokenScopes): bool
    {
        if ($accessTokenScopes == ['*']) {
            return true;
        }
        return in_array($this->permission, array_merge($accessTokenScopes, ['*']));
    }
}
