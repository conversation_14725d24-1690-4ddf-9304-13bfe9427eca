<?php

namespace App\Listeners;

use App\Events\Queue\AveragePriceChanged;
use App\Helpers\ManufacturingOrderHelper;
use Izam\Daftra\Queue\Listeners\IEventHandler;
use Izam\ManufacturingOrder\Services\ManufacturingOrderCostSheetService;
use Izam\ManufacturingOrder\Services\ManufacturingOrderService;

class ManufactureOrderListener implements IEventHandler
{
    private $RequisitionModel;
    private $RequisitionItemModel;
    private $ManufacturingOrderModel;
    private $manufacturingOrderService;
    private $manufactureOrderCostSheetService;
    private $manufactureOrderIds = [];
    private $manufacturingOrders;
    private $StockTransactionModel;
    private $averagePriceProductId;

    public function handle($event)
    {
        // Extend the time limit to 5 hours
        set_time_limit(3600 * 5);

        // If Manufacturing Plugin is not active, return
        if (!ifPluginActive(MANUFACTURING_PLUGIN)) {
            return;
        }

        // Load Models and Services
        $this->RequisitionModel = GetObjectOrLoadModel('Requisition');
        $this->RequisitionItemModel = GetObjectOrLoadModel('RequisitionItem');
        $this->StockTransactionModel = GetObjectOrLoadModel('StockTransaction');
        $this->ManufacturingOrderModel = GetObjectOrLoadModel('ManufacturingOrder');
        $this->manufacturingOrderService = resolve(ManufacturingOrderService::class);
        $this->manufactureOrderCostSheetService = resolve(ManufacturingOrderCostSheetService::class);
        $eventData = $event->data;
        $this->averagePriceProductId = $eventData->product_id ?? null;
        $from_date = $eventData->old_received_date ?? $eventData->start_date ?? date('Y-m-d H:i:s');

        // Manufacture order IDs from outbound requisitions
        $this->initManufactureOrderIdsFromOutboundManufactureRequisitions($this->averagePriceProductId, $from_date);
        // This is required before updating the Inbound Manufacture Product Price
        $this->updateManufactureOrdersTotalCosts();
        $this->updateInboundRequisitionsManufactureOrders();
        $this->updateManufactureOrderCostSheetData();
        $this->updateManufactureOrders();
        $this->updateManufactureOrdersJournals();
    }

    private function updateRequisitionAndRelatedData($ref_id, $requisition_id, $unit_price, $quantity) {
        // Update Requisition Item
        $this->RequisitionItemModel->id = $ref_id;
        $this->RequisitionItemModel->saveField('unit_price' , $unit_price);
        $this->RequisitionItemModel->saveField('subtotal' , $unit_price * abs($quantity));
        $requisition = $this->RequisitionModel->find('first' , ['recursive' => -1, 'conditions' => ['Requisition.id' => $requisition_id]]);
        // Update Requisition Journals
        $this->RequisitionModel->update_journals($requisition , null);
        // Update StockTransaction
        $stock_transaction_id = $this->StockTransactionModel->find('first', ['recursive' => -1, 'conditions' => ['StockTransaction.ref_id' => $ref_id]])['StockTransaction']['id'];
        $this->StockTransactionModel->id = $stock_transaction_id;
        $this->StockTransactionModel->saveField('price', $unit_price);
    }

    /*
     * Fires ManufacturingOrderUpdated Event for each Manufacture Order ID.
     */
    private function updateManufactureOrders(){
        foreach ($this->manufactureOrderIds as $orderId) {
            $this->manufacturingOrderService->updateManufacturingOrderMaterialsAveragePrices($orderId);
        }
    }

    public function updateInboundRequisitionsManufactureOrders(): void
    {
        // Get Inbound Manufacture Orders Requisitions
        $inboundRequisitions = $this->RequisitionModel->find('all', [
                'conditions' => [
                'Requisition.order_type' => $this->RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,
                'Requisition.order_id' => $this->manufactureOrderIds
            ],
            'order' => ['Requisition.date' => 'ASC'],
        ]);

        // Get Earliest Requisition that will need fixing so we can fire AveragePriceChanged event using it's data.
        $earliestRequisition = $inboundRequisitions[0];

        // Map Inbound Requisitions to Manufacture Order IDs
        $manufactureOrderRequisitions = [];
        foreach ($inboundRequisitions as $requisition) {
            $manufactureOrderRequisitions[$requisition['Requisition']['order_id']] = $requisition;
        }

        $this->manufacturingOrders = $this->ManufacturingOrderModel->find('all', ['conditions' => ['ManufacturingOrder.id' => $this->manufactureOrderIds]]);
        // Update Requisition Item for each Manufacture Order Product
        foreach ($this->manufacturingOrders as $manufacturingOrder) {
            $orderId = $manufacturingOrder['ManufacturingOrder']['id'];
            $ref_id = $manufactureOrderRequisitions[$orderId]["RequisitionItem"][0]["id"];
            $requisition_id = $manufactureOrderRequisitions[$orderId]["Requisition"]["id"];
            $unit_price = ManufacturingOrderHelper::calculateMainProductPrice($manufacturingOrder);
            $quantity =  $manufactureOrderRequisitions[$orderId]["RequisitionItem"][0]["quantity"];
            $this->updateRequisitionAndRelatedData($ref_id, $requisition_id, $unit_price, $quantity);
        }

        $cascadeProductId = $earliestRequisition['RequisitionItem'][0]['product_id'];
        // Manual Update that will trigger AveragePriceChanged event in this event for the other product found in the inbound requisition of the manufacture order and the cascade product is not the same as the product that triggered this event.
        if (!empty($earliestRequisition) && $cascadeProductId != $this->averagePriceProductId) {
            $from_date = $earliestRequisition['Requisition']['date'];
            $transaction_after = $this->getTransactionFromRefId($earliestRequisition['RequisitionItem'][0]['id'], $cascadeProductId);
            dispatch_event_action(new AveragePriceChanged([
                'product_id' => $cascadeProductId,
                'old_received_date' => $from_date,
                'start_transaction' => $transaction_after,
                'start_date' => $from_date
            ], "product-$cascadeProductId"));
        }

    }

    /**
     * Initalizes manufacture order IDs from outbound requisitions.
     * @param array $outbound_requisitions
     * @return array
     */
    public function initManufactureOrderIdsFromOutboundManufactureRequisitions($product_id, $from_date): void {
        $outbound_requisitions = $this->RequisitionModel->find('all', [
            'recursive' => -1,
            'group' => 'Requisition.id',
            'joins' => [
                [
                    'table' => 'requisition_items',
                    'alias' => 'RequisitionItem',
                    'type' => 'INNER',
                    'conditions' => [
                        'RequisitionItem.requisition_id = Requisition.id'
                    ]
                ]
            ],
            'conditions' => [
                'RequisitionItem.product_id' => $product_id,
                'Requisition.order_type' => $this->RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
                'Requisition.date >=' => $from_date
            ]
        ]);
        foreach ($outbound_requisitions as $outbound_requisition) {
            $this->manufactureOrderIds[] = $outbound_requisition['Requisition']['order_id'];
        }
    }

    private function updateManufactureOrdersJournals()
    {
        foreach ($this->manufacturingOrders as $order) {
            $this->ManufacturingOrderModel->update_journals($order);
        }
    }

    private function updateManufactureOrderCostSheetData()
    {
        $all_manufacture_requisitions = $this->RequisitionModel->find('all', [
            'conditions' => [
                'Requisition.order_type' => [
                    $this->RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
                    $this->RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
                    $this->RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP
                ],
                'Requisition.order_id' => $this->manufactureOrderIds
            ]
        ]);

        foreach ($all_manufacture_requisitions as $requisition) {
            $this->manufactureOrderCostSheetService->saveMaterialsRequistionCosts($requisition, $requisition['Requisition']['order_type']);
        }
    }

    private function updateManufactureOrdersTotalCosts() {
        foreach ($this->manufactureOrderIds as $orderId) {
            $this->manufacturingOrderService->updateManufactureOrderData($orderId);
        }
    }

    public static function getInstance()
    {
        return new self();
    }

    private function getTransactionFromRefId($ref_id, $product_id) {
        $transaction_after = $this->StockTransactionModel->find('first', [
            'conditions' => [
                'StockTransaction.ref_id' => $ref_id,
                'StockTransaction.product_id' => $product_id,
            ],
            'recursive' => 1,
        ]);
        // to make the data sturcture identical on both ends
        $transaction_after['StockTransaction']['Product'] = $transaction_after['Product'];
        unset($transaction_after['Product']);
        return $transaction_after;
    }
}
