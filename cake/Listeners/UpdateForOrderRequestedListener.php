<?php

	namespace App\Listeners;

	use Izam\Daftra\Queue\Listeners\IEventHandler;

	class UpdateForOrderRequestedListener implements IEventHandler
	{
		private $RequisitionModel;

		public function __construct($RequisitionModel)
		{
			$this->RequisitionModel = $RequisitionModel;
		}

		public function handle($event)
		{
			set_time_limit(3600 * 5);
			$eventData = $event->data;
            $eventData = json_decode(json_encode($eventData), true);
			$this->RequisitionModel->updateForOrder(
				$eventData['order'],
				$eventData['order_type'],
				$eventData['get_pending'],
				$eventData['get_cancelled'],
				$eventData['created_status']
			);
			if (empty($eventData['order'])) {
				return;
			}
			// If the Source didn't have a PurchaseOrder (PurchaseInvoice) it will just return
			if (empty($eventData['order']['PurchaseOrder'])) {
				return;
			}
			// Double checking to make sure the PurchaseOrderID Exists
			if (empty($eventData['order']['PurchaseOrder']['id'])) {
				return;
			}
			$PurchaseOrderId = $eventData['order']['PurchaseOrder']['id'];
			$PurchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
			$updatedPurchaseOrder = $PurchaseOrderModel->getPurchaseOrder($PurchaseOrderId);
			$PurchaseOrderModel->update_journals($updatedPurchaseOrder, false);
		}

		public static function getInstance()
		{
			return new self(GetObjectOrLoadModel('Requisition'));
		}
	}
