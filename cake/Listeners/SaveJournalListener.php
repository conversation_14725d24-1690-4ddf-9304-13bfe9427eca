<?php


namespace App\Listeners;


use Izam\Daftra\Queue\Listeners\IEventHandler;

class SaveJournalListener implements IEventHandler
{

    private $Journal;

    public function __construct($journalModel)
    {
        $this->Journal = $journalModel;
    }

    public function handle($event)
    {
        $eventData = json_decode(json_encode($event->data), true);
        $Model = GetObjectOrLoadModel($eventData['model_name']);
        $Model->data = $eventData['model_data'];
        $this->Journal->autoJournalRelatedAlias = $Model->alias;
        $this->Journal->autoJournalRelatedData[$Model->alias] = array_replace_recursive($this->Journal->autoJournalRelatedData[$Model->alias], $eventData['model_data']);
        $journal=$Model->get_journals($Model->data);
        if($journal!==false)
        {
            $journal_id = $this->Journal->update_auto_journal($journal, ifPluginActive(AccountingPlugin), ifPluginActive(AccountingPlugin));
        }
    }

    public static function getInstance()
    {
        return new self(GetObjectOrLoadModel("Journal"));
    }
}
