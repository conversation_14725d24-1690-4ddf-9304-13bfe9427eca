<?php


namespace App\Listeners;


use Izam\Daftra\Queue\Listeners\IEventHandler;

class UpdateProductAveragePriceListener implements IEventHandler
{

    private $StockTransaction;

    public function __construct($StockTransactionModel)
    {
        $this->StockTransaction = $StockTransactionModel;
    }

    public function handle($event)
    {
        set_time_limit(3600 * 5);
        $eventData = $event->data;
        $oldReceivedDate = null;
        if($eventData->old_received_date) {
            $oldReceivedDate = $eventData->old_received_date;
        }
        $this->StockTransaction->average_on_all(
            $eventData->product_id,
            json_decode(json_encode($eventData->start_transaction), true),
            $oldReceivedDate
        );
    }

    public static function getInstance()
    {
       return new self(GetObjectOrLoadModel('StockTransaction'));
    }
}
