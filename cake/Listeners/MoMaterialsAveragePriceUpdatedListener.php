<?php

namespace App\Listeners;

use App\Helpers\ManufacturingOrderHelper;
use Izam\Daftra\Queue\Listeners\IEventHandler;
use Izam\ManufacturingOrder\Services\ManufacturingOrderService;

class MoMaterialsAveragePriceUpdatedListener implements IEventHandler
{
    /** @var \Requisition */
    private $RequisitionModel;

    /** @var \RequisitionItem */
    private $RequisitionItemModel;

    /** @var \ManufacturingOrder */
    private $ManufacturingOrderModel;

    /** @var ManufacturingOrderService */
    private $manufacturingOrderService;

    public function handle($event)
    {
        /** @var \Requisition */
        $this->RequisitionModel = GetObjectOrLoadModel('Requisition');
        /** @var \RequisitionItem */
        $this->RequisitionItemModel = GetObjectOrLoadModel('RequisitionItem');
        /** @var \ManufacturingOrder */
        $this->ManufacturingOrderModel = GetObjectOrLoadModel('ManufacturingOrder');
        /** @var ManufacturingOrderService */
        $this->manufacturingOrderService = resolve(ManufacturingOrderService::class);

        $date_from = $event->data->date_from;        
        
        if(isset($event->data->use_outbound_materials)){
            $this->updateStartingFromOutboundMaterials($date_from);
        }

        $requisitions = $this->RequisitionModel->find('all', [
            'conditions' => [
                'Requisition.order_type' => $this->RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,
                'Requisition.date >=' => $date_from
            ]
        ]);

        foreach ($requisitions as $requisition) {
            $orderId = $requisition['Requisition']['order_id'];
            $manufacturingOrder = $this->ManufacturingOrderModel->find('first', ['conditions' => ['ManufacturingOrder.id' => $orderId]]);
            if(!$manufacturingOrder){
                continue;
            }
            $orderId = $manufacturingOrder['ManufacturingOrder']['id'];
    
            $this->manufacturingOrderService->updateManufacturingOrderMaterialsAveragePrices($orderId);
    
    
            /** updated data of manufacturing order */
            $manufacturingOrder = $this->ManufacturingOrderModel->find('first', ['conditions' => ['ManufacturingOrder.id' => $orderId]]);

            if ($requisition && !empty($requisition["RequisitionItem"])) {
                $this->updateRequisitionItemAndTotalAndJournals(
                    $requisition["RequisitionItem"][0]["id"],
                    $requisition["Requisition"]["id"],
                    ManufacturingOrderHelper::calculateMainProductPrice($manufacturingOrder),
                    $requisition["RequisitionItem"][0]["quantity"]);
            }
            $this->ManufacturingOrderModel->update_journals($manufacturingOrder);
        }
    }

    private function updateRequisitionItemAndTotalAndJournals($ref_id, $requisition_id, $unit_price, $quantity) {
        $this->RequisitionItemModel->id = $ref_id ;
        $this->RequisitionItemModel->saveField('unit_price' , $unit_price );
        $this->RequisitionItemModel->saveField('subtotal' , $unit_price * abs($quantity));
        $requisition = $this->RequisitionModel->find('first' , ['conditions' => ['Requisition.id' => $requisition_id]]);
        $this->RequisitionModel->update_journals($requisition , NULL);
    }

    private function updateStartingFromOutboundMaterials($date_from){
        $outboundRequisitions = $this->RequisitionModel->find('all', [
            'conditions' => [
                'Requisition.order_type' => $this->RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
                'Requisition.date >=' => $date_from
            ]
        ]);
        foreach ($outboundRequisitions as $outboundRequisition) {
            $this->manufacturingOrderService->updateManufacturingOrderMaterialsAveragePrices($outboundRequisition['Requisition']['order_id']);
        }
    }

    public static function getInstance()
    {
        return new self();
    }
}
