<?php

namespace App\Services;

class DefaultDBSchema
{
    protected $tables = [
        'addresses' => 'CREATE TABLE IF NOT EXISTS addresses (
            id BIGINT(20) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            entity_type varchar(256) NOT NULL,
            entity_id BIGINT NOT NULL,
            address1 varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
            address2 varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
            city varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
            state varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
            postal_code varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
            country_code varchar(3) COLLATE utf8_unicode_ci DEFAULT NULL,
            created DATETIME DEFAULT CURRENT_TIMESTAMP,
            modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );',
        'shipping_options' => 'CREATE TABLE IF NOT EXISTS `shipping_options` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `fees` double(19,10) NOT NULL,
            `display_order` int(11) NOT NULL,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `description` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
            `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
            `modified` datetime ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `deleted_at` datetime NULL,
            PRIMARY KEY(`id`),
            INDEX shipping_options_display_order_index (`display_order`),
            INDEX shipping_options_status_index (`status`),
            INDEX shipping_options_created_index (`created`),
            INDEX shipping_options_deleted_at_index (`deleted_at`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;',
        'entity_attachments' => 'CREATE TABLE IF NOT EXISTS `entity_attachments`
        (
          `id`     INT AUTO_INCREMENT NOT NULL,
          `entity_id` INT NOT NULL,
          `entity_key` VARCHAR(255) NOT NULL,
          `file_id` INT NOT NULL,
          `sort_index` INT NULL DEFAULT 0,
          primary key (id),
          INDEX(`entity_id`),
          INDEX(`entity_key`),
          FOREIGN KEY file_fk (file_id) REFERENCES files (id) ON DELETE CASCADE ON UPDATE CASCADE
        )engine = innodb;'
    ];

    public function has($key)
    {
        return array_key_exists($key, $this->tables);
    }
    public function get($key)
    {
        return $this->tables[$key];
    }
}
