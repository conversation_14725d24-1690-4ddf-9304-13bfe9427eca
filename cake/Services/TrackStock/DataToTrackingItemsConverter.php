<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:26 م
 */

namespace App\Services\TrackStock;


use App\Utils\TrackStockUtil;

class DataToTrackingItemsConverter
{
    /**
     * @param $data
     * @param $type
     * @return TrackingItem[]
     */
    static function convertData($data, $type)
    {
        $mapping = [
            TrackStockUtil::REQUISITION => RequisitionTrackingItemConverter::class,
        ];
        $converter = new $mapping[$type]();
        return $converter->convert($data, GetObjectOrLoadModel('Product'));
    }
}