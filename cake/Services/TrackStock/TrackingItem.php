<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:29 م
 */

namespace App\Services\TrackStock;


use App\Utils\TrackStockUtil;

class TrackingItem
{
//    public $id;
    public $referenceId;
    public $refQuantity = null;
    public $serial;
    public $lot;
    public $expiryDate;
    public $quantity;
    public $productId;
    public $code;
    public $storeId;
    public $trackingType;
    public $trackingBalance = null;
    public $balance = null;
    private $product = null;
    private $trackingRecord = null;
    private $store = null;
    /**
     * TrackingItem constructor.
     * @param $serial
     * @param $lot
     * @param $expiryDate
     * @param $quantity
     * @param $productId
     * @param $storeId
     */
    public function __construct($referenceId, $serial = null, $lot = null, $expiryDate = null, $trackingQuantity, $productId, $storeId = null, $trackingType = null, $refQuantity = null)
    {
        $this->referenceId = $referenceId;
        $this->serial = $serial;
        $this->lot = $lot;
        $this->expiryDate = $expiryDate;
        $this->quantity = $trackingQuantity;
        $this->productId = $productId;
        $this->storeId = $storeId;
        $this->trackingType = $trackingType;
        $this->refQuantity = $refQuantity ?:$this->quantity ;
    }

    /**
     * @return null gets store balance
     */
    public function getBalance()
    {
        if($this->balance === null)
        {
            $trackingRecord = $this->getTrackingRecord();
            $this->balance = $trackingRecord['TrackingNumber']['quantity'];
        }
        return $this->balance;
    }

    public function getTrackingRecord()
    {
        if($this->trackingRecord === null)
        {
            $TrackingNumber = GetObjectOrLoadModel('TrackingNumber');
            if($this->expiryDate === false) {
                $this->expiryDate = null;
            }
            $conditions = [
                'TrackingNumber.serial' => $this->serial,
                'TrackingNumber.lot' => $this->lot,
                'TrackingNumber.expiry_date' => $this->expiryDate,
                'TrackingNumber.product_id' => $this->productId,
                'TrackingNumber.store_id' => $this->storeId,
            ];
            if(empty($this->lot)) {
                unset($conditions['TrackingNumber.lot']);
            }
            $this->trackingRecord = $TrackingNumber->find('first', ['conditions' => $conditions]);

        }
        return $this->trackingRecord;
    }

    public function setProduct($product){
        $this->product = $product;
        $this->code = !empty($product['Product']['product_code']) ? $product['Product']['product_code'] : $product['Product']['id'] ;
        return $this;
    }

    public function setStore($store)
    {
        $this->store = $store;
        return $this;
    }


    public function getProduct()
    {
     if($this->product === null){
            $Product = GetObjectOrLoadModel('Product');
            $this->setProduct($Product->findById($this->productId));
        }
        return $this->product;
    }



    /**
     * @return array|null
     */
    public function exists()
    {
        return $this->getTrackingRecord();
    }

    public function adjustBalance($qty)
    {
        $this->balance += $qty;
    }

    public function getDisplayName()
    {
        if($this->serial)
        {
            return $this->serial;
        }
        if($this->lot && $this->expiryDate)
        {
            return "$this->lot-".format_date($this->expiryDate);
        }
        if($this->lot)
        {
            return $this->lot;
        }
        if($this->expiryDate)
        {
            return format_date($this->expiryDate);
        }
    }

    /**
     *
     */
    public function getTrackingBalance()
    {
        if ($this->trackingBalance === null)
        {
            $TrackingNumber = GetObjectOrLoadModel('TrackingNumber');
            $conditions = [
                'TrackingNumber.serial' => $this->serial,
                'TrackingNumber.lot' => $this->lot,
                'TrackingNumber.expiry_date' => $this->expiryDate,
            ];
            $this->trackingBalance = $TrackingNumber->find('all', ['conditions' => $conditions, 'fields' => 'sum(quantity) as qty'])[0][0]['qty'];
            if($this->trackingBalance === null)
            {
                $this->trackingBalance = 0;
            }
        }

        return $this->trackingBalance;
    }

    public function getIdentifier()
    {
        switch ($this->trackingType)
        {
            case TrackStockUtil::TYPE_SERIAL:
                return $this->code.'-'.$this->serial;
            case TrackStockUtil::TYPE_LOT:
                return $this->code.'-'.$this->lot;
            case TrackStockUtil::TYPE_EXPIRY:
                return $this->code.'-'.$this->expiryDate;
            case TrackStockUtil::TYPE_LOT_EXPIRY:
                return $this->code.'-'.$this->lot . '-'. $this->expiryDate;
            default:
                return $this->code;
        }
    }
    
    public function hasTrackingNumber(){
        switch ($this->trackingType)
        {
            case TrackStockUtil::TYPE_SERIAL:
                return ($this->serial !== '' && $this->serial !== null && $this->serial !== false);
            case TrackStockUtil::TYPE_LOT:
                return ($this->lot !== '' && $this->lot !== null && $this->lot !== false);
            case TrackStockUtil::TYPE_EXPIRY:
                return ($this->expiryDate !== '' && $this->expiryDate !== null && $this->expiryDate !== false);
            case TrackStockUtil::TYPE_LOT_EXPIRY:
                return $this->lot . $this->expiryDate;
            default:
                return false;
        }
    }
    public function getStore()
    {
        if($this->store === null || $this->store['Store']['id'] != $this->storeId) //we check on the store id in case of changing the store incase of a transfer
        {
            $Store = GetObjectOrLoadModel('Store');
            $this->store = $Store->findById($this->storeId);
        }
        return $this->store;
    }
}