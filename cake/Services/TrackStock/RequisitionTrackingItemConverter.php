<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:27 م
 */

namespace App\Services\TrackStock;


use App\Utils\TrackStockUtil;

class RequisitionTrackingItemConverter extends TrackingItemConverterAbstract
{

    public $toStoreId = null;

    public function convert($data, $productModel)
    {
        $this->toStoreId = $data['Requisition']['to_store_id'];
        return parent::convert($data, $productModel);
    }

    protected function getMainRecordAlias()
    {
        return 'Requisition';
    }

    protected function getConvertibleItemsAlias()
    {
        return 'RequisitionItem';
    }
}