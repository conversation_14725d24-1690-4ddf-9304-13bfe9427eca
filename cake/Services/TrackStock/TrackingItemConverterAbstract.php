<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 23/12/19
 * Time: 03:18 م
 */

namespace App\Services\TrackStock;


use App\Utils\TrackStockUtil;

abstract class TrackingItemConverterAbstract
{

    abstract protected function getMainRecordAlias();
    abstract protected function getConvertibleItemsAlias();
    private $Model = null;

    public function __construct()
    {
        $this->Model = GetObjectOrLoadModel($this->getMainRecordAlias());
    }

    private $isEdit = false;
    /**
     * @var array used to save the mapping
     *  between the tracking items and the validated data
     *  so when there is an error in a tracking item
     *  we can trace it back to the validated data
     */
    private $refTrackings = [];
    /**
     * @var array used to save the old tracking data of an edited transaction
     * so we can use this data to calculate the change in quantities of each
     * tracking data in the old transaction
     *
     */
    public $editedTrackings = [];

    private function getItemTrackingData($item, &$serial, &$lot, &$expiryDate)
    {
        if(isset($item['tracking_data']) && !empty($item['tracking_data']))
        {
            $trackingData = json_decode($item['tracking_data'], true);
            $serial = $trackingData['serial'];
            $lot = $trackingData['lot'];
            $expiryDate = $trackingData['expiry_date'];
        }else{
            $serial = $item['serial'];
            $lot = $item['lot'];
            $expiryDate = $item['expiry_date'];
	        if (!empty($item['lot_expiry_date'])) {
	            $lot_expiry_date = explode(';', $item['lot_expiry_date']);
	            $lot = $lot_expiry_date[0];
		        $expiryDate = $lot_expiry_date[1];
	        }
        }
    }

    protected function mapFunctionToTrackingItems($data, $productModel)
    {
        /**
         * @var $trackingItems used to store the new data to be validated
         */
        $Store = GetObjectOrLoadModel('Store');
        $trackingItems = [];
        foreach ($data[$this->getConvertibleItemsAlias()] as $k => $item)
        {
            $storeId = isset($item['store_id']) ? $item['store_id'] : $data[$this->getMainRecordAlias()]['store_id'];
            $this->getItemTrackingData($item, $serial, $lot, $expiryDate);

            if($expiryDate)
            {
                $expiryDate = $productModel->formatDate($expiryDate) ?:$expiryDate;
            }
            $product = $productModel->findById($item['product_id']);
            $store = $Store->findById($storeId);
            $trackingType = $product['Product']['tracking_type'];
            $this->refTrackings[$k] = $trackingType ;
            if(!empty($serial) && is_array($serial))
            {
                foreach ($serial as $seri)
                {
                    $serialQty = 1;
                    $trackingItem = new TrackingItem($k,$seri, $lot, $expiryDate, $serialQty, $item['product_id'], $storeId, $product['Product']['tracking_type'], $item['quantity']);
                    $trackingItem->setStore($store)->setProduct($product);

                    if(isset($trackingItems[$trackingItem->getIdentifier()]))
                    {
                        $trackingItems[$trackingItem->getIdentifier().'1'] = $trackingItem;
                    }else{
                        $trackingItems[$trackingItem->getIdentifier()] = $trackingItem;
                    }
                }
            }else{
                $itemQty = $item['quantity'];
                $trackingItem = new TrackingItem($k, $serial, $lot, $expiryDate, $itemQty, $item['product_id'], $storeId, $product['Product']['tracking_type']);
                $trackingItem->setStore($store)->setProduct($product);
                if(isset($trackingItems[$trackingItem->getIdentifier()]))
                {
                    $trackingItems[$trackingItem->getIdentifier().'1'] = $trackingItem;
                }else{
                    $trackingItems[$trackingItem->getIdentifier()] = $trackingItem;
                }
            }
        }
        return $trackingItems;
    }


    protected function convertDataToTrackingItems($newData, $oldData,$productModel)
    {
        $this->editedTrackings = $this->mapFunctionToTrackingItems($oldData, $productModel);
        return $this->mapFunctionToTrackingItems($newData, $productModel);
    }


    public function convert($data, $productModel)
    {
        $oldRecord = null;
        if(isset($data[$this->getMainRecordAlias()]['id']) && !empty($data[$this->getMainRecordAlias()]['id']))
        {
            $this->isEdit = true;
            //in this section we know that this item is an edited item
            //so we get its previous tracking data
            $oldRecord = $this->Model->findById($data[$this->getMainRecordAlias()]['id']);
        }

        return $this->convertDataToTrackingItems($data,$oldRecord, $productModel);
    }

    public function setErrors($errors)
    {
        $flashErrors = [];
        foreach ($errors as $rule => $refErrors)
        {
            foreach ($refErrors as $refIndex => $message)
            {
                if(!isset($Requisition->validationErrors[$this->getConvertibleItemsAlias()][$refIndex]))
                {
                    $flashErrors[] = $message;
                    $this->Model->validationErrors[$this->getConvertibleItemsAlias()][$refIndex][TrackStockUtil::getTrackingTypeFieldName($this->refTrackings[$refIndex])] = $message;
                }
            }
        }
        if(!IS_REST) {
            CustomValidationFlash($flashErrors);
        }
    }

    public function isEdit()
    {
        return $this->isEdit;
    }

    public static function getItemTrackingDataForSave($item) {
        $Model = GetObjectOrLoadModel('ActionLine');
        if(isset($item['lot_expiry_date']) && !empty($item['lot_expiry_date'])) {
            $temp = explode(';',$item['lot_expiry_date']);
            $item['lot'] = $temp[0];
            $item['expiry_date'] = $temp[1];
        }
        $trackingData = [
            'lot' => isset($item['lot']) ? $item['lot'] : null,
            'expiry_date' => isset($item['expiry_date']) ? $Model->formatDate($item['expiry_date']) : null,
            'serial' => isset($item['serial']) ? $item['serial'] : null
        ];
        return $trackingData;
    }

}
