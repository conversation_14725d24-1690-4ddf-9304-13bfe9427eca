<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:27 م
 */

namespace App\Services\TrackStock;


use App\Utils\TrackStockUtil;

class PurchaseOrderTrackingItemConverter extends TrackingItemConverterAbstract
{



    protected function getMainRecordAlias()
    {
        return 'PurchaseOrder';
    }

    protected function getConvertibleItemsAlias(){
        return 'PurchaseOrderItem';
    }



    public function convert($data, $productModel)
    {
        return parent::convert($data, $productModel); // TODO: Change the autogenerated stub
    }

    protected function mapFunctionToTrackingItems($data, $productModel)
    {
        $trackingItems = parent::mapFunctionToTrackingItems($data, $productModel); // TODO: Change the autogenerated stub
        return $trackingItems;
    }

    protected function convertDataToTrackingItems($newData, $oldData,$productModel)
    {
        if(
            ($newData['PurchaseOrder']['draft'] && $oldData['PurchaseOrder']['draft']) ||
            ($newData['PurchaseOrder']['type'] == \PurchaseOrder::PURCHASE_INVOICE && !$newData['PurchaseOrder']['is_received'] && !$oldData['PurchaseOrder']['is_received'])
        )
        {
            return [];
        }else if(
        ($newData['PurchaseOrder']['draft'] && !$oldData['PurchaseOrder']['draft']) ||
        ($newData['PurchaseOrder']['type'] == \PurchaseOrder::PURCHASE_INVOICE && !$newData['PurchaseOrder']['is_received'] && $oldData['PurchaseOrder']['is_received'])
        ){
            //this will return the return the old tracking items to validate on it
            //in case we converted an invoice to draft
            return parent::convertDataToTrackingItems([], $oldData, $productModel);
        }else if(
        (!$newData['PurchaseOrder']['draft'] && $oldData['PurchaseOrder']['draft']) ||
        ($newData['PurchaseOrder']['type'] == \PurchaseOrder::PURCHASE_INVOICE && $newData['PurchaseOrder']['is_received'] && !$oldData['PurchaseOrder']['is_received'])
        ){
            //this will return empty tracking items for the old data when we convert a purchase order from draft to not draft
            return parent::convertDataToTrackingItems($newData, [], $productModel);
        }else
        {
            return parent::convertDataToTrackingItems($newData, $oldData, $productModel);
        }
    }
}
