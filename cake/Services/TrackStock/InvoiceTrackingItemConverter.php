<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:27 م
 */

namespace App\Services\TrackStock;


use App\Utils\TrackStockUtil;

class InvoiceTrackingItemConverter extends TrackingItemConverterAbstract
{


    private $isNewRecordDraft = false;

    protected function getMainRecordAlias()
    {
        return 'Invoice';
    }

    protected function getConvertibleItemsAlias(){
        return 'InvoiceItem';
    }



    public function convert($data, $productModel)
    {
        return parent::convert($data, $productModel); // TODO: Change the autogenerated stub
    }

    protected function convertDataToTrackingItems($newData, $oldData,$productModel)
    {
        if($newData['Invoice']['draft'] && $oldData['Invoice']['draft'])
        {
            return [];
        }else if($newData['Invoice']['draft'] && !$oldData['Invoice']['draft']){
            //this will return the return the old tracking items to validate on it
            //in case we converted an invoice to draft
            return parent::convertDataToTrackingItems([], $oldData, $productModel);
        }else if(!$newData['Invoice']['draft'] && $oldData['Invoice']['draft']){
            //this will return empty tracking items for the old data when we convert an invoice from draft to not draft
            return parent::convertDataToTrackingItems($newData, [], $productModel);
        }else
        {
            return parent::convertDataToTrackingItems($newData, $oldData, $productModel);
        }
    }

}