<?php


namespace App\Services\Journal;


class JournalAccountsBalanceCalculator extends \Izam\Daftra\Journal\Services\JournalAccountsBalanceCalculator
{


    /**
     * @var \AppModel
     */
    private $journalAccount;

    public function __construct(\PDO $pdo)
    {
        $this->journalAccount = GetObjectOrLoadModel('JournalAccount');
        parent::__construct($pdo);
    }

    private function getDateRangeFilter(JournalAccountBalanceCalculatorRequest $request) {
        $where = [];
        if(!empty($request->getDateFrom())) {
            $where[] = " J.date >".($request->getDateFromEqual() ? '=':'')." {$request->getDateFrom()}";
        }

        if(!empty($request->getDateTo())) {
            $where[] = " J.date <".($request->getDateToEqual() ? '=':'')." '{$request->getDateTo()}'";
        }
        return $where;
    }

    private function getBranchIdFilter(JournalAccountBalanceCalculatorRequest $request) {
        $where = [];
        if($request->getBranchId()) {
            $where[] =  '`J`.`branch_id`= '.$request->getBranchId();
        } else  if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)) {
            if (\getCakeSession(BRANCH_TRANSACTIONS_KEY) && \getCakeSession(BRANCH_TRANSACTIONS_KEY) !== '-1') {
                $where[] =  '`J`.`branch_id`= '.\getCakeSession(BRANCH_TRANSACTIONS_KEY);
            } else if (getCakeSession(\BRANCH_TRANSACTIONS_KEY) === '-1') {
                $where[] = 'J.`branch_id` in ('.implode(',', array_keys(\getStaffBranches())).')';
            }
        }

        return $where;
    }

    private function getFYFilter(JournalAccountBalanceCalculatorRequest $request) {
        $where = [];
        $fy = $request->getFinancialYearId()?:false;
        $Journal = GetObjectOrLoadModel('Journal');
        $where[] = $fy_cond=$Journal->get_financial_conditions($fy,'J');;
        return $where;
    }

    private function filterByStaff(JournalAccountBalanceCalculatorRequest $request) {
        $where = [];
        if($request->getStaffId() !== '' && $request->getStaffId() !== null) {
            $where[] = "J.staff_id = ".$request->getStaffId();
        }
        return $where;
    }

}