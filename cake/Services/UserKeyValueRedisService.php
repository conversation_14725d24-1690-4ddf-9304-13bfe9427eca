<?php

namespace App\Services;

class UserKeyValueRedisService extends UserSessionRedisService
{
    private function getPrefixSite(){
        return 'site:'.getCurrentSite('id');
    }

    /**
     * Store a value in Redis with a given key.
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function set($key, $value)
    {
        $siteKey = $this->getPrefixSite() . ':' . $key;
        return $this->redis->set($siteKey, json_encode($value));
    }

    /**
     * Retrieve a value from Redis by its key.
     *
     * @param string $key
     * @return mixed|null
     */
    public function get($key)
    {
        $siteKey = $this->getPrefixSite() . ':' . $key;
        $value = $this->redis->get($siteKey);
        return $value ? json_decode($value, true) : null;
    }

    /**
     * Check if a key exists in Redis.
     *
     * @param string $key
     * @return bool
     */
    public function exists($key)
    {
        $siteKey = $this->getPrefixSite() . ':' . $key;
        return $this->redis->exists($siteKey);
    }


    /**
     * Delete a key from Redis.
     *
     * @param string $key
     * @return int Number of deleted keys (1 if deleted, 0 if not found)
     */
    public function delete($key)
    {
        $siteKey = $this->getPrefixSite() . ':' . $key;
        return $this->redis->del($siteKey);
    }
}
