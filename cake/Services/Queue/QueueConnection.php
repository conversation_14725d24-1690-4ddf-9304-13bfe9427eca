<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/15/20
 * Time: 3:09 PM
 */

namespace App\Services\Queue;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class QueueConnection
{
    /**
     * @var array
     * to be used config
     */
    static $channelConfig = [];
    static $connectionConfig = [];
    /**
     * @var \PhpAmqpLib\Connection\AMQPStreamConnection
     */
    static $connection;

    /**
     * @var \PhpAmqpLib\Channel\AMQPChannel
     */
    static $channel;
    private function __construct()
    {
    }

    /**
     * @return QueueConnection|AMQPStreamConnection
     */
    public static function getConnection() {
        if(!self::$connection) {
            self::$connection =  new AMQPStreamConnection(
                getenv('RABBITMQ_CONNECTION_HOST'),
                getenv('RABBITMQ_CONNECTION_PORT'),
                getenv('RABBITMQ_CONNECTION_USER'),
                getenv('RABBITMQ_CONNECTION_PASSWORD')
            );
        }
        return self::$connection;
    }

    /**
     * @return array|null|\PhpAmqpLib\Channel\AMQPChannel
     */
    public static function getChannel() {
        if(!self::$channel) {
            $connection = self::getConnection();
            self::$channel = $connection->channel();
            self::$channel->queue_declare(getenv('RABBITMQ_CONNECTION_DEFAULT_QUEUE'), false, true, false, false);
        }
        return self::$channel;
    }

    public function __destruct()
    {
        self::close();
    }

    public static function close() {
        self::getChannel()->close();
        self::getConnection()->close();
    }

}