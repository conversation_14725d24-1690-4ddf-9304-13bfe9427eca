<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/15/20
 * Time: 3:23 PM
 */

namespace App\Services\Queue;


class QueueMessage
{


    public $data;
    public $consumerMethod;
    public $consumerClass;
    public $isConsumerMethodStatic;
    public $apiKey;
    private $siteId;
    private $staffId;

    /**
     * QueueMessage constructor.
     * @param $data
     * @param $consumerMethod
     * @param $consumerClass
     * @param $apiKey
     * @param bool $isConsumerMethodStatic
     */
    public function __construct($data, $consumerMethod, $consumerClass, $apiKey = null, $isConsumerMethodStatic = true)
    {
        $this->data = $data;
        $this->consumerMethod = $consumerMethod;
        $this->consumerClass = $consumerClass;
        $this->siteId = getCurrentSite('id');
        $this->apiKey = $apiKey;
        $this->staffId = getAuthOwner('staff_id');
        $this->isConsumerMethodStatic = $isConsumerMethodStatic;
    }


    public function toJson() {
        return json_encode([
            'data' => $this->data,
            'method' => $this->consumerMethod,
            'class' => $this->consumerClass,
            'siteId' => $this->siteId,
            'apiKey' => $this->apiKey,
            'staffId' => $this->staffId,
            'isConsumerMethodStatic' => $this->isConsumerMethodStatic
        ]);
    }

}