<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/15/20
 * Time: 3:15 PM
 */

namespace App\Services\Queue;


use PhpAmqpLib\Message\AMQPMessage;

class QueueMessageSender
{
    /**
     * @param $message QueueMessage
     */
    public function send($message) {
        $channel = QueueConnection::getChannel();
        $properties = array('content_type' => 'application/json', 'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT);
        $msg = new AMQPMessage($message->toJson(), $properties);
        $channel->basic_publish($msg, '', 'default');
//        QueueConnection::close();
    }
}