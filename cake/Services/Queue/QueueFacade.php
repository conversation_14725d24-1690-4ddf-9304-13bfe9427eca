<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/15/20
 * Time: 2:57 PM
 */

namespace App\Services\Queue;

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class QueueFacade
{
    public static function produceInvoiceCommission($invoiceId) {
        $queueMessageSender = new QueueMessageSender();
        $message = new QueueMessage([$invoiceId], 'calculate', '\App\Services\Commissions\InvoiceCommissionHandler', null, false);
        $queueMessageSender->send($message);
    }

}