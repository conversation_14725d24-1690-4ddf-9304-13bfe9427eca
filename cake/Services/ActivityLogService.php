<?php


namespace App\Services;


use ActivityLog\Strategies\Abstracts\ActivityLogStrategyAbstract;
use App\Entity\EntityStructureGetter;
use Izam\Daftra\ActivityLog\ActivityLogServiceAbstract;
use Izam\Daftra\Common\EntityStructure\Entity;
use Izam\Daftra\Common\EntityStructure\Field;
use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;
use Izam\Forms\Element\ForeignKey;
use Ramsey\Uuid\Uuid;

class ActivityLogService extends ActivityLogServiceAbstract
{
    protected $authenticatedUser;
    /**
     * @var EntityStructureGetter
     */
    private $structureGetter;
    /**
     * @var \AppModel
     */
    private $activityLogModel;
    /**
     * @var \AppModel
     */
    private $activityLogRelationModel;

    public function __construct()
    {
        $this->activityLogModel = GetObjectOrLoadModel('ActivityLog');
        $this->activityLogRelationModel = GetObjectOrLoadModel('ActivityLogRelation');
        $this->structureGetter = getEntityBuilder();
    }

    protected function getEntity($entityKey)
    {
        return $this->structureGetter->buildEntity($entityKey);
    }

    /**
     * @param Field $entityField
     * @param $value
     * @return mixed|void
     */
    protected function formatValue($entityField, $value)
    {
        try {
            switch ($entityField->getType()) {
                case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE:
                case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_PICKER:
                case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER_UTC:
                case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_TIME_PICKER:
                case EntityFieldUtil::ENTITY_FIELD_TYPE_DATE_RANGE_PICKER:
                    if (empty($value) || $value == "0000-00-00") {
                        $value = null;
                    } else {
                        $value = format_date($value);
                    }
                    break;
                case EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN:
                    $allowedValues = json_decode($entityField->getAllowedValues(),true);
                    if(isset($allowedValues[$value])) {
                        $value = $allowedValues[$value];
                    }
                    break;
                case EntityFieldUtil::ENTITY_FIELD_TYPE_FOREIGN_KEY:
                case EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN:
                case EntityFieldUtil::ENTITY_FIELD_TYPE_AUTO_STAFF:
                    if(empty($entityField->getRelation())) {
                       continue;
                    }
                    $targetEntity = $entityField->getRelation()->getTargetEntity();
                    if ($this->onlyCustom) {
                        $targetRecord = getRecordWithEntityStructureForActivityLog($targetEntity->getEntityKey(), $value, 1);
                    } else {
                        $targetRecord = getRecordWithEntityStructure($targetEntity->getEntityKey(), $value, 1);
                    }
                    if($targetRecord && isset($targetRecord->{$targetEntity->getListingField()->getName()})) {
                        $value = $targetRecord->{$targetEntity->getListingField()->getName()};
                    }
                    break;
            }
        }catch (\Exception $exception) {

        }
        return $value;
    }

    protected function getAuthenticatedUser()
    {
        if(!$this->authenticatedUser) {
            $this->authenticatedUser = getAuthOwner();
        }
        if(!$this->authenticatedUser) {
            $this->authenticatedUser = getAuthStaff();
        }
        if(!$this->authenticatedUser) {
            $this->authenticatedUser = getAuthClient();
        }
        return $this->authenticatedUser;
    }

    protected function saveActivityLog($data)
    {
        $this->activityLogModel->create();
        $data['activity_log']['created'] = get_utc_date_time();
        $this->activityLogModel->save($data['activity_log']);
        $activityLogRecordId = $this->activityLogModel->id;
        $relationIds = [];
        foreach ($data['relations'] as $relation) {
            $this->activityLogRelationModel->create();
            $relation['activity_log_id'] = $activityLogRecordId;
            $this->activityLogRelationModel->save($relation);
        }
    }

    public static function getUuid()
    {
        return Uuid::uuid4()->toString();
    }
}
