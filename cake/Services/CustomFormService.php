<?php


namespace App\Services;


use App\Utils\MYSQLCodesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Exception;

class CustomFormService
{

    /**
     * @todo change code to be util
     * @param $form_name
     * @param $items
     * @param $codes
     */
    public static function saveFields($form_name, $items, $customFormRecord, $db_table_name, $is_delete = true)
    {
        $codes = MYSQLCodesUtil::$codes;
        $has_error = false;
        $CustomForm = GetObjectOrLoadModel('CustomForm');
        $CustomFormField = GetObjectOrLoadModel('CustomFormField');
        $counter = 0;
        $id = $customFormRecord['CustomForm']['id'];
        $table_name = $customFormRecord['CustomForm']['table_name'];
        $fields_arr = array();
        foreach ($items as $key => $item)
        {
            $form_fields_data = array();
            $item_id = 0;
            if ( isset($item["item_id"]) && $item["item_id"] > 0) {
                $item_id = $item["item_id"];
                $fields_arr[] = $item_id;
            }
            unset($item["item_id"]);
//                unset($item["belongs_to_tables"]);
            // no-sql element (separator - free static content)
            $add_user_field_flag = true;
            if(isset($item["pure_element"]))
            {
                unset($item["pure_element"]);
                $add_user_field_flag = false;
            }
            else{
                $item["type_options"] = json_encode((isset($item["type_options"])?$item["type_options"]:array()));
                if(isset($item["validation_options"]) && isset($item['validation_options']['min_date'])) {
                    $item['validation_options']['min_date']  = $CustomForm->formatDate($item['validation_options']['min_date']);
                }
                if(isset($item["validation_options"]) && isset($item['validation_options']['max_date'])) {
                    $item['validation_options']['max_date']  = $CustomForm->formatDate($item['validation_options']['max_date']);
                }
                $item["validation_options"] = json_encode((isset($item["validation_options"])?$item["validation_options"]:array()));
                $item["layout_options"] = json_encode((isset($item["layout_options"])?$item["layout_options"]:array()));
            }
            $item["custom_form_id"] = $id;
            $item["display_order"] = $counter +1;
            $add_two_fields = false;
            // special case to add 2 fields for currency field
            if($item["type"] == 150)
            {
                $add_two_fields = true;
            }

            $counter++;
            if ($item_id == 0)
            {

                // add new field
                $CustomFormField->create();
                $form_fields_data["CustomFormField"] = $item;

                $CustomFormField->save($form_fields_data);
                $last_inserted_id = $CustomFormField->getLastInsertId();
                $fields_arr[] = $last_inserted_id;
                //debug($last_inserted_id);

                // and get its id to alter user table add this column
                if($add_user_field_flag == true)
                {
                    if ($CustomFormField->update_user_form_table($id,$last_inserted_id,$codes[$item["type"]], $add_two_fields , $table_name ) === false ){
                        $has_error = "An error occurred, you cannot save the custom fields" ;
                        $CustomFormField->del($last_inserted_id);
                    }
                }

            }
            else{
                // check if $item_id own to user form
                $check_user_field_exist = $CustomFormField->find("all",array(
                    "conditions" => array(
                        "CustomFormField.id" => $item_id,
                        "CustomFormField.custom_form_id" => $id,
                        "CustomFormField.is_deleted" => 0
                    )
                ));

                if (count($check_user_field_exist))
                {
                    $CustomFormField->id = $item_id;
                    $form_fields_data["CustomFormField"] = $item;
                    $CustomFormField->save($form_fields_data);
                }
                else{

                    // add new field
                    $CustomFormField->create();
                    $form_fields_data["CustomFormField"] = $item;
                    $CustomFormField->save($form_fields_data);
                    $last_inserted_id = $CustomFormField->getLastInsertId();
                    $fields_arr[] = $last_inserted_id;
                    //debug($last_inserted_id);

                    // and get its id to alter user table add this collumn
                    if($add_user_field_flag == true)
                    {
                        debug ( 'updating');
                        $CustomFormField->update_user_form_table($id,$last_inserted_id,$codes[$item["type"]], $add_two_fields, $table_name);
                    }
                }
            }
        }
        $table_size = $CustomFormField->get_data_size($db_table_name );
        $num_rows = $CustomFormField->count_custom_table_rows($db_table_name );


        $table_size = $table_size[0][0]["size_kb"];

        $last_submitted = date('y-m-d h:i:s');
        \App::import('Core', 'Sanitize');

        $config = \ConnectionManager::getDataSource('default');
        $CustomForm->update_data(" `data_size` = $table_size
             , `num_rows` = $num_rows , `last_submitted` = '$last_submitted' , `title` = '".\Sanitize::escape($form_name, 'default')."' where `id` = $id ");
        if (count($fields_arr))
        {
            $condition = "and id not in(".implode(',', $fields_arr).")" ;
        }else {
            $condition = "" ;
        }

        try {
            if ($is_delete) {
                static::deleteOldFields($id, $fields_arr, $condition, $table_name);
            }
        } catch (Exception $exception) {
            return  $exception->getMessage();
        }
        $database =\ConnectionManager::getDataSource('default')->config['database'];
        $key = $database.'_default_'.$db_table_name;
        \Cache::delete($key,'_cake_model_');
        $key = 'default' . '_' . $database . '_list'; // php8 fix
        \Cache::delete($key,'_cake_model_');
        return $has_error;
    }

    public static function deleteOldFields($id, $fields_arr, $condition , $table_name = null)
    {
        $CustomFormField = GetObjectOrLoadModel('CustomFormField');
        $CustomFormField->recursive = 1;
        $etaValues = array_filter([\settings::getValue(PluginUtil::ETA_PLUGIN, 'invoice_purchase_order_description'), \settings::getValue(PluginUtil::ETA_PLUGIN, 'invoice_purchase_order_reference'),
            \settings::getValue(PluginUtil::ETA_PLUGIN, 'item_code_tax'),  \settings::getValue(PluginUtil::ETA_PLUGIN, 'item_code_type'),
        ]);
        $fieldsToBeDeleted = $CustomFormField->find('all', ['conditions' => ['CustomFormField.is_deleted' => 0, 'CustomFormField.custom_form_id' => $id, 'NOT' => ['CustomFormField.id' => $fields_arr]]]);
        foreach ($fieldsToBeDeleted as $deletedField) {
                $fieldId = $deletedField["CustomFormField"]["id"];
    //        $CustomFormField->logDelete($deletedField);
            if (in_array($fieldId, $etaValues)) {
                throw new Exception(sprintf(__("You cannot delete this field as it’s already selected in the %s", true), "<a href='/v2/owner/electronic_invoice_settings' target='_blank' style='text-decoration: underline'>".__("Electronic Invoice Setting", true)."</a>"));
            }
        }
        $CustomFormField->delete_removed_fields(" where custom_form_id = $id $condition");
        $CustomFormField->drop_removed_fields_cols_from_user_data_table($id, $table_name);

    }

    public static function createCustomDataTable($table_name)
    {
        $CustomForm = GetObjectOrLoadModel('CustomForm');
        $last_inserted_id = $CustomForm->getLastInsertId();

        \ConnectionManager::getDataSource('default')->cacheSources=false;
        \ConnectionManager::getDataSource('default')->sources(true);
         $CustomForm->create_user_form_table($last_inserted_id , $table_name =="" ?null  : $table_name);
        \ConnectionManager::getDataSource('default')->cacheSources=false;
        \ConnectionManager::getDataSource('default')->sources(true);
        if (file_exists(TMP_PATH . '/cache/models/cake_model_' . json_decode(getCurrentSite('db_config'), true)['database'] . '_default_' . $table_name)) {
                @unlink(TMP_PATH . '/cache/models/cake_model_' . json_decode(getCurrentSite('db_config'), true)['database'] . '_default_' . $table_name);
        }
        $database = \ConnectionManager::getDataSource('default')->config['database'];
        $key = 'default' . '_' . $database . '_list'; // php8 fix
        \Cache::delete($key,'_cake_model_');
    }
}
