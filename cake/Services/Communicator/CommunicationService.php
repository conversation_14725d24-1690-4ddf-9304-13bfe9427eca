<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/16/20
 * Time: 1:01 PM
 */

namespace App\Services\Communicator;

/**
 * @method static mixed calculateInvoiceCommission($invoiceId)
 * @method static mixed deleteCommissions($commissionIds)
 * @method static mixed updatePluginWidgets($plugin_id, $status, $reflected_plugins)
 * @see \App\Services\Communicator\CommunicatorInterface
 */

class CommunicationService
{
    private $communicators = [
        'http' => HttpCommunicator::class,
        'rabbitMq' => RabbitMqCommunicator::class
    ];

    private $default  = 'http';
    /**
     * @var
     */
    private $communicator;

    public function __construct()
    {
        $this->default = getenv('COMMUNICATOR_DEFAULT')?:'http';
        $this->comm($this->default);
    }

    public function comm($communicator) {
        $this->communicator = new $this->communicators[$communicator]();
        return $this;
    }

    public function __call($name, $arguments)
    {
        return $this->communicator->{$name}(...$arguments);
    }
}