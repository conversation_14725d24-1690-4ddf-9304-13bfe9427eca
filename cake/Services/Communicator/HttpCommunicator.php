<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/16/20
 * Time: 1:08 PM
 */

namespace App\Services\Communicator;

\App::import('Component', 'ApiRequestsComponent');

class HttpCommunicator implements CommunicatorInterface
{
    /**
     * @var \ApiRequestsComponent
     */
    private $requestClient;

    public function __construct()
    {
        \App::import('Component', 'ApiRequestsComponent');
        $this->requestClient = new \ApiRequestsComponent();
    }

    public function calculateInvoiceCommission($invoiceId)
    {
        $url = "/v2/api/commissions/calculate";
        $this->requestClient->request($url,false,'POST', ['invoice_id' => $invoiceId]);
    }

    public function deleteCommissions($commissionIds)
    {
        $url = "/v2/api/commissions/delete";
        $result = $this->requestClient->request($url, false, 'POST', ["sales_commissions_ids" => $commissionIds]);
        return $result;
    }

    public function updatePluginWidgets($plugin_id, $status, $reflected_plugins)
    {
        $url = '/v2/api/widget/updatePluginWidgets';
        return $this->requestClient->request($url, false, 'POST', ['plugin_id' => $plugin_id, 'status' => filter_var($status, FILTER_VALIDATE_BOOLEAN), 'reflected_plugins' => $reflected_plugins]);
    }
}