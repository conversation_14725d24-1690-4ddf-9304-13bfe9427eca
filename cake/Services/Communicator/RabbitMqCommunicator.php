<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/16/20
 * Time: 1:07 PM
 */

namespace App\Services\Communicator;

use App\Services\Queue\QueueMessage;
use App\Services\Queue\QueueMessageSender;

class RabbitMqCommunicator implements CommunicatorInterface
{
    public function calculateInvoiceCommission($invoiceId)
    {
        \App\Services\Queue\QueueFacade::produceInvoiceCommission($invoiceId);
    }

    public function deleteCommissions($commissionIds) {
        $queueMessageSender = new QueueMessageSender();
        $message = new QueueMessage([$commissionIds], 'deleteMany', '\App\Services\Commissions\CommissionService', null, false);
        $queueMessageSender->send($message);
    }

    public function updatePluginWidgets($plugin_id, $status, $reflected_plugins)
    {

    }
}