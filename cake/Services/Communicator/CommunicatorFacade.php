<?php
/**
 * Created by PhpStorm.
 * User: bilal-azzam
 * Date: 6/16/20
 * Time: 1:15 PM
 */

namespace App\Services\Communicator;

class CommunicatorFacade
{
    static $instance;

    /**
     * @var CommunicationService
     */
    private $communicationService;

    private function __construct()
    {
        $this->communicationService = new CommunicationService();
    }

    public function comm($comm) {
        $this->communicationService->comm($comm);
        return $this;
    }

    public function calculateInvoiceCommission($invoiceId) {
        $this->communicationService->calculateInvoiceCommission($invoiceId);
    }

    public function deleteCommissions($commissionIds) {
        $this->communicationService->deleteCommissions($commissionIds);
    }

    public function updatePluginWidgets($plugin_id, $status, $reflected_plugins) {
        $this->communicationService->updatePluginWidgets($plugin_id, $status, $reflected_plugins);
    }

    public static function getInstance() {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}