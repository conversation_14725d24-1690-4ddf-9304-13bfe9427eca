<?php

namespace App\Services;

use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\Queue\EventPlatformUtil;
use Izam\Daftra\Common\Queue\EventStatusUtil;
use Izam\Daftra\Common\Queue\ListenerStatusUtil;
use Izam\Daftra\Common\Queue\QueueExitStatusCodeUtil;
use Izam\Daftra\Queue\Models\EventActionServer;
use Izam\Daftra\Queue\Repositories\EventActionServerRepository;
use Izam\Daftra\Queue\Services\QueueServerService;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use Rollbar\Rollbar;

class QueueRunner
{
    /**
     * @var
     * current event Listener used for fatal error shutdown
     */
    private $eventListener;
    /**
     * @var \AppModel
     */
    private $EventAction;
    /**
     * @var \AppModel
     */
    private $EventActionListener;

    private $batch = "";


    public function __construct()
    {
        $this->EventAction = GetObjectOrLoadModel('EventAction');
        $this->EventActionListener = GetObjectOrLoadModel('EventActionListener');
        register_shutdown_function([$this, 'shutdownHandler']);
    }

    /**
     * @param $limitInMb
     * checks if memmory limit in MB is exceded if true it sends error
     */
    public static function checkMemory($limitInMb) {
        if((memory_get_usage(true) / (1024*1024) ) > $limitInMb) {
            $errorData['trace'] = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 50);
            $errorData['memory'] = memory_get_usage(true) / (1024*1024) ;
            $errorData['site'] = getCurrentSite();
            error_reporting(1);
            // initRollbar();
            $result = Rollbar::error('memory limit exceeded', $errorData);
            if(isRunningInQueue()) {
                throw new \Exception('memory limit exceeded');
            }
        }
    }


    private function getEventListeners($siteId, $runTime, $listenerId = null, $eventId = null) {
        $listenerConditions = [
            'EventActionListener.site_id' => $siteId,
            'EventActionListener.status' => ListenerStatusUtil::CREATED,
            'EventActionListener.platform' => EventPlatformUtil::CAKE,
        ];
//        if($runTime != 'all') {
//            $listenerConditions['EventActionListener.runtime_env'] = $runTime;
//        }

        if ($listenerId) {
            $listenerConditions['EventActionListener.id'] =  $listenerId;
        } else {
            $listenerConditions[] = "EventActionListener.site_id NOT IN (SELECT site_id from event_action_listeners where status = '".ListenerStatusUtil::PROCESSING."' group by site_id)";
        }

        if ($eventId) {
            $listenerConditions['EventActionListener.event_action_id'] =  $eventId;
        }

        $eventActionListeners = $this->EventActionListener->find('all', ['limit' => 100,'conditions' => $listenerConditions, 'order' => ['EventActionListener.id asc']]);
        return $eventActionListeners;
    }


    public function shutdownHandler()
    {
        $eventActionListener = $this->eventListener;
        $error = error_get_last();
        if( $error !== NULL) {
            $errno = $error["type"];
            if (
                in_array($errno, array(E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR,) )&&
                $eventActionListener['EventActionListener']['id']
            ) {
                notify_admin_fetal_error($error, "event_queue_error");
                $EventActionListener = GetObjectOrLoadModel('EventActionListener');
                $EventActionListener->id = $eventActionListener['EventActionListener']['id'];
                $eventActionListener['EventActionListener']['tries'] += 1;
                $eventActionListener['EventActionListener']['status'] = ListenerStatusUtil::CREATED;
                if($eventActionListener['EventActionListener']['tries'] > 5) {
                    $eventActionListener['EventActionListener']['status'] = ListenerStatusUtil::KILLED;
                }
                $eventActionListener['EventActionListener']['error'] = json_encode($error);
                $EventActionListener->save($eventActionListener);
            }
        }
    }

    public function runSiteEvents($siteId, $runTime, $listenerId = null, $eventId = null)
    {
        $this->shutdownHandler();
        $i = 0;
        $this->batch = getmypid() . '-' . round(microtime(true));
        while (($eventListeners = $this->getEventListeners($siteId, $runTime, $listenerId, $eventId)) && $i <= 2) {
            $i++;
            foreach ($eventListeners as $eventListener) {
                $this->EventAction->id = $eventListener['EventAction']['id'];
                $eventListener['EventAction']['status'] = EventStatusUtil::PROCESSING;
                $this->EventAction->save($eventListener['EventAction']);
                $this->eventListener = $eventListener;
                try {
                    $this->auth($eventListener['EventAction']['token']);
                } catch (\Exception $exception) {
                    $eventListener['EventActionListener']['error'] = 'not authorized';
                    $eventListener['EventActionListener']['status'] = ListenerStatusUtil::FINISHED;
                    if (!empty($eventListener['EventAction']['id']) && !empty($eventListener['EventActionListener']['event_action_id'])) {
                        $this->EventActionListener->save($eventListener['EventActionListener']);
                    }
                    continue;
                }
                $this->runListenerOld($eventListener, $eventListener);
            }
        }
    }

    private function auth($token) {
        $tokenData = jwt_parse_token($token);
        setRequestCurrentBranch($tokenData->userBranch);
        $sessionData = AuthHelper::generateLoggedUserSessionData($tokenData->userType, $tokenData->userId);
        AuthHelper::setLoggedUserSessionData($sessionData);
    }

    public function runListenerProcess($listener_id, $server_id)
    {
        IzamDatabaseServiceProvider::addConnection( getIzamDatabaseConfig('queue_database'), 'queue_server');
        $this->queueService =  new QueueServerService(new EventActionServerRepository(new EventActionServer()));
        $listener = $this->queueService->getListener($listener_id);
        if(empty($listener)){
            exit;
        }
        if ($listener->status == ListenerStatusUtil::PROCESSING) {
            exit;
        }
        if (empty($listener->action_event)) {
            $this->queueService->killProcess($listener, "There is no event");
            exit;
        }
       // CurrentSiteDatabaseProvider::resetDefaultConnection(Site::find($listener->site_id));
        $Site = GetObjectOrLoadModel('Site');
        $siteId = $listener->site_id;
        \Configure::write('is_queue', true);
        set_time_limit(0);
        $site = $Site->findById($siteId );
        if (empty($site['Site'])) {
            echo "site not found ".$siteId;
            exit (QueueExitStatusCodeUtil::SITE_NOT_FOUND);
        }
        db_reconnect('default', json_decode($site['Site']['db_config'],true));
        setCurrentSite($siteId);
        #########################################################################
        //after we load current_site_Database_connection, we set provider for another service that depend on it
        IzamDatabaseServiceProvider::boot(getPDO(),getPortalConfig());
        IzamDatabaseServiceProvider::addConnection(  getIzamDatabaseConfig('queue_database'), 'local_queue');
        \Izam\Limitation\LimitationServiceProvider::boot(new \App\Helpers\PluginHelper(), ['current_site_connection' => getPDO(), 'portal_connection' => getPDO('portal')]);

        register_app_events();

        $Timezone = GetObjectOrLoadModel('Timezone');
        $zone = $Timezone->field('zone_name', array("Timezone.id" => $site['Site']['timezone']));
        date_default_timezone_set($zone);

        try {
            $this->auth($listener->action_event->token);
        } catch (\Exception $exception) {
            $listener->status = ListenerStatusUtil::FINISHED;
            $listener->error = "Not Authorized :".$exception;
            $listener->finished_at = "{$this->getTime()}";
            $listener->save();
            exit;
        }
        $this->runListener($listener, $server_id);
    }

    private function runListener($listener, $server_id) {
        // value retrieved from env or setting
        $this->queueService->checkProcessExceedTries($listener);
        try {
            set_time_limit(300);
            $this->queueService->executeListener($listener, $server_id);
            $handler = $listener->handler;
            $handlerObj = $handler::getInstance();
            $result = $handlerObj->handle($listener->action_event);
            $this->queueService->finishListener($listener, $result);
//            $this->queueService->finishEvent($listener->action_event);
        } catch (\Throwable $exception) {
            $this->queueService->listenerHasError($listener, $exception);
        }
        exit;
    }

    private function runListenerOld($eventAction, $eventActionListener) {
        try{
            set_time_limit(300);
            $this->EventActionListener->id = $eventActionListener['EventActionListener']['id'];
            if($eventActionListener['EventActionListener']['tries'] > 5) {
                $eventActionListener['EventActionListener']['status'] = ListenerStatusUtil::KILLED;
                $this->EventActionListener->save($eventActionListener);
                return;
            }
            $eventActionListener['EventActionListener']['tries'] += 1;
            $eventActionListener['EventActionListener']['status'] = ListenerStatusUtil::PROCESSING;
            $eventActionListener['EventActionListener']['started_at'] = $this->getTime();
            $eventActionListener['EventActionListener']['batch'] = $this->batch;
            if(empty($eventActionListener['EventActionListener']['first_processing_at'])) {
                $eventActionListener['EventActionListener']['first_processing_at'] = $this->getTime();
            }
            /**
             * handle if to listeners are running in the same time
             * if a second process was created before the first process set the listener status
             * to processing
             */
            $isRunning = $this->EventActionListener->find('first', [
                'conditions' => [
                    'EventActionListener.site_id' => $eventActionListener['EventActionListener']['site_id'],
                    'EventActionListener.status' => ListenerStatusUtil::PROCESSING]
            ]);
            if($isRunning) {
                echo "Another Process Is Running".PHP_EOL;
                exit;
            }
            $result = $this->EventActionListener->save($eventActionListener);
            if(!$result) {
                exit;
            }
            $handler = $eventActionListener['EventActionListener']['handler'];
            $handlerObj = $handler::getInstance();
            $result = $handlerObj->handle($eventAction);
            $eventActionListener['EventActionListener']['result'] = json_encode($result);
            $eventActionListener['EventActionListener']['error'] = null;
            $eventActionListener['EventActionListener']['status'] = ListenerStatusUtil::FINISHED;
        }catch (\Exception $exception) {
            notify_admin_fetal_error(json_encode($exception), "event_queue_exception");
            $eventActionListener['EventActionListener']['status'] = ListenerStatusUtil::CREATED;
            $eventActionListener['EventActionListener']['error'] = $exception;
        } finally {
            $eventActionListener['EventActionListener']['finished_at'] = $this->getTime();
            $this->EventActionListener->save($eventActionListener['EventActionListener']);
            self::checkMemory(10000);
        }
        exit;
    }


    private function getTime() {
        return get_utc_date_time();
    }

    private function handleExceptions($exception) {
        try {
            throw new $exception;
        } catch (\Exception $exception) {
            return json_encode([$exception->getMessage(), $exception->getCode(), $exception->getLine(), $exception->getFile(), $exception->getPrevious(), $exception->getTrace()]);
        }
    }
}
