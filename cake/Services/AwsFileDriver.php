<?php

namespace App\Services;

// use Carbon\Carbon;
use Izam\Daftra\Journal\Services\File\FileDriverInterface;

class AwsFileDriver implements FileDriverInterface
{
    public function getUrl($path)
    {
        $s3FileManager = new \App\Services\S3FileManager();
        // $fileName = $file['name'];
        $filePath = $s3FileManager->getUrl($path);
        return  $filePath;
    }

    public function setConfig($config)
    {
        // TODO: Implement setConfig() method.
    }
}
