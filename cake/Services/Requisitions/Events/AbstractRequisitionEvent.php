<?php
namespace App\Services\Requisitions\Events;


abstract class AbstractRequisitionEvent 
{

    protected $listeners;
    protected $data;


    public function __construct(array $data)
    { 
        $this->data = $data;
    }
    
    public function run():void {
        foreach($this->listeners as $listener){
            $instance = new $listener($this->data, get_called_class());
            $instance->handle();
        }
    }
}
