<?php
namespace App\Services\Requisitions\Listeners;

use App\Models\Requisition;
use Izam\StockRequest\Services\StockRequestService;
use Izam\Daftra\Common\Utils\RequisitionUtil;

class UpdateStockRequestStatusListener extends AbstractRequisitionListener
{
    
    public function handle(){
        if(in_array($this->data['Requisition']['order_type'],
            [ 
                RequisitionUtil::ORDER_TYPE_TRANSFER_REQUISITION,
                RequisitionUtil::TYPE_OUTBOUND,
                RequisitionUtil::TYPE_INBOUND
            ]) && $this->data['Requisition']['order_id'] != 0
        ){
           
            $stockRequestService = resolve(StockRequestService::class);
            $stockRequestService->updateStockRequestSubStatus($this->data['Requisition']['order_id']);
        }elseif(in_array($this->data['Requisition']['order_type'] , [
            RequisitionUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
            RequisitionUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL
        ]))
        {
            $stockRequestService = resolve(StockRequestService::class);
            $stockRequestService->updateStockRequestRelatedToManufacturingOrder($this->data['Requisition']['id'], $this->data['RelatedStockRequests']);
        }
    }
}
