<?php
namespace App\Services\Requisitions\Listeners;

use Izam\Daftra\Common\Utils\ManufacturingOrderCostSheetRecordTypeUtil;
use Izam\Daftra\Common\Utils\RequisitionOrderTypeUtil;
use Izam\ManufacturingOrder\Services\ManufacturingOrderService;
use Izam\ManufacturingOrder\Services\ManufacturingOrderCostSheetService;
use Izam\Daftra\Common\Utils\RequisitionUtil;

class ManufactureOrderRequisitionListener extends AbstractRequisitionListener
{
    
    public function handle(){
        if(in_array($this->data['Requisition']['order_type'],
            [ 
                RequisitionUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
                RequisitionUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
            ])
        ){
            $includeRefunds = $this->data['Requisition']['order_type'] === RequisitionUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND;
            $manufactureOrderService = resolve(ManufacturingOrderService::class);
            $manufactureOrderService->updateManufactureOrderData($this->data['Requisition']['order_id'] , $includeRefunds);

            $manufacturingOrderCostSheetService = resolve(ManufacturingOrderCostSheetService::class);
            $manufacturingOrderCostSheetService->saveMaterialsRequistionCosts($this->data, $this->data['Requisition']['order_type']);
        }elseif($this->data['Requisition']['order_type'] == RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP){
            $manufacturingOrderCostSheetService = resolve(ManufacturingOrderCostSheetService::class);
            $manufacturingOrderCostSheetService->saveMaterialsRequistionCosts($this->data, $this->data['Requisition']['order_type']);
        }
    }
}
