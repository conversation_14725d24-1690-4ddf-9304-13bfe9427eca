<?php

namespace App\Services\JournalAccount;

use ClassRegistry;

/**
 * Helper class for managing journal account selection and formatting.
 * This class encapsulates logic for working with journal accounts and categories,
 * making the controller code cleaner and more maintainable.
 */
class JournalAccountHelper {

    /**
     * Determines the selected account ID from item data.
     * Priority: 'sales_account_id' > 'sales_cost_account_id'
     *
     * @param array $itemData - Item data array (typically from $this->data)
     * @return int|null - Account ID if found, otherwise null
     */
    public static function getSelectedAccountId($itemData) {
    $priorityFields = ['sales_account_id', 'sales_cost_account_id', 'account_id'];

    foreach ($priorityFields as $field) {
        if (!empty($itemData[$field])) {
            return $itemData[$field];
        }
    }

    return null;
}


    /**
     * Retrieves journal category options (used for optgroup labels).
     * The result is a flat key-value pair of category ID => name.
     *
     * @return array - Category options indexed by category ID
     */
    public static function getJournalCategoryOptions() {
        $JournalCat = ClassRegistry::init('JournalCat');

        return \Set::combine(
            $JournalCat->getJournalCats('all', ['order' => ['JournalCat.code' => 'DESC']]),
            '{n}.JournalCat.id',
            '{n}.JournalCat.name'
        );
    }

    /**
     * Retrieves a specific journal account record by its ID.
     *
     * @param int $accountId - The account ID to look up
     * @return array|null - Journal account record if found, null otherwise
     */
    public static function getJournalAccountById($accountId) {
        $JournalAccount = ClassRegistry::init('JournalAccount');

        return $JournalAccount->find('first', [
            'conditions' => ['JournalAccount.id' => $accountId]
        ]);
    }

    /**
     * Builds a human-readable label for the optgroup using the parent category hierarchy.
     * For example: "Revenue > Sales > Online"
     *
     * @param string $parentCatIds - Comma-separated category IDs string (e.g., "3,7,12")
     * @param array $categoryOptions - Array of category ID => name
     * @return string - Constructed label from category hierarchy
     */
    public static function buildOptgroupLabel($parentCatIds, $categoryOptions) {
        $ids = array_reverse(array_filter(explode(',', $parentCatIds), function ($id) {
            return $id != -1;
        }));

        $parts = array();
        foreach ($ids as $id) {
            if (isset($categoryOptions[$id])) {
                $parts[] = $categoryOptions[$id];
            }
        }

        return implode(' > ', $parts);
    }

    /**
     * Formats a journal account into a structured option array suitable for form dropdowns.
     *
     * @param array $account - The journal account record
     * @param string $label - The optgroup label for categorization
     * @return array - Structured option with value, text, optgroup, and details
     */
    public static function formatJournalAccountOption($account, $label) {
        return array(
            'value' => $account['JournalAccount']['id'],
            'text' => $account['JournalAccount']['code'] . ' - ' . __at($account['JournalAccount']['name']),
            'optgroup' => $label,
            'details' => $account['JournalAccount']['parent_cat_ids'],
        );
    }

    /**
     * Builds the optgroups array structure for form select dropdowns.
     * Grouped by 'details' (category path), each group includes its label and options.
     *
     * @param array $accountOption - Formatted journal account option
     * @return array - Optgroup structure with nested options
     */
    public static function buildOptgroups($accountOption) {

        return array(
            array(
                'label' => $accountOption['optgroup'],
                'options' => array(array(
                    'value' => $accountOption['value'],
                    'text' => $accountOption['text']
                ))
            )
        );
    }
}
