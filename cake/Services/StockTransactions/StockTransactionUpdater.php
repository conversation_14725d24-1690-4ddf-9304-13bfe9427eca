<?php

namespace App\Services\StockTransactions;

use App\Services\StockTransactions\StockTransactionUpdaters\InvoiceUpdater;
use App\Services\StockTransactions\StockTransactionUpdaters\PurchaseOrderUpdater;
use App\Services\StockTransactions\StockTransactionUpdaters\RequisitionUpdater;
use StockTransaction;

class StockTransactionUpdater
{
    static function update($source, $data)
    {
        $mapping = [
            StockTransaction::SOURCE_INVOICE => InvoiceUpdater::class,
            StockTransaction::SOURCE_RQ_INVOICE => RequisitionUpdater::class,

            StockTransaction::SOURCE_RR => InvoiceUpdater::class,
            StockTransaction::SOURCE_RQ_INVOICE_REFUND => RequisitionUpdater::class,

            StockTransaction::SOURCE_CN => InvoiceUpdater::class,
            StockTransaction::SOURCE_RQ_INVOICE_CREDIT_NOTE => RequisitionUpdater::class,

            StockTransaction::SOURCE_PO => PurchaseOrderUpdater::class,
            StockTransaction::SOURCE_RQ_PURCHASE_ORDER => RequisitionUpdater::class,

            StockTransaction::SOURCE_PR => PurchaseOrderUpdater::class,
            StockTransaction::SOURCE_RQ_PURCHASE_REFUND => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_PURCHASE_DEBIT_NOTE => RequisitionUpdater::class,
            StockTransaction::SOURCE_PDN => PurchaseOrderUpdater::class,

            StockTransaction::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT => RequisitionUpdater::class,

            StockTransaction::SOURCE_RQ_MANUAL_INBOUND => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_MANUAL_OUTBOUND => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_TRANSFER_REQUISITION => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_STOCKTAKING_OUT => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_STOCKTAKING_IN => RequisitionUpdater::class,

            StockTransaction::SOURCE_RQ_POS_INBOUND => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_POS_OUTBOUND => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => RequisitionUpdater::class,
            StockTransaction::SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND => RequisitionUpdater::class,
        ];
        if(!isset($mapping[$source])){
            return null;
        }
        $Updater = new $mapping[$source]();
        $Updater->update($data);
    }
}
