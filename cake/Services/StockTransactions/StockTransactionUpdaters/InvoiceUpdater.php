<?php

namespace App\Services\StockTransactions\StockTransactionUpdaters;

use App\Helpers\TrackingNumberHelper;
use App\Utils\TrackStockUtil;
use Invoice;
use InvoiceItem;
use Requisition;
use settings;
use StockTransaction;

class InvoiceUpdater extends CommonUpdater
{
    private $InvoiceModel;
    /**
     * @var null
     * used in discount calculation
     */
    private $itemsSubtotal = null;
    public function update($data)
    {
        $this->Model = $this->InvoiceModel = GetObjectOrLoadModel('Invoice');
        $this->setModelData($data);

        if (!$this->shouldContinue()) return;

        $this->generateTransactions();
        $this->deleteRequisitions();
        $this->saveTransactions();
        $this->removeDeletedTransactions();
    }

    private function generateTransactions()
    {
        $receivedDate = $this->fixReceivedDate($this->modelData['Invoice']['date']);
        $sourceType = $this->getSourceType();
        $transactionType = $this->getTransactionType();
        $status = $this->modelData['Invoice']['draft'] ? StockTransaction::STATUS_DRAFT : StockTransaction::STATUS_PROCESSED;
        $date = $this->StockTransactionModel->formatDateTime($this->modelData['Invoice']['date'] . ' ' . date('H:i:s', strtotime($this->modelData['Invoice']['created'])));
        foreach ($this->modelData['InvoiceItem'] as $item) {
            $trackingType = $item['Product']['tracking_type'];
            if (!$this->hasProductId($item)) continue;
            if (!$this->isTracked($item) && !$this->wasTracked($item)) continue;
            $transactionArray = [
                'received_date' => $receivedDate,
                'order_id' => $this->modelData['Invoice']['id'],
                'ref_id' => $item['id'],
                'currency_code' => $this->modelData['Invoice']['currency_code'],
                'product_id' => $item['product_id'],
                'source_type' => $sourceType,
                'transaction_type' => $transactionType,
                'added_by' => $this->modelData['Invoice']['staff_id'],
                'store_id' => $item['store_id'] ?: $this->modelData['Invoice']['store_id'],
                'status' => $status,
                'quantity' => $this->getQuantity($item),
                'total_price' => $this->getTotalPrice($item),
                'purchase_price' => (empty($item['Product']['average_price']) ? 0 : $item['Product']['average_price']),
                'subscription_id' => $this->modelData['Invoice']['subscription_id'],
                'client_id' => $this->modelData['Invoice']['client_id'],
                'date' => $date,
                'unit_name' => $item['unit_name'], 'unit_small_name' => $item['unit_small_name'],
                'unit_factor' => $item['unit_factor'],
                'unit_factor_id' => $item['unit_factor_id'],
                'Product' => $item['Product'],
                'discount' => $this->getDiscount($item),
                'price' => $this->getPrice($item),
                'tracking_data' => json_decode($item['tracking_data'], true),
            ];
			$this->setBranchIdFromSource($transactionArray);
            if ($trackingType === TrackStockUtil::TYPE_SERIAL) {
                $this->addSerialTransactions($transactionArray);
            } else
                $this->addOthersTransaction($transactionArray);
        }
        $this->itemsSubtotal = null;
    }

    private function isRequisitionsEnabled()
    {
        return settings::getValue(InventoryPlugin, 'enable_requisitions');
    }

    private function getSourceType()
    {
        switch ($this->modelData['Invoice']['type']) {
            case INVOICE::Invoice:
                return StockTransaction::SOURCE_INVOICE;
            case INVOICE::Refund_Receipt :
                return StockTransaction::SOURCE_RR;
            case INVOICE::Credit_Note:
                return StockTransaction::SOURCE_CN;
        }
    }

    private function getRequisitionSourceType()
    {
        switch ($this->modelData['Invoice']['type']) {
            case INVOICE::Invoice:
                return Requisition::ORDER_TYPE_INVOICE;
            case INVOICE::Refund_Receipt :
                return Requisition::ORDER_TYPE_INVOICE_REFUND;
            case INVOICE::Credit_Note:
                return Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE;
        }
    }

    private function getTransactionType()
    {
        switch ($this->modelData['Invoice']['type']) {
            case INVOICE::Invoice:
                return StockTransaction::TRANSACTION_OUT;
            case INVOICE::Refund_Receipt :
                return StockTransaction::TRANSACTION_IN;
            case INVOICE::Credit_Note:
                return StockTransaction::TRANSACTION_IN;
        }
    }

	private function isPosEnabled()
    {
        $PosShift = GetObjectOrLoadModel('PosShift');
        $calculatedPerInvoice = $PosShift->checkPosShiftIdIsCalculatedPerInvoice($this->modelData['Invoice']['pos_shift_id']);
        return !$calculatedPerInvoice && !empty($this->modelData['Invoice']['pos_shift_id']);
    }

    private function isInvoice()
    {
        if (!isset($this->modelData['Invoice']['id'])) return false;
        switch ($this->modelData['Invoice']['type']) {
            case Invoice::Invoice:
                return true;
            case Invoice::Refund_Receipt:
                return true;
            case Invoice::Credit_Note:
                return true;
            default:
                return false;
        }
    }

    private function deleteRequisitions()
    {
        $requisitions = $this->RequisitionModel->find('list', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['order_id' => $this->modelData['Invoice']['id'], 'order_type' => $this->getRequisitionSourceType()]]);
        foreach ($requisitions as $k => $r) {
            $this->RequisitionModel->delete_with_related($k);
        }
    }

    protected function getTransactionMetaData($transaction) {
        return ['param3' => $this->modelData['Invoice']['no'], 'param4' => $transaction['Product']['product_code'], 'param5' => $transaction['Product']['name']];
    }

    private function removeDeletedTransactions()
    {
        $removed_transactions = $this->StockTransactionModel->find('all', ['conditions' =>
            [
                'StockTransaction.order_id' => $this->modelData['Invoice']['id'],
                ['NOT' =>
                    ['StockTransaction.id' => $this->savedTransactionIds],
                ],
                'StockTransaction.source_type' => [StockTransaction::SOURCE_INVOICE, StockTransaction::SOURCE_CN, StockTransaction::SOURCE_RR],
            ]]);
        foreach ($removed_transactions as $transaction) {
            //Those 2 values are used for add_to_average function
            $transaction['StockTransaction']['subscription_id'] = $this->modelData['Invoice']['subscription_id'];
            $transaction['StockTransaction']['client_id'] = $this->modelData['Invoice']['client_id'];
            StockTransaction::removeTransaction($transaction['StockTransaction'], ['param3' => $this->modelData['Invoice']['no']]);
        }
    }

    private function removeAllTransactions()
    {
        $this->StockTransactionModel->deleteAll(['StockTransaction.order_id' => $this->modelData['Invoice']['id'], 'StockTransaction.source_type' => [StockTransaction::SOURCE_INVOICE, StockTransaction::SOURCE_CN, StockTransaction::SOURCE_RR]]);
    }

    private function setModelData($data)
    {
        if (is_numeric($data)) {
            $this->modelData = $this->InvoiceModel->getInvoice($data);
        } else {
            $this->modelData = $data;
        }
        // To be used when calculating taxes
        $this->order = $this->modelData;
	    $this->model_name = 'Invoice';
    }

    private function shouldContinue()
    {
        if (!$this->isDataFound($this->modelData)) return false;
        if ($this->isRequisitionsEnabled() && empty($this->modelData['Invoice']['draft'])) {
            $this->removeAllTransactions();
            return false;
        }
        if(!empty($this->modelData['Invoice']['pos_shift_id']) && $this->isPosEnabled()) {
            return false;
        }
        if (!$this->isInvoice()) return false;
        return true;
    }

	/**
	 * @param $item
	 * @param $trackingType
	 * @return float|int
	 */
	private function getDiscount($item)
	{
		// This is ported from PurchaseOrder Discount calculation because it seems that $item['calculated_discount'] is not reliable, and is calculating wrong discount values for repeated items.

		// We had to re-calculate the subtotal to get the original subtotal without any discounts/taxes or anything substracted from it
		$subtotal = 0;
		if($this->itemsSubtotal === null) {
		    $this->itemsSubtotal = $this->calculateItemsSubTotal($this->modelData['InvoiceItem']);
        }
        $subtotal = $this->itemsSubtotal;
        $itemSpecificDiscount = $this->InvoiceModel->calculate_item_discount($item, ((float)$item['unit_price'] * (float)$item['quantity']));
		$item_original_price = (float)$item['unit_price'] * (float)$item['quantity'];
		$itemPriceAfterInvoiceItemDiscount = $item_original_price - $itemSpecificDiscount;
        $summary_discount_over_subtotal = $subtotal != 0 ? ($this->modelData['Invoice']['summary_discount'] / $subtotal) * $itemPriceAfterInvoiceItemDiscount : false;
		$invoice_discount_per_item = null !== $this->modelData['Invoice']['summary_discount'] ? ($summary_discount_over_subtotal) : $this->modelData['Invoice']['discount'] / 100 * $itemPriceAfterInvoiceItemDiscount;

		// We Also have to calculate the invoice item discount because in PurchaseOrders there was no inline discount per item
//		if ($item['discount_type'] == InvoiceItem::DISCOUNT_AMOUNT) {
//			$item_local_discount = $item['discount'] * $item['quantity'];
//		} else if ($item['discount_type'] == InvoiceItem::DISCOUNT_PERCENTAGE) {
//			$item_local_discount = $item['discount'] * $item_original_price / 100;
//		} else {
//			$item_local_discount = 0;
//		}
		$item_discounted_price = $item_original_price - $invoice_discount_per_item - $itemSpecificDiscount;

		$store_id = $item['store_id'] ?: $this->modelData["{$this->model_name}"]['store_id'];
		$product_id = $item['product_id'];
		$tracking_type = $item['Product']['tracking_type'];
		$storeIdAndProductIdExists = isset($this->transactions[$store_id][$product_id]);

		$real_item_discount = $item_original_price != 0 ? ($item_original_price - $item_discounted_price) / $item_original_price * $item['unit_price'] : false;
		/*
		 * To Simplify This, We basically have to calculate the previous row discount and current row discount and add them together.
		 * then we divide them on quantity so we can get the base item discount, It's okay if you get lost just let me know
		 */

        if($tracking_type !== TrackStockUtil::QUANTITY_ONLY && $storeIdAndProductIdExists) {
            $trackingIdentifier = TrackingNumberHelper::getTrackingDataIdentifier($item['tracking_data']);
            if(isset($this->transactions[$store_id][$product_id][$trackingIdentifier])) {
                $repeated_row_discount = $this->transactions[$store_id][$product_id][$trackingIdentifier]['discount'] * $this->transactions[$store_id][$product_id][$trackingIdentifier]['quantity'];
                $current_row_discount = $real_item_discount * $item['quantity'];
                return ($repeated_row_discount + $current_row_discount) / ($item['quantity'] + $this->transactions[$store_id][$product_id][$trackingIdentifier]['quantity']);
            }
        }

		if ($storeIdAndProductIdExists && $tracking_type === TrackStockUtil::QUANTITY_ONLY) {
			$repeated_row_discount = $this->transactions[$store_id][$product_id]['discount'] * $this->transactions[$store_id][$product_id]['quantity'];
			$current_row_discount = $real_item_discount * $item['quantity'];
            $total_discount = $this->transactions[$store_id][$product_id]['quantity'];
            if ($item['quantity']  > 0) {
                $total_discount = ($repeated_row_discount + $current_row_discount) / ($item['quantity'] + $this->transactions[$store_id][$product_id]['quantity']);
            }
            return $total_discount;
		}
		// Different Item then return the normal calculated discount without adding anything.
		return $real_item_discount;
	}

}
