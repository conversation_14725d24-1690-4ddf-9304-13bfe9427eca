<?php

namespace App\Services\StockTransactions\StockTransactionUpdaters;


use App\Helpers\TrackingNumberHelper;
use Requisition;
use PurchaseOrder;
use StockTransaction;
use App\Utils\TrackStockUtil;

class PurchaseOrderUpdater extends CommonUpdater
{
    private $PurchaseOrderModel;

    /**
     * @var null
     * used in discount calculation
     */
    private $itemsSubtotal = null;


    public function update($data)
    {
        $this->Model = $this->PurchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
        $this->setModelData($data);

        if (!$this->shouldContinue()) return;

        $this->generateTransactions();
        $this->deleteRequisitions();
        $this->saveTransactions();
        $this->removeDeletedTransactions();
        $this->updatePurchaseOrderStatus();
    }

    private function updatePurchaseOrderStatus() {
        $this->StockTransactionModel->updatePurchaseOrderStatus($this->modelData['PurchaseOrder']['id']);
    }

    private function generateTransactions()
    {
        foreach ($this->modelData['PurchaseOrderItem'] as $item) {
            $trackingType = $item['Product']['tracking_type'];
            if (!$this->hasProductId($item)) continue;
            if (!$this->isTracked($item) && !$this->wasTracked($item)) continue;
            $transactionArray = [
                'received_date' => $this->fixReceivedDate($this->modelData['PurchaseOrder']['received_date']),
                'order_id' => $this->modelData['PurchaseOrder']['id'],
                'ref_id' => $item['id'],
                'currency_code' => $this->modelData['PurchaseOrder']['currency_code'],
                'product_id' => $item['product_id'],
                'source_type' => $this->getSourceType(),
                'transaction_type' => $this->getTransactionType(),
                'added_by' => $this->modelData['PurchaseOrder']['staff_id'],
                'status' => $this->getStockTransactionStatus(),
                'po_status' => $this->getPurchaseOrderStatus(),
                'quantity' => $this->getQuantity($item),
                'total_price' => $this->getTotalPrice($item),
                'purchase_price' => (empty($item['Product']['average_price']) ? 0 : $item['Product']['average_price']),
                'date' => $this->StockTransactionModel->formatDateTime($this->modelData['PurchaseOrder']['date'] . ' ' . date('H:i:s', strtotime($this->modelData['PurchaseOrder']['created']))),
                'unit_name' => $item['unit_name'], 'unit_small_name' => $item['unit_small_name'],
                'unit_factor' => $item['unit_factor'],
                'unit_factor_id' => $item['unit_factor_id'],
                'store_id' => !empty($item['store_id']) ? $item['store_id'] : $this->modelData['PurchaseOrder']['store_id'],
                'Product' => $item['Product'],
                'discount' => $this->calculateDiscount($item),
                'price' => $this->getPrice($item),
                'tracking_data' => json_decode($item['tracking_data'], true),
            ];
	        $this->setBranchIdFromSource($transactionArray);
            if ($trackingType === TrackStockUtil::TYPE_SERIAL) {
                $this->addSerialTransactions($transactionArray);
            } else
                $this->addOthersTransaction($transactionArray);
        }
        $this->itemsSubtotal = null;
    }

    private function getStockTransactionStatus(){
        switch ($this->modelData['PurchaseOrder']['type']) {
            case PurchaseOrder::PURCHASE_INVOICE:
                // Draft
                if(!empty($this->modelData['PurchaseOrder']['draft']))
                    return StockTransaction::STATUS_DRAFT;
                // Not Received but not Draft
                else if(empty($this->modelData['PurchaseOrder']['is_received']))
                    return StockTransaction::STATUS_PENDING;
                else
                // Received and Not Draft
                    return StockTransaction::STATUS_PROCESSED;
            case PurchaseOrder::Purchase_Refund:
            case PurchaseOrder::DEBIT_NOTE:
                if(!empty($this->modelData['PurchaseOrder']['draft'])){

                    return StockTransaction::STATUS_DRAFT;
                }elseif(empty($this->modelData['PurchaseOrder']['is_received'])){
                    return StockTransaction::STATUS_PENDING;
                }
                return StockTransaction::STATUS_PROCESSED;
                
        }
    }

    private function getPurchaseOrderStatus(){
        // Yes this is different from the above :D
        switch ($this->modelData['PurchaseOrder']['type']) {
            case PurchaseOrder::PURCHASE_INVOICE:
                return $this->modelData['PurchaseOrder']['is_received'];
            case PurchaseOrder::Purchase_Refund:
            case PurchaseOrder::DEBIT_NOTE:
                return 1;
        }
    }

    private function calculateDiscount($item)
    {
        if ($this->PurchaseOrderModel->is_discount_received) {
            return 0;
        }

        if ($this->itemsSubtotal === null) {
            $this->itemsSubtotal = $this->calculateItemsSubTotal($this->modelData['PurchaseOrderItem'] );
        }
        $subtotal = $this->itemsSubtotal;
        $item_original_price = (float)$item['unit_price'] * (float)$item['quantity'];
        $itemSpecificDiscount = $this->PurchaseOrderModel->calculate_item_discount($item, ((float)$item['unit_price'] * (float)$item['quantity']));
        $itemPriceAfterInvoiceItemDiscount = $item_original_price - $itemSpecificDiscount;

        $invoice_discount_per_item =
        (null !== $this->modelData['PurchaseOrder']['summary_discount'])
        ? (!$subtotal ? 0 : ($this->modelData['PurchaseOrder']['summary_discount'] / $subtotal) * $itemPriceAfterInvoiceItemDiscount)
        : (!$itemPriceAfterInvoiceItemDiscount ? 0 : $this->modelData['PurchaseOrder']['discount'] / 100 * $itemPriceAfterInvoiceItemDiscount);
        
        $item_discounted_price = $item_original_price - $invoice_discount_per_item - $itemSpecificDiscount;

	    $store_id = $item['store_id'] ?: $this->modelData["{$this->model_name}"]['store_id'];
	    $product_id = $item['product_id'];
	    $tracking_type = $item['Product']['tracking_type'];
	    $storeIdAndProductIdExists = isset($this->transactions[$store_id][$product_id]);

        $real_item_discount = $item_original_price != 0 ? ($item_original_price - $item_discounted_price) / $item_original_price * $item['unit_price'] : false;
	    /*
	     * To Simplify This, We basically have to calculate the previous row discount and current row discount and add them together.
	     * then we divide them on quantity so we can get the base item discount, It's okay if you get lost just let me know
	     */
	    if ($storeIdAndProductIdExists && $tracking_type === TrackStockUtil::QUANTITY_ONLY) {
		    $repeated_row_discount = $this->transactions[$store_id][$product_id]['discount'] * $this->transactions[$store_id][$product_id]['quantity'];
			$current_row_discount = $real_item_discount * $item['quantity'];
            $total_quantity = ($item['quantity'] + $this->transactions[$store_id][$product_id]['quantity']);
            return $this->transactions[$store_id][$product_id]['quantity'] != 0  && $total_quantity !=0 ? (($repeated_row_discount + $current_row_discount) / $total_quantity) : false;
	    }
		// This incase the product is not serial but tracking, we access $this->>transactions in a different way using [$trackingIdentifier]] key
	    if ($storeIdAndProductIdExists && $tracking_type !== TrackStockUtil::TYPE_SERIAL) {
		    $trackingIdentifier = TrackingNumberHelper::getTrackingDataIdentifier($item['tracking_data']);
            if (!isset($this->transactions[$store_id][$product_id][$trackingIdentifier])){
                return $real_item_discount;
            }
		    $repeated_row_discount = $this->transactions[$store_id][$product_id][$trackingIdentifier]['discount'] * $this->transactions[$store_id][$product_id][$trackingIdentifier]['quantity'];
		    $current_row_discount = $real_item_discount * $item['quantity'];
		    $total_quantity = ($item['quantity'] + $this->transactions[$store_id][$product_id][$trackingIdentifier]['quantity']);
		    return $this->transactions[$store_id][$product_id][$trackingIdentifier]['quantity'] != 0  && $total_quantity !=0 ? (($repeated_row_discount + $current_row_discount) / $total_quantity) : false;
	    }
		// Different Item then return the normal calculated discount without adding anything.
        return $real_item_discount;
    }

    private function getSourceType()
    {
        switch ($this->modelData['PurchaseOrder']['type']) {
            case PurchaseOrder::PURCHASE_INVOICE:
                return StockTransaction::SOURCE_PO;
            case PurchaseOrder::Purchase_Refund:
                return StockTransaction::SOURCE_PR;
            case PurchaseOrder::DEBIT_NOTE:
                return StockTransaction::SOURCE_PDN;    
        }
    }

    private function getRequisitionSourceType()
    {
        switch ($this->modelData['PurchaseOrder']['type']) {
            case PurchaseOrder::PURCHASE_INVOICE:
                return Requisition::ORDER_TYPE_PURCHASE_ORDER;
            case PurchaseOrder::Purchase_Refund :
                return Requisition::ORDER_TYPE_PURCHASE_REFUND;
            case PurchaseOrder::DEBIT_NOTE:
                return Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE;
        }
    }

    private function getTransactionType()
    {
        switch ($this->modelData['PurchaseOrder']['type']) {
            case PurchaseOrder::PURCHASE_INVOICE :
                return StockTransaction::TRANSACTION_IN;
            case PurchaseOrder::Purchase_Refund:
                return StockTransaction::TRANSACTION_OUT;
            case PurchaseOrder::DEBIT_NOTE:
                return StockTransaction::TRANSACTION_OUT;   
        }
    }

    private function deleteRequisitions()
    {
        $requisitions = $this->RequisitionModel->find('list', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['order_id' => $this->modelData['PurchaseOrder']['id'], 'order_type' => $this->getRequisitionSourceType()]]);
        foreach ($requisitions as $k => $r) {
            $this->RequisitionModel->delete_with_related($k);
        }
    }

    protected function getTransactionMetaData($transaction) {
        return ['param3' => $this->modelData['PurchaseOrder']['no'], 'param4' => $transaction['Product']['product_code'], 'param5' => $transaction['Product']['name']];
    }

    private function removeDeletedTransactions()
    {
        $removed_transactions = $this->StockTransactionModel->find('all', ['conditions' =>
            [
                'StockTransaction.order_id' => $this->modelData['PurchaseOrder']['id'],
                ['NOT' =>
                    ['StockTransaction.id' => $this->savedTransactionIds],
                ],
                'StockTransaction.source_type' => [StockTransaction::SOURCE_PO, StockTransaction::SOURCE_PR, StockTransaction::SOURCE_PDN],
            ]]);
        foreach ($removed_transactions as $transaction) {
            StockTransaction::removeTransaction($transaction['StockTransaction'], ['param3' => $this->modelData['PurchaseOrder']['no']]);
        }
    }

    private function setModelData($data)
    {
        if (is_numeric($data)) {
            $this->modelData = $this->PurchaseOrderModel->getPurchaseOrder($data, [], false, true, false);
        } else {
            $this->modelData = $data;
        }
        // To be used when calculating taxes
        $this->order = $this->modelData;
	    $this->model_name = 'PurchaseOrder';
    }

    private function shouldContinue()
    {
        if (!$this->isDataFound($this->modelData)) {
            return false;
        }
        return true;
    }
}
