<?php

namespace App\Services\StockTransactions\StockTransactionUpdaters;

\App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));

use App\Helpers\Common\PurchaseOrderAndInvoiceHelper;
use App\Helpers\TaxesHelper;
use App\Utils\TrackStockUtil;
use CurrencyConverter;
use Invoice;
use Izam\ManufacturingOrder\Events\MoMaterialsAveragePriceUpdated;
use Requisition;
use Set;
use StockTransaction;

class RequisitionUpdater extends CommonUpdater
{
	private $oType;
	private $InvoiceData;
	private $PurchaseOrderData;
	private $source_model_name;
    public function update($data)
    {

        $this->setModelData($data);
        if (!$this->isDataFound($data)) {
	        return;
        }
		if ($this->isRequisitionRejected()) {
			$this->removeRejectedRequisitionTransactions();
			return;
		}

//        $this->updateStockTransactionProductPrice($data);
        // Added to handel large number of tracking numbers . 
        ini_set('memory_limit','2G');
        ini_set('max_execution_time', 3600);
        $this->initOrderItems();
        $this->generateTransactions();
        $this->saveTransactions();
        $this->removeOldTransactions();
//        $this->extraUpdateLogic();
    }

    public function extraUpdateLogic(){        
        if($this->modelData['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
            dispatch_event_action(new MoMaterialsAveragePriceUpdated([
                'use_outbound_materials' => true,
                'date_from' => $this->modelData['Requisition']['date'],
            ]));                
        }
    }

    private function calculateTransactionStatus()
    {
	    if ($this->modelData['Requisition']['status'] == Requisition::STATUS_DRAFT) return Requisition::STATUS_DRAFT;
	    if ($this->modelData['Requisition']['status'] == Requisition::STATUS_PENDING) return StockTransaction::STATUS_PENDING;
        return StockTransaction::STATUS_PROCESSED;
    }

    private function calculateDiscount($item)
    {
        switch ($this->oType['source_model']) {
            case 'PurchaseOrder':
                $PoModel = GetObjectOrLoadModel('PurchaseOrder');
                if ($PoModel->is_discount_received) {
                    return 0;
                }
                if(!isset($this->PurchaseOrderData)){
                    $this->PurchaseOrderData = $PoModel->getPurchaseOrder($this->modelData['Requisition']['order_id']);
                }
                return $this->calculateInvoicePurchaseDiscount($this->PurchaseOrderData['PurchaseOrderItem'], $item);
            case 'Invoice':
                $this->setInvoiceData();
                return $this->calculateInvoicePurchaseDiscount($this->InvoiceData['InvoiceItem'], $item);

            case 'PosShift':
                if(ifPluginActive(PosPlugin)){
                    $productTotalPriceAndDiscount = $this->PosShiftModel->getPosShiftProductTotalPriceAndDiscount($this->modelData['Requisition']['order_id'], $item['product_id']);
                    $transactions[$item['product_id']]['total_price'] = $productTotalPriceAndDiscount['summary_subtotal'];
                    $transactions[$item['product_id']]['discount'] = $productTotalPriceAndDiscount['discount'] / $this->transactions[$item['product_id']]['quantity'];
                    return $transactions[$item['product_id']]['discount'];
                }
        }
        return 0;
    }

    private function calculateInvoicePurchaseDiscount($invoice_items, $item)
    {
        if ($item['quantity'] == 0) return false;
        $total_product_discount = 0;
        $productQty = 0;
        foreach($invoice_items as $invoice_item){
            if ($invoice_item['product_id'] == $item['product_id']){
                $productQty += $invoice_item['quantity'];
                $total_product_discount += $invoice_item['calculated_discount'];
            }
        }
        $currency_rate = 1;
        $sourceModelData = $this->source_model_name . 'Data';
        // The Source Might be in Different Currency so we have to make sure that we also multiply the discount with the currency_rate here as well
        if ($this->$sourceModelData[$this->source_model_name]['currency_code'] != getCurrentSite('currency_code')) {
            $currency_rate = CurrencyConverter::index($this->$sourceModelData[$this->source_model_name]['currency_code'], getCurrentSite('currency_code'), date('Y-m-d', strtotime($this->fixReceivedDate($this->$sourceModelData[$this->source_model_name]['date']))));
        }
        $total_product_discount *= $currency_rate;
        $discountPerItem = 0;
        if($productQty) {
            $discountPerItem = $total_product_discount / $productQty;
        }
        return $discountPerItem;
    }

    protected function getTransactionMetaData($transaction) {
        return ['param3' => $this->modelData['PurchaseOrder']['no'], 'param4' => $transaction['Product']['product_code'], 'param5' => $transaction['Product']['name']];
    }

    private function removeOldTransactions()
    {
        $removed_transactions = $this->StockTransactionModel->find('all',
            ['conditions' =>
                [
                	'StockTransaction.order_id' => $this->modelData['Requisition']['id'],
	                'NOT' => ['StockTransaction.id' => $this->savedTransactionIds],
	                'StockTransaction.source_type' => StockTransaction::getSourceRequisition()
                ]]);
        if (!empty($removed_transactions))
            foreach ($removed_transactions as $transaction) {
                StockTransaction::removeTransaction($transaction['StockTransaction'], ['param3' => null]);
            }
    }

    private function initOrderItems()
    {
        $orderTypes = [
            Requisition::ORDER_TYPE_INVOICE,
            Requisition::ORDER_TYPE_PURCHASE_ORDER,
            Requisition::ORDER_TYPE_PURCHASE_REFUND,
            Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE,
            Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE,
            Requisition::ORDER_TYPE_INVOICE_REFUND,
        ];
        $this->orderType = $this->modelData['Requisition']['order_type'];
        if (in_array($this->orderType,$orderTypes)) {
            $this->oType = Requisition::$requisition_order_types[$this->modelData['Requisition']['order_type']];
            $this->order = $this->RequisitionModel->getOrder($this->modelData['Requisition']['order_id'], $this->modelData['Requisition']['order_type']);
            foreach ($this->order[$this->oType['item_source_model']] as $v) {
                if(!isset($this->orderItems[$v['product_id']])) {
                    $this->orderItems[$v['product_id']] = 0;
                }
                $this->orderItems[$v['product_id']] += ($v['quantity'] * $v['unit_price']);
            }
        }
	    // To be used when calculating taxes
	    $this->order = $this->modelData;
	    $this->model_name = "Requisition";
	    $this->source_model_name = $this->oType['source_model'];
    }

    private function generateTransactions()
    {   

        $requisitionItemsPurchasePrices = $this->StockTransactionModel->find('list', [
            'fields' => ['StockTransaction.ref_id', 'StockTransaction.purchase_price'],
            'conditions' => [
                'StockTransaction.ref_id' => Set::extract('{n}.id', $this->modelData['RequisitionItem']),
                'StockTransaction.order_id' => $this->modelData['Requisition']['id'],
                'StockTransaction.source_type' => StockTransaction::getSourceRequisition(),
            ],
            'recursive' => -1
        ]);

        

        foreach ($this->modelData['RequisitionItem'] as $item) {
            $trackingType = $item['Product']['tracking_type'];
            if(empty($item['Product'])) continue;
            if (!$this->isTracked($item) && !$this->wasTracked($item)) continue;
            $transactionArray = [
                'received_date' => $this->fixReceivedDate($this->modelData['Requisition']['date']),
                'order_no' => $this->modelData['Requisition']['number'],
                'order_id' => $this->modelData['Requisition']['id'],
                'ref_id' => $item['id'],
                'currency_code' => getCurrentSite('currency_code'), //as per desoky instructions every requisition should have the same currency as the site
                'product_id' => $item['product_id'],
                'source_type' => StockTransaction::getStockTransactionType($this->modelData['Requisition']['order_type']),
                'transaction_type' => (($this->modelData['Requisition']['type'] == Requisition::TYPE_OUTBOUND) ? StockTransaction::TRANSACTION_OUT : StockTransaction::TRANSACTION_IN),
                'added_by' => ($this->modelData['Requisition']['staff_id'] ?: getAuthOwner('staff_id')),
                'status' => $this->calculateTransactionStatus(),
                'quantity' => $this->getQuantity($item),
                'total_price' => $this->getTotalPrice($item, $this->modelData['Requisition']['order_type']),
                'purchase_price' => isset($requisitionItemsPurchasePrices[$item['id']]) ? $requisitionItemsPurchasePrices[$item['id']] : (empty($item['Product']['average_price']) ? 0 : $item['Product']['average_price']),
                'date' => $this->StockTransactionModel->formatDateTime(date('Y-m-d H:i:s')),
                'unit_name' => $item['unit_name'],
                'unit_small_name' => $item['unit_small_name'],
                'unit_factor' => $item['unit_factor'],
                'unit_factor_id' => $item['unit_factor_id'],
                'store_id' => $this->modelData['Requisition']['store_id'],
                'discount' => $this->calculateDiscount($item),
                'price' => $this->getPrice($item, $this->modelData['Requisition']['order_type']),
                'Product' => $item['Product'],
                'tracking_data' => json_decode($item['tracking_data'], true)
            ];
	        $this->setBranchIdFromSource($transactionArray);
            if ($trackingType === TrackStockUtil::TYPE_SERIAL) {
                $this->addSerialTransactions($transactionArray);
            } else {
	            $this->addOthersTransaction($transactionArray);
            }
        }

    }
    /**
     * @param $item
     * @param InvoiceUpdater $instance
     * @return float|int|mixed
     */
    protected function getPrice($item,  $type = Requisition::ORDER_TYPE_INVOICE)
    {
        $total_price = $total_item_tax = 0;
        if ($this->isRequisitionSourcePos()){
            $invoice_type = $this->getInvoiceTypeFromRequisitionOrderType();
            if(ifPluginActive(PosPlugin)) {
                $data = $this->PosShiftModel->getPosShiftProductTotalPriceAndDiscount($this->modelData['Requisition']['order_id'], $item['product_id'], $invoice_type);
                $total_price = $data['summary_subtotal'];
                $total_item_tax = $data['tax1_total'] + $data['tax2_total'];
            }
        } else {
            $total_price = $this->getTotalPrice($item, $type);
            $total_item_tax = $this->calculateItemIncludedTax($item, $this->model_name);
        }
        $total_quantity = $this->getQuantity($item);
        if ($total_quantity == 0) return false;
        return ($total_price - $total_item_tax) / $total_quantity;
    }


    private function setModelData($data)
    {
        if (is_numeric($data)) {
            $this->RequisitionModel = GetObjectOrLoadModel('Requisition');
            $this->modelData = $this->RequisitionModel->getRequisition($data);
        } else {
            $this->modelData = $data;
        }
    }

    private function isRequisitionRejected() {
	    return $this->modelData['Requisition']['status'] == Requisition::STATUS_CANCELLED;
    }

	private function removeRejectedRequisitionTransactions() {
		$rejected_requisition_transaction_ids = Set::extract('{n}.StockTransaction.id', $this->StockTransactionModel->find('list', ['fields', 'id', 'recursive' => -1, 'conditions' => [
			'StockTransaction.order_id' => $this->modelData['Requisition']['id'],
			'StockTransaction.source_type' => StockTransaction::getSourceRequisition(),
		]]));
		foreach ($rejected_requisition_transaction_ids as $transaction) {
			StockTransaction::removeTransaction($transaction);
		}
	}
    /*
     * Return Total Price Of the RequisitionItem of Product ID
     * This is used to get the Total Price Of a Single Transaction and
     * Then will Be Divided later by The Quantity To Get The average Price
     * */
    protected function getTotalPrice($item, $type = Requisition::ORDER_TYPE_INVOICE){
	    if ($this->isRequisitionSourcePos()){
		    return $this->getPosItemTotalPrice($item);
	    }
        if ($this->isRequisitionSourceInvoice()){
	        return $this->getInvoiceItemTotalPrice($item, $type);
        }
	    if ($this->isRequisitionSourcePurchaseOrder()){
		    return $this->getPurchaseOrderItemTotalPrice($item, $type);
	    }
		// If Source Is Actually Requisition
	    return parent::getTotalPrice($item, $type);
    }

    private function isRequisitionSourceInvoice(){
    	return $this->oType['source_model'] === 'Invoice';
    }

	private function isRequisitionSourcePos(){
    	if (!in_array($this->orderType, [Requisition::ORDER_TYPE_POS_INBOUND, Requisition::ORDER_TYPE_POS_OUTBOUND])) return false;
		return true;
	}

	private function isRequisitionSourcePurchaseOrder(){
		return $this->oType['source_model'] === 'PurchaseOrder';
	}

	protected function calculateItemIncludedTax($item, $modelName){
    	if ($this->isRequisitionSourceInvoice()){
    		$total_taxes = 0;
		    $this->setInvoiceData();
            $totalOrderQty = 0;
            foreach ($this->InvoiceData['InvoiceItem'] as $invoiceItem){
		    	$invoice_id = $invoiceItem['invoice_id'];
		    	if ($item['product_id'] == $invoiceItem['product_id']){
                    $totalOrderQty += $invoiceItem['quantity'];
                    if ($invoiceItem['summary_tax1']){
		    			$total_taxes += $this->isInvoiceTaxInclusive($invoiceItem['tax1'], $invoice_id) ? $invoiceItem['summary_tax1'] : 0;
				    }
		    		if ($invoiceItem['summary_tax2']){
					    $total_taxes += $this->isInvoiceTaxInclusive($invoiceItem['tax2'], $invoice_id) ? $invoiceItem['summary_tax2'] : 0;
				    }
			    }
		    }
            return TaxesHelper::calculatePartialTax($total_taxes, $totalOrderQty, $item['quantity']);
	    }
		if ($this->isRequisitionSourcePurchaseOrder()){ // PurchaseOrder
			$total_taxes = 0;
			$this->setPurchaseOrderData();
            $totalOrderQty = 0;
			foreach ($this->PurchaseOrderData['PurchaseOrderItem'] as $purchase_item){
				$purchase_order_id = $purchase_item['purchase_order_id'];
				if ($item['product_id'] == $purchase_item['product_id']){
                    $totalOrderQty += $purchase_item['quantity'];
					if ($purchase_item['summary_tax1']){
						$total_taxes += $this->isPurchaseOrderTaxInclusive($purchase_item['tax1'], $purchase_order_id) ? $purchase_item['summary_tax1'] : 0;
					}
					if ($purchase_item['summary_tax2']){
						$total_taxes += $this->isPurchaseOrderTaxInclusive($purchase_item['tax2'], $purchase_order_id) ? $purchase_item['summary_tax2'] : 0;
					}
				}
			}
            return TaxesHelper::calculatePartialTax($total_taxes, $totalOrderQty, $item['quantity']);
		}
    	return parent::calculateItemIncludedTax($item, $modelName); // will return zero anyway because Requisitions don't have taxes
	}

	protected function isInvoiceTaxInclusive($tax_id, $invoice_id){
		$InvoiceTaxModel = GetObjectOrLoadModel('InvoiceTax');
		$tax = $InvoiceTaxModel->find('first', ['conditions' => ['tax_id' => $tax_id, 'InvoiceTax.invoice_id' => $invoice_id]]);
		return $tax['InvoiceTax']['included'];
	}

	protected function isPurchaseOrderTaxInclusive($tax_id, $purchase_order_id){
		$PurchaseOrderTaxModel = GetObjectOrLoadModel('PurchaseOrderTax');
		$tax = $PurchaseOrderTaxModel->find('first', ['conditions' => ['tax_id' => $tax_id, 'PurchaseOrderTax.purchase_order_id' => $purchase_order_id]]);
		return $tax['PurchaseOrderTax']['included'];
	}

	protected function setInvoiceData(){
		if (!$this->InvoiceData){
			$this->InvoiceData = $this->getInvoiceData();
		}
	}

	protected function setPurchaseOrderData(){
    	if (!$this->PurchaseOrderData){
    		$this->PurchaseOrderData = $this->getPurchaseOrderData();
	    }
	}

	protected function getInvoiceData(){
		$InvoiceModel = GetObjectOrLoadModel('Invoice');
    	return $InvoiceModel->getInvoice($this->modelData['Requisition']['order_id']);
	}

	protected function getPurchaseOrderData()
	{
		$PurchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
		return $PurchaseOrderModel->getPurchaseOrder($this->modelData['Requisition']['order_id']);
	}
	/**
	 * @param $item
	 * @return float|int
	 */
	private function getInvoiceItemTotalPrice($item, $type = Requisition::ORDER_TYPE_INVOICE)
	{
		$this->setInvoiceData();
		$total_price = 0;
		$total_quantity = 0;
        $currency_rate = 1;
        // The PurchaseOrder Might be in Different Currency so we have to make sure that we also multiply with the currency_rate here as well
        if ($this->InvoiceData['Invoice']['currency_code'] != getCurrentSite('currency_code')) {
            $Requisition = GetObjectOrLoadModel('Requisition');
            if($this->InvoiceData['Invoice']['summary_total'] == 0) {
                $sourceModelData = $this->source_model_name . 'Data';
                $currency_rate = CurrencyConverter::index($this->$sourceModelData[$this->source_model_name]['currency_code'], getCurrentSite('currency_code'), date('Y-m-d', strtotime($this->fixReceivedDate($this->$sourceModelData[$this->source_model_name]['date']))));
            } else {
                $currency_rate = $Requisition->getSourceCurrencyRateFromJournal($this->InvoiceData, 'Invoice', $type);
            }
        }
		foreach ($this->InvoiceData['InvoiceItem'] as $invoice_item) {
			if ($invoice_item['product_id'] == $item['product_id'] && $this->modelData['Requisition']['store_id'] == $invoice_item['store_id']) {
				$total_quantity += $invoice_item['quantity'];
				$total_price += $invoice_item['unit_price'] * $currency_rate * $invoice_item['quantity'];
			}
		}
		if ($total_quantity != $item['quantity'] && $total_quantity != 0) {
			$average_price = $total_price / $total_quantity;
			$total_price = $average_price * $item['quantity'];
		}
		return $total_price;
	}

	/**
	 * @param $item
	 * @return float|int
	 */
	private function getPurchaseOrderItemTotalPrice($item, $order_type = Requisition::ORDER_TYPE_PURCHASE_ORDER)
	{
		$this->setPurchaseOrderData();
		$total_price = 0;
		$total_quantity = 0;
        $currency_rate = 1;
        $sourceModelData = $this->source_model_name . 'Data';
        // The PurchaseOrder Might be in Different Currency so we have to make sure that we also multiply with the currency_rate here as well
        // We can't take the journal currency rate here because the Order is not Journaled when the total is 0 (100% discount scenario)
        if ($this->$sourceModelData[$this->source_model_name]['currency_code'] != getCurrentSite('currency_code')) {
            $currency_rate = CurrencyConverter::index($this->$sourceModelData[$this->source_model_name]['currency_code'], getCurrentSite('currency_code'), date('Y-m-d', strtotime($this->fixReceivedDate($this->$sourceModelData[$this->source_model_name]['date']))));
        }
		foreach ($this->PurchaseOrderData['PurchaseOrderItem'] as $purchase_item) {
			if ($purchase_item['product_id'] == $item['product_id']) {
                    $total_price += $purchase_item['unit_price'] * $currency_rate * $purchase_item['quantity'];
                    $total_quantity += $purchase_item['quantity'];
                }
            }
		if ($total_quantity != $item['quantity'] && $total_quantity != 0) {
			$average_price = $total_price / $total_quantity;
			$total_price = $average_price * $item['quantity'];
		}
        $defaultCurrencyCode = getCurrentSite('currency_code');
		// Purchase Order currency Is different from Site Currency
		if ($this->PurchaseOrderData['PurchaseOrder']['currency_code'] != $defaultCurrencyCode && $this->modelData['Requisition']['currency_code'] != $defaultCurrencyCode) {
			$expense_distribution_product_cost = $this->getExpenseDistributionProductTotalCost($item);
//			$currency_rate = CurrencyConverter::index($this->PurchaseOrderData['PurchaseOrder']['currency_code'], getCurrentSite('currency_code'));
            $ExpenseDistributionModel = GetObjectOrLoadModel('ExpenseDistribution');
            $expense_distribution = $ExpenseDistributionModel->find('first', ['conditions' => ['ExpenseDistribution.requisition_id' => $item['requisition_id']]]);
            // Then we convert back the Expense Distribution Total Cost Amount to the PurchaseOrder Currency so we can Add them
            $currency_rate = !empty($expense_distribution['ExpenseDistribution']['requisition_rate']) ? $expense_distribution['ExpenseDistribution']['requisition_rate'] : 1;
//            // Then we convert back the Expense Distribution Total Cost Amount to the PurchaseOrder Currency so we can Add them
			$expense_distribution_product_cost /= $currency_rate;
            debug($currency_rate);
            debug($expense_distribution_product_cost);
            debug($total_price);
		} else {
			$expense_distribution_product_cost = $this->getExpenseDistributionProductTotalCost($item);
		}
		return $total_price + $expense_distribution_product_cost;
	}

	/**
	 * @param $item
	 * @return mixed
	 */
	private function getPosItemTotalPrice($item)
	{
		$invoice_type = $this->getInvoiceTypeFromRequisitionOrderType();
		if(ifPluginActive(PosPlugin)) {
            return $this->PosShiftModel->getPosShiftProductTotalPriceAndDiscount($this->modelData['Requisition']['order_id'], $item['product_id'], $invoice_type)['summary_subtotal'];
        }
	}

	/**
	 * @param $item
	 * @param $expense_distribution_product_cost
	 * @return mixed
	 */
	private function getExpenseDistributionProductTotalCost($item)
	{
		$expense_distribution_product_cost = 0;
		$ExpenseDistributionModel = GetObjectOrLoadModel('ExpenseDistribution');
		$expense_distribution = $ExpenseDistributionModel->find('first', ['conditions' => ['ExpenseDistribution.requisition_id' => $item['requisition_id']]]);
		if ($expense_distribution) {
			foreach ($expense_distribution['ExpenseDistributionProduct'] as $expense_distribution_product) {
				if ($expense_distribution_product['product_id'] == $item['product_id']) {
					$expense_distribution_product_cost += $expense_distribution_product['total_cost'];
				}
			}
		}
		return $expense_distribution_product_cost;
	}

	/**
	 * @return int
	 */
	private function getInvoiceTypeFromRequisitionOrderType()
	{
		return $this->orderType == Requisition::ORDER_TYPE_POS_OUTBOUND ? Invoice::Invoice : Invoice::Refund_Receipt;
	}

    private function updateStockTransactionProductPrice($data)
    {
        if (!isset($data['Requisition']['parent_requisition_id'])) {
            return;
        }
        if($data['Requisition']['order_type'] != Requisition::ORDER_TYPE_INVOICE) {
            return;
        }
        $requisition = GetObjectOrLoadModel('Requisition');
        $result = $requisition->find('all', ['conditions' => ["Requisition.parent_requisition_id =  {$data['Requisition']['parent_requisition_id']} OR Requisition.id = {$data['Requisition']['parent_requisition_id']}"]]);
        $requisitionIds = array_column(array_column($result, 'Requisition'), 'id');
        $invoiceItem = GetObjectOrLoadModel('InvoiceItem');
        $invoiceItems = $invoiceItem->find('all', ['conditions' => ["InvoiceItem.invoice_id =  {$data['Requisition']['order_id']}"]]);
        $stockTransaction = GetObjectOrLoadModel('StockTransaction');

        foreach ($invoiceItems as $item) {
            $stockTransaction->updateAll(
                ['price' => $item['InvoiceItem']['unit_price']],
                [
                    'order_id IN (' . implode(',', $requisitionIds) . ')',
                    "product_id =  {$item['InvoiceItem']['product_id']}"
                ]
            );
        }
    }
}
