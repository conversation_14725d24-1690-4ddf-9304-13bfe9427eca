<?php
namespace App\Services\StockTransactions\StockTransactionUpdaters;


use App\Helpers\TrackingNumberHelper;
use App\Utils\TrackStockUtil;
use DateTime;
use Requisition;
use StockTransaction;

Class CommonUpdater
{
    // Models
    protected $TaxModel;
    protected $StockTransactionModel;
    protected $RequisitionModel;
    protected $PosShiftModel;
    protected $Model;

    // Properties
    protected $savedTransactionIds;
    protected $transactions;
    protected $modelData;
    protected $order;
    protected $orderType;
    protected $orderItems;
    protected $model_name;
    protected $taxes_array;



    public function __construct()
    {
        $this->StockTransactionModel = GetObjectOrLoadModel('StockTransaction');
        $this->RequisitionModel = GetObjectOrLoadModel('Requisition');
        if(ifPluginActive(PosPlugin)){
            $this->PosShiftModel = GetObjectOrLoadModel('PosShift');
        }
        $this->orderItems = [];
        $this->transactions = [];
        $this->savedTransactionIds = [];
        $this->taxes_array = [];
    }

	/**
	 * @param $item
	 * @param InvoiceUpdater $instance
	 * @return mixed
     * gets the total quantity of product in order
     * so that it can be saved in a single transaction
     * if its tracking is quantity
	 */
	protected function getQuantity($item)
	{
		$store_id = isset($item['store_id']) ?$item['store_id']: (isset($this->modelData["{$this->model_name}"]['store_id']) ? $this->modelData["{$this->model_name}"]['store_id'] : null);
		$product_id = $item['product_id'];
		$tracking_type = $item['Product']['tracking_type'];
		$storeIdAndProductIdExists = isset($this->transactions[$store_id][$product_id]);

        if($tracking_type !== TrackStockUtil::QUANTITY_ONLY && $storeIdAndProductIdExists) {
            $trackingIdentifier = TrackingNumberHelper::getTrackingDataIdentifier($item['tracking_data']);
            if(isset($this->transactions[$store_id][$product_id][$trackingIdentifier])) {
                return $item['quantity'] + $this->transactions[$store_id][$product_id][$trackingIdentifier]['quantity'];
            }
        }
		// Store Id Exists and Product Already Added then we Get the Total Quantity
		if ($storeIdAndProductIdExists && $tracking_type === TrackStockUtil::QUANTITY_ONLY) {
			return $item['quantity'] + $this->transactions[$store_id][$product_id]['quantity'];
		}
		return $item['quantity'];
	}

	protected function getTotalPrice($item, $type = null)
	{
		$itemTotal = $item['quantity'] * $item['unit_price'];

		$store_id = $item['store_id'] ?: $this->modelData["{$this->model_name}"]['store_id'];
		$product_id = $item['product_id'];
		$tracking_type = $item['Product']['tracking_type'];

		$storeIdAndProductIdExists = isset($this->transactions[$store_id][$product_id]);


        if($tracking_type !== TrackStockUtil::QUANTITY_ONLY && $storeIdAndProductIdExists) {
            $trackingIdentifier = TrackingNumberHelper::getTrackingDataIdentifier($item['tracking_data']);
            if(isset($this->transactions[$store_id][$product_id][$trackingIdentifier])) {
                $itemTotal += $this->transactions[$store_id][$product_id][$trackingIdentifier]['total_price'];
            }
        }

		// Store Id Exists and Product Already Added then we Get the Total Price and Add it to the current Total
		if ($storeIdAndProductIdExists && $tracking_type === TrackStockUtil::QUANTITY_ONLY) {
			$itemTotal += $this->transactions[$store_id][$product_id]['total_price'];
		}

		// Extra Old Behavior Copied as we have seen before only works in Invoices So It's harmless
		if (isset($item['extra_details'])) {
			$extraDetails = json_decode($item['extra_details'], true);
			if (isset($extraDetails['copayment'])) {
				$itemTotal += $extraDetails['copayment'];
			}
        }
		return $itemTotal;
	}

	/**
	 * @param $item
	 * @param InvoiceUpdater $instance
	 * @return float|int|mixed
	 */
	protected function getPrice($item, $type = null)
	{
		$total_price = $this->getTotalPrice($item, $type);
		$total_item_tax = $this->calculateItemIncludedTax($item, $this->model_name);
		$total_quantity = $this->getQuantity($item);
        if ($total_quantity == 0) return false;
		return ($total_price - $total_item_tax) / $total_quantity;
	}

	protected function calculateItemIncludedTax($item, $modelName)
    {
        $taxModelName = "{$modelName}Tax"; // Will Evaluate To InvoiceTax or PurchaseOrderTax
        $foreignKeyNameId = $modelName === 'Invoice' ? $taxModelName.'.invoice_id' : $taxModelName.'.purchase_order_id';
        // We Only calculate the taxes for Invoices and PurchaseOrder
        if(in_array($modelName, ['Invoice', 'PurchaseOrder'])){
            $this->TaxModel = GetObjectOrLoadModel($taxModelName);
        } else {
            return 0;
        }
        $included_tax = 0;
        if (!empty($item['tax1'])) {
            $tax1 = $this->TaxModel->find('first', ['conditions' => ['tax_id' => $item['tax1'], $foreignKeyNameId => $this->modelData[$modelName]['id']]]);
            if ($tax1[$taxModelName]['included'] == 1) {
                $included_tax += $item['summary_tax1'];
            }
        }
        if (!empty($item['tax2'])) {
            $tax2 = $this->TaxModel->find('first', ['conditions' => ['tax_id' => $item['tax2'], $foreignKeyNameId => $this->modelData[$modelName]['id']]]);
            if ($tax2[$taxModelName]['included'] == 1) {
                $included_tax += $item['summary_tax2'];
            }
        }

        $item_store_id = $item['store_id'] ?: $this->modelData["$modelName"]['store_id'];
        $product_id = $item['product_id'];

        $trackingIdentifier = TrackingNumberHelper::getTrackingDataIdentifier($item['tracking_data']);
        if($trackingIdentifier) {
            if (isset($this->taxes_array[$item_store_id][$product_id][$trackingIdentifier])){
                $this->taxes_array[$item_store_id][$product_id][$trackingIdentifier] += $included_tax;
            } else {
                $this->taxes_array[$item_store_id][$product_id] = [$trackingIdentifier => $included_tax];
            }
            return $this->taxes_array[$item_store_id][$product_id][$trackingIdentifier];
        } else {
            if (isset($this->taxes_array[$item_store_id][$product_id])){
                if (is_array($this->taxes_array[$item_store_id][$product_id])) {
                    $this->taxes_array[$item_store_id][$product_id][0] += $included_tax;
                } else {
                    $this->taxes_array[$item_store_id][$product_id] += $included_tax;
                }
            } else {
                if (is_array($this->taxes_array[$item_store_id][$product_id])) {
                    $this->taxes_array[$item_store_id][$product_id][0] = $included_tax;
                } else {
                    $this->taxes_array[$item_store_id][$product_id] = $included_tax;
                }
            }
        }
        return $this->taxes_array[$item_store_id][$product_id][0] ??$this->taxes_array[$item_store_id][$product_id];
    }

    public static function trackingDataExists($trackingType, $trackingData)
    {
        if($trackingType === TrackStockUtil::TYPE_LOT_EXPIRY) {
            return !empty($trackingData['lot']) && !empty($trackingData['expiry_date']);
        }
        return $trackingData[$trackingType] !== null;
    }

    protected function wasTracked($item)
    {
        return ($this->StockTransactionModel->find('count', ['conditions' => ['StockTransaction.product_id' => $item['product_id']]]) > 0);
    }

    protected function isTracked($item)
    {
        return !empty($item['Product']['track_stock']);
    }

    protected function hasProductId($item)
    {
        return !empty($item['product_id']);
    }

    protected function isDataFound($data)
    {
        if (!empty($data)) return true;
        return false;
    }

    protected function addSerialTransactions($transactionArray)
    {
    	$store_id = $transactionArray['store_id'];
    	$product_id = $transactionArray['product_id'];
        $writtenQuantity = $transactionArray['quantity'];
        // Since it's a serial transaction we always set the quantity to 1
        $transactionArray['quantity'] = 1;
        $trackingData = $transactionArray['tracking_data'];
        $trackingType = $transactionArray['Product']['tracking_type'];
        if ($this->trackingDataExists($trackingType, $trackingData)) {
            $transactionArray['quantity'] = 1;
            $serialsArray = $trackingData['serial'];
            foreach ($serialsArray as $serial) {
                $transactionArray['tracking_data'] = [
                    'serial' => $serial,
                ];
                $this->transactions[$store_id][$product_id][] = $transactionArray;
            }
        } else {
            // Serials Weren't Found In the Tracking Data
            for ($i = 0; $i < $writtenQuantity; $i++) {
                $transactionArray['tracking_number_id'] = -1;
	            $this->transactions[$store_id][$product_id][] = $transactionArray;
            }
        }
    }

	// This Function checks $received_date/$date for 00:00 and replaces them with the hour/minute of the timestamp of created record to fix the order of the transactions in The product transaction list.
	protected function fixReceivedDate($received_date) {
		$mysql_date = $this->StockTransactionModel->formatDateTime($received_date);
		$received_date_datetime_mysql = DateTime::createFromFormat('Y-m-d H:i:s', $mysql_date);
        if (!$received_date_datetime_mysql || (strpos($received_date_datetime_mysql->format("H:i:s"), "00:00:00") !== false && !str_contains($received_date, "00:00:00"))) {
            return date('Y-m-d', strtotime($mysql_date)) . ' ' . date('H:i:s', strtotime($this->modelData[$this->model_name]['created']));
		}
		return $received_date_datetime_mysql->format('Y-m-d H:i:s');
	}

    protected function addOthersTransaction($transactionArray)
    {
	    $store_id = $transactionArray['store_id'];
	    $product_id = $transactionArray['product_id'];
        // all Other Product Tracking Types
        $trackingData = $transactionArray['tracking_data'];
        $trackingType = $transactionArray['Product']['tracking_type'];
        if (!$this->trackingDataExists($trackingType, $trackingData)) {
            $transactionArray['tracking_number_id'] = -1;
        }
        if ($trackingType !== TrackStockUtil::QUANTITY_ONLY) {
            $trackingIdentifier = TrackingNumberHelper::getTrackingDataIdentifier($trackingData);
            $this->transactions[$store_id][$product_id][$trackingIdentifier] = $transactionArray;
        } else {
        	// Here we Overwrite the old transaction if found or we simply add a new one statically this way
	        $this->transactions[$store_id][$product_id] = $transactionArray;
        }
    }

    protected function getTransactionMetaData($transaction) {
        return ['param3' => $this->modelData['Invoice']['no'], 'param4' => $transaction['Product']['product_code'], 'param5' => $transaction['Product']['name']];
    }

    protected function saveTransactions()
    {
        foreach ($this->transactions as $storeArray) {
            foreach ($storeArray as $productArray){
            	// if $productArray is a transaction Aka Quantity Only
	            if (isset($productArray['Product'])){
	            	$this->saveTransaction($productArray);
	            } else {
		            // If $productArray is a transactions array for the product (Serials, Lots ..etc)
	            	foreach($productArray as $transaction){
			            $this->saveTransaction($transaction);
		            }
	            }
            }
        }
    }

    protected function calculateItemsSubTotal($items) {
        $subtotal = 0;
        foreach ($items as $item) {
            $itemSub = (float)$item['unit_price'] * (float)$item['quantity'];
            $discount_val = $this->Model->calculate_item_discount($item, $itemSub);
            $subtotal += $itemSub - $discount_val;
        }
        return $subtotal;
    }

    protected function saveTransaction($transaction){
        if ($this->orderType == Requisition::ORDER_TYPE_TRANSFER_REQUISITION) {
            $transferOutTransaction = $transaction;
            $transferOutTransaction['transaction_type'] = StockTransaction::TRANSACTION_OUT;
            $transactionMetaData = $this->getTransactionMetaData($transferOutTransaction);
            $this->savedTransactionIds[] = StockTransaction::saveTransaction($transferOutTransaction, $transactionMetaData);
            $transaction['store_id'] = $this->modelData['Requisition']['to_store_id'];
        }
        $transactionMetaData = $this->getTransactionMetaData($transaction);
        $transactin_id = StockTransaction::saveTransaction($transaction, $transactionMetaData);
        $this->savedTransactionIds[] = $transactin_id;
        $this->checkNotSavedStockTransaction($transactin_id, $transaction);
    }

	// This should prevent problems related to saving the stock_transaction on different branches
	protected function setBranchIdFromSource(&$transactionArray){
		if (!ifPluginActive(BranchesPlugin)) {
			return false;
		}
		if ($this->modelData[$this->model_name]['branch_id'] != getCurrentBranchID() && empty($transactionArray['branch_id'])) {
			$transactionArray['branch_id'] = $this->modelData[$this->model_name]['branch_id'] ?: getCurrentBranchID();
		}
	}

    protected function checkNotSavedStockTransaction($transactin_id, $transaction){
        if(empty($transactin_id)){
            \Rollbar::log(\Rollbar\Payload\Level::ERROR , "stock not saved successfully", [
                'transaction_data' => $transaction
            ]);
        }
    }
}
