<?php

namespace App\Services\StockTransactions;


use StockTransaction;

class TrackingNumberHelper
{
    private $TrackingNumberModel;
    private $StockTransactionModel;

    public function recordTracking($product, $tracking_data, $product_id, $quantity, $store_id)
    {
        $this->TrackingNumberModel = GetObjectOrLoadModel('TrackingNumber');
        $tracking_id = self::findTrackingId($tracking_data, $product['tracking_type'], $product_id, $store_id);
        if (!$tracking_id) {
            // Create Tracking (New Item)
            return $this->createTracking($tracking_data, $product_id, $store_id, $quantity);
        }
        return $this->adjustTrackingQuantity($tracking_id, $quantity);
    }

    public static function findTrackingId($tracking_data, $type, $product_id, $store_id)
    {
        $TrackingNumberModel = GetObjectOrLoadModel('TrackingNumber');
        switch ($type) {
            case 'serial':
                $tracking_id = $TrackingNumberModel->find('first', ['conditions' => ['TrackingNumber.product_id' => $product_id, 'TrackingNumber.serial' => $tracking_data['serial'], 'TrackingNumber.store_id' => $store_id],
                ])['TrackingNumber']['id'];
                break;
            case 'lot':
                $tracking_id = $TrackingNumberModel->find('first', ['conditions' => ['TrackingNumber.product_id' => $product_id, 'TrackingNumber.lot' => $tracking_data['lot'], 'TrackingNumber.store_id' => $store_id]])['TrackingNumber']['id'];
                break;
            case 'expiry_date';
                $tracking_id = $TrackingNumberModel->find('first', ['conditions' => ['TrackingNumber.product_id' => $product_id, 'TrackingNumber.expiry_date' => $tracking_data['expiry_date'], 'TrackingNumber.store_id' => $store_id]])['TrackingNumber']['id'];
                break;
            case 'lot_and_expiry_date';
                $tracking_id = $TrackingNumberModel->find('first', ['conditions' => ['TrackingNumber.product_id' => $product_id, 'TrackingNumber.expiry_date' => $tracking_data['expiry_date'], ['TrackingNumber.lot' => $tracking_data['lot']], 'TrackingNumber.store_id' => $store_id]])['TrackingNumber']['id'];
                break;
            default:
                return false;
        }
        return $tracking_id;
    }

    function createTracking($tracking_data, $product_id, $store_id, $quantity)
    {
        // We Return -1 Here because we should return -1 for PENDING/Draft Transactions
        if(!$this->isTrackingDataValid($tracking_data)) return -1;
        $this->TrackingNumberModel = GetObjectOrLoadModel('TrackingNumber');
        $tracking_data = [
            'lot' => $tracking_data['lot'] ?: null,
            'expiry_date' => isset($tracking_data['expiry_date']) ? ($this->TrackingNumberModel->formatDate($tracking_data['expiry_date']) ? $this->TrackingNumberModel->formatDate($tracking_data['expiry_date']) : $tracking_data['expiry_date']) : null,
            'serial' => $tracking_data['serial'] ?: null,
            'product_id' => $product_id,
            'store_id' => $store_id,
            'quantity' => $quantity,
            'created' => null,
            'modified' => null
        ];
        $this->TrackingNumberModel->create();
        $this->TrackingNumberModel->save($tracking_data);
        $tracking_id = $this->TrackingNumberModel->getLastInsertID();
        unset($this->TrackingNumberModel->id);
        return $tracking_id;
    }

    function adjustTrackingQuantity($tracking_id, $quantity)
    {
        if(ifPluginActive(PRODUCT_TRACKING_PLUGIN))
        {
            $this->TrackingNumberModel = GetObjectOrLoadModel('TrackingNumber');
            $this->StockTransactionModel = GetObjectOrLoadModel('StockTransaction');
            $result = $this->TrackingNumberModel->findById($tracking_id);
            if (!$result) return false;
            // Search For Transactions For this Product First !
            $transactions_exist = $this->StockTransactionModel->find('first', ['conditions' => ['StockTransaction.tracking_number_id' => $tracking_id]]);
            // If product doesn't have any transactions remove it's tracking for good and return;
            if (false === $transactions_exist)
                return $this->removeTracking($result['TrackingNumber']['id']);
            $this->TrackingNumberModel->id = $tracking_id;
            $this->TrackingNumberModel->saveField('quantity', ($result['TrackingNumber']['quantity'] + $quantity), false);
            unset($this->TrackingNumberModel->id);
            return $tracking_id;
        }
    }

    public function fixTrackingNumberQuantity($tracking_number_id) {
        if(ifPluginActive(PRODUCT_TRACKING_PLUGIN)) {
            $this->TrackingNumberModel = GetObjectOrLoadModel('TrackingNumber');
            $this->StockTransactionModel = GetObjectOrLoadModel('StockTransaction');
            $result = $this->TrackingNumberModel->findById($tracking_number_id);
            if (!$result) return false;
            $store_id = $result['TrackingNumber']['store_id'];
            $confirmed_status = StockTransaction::STATUS_PROCESSED;
            // Use false 2nd parameter when doing query on model to prevent caching
            $summed_quantity = $this->StockTransactionModel->query("SELECT SUM(quantity) as total FROM stock_transactions WHERE tracking_number_id = $tracking_number_id AND store_id = $store_id AND status = $confirmed_status", false)[0][0]['total'];
            $this->TrackingNumberModel->create();
            $this->TrackingNumberModel->id = $tracking_number_id;
            $this->TrackingNumberModel->saveField('quantity', $summed_quantity, false);
        }
    }

    public function removeTracking($tracking_id)
    {
        $this->TrackingNumberModel = GetObjectOrLoadModel('TrackingNumber');
        $result = $this->TrackingNumberModel->findById($tracking_id);
        if (!$result) return false;
        $this->TrackingNumberModel->delete($tracking_id);
        return false;
    }

    public static function calculateTrackingQuantity($transactionType, $mode, $old_quantity, $new_quantity)
    {
        switch ($transactionType) {
            case StockTransaction::TRANSACTION_IN:
                switch ($mode) {
                    case 'Add':
                        return $new_quantity;
                    case 'Edit':
                        return $new_quantity - $old_quantity;
                    case 'Remove':
                        return -1 * $old_quantity;
                }
                break;
            case StockTransaction::TRANSACTION_OUT:
                switch ($mode) {
                    case 'Add':
                        return -1 * $new_quantity;
                    case 'Edit':
                        return -1 * ($new_quantity + $old_quantity);
                    case 'Remove':
                        return -1 * $old_quantity;
                }
                break;
        }
    }

    private function isTrackingDataValid($tracking_data){
        $serial = $tracking_data['serial'] != NULL;
        $lot = $tracking_data['lot'] != NULL;
        $expiry_date = $tracking_data['expiry_date'] != NULL;
        if(!$serial && !$lot && !$expiry_date) return false;
        return true;
    }
}
