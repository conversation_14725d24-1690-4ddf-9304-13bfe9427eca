<?php

namespace App\Services\SalesInvoicesReferrals;

use Exception;

use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Cookie\CookieJar;

class SalesInvoicesReferral
{
    /**
     * @var mixed
     */
    protected $invoiceReferralModel;

    /**
     * @var mixed
     */
    protected $clientsModel;

    /**
     * @var mixed
     */
    protected $suppliersModel;

    /**
     * @var mixed
     */
    protected $taxesModel;

    /**
     * @var string
     */
    protected $apiKey;

    /**
     * @var string
     */
    protected string $baseApiUrl;

    /**
     * @var GuzzleClient
     */
    protected static $httpClient;

    /**
     * @var int
     */
    protected $siteId;

    public function __construct()
    {
        $this->clientsModel = GetObjectOrLoadModel("Client");
        $this->suppliersModel = GetObjectOrLoadModel("Supplier");
        $this->taxesModel = GetObjectOrLoadModel("Tax");
        $this->invoiceReferralModel = GetObjectOrLoadModel(
            "SalesInvoicesReferral"
        );
        $apiKeysModel = GetObjectOrLoadModel("ApiKey");
        $apiKey = $apiKeysModel->createKey("internalApi");
        $this->apiKey = $apiKey["ApiKey"]["key"] ?? $apiKey;

        $this->baseApiUrl = sprintf(
            "https://%s/api2/",
            getCurrentSite("subdomain")
        );

        $this->siteId = getCurrentSite("id");
    }

    /**
     * Function to encrypt unique invoice data.
     *
     * @param int $invoiceId
     * @param int $siteId
     * @param string $model
     *
     * @return string
     */
    public function getSIPHash(
        int $invoiceId,
        int $siteId,
        string $model
    ): string {
        return base64_encode(
            json_encode([
                "model" => $model,
                "invoice_id" => $invoiceId,
                "site_id" => $siteId,
            ])
        );
    }

    /**
     * Validate a given hash string and decrypt it.
     *
     * @param string $hash
     *
     * @return bool|array
     */
    public function decodeSIPHash(string $hash): bool|array
    {
        $info = json_decode(base64_decode($hash), true);

        return $info;
    }

    /**
     * Select from the table based on hashing script.
     *
     * @param string $hashString
     *
     * @return array|bool
     */
    public function findByTrackingHashString(string $hashString): array|bool
    {
        $model = $this->invoiceReferralModel->find("first", [
            "conditions" => [
                'JSON_EXTRACT(metainfo, "$.tracking_code")' => $hashString,
                // "SalesInvoicesReferral.status" => "pending",
            ],
        ]);

        return $model["SalesInvoicesReferral"] ?? false;
    }

    /**
     * Update the target site column for a give hash string.
     *
     * @param string $hashString
     * @param string $targetSiteId
     *
     * @return void
     */
    public function updateTargetSiteByHashString(
        string $hashString,
        string $targetSiteId
    ): void {
        $this->invoiceReferralModel->updateAll(
            [
                "SalesInvoicesReferral.target_site_id" => $targetSiteId,
            ],
            [
                'JSON_EXTRACT(metainfo, "$.tracking_code")' => $hashString,
            ]
        );
    }

    /**
     * Insert a new row to the sales_invoces_referral model
     *
     * @param array $data
     *
     * @return void
     */
    public function insertNewReferral($data): void
    {
        $this->invoiceReferralModel->save(
            [
                "SalesInvoicesReferral" => $data,
            ],
            false
        );
    }

    /**
     * Updates the Status column and the metainfo json doc
     *
     * @param int $referralId
     * @param ?array $metainfo
     * @param string $status
     */
    public function updateStatusAndMetaInfo(
        int $referralId,
        ?array $metainfo = null,
        string $status = "inserted"
    ): void {
        $this->invoiceReferralModel->id = $referralId;

        $data = [
            "status" => $status,
        ];

        if ($metainfo) {
            $data['metainfo'] = json_encode($metainfo);
        }

        $this->invoiceReferralModel->save($data);
    }

    /**
     * Factory method to sync tracked invoice with new installation.
     *
     * @param string $hashString
     *
     * @throws Exception
     * @return array|bool
     */
    public function syncTrackedInvoice($hashString, $siteId): array|bool
    {
        $trackedInvoiceReferral = $this->findByTrackingHashString($hashString);

        // Do nothing if there are no referral entry exists
        if (!$trackedInvoiceReferral) {
            return false;
        }

        // Break the execution if the import process tries to import the
        // invoice in the same site
        if ($trackedInvoiceReferral["source_site_id"] == $siteId) {
            return [
                'status' => 'ERR',
                'errors' => [
                    __t(
                        $trackedInvoiceReferral['model'] == 'Invoice'
                        ? 'You cannot convert the Sales Invoice to Purchase Invoice in the same site'
                        : 'You cannot convert the Purchase Invoice to Sales Invoice in the same site'
                    )
                ]
            ];
        }

        if ($trackedInvoiceReferral['status'] == 'inserted') {
            return [
                'status' => 'ERR',
                'errors' => [
                    __t(
                        $trackedInvoiceReferral['model'] == 'Invoice'
                        ? 'You cannot convert the same sales invoice to a purchase invoice more than once for the same site'
                        : 'You cannot convert the same purchase invoice to a sales invoice more than once for the same site'
                    )
                ]
            ];
        }

        // Do nothing if invoice status is not pending
        // @todo check what should be done if the status is failed or canceled
        if ($trackedInvoiceReferral['status'] != 'pending') {
            return false;
        }

        // If the target site id is null, update it to the current site id
        if (!$trackedInvoiceReferral["target_site_id"]) {
            $this->updateTargetSiteByHashString($hashString, $siteId);
        }

        // To start importing invoices & purchase orders
        // the Sales & Inventory plugins must be enabled
        $requiredPlugins = [];
        if (!ifPluginActive(InvoicesPlugin)) {
            $requiredPlugins[] = InvoicesPlugin;
        }
        if (!ifPluginActive(InventoryPlugin)) {
            $requiredPlugins[] = InventoryPlugin;
        }
        if (!ifPluginActive(PURCHASE_CYCLE_PLUGIN)) {
            $requiredPlugins[] = PURCHASE_CYCLE_PLUGIN;
        }
        $this->enableRequiredPlugins($requiredPlugins);

        $trackedInvoice = json_decode(
            $trackedInvoiceReferral["invoice"],
            true
        );

        // Handle and prepare the taxes object
        $taxes = $this->handleTaxes($trackedInvoice["InvoiceTax"]);

        // Insert the new model based on the referral entry model
        $insertedInvoice = match ($trackedInvoiceReferral["model"]) {
            "Invoice" => $this->convertInvoiceToPurchaseOrder(
                $trackedInvoice,
                $taxes
            ),
            "PurchaseOrder" => $this->convertPurchaseOrderToInvoice(
                $trackedInvoice,
                $taxes
            ),
            default => throw new Exception("Invalid model"),
        };

        // On inserting the invoice successfully:
        // update it's status to be inserted
        // otherwise update it to be failed
        $status = "failed";
        if ($insertedInvoice['status'] == "OK") {
            $metainfo = json_decode($trackedInvoiceReferral["metainfo"], true);
            $metainfo["invoice_id"] = $insertedInvoice["id"];
            $status = "inserted";
        }

        $this->updateStatusAndMetaInfo(
            referralId: $trackedInvoiceReferral["id"],
            metainfo: $metainfo,
            status: $status
        );

        $insertedInvoice['model'] = $trackedInvoiceReferral["model"];

        return $insertedInvoice;
    }

    /**
     * Factory method to convert a give purchase order invoice
     * into a sales invoice
     *
     * @param array $trackedInvoice
     * @param array $taxes
     *
     * @return array
     */
    public function convertPurchaseOrderToInvoice(
        array $trackedInvoice,
        array $taxes
    ): array {
        $client = $this->getClient($trackedInvoice["Site"]["email"]);

        if (!$client) {
            $client = $this->insertClient($trackedInvoice["Site"]);

            if ($client['status'] !== 'OK') {
                return $client;
            }
        }

        $clientId = $client["id"];

        $invoice = $this->insertSalesInvoice(
            $trackedInvoice,
            $taxes,
            $clientId
        );

        return $invoice;
    }

    /**
     * Factory method to convert a give sales invoice
     * into a purchase order invoice
     *
     * @param array $trackedInvoice
     * @param array $taxes
     *
     * @return array
     */
    public function convertInvoiceToPurchaseOrder(
        array $trackedInvoice,
        array $taxes
    ): array {
        $supplier = $this->getSupplier($trackedInvoice["Site"]["email"]);

        if (!$supplier) {
            $supplier = $this->insertSupplier($trackedInvoice["Site"]);

            if ($supplier['status'] !== 'OK') {
                return $supplier;
            }
        }

        $supplierId = $supplier["id"];

        $invoice = $this->insertPurchaseOrder(
            $trackedInvoice,
            $taxes,
            $supplierId
        );

        return $invoice;
    }

    /**
     * Factory method to initialize the Http client.
     *
     * @return GuzzleClient
     */
    private function initHttpClient()
    {
        if (!self::$httpClient) {
            self::$httpClient = new GuzzleClient([
                "base_uri" => $this->baseApiUrl,
                "verify" => false,
                "headers" => [
                    "content-type" => "application/json",
                    "APIKEY" => $this->apiKey,
                ],
            ]);
        }

        return self::$httpClient;
    }

    /**
     * Select a client from the database using a given email.
     * This used to prevent duplicating the clients for each import process.
     *
     * @param string $email
     *
     * @return array|bool
     */
    protected function getClient(string $email): array|bool
    {
        $client = $this->clientsModel->find("first", [
            "conditions" => [
                "Client.email" => $email,
            ],
        ]);

        return $client["Client"] ?? false;
    }

    /**
     * Send a request to the API to insert a new client.
     *
     * @param array $client
     *
     * @return array
     */
    protected function insertClient(array $client): array
    {
        unset($client["id"]);
        unset($client["password"]);

        try {
            $request = $this->initHttpClient()->post("clients", [
                "json" => [
                    "Client" => $client,
                ],
            ]);

            $response = json_decode($request->getBody()->getContents(), true);

            $response['status'] = 'OK';

            return $response;
        } catch (BadResponseException $e) {
            return [
                'status' => 'ERR',
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * Select a supplier from the database using a given email.
     * This used to prevent duplicating the suppliers for each import process.
     *
     * @param string $email
     *
     * @return array|bool
     */
    protected function getSupplier(string $email): array|bool
    {
        $supplier = $this->suppliersModel->find("first", [
            "conditions" => [
                "Supplier.email" => $email,
            ],
        ]);

        return $supplier["Supplier"] ?? false;
    }

    /**
     * Send a request to the API to insert a new supplier.
     *
     * @param array $supplier
     *
     * @return array
     */
    protected function insertSupplier(array $supplier)
    {
        unset($supplier["id"]);
        unset($supplier["password"]);

        try {
            $request = $this->initHttpClient()->post("suppliers", [
                "json" => [
                    "Supplier" => $supplier,
                ],
            ]);

            $response = json_decode($request->getBody()->getContents(), true);

            $response['status'] = 'OK';

            return $response;
        } catch (BadResponseException $e) {
            return [
                'status' => 'ERR',
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * Check for a given invoice or purchase order taxes
     * if the tax entry is already exists in the db then use it
     * else insert it as a new tax entry
     *
     * @param array $invoiceTaxes
     *
     * @return array
     */
    private function handleTaxes(array $invoiceTaxes): array
    {
        $taxes = [];
        foreach ($invoiceTaxes as $invoiceTax) {
            $tax = $this->getTax(
                $invoiceTax["name"],
                $invoiceTax["value"],
                $invoiceTax['included'] ?? 0,
            );

            if (!$tax) {
                $tax = $this->insertTax($invoiceTax);
            }

            $taxes[$invoiceTax["tax_id"]] = $tax["id"];
        }
        return $taxes;
    }

    /**
     * Select a tax entry from the database using it's name, value and if or
     * not included.
     *
     * @param string $taxName
     * @param string $taxValue
     * @param string|int $included
     *
     * @return array|bool
     */
    private function getTax(
        string $taxName,
        string $taxValue,
        string|int $included
    ): array|bool {

        $conditions = [
            "Tax.name" => $taxName,
            "Tax.value" => $taxValue,
        ];

        if ($included) {
            $conditions['Tax.included'] = $included;
        }

        $tax = $this->taxesModel->find("first", [
            "conditions" => $conditions,
        ]);

        return $tax["Tax"] ?? false;
    }

    /**
     * Insert a new tax entry into the database.
     *
     * @param array $tax
     *
     * @return array|bool
     */
    private function insertTax(array $tax): array|bool
    {
        $this->taxesModel->id = null;
        $data = [
            "name" => $tax["name"],
            "value" => $tax["value"],
            "included" => $tax["included"] ?? 0,
            "description" => $tax["name"],
            "site_id" => $this->siteId,
        ];

        $tax = $this->taxesModel->save([
            "Tax" => $data,
        ]);

        if (!$tax) {
            return false;
        }

        return [
            "id" => $this->taxesModel->getLastInsertID(),
        ];
    }

    /**
     * Perform a API call to insert a Purchase Order resource.
     *
     * @param array $trackerInvoice
     * @param array $taxes
     * @param int $supplierId
     *
     * @return array
     */
    private function insertPurchaseOrder(
        array $trackerInvoice,
        array $taxes,
        int $supplierId
    ) : array {
        $invoice = [];
        $invoice["PurchaseOrder"] = [
            "site_id" => $this->siteId,
            "supplier_id" => $supplierId,
            "type" => 0,
            "currency_code" => $trackerInvoice["Invoice"]["currency_code"],
            "draft" => 1,
            "date" => $trackerInvoice["Invoice"]["date"],
            "discount" => $trackerInvoice["Invoice"]['discount'] ?? "",
            "shipping_options" => $trackerInvoice["Invoice"]["shipping_options"] ?? "",
            "shipping_amount" => $trackerInvoice["Invoice"]["shipping_amount"] ?? "",
        ];

        $invoiceItems = [];
        foreach ($trackerInvoice["InvoiceItem"] as $key => $invoiceItem) {

            $product = $this->syncProduct($invoiceItem, $taxes, 'purchase_order');

            $invoiceItems[$key] = [
                "item" => $invoiceItem["item"],
                "description" => $invoiceItem["description"],
                "unit_price" => $invoiceItem["unit_price"],
                "tax1" => $taxes[$invoiceItem["tax1"]] ?? "",
                "tax2" => $taxes[$invoiceItem["tax2"]] ?? "",
                "quantity" => $invoiceItem["quantity"],
            ];

            if (isset($invoiceItem['discount'])) {
                $invoiceItems[$key]['discount'] = $invoiceItem['discount'];
                $invoiceItems[$key]['discount_type'] = $invoiceItem['discount_type'];
            }

            if (isset($product['status']) && $product['status'] == 'OK') {
                $invoiceItems[$key]['product_id'] = $product['id'];
            }
        }

        $invoice["PurchaseOrderItem"] = $invoiceItems;

        try {
            $request = $this->initHttpClient()->post("purchase_invoices", [
                "query" => ["send" => "draft"],
                "json" => $invoice,
            ]);

            $response = json_decode($request->getBody()->getContents(), true);

            $response['status'] = 'OK';

            return $response;
        } catch (BadResponseException $e) {
            return [
                'status' => 'ERR',
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * Perform a API call to insert an Invoice resource.
     *
     * @param array $trackerInvoice
     * @param array $taxes
     * @param int $clientId
     *
     * @return array
     */
    public function insertSalesInvoice(
        array $trackerInvoice,
        array $taxes,
        int $clientId
    ): array {
        $invoice = [];

        $invoice["Invoice"] = [
            "site_id" => $this->siteId,
            "client_id" => $clientId,
            "store_id" => 1,
            "type" => 0,
            "currency_code" => $trackerInvoice["Invoice"]["currency_code"],
            "draft" => 1,
            "date" => $trackerInvoice["Invoice"]["date"],
            "discount" => $trackerInvoice["Invoice"]["discount"] ?? "",
            "shipping_options" => $trackerInvoice["Invoice"]["shipping_options"] ?? "",
            "shipping_amount" => $trackerInvoice["Invoice"]["shipping_amount"] ?? "",
        ];

        $invoiceItems = [];
        foreach ($trackerInvoice["InvoiceItem"] as $key => $invoiceItem) {

            $product = $this->syncProduct($invoiceItem, $taxes, 'sales');

            $invoiceItems[$key] = [
                "item" => $invoiceItem["item"],
                "description" => $invoiceItem["description"],
                "unit_price" => $invoiceItem["unit_price"],
                "tax1" => $taxes[$invoiceItem["tax1"]] ?? "",
                "tax2" => $taxes[$invoiceItem["tax2"]] ?? "",
                "quantity" => $invoiceItem["quantity"],
            ];

            if (isset($invoiceItem['discount'])) {
                $invoiceItems[$key]['discount'] = $invoiceItem['discount'];
                $invoiceItems[$key]['discount_type'] = $invoiceItem['discount_type'];
            }

            if (isset($product['status']) && $product['status'] == 'OK') {
                $invoiceItems[$key]['product_id'] = $product['id'];
            }
        }

        $invoice["InvoiceItem"] = $invoiceItems;

        try {
            $request = $this->initHttpClient()->post("invoices", [
                "query" => ["send" => "draft"],
                "json" => $invoice,
            ]);

            $response = json_decode($request->getBody()->getContents(), true);

            $response['status'] = 'OK';

            return $response;
        } catch (BadResponseException $e) {
            return [
                'status' => 'ERR',
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * Check if a product is already exists
     * Otherwise insert it
     *
     * @param array $product
     * @param array $taxes
     * @param string $type
     */
    private function syncProduct(
        array $product,
        array $taxes,
        string $type
    ): array {
        $productsModel = GetObjectOrLoadModel('Product');

        if ($type == 'sales') {
            $untiPrice = 0;
            $buyPrice = $product['unit_price'];
        } else {
            $untiPrice = $product['unit_price'];
            $buyPrice = 0;
        }

        $daftraProduct = $productsModel->find('first', [
            'conditions' => [
                'Product.name' => $product['item'],
                'Product.unit_price' => $untiPrice,
                // 'Product.tax1' => $taxes[$product['tax1']] ?? '',
                // 'Product.tax2' => $taxes[$product['tax2']] ?? '',
            ],
        ]);

        if (isset($daftraProduct['Product'])) {
            return [
                'status' => 'OK',
                'id' => $daftraProduct['Product']['id']
            ];
        }

        try {
            $request = $this->initHttpClient()->post('products', [
                'json' => [
                    'Product' => [
                        'name' => $product['item'],
                        'description' => $product['description'],
                        'unit_price' => $untiPrice,
                        'buy_price' => $buyPrice,
                        'tax1' => $taxes[$product['tax1']] ?? '',
                        'tax2' => $taxes[$product['tax2']] ?? '',
                        'discount' => $product['discount'] ?? '',
                        'discount_type' => $product['discount_type'] ?? '',
                        'type' => $product['Product']['type'] ?? '1',
                        'track_stock' => $product['Product']['track_stock'] ?? 0
                    ],
                ],
            ]);

            $response = json_decode($request->getBody()->getContents(), true);

            $response['status'] = 'OK';

            return $response;
        } catch (BadResponseException $e) {
            return [
                'status' => 'ERR',
                'errors' => [$e->getMessage()],
            ];
        } 
    }

    /**
     * Enable Sales & Inventory plugins.
     *
     * @param array $plugins
     *
     * @return void
     */
    public function enableRequiredPlugins(array $plugins): void
    {
        $site = GetObjectOrLoadModel('Site');
        foreach ($plugins as $plugin) {
            $site->owner_update_plugin(
                $plugin,
                "true",
                $plugin == PURCHASE_CYCLE_PLUGIN ? true : false
            );
        }
    }

    /**
     * Check for a given client
     *
     * @param int $authId
     * @param string $model
     * @param int $siteId
     *
     * @return array|bool
     */
    public function checkForExistsUsers(
        int $authId,
        string $model,
        int $siteId
    ): array|bool {
        $model = $this->invoiceReferralModel->find("first", [
            "conditions" => [
                'JSON_EXTRACT(invoice, "$.Client.id")' => $authId,
                'SalesInvoicesReferral.model' => $model,
                'SalesInvoicesReferral.source_site_id' => $siteId,
            ],
        ]);

        return $model["SalesInvoicesReferral"] ?? false;
    }
}
