<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 02:26 م
 */

namespace App\Services\Menu\Filter;



class PrintableTemplate extends MenuFilterAbstract
{
    public function canShow()
    {
        $plugins = [
            ifPluginActive(SalesPlugin), ifPluginActive(WorkOrderPlugin), ifPluginActive(InventoryPlugin),
            ifPluginActive(FollowupPlugin), ifPluginActive(SalesPlugin), ifPluginActive(ClientsPlugin),
            ifPluginActive(AccountingPlugin), ifPluginActive(BookingPlugin), ifPluginActive(PrescriptionPlugin)
        ];
        return in_array(true, $plugins);
    }

}