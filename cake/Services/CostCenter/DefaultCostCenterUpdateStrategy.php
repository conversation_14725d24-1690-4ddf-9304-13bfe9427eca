<?php

namespace App\Services\CostCenter;


class DefaultCostCenterUpdateStrategy implements CostCenterUpdateStrategyInterface {

    public function __construct(
        private $cctModel,
        private $calculateAccountTotalsFn,
        private $getDeletedTransactionsFn,
        private $getSortedTransactionsByIdFn,
        private $recalculatePerTransactionFn,
        private $recalculateFn
    ) {}

    public function updateCostCenters(array $journal): void {
        $accountTotals = call_user_func($this->calculateAccountTotalsFn, $journal);
        $costCenterTransactions = $this->cctModel->getJournalCostTransaction($journal['Journal']['id']);
        $deletedCCTIds = call_user_func($this->getDeletedTransactionsFn, $costCenterTransactions, array_keys($accountTotals));

        $isNewCostCenterMode = !(
            isset($costCenterTransactions[0]['CostCenterTransaction']) &&
            !isset($costCenterTransactions[0]['CostCenterTransaction']['journal_transaction_id'])
        );

        $updatedTransactions = $isNewCostCenterMode
            ? call_user_func($this->recalculatePerTransactionFn, $costCenterTransactions, call_user_func($this->getSortedTransactionsByIdFn, $journal))
            : call_user_func($this->recalculateFn, $costCenterTransactions, $accountTotals);
        
        foreach ($updatedTransactions as $updatedTransaction) {
            $this->cctModel->id = $updatedTransaction['id'];
            $this->cctModel->save($updatedTransaction);
        }

        if (!empty($deletedCCTIds)) {
            $this->cctModel->deleteAll(['CostCenterTransaction.id' => $deletedCCTIds]);
        }
    }
}
