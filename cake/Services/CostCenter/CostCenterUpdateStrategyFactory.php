<?php

namespace App\Services\CostCenter;

class CostCenterUpdateStrategyFactory
{
    public static function create($journal, $cctModel, $calcFn, $deletedFn, $sortFn, $recalcPerTxnFn, $recalcFn)
    {
        $defaultStrategy = new DefaultCostCenterUpdateStrategy(
            $cctModel,
            $calcFn,
            $deletedFn,
            $sortFn,
            $recalcPerTxnFn,
            $recalcFn
        );

        return match ($journal['Journal']['entity_type']) {
            \Journal::JOURNAL_ENTITY_TYPE_PURCHASE_ORDER =>
            new PurchaseOrderCostCenterUpdateStrategy($defaultStrategy),
            default => $defaultStrategy
        };
    }
}
