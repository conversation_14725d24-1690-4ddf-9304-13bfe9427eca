<?php


namespace App\Services\CostCenter;


class CostCenterFacade
{
    private static $bindings = [];

    public static function updateJournalCostCenters($journalId) {
        if(!isset(self::$bindings['JCCU'])) {
            self::$bindings['JCCU'] =  new JournalCostCenterUpdater(
                GetObjectOrLoadModel('Journal'),
                GetObjectOrLoadModel('CostCenterTransaction')
            );
        }
        $journalCostCenterUpdater = self::$bindings['JCCU'];
        /**
         * @var $journalCostCenterUpdater JournalCostCenterUpdater
         */
        $journalCostCenterUpdater->update($journalId);
    }

}