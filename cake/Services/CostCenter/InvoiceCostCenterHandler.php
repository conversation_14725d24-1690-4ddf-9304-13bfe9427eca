<?php

namespace App\Services\CostCenter;


class InvoiceCostCenterHandler extends CostCenterUpdatableAbstract
{
    protected $Invoice;
    private $is_discount_allowed;

    public function __construct($Invoice)
    {
        $this->Invoice = $Invoice;
        $is_discount_allowed = \settings::getValue(AccountingPlugin , "discount_allowed_accounts_routing");
        $this->is_discount_allowed = (!empty($is_discount_allowed) && $is_discount_allowed!=\Settings::CANCEL_ACCOUNTS_ROUTING);
    }

    public function getEntity(){
        return $this->Invoice;
    }

    public function findById($id): array
    {
        return $this->Invoice->findById($id);
    }

    public function getJournalId($id, $entity_type = false): int
    {
        $journal = $this->Invoice->getJournal($id, $entity_type);
        return $journal['Journal']['id'];
    }

    public function getJournalTransactions($id, $entity_type = false): array
    {
        $journal = $this->Invoice->getJournal($id, $entity_type);
        return $journal['JournalTransaction'];
    }

    public function getEntityItems(array $entity): array
    {
        return $entity['InvoiceItem'];
    }

    public function getCurrencyCode(array $entity): string
    {
        return $entity['Invoice']['currency_code'];
    }
    

    public function getCostCenterAmount(array $entity, array $item): float
    {
        $taxes = $entity['InvoiceTax'] ?? [];
        $quantity = (float)($item['quantity'] ?? 0);
        $unitPrice = (float)($item['unit_price'] ?? 0);
        $calculatedDiscount = (float)($item['calculated_discount'] ?? 0);

        // Only apply discount if it's NOT allowed (as per original logic)
        $discount = $this->is_discount_allowed ? 0 : $calculatedDiscount;

        // Initial net subtotal
        $netSubtotal = ($unitPrice * $quantity) - $discount;

        // Map taxes by ID for quick lookup
        $filteredTaxes = [];
        foreach ($taxes as $tax) {
            if (isset($tax['tax_id'])) {
                $filteredTaxes[$tax['tax_id']] = $tax;
            }
        }

        // Exclude included taxes from netSubtotal
        foreach (['tax1', 'tax2'] as $taxKey) {
            $taxId = $item[$taxKey] ?? null;
            if ($taxId && !empty($filteredTaxes[$taxId]) && $filteredTaxes[$taxId]['included']) {
                $summaryTaxKey = $taxKey === 'tax1' ? 'summary_tax1' : 'summary_tax2';
                $netSubtotal -= (float)($item[$summaryTaxKey] ?? 0);
            }
        }

        return $netSubtotal;
    }




    public function getTransactionKeyPrefix(array $item): string
    { 
        return 'product_sales_' . $item['sales_account_id'];
    }

    public function getFallbackTransactionKey(): string
    {
        return 'sales';
    }

}
