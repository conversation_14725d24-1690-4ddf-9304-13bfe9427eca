<?php

namespace App\Services\CostCenter;

use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class CostCenterAssigner
{
    protected $CostCenter;
    protected $Journal;

    public function __construct()
    {
        $this->CostCenter = GetObjectOrLoadModel('CostCenter');
        $this->Journal    = GetObjectOrLoadModel('Journal');
    }

    /**
     * Mirrors the controller's POST block logic without renaming original variables.
     *
     * @param int    $journal_id
     * @param int    $account_id
     * @param int    $journal_transaction_id
     * @param array  $cost_center_transactions     // from $this->data['CostCenterTransaction']
     * @param float  $total_transaction_amount
     * @param string $default_currency
     * @param bool   $is_iframe                    // pass: isset($_POST['box']) && $_POST['box']
     * @return array{
     *   result: bool,
     *   message?: string|null,
     *   errors: array<int,string>,
     *   post_action: 'iframe'|'redirect'|'none',
     *   deleted_ids: array<int,int>
     * }
     */
    public function assignCostCentersToJournalTransaction(
        $journal_id,
        $journal_transaction_id,
        $account_id,
        $cost_center_transactions,
        $is_iframe = false
    ) {

        $default_currency = $this->Journal->get_default_currency();

        $errors = [];
        $deleted_ids = [];

        $currentCostCenters = $this->CostCenter->CostCenterTransaction->getCostCenterTransactionsByJtransactionId($journal_transaction_id);

        $costCenterTransactionList = $this->fillCostCenterTransactionData($journal_id, $account_id, $journal_transaction_id, $default_currency, $cost_center_transactions);

        $data = $costCenterTransactionList['costCenterTransactionData'];
        $oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL, $journal_id, 3);
        $to_delete = $costCenterTransactionList['to_delete'];

        if (!empty($to_delete)) {
            $this->CostCenter->CostCenterTransaction->deleteAll(['CostCenterTransaction.id' => $to_delete]);
            $deleted_ids = array_values($to_delete);
        }

        // sum + tolerance exactly as original
        $sum = array_reduce($data, function ($sum, $item) {
            $sum += (float)$item["CostCenterTransaction"]['credit'] + (float)$item["CostCenterTransaction"]['debit'];
            return $sum;
        }, 0);

        $journal = $this->Journal->findByid($journal_id);


        $currentTransaction = null;
        foreach ($journal['JournalTransaction'] as $transaction) {
            if ($transaction['id'] == $journal_transaction_id) {
                $currentTransaction = $transaction;
                break;
            }
        }


        $total_transaction_amount = $currentTransaction['credit'] > $currentTransaction['debit'] ? $currentTransaction['credit'] : $currentTransaction['debit'];

        if ($total_transaction_amount + 0.009 < $sum) {
            return [
                'result' => false,
                'message' => __("Cost Center Transactions Total Amount Can't Exceed Account Transaction Amount", true),
                'errors' => $errors,
                'post_action' => 'none',
                'deleted_ids' => $deleted_ids,
            ];
        }

        // empty cost centers check exactly as original
        $emptyCostCenters = array_filter($data, function($costCenterTransaction) {
            return !isset($costCenterTransaction['CostCenterTransaction']['cost_center_id']) || $costCenterTransaction['CostCenterTransaction']['cost_center_id'] == "";
        });

        if (!empty($emptyCostCenters)) {
            $errors[] = __("Please select valid cost center", true);
            return [
                'result' => false,
                'message' => end($errors) ?: null,
                'errors' => $errors,
                'post_action' => 'none',
                'deleted_ids' => $deleted_ids,
            ];
        }

        // save exactly as original naming
        $result = $this->CostCenter->save_manual_cost_transactions($data);

        // delete removed-by-user diff (same behavior/variables)
        if ($currentCostCenters) {
            $deletedCostCenterTransaction = array_diff(array_values($currentCostCenters), array_values($costCenterTransactionList['costCenterTransactionIds']));
            if (!empty($deletedCostCenterTransaction)) {
                $this->CostCenter->CostCenterTransaction->deleteAll(['CostCenterTransaction.id' => $deletedCostCenterTransaction]);
                $deleted_ids = array_values(array_unique(array_merge($deleted_ids, $deletedCostCenterTransaction)));
            }
        }

        // if empty data, delete all by journal_id (unchanged)
        if (empty($data)) {
            $this->CostCenter->CostCenterTransaction->deleteAll(['CostCenterTransaction.journal_id' => $journal_id]);
            // keep $result as true here, matching the successful path of "nothing to save"
            $result = true;
        }

        // activity log (same variable names as original)
        if ($result) {
            $oldData = $oldData->toArray();
            $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL, $journal_id, 3)->toArray();
            try {
                $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::JOURNAL);
                $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
                $requests = $activityLogRequestCreator->create($st, $newData, $oldData);

                $activityLogService = new \App\Services\ActivityLogService();
                foreach ($requests as $requestObj) {
                    $activityLogService->addActivity($requestObj);
                }
            } catch (\Throwable $e) {
                // per requirement (2): logging failure makes the whole operation fail
                $errors[] = __('Failed to write activity log.', true);
                return [
                    'result' => false,
                    'message' => end($errors) ?: null,
                    'errors' => $errors,
                    'post_action' => 'none',
                    'deleted_ids' => $deleted_ids,
                ];
            }
        }

        if ($result) {
            return [
                'result' => true,
                'message' => null, // controller shows its standard success flash
                'errors' => [],
                'post_action' => $is_iframe ? 'iframe' : 'redirect',
                'deleted_ids' => $deleted_ids,
            ];
        }

        // match original failure message text/shape
        $errors[] = sprintf(__('The %s could not be saved. Please, try again', true), __('cost centers', true));
        return [
            'result' => false,
            'message' => end($errors) ?: null,
            'errors' => $errors,
            'post_action' => 'none',
            'deleted_ids' => $deleted_ids,
        ];
    }
    
    function fillCostCenterTransactionData($journal_id, $account_id, $journal_transaction_id, $default_currency, $cost_center_transactions) {
        $data = [];
        $to_delete = [];
        $ids = [];
        foreach ($cost_center_transactions as $k => $cost_center_transaction) {
            if ($cost_center_transaction['percentage']==0) {
                $to_delete[]=$cost_center_transaction['id'];
                continue;
            }
            $data[$k]['CostCenterTransaction'] = $cost_center_transaction;
            $data[$k]['CostCenterTransaction']['journal_id'] = $journal_id;
            $data[$k]['CostCenterTransaction']['journal_account_id'] = $account_id;
            $data[$k]['CostCenterTransaction']['journal_transaction_id'] = $journal_transaction_id;
            $data[$k]['CostCenterTransaction']['is_auto'] = false;
            $data[$k]['CostCenterTransaction']['currency_code'] = $default_currency;
            $data[$k]['CostCenterTransaction']['rate'] = 1;
            $ids[$cost_center_transaction['id']] = $cost_center_transaction['id'] ?? false;
        }

        return ['costCenterTransactionData' => $data, 'to_delete' => $to_delete, 'costCenterTransactionIds' => $ids];
    }
}
