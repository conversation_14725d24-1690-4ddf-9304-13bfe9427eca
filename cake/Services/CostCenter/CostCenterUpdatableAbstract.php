<?php


namespace App\Services\CostCenter;


abstract class CostCenterUpdatableAbstract
{
    abstract public function findById($id): array;

    abstract public function getJournalId($id, $entity_type = false): ?int;

    abstract public function getJournalTransactions($id, $entity_type = false): array;

    abstract public function getEntityItems(array $entity): array;

    abstract public function getCurrencyCode(array $entity): string;

    abstract public function getTransactionKeyPrefix(array $item): string;

    abstract public function getFallbackTransactionKey(): string;

    public function hasFallbackPrefix() : bool {
        return false;
    }

    public function getFallbackPrefix($fallbackKey, $item) : string {
        return '';
    }

    public function getCostCenterAmount(array $entity, array $item): float
    {
         return $item['subtotal'] - (($item['summary_tax1'] ?? 0) + ($item['summary_tax2'] ?? 0)); 
    }

}
