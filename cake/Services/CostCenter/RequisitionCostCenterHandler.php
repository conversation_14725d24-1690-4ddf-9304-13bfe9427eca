<?php

namespace App\Services\CostCenter;

class RequisitionCostCenterHandler extends CostCenterUpdatableAbstract
{
    protected $Invoice;

    public function __construct($Invoice)
    {
        $this->Requisition = $Invoice;
    }

    public function findById($id): array
    {
        return $this->Requisition->findById($id);
    }

    public function getJournalId($id, $entity_type = false): ?int
    {
        $journal = $this->Requisition->getJournal($id, $entity_type);
        return $journal['Journal']['id'];
    }

    public function getJournalTransactions($id, $entity_type = false): array
    {
        $journal = $this->Requisition->getJournal($id, $entity_type);
        return $journal['JournalTransaction'];
    }

    public function getEntityItems(array $entity): array
    {
        return $entity['RequisitionItem'];
    }

    public function getCurrencyCode(array $entity): string
    {
        return $entity['Requisition']['currency_code'];
    }

    public function getTransactionKeyPrefix(array $item): string
    {
        return 'requisition_item_' . $item['product_id'] . '_' . $item['id'];
    }

    public function getFallbackTransactionKey(): string
    {
        return 'account';
    }

}
