<?php

namespace App\Services\CostCenter;

use Izam\Daftra\Common\Utils\SettingsUtil;

class PurchaseOrderCostCenterUpdateStrategy implements CostCenterUpdateStrategyInterface {

    public function __construct(
        private DefaultCostCenterUpdateStrategy $defaultStrategy
    ) {}

    public function updateCostCenters(array $journal): void {
        $enable = \Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);

        if ($enable) {
            $CostCenter = GetObjectOrLoadModel('CostCenter');
            $CostCenterTransaction = GetObjectOrLoadModel('CostCenterTransaction');
            $PurchaseOrder = GetObjectOrLoadModel('PurchaseOrder');

            $costCenterService = new PurchaseInvoiceCostCenterHandler($PurchaseOrder);
            $service = new CostCenterUpdaterService(
                $costCenterService,
                $CostCenter,
                $CostCenterTransaction
            );
            $service->handleCostCenters($journal['Journal']['entity_id']);
        } else {
            // fallback to normal recalculation
            $this->defaultStrategy->updateCostCenters($journal);
        }
    }
}
