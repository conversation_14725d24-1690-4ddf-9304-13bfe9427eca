<?php

namespace App\Services\CostCenter;


class PurchaseInvoiceCostCenterHandler extends CostCenterUpdatableAbstract
{
    protected $PurchaseOrder;

    public function __construct($PurchaseOrder)
    {
        $this->PurchaseOrder = $PurchaseOrder;
    }

    public function findById($id): array
    {
        $this->PurchaseOrder->recursive = 2;
        return $this->PurchaseOrder->findById($id);
    }

    public function getJournalId($id, $entity_type = false): ?int
    {
        $journal = $this->PurchaseOrder->getJournal($id, $entity_type);
        return $journal['Journal']['id'];
    }

    public function getJournalTransactions($id, $entity_type = false): array
    {
        $journal = $this->PurchaseOrder->getJournal($id, $entity_type);
        return $journal['JournalTransaction'];
    }

    public function getEntityItems(array $entity): array
    {
        return $entity['PurchaseOrderItem'];
    }

    public function getCurrencyCode(array $entity): string
    {
        return $entity['PurchaseOrder']['currency_code'];
    }


    public function hasFallbackPrefix() : bool {
        return true;
    }

    public function getFallbackPrefix($fallbackKey, $item) : string {
       return $fallbackKey.'_store_id_'.$item['store_id'];
    }


    public function getTransactionKeyPrefix(array $item): string
    {
        if(isset($item['cost_center_id']) && $item['cost_center_id'] > 0){
            if(isset($item['sales_cost_account_id']) && $item['sales_cost_account_id'] > 0){
                return 'purchase_invoice_product_sales_product_id_' . $item['product_id'] .'_'. $item['sales_cost_account_id']. '_store_id_' . $item['store_id'];
            }
        }

        return 'product_sales_cost_' . $item['product_id'] . '_' . $item['id'];
    }

    public function getFallbackTransactionKey(): string
    {
        return 'purchases';
    }

}
