<?php

namespace App\Services\CostCenter;


class CostCenterUpdaterService
{
    protected CostCenterUpdatableAbstract $entityHandler;
    protected $CostCenter;
    protected $CostCenterTransaction;

    public function __construct(
        CostCenterUpdatableAbstract $entityHandler,
        $CostCenter,
        $CostCenterTransaction
    ) {
        $this->entityHandler = $entityHandler;
        $this->CostCenter = $CostCenter;
        $this->CostCenterTransaction = $CostCenterTransaction;
    }

    public function handleCostCenters(int $entityId,  $entityType = false): void
    {
        $entity = $this->entityHandler->findById($entityId);
        $journalId = $this->entityHandler->getJournalId($entityId, $entityType);
        if(!$journalId) {
            return;
        }

        
        $transactions = $this->entityHandler->getJournalTransactions($entityId, $entityType);

        $journalTransactionsByKey = $this->indexJournalTransactions($transactions);
        $costCenterTransactions = [];
        $sharedCostCenterData = [];
        foreach ($this->entityHandler->getEntityItems($entity) as $item) {

            if (empty($item['cost_center_id'])) {
                continue;
            }
 
            $transactionKey = $this->entityHandler->getTransactionKeyPrefix($item);
            if (isset($journalTransactionsByKey[$transactionKey])) {
                $transaction = $journalTransactionsByKey[$transactionKey];
                $this->deleteCostCenterTransactions($transaction['id']);
                $amount = $this->entityHandler->getCostCenterAmount($entity, $item);
                $percentage = 0;
                $totalTransactionAmount = $transaction['currency_credit'] > 0 ? $transaction['currency_credit'] : $transaction['currency_debit'];
                if ($totalTransactionAmount != 0) {
                    $percentage = ($amount / $totalTransactionAmount) * 100;
                }
                $costCenterTransactions[] = $this->buildCostCenterTransaction(
                    $item['cost_center_id'],
                    $transaction,
                    $this->entityHandler->getCurrencyCode($entity),
                    $journalId,
                    $percentage,
                    false
                );
            } else {
                $fallbackKey = $this->entityHandler->getFallbackTransactionKey();
                $hasFallbackPrefix = $this->entityHandler->hasFallbackPrefix();
                $fallbackIndex = $fallbackKey;
                if($hasFallbackPrefix){
                  $fallbackIndex = $this->entityHandler->getFallbackPrefix($fallbackKey, $item);
                }
                if (isset($journalTransactionsByKey[$fallbackIndex])) {
                    $fallbackTransaction = $journalTransactionsByKey[$fallbackIndex];
                    $this->deleteCostCenterTransactions($fallbackTransaction['id']);
                    $sharedCostCenterData[] = [
                        'cost_center_id' => $item['cost_center_id'],
                        'transaction' => $fallbackTransaction,
                        'cost_center_proportion' =>  $this->entityHandler->getCostCenterAmount($entity, $item)  
                    ];
                }
           }
        }

        $costCenterTransactions = array_merge(
            $costCenterTransactions,
            $this->buildSharedCostCenterTransactions(
                $sharedCostCenterData,
                $this->entityHandler->getCurrencyCode($entity),
                $journalId
            )
        );

        $this->CostCenter->save_manual_cost_transactions($costCenterTransactions);
    }

    private function indexJournalTransactions(array $transactions): array
    {
        $indexed = [];
        foreach ($transactions as $transaction) {
            $indexed[$transaction['subkey']] = $transaction;
        }
        return $indexed;
    }

    private function deleteCostCenterTransactions(int $journalTransactionId): void
    {
        $this->CostCenterTransaction->deleteAll([
            'CostCenterTransaction.journal_transaction_id' => $journalTransactionId
        ]);
    }

    private function buildCostCenterTransaction(
        int $costCenterId,
        array $transaction,
        string $currencyCode,
        int $journalId,
        float $percentage,
        bool $isAuto = false
    ): array {
        $type = $transaction['debit'] > 0 ? 'debit' : 'credit';
        
        return [
            'cost_center_id' => $costCenterId,
            'journal_account_id' => $transaction['journal_account_id'],
            'currency_code' => $currencyCode,
            'journal_id' => $journalId,
            'journal_transaction_id' => $transaction['id'],
            'percentage' => $percentage,
            $type => ($percentage / 100) * $transaction[$type],
            'is_auto' => $isAuto,
        ];
    }

    private function buildSharedCostCenterTransactions(array $entries, string $currencyCode, int $journalId): array
    {
        $transactions = [];
        if (empty($entries)) {
            return [];
        }

        // Determine type once (assumes all transactions are same type: debit or credit)
        $firstTransaction = $entries[0]['transaction'];
        $type = $firstTransaction['debit'] > 0 ? 'debit' : 'credit';

        // Calculate total cost center proportion
        $totalProportion = $firstTransaction[$type];

        if ($totalProportion == 0.0) {
            return []; // Or handle zero proportion case if needed
        }

        foreach ($entries as $entry) {
            $transaction = $entry['transaction'];
            $proportion = (float)$entry['cost_center_proportion'];
            $percentage = (($proportion * $transaction['currency_rate']) / $totalProportion) * 100;
            $transactions[] = [
                'cost_center_id' => $entry['cost_center_id'],
                'journal_account_id' => $transaction['journal_account_id'],
                'currency_code' => $currencyCode,
                'journal_id' => $journalId,
                'journal_transaction_id' => $transaction['id'],
                'percentage' => $percentage,
                'currency_'.$type => $entry['cost_center_proportion'],
                $type => $entry['cost_center_proportion'] * $transaction['currency_rate'],
                'is_auto' => false,
            ];
        }

        return $transactions;
    }


}
