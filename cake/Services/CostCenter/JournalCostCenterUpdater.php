<?php


namespace App\Services\CostCenter;

use Izam\Daftra\Common\Utils\SettingsUtil;

/**
 * Class JournalCostCenterUpdater
 * @package App\Services\CostCenter
 * this class updates journal cost center transactions
 * by recalculating the amounts of every cost center transaction added
 * in the journal ,and remove cost center transactions of removed journal transaction
 */
class JournalCostCenterUpdater
{

    private $journalModel;
    private $cctModel;

    public function __construct($journalModel, $CCTModel)
    {
        $this->journalModel = $journalModel;
        $this->cctModel = $CCTModel;
    }

    public function calculateAccountTotals($journal) {
        $accountTotals = [];
        foreach ($journal['JournalTransaction'] as $transaction) {
            if(!isset($accountTotals[$transaction['journal_account_id']])) {
                $accountTotals[$transaction['journal_account_id']] = ['credit' => 0, 'debit' => 0, 'currency_debit' => 0, 'currency_credit' => 0];
            }
            $accountTotals[$transaction['journal_account_id']]['credit'] += $transaction['credit'];
            $accountTotals[$transaction['journal_account_id']]['debit'] += $transaction['debit'];
            $accountTotals[$transaction['journal_account_id']]['currency_debit'] += $transaction['currency_debit'];
            $accountTotals[$transaction['journal_account_id']]['currency_credit'] += $transaction['currency_credit'];
        }
        return $accountTotals;
    }

    public function getSortedTransactionsById($journal) {
        $transactions = [];
        foreach ($journal['JournalTransaction'] as $transaction) {
            $transactions[$transaction['id']] = $transaction;
        }
        return $transactions;
    }

    public function recalculateCCT($costCenterTransaction,$accountTotal) {
        return [
            'credit' => $accountTotal['credit'] * ($costCenterTransaction['percentage'] / 100),
            'debit' => $accountTotal['debit'] * ($costCenterTransaction['percentage'] / 100),
            'currency_debit' => $accountTotal['currency_debit'] * ($costCenterTransaction['percentage'] / 100),
            'currency_credit' => $accountTotal['currency_credit'] * ($costCenterTransaction['percentage'] / 100)
        ];
    }

    public function recalculate($costCenterTransactions, $accountTotals) {
        $updatedTransactions = [];
        foreach ($costCenterTransactions as $k => $costCenterTransaction) {
            $costCenterTransaction = $costCenterTransaction['CostCenterTransaction'];
            $accountId = $costCenterTransaction['journal_account_id'];
            if(!isset($accountTotals[$accountId])) {
                continue;
            }
            $accountTotal = $accountTotals[$accountId];
            $CCTAmounts = $this->recalculateCCT($costCenterTransaction, $accountTotal);
            $updatedTransactions[] = array_merge($costCenterTransaction, $CCTAmounts);
        }
        return $updatedTransactions;
    }

    public function recalculatePerTransaction($costCenterTransactions, $transactions) {
        $updatedTransactions = [];
        foreach ($costCenterTransactions as $k => $costCenterTransaction) {
            $costCenterTransaction = $costCenterTransaction['CostCenterTransaction'];
            $transactionId = $costCenterTransaction['journal_transaction_id'];
            if(!isset($transactions[$transactionId])) {
                continue;
            }
            $transaction = $transactions[$transactionId];
            $CCTAmounts = $this->recalculateCCT($costCenterTransaction, $transaction);
            $updatedTransactions[] = array_merge($costCenterTransaction, $CCTAmounts);
        }
        return $updatedTransactions;
    }

    public function update($journalId) {
        $journal = $this->journalModel->get_journal($journalId);

        $strategy = CostCenterUpdateStrategyFactory::create(
            $journal,
            $this->cctModel,
            [$this, 'calculateAccountTotals'],
            [$this, 'getDeletedTransactions'],
            [$this, 'getSortedTransactionsById'],
            [$this, 'recalculatePerTransaction'],
            [$this, 'recalculate']
        );

        $strategy->updateCostCenters($journal);
    }


    public function getDeletedTransactions($transactions, $accountIds) {
        $deletedCCTIds = [];
        foreach ($transactions as $k => $costCenterTransaction) {
            $costCenterTransaction = $costCenterTransaction['CostCenterTransaction'];
            $accountId = $costCenterTransaction['journal_account_id'];
            if(!in_array($accountId,$accountIds)) {
                $deletedCCTIds[] = $costCenterTransaction['id'];
            }
        }
        return $deletedCCTIds;
    }
}