<?php

namespace App\Services\WorkOrder;

class WorkOrderDeleter implements WorkOrderDeleterInterface
{
    private $url;

    private $label;
    
    /** @return bool*/
    public function delete($workOrders)
    {
        $ids = [];

        foreach ($workOrders as $workOrder) {
            $ids[] = $workOrder['WorkOrder']['id'];
        }

        $WorkOrderModel = GetObjectOrLoadModel('WorkOrder');

        if (!$WorkOrderModel->delete_with_related($ids)) {
            return false;
        }

        foreach ($workOrders as $workOrder) {
            $ActionLineModel = GetObjectOrLoadModel('ActionLine');
            $ActionLineModel->add_actionline(ACTION_DELETE_WORK_ORDER, [
                'primary_id' => $workOrder['WorkOrder']['client_id'],
                'secondary_id' => $workOrder['WorkOrder']['client_id'],
                'param2' => $workOrder['WorkOrder']['title'],
                'param3' => $workOrder['WorkOrder']['number']
            ]);
        }

        return true;
    }

    public function isDeletable($workOrder)
    {
        $WorkOrderModel = GetObjectOrLoadModel('WorkOrder');

        return $WorkOrderModel->deletable($workOrder);
    }

    public function setRedirectUrl($url)
    {
        $this->url = $url;
    }

    public function setLabel($label)
    {
        $this->label = $label;
    }

    public function getRedirectUrl()
    {
        return $this->url;
    }

    public function getLabel()
    {
        return $this->label;
    }
}
