<?php 

namespace App\Services\WorkOrder\WorkOrderTransactionAssigners;

use Exception;

class JournalTransactionAssigner implements TransactionAssignerInterface{

    protected $label;
    protected $type;



    public function __construct($type)
    {   
       $this->type = $type;
    }

    public function getLabel(){
        return $this->label;
    }

    public function getModelKey(){
        return 'Journal';
    }

    public function getCodeKey(){
        return 'number';
    }

    public function getSuggestions(){
        $journalModel = GetObjectOrLoadModel('Journal');
        $conditions['Journal.entity_type'] = '';
        $conditions['Journal.entity_id'] = 0;
        $conditions['Journal.is_automatic'] = false;
        $suggestions = $journalModel->find('all', array('limit' => 5, 'conditions' => $conditions , 'order' => 'Journal.id desc', 'recursive' => 1));
        $label = __('Journal', true);
        $this->label = $label;
        return $suggestions;
    }

    public function getAssigned($relatedId, $limit = 5){
        $label = __('Journal', true);
        $this->label = $label;
        $journalModel = GetObjectOrLoadModel('Journal');
        $result = $conditions = [];
        $conditions['Journal.entity_type'] = '';
        $conditions['Journal.is_automatic'] = false;
        if($relatedId) {
            $conditions['Journal.entity_id'] = $relatedId;
            $conditions['Journal.entity_type'] = 'work_order';
        } else {
            $conditions['Journal.entity_id'] = 0;
        }
        $suggestions = $journalModel->find('all', array('limit' => $limit, 'conditions' => $conditions , 'order' => 'Journal.id desc', 'recursive' => 1));
        return $suggestions;
    }

    public function getSearchResult($searchValue, $workOrderId = false){
        $journalModel = GetObjectOrLoadModel('Journal');
        $conditions = [];
        $conditions['Journal.entity_type'] = '';
        $conditions['Journal.entity_id'] = 0;
        $conditions['Journal.is_automatic'] = false;
        $conditions['OR'] = ['Journal.'.$this->getCodeKey() => $searchValue,'Journal.date LIKE "%'.$searchValue.'%"'];
        $invoices = $journalModel->find('all', array('limit' => 30, 'conditions' => $conditions , 'order' => 'Journal.date desc', 'recursive' => 1));
        $label = __('Journal', true);
        $this->label = $label;
        return $invoices;
    }

    public function assign($transactionId, $workflowId){
        $journalModel = GetObjectOrLoadModel('Journal');
        $conditions = [];
        $conditions['Journal.entity_type'] = '';
        $conditions['Journal.entity_id'] = 0;
        $conditions['Journal.is_automatic'] = false;   
        $conditions['Journal.id'] = $transactionId;
        $journal = $journalModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$journal) {
            throw new Exception('Invalid transaction');
        }

        $label = __('Journal', true);

        $journalModel->update($transactionId, ['entity_type' => 'work_order', 'id' => $transactionId,'entity_id' => $workflowId]);
        
        return ['assignedLabel' => $label.' #'.$journal['Journal'][$this->getCodeKey()]];
    }


    public function unassign($transactionId, $workflowId){
        $journalModel = GetObjectOrLoadModel('Journal');
        $conditions = [];
        $conditions['Journal.entity_id'] = $workflowId;
        $conditions['Journal.entity_type'] = 'work_order';
        $conditions['Journal.is_automatic'] = false;   
        $conditions['Journal.id'] = $transactionId;
        $journal = $journalModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$journal) {
            throw new Exception('Invalid transaction');
        }

        $label = __('Journal', true);

        $journalModel->update($transactionId, ['entity_type' => '', 'id' => $transactionId,'entity_id' => 0]);
        
        return ['unassignedLabel' => $label.' #'.$journal['Journal'][$this->getCodeKey()]];
    }
}
