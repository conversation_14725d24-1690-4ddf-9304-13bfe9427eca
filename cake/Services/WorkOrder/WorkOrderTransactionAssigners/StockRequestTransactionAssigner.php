<?php 

namespace App\Services\WorkOrder\WorkOrderTransactionAssigners;

use Exception;

class StockRequestTransactionAssigner implements TransactionAssignerInterface{

    protected $label;
    protected $type;
    private $isWorkFlow;


    public function __construct($type, $isWorkFlow = 1)
    {
        $this->isWorkFlow = $isWorkFlow;
        $this->type = $type;
    }

    public function getLabel(){
        return $this->label;
    }

    public function getModelKey(){
        return 'StockRequest';
    }

    public function getCodeKey(){
        return 'code';
    }

    public function getSuggestions(){
        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.work_flow_id'] = null;
        $conditions['StockRequest.work_order_id'] = null;
        $suggestions = $stockRequestModel->find('all', array('limit' => 5, 'conditions' => $conditions , 'order' => 'StockRequest.id desc', 'recursive' => -1));
        $label = __('Stock Request', true);
        $this->label = $label;
        return $suggestions;
    }

    public function getSearchResult($searchValue, $workOrderId = false){
        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.work_flow_id'] = null;
        $conditions['StockRequest.work_order_id'] = null;
        $dateValue = $searchValue;
        try {
            $dateValue = $this->getValidDate($dateValue) ?? $dateValue;
        } catch (\Throwable $th) {
        }
        $conditions['OR'] = ['StockRequest.'.$this->getCodeKey().' LIKE "%'.$searchValue.'%"', 'StockRequest.date LIKE "%'.$dateValue.'%"'];
        $results = $stockRequestModel->find('all', array('limit' => 30, 'conditions' => $conditions , 'order' => 'StockRequest.date desc', 'recursive' => -1));
        $label = __('Stock Request', true);
        $this->label = $label;
        return $results;
    }

    private function getValidDate($date, $format = 'd/m/Y') {
        $dateTime = \DateTime::createFromFormat($format, $date);
        if($dateTime && $dateTime->format($format) === $date){
            return $dateTime->format('Y-m-d');
        }
        return null;
    }

    public function assign($transactionId, $workflowId){
        return  $this->isWorkFlow ? 
                $this->assignWorkFlow($transactionId, $workflowId)
            :   $this->assignWorkOrder($transactionId, $workflowId) ;
    }

    private function assignWorkFlow($transactionId, $workflowId){
        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.id'] = $transactionId;
        $conditions['StockRequest.work_flow_id'] = null;
        $conditions['StockRequest.work_order_id'] = null;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$stockRequest) {
            throw new Exception('Invalid transaction');
        }
        $label = __('Stock Request', true);        
        $stockRequestModel->update($transactionId, ['work_flow_id' => $workflowId]);
        return ['assignedLabel' => $label.' #'.$stockRequest['StockRequest'][$this->getCodeKey()]];
    }

    public function getAssigned($relatedId, $limit = 30){
        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $keyName = $this->isWorkFlow ? "work_flow_id" : "work_order_id";
        $conditions["StockRequest.{$keyName}"] = null;
        if($relatedId) {
            $conditions["StockRequest.{$keyName}"] = $relatedId;
        }
        $results = $stockRequestModel->find('all', array('limit' => $limit, 'conditions' => $conditions , 'order' => 'StockRequest.date desc', 'recursive' => -1));
        $label = __('Stock Request', true);
        $this->label = $label;
        return $results;
    }

    private function assignWorkOrder($transactionId, $workflowId){
        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.id'] = $transactionId;
        $conditions['StockRequest.work_flow_id'] = null;
        $conditions['StockRequest.work_order_id'] = null;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$stockRequest) {
            throw new Exception('Invalid transaction');
        }
        $label = __('Stock Request', true);
        $stockRequestModel->update($transactionId, ['work_order_id' => $workflowId]);
        return ['assignedLabel' => $label.' #'.$stockRequest['StockRequest'][$this->getCodeKey()]];
    }

    
    public function unassign($transactionId, $workflowId){
        return  $this->isWorkFlow ? 
                $this->unassignWorkFlow($transactionId, $workflowId)
            :   $this->unassignWorkOrder($transactionId, $workflowId) ;
    }

    private function unassignWorkFlow($transactionId, $workflowId){

        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.id'] = $transactionId;
        $conditions['StockRequest.work_flow_id'] = $workflowId;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$stockRequest) {
            throw new Exception('Invalid transaction');
        }
        $label = __('Stock Request', true);        
        $stockRequestModel->update($transactionId, ['work_flow_id' => null]);
        return ['unassignedLabel' => $label.' #'.$stockRequest['StockRequest'][$this->getCodeKey()]];
    }

    private function unassignWorkOrder($transactionId, $workflowId){

        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.id'] = $transactionId;
        $conditions['StockRequest.work_order_id'] = $workflowId;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$stockRequest) {
            throw new Exception('Invalid transaction');
        }
        $label = __('Stock Request', true);
        $stockRequestModel->update($transactionId, ['work_order_id' => null]);
        return ['assignedLabel' => $label.' #'.$stockRequest['StockRequest'][$this->getCodeKey()]];
    }


}
