<?php

namespace App\Services\WorkOrder\WorkOrderTransactionAssigners;

interface TransactionAssignerInterface
{
    public function __construct($type);
    public function getLabel();
    public function getModelKey();
    public function getCodeKey();
    public function getSuggestions();
    public function getAssigned($relatedId, $limit = 30);
    public function getSearchResult($searchValue, $workOrderId = false);
    public function assign($transactionId, $workflowId);
    public function unassign($transactionId, $workflowId);

}
