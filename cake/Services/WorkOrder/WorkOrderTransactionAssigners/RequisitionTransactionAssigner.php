<?php 

namespace App\Services\WorkOrder\WorkOrderTransactionAssigners;

use Exception;
use Izam\Daftra\Common\Utils\RequisitionUtil;

class RequisitionTransactionAssigner implements TransactionAssignerInterface{

    protected $label;
    protected $type;

    protected $mapTypes = [
        'inbound_requisition'     => RequisitionUtil::TYPE_INBOUND,
        'outbound_requisition'    => RequisitionUtil::TYPE_OUTBOUND
    ];

    public function __construct($type)
    {   
        $this->type = $type;   
    }

    public function getLabel(){
        switch($this->type){
            case 'inbound_requisition':
                $label = __('Inbound Requisition', true);
                break;
            case 'outbound_requisition':
                $label = __('Outbound Requisition', true);
                break;       
        }
        return $label;
    }

    public function getModelKey(){
        return 'Requisition';
    }

    public function getCodeKey(){
        return 'number';
    }

    public function getSuggestions(){
        $requsitionModel = GetObjectOrLoadModel('Requisition');
        $conditions = [];
        $conditions[] = 'Requisition.work_order_id IS NULL';
        $conditions['Requisition.type'] = $this->mapTypes[$this->type];
        $suggestions = $requsitionModel->find('all', array('limit' => 5, 'conditions' => $conditions , 'order' => 'Requisition.id desc', 'recursive' => 1));
        $this->label = $this->getLabel();
        return $suggestions;
    }

    public function getSearchResult($searchValue, $workOrderId = false){
        $requsitionModel = GetObjectOrLoadModel('Requisition');
        $conditions = [];
        $conditions['Requisition.type'] = $this->mapTypes[$this->type];
        $conditions[] = 'Requisition.work_order_id IS NULL';
        $conditions['OR'] = ['Requisition.'.$this->getCodeKey() => $searchValue,'Requisition.date LIKE "%'.$searchValue.'%"'];
        $invoices = $requsitionModel->find('all', array('limit' => 30, 'conditions' => $conditions , 'order' => 'Requisition.date desc', 'recursive' => 1));
        $this->label = $this->getLabel();
        return $invoices;
    }

    public function getAssigned($relatedId, $limit = 30){
        $requsitionModel = GetObjectOrLoadModel('Requisition');
        $result = $conditions = [];
        $conditions['Requisition.type'] = $this->mapTypes[$this->type];
        if($relatedId) {
            $conditions['Requisition.work_order_id'] = $relatedId;
        }else {
            $conditions[] = 'Requisition.work_order_id IS NULL';
        }

        $requisitions = $requsitionModel->find('all', array('limit' => $limit, 'conditions' => $conditions , 'order' => 'Requisition.date desc', 'recursive' => 1));
        $this->label = $this->getLabel();        
        return $requisitions;
    }

    public function assign($transactionId, $workflowId){
        $invoiceModel = GetObjectOrLoadModel('Requisition');
        $conditions[] = 'Requisition.work_order_id IS NULL';
        $conditions['Requisition.id'] = $transactionId;
        $invoice = $invoiceModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $invoiceModel->update($transactionId, ['work_order_id' => $workflowId]);
        
        return ['assignedLabel' => $label.' #'.$invoice['Requisition'][$this->getCodeKey()]];
    }

    public function unassign($transactionId, $workflowId){
        $invoiceModel = GetObjectOrLoadModel('Requisition');
        $conditions['Requisition.work_order_id'] = $workflowId;
        $conditions['Requisition.id'] = $transactionId;
        $invoice = $invoiceModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $invoiceModel->update($transactionId, ['work_order_id' => null]);
        
        return ['unassignedLabel' => $label.' #'.$invoice['Requisition'][$this->getCodeKey()]];
    }

}
