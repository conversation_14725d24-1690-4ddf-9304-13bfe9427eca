<?php 

namespace App\Services\WorkOrder\WorkOrderTransactionAssigners;

use Exception;
use Invoice;

class InvoiceTransactionAssigner implements TransactionAssignerInterface{

    protected $label;
    protected $type;
    protected $mapTypes = [
        'invoice'     => Invoice::Invoice,
        'estimate'    => Invoice::Estimate,
        'sales_order' => Invoice::SALES_ORDER,
        'credit_note' => Invoice::Credit_Note
    ];

    public function __construct($type)
    {
        $this->type = $type;   
    }

    public function getLabel(){

        switch($this->mapTypes[$this->type]){
            case Invoice::Invoice:
                $label = __('Invoice', true);
                break;
            case Invoice::Estimate:
                $label = __('Estimate', true);
                break;
            case Invoice::SALES_ORDER:
                $label = __('Sales Order', true);
                break;
            case Invoice::Credit_Note:
                $label = __('Credit Note', true);
                break;
        }

        return $label;
    }

    public function getModelKey(){
        return 'Invoice';
    }

    public function getCodeKey(){
        return 'no';
    }

    public function getSuggestions(){
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $result = $conditions = [];
        $conditions[] = 'Invoice.work_order_id IS NULL';
        $conditions['Invoice.type'] = $this->mapTypes[$this->type];
        $suggestions = $invoiceModel->find('all', array('limit' => 5, 'conditions' => $conditions , 'order' => 'Invoice.id desc', 'recursive' => 1));
        return $suggestions;
    }

    public function getAssigned($relatedId, $limit = 5){
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $result = $conditions = [];
        if($relatedId) {
            $conditions['Invoice.work_order_id'] = $relatedId;
        } else {
            $conditions[] = 'Invoice.work_order_id IS NULL';
        }
        $conditions['Invoice.type'] = $this->mapTypes[$this->type];
        $suggestions = $invoiceModel->find('all', array('limit' => $limit, 'conditions' => $conditions , 'order' => 'Invoice.id desc', 'recursive' => 1));
        return $suggestions;
    }

    public function getSearchResult($searchValue, $workOrderId = false){
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $conditions = [];
        $conditions['Invoice.type'] = $this->mapTypes[$this->type];
        if($workOrderId) {
            $conditions['Invoice.work_order_id'] = $workOrderId;
        } else {
            $conditions[] = 'Invoice.work_order_id IS NULL';
        }
        $conditions['OR'] = ['Invoice.no' => $searchValue,'Invoice.date LIKE "%'.$searchValue.'%"'];
        $invoices = $invoiceModel->find('all', array('limit' => 30, 'conditions' => $conditions , 'order' => 'Invoice.date desc', 'recursive' => 1));
        return $invoices;
    }

    public function assign($transactionId, $workflowId){
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $conditions['Invoice.type'] = $this->mapTypes[$this->type];
        $conditions[] = 'Invoice.work_order_id IS NULL';
        $conditions['Invoice.id'] = $transactionId;
        $invoice = $invoiceModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $invoiceModel->update($transactionId, ['work_order_id' => $workflowId]);

        // if invoice has requisition then update requisition work order id
        $requisitionModel = GetObjectOrLoadModel('Requisition');
        $requisition = $requisitionModel->find( 'first', array('conditions' => ['Requisition.order_id' => $transactionId, 'Requisition.order_type' => 3], 'recursive' => -1));
        if($requisition) {
            $requisitionModel->update($requisition['Requisition']['id'], ['work_order_id' => $workflowId]);
        }

        return ['assignedLabel' => $label.' #'.$invoice['Invoice'][$this->getCodeKey()]];
    }

    public function unassign($transactionId, $workflowId){
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $conditions['Invoice.type'] = $this->mapTypes[$this->type];
        $conditions['Invoice.work_order_id'] = $workflowId;
        $conditions['Invoice.id'] = $transactionId;
        $invoice = $invoiceModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $invoiceModel->update($transactionId, ['work_order_id' => null]);
        
        return ['unassignedLabel' => $label.' #'.$invoice['Invoice'][$this->getCodeKey()]];
    }
}
