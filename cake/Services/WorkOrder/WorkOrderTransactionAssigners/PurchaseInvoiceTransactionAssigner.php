<?php 

namespace App\Services\WorkOrder\WorkOrderTransactionAssigners;

use Exception;
use PurchaseOrder;

class PurchaseInvoiceTransactionAssigner implements TransactionAssignerInterface{

    protected $label;
    protected $type;

    public function __construct($type)
    {
        $this->type = $type;   
    }

    public function getLabel(){
        return __('Purchase Invoice', true);
    }

    public function getModelKey(){
        return 'PurchaseOrder';
    }

    public function getCodeKey(){
        return 'no';
    }

    public function getSuggestions(){
        $purchasePurchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
        $result = $conditions = [];
        $conditions[] = 'PurchaseOrder.work_order_id IS NULL';
        $conditions['PurchaseOrder.type'] = PurchaseOrder::PURCHASE_INVOICE;
        $suggestions = $purchasePurchaseOrderModel->find('all', array('limit' => 5, 'conditions' => $conditions , 'order' => 'PurchaseOrder.id desc', 'recursive' => 1));
        return $suggestions;
    }

    public function getSearchResult($searchValue, $workOrderId = false){
        $invoiceModel = GetObjectOrLoadModel('PurchaseOrder');
        $conditions = [];
        $conditions['PurchaseOrder.type'] = PurchaseOrder::PURCHASE_INVOICE;
        $conditions[] = 'PurchaseOrder.work_order_id IS NULL';
        $conditions['OR'] = ['PurchaseOrder.no' => $searchValue,'PurchaseOrder.date LIKE "%'.$searchValue.'%"'];
        $invoices = $invoiceModel->find('all', array('limit' => 30, 'conditions' => $conditions , 'order' => [
                            "CASE 
                                WHEN PurchaseOrder.no = '{$searchValue}' THEN 1
                                WHEN PurchaseOrder.date LIKE '%{$searchValue}%' THEN 2
                                ELSE 3 END",
                            'PurchaseOrder.date DESC'
                        ], 'recursive' => 1));
        return $invoices;
    }

    public function getAssigned($relatedId, $limit = 30){
        $invoiceModel = GetObjectOrLoadModel('PurchaseOrder');
        $result = $conditions = [];
        $conditions['PurchaseOrder.type'] = PurchaseOrder::PURCHASE_INVOICE;
        if($relatedId) {
            $conditions['PurchaseOrder.work_order_id'] = $relatedId;
        } else {
            $conditions[] = 'PurchaseOrder.work_order_id IS NULL';
        }
        $invoices = $invoiceModel->find('all', array('limit' => $limit, 'conditions' => $conditions , 'order' => 'PurchaseOrder.date desc', 'recursive' => 1));
        return $invoices;
    }

    public function assign($transactionId, $workflowId){
        $invoiceModel = GetObjectOrLoadModel('PurchaseOrder');
        $conditions[] = 'PurchaseOrder.work_order_id IS NULL';
        $conditions['PurchaseOrder.id'] = $transactionId;
        $invoice = $invoiceModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $invoiceModel->update($transactionId, ['work_order_id' => $workflowId]);
        
        return ['assignedLabel' => $label.' #'.$invoice['PurchaseOrder'][$this->getCodeKey()]];
    }

    public function unassign($transactionId, $workflowId){
        $invoiceModel = GetObjectOrLoadModel('PurchaseOrder');
        $conditions['PurchaseOrder.work_order_id'] = $workflowId;
        $conditions['PurchaseOrder.id'] = $transactionId;
        $invoice = $invoiceModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $invoiceModel->update($transactionId, ['work_order_id' => null]);
        
        return ['unassignedLabel' => $label.' #'.$invoice['PurchaseOrder'][$this->getCodeKey()]];
    }

}
