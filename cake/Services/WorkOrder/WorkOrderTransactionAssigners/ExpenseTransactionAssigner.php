<?php 

namespace App\Services\WorkOrder\WorkOrderTransactionAssigners;

use Expense;

class ExpenseTransactionAssigner implements TransactionAssignerInterface{

    protected $label;
    protected $type;

    public function __construct($type)
    {
        $this->type = $type;   
    }

    public function getLabel(){

        switch($this->type){
            case 'income':
                $label = __('Income', true);
                break;
            case 'expense':
                $label = __('Expense', true);
                break;       
        }
        return $label;

    }

    public function getModelKey(){
        return 'Expense';
    }

    public function getCodeKey(){
        return 'expense_number';
    }

    public function getSuggestions(){
        $expenseModel = GetObjectOrLoadModel('Expense');
        $result = $conditions = [];
        $conditions[] = 'Expense.work_order_id IS NULL';
        $conditions['Expense.is_income'] = $this->type == "income" ? true : false;
        $suggestions = $expenseModel->find('all', array('limit' => 5, 'conditions' => $conditions , 'order' => 'Expense.id desc', 'recursive' => 1));
        $this->label = $this->getLabel();
        return $suggestions;
    }

    public function getSearchResult($searchValue, $workOrderId = false){
        $invoiceModel = GetObjectOrLoadModel('Expense');
        $conditions = [];
        $conditions['Expense.is_income'] = $this->type == "income" ? true : false;
        $conditions[] = 'Expense.work_order_id IS NULL';
        $conditions['OR'] = ['Expense.expense_number' => $searchValue,'Expense.date LIKE "%'.$searchValue.'%"'];
        $invoices = $invoiceModel->find('all', array('limit' => 30, 'conditions' => $conditions , 'order' => 'Expense.date desc', 'recursive' => 1));
        $this->label = $this->getLabel();
        return $invoices;
    }

    public function getAssigned($relatedId, $limit = 30){
        $expenseModel = GetObjectOrLoadModel('Expense');
        $result = $conditions = [];
        if($relatedId) {
            $conditions['Expense.work_order_id'] = $relatedId;
        } else {
            $conditions[] = 'Expense.work_order_id IS NULL';
        }
        $conditions['Expense.is_income'] = $this->type == "income" ? true : false;
        $expenses = $expenseModel->find('all', array('limit' => $limit, 'conditions' => $conditions , 'order' => 'Expense.date desc', 'recursive' => 1));
        return $expenses;
    }

    public function assign($transactionId, $workflowId){
        $expenseModel = GetObjectOrLoadModel('Expense');
        $conditions['Expense.is_income'] = $this->type == "income" ? true : false;
        $conditions[] = 'Expense.work_order_id IS NULL';
        $conditions['Expense.id'] = $transactionId;
        $expense = $expenseModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$expense) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $expenseModel->id = $transactionId;
        $expenseModel->saveField("work_order_id", $workflowId, false);
        
        return ['assignedLabel' => $label.' #'.$expense['Expense'][$this->getCodeKey()]];
    }

    public function unassign($transactionId, $workflowId){
        $expenseModel = GetObjectOrLoadModel('Expense');
        $conditions['Expense.is_income'] = $this->type == "income" ? true : false;
        $conditions['Expense.work_order_id'] = $workflowId;
        $conditions['Expense.id'] = $transactionId;
        $expense = $expenseModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if(!$expense) {
            throw new Exception('Invalid transaction');
        }

        $label = $this->getLabel();

        $expenseModel->id = $transactionId;
        $expenseModel->saveField("work_order_id", null, false);
        
        return ['unassignedLabel' => $label.' #'.$expense['Expense'][$this->getCodeKey()]];
    }

}
