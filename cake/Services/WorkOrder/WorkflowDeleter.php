<?php

namespace App\Services\WorkOrder;

use App\Events\Queue\Workflow\WorkflowDeletedEvent;
use App\Services\ActivityLogService;
use Izam\Daftra\ActivityLog\Requests\ActivityLogRequest;

class WorkflowDeleter implements WorkOrderDeleterInterface
{
    private $url;

    private $label;
    
    public function delete($workOrders)
    {
        $ids = [];
        
        foreach ($workOrders as $workOrder) {
            $ids[] = $workOrder['WorkOrder']['id'];
        }

        $WorkOrderModel = GetObjectOrLoadModel('WorkOrder');

        if (!$WorkOrderModel->delete_with_related($ids)) {
            return false;
        }

        foreach ($workOrders as $workOrder) {
            dispatch_event_action(new WorkflowDeletedEvent(json_encode($workOrder['WorkOrder'])));
            (new ActivityLogService())
                ->addActivity(
                    new ActivityLogRequest($workOrder['WorkOrder']['id'], getWorkFlowTypeEntityName($workOrder['WorkOrder']['workflow_type_id']), 'deleted', 'Workflow #' . $workOrder['WorkOrder']['id'], '', [], [])
                );
        }
        
        return true;
    }

    public function isDeletable($workOrder)
    {
        $WorkOrderModel = GetObjectOrLoadModel('WorkOrder');

        return $WorkOrderModel->canDeleteWorkflow($workOrder['WorkOrder']['id']);
    }

    public function setRedirectUrl($url)
    {
        $this->url = $url;
    }

    public function setLabel($label)
    {
        $this->label = $label;
    }

    public function getRedirectUrl()
    {
        return $this->url;
    }

    public function getLabel()
    {
        return $this->label;
    }
}