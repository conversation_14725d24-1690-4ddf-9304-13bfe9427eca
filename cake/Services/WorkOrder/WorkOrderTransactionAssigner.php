<?php

namespace App\Services\WorkOrder;

use App\Services\WorkOrder\WorkOrderTransactionAssigners\ExpenseTransactionAssigner;
use App\Services\WorkOrder\WorkOrderTransactionAssigners\InvoiceTransactionAssigner;
use App\Services\WorkOrder\WorkOrderTransactionAssigners\JournalTransactionAssigner;
use App\Services\WorkOrder\WorkOrderTransactionAssigners\PurchaseInvoiceTransactionAssigner;
use App\Services\WorkOrder\WorkOrderTransactionAssigners\RequisitionTransactionAssigner;
use App\Services\WorkOrder\WorkOrderTransactionAssigners\StockRequestTransactionAssigner;
use Exception;
use Invoice;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\RequisitionUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;

class WorkOrderTransactionAssigner{

    const TYPE_INVOICE = 'invoice';
    const TYPE_ESTIMATE = 'estimate';
    const TYPE_SALES_ORDER = 'sales_order';
    const TYPE_CREDITNOTE = 'credit_note';
    const TYPE_EXPENSE = 'expense';
    const TYPE_INCOME = 'income';
    const TYPE_PURCHASE_INVOICE = 'purchase_invoice';
    const TYPE_INBOUND_REQUISTION = 'inbound_requisition';
    const TYPE_OUTBOUND_REQUISTION = 'outbound_requisition';
    const TYPE_JOURNAL = 'journal';
    const TYPE_STOCK_REQUEST = 'stock_request';

    const TransactionObjMapper = [
        self::TYPE_INVOICE              => InvoiceTransactionAssigner::class,
        self::TYPE_ESTIMATE             => InvoiceTransactionAssigner::class,
        self::TYPE_SALES_ORDER          => InvoiceTransactionAssigner::class,
        self::TYPE_CREDITNOTE           => InvoiceTransactionAssigner::class,
        self::TYPE_EXPENSE              => ExpenseTransactionAssigner::class,
        self::TYPE_INCOME               => ExpenseTransactionAssigner::class,
        self::TYPE_PURCHASE_INVOICE     => PurchaseInvoiceTransactionAssigner::class,
        self::TYPE_INBOUND_REQUISTION   => RequisitionTransactionAssigner::class,
        self::TYPE_OUTBOUND_REQUISTION  => RequisitionTransactionAssigner::class,
        self::TYPE_JOURNAL              => JournalTransactionAssigner::class,
        self::TYPE_STOCK_REQUEST        => StockRequestTransactionAssigner::class,
    ];


    public static function getSuggestions($transactionType, $isWorkflow = 1) {
        $mappedObj = self::TransactionObjMapper[$transactionType];
        if(!isset($mappedObj)) {
            throw new Exception('Invalid work order assigner');
        }
        $instance =  (new $mappedObj($transactionType, $isWorkflow));
        return ['suggestions' => $instance->getSuggestions(),'label' => $instance->getLabel(),'modelKey' => $instance->getModelKey(),'code' => $instance->getCodeKey()];
        
    }

    public static function getAssigned($transactionType, $isWorkflow = 1, $relatedId = false) {
        $mappedObj = self::TransactionObjMapper[$transactionType];
        if(!isset($mappedObj)) {
            throw new Exception('Invalid work order assigner');
        }
        $instance =  (new $mappedObj($transactionType, $isWorkflow));
        return ['suggestions' => $instance->getAssigned($relatedId, 300),'label' => $instance->getLabel(),'modelKey' => $instance->getModelKey(),'code' => $instance->getCodeKey()];
        
    }

    public static function getSearchResult($transactionType, $searchValue, $isWorkflow = 1, $relatedId = false) {
        $mappedObj = self::TransactionObjMapper[$transactionType];
        if(!isset($mappedObj)) {
            throw new Exception('Invalid work order assigner');
        }
        $instance =  (new $mappedObj($transactionType, $isWorkflow));
        return ['result' => $instance->getSearchResult($searchValue, $relatedId),'label' => $instance->getLabel(),'modelKey' => $instance->getModelKey(),'code' => $instance->getCodeKey()];
        
    }

    public static function assignTransaction($transactionType, $transactionId, $workflowId, $isWorkflow = true) {
        $mappedObj = self::TransactionObjMapper[$transactionType];
        if(!isset($mappedObj)) {
            throw new Exception('Invalid work order assigner');
        }
        $instance =  (new $mappedObj($transactionType, $isWorkflow));
        return $instance->assign($transactionId, $workflowId);
        
    }

    public static function unassignTransaction($transactionType, $transactionId, $workflowId, $isWorkflow = true) {
        $mappedObj = self::TransactionObjMapper[$transactionType];
        if(!isset($mappedObj)) {
            throw new Exception('Invalid work order assigner');
        }
        $instance =  (new $mappedObj($transactionType, $isWorkflow));
        return $instance->unassign($transactionId, $workflowId);
        
    }

  
    public static function listOfTransactionsTypes(): array
    {
        $transactionList = [
            self::TYPE_INVOICE => __("Invoice", true),
            self::TYPE_ESTIMATE => __("Estimate", true),
        ];
        if (\settings::getValue(PluginUtil::SalesPlugin, SettingsUtil::ENABLE_SALES_ORDER)) {
            $transactionList[self::TYPE_SALES_ORDER] = __("Sales Order", true);
        }
        $transactionList = array_merge($transactionList, [
            self::TYPE_CREDITNOTE => __("Credit Note", true),
            self::TYPE_EXPENSE => __("Expense", true),
            self::TYPE_INCOME => __("Income", true),
            self::TYPE_PURCHASE_INVOICE => __("Purchase Invoice", true),
            self::TYPE_INBOUND_REQUISTION => __("Inbound Requisition", true),
            self::TYPE_OUTBOUND_REQUISTION => __("Outbound Requisition", true),
            self::TYPE_JOURNAL => __("Journal", true),
        ]);
        if(\settings::getValue(InventoryPlugin, \settings::ENABLE_STOCK_REQUESTS)) {
            $transactionList[self::TYPE_STOCK_REQUEST] = __("Stock Request", true);
        }

        return $transactionList;
    }


    public static function hasAnyTransactions($id) {
        //First check for invoices
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $conditions = [];
        $conditions['Invoice.work_order_id'] = $id;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if($invoice) {
            return true;
        }
        //Check for expenses or incomes
        $expenseModel = GetObjectOrLoadModel('Expense');
        $conditions = [];
        $conditions['Expense.work_order_id'] = $id;
        $expense = $expenseModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if($expense) {
            return true;
        }

        $invoiceModel = GetObjectOrLoadModel('Requisition');
        $conditions = [];
        $conditions['Requisition.work_order_id'] = $id;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if($invoice) {
            return true;
        }

        $invoiceModel = GetObjectOrLoadModel('PurchaseOrder');
        $conditions = [];
        $conditions['PurchaseOrder.work_order_id'] = $id;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if($invoice) {
            return true;
        }

        //Check for journals
        $journalModel = GetObjectOrLoadModel('Journal');
        $conditions = [];
        $conditions['Journal.entity_type'] = 'work_order';
        $conditions['Journal.entity_id'] = $id;
        $conditions['Journal.is_automatic'] = false;   
        $journal = $journalModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if($journal) {
            return true;
        }

        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.work_flow_id'] = $id;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if($stockRequest) {
            return true;
        }

        $conditions = [];
        $conditions['StockRequest.work_order_id'] = $id;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if($stockRequest) {
            return true;
        }
        
        return false;

    }

    public static function getCurrentTransactionTypesForEntity($id) {
        $listOfTransactionsTypes = self::listOfTransactionsTypes();
        //First check for invoices
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $conditions['Invoice.work_order_id'] = $id;
        $conditions['Invoice.type'] = Invoice::Invoice;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
           unset($listOfTransactionsTypes[self::TYPE_INVOICE]);
        }

        $conditions = [];
        $conditions['Invoice.work_order_id'] = $id;
        $conditions['Invoice.type'] = Invoice::Estimate;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
           unset($listOfTransactionsTypes[self::TYPE_ESTIMATE]);
        }

        $conditions = [];
        $conditions['Invoice.work_order_id'] = $id;
        $conditions['Invoice.type'] = Invoice::SALES_ORDER;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
           unset($listOfTransactionsTypes[self::TYPE_SALES_ORDER]);
        }

        $conditions = [];
        $conditions['Invoice.work_order_id'] = $id;
        $conditions['Invoice.type'] = Invoice::Credit_Note;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
           unset($listOfTransactionsTypes[self::TYPE_CREDITNOTE]);
        }

        //Check for expenses or incomes
        $expenseModel = GetObjectOrLoadModel('Expense');
        $conditions = [];
        $conditions['Expense.work_order_id'] = $id;
        $conditions['Expense.is_income'] = false;
        $expense = $expenseModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$expense) {
            unset($listOfTransactionsTypes[self::TYPE_EXPENSE]);
        }

        $expenseModel = GetObjectOrLoadModel('Expense');
        $conditions = [];
        $conditions['Expense.work_order_id'] = $id;
        $conditions['Expense.is_income'] = true;
        $expense = $expenseModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$expense) {
            unset($listOfTransactionsTypes[self::TYPE_INCOME]);
        }

        $invoiceModel = GetObjectOrLoadModel('PurchaseOrder');
        $conditions = [];
        $conditions['PurchaseOrder.work_order_id'] = $id;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            unset($listOfTransactionsTypes[self::TYPE_PURCHASE_INVOICE]);
        }

        $invoiceModel = GetObjectOrLoadModel('Requisition');
        $conditions = [];
        $conditions['Requisition.work_order_id'] = $id;
        $conditions['Requisition.type'] = RequisitionUtil::TYPE_INBOUND;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            unset($listOfTransactionsTypes[self::TYPE_INBOUND_REQUISTION]);
        }

        $invoiceModel = GetObjectOrLoadModel('Requisition');
        $conditions = [];
        $conditions['Requisition.work_order_id'] = $id;
        $conditions['Requisition.type'] = RequisitionUtil::TYPE_OUTBOUND;
        $invoice = $invoiceModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$invoice) {
            unset($listOfTransactionsTypes[self::TYPE_OUTBOUND_REQUISTION]);
        }

        //Check for journals
        $journalModel = GetObjectOrLoadModel('Journal');
        $conditions = [];
        $conditions['Journal.entity_type'] = 'work_order';
        $conditions['Journal.entity_id'] = $id;
        $conditions['Journal.is_automatic'] = false;   
        $journal = $journalModel->find('count', array('conditions' => $conditions, 'recursive' => -1));
        if(!$journal) {
            unset($listOfTransactionsTypes[self::TYPE_JOURNAL]);
        }

        $temp = $listOfTransactionsTypes[self::TYPE_STOCK_REQUEST];
        unset($listOfTransactionsTypes[self::TYPE_STOCK_REQUEST]);
        $stockRequestModel = GetObjectOrLoadModel('StockRequest');
        $conditions = [];
        $conditions['StockRequest.work_flow_id'] = $id;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if($stockRequest) {
            $listOfTransactionsTypes[self::TYPE_STOCK_REQUEST] = $temp;
        }

        $conditions = [];
        $conditions['StockRequest.work_order_id'] = $id;
        $stockRequest = $stockRequestModel->find('first', array('conditions' => $conditions, 'recursive' => -1));
        if($stockRequest) {
            $listOfTransactionsTypes[self::TYPE_STOCK_REQUEST] = $temp;
        }

        return $listOfTransactionsTypes;

    }

}
