<?php

namespace App\Services;


class PrintingToolkitService
{
    private function openKioskBat($launchUrl)
    {
        return '

            @echo off
            setlocal
            
            set "regKey=HKEY_CURRENT_USER\Software\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice"
            for /f "tokens=2,*" %%A in (\'reg query "%regKey%" /v ProgId\') do set "browserPath=%%B"
            
            if "%browserPath%"=="ChromeHTML" (
                set "browserProcess=chrome.exe" 
            ) else if "%browserPath%"=="FirefoxURL" (
                set "browserProcess=firefox.exe" 
            ) else if "%browserPath%"=="MSEdgeHTM" (
                set "browserProcess=msedge.exe" 
            ) else (
                set "defaultBrowser=Unknown"
            )
            
            taskkill /F /IM "%browserProcess%" 2>nul
            
            set app_url="' . $launchUrl . '"
            
            set chrome_flags= --enable-print-preview --kiosk-printing
            
            rem ==== Launch Chrome with app
            
            start %browserProcess% %app_url% %chrome_flags%
            endlocal
        ';
    }

    private function registerProtocolVbs()
    {
        return '
            If Not WScript.Arguments.Named.Exists("elevate") Then
              CreateObject("Shell.Application").ShellExecute WScript.FullName _
                , """" & WScript.ScriptFullName & """ /elevate", "", "runas", 1
              WScript.Quit
            End If
            
            Dim WshShell
            Set WshShell = WScript.CreateObject("WScript.Shell")

            Set fso = CreateObject("Scripting.FileSystemObject")
            Dim fullPath
            Set objFile = fso.GetFile(Wscript.ScriptFullName)
            fullPath = fso.GetParentFolderName(objFile)
            
            \' Write registry values
            WshShell.RegWrite "HKEY_CLASSES_ROOT\\'. Domain_Name_Only .'PrintingToolkit\URL Protocol", "", "REG_SZ"
            WshShell.RegWrite "HKEY_CLASSES_ROOT\\'. Domain_Name_Only .'PrintingToolkit\shell\open\", "", "REG_SZ"
            WshShell.RegWrite "HKEY_CLASSES_ROOT\\'. Domain_Name_Only .'PrintingToolkit\shell\open\command\", fullPath & "\daftra-open-kiosk.bat", "REG_SZ"
            
            \' Set the path to the target file or program
            targetPath = fullPath & "\\' . Domain_Name_Only . '-open-kiosk.bat" 
        
        
            \' Get the Desktop folder path
            desktopPath = WshShell.SpecialFolders("Desktop")
        
            \' Create a shortcut object
            Set objShortcut = WshShell.CreateShortcut(desktopPath & "\\' . Site_Full_name . ' reopen browser in kiosk.lnk")
        
            \' Set properties for the shortcut
            objShortcut.TargetPath = targetPath
            \' objShortcut.WorkingDirectory = "C:\Path\To\Working\Directory" \' Optional
            objShortcut.IconLocation = fullPath & "\\'. Domain_Name_Only .'.ico" \' Optional
            objShortcut.Save
        
            \' Clean up objects
            Set objShortcut = Nothing
            Set WshShell = Nothing
        ';
    }

    public function getDownloadable($url)
    {

        $zipArchive = new \ZipArchive();
        $zipFile = __DIR__ . "/../webroot/files/" . SITE_HASH . "/". Site_Full_name ."PrintingToolkit.zip";
        $beforeCompress = __DIR__ . "/../webroot/files/" . SITE_HASH . "/beforeCompress/";


        $zipArchive->open($zipFile, \ZipArchive::OVERWRITE | \ZipArchive::CREATE);

        if (!is_dir($beforeCompress)) mkdir($beforeCompress, 0777, true);

        file_put_contents($beforeCompress . Domain_Name_Only . '-open-kiosk.bat', $this->openKioskBat($url));
        $zipArchive->addFile($beforeCompress . Domain_Name_Only . '-open-kiosk.bat', ltrim(Domain_Name_Only . '-open-kiosk.bat', '/'));

        file_put_contents($beforeCompress . 'install-toolkit.vbs', $this->registerProtocolVbs());
        $zipArchive->addFile($beforeCompress . 'install-toolkit.vbs', ltrim('install-toolkit.vbs', '/'));
        
        $zipArchive->addFile(__DIR__.'/../webroot/css/images/' . Site_Full_name . '-favicon.ico', Domain_Name_Only. '.ico');

        $zipArchive->close();

        return $zipFile;
    }
}
