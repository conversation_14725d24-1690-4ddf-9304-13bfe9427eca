<?php

namespace App\Services;

/**
 * UserSessionRedisService
 * 
 * This service handles user session management using Redis. It allows for initializing
 * a Redis connection, retrieving current user sessions, adding new sessions, and 
 * revoking existing sessions while ensuring the current session remains active.
 */
class UserSessionRedisService
{
    protected $redis;

    private function getPrefixSite(){
        return 'site:'.getCurrentSite('id');
    }

    /**
     * Initialize the Redis connection using the specified REDIS_SERVER environment variable.
     * The method handles authentication, selecting the database, and connecting to the Redis server.
     * 
     * @return $this
     */
    public function init()
    {
        $this->redis = new \Redis();
        $parsedUrl = parse_url(REDIS_SERVER);
 
        // Handling the port if specified in the URL
        $port = isset($parsedUrl['port']) ? $parsedUrl['port'] : 6379;

        $this->redis->connect($parsedUrl['scheme']."://".$parsedUrl['host'], $port);

        // Authenticate if there is a user and pass in the URL
        if (isset($parsedUrl['query'])) {
            $this->redis->auth(explode('=',$parsedUrl['query'],2)[1]);
        }

        // Optionally select the database number if specified in the URL
        if (isset($parsedUrl['path'])) {
            $db = ltrim($parsedUrl['path'], '/');
            if (is_numeric($db)) {
                $this->redis->select((int)$db);
            }
        }

        return $this;
    }

    /**
     * Retrieve the current user sessions for a given user ID and site ID.
     * 
     * @param string $userId
     * @return array
     */
    public function getCurrentUserSessions($userId)
    {
        $sessions = $this->redis->hGet($this->getPrefixSite(), $userId);
        return $sessions ? json_decode($sessions, true) : [];
    }

    /**
     * Add a new session ID to the user's current sessions if it doesn't already exist.
     * 
     * @param string $userId
     * @param string $sessionId
     * @return void
     */
    public function add($userId, $sessionId)
    {
        $currentSessions = $this->getCurrentUserSessions($userId);
        if (!in_array($sessionId, $currentSessions)) {
            $currentSessions[] = $sessionId;
            $this->redis->hSet($this->getPrefixSite(), $userId, json_encode($currentSessions));
        }

    }

    /**
     * Revoke all user sessions except the current one for a given user ID and site ID.
     * 
     * @param string $siteId
     * @param string $userId
     * @return void
     */
    public function revokeSessions($userId)
    {
        $sessions = $this->getCurrentUserSessions($userId);
        // Save the current session ID to restore later
        $currentSessionId = session_id();
        
        // Filter out the current session ID
        $sessionsToRevoke = array_filter($sessions, function($session) use ($currentSessionId) {
            return $session !== $currentSessionId;
        });

        // Revoke sessions
        if (!empty($sessionsToRevoke)) {
            foreach ($sessionsToRevoke as $session) {
                session_write_close(); // Ensure the current session is saved properly
                session_id($session);
                session_start();
                session_destroy();
            }
        }

        // Restore the current session
        session_id($currentSessionId);
        session_start();

        // Update Redis with the current session only
        $this->redis->hSet($this->getPrefixSite(), $userId, json_encode([$currentSessionId]));
    }


    /**
     * Check if the user has an active session. 
     * If not, assign the current session ID to Redis.
     * 
     * @param string $userId
     * @return void
     */
    public function checkOrAssignSession($userId)
    {
        // Get the current session ID
        $currentSessionId = session_id();

        // If the user has no active sessions, assign the current session ID
        $this->add($userId, $currentSessionId);
    }


}
