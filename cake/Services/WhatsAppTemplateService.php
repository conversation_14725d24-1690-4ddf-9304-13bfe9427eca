<?php

namespace App\Services;

use App\Repositories\WhatsAppTemplateRepository;
use SendSMS\WhatsAppSendSMS;

class WhatsAppTemplateService
{

    /**
     * @var WhatsAppTemplateRepository
     */
    public $repository;

    /**
     * @var WhatsAppSendSMS
     */
    public $sendSMSApi;

    public function __construct(WhatsAppTemplateRepository $repository, WhatsAppSendSMS $sendSMS)
    {
        $this->repository = $repository;
        $this->sendSMSApi = $sendSMS;
    }

    public function createInitialTemplates()
    {
        $templates = $this->repository->getAllTemplates();
        $response = [];
        foreach ($templates as $template) {
            $response[] = $this->sendSMSApi->createMessageTemplate($template);
        }
        return $response;

    }
}