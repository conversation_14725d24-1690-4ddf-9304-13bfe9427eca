<?php

namespace App\Services\Report;

use App\Services\Report\Types\Xlsx;

class ReportFactory
{
    /**
     * @param $type
     * @return Xlsx|void
     */
    public static function init($type)
    {
        switch ($type) {
            case ReportUtil::XLSX:
                return new Xlsx();
                break;
        }
    }

    /**
     * @param $reportAction
     * @param $reportType
     * @return string
     */
    public static function element($reportAction, $reportType)
    {
        $element = 'reports/';
        $isElement = true;
        switch ($reportAction) {
            case 'owner_payments':
                $element .= "csv-payment-payment";
                break;
            case 'owner_products':
                $element .= "xlsx/product-{$reportType}";
                break;
            case 'owner_revenue':
                $element .= "/xlsx/revenue-{$reportType}";
                break;
            case 'owner_stock_transactions_profit':
                $isElement = false;
                $element = "/reports/xlsx/owner_stock_transactions_profit";
                break;     
            case 'owner_taxes':
                $isElement = false;
                $element = "/reports/csv/owner_taxes";
                break;  
            case 'owner_new_taxes':
                $isElement = false;
                $element = "/reports/csv/owner_new_taxes";
                break;     
            case 'owner_accounts':
                $isElement = false;
                $element = "/reports/csv/owner_accounts";
                break;   
            case 'owner_profit':
                $isElement = false;
                $element = "/reports/csv/owner_profit";
                break;   
            case 'owner_accounts_profit_accrual':
                $isElement = false;
                $element = "/reports/csv/owner_accounts_profit_accrual";
                break;       
            case 'owner_financial_report':
                $isElement = false;
                $element = "/reports/csv/owner_financial_report";
                break;   
            case 'owner_report':    
                $isElement = false;
                if($reportType == "time_tracking") {
                    $element = "/time_tracking/xlsx/owner_report_date";
                } else {
                    $element = "/reports/csv/owner_report";
                }
                break;      
            case 'owner_cost_centers':
                $isElement = false;
                $element = "/reports/csv/owner_cost_centers";
                break;      
            case 'owner_journal_transactions':
                $isElement = false;
                $element = "/reports/csv/owner_journal_transactions";
                break;   
            case 'owner_work_orders':
                $isElement = false;
                $element = "/reports/csv/owner_work_orders";
                break;

            case 'owner_tags':
                $isElement = true;
                $element = "reports/tags/csv-tags";
                break;   

            case 'owner_aged_debtors':
                $isElement = false;
                $element = "/reports/csv/owner_aged_debtors";
                break;    
                
            case 'owner_stocktaking_sheet':
                $isElement = false;
                $element = "/products/xlsx/owner_stocktaking_sheet";
                break;   

            case 'owner_store_summary':
                $isElement = false;
                $element = "/products/xlsx/owner_store_summary";
                break;   

            case 'owner_worthsheet':
                $isElement = false;
                $element = "/products/xlsx/owner_worthsheet";
                break;    

            case 'owner_stock_transactions':
                $isElement = false;
                $element = "/reports/xlsx/owner_stock_transactions";
                break;      
            case 'owner_products_profit':
                $isElement = false;
                $element = "/products/xlsx/owner_products_profit";
            break;
            case 'owner_advance_payments':
                 $element .= "csv-advance-payment-client";
                break;
            case 'owner_cash_flow':
                $isElement = false;
                $element = "/reports/xlsx/owner_cash_flow";
                break;
            default:
                $isElement = false;
                $element = "/reports/xlsx/owner_report";
                break;
        }
        return ['element' => $element, 'isElement' => $isElement];
    }
}
