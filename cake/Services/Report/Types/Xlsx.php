<?php

namespace App\Services\Report\Types;

use App\Services\Report\Report;

class Xlsx extends Report
{
    /**
     * @param $name
     * @param $view
     * @param $element
     * @param $reportType
     * @return mixed|void
     * @throws \Exception
     */
    public function export($name, $view, $element, $reportType)
    {

        require_once APP . 'vendors' . DS . 'PHPExcel.php';
        \App::import('View', 'View');
        $view =  new \View($view);

        $data = $element['isElement'] ? $view->element($element['element']) : $view->render($element['element']);
        //as "=" is a special character in excel so we have to escape it
        $data = str_replace(["="], ["\="], $data);
        $utf8_with_bom = chr(239) . chr(187) . chr(191) . $data;
        $objReader = \PHPExcel_IOFactory::createReader('CSV');
        $objReader->setInputEncoding('UTF-8');
        $siteHash = SITE_HASH;
        if (!is_dir("/tmp/{$siteHash}")) mkdir("/tmp/{$siteHash}");
        file_put_contents("/tmp/{$siteHash}/{$name}.csv", $utf8_with_bom);
        header('Content-Type: application/vnd.ms-excel');

        $outputFileName = low(\Inflector::slug($this->site['business_name'])) . "-{$name}-{$reportType}.xls";

        $reportTitle = $view->viewVars['report_title'] ?? null;

        if ($reportTitle) {
            // same naming convention used in csv and and pdf
            $outputFileName = uniqid() . '-' . $reportTitle .'.xls';
        }

        header('Content-Disposition: attachment; filename=' . $outputFileName, true);

        $objPHPExcel = $objReader->load("/tmp/{$siteHash}/{$name}.csv");
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save('php://output');

        exit();
    }
}
