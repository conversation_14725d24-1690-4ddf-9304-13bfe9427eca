<?php

namespace App\Services\FinancialYear;

use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\Modal;
use Izam\View\Form\Element\Pagination;
use Izam\View\Form\Element\Sperator;
use Izam\View\Form\Element\Stack;
use Izam\View\Form\Element\StaticContent;
use Izam\View\Form\Element\StatusCircle;
use Izam\View\Form\Element\TitleText;
use Izam\View\Form\Element\ViewActions;
use Izam\View\Form\Tab\IframeElement;
use Laminas\Form\Element;

class ShowService
{
    public static function getTabs($data): array
    {
        return [
            [
                'type' => Element::class,
                'name' => 'details',
                'label' => __t('Details'),
                'value' => [
                    "viewPath" => "financial_years/partials/details",
                    "context" => ["data" => $data],
                ],
            ],
            [
                'type' => IframeElement::class,
                'name' => 'activity-log',
                'label' => __t('Activity Log'),
                'attributes' => [
                    "src" => "/v2/owner/activity_logs/entity/iframe?entity_key=financial_year&entity_id=" . $data->id . "&sort=DESC&layout2022=1",
                ]
            ]
        ];
    }


    public static function getActions($data, $params): ViewActions
    {
        $viewActions = new ViewActions('View-Action');

        if (!$data['FinancialYear']['is_closed']) {
            $editAnchor = new Anchor('edit-action');
            $editAnchor->setLabel(__t('Edit'))
                ->setAttribute('href', \Router::url([
                        'controller' => $params['controller'],
                        'action' => 'edit' , $data['FinancialYear']['id']
                    ]). '?branch=' . $data['FinancialYear']['branch_id']
                )
                ->setOption('icon', 'pencil');

            $closePeriodBtn = new Element\Button('close-period');
            $closePeriodBtn
                ->setLabel(__t('Close'))
                ->setOption('modal', 'close-period-modal')
                ->setOption('theme', 'delete')
                ->setOption('icon', 'stop-circle');

            $deleteBtn = new Element\Button('delete-action');
            $deleteBtn->setLabel(__t('Cancel'))
                ->setOption('theme', 'delete')
                ->setOption('message', sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Financial Period')))
                ->setOption('action', \Router::url([
                    'controller' => $params['controller'],
                    'action' => 'edit', $data['FinancialYear']['id']
                ]). '?branch=' . $data['FinancialYear']['branch_id'])
                ->setOption('icon', 'trash-can');

            $viewActions
                ->add($editAnchor)
                ->add($closePeriodBtn)
                ->add($deleteBtn);
        }

        return $viewActions;
    }

    public static function getPageHeader($data, $viewVars): Header
    {
        $pageTitle = new TitleText('page-title');

        $pageTitle->setTitle(sprintf(
            '%s %s %s %s %s',
            __t('Financial Period'),
            __t('From'),
            format_date($data['FinancialYear']['start_date']),
            __t('to'),
            format_date($data['FinancialYear']['end_date'])
        ))
        ->setSecondaryInfo($data['Branch']['name']);

        $leftStack = new Stack('Left-Stack');
        $leftStack->setOption('theme', 'theme4');
        $treasuryStatus = new StatusCircle('Unit-Status');
        if ($data['FinancialYear']['is_closed']) {
            $treasuryStatus->setLabel(__t('Closed', true))
                ->setOption('color', 'inactive');
        } else {
            $treasuryStatus->setLabel(__t('Open', true))
                ->setOption('color', 'active');
        }
        $leftStack->add($treasuryStatus);

        if (!$data['FinancialYear']['is_closed']) {
            $rightItem = new Button('close-period');
            $rightItem
                ->setLabel(__t('Close Period'))
                ->setOption('color', '#00B0EF')
                ->setOption('modal', 'close-period-modal');
        } else {
            $rightStack = new Stack('left-Stack');
            $rightStack->setOption('theme', 'theme3');

            $transactionFormat = self::getTransactionFormat($data);

            $profitLossValue = sprintf('
                <div class="hstack gap-6">
                    <span class="vr opacity-100 bg-%s" style="--bs-border-width: 3px"></span>
                    <span class="text-amount text-success">%s</span>
                </div>',
                $transactionFormat['spanColor'],
                format_price($transactionFormat['amountTransaction'], $transactionFormat['currencyCode'])
            );

            $profitLoss = new Element('Profit-Loss');
            $profitLoss->setLabel($profitLossValue);

            $rightStack
                ->add($profitLoss);

            $rightItem = $rightStack;
        }
        $params = $_GET;
        unset($params['url']);
        $params = (!empty(http_build_query($params))) ? '?' . http_build_query($params) : '';
        $pagePagination = new Pagination('View-Pagination');
        $paginationNextBtn = new Anchor('Pagination-Next-Btn');
        $paginationNextBtn->setLabel(__t('Next'))
            ->setAttribute('href', $viewVars['izamNavigation']->getPageNextUrl())
            ->setOption('icon', 'chevron-down');
        if (!$viewVars['has_next']) {
            $paginationNextBtn->setOption('disabled', 'disabled');
        }

        $paginationPreviousBtn = new Anchor('Pagination-Previous-Btn');
        $paginationPreviousBtn->setLabel(__t('Previous'))
            ->setAttribute('href', $viewVars['izamNavigation']->getPagePreviousUrl() . $params)
            ->setOption('icon', 'chevron-up');
        if (!$viewVars['has_prev']) {
            $paginationPreviousBtn->setOption('disabled', 'disabled');
        }

        $pagePagination
            ->add($paginationNextBtn)
            ->add($paginationPreviousBtn);

        $header = new Header('page-header');

        $header
            ->addLeft($pageTitle)
            ->addLeft(new Sperator('separator2'))
            ->addLeft($leftStack)
            ->addRight($rightItem)
            ->addRight($pagePagination)
        ;

        return $header;
    }

    public static function getModals($data, $controller)
    {
        $modal = new Modal('close-period-modal');

        $statDate = format_date($data['FinancialYear']['start_date']);
        $endDate = format_date($data['FinancialYear']['end_date']);

        $label = sprintf('
            <span>%s<span data-close-period-modal-from="true"> %s </span> %s <span data-close-period-modal-to="true"> %s </span>',
            __t('Closing Financial Period from'),
            $statDate,
            __t('to'),
            $endDate
        );

        $modal->setLabel($label);

        $modal->setOption('action', \Router::url([
            'controller' => $controller,
            'action' => 'add',
            '?' => ['branch' => $data['FinancialYear']['branch_id']]
        ]));

        $cancelBtn = (new Element\Button('cancel-modal-btn'))
            ->setLabel(__t('Cancel'))
            ->setOption('color', 'secondary')
            ->setOption('dismiss', true);

        $actionBtn = (new Element\Button("close-period-modal-btn"))
            ->setLabel(__t('Setup the Next Period'))
            ->setOption('color', 'success')
            ->setOption('submit', true);

        $methodInput = (new Element\Hidden('_method'))->setValue('GET');

        $content = new StaticContent('msg');

        $msg = sprintf(
            '%s <span class="fw-bold">%s</span> %s <span class="fw-bold">%s</span>, %s',
            __t('To Close the current open period from'),
            $statDate,
            __t('to'),
            $endDate,
            __t('please specify the next period dates.')
        );

        $content->setValue($msg);

        $modal
            ->addButton($cancelBtn)
            ->addButton($actionBtn)
            ->addToForm($content)
            ->addToForm($methodInput);

        return [$modal];
    }

    public static function getTransactionFormat($data)
    {
        $transactionFormat = ['hasTransaction' => false, 'spanColor' => '', 'amountTransaction' => '', 'currencyCode' => ''];
        if ($data['FinancialYear']['is_closed']) {
            foreach ($data['Journal']['JournalTransaction'] as $key => $transaction) {
                if ($transaction['subkey'] == \Journal::PROFIT_LOSS_ACCOUNT_SUBKEY || $transaction['JournalAccount']['entity_type'] == \Journal::RETAINED_EARNINGS) {
                    $transactionFormat = ['hasTransaction' => true, 'spanColor' => 'danger', 'amountTransaction' => '-' . $transaction['currency_debit'], 'currencyCode' => $transaction['currency_code']];
                    if ($transaction['currency_credit']) {
                        $transactionFormat = ['hasTransaction' => true, 'spanColor' => 'success', 'amountTransaction' => $transaction['currency_credit'], 'currencyCode' => $transaction['currency_code']];
                    }
                    break;
                }
                if($transaction['subkey'] == \Journal::PROFIT_LOSS_ACCOUNT_SUBKEY || $transaction['JournalAccount']['entity_type'] == \Journal::CAPITAL_PROFIT_LOSS_ACCOUNT_ENTITY_TYPE){
                    $transactionFormat = ['hasTransaction' => true, 'spanColor' => 'danger', 'amountTransaction' => '-' . $transaction['currency_debit'], 'currencyCode' => $transaction['currency_code']];
                    if ($transaction['currency_credit']) {
                        $transactionFormat = ['hasTransaction' => true, 'spanColor' => 'success', 'amountTransaction' => $transaction['currency_credit'], 'currencyCode' => $transaction['currency_code']];
                    }
                }
            }
        }

        return $transactionFormat;
    }
}