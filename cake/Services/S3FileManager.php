<?php

namespace App\Services;

use Aws\S3\S3Client;
use League\Flysystem\AwsS3v3\AwsS3Adapter as S3Adapter;

class S3FileManager
{
    public function getUrlAttribute($path, $name)
    {
        if (!$path) {
            return null;
        }

        $timeInMinutes = defined(AWS_LIFE_TIME) ? AWS_LIFE_TIME : 1;

        return $this->temporaryUrl(
            $path, time() + ($timeInMinutes * 60),
            ['ResponseContentDisposition' => 'attachment; filename="' . utf8_encode($name) . '"']
        );
    }
    public function getUrl($path)
    {
        if (!$path) {
            return null;
        }
    
        $timeInMinutes = defined(AWS_LIFE_TIME) ? AWS_LIFE_TIME : 1;
    
        return $this->temporaryUrl(
            $path, time() + ($timeInMinutes * 60),
            ['ResponseContentDisposition' => 'attachment']
        );
    }
    public function getPermanentUrlAttribute($path, $name)
    {
        if (!$path) {
            return null;
        }

        $timeInMinutes = defined(AWS_LIFE_TIME) ? AWS_LIFE_TIME : 12960000;

        return $this->temporaryUrl(
            $path, time() + ($timeInMinutes * 60),
            ['ResponseContentDisposition' => 'attachment; filename="' . $name . '"']
        );
    }

    public function temporaryUrl($path, $expiration, array $options = [])
    {
        $s3Config = [
            'driver' => 's3',
            'key' => AWS_ACCESS_KEY_ID,
            'secret' => AWS_SECRET_ACCESS_KEY,
            'region' => AWS_DEFAULT_REGION,
            'bucket' => AWS_BUCKET,
            'url' => AWS_URL,
            'version' => 'latest',
            'credentials' => [
                'key' => AWS_ACCESS_KEY_ID,
                'secret' => AWS_SECRET_ACCESS_KEY,
            ]
        ];

        $adapter = new S3Adapter(new S3Client($s3Config), $s3Config['bucket']);
        
        return $this->getAwsTemporaryUrl($adapter, $path, $expiration, $options);
    }

    private function getAwsTemporaryUrl($adapter, $path, $expiration, $options)
    {
        $client = $adapter->getClient();

        $command = $client->getCommand('GetObject', array_merge([
            'Bucket' => $adapter->getBucket(),
            'Key' => $adapter->getPathPrefix().$path,
        ], $options));

        $uri = $client->createPresignedRequest(
            $command, $expiration
        )->getUri();

        return (string) $uri;
    }
}
