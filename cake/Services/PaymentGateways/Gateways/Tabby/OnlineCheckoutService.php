<?php

namespace App\Services\PaymentGateways\Gateways\Tabby;

use GuzzleHttp\Exception\ClientException;

class OnlineCheckoutService extends TabbyBaseService
{
    /**
     * @var array
     */
    protected $customer;

    /**
     * @var array
     */
    protected $merchantUrls;


    /**
     * @var float
     */
    protected $shippingAmount;

    /**
     * @var float
     */
    protected $taxAmount;

    /**
     * @var string
     */
    protected $language;

    /**
     * @var string
     */
    protected string $merchantCode = '';

    public function __construct($publicKey, $secretKey, $environment)
    {
        parent::__construct($publicKey, $secretKey, $environment);
    }

    /**
     * Set the metchants urls array
     *
     * @param array $merchantUrls
     *
     * @return TabbyBaseService
     */
    public function setMerchantUrls(array $merchantUrls): TabbyBaseService
    {
        if ($merchantUrls) {
            $this->merchantUrls = $merchantUrls;
        } else {
            $this->merchantUrls = [];
        }

        return $this;
    }

    /**
     * Set payload customer
     *
     * @param array $customer
     *
     * @return TabbyBaseService
     */
    public function setCustomer(array $customer): TabbyBaseService
    {
        $this->customer = $customer;

        return $this;
    }

    /**
     * Set shipping amount value
     *
     * @param float $amount
     *
     * @return TabbyBaseService
     */
    public function setShippingAmount(float $amount): TabbyBaseService
    {
        $this->shippingAmount = $this->formatMoney($amount);

        return $this;
    }

    /**
     * Set tax amount value
     *
     * @param float $amount
     *
     * @return TabbyBaseService
     */
    public function setTaxAmount(float $amount): TabbyBaseService
    {
        $this->taxAmount = $this->formatMoney($amount);

        return $this;
    }

    /**
     * Set the payment page language.
     *
     * @param string $language
     *
     * @return TabbyBaseService
     */
    public function setLanguage(string $language): TabbyBaseService
    {
        $this->language = $language;

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function preparePayload(): array
    {
        $payload = [
            'lang' => $this->language ?? 'en',
            'merchant_code' => $this->merchantCode,
            'payment' => [
                'amount' => str_replace(',', '', $this->totalAmount),
                'currency' => $this->currencyCode,
                'description' => '',
                'buyer' => $this->customer,
                'shipping_address' => $this->shippingAddress,
                'order' => [
                    'tax_amount' => $this->taxAmount ?? 0,
                    'shipping_amount' => $this->shippingAmount ?? 0,
                    'discountAmount' => $this->discountAmount ?? 0,
                    'reference_id' => $this->orderReferenceId,
                    'items' => $this->items,
                ],
            ],
        ];
        if (!empty($this->merchantUrls)) {
            $payload['merchant_urls'] = $this->merchantUrls;
        }
        return $payload;
    }

    /**
     * @inheritdoc
     */
    public function processService(): array
    {
        try {
            $request = $this->getHttpClient()->post('checkout', [
                'json' => $this->preparePayload(),
                'headers' => [
                    'Authorization' => sprintf(
                        'Bearer %s',
                        $this->publicKey
                    ),
                ]
            ]);

            return [
                'status' => 'OK',
                'data' => json_decode($request->getBody()->getContents(), true)
            ];
        } catch (ClientException $e) {

            $response = $e->getResponse()->getBody()->getContents();
            $response = json_decode($response, true);

            $this->log("Get order details :: Error", context: [
                'payload' => $this->preparePayload(),
                'error' => $response
            ]);

            return [
                'status' => 'ERR',
                'error' => '',
                'errors' => [
                    $response['error']
                ],
            ];
        }
    }

    public function sendSMSNotifcation($sessionId): array
    {
        try {
            $request = $this->getHttpClient()->post('checkout/'.$sessionId.'/send_hpp_link', [
                'headers' => [
                    'Authorization' => sprintf(
                        'Bearer %s',
                        $this->publicKey
                    ),
                ]
            ]);

            return [
                'status' => 'OK',
                'data' => json_decode($request->getBody()->getContents(), true)
            ];
        } catch (ClientException $e) {

            $response = $e->getResponse()->getBody()->getContents();
            $response = json_decode($response, true);

            $this->log("Get order details :: Error", context: [
                'payload' => $this->preparePayload(),
                'error' => $response
            ]);

            return [
                'status' => 'ERR',
                'error' => '',
                'errors' => [
                    $response['error']
                ],
            ];
        }
    }
}
