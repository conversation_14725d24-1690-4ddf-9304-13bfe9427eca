<?php

namespace App\Services\PaymentGateways\Gateways\Tabby;

use App\Services\PaymentGateways\{
    AbstractPaymentGateway,
    PaymentGatewayInterface,
};

use App\Services\PaymentGateways\Concerns\{
    HasWebhookConcern,
    HasReturnUrlConcern,
    HasRedirectionConcern,
};

/**
 * Entry point class to the gateway implementation.
 *
 * @todo the defined methods are implementing the concrete implementation
 * of the service, which is not the responsibility of the class methods.
 * this should be revoked to be defined out side of the class methods.
 * OR to pass a direct variables with the services required data instead
 * of a single array that holds all of the key => value data.
 */
#[
    HasWebhookConcern(shortName: 'hasWebhook'),
    HasReturnUrlConcern(
        shortName: 'hasReturnUrl',
        gatewayReturnUrlUniqueParameter: 'payment_id'
    ),
    HasRedirectionConcern(shortName: 'hasRedirection'),
]
class Gateway extends AbstractPaymentGateway implements PaymentGatewayInterface
{
    public function __construct(private ?array $parameters = []) {}

    /**
     * @inheritdoc
     */
    final public function getName(): string
    {
        return 'tabby';
    }

    /**
     * Creates the checkout session.
     *
     * @param array $data
     *
     * @return array
     */
    public function createSession(array $data): array
    {
        $service = new OnlineCheckoutService(
            $this->parameters['username'],
            $this->parameters['option1'],
            getenv('TABBY_ENVIRONMENT', 'sandbox')
        );
        $service->setCurrency($data['Invoice']['currency_code'] ?: $data['currency_code']);
        $service->setLanguage(
            $data['invoice']['language_id'] == 7
                ? 'ar'
                : 'en'
        );
        $countryCode = $data['Client']['country_code']
            ?: $data['Client']['secondary_country_code']
            ?: $data['country_code']
            ?: getCurrentSite('country_code');
        $service->setCountryCode($countryCode);
        $service->setTotalAmount(round($data['InvoicePayment']['amount'], 2) ?: round($data['amount'], 2));
        $service->setDiscountAmount(($data['Invoice']['summary_discount'] ?: $data['invoice']['summary_discount']) ?: 0.0);
        $service->setShippingAmount(0);
        $service->setTaxAmount(0);
        $service->setOrderReferenceId($data['InvoicePayment']['id'] ?: $data['id']);
        if (isset($data['source']) && $data['source'] == "client_add_payment_credit"){
            $orderNumber = $data['order_id'] ?: $data['id'];
        } else {
            $orderNumber = $data['Invoice']['id'] ?: $data['invoice_id'];
        }

        $service->setOrderNumber($orderNumber);
        $service->setMerchantCode($this->parameters['option2']);

        $service->setShippingAddress([
            'city' => $data['Client']['city'] ?: $data['city'],
            'zip' => $data['Client']['postal_code'] ?: $data['postal_code'],
            'line1' => $data['Client']['address1'] ?: $data['address1']
        ]);

        $service->setCustomer([
            'email' => $data['Client']['email'] ?: $data['email'],
            'name' => sprintf(
                "%s %s",
                ($data['Client']['first_name'] ?: $data['first_name']) ?? '-',
                ($data['Client']['last_name'] ?: $data['last_name']) ?? '-'
            ),
            'phone' => $data['client_phone_number'] ?? $data['Client']['phone1'],
        ]);

        if (isset($data['source']) && $data['source'] != "POS") {
            $service->setMerchantUrls($data['merchant_urls']);
        } else {
            $service->setMerchantUrls([]);
        }
        if (isset($data['source']) && $data['source'] == "client_add_payment_credit"){
            $orderItems[] = ['id' => $data['order_id'], 'item' => 'Client Add Credit', 'unit_price' => $data['amount'], 'quantity' => 1];
        } else {
            $orderItems = $data['InvoiceItem'] ?: $data['invoiceItems'];
        }
        $service->setItems($orderItems);
        return $service->processService();
    }

    public function sendSMSNotification($sessionId)
    {
        $service = new OnlineCheckoutService(
            $this->parameters['username'],
            $this->parameters['option1'],
            getenv('TABBY_ENVIRONMENT', 'sandbox')
        );
        return $service->sendSMSNotifcation($sessionId);
    }

    /**
     * Authorize a payment.
     *
     * @param array $data
     *
     * @return array
     */
    public function authorize(array $data): array
    {
        $service = new AuthorizeService(
            $this->parameters['username'],
            $this->parameters['option1'],
            getenv('TABBY_ENVIRONMENT', 'sandbox')
        );

        $service->setTransactionReference($data['payment_id']);

        return $service->processService();
    }

    /**
     * Capture a given payment
     *
     * @param array $data
     *
     * @return array
     */
    public function capture(array $data): array
    {
        $service = new CaptureService(
            $this->parameters['username'],
            $this->parameters['option1'],
            getenv('TABBY_ENVIRONMENT', 'sandbox')
        );

        $service->setTransactionReference($data['payment_id']);
        $service->setTotalAmount($data['amount']);

        return $service->processService();
    }

    /**
     * Refund a given payment
     *
     * @param array $data
     *
     * @return array
     */
    public function refund(array $data): array
    {
        $service = new RefundService(
            $this->parameters['username'],
            $this->parameters['option1'],
            getenv('TABBY_ENVIRONMENT', 'sandbox')
        );

        $service->setTransactionReference($data['payment_id']);
        $service->setTotalAmount($data['amount']);

        return $service->processService();
    }

    /**
     * Register a webhook url.
     *
     * @param array $data
     *
     * @return array
     */
    public function setupWebhook(array $data): array
    {
        $service = new RegisterWebhookService(
            $this->parameters['username'],
            $this->parameters['option1'],
            getenv('TABBY_ENVIRONMENT', 'sandbox')
        );

        $service->setMerchantCode($data['merchant_code']);
        $service->setWebhookUrl($data['webhook_url']);
        $service->setAuthToken($data['auth_token']);

        return $service->processService();
    }

    /**
     * Helper method to resolve the InvoicePayment status
     * using a given gateway status
     *
     * @param string $gatewayStatus
     *
     * @return int
     */
    public function resolvePaymentStatus($gatewayStatus): int
    {
        return $this->parameters['statuses'][$gatewayStatus] ?? 0;
    }
}
