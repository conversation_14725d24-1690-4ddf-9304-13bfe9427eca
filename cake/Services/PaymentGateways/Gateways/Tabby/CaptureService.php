<?php

namespace App\Services\PaymentGateways\Gateways\Tabby;

use GuzzleHttp\Exception\ClientException;

class CaptureService extends TabbyBaseService
{
    /**
     * @inheritdoc
     */
    protected function preparePayload(): array
    {
        return [
            'amount' => (string) $this->totalAmount,
        ];
    }

    /**
     * @inheritdoc
     */
    public function processService(): array
    {
        try {
            $request = $this->getHttpClient()->post(
                sprintf('payments/%s/captures', $this->transactionReference),
                [
                    'json' => $this->preparePayload()
                ]
            );

            return [
                'status' => 'OK',
                'data' => json_decode($request->getBody()->getContents(), true)
            ];
        } catch (ClientException $e) {

            $response = $e->getResponse()->getBody()->getContents();
            $response = json_decode($response, true);

            $this->log("Get order details :: Error", context: [
                'payload' => $this->preparePayload(),
                'error' => $response
            ]);

            return [
                'status' => 'ERR',
                'error' => '',
                'errors' => [
                    $response['error']
                ],
            ];
        }
    }
}
