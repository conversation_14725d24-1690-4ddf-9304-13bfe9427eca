<?php

namespace App\Services\PaymentGateways\Gateways\Tabby;

use GuzzleHttp\Exception\ClientException;

class AuthorizeService extends TabbyBaseService
{
    /**
     * @inheritdoc
     */
    protected function preparePayload(): array
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function processService(): array
    {
        try {
            $request = $this->getHttpClient()->get(
                sprintf('payments/%s', $this->transactionReference)
            );

            return [
                'status' => 'OK',
                'data' => json_decode($request->getBody()->getContents(), true)
            ];
        } catch (ClientException $e) {

            $response = $e->getResponse()->getBody()->getContents();
            $response = json_decode($response, true);

            $this->log("Get order details :: Error", context: [
                'payload' => $this->preparePayload(),
                'error' => $response
            ]);

            return [
                'status' => 'ERR',
                'error' => '',
                'errors' => [
                    $response['error']
                ],
            ];
        }
    }
}
