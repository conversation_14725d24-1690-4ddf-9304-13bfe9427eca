<?php

namespace App\Services\PaymentGateways\Gateways\Tabby;

use GuzzleHttp\Exception\ClientException;

class RegisterWebhookService extends TabbyBaseService
{
    /**
     * @var string
     */
    protected string $authToken;

    /**
     * @var string
     */
    protected string $webhookUrl;

    /**
     * Set the payment page language.
     *
     * @param string $language
     *
     * @return TabbyBaseService
     */
    public function setAuthToken(string $authToken): TabbyBaseService
    {
        $this->authToken = $authToken;

        return $this;
    }

    /**
     * Set the webhook endpoint.
     *
     * @param string $webhookUrl
     *
     * @return TabbyBaseService
     */
    public function setWebhookUrl(string $webhookUrl): TabbyBaseService
    {
        $this->webhookUrl = $webhookUrl;

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function preparePayload(): array
    {
        return [
            'url' => (string) $this->webhookUrl,
            'is_test' => true,
            'header' => [
                'title' => 'X-Daftra-Auth',
                'value' => $this->authToken,
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function processService(): array
    {
        try {
            $request = $this->getHttpClient()->post($this->resolveUrl(), [
                'json' => $this->preparePayload(),
                'headers' => [
                    'X-Merchant-Code' => $this->merchantCode,
                    'Authorization' => sprintf(
                        'Bearer %s',
                        $this->secretKey
                    ),
                ],
            ]);

            return [
                'status' => 'OK',
                'data' => json_decode($request->getBody()->getContents(), true)
            ];
        } catch (ClientException $e) {

            $response = $e->getResponse()->getBody()->getContents();
            $response = json_decode($response, true);

            $this->log("Get order details :: Error", context: [
                'payload' => $this->preparePayload(),
                'error' => $response
            ]);

            return [
                'status' => 'ERR',
                'error' => '',
                'errors' => [
                    $response['error']
                ],
            ];
        }
    }

    /**
     * According to Tabby, the webhooks working only with v1 api version.
     *
     * @return string
     */
    private function resolveUrl(): string
    {
        $url = str_replace(
            '/v2/',
            '/v1/',
            $this->getHttpEnvironmentUri()
        );

        return "{$url}webhooks";
    }
}
