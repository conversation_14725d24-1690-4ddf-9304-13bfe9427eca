<?php

namespace App\Services\PaymentGateways\Gateways\Tabby;

use GuzzleHttp\Exception\ClientException;

class RefundService extends TabbyBaseService
{
    /**
     * @var string
     */
    protected $comment = null;

    protected $refundAmount = null;


    /**
     * @var string
     */
    protected $orderId;

    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function setRefundAmount($refundAmount)
    {
        $this->refundAmount = $refundAmount;

        return $this;
    }
    /**
     * @inheritdoc
     */
    protected function preparePayload(): array
    {
        return [
            'amount' => (string) $this->refundAmount,
        ];
    }

    public function setComment($comment)
    {
        $this->comment = $comment;

        return $this;
    }


    /**
     * @inheritdoc
     */
    public function processService(): array
    {
        try {
            $request = $this->getHttpClient()->post(
                sprintf('payments/%s/refunds', $this->orderId),
                [
                    'json' => $this->preparePayload()
                ]
            );

            return [
                'status' => 'OK',
                'data' => json_decode($request->getBody()->getContents(), true)
            ];
        } catch (ClientException $e) {

            $response = $e->getResponse()->getBody()->getContents();
            $response = json_decode($response, true);

            $this->log("Get order details :: Error", context: [
                'payload' => $this->preparePayload(),
                'error' => $response
            ]);

            return [
                'status' => 'ERR',
                'error' => '',
                'errors' => [
                    $response['error']
                ],
            ];
        }
    }
}
