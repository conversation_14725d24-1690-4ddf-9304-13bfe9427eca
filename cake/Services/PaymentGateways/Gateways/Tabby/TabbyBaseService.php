<?php

namespace App\Services\PaymentGateways\Gateways\Tabby;

use App\Services\PaymentGateways\BaseGatewayService;

use GuzzleHttp\Exception\ClientException;

use \Rollbar\{
    Rollbar,
    Payload\Level,
};

/**
 * Base service for tabby payment gateway.
 *
 * @todo implement base http service and move the
 * http related code to it
 */
abstract class TabbyBaseService extends BaseGatewayService
{
    /**
     * @var array
     */
    protected $httpEnvironmentUris = [
        'sandbox' => 'https://api.tabby.ai/api/v2/',
        'live' => 'https://api.tabby.ai/api/v2/',
    ];

    /**
     * @var float
     */
    protected $totalAmount;

    /**
     * @var string
     */
    protected $currencyCode;

    /**
     * @var string
     */
    protected $countryCode;

    /**
     * @var string
     */
    protected $orderReferenceId;

    /**
     * @var string
     */
    protected $orderNumber;

    /**
     * @var array
     */
    protected $shippingAddress;

    /**
     * @var array
     */
    protected $items;

    /**
     * @var float
     */
    protected $discountAmount;

    /**
     * @var string
     */
    protected string $transactionReference;

    /**
     * @var string
     */
    protected string $merchantCode;

    public function __construct(
        protected string $publicKey,
        protected string $secretKey,
        protected string $environment
    ) {
        if (!$this->publicKey) {
            $this->log("Invalid API Token", Level::ERROR);
            return false;
        }

        if (!$this->secretKey) {
            $this->log("Invalid Notification Token", Level::ERROR);
            return false;
        }

        $this->environment = getenv('TABBY_ENVIRONMENT', 'sandbox');

        $this->log("Start Tabby Service");
    }

    /**
     * Set total amount value
     *
     * @param float $amount
     *
     * @return TabbyBaseService
     */
    public function setTotalAmount(float $amount): TabbyBaseService
    {
        $this->totalAmount = $this->formatMoney($amount);

        return $this;
    }

    /**
     * Prepare currency code
     *
     * @param string $currency
     *
     * @return TabbyBaseService
     */
    public function setCurrency(string $currency): TabbyBaseService
    {
        $this->currencyCode = $currency;
        
        return $this;
    }

    /**
     * Prepare the reference id
     *
     * @param string $orderReferenceId
     *
     * @return TabbyBaseService
     */
    public function setOrderReferenceId(
        string $orderReferenceId
    ): TabbyBaseService {
        $this->orderReferenceId = $orderReferenceId;

        return $this;
    }

    /**
     * Prepare the order number string
     *
     * @param string $orderNumber
     *
     * @return TabbyBaseService
     */
    public function setOrderNumber(string|int $orderNumber): TabbyBaseService
    {
        $this->orderNumber = $orderNumber;

        return $this;
    }

    /**
     * Set shipping address array
     *
     * @param array $shippingAddress
     *
     * @return TabbyBaseService
     */
    public function setShippingAddress(
        array $shippingAddress
    ): TabbyBaseService {
        $this->shippingAddress = $shippingAddress;

        return $this;
    }

    /**
     * Prepare the items collection.
     * and if the requested resource requires tax ammount
     * the method will set the tax amount as well.
     *
     * @param array $invoiceItems
     *
     * @return TabbyBaseService
     */
    public function setItems(array $invoiceItems): TabbyBaseService
    {
        $totalTaxes = 0;
        foreach ($invoiceItems as $invoiceItem) {
            $calculateItemDiscount = $this->calculateItemDiscount($invoiceItem);

            $this->items[] = [
                'title' => $invoiceItem['item'],
                'quantity' => (int)$invoiceItem['quantity'],
                'unit_price' => $this->formatMoney($invoiceItem['unit_price']),
                'discount_amount' => $this->formatMoney(
                    $calculateItemDiscount
                ),
                'category' => 'digital',
            ];

            $totalTaxes =+ $invoiceItem['summary_tax1'];
        }

        return $this->setTaxAmount($totalTaxes);
    }

    /**
     * Calculate item discount
     *
     * @param array $item
     *
     * @return float
     */
    private function calculateItemDiscount(array $item): float
    {
        return 0;
    }

    /**
     * Set discount amount
     *
     * @param float $discountAmount
     *
     * @return TabbyBaseService
     */
    public function setDiscountAmount(float $discountAmount): TabbyBaseService
    {
        $this->discountAmount = $this->formatMoney($discountAmount);

        return $this;
    }

    /**
     * Set the country iso 2 code
     *
     * @param string $countryCode
     *
     * @return TabbyBaseService
     */
    public function setCountryCode(string $countryCode): TabbyBaseService
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    /**
     * Set the merchant code.
     *
     * @param string $merchantCode
     *
     * @return TabbyBaseService
     */
    public function setMerchantCode(string $merchantCode): TabbyBaseService
    {
        $this->merchantCode = $merchantCode;

        return $this;
    }

    /**
     * Set the unique transaction reference that is
     * coming from the payment gatway.
     *
     * @param string $transactionReference
     *
     * @return TabbyBaseService
     */
    public function setTransactionReference(
        $transactionReference
    ): TabbyBaseService {
        $this->transactionReference = $transactionReference;

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function getHttpEnvironmentUri(): string
    {
        return $this->httpEnvironmentUris[
            $this->environment
        ] ?? 'https://api.tabby.ai/api/v2/';
    }

    /**
     * @inheritdoc
     */
    protected function getHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => sprintf(
                'Bearer %s',
                $this->secretKey,
            ),
        ];
    }

    /**
     * Helper function to format the float values to an accepted
     * format by Tabby
     *
     * @param float|string $number
     *
     * @return float
     */
    public function formatMoney(float|string $number): string
    {
        return round($number, 2);
    }

    /**
     * Factory logger
     *
     * @param string $message
     * @param string $level
     * @param array $context
     *
     * @return void
     */
    public function log($message, $level = null, array $context = []): void
    {
        Rollbar::log(
            $level ?? Level::INFO, "[Tabby] " . $message,
            $context ?? []
        );

        return;
    }

    public function getOrderDetails($orderId)
    {
        try {
            $request = $this->getHttpClient()->get(sprintf('/api/v2/payments/%s', $orderId), [
                'headers' => [
                    $this->getHeaders()
                ]
            ]);

            return json_decode(
                $request->getBody()->getContents(),
                true
            );
        } catch (ClientException $e) {

            $this->log("Get order details :: Error", context: [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage()
            ];
        }
    }

}
