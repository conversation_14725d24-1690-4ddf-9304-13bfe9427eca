<?php

namespace App\Services\PaymentGateways;

use ReflectionObject;

abstract class AbstractPaymentGateway
{
    /**
     * Check if the current instance has a give concern short name.
     * The short name is a predfined propery on the class.
     * In general, the concerns can be an attribute or an interface,
     * currently the method is only supports the handling of the attribute
     * concern and needs to be upgraded to be able to handle both of cases.
     *
     * @param string $concern
     *
     * @return bool
     */
    public function hasConcern($concern)
    {
        $classObject = new ReflectionObject($this);

        $attributes = $classObject->getAttributes(
            $this->resolveConcernShortName($concern)
        );

        foreach ($attributes as $attribute) {
            $attributeInstance = $attribute->newInstance();
            if ($concern == $attributeInstance->shortName) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get a payment gateway related concern instance.
     *
     * @param string $concern
     *
     * @return ?object
     */
    public function getConcern(string $concern): ?object
    {
        $classObject = new ReflectionObject($this);

        $attribute = $classObject->getAttributes(
            $this->resolveConcernShortName($concern)
        );

        return $attribute[0]?->newInstance();
    }

    /**
     * Helper method to resolve a given short concern name into a fully
     * resolved namespace.
     *
     * @param string $shortName
     *
     * @return string
     */
    private function resolveConcernShortName($shortName): string
    {
        return sprintf(
            "%s\\Concerns\\%sConcern",
            __NAMESPACE__,
            ucfirst($shortName)
        );
    }
}
