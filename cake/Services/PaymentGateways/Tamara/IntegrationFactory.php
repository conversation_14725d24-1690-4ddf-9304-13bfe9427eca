<?php

namespace App\Services\PaymentGateways\Tamara;

use Router;
use App\Services\PaymentGateways\Tamara\OnlineCheckoutService;

class IntegrationFactory
{

    private $allowedCountries = [
        'SA' => true,
        'KW' => true,
    ];
    public function validate_phone($phone, $country_code = null)
    {
        try {
            $phone = str_replace(['(', ')', '-', ' '], '', $phone);
            $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            $num = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            $phone = str_replace($arabic, $num, $phone);
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();

            $phoneBasedonCountry = $phoneUtil->parse($phone, $country_code);
            $number = $phoneUtil->isValidNumber($phoneBasedonCountry);
            $phone = $phoneUtil->getNumberType($phoneBasedonCountry);
            if ($number && $phone == \libphonenumber\PhoneNumberType::MOBILE) {
                return $phoneUtil->format($phoneBasedonCountry, \libphonenumber\PhoneNumberFormat::E164);
            } else {
                return false;
            }
        } catch (\Exception $e) {
            dump($e->getMessage());
            return false;
        }
    }

    public function createCheckout($client_data, $data, $phoneNumber)
    {
        if (getAuthClient()) {
            if (isset($this->allowedCountries[$data['InvoicePayment']['country_code']]) === false) {
                return ['status' => false, 'errorMsg' => __('Country is not valid!', true)];
            }

            if (!$this->validate_phone($phoneNumber, $data['InvoicePayment']['country_code'])){
                return ['status' => false, 'errorMsg' => __('Phone is not valid!', true)];
            }
            $res = $this->prepareDirectIntegration(array_merge($client_data, $data));

            return ['response' => $res, 'out' => true, 'url' => $res['checkout_url']];
        } else {
            if (isset($this->allowedCountries[$data['InvoicePayment']['country_code']]) === false) {
                return ['status' => false, 'errorMsg' => __('Country is not valid!', true)];
            }

            if (!$this->validate_phone($phoneNumber, $data['InvoicePayment']['country_code'])){
                return ['status' => false, 'errorMsg' => __('Phone is not valid!', true)];
            }

            $instoreService = new \App\Services\PaymentGateways\Tamara\TamaraInStoreService();

            $res = json_decode($instoreService->createCheckout($client_data, $data, $phoneNumber), true);
            return ['response' => $res, 'out' => false, 'url' => ''];
        }
    }

    private function prepareDirectIntegration($data)
    {
        $tamaraCheckout = $this->initDirectIntegration();

        $paymentData = $data['InvoicePayment'];

        $tamaraCheckout->setCurrency($paymentData['currency_code']);
        $tamaraCheckout->setCountryCode($paymentData['country_code']);
        $tamaraCheckout->setTotalAmount($paymentData['amount']);
        $tamaraCheckout->setDiscountAmount("0");
        $tamaraCheckout->setShippingAmount(0);
        $tamaraCheckout->setOrderReferenceId($paymentData['client_id']);
        $tamaraCheckout->setOrderNumber($paymentData['client_id']);

        $tamaraCheckout->setShippingAddress([
            'city' => $paymentData['city'],
            'country_code' => $paymentData['country_code'],
            'first_name' => $paymentData['first_name'] ?? '-',
            'last_name' => $paymentData['last_name'] ?? '-',
            'line1' => $paymentData['address1']
        ]);

        $tamaraCheckout->setCustomer([
            'email' => $paymentData['email'],
            'first_name' => $paymentData['first_name'] ?? '-',
            'last_name' => $paymentData['last_name'] ?? '-',
            'phone_number' => $paymentData['phone1'] ?? $paymentData['phone2'],
        ]);

        $tamaraCheckout->setMerchantUrls([
            'cancel' => Router::url([
                'controller' => 'invoices',
                'action' => 'processTamaraPayment',
                'client' => false,
                'cancel',
                'dashboard',
            ], true),
            'failure' => Router::url([
                'controller' => 'invoices',
                'action' => 'processTamaraPayment',
                'client' => false,
                'failure',
                'dashboard',
            ], true),
            'success' => Router::url([
                'controller' => 'invoices',
                'action' => 'processTamaraPayment',
                'client' => false,
                'success',
                'dashboard',
            ], true),
            'notification' => Router::url([
                'controller' => 'invoices',
                'action' => 'processTamaraPayment',
                'client' => false,
                'notification',
                'dashboard',
            ], true),
        ]);

        $item = [
            'item' => $paymentData['notes'] ?: $paymentData['source'],
            'product_id' => $paymentData['client_id'],
            'quantity' => 1,
            'unit_price' => $paymentData['amount'],
            'calculated_discount' => 0,
            'summary_tax1' => 0,
            'subtotal' => $paymentData['amount']
        ];
        $tamaraCheckout->setItems([$item]);

        $tamaraCheckout->setPaymentType('PAY_BY_INSTALMENTS');

        return $tamaraCheckout->createCheckoutSession();
    }

    private function initDirectIntegration()
    {
        $sitePaymentGateway = \ClassRegistry::init('SitePaymentGateway');
        $tamaraOptions = $sitePaymentGateway->find('first', [
            'conditions' => [
                'SitePaymentGateway.payment_gateway' => 'tamara'
            ]
        ]);
        $tamaraOptions = $tamaraOptions['SitePaymentGateway'];

        return new OnlineCheckoutService($tamaraOptions['username'], $tamaraOptions['option1']);
    }
}