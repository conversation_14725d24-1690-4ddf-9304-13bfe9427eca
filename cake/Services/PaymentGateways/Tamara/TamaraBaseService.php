<?php

namespace App\Services\PaymentGateways\Tamara;

use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Exception\ClientException;

use \libphonenumber\PhoneNumberUtil;

use \Rollbar\{
    Rollbar,
    Payload\Level,
};

/**
 * Base service for tamara payment gateway.
 *
 * @todo implement base http service and move the
 * http related code to it
 */
abstract class TamaraBaseService
{
    /**
     * @var mixed
     */
    protected $httpClient = null;

    /**
     * @var array
     */
    protected $httpEnvironmentUris = [
        'sandbox' => 'https://api-sandbox.tamara.co',
        'live' => 'https://api.tamara.co',
    ];

    /**
     * @var string
     */
    protected $environment = 'sandbox';

    /**
     * @var string
     */
    protected $currencyCode;

    /**
     * @var string
     */
    protected $countryCode;

    /**
     * @var string
     */
    protected $orderReferenceId;

    /**
     * @var string
     */
    protected $orderNumber;

    /**
     * @var array
     */
    protected $items;

    /**
     * @var string
     */
    protected $paymentType;

    /**
     * @var float
     */
    protected $discountAmount;

    /**
     * @var array
     */
    protected $additionalData;

    /**
     * Prepare the final payload that suit the requested endpoint.
     *
     * @return array
     */
    abstract protected function preparePayload();

    public function __construct(
        protected mixed $apiToken = null,
        protected mixed $notificationToken = null
    ) {
        if (!$this->apiToken) {
            $this->log("Invalid API Token", Level::ERROR);
            return false;
        }

        if (!$this->notificationToken) {
            $this->log("Invalid Notification Token", Level::ERROR);
            return false;
        }
    }

    /**
     * Prepare currency code
     *
     * @param string $currency
     *
     * @return TamaraBaseService
     */
    public function setCurrency(string $currency)
    {
        $this->currencyCode = $currency;
        
        return $this;
    }

    /**
     * Prepare the order reference id
     *
     * @param string $orderReferenceId
     *
     * @return TamaraBaseService
     */
    public function setOrderReferenceId(string $orderReferenceId)
    {
        $this->orderReferenceId = $orderReferenceId;

        return $this;
    }

    /**
     * Prepare the order number string
     *
     * @param string $orderNumber
     *
     * @return TamaraBaseService
     */
    public function setOrderNumber(string|int $orderNumber)
    {
        $this->orderNumber = $orderNumber;

        return $this;
    }

    /**
     * Set the additional data collection.
     *
     * @param array $additionalData
     *
     * @return TamaraBaseService
     */
    public function setAdditionalData(array $additionalData)
    {
        $this->additionalData = $additionalData;

        return $this;
    }

    /**
     * Prepare the items collection.
     * and if the requested resource requires tax ammount
     * the method will set the tax amount as well.
     *
     * @param array $invoiceItems
     *
     * @return TamaraBaseService
     */
    public function setItems(array $invoiceItems)
    {
        $totalTaxes = 0;
        foreach ($invoiceItems as $invoiceItem) {

            $calculateItemDiscount = $this->calculateItemDiscount($invoiceItem);

            $this->items[] = [
                'reference_id' => $this->orderReferenceId,
                'type' => 'digital',
                'name' => $invoiceItem['item'],
                'sku' => $invoiceItem['product_id'] ?: $invoiceItem['invoice_id'],
                'quantity' => $invoiceItem['quantity'],
                'unit_price' => [
                    'amount' => (float) $invoiceItem['unit_price'],
                    'currency' => $this->currencyCode
                ],
                'discount_amount' => [
                    'amount' => (float) $calculateItemDiscount,
                    'currency' => $this->currencyCode
                ],
                'tax_amount' => [
                    'amount' => (float) $invoiceItem['summary_tax1'],
                    'currency' => $this->currencyCode
                ],
                'total_amount' => [
                    'amount' => (float) ((
                        $invoiceItem['unit_price'] * $invoiceItem['quantity']
                    ) - $calculateItemDiscount),
                    'currency' => $this->currencyCode
                ]
            ];

            $totalTaxes += $invoiceItem['summary_tax1'];
        }

        if (method_exists($this, 'setTaxAmount')) {
            $this->setTaxAmount($totalTaxes);
        }

        return $this;
    }

    /**
     * Calculate item discount
     *
     * @param array $item
     *
     * @return float
     */
    private function calculateItemDiscount(array $item): float
    {
        if ((float)$item['discount'] <= 0) {
            return 0;
        }

        if ($item['discount_type'] == 2) {
            return (float) ($item['discount'] * $item['quantity']);
        }

        return (float) ((
            $item['unit_price'] * $item['quantity']
        ) / $item['discount']);
    }

    /**
     * Set discount amount
     *
     * @param float $discountAmount
     *
     * @return TamaraBaseService
     */
    public function setDiscountAmount(float $discountAmount)
    {
        $this->discountAmount = $discountAmount;

        return $this;
    }

    /**
     * Set the country iso 2 code
     *
     * @param string $countryCode
     *
     * @return TamaraBaseService
     */
    public function setCountryCode(string $countryCode)
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    /**
     * Set the payment type for the requested session
     *
     * @param string $paymentType
     *
     * @return TamaraBaseService
     */
    public function setPaymentType(string $paymentType)
    {
        $this->paymentType = $paymentType;

        return $this;
    }

    /**
     * Set HTTP client.
     * @todo use PSR interfaces to set the type of the passed client
     *
     * @param mixed $client
     *
     * @return void
     */
    public function setHttpClient($client)
    {
        $this->httpClient = $client;

        return $this;
    }

    /**
     * Get default HTTP environment url.
     *
     * @return string
     */
    protected function getHttpEnvironmentUri()
    {
        return $this->httpEnvironmentUris[
            getenv('TAMARA_ENIRONMENT', 'sandbox')
        ] ?? 'https://api-sandbox.tamara.co';
    }

    /**
     * Get default http timeout
     *
     * @return double
     */
    protected function getHttpTimeout()
    {
        return 2.0;
    }

    /**
     * Factory logger
     *
     * @param string $message
     * @param string $level
     * @param array $context
     *
     * @return void
     */
    public function log($message, $level = null, array $context = []): void
    {
        Rollbar::log($level ?? Level::INFO, "[Tamara] " . $message, $context ?? []);

        return;
    }

    /**
     * Factory method to use GuzzleHTTP as the
     * default HTTP client.
     *
     * @return void
     */
    protected function httpGuzzleClientFactory()
    {
        $this->setHttpClient(
            new GuzzleClient([
                'base_uri' => $this->getHttpEnvironmentUri(),
                'timeout' => $this->getHttpTimeout() ?? 2.0
            ])
        );
    }

    /**
     * Return http client, if the http client is not set yet
     * init it via the default factory method
     *
     * @return mixed
     */
    protected function getHttpClient()
    {
        if (!$this->httpClient) {
            $this->httpGuzzleClientFactory();
        }
        
        return $this->httpClient;
    }

    /**
     * Perform an inquiry for a specific order id
     * on Tamara's side
     *
     * @param string $orderId
     *
     * @return array
     */
    public function getOrderDetails($orderId)
    {
        try {
            $request = $this->getHttpClient()->get(sprintf('/orders/%s', $orderId), [
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $this->apiToken
                ]
            ]);

            return json_decode(
                $request->getBody()->getContents(),
                true
            );
        } catch (ClientException $e) {

            $this->log("Get order details :: Error", context: [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage()
            ];
        }
    }
}
