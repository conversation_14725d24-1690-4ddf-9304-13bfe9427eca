<?php

namespace App\Services\PaymentGateways\Tamara;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Rollbar\Rollbar;
use App\models\TamaraInStoreCheckout;

class TamaraInStoreService
{
    private $ENDPOINTS;

    private $client;
    public function __construct()
    {
        initRollbar();
        $PaymentGateway = \ClassRegistry::init('SitePaymentGateway');
        $pg = $PaymentGateway->find(array('payment_gateway' => 'tamara'), null, null, -1);
        $userOptions = $pg['SitePaymentGateway'];
        $this->client = new Client();
        $this->ENDPOINTS = [
            'inStoreLink' => '/checkout/in-store-session',
            'orderDetailsLink' => '/orders',
            'url' => (getenv("TAMARA_ENIRONMENT") == "sandbox") ? 'https://api-sandbox.tamara.co' : 'https://api.tamara.co',
            'api_token' => $userOptions['username']
        ];
    }

    public function createCheckout($client_data, $data, $phone_number)
    {
        $paymentData = $data['InvoicePayment'];

        if ($phone_number) {
            $phoneValidation = $this->validate_phone($phone_number, 'SA');
            $phone_number = $phoneValidation;
            if (!$phone_number) {
                return json_encode(array('status' => false, 'out' => false, 'error_message' => __('Phone is not valid!', true)));
            }
        } else {
            return json_encode(array('status' => false, 'out' => false, 'error_message' => __('Phone is not valid!', true)));
        }

        $calculateItemDiscount = function(array $item) {

            if ((float)$item['discount'] <= 0) {
                return 0;
            }

            if ($item['discount_type'] == 2) {
                return (float) ($item['discount'] * $item['quantity']);
            }

            return (float) ((
                $item['unit_price'] * $item['quantity']
            ) / $item['discount']);
        };

        $items = [];
        foreach ($data['InvoiceItem'] as $invoiceItem) {
            $item = [
                'reference_id' => $data['InvoicePayment']['invoice_id'] ?: $data['InvoicePayment']['code'],
                'type' => 'digital',
                'name' => $invoiceItem['item'],
                'sku' => $invoiceItem['product_id'] ?: $invoiceItem['invoice_id'],
                'quantity' => $invoiceItem['quantity'],
                'unit_price' => [
                    'amount' => (float) $invoiceItem['unit_price'],
                    'currency' => $paymentData['currency_code']
                ],
                'discount_amount' => [
                    'amount' => $calculateItemDiscount($invoiceItem),
                    'currency' => $paymentData['currency_code']
                ],
                'tax_amount' => [
                    'amount' => (float) $invoiceItem['summary_tax1'],
                    'currency' => $paymentData['currency_code']
                ],
                'total_amount' => [
                    'amount' => (float) $invoiceItem['subtotal'],
                    'currency' => $paymentData['currency_code']
                ]
            ];

            $items[] = $item;
        }

        $additional_data = [
            'store_code' => $client_data['Client']['business_name'],
        ];
        if (CurrentSiteLang() == "ara") {
            $lang = "ar_SA";
        } else {
            $lang = "en_US";
        }

        if ($client_data['Client']['type'] == \Client::$BUSINESS_CLIENT_TYPE){
            return json_encode(array('status' => false, 'out' => false, 'error_message' => __('Tamara is not accepting Business To Business payments', true)));
        } else {
            $payment_type = "PAY_BY_INSTALMENTS";
        }

        $in_store_checkout = new TamaraInStoreCheckout();
        $in_store_checkout->set_email($client_data['Client']['email'] ?: "<EMAIL>");
        $in_store_checkout->set_phone_number($phone_number);
        $in_store_checkout->set_payment_type($payment_type);
        $in_store_checkout->set_locale($lang);
        $in_store_checkout->set_total_amount($paymentData['amount'], $paymentData['currency_code']);
        $in_store_checkout->set_reference_id($paymentData['invoice_id']);
        $in_store_checkout->set_order_number($paymentData['invoice_id']);
        $in_store_checkout->set_items($items);
        $in_store_checkout->set_additional_data($additional_data);

        $data = [
            'total_amount' => $in_store_checkout->get_total_amount(),
            'phone_number' => $in_store_checkout->get_phone_number(),
            // 'email' => $in_store_checkout->get_email(),
            'email' => '<EMAIL>',
            'expiry_time' => $in_store_checkout->get_expiry_time(),
            'items' => $in_store_checkout->get_items(),
            'order_reference_id' => $in_store_checkout->get_reference_id(),
            'order_number' => $in_store_checkout->get_order_number(),
            'additional_data' => $in_store_checkout->get_additional_data(),
            'locale' => $in_store_checkout->get_locale(),
            'payment_type' => $in_store_checkout->get_payment_type()
        ];

        try {
            Rollbar::info('start Calling In Store Checkout For Tamara');
            $request = $this->client->post($this->ENDPOINTS['url'].$this->ENDPOINTS['inStoreLink'], [
                'body' => json_encode($data),
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'text/plain',
                    'authorization' => 'Bearer '.$this->ENDPOINTS['api_token']
                ]
            ]);
        } catch (ClientException $e){
            $res = json_decode($e->getResponse()->getBody()->getContents(), true)['message'];
            return json_encode(array('status' => false, 'out' => false, 'error_message' => $res));
        }
        return $request->getBody()->getContents();
    }

    public function getOrderDetails($order_id)
    {
        $request = $this->client->get($this->ENDPOINTS['url'].$this->ENDPOINTS['orderDetailsLink']."/".$order_id, [
            'headers' => [
                'content-type' => 'application/json',
                'accept' => 'application/json',
                'authorization' => 'Bearer '.$this->ENDPOINTS['api_token']
            ]
        ]);
        return json_decode($request->getBody()->getContents(), true);
    }

    public function voidTransaction($invoicePayment){
        $checkout_id = json_decode($invoicePayment['extra_details'], true)['checkout-id'];
        $transaction_id = $invoicePayment['transaction_id'];
        try {
            Rollbar::info('start Calling Void Transaction End Point');
            $request = $this->client->post($this->ENDPOINTS['url'].'/checkout/'.$checkout_id.'/void?order_id='.$transaction_id, [
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => 'Bearer '.$this->ENDPOINTS['api_token']
                ]
            ]);
        } catch (ClientException $e){
            $res = json_decode($e->getResponse()->getBody()->getContents(), true)['message'];
            Rollbar::error('Error in Voiding Transaction'. $res);
            return json_encode(array('status' => false, 'out' => false, 'error_message' => $res));
        }
        return $request->getBody()->getContents();
    }

    public function validate_phone($phone, $country_code = null)
    {
        try {
            $phone = str_replace(['(', ')', '-', ' '], '', $phone);
            $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            $num = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            $phone = str_replace($arabic, $num, $phone);
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();

            $phoneBasedonCountry = $phoneUtil->parse($phone, $country_code);
            $number = $phoneUtil->isValidNumber($phoneBasedonCountry);
            if ($number) {
                return $phoneUtil->format($phoneBasedonCountry, \libphonenumber\PhoneNumberFormat::E164);
            } else {
                return false;
            }
        } catch (\Exception $e) {
            dump($e->getMessage());
            return false;
        }
    }

}
