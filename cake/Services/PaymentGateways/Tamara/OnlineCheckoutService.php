<?php

namespace App\Services\PaymentGateways\Tamara;

use GuzzleHttp\Exception\ClientException;
use Rollbar\Payload\Level;

class OnlineCheckoutService extends TamaraBaseService
{
    /**
     * @var array
     */
    protected $customer;

    /**
     * @var array
     */
    protected $merchantUrls;

    /**
     * @var double
     */
    protected $totalAmount;

    /**
     * @var double
     */
    protected $shippingAmount;

    /**
     * @var double
     */
    protected $taxAmount;

    /**
     * @var array
     */
    protected $webhookEvents = [
        "order_approved",
        "order_declined",
        "order_authorised",
        "order_canceled",
        "order_captured",
        "order_refunded",
        "order_expired"
    ];

    public function __construct($apiToken, $notificationToken = '')
    {
        parent::__construct($apiToken, $notificationToken);
    }

    /**
     * Set the metchants urls array
     *
     * @param array $merchantUrls
     *
     * @return TamaraBaseService
     */
    public function setMerchantUrls($merchantUrls)
    {
        $this->merchantUrls = $merchantUrls;

        return $this;
    }

    /**
     * Set total amount value
     *
     * @param float $amount
     *
     * @return TamaraBaseService
     */
    public function setTotalAmount(float $amount)
    {
        $this->totalAmount = $amount;

        return $this;
    }

    /**
     * Set shipping amount value
     *
     * @param float $amount
     *
     * @return TamaraBaseService
     */
    public function setShippingAmount(float $amount)
    {
        $this->shippingAmount = $amount;

        return $this;
    }

    /**
     * Set tax amount value
     *
     * @param float $amount
     *
     * @return TamaraBaseService
     */
    public function setTaxAmount(float $amount)
    {
        $this->taxAmount = $amount;

        return $this;
    }

    /**
     * Set payload customer
     *
     * @param array $customer
     *
     * @return TamaraBaseService
     */
    public function setCustomer(array $customer)
    {
        $this->customer = $customer;

        return $this;
    }

    /**
     * Set shipping address array
     *
     * @param array $shippingAddress
     *
     * @return TamaraBaseService
     */
    public function setShippingAddress(array $shippingAddress)
    {
        $this->shippingAddress = $shippingAddress;

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function preparePayload()
    {
        return [
            'total_amount' => [
                'amount' => $this->totalAmount,
                'currency' => $this->currencyCode,
            ],
            'shipping_amount' => [
                'amount' => $this->shippingAmount,
                'currency' => $this->currencyCode,
            ],
            'tax_amount' => [
                'amount' => $this->taxAmount,
                'currency' => $this->currencyCode,
            ],
            'order_reference_id' => $this->orderReferenceId,
            'order_number' => $this->orderNumber,
            'discount' => [
                'name' => 'Invoice discount',
                'amount' => [
                    'amount' => $this->discountAmount,
                    'currency' => $this->currencyCode
                ]
            ],
            'items' => $this->items,
            'consumer' => $this->customer,
            'country_code' => $this->countryCode,
            'description' => $this->description ?? '-',
            'merchant_url' => $this->merchantUrls,
            'payment_type' => $this->paymentType,

            'instalments' => 3,
            'shipping_address' => $this->shippingAddress,
            'locale' => 'ar_SA',
            'additional_data' => $this->additionalData ?? [],
        ];
    }

    /**
     * Send request to create an online checkout session
     * and return the checkout response
     *
     * @return array
     */
    public function createCheckoutSession()
    {
        try {
            $request = $this->getHttpClient()->post('/checkout', [
                'body' => json_encode($this->preparePayload()),
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $this->apiToken
                ]
            ]);

            return json_decode(
                $request->getBody()->getContents(),
                true
            );
        } catch (ClientException $e) {

            $this->log("Get order details :: Error", context: [
                'payload' => $this->preparePayload(),
                'error' => $e->getMessage()
            ]);

            $error = $e->getResponse()->getBody()->getContents();

            try {
                $e = json_decode($error, true);
            } catch (\Exception $E){
                $this->log("Can't decode the respones from API", context: [
                    'payload' => $this->preparePayload(),
                    'error' => $E->getMessage()
                ]);
            }
            return [
                'error' => $e['message']
            ];
        }
    }

    /**
     * Authorize order using order id
     *
     * @param string $orderId
     *
     * @return array
     * @todo move this into a separated service
     */
    public function authorizeOrder($orderId)
    {
        try {
            $endpoint = sprintf('/orders/%s/authorise', $orderId);
            $request = $this->getHttpClient()->post($endpoint, [
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $this->apiToken
                ]
            ]);

            return json_decode(
                $request->getBody()->getContents(),
                true
            );
        } catch (ClientException $e) {

            $this->log("Get order details :: Error", context: [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Prepare the final payload that suit the capture payment endpoint.
     *
     * @return array
     */
    protected function prepareCapturePaymentPayload($orderDetailsPayload)
    {
        $payload = [
            'order_id' => $orderDetailsPayload['order_id'],
            'total_amount' => $orderDetailsPayload['total_amount'],
            'items' => $orderDetailsPayload['items'],
            'shipping_amount' => $orderDetailsPayload['shipping_amount'],
            'shipping_info' => [
                'shipped_at' => date('c'),
                'shipping_company' => 'NONE',
            ],
            'tax_amount' => $orderDetailsPayload['tax_amount'],
        ];

        if (
            !empty($orderDetailsPayload['discount_amount']['name'])
        ) {
            $payload['discount_amount'] = $orderDetailsPayload['discount_amount']['amount'];
        }

        return $payload;
    }

    /**
     * Send capture order request
     *
     * @param array $orderDetailsPayload
     *
     * @return array
     * @todo move this with prepareCapturePaymentPayload into a separated service
     */
    public function captureOrder($orderDetailsPayload)
    {
        try {
            $request = $this->getHttpClient()->post('/payments/capture', [
                'body' => json_encode(
                    $this->prepareCapturePaymentPayload($orderDetailsPayload)
                ),
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $this->apiToken
                ]
            ]);

            return json_decode(
                $request->getBody()->getContents(),
                true
            );
        } catch (ClientException $e) {

            $this->log("Get order details :: Error", context: [
                'payload' => $this->prepareCapturePaymentPayload(
                    $orderDetailsPayload
                ),
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Signup a new webhook endpoint
     *
     * @param string $url
     *
     * @return array
     * @todo move this into a separated service
     */
    public function signupWebhook($url)
    {
        try {
            $request = $this->getHttpClient()->post('/webhooks', [
                'body' => json_encode([
                    'url' => $url,
                    'events' => $this->webhookEvents,
                    'headers' => []
                ]),
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $this->apiToken
                ]
            ]);

            return json_decode(
                $request->getBody()->getContents(),
                true
            );
        } catch (ClientException $e) {
            $response = $e->getResponse()->getBody()->getContents();
            $response = json_decode($response, true);
            
            if ($response['errors'][0]['error_code'] == 'webhook_already_registered') {
                return [
                    'webhook_id' => $response['errors'][0]['data']['webhook_id']
                ];
            }
            
            return [
                'error' => $e->getMessage()
            ];
        }
    }
}
