<?php

namespace App\Services\PaymentGateways\Tamara;

use GuzzleHttp\Exception\ClientException;
use Rollbar\Payload\Level;

class RefundService extends TamaraBaseService
{
    /**
     * @var float
     */
    protected $totalAmount;

    /**
     * @var string
     */
    protected $comment = null;

    /**
     * @var string
     */
    protected $orderId;

    /**
     * Set the refund total amount.
     *
     * @param float $totalAmount
     *
     * @return TamaraBaseService
     */
    public function setTotalAmount(float $totalAmount)
    {
        $this->totalAmount = $totalAmount;

        return $this;
    }

    /**
     * Set the refund process comment.
     *
     * @param string $comment
     *
     * @return TamaraBaseService
     */
    public function setComment($comment)
    {
        $this->comment = $comment;

        return $this;
    }

    /**
     * Set order id that needs to be refunded.
     *
     * @param string $orderId
     *
     * @return TamaraBaseService
     */
    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function preparePayload()
    {
        return [
            'total_amount' => [
                'amount' => $this->totalAmount,
                'currency' => $this->currencyCode
            ],
            'comment' => $this->comment ?: 'Refund for an order'
        ];
    }

    /**
     * Process the refund service.
     *
     * @return array
     */
    public function processService(): array
    {
        try {
            $request = $this->getHttpClient()->post(sprintf(
                '/payments/simplified-refund/%s',
                $this->orderId,
            ), [
                'body' => json_encode($this->preparePayload()),
                'headers' => [
                    'content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $this->apiToken
                ]
            ]);

            return json_decode(
                $request->getBody()->getContents(),
                true
            );
        } catch (ClientException $e) {
            $errors = json_decode(
                $e->getResponse()->getBody()->getContents(),
                true
            );

            $this->log("Refund process :: Error", context: [
                'error' => $errors
            ]);

            return [
                'status' => 'ERR',
                'error' => $errors,
            ];
        }
    }
}
