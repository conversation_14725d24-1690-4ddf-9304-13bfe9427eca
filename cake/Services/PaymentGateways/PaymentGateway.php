<?php

namespace App\Services\PaymentGateways;

class PaymentGateway
{
    /**
     * Factory method to create an instance for a given payment gateway.
     *
     * @param string $paymentGateway
     * @param array $options
     *
     * @return PaymentGatewayInterface
     */
    public static function create(
        string $paymentGateway, array $options = []
    ): PaymentGatewayInterface {
        $gatewayClass = self::resolve($paymentGateway);
        $gatewayObject = new $gatewayClass($options);
        return $gatewayObject;
    }

    /**
     * Resolve a given class string into a fully resolved payment gateway
     * class namespace.
     *
     * @param string $paymentGateway
     *
     * @return string
     */
    private static function resolve(string $paymentGateway): string
    {
        $class = sprintf(
            "%s\\Gateways\\%s\\Gateway",
            __NAMESPACE__,
            ucfirst($paymentGateway)
        );

        return $class;
    }
}
