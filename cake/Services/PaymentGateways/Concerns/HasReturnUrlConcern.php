<?php

namespace App\Services\PaymentGateways\Concerns;

use Attribute;

#[Attribute(Attribute::TARGET_CLASS)]
class HasReturnUrlConcern
{
    /**
     * Initialize the concern. The short name parameter is used to identify
     * the concern, so we can call retrieve it using that short name.
     * The gatewayReturnUrlUniqueParameter parameter is used to identify the
     * query param that the gateway will pass to the return url, this is useful
     * to be used on the return url callback function.
     *
     * @param ?string $shortName
     * @param ?string $gatewayReturnUrlUniqueParameter
     */
    public function __construct(
        public ?string $shortName = null,
        private ?string $gatewayReturnUrlUniqueParameter = null,
    ) {}

    /**
     * Returns the gateway return url.
     *
     * @return string
     */
    public function getUniqueKey(): string
    {
        return $this->gatewayReturnUrlUniqueParameter;
    }
}
