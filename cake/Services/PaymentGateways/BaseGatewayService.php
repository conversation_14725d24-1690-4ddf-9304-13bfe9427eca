<?php

namespace App\Services\PaymentGateways;

use Guzzle<PERSON>ttp\Client as GuzzleClient;
use GuzzleHttp\Exception\ClientException;

abstract class BaseGatewayService
{
    /**
     * @var mixed
     */
    protected $httpClient = null;

    /**
     * Prepare the final payload that suit the requested endpoint.
     *
     * @return array
     */
    abstract protected function preparePayload(): array;

    /**
     * Process the main service function
     *
     * @return array
     */
    abstract public function processService(): array;

    /**
     * Get default HTTP environment url.
     *
     * @return string
     */
    abstract protected function getHttpEnvironmentUri();

    /**
     * Get connection headers to be used with the http client.
     *
     * @return array
     */
    abstract protected function getHeaders();

    /**
     * Set HTTP client.
     * @todo use PSR interfaces to set the type of the passed client
     *
     * @param mixed $client
     *
     * @return void
     */
    public function setHttpClient($client)
    {
        $this->httpClient = $client;

        return $this;
    }

    /**
     * Factory method to use <PERSON>uz<PERSON>HTTP as the
     * default HTTP client.
     *
     * @return void
     */
    protected function httpGuzzleClientFactory()
    {
        $this->setHttpClient(
            new GuzzleClient([
                'base_uri' => $this->getHttpEnvironmentUri(),
                'headers' => $this->getHeaders(),
            ])
        );
    }

    /**
     * Return http client, if the http client is not set yet
     * init it via the default factory method
     *
     * @return mixed
     */
    protected function getHttpClient()
    {
        if (!$this->httpClient) {
            $this->httpGuzzleClientFactory();
        }
        
        return $this->httpClient;
    }
}
