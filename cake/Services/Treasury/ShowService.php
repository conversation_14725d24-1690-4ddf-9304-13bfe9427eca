<?php

namespace App\Services\Treasury;

use Izam\Aws\Aws;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\TreasuryTypeUtil;
use Izam\Entity\Service\S3FileManager;
use Izam\Template\TemplateRepository;
use Izam\View\Form\Element\Header;
use Izam\View\Form\Element\Icon;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\ImageHeader;
use Izam\View\Form\Element\Pagination;
use Izam\View\Form\Element\Sperator;
use Izam\View\Form\Element\SplitButtonDropdowns;
use Izam\View\Form\Element\Stack;
use Izam\View\Form\Element\StatusCircle;
use Izam\View\Form\Element\TitleText;
use Izam\View\Form\Element\ViewActions;
use Izam\View\Form\Tab\IframeElement;
use Laminas\Form\Element;
use Laminas\Form\Element\Button;

class ShowService
{
    public static function getTabs($data, $details): array
    {
        $data['details'] = $details;
        $tabs = [
            [
                'type' => Element::class,
                'name' => 'details',
                'label' => __t('Details'),
                'value' => [
                    "viewPath" => "treasuries/partials/details",
                    "context" => ['data' => $data],
                ],
            ],
            [
                'type' => IframeElement::class,
                'name' => 'system-transactions',
                'label' => __t('System Transactions'),
                'attributes' => [
                    "src" => "/v2/owner/banks/" . $data['Treasury']['id'] . "/system_transactions?iframe=1&show_filters=1",
                ]
            ],
        ];

        if ($data['Treasury']['type'] == \Treasury::TYPE_BANK) {
            $params = ['iframe' => 1, 'bank_id' => $data['Treasury']['id'], 'show_filters' => 1];
            if (isset($_GET['redirect-back']) && 'bank-transactions' == $_GET['redirect-back']) {
                $temp = $_SESSION['iframeRedirectBackParams'] ?? [];
                unset($_SESSION['iframeRedirectBackParams']);
                $temp['show_filters'] = 1;
                $params = array_merge($params, $temp);
            }
            $tabs[] = [
                'type' => IframeElement::class,
                'name' => 'bank-transactions',
                'label' => __t('Bank Statement'),
                'attributes' => [
                    "src" => "/v2/owner/bank_transactions?" . http_build_query($params),
                ]
            ];
        }
        $tabs[] = [
            'type' => IframeElement::class,
            'name' => 'transfers',
            'label' => __t('Transfers'),
            'attributes' => [
                "src" => \Router::url(['controller' => 'treasuries', 'action' => 'list_transfers', $data['Treasury']['id']])."?iframe=1&show_filters=1",
            ]
        ];

        $tabs[] = [
            'type' => IframeElement::class,
            'name' => 'activity-log',
            'label' => __t('Activity Log'),
            'attributes' => [
                "src" => \Router::url(['action' => 'timeline', $data['Treasury']['id'], '?' => ['box' => 1, 'layout2022' => 1]]),
            ]
        ];
        return $tabs;
    }

    public static function getActions($data): ViewActions
    {
        $viewActions = new ViewActions('View-Action');

        $editAnchor = new Anchor('edit-action');
        $editAnchor->setLabel(__t('Edit'))
            ->setAttribute('href', \Router::url([
                'controller' => 'treasuries',
                'action' => 'edit' , $data['Treasury']['id']
            ]))
            ->setOption('icon', 'pencil');

        $viewActions
            ->add($editAnchor);

        if (check_permission(Edit_General_Settings) || check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
            if (isset($data['Treasury']['metainfo']['callback']['connection_id'])) {
                /** @todo add disconnect as ajax */
                /*$disconnectButton = new Button

                $viewActions
                    ->add($disconnectButton);*/

                $disconnectBtn = new Element\Button('disconnect');
                $disconnectBtn
                    ->setOption('action', "/v2/owner/entity/leave_application/$data->id/update-status")
                    ->setOption('color', 'danger')
                    ->setOption('modal', 'disconnect-modal')
                    ->setLabel(__t('Reject'))
                    ->setOption('icon', 'close-circle');

                $viewActions
                    ->add($disconnectBtn);

            }
        }

        if ($data['Treasury']['active'] == 1) {
            $deactivateBtn = new Anchor('deactivate-action');
            $deactivateBtn->setLabel(__t('Deactivate'))
                ->setAttribute('href', \Router::url([
                    'controller' => 'treasuries',
                    'action' => 'mark_active' , $data['Treasury']['id'], 0
                ]))
                ->setOption('icon', 'stop-circle');

            $viewActions
                ->add($deactivateBtn);

            $transferAnchor = new Anchor('transfer-action');
            $transferAnchor
                ->setLabel(__t('Transfer'))
                ->setAttribute('href', \Router::url([
                    'controller' => 'treasuries',
                    'action' => 'transfer', '?' => "from_treasury_id=" . $data['Treasury']['id']
                ]))
                ->setOption('icon', 'swap-horizontal-bold');;

            $viewActions->add($transferAnchor);
        }

        if ($data['Treasury']['active'] == 0) {
            $activateBtn = new Anchor('activate-action');
            $activateBtn->setLabel(__t('Activate'))
                ->setAttribute('href', \Router::url([
                    'controller' => 'treasuries',
                    'action' => 'mark_active' , $data['Treasury']['id'], 1
                ]))
                ->setOption('icon', 'play-circle');
            $viewActions
                ->add($activateBtn);
        }

        if ($data['Treasury']['is_primary'] != 1 && check_permission(Edit_General_Settings)) {
            $markPrimaryAnchor = new Anchor('primary-action');
            $markPrimaryAnchor->setLabel(__t('Mark as primary'))
                ->setAttribute('href', \Router::url([
                    'controller' => 'treasuries',
                    'action' => 'mark_primary' , $data['Treasury']['id'], 1
                ]))
                ->setOption('icon', 'flag');

            $viewActions->add($markPrimaryAnchor);
        }

        if (check_permission(Edit_General_Settings) || check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
            if ($data['Treasury']['type'] == \Treasury::TYPE_BANK) {
                if (!empty($data['Treasury']['currency_code'])) {
                    $importAnchor = new Anchor('import-action');
                    $importAnchor
                        ->setLabel(__t('Import Bank Statement'))
                        ->setOption('icon', 'database-import')
                        ->setAttribute('href', '/v2/owner/import/bank_transaction?extraData[bank_id]=' . $data['Treasury']['id']);
                    $viewActions->add($importAnchor);

                } else {
                    /** @todo continue implementing modal */
                    $importBtn = new Element\Button('import');
                    $importBtn
                        ->setOption('action', "/v2/owner/entity/leave_application/$data->id/update-status")
                        ->setOption('color', 'danger')
                        ->setOption('modal', 'reject-modal')
                        ->setLabel(__t('Reject'))
                        ->setOption('icon', 'close-circle');

                }
            }

            if (isset($data['Treasury']['metainfo']['callback']['connection_id'])) {
                $treasuryMetaInfo = $data['Treasury']['metainfo'];

                $url = sprintf(
                    "/v2/owner/banking/reconciliation/pull/%s?extraData[bank_id]=%s",
                    $treasuryMetaInfo['provider'],
                    $data['Treasury']['id']
                );

                $pullTransactionsAnchor = new Anchor('pull-transactions-action');
                $pullTransactionsAnchor
                    ->setLabel(__t('Pull transactions'))
                    ->setOption('icon', 'database-import')
                    ->setAttribute('href', $url);
                $viewActions->add($pullTransactionsAnchor);
            }
        }

        $deleteBtn = new Button('delete-action');

        $label = $data['Treasury']['type'] == \Treasury::TYPE_BANK ? 'Bank' : 'Treasury';

        $deleteBtn
            ->setOption('message', sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t($label)))
            ->setOption('action', \Router::url(['controller' => 'treasuries', 'action' => 'delete', $data['Treasury']['id']]))
            ->setOption('theme', 'delete')
            ->setLabel(__t('Delete'))
            ->setOption('icon', 'trash-can');

        $viewActions->add($deleteBtn);

        $repo = new TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates(EntityKeyTypesUtil::TREASURY);
        if (!empty($viewTemplates['global']) || !empty($viewTemplates['local'])) {
            $printableBtn = new SplitButtonDropdowns('Printable-dropdown');
            $printableBtn->setLabel('Printable');
            foreach ($viewTemplates['global'] as  $template) {
                $tempGlobalAnchor = new Anchor('global-template-'.$template['id']);
                $tempGlobalAnchor->setLabel($template['name'])
                    ->setAttribute('href', show_printable_template_view_page($template['id'], $data['Treasury']['id'], $template['name']) . '?template=global');
                $printableBtn->add($tempGlobalAnchor);
            }

            foreach ($viewTemplates['local'] as $template) {
                $tempLocalAnchor = new Anchor('local-template-' . $template['id']);
                $tempLocalAnchor->setLabel($template['name'])
                    ->setAttribute('href', show_printable_template_view_page($template['id'], $data['Treasury']['id'], $template['name']) . '?template=local');
                $printableBtn->add($tempLocalAnchor);
            }
            $viewActions->add($printableBtn);
        }

        return $viewActions;
    }

    public static function getPageHeader($data, $details, $viewVars): Header
    {
        $account = $details['account'];


        if (!empty($data['Treasury']['metainfo']['bank']['portal_bank']['bank_logo'])){
            $iconTitle = new ImageHeader('icon-title');
            $iconTitle->setAttribute('src', Aws::getPermanentUrl($data['Treasury']['metainfo']['bank']['portal_bank']['bank_logo']))
                ->setAttribute('alt', $data['Treasury']['name'])
                ->setAttribute('class', 'thumb-sm thumb')
                ->setAttribute("loading", "lazy");
        }else{
            $iconTitle = new Icon('icon-title');
            if (\Treasury::TYPE_BANK == $data['Treasury']['type']) {
                $iconTitle->setOption('color', 'success')
                    ->setOption('icon', 'bank');
            } else {
                $iconTitle->setOption('color', 'primary')
                    ->setOption('icon', 'wallet');
            }
        }


        $pageOperator = new Sperator('operator');

        $leftStack = new Stack('Left-Stack');
        $leftStack->setOption('theme', 'theme4');
        $treasuryStatus = new StatusCircle('Unit-Status');
        if (1 == $data['Treasury']['active']) {
            $treasuryStatus->setLabel(__t('Active'))
                ->setOption('color', 'active');
        } else {
            $treasuryStatus->setLabel(__t('Inactive'))
                ->setOption('color', 'inactive');
        }
        $leftStack->add($treasuryStatus);
        $pagePagination = new Pagination('View-Pagination');
        $paginationNextBtn = new Anchor('Pagination-Next-Btn');
        $paginationNextBtn->setLabel(__t('Next'));
        $paginationNextBtn->setAttribute('href', $viewVars['next_url']);
        $paginationNextBtn->setOption('icon', 'chevron-down');
        if (!$viewVars['next_url']) {
            $paginationNextBtn->setOption('disabled', 'disabled');
        }

        $paginationPreviousBtn = new Anchor('Pagination-Previous-Btn');
        $paginationPreviousBtn->setLabel(__t('Previous'));
        $paginationPreviousBtn->setAttribute('href', $viewVars['prev_url']);
        $paginationPreviousBtn->setOption('icon', 'chevron-up');
        if (!$viewVars['prev_url']) {
            $paginationPreviousBtn->setOption('disabled', 'disabled');
        }

        $pagePagination->add($paginationPreviousBtn)
            ->add($paginationNextBtn);

        $rightStack = new Stack('left-Stack');
        $rightStack->setOption('theme', 'theme4');

        $totalBalance = $details['total_balance'];

        if (!empty($details['show_price_tooltips'])) {
            $toLocalText = format_price($totalBalance['to_local']['value'], $totalBalance['to_local']['currency']);
            $tooltipsTitle = 1 . $details['system_currency'] . ' = ' . $totalBalance['to_local']['rate_val'] . $totalBalance['treasury']['currency'];

            $priceToolTipValue = sprintf('
            <div class="text-start">
                <abbr class="fs-8 text-light-3" title="%s">%s
                    <i class="mdi mdi-help-circle"></i>
                </abbr>
            </div>', $tooltipsTitle, $toLocalText);
        }

        $totalBalanceValue = sprintf('
            <div class="hstack gap-6" >
                <span class="vr opacity-100 %s" style="--bs-border-width: 5px"></span>
                <span>
                    <span class="hstack gap-6" style="display: inline-block">
                        <span class="text-amount">%s</span>
                        <span class="text-light-3" style="display: block; text-align: right; font-weight: bold">%s</span>    
                    </span>
                    %s
                </span>
            </div>',
            $totalBalance['treasury']['value'] > 0 ? 'bg-success' : 'bg-danger',
            format_price_simple($totalBalance['treasury']['value'], $totalBalance['treasury']['currency']),
            $totalBalance['treasury']['currency'],
            $priceToolTipValue ?? ''
        );

        $totalBalanceElement = new Element('total-balance');
        $totalBalanceElement->setLabel($totalBalanceValue);

        if ($data['Treasury']['type'] == TreasuryTypeUtil::BANK && !empty($data['Treasury']['currency_code']) && !empty($details['not_matched_transactions'])) {
            $btnBankMatch = new Anchor('match-bank');
            $btnBankMatch->setLabel(__t('Match'))
                ->setOption('theme', 'primary')
                ->setOption('removeInnerText', true)
                ->setOption('size', 'sm')
                ->setAttribute('href', "/v2/owner/banks/" . $data['Treasury']['id'] . "/match");
            $rightStack
                ->add($btnBankMatch);
        }

        $ledgerReportAnchor = new Anchor('ledger-report');
        $ledgerReportAnchor->setOption('theme','secondary');
        $ledgerReportAnchor->setOption('icon', 'chart-pie');
        $ledgerReportAnchor->setAttribute('href', "/owner/reports/journal_transactions/?".http_build_query([
                'show_net' => '',
                'report_type' => 'transaction',
                'journal_cat_id' => '',
                'journal_account_id' => $account['JournalAccount']['id'],
                'currency' => -1
            ]));
        $ledgerReportAnchor->setAttribute('target', '_blank');
        $rightStack
            ->add($totalBalanceElement)
            ->add($ledgerReportAnchor);

        $header = new Header('page-header');

        $header
            ->setOption('wrapperSize', 'col-6')
            ->addLeft($iconTitle)
            ->addLeft(self::getTitleElement($data, $account))
            ->addLeft($pageOperator)
            ->addLeft($leftStack)
            ->addRight($rightStack)
            ->addRight($pagePagination)
        ;

        return $header;
    }

    private static function getTitleElement($data, $account)
    {
        $pageTitle = new TitleText('page-title');
        $pageTitle->setTitle($data['Treasury']['name'])
            ->setOption('shrink', 'flex-shrink-1');

        if ($data['Treasury']['is_primary']) {
            $primaryHtml = sprintf(
                '<span class="status-icon">
                <i class="mdi mdi-flag text-primary"></i>
                <span class="text-dark-3">%s</span>
            </span>',
                __t('Primary')
            );
            $pageTitle->setSecondaryInfo($primaryHtml);
        }

        if (self::isAccountingPluginActive($account)) {
            $ledgerHtml = sprintf(
                '%s: <a class="text-light-3" href="%s" target="_blank">%s</a>',
                __t("Ledger Account"),
                '/v2/owner/chart-of-accounts/accounts/' . $account['JournalAccount']['id'],
                $account['JournalAccount']['name'] . ' #' . $account['JournalAccount']['code']
            );
            $pageTitle->setSubtitle($ledgerHtml);
        }

        return $pageTitle;
    }

    private static function isAccountingPluginActive($account)
    {
        return ifPluginActive(AccountingPlugin) && check_permission(VIEW_ALL_JOURNALS) && !empty($account['JournalAccount']['id']);
    }
}