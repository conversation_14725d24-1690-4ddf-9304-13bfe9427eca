<?php

namespace App\Services\LocalEntityForm;

use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\AppEntitiesFormErrorHandlerDecorator;
use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Database\Capsule\Manager as DB;

class AdditionalFormsHandler
{
    private CustomFieldsFormService $customFieldsFormService;
    private AppEntitiesFormErrorHandlerDecorator $appEntitiesFormService;

    public function __construct(string $baseEntityKey)
    {
        $this->customFieldsFormService = new CustomFieldsFormService($baseEntityKey);
        $this->appEntitiesFormService = new AppEntitiesFormErrorHandlerDecorator($baseEntityKey);
    }

    public function getEditForms(int $referenceId, array $submittedData): array
    {
        $additionalFieldsForm = $this->customFieldsFormService->getEditForm($referenceId, $submittedData);

        $appEntitiesFieldsForms = $this->appEntitiesFormService->getAppsEditForms($referenceId, $submittedData);

        return $additionalFieldsForm ? array_merge([$additionalFieldsForm], $appEntitiesFieldsForms) : $appEntitiesFieldsForms;
    }

    public function validate(array $data, $skipRequired = false): bool
    {
        return $this->customFieldsFormService->validate($data, $skipRequired) & $this->appEntitiesFormService->validate($data);
    }

    public function store(int $referenceId, array $data): void
    {
        $this->customFieldsFormService->store($referenceId, $data);
        $this->appEntitiesFormService->save($referenceId, $data);
    }

    public function update(int $referenceId, array $data): void
    {
        /** get custom record id if exists. fix duplicate custom data in api update  */
        $customRecordRow = $this->getCustomDataRecord($referenceId);
        if ($customRecordRow) {
            $data[$this->getCustomEntityKey()]['id'] = $customRecordRow->id;
        }

        $this->customFieldsFormService->updateCustomData($referenceId, $data);
        $this->appEntitiesFormService->update($referenceId, $data);
    }

    public function getCreateForms(array $submittedData): array
    {
        $additionalFieldsForm = $this->customFieldsFormService->getCreateForm($submittedData);

        $appEntitiesFieldsForms = $this->appEntitiesFormService->getAppsCreateForms($submittedData);

        return $additionalFieldsForm ? array_merge([$additionalFieldsForm], $appEntitiesFieldsForms) : $appEntitiesFieldsForms;
    }

    public function show(int $referenceId): array
    {
        $additionalFieldsShowForm = $this->customFieldsFormService->show($referenceId);

        $appEntitiesFieldsShowForm = $this->appEntitiesFormService->show($referenceId);

        return $additionalFieldsShowForm ? array_merge($appEntitiesFieldsShowForm, [$additionalFieldsShowForm]) : $appEntitiesFieldsShowForm;
    }

    public function formatForImport($submittedData): array
    {
        return $this->customFieldsFormService->formatForImport($submittedData);
    }

    public function getImportFields(): array
    {
        $additionalFieldsImportFields = $this->customFieldsFormService->getImportFields();

        $appEntitiesFieldsImportFields = $this->appEntitiesFormService->getImportFields();

        return $additionalFieldsImportFields ? array_merge($additionalFieldsImportFields, $appEntitiesFieldsImportFields) : $appEntitiesFieldsImportFields;
    }

    public function getValidationErrors(): array
    {
        return array_merge(
            $this->customFieldsFormService->getValidationErrors(), $this->appEntitiesFormService->getValidationErrors()
        );
    }

    public function getCustomEntityKey()
    {
        return $this->customFieldsFormService->getEntityKey();
    }

    public function getCustomDataRecord($referenceId)
    {
        $showAction = resolve(AppEntityShowAction::class);
        try {
            return $showAction->handleBy2($this->getCustomEntityKey(), ['reference_id' => $referenceId], 1);
        } catch (\Throwable) {
            return [];
        }
    }

    public function deleteRelatedCustomFieldRecords($referenceId): void
    {
        $customDataTableName = $this->getCustomEntityKey();
        try {
            DB::table($customDataTableName)->where('reference_id', $referenceId)->delete();
        } catch (\Throwable) {
        }
    }
}
