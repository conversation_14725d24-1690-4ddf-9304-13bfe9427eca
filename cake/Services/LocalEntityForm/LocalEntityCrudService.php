<?php

namespace App\Services\LocalEntityForm;

use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Daftra\Common\Exception\EntityRecordNotFound;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use Izam\Daftra\Validator\Services\EntityRulesGetter;
use Izam\Daftra\Validator\Validators\EntityValidator;
use Izam\Entity\EntitySaver\IEntitySaver;
use Izam\Entity\EntitySaver\SaverManager;
use Izam\Entity\Formatter\EntityFormatter\EntityFormFormatter;
use Izam\Entity\Formatter\EntityFormatter\EntityItemFormatter;
use Izam\Entity\Formatter\EntityFormatter\IEntityItemFormatter;
use Izam\Entity\Formatter\FormatterManger;
use Izam\Entity\Repositories\SchemaSystemRepository;
use Izam\Entity\Repository\AppEntityMetaRepository;
use Izam\Entity\Repository\AppEntityMetaRepositoryInterface;
use Izam\Entity\Service\EntityToForm;
use Izam\Entity\Service\SpecBuilder;
use Izam\Entity\Service\ViewMetaDataGenerator;
use Laminas\Form\FormInterface;
use Throwable;

class LocalEntityCrudService
{
    private \CakeSession $session;
    private SchemaSystemRepository $schemaSystemRepository;
    private IEntitySaver $saver;
    private AppEntityMetaRepositoryInterface $appEntityMetaRepository;
    private string $entityKey;
    private array $importableFields = [
        EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_TEXTAREA, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_URL, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_EMAIL, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_PHONE_NUMBER, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_NUMBER, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_CHECKBOX, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_FORMATTED_TEXT, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_CURRENCY_DROPDOWN, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_DROP_DOWN, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DROPDOWN, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_DATE, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_TIME, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_DYNAMIC_DROPDOWN, 
        EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN, 
    ];

    public function __construct(string $entityKey)
    {
        $this->entityKey = $entityKey;
        $this->session = new \CakeSession();
        $this->schemaSystemRepository = new SchemaSystemRepository();
        $repository = $this->appEntityMetaRepository = new AppEntityMetaRepository();
        $this->saver = new SaverManager($repository);
    }

    public function getCreateForm(array $submittedData, string $label): ?FormInterface
    {
        if (!$this->entityExist()) {
            return null;
        }

        if (isset($submittedData[$this->entityKey])) {
            $formData = $submittedData[$this->entityKey];
        }

        return $this->createForm($formData ?? [], $label);
    }

    public function store(int $referenceId, array $data): void
    {
        if (!$this->entityExist()) {
            return ;
        }

        $data['reference_id'] = $referenceId;

        $structureGetter = getEntityBuilder();
        $entity = $structureGetter->buildEntity($this->entityKey);

        $formattedRequest = FormatterManger::format($data, $this->entityKey, $this->appEntityMetaRepository);

        $this->saver->save($entity, $formattedRequest);
    }

    public function isValid(array $data, $skipRequired = false): bool
    {
        if (!$this->entityExist()) {
            return true;
        }

        $daftraValidator = new EntityValidator($this->entityKey, new EntityRulesGetter(new AppEntityMetaRepository()));

        $valid = $daftraValidator->isValid($data, $skipRequired);

        if (!$valid) {
            $this->session->write($this->entityKey, $daftraValidator->getErrors());
            return false;
        }

        return true;
    }

    public function getEditForm(int $referenceId, array $submittedData, string $label): ?FormInterface
    {
        if (!$this->entityExist()) {
            return null;
        }
        $oldData = $this->formatRecord($referenceId, new EntityFormFormatter());

        $form = $this->createForm($oldData, $label);

        /** there's a validation error */
        if (isset($submittedData[$this->entityKey])) {
            $data = $submittedData[$this->entityKey];
            $form->setData($data);
        }

        return $form;
    }

    private function createForm(array $data, string $label): ?FormInterface
    {
        $formBuilder = new EntityToForm(new SpecBuilder());

        //@TODO: refactor to parameter if used in new ui
        $form = $formBuilder->build($this->entityKey, $this->schemaSystemRepository , backwardCompatibility:true );

        if (is_null($form)){
            return null;
        }
        $form->setData($data);
        $form->setLabel($label);
        $form->setName("data[$this->entityKey]");
        $form->setWrapElements(true);

        $errors = $this->session->read($this->entityKey);
        if ($errors) {
            $this->session->delete($this->entityKey);
            $this->setErrorMessages($errors, $form);
        }

        $form->prepare();

        return $form;
    }

    public function update(int $referenceId, array $data): void
    {
        if (!$this->entityExist()) {
            return ;
        }

        $structureGetter = getEntityBuilder();
        $entity = $structureGetter->buildEntity($this->entityKey);

        $formattedRequest = FormatterManger::format($data, $this->entityKey, $this->appEntityMetaRepository);

        $formattedRequest['reference_id'] = $referenceId;

        try {

            $showAction = resolve(AppEntityShowAction::class);
            $record = $showAction->handleBy($this->entityKey, ['reference_id' => $referenceId], 1);
            $formattedRequest['id'] = $record->id;
            $this->saver->save($entity, $formattedRequest);
        } catch (EntityRecordNotFound) {
            $this->saver->save($entity, $formattedRequest);
        } catch (Throwable $t) {
            //entity might exist but table doe not
        }
    }


    public function updateCustomData(int $referenceId, array $data): void
    {
        if (!$this->entityExist()) {
            return ;
        }

        $structureGetter = getEntityBuilder();
        $entity = $structureGetter->buildEntity($this->entityKey);

        $formattedRequest = FormatterManger::format($data, $this->entityKey, $this->appEntityMetaRepository);

        $formattedRequest['reference_id'] = $referenceId;

        try {

            $showAction = resolve(AppEntityShowAction::class);
            $record = $showAction->handleBy2($this->entityKey, ['reference_id' => $referenceId], 1);
            $formattedRequest['id'] = $record->id;
            $this->saver->save($entity, $formattedRequest);
        } catch (EntityRecordNotFound) {
            $this->saver->save($entity, $formattedRequest);
        } catch (Throwable $t) {
            //entity might exist but table doe not
        }
    }

    public function show(int $referenceId, string $label): ?FormInterface
    {
        if (!$this->entityExist()) {
            return null;
        }

        return $this->createForm(
            $this->formatRecord($referenceId, new EntityItemFormatter()),
            $label
        );
    }

    private function entityExist(): bool
    {
        $entity = (new SchemaSystemRepository())->getEntity($this->entityKey);
        return $entity ? true : false;
    }

    private function setErrorMessages(array $messages, FormInterface $form): void
    {
        $filedSetKeys = [];
        foreach ($form->getFieldsets() as $key => $val) {
            $filedSetKeys[] = $key;
        }
        foreach ($filedSetKeys as $messageKey) {
            if (isset($messages[$messageKey])) {
                $messages[$messageKey] = ['general' => $messages[$messageKey]];
            }
        }
        $messageParsing = array_undot($messages);
        if (isset($messageParsing)) {
            $form->setMessages($messageParsing);
        }
    }

    private function formatRecord($referenceId, IEntityItemFormatter $formFormatter): array
    {
        $showAction = resolve(AppEntityShowAction::class);

        try {
            $record = $showAction->handleBy($this->entityKey, ['reference_id' => $referenceId], 1);
        } catch (\Throwable) {
            return [];
        }

        $structureGetter = getEntityBuilder();
        $structure = $structureGetter->buildEntity($this->entityKey);
        $viewMetaDataGenerator = new ViewMetaDataGenerator();
        $meta = $viewMetaDataGenerator->generate($structure);

        return json_decode(json_encode(
            $formFormatter->format($record, $meta)), true
        );
    }

    public function getImportFields($entityLabel = null): array
    {
        $entity = $this->schemaSystemRepository->getEntity($this->entityKey);

        foreach ($entity->localEntityFields as $entityField) {
            if (!in_array($entityField->field_type, $this->importableFields)) {
                continue;
            }

            $fieldLabel = $entityField->label;

            if ($entityLabel) {
                $fieldLabel .= ' [' . $entityLabel . ']';
            }

            $fields[$entityField->key] = [
                'is_unique' => false,
                'required' => false,
                'title' => $fieldLabel,
                'match' => [
                    $entityField->label,
                    strtolower($entityField->label),
                    str_replace(' ','' , $entityField->label),
                    strtolower(str_replace (' ','' , $entityField->label))
                ]
            ];
        }

        return $fields ?? [];
    }

    public function getPlaceholdersFields($entityLabel = null): array
    {
        $entity = $this->schemaSystemRepository->getEntity($this->entityKey);
        foreach ($entity->localEntityFields as $entityField) {
            if (!in_array($entityField->field_type, $this->importableFields)) {
                continue;
            }
            $fieldLabel = $entityField->label;
            if ($entityLabel) {
                $fieldLabel .= ' [' . $entityLabel . ']';
            }
            $fields['{%' . $entityField->entity . '_' . $entityField->db_name . '%}'] = $fieldLabel;
        }
        return $fields ?? [];
    }

    public function getPlaceholdersValues(int $referenceId): array
    {
        if (!$this->entityExist()) {
            return [];
        }
        $record = $this->formatRecord($referenceId, new EntityItemFormatter());
        $result = [];
        foreach ($record as $key => $value) {
            if (is_array($value)) {
                $value = $value['text'] ?? implode(' ', $value);
            }
            $result['{%' . $this->entityKey . '_' . $key . '%}'] = $value;
        }
        return $result;
    }

    public function getValidationErrors(): array
    {
        /**
         * If the entity does not exist return 0 errors
         */
        if(!$this->entityExist()) {
            return [];
        }

        $formBuilder = new EntityToForm(new SpecBuilder());

        $form = $formBuilder->build($this->entityKey, $this->schemaSystemRepository);

        $errors = $this->session->read($this->entityKey);
        $formattedErrors = [];

        if (is_array($errors)) {
            foreach ($errors as $fieldName => $error) {
                if (str_contains($fieldName, '.')) {
                    $parts = explode('.', $fieldName);
                    $fieldName= $parts[0]; 
                }
                $field = $form->get($fieldName);
                $fieldLabel = $field->getLabel();

                $formattedErrors[$fieldLabel] = $error;
            }
            $this->session->delete($this->entityKey);
        }
        return $formattedErrors;
    }
}
