<?php

namespace App\Services\LocalEntityForm;

use Izam\Daftra\AppManager\Models\AppEntity;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppEntityRepositoryInterface;
use Izam\Daftra\AppManager\Services\Interfaces\AppEntitiesFormHandlerInterface;
use Izam\Daftra\Common\EntityStructure\Utils\LocalEntityTypeUtil;

class AppEntitiesFormService implements AppEntitiesFormHandlerInterface
{
    private AppEntityRepositoryInterface $appEntityRepo;

    private string $baseEntityKey;

    public function __construct()
    {
        $this->appEntityRepo = resolve(AppEntityRepositoryInterface::class);
    }

    public function setBaseEntityKey(string $baseEntityKey): void
    {
        $this->baseEntityKey = $baseEntityKey;
    }

    /** validates form and set validation errors to session */
    public function validate($data): bool
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'))
            ->where('relation_type', LocalEntityTypeUtil::CUSTOM_DATA);

        $allFormsValid = true;

        /** @var AppEntity $appEntity*/
        foreach ($appEntities as $appEntity) {
            $entityKey = $appEntity->getEntityKey();
            $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());

            if (!isset($data[$entityKey])) {
                continue;
            }

            $valid = $localEntityCrudService->isValid($data[$entityKey]);

            if (!$valid) {
                $allFormsValid = false;
            }
        }

        return $allFormsValid;
    }

    public function getValidationErrors(): array
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'))
            ->where('relation_type', LocalEntityTypeUtil::CUSTOM_DATA);

        $validationErrors = [];

        /** @var AppEntity $appEntity*/
        foreach ($appEntities as $appEntity) {
            $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());
            $validationErrors = array_merge($validationErrors, $localEntityCrudService->getValidationErrors());
        }

        return $validationErrors;
    }

    public function save($referenceId, $data): void
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'))
            ->where('relation_type', LocalEntityTypeUtil::CUSTOM_DATA);

        /** @var AppEntity $appEntity*/
        foreach ($appEntities as $appEntity) {
            $entityKey = $appEntity->getEntityKey();

            $data[$entityKey]['reference_id'] = $referenceId;
            $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());
            $localEntityCrudService->store($referenceId, $data[$entityKey]);
        }
    }

    public function update($referenceId, $data): void
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'))
            ->where('relation_type', LocalEntityTypeUtil::CUSTOM_DATA);

        foreach ($appEntities as $appEntity) {
            if (isset($data[$appEntity->getEntityKey()])) {
                $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());
                $localEntityCrudService->update($referenceId, $data[$appEntity->getEntityKey()]);
            }
        }
    }

    public function getAppsCreateForms($submittedData): array
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'))
            ->where('relation_type', LocalEntityTypeUtil::CUSTOM_DATA);

        /** @var AppEntity $appEntity */
        foreach ($appEntities as $appEntity) {

            $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());
            $form = $localEntityCrudService->getCreateForm($submittedData, $this->getLabel($appEntity));

            if (count($form->getElements()) == 0) {
                continue;
            }

            $forms[] = $form;
        }

        return $forms ?? [];
    }

    public function getAppsEditForms($referenceId, $submittedData): array
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'))
            ->where('relation_type', LocalEntityTypeUtil::CUSTOM_DATA);

        /** @var AppEntity $appEntity */
        foreach ($appEntities as $appEntity) {
            $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());

            $form = $localEntityCrudService->getEditForm($referenceId, $submittedData, $this->getLabel($appEntity));

            if (count($form->getElements()) == 0) {
                continue;
            }

            $forms[] = $form;
        }

        return $forms ?? [];
    }

    public function show($referenceId): array
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'))
            ->where('relation_type', LocalEntityTypeUtil::CUSTOM_DATA);

        /** @var AppEntity $appEntity */
        foreach ($appEntities as $appEntity) {
            $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());
            $form = $localEntityCrudService->show($referenceId, $this->getLabel($appEntity));
            if ($form) {
                $forms[] = $form; 
            }
        }

        return $forms ?? [];
    }

    private function getLabel($appEntity): string
    {
        $locale = CurrentSiteLang() === 'ara' ? 'ar' : 'en';
        return sprintf(__t('Additional info for application %s'), $appEntity->app->getTranslation($locale)->name);
    }

    public function getImportFields(): array
    {
        $appEntities = $this->appEntityRepo->getActiveAppEntities($this->baseEntityKey, getAuthOwner('id'));

        $fields = [];

        /** @var AppEntity $appEntity */
        foreach ($appEntities as $appEntity) {
            $localEntityCrudService = new LocalEntityCrudService($appEntity->getEntityKey());
            $fields = array_merge($fields, $localEntityCrudService->getImportFields($appEntity->app->getName()));
        }

        return $fields;
    }
}
