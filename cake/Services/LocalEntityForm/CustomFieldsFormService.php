<?php

namespace App\Services\LocalEntityForm;

use Izam\Dynamic\List\Repository\EntityRepository;
use Izam\Entity\Formatter\Import\FormatterFilter;
use Izam\Entity\Repository\AppEntityMetaRepository;
use Laminas\Form\FormInterface;

class CustomFieldsFormService
{
    private string $baseEntityKey;
    private LocalEntityCrudService $localEntityCrudService;

    public function __construct(string $baseEntityKey)
    {
        $this->baseEntityKey = $baseEntityKey;
        $this->localEntityCrudService = new LocalEntityCrudService($this->getEntityKey());
    }

    public function getCreateForm(array $submittedData): ?FormInterface
    {
        return $this->localEntityCrudService->getCreateForm($submittedData, $this->getFormLabel());
    }

    public function validate(array $data, $skipRequired = false): bool
    {
        return $this->localEntityCrudService->isValid($data[$this->getEntityKey()] ?? [], $skipRequired);
    }

    public function store(int $referenceId, array $data): void
    {
        $this->localEntityCrudService->store($referenceId, $data[$this->getEntityKey()] ?? []);
    }

    public function getEditForm(int $referenceId, array $submittedData): ?FormInterface
    {
        return $this->localEntityCrudService->getEditForm($referenceId, $submittedData, $this->getFormLabel());
    }

    public function update(int $referenceId, array $data): void
    {
        $this->localEntityCrudService->update($referenceId, $data[$this->getEntityKey()] ?? []);
    }

    public function updateCustomData(int $referenceId, array $data): void
    {
        $this->localEntityCrudService->updateCustomData($referenceId, $data[$this->getEntityKey()] ?? []);
    }

    public function show(int $referenceId): ?FormInterface
    {
        return $this->localEntityCrudService->show($referenceId, $this->getFormLabel());
    }

    public function getImportFields(): array
    {
        return $this->localEntityCrudService->getImportFields();
    }

    public function getEntityKey(): string
    {
        return 'le_custom_data_' . $this->baseEntityKey;
    }

    private function getFormLabel(): string
    {
        $label = (new EntityRepository())->first(['key'=> $this->baseEntityKey])->label;

        return sprintf(__t("%s More Information"), __t($label));
    }

    public function formatForImport($submittedData)
    {
        return [
            $this->getEntityKey() => 
                FormatterFilter::format(
                    $submittedData[$this->getEntityKey()], $this->getEntityKey(), new AppEntityMetaRepository()
                )
        ];
    }

    public function getValidationErrors()
    {
        return $this->localEntityCrudService->getValidationErrors();
    }

    public function getPlaceholdersFields(): array
    {
        return $this->localEntityCrudService->getPlaceholdersFields();
    }

    public function getPlaceholdersValues(int $referenceId): array
    {
        return $this->localEntityCrudService->getPlaceholdersValues($referenceId);
    }
}
