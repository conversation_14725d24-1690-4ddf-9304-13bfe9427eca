<?php

namespace App\Services\CreditDistribution;

use PurchaseOrder;

/**
 * Class CreditDistributionService
 * @package App\Services\CreditDistribution
 */
class SupplierCreditDistributionService
{
    protected $PurchaseOrderModel;
    protected $PurchaseOrderPaymentModel;
    protected $SupplierCreditDistributionModel;

    public function __construct()
    {
        $this->PurchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
        $this->PurchaseOrderPaymentModel = GetObjectOrLoadModel('PurchaseOrderPayment');
        $this->SupplierCreditDistributionModel = GetObjectOrLoadModel('SupplierCreditDistribution');
    }

    /**
     * @param $purchase_order_payment_id
     * @param $purchase_order_id
     * @return array|bool[]
     */
    public static function getSupplierPayemntID($purchase_order_payment_id, $purchase_order_id)
    {
        $creditDistributionModel = GetObjectOrLoadModel('SupplierCreditDistribution');
        $distribution = $creditDistributionModel->find('first', array('conditions' => array(
            'SupplierCreditDistribution.purchase_order_payment_id' => $purchase_order_payment_id,
            'SupplierCreditDistribution.purchase_order_id' => $purchase_order_id
        )));

        if ($distribution && $distribution['SupplierCreditDistribution']['supplier_credit_purchase_order_payment_id']) {
            return [
                'result' => true,
                'supplier_payment_id' => $distribution['SupplierCreditDistribution']['supplier_credit_purchase_order_payment_id']
            ];
        }

        return ['result' => false];
    }

    /**
     * @param $supplier_id
     * @param null $currency_code
     * @return bool
     */
    public static function validateSupplierHasValidPurchaseInvoices($supplier_id, $currency_code = null)
    {
        $conditions = [
            'PurchaseOrder.type' => PurchaseOrder::PURCHASE_INVOICE,
            'PurchaseOrder.supplier_id' => $supplier_id,
            'PurchaseOrder.payment_status' => [PO_STATUS_UNPAID, PO_STATUS_PARTIAL_PAID],
            'PurchaseOrder.draft <>1'
        ];

        if ($currency_code)
            $conditions['PurchaseOrder.currency_code'] = $currency_code;

        $purchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
        $purchase_orders = $purchaseOrderModel->find('count', ['conditions' => $conditions]);

        if ($purchase_orders)
            return true;
        else
            return false;
    }

    /**
     * @param $distributions
     * @param $total_payment_amount
     * @return array
     */
    public static function validateDistributionForm($distributions, $total_payment_amount)
    {
        $error_msg = null;
        $validation_status = true;
        $purchase_orders_ids = [];
        $total_distribution = 0;
        foreach ($distributions as $distribution) {

            $distribution['unpaid_amount'] = (float)filter_var($distribution['unpaid_amount'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            $distribution['distribution_amount'] = (float)$distribution['distribution_amount'];

	        if (empty($distribution['purchase_order_id'])) {
		        $validation_status = false;
		        $error_msg = __("Please select a Purchase Invoice", true);
				break;
	        }

            $total_distribution += $distribution['distribution_amount'];
            if (round($distribution['distribution_amount'], 2)  > round($distribution['unpaid_amount'], 2)  && abs($distribution['distribution_amount'] - $distribution['unpaid_amount']) > 0.001) {
                $validation_status = false;
                $error_msg = __("The distribution amount should be less than or equal to the unpaid amount", true);
            }
            if ($total_payment_amount != (float)$total_payment_amount) {
                $validation_status = false;
                $error_msg = __(sprintf('%s must be a number', __('amount', true)), true);
            }

            if (in_array($distribution['purchase_order_id'], $purchase_orders_ids)) {
                $validation_status = false;
                $error_msg = __("You cannot select the same purchase invoice twice", true);
            } else
                $purchase_orders_ids[] = $distribution['purchase_order_id'];
        }

        if ($total_distribution > $total_payment_amount && abs($total_distribution - (float) $total_payment_amount) > 0.001) {
            $validation_status = false;
            $error_msg = __("The total distribution amount should be less than or equal to the supplier credit balance", true);
        }

        return array('result' => $validation_status, 'message' => $error_msg);
    }

    /**
     * @param $supplier_id
     * @param $currency_code
     * @param null $supplier_payment_id
     * @return array
     */
    public static function getPurchaseInvoicesOptions($supplier_id, $currency_code, $supplier_payment_id = null)
    {
        $basic_conditions = [
            'PurchaseOrder.type' => [PurchaseOrder::PURCHASE_INVOICE ,PurchaseOrder::CREDIT_NOTE ],
            'PurchaseOrder.supplier_id' => $supplier_id,
            'PurchaseOrder.currency_code' => $currency_code,
            'PurchaseOrder.payment_status' => [PO_STATUS_UNPAID, PO_STATUS_PARTIAL_PAID],
            'PurchaseOrder.draft <>1'
        ];

        if ($supplier_payment_id) {
            $creditDistributionModel = GetObjectOrLoadModel('SupplierCreditDistribution');
            $distributions = $creditDistributionModel->find('all', array('conditions' => array('SupplierCreditDistribution.supplier_credit_purchase_order_payment_id' => $supplier_payment_id)));

            $purchase_order_ids = [];
            $distribution_values = [];
            foreach ($distributions as $old_distribution) {
                $purchase_order_id = $old_distribution['SupplierCreditDistribution']['purchase_order_id'];
                $purchase_order_ids[] = $purchase_order_id;
                $distribution_values[$purchase_order_id]['paid_amount'] = $old_distribution['SupplierCreditDistribution']['paid_amount'];
                $distribution_values[$purchase_order_id]['unpaid_amount'] = $old_distribution['SupplierCreditDistribution']['unpaid_amount'];
            }
        }

        if (!empty($purchase_order_ids)) {
            $conditions = [
                'OR' => ['PurchaseOrder.id' => $purchase_order_ids, $basic_conditions]
            ];
        } else {
            $conditions = $basic_conditions;
        }

        $purchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
        $purchase_orders = $purchaseOrderModel->find('all', ['conditions' => $conditions]);

        $purchase_ordersOptions = [];
        foreach ($purchase_orders as $purchase_order) {
            $purchase_order_id = $purchase_order['PurchaseOrder']['id'];
            if ($supplier_payment_id && in_array($purchase_order_id, $purchase_order_ids)) {
                $paid_amount = format_price($distribution_values[$purchase_order_id]['paid_amount'], $currency_code);
                $unpaid_amount = format_price($distribution_values[$purchase_order_id]['unpaid_amount'], $currency_code);
                $unpaid_value = $distribution_values[$purchase_order_id]['unpaid_amount'];
            } else {
                $paid_amount = format_price($purchase_order['PurchaseOrder']['summary_paid'], $currency_code);
                $unpaid_amount = format_price($purchase_order['PurchaseOrder']['summary_unpaid'], $currency_code);
                $unpaid_value = $purchase_order['PurchaseOrder']['summary_unpaid'];
            }

            $option['id'] = $purchase_order_id;
            $option['text'] = __t('Purchase Invoice') .'#' . $purchase_order['PurchaseOrder']['no'] . ' (' . format_date($purchase_order['PurchaseOrder']['date']) . ')';
            if ($purchase_order['PurchaseOrder']['type']==PurchaseOrder::CREDIT_NOTE) {
                $option['text'] =  __t('Credit Note').'#' . $purchase_order['PurchaseOrder']['no'] . ' (' . format_date($purchase_order['PurchaseOrder']['date']) . ')';
            }
            $option['purchase_order_amount'] = format_price($purchase_order['PurchaseOrder']['summary_total'], $currency_code);
            $option['paid_amount'] = $paid_amount;
            $option['unpaid_amount'] = $unpaid_amount;
            $option['unpaid_value'] = $unpaid_value;
            $purchase_ordersOptions[] = $option;
        }

        return $purchase_ordersOptions;
    }

    /**
     * @param $payment_id
     * @param $new_amount
     */
    private function updatePaymentAmount($payment_id, $new_amount)
    {
        $payment = $this->PurchaseOrderPaymentModel->findById($payment_id);
        $payment[$this->PurchaseOrderPaymentModel->alias]['amount'] = $new_amount;
        $this->PurchaseOrderPaymentModel->save($payment[$this->PurchaseOrderPaymentModel->alias], array('validate' => false, 'callbacks' => true, 'fieldList' => false));
        $this->PurchaseOrderModel->updatePurchaseOrderPayments($payment['PurchaseOrder']['id']);

        $this->PurchaseOrderPaymentModel->add_actionline(ACTION_UPDATE_PO_PAYMENT, array(
            'primary_id' => $payment['PurchaseOrder']['id'],
            'secondary_id' => $payment['PurchaseOrder']['supplier_id'],
            'param1' => $payment['PurchaseOrderPayment']['amount'],
            'param2' => $payment['PurchaseOrder']['payment_status'],
            'param3' => $payment['PurchaseOrder']['summary_paid'],
            'param4' => $payment['PurchaseOrder']['no'],
            'param5' => $payment['PurchaseOrderPayment']['id'],
            'param6' => $payment['PurchaseOrder']['summary_total'],
            'param7' => $payment['PurchaseOrderPayment']['status'],
            'param8' => $payment['PurchaseOrderPayment']['payment_method'],
            'param9' => $payment['PurchaseOrderPayment']['transaction_id']
        ));
    }

    /**
     * @param $payment_id
     */
    private function deletePurchaseOrderPayment($payment_id)
    {
        $payment = $this->PurchaseOrderPaymentModel->findById($payment_id);
        $this->PurchaseOrderPaymentModel->delete($payment[$this->PurchaseOrderPaymentModel->alias]['id']);
        $this->PurchaseOrderModel->updatePurchaseOrderPayments($payment[$this->PurchaseOrderPaymentModel->alias]['purchase_order_id']);

        $this->PurchaseOrderPaymentModel->add_actionline(ACTION_DELETE_PO_PAYMENT, array(
            'primary_id' => $payment['PurchaseOrder']['id'],
            'secondary_id' => $payment['PurchaseOrder']['supplier_id'],
            'param1' => $payment['PurchaseOrderPayment']['amount'],
            'param2' => $payment['PurchaseOrder']['payment_status'],
            'param3' => $payment['PurchaseOrder']['summary_paid'],
            'param4' => $payment['PurchaseOrder']['no'],
            'param5' => $payment['PurchaseOrderPayment']['id'],
            'param6' => $payment['PurchaseOrder']['summary_total'],
            'param7' => $payment['PurchaseOrderPayment']['status'],
            'param8' => $payment['PurchaseOrderPayment']['payment_method'],
            'param9' => $payment['PurchaseOrderPayment']['transaction_id']
        ));
        $this->PurchaseOrderPaymentModel->delete_auto_journals($payment['PurchaseOrderPayment']['id']);
    }

    /**
     * @param $purchase_order_id
     * @param $dataArr
     * @return array status: Insert Payment Status, payment_id: Inserted Payment ID
     */
    private function insertPurchaseOrderPayment($purchase_order_id, $dataArr)
    {
        $status = false;
        $payment_id = null;
        $result = $this->PurchaseOrderModel->ownerAddPayment($dataArr, ['check_supplier_credit' => false]);
        if ($result['status']) {
            $status = true;
            $payment_id = $result['payment_id'];
            $this->PurchaseOrderModel->updatePurchaseOrderPayments($purchase_order_id);
            $subPurchaseOrderPayment = $this->PurchaseOrderPaymentModel->read(null, $result['payment_id']);

            $this->PurchaseOrderPaymentModel->add_actionline(ACTION_ADD_PO_PAYMENT, array(
                'primary_id' => $subPurchaseOrderPayment['PurchaseOrder']['id'],
                'secondary_id' => $subPurchaseOrderPayment['PurchaseOrder']['supplier_id'],
                'param1' => $subPurchaseOrderPayment['PurchaseOrderPayment']['amount'],
                'param2' => empty($subPurchaseOrderPayment['PurchaseOrder']['draft']) ? $subPurchaseOrderPayment['PurchaseOrder']['payment_status'] : -1,
                'param3' => $subPurchaseOrderPayment['PurchaseOrder']['summary_paid'],
                'param4' => $subPurchaseOrderPayment['PurchaseOrder']['no'],
                'param5' => $subPurchaseOrderPayment['PurchaseOrderPayment']['id'],
                'param6' => $subPurchaseOrderPayment['PurchaseOrder']['summary_total'],
                'param7' => $subPurchaseOrderPayment['PurchaseOrderPayment']['status'],
                'param8' => $subPurchaseOrderPayment['PurchaseOrderPayment']['payment_method'],
                'param9' => $subPurchaseOrderPayment['PurchaseOrderPayment']['transaction_id']
            ));
        }

        return array('status' => $status, 'payment_id' => $payment_id);
    }

    /**
     * @param $supplier_payment_id
     */
    public function deleteSubPayments($supplier_payment_id)
    {
        $distributions = $this->SupplierCreditDistributionModel->find('all', array('conditions' => array('SupplierCreditDistribution.supplier_credit_purchase_order_payment_id' => $supplier_payment_id)));
        if ($distributions) {
            foreach ($distributions as $distribution) {
                $this->deletePurchaseOrderPayment($distribution['SupplierCreditDistribution']['purchase_order_payment_id']);
            }
        }
    }

    /**
     * @param $purchase_PurchaseOrderPaymentId
     * deletes supplier credit distribution of purchase invoice payment id
     */
    public function deletePurchaseOrderPaymentDistributionRecord($purchase_PurchaseOrderPaymentId)
    {
        $conditions = ['SupplierCreditDistribution.purchase_order_payment_id' => $purchase_PurchaseOrderPaymentId];
        $this->SupplierCreditDistributionModel->deleteAll($conditions);
    }

    /**
     * @param $supplier_payment_id
     * @param null $sub_payment_id
     */
    public function deleteDistributions($supplier_payment_id, $sub_payment_id = null)
    {
        $conditions['SupplierCreditDistribution.supplier_credit_purchase_order_payment_id'] = $supplier_payment_id;
        if ($sub_payment_id)
            $conditions['SupplierCreditDistribution.purchase_order_payment_id'] = $sub_payment_id;

        $this->SupplierCreditDistributionModel->deleteAll($conditions);
    }

    /**
     * @param $supplier_payment_record
     * @param $new_distributions
     * @return array Distribution Data to be Inserted
     */
    public function addSubPayments($new_distributions, $supplier_payment_record = null, $currency_code = null)
    {
        $distributionDataArr = [];
        foreach ($new_distributions as $new_distribution) {
            $paymentDataArr['PurchaseOrderPayment']['id'] = null;
            $paymentDataArr['PurchaseOrderPayment']['payment_method'] = 'supplier_credit';
            $paymentDataArr['PurchaseOrderPayment']['purchase_order_id'] = $new_distribution['purchase_order_id'];
            $paymentDataArr['PurchaseOrderPayment']['amount'] = $new_distribution['distribution_amount'];
            $paymentDataArr['PurchaseOrderPayment']['status'] = PAYMENT_STATUS_COMPLETED;

            if ($supplier_payment_record) {
                $paymentDataArr['PurchaseOrderPayment']['currency_code'] = $supplier_payment_record['PurchaseOrderPayment']['currency_code'];
                $paymentDataArr['PurchaseOrderPayment']['date'] = $supplier_payment_record['PurchaseOrderPayment']['date'];
                $paymentDataArr['PurchaseOrderPayment']['staff_id'] = $supplier_payment_record['PurchaseOrderPayment']['staff_id'];
            } else {
                $paymentDataArr['PurchaseOrderPayment']['currency_code'] = $currency_code;
                $paymentDataArr['PurchaseOrderPayment']['date'] = date('Y-m-d');
                $paymentDataArr['PurchaseOrderPayment']['staff_id'] = 0;
            }

            $subPurchaseOrder = $this->PurchaseOrderModel->read(null, $new_distribution['purchase_order_id']);
            $result = $this->insertPurchaseOrderPayment($new_distribution['purchase_order_id'], $paymentDataArr);
            if ($result['status']) {
                $distributionDataArr[] = [
                    'purchase_order_payment_id' => $result['payment_id'],
                    'purchase_order_id' => $subPurchaseOrder['PurchaseOrder']['id'],
                    'purchase_order_amount' => $subPurchaseOrder['PurchaseOrder']['summary_total'],
                    'paid_amount' => $subPurchaseOrder['PurchaseOrder']['summary_paid'],
                    'unpaid_amount' => $subPurchaseOrder['PurchaseOrder']['summary_unpaid'],
                    'distribution_amount' => $new_distribution['distribution_amount']
                ];
            }
        }

        return $distributionDataArr;
    }

    /**
     * @param $supplier_payment_id
     * @param $dataArr
     */
    public function addDistributions($supplier_payment_id, $dataArr)
    {
        foreach ($dataArr as $data_record) {
            $distributionDataArr['SupplierCreditDistribution']['id'] = null;
            $distributionDataArr['SupplierCreditDistribution']['supplier_credit_purchase_order_payment_id'] = $supplier_payment_id;
            $distributionDataArr['SupplierCreditDistribution']['purchase_order_payment_id'] = $data_record['purchase_order_payment_id'];
            $distributionDataArr['SupplierCreditDistribution']['purchase_order_id'] = $data_record['purchase_order_id'];
            $distributionDataArr['SupplierCreditDistribution']['purchase_order_amount'] = $data_record['purchase_order_amount'];
            $distributionDataArr['SupplierCreditDistribution']['paid_amount'] = $data_record['paid_amount'];
            $distributionDataArr['SupplierCreditDistribution']['unpaid_amount'] = $data_record['unpaid_amount'];
            $distributionDataArr['SupplierCreditDistribution']['distribution_amount'] = $data_record['distribution_amount'];

            $this->SupplierCreditDistributionModel->save($distributionDataArr, array('validate' => false, 'fieldList' => null));
        }
    }

    /**
     * @param $supplier_payment_id
     * @param $new_distributions
     * @return array Distribution Data to be Updated
     */
    public function updateSubPayments($supplier_payment_id, $new_distributions)
    {
        $old_distribution_records = $this->SupplierCreditDistributionModel->find('all', array('conditions' => array('SupplierCreditDistribution.supplier_credit_purchase_order_payment_id' => $supplier_payment_id)));
        $newValuesArr = [];
        foreach ($new_distributions as $new_distribution) {
            $newValuesArr[$new_distribution['purchase_order_id']] = $new_distribution['distribution_amount'];
        }

        $records_to_update = [];
        $records_to_delete = [];
        foreach ($old_distribution_records as $old_distribution_record) {
            if (in_array($old_distribution_record['SupplierCreditDistribution']['purchase_order_id'], array_keys($newValuesArr))) {
                /** Update Record **/
                if ($old_distribution_record['SupplierCreditDistribution']['distribution_amount'] != $newValuesArr[$old_distribution_record['SupplierCreditDistribution']['purchase_order_id']]) {
                    $old_distribution_record['SupplierCreditDistribution']['distribution_amount'] = $newValuesArr[$old_distribution_record['SupplierCreditDistribution']['purchase_order_id']];
                    $records_to_update[$old_distribution_record['SupplierCreditDistribution']['id']] = $old_distribution_record;
                    $this->updatePaymentAmount($old_distribution_record['SupplierCreditDistribution']['purchase_order_payment_id'], $newValuesArr[$old_distribution_record['SupplierCreditDistribution']['purchase_order_id']]);
                }
            } else {
                /** Delete Record **/
                $this->deletePurchaseOrderPayment($old_distribution_record['SupplierCreditDistribution']['purchase_order_payment_id']);
                $records_to_delete[] = $old_distribution_record['SupplierCreditDistribution']['purchase_order_payment_id'];
            }
            unset($newValuesArr[$old_distribution_record['SupplierCreditDistribution']['purchase_order_id']]);
        }

        $records_to_insert = [];
        if ($newValuesArr) {
            /** Insert Record **/
            foreach ($newValuesArr as $purchase_order_id => $distribution_amount) {
                $records_to_insert[] = ['purchase_order_id' => $purchase_order_id, 'distribution_amount' => $distribution_amount];
            }
            $supplierPayment = $this->PurchaseOrderPaymentModel->read(null, $supplier_payment_id);
            $records_to_insert = $this->addSubPayments($records_to_insert, $supplierPayment);
        }

        return array('records_to_insert' => $records_to_insert, 'records_to_update' => $records_to_update, 'records_to_delete' => $records_to_delete);
    }

    /**
     * @param $supplier_payment_id
     * @param $dataArr
     */
    public function updateDistributions($supplier_payment_id, $dataArr)
    {
        if ($dataArr['records_to_insert'])
            $this->addDistributions($supplier_payment_id, $dataArr['records_to_insert']);

        if ($dataArr['records_to_delete'])
            $this->deleteDistributions($supplier_payment_id, $dataArr['records_to_delete']);

        if ($dataArr['records_to_update']) {
            foreach ($dataArr['records_to_update'] as $supplier_dist_id => $dist_record)
                $this->SupplierCreditDistributionModel->update($supplier_dist_id, $dist_record);
        }
    }
}
