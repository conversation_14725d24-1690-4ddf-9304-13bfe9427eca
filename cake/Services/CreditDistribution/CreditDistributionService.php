<?php

namespace App\Services\CreditDistribution;

use Invoice;

/**
 * Class CreditDistributionService
 * @package App\Services\CreditDistribution
 */
class CreditDistributionService
{
    protected $InvoiceModel;
    protected $InvoicePaymentModel;
    protected $ClientCreditDistributionModel;

    public function __construct()
    {
        $this->InvoiceModel = GetObjectOrLoadModel('Invoice');
        $this->InvoicePaymentModel = GetObjectOrLoadModel('InvoicePayment');
        $this->ClientCreditDistributionModel = GetObjectOrLoadModel('ClientCreditDistribution');
    }

    /**
     * @param $invoice_payment_id
     * @param $invoice_id
     * @return array|bool[]
     */
    public static function getClientPayemntID($invoice_payment_id, $invoice_id)
    {
        $creditDistributionModel = GetObjectOrLoadModel('ClientCreditDistribution');
        $distribution = $creditDistributionModel->find('first', array('conditions' => array(
            'ClientCreditDistribution.invoice_payment_id' => $invoice_payment_id,
            'ClientCreditDistribution.invoice_id' => $invoice_id
        )));

        if ($distribution && $distribution['ClientCreditDistribution']['client_credit_invoice_payment_id']) {
            return [
                'result' => true,
                'client_payment_id' => $distribution['ClientCreditDistribution']['client_credit_invoice_payment_id']
            ];
        }

        return ['result' => false];
    }

    /**
     * @param $client_id
     * @param null $currency_code
     * @return bool
     */
    public static function validateClientHasValidInvoices($client_id, $currency_code = null)
    {
        $conditions = [
            'Invoice.type' =>[ Invoice::Invoice, Invoice::DEBIT_NOTE],
            'Invoice.client_id' => $client_id,
            'Invoice.payment_status' => [INVOICE_STATUS_UNPAID, INVOICE_STATUS_PARTIAL_PAID],
            'Invoice.draft <>1'
        ];

        if($currency_code)
            $conditions['Invoice.currency_code'] = $currency_code;

        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $invoices = $invoiceModel->find('count', ['conditions' => $conditions]);

        if ($invoices)
            return true;
        else
            return false;
    }

    /**
     * @param $distributions
     * @param $total_payment_amount
     * @return array
     */
    public static function validateDistributionForm($distributions, $total_payment_amount)
    {
        if (!is_numeric($total_payment_amount)) {
            return [
                "result" => false,
                'message' => __("The client credit balance should be number", true),
            ];
        }
        $error_msg = null;
        $validation_status = true;
        $invoices_ids = [];
        $total_distribution = 0;
        foreach ($distributions as $distribution) {
                   
            if(!is_numeric($distribution['distribution_amount'])){
                $validation_status = false;
                $error_msg = sprintf(__('%s must be a number',true) , __('Distribution Amount', true));
            }    

            $distribution['unpaid_amount'] = (float)filter_var($distribution['unpaid_amount'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            $distribution['distribution_amount']= (float)$distribution['distribution_amount'];

            $total_distribution += $distribution['distribution_amount'];
            if ( round($distribution['distribution_amount'],2)  > round($distribution['unpaid_amount'],2)  && abs($distribution['distribution_amount'] - $distribution['unpaid_amount']) > 0.001) {
                $validation_status = false;
                $error_msg = __("The distribution amount should be less than or equal to the unpaid amount", true);
            }
            if($total_payment_amount != (float)$total_payment_amount) {
                $validation_status = false;
                $error_msg = __(sprintf('%s must be a number', __('amount', true)),true);
            }

            if (in_array($distribution['invoice_id'], $invoices_ids)) {
                $validation_status = false;
                $error_msg = __("You cannot select the invoice twice", true);
            } else
                $invoices_ids[] = $distribution['invoice_id'];
        }

        if (round($total_distribution,5) > round($total_payment_amount,5) && abs($total_distribution - (float) $total_payment_amount) > 0.001) {
            $validation_status = false;
            $error_msg = __("The total distribution amount should be less than or equal to the client credit balance", true);
        }

        return array('result' => $validation_status, 'message' => $error_msg);
    }

    /**
     * @param $client_id
     * @param $currency_code
     * @param null $client_payment_id
     * @return array
     */
    public static function getInvoicesOptions($client_id, $currency_code, $client_payment_id = null)
    {
        $basic_conditions = [
            'Invoice.type' =>[ Invoice::Invoice,Invoice::DEBIT_NOTE],
            'Invoice.client_id' => $client_id,
            'Invoice.currency_code' => $currency_code,
            'Invoice.payment_status' => [INVOICE_STATUS_UNPAID, INVOICE_STATUS_PARTIAL_PAID],
            'Invoice.draft <>1'
        ];

        if ($client_payment_id) {
            $creditDistributionModel = GetObjectOrLoadModel('ClientCreditDistribution');
            $distributions = $creditDistributionModel->find('all', array('conditions' => array('ClientCreditDistribution.client_credit_invoice_payment_id' => $client_payment_id)));

            $invoice_ids = [];
            $distribution_values = [];
            foreach ($distributions as $old_distribution) {
                $invoice_id = $old_distribution['ClientCreditDistribution']['invoice_id'];
                $invoice_ids[] = $invoice_id;
                $distribution_values[$invoice_id]['paid_amount'] = $old_distribution['ClientCreditDistribution']['paid_amount'];
                $distribution_values[$invoice_id]['unpaid_amount'] = $old_distribution['ClientCreditDistribution']['unpaid_amount'];
            }
        }

        if (!empty($invoice_ids)) {
            $conditions = [
                'OR' => ['Invoice.id' => $invoice_ids, $basic_conditions]
            ];
        } else {
            $conditions = $basic_conditions;
        }

        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $invoices = $invoiceModel->find('all', ['conditions' => $conditions]);

        $invoicesOptions = [];
        foreach ($invoices as $invoice) {
            $invoice_id = $invoice['Invoice']['id'];
            if ($client_payment_id && in_array($invoice_id, $invoice_ids)) {
                $paid_amount = format_price($distribution_values[$invoice_id]['paid_amount'], $currency_code);
                $unpaid_amount = format_price($distribution_values[$invoice_id]['unpaid_amount'], $currency_code);
                $unpaid_value = $distribution_values[$invoice_id]['unpaid_amount'];
            } else {
                $paid_amount = format_price($invoice['Invoice']['summary_paid'], $currency_code);
                $unpaid_amount = format_price($invoice['Invoice']['summary_unpaid'], $currency_code);
                $unpaid_value = $invoice['Invoice']['summary_unpaid'];
            }

            $option['id'] = $invoice_id;

            $type_text=__t('Invoice');
            if ($invoice['Invoice']['type']==Invoice::DEBIT_NOTE) {
                $type_text = __t('Debit Note') ;
            }
            $option['text'] = $type_text.' #' . $invoice['Invoice']['no'] . ' (' . format_date($invoice['Invoice']['date']) . ')';
            $option['invoice_amount'] = format_price($invoice['Invoice']['summary_total'], $currency_code);
            $option['paid_amount'] = $paid_amount;
            $option['unpaid_amount'] = $unpaid_amount;
            $option['unpaid_value'] = $unpaid_value;
            $invoicesOptions[] = $option;
        }

        return $invoicesOptions;
    }

    /**
     * @param $payment_id
     * @param $new_amount
     */
    private function updatePaymentAmount($payment_id, $new_amount) {
        $payment = $this->InvoicePaymentModel->findById($payment_id);
        $payment[$this->InvoicePaymentModel->alias]['amount'] = $new_amount;
        $this->InvoicePaymentModel->save($payment[$this->InvoicePaymentModel->alias], array('validate' => false, 'callbacks' => true, 'fieldList' => false));
        $this->InvoiceModel->updateInvoicePayments($payment['Invoice']['id']);

        $this->InvoicePaymentModel->add_actionline(ACTION_UPDATE_INVOICE_PAYMENT, array(
            'primary_id' => $payment['Invoice']['id'],
            'secondary_id' => $payment['Invoice']['client_id'],
            'param1' => $payment['InvoicePayment']['amount'],
            'param2' => $payment['Invoice']['payment_status'],
            'param3' => $payment['Invoice']['summary_paid'],
            'param4' => $payment['Invoice']['no'],
            'param5' => $payment['InvoicePayment']['id'],
            'param6' => $payment['Invoice']['summary_total'],
            'param7' => $payment['InvoicePayment']['status'],
            'param8' => $payment['InvoicePayment']['payment_method'],
            'param9' => $payment['InvoicePayment']['transaction_id']
        ));
    }

    /**
     * @param $payment_id
     */
    private function deleteInvoicePayment($payment_id)
    {
        $payment = $this->InvoicePaymentModel->findById($payment_id);
        $this->InvoicePaymentModel->delete($payment[$this->InvoicePaymentModel->alias]['id']);
        $this->InvoiceModel->updateInvoicePayments($payment[$this->InvoicePaymentModel->alias]['invoice_id']);

        $this->InvoicePaymentModel->add_actionline(ACTION_DELETE_INVOICE_PAYMENT, array(
            'primary_id' => $payment['Invoice']['id'],
            'secondary_id' => $payment['Invoice']['client_id'],
            'param1' => $payment['InvoicePayment']['amount'],
            'param2' => $payment['Invoice']['payment_status'],
            'param3' => $payment['Invoice']['summary_paid'],
            'param4' => $payment['Invoice']['no'],
            'param5' => $payment['InvoicePayment']['id'],
            'param6' => $payment['Invoice']['summary_total'],
            'param7' => $payment['InvoicePayment']['status'],
            'param8' => $payment['InvoicePayment']['payment_method'],
            'param9' => $payment['InvoicePayment']['transaction_id']
        ));
        $this->InvoicePaymentModel->delete_auto_journals($payment['InvoicePayment']['id']);
    }

    /**
     * @param $invoice_id
     * @param $dataArr
     * @return array status: Insert Payment Status, payment_id: Inserted Payment ID
     */
    private function insertInvoicePayment($invoice_id, $dataArr)
    {
        $status = false;
        $payment_id = null;
        $result = $this->InvoiceModel->ownerAddPayment($dataArr, ['check_client_credit' => false]);
        if ($result['status']) {
            $status = true;
            $payment_id = $result['payment_id'];
            $this->InvoiceModel->updateInvoicePayments($invoice_id);
            $subInvoicePayment = $this->InvoicePaymentModel->read(null, $result['payment_id']);

            $this->InvoicePaymentModel->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array(
                'primary_id' => $subInvoicePayment['Invoice']['id'],
                'secondary_id' => $subInvoicePayment['Invoice']['client_id'],
                'param1' => $subInvoicePayment['InvoicePayment']['amount'],
                'param2' => empty($subInvoicePayment['Invoice']['draft']) ? $subInvoicePayment['Invoice']['payment_status'] : -1,
                'param3' => $subInvoicePayment['Invoice']['summary_paid'],
                'param4' => $subInvoicePayment['Invoice']['no'],
                'param5' => $subInvoicePayment['InvoicePayment']['id'],
                'param6' => $subInvoicePayment['Invoice']['summary_total'],
                'param7' => $subInvoicePayment['InvoicePayment']['status'],
                'param8' => $subInvoicePayment['InvoicePayment']['payment_method'],
                'param9' => $subInvoicePayment['InvoicePayment']['transaction_id']
            ));
        }

        return array('status' => $status, 'payment_id' => $payment_id);
    }

    /**
     * @param $client_payment_id
     */
    public function deleteSubPayments($client_payment_id)
    {
        $distributions = $this->ClientCreditDistributionModel->find('all', array('conditions' => array('ClientCreditDistribution.client_credit_invoice_payment_id' => $client_payment_id)));
        if ($distributions) {
            foreach ($distributions as $distribution) {
                $this->deleteInvoicePayment($distribution['ClientCreditDistribution']['invoice_payment_id']);
            }
        }
    }

    /**
     * @param $invoicePaymentId
     * deletes client credit distribution of invoice payment id
     */
    public function deleteInvoicePaymentDistributionRecord($invoicePaymentId) {
        $conditions = ['ClientCreditDistribution.invoice_payment_id' => $invoicePaymentId];
        $this->ClientCreditDistributionModel->deleteAll($conditions);
    }

    /**
     * @param $client_payment_id
     * @param null $sub_payment_id
     */
    public function deleteDistributions($client_payment_id, $sub_payment_id = null)
    {
        $conditions['ClientCreditDistribution.client_credit_invoice_payment_id'] = $client_payment_id;
        if ($sub_payment_id)
            $conditions['ClientCreditDistribution.invoice_payment_id'] = $sub_payment_id;

        $this->ClientCreditDistributionModel->deleteAll($conditions);
    }

    /**
     * @param $client_payment_record
     * @param $new_distributions
     * @return array Distribution Data to be Inserted
     */
    public function addSubPayments($new_distributions, $client_payment_record = null, $currency_code = null)
    {
        $distributionDataArr = [];
        foreach ($new_distributions as $new_distribution) {
            $paymentDataArr['InvoicePayment']['id'] = null;
            $paymentDataArr['InvoicePayment']['payment_method'] = 'client_credit';
            $paymentDataArr['InvoicePayment']['invoice_id'] = $new_distribution['invoice_id'];
            $paymentDataArr['InvoicePayment']['amount'] = $new_distribution['distribution_amount'];
            $paymentDataArr['InvoicePayment']['status'] = PAYMENT_STATUS_COMPLETED;

            if ($client_payment_record) {
                $paymentDataArr['InvoicePayment']['currency_code'] = $client_payment_record['InvoicePayment']['currency_code'];
                $paymentDataArr['InvoicePayment']['date'] = $client_payment_record['InvoicePayment']['date'];
                $paymentDataArr['InvoicePayment']['staff_id'] = $client_payment_record['InvoicePayment']['staff_id'];
            } else {
                $paymentDataArr['InvoicePayment']['currency_code'] = $currency_code;
                $paymentDataArr['InvoicePayment']['date'] = date('Y-m-d');
                $paymentDataArr['InvoicePayment']['staff_id'] = 0;
            }

            $subInvoice = $this->InvoiceModel->read(null, $new_distribution['invoice_id']);
            $result = $this->insertInvoicePayment($new_distribution['invoice_id'], $paymentDataArr);
            if ($result['status']) {
                $distributionDataArr[] = [
                    'invoice_payment_id' => $result['payment_id'],
                    'invoice_id' => $subInvoice['Invoice']['id'],
                    'invoice_amount' => $subInvoice['Invoice']['summary_total'],
                    'paid_amount' => $subInvoice['Invoice']['summary_paid'],
                    'unpaid_amount' => $subInvoice['Invoice']['summary_unpaid'],
                    'distribution_amount' => $new_distribution['distribution_amount']
                ];
            }
        }

        return $distributionDataArr;
    }

    /**
     * @param $client_payment_id
     * @param $dataArr
     */
    public function addDistributions($client_payment_id, $dataArr)
    {
        foreach ($dataArr as $data_record) {
            $distributionDataArr['ClientCreditDistribution']['id'] = null;
            $distributionDataArr['ClientCreditDistribution']['client_credit_invoice_payment_id'] = $client_payment_id;
            $distributionDataArr['ClientCreditDistribution']['invoice_payment_id'] = $data_record['invoice_payment_id'];
            $distributionDataArr['ClientCreditDistribution']['invoice_id'] = $data_record['invoice_id'];
            $distributionDataArr['ClientCreditDistribution']['invoice_amount'] = $data_record['invoice_amount'];
            $distributionDataArr['ClientCreditDistribution']['paid_amount'] = $data_record['paid_amount'];
            $distributionDataArr['ClientCreditDistribution']['unpaid_amount'] = $data_record['unpaid_amount'];
            $distributionDataArr['ClientCreditDistribution']['distribution_amount'] = $data_record['distribution_amount'];
            
            $this->ClientCreditDistributionModel->save($distributionDataArr, array('validate' => false, 'fieldList' => null));
        }
    }

    /**
     * @param $client_payment_id
     * @param $new_distributions
     * @return array Distribution Data to be Updated
     */
    public function updateSubPayments($client_payment_id, $new_distributions)
    {
        $old_distribution_records = $this->ClientCreditDistributionModel->find('all', array('conditions' => array('ClientCreditDistribution.client_credit_invoice_payment_id' => $client_payment_id)));
        $newValuesArr = [];
        foreach ($new_distributions as $new_distribution) {
            $newValuesArr[$new_distribution['invoice_id']] = $new_distribution['distribution_amount'];
        }

        $records_to_update = [];
        $records_to_delete = [];
        foreach ($old_distribution_records as $old_distribution_record) {
            if (in_array($old_distribution_record['ClientCreditDistribution']['invoice_id'], array_keys($newValuesArr))) {
                /** Update Record **/
                if ($old_distribution_record['ClientCreditDistribution']['distribution_amount'] != $newValuesArr[$old_distribution_record['ClientCreditDistribution']['invoice_id']]) {
                    $old_distribution_record['ClientCreditDistribution']['distribution_amount'] = $newValuesArr[$old_distribution_record['ClientCreditDistribution']['invoice_id']];
                    $records_to_update[$old_distribution_record['ClientCreditDistribution']['id']] = $old_distribution_record;
                    $this->updatePaymentAmount($old_distribution_record['ClientCreditDistribution']['invoice_payment_id'], $newValuesArr[$old_distribution_record['ClientCreditDistribution']['invoice_id']]);
                }
            } else {
                /** Delete Record **/
                $this->deleteInvoicePayment($old_distribution_record['ClientCreditDistribution']['invoice_payment_id']);
                $records_to_delete[] = $old_distribution_record['ClientCreditDistribution']['invoice_payment_id'];
            }
            unset($newValuesArr[$old_distribution_record['ClientCreditDistribution']['invoice_id']]);
        }

        $records_to_insert = [];
        if ($newValuesArr) {
            /** Insert Record **/
            foreach ($newValuesArr as $invoice_id => $distribution_amount) {
                $records_to_insert[] = ['invoice_id' => $invoice_id, 'distribution_amount' => $distribution_amount];
            }
            $clientPayment = $this->InvoicePaymentModel->read(null, $client_payment_id);
            $records_to_insert = $this->addSubPayments($records_to_insert, $clientPayment);
        }

        return array('records_to_insert' => $records_to_insert, 'records_to_update' => $records_to_update, 'records_to_delete' => $records_to_delete);
    }

    /**
     * @param $client_payment_id
     * @param $dataArr
     */
    public function updateDistributions($client_payment_id, $dataArr)
    {
        if($dataArr['records_to_insert'])
            $this->addDistributions($client_payment_id, $dataArr['records_to_insert']);

        if($dataArr['records_to_delete'])
            $this->deleteDistributions($client_payment_id, $dataArr['records_to_delete']);

        if($dataArr['records_to_update']) {
            foreach ($dataArr['records_to_update'] as $client_dist_id => $dist_record)
                $this->ClientCreditDistributionModel->update($client_dist_id, $dist_record);
        }
    }
}