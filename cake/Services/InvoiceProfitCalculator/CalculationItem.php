<?php

namespace App\Services\InvoiceProfitCalculator;

class CalculationItem
{
    private $productId;
    private $unitPrice;
    private $purchasePrice;
    private $quantity;
    private $discount;
    private $discountAmount;
    private $finalPrice;
    private $tax1Id;
    private $tax2Id;
    private $product;
    private $tracking_data;
    private $calculatedDiscount;
    private $unitFactor;

    public function __construct($invoiceItem)
    {
        $this->productId = $invoiceItem['product_id'];
        $this->quantity = $invoiceItem['quantity'];
        $this->tax1Id = $invoiceItem['tax1'];
        $this->tax2Id = $invoiceItem['tax2'];
        $this->unitPrice = $invoiceItem['unit_price'];
        if($invoiceItem['discount_type'] == \InvoiceItem::DISCOUNT_PERCENTAGE) {
            $this->discount = $invoiceItem['discount'];
        } else {
            $this->discountAmount = $invoiceItem['discount'];
        }
        $this->finalPrice = $invoiceItem['unit_price'];
        $this->product = $invoiceItem['Product'];
        $this->calculatedDiscount = $invoiceItem['calculated_discount'];
        $this->unitFactor = $invoiceItem['unit_factor'];
    }

    public function getProductName() {
        return $this->product['name'];
    }

    public function getProductCode() {
        return $this->product['product_code'];
    }

    /**
     * @return mixed
     */
    public function getProduct()
    {
        return $this->product;
    }

    /**
     * @param mixed $Product
     * @return CalculationItem
     */
    public function setProduct($Product)
    {
        $this->product = $Product;
        return $this;
    }
    /**
     * @return mixed
     */
    public function getProductId()
    {
        return $this->productId;
    }

    /**
     * @param mixed $productId
     * @return CalculationItem
     */
    public function setProductId($productId)
    {
        $this->productId = $productId;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getUnitPrice()
    {
        return $this->unitPrice;
    }

    /**
     * @param mixed $unitPrice
     * @return CalculationItem
     */
    public function setUnitPrice($unitPrice)
    {
        $this->unitPrice = $unitPrice;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getPurchasePrice()
    {
        return $this->purchasePrice;
    }

    /**
     * @param mixed $purchasePrice
     * @return CalculationItem
     */
    public function setPurchasePrice($purchasePrice)
    {
        $this->purchasePrice = $purchasePrice;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * @param mixed $quantity
     * @return CalculationItem
     */
    public function setQuantity($quantity)
    {
        $this->quantity = $quantity;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getDiscount()
    {
        return $this->discount;
    }

    /**
     * @param mixed $discount
     * @return CalculationItem
     */
    public function setDiscount($discount)
    {
        $this->discount = $discount;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getDiscountAmount()
    {
        return $this->discountAmount;
    }

    /**
     * @param mixed $discountAmount
     * @return CalculationItem
     */
    public function setDiscountAmount($discountAmount)
    {
        $this->discountAmount = $discountAmount;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getFinalPrice()
    {
        return $this->finalPrice;
    }

    /**
     * @param mixed $finalPrice
     * @return CalculationItem
     */
    public function setFinalPrice($finalPrice)
    {
        $this->finalPrice = $finalPrice;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getTax1Id()
    {
        return $this->tax1Id;
    }

    /**
     * @param mixed $tax1Id
     * @return CalculationItem
     */
    public function setTax1Id($tax1Id)
    {
        $this->tax1Id = $tax1Id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getTax2Id()
    {
        return $this->tax2Id;
    }

    /**
     * @param mixed $tax2Id
     * @return CalculationItem
     */
    public function setTax2Id($tax2Id)
    {
        $this->tax2Id = $tax2Id;
        return $this;
    }



      /**
     * @return mixed
     */
    public function getTrackingData()
    {
        return $this->tracking_data;
    }

    /**
     * @param mixed $tracking_data
     * @return CalculationItem
     */
    public function setTrackingData($tracking_data)
    {
        $this->tracking_data = $tracking_data;
        return $this;
    }
    
     
    public function getCalculatedDiscount()
    {
        return $this->calculatedDiscount;
    }

    public function getUnitFactor()
    {
        return $this->unitFactor;
    }
    
}