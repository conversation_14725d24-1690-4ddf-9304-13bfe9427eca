<?php

namespace App\Services\InvoiceProfitCalculator;

use App\Utils\TrackStockUtil;
use Izam\Daftra\Common\Utils\PluginUtil;

class InvoiceProfitFactory
{

    /**
     * @param $invoiceItems
     * @param $invoiceRequisitions
     * @return CalculationItem[]
     */
    public static function createCalculationItemsFromRequisition($invoiceItems, $invoiceRequisitions, $stockTransactions) {
        $profitInvoiceItems = $invoiceItems;
        $calculationItems = [];
        foreach ($invoiceRequisitions as $invoiceRequisition) {
            foreach ($invoiceRequisition['RequisitionItem'] as $reqItemIndex => $reqItem) {
                foreach ($stockTransactions as $stockTransaction) {
                    if(
                        $stockTransaction['StockTransaction']['product_id'] === $reqItem['product_id'] &&
                        $stockTransaction['StockTransaction']['order_id'] === $invoiceRequisition['Requisition']['id'] &&
                        $stockTransaction['StockTransaction']['ref_id'] === $reqItem['id']

                    ) {
                        $stock = $stockTransaction;
                        foreach ($profitInvoiceItems as $invoiceItemIndex => $profitInvoiceItem) {
                            if($reqItem['product_id'] === $profitInvoiceItem['product_id']) {
                                $calculationItem = new CalculationItem($profitInvoiceItem);
                                $calculationItem
                                        ->setPurchasePrice($stock['StockTransaction']['purchase_price']);
                                if($reqItem['quantity'] <= $profitInvoiceItem['quantity']) {
                                    $calculationItem
                                        ->setQuantity($reqItem['quantity'])
                                    ;
                                    $profitInvoiceItems[$invoiceItemIndex]['quantity'] -= $reqItem['quantity'];
                                    //requisition item finished
                                } else if($reqItem['quantity'] > $profitInvoiceItem['quantity']) {
                                    $reqItem['quantity'] -= $profitInvoiceItem['quantity'];
                                    $profitInvoiceItems[$invoiceItemIndex]['quantity'] = 0;
                                    //invoice item finished
                                }
                                $calculationItems[] = $calculationItem;
                                if($profitInvoiceItems[$invoiceItemIndex]['quantity'] === 0) {
                                    unset($profitInvoiceItems[$invoiceItemIndex]);
                                }
                            }
                        }
                    }
                }
            }
        }
        return $calculationItems;
    }

    /**
     * @param $invoiceItems
     * @param $stocks
     * @return CalculationItem[]
     */
    public static function createCalculationItemsFromStockTransactions(array $invoiceItems, array $stocks): array
    {
        $calculationItems = [];
        $trackingNumber = null;
        if (ifPluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN)) {
            $trackingNumber = GetObjectOrLoadModel('TrackingNumber');
        }
        $invoiceItems = self::calculateAverageUnitPriceForDuplicateProduct($invoiceItems);
        $processedInvoiceItems = [];

        foreach ($stocks as $stock) {
            $stockTransaction = $stock['StockTransaction'];
            foreach ($invoiceItems as $invoiceItem) {
                $isSameProduct = $invoiceItem['product_id'] === $stockTransaction['product_id'];
                $isNotProcessed = !in_array($invoiceItem['id'], $processedInvoiceItems, true);
                $matchesByPriceOrRef = $invoiceItem['unit_price'] == $stockTransaction['price'] || $invoiceItem['id'] == $stockTransaction['ref_id'] || $invoiceItem['invoice_id'] == $stockTransaction['order_id'];
                if ($isSameProduct && $isNotProcessed && $matchesByPriceOrRef) {
                    $calculationItem = new CalculationItem($invoiceItem);
                    $calculationItem->setPurchasePrice($stockTransaction['purchase_price']);
                    if (
                        $stock['Product']['tracking_type'] != TrackStockUtil::QUANTITY_ONLY &&
                        $stockTransaction['tracking_number_id'] > 0 &&
                        $trackingNumber
                    ) {
                        $trackingData = $trackingNumber->getTrackingData(
                            $stockTransaction['tracking_number_id'],
                            $stock['Product']['tracking_type']
                        );
                        $calculationItem->setTrackingData($trackingData);
                    }
                    $calculationItems[] = $calculationItem;
                    $processedInvoiceItems[] = $invoiceItem['id'];
                }
            }
        }
        return $calculationItems;
    }



    private static function calculateAverageUnitPriceForDuplicateProduct($invoiceItems): array
    {
        $Invoice = GetObjectOrLoadModel('Invoice');
        $groupedItems = [];
        foreach ($invoiceItems as $item) {
            $product_id = $item['product_id'];
            $sub = (float)$item['unit_price'] * (float)$item['quantity'];
            $discount_val = $Invoice->calculate_item_discount($item, $sub);
            if (!array_key_exists($product_id, $groupedItems)) {
                $groupedItems[$product_id] = ['unit_price' => $item['unit_price'], 'quantity' => $item['quantity'] ,'calculated_discount'=>$discount_val];
            } else {
                $total_quantity=($groupedItems[$product_id]['quantity'] + $item['quantity']);

                if ($total_quantity==0) {
                    $groupedItems[$product_id]['unit_price'] = 0;
                }else{
                    $groupedItems[$product_id]['unit_price'] = (($groupedItems[$product_id]['unit_price'] * $groupedItems[$product_id]['quantity']) +
                            ($item['unit_price'] * $item['quantity'])) / $total_quantity;
                }
                $groupedItems[$product_id]['quantity'] += $item['quantity'];
                $groupedItems[$product_id]['calculated_discount'] += $discount_val;
            }
        }
        $finalResult = [];
        foreach ($invoiceItems as $item) {
            $item['unit_price'] = $groupedItems[$item['product_id']]['unit_price'];
            $finalResult[] = $item;
        }
        return $finalResult;
    }

}