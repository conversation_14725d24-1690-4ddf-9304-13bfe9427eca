<?php

namespace App\Repositories\Helpers;

class EntityConditionsHelper
{
    public static function convertConditionsToSql($conditions, $operator = 'AND') {
        $conditionsString = '';
        $filters = [];
        foreach ($conditions as $key => $condition) {
            if(strtolower($key) === 'AND') {
                $filters[] = self::convertConditionsToSql($condition);
            } else if(strtolower($key) === 'OR') {
                $filters[] = self::convertConditionsToSql($condition);
            } else {
                $fieldName = $key;
                $value = $condition['value']??null;
                $operator = $condition['operator'] ?? $operator;
                $filters[] = self::buildFilter($fieldName, $condition);
            }
        }
        if(!empty($filters)) {
            $conditionsString = "(" . implode(" $operator ", $filters) . ")";
        }
        return $conditionsString;
    }

    public static function buildFilter($fieldName, $operations) {
        $evaluatedFilters = [];
        foreach ($operations as $operation) {
            $operator = $operation['operator'];
            $value = $operation['value'];

            switch (strtolower($operator)) {
                case 'equal':
                default:
                    $evaluatedFilters[] = "$fieldName = '$value'";
                    break;
            }
        }
        return implode(' AND ', $evaluatedFilters);
    }
}
