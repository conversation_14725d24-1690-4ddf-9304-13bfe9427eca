<?php


namespace App\Repositories;

use CreditCharge;

class CreditChargeRepository
{


    private function getAvailableCreditChargesQuery($client_id, $credit_charge_type, $date, $excludeChargeUsages = [])
    {  
        $creditCharge =  GetObjectOrLoadModel('CreditCharge');
        $excludeChargeUsagesCondition = '';

        if($excludeChargeUsages) { 
            $implodedChargeUsages = implode(",", $excludeChargeUsages);  
            $excludeChargeUsagesCondition = "AND charge_usages.id NOT IN({$implodedChargeUsages})";
        }
        //Check for date before anything
        return  $creditCharge->query('SELECT
        `credit_types`.`allow_decimal` AS `allow_decimal`,
        CONCAT(
            "#",
            credit_types.id,
            " ",
            credit_types.name
        ) AS credit_name,
        `credit_usage_id` AS `credit_usage_id`,
        `credit_charges`.`id` AS `credit_charge_id`,
        credit_charges.credit_type_id AS credit_type_id,
        (
            CASE WHEN SUM(charge_usages.amount) IS NULL THEN 0 ELSE SUM(charge_usages.amount)
        END
    ) AS total_amount_charge,
    `expiry_date`,
    `credit_charges`.`amount`
    FROM
        `credit_charges`
    LEFT JOIN `charge_usages` ON (`credit_charges`.`id` = `charge_usages`.`credit_charge_id` '.$excludeChargeUsagesCondition.')
    INNER JOIN `credit_types` ON `credit_charges`.`credit_type_id` = `credit_types`.`id`
    WHERE
        (
            DATE(`expiry_date`) >= "' . $date . '" OR `expiry_date` IS NULL
        ) AND DATE(`start_date`) <= "' . $date . '" AND `client_id` = ' . $client_id . '
         AND `credit_charges`.`status` != "suspended" 
         AND `credit_charges`.`deleted_at` IS NULL 
         AND `credit_types`.`deleted_at` IS NULL 
         AND `credit_charges`.`credit_type_id` = ' . $credit_charge_type . '
    GROUP BY
        `credit_charges`.`id`
    HAVING
        credit_charges.amount > total_amount_charge
    ORDER BY
        IF(ISNULL(expiry_date),
        1,
        0),
        expiry_date ASC,
        IF(ISNULL(start_date),
        1,
        0),
        start_date ASC,
        credit_charges.amount ASC');
    }


    public function getClientCreditCharges($client_id, $credit_charge_type, $date, $excludeChargeUsages = [])
    {
        return $this->getAvailableCreditChargesQuery($client_id, $credit_charge_type, $date, $excludeChargeUsages);
    }

    public function getClientCredit($client_id, $credit_charge_type, $date)
    {


        if (empty($credit_charge_type)) {
            return 0;
        }

        $result = $this->getAvailableCreditChargesQuery($client_id, $credit_charge_type, $date);

      
        if (empty($result)) {
            return 0;
        }

        $amount = $total_amount_charge = 0;

        foreach ($result as $creditCharge) {
            $amount += $creditCharge['credit_charges']['amount'];
            $total_amount_charge += $creditCharge[0]['total_amount_charge'];
        }

        return round(($amount - $total_amount_charge), 3);
    }
}
