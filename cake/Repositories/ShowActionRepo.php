<?php


namespace App\Repositories;


use App\Repositories\Helpers\EntityConditionsHelper;
use Izam\Daftra\Common\EntityStructure\Entity;
use Izam\Daftra\Common\EntityStructure\IEntity;
use Izam\Daftra\Common\EntityStructure\Relation\Relation;
use Izam\Daftra\Common\Repositories\EntityIdsLoaderRepoInterface;
use Izam\Daftra\Common\Repositories\EntityRecordFinderRepositoryInterface;
use Izam\Daftra\Common\Repositories\EntityRecordsLoaderInterface;
use Izam\Entity\Repository\DynamicRepo;

class ShowActionRepo implements EntityIdsLoaderRepoInterface,EntityRecordsLoaderInterface,EntityRecordFinderRepositoryInterface
{
    private function getConnection(IEntity $entity) {
        if($entity->getConnection() === 'currentSite') {
            $connectionName = 'default';
        } else {
            $connectionName = 'portal';
        }
        return \ConnectionManager::getDataSource($connectionName);
    }

    private function findIn(IEntity $table, $field, $value, $return, $conditions = []) {
        $connection = $this->getConnection($table);
	    $value = array_filter($value ?? []);
	    if ($value === []) {
            return [];
        } else {
            $extraConditions = "";
            if(!empty($conditions)) {
                $extraConditions = "AND ". EntityConditionsHelper::convertConditionsToSql($conditions);
            }
            $values = implode(',', $value);
            $query = "SELECT $return from `{$table->getTable()}` where $field in ($values) {$extraConditions}";
            $result = $connection->fetchAll($query, false);
            $array = [];
            foreach ($result as $record) {
                if(!empty($record[$table->getTable()][$return])) {
                    $array[] = $record[$table->getTable()][$return];
                }
            }
        }
        return $array;
    }

    public function getHasManyIds(IEntity $table, $field, $value, $return, $conditions = [])
    {
        return $this->findIn($table, $field, $value, $return, $conditions);
    }

    public function getHasOneIds(IEntity $table, $field, $value, $return, $conditions = [])
    {
        return $this->findIn($table, $field, $value, $return, $conditions);
    }

    public function getBelongsToIds(Relation $relation, $parentIds)
    {
        if(!is_null($relation->getSourceField()))
        {
            return $this->findIn($relation->getEntity(), $relation->getEntity()->getPrimaryKey(), $parentIds, $relation->getSourceField()->getName(), $relation->getConditions());
        }else{
            return [];
        }
    }

    public function findRecord(IEntity $entity, $id)
    {
        $query = "SELECT * from {$entity->getTable()} where id = $id limit 1";
        $results = $this->getConnection($entity)->fetchAll($query, false);
        return $results[0][$entity->getTable()]?(object) $results[0][$entity->getTable()]:false;
    }

    public function findOneBY(IEntity $entity, $conditions)
    {
        $dynamicRepo = new DynamicRepo();

        return $dynamicRepo->findOneBY($entity->getTable(), $conditions);
    }

    public function findRecordBy(IEntity $entity, $conditions)
    {
        $dynamicRepo = new DynamicRepo();

        return $dynamicRepo->findBY($entity->getTable(), $conditions);
    }

    public function findAll(IEntity $entity, $field, $ids)
    {
	    $ids = array_filter($ids);
        if(empty($ids)) {
            return [];
        }
	    $ids = array_map(function($value) {
			if (is_string($value)) {
				return "'$value'";
			}
			return $value;
	    }, $ids);
        $values = implode(',', $ids);
        $query = "SELECT * FROM `{$entity->getTable()}` where {$field} in (".$values.")";
        $results = $this->getConnection($entity)->fetchAll($query, false);
        $array = [];
        foreach ($results as $result) {
            $array[] = (object)$result[$entity->getTable()];
        }
        return $array;
    }
}
