<?php


namespace App\Repositories;


use App\Queue\Factory\ListenerFactory;
use Izam\Daftra\Common\Queue\DaftraListener;
use Izam\Daftra\Common\Queue\EventEnvUtil;
use Izam\Daftra\Common\Queue\EventPlatformUtil;
use Izam\Daftra\Common\Queue\ListenerStatusUtil;
use Izam\Daftra\Queue\Events\EventAbstract;


class EventActionRepository extends \Izam\Daftra\Common\Queue\DaftraEventRepository implements \Izam\Daftra\Queue\Repositories\IEventRepository
{
    public static $Saved = false;

    /**
     * @var \AppModel
     */
    private $EventAction;
    /**
     * @var \AppModel
     */
    private $EventActionListener;

    public function __construct()
    {
        $this->EventAction = GetObjectOrLoadModel('EventAction');
        $this->EventActionListener = GetObjectOrLoadModel('EventActionListener');
    }
    /**
     * @param EventAbstract $eventAbstract
     * @return EventAbstract|void
     */
    public function save($eventAbstract)
    {
        $this->EventAction->create();
        $eventData = $eventAbstract->toArray();
        $eventData['site_id'] = getCurrentSite('id');
        $eventData['data'] = json_encode($eventData['data']);
        $eventData['platform'] = EventPlatformUtil::CAKE;
        if (PHP_SAPI !== 'cli') {
            $eventData['runtime_env'] = EventEnvUtil::FOREGROUND;
        } else {
            $eventData['runtime_env'] = EventEnvUtil::BACKGROUND;
        }

        $result = $this->EventAction->save($eventData);
        if(!$result) {
            $id = getCurrentSite('id');
            file_put_contents('/tmp/queue-saver-'.$id.'-error.txt', json_encode([
                'data' => $eventData,
                'validation_errors' => $this->EventAction->validationErrors,
                'stack' => debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 5)
            ]));
        }

        $eventAbstract->setId($this->EventAction->id);
        self::$Saved = true;
        return $eventAbstract;
    }
    /**
     * @param $eventId
     * @return array|void
     */
    public function getEventForeGroundListener($eventId)
    {
        return array_column($this->EventActionListener->find('all', ['conditions' => [
            'EventActionListener.event_action_id' => $eventId,
            'EventActionListener.runtime_env' => EventEnvUtil::FOREGROUND,
            'EventActionListener.status' => ListenerStatusUtil::CREATED,
        ],
        ]), 'EventActionListener');
    }

    /**
     * @param array $eventListeners
     * @return mixed|void
     */
    public function saveEventListeners($eventListeners = [])
    {
        $lisData = [];
        foreach ($eventListeners as $eventListener) {
            /**
             * @var $eventListener DaftraListener
             */
            if(empty($eventListener->toArray()['event_action_id'])) {
                $id = getCurrentSite('id');
                file_put_contents('/tmp/queue-saver-'.$id.'-error.txt', json_encode([
                    'data' => $eventListener->toArray(),
                    'stack' => debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 5)
                ]));
                continue;
            }
            $lisData[] = $eventListener->toArray();
        }
        if(!empty($lisData)) {
            $this->EventActionListener->saveAll($lisData);
        }
    }

    public function generateListenerData($eventListenerHandler)
    {
        return ListenerFactory::getInstance($eventListenerHandler);
    }
}
