<?php

namespace App\Repositories;

use App\Utils\FileUtil;

class WorkFlowRepository
{
    private $workFlowTypeModel;
    public function __construct()
    {
        $this->workFlowTypeModel =  GetObjectOrLoadModel('WorkflowType');
    }

    public function getWorkFlowType($isActive = 1)
    {
        $workFlowType = GetObjectOrLoadModel('WorkflowType');
        return $workFlowType->find('all', ['conditions' => ['WorkflowType.status' => $isActive]]);
    }
}