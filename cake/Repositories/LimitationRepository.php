<?php


namespace App\Repositories;


use App\Utils\FileUtil;
use Izam\Entity\Repository\FileRepository;

class LimitationRepository
{
    private $genericModel;
    private $limitationModel;
    private $siteLimitationModel;
    private $sitePluginModel;
    private $planLimitationModel;

    public function __construct()
    {
        $this->genericModel =  GetObjectOrLoadModel('Staff');
        $this->limitationModel =  GetObjectOrLoadModel('Limitation');
        $this->siteLimitationModel =  GetObjectOrLoadModel('SiteLimitation');
        $this->sitePluginModel =  GetObjectOrLoadModel('SitePlugin');
        $this->planLimitationModel =  GetObjectOrLoadModel('PlanLimitation');
    }

    public function getActiveSiteLimitations($site, $additional_limits = [])
    {
        $query="";
        if (!empty($additional_limits)){
            $query = "OR name IN ('".implode(',', $additional_limits)."')";
        }
        $plan_limitations = $this->planLimitationModel->query("
            SELECT *
            FROM `plan_limitations`
            WHERE find_in_set('".$site['plan_id']."', `plan_ids`)
            AND type = 'integer'
            $query
            ORDER BY `display_order`
        ");

        $plan_limitations = array_column($plan_limitations, 'plan_limitations');
        $final_site_limitations = [];

        foreach ($plan_limitations as $index => $plan_limitation) {

            if ($this->checkIfLimitationIsBoolean($plan_limitation)) {
                continue;
            }

            $limitation = $this->getLimitation($plan_limitation['name']);

            if (! $this->checkIfPluginIsActive($limitation['plugin_id'], $site['id'])) {
                continue;
            }

            $site_limitation = $this->getSiteLimitation($plan_limitation['name'], $site['id']);

//            if (empty($site_limitation)) {
//                continue;
//            }

            $plan_limitations[$index]['limitation'] = $limitation;
            $plan_limitations[$index]['site_limitation'] = $site_limitation;
            $final_site_limitations[] = $plan_limitations[$index];
        }
        $limitation = $this->getLimitation('gb_storage');
        $storage_limitation =  $this->getSiteLimitation('gb_storage', $site['id']);
        if($storage_limitation){
            $final_site_limitations[] = ['name' => 'gb_storage', 'site_limitation' => $storage_limitation, 'limitation' => $limitation];
        }else{
            $final_site_limitations[] = ['name' => 'gb_storage', 'site_limitation' => ["name" => "gb_storage"], 'limitation' => $limitation];
        }

        return $final_site_limitations;

//        $site_limitations = $this->siteLimitationModel->query("
//            SELECT *
//            FROM `site_limitations`
//            WHERE `site_id` = '".$site['id']."'
//        ");
//
//        $site_limitations = array_column($site_limitations, 'site_limitations');
//        $final_site_limitations = [];
//
//        foreach ($site_limitations as $index => $site_limitation) {
//
//            $limitation = $this->getLimitationFromPlanLimitation($site_limitation['name']);
//
//            if (! $this->checkIfPluginIsActive($limitation['plugin_id'], $site['id'])) {
//                continue;
//            }
//
//            $plan_limitation = $this->getPlanLimitationFromSiteLimitation($site_limitation['name'], $site['plan_id']);
//
//            if (empty($plan_limitation)) {
//                continue;
//            }
//
//            if ($this->checkIfLimitationIsBoolean($plan_limitation)) {
//                continue;
//            }
//
//            $site_limitations[$index]['limitation'] = $limitation;
//            $site_limitations[$index]['plan_limitation'] = $plan_limitation;
//            $final_site_limitations[] = $site_limitations[$index];
//        }
//
//        return $final_site_limitations;
    }

//    public function getLimitationFromPlanLimitation($name)
//    {
//        $limitation = $this->limitationModel->query("
//            SELECT *
//            FROM `limitations`
//            WHERE `name` = '$name'
//        ");
//
//        return array_column($limitation, 'limitations')[0];
//    }

    public function getLimitation($name)
    {
        $limitation = $this->limitationModel->query("
            SELECT * 
            FROM `limitations`
            WHERE `name` = '$name'
        ");

        return array_column($limitation, 'limitations')[0];
    }

    public function checkIfPluginIsActive($plugin_id, $site_id)
    {
        if ($plugin_id == 0 || $plugin_id == null) {
            return true;
        }

        $data = $this->sitePluginModel->query("
            SELECT *
            FROM `site_plugins`
            WHERE `site_id` = '$site_id' AND `plugin_id` = '" . $plugin_id . "' AND `active` = 1
        ");

        return !empty($data);
    }

//    public function getPlanLimitationFromSiteLimitation($limitation_name, $plan_id)
//    {
//        $plan_limitation = $this->planLimitationModel->query("
//            SELECT pl.*
//            FROM `plan_limitations` pl
//            WHERE find_in_set(" . $plan_id . ", pl.plan_ids) AND `name` = '$limitation_name'
//            ORDER BY `display_order`
//        ");
//
//        return array_column($plan_limitation, 'pl')[0];
//    }

    public function getSiteLimitation($limitation_name, $site_id)
    {
        $site_limitation = $this->siteLimitationModel->query("
            SELECT * 
            FROM `site_limitations`
            WHERE `site_id` = '".$site_id."' AND `name` = '$limitation_name' 
        ");

        return array_column($site_limitation, 'site_limitations')[0];
    }

    public function checkIfLimitationIsBoolean($plan_limitation)
    {
        if ($plan_limitation['strategy'] == 'embedded' && $plan_limitation['type'] == 'bool') {
            return true;
        }
    }

//    public function formatLimitationForView($site_limitations)
//    {
//        $formatted_limitations = [];
//
//        foreach ($site_limitations as $index => $limitation) {
//
//            $formatted_limitations[$index]['title'] = $limitation['limitation']['title'];
//            $formatted_limitations[$index]['display_order'] = $limitation['limitation']['display_order'];
//
//            $max_value = $limitation['max_value'];
//            $current_value = $this->genericModel->query($limitation['plan_limitation']['minimum_allowed_query'])[0][0];
//            $current_value = $current_value[key($current_value)];
//            $current_value = $current_value ?: 0;
//
//            if ($limitation['name'] == 'files_count') {
//                $bytes = FileUtil::transformSize($current_value, FileUtil::GB, FileUtil::B);
//                $current_value = FileUtil::formatSizeUnits($bytes);
//            }
//
//            if ($limitation['plan_limitation']['strategy'] == 'selectable' && $limitation['plan_limitation']['add_more']) {
//
//                $formatted_limitations[$index]['order_extra'] = true;
//                $formatted_limitations[$index]['btn_url'] = \Router::url(array('controller' => 'sites', 'action' => 'owner_increase_limit', $limitation['name']));
//
//            }
//
//            $formatted_limitations[$index]['max_value'] = $max_value;
//            $formatted_limitations[$index]['current_value'] = $current_value;
//            $formatted_limitations[$index]['percent'] = round($current_value / $max_value, 2) * 100;
//        }
//
//        // Order by limitation `display_order`
//        usort($formatted_limitations, function ($a, $b) {
//            return $a['display_order'] > $b['display_order'];
//        });
//
//        return $formatted_limitations;
//    }

    public function formatLimitationForView($plan_limitations)
    {
        $formatted_limitations = [];

        foreach ($plan_limitations as $index => $limitation) {


            $max_value = $limitation['included_value'];
            if (!empty($limitation['site_limitation'])) {
                $max_value = $limitation['site_limitation']['max_value'];
            }

            $current_value = $this->genericModel->query($limitation['minimum_allowed_query'])[0][0];
            if (!empty($current_value)) {
                $current_value = $current_value[key($current_value)];
            } else if ($limitation['site_limitation']['current_value']) {
                $current_value = $limitation['site_limitation']['current_value'];
            } else {
                $current_value = 0;
            }

            if ($limitation['name'] === 'user_branch_limit') {
                if (ifPluginActive(BranchesPlugin)) {
                    $branchModel = GetObjectOrLoadModel('Branch');
                    $branchesCount = $branchModel->find('count', ['conditions'=> ['status' => 1]]);

                    if ($branchesCount > 0) {
                        $branchesCount = $branchesCount -1;
                        $current_value += $branchesCount;
                    }
                }
            }

            if ($limitation['name'] == 'files_count') {
                $bytes = FileUtil::transformSize($current_value, FileUtil::GB, FileUtil::B);
                $current_value = FileUtil::formatSizeUnits($bytes);
            }

            if ($limitation['name'] == 'gb_storage') {
                $fileRepo = new FileRepository();
                $giga_bytes = round(FileUtil::transformSize($fileRepo->getTotalFilesSize(), FileUtil::B, FileUtil::GB), 2);
                $current_value = $giga_bytes;
                if(!$max_value){
                    $packageId = getCurrentSite('package_id');
                    if($packageId > 1) {
                        $max_value = round(FileUtil::transformSize(50 * 1024, FileUtil::MB, FileUtil::GB), 2);
                    } else {
                        $max_value = round(FileUtil::transformSize(500, FileUtil::MB, FileUtil::GB), 2);
                    }
                }
            }

            if ($limitation['strategy'] == 'selectable' && $limitation['add_more']) {

                $formatted_limitations[$index]['order_extra'] = true;
                $formatted_limitations[$index]['btn_url'] = \Router::url(array('controller' => 'sites', 'action' => 'owner_increase_limit', $limitation['name']));

            }
            $currOverMaxx = $max_value != 0 ? $current_value / $max_value : false;

            // if user storage usage is above 20% show it in site info if not hide it
            if ($limitation['name'] == 'gb_storage' && round($currOverMaxx, 2) * 100 < 20) {
                continue;
            }
            $formatted_limitations[$index]['title'] = $limitation['limitation']['title'];
            $formatted_limitations[$index]['display_order'] = $limitation['limitation']['display_order'];
            $formatted_limitations[$index]['max_value'] = $max_value;
            $formatted_limitations[$index]['current_value'] = $current_value;
            $formatted_limitations[$index]['percent'] = round($currOverMaxx, 2) * 100;
        }

        // Order by limitation `display_order`
        usort($formatted_limitations, function ($a, $b) {
            return $a['display_order'] > $b['display_order'];
        });

        return $formatted_limitations;
    }

    public function checkSiteHasLimitation($limitationName)
    {
        $planId = getCurrentSite('plan_id');
        $plan_limitations = $this->planLimitationModel->query("
            SELECT *
            FROM `plan_limitations`
            WHERE find_in_set('".$planId."', `plan_ids`) AND `name` = '$limitationName'
            ORDER BY `display_order`
        ");

        return count($plan_limitations) > 0;
    }
}
