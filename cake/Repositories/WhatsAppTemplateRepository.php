<?php

namespace App\Repositories;

use App\Domain\WhatsAppTemplateComponentDTO;
use App\Domain\WhatsAppTemplateDTO;

class WhatsAppTemplateRepository
{
    private $model;

    public function __construct()
    {
        $this->model = GetObjectOrLoadModel('WhatsAppTemplate');
    }

    /**
     * @return WhatsAppTemplateDTO[]
     */
    public function getAllTemplates()
    {
        $templates = $this->model->getSmsTemplates(null, 'whatsapp');
        $dtos = [];
        foreach ($templates as $template) {
            $dtos[] = new WhatsAppTemplateDTO(
                $template['WhatsAppTemplate']['id'],
                $template['WhatsAppTemplate']['name'],
                $template['WhatsAppTemplate']['language'],
                $template['WhatsAppTemplate']['category'],
                array_map(function ($component) {
                    return new WhatsAppTemplateComponentDTO(
                        $component['type'],
                        $component['text']
                    );
                }, $template['WhatsAppTemplateComponent'] ?? [])
            );
        }
        return $dtos;
    }
}

