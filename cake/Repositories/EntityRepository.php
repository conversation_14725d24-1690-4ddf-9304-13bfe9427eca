<?php


namespace App\Repositories;


use App\Helpers\ModelHelper;
use Izam\Daftra\Cache\PortalCache;
use Izam\Daftra\Cache\SharedPortalCache;

class EntityRepository
{
    /**
     * @var \AppModel
     */
    private $entityModel;
    /**
     * @var \AppModel
     */
    private $localEntityModel;
    /**
     * @var \AppModel
     */
    private $fieldModel;

    /**
     * @var \AppModel
     */
    private $fieldRelationModel;
       /**
     * @var \AppModel
     */
    private $entityFieldModel;
    /**
     * @var \AppModel
     */
    private $localEntityFieldModel;
    /**
     * @var \AppModel
     */
    private $localFieldRelationModel;

    public function __construct()
    {
        $this->localEntityModel = GetObjectOrLoadModel('LocalEntity');
        $this->localEntityFieldModel= GetObjectOrLoadModel('LocalEntityField');
        $this->localFieldRelationModel = GetObjectOrLoadModel('LocalFieldRelation');
    }

    protected function getEntityModel() {
        if(empty($this->entityModel)) {
            $this->entityModel = GetObjectOrLoadModel('Entity');
        }
        return $this->entityModel;
    }

    protected function getEntityFieldModel() {
        if(empty($this->entityFieldModel)) {
            $this->entityFieldModel = GetObjectOrLoadModel('EntityField');
        }
        return $this->entityFieldModel;
    }

    protected function getFieldRelationModel() {
        if(empty($this->fieldRelationModel)) {
            $this->fieldRelationModel = GetObjectOrLoadModel('FieldRelation');
        }
        return $this->fieldRelationModel;
    }


    public function getEntity($entityKey) {
        $conditions = ['conditions' => ['key' => $entityKey]];
        $entity = $this->getEntityModel()->find('first', $conditions)['Entity'];
        if(!$entity) {
            $entity = $this->localEntityModel->find('first', $conditions)['LocalEntity'];
            if (empty($entity)) {
                throw new \Exception("Entity '$entityKey' not found.");
            }
            $entity['is_global'] = false;
        } else {
            $entity['is_global'] = true;
        }

        return (object) $entity;
    }

    public function getAllEntities() {
        $cacheName = '__entity_cache';
        $allEntities = PortalCache::get($cacheName, function() {
            $allEntities = $this->getEntityModel()->find('all');
            $localEntities = $this->localEntityModel->find('all');
            return array_merge($allEntities, $localEntities);
        });
        $entities = [];
        foreach ($allEntities as $entity) {
            //map by key
            if ( isset($entity['Entity']) ){
                $entity = $entity['Entity'] ;
                $entity['is_global'] = true;
            }else{
                $entity =  $entity['LocalEntity'] ;
                $entity['is_global'] = false;
            }
            $entities[$entity['key']] = (object) $entity;

        }
        return $entities;
    }

    public function getAllFields() {
        $cacheName = '__fields_cache';
        $allFields = SharedPortalCache::get($cacheName, function() {
            $allFields = $this->getEntityFieldModel()->find('all');
            return $allFields;
        });
        $fields = [];
        foreach ($allFields as $field) {
            //map by key
            $fields[$field['EntityField']['entity']][] = $field;
        }
        return $fields;
    }

    public function getAllRelations() {
        $cacheName = '__relations_cache';
        $allRelations = SharedPortalCache::get($cacheName, function() {
            $allRelations = $this->getFieldRelationModel()->find('all');
            return $allRelations;
        });
        $relations = [];
        foreach ($allRelations as $relation) {
            //map by key
            $relations[$relation['FieldRelation']['entity']][] = $relation;
        }
        return $relations;
    }

    public function getEntityFields($entityKey) {
        $fields = $this->getAllFields();
        $fields = $fields[$entityKey];
        $modelName = 'EntityField';
        if(empty($fields)) {
            $conditions = [
                'conditions' => ['entity' => $entityKey,'deleted_at IS NULL'],
                'order' => 'id',
            ];
            $fields = $this->localEntityFieldModel->find('all', $conditions);
            $modelName = 'LocalEntityField';
        }
        $fields = ModelHelper::recordsArrayToObjects($fields, $modelName);
        return $fields;
    }

    public function getEntityRelations($entityKey) {
        $conditions = ['conditions' => ['entity' => $entityKey], 'order' => 'id'];
        $relations = $this->getAllRelations();
        $relations = $relations[$entityKey];
        $modelName = 'FieldRelation';
        $relations = ModelHelper::recordsArrayToObjects($relations, $modelName);
        $localRelations = $this->localFieldRelationModel->find('all', $conditions);
        $modelName = 'LocalFieldRelation';
        $localRelations = ModelHelper::recordsArrayToObjects($localRelations, $modelName);
        $relations = array_merge($relations, $localRelations);
        return $relations;
    }


    public function getChildrenEntities($entityKey) {
        $localEntities = $this->localEntityModel->find('list', ['fields' => ['LocalEntity.key','LocalEntity.label'],'conditions' => ['LocalEntity.parent_entity' => $entityKey, 'LocalEntity.db_name is not null']]);
        $allEntities = $this->getAllEntities();
        $entities = [];
        foreach ($allEntities as $entity) {
            if($entity->parent_entity === $entityKey) {
                $entities[$entity->key] = $entity->label;
            }
        }
        return array_merge($localEntities, $entities);
    }

}
