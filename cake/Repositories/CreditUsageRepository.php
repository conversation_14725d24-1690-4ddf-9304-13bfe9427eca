<?php


namespace App\Repositories;

use CurrencyConverter;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator ;
use Izam\Daftra\Common\Utils\PluginUtil;
use Setting;

class CreditUsageRepository
{

    
    protected $creditCharges = [];


    public function creditChargesMeta($creditCharges, $minimumRedemptionPoints)
    {

        $amount = $total_amount_charge = 0;

        $creditChargesList = [];

        foreach ($creditCharges as $creditCharge) {
            $amount += $creditCharge['credit_charges']['amount'];
            $total_amount_charge += $creditCharge[0]['total_amount_charge'];
            $currentCreditCharge = $creditCharge['credit_charges'];
            $creditChargesList[] = ['id' => $currentCreditCharge['credit_charge_id'], 'amount' => $currentCreditCharge['amount'] - $creditCharge[0]['total_amount_charge']];
        }

        $currentAmount = ($amount - $total_amount_charge);

        return ['currentAmount' => $currentAmount, 'creditChargesList' => $creditChargesList, 'minimumRedemptionPoints' => $minimumRedemptionPoints];
    }


    public function validate($points, $creditChargesMeta)
    {

        $creditCharges =  $creditChargesMeta['creditChargesList'];
        $currentAmount = (float) round($creditChargesMeta['currentAmount'], 4);
        $points = (float) round($points, 4);
 
        if (count($creditCharges) == 0) {
            return false;
        }

        if ($points > $currentAmount) {
            return false;
        }

        return true;
    }

    //minimum redemption points
    public function exceedMinimumRedemption($creditChargesMeta) {
        return $creditChargesMeta['currentAmount'] >= $creditChargesMeta['minimumRedemptionPoints'];
    }


    public function add($creditChargesMeta, $invoice_id, $client_id, $date, $credit_type_id, $points, $discount = null, $currencyCode = null)
    {   

        if($creditChargesMeta['currentAmount'] == 0 || is_null($points)){
            return false; 
        }

        $creditUsage =  GetObjectOrLoadModel('CreditUsage');
        $chargeUsage =  GetObjectOrLoadModel('ChargeUsage');
        $invoice =  GetObjectOrLoadModel('Invoice');
        $creditType = GetObjectOrLoadModel('CreditType');

        $creditChargesList = $creditChargesMeta['creditChargesList'];

        $invoiceObj = $invoice->find ( 'first' , [  'conditions' => ['Invoice.id' =>$invoice_id], 'recursive' => -1]);
 
        $creditTypeName = $creditType->findById($credit_type_id)['CreditType']['name'];

        $creditUsage = $creditUsage->add([
            'client_id'       => $client_id,
            'credit_type_id'  => $credit_type_id,
            'usage_date'      => $date,
            'created'         => date("Y:m:d H:i:s"),
            'modified'        => date("Y:m:d H:i:s"),
            'invoice_id'      => $invoice_id,
            'description'     => sprintf(__("Redeem %d %s for %s Discount in Invoice", true), $points, $creditTypeName, formatPriceDisplayCurrency($discount, $currencyCode)).' #'.$invoiceObj['Invoice']['no']
        ]);
        $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::CREDIT_USAGE_ENTITY_KEY, $creditUsage, 1)->toArray();
        $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::CREDIT_USAGE_ENTITY_KEY);
        $requests = (new EntityActivityLogRequestsCreator(

        ))->create($st, $newData, [], []);
        $activityLogService =  new \App\Services\ActivityLogService();
        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }

        $index = 0;

        // php8
        $elapsedPoints = (float) $points;
        do {
            $minPoints = (float) min($elapsedPoints, $creditChargesList[$index]['amount']);
            $elapsedPoints -= $minPoints;
            if (!empty($minPoints)) {
                $chargeUsage->add([
                    'credit_usage_id'   => $creditUsage,
                    'credit_charge_id'  => $creditChargesList[$index]['id'],
                    'amount'            => $minPoints
                ]);
            }
            $index++;
        } while ($elapsedPoints);

        if (!$creditUsage) {
            return false;
        }

        return true;
    }

    public function calculateDiscountAmount($points, $client_loyalty_conversion_factor, $currency, $date)
    {
        $rate = CurrencyConverter::index($currency, getCurrentSite('currency_code'), $date);
        $discountValue = (float) $points *  (float) $client_loyalty_conversion_factor;
        $discount = round($discountValue / $rate, 3);
        return $discount;
    }

    public function getUsedLoyaltyCreditByInvoiceId($invoice_id) {

        $creditUsage =  GetObjectOrLoadModel('CreditUsage');
        $invoiceCreditUsage = $creditUsage->find('first', ['conditions' => ['invoice_id' => $invoice_id]]);
        $amount = 0;
        if($invoiceCreditUsage) {
            foreach($invoiceCreditUsage['ChargeUsage'] as $chargeUsage) {
                $amount += $chargeUsage['amount'];
            }
        }
        return $amount;
    }

    public function restoreByInvoiceId($invoice_id) {

        $creditUsage =  GetObjectOrLoadModel('CreditUsage');
        $chargeUsage =  GetObjectOrLoadModel('ChargeUsage');

        $invoiceCreditUsage = $creditUsage->find('first', ['conditions' => ['invoice_id' => $invoice_id]]);

        if($invoiceCreditUsage) {

            foreach($invoiceCreditUsage['ChargeUsage'] as $chargeUsageData) {
               $chargeUsage->delete($chargeUsageData['id']); 
            }  

            $creditUsage->delete($invoiceCreditUsage['CreditUsage']['id']);
    
        } 

    }

    public function getChargeUsagesByInvoiceId($invoice_id) {
        $chargeUsage =  GetObjectOrLoadModel('ChargeUsage');
        return  $chargeUsage->query('
                    SELECT
                    charge_usages.*
                FROM
                    credit_usages
                INNER JOIN charge_usages as charge_usages ON(
                        charge_usages.credit_usage_id = credit_usages.id
                    )
                WHERE
                    credit_usages.invoice_id = '.$invoice_id.'
        ');
    }

    public function getCreditUsageByInvoice($invoice_id)
    {
        $creditUsage =  GetObjectOrLoadModel('CreditUsage');
        $creditUsage->recursive = 2;
        return $creditUsage->find('first', ['conditions' => ['invoice_id' => $invoice_id]]);
    }

}
