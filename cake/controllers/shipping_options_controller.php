<?php

class ShippingOptionsController extends AppController {
	var $name = 'ShippingOptions';
	var $autoRender = false;

	function owner_index() {
		$ShippingOptionsModel = GetObjectOrLoadModel('ShippingOption');
		$shipping_options = $ShippingOptionsModel->find('all', ['recursive' => -1, 'conditions' => [
			'ShippingOption.status' => 1,
			'ShippingOption.deleted_at IS NULL'
		]]);
		$this->set('rest_model_name', 'CustomForm');
		$this->set('rest_item', $shipping_options);
		$this->render("view");
	}
}