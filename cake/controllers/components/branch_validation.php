<?php

class BranchValidationComponent
{
    private $controller;

    function initialize(&$controller, $settings = [])
    {
        $this->controller =& $controller;
    }

    public function validatePosBranch($shift)
    {
        if (!ifPluginActive(BranchesPlugin)) return true;
        $BranchesModel = GetObjectOrLoadModel('Branch');
        $branchName = $BranchesModel->find('first', ['conditions' => ['Branch.id' => $shift['PosShift']['branch_id']]])['Branch']['name'];
        if ($shift['PosShift']['branch_id'] != getCurrentBranchID()) {
            $this->controller->flashMessage(sprintf(__("This POS Session is in Branch %s", true), $branchName));
            $this->controller->redirect(['action' => 'index']);
        }
        return true;
    }
}
