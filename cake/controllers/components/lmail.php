<?php
if(!class_exists("EmailComponent")) {
    require_once CAKE_CORE_INCLUDE_PATH . DS . 'cake' . DS . 'libs' . DS . 'controller' . DS . 'components' . DS . 'email.php';
}
class LmailComponent extends EmailComponent {

	var $name = 'Lmail';
	
	var $language = '41';
	
	public function send($content = null, $template = null, $layout = null) {
		if (!$template && $this->template && $this->template != 'plain'){
			$template = $this->template;
		}
		
		$content = $this->_renderTemplateContent($template);
		return parent::send($content, 'plain', $layout);
	}
	
	function _renderTemplateContent($template) {
		$LanguageModel = GetObjectOrLoadModel('Language');
		/* @var $LanguageModel Language */
		$language = $LanguageModel->field('Language.code3', array('Language.id' => $this->language));
		
		$templateFile = APP . 'locale' . DS . $language . DS . 'LC_MESSAGES' . DS . 'emails' . DS . $template . '.ctp';
		if (!file_exists($templateFile)){
			$templateFile = APP . 'locale' . DS . 'eng' . DS . 'LC_MESSAGES' . DS . 'emails' . DS . $template . '.ctp';
		}

		$view = new View($this->Controller, false);
		$contents = $view->element('../../' . str_replace(array(APP, '.ctp'), '', $templateFile), $this->Controller->viewVars, true);
		
//		ob_start();
//		extract($this->Controller->viewVars);
//		require $templateFile;
//
//		$contents = ob_get_contents();
//		ob_end_clean();
		return $contents;
	}
}
