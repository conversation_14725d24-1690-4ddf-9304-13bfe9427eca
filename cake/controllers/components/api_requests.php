<?php

use Rollbar\Rollbar;

App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));

/**
 * @property EmailComponent $Email
 */
class ApiRequestsComponent {

    /**
     * @var AppController
     */
    var $Controller;

    private $statusCode;

    /**
     * @param $url
     * @param bool $apiKey
     * @param bool $method
     * @param array $params
     * @param array $extraHeaders
     * @return mixed
     */
    public function request($url, $apiKey = false, $method = false, $params = [], $extraHeaders = [], $timeout = 20, $background = 0)
    {
        if (PHP_SAPI == 'cli' || $apiKey) {
            $apikeyModel = GetObjectOrLoadModel('ApiKey');

            $apiKey = $apikeyModel->findOrCreate('internalApi')['ApiKey']['key'];
        }
        $beta = getCurrentSite('beta_version');
        $subDomain = getCurrentSite('subdomain');
        if ($beta == 1 && getenv('APP_ENV') !== 'local') {
            $subDomain = str_replace(Domain_Short_Name, Beta_Domain, $subDomain);
        } else {
            $subDomain = str_replace(Beta_Domain, Domain_Short_Name, $subDomain);
        }
        $url = 'https://' . $subDomain . $url;
        if (!$background) {
            return $this->curlRequest($url, $apiKey, $method, $params, $extraHeaders, $timeout);
        }
        return $this->passRequest($url, $apiKey, $method, $params, $extraHeaders, $timeout);
    }

    public function curlRequest($url, $apiKey = false, $method = false, $params = [], $extraHeaders = [], $timeout = 20)
    {
        $beta = getCurrentSite('beta_version');
        $subDomain = getCurrentSite('subdomain');
        if ($beta == 1) {
            $subDomain = str_replace(Domain_Short_Name, Beta_Domain, $subDomain);
        } else {
            $subDomain = str_replace(Beta_Domain, Domain_Short_Name, $subDomain);
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 0);
        curl_setopt($ch, CURLOPT_DNS_USE_GLOBAL_CACHE, 0);
        curl_setopt($ch, CURLOPT_RESOLVE,  ["{$subDomain}:80:127.0.0.1","{$subDomain}:443:127.0.0.1"]);

        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, 1);
        } else if ($method) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        }
        if (!empty($params)) {
            $data = json_encode($params, JSON_UNESCAPED_SLASHES);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        $httpHeaders = ['Connection: close', 'content-type: application/json'];
        if ($apiKey) {
            $sessionClosed = false;
            $httpHeaders[] = 'HTTP_APIKEY: ' . $apiKey;
            $httpHeaders[] = 'APIKEY: ' . $apiKey;
        } else {
            //take care to close the session before send and start it again
            $cookie='Cookie: ' . session_name() . '=' . session_id();
            if(isset($_COOKIE['useRedis']) && $_COOKIE['useRedis']==1){
            $cookie .=';useRedis=1';
            }
            $httpHeaders[] = $cookie;
            $sessionClosed = true;
        }
        if ($extraHeaders) {
            $httpHeaders = array_merge($httpHeaders, $extraHeaders);
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $httpHeaders);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        $flashedMessages = $_SESSION['Message'];
        if ($sessionClosed) {
	        session_write_close();
        }
        $result = curl_exec($ch);
        if ($sessionClosed) {
            session_start();
            $_SESSION['Message'] = $flashedMessages;
        }

        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $this->setStatusCode($httpcode);
        if ($error = curl_error($ch)) {
            $data_rollbar = [];
            $data_rollbar['url'] = $url;
            $data_rollbar['method'] = $method;
            $data_rollbar['data'] = $data;
            $data_rollbar['headers'] = $httpHeaders;
//            Rollbar::error('Curl Url fail', ['errors' => curl_error($ch), 'data' => $data_rollbar]);
            return false;
        }
        return json_decode($result, true);
    }

    public function passRequest($url, $apiKey = false, $method = false, $params = [], $extraHeaders = [], $timeout = 20)
    {
        $data = [$url, $apiKey, $method, $params, $extraHeaders, $timeout ];
        $body = base64_encode(serialize($data));
        $fullVersion = explode('.', PHP_VERSION);
        $version = $fullVersion[0].'.'.$fullVersion[1];
        $command = "nohup /usr/bin/php{$version} " . APP.WEBROOT_DIR. "/cron.php /sites/run_background_api/{$body} > /dev/null 2>&1 & echo $!";
        exec($command ,$op);
    }

    public function translateRequest($body)
    {
        $body = unserialize(base64_decode($body));
        $this->curlRequest($body[0], $body[1], $body[2], $body[3], $body[4], $body[5]);
    }

    public function startup(&$controller) {
        $this->Controller = $controller;
    }

    public function initialize(&$controller, $settings=array())
    {
        
    }

    public function shutdown(&$controller){
        
    }

    private function setStatusCode($status_code)
    {
        $this->statusCode = $status_code;
    }

    public function getStatusCode()
    {
    	return $this->statusCode;
    }

}
