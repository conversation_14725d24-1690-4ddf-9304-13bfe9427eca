<?php

class ClientValidationComponent
{
	private $controller;

	function initialize(&$controller, $settings = [])
	{
		$this->controller =& $controller;
	}

	public function validateClientSuspended($client_id, $addOneBehaviour = true)
	{
		if (empty($client_id)) return;
		$ClientModel = GetObjectOrLoadModel('Client');
		$client = $ClientModel->find('first', ['recursive' => -1, 'conditions' => ['Client.id' => $client_id]]);
		if ($client && $client['Client']['suspend'] == "1") {
            if (!$addOneBehaviour) {
                return ['message' => __('You cannot create a new transaction for a suspended client', true)];
            }

			if (IS_REST) {
				$this->controller->cakeError("error400", ["message" => __('You cannot create a new transaction for a suspended client', true)]);
			}
			$this->controller->flashMessage(__('You cannot create a new transaction for a suspended client', true));
			$this->controller->redirect(['action' => 'index']);
		}
	}
}
