<?php

use Izam\Aws\Aws;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\ShortUrl\ShortUrl;
use Izam\Logging\Service\RollbarLogService;
use Izam\Template\EmailSender\Services\SiteSMTPTokenManager;
use Rollbar\Payload\Level;
use Rollbar\Rollbar;

App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));

/**
 * @property EmailComponent $Email
 */
class SysEmailsComponent {

    /**
     * @var AppController
     */
    var $Controller; 

    /**
     * @var SystemEmail
     */
    var $SystemEmail;
    var $components = array('Email');
    var $is_behind = false;
    public $error_message = "";

    public function startup(&$controller) {
        $this->Controller = $controller;
        $this->SystemEmail = GetObjectOrLoadModel('SystemEmail');
    }

    public function _get_smtp($site = null) {

        if ($site['use_smtp'] == 1) {
            $ssl = $site['smtp_ssl'] == 1 ? "ssl://" : "";
            $array = array(
                'port' => $site['smtp_port'] ? $site['smtp_port'] : ($site['smtp_ssl'] ? 465 : 25),
                'timeout' => '30',
                'host' => $ssl . $site['smtp_host'],
                'username' => $site['smtp_user_name'],
                'password' => $site['smtp_password'],
                'client' => Domain_Name_Only
            );
        } else {
            return array();
        }


        return $array;
    }

    public function _set_smtp($id = null) {

        $array = $this->_get_smtp($id);

        if (isset($array['port'])) {

            $this->Email->smtpOptions = $array;
            $this->Email->delivery = 'smtp';
            $this->Email->is_behind = false;
        }
    }

    public function QuoteRequest($Supplier, $RequestID, $site, $QuoteRequest) {

        $this->Controller->loadModel('SupplierDirectory');
        $this->Controller->loadModel('QuoteRequest');
        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate('quote-request');
        $Supplier['SupplierDirectory'] = $Supplier;

        $SupplierHash = $this->Controller->SupplierDirectory->getHash($Supplier);

        $QuoteRequestRow = $this->Controller->QuoteRequest->read(null, $RequestID);
        $RequestHash = $this->Controller->QuoteRequest->getHash($QuoteRequestRow);

        $QuoteLink = "https://" . Portal_Full_Name . "/supplier/quote/$RequestID/$RequestHash";

        $RemoveLink = "https://" . Portal_Full_Name . "/supplier/unsubscribe/{$Supplier['SupplierDirectory']['id']}/$SupplierHash";

        $HomeLink = "https://" . Portal_Full_Name . "/supplier/index/{$Supplier['SupplierDirectory']['id']}/$SupplierHash";

        $placeholders = array(
            '{%supplier-company-name%}' => $Supplier['company_name'],
            '{%quote-request-id%}' => $RequestID,
            '{%quote-request-table%}' => $QuoteRequest,
            '{%quote-request-link%}' => $QuoteLink,
            '{%supplier-home-link%}' => $HomeLink,
            '{%remove-link%}' => $RemoveLink,
        );


        $to = $Supplier['email'];
        // echo $to;
        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $Supplier['id']), array('from' => array('name' => Site_Full_name_NoSpace . ' Suppliers Directory', 'email' => 'noreply@' . Domain_Short_Name), 'more_bcc' => array($this->Controller->config['txt.admin_mail']), 'use_smtp' => 0));
        // var_dump($result);
        // die();

        return $result;
    }

    public function SharePost($ClientID, $PostID, $site) {

        $this->Controller->loadModel('Client');
        $this->Controller->loadModel('Post');
        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate('share-with-client');

        $ClientRow = $this->Controller->Client->read(null, $ClientID);
        $client = $ClientRow['Client'];
        $client_login_hash = $this->Controller->Client->getHash($ClientID);

        $PostLink = Router::url('/', true) . "posts/view/$PostID/$client_login_hash";

        $view = new View($this->Controller, false);

        $placeholders = array(
            '{%post-link%}' => $PostLink,
            '{%post_link%}' => $PostLink,
        );
		
		if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];
			
		}

        $placeholders = $placeholders + PlaceHolder::staff_place_holder($client['staff_id']) + PlaceHolder::site_place_holder($site) + PlaceHolder::client_place_holder($ClientRow);



        $result = $this->sendEmail($ClientRow['Client']['email'], $site, $defaultTemplate, $placeholders, array(), array('client_id' => $ClientRow['Client']['id']), array());
        // var_dump($result);
        // die();

        return $result;
    }

    public function staffDetails($staff, $site) {

        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate('staff-details');
        
        $loginLink = Router::url('/', true);


        if (empty($staff['password'])) {
            $staff['password'] = substr(base64_encode(HashPassword(time() . mt_rand())), 0, 7);
            $this->Controller->loadModel('Staff');
            $this->Cotnroller->Staff->id = $staff['id'];
            $this->Controller->Staff->saveField('password', HashPassword($staff['password']), array('validate' => false, 'callbacks' => false));
        }

        $placeholders = array(
            '{%staff-name%}' => $staff['name'],
            '{%staff_name%}' => $staff['name'],
            '{%staff-email%}' => $staff['email'],
            '{%staff_email%}' => $staff['email'],
            '{%staff-password%}' => $staff['password'],
            '{%staff_password%}' => $staff['password'],
            '{%login-link%}' => "<a href=\"$loginLink\">$loginLink</a>",
            '{%login-url%}' => $loginLink,
            '{%login_link%}' => "<a href=\"$loginLink\">$loginLink</a>",
        );


        $to = $staff['email'];
        // echo $to;
        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('to_staff_id' => $staff['id']));
        // var_dump($result);
        // die();

        return $result;
    }

    public function clientDetails($client, $site) {

        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate('client-details');

        $loginLink = Router::url('/', true);


        if (empty($client['password'])) {
            $client['password'] = substr(base64_encode(HashPassword(time() . mt_rand())), 0, 7);
            $this->Controller->loadModel('Client');
            $this->Controller->Client->id = $client['id'];
            $this->Controller->Client->saveField('password', HashPassword($client['password']), array('validate' => false, 'callbacks' => false));
        }



        $placeholders = PlaceHolder::site_place_holder($site) + PlaceHolder::staff_place_holder(isset($client['staff_id']) ? $client['staff_id'] : $site['staff_id']) + PlaceHolder::client_place_holder($client) + array(
            '{%client-password%}' => $client['password'],
            '{%client_password%}' => $client['password'],
            '{%login-link%}' => "<a href=\"$loginLink\">$loginLink</a>",
            '{%login_link%}' => "<a href=\"$loginLink\">$loginLink</a>",
            '{%login-url%}' => $loginLink,
        );
		
		if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];
			
		}


        $to = $client['email'];
        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $client['id']));


        return $result;
    }

    function __addFormOptions($options) {
        if (!empty($options['cc_email'])) {
            if (strpos($options['cc_email'], ','))
                $this->Email->cc = explode(',', strtolower($options['cc_email']));
            else if (strpos($options['cc_email'], ';'))
                $this->Email->cc = explode(';', strtolower($options['cc_email']));
            else
                $this->Email->cc = array(strtolower($options['cc_email']));
        }
        $bcc_array = array();
        if (!empty($options['bcc_email'])) {
            if (strpos($options['bcc_email'], ','))
                $bcc_array = explode(',', $options['bcc_email']);
            else if (strpos($options['bcc_email'], ';'))
                $bcc_array = explode(';', $options['bcc_email']);
            else
                $bcc_array[] = $options['bcc_email'];
        }

        if ($options['enable_bcc']) {
            $bcc_array[] = getAuthOwner('email');
        }

        if (!empty($bcc_array)) {
            $this->Email->bcc = $bcc_array;
        }
    }

    public function _set_smtp_to($to) {


        $this->is_behind = false;
        $this->Email->delivery = 'mail';

        $smtp_domains = array('live1.', 'hotmail1.', 'outlook1.');

        foreach ($smtp_domains as $domain)
            if (strpos($to, $domain)) {
                $ssl = "ssl://";
                $this->Email->smtpOptions = array('');
                $array = array(
                    'port' => 587,
                    'timeout' => '30',
                    'host' => 'smtp.office365.com',
                    'username' => '<EMAIL>',
                    'password' => 'Mmoohh126',
                    'client' => Domain_Name_Only
                );
                $this->Email->smtpOptions = $array;
                $this->Email->delivery = 'smtp';
                $this->is_behind = true;


                break;
            }
    }

    public function sendEmail($to, $site, $template, $placeholders, $attachments, $email_log_data = array(), $options = array()) {


        $view = new View($this->Controller, false);

        if (empty($site))
            $site = getCurrentSite();
        
        if ($site['use_smtp'] != 1) {
            $email_logs = ClassRegistry::init('EmailLog');
            $email_logs->recursive = -1;
            $date = date("Y-m-d");
            $today_email_count = $email_logs->find('all', array('fields' => 'sum(ROUND ((LENGTH(send_to)- LENGTH( REPLACE ( send_to, ",", "") ) ) / LENGTH(","))+1) AS count', 'conditions' => array('Date(EmailLog.sent_date)' => $date)));
            $today_email_count = intval($today_email_count[0][0]['count']);

            if ($today_email_count >= 100) {
                $this->error_message = __("You can't send more than 1,00 emails per day.", true);
                return false;
            }
        }
        if(key_exists('{%view-link%}',$placeholders)) {
            $placeholders['{%view-link%}'] = sprintf('<a href="%s">%s</a>',$placeholders['{%view-link%}'],$placeholders['{%view-link%}']);
        }    

//debug($site);
        $site_placeholders = array();
        if (empty($site['address1'])) {
            $site_placeholders['{%address1%}<br/>'] = $site_placeholders['{%address1%}<br />'] = $site_placeholders['{%address1%}<br>'] = '';
        }
        if (empty($site['address2'])) {
            $site_placeholders['{%address2%}<br/>'] = $site_placeholders['{%address2%}<br />'] = $site_placeholders['{%address2%}<br>'] = '';
        }
        $site_placeholders = $site_placeholders + PlaceHolder::site_place_holder($site);


        $current_site = getAuthOwner();
        $staff_place_holder = PlaceHolder::staff_place_holder($current_site['staff_id']);



        $placeholders = array_merge($staff_place_holder, $site_placeholders, $placeholders);
        if (!empty($placeholders['{%client-first-name%}'])) {
            $placeholders2[__('Hi', true) . ' ' . '{%client-name%}'] = __('Hi', true) . ' ' . '{%client_first_name%}';
            $placeholders2[__('Dear', true) . ' ' . '{%client-name%}'] = __('Dear', true) . ' ' . '{%client_first_name%}';
            $placeholders2[__('Hello', true) . ' ' . '{%client-name%}'] = __('Hello', true) . ' ' . '{%client_first_name%}';
            $placeholders2[__('Hi', true) . ' ' . '{%client_name%}'] = __('Hi', true) . ' ' . '{%client_first_name%}';
            $placeholders2[__('Dear', true) . ' ' . '{%client_name%}'] = __('Dear', true) . ' ' . '{%client_first_name%}';
            $placeholders2[__('Hello', true) . ' ' . '{%client_name%}'] = __('Hello', true) . ' ' . '{%client_first_name%}';
            $placeholders = array_merge($placeholders2, $placeholders);
        }
        
        $placeholders['{%app_logo%}'] = 'https://' . Domain_Short_Name . '/themed/'. Domain_Name_Only .'/images/logo-'. (getCurrentSite('language_code') == 7 ? 'ar' :'en') .'.png';


        if(isset($template['EmailTemplate']))
        {
            $subject = PlaceHolder::replace($template['EmailTemplate']['subject'], array_keys($placeholders), $placeholders);
            $message = $template['EmailTemplate']['body'];
        }
        else if(isset($template['PrintableTemplate']))
        {
            $subject = PlaceHolder::replace($template['PrintableTemplate']['subject'], array_keys($placeholders), $placeholders);
            $message = $template['PrintableTemplate']['body'];
        }
        // used to replace images uploaded form editor .
        $message = str_replace( "../../../",'https://'.$current_site['subdomain']."/", $message);
        
        $message = PlaceHolder::replace($message, array_keys($placeholders), $placeholders);

        $message=str_replace(Beta_Domain, Domain_Short_Name, $message);
        $allow = true;

        preg_match_all('#\bhttps?://[^,\s()<>]+(?:\([\w\d]+\)|([^,[:punct:]\s]|/))#', $message, $match);
        foreach ($match[0] as $url) {
            $url = strtolower($url);
            if (!strpos($url, strtolower($current_site['subdomain'])) && !strpos($url, strtolower('daftra')) && !strpos($url, strtolower('enerpize')) && !strpos($url, strtolower('onlineinvoices')) && !strpos(($url), '.png') && !strpos(($url), '.jpg') && !strpos(($url), '.woff') && !strpos(($url), '.gif') && $current_site['plan_id'] == 1) {
                $allow = false;
            }
        }





        if ($site['use_smtp'] != 1 and $allow == false) {

            $smtp_option = '<b><a target="_blank" href="' . Router::url('/smtp_settings', true) . '">SMTP option</a></b>';
            $this->error_message = __("It is not allowed to send a custom email with external URLs using a free accounts, please activate the $smtp_option, upgrade your account or contact us for more information", true);
            return false;
        }
        $uniq = uniqid("email_");
        $uniq_img = "https://" . $site['subdomain'] . '/email_view/' . $uniq;
        $messagetosend = $message . "<img width=\"1\" height=\"1\" src='{$uniq_img}'>";
        $this->_set_smtp_to($to);
        $this->_set_smtp($site);



        if ($site['use_smtp'] != 1) {
            $this->Email->headers = array('on-behalf-of' => $site['email']);
        }
        $email_list = $this->fix_arabic_names($to);


        $this->Email->to = implode(',', $email_list);



        if (isset($options['from']) && is_array($options['from'])) {
            $business_name = $options['from']['name'];
            $business_email = $options['from']['email'];
        } else {
            $business_name = $site['use_smtp'] == 1 ? $site['smtp_from_name'] : str_replace(['(', ')'], '', $site['business_name']);
            $business_email = $site['use_smtp'] == 1 ? $site['smtp_from_email'] : strtolower($site['email']);
        }

        if ($this->is_behind) {
            $this->Email->replyTo = $site['email'];
            $business_email = '<EMAIL>';
        }
        $business_name = implode(' ', array_slice(explode(' ', $business_name), 0, 5));
        $getFrom = $this->_getFrom($business_name, $business_email);
        // $f_from=implode(',',  $this->fix_arabic_names($getFrom));
        $from = $this->Email->from = $getFrom;


        $this->Email->sendAs = 'html';
        $this->Email->template = 'system-email';
        $this->Email->subject = $subject;
        $this->Email->attachments = $attachments;

        $this->Email->cc = array();
        $this->Email->bcc = array();

        if (!empty($template['EmailTemplate']['cc_email'])) {
            if (strpos($template['EmailTemplate']['cc_email'], ','))
                $this->Email->cc = explode(',', strtolower($template['EmailTemplate']['cc_email']));
            else if (strpos($template['EmailTemplate']['cc_email'], ';'))
                $this->Email->cc = explode(';', strtolower($template['EmailTemplate']['cc_email']));
            else
                $this->Email->cc = array(strtolower($template['EmailTemplate']['cc_email']));
        }
        else if(!empty($template['PrintableTemplate']['cc_email']))
        {
            if (strpos($template['PrintableTemplate']['cc_email'], ','))
                $this->Email->cc = explode(',', strtolower($template['PrintableTemplate']['cc_email']));
            else if (strpos($template['PrintableTemplate']['cc_email'], ';'))
                $this->Email->cc = explode(';', strtolower($template['PrintableTemplate']['cc_email']));
            else
                $this->Email->cc = array(strtolower($template['PrintableTemplate']['cc_email']));
        }

        $bcc_array = array();
        if (!empty($template['EmailTemplate']['bcc_email'])) {
            if (strpos($template['EmailTemplate']['bcc_email'], ','))
                $bcc_array = explode(',', $template['EmailTemplate']['bcc_email']);
            else if (strpos($template['EmailTemplate']['bcc_email'], ';'))
                $bcc_array = explode(';', $template['EmailTemplate']['bcc_email']);
            else
                $bcc_array[] = $template['EmailTemplate']['bcc_email'];
        }
        else if (!empty($template['PrintableTemplate']['bcc_email'])) {
            if (strpos($template['PrintableTemplate']['bcc_email'], ','))
                $bcc_array = explode(',', $template['PrintableTemplate']['bcc_email']);
            else if (strpos($template['PrintableTemplate']['bcc_email'], ';'))
                $bcc_array = explode(';', $template['PrintableTemplate']['bcc_email']);
            else
                $bcc_array[] = $template['PrintableTemplate']['bcc_email'];
        }

        $to_array = array();
        if (!empty($template['EmailTemplate']['to_email'])) {
            if (strpos($template['EmailTemplate']['to_email'], ','))
                $to_array = explode(',', $template['EmailTemplate']['to_email']);
            else if (strpos($template['EmailTemplate']['to_email'], ';'))
                $to_array = explode(';', $template['EmailTemplate']['to_email']);
            else
                $to_array[] = $template['EmailTemplate']['to_email'];
        } else if (!empty($template['PrintableTemplate']['to_email'])) {
            if (strpos($template['PrintableTemplate']['to_email'], ','))
                $to_array = explode(',', $template['PrintableTemplate']['to_email']);
            else if (strpos($template['PrintableTemplate']['to_email'], ';'))
                $to_array = explode(';', $template['PrintableTemplate']['to_email']);
            else
                $to_array[] = $template['PrintableTemplate']['to_email'];
        }

        if (isset($template['EmailTemplate']) && isset($template['EmailTemplate']['enable_bcc']) && $template['EmailTemplate']['enable_bcc']) {
            $bcc_array[] = $site['email'];
        } else if (isset($template['PrintableTemplate']) && $template['PrintableTemplate']['enable_bcc']) {
            $bcc_array[] = $site['email'];
        }

        if (isset($options['more_bcc'])) {
            if (is_array($options['more_bcc'])) {
                foreach ($options['more_bcc'] as $bcc) {
                    $bcc_array[] = $bcc;
                }
            }
        }

        if (!empty($bcc_array)) {
            $this->Email->bcc = $bcc_array;
        }

        $this->Controller->set('emailMessage', $messagetosend);
        $sites=explode(",",\get_portal_setting_value('no_auto_suspend'));

        if($site['use_smtp'] == 0 && !in_array(getCurrentSite('id'),$sites)){


            if((count($this->Email->cc) + count($bcc_array) + count($to_array)) > 20){
                \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , "Spam Email Sending Alert", ['site_id' => getCurrentSite('id'), 'cc' => $this->Email->cc, 'bcc' => $bcc_array, 'to' => $to_array]);


                suspendSite();

                return false;
            }
            $this->Controller->loadModel('EmailLog');
            if(php_sapi_name()!='cli') {
                if (
                    $this->Controller->EmailLog->find('count', [
                        'conditions' => [
                            'EmailLog.staff_id' => getAuthOwner('staff_id'),
                            'EmailLog.sent_date >' => date('Y-m-d H:i:s', time() - (60 * 60))
                        ]]) >= 50
                    || $this->Controller->EmailLog->find('count', [
                        'conditions' => [
                            'EmailLog.staff_id' => getAuthOwner('staff_id'),
                            'EmailLog.sent_date >' => date('Y-m-d H:i:s', time() - (60 * 60 * 24))
                        ]]) >= 200
                ) {
                    \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO, "Spam Email Sending Alert", ['site_id' => getCurrentSite('id'), 'staff_id' => getAuthOwner('staff_id')]);

                        suspendSite();

                    return false;
                }
            }
        }

        if (isset($options['use_smtp']) and $options['use_smtp'] == 0) {
            unset($this->Email->smtpOptions);
            $this->Email->delivery = 'mail';
        }

        if (!empty($site['smtp_credentials'])) {
            $SiteModel = GetObjectOrLoadModel('Site');
            $dbSite = $SiteModel->find('first', ['conditions' => ['Site.id' => getCurrentSite('id')], 'recursive' => -1]);
            $this->Email->delivery = $site['smtp_host'] == 'smtp.office365.com' ? 'outlook' : 'google';

            if ($this->Email->delivery == 'google') {
                try {
                    $credentials = SiteSMTPTokenManager::getAccessToken($dbSite['Site']['smtp_credentials']);
                } catch (\Exception $e) {
                    RollbarLogService::log(Level::ERROR, $e->getMessage(), [],true);
                    $link = '/smtp_settings';
                    $this->error_message = sprintf(
                        __t('Please reauthenticate to continue by clicking %s.'),
                        sprintf(__t("<a  href='$link'>%s</a>"), __t('here')),
                    );
                    return false;
                }
            } else {
                $credentials = $dbSite['Site']['smtp_credentials'];
            }

            $this->Email->credentials = $credentials;
            $this->Email->username = $site['smtp_user_name'];
            $this->Email->senderMail = $site['smtp_from_email'];
            $this->Email->senderName = $site['smtp_from_name'];
        }

        $result = $this->Email->send();


        if ($result) {

            $attachemnt_names = array();

            if (!empty($attachments)) {
                foreach ($attachments as $key => $file)
                    if (!str_contains($key, 's3_')) {
                        $attachemnt_names[] = basename($file);
                    } else {
                        $attachemnt_names[] = $key;
                    }
            }

            $this->Controller->loadModel('EmailLog');
            $this->Controller->EmailLog->create();
 
            if ($this->Controller->EmailLog->save(array(
                        'EmailLog' => array_merge(array(
                            'uniq_hash' => $uniq,
                            'site_id' => $site['id'],
                            'cc_email' => implode(', ', $this->Email->cc),
                            'bcc_email' => implode(', ', $this->Email->bcc),
                            'subject' => $subject,
                            'body' => $message,
                            'sent_date' => date('Y-m-d H:i:s'),
                            'send_to' => $to,
                            'send_from' => $from,
                            'use_smtp' => $site['use_smtp'],
                            'staff_id' => getAuthOwner('staff_id'),
                            'attachments' => implode(', ', $attachemnt_names),
                                ), $email_log_data)
                    ))) {

                $id = $this->Controller->EmailLog->id;

                return $id;
            }
    }else{
        if($this->Email->delivery=='smtp'){
            $this->error_message = 
                trim($this->Email->smtpError) == '' ? 
                    'Please recheck SMTP Settings.' : 
                    $this->Email->smtpError;
        } elseif($this->Email->delivery == 'xoauth2' || $this->Email->delivery == 'outlook') {
            if ($this->Email->error) {
                $this->error_message = $this->Email->error;
            }else{
                $link = '/smtp_settings';
                $this->error_message = sprintf(
                    __t('Please reauthenticate to continue by clicking %s.'),
                    sprintf(__t("<a  href='$link'>%s</a>"), __t('here')),
                );
            }
        } else {
            if (!mxrecordValidate($from)) {
                $this->error_message = sprintf(__("The email %s seems to not be reachable, please enter a valid email", true), htmlentities($from));
            }
        }
    }
        return $result;
    }

    public function invoice($invoice, $client, $site, $template = false, $attached_docs = array(), $to_owner = false, $more_emails = array(), $post_attachments = [], $force_attach = false,$s3_attachments = []) {
		
		if (empty($site)) $site = getCurrentSite();

        $hash = $this->Controller->Invoice->getHash($invoice);

        $site['subdomain'] = getDomain();

        $viewLink = 'https://' . $site['subdomain'] . '/invoices/view/' . $invoice['id'] . '?hash=' . $hash;
        if ($to_owner) {
            $viewLink = 'https://' . $site['subdomain'] . '/owner/invoices/view/' . $invoice['id'];
        }


        if ($invoice['type'] == "5") {

            $viewLink = 'https://' . $site['subdomain'] . '/invoices/view_creditnote/' . $invoice['id'] . '?hash=' . $hash;
            if ($to_owner) {
                $viewLink = 'https://' . $site['subdomain'] . '/owner/invoices/view_creditnote/' . $invoice['id'];
            }
        }

        if ($invoice['type'] == "6") {
            $viewLink = 'https://' . $site['subdomain'] . '/invoices/view_refund/' . $invoice['id'] . '?hash=' . $hash;
            if ($to_owner) {
                $viewLink = 'https://' . $site['subdomain'] . '/owner/invoices/view_refund/' . $invoice['id'];
            }
        }

        if ($invoice['type'] == Invoice::DEBIT_NOTE) {
            $viewLink = 'https://' . $site['subdomain'] . '/invoices/view_debitnote/' . $invoice['id'] . '?hash=' . $hash;
            if ($to_owner) {
                $viewLink = 'https://' . $site['subdomain'] . '/owner/invoices/view_debitnote/' . $invoice['id'];
            }
        }

        if ($invoice['type'] == Invoice::ADVANCE_PAYMENT) {
            $viewLink = 'https://' . $site['subdomain'] . '/invoices/view_advance_payment/' . $invoice['id'] . '?hash=' . $hash;
            if ($to_owner) {
                $viewLink = 'https://' . $site['subdomain'] . '/owner/invoices/view_advance_payment/' . $invoice['id'];
            }
        }

        $paymentLink = 'https://' . $site['subdomain'] . '/invoices/pay/' . $invoice['id'] . '?hash=' . $hash;

        $current_site = getAuthOwner();

        $staff_place_holders = PlaceHolder::staff_place_holder($invoice['staff_id']);


        $view = new View($this->Controller, false);

	    $ShortUrlGenerator = new ShortUrl(getPDO());
        $placeholders = PlaceHolder::invoice_place_holder($invoice) + [
            '{%payment-link%}' => '<a href="' . $paymentLink . '">' . $paymentLink . '</a>',
            '{%payment_link%}' => '<a href="' . $paymentLink . '">' . $paymentLink . '</a>',
            '{%view-link%}' => '<a href="' . $viewLink . '">' . $viewLink . '</a>',
            '{%view-url%}' => $viewLink,
            '{%view_link%}' => '<a href="' . $viewLink . '">' . $viewLink . '</a>',
	        '{%short-link%}' => '<a href="' . $ShortUrlGenerator->generateShortUrl($viewLink) . '">' . $ShortUrlGenerator->generateShortUrl($viewLink) . '</a>',
	        ];

        $client_place_holders=PlaceHolder::client_place_holder($invoice['Client']);
        foreach($client_place_holders as $key=>$ph){
            if(!in_array($key,array_keys($placeholders))){
                $placeholders[$key]=$ph;
            }
        }

        $placeholders = array_merge($placeholders, $staff_place_holders);
		
		
		if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];
			
		}
		
		
        if ($to_owner === true || $to_owner === 1)
            $to = strtolower($site['email']);
        else
            $to = strtolower($client['email']);
        $attachments = array();
        if (
            (!isset($template['EmailTemplate']['attach_invoice']) || $template['EmailTemplate']['attach_invoice'] || $force_attach) &&
            (method_exists($this->Controller, '_createInvoice') || method_exists($this->Controller, '_createDebitNote'))
        ) {
            $label = $invoice['type'] == Invoice::DEBIT_NOTE ? 'Debit-Note-' : 'Invoice'; 
            $attachments[$label . $invoice['no'] . '.pdf'] = $invoice['type'] == Invoice::DEBIT_NOTE ? call_user_func([$this->Controller, '_createDebitNote'], $invoice['id']) : call_user_func([$this->Controller, '_createInvoice'], $invoice['id']) ; //php8 fix
        }
        if (getCurrentSite('id') == 2109941) {
            $invoiceDir = '/tmp/' . SITE_HASH . '/';
            $attachments[$invoice['Invoice']['id'] . '_test.pdf'] = $invoiceDir . $invoice['Invoice']['id'] . '_test.pdf';
        }



        if (!isset($template['EmailTemplate']['attatch_associated_documents']) || !empty($template['EmailTemplate']['attatch_associated_documents'])) {
            if (!empty($attached_docs) && sizeof($attached_docs)) {
                $this->Controller->loadModel('Document');
                $document_ids = array();
                foreach ($attached_docs as $doc) {
                    $document_ids[] = $doc['document_id'];
                }
                $invoice_documents = $this->Controller->Document->find('all', array('conditions' => array('Document.id' => $document_ids)));

                foreach ($invoice_documents as $adoc) {
                    $attachments[] = WWW_ROOT . $adoc['Document']['file_full_path'];
                }
            }
 
           // Handle s3 documents .
            $attachments = $this->prepare_s3_attachment_for_send($attachments, $s3_attachments);
    
        }
        if (!empty($template['EmailTemplate']['attach_terms']) && !empty($invoice['required_terms'])) {

            $this->Controller->loadModel('Term');
            $term = $this->Controller->Term->findById($invoice['terms_id']);
            if ($term) {
                if ($term['Term']['type'] == 'file') {
                    $attachments[] = WWW_ROOT . $term['Term']['file_full_path'];
                } else {
                    $attachments[] = $this->Controller->Term->createTermFile($term);
                }
            }
        }
        if (!empty($template['EmailTemplate']['attachments']) && !empty($template['EmailTemplate']['attachments']['tmp_name'])) {
            move_uploaded_file($template['EmailTemplate']['attachments']['tmp_name'], $path = dirname($template['EmailTemplate']['attachments']['tmp_name']) . DS . $template['EmailTemplate']['attachments']['name']);
            $attachments[] = $path;
        }
 
         $eh = '-Invoice' . SITE_HASH;
        $new_attachments = [];
        foreach($attachments as $key=>$value) {
            $newname=basename($value);
            $newname=  str_replace($eh, '', $newname);
        if(is_numeric($key)) {
            $new_attachments[$newname] = $value;
        }else{
            $new_attachments[$key] = $value;
        }
        }

        $attachments = array_merge($new_attachments ?? [], $post_attachments ?? []);




        $more_emails = array_unique($more_emails);
        $more_emails = array_filter($more_emails, function($var) {
                    if (empty($var)) {
                        return false;
                    } else {
                        return true;
                    }
                });
        if (count($more_emails) > 0) {

            $result = $this->sendEmail(implode(",", $more_emails), $site, $template, $placeholders, $attachments, array('client_id' => $client['id'], 'invoice_id' => $invoice['id']));
            $to = implode(",", $more_emails);
        } else {
            $result = $this->sendEmail($to, $site, $template, $placeholders, $attachments, array('client_id' => $to_owner ? -1 : $client['id'], 'invoice_id' => $invoice['id']));
        }
        if ($result) {
            $sendTime = date('Y-m-d H:i:s');
            if (!$to_owner) {
                $this->Controller->Invoice->save(array('Invoice' => array('id' => $invoice['id'], 'last_sent' => $sendTime)), array('validate' => false, 'callbacks' => false, 'fieldList' => array('id', 'last_sent')));
                $arry = array('param2' => $to, 'param8' => $result, 'primary_id' => $invoice['id'], 'param4' => $invoice['no'], 'secondary_id' => $invoice['client_id']);
                if ($invoice['type'] == 0) {
                    $action_line = ACTION_SEND_INVOICE;
                } elseif ($invoice['type'] == 5) {
                    $action_line = ACTION_SEND_CREDITNOTE;
                } elseif ($invoice['type'] == 6) {
                    $action_line = ACTION_SEND_REFUND;
                } elseif ($invoice['type'] == 3) {
                    $action_line = ACTION_SEND_ESTIMATE;
                } else {
                    $action_line = ACTION_SEND_INVOICE;
                }

                $this->Controller->add_actionline($action_line, $arry);
            }
        }
        return $result;
    }

    public function booking($record, $receiver, $site, $template = false, $attached_docs = array(), $to_owner = false, $more_emails = array(), $post_attachments = []) {

        if (empty($site)) $site = getCurrentSite();

        $current_site = getAuthOwner();

        $view = new View($this->Controller, false);

        $placeholders = PlaceHolder::bookings_place_holder($record);

        if ($to_owner === true || $to_owner === 1)
            $to = strtolower($site['email']);
        else
            $to = strtolower($receiver['email']);
        $attachments = array();

        if (!isset($template['EmailTemplate']['attatch_associated_documents']) || !empty($template['EmailTemplate']['attatch_associated_documents'])) {
            if (sizeof($attached_docs)) {
                $this->Controller->loadModel('Document');
                $document_ids = array();
                foreach ($attached_docs as $doc) {
                    $document_ids[] = $doc['document_id'];
                }
                $invoice_documents = $this->Controller->Document->find('all', array('conditions' => array('Document.id' => $document_ids)));

                foreach ($invoice_documents as $adoc) {
                    $attachments[] = WWW_ROOT . $adoc['Document']['file_full_path'];
                }
            }
        }


        if (!empty($template['EmailTemplate']['attachments']) && !empty($template['EmailTemplate']['attachments']['tmp_name'])) {
            move_uploaded_file($template['EmailTemplate']['attachments']['tmp_name'], $path = dirname($template['EmailTemplate']['attachments']['tmp_name']) . DS . $template['EmailTemplate']['attachments']['name']);
            $attachments[] = $path;
        }
        $eh = '-Booking' . SITE_HASH;
        foreach($attachments as $key=>$value) {
            $newname=basename($value);
            $newname=  str_replace($eh, '', $newname);
            $new_attachments[$newname]=$value;
        }
        $attachments = array_merge($new_attachments ?? [], $post_attachments ?? []);



        $more_emails = array_unique($more_emails);
        $more_emails = array_filter($more_emails, function($var) {
            if (empty($var)) {
                return false;
            } else {
                return true;
            }
        });
        if (count($more_emails) > 0) {

            $result = $this->sendEmail(implode(",", $more_emails), $site, $template, $placeholders, $attachments, array('email_to' => $to, 'appointment' => $record['FollowUpReminder']['id']));
            $to = implode(",", $more_emails);
        } else {
            $result = $this->sendEmail($to, $site, $template, $placeholders, $attachments, array('email_to' => $to, 'appointment' => $record['FollowUpReminder']['id']));
        }
        return $result;
    }

    public function appointment($appointment, $receiver, $site, $template = false, $attached_docs = array(), $to_owner = false, $more_emails = array(), $post_attachments = []) {

        if (empty($site)) $site = getCurrentSite();

        $current_site = getAuthOwner();

        $view = new View($this->Controller, false);

        $placeholders = PlaceHolder::appointments_place_holder($appointment);

        if ($to_owner === true || $to_owner === 1)
            $to = strtolower($site['email']);
        else
            $to = strtolower($receiver['email']);
        $attachments = array();

        if (!isset($template['EmailTemplate']['attatch_associated_documents']) || !empty($template['EmailTemplate']['attatch_associated_documents'])) {
            if (sizeof($attached_docs)) {
                $this->Controller->loadModel('Document');
                $document_ids = array();
                foreach ($attached_docs as $doc) {
                    $document_ids[] = $doc['document_id'];
                }
                $invoice_documents = $this->Controller->Document->find('all', array('conditions' => array('Document.id' => $document_ids)));

                foreach ($invoice_documents as $adoc) {
                    $attachments[] = WWW_ROOT . $adoc['Document']['file_full_path'];
                }
            }
        }


        if (!empty($template['EmailTemplate']['attachments']) && !empty($template['EmailTemplate']['attachments']['tmp_name'])) {
            move_uploaded_file($template['EmailTemplate']['attachments']['tmp_name'], $path = dirname($template['EmailTemplate']['attachments']['tmp_name']) . DS . $template['EmailTemplate']['attachments']['name']);
            $attachments[] = $path;
        }
        $eh = '-Appointment' . SITE_HASH;
        $new_attachments = [];
        foreach($attachments as $key=>$value) {
            $newname=basename($value);
            $newname=  str_replace($eh, '', $newname);
            $new_attachments[$newname]=$value;
        }
        $attachments = array_merge($new_attachments, $post_attachments);




        $more_emails = array_unique($more_emails);
        $more_emails = array_filter($more_emails, function($var) {
            if (empty($var)) {
                return false;
            } else {
                return true;
            }
        });
        if (count($more_emails) > 0) {

            $result = $this->sendEmail(implode(",", $more_emails), $site, $template, $placeholders, $attachments, array('email_to' => $to, 'appointment' => $appointment['FollowUpReminder']['id']));
            $to = implode(",", $more_emails);
        } else {
            $result = $this->sendEmail($to, $site, $template, $placeholders, $attachments, array('email_to' => $to, 'appointment' => $appointment['FollowUpReminder']['id']));
        }
        return $result;
    }

    public function order($purchase_order, $supplier, $site, $template = false, $attached_docs = array(), $to_owner = false, $more_emails = array(),$s3_attachments = []) {
       
        $hash = $this->Controller->PurchaseOrder->getHash($purchase_order);

        $site['subdomain'] = getDomain();

        //purchase quotation - order- invoice (cale)
        //purchase_orders purchase_quotations
        
        $viewLinks = [
            PurchaseOrder::PURCHASE_ORDER     => 'https://' . $site['subdomain'] . '/purchase_orders/view/' . $purchase_order['id'] . '?hash=' . $hash,
            PurchaseOrder::PURCHASE_QUOTATION => 'https://' . $site['subdomain'] . '/purchase_quotations/view/' . $purchase_order['id'] . '?hash=' . $hash,
            PurchaseOrder::PURCHASE_INVOICE   => 'https://' . $site['subdomain'] . '/purchase_invoices/view/' . $purchase_order['id'] . '?hash=' . $hash,
            PurchaseOrder::Purchase_Refund   => 'https://' . $site['subdomain'] . '/purchase_invoices/view/' . $purchase_order['id'] . '/' . PurchaseOrder::Purchase_Refund . '?hash=' . $hash,
            PurchaseOrder::DEBIT_NOTE   => 'https://' . $site['subdomain'] . '/purchase_invoices/view/' . $purchase_order['id'] . '/' . PurchaseOrder::DEBIT_NOTE . '?hash=' . $hash,
            PurchaseOrder::CREDIT_NOTE   => 'https://' . $site['subdomain'] . '/purchase_invoices/view/' . $purchase_order['id'] . '/' . PurchaseOrder::CREDIT_NOTE . '?hash=' . $hash,
        ];

        $type = $purchase_order['type'];

        $viewLink = $viewLinks[$type] ;
 
        if ($to_owner) {
            //purchase invocie cake, purchase order, quotation laravel
            $viewLink = 'https://' . $site['subdomain'] . '/owner/purchase_invoices/view/' . $purchase_order['id'];
            $viewLinks = [
                PurchaseOrder::PURCHASE_ORDER     => 'https://' . $site['subdomain'] . '/v2/owner/entity/'.EntityKeyTypesUtil::PURCHASE_ORDER.'/' . $purchase_order['id'].'/show' . '?hash=' . $hash,
                PurchaseOrder::PURCHASE_QUOTATION => 'https://' . $site['subdomain'] . '/v2/owner/entity/'.EntityKeyTypesUtil::PURCHASE_QUOTATION.'/' . $purchase_order['id'] .'/show'. '?hash=' . $hash,
                PurchaseOrder::PURCHASE_INVOICE   => 'https://' . $site['subdomain'] . '/owner/purchase_invoices/view/' . $purchase_order['id'] . '?hash=' . $hash,
                PurchaseOrder::DEBIT_NOTE   => 'https://' . $site['subdomain'] . 'owner/purchase_invoices/view_debit_note/' . $purchase_order['id'] . '?hash=' . $hash,
                PurchaseOrder::CREDIT_NOTE   => 'https://' . $site['subdomain'] . 'owner/purchase_invoices/view_credit_note/' . $purchase_order['id'] . '?hash=' . $hash,
            ];
            
            $viewLink = $viewLinks[$type]; 
        } 

        $paymentLink = 'https://' . $site['subdomain'] . '/purchase_invoices/pay/' . $purchase_order['id'] . '?hash=' . $hash;

        $placeholders = PlaceHolder::purchaseorders_place_holder($purchase_order) + array(
            '{%payment-link%}' => '<a href="' . $paymentLink . '">' . $paymentLink . '</a>',
            '{%view-link%}' => '<a href="' . $viewLink . '">' . $viewLink . '</a>',
            '{%view-url%}' => $viewLink,
            '{%payment_link%}' => '<a href="' . $paymentLink . '">' . $paymentLink . '</a>',
            '{%view_link%}' => '<a href="' . $viewLink . '">' . $viewLink . '</a>',
        );

        if ($to_owner === true || $to_owner === 1)
            $to = strtolower($site['email']);
        else
            $to = strtolower($supplier['email']);
        $attachments = array();


        if ((!isset($template['EmailTemplate']['attach_purchase_order']) || $template['EmailTemplate']['attach_purchase_order']) && method_exists($this->Controller, '_createPurchaseOrder')) {

            $attachments['PurchaseOrder-'.$purchase_order['no'].'.pdf']  = call_user_func_array([$this->Controller, '_createPurchaseOrder'], [$purchase_order['id']]);
        }

        if (!empty($template['EmailTemplate']['attachments']) && !empty($template['EmailTemplate']['attachments']['tmp_name'])) {
            move_uploaded_file($template['EmailTemplate']['attachments']['tmp_name'], $path = dirname($template['EmailTemplate']['attachments']['tmp_name']) . DS . $template['EmailTemplate']['attachments']['name']);
            $attachments[] = $path;
        }

        if (!isset($template['EmailTemplate']['attatch_associated_documents']) || !empty($template['EmailTemplate']['attatch_associated_documents'])) {
            if (sizeof($attached_docs)) {
                $this->Controller->loadModel('Document');
                $document_ids = array();
                foreach ($attached_docs as $doc) {
                    $document_ids[] = $doc['document_id'];
                }
                $invoice_documents = $this->Controller->Document->find('all', array('conditions' => array('Document.id' => $document_ids)));

                foreach ($invoice_documents as $adoc) {
                    $attachments[] = WWW_ROOT . $adoc['Document']['file_full_path'];
                }
            }

            // Handle s3 documents .
            $attachments = $this->prepare_s3_attachment_for_send($attachments, $s3_attachments);
        }



        $more_emails = array_unique($more_emails);
        $more_emails = array_filter($more_emails, function($var) {
                    if (empty($var)) {
                        return false;
                    } else {
                        return true;
                    }
                });
        if (count($more_emails) > 0) {

            $result = $this->sendEmail(implode(",", $more_emails), $site, $template, $placeholders, $attachments, array('supplier_id' => $supplier['id'], 'purchase_order_id' => $purchase_order['id']));
            $to = implode(",", $more_emails);
        } else {
            $result = $this->sendEmail($to, $site, $template, $placeholders, $attachments, array('supplier_id' => $to_owner ? -1 : $supplier['id'], 'purchase_order_id' => $purchase_order['id']));
        }
        if ($result) {
            $sendTime = date('Y-m-d H:i:s');
            if (!$to_owner) {
                $this->Controller->PurchaseOrder->save(array('PurchaseOrder' => array('id' => $purchase_order['id'], 'last_sent' => $sendTime)), array('validate' => false, 'callbacks' => false, 'fieldList' => array('id', 'last_sent')));
                $arry = array('param2' => $to, 'param8' => $result, 'primary_id' => $purchase_order['id'], 'param4' => $purchase_order['no'], 'secondary_id' => $purchase_order['supplier_id']);
                $this->Controller->add_actionline(ACTION_SEND_PO, $arry);
            }
        }
        return $result;
    }

    public function estimate($estimate, $client, $site, $template = false, $attached_docs = array(), $more_emails = array(), $post_attachments = [],$s3_attachments = []) {
         
        $placeholders = PlaceHolder::estimate_place_holder($estimate);
		
		if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];
			
		}
		
        $staff_place_holders = PlaceHolder::staff_place_holder($estimate['staff_id']);
        $to = strtolower($client['email']);
        $attachments = array();
        if ((!isset($template['EmailTemplate']['attach_invoice']) || $template['EmailTemplate']['attach_invoice']) && method_exists($this->Controller, '_createInvoice')) {
            $attachments['Estimate-'.$estimate['no'].'.pdf'] = call_user_func([$this->Controller,'_createEstimate'], $estimate['id']); //php8 fix
        }
		
		if (!isset($template['EmailTemplate']['attatch_associated_documents']) || !empty($template['EmailTemplate']['attatch_associated_documents'])) {
            if (sizeof($attached_docs)) {
                $this->Controller->loadModel('Document');
                $document_ids = array();
                foreach ($attached_docs as $doc) {
                    $document_ids[] = $doc['document_id'];
                }
                $invoice_documents = $this->Controller->Document->find('all', array('conditions' => array('Document.id' => $document_ids)));

                foreach ($invoice_documents as $adoc) {
                    $attachments[] = WWW_ROOT . $adoc['Document']['file_full_path'];
                }
            }

            // Handle s3 documents .
            $attachments = $this->prepare_s3_attachment_for_send($attachments, $s3_attachments);
        }
		
        if (!empty($template['EmailTemplate']['attachments']) && !empty($template['EmailTemplate']['attachments']['tmp_name'])) {
            move_uploaded_file($template['EmailTemplate']['attachments']['tmp_name'], $path = dirname($template['EmailTemplate']['attachments']['tmp_name']) . DS . $template['EmailTemplate']['attachments']['name']);
            $attachments[] = $path;
        }
        $attachments = array_merge($attachments, $post_attachments);
    
        $more_emails = array_unique($more_emails);
        $more_emails = array_filter($more_emails, function($var) {
                    if (empty($var)) {
                        return false;
                    } else {
                        return true;
                    }
                });

        if (count($more_emails) > 0) {

            $result = $this->sendEmail(implode(",", $more_emails), $site, $template, array_merge($placeholders, $staff_place_holders), $attachments, array('client_id' => $client['id'], 'invoice_id' => $estimate['id']));
            $to = implode(",", $more_emails);
        } else {
            $result = $this->sendEmail($to, $site, $template, $placeholders, $attachments, array('client_id' => $client['id'], 'invoice_id' => $estimate['id']));
        }


        if ($result) {

            $arry = array('param2' => $to, 'param8' => $result, 'param4' => $estimate['no'], 'primary_id' => $estimate['id'], 'secondary_id' => $estimate['client_id']);
            $this->Controller->add_actionline(ACTION_SEND_ESTIMATE, $arry);
        }



        return $result;
    }
    public function salesOrder($salesOrder, $client, $site, $template = false, $attached_docs = array(), $more_emails = array(), $post_attachments = [],$s3_attachments = []) {

        $placeholders = PlaceHolder::sales_order_place_holder($salesOrder);
        if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];

		}

        $staff_place_holders = PlaceHolder::staff_place_holder($salesOrder['staff_id']);
        $to = strtolower($client['email']);
        $attachments = array();
        if ((!isset($template['EmailTemplate']['attach_invoice']) || $template['EmailTemplate']['attach_invoice']) && method_exists($this->Controller, '_createInvoice')) {
            $attachments['SalesOrder-'.$salesOrder['no'].'.pdf'] = call_user_func([$this->Controller,'_createSalesOrder'], $salesOrder['id']); //php8 fix
        }

		if (!isset($template['EmailTemplate']['attatch_associated_documents']) || !empty($template['EmailTemplate']['attatch_associated_documents'])) {
            if (sizeof($attached_docs)) {
                $this->Controller->loadModel('Document');
                $document_ids = array();
                foreach ($attached_docs as $doc) {
                    $document_ids[] = $doc['document_id'];
                }
                $invoice_documents = $this->Controller->Document->find('all', array('conditions' => array('Document.id' => $document_ids)));

                foreach ($invoice_documents as $adoc) {
                    $attachments[] = WWW_ROOT . $adoc['Document']['file_full_path'];
                }
            }

            // Handle s3 documents .
            $attachments = $this->prepare_s3_attachment_for_send($attachments, $s3_attachments);
        }

        if (!empty($template['EmailTemplate']['attachments']) && !empty($template['EmailTemplate']['attachments']['tmp_name'])) {
            move_uploaded_file($template['EmailTemplate']['attachments']['tmp_name'], $path = dirname($template['EmailTemplate']['attachments']['tmp_name']) . DS . $template['EmailTemplate']['attachments']['name']);
            $attachments[] = $path;
        }
        $attachments = array_merge($attachments, $post_attachments);

        $more_emails = array_unique($more_emails);
        $more_emails = array_filter($more_emails, function($var) {
                    if (empty($var)) {
                        return false;
                    } else {
                        return true;
                    }
                });

        if (count($more_emails) > 0) {

            $result = $this->sendEmail(implode(",", $more_emails), $site, $template, array_merge($placeholders, $staff_place_holders), $attachments, array('client_id' => $client['id'], 'invoice_id' => $salesOrder['id']));
            $to = implode(",", $more_emails);
        } else {
            $result = $this->sendEmail($to, $site, $template, $placeholders, $attachments, array('client_id' => $client['id'], 'invoice_id' => $salesOrder['id']));
        }


        if ($result) {

            $arry = array('param2' => $to, 'param8' => $result, 'param4' => $salesOrder['no'], 'primary_id' => $salesOrder['id'], 'secondary_id' => $salesOrder['client_id']);
            $this->Controller->add_actionline(ACTION_SEND_ESTIMATE, $arry);
        }



        return $result;
    }

    function estimateReply($client, $site, $estimate, $message, $sender = 'client') {

        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate('estimate-reply');



        if ($sender == 'client') {
            $to = $site['email'];
            $senderName = $client['business_name'];
            $receiverName = $site['business_name'];
            $url = Router::url('/owner/invoices/view_estimate/' . $estimate['id'], true);
        } else {
            $to = $client['email'];
            $senderName = $site['business_name'];
            $receiverName = $client['business_name'];
            $url = Router::url('/invoices/view_estimate/' . $estimate['id'] . '?hash=' . $this->Controller->Invoice->getHash($estimate), true);
        }

        $viewLink = "<a href=\"$url\">$url</a>";


        $placeholders = array(
            '{%sender-name%}' => h($senderName),
            '{%sender_name%}' => h($senderName),
            '{%receiver-name%}' => h($receiverName),
            '{%receiver_name%}' => h($receiverName),
            '{%estimate-no%}' => $estimate['no'],
            '{%estimate_no%}' => $estimate['no'],
            '{%message%}' => nl2br(h($message)),
            '{%view-link%}' => $viewLink,
            '{%view-url%}' => $url,
            '{%view_link%}' => $viewLink,
        );
		
		if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];
			
		}

        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $client['id'], 'invoice_id' => $estimate['id']));

        return $result;
    }

    public function estimateAccepted($estimate) {

        $site = getCurrentSite();

        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate('estimate_accepted');


        $view = new View($this->Controller, false);


        $placeholders = array(
            '{%client-business-name%}' => h($estimate['Client']['business_name']),
            '{%client-name%}' => trim($estimate['Client']['first_name'] . ' ' . h($estimate['Client']['last_name'])) != ''
                ? h($estimate['Client']['first_name'] . ' ' . h($estimate['Client']['last_name']))
                : h($estimate['Client']['business_name']),
            '{%client-address%}' => h($estimate['Client']['address1']) . ife($estimate['Client']['address2'], '<br />' . $estimate['Client']['address2']),
            '{%client-city%}' => h($estimate['Client']['city']),
            '{%client-state%}' => h($estimate['Client']['state']),
            '{%client-postal-code%}' => h($estimate['Client']['postal_code']),
            '{%full_client_address%}' => $view->element('format_address_html', $estimate['Client'] + array('is_inline' => true)),
            '{%full-client-address%}' => $view->element('format_address_html', $estimate['Client'] + array('is_inline' => true)),
            '{%client-country%}' => $estimate['Client']['country_code'],
            '{%estimate-no%}' => $estimate['Invoice']['no'],
            '{%estimate-total%}' => format_price($estimate["Invoice"]['summary_total'], $estimate['Invoice']['currency_code']),
            '{%discount%}' => intval($estimate['Invoice']['discount']),
            '{%view_link%}' => Router::url('/owner/invoices/view_estimate/' . $estimate['Invoice']['id'], true),
            '{%view-link%}' => Router::url('/owner/invoices/view_estimate/' . $estimate['Invoice']['id'], true),
            '{%view-url%}' => Router::url('/owner/invoices/view_estimate/' . $estimate['Invoice']['id'], true),
            '{%invoice_link%}' => Router::url('/owner/invoices/convert/' . $estimate['Invoice']['id'], true),
            '{%invoice-link%}' => Router::url('/owner/invoices/convert/' . $estimate['Invoice']['id'], true),
        );


        $to = $site['email'];

        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $estimate['Client']['id'], 'invoice_id' => $estimate['Invoice']['id']));

        return $result;
    }

    private function getPaymentStatusMessage($status) {
        return match ($status) {
            'payment-complete-client', 'payment-complete-owner' => __('Your payment has been completed', true),
            'payment-pending-client', 'payment-pending-owner' => __('Payment is pending', true),
            'payment-failed-client', 'payment-failed-owner' => __('Payment Failed', true),
            default => ''
        };
    }
    protected function _paymentClient($template, $payment, $invoice, $client, $site) {
		if (empty($site)) $site = getCurrentSite();
		
        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate($template);

        $site['subdomain'] = getDomain();

        $url =  'https://' . $site['subdomain'] . '/client/invoice_payments/view/' . $payment['id'];

        $placeholders = $this->_paymentPlaceholders($payment, $invoice, $site, $client);
        $placeholders['{%view-link%}'] = "<a href=\"$url\">$url</a>";
        $placeholders['{%view-url%}'] = $url;
        $placeholders['{%view_link%}'] = "<a href=\"$url\">$url</a>";
        $placeholders['{%status-message%}'] =  $this->getPaymentStatusMessage($template);
        $to = $client['email'];


        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $client['id'], 'invoice_id' => $invoice['id']));
        return $result;
    }

    // credit payment
    protected function _creditpaymentClient($template, $payment, $client, $site) {
		if (empty($site)) $site = getCurrentSite();
        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate($template);

        $site['subdomain'] = getDomain();

        $url = 'https://' . $site['subdomain'] . '/client/invoice_payments/view/' . $payment['id'];

        $placeholders = $this->_paymentPlaceholders($payment, array(), $site, $client);
        $placeholders['{%view-link%}'] = "<a href=\"$url\">$url</a>";
        $placeholders['{%view-url%}'] = $url;
        $placeholders['{%view_link%}'] = "<a href=\"$url\">$url</a>";
        $to = $client['email'];


        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $client['id']));
        return $result;
    }

    protected function _creditpaymentOwner($template, $payment, $client, $site) {

        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate($template);



        $view_url = Router::url('/owner/invoice_payments/view/' . $payment['id'], true);
        $edit_url = Router::url('/owner/invoice_payments/edit/' . $payment['id'], true);

        $placeholders = $this->_paymentPlaceholders($payment, array(), $site, $client);
        $placeholders['{%view-link%}'] = "<a href=\"$view_url\">$view_url</a>";
        $placeholders['{%view-url%}'] = $view_url;
        $placeholders['{%view_link%}'] = "<a href=\"$view_url\">$view_url</a>";
        $placeholders['{%edit-link%}'] = "<a href=\"$edit_url\">$edit_url</a>";
        $placeholders['{%edit-url%}'] = $edit_url;
        $placeholders['{%edit_link%}'] = "<a href=\"$edit_url\">$edit_url</a>";
        $placeholders['{%status-message%}'] =  $this->getPaymentStatusMessage($template);
        $to = $site['email'];


        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $client['id']));
        return $result;
    }

    function creditpaymentCompleteClient($payment, $client, $site) {
        return $this->_creditpaymentClient('credit-payment-complete-client', $payment, $client, $site);
    }

    function creditpaymentCompleteOwner($payment, $client, $site) {
        return $this->_creditpaymentOwner('credit-payment-complete-owner', $payment, $client, $site);
    }

    // end of credit payment emails
    function paymentCompleteClient($payment, $invoice, $client, $site) {
        return $this->_paymentClient('payment-complete-client', $payment, $invoice, $client, $site);
    }

    function paymentPendingClient($payment, $invoice, $client, $site) {
        return $this->_paymentClient('payment-pending-client', $payment, $invoice, $client, $site);
    }

    function paymentFailedClient($payment, $invoice, $client, $site) {
        return $this->_paymentClient('payment-failed-client', $payment, $invoice, $client, $site);
    }

    protected function _paymentOwner($template, $payment, $invoice, $client, $site) {
		if (empty($site)) $site = getCurrentSite();
		

        $this->Controller->loadModel('EmailTemplate');
        $defaultTemplate = $this->Controller->EmailTemplate->getDefaultTemplate($template);

        $site['subdomain'] = getDomain();
        $view_url = 'https://' . $site['subdomain'] . '/owner/invoice_payments/view/' . $payment['id'];
        $edit_url = 'https://' . $site['subdomain'] . '/owner/invoice_payments/edit/' . $payment['id'];

        $placeholders = $this->_paymentPlaceholders($payment, $invoice, $site, $client);
        $placeholders['{%view-link%}'] = "<a href=\"$view_url\">$view_url</a>";
        $placeholders['{%view-url%}'] = $view_url;
        $placeholders['{%view_link%}'] = "<a href=\"$view_url\">$view_url</a>";
        $placeholders['{%edit-link%}'] = "<a href=\"$edit_url\">$edit_url</a>";
        $placeholders['{%edit-url%}'] = $edit_url;
        $placeholders['{%edit_link%}'] = "<a href=\"$edit_url\">$edit_url</a>";
        $placeholders['{%status-message%}'] =  $this->getPaymentStatusMessage($template);
        $to = $site['email'];

        $result = $this->sendEmail($to, $site, $defaultTemplate, $placeholders, array(), array('client_id' => $client['id'], 'invoice_id' => $invoice['id']));
        return $result;
    }

    function paymentCompleteOwner($payment, $invoice, $client, $site) {
        return $this->_paymentOwner('payment-complete-owner', $payment, $invoice, $client, $site);
    }

    function paymentPendingOwner($payment, $invoice, $client, $site) {
        return $this->_paymentOwner('payment-pending-owner', $payment, $invoice, $client, $site);
    }

    function paymentFailedOwner($payment, $invoice, $client, $site) {
        return $this->_paymentOwner('payment-failed-owner', $payment, $invoice, $client, $site);
    }

    protected function _paymentPlaceholders($payment, $invoice, $site, $client) {
        if(empty($client)){
            $client=array();
        }
        debug(func_get_args());
        if (empty($site))
            $site = getCurrentSite();
        $dateFormats = getDateFormats('std');
        $dateFormat = $dateFormats[$site['date_format']];

        $view = new View($this->Controller, false);

        $placeholders = array(
            '{%business-name%}' => h($site['business_name']),
            '{%business_name%}' => h($site['business_name']),
            '{%address1%}' => h($site['address1']),
            '{%address2%}' => h($site['address2']),
            '{%city%}' => h($site['city']),
            '{%state%}' => h($site['state']),
            '{%postal-code%}' => h($site['postal_code']),
            '{%full-business-address%}' => $view->element('format_address_html', $site + array('is_inline' => true)),
            '{%full_business_address%}' => $view->element('format_address_html', $site + array('is_inline' => true)),
            '{%telephone%}' => h($site['phone1']),
            '{%mobile%}' => h($site['phone2']),
            '{%client-business-name%}' => h($client['business_name']),
            '{%client_business-name%}' => h($client['business_name']),
            '{%full_client_address%}' => $view->element('format_address_html', $client + array('is_inline' => true)),
            '{%full-client-address%}' => $view->element('format_address_html', $client + array('is_inline' => true)),
            '{%client_first_name%}' => $client['first_name'],
            '{%client-first-name%}' => $client['first_name'],
            '{%client_first-name%}' => $client['first_name'],
            '{%client_last_name%}' => $client['last_name'],
            '{%client-last-name%}' => $client['last_name'],
            '{%client_last-name%}' => $client['last_name'],
            '{%client-address%}' => h($client['address1']) . ife($client['address2'], '<br />' . $client['address2']),
            '{%client-city%}' => h($client['city']),
            '{%client-state%}' => h($client['state']),
            '{%client-postal-code%}' => h($client['postal_code']),
            '{%client-email%}' => $client['email'],
            '{%client_email%}' => $client['email'],
            '{%client-name%}' => trim($client['first_name'] . ' ' . h($client['last_name'])) != ''
                ? h($client['first_name'] . ' ' . h($client['last_name']))
                : h($client['business_name']),
            '{%payment-no%}' => h($payment['id']),
            '{%transacion-id%}' => ife($payment['transaction_id'], h($payment['transaction_id']), __('N/A', true)),
            '{%transaction-id%}' => ife($payment['transaction_id'], h($payment['transaction_id']), __('N/A', true)),
            '{%payment-amount%}' => format_price($payment['amount'], $payment['currency_code']),
            '{%invoice-no%}' => $invoice['no'],
            '{%payment-date%}' => format_date(($payment['date'])),
            '{%payment-method%}' => h($payment['payment_method']),
            '{%payment-message%}' => isset($payment['response_message']) ? h($payment['response_message']) : '',
            '{%payment-currency%}' => $payment['currency_code'],
            '{%invoice-paid%}' => format_price($invoice['summary_paid'], $invoice['currency_code']),
            '{%invoice-unpaid%}' => format_price($invoice['summary_unpaid'], $invoice['currency_code']),
                ) + PlaceHolder::staff_place_holder($invoice['staff_id']);
        foreach ($placeholders as $key => $value) {
            $placeholders[str_replace('-', '_', $key)] = $value;
        }
		
		if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];
			
		}
        return $placeholders;
    }

    function clientStatemnet($client) {
        
    }

    function check_empty($var) {

        if (empty($var)) {
            return false;
        } else {
            return true;
        }
    }

    protected function _getFrom($name, $email) {
        return '' . str_replace('"', '', $name) . ' <' . strtolower($email) . '>';
    }

    function fix_arabic_names($emails) {
        foreach (explode(',', $emails) as $to) {
            $in = $to;
            preg_match_all('!(.*?)\s+<\s*(.*?)\s*>!', $in, $matches);
            $out = array();
            for ($i = 0; $i < count($matches[0]); $i++) {
                $out[] = array(
                    'name' => $matches[1][$i],
                    'email' => $matches[2][$i],
                );
            }
            if (!empty($out)) {
                foreach ($out as $outemails) {
                    $email_list[] = mb_encode_mimeheader($outemails['name'], 'UTF-8', 'B','') . " <" . $outemails['email'] . ">";
                }
            } else {
                $email_list[] = $in;
            }
        }
        return $email_list;
    }
    public function work_order($work_order, $client, $site, $template = false, $more_emails = [] ) {


//        $hash = $this->Controller->Invoice->getHash($work_order);



//        $viewLink = 'https://' . $site['subdomain'] . '/work_orders/view/' . $work_order['id'] . '?hash=' . $hash;
        
//        $paymentLink = 'https://' . $site['subdomain'] . '/invoices/pay/' . $work_order['id'] . '?hash=' . $hash;


        $staff_place_holders = PlaceHolder::staff_place_holder($work_order['staff_id']);


        $view = new View($this->Controller, false);

        $placeholders = PlaceHolder::work_order_place_holder($work_order) ;
        
        $placeholders = array_merge($placeholders, $staff_place_holders);
		
		if(empty($placeholders['{%client-first-name%}'])&&empty($placeholders['{%client_first_name%}'])&&$client['type']==2)
		{
			$full_name=explode(' ',trim(preg_replace('!\s+!', ' ',$client['business_name'])));
			$placeholders['{%client_first_name%}']=$placeholders['{%client-first-name%}']=$full_name[0];
			$placeholders['{%client_last_name%}']=$placeholders['{%client-last-name%}']=$full_name[1];
			
		}
		
        if ($to_owner === true || $to_owner === 1)
            $to = strtolower($site['email']);
        else
            $to = strtolower($client['email']);
//        if ((!isset($template['EmailTemplate']['attach_invoice']) || $template['EmailTemplate']['attach_invoice']) && method_exists($this->Controller, '_createInvoice')) {
         if(is_callable([$this->Controller,'_createWO'])){
            $attachments = array();
            $attachments[] = call_user_func([ $this->Controller,'_createWO'], $work_order['id']); //php8 fix
         }

//        }

//        if (!isset($template['EmailTemplate']['attatch_associated_documents']) || !empty($template['EmailTemplate']['attatch_associated_documents'])) {
//            if (sizeof($attached_docs)) {
//                $this->Controller->loadModel('Document');
//                $document_ids = array();
//                foreach ($attached_docs as $doc) {
//                    $document_ids[] = $doc['document_id'];
//                }
//                $invoice_documents = $this->Controller->Document->find('all', array('conditions' => array('Document.id' => $document_ids)));
//
//                foreach ($invoice_documents as $adoc) {
//                    $attachments[] = WWW_ROOT . $adoc['Document']['file_full_path'];
//                }
//            }
//        }

//        if (!empty($template['EmailTemplate']['attach_terms']) && !empty($work_order['required_terms'])) {
//
//            $this->Controller->loadModel('Term');
//            $term = $this->Controller->Term->findById($work_order['terms_id']);
//            if ($term) {
//                if ($term['Term']['type'] == 'file') {
//                    $attachments[] = WWW_ROOT . $term['Term']['file_full_path'];
//                } else {
//                    $attachments[] = $this->Controller->Term->createTermFile($term);
//                }
//            }
//        }


//        if (!empty($template['EmailTemplate']['attachments']) && !empty($template['EmailTemplate']['attachments']['tmp_name'])) {
//            move_uploaded_file($template['EmailTemplate']['attachments']['tmp_name'], $path = dirname($template['EmailTemplate']['attachments']['tmp_name']) . DS . $template['EmailTemplate']['attachments']['name']);
//            $attachments[] = $path;
//        }
//         $eh = '-Invoice' . SITE_HASH;
debug (  $attachments ) ;
        foreach($attachments as $key=>$value) {
            $newname=basename($value);
            $newname=  str_replace($eh, '', $newname);
        $new_attachments[$newname]=$value;    
        }
//        $attachments = array_merge($new_attachments, $post_attachments);
       
      
        debug ( $placeholders ) ;

        $more_emails = array_unique($more_emails);
        $more_emails = array_filter($more_emails, function($var) {
                    if (empty($var)) {
                        return false;
                    } else {
                        return true;
                    }
                });
        //$attachments = []  ; 
        if (count($more_emails) > 0) {

            $result = $this->sendEmail(implode(",", $more_emails), $site, $template, $placeholders, $attachments, array('client_id' => $client['id'], 'invoice_id' => $work_order['id']));
            $to = implode(",", $more_emails);
        } else {
            $result = $this->sendEmail($to, $site, $template, $placeholders, $attachments, array('client_id' => $to_owner ? -1 : $client['id'], 'invoice_id' => $work_order['id']));
        }
//        if ($result) {
//            $sendTime = date('Y-m-d H:i:s');
//            if (!$to_owner) {
//                $this->Controller->Invoice->save(array('Invoice' => array('id' => $work_order['id'], 'last_sent' => $sendTime)), array('validate' => false, 'callbacks' => false, 'fieldList' => array('id', 'last_sent')));
//                $arry = array('param2' => $to, 'param8' => $result, 'primary_id' => $work_order['id'], 'param4' => $work_order['no'], 'secondary_id' => $work_order['client_id']);
//                if ($work_order['type'] == 0) {
//                    $action_line = ACTION_SEND_INVOICE;
//                } elseif ($work_order['type'] == 5) {
//                    $action_line = ACTION_SEND_CREDITNOTE;
//                } elseif ($work_order['type'] == 6) {
//                    $action_line = ACTION_SEND_REFUND;
//                } else {
//                    $action_line = ACTION_SEND_INVOICE;
//                }
//
//                $this->Controller->add_actionline($action_line, $arry);
//            }
//        }
        return $result;
    }


    /**
     * Helper method to send notification emails based on the $email value
     *
     * @param string $email
     * @param array $invoicePayment
     * @param array $invoice
     * @param array $client
     * @param array $site
     *
     * @return void
     */
    public function paymentNotificationHelper(
        $email,
        $invoicePayment,
        $invoice,
        $client,
        $site
    ) {

        $action = 'Failed';

        if ($email == 'complete' || $email == 'pending') {
            $action = ucfirst($email);
        }

        $clientMethod = sprintf("payment%sClient", $action);
        $ownerMethod = sprintf("payment%sOwner", $action);
        $this->$clientMethod(
            $invoicePayment,
            $invoice,
            $client,
            $site
        );

        $this->$ownerMethod(
            $invoicePayment,
            $invoice,
            $client,
            $site
        );
    }

    private function prepare_s3_attachment_for_send($attachments, $s3_attachments){
        foreach($s3_attachments as $doc)
        {
            $path = "/tmp/".SITE_HASH."/s3/";
            $fullPath = $path.strtolower(str_replace(" ", "_", $doc['name']));
            if (!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            file_put_contents($fullPath, file_get_contents(Aws::getPermanentUrl($doc['path'])));
            $attachments['s3_'.$doc['name']] = $fullPath;
        }
        return $attachments;
    }
}
