<?php

use App\Helpers\UsersHelper;

App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));

/**
 * @property EmailComponent $Email
 */
class ItemPermissionsComponent  {

    /**
     * @var AppController
     */
    var $Controller; 

    /**
     * @var SystemEmail
     */
    var $ItemPermission;

    public function setData($item_type = null , $item_id = null )
    {
        $this->Staff = GetObjectOrLoadModel('Staff');
        $this->Role = GetObjectOrLoadModel('Role');
        $this->Controller->set('staffs' , UsersHelper::getInstance()->getList());
        $this->Controller->set('roles' ,  $this->Role->find('list', ['conditions'=> ['Role.id !='=>'-1']]));
        $this->Controller->set('permission_levels' , ItemPermission::$permissions[$item_type] ) ;
        $this->Controller->set('groups' , $this->ItemPermission->getItemTypeGroups($item_type) ) ;
        if ( !empty($item_id) && !empty($item_type))
        {
            $ungrouped_permissions = $this->ItemPermission->find('all' , ['conditions' => ['item_id' => $item_id , 'item_type' => $item_type] ]) ; 
            $grouped_permissions = [] ; 
            foreach ( $ungrouped_permissions as $v )
            {
                $grouped_permissions[$v['ItemPermission']['permission_level']]['group_type'] = $v['ItemPermission']['group_type'];
                $grouped_permissions[$v['ItemPermission']['permission_level']]['group_id'][$v['ItemPermission']['group_type']][] = $v['ItemPermission']['group_id'];
            }
            $this->Controller->set('item_permissions' , $grouped_permissions );
        }
        
    }

    public function startup(&$controller) {
        $this->Controller = $controller;
        $this->ItemPermission = GetObjectOrLoadModel('ItemPermission');
    }
    public function initialize(&$controller, $settings=array())
    {
        
    }
    public function shutdown(&$controller){
        
    }


}
