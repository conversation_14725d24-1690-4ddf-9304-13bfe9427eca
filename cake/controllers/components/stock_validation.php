<?php

use App\Validators\StockTransaction\ProductBalanceValidator;

class StockValidationComponent {

	private $controller;

	function initialize(&$controller, $settings = []){
		$this->controller =& $controller;
	}


	/**
	 * @param $controller - just pass the controller
	 * @param $item_type - [requisition, invoice, purchase_order]
	 * @param $newData - New Data Array ($this->data)
	 * @param $mode - ['deduct','add']
	 * @param array $oldData - Old Data $this->find($id) for your entity
	 * @param bool $redirectToReferer - should redirect to referer (incase of delete) or just return true/false
	 */
	public function validateQuantityAvailable($item_type, $newData, $mode, $oldData = [], $redirectToReferer = false)
	{
		/*
		 *  Why do we have 3 different ways implemented to prevent the action/flow incase there is no stock of a specific product
		 *  1 - Modifying $store_balance to false and checking it with if/else
		 *  2 - Using If (empty(validationErrors)) (hmmm) ?
		 *  3 - Manually Forcing a Redirect to referer incase of owner_delete
		 *  (This is stupid and Needs to be changed to be only one way to prevent the action)
		 *  (And Yes I need the controller for redirect/referer/flashMessage since it didn't work with CustomFlashMessage)
		 */
		$validator = new ProductBalanceValidator($item_type);
		$modelName = ProductBalanceValidator::$itemTypes[$item_type]['model'];
		$itemModel = ProductBalanceValidator::$itemTypes[$item_type]['itemModel'];
		$modelObject = GetObjectOrLoadModel($modelName);
		$resultIndeces = $validator->validate($newData, $mode, $oldData);
		$productModel = GetObjectOrLoadModel('Product');
        $stockTransactionModel = GetObjectOrLoadModel('StockTransaction');
        if ($resultIndeces !== true) {
			$errorMessages = [];
			foreach($resultIndeces as $resultIndex){
				$productIndex = $resultIndex;
				$productId = $newData[$itemModel][$productIndex]['product_id'];
				$store_id = $newData[$itemModel][$productIndex]['store_id'] ?: $newData[$modelName]['store_id'];
				$stockBalance = $stockTransactionModel->getProductStockBalance($productId, $store_id);
				$balance = sprintf(__('Available is %s', true), $stockBalance);
				$msg = __('Amount not sufficient for %s', true);
				$product = $productModel->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $productId]]);
				$name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
				$errorMessage = sprintf($msg, $name_code . " " . $balance);
				$errorMessages[] = $errorMessage;
				$modelObject->validationErrors[$itemModel][$productIndex]['quantity'] = ['quantity' => $errorMessage];
			}
			if (IS_REST) {
                $this->controller->cakeError("error400", ["message" => $errorMessages]);
            }
            $session = new \CakeSession;
            $session->write('Message.CustomError', ['message' => [$errorMessages]]);
	
			if (empty($this->controller)) {
				return false;
			}
			if($this->controller->RequestHandler->isAjax()){
				die(json_encode(['error'=>true, 'message' => implode(', ', $errorMessages)]));
			}


			$messages[] = $errorMessages;
            $validatedBundles = $session->read('Message.CustomError');
			if(!empty($validatedBundles)){
				$messages[] = array_shift($validatedBundles['message']);
			}
		 
			$messages = array_merge(...$messages);
 
			$session->write('Message.CustomError', ['message' => [$messages]]);
            if($redirectToReferer){
				$this->controller->redirect($this->controller->referer());
			}
			return false;
		}
		return true;
	}

}
