<?php

class SupplierValidationComponent
{
	private $controller;

	function initialize(&$controller, $settings = [])
	{
		$this->controller =& $controller;
	}

	public function validateSupplierSuspended($supplier_id,$return_message=false)
	{
		if (empty($supplier_id)) return;
        $SupplierModel = GetObjectOrLoadModel('Supplier');
		$row = $SupplierModel->find('first', ['recursive' => -1, 'conditions' => ['Supplier.id' => $supplier_id]]);
		if ($row && $row['Supplier']['suspend'] == "1") {
            $message=__t('You cannot create a new transaction for a suspended supplier');
			
			if ($return_message) {
				return ['error'=>true ,'message'=>$message];
			}

			if (IS_REST) {
				$this->controller->cakeError("error400", ["message" => $message]);
			}
			$this->controller->flashMessage($message);
			$this->controller->redirect(['action' => 'index']);
		}
	}
}
