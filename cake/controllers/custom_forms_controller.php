<?php

use App\Services\CustomFormService;
use Izam\Daftra\Common\EntityStructure\CacheDriverInterface;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Entity\Helper\EntityHasLegacyCustomFields;

class CustomFormsController extends AppController {
    
    var $uses = array('Currency');
    var $helpers = array('fck',"form"); /** load fck for using ckeditor and load form helper **/
    var $name = 'CustomForms';
    var $controller = 'CustomForms';
    var $multi_title = 'Create your Form';
    var $single_title = 'Create your Form';

    /** $fields_type
     * have each field code
    **/



    var $fields_type = array(
        "10" => "singleline",
        "20" => "multiline",
        "30" => "number",
        "40" => "decimal",
        "60" => "email",
        "70" => "date",
        "80" => "time",
        "90" => "datetime", 
        "100" => "dropdown", 
        "105" => "radio", 
        "110" => "multiple_dropdown", 
        "120" => "checkbox", 
        "130" => "multiple_checkbox", 
        "140" => "website", 
        "150" => "currency", 
        "160" => "file_upload", 
        "165" => "image_upload", 
        "170" => "automatic_serial_number",
        "180" => "separator",
        "190" => "free_static_content",
        "200" => "map",
        "210" => "rating_input",
        "220" => "belongs_to",
        "230" => "has_many",
    );

    /** $mysql_codes
     * have each field code with its mysql type
     **/
    var $mysql_codes = array(
        "10" => "Text",
        "20" => "Text",
        "30" => "bigint",
        "40" => "double",
        "60" => "Text",
        "70" => "date",
        "80" => "time",
        "90" => "datetime",
        "100" => "Text",
        "105" => "Text",
        "110" => "Text",
        "120" => "boolean",
        "130" => "Text",
        "140" => "Text",
        "150" => "Text",
        "160" => "Text",
        "165" => "Text",
        "170" => "bigint",
        "200" => "Text",
        "210" => "Int (11)",
        "220" => "Int (11)",
        "230" => "boolean",
    );
    var $more_js_labels = array(
        'Are you sure ?',
    );
    /** beforeFilter()
     * set page title and load models (CustomForm - CustomFormField)
     * set additional variables for controller name , page title in (single - multi) line mode
     **/
    function beforeFilter() {

 $this->js_lang_labels = array_merge($this->js_lang_labels, $this->more_js_labels);
 
        parent::beforeFilter();
        $this->titleAlias = __('Create your Form', true);


        $this->loadModel('CustomTable');
        $this->loadModel("CustomForm");
        $this->loadModel("CustomFormField");
        
        $this->set('controller', $this->controller);
        $this->set('multi_title', $this->multi_title);
        $this->set('single_title', $this->single_title);
    }

    /** owner_index()
     * show view that you can :-
     * list of created forms
     * add new form
     * edit exist form data
     * edit exist form fields
     * view and manage rows to this form table
     * soft-delete (that you can cancel update after deleting) to forms
    **/
    public function owner_index()
    {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        //removed to be used inside clients/products only .
        $client = getAuthOwner();

        $this->CustomForm->recursive = -1;

        debug("index here will show table list");
        $client_forms = $this->CustomForm->find("all",
            array(
                "conditions" => array(
//                    "CustomForm.client_id" => $client["id"],
//                    "CustomForm.is_deleted" => 0
                )
            )
        );
//		dd($client_forms);
//        debug($client_forms);
        $this->set("client_forms",$client_forms);
        $this->set('title_for_layout',  h(__($this->single_title, true)));
    }

    /** owner_save($id = null)
     * this function can :-
     * add new form in table >> custom_forms and create table >> (custom_table_$form_id_created)
     * edit exist form
     * then redirect to owner_custom_fields($id) with form_id
     **/
    public function owner_save($id = null)
    {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        //removed to be used inside clients/products only .
        $client = getAuthOwner();
        if ($id != null) {
            $form_data = $this->CustomForm->read(null,$id);

//            if($form_data["CustomForm"]["client_id"] != $client["id"])
//            {
//                $this->flashMessage(__("Form Not Found!!", true));
//                $this->redirect(array('controller' => $this->controller, 'action' => 'save'));
//            }

            if($form_data["CustomForm"]["is_deleted"] == 1)
            {
                $this->flashMessage(__("Form Is Deleted!!", true));
                $this->redirect(array('controller' => $this->controller, 'action' => 'save'));
            }
            $this->set("form_data",$form_data);
        }
        //debug($form_data);

        if (!empty($this->data)) {
//            $this->data["CustomForm"]["client_id"] = $client["id"];
            $this->data["CustomForm"]["last_submitted"] = date('y-m-d h:i:s');
            debug($this->data);
			 
            $last_inserted_id = 0;
            if ($id == null) {
                $this->CustomForm->create();

                if($this->CustomForm->save($this->data))
                {


                    CustomFormService::createCustomDataTable($this->data["CustomForm"]["table_name"]);
                    $last_inserted_id = $this->CustomForm->getLastInsertId();
                    $this->redirect(array('controller' => $this->controller, 'action' => 'custom_fields',$last_inserted_id));

                }
                debug($this->CustomForm->validationErrors);
            }
            else{

                $this->CustomForm->id = $form_data["CustomForm"]["id"];
                if($this->CustomForm->save($this->data))
                {
                    $last_inserted_id = $form_data["CustomForm"]["id"];

                  //$this->flashMessage(__("Form Is Saved Successfully", true), 'Sucmessage');
                    
                    
                    $this->redirect(array('controller' => $this->controller, 'action' => 'custom_fields',$last_inserted_id));
                }

            }
            
        }
        
    }


    function owner_test()
    {

//        $this->CustomFormField->update_user_form_table(35,160,$this->mysql_codes[30], true , 'clients' );
    }

    /** owner_custom_fields($id)
     * this function can :-
     * list all created fields to make edit
     * select form sidebar menu items
     * receive new items from ajax request that data on format of array
     * add and edit fields in table custom_form_fields
     * alter existing table (custom_table_$form_id_created) from  owner_save($id) to add new fields
     * each custom field its name is >> field_$field_id , $field_id obtained from custom_form_fields id attribute
     **/
    function owner_custom_fields($id = null)
    {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        $this->loadModel("CustomFormField");
        $client = getAuthOwner();
        $currencies = $this->Currency->getCurrencyList(array(), false);
        $this->set('currencies', $currencies);
        $this->set('geocode_api',$this->config['txt.google_geocode_api']);
        $this->set('google_maps_api',$this->config['txt.google_maps_api']);
        // check if id is passed
        if ($id == null) {
            $this->flashMessage(__("Form Not Found!!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
        
        // check if id is own to this user
        $results = $this->CustomForm->read(null,$id);
//        if ($results["CustomForm"]["client_id"] != $client["id"]) {
//            $this->flashMessage(__("Form Not Found!!", true));
//            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
//        }
        $this->set('referer', $this->referer());
        $this->set("form_id",$id);
        $this->set('fieldFilterTypes', CustomForm::getCustomModelFieldFilterType());
        //debug($id);
        
        // check if user table is set 
		$table_name = $results["CustomForm"]['table_name'];
		if ( trim ($table_name ) == "" )
		{
			$db_table_name = "custom_table_".$id ; 
			$results_count = $this->CustomForm->check_if_table_exist("custom_table_".$id);
		}else 
		{
			$db_table_name = $this->CustomForm->get_custom_table_database_name($table_name);
			$results_count = $this->CustomForm->check_if_table_exist($this->CustomForm->get_custom_table_database_name($table_name));
		}			
        
        //debug($results_count);
        if (count($results_count) == 0) {
            $this->flashMessage(__("Table Not Found, Create it at first", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'save'));
        }
        
        // get all user fields
        $this->CustomFormField->recursive = -1;
        $all_user_fields = $this->CustomFormField->find("all",array(
            "conditions" => array(
                "CustomFormField.custom_form_id" => $id,
                "CustomFormField.is_deleted" => 0
            ),
            "order" => array("CustomFormField.display_order asc")
        ));

        $user_fields_values = array();
        foreach ($all_user_fields as $key => $value) 
        {
            $value["CustomFormField"]["type_options"] = json_decode($value["CustomFormField"]["type_options"]);
            $value["CustomFormField"]["validation_options"] = json_decode($value["CustomFormField"]["validation_options"]);
            if(isset($value["CustomFormField"]["validation_options"]->min_date) && format_date($value["CustomFormField"]["validation_options"]->min_date)) {
                $value["CustomFormField"]["validation_options"]->min_date = format_date($value["CustomFormField"]["validation_options"]->min_date);
            }
            if(isset($value["CustomFormField"]["validation_options"]->max_date) && format_date($value["CustomFormField"]["validation_options"]->max_date)) {
                $value["CustomFormField"]["validation_options"]->max_date = format_date($value["CustomFormField"]["validation_options"]->max_date);
            }
            $value["CustomFormField"]["layout_options"] = json_decode($value["CustomFormField"]["layout_options"]);
            $field_type = $this->fields_type[$value["CustomFormField"]["type"]];
            $user_fields_values[$field_type.'_'.$key] = $value["CustomFormField"];
        }
        //debug($user_fields_values);
        $this->set("all_user_fields",$user_fields_values);
        
        $default_user_table_columns = array();
        $custom_user_table_columns = array();
        $user_table = $this->CustomFormField->get_user_table_cols($id,$table_name);
        
        foreach ($user_table as $key => $value) 
        {
            foreach ($value as $key2 => $value2) 
            {
                if ($value2["Field"] == "id" || $value2["Field"] == "created" || $value2["Field"] == "modified") {
                    $default_user_table_columns[] = $value2["Field"];
                }
                else{
                    $temp = str_replace("field_", "", $value2["Field"]);
                    $custom_user_table_columns[$temp] = $value2["Field"];
                }
                
            }
        }
        $mysql_fields = array();
        if ($this->RequestHandler->isAjax()) {
            $has_error = CustomFormService::saveFields($_POST['custom_form_name'], $_POST["data"], $results, $db_table_name);
                        if ( $has_error ) {
                          if (is_string($has_error)) {
                              $this->flashMessage($has_error);
                          } else {
                              $this->flashMessage(__("An Error occurred", true));
                          }
                        }else {
                            $this->flashMessage(__("Your settings have been updated", true), 'Sucmessage');
                        }


            die();



        }
        $customFormsCount = $this->CustomForm->find('count', ['conditions' => ['CustomForm.is_deleted' => 0]]);
		$this->set('customFormsCount', $customFormsCount);
        $this->set( 'table_names' , $this->CustomForm->get_belongs_to_tables () ) ;
		$this->set('native_custom_tables', $this->CustomForm->get_native_custom_tables($id));
        $this->set ( 'form_title' , $results['CustomForm']['title']);
        $this->set ('table_name' , $table_name) ;
        $results_count = $this->CustomForm->get_user_table_data(" count(*) as results_count ",$id ,"" , $db_table_name);
        $this->set('results_count',$results_count[0][0]['results_count']);
        $this->set('title_for_layout',  h(__($this->single_title, true)));

        $cacheDriver = resolve(CacheDriverInterface::class);
        $cacheDriver->delete(EntityHasLegacyCustomFields::getSiteCacheKey($table_name));
    }
	
    /** owner_delete_form()
     * this function can :-
     * soft-delete to form that alter its row field (is_deleted) to be 0 in table >> custom_forms
     **/
    function owner_delete_form()
    {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        $client = getAuthOwner();
        $output = array();
        $output["success"] = false;
        $output["msg"] = "";

        if ($this->RequestHandler->isAjax()) {

            $form_id = $_POST["form_id"];

            if(!isset($form_id) || !($form_id > 0))
            {
                $this->flashMessage(__("Invalid Data Provided", true));
                die();
            }

            $form_data = $this->CustomForm->read(null,$form_id);

//            if($form_data["CustomForm"]["client_id"] != $client["id"])
//            {
//                $this->flashMessage(__("Form Not Found!!", true));
//                die();
//            }

            // update
            $updated_arr = array();
            $this->CustomForm->id = $form_data["CustomForm"]["id"];
            $is_deleted = 0;
            $output["msg"] = __("Form is Canceled Deleted Successfully",true);
            if($form_data["CustomForm"]["is_deleted"] == 0)
            {
                $is_deleted = 1;
                $output["msg"] = __("Form is Deleted Successfully",true);
            }
            $updated_arr["CustomForm"]["is_deleted"] = $is_deleted;
            if($this->CustomForm->save($updated_arr))
            {
                $this->flashMessage($output["msg"]);
                die();
            }
            else{
                $this->flashMessage(__("Something wrong occured", true));
                die();
            }

        }
        else{
            $this->flashMessage(__("Request Not Permitted !!", true));
            die();
        }

    }
	
    public function owner_preview_fields ( $hash = null ,$form_id ){
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if ( is_null ($hash) ) {
            /*if ( !empty ( $_POST['data']))
            {*/
                $items = $_POST["data"];
                $counter = 0;
                $fields_arr = array(); 
                $fields = [] ; 
                foreach ($items as  $item) 
                {
                    $form_fields_data = array();
                    $item_id = 0;
                    if ( isset($item["item_id"]) && $item["item_id"] > 0) {
                        $item_id = $item["item_id"];
                        $fields_arr[] = $item_id;
                    }
                    unset($item["item_id"]);
                    // no-sql element (separator - free static content)
                    $add_user_field_flag = true;
                    if(isset($item["pure_element"]))
                    {
                        unset($item["pure_element"]);
                        $add_user_field_flag = false;
                    }
                    else{
                        $item["type_options"] = json_encode((isset($item["type_options"])?$item["type_options"]:array()));
                        $item["validation_options"] = json_encode((isset($item["validation_options"])?$item["validation_options"]:array()));
                        $item["layout_options"] = json_encode((isset($item["layout_options"])?$item["layout_options"]:array()));
                    }
                    $item["custom_form_id"] = $id;
                    $item["display_order"] = $counter +1;
                    $add_two_fields = false;
                    // special case to add 2 fields for currency field
                    if($item["type"] == 150)
                    {
                        $add_two_fields = true;
                    }

                    $counter++;
                    $item ['id']=$counter;
                    $form_fields_data["CustomFormField"] = $item;
                    $fields[] = $form_fields_data;
                }
                $fields['custom_form_name'] = $_POST['custom_form_name'];
                $gen_hash = 'preview_hash_'.time() ; 
                $this->Session->write($gen_hash, $fields);
                die ( $gen_hash ) ;
           // }
        }else {
            
            $fields = $this->Session->read($hash );
            $custom_form_name = $fields['custom_form_name'];
            unset ( $fields['custom_form_name']);
            $reread_form = $this->CustomForm->findById ($form_id );
            //if ( !empty ($fields ) && !is_null ( $fields ) && $fields != ''  ){
                $this->Session->delete($hash );
                $this->set ( 'form_name' ,$custom_form_name );
                $this->set ( 'fields' , $fields ) ;
                $this->set ( 'preview_only' , 1 ) ;
                $this->render ( 'owner_save_row');
            //}
            
        }
    }
    

    /** owner_save_row($form_id, $id = null)
     * this function can :-
     * add new row to created table >> custom_table_1
     * edit exist row via passing $id >> row_id and $form_id to determine which custom table you treat with
     **/
     function owner_save_row($form_id, $id = null)
    {

        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if(!isset($form_id) || !($form_id > 0))
        {
            $this->flashMessage(__("Form Not Exist !!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
		$custom_form = $this->CustomForm->findById($form_id);
        if(!is_array($custom_form) || count($custom_form) == 0)
        {
            $this->flashMessage(__("Form Not Exist !!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
		$CustomForm = $this->CustomForm->get_custom_form_model($custom_form);

		if($this->RequestHandler->isPost() || $this->RequestHandler->isPut() )
		{
			$validationErrors = [];
			
			if($id){
				$this->data['CustomModel']['id'] = $id;
			}
			
			$result = $this->CustomForm->sperate_custom_form_data_and_custom_form($CustomForm,$this->data);
			$subforms_data = $result['subforms'];
			$custom_data = $result['custom_form'];
			$result = $this->CustomForm->validate_custom_form_data($custom_form, $custom_data);
			//validating the custom form
			if($result['errors'])
			{
				$validationErrors = $result['errors'];
			}else{
				$custom_data = [];
				$custom_data = $result['validated_data'];
				foreach($subforms_data as $subform_model_name => &$subform_data)
				{
					$custom_model_id = end(explode('_', $subform_model_name));
					$subform = $this->CustomForm->findByid($custom_model_id);
					if($subform)
					{
						$SubformModel = GetObjectOrLoadModel($subform_model_name);
						foreach($subform_data as $k => &$subform_record){
							//validating the  subforms
							if($subform_record['id'])
							{
								$SubformModel->id = $subform_record['id'];
							}
							$result = $this->CustomForm->validate_custom_form_data($subform, $subform_record);
//							debug($result);
							if($result['errors']){
								$validationErrors[$subform_model_name][$k] = $result['errors'];
								break;
							}else{
								
								$subform_record = $result['validated_data'];
							}
							
						}
						
					}
				if($validationErrors){
						break;
					}
				}
				// all data is validated
				if(empty($validationErrors))
				{
					if($custom_record_id = $this->CustomForm->save_custom_form_data($CustomForm, $custom_data))
					{
						if(!empty($subforms_data))
						{
							$this->CustomForm->save_sub_forms($CustomForm, $custom_record_id, $subforms_data);	
						}
						
					}
					if(IS_REST){
							if(!$id){
								$this->set('id', $custom_record_id);
								$this->render('created');
								return;
							}else{
								$this->render('success');
								return;
							}
						}
					//now save all the data
				}else{
					if(IS_REST) $this->cakeError("error400", [ "validation_errors"=>$validationErrors]);
				}
				
			}
//			dd($validationErrors);
			
			
			
			
			
			

		}
		if(!empty($validationErrors)){
			
			$CustomForm->validationErrors =  $validationErrors;
		
		}
		$CustomForm->custom_table_id = $form_id;
		$CustomForm->custom_model_name = $CustomForm->alias;
//		dd($CustomForm);
		$result = $this->CustomForm->get_display_form_fields($CustomForm, $id);
		$form_fields = $custom_fields_data['fields'];
        
        $currencies = $this->Currency->getCurrencyList(array(), true);
		$this->set('table', $result['table']); //for the redirect link when you want to edit the custom form field
		$this->set('item_id', $result['item_id']); //for the redirect link when you want to edit the custom form field
		$this->set('custom_form_fields', $result['fields']);
		$this->set('many_fields', $result['many_fields']);
//		die(var_dump($CustomForm->alias));
		$this->set('table_name', $CustomForm->alias); //for the redirect link when you want to edit the custom form field
		$this->set('file_form', $result['file_form']);
        $this->set('currencies', $currencies);
        $this->set("fields",$result['fields']);
        $this->set("row_id",$id);
        $this->set("form_id",$form_id);
		$this->set('custom_form', $custom_form);

    }
	



    /**
     * Gets the records to be placed in the model data field 
     * @param string $table_name
     * @param string $model_field
     * @param string $make_field
     * @param INT $make_id
     */
    public function owner_get_make_data ( $table_name , $model_field,$make_field ,  $make_id = null ) 
    {
        $this->loadModel('CustomTable');
        $this->CustomTable->_modelSettings($table_name, NULL, NULL);
        $all_data = $this->CustomTable->find('list' , ['fields' => $make_field ,  'recursive' => -1 , 'conditions' => [$model_field => $make_id ] ] );
        die  ( json_encode($all_data ));
    }

    /** owner_manage_data($form_id)
     * this function can :-
     * list all table rows that you can filtering results , view , edit , delete any rows
     **/
    public function owner_manage_data($form_id)
    {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		
        $client = getAuthOwner();

        // check if this form is set and has data and has own to this client
        if(!isset($form_id))
        {
            $this->flashMessage(__("Form Not Exist !!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
        $form_result = $this->CustomForm->_get_form_data($form_id, $client);
		//debug ( $form_result) ;
        if(!is_array($form_result) || count($form_result) == 0)
        {
            $this->flashMessage(__("Form Not Exist !!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
        $this->set("form_result",$form_result);

        
        // get form fields if exist
        $form_fields = $this->CustomFormField->get_form_fields($form_id);
        $this->set('fieldsCount', count($form_fields));

        // get is_listing & is_filtered fields and set fields names
		$db_table_name = $form_result["CustomForm"]['table_name'];
		if ( trim ($db_table_name ) == "" )
		{
			$table_name = "custom_table_$form_id";
		}else 
		{
			$table_name = $this->CustomForm->get_custom_table_database_name($db_table_name);
		}
        $custom_fields = $this->CustomForm->_load_custom_fields(NULL,$table_name);
//        $reread_form = $custom_fields['rr'];
//        $cc = $custom_fields ['cc'];
//        $this->set('fields', $custom_fields['fields']);
        $fields_names_arr = array("id");
        $get_lising_arr = array();
        $get_listing_type_arr = array(0);
        $get_filtered_arr = array();
        $form_fields = $custom_fields['fields'] ; 
        
        foreach($form_fields as $key => $value)
        {
            if($value["CustomFormField"]["is_listing"] == 1)
            {
                // special case if currency type
				
                if($value["CustomFormField"]["type"] == 150)
                {
                    $fields_names_arr[] = "field_code_".$value["CustomFormField"]["id"];
                }
                $get_listing_type_arr[] = $value["CustomFormField"]["type"];
                $get_lising_arr[] = $value;
                $fields_names_arr[] = "field_".$value["CustomFormField"]["id"];
            }

            if($value["CustomFormField"]["is_filtered"] == 1)
            {
                $get_filtered_arr[] = $value;
            }

        }
//		die ( print_pre( $get_filtered_arr ) );
        $this->set("form_id",$form_id);
        $this->set("fields_names_arr",$fields_names_arr);
        $this->set("get_lising_arr",$get_lising_arr);
        $this->set("get_listing_type_arr",$get_listing_type_arr);
		
        $currencies = $this->Currency->getCurrencyList(array(), true);
        $this->set('currencies', $currencies);

        // set current form model
        $this->CustomForm->_modelSettings($table_name,$form_fields, $form_id);

        // get values from fields names arr
        $conditions = "";
        $current_page = 0;
        if(isset($_GET) && is_array($_GET) && count($_GET))
        {
            $conditions = $this->CustomTable->get_value_array_condition($_GET);
            if(isset($_GET["page"]))
            {
                $current_page = $_GET["page"];
            }
        }
        
        $fields_values = $this->CustomTable->get_rows_with_specific_cols($fields_names_arr, $form_id,null, $conditions , $current_page);
		// get all rows count
        $rows_count = $this->CustomTable->get_rows_with_specific_cols($fields_names_arr, $form_id,null, $conditions , -1, true);
        $rows_count = $rows_count[0][0]["results_count"];
        //debug($get_filtered_arr);
        $this->set("fields_values",$fields_values);
        $this->set("rows_count",$rows_count);
        $this->set("per_page",$this->CustomTable->per_page);
        $this->set("get_filtered_arr",$get_filtered_arr);
		$this->set("table_name" , $table_name );
		$this->set('custom_form', $form_result);
		
    }
	
	public function api_form_data($form_id){
		$client = getAuthOwner();
		
        // check if this form is set and has data and has own to this client
        if(!isset($form_id))
        {
			if(IS_REST) $this->cakeError('error404', array('message' => __('Supplier not found', true)));
//            $this->flashMessage(__("Form Not Exist !!", true));
//            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
        $form = $this->CustomForm->_get_form_data($form_id);
		$this->loadModel('CustomFormField');
		$form_fields = $this->CustomFormField->get_form_fields($form_id);
		//debug ( $form_result) ;
        if(!is_array($form) || count($form) == 0)
        {
			if(IS_REST) $this->cakeError('error404', array('message' => __('Supplier not found', true)));
//            $this->flashMessage(__("Form Not Exist !!", true));
//            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
		$CustomForm = $this->CustomForm->get_custom_form_model($form);
		$has_many = $this->CustomForm->has_many_field($form_fields);
		
		if($has_many){
			$has_many_relations = $this->CustomForm->generate_has_many_relations($CustomForm->alias,$has_many);
			$CustomForm->bindModel([
					'hasAndBelongsToMany' => $has_many_relations
				],false);
			
		}
		$labels = $this->CustomFormField->find('list',['fields' => ['id','label']]);
//		debug($labels);
		
		if(IS_REST){
			$CustomForm->recursive = 1;
//			dd($this->paginate($CustomForm));
			$result = $this->paginate($CustomForm);
//			dd($result);
//			foreach($result as $k => &$custom_record){
//				foreach($custom_record as $custom_model_name => &$custom_model_data)
//				{
//					
//					if(is_array($custom_model_data[0])){
//						//this is the a has many result
//						foreach($custom_model_data as $has_many_index => &$custom_has_many_data)
//						{
//							$custom_has_many_data = $this->CustomForm->rename_result_fields($custom_has_many_data,$labels);
//							
//						}
//					}
//					else{
//						$custom_model_data = $this->CustomForm->rename_result_fields($custom_model_data,$labels);
//					}
//					
//				}
//			}

			$this->set('rest_items', $result);
			$this->set('rest_model_name', "CustomForm");
			$this->render("index");
		}
		
	}
	
	function owner_delete_record($form_id, $id){
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$output_arr = array();
        $output_arr["success"] = false;
        $output_arr["msg"] = "";
		
		if(!$form_id || !$id){
			
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('id', true))]);
		}
		$form_data = $this->CustomForm->read(null,$form_id);
		if(!$form_data)
		{
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), 'Form')]);
		}
		$CustomTable = $this->CustomForm->get_custom_form_model($form_data);
		$custom_record = $CustomTable->findById($id);
		if(!$custom_record)
		{
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), $form_data['CustomForm']['title'])]);
		}
		$result = $this->CustomForm->delete_custom_record_with_related($CustomTable->name,$id);
		$module_name = __('Custom Form',true);
		if($result)
		{
			if(IS_REST){
			$this->set("message", sprintf(__('%s has been deleted', true), ucfirst($module_name)));
			$this->render("success");
			return;
			}
			$output_arr["success"] = true;
			echo json_encode($output_arr);
			
		}else{
			if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
		}
		die();
		
	}

    /** owner_delete_specific_row()
     * this function can :-
     * receive request from owner_manage_data view to delete specific custom table row in ajax request
     **/
//    function owner_delete_specific_row()
//    {
//        
//        $output_arr = array();
//        $output_arr["success"] = false;
//        $output_arr["msg"] = "";
//
//        if ($this->RequestHandler->isAjax()) {
//
//            $form_id = $_POST["form_id"];
//            $row_id = $_POST["row_id"];
//
//            if(isset($form_id) && $form_id > 0 && isset($row_id) && $row_id >0)
//            {
//
//                $form_data = $this->CustomForm->read(null,$form_id);
////                if($form_data["CustomForm"]["client_id"] == $client["id"])
////                {
//
//                    // get form fields if exist
//                    $form_fields = $this->CustomFormField->get_form_fields($form_id);
//
//                    // set current form model
//                    $this->CustomForm->_modelSettings("custom_table_$form_id",$form_fields, $form_id);
//					die(var_dump($this->CustomTable->alias));
//                    // check if rowis exist to delete
//                    $result_row = $this->CustomTable->read(null,$row_id);
//                    if(is_array($result_row) && count($result_row))
//                    {
//						$this->CustomForm->delete_custom_record_with_related($this->CustomTable->alias,$row_id );
//                        $output_arr["success"] = true;
//                    }
//
////                }
//
//            }
//        }
//
//        echo json_encode($output_arr);
//        die();
//    }
	
	function api_view_row($form_id,$id)
	{
		
		if(!$form_id || !$id){
			
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('id', true))]);
		}
		$form_data = $this->CustomForm->read(null,$form_id);
		if(!$form_data)
		{
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), 'Form')]);
		}
		$CustomTable = $this->CustomForm->get_custom_form_model($form_data);
		$custom_record = $CustomTable->findById($id);
		if(!$custom_record)
		{
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), $form_data['CustomForm']['title'])]);
		}
	
		$form_fields = $this->CustomFormField->get_form_fields($form_id);
		$relations = $this->CustomForm->get_custom_model_relations($CustomTable->alias,$form_fields);
		$CustomTable->bindModel($relations,false);
		$custom_record = $CustomTable->findById($id);
		
		$this->set('rest_item', $custom_record);
		$this->set('rest_model_name', CustomForm);
		$this->render("view");
	}

    /** owner_view_row($form_id,$row_id)
     * this function can :-
     * get data of specific row from custom_table_$form_id and list it in view
     **/
    function owner_view_row($form_id,$row_id)
    {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		
        $client = getAuthOwner();
		
        if(!isset($form_id) || !($form_id > 0))
        {
            $this->flashMessage(__("Form Not Exist !!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }

        $form_result = $this->CustomForm->_get_form_data($form_id, $client);
        if(!is_array($form_result) || count($form_result) == 0)
        {
            $this->flashMessage(__("Form Not Exist !!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }

        if(!isset($row_id) || !($row_id > 0))
        {
            $this->flashMessage(__("Item Not Found!!", true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'manage_data',$form_id));
        }
		$result = $this->CustomForm->prepare_custom_fields($form_id, $row_id);
		$this->set('sections', $result);
		$this->set("form_id",$form_id);
        $this->set("row_id",$row_id);
//        $this->set("types_arr",$types_arr);
//        $this->set("fields_arr",$fields_arr);
//        $this->set("labels_arr",$labels_arr);
//        $this->set("field_values",$field_values);
    }
	function owner_edit_custom_fields ($table_name ,$redir_back = 0 ) {

        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$this->loadModel ('CustomForm' );
		if(!in_array($table_name,CustomForm::$relatedEntitiesTableName)){
            $this->flashMessage(__("Table Not Found!!", true));
            $this->redirect($this->referer());
        }

		$cform = $this->CustomForm->findByTableName ($table_name );
		if ( !empty ( $cform )) {
            $this->redirect ( '/owner/custom_forms/custom_fields/'.$cform['CustomForm']['id']."?redir=$redir_back") ;die ;
                }else {
                    $client = getAuthOwner();

//                    $this->data["CustomForm"]["client_id"] = $client["id"];
                    $this->data["CustomForm"]["title"] = str_replace("_"," ",ucfirst (Inflector::singularize($table_name))).' More Information';
                    $this->data["CustomForm"]["table_name"] = $table_name;
                    $this->data["CustomForm"]["last_submitted"] = date('y-m-d h:i:s');
                    $this->owner_save() ; 
                    
                }
                die ; 
		
	}
    function owner_delete_attachment ($field_id , $custom_item_id = null) {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel  ( 'CustomFormField');
        $this->loadModel  ( 'CustomTable');
        $field_data  = $this->CustomFormField->findById ($field_id);
//        print_r ( $field_data  ) ;
        if ( !empty ( $field_data ) && !empty($custom_item_id)) {
            $table_name = $this->CustomForm->get_custom_table_database_name($field_data['CustomForm']['table_name']);
            $this->CustomForm->_modelSettings($table_name, [], 0);
            $custom_field  = $this->CustomTable->find('first' , ['conditions' => [
                
                'id' => $custom_item_id]]);
            
            $this->CustomTable->id = $custom_field[$table_name]['id'];
            $this->CustomTable->saveField ( 'field_'.$field_id , "" );
            unlink ($custom_field[$table_name]['field_'.$field_id] );
            die ( "1");
        }else {
            die ( "0");
        }
    }
    function owner_get_autocomplete ( $field_id ) {
        die ( json_encode ( $this->CustomForm->get_distinct_field_values ( $field_id ) ) ) ;
    }
	

	
	function owner_get_form_view($model_name, $item_id = null){
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$result = $this->CustomForm->get_display_form_fields($model_name, $item_id);
//		dd($reuslt );
		
		$this->set('table', $result['table']);
		$this->set('item_id', $result['item_id']);
		$this->set('fields', $result['fields']);
        $this->set('table_name', $result['table_name']);
//        return ['rr' => $reread_form, 'cc' => $cc, "fields" => $form_fields, 'table_name' => $custom_table_name];
		
		
	}
	
	function api_get_fields($form_id)
	{
		if(!$form_id ){
			
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('id', true))]);
		}
		$form_data = $this->CustomForm->read(null,$form_id);
		if(!$form_data)
		{
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), 'Form')]);
		}
	
		$form_fields = $this->CustomFormField->get_form_fields($form_id);
		
		$this->set('rest_item', $form_fields);
		$this->set('rest_model_name', 'CustomForm');
		$this->render("view");
	}

	function owner_custom_fields_update()
    {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('CustomFormField');
        $this->loadModel('CustomForm');

        $count = $this->CustomFormField->updateCustomFields();
        die($count .' fields updated');
    }

    public function owner_restore_fields() {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('CustomFormField');
        if(!empty($_POST)) {
            $this->CustomFormField->updateAll(['CustomFormField.is_deleted' => 0], ['CustomFormField.is_deleted' => 1, 'CustomFormField.id' => $_POST['id']]);
            $this->flashMessage(__("Fields Restored", true), 'Sucmessage');
        }
        $deletedCustomFormFields = $this->CustomFormField->find('all', ['conditions' => ['CustomFormField.is_deleted' => true]]);
        $this->set('customFormFields', $deletedCustomFormFields);
        $this->set('fieldTypes', $this->fields_type);
    }
}

