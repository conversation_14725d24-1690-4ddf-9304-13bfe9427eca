<?php

App::import('Vendor', 'settings');

class AssetGroupsController extends AppController {

    var $name = 'AssetGroups';
//    var $validate_schema;

   
    function beforeFilter() {
        parent::beforeFilter();
        
    }
    function __settings ($exclude_group = null ) {
        $asset_groups = $this->AssetGroup->find ( 'list' , ['conditions' => ['AssetGroup.id <> '.intval($exclude_group)] ]);
        $this->set ( 'asset_groups' , $asset_groups );
    }
    /** 
     * Form for adding an asset group
     */
    function owner_add ( ) 
    {
       if ( !empty ( $this->data ) ){
           if ( $asset_id = $this->AssetGroup->add ( $this->data , $validation_errors )) {
               $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset Group', true)), 'Sucmessage');
               $this->redirect (Router::url(['action' => 'view' , $asset_id ]) ) ;
           } else {
               $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset Group', true)));
           }
       }
       $this->__settings ( ) ;
    }
    /**
     * Form for editing an asset group
     */
    function owner_edit ( $id ) 
    {
       if ( !empty ( $this->data ) ){
           if ( $asset_id = $this->AssetGroup->update ( $id , $this->data , $validation_errors )) {
               $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset Group', true)), 'Sucmessage');
               $this->redirect (Router::url(['action' => 'edit' , $asset_id ]) ) ;
           } else {
               $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset Group', true)));
           }
       }else {
           $this->data = $this->AssetGroup->find ('first' ,['conditions' => ['AssetGroup.id' => $id] ] ) ;
       }
        $this->__settings ( $id ) ;
        $this->render('owner_add');
    }
    
    

}
