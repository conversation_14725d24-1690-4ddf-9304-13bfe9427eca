<?php

use Izam\Daftra\Common\Utils\PermissionUtil;

class ApiRolesController extends AppController
{
    var $name = 'ApiRoles';
    var $ApiRole;
	var $helpers = array('Html', 'Form');
    var $uses = ['ApiRole', 'ApiKey'];
    function owner_edit($id = null)
    {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $ApiKey = $this->ApiKey->find('first', ['conditions' => ['ApiKey.id' => $id, 'ApiKey.site_id' => getCurrentSite('id')]]);
        if (!$ApiKey) {
            $this->flashMessage(__('Not found', true));
            $this->redirect(array('controller' => 'api_keys','action' => 'index'));
        }
        if (!empty($this->data)) {
            if (!empty($this->data['ApiRole']['urls'])) {
                $urls = explode("\n", trim($this->data['ApiRole']['urls']));
                foreach ($urls as $key => &$url) {
                    $url = trim($url);
                    $url = preg_replace("(^https?://$_SERVER[SERVER_NAME]/)", "", $url);
                    $url = preg_replace("(^$_SERVER[SERVER_NAME]/)", "", $url);
                    $url = preg_replace("(^/)", "", $url);
                    if (empty($url)) {
                        unset($urls[$key]);
                    }
                }
                $this->data['ApiRole']['api_key_id'] = $id;
                $this->data['ApiRole']['urls'] = json_encode(array_values($urls));
            }
            if($this->ApiRole->save($this->data)){
                $this->flashMessage(__('Saved', true), 'Sucmessage');
                $this->redirect(array('controller' => 'api_keys','action' => 'index'));
            }else{
                $this->flashMessage(__('error', true));
            }
        }
        $this->set('apiKey', $ApiKey);
    }
}
