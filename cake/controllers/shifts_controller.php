<?php
class ShiftsController extends AppController {

	var $name = 'Shifts';

	/**
	 * @var Shift
	 */
	var $Shift;
	var $helpers = array('Html', 'Form');

	

	function owner_index() {
		$this->Shift->recursive = 0;
		$conditions = $this->_filter_params();
		$owner=getAuthOwner();
		if($owner['staff_id']==0) {
            $this->Shift->applyBranch['onFind'] = false;
        }
		$this->set('shifts', $this->paginate('Shift', $conditions));
	}

	function owner_test_available()
	{
        $this->Shift->getStaffShiftsDaysForDatePicker(19);
		$staff_id = 13;
		$date = '2018-11-28';
		$duration = 110;
		$slot = 15;
		$result = $this->Shift->get_staff_available_time($staff_id, $date, $duration, $slot, [12675]);
	}
			
	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('shift', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->Shift->recursive = 2;
		$this->loadModel('Role');
		$roles = $this->Role->find('list');
		$this->set('roles', $roles);
		$shift = $this->Shift->findById($id);
		$shift_days = $this->Shift->get_shift_days();
//		$this->Shift->merge_
		$this->set('shift_days',$shift_days);
		$this->set('shift', $shift);
		
	}

	function owner_add() {

		if (!empty($this->data)) {
			$result = $this->Shift->add_shift($this->data);
			
			if ($result['status']) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('Shift',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Shift',true)));
			}
		}
		$this->form_common();
	}
	
	function form_common()
	{
		$shift_days = $this->Shift->get_shift_days();
		$this->set('shift_days',$shift_days);
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'shift',true)));
			$this->redirect(array('action'=>'index'));
		}
		$shift = $this->Shift->read(null, $id);

		if (!empty($this->data)) {
			$result = $this->Shift->add_shift($this->data);
			if ($result['status']) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('Shift',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('Shift',true)));
			}
		}
		if (empty($this->data)) {
//			$this->data = $shift;
			$this->set('shift', $shift);
		}
		$this->form_common();
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('shift',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('shift', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('shifts', true);
		 } 
		$shifts = $this->Shift->find('all',array('conditions'=>array('Shift.id'=>$id)));
		if (empty($shifts)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->Shift->deleteAll(array('Shift.id'=>$_POST['ids']),true,true)) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('shifts',$shifts);
	}

    public function get_staff_work_days($staffId)
    {
        echo json_encode(array_values($this->Shift->getStaffShiftsDaysForDatePicker($staffId)));
        exit;
    }
}
?>
