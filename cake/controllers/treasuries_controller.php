<?php

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use App\Services\Treasury\ShowService;
use Izam\View\Form\Tab\Services\CreateTabsServices;
use Izam\Limitation\Utils\LimitationUtil;
use Izam\Daftra\Common\Utils\BankTransactionsStatusUtil;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Navigation\Navigation;
use Izam\View\Form\Element\LengthAwarePaginator;
use Izam\Entity\Service\CreateFilterFormService;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\TreasuryTransferComponent;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
/**
 * @property Treasury $Treasury
 * @property ItemPermission $ItemPermission
 * @property TreasuryTransferTransaction $TreasuryTransferTransaction
 * @property TreasuryTransfer $TreasuryTransfer
 * @property JournalAccount $JournalAccount
 */
class TreasuriesController extends AppController {

    var $name = 'Treasuries';
    var $helpers = array('Html', 'Form');
    var $components = ["ItemPermissions"];

    function beforeFilter() {
        parent::beforeFilter();
        if ( !ifPluginActive(ExpensesPlugin) ) {
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
    }
    function owner_index ( ) {
        if(!IS_REST){
            $this->redirect('/v2/owner/banks/treasury');
        }

        $staff_id=getAuthOwner('staff_id');
        if($staff_id==0){
            $is_admin=true;
        }else {
            $is_admin = (bool)getAuthStaff()['Role']['is_super_admin'];

        }
        $this->loadModel('ItemPermission');
        if($is_admin==false) {
            $hisWithDrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW,null,true) ?: [];
            $hisDepositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];

            $hisTreasuries = array_unique(array_merge(array_keys($hisWithDrawTreasuries), array_keys($hisDepositTreasuries)));
            $conditions= ['Treasury.id' => $hisTreasuries];
        }else{
            $hisTreasuries=true;
            $conditions=[];
        }
        if ($hisTreasuries || check_permission(Edit_General_Settings)) {
            $this->paginate['Treasury']['order'] = 'Treasury.name';
            $all_treasuries = $this->paginate('Treasury',$conditions);
            foreach ( $all_treasuries as $k => &$v ) {
                $v['Treasury']['balance'] = $this->Treasury->get_total_balance ( [$v['Treasury']['id'] => '' ])[$v['Treasury']['id']];
                unset ( $v ) ;
            }
        } else {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
        $this->set ( 'all_treasuries' ,$all_treasuries );//
        $this->set ( 'default_currency' ,$this->Treasury->get_default_currency ( ) );//
        $hisTreasuriesCount = is_bool($hisTreasuries) ? 0 : count($hisTreasuries);
        $this->set("results_count", $hisTreasuriesCount);// $results_count[0][0]["results_count"]);
        $this->setup_nav_data($all_treasuries);

        if(IS_REST){
            $this->set('rest_items', $all_treasuries);
            $this->set('rest_model_name', "Treasury");
            $this->render("index");
        }
    }

    /**
     * takes get parameters :
     * 1- permission in (ItemPermission::PERMISSION_WITHDRAW, ItemPermission::PERMISSION_DEPOSIT)
     */
    public function owner_list()
    {

        $staff_id=getAuthOwner('staff_id');
        if($staff_id==0){
            $is_admin=true;
        }else {
            $is_admin = (bool)getAuthStaff()['Role']['is_super_admin'];
        }
        $this->loadModel('ItemPermission');
        $conditions=[];
        if($is_admin==false) {
            if(isset($_GET['permission']))
            {
                if($_GET['permission'] == ItemPermission::PERMISSION_WITHDRAW)
                {
                    $hisWithDrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW,null,true) ?: [];
                    $hisDepositTreasuries = [];
                }else if($_GET['permission'] == ItemPermission::PERMISSION_DEPOSIT){
                    $hisDepositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];
                    $hisWithDrawTreasuries = [];
                }
            }else{
                $hisWithDrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW,null,true) ?: [];
                $hisDepositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];
            }


            $hisTreasuries = array_unique(array_merge(array_keys($hisWithDrawTreasuries), array_keys($hisDepositTreasuries)));
            $conditions= ['Treasury.id' => $hisTreasuries];
        }else{
            $hisTreasuries=true;
        }
        $conditions['active'] = 1;
        if ($hisTreasuries) {
            $this->paginate['Treasury']['order'] = 'Treasury.name';
            $all_treasuries = $this->Treasury->find('all', ['conditions' => $conditions]);

            foreach ( $all_treasuries as $k => &$v ) {
                $v['Treasury']['balance'] = $this->Treasury->get_total_balance ( [$v['Treasury']['id'] => '' ])[$v['Treasury']['id']];
                unset ( $v ) ;
            }
        } else {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }

        if(IS_REST){

            $this->set('rest_items', $all_treasuries);
            $this->set('rest_model_name', "Treasury");
            $this->render("index");
        }


    }

    function owner_list_transfers ( $id )
    {
        if (!check_permission(VIEW_ASSIGNED_TREASURY)) {
            if (IS_REST) $this->cakeError('error403');
            $this->flashMessage(sprintf(__('You don\'t have permission to open this %s', true), __('Page',true)));
            if(!empty($_GET['iframe'])) {
                
                $this->layout = 'box';
                return $this->render('../elements/not_allowed_error_message');
            }
            $this->redirect('/');
        }

        $treasury = $this->Treasury->find ( 'first' , ['conditions' => ['Treasury.id' => $id ]]);
        if ( empty ($treasury ) )
        {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Treasury', true))));
            $this->izamFlashMessage(sprintf ( __('%s not found', TRUE) , __("Treasury" , true) ),'danger');
            $this->redirect($this->referer(array('action' => 'index'), true));
            die ;
        }
        $this->loadModel('TreasuryTransfer');
        $belongsTo = array(
            'ToTreasury' => array('className' => 'Treasury', 'foreignKey' => 'to_treasury_id', 'order' => 'ToTreasury.id asc', 'dependant' => false),
            'FromTreasury' => array('className' => 'Treasury', 'foreignKey' => 'from_treasury_id', 'order' => 'FromTreasury.id asc', 'dependant' => false),
        );
        $this->TreasuryTransfer->bindModel(array('belongsTo'=>$belongsTo),false);
        $sort=$_GET['sort']?$_GET['sort']:'desc';
        $owner = getAuthOwner();
        $conditions=[];


        if (!empty($this->params['url']['filter']['date_from'])) {
            $date_from = $this->TreasuryTransfer->formatDate($this->params['url']['filter']['date_from'].' 00:00', $owner['date_format']);
            $conditions['TreasuryTransfer.transfer_date >=']=$date_from;
        }
        if (!empty($this->params['url']['filter']['date_to'])) {
            $date_to = $this->TreasuryTransfer->formatDate($this->params['url']['filter']['date_to'].' 23:59', $owner['date_format']);
            $conditions['TreasuryTransfer.transfer_date <=']=$date_to;
        }

        $this->paginate = array(
                'order' => array('TreasuryTransfer.transfer_date ' => $sort),
                'conditions' => $conditions + ['OR' => ['TreasuryTransfer.from_treasury_id' => $id, 'TreasuryTransfer.to_treasury_id' => $id]]
        );

        $rows = $this->paginate('TreasuryTransfer');

        // $rows=$this->TreasuryTransfer->find('all',array('order'=>'TreasuryTransfer.transfer_date '.$sort,'limit'=>3000,'conditions'=>$conditions+['OR'=>['TreasuryTransfer.from_treasury_id'=>$id,'TreasuryTransfer.to_treasury_id'=>$id]]));
        $rows_count=$this->TreasuryTransfer->find('count',array('limit'=>3000,'conditions'=>$conditions+['OR'=>['TreasuryTransfer.from_treasury_id'=>$id,'TreasuryTransfer.to_treasury_id'=>$id]]));
        $this->set('id',$id);
        $this->set('rows',$rows);
        $this->set('rows_count',$rows_count);

        $arrow_direction = "forward";
        if (is_rtl()) {
            $arrow_direction = "arrow-left-bold";
        }
        $this->set('arrow_direction',$arrow_direction);
        $filtered=$this->params['url'];
        unset($filtered['ext']);
        unset($filtered['url']);
        $this->set('filtered',$filtered);

        $this->setDefaultViewData();
        $this->view = 'izam';
        $this->setIndexData();
        /** will move ListingRedirectService in package and use it */
        if ($this->viewVars['pagination']->total() == 0) {
            $queryParams = $_GET;
            unset($queryParams['url'], $queryParams['iframe'], $queryParams['show_filters']);
            if (empty($queryParams)) {
                return $this->render('default/iframe_no_added_yet');
            }
            return $this->render('default/iframe_no_result_found');
        }
        return $this->render('default/iframe_index');
    }


    public function prepare_data_for_view($data ,$key)
    {
        return array_map( function ($item) use ($key)
        {
            $item_array = (array)$item;
            $temp_item_array=$item_array[$key];
            unset($item_array[$key]);
            return  array_merge($temp_item_array,$item_array);
        },$data);
    }



    function owner_view ( $id ) {
        if (
            !check_permission(VIEW_ASSIGNED_TREASURY)  &&
            !check_permission(Edit_General_Settings) &&
            !check_permission(CHANGE_DEFAULT_TREASURY) &&
            !check_permission(VIEW_ALL_JOURNALS) &&
            !check_permission(MANAGE_JOURNAL_ACCOUNTS)
        ){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
        $staff_id=getAuthOwner('staff_id');
        if($staff_id==0){
            $is_admin=true;
        }else {
            $is_admin = (bool)getAuthStaff()['Role']['is_super_admin'];
        }
        if($is_admin==false) {
            $this->loadModel('ItemPermission');
            $hisWithDrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW,null,true) ?: [];
            $hisDepositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];

            $hisTreasuries = array_unique(array_merge(array_keys($hisWithDrawTreasuries), array_keys($hisDepositTreasuries)));
            if(!in_array($id , $hisTreasuries)) {
                if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Treasury', true))));
                $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
                $this->redirect ( '/');
            }
        }

        $this->loadModel ( 'JournalAccount'  ) ;
        $this->loadModel ( 'Currency'  ) ;
        if ( !in_array($id,array_keys ( $this->Treasury->get_list (0,null,false)))) {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Treasury', true))));
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
        $treasury = $this->Treasury->find ( 'first' , ['conditions' => ['Treasury.id' => $id ]]);

        if (isset($treasury['Treasury']['metainfo'])) {
            $treasury['Treasury']['metainfo'] = json_decode(
                $treasury['Treasury']['metainfo'],
                true
            );
        }

        if ( empty ($treasury ) )
        {

            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Treasury', true))));
            $this->izamFlashMessage(sprintf ( __('%s not found', TRUE) , __("Treasury" , true) ),'danger');
            $this->redirect((array('action' => 'index')));
            die ;
        }
        $this->set('title_for_layout',  (__t('View'). ' '.$treasury['Treasury']['name']));
        $this->loadModel ('JournalAccount');
        $this->loadModel('Journal');
        $this->JournalAccount->recursive=-1;
        $account_id = $this->Journal->get_auto_account ( ['entity_id' => $id, 'entity_type' =>  'treasury'])['JournalAccount']['id'];
        $this->setup_nav_view($id);
        $this->set ('treasury' ,$treasury );
        $this->set ('account_id' ,$account_id );
        $this->loadModel('TreasuryTransfer');
        $Journal = GetObjectOrLoadModel('Journal');
        $account = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::TREASURY_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id]);
        $this->set('account', $account);

        $rows_count=$this->TreasuryTransfer->find('count',array('limit'=>3000,'conditions'=>['OR'=>['TreasuryTransfer.from_treasury_id'=>$id,'TreasuryTransfer.to_treasury_id'=>$id]]));
        $this->set('rows_count',$rows_count);
        $details = $this->treasury_detail($treasury);
        $details['account'] = $account;

        if(IS_REST){
            $this->set('rest_item', $treasury);
            $this->set('rest_model_name', "Treasury");
            $this->render("view");
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $this->set('view_templates', $repo->getEntityViewTemplates('treasury'));

        $tabsData = ShowService::getTabs($treasury, $details);
        $tabs = izam_resolve(CreateTabsServices::class)->createTabs($tabsData);
        $this->set('tabs', $tabs);

        $this->set('viewActions', ShowService::getActions($treasury));
        $this->set('pageHead', ShowService::getPageHeader($treasury, $details, $this->viewVars));


        $this->setDefaultViewData();
        $this->view = 'izam';
        return $this->render('default/show');
    }

    function owner_timeline($id = false) {
        $treasury = $this->Treasury->findById($id);
        $this->set('treasury', $treasury);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Treasury', array('primary_id' => $id));

        $action_list = $timeline->getActionsList();
        $timeline->init(array('primary_id' => $id), $action_list);
        $data = $timeline->getDataArray();

        $this->set('data', $data);
        $this->loadModel('ActionLine');

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $actions[$key] = $action;
            }
        }
        $this->set('actions', $actions);

    }

    function owner_timeline_row($id = null) {
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Treasury', array('primary_id' => $id));
        $this->set('data', $timeline->getDataArray());
        echo $timeline->view_action($id);
        die();
    }

    function treasury_detail($treasury){
        $id=$treasury['Treasury']['id'];
        $treasury_permissions=  $this->Treasury->treasury_permissions($id);

        $this->set('treasury_permissions', $treasury_permissions);

        $convert_treasury_currency_to_local = $this->Treasury->convert_treasury_currency_to_local($treasury);
        $total_balance_val = $convert_treasury_currency_to_local['sum_local_cur'];
        $system_currency=  $this->Treasury->get_default_currency();
        $total_balance = [
            'treasury' => ['value' => $total_balance_val, 'currency' => $system_currency]
        ];
        $currency_balances_with_local = [];

        if (!empty($treasury['Treasury']['currency_code'])&& $treasury['Treasury']['type'] == Treasury::TYPE_BANK ) {
            $rate_val = CurrencyConverter::index($system_currency, $treasury['Treasury']['currency_code']);


            $total_balance = [
                'treasury' => ['value' => $convert_treasury_currency_to_local['sum_treasury_currency_value'], 'currency' => $treasury['Treasury']['currency_code']],
                'to_local' => ['value' => $total_balance_val, 'currency' => $system_currency,'rate_val'=>$rate_val],

            ];
        } else {

            $currency_balances_with_local = $convert_treasury_currency_to_local['treausry_currencies'];
        }

        $filter_conditions = $this->getFilterConditions();
        $show_price_tooltips=false;

        if(!empty($total_balance['to_local']) && !empty($total_balance['treasury']['value'] ) && $treasury['Treasury']['currency_code'] != $system_currency)
        {
           $show_price_tooltips=true;
        }


        $prev_treasury = $this->Treasury->find( 'first' , ['fields' => array( 'MAX(Treasury.id) as id' ),'conditions' =>  array_merge($filter_conditions,['Treasury.id <' => $id ]) ]);
        $next_treasury = $this->Treasury->find( 'first' , ['fields' => array('MIN(Treasury.id) as id' ),'conditions' =>   array_merge($filter_conditions,['Treasury.id >' => $id ]) ]);



        $prev_url = false;
        $next_url = false;

        $pag_url_parm=$_GET;
        if (!empty( $pag_url_parm['url'])) {
            unset( $pag_url_parm['url']);
        }
        $pag_url_parm='?'.http_build_query($pag_url_parm);

        $izamNavigation = new Navigation();
        $izamNavigation->setPageAddUrl(Router::url(['action'=>'add','?'=>$treasury['Treasury']['type'] == Treasury::TYPE_BANK ?'bank_account=1':'' ] ));
        $this->set('izamNavigation', $izamNavigation);
        if (!empty($prev_treasury[0]['id'])) {
            $prev_url = '/owner/treasuries/view/'.$prev_treasury[0]['id'].$pag_url_parm;
            $izamNavigation->setPagePreviousUrl($prev_url);
        }
        if (!empty($next_treasury[0]['id'])) {
            $next_url = '/owner/treasuries/view/'.$next_treasury[0]['id'].$pag_url_parm;
            $izamNavigation->setPageNextUrl($next_url);
        }
        $this->set('izamNavigation', $izamNavigation);

        $this->loadModel('BankTransaction');

        $not_matched_transactions = $this->BankTransaction->find( 'first' , ['recursive' => -1,'conditions' => ['BankTransaction.bank_id' => $id ,'BankTransaction.status'=>BankTransactionsStatusUtil::NOT_MATCHED ]]);


        $this->set('show_price_tooltips', $show_price_tooltips);
        $this->set('not_matched_transactions', $not_matched_transactions);
        $this->set('prev_url', $prev_url);
        $this->set('next_url', $next_url);
        $this->set('currency_balances_with_local', $currency_balances_with_local);
        $this->set('total_balance', $total_balance);
        $this->set('system_currency', $system_currency);

        return [
            'treasury_permissions' => $treasury_permissions,
            'show_price_tooltips' => $show_price_tooltips,
            'not_matched_transactions' => $not_matched_transactions,
            'prev_url' => $prev_url,
            'next_url' => $next_url,
            'currency_balances_with_local' => $currency_balances_with_local,
            'total_balance' => $total_balance,
            'system_currency' => $system_currency
        ];
    }

    function getFilterConditions()
    {
        $filter_conditions = $this->_filter_params();
        $pag_url_parm=[];

        foreach ($filter_conditions as $key => $condition) {
            if (is_array($condition)) {
                $filter_conditions[$key . ' LIKE'] = '%' . $filter_conditions[$key]["like"] . '%';
                unset($filter_conditions[$key]);
            }
        }

        return $filter_conditions;
    }

    function _formCommon($id = null, $bank = false)
    {
        $this->loadModel('Journal');
//        if($bank)
//        {
//            $this->setAccountRoute('banks_accounts_routing', Journal::, $id);
//        }else{
        $this->setAccountRoute('treasuries_accounts_routing', Journal::TREASURY_ACCOUNT_ENTITY_TYPE, $id);
//        }

    }

    function owner_add ( ) {

        if (!check_permission(Edit_General_Settings) && !check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }

        // Check site limitation
        $this->handleSiteLimit(
            checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::TREASURIES_BANKS),
            '/v2/owner/banks/treasury'
        );

            if (!IS_REST) {
                $this->data=$_POST;
            }

            $this->set ( 'form_url' ,'/owner/treasuries/add');
        if ( !empty ($this->data)) {
            $this->Treasury->create () ;
            if ( $this->data['Treasury']['is_primary'] == 1 )
            {
                $this->data['Treasury']['active'] = 1 ;
                $this->Treasury->updateAll( ['is_primary' => 0 ] );
            }
            $this->data['Treasury']['staff_id'] = getAuthOwner('staff_id');

            $bankReconciliations = $this->Treasury->handleBankReconciliations(
                session: new CakeSession,
                data: $this->data,
                metainfo: null,
            );

            if ($bankReconciliations['status'] == 'ERR') {
                $this->flashMessage($bankReconciliations['errors'][0]);
                $this->redirect (['action'=> 'add?bank_account=1']);
            }

            $this->data['Treasury']['metainfo'] = json_encode($bankReconciliations['data'] ?? []);

            if ( $this->Treasury->save($this->data) ){
                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_TREASURY)
                    ->save($this->data['JournalAccountRoute'], $this->Treasury->id);

                $re_read=$this->Treasury->findById($this->Treasury->id);
                $arr['primary_id']=$re_read['Treasury']['id'];
                $arr['param1']=$re_read['Treasury']['name'];
                $arr['param2']=$re_read['Treasury']['type'];
                $arr['param3']=$re_read['Treasury']['active'];
                $arr['param4']=$re_read['Treasury']['is_primary'];
                $this->Treasury->add_actionline ( ACTION_ADD_TREASURY , $arr) ;
                $this->loadModel('ItemPermission') ;
                $this->ItemPermission->savePermissions($this->data ,$this->Treasury->id , ItemPermission::ITEM_TYPE_TREASURY);
                if(IS_REST){
                    $this->set('id', $this->Treasury->id);
                    $this->render('created');
                    return;
                }


                $this->izamFlashMessage(sprintf(__('%s has been saved', TRUE) ,$this->data['Treasury']['name']) , 'success' );

                $this->redirect(['action'=> 'view',$re_read['Treasury']['id']]);
            } else {
                if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Treasury->validationErrors]);
            }
        }

        (new CakeSession)->write('BankingReconciliations', null);
        $this->set('title_for_layout',  (__t('add'). ' '.__t('Treasury')));

        $this->loadModel('Bank');
        $lang = $_SESSION['CurrentSite']['language_code'] == 7 ? "ar" : "en";

        $banks = $this->Bank->find('all', [
            'conditions' => [
                'OR' => [
                    'Bank.countries' => -1,
                    'Bank.countries LIKE ' => "%".$_SESSION['CurrentSite']['country_code']."%"
                ],
            ],
            'fields' => ['bank_name_'.$lang.' AS name', 'Bank.*'],
            'limit' => 50,
        ]);
        foreach ($banks as $index => $bank){
            $bankLogo = (new App\Services\S3FileManager)->getUrl(
                $bank['Bank']['bank_logo']
            );
            $banks[$index]['Bank']['bank_logo'] = $bankLogo;
            foreach ($bank['BankProvidersToBank'] as $ind => $provider) {
                $bank['BankProvidersToBank'][$ind]['provider_name'] = $this->Bank::BANK_PROVIDERS[$provider['bank_provider']];
            }
            $banks[$index]['Bank']['providers_to_bank'] = $bank['BankProvidersToBank'];
        }
        $this->set('banks', $banks);

        if ( !empty ($_GET['bank_account']) && check_permission(Edit_General_Settings) )
        {
            $this->data['Treasury']['type'] = Treasury::TYPE_BANK ;
            $this->loadModel ( 'Currency' ) ;
            $currencyCodes = $this->Currency->getCurrencyList();
            $this->set('currencyCodes', $currencyCodes);
            $this->set('title_for_layout',  (__t('add'). ' '.__t('Bank Account')));

        }
        $show_types = false ;
        if (!check_permission(Edit_General_Settings) )
        {
            $show_types = false ;
        }
        $this->_formCommon(null, isset($_GET['bank_account']));
        $this->set('show_types' , $show_types );
        $this->set ( 'types' ,$this->Treasury->get_types ( )  );
        $this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_TREASURY);
        $system_currency=  $this->Treasury->get_default_currency();

        $this->set('system_currency',$system_currency);


        $this->setDefaultViewData();


        $this->set('oldData', $this->data);

        $this->view='izam';
        $this->render('treasuries/add');
    }
    function owner_edit (  $id = null) {
        if (
            !check_permission(VIEW_ASSIGNED_TREASURY)  &&
            !check_permission(Edit_General_Settings) &&
            !check_permission(CHANGE_DEFAULT_TREASURY) &&
            !check_permission(VIEW_ALL_JOURNALS) &&
            !check_permission(MANAGE_JOURNAL_ACCOUNTS)
        ) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }

        $staff_id=getAuthOwner('staff_id');
        if($staff_id==0){
            $is_admin=true;
        }else {
            $is_admin = (bool)getAuthStaff()['Role']['is_super_admin'];
        }
        if($is_admin==false) {
            $this->loadModel('ItemPermission');
            $hisWithDrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW,null,true) ?: [];
            $hisDepositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];

            $hisTreasuries = array_unique(array_merge(array_keys($hisWithDrawTreasuries), array_keys($hisDepositTreasuries)));
            if(!in_array($id , $hisTreasuries)) {
                if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Treasury', true))));
                $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
                $this->redirect ( '/');
            }
        }
//        if ( !in_array($id,array_keys ( $this->Treasury->get_list (0) ) ) ) {
//            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
//            $this->redirect ( '/');
//        }
        $this->set ( 'form_url' ,'/owner/treasuries/edit/'.$id);
        $this->_formCommon($id, isset($_GET['bank_account']));
        $reread = $this->Treasury->findById ($id ) ;
        if(IS_REST) $this->data['Treasury']['id'] = $id;
        if ( !empty( $reread ) ){
            $this->data=$_POST;

            if ( !empty ($this->data ) ) {

                $metainfo = json_decode($reread['Treasury']['metainfo'], 1) ?? [];

                $bankReconciliations = $this->Treasury->handleBankReconciliations(
                    session: new CakeSession,
                    data: $this->data,
                    metainfo: $metainfo,
                );

                if ($bankReconciliations['status'] == 'ERR') {
                    $this->flashMessage($bankReconciliations['errors'][0]);
                    $this->redirect (['action'=> 'edit', $id]);
                }

                $this->data['Treasury']['metainfo'] = json_encode($bankReconciliations['data'] ?? []);

                // Check site limitation
                if ($this->data['Treasury']['active'] && $reread['Treasury']['active'] == 0) {
                    $this->handleSiteLimit(
                        checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::TREASURIES_BANKS),
                        ['action' => 'index']
                    );
                }

                if ( !$this->Treasury->can_update_treasury_currency($reread)) {
                    if (!empty($this->data['Treasury']['currency_code']) && $this->data['Treasury']['currency_code'] !=$reread['Treasury']['currency_code']) {
                        $this->izamFlashMessage(__t('Can not change currency of this bank as it already has transactions') , 'danger' );
                        $this->redirect(['action' => 'edit',$id]);
                    }
                    $this->data['Treasury']['currency_code'] =$reread['Treasury']['currency_code'];

                }
                if($this->data['Treasury']['active'] =="0"){
                    $this->data['Treasury']['is_primary'] =0;
                }
                $this->data['Treasury']['id']=$id;
                $this->Treasury->set ($this->data );

                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_TREASURY)->save($this->data['JournalAccountRoute'], $id);
                $this->Treasury->save();

                $re_read=$this->Treasury->findById($this->Treasury->id);
                $arr['primary_id']=$re_read['Treasury']['id'];
                $arr['param1']=$re_read['Treasury']['name'];
                $arr['param2']=$re_read['Treasury']['type'];
                $arr['param3']=$re_read['Treasury']['active'];
                $arr['param4']=$re_read['Treasury']['is_primary'];
                $this->Treasury->add_actionline ( ACTION_EDIT_TREASURY , $arr) ;
                $this->loadModel('ItemPermission') ;
                $this->ItemPermission->savePermissions($this->data ,$id , ItemPermission::ITEM_TYPE_TREASURY);
                if(IS_REST){
                    $this->render('success');
                    return;
                }

                $this->izamFlashMessage(sprintf(__t('%s Updated Successfully') ,$this->data['Treasury']['name']) , 'success' );

                $this->redirect(['action'=> 'view',$id]);
            } else {
                (new CakeSession)->write('BankingReconciliations', null);
            }

            // if the type of treasury equal bank check  currency
            if ($reread['Treasury']['type'] == Treasury::TYPE_BANK) {
                $disable_change_currency=false;
                $this->loadModel ( 'Currency' ) ;
                $getCurrencyConditions = null;
                if (!$this->Treasury->can_update_treasury_currency($reread)) {
                    $getCurrencyConditions = ['code' => $reread['Treasury']['currency_code']];
                    $disable_change_currency=true;
                }
                $this->set('disable_change_currency',$disable_change_currency );
                $currencyCodes = $this->Currency->getCurrencyList($getCurrencyConditions);
                $this->set('currencyCodes', $currencyCodes);
            }


            $accountEntity=Journal::TREASURY_ACCOUNT_ENTITY_TYPE;
            $accountRoute = $this->JournalAccountRoute->Journal->get_auto_account(['entity_type' => $accountEntity, 'entity_id'=> $id]);
            $journalAccountID=$accountRoute?$accountRoute['JournalAccount']['id']:null;

            $Model = ClassRegistry::init('JournalAccount');
		    $routing_value_options = $Model->getRecordsList(array('JournalAccount.id' => $journalAccountID), 30);
            $this->set ('routing_value_options' ,$routing_value_options );
            $this->set ( 'types' ,$this->Treasury->get_types ( )  );
            $this->data = $reread ;
            $this->set('title_for_layout',  (__t('Edit', true) .' '. $reread['Treasury']['name']));
            $this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_TREASURY, $id);
            // $this->render ('owner_add') ;
            // $system_currency=  $this->Treasury->get_default_currency();

            // $this->set('system_currency',$system_currency);
            $this->set('oldData',$reread);
            $metainfo = json_decode($reread['Treasury']['metainfo'], true) ?? [];

            $bankLogo = null;
            if (
                isset($metainfo['bank']['portal_bank']['bank_logo'])
                && $metainfo['bank']['portal_bank']['bank_logo'] != ""
            ) {
                $bankLogo = (new App\Services\S3FileManager)->getUrl(
                    $metainfo['bank']['portal_bank']['bank_logo']
                );
            }

            $this->set('metainfo', $metainfo);
            $this->set('bankLogo', $bankLogo);
            $this->set('providers_to_banks', $this->Treasury->getSelectedProviders($metainfo));
            $this->setDefaultViewData();
            $this->view='izam';
            $this->render('treasuries/add');

        } elseif (IS_REST) {
            $this->cakeError('error404', array('message' => __('Treasury not found', true)));
        }

    }
    function owner_delete (  $id = null ) {

        if (
            !check_permission(VIEW_ASSIGNED_TREASURY)  &&
            !check_permission(Edit_General_Settings) &&
            !check_permission(CHANGE_DEFAULT_TREASURY) &&
            !check_permission(VIEW_ALL_JOURNALS) &&
            !check_permission(MANAGE_JOURNAL_ACCOUNTS)
        ) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }

        $staff_id=getAuthOwner('staff_id');
        if($staff_id==0){
            $is_admin=true;
        }else {
            $is_admin = (bool)getAuthStaff()['Role']['is_super_admin'];
        }
        if($is_admin==false) {
            $this->loadModel('ItemPermission');
            $hisWithDrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW,null,true) ?: [];
            $hisDepositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];

            $hisTreasuries = array_unique(array_merge(array_keys($hisWithDrawTreasuries), array_keys($hisDepositTreasuries)));
            if(!in_array($id , $hisTreasuries)) {
                if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Treasury', true))));
                $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
                $this->redirect ( '/');
            }
        }

        $reread_treasury = $this->Treasury->findById ( $id ) ;
        if ( $reread_treasury['Treasury']['is_primary'] != 1  ) {
            $this->Treasury->create ( $reread_treasury ) ;
            $this->loadModel ( 'Expenses');
            $this->loadModel ( 'JournalAccount');
            $this->loadModel ( 'JournalAccountRoute');
            $this->loadModel ( 'JournalTransaction');
            $this->loadModel ('BankTransaction');
            $ja = $this->JournalAccount->find ( 'first' , ['applyBranchFind'=>false,'recursive' => -1  ,  'conditions' => ['entity_id' => $id  , 'entity_type' => 'treasury'] ]);
            if ( empty ( $ja ) ) {
                $ja = $this->JournalAccountRoute->find ( 'first' , ['applyBranchFind'=>false,'recursive' => -1  ,  'conditions' => ['entity_id' => $id  , 'entity_type' => 'treasury'] ]);
                $account_id=$ja['JournalAccountRoute']['account_id'];
            }else{
                $account_id=$ja['JournalAccount']['id'];
            }
            $transactions = []  ;
            if ( !empty ( $ja ) ) {
                $transactions = $this->JournalTransaction->find ( 'all' , ['applyBranchFind'=>false,'recursive' => -1  ,'conditions' => ['journal_account_id' => $account_id] ] ) ;
            }
            $bank_transactions = $this->BankTransaction->find('count', ['conditions' => ['BankTransaction.bank_id' => $id]]);

            $this->loadModel('TreasuryTransfer');
            $rows_count=$this->TreasuryTransfer->find('count',array('applyBranchFind'=>false,'limit'=>3000,'conditions'=>['OR'=>['TreasuryTransfer.from_treasury_id'=>$id,'TreasuryTransfer.to_treasury_id'=>$id]]));

            $this->loadModel ( 'Staff');
            $defaultStaffTreasuries = $this->Staff->find('count',['applyBranchFind'=>false,'conditions'=>['default_treasury_id'=>$id]]);
//            $financial_trans = $this->Expenses->find('all' , ['conditions'=> ['Treasury.id is not null' , 'Expenses.treasury_id' => $id]]);
            if($this->Treasury->hasLoans()){
                if(IS_REST) $this->cakeError("error400", ["message"=>__("This treasury has loan, you can make it inactive",true)]);
                $this->izamFlashMessage(__("This treasury has loan, you can make it inactive",true) , 'danger');
            }elseif($bank_transactions > 0){
                if(IS_REST) $this->cakeError("error400", ["message"=>__("This treasury has existing transactions, you can make it inactive",true)]);
                $this->izamFlashMessage(__("This treasury has existing transactions, you can make it inactive",true) , 'danger');
            }elseif ((empty ($transactions) && $defaultStaffTreasuries <= 0 && $rows_count == 0 && (!ifPluginActive(ChequeCycle) || !$this->Treasury->hasChequeBooks())) && $this->Treasury->delete($id)){
                if(IS_REST){
                    $this->set("message", sprintf(__('%s has been deleted', true), $reread_treasury['Treasury']['name']));
                    $this->render("success");
                    return;
                }
                $arr=[];
                $arr['primary_id']=$reread_treasury['Treasury']['id'];
                $arr['param1']=$reread_treasury['Treasury']['name'];
                $arr['param2']=$reread_treasury['Treasury']['type'];
                $arr['param3']=$reread_treasury['Treasury']['active'];
                $arr['param4']=$reread_treasury['Treasury']['is_primary'];
                $this->Treasury->add_actionline ( ACTION_DELETE_TREASURY , $arr) ;
                $this->izamFlashMessage(sprintf (__('%s has been deleted', TRUE) , $reread_treasury['Treasury']['name'] ) ,'success');
            }else {

                $flashMessage=__("This treasury has existing transactions or assigned to staff or assigned to cheque book or has loan, you can make it inactive", TRUE);
                $error400FlashMessage=__("This treasury has existing transactions, you can make it inactive.",true);

                if ($reread_treasury['Treasury']['type'] == Treasury::TYPE_BANK) {
                    $flashMessage=__("This bank has existing transactions or assigned to staff or assigned to cheque book or has loan, you can make it inactive", TRUE);
                    $error400FlashMessage=__("This bank has existing transactions, you can make it inactive.",true);
                }
                if(IS_REST) $this->cakeError("error400", ["message"=>$error400FlashMessage]);

                $this->izamFlashMessage($flashMessage,'danger');

            }

        } else {
            if(IS_REST) $this->cakeError("error400", ["message"=>__("You can't delete primary treasury",true)]);
            $this->izamFlashMessage(__("You can't delete primary treasury", TRUE),'danger');
        }
        $this->redirect(['action' => 'index']);
    }
    function owner_edit_transfer($id = null)
    {
        $this->loadModel ( 'TreasuryTransfer') ;
        $this->TreasuryTransfer->bindAttachmentRelation(EntityKeyTypesUtil::TREASURY_TRANSFER);
        $transfer = $this->TreasuryTransfer->findById ( $id ) ;
        $this->validate_open_day($transfer['Treasury']['transfer_date']);
        $this->set('title_for_layout',  sprintf(__('Edit Transfer %s', true), $transfer['TreasuryTransfer']['id']));
        if ( empty ( $transfer ) ) {
            $this->izamFlashMessage(sprintf(__("%s is not found", true), __('Money Transfer', true)), 'danger');
            $this->redirect  ( ['controller' => 'treasuries' , 'action' => 'index']);
        }else {

            if (!empty ( $this->data) ){
                $this->validate_open_day($this->data['Treasury']['transfer_date']);
                $transfer_id = $this->Treasury->transfer_balance (  $this->data['Treasury']['from_treasury_id'] ,$this->data['Treasury']['to_treasury_id'] , $this->data['Treasury']['balance'] , $this->data['Treasury']['from_currency_code'] ,  $this->data['Treasury']['to_currency_code'],  $this->data['Treasury']['currency_rate'] , $this->data['Treasury']['transfer_date'] , $this->data['Treasury']['notes'] , $id );
                if ( $transfer_id > 0 && !is_array ($transfer_id)  ) {

                    $attachments = $this->data['Treasury']['attachment'];
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);

                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save(EntityKeyTypesUtil::TREASURY_TRANSFER, $transfer_id, $imagesIds);
                    }

                    $this->izamFlashMessage(sprintf(__('%s has been saved', true), __('Money Transfer', true)), 'success');
                    $this->redirect('/owner/treasuries/view/'.$this->data['Treasury']['from_treasury_id']);
                }else if ( $transfer_id == -1 ){
                    $this->flashMessage(sprintf(__("%s is not found", true), __('Money Transfer', true)), 'Errormessage', 'secondaryMessage');

                }else if ( $transfer_id == -2 ){
                    $this->flashMessage(sprintf(__("Treasury doesn't have the desired amount", true), __('Transfer', true)), 'Errormessage', 'secondaryMessage');
                }else {
                    $this->Treasury->validationErrors = $transfer_id;
                    $this->flashMessage(sprintf(__("Didn't save transfer", true)), 'Errormessage', 'secondaryMessage');
                }
            }else {
                $transfer['TreasuryTransfer']['transfer_date'] = format_datetime ( $transfer['TreasuryTransfer']['transfer_date']) ;
                $this->data['Treasury'] = $transfer['TreasuryTransfer'] ;
                $this->data['Attachments'] = $transfer['Attachments'] ;
            }
            if(!empty($this->data['Treasury']['attachment'])){
                $filesId = explode(',',$this->data['Treasury']['attachment']);
                $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                $this->data['Attachments'] = $attachment;
            }
            $this->set ( 'action' , '/edit_transfer/');
            $this->set_view_data ( ) ;
            $this->render ( 'owner_transfer') ;
        }
    }
    function owner_transfer () {
        if (!check_permission([VIEW_ASSIGNED_TREASURY, CHANGE_DEFAULT_TREASURY, Edit_General_Settings])) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->Treasury->bindAttachmentRelation('treasury');
        if ( !empty ( $this->data ) ) {
            $this->validate_open_day($this->data['Treasury']['transfer_date']);
            $transfer_id = $this->Treasury->transfer_balance (  $this->data['Treasury']['from_treasury_id'] ,$this->data['Treasury']['to_treasury_id'] , $this->data['Treasury']['balance'] , $this->data['Treasury']['from_currency_code'] ,  $this->data['Treasury']['to_currency_code'],  $this->data['Treasury']['currency_rate'] , $this->data['Treasury']['transfer_date'] , $this->data['Treasury']['notes'] );
            if ( $transfer_id > 0 && !is_array ($transfer_id)  ) {

                $attachments = $this->data['Treasury']['attachment'];
                if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);

                $imagesIds = explode(',',$attachments);
                if(!empty($imagesIds))
                {
                    izam_resolve(AttachmentsService::class)->save(EntityKeyTypesUtil::TREASURY_TRANSFER, $transfer_id, $imagesIds);
                }

                $this->izamFlashMessage(sprintf(__('%s has been saved', true), __('Money Transfer', true)), 'success');
                $this->redirect('/owner/treasuries/view/'.$this->data['Treasury']['from_treasury_id']);
            }else if ( $transfer_id == -1 ){
                $this->flashMessage(sprintf(__("%s is not found", true), __('Money Transfer', true)), 'Errormessage', 'secondaryMessage');

            }else if ( $transfer_id == -2 ){
                $this->flashMessage(sprintf(__("Treasury doesn't have the desired amount", true), __('Transfer', true)), 'Errormessage', 'secondaryMessage');
            }else {
                if(!empty($this->data['Treasury']['attachment'])){
                    $filesId = explode(',',$this->data['Treasury']['attachment']);
                    $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                    $this->data['Attachments'] = $attachment;
                }
                $this->Treasury->validationErrors = $transfer_id;
                $this->flashMessage(sprintf(__("Didn't save transfer", true)), 'Errormessage', 'secondaryMessage');
            }
        }
        if (!empty ( $_GET['from_treasury_id']) ){
            $this->data['Treasury']['from_treasury_id'] = intval ($_GET['from_treasury_id'] );
        }
        $this->set ( 'action' , '/transfer/');
        $this->set_view_data ( ) ;

    }
    function owner_delete_transfer ( $id,$t = null ) {
        $this->Treasury->delete_transfer ( $id ) ;

        $this->izamFlashMessage(sprintf (__('%s has been deleted', TRUE) , __('Transfer',true)) , 'success');
        $this->redirect(array('action'=>'view',$t,'#transfers'));

    }
    private function set_view_data ( ) {
        $this->loadModel ( 'Currency' ) ;
        $this->loadModel ( 'ItemPermission' ) ;
        $depositTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY, ItemPermission::PERMISSION_DEPOSIT);
        $withdrawTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY, ItemPermission::PERMISSION_WITHDRAW);
        $treasuries = $depositTreasuries + $withdrawTreasuries;
        $this->set('treasuries', $treasuries);
        $this->set('depositTreasuries', $depositTreasuries);
        $this->set('withdrawTreasuries', $withdrawTreasuries);

        $all_currencies = $this->Currency->getCurrencyList ( );
        $t_cur = $this->Treasury->get_treasury_currencies ( null , $all_currencies);
        $t_cur=$this->checkBankHasCurrency($t_cur,$all_currencies);
        // dd($t_cur);
        $currency_balances = $this->Treasury->get_currency_balances ( $treasuries);
        foreach ($currency_balances as &$cc  )
        {
            foreach ( $cc as &$k ) {
                $k = $k * -1 ;
                unset ( $k ) ;
            }
            unset ( $cc ) ;
        }
 
        foreach ($currency_balances as $treasury_id => &$value) {
            $details = $this->treasury_detail_to_local_currency($treasury_id);
            if(isset($details['to_local'])){
                $value[$details['to_local']['currency']] = $details['to_local']['value'];
            }
        }
       
        $this->set ( 'currency_balances' , $currency_balances ) ;
        $this->set ( 'treasury_currencies' , $t_cur ) ;
        $this->set ( 'all_currencies' , $all_currencies ) ;
//        $this->set ( 'treasuries',$treasuries);
    }

    public function checkBankHasCurrency($t_cur,$all_currencies)
    {
        //if bank has currency send only bank currency and remove others
        foreach ($t_cur as $treasury_id => $currencies) {
            $treasury = $this->Treasury->findById ( $treasury_id);
            $currency_code=$treasury['Treasury']['currency_code'];

            if (!empty($currency_code) && $treasury['Treasury']['type'] = Treasury::TYPE_BANK) {
                $t_cur[$treasury_id]=[$currency_code=>$all_currencies[$currency_code]];
            }
        }
        return $t_cur;
    }
    
    function owner_mark_primary ( $treasury_id ) {
        if ( !check_permission(CHANGE_DEFAULT_TREASURY) ) {
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
        $row= $this->Treasury->find('first',array( 'conditions' => array('Treasury.id' => $treasury_id)));
        if(!$row){
            $this->izamFlashMessage(sprintf ( __('%s not found', TRUE) , __("Treasury" , true) ),'danger');
            $this->redirect(array('action'=>'index'));
        }
        if ($row['Treasury']['active'] != '1') {
            $this->handleSiteLimit(
                checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::TREASURIES_BANKS),
                ['action' => 'index', $treasury_id]
            );
        }
        $this->Treasury->updateAll( ['is_primary' => 0, 'applyBranchSave' => false ] );
        $this->Treasury->updateAll( ['is_primary' => 1, 'active' => 1,'applyBranchSave' => false ] , ['Treasury.id' => $treasury_id]);

        $re_read=$this->Treasury->findById($treasury_id);
        $arr['primary_id']=$re_read['Treasury']['id'];
        $arr['param1']=$re_read['Treasury']['name'];
        $arr['param2']=$re_read['Treasury']['type'];
        $arr['param3']=$re_read['Treasury']['active'];
        $arr['param4']=$re_read['Treasury']['is_primary'];
        $this->Treasury->add_actionline ( ACTION_EDIT_TREASURY , $arr) ;


        $this->izamFlashMessage(sprintf(__('The %s has been saved as primary', true), $re_read['Treasury']['name']), 'success');
        $ref_url = $_SERVER['HTTP_REFERER'];
        if ( empty ( $ref_url ) ) {
            $ref_url = ['controller' => 'treasuries' , 'action' => 'view' , $treasury_id];
        }
        $this->redirect($ref_url);
    }
    function owner_mark_active ( $treasury_id,$active ) {

        // Check site limitation
        if ($active) {
            $this->handleSiteLimit(
                checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::TREASURIES_BANKS),
                ['action' => 'view', $treasury_id]
            );
        }

        if ( !check_permission(CHANGE_DEFAULT_TREASURY) ) {
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
        $row= $this->Treasury->find('first',array('Treasury.id'=>$treasury_id));
        if(!$row){
            $this->izamFlashMessage(sprintf ( __('%s not found', TRUE) , __("Treasury" , true) ),'danger');
            $this->redirect(array('action'=>'index'));
        }
        $this->Treasury->id=$treasury_id;
        $this->Treasury->saveField('active',$active,false);
        if($active==0) {

            $this->Treasury->saveField('is_primary',0,false);
        }
        $re_read=$this->Treasury->findById($treasury_id);
        $arr['primary_id']=$re_read['Treasury']['id'];
        $arr['param1']=$re_read['Treasury']['name'];
        $arr['param2']=$re_read['Treasury']['type'];
        $arr['param3']=$re_read['Treasury']['active'];
        $arr['param4']=$re_read['Treasury']['is_primary'];
        $this->Treasury->add_actionline ( ACTION_EDIT_TREASURY , $arr) ;
        $ref_url = $_SERVER['HTTP_REFERER'];
        if ( empty ( $ref_url ) ) {
            $ref_url = ['controller' => 'treasuries' , 'action' => 'view' , $treasury_id];
        }
        $this->izamFlashMessage(sprintf(__t('%s Updated Successfully') ,$re_read['Treasury']['name']) , 'success' );

        $this->redirect($ref_url);
    }
    function owner_get_treasury_balance ( $treasury_id) {
        echo $this->Treasury->get_balance ( $treasury_id); die ;
    }

    function owner_statement ( $treasury_id) {
        if ( !in_array($treasury_id,array_keys ( $this->Treasury->get_list (0) ) ) ) {
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
        $this->loadModel('JournalAccount');
        $ja = $this->JournalAccount->find ( 'first' , ['recursive'=> -1 , 'fields' => 'id', 'conditions' => ['entity_type' => 'treasury', 'entity_id' => $treasury_id] ]);
        $account_id = $ja['JournalAccount']['id'];
        if($this->RequestHandler->isAjax()){
            $this->set("is_ajax",true);
        }

        $this->loadModel('JournalTransaction');
        $this->loadModel('Journal');
        $this->Journal->recursive = 1;
        $conditions = $this->_filter_params();
        debug($conditions);
        if(isset($_GET['ASC']) || isset($_GET['asc']) ) $order = "ASC";
        else $order = "DESC" ;
        debug($order);
//		$journals = $this->Journal->find('all',array('order' => array('Journal.created' =>)))
        $this->paginate =
            array(
                'order' => array(
                    'Journal.date' => $order,
                    'JournalTransaction.id' => $order,

                )
            );
        $conditions['JournalTransaction.journal_account_id'] = $account_id ;
        debug($_GET);
        if(isset($_GET['date_from']) && !empty($_GET['date_from']))
        {
            $date_from = urldecode($_GET['date_from']);
            $conditions[] = "Journal.date >= '$date_from'" ;

        }

        if(isset($_GET['date_to']) && !empty($_GET['date_to'])){
            $date_to = urldecode($_GET['date_to']);
            $conditions[] = "Journal.date <= '$date_to'" ;
        }
        debug($conditions);

        $transactions = $this->paginate('JournalTransaction', $conditions);

        if($order == "ASC"){

            $starting_balance = $this->Journal->calculate_account_balance_before($account_id,$transactions[0]);
            $transactions = $this->Journal->calculate_account_balance_after_transactions($transactions,$starting_balance,$order);

        }else{

            $starting_balance = $this->Journal->calculate_account_balance_before($account_id,end($transactions));
            $transactions = $this->Journal->calculate_account_balance_after_transactions($transactions,$starting_balance,$order);

        }
        $this->set('currency_code', $this->Journal->get_default_currency());
        $this->set('transactions', $transactions);


        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->set('current_staff_id',getAuthStaff('id'));
        $this->render ( '/journal_accounts/owner_list_transactions');
    }
    function owner_ajax_convert_currency ( $from , $to ) {
        App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
        $rate_val = CurrencyConverter::index($from, $to, date('Y-m-d'));
        if (empty ( $rate_val ) ) {
            die ( -1 );
        }else {
            die ( $rate_val  ) ;
        }

    }
    function update_staff_permissions(){
        $this->loadModel('Staff') ;
        $this->loadModel('ItemPermission') ;
        $staffMembers = $this->Staff->find('all');
        foreach ( $staffMembers as $s ) {
            $this->ItemPermission->create();
            $this->ItemPermission->save(['ItemPermission' => [
                'item_id' => $s['Staff']['default_treasury_id'],
                'item_type' => ItemPermission::ITEM_TYPE_TREASURY,
                'group_id' => $s['Staff']['id'],
                'group_type' => ItemPermission::GROUP_STAFF,
                'permission_level' => ItemPermission::PERMISSION_STOCK_UPDATING,
            ] ]);
        }
        die('Done');
    }

    private function setIndexData()
    {
        $pagination = new LengthAwarePaginator('list-transfers-pagination');
        $pagination->loadFromPaging($this->params['paging']['TreasuryTransfer']);
        $pagination->setPath($this->params['url']['url']);

        $builder = getEntityBuilder();
        $structure = $builder->buildEntity(EntityKeyTypesUtil::TREASURY_TRANSFER);
        $form = CreateFilterFormService::build($structure);

        $this->set('pageHead', TreasuryTransferComponent::PageHeaderButtons($pagination));
        $this->set('pagination', $pagination);
        $this->set('view_folder', 'transfers');
        $this->set('isGlobal', true);
        $iframe = isset($this->params['url']['iframe']) ? '&iframe=1' : '&';
        $this->set('reset_filters_url', $pagination->getPath() . '?reset=1'.$iframe);
        $this->set('links', TreasuryTransferComponent::pageButtonsWithoutData());
        $this->set('filtersForm', $form);
    }
     
    /*
        This function is used to get the total balance of a treasury in the local currency.
        On the transfer page, it was displaying only the treasury's currency, not converting all to the local currency.
    */
    function treasury_detail_to_local_currency($treasury_id)
    {
        $treasury = $this->Treasury->find ( 'first' , ['conditions' => ['Treasury.id' => $treasury_id ]]);
        $id = $treasury['Treasury']['id'];
        $treasury_permissions =  $this->Treasury->treasury_permissions($id);

        $this->set('treasury_permissions', $treasury_permissions);

        $convert_treasury_currency_to_local = $this->Treasury->convert_treasury_currency_to_local($treasury);
        $total_balance_val = $convert_treasury_currency_to_local['sum_local_cur'];
        $system_currency =  $this->Treasury->get_default_currency();
        $total_balance = [
            'treasury' => ['value' => $total_balance_val, 'currency' => $system_currency]
        ];

        if (!empty($treasury['Treasury']['currency_code']) && $treasury['Treasury']['type'] == Treasury::TYPE_BANK) {
            $rate_val = CurrencyConverter::index($system_currency, $treasury['Treasury']['currency_code']);


            $total_balance = [
                'treasury' => ['value' => $convert_treasury_currency_to_local['sum_treasury_currency_value'], 'currency' => $treasury['Treasury']['currency_code']],
                'to_local' => ['value' => $total_balance_val, 'currency' => $system_currency, 'rate_val' => $rate_val],

            ];
        }

        return $total_balance;
    }
}
