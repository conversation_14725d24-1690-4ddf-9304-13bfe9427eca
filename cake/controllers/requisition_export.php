<?php

use Izam\Attachment\Service\AttachmentsService;

		$this->loadModel ('RequisitionItem' );
		$this->set('conditions_key',$conditions_key);

		set_time_limit ( 3600);
		ini_set('memory_limit','20G');

		$order_list = array(
			'date_desc' => array('title' => __('Date', true) . ' (' . __('Recent First', true) . ')', 'fields' => 'Requisition.date DESC, Requisition.id DESC'),
			'date_asc' => array('title' => __('Date', true) . ' (' . __('Oldest First', true) . ')', 'fields' => 'Requisition.date, Requisition.id'),
			'no_desc' => array('title' => __('Number', true) . ' (' . __('Biggest First', true) . ')', 'fields' => 'Requisition.number DESC'),
			'no_asc' => array('title' => __('Number', true) . ' (' . __('Smallest First', true) . ')', 'fields' => 'Requisition.number')
		);
		
		
		//echo getPaymentStatuses ( 1 );
		$fields_list = array(
			'id' => array('title' => "ID", 'field' => 'Requisition.id'),
			'staff_id' => array('title' => "Staff ID", 'field' => 'Requisition.staff_id'),
			'staff_name'=> array ('title' =>'Staff Member Name', 'field' =>  ('Staff.name')),
			'store_id' => array('title' => "Store ID", 'field' => 'Requisition.store_id'),
			'to_store_id' => array('title' => "To Store ID", 'field' => 'Requisition.to_store_id'),
			'status' => array('title' => "Status", 'field' => 'Requisition.status'),
			'type' => array('title' => "Type", 'field' => 'Requisition.type'),
			'number' => array('title' => "Number", 'field' => 'Requisition.number'),
			'date' => array('title' => "Date", 'field' => 'Requisition.date'),
			'notes' => array('title' => "Notes", 'field' => 'Requisition.notes'),
			'order_id' => array('title' => "Order ID", 'field' => 'Requisition.order_id'),
			'order_type' => array('title' => "Order Type", 'field' => 'Requisition.order_type'),
			'order_number' => array('title' => "Order Number", 'field' => 'Requisition.order_number'),
			'currency_code' => array('title' => "Currency Code", 'field' => 'Requisition.currency_code'),
			'journal_account_id' => array('title' => "Journal Account ID", 'field' => 'JournalAccount.code'),
			'parent_requisition_id' => array('title' => "Parent Requisition ID", 'field' => 'Requisition.parent_requisition_id'),
			'work_order_id' => array('title' => "Work Order ID", 'field' => 'Requisition.work_order_id'),
			'created' => array('title' => "Created", 'field' => 'Requisition.created'),
			'modified' => array('title' => "Modified", 'field' => 'Requisition.modified'),
			'branch_id' => array('title' => "Branch ID", 'field' => 'Requisition.branch_id'),
		);
		
		$item_fields_list = array(
			'item' => array('title' => 'Item', 'field' => 'RequisitionItem.item'),
			'description' => array('title' => 'Description', 'field' => 'RequisitionItem.description'),
			'unit_price' => array('title' => 'Unit Price', 'field' => 'RequisitionItem.unit_price'),
			'quantity' => array('title' => 'Quantity', 'field' => 'RequisitionItem.quantity'),
			'quantity2' => array('title' => 'Quantity 2', 'field' => 'RequisitionItem.quantity2'),
			'requested_quantity' => array('title' => 'Requested Quantity', 'field' => 'RequisitionItem.requested_quantity'),
			'subtotal' => array('title' => 'Subtotal', 'field' => 'RequisitionItem.subtotal'),
			'product_id' => array('title' => 'Product ID', 'field' => 'RequisitionItem.product_id'),
			'display_order' => array('title' => 'Display Order', 'field' => 'RequisitionItem.display_order'),
			'created' => array('title' => 'Created Date', 'field' => 'RequisitionItem.created'),
			'modified' => array('title' => 'Modified Date', 'field' => 'RequisitionItem.modified'),
			'unit_name' => array('title' => 'Unit Name', 'field' => 'RequisitionItem.unit_name'),
			'unit_small_name' => array('title' => 'Unit Small Name', 'field' => 'RequisitionItem.unit_small_name'),
			'unit_factor' => array('title' => 'Unit Factor', 'field' => 'RequisitionItem.unit_factor'),
			'unit_factor_id' => array('title' => 'Unit Factor ID', 'field' => 'RequisitionItem.unit_factor_id'),
			'serials' => array('title' => 'Serials', 'field' => 'RequisitionItem.serials'),
			'extra_details' => array('title' => 'Extra Details', 'field' => 'RequisitionItem.extra_details'),
			'tracking_data' => array('title' => 'Tracking Data', 'field' => 'RequisitionItem.tracking_data'),
		);
		
		
		$conditions = [];

		if(!empty($conditions_key)) {
			$conditions = $this->Session->read($conditions_key);
		}


		if(isset($_POST['ids']) && $_POST['ids']){
			
		
			$this->set('ids',$_POST['ids']);
			$conditions['Requisition.id'] = !is_array($_POST['ids'])?explode(',', $_POST['ids']):$_POST['ids'];
		}
		
		if(!empty($this->params['url']['filter_ids'])){
			$conditions['Requisition.id'] = explode(',', $this->params['url']['filter_ids']);
		} else if(isset($_POST['filter_ids']) && $_POST['filter_ids'])
		{
			$conditions['Requisition.id'] = explode(',', $_POST['filter_ids']);
		}

		$item_columns_list = [];
		$itemColumnsNames = [];
		$itemColumnsLabels = [];

		$count = $this->Requisition->find('count', ['recursive' => -1, 'conditions' => $conditions]);

		if ($count > 5000) {
			$this->flashMessage(__("You can't export more than 5000 record, Please change filters to match limit.", TRUE));
			return $this->redirect("/owner/invoices");
		}

		$invoices_item_colums = $this->Requisition->find('all', ['recursive' => -1, 'conditions' => $conditions + ['Invoice.item_columns !=' => '']]);
		$itemColumns=[];
		$i=0;
		foreach ($invoices_item_colums as $item) {
			foreach (json_decode($item['Invoice']['item_columns'], true) as $key => $item_values) {
				if(is_array($item_values) && !in_array($item_values['label'],$itemColumnsLabels)) {
					$i++;
					$item_columns_list[$i] = $item_values['label'];
					$itemColumnsNames[$i] = $item_values['name'];
					$itemColumnsLabels[$i] = $item_values['label'];
				}
			}
		}

		$this->set('item_columns_list',$item_columns_list);

		if(empty($conditions))
		{
			
			$this->flashMessage(__("Invalid list to export", true), 'Errormessage');
			$this->redirect(array('action' => 'index'));
		}
		if($report_template_id!=false&&$quick_report)
		{
			$this->loadModel ('SavedReport' ) ;
			//Get the Data
			$sr = $this->SavedReport->findById($report_template_id);
			$this->data=json_decode ( $sr['SavedReport']['data'] ,true);
			if(empty($this->data))
			{
				$this->flashMessage(__("Couldn't find this report template", true), 'Errormessage');
				$this->redirect(array('action' => 'export',$conditions_key));
			}
		}
		
 		// Default post data
		$data = $this->data;


		/**
		 * If data is passed in the url, then we are exporting to a file
		 * We support both get and post in the form action
		 */

		if(!empty($this->params['url']['data']) && !empty($this->params['url']['data']['export_to'])){
			$data = $this->params['url']['data'];
		}
		
		if(!empty($data))
		{	

			$params['conditions']=$conditions;
			$params['order'] = 'Requisition.date DESC, Requisition.id DESC';
			if(!empty($data['order'])&&in_array($data['order'],array_keys($order_list)))
			{
				$params['order']=$order_list[$data['order']]['fields'];
			}
			$params ['fields'] = [] ;
            $this->loadModel('Product');
			//$params['limit'] = 10 ;
			$this->Requisition->bindModel(
				[
					'belongsTo' => [
						'JournalAccount' => ['className' => 'JournalAccount', 'foreignKey' => 'journal_account_id'],
					]
				],
				false
			);
			$unbind = [
				'InvoiceReminder',
				'EmailLog',
				'InvoiceDocument',
			];

			$this->Requisition->unbindModel(
				['hasMany' => $unbind]
			);
			$products = [];

			$count = $this->Requisition->find('count' , $params );

			if($count > 5000 ) {
				$this->flashMessage(__("You can't export more than 5000 record, Please change filters to match limit.", TRUE));
				return $this->redirect("/owner/invoices");
			}
			$this->Requisition->unbindModel(
				['hasMany' => $unbind]
			);
			$this->Requisition->recursive = 1;
			$alldata = $this->Requisition->find('all' , $params ) ;
			$ProductImage= GetObjectOrLoadModel('ProductImage');

			foreach($alldata as &$requisition){
			 foreach($requisition['RequisitionItem'] as &$item){
         
			 if(!empty($item['product_id'])){
				 if(!isset($products[$item['product_id']])) {
					 $product=$this->Product->find('first',array('recursive'=>-1,'conditions'=>['Product.id'=>$item['product_id']])) ;
					 $products[$item['product_id']] = $product;
				 }


				 $ProductImageRow=$ProductImage->find('first',['conditions'=>['ProductImage.product_id'=>$item['product_id'],'ProductImage.default'=>1]]);
				 $masterImage = resolve(AttachmentsService::class)->getDefault('product' ,$item['product_id']);
 
				 $productMasterImage = '';
				 if(!empty($ProductImageRow['ProductImage']['file_full_path'])){
					 $productMasterImage = ("https://".getCurrentSite('subdomain').'/'.$ProductImageRow['ProductImage']['file_full_path']);
				 }elseif(isset($masterImage[0]->files)){
					 $productMasterImage = "https://".getCurrentSite('subdomain')."/v2/owner/entity/files/preview/" . ($masterImage[0]->files->id);
				 }
				  
				 $item['product_image']= $productMasterImage ;
				 $item['barcode']=$products[$item['product_id']]['Product']['barcode'];
				 $item['product_code']=$products[$item['product_id']]['Product']['product_code']; 
				}
             }
            }

			//If they didn't select anything .
			if (empty ($data['item_fields_select']) && ($data['item_select'] == "items" || $data['item_select']=="item_columns") ){

				$data['item_fields_select'] = array_keys ($item_fields_list);
			}
			if (empty ($data['item_columns_select']) && $data['item_select']=="item_columns" ){
				$data['item_columns_select'] = array_keys ($item_columns_list);
			}
			if (empty ($data['fields_select']) ){
				$data['fields_select'] = array_keys ($fields_list);
			}

			$rows = [] ;
			foreach ( $data['fields_select'] as $v ){
				$rows[1][$fields_list[$v]['title']] .= __($fields_list[$v]['title'] , true  );
			}
			foreach ( $data['item_fields_select'] as $v ){
				$rows[1][ $item_fields_list[$v]['title'] ].= __($item_fields_list[$v]['title'] , true  );
			}
 
			foreach ( $data['item_columns_select'] as $v ){
				$rows[1][ $v ].= __($item_columns_list[$v] , true  );
			}
			//debug ( $rows ) ; 
			if ( isset ($_POST['save_as_template'] ) && $_POST['save_as_template'] =="1" )
			{
				$template_name = $_POST['template_name'] ; 
				$this->loadModel ('SavedReport' );
				$data['SavedReport'] = ['site_id' => getCurrentSite('id')  , 'title' => $template_name , 'type' => INVOICES_EXPORT , 'data' => json_encode($data) ];
				$this->SavedReport->set ($data );
				$this->SavedReport->save ($data);
			}

			//Putting data for CSV file
			$i = 2;
			App::import('Vendor', 'PlaceHolder');
			foreach ( $alldata as $d ) {
                $placeholders = [];
				if ( $d['Requisition']['staff_id'] == 0 ){
					$d['Staff']['name'] = getCurrentSite('first_name' )." ".getCurrentSite('last_name');
				} else {
					$d['Staff']['name'] = $d['Staff']['first_name']." ".$d['Staff']['last_name'];
				}

				foreach ($data['fields_select'] as $k => $v ){
					$field = explode ('.',  $fields_list[$v]['field'] );

					if ( $v =="payment_status" ){
						if($d['Invoice']['type'] == Invoice::Estimate) {
							$rows[$i][$fields_list[$v]['title']] = Invoice::getEstimateStatuses()[$d['Invoice']['payment_status']] ;
						} else {
							$rows[$i][$fields_list[$v]['title']] = Invoice::getPaymentStatuses()[$d['Invoice']['payment_status']] ;
						}

					}else if ( $v =="client_full_name" ){
						$rows[$i][$fields_list[$v]['title']] = ( $d['Client']['first_name']==""&& $d['Client']['last_name']=="")?  "{$d['Client']['business_name']} ({$d['Client']['first_name']} {$d['Client']['last_name']})" : $d['Client']['business_name'];
					}else if ( $v ==  "client_secondary_full_address" ){
						$rows[$i][$fields_list[$v]['title']] = "{$d['Client']['secondary_address1']}\n{$d['Client']['secondary_address2']}\n{$d['Client']['secondary_city']}, {$d['Client']['secondary_state']},{$d['Client']['secondary_postal_code']}\n{$d['Client']['secondary_country_code']}" ;  ;
					}else if ( $v ==  "client_full_address" ){
						$rows[$i][$fields_list[$v]['title']] = "{$d['Client']['address1']}\n{$d['Client']['address2']}\n{$d['Client']['city']}, {$d['Client']['state']},{$d['Client']['postal_code']}\n{$d['Client']['country_code']}" ;
					}else if ($fields_list[$v]['field']  == "custom" )  {
						foreach ( $d['InvoiceCustomField'] as $cc ){
							if( $cc['label'] == $fields_list[$v]['title'] ){
								$rows[$i][$fields_list[$v]['title']] = empty($placeholders) ? $cc['value'] : PlaceHolder::replace($cc['value'], array_keys($placeholders), array_values($placeholders),true);
							}
						}
					}else if ($v == "due_after" )  {
						$rows[$i][$fields_list[$v]['title']] = date('Y-m-d', strtotime('+' . $d['Invoice']['due_after'] . ' days', strtotime($d['Invoice']['date'])));
					}else {
						$rows[$i][$fields_list[$v]['title']] = $d[$field[0]][$field[1]];
					}

				}
				$temp = [];
				foreach ($rows[1] as $key => $value) {
					if (is_array($rows[$i]) && !array_key_exists($key, $rows[$i])) {
						$temp[$key] = '';
					}else{
						$temp[$key] = $rows[$i][$key];
					}
				}
				$rows[$i] = $temp;

				if (!empty ($data['item_fields_select']) && ($data['item_select'] == "items" )) {
					foreach ($d['RequisitionItem'] as $dinv_item) {
						$rows[$i] = $temp;
						foreach ($data['item_fields_select'] as $v) {

							$field = explode('.', $item_fields_list[$v]['field']);
							$model = $field[0];
							$rows[$i][$item_fields_list[$v]['title']] = $dinv_item[$field[1]]; 
						}
					 
						$i++;
					}

				}

				$i ++;
			}
			App::import('Vendor', 'csv');
			if(  $data['export_to'] == 'xml' ){
				$exporter = new ExportDataExcel('browser', 'data.xml');
			}else if(  $data['export_to'] == 'csv_semicolon' ){
				$exporter = new ExportDataCSV('browser', 'data.csv' , ";" );
			}else if(  $data['export_to']== 'excel' ){
				$exporter = new ExportDataExcel('browser', 'data.xls');
			}else {
				$exporter = new ExportDataCSV('browser', 'data.csv');
			} 
			$exporter->initialize();
			
			foreach ( $rows as $r ){
				$exporter->addRow($r );
			}
			
			$exporter->finalize();die ; 
			 
		}
		if ( $report_template_id ){
			$this->loadModel('SavedReport');
			$sr = $this->SavedReport->findById($report_template_id);
			$this->data=json_decode ( $sr['SavedReport']['data'] ,true);
			$this->set ( 'report_template_id' , $report_template_id );
		}
		$this->set ( 'order_list' , $order_list );
		$this->set ( 'fields_list' , $fields_list );
		$this->set ( 'item_fields_list' , $item_fields_list );

?>
