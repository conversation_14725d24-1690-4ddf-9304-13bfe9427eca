<?php

use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;

App::import('Sanitize');
App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
class RentalReservationOrderController extends AppController
{

    var $name = 'RentalReservationOrder';



    function owner_transaction_list($id)
    {
        $this->set('id', $id);
       
        $reservation_order_Type = InvoiceSourceTypesUtil::RESERVATION_ORDER;
        $reservation_order = $this->RentalReservationOrder->find('first', ['conditions'=>['RentalReservationOrder.id'=>$id]]);

        $owner = getAuthOwner();
        $client_id = $reservation_order['RentalReservationOrder']['client_id'];
        $client = $this->Client->find('first', array(
            'applyBranchFind' => false,
            'conditions' => array( 'Client.id'   => $client_id),
        ));;

        $placeholders = array_merge(PlaceHolder::site_place_holder() + PlaceHolder::datetime_place_holder(), PlaceHolder::client_place_holder($client), PlaceHolder::staff_place_holder($owner['staff_id']));
        $this->set('placeholders', $placeholders);

        $statement_header_html =  settings::getValue(0, "client_statement_header_html");
        $custom_header = PlaceHolder::replace($statement_header_html, array_keys($placeholders), $placeholders);
        $this->set('custom_header', $custom_header);

        $statement_footer_html =  settings::getValue(0, "statement_footer_html");
        $custom_footer = PlaceHolder::replace($statement_footer_html, array_keys($placeholders), $placeholders);
        $this->set('custom_footer', $custom_footer);

        $transcation_columns_setting = settings::getValue(0, "transcation_columns_setting");
        $this->set('transcation_columns_setting', $transcation_columns_setting);


        $this->set('client', $client);
        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            exit();
        }
     
        $this->layout = false;
        $starting_balance = array();
        $page = $this->params['url']['page']  ? intval($this->params['url']['page']) : 1;
        $date_from = $this->params['url']['date_from'] ?? null;
        $date_to = $this->params['url']['date_to'] ?? null;


        $per_page = 600;
        $invoice_more_query = "";
        $payment_more_query = "";
        $starting_limit = "";

        $filters = ['odd_spaces' => false, 'encode' => false, 'dollar' => false, 'carriage' => false, 'unicode' => false, 'escape' => false, 'backslash' => false];

        $date_from = Sanitize::clean($date_from, $filters);
        $date_to = Sanitize::clean($date_to, $filters);
        $page = Sanitize::clean($page, $filters);
        if (!empty($date_from)) {
            $invoice_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
            $before_invoice_more_query = " And date <'{$date_from}'";
            $payment_start_query = " And date >='{$date_from}' And date <='{$date_to}'";
            $payment_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
        }

        if ($page > 1) {
            $starting_limit =  " LIMIT " . ($per_page * ($page - 1));
        }

        $bare_query = "select 1 as my_order ,`date` as created,'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,due_after,'payment_method',currency_code,(SELECT IF(alter_description!='',alter_description,description) COLLATE utf8_unicode_ci FROM `journals` where entity_type=(CASE WHEN invoices.type=" . Invoice::Invoice . " THEN 'invoice' WHEN invoices.type=" . Invoice::Refund_Receipt . " THEN 'refund_receipt' WHEN invoices.type=" . Invoice::Credit_Note . " THEN 'credit_note' WHEN invoices.type=" . Invoice::DEBIT_NOTE . " THEN 'debit_note' END) and entity_id=invoices.id limit 1) as description from invoices where invoices.source_type = '$reservation_order_Type' AND invoices.source_id ='$id' and type in(" . implode(',', [Invoice::Invoice, Invoice::Credit_Note, Invoice::Refund_Receipt, Invoice::DEBIT_NOTE]) . ") and draft <> 1 %s
                 %s
                UNION select 2 as my_order,`date` as created,'payment',id,(select no from invoices where id=invoice_id),null,client_id,date,status,invoice_id,amount,null,payment_method,currency_code,null from invoice_payments where (invoice_id in(select id from invoices where invoices.source_type = '$reservation_order_Type' AND invoices.source_id ='$id' and type in(" . implode(',', [Invoice::Invoice, Invoice::Credit_Note, Invoice::DEBIT_NOTE]) . ") and draft <> 1) ) and payment_method <> 'client_credit' and payment_method <> 'starting_balance' and status=1  %s 
                UNION select 3 as my_order,`date` as created,'Refund Payment',id,(select no from invoices where id=invoice_id),'6-1',client_id,date,status,'refund',amount,null,payment_method,currency_code,null from invoice_payments where (invoice_id in(select id from invoices where invoices.source_type = '$reservation_order_Type' AND invoices.source_id ='$id' and type in(" . implode(',', [Invoice::Refund_Receipt]) . ") and draft <> 1)) %s ORDER BY `date` , my_order ASC,`created` ASC";

        $allt_starting = sprintf($bare_query, $payment_start_query, $payment_start_query, $payment_start_query, $payment_start_query, $payment_start_query);

        if ($page > 1) {

            $starting_invoice_with_payment = $this->Client->query($allt_starting . $starting_limit, false);
        }
        if (!empty($date_from)) {
            $before = sprintf($bare_query, $before_invoice_more_query, $before_invoice_more_query, $before_invoice_more_query, $before_invoice_more_query, $before_invoice_more_query);
            $before_starting_invoice_with_payment = $this->Client->query($before, false);
            if (isset($starting_invoice_with_payment)) {
                $starting_invoice_with_payment = array_merge($starting_invoice_with_payment, $before_starting_invoice_with_payment);
            } else {
                $starting_invoice_with_payment = $before_starting_invoice_with_payment;
            }
        }

        foreach ($starting_invoice_with_payment as $t) {
            $t = $t[0];
            if ($t['invoice'] == 'payment' or ($t['invoice'] == "Starting Balance" || ($t['invoice'] == 'invoice' and in_array($t['type'], array(Invoice::Refund_Receipt, Invoice::Credit_Note))))) {

                $t['summary_total'] = $t['summary_total'] * -1;

                $negation = 1;
            }
            if ($t['type'] == "6-1") {
                $t['summary_total'] = $t['summary_total'] * -1;
            }
            $starting_balance[$t['currency_code']] += $t['summary_total'];
        }

        $paginator_url = 'date_from=' . $date_from . '&';
        $paginator_url .= 'date_from=' . $date_to . '&';
        $this->set('paginator_url', $paginator_url);
        $this->set('starting_balance', $starting_balance);
        $allt = sprintf($bare_query, $payment_more_query, $invoice_more_query, $payment_more_query, $payment_more_query, $payment_start_query);

        $invoice_with_payment = $this->Client->query($allt . " LIMIT $per_page OFFSET " . ($per_page * ($page - 1)));
        $this->loadModel('Invoice');
        $this->loadModel('InvoiceLayout');
        $layoutsList = $this->InvoiceLayout->find('all');
        $layouts = [];
        foreach ($layoutsList as $layout) {
            $layouts[$layout['InvoiceLayout']['id']] = $layout;
        }

        $invoices = [];

        foreach ($invoice_with_payment as $t) {
            $t = $t[0];
            if (isset($_GET['show_details']) && $_GET['show_details'] == 'true') {
                if (in_array($t['type'], [Invoice::Invoice, Invoice::Refund_Receipt, Invoice::Estimate, Invoice::Credit_Note])) {
                    $invoices_ids[] = $t['id'];
                }
            }
        }
        $this->Invoice->applyBranch['onFind'] = false;
        $invoices_data = $this->Invoice->find('all', ['conditions' => ['Invoice.id' => $invoices_ids]]);
        $this->Invoice->applyBranch['onFind'] = true;
        foreach ($invoices_data as $invoice) {
            $invoices[$invoice['Invoice']['id']] = $this->Invoice->prepareForView($invoice);
        }
        $this->set('invoices', $invoices);
        $this->set('layouts', $layouts);
        $count = $this->Client->flat_query_results("select count(*)as cc from ( $allt )inv");
        $this->set('transacti   on_count', $count[0]['cc']);
        $this->set('current_page', $page);
        $this->set('count', ceil($count[0]['cc'] / $per_page));


        $this->set('InvoiceTypeList', Invoice::getInvoiceTypeList());
        $this->loadModel('InvoicePayment');
        $this->set('payment_methods', InvoicePayment::getAllPaymentMethods());
        $this->set('invoice_with_payment', $invoice_with_payment);
        $is_custom_header = settings::getValue(0, "statement_header_setting");
        $placeholders = array_merge(PlaceHolder::site_place_holder(), PlaceHolder::datetime_place_holder(), PlaceHolder::client_place_holder($client), PlaceHolder::staff_place_holder($owner['staff_id']));
        if ($is_custom_header) {
            $statement_header_html =  settings::getValue(0, "statement_header_html");
            $custom_header = PlaceHolder::replace($statement_header_html, array_keys($placeholders), $placeholders);
            $this->set('custom_header', $custom_header);
        }

        $this->set('is_custom_header', $is_custom_header);
        if ($this->RequestHandler->isAjax()) {
        $this->render('../rental_reservation_order/owner_transaction_list_element');
        }
    }
}
