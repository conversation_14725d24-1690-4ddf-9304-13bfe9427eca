<?php

use App\Helpers\UsersHelper;
use App\Services\Report\ReportFactory;
use App\Services\Report\ReportUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;

class TimeTrackingController extends AppController {

    var $name = 'TimeTracking';
    var $components = array('RequestHandler');
    var $timetracking_js_labels = array(
        'Add Time',
        'Add',
    );

    public function api_view($id = null) {
        $site = getAuthOwner();
        $SelectStaff = $this->Session->read('locked_staff') || $this->Session->read('locked_staff') == '0' ? $this->Session->read('locked_staff') : $site['staff_id'];
        if ( !check_permission(Edit_All_Timesheets) && !check_permission(Track_All_Staffs_Times) ){
            $SelectStaff = getAuthOwner ('staff_id' );
        }
        $time_tracking = $this->TimeTracking->find("first", ["conditions"=>['TimeTracking.id'=>$id, 'TimeTracking.staff_id'=>$SelectStaff]] );
        if(empty($time_tracking)) $this->cakeError('error404', array('message' => __('Time not found', true)));
        else $this->set('rest_item', $time_tracking);
        $this->set('rest_model_name', "TimeTracking");
        $this->render("view");
    }
    function owner_index($date = 'today') {

        if(!check_permission([PermissionUtil::Enter_Timesheet, PermissionUtil::Edit_All_Timesheets, PermissionUtil::Track_All_Staffs_Times])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this-> set ( 'is_ajax' , $this->RequestHandler->isAjax() );
        App::import('Vendor', 'settings');
        $this->loadModel('Project');
        $this->loadModel('TimeActivity');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->timetracking_js_labels);
        $site = getAuthOwner();
        debug(date_default_timezone_get());
        debug(date('Y-m-d'));
        $SelectStaff = $this->Session->read('locked_staff') || $this->Session->read('locked_staff') == '0' ? $this->Session->read('locked_staff') : $site['staff_id'];

        if ( !check_permission(Edit_All_Timesheets) && !check_permission(Track_All_Staffs_Times) ){
            $SelectStaff = getAuthOwner ('staff_id' );
        }
        debug ( $this->Session->read('locked_staff')  );

        $this->modelName = "TimeTracking";
        $this->TimeTracking->recursive = 0;
        $conditions = $this->_filter_params();

        //if ($site['staff_id'] != 0 and !check_permission(Track_All_Staffs_Times)) {
        //$conditions= $this->Session->read ('TimeTracking_Filter' );
        debug ( $conditions ) ;
        if(IS_REST) $_GET['list']=1;
        if ( !isset ($_GET['list']) ){
            $conditions['TimeTracking.staff_id'] = $SelectStaff;
        }else {

            if ( isset ( $conditions['TimeTracking.activity_id']) && trim($conditions['TimeTracking.activity_id']) != "" ){
                $pro = $this->TimeActivity->findByName ( $conditions['TimeTracking.activity_id'] );

                $conditions['TimeTracking.activity_id'] = $pro['TimeActivity']['id'] ;
            }
            if ( isset ( $conditions['TimeTracking.project_id']) && trim($conditions['TimeTracking.project_id']) != "" ){
                $pro = $this->Project->findByName ( $conditions['TimeTracking.project_id'] );

                $conditions['TimeTracking.project_id'] = $pro['Project']['id'] ;
            }
            if ( isset ( $conditions['TimeTracking.staff_id']) && $conditions['TimeTracking.staff_id'] > 0 ){
                $conditions['TimeTracking.staff_id'] = $conditions['TimeTracking.staff_id'] ;
                $SelectStaff= $conditions['TimeTracking.staff_id'];
            }
            $this->Session->write ('TimeTracking_Filter' ,  $this->_filter_params());

        }

        $this->set('selected_staff_id', $SelectStaff);
        $this->set('staff_id', $site['staff_id']);
        if ( isset ( $_GET['time_tracking_id']) &&$_GET['time_tracking_id'] > 0 ){
            $conditions['TimeTracking.id'] = intval ($_GET['time_tracking_id'] ) ;
        }
        if ( ifPluginActive(WorkOrderPlugin) ) {
            $this->loadModel ( 'WorkOrder' ) ;
            if ( !empty ( $_GET['add_work_order_id'] )  ) {
                $this->data['TimeTracking']['work_order_id'] =  $_GET['add_work_order_id'];
//                $_GET['work_order_id'] = $_GET['add_work_order_id'];
            }
            if ( !empty ( $_GET['work_order_id'] )  ) {
                $conditions['TimeTracking.work_order_id'] =  $_GET['work_order_id'];
            }
            $this->set ( 'work_order_ids' ,["" => '['.__("Choose from a list" , true ).']'] + $this->WorkOrder->get_work_orders (['WorkOrder.status'=>WorkOrder::STATUS_OPEN]  ) ) ;
        }
        $timetrackings = $this->paginate('TimeTracking', $conditions);
        $this->set('timetrackings', $timetrackings);
        // $this->set('title_for_layout',  __('Set Taxes', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');
        //warning suppress
        $this->crumbs[1]['title'] = $this->pageTitle ?? '';
        $this->crumbs[1]['url'] = '#';

        $this->set('content', $this->get_snippet('taxes'));
        /* $this->loadModel('TimeTrackingSetting');
          $tts_data = $this->TimeTrackingSetting->read(null, 1);
          $this->set('tts', $tts_data['TimeTrackingSetting']); */

        $projects = $this->Project->getList();

        foreach ( $projects as $k => $n ) {
            $projects[$n] = $projects[$k];
            unset (  $projects[$k]  );
        }
        //debug ($projects ) ;
        $this->set('projects', $projects);

        $activities = $this->TimeActivity->getList();
        foreach ( $activities as $k => $n ) {
            $activities[$n] = $activities[$k];
            unset (  $activities[$k]  );
        }
        //debug ( $activities ) ;
        $this->set('activities', $activities);
        $this->loadModel('Staff');
        $staffs = UsersHelper::getInstance()->getList();
        //unset($staffs[$site['staff_id']]);
        $this->set('staffs', $staffs);

        $is_action = ($this->action == __FUNCTION__);
        $this->TimeTracking->recursive = 0;
        if ($date == 'today') {
            $date = date('Y-m-d', strtotime('today'));
        }
        else
            $date = $this->TimeTracking->formatDate($date);


        $weekday = (int) date('w', strtotime($date));
        App::import('Vendor', 'settings');
        $week_day = settings::getValue ( 0  ,"week_day" ) ;
        //    debug(var_dump($week_day));
        if ( !isset ($week_day)  ){
            debug(123);
            $week_day= TIMETRACKING_FIRST_WEEKDAY ;
        }
        $local_weekday = $weekday - $week_day;// TimeTracking::FIRST_DAY_OF_WEEK;

        if ($local_weekday < 0)
            $local_weekday += 7;

        $startdate = strtotime($date) - $local_weekday * DAY;

        $total_hours = Set::combine($this->TimeTracking->find('all', array('conditions' => array('TimeTracking.staff_id' => $SelectStaff, 'TimeTracking.date BETWEEN ' . "'" . date('Y-m-d', $startdate) . "' AND '" . date('Y-m-d', $startdate + 6 * DAY) . "'"), 'fields' => array('SUM(time)', 'date'), 'group' => 'date')), '{n}.TimeTracking.date', '{n}.0.SUM(time)');
        $week_total = array('time' => $total = array_sum($total_hours), 'formatted' => TimeTracking::getTime($total));
        foreach ($total_hours as $key => $hour) {
            $total_hours[$key] = $hour;
        }
        if ( !isset ($_GET['list']) ){
            $timeSheets = $this->TimeTracking->findAllByStaffIdAndDate($SelectStaff, date('Y-m-d', strtotime($date)));

            debug ( getAuthOwner ('staff_id' ) );
        }else {
            $this->TimeTracking->order = "TimeTracking.date desc" ;
            if ( !check_permission ( View_All_Time_Sheets ) && !check_permission (Track_All_Staffs_Times)  ){
                $conditions['TimeTracking.staff_id'] = getAuthOwner ('staff_id' );
            }

            $timeSheets = $this->paginate('TimeTracking',$conditions);

            debug ($timeSheets ) ;
        }
        $this->set('timeEntries', $timeSheets);

        if ( !isset ( $_GET['list'] )){
            $total = 0;
            foreach ($timeSheets as $entry){ $total += $entry['TimeTracking']['time'];}
            $this->set ( 'total' , $total );
        }

        $hours_per_date = array_map(array($this->TimeTracking, 'getTime'), $total_hours);
        $this->set('hours_per_date', $hours_per_date);

        $getMonthEntries = $this->getMonthEntries(date('n', strtotime($date)), date('Y', strtotime($date)));
        $this->set('entries_json', $getMonthEntries);
        $this->set('startdate', $startdate);
        $this->set('date', $date);
        $this->set('weekday', $weekday);
        $this->set('week_total', $week_total);
        $this->set('week_total', $week_total);


        $this->set('locked_staff', $this->Session->read('locked_staff'));
        if(IS_REST){
            $this->set('rest_items', $timetrackings);
            $this->set('rest_model_name', "TimeTracking");
            $this->render("index");
        } else {
            $this->render('owner_index2');
        }
    }

    function owner_index2() {
        if(!check_permission([PermissionUtil::Enter_Timesheet, PermissionUtil::Edit_All_Timesheets, PermissionUtil::Track_All_Staffs_Times])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $site = getAuthOwner();
        debug ( 'test' ) ;
        $this->modelName = "TimeTracking";
        $this->TimeTracking->recursive = 0;
        $conditions = $this->_filter_params();
        if (!check_permission(Track_All_Staffs_Times)) {
            $conditions['TimeTracking.staff_id'] = $site['staff_id'];
        }
        $this->set('timetrackings', $this->paginate('TimeTracking', $conditions));
        // $this->set('title_for_layout',  __('Set Taxes', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';

        $this->set('content', $this->get_snippet('taxes'));
        /* $this->loadModel('TimeTrackingSetting');
          $tts_data = $this->TimeTrackingSetting->read(null, 1);
          $this->set('tts', $tts_data['TimeTrackingSetting']); */
        $this->loadModel('Project');
        $projects = $this->Project->find('list');
        $this->set('projects', $projects);
        $this->loadModel('TimeActivity');
        $activities = $this->TimeActivity->find('list');
        $this->set('activities', $activities);
        $this->loadModel('Staff');
        $staffs = $this->Staff->getList();
        unset($staffs[$site['staff_id']]);
        $this->set('staffs', $staffs);
    }
    function owner_get_projects_ajax () {
        $this->loadModel('Project');
        $projects = $this->Project->getList();
        foreach ( $projects as $k => $n ) {
            $projects[$n] = $projects[$k];
            unset (  $projects[$k]  );
        }
        die ( json_encode ($projects ));

    }
    function get_activities_ajax () {
        $this->loadModel('TimeActivity');
        $activities		= $this->TimeActivity->find('list');
        foreach ( $activities as $k => $n ) {
            $activities[$n] = $activities[$k];
            unset (  $activities[$k]  );
        }
        die ( json_encode ($activities ));
    }
    function owner_add($date = "") {
        if(!check_permission([PermissionUtil::Enter_Timesheet, PermissionUtil::Edit_All_Timesheets, PermissionUtil::Track_All_Staffs_Times])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $site = getAuthOwner();
        if (!check_permission(Edit_All_Timesheets) && ($owner['staff_id'] != $entry['TimeTracking']['staff_id'] || !check_permission(Enter_Timesheet))) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index', $date));
        }
        $this->loadModel('Project');
        $projects = $this->Project->find('list');
        $this->set('projects', $projects);
        $this->loadModel('TimeActivity');
        $activities = $this->TimeActivity->find('list');
        $this->set('activities', $activities);

        if (!empty($this->data)) {

//			die(debug ( $this->data  )) ;

            // Add branch_id to save for current branch also is filter for current branch
            $this->data['TimeTracking']['branch_id'] = getCurrentBranchID();

            $tt_data = $this->TimeTracking->saveTime($this->data , $date);
//			die(debug($tt_data));
            if ($tt_data['status']) {
                $tt_data = $tt_data['data'];
                $this->loadModel ( 'TimeActivity' );
                $this->loadModel ( 'Project' );
                $activity = $this->TimeActivity->find ( 'first' , ['conditions' => ['id' => $tt_data['TimeTracking']['activity_id']]]  )  ;
                $project  =  $this->Project->find ( 'first' , ['conditions' => ['id' => $tt_data['TimeTracking']['project_id']]]  )  ;
                $this->add_actionline($this->data['TimeTracking']['id'] ? ACTION_EDIT_TIME : ACTION_ADD_TIME, array('primary_id' => $tt_data['TimeTracking']['id'], 'param1' => $tt_data['TimeTracking']['time'], 'param2' => $tt_data['TimeTracking']['project_id'], 'param3' => $tt_data['TimeTracking']['activity_id'], 'param4' => $project['Project']['name'], 'param5' => $activity['TimeActivity']['name'], 'param6' => $tt_data['TimeTracking']['date']));
                if(IS_REST){
                    $this->set('id', $this->data['TimeTracking']['id']);
                    $this->render('created');
                    return;
                }
                if (!$this->RequestHandler->isAjax()) {
                    $this->flashMessage(sprintf(__('%s has been saved', true), __('Time', true)), 'Sucmessage');
                    $redirect = array('action' => 'index', $date);
                    if (!empty($this->params['url']['box'])) {
                        $redirect = array('action' => 'add', $date, '?' => array('box' => 1, 'success' => 1));
                    }
                    $this->redirect($redirect);
                } else {

                    echo json_encode(array('error' => 'false' , 'list' => isset ($_GET['list'] )?"1":"0"));
                    die();
                }
            } else {
                if(IS_REST) $this->cakeError("error400", ["message"=>$tt_data['message'], "validation_errors"=>$this->TimeTracking->validationErrors]);
                if ($this->RequestHandler->isAjax()) {
                    $errors = $this->TimeTracking->validationErrors;
                    echo json_encode(array('error' => 'true', 'errors' => $errors));
                    die();
                }
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Time', true)));
            }
        }
        /* $this->loadModel('TimeTrackingSetting');
          $tts_data = $this->TimeTrackingSetting->read(null, 1);
          $this->set('tts', $tts_data['TimeTrackingSetting']); */

        $this->loadModel('Staff');
        $staffs = UsersHelper::getInstance()->find('list');
        $this->set('staffs', $staffs);
        $this->set('site', $site);
        $this->set('title_for_layout',  __('Add Time', true));
    }

    function owner_edit($id = null) {
        if(!check_permission([PermissionUtil::Enter_Timesheet, PermissionUtil::Edit_All_Timesheets, PermissionUtil::Track_All_Staffs_Times])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        debug ( 'test' ) ;
        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');
        $entry = $this->TimeTracking->find(array('TimeTracking.id' => $id));
        if (!$entry) {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Time', true))));
            $this->flashMessage(sprintf(__("%s not found.", true), __('Time', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        if (!check_permission(Edit_All_Timesheets) && ($owner['staff_id'] != $entry['TimeTracking']['staff_id'] || !check_permission(Enter_Timesheet))) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

        $this->loadModel('Project');
        $projects = $this->Project->find('list');
        $this->loadModel('TimeActivity');
        $activities = $this->TimeActivity->find('list');
        if(IS_REST) $this->data['TimeTracking']['id'] = $id;

        if (!empty($this->data)) {

            $this->data['TimeTracking']['date'] = $this->TimeTracking->formatDate($this->data['TimeTracking']['date']);
            //debug ('nour' );
            $this->data ['TimeTracking']['project_id'] = $this->Project->findOrCreate ($this->data ['TimeTracking']['project_id'][0]);

            debug ( $this->data ['TimeTracking']['project_id']  ) ;
            $this->data ['TimeTracking']['project_id'] = $this->data ['TimeTracking']['project_id']['id'];
            $this->data ['TimeTracking']['activity_id'] = $this->TimeActivity->findOrCreate ($this->data ['TimeTracking']['activity_id'][0]);
            $this->data ['TimeTracking']['activity_id'] = $this->data ['TimeTracking']['activity_id']['id'];
            debug ( $this->data ) ;
            if ($this->TimeTracking->save($this->data)) {
                $this->add_actionline(ACTION_EDIT_TIME, array('primary_id' => $id, 'param1' => $this->data['TimeTracking']['time'], 'param2' => $this->data['TimeTracking']['project_id'], 'param3' => $this->data['TimeTracking']['activity_id'], 'param4' => $projects[$this->data['TimeTracking']['project_id']], 'param5' => $activities[$this->data['TimeTracking']['activity_id']], 'param6' => $this->data['TimeTracking']['date']));
                if(IS_REST){
                    $this->render('success');
                    return;
                }
                if (!$this->RequestHandler->isAjax())
                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Time', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index', $this->data['TimeTracking']['date']));
            } else {
                if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->TimeTracking->validationErrors]);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Time', true)));
            }
        } elseif ($this->RequestHandler->isAjax()) { //get ajax before re-submitting
            $entry['TimeTracking']['date'] = format_date($entry['TimeTracking']['date']);
            debug ($entry);
            unset ($entry['Staff']['password'] );
            //$entry['Staff']['password'] =
            die(json_encode($entry));
        } else {
            $this->data = $entry;
        }
        /* $this->loadModel('TimeTrackingSetting');
          $tts_data = $this->TimeTrackingSetting->read(null, 1);
          $this->set('tts', $tts_data['TimeTrackingSetting']); */

        $this->loadModel('Staff');
        UsersHelper::getInstance()->find('list');
        $staffs = $this->Staff->find('list');
        $this->set('staffs', $staffs);
        $this->set('projects', $projects);

        $this->set('activities', $activities);
        $this->set('title_for_layout',  h(sprintf(__('%s - TimeTracking', true), $project['TimeTracking']['id'])));

        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        if(!check_permission([PermissionUtil::Enter_Timesheet, PermissionUtil::Edit_All_Timesheets, PermissionUtil::Track_All_Staffs_Times])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        if (!empty($id)) {
            $entry = $this->TimeTracking->find(array('TimeTracking.id' => $id));
        }

        if (!$id && empty($_POST)) {
            if ($this->RequestHandler->isAjax())
            {
                $ajax_data['error'] = true;
                $ajax_data['errors']['date'] = sprintf(__('Invalid %s', true), __('Time', true));
            }
            if(IS_REST){
                $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Time', true))]);
            }else{
                $this->flashMessage(sprintf(__('Invalid %s', true), __('Time', true)));
                $this->redirect(array('action' => 'index'));
            }
        }
        $module_name = __('TimeTracking', true);
        if (is_array($id) && count($id) > 1) {
            $module_name = __('TimeTracking', true);
        }
        $conditions = array();
        $conditions['TimeTracking.id'] = $id;

        $TimeTrackings = $this->TimeTracking->find('all', array('conditions' => $conditions));

        $ajax_data = ['error' => false];
        foreach($TimeTrackings as $k => $TimeTracking){
            //$is_opened = $this->validate_open_day($TimeTracking['TimeTracking']['date'],false);
            $this->loadModel('ClosedPeriod');
            $is_opened = $this->ClosedPeriod->is_opened_date($TimeTracking['TimeTracking']['date']);

            if(!$is_opened){
                $message = $this->TimeTracking->get_closed_period_message(format_date($TimeTracking['TimeTracking']['date']));
                //if he reaches here then its an ajax request and the its a closed date
                $ajax_data['error'] = true;
                $ajax_data['errors']['date'] = $message;

            }
        }

        if (empty($TimeTrackings)) {
            if ($this->RequestHandler->isAjax())
            {
                $ajax_data['error'] = true;
                $ajax_data['errors']['date'] = sprintf(__('Invalid %s', true), __('Time', true));
            }
            if(IS_REST){
                $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), "Time")]);
            }else{
                $this->flashMessage(sprintf(__('%s not found', true), "Time"));
                $this->redirect($this->referer(array('action' => 'index'), true));
            }
        }

        if ((empty($_POST['ids']) && $this->RequestHandler->isAjax()) || IS_REST) {
            $_POST['ids'] = $id;
        }
        $conditions['TimeTracking.id'] = $_POST['ids'];
        if (!check_permission(Edit_All_Timesheets))
            $conditions['TimeTracking.staff_id'] = getAuthOwner('staff_id');

        if ($this->RequestHandler->isAjax() || IS_REST || (!empty($_POST['submit_btn']) && !empty($_POST['ids']))) {
            foreach ($TimeTrackings as $TimeTracking) {
                $this->add_actionline(ACTION_DELETE_TIME, array('primary_id' => $TimeTracking['TimeTracking']['id'], 'param1' => $TimeTracking['TimeTracking']['time'], 'param2' => $TimeTracking['TimeTracking']['project_id'], 'param3' => $TimeTracking['TimeTracking']['activity_id'], 'param6' => $TimeTracking['TimeTracking']['date']));
            }

            if($ajax_data['error'] == true)
            {
                die(json_encode($ajax_data,JSON_UNESCAPED_SLASHES));
            }
            if (($this->RequestHandler->isAjax() || IS_REST || $_POST['submit_btn'] == 'yes') && $this->TimeTracking->deleteAll(array('TimeTracking.id' => $_POST['ids']))) {
                if(IS_REST){
                    $this->set("message", sprintf(__('%s has been deleted', true), "Time"));
                    $this->render("success");
                    return;
                }else{
                    if (!$this->RequestHandler->isAjax())
                        $this->flashMessage(sprintf(__('%s has been deleted', true), "Time"), 'Sucmessage');
                    else{
                        die(json_encode($ajax_data,JSON_UNESCAPED_SLASHES));
                    }
                    if ( isset ($_GET['list'] ) ) $this->redirect(array('action' => 'index', "?" => "list"));
                    else $this->redirect(array('action' => 'index',$entry['TimeTracking']['date']));
                }

            } else {
                if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect(array('action' => 'index' , '?' => 'list'));
            }

        }

        $this->set('title_for_layout',  __('Delete TimeTracking', true));

        $this->set('TimeTrackings', $TimeTrackings);
        $this->set('module_name', $module_name);
    }

    function __report_data() {

        $owner = getAuthOwner();

        $url_params = $this->params['url'];
        $conditions = array();
        $formats = getDateFormats('std');
        $dateFormat = $formats[$owner['date_format']];

        if (isset($url_params['staff_id']) and $url_params['staff_id'] !== "") {
            $conditions['TimeTracking.staff_id'] = $url_params['staff_id'];
        }
        if (!check_permission(Track_All_Staffs_Times) && !check_permission(Track_All_Staffs_Times)) {
            $conditions['TimeTracking.staff_id'] = $owner['staff_id'];
        }
        if (isset($url_params['project_id']) and intval($url_params['project_id']) != 0) {
            $conditions['TimeTracking.project_id'] = $url_params['project_id'];
        }
        if (isset($url_params['activity_id']) and intval($url_params['activity_id']) != 0) {
            $conditions['TimeTracking.activity_id'] = $url_params['activity_id'];
        }
        if (isset($url_params['date_from']) and $url_params['date_from'] != "") {

            $date = $this->TimeTracking->formatDate($url_params['date_from']);
            $conditions['TimeTracking.date >= '] = $date;
        }

        if (isset($url_params['date_to']) and $url_params['date_to'] != "") {
            $date = $this->TimeTracking->formatDate($url_params['date_to']);
            $conditions['TimeTracking.date <= '] = $date . (' 23:59:59');
        }
        if (!empty ( $url_params['work_order_id'])) {
            $conditions['TimeTracking.work_order_id'] = intval( $url_params['work_order_id']);
        }


        $group = array();
        if (isset($url_params['group'])) {

            switch ($url_params['group']) {
                case 'staff':
                    $group = "TimeTracking.staff_id, date, project_id, activity_id, TimeTracking.id";
                    $order = 'Staff.name';
                    break;
                case 'project':
                    $group = "TimeTracking.project_id,TimeTracking.date, TimeTracking.id";
                    $order = 'Project.name';
                    break;
                case 'date':
                    $date_format = "TimeTracking.date";
                    $group = "$date_format, project_id, activity_id, staff_id, TimeTracking.id";
                    $order = 'date';
                    break;
                case 'daily':
                    $date_format = "TimeTracking.date";
                    $group = "$date_format, project_id , activity_id , staff_id, TimeTracking.id";
                    $order = 'date';
                    break;
                case 'weekly':
                    $date_format = "TimeTracking.date";
                    $group = "$date_format , project_id , activity_id , staff_id, TimeTracking.id";
                    $order = 'date';
                    break;
                case 'monthly':
                    $date_format = "DATE_FORMAT(TimeTracking.date,'%m-%Y')";
                    $group = "$date_format, project_id, activity_id, staff_id, TimeTracking.id";
                    $order = 'date';
                    break;
                case 'yearly':
                    $date_format = "DATE_FORMAT(TimeTracking.date,'%Y')";
                    $group = "$date_format, project_id, activity_id, staff_id, TimeTracking.id";
                    $order = 'date';
                    break;
                case 'activity':
                    $group = "TimeTracking.activity_id, date, project_id, TimeTracking.id ";
                    $order = 'Activity.name';
                    break;
            }
        }
        $order_by = array();
        if (!empty($order))
            $order_by[$order] = 'ASC';
        $order_by['TimeTracking.date'] = 'ASC';

        $Times = $this->TimeTracking->find('all', array('order' => $order_by, 'fields' => 'sum(TimeTracking.cost) as my_cost , (sum(TimeTracking.cost)/sum(TimeTracking.time)) as my_unit_price,TimeTracking.staff_id,TimeTracking.notes,activity_id,TimeTracking.date,TimeTracking.project_id,sum(TimeTracking.time) as times', 'group' => $group, 'conditions' => $conditions));
        foreach ($Times as $key => $time) {
            if ($Times[$key]['TimeTracking']['project_id'] == null or empty($Times[$key]['TimeTracking']['project_id'])) {
                $Times[$key]['TimeTracking']['project_id'] = 0;
            }

            if ($Times[$key]['TimeTracking']['staff_id'] == null or empty($Times[$key]['TimeTracking']['staff_id'])) {
                $Times[$key]['TimeTracking']['staff_id'] = 0;
            }

            if ($Times[$key]['TimeTracking']['activity_id'] == null or empty($Times[$key]['TimeTracking']['activity_id'])) {
                $Times[$key]['TimeTracking']['activity_id'] = 0;
            }
        }
        debug ( $Times ) ;
        $NewList = [];
        if (isset($url_params['group'])) {
            switch ($url_params['group']) {
                case 'date':
                case 'daily':

                    foreach ($Times as $Time) {
                        $Time['TimeTracking']['times'] = $Time[0]['times'];
                        $Time['TimeTracking']['total_cost'] += $Time[0]['my_cost'];
                        $NewList[$Time['TimeTracking']['date']][] = $Time['TimeTracking'];
                    }
                    $last_datetime = strtotime($this->TimeTracking->formatDate($url_params['date_to']) );
                    $current_datetime = strtotime($this->TimeTracking->formatDate($url_params['date_from']));
                    while ( $current_datetime <= $last_datetime ) {

                        if ( !isset ($NewList[date('Y-m-d' , $current_datetime)])){
//							$NewList[date('Y-m-d' , $current_datetime)] = array (['date' =>date('Y-m-d' , $current_datetime) ])  ; 
                        }
                        $current_datetime += 24*60*60;
                    }
                    uasort ($NewList, function ($a , $b){
                        $ac = strtotime ( $a[0]['date']  ) ;$bc = strtotime ($b[0]['date']  );
                        if ($ac == $bc) {
                            return 0;
                        }
                        return ($ac < $bc || empty ($ac) ) ? -1 : 1;
                    } );
                    return $NewList;
                    break;

                case 'weekly':
                    $last_datetime = strtotime($this->TimeTracking->formatDate($url_params['date_to']) );
                    $current_datetime = strtotime($this->TimeTracking->formatDate($url_params['date_from']));
                    while ( $current_datetime <= $last_datetime ) {

                        if ( !isset ($NewList[$this->get_week_start_new(date('Y-m-d' , $current_datetime))]) ){
                            $NewList[$this->get_week_start_new(date('Y-m-d' , $current_datetime))] = array (['date' =>date('Y-m-d' , $current_datetime) ])  ;
                        }
                        $current_datetime += 24*60*60;
                    }
                    foreach ($Times as $Time) {
                        $Time['TimeTracking']['times'] = $Time[0]['times'];
                        $Time['TimeTracking']['total_cost'] += $Time[0]['my_cost'];
                        //debug ($Time['TimeTracking']['date'] );

                        $NewList[$this->get_week_start_new($Time['TimeTracking']['date'])][] = $Time['TimeTracking'];
                    }


                    return $NewList;
                    break;
                case 'monthly':
                    foreach ($Times as $Time) {
                        $Time['TimeTracking']['times'] = $Time[0]['times'];
                        $Time['TimeTracking']['total_cost'] += $Time[0]['my_cost'];
                        $NewList[$this->date2monthyear($Time['TimeTracking']['date'])][] = $Time['TimeTracking'];
                    }
                    //debug($NewList);
                    return $NewList;
                    break;
                case 'yearly':
                    foreach ($Times as $Time) {
                        $Time['TimeTracking']['times'] = $Time[0]['times'];
                        $Time['TimeTracking']['total_cost'] += $Time[0]['my_cost'];
                        $NewList[$this->date2year($Time['TimeTracking']['date'])][] = $Time['TimeTracking'];
                    }
                    //debug($NewList);
                    return $NewList;
                    break;
                case 'project':

                    foreach ($Times as $Time) {
                        //$Time['TimeTracking']['my_unit_price'] =0;
                        $Time['TimeTracking']['times'] = $Time[0]['times'];
                        $Time['TimeTracking']['total_cost'] += $Time[0]['my_cost'];
                        debug ( $Time);
                        $NewList[$Time['TimeTracking']['project_id']][] = $Time['TimeTracking'];
                        $NewList[$Time['TimeTracking']['project_id']]['my_unit_price'] = $Time[0]['times'] !=0 ? $Time[0]['my_cost']/$Time[0]['times'] : false; // php8 fix
                    }
                    debug ( $NewList );
                    //die ;
                    //debug ( $Time );
                    return $NewList;
                    break;
                case 'staff':

                    foreach ($Times as $Time) {
                        $Time['TimeTracking']['times'] = $Time[0]['times'];
                        $Time['TimeTracking']['total_cost'] += $Time[0]['my_cost'];
                        //$Time['TimeTracking']['my_unit_price'] = $Time[0]['my_cost'];
                        $NewList[$Time['TimeTracking']['staff_id']][] = $Time['TimeTracking'];
                        $NewList[$Time['TimeTracking']['staff_id']]['my_unit_price'] = $Time[0]['times'] != 0 ? $Time[0]['my_cost']/$Time[0]['times'] : false;// php8 fix
                    }
                    return $NewList;
                    break;
                case 'activity':
                    foreach ($Times as $Time) {
                        $Time['TimeTracking']['times'] = $Time[0]['times'];
                        $Time['TimeTracking']['total_cost'] += $Time[0]['my_cost'];
                        $NewList[$Time['TimeTracking']['activity_id']][] = $Time['TimeTracking'];
                        $NewList[$Time['TimeTracking']['activity_id']]['my_unit_price'] = $Time[0]['times'] != 0 ? $Time[0]['my_cost']/$Time[0]['times'] : false;// php8 fix
                    }
                    return $NewList;
                    break;
            }
        }
    }

    function get_week_start($date) {
        $row = $this->TimeTracking->query("select adddate('{$date}', INTERVAL 1-DAYOFWEEK(curdate()) DAY) WeekStart");
        return ($row[0][0]['WeekStart']);
    }

    function get_week_start_new ( $date ) {
        $weekday = (int) date('w', strtotime($date));
        App::import('Vendor', 'settings');
        $week_day = settings::getValue ( 0  ,"week_day" ) ;
        if ( !$week_day  ){
            $week_day= TIMETRACKING_FIRST_WEEKDAY ;
        }
        $local_weekday = $weekday - $week_day;// TimeTracking::FIRST_DAY_OF_WEEK;
        if ($local_weekday < 0)
            $local_weekday += 7;

        $startdate = strtotime($date) - $local_weekday * DAY;
        return  date ('Y-m-d' , $startdate) ;
    }
    function date2monthyear($date) {
        return date("Y-m-01", strtotime($date));
    }

    function date2year($date) {
        return date("Y-01-01", strtotime($date));
    }

    function owner_report() {
        if(!check_permission([PermissionUtil::Enter_Timesheet, PermissionUtil::Edit_All_Timesheets, PermissionUtil::Track_All_Staffs_Times])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('Staff');
        if (ifPluginActive(WorkOrderPlugin)){
            $this->loadModel('WorkOrder');
            $this->set ( 'work_orders', $this->WorkOrder->get_work_orders ( ['status' => [WorkOrder::STATUS_OPEN ,WorkOrder::STATUS_CLOSED ] ] )) ;
        }
        $staffs = $this->Staff->getList(true);
        $this->set('staffs', $staffs);
        $this->loadModel('Project');
        $projects = $this->Project->find('list');
        $this->set('projects', $projects);
        $this->loadModel('TimeActivity');
        $activities = $this->TimeActivity->find('list');
        $this->set('activities', $activities);
        $this->set('owner', getAuthOwner());
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $this->set('date_str', ((empty($url_params['date_from']) ? '' : __('From', true) . ': ' . $url_params['date_from']) . ' ' . (empty($url_params['date_to']) ? '' : __('To', true) . ': ' . $url_params['date_to'])));

        $summary = $_GET['summary'] == "yes" ? __('Summary', true) : __('Detailed', true);
        $this->set('title_for_layout', $summary . ' ' . __('Time Report By ' . ucfirst($_GET['group']), true));

        if (isset($url_params['group'])) {
            $this->set('group_by', $url_params['group']);
            $this->set('data', $this->__report_data());
            $dates = array('daily', 'weekly', 'monthly', 'yearly');
            if (!empty($_GET['quick'])){
                $any_data = $this->TimeTracking->find('count');

                if($any_data==0){
                    die();
                }

                $this->layout = false;
            }

            if (in_array($url_params['group'], $dates)) {
                $this->render('owner_report_date');
            } else {
                $this->render('owner_report_' . $url_params['group']);
            }
        }

    }

    function owner_settings() {
        /* $this->loadModel('TimeTrackingSetting');

          $data = $this->TimeTrackingSetting->read(null, 1);
          if (!empty($this->data)) {
          $this->data['TimeTrackingSetting']['id'] = 1;
          if ($this->TimeTrackingSetting->save($this->data)) {
          $this->flashMessage(sprintf(__('%s  has been saved', true), __('Time Tracking Settings', true)), 'Sucmessage');
          $this->redirect(array('action' => 'settings'));
          } else {
          $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Time Tracking Settings', true)));
          }
          } else {
          $this->data = $data;
          } */
    }

    function getMonthEntries($month, $year) {
        $site = getAuthOwner();
        $SelectStaff = $this->Session->read('locked_staff') ? $this->Session->read('locked_staff') : $site['staff_id'];

        $json = json_encode(Set::combine(
            $this->TimeTracking->find('all', array('fields' => array('TimeTracking.id', 'TimeTracking.staff_id', 'TimeTracking.project_id', 'TimeTracking.activity_id', 'DAY(TimeTracking.date) as day', 'TimeTracking.time', 'TimeTracking.notes', 'Project.name', 'Activity.name'), 'conditions' => array('TimeTracking.staff_id' => $SelectStaff, 'YEAR(TimeTracking.date)' => $year, 'MONTH(TimeTracking.date)' => $month)))
            , '{n}.TimeTracking.id', '{n}', '{n}.0.day')
        );
        return $json;
    }

    function owner_getMonthEntries($month, $year) {
        $site = getAuthOwner();
        $this->layout = null;
        $SelectStaff = $this->Session->read('locked_staff') ? $this->Session->read('locked_staff') : $site['staff_id'];

        $json = json_encode(Set::combine(
            $this->TimeTracking->find('all', array('fields' => array('TimeTracking.id', 'TimeTracking.staff_id', 'TimeTracking.project_id', 'TimeTracking.activity_id', 'DAY(TimeTracking.date) as day', 'TimeTracking.time', 'TimeTracking.notes', 'Project.name', 'Activity.name'), 'conditions' => array('TimeTracking.staff_id' => $SelectStaff, 'YEAR(TimeTracking.date)' => $year, 'MONTH(TimeTracking.date)' => $month)))
            , '{n}.TimeTracking.id', '{n}', '{n}.0.day')
        );
        echo $json;
        die();
    }

    function owner_lock_staff($id=null) {
        if (!check_permission(Track_All_Staffs_Times)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index', $date));
        }
        $this->Session->write('locked_staff', $id . '');
        $this->redirect(array('action' => 'index'));
        die();
    }

    function owner_create_invoice() {
        if(!check_permission([PermissionUtil::Enter_Timesheet, PermissionUtil::Edit_All_Timesheets, PermissionUtil::Track_All_Staffs_Times])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $owner = getAuthOwner();
        $this->loadModel('Staff');
        $staffs = $this->Staff->getList();
        $this->set('staffs', $staffs);
        $this->loadModel('Project');
        $projects = $this->Project->find('list');
        $this->set('projects', $projects);
        $this->loadModel('TimeActivity');
        $activities = $this->TimeActivity->find('list');
        $this->set('activities', $activities);
        $this->set('owner', $owner);
        $url_params = $this->params['url'];

        //$conditions = $this->_filter_params();
        $conditions = $this->Session->read ( "TimeTracking_Filter") ;
        //print_r ($conditions );
        if ( isset ( $conditions['TimeTracking.activity_id']) && $conditions['TimeTracking.activity_id'] != "" ){
            $pro = $this->TimeActivity->findByName ( $conditions['TimeTracking.activity_id'] );

            $url_params['activity_id'] = $pro['TimeActivity']['id'] ;
        }
        if ( isset ( $conditions['TimeTracking.project_id']) && $conditions['TimeTracking.project_id'] != "" ){
            $pro = $this->Project->findByName ( $conditions['TimeTracking.project_id'] );

            $url_params['project_id'] = $pro['Project']['id'] ;
        }
        if ( isset ( $conditions['TimeTracking.staff_id']) && $conditions['TimeTracking.staff_id'] > 0 ){
            $url_params['staff_id'] = $conditions['TimeTracking.staff_id'] ;
        }
        //$this->set ( 'conditions'  , $conditions ) ;
        $this->set('url_params', $url_params);
        if (isset($url_params['create'])) {

            $Data = array();
            $items = array();
            $list = $this->__report_data();
            $item_names = array('project' => $projects, 'staff' => $staffs, 'activity' => $activities);
            $i = 0;
            debug ($list );
            foreach ($list as $key => $Times) {
//                $Data['InvoiceItem'][$i]['item'] = $url_params['group'] == 'date' ? format_date($key) : $item_names[$url_params['group']][$key];
//                $Data['InvoiceItem'][$i]['unit_price'] = $url_params['hour_rate']>0 ? $url_params['hour_rate'] : $Times['my_unit_price'];//$url_params['hour_rate'];
//                $Data['InvoiceItem'][$i]['quantity'] = 0;
//                $Data['InvoiceItem'][$i]['description'] = '';
                $descriptions = array();


                $total_cost =0 ;
                foreach ($Times as $kk => $time) {
                    if ( $kk === 'my_unit_price'){
                        continue ;
                    }
                    $total_cost += $time['total_cost'] ;

                    $Data['InvoiceItem'][$i]['quantity']+=$time['times'];
                    $currenct_description = (
                        (!empty($url_params['format_date']) ? format_date($time['date']) . ' ' : '') .
                        (!empty($url_params['format_activity']) && !empty($time['activity_id']) ? $activities[$time['activity_id']] . ' ' : '') .
                        (!empty($url_params['format_project']) && !empty($time['project_id']) ? __('on', true) . ' ' . $projects[$time['project_id']] . ' ' : '') .
                        (!empty($url_params['format_staff']) && !empty($time['staff_id']) ? __('by', true) . ' ' . $staffs[$time['staff_id']] : '') .
                        (!empty($url_params['format_note']) && !empty($time['notes']) ? "\r\n" . $time['notes'] . "\r\n" : '')
                    );
                    $descriptions[] = $currenct_description;
                    if ($url_params['format'] == 1) {
                        if ( $kk === 'my_unit_price'){
                            //$i++;
                            continue ;
                        }
                        $Data['InvoiceItem'][$i]['description'] = $descriptions[0];
                        $descriptions = array();
                        $Data['InvoiceItem'][$i]['quantity'] = round($Data['InvoiceItem'][$i]['quantity'], 4);

                        $Data['InvoiceItem'][$i]['item'] = $url_params['group'] == 'date' ? format_date($key) : $item_names[$url_params['group']][$key];
                        $Data['InvoiceItem'][$i]['unit_price'] = $url_params['hour_rate']>0?  $url_params['hour_rate']  : $Times['my_unit_price'] ;//$url_params['hour_rate'];
//                        $Data['InvoiceItem'][$i]['quantity'] = 0;
                        $Data['InvoiceItem'][$i]['description'] = $currenct_description;
                        $i++;
                    }

                }
                if ($url_params['format'] == 2) {
                    $Data['InvoiceItem'][$i]['item'] = $url_params['group'] == 'date' ? format_date($key) : $item_names[$url_params['group']][$key];
                    $Data['InvoiceItem'][$i]['description'] = implode("\r\n", array_unique($descriptions));
                    $Data['InvoiceItem'][$i]['quantity'] = round($Data['InvoiceItem'][$i]['quantity'], 4);
                    $Data['InvoiceItem'][$i]['unit_price'] =(empty ($url_params['hour_rate'] )?($Data['InvoiceItem'][$i]['quantity'] > 0) ? round($total_cost / $Data['InvoiceItem'][$i]['quantity'] ,2) : 0 : $url_params['hour_rate'] ) ;

                    $i++;
                }

            }

            $this->Session->write('invoice_type', 1);
            $this->Session->write('invoice_items', $Data);
            $this->redirect(array('controller' => 'invoices', 'action' => 'add'));
        }
    }

    function create_details($key, $data, $include, $projects, $activities, $staffs) {
        $new_detail = array();
        foreach ($data as $row) {
            $new_detail[$row['TimeTracking'][$key]][] = ((in_array('project_id', $include)) ? $projects[$row['TimeTracking']['project_id']] . ' ' : "") . (in_array('activity_id', $include) == 1 ? $activities[$row['TimeTracking']['activity_id']] . ' ' : "") . (in_array('staff_id', $include) ? $staffs[$row['TimeTracking']['staff_id']] : "");
        }
        return $new_detail;
    }
    /*function owner_ui_settings ( ) {

        App::import('Vendor', 'settings');
        if ( $this->data  ) {
            settings::setValue ("0" , "week_day" , $this->data ['week_day'] ) ;
        }
        /*$week_day = settings::getValue ( 0  ,"week_day" ) ;
        debug  ($week_day ) ;
        $this->set ( 'week_day' , $week_day );
        $this->redirect ('/owner/time_tracking/index') ;
    }*/
    function owner_ui_settings ( ) {

        if (!check_permission(Edit_General_Settings) && !check_permission (Add_New_Project)&& !check_permission (Edit_Delete_Projects) && !check_permission (Add_New_Time_Activity) && !check_permission (Edit_Delete_Time_Activity ) ) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        App::import('Vendor', 'settings');

        if ( $this->data  ) {
            if ( $this->data['time_to_expense'] ){
                settings::setValue ("0" , "time_to_expense" , 1 ) ;
            }else {
                settings::setValue ("0" , "time_to_expense" , 0 ) ;
            }
            settings::setValue ("0" , "week_day" , $this->data ['week_day'] ) ;
            settings::setValue ("0" , "admin_hourly_rate" , $this->data ['admin_hourly_rate'] ) ;


            $this->flashMessage(sprintf(__('%s has been saved', true), __('Time Tracking Settings', true)), 'Sucmessage');
            $this->redirect ( array('action' => 'ui_settings' ) );
        }
        $this->set ( 'week_day' ,  settings::getValue ( 0  ,"week_day" )?settings::getValue ( 0  ,"week_day" ):TIMETRACKING_FIRST_WEEKDAY  );
        $this->set ( 'time_to_expense' ,settings::getValue ( 0  ,"time_to_expense" )  );
        $this->set ( 'admin_hourly_rate' ,settings::getValue ( 0  ,"admin_hourly_rate" )  );

        //Projects
        $this->loadModel ( 'Project');
        $this->Project->recursive = 0;
        if ( isset ( $_GET['project_name'] ) ){
            $conditions['Project.name like'] = "%{$_GET['project_name']}%";
        }
        debug ( $conditions );
        $this->paginate['Project'] = array('conditions' => $conditions,
            'order' => array('Project.name asc'),
        );
        $this->set('projects', $this->paginate('Project'));

        //Time Activities
        $this->loadModel('TimeActivity' );
        $conditions = []   ;
        $this->TimeActivity->recursive = 0;
        if ( isset ( $_GET['activity_name'] ) ){
            $conditions['TimeActivity.name like'] = "%{$_GET['activity_name']}%";
        }
        $this->paginate['TimeActivity'] = array('conditions' => $conditions,
            'order' => array('TimeActivity.name asc'),
        );
        $this->set('timeactivities', $this->paginate('TimeActivity'));


    }

    public function beforeRender()
    {
        parent::beforeRender();
        if (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == ReportUtil::XLSX) { 
            $this->autoRender = false;
            $this->theme = false;
            $this->autoLayout = $this->layout = false;
            $reportAction = $this->params['action'];
            $reportName = str_replace('owner_', '', $reportAction);
            $element = ReportFactory::element($reportAction, 'time_tracking'); 
            ReportFactory::init(ReportUtil::XLSX)->export($reportName, $this, $element, $reportType ? $reportType : $this->params['pass'][0]);
        }
    }


}

?>
