<?php

use App\Modules\Template\DTO\Attachment;
use App\Modules\Template\DTO\SMTPConfig;
use App\Modules\Template\DTO\SMTPMessage;
use App\Repositories\LimitationRepository;
use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\Auth\AuthUserTypeUtil;
use Izam\Daftra\Common\Utils\BusinessNumberCommonFieldsUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Daftra\Portal\Models\EmailUpdate as PortalEmailUpdate;
use Izam\Daftra\Portal\Models\Feedback as PortalFeedback;
use Izam\Daftra\Portal\Models\Oauth as PortalOauth;
use Izam\Daftra\Portal\Models\SiteToken as PortalSiteToken;
use Izam\Daftra\Portal\Services\SitePluginService;
use Izam\Daftra\Portal\Services\SiteService;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use App\Services\SalesInvoicesReferrals\SalesInvoicesReferral;
use App\Services\UserSessionRedisService;
use Izam\Template\EmailSender\Services\Google;
use Izam\Template\EmailSender\Services\Outlook;
use Webmozart\Assert\Assert;

App::import('Vendor', 'settings');

/**
 * @property Country $Country
 * @property Language $Language
 * @property Currency $Currency
 * @property Timezone $Timezone
 * @property Plugin $Plugin
 * @property SitePlugin $SitePlugin
 */
class SitesController extends AppController
{
    public $uses = null;
    var $name = 'Sites';

    /**
     * @var Site
     */
    var $Site;
    var $components = array('Cookie', 'Lmail', 'Email', 'ApiRequests');
    var $helpers = array('Html');
    /* append js_lang_labels */
    var $invoice_js_labels = array(
        'Tax Identification',
        'Active',
        'Not Active',
    );

    function get_format_price_variables()
    {
        $data['currencies'] = (include APP . 'config' . DS . 'currencies.php');
        $data['number_formats'] = ($number_formats);
        $data['country_code'] = getCurrentSite('country_code');
        die(json_encode($data));
    }

    function owner_logout()
    {

        $this->Session->delete('STAFF');
        $this->Session->delete('OWNER');
        $this->Session->delete('CurrentPlugin');
        $this->Session->delete('CurrentSite');
        $this->Session->delete('timezone');
        $this->Session->delete('pagination-index');
        $this->Session->destroy();
        $this->Cookie->destroy();
        $this->redirect('/');
    }

    function owner_update_beta()
    {
        $this->redirect('/');
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            $this->redirect('/');
        }
        $this->loadModel('Site');
        $this->Site->recursive = -1;
        $currentSite = $this->Site->find(['id' => getCurrentSite('id')]);
        $newBetaState = ($currentSite['Site']['beta_version'] == '1' ? '0' : '1');
        if ($newBetaState == '1') {
            $config = json_decode($currentSite['Site']['db_config'], true);
            $sql_file = APP . 'other' . DS . 'beta.sql';
            if (file_exists($sql_file)) {
//                shell_exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} {$config['database']} < " . $sql_file);
                exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} {$config['database']} < " . $sql_file, $out, $status);
                if ($status != 0) {
                    notify_admin_fetal_error("owner_update_beta Error In Line 64");
                }
            }
            $this->add_actionline(ACTION_MOVE_TO_BETA);
        } else {
            $this->add_actionline(ACTION_MOVE_TO_LIVE);
        }
        $this->Site->id = $currentSite['Site']['id'];
        SiteService::updateById($currentSite['Site']['id'], ['beta_version' => $newBetaState]);
        $this->add_stats($newBetaState == '1' ? STATS_MOVED_TO_BETA_ACTION : STATS_MOVED_TO_LIVE_ACTION);
        $this->Session->destroy();
        $this->redirect('/');
    }

    public function owner_my_accounts()
    {
        if (!isOwner()) {
            $this->flashMessage(__('Please contact the site admin to upgrade or renew your account', true));
            $this->Redirect('/');
        }
        $token = $this->__createToken();

        $this->redirect("https://" . Portal_Full_Name . getSiteLangUrl("/sites/dashboard/" . getCurrentSite('id') . '?token=' . $token));
    }

    function owner_save_site_details()
    {
        $this->loadModel('Site');
        $site = getAuthOwner();
        if (!empty($this->data)) {
            $this->data['Site']['id'] = $site['id'];
            $this->data['Site']['colors_set'] = 1;

            $fieldlist = array('id', 'theme_color', 'font_color', 'colors_set');
            if (!empty($this->data['Site']['site_logo']) && $this->data['Site']['site_logo'] != $site['site_logo']) {
                $fieldlist[] = 'site_logo';
            }
            $applyBrach = ifPluginActive(BranchesPlugin);
            settings::setValue(0, 'enable_simple_side_menu', $this->data['Site']['enable_simple_side_menu'], $applyBrach);
            unset($this->data['Site']['enable_simple_side_menu']);
            if (SiteService::updateById($site['id'], $this->data['Site'], ['callbacks' => false, 'validate' => false, 'fieldList' => $fieldlist])) {
                //$this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
//                if (!empty($this->data['Site']['site_logo']) && $this->data['Site']['site_logo'] != $site['site_logo']) {
//                    $logoFile = WWW_ROOT . DS . 'files' . DS . 'images' . DS . 'tmp-logos' . DS . $this->data['Site']['site_logo'];
//                    $destDir = WWW_ROOT . DS . 'files' . DS . 'images' . DS . 'site-logos' . DS;
//
//                    if (file_exists($logoFile)) {
//                        rename($logoFile, $destDir . basename($logoFile));
//                    }
//
//                    if ($site['site_logo'] && file_exists($destDir . $site['site_logo'])) {
//                        unlink($destDir . $site['site_logo']);
//                    }
//                }

                $this->Site->reload_session();
                echo true;
            } else {
                //$this->flashMessage(__('Could not update your settings. Please, try again.', true));
                echo false;
            }
        }
        exit();
    }

    /*
     * this method will edit the plugins for the industry go check portal tables (Pluins, site_plugins)
     */

    function owner_update_plugin($id, $status, $recursiveCall = false)
    {
        $this->loadModel('Site');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->invoice_js_labels);
        return $this->Site->owner_update_plugin($id, $status, $recursiveCall);
    }

    function api_update_plugin($id, $status, $recursiveCall = false)
    {
        return $this->owner_update_plugin($id, $status, $recursiveCall);
    }

    function owner_plugin_manager($page_id = 2)
    {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $owner = getAuthOwner();

        if ($owner['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        if ($page_id == 1) {
            $page_id = 2;
            $plugin_home = true;
            $this->set('plugin_home', $plugin_home);
        }
        $db_config = new DATABASE_CONFIG();

        //if(!isset($db_config->{$db})) throw new Exception ("DB source not found", 500);
        $config = $db_config->default;

        $site = getCurrentSite();
        //$db=  json_decode($site['db_config'],true);


        //load the data base models to retrieve data from
        $this->loadModel('SitePlugin');
        $this->loadModel('Plugin');
        //$sticky_list = $this->Plugin->find('list', array('conditions' => array('active' => 1, 'sticky' => 1)));
        $this->loadModel('Site');
        if (!empty($this->data)) {


            //$CurrentPlugin = $this->SitePlugin->find('all', array('conditions' => array('SitePlugin.site_id' => $site['id'])));
            // echo "<pre>";
            // print_r($CurrentPlugin);
            // die();
            if ($page_id == 3) {
                $this->Site->id = $site['id'];
                SiteService::updateById($site['id'], ['industry_id' => $this->data['industry_id']], ['validate' => false]);
//                $this->Site->saveField('version', 2, false);
                $this->Site->reload_session();
            }

            if ($plugin_home) {
                if (!empty($this->data['client_type'])) {//
                    settings::setValue(ClientsPlugin, 'client_type', $this->data['client_type']);
                }
                if (!empty($this->data['invoicing_method'])) {//
                    settings::setValue(InvoicesPlugin, 'invoicing_method', $this->data['invoicing_method']);
                }


                if (!empty($this->data['sold_item_type'])) {//
                    settings::setValue(InvoicesPlugin, 'sold_item_type', $this->data['sold_item_type']);
                }

                $this->Site->id = $site['id'];
//                $this->Site->saveField('version',2,false);

                $this->Site->reload_session();
            }
            if ($page_id == 1) {


                // $this->SitePlugin->updateAll(array('SitePlugin.active' => 0), array('SitePlugin.site_id <=' => $site['id']));

//            foreach ($CurrentPlugin as $key=>$Pluginrow) {
//                $installed_p_list[$Pluginrow['SitePlugin']['plugin_id']]=$Pluginrow['SitePlugin']['installed'];
//                $plist[$Pluginrow['SitePlugin']['plugin_id']] = $Pluginrow['SitePlugin']['id'];
//            }

                foreach ($this->data['Plugin'] as $list) {
                    $oplugin = $this->Plugin->read(null, $list);
                    if (!$oplugin) {
                        continue;
                    }
                    $save = array();
                    $save['SitePlugin']['plugin_id'] = $list;
                    $save['SitePlugin']['site_id'] = $site['id'];
                    $save['SitePlugin']['active'] = 1;
                    $save['SitePlugin']['installed'] = 1;
                    if ($oplugin['Plugin']['is_external'] == "1" && !in_array($plist, $installed_p_list)) {
                        $sql_file = APP . 'other' . DS . 'plugin' . DS . strtolower($oplugin['Plugin']['plugin_key']) . '.sql';
                        if (file_exists($sql_file)) {
//                        shell_exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} {$config['database']} < " . $sql_file);
                            exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} {$config['database']} < " . $sql_file, $out, $status);
                            if ($status != 0) {
                                notify_admin_fetal_error(__FUNCTION__ . " Error In Line " . __LINE__);
                            }
                        }
                        if (method_exists($this->Plugin, 'plugin_callback_' . $save['SitePlugin']['plugin_id'])) {
                            $this->Plugin->{'plugin_callback_' . $save['SitePlugin']['plugin_id']}();
                        }
                    }
                    if (!in_array($list, array_keys($plist))) {
                        SitePluginService::create($save['SitePlugin']);
                    } else {
                        SitePluginService::updateById($plist[$list], $save['SitePlugin']);
                    }
                    unset($save);
                }

                $InstalledPlugin = array_keys($this->SitePlugin->find('list', array('fields' => 'plugin_id,plugin_id', 'conditions' => array('SitePlugin.active' => 1, 'SitePlugin.site_id' => $site['id']))));

                $PluginList = array_keys($this->SitePlugin->Plugin->find('list', array('fields' => 'id,id', 'conditions' => array('sticky' => 0, 'active' => 1))));
                $result = array_diff($PluginList, $InstalledPlugin);


                $p = $this->Plugin->find('list', array('conditions' => array('id' => $result)));
                $p2 = $this->Plugin->find('list', array('conditions' => array('id' => $InstalledPlugin)));

                $this->add_actionline(ACTION_UPDATED_PLUGIN, array('param5' => implode(',', $p2), 'param4' => implode(',', $p)));
                $tempFileName = APP . "tmp" . DS . "cache" . DS . "models" . DS . "cake_model_default_" . $config['database'] . "_list";
                unlink($tempFileName);
                $this->Session->delete('CurrentPlugin');
                Cache::delete('current_plugin_' . $site['id']);
                \Izam\Daftra\Cache\PortalCache::clear();
                getCurrentPlugin();
                delete_menus();
            }
            if ($page_id == 1) {
                //$this->flashMessage(__('You have completed your registration. You can now start invoicing.', true), 'Sucmessage');

                if (false && $this->Cookie->read('invoice_template_invoice')) {
                    $this->loadModel('InvoiceLayoutCustomField');
                    $this->loadModel('InvoiceLayout');
                    $this->loadModel('InvoiceLayoutTpl');
                    $invoice_tpl = $this->InvoiceLayoutTpl->read(null, 1);
                    //  debug($invoice_tpl);
                    //   print_r($invoice_tpl);
                    $json_invoice_template = json_decode($_COOKIE['CakeCookie']['invoice_template_invoice'], true);
                    $data['InvoiceLayout']['label_invoice_no'] = $json_invoice_template['label_invoice_no'];
                    $data['InvoiceLayout']['label_date'] = $json_invoice_template['label_date'];
                    $data['InvoiceLayout']['label_description'] = $json_invoice_template['label_description'];
                    $data['InvoiceLayout']['label_quantity'] = $json_invoice_template['label_qty'];
                    $data['InvoiceLayout']['label_unit_price'] = $json_invoice_template['label_unit_price'];
                    $data['InvoiceLayout']['label_tax1'] = $json_invoice_template['label_tax_1'];
                    $data['InvoiceLayout']['label_tax2'] = $json_invoice_template['label_tax_2'];
                    $data['InvoiceLayout']['label_subtotal'] = $json_invoice_template['label_subtotal'];
                    $data['InvoiceLayout']['label_item_total'] = $json_invoice_template['label_total'];
                    $data['InvoiceLayout']['label_discount'] = $json_invoice_template['label_discount'];
                    $data['InvoiceLayout']['name'] = $json_invoice_template['extra'];
                    $data['InvoiceLayout']['invoice_title'] = $json_invoice_template['extra'];
                    $data['InvoiceLayout']['html'] = $invoice_tpl['InvoiceLayout']['html'];
                    $data['InvoiceLayout']['footer'] = $invoice_tpl['InvoiceLayout']['footer'];
                    $data['InvoiceLayout']['items_list'] = $invoice_tpl['InvoiceLayout']['items_list'];
                    $data['InvoiceLayout']['custom_fields'] = $invoice_tpl['InvoiceLayout']['custom_fields'];
                    $data['InvoiceLayout']['logo'] = $invoice_tpl['InvoiceLayout']['logo'];
                    $data['InvoiceLayout']['client_info'] = $invoice_tpl['InvoiceLayout']['client_info'];
                    $data['InvoiceLayout']['ship_info'] = $invoice_tpl['InvoiceLayout']['ship_info'];
                    $data['InvoiceLayout']['business_info'] = $invoice_tpl['InvoiceLayout']['business_info'];
                    if (!empty($json_invoice_template['field1_value'])) {

                        $data['InvoiceLayout']['field1'] = $json_invoice_template['label_field1'];
                    }
                    if (!empty($json_invoice_template['field2_value'])) {
                        $data['InvoiceLayout']['field2'] = $json_invoice_template['label_field2'];
                    }
                    if (!empty($json_invoice_template['field3_value'])) {
                        $data['InvoiceLayout']['field3'] = $json_invoice_template['label_field3'];
                    }
                    if (!empty($json_invoice_template['field4_value'])) {
                        $data['InvoiceLayout']['field4'] = $json_invoice_template['label_field4'];
                    }
                    if (!empty($json_invoice_template['field5_value'])) {
                        $data['InvoiceLayout']['field5'] = $json_invoice_template['label_field5'];
                    }
                    $data['InvoiceLayout']['default_timesheet'] = 1;
                    $data['InvoiceLayout']['default_estimate'] = 1;
                    $data['InvoiceLayout']['default'] = 1;
                    $data['InvoiceLayout']['template_id'] = 1;
                    $save = $this->InvoiceLayout->save($data);
                    $layoutid = $this->InvoiceLayout->getLastInsertID();
                    $this->InvoiceLayout->updateAll(array('InvoiceLayout.default_timesheet' => 0), array('InvoiceLayout.id !=' => $layoutid));
                    $this->InvoiceLayout->updateAll(array('InvoiceLayout.default_estimate' => 0), array('InvoiceLayout.id !=' => $layoutid));
                    $this->InvoiceLayout->updateAll(array('InvoiceLayout.default' => 0), array('InvoiceLayout.id !=' => $layoutid));

                    if (!empty($json_invoice_template['field1_value'])) {
                        $CF['InvoiceLayoutCustomField']['invoice_layout_id'] = $layoutid;
                        $CF['InvoiceLayoutCustomField']['label'] = $json_invoice_template['label_field1'];
                        $CF['InvoiceLayoutCustomField']['value'] = $json_invoice_template['field1_value'];
                        $this->InvoiceLayoutCustomField->create();
                        $this->InvoiceLayoutCustomField->save($CF);
                    }

                    if (!empty($json_invoice_template['field2_value'])) {
                        $CF['InvoiceLayoutCustomField']['invoice_layout_id'] = $layoutid;
                        $CF['InvoiceLayoutCustomField']['label'] = $json_invoice_template['label_field2'];
                        $CF['InvoiceLayoutCustomField']['value'] = $json_invoice_template['field2_value'];
                        $this->InvoiceLayoutCustomField->create();
                        $this->InvoiceLayoutCustomField->save($CF);
                    }

                    if (!empty($json_invoice_template['field3_value'])) {
                        $CF['InvoiceLayoutCustomField']['invoice_layout_id'] = $layoutid;
                        $CF['InvoiceLayoutCustomField']['label'] = $json_invoice_template['label_field3'];
                        $CF['InvoiceLayoutCustomField']['value'] = $json_invoice_template['field3_value'];
                        $this->InvoiceLayoutCustomField->create();
                        $this->InvoiceLayoutCustomField->save($CF);
                    }

                    if (!empty($json_invoice_template['field4_value'])) {
                        $CF['InvoiceLayoutCustomField']['invoice_layout_id'] = $layoutid;
                        $CF['InvoiceLayoutCustomField']['label'] = $json_invoice_template['label_field4'];
                        $CF['InvoiceLayoutCustomField']['value'] = $json_invoice_template['field4_value'];
                        $this->InvoiceLayoutCustomField->create();
                        $this->InvoiceLayoutCustomField->save($CF);
                    }

                    if (!empty($json_invoice_template['field5_value'])) {
                        $CF['InvoiceLayoutCustomField']['invoice_layout_id'] = $layoutid;
                        $CF['InvoiceLayoutCustomField']['label'] = $json_invoice_template['label_field5'];
                        $CF['InvoiceLayoutCustomField']['value'] = $json_invoice_template['field5_value'];
                        $this->InvoiceLayoutCustomField->create();
                        $this->InvoiceLayoutCustomField->save($CF);
                    }
                }


                if ($this->Cookie->read('final_url')) {

                    $final_url = $this->Cookie->read('final_url');
                    $this->Cookie->del('final_url');
                    $this->redirect($final_url);
                }

                $this->redirect('/?welcome=1');
            } else {

                $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
                $this->redirect('/?welcome=1');
            }
        }
        $oldRecursive = $this->Plugin->recursive;
        $this->Plugin->recursive = 2;
        $conditions['AND'] = array('Plugin.sticky' => 0, 'Plugin.active' => 1);
        if (isset($_GET['show_all']) && $_GET['show_all'] == '2') {
            $conditions['AND'] = array('Plugin.sticky' => 0);
            $this->Plugin->recursive = $oldRecursive;
        }
        $conditions['AND'] += [
            'OR' => [
                ['beta_version' => $site['beta_version']],
                ['beta_version' => null]
            ]
        ];
        $this->loadModel('User');
        $userAgency = $this->User->find('first', ['conditions' => ['User.id' => $site['user_id']]]);
        if (in_array($userAgency['User']['agency_id'], [161, 158, 137])) {
            $conditions['AND'] += [
                ['Plugin.id !=' => WebsiteFrontPlugin]
            ];
        }
        $allPlugins = $this->Plugin->find('all', array('order' => 'Plugin.display_order asc', 'conditions' => $conditions));
        if (!(isset($_GET['show_all']) && $_GET['show_all'] == '2' && !empty($site['system_industry_id']))) {
            foreach ($allPlugins as $pluginIndex => $plugin) {
                $willBeAdded = empty($plugin['PluginIndustry']);
                foreach ($plugin['PluginIndustry'] as $pluginIndustry) {
                    if ($site['system_industry_id'] == $pluginIndustry['system_industry_id']) {
                        $willBeAdded = true;
                        break;
                    }
                }
                if (!$willBeAdded)
                    unset($allPlugins[$pluginIndex]);
                unset($allPlugins[$pluginIndex]['PluginIndustry']);
            }
        }
        $this->set('main_plugins', $allPlugins);
        $this->set('plugins', $this->SitePlugin->find('list', array('fields' => 'plugin_id,plugin_id', 'conditions' => array('SitePlugin.active' => 1, 'SitePlugin.site_id' => $site['id']))));
        $this->set('content', $content = $this->get_snippet('plugin-manager-' . $page_id, false, $page_id == 2 ? true : false));
        $this->set('plugin_array', $this->Plugin->getList());
        $this->set('page_id', $page_id);
        $this->set('title_for_layout', __('Plugin Manager', true));

        $this->loadModel('Industry');
        $this->set('industries', $this->Industry->find('list', array('conditions' => array('Industry.id' => $site['industry_id']))));
        $this->_settings();
        $this->set('site', $site);
    }

    function owner_plugin_manager_backup()
    {
        $site = getAuthOwner();
        $this->loadModel('SitePlugin');
        $this->loadModel('Plugin');
        $sticky_list = $this->Plugin->find('list', array('conditions' => array('sticky' => 1)));
        if (!empty($this->data)) {
            SitePluginService::updateWhere(['active' => 0], ['site_id' => $site['id']]);
            SitePluginService::updateWhere(['active' => 1], ['site_id' => $site['id'], 'plugin_id' => array_keys($sticky_list)]); //
            SitePluginService::updateWhere(['active' => 1], ['site_id' => $site['id'], 'plugin_id' => array_values($this->data['Plugin'])]);
            $this->Session->delete('CurrentPlugin');
            Cache::clear('Current_Plugin_' . getCurrentSite('id'));
            getCurrentPlugin();
            $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
        }
        $this->set('main_plugins', $this->Plugin->find('all', array('order' => 'Plugin.display_order asc', 'conditions' => array('Plugin.sticky' => 0, 'Plugin.active' => 1))));
        $this->set('plugins', $this->SitePlugin->find('all', array('order' => 'Plugin.display_order asc', 'conditions' => array('Plugin.sticky' => 0, 'SitePlugin.site_id' => $site['id']))));
        $this->_settings();
    }

    function owner_backup()
    {
        if (isset($_GET['notallowed']) && $_GET['notallowed'] == "true") {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
        }
        $this->pageTitle = __('Backup', true);
        App::import('Helper', 'Html');
        $html = new HtmlHelper();
        $upgrade_link = $html->link(__('Upgrade', true), array('prefix' => 'owner', 'controller' => 'sites', 'action' => 'renew', 'owner' => true));
        $renew_link = $html->link(__('Renew', true), array('prefix' => 'owner', 'controller' => 'owner', 'action' => 'upgrade', 'owner' => true));
        $this->loadModel('Site');
        $site = $this->Site->read(null, getCurrentSite('id'));
        $site = $site['Site'];
        $owner = getAuthOwner();
        $staff = getAuthStaff();
        $allowedStaff = $staff && $staff['role_id'] == -1;
        if (!$allowedStaff && $owner['staff_id'] != 0) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if ($site['plan_id'] == 1) {
            $msg = sprintf(__('Sorry, This feature is available for premium accounts only, You need to %s your account', true), $upgrade_link);

            $this->flashMessage($msg);
            $this->redirect('/');
        } else if (strtotime($site['expiry_date']) < time()) {
            $msg = sprintf(__('Sorry, You need to %s your expired subscription  to access this page', true), $renew_link);
            $this->flashMessage($msg);
            $this->redirect('/');
        }
        if (isset($this->params['url']['error'])) {
            switch ($this->params['url']['error']) {
                case 'notallowed':
                    $this->flashMessage(__('You are not allowed to view this page', TRUE));
                    $this->redirect('/');
                    break;
                case 'need_to_upgrade':
                    $msg = sprintf(__('Sorry, This feature is available for premium accounts only, You need to %s your account', true), $upgrade_link);
                    $this->flashMessage($msg);
                    $this->redirect('/');
                    break;
                case 'need_to_renew':
                    $msg = sprintf(__('Sorry, You need to %s your expired subscription to access this page', true), $renew_link);
                    $this->flashMessage($msg);
                    $this->redirect('/');
                    break;
            }
        }
    }

//    function owner_notifications() {
//        $conditions = array();
//        if (empty($_GET['dismissed']))
//            $conditions[] = '(Notification.dismissed is null or Notification.dismissed=0)';
//        else
//            $conditions[] = '(Notification.dismissed =1)';
//
//        App::import('Vendor', 'notification');
//        $this->loadModel('Notification');
//        $this->set('noti_texts', $this->Notification->noti_text);
//        $this->paginate['Notification']['order'] = 'Notification.id  DESC';
//        $notifications = $this->paginate('Notification', $conditions);
//        foreach ($notifications as $i => $notification) {
//            $notifications[$i] = NotificationV::getNotificationDetails($notification);
//        }
//
//        $this->set('notifications', $notifications);
//        $this->set('title_for_layout',  __('Notifications', true));
//    }


    function notifications()
    {

        if (getAuthClient()) {
            $this->redirect('/client/sites/notifications');
        } else {
            $this->redirect(Router::url(array('controller' => 'sites', 'action' => 'notifications', 'owner' => 1, 'prefix' => 'owner')));
        }


    }

    function owner_notifications()
    {

        if (getAuthClient() || getAuthOwner() || getAuthStaff()) {
            App::import('Vendor', 'notification_2');
            $conditions = array();
            if (empty($_GET['dismissed']))
                $conditions[] = '(Notification_2.status <> ' . NotificationV2::NOTI_STATUS_DISMISSED . ')';
            else
                $conditions[] = '(Notification_2.status =' . NotificationV2::NOTI_STATUS_DISMISSED . ')';

            if (getAuthClient()) {
                $conditions['Notification_2.user_id'] = getAuthClient('id');
                $conditions['Notification_2.user_type'] = NotificationV2::NOTI_USER_TYPE_CLIENT;
            } else {
                $conditions['Notification_2.user_id'] = getAuthStaff('id');
                $conditions['Notification_2.user_type'] = NotificationV2::NOTI_USER_TYPE_STAFF;
            }

            $this->loadModel('Notification_2');
            $this->set('noti_texts', $this->Notification_2->noti_text);
            $this->paginate['Notification_2']['order'] = 'Notification_2.id  DESC';
            $notifications = $this->paginate('Notification_2', $conditions);
            foreach ($notifications as $i => $notification) {
                $notifications[$i] = NotificationV2::getNotificationDetails($notification);
            }
            $excluded = NotificationV2::get_exculded();

            $this->set('excluded', $excluded);
            $this->set('notifications', $notifications);
            $this->set('title_for_layout', __('Notifications', true));
            $this->render('notifications');
        } else {
            $this->flashMessage(__('You are not allowed in this page', true));
            $this->redirect('/');
        }

    }

    function client_notifications()
    {

        if (getAuthClient()) {
            App::import('Vendor', 'notification_2');
            $conditions = array();
            if (empty($_GET['dismissed']))
                $conditions[] = '(Notification_2.status <> ' . NotificationV2::NOTI_STATUS_DISMISSED . ')';
            else
                $conditions[] = '(Notification_2.status =' . NotificationV2::NOTI_STATUS_DISMISSED . ')';

            if (getAuthClient()) {
                $this->layout = 'default';
                $conditions['Notification_2.user_id'] = getAuthClient('id');
                $conditions['Notification_2.user_type'] = NotificationV2::NOTI_USER_TYPE_CLIENT;
            } else {
                $this->layout = 'default';
                $conditions['Notification_2.user_id'] = getAuthStaff('id');
                $conditions['Notification_2.user_type'] = NotificationV2::NOTI_USER_TYPE_STAFF;
            }

            $this->loadModel('Notification_2');
            $this->set('noti_texts', $this->Notification_2->noti_text);
            $this->paginate['Notification_2']['order'] = 'Notification_2.id  DESC';
            $notifications = $this->paginate('Notification_2', $conditions);
            foreach ($notifications as $i => $notification) {
                $notifications[$i] = NotificationV2::getNotificationDetails($notification);
            }
            $excluded = NotificationV2::get_exculded();

            $this->set('excluded', $excluded);
            $this->set('notifications', $notifications);
            $this->set('title_for_layout', __('Notifications', true));
            $this->render('notifications');
        } else {
            $this->flashMessage(__('You are not allowed in this page', true));
            $this->redirect('/');
        }
    }

//    function owner_list_notifications($key = null) {
//        App::import('Vendor', 'notification_2');
//        $this->set('noti_key', $key);
//        $this->set('noti_list', NotificationV::getallnotification($key));
//    }
    function list_notifications($key = null)
    {
        App::import('Vendor', 'notification_2');

        $this->set('noti_key', $key);
        $this->set('noti_list', NotificationV2::getallnotification($key));
    }

    /**
     *
     * @param type $notifcation_id
     * @param type $exculde_items 1 => dont notify me for this action for this user,0 dont notify me for this action
     */

    function exclude_notification($notifcation_id, $exclude_all)
    {
        App::import('Vendor', 'notification_2');
        NotificationV2::exclude($notifcation_id, 0, $exclude_all);
        $this->flashMessage(__('Notification excluded  successfully', true), 'Sucmessage');
        $this->redirect(Router::url(array('action' => 'notifications2')));
    }


    function owner_view_notification($id = null)
    {
        App::import('Vendor', 'notification_2');
        NotificationV2::read_notification($id);
        $row = NotificationV2::view_notification($id);
        $noti_text = NotificationV2::noti_text();
        echo $noti_text[$row['Notification']['notification_key']]['head'];
        die();
    }
//
//    function owner_dismiss_notification($id, $key = null) {
//        App::import('Vendor', 'notification');
//        if ($id == "0") {
//            NotificationV::dismiss_notifications($key);
//        } else {
//            NotificationV::dismiss_notification($id);
//        }
//        if (!$this->RequestHandler->isAjax()) {
//            $this->flashMessage(__('Notification dismissed  successfully', true), 'Sucmessage');
//            $this->redirect(array('action' => 'notifications'));
//        } else {
//            echo "ok";
//            die();
//        }
//    }

    function dismiss_notification($id, $key = null)
    {
        App::import('Vendor', 'notification_2');
        if ($id == "0") {
            NotificationV2::dismiss_notifications($key);
        } else {
            NotificationV2::dismiss_notification($id);
        }
        if (!$this->RequestHandler->isAjax()) {
            $this->flashMessage(__('Notification dismissed  successfully', true), 'Sucmessage');
            $this->redirect(array('action' => 'notifications'));
        } else {
            echo "ok";
            die();
        }
    }


    function owner_delete_notification()
    {
        App::import('Vendor', 'notification_2');
        foreach ($_POST['ids'] as $id) {
            NotificationV2::delete_byid($id);
        }
        $this->flashMessage(__('Notification Deleted  successfully', true), 'Sucmessage');
        $this->redirect(array('action' => 'notifications'));
    }

//    function owner_dismiss_notifications() {
//        foreach ($_POST['ids'] as $id) {
//            NotificationV::dismiss_notification($id);
//        }
//        $this->flashMessage(__('Notification Dismissed  successfully', true), 'Sucmessage');
//        $this->redirect(array('action' => 'notifications'));
//    }

    function dismiss_notifications()
    {
        App::import('Vendor', 'notification_2');
        foreach ($_POST['ids'] as $id) {
            $result = NotificationV2::dismiss_notification($id);
            if (!$result)
                break;
        }
        if ($result) {
            $this->flashMessage(__('Notification Dismissed  successfully', true), 'Sucmessage');
        } else {
            $this->flashMessage(__('Couldn\'t find notification', true));
        }

        $this->redirect(array('action' => 'notifications'));
    }

    function owner_smtp_settings()
    {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
//        App::import('Vendor', 'notification');
        App::import('Vendor', 'SMTPSettings', array('file' => 'SMTPSettings.php'));
        $this->set('title_for_layout', __('SMTP Settings', true));
        $this->loadModel('Site');
        $this->set('googleAuthUrl', Google::getAuthURl('legacy'));
        $this->set('outlookAuthUrl', Outlook::getAuthURl('legacy'));
        if (!empty($this->data)) {
            unset($this->data['Site']['advanced_options']);
            if ($this->Site->ChangeSmtp($this->data, false)) {
//                NotificationV::delete_notification(NOTI_NO_SMTP_SETTING, null);
                $this->Site->reload_session();
                if ($this->data['Site']['use_smtp'] != "1") {

                    $this->Site->ChangeSmtp($this->data, true);
                    $this->Site->reload_session();
                    $this->flashMessage(__('SMTP is Disabled successfully', true), 'Sucmessage');
                    $this->add_actionline(ACTION_UPDATE_SMTP_SETTINGS, array('param2' => $this->data['Site']['smtp_from_email'], 'param3' => 0));
                    $this->redirect(array('action' => 'smtp_settings'));
                }
                $this->Email->from = $this->data['Site']['smtp_from_name'] . ' <' . $this->data['Site']['smtp_from_email'] . '>';

                $this->Email->to = $this->data['Site']['smtp_from_email'];
                $this->Email->subject = 'Test SMTP Email from ' . Domain_Short_Name;
                $ssl = $this->data['Site']['smtp_ssl'] == 1 ? "ssl://" : "";
                $this->data['Site']['smtp_user_name'] = trim($this->data['Site']['smtp_user_name']);
                $this->data['Site']['smtp_host'] = trim($this->data['Site']['smtp_host']);


                $this->Email->smtpOptions = array(
                    'port' => $this->data['Site']['smtp_port'] ? $this->data['Site']['smtp_port'] : ($this->data['Site']['smtp_ssl'] ? 465 : 25),
                    'timeout' => '30',
                    'credentials' => $this->data['Site']['smtp_credentials'],
                    'host' => $ssl . $this->data['Site']['smtp_host'],
                    'username' => $this->data['Site']['smtp_user_name'],
                    'password' => $this->data['Site']['smtp_password'],
                    'client' => str_replace(' ', '', Site_Full_name)
                );
                $this->Email->delivery = 'smtp';

                if (!empty($this->data['Site']['smtp_credentials'])) {
                    if (!$this->Email->validate()) {
                        $msg = __('SMTP Settings could not be saved. Please, try again.', true);
                        if ($this->Email->error) {
                            $msg .= ' ' . $this->Email->error;
                        }

                        $this->flashMessage($msg);
                    }else{
                        $this->handleSmtpSuccess();
                    }

                } else if ($this->Email->send('SMTP test from ' . Domain_Short_Name . ', sorry for the inconvenience') && $this->Email->smtpError == "") {
                    $this->handleSmtpSuccess();
                } else {
                    $this->flashMessage(__('Error In SMTP: ' . $this->Email->smtpError, true));
                }
            } else {
                $this->flashMessage(__('SMTP Settings could not be saved. Please, try again.', true));
            }
        } else {
            $smtps = SMTPSettings::getSettings();

            //get the snippet data
            foreach ($smtps as $index => $smtp) {
                $smtps[$index]['snippet'] = $this->get_snippet($smtp['snippet'], false, false);
            }

            $user = $this->Site->findById(getAuthOwner('id'));
            $this->data = $user;

            $this->set('smtps', $smtps);
        }
    }

    private function handleSmtpSuccess()
    {
        $this->Site->ChangeSmtp($this->data, true);
        $this->Site->reload_session();
        $this->flashMessage(__('SMTP is Enabled Successfully', true), 'Sucmessage');
        $this->add_actionline(ACTION_UPDATE_SMTP_SETTINGS, [
            'param2' => $this->data['Site']['smtp_from_email'],
            'param3' => 1
        ]);
        $this->set('googleAuthUrl', Google::getAuthURl('legacy'));
        $this->set('outlookAuthUrl', Outlook::getAuthURl('legacy'));
        $this->redirect(array('action' => 'smtp_settings'));
    }

    function staff_redirect()
    {
        //Get Current Menu using AJAX
        session_write_close();
        $menus = $this->ApiRequests->request('/api2/layout/?debug=0')['data']['Layout']['menu'];
        session_start();
        $menusKey = key($menus);
        $firstSubMenu = isset($menus[$menusKey]['submenus']) && isset($menus[$menusKey]['submenus'][0]) ? $menus[$menusKey]['submenus'][0] : [];
        $firstLink = $firstSubMenu ? $firstSubMenu[key($firstSubMenu)]['url'] : null;
        $firstLink ? $this->redirect($firstLink) : $this->redirect(array('controller' => 'staffs', 'action' => 'account_info'));
    }

    function owner_dashboard()
    {
        /**
         * Handling the Sales invoices referrals system
         * @see https://izam.atlassian.net/browse/DAFTRA-58317
         * @see https://izam.atlassian.net/browse/DAFTRA-58339
         *
         * @todo replace this logic by only update the target_site_id with current
         * site id and in the main dashboard page / cron continue the sync process
         */
        if ($sipHash = $this->Cookie->read('sip_hash')) {
            $trackedInvoice = (new SalesInvoicesReferral)->syncTrackedInvoice(
                $sipHash,
                getCurrentSite('id')
            );
            $this->Cookie->domain = Domain_Short_Name;
            $this->Cookie->delete('sip_hash');
            if (
                $trackedInvoice && (
                    isset($trackedInvoice['status']) &&
                    $trackedInvoice['status'] == 'OK'
                )
            ) {
                return $this->redirect([
                    'controller' => $trackedInvoice['model'] == 'Invoice' ?
                        'purchase_invoices' :
                        'invoices',
                    'action' => 'edit',
                    $trackedInvoice['id']
                ]);
            } else if (
                isset($trackedInvoice['status'])
                && $trackedInvoice['status'] == 'ERR'
            ) {
                $this->flashMessage(
                    $trackedInvoice['errors'][0] ?? 'Invalid request'
                );
            }
        }

        //TODO: util vendor
        if (!ifPluginActive(SalesPlugin) || !check_permission(Invoices_View_Invoices_Details)) {
            return $this->redirect('/v2/owner/dashboard');
        }

        $this->loadModel('Block');
        $site = getAuthOwner();
        $this->set('staff_id', $site['staff_id']);
        $user = getAuthOwner();
        $this->set('user', $user);

        if (!empty($site['staff_id']) && $site['staff_id'] != '0' && !check_permission(Invoices_View_Invoices_Details)) {
            $this->staff_redirect();
        }

        $this->set('blocks', $this->Block->find('all', array('order' => 'display_order', 'conditions' => array('site_id' => $site['id']), 'recursive' => -1)));
        $this->set('sub_blocks', (include_once APP . 'blocks.php'));

        $this->loadModel('Invoice');
        $extrat_conditions = array();
        if (check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details)) {
            if (!check_permission(Invoices_View_All_Invoices)) {
                $extrat_conditions['Invoice.staff_id'] = $site['staff_id'];
            }
            $recentIvoices = $this->Invoice->find('all', array('limit' => 10, 'order' => 'Invoice.date DESC, Invoice.id DESC', 'conditions' => $extrat_conditions + array('Invoice.type' => 0), 'recursive' => -1));
            $this->Invoice->setEntityAppData($recentIvoices);
            $this->set('recentInvoices', $recentIvoices);
        }

        $month_invoices_count = $this->Invoice->find('count', array('fields' => 'DISTINCT Invoice.date', 'conditions' => $extrat_conditions + array('Invoice.type' => 0, '(Invoice.draft = 0 OR Invoice.draft IS  NULL) ', 'Invoice.date <=' => date('Y-m-d 23:59:59'), 'Invoice.date >=' => date('Y-m-d', strtotime('-1 Month'))), 'recursive' => -1));
        $x = $this->Invoice->find('all', array('fields' => 'DISTINCT Invoice.date', 'conditions' => $extrat_conditions + array('Invoice.type' => 0, '(Invoice.draft = 0 OR Invoice.draft IS  NULL) ', 'Invoice.date <=' => date('Y-m-d 23:59:59'), 'Invoice.date >=' => date('Y-m-d', strtotime('-1 Month'))), 'recursive' => -1));

        if ($month_invoices_count < 5) {
            $all_invoices_count = $this->Invoice->find('count', array('fields' => 'DISTINCT Invoice.date', 'conditions' => $extrat_conditions + array('Invoice.type' => 0, '(Invoice.draft = 0 OR Invoice.draft IS  NULL) ', 'Invoice.date <=' => date('Y-m-d 23:59:59')), 'recursive' => -1));
            if ($all_invoices_count >= $month_invoices_count * 2)
                $this->set('show_year', true);
        }

        $this->set('invoiceStatus', Invoice::getPaymentStatuses());
        $this->Invoice->bindModel(array('Subscription' => array('className' => 'Invoice', 'type' => 'belongsTo')));
        $this->Invoice->unbindModel(array('belongsTo' => array('Site', 'Client')));
        $this->set('invoiceStatuses', Invoice::getPaymentStatuses());
        $dueInvoices = $this->Invoice->find('all', array('recursive' => -1, 'limit' => 10, 'conditions' => $extrat_conditions + array('Invoice.type' => 0, 'Invoice.date <= CURDATE()', 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) >= CURDATE()', 'Invoice.payment_status not ' => array(INVOICE_STATUS_PAID, INVOICE_STATUS_REFUNDED), '(Invoice.draft !=1 OR Invoice.draft is null)'), 'order' => 'Invoice.date DESC'));
        $this->Invoice->setEntityAppData($dueInvoices);
        $this->set('dueInvoices', $dueInvoices);
        $this->set('dueInvoicesCount', $this->Invoice->find('count', array('recursive' => -1, 'conditions' => $extrat_conditions + array('Invoice.type' => 0, 'Invoice.date <= CURDATE()', 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) >= CURDATE()', 'Invoice.payment_status not ' => array(INVOICE_STATUS_PAID, INVOICE_STATUS_REFUNDED), '(Invoice.draft !=1 OR Invoice.draft is null)'))));
        $overdueInvoice = $this->Invoice->find('all', array('recursive' => -1, 'limit' => 10, 'conditions' => $extrat_conditions + array('Invoice.type' => 0, 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) < CURDATE()', 'Invoice.payment_status not ' => array(INVOICE_STATUS_PAID, INVOICE_STATUS_REFUNDED), '(Invoice.draft !=1 OR Invoice.draft is null)'), 'order' => 'Invoice.date DESC'));
        $this->Invoice->setEntityAppData($overdueInvoice);
        $this->set('overdueInvoices', $overdueInvoice);
        $this->set('overdueInvoicesCount', $this->Invoice->find('count', array('recursive' => -1, 'conditions' => $extrat_conditions + array('Invoice.type' => 0, 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) < CURDATE()', 'Invoice.payment_status not ' => array(INVOICE_STATUS_PAID, INVOICE_STATUS_REFUNDED), '(Invoice.draft !=1 OR Invoice.draft is null)'))));

        $this->loadModel('InvoicePayment');

        $condition = '';
        if (!check_permission(Invoices_View_All_Invoices) && (check_permission(Invoices_View_Invoices_Details))) {
            $condition  = ' AND InvoicePayment.staff_id = '.(int)$site['staff_id'];
        }
        if(!getAuthOwner('is_super_admin')){
            if(ifPluginActive(BranchesPlugin)) {
                $branches = implode(',',getStaffBranchesIDs($site['staff_id']));
                $condition  .= ' AND  InvoicePayment.branch_id IN ('.$branches.') ';
            }
        }

        $recentPayments = $this->InvoicePayment->query('
			SELECT *
				FROM (
				    SELECT *
				    FROM `invoice_payments` AS `InvoicePayment`
				    WHERE `InvoicePayment`.`status` IN ( 1, 2 )'.$condition.'
				    ORDER BY `InvoicePayment`.`date` DESC
				    LIMIT 10
				) AS `InvoicePayment`
				LEFT JOIN `staffs` AS `Staff` ON (`InvoicePayment`.`staff_id` = `Staff`.`id`)
				LEFT JOIN `invoices` AS `Invoice` ON (`InvoicePayment`.`invoice_id` = `Invoice`.`id`)
				LEFT JOIN `clients` AS `Client` ON (`InvoicePayment`.`client_id` = `Client`.`id`)
				LEFT JOIN `treasuries` AS `Treasury` ON (`InvoicePayment`.`treasury_id` = `Treasury`.`id`);
		', false);
        $this->set('recentPayments', $recentPayments);
        $this->set('recentPaymentsCount', $this->InvoicePayment->find('count', array('recursive' => -1, 'order' => 'InvoicePayment.id DESC', 'conditions' => $extrat_conditions + array('InvoicePayment.status' => array(1, 2)))));

        $this->loadModel('Staff');
        $staffs = $this->Staff->getList();
        $this->set('staffs', $staffs);

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, false);
        $this->set('payment_methods', $paymentMethods);
        $this->set('statuses', $this->InvoicePayment->getPaymentStatus());
        $this->set('paymentStatus', InvoicePayment::getPaymentStatus());

        $this->loadModel('SavedReport');
        $this->set(array(
            'taxReports' => $this->SavedReport->getSavedReportsList(TAX_REPORT),
            'paymentReports' => $this->SavedReport->getSavedReportsList(PAYMENT_REPORT),
            'revenueReports' => $this->SavedReport->getSavedReportsList(REVENUE_REPORT),
        ));

        if (!empty($checklist)) {
            $this->set('checklistSnippet', $this->get_snippet('checklist'));
        }

        $siteCreationDate = date("Y-m-d", strtotime(getCurrentSite('created')));
        $orientationSnippetRemainingPeriod = date('Y-m-d', strtotime($siteCreationDate . ' + 30 days'));
        $now_date = date('Y-m-d');
        if (strtotime($orientationSnippetRemainingPeriod) > strtotime($now_date) && check_permission(Edit_General_Settings)) {
            $this->set('orientationSnippet', $this->get_compiled_snippet('orientation-message'));
        }

        $ksaOrientationRemainingPeriod = date('Y-m-d', strtotime($siteCreationDate . ' + 45 days'));
        if (strtotime($ksaOrientationRemainingPeriod) > strtotime($now_date) && getCurrentSite('country_code') === 'SA' && check_permission(Edit_General_Settings) && !ifPluginActive(EINVOICE_SA_PLUGIN)) {
            $this->set('ksaEinvoiceSnippet', $this->get_compiled_snippet('ksa-invoice-orientation'));
        }

        if (ifPluginActive(EINVOICE_SA_PLUGIN)) {
            $this->set('ksaEInvoiceGuide', $this->get_compiled_snippet('ksa-invoice-guide'));
        }

        if (ifPluginActive(InventoryPlugin) && check_permission(Track_Inventory) && check_permission(View_his_own_Products)) {
            $this->loadModel('Product');

            $conditions = array();
            $conditions[] = '(Product.status IS NULL OR Product.status = ' . ProductStatusUtil::STATUS_ACTIVE . ') AND  Product.track_stock = 1 AND  Product.track_stock  IS NOT NULL  AND  ( Product.stock_balance IS NULL OR Product.stock_balance <= 0 OR (Product.stock_balance <= Product.low_stock_thershold AND  Product.low_stock_thershold IS NOT NULL )  ) ';
            if (!check_permission(View_All_Products))
                $conditions['Product.staff_id'] = $site['staff_id'];
            $low_stock_count=0;
            $low_products=[];
            //if(getCurrentSite('id')!=2167871) {
                $low_stock_count = $this->Product->find('count', array('conditions' => $conditions));
                $low_products = $this->Product->find('all', array('recursive' => -1, 'conditions' => $conditions, 'order' => array('Product.modified' => 'DESC'), 'limit' => 20));
            //}
            $this->set('low_stock_count', $low_stock_count);
            $this->set('low_products', $low_products);
        }

        if (ifPluginActive(FollowupPlugin) && (check_permission(View_All_Attachments_And_Notes_For_All_Clients) || check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) || check_permission(View_His_Own_Notes_Attachments_Only))) {
            $this->loadModel('ClientAppointment');
            $this->loadModel('FollowUpReminder');
            $this->FollowUpReminder->recursive = -1;
            $this->loadModel('Post');
            $conditions = array();
            $conditions['FollowUpReminder.partner_type'] = 1;
            $conditions['FollowUpReminder.status'] = 0;
            $partnerTypeJoin = [
                ['table' => 'clients', 'alias' => 'Partner', '0' => 'inner', 'conditions' => ['Partner.id = FollowUpReminder.partner_id']]
            ];
            $appointments_count = $this->FollowUpReminder->find('count', array('joins' => $partnerTypeJoin, 'conditions' => $conditions));
            $this->set('appointments_count', $appointments_count);
            $conditions[] = 'FollowUpReminder.date < "' . date('Y-m-d', strtotime('tomorrow')) . '"';
            $due_appointments_count = $this->FollowUpReminder->find('count', array('joins' => $partnerTypeJoin, 'conditions' => $conditions));
            $this->set('due_appointments_count', $due_appointments_count);
        }

        // warning suppress
        if (isset($jsonPayment) || isset($jsonTax) || isset($jsonRevenue))
            $this->set(compact('jsonPayment', 'jsonTax', 'jsonRevenue'));
        // end warning suppress
        $this->set('crumbs', array());
        $this->set('languages', $this->_json_lang());

        //Getting invoices statuses/colors if enabled
        $enable_invoice_status = settings::getValue(InvoicesPlugin, 'enable_invoice_status');
        $this->set('enable_invoice_status', $enable_invoice_status);
        if ($enable_invoice_status) {

            //Getting followup statuses
            $this->loadModel('FollowUpStatus');
            $this->loadModel('Post');
            $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::INVOICE_TYPE);
            if (check_permission(Edit_General_Settings)) {
                $FollowUpStatus[] = array('name' => false, 'value' => false, 'data-divider' => "true");
                $FollowUpStatus[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> Edit Statuses List </span>', 'name' => __('Edit Statuses List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
            }
            $this->set('FollowUpStatuses', $FollowUpStatus);

            $invoice_statuses = $this->FollowUpStatus->getList(Post::INVOICE_TYPE, true);
            $this->set('invoice_statuses', $invoice_statuses);
            $invoice_status_colors = $this->FollowUpStatus->getList(Post::INVOICE_TYPE, true, array(), 'color');
            $this->set('colors_options', $this->FollowUpStatus->colors);
            $this->set('invoice_status_colors', $invoice_status_colors);
        }
        $this->set('title_for_layout', __('Dashboard', true));
    }

    function owner_change_email()
    {
        $this->loadModel('EmailUpdate');
        $site = getAuthOwner();
        $this->loadModel('Site');
        if (!empty($this->data)) {
            if ($this->Site->ChangeEmail($this->data)) {
                $old_email = getAuthOwner('email');
                $new_email = $this->data['Site']['email'];
                $this->loadModel('Staff');
                $this->Staff->changeOwnerUserEmail($new_email);
                PortalEmailUpdate::where('site_id', getAuthOwner('id'))->delete();
                $EmailUpdate['EmailUpdate']['old_email'] = $old_email;
                $EmailUpdate['EmailUpdate']['new_email'] = $new_email;
                $EmailUpdate['EmailUpdate']['site_id'] = getAuthOwner('id');
                PortalEmailUpdate::create($EmailUpdate['EmailUpdate']);

                $this->flashMessage(__('The Email address has been saved', true), 'Sucmessage');
                $this->Site->reload_session();
                $this->add_actionline(ACTION_UPDATE_EMAIL, array('param2' => $old_email, 'param3' => $new_email));
                $this->redirect(array('action' => 'change_email'));
            } else {
                $this->flashMessage(__('The email address could not be saved. Please try again.', true));
            }
        }
        if (empty($this->data)) {
            $this->data['Site'] = $site;
        }

        $this->crumbs[0]['title'] = __('Change Email', true);
        $this->crumbs[0]['url'] = array('action' => 'change_email');
        $this->set('title_for_layout', __('Change Email', true));
    }

    function owner_change_password()
    {
        $site = getAuthOwner();
        $this->loadModel('Site');
        if (!empty($this->data)) {
            if ($this->Site->ChangePassword($this->data, $site)) {
                $this->loadModel('Staff');
                $this->Staff->changeOwnerUserPassword($this->data["Site"]['password']);
                AuthHelper::revoke(AuthUserTypeUtil::OWNER, $site['id']);
               $sessionId = session_id();
               clearSession("owner", getAuthOwner('id'), $sessionId);
               if(useRedis()) {
                   try {
                       $userSessionRedisService = resolve(UserSessionRedisService::class);
                       $userSessionRedisService->revokeSessions($site['id']);
                   } catch (\Throwable $e) {
                       \Rollbar\Rollbar::log(\Rollbar\Payload\Level::WARNING, 'Failed to revoke session from Redis: ' . $e->getMessage());
                   }
               }
                $this->add_actionline(ACTION_CHNAGE_PASSWORD);
                $this->flashMessage(__('The password has been saved', true), 'Sucmessage');
                $this->redirect(array('action' => 'change_password'));
            } else {
                $this->flashMessage(__('The password could not be saved. Please, try again.', true));
            }
        }

        $this->crumbs[0]['title'] = __('Change Password', true);
        $this->crumbs[0]['url'] = array('action' => 'change_password');
        $this->set('title_for_layout', __('Change Password', true));
    }

    function owner_change_invoice_title()
    {

        $site = getAuthOwner();
        $this->loadModel('Site');
        if (!empty($this->data)) {
            $this->Site->id = $site['id'];
            if (SiteService::updateById($site['id'], array('invoice_default_title' => $this->data['Site']['invoice_default_title'])) && SiteService::updateById($site['id'], array('enable_po' => $this->data['Site']['enable_po']))) {
                $this->flashMessage(__('The default invoice title has been updated', true), 'Sucmessage');
                $this->Site->reload_session();
                $this->redirect('/');
            } else {
                $this->flashMessage(__('Invoice title could not be saved. Please, try again.', true));
            }
        } else {
            $this->data['Site'] = $site;
            if (empty($this->data['Site']['invoice_default_title']))
                $this->data['Site']['invoice_default_title'] = __('Invoice', true);
            debug($this->data['Site']);
        }

        $this->crumbs[0]['title'] = __('Default Invoice Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_invoice_title');
        $this->set('title_for_layout', __('Change Invoice Title', true));
    }

    function owner_change_settings()
    {
        $_REQUEST=modifyArray($_REQUEST);
        $this->data=modifyArray($this->data);
        $this->loadModel('Site');

        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->invoice_js_labels);
        App::import('Vendor', 'settings');
        App::import('Vendor', 'notification');
        App::import('Vendor', 'sites_local');
        $this->set('bnf', Localize::get_business_number_fields());

        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        $this->loadModel('Site');

        if (!empty($this->data)) {

            $this->data['Site']['business_name'] = html_entity_decode($this->data['Site']['business_name']);
            $this->data['Site']['first_name'] = html_entity_decode($this->data['Site']['first_name']);
            $this->data['Site']['last_name'] = html_entity_decode($this->data['Site']['last_name']);
            $this->data['Site']['address1'] = html_entity_decode($this->data['Site']['address1']);
            $this->data['Site']['address2'] = html_entity_decode($this->data['Site']['address2']);
            $this->data['Site']['id'] = $site['id'];
            unset($this->data['Site']['plan_id']);
            unset($this->data['Site']['email']);
            unset($this->data['Site']['password']);
            unset($this->data['Site']['expiry_date']);
            unset($this->data['Site']['last_ip']);
            unset($this->data['Site']['last_login']);
            unset($this->data['Site']['subdomain']);
            if (settings::getValue(ExpensesPlugin, 'journals_local_currency_code')) {
                unset($this->data['Site']['currency_code']);
            }
            if ($site['bn1_locked']) {
                $this->data['Site']['bn1'] = $site['bn1'];
            }
            if ($site['bn2_locked']) {
                $this->data['Site']['bn2'] = $site['bn2'];
            }
            unset($this->data['Site']['country_code']);


            if(!empty($_FILES['data']['name']['Site']['site_logo']) && $_FILES['data']['size']['Site']['site_logo']>0) {
                $_FILES['site_logo']['name'] = $_FILES['data']['name']['Site']['site_logo'];
                $_FILES['site_logo']['tmp_name'] = $_FILES['data']['tmp_name']['Site']['site_logo'];
                $_FILES['site_logo']['size'] = $_FILES['data']['size']['Site']['site_logo'];
                $_FILES['site_logo']['type'] = $_FILES['data']['type']['Site']['site_logo'];

                $res = $this->_saveLogo();

                $this->data['Site']['site_logo']=$res['image_name'];
            }else{
                unset($this->data['Site']['site_logo']);
            }

//            $this->data = $this->Site->invokeBehaviorBeforeSave('Site', 'image', $this->data);
            if (SiteService::updateById($site['id'], $this->data['Site'])) {
                if (isset($this->data['Site']['invoicing_method']) && ($this->data['Site']['invoicing_method'] !== ""))
                    settings::setValue(InvoicesPlugin, 'invoicing_method', $this->data['Site']['invoicing_method']);

                if (isset($this->data['Site']['sold_item_type']) && ($this->data['Site']['sold_item_type'] !== ""))
                    settings::setValue(InvoicesPlugin, 'sold_item_type', $this->data['Site']['sold_item_type']);

                if (isset($this->data['Site']['invoice_print_method']) && ($this->data['Site']['invoice_print_method'] !== "")) {
                    settings::setValue(InvoicesPlugin, 'invoice_print_method', $this->data['Site']['invoice_print_method']);
                }
                if (isset($this->data['Site']['negative_currency_formats']) && ($this->data['Site']['negative_currency_formats'] !== "")){
                    settings::setValue(0, 'negative_currency_formats', $this->data['Site']['negative_currency_formats']);
                }
               
                //Delete Cached Menu

//                NotificationV::delete_notification(NOTI_NO_LOGO_UPLOADED, null);
                //Configure::write('Config.language',CurrentSiteLang());

                $fileName = $_FILES['data']['name']['Site']['site_logo'];
                $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                $allowed_extensions = $this->Site->actsAs['image']['site_logo']['extensions'];
                $arrayString = implode(',', $allowed_extensions);
                if (!empty($fileName) && !in_array(strtolower($ext), $allowed_extensions)) {
                    $this->flashMessage(__('Your settings have been updated except (Logo), Reason : Invalid extension "' . $ext . '" and the allowed extensions : ' . $arrayString, true));
                unset($this->data['Site']['site_logo']);
                } else {
                    if ($this->data['Site']['language_code'] == '41') {
                        $this->flashMessage('Your settings have been updated', 'Sucmessage');
                    } elseif ($this->data['Site']['language_code'] == '7') {
                        $this->flashMessage('تم تحديث الإعدادات', 'Sucmessage');
                    } else {
                        $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
                    }
                }
                $this->loadModel('Staff');
                $ownerStaff = $this->Staff->find('first', ['conditions' => ['role_id' => Staff::OWNER_ROLE_ID]]);
                if($ownerStaff){
                    $updatedSiteData = $this->Site->findById($site['id']);
                    $ownerStaffId= $ownerStaff['Staff']['id'];
                    $this->Staff->saveOwnerUser($updatedSiteData['Site'], $ownerStaffId);
                    if(getAuthOwner('staff_id') == $ownerStaffId){
                        $StaffRow = $this->Staff->getStaffWithRoleForSession($ownerStaffId);
                        $this->Session->write('STAFF', $StaffRow['Staff']);
                    }
                }
                $this->add_actionline(ACTION_UPDATE_SETTINGS, array('param4' => json_encode($this->data['Site'])));
                $this->Site->reload_session();
                delete_menus();

                if (ifPluginActive(AccountingPlugin)) {
                    $this->updateJournalLang();
                }

                $this->redirect(array('action' => 'change_settings'));
            } else {
                $this->flashMessage(__('Could not update your settings. Please, try again.', true));
            }
        }

        if (empty($this->data)) {
            $this->data = $this->Site->findById($site['id']);
            $this->data['Site']['negative_currency_formats'] = settings::getValue(0, 'negative_currency_formats',null, false);
            $this->data['Site']['first_name'] = htmlspecialchars_decode($this->data['Site']['first_name'], ENT_QUOTES);
            $this->data['Site']['last_name'] = htmlspecialchars_decode($this->data['Site']['last_name'], ENT_QUOTES);
        }
        $this->_settings();
        $this->loadModel('Industry');
        $this->set('industries', $this->Industry->find('list'));
        $this->crumbs = array();
        $formData = settings::formData(0);
        $price = format_price(19.66);
        $negativeAmountOnly = __("-$price", true);
        $negativeAmountWithPara = __("($price)", true);

        $negativeOptions = [
            \settings::NEGATIVE_CURRENCY_FORMAT_AMOUNT_ONLY => $negativeAmountOnly,
            \settings::NEGATIVE_CURRENCY_FORMAT_AMOUNT_WITH_PARENTHESIS => $negativeAmountWithPara,
        ];
        $formData['negative_currency_formats']['options'] =
        $this->set('negative_currency_formats', $negativeOptions);


        $this->crumbs[0]['title'] = __('General Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings');

        $this->set('title_for_layout',  __('General Settings', true));
    }

//    function beforeFilter() {
//        parent::beforeFilter();
//        $this->titleAlias = __('System', true);
//    }

    function admin_index()
    {
        $this->loadModel('Site');
        $this->Site->recursive = 0;
        $conditions = $this->_filter_params();
        $this->paginate['Site']['order'] = 'Site.id + 0 DESC';
        $this->set('sites', $this->paginate('Site', $conditions));
    }

    function admin_add()
    {
        $this->loadModel('Site');
        if (!empty($this->data)) {
            if ($this->Site->adminSave($this->data)) {
                $this->flashMessage(__('Site has been saved', true), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(__('Cannot save site', true));
            }
        }

        $this->loadModel('Country');
        $this->set('czones', $this->Country->getCountriesTimezones());
        $this->set('ccurr', $this->Country->getCountriesCurrencies());
        $this->_settings();
    }

    function admin_edit($id = false)
    {
        $this->loadModel('Site');
        $site = $this->Site->findById($id);

        if (!$site) {
            $this->flashMessage(__('Site not found', TRUE));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        if (!empty($this->data)) {
            if ($this->Site->adminSave($this->data)) {
                $this->flashMessage(__('Site has been saved', true), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(__('Cannot save site', true));
            }
        } else {
            $this->data = $site;
        }

        $this->loadModel('Country');
        $this->set('czones', $this->Country->getCountriesTimezones());
        $this->set('ccurr', $this->Country->getCountriesCurrencies());

        $this->_settings();
        $this->render('admin_add');
    }

    function admin_delete($id = FALSE)
    {
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        $module_name = __('site', true);
        $verb = __('has', true);
        if (!empty($_POST['ids']) && count($_POST['ids']) > 1) {
            $verb = __('have', true);
            $module_name = __('sites', true);
        }

        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), $module_name));
            $referer_url = !($this->Session->check('referer_url')) ? array('action' => 'index') : $this->Session->read('referer_url');
            $this->Session->delete('referer_url');
            $this->redirect($referer_url);
        }

        $this->loadModel('Site');
        $sites = $this->Site->find('all', array('conditions' => array('Site.id' => $id)));
        if (empty($sites)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect(array('action' => 'index'));
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes' && $this->Site->deleteAll(array('Site.id' => $id))) {
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                $referer_url = $this->_get_referer_path();
                $this->redirect(array('action' => 'index'));
            } else {
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            }
        }

        $this->set('sites', $sites);
        $this->set('module_name', $module_name);

        $this->set('title_for_layout', __('Delete Site', true));
    }

    function _final_url($final_url)
    {
        $final_url = explode('_', $final_url, 2);

        switch ($final_url[0]) {
            case 'quote':
                $this->redirect(array('controller' => 'quote_requests', 'action' => 'view', $final_url[1]));
                break;
            default:
                break;
        }
    }

    function _settings()
    {
        $form_data = settings::formData(InvoicesPlugin);
        $form_data += settings::formData(ClientsPlugin);
        $this->set('form_data', $form_data);
        $this->loadModel('Country');
        $this->loadModel('Language');
        $this->loadModel('Currency');
        $this->loadModel('Timezone');

        $this->set('countryCodes', $this->Country->getCountryList());
        $this->set('languageCodes', $this->Language->getLanguageList());
        $this->set('currencyCodes', $this->Currency->getCurrencyList());
        $this->set('timezones', $this->Timezone->getTimezoneList($this->data['Site']['timezone']));
        $this->set('dateFormats', getDateFormats(true));

        $this->set('image_settings', $this->Site->getImageSettings());

        $this->loadModel('Plan');
        $this->set('plans', $this->Plan->find('list', array('order' => 'Plan.display_order', 'fields' => 'id, plan_name')));
    }

    function admin_login_as($id = null)
    {
        $this->loadModel('Site');
        $site = $this->Site->findById($id);
        if (!$site) {
            $this->flashMessage(__('Site not found', true));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        file_put_contents('/tmp/' . md5($id), $id);
        $this->redirect('https://' . $site['Site']['subdomain'] . '/sites/login_as', 302);
    }

    function login_as()
    {
        $this->loadModel('Site');
        $this->loadModel('SiteToken');
        $row = $this->SiteToken->find(['token' => $_GET['token'], 'user_id' => getCurrentSite('id')]);
        $this->Session->destroy();
        $id = getCurrentSite('id');
        if (file_exists('/tmp/' . md5($id)) || $row) {

            $f = file_get_contents('/tmp/' . md5($id));
            if (empty($f) && !empty($row)) {
                $f = $row['SiteToken']['cc'];
            }

            if (empty($_GET['user'])) {
                $_SESSION['CurrentSite']['is_login_as_admin'] = $f;
                $owner['is_login_as_admin'] = $f;
            }
            $siteTokenExtraData = json_decode($row['SiteToken']['extra_data'], true);
            $owner = getCurrentSite();
            $owner['type'] = 'owner';
            $owner['staff_id'] = 0;
            $owner['is_super_admin'] = 1;
            if(!empty($siteTokenExtraData)) {
                $this->Session->write('LOGGED_AS_ADMIN', $siteTokenExtraData);
            }
            $this->Session->write('OWNER', $owner);
            $this->Session->write('STAFF', false);
            unlink('/tmp/' . md5($id));
            $this->layout = '';
            $this->add_stats(STATS_LOG_IN);
            $this->loadModel('Site');
            $this->Site->id = getCurrentSite('id');
            SiteService::updateById(getCurrentSite('id'), ['last_login' => date('Y-m-d H:i:s')]);
            if (isset($this->params['url']['next_url']) and $this->params['url']['next_url'] != "") {
                $this->redirect($this->params['url']['next_url']);
            }
            $previous_page = $this->Session->read("LOGIN_REDIRECT");
            if (empty($previous_page)) {
                if (ifPluginActive(WebsiteFrontPlugin)) {
                    $previous_page = '/owner/sites/dashboard';
                } else {
                    $previous_page = "/";
                }
            }
            $this->Session->delete("LOGIN_REDIRECT");
            $url = str_replace('http://', 'https://', Router::url($previous_page, true));
            $this->redirect($url);
        } else {
            $this->redirect(array('controller' => 'clients', 'action' => 'login'));
        }
    }

    function firstlogin($final_url = Null)
    {
        $row = false;
        if (isset($_GET['token']) && !empty($_GET['token'])) {
            $this->loadModel('SiteToken');
            $row = $this->SiteToken->find(['token' => $_GET['token'], 'user_id' => getCurrentSite('id')]);
        }
        if ($final_url != Null) {
            $this->Session->write('final_url', $final_url);
        }
        $this->loadModel('Site');
        if (isset($_GET['lang'])) {
            $this->loadModel('Site');
            $langData['Site']['id'] = getCurrentSite()['id'];
            $langData['Site']['language_code'] = ($_GET['lang'] == 'ar') ? 7 : 41;
            SiteService::updateById($langData['Site']['id'], ['language_code' => $langData['Site']['language_code']]);
            $session = new CakeSession;
            $session->write('CurrentSite', null);
        }

        $domain = str_replace(Beta_Domain, Domain_Short_Name, $_SERVER['HTTP_HOST']);
        $site = $this->Site->find(array('Site.subdomain' => $domain), false, false, -1);
        $id = $site['Site']['id'];
        $code = md5($id);
        $tmpFile = "/tmp/$code";
        if (file_exists($tmpFile) || $row) {

            $owner = $this->Site->find(array('Site.id' => $id), false, false, -1);
            $owner['Site']['type'] = 'owner';
            $owner['Site']['staff_id'] = 0;
            $this->Session->write('OWNER', $owner['Site']);
            unlink($tmpFile);
            $this->layout = '';
            $this->render('login_as');
//			$this->redirect(array('action' => 'first_settings', 'owner' => true));
        } else {
            $this->redirect(array('controller' => 'clients', 'action' => 'login'));
        }
    }

    function owner_first_settings()
    {
        clearWebSiteCache();
        $appsomo_plans = ['License Tier 1', 'License Tier 2', 'License Tier 3', 'License Tier 4', 'License Tier 5'];
        App::import('Vendor', 'settings');
        App::import('Vendor', 'sites_local');
        $this->set('bnf', Localize::get_business_number_fields());
        $this->loadModel('SupplierDirectory');
        $this->loadModel('Site');
        $this->loadModel('Client');
        $siteID = getCurrentSite('id');
        $firstLogin = $this->Site->field('first_login', array('Site.id' => $siteID));
        $this->loadModel('Stats');
        $resetCount = $this->Stats->find('count', [
            'conditions' => [
                'Stats.site_id' => $siteID,
                'action_key' => RESET_ACCOUNT_DATA_SUCCESS
            ]
        ]);

        if ($resetCount > 0) {
            $this->set('hide_country_selection', true);
        }

        if (!file_exists(WWW_ROOT . "/files/" . SITE_HASH . "/")) {
            mkdir(WWW_ROOT . "/files/" . SITE_HASH . "/");
        }

        if (!$firstLogin) {

            if (IS_REST) {
                http_response_code(422);
                echo json_encode([
                    "result" => "fail",
                    "code" => 422,
                    'error' => 'You Already Finish setting up your account'
                ]);
                die();
            }
            $this->redirect('/');
        }
        $this->set('no_header', 1);

        $site = $this->Site->findById($siteID);
        if (in_array($site['Plan']['plan_name'], $appsomo_plans)) {
            $this->set('hide_phones', true);
            $removeValidation = ['phone1', 'phone2'];
        } else {
            $removeValidation = [];
            $this->set('hide_phones', false);
        }
        $this->loadModel('Country');

        $czones = $this->Country->getCountriesTimezones();

        $ccurr = $this->Country->getCountriesCurrencies();

        if (!empty($this->data)) {
            if ($resetCount > 0){
                unset($this->data['Site']['country_code']);
                unset($this->data['Site']['currency_code']);
            }
            $orgData = $this->data;
            $land_page = getCurrentSite('landing_page');
            if (!empty($land_page)) {
                require_once dirname(getcwd()) . DS . 'vendors' . DS . 'Landing.php';
                $this->loadModel('Post');
                $this->loadModel('FollowUpAction');
                $this->FollowUpAction->deleteAll([]);
                $this->loadModel('FollowUpStatus');
                $this->FollowUpStatus->deleteAll([]);
                $main_actions = Landing::FollowUpAction($land_page);
                foreach ($main_actions as $action) {
                    $this->FollowUpAction->create();
                    $action_array = array();
                    $action_array['FollowUpAction']['name'] = $action;
                    $action_array['FollowUpAction']['item_type'] = Post::CLIENT_TYPE;
                    $this->FollowUpAction->save($action_array);
                }
                $main_status = Landing::FollowUpAction($land_page);
                foreach ($main_status as $status_row) {
                    $this->FollowUpStatus->create();
                    $status_array = array();
                    $status_array['FollowUpStatus']['name'] = $status_row['name'];
                    $status_array['FollowUpStatus']['color'] = $status_row['color'];
                    $status_array['FollowUpStatus']['item_type'] = Post::CLIENT_TYPE;
                    $this->FollowUpStatus->save($status_array);
                }

            }

            if(!empty($_FILES['data']['name']['Site']['site_logo']) && $_FILES['data']['size']['Site']['site_logo']>0) {
                $_FILES['site_logo']['name'] = $_FILES['data']['name']['Site']['site_logo'];
                $_FILES['site_logo']['tmp_name'] = $_FILES['data']['tmp_name']['Site']['site_logo'];
                $_FILES['site_logo']['size'] = $_FILES['data']['size']['Site']['site_logo'];
                $_FILES['site_logo']['type'] = $_FILES['data']['type']['Site']['site_logo'];

                $res = $this->_saveLogo();
                $this->data['Site']['site_logo']=$res['image_name'];
            }else{
                unset($this->data['Site']['site_logo']);
            }


            $result = $this->Site->saveFirstSettings($this->data, $removeValidation);

            if ($result['status']) {
                /*App::import('Vendor', 'daftra_sdk/bootstrap');
                $client = \sdk\Client::getByNumber(getCurrentSite('id'));
                if (!$client) {
                    $client_data = $this->data['Site'];
                    $client_data['default_currency_code'] = $client_data['currency_code'];
                    $client_data['business_name'] = getCurrentSite('business_name');
                    $client_data['email'] = getCurrentSite('email');
                    $client_data['client_number'] = getCurrentSite('id');

                    $client_data['is_offline'] = 1;
                    $client = new \sdk\Client($client_data);
                    if ($client->insert()) {
                        $Date = date('Y-m-d H:i:s', time());
                        if (in_array($this->data['Site']['country_code'], unserialize(FOLLOWUP_COUNTRIES_APPOINTMENTS))) {

                            $client->addAppointment(date('Y-m-d H:i:s', strtotime($Date . ' + 1 days')), "Registered a new account");
                        }
                    }
                }*/
                $updateSiteData = $this->Site->findById($siteID);
                $this->loadModel('Staff');
                $ownerUserStaff = $this->Staff->saveOwnerUser(site:$updateSiteData['Site']);
                $this->Site->reload_session();
                $db_config = new DATABASE_CONFIG();
                $config = $db_config->default;
                $JOURNAL_ACCOUNTING_SYSTEM = APP . 'other' . DS . "accounts_chart_" . JOURNAL_ACCOUNTING_SYSTEM . "_" . substr(CurrentSiteLang(), 0, 2) . ".sql";
                if (settings::getValue(0, 'journal_accounting_system_installed', null, false, false) != "1") {
                    settings::setValue(0, 'journal_accounting_system_installed', 1);
                    settings::setValue(0, 'current_journal_accounting_system', JOURNAL_ACCOUNTING_SYSTEM);
                    if (!file_exists($JOURNAL_ACCOUNTING_SYSTEM)) {
                        $JOURNAL_ACCOUNTING_SYSTEM = APP . 'other' . DS . "accounts_chart_" . JOURNAL_ACCOUNTING_SYSTEM . "_en.sql";
                    }
                    exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} --default-character-set=utf8 {$config['database']} < " . $JOURNAL_ACCOUNTING_SYSTEM, $out, $status);
                    if ($status != 0) {
                        notify_admin_fetal_error(__FUNCTION__ . " Error In Line " . __LINE__);
                        $this->add_stats(STATUS_JOURNAL_ACCOUNTING_SYSTEM_IMPORT_FAIL, [print_r($out, true)]);
                    }
                }

                if ($result['error_upload_logo']) {
                    $this->flashMessage(__('Site Logo not uploaded invalid file type', true));
                }

                $this->loadModel('Plugin');
                $this->loadModel('SitePlugin');
                $site = getCurrentSite();
                $activePlugins = array_keys($this->Plugin->getActiveStickyPlugin());
                foreach ($activePlugins as $plugin_id) {
                    if ($plugin_id == 0) {
                        continue;
                    }
                    $this->Site->owner_update_plugin($plugin_id, "true", false, false, false);
                }
                Cache::delete('current_plugin_' . $site['id']);
                getCurrentPlugin();
                delete_menus();
                $this->loadModel('User');
                $user = $this->User->find('first', ['conditions' => ['User.id' => $site['user_id']]]);
                if (!empty($user['User']['trk'])) {
                    $this->loadModel('SystemIndustry');
                    $forceCondition = [
                        'active' => 1,
                        ' CONCAT(\',\',force_trk,\',\')  LIKE  \'%,' . $user['User']['trk'] . ',%\'',
                        'force_trk IS NOT NULL'
                    ];
                    $confirmCondition = [
                        'active' => 1,
                        ' CONCAT(\',\',trk,\',\')  LIKE  \'%,' . $user['User']['trk'] . ',%\'',
                        'trk IS NOT NULL'
                    ];
                    $force_si = $this->SystemIndustry->find('first', ['conditions' => $forceCondition]);
                    $confirm_si = $this->SystemIndustry->find('first', ['conditions' => $confirmCondition]);

                    if (!empty($force_si)) {
                        $this->Site->saveField('system_industry_id', $force_si['SystemIndustry']['id']);
                        $this->SystemIndustry->CloneTemplate($force_si['SystemIndustry']['id']);
                        $this->Client->resetClientLocationAndCurrencyAfterClone();
                        $this->Plugin->enable_ksa_invoices_plugin();
                        $this->loadModel('Journal');
                        $this->Journal->construct_jounals();
                        clearWebSiteCache();
                        $this->redirect('/?welcome=1');
                    } elseif (!empty($confirm_si)) {
                        $this->redirect(array('action' => 'industry_confirm', $confirm_si['SystemIndustry']['id']));
                    }
                }

                if (!empty($land_page)) {
                    $this->loadModel('LandingPage');
                    $lp = $this->LandingPage->find('first', array('conditions' => array('(LandingPage.system_industry_id is not null or LandingPage.system_industry_id=0)', 'LandingPage.permalink' => $land_page)));
                    if (!empty($lp)) {
                        $this->redirect(array('action' => 'industry_confirm', $lp['LandingPage']['system_industry_id']));
                    }
                }

                if (IS_REST) {
                    $this->Site->owner_update_plugin(PosPlugin, "true", false, false, false);
                    $this->set('rest_model_name', "Site");
                    $this->set("rest_item", $this->Site->find("first", ["fields" => array_keys($orgData['Site']) + ["id" => "id"], "conditions" => ["Site.id" => getCurrentSite('id')]]));
                    $this->render('view');
                    return;
                }

                $this->redirect(array('action' => 'select_industry', 1));
            } else {
                if (IS_REST) {
                    http_response_code(400);
                    echo json_encode([
                        "result" => "failed",
                        "code" => 400,
                        'validation_errors' => $this->Site->validationErrors
                    ]);
                    die();
                }
                $this->flashMessage(__('Please, fix errors below.', true));
            }
        } else {

            $this->data = $site;


                $this->data['Site']['country_code'] = IP2CountryCode(get_real_ip());
                if (!empty($czones[$this->data['Site']['country_code']])) {
                    $this->data['Site']['timezone'] = $czones[$this->data['Site']['country_code']][0];
                }
                if(in_array($this->data['Site']['country_code'], ['AU', 'CA', 'US', 'FR', 'RU', 'MX', 'UK', 'CN', 'NZ'])){
                    $location = IP2Location(get_real_ip());
                    $location_state = str_replace(" ", "_", $location['state']);
                    $location_city = str_replace(" ", "_", $location['city']);
                    $this->loadModel('Timezone');
                    $location_timezone = $this->Timezone->find('first', ['conditions' => [
                        'Timezone.title NOT' => null,
                        'Timezone.title <>' => '',
                        'Timezone.country_id' => $this->Country->field('id', ['Country.code' => $this->data['Site']['country_code']]),
                        "OR" => [
                            "Timezone.zone_name LIKE '%$location_state%'",
                            "Timezone.zone_name LIKE '%$location_city%'",
                            "Timezone.offset = '{$location["timezone"]}'",
                        ]
                    ]]);
                    if(!empty($location_timezone)){
                        $this->data['Site']['timezone'] = $location_timezone['Timezone']['id'];
                    }
                }

            if (empty($this->data['Site']['currency_code'])) {
                if (isset($ccurr[$this->data['Site']['country_code']])) {
                    $this->data['Site']['currency_code'] = $ccurr[$this->data['Site']['country_code']];
                }
            }
        }

        $this->_settings();
        if (!empty($this->data['Site']['industry_id'])) {
            $this->loadModel('Industry');
            $this->set('industries', $this->Industry->find('list', array('conditions' => array('Industry.id' => $this->data['Site']['industry_id']))));
        }
        $this->set('logoSnippet', $this->get_snippet('quick-logo-upload', false, false));
        $this->set('loginMessage', $this->get_snippet('first-login-message', false, false));

        $this->set('countriesJSON', $this->Country->getCountriesJSON());
        $this->set(compact('czones', 'ccurr'));

        $this->set('languageCodes', $this->Language->getLanguageList());

        //debug($this->data['Site']['timezone']);

        $find = $this->SupplierDirectory->find('first', array('conditions' => array('SupplierDirectory.site_id' => getCurrentSite('id'))));
        $this->set('SupplierDirectory', $find);
        $this->set('title_for_layout', __('Welcome to your account', true));

        $this->set('google_analytics_head',get_portal_setting_value('google_analytics_head'));
        $this->set('google_analytics_body',get_portal_setting_value('google_analytics_body'));
        $this->set('positions', $this->Site->get_site_positions());
        $this->set('company_sizes', $this->Site->get_site_company_size());
    }

    function owner_welcome()
    {
        $this->set('welcomeContent', $this->get_snippet('welcome'));
        $this->set('title_for_layout', __('Welcome', true));
    }

    function owner_upload_image()
    {
        $this->loadModel('Site');
        //Configure::write('debug', 0);

        if (!empty($_FILES['site_logo'])) {
//            $image = $_FILES['site_logo'];
//            switch ($image['error']) {
//                case UPLOAD_ERR_CANT_WRITE:
//                case UPLOAD_ERR_NO_TMP_DIR:
//                case UPLOAD_ERR_PARTIAL:
//                case UPLOAD_ERR_EXTENSION:
//                    $error = __('Could not upload this image', true);
//                    break;
//                case UPLOAD_ERR_INI_SIZE:
//                case UPLOAD_ERR_FORM_SIZE:
//                    $error = __('Sorry, Couldn\'t upload your logo, File size is too large', true);
//                    break;
//                case UPLOAD_ERR_NO_FILE:
//                    $error = __('No uploaded file', true);
//                    break;
//            }
//
//            if (empty($error)) {
//                if ($image['size'] / 1024 > 25000) {
//                    $error = __('File size large', true);
//                } else {
//                    $info = getimagesize($image['tmp_name']);
//                    $compatMimes = array('image/jpeg', 'image/gif', 'image/png');
//                    $ext = strtolower(substr($image['name'], strrpos($image['name'], '.')));
//
//                    if (!$info || !in_array($info['mime'], $compatMimes) || !in_array($ext, array('.jpg', '.gif', '.jpeg', '.png'))) {
//                        $error = __('Invalid image type', true);
//                    } else {
//                        $destDir = WWW_ROOT . DS . 'files' . DS . 'images' . DS . 'tmp-logos';
//                        $name = substr($image['name'], 0, strrpos($image['name'], '.'));
//
//                        $destFile = uniqid() . '_' . (Inflector::slug($name) . $ext);
//                        App::import('Vendor', 'S3LogoUploader', array('file' => 'S3LogoUploader.php'));
//                        S3LogoUploader::createS3Client();
//                        $res=S3LogoUploader::uploadToS3("site-logos/".$destFile,file_get_contents($image['tmp_name']));
//                            $success = __('logo has been uploaded click save to save changes', true);
//                            $this->set('image_name', $destFile);
//                            $this->set('image_path', '/files/images/tmp-logos/' . $destFile . '?w=250&h=100');
//
//                    }
//                }
//            }
            $imageResult = $this->_saveLogo();
//            print_r($this->Site->validationErrors);
            if (!$imageResult) {
                $error = $this->Site->validationErrors['site_logo'];
                $this->set(compact('error'));
            } else {
                $success = __('logo has been uploaded click save to save changes', true);
                $this->set('image_name', $imageResult['image_name']);
                $this->set('image_path', $imageResult['image_path']);
            }

            if (!empty($success)) {
                $this->set(compact('success'));
            }

            $this->Site->reload_session();
        }

        $this->layout = false;
    }

    function _saveLogo()
    {
        if (!empty($_FILES['site_logo'])) {
            $image = $_FILES['site_logo'];
            switch ($image['error']) {
                case UPLOAD_ERR_CANT_WRITE:
                case UPLOAD_ERR_NO_TMP_DIR:
                case UPLOAD_ERR_PARTIAL:
                case UPLOAD_ERR_EXTENSION:
                    $error = __('Could not upload this image', true);
                    break;
                case UPLOAD_ERR_INI_SIZE:
                case UPLOAD_ERR_FORM_SIZE:
                    $error = __('Sorry, Couldn\'t upload your logo, File size is too large', true);
                    break;
                case UPLOAD_ERR_NO_FILE:
                    $error = __('No uploaded file', true);
                    break;
            }

            if (empty($error)) {
                if ($image['size'] / 1024 > 25000) {
                    $error = __('File size large', true);
                    $this->Site->invalidate('site_logo', $error);
                    return false;
                } else {
                    $info = getimagesize($image['tmp_name']);
                    $compatMimes = array('image/jpeg', 'image/gif', 'image/png');
                    $ext = strtolower(substr($image['name'], strrpos($image['name'], '.')));

                    if (!$info || !in_array($info['mime'], $compatMimes) || !in_array($ext, array('.jpg', '.gif', '.jpeg', '.png'))) {
                        $error = __('Invalid image type', true);
                        $this->Site->invalidate('site_logo', $error);
                        return false;
                    } else {
                        $name = substr($image['name'], 0, strrpos($image['name'], '.'));

                        $destFile = uniqid() . '_' . (Inflector::slug($name) . $ext);
                        App::import('Vendor', 'S3LogoUploader', array('file' => 'S3LogoUploader.php'));
                        S3LogoUploader::createS3Client();
                        $res = S3LogoUploader::uploadToS3("site-logos/" . $destFile, file_get_contents($image['tmp_name']));
                        return ['status'=>true,'image_path'=>'/files/images/site-logos/' . $destFile . '?w=250&h=100','image_name'=>$destFile];

                    }
                }
            } else {
                $this->Site->invalidate('site_logo', $error);

                return false;
            }

        }
        return true;
    }
    function owner_account_info()
    {
        $this->loadModel('Site');
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $site = $this->Site->findById(getCurrentSite('id'));
        // List site limitations
        $limitation_repo = new LimitationRepository();
        $site_limitations = $limitation_repo->getActiveSiteLimitations($site['Site'], ['custom_domain_parking']);
        $site_limitations = $limitation_repo->formatLimitationForView($site_limitations);
        $this->set('site_limitations', $site_limitations);

        $owner = getAuthOwner();
        $this->set('owner', $owner);
        $this->set('site', $site);
//        $this->set('limits', $this->Site->getLimits());
        $this->set('is_free', getCurrentSite('plan_id') == 1);
        $this->set('title_for_layout', __('Account Information', true));
        $this->loadModel('Timezone');
        $zname = $this->Timezone->field('title', array('Timezone.id' => $site['Site']['timezone']));
        if (empty($zname))
            $zname = $this->Timezone->field('zone_name', array('Timezone.id' => $site['Site']['timezone']));

        $this->set('pincode', $this->Site->get_pincode());
        $this->set('timezone', $zname);

        $canCreateCustomDomain = \Izam\Limitation\LimitationService::checkPlanLimitationExists($site, \Izam\Limitation\Utils\LimitationUtil::CUSTOM_DOMAIN_PARKING);

        $this->set('canCreateCustomDomain', $canCreateCustomDomain);
    }

    function owner_industry_json_find()
    {
        if (!empty($_GET['q'])) {
            $this->loadModel('Industry');
            $value = mysql_escape_string($_GET['q']);
            $inds = array();
            $conditions = array();
            $conditions['OR'] = array("Industry.name like '$value%'", "Industry.name like '% $value%'");
            $inds = $this->Industry->find('all', array('conditions' => $conditions, 'limit' => 15, 'order' => 'Industry.name', 'recursive' => -1));
            $result = array();
            foreach ($inds as $ind) {
                $result[] = array(
                    'name' => $ind['Industry']['name'],
                    'id' => $ind['Industry']['id'],
                    'image' => Router::url($ind['Industry']['image_thumb_full_path']),
                );
            }


            echo json_encode($result);
            die();
        }
    }

    public function owner_upgrade()
    {
        if (!isOwner()) {
            $this->flashMessage(__('Please contact the site admin to upgrade or renew your account', true));
            $this->Redirect('/');
        }
        $token = $this->__createToken();
        $this->redirect("https://" . Portal_Full_Name . getSiteLangUrl("/sites/upgrade/" . getCurrentSite('id') . "?token=$token"));
    }

    public function owner_connect($payment, $action = "connect", $id = null)
    {
        if ($payment == 'squareup') {

            $real_payment = 'square';
        } else {
            $real_payment = $payment;
        }
        $owner = getAuthOwner();
        $site = getCurrentSite();

        if ($action == "update") {

            $this->loadModel('Oauth');
            $this->loadModel('SitePaymentGateway');
            $this->SitePaymentGateway->initiateGateways($site['id'], $site['country_code']);
            $payment_row = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.payment_gateway' => $real_payment)));

            $row = $this->Oauth->read(null, $id);

            if (!$row) {
                $this->flashMessage('Errors connecting your account please try again');
                $this->redirect(array('controller' => 'payment_gateways'));
            }
            if ($row['Oauth']['status'] == 0) {
                $this->flashMessage('Errors connecting your account please try again');
                $this->redirect(array('controller' => 'payment_gateways'));
            }
            $data = json_decode($row['Oauth']['data'], true);

            if ($payment != $row['Oauth']['payment']) {
                $this->flashMessage('Errors connecting your account');
                $this->redirect(array('controller' => 'payment_gateways'));
            }
            $payment = $row['Oauth']['payment'];

            $this->SitePaymentGateway->id = $payment_row['SitePaymentGateway']['id'];
            if ($payment == 'stripe') {
                $datatosave = array();
                $datatosave['SitePaymentGateway']['username'] = $data['access_token'];
                $datatosave['SitePaymentGateway']['option1'] = $data['stripe_publishable_key'];
                $datatosave['SitePaymentGateway']['option2'] = 1;
                $datatosave['SitePaymentGateway']['active'] = 1;
                $this->SitePaymentGateway->save($datatosave);
                $this->flashMessage(__('Your stripe account is now connected', true), 'Sucmessage');
            } elseif ($payment == 'squareup') {
                $datatosave['SitePaymentGateway']['username'] = SQUAREUP_APPLICATION;
                $datatosave['SitePaymentGateway']['option1'] = $data['access_token'];
                $datatosave['SitePaymentGateway']['option2'] = 1;
                $datatosave['SitePaymentGateway']['active'] = 1;
                $this->SitePaymentGateway->save($datatosave);
                $this->flashMessage(__('Your squareup account is now connected', true), 'Sucmessage');
            }
            $this->redirect(array('controller' => 'payment_gateways'));
        }


        $token = $this->__createToken();

        $this->redirect("https://" . Portal_Full_Name . "/connect/" . $payment . "/" . getCurrentSite('id') . '?token=' . $token);
    }

    public function owner_upgrade_staff()
    {
        $site = getAuthOwner('staff_id');
        if ($site != 0) {
            $this->Redirect('/');
        }
        $token = $this->__createToken();

        $this->redirect("https://" . Portal_Full_Name . "/upgrade_staff/" . getCurrentSite('id') . '?token=' . $token);
    }

    function __createToken()
    {
        $data = json_encode(array('site_id' => getCurrentSite('id')));
        $token = sha1(time() . $data);

        file_put_contents("/tmp/$token", $data);
        $this->loadModel('SiteToken');
        $this->SiteToken->id = $this->SiteToken->find(['user_id' => getCurrentSite('id')])['SiteToken']['id'];
        $result = PortalSiteToken::create(['user_id' => getCurrentSite('id'), 'token' => $token, 'cc' => 'user']);
        return $token;
    }

    public function owner_renew()
    {

        if (!isOwner()) {
            $this->flashMessage(__('Please contact the site admin to upgrade or renew your account', true));
            $this->Redirect('/');
        }
        $token = $this->__createToken();
        $this->redirect("https://" . Portal_Full_Name . getSiteLangUrl("/sites/renew/" . getCurrentSite('id') . "?token=$token"));
    }

    public function owner_cancel()
    {
        if (!isOwner()) {
            $this->flashMessage(__('Please contact the site admin to upgrade or renew your account', true));
            $this->Redirect('/');
        }
        $token = $this->__createToken();
        $this->redirect("https://" . Portal_Full_Name . getSiteLangUrl("/sites/cancel/" . getCurrentSite('id') . "?token=$token"));
    }

    public function owner_increase_limit($type)
    {
        $site_id = getCurrentSite('id');
        if (!isOwner()) {
            $this->flashMessage(__('Please contact the site admin to upgrade or renew your account', true));
            $this->Redirect('/');
        }
        $token = $this->__createToken();
        $this->redirect("https://" . Portal_Full_Name . getSiteLangUrl("/sites/increase_limit/$site_id/$type?token=$token"));
    }

    public function owner_contact()
    {
        $this->redirect(array('controller' => 'sites_enquiries', 'action' => 'index'));
        $this->loadModel('Feedback');
        if (!empty($this->data)) { // this code looks deprecated because the form in the view is using /owner/sites_enquiries/index
            $this->data['Feedback']['site_id'] = getCurrentSite('id');
            if (PortalFeedback::create($this->data['Feedback'])) {
                $site_name = getCurrentSite('business_name');
                $this->Email->to = $this->config['txt.admin_mail'];
                $this->Email->from = "{$this->config['txt.site_name']} <{$this->config['txt.send_mail_from']}>";
                $this->Email->subject = "[{$site_name}]" . $this->data['Feedback']['subject'];
                $this->Email->sendAs = 'html';
                $this->Email->template = 'owner_feedback';

                $this->Email->send();

                $this->flashMessage(__('Your feedback has been sent', true), 'Sucmessage');
                $this->Redirect(array('action' => 'contact'));
            } else {
                $this->flashMessage(__('Could not send feedback', true));
            }
        }

        $this->set('reasons', $this->Feedback->reasons);
    }

    function admin_bulk_sql()
    {
        $this->loadModel('Site');
        $sites = $this->Site->find('all');
        $this->set(compact('sites'));
        $configs = Set::combine($sites, '{n}.Site.id', '{n}.Site.db_config');
        if (!empty($this->data)) {
            $results = array();
            $queries = explode(';', $this->data['Site']['queries']);
            $errors = 0;
            foreach ($this->data['Site']['sites'] as $site_id) {
                $config = $configs[$site_id];
                extract(json_decode($config, 1));
                if (empty($driver)) {
                    $driver = 'mysql';
                }
                $pdo = new PDO("$driver: host=$host; dbname=$database", $login, $password);
                foreach ($queries as $query) {
                    $result = $pdo->exec($query);
                    $results[$site_id][] = $result;
                    if ($result === false) {
                        $errors++;
                    }
                }
            }
            if (!$errors) {
                $this->flashMessage('Queries executed successfully ', 'Sucmessage');
            } else {
                $this->flashMessage($errors . ' Errors occurred ');
            }
            $this->redirect(array('action' => $this->action));
        }
    }

    function email_updates()
    {
        $this->loadModel('EmailUpdate');
        $current_aliases = file_get_contents('/etc/aliases');
        //print_r($current_aliases);
        $rows = $this->EmailUpdate->find('all');
        foreach ($rows as $row) {
            $email_alias = str_replace("@", "_", $row['EmailUpdate']['new_email']);
            $old_email_alias = str_replace("@", "_", $row['EmailUpdate']['old_email']);
            //print_r($email_alias);
            $pos = strpos($current_aliases, $row['EmailUpdate']['old_email']);
            print_r($pos);
            if ($pos === false) {
                $current_aliases .= "\n" . $email_alias . ":" . $row['EmailUpdate']['new_email'];
            } else {
                $current_aliases = str_replace(array($old_email_alias, $row['EmailUpdate']['old_email']), array($email_alias, $row['EmailUpdate']['new_email']), $current_aliases);
            }
            PortalEmailUpdate::where('id', $row['EmailUpdate']['id'])->delete();
            //print_r($current_aliases);
        }
        file_put_contents("/etc/aliases", $current_aliases);
        $this->layout = 'box';
        $this->render = false;
        die();
    }


    function renew_token($id = "null", $mode = "live")
    {
        $this->loadModel('Site');
        echo "Starting Renew Token Cron\n\r";
        $this->config = parse_ini_file(WWW_ROOT . '../app_config.ini');

        Configure::write('debug', 0);
        App::import('Vendor', 'oilogger');
        Oilogger::initSession('renew_token' . date('Y_m_d') . '.txt');
        Oilogger::log('Started......................');
        Oilogger::log('Current Date: ' . date('Y-m-d H:i:s'));

        $this->loadModel('Oauth');
        if ($id != "null") {
            //$conditions['Oauth.payment']='stripe';
            $conditions['Oauth.status'] = 1;
            $conditions[] = 'NOW() > DATE_ADD(Oauth.modified,  INTERVAL 6 DAY)';
        }
        if ($id != "null") {
            unset($conditions);
            $conditions['Oauth.status'] = 1;
            $conditions['Oauth.id'] = intval($id);
        }

        $rows = $this->Oauth->find('all', array('conditions' => $conditions));
        Oilogger::log('Total Token To Renew ' . count($rows));
        // print_r($rows);
        foreach ($rows as $row) {
            if ($row['Site']['status'] != SITE_STATUS_ACTIVE) {
                continue;
            }
            Oilogger::log('Site ' . $row['Site']['id']);
            if (empty($row['Site']['id'])) {
                Oilogger::log('Site not Found ' . $row['Oauth']['site_id']);
                continue;
            }

            $Oauthdata = array();
            $this->Oauth->id = $row['Oauth']['id'];
            $config = json_decode($row['Site']['db_config'], true);
            ConnectionManager::getDataSource('default')->swtich_db($config);
            $this->loadModel('SitePaymentGateway');
            $this->SitePaymentGateway->getDataSource()->swtich_db($config);

            $token_data = json_decode($row['Oauth']['data'], true);
            if ($row['Oauth']['payment'] == 'squareup') {
                $real_payment = 'square';
            } else {
                $real_payment = $row['Oauth']['payment'];
            }


            if ($row['Oauth']['payment'] == "stripe") {
                $token_data = json_decode($row['Oauth']['data'], true);
                $data = array("client_secret" => STRIPE_CLIENT_SECRET, "client_id" => $token_data['stripe_user_id'], "refresh_token" => $token_data['refresh_token'], "grant_type" => "refresh_token");
                $ch = curl_init('https://connect.stripe.com/oauth/token');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $result = curl_exec($ch);
                if ($mode == "debug") {
                    Oilogger::log('Result ' . print_r($result, true));
                    echo $this->Oauth->id . "<br>";
                    continue;
                }
                $err = curl_errno($ch);
                if ($err == 0) {
                    $Oauthdata['Oauth']['data'] = $result;
                    PortalOauth::create($Oauthdata['Oauth']); ;
                    $new = json_decode($result, true);
                    Oilogger::log('Renew result ' . $result);
                    $datatosave = array();
                    $datatosave['SitePaymentGateway']['username'] = $new['access_token'];
                    $datatosave['SitePaymentGateway']['option1'] = $new['stripe_publishable_key'];
                    $datatosave['SitePaymentGateway']['option2'] = 1;
                    $save = true;
                }
            } elseif ($row['Oauth']['payment'] == 'squareup') {
                $token_data = json_decode($row['Oauth']['data'], true);
                $data = array("access_token" => $token_data['access_token']);
                $data_string = json_encode($data);
                $ch = curl_init("https://connect.squareup.com/oauth2/clients/" . SQUAREUP_APPLICATION . "/access-token/renew");
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Client ' . $this->config['txt.squareup_client_secret'],
                    'Content-Type: application/json', 'Content-Length: ' . strlen($data_string)));

                $result = curl_exec($ch);
                if ($mode == "test") {
                    echo $this->Oauth->id . "<br>";
                    echo $result;
                    echo "<br>";
                    continue;
                }
                $err = curl_errno($ch);
                if ($err == 0) {
                    $new = json_decode($result, true);
                    if (isset($new['access_token'])) {
                        $save = true;
                        $Oauthdata['Oauth']['data'] = $result;
                        PortalOauth::create($Oauthdata['Oauth']);
                    } else {
                        $Oauthdata['Oauth']['status'] = 0;
                        PortalOauth::create($Oauthdata['Oauth']);
                        $save = false;
                    }
                    $datatosave = array();
                    $datatosave['SitePaymentGateway']['username'] = SQUAREUP_APPLICATION;
                    $datatosave['SitePaymentGateway']['option1'] = $new['access_token'];
                    $datatosave['SitePaymentGateway']['option2'] = 1;
                }
            }


            //  print_r($this->SitePaymentGateway->query("select database()"));
            //var_dump($save);
            if ($save == true) {
                //echo $real_payment."\n";
                //	print_r($datatosave);
                $payment_row = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.payment_gateway' => $real_payment)));
                $this->SitePaymentGateway->id = $payment_row['SitePaymentGateway']['id'];
                $row = $this->SitePaymentGateway->save($datatosave);
            }
            unset($datatosave);
            $save = false;
            // print_r($payment_row);
        }
        die();
    }

    function owner_select_industry($auto_select = false)
    {
        $this->loadModel('ActionLine');
        $actionlinecount = $this->ActionLine->find('count');
        if ($actionlinecount > 0 && !isset($_GET['force'])) {
            $this->redirect('/');
        }
        $site = getCurrentSite();
        $this->set('site', $site);
        $this->loadModel('SystemIndustry');
        if ($site['id'] % 300 == 1) $this->SystemIndustry->update_stats();
        if ($auto_select == true) {
            $this->SystemIndustry->recursive = -1;
            $rows = $this->SystemIndustry->find('all', array('order' => 'SystemIndustry.display_order asc', 'conditions' => array('active' => 1, 'SystemIndustry.language_id' => $site['language_code'])));
            foreach ($rows as $row) {
                $keywords = explode(',', $row['SystemIndustry']['keywords']);
                // sanitize keywords data
                $keywords = array_map('trim', $keywords);
                $keywords = array_filter($keywords);
                foreach ($keywords as $keyword) {
                    $pos = strpos($site['business_name'], $keyword);
                    if ($pos !== false) {
                        $this->redirect(array('action' => 'industry_confirm', $row['SystemIndustry']['id']));
                    }
                }
            }
            $this->redirect(array('action' => 'select_industry'));
            die();
        }
        $country = $_SESSION['OWNER']['country_code'];
        $si = $this->SystemIndustry->find('all', array('order' => ['SystemIndustry.display_order asc', 'SystemIndustry.last_month_uses_count DESC'], 'conditions' => ['SystemIndustry.language_id' => $site['language_code'], 'SystemIndustry.active' => 1, ['OR' => ['SystemIndustry.countries LIKE' => "%$country%", 'SystemIndustry.countries IS NULL', 'SystemIndustry.countries' => '']]]));
        $si_list = $this->SystemIndustry->find('list', ['order' => ['SystemIndustry.display_order asc', 'SystemIndustry.last_month_uses_count DESC'], 'conditions' => ['SystemIndustry.language_id' => $site['language_code'], 'SystemIndustry.active' => 1, ['OR' => ['SystemIndustry.countries LIKE' => "%$country%", 'SystemIndustry.countries IS NULL', 'SystemIndustry.countries' => '']]]]);

        $names = array_values($si_list);
        $conditions['SystemIndustry.active'] = 1;
        if (count($names) > 1) {
            $conditions['SystemIndustry.name not'] = $names;
        } else {
            $conditions['SystemIndustry.name !='] = $names[0];
        }
        $conditions['SystemIndustry.language_id'] = $site['language_code'];
        $si_other = $this->SystemIndustry->find('all', array('order' => 'SystemIndustry.display_order asc', 'conditions' => $conditions));
//       $si_other = [];
        $all_si = array_merge_recursive((array)$si, $si_other);
        $this->set('si', $all_si);
        $this->loadModel('LandingPagesCategory');
        $language_id = getCurrentSite('language_code');
        $this->set('current_language', $language_id);
        $landing_page_industries = $this->LandingPagesCategory->find('all', array('conditions' => array('LandingPagesCategory.type' => 0, 'LandingPagesCategory.language_id' => $language_id), 'Order'=>array('id'=>'ASC')));
        $categorized_industries = [];
        foreach ($landing_page_industries as $industries) {
            if (empty($industries['SystemIndustry'])) {
                continue;
            }
            foreach ($industries['SystemIndustry'] as $industry) {
                $categorized_industries[] = $industry['id'];
            }
        }
        $other_industries = $this->SystemIndustry->find('all', ['conditions' => ['SystemIndustry.active' => 1, 'SystemIndustry.language_id' => $site['language_code'], 'SystemIndustry.id NOT' => $categorized_industries, ['OR' => ['SystemIndustry.countries LIKE' => "%$country%", 'SystemIndustry.countries IS NULL', 'SystemIndustry.countries' => '']]]]);
        $this->set('landing_page_industries', $landing_page_industries);
        $this->set('other_landing_page_industries', $other_industries);
        $this->layout = '';
        $this->view = 'izam';
        $this->render('select-industry/index');
    }

    function owner_update_currency($currency_code)
    {
        $this->loadModel('Site');
        if (!check_permission(Edit_General_Settings)) return false;
        set_time_limit(999999);
        ini_set("memory_limit", "10G");

        $this->loadModel('Currency');


        $new_curr = $this->Currency->findByCode($currency_code);
        if (!empty($new_curr)) {
            $site = getCurrentSite();
            $this->Site->id = $site['id'];
            SiteService::updateById($site['id'], ['currency_code' => $currency_code]);

            App::import('Vendor', 'settings');
            $this->data[ExpensesPlugin]['journals_local_currency_code'] = $new_curr['Currency']['code'];
            settings::setData($this->data);
            $this->loadModel('Journal');
            $this->loadModel('StockTransaction');

            $id = 0;
            while ($journals = $this->Journal->find('all', array('conditions' => ['Journal.id > ' . $id], 'limit' => 100, 'order' => 'Journal.id DESC'))) {
                foreach ($journals as $journal) {
                    $this->Journal->save_journal($journal, false, true);
                    $id = $journal['Journal']['id'];
                }
                unset($journals);
            }

            $id = 0;
            $this->StockTransaction->average_on_all();


        }

        $this->autoRender = $this->autoLayout = false;

    }

    function owner_industry_confirm($id, $confirm = "")
    {
        $this->loadModel('Site');
        $this->loadModel('SitePlugin');
        $this->loadModel('Plugin');
        $this->loadModel('Client');

        set_time_limit(300);
        $this->loadModel('ActionLine');
        $actionlinecount = $this->ActionLine->find('count');
        if ($actionlinecount > 0 && !isset($_GET['force'])) {
            $this->redirect('/');
        }
        $site = getCurrentSite();
        if ($id == -1) {
            $mainPlugin = [AccountingPlugin, InventoryPlugin, StaffPlugin, ExpensesPlugin];
            if ($site['country_code'] == 'SA') {
                $mainPlugin[] = EINVOICE_SA_PLUGIN;
            }
            foreach ($mainPlugin as $plugin_id) {
                $this->Site->owner_update_plugin($plugin_id, "true", false, false, false);
            }
            Cache::delete('current_plugin_' . $site['id']);
            getCurrentPlugin();
            delete_menus();
            clearWebSiteCache();
            $this->redirect('/v2/owner/plugin-manager/1');
        }
        clearWebSiteCache();
        $this->loadModel('SystemIndustry');
        $row = $this->SystemIndustry->read(null, $id);
        if (!$row) {
            $this->redirect(array('action' => 'select_industry'));
        }

        if ($confirm != "") {
            $this->SystemIndustry->CloneTemplate($id);
            $this->Client->resetClientLocationAndCurrencyAfterClone();
            $this->Plugin->enable_ksa_invoices_plugin();
            $this->loadModel('Journal');
            $this->Journal->construct_jounals();
            $this->Site->activate_plugins_depending_on_position_and_company_size();
            clearWebSiteCache();
            $this->Site->save(['first_login' => 0, 'system_industry_id' => $id, 'id' => $site['id']]);
            $this->reloginAsOwnerStaffUser();
            $this->redirect('/?welcome=1');
        }

        $this->set('id', $id);
        $this->set('Industry', $row);
        $this->set('site', getCurrentSite());
        $this->layout = '';
    }

    function owner_adv_industry_json_find()
    {
        $this->loadModel('Site');
        $site = getCurrentSite();
//$site['language_code'];
        $this->loadModel('SystemIndustry');
        $country = $_SESSION['OWNER']['country_code'];

        if (!empty($_GET['q'])) {

            $value = mysql_escape_string($_GET['q']);
            $inds = array();
            $conditions = array();
            $conditions['SystemIndustry.active'] = 1;
            $conditions['SystemIndustry.language_id'] = $site['language_code'];
            $conditions['AND']['OR'][] = "SystemIndustry.countries LIKE '%$country%'";
            $conditions['AND']['OR'][] = 'SystemIndustry.countries IS NULL';
            $conditions['AND']['OR'][] = 'SystemIndustry.countries = ""';
            $conditions['OR'][] = "SystemIndustry.name like '%$value'";
            $conditions['OR'][] = "SystemIndustry.name like '%$value%'";
            $conditions['OR'][] = "FIND_IN_SET('$value',REPLACE(SystemIndustry.keywords,', ',','))";
            $conditions['OR'][] = "SystemIndustry.is_sticky = 1";

            $value = $_GET['q'];
            $values = explode(' ', $value);
            foreach ($values as $keyword) {
                $keyword = mysql_escape_string($keyword);
                $conditions['OR'][] = "FIND_IN_SET('$keyword',REPLACE(SystemIndustry.keywords,', ',','))";
            }
//            $conditions['language_id']=$site['language_code'];
            $inds = $this->SystemIndustry->find('all', array('order' => 'SystemIndustry.display_order asc, SystemIndustry.name asc', 'conditions' => $conditions, 'recursive' => -1));
            $inds_list = $this->SystemIndustry->find('list', array('order' => 'SystemIndustry.display_order asc, SystemIndustry.name asc', 'conditions' => $conditions, 'recursive' => -1));

            $names = array_values($inds_list);
            if (count($names) > 1) {
                $conditions['SystemIndustry.name not'] = $names;
            } else {
                $conditions['SystemIndustry.name !='] = $names[0];
            }
//            unset($conditions['language_id']);
//            $conditions[]="(language_id is null or  language_id!={$site['language_code']})";
            $si_other = $this->SystemIndustry->find('all', array('conditions' => $conditions));
            $inds = array_merge_recursive($inds, $si_other);

        } else {
            $si = $this->SystemIndustry->find('all', array('order' => 'SystemIndustry.is_sticky asc,SystemIndustry.display_order desc, SystemIndustry.name asc', 'conditions' => array('SystemIndustry.language_id' => $site['language_code'], 'SystemIndustry.active' => 1, ['OR' => ['SystemIndustry.countries LIKE' => "%$country%", 'SystemIndustry.countries IS NULL', 'SystemIndustry.countries' => '']])));
            $si_list = $this->SystemIndustry->find('list', array('order' => 'SystemIndustry.is_sticky asc,SystemIndustry.display_order desc, SystemIndustry.name asc', 'conditions' => array('SystemIndustry.language_id' => $site['language_code'], 'SystemIndustry.active' => 1, ['OR' => ['SystemIndustry.countries LIKE' => "%$country%", 'SystemIndustry.countries IS NULL', 'SystemIndustry.countries' => '']])));

            $names = array_values($si_list);
            $conditions['SystemIndustry.active'] = 1;
            if (count($names) > 1) {
                $conditions['SystemIndustry.name not'] = $names;
            } else {
                $conditions['SystemIndustry.name !='] = $names[0];
            }
//       $si_other=$this->SystemIndustry->find('all',array('order'=>'SystemIndustry.display_order asc','conditions'=>$conditions));
            $si_other = [];
            $inds = array_merge_recursive($si, $si_other);
        }


        $result = array();
        foreach ($inds as $ind) {

            $result[] = array(
                'name' => $ind['SystemIndustry']['name'],
                'id' => $ind['SystemIndustry']['id'],
                'image' => Router::url($ind['SystemIndustry']['image_full_path']),
            );
        }
        $result[] = array(
            'name' => __('Other', true),
            'id' => -1,
            'image' => Router::url('/other_industry.png'),
        );

        echo json_encode($result);
        die();
    }

    public function not_found()
    {
        $this->cakeError('error404');
    }

    function send_sms_campaign($campaign_id)
    {
        if (PHP_SAPI != "cli") exit;
        IzamDatabaseServiceProvider::boot(getPDO(),getPortalConfig());
        App::import('Vendor', 'SendSMS', array('file' => 'SendSMS/autoload.php'));
        $this->loadModel("SmsCampaign");

        $smsCampaign = $this->SmsCampaign->find("first", ["conditions" => ["SmsCampaign.id" => $campaign_id, "SmsCampaign.state" => "ready"], "recursive" => -1]);
        if (!empty($smsCampaign)) {

            $this->loadModel('Site');
            $this->loadModel('Timezone');
            $site = $this->Site->findById($smsCampaign['SmsCampaign']['site_id']);
            $GLOBALS['site'] = $site['Site'];
            IzamDatabaseServiceProvider::setSiteConnection(getPdo());
            removePdo();
            $zone = $this->Timezone->field('zone_name', array("Timezone.id" => $site['Site']['timezone']));
            date_default_timezone_set($zone);
            $config = json_decode($site['Site']['db_config'], true);
            ConnectionManager::getDataSource('default')->swtich_db($config);
            \SendSMS\SendSMS::sendCampaign($smsCampaign['SmsCampaign']['id']);
        }
        exit;
    }

    function send_sms_daemon()
    {
        App::import('Vendor', 'oilogger');
        $logfile = date('Y_m_d');
        Oilogger::initSession('sms_' . $logfile . '.txt');
        if (PHP_SAPI == "cli") {
            $run_file = '/var/run/send_sms_daemon.pid';
            $id = getmypid();
            $run_file_content = file_get_contents($run_file);
            if ($run_file_content) {
                $cmd = file_get_contents("/proc/{$run_file_content}/cmdline");
                $old_time = intval(file_get_contents("/var/run/send_sms_daemon_time.pid"));
                if (strpos($cmd, 'send_sms_daemon') && (time() - $old_time) < 3600) {

                    exit;
                } else {

                    exec("kill -9 $run_file_content");
                    file_put_contents($run_file, $id);
                }
            } else {

                file_put_contents($run_file, $id);
            }
        }
        if (PHP_SAPI != "cli") {
            exit;
        }

        $fullVersion = explode('.', PHP_VERSION);
        $version = $fullVersion[0] . '.' . $fullVersion[1];
        $php_bin_path = trim(str_replace("\n", "", shell_exec("which php$version")));
        App::import('Vendor', 'SendSMS', array('file' => 'SendSMS/autoload.php'));
        $this->loadModel("SmsCampaign");
        while (true) {
            $smsCampaigns = $this->SmsCampaign->find("count", ["conditions" => ["SmsCampaign.site_id !=" => 44, "SmsCampaign.state" => "working"], "recursive" => -1]);
            if($smsCampaigns>25){
                echo "Sleeping for 5 seconds before running another 25 process \n ";
                sleep(5);
            }
            $smsCampaigns = $this->SmsCampaign->find("all", ["conditions" => ["SmsCampaign.site_id !=" => 44, "SmsCampaign.state" => "ready", "SmsCampaign.pid" => 0], "recursive" => -1, "limit" => 10]);
            if (!empty($smsCampaigns)) {
                foreach ($smsCampaigns as $smsCampaign) {
                    echo $smsCampaign['SmsCampaign']['id'] . "\n";
                    Oilogger::log('SmsCampaign ' . $smsCampaign['SmsCampaign']['id']);
                    $command = "nohup " . $php_bin_path . ' ' . dirname(dirname(__FILE__)) . DS . "webroot/cron.php /sites/send_sms_campaign/{$smsCampaign['SmsCampaign']['id']} > /dev/null 2>&1 & echo $!";
                    echo $command . "\n";
                    exec($command, $op);
                    $pid = (int)$op[0];
                    $this->SmsCampaign->updateAll(['SmsCampaign.pid' => $pid], ["SmsCampaign.id" => $smsCampaign['SmsCampaign']['id']]);
                    // Update sms process time to ensure corn still running and not zombie
                    file_put_contents('/var/run/send_sms_daemon_time.pid', time());
                }
            } else {
                sleep(5);
            }
        }
        exit;
    }

    public function run_background_api($body)
    {
        App::import('Component', 'ApiRequestsComponent');
        $apiRequests = new ApiRequestsComponent();
        $response = $apiRequests->translateRequest($body);
        die;
    }

    public function api_check_first_login()
    {
        $site = getCurrentSite();
        $allowed_fields = ["first_login"];
        if (is_array($site)) {
            foreach ($site as $key => $value)
                if (!in_array($key, $allowed_fields))
                    unset($site[$key]);
            if ($site['staff_id'] > 0) {
                $site['staff'] = getAuthStaff();
                unset($site['staff']["password"]);
            }
        }

        $this->set('rest_item', ["Site" => $site]);
        $this->set('rest_model_name', "Site");
        $this->render("view");
    }

    public function api_me()
    {
        $jsFormats = getDateFormats('moment_js');
        $siteFormat = getCurrentSite('date_format');
        $jsDateFormat = $jsFormats[$siteFormat];

        $site = getAuthOwner();

        $allowed_fields = ['plan_id',"tolerant","expiry_date","beta_version","id", "business_name", "first_name", "last_name", "subdomain", "site_logo", "invoice_logo", "address1", "address2", "city", "state", "postal_code", "phone1", "phone2", "country_code", "bn1", "bn2", "timezone", "date_format", "currency_code", "language_code", "email", "staff_id", "is_super_admin", "beta_version", "package_id",""];
        if (is_array($site)) {
            foreach ($site as $key => $value)
                if (!in_array($key, $allowed_fields))
                    unset($site[$key]);
            if ($site['staff_id'] > 0) {
                $site['staff'] = getAuthStaff();
                unset($site['staff']["password"]);
            }
        }
        $selected_date_format = getAuthOwner('date_format');
        $site['date_format'] = getDateFormats('moment_js')[$selected_date_format];
        $currencies = include APP . 'config' . DS . 'currencies.php';
        $site['currencyFormat'] = $currencies[$site['currency_code']];
        // added based on mobile team need .
        $site['currencies'] = $this->getCurrenciesFormat($site['currency_code']);
        $site['numberFormat'] = $number_formats[$site['currency_code'] . '-' . $site['country_code']] ?: $number_formats[$site['currency_code']];   //from (( include APP . 'config' . DS . 'currencies.php' ))
        $site['SITE_HASH'] = SITE_HASH;
        $site['url']  = $this->convertSubdomain($site['subdomain'],$site['beta_version']);
        $site['subdomain']  = $site['subdomain'];
        $site['is_beta']  = (bool) $site['beta_version'];
        $tolerant = (int) $site['tolerant'];
        $site['is_expired'] = ($site['plan_id'] != 1 && (date('Y-m-d') > date("Y-m-d",strtotime($site['expiry_date']." +$tolerant day"))));
        unset($site['plan_id']);
        $site['expiry_date'] =date("Y-m-d", strtotime($site['expiry_date']));
        $site['moment_format'] = $jsDateFormat;
        $site['ALLOWED_LOYALTY_PLUGIN'] = ifPluginActive(CLIENT_LOYALTY_PLUGIN);
        $this->set('rest_item', ["Site" => $site]);
        $headers = array_change_key_case(getallheaders(), CASE_LOWER);
        $extraValues = [];
        if ($headers['x-app-name'] == "EXP"){
            $extraValues = ['expensesIsMandatoryEnabled'=>!!Settings::getValue(ExpensesPlugin , 'selecting_expense_account_is_mandatory' , allowCache: false)];
        }
        $this->set('extraValues' , $extraValues);
        $this->set('rest_model_name', "Site");
        $this->render("view");
    }
    private function convertSubdomain($subdomain, $beta_version)
    {
        $beta_domain = Domain_Name_Only.'.';
        $main_domain  = BETA_DOMAIN_NAME_ONLY.'.';
        if ($beta_version) {
            $beta_domain = BETA_DOMAIN_NAME_ONLY.'.';
            $main_domain  = Domain_Name_Only.'.';
        }
        $subDomain = str_replace($main_domain, $beta_domain, $subdomain);
        return 'https://' . $subDomain;
    }
    function api_local()
    {

        if (!isset($this->params['url']['country_code']) || $this->params['url']['country_code'] = "") {
            http_response_code(422);
            echo json_encode([
                "result" => "fail",
                "code" => 422,
                'error' => 'Please provide country code'
            ]);
        }
        App::import('Vendor', 'sites_local');
        $list = BusinessNumberCommonFieldsUtil::getBusinessNumberFields();
        $list = $list[strtoupper($_GET['country_code'])];
        $data = [];
        // Added to fix label_ar in mobile app . 
        $translatedKeys = ['VAT Number' => 'الرقم الضريبي' ,'C.R' => 'سجل تجاري'  , 'Tax ID' => 'بطاقة ضريبية'];

        foreach ($list as $key => $value) {
            $data[] = ["name" => $key, "label" => $value['label'], "label_ar" => $translatedKeys[$value['label']] ?? __($value['label'], true)];
        }
        http_response_code(200);
        echo json_encode([
            "result" => "success",
            "code" => 200,
            'data' => $data
        ]);
        die();
    }

    function api_local_list()
    {

        App::import('Vendor', 'sites_local');
        $list = BusinessNumberCommonFieldsUtil::getBusinessNumberFields();
        $data = [];
        $translatedKeys = ['vat number' => "الرقم الضريبي"];

        foreach ($list as $key => $raw) {
            $subData = [];
            foreach ($raw as $key2 => $value) {
                $subData[] = ["name" => $key2, "label" => $value['label'], "label_ar" => $translatedKeys[strtolower($value['label'])] ?? __($value['label'], true)];
            }
            $data[] = ['name' => $key, 'subData' => $subData];
        }
        http_response_code(200);
        echo json_encode([
            "result" => "success",
            "code" => 200,
            'data' => $data
        ]);
        die();
    }

    function api_get_layout()
    {
        $layout = [];
        $staff = $this->Session->read('STAFF');
        if ($this->Session->check('OWNER')) {
            $user = $this->Session->read('OWNER');
            $view = new View($this);
            $menu = json_decode(get_menus($view, $user['type'], @$staff['role_id']), true);
            $layout = ['menu' => $menu];
        }
        $this->set('rest_item', ["Layout" => $layout]);
        $this->set('rest_model_name', "Layout");
        $this->render("view");
    }

    function api_get_menu_data()
    {
        $staff = $this->Session->read('STAFF');
        if ($this->Session->check('OWNER')) {
            $user = $this->Session->read('OWNER');
            $view = new View($this);
            $menu = get_menus_data($view, $user['type'], $staff['role_id'] ?? 0);

        }
        $this->autoRender = false;
        return json_encode(["menu_data" => $menu]);
    }

    function api_get_custom_pos_javascript()
    {
        $pos_global_js = settings::getValue(PosPlugin, 'pos_global_js', null, false, false);
        $pos_button_js = settings::getValue(PosPlugin, 'pos_button_js', null, false, false);
        $this->set('rest_model_name', 'CustomForm');
        $this->set('rest_item', [
            "global_js" => $pos_global_js,
            "button_js" => $pos_button_js
        ]);
        $this->render("view");
    }

    function api_get_current_user_permission()
    {
        $add_new_client_permission = check_permission(Clients_Add_New_Client);
        $edit_all_clients_permissions = check_permission(Edit_Delete_all_clients);
        $edit_his_own_clients_permissions = check_permission(Edit_And_delete_his_own_added_clients);
        $his_own_clients = GetObjectOrLoadModel('ItemStaff')->get_staff_items_list(getAuthStaff('id'), ItemStaff::CLIENT_ITEM_TYPE);
        $this->set('rest_model_name', 'CustomForm');
        $this->set('rest_item', [
            "add_client" => $add_new_client_permission,
            "edit_his_own" => $edit_his_own_clients_permissions,
            "edit_all" => $edit_all_clients_permissions,
            "his_own_clients" => array_values($his_own_clients),
            'redeem_loyalty_points' => check_permission(PermissionUtil::REDEEM_LOYALTY_POINTS),
        ]);
        $this->render("view");
    }

    function api_clear_cache()
    {
        clearWebSiteCache();
        $site = getCurrentSite();
        $this->set('rest_item', ["Layout" => $site]);
        $this->set('rest_model_name', "site");
        $this->render("view");
    }

    function api_get_plugin_status()
    {
        $plugin_id = $_GET['plugin_id'] ?: \Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN;
        $this->set('rest_model_name', 'CustomForm');
        $this->set('rest_item', [
            'request_plugin_id' => $plugin_id,
            'enabled' => ifPluginActive($plugin_id)
        ]);
        $this->render("view");
    }

    function api_pos_shortcuts_test()
    {
        /**
         * Excluded Shortcuts:remove ctrl f
         * CTRL + T
         * CTRL + N
         * CTRL + W
         * CTRL + X
         * CTRL + C
         * CTRL + V
         * CTRL + R
         * ESC
         * TAB
         * ENTER
         * SPACE
         * BACKSPACE
         */
        $data = [
            'order_panel_shortcuts' => [
                'order_controllers' => [
                    'new_order' => [
                        // Alt + N
                        [
                            'key' => 78,
                            'holdKey' => 18,
        
                        ]
                    ],
                    'hold_order' => [
                        // Alt + h
                        [
                            'key' => 72,
                            'holdKey' => 18
                        ]
                    ],
                    'choose_previous_order' => [
                        // shift + <
                        [
                            'key' => 37,
                            'holdKey' => 16
                        ]
                    ],
                    'choose_next_order' => [
                        // shift + >
                        [
                            'key' => 39,
                            'holdKey' => 16
                        ]
                    ],
                    'discard_order' => [
                        // ctrl + delete
                        [
                            'key' => 46,
                            'holdKey' => 17
                        ]
                    ],
                ],
                'items_selection_controllers' => [
                    'choose_previous_item' => [
                        // shift + /\
                        [
                            'key' => 38,
                            'holdKey' => 16
                        ]
                    ],
                    'choose_next_item' => [
                        // shift + \/
                        [
                            'key' => 40,
                            'holdKey' => 16
                        ]
                    ],
                    'remove_order_item' => [
                        // delete
                        [
                            'key' => 46,
                            'holdKey' => null
                        ]
                    ],
                ],
                'item_adjustment_controllers' => [
                    'choose_quantity' => [
                        // Alt + q
                        [
                            'key' => 81,
                            'holdKey' => 18
                        ]
                    ],
                    'choose_discount_percent' => [
                        // Alt + f
                        [
                            'key' => 70,
                            'holdKey' => 18
                        ]
                    ],
                    'choose_discount_currency' => [
                        // Alt + d
                        [
                            'key' => 68,
                            'holdKey' => 18
                        ]
                    ],
                    'choose_price' => [
                        // Alt + p
                        [
                            'key' => 80,
                            'holdKey' => 18
                        ]
                    ],
                ],
                'payment_controllers' => [
                    'show_payment' => [
                        // shift + enter
                        [
                            'key' => 13,
                            'holdKey' => 16
                        ],
                    ],
                ],
            ],
            'items_panel_shortcuts' => [
                'items_navigation' => [
                    'navigate_through_items' => [
                        [
                            'html' => '<i class="fa fa-arrow-left m-r-xs m-l-xs"></i> <i class="fa fa-arrow-up m-r-xs m-l-xs"></i> <i class="fa fa-arrow-right m-r-xs m-l-xs"></i> <i class="fa fa-arrow-down m-r-xs m-l-xs"></i>'
                        ]
                    ],
                    'select_category' => [
                        [
                            'html' => '<span>SHIFT + NUM</span>'
                        ]
                    ],
                    'back' => [
                        // ctrl + backspace
                        [
                            'key' => 8,
                            'holdKey' => 17
                        ]
                    ],
                ],
                'information_panels_switchers' => [
                    'show_products' => [
                        // ctrl + p
                        [
                            'key' => 80,
                            'holdKey' => 17
                        ]
                    ],
                    'show_product_info' => [
                        // ctrl + ?
                        [
                            'key' => 191,
                            'holdKey' => 17
                        ]
                    ],
                    'show_order_source' => [
                        // ctrl + o
                        [
                            'key' => 79,
                            'holdKey' => 17
                        ]
                    ],
                    'show_invoices' => [
                        // ctrl + i
                        [
                            'key' => 73,
                            'holdKey' => 17
                        ]
                    ],
                    'show_clients' => [
                        // ctrl + space
                        [
                            'key' => 32,
                            'holdKey' => 17
                        ]
                    ],
                    'search' => [
                        // ctrl + f
                        [
                            'key' => 70,
                            'holdKey' => 17
                        ]
                    ],
                ],
            ],
        ];
        die(json_encode($data));
    }

    public function owner_switch_to_master()
    {
        $this->loadModel('Site');
        if (!check_permission(CHANGE_SITE_VERSION)) {
            $this->flashMessage(__('You Are Not Allowed to Submit This Action', TRUE));
            $this->redirect('/');
        }
        $this->Site->recursive = -1;
        $currentSite = $this->Site->find(['id' => getCurrentSite('id')]);
        $this->add_actionline(ACTION_MOVE_TO_LIVE);
        $this->Site->id = $currentSite['Site']['id'];
        SiteService::updateById($currentSite['Site']['id'], ['beta_version' => 0]);
        $this->add_stats(STATS_MOVED_TO_LIVE_ACTION);
        $this->Session->destroy();
        $this->redirect('/');
    }

    public function api_spelled_currency()
    {
        $spelledCurrency = PlaceHolder::tafketWithChange($_GET['salary'], $_GET['currency']);
        echo json_encode(['spelledCurrency' => $spelledCurrency]);
        die();
    }

    private function getCurrenciesFormat($currency_code)
    {
        $currencies_ar = include APP . 'config' . DS . 'ar_currencies.php';
        $currencies = include APP . 'config' . DS . 'currencies.php';
        return ['currencies_ar' => $currencies_ar[$currency_code], 'currencies_en' => $currencies[$currency_code]];
    }

    private function updateJournalLang() : void
    {
        $this->loadModel('JournalCat');
        $this->loadModel('JournalAccount');

        $this->JournalCat->updateLanguage();
        $this->JournalAccount->updateLanguage();
    }

    function owner_delete_field($type, $id, $field = false, $redirect = false) {
        if (!$redirect) {
            $redirect = $this->referer();
        }
        $modelName  = 'Site';
        $type = ucfirst($type);
        $this->loadModel('Site');
        $conds = array("$modelName.id" => getAuthOwner('id'));
        $item = $this->Site->find($conds, false, false, false, -1);

        if (!$item) {
            $this->flashMessage(__('Site not found', true));
            $this->redirect($redirect);
        } else if (!$field) {
            $this->flashMessage(__('Invalid file name', true));
            $this->redirect($redirect);
        } else {
            $this->Site->id = $id;
            $base_name = $this->Site->field($field);
            if (!$base_name) {
                $this->flashMessage(sprintf(__('%s is not found', true), ucfirst($type)));
                $this->redirect($redirect);
            }
            if (!$this->Site->saveField($field, '')) {
                $this->flashMessage(sprintf(__('Could not delete %s', true), lcfirst($type)));
                $this->redirect($redirect);
            }

            $this->Site->{'delete' . $type}($field, $base_name);
            $this->Site->reload_session();

            $this->flashMessage(sprintf(__('%s has been removed', true), __(ucfirst($type),true)), 'Sucmessage');
            $this->redirect($redirect);
        }
    }

    function owner_skip_industry_select(){
        $this->loadModel('ActionLine');
        $actionlinecount = $this->ActionLine->find('count');
        if ($actionlinecount > 0 && !isset($_GET['force'])) {
            $this->redirect('/');
        }
        $this->loadModel('Site');
        $this->Site->save(['first_login' => 0, 'id' => getCurrentSite('id')]);
        $this->Site->activate_plugins_depending_on_position_and_company_size();
        $this->reloginAsOwnerStaffUser();
        $this->redirect('/v2/owner/plugin-manager/1');
    }

    private function reloginAsOwnerStaffUser(){
        $this->loadModel('Staff');
        $this->loadModel('Client');
        $ownerUserStaff = $this->Staff->getOwnerUser();
        if($ownerUserStaff){
            $this->Session->delete('STAFF');
            $this->Session->delete('OWNER');
            $this->Session->delete('CurrentPlugin');
            $this->Session->delete('CurrentSite');
            $this->Session->delete('timezone');
            $this->Session->delete('pagination-index');
            $this->Session->destroy();
            $this->Cookie->destroy();
            $this->Session->write('isOwnerStaffFirstLogin', true);
            $this->redirect("/clients/login");
        }
    }

    public function api_switch_booking_plugin_mode(){
        if (!check_permission([PermissionUtil::MANAGE_BOOKING_SETTINGS, PermissionUtil::View_His_Own_Notes_Attachments_Only])) {
            $this->cakeError('error403');
            return;
        }
        $raw_data = file_get_contents('php://input');
        $data = json_decode($raw_data, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($data) || !in_array($data['mode'] ?? null, ['beta', 'classic'])) {
            $this->cakeError('error400'); return;
        }
        $mode = $data['mode'];
        if($mode == 'beta'){
            if(ifPluginActive(BookingPlugin)){
                $this->Site->owner_update_plugin(BookingPlugin, false, false, false);
            }
            if(!ifPluginActive(NEW_BOOKING_PLUGIN)){
                $this->Site->owner_update_plugin(NEW_BOOKING_PLUGIN, true, false, false);
            }
        }elseif($mode == 'classic'){
            if(!ifPluginActive(BookingPlugin)){
                $this->Site->owner_update_plugin(BookingPlugin, true, false, false);
            }
            if(ifPluginActive(NEW_BOOKING_PLUGIN)){
                $this->Site->owner_update_plugin(NEW_BOOKING_PLUGIN, false, false, false);
            }
        }
        if(IS_REST){
            die(json_encode(['status' => true]));
        }else{
            $this->redirect($this->referer());
        }
    }
}
