<?php

class AddressController extends AppController
{
    function owner_add()
    {
        $this->autoRender = false;
        $this->RequestHandler->respondAs('json');
        http_response_code(200);

        $address = $this->data['Address'];

        if (empty($address['address1']) && empty($address['address2']) && empty($address['city']) && empty($address['state']) && empty($address['postal_code'])) {
            if (IS_REST) {
                return json_encode([
                    'success' => false,
                    'message' => 'Client address cannot be empty'
                ]);
            }
        }

        $this->loadModel('Address');

        $this->Address->create();

        $record['Address'] = array_merge($address, [
            'created' => date("Y:m:d H:i:s"),
            'modified' => date("Y:m:d H:i:s"),
        ]);

        $this->Address->save($record);

        if (IS_REST) {
            return json_encode([
                'success' => true,
                'id' => $this->Address->getLastInsertID()
            ]);
        }
    }
}

