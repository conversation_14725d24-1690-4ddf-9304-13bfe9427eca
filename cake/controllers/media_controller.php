<?php
class MediaController extends AppController {

	var $name = 'Media';

	/**
	 * @var Media
	 */
	var $Media;
	var $helpers = array('Html', 'Form');

//	function owner_photo($name)
//	{
//		$this->Media->find('first',array('condition'))
//	}

	function owner_menu()
	{
       
	   $menu = $this->Media->get_media_menu();
	   $this->set('menu',$menu);
	}
	
	function owner_asset_upload()
	{
//		die(Debug(getCurrentSite()));
//		Configure::write('debug',2);
		if(!empty($_FILES))
		{	
			foreach($_FILES['Media'] as $k => $v)
			{
				$file[$k] =	$v[0];
			}
			$data['Media']['file'] = $file;
			$data['Media']['description'] = 'page asset';
			$data['Media']['title'] = $file['name']	;
			$data['Media']['type'] = 'photo';
			$data['Media']['active'] = 1;
			$this->Media->create();
//			die(debug($data));
			if($this->Media->save($data))
			{
				$photo = $this->Media->findById($this->Media->id);
				$file_url = "https:/" . Router::url('/'. getCurrentSite('subdomain').'/'.str_replace(DS, '/', $this->Media->getFileSettings('file')['folder']) . $photo['Media']['file']);
				die(json_encode(["data" => [$file_url]]));
			}else
			{
				die(var_dump($this->Media->validationErrors));
			}
			
		}
		die(var_dump($_FILES));
	}
	
	function owner_test()
	{
		die(debug($this->Media->temp_get_menu_links()));
//		die(debug($this->Media->get_navigation_menu()));
//		die(debug($this->Media->temp_get_media_config()));
	}
	
	function owner_index($media_type) {
		$this->Media->recursive = 0;
		$conditions = $this->_filter_params();
		if($media_type)
		{
			$conditions['Media.type'] = $media_type;
		}
		if(!empty($_GET['parent_type']) && !empty($_GET['parent']))
		{
			
			$parent_type = $_GET['parent_type'];
			$isMedia = $parent_type == 'Media';
			$parent = $_GET['parent'];
			$this->loadModel('MediasParent');
			$media_parents_conditions = [];
			if($isMedia)
			{
				if(is_numeric($parent))
				{
					$media_parents_conditions['MediasParent.parent_id'] = $parent;
				}else
				{
					$parent = $this->Media->findByTitle($parent)['Media']['id'];
					$media_parents_conditions['MediasParent.parent_id'] = $parent;
				}
				
			}
			$media_parents_conditions['MediasParent.parent_type'] = $parent_type;
			
			$conditions['Media.id'] = $this->MediasParent->find('list',array('fields' => array('MediasParent.media_id'), 'conditions' => $media_parents_conditions));
			$parent = $this->Media->get_parent_data($parent_type,$parent);
			$parent_type_config = Media::get_media_fields($parent['media_type']);
			
			$this->set('parent',$parent);
			$media_type = $parent_type_config['child']['type'];
//			$this->set('parent_type_config',$parent_type_config);
//			$conditions['MediasParent.parent_id'] = $_GET['parent_id'];
//			$conditions['MediasParent.parent_type'] = $_GET['parent_type'];
		}
		
		$medias = $this->paginate('Media', $conditions);
		debug($medias);
		//if no media type then we get the parent_media type for the breadcrumbs
		if(!$media_type && $medias[0]['Media']['type']){
			$media_type = $medias[0]['Media']['type'];
			
		}
		$config = Media::get_media_fields($media_type);
		(debug($config));
		$media_has_children = !empty($config['media_parent_type']);
		$this->set('media_has_children',$media_has_children);
		$title = $config['title'];
		$this->set('media_parent_type',$config['media_parent_type']);
		$this->set('media_type',$media_type);
		debug($title);
		$this->set('index_title',$title);
		$this->set('is_ajax', $this->RequestHandler->isAjax());
		$this->set('media_types',$this->Media->get_media_types());
		
		$this->set('media',$medias );
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('media', true)),true));
			$this->redirect(array('plugin' => 'website_front', 'action'=>'index'));
		}
		$this->set('media', $this->Media->read(null, $id));
	}

	function owner_add($media_type,$parent_id = null) {
	
			if (!empty($this->data)) {
//				if(!empty($this->data['Media']['parent_type']))
//				{
//					
//					$this->data['Media']['MediasParent'][0] = ['parent_type' => $this->data['Media']['parent_type'], 'parent_id' => $this->data['Media']['parent_id']];
//					unset($this->data['Media']['parrent_type']);
//					unset($this->data['Media']['parrent_id']);
//				}
				
				$this->Media->create();
				if ($this->Media->saveall($this->data)) {
					$this->flashMessage(sprintf (__('The %s has been saved', true), __('media',true)), 'Sucmessage');
					if(!$this->data['MediasParent'][0]['parent_id'])
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index',$this->data['Media']['type']));
					else
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index', '?' => "parent={$this->data['MediasParent'][0]['parent_id']}&parent_type={$this->data['MediasParent'][0]['parent_type']}"));
				} else {
					$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('media',true)));
						if(!$this->data['MediasParent'][0]['parent_id'])
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index',$this->data['Media']['type']));
						else
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index', '?' => "parent={$this->data['MediasParent'][0]['parent_id']}&parent_type={$this->data['MediasParent'][0]['parent_type']}"));
				}
			}
			if($parent_id)
			{
				$this->data['MediasParent.0.parent_id'] = $parent_id;
				$this->set('parent_id',$parent_id);
			}
			$media_data = Media::get_media_fields($media_type);
			
			$parent = $this->Media->get_parent_data($media_data['parent_model'], $parent_id);
			$this->set('parent',$parent);
			
//			die(Debug($media_data));	
//			$this->set('media_types',$this->Media->get_media_types());
//			$this->set('media_type',$media_type);
			$this->set('media_data',$media_data);
			$this->set('file_settings',$this->Media->getFileSettings());
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'media',true)));
			$this->redirect(array('plugin' => 'website_front', 'action'=>'index'));
		}
		$media = $this->Media->read(null, $id);
		if (!empty($this->data)) {
//			die(debug($this->data));	
			if ($this->Media->saveall($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('media',true)), 'Sucmessage');
					if(!$this->data['MediasParent'][0]['parent_id'])
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index',$this->data['Media']['type']));
					else
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index', '?' => "parent={$this->data['MediasParent'][0]['parent_id']}&parent_type={$this->data['MediasParent'][0]['parent_type']}"));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('media',true)));
								if(!$this->data['MediasParent'][0]['parent_id'])
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index',$this->data['Media']['type']));
					else
						$this->redirect(array('plugin' => 'website_front', 'action'=>'index', '?' => "parent={$this->data['MediasParent'][0]['parent_id']}&parent_type={$this->data['MediasParent'][0]['parent_type']}"));

			}
		}
		if (empty($this->data)) {
			$this->data = $media;
		}
		$parent = $this->Media->get_parent_data($media['MediasParent'][0]['parent_type'], $media['MediasParent'][0]['parent_id']);
		$this->set('parent',$parent);
		$media_data = Media::get_media_fields($media['Media']['type']);
			$this->set('media_types',$this->Media->get_media_types());
			$this->set('media_type',$media_type);
			$this->set('media_data',$media_data);
		$this->set('file_settings',$this->Media->getFileSettings());
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('media',true)));
			$this->redirect(array('plugin' => 'website_front', 'action'=>'menu'));
		}
		$module_name= __('media', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('media', true);
		 } 
		$media = $this->Media->find('all',array('conditions'=>array('Media.id'=>$id)));
		if (empty($media)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('plugin' => 'website_front', 'action' => 'menu'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->Media->deleteAll(array('Media.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('plugin' => 'website_front', 'action'=>'menu'));
			}
			else{
				$this->redirect(array('plugin' => 'website_front', 'action'=>'menu'));
			}
		}
		$this->set('media',$media);
	}
}
?>