<?php

class MassuploadsController extends AppController {

    var $name = 'Massuploads';
    var $helpers = array('Html', 'Form');
    var $components = array('<PERSON>ie', 'RequestHandler', 'Email', 'SysEmails', 'Lmail');

    function owner_add() {
        $this->set('upload_settings', $this->Massupload->upload_settings);

        $ajax = $this->RequestHandler->isAjax();
        if (!empty($this->data) and $ajax==true) {
            require_once APP . 'vendors' . DS . 'FileUploader.php';
            echo json_encode(FileUploader::Postupload($this->data['Massupload']['file_name'], $this->Massupload->upload_settings['file_name']));
            die();
        }
    }

}
?>