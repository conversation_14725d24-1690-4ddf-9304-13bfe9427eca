<?php

use App\Reports\AttendanceSheet\ApiFormatter as AttendanceSheetReportApiFormatter;
use App\Reports\Payslips\ApiFormatter as PayrollReportApiFormatter;
use App\Services\Report\ReportFactory;
use App\Services\Report\ReportUtil;
use App\Utils\ReportLimitTrait;
use Izam\Daftra\AppManager\Repositories\AppReportRepository;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\AppReportErrorHandlerDecorator;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;

App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
App::import('Vendor', 'sites_local');
class ReportsController extends AppController {
    use ReportLimitTrait;
    var $uses = array('Invoice', 'SavedReport');
    var $components = array('RequestHandler');
    static $currencyRates;
    /**
     * @var Invoice
     */
    var $Invoice;

    /**
     *
     * @var Currency
     */
    var $Currency;

    /**
     *
     * @var SavedReport
     */
    var $SavedReport;
    var $reportType = 0;
    var $modelName = 'SavedReport';
    function beforeFilter()
    {
        $this->redirectToConfirmation();
        parent::beforeFilter(); // TODO: Change the autogenerated stub
        //session_write_close();
    }

    function owner_confirm_csv()
    {
        $report_return_url = strtok(base64_decode($_GET['report_url']), '?');
        $this->set('report_url', $report_return_url);
        $this->set('download_url',base64_decode($_GET['download_url']));
        $this->set('records_count',$_GET['records_count']);
        $this->set('handler',$_GET['handler']);
    }

    function owner_report($name=null, $type = null)
        {
            if(!$this->report_permissions($name)){
                $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
                $this->redirect($this->referer());
            }
        if(is_null($name)) {
            $this->flashMessage(__('Report key not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
            return;
        }

        if(isset($_GET['bilal']) &&  $_GET['bilal'] == 2) {
            error_reporting(E_ERROR | E_WARNING | E_PARSE);
            ini_set('display_errors',1);
        }
        set_time_limit ( 30*60);
        ini_set('memory_limit','2G');
        $reports_js_labels = array('Please choose Credit Type');
        $this->js_lang_labels = array_merge($this->js_lang_labels,$reports_js_labels);
        App::import('vendor','ReportV2',['file'=>'ReportV2/autoload.php']);
        $showReport = ($_GET['show_report'] ?? false ) || ($_GET['wants_json'] ?? null);
        $params = $this->params['url'];
        unset($params['ext'],$params['url'],$params['debug']);
//        foreach($params as $k => $v){
//            if(is_array($v) && count($v) == 1){
//
//                $params[$k] = $v[0];
//            }
//        }
        $params['pass'] = $this->params['pass'];
        if($name){
            try{
                $params = $this->setParamsPermissions($params,$name);
                $tempReportFactoryCreate = \ReportV2\ReportFactory::create($name, $params, $type);
                $report =& $tempReportFactoryCreate;
                $report->setResponseType($this->RequestHandler->responseType());
            } catch (Exception $e){
                $this->flashMessage(sprintf(__('No Report Found for This Key', true)));
                $this->redirect($this->referer());
            }
            if(!$report->isValidReport())
            {
                if (isset($this->params['url']['wants_json'])){
                    header('Content-Type: application/json');
                    return die(json_encode(['isVisible'=>false , 'items'=>[]]));
                }
                $this->flashMessage(__("You don't have permission to view this report", true));
                $this->redirect('/');
            }
            $report_meta = $report->get_report_meta_data();
            $filters = $report->get_filter_inputs();
            $header_filters = $report->get_filters_key_value($filters);
            if(!empty($showReport))
            {
                $report_data = $report->get_report_data(isset($this->params) ? $this->params : []);
                $this->set('report_data', $report_data);
                $totalRowCount = 0;
                foreach ($report_data as $currency) {
                    if (isset($currency['data'])) {
                        foreach ($currency['data'] as $datum) $totalRowCount += count($datum);
                    }
                }
                if (isset($this->params['url']['wants_json']) || $this->params['url']['ext'] == 'json'){
                    header('Content-Type: application/json');
                    if ( $name == 'attendance_sheets'){
                        $this->api_get_attendance_report_for_staff($report_data);
                    }elseif ($name == 'payslips'){
                        $this->api_get_payroll_report_for_staff($report_data ,$params);
                    } else {
                        //respond json
                        $jsonData = $report->getJsonData();
                        die(json_encode($jsonData));
                    }
                }
                if(!$this->params['url']['summary']){
                    $this->displayLimitThreshold($totalRowCount, 10000);
                }
            }
            $owner = getAuthOwner();
            $this->set('tax_label', Localize::get_field_label_by_country_code('bn1', $owner['country_code']));
            $this->set('report_meta', $report_meta);
            $this->set('report_name', $name);
            $this->set('filters', $filters);
            $this->set('header_filters',$header_filters);
            $params = $report->get_params();
            $this->data = $params;
            $this->set('Obj',$report);
            $this->set('params', $params);
            $this->set('title_for_layout', __($report_meta['title'], true) . (!empty(trim($report->groupByName)) ? ' - ' . __('Group By', true) . ' ' . __($report->groupByName, true) : ''));
            $this->set('owner', getAuthOwner());
        }else{
            $reports = ReportV2::$reports;
            $this->set('reports', $reports);
        }
    }

    public function owner_app_report($name)
    {
        $this->owner_report($name, 'app_report');

        $this->render('owner_report');
    }

    function owner_supplier_bnr() {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $this->set('report_type','supplier');
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true);
        $this->set('paymentMethods',$paymentMethods);
        $this->reportType = 2;
        $owner = getAuthOwner();

        /* if ($this->RequestHandler->isAjax() || $this->RequestHandler->isFlash()) {
          $this->_owner_payments_ajax();
          } */

        $this->_ownerSave();
        $this->set(array('reportType' => ''));



        require_once APP . 'vendors' . DS . 'Report.php';
        $report = new Report('Nbr', 'supplier');

        $this->set($report->getDataArray(), false);
        $this->set('jsonParams', $report->jsonAdapter(), false);
        $main_currency= getAuthOwner('currency_code');
        $arr_rates=[];
        foreach($this->viewVars["reportData"] as &$row){
            if(!isset($arr_rates[$row['currency_code']])){
                $arr_rates[$row['currency_code']] = CurrencyConverter::index($row['currency_code'], $owner["currency_code"], date('Y-m-d'));
            }
            $row['subtotal']=$arr_rates[$row['currency_code']]*$row['subtotal'];
            $row['currency_code']=$main_currency;
        }
        $this->set("currency_rates", $arr_rates);
        $this->_settings();


        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, true);
        $this->set('payment_methods', $paymentMethods);


        $this->set('title_for_layout',  __('Pnr', true) . ' - ' . __('Reports', true));


    }

    function owner_index() {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }

        $conditions = $this->_filter_params();
        $conditions['SavedReport.site_id'] = getAuthOwner('id');
        $this->paginate['SavedReport'] = array(
            'order' => 'SavedReport.title',
            'conditions' => $conditions
        );

        $this->set('reports', $this->paginate('SavedReport'));
        $this->set('reportTypes', SavedReport::getReportTypeNames());
        $this->set('content', $this->get_snippet('saved-reports'));
    }

    public static $alreadyTraversed = [];

    function generateTree(&$children,$accountsData) {
        foreach ($children as &$child) {
            if(isset($accountsData[$child['id']]['children'])) {
                if (!isset(self::$alreadyTraversed[$child['id']])) {
                    self::$alreadyTraversed[$child['id']] = true;
                    $child['children'] = $this->generateTree($accountsData[$child['id']]['children'], $accountsData);
                } else {
//                    die(dump(self::$alreadyTraversed, $child, debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10)));
                }
            }
        }
        return $children;
    }

    function addChildrenToArray($children) {
        $sortedArray = [];
        foreach ($children as $child) {
            $grandChildren = $child['children'];
            unset($child['children']);
            $sortedArray[] = $child;
            if($grandChildren) {
                $sortedArray = array_merge($sortedArray, $this->addChildrenToArray($grandChildren));
            }
        }
        return $sortedArray;
    }
    /**
     *
     * @param type $report_id the report to get its data
     * @param type $use_in_other_place if isset to true the data won't be displayed but else returned to who ever called it
     * @param type $fy_id financial year id, false for all, -1 for current open year, other for closed year id
     */
    function owner_accounts($report_id,$fy_id=false){
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        if ($report_id === "2" && empty($_GET['date_from'])) {
            $_GET['date_from'] = format_date("01-01-2009") ;
        }
        set_time_limit(3600);
        ini_set('memory_limit','10G');
        $this->loadModel('Journal');

        $this->Journal->update_cat_parent_ids();
        // Caused a performance issue when handling a large number of journal accounts.
        //  $this->Journal->update_account_parent_ids();
        $this->Journal->updateAccountLevelEvenUnreachable();
        $report_data = $this->Journal->generate_journal_statment_report($report_id);
        foreach ($report_data['data'] as &$group) {
            foreach ($group['accounts'] as &$account) {
                $sortedAccounts = [];
                $treeData = [];
                $treeData[$account['id']]['data'] = $account;
                foreach ($account['children'] as $child) {
                    $treeData[$child['id']]['data'] = $child;
                    $treeData[$child['journal_cat_id']]['children'][] = $child;
                }
                foreach ($treeData as $id => &$item) {
                    if(isset($item['children'])) {
                        $item['children'] = $this->generateTree($item['children'], $treeData);
                    }
                }
                unset($item);
                foreach ($treeData as $item) {
                    if(!isset($treeData[$item['data']['journal_cat_id']])) {
                        if(isset($item['children'])) {
                            $sortedAccounts = array_merge($sortedAccounts, $this->addChildrenToArray($item['children']));
                        }
                    }
                }
                $filter = [];
                foreach ($sortedAccounts as $k => $sortedAccount) {
                    if(!isset($filter[$sortedAccount['code']])) {
                        $filter[$sortedAccount['code']] = true;
                    } else {
                        unset($sortedAccounts[$k]);
                    }
                }
                $account['children'] = $sortedAccounts;
            }
        }
        $report = $report_data['report'];
        if(!empty($report))
        {
            $data = $report_data['data'];
            $this->set('currency', $this->Journal->get_default_currency());
            $this->set('report', $report);

            $this->set('account_groups',$data);
        }else
        {
            $this->flashMessage(sprintf(__('No Report Found for This Id', true)));
            $this->redirect($this->referer());
        }
        $owner = getAuthOwner();
        $this->owner = $owner;
        $this->set('owner',$owner);
        $this->loadModel('CostCenter');
        $costCenterList = $this->CostCenter->get_secondary_cost_centers('list');
        $this->loadModel('FinancialYear');
        $financialYears = $this->FinancialYear->getFinancialYears(false);
        $this->set('financial_years',$financialYears);
        $maxLevelQuery = 'select max(accounts_cats_max_level.max_level) as max_level from (select max(level) as max_level from journal_accounts UNION select max(level) as max_level from journal_cats) as accounts_cats_max_level;';
        $maxLevel = $this->Journal->query($maxLevelQuery);
        $maxLevel = $maxLevel[0][0]['max_level'];
        $first_journal=$this->Journal->find('first',  array('fields'=>'date','order' => array('date' => 'ASC'), 'recursive' => -1));
        $this->set('first_journal', $first_journal);
        $this->set('is_balance_sheet', $report_id === "2");
        $this->set('maxLevel', $maxLevel);
        $this->set('filterParams', $this->params['url']);
        $this->set('title_for_layout',  __($report['title'],true));
        $this->set('costCenterList', $costCenterList);
    }


    /**
     *
     * @param type $report_id the report to get its data
     * @param type $use_in_other_place if isset to true the data won't be displayed but else returned to who ever called it
     */
    function owner_accounts_profit($report_id){
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $this->loadModel('Journal');
        $options = array();
        $this->Journal->update_cat_parent_ids();

        $periods = array(
            'monthly' => __('Monthly',true),
            'quarterly' => __('Quarterly',true),
            'annually' => __('Annually',true),
        );
        $this->set('periods',$periods);
        $report_data = $this->Journal->generate_statment_report($report_id);

        $report = $report_data['report'];
        $options = $report_data['options'];
        if(!empty($report))
        {
            $data = $report_data['data'];

            $this->set('currency', $this->Journal->get_default_currency());
            $this->set('report', $report);
            $this->set('options',$options);
            $this->set('account_groups',$data);
        }else
        {
            $this->flashMessage(sprintf(__('No Report Found for This Id', true)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if($_GET['quick'])
        {
            App::import('Vendor', 'settings');
            $settings_data[ReportsPlugin]['profit_report'] = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
            settings::setData($settings_data);
        }
        $owner = getAuthOwner();
        $this->owner = $owner;
        $this->set('owner',$owner);
        $this->set('title_for_layout',  __($report['title'],true));
    }

    public function owner_accounts_profit_accrual()
    {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        // This part was added because the update_account_parent_ids() function does not work properly when there are many accounts.
        ini_set('memory_limit','10G');
        App::import('vendor','CustomReport',['file'=>'CustomReport/autoload.php']);
        $report = new \CustomReport\AccountProfitAccrual($this->params['url']);
        $report->getReportData();
        $this->set('report', $report);
        if($_GET['quick'])
        {
            App::import('Vendor', 'settings');
            $settings_data[ReportsPlugin]['profit_report'] = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
            settings::setData($settings_data);
        }
        $this->loadModel('Journal');
        $this->Journal->update_account_parent_ids();
        $this->loadModel('CostCenter');
        $this->set('title_for_layout', __($this->viewVars["report"]->title, true));
        $costCenterList = $this->CostCenter->get_secondary_cost_centers('list');
        $this->loadModel('CostCenter');
        $this->set('costCenterList', $costCenterList);
        $this->loadModel('FinancialYear');
        $financialYears = $this->FinancialYear->getFinancialYears(false);
        $this->set('financial_years',$financialYears);


    }

    public function owner_load_report_chart_ajax()
    {
        $this->layout = '';
        $this->set('jsonParams',  $_POST['data']);
        $this->set('div',  'chart');
        echo $this->render('/elements/reports/charts');
        die;
    }

    function owner_list() {
        /** @var $appReportService appReportErrorHandlerDecorator */
        $appReportService = resolve(AppReportErrorHandlerDecorator::class);

        /** @todo refactor this and move it to service */
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $user = getAuthOwner();
        $this->set('user', $user);
        $this->set('cats', $this->getList());
        if(ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::WORKFLOW_PLUGIN)) {
            $workFlowType = GetObjectOrLoadModel('WorkflowType');
            $workflowTypes = $workFlowType->find('all', ['conditions' => ['WorkflowType.status' => 1]]);
            $this->set('workflowTypes', $workflowTypes);
        }

        $this->loadModel('Report');
        $typeGroupedJsonReports = $this->Report->getTypeGroupedReports();
        $this->set('typeGroupedJsonReports', $typeGroupedJsonReports);
        $this->set('appReports', $appReportService->get());
        $this->set('enable_bundle',settings::getValue(InventoryPlugin, 'enable_bundles'));
        $this->set('enableLotAndSerial', ifPluginActive(PRODUCT_TRACKING_PLUGIN));
        $this->set('title_for_layout',  __('Reports', true));

    }

    function getList($cat = false) {

        $owner = getAuthOwner();

        $today = format_date(date('Y-m-d'));
        $month_ago = format_date(date('Y-m-d', strtotime('-1 Month')));
        $quarter_ago = format_date(date('Y-m-d', strtotime('-3 Months')));
        $year_ago = format_date(date('Y-m-d', strtotime('-1 Year')));
        $current_year = format_date(date('Y').'-01-01');
        $current_year_end = format_date(date('Y').'-12-31');

        $local_today = format_date(date('Y-m-d'));
        $local_month_ago = format_date(date('Y-m-d', strtotime('-1 Month')));
        $local_quarter_ago = format_date(date('Y-m-d', strtotime('-3 Months')));
        $local_year_ago = format_date(date('Y-m-d', strtotime('-1 Year')));
        $Journal = GetObjectOrLoadModel('Journal');
        $clientsCat =$Journal->getAutoCat(['entity_type' => Journal::CLIENTS_CAT_ENTITY_TYPE, 'entity_id' => 0]);
        $clientsCatId = $clientsCat['JournalCat']['id'];
        $suppliersCat =$Journal->getAutoCat(['entity_type' => Journal::SUPPLIERS_CAT_ENTITY_TYPE, 'entity_id' => 0]);
        $supplierCatId = $suppliersCat['JournalCat']['id'];
        $menu = array(
            'invoices' => array(
                'title' => __('Sales Reports', true),
                'permissions_contain' => array(Invoices_View_Invoices_Details, Invoices_View_All_Invoices),
                'class' => 'revenue',
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'client_list' => array('plugin' => ClientsPlugin, 'title' => __('Clients List', true), 'url' => "/owner/reports/report/clients"),
                    'client_balance' => array('title' => __('Clients Balance', true), 'url' => "/owner/reports/report/client_balance"),
                    'client_aged_ledger' => array('title' => __('Aged Debtors', true).' ('.__('Ledger',true).')', 'url' => "/owner/reports/report/client_aged_ledger"),
                    'clients_sales' => array('title' => __('Clients Sales', true), 'url' => "/owner/reports/report/clients_sales"),
                    'clients_payments' => array('title' => __('Clients Payments', true), 'url' => "/owner/reports/report/clients_payments"),
                    'clients_ledger' => array('plugin' => AccountingPlugin, 'title' => __('Clients Statement', true), 'url' => "/owner/reports/journal_transactions?show_net=&report_type=transaction&date_from=&date_to=&group_by=account&journal_cat_id=$clientsCatId&parent=$clientsCatId&data%5Basc%5D=&currency=-1&parent_type=client"),
                    'daily_revenue_summary' => array('title' => __('Daily Revenue Summary', true), 'url' => "/owner/reports/revenue?report_type=period&date_from={$month_ago}&date_to={$today}&group_by=daily&currency=-1"),
                    'weekly_revenue_summary' => array('title' => __('Weekly Revenue Summary', true), 'url' => "/owner/reports/revenue?report_type=period&date_from={$quarter_ago}&date_to={$today}&group_by=weekly&currency=-1"),
                    'monthly_revenue_summary' => array('title' => __('Monthly Revenue Summary', true), 'url' => "/owner/reports/revenue?report_type=period&date_from={$year_ago}&date_to={$today}&group_by=monthly&currency=-1"),
                    'yearly_revenue_summary' => array('title' => __('Yearly Revenue Summary', true), 'url' => "/owner/reports/revenue?report_type=period&date_from=&date_to=&group_by=yearly&currency=-1"),
                    'client_revenue_summary' => array('title' => __('Revenue by Client Summary', true), 'url' => "/owner/reports/revenue?report_type=client&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'staff_revenue_summary' => array('plugin' => StaffPlugin, 'class' => 'user-summary', 'title' => __('Revenue by Staff Member Summary', true), 'url' => "/owner/reports/revenue?report_type=staff&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'sales_person_revenue_summary' => array('plugin' => StaffPlugin, 'class' => 'user-summary', 'title' => __('Revenue by Sales Person Summary', true), 'url' => "/owner/reports/revenue?report_type=sales_person&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'daily_revenue_detailed' => array('title' => __('Detailed Daily Revenue', true), 'url' => "/owner/reports/revenue?report_type=invoice&date_from={$month_ago}&date_to={$today}&group_by=daily&currency=-1"),
                    'weekly_revenue_detailed' => array('title' => __('Detailed Weekly Revenue', true), 'url' => "/owner/reports/revenue?report_type=invoice&date_from={$quarter_ago}&date_to={$today}&group_by=weekly&currency=-1"),
                    'monthly_revenue_detailed' => array('title' => __('Detailed Monthly Revenue', true), 'description' => 'some lines here and some lines here', 'url' => "/owner/reports/revenue?report_type=invoice&date_from={$year_ago}&date_to={$today}&group_by=monthly&currency=-1"),
                    'yearly_revenue_detailed' => array('title' => __('Detailed Yearly Revenue', true), 'url' => "/owner/reports/revenue?report_type=invoice&group_by=yearly&currency=-1"),
                    'client_revenue_detailed' => array('title' => __('Detailed Revenue by Client', true), 'url' => "/owner/reports/revenue?report_type=invoice&date_from={$month_ago}&date_to={$today}&group_by=client&currency=-1"),
                    'staff_revenue_detailed' => array('plugin' => StaffPlugin, 'class' => 'user-detailed', 'title' => __('Detailed Revenue by Staff Member', true), 'url' => "/owner/reports/revenue?report_type=invoice&date_from={$month_ago}&date_to={$today}&group_by=staff&currency=-1"),
                    'sales_person_revenue_detailed' => array('plugin' => StaffPlugin, 'class' => 'user-detailed', 'title' => __('Detailed Revenue by Sales Person', true), 'url' => "/owner/reports/revenue?report_type=invoice&date_from={$month_ago}&date_to={$today}&group_by=sales_person&currency=-1"),
                    'daily_payments_summary' => array('title' => __('Daily Payments (Summary)', true), 'url' => "/owner/reports/payments?group_by=daily&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_payments_summary' => array('title' => __('Weekly Payments (Summary)', true), 'url' => "/owner/reports/payments?group_by=weekly&is_summary=1&report_type=payment&type=payment&date_from={$quarter_ago}&date_to={$today}&currency=-1"),
                    'monthly_payments_summary' => array('title' => __('Monthly Payments (Summary)', true), 'url' => "/owner/reports/payments?group_by=monthly&is_summary=1&report_type=payment&type=payment&date_from={$year_ago}&date_to={$today}&currency=-1"),
                    'yearly_payments_summary' => array('title' => __('Yearly Payments (Summary)', true), 'url' => "/owner/reports/payments?group_by=yearly&is_summary=1&report_type=payment&type=payment&date_from=&date_to=&currency=-1"),
                    'staff_payments_summary' => array('plugin' => StaffPlugin, 'title' => __('Payments by Staff Member (Invoiced by) (Summary)', true), 'url' => "/owner/reports/payments?group_by=staff&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'collected_by_payments_summary' => array('title' => __('Payments by Staff Member(Collected by) (Summary)', true), 'url' => "/owner/reports/payments?group_by=collected_by&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'client_payments_summary' => array('title' => __('Payments by Client (Summary)', true), 'url' => "/owner/reports/payments?group_by=client&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'method_payments_summary' => array('title' => __('Payments by Method (Summary)', true), 'url' => "/owner/reports/payments?group_by=payment_method&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'daily_payments_detailed' => array('title' => __('Daily Payments (Detailed)', true), 'url' => "/owner/reports/payments?group_by=daily&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_payments_detailed' => array('title' => __('Weekly Payments (Detailed)', true), 'url' => "/owner/reports/payments?group_by=weekly&is_summary=0&report_type=payment&type=payment&date_from={$quarter_ago}&date_to={$today}&currency=-1"),
                    'monthly_payments_detailed' => array('title' => __('Monthly Payments (Detailed)', true), 'url' => "/owner/reports/payments?group_by=monthly&is_summary=0&report_type=payment&type=payment&date_from={$year_ago}&date_to={$today}&currency=-1"),
                    'yearly_payments_detailed' => array('title' => __('Yearly Payments (Detailed)', true), 'url' => "/owner/reports/payments?group_by=yearly&is_summary=0&report_type=payment&type=payment&date_from=&date_to=&currency=-1"),
                    'staff_payments_detailed' => array('plugin' => StaffPlugin, 'title' => __('Payments by Staff Member (Invoiced by) (Detailed)', true), 'url' => "/owner/reports/payments?group_by=staff&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'collected_by_payments_detailed' => array('title' => __('Payments by Staff Member (Collected by) (Detailed)', true), 'url' => "/owner/reports/payments?group_by=collected_by&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'client_payments_detailed' => array('title' => __('Payments by Client (Detailed)', true), 'url' => "/owner/reports/payments?group_by=client&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'method_payments_detailed' => array('title' => __('Payments by Method (Detailed)', true), 'url' => "/owner/reports/payments?group_by=payment_method&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'advance_payments_report_detailed' => array('title' => __('Advance payments report (Detailed)', true), 'url' => "/owner/reports/advance_payments?group_by=client&is_summary=0&report_type=client&type=advance_payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'advance_payments_report_summary' => array('title' => __('Advance payments report (Summary)', true), 'url' => "/owner/reports/advance_payments?group_by=client&is_summary=1&report_type=client&type=advance_payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                )
            ),
            'purchase_orders' => array(
                'title' => __('Purchases Reports', true),
                'permissions_contain' => array(View_his_own_created_Purchase_Orders, View_All_Purchase_Orders),
                'class' => 'revenue',
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'supplier_list' => array('title' => __('Suppliers List', true), 'url' => "/owner/reports/report/supplier"),
                    'supplier_balance' => array('title' => __('Suppliers Balance', true), 'url' => "/owner/reports/report/supplier_balance"),
                    'supplier_aged_ledger' => array('title' => __('Aged Creditors', true).' ('.__('Ledger',true).')', 'url' => "/owner/reports/report/supplier_aged_ledger"),
                    'purchases' => array('title' => __('Suppliers Purchase Invoices', true), 'url' => "/owner/reports/report/purchases"),
                    'paid_purchases' => array('title' => __('Purchase Order Payments', true), 'url' => "/owner/reports/report/paid_purchases"),
                    'supplier_ledger' => array('plugin' => AccountingPlugin, 'title' => __('Suppliers Statement', true), 'url' => "/owner/reports/journal_transactions?show_net=&report_type=transaction&date_from=&date_to=&group_by=account&journal_cat_id=$supplierCatId&parent=$supplierCatId&data%5Basc%5D=&currency=-1&parent_type=supplier"),

//                    'daily_revenue_summary' => array('title' => __('Daily Revenue Summary', true), 'url' => "/owner/reports/purchases?report_type=period&date_from={$month_ago}&date_to={$today}&group_by=daily&currency=-1"),
//                    'weekly_revenue_summary' => array('title' => __('Weekly Revenue Summary', true), 'url' => "/owner/reports/purchases?report_type=period&date_from={$quarter_ago}&date_to={$today}&group_by=weekly&currency=-1"),
//                    'monthly_revenue_summary' => array('title' => __('Monthly Revenue Summary', true), 'url' => "/owner/reports/purchases?report_type=period&date_from={$year_ago}&date_to={$today}&group_by=monthly&currency=-1"),
//                    'yearly_revenue_summary' => array('title' => __('Yearly Revenue Summary', true), 'url' => "/owner/reports/purchases?report_type=period&date_from=&date_to=&group_by=yearly&currency=-1"),
                    'client_revenue_summary' => array('title' => __('Revenue by Client Summary', true), 'url' => "/owner/reports/purchases?report_type=supplier&group_by=supplier&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'staff_revenue_summary' => array('plugin' => StaffPlugin, 'class' => 'user-summary', 'title' => __('Revenue by Staff Member Summary', true), 'url' => "/owner/reports/purchases?report_type=staff&group_by=staff&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'daily_revenue_detailed' => array('title' => __('Detailed Daily Revenue', true), 'url' => "/owner/reports/purchases?report_type=invoice&date_from={$month_ago}&date_to={$today}&group_by=daily&currency=-1"),
                    'weekly_revenue_detailed' => array('title' => __('Detailed Weekly Revenue', true), 'url' => "/owner/reports/purchases?report_type=invoice&date_from={$quarter_ago}&date_to={$today}&group_by=weekly&currency=-1"),
                    'monthly_revenue_detailed' => array('title' => __('Detailed Monthly Revenue', true), 'description' => 'some lines here and some lines here', 'url' => "/owner/reports/purchases?report_type=invoice&date_from={$year_ago}&date_to={$today}&group_by=monthly&currency=-1"),
                    'yearly_revenue_detailed' => array('title' => __('Detailed Yearly Revenue', true), 'url' => "/owner/reports/purchases?report_type=invoice&group_by=yearly&currency=-1"),
                    'client_revenue_detailed' => array('title' => __('Detailed Revenue by Client', true), 'url' => "/owner/reports/purchases?report_type=purchase_order&date_from={$month_ago}&date_to={$today}&group_by=supplier&currency=-1"),
                    'staff_revenue_detailed' => array('plugin' => StaffPlugin, 'class' => 'user-detailed', 'title' => __('Detailed Revenue by Staff Member', true), 'url' => "/owner/reports/purchases?report_type=invoice&date_from={$month_ago}&date_to={$today}&group_by=staff&currency=-1"),
                    'daily_payments_summary' => array('title' => __('Daily Payments (Summary)', true), 'url' => "/owner/reports/popayments?group_by=daily&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_payments_summary' => array('title' => __('Weekly Payments (Summary)', true), 'url' => "/owner/reports/popayments?group_by=weekly&is_summary=1&report_type=payment&type=payment&date_from={$quarter_ago}&date_to={$today}&currency=-1"),
                    'monthly_payments_summary' => array('title' => __('Monthly Payments (Summary)', true), 'url' => "/owner/reports/popayments?group_by=monthly&is_summary=1&report_type=payment&type=payment&date_from={$year_ago}&date_to={$today}&currency=-1"),
                    'yearly_payments_summary' => array('title' => __('Yearly Payments (Summary)', true), 'url' => "/owner/reports/popayments?group_by=yearly&is_summary=1&report_type=payment&type=payment&date_from=&date_to=&currency=-1"),
                    'staff_payments_summary' => array('plugin' => StaffPlugin, 'title' => __('Payments by Staff Member (Invoiced by) (Summary)', true), 'url' => "/owner/reports/popayments?group_by=staff&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'collected_by_payments_summary' => array('title' => __('Payments by Staff Member(Collected by) (Summary)', true), 'url' => "/owner/reports/popayments?group_by=collected_by&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'client_payments_summary' => array('title' => __('Payments by Client (Summary)', true), 'url' => "/owner/reports/popayments?group_by=client&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'method_payments_summary' => array('title' => __('Payments by Method (Summary)', true), 'url' => "/owner/reports/popayments?group_by=payment_method&is_summary=1&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'daily_payments_detailed' => array('title' => __('Daily Payments (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=daily&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_payments_detailed' => array('title' => __('Weekly Payments (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=weekly&is_summary=0&report_type=payment&type=payment&date_from={$quarter_ago}&date_to={$today}&currency=-1"),
                    'monthly_payments_detailed' => array('title' => __('Monthly Payments (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=monthly&is_summary=0&report_type=payment&type=payment&date_from={$year_ago}&date_to={$today}&currency=-1"),
                    'yearly_payments_detailed' => array('title' => __('Yearly Payments (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=yearly&is_summary=0&report_type=payment&type=payment&date_from=&date_to=&currency=-1"),
                    'staff_payments_detailed' => array('plugin' => StaffPlugin, 'title' => __('Payments by Staff Member (Invoiced by) (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=staff&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'collected_by_payments_detailed' => array('title' => __('Payments by Staff Member (Collected by) (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=collected_by&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'client_payments_detailed' => array('title' => __('Payments by Client (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=client&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'method_payments_detailed' => array('title' => __('Payments by Method (Detailed)', true), 'url' => "/owner/reports/popayments?group_by=payment_method&is_summary=0&report_type=payment&type=payment&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'product_purchases-product-details' => ['title' => __('Product Purchases Report - by Product', true), 'url' => "/owner/reports/report/product_purchases?summary=0&from_date={$local_month_ago}&to_date={$local_today}&group_by=Product&show_report=1"],
                    'product_purchases-product-summary' => ['title' => __('Product Purchases Report - by Product', true), 'url' => "/owner/reports/report/product_purchases?summary=1&from_date={$local_month_ago}&to_date={$local_today}&group_by=Product&show_report=1"],
                    'product_purchases-supplier-details' => ['title' => __('Product Purchases Report - by Supplier', true), 'url' => "/owner/reports/report/product_purchases?summary=0&from_date={$local_month_ago}&to_date={$local_today}&group_by=Supplier&show_report=1"],
                    'product_purchases-supplier-summary' => ['title' => __('Product Purchases Report - by Supplier', true), 'url' => "/owner/reports/report/product_purchases?summary=1&from_date={$local_month_ago}&to_date={$local_today}&group_by=Supplier&show_report=1"],
                    'product_purchases-staff-details' => ['title' => __('Product Purchases Report - by Staff', true), 'url' => "/owner/reports/report/product_purchases?summary=0&from_date={$local_month_ago}&to_date={$local_today}&group_by=Staff&show_report=1"],
                    'product_purchases-staff-summary' => ['title' => __('Product Purchases Report - by Staff', true), 'url' => "/owner/reports/report/product_purchases?summary=1&from_date={$local_month_ago}&to_date={$local_today}&group_by=Staff&show_report=1"],

                )
            ),
            'inventory' => array(
                'title' => __('Store Reports', true),
                'permissions_contain' => array(Track_Inventory),
                'class' => 'inventory',
                'plugin' => InventoryPlugin,
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'summary_of_stock_balances' => ['title' => 'Summary Of Stock Balance', 'url' => '/owner/reports/report/warehouse_stock_balance'],
                    'stocktaking_sheet' => array('title' => __('Stocktaking Sheet', true), 'url' => "/owner/products/stocktaking_sheet"),
                    'store_summary' => array('title' => __('Inventory transactions Summary', true), 'url' => "/owner/products/store_summary"),
                    'worthsheet' => array('title' => __('Inventory Value', true), 'url' => "/owner/products/worthsheet"),
                    'transaction' => array('title' => __('Inventory Transactions', true), 'url' => "/owner/reports/stock_transactions"),
                    'daily_report_summary' => array('title' => __('Daily Product (Summary)', true), 'url' => "/owner/reports/products?group_by=daily&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'daily_report_detailed' => array('title' => __('Daily Product (Detailed)', true), 'url' => "/owner/reports/products?group_by=daily&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_report_summary' => array('title' => __('Weekly Product (Summary)', true), 'url' => "/owner/reports/products?group_by=weekly&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_report_detailed' => array('title' => __('Weekly Product (Detailed)', true), 'url' => "/owner/reports/products?group_by=weekly&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'monthly_report_summary' => array('title' => __('Monthly Product (Summary)', true), 'url' => "/owner/reports/products?group_by=monthly&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'monthly_report_detailed' => array('title' => __('Monthly Product (Detailed)', true), 'url' => "/owner/reports/products?group_by=monthly&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'yearly_report_summary' => array('title' => __('Yearly Product (Summary)', true), 'url' => "/owner/reports/products?group_by=yearly&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'yearly_report_detailed' => array('title' => __('Yearly Product (Detailed)', true), 'url' => "/owner/reports/products?group_by=yearly&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'client_product_summary' => array('title' => __('Product Sales by Client(Summary)', true), 'url' => "/owner/reports/products?group_by=client&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'client_product_detailed' => array('title' => __('Product Sales by Client(Detailed)', true), 'url' => "/owner/reports/products?group_by=client&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'staff_product_summary' => array('title' => __('Product Sales by Staff Member(Summary)', true), 'url' => "/owner/reports/products?group_by=staff&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'staff_product_detailed' => array('title' => __('Product Sales by Staff Member(Detailed)', true), 'url' => "/owner/reports/products?group_by=staff&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'sales_person_product_summary' => array('title' => __('Product Sales by Sales Person(Summary)', true), 'url' => "/owner/reports/products?group_by=sales_person&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'sales_person_product_detailed' => array('title' => __('Product Sales by Sales Person(Detailed)', true), 'url' => "/owner/reports/products?group_by=sales_person&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'product_product_summary' => array('title' => __('Product by Product (Summary)', true), 'url' => "/owner/reports/products?group_by=product&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'product_product_detailed' => array('title' => __('Product by Product (Detailed)', true), 'url' => "/owner/reports/products?group_by=product&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'category_product_summary' => array('title' => __('Product by Product (Summary)', true), 'url' => "/owner/reports/products?group_by=category&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'category_product_detailed' => array('title' => __('Product by Product (Detailed)', true), 'url' => "/owner/reports/products?group_by=category&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'brand_product_summary' => array('title' => __('Product by Product (Summary)', true), 'url' => "/owner/reports/products?group_by=brand&is_summary=1&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'brand_product_detailed' => array('title' => __('Product by Product (Detailed)', true), 'url' => "/owner/reports/products?group_by=brand&is_summary=0&report_type=product&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'bundles_list' => ['title' => __('Bundles List', true), 'url' => '/owner/reports/report/bundle'],
                    'bundle_units' => ['title' => __('Available Quantity for Bundles', true), 'url' => '/owner/reports/report/bundle?allow_unit=1'],
                    'product_average_cost' => ['title' => __('Product Average Cost', true), 'url' => "/owner/reports/report/product_average_cost?from_date=$month_ago&to_date=$today&summary=1&show_report=1"],
                    'detailed_stock_transactions' => ['title' => __('Detailed Stock Transactions For Each Product', true), 'url' => "/owner/reports/report/detailed_stock_transactions?from_date=$month_ago&to_date=$today&currency=All&summary=1&show_report=1"],
                    'inventory_turnover' => ['title' => __('Inventory Turnover', true), 'url' => "/owner/reports/report/inventory_turnover?from_date=$month_ago&to_date=$today"],
                )
            ),
            'accounting' => array(
                'title' => __('Accounting Reports', true),
                'permissions_contain' => array(View_All_Tax_Report,View_His_Own_Reports,View_All_expenses,View_All_incomes,VIEW_ALL_JOURNALS),
                'class' => 'revenue',
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'tax_summary' => array('permissions_must' => [View_All_Tax_Report], 'title' => __('Tax Summary ', true), 'url' => "/owner/reports/taxes?details=0&tax=&invoice_type=accrual&date_to=$today&date_from=$month_ago&currency=".$owner['currency_code']),
                    'tax_details' => array('permissions_must' => [View_All_Tax_Report], 'title' => __('Tax Details', true), 'url' => "/owner/reports/taxes?details=1&tax=&invoice_type=accrual&date_to=$today&date_from=$month_ago&currency=".$owner['currency_code']),
	                'tax_declartion' => array('permissions_must' => [View_All_Tax_Report], 'title' => __('Tax Summary ', true), 'url' => "/owner/reports/new_taxes?details=0&tax=&invoice_type=accrual&date_to=$today&date_from=$month_ago&currency=".$owner['currency_code']),
	                'financial_transactions' => array('permissions_must' => [View_All_expenses,View_All_incomes], 'title' => __('Financial Transactions', true), 'url' => "/owner/reports/financial_report?date_range_selector=lastyear"),
                    'products_profit' => array('title' => __('Product Sales Profit', true), 'url' => "/owner/products/products_profit"),
                    'income_statement' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'title' => __('Income Statement', true), 'url' => "/owner/reports/accounts/1"),
                    'balance_sheet' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'title' => __('Balance Sheet', true), 'url' => "/owner/reports/accounts/2"),
                    'trial_balance_only' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'title' => __('Trial Balance Report', true), 'url' => "/owner/reports/journal_transactions?report_type=cat&date_to={$local_today}&group_by=&journal_account_id=&staff_id=&currency=-1&type=trial-balance"),
                    'trial_nets' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'title' => __('Trial Nets Report', true), 'url' => "/owner/reports/journal_transactions?balance_only=1&report_type=cat&show_net=1&date_from=&date_to={$local_today}&group_by=&journal_account_id=&staff_id=&currency=-1&type=trial-net"),
                    'trial_balance&nets' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'title' => __('Trial Balance & Nets Report', true), 'url' => "/owner/reports/journal_transactions?report_type=cat&show_net=1&date_from={$current_year}&date_to={$local_today}&group_by=&journal_account_id=&staff_id=&currency=-1&type=trial-balance-nets"),
                    'ledger' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'plugin' => AccountingPlugin, 'title' => __('Ledger', true), 'url' => "/owner/reports/journal_transactions?show_net=&date_from=&date_to=&report_type=transaction&group_by=account&journal_cat_id=&journal_account_id=&staff_id=&currency=-1"),
                    'journals' => ['permissions_must' => [VIEW_ALL_JOURNALS], 'title' => __('Journals', true), 'url' => "/owner/reports/report/journals?summary=1"],
                    'journal_transactions' => ['permissions_must' => [VIEW_ALL_JOURNALS], 'title' => __('Journal Transactions', true), 'url' => "/owner/reports/report/journal_transactions?summary=0"],
                    'chart_of_accounts' => ['permissions_must' => [VIEW_ALL_JOURNALS, View_His_Own_Reports], 'title' => __('Chart of Accounts Directory', true), 'url' => "/owner/reports/report/chart_of_accounts"],
                    'cost_center' => array('permissions_must' => [VIEW_ALL_JOURNALS, VIEW_COST_CENTERS], 'title' => __('Cost Centers Report', true), 'url' => "/owner/reports/cost_centers"),
                    'profit_lostt_cash' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'plugin' => ExpensesPlugin, 'title' => __('Cash', true), 'url' => "/owner/reports/profit?type=monthly&date_from={$year_ago}&date_to={$today}"),
                    'profit_lostt_accural' => array('permissions_must' => [VIEW_ALL_JOURNALS], 'plugin' => AccountingPlugin, 'title' => __('Accrual', true), 'url' => "/owner/reports/accounts_profit_accrual?type=monthly&date_from={$year_ago}&date_to={$today}"),
                    'cash_flow' => array('title' => __('Cash Flow Report', true), 'url' => "/owner/reports/cash_flow?date_from={$year_ago}&date_to={$today}"),

                    'daily_profit_report_summary' => array('title' => __('Daily Profit (Summary)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=daily&is_summary=1&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'daily_profit_report_detailed' => array('title' => __('Daily Profit (Detailed)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=daily&is_summary=0&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_profit_report_summary' => array('title' => __('Weekly Profit (Summary)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=weekly&is_summary=1&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'weekly_profit_report_detailed' => array('title' => __('Weekly Profit (Detailed)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=weekly&is_summary=0&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'monthly_profit_report_summary' => array('title' => __('Monthly Profit (Summary)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=monthly&is_summary=1&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'monthly_profit_report_detailed' => array('title' => __('Monthly Profit (Detailed)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=monthly&is_summary=0&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'yearly_profit_report_summary' => array('title' => __('Yearly Profit (Summary)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=yearly&is_summary=1&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'yearly_profit_report_detailed' => array('title' => __('Yearly Profit (Detailed)', true), 'url' => "/owner/reports/stock_transactions_profit?group_by=yearly&is_summary=0&report_type=&type=product&product_id=&category=&brand=&staff_id=&date_from={$month_ago}&date_to={$today}&currency=-1"),
                    'asset' => ['permissions_must' => [View_His_Own_Reports], 'title' => __('Assets', true), 'url' => "/owner/reports/report/assets"],
                )
            ),
            'cheques' => array(
                'title' => __('Cheques Reports', true),
                'permissions_contain' => array(View_His_Own_Reports),
                'class' => 'revenue',
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'payable_cheques' => array('permissions_must' => [], 'title' => __('Payable Cheques', true), 'url' => "/owner/reports/report/payable_cheques"),
                    'receivable_cheques' => array('permissions_must' => [], 'title' => __('Receivable Cheques', true), 'url' => "/owner/reports/report/receivable_cheques"),
                )
            ),
            'time_tracking' => array(
                'title' => __('Time-Tracking Reports', true),
                'permissions_contain' => array(/*Enter_Timesheet,*/ Track_All_Staffs_Times , Edit_All_Timesheets),
                'class' => 'revenue',
                'plugin' => TimeTrackingPlugin,
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'staff_report_summary' => array('title' => __('Staff Report Summary', true), 'url' => "/owner/time_tracking/report?group=staff&staff_id=&project_id=&activity_id=&summary=yes&date_from={$local_month_ago}&date_to={$local_today}"),
                    'staff_report_detailed' => array('title' => __('Staff Report (Detailed)', true), 'url' => "/owner/time_tracking/report?group=staff&staff_id=&project_id=&activity_id=&summary=no&date_from={$local_month_ago}&date_to={$local_today}"),
                    'project_report_summary' => array('title' => __('Project Report Summary', true), 'url' => "/owner/time_tracking/report?group=project&staff_id=&project_id=&activity_id=&summary=yes&date_from={$local_month_ago}&date_to={$local_today}"),
                    'project_report_detailed' => array('title' => __('Project Report (Detailed)', true), 'url' => "/owner/time_tracking/report?group=project&staff_id=&project_id=&activity_id=&summary=no&date_from={$local_month_ago}&date_to={$local_today}"),
                    'activity_report_summary' => array('title' => __('Activity Report Summary', true), 'url' => "/owner/time_tracking/report?group=activity&staff_id=&project_id=&activity_id=&summary=yes&date_from={$local_month_ago}&date_to={$local_today}"),
                    'activity_report_detailed' => array('title' => __('Activity Report (Detailed)', true), 'url' => "/owner/time_tracking/report?group=activity&staff_id=&project_id=&activity_id=&summary=no&date_from={$local_month_ago}&date_to={$local_today}"),
                    'daily_report_summary' => array('title' => __('Daily Report Summary', true), 'url' => "/owner/time_tracking/report?group=daily&staff_id=&project_id=&activity_id=&summary=yes&date_from={$local_month_ago}&date_to={$local_today}"),
                    'daily_report_detailed' => array('title' => __('Daily Report (Detailed)', true), 'url' => "/owner/time_tracking/report?group=daily&staff_id=&project_id=&activity_id=&summary=no&date_from={$local_month_ago}&date_to={$local_today}"),
                    'weekly_report_summary' => array('title' => __('Weekly Report Summary', true), 'url' => "/owner/time_tracking/report?group=weekly&staff_id=&project_id=&activity_id=&summary=yes&date_from={$local_quarter_ago}&date_to={$local_today}"),
                    'weekly_report_detailed' => array('title' => __('Weekly Report (Detailed)', true), 'url' => "/owner/time_tracking/report?group=weekly&staff_id=&project_id=&activity_id=&summary=no&date_from={$local_quarter_ago}&date_to={$local_today}"),
                    'monthly_report_summary' => array('title' => __('Monthly Report Summary', true), 'url' => "/owner/time_tracking/report?group=monthly&staff_id=&project_id=&activity_id=&summary=yes&date_from={$local_year_ago}&date_to={$local_today}"),
                    'monthly_report_detailed' => array('title' => __('Monthly Report (Detailed)', true), 'url' => "/owner/time_tracking/report?group=monthly&staff_id=&project_id=&activity_id=&summary=no&date_from={$local_year_ago}&date_to={$local_today}"),
                    'yearly_report_summary' => array('title' => __('Yearly Report Summary', true), 'url' => "/owner/time_tracking/report?group=yearly&staff_id=&project_id=&activity_id=&summary=yes&date_from=&date_to={$local_today}"),
                    'yearly_report_detailed' => array('title' => __('Yearly Report (Detailed)', true), 'url' => "/owner/time_tracking/report?group=yearly&staff_id=&project_id=&activity_id=&summary=no&date_from=&date_to={$local_today}"),
                )
            ),
            'finance' => array(
                'title' => __('Finance Reports', true),
                'permissions_contain' => array(View_his_own_expenses, View_All_expenses, View_his_own_incomes, View_All_incomes),
                'class' => 'revenue',
                'plugin' => ExpensesPlugin,
                "icon-class" => "icon-pie-chart",
                'subs' => []
            ),
            'work_orders' => array(
                'title' => __('Work Order Reports', true),
                'permissions_contain' => array(VIEW_ALL_WORK_ORDERS),
                'class' => 'revenue',
                'plugin' => WorkOrderPlugin,
                "icon-class" => "icon-pie-chart",
                'subs' => [
                    'work_orders' => array('title' => __('Work Order', true), 'url' => "/owner/reports/work_orders"),
                    'work_orders_tags' => array('title' => __('Work Order by Tags', true), 'url' => "/owner/reports/tags?group_by=tag&is_summary=&report_type=tags&type=tags&tag_id=-1&item_type=9&date_from=&date_to="),
                    'work_orders_appointment' => array('title' => __('Work Orders Appointments', true), 'url' => "/owner/reports/report/work_order_appointment"),
                    'work_orders_profit_summary' => array('title' => __('Work Orders Profit', true).' - '. __('Summary', true), 'url' => "/owner/reports/report/work_order_profit_summary"),
                    'work_orders_profit_details' => array('title' => __('Work Orders Profit', true).' - '. __('Details', true), 'url' => "/owner/reports/report/work_order_profit_details"),
                ]
            ),
            'clients' => array(
                'title' => __('Clients\' Reports', true),
                'permissions_contain' => array(VIEW_ALL_CLIENTS_REPORTS, VIEW_HIS_OWN_CLIENTS_REPORTS),
                'class' => 'revenue',
//                'plugin' => ExpensesPlugin,
                "icon-class" => "icon-pie-chart",
                'subs' => [
                    'aged_debtors' => array('title' => __('Aged Debtors', true).' ('.__('Invoices',true).')', 'url' => "/owner/reports/aged_debtors"),
                    'appointments' => array('title' => __('Clients Appointments', true), 'url' => "/owner/reports/report/appointment"),
                    'installments' => array('title' => __('Clients Installments', true), 'url' => "/owner/reports/report/installments?from_date={$local_month_ago}&to_date={$local_today}&show_report=1"),
                ]
            ),
            'sms_reports' => array(
                'title' => __('SMS Reports', true),
                'permissions_contain' => array(Edit_General_Settings),
                'class' => 'sms',
                'plugin' => SMSPlugin,
                "icon-class" => "icon-pie-chart",
                'subs' => []
            ),
            'pnr_report' => array(
                'title' => __('Pnr Report', true),
                'permissions_contain' => array(VIEW_ALL_WORK_ORDERS),
                'class' => 'pnr_report',
                'plugin' => BnrPlugin,
                "icon-class" => "icon-pie-chart",
                'subs' => []
            ),
            'employees_reports' => [
                'title' => __('Employees Reports', true),
                'permissions_contain' => array(View_His_Own_Reports, VIEW_ATTENDANCE_REPORT, VIEW_PAY_RUN, PermissionUtil::MANAGE_EMPLOYEE_DOCUMENTS,PermissionUtil::VIEW_EMPLOYEE_DOCUMENTS,PermissionUtil::MANAGE_HRM_SYSTEM),
                'class' => 'revenue',
                'plugin' => HRM_PLUGIN,
                "icon-class" => "icon-pie-chart",
                "subs" => [
                    'employees_residency_status' => ['title' => __('Employees Residency Status Report', true), 'url' => '/owner/reports/report/employees_residency_status'],
                    'required_documents_compliance_report' => ['title' => __('Required Documents Compliance Report', true), 'url' => '/owner/reports/report/required_documents_compliance_report'],
                    'employees_documents_summary' => ['title' => __('Employee Documents - Summary Report', true), 'url' => '/owner/reports/report/employees_documents_summary'],
                    'employees_expiry_report' => ['title' => __('Employee Documents - Expiry Report', true), 'url' => '/owner/reports/report/employees_expiry_report'],
                    'employees_details_report' => ['title' => __('Employee Documents Details Report', true), 'url' => '/owner/reports/report/employees_details_report'],
                ]
            ],
            "product_tracking_reports" => [
                "title" => __("Product Tracking Reports", true),
                "subs" => [
                    'tracking_products_by_lot_and_expiry_date' => ['title' => __('Tracking Products with Lot and Expiry date', true), 'url' => "/owner/reports/report/tracking_products_by_lot_and_expiry_date?brand=&summary=&show_report=1"],
                    'tracking_products_by_serial'=> ['title' => __('Tracking Products with Serial', true), 'url' => "/owner/reports/report/tracking_products_by_serial"],
                    'tracking_products_by_lot'=> ['title' => __('Tracking Products with Lot', true), 'url' => "/owner/reports/report/tracking_products_by_lot?show_report=1"],
                    'tracking_products_by_expiry_date' => ['title' => __('Tracking Products with Expiry date', true), 'url' => "/owner/reports/report/tracking_products_by_expiry_date?from_date={$local_month_ago}&to_date={$local_today}&show_report=1"],
                ]
            ],
            "credits_reports" => [
                "title" => __("Credit Reports",true),
                'permissions_contain' => [],
                "class" => "",
                "plugin" => CREDIT_PLUGIN,
                "icon-class" => "",
                "subs" => [
                    'detailed_credit_usage_report' => ['title' => __('Credit Usages', true), 'url' => "/owner/reports/report/credit_usages?from_date={$local_month_ago}&to_date={$local_today}&client_id=&credit_type=&employee=&group_by=Credit+Type&summary=0&show_report=1"],
                    'summary_credit_usage_report' => ['title' => __('Credit Usages', true), 'url' => "/owner/reports/report/credit_usages?from_date={$local_month_ago}&to_date={$local_today}&client_id=&credit_type=&employee=&group_by=Credit+Type&summary=1&show_report=1"],
                    'detailed_credit_charge_report' => ['title' => __('Credit Charges', true), 'url' => "/owner/reports/report/credit_charge_details?from_date={$local_month_ago}&to_date={$local_today}&client_id=&credit_type=&employee=&group_by=Credit+Type&summary=0&show_report=1"],
                    'summary_credit_charge_report' => ['title' => __('Credit Charges', true), 'url' => "/owner/reports/report/credit_charge_details?from_date={$local_month_ago}&to_date={$local_today}&client_id=&credit_type=&employee=&group_by=Credit+Type&summary=1&show_report=1"],
                    'detailed_expired_memberships_report' => ['title' => __('Expired Memberships Details', true), 'url' => "/owner/reports/report/expired_memberships?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=0&show_report=1"],
                    'summary_expired_memberships_report' => ['title' => __('Expired Memberships Details', true), 'url' => "/owner/reports/report/expired_memberships?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=1&show_report=1"],
                ]
            ],
            "memberships_reports" => [
                "title" => __("Memberships Reports", true),
                'permissions_contain' => [],
                "class" => "",
                "plugin" => MEMBERSHIP_PLUGIN,
                "icon-class" => "",
                "subs" => [
                    'detailed_expired_memberships_report' => ['title' => __('Expired Memberships', true), 'url' => "/owner/reports/report/expired_memberships?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=0&show_report=1"],
                    'summary_expired_memberships_report' => ['title' => __('Expired Memberships', true), 'url' => "/owner/reports/report/expired_memberships?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=1&show_report=1"],
                    'detailed_memberships_subscriptions_report' => ['title' => __('Memberships Subscriptions', true), 'url' => "/owner/reports/report/memberships_subscriptions?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=0&show_report=1"],
                    'summary_memberships_subscriptions_report' => ['title' => __('Memberships Subscriptions', true), 'url' => "/owner/reports/report/memberships_subscriptions?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=1&show_report=1"],
                    'detailed_new_memberships_report' => ['title' => __('New Memberships', true), 'url' => "/owner/reports/report/new_membership?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=0&show_report=1"],
                    'summary_new_memberships_report' => ['title' => __('New Memberships', true), 'url' => "/owner/reports/report/new_membership?from_date={$local_month_ago}&to_date={$local_today}&client_id=&group_by=Monthly&summary=1&show_report=1"],
                ]
            ],
            "rental_reports" => [
                "title" => __("Rental Reports", true),
                'permissions_contain' => [View_His_Own_Reports],
                "class" => "",
                "plugin" => RENTAL_PLUGIN,
                "icon-class" => "icon-pie-chart",
                "subs" => [
                    'unit_availability' => ['title' => __('Units Availability', true), 'url' => "/v2/owner/reports/rental/unit-availability"],
                    'units_pricing' => ['title' => __('Units Pricing', true), 'url' => "/v2/owner/reports/rental/units-pricing"],

                    'summary_unit_type_report' => ['title' => sprintf( __('Units Income and Expenses by  %s', true),  __('Unit Type', true) ), 'url' => "/owner/reports/report/rental_reports?date_from={$local_month_ago}&date_to={$local_today}&group_by=Unit+Type&currency=All&show_report=1"],
                    'summary_unit_report' => ['title' => sprintf(__('Units Income and Expenses by  %s', true),__('Unit Name', true)), 'url' => "/owner/reports/report/rental_reports?date_from={$local_month_ago}&date_to={$local_today}&group_by=Unit&currency=All&show_report=1"],
                    'summary_unit_daily_report' => ['title' => sprintf(__('%s Units Income and Expenses', true),__('Daily',true) ), 'url' => "/owner/reports/report/rental_reports?date_from={$local_month_ago}&date_to={$local_today}&group_by=Daily&currency=All&show_report=1"],
                    'summary_unit_weekly_date_report' => ['title' => sprintf(__('%s Units Income and Expenses', true),__('Weekly', true) ), 'url' => "/owner/reports/report/rental_reports?date_from={$local_month_ago}&date_to={$local_today}&group_by=Weekly&currency=All&show_report=1"],
                    'summary_unit_monthly_report' => ['title' => sprintf(__('%s Units Income and Expenses', true),__('Monthly', true) ), 'url' => "/owner/reports/report/rental_reports?date_from={$local_month_ago}&date_to={$local_today}&group_by=Monthly&currency=All&show_report=1"],
                ]
                ],
            'manufacturing' => array(
                'title' => __('Manufacturing Reports', true),
                'permissions_contain' => [VIEW_ALL_MANUFACTURING_ORDERS, VIEW_HIS_OWN_MANUFACTURING_ORDERS],
                'class' => 'revenue',
                "plugin" => MANUFACTURING_PLUGIN,
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'manufacturing_order_costs' => array('permissions_must' => [PermissionUtil::VIEW_HIS_OWN_MANUFACTURING_ORDERS], 'title' => __('Manufacturing Order Costs', true), 'url' => "/owner/reports/report/manufacturing_order_costs"),
                )
            ),
            'lease_contract' => array(
                'title' => __('Lease Contracts Report', true),
                'permissions_contain' => array(VIEW_RESERVATION_ORDERS),
                'class' => 'revenue',
                "plugin" => LEASE_CONTRACT_PLUGIN,
                "icon-class" => "icon-pie-chart",
                'subs' => array(
                    'lease_contract_installment' => array('permissions_must' => [VIEW_RESERVATION_ORDERS], 'title' => __('Lease Contracts Installments', true), 'url' => "/owner/reports/report/contract_installments"),
                    'summary_unit_type_report' => ['permissions_must' => [VIEW_RESERVATION_ORDERS],'title' => sprintf( __('Units Income and Expenses by  %s', true),  __('Unit Type', true) ), 'url' => "/owner/reports/report/lease_contract_income_and_expenses?date_from={$local_month_ago}&date_to={$local_today}&group_by=Unit+Type&currency=All&show_report=1"],
                    'summary_unit_report' => ['permissions_must' => [VIEW_RESERVATION_ORDERS],'title' => sprintf(__('Units Income and Expenses by  %s', true),__('Unit Name', true)), 'url' => "/owner/reports/report/lease_contract_income_and_expenses?date_from={$local_month_ago}&date_to={$local_today}&group_by=Unit&currency=All&show_report=1"],
                    'summary_unit_daily_report' => ['permissions_must' => [VIEW_RESERVATION_ORDERS],'title' => sprintf(__('%s Units Income and Expenses', true),__('Daily',true) ), 'url' => "/owner/reports/report/lease_contract_income_and_expenses?date_from={$local_month_ago}&date_to={$local_today}&group_by=Daily&currency=All&show_report=1"],
                    'summary_unit_weekly_date_report' => ['permissions_must' => [VIEW_RESERVATION_ORDERS],'title' => sprintf(__('%s Units Income and Expenses', true),__('Weekly', true) ), 'url' => "/owner/reports/report/lease_contract_income_and_expenses?date_from={$local_month_ago}&date_to={$local_today}&group_by=Weekly&currency=All&show_report=1"],
                    'summary_unit_monthly_report' => ['permissions_must' => [VIEW_RESERVATION_ORDERS],'title' => sprintf(__('%s Units Income and Expenses', true),__('Monthly', true) ), 'url' => "/owner/reports/report/lease_contract_income_and_expenses?date_from={$local_month_ago}&date_to={$local_today}&group_by=Monthly&currency=All&show_report=1"],
                )
            ),
        );
        $employees_subs_array = $menu['employees_reports']['subs'];
        if(ifPluginActive(HRM_ATTENDANCE_PLUGIN)){
            $employees_subs_array = array_merge($employees_subs_array, [
                'summary_attendance_report_employee' => ['title' => __('Summary Attendance Report - by Employee', true), 'url' => "/owner/reports/report/attendance_sheets?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&department_id=&designation_id=&branch_id=&shift_id=&group_by=Employee&summary=&show_report=1"],
                'summary_attendance_report_month' => ['title' => __('Summary Attendance Report - by Month', true), 'url' => "/owner/reports/report/attendance_sheets?staff_id=&from_date={$local_year_ago}&to_date={$local_today}&department_id=&designation_id=&branch_id=&shift_id=&group_by=Monthly&summary=&show_report=1"],
                'summary_attendance_report_day' => ['title' => __('Summary Attendance Report - by Day', true), 'url' => "/owner/reports/report/attendance_sheets?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&department_id=&designation_id=&branch_id=&shift_id=&group_by=Daily&summary=&show_report=1"],
                'summary_attendance_report_week' => ['title' => __('Summary Attendance Report - by Week', true), 'url' => "/owner/reports/report/attendance_sheets?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&department_id=&designation_id=&branch_id=&shift_id=&group_by=Weekly&summary=&show_report=1"],
                'summary_attendance_report_year' => ['title' => __('Summary Attendance Report - by Year', true), 'url' => "/owner/reports/report/attendance_sheets?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&department_id=&designation_id=&branch_id=&shift_id=&group_by=Yearly&summary=&show_report=1"],
                'summary_attendance_report_department' => ['title' => __('Summary Attendance Report - by Department', true), 'url' => "/owner/reports/report/attendance_sheets?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&department_id=&designation_id=&branch_id=&shift_id=&group_by=Department&summary=&show_report=1"],
                'detailed_attendance_report_multiple_employees' => ['title' => __('Detailed Attendance Report (Multiple Employees)', true), 'url' => "/v2/owner/reports/attendance/attendance_multiple"],
                'detailed_attendance_report_single_employees' => ['title' => __('Detailed Attendance Report (Single Employee)', true), 'url' => "/v2/owner/reports/attendance/attendance"],
                'leave_balance_report' => ['title' => __('Employees Leave Balance Report', true), 'url' => "/v2/owner/reports/attendance/leave-balance"],
                'allocated_shift_report' => ['title' => __('Attendance Shift Report', true), 'url' => "/v2/owner/reports/attendance/shift"],
                'detailed_attendance_sheets' => ['title' => __('Attendance Sheets Report', true), 'url' => "/owner/reports/report/detailed_attendance_sheets?staff_id=&department_id=&designation_id=&employment_level_id=&employment_type_id=&attendance_shift_id=&active=&date_from={$current_year}&date_to={$current_year_end}&summary=&show_report=1"],
            ]);
        }
        if(ifPluginActive(HRM_PAYROLL_PLUGIN)){
            $employees_subs_array = array_merge($employees_subs_array , [
                'detailed_payroll_report_employee' => ['title' => __('Salaries Report - by Employee', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Employee&summary=&show_report=1"],
                'summary_payroll_report_employee' => ['title' => __('Salaries Report - by Employee', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Employee&summary=1&show_report=1"],
                'detailed_payroll_report_yearly' => ['title' => __('Salaries Report - by Year', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Yearly&summary=&show_report=1"],
                'summary_payroll_report_yearly' => ['title' => __('Salaries Report - by Year', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Yearly&summary=1&show_report=1"],
                'detailed_payroll_report_monthly' => ['title' => __('Salaries Report - by Month', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_year_ago}&to_date={$local_today}&group_by=Monthly&summary=&show_report=1"],
                'summary_payroll_report_monthly' => ['title' => __('Salaries Report - by Month', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_year_ago}&to_date={$local_today}&group_by=Monthly&summary=1&show_report=1"],
                'detailed_payroll_report_department' => ['title' => __('Salaries Report - by Department', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Department&summary=&show_report=1"],
                'summary_payroll_report_department' => ['title' => __('Salaries Report - by Department', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Department&summary=1&show_report=1"],
                'loans_report' => ['title' => __('Employees Loans Report', true), 'url' => "/owner/reports/report/loans"],
                'contracts_report' => ['title' => __('Employees Contracts Report', true), 'url' => "/owner/reports/report/contracts"],
                'detailed_payroll_report_branch' => ['plugin' => BranchesPlugin, 'title' => __('Salaries Report - by Branch', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Branch&summary=&show_report=1"],
                'summary_payroll_report_branch' => ['plugin' => BranchesPlugin, 'title' => __('Salaries Report - by Branch', true), 'url' => "/owner/reports/report/payslips?staff_id=&from_date={$local_month_ago}&to_date={$local_today}&group_by=Branch&summary=1&show_report=1"]
            ]);
        }
        $menu['employees_reports']['subs'] = $employees_subs_array;
        $finance_subs_array = [];
        if ( check_permission (View_All_incomes)){
            $finance_subs_array = array(
                'daily_incomes_summary' => array('title' => __('Daily Incomes (Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Daily&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'weekly_incomes_summary' => array('title' => __('Weekly Incomes (Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Weekly&summary=1&from_date={$quarter_ago}&to_date={$today}&show_report=1"),
                'monthly_incomes_summary' => array('title' => __('Monthly Incomes (Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Monthly&summary=1&from_date={$year_ago}&to_date={$today}&show_report=1"),
                'yearly_incomes_summary' => array('title' => __('Yearly Incomes (Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Yearly&summary=1&from_date=&to_date=&show_report=1"),
                'staff_incomes_summary' => array('plugin' => StaffPlugin, 'title' => __('Incomes by Staff Member(Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Staff&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'client_incomes_summary' => array('title' => __('Incomes by Client (Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Client&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'category_incomes_summary' => array('title' => __('Incomes by Category (Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Category&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'vendor_incomes_summary' => array('title' => __('Incomes by Vendor (Summary)', true), 'url' => "/owner/reports/report/incomes?group_by=Vendor&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'daily_incomes_detailed' => array('title' => __('Daily Incomes (Detailed)', true), 'url' => "/owner/reports/report/incomes?group_by=Daily&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'weekly_incomes_detailed' => array('title' => __('Weekly Incomes (Detailed)', true), 'url' => "/owner/reports/report/incomes?group_by=Weekly&summary=0&from_date={$quarter_ago}&to_date={$today}&show_report=1"),
                'monthly_incomes_detailed' => array('title' => __('Monthly Incomes (Detailed)', true), 'url' => "/owner/reports/report/incomes?group_by=Monthly&summary=0&from_date={$year_ago}&to_date={$today}&show_report=1"),
                'yearly_incomes_detailed' => array('title' => __('Yearly Incomes (Detailed)', true), 'url' => "/owner/reports/report/incomes?group_by=Yearly&summary=0&from_date=&to_date=&show_report=1"),
                'staff_incomes_detailed' => array('plugin' => StaffPlugin, 'title' => __('Incomes by Staff Member(Detailed) ', true), 'url' => "/owner/reports/report/incomes?group_by=Staff&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'client_incomes_detailed' => array('title' => __('Incomes by Client (Detailed)', true), 'url' => "/owner/reports/report/incomes?group_by=Client&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'category_incomes_detailed' => array('title' => __('Incomes by Category (Detailed)', true), 'url' => "/owner/reports/report/incomes?group_by=Category&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'vendor_incomes_detailed' => array('title' => __('Incomes by Vendor (Detailed)', true), 'url' => "/owner/reports/report/incomes?group_by=Vendor&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
            );
        }
        if ( check_permission (View_All_expenses) ){
            $finance_subs_array = array_merge ($finance_subs_array , [
                'daily_expenses_summary' => array('title' => __('Daily Expenses (Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Daily&summary=1&date_from={$month_ago}&date_to={$today}&show_report=1"),
                'weekly_expenses_summary' => array('title' => __('Weekly Expenses (Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Weekly&summary=1&from_date={$quarter_ago}&to_date={$today}&show_report=1"),
                'monthly_expenses_summary' => array('title' => __('Monthly Expenses (Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Monthly&summary=1&from_date={$year_ago}&to_date={$today}&show_report=1"),
                'yearly_expenses_summary' => array('title' => __('Yearly Expenses (Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Yearly&summary=1&date_from=&date_to=&show_report=1"),
                'staff_expenses_summary' => array('plugin' => StaffPlugin, 'title' => __('Expenses by Staff Member(Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Staff&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'client_expenses_summary' => array('title' => __('Expenses by Client (Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Client&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'category_expenses_summary' => array('title' => __('Expenses by Category (Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Category&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'vendor_expenses_summary' => array('title' => __('Expenses by Vendor (Summary)', true), 'url' => "/owner/reports/report/expenses?group_by=Vendor&summary=1&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'daily_expenses_detailed' => array('title' => __('Daily Expenses (Detailed)', true), 'url' => "/owner/reports/report/expenses?group_by=Daily&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'weekly_expenses_detailed' => array('title' => __('Weekly Expenses (Detailed)', true), 'url' => "/owner/reports/report/expenses?group_by=Weekly&summary=0&from_date={$quarter_ago}&to_date={$today}&show_report=1"),
                'monthly_expenses_detailed' => array('title' => __('Monthly Expenses (Detailed)', true), 'url' => "/owner/reports/report/expenses?group_by=Monthly&summary=0&from_date={$year_ago}&to_date={$today}&show_report=1"),
                'yearly_expenses_detailed' => array('title' => __('Yearly Expenses (Detailed)', true), 'url' => "/owner/reports/report/expenses?group_by=Yearly&summary=0&from_date=&to_date=&show_report=1"),
                'staff_expenses_detailed' => array('plugin' => StaffPlugin, 'title' => __('Expenses by Staff Member(Detailed) ', true), 'url' => "/owner/reports/report/expenses?group_by=Staff&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'client_expenses_detailed' => array('title' => __('Expenses by Client (Detailed)', true), 'url' => "/owner/reports/report/expenses?group_by=Client&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'category_expenses_detailed' => array('title' => __('Expenses by Category (Detailed)', true), 'url' => "/owner/reports/report/expenses?group_by=Category&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1"),
                'vendor_expenses_detailed' => array('title' => __('Expenses by Vendor (Detailed)', true), 'url' => "/owner/reports/report/expenses?group_by=Vendor&summary=0&from_date={$month_ago}&to_date={$today}&show_report=1")]);
        }
        $menu['finance']['subs'] = $finance_subs_array  ;
        App::import('Vendor', 'settings');
        $expenses_hide_client=settings::getValue ( 0  ,"expenses_hide_client" );
        if($expenses_hide_client)
        {
            unset(
                $finance_subs_array['client_expenses_summary'],
                $finance_subs_array['client_expenses_detailed'],
                $finance_subs_array['client_incomes_summary'],
                $finance_subs_array['client_incomes_detailed']
            );
        }
        if(settings::getValue(InventoryPlugin, 'enable_requisitions')){
            $menu['inventory']['subs']['inventory_requistion'] = ['title' => __('Inventory Consumption for Invoices and Returns', true), 'url' => '/owner/reports/report/inventory_requistion'];
        }
        if(settings::getValue(InventoryPlugin, 'enable_requisitions_po')){
            $menu['inventory']['subs']['purchase_requistion'] = ['title' => __('Inventory Consumption for Purchase Invoices and Returns', true), 'url' => '/owner/reports/report/purchase_requistion'];
        }
        $new_menu = array();
        foreach ($menu as $key => $options) {
            $add_this = false;
            //warning suppress
            if(array_key_exists('permissions_contain',$options))
            foreach ($options['permissions_contain'] as $permission)
                if (check_permission($permission)) {
                    $add_this = true;
                    break;
                }
            if(!isset($options['permissions_contain']) || empty($options['permissions_contain'])) $add_this = true;
            if(isset($options['permissions_must']))
            {
                foreach ($options['permissions_must'] as $permission)
                    if (!check_permission($permission)) {
                        $add_this = false;
                        break;
                    }

            }
            if (isset($options['plugin']) && !ifPluginActive($options['plugin']))
                $add_this = false;

            if ($add_this) {
                foreach ($options['subs'] as $report_key => $report_options) {
                    if (!empty($report_options['plugin']) && !ifPluginActive($report_options['plugin']))
                        unset($options['subs'][$report_key]);

                    if(isset($report_options['permissions_must']) && !empty($report_options['permissions_must']))
                    {
                        foreach ($report_options['permissions_must'] as $permission)
                            if (!check_permission($permission)) {
                                unset($options['subs'][$report_key]);
                                break;
                            }
                    }
                }
                $new_menu[$key] = $options;
            }
        }

        if ($cat)
            $new_menu = $new_menu[$cat];


        return $new_menu;
    }

    public function owner_test()
    {
        $this->loadModel('Journal');
        $this->loadModel('Invoice');
        $journals = $this->Journal->find('all',array('conditions' => array( 'OR' => array( 'Journal.total_credit > 10000000000', 'Journal.total_debit > 10000000000') )));
        foreach($journals as $k => $journal)
        {
            $this->Invoice->delete($journal['Journal']['entity_id']);
        }
        die(Debug($journals));
    }

    function owner_load($id = false) {
        if (!$id && empty($_GET['load'])) {
            $this->redirect(array('action' => 'payments'));
        }

        $report_id = $id ? $id : intval($_GET['load']);
        $report = $this->SavedReport->find(array('SavedReport.id' => $report_id, 'SavedReport.site_id' => getAuthOwner('id')));

        if (!$report) {
            $this->flashMessage(__('Report not found', true));
            $this->redirect(array('action' => 'index'));
        }

        $reportType = SavedReport::getReportTypes($report['SavedReport']['type']);
        $params = json_decode($report['SavedReport']['data'], true);
        $dateFormats = getDateFormats('std');
        $dateFormat = $dateFormats[getCurrentSite('date_format')];

        if (!empty($params['date_from'])) {
            $params['date_from'] = format_date(($params['date_from']));
        }

        if (!empty($params['date_to'])) {
            $params['date_to'] = format_date(($params['date_to']));
        }
        if ($this->RequestHandler->isAjax()) {
            require_once APP . 'vendors' . DS . 'Report.php';
            $reportObj = new Report($reportType['type'], $params['report_type'], $params);
            die(json_encode(array('data' => $reportObj->jsonAdapter(), 'report' => $report['SavedReport']['title'])));
        } else {
            $params['report_id'] = $report_id;
            $url = array('action' => $reportType['action']);
            $url['?'] = $params;
            $this->redirect($url);
        }
    }

    function owner_cost_centers()
    {
        if (!check_permission([VIEW_ALL_JOURNALS, VIEW_COST_CENTERS])) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        set_time_limit(3600);
        ini_set('memory_limit','10G');
        $this->_ownerSave();
        $owner = getAuthOwner();
        $periods = array('monthly' => __('Monthly', true), 'quarterly' => __('Quarterly', true), 'yearly' => __('Yearly', true));
//        $incometypes = array('payment' => __('Invoice\'s Payment Date', true),'paid' => __('Invoice Issue Date (Paid Invoices)', true), 'amount' => __('Invoice Issue Date (All Invoices)', true));
//        $this->set(compact('owner', 'currencies', 'periods',  'incometypes'));
        $this->set(compact('owner', 'periods'));
        $this->loadModel('CostCenter');
        $primary_cost_centers_list = $this->CostCenter->get_primary_cost_centers(true);
        $secondary_cost_centers_list = $this->CostCenter->get_secondary_cost_centers(true);
        $this->set('cost_centers_list', $primary_cost_centers_list + $secondary_cost_centers_list);
        $this->loadModel('JournalAccount');
        $journal_accounts = $this->JournalAccount->find('list');

        $this->set('journal_accounts', $journal_accounts);
        $this->set('primary_cost_centers_list',$primary_cost_centers_list);
        $this->set('secondary_cost_centers_list',$secondary_cost_centers_list);
        $this->set('default_currency', $this->CostCenter->get_default_currency());
        if (!empty($_GET['group_by'])) {

            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('CostCenter');


            $this->set($report->getDataArray(), false);
            $this->set('jsonParams', $report->jsonAdapter(), false);


            if (!empty($_GET['quick'])){
                $this->layout = '';
            }
            $this->set('title_for_layout',  __($this->viewVars['report_title'] ,true));
        }else
            $this->set('title_for_layout',  __('Cost Centers Report',true));

    }





    function owner_aged_debtors()
    {
        //Fix memory bug with aged debtors reports
        ini_set('memory_limit','4G');
        set_time_limit(600);
        $this->_ownerSave();
        $owner = getAuthOwner();
//        $periods = array('monthly' => __('Monthly', true), 'quarterly' => __('Quarterly', true), 'yearly' => __('Yearly', true));
        $this->loadModel('Client');
        $conditions = [];
        $viewAll = true;
        if (!check_permission(VIEW_ALL_CLIENTS_REPORTS) && check_permission(VIEW_HIS_OWN_CLIENTS_REPORTS)){
            $conditions[] = '(Client.staff_id = ' . $owner['staff_id'] . '  OR  Client.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . 1 . ' AND item_staffs.staff_id=' . $owner['staff_id'] . ' )) ';
            $viewAll = false;
        }
        if (isset($_GET['staff_id']) && !empty($_GET['staff_id'])) {
            $staffId = $_GET['staff_id'];
            $conditions[] = '(Client.staff_id = ' . $staffId . '  OR  Client.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . 1 . ' AND item_staffs.staff_id=' . $staffId . ' )) OR Client.id IN(SELECT client_id FROM invoices WHERE invoices.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . 12 . ' AND item_staffs.staff_id=' . $staffId . '))';
            $viewAll = false;
        }
        $clients = $this->Client->find('list',[ 'applyBranchFind' => false,'fields' => ['id', 'business_name'] , 'conditions' => $conditions ]);
//		die(debug($clients));
        $this->set('clients',$clients);
        $this->set(compact('owner','viewAll'));
        $this->loadModel('Invoice');
        $this->set('default_currency', $this->Invoice->get_default_currency());
//        if (!empty($_GET['group_by'])) {
        require_once APP . 'vendors' . DS . 'Report.php';
        $report = new Report('AgedDebtor');


        $this->set($report->getDataArray(), false);
        $this->set('jsonParams', $report->jsonAdapter(), false);


        if (!empty($_GET['quick'])){
            $this->layout = '';
        }
        $this->set('title_for_layout',  __($this->viewVars['report_title'] ,true));
//        }else
//		$this->set('title_for_layout',  __('Cost Centers Report',true));

        $this->set('client_categories', $categories = $this->Client->getCategoriesListWithIds());

    }

    function owner_tags()
    {


        $this->_ownerSave();
        $owner = getAuthOwner();
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->Staff->applyBranch['onFind'] = false;
            $this->set('staffs', $this->Staff->getList());
        }
        $this->loadModel('Tag');
        $this->loadModel('ItemsTag');
        $tags_list = $this->Tag->find('list');

        $periods = array('monthly' => __('Monthly', true), 'quarterly' => __('Quarterly', true), 'yearly' => __('Yearly', true));
//        $incometypes = array('payment' => __('Invoice\'s Payment Date', true),'paid' => __('Invoice Issue Date (Paid Invoices)', true), 'amount' => __('Invoice Issue Date (All Invoices)', true));
        $this->set('tags_list',$tags_list);
        $this->set('item_types',ItemsTag::tag_type_to_model_name());
        $this->set('item_types_names',ItemsTag::get_item_types());
//        $this->set(compact('owner', 'currencies', 'periods',  'incometypes'));
        $this->set(compact('owner', 'periods'));
        $arr_rates = array();

        if (!empty($_GET['type'])) {
            $type = 'tag';

//			$income_type = $_GET['income_type'];


            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('Tag');


            $this->set($report->getDataArray(), false);
            $this->set('jsonParams', $report->jsonAdapter(), false);




//                $this->set("reportData", $arr);
//                $this->set('jsonParams', $report->jsonAdapter($arr), false);
//                $this->set('type', $type);
//                $this->set('typeStr', $type);
//                $this->set('reportType', 'monthly');



            if (!empty($_GET['quick'])){
                $this->layout = '';
            }
            $this->set('title_for_layout',  __($this->viewVars['report_title'] ,true));
        }else
            $this->set('title_for_layout',  __('Tags Report',true));

    }

    function owner_work_orders()
    {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        //Raise Work order memory limit
        ini_set('memory_limit','4G');
        $this->loadModel('FollowUpReminder');
        $this->_ownerSave();
        $owner = getAuthOwner();
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }
        $this->loadModel('WorkOrder');
//		$this->loadModel('ItemsTag');
//		$tags_list = $this->Tag->find('list');

        $periods = array('monthly' => __('Monthly', true), 'quarterly' => __('Quarterly', true), 'yearly' => __('Yearly', true));
//        $incometypes = array('payment' => __('Invoice\'s Payment Date', true),'paid' => __('Invoice Issue Date (Paid Invoices)', true), 'amount' => __('Invoice Issue Date (All Invoices)', true));
//        $this->set('tags_list',$tags_list);
//        $this->set('item_types',ItemsTag::tag_type_to_model_name());
//        $this->set('item_types_names',ItemsTag::get_item_types());
//        $this->set(compact('owner', 'currencies', 'periods',  'incometypes'));
        $this->set(compact('owner', 'periods'));
        $this->loadModel('Client');
        $this->set('clients', $this->Client->find('list',['fields' => ['business_name']]));
        $this->loadModel('FollowUpStatus');
        $statuses = $this->WorkOrder->get_status_list();

        $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(FollowUpReminder::WORK_ORDER_TYPE);
        $FollowUpStatusList = $this->FollowUpStatus->find('list');
        $this->set('Fstatus_list',$FollowUpStatusList);
        $this->set('status_list',$statuses);
        $f_groups = [];
        foreach ( $FollowUpStatus as $f ) {

            $f_groups[$statuses[$f['status']] ][] = $f;
        }
//			die(Debug($f_groups));
        $this->set('work_orders_status', $f_groups);

        if (!empty($_GET['type'])) {
            $type = 'WorkOrder';

//			$income_type = $_GET['income_type'];


            require_once APP . 'vendors' . DS . 'Report.php';
//			die(Debug($_GET));
            $report = new Report($type);

            $this->set($report->getDataArray(), false);
            $this->set('jsonParams', $report->jsonAdapter(), false);
            if (!empty($_GET['quick'])){
                $this->layout = '';
            }
            $this->set('title_for_layout',  __($this->viewVars['report_title'] ,true));
        }else{
            $this->loadModel('CustomForm');
            $reread_form = $this->CustomForm->findByTableName("work_orders");
            if (!empty($reread_form)) {
                $this->loadModel('CustomFormField');

                $filtered_fields = array();
                $form_fields = $this->WorkOrder->get_custom_fields_filters($reread_form['CustomForm']['id']);
                foreach ($form_fields as $key => $value) {
                    if ($value['myval']["CustomFormField"]["is_filtered"] == 1) {
                        $filtered_fields[] = $value['myval'];
                    }
                }
//			dd($filtered_fields);
                $this->set('filtered_fields', $filtered_fields);

            }
            $this->set('title_for_layout',  __('Work Orders Report',true));
        }
    }
    private function redirect_to_csv()
    {
        $actual_link = "https://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
        if (!str_contains($actual_link, '.csv')) {
            $url_parts = parse_url($actual_link);
            $actual_link = str_replace($url_parts['path'], $url_parts['path'] . ".csv", $actual_link);
            return redirect($actual_link);
        }
    }
    private function check_if_csv()
    {
        $actual_link = "https://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";

        if (str_contains($actual_link, '.csv')) {
            return true;
        }
        return false;
    }




    function owner_stock_transactions_profit(){

//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        //Raise Stock transactions profit memory limit
        ini_set('memory_limit','4G');
        set_time_limit(3600);
        $this->loadModel('Journal');
        $this->reportType = 20;
        $this->_ownerSave();
        $owner = getAuthOwner();
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }
        $this->loadModel('Product');
        $this->set('products', $this->Product->getProductList());
        $this->_settings();
        $currency = $this->Journal->get_default_currency();
        $periods = array('monthly' => __('Monthly', true), 'quarterly' => __('Quarterly', true), 'yearly' => __('Yearly', true));

        $this->set(compact('owner', 'currencies', 'periods'));
        $arr_rates = array();
        if (!empty($_GET['type'])) {
            $type = $_GET['type'];

            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('StockTransaction');
            $this->set('report',$report);
            if (!isset($_GET['currency_code']) || $_GET['currency_code']== -1) {

                $this->set($report->getDataArray(), false);
                $this->set('jsonParams', $report->jsonAdapter(), false);

                foreach($this->viewVars['reportData'] as $group_currency => &$groups )
                {
                    foreach($groups as $group_title => &$group_data)
                    {
                        foreach($group_data as $k => &$transaction)
                        {
                            $transaction['quantity'] = $transaction['quantity'] * -1;
                            if($transaction['currency_code']  != $currency && !empty($transaction['currency_code']))
                            {
                                if(!isset($arr_rates[$transaction['currency_code']]))
                                    $arr_rates[$transaction['currency_code']] = CurrencyConverter::index($transaction['currency_code'], $currency, date('Y-m-d' , strtotime($transaction['received_date']??date('Y-m-d'))));

                                $transaction['price'] = $transaction['price']  * $arr_rates[$transaction['currency_code']] ;
                                if (empty($transaction['invoice_item'])) {
                                    $transaction['discount'] = $transaction['discount']  * $arr_rates[$transaction['currency_code']] ;
                                }
                                else{
                                    $transaction['discount'] = $transaction['invoice_item']['calculated_discount'] * $arr_rates[$transaction['currency_code']];
                                }

                            } else {
                                if (!empty($transaction['invoice_item'])) {
                                    $transaction['discount'] = $transaction['invoice_item']['calculated_discount'];
                                }
                            }
                        }
                    }

                }


            } else {


                // get data of all currencies and convert them and sum them
                $arr = array();
                foreach ($this->viewVars['currencies'] as $key1 => $value1) {
                    $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"], date('Y-m-d'));
                }

                foreach ($this->viewVars['currencies'] as $key1 => $value1) {


                    $get_data = $report->getDataArray($key1);

                    foreach ($get_data['reportData'] as $key2 => $value2) {

                        if ($key2 != "col_labels") {
                            //debug($key2);
                            //$salaray_arr = array();
                            foreach ($value2 as $key3 => $value3) {
                                //debug($value3);
                                if ($key2 == "income" || $key2 == "expenses" || $key2 == "refunds" ) {
                                    foreach ($value3 as $key4 => $value4) {
                                        //debug($key4);
                                        //$rate_val = CurrencyConverter::index($key1,$owner["currency_code"]);
//                                        $rate_val = 1;
                                        if (isset($arr[$key2][$key3][$key4])) {
                                            $arr[$key2][$key3][$key4] += ($value4 * $arr_rates[$key1]);
                                        } else {
                                            $arr[$key2][$key3][$key4] = ($value4 * $arr_rates[$key1]);
                                        }
                                    }
                                } else {

                                    //$rate_val = CurrencyConverter::index($key1,$owner["currency_code"]);
//                                    $rate_val = 1;
                                    if (isset($arr[$key2][$key3])) {
                                        $arr[$key2][$key3] += ($value3 * $arr_rates[$key1]);
                                    } else {
                                        $arr[$key2][$key3] = ($value3 * $arr_rates[$key1]);
                                    }
                                }
                            }
                            //$arr[$key2] = $salaray_arr;
                        } else {

                            //col_labels
                            $arr[$key2] = $value2;
                        }
                    }
                    //  print_pre($arr);

//                    break;
                }

                //debug($arr_rates);
                $this->set("currency_rates", $arr_rates);
                $this->set("reportData", $arr);
                $this->set('jsonParams', $report->jsonAdapter($arr), false);
                $this->set("currency", $currency);
                $this->set('type', $type);
                $this->set('income_type', $income_type);
                $this->set('typeStr', $type);
                $this->set('reportType', 'monthly');

            }


            if (!empty($_GET['quick'])){
                $this->layout = '';
            }
            $this->set('title_for_layout',  __($this->viewVars['report_title'] ,true));
        }else
            $this->set('title_for_layout',  __('Profits Report',true));

        $this->set('client_categories', $categories = $this->Client->getCategoriesListWithIds());

    }


    function owner_profit() {

//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }

        if (ifPluginActive(WorkOrderPlugin)){
            $this->loadModel('WorkOrder');
            $this->set ( 'work_orders', $this->WorkOrder->get_work_orders ( ['status' => [WorkOrder::STATUS_OPEN ,WorkOrder::STATUS_CLOSED ] ] )) ;
        }
        $this->reportType = 20;
        $owner = getAuthOwner();

        $this->_settings();
        $periods = array('monthly' => __('Monthly', true), 'quarterly' => __('Quarterly', true), 'yearly' => __('Yearly', true));
        $incometypes = array('payment' => __('Invoice\'s Payment Date', true),'paid' => __('Invoice Issue Date (Paid Invoices)', true), 'amount' => __('Invoice Issue Date (All Invoices)', true));

        $this->set(compact('owner', 'periods',  'incometypes'));

        if (!empty($_GET['type'])) {
            $type = $_GET['type'];

            $income_type = $_GET['income_type'];


            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('Profit');

            if ($_GET['currency'] != -1) {
                $this->set($report->getDataArray(), false);
                $this->set('jsonParams', $report->jsonAdapter(), false);
            } else {


                // get data of all currencies and convert them and sum them
                $arr = array(); 

                foreach ($this->viewVars['currencies'] as $key1 => $value1) {

//                    if($key1 == "EGP")
//                    {
//                        break;
//                    }
                    $get_data = $report->getDataArray($key1);

                    foreach ($get_data['reportData'] as $key2 => $value2) {

                        if ($key2 != "col_labels") {
                            //debug($key2);
                            //$salaray_arr = array();
                            foreach ($value2 as $key3 => $value3) {
                                //debug($value3);
                                if ($key2 == "income" || $key2 == "expenses" || $key2 == "refunds" ) {
                                    foreach ($value3 as $key4 => $value4) {
                                        //debug($key4);
                                        //$rate_val = CurrencyConverter::index($key1,$owner["currency_code"]);
//                                        $rate_val = 1;
                                        if (isset($arr[$key2][$key3][$key4])) {
                                            $arr[$key2][$key3][$key4] += ($value4);
                                        } else {
                                            $arr[$key2][$key3][$key4] = ($value4);
                                        }
                                    }
                                } else {

                                    //$rate_val = CurrencyConverter::index($key1,$owner["currency_code"]);
//                                    $rate_val = 1;
                                    if (isset($arr[$key2][$key3])) {
                                        $arr[$key2][$key3] += ($value3);
                                    } else {
                                        $arr[$key2][$key3] = ($value3);
                                    }
                                }
                            }
                            //$arr[$key2] = $salaray_arr;
                        } else {

                            //col_labels
                            $arr[$key2] = $value2;
                        }
                    }
                    //  print_pre($arr);

//                    break;
                }

                //debug($arr_rates);
                $this->set("currency_rates", $arr_rates);
                $this->set("reportData", $arr);
                $this->set('jsonParams', $report->jsonAdapter($arr), false);
                $this->set("currency", $owner["currency_code"]);
                $this->set('type', $type);
                $this->set('income_type', $income_type);
                $this->set('typeStr', $type);
                $this->set('reportType', 'monthly');
                $this->set('filterParams', $this->params['url']);
            }
            $this->set('title_for_layout', __("Profit & Loss Report (Cash)", true));


            if (!empty($_GET['quick'])){
                $this->layout = '';
            }
            if(isset($_GET['quick']) && $_GET['quick'])
            {
                App::import('Vendor', 'settings');
                $settings_data[ReportsPlugin]['profit_report'] = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
                settings::setData($settings_data);
            }
        }



//        debug($this->viewVars['reportData']);
    }

    function owner_taxes() {

//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
    	set_time_limit(90);
		ini_set('memory_limit', '4G');
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }
        $this->reportType = 1;
        $this->_ownerSave();
        $owner = getAuthOwner();

        if (empty($this->params['url']['date_from']))
            $this->params['url']['date_from'] = date('Y-m-d', strtotime('-1 Year'));

        if (empty($this->params['url']['date_to']))
            $this->params['url']['date_to'] = date('Y-m-d');

        if (empty($this->params['url']['details']))
            $this->params['url']['details'] = 0;

        if (!empty($this->params['url']['invoice_type'])) {

            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('Tax');
            $report=$report->getDataArray($this->params['url']);
            $this->set($report, false);
            $this->set('invoice_type', $this->params['url']['invoice_type']);
            $this->loadModel('Client');
            $this->loadModel('Supplier');
            $this->Client->applyBranch['onFind']=false;
            $clients_a = $this->Client->find('all' , ['recursive' => -1  ]);
            $clients = [] ;
            foreach ( $clients_a as $k =>$c )
            {
                $clients[$c['Client']['id']] = $c;
            }
            $this->Supplier->applyBranch['onFind']=false;
            $suppliers_a = $this->Supplier->find('all' , ['recursive' => -1  ]);
            $suppliers = [] ;
            foreach ( $suppliers_a as $k =>$c )
            {
                $suppliers[$c['Supplier']['id']] = $c;
            }
            $this->set ( 'suppliers' , $suppliers ) ;
            $this->set ( 'clients' , $clients ) ;
            //$this->set('jsonParams', $report->jsonAdapter(), false);
        } else {
            $this->set('date_from', $this->params['url']['date_from']);
            $this->set('date_to', $this->params['url']['date_to']);
        }
        $this->set('details', $this->params['url']['details']);
        $this->set('selTax', $this->params['url']['tax_id']);

        if (empty($this->params['url']['details']))
            $this->set('title_for_layout',  __('Tax Summary', true) . ' - ' . __('Reports', true));
        else
            $this->set('title_for_layout',  __('Tax Details', true) . ' - ' . __('Reports', true));

        $this->loadModel('Tax');
        $taxes_list = $this->Tax->getFullTaxList ( ) ;// array_merge($this->Invoice->getTaxList() , $this->Tax->get_deleted_taxes() ) ;
        $this->_settings();
        $this->set(compact('taxes_list', 'owner'), false);

        //debug($this->viewVars["report_data"]);
    }

	function owner_new_taxes() {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        ini_set('memory_limit', '3G');
		if (ifPluginActive(StaffPlugin)) {
			$this->loadModel('Staff');
			$this->set('staffs', $this->Staff->getList());
		}
		$this->reportType = 1;
		$this->_ownerSave();
		$owner = getAuthOwner();

		if (empty($this->params['url']['date_from']))
			$this->params['url']['date_from'] = date('Y-m-d', strtotime('-1 Year'));

		if (empty($this->params['url']['date_to']))
			$this->params['url']['date_to'] = date('Y-m-d');

		if (empty($this->params['url']['details']))
			$this->params['url']['details'] = 0;

		if (!empty($this->params['url']['invoice_type'])) {

			require_once APP . 'vendors' . DS . 'Report.php';
			$report = new Report('Tax');

            $report=$report->getDataArray($this->params['url']);
            $this->set($report, false);
            $this->set('invoice_type', $this->params['url']['invoice_type']);
			$this->loadModel('Client');
			$this->loadModel('Supplier');
			$this->Client->applyBranch['onFind']=false;
			$clients_a = $this->Client->find('all' , ['recursive' => -1  ]);
			$clients = [] ;
			foreach ( $clients_a as $k =>$c )
			{
				$clients[$c['Client']['id']] = $c;
			}
			$this->Supplier->applyBranch['onFind']=false;
			$suppliers_a = $this->Supplier->find('all' , ['recursive' => -1  ]);
			$suppliers = [] ;
			foreach ( $suppliers_a as $k =>$c )
			{
				$suppliers[$c['Supplier']['id']] = $c;
			}
			$this->set ( 'suppliers' , $suppliers ) ;
			$this->set ( 'clients' , $clients ) ;
			//$this->set('jsonParams', $report->jsonAdapter(), false);
		} else {
			$this->set('date_from', $this->params['url']['date_from']);
			$this->set('date_to', $this->params['url']['date_to']);
		}
		$this->set('details', $this->params['url']['details']);
		$this->set('selTax', $this->params['url']['tax_id']);

		if (empty($this->params['url']['details']))
			$this->set('title_for_layout',  __('Tax Declaration', true));
		else
			$this->set('title_for_layout',  __('Tax Details', true) . ' - ' . __('Reports', true));

		$this->loadModel('Tax');
		$taxes_list = $this->Tax->getFullTaxList ( ) ;// array_merge($this->Invoice->getTaxList() , $this->Tax->get_deleted_taxes() ) ;
		$this->_settings();
		$this->set(compact('taxes_list', 'owner'), false);
		$this->set('tax_label', Localize::get_field_label_by_country_code('bn1', $owner['country_code']));
		//debug($this->viewVars["report_data"]);
	}


    function owner_products() {
        if(!check_permission([PermissionUtil::View_his_own_Products, PermissionUtil::View_All_Products])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        set_time_limit(99999);
        ini_set("memory_limit", "10G");

        if(ifPluginActive(InventoryPlugin)){
            $this->loadModel('ItemPermission');
            $this->set('stores', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW) );
        }
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('Product');
        $this->loadModel('Client');
        $this->set('products', $this->Product->getProductList());
        $this->loadModel('FollowUpStatus');
        $this->loadModel('Post');
        $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::INVOICE_TYPE);

        $this->set('FollowUpStatuses', $FollowUpStatus);

        $clients_data = $this->Client->find ( 'all' , ['recursive' => 0]);
        $clients = []  ;
        foreach ($clients_data as $cc ){
            $clients[$cc['Client']['id']] = $cc['Client']['business_name'].' (#'.$cc['Client']['client_number'].')';
        }
        $this->set('clients',$clients );


        $this->reportType = 1;
        $this->_ownerSave();


        if (empty($this->params['url']['date_from']))
            $this->params['url']['date_from'] = date('Y-m-d', strtotime('-1 Month'));

        if (empty($this->params['url']['date_to']))
            $this->params['url']['date_to'] = date('Y-m-d');


        $this->_settings();


        $this->_ownerSave();
        $this->set(array('type' => '', 'reportType' => ''));
        if (!empty($_GET['report_type'])) {
            $type = 'client';
            if (low($_GET['report_type']) == 'client') {
                $type = 'client';
            } else {
                $type = low($_GET['type']);
                if (!in_array($type, array('client', 'daily', 'weekly', 'monthly', 'yearly', 'lastmonth', 'lastyear', 'staff', 'product'))) {
                    $type = 'monthly';
                }
            }
            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('Product', 'product');
            $result=$report->getDataArray();


            $this->set($result, false);
            $this->set('jsonParams', $report->jsonAdapter(), false);

            //debug($this->viewVars["reportData"]);

            $this->_settings();

            if (isset($_GET["currency"]) && $_GET["currency"] == -1) {

                $owner = getAuthOwner();

                // get data of all currencies and convert them and sum them
                $arr = array();
                $arr_rates = array();
                $currencies = $this->viewVars["currencies"];

                foreach ($currencies as $key1 => $value1) {

                    $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"], date('Y-m-d'));

                }


                //debug($this->viewVars["reportData"]);
                $counter = 0;

//                debug($this->viewVars["reportData"]);
                // ex($key => AED, EGP - cash)

                foreach ($this->viewVars["reportData"] as $key => $value) {

                    if ($key == "Cash") {

                        // not converted !! (Cash = Total quantity)
                        if (isset($arr[$key])) {
                            $arr[$key] += $value;
                        } else {
                            $arr[$key] = $value;
                        }
                    } else {

                        // ex($key2 => [], date, integer)
                        foreach ($value as $key2 => $value2) {
                            //debug($key2);
                            // ex($key3 => 0-1-2-....)
                            foreach ($value2 as $key3 => $value3) {
                                $rate = CurrencyConverter::index($value3["currency_code"], $owner["currency_code"], $value3["date"]);
                                if (isset($arr[$owner["currency_code"]][$key2])) {

                                    $get_new_index = count($arr[$owner["currency_code"]][$key2]);
                                    $value3["unit_price"] = (($value3["unit_price"] * 1)*$rate);
                                    $value3["calculated_discount"] = (($value3["calculated_discount"] * 1)*$rate);
                                    $value3["currency_code"] = $owner["currency_code"];
                                    $arr[$owner["currency_code"]][$key2][$get_new_index] = $value3;
                                } else {
                                    $value3["unit_price"] = (($value3["unit_price"] * 1)*$rate);
                                    $value3["calculated_discount"] = (($value3["calculated_discount"] * 1)*$rate);
                                    $value3["currency_code"] = $owner["currency_code"];
                                    $arr[$owner["currency_code"]][$key2][0] = $value3;
                                    //$counter = $counter+1;
                                }
                            }
                            if(count($value2)==0 && empty($arr[$owner["currency_code"]][$key2][0])){

                                $arr[$owner["currency_code"]][$key2] = [];


                            }
                        }
                    }// end else
                } // endforeach
                /*foreach ( $arr as $k => $v ) {
                    uksort ($arr[$k], function ($a , $b){
                        $ac = strtotime ( $a  ) ;
                        $bc = strtotime ($b );
                         if ($ac == $bc) {
                                return 0;
                            }
                            return ($ac < $bc  ) ? -1 : 1;
                    } );
                }*/

                $this->set("reportData", $arr);
                $this->set("reportCurrencies", array($owner["currency_code"]));
                $this->set("currency_rates", $arr_rates);
            }
        }
        $this->set('title_for_layout',  isset($this->viewVars["report_title"])? __($this->viewVars["report_title"], true) : __("Products Report", true));
        $this->loadModel('Product');
        $this->set('categories', $this->Product->getCategoriesList(false));
        $this->set('client_categories', $categories = $this->Client->getCategoriesListWithIds());
//        debug($this->Product->lastQuery(true));
    }


    function owner_bnr() {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $this->set('report_type','client');
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true);
        $this->set('paymentMethods',$paymentMethods);
        $this->reportType = 2;
        $owner = getAuthOwner();

        /* if ($this->RequestHandler->isAjax() || $this->RequestHandler->isFlash()) {
          $this->_owner_payments_ajax();
          } */

        $this->_ownerSave();
        $this->set(array('type' => '', 'reportType' => ''));

        if (low($_GET['report_type']) == 'item') {
            $type = 'item';
        } else {
            $type = low($_GET['type']);
            if (!in_array($type, array('daily', 'weekly', 'monthly', 'yearly', 'lastmonth', 'lastyear'))) {
                $type = 'monthly';
            }
        }

        require_once APP . 'vendors' . DS . 'Report.php';
        $report = new Report('Nbr', 'item');

        $this->set($report->getDataArray(), false);
        $this->set('jsonParams', $report->jsonAdapter(), false);

        //debug($this->viewVars["reportData"]);

        $this->_settings();
//isset($_GET["currency"]) && $_GET["currency"] == -1
        if (false) {

            // get data of all currencies and convert them and sum them
            $arr = array();
            $arr_rates = array();
            $currencies = $this->viewVars["currencies"];
            //   debug($owner["currency_code"]);
            foreach ($currencies as $key1 => $value1) {
                $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"], date('Y-m-d'));
            }
            // debug($arr_rates);

            // debug($this->viewVars["reportData"]);
            $counter = 0;
            // ex($key1 => AUD)
            //elwan
            //print_pre($this->viewVars["reportData"]);
            //die();
            foreach ($this->viewVars["reportData"] as $key1 => $value1) {

                // ex($key2 => dateformat - company name)
                foreach ($value1 as $key2 => $value2) {

//                       debug($value1);
                    if ($key2 !== "methods") {
                        //  debug($key2);
                        // ex($key3 => 0, 1, 2)
                        foreach ($value2 as $key3 => $value3) {

                            //debug($key3);

                            $value3["summary_subtotal"] = ($value3["summary_subtotal"] * $arr_rates[$key1]);
                            $value3["summary_paid"] = ($value3["summary_paid"] * $arr_rates[$key1]);
                            $value3["summary_refund"] = ($value3["summary_refund"] * $arr_rates[$key1]);
                            $value3["summary_po"] = ($value3["summary_po"] * $arr_rates[$key1]);

                            $value3["currency_code"] = $owner["currency_code"];

                            $arr[$owner["currency_code"]][$key2][0] = $value3;
                            $counter = $counter + 1;
                        }
                        if(count($value2)==0&&!isset( $arr[$owner["currency_code"]][$key2][0])){
                            $newarr["amount"] = 0;
                            $newarr["currency_code"] = $owner["currency_code"];
                            $arr[$owner["currency_code"]][$key2][0] = $newarr;


                        }

                    } else {
//
//                            foreach ($value2 as $key3 => $value3) {
//
//                                if (isset($arr[$owner["currency_code"]][$key2][$key3])) {
//                                    $arr[$owner["currency_code"]][$key2][$key3] += ($value3 * $arr_rates[$key1]);
//                                } else {
//                                    $arr[$owner["currency_code"]][$key2][$key3] = ($value3 * $arr_rates[$key1]);
//                                }
//                            }
                    }
                }

            }
            foreach ( $arr as $k => $v ) {
                uksort ($arr[$k], function ($a , $b){
                    $ac = strtotime ( $a  ) ;
                    $bc = strtotime ($b );
                    if ($ac == $bc) {
                        return 0;
                    }
                    return ($ac < $bc  ) ? -1 : 1;
                } );
            }

            //debug( $arr ) ;
            //debug($this->viewVars["jsonParams"]);

            die();
            $this->set("reportData", $arr);
            $this->set("reportCurrencies", array($owner["currency_code"]));
            $this->set("currency_rates", $arr_rates);


        }


        //$this->_settings();

        $this->set('clients', $this->Invoice->Client->getClientsList($owner['id'], array('Client.id in (SELECT DISTINCT client_id FROM invoices WHERE invoices.site_id = ' . $owner['id'] . ')')));

        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, true);
        $this->set('payment_methods', $paymentMethods);


        $this->set('title_for_layout',  __('Nbr', true) . ' - ' . __('Reports', true));


    }


    function owner_payments() {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        //Raise Work order memory limit
        set_time_limit(3600);
        ini_set('memory_limit','4G');
        $this->reportType = 2;
        $owner = getAuthOwner();

        /* if ($this->RequestHandler->isAjax() || $this->RequestHandler->isFlash()) {
          $this->_owner_payments_ajax();
          } */

        $this->_ownerSave();
        $this->set(array('type' => '', 'reportType' => ''));
        if (!empty($_GET['report_type'])) {
            $type = 'client';
            if (low($_GET['report_type']) == 'client') {
                $type = 'client';
            } else {
                $type = low($_GET['type']);
                if (!in_array($type, array('client', 'daily', 'weekly', 'monthly', 'yearly', 'lastmonth', 'lastyear', 'staff', 'payment', 'payment_method'))) {
                    $type = 'monthly';
                }
            }

            require_once APP . 'vendors' . DS . 'Report.php';


            $report = new Report('Payment', $type);

            $this->set($report->getDataArray(), false);

            $this->set('jsonParams', $report->jsonAdapter(), false);

            //debug($this->viewVars["reportData"]);

            $this->_settings();

            if (isset($_GET["currency"]) && $_GET["currency"] == -1) {

                // get data of all currencies and convert them and sum them
                $arr = array();
                $arr_rates = array();
                $currencies = $this->viewVars["currencies"];
                //   debug($owner["currency_code"]);
                foreach ($currencies as $key1 => $value1) {
                    $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"], date('Y-m-d'));
                }
                // debug($arr_rates);

                // debug($this->viewVars["reportData"]);
                $counter = 0;
                // ex($key1 => AUD)
                foreach ($this->viewVars["reportData"] as $key1 => $value1) {

                    // ex($key2 => dateformat - company name)
                    foreach ($value1 as $key2 => $value2) {
//                       debug($value1);
                        if ($key2 !== "methods") {
                            //  debug($key2);
                            // ex($key3 => 0, 1, 2)
                            foreach ($value2 as $key3 => $value3) {
                                //debug($key3);
                                //debug($value3);
                                $value3["amount"] = ($value3["amount"] * ($value3['currency_rate'] ?? $arr_rates[$key1]));
                                $value3["currency_code"] = $owner["currency_code"];

                                $arr[$owner["currency_code"]][$key2][$counter] = $value3;
                                $counter = $counter + 1;
                            }
                            if(is_countable($value2) && count($value2)==0&&!isset( $arr[$owner["currency_code"]][$key2][0])){
                                $newarr["amount"] = 0;
                                $newarr["currency_code"] = $owner["currency_code"];
                                $arr[$owner["currency_code"]][$key2][0] = $newarr;


                            }

                        } else {
//
//                            foreach ($value2 as $key3 => $value3) {
//
//                                if (isset($arr[$owner["currency_code"]][$key2][$key3])) {
//                                    $arr[$owner["currency_code"]][$key2][$key3] += ($value3 * $arr_rates[$key1]);
//                                } else {
//                                    $arr[$owner["currency_code"]][$key2][$key3] = ($value3 * $arr_rates[$key1]);
//                                }
//                            }
                        }
                    }

                }
                foreach ( $arr as $k => $v ) {
                    uksort ($arr[$k], function ($a , $b){
                        $ac = strtotime ( $a  ) ;
                        $bc = strtotime ($b );
                        if ($ac == $bc) {
                            return 0;
                        }
                        return ($ac < $bc  ) ? -1 : 1;
                    } );
                }
                debug ( $arr );
                //debug( $arr ) ;
                //debug($this->viewVars["jsonParams"]);
                $this->set("reportData", $arr);
                $this->set("reportCurrencies", array($owner["currency_code"]));
                $this->set("currency_rates", $arr_rates);
            }
        }

        //$this->_settings();

        $this->set('clients', $this->Invoice->Client->getClientsList($owner['id'], ['Client.id in (SELECT DISTINCT client_id FROM invoices )']));

        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, true);
        $paymentMethods['client_credit']='Client Credit';

        $this->set('payment_methods', $paymentMethods);


        $this->set('title_for_layout',  isset($this->viewVars["report_title"])? __($this->viewVars["report_title"], true) : (__('Payments', true) . ' - ' . __('Reports', true)));

        if (!empty($_GET['quick'])) {
            if(empty($this->viewVars['reportData'])){
                die();
            }


            $this->layout = '';



            $this->render('owner_payment_quick');
        }
        $this->set('client_categories', $categories = $this->Client->getCategoriesListWithIds());

    }
    function owner_popayments() {

//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $this->reportType = 2;
        $owner = getAuthOwner();

        /* if ($this->RequestHandler->isAjax() || $this->RequestHandler->isFlash()) {
          $this->_owner_payments_ajax();
          } */

        $this->_ownerSave();
        $this->set(array('type' => '', 'reportType' => ''));
        if (!empty($_GET['report_type'])) {
            $type = 'supplier';
            if (low($_GET['report_type']) == 'supplier') {
                $type = 'supplier';
            } else {
                $type = low($_GET['type']);
                if (!in_array($type, array('supplier', 'daily', 'weekly', 'monthly', 'yearly', 'lastmonth', 'lastyear', 'staff', 'payment', 'payment_method'))) {
                    $type = 'monthly';
                }
            }
            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('POPayment', $type);

            $this->set($report->getDataArray(), false);
            $this->set('jsonParams', $report->jsonAdapter(), false);

            //debug($this->viewVars["reportData"]);

            $this->_settings();

            if (isset($_GET["currency"]) && $_GET["currency"] == -1) {

                // get data of all currencies and convert them and sum them
                $arr = array();
                $arr_rates = array();
                $currencies = $this->viewVars["currencies"];
                //   debug($owner["currency_code"]);
                foreach ($currencies as $key1 => $value1) {
                    $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"], date('Y-m-d'));
                }
                // debug($arr_rates);

                // debug($this->viewVars["reportData"]);
                $counter = 0;
                // ex($key1 => AUD)

                foreach ($this->viewVars["reportData"] as $key1 => $value1) {

                    // ex($key2 => dateformat - company name)
                    foreach ($value1 as $key2 => $value2) {
//                       debug($value1);
                        if ($key2 != "methods") {
                            //  debug($key2);
                            // ex($key3 => 0, 1, 2)
                            foreach ($value2 as $key3 => $value3) {
                                //debug($key3);
                                //debug($value3);
                                $value3["amount"] = ($value3["amount"] * $arr_rates[$key1]);
                                $value3["currency_code"] = $owner["currency_code"];

                                $arr[$owner["currency_code"]][$key2][$counter] = $value3;
                                $counter = $counter + 1;
                            }
                            if(count($value2)==0&&!isset( $arr[$owner["currency_code"]][$key2][0])){
                                $newarr["amount"] = 0;
                                $newarr["currency_code"] = $owner["currency_code"];
                                $arr[$owner["currency_code"]][$key2][0] = $newarr;


                            }

                        } else {
//
//                            foreach ($value2 as $key3 => $value3) {
//
//                                if (isset($arr[$owner["currency_code"]][$key2][$key3])) {
//                                    $arr[$owner["currency_code"]][$key2][$key3] += ($value3 * $arr_rates[$key1]);
//                                } else {
//                                    $arr[$owner["currency_code"]][$key2][$key3] = ($value3 * $arr_rates[$key1]);
//                                }
//                            }
                        }
                    }

                }
                foreach ( $arr as $k => $v ) {
                    uksort ($arr[$k], function ($a , $b){
                        $ac = strtotime ( $a  ) ;
                        $bc = strtotime ($b );
                        if ($ac == $bc) {
                            return 0;
                        }
                        return ($ac < $bc  ) ? -1 : 1;
                    } );
                }
                debug ( $arr );
                //debug( $arr ) ;
                //debug($this->viewVars["jsonParams"]);
                $this->set("reportData", $arr);
                $this->set("reportCurrencies", array($owner["currency_code"]));
                $this->set("currency_rates", $arr_rates);
            }
        }

        //$this->_settings();

        $this->set('clients', $this->Invoice->Client->getClientsList($owner['id'], array('Client.id in (SELECT DISTINCT client_id FROM invoices WHERE invoices.site_id = ' . $owner['id'] . ')')));

        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, true);
        $this->set('payment_methods', $paymentMethods);
        $this->loadModel ( 'Supplier');
        $this->set('suppliers', $this->Supplier->getSuppliersList());

        $this->set('title_for_layout',  $this->viewVars['report_title'] ?? __('Payments', true) . ' - ' . __('Reports', true));

        if (!empty($_GET['quick'])) {
            if(empty($this->viewVars['reportData'])){
                die();
            }


            $this->layout = '';



            $this->render('owner_payment_quick');
        }
    }

    function owner_payments_ajax() {
        require_once APP . 'vendors' . DS . 'Report.php';
        $owner = getAuthOwner();
        $dateFormats = getDateFormats('std');
        $format = $dateFormats[$owner['date_format']];

        if ($this->params['url']['default']) {

            $report = new Report('Payment', 'daily', $params = array('date_from' => format_date(date("Y-m-d", strtotime('-1 Month')))));
            die(json_encode($report->jsonAdapter()));
        }
    }

    function owner_revenue_ajax() {
        set_time_limit(3600);
        ini_set('memory_limit','4G');
        require_once APP . 'vendors' . DS . 'Report.php';
        $owner = getAuthOwner();
        $dateFormats = getDateFormats('std');
        $format = $dateFormats[$owner['date_format']];

        if ($this->params['url']['default']) {

            $report = new Report('Revenue', 'period', $params = array('group_by' => 'daily', 'date_from' => format_date(date("Y-m-d", strtotime('-1 Month'))), 'date_to' => format_date(date("Y-m-d"))));
            die(json_encode($report->jsonAdapter()));
        }
    }

    function owner_taxes_ajax() {
        require_once APP . 'vendors' . DS . 'Report.php';
        $owner = getAuthOwner();
        $dateFormats = getDateFormats('std');
        $format = $dateFormats[$owner['date_format']];

        if ($this->params['url']['default']) {
            $report = new Report('Tax', false, array('date_from' => format_date(date("Y-m-d", strtotime('-1 Month'))), 'currency' => $owner['currency_code']));
            die(json_encode($report->jsonAdapter()));
        }
    }


    function owner_journal_transactions() {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        ini_set('memory_limit','10G');
        set_time_limit(600);
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }
        if (!empty($this->params['url']['fy_id']) && is_array($this->params['url']['fy_id']) && count($this->params['url']['fy_id']) > 1) {
            $financialYears = $this->getFinancialYears($this->params['url']['fy_id']);
            $isSerial = $this->isFinancialYearsSequential($financialYears);
            if (!$isSerial) {
                $this->flashMessage(__("The selected fiscal years are not arranged chronologically.", true), 'Errormessage', 'secondaryMessage');
                $this->redirect($this->referer());
            }
        }
        
        if (ifPluginActive(FollowupPlugin)) {
            $this->loadModel('FollowUpStatus');
            $this->loadModel('Post');
            $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::CLIENT_TYPE);
            if (check_permission(Edit_General_Settings)) {
                $FollowUpStatus[] = array('name' => false, 'value' => false, 'data-divider' => "true");
                $FollowUpStatus[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> Edit Statuses List </span>', 'name' => __('Edit Statuses List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
            }

            $this->set('FollowUpStatuses', $FollowUpStatus);
            $client_statuses = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, true);
            $this->set('client_statuses', $client_statuses);

            $client_status_colors = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, true, array(), 'color');
            $this->set('colors_options', $this->FollowUpStatus->colors);
            $this->set('client_status_colors', $client_status_colors);
        }
        $this->set('enableTags', \settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_TAGS_IN_JOURNAL));
        $this->loadModel('Journal');
        //i moved this code in fake controller fix_journal_transaction_report because  it take too much time
        $this->Journal->updateAccountLevelEvenUnreachable();
//        $this->Journal->update_cat_parent_ids();
//        $this->Journal->update_account_parent_ids();
        $this->loadModel('JournalCat');
        $this->loadModel('JournalAccount');
        if (empty($this->params['url']['type']) && $this->params['url']['type'] != 'trial-balance-nets') {
            $accounts_list = $this->JournalAccount->find('list');
            $this->set('accounts_list',$accounts_list);
            $catsList = array_map(function($name){
                $name = __at($name);
                return $name;
            }, $this->JournalCat->find('list'));
            $this->set('cats_list', $catsList);
        }
        $owner = getAuthOwner();
        $this->owner = $owner;
        $reportType = '';
        $default_currency = $this->Journal->get_default_currency();
        $this->set('default_currency',$default_currency);
        $formats = getDateFormats('std');
        $dateFormat = $formats[$owner['date_format']];
        $this->loadModel('FinancialYear');
        $financialYears = $this->FinancialYear->getFinancialYears(false);
        $this->set('financial_years',$financialYears);
        if(!empty($_GET['date_from'])) {
            $df = format_date(($_GET['date_from']));
            $this->set('df',$df);
        }


        if (!empty($_GET['date_to'])) {
            if (empty($_GET['data']['date_to'])) {
                $_GET['data']['date_to']=$_GET['date_to'];
            }
            $dt = format_date(($_GET['date_to']));
            $this->set('dt',$dt);
        }
        $params = '';
        $this->set(compact('reportType', 'type'));
        if (!empty($_GET['report_type'])) {
            App::import('Core', 'Sanitize');
            $type = low($_GET['report_type']);
            if($type == 'all')
            {
                $type = 'transaction';
                $_GET['group_by'] = 'all';
            }
            if (!in_array($type, array('account', 'transaction', 'period', 'staff','cat'))) {
                $type = 'transaction';
            }
            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('JournalTransaction', $type);
            $dataArray = $report->getDataArray();
            $this->set('title_for_layout',  $dataArray['report_title']);
            if ( $this->params['url']['ext'] == 'json'){
                return json_encode ( $dataArray);
                die ;
            }
            $this->set($dataArray, false);
            $this->set('jsonParams', $report->jsonAdapter(), false);
            if (isset($_GET["currency"]) && $_GET["currency"] == -1 || ($type == 'cat')) {

                // get data of all currencies and convert them and sum them
                $arr = array();
                $arr_rates = array();
                $currencies = $this->viewVars["currencies"];
                foreach ($currencies as $key1 => $value1) {
                    $arr_rates[$key1] = CurrencyConverter::index($key1,$default_currency, date('Y-m-d'));
                }
                $counter = 0;

                foreach ($this->viewVars["reportData"] as $key1 => $value1) {
                    foreach ($value1 as $key2 => $value2) {
                        if ($type == 'account' || $type == 'staff' || $type == 'period' || $type == 'cat') {
                            
                            if($key2 == 'acc_id') {
                              $arr[$key1][$key2] = $value2;
                            }elseif ($key2 != "name") {
                                foreach ($value2 as $key3 => $value3) {
                                    if ($key3 == "0") {
                                        if (isset($arr[$key1][$default_currency][$key3])) {
                                            $arr[$key1][$default_currency][$key3]["credit"] += $value3["credit"] ;
                                            $arr[$key1][$default_currency][$key3]["debit"] += $value3["debit"] ;
                                            continue;
                                        } else {
                                            $value3["total_credit"] = $value3["total_credit"] ;
                                            $value3["total_debit"] = $value3["total_debit"] ;
                                            $arr[$key1][$default_currency][$key3] = $value3;
                                            continue;
                                        }
                                    }
                                    if (isset($value3['currency_code'])) {
                                        $value3['currency_code'] = $default_currency;
                                    }
                                    $arr[$key1][$default_currency][$key3] = $value3;
                                }
                            } else {
                                if($value2 === "0"){
                                    $arr[$key1][$key2] = "0";
                                }else{
                                $arr[$key1][$key2] = __($value2, true);
                                }
                            }
                        } else {
                            foreach ($value2 as $key3 => $value3) {
                                $value3["credit"] = ($value3["credit"] );
                                $value3["debit"] = ($value3["debit"] );
                                $value3["currency_code"] = $default_currency;
                                $arr[$default_currency][$key2][$counter] = $value3;
                                $counter = $counter + 1;
                            }
                        }

                    }

                }
                foreach ( $arr as $k => $v ) {
                    uksort ($arr[$k], function ($a , $b){
                        $ac = strtotime ( $a  ) ;
                        $bc = strtotime ($b );
                        if ($ac == $bc) {
                            return 0;
                        }
                        return ($ac < $bc  ) ? -1 : 1;
                    } );
                }

                if($type == 'transaction' && !empty($_GET['date_from'])){
                    //this block is to calculate the before credit for the ledger report based on the first row of each account
                    foreach($arr as $currency => &$group)
                    {
                        foreach($group as $group_title => &$group_data){
                            $first_index = array_keys($group_data)[0];
                            $options['date_to']=  $_GET['date_from'];
                            if(isset($_GET['fy_id']))   $options['fy_id']=$_GET['fy_id'];

                            if (isset($_GET['cost_center_id'])) {
                                $options['cost_center_id']=  $_GET['cost_center_id'];
                            }
                            $balance_before = $this->Journal->report_caculate_account_total($group_data[$first_index]['journal_account_id'],$options, false);
                            if($balance_before['total_credit'] >= $balance_before['total_debit'])
                                $group_data[$first_index]['before_credit'] = $balance_before['total_credit'] - $balance_before['total_debit'];
                            else
                                $group_data[$first_index]['before_debit'] = $balance_before['total_debit'] - $balance_before['total_credit']  ;
                        }
                    }
                }
                $this->set("reportData", $arr);
                if($this->RequestHandler->isAjax()){
                    die(json_encode($arr,JSON_UNESCAPED_UNICODE));
                }
                $this->set("reportCurrencies", array($this->Journal->get_default_currency()));
                $this->set("currency_rates", $arr_rates);
                $this->loadModel('CostCenter');
                $costCenterList = $this->CostCenter->get_secondary_cost_centers('list');
                $this->set('costCenterList', $costCenterList);
            }

        }
        if(empty($this->pageTitle))
        {
            $this->set('title_for_layout',  __($this->viewVars['report_title'], true));
        }


    }

    /*
     * this function is used to save html to a temp file
     * params are sent using post:$_POST['filename'],$_reportPOST['html']
     * returns the $temp_file_name
     */
    function owner_save_html()
    {
        $dir = REPORT_SHARED_DIR . DS . SITE_HASH . DS;
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
        }
        if((isset($_POST['html']) && !empty($_POST['html']))){

            $html = $_POST['html'];
            if((isset($_POST['filename']) && !empty($_POST['filename']))){
                $filename = $_POST['filename'];
                file_put_contents($dir.trim(end(explode('\\',$filename))),$html);
            }else{

                if (!file_exists($dir)) {
                    mkdir($dir, 0777, true);
                }

                $filename = tempnam($dir, "cat_");
                file_put_contents($filename, $html);
            }

            die(end(explode(DS,$filename)));
        }
    }

    function owner_revenue() {
        if(!check_permission([PermissionUtil::Invoices_View_Invoices_Details, PermissionUtil::Invoices_View_All_Invoices])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        set_time_limit(6000);
        ini_set('memory_limit','4G');
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->Staff->applyBranch['onFind'] = false;
            $this->set('staffs', $wt=$this->Staff->getList());
            $this->set('includeOwner', true);
        }
        $this->reportType = 3;
        $this->_ownerSave();
        $owner = getAuthOwner();
        $this->owner = $owner;
        $reportType = '';
        $params = '';
        // warning suppress
        $type = '';
        $this->set(compact('reportType', 'type'));

        if (!empty($_GET['report_type'])) {
            App::import('Core', 'Sanitize');
            $type = !is_array($_GET['report_type'])? low($_GET['report_type']) : null;
            if (!in_array($type, array('client', 'invoice', 'period', 'staff', 'sales_person'))) {
                $type = 'client';
            }
            debug ( $type ) ;
            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('Revenue', $type);
            $dataArray = $report->getDataArray();
//			die(debug($dataArray));
//            debug ($dataArray );
            if ( $this->params['url']['ext'] == 'json'){
                return json_encode ( $dataArray);
                die ;
            }
//            debug ( $dataArray ) ;
            $this->set($dataArray, false);
            $this->set('jsonParams', $report->jsonAdapter(), false);

            $this->_settings();

            if (isset($_GET["currency"]) && $_GET["currency"] == -1) {

                // get data of all currencies and convert them and sum them
                $arr = array();
                $arr_rates = array();
                $currencies = $this->viewVars["currencies"];
                foreach ($currencies as $key1 => $value1) {
                    $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"], date('Y-m-d'));
                }



                $counter = 0;
                // ex($key1 => 157)

                foreach ($this->viewVars["reportData"] as $key1 => $value1) {

                    // ex($key2 => AUD - name)
                    foreach ($value1 as $key2 => $value2) {

                        if ($type == 'client' || $type == 'staff' || $type == 'sales_person' || $type == 'period') {

                            if ($key2 != "name") {

                                foreach ($value2 as $key3 => $value3) {
                                    //debug($key3);
                                    //debug($value3);

                                    if ($key3 == "0") {
                                        if (isset($arr[$key1][$owner["currency_code"]][$key3])) {
                                            $arr[$key1][$owner["currency_code"]][$key3]["total"] += ($value3["total"] * $arr_rates[$value2['I']['currency_code']]);
                                            $arr[$key1][$owner["currency_code"]][$key3]["paid"] += ($value3["paid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $arr[$key1][$owner["currency_code"]][$key3]["unpaid"] += ($value3["unpaid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $arr[$key1][$owner["currency_code"]][$key3]["refund"] += ($value3["refund"] * $arr_rates[$value2['I']['currency_code']]) ;
                                            if(!empty($_GET['show_not_taxed']) && !empty( $value3["totalWithoutTax"])){
                                                $arr[$key1][$owner["currency_code"]][$key3]["totalWithoutTax"] = ($value3["totalWithoutTax"] * $arr_rates[$value2['I']['currency_code']]);
                                            }
                                            continue;
                                        } else {
                                            $value3["total"] = ($value3["total"] * $arr_rates[$value2['I']['currency_code']]);
                                            $value3["paid"] = ($value3["paid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $value3["unpaid"] = ($value3["unpaid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $value3["refund"] = ($value3["refund"] * $arr_rates[$value2['I']['currency_code']]);;
                                            if(!empty($_GET['show_not_taxed']) && !empty( $value3["totalWithoutTax"])){
                                                $value3["totalWithoutTax"] = ($value3["totalWithoutTax"] * $arr_rates[$value2['I']['currency_code']]);;
                                            }
                                            $arr[$key1][$owner["currency_code"]][$key3] = $value3;
                                            continue;
                                        }
                                    }

                                    if (isset($value3['currency_code'])) {
                                        $value3['currency_code'] = $owner["currency_code"];
                                    }

                                    $arr[$key1][$owner["currency_code"]][$key3] = $value3;
                                }
                            } else {
                                $arr[$key1][$key2] = $value2;
                            }
                            // warning suppress
                            $arr[$key1]['bn1'] = $value1['bn1'] ?? null;
                            $arr[$key1]['bn1_label'] =$value1['bn1_label'] ?? null;
                        } else {

                            // invoice type
                            // ex($key2 => Adanced Tech, $key3=> 0)
                            foreach ($value2 as $key3 => $value3) {
                                $currencyRate = $value3['currency_rate'] ?? CurrencyConverter::index($value3["currency_code"], $owner["currency_code"], $value3["date"]);
                                $value3["summary_total"] = ($value3["summary_total"] * $currencyRate);
                                $value3["summary_paid"] = ($value3["summary_paid"] * $currencyRate);
                                $value3["summary_unpaid"] = ($value3["summary_unpaid"] * $currencyRate);
                                $value3["summary_refund"] = ($value3["summary_refund"] * $currencyRate);

                                if(!empty($_GET['show_not_taxed'])){
                                    $value3["totalWithoutTax"] = ($value3["totalWithoutTax"] * $currencyRate);
                                }
                                $value3["currency_code"] = $owner["currency_code"];

                                $arr[$owner["currency_code"]][$key2][$counter] = $value3;
                                $counter = $counter + 1;
                                //$arr[$key1][$key2][$counter]['bn1'] = $value1['bn1'];
                                //$arr[$key1][$key2][$counter]['bn1_label'] =$value1['bn1_label'];
                            }

                        }
                    }

                }

                foreach ( $arr as $k => $v ) {
                    uksort ($arr[$k], function ($a , $b){
                        $ac = strtotime ( $a  ) ;
                        $bc = strtotime ($b );
                        if ($ac == $bc) {
                            return 0;
                        }
                        return ($ac < $bc  ) ? -1 : 1;
                    } );
                }
                //debug($arr);
                //debug($this->viewVars["jsonParams"]);
                $this->set("reportData", $arr);


                $this->set("reportCurrencies", array($owner["currency_code"]));
                $this->set("currency_rates", $arr_rates);
            }
        }

        //$this->_settings();

        $this->loadModel('CustomForm');
        $reread_form = $this->CustomForm->findByTableName("invoices");
        if (!empty($reread_form)) {
            $this->loadModel('CustomFormField');

            $filtered_fields = array();
            $form_fields = $this->Invoice->get_custom_fields_filters($reread_form['CustomForm']['id']);
            foreach ($form_fields as $key => $value) {
                if ($value['myval']["CustomFormField"]["is_filtered"] == 1) {
                    $filtered_fields[] = $value['myval'];
                }
            }
            $this->set('filtered_fields', $filtered_fields);

        }
        $this->set('title_for_layout',  isset($this->viewVars["report_title"])? __($this->viewVars["report_title"], true) : __("Invoices Report",true));
        $this->set('client_categories', $categories = $this->Client->getCategoriesListWithIds());
        $shipping_option_data = $this->getShippingOptionData();
        if(!empty($shipping_option_data) && count($shipping_option_data) > 1) {
            $this->set('shipping_option_data',$shipping_option_data);
        }

        if (!empty($_GET['quick'])) {
            if(empty($this->viewVars['reportData'])){
                die();
            }

            $this->layout = '';
            $this->render('owner_revenue_quick');
        }

    }


    public function getCurrencyRate($from, $to, $date)
    {
        if(!isset(self::$currencyRates[$from][$date])) {
            return self::$currencyRates[$from][$date] = CurrencyConverter::index($from, $to, $date);;
        }

        return self::$currencyRates[$from][$date];

    }

    function owner_purchases() {

//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }
        $this->reportType = 3;
        $this->_ownerSave();
        $owner = getAuthOwner();
        $this->owner = $owner;
        $reportType = '';
        $params = '';
        $this->set(compact('reportType', 'type'));

        if (!empty($_GET['report_type'])) {
            App::import('Core', 'Sanitize');
            $type = low($_GET['report_type']);
            if (!in_array($type, array('supplier', 'purchase_order', 'period', 'staff'))) {
                $type = 'supplier';
            }

            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('Purchases', $type);
            $dataArray = $report->getDataArray();
            if ( $this->params['url']['ext'] == 'json'){
                return json_encode ( $dataArray);
                die ;
            }
//            debug ( $dataArray ) ;
            $this->set($dataArray, false);
            $this->set('jsonParams', $report->jsonAdapter(), false);

            $this->_settings();

            if (isset($_GET["currency"]) && $_GET["currency"] == -1) {

                // get data of all currencies and convert them and sum them
                $arr = array();
                $arr_rates = array();
                $currencies = $this->viewVars["currencies"];

                foreach ($currencies as $key1 => $value1) {
                    $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"], date('Y-m-d'));
                }


                //debug($this->viewVars["reportData"]);
                $counter = 0;
                // ex($key1 => 157)
                foreach ($this->viewVars["reportData"] as $key1 => $value1) {

                    // ex($key2 => AUD - name)
                    foreach ($value1 as $key2 => $value2) {
                        if ($type == 'supplier' || $type == 'staff' || $type == 'period') {
                            if ($key2 != "name") {
                                if (is_array($value2)) {
                                    uasort($value2, function ($a, $b) {
                                        $ac = isset($a["date"]) ? strtotime($a["date"]) : false;
                                        $bc = isset($b["date"])? strtotime($b["date"]) : false;
                                        if ($ac == $bc) {
                                            return 0;
                                        }
                                        return ($ac < $bc) ? -1 : 1;
                                    });
                                }
                                foreach ($value2 as $key3 => $value3) {
                                    //debug($key3);
                                    //debug($value3);

                                    if ($key3 == "0") {
                                        if (isset($arr[$key1][$owner["currency_code"]][$key3])) {
                                            $arr[$key1][$owner["currency_code"]][$key3]["total"] += ($value3["total"] * $this->getCurrencyRate($value2['I']['currency_code'], $owner['currency_code'], $value3['date']));
                                            $arr[$key1][$owner["currency_code"]][$key3]["paid"] += ($value3["paid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $arr[$key1][$owner["currency_code"]][$key3]["unpaid"] += ($value3["unpaid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $arr[$key1][$owner["currency_code"]][$key3]["refund"] += ($value3["refund"] * $arr_rates[$value2['I']['currency_code']]);
                                            continue;
                                        } else {
                                            $value3["total"] = ($value3["total"] * $arr_rates[$value2['I']['currency_code']]);
                                            $value3["paid"] = ($value3["paid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $value3["unpaid"] = ($value3["unpaid"] * $arr_rates[$value2['I']['currency_code']]);
                                            $value3["refund"] = ($value3["refund"] * $arr_rates[$value2['I']['currency_code']]);

                                            $arr[$key1][$owner["currency_code"]][$key3] = $value3;
                                            continue;
                                        }
                                    }

                                    if (isset($value3['currency_code'])) {
                                        $value3['currency_code'] = $owner["currency_code"];
                                    }

                                    $arr[$key1][$owner["currency_code"]][$key3] = $value3;
                                }
                            } else {
                                $arr[$key1][$key2] = $value2;
                            }
                            $arr[$key1]['bn1'] = $value1['bn1'];
                            $arr[$key1]['bn1_label'] =$value1['bn1_label'];
                        } else {


                            // purchase_order type
                            // ex($key2 => Adanced Tech, $key3=> 0)
                            foreach ($value2 as $key3 => $value3) {
                                $rateForTransaction = $this->getCurrencyRate($value3["currency_code"], $owner['currency_code'],$value3['date']);
                                $value3["summary_total"] = ($value3["summary_total"] * $rateForTransaction);
                                $value3["summary_paid"] = ($value3["summary_paid"] * $rateForTransaction);
                                $value3["summary_unpaid"] = ($value3["summary_unpaid"] * $rateForTransaction);
                                $value3["currency_code"] = $owner["currency_code"];

                                $arr[$owner["currency_code"]][$key2][$counter] = $value3;
                                $counter = $counter + 1;
                            }
                            $arr[$key1][$key2]['bn1'] = $value1['bn1'];
                            $arr[$key1][$key2]['bn1_label'] =$value1['bn1_label'];
                        }
                    }

                }
                foreach ( $arr as $k => $v ) {
                    uksort ($arr[$k], function ($a , $b){
                        $ac = strtotime ( $a  ) ;
                        $bc = strtotime ($b );
                        if ($ac == $bc) {
                            return 0;
                        }
                        return ($ac < $bc  ) ? -1 : 1;
                    } );
                }
//                debug($arr);
                //debug($this->viewVars["jsonParams"]);
                $this->set("reportData", $arr);


                $this->set("reportCurrencies", array($owner["currency_code"]));
                $this->set("currency_rates", $arr_rates);
            }
        }
        $this->loadModel ( 'Supplier');
        $this->set('suppliers', $this->Supplier->getSuppliersList());
        //$this->_settings();
        $this->set('title_for_layout',  $this->viewVars['report_title'] ??  __("Purchases Report",true));
        if (!empty($_GET['quick'])) {
            if(empty($this->viewVars['reportData'])){
                die();
            }

            $this->layout = '';
            $this->render('owner_revenue_quick');
        }

    }
    function _ownerSave() {
        if (!empty($this->data)) {
            $this->data['SavedReport']['site_id'] = getAuthOwner('id');

            $params = json_decode($this->data['SavedReport']['data'], true);
            if (!empty($params['date_from'])) {
                $params['date_from'] = $this->SavedReport->formatDate($params['date_from'], getCurrentSite('date_format'));
            }
            if (!empty($params['date_to'])) {
                $params['date_to'] = $this->SavedReport->formatDate($params['date_to'], getCurrentSite('date_format'));
            }
            $this->data['SavedReport']['data'] = json_encode($params);
            if ($this->SavedReport->save($this->data)) {

                $this->flashMessage(__('Report has been saved', true), 'Sucmessage');
                $this->redirect($_SERVER['REQUEST_URI']);
            } else {
                $this->flashMessage(__('Could not save report', true));
            }
        }

        $this->set('savedReports', $this->SavedReport->getSavedReportsList($this->reportType));
    }

    function owner_delete($id = null) {
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('report', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('Report', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {
            $verb = __('have', true);
            $module_name = __('Reports', true);
        }
        $conditions = array();
        $conditions['SavedReport.site_id'] = getAuthOwner('id');
        $conditions['SavedReport.id'] = $id;
        $reports = $this->SavedReport->find('all', array('conditions' => $conditions));

        if (empty($reports)) {
            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->SavedReport->deleteAll(array('SavedReport.id' => $_POST['ids']))) {
                $this->flashMessage(sprintf(__('%s %s been deleted', true), $module_name, $verb), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('Could not delete %s', true), low($module_name)));
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  __('Delete Document', true));

        $this->set('reports', $reports);
        $this->set('module_name', $module_name);
    }

    function _settings() {

        $this->loadModel('Invoice');
        $owner = getAuthOwner();
        $Currencylist = $this->Invoice->getCurrencylist();
        $invoice_type_list = $this->Invoice->getInvoiceTypeList();
        $invoice_type_list['2'] = __('Outbound Requisition', true);
        $this->set('invoices_type_list', $invoice_type_list);
        $this->set('invoices_view_link', $this->Invoice->getInvoiceViewActionList());
        $this->set('clients_list',$this->Invoice->Client->getClientsList(false,array(),999999999999));
        $this->loadModel('Expense');
        foreach ($this->Expense->getCurrencylist() as $ExpenseCurrency) {
            if(!in_array($ExpenseCurrency, $Currencylist)){
                $Currencylist[] = $ExpenseCurrency;
            }
        }

        $this->loadModel('PurchaseOrder');
        foreach ($this->PurchaseOrder->getCurrencylist() as $PurchaseOrderCurrency) {
            if(!in_array($PurchaseOrderCurrency, $Currencylist)){
                $Currencylist[] = $PurchaseOrderCurrency;
            }
        }

        $this->loadModel('Asset');
        foreach ($this->Asset->getCurrencylist() as $AssetCurrency) {
            if(!in_array($AssetCurrency, $Currencylist)){
                $Currencylist[] = $AssetCurrency;
            }
        }

//        debug($Currencylist);
        if (empty($Currencylist)) {
            $Currencylist[] = getCurrentSite('currency_code');
        }
        $currencies = [];
        if (count($Currencylist) > 0) {
            $currencies = getCurrenciesList($Currencylist);
        }
        $this->set('currencies', $currencies);
        $this->set('owner', $owner);
        $this->loadModel('OrderSource');
        $showOrderSourceFilter = $this->OrderSource->find('count');
        $this->set('showOrderSourceFilter', $showOrderSourceFilter);

    }

    function owner_stock_transactions ( ) {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        ini_set('memory_limit','8G');
        set_time_limit(3600);
        $this->loadModel ( 'Store' );
        $this->loadModel ( 'Product' );
        $this->loadModel ( 'StockTransaction');
        $this->loadModel ( 'TransactionCategory');
        $this->loadModel ( 'ItemPermission');
        $stores_list = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW,null,false,true);
        $this->set ( 'store_list' , $stores_list );

        if (ifPluginActive(WorkOrderPlugin)){
            $this->loadModel('WorkOrder');
            $this->set ( 'work_orders', $this->WorkOrder->get_work_orders ( ['status' => [WorkOrder::STATUS_OPEN ,WorkOrder::STATUS_CLOSED ] ] )) ;
        }
        $owner = getCurrentSite() ;
        $this->set ( 'owner' , $owner );

//        $products_list = $this->Product->find('list' , ['conditions' => ["(Product.deactivate <> 1 Or Product.deactivate is null) and track_stock <> 0" ]]);
//        $this->set ( 'products_list' , $products_list);

        $this->set ( 'stores_list' , $stores_list);
        $cat_list= $this->TransactionCategory->find ( 'list' , ['conditions' => ['deleted = 0']]);
        $this->set ( 'cat_list' ,$cat_list ) ;
        $types = $this->StockTransaction->getSources();

        $type_filter = $types;
        foreach ($cat_list as $k => $v ) {
            $type_filter["cat_".$k] = $v;
        }
        $this->set ( 'types' , $types);
        $this->set ( 'type_filter' , $type_filter);

        $this->data = $this->params['url']['data'];
        if ( !empty ( $this->data)){
            $this->set ( 'enable_multi_units' , settings::getValue(InventoryPlugin, 'enable_multi_units') );
            if ( $this->viewVars['enable_multi_units'] && !empty($this->data['product']) ){
                $this->set('main_factor' , $this->Product->get_default_factor ( $this->data['product'] ) );
            }
            //debug ( $this->data ) ;
            $date_from =$this->Store->formatDate($this->data['date_from']) ;
            $date_to = $this->Store->formatDate($this->data['date_to']) ;
            $starting_quantity = 0 ;

            //Generating conditions for the report.
            $conditions = ['StockTransaction.ignored = 0'] ;
            if ($this->data['store'] != ""  )
            {
                $conditions['store_id'] =$this->data['store'];
            }else{
                $conditions[] = 'store_id IN ('.implode(',', array_keys($stores_list)).')';
            }
            if ($this->data['product'] != ""  )
            {
                if (substr($this->data['product']??'', 0, 1) === 'g') {// if the product is item group record
                    $itemGroupId = intval(str_replace("g","",$this->data['product']));
                    $itemGroupProducts = $this->Product->find('all',['fields'=> ['id'], 'recursive' => -1, 'conditions' => ['Product.item_group_id' => $itemGroupId]]);
                    $itemGroupProducts = array_map(function($p){
                        return $p['Product']['id'];
                    }, $itemGroupProducts);
                    $conditions[] =  'product_id IN ('.implode(',', $itemGroupProducts).')';
                }else{
                    $conditions['product_id'] = $this->data['product'];
                }
            }
            $conditions[] = "Product.id is not null and (Product.status <> ". ProductStatusUtil::STATUS_INACTIVE ." or Product.status is null) and StockTransaction.status = 4 and track_stock <> 0";
            if ( $this->data['type_filter'] !="" ){
                if ( strpos ( $this->data['type_filter'] , "cat_") === false ){
                    $conditions['StockTransaction.source_type'] =  $this->data['type_filter'];
                    $conditions['transaction_category_id'] = null ;
                }else {
                    $conditions['transaction_category_id'] = substr ($this->data['type_filter'] , 4);
                }
            }
            if ( $this->data['date_from'] != "" ){
                $new_conditions = $conditions ;$new_conditions[] = "StockTransaction.received_date < '$date_from 00:00:00'";
                $sum_start = $this->StockTransaction->find ( 'first' , ['fields' => "SUM(quantity) as start_quantity" , "conditions" => $new_conditions]);
                $starting_quantity = $sum_start[0]['start_quantity'];
                $conditions[] = "`received_date` >= '$date_from 00:00:00'" ;
            }
            if ( $this->data['date_to'] != "" ){
                $conditions[] = "`received_date` <= '$date_to 23:59:59'" ;
            }
            if (ifPluginActive(WorkOrderPlugin ) && !empty ( $this->data['work_order_id'])){
                $this->loadModel ( 'WorkOrder');
                $conditions['StockTransaction.id'] = $this->WorkOrder->get_stock_transaction_ids ( intval ($this->data['work_order_id']));
            }

            $args = ['order' => ['received_date' => 'ASC'],'applyBranchFind' => false, 'conditions'=> $conditions ];

            $transactionsCount =  $this->StockTransaction->find ( 'count' , $args);

            if(!isset($this->params['url']['threshold_limit'])){
                $this->displayLimitThresholdByCount($transactionsCount, 1000,'v2');
            }

            $transactions = [];

            if(!isset($this->params['url']['threshold_limit'])){
                $transactions = $this->StockTransaction->find ( 'all' , $args);
            }

            $requisition_ids = [];

			foreach ($transactions as $transaction) {
				if ($transaction['StockTransaction']['source_type'] > 100) {
					$requisition_ids[] = $transaction['StockTransaction']['order_id'];
				}
	        }
			$RequsitionModel = GetObjectOrLoadModel('Requisition');
			$requisitions_notes = $RequsitionModel->find('list', ['fields' => ['id', 'notes'], 'recursive' => -1, 'conditions' => [
				'Requisition.id' => $requisition_ids
			]]);


            $date_str= "";
            if ( $this->data['date_from'] != "" ){
                $date_str .= sprintf ( __("From %s" , true ) ,$this->data['date_from'] )." ";
            }
            if ( $this->data['date_from'] != "" ){
                $date_str .= sprintf ( __("To %s" , true ) ,$this->data['date_to'] )." ";
            }
            if ( trim($date_str) == "" ) {
                $date_str = format_date(date('Y-m-d' )) . ' ' . date('H:i');
            }



			$this->set( 'requisitions_notes', $requisitions_notes);
            $this->set ( 'date_str' , $date_str ) ;
            $this->set ( 'starting_quantity' , $starting_quantity);
            $this->set ( 'transactions' , $transactions);


        }
        if (substr($this->data['product']??'', 0, 1) === 'g') {// if the product is item group record
            $itemGroupId = intval(str_replace("g","",$this->data['product']));
            $this->loadModel('ItemGroup') ;
            $itemGroup = $this->ItemGroup->find('first' ,  ['fields'=> ['name'], 'recursive'=> -1,'conditions' =>['id' => str_replace("g","",$itemGroupId)]]);
            $title = __('Inventory Detailed Transactions',true)
            . (($this->data['store'] != "") ?' - '.$transactions[0]['Store']['name'] : "")
            . (($this->data['product'] != "") ?' - '.$itemGroup['ItemGroup']['name'].' ('.__('Item Group', true).')' : "");
        }else{
            $title = __('Inventory Detailed Transactions',true)
            . (($this->data['store'] != "") ?' - '.$transactions[0]['Store']['name'] : "")
            . (($this->data['product'] != "") ?' - '.$transactions[0]['Product']['name'] : "");
        }

        $this->set ( 'mytitle' , $title ) ;
        $this->set('title_for_layout', $title);
    }
    function owner_financial_report ( )
    {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $this->loadModel ( 'Expense');
        $this->loadModel ( 'InvoicePayment');
        $current_currency = getCurrentSite('currency_code');
        if (ifPluginActive(WorkOrderPlugin)){
            $this->loadModel('WorkOrder');
            $this->set ( 'work_orders', $this->WorkOrder->get_work_orders ( ['status' => [WorkOrder::STATUS_OPEN ,WorkOrder::STATUS_CLOSED ] ] )) ;
        }
        $view_query = $this->InvoicePayment->get_financial_report_view () ;
        //Getting filters from URL
        $date_from = $this->Expense->formatDate($this->params['url']['date_range_from'] );
        $date_to = $this->Expense->formatDate($this->params['url']['date_range_to']) ;
        $page = $this->params['url']['page']?intval ($this->params['url']['page']) : 1 ;
        //TODO how to implement branches in this report
        //Handle Branches
        $branchCondition = "";
        $branchCondition2 = "";
        if (ifPluginActive(BranchesPlugin)) {
            $allowedBranches = getStaffBranchesIDsSuspended();
            $branchesData = $this->params['url']['branch_id'];
            if (isset($branchesData) && !empty($branchesData)) {
                if (!is_array($branchesData))
                    $branchesData = [$branchesData];
                foreach ($branchesData as $key => $value) {
                    if (!in_array($value, $allowedBranches))
                        unset($branchesData[$key]);
                }
            }
            if (empty($branchesData))
                $branchesData = $allowedBranches;
            $branchCondition = " and {{}}.branch_id IN (" . implode(',', $branchesData) . ')';

            $this->InvoicePayment->FRBranchConditions = $branchCondition;
        }
        //PerPage paginator
        $per_page = 300 ;
        if ( !empty ($this->params['url']['ext']) ){
//            $per_page = INF ;
//            $page = 0 ;
        }
        if($this->layoutPath){
            $per_page = 10000;
            $page = 1;
        }
        if ( trim($this->params['url']['currency']) != ""  )
        {
            $current_currency = $this->params['url']['currency'];
        }

        $starting_balance = 0 ;

        $paginator_url = "" ;
        $where =  "{{}}.currency_code='$current_currency' AND " ;
        $where2 = "1 " ;
        if ( isset ($this->params['url']['date_range_selector']) &&$this->params['url']['date_range_selector'] == "lastyear" ){
            $where .="  YEAR({{}}.`date`) = YEAR(CURRENT_DATE - INTERVAL 1 YEAR) AND ";
            $where2 = " YEAR( {{}}.`date`) < YEAR(CURRENT_DATE - INTERVAL 1 YEAR) AND {{}}.currency_code='$current_currency' ";
            $paginator_url .= "date_range_selector=lastyear&";
        }else if ( isset ($this->params['url']['date_range_selector']) &&$this->params['url']['date_range_selector'] == "lastmonth" ){
            $paginator_url .= "date_range_selector=lastmonth&";
            $where .="  date_format({{}}.`date`,'%Y-%m')=date_format((CURRENT_DATE - INTERVAL 1 MONTH),'%Y-%m') and ";
//            $where .="  YEAR( {{}}.`date` ) = YEAR(CURRENT_DATE)
//AND MONTH({{}}.`date`) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH) AND ";

            $where2 ="  {{}}.`date` < date_format(CONCAT(DATE_FORMAT(CURRENT_DATE - INTERVAL 1 MONTH, '%Y-%m'), '-01') AND {{}}.currency_code='$current_currency' ";

        }else {
            if ( trim($this->params['url']['date_range_from']) != ""  )
            {
                $where .= "{{}}.`date` >= '".$date_from."' AND ";
                $paginator_url .= "date_range_from=" .$this->params['url']['date_range_from']."&";
                //Getting the starting balance before the date from .
                $where2 = "{{}}.`date` < '$date_from' AND {{}}.currency_code='$current_currency'";


            }
            if ( trim($this->params['url']['date_range_to']) != ""  )
            {
                $where .= "{{}}.`date` <= '".$date_to."' AND ";
                $paginator_url .= "date_range_to=" .$this->params['url']['date_range_to']."&";
            }
        }
        if (trim($this->params['url']['branch_id']) != "") {
            $paginator_url .= "branch_id=" . $this->params['url']['branch_id'] . "&";
        }
        if (trim($this->params['url']['currency']) != "") {
            $paginator_url .= "currency=" . $this->params['url']['currency'] . "&";
        }
        if ( ifPluginActive(WorkOrderPlugin)&&  !empty ( $this->params['url']['work_order_id']))
        {
            $where .= "{{}}.work_order_id = ".intval($this->params['url']['work_order_id'])." AND ";
            $where2 .= " AND {{}}.work_order_id = ".intval($this->params['url']['work_order_id']);
        }
        $where2 .=$branchCondition;
        $where .= '1 ';
        if ( $where2 != "" && $where2 != "1 " )
        {
            $starting_balance += $this->InvoicePayment->previous_period_financial_sum ( $where2 );
        }

        $where .= $branchCondition;



        if ( $page > 1 ) {
            //Getting the starting balance for the next page .
            $starting_balance += $this->InvoicePayment->get_previous_page_balance ( $where , $per_page  , $page );
        }

        $results = $this->InvoicePayment->get_financial_results (  $where ,$per_page , $page, false, true ) ;
        $results_count = 0;
        if(is_countable($results)) {
            $results_count = count($results);
        }
        debug ( $results_count );
        $count_result = $this->InvoicePayment->count_all_financial_results ( $where ) ;
        $this->set ('previous_income' , $this->InvoicePayment->get_previous_income ( $where , $per_page , $page));
        $this->set ('previous_expense' , $this->InvoicePayment->get_previous_expense ( $where , $per_page , $page));
        $currency_query = sprintf($view_query , 1 ,1 , 1, 1,1 );
        $currencies_result = $this->Expense->flat_query_results ("select distinct(currency_code) as currency from ($currency_query ) aaa " ) ;
        $currencies_list = [] ;
        foreach ($currencies_result as $c ) {
            $currencies_list[$c['currency'] ] = $c['currency'] ;
        }
        $date_str = "" ;
        if ( $this->params['url']['date_range_from'] ){
            $date_str .= sprintf ( __("From %s" , true ) ,$this->params['url']['date_range_from'] )." ";
        }
        if ( $this->params['url']['date_range_to'] ){
            $date_str .= sprintf ( __("To %s" , true ) ,$this->params['url']['date_range_to'] )." ";
        }
        if ( trim ($this->params['url']['date_range_selector'] ) != "" )
        {
            $date_str .= ($this->params['url']['date_range_selector'] == "lastmonth" ?__("Month",true) . ' '.  date("m-Y",strtotime("-1 Month")):__("Year",true) . ' '. date("Y",strtotime("-1 year")));
        }
        if ( $date_str == "" ) {
            $date_str = format_date(date('Y-m-d'  )) . ' ' . date('H:i');
        }
        if ( isset ( $categories [$this->params['url']['category']] ) ) {
            $date_str .= " - ".$categories [$this->params['url']['category']];
        }
        $report_title = __("Financial Transactions", true). ' ('.$current_currency.') ';
        $this->set('title_for_layout',  __("Financial Transactions", true));
        $this->set ( 'date_str' , $date_str);
        $this->set ( 'report_title' , $report_title);
        $this->set ( 'payment_methods' , InvoicePayment::getPaymentMethods() ) ;
        $this->set ( 'current_currency' , $current_currency );
        $this->set ( 'currencies' , $currencies_list );
        $this->set ( 'paginator_url' , $paginator_url );
        $this->set ( 'current_page' , $page );
        $this->set ( 'count' , ceil ($count_result[0]['cc']/$per_page) );
        $this->set ( 'starting_balance' , $starting_balance ) ;
        $this->set ( 'results' , $results ) ;
    }
    function owner_account_transactions ($account_id )
    {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        if($this->RequestHandler->isAjax()){
            $this->set("is_ajax",true);
        }
        $this->loadModel('Journal');
        $this->loadModel('JournalTransaction');
        $this->loadModel('JournalAccount');
        $this->Journal->recursive = 1;
        $conditions = $this->_filter_params();


        if(isset($_GET['ASC']) || isset($_GET['asc']) ) $order = "ASC";
        else $order = "DESC" ;
        $order = "DESC" ;
        $account_id = !empty($_GET['id']) ? urlencode($_GET['id']) : $account_id;
//		$journals = $this->Journal->find('all',array('order' => array('Journal.created' =>)))
        $this->paginate = array(
            'order' => array(
                'Journal.date' => $order,
                'JournalTransaction.id' => $order,
            )
        );
        $conditions['JournalTransaction.journal_account_id'] = $account_id ;
        debug($_GET);
        if(isset($_GET['date_from']) && !empty($_GET['date_from']))
        {
            $date_from = urldecode($_GET['date_from']);
            $conditions[] = "Journal.date >= '$date_from'" ;

        }

        if(isset($_GET['currency_code'])  && !empty($_GET['currency_code'])){
            $currency = $_GET['currency_code'];
            $conditions[] = 'Journal.currency_code = "'.$_GET['currency_code'].'"';
        }
        if(isset($_GET['date_to']) && !empty($_GET['date_to'])){
            $date_to = urldecode($_GET['date_to']);
            $conditions[] = "Journal.date <= '$date_to'" ;
        }
        debug($conditions);

        $transactions = $this->paginate('JournalTransaction', $conditions);
//            dd($transactions   );
        $this->loadModel('Journal');

        if($order == "ASC"){

            $starting_balance = $this->Journal->calculate_account_balance_before($account_id,$transactions[0],$currency);

            $transactions = $this->Journal->calculate_account_balance_after_transactions($transactions,$starting_balance,$order,$currency);

        }else{

            $starting_balance = $this->Journal->calculate_account_balance_before($account_id,end($transactions),$currency);
            $transactions = $this->Journal->calculate_account_balance_after_transactions($transactions,$starting_balance,$order,$currency);

        }
//            dd ( $starting_balance ) ;
        $this->set('default_currency',$this->Journal->get_default_currency());
        if(empty($currency)){
//			$this->set('show_other_currencies',true);
            $this->set('currency_code', $this->Journal->get_default_currency());
        }else{

            $this->set('currency_code', $currency);
        }
        $this->set('starting_balance', $starting_balance);
        $this->set('transactions', $transactions);
//            dd($account_id ) ;
//            dd($transactions ) ;
        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->set('current_staff_id',getAuthStaff('id'));
    }

    function owner_report_generator()
    {
        App::import('vendor','ReportGenerator',['file'=>'ReportGenerator/autoload.php']);
        $reportGenerator = new \ReportGenerator\ReportGenerator();
        die(json_encode($reportGenerator->entities));
        $this->set('reportGenerator', $reportGenerator);
        if(!empty($this->data))
        {
            die(debug($this->data));
        }
    }

    function owner_balance_sheet()
    {
//        if(!check_permission([PermissionUtil::View_His_Own_Reports])){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $this->loadModel('JournalAccount');
        $this->loadModel('Journal');
        $this->loadModel('JournalCat');
        $start = microtime(true);
        $options = $_GET;
        $expense = $this->Journal->report_caculate_cat_total(43, $options);
        $netExpense =  $expense['total_credit'] - $expense['total_debit'] ;
        $income = $this->Journal->report_caculate_cat_total(27, $options);
        $netIncome = $income['total_credit'] - $income['total_debit'];
        $assetsAccounts = $this->JournalAccount->getLeafJournalCatsAndAccounts(15, $options);
        $liabilitiesAccounts = $this->JournalAccount->getLeafJournalCatsAndAccounts(39, $options);
        $profitAndLoss =  $netIncome - $netExpense;
        $totalAssets =  (($assetsAccounts[0]['JournalCat']['type']  == Journal::JOURNAL_TYPE_DEBITOR)
            ? ($assetsAccounts[0]['JournalCat']['total_debit'] - $assetsAccounts[0]['JournalCat']['total_credit'])
            : ($assetsAccounts[0]['JournalCat']['total_credit'] - $assetsAccounts[0]['JournalCat']['total_debit'])
        );
        $totalLiab =  (($liabilitiesAccounts[0]['JournalCat']['type']  == Journal::JOURNAL_TYPE_DEBITOR)
            ? ($liabilitiesAccounts[0]['JournalCat']['total_debit'] - $assetsAccounts[0]['JournalCat']['total_credit'])
            : ($assetsAccounts[0]['JournalCat']['total_credit'] - $liabilitiesAccounts[0]['JournalCat']['total_debit'])
        );
        $time_elapsed_secs = microtime(true) - $start;
//        var_dump($time_elapsed_secs);
//        dd($totalLiab);
        $this->set('totalAssets' , $totalAssets);
        $this->set('totalLiab' , $totalLiab);
        $this->set('assetsAccounts', $assetsAccounts );
        $this->set('liabAccounts', $liabilitiesAccounts );
        $this->set('profitAndLoss', $profitAndLoss);
        $this->set('currency', $this->Journal->get_default_currency());
    }


    function model_select($model, $displayField)
    {
        $this->loadModel($model);
        $conditions = [];
        if($_GET['q'])
        {
            //TODO input sanatization
            $conditions[] = "$model.$displayField LIKE \"%{$_GET['q']}%\"";
        }
        return die(json_encode($this->{$model}->find('list', ['conditions' => $conditions, 'limit' => 10, 'fields' => ['id', $displayField]])));
    }

    function save_report($entity)
    {
//        $data = json_decode(file_get_contents("php://input"), true);
        $data = json_decode('{"reportEntities":{"Invoice":{"name":"Invoice","showFields":true,"related":["Client"]},"Client":{"name":"Client","relatedTo":"Invoice","showFields":true,"related":[]}},"operations":{"Invoice-total":"SUM"},"fields":{"Invoice-id":{"data":{"name":"id","table_field":"id","field_type":"NUMBER"}},"Invoice-date":{"data":{"name":"date","table_field":"date","field_type":"DATE"}},"Invoice-client_id":{"data":{"name":"client id","table_field":"client_id","field_type":"DATE"}},"Invoice-total":{"data":{"name":"summary total","table_field":"summary_total","field_type":"PRICE"}},"Client-name":{"data":{"name":"name","table_field":"business_name","field_type":"STRING"}}},"sortFields":[{"value":"Invoice-date","label":"Invoice-date"}],"groupFields":[{"value":"Invoice-date","label":"Invoice-date"},{"value":"Invoice-client_id","label":"Invoice-client_id"}],"filters":{"Client-name":{"name":"select","operation":"equal","type":"select","display_field":{"field":"business_name","model":"Client"},"component":"SelectFilter","data":{"value":"2","label":"صالح هزاع الشمراني"}},"Invoice-date":{"name":"DateRange","operation":"equal","type":"date_range","component":"DateRange","data":{"dateRangeOption":"last_month","dateFrom":"2019-03-21","dateTo":"2019-03-21"}}}}', true);
        App::import('vendor','ReportGenerator',['file'=>'ReportGenerator/autoload.php']);
        $reportGenerator = new \ReportGenerator\ReportGenerator();
        $reportGenerator->save($entity, $data);
        die();
    }

    public function beforeRender()
    {
        parent::beforeRender();
        if (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == ReportUtil::XLSX) {
            $this->autoRender = false;
            $this->theme = false;
            $this->autoLayout = $this->layout = false;
            $reportAction = $this->params['action'];
            $reportType = $this->params['url']['report_type'];
            $reportName = str_replace('owner_', '', $reportAction);
            $element = ReportFactory::element($reportAction, $reportType);
            ReportFactory::init(ReportUtil::XLSX)->export($reportName, $this, $element, $reportType ? $reportType : $this->params['pass'][0]);
        }
    }

    public function owner_cash_flow() {
        $this->loadModel('Journal');
        $this->set('default_currency', $this->Journal->get_default_currency());
        $report = new \App\Reports\CashFlow\CashFlowReport();
        $params = $this->params['url'];
        $report_data = $report->get([
            'date_to' => $params['date_to'],
            'date_from' => $params['date_from'],
            'branch_id' => $params['data']['branch_id']
        ]);
        $this->set('report_data', $report_data);
        $this->set('title_for_layout', __('Cash Flow Report',true));


    }

    public function owner_benchmarks()
    {
        $this->layout = '';
        $this->loadModel('Benchmark');
        $this->set('benchmarks', $this->Benchmark->find('all'));
    }

    private function getShippingOptionData()
    {
        $this->loadModel('ShippingOption');
        return  $this->ShippingOption->getList();
    }

    public function api_get_attendance_report_for_staff($report_data)
    {
        $this->autoRender = false; // Disable view rendering
        $formatter = new AttendanceSheetReportApiFormatter($report_data);
        $params=$this->params['url'];
        $formatter->formatApiResponse($params);
    }



    function api_set_report_session(){
        $key = $this->data['key'];
        $imageUrl = $this->data['imageUrl'];
        $chartId = $this->data['chartId'];
        $session_key = sprintf('%s_%s', $key, getCurrentSite('id'));
        $data = [
            $chartId => $imageUrl,
        ];
        if($this->Session->check($session_key)){
            $data = array_merge($data, $this->Session->read($session_key));
        }
        $this->Session->write($session_key, $data);
        die(json_encode(['message' => 'success']));
    }

    private function api_get_payroll_report_for_staff(?array $report_data , $params)
    {
        $this->autoRender = false;
        $formatter = new PayrollReportApiFormatter($report_data);
        $formatter->formatApiResponse($params);
    }

    private function setParamsPermissions($params,$name)
    {
        if (isset($this->params['url']['wants_json'])) {
            if ($name == 'attendance_sheets') {
                $params['permissions'] = [
                    PermissionUtil::VIEW_ALL_THE_ATTENDANCE_LOG,
                    PermissionUtil::VIEW_HIS_OWN_ATTENDANCE_LOG
                ];
            } elseif ($name == 'payslips') {
                $params['permissions'] = [
                    PermissionUtil::VIEW_PAY_RUN
                ];
            }
        }
        return $params;
    }


    function owner_advance_payments() {
        set_time_limit(3600);
        ini_set('memory_limit','4G');
        $this->reportType = 2;
        $owner = getAuthOwner();
        $this->set(array('type' => '', 'reportType' => ''));
        if (!empty($_GET['report_type'])) {
            $type = 'client';
            if (low($_GET['report_type']) != 'client') {
                $type = low($_GET['type']);
                if (!in_array($type, array('client', 'daily', 'weekly', 'monthly', 'yearly', 'lastmonth', 'lastyear', 'staff', 'payment_method'))) {
                    $type = 'monthly';
                }
            }
            require_once APP . 'vendors' . DS . 'Report.php';
            $report = new Report('AdvancePayment');
            $this->set($report->getDataArray(), false);
            $this->set('jsonParams', $report->jsonAdapter(), false);
            $this->_settings();
        }

        $this->set('clients', $this->Invoice->Client->getClientsList($owner['id'], ['Client.id in (SELECT DISTINCT client_id FROM invoices )']));
        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }
        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, true);
        $paymentMethods['client_credit']='Client Credit';
        $this->set('payment_methods', $paymentMethods);
        $this->set('type',$type);
        $this->set('title_for_layout',  isset($this->viewVars["report_title"])? __($this->viewVars["report_title"], true) : (__('Payments', true) . ' - ' . __('Reports', true)));
    }

    private function report_permissions($report_name): bool
    {
        if (getAuthOwner('staff_id') == 0) {
            return true;
        }
        $permission = match ($report_name) {
            'expenses', 'incomes', => [Invoices_View_All_Invoices, View_His_Own_Reports, View_All_expenses],
            'expired_memberships', 'memberships_subscriptions', 'new_membership' => [PermissionUtil::View_His_Own_Reports],
            'client_aged_ledger', 'clients', 'client_balance', 'clients_sales', 'clients_payments', 'appointment', 'installments' => [VIEW_ALL_CLIENTS_REPORTS, VIEW_HIS_OWN_CLIENTS_REPORTS],
            'warehouse_stock_balance', 'product_average_cost', 'detailed_stock_transactions', 'inventory_requistion', 'purchase_requistion', 'bundle', 'tracking_products_by_lot_and_expiry_date', 'tracking_products_by_serial', 'tracking_products_by_lot', 'tracking_products_by_expiry_date' => [Track_Inventory],
            default => [View_His_Own_Reports]
        };
        return check_permission($permission);
    }



    private function isFinancialYearsSequential(array $financialYears): bool
    {
        $isSerial = true;
        usort($financialYears, function ($a, $b) {
            return strcmp($a['FinancialYear']['start_date'], $b['FinancialYear']['start_date']);
        });
        for ($i = 1; $i < count($financialYears); $i++) {
            $prev = $financialYears[$i - 1]['FinancialYear'];
            $curr = $financialYears[$i]['FinancialYear'];
            $expectedStart = (new DateTime($prev['end_date']))->modify('+1 day')->format('Y-m-d');
            if ($curr['start_date'] !== $expectedStart) {
                $isSerial = false;
            }
        }
        return $isSerial;
    }

    private function getFinancialYears($fyIds = [])
    {
        $this->loadModel('FinancialYear');
        $this->FinancialYear->applyBranch['onFind'] = false;
        $financialYears = $this->FinancialYear->find('all', ['fields' => ['DISTINCT FinancialYear.start_date,  FinancialYear.end_date'], 'conditions' => [' FinancialYear.id IN ( ' . implode(',', $fyIds) . ' )'], 'order' => ['FinancialYear.start_date' => 'ASC']]);
        $this->FinancialYear->applyBranch['onFind'] = true;
        return $financialYears;
    }
}
