<?php
class PaymentGatewaysController extends AppController {

    var $name = 'PaymentGateways';

    /**
     * @var SitePaymentGateway
     */
    var $SitePaymentGateway;
    var $helpers = array('Html', 'Form');
    var $uses = array('SitePaymentGateway');

    function owner_index() {
        $this->loadModel('SitePaymentGateway');
           $this->loadModel('Treasury');
            $this->loadModel('Tax');
            $this->set('taxes', $this->Tax->getTaxList());
           $this->set('treasuries',$this->Treasury->get_list());
           $this->set('primary_treasury',$this->Treasury->get_primary());
        $offlines = array('offline', 'bank', 'cash', 'cheque', 'credit','client_credit','paytabs');

        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_Payment_Options)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $owner_id = $this->_getOwnerId();
        $this->loadModel('InvoicePayment');
        $this->SitePaymentGateway->initiateGateways($owner_id,$site['country_code']);

        if (!empty($this->data)) {
            $data = array();
            $default = 0;   
            foreach ($this->data['SitePaymentGateway'] as $sitePG) {
                if(!isset($sitePG['active'])){
                    $sitePG['active']=false;
                }

                if (!empty($sitePG['payment_gateway'])) {
                    if ($sitePG['calculate_fees']) {
                        $sitePG['settings'] = json_encode([
                            'fixed_rate' => $sitePG['fixed_rate'] ?: 0,
                            'percentage_rate' => $sitePG['percentage_rate'] ?: 0,
                            'minimum_amount' => $sitePG['minimum_amount'] ?: 0,
                            'tax_id' => $sitePG['tax_id'] ?: 0
                        ]);
                    }
                    $data[]['SitePaymentGateway'] = $sitePG;
                }
                if (!empty($sitePG['default'])) {
                    $default = $sitePG['id'];
                }
            }

            if ($this->SitePaymentGateway->saveAll($data, array('validate' => false))) {

                NotificationV2::delete_notification(NotificationV2::NOTI_NO_ONLINE_PAYMENT_OPTIONS, null);

                if ($default) {
                    $this->SitePaymentGateway->updateAll(array('SitePaymentGateway.default' => 0), array('SitePaymentGateway.id !=' => $default));
                }
                $this->flashMessage(__('Your payment options have been updated', true), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(__('Could not save payment options', true));
            }
        } else {
            //	$conditions['SitePaymentGateway.site_id'] = $owner_id;
            $methods = array_keys(InvoicePayment::getPaymentMethods());
            $sitePaymentGateways = $this->SitePaymentGateway->find('all', array('recursive' => -1, 'order' => 'SitePaymentGateway.active DESC, SitePaymentGateway.default DESC, SitePaymentGateway.payment_gateway ASC , field(SitePaymentGateway.payment_gateway, ' . implode(', ', array_map('csv_quote', $methods)) . ')'));
            $this->data['SitePaymentGateway'] = array();
            $categorizedGateways = [];
            foreach ($sitePaymentGateways as $gway) {
                if (getCurrentSite('country_code') != "SA" && $gway['SitePaymentGateway']['payment_gateway'] == "tabby") {
                    continue;
                }
                if($gway['SitePaymentGateway']['payment_gateway'] == "paymob" && ((empty($gway['SitePaymentGateway']['username']) || empty($gway['SitePaymentGateway']['option1']) || empty($gway['SitePaymentGateway']['option2']) || empty($gway['SitePaymentGateway']['option3'])) && !$gway['SitePaymentGateway']['active'])){
                    continue;
                }
                $paymentGatewaySettings = $gway['SitePaymentGateway']['settings'] ? json_decode($gway['SitePaymentGateway']['settings'], true) : null;
                if ($paymentGatewaySettings) {
                    $gway['SitePaymentGateway']['calculate_fees'] = true;
                    $gway['SitePaymentGateway']['fixed_rate'] = $paymentGatewaySettings['fixed_rate'];
                    $gway['SitePaymentGateway']['percentage_rate'] = $paymentGatewaySettings['percentage_rate'];
                    $gway['SitePaymentGateway']['minimum_amount'] = $paymentGatewaySettings['minimum_amount'];
                    $gway['SitePaymentGateway']['tax_id'] = $paymentGatewaySettings['tax_id'];
                } else {
                    $gway['SitePaymentGateway']['calculate_fees'] = false;
                }
                $this->data['SitePaymentGateway'][] = $gway['SitePaymentGateway'];
                if(!isset($categorizedGateways[$gway['SitePaymentGateway']['category']])){
                    $categorizedGateways[$gway['SitePaymentGateway']['category']] = [];
                }
                $categorizedGateways[$gway['SitePaymentGateway']['category']][] = $gway['SitePaymentGateway'];
            }
        }

        $this->set('categorizedGateways', $categorizedGateways);
        $this->set('gateways', $pms = InvoicePayment::getPaymentMethods());
        $this->set('gatewaysData', $pms = InvoicePayment::AdvancedPaymentMethods());

        foreach ($pms as $method => $label) {
            $snippets[$method] = str_replace('{$subdomain}', 'https://' . $site['subdomain'] . '/', $this->get_snippet('payment-' . $method));
        }
        $this->set('payment_snippets', $snippets);

        $manually_gateways = $this->SitePaymentGateway->find('all', array('conditions' => array('manually_added' => 1), 'order' => array('label')));
        $this->set('manually_gateways', $manually_gateways);



        $this->set('content', $this->get_snippet('payment-gateways'));

        if ($this->params['prefix'] == 'owner') {
            $this->crumbs = array();
            $this->crumbs[0]['title'] = __('Settings', true);
            $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');

            $this->crumbs[1]['title'] = __('Payment Options', true);
            $this->crumbs[1]['url'] = '#';
        }
        $this->set('title_for_layout',  __('Payment Methods', true));
        $this->set('offlines', $offlines);
    }

    function _getOwnerId() {
        if ($this->params['prefix'] == 'admin') {
            return 0;
        }

        return getAuthOwner('id');
    }

    //<editor-fold defaultstate="collapsed" desc="Old unneeded functions">
    function owner_add() {

        if (!check_permission(Edit_Payment_Options)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('/'));
        }
          $this->loadModel('Treasury');
           $this->set('treasuries',$this->Treasury->get_list());
        $this->loadModel('Tax');
        $this->set('taxes', $this->Tax->getTaxList());
        if (!empty($this->data)) {
            if(isset($_GET['ajax']) && (!isset($this->data['SitePaymentGateway']['label']) || trim($this->data['SitePaymentGateway']['label']) == "")) {
                die(json_encode(array('message' => __('Please add a label for the gateway', true),'code' => -1 )));
            }
            $isArabic = false;
            $this->SitePaymentGateway->create();
            $this->data['SitePaymentGateway']['site_id'] = getAuthOwner('id');
            $this->data['SitePaymentGateway']['manually_added'] = 1;
            if(!preg_match('/[^A-Za-z0-9]/', $this->data['SitePaymentGateway']['payment_gateway'])){
                if(is_arabic($this->data['SitePaymentGateway']['label'])) {
                    $uniqueId = uniqid();
                    $isArabic = true;
                    $this->data['SitePaymentGateway']['payment_gateway'] = $uniqueId;
                } else {
                    $this->data['SitePaymentGateway']['payment_gateway'] = $this->data['SitePaymentGateway']['label'];
                }
            }else{
            $this->data['SitePaymentGateway']['payment_gateway'] = Inflector::slug(strtolower($this->data['SitePaymentGateway']['label']));
            }



            if ($this->data['SitePaymentGateway']['calculate_fees']) {
                $this->data['SitePaymentGateway']['settings'] = json_encode([
                    'fixed_rate' => $this->data['SitePaymentGateway']['fixed_rate'] ?: 0,
                    'percentage_rate' => $this->data['SitePaymentGateway']['percentage_rate'] ?: 0,
                    'minimum_amount' => $this->data['SitePaymentGateway']['minimum_amount'] ?: 0,
                    'tax_id' => $this->data['SitePaymentGateway']['tax_id'] ?: 0,

                ]);
            } else {
                $this->data['SitePaymentGateway']['settings'] = NULL;
            }
            $this->data['SitePaymentGateway']['category'] = PAYMENT_CUSTOM_CATEGORY; 
            $methods = InvoicePayment::getPaymentMethods();
            $hm = $this->SitePaymentGateway->findByLabel($this->data['SitePaymentGateway']['label']);
            if (!empty($hm)) {
                if(isset($_GET['ajax'])) {
                    die(json_encode(array('message' => sprintf(__('Error: %s is already added before (%s)', true), $hm['SitePaymentGateway']['label'], $hm['SitePaymentGateway']['label']),'code' => -1 )));
                }
                $this->flashMessage(sprintf(__('Error: %s is already added before (%s)', true), $hm['SitePaymentGateway']['label'], $hm['SitePaymentGateway']['label']));
            }elseif(in_array(strtolower($this->data['SitePaymentGateway']['label']), array_map('strtolower', $methods)) || in_array(strtolower($this->data['SitePaymentGateway']['label']), array_map('strtolower', array_keys($methods)))){
                if(isset($_GET['ajax'])) {
                    die(json_encode(array('message' => __('System Reserved Payment Method, you can add a prefix or suffix to it', true),'code' => -1 )));
                }
                $this->flashMessage(__('System Reserved Payment Method, you can add a prefix or suffix to it', true));
            } else if ($this->SitePaymentGateway->save($this->data, array('validate' => false))) {
                if($isArabic) {
                    $this->SitePaymentGateway->saveField('payment_gateway', 'manual_payment_'.$this->SitePaymentGateway->id, ['callbacks' => false]);
                }
                if(isset($_GET['ajax'])) {
                    $sitePaymentGateway = $this->SitePaymentGateway->findById($this->SitePaymentGateway->id);
                    $sitePaymentGateways = $this->SitePaymentGateway->find('all', array('recursive' => -1, 'order' => 'SitePaymentGateway.active DESC, SitePaymentGateway.default DESC, field(SitePaymentGateway.payment_gateway, ' . implode(', ', array_map('csv_quote', array_keys($methods))) . ')'));
                    $categorizedGateways = [];
                    foreach ($sitePaymentGateways as $gway) {
                        if(!isset($categorizedGateways[$gway['SitePaymentGateway']['category']])){
                            $categorizedGateways[$gway['SitePaymentGateway']['category']] = [];
                        }
                        $categorizedGateways[$gway['SitePaymentGateway']['category']][] = $gway['SitePaymentGateway'];
                    }
                    die(json_encode(array('message' => sprintf(__('%s has been saved', true), __('Payment Gateway', true)),'customTitle' => __('Custom Payment Methods', true),'categories' => array_keys($categorizedGateways),'payment_gateway' => $sitePaymentGateway['SitePaymentGateway'],'code' => 1 )));
                }
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Payment Gateway', true)), 'Sucmessage');
                $redirect = array('controller' => 'payment_gateways', 'action' => 'index');
                $this->redirect($redirect);
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Payment Gateway', true)));
            }
        } else {
            $this->data['SitePaymentGateway']['active'] = true;
        }


        $this->set('title_for_layout',  __('Add Payment Method', true));
    }

    function owner_edit($id = null) {

        if (!check_permission(Edit_Payment_Options)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('/'));
        }

            $this->loadModel('Treasury');
           $this->set('treasuries',$this->Treasury->get_list());
        $this->loadModel('Tax');
        $this->set('taxes', $this->Tax->getTaxList());
        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');
        $SitePaymentGateway = $this->SitePaymentGateway->findById($id);
        if (!$SitePaymentGateway || empty($SitePaymentGateway['SitePaymentGateway']['manually_added'])) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Payment Gateway', true)));
            $this->redirect($this->referer(array('controller' => 'payment_gateways', 'action' => 'index')));
        }


        if (!empty($this->data)) {
            unset($this->data['SitePaymentGateway']['manually_added']);
            unset($this->data['SitePaymentGateway']['payment_gateway']);
            if ($this->data['SitePaymentGateway']['calculate_fees']) {
                $this->data['SitePaymentGateway']['settings'] = json_encode([
                    'fixed_rate' => $this->data['SitePaymentGateway']['fixed_rate'] ?: 0,
                    'percentage_rate' => $this->data['SitePaymentGateway']['percentage_rate'] ?: 0,
                    'minimum_amount' => $this->data['SitePaymentGateway']['minimum_amount'] ?: 0,
                    'tax_id' => $this->data['SitePaymentGateway']['tax_id'] ?: 0
                ]);
            } else {
                $this->data['SitePaymentGateway']['settings'] = NULL;
            }

            $hm = $this->SitePaymentGateway->find('first',['conditions'=>['SitePaymentGateway.id !='=>$id,'SitePaymentGateway.label'=>$this->data['SitePaymentGateway']['label']]]);
            if (!empty($hm)) {
                $this->flashMessage(sprintf(__('Error: %s is already added before (%s)', true), $hm['SitePaymentGateway']['label'], $hm['SitePaymentGateway']['label']));
            }else {
                if ($this->SitePaymentGateway->save($this->data, array('validate' => false))) {
                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Payment Gateway', true)), 'Sucmessage');
                    $this->redirect(array('controller' => 'payment_gateways', 'action' => 'index'));
                } else {
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Payment Gateway', true)));
                }
            }
        } else {
            $paymentGatewaySettings = $SitePaymentGateway['SitePaymentGateway']['settings'] ? json_decode($SitePaymentGateway['SitePaymentGateway']['settings'], true) : null;
            if ($paymentGatewaySettings) {
                $SitePaymentGateway['SitePaymentGateway']['calculate_fees'] = true;
                $SitePaymentGateway['SitePaymentGateway']['fixed_rate'] = $paymentGatewaySettings['fixed_rate'];
                $SitePaymentGateway['SitePaymentGateway']['percentage_rate'] = $paymentGatewaySettings['percentage_rate'];
                $SitePaymentGateway['SitePaymentGateway']['minimum_amount'] = $paymentGatewaySettings['minimum_amount'];
                $SitePaymentGateway['SitePaymentGateway']['tax_id'] = $paymentGatewaySettings['tax_id'];
            } else {
                $SitePaymentGateway['SitePaymentGateway']['calculate_fees'] = false;
            }
            $this->data = $SitePaymentGateway;
        }


        $this->set('title_for_layout',  h(sprintf(__('%s - Payment Methods', true), $SitePaymentGateway['SitePaymentGateway']['label'])));

        $this->render('owner_add');
    }

    function owner_delete($id = null) {

        if (!check_permission(Edit_Payment_Options)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('/'));
        }

        if (empty($id)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('Payment Method', true)));
            $this->redirect(array('controller' => 'payment_gateways', 'action' => 'index'));
        }

        $conditions = array();

        $conditions['SitePaymentGateway.id'] = $id;

        $SitePaymentGateway = $this->SitePaymentGateway->find('first', array('conditions' => $conditions));
        if (empty($SitePaymentGateway) || empty($SitePaymentGateway['SitePaymentGateway']['manually_added'])) {
            $this->flashMessage(sprintf(__('%s not found', true), __('Payment Method', true)));
            $this->redirect($this->referer(array('controller' => 'payment_gateways', 'action' => 'index'), true));
        }

        $this->loadModel('InvoicePayment');
        $this->loadModel('PurchaseOrderPayment');
        $invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['InvoicePayment.payment_method' => $SitePaymentGateway['SitePaymentGateway']['payment_gateway']]]);
        if($invoicePayment) {
            if(isset($_GET['ajax'])) {
                die(json_encode(array('message' => __("You cannot delete the payment gateway as it's already selected in the system transactions and you can deactivate it instead of the delete.", true),'id' => $id,'code' => 0 )));
            }
            $this->flashMessage(__("You cannot delete the payment gateway as it's already selected in the system transactions and you can deactivate it instead of the delete.", true));
            $this->redirect(array('controller' => 'payment_gateways', 'action' => 'index'));
        }
        $purchaseOrderPayment = $this->PurchaseOrderPayment->find('first', ['conditions' => ['InvoicePayment.payment_method' => $SitePaymentGateway['SitePaymentGateway']['payment_gateway']]]);
        if($purchaseOrderPayment) {
            if(isset($_GET['ajax'])) {
                die(json_encode(array('message' => __("You cannot delete the payment gateway as it's already selected in the system transactions and you can deactivate it instead of the delete.", true),'id' => $id,'code' => 0 )));
            }
            $this->flashMessage(__("You cannot delete the payment gateway as it's already selected in the system transactions and you can deactivate it instead of the delete.", true));
            $this->redirect(array('controller' => 'payment_gateways', 'action' => 'index'));
        }
        if (!empty($id) && $this->SitePaymentGateway->deleteAll($conditions)) {

            if(isset($_GET['ajax'])) {
                die(json_encode(array('message' => sprintf(__('%s been deleted', true), __('Payment Method', true)),'id' => $id,'code' => 1 )));
            }

            $this->flashMessage(sprintf(__('%s been deleted', true), __('Payment Method', true)), 'Sucmessage');
            $this->redirect(array('controller' => 'payment_gateways', 'action' => 'index'));
        } else {
            $this->flashMessage(sprintf(__('%s not found', true), __('Payment Method', true)));
            $this->redirect(array('controller' => 'payment_gateways', 'action' => 'index'));
        }

        $this->redirect(array('controller' => 'payment_gateways', 'action' => 'index'));
    }

    //------------------------
    function owner_add_payment() {
        $this->layout = false;

        if (!empty($this->data)) {
            $this->data['SitePaymentGateway']['site_id'] = getAuthOwner('id');
            $this->SitePaymentGateway->create();
            if ($this->SitePaymentGateway->save($this->data)) {
                $this->flashMessage(sprintf(__('The %s  has been saved', true), __('site payment gateway', true)), 'Sucmessage');
                $this->data['SitePaymentGateway']['id'] = $this->SitePaymentGateway->getLastInsertID();
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('site payment gateway', true)));
            }
        }
    }

    //</editor-fold>

    function owner_set_default($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', 'site payment gateway', true)));
            $this->redirect(array('action' => 'index'));
        }
        $data['SitePaymentGateway']['id'] = $id;
        $data['SitePaymentGateway']['default'] = 1;
        if ($this->SitePaymentGateway->updateAll(array('default' => 0), array('site_id' => getAuthOwner('id'))) && $this->SitePaymentGateway->save($data)) {
            $this->flashMessage(sprintf(__('The %s  has been updated', true), __('site payment gateway', true)), 'Sucmessage');
            $this->redirect(array('action' => 'index'));
        } else {
            $this->flashMessage(sprintf(__('The %s could not be updated. Please, try again', true), __('site payment gateway', true)));
        }
    }

    //------------------------
    function beforeFilter() {
        parent::beforeFilter();
        $this->modelName = "SitePaymentGateway";
    }

    public function owner_validate_paytabs_credentials()
    {
        App::import('Vendor', 'PayTabsPayment', array('file' => 'payments/PayTabsPayment.php'));
        $merchant_email = isset($_POST['merchant_email']) ? $_POST['merchant_email']:null;
        $secret_key = isset($_POST['secret_key']) ? $_POST['secret_key']:null;

        if ($merchant_email && $secret_key) {
            if (PayTabsPayment::verifyCredentials($merchant_email, $secret_key))
                die(json_encode(['status' => true, 'valid' => true]));
            else
                die(json_encode(['status' => true, 'valid' => false]));
        } else {
            die(json_encode(['status' => false]));
        }
    }


    function get_snippet($name, $language_id = false, $close = true)
    {
        if (empty($language_id)) {
            $language_id = 41;
            if (getCurrentSite('language_code')) {
                $language_id = getCurrentSite('language_code');
            }
            if (!empty(getAuthStaff()) ) {
                $language_id = getAuthStaff('language_code');
            }
        }
        if(empty($language_id)) $language_id=41;

        $this->loadModel('Snippet');
        $conditions['Snippet.name'] = $name;
        $conditions[] = "(Snippet.language_id='$language_id' OR Snippet.language_id='41')"; //array('OR'=>array('Snippet.language_id'=>$language_id),array('Snippet.language_id'=>41));

        $owner_id = getAuthOwner('id');

        if (!empty($owner_id) && !empty($this->params['prefix']) && $this->params['prefix'] == 'owner' && $close) {
            $this->loadModel('SnippetsUser');

            $snippetUsers = $this->SnippetsUser->find('first', [
                'conditions' => ['SnippetsUser.snippet_name' => $name, 'SnippetsUser.user_id' => $owner_id],
                'fields' => ['snippet_name']
            ]);

            if ($snippetUsers) {
                $conditions[] = ['Snippet.name != ' => $name]; 
            }

        }

        $snippet = $this->Snippet->find('first', array('conditions' => $conditions, 'order' => "FIELD(Snippet.language_id,$language_id,41)"));
        // warning suppress
        $content = $snippet['Snippet']['content'] ?? null;
        if (!empty($content)) {
            $content = "<div class='payment_modal-snippet'>
            <div class='payment_modal-title d-flex align-items-center justify-content-between'>
                <span class='payment_modal-close'><i class='mdi mdi-close-circle-outline fs-28'></i></span>
            </div>{$content}</div>";
            $this->set('snippet_top', $name);
        }

        return $content;
    }

    public function owner_get_templates($gateway) 
    {
        $this->loadModel('Tax');
        $this->set('taxes', $this->Tax->getTaxList());
        $this->loadModel('Treasury');
        $offlines = array('offline', 'bank', 'cash', 'cheque', 'credit','client_credit','paytabs');
        $site = getAuthOwner();
        $conditions['SitePaymentGateway.payment_gateway'] = $gateway;
        $sitePaymentGateway = $this->SitePaymentGateway->find('first', array('conditions' => $conditions, 'recursive' => -1, 'order' => 'SitePaymentGateway.active DESC'));
        $paymentGatewaySettings = $sitePaymentGateway['SitePaymentGateway']['settings'] ? json_decode($sitePaymentGateway['SitePaymentGateway']['settings'], true) : null;
        if ($paymentGatewaySettings) {
            $sitePaymentGateway['SitePaymentGateway']['calculate_fees'] = true;
            $sitePaymentGateway['SitePaymentGateway']['fixed_rate'] = $paymentGatewaySettings['fixed_rate'];
            $sitePaymentGateway['SitePaymentGateway']['percentage_rate'] = $paymentGatewaySettings['percentage_rate'];
            $sitePaymentGateway['SitePaymentGateway']['minimum_amount'] = $paymentGatewaySettings['minimum_amount'];
            $sitePaymentGateway['SitePaymentGateway']['tax_id'] = $paymentGatewaySettings['tax_id'];
            $sitePaymentGateway['SitePaymentGateway']['country'] = $paymentGatewaySettings['country'];
        } else {
            $sitePaymentGateway['SitePaymentGateway']['calculate_fees'] = false;
        }
        $this->data = $sitePaymentGateway;
        $is_custom = $sitePaymentGateway['SitePaymentGateway']['category'] == PAYMENT_CUSTOM_CATEGORY ? true : false;
        $this->set('CUSTOM_PAYMENT_GATEWAY', $is_custom);
        $this->set('treasuries',$this->Treasury->get_list());
        $this->set('sitePaymentGateway', $sitePaymentGateway);
        $this->set('offlines', $offlines);
        $pms = InvoicePayment::AdvancedPaymentMethods();
        foreach ($pms as $method => $label) {
            $snippets[$method] = str_replace('{$subdomain}', 'https://' . $site['subdomain'] . '/', $this->get_snippet('payment-' . $method));
        }

        $this->set('payment_snippets', $snippets);

        if($is_custom) {
            $this->params['url']['box'] = true;
            $id = $sitePaymentGateway['SitePaymentGateway']['id'];
            $this->set('url',array('controller'=>'payment_gateways', 'action' => 'owner_update_payment_gateway_v2', $id));
            $template = $this->render('owner_add');
        } else {
            $template = $this->render('owner_gateway_template');
        }

        die($template);
    }

    public function owner_update_payment_gateway_v2($id=null){
        $data = [];
        if(!isset($this->data['SitePaymentGateway'])) {
           die(json_encode(['message' => 'invalid payment gateway', 'code' => -1]));
        }

        $payment_gateway = $this->data['SitePaymentGateway'];
        $paymentClass = getPaymentClass($payment_gateway['payment_gateway']);

        unset($payment_gateway['manually_added']);
        unset($payment_gateway['payment_gateway']);
        if ($payment_gateway['calculate_fees'] || $payment_gateway['country']) {
            $settings = [
                'fixed_rate' => $payment_gateway['fixed_rate'] ?: 0,
                'percentage_rate' => $payment_gateway['percentage_rate'] ?: 0,
                'minimum_amount' => $payment_gateway['minimum_amount'] ?: 0,
                'tax_id' => $payment_gateway['tax_id'] ?: 0
            ];
            if($payment_gateway['country']){
                $settings['country'] = $payment_gateway['country'];
            }
            $payment_gateway['settings'] = json_encode($settings);
        } else {
            $payment_gateway['settings'] = NULL;
        }
        if(class_exists($paymentClass)) {
            $paymentMethod = new $paymentClass;
            if (method_exists($paymentMethod, 'beforeSettingsSaving')) {
                $webhook = $paymentMethod->beforeSettingsSaving($payment_gateway);
                if ($webhook) {
                    $optionKey = $webhook['option_key'] ?? 'option2';
                    $payment_gateway[$optionKey] = $webhook['webhook_id'] ?? null;
                }
            }
        }
        if ($this->SitePaymentGateway->save(['SitePaymentGateway' => $payment_gateway], array('validate' => false))) {
          $data['message'] = __('Payment gateway has been updated', true);
        } else {
            $data['message'] = __('Payment gateway couldn\'t be updated', true);
        }

        die(json_encode($data));
    }
}

