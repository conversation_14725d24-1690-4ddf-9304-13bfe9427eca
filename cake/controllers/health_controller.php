<?php

class HealthController extends AppController {
    var $uses = [];

    function index() {
        session_destroy();
        echo 'ok';
        die();
    }

    public function healthz()
    {
        $serviceStatuses = [];
        $this->layout = '';

        $serviceStatuses['Apache'] = 'Operational';
        $serviceStatuses['Portal DB'] = $this->checkPortalDb() ? 'Operational' : 'Unavailable';
        $serviceStatuses['System DB'] = $this->checkSystemDb() ? 'Operational' : 'Unavailable';
        $serviceStatuses['Cake Core Cache'] = $this->checkCakeCoreCache() ? 'Operational' : 'Unavailable';
        $serviceStatuses['Default cache'] = $this->checkDefaultCache() ? 'Operational' : 'Unavailable';
        $serviceStatuses['Redis'] = $this->checkRedisSession() ? 'Operational' : 'Unavailable';

        $isOperational = array_reduce($serviceStatuses, function ($carry, $status) {
            return $carry && $status === 'Operational';
        }, true);

        if (!$isOperational) {
            http_response_code(500);
        }

        $this->set('serviceStatuses', $serviceStatuses);
    }

    private function checkPortalDb()
    {
        return ConnectionManager::getDataSource('portal')->connect();
    }

    private function checkSystemDb()
    {
        return ConnectionManager::getDataSource('default')->connect();
    }

    private function checkCakeCoreCache()
    {
        $writeKey = time();
        Cache::write($writeKey, 'test', '_cake_core_');
        $result = Cache::read($writeKey, '_cake_core_') == 'test';
        Cache::delete($writeKey, '_cake_core_');
        return $result;

    }

    private function checkDefaultCache()
    {
        $writeKey = time();
        Cache::write($writeKey, 'test');
        $result = Cache::read($writeKey) == 'test';
        Cache::delete($writeKey);
        return $result;
    }

    private function checkRedisSession()
    {
        try {
            $parsedUrl = parse_url(REDIS_SERVER);
            $finalServerUrl = $parsedUrl['scheme']. '://' . $parsedUrl['host'];
            parse_str($parsedUrl['query'], $query);
            $redis = new \Redis();
            if ($redis->connect($finalServerUrl, 6379)) {
                if (isset($query['password']) || isset($query['auth'])) {
                    $redis->auth($query['password'] ?? $query['auth']);
                }
                $redis->ping();
                return true;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }
}