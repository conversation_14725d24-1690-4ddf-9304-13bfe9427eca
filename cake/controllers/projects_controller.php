<?php

class ProjectsController extends AppController {

    var $name = 'Projects';

    function owner_index($no_layout = null ) {
	$this->layout= "box" ;
        $site = getAuthOwner();
		
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_Delete_Projects)&&!check_permission(Add_New_Project)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->Project->recursive = 0;
        $conditions = $this->_filter_params();
       $this->paginate['Project'] = array('conditions' => $conditions,
            'order' => array('Project.name asc'),
        );

        $this->set('projects', $this->paginate());
        $this->set('content', $this->get_snippet('projects'));
        $this->set('title_for_layout',  h(__('Projects', true)));
    }

    function owner_add() {
		$this->layout= "box" ;
        if (!check_permission(Add_New_Project)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $staff_project_rates=[];
        if (!empty($this->data)) {
          
          
            $this->Project->create();
			$this->loadModel ('StaffProjectHourlyRate');
            $this->data['Project']['staff_id'] = getAuthOwner('staff_id');

            if (isset($this->data['StaffProjectHourlyRate'])) {
                // when validations error staff_project_rates doesn't have value 
                $staff_project_rates = array_map(function ($element) {
                    return ['StaffProjectHourlyRate' => $element];
                }, $this->data['StaffProjectHourlyRate']);
            }

            if ($this->Project->save($this->data)) {
				
				foreach ($this->data['StaffProjectHourlyRate'] as $k => $v ){
					$this->data['StaffProjectHourlyRate'][$k]['project_id'] = $this->Project->id;
				}
            if(is_countable($this->data['StaffProjectHourlyRate']) && count($this->data['StaffProjectHourlyRate'])>0){
				//print_r ( $this->data['StaffProjectHourlyRate']) ; die ; 
               
				$this->StaffProjectHourlyRate->saveAll($this->data['StaffProjectHourlyRate'] );
            }
                                           
                $this->add_actionline(ACTION_ADD_PROJECT, array('primary_id' => $this->Project->id,'param1'=>$this->data['Project']['name'],'param2'=>$this->data['Project']['status']));                
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Project', true)), 'Sucmessage');
                $redirect = array('action' => 'index');
                if (!empty($this->params['url']['box'])) {
                    $redirect = array('action' => 'add', '?' => array('box' => 1, 'success' => 1));
                }
                $this->redirect($redirect);
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Project', true)));
            }
        }
		else
		{
			$this->data['Project']['status'] = true;
		}

        $this->set ('staff_project_rates' , $staff_project_rates );

		$this->loadModel('Staff' ) ; 
		$this->set ('staff_ids',$this->Staff->getList () ) ; 
        $this->set('title_for_layout',  __('Add Project', true));
    }

    function owner_edit($id = null	, $no_layout = null ) {
		/*f ( $no_layout == 'no_layout' || $_GET['box'] == 1){
			$this->layout= "box" ;
		}*/
        $this->layout= "box" ;
		$this->set ( 'no_layout' , $no_layout ) ;
		$this->loadModel ('StaffProjectHourlyRate');
        if (!check_permission(Edit_Delete_Projects)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');
        $project = $this->Project->find(array('Project.id' => $id));
        if (!$project) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Project', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }


		$staff_project_rates = $this->StaffProjectHourlyRate->find ('all' , ['conditions' => ['project_id' => $id]]);
        if (!empty($this->data)) {
            $this->data['Project']['id'] = $id;
			
            foreach  ( $staff_project_rates as  $v ) {
                    if ( !in_array (  $v['StaffProjectHourlyRate']['id'] ,array_column($this->data['StaffProjectHourlyRate'] ,'id' )) ){
                            $this->StaffProjectHourlyRate->delete ( $v['StaffProjectHourlyRate']['id'] );
                    }
            }
            $staffs =[];
            foreach ($this->data['StaffProjectHourlyRate'] as $k => $v ){
                    $this->data['StaffProjectHourlyRate'][$k]['project_id'] = $id;
                    $staffs[$this->data['StaffProjectHourlyRate'][$k]['staff_id']] = $this->data['StaffProjectHourlyRate'][$k]['hourly_rate'];
            }
            //print_r ( $this->data['StaffProjectHourlyRate']) ; die ; 
  if(is_countable($this->data['StaffProjectHourlyRate']) && count($this->data['StaffProjectHourlyRate'])>0){            
            $this->StaffProjectHourlyRate->saveAll($this->data['StaffProjectHourlyRate'] );
  }
            if ($this->Project->save($this->data)) {
                $this->loadModel ( 'TimeTracking');
                foreach ( $staffs as $s => $h)
                {
                    $allTimes = $this->TimeTracking->get_time_records ( $s , $id );
                    
                    foreach ( $allTimes as $t ) {
                        $this->TimeTracking->update_time_records ( $s , $h , $id );//$hourly_rate );
                    }
                }
                
            $this->add_actionline(ACTION_EDIT_PROJECT, array('primary_id' => $this->Project->id,'param1'=>$this->data['Project']['name'],'param2'=>$this->data['Project']['status']));                                
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Project', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index' , ($no_layout == 'no_layout' || $_GET['box'] == 1)?'no_layout' : "" ));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Project', true)));
            }
        } else {
            $this->data = $project;
        }
        $this->set('title_for_layout',  h(sprintf(__('%s - Project', true), $project['Project']['name'])));
		$this->loadModel ( 'Staff' );
		$staff_project_rates = $this->StaffProjectHourlyRate->find ('all' , ['conditions' => ['project_id' => $id]]);//$this->Staff->query ("select * from staff_project_hourly_rates where project_id = '$id' " );
		//print_r ( $staff_project_rates ) ; die ; 
		//debug ( $staff_project_rates );
        $this->set ('staff_project_rates' , $staff_project_rates );
		
		$this->set ('staff_ids',$this->Staff->getList () ) ; 
        $this->render('owner_add');
    }

    function owner_delete($id = null, $no_layout = null) {
		/*if ( $no_layout == 'no_layout' || $_GET['box'] == 1){
			$this->layout= "box" ;
		}*/
		$this->layout= "box" ;
        $owner = getAuthOwner();
        if (!check_permission(Edit_Delete_Projects)) {
            $this->flashMessage(__("You are not allowed to delete projects", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('project', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('project', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) { //php8 fix
            $verb = __('have', true);
            $module_name = __('projects', true);
        }
        $conditions = array();
        
        $conditions['Project.id'] = $id;
        $projects = $this->Project->find('all', array('conditions' => $conditions));
        if (empty($projects)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->Project->deleteAll($conditions)) {
             foreach($projects as $project){
            $this->add_actionline(ACTION_DELETE_PROJECT, array('primary_id' => $project['Project']['id'],'param1'=>$project['Project']['name'],'param2'=>$project['Project']['status']));                                 
             }                
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                $this->redirect(array('action' => 'index' ,($no_layout == 'no_layout')?'no_layout' : "" ));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  h(__('Delete Projects', true)));

        $this->set('projects', $projects);
        $this->set('module_name', $module_name);
    }

}

?>