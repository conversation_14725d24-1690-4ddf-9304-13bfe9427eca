<?php


use App\Repositories\LimitationRepository;
use Izam\Limitation\Utils\LimitationUtil;

class DesktopApplicationController extends AppController
{
    var $uses = [];

    public function owner_view()
    {
        // check for pos terminals limitation pos_terminals
        $limitation_repo = new LimitationRepository();
        if (!$limitation_repo->checkSiteHasLimitation(LimitationUtil::POS_TERMINALS)) {
            $_SESSION['limit_exceeded'] = [
                'status' => true,
                'title' => __('Offline POS Terminals', true),
                'message' => sprintf(__("Your current plan doesn't support adding more %s", true), __('Offline POS Terminals', true)),
                'upgrade_url' => "/owner/sites/increase_limit/pos_terminals",
                'upgrade_btn_title' => __('Upgrade Now', true),
            ];
            $this->redirect('/v2/owner/pos/settings');
        }
    }
}
