<?php

use Izam\Daftra\Portal\Services\SnippetService;

/**
 * @property Snippet Snippet
 * @var Snippet
 */
class SnippetsController extends AppController {

    var $name = 'Snippets';
    var $helpers = array('Html', 'Form','Fck' , 'Mixed');

	var $Snippet;

    function admin_index() {
        $conditions=$this->_filter_params();
        $snippets=array();


		if(!empty ($this->data['Snippet']['language_id']) && !empty ($this->data['Snippet']['snippet_name'])){
			$this->redirect("/admin/snippets/edit/{$this->data['Snippet']['snippet_name']}/{$this->data['Snippet']['language_id']}");
		}

        foreach($this->Snippet->snippetss as $snippet) {
            $snippets[]=$snippet['title'];
        }
        $list_snippets= $this->paginate('Snippet',$conditions);

        $this->set('list_snippets', $list_snippets);
        $this->loadModel('Language');
		$this->set('languages',$this->Language->getLanguageList());
		$this->set('snippets',$snippets);
    }

//----------------------
    function admin_edit($name= null,$language_id= null) {

        if ((empty ($name) || empty ($language_id)) ) {
            $this->flashMessage(__('Invalid Snippet', true));
            $this->redirect(array('action'=>'index'));
        }

        if (!empty($this->data)) {
			$this->data['Snippet']['language_id']=$language_id;
            if (SnippetService::updateById($this->data['Snippet']['id'], $this->data['Snippet'])) {
                $this->flashMessage(__('Snippet has been saved', true), 'Sucmessage');
                $this->redirect(array('action'=>'index'));
            } else {
                $this->flashMessage(__('Snippet could not be saved. Please, try again.', true));
            }
        } else {
			$snippet = $this->Snippet->find('first',array('conditions'=>array('Snippet.name' => $name, 'Snippet.language_id'=>$language_id)));
			if ($snippet){
				$this->data = $snippet;
			} else {
				$this->data['Snippet']['language_id'] = $language_id;
				$this->data['Snippet']['name'] = $name;
				$this->data['Snippet']['content'] = '';
			}
//            $id= $this->Snippet->field('id', array('Snippet.name'=>$name,'Snippet.language_id'=>$language_id));
//            //if this snippet does not exist in the DB , Add it
//            if(empty ($id)) {
//                if(!$this->Snippet->save(array('Snippet'=>array('name'=>$name,'language_id'=>$language_id)))) {
//                    $this->flashMessage(__('System Error, please contact the admin', true));
//                    $this->redirect(array('action'=>'index'));
//                }
//                else {
//                    $id=$this->Snippet->id;
//                }
//            }
//            $this->data = $this->Snippet->find('first',array('conditions'=>array('Snippet.id'=>$id,'Snippet.language_id'=>$language_id)));
        }
        /*$this->set('advanced_editor',
            isset($this->Snippet->snippetss[$name]['advanced_editor'])&&!$this->Snippet->snippetss[$name]['advanced_editor']?false:true );
            $this->set('snippet_properties',$this->Snippet->snippetss[$name]);*/
		$this->set('language_id',$language_id);
		$this->set('name',$name);
        $this->render('admin_add');
    }
//------------------------------
    function admin_delete($id = null) {
        if (!$id) {
            $this->flashMessage(__('Invalid snippet', true));
            $this->redirect(array('action'=>'index'));
        }
        if (SnippetService::deleteWhere(['id' => $id])) {
            $this->flashMessage(__('Snippet has been deleted', true), 'Sucmessage');
            $this->redirect(array('action'=>'index'));
        }else{
            $this->flashMessage(__('Could not delete snippet', true));
            $this->redirect(array('action'=>'index'));
        }
    }
	//---------------------------------
    function admin_delete_multi() {
        if (empty($_POST['ids'])||!is_array($_POST['ids'])) {
            $this->flashMessage(__('Invalid snippets', true));
            $this->redirect(array('action'=>'index'));
        }
        if (SnippetService::deleteWhere(['id' => $_POST['ids']])) {
            $this->flashMessage(__('Snippets have been deleted', true), 'Sucmessage');
            $this->redirect(array('action'=>'index'));
        }
        else {
            $this->flashMessage(__('Could not delete snippets', true));
            $this->redirect(array('action'=>'index'));
        }
    }
	
	
	function owner_test($page)
	{
		
		$this->render($page );
	}
	//---------------------------
	function admin_get_snippets($language_id= null){
		Configure::write('debug',0);
		$this->layout=$this->autoRender=false;

		$snippets= $this->Snippet->find('list',array('conditions'=>array('language_id'=>$language_id),'order'=>'Snippet.name'));

		$org_snippets=array();
		foreach($this->Snippet->snippetss as $snippet){
			if(!in_array($snippet['name'], $snippets)){
				$org_snippets[]=array('name'=>$snippet['name'],'title'=>$this->Snippet->getSnippetTitle($snippet['name']));
			}
		}
		echo json_encode($org_snippets);
	}

    function dismiss_snippet()
    {
        $this->data = $_POST;
        if (!empty($this->data)) {
            $this->loadModel('SnippetsUser');
            $snippet_key = $this->data['key'];
            $user_id = getAuthOwner('staff_id');
            $record = $this->SnippetsUser->find('list', array('conditions' => array('SnippetsUser.snippet_name' => $snippet_key, 'SnippetsUser.user_id' => $user_id)));
            if (!$record) {
                if ($this->SnippetsUser->save(array('snippet_name' => $snippet_key, 'user_id' => $user_id), false))
                    die(json_encode(array('status' => true, 'result' => 'dismissed')));
                else
                    die(json_encode(array('status' => false, 'result' => 'failed')));
            } else {
                die(json_encode(array('status' => true, 'result' => 'found')));
            }
        } else {
            die(json_encode(array('status' => false, 'result' => 'key missing')));
        }
    }
}
?>