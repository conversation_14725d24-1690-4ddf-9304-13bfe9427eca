<?php

class DefaultSystemEmailsController extends <PERSON>pp<PERSON>ontroller {

	var $name = 'DefaultSystemEmails';

	/**
	 * @var DefaultSystemEmail
	 */
	var $DefaultSystemEmail;
	var $helpers = array('Html', 'Form');

	function admin_index() {

		$this->set('emails', $this->DefaultSystemEmail->getDefaultEmails());
	}

	function admin_edit($type, $language_id) {
		$defaultEmail = DefaultSystemEmail::getDefaultSpecs();
		$type = low($type);

		if (!isset($defaultEmail[$type])){
			$this->flashMessage(__('Message not found', true));
			$this->redirect(array('action' => 'index'));
		}

		if (!empty($this->data)) {
            $this->data['DefaultSystemEmail']['type'] = $type;
            $this->data['DefaultSystemEmail']['language_id'] = $language_id;
			if (\Izam\Daftra\Portal\Models\DefaultSystemEmail::where('id', $this->data['DefaultSystemEmail']['id'])->update($this->data['DefaultSystemEmail'])) {
				$this->flashMessage(__('The message has been saved', true), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->flashMessage(__('Could not save the message', true));
			}
		} else {
			$email = $this->DefaultSystemEmail->find(array('DefaultSystemEmail.type' => $type, 'DefaultSystemEmail.language_id' => $language_id));
			$this->data = $email;
		}
        
        $this->loadModel('Language');
        $this->set('languages', $this->Language->find('list', ['conditions' => ['active', '1']]));

        $this->set('specs', $defaultEmail[$type]);
        $this->set('type', $type);
        $this->set('language_id', $language_id);
		$this->helpers[] = 'fck';
    }
	
	

//	function owner_index() {
//		$this->DefaultSystemEmail->recursive = 0;
//		$conditions = $this->_filter_params();
//		$this->set('defaultSystemEmails', $this->paginate('DefaultSystemEmail', $conditions));
//	}
//
//	function owner_view($id = null) {
//		if (!$id) {
//			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('default system email', true)),true));
//			$this->redirect(array('action'=>'index'));
//		}
//		$this->set('defaultSystemEmail', $this->DefaultSystemEmail->read(null, $id));
//	}
//
//	function owner_add() {
//		if (!empty($this->data)) {
//			$this->DefaultSystemEmail->create();
//			if ($this->DefaultSystemEmail->save($this->data)) {
//				$this->flashMessage(sprintf (__('The %s has been saved', true), __('default system email',true)), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			} else {
//				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('default system email',true)));
//			}
//		}
//	}
//
//	function owner_edit($id = null) {
//		if (!$id && empty($this->data)) {
//			$this->flashMessage(sprintf (__('Invalid %s.', 'default system email',true)));
//			$this->redirect(array('action'=>'index'));
//		}
//		$defaultSystemEmail = $this->DefaultSystemEmail->read(null, $id);
//		if (!empty($this->data)) {
//			if ($this->DefaultSystemEmail->save($this->data)) {
//				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('default system email',true)), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			} else {
//				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('default system email',true)));
//			}
//		}
//		if (empty($this->data)) {
//			$this->data = $defaultSystemEmail;
//		}
//		$this->render('owner_add');
//	}
//
//	function owner_delete($id = null) {
//		if (empty($id) && !empty ($_POST['ids'])) {
//			$id = $_POST['ids'];
//		 }
// 		if (!$id && empty ($_POST)) {
//			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('default system email',true)));
//			$this->redirect(array('action'=>'index'));
//		}
//		$module_name= __('defaultSystemEmail', true);
//		if(count($id) > 1){
//			$module_name= __('defaultSystemEmails', true);
//		 }
//		$defaultSystemEmails = $this->DefaultSystemEmail->find('all',array('conditions'=>array('DefaultSystemEmail.id'=>$id)));
//		if (empty($defaultSystemEmails)){
//			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
//			$this->redirect($this->referer(array('action' => 'index'), true));
//		}
//		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
//			if ($_POST['submit_btn'] == 'yes' && $this->DefaultSystemEmail->deleteAll(array('DefaultSystemEmail.id'=>$_POST['ids']))) {
//				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			}
//			else{
//				$this->redirect(array('action'=>'index'));
//			}
//		}
//		$this->set('defaultSystemEmails',$defaultSystemEmails);
//	}
}

?>