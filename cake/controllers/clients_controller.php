<?php
App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use App\Helpers\UsersHelper;
use App\Repositories\CreditChargeRepository;
use App\Services\CreditDistribution\CreditDistributionService;
use App\Services\UserSessionRedisService;
use Izam\Daftra\Client\Exceptions\CanNotDeleteClientException;
use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\Auth\AuthUserTypeUtil;
use Izam\Daftra\Common\Factories\SocialMediaLinkCreatorFactory;
use Izam\Daftra\Common\RelatedEntities\ViewChildEntitiesHelper;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Portal\Models\Timezone as PortalTimezone;
use Izam\Limitation\Utils\LimitationUtil;
use App\Transformers\ServiceModelDataTransformer;
use Izam\Daftra\Client\Services\ClientService;
use Gettext\Translations;
use Izam\Daftra\Common\Utils\PostTypeUtil;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Entity\Helper\EntityActivityLog;
use Izam\Aws\Aws;
use Izam\Daftra\Invoice\Utils\InvoiceTypeUtil;
use Izam\Logging\Service\RollbarLogService;
use Izam\Entity\Helper\EntityHasLegacyCustomFields;
/**
 * @property Site $site
 * @property InvoicePayment $InvoicePayment
 * @property InvoiceInstallmentAgreement $InvoiceInstallmentAgreement
 */
class ClientsController extends AppController {

    var $name = 'Clients';

    /**
     * @var Client
     * @var RequestHandler
     */
    var $Client;
    var $helpers = array('Html', 'Form', 'Fck');
    var $components = array('Cookie', 'RequestHandler', 'Email', 'SysEmails', 'Lmail', 'ClientValidation');

    /* append js_lang_labels */
    var $client_js_labels = array(
        'You have to enter the "Email Address" of the client, or change his Invoicing Method',
        'Full Name',
        'Business Name',
		"Passwords doesn't match",
		'Total Debit Must be Equal to Total Credit'
    );
    var $paginate = [];

    function __form_common($id = null) {
        App::import('Vendor', 'settings');
        $this->loadModel('JournalAccountRoute');
        $this->setAccountRoute('clients_accounts_routing', Journal::CLIENT_ACCOUNT_ENTITY_TYPE, $id);
        $invoicing_method = settings::getValue(InvoicesPlugin, 'invoicing_method');
        $this->set('default_invoice_method', $invoicing_method);
        $hide_invoice_method = ($invoicing_method == settings::OPTION_INVOICING_METHOD_PRINT || $invoicing_method == settings::OPTION_INVOICING_METHOD_EMAIL);
        if($hide_invoice_method==false){
           $invoicing_method=settings::getValue(InvoicesPlugin, "preferred_invoicing_method");
        }
        $this->set('hide_invoice_method', $hide_invoice_method);
		// is_offline can be set to "0" manually which is send emails ya elwan
        if (is_null($this->data['Client']['is_offline'])) {
            if ($invoicing_method == settings::OPTION_INVOICING_METHOD_PRINT)
                $this->data['Client']['is_offline'] = 1;
            else if ($this->Cookie->read('last_is_offline') !== null && !$hide_invoice_method)
                $this->data['Client']['is_offline'] = $this->Cookie->read('last_is_offline');
            else
                $this->data['Client']['is_offline'] = settings::OPTION_INVOICING_METHOD_PRINT;
        }

        if (ifPluginActive(INSURANCE_PLUGIN)) {
            $this->loadModel('InsuranceAgent');
            $this->loadModel('InsuranceAgentClass');
            $insuranceAgents = $this->InsuranceAgent->find('list',['conditions' => ['InsuranceAgent.active' => 1]]);
            $insuranceAgentClasses = $this->InsuranceAgentClass->find('list',['fields' => ['InsuranceAgentClass.id','InsuranceAgentClass.name','InsuranceAgentClass.insurance_agent_id']]);
            $this->set('insuranceAgents',$insuranceAgents);
            $this->set('allInsuranceAgentClasses',$insuranceAgentClasses);
            if ($id) {
                $this->loadModel('ClientInsuranceClass');
                $clientInsuranceClass = $this->ClientInsuranceClass->find('first',['conditions' => ['ClientInsuranceClass.client_id' => $id]]);
                if ($clientInsuranceClass) {
                    $this->data['Client']['insurance_agent'] = $clientInsuranceClass['InsuranceAgentClass']['insurance_agent_id'];
                    $this->data['Client']['insurance_agent_class'] = $clientInsuranceClass['InsuranceAgentClass']['id'];
                    $this->data['Client']['insurance_number'] = $clientInsuranceClass['ClientInsuranceClass']['insurance_number'];
                    $this->set('insuranceAgentClasses',$insuranceAgentClasses[$clientInsuranceClass['InsuranceAgentClass']['insurance_agent_id']]);
                } else {
                    $this->set('insuranceAgentClasses',$insuranceAgentClasses);
                }
            }
        }
    }

    protected $mysqli = null;

    function getMysqli()
    {
        if (is_null($this->mysqli)) {
            $db_config = json_decode(getCurrentSite('db_config'), true);
            return $this->mysqli = connectToDatabase($db_config);
        } else  {
            return  $this->mysqli;
        }
    }

    public function api_view($id = null) {
		$client = $this->Client->find("first", ["conditions" => array("Client.id" => $id)] );
		if(empty($client)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Client", true))));
		$owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(Clients_View_All_Clients) && $client['Client']['staff_id'] != $staff) || !check_permission(Clients_View_his_own_Clients)) {
				$this->loadModel('ItemStaff');
				$this->loadModel('Post');
                if (!ifPluginActive(FollowupPlugin) || $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $id, 'ItemStaff.staff_id' => $staff)))==0) {
					$this->cakeError('error403');
                }
            }
        }
        $this->set('rest_item', $client);
		$this->set('rest_model_name', "Client");
		$this->render("view");
	}

    function owner_barcode($client_id) {
        App::import('Vendor', 'barcode_gen/bootstrap');

        $client_data = $this->Client->findById($client_id);
        header('Content-Type: image/jpeg');
        header("Content-disposition: inline; filename: download.jpg");

        $generatorJPG = new Picqer\Barcode\BarcodeGeneratorJPG();
        file_put_contents('php://output', $generatorJPG->getBarcode($client_id . $client_data['Client']['client_number'], $generatorJPG::TYPE_CODE_128));
        die;
    }
	
	function client_profile(){
        $client = getAuthClient();
        if($client)
		{
            $id = $client['id'];
			$client = $this->Client->find('first',array('conditions' => array('Client.id' => $client['id'])));
			if (empty($client['ClientDetail'])) {
			    $detalis = $client['Client'];
			    $detalis['first_name'] = explode(' ', $detalis['business_name'], 2)[0];
                $detalis['last_name'] = explode(' ', $detalis['business_name'], 2)[1];
                $detalis['mobile'] = !empty($detalis['phone1']) ? $detalis['phone1'] : $detalis['phone2'];
                $client['ClientDetail'] = [$detalis];
            }
			$this->set('client',$client);
			$this->Set('Openlist', $this->Client->getUnpaid($client['Client']['id']));
			 $this->Set('overduelist', $this->Client->getOverDue($client['Client']['id']));
			 App::import('Vendor', 'settings');
        $this->set('client_settings',settings::getPluginValues(ClientsPlugin));
		$this->set("geocode_api",$this->config['txt.google_geocode_api']);
        $this->set("google_maps_api",$this->config['txt.google_maps_api']);
		$owner = getAuthOwner();
		$this->set('site',$owner);
			$this->loadModel('Invoice');
			$this->loadModel('EmailLog');
			$this->set('issuedCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.issue_date <=' => date('Y-m-d'), 'Invoice.draft !=' => 1), 'recursive' => -1)));
			  $this->set('dueCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.date <=' => date('Y-m-d 23:59:59'), 'Invoice.draft !=' => 1, 'Invoice.payment_status !=' => INVOICE_STATUS_PAID))));
			  $this->set('overdueCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.draft !=' => 1, 'DATE_ADD(date, INTERVAL Invoice.due_after DAY) < ' => date('Y-m-d'), 'Invoice.payment_status !=' => INVOICE_STATUS_PAID))));

			  $this->set('subscriptionCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 1, 'Invoice.active' => 1, 'OR' => array('OR' => array('Invoice.subscription_max_repeat' => 0, 'Invoice.subscription_max_repeat IS NULL'), 'Invoice.subscription_repeated <= Invoice.subscription_max_repeat')))));

			  $this->set('lastPayment', $this->Invoice->InvoicePayment->find('first', array('conditions' => array('Invoice.client_id' => $id, 'InvoicePayment.status' => PAYMENT_STATUS_COMPLETED), 'recursive' => 0, 'order' => 'InvoicePayment.created DESC', 'fields' => 'InvoicePayment.id, InvoicePayment.created, InvoicePayment.amount, InvoicePayment.currency_code, Invoice.no, Invoice.id')));
			  $this->set('lastInvoice', $this->Invoice->find('first', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.draft !=' => 1), 'order' => 'Invoice.created DESC', 'recursive' => -1)));
			  $this->set('lastEmail', $this->EmailLog->find('first', array('conditions' => array('EmailLog.client_id' => $id), 'order' => 'EmailLog.sent_date DESC', 'fields' => 'EmailLog.id, EmailLog.sent_date')));
            if(IS_REST){
                $this->set('rest_item', $client);
                $this->set('rest_model_name', "Client");
                $this->render("view");
            }
        } else {
            if(IS_REST) $this->cakeError('error401');
        }
		
	}

    function client_dashboard() {
        $this->loadModel('ClientBlock');
        $client = getAuthClient();
        $client_id = $client['id'];
        $this->set('blocks', $this->ClientBlock->find('all', array('order' => 'display_order', 'conditions' => array('client_id' => $client_id), 'recursive' => -1)));
        $this->set('sub_blocks', (include_once APP . 'client_blocks.php'));

        $this->loadModel('Invoice');

        $this->loadModel('InvoicePayment');
        $recentPayments = $this->InvoicePayment->find('all', array('conditions' => array('Invoice.draft <> 1', 'InvoicePayment.amount >=' => 0, 'Invoice.client_id' => $client_id, 'InvoicePayment.status' => PAYMENT_STATUS_COMPLETED), 'limit' => 10, 'order' => 'InvoicePayment.date DESC', 'recursive' => 0));
        $paymentStatus = InvoicePayment::getPaymentStatus();

        $recentInvoices = $this->InvoicePayment->Invoice->find('all', array('conditions' => array('Invoice.draft <> 1', 'Invoice.client_id' => $client_id, 'Invoice.type' => 0, 'Invoice.payment_status !=' => INVOICE_STATUS_PAID), 'limit' => 10, 'order' => 'Invoice.date DESC', 'recursive' => -1));
        $dueInvoices = $this->InvoicePayment->Invoice->find('all', array('recursive' => -1, 'limit' => 10, 'conditions' => array('Invoice.draft <> 1', 'Invoice.type' => 0, 'Invoice.client_id' => $client['id'], 'Invoice.date <= CURDATE()', 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) >= CURDATE()', 'OR' => array('Invoice.payment_status !=' => 2, 'Invoice.payment_status' => null)), 'fields' => 'Invoice.id, Invoice.no, Invoice.date, Invoice.client_business_name, Invoice.payment_status, Invoice.summary_total, Invoice.currency_code', 'order' => 'Invoice.date DESC'));
        $dueInvoicesCount = $this->InvoicePayment->Invoice->find('count', array('recursive' => -1, 'conditions' => array('Invoice.draft <> 1', 'Invoice.type' => 0, 'Invoice.client_id' => $client['id'], 'Invoice.date <= CURDATE()', 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) >= CURDATE()', 'OR' => array('Invoice.payment_status !=' => 2, 'Invoice.payment_status' => null)), 'order' => 'Invoice.date DESC'));

        $overdueInvoices = $this->InvoicePayment->Invoice->find('all', array('recursive' => -1, 'limit' => 10, 'conditions' => array('Invoice.draft <> 1', 'Invoice.type' => 0, 'Invoice.client_id' => $client['id'], 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) < CURDATE()', 'OR' => array('Invoice.payment_status !=' => 2, 'Invoice.payment_status' => null)), 'fields' => 'Invoice.id, Invoice.no, Invoice.date, Invoice.client_business_name, Invoice.payment_status, Invoice.summary_total, Invoice.currency_code', 'order' => 'Invoice.date DESC'));
        $overdueInvoicesCount = $this->InvoicePayment->Invoice->find('count', array('recursive' => -1, 'limit' => 10, 'conditions' => array('Invoice.draft <> 1', 'Invoice.type' => 0, 'Invoice.client_id' => $client['id'], 'DATE_ADD(`Invoice`.`date` , INTERVAL `Invoice`.`due_after` DAY) < CURDATE()', 'OR' => array('Invoice.payment_status !=' => 2, 'Invoice.payment_status' => null)), 'order' => 'Invoice.date DESC'));
        $invoiceStatus = Invoice::getPaymentStatuses();

        $this->set(compact('recentPayments', 'paymentStatus', 'recentInvoices', 'dueInvoices', 'overdueInvoices', 'invoiceStatus', 'overdueInvoicesCount', 'dueInvoicesCount'));
        $latestInvoices = $this->Invoice->find('all', array('conditions' => array(
            'Invoice.client_id' => $client_id, 'OR' => array(
                array('Invoice.type' => 0, 'Invoice.draft <> 1', 'OR' => array('Invoice.payment_status !=' => INVOICE_STATUS_PAID, 'Invoice.payment_status' => null)),
//                array('Invoice.type' => 3, 'Invoice.draft <> 1', 'OR ' => array('Invoice.payment_status' => null, 'NOT' => array('Invoice.payment_status' => array(ESTIMATE_STATUS_ACCEPTED, ESTIMATE_STATUS_INVOICED))))
            )), 'limit' => 5, 'order' => 'Invoice.created DESC, Invoice.id DESC', 'recursive' => -1));
        $latestEstimates = $this->Invoice->find('all', array('conditions' => array(
            'Invoice.client_id' => $client_id, 'OR' => array(
//                array('Invoice.type' => 0, 'Invoice.draft <> 1', 'OR' => array('Invoice.payment_status !=' => INVOICE_STATUS_PAID, 'Invoice.payment_status' => null)),
                array('Invoice.type' => 3, 'Invoice.draft <> 1', 'OR ' => array('Invoice.payment_status' => null, 'NOT' => array('Invoice.payment_status' => array(ESTIMATE_STATUS_ACCEPTED, ESTIMATE_STATUS_INVOICED, ESTIMATE_STATUS_SALES_ORDERED))))
            )), 'limit' => 5, 'order' => 'Invoice.created DESC, Invoice.id DESC', 'recursive' => -1));

		//notes section
		$this->loadModel('Post');
		$conditions = array('Post.item_type' => Post::CLIENT_TYPE, 'Post.item_id' => $client_id);

		// appointments
        App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
        $this->loadModel('FollowUpReminder');
        $client = getAuthClient();
        $appointments = $this->FollowUpReminder->getPartnerClientAppointments($client['id'], true);
        $appointments = array_slice($appointments, 0, 5);
        $relatedModels = [];
        foreach ($appointments as $k => $v ){
            $typeItemModel = FollowUpReminder::$types_data[$v['FollowUpReminder']['item_type']]['item_model'];
            $relatedModel = isset($relatedModels[$typeItemModel]) ? $relatedModels[$typeItemModel] : $relatedModels[$typeItemModel] = GetObjectOrLoadModel($typeItemModel);
            $relatedModel->recursive = -1;
            $appointments[$k][$typeItemModel] = $relatedModel->findById($v['FollowUpReminder']['item_id'])[$typeItemModel];
            if(empty($appointments[$k][$typeItemModel])) {
                unset($appointments[$k]);
                continue;
            }
        }
        $this->set('appointments',$appointments);
        $this->loadModel('Staff');
        $staffs = $this->Staff->find('list');
        $this->set('staffs',$staffs);

        $last_posts = $this->Post->find('first', array('order' => 'Post.id desc', 'conditions' => $conditions));
        if (isset($last_posts['Post'])) {
            $arry = array('primary_id' => $last_posts['Post']['id'], 'secondary_id' => $client['id'], 'param1' => $last_posts['Post']['id']);
            $this->add_actionline(ACTION_CLIENT_VIEW_POST, $arry);
        }

        $conditions['Post.client_permissions'] = 1;

        $order = 'Post.date desc';
		        $this->Post->recursive = 3;

        $posts['data'] = $this->Post->find('all', array( 'limit' => 5, 'order' => $order, 'conditions' => $conditions));
        $this->set('posts', $posts);
		//end notes section

        $this->set('latestInvoices', Set::classicExtract($latestInvoices, '{n}.Invoice'));
        $this->set('latestEstimates', Set::classicExtract($latestEstimates, '{n}.Invoice'));
        $this->Set('Openlist', $this->Client->getUnpaid($client_id));
        $this->Set('overduelist', $this->Client->getOverDue($client_id));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Dashboard', true);
        $this->crumbs[0]['url'] = '#';

        $this->set('languages', $this->_json_lang());

        $this->titleAlias = 'home';
        $this->set('title_for_layout',  __('Dashboard', true));

		
		$client_invoices_count = count($latestInvoices);
//		$client_invoices_count = 1;
		$client_shared_notes_count = count((array)$last_posts);
        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, false, false);
        $this->set('paymentMethods', $paymentMethods);
		$client_settings = $this->Session->read('CLIENT_SETTINGS');
//		debug($client_settings);

        if(
            !ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::WebsiteFrontPlugin) &&
            ($client_settings['client_permission_invoices'] == 0 || $client_invoices_count <= 0) &&
            ($client_settings['client_permission_estimates'] == 0 || $client_invoices_count <= 0 ) &&
            ($client_settings['client_permission_posts'] == 0 || $client_shared_notes_count <= 0 ) &&
            ($client_settings['client_permission_view_profile'] == 0 ) &&
            ($client_settings['client_permission_work_orders'] == 0 ) 
			)
				{

                                         $this->flashMessage(__('You are not allowed to login', true), 'Errormessage', 'Loginpage');
					 $this->redirect(Router::url(array('action' => 'logout','client' => true)));
				}

		if($client_settings['client_permission_invoices'] == 0 || $client_invoices_count <= 0)
		{
//			if(!($client_settings['client_permission_estimates'] == 0 || $client_invoices_count <= 0 ))
//			{
//				 $this->redirect(Router::url(array('controller' => 'invoices', 'action' => 'estimates', 'client' => true)));
//			}else if(!($client_settings['client_permission_posts'] == 0 || $client_shared_notes_count <= 0)){
//				 $this->redirect(Router::url(array('controller' => 'posts', 'action' => 'index', 'client' => true)));
//			}else if(!($client_settings['client_permission_view_profile'] == 0)){
//				 $this->redirect(Router::url(array('controller' => 'clients', 'action' => 'profile', 'client' => true)));
//			}
		}
		if (!ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::WebsiteFrontPlugin)) {
            $this->clientRedirect();
        }


    }

    private function clientRedirect()
    {

        $menus = getClientMenu($this->viewVars);
        $firstPage = $menus[array_keys($menus)[0]];
        if(!empty($firstPage['url']['action'])){
            $this->redirect(Router::url($firstPage['url']));
        }

    }

    function owner_delete_staff($id = null, $staff_id = null) {

        $this->loadModel('Staff');

        $this->loadModel('ItemStaff');
        $this->loadModel('Post');

        $ajax = $this->RequestHandler->isAjax();
        if (!$id or !$staff_id) {
            if ($ajax) {
                echo 'false';
                die();
            } else {
                $this->flashMessage(__('Client not found', true));
                $this->redirectToIndex();
            }
        }
        $client = $this->Client->find(array('Client.id' => $id));
        if (!$client and !$ajax) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }
        if (!$client and $ajax) {
            echo 'false';
            die();
        }
        $this->ItemStaff->deleteAll(array('ItemStaff.staff_id' => $staff_id, 'ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $id), false);
        $staff = $this->Staff->read(null, $staff_id);
        $this->add_actionline(ACTION_UNASSIGN_STAFF_FROM_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => null, 'param4' => $client['Client']['client_number'], 'param5' => $staff_id, 'param6' => $staff['Staff']['name']));
        if ($ajax) {
            echo 'ok';
        } else {
            $this->flashMessage(__('The client is no longer assigned to this staff member', true));
            $this->redirect($this->referer(array('action' => 'view', $id)));
        }
        die();
    }

    function owner_assign_staff($id = null) {


        if (!check_permission(Assign_Clients_To_Staff)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirectToIndex();
        }
        $this->loadModel('ItemStaff');
        $this->loadModel('Post');
        $this->loadModel('Staff');

        $this->Client->applyBranch=['OnFind'=>false];
        $client = $this->Client->find(array('Client.id' => $id));

        if (!$client) {
            if(IS_REST){
                die(json_encode(['status' => false , 'message'=>__('Client not found', true)]));
            }
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }
        if (!empty($this->data)) {
            $current_staff = $this->ItemStaff->find('list', array('fields' => 'id,staff_id', 'conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $id)));
            if ($current_staff) {
                $diff = array_diff(array_values($current_staff), array_values((array)$this->data['ItemStaff']['staff_id']));
            } else {
                $diff = array();
            }



			App::import('Vendor', 'notification_2');

            $this->ItemStaff->assign_staff_members($this->data['ItemStaff']['staff_id'], $id, isset($this->data['ItemStaff']['item_type']) ? $this->data['ItemStaff']['item_type'] : 1);

            $trigger = NotificationV2::get_trigger();
			NotificationV2::add_notification(NotificationV2::NOTI_ASSIGN_CLIENT,$id,$this->data['ItemStaff']['staff_id'],$user_type = NotificationV2::NOTI_USER_TYPE_STAFF,             $trigger['trigger_type'],$trigger['trigger_id']);


            if (is_array($this->data['ItemStaff']['staff_id'])) {
                $staff = $this->Staff->find('list', array('conditions' => array('Staff.id' => $this->data['ItemStaff']['staff_id'])));
                $this->add_actionline(ACTION_ASSIGN_STAFF_TO_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => null, 'param4' => $client['Client']['client_number'], 'param5' => implode(',', array_keys($staff)), 'param6' => implode(',', array_values($staff))));
                if (count($diff) > 0) {
                    $staff = $this->Staff->find('list', array('conditions' => array('Staff.id' => array_values($current_staff))));
                    $this->add_actionline(ACTION_UNASSIGN_STAFF_FROM_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => null, 'param4' => $client['Client']['client_number'], 'param5' => implode(',', array_keys($staff)), 'param6' => implode(',', array_values($staff))));
                }
            } else {
                $staff = $this->Staff->find('list', array('conditions' => array('Staff.id' => array_values($current_staff))));
                $this->add_actionline(ACTION_UNASSIGN_STAFF_FROM_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => null, 'param4' => $client['Client']['client_number'], 'param5' => implode(',', array_keys($staff)), 'param6' => implode(',', array_values($staff))));
            }
            if(IS_REST){
                die(json_encode(['status' => true]));
            }
            $this->flashMessage(__('The client has been assigned to the selected staff member(s)', true), 'Sucmessage');
            $this->set('reload', "true");
        }

        if(IS_REST){
            die(json_encode(['status' => false]));
        }
        $this->set('client', $client);
        $this->set('client_type', Post::CLIENT_TYPE);
        $this->set('id', $id);
        $assignedStaffs = $this->ItemStaff->find('list', array('fields' => 'staff_id,id', 'conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $id)));
        $this->set('staff_assignd', $assignedStaffs);
        $this->set('staffsz', UsersHelper::getInstance()->setSelectedStaff(array_keys($assignedStaffs))->getList(false, [], true, true));
    }

    function owner_assign_staff_multi() {
        $ids = explode(',', $this->data['Client']['ids']);
        $filter_query = $this->data['Client']['filter_query'];
        $filter_query = json_decode($filter_query, true);

        if (!check_permission(Assign_Clients_To_Staff)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/client/list');
        }

        if (empty($ids)) {
            $this->flashMessage(__("You should select one client at least", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/client/list');
        }

        $this->loadModel('ItemStaff');
        $this->loadModel('Post');
        $this->loadModel('Staff');

        $this->Client->applyBranch = ['OnFind' => false];

        if (!empty($this->data)) {

            App::import('Vendor', 'notification_2');

            if ($ids[0] == 'all') {
                $ids = $this->getIdsAllSelected($ids, $filter_query['filter']['assigned_to.staff_id']['in']);
            }
            $staffs = !empty($this->data['Client']['staff']) ? $this->data['Client']['staff'] : [];

            foreach ($ids as $id) {

                $client = $this->Client->find(array('Client.id' => $id));

                if (!$client) {
                    if (IS_REST) {
                        die(json_encode(['status' => false, 'message' => __('Client not found', true)]));
                    }
                    $this->flashMessage(__('Client not found', true));
                    $this->redirect('/v2/owner/entity/client/list');
                }

                $current_staff = $this->ItemStaff->find('list', array('fields' => 'id,staff_id', 'conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $id)));

                $diff = [];
                if ($current_staff) {
                    $diff = array_diff(array_values($current_staff), array_values($staffs));
                }

                $this->ItemStaff->assign_staff_members($staffs, $id, 1);

                $trigger = NotificationV2::get_trigger();
                NotificationV2::add_notification(NotificationV2::NOTI_ASSIGN_CLIENT, $id, $staffs, $user_type = NotificationV2::NOTI_USER_TYPE_STAFF, $trigger['trigger_type'], $trigger['trigger_id']);


                if (is_array($staffs)) {
                    $staff = $this->Staff->find('list', array('conditions' => array('Staff.id' => $staffs)));
                    $this->add_actionline(ACTION_ASSIGN_STAFF_TO_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => null, 'param4' => $client['Client']['client_number'], 'param5' => implode(',', array_keys($staff)), 'param6' => implode(',', array_values($staff))));
                    if (count($diff) > 0) {
                        $staff = $this->Staff->find('list', array('conditions' => array('Staff.id' => array_values($current_staff))));
                        $this->add_actionline(ACTION_UNASSIGN_STAFF_FROM_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => null, 'param4' => $client['Client']['client_number'], 'param5' => implode(',', array_keys($staff)), 'param6' => implode(',', array_values($staff))));
                    }
                }
            }

            if (IS_REST) {
                die(json_encode(['status' => true]));
            }
            $this->flashMessage(__('Employees assigned successfully to the selected clients', true), 'Sucmessage');
        }


        $this->redirect('/v2/owner/entity/client/list');
    }

    function owner_send_email($id = null) {
        $site = getAuthOwner();
        $client = $this->Client->find(array('Client.id' => $id));

        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }
        $this->loadModel('Invoice');

        if ($site['use_smtp'] != 1) {


            $check = check_daily_email_limit();


            if (!$check['status']) {
                $this->Email->from = $this->config['txt.admin_mail'];
                $this->Email->to = $this->config['txt.admin_mail'];
                $this->Email->subject = $site['business_name'] . ' Email Limit';
                $this->Email->send('Site: ' . $site['business_name'] . '<br>' . 'Subdomin: ' . $site['subdomain']);

                $this->flashMessage($check['message']);
                $this->redirect(array('action' => 'view', $id));
            }
        }

        if (empty($client['Client']['email'])) {
            $this->flashMessage(__('Client email not set', true));
            $this->redirectToIndex();
        }
//        debug($client);

        $this->loadModel('EmailLog');
        $this->loadModel('EmailTemplate');
        $this->set('client_email', $this->Client->getClientEmails($id));
        $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('client-email');

        //  $placeHolders = $this->EmailTemplate->getPlaceHoldersList('client-email');
        $placeHolders = PlaceHolder::client_place_holder_list() + PlaceHolder::site_place_holder_list() + PlaceHolder::staff_place_holder_list();
        $this->set('PlaceHolders', $placeHolders);
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->set(compact('defaultTemplate'));

        if (!empty($this->data)) {
            if (empty($this->data['EmailTemplate']['to_email'])) {
                $this->EmailTemplate->validationErrors['to_email'] = __('Please enter client email', true);
                $error = true;
            }

            $count_email = explode(',', $this->data['EmailTemplate']['to_email']);
            $count_email_2 = explode(';', $this->data['EmailTemplate']['to_email']);
            if (!empty($this->data['EmailTemplate']['to_email']) and (count($count_email) > 5 or count($count_email_2) > 5)) {
                $this->EmailTemplate->validationErrors['to_email'] = __('You can only send 5 emails at once', true);
                $error = true;
            }
            $count_cc_email = explode(',', $this->data['EmailTemplate']['cc_email']);
            $count_cc_email_2 = explode(';', $this->data['EmailTemplate']['cc_email']);
            if (!empty($this->data['EmailTemplate']['cc_email']) and (count($count_cc_email) > 3 or count($count_cc_email_2) > 3)) {
                $this->EmailTemplate->validationErrors['cc_email'] = __('You can only send 3 cc emails at once', true);
                $error = true;
            }

            $count_bcc_email = explode(',', $this->data['EmailTemplate']['bcc_email']);
            $count_bcc_email_2 = explode(';', $this->data['EmailTemplate']['bcc_email']);
            if (!empty($this->data['EmailTemplate']['bcc_email']) and (count($count_bcc_email) > 3 or count($count_bcc_email_2) > 3)) {
                $this->EmailTemplate->validationErrors['bcc_email'] = __('You can only send 3 bcc emails at once', true);
                $error = true;
            }

            if (empty($this->data['EmailTemplate']['subject'])) {
                $this->EmailTemplate->validationErrors['subject'] = __('Please enter subject', true);
                $error = true;
            }
            if (empty($this->data['EmailTemplate']['body'])) {
                $this->EmailTemplate->validationErrors['body'] = __('Please enter Message', true);
                $error = true;
            }
            if ($error) {
                $this->flashMessage(__('Could not send email to client', true));
            } else {
                $placeHolders = PlaceHolder::site_place_holder($site) + PlaceHolder::client_place_holder($client) + PlaceHolder::staff_place_holder($site['staff_id']);

                $attachments = array();
                if (file_exists($this->data['EmailTemplate']['attachments']['tmp_name'])) {
                    move_uploaded_file($this->data['EmailTemplate']['attachments']['tmp_name'], '/tmp/' . $this->data['EmailTemplate']['attachments']['name']);
                    $attachments[$this->data['EmailTemplate']['attachments']['name']] = '/tmp/' . $this->data['EmailTemplate']['attachments']['name'];
                }

                
                // Used to Fix Attachments urls uploaded from  email editor .         
                if (isset($this->data['EmailTemplate']['body']))
                {
                    $this->data['EmailTemplate']['body'] = str_replace('src="../../../files','src="https://' . $site['subdomain'].'/files' ,  $this->data['EmailTemplate']['body'] );
                }       
                
                $send_email = $this->SysEmails->sendEmail($this->data['EmailTemplate']['to_email'], $site, $this->data, $placeHolders, $attachments, array('client_id' => $client['Client']['id']));

                if ($send_email) {
                    $this->add_actionline(ACTION_SEND_EMAIL_TO_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $this->data['EmailTemplate']['to_email'], 'param5' => $send_email, 'param4' => $client['Client']['client_number']));
                    $this->flashMessage(__('The Email message has been sent to client', true), 'Sucmessage');
                    $this->redirect(array('action' => 'view', $id));
                } else {
                    $this->flashMessage(sprintf(__('Could not send email to the client, %s', true), $this->SysEmails->error_message));
                }
            }
        } else {
            $this->data = $defaultTemplate;
            unset($this->data["EmailTemplate"]['id']);
        }

        $this->set('client', $client);
        $email_templates = $this->EmailTemplate->getEmailTemplates('client-email');

        $this->set('email_templates', $email_templates);
    }

    function owner_send_emails() {

        set_time_limit(99999);
        ini_set("memory_limit", "5542M");
        	$site = getAuthOwner();

        if(empty($this->data['EmailTemplate']['ids']) and empty($_POST['ids'])){
            $this->flashMessage(sprintf(__('Invalid %s', true), __('client', true)));
            $this->redirectToIndex();

        }
        $ids = isset($_POST['ids']) ? $_POST['ids'] : explode(',', $this->data['EmailTemplate']['ids']);

        if(!is_array($ids)){
            $ids = explode(',', $ids);
        }

        if (empty($ids)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('client', true)));
            $this->redirectToIndex();
        }

        if (is_countable($ids) && count($ids) > 1000) {
            $this->flashMessage(__("You can't select more than 1,000 clients at one time.", true));
            $this->redirect(array('action' => 'index'));
        }
        $site = getAuthOwner();
        $this->loadModel('Invoice');

        if ($site['use_smtp'] != 1) {
            $check = check_daily_email_limit();
            if (!$check['status']) {
                $this->Email->from = $this->config['txt.admin_mail'];
                $this->Email->to = $this->config['txt.admin_mail'];
                $this->Email->subject = $owner['business_name'] . ' Email Limit';
                $this->Email->send('Site : ' . $owner['business_name'] . '<br>' . 'Subdomin : ' . $owner['subdomain']);

                $this->flashMessage($check['message']);
                $this->redirectToIndex();
            }
        }

        $this->loadModel('EmailLog');
        $this->loadModel('EmailTemplate');

        $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('client-email');
        $placeHolders = $this->EmailTemplate->getPlaceHoldersList('client-email');
        $this->set('PlaceHolders', $placeHolders);
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->set(compact('defaultTemplate'));

        if (!empty($this->data) and !empty($this->data['EmailTemplate']['action'])) {

            if (empty($this->data['EmailTemplate']['subject'])) {
                $this->EmailTemplate->validationErrors['subject'] = __('Please enter subject', true);
                $error = true;
            }
            if (empty($this->data['EmailTemplate']['body'])) {
                $this->EmailTemplate->validationErrors['body'] = __('Please enter Message', true);
                $error = true;
            }
            if ($error) {
                $this->flashMessage(__('Could not send email to clients', true));
            } else {
                $attachments = array();
                if (file_exists($this->data['EmailTemplate']['attachments']['tmp_name'])) {
                    move_uploaded_file($this->data['EmailTemplate']['attachments']['tmp_name'], '/tmp/' . $this->data['EmailTemplate']['attachments']['name']);
                    $attachments[$this->data['EmailTemplate']['attachments']['name']] = '/tmp/' . $this->data['EmailTemplate']['attachments']['name'];
                }
                $client_list = explode(',', $this->data['EmailTemplate']['ids']);

                foreach ($client_list as $client_id) {
                    $client = $this->Client->read(null, $client_id);

                    if (!empty($client['Client']['email'])) {
                        $placeHolders = PlaceHolder::site_place_holder($site) + PlaceHolder::client_place_holder($client) + PlaceHolder::staff_place_holder($site['staff_id']);
//                        $placeHolders = array(
//                            '{%client-number%}' => $client['Client']['client_number'],
//                            '{%client-name%}' => empty($client['Client']['first_name']) && empty($client['Client']['first_name']) ? $client['Client']['business_name'] : $client['Client']['first_name'] . ' ' . $client['Client']['last_name'],
//                            '{%client-business_name%}' => $client['Client']['business_name'],
//                            '{%client-address1%}' => $client['Client']['address1'],
//                            '{%client-address2%}' => $client['Client']['address2'],
//                            '{%client-city%}' => $client['Client']['city'],
//                            '{%client-state%}' => $client['Client']['state'],
//                            '{%client-postal-code%}' => $client['Client']['postal_code'],
//                            '{%client-country%}' => $client['Country']['country'],
//                        );



                        $send_email = $this->SysEmails->sendEmail($client['Client']['email'], $site, $this->data, $placeHolders, $attachments, array('client_id' => $client['Client']['id']));
                        if (!$send_email) {
                            $this->flashMessage(sprintf(__('Could not send email to the client, %s', true), $this->SysEmails->error_message));
                            $this->redirect(array('action' => 'index'));
                        }
                        $this->add_actionline(ACTION_SEND_EMAIL_TO_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param5' => $send_email, 'param4' => $client['Client']['client_number']));
                    }
                }

                $this->flashMessage(__('email has been sent to client', true), 'Sucmessage');
                $this->redirectToIndex();
            }
        } else {
            $this->data = $defaultTemplate;
        }

        $clients = $this->Client->find('all', array('conditions' => array('Client.email !=' => '', 'Client.id' => $ids)));

        $id_list = [];
        foreach ($clients as $client) {
            $id_list[] = $client['Client']['id'];
        }

        $this->set('ids', $id_list);
        $this->set('clients', $clients);
        $email_templates = $this->EmailTemplate->getEmailTemplates('client-email');

        $this->set('email_templates', $email_templates);
    }

    function owner_json_find($is_report = null, $emptyValue = null) {
        Configure::write('debug', 0);
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        $db_config = json_decode(getCurrentSite('db_config'), true);
//        die(var_dump(mysqli_real_escape_string(new mysqli(), $this->params['url']['q'])));
        if (!empty($value)) {
            $value = mysqli_real_escape_string($this->getMysqli(), $value);
            $clients = array();
            $conditions = array();
            if(isset($_GET['suspend']) && (!$_GET['suspend'] || in_array($_GET['suspend'], ['0', 'false']))){
                $conditions[] = ' ( suspend <> 1 OR suspend is null )';
            } 
            if (isset($_GET['type']) && isset($_GET['action']) ){
                $conditions[] = $this->checkCanViewHisOwnClientsCondition($_GET['type'], $_GET['action'], $owner['staff_id']);
            }
            $allPermission = $is_report ? VIEW_ALL_CLIENTS_REPORTS : Clients_View_All_Clients;
            $ownPermission = $is_report ? VIEW_HIS_OWN_CLIENTS_REPORTS : Clients_View_his_own_Clients;
            if (!$this->Client->global_need_clients() && !check_permission($allPermission) && check_permission($ownPermission)) {
                $conditions[] = $this->getCanViewHisOwnClientsCondition($owner['staff_id']);
            }

            if ($this->Client->global_need_clients() || check_permission([$allPermission,$ownPermission])) {
                $conditions['OR'] = $this->Client->getSearchClientConditions($value);

                $order_by='Client.business_name asc';
                if (is_numeric($value)) {
                    $order_by='Client.client_number asc';
                }
                $clients = $this->Client->find('all', array('limit' => 30, 'conditions' => $conditions , 'order' => $order_by, 'recursive' => 1));

                $result = array();
                $aws = new Aws();
                foreach ($clients as $client) {
                    $full_name = '';
                    if (!empty($client['Client']['first_name']) || !empty($client['Client']['last_name']))
                        $full_name = $client['Client']['first_name'] . ' ' . $client['Client']['last_name'];
                    if ($full_name == $client['Client']['business_name'])
                        unset($full_name);
                    $result[] = array(
                        'name' => ($client['Client']['business_name'] . ' ' . (empty($full_name) ? '' : '(' . $full_name . ') ') . ' #' . $client['Client']['client_number']),
                        'id' => $client['Client']['id'],
						'photo' =>  resolve(AttachmentsService::class)->resolveClientImage((object)$client['Client']),
						'phone1' => $client['Client']['phone1']?$client['Client']['phone1']:$client['Client']['phone2'],
						'phone2' => $client['Client']['phone2'],
                        'details' => ''
                    );
                }
            }
            if (!empty($result))
                array_unshift($result, array(
                    'name' => $emptyValue ? $emptyValue : __('Please Select', true) . ':',
                    'id' => '',
                    'details' => ''
                ));
            echo json_encode($result, JSON_UNESCAPED_SLASHES);
            die();
        }else {
            echo json_encode(array());
            die();
        }
    }

    function api_search_client()
    {
        if (empty($this->data)) {
            die(json_encode([]));
        }
        $owner = getAuthOwner();
        $value = $this->data['searchText'];
        $value = mysqli_real_escape_string($this->getMysqli(), $value);
        $conditions = array();
        if (!check_permission(Clients_View_All_Clients) && check_permission(Clients_View_his_own_Clients)) {
            $conditions[] = $this->getCanViewHisOwnClientsCondition($owner['staff_id']);
        }
        if (check_permission([Clients_View_All_Clients, Clients_View_his_own_Clients])) {
            $conditions['OR'] = $this->Client->getSearchClientConditions($value);

            $unprocessedClients = $this->Client->find('all', array('limit' => 30, 'conditions' => $conditions, 'order' => 'Client.business_name asc', 'recursive' => 1));
            $clients = $this->processClients($unprocessedClients);
            die(json_encode($clients, JSON_UNESCAPED_SLASHES));
        }
        die(json_encode([]));
    }

    //-----------------------------
    function client_change_email() {
        $client = getAuthClient();
        if (!empty($this->data)) {
            if ($this->Client->ChangeEmail($this->data)) {
                $this->add_actionline(ACTION_CLIENT_CHNAGE_EMAIL, array('staff_id' => -1, 'primary_id' => $client['id'], 'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number'], 'param5' => $this->data['Client']['email']));
                $this->flashMessage(__('Email has been saved', true), 'Sucmessage');
                $this->Client->reload_session();
                $this->redirect(array('action' => 'change_email'));
            } else {
                $this->flashMessage(__('Email could not be saved. Please, try again.', true));
            }
        }
        if (empty($this->data)) {
            $this->data['Client'] = $client;
        }
        if(getClientSettings("prevent_client_edit_profile")){
            $this->redirect("/");
        }
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Change Email', true);
        $this->crumbs[0]['url'] = '#';

        $this->set('title_for_layout',  __('Change Email', true));
    }

    //----------------------------------
    function client_change_password() {
        $client = getAuthClient();

        if (!empty($this->data)) {
            if ($this->Client->ChangePassword($this->data)) {
                AuthHelper::revoke(AuthUserTypeUtil::CLIENT, $client['id']);
                $this->add_actionline(ACTION_CLIENT_CHNAGE_PASSWORD, array('staff_id' => -1, 'primary_id' => $client['id'], 'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number']));
                $this->flashMessage(__('Password has been saved', true), 'Sucmessage');
                $this->redirect(array('action' => 'change_password'));
            } else {
                $this->flashMessage(__('Password could not be saved. Please, try again.', true));
            }
        }
        if(getClientSettings("prevent_client_edit_profile")){
            $this->redirect("/");
        }

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Change Password', true);
        $this->crumbs[0]['url'] = '#';

        $this->set('title_for_layout',  __('Change Password', true));
    }

    //---------------------------------
    function client_change_settings() {
        $_REQUEST=modifyArray($_REQUEST);
        $this->data=modifyArray($this->data);
        $this->Client->applyBranch= ['onFind' => false, 'onSave' => false];
        $client = getAuthClient();
        if (!empty($this->data)) {
            $data = array();
            $this->data['Client']['id'] = $client['id'];
            unset($this->data['Client']['client_number']);
            unset($this->data['Client']['site_id']);
            unset($this->data['Client']['email']);
            unset($this->data['Client']['password']);
            unset($this->data['Client']['suspend']);
            unset($this->data['Client']['last_ip']);
            unset($this->data['Client']['last_login']);

            if ($this->Client->save($this->data)) {
                $this->add_actionline(ACTION_CLIENT_UPDATE_PROFILE, array('staff_id' => -1, 'primary_id' => $client['id'], 'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number'], 'param5' => json_encode($this->data['Client'])));
                $this->flashMessage(__('Your profile has been saved', true), 'Sucmessage');
                $this->Client->reload_session();
                $this->redirect(array('action' => 'change_settings'));
            } else {
                $this->flashMessage(__('Settings could not be saved. Please, try again.', true));
            }
        }
        if (empty($this->data)) {
            $dbClient = $this->Client->findById($client['id']);
            $this->data['Client'] = $dbClient['Client'];
            
        }
        if(getClientSettings("prevent_client_edit_profile")){
            $this->redirect("/");
        }
        $this->loadModel('Country');
        $this->loadModel('Language');
        $this->loadModel('Currency');

        $this->set('countryCodes', $this->Country->getCountryList());
        $this->set('languageCodes', $this->Language->getLanguageList());
        $this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = '#';

        $this->set('title_for_layout',  __('Change Settings', true));
    }

    //----------------------
    function client_logout() {
        $this->Session->delete('CLIENT');
		
        $this->Cookie->destroy();
        //   $this->Cookie->delete('User_hash');

        $this->redirect('/');
    }

    //--------------------------------

    function owner_change_category() {
        $ids = isset($_POST['ids']) ? $_POST['ids'] : explode(',', $this->data['Client']['ids']);
        if (empty($ids[0])) {
            unset($ids[0]);
        }
        $referer_url = $this->_get_referer_path(true);

        if (empty($ids)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('clients', true)));
            $this->redirect($referer_url);
        }
        $this->set('ids', $ids);

        $this->set('categories', $categories = $this->Client->getCategoriesList(false));

        if (!empty($this->data) and !empty($this->data['Client']['action'])) {
            $this->loadModel('FollowUpStatus');

            $ids_array = explode(',', $this->data['Client']['ids']);
            $data = $this->data;
            $this->loadModel('Category');
            $categoryCriteria = [
                'name' => $data['Client']['category'],
                'description' => $data['Client']['category'],
                'category_type' => $this->Category::CATEGORY_TYPE_CLIENT,
            ];
            $category = $this->Category->find('first', ['conditions' => $categoryCriteria]);
            foreach ($ids_array as $client_id) {
                $client = $this->Client->read(null, $client_id);
                if ($client) {
                    $this->Client->saveField('category', $category['Category']['name']);
                    $this->Client->saveField('category_id', $category['Category']['id']);
                    $this->add_actionline(ACTION_UPDATE_CLIENT_CATEGORY, array('primary_id' => $client_id, 'secondary_id' => $client_id, 'param1' => $client['Client']['client_number'], 'param2' => $client['Client']['business_name'], 'param3' => $category['Category']['name']));
                }
            }
            $this->flashMessage(__('Clients has been saved', true), 'Sucmessage');

            if (!empty($this->params['url']['box'])) {
                $this->set('box', true);
                $this->set('save', true);
            } else {
                $this->redirect($referer_url);
            }
        }
    }

    function owner_index() {
        if (!IS_REST) {
            if (!\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('clients')) {
                return $this->redirect('/v2/owner/entity/client/list');
            }
        }

        $this->Client->bindAttachmentRelationWithEntityAndFieldKey('client','clients.photo');

        ini_set('memory_limit','4G');
        $this->loadModel('Timezone');
        // echo Configure::read('Config.language');
        $client_settings=settings::getPluginValues(ClientsPlugin);
        $this->Client->recursive = 0;
	

        $owner = getAuthOwner();
        $conditions = array();


        $this->loadModel('Post');
        $results_count = $this->Client->query("select count(*) as results_count from clients");
        debug($results_count[0][0]["results_count"]);
        $this->set("results_count", $results_count[0][0]["results_count"]);
		
		
        $filter_conditions = $this->_filter_params();
//		dd($filter_conditions);
        $conditions[] = $filter_conditions;

        // check assign_staff_id
        if( isset($this->params['url']['assign_staff_id']) && !empty($this->params['url']['assign_staff_id']) )
        {
            $assign_staff_id = (int)$this->params['url']['assign_staff_id'];
            $assign_staff_id = mysqli_real_escape_string( $this->getMysqli(), $assign_staff_id );
            $this->loadModel('Post');
            $conditions[] = '(Client.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . Post::CLIENT_TYPE . ' AND item_staffs.staff_id=' . $assign_staff_id . ' )) ';
        }

        if ($owner['staff_id'] != 0) {
            if (!check_permission(Clients_View_All_Clients) && !check_permission(Clients_View_his_own_Clients) && !check_permission(Add_Notes_Attachments_For_All_Clients) && !check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) && !check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }

            if (!check_permission(Clients_View_All_Clients)) {

                if (ifPluginActive(FollowupPlugin)){
                    $conditions[] = $this->getCanViewHisOwnClientsCondition($owner['staff_id']);
                }else{
                    $conditions['Client.staff_id'] = $owner['staff_id'];
                }

            }
        }

        if (!empty($_GET['keywords'])) {
            $keywords = trim(($_GET['keywords']));
            $conditions['OR'] = array(
                'Client.id LIKE' => '%' . $keywords . '%',
                'Client.client_number LIKE' => '%' . $keywords . '%',
                'Client.business_name LIKE' => '%' . $keywords . '%',
                'Client.first_name LIKE' => '%' . $keywords . '\'%',
                'Client.last_name LIKE' => '%' . $keywords . '%',
                'Client.email LIKE' => '%' . $keywords . '%',
                'Client.phone1 LIKE' => '%' . $keywords . '%',
                'Client.phone2 LIKE' => '%' . $keywords . '%',
            );

            if(!empty($client_settings['national_id'])&&$client_settings['national_id'] == 1) {
                $conditions['OR']['Client.national_id LIKE']=  '%' . $keywords . '%';
            }
        }

        $this->paginate['order'] = "Client.business_name";
        if (!empty($conditions[0])) {
            $this->set('search_filter', "true");
        }

        App::import('Vendor', 'settings');
        $this->set('client_settings',$client_settings);
		$this->set('file_settings', $this->Client->getImageSettings());
        $oldRecursive = $this->Client->recursive;
        $this->Client->recursive = 1;
        $clients = $this->processClients($this->paginate('Client', $conditions));
        $this->Client->recursive = $oldRecursive;
		$this->setConditionsKey('clients', $conditions);
        $this->set('clients', $clients );
        $this->set('staff_id', $owner['staff_id']);

        $this->loadModel('Country');
        $this->loadModel('Currency');


        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->set('site', $owner);


        $this->set('countryCodes', $this->Client->getCountryList());
        $this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());
        $this->set('statuses', array('' => __('Any', true), 0 => __('Active', true), 1 => __('Suspended', true)));
        $this->set('types',[settings::OPTION_CLIENT_TYPE_BUSINESS => __('Business Only', true), settings::OPTION_CLIENT_TYPE_INDIVIDUAL  => __('Individual Only', true)]);

        $this->set('content', $this->get_snippet('clients'));

        if (ifPluginActive(FollowupPlugin)) {
            $this->loadModel('FollowUpStatus');
            $this->loadModel('Post');
            $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::CLIENT_TYPE);
            if (check_permission(Edit_General_Settings)) {
                $FollowUpStatus[] = array('name' => false, 'value' => false, 'data-divider' => "true");
                $FollowUpStatus[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> Edit Statuses List </span>', 'name' => __('Edit Statuses List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
            }
            $this->set('FollowUpStatuses', $FollowUpStatus);

            $client_statuses = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, true);
            $this->set('client_statuses', $client_statuses);

            $client_status_colors = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, true, array(), 'color');
            $this->set('colors_options', $this->FollowUpStatus->colors);
            $this->set('client_status_colors', $client_status_colors);
        }

		$clients_ids = array();
		foreach($clients as $k => $client)
		{
			$clients_ids[] = $client['Client']['id'];
            $clients[$k]['Client']['group_price_id'] = $this->setGruopPriceId($client['Client']['group_price_id']);
 
		}
		$this->loadModel('ItemsTag');
		$this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_CLIENT);
		$clients_tags = $this->ItemsTag->get_items_tags($clients_ids,ItemsTag::TAG_ITEM_TYPE_CLIENT);
		
		$this->set('clients_tags',$clients_tags);


		$this->setup_nav_data($clients);
        $this->set('categories', $this->Client->getCategoriesList(false,true));
        $this->_filterLinks();
        $this->set('title_for_layout',  __('Clients', true));
		if(IS_REST){
			$this->set('rest_items', $clients);
			$this->set('rest_model_name', "Client");
			$this->render("index");
		}
    }

    function get_client($id = null) {
        $ClientModel = GetObjectOrLoadModel('Client');
        $ClientModel->create();
        $this->loadModel('Post');
        $enable_multiple_addresses = settings::getValue(ClientsPlugin,'multiple_addresses');
        $owner = getAuthOwner();
        $conditions = [];
        if ($owner['staff_id'] != 0) {

            if (!check_permission(Clients_View_All_Clients)) {

                if (ifPluginActive(FollowupPlugin)){
                    $conditions[] = $this->getCanViewHisOwnClientsCondition($owner['staff_id']);
                }else{
                    $conditions['Client.staff_id'] = $owner['staff_id'];
                }

            }
        }
        $conditions['Client.id'] = $id;
        if ($enable_multiple_addresses) {
            $clients = $ClientModel->find('all', ['conditions' => $conditions, 'recursive' => 1]);
        } else {
            $clients = $ClientModel->find('all', ['conditions' => $conditions, 'recursive' => -1]);
        }
        return $this->processClients($clients);
    }
	function api_get_clients() {
        ini_set('memory_limit', '8G');
		$ClientModel = GetObjectOrLoadModel('Client');
        $this->loadModel('Post');
		$enable_multiple_addresses = settings::getValue(ClientsPlugin,'multiple_addresses');
        $owner = getAuthOwner();
        $conditions = [];
        if ($owner['staff_id'] != 0) {

            if (!check_permission(Clients_View_All_Clients)) {

                if (ifPluginActive(FollowupPlugin)){
                    $conditions[] = $this->getCanViewHisOwnClientsCondition($owner['staff_id']);
                }else{
                    $conditions[] = "( Client.staff_id = " . $owner['staff_id'] .  $this->Client->getPosDefaultClient(). " )";
                }

            }
        }
//        $recursive = 1;
//		if (!$enable_multiple_addresses) {
//            $recursive = -1;
//        }
        $clients = $ClientModel->find('all', ['conditions' => $conditions, 'recursive' => 1]);

        $clients = $this->processClients($clients);
        die(json_encode($clients, true));
	}

    private function processClients($clients)
    {
        return GetObjectOrLoadModel('Client')->processClients($clients);
    }


    function _filterLinks() {
        $params = $this->params['url'];
        unset($params['url'], $params['ext']);

        $filterLinks = array();

        foreach (array('name' => __('Client name', true), 'address' => __('Address', true), 'postal_code' => __('Postal code', true)) as $var => $title) {
            if (!empty($params[$var])) {
                $filterLinks[] = array(
                    'title' => $title,
                    'value' => $params[$var],
                    'var' => $var
                );
            }
        }

        if (isset($params['status'])) {
            if ($params['status'] == '0') {
                $filterLinks[] = array(
                    'title' => __('Status', true),
                    'value' => __('Active', true),
                    'var' => 'status'
                );
            } elseif ($params['status'] == '1') {
                $filterLinks[] = array(
                    'title' => __('Status', true),
                    'value' => __('Suspended', true),
                    'var' => 'status'
                );
            }
        }

        if (!empty($params['country_code'])) {
            $country = $this->Country->field('country', array('Country.code' => $params['country_code']));
            $filterLinks[] = array(
                'title' => __('Country', true),
                'value' => $country,
                'var' => 'country_code'
            );
        }

        $this->set(compact('filterLinks', 'params'));
    }

    //-----------------------------
    function owner_login_as($client_id = false) {
		if(isSuperAdmin()==false){
		  $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');	
		}
        $client = $this->Client->findById($client_id);
        if (!$client) {
            $this->flashMessage(__('Client not found', TRUE));
            $this->redirectToIndex();
        }

        $query = 'UPDATE clients SET last_login = NOW(), last_ip = "' . get_real_ip() . '" WHERE id = ' . $client_id;
        $this->Client->query($query);
        write_client_settings();
        $this->Client->reload_session($client);
        $this->add_actionline(ACTION_LOGIN_AS_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param4' => $client['Client']['client_number']));
        if (ifPluginActive(WebsiteFrontPlugin) && !isMobileApp()) {
            return $this->redirect('/client/dashboard');
	    }
	    return $this->redirect('/');
    }

    //---------------------------
    function _filter_params($params = false, $filters = array(), $passedModelName = false) {
        $conditions = parent::_filter_params($params , $filters, $passedModelName);
		
        if (!empty($conditions['Client.name LIKE'])) {
            // todo add link and change to mysqli
			$value = mysqli_real_escape_string($this->getMysqli(), str_replace('%','',$conditions['Client.name LIKE']));
            $conditions['OR'] = $this->Client->getSearchClientConditions($value);
            unset($conditions['Client.name LIKE']);
        }

        if (!empty($conditions['Client.address LIKE'])) {
            $conditions[] = "CONCAT_WS(' ', Client.address1, Client.address2, Client.city, Client.state) LIKE '" . mysql_escape_string($conditions['Client.address LIKE']) . "'";
            unset($conditions['Client.address LIKE']);
        }

        if (!empty($conditions['Client.status']) || (isset($conditions['Client.status']) && $conditions['Client.status'] == '0')) {
            $conditions[] = "Client.suspend = " . mysqli_real_escape_string($this->getMysqli() ,$conditions['Client.status']);
            unset($conditions['Client.status']);
        }
		
		if(count($conditions) > 0 )
		{
			
			//used in admin resposive list
			$_SESSION['conditioned'] = true ;
			
		}
		
		if (!$this->RequestHandler->isAjax()) {
		$this->write_filter_conditions($conditions);
		}
		
        return $conditions;
    }

    function owner_change_statuses() {
        $this->loadModel('FollowUpStatus');
        $this->loadModel('Post');

        $ids = isset($_POST['ids']) ? $_POST['ids'] : explode(',', $this->data['Client']['ids']);
        if (empty($ids[0])) {
            unset($ids[0]);
        }
        if (empty($ids)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('clients', true)));
            $this->redirectToIndex();
        }
        $this->set('ids', $ids);

        if (!empty($this->data) and !empty($this->data['Client']['action'])) {
            $FollowUpStatus = $this->FollowUpStatus->getList(Post::CLIENT_TYPE);
            $ids_array = explode(',', $this->data['Client']['ids']);
            foreach ($ids_array as $client_id) {
                $client = $this->Client->read(null, $client_id);
                if ($client) {
                    $this->Client->id = $client_id;
                    $this->Client->saveField('follow_up_status', $this->data['Client']['status']);
                    $client = $this->Client->read(null, $client_id);
                    $this->add_actionline(ACTION_UPDATE_CLIENT_STATUS, array('primary_id' => $client_id, 'secondary_id' => $client_id, 'param1' => $client['Client']['client_number'], 'param2' => $client['Client']['business_name'], 'param3' => $FollowUpStatus[$this->data['Client']['status']]));
                }
            }
            $this->flashMessage(__('Clients has been saved', true), 'Sucmessage');
            if (!empty($this->params['url']['box'])) {
                $this->set('box', true);
                $this->set('save', true);
            } else {
                $referral_url = $this->_get_referer_path();
                $this->redirect($referral_url);
            }
        }

        $statuses = $this->FollowUpStatus->getLisWithColor(Post::CLIENT_TYPE);
        $this->set('statuses', $statuses);
    }

    function owner_change_status($id, $status_id) {


        $this->loadModel('FollowUpStatus');
        $this->loadModel('Post');
        $FollowUpStatus = $this->FollowUpStatus->getList(Post::CLIENT_TYPE);

        $client = $this->Client->find(array('Client.id' => $id));

        if (!$client) {
            $this->flashMessage(__('Client not found', TRUE));
            $this->redirectToIndex();
        }

        $staff = getAuthStaff('id');
        $assignedStaffs = $this->Client->get_assigned_staff($id);

        if ((!check_permission(Edit_Delete_all_clients) && $client['Client']['staff_id'] != $staff && !in_array($staff, $assignedStaffs)) || !check_permission(Edit_And_delete_his_own_added_clients)) {

            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirectToIndex();
        }


        $this->Client->id = $id;
        $status = $this->FollowUpStatus->find('first',['conditions' => ['FollowUpStatus.item_type' => Post::CLIENT_TYPE, 'FollowUpStatus.id' => $status_id]]);
        if($status){
            $this->Client->saveField('secondary_follow_up_status', $status_id);
            if($status['FollowUpStatus']['is_primary']){
                $this->Client->saveField('follow_up_status', $status_id);
            }
            $this->add_actionline(ACTION_UPDATE_CLIENT_STATUS, array('primary_id' => $id, 'secondary_id' => $id, 'param1' => $client['Client']['client_number'], 'param2' => $client['Client']['business_name'], 'param3' => $FollowUpStatus[$status_id]));
            $this->flashMessage(__('Client status has been updated', true), 'Sucmessage', 'secondaryMessage');
            $this->redirect(array('action' => 'view', $id));
        }elseif($status_id == 0){
            $this->add_actionline(ACTION_UPDATE_CLIENT_STATUS, array('primary_id' => $id, 'secondary_id' => $id, 'param1' => $client['Client']['client_number'], 'param2' => $client['Client']['business_name'], 'param3' => null));
            $this->Client->saveField('follow_up_status', null);
            $this->Client->saveField('secondary_follow_up_status', null);
            $this->flashMessage(__('Client status has been updated', true), 'Sucmessage', 'secondaryMessage');
            $this->redirect(array('action' => 'view', $id));
        }else{
            $this->flashMessage(__('Status not found', true));
            $this->redirect(array('action' => 'view', $id));
        }
        die();
    }

    function owner_ajax_map_update(){
        if($this->RequestHandler->isAjax() && isset($_POST['location'])){
            $this->Client->id = $_POST['id'];
            $this->Client->saveField('map_location',$_POST['location']);
            die('success');
        }
    }

//---------------------
    function owner_view($id = null) {


        set_time_limit(9999999);
        ini_set("memory_limit", "20G");
        $condition = "and EntityAttachment.entity_field_key is null";
        $this->Client->bindAttachmentRelation('client',$condition);
        $this->Client->bindAttachmentRelationWithEntityAndFieldKey('client','clients.photo');
        $this->loadModel('Post');
        $this->loadModel('ItemStaff');
        $site_id = getAuthOwner('id');

        //get the client
        $client = $this->Client->find(array('Client.id' => $id));
//        debug($client);


        if (!$client) {
            $this->flashMessage(__('Client not found', TRUE));
            $this->redirectToIndex();
        }
        $this->loadModel('Invoice');

        $this->Invoice->applyBranch['onFind'] = false;
        $invoicesCount = $this->Invoice->find('count', ['conditions' => ['Invoice.client_id' => $id], 'recursive' => -1]);
        $ignoreCalculateClientBalance = $invoicesCount <= INVOICE_MAX_COUNT_FOR_VIEW;
        $this->Invoice->applyBranch['onFind'] = true;

        $salesOrderCount = $this->Client->get_sales_order_count($id);
        $this->set('salesOrderCount',$salesOrderCount);

        if(ifPluginActive(AccountingPlugin) && $invoicesCount <= INVOICE_MAX_COUNT_FOR_VIEW){
            $this->Client->adjust_and_pay($id);
        }
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');

            $hasAccess = false;

            $shareClients = settings::getValue(BranchesPlugin, 'share_clients');

            $count = $this->ItemStaff->find('count', [
                'conditions' => [
                    'ItemStaff.item_type' => ItemStaff::CLIENT_ITEM_TYPE,
                    'ItemStaff.item_id' => $id,
                    'ItemStaff.staff_id' => $staff,
                ],
                'applyBranchFind' => !$shareClients,
            ]);

            // Check if staff has permission to view all clients
            if (check_permission(Clients_View_All_Clients)) {
                $hasAccess = true;
            } 
            // Check if the client belongs to the staff and they have permission to view their own clients
            elseif (($client['Client']['staff_id'] == $staff && check_permission(Clients_View_his_own_Clients)) || (check_permission(Clients_View_his_own_Clients) && $count > 0)) {
                $hasAccess = true;
            }

            if (!$hasAccess) {
                $this->flashMessage(__("You are not allowed to view this client", true), 'Errormessage', 'secondaryMessage');
                $this->redirectToIndex();
            }
        }
        // warning suppress
        if(isset($_GET['nav_source']) && $_GET['nav_source'] == 'appointment')
        {
		
			$this->set('appointments_nav',$this->setup_nav_view($_GET['appointment_id'],'ClientAppointments.nav'));
			$this->set('appintment_id',$_GET['appointment_id']);
			$appointment_filter_url = $this->Session->read('appointments.owner_index');
			$this->set('appointment_filter_url',$appointment_filter_url);
			
        }
        else
		{
					$this->set('client_nav',$this->setup_nav_view($id));

		}	
		if (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $id ){
            if($ignoreCalculateClientBalance) {
                extract ( $this->Client->get_statement_data($id));
            }
        }

        $this->set("geocode_api",$this->config['txt.google_geocode_api']);
        $this->set("google_maps_api",$this->config['txt.google_maps_api']);

        $staff_id = getCurrentSite('staff_id');

        if(ifPluginActive(PrescriptionPlugin))
        {
            $this->loadModel('Prescription');
            $prescs = $this->Prescription->find('all',['conditions'=>['Prescription.staff_id'=>$staff_id,'Prescription.client_id'=>$id]]);
            $this->set('prescs',$prescs );

        }

//		dd($client);
        $this->set(compact('client'));
        App::import('Vendor', 'settings');
        $this->set('client_settings',settings::getPluginValues(ClientsPlugin));
        //Basic settings
        $this->loadModel('Country');
        $this->loadModel('Language');
        $this->loadModel('Currency');
        $this->set('country', $this->Country->get_country_code($client['Client']['country_code']));
        $this->set('language', $this->Language->get_language_code($client['Client']['language_code']));
        $this->set('currency', $this->Currency->get_currency_code($client['Client']['default_currency_code']));

        //Invoice conditions
        $conditions = array();
        $conditions['Invoice.type'] = 0;
        $conditions['Invoice.client_id'] = $id;
        //   $conditions['Invoice.site_id'] = $site_id;

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->set('staff_count', $this->Staff->find('count'));
        //Get invoices
//        $this->Invoice->applyBranch['onFind'] = false;
        if ((check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details))) {
            if (!check_permission(Invoices_View_All_Invoices)) {
                     $conditions[]['OR'] = [    //same behavior in invoice listing 
                        'Invoice.staff_id' => $owner['staff_id'],
                        'Invoice.sales_person_id' => $owner['staff_id'],
                ];
            }
    
            $invoices = $this->Invoice->find('all', array('conditions' => $conditions, 'order' => 'Invoice.date DESC', 'limit' => 20));
            $this->set('invoices', $invoices);

            $this->set('invoicesCount', $this->Invoice->find('count', array('conditions' => $conditions)));

            $Paymentconditions[]='InvoicePayment.payment_method <> "client_credit"';
            $Paymentconditions[]='InvoicePayment.payment_method <> "starting_balance"';
            if ($site['staff_id'] != 0) {

                if (!check_permission(Invoices_View_All_Invoices) && (check_permission(Invoices_View_Invoices_Details))) {
                    $conditions['InvoicePayment.staff_id'] = $site['staff_id'];
                }
            }
            $this->loadModel('InvoicePayment');
            $this->InvoicePayment->applyBranch['onFind'] = false;

            $firstPaymentCount=$this->InvoicePayment->find('count',array('conditions'=> array_merge($Paymentconditions, ['Invoice.client_id' => $id, 'InvoicePayment.client_id IS NULL'])));
            $secondPaymentCount=$this->InvoicePayment->find('count',array('conditions'=> array_merge($Paymentconditions, ['InvoicePayment.client_id' => $id])));
            $count = $firstPaymentCount + $secondPaymentCount;
            $this->set('paymentsCount',$count);
            $this->set('statuses', Invoice::getPaymentStatuses());

            //Estimates
            $conditions = array(
                'Invoice.client_id' => $id,
                'Invoice.type' => 3
            );
            if (!check_permission(Invoices_View_All_Invoices)) {
                $conditions['Invoice.staff_id'] = $owner['staff_id'];
            }
            $this->loadModel('InvoicePayment');
            $sb = $this->InvoicePayment->find('first', array('recursive' => -1, 'conditions' => ['payment_method' => 'starting_balance', 'InvoicePayment.client_id' => $id]));
            $this->set('sb',$sb);
            if (ifPluginActive(WorkOrderPlugin)) {
                $this->loadModel("WorkOrder");
                $work_orders = $this->WorkOrder->find('all', array('conditions' => ['WorkOrder.client_id' => $id], 'order' => 'WorkOrder.created DESC', 'limit' => 20));
                $this->set('workOrders', $work_orders);
                $this->set('workOrdersCount', $this->WorkOrder->find('count', ['conditions' => ['WorkOrder.client_id' => $id, 'WorkOrder.workflow_type_id IS NULL']]));
            }

            //Invoice Payments
            //$invoice_payments = $this->Invoice->InvoicePayment->find('all', array('conditions' => array('Invoice.client_id' => $id), 'limit' => 20, 'order' => 'InvoicePayment.created DESC'));
            //$this->set('invoice_payments', $invoice_payments);
            $this->set('ipCount', $this->Invoice->InvoicePayment->find('count', array('conditions' => array('Invoice.client_id' => $id))));
            $this->set('invoicePaymentStatus', InvoicePayment::getPaymentStatus());
            $this->set('invoicePaymentMethods', InvoicePayment::getPaymentMethods());
        }
        if (check_permission([ESTIMATES_VIEW_ALL_ESTIMATES, ESTIMATES_VIEW_HIS_OWN_ESTIMATES])) {
            $conditions = ['Invoice.client_id' => $id, 'Invoice.type' => 3];
            if (check_permission(ESTIMATES_VIEW_HIS_OWN_ESTIMATES) && !check_permission(ESTIMATES_VIEW_ALL_ESTIMATES) && $owner['staff_id'] != 0) {
                $conditions[] = "( Invoice.staff_id = '" . $owner['staff_id'] . "' OR Invoice.sales_person_id = '" . $owner['staff_id'] . "' )";
            }
            $estimates = $this->Invoice->find('all', array('conditions' => $conditions, 'order' => 'Invoice.id DESC', 'limit' => 20));
            $this->set('estimates', $estimates);
            $this->set('estimatesCount',$this->Invoice->find('count', array('conditions' => $conditions)));
            $this->set('estimateStatuses', Invoice::getEstimateStatuses());
        }
        //Email Logs


        $this->loadModel('EmailLog');
        if (check_permission(View_All_Activity_Logs) or check_permission(View_His_Activity_Logs)) {
            if (!check_permission(View_All_Activity_Logs)) {
                $email_logs = $this->EmailLog->find('all', array('conditions' => array('EmailLog.staff_id' => getAuthOwner('staff_id'), 'EmailLog.client_id' => $id), 'limit' => 20, 'order' => 'EmailLog.sent_date DESC'));
                $this->set('emailCount', $this->EmailLog->find('count', array('conditions' => array('EmailLog.staff_id' => getAuthOwner('staff_id'), 'EmailLog.client_id' => $id))));
                $this->set('email_logs', $email_logs);
            } else {
                $email_logs = $this->EmailLog->find('all', array('conditions' => array('EmailLog.client_id' => $id), 'limit' => 20, 'order' => 'EmailLog.sent_date DESC'));
                $this->set('emailCount', $this->EmailLog->find('count', array('conditions' => array('EmailLog.client_id' => $id))));
                $this->set('email_logs', $email_logs);
            }
        } else {
            $this->set('emailCount', 0);
        }

        $this->set('bookingsCount', 0);

        if (isPluginActivePure(BookingPlugin)) {
            $clientBookingsCount = $this->Invoice->find('count', ['conditions' => ['Invoice.type' => Invoice::BOOKING, 'Invoice.client_id' => $id]]);

            $this->set('bookingsCount', $clientBookingsCount);
        }

        if(isPluginActivePure(CLIENT_ATTENDANCE_PLUGIN)){
            $this->loadModel('ClientAttendanceLog');
            $this->set('clientAttendanceLogsCount', $this->ClientAttendanceLog->find('count', ['conditions' => ['ClientAttendanceLog.client_id' => $id,'ClientAttendanceLog.deleted_at is null']]));
        }
        //

        $this->loadModel('FollowUpReminder');
        $this->loadModel('ClientAppointment');
        $post_conditions = []; //array( 'Post.item_type' => Post::CLIENT_TYPE);
        // $client_invoices = $this->Invoice->find('list', ['conditions' => ['Invoice.client_id' => $id]]);

        // $invoice_posts = $this->Post->find('list', ['fields' => array('Post.item_id'), 'conditions' => ['Post.item_id' => array_keys($client_invoices), 'Post.client_permissions' => 1]]);

        if (!empty($invoice_posts)) {
            $post_conditions['OR'] = [ 'AND' => [ 'Post.item_id' => $id, 'Post.item_type' => Post::CLIENT_TYPE], 'Post.id' => array_keys($invoice_posts)];
        } else {
            $post_conditions['Post.item_type'] = Post::CLIENT_TYPE;
            $post_conditions['Post.item_id'] = $id;
        }
        if (!check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) && check_permission(View_His_Own_Notes_Attachments_Only)) {
            $post_conditions['Post.staff_id'] = $owner['staff_id'];
        }


        $note_actions = array();
        $ActionList = $this->Post->find('all', array('group' => 'Post.action_id', 'fields' => 'DISTINCT Post.action_id', 'order' => 'Post.date desc', 'conditions' => $post_conditions));
        foreach ($ActionList as $note_action) {
            $note_actions[] = $note_action['Post']['action_id'];
        }

        $this->loadModel('FollowUpAction');

        $note_action_list = $this->FollowUpAction->getList(Post::CLIENT_TYPE, false, array('FollowUpAction.id' => $note_actions));
        $this->set('note_action_list', $note_action_list);

        $note_followups = array();
        $FollowUpList = $this->Post->find('all', array('group' => 'Post.status_id', 'fields' => 'DISTINCT Post.status_id', 'order' => 'Post.date desc', 'conditions' => $post_conditions));
        foreach ($FollowUpList as $note_followup) {
            $note_followups[] = $note_followup['Post']['status_id'];
        }
        if (ifPluginActive(FollowupPlugin) && (check_permission(View_All_Attachments_And_Notes_For_All_Clients) || check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) || check_permission(View_His_Own_Notes_Attachments_Only)) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $id)) {
            $this->loadModel('ClientAppointment');
            $this->loadModel('FollowUpReminder');
            $this->loadModel('Post');
            $conditions = array();
            $items = $this->Invoice->find('list', ['fields' => ['Invoice.id', 'Invoice.id'],'conditions' => ['Invoice.client_id' => $id]]);
            $item_ids = [];
            foreach ($items as $i) {
                $item_ids = $i;
            }
            $or_condition[] = ['FollowUpReminder.item_id' => $item_ids, 'FollowUpReminder.item_type' => [FollowUpReminder::INVOICE_TYPE, FollowUpReminder::ESTIMATE_TYPE]];
            if (ifPluginActive(WorkOrderPlugin ) ){
                $this->loadModel ( 'WorkOrder');
                $wo_items = $this->WorkOrder->find('all', ['conditions' => ['client_id' => $id]]);
                $wo_item_ids = [];
                foreach ($wo_items as $i) {
                    $wo_item_ids[] = $i['WorkOrder']['id'];
                }
                $or_condition[] = ['FollowUpReminder.item_id' => $wo_item_ids, 'FollowUpReminder.item_type' => [ FollowUpReminder::WORK_ORDER_TYPE]];
            }
            
            $or_condition[] = ['FollowUpReminder.item_id' => $id, 'FollowUpReminder.item_type' => [FollowUpReminder::CLIENT_TYPE]];
            $conditions['OR'] = $or_condition;

//            $conditions['ClientAppointment.item_type'] = Post::INVOICE_TYPE;
//            $conditions['FollowUpReminder.status'] = ClientAppointment::Status_Scheduled;
//            $conditions['FollowUpReminder.item_id'] = $id;
            if (check_permission(View_All_Attachments_And_Notes_For_All_Clients)) {

            } else if (check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients)) {
                $conditions[] = ' ( FollowUpReminder.staff_id = ' . $owner['staff_id'] . ' OR  ' . $owner['staff_id'] . ' IN (SELECT staff_id FROM item_staffs WHERE item_staffs.item_type = ' . Post::CLIENT_TYPE . ' AND item_staffs.item_id=FollowUpReminder.item_id) )';
            } else if (check_permission(View_His_Own_Notes_Attachments_Only)) {
                $conditions['FollowUpReminder.staff_id'] = $owner['staff_id'];
            }
            $conditions['FollowUpReminder.status'] = 0 ;
            $statuses['-1'] = __('All', true);
            $statuses = $this->ClientAppointment->getStatuses();

            $this->set ( 'appointment_status_counts' , $this->FollowUpReminder->get_statuses_count (  null,null, FollowUpReminder::CLIENT_PARTNER_TYPE,$id  ) ) ;
            $this->set ( 'appointment_statuses' , [-1 => __("All",true )]+$statuses )  ;
			$appointments_count = $this->FollowUpReminder->getTypeAppointmentsCount(null,null, FollowUpReminder::CLIENT_PARTNER_TYPE, $id);

//            $appointments_count = $this->FollowUpReminder->find('count', array('conditions' => $conditions));
            $this->set('appointments_count', $appointments_count);
            $conditions[] = 'FollowUpReminder.date < "' . date('Y-m-d', strtotime('tomorrow')) . '"';
            $due_appointments_count = $this->FollowUpReminder->find('count', array('conditions' => $conditions));
            
            $this->set('due_appointments_count', $due_appointments_count);
        }

        $this->loadModel('FollowUpStatus');
        $note_followup_list = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, false, array('FollowUpStatus.id' => $note_followups));
        $this->set('note_followup_list', $note_followup_list);

        $note_staffs = array();
        $StaffList = $this->Post->find('all', array('group' => 'Post.staff_id', 'fields' => 'DISTINCT Post.staff_id', 'order' => 'Post.date desc', 'conditions' => $post_conditions));
        foreach ($StaffList as $note_sttaff) {
            $note_staffs[] = $note_sttaff['Post']['staff_id'];
        }


        $note_staff_list = $this->Staff->getList(true, array('Staff.id' => $note_followups));

        $this->set('note_staff_list', $note_staff_list);
        if(!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $id){
            $post_ids = $this->Post->find('list', array('fields' => 'Post.id','conditions' => $post_conditions));
            $this->set('post_count', count($post_ids));
        }
//        $this->set('appointment_count', $this->ClientAppointment->find('count', array('order' => 'ClientAppointment.date asc', 'conditions' => array(/*'ClientAppointment.status' => 0,*/ 'ClientAppointment.item_id' => $id, 'ClientAppointment.item_type' => Post::CLIENT_TYPE))));
        $this->set('last_appointment', $this->ClientAppointment->find('first', array('order' => 'ClientAppointment.date asc', 'conditions' => array('ClientAppointment.status' => 0, 'ClientAppointment.item_id' => $id, 'ClientAppointment.item_type' => Post::CLIENT_TYPE))));

        $actions = $this->FollowUpAction->getList(Post::CLIENT_TYPE);

        $this->set('actionlist', $actions);
//        //Payments summary
//        $starting_balance = array();
//        $page = $this->params['url']['page'] ? intval($this->params['url']['page']) : 1;
//        $per_page = 300;
//        $allt = "select 'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,due_after,'payment_method',currency_code from invoices where client_id='$id' and type in(0,5,6) and draft <> 1 UNION select 'payment',id,null,null,client_id,date,status,invoice_id,amount,null,payment_method,currency_code from invoice_payments where (invoice_id in(select id from invoices where client_id='$id' and type in(0,5) and draft <> 1) or client_id='$id') and payment_method <> 'client_credit' ORDER BY `date` DESC";
//        $starting_invoice_with_payment = $this->Client->query($allt . " LIMIT " . ($per_page * ($page - 1)) . " OFFSET 0");
//        //print_r($starting_invoice_with_payment);
//        foreach ($starting_invoice_with_payment as $t) {
//            $t = $t[0];
//            if ($t['invoice'] == 'payment' or ($t['invoice'] == 'invoice' and in_array($t['type'], array(Invoice::Credit_Note)))) {
//                $t['summary_total'] = $t['summary_total'] * -1;
//            }
//            $starting_balance[$t['currency_code']] +=$t['summary_total'];
//        }
//        // print_r($starting_balance);
//        $this->set('starting_balance', $starting_balance);
//        $invoice_with_payment = $this->Client->query($allt . " LIMIT $per_page OFFSET " . ($per_page * ($page - 1)));
//        $count = $this->Client->flat_query_results("select count(*)as cc from ( $allt )aaa");
//        $this->set('transaction_count', $count[0]['cc']);
//        $this->set('current_page', $page);
//        $this->set('count', ceil($count[0]['cc'] / $per_page));
        $builder = getEntityBuilder();
        $entity = $builder->buildEntity(EntityKeyTypesUtil::CLIENT_ENTITY_KEY);

        $this->loadModel('LocalEntity');
        $this->set('tabActions', ViewChildEntitiesHelper::setTabs($id,getAuthOwner('staff_id'), $entity));
        $this->set('entity', $entity);
        $this->set('InvoiceTypeList', Invoice::getInvoiceTypeList());
        $this->loadModel('InvoicePayment');
        $this->set('payment_methods', InvoicePayment::getPaymentMethods());
        if (ifPluginActive(WorkflowPlugin)) {
            $this->set('workFlowTabs', $this->getWorkFlowTabs($id));
        }
        // $this->set('invoice_with_payment', $invoice_with_payment);
//        $summaries = $this->Client->query('SELECT I.currency_code, ROUND(SUM(I.summary_total), 2) as `total`, ROUND(SUM(I.summary_paid), 2) as `paid`, ROUND(SUM(I.summary_unpaid), 2) as unpaid FROM invoices as I WHERE I.client_id = ' . intval($id) . ' AND I.type = 0 AND I.draft <> 1 GROUP BY I.currency_code ORDER BY I.currency_code');

//Is not set using get_statement_data
        if (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $id && $ignoreCalculateClientBalance){
            $journal_account_id=$this->Client->get_journal_id($id);
            if($journal_account_id==0){
                $journal_account_id='NULLx';
            }
            $this->set ( $this->Client->get_statement_data($id)) ;
            $allt = "select 1 as my_order , created,'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,due_after,'payment_method',currency_code,'description' from invoices where client_id='$id' and type in(0,5,6) and draft <> 1
                UNION select 1 as my_order , created,'Starting Balance',id,null,0,'$id',date,status,id as invoice_id,amount,'due_after','payment_method',currency_code,null from invoice_payments where client_id='$id' and payment_method = 'starting_balance'
                UNION select 2 as my_order,created,'payment',id,(select no from invoices where id=invoice_id),null,client_id,date,status,invoice_id,amount,null,payment_method,currency_code,null from invoice_payments where (invoice_id in(select id from invoices where client_id='$id' and type in(0,5,16) and draft <> 1) or (client_id='$id' AND invoice_id is null)) and payment_method <> 'client_credit' and payment_method <> 'starting_balance' and status=1 
                UNION SELECT 1 AS my_order,JT.`created`,'journal',J.`id`,null,null,'$id',JT.`created`,1,JT.`id`,ROUND(JT.currency_credit-JT.currency_debit,2) AS `amount`,null,'none',JT.currency_code,JT.description FROM `journal_transactions` AS `JT`  LEFT JOIN `journals` AS `J` ON(`JT`.`journal_id` = `J`.`id`) WHERE J.draft = 0 and ((J.entity_type not in('invoice','invoice_payment','refund_receipt','credit_note', 'year_opening_balance','year_closing_balance')) OR (J.entity_type in ('invoice','refund_receipt','credit_note') and J.entity_id not in (select id from invoices where client_id = $id) ) ) and JT.journal_account_id={$journal_account_id}
                UNION select 3 as my_order,created,'Refund Payment',id,(select no from invoices where id=invoice_id),'6-1',client_id,date,status,'refund',amount,null,payment_method,currency_code,null from invoice_payments where status=1 and (invoice_id in(select id from invoices where client_id='$id' and type in(6) and draft <> 1))  ORDER BY `date` , my_order ASC,`created` ASC";
                $transaction_count = $this->Client->flat_query_results("select count(*)as cc from ( $allt )aaa");

                $this->set('transaction_count', $transaction_count['0']['cc']);
        }



        //        $allt = "select 'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,due_after,'payment_method',currency_code from invoices where client_id='$id' and type in(0,5,6) and draft <> 1  UNION select 'payment',id,null,null,client_id,date,status,invoice_id,amount,null,payment_method,currency_code from invoice_payments where (invoice_id in(select id from invoices where client_id='$id' and type in(0,5) and draft <> 1) or client_id='$id') and payment_method <> 'client_credit' and status=1   ORDER BY `date` ASC";

        //Quick Info
        $this->set('issuedCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.draft !=' => 1), 'recursive' => -1)));
        $this->set('dueCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.date <=' => date('Y-m-d 23:59:59'), 'Invoice.draft !=' => 1, 'Invoice.payment_status !=' => INVOICE_STATUS_PAID))));
        $this->set('overdueCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.draft !=' => 1, 'DATE_ADD(date, INTERVAL Invoice.due_after DAY) < ' => date('Y-m-d'), 'Invoice.payment_status !=' => INVOICE_STATUS_PAID))));

        $this->set('subscriptionCount', $this->Invoice->find('count', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 1, 'Invoice.active' => 1, 'OR' => array('OR' => array('Invoice.subscription_max_repeat' => 0, 'Invoice.subscription_max_repeat IS NULL'), 'Invoice.subscription_repeated <= Invoice.subscription_max_repeat')))));

        $this->set('lastPayment', $this->Invoice->InvoicePayment->find('first', array('conditions' => array('Invoice.client_id' => $id, 'InvoicePayment.status' => PAYMENT_STATUS_COMPLETED), 'recursive' => 0, 'order' => 'InvoicePayment.created DESC', 'fields' => 'InvoicePayment.id, InvoicePayment.created, InvoicePayment.amount, InvoicePayment.currency_code, Invoice.no, Invoice.id, InvoicePayment.code')));
        $this->set('lastInvoice', $this->Invoice->find('first', array('conditions' => array('Invoice.client_id' => $id, 'Invoice.type' => 0, 'Invoice.draft !=' => 1), 'order' => 'Invoice.created DESC', 'recursive' => -1)));
        $this->set('lastEmail', $this->EmailLog->find('first', array('conditions' => array('EmailLog.client_id' => $id), 'order' => 'EmailLog.sent_date DESC', 'fields' => 'EmailLog.id, EmailLog.sent_date')));
        if($ignoreCalculateClientBalance) {

            $overdueInvoices = $this->Client->getOverDue($id);
            $this->set('overduelist', $overdueInvoices);
            $OpenInvoices = $this->Client->getUnpaid($id);
            $Creditlist = $this->Client->getAllCredit($id);
            $this->set('Openlist', $OpenInvoices);
            $this->set('Creditlist', $Creditlist);
        }

        //  $this->set('post_count', $this->Post->find('count', array('conditions' => array('Post.item_type' => Post::CLIENT_TYPE, 'Post.item_id' => $id))));

        $this->set('staff_assignd', $this->ItemStaff->find('all', array('conditions' => array('ItemStaff.item_type' => ItemStaff::CLIENT_ITEM_TYPE, 'ItemStaff.item_id' => $id))));
        $this->set('is_assignd', $this->ItemStaff->hasAny(array('ItemStaff.staff_id' => getAuthOwner('staff_id'), 'ItemStaff.item_type' => ItemStaff::CLIENT_ITEM_TYPE, 'ItemStaff.item_id' => $id)));
        if (ifPluginActive(FollowupPlugin)) {


            $FollowUpStatus = $this->FollowUpStatus->getLisWithColorList(Post::CLIENT_TYPE);
            $this->set('FollowUpStatus', $FollowUpStatus);
        }


        $this->set('title_for_layout',  __('View Client', true));
        $this->loadModel('ActionLine');
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Client', array('primary_id' => $id));

        $action_list = $timeline->getClientActionsList();


        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $clientt_actions[$key] = $action;
            }
        }
        App::import('Vendor', 'settings');
        $this->data['Tabs'] = json_decode(settings::getValue(0, "clients_api"), true);

        $client_data = $client['Client'];

        foreach ($client_data as $k => $v) {
            $client_data['$' . $k] = $v;
            unset($client_data[$k]);
        }
        $string = str_replace(array_keys($client_data), array_values($client_data), json_encode($this->data['Tabs']));
        $this->data['Tabs'] = json_decode($string, true) ?? [];
        usort($this->data['Tabs'], function ($a, $b) {
            return ($a['order'] < $b['order']) ? -1 : 1;
        });
        $this->set('actions', $clientt_actions);




        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getActivePrintableTemplates('client');

        $this->set('has_templates', false);
        if(!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

		App::import('Vendor', 'notification_2');
		
		NotificationV2::view_notificationbyref($id,NotificationV2::NOTI_ASSIGN_CLIENT);
		$Journal = GetObjectOrLoadModel('Journal');
        $account = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::CLIENT_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id]);
        $this->set('account', $account);
		$this->loadModel('ItemsTag');
		$this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_CLIENT);
		$tags = $this->ItemsTag->get_item_tags($id,ItemsTag::TAG_ITEM_TYPE_CLIENT,true);
		$this->set('tags',$tags);
		
		$has_permission_to_distribute = false;
		if (!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && check_permission(Invoices_Add_Payments_to_All))
            $has_permission_to_distribute = true;
		$this->set('has_permission_to_distribute', $has_permission_to_distribute);

        // show client barcode if barcode setting is enabled
        $barcodeSetting = settings::getValue(ClientsPlugin, 'barcode');
        if ($barcodeSetting == 1) {
            $barcode = OIBarcode::getAutoBarcode(CLIENT_BARCODE, $client['Client']['id']);
            $this->set(compact('barcode'));
        }

        if (Settings::getValue(ClientsPlugin, 'client_integrate_social_media') && $client['Client']['phone2']) {
            $this->setShareWithSocialMediaData($client);
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('client');

        $ownClients = $this->getHisOwnClients();
        $this->set('ownClients', $ownClients);

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);
        $this->set('has_custom_data', !EntityHasLegacyCustomFields::check('clients'));

        $this->set('forms', $this->createAdditionalFieldsFormHandlerInstance()->show($id));

        $this->loadModel('ActivityLogRelation');
        $activityLog = $this->ActivityLogRelation->find('first', ['conditions' => ['ActivityLogRelation.entity_id' => $client['Client']['id'], 'ActivityLogRelation.entity_key' => 'client']]);
        $this->set('showActivityLog', $activityLog);
        
    }


    private function setShareWithSocialMediaData($client) {
        $this->set('shareWithSocialMedia', 1);
        $placeholders = PlaceHolder::client_place_holder($client);

        $messageTemplates = Settings::getValue(ClientsPlugin, 'social_media_share_message_templates');
        $messageTemplates = json_decode($messageTemplates, true);
        $socialMediaMessageData = [];
        foreach ($messageTemplates as $key => $messageTemplate) {
            if (is_array($messageTemplate)) { // handles custom platforms
                $linkTemplate = $messageTemplate['link_template'];
                $messageTemplate = $messageTemplate['message'];
            }
            $message = PlaceHolder::replace($messageTemplate, array_keys($placeholders), $placeholders);
            $socialMediaMessageData[] = [
                'link' => SocialMediaLinkCreatorFactory::make($key, $linkTemplate ?? null)->getLink($client['Client']['phone2'], $message, $key),
                'label' => $key,
            ];
        }
        $this->set('socialLinks', $socialMediaMessageData);
//            $this->set('WhatsAppLink', $this->Invoice->getInvoiceClientWhattsapLink($invoice, 'invoice'));
    }

    /**
     * @param $barcode
     * @return array|bool
     */
    public function owner_search_by_barcode($barcode)
    {
        return OIBarcode::searchBarcode($barcode);
    }

    /**
     * @return void
     */
    public function owner_client_has_membership()
    {
        $barcode = $_GET['q'];
        $data = $this->owner_search_by_barcode($barcode);
        if ($data == false) {
            die(json_encode(['message' => __('Client Not Found', true)]));
        }
        $client = $data['data'];
        $this->loadModel('Membership');
        $membership = $this->Membership->find('first', ['conditions' => ['Membership.client_id' => $client['Client']['id'], 'Membership.deleted_at IS NULL']]);
        if ($membership) {
            die(json_encode(['message' => 'Membership found', 'membership' => $membership['Membership']]));
        } else {
            die(json_encode(['message' => 'Membership not found']));
        }
    }

    /**
     * @return void
     */
    public function owner_client_attendance_by_barcode()
    {
        $barcode = $this->params['url']['q'];
        $data = $this->owner_search_by_barcode($barcode);
        if ($data == false) {
            die(json_encode(['message' => __('Client Not Found', true)]));
        }
        $client = $data['data'];
        die(json_encode(['client' => $client['Client']]));
    }

    public function owner_add_bulk()
    {
        $results = [];

        $invoices = $this->data;

        foreach ($invoices as $invoice) {
            $this->data = $invoice;
            $results[] = $this->owner_add(null, false);
        }

        die(json_encode($results));
    }

    function owner_get_appointment_statuses_count($clientId)
    {
        $this->loadModel('FollowUpReminder');
        $this->loadModel('ClientAppointment');
        $statuses = $this->ClientAppointment->getStatuses();
        $statuses = ["-1" => __("All",true )] + $statuses;
        $counts = $this->FollowUpReminder->get_statuses_count(null, null, FollowUpReminder::CLIENT_PARTNER_TYPE, $clientId); 
        $response = [];
        foreach($statuses as $key => $status) {
            $response[$status] = $counts[$key] ;
        }        
        die(json_encode($response));
    }

    /**
     * @param bool $addOneBehaviour when set to false it returns the result instead of printing them, used owner_add_bulk
     */
    function owner_add($id = null, $addOneBehaviour = true) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $enable_multiple_addresses = settings::getValue(ClientsPlugin,'multiple_addresses');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->client_js_labels);
        $this->loadModel("Country");
        $this->loadModel("Site");
        App::import('Vendor', 'settings');
        App::import('Vendor', 'AutoNumber');
        App::import('Vendor', 'notification');
        App::import('Vendor', 'sites_local');
        $countrires = $this->Country->getCountryList();
        $bnf =  Localize::get_business_number_fields();
        foreach ($countrires as $key => $country) {
            if (!isset($bnf[$key])) {
                $bnf[$key] = ["bn2" => ["label" => "VAT Number"]];
            }
        }
        $this->set('bnf', $bnf);

        if (!check_permission(Clients_Add_New_Client)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'danger', 'secondaryMessage');
            $this->redirectToIndex();
        }


        $client_settings = settings::getPluginValues(ClientsPlugin);
        $this->set('client_settings',$client_settings);
        if($client_settings['gender'] == '1'){
            $genders = $this->Client->get_gender_list();
            $this->set('genders',$genders);
        }

        $this->set("geocode_api",$this->config['txt.google_geocode_api']);
        $this->set("google_maps_api",$this->config['txt.google_maps_api']);

        $site = getCurrentSite();

        if($client_settings['birth_date'] == '1'){
            $formats = getDateFormats('std');
            $dateFormat = $formats[$site['date_format']];
        }

        // Check site limitation
        $ajax = $this->RequestHandler->isAjax();
        if (checkIfLimitExistsV2($site['id'], LimitationUtil::CLIENTS_COUNT)) {
              $limit_redirect= ['action' => 'index'];
              if (!\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('clients')) {
                $limit_redirect = '/v2/owner/entity/client/list';
            }
            $this->handleSiteLimit(
                checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::CLIENTS_COUNT),
                $limit_redirect,
                $ajax
            );
        } else {
            $this->loadModel('Site');
            $result = $this->Site->check_add_client_limit($site['id']);
            if (empty($result['status'])) {
                $this->add_stats(STATS_ERROR_CLIENT_LIMITATION_REACHED, array($result['message']));
                if (!$addOneBehaviour) {
                    return ["message" => $result['message']];
                }

                if(IS_REST) $this->cakeError("error402", ["message"=>$result['message']]);
                if ($ajax) {
                    die(json_encode(array(
                        'message' => $result['message'],
                        'class' => 'Errormessage',
                        'error' => true
                    )));
                } else {
                    $this->flashMessage($result['message']);
                    $this->redirectToIndex();
                }
            }
        }
        $condition = "and EntityAttachment.entity_field_key is null";
        $this->Client->bindAttachmentRelation('client',$condition);

        if (!empty($this->data)) {
            if(!empty($this->data['Client']['longitude']) && !empty($this->data['Client']['latitude']) && !empty($this->data['Client']['saved_map_zoom'])){
                $this->data['Client']['map_location'] = $this->data['Client']['longitude'] . "," . $this->data['Client']['latitude'] . "," . $this->data['Client']['saved_map_zoom'] ;
                unset($this->data['Client']['longitude']);
                unset($this->data['Client']['latitude']);
                unset($this->data['Client']['saved_map_zoom']);
                
            }
            $this->data['Client']['staff_id'] = getAuthOwner('staff_id');

            if (is_array($this->data['Client']['category'])) {
                $this->data['Client']['category'] = $this->data['Client']['category'][0];
            }
            if (empty($this->data['Client']['category'])) {
                $this->data['Client']['category'] = "";
            }
            if (isset($this->data['Client']['skip_email'])) {
                unset($this->Client->validate['email']);
                unset($this->Client->validate['phone2']);
            }
            if($this->data['Client']['data[WorkOrder'] && empty($this->data['Client']['email'])) {

                $this->data['Client']['is_offline']=1;
                unset($this->Client->validate['email']);
                unset($this->Client->validate['phone2']);
            }

            if ($this->data['Client']['is_offline'] == "1") {
                unset($this->data['Client']['password']);
                unset($this->data['Client']['confirm_password']);
            }
            if(IS_REST && !isset($_GET['verify_phone2'])){
                unset($this->Client->validate['phone2']);
            }

			if (empty($this->data['Client']['email']) && !isset($this->data['Client']['is_offline']) ) {
				$this->data['Client']['is_offline'] = 1;
			}

            if(IS_REST){
                $this->data['Client']['type'] = $this->getClientType($this->data);
            }
            $this->Client->create();
            $this->Client->set($this->data);
            if ($ajax or (
                (isset($this->params['url']['ignore_custom_fields']) && $this->params['url']['ignore_custom_fields'] == true) & $this->Client->validates()
                OR ( $additionalFieldsFormHandler->validate($this->data) &
                $this->Client->validates()
                && !$ajax)
                )
            ) {   
                if ($ajax) {
                    $additionalFieldsFormHandler->validate($this->data);
                    $this->Client->validationErrors = array_merge($this->Client->validationErrors, $additionalFieldsFormHandler->getValidationErrors());
                    if($this->Client->validationErrors) {
                        $messages = $this->getFirstCustomValidationMessage($this->Client->validationErrors);
                        $field = array_keys($messages)[0];
                        $error = array_shift($messages)[0];
                    
                        $message = sprintf('<div class="alert alert-danger Errormessage" <strong>%s</strong>: %s</div>',$field,$error);

                        die(json_encode(array('message' => $message,'client' => false, 'error' => true, 'errors' => $this->Client->validationErrors)));
                    }
                }


                if($this->data['Client']['birth_date']){
                    $this->data['Client']['birth_date'] = $this->Client->formatDateTime($this->data['Client']['birth_date']);
                } else {
                    unset($this->data['Client']['birth_date']);
                }

                if(!empty($this->data['Client']['starting_balance']))
                {
                    $this->validateOpenDayWithValidationError($this->data,'Client', 'starting_balance_date');
                }
                if(empty($this->Client->validationErrors))
                {
                    if(
                        isset($this->data['JournalAccountRoute']['account_id']) &&
                        !empty($this->data['JournalAccountRoute']['account_id'])
                    ) {
                        $this->Client->disableAutoJournal = true;
                    }
                    $result = $this->Client->saveClient($this->data);
                    $client_id = $this->Client->getLastInsertID();
                }else{
                    $result['status'] = false;
                }
                // Store client addresses
                if ($enable_multiple_addresses) {
                    $this->loadModel('Address');
                    foreach ($this->data['Address'] as $address) {
                        $this->Address->create();

                        $record['Address'] = array_merge($address, [
                            'entity_id' => $client_id,
                            'entity_type' => 'Client',
                            'country_code' => $result['data']['country_code'],
                            'created' => date("Y:m:d H:i:s"),
                            'modified' => date("Y:m:d H:i:s"),
                        ]);

                        $this->Address->save($record);
                    }
                }

                $this->Cookie->write('last_is_offline', $this->data['Client']['is_offline']);


                if ($result['status']) {
                    $attachments = $this->data['Client']['attachment'];
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                    
                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save('client', $this->Client->id, $imagesIds); 
                    }

                    $additionalFieldsFormHandler->store($client_id, $this->data);

                    izam_resolve(ClientService::class)->insert(ServiceModelDataTransformer::transform($result['data'], 'Client'));
//                    if (!empty($this->data['JournalAccountRoute']['account_id'])) {
//                        JournalAccount::updateEntityTypeAndId($this->data['JournalAccountRoute']['account_id'], "client",  $client_id);
//                    }
                    RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_CLIENT)
                        ->save($this->data['JournalAccountRoute'], $this->Client->id);
                    if(isset($this->Client->CustomModelError) && $this->Client->CustomModelError==false){
                        $this->flashMessage(__("Custom Fields can not be saved", true), 'Errormessage', 'secondaryMessage');
                    }
                    $this->loadModel('ItemStaff');
                    $staff_id=getAuthOwner('staff_id');
                    if($staff_id!=0) {
                        $this->ItemStaff->assign_staff_members([$staff_id],$client_id,ItemStaff::CLIENT_ITEM_TYPE);
                    }
                    $this->add_actionline(ACTION_ADD_CLIENT, array('primary_id' => $client_id, 'secondary_id' => $client_id, 'param2' => $this->data['Client']['business_name'], 'param3' => $this->data['Client']['email'], 'param4' => $result['data']['client_number'], 'param5' => $this->data['Client']['phone1'] . ' ' . $this->data['Client']['phone2']));
                    settings::setValue(0, 'last_client_type', $this->data['Client']['type']);
                    if(!empty($this->data['Client']['starting_balance'])){
                        $currencyCode = $ClientData['default_currency_code'] ?? getAuthOwner('currency_code');

                        $ClientData=$this->data['Client'];
                        $open_balance_result=$this->Client->addOpenBalance(
                            $this->Client->id,
                            (float) $ClientData['starting_balance']*-1,
                            getAuthOwner('staff_id'),
                            $currencyCode, 
                            $ClientData['starting_balance_date']
                        );
                        $this->loadModel('InvoicePayment');
                        $invoicePayment=$this->InvoicePayment->read(null,$open_balance_result['payment_id']);
                        $this->add_actionline(ACTION_ADD_CLIENT_OPENING_BALANCE,array('primary_id' => $open_balance_result['payment_id'],'secondary_id' => $invoicePayment['InvoicePayment']['client_id'],'param1' => $invoicePayment['InvoicePayment']['amount'] * -1,'param2' => $invoicePayment['InvoicePayment']['status'],'param2' => $invoicePayment['Client']['business_name'],'param4' => $invoicePayment['Client']['client_number']));

                    }

                    $this->add_stats(STATS_CREATE_CLIENT, array($client_id, $ajax));

                    if (!empty($result['data']['send_notify']) && empty($result['data']['suspend'])) {
                        $this->set('user', $result['data']);
                        $mailResult = $this->_send_email_to_user($result['data']);
                        if (!$ajax && !IS_REST) {
                            if ($mailResult) {
                                $this->data = array();
                                $this->flashMessage(__('Your message has been sent.', true), 'Sucmessage', 'secondaryMessage');
                            } else {
                                $this->flashMessage(__("Cannot send email", true), 'Errormessage', 'secondaryMessage');
                            }
                        }
                    }
                    if (!$addOneBehaviour) {
                        return [
                            "result" => "successful",
                            "code" => 202,
                            "id" => $client_id,
                            "client_number" => $result['data']['client_number']
                        ];
                    }

					if (isset($_GET['from_pos'])) {
						$this->set('action_mode', 'add');
						$this->set('client_data', $this->get_client($result['data']['id']));
						$this->render('owner_add_pos');
						return;
					}

					if (isset($_GET['from_invoice'])) {
						$this->set('action', 'add_new_advanced_client');
						$this->set('data', $result['data']);
                        $this->render('/elements/result_js');
						return;
					}

					if(IS_REST){
						$this->set('id', $client_id);
						$this->set('client_number', $result['data']['client_number']);
						$this->render('clients/created');
						return;
					}
                    if ($ajax) {
                        $client = $result['data'];
                        $client['id'] = $client_id;

                        $view = new View($this, false);
                        $client['full_address'] = $view->element('format_address_html', $client);


                        die(json_encode(array('error' => false, 'errors' => false, 'client' => $client)));
                    } else {
                        if (!$addOneBehaviour) {
                            return ['status' => true,'id' => $client_id];
                        }

						if(IS_REST){
							die(json_encode(['status' => true,'id' => $client_id]));
						}
						if(isset($this->data['Client']['membership_redirect']) && $this->data['Client']['membership_redirect']){
							$this->redirect('/v2/owner/memberships/create?client_id='.$client_id);
						}
						if(isset($this->data['Client']['workflow_redirect']) && $this->data['Client']['workflow_redirect']){
							$this->redirect("/v2/owner/entity/{$this->data['Client']['workflow_redirect']}/create?client_id={$client_id}");
						}
                        $this->flashMessage(sprintf(__('%s has been saved', true), __('Client', true)), 'Sucmessage');
						$this->redirect(array('action' => 'view', $client_id));
                        
                    }
                } else {
                    if(!empty($this->data['Client']['attachment'])){                       
                        $filesId = explode(',',$this->data['Client']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                    }
                    $this->add_stats(STATS_CREATE_CLIENT_ERROR, array(serialize($this->Client->validationErrors), serialize($this->Client->data)));
                    if (!$addOneBehaviour) {
                        return [
                            'message' => $result['message'],
                            'validation_errors' => $this->Client->validationErrors
                        ];
                    }
					if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Client->validationErrors]);
                    if ($ajax) {
                        if (isset($this->Client->validationErrors['email'])) {
                            if (!empty($this->data['Client']['email'])) {
                                $row = $this->Client->find('first', array('order' => 'Client.client_number desc', 'conditions' => array('Client.email' => $this->data['Client']['email'])));
                                if ($row) {
                                    $client_url = Router::url(array('action' => 'view', $row['Client']['id']));
                                    $client_edit_url = Router::url(array('action' => 'edit', $row['Client']['id']));
                                    $old_client_id = $row['Client']['id'];
                                    $fmsg = sprintf(__('<h4>' . __('This email is already entered for an', true) . ' <a class="limit-link text-u-l" target="_blank" href="%s">' . __('Existing client', true) . '</a></h4><div class="clearfix"></div> <a class="add_as_newclient rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Add as a new client', true) . '</a> <a  data-id="%s" data-url="%s" data-view-url="%s" class="Update_Existed_Client rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Update the existed client details', true) . '</a> <a class="rounded-item button-2 btn btn-default btn-sm zoom-fix  " onclick="CancelClient()" href="#">' . __('Cancel', true) . '<a/>', true), $client_url, $old_client_id, $client_edit_url, $client_url, Router::url(array('action' => 'index')));
                                } else {
                                    $fmsg = '';
                                }
                            } else {
                                $fmsg = '';
                            }
                            //$this->flashMessage($fmsg);
                        }

                        if (isset($this->Client->validationErrors['phone2'])) {
                            if (!empty($this->data['Client']['phone2'])) {
                                $row = $this->Client->find('first', array('order' => 'Client.client_number desc', 'conditions' => array('Client.phone2' => $this->data['Client']['phone2'])));
                                if ($row) {
                                    $client_url = Router::url(array('action' => 'view', $row['Client']['id']));
                                    $client_edit_url = Router::url(array('action' => 'edit', $row['Client']['id']));
                                    $old_client_id = $row['Client']['id'];
                                    $fmsg = sprintf(__('<h4>' . __('This phone number is already entered for an', true) . ' <a class="limit-link text-u-l" target="_blank" href="%s">' . __('Existing client', true) . '</a></h4><div class="clearfix"></div> <a class="add_as_newclient rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Add as a new client', true) . '</a> <a  data-id="%s" data-url="%s" data-view-url="%s" class="Update_Existed_Client rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Update the existed client details', true) . '</a> <a class="rounded-item button-2 btn btn-default btn-sm zoom-fix  " onclick="CancelClient()" href="#">' . __('Cancel', true) . '<a/>', true), $client_url, $old_client_id, $client_edit_url, $client_url, Router::url(array('action' => 'index')));
                                } else {
                                    $fmsg = '';
                                }
                            } else {
                                $fmsg = '';
                            }
                            //$this->flashMessage($fmsg);
                        }
                        die(json_encode(array('fmsg' => $fmsg, 'client' => false, 'error' => true, 'errors' => $this->Client->validationErrors)));
					}else {

                        if (isset($this->Client->validationErrors['email'])) {
                            $row = $this->Client->find('first', array('order' => 'Client.client_number desc', 'conditions' => array('Client.email' => $this->data['Client']['email'])));
                            $client_url = Router::url(array('action' => 'view', $row['Client']['id']));
                            $client_edit_url = Router::url(array('action' => 'edit', $row['Client']['id']));
                            $old_client_id = $row['Client']['id'];
                            $fmsg = sprintf(__('<p>' . __('This email is already entered for an', true) . ' <a class="limit-link text-u-l" target="_blank" href="%s">' . __('Existing client', true) . '</a></p><div class="clearfix"></div> <a class="add_as_newclient rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Add as a new client', true) . '</a> <a  data-id="%s" data-url="%s" data-view-url="%s" class="Update_Existed_Client rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Update the existing client details', true) . '</a> <a class="rounded-item button-2 btn btn-default btn-sm zoom-fix  " onclick="CancelClient()" href="%s">' . __('Cancel', true) . '<a/>', true), $client_url, $old_client_id, $client_edit_url, $client_url, Router::url(array('action' => 'index')));
                            $this->flashMessage($fmsg, 'alert alert-danger m-t', 'secondaryMessage');

                        }
                        if (isset($this->Client->validationErrors['phone2'])) {

                            $row = $this->Client->find('first', array('order' => 'Client.client_number desc', 'conditions' => array('Client.phone2' => $this->data['Client']['phone2'])));
                            $client_url = Router::url(array('action' => 'view', $row['Client']['id']));
                            $client_edit_url = Router::url(array('action' => 'edit', $row['Client']['id']));
                            $old_client_id = $row['Client']['id'];
                            $fmsg = sprintf(__('<p>' . __('This phone1 is already entered for an', true) . ' <a class="limit-link text-u-l" target="_blank" href="%s">' . __('Existing client', true) . '</a></p><div class="clearfix"></div> <a class="add_as_newclient rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Add as a new client', true) . '</a> <a  data-id="%s" data-url="%s" data-view-url="%s" class="Update_Existed_Client rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Update the existing client details', true) . '</a> <a class="rounded-item button-2 btn btn-default btn-sm zoom-fix  " onclick="CancelClient()" href="%s">' . __('Cancel', true) . '<a/>', true), $client_url, $old_client_id, $client_edit_url, $client_url, Router::url(array('action' => 'index')));
                            $this->flashMessage($fmsg, 'alert alert-danger m-t', 'secondaryMessage');
                        }
                        foreach($this->Client->validationErrors as $error){
                            CustomValidationFlash($error);
                        }
                        $this->flashMessage(__('Could not save client', true));
                    }
                }
            } else {

                if (IS_REST) {
                    $this->Client->validationErrors = array_merge($this->Client->validationErrors, $additionalFieldsFormHandler->getValidationErrors());
                }
                if (!$addOneBehaviour) {
                    return [
                        'message' => $result['message'],
                        'validation_errors' => $this->Client->validationErrors
                    ];
                }
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Client->validationErrors]);
                if ($ajax) {
                    die(json_encode(array('fmsg' => __('Could not save client', true), 'message' => __('Could not save client', true), 'client' => false, 'error' => true, 'errors' => $this->Client->validationErrors)));
                } else {

                    if (isset($this->Client->validationErrors['phone2'])) {
                        if (!empty($this->data['Client']['phone2'])) {
                            $row = $this->Client->find('first', array('order' => 'Client.client_number desc', 'conditions' => array('Client.phone2' => $this->data['Client']['phone2'])));
                            if ($row) {
                                $client_url = Router::url(array('action' => 'view', $row['Client']['id']));
                                $client_edit_url = Router::url(array('action' => 'edit', $row['Client']['id']));
                                $old_client_id = $row['Client']['id'];
                                $fmsg = sprintf(__('<p>' . __('This phone is already entered for an', true) . ' <a class="limit-link text-u-l" target="_blank" href="%s">' . __('Existing client', true) . '</a></p><div class="clearfix"></div> <a class="add_as_newclient rounded-item button  btn btn-success btn-sm zoom-fix" href="#" style="display: none">' . __('Add as a new client', true) . '</a> <a  data-id="%s" data-url="%s" data-view-url="%s" class="Update_Existed_Client rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Update the existing client details', true) . '</a> <a class="rounded-item button-2 btn btn-default btn-sm zoom-fix  " onclick="CancelClient()" href="#">' . __('Cancel', true) . '<a/>', true), $client_url, $old_client_id, $client_edit_url, $client_url, Router::url(array('action' => 'index')));
                            } else {
                                $fmsg = '';
                            }
                        } else {
                            $fmsg = '';
                        }
                        $this->flashMessage($fmsg, 'alert alert-danger m-t', 'secondaryMessage');
                    }

                    if (isset($this->Client->validationErrors['email'])) {
                        if (!empty($this->data['Client']['email'])) {
                            $row = $this->Client->find('first', array('order' => 'Client.client_number desc', 'conditions' => array('Client.email' => $this->data['Client']['email'])));
                            if ($row) {
                                $client_url = Router::url(array('action' => 'view', $row['Client']['id']));
                                $client_edit_url = Router::url(array('action' => 'edit', $row['Client']['id']));
                                $old_client_id = $row['Client']['id'];
                                $fmsg = sprintf(__('<p>' . __('This email is already entered for an', true) . ' <a class="limit-link text-u-l" target="_blank" href="%s">' . __('Existing client', true) . '</a></p><div class="clearfix"></div> <a class="add_as_newclient rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Add as a new client', true) . '</a> <a  data-id="%s" data-url="%s" data-view-url="%s" class="Update_Existed_Client rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Update the existing client details', true) . '</a> <a class="rounded-item button-2 btn btn-default btn-sm zoom-fix  " onclick="CancelClient()" href="#">' . __('Cancel', true) . '<a/>', true), $client_url, $old_client_id, $client_edit_url, $client_url, Router::url(array('action' => 'index')));
                            } else {
                                $fmsg = '';
                            }
                        } else {
                            $fmsg = '';
                        }
                        $this->flashMessage($fmsg, 'alert alert-danger m-t', 'secondaryMessage');
                    }

                    $this->flashMessage(__('Could not save client', true));
                }
            }
        } else {
            if(isset($id)){
                $client = $this->Client->read(null, $id);
                if($client){
                    $this->data = $client;
                    unset($this->data['Client']['id']);
                    if(!empty($this->data['Client']['category'])){
                        $this->data['Client']['category'] = json_encode(array($this->data['Client']['category']));
                    }
                }else{
                    $this->flashMessage(__('Client not found', true));
                    $this->redirectToIndex();
                }
            }else{
                $this->data['Client']['type']=settings::getValue(ClientsPlugin, "client_type");
                if(settings::getValue(ClientsPlugin, "client_type")==settings::OPTION_CLIENT_TYPE_BOTH){
                    $this->data['Client']['type']=settings::getValue(0, "last_client_type");   
                }
            }
            $this->data['Client']['client_number'] = $this->data['Client']['default_client_number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_CLIENT);
            $this->data['Client']['starting_balance_date'] =format_date(date("Y-m-d"));

            
            
            if(settings::getValue(ClientsPlugin, "client_type")!=settings::OPTION_CLIENT_TYPE_BOTH){
            $this->set('hide_client_type',true);    
            }
        }
        $this->loadModel('Timezone');
        $this->set('client_type',settings::getValue(ClientsPlugin, "client_type"));
        $this->set('enable_multiple_addresses', $enable_multiple_addresses);
        $this->set('addClientText', $this->get_snippet('add-client'));
        $this->set('timezones', $this->Timezone->getTimezoneList($this->data['Site']['timezone']));
        $this->loadModel('Invoice');
        $this->set('invoice_methods', Invoice::getMethods());
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('Client'));

        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));

        $this->_settings();
        $this->set('title_for_layout',  __('Add Client', true));

    }

//--------------------------
    function _send_email_to_user($client) {
        $site = getAuthOwner();
//
//		$this->Lmail->to = $to;
//		$this->Lmail->sendAs = 'html';
//		$this->Lmail->layout = 'contact';
//		$this->Lmail->template = 'contact';
//		$this->Lmail->from = $site['business_name'] . '<' . $site['email'] . '>';
//		$this->Lmail->subject = "{$site['business_name']}: " . sprintf(__('Welcome to your user account at %s', true), $site['business_name']);
//		$this->Lmail->language = $this->Client->get_client_language($client_id);

        $this->_setBranded();
        return $this->SysEmails->clientDetails($client, $site);
//		if ($this->Lmail->send()) {
//			return true;
//		}
//		return false;
    }

//---------------------
    function owner_send_login_details($id = false) {
        $referral_url = '/v2/owner/entity/client/list';

        $client = $this->Client->find(array('Client.id' => $id));
        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }

        $new_password = substr(base64_encode(HashPassword(time() . mt_rand())), 0, 7);
        $client['Client']['password_view'] = $client['Client']['password'] = $new_password;
        if ($this->Client->save($client, false)) {
            $this->set('user', $client['Client']);
            $result = $this->_send_email_to_user($client['Client']);
            if ($result) {
                $this->add_actionline(ACTION_SEND_LOGIN_DETAILS, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param4' => $client['Client']['client_number']));
                $this->flashMessage(__('Login details have been sent.', true), 'Sucmessage');
            } else {
                $this->flashMessage(__('Login details could not be sent!', true));
				if(!empty($this->SysEmails->error_message)){
				$this->flashMessage($this->SysEmails->error_message, 'Errormessage', 'secondaryMessage');
				}				
            }
        } else {

            $this->flashMessage(__('Login details could not be sent!', true));
        }

        $this->redirectToIndex();
    }

//---------------------


    /**
     * this action used in invoices to show current client balance it will return html code
     * this action should be called with ajax request
     * @param $id id of client
     * @param string $currency_code if empty will return all client currency_code
     */
    function owner_balance($id = null,$currency_code=""){
    $owner = getAuthOwner();

    $client = $this->Client->find(array('Client.id' => $id));
    if(!$client){
        die();
    }

    if ($owner['staff_id'] != 0) {
        $staff = getAuthStaff('id');
        if ((!check_permission(Clients_View_All_Clients) && $client['Client']['staff_id'] != $staff) && !check_permission(Clients_View_his_own_Clients)) {
            die();
        }
    }

    $this->set('hide_over_due',true);
    $this->loadModel('Invoice');
    $count = $this->Invoice->find('count', ['applyBranchFind' => false, 'conditions' => ['Invoice.client_id' => $id]]);
    if($count < 10000 ){
        $overdueInvoices = $this->Client->getOverDue($id,$currency_code);
        $OpenInvoices = $this->Client->getUnpaid($id,$currency_code);
        $Creditlist = $this->Client->getAllCredit($id,$currency_code);
    } else {
        $overdueInvoices = [];
        $OpenInvoices = [];
        $Creditlist = [];
    }
    $this->set('overduelist', $overdueInvoices);
    $this->set('Openlist', $OpenInvoices);
    $this->set('Creditlist', $Creditlist);
}

    function owner_change_password($id = null) {
        $owner = getAuthOwner();
        $client = $this->Client->find(array('Client.id' => $id));
        if(!$client){
            $this->flashMessage(__("Client not found", true), 'Errormessage', 'secondaryMessage');
            $this->redirectToIndex();
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(Edit_Delete_all_clients) && $client['Client']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_clients)) {
                $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
                $this->redirectToIndex();
            }
        }

        $this->set('client',$client);

        if (!empty($this->data)) {

            $this->Client->validate=array('password' => array(
                'rule' => array('minLength', 4),'required'=>true, 'allowEmpty' => false, 'message' => __('The password should be 6 characters at least', true)
            ),'confirm_password' => array('rule' => 'checkPasswd','required'=>true, 'allowEmpty' => false, 'message' => __('Please enter confirm password', true)));

            $this->Client->set($this->data);
            if( $this->Client->validates()){
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Client', true)), 'Sucmessage');
                $this->Client->id=$id;
                //$this->data['Client']['password'] = HashPassword($this->data['Client']['password']);
                $row=$this->Client->save($this->data,false);
                if($row){
                    AuthHelper::revoke(AuthUserTypeUtil::CLIENT, $client['Client']['id']);
                    $this->add_actionline(ACTION_OWNER_CHNAGE_CLIENT_PASSWORD, array('primary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param4' => $client['Client']['client_number']));
                }
            }else{
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Client', true)));
            }

        }
        $this->set('id',$id);
    }

    private function createAdditionalFieldsFormHandlerInstance()
    {
        return new \App\Services\LocalEntityForm\AdditionalFormsHandler(EntityKeyTypesUtil::CLIENT_ENTITY_KEY);
    }

    function owner_edit($id = null) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $enable_multiple_addresses = settings::getValue(ClientsPlugin,'multiple_addresses');
        $this->loadModel("Country");
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->client_js_labels);
        App::import('Vendor', 'settings');
        App::import('Vendor', 'notification');
        App::import('Vendor', 'sites_local');
        $countrires = $this->Country->getCountryList();
        $bnf =  Localize::get_business_number_fields();
        foreach ($countrires as $key => $country) {
            if (!isset($bnf[$key])) {
                $bnf[$key] = ["bn2" => ["label" => "Vat number"]];
            }
        }
        $this->set('bnf', $bnf);
        $this->set('client_type',settings::getValue(ClientsPlugin, "client_type"));
        $owner = getAuthOwner();
        $this->loadModel('Address');

        $this->_record_referer_path();

        $this->loadModel('ItemStaff');
        
        $this->loadModel('Post');

        if ($this->RequestHandler->isAjax()) {
            $this->layout = '';
        }

        $site_id = getAuthOwner('id');
        $condition = "and EntityAttachment.entity_field_key is null";
        $this->Client->bindAttachmentRelation('client',$condition);

        $client = $this->Client->find(array('Client.id' => $id));
        if ($enable_multiple_addresses) {
            $this->loadModel('Address');
            $addresses = $this->Address->find('all', ['conditions' => [
                'Address.entity_id' => $id, 'Address.entity_type' => 'Client'
            ]], ['recursive' => -1]);
        }

        if (!$client) {
			if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Client", true))));
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(Edit_Delete_all_clients) && $client['Client']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_clients)) {

                $exit = true;
                if (ifPluginActive(FollowupPlugin)){
                    $shared_client = settings::getValue(BranchesPlugin, 'share_clients');
                    if($shared_client){
                        $this->ItemStaff->applyBranch['onFind'] = false;
                    }
                    $count = $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $id, 'ItemStaff.staff_id' => $staff)));
                }

                if ($count > 0)
                    $exit = false;

                if ($exit) {
					if(IS_REST) $this->cakeError('error403');
                    $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
                    $this->redirectToIndex();
                }
            }
        }



        $client_settings = settings::getPluginValues(ClientsPlugin);
        $this->set('client_settings',$client_settings);
        if($client_settings['gender'] == '1'){
            $genders = $this->Client->get_gender_list();
            $this->set('genders',$genders);
        }

        if(!empty($client['Client']['map_location'])){
            $location = explode(",",$client['Client']['map_location'] );
            $client['Client']['longitude'] = $location[0];
            $client['Client']['latitude'] = $location[1];
            $client['Client']['zoom'] = $location[2];

        }
        $this->set("geocode_api",$this->config['txt.google_geocode_api']);
        $this->set("google_maps_api",$this->config['txt.google_maps_api']);




        if($client_settings['birth_date'] == '1'){
            $formats = getDateFormats('std');
            $dateFormat = $formats[$owner['date_format']];
        }

		if(IS_REST) $this->data['Client']['id'] = $id;

        
        if (!empty($this->data)) {
            if (isset($this->data['Client']['skip_email'])) {
                unset($this->Client->validate['email']);
                unset($this->Client->validate['phone2']);
            }
            $oldData = EntityActivityLog::getRecordWithEntityStructureWithCustomFieldsForActivityLog(EntityKeyTypesUtil::CLIENT_ENTITY_KEY, $id, 3);

            if(empty($this->data['ItemsTag']) || empty($this->data['ItemsTag']['tags'])){
                $this->data['ItemsTag']['tags'] = [];
            }


            // Store client addresses
            $oldAddress = '';
            if ($enable_multiple_addresses) {
                $oldAddress = $this->Address->find('all', ['conditions' => ['Address.entity_id' => $id, 'Address.entity_type' => 'Client']], ['recursive' => -1]);
                $oldAddress = implode(',', array_column(array_column($oldAddress, 'Address'), 'address1'));
                $this->Address->deleteAll(['Address.entity_id' => $id, 'Address.entity_type' => 'Client']);
                if (!is_null($this->data['Address'])) {
                    $addresses = $this->Address->find('all', ['conditions' => ['Address.entity_id' => $id, 'Address.entity_type' => 'Client']], ['recursive' => -1]);

                    foreach ($this->data['Address'] as $address) {
                        if (empty($address['address1']) && empty($address['address2']) && empty($address['city']) && empty($address['state']) && empty($address['postal_code'])) {
                            continue;
                        }

                        $this->Address->create();

                        $record['Address'] = array_merge($address, [
                            'entity_id' => $id,
                            'entity_type' => 'Client',
                            'country_code' => $client['Client']['country_code'],
                            'created' => date("Y:m:d H:i:s"),
                            'modified' => date("Y:m:d H:i:s"),
                        ]);

                        $this->Address->save($record);
                    }
                }

                $addresses = $this->Address->find('all', ['conditions' => ['Address.entity_id' => $id, 'Address.entity_type' => 'Client']], ['recursive' => -1]);

            } elseif ($enable_multiple_addresses && is_null($this->data['Address'])) {
                $this->Address->deleteAll(['Address.entity_id' => $id, 'Address.entity_type' => 'Client']);
            }


//			die(debug($this->data));
            if (!empty($this->data['Client']['birth_date'])) {
                $this->data['Client']['birth_date'] = $this->Client->formatDate($this->data['Client']['birth_date']);
            }
			if(IS_REST) $this->data['Client']['id']=$id;
            if (empty($this->data['Client']['id'])) {
                $this->loadModel('Site');
                $result = $this->Site->check_add_client_limit();
                if (!$result['status']) {
                    $this->add_stats(STATS_ERROR_CLIENT_LIMITATION_REACHED, array($result['message']));
					if(IS_REST) $this->cakeError("error402", ["message"=>$check['message']]);
                    if ($this->RequestHandler->isAjax()) {
                        die(json_encode(array('client' => false, 'error' => true, 'errors' => array())));
                    } else {
                        $this->_flashLimitMessage($result['message'], __('clients', true));
                        $this->redirectToIndex();
                    }
                }
            }
            if (is_array($this->data['Client']['category'])) {
                $this->data['Client']['category'] = $this->data['Client']['category'][0];
                $this->data['Client']['category_id'] = $this->data['Client']['category'][0];
            }

            if ($this->data['Client']['is_offline'] == "1") {
                unset($this->data['Client']['password']);
                unset($this->data['Client']['confirm_password']);
            }

            if (empty($this->data['Client']['category'])) {
                $this->data['Client']['category'] = "";
                $this->data['Client']['category_id'] = "";
            }
            unset($this->Client->validate['phone2']);

            if(!empty($this->data['Client']['longitude']) && !empty($this->data['Client']['latitude']) && !empty($this->data['Client']['saved_map_zoom'])){
                $this->data['Client']['map_location'] = $this->data['Client']['longitude'] . "," . $this->data['Client']['latitude'] . "," . $this->data['Client']['saved_map_zoom'] ;
                unset($this->data['Client']['longitude']);
                unset($this->data['Client']['latitude']);
                unset($this->data['Client']['saved_map_zoom']);
            }

            if(IS_REST){
                $this->data['Client']['type'] = $this->getClientType($this->data);
            }


            $ajax=$this->RequestHandler->isAjax();
            if ($ajax or (
                    (isset($this->params['url']['ignore_custom_fields']) && $this->params['url']['ignore_custom_fields'] == true) & $this->Client->validates()
                    or ($additionalFieldsFormHandler->validate($this->data) &
                        $this->Client->validates()
                        && !$ajax)
                )
            ) {
                $this->data['Client']['branch_id'] = $client['Client']['branch_id'];
                $result = $this->Client->saveClient($this->data);
                if ($result['status']) {
                    $attachments = $this->data['Client']['attachment'];
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                    if(!empty($attachments))
                    {
                        $imagesIds = explode(',',$attachments);
                        izam_resolve(AttachmentsService::class)->save('client', $this->Client->id, $imagesIds);
                    }
                    $additionalFieldsFormHandler->update($id, $this->data);
                    $newData = EntityActivityLog::getRecordWithEntityStructureWithCustomFieldsForActivityLog(EntityKeyTypesUtil::CLIENT_ENTITY_KEY, $id, 3);
                    EntityActivityLog::activityLogUpdateForCustomData(EntityKeyTypesUtil::CLIENT_ENTITY_KEY, $newData, $oldData);
                   // JournalAccount::updateEntityTypeAndId($this->data['JournalAccountRoute']['account_id'], "client",  $id);
                    JournalAccount::updateAccountName($this->data['Client'], 'client');
                    izam_resolve(ClientService::class)->update(ServiceModelDataTransformer::transform($result['data'], 'Client'));

                    RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_CLIENT)
                        ->save($this->data['JournalAccountRoute'], $id);
                    if(isset($this->Client->CustomModelError) && $this->Client->CustomModelError=false){
                        $this->flashMessage(__("Custom Fields can not be saved", true), 'Errormessage', 'secondaryMessage');
                    }
					$this->add_actionline(ACTION_UPDATE_CLIENT, array('primary_id' => $id, 'secondary_id' => $id, 'param2' => $this->data['Client']['business_name'], 'param3' => $this->data['Client']['email'], 'param4' => $this->data['Client']['client_number'] . (!empty($oldAddress) ? ' address: ' . $oldAddress : '' ) , 'param5' => $this->data['Client']['phone1'] . ' ' . $this->data['Client']['phone2']));
	                if (isset($_GET['from_pos'])) {
		                $this->set('action_mode', 'edit');
		                $this->set('client_data', $this->get_client($result['data']['id']));
		                $this->render('owner_add_pos');
		                return;
	                }
                    if (isset($_GET['from_invoice'])) {
						$this->set('action', 'add_new_advanced_client');
						$this->set('data', $result['data']);
                        $this->render('/elements/result_js');
						return;
					}
					if(IS_REST){
						$this->render('success');
						return;
					}
					if ($ajax) {
                        $client = $result['data'];

                        $view = new View($this, false);
                        $client['full_address'] = $view->element('format_address_html', $client);

                        die(json_encode(array('error' => false, 'errors' => false, 'client' => $client)));
					}else{
						$this->flashMessage(sprintf(__('%s  has been saved', true), __('Client', true)), 'Sucmessage');
//                        $referral_url = $this->_get_referer_path();

                        $referral_url = isset($this->data['Client']['ref_url']) ? $this->data['Client']['ref_url'] : $this->_get_referer_path();
                       
                        if ( empty( $referral_url ) ){
                            $this->redirect($referral_url);
                        }else {
                            $this->redirect(Router::url(['action' => 'view' , $id]));
                        }	
					}
				}else{
					if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Client->validationErrors]);
					if ($ajax){
						die(json_encode(array('client' => false, 'error' => true, 'errors' => $this->Client->validationErrors)));						
					}
                    if (isset($this->Client->validationErrors['email']) && !empty($this->data['Client']['email'])) {
                        $row = $this->Client->find('first', array('order' => 'Client.client_number desc', 'conditions' => array('Client.email' => $this->data['Client']['email'])));
                        if ($row) {
                            $client_url = Router::url(array('action' => 'view', $row['Client']['id']));
                            $client_edit_url = Router::url(array('action' => 'edit', $row['Client']['id']));
                            $old_client_id = $row['Client']['id'];
                            $fmsg = sprintf(__('<p>' . __('This email is already entered for an', true) . ' <a class="limit-link text-u-l" target="_blank" href="%s">' . __('Existing client', true) . '</a></p><div class="clearfix"></div> <a class="add_as_newclient rounded-item button  btn btn-success btn-sm zoom-fix" href="#">' . __('Update the existed client details', true) . '</a> <a class="rounded-item button-2 btn btn-default btn-sm zoom-fix  " onclick="CancelClient()" href="#">' . __('Cancel', true) . '<a/>', true), $client_url, $old_client_id, $client_edit_url, $client_url, Router::url(array('action' => 'index')));
                            $this->flashMessage($fmsg, 'alert alert-danger m-t', 'secondaryMessage');
                        }
                    }
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Client', true)));
				}
            } else {
                $this->Client->validationErrors = array_merge($this->Client->validationErrors, $additionalFieldsFormHandler->getValidationErrors());
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Client->validationErrors]);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Client', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $client;
            if(!empty($this->data['Client']['category'])){
            $this->data['Client']['category']=  json_encode(array($this->data['Client']['category']));
            }
            $jsDateFormat = getDateFormats('std');
			


            unset($this->data['Client']['password']);
        }
        if(!empty($this->data['Client']['attachment'])){                       
            $filesId = explode(',',$this->data['Client']['attachment']);
            $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
            $this->data['Attachments'] = $attachment;
        }
        // warning suppress
        $this->data['Address'] = $addresses ?? [];
        $this->set('enable_multiple_addresses', $enable_multiple_addresses);
        $this->set('addClientText', '');
        $this->loadModel('Invoice');
        $this->set('invoice_methods', Invoice::getMethods());
        $this->_settings($id);
        $this->set('timezones', $this->Timezone->getTimezoneList($this->data['Site']['timezone']));
        $this->set('title_for_layout',  __('Edit Client', true));
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('Client'));

        $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));
        $this->render('owner_add');
    }

//	function upload_photo() {
////        if(get_real_ip() == '************'){
////            Configure::write('debug',2);
////        }
//        $user = $this->_getUser(true);
//        if (!empty($this->data)) {
//            $this->data['User']['id'] = $user['id'];
//            if ($this->User->save($this->data)) {
//
//            } else {
//
//            }
//        }
//        die();
//    }

    function _settings($id = null) {
        $this->loadModel('Timezone');
        // warning suppress
        if(isset($this->data['Site'])){
            $this->set('timezones', $this->Timezone->getTimezoneList($this->data['Site']['timezone']));
        }
        $this->loadModel('Country');
        $this->loadModel('Language');
        $this->loadModel('Currency');

        $this->set('countryCodes', $this->Country->getCountryList());
        $this->set('languageCodes', $this->Language->getLanguageList());
        $this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());
        $this->loadModel('Site');
        $this->set('default_language', $this->Site->get_site_language());
        $this->set('default_currency', $this->Site->get_site_currency());
        $this->set('default_country', $this->Site->get_site_country());
        $this->set('categorieslist', $this->Client->getCategoriesList(false));
        $this->loadModel('GroupPrice');
        $this->set('GroupPrices', $this->GroupPrice->getAll());
        $this->set('ActiveGroupPrices', $this->GroupPrice->getActiveList());
        $this->set('client_settings',settings::getPluginValues(ClientsPlugin));
        $this->set('image_settings', $this->Client->getImageSettings());
        $this->__form_common($id);
    }


    /**
     * @todo this method should be deprecated because delete_client is used by API and frontend
     * also has logic in check_deletion, that is not called here
     */
    function owner_delete($id = null)
    {
        if (empty($_POST['submit_btn'])) {
            $this->_record_referer_path();
        } else {
            $referer_url = $this->_get_referer_path(true);
        }

        if (!isset($referer_url)) {
            $referer_url = $this->_get_referer_path(false);
        }

        $this->set('referer_url', $referer_url);
        set_time_limit(600);
        $this->loadModel('ItemStaff');
        $this->loadModel('Post');

        /** If Request Type is Post (As in Multiple Delete) **/
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('client', true)));
            $this->redirect($referer_url);
        }

        $module_name = __('client', true);
        if (is_countable($_POST['ids']) && count($_POST['ids']) > 1) {
            $module_name = __('clients', true);
        }

        $clients = $this->Client->find('all', array('recursive' => -1, 'conditions' => array('Client.id' => $id)));
        if (empty($clients)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($referer_url);
        }

        /** Permission Check IF Not Owner **/
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = $owner['staff_id'];
            foreach ($clients as $client) {
                if ((!check_permission(Edit_Delete_all_clients) && $client['Client']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_clients)) {
                    $exit = true;
                    if (ifPluginActive(FollowupPlugin))
                        $count = $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $client['Client']['id'], 'ItemStaff.staff_id' => $staff)));

                    if ($count > 0)
                        $exit = false;

                    if ($exit) {
                        $this->flashMessage(__("You are not allowed to edit this client", true), 'Errormessage', 'secondaryMessage');
                        $this->redirect($referer_url);
                    }
                }
            }
        }

        /** Attendance Log Check **/
        foreach ($clients as $client) {
            if (ifPluginInstalled(CLIENT_ATTENDANCE_PLUGIN)) {
                $this->loadModel('ClientAttendanceLog');
                $logCount = $this->ClientAttendanceLog->find('count', array('conditions' => array('ClientAttendanceLog.client_id' => $client['Client']['id'])));
                if ($logCount > 0) {
                    $errorMsg = __('You cannot delete the Client as he already has an Attendance Log', true);
                    if (IS_REST) {
                        $this->cakeError("error400", ["message" => $errorMsg]);
                    } else {
                        $this->flashMessage($errorMsg);
                        $this->redirect($referer_url);
                    }
                }
            }
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes' && !($hasMembershipOrCreditCharge = $this->Client->hasMembershipOrCreditCharge($id)) && $this->Client->delete($id)) {
                foreach ($clients as $client) {
                    $this->add_actionline(ACTION_DELETE_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param4' => $client['Client']['client_number'], 'param5' => $client['Client']['phone1'] . ' ' . $client['Client']['phone2']));
                    $this->Client->delete_auto_accounts($client['Client']['id']);
                }

                $this->add_stats(STATS_REMOVE_CLIENT, array(serialize($id)));
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect($referer_url);
            } else {
                if (isset($hasMembershipOrCreditCharge) && $hasMembershipOrCreditCharge) {
                    $this->flashMessage(sprintf(__('You cannot delete the client already has a membership or credit charge in the system', true), ucfirst($module_name)), 'Errormessage', 'secondaryMessage');
                    $this->redirect($referer_url);
                }

                $this->flashMessage(sprintf(__('Could not delete %s, Please check that you have deleted all client transactions first.', true), ucfirst($module_name)));
                $this->redirect($referer_url);
            }
        }

        $this->set('clients', $clients);
        $this->set('module_name', $this->Client->name);
        $this->set('title_for_layout',  __('Delete Client', true));
        App::import('Vendor', 'notification_2');
        NotificationV2::delete_notificationbyref($id, NotificationV2::NOTI_ASSIGN_CLIENT);
        NotificationV2::delete_notificationbytrigger(NotificationV2::NOTI_TRIGGER_CLIENT, $id);
    }

    //---------------------------
    function owner_suspend_users($suspend = 0, $id = false) {

        if (!check_permission(Edit_Delete_all_clients)) {
            $this->flashMessage(__("You are not allowed to edit this client", true), 'Errormessage', 'secondaryMessage');
            $this->redirectToIndex();
        }
        $this->_record_referer_path();

        if (!empty($id)) {
            $_POST['ids'] = array();
            $_POST['ids'][] = $id;
        }
        if (empty($_POST['ids']) || !is_array($_POST['ids'])) {
            $this->flashMessage(__('Invalid clients', true));
            $referer_url = $this->_get_referer_path();
            $this->redirect($referer_url);
        }
        $site_id = getAuthOwner('id');
        $clients = $this->Client->find('all', array('recursive' => -1 ,'conditions' => array('Client.id' => $_POST['ids'])));
        if (empty($clients)) {
            $this->flashMessage(sprintf(__('%s not found', true), __('Client', true)));
            $referer_url = $this->_get_referer_path();
            $this->redirect($referer_url);
        }
        if ($this->RequestHandler->isAjax()){
            if ($this->Client->updateAll(array('Client.suspend' => $suspend), array('Client.id' => $_POST['ids']))){
                $this->set("message", __('Client status has been updated', true));
                return $this->render("success");
            }else{
                return $this->cakeError("error500", ["message" => sprintf(__('%s could not be saved. Please, try again', true), __('Client', true))]);
            }
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {

            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes' && $this->Client->updateAll(array('Client.suspend' => $suspend), array('Client.id' => $_POST['ids']))) {

                $this->flashMessage(__('Client status updated', true), 'Sucmessage');
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            } else {
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            }
        }
        $this->set('action', empty($suspend) ? "Active" : "Suspend");
        $this->set('suspend', $suspend);
        $this->set('clients', $clients);

        $this->set('title_for_layout',  __('Suspend Client', true));
    }

    //-----------------------------
    function owner_get_user_data($id = 0) {
        $this->autoRender = false;
        $site_id = getAuthOwner('id');
        Configure::write('debug', 0);
        $client = $this->Client->find(array('Client.id' => $id));

        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if (!check_permission(Clients_View_All_Clients) && $client['Client']['staff_id'] != $staff) {
                $result = array('error' => true, 'client' => false);
            } elseif (!check_permission(Clients_View_his_own_Clients)) {
                $result = array('error' => true, 'client' => false);
            }
        }
        if ($client) {
            unset($client['Client']['password'], $client['Client']['created'], $client['Client']['modified']);

            $view = new View($this, false);
            $client['Client']['full_address'] = $view->element('format_address_html', $client['Client']);
 
            if(check_permission(REDEEM_LOYALTY_POINTS) && ifPluginActive(CLIENT_LOYALTY_PLUGIN) && !empty( Settings::getValue(CLIENT_LOYALTY_PLUGIN, "client_loyalty_credit_type"))) {
                $date = strtotime($this->Client->formatDate($_REQUEST['date'], getCurrentSite('date_format')));
                $creditChargeRepo = new CreditChargeRepository();
                $this->loadModel('CreditType');
                $credit_charge_type = Settings::getValue(CLIENT_LOYALTY_PLUGIN, "client_loyalty_credit_type");
                $creditType = $this->CreditType->findById($credit_charge_type);
                $client['Client']['client_loyalty_points_type'] = $creditType;
                $client['Client']['client_loyalty_points'] = $creditChargeRepo->getClientCredit($client['Client']['id'], $credit_charge_type, date("Y:m:d H:i:s", $date));
                $client['Client']['checkSiteLimitLoyaltyPts'] = checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::LOYALTY_POINTS);
            }

            if (check_permission(Edit_Delete_all_clients)) {
                $readonly = 0;
            } elseif (check_permission(Edit_And_delete_his_own_added_clients) and $client['Client']['staff_id'] == $staff) {
                $readonly = 0;
            } else {
                $readonly = 1;
            }
            $result = array('readonly' => $readonly, 'error' => false, 'client' => $client['Client']);
        } else {
            $result = array('error' => true, 'client' => false);
        }
        $this->loadModel('Invoice');
        $count = $this->Invoice->find('count', ['applyBranchFind' => false, 'conditions' => ['Invoice.client_id' => $id]]);
        if($count < 10000) {
            $result['client']['unpaid_balance'] = $this->Client->getUnpaid($id);
            $result['client']['credit_balance'] = $this->Client->getAllCredit($id);
        }

        // client addresses and default address
        $client_default_address = [[
            'id' => 0,
            'address1' => $client['Client']['address1'],
            'address2' => $client['Client']['address2'] ?? '',
            'city' => $client['Client']['city'] ?? '',
            'state' => $client['Client']['state'] ?? '',
            'postal_code' => $client['Client']['postal_code'] ?? '',
        ]];

        $enable_multiple_addresses = settings::getValue(ClientsPlugin,'multiple_addresses');

        $addresses = [];
        if ($enable_multiple_addresses) {
            $this->loadModel('Address');
            $addresses = $this->Address->find('all', ['conditions' => [
                'Address.entity_id' => $id, 'Address.entity_type' => 'Client'
            ]], ['recursive' => -1]);
            $addresses = array_column($addresses, 'Address');
        }

        $result['client']['address'] = array_merge($client_default_address, $addresses);

        // get client latest invoice
        $latest_used_address_id = 0;
        if ($enable_multiple_addresses) {
            $this->loadModel('Invoice');
            $address_id = $this->Invoice->find('first', [
                'conditions' => [
                    'Invoice.client_id' => $client['Client']['id']
                ],
                'fields' => ['Invoice.address_id'],
                'recursive' => -1,
                'order' => 'Invoice.id DESC'
            ]);
            $latest_used_address_id = $address_id['Invoice']['address_id'];
        }

        $result['client']['last_used_address_id'] = $latest_used_address_id;

        die(json_encode($result));
    }

    function owner_get_photo($id = null) {
        $this->Client->bindAttachmentRelationWithEntityAndFieldKey('client','clients.photo');
        $client = $this->Client->find('first',array('conditions'=>array('Client.id' => $id)));

        if(!empty($client['AttachmentsByFieldKey'])){
            foreach($client['AttachmentsByFieldKey'] as $attachment){
                echo \Izam\Aws\Aws::getPermanentUrl($attachment['path']);
                // reset old photo .
                $this->Client->query("Update clients set photo = '' where id = $id ");
                die();
            }
        }

        echo DS. "files" . DS . SITE_HASH . DS . "photos" . DS . $client['Client']['photo'];
        die();
    }

    function owner_upload_photo($id = null) {
        $this->loadModel('Post');
        $this->loadModel('ItemStaff');
        $site_id = getAuthOwner('id');
        //get the client
        $client = $this->Client->find(array('Client.id' => $id));
//        debug($client);


        if (!$client) {
            $this->flashMessage(__('Client not found', TRUE));
            $this->redirectToIndex();
        }
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(Clients_View_All_Clients) && $client['Client']['staff_id'] != $staff) || !check_permission(Clients_View_his_own_Clients)) {

                $exit = true;
                if (ifPluginActive(FollowupPlugin))
                    $count = $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => ItemStaff::CLIENT_ITEM_TYPE, 'ItemStaff.item_id' => $id, 'ItemStaff.staff_id' => $staff)));
                if ($count > 0)
                    $exit = false;

                if ($exit) {
                    $this->flashMessage(__("You are not allowed to view this client", true), 'Errormessage', 'secondaryMessage');
                    $this->redirectToIndex();
                }
            }
        }

        if (!empty($this->data)) {
            $this->Client->id= $client['Client']['id'];
            if ($this->Client->save($this->data)) {
                echo "done";
            } else {
                echo "error";
            }
        }
        die();
    }

    //--------------------------------
    function login() {
        write_client_settings();
        $this->set('client_permission_register',settings::getValue(ClientsPlugin, 'client_permission_register'));
        if ($this->Cookie->read('User_id')) {

			$user_id=$this->Cookie->read('User_id');
            if ($this->Cookie->read('User_level') == 3) {
                $client = $this->Client->find(array('Client.id'=>$user_id,'Client.suspend'=>0));
                if (md5($_SERVER['HTTP_USER_AGENT'] . $client['Client']['id'].$client['Client']['password']) == $this->Cookie->read('User_hash')) {
                    $this->Client->reload_session($client);
                    Cache::delete(getNotificationCacheKey());
                    if ($this->Session->check('LOGIN_REDIRECT')) {
                        $redirectTo = $this->Session->read('LOGIN_REDIRECT');
                        $this->Session->delete('LOGIN_REDIRECT');
                        $this->redirect($redirectTo);
                    }else{
                        $this->redirect('/');
                    }
                } else {
                    $this->Cookie->destroy();
                    $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
                    $this->redirect(getLoginUrl());
                }
            } elseif ($this->Cookie->read('User_level') == 2) {
                $this->loadModel('Site');
                $this->loadModel('Staff');
                $staff = $this->Staff->find(array('Staff.id'=>$user_id,'Staff.active'=>1,'Staff.can_access_system'=>1));
                if (!$staff) {
                    $this->Cookie->destroy();
                    $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
                    $this->redirect(getLoginUrl());
                } else {
                    if (md5($_SERVER['HTTP_USER_AGENT'] . $staff['Staff']['id'].$staff['Staff']['password']) == $this->Cookie->read('User_hash')) {
                        $query = 'UPDATE staffs SET last_login = NOW() WHERE id = ' . $user_id;
                        $this->Staff->query($query);

                        $this->Staff->reload_session($this->Cookie->read('User_id'));
                        $this->Site->reload_session(getCurrentSite('id'));
                        Cache::delete(getNotificationCacheKey());
                        if ($this->Session->check('LOGIN_REDIRECT')) {
                            $redirectTo = $this->Session->read('LOGIN_REDIRECT');
                            $this->Session->delete('LOGIN_REDIRECT');
                            $this->redirect($redirectTo);
                        }else{
                            $this->redirect('/');
                        }
                    } else {
                        $this->Cookie->destroy();
                        $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
                        $this->redirect($this->Session->read('LOGIN_REDIRECT'));
                    }
                }
            } else {

                $this->loadModel('Site');
                $site_id=getCurrentSite('id');
                if(empty($site_id)){
                    $site_id=$this->Cookie->read('User_id');
                }
                $site = $this->Site->findById($site_id);

                if (!$site) {
                    $this->Cookie->destroy();
                    $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
                    $this->redirect(getLoginUrl());
                } else {

                    if (md5($_SERVER['HTTP_USER_AGENT'] . $site['Site']['id'].$site['Site']['password']) == $this->Cookie->read('User_hash')) {
//                        print_pre(md5($_SERVER['HTTP_USER_AGENT'] . $site['Site']['id'].$site['Site']['password']));
//                        print_pre($this->Cookie->read('User_hash'));
//                        die();
                        $this->Site->reload_session($site['Site']['id']);
                        if ($this->Session->check('LOGIN_REDIRECT')) {
                            $redirectTo = $this->Session->read('LOGIN_REDIRECT');
                            $this->Session->delete('LOGIN_REDIRECT');
                            $this->redirect($redirectTo);
                        }else{
                            $this->redirect('/');
                        }
                        Cache::delete(getNotificationCacheKey());
                    } else {
                        $this->Cookie->destroy();
                        $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
                        $this->redirect(getLoginUrl());
                    }
                }
            }
        }
        $isOwnerStaffFirstLogin = $this->Session->read("isOwnerStaffFirstLogin");
        if (!empty($this->data) || $isOwnerStaffFirstLogin) {
            $result = [];
            if($isOwnerStaffFirstLogin){
                $this->loadModel('Staff');
                $ownerUserStaff = $this->Staff->getOwnerUser();
                if($ownerUserStaff){
                    $result = $this->Client->login_system([
                        'email'=> $ownerUserStaff['email_address'],
                        'password'=> $ownerUserStaff['password'],
                        'password_already_hashed'=> true
                    ]);
                }
            }else{
                $result = $this->Client->login_system($this->data['Client']);
            }
            $this->loadModel("Site");
            if (isset($result["is_staff"]) && $result["is_staff"] != 0 && ($result["role_id"] != -1)  && $this->Site->isFreeAndCreatedFromMoreThanOneMonth($_SERVER["SERVER_NAME"])) {
                $this->flashMessage(__("Only the main system admin can log in to the system as the site exceeds the trial period in the free plan", true), 'Errormessage', 'Loginpage');
                $this->redirect(getLoginUrl());
            }
            if ($result['success']) {
                write_client_settings();
                if ($result['type'] == 'client') {
                    $result['user']['client_type'] = $result['user']['type'];
                }
                $result['user']['type'] = $result['type'];
                $result['user']['staff_id'] = $result['is_staff'];
                $result['user']['is_super_admin'] = $result['is_super_admin'];
                $password=HashPassword($this->data['Client']['password']);
                $session_name = strtoupper($result['type']);
                /**
                 * Redis User Session Manager
                 */
                if(useRedis()) {
                    try {
                        $userSessionRedisService = resolve(UserSessionRedisService::class);
                        $session_id = $this->Session->id();
                        $userSessionRedisService->add($result['staff']['id'] ?? $result['user']['id'], $session_id);
                    } catch (\Throwable $e) {
                        \Rollbar\Rollbar::log(\Rollbar\Payload\Level::WARNING, 'Failed to add session to Redis: ' . $e->getMessage());
                    }       
                }
                $this->Session->write($session_name, $result['user']);
                $this->Session->write('STAFF', isset($result['staff']) ? $result['staff'] : false);
                Cache::delete(getNotificationCacheKey());
                if (!$result['staff'] && $result['type'] == "owner") {
                    $this->Cookie->write('User_level', "1", false, Cookie_Life);
                    $this->Cookie->write('User_id', getCurrentSite('id'), false, Cookie_Life);
                    $this->Cookie->write('User_hash', md5($_SERVER['HTTP_USER_AGENT'] . getCurrentSite('id').$password), false, Cookie_Life);
                } elseif ($result['type'] == "owner") {
                    if(ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin) && count(getStaffBranchesIDs()) > 0 && !in_array($_SESSION['branch_id'], getStaffBranchesIDs())) {
                        $this->Session->write('branch_id', getStaffBranchesIDs()[0]);
                    } else {
                        $this->Session->write('branch_id', getCurrentBranchID());
                    }
                    $this->Cookie->write('User_level', "2", false, Cookie_Life);
                    $this->Cookie->write('User_id', $result['is_staff'], false, Cookie_Life);
                    $this->Cookie->write('User_hash', md5($_SERVER['HTTP_USER_AGENT'] . $result['is_staff'].$password), false, Cookie_Life);
                } else {
                    $this->Cookie->write('User_level', "3", false, Cookie_Life);
                    $this->Cookie->write('User_id', $result['client']['id']);
                    $this->Cookie->write('User_hash', md5($_SERVER['HTTP_USER_AGENT'] . $result['client']['id'].$client['client']['password'], false));
                }

                if ($result['user']['type'] == 'owner') {
                    $this->add_stats(STATS_LOG_IN);
                    $this->loadModel('Site');
                    $this->Site->id = getAuthOwner('id');
                    $this->add_actionline(ACTION_LOGIN);
                    $this->Site->saveField('last_login', date('Y-m-d H:i:s'));
                } else {
                    
                    // check for client settings
                    // $client_settings =	settings::getPluginValues(ClientsPlugin);
                    $client = getAuthClient();
                    $this->add_actionline(ACTION_CLIENT_LOGIN, array('staff_id' => -1, 'primary_id' => $client['id'], 'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number']));
                    $this->add_stats(STATS_CLIENT_LOGIN, array($result['user']['id']));
                }

                if (!empty($this->params['url']['box'])) {
                    $this->set('removeBox', true);

                    $this->render('login-box');
                    return;
                } else {

                    $previous_page = $this->Session->read("LOGIN_REDIRECT");
                    if (empty($previous_page)) {
						if(ifPluginActive(WebsiteFrontPlugin) && !isMobileApp())
						{
							if($result['staff'])
								$previous_page = '/owner/sites/dashboard' ;
							else
								$previous_page = '/home' ;
						}else{
							$previous_page = "/";
						}
                        
                    }
                    $this->Session->delete("LOGIN_REDIRECT");
					if (IS_REST2) {

						if($_GET['redirect']){
							$this->redirect (Router::url(array('plugin' => 'website_front','controller' => 'contents', 'action' => 'home')));
						}
						die(json_encode(['data'=>$_SESSION,'status' => true]));
					}

                    $url = str_replace('http://', 'https://', Router::url($previous_page, true));
                    if($isOwnerStaffFirstLogin){
                        $this->Session->delete('isOwnerStaffFirstLogin');
                        $this->redirect('/v2/owner/plugin-manager/1');
                    }else{
                        $this->redirect($url);
                    }
                }
            } else {
				if (IS_REST2) {
				    die(json_encode(['status' => false, 'message' => __($result['errorMessage'], true)]));
                }
                if (isset($_GET['redirect'])){
                    return $this->redirect(getLoginUrl());
                }
                $this->flashMessage(__($result['errorMessage'], true), 'Errormessage', 'Loginpage');
            }
        } else {
            if (!empty($_GET['email'])) {
                $this->data['Client']['email'] = h($_GET['email']);
            }
        }

        if ($this->Session->read('firstlogin')) {
            $this->set('firstlogin', true);
            $this->set('loginSnippet', $this->get_snippet('first-login-message'));
        }

        $this->layout = false;
        $this->set('title_for_layout',  __("Client Login", true));

        if (!empty($this->params['url']['box'])) {
            $this->set('box', true);
            $this->render('login-box');
        }
    }

    //-------------------------------------
    function owner_load_list($includes = false) {

        $this->autoRender = false;
        if ($includes && strlen($includes))
            $includes = explode(',', $includes);
        die(json_encode(array('error' => false, 'clients' => $this->Client->getClientsList(false, array(), 20, 'Client.modified desc', $includes))));
    }

    //--------------------------------------------
    function forgot() {
        $this->_is_user();
        $this->layout = "login";
        if (!empty($this->data)) {
            $user = $this->Client->validateForgot($this->data);
            if (!$user['success']) {
                $this->flashMessage(__('Please fix errors and try again', true));
            } else {
                $data = $user['user'];
                $data['staff'] = $user['staff'];
                $data['type'] = $user['type'];
                if (isset($user['user']['email_address'])) {
                    $data['email'] = $user['user']['email_address'];
                } else {
                    $data['email'] = $user['user']['email'];
                }

                $str = "{$data['first_name']} {$data['email']} {$data['id']}";
                $new_password = hash_string($str);
                $this->Lmail->to = $this->data['Client']['email'];
                if($data['type']=="client" || $data['type']=="staff"){
                    $this->Lmail->from = getCurrentSite('business_name') . " <" . getCurrentSite('email') . ">";
                }else{
                    $this->Lmail->from = Site_Full_name . " <" . "support@" . Domain_Short_Name . ">";
                }
                $this->loadModel('Site');
                $this->Lmail->subject = $this->config['txt.site_name'] . ': ' . __('Password Reset Confirmation', true);
                $this->Lmail->sendAs = 'html';
                $this->Lmail->template = 'forgot';
                if ($data['type'] == 'client') {
                    $this->Lmail->language = $this->Client->get_client_language($data['id']);
                } else {

                    $this->Lmail->language = $this->Site->get_site_language($data['id']);
                }
                $this->set('data', $data);
                $this->set('new_password', $new_password);
                $this->Lmail->send();
                $this->Session->write('user_data', $data);
                $this->Session->write('confirm_code', $new_password);
                $this->redirect(array('action' => 'reset'));
            }
        }else{
            $this->Session->delete('user_data');
            $this->Session->delete('confirm_code');
        }
        $this->set('title_for_layout',  __("Forgot Password", true));
    }

    //---------------------------------------
    function reset() {
        $this->_is_user();
        if (!$this->Session->check('user_data') || !$this->Session->check('confirm_code')) {
            $this->Session->delete('user_data');
            $this->Session->delete('confirm_code');
            $this->redirect(array('action' => 'forgot'));
        }
        $user_data = $this->Session->read('user_data');
        if (!empty($this->data['Client'])) {
            if ($this->data['Client']['code'] == $this->Session->read('confirm_code')) {
                $encodedMail = urlencode($user_data['email']);
                $this->redirect(array('action' => 'confirm_password?email='.$encodedMail.'&code='.$this->Session->read('confirm_code')));
            } else {
                $this->Client->validationErrors['code'] = __('The code you entered is invalid.', true);
            }
        }

        $this->set('email', $user_data['email']);
        $this->set('title_for_layout',  __("Reset Password", true));
    }

    function email_view($hash = "") {
        $this->loadModel('EmailLog');
        if ($hash == "") {
            $this->redirect("/");
        }
        $find_hash = $this->EmailLog->find('first', array('conditions' => array('EmailLog.uniq_hash' => $hash)));
        if (isset($find_hash['EmailLog']['id']) & $find_hash['EmailLog']['invoice_id'] != NULL && $find_hash['EmailLog']['client_id'] != NULL) {
            $this->add_actionline(ACTION_CLIENT_READ_INVOICE_EMAIL, array('primary_id' => $find_hash['EmailLog']['invoice_id'], 'secondary_id' => $find_hash['EmailLog']['client_id'], 'param8' => $find_hash['EmailLog']['id'], 'staff_id' => -2));
        }
        if (isset($find_hash['EmailLog']['id']) & $find_hash['EmailLog']['purchase_order_id'] != NULL) {
            $this->add_actionline(ACTION_READ_PO_EMAIL, array('primary_id' => $find_hash['EmailLog']['purchase_order_id'], 'secondary_id' => $find_hash['EmailLog']['supplier_id'], 'param8' => $find_hash['EmailLog']['id'], 'staff_id' => -2));
        }
        header('Content-Type: image/gif');
        echo base64_decode('R0lGODlhAQABAJAAAP8AAAAAACH5BAUQAAAALAAAAAABAAEAAAICBAEAOw==');
        die();
    }

    //---------------------------------------
    function confirm_password() {

        $this->Client->applyBranch=['onFind' => false, 'onSave' => false];
        //print_r($_SESSION['user_data']);
        $this->_is_user();

        if (!$this->Client->check_code_confirmation($this->params['url'])) {
            $this->redirect(array('action' => 'forgot'));
        }
        if (!empty($this->data['Client'])) {
            if ($this->Client->saveConfirmPassword($this->data)) {
                $this->flashMessage(__('New Password has been saved', true), 'Sucmessage', 'Loginpage');
                $this->redirect(array('action' => 'login'));
            } else {
                $this->flashMessage(__('Failed to save new password ,try again', true));
            }
        }
        $this->set('email', $this->params['url']['email']);
        $this->set('code', $this->params['url']['code']);
        $this->set('title_for_layout',  __("Reset Password", true));
    }

    //-------------------------------------

    /**
     * @return void
     * @throws Exception
     * @deprecated
     */
    function set_timezones() {
        $timezones = timezone_identifiers_list();
        $this->loadModel('Timezone');

        foreach ($timezones as $timezone) {
            $data = array();
            $data['Timezone']['zone_name'] = $timezone;
            $dateTimeZone = new DateTimeZone($timezone);
            $dateTime = new DateTime("now", $dateTimeZone);
            $data['Timezone']['offset'] = ($dateTimeZone->getOffset($dateTime) / 3600);
            $sign = "-";
            $zero = "";
            $min = "00";
            if ($data['Timezone']['offset'] >= 0) {
                $sign = "+";
            }
            if (($data['Timezone']['offset'] <= 9 && $data['Timezone']['offset'] >= 0) || ($data['Timezone']['offset'] >= -9 && $data['Timezone']['offset'] < 0)) {
                $zero = "0";
            }
            if ($data['Timezone']['offset'] < 0) {
                $data['Timezone']['offset']*= - 1;
            }
            if (is_float($data['Timezone']['offset'])) {
                $fraction = $data['Timezone']['offset'] - floor($data['Timezone']['offset']);
                $data['Timezone']['offset'] = floor($data['Timezone']['offset']);
                $min = $fraction * 60;
            }
            $data['Timezone']['offset'] = $sign . $zero . $data['Timezone']['offset'] . ":" . $min;

            PortalTimezone::create($data['Timezone']);
        }

        exit();
    }

    function owner_myob() {
        $owner = getAuthOwner();
        //$this->Client->bind(array('Country' => array('foreignKey' => false, 'type' => 'belongsTo', 'conditions' => 'Country.code = Client.country_code')));
        $clients = $this->Client->find('all', array('conditions' => array(), 'order' => 'Client.client_number'));
        $this->set(compact('clients', 'owner'));
    }

    function owner_statement($id = false) {

		$owner = getAuthOwner();
		  $staff = getAuthStaff('id');
        $this->loadModel('InvoicePayment');
        App::import('Vendor', 'settings');
        App::import('Vendor', 'PlaceHolder');
        $client = $this->Client->find(array('Client.id' => $id));
        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }
        $clientName = $client['Client']['first_name'] . ' ' . $client['Client']['last_name'];
        if (!empty($client['Client']['business_name'])) {
            $clientName = $client['Client']['business_name'];
        }
        $this->set('title_for_layout',  sprintf(__('Account statement for %s', true),$clientName));
		$this->loadModel('Post');
		$this->loadModel('ItemStaff');
		$is_assigned=$this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $id, 'ItemStaff.staff_id' => $staff)));
        // Get Starting Balance
        $this->InvoicePayment->applyBranch['onFind'] = false;
        $sb=$this->InvoicePayment->find('first',array('recursive'=>'-1','conditions'=>array('payment_method'=>'starting_balance','InvoicePayment.client_id'=>$id)));
        $this->set('sb',$sb);
        
        if ($owner['staff_id'] != 0) {
          
            if ((!check_permission(Clients_View_All_Clients) && $client['Client']['staff_id'] != $staff && $is_assigned==0) || !check_permission(Clients_View_his_own_Clients) ) {
				
                $this->flashMessage(__("You are not allowed to view this client !", true), 'Errormessage', 'secondaryMessage');
                $this->redirectToIndex();
            }
        }
        $this->set(compact('client'));
        if (low($this->params['url']['ext']) == 'pdf') {
            $this->owner_view_statement($id);
		   }
    }
    function client_statement() {
        $id=  getAuthClient('id');
        App::import('Vendor', 'settings');
        App::import('Vendor', 'PlaceHolder');
        $client = $this->Client->find(array('Client.id' => $id));
        $this->set(compact('client'));
        if (low($this->params['url']['ext']) == 'pdf') {
            $this->owner_view_statement($id);
        }

    }

    function client_view_statement($id = false) {
        App::import('Vendor', 'settings');
        $this->loadModel('Site');
        $this->loadModel('Invoice');
        $this->loadModel('InvoicePayment');
        $client = $this->Client->find(array('Client.id' => $id));
        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }

        $Client_Credit = __('Client Credit', true);

        $any_refund=$this->Invoice->hasAny(array('Invoice.type'=>Invoice::Refund_Receipt));
        $any_credit=$this->Invoice->hasAny(array('Invoice.type'=>Invoice::Credit_Note));
        $any_client_credit=$this->InvoicePayment->hasAny(array('InvoicePayment.client_id is not null'));
        // $new_element=$any_refund or $any_credit or $any_client_credit;

        //$this->set('new_element',$new_element);
        //Getting detailedSummaries and summaries
        $client_starting_balance=$this->InvoicePayment->find('first',array('conditions'=>array('InvoicePayment.payment_method'=>'starting_balance','InvoicePayment.client_id'=>$id)));

        $this->set('client_starting_balance',$client_starting_balance['InvoicePayment']);
        $sc=$this->Client->get_statement_data($id);

        $this->set ( $sc) ;


        $owner = getAuthOwner();
        $placeholders = array_merge(PlaceHolder::datetime_place_holder(), PlaceHolder::client_place_holder($client), PlaceHolder::staff_place_holder($owner['staff_id']));
        $this->set('placeholders', $placeholders);

        $statement_header_html = settings::getValue(0, "statement_header_html") ;
        $custom_header = PlaceHolder::replace ($statement_header_html, array_keys($placeholders), $placeholders);
        $this->set('custom_header', $custom_header);

        $statement_footer_html =  settings::getValue(0, "statement_footer_html");
        $custom_footer = PlaceHolder::replace($statement_footer_html, array_keys($placeholders), $placeholders);
        $this->set('custom_footer', $custom_footer);
        

        $this->layout = 'box';
        $this->set(compact('client', 'detailedSummaries', 'summaries'));

        if ($this->action == 'owner_view_statement' && low($this->params['url']['ext']) == 'pdf') {
            $this->render('owner_statement');
        }
        $this->render('client_view_statement');
    }
    function owner_view_statement($id = false) {

        App::import('Vendor', 'settings');
        $this->loadModel('Site');
        $this->loadModel('Invoice');
        $this->loadModel('InvoicePayment');
        $client = $this->Client->find(array('Client.id' => $id));
        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }

        $Client_Credit = __('Client Credit', true);

        $any_refund=$this->Invoice->hasAny(array('Invoice.type'=>Invoice::Refund_Receipt));
        $any_credit=$this->Invoice->hasAny(array('Invoice.type'=>Invoice::Credit_Note));
        $any_client_credit=$this->InvoicePayment->hasAny(array('InvoicePayment.client_id is not null'));
        // $new_element=$any_refund or $any_credit or $any_client_credit;

        //$this->set('new_element',$new_element);
        //Getting detailedSummaries and summaries
        $this->InvoicePayment->applyBranch['onFind'] = false;
        $client_starting_balance=$this->InvoicePayment->find('first',array('conditions'=>array('InvoicePayment.payment_method'=>'starting_balance','InvoicePayment.client_id'=>$id)));

        $this->set('client_starting_balance',$client_starting_balance['InvoicePayment']);
        $sc=$this->Client->get_statement_data($id);
        $this->set ( $sc) ;

        $owner = getAuthOwner();
        $placeholders = array_merge(PlaceHolder::site_place_holder(), PlaceHolder::datetime_place_holder(), PlaceHolder::client_place_holder($client), PlaceHolder::staff_place_holder($owner['staff_id']));
        $this->set('placeholders', $placeholders);

        $statement_header_html = settings::getValue(0, "statement_header_html", null, false);
        $custom_header = PlaceHolder::replace($statement_header_html,array_keys($placeholders), $placeholders);
        $this->set ('custom_header', $custom_header);

        $statement_footer_html = settings::getValue(0, "statement_footer_html", null, false) ;
        $custom_footer = PlaceHolder::replace($statement_footer_html, array_keys($placeholders), $placeholders);
        $this->set('custom_footer', $custom_footer);

        $this->layout = 'box';
        $this->set(compact('client', 'detailedSummaries', 'summaries'));

        if ($this->action == 'owner_view_statement' && low($this->params['url']['ext']) == 'pdf') {
            $this->render('owner_statement');
        }
    }

    function owner_send_statement($id = false) {
        $site = getAuthOwner();
        $this->loadModel('EmailTemplate');
        $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('client-statement');

        $client = $this->Client->find(array('Client.id' => $id));

        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }

//        $placeHolders = array(
//            '{%client-number%}' => $client['Client']['client_number'],
//            '{%client-name%}' => empty($client['Client']['first_name']) && empty($client['Client']['first_name']) ? $client['Client']['business_name'] : $client['Client']['first_name'] . ' ' . $client['Client']['last_name'],
//            '{%client-email%}' => $client['Client']['email'],
//            '{%client-business-name%}' => $client['Client']['business_name'],
//            '{%client-first-name%}' => $client['Client']['first_name'],
//            '{%client-last-name%}' => $client['Client']['last_name'],
//            '{%client-address%}' => h($client['Client']['address1']) . ife($client['Client']['address2'], '<br />' . $client['Client']['address2']),
//            '{%client-address1%}' => $client['Client']['address1'],
//            '{%client-address2%}' => $client['Client']['address2'],
//            '{%client-city%}' => $client['Client']['city'],
//            '{%client-state%}' => $client['Client']['state'],
//            '{%client-postal-code%}' => $client['Client']['postal_code'],
//            '{%client-country%}' => $client['Country']['country'],
//        );
        $placeHolders = PlaceHolder::client_place_holder($client);
        $attachments = array($this->_createStatement($id));

        $to = $client['Client']['email'];

        if ($this->SysEmails->sendEmail($to, $site, $defaultTemplate, $placeHolders, $attachments, array('client_id' => $client['Client']['id']))) {
            $this->flashMessage(__('The statement has been sent', true), 'Sucmessage');
            $this->redirect(array('action' => 'view', $id));
        } else {
            $this->flashMessage(__('Could not send statement', true));
        }

        debug ( 'nour');
        $this->loadModel('EmailTemplate');
        $this->set('emailTemplates', $this->EmailTemplate->getEmailTemplateList(2));
        $this->Set('fullEmailTemplates', $this->EmailTemplate->find('all', array('conditions' => array('EmailTemplate.type' => 2), 'order' => 'EmailTemplate.title')));

        $type = EmailTemplate::getTypes(2);
        $this->set('PlaceHolders', $this->EmailTemplate->getPlaceHoldersList($invoice_type));
        $this->set('placeHolders', $type['placeHolders']);
        $this->set('client', $client);
		$this->loadModel('EmailLog');
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->helpers[] = 'fck';

    }

    function _createStatement($client_id) {
        $this->owner_view_statement($client_id);
        $View = new View($this, true);
        /* @var $View View */
        $View->set('save', true);
        $View->params['url']['ext'] = 'pdf';
        $View->layout = '';
        $site = getCurrentSite();

        $View->render('pdf/owner_statement', '');

        return $filename = $View->statementFileName;
    }

    function owner_autocomplete() {
        Configure::write('debug', 0);

        if (!$this->RequestHandler->isAjax()) {
            // $this->redirect(array('action' => ' index'));
        }

        if (empty($this->params['url']['term'])) {
            die(json_encode(array()));
        }
        $con = array();

        $q = '%' . mysqli_real_escape_string( $this->Client->getDataSource()->connection, $this->params['url']['term']) . '%';
        if (!check_permission(Clients_View_All_Clients)) {
            $con[] = $this->getCanViewHisOwnClientsCondition(getAuthOwner('staff_id'));
        }

        $conditions = array_merge($con, array('CONCAT_WS(" ", Client.client_number, Client.first_name, Client.last_name, Client.business_name) LIKE' => $q));
        $dbClients = $this->Client->find('all', array('conditions' => $conditions, 'limit' => 10, 'order' => 'CONCAT_WS(" ", Client.first_name, Client.last_name, Client.business_name)', 'recursive' => -1, 'fields' => 'Client.id, Client.client_number, Client.first_name, Client.last_name, Client.business_name'));
        $clients = array();

        foreach ($dbClients as $client) {
            $clients[] = array('label' => '#'.$client['Client']['client_number'] . ' ' . $client['Client']['first_name'] . ' ' . $client['Client']['last_name'] . ' (' . $client['Client']['business_name'] . ')', 'value' => $client['Client']['id']);
        }

        die(json_encode($clients));
    }

    function keep_alive() {

        $status = $this->Session->check('OWNER');
        $message = '';
        if (!$status) {
            $message = __('Session has expired, please login', true);
        }

        die(json_encode(compact('message', 'status')));
    }

    /**
     * @var $Lmail LmailComponent
     */
    var $Lmail;

    function owner_timeline($id = false) {
        $this->set('invoice_id', $id);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
//        $invoice = $this->Invoice->getInvoice($id, array('Invoice.type' => 0));
//
//
//        if (!$invoice) {
//            $this->flashMessage(__('Invoice not found', true));
//            $this->redirect($this->referer(array('action' => 'index'), true));
//        }

        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Client', array('secondary_id' => $id));




        $action_list = $timeline->getClientActionsList();
        $timeline->init(array('secondary_id' => $id), $action_list);
        $data = $timeline->getDataArray();

        $this->set('data', $data);
        $this->loadModel('ActionLine');

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $invoice_actions[$key] = $action;
            }
        }
        $this->set('actions', $invoice_actions);

        //$this->set('jsonParams', $timeline->jsonAdapter(), false);
    }

    function owner_transaction_list($id , $send_email=0) {
        $owner=getAuthOwner();
        $client = $this->Client->read(null, $id);

        $this->set('client', $client);
        if (!$client) {
            $this->flashMessage(__('Client not found', true));
            $this->redirectToIndex();
        }
        $journal_account_id=$this->Client->get_journal_id($id);
        if($journal_account_id==0){
         $journal_account_id='NULL';   
        }
        $this->layout = false;
        $starting_balance = array();
        $page = $this->params['url']['page']  ? intval($this->params['url']['page']) : 1;
        $date_from = $this->params['url']['date_from'];
        $date_to = $this->params['url']['date_to'];


        $per_page = 600;
        $invoice_more_query = "" ;
        $payment_more_query = "" ;
		$starting_limit =""; 
        if (!empty($date_from)) {

            $date_from = $this->params['url']['date_from'];
            $date_to = $this->params['url']['date_to'];

            $invoice_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
            $before_invoice_more_query = " And date <'{$date_from}'";

            $payment_start_query = " And date >='{$date_from}' And date <='{$date_to}'";

            $payment_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
        }
//        else if ( $page == 1 ){ //this part causes issues when you don't filter with date
//            $payment_start_query = " And FALSE ";
//        }
		debug ( $page );
		if ( $page > 1 ){
			$starting_limit =  " LIMIT " . ($per_page * ($page - 1));
		}
        $bare_query = "select 1 as my_order ,`created`,'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,summary_paid, due_after,'payment_method',currency_code,(SELECT IF(alter_description!='',alter_description,description) COLLATE utf8_unicode_ci FROM `journals` where draft = 0 " . $this->getBranchCondition('journals') . " and entity_type=(CASE WHEN invoices.type=0 THEN 'invoice' WHEN invoices.type=6 THEN 'refund_receipt' WHEN invoices.type=5 THEN 'credit_note' END) and entity_id=invoices.id limit 1) as description from invoices where client_id='$id' " . $this->getBranchCondition('invoices') . " and type in(0,5,6) and draft <> 1 %s
                UNION select 1 as my_order , `created`,'Starting Balance',(select id from journals where draft = 0 and entity_type='invoice_payment' and entity_id=invoice_payments.id),null,0,'$id',date,status,id as invoice_id,amount,0,'due_after','payment_method',currency_code,null from invoice_payments where client_id='$id' " . $this->getBranchCondition('invoice_payments') . " and payment_method = 'starting_balance' %s
                UNION select 2 as my_order,`created`,'payment',id,(select no from invoices where id=invoice_id " . $this->getBranchCondition('invoices') . " ),null,client_id,date,status,invoice_id,amount,0,null,payment_method,currency_code,null from invoice_payments where (invoice_id in(select id from invoices where client_id='$id' " . $this->getBranchCondition('invoices') . " and type in(0,5,16) and draft <> 1) or (client_id='$id' AND invoice_id is null))  " . $this->getBranchCondition('invoice_payments') . "  and payment_method <> 'client_credit' and payment_method <> 'starting_balance' and status=1  %s 
                UNION SELECT 1 AS my_order,J.`created`,'journal',J.`id`,null,null,'$id',J.`date`,1,JT.`id`,ROUND(JT.currency_credit-JT.currency_debit,2)*-1 AS `amount`,0,null,'none',JT.currency_code,JT.description FROM `journal_transactions` AS `JT`  LEFT JOIN `journals` AS `J` ON(`JT`.`journal_id` = `J`.`id`) WHERE J.draft = 0 and ((J.entity_type not in('invoice','invoice_payment','refund_receipt','credit_note', 'year_opening_balance','year_closing_balance')) OR (J.entity_type in ('invoice','refund_receipt','credit_note') and J.entity_id not in (select id from invoices where client_id = $id) )) " . $this->getBranchCondition('J') . " and JT.journal_account_id={$journal_account_id} %s
                UNION select 3 as my_order,`created`,'Refund Payment',id,(select no from invoices where id=invoice_id " . $this->getBranchCondition('invoices') . "),'6-1',client_id,date,status,'refund',amount,0,null,payment_method,currency_code,null from invoice_payments where  status=1 and (invoice_id in(select id from invoices where client_id='$id' AND payment_method != 'client_credit' " . $this->getBranchCondition('invoices') . " and type in(6) and draft <> 1) " . $this->getBranchCondition('invoice_payments') . ") %s 
                UNION SELECT 1 AS my_order, J.`created`, 'journal', J.`id`, null, null, '16', J.`date`, 1, JT.`id`, ROUND(JT.currency_credit - JT.currency_debit, 2) * -1 AS `amount`, 0, null, 'none', JT.currency_code, JT.description FROM journal_transactions AS JT LEFT JOIN journals AS J ON (JT.journal_id = J.id) WHERE JT.journal_account_id = {$journal_account_id} AND JT.subkey like '%%-client-1' 
                ORDER BY date ASC, `created` ASC, my_order ASC";

        $allt_starting = sprintf ( $bare_query , $payment_start_query,$payment_start_query , $payment_start_query , $payment_start_query,$payment_start_query);

        if ( $page > 1 ){

            $starting_invoice_with_payment = $this->Client->query($allt_starting. $starting_limit , false);
        }
        if(!empty($date_from)){
            $before = sprintf ( $bare_query , $before_invoice_more_query,$before_invoice_more_query , $before_invoice_more_query , $before_invoice_more_query,$before_invoice_more_query);
            $before_starting_invoice_with_payment = $this->Client->query($before, false);
            if(isset($starting_invoice_with_payment)){
                $starting_invoice_with_payment=array_merge($starting_invoice_with_payment,$before_starting_invoice_with_payment);
            }else{
                $starting_invoice_with_payment=$before_starting_invoice_with_payment;
            }
        }
        //print_r($starting_invoice_with_payment);
		//$starting_balance =  0 ;
        foreach ($starting_invoice_with_payment as $t) {
            $t = $t[0];
            if ($t['invoice'] == 'payment' or ($t['invoice']=="Starting Balance" || ($t['invoice'] == 'invoice' and in_array($t['type'], array(Invoice::Refund_Receipt,Invoice::Credit_Note))))) {

                $t['summary_total'] = $t['summary_total'] * -1;

                $negation=1;
            }
            if($t['type']=="6-1"){
                $t['summary_total'] = $t['summary_total'] * -1;
            }
            $starting_balance[$t['currency_code']] +=$t['summary_total'];
        }
		debug ( $starting_balance ) ;
        // print_r($starting_balance);
        $paginator_url = 'date_from=' . $this->params['url']['date_from'] . '&';
        $paginator_url .='date_from=' . $this->params['url']['date_to'] . '&';
        $this->set('paginator_url', $paginator_url);
        $this->set('starting_balance', $starting_balance);
        $allt = sprintf ( $bare_query ,$payment_more_query,$invoice_more_query , $payment_more_query , $payment_more_query,$payment_start_query);

        $invoice_with_payment = $this->Client->query($allt . " LIMIT $per_page OFFSET " . ($per_page * ($page - 1)));
        $this->loadModel('Invoice');
        $this->loadModel('InvoiceLayout');
        $layoutsList=$this->InvoiceLayout->find('all');
        $layouts=[];
        foreach($layoutsList as $layout){
            $layouts[$layout['InvoiceLayout']['id']]=$layout;
        }

        $invoices=[];
        $this->loadModel('InvoicePayment');
        $this->InvoicePayment->applyBranch['onFind'] = false;
        foreach($invoice_with_payment as $k => $t){
            $t=$t[0];
            if(isset($_GET['show_details']) && $_GET['show_details']=='true') {
                if (in_array($t['type'], [Invoice::Invoice, Invoice::Refund_Receipt, Invoice::Estimate, Invoice::Credit_Note])) {
                    $ids[] = $t['id'];
                }
            }

            if($t['invoice'] == 'payment'){
                $conditions['InvoicePayment.id'] = $t['id'];
                $invoicepayment = $this->InvoicePayment->find('first', array('conditions' => $conditions));
                $invoice_with_payment[$k][0]['code'] = !empty($invoicepayment['InvoicePayment']['code']) ? $invoicepayment['InvoicePayment']['code'] : $invoicepayment['InvoicePayment']['id'];
            }
        }
        $this->InvoicePayment->applyBranch['onFind'] = true;

        $this->Invoice->applyBranch['onFind'] = false;
        $this->Invoice->unbindModel(['hasMany' => ['InvoiceItem']]);
        $invoices_list = $this->Invoice->find('all', ['conditions' => ['Invoice.id' => $ids]]);
        $this->Invoice->applyBranch['onFind'] = true;
        
        foreach($invoices_list as $invoice){
            $invoices[$invoice['Invoice']['id']] = $invoice;
            $total_discount[$invoice['Invoice']['id']] = $invoice['Invoice']['summary_discount'];
        }

        $this->loadModel('InvoiceItem');
        $this->InvoiceItem->unbindModel(['belongsTo' => ['Invoice']]);
        $invoice_items = $this->InvoiceItem->find('all', ['conditions' => ['InvoiceItem.invoice_id' => $ids], 'recursive' => 2]);
        foreach($invoice_items as $item){
            $copaymentTotal[$item['InvoiceItem']['invoice_id']] = 0;
            $item['InvoiceItem']['Product'] = $item['Product'];
            $item['InvoiceItem']['product_image'] = '';
            if($item['InvoiceItem']['product_id']){
                $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product', $item['InvoiceItem']['Product']['id']);
                if(count( $defaultS3Images))
                {
                    $item['InvoiceItem']['product_image'] = $defaultS3Images[0]->files->path;
                    $item['InvoiceItem']['product_image_s3'] = true;
                }elseif (is_countable($item['Product']['ProductMasterImage']) && count($item['Product']['ProductMasterImage'])) {
                    $item['InvoiceItem']['product_image'] = 'https://' . getCurrentSite('subdomain') . appendFullPath(getCurrentSite('id'), 'product-images', $item['Product']['ProductMasterImage']['file']);
                    $item['InvoiceItem']['product_image_s3'] = false;
                }
            }
            $subtotal = (float)$item['InvoiceItem']['unit_price'] * (float)$item['InvoiceItem']['quantity'];
            $discount_val = 0 ; 
            if ( !empty($item['InvoiceItem']['discount']))
            {
                $discount_val = $this->Invoice->calculate_item_discount($item['InvoiceItem'], $subtotal);
                $total_discount[$item['InvoiceItem']['invoice_id']] += $discount_val;
                $subtotal -= $discount_val ;
                $item['InvoiceItem']['discount_value'] = $discount_val;
                $item['InvoiceItem']['discount_string'] = ($item['InvoiceItem']['discount_type'] == Invoice::DISCOUNT_TYPE_PERCENTAGE ? $item['InvoiceItem']['discount']."%":format_price_simple($item['InvoiceItem']['discount'],false,false));
            }
            $item['InvoiceItem']['item_subtotal'] = $subtotal ; 
            if (ifPluginActive(INSURANCE_PLUGIN)) {
                if (!empty($item['InvoiceItem']['extra_details']) && is_string($item['InvoiceItem']['extra_details'])){
                    $item['InvoiceItem']['copayment'] = json_decode($item['InvoiceItem']['extra_details'], true)['copayment'] ?? 0;
                    $copaymentTotal[$item['InvoiceItem']['invoice_id']] += $item['InvoiceItem']['copayment'];
                }
            }
            $invoices[$item['InvoiceItem']['invoice_id']]['InvoiceItem'][] = $item['InvoiceItem'];
            $invoices[$item['InvoiceItem']['invoice_id']]['Invoice']['summary_copayment'] = $copaymentTotal[$item['InvoiceItem']['invoice_id']];
            $invoices[$item['InvoiceItem']['invoice_id']]['Invoice']['summary_total_discount'] = $total_discount[$item['InvoiceItem']['invoice_id']];
        }
        $this->set('invoices', $invoices);
        $this->loadModel('Tax');
        $this->set('taxes_list', $this->Tax->find('all'));
        $this->set('layouts',$layouts);
        $count = $this->Client->flat_query_results("select count(*)as cc from ( $allt )aaa");
        $this->set('transacti   on_count', $count[0]['cc']);
        $this->set('current_page', $page);
        $this->set('count', ceil($count[0]['cc'] / $per_page));


        $this->set('InvoiceTypeList', Invoice::getInvoiceTypeList());
        $this->set('payment_methods', InvoicePayment::getAllPaymentMethods());
        $this->set('invoice_with_payment', $invoice_with_payment);

        $placeholders = array_merge(PlaceHolder::site_place_holder(), PlaceHolder::datetime_place_holder(), PlaceHolder::client_place_holder($client), PlaceHolder::staff_place_holder($owner['staff_id']));
        $this->set('placeholders', $placeholders);

        $statement_header_html = settings::getValue(0, "statement_header_html");
        $custom_header = PlaceHolder::replace ($statement_header_html, array_keys($placeholders), $placeholders);
        $this->set('custom_header', $custom_header);

        $statement_footer_html = settings::getValue(0, "statement_footer_html");
        $custom_footer = PlaceHolder::replace($statement_footer_html, array_keys($placeholders), $placeholders);
        $this->set('custom_footer', $custom_footer);

        $transcation_columns_setting = settings::getValue(0, "transcation_columns_setting");
        $this->set('transcation_columns_setting', $transcation_columns_setting);

        $sc=$this->Client->get_statement_data($id, $date_from, $date_to);
        $this->set('paid_to_date', $sc['paidAmounts']);
        $this->set('owner', $owner);

        if ( $send_email == 1 ) {
            $this->autoRender = false ;
            $v = $this->render('../clients/owner_transaction_list');
            $site = getAuthOwner();
            if ( $this->SysEmails->sendEmail($client['Client']['email'], $site, ['EmailTemplate'=> ['body' => $v , 'subject' => __("Transaction List" , true )] ], [], [], array('client_id' => $client['Client']['id'])) ) {
                $this->flashMessage(__('Email has been sent', true), 'Sucmessage');

            } else {
                $this->flashMessage(__('Could not send statement', true));
            }
            $this->redirect(array('action' => 'view', $id."#TransactionList")); die ;
        }
    }

    function owner_czones() {
        $this->layout = '';
		$this->Country=GetObjectOrLoadModel('Country');
        $this->set('czones', $this->Country->getCountriesTimezones());
    }

    function owner_add_payment_credit($id = false,$work_order_id=false) {

        $owner = getAuthOwner();

        $this->loadModel('InvoicePayment');
        if (!empty($this->data['InvoicePayment']['client_id'])) {
            $id = $this->data['InvoicePayment']['client_id'];
        }

        $this->ClientValidation->validateClientSuspended($id);
        $client = $this->Client->read(null, $id);


        $this->set('client', $client);
        if (!$client) {
            if (!IS_REST) {
                $this->flashMessage(__('Client not found', true));
                $this->redirectToIndex();
            } else {
                $this->cakeError("error400", ["message" => sprintf(__("%s is not found", true), __('client', true))]);
            }
        }
        if (!check_permission(Invoices_Add_Payments_to_All)) {
            if (!IS_REST) {
                $this->flashMessage(__( "You are not allowed to view this page", true));
                $this->redirectToIndex();
            } else {
                $this->cakeError("error400", ["message" => sprintf(__('You don\'t have permission to open this %s', true), __('Page',true))]);
            }
        }
        // $this->loadModel('ClientToken');
        // $tokens = $this->ClientToken->find('all', array('order' => 'ClientToken.id desc', 'conditions' => array('ClientToken.payment_method' => 'stripe', 'ClientToken.client_id' => $id)));
        // $tokens_count = $this->ClientToken->find('count', array('conditions' => array('payment_method' => 'stripe', 'ClientToken.client_id' => $id)));
        // $this->set('tokens', $tokens);
        // $this->set('tokens_count', $tokens_count);

        $this->set('client_phone_number', $client['Client']['phone2'] ?: $client['Client']['phone1']);
        if (!empty($this->data)) {
            App::import('Vendor', 'AutoNumber');
            /** Validate Invoice Payment Distributions **/
            if (!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && check_permission(Invoices_Add_Payments_to_All) && !empty($this->data['paymentInvoices'])) {
                $validation_result = CreditDistributionService::validateDistributionForm($this->data['paymentInvoices'], $this->data['InvoicePayment']['amount']);
                if (!$validation_result['result']) {
                    $this->flashMessage($validation_result['message'], 'Errormessage', 'secondaryMessage');
                }
            } else {
                $validation_result['result'] = true;
            }

            if ($validation_result['result']) {
                $this->data['InvoicePayment']['client_id'] = $id;
                if (empty($this->data['InvoicePayment']['staff_id']) || !check_permission(Invoices_Add_Payments_to_All))
                    $this->data['InvoicePayment']['staff_id'] = $owner['staff_id'];

                $this->data['InvoicePayment']['client_pay'] = 0;
                $invoicePaymentAttachments = $this->data['InvoicePayment']['attachment'];
                unset($this->data['InvoicePayment']['attachment']);

                /**
                 * Updates the 'extra_details' field in InvoicePayment with the client's balance 
                 * if the selected client is different from the default POS client.
                 * 
                 * - Checks if the selected client ID differs from the default POS client ID.
                 * - If client balance restrictions are not disabled in settings:
                 *   - Retrieves the total unpaid invoices for the client in the specified currency.
                 *   - Retrieves the total available credit for the client in the same currency.
                 *   - Calculates the net balance (unpaid invoices - available credit).
                 *   - Stores the calculated balance in 'extra_details' as a JSON-encoded value.
                 */
                if(settings::getValue(PosPlugin, 'pos_default_client') != $id) {
                    if (!settings::getValue(0, 'disable_client_unpaid_balance_in_extra_details')) {
                        $tempJSON['client_balance'] = $this->Client->getClientTotalBalance($id, $this->data['InvoicePayment']['currency_code']);
                        $this->data['InvoicePayment']['extra_details'] = json_encode($tempJSON);
                    }
                }

                $result = $this->Client->ownerAddPayment($this->data);

                $attachments = [];
                if(!empty($invoicePaymentAttachments))
                {
                    $attachments = explode(',',$invoicePaymentAttachments);
                    izam_resolve(AttachmentsService::class)->save('invoice_payment', $result['payment_id'], $attachments);
                }

                if (IS_REST) {
                    $this->set('id', $result['payment_id']);
                    $this->render('created');
                    return;
                }
                if ($result['out']) {
                    $this->redirect($result['url'], 302);
                }

                if ($result['status']) {
                    if(isset($result['client_secret'])){
                        $this->layout = false;
                        $this->set('client_secret', $result['client_secret']);
                        $this->set('public_key', $result['public_key']);
                        $this->render('/invoice_payments/stripe_next_action');
                        return;
                    }
                    $this->InvoicePayment->alias = 'InvoicePayment';
                    $invoicePayment = $this->InvoicePayment->read(null, $result['payment_id']);

                    /** Distribute Client Credit on Invoices **/
                    if (
                        !settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') &&
                        check_permission(Invoices_Add_Payments_to_All) &&
                        !empty($this->data['paymentInvoices']) &&
                        $invoicePayment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED
                    ) {
                        $dist_service = new CreditDistributionService();
                        $distribution_data = $dist_service->addSubPayments($this->data['paymentInvoices'], $invoicePayment);
                        if ($distribution_data)
                            $dist_service->addDistributions($invoicePayment['InvoicePayment']['id'], $distribution_data);
                    }

                    if ($result['convert_to_invoice_payment'] == false) {
                        $this->add_actionline(ACTION_ADD_CLIENT_PAYMENT, array('primary_id' => 0, 'secondary_id' => $invoicePayment['InvoicePayment']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['InvoicePayment']['status'], 'param3' => $invoicePayment['InvoicePayment']['id'], 'param4' => $invoicePayment['InvoicePayment']['payment_method'], 'param5' => $invoicePayment['InvoicePayment']['transaction_id']));
                    } else {
                        $this->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => empty($invoicePayment['Invoice']['draft']) ? $invoicePayment['Invoice']['payment_status'] : -1, 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));
                    }

                    $this->Client->pay_invoices_from_credit($id);
                    $this->flashMessage(__('The payment has been added to the client', true), 'Sucmessage');
                    if($work_order_id)
                        $this->redirect(array('controller'=>'work_orders', 'action' => 'view', $work_order_id));
                        else
                    $this->redirect(array('action' => 'view', $id));
                } else {
                    $this->flashMessage(__('Could not add payment ', true) . $result['error_message']);
                }
            }
        } else {
            if (empty($client['Client']['default_currency_code'])) {
                $this->data['InvoicePayment']['currency_code'] = $owner['currency_code'];
            } else {
                $this->data['InvoicePayment']['currency_code'] = $client['Client']['default_currency_code'];
            }
            if($work_order_id)
            $this->data['InvoicePayment']['payment_work_order_id'] = $work_order_id;
            if (isset($this->params['url']['amount'])) {
                $this->data['InvoicePayment']['amount'] = $this->params['url']['amount'];
            }
            if (isset($this->params['url']['currency_code'])) {
                $this->data['InvoicePayment']['currency_code'] = $this->params['url']['currency_code'];
            }
            $this->data['InvoicePayment']['date'] = format_date(date("Y-m-d"));
            $this->data['InvoicePayment']['status'] = 1;
            $this->data['InvoicePayment']['staff_id'] = $owner['staff_id'];
        }

        if (!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && check_permission(Invoices_Add_Payments_to_All)) {
            $invoicesOptions = CreditDistributionService::getInvoicesOptions($client['Client']['id'], $this->data['InvoicePayment']['currency_code']);
            $has_invoices = CreditDistributionService::validateClientHasValidInvoices($client['Client']['id']);
            $this->set('invoicesOptions', $invoicesOptions);
            $this->set('hasInvoices', $has_invoices);
        }

        $staffs = $this->InvoicePayment->Staff->getList();
        if (!check_permission(Invoices_Add_Invoice_Payments)) {
            $staffs = [getAuthOwner('staff_id') => $staffs[getAuthOwner('staff_id')]]; //this mean  he has only permission to Add Payments to his own (created) Invoices
        }
        $this->set('staffs', $staffs);
        $this->set('statuses', InvoicePayment::getPaymentStatus());
        $this->loadModel('SitePaymentGateway');
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));
        $Tamara = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'tamara')));
        $this->set('Tamara', $Tamara);

        $this->set('payment_treasury',$payment_treasury=$this->SitePaymentGateway->find('list',array('fields'=>'SitePaymentGateway.payment_gateway,SitePaymentGateway.treasury_id')));
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));

        unset($paymentMethods['client_credit']);
        $this->set('paymentMethods', $paymentMethods);
        $Stripe = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'stripe')));
        $this->set('Stripe', $Stripe);
        if(!empty($Stripe['SitePaymentGateway']['username'])) {
            $this->set('customerSessionClientSecret', $this->InvoicePayment->create_stripe_customer_session($Stripe['SitePaymentGateway']['username'], $id));
        }

        $offlines = array('offline', 'bank', 'cash', 'cheque', 'credit');
        foreach ($fullPG as $method) {
            if (!empty($method['SitePaymentGateway']['manually_added']))
                $offlines[] = $method['SitePaymentGateway']['payment_gateway'];
        }
        foreach ($offlines as $offline) {
            $instructions[$offline] = $this->SitePaymentGateway->field('option1', array('SitePaymentGateway.payment_gateway' => $offline, 'SitePaymentGateway.active' => 1));
        }

        $this->set('offlines', $offlines);
        $Square = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'square')));
        $this->set('Square', $Square);
        $Tap = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'tap')));
        $this->set('Tap', $Tap);
        $this->set(compact('invoice'));


        $this->loadModel('Currency');
        $this->set('currencyCodes', $this->Currency->getCurrencyList(array(), true));
        $this->loadModel('Treasury');
        $this->loadModel('ItemPermission');
        
        $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT)) ;
        $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/client/list';

        $this->crumbs[1]['title'] = __('Add Payment', true);
        $this->crumbs[1]['url'] = '#';


//        $this->set('file_settings', $this->Invoice->InvoicePayment->getFileSettings());
        $this->set('file_settings', $this->InvoicePayment->getFileSettings());
        $this->set('content', $this->get_snippet('add-payment-owner'));
        $this->set('title_for_layout',  __('Add Payment', true));
    }

    function owner_test_mail($id = null) {
        $owner = getCurrentSite();
        echo "<pre>";
        $this->loadModel('InvoicePayment');
        $invoicePayment = $this->InvoicePayment->read(null, $id);

        $email = $this->SysEmails->creditpaymentCompleteClient($invoicePayment['InvoicePayment'], $invoicePayment['Client'], $owner);

        die();
    }

    function client_add_payment_credit() {

        $client = getAuthClient();
        $owner = getCurrentSite();
        $this->loadModel('Invoice');
        // Redirect Client to invoice pay page if only one unpaid invoice found
        if (isset($this->params['url']['currency_code']) and $this->params['url']['currency_code']!="") {
        $overdue_amount = $this->Client->getUnpaid($client['id'], $this->params['url']['currency_code']);
         $count_unpaid_invoice=$this->Invoice->find('count', array('order'=>'Invoice.date asc,Invoice.id asc','conditions' => array('currency_code'=>$this->params['url']['currency_code'],'payment_status!=2', 'Invoice.type' => 0, 'Invoice.client_id' => $client['id'], 'Invoice.draft <> 1')));
         if($count_unpaid_invoice==1){
         $first_unpaid_invoice=$this->Invoice->find('first', array('order'=>'Invoice.date asc,Invoice.id asc','conditions' => array('currency_code'=>$this->params['url']['currency_code'],'payment_status!=2', 'Invoice.type' => 0, 'Invoice.client_id' => $client['id'], 'Invoice.draft <> 1')));
         if($overdue_amount[$this->params['url']['currency_code']]==$first_unpaid_invoice['Invoice']['summary_unpaid']){
         $this->redirect(array('controller'=>'invoices','action'=>'pay',$first_unpaid_invoice['Invoice']['id']))    ;
         }
         }
         }
         
        $this->loadModel('InvoicePayment');

        $this->set('AdvancedPaymentMethods', InvoicePayment::AdvancedPaymentMethods());
        // $this->loadModel('ClientToken');
        // $tokens = $this->ClientToken->find('all', array('order' => 'ClientToken.id desc', 'conditions' => array('ClientToken.payment_method' => 'stripe', 'ClientToken.client_id' => $client['id'])));
        // $tokens_count = $this->ClientToken->find('count', array('conditions' => array('payment_method' => 'stripe', 'ClientToken.client_id' => $client['id'])));
        // $this->set('tokens', $tokens);
        // $this->set('tokens_count', $tokens_count);

        if (!empty($this->data)) {

            App::import('Vendor', 'AutoNumber');
            // Handle ccavenue payment_method
            if ($this->data['InvoicePayment']['payment_method'] == "ccavenue") {

                $this->Invoice->InvoicePayment->validate= array(
                    'first_name' => array('required' => true,'allowEmpty' => false, 'rule' => 'az123','message' => __('only English Alphanumeric characters Allowed', true)),
                    'last_name' => array('required' => true,'allowEmpty' => false, 'rule' => 'az123','message' => __('only English Alphanumeric characters Allowed', true)),
                    'address1' => array('required' => true,'allowEmpty' => false, 'rule' => 'az123','message' => __('only English Alphanumeric characters Allowed', true)),
                    'city' => array('required' => true,'allowEmpty' => false, 'rule' => 'az123','message' => __('only English Alphanumeric characters Allowed', true)),
                    'state' => array('required' => true,'allowEmpty' => false, 'rule' => 'az123','message' => __('only English Alphanumeric characters Allowed', true)),
                    'postal_code' => array('required' => true,'allowEmpty' => false, 'rule' => 'az123','message' => __('only English Alphanumeric characters Allowed', true)),
                    'phone1' => array('required' => true,'allowEmpty' => false, 'rule' => 'only1to9','message' => __('only English Alphanumeric characters Allowed', true)),
                );
            }

            if($this->data['InvoicePayment']['payment_method']=="client_credit"){
             $this->flashMessage(__('Thank you, Your payment has been completed.', true), 'Sucmessage');
             $this->Client->pay_invoices_from_credit($client['id'],true);   
             $this->redirect("/");
            }
            $this->data['InvoicePayment']['client_id'] = $client['id'];
            // $this->data['InvoicePayment']['status'] = 0;
            $this->data['InvoicePayment']['client_pay'] = 1;
            $this->data['InvoicePayment']['source'] = 'client_add_payment_credit';
            $result = $this->Client->ownerAddPayment($this->data);
            if ($result['out']) {
                if($this->data['InvoicePayment']['payment_method'] == "tabby") {
                    $redirect_url = $result['url']['web_url'];
                } else {
                    $redirect_url = $result['url'];
                }
                $this->redirect($redirect_url, 302);
            }

            if ($result['status']) {
                if(isset($result['client_secret'])){
                    $this->layout = false;
                    $this->set('client_secret', $result['client_secret']);
                    $this->set('public_key', $result['public_key']);
                    $this->render('/invoice_payments/stripe_next_action');
                    return;
                }

                $invoicePayment = $this->InvoicePayment->read(null, $result['payment_id']);
                $this->SysEmails->creditpaymentCompleteClient($invoicePayment['InvoicePayment'], $invoicePayment['Client'], $owner['Site']);
                $this->SysEmails->creditpaymentCompleteOwner($invoicePayment['InvoicePayment'], $invoicePayment['Client'], $owner['Site']);


                //(primary_id => invoice_id, sec => client_id, p1=>total, p2=> invoice_status, p3=>invoice_summary_paid, p4=>payment_id, p5=>payment_amount, p6=>payment_status, p7=>payment_method )

                $this->add_actionline(ACTION_CLIENT_ADD_CLIENT_PAYMENT, array('primary_id' => 0, 'secondary_id' => $invoicePayment['InvoicePayment']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['InvoicePayment']['status'], 'param3' => $invoicePayment['InvoicePayment']['id'], 'param4' => $invoicePayment['InvoicePayment']['payment_method'], 'param5' => $invoicePayment['InvoicePayment']['transaction_id']));
                $this->Client->pay_invoices_from_credit($client['id'],true);
                $this->flashMessage(__('Thank you, Your payment has been completed.', true), 'Sucmessage');

                $this->redirect(array('controller' => 'invoice_payments', 'action' => 'view', $result['payment_id']));
            } else {

                // $this->flashMessage(__('Could not add payment ' . $result['error_message'], true));
                $this->flashMessage(__('Could not add payment ', true) . $result['error_message']);
            }
        } else {
            // print_r($client);

            $this->data['InvoicePayment'] = $client;
            if (empty($client['default_currency_code'])) {
                $this->data['InvoicePayment']['currency_code'] = $owner['currency_code'];
            } else {
                $this->data['InvoicePayment']['currency_code'] = $client['default_currency_code'];
            }
            $this->data['InvoicePayment']['date'] = format_date(date("Y-m-d"));
            $this->data['InvoicePayment']['status'] = 1;
            $this->data['InvoicePayment']['staff_id'] = 0;
            if (isset($this->params['url']['currency_code']) and $this->params['url']['currency_code']!="") {

                //  $unpaid_count = $this->Client->getUnpaidCount($client['id'], $this->params['url']['currency_code']);
                //  if ($unpaid_count == 1) {
                //  $invoice = $this->InvoicePayment->Invoice->find('all', array('recursive' => -1, 'limit' => 10, 'conditions' => array('Invoice.draft <> 1', 'Invoice.type' => 0, 'Invoice.client_id' => $client['id'], 'OR' => array('Invoice.payment_status !=' => 2, 'Invoice.payment_status' => null)), 'fields' => 'Invoice.id, Invoice.no, Invoice.date, Invoice.client_business_name, Invoice.payment_status, Invoice.summary_unpaid, Invoice.currency_code', 'order' => 'Invoice.date DESC'));

                //  $this->redirect(array('controller' => 'invoices', 'action' => 'pay', $invoice[0]['Invoice']['id']));
                //}
                $this->data['InvoicePayment']['currency_code'] = $this->params['url']['currency_code'];
                
                $this->data['InvoicePayment']['amount'] = $overdue_amount[$this->params['url']['currency_code']];
                $this->set('total_amount', format_price($overdue_amount[$this->params['url']['currency_code']], $this->params['url']['currency_code']));
                $this->set('fixed_amount', true);
            } else {
                $this->set('total_amount', 0);
                $this->set('fixed_amount', false);
            }
        }
        $this->loadModel('SitePaymentGateway');
        $gateway = $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id'));

        $tamaraPayment = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active' => 1 , 'payment_gateway' => 'tamara')));
        if($tamaraPayment['SitePaymentGateway']['option3']){
            $this->set('tamara_public_key', $tamaraPayment['SitePaymentGateway']['option3']);
        }

        $tabbyPayment = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active' => 1 , 'payment_gateway' => 'tabby')));
        if($tabbyPayment['SitePaymentGateway']['username']){
            $this->set('tabby_public_key', $tabbyPayment['SitePaymentGateway']['username']);
            $this->set('tabby_merchantCode', $tabbyPayment['SitePaymentGateway']['option2']);
        }


        $this->set('default_payment_gateway', $gateway);


        $this->set('default_payment_method',$gateway);

        if ($gateway) {
            $this->data['InvoicePayment']['payment_method'] = $gateway;
        }


//        /function getPaymentGateways($site_id = false,$list=true,$only_active=true, $include_previously_used=false,$disable_for_client=true) {

        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, false, false);

        if (empty($paymentMethods)) {
            $this->redirect('/');
        }

        unset($paymentMethods['client_credit']);
            if(!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices')){
            $client_credit = $this->Invoice->Client->client_credit($client['id'], $_GET['currency_code']);

            if ($client_credit <= 0) {
                unset($paymentMethods['client_credit']);
            }elseif($client_credit<$overdue_amount[$this->params['url']['currency_code']]){
              unset($paymentMethods['client_credit']);  
            } else {
                
                
                $this->set('client_credit', $client_credit);
                unset($paymentMethods['client_credit']);
                $paymentMethods['client_credit'] = sprintf(__('Client Credit (%s)', true), $client_credit);
            }
         } 
        $this->set('paymentMethods', $paymentMethods);

        $offlines = array('offline', 'bank', 'cash', 'cheque', 'credit');
        foreach ($fullPG as $method) {
            if (!empty($method['SitePaymentGateway']['manually_added']))
                $offlines[] = $method['SitePaymentGateway']['payment_gateway'];
        }
        foreach ($offlines as $offline) {
            $instructions[$offline] = $this->SitePaymentGateway->field('option1', array('SitePaymentGateway.payment_gateway' => $offline, 'SitePaymentGateway.active' => 1));
        }

        $this->set('offlines', $offlines);




        $Stripe = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'stripe')));
        $this->set('Stripe', $Stripe);
        if(!empty($Stripe['SitePaymentGateway']['username'])) {
            $this->set('customerSessionClientSecret', $this->InvoicePayment->create_stripe_customer_session($Stripe['SitePaymentGateway']['username'], $client['id']));
        }

        $Square = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'square')));
        $this->set('Square', $Square);
        $Tap = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'tap')));
        $this->set('Tap', $Tap);

        $SecurePay = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active' => 1 , 'payment_gateway' => 'securepay')));
        $this->set('SecurePay', $SecurePay);
        $this->set(compact('invoice'));


        $this->loadModel('Currency');
        $this->set('currencyCodes', $this->Currency->getCurrencyList(array(), true));
        $this->loadModel('Country');
        $this->set('countryCodes', $this->Country->getCountryList());



        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/client/list';

        $this->crumbs[1]['title'] = __('Add Payment', true);
        $this->crumbs[1]['url'] = '#';


//        $this->set('file_settings', $this->Invoice->InvoicePayment->getFileSettings());
        $this->set('file_settings', $this->InvoicePayment->getFileSettings());
        $this->set('content', $this->get_snippet('add-payment-owner'));
        $this->set('title_for_layout',  __('Add Payment', true));
    }

    function owner_delete_client($id = null)
    {
        if (empty($_POST['submit_btn'])) {
            $this->_record_referer_path();
        } else {
            $referer_url = $this->_get_referer_path(true);
        }
        if (!isset($referer_url)) {
            $referer_url = $this->_get_referer_path(false);
        }

        $this->set('referer_url', $referer_url);
        $this->loadModel('ItemStaff');
        $this->loadModel('Invoice');
        $this->loadModel('Post');
        if(ifPluginActive(INSTALLMENT_AGREEMENT_PLUGIN)) {
            $this->loadModel('InvoiceInstallmentAgreement');
        }

        /** If Request Type is Post (As in Multiple Delete) **/
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        if (!$id && empty($_POST)) {
            if (IS_REST) {
                $this->cakeError("error400", ["message" => sprintf(__('Invalid %s', true), __('client', true))]);
            } else {
                $this->flashMessage(sprintf(__('Invalid %s', true), __('client', true)));
                $this->redirect($referer_url);
            }
        }

        $module_name = __('client', true);
        if (count((array)$_POST['ids']) > 1) {
            $module_name = __('clients', true);
        }

        $clients = $this->Client->find('all', array('conditions' => array('Client.id' => $id)));
        if (empty($clients)) {
            if (IS_REST) {
                $this->cakeError("error404", ["message" => sprintf(__('%s not found', true), ucfirst($module_name))]);
            } else {
                $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
                $this->redirect($referer_url);
            }
        }

        /** Permission Check IF Not Owner **/
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = $owner['staff_id'];
            foreach ($clients as $client) {
                if ((!check_permission(Edit_Delete_all_clients) && $client['Client']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_clients)) {
                    $exit = true;
                    if (ifPluginActive(FollowupPlugin))
                        $count = $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.item_id' => $client['Client']['id'], 'ItemStaff.staff_id' => $staff)));

                    if ($count > 0)
                        $exit = false;

                    if ($exit) {
                        if (IS_REST) $this->cakeError('error403');
                        $this->flashMessage(__("You are not allowed to edit this client", true), 'Errormessage', 'secondaryMessage');
                        $this->redirect($referer_url);
                    }
                }
            }
        }
        $entityClients = [];

        /** Installment Agreements Check **/
        if (ifPluginActive(INSTALLMENT_AGREEMENT_PLUGIN)) {
            foreach ($clients as $client) {
                $installment = $this->InvoiceInstallmentAgreement->find('count', [
                    'applyBranchFind' => false,
                    'conditions' => [
                        'InvoiceInstallmentAgreement.client_id' => $client['Client']['id'],
                        'InvoiceInstallmentAgreement.deleted_at IS NULL'
                    ]
                ]);

                if ($installment > 0) {
                    $errorMsg = __("You cannot delete the client as there's related installment agreement", true);
                    if (IS_REST) {
                        $this->cakeError("error400", ["message" => $errorMsg]);
                    } else {
                        $this->flashMessage($errorMsg);
                        $this->redirect($referer_url);
                    }
                }
            }
        }

        /** Attendance Log Check **/
        foreach ($clients as $client) {
            $entityClients[$client['Client']['id']] = getRecordWithEntityStructureForActivityLog('client', $client['Client']['id'], 2)->toArray();
            if (ifPluginInstalled(CLIENT_ATTENDANCE_PLUGIN)) {
                $this->loadModel('ClientAttendanceLog');
                $logCount = $this->ClientAttendanceLog->find('count', [
                    'conditions' => [
                        'ClientAttendanceLog.client_id' => $client['Client']['id'],
                        'ClientAttendanceLog.deleted_at IS NULL'
                    ]
                ]);

                if ($logCount > 0) {
                    $errorMsg = __('You cannot delete the Client as he already has an Attendance Log', true);
                    if (IS_REST) {
                        $this->cakeError("error400", ["message" => $errorMsg]);
                    } else {
                        $this->flashMessage($errorMsg);
                        $this->redirect($referer_url);
                    }
                }
            }
            if (ifPluginInstalled(RENTAL_PLUGIN)) {
                $this->loadModel('RentalReservationOrder');
                $reservation_count = $this->RentalReservationOrder->find( 'first' , ['applyBranchFind' => false, 'conditions' => ['RentalReservationOrder.client_id' => $client['Client']['id']]] );
                if ($reservation_count) {
                    $errorMsg = sprintf(__t("You cannot delete the client as there's a related reservation order #%s"),"<a class='link-text' href='/v2/owner/rental/reservation-orders/".$reservation_count['RentalReservationOrder']['id']."'>".$reservation_count['RentalReservationOrder']['order_number']."</a>");
                    if (IS_REST) {
                        $this->cakeError("error400", ["message" => $errorMsg]);
                    } else {
                        $this->flashMessage($errorMsg);
                        $this->redirect($referer_url);
                    }
                }
            }
            if (ifPluginInstalled(MANUFACTURING_PLUGIN)) {
                $this->loadModel('ManufacturingOrder');
                $manufacturing_order = $this->ManufacturingOrder->find('first', ['applyBranchFind' => false, 'conditions' => ['ManufacturingOrder.client_id' => $client['Client']['id']]]);
                if ($manufacturing_order) {
                    $errorMsg = sprintf(__t("You cannot delete the client as there's a related Manufacturing Order #%s"), "<a class='link-text' href='/v2/owner/entity/manufacturing_order/" . $manufacturing_order['ManufacturingOrder']['id'] . "/show'>" . $manufacturing_order['ManufacturingOrder']['code'] . "</a>");
                    if (IS_REST) {
                        $this->cakeError("error400", ["message" => $errorMsg]);
                    } else {
                        $this->flashMessage($errorMsg);
                        $this->redirect($referer_url);
                    }
                }
            }
        }

        if (IS_REST) {
            $_POST['submit_btn'] = 'yes';
            $_POST['ids'] = [$id];
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];

            try {
                $this->Client->check_deletion($id);
            } catch (CanNotDeleteClientException $exception) {
                $message = $exception->getMessage();

                if (IS_REST){
                    $this->cakeError('error403', [
                        'message' => $message ?? __("You are not allowed to delete this client", true)
                    ]);
                }

                $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
                $this->redirect($referer_url);
            }

            foreach ($_POST['ids'] as $k => $v) {
                if (!$this->Client->deleteAble($v)) {
                    if (IS_REST) $this->cakeError('error400', ["message" => sprintf(__('Could not delete %s, Please check that you have deleted all %s transactions first.', true), ucfirst($module_name), ucfirst($module_name))]);
                    $this->flashMessage(sprintf(__('Could not delete %s, Please check that you have deleted all %s transactions first.', true), ucfirst($module_name), ucfirst($module_name)));
                    $this->redirect($referer_url);
                }
            }
            $entity = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::CLIENT_ENTITY_KEY);
            if ($_POST['submit_btn'] == 'yes' && !($hasMembershipOrCreditCharge = $this->Client->hasMembershipOrCreditCharge($id)) && $this->Client->delete_with_related($id)) {
                foreach ($clients as $client) {
                    izam_resolve(ClientService::class)->delete($entityClients[$client['Client']['id']]);
                    $this->add_actionline(ACTION_DELETE_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param4' => $client['Client']['client_number'], 'param5' => $client['Client']['phone1'] . ' ' . $client['Client']['phone2']));
                    $this->Client->delete_auto_accounts($client['Client']['id']);
                    NotificationV2::delete_notificationbyref($client['Client']['id'], NotificationV2::NOTI_ASSIGN_CLIENT);
                    NotificationV2::delete_notificationbytrigger(NotificationV2::NOTI_TRIGGER_CLIENT, $client['Client']['id']);
                    (izam_resolve(\Izam\Entity\EntityDeleter\EntityDeleter::class))->delete($entity , $entityClients[$client['Client']['id']]['id']);
                }
                $this->add_stats(STATS_REMOVE_CLIENT, array(serialize($id)));
                if (IS_REST) {
                    $this->set("message", sprintf(__('%s has been deleted', true), ucfirst($module_name)));
                    $this->render("success");
                    return;
                } else {
                    $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                    $this->redirectToIndex();
                }
            } else {
                if (IS_REST) $this->cakeError("error500", ["message" => sprintf(__('Could not delete %s, Please check that you have deleted all client transactions first.', true), ucfirst($module_name))]);

                if (isset($hasMembershipOrCreditCharge) && $hasMembershipOrCreditCharge) {
                    $this->flashMessage(sprintf(__('You cannot delete the client already has a membership or credit charge in the system', true), ucfirst($module_name)), 'Errormessage', 'secondaryMessage');
                    $this->redirect($referer_url);
                }

                $this->flashMessage(sprintf(__('Could not delete %s, Please check that you have deleted all client transactions first.', true), ucfirst($module_name)));
                $this->redirect($referer_url);
            }
        } else if (empty ($_POST['submit_btn']) && !empty($id)) {
            $this->set('credit_notes_count', $this->Client->get_invoices_count($id, Invoice::Credit_Note));
            $this->set('refund_receipts_count', $this->Client->get_invoices_count($id, Invoice::Refund_Receipt));
            $this->set('estimates_count', $this->Client->get_invoices_count($id, Invoice::Estimate));
            $this->set('invoices_count', $this->Client->get_invoices_count($id, Invoice::Invoice));
            $this->set('payments_count', $this->Client->get_payment_count($id));
            $this->set('appointments_count', $this->Client->get_appointment_count($id));
            $this->set('notes_count', $this->Client->get_note_count($id));
            $this->set('expenses_count', $this->Client->get_expense_count($id, false));
            $this->set('incomes_count', $this->Client->get_expense_count($id, true));
        }

        $this->set('clients', $clients);
        $this->set('module_name', $this->Client->name);
        $this->set('title_for_layout',  __('Delete Client', true));
    }
	
	function register()
	{

	    if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::WebsiteFrontPlugin) && empty(getAuthClient()) && empty($this->data)) {
            $this->redirect("/contents/register");
        }
	    if(!settings::getValue(ClientsPlugin, 'client_permission_register'))
        {
            $this->flashMessage(__('Registration is disabled for this site',true));
            $this->redirect('/');
        }

		if(getAuthClient()){
			$this->flashMessage(__('You are already logged in',true));
			$this->redirect('/');
		}else{
		    $this->set('site', getCurrentSite());
				if(!empty($this->data))
				{
                    App::import('Vendor', 'AutoNumber');
				    $this->data['Client']['client_number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_CLIENT);
					if($this->Client->register($this->data)){

					\AutoNumber::update_auto_serial(\AutoNumber::TYPE_CLIENT);
					$result = $this->Client->login_system($this->data['Client']);
					$this->Client->writeClientSession($result);

					$previous_page = $this->Session->read("LOGIN_REDIRECT");
					if (empty($previous_page)) {
						$previous_page = "/";
					}
					$this->Session->delete("LOGIN_REDIRECT");
					$url = str_replace('http://', 'https://', Router::url($previous_page, true));
					$this->flashMessage(__('You Successfully Registered', true), 'Sucmessage');
					$this->redirect($url);


					}else{
						$this->flashMessage(__("Couldn't Complete Registration Please Fix Below Errors", true));

					}
				}
			}
			$this->layout = false;
	}
	
	public function owner_settings(){
		if (!check_permission(EDIT_CLIENT_Settings)) {
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		
		if (!empty($this->data)) {
            settings::setData($this->data);
            write_client_settings();
            $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
        }
			$formdata = settings::formData(ClientsPlugin);
			$this->data[ClientsPlugin]['photo'] = $formdata['photo']['value'];
			$this->data[ClientsPlugin]['client_type'] = $formdata['client_type']['value'];
			$this->data[ClientsPlugin]['birth_date'] = $formdata['birth_date']['value'];
			$this->data[ClientsPlugin]['map_location'] = $formdata['map_location']['value'];
			$this->data[ClientsPlugin]['starting_balance'] = $formdata['starting_balance']['value'];
			$this->data[ClientsPlugin]['gender'] = $formdata['gender']['value'];
			$this->data[ClientsPlugin]['enable_client_credit_limit'] = $formdata['enable_client_credit_limit']['value'];
			$this->data[ClientsPlugin]['client_credit_period'] = $formdata['client_credit_period']['value'];
			$this->data[ClientsPlugin]['national_id'] = $formdata['national_id']['value'];
			$this->data[ClientsPlugin]['multiple_addresses'] = $formdata['multiple_addresses']['value'];
			$this->data[ClientsPlugin]['barcode'] = $formdata['barcode']['value'];
			$this->data[ClientsPlugin]['link'] = $formdata['link']['value'];
            $this->data[ClientsPlugin]['client_integrate_social_media'] = $formdata['client_integrate_social_media']['value'];

			$this->set('form_data',$formdata);

	}
	public function owner_permission_settings(){
		if (!check_permission(EDIT_CLIENT_Settings)) {
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		
		if (!empty($this->data)) {
			settings::setData($this->data);
			write_client_settings();
			$this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
		} else {
			$this->set('is_estimates_disabled',settings::getValue(InvoicesPlugin, 'disable_estimate_module'));
			
			$formdata = settings::formData(ClientsPlugin);
			$this->data[ClientsPlugin]['client_permission_register'] = $formdata['client_permission_register']['value'];
			if(ifPluginActive(BookingPlugin)){
				//booking
				$this->data[ClientsPlugin]['client_permission_disable_booking'] = $formdata['client_permission_disable_booking']['value'];
				$this->data[ClientsPlugin]['client_permission_disable_cancel_booking'] = $formdata['client_permission_disable_cancel_booking']['value'];
			}
			$this->data[ClientsPlugin]['register'] = $formdata['register']['value'];
                        $this->data[ClientsPlugin]['client_permission_view_appointment'] = $formdata['client_permission_view_appointment']['value'];
                        $this->data[ClientsPlugin]['client_disable_online_access'] = $formdata['client_disable_online_access']['value'];
			$this->data[ClientsPlugin]['client_permission_view_profile'] = $formdata['client_permission_view_profile']['value'];
			$this->data[ClientsPlugin]['client_permission_edit_profile'] = $formdata['client_permission_edit_profile']['value'];
			$this->data[ClientsPlugin]['client_permission_invoices'] = $formdata['client_permission_invoices']['value'];
			$this->data[ClientsPlugin]['client_permission_estimates'] = $formdata['client_permission_estimates']['value'];
			$this->data[ClientsPlugin]['client_permission_posts'] = $formdata['client_permission_posts']['value'];
			$this->data[ClientsPlugin]['enable_client_credit_limit'] = $formdata['enable_client_credit_limit']['value'];
			$this->data[ClientsPlugin]['client_credit_period'] = $formdata['client_credit_period']['value'];
			$this->data[ClientsPlugin]['client_permission_work_orders'] = $formdata['client_permission_work_orders']['value'];
			$this->data[ClientsPlugin]['show_client_billing_form'] = $formdata['show_client_billing_form']['value'];
            
			$this->set('form_data',$formdata);
			
		}
	}
	
	public function owner_processPaytabsPayment()
    {
        /** Handling Paytabs after Payment **/
        App::import('Vendor', 'PayTabsPayment', array('file' => 'payments/PayTabsPayment.php'));
        $this->loadModel('SitePaymentGateway');
        $paytabsRecord = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.payment_gateway' => 'paytabs')))['SitePaymentGateway'];
        $paymentReference = $_POST['payment_reference'];
        $paymentValidationData = PayTabsPayment::validatePaytabsPayment($paytabsRecord, $paymentReference);
        $responseMsg = $paymentValidationData['result'];
        $responseCode = $paymentValidationData['response_code'];
        $transactionID = $paymentValidationData['transaction_id'];

        $updateData = null;
        $codes = PayTabsPayment::getResponseCodeArrays();
        if (in_array($responseCode, $codes['successArr'])) {
            $updateData = array(
                'transaction_id' => $transactionID,
                'status' => PAYMENT_STATUS_COMPLETED,
                'response_code' => $responseCode,
                'response_message' => $responseMsg
            );
        } elseif (in_array($responseCode, $codes['pendingArr'])) {
            $updateData = array(
                'transaction_id' => $transactionID,
                'status' => PAYMENT_STATUS_PENDING,
                'response_code' => $responseCode,
                'response_message' => $responseMsg
            );
        } else {
            $updateData = array(
                'transaction_id' => $transactionID,
                'status' => PAYMENT_STATUS_FAILED,
                'response_code' => $responseCode,
                'response_message' => $responseMsg
            );
        }

        $payment_id = $paymentValidationData['reference_no'];
        $this->loadModel('InvoicePayment');
        $this->InvoicePayment->alias = 'InvoicePayment';
        $invoicePayment = $this->InvoicePayment->find('first', array('conditions' => array('InvoicePayment.id' => $payment_id)));
        foreach ($updateData as $key => $value) {
            $invoicePayment['InvoicePayment'][$key] = $value;
        }
        $this->InvoicePayment->save($invoicePayment, array('validate' => false, 'callbacks' => false));

        $client_id = $invoicePayment['InvoicePayment']['client_id'];
        $this->Client->pay_invoices_from_credit($client_id);
        $alertData = PayTabsPayment::displayPaytabsResponseCode($responseCode);
        $this->flashMessage("(Transaction: $transactionID) $responseMsg " . $alertData['msg'] . " (Code: $responseCode)", $alertData['class']);
        $this->redirect(array('action' => 'view', $client_id));
    }

	function owner_getClientInvoicesForCreditDistribution()
    {
        $client_id = $_GET['client_id'];
        $currency_code = $_GET['currency_code'];
        $invoicesOptions = CreditDistributionService::getInvoicesOptions($client_id, $currency_code);
        die(json_encode($invoicesOptions));
    }

    function owner_distribute_credit($client_id)
    {
        if (settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') || !check_permission(Invoices_Add_Payments_to_All)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('controller' => 'clients', 'action' => 'view', $client_id));
        }

        if (!empty($this->data)) {
            if (!empty($this->data['paymentInvoices'])) {
                $validation_result = CreditDistributionService::validateDistributionForm($this->data['paymentInvoices'], $this->data['balance']);
                if ($validation_result['result']) {
                    $dist_service = new CreditDistributionService();
                    $distribution_data = $dist_service->addSubPayments($this->data['paymentInvoices'], null, $this->data['currency_code']);
                    if ($distribution_data)
                        $dist_service->addDistributions(null, $distribution_data);

                    $this->flashMessage(sprintf(__('%s Added Successfully', true), __('Distributions', true)), 'Sucmessage');
                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $client_id));
                } else {
                    $this->flashMessage($validation_result['message'], 'Errormessage', 'secondaryMessage');
                }
            } else {
                $this->flashMessage(sprintf(__('%s Not Found', true), __('Distributions', true)), 'Errormessage', 'secondaryMessage');
            }
        }

        $currency_code = $_GET['currency_code'];
        if (!$currency_code) {
            $this->flashMessage(sprintf(__('%s Not Found', true), __('Currency Code', true)), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('controller' => 'clients', 'action' => 'view', $client_id));
        }
        
        $credit_list = $this->Client->getAllCredit($client_id);
        $balance = $credit_list[$currency_code];
        $this->set('balance', $balance);
        $this->set('currency_code', $currency_code);
        $this->set('client_id', $client_id);

        $invoicesOptions = CreditDistributionService::getInvoicesOptions($client_id, $currency_code);
        $this->set('invoicesOptions', $invoicesOptions);

        $client = $this->Client->findById($client_id);
        $breadcrumbs = array();
        $breadcrumbs[0]['title'] = __('Clients', true);
        $breadcrumbs[0]['link'] = '/v2/owner/entity/client/list';
        $breadcrumbs[1]['title'] = $client['Client']['business_name']." (#".$client['Client']['client_number'].")";
        $breadcrumbs[1]['link'] = Router::url(array('controller' => 'clients', 'action' => 'view', $client_id));
        $breadcrumbs[2]['title'] = __('Distribute', true);
        $breadcrumbs[2]['link'] = '#';
        $this->set('breadcrumbs', $breadcrumbs);
        $this->set('title_for_layout',  __('Distribute Credit', true));
    }
    function translate($lang='ara'){
        header("Content-type: application/json");

        $default_source_file=dirname(dirname(__FILE__)) .DS. 'locale' . DS.$lang.DS.'LC_MESSAGES'.DS.'default.po';
        $source_file=dirname(dirname(__FILE__)) .DS. 'locale' . DS.$lang.DS.'LC_MESSAGES'.DS.(defined('DefaultLang')?('default-'.DefaultLang):'default').".po";
        if (!file_exists($source_file)) {
            $source_file = $default_source_file;
            if (!file_exists($source_file)) {
                echo json_encode([]);
                die();
            }
        }
        $temp_file=tempnam('temp','translate');
        $cakeTranslations = Translations::fromPoFile($source_file)->toPhpArrayFile($temp_file);

        $arr=include $temp_file;
        $newArr=[];
        unset($arr['domain'],$arr['plural-forms'],$arr['messages'][''][''][0],$arr['messages']['']['']);
        foreach($arr['messages'][''] as $key=>$value){
            $newArr[$key]=$value[0];
        }
        unlink($temp_file);
        echo json_encode($newArr);
        die();
    }

    public function owner_loyalty_points($id = null) {
        $res = [];
        if(ifPluginActive(CLIENT_LOYALTY_PLUGIN)) {
            $creditChargeRepo = new CreditChargeRepository();
            $credit_charge_type = Settings::getValue(CLIENT_LOYALTY_PLUGIN, "client_loyalty_credit_type");
            $res['points'] = $creditChargeRepo->getClientCredit($id, $credit_charge_type, date("Y:m:d"));
            $res['factor'] = (float) Settings::getValue(CLIENT_LOYALTY_PLUGIN, "client_loyalty_conversion_factor", null, false) ?? null;
            $this->loadModel('CreditType');
            $credit_charge_type = Settings::getValue(CLIENT_LOYALTY_PLUGIN, "client_loyalty_credit_type");
            $creditType = $this->CreditType->findById($credit_charge_type);
            $res['creditType'] = $creditType;
            $res['checkSiteLimitLoyaltyPts'] = checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::LOYALTY_POINTS);
        }

        die(json_encode($res));
    }

    private function getCanViewHisOwnClientsCondition($staff_id): string
    {
        return '(Client.staff_id = ' . $staff_id . '  OR  Client.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . PostTypeUtil::CLIENT_TYPE . ' AND item_staffs.staff_id=' . $staff_id . ' ) '.$this->Client->getPosDefaultClient().' ) ';
    }

    private function getHisOwnClients():array
    {
        $staff_id = getAuthOwner('staff_id');
        $this->loadModel('Client');

        return $this->Client->find('list', ['conditions' => [$this->getCanViewHisOwnClientsCondition($staff_id)]]);
    }

    private function hasPermissionToViewHasOwnClients():bool
    {
        return check_permission(INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS) && !check_permission(Invoices_Add_New_Invoices);
    }

    private function checkCanViewHisOwnClientsCondition($modelTagName, $actionMethod, $staff_id)
    {
        switch ($modelTagName) {
            case 'invoice':
            case 'creditnote':
            case 'refund':
            case 'add_subscription':
            case 'edit_subscription':
                if ((in_array($actionMethod, ["add", "add_creditnote", "add_refund", "add_subscription"]) && $this->hasPermissionToViewHasOwnClients())
                    || (in_array($actionMethod, ["edit", "edit_creditnote", "edit_refund", "edit_subscription"]) && check_permission(Invoices_Edit_his_own_Invoices) && !check_permission(Clients_View_All_Clients) && !check_permission(Invoices_Edit_All_Invoices))) {
                    return $this->getCanViewHisOwnClientsCondition($staff_id);
                }
            case 'estimate':
                if ((in_array($actionMethod, ["add_estimate"]) &&  check_permission(ESTIMATES_ADD_NEW_TO_HIS_OWN_CLIENTS) && !check_permission(ESTIMATES_ADD_NEW_TO_ALL_CLIENTS))
                    || (in_array($actionMethod, ["edit_estimate",]) && check_permission(ESTIMATES_EDIT_DELETE_HIS_OWN_ESTIMATES) && !check_permission(ESTIMATES_EDIT_DELETE_ALL_ESTIMATES))) {
                    return $this->getCanViewHisOwnClientsCondition($staff_id);
                }
            case 'sales_order':
                if ((in_array($actionMethod, ["add_sales_order"]) &&  check_permission(SALES_ORDER_ADD_NEW_TO_HIS_OWN_CLIENTS) && !check_permission(SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS))
                    || (in_array($actionMethod, ["edit_sales_order",]) && check_permission(SALES_ORDER_EDIT_DELETE_HIS_OWN_SALES_ORDER) && !check_permission(SALES_ORDER_EDIT_DELETE_ALL_SALES_ORDER))) {
                    return $this->getCanViewHisOwnClientsCondition($staff_id);
                }
            case 'debitnote':
                if ((in_array($actionMethod, ["add_debitnote"]) && check_permission(DEBIT_NOTE_ADD_NEW_TO_HIS_OWN_CLIENTS) && !check_permission(DEBIT_NOTE_ADD_NEW_TO_ALL_CLIENTS))
                ||  (in_array($actionMethod, ["edit_debitnote"]) && check_permission(DEBIT_NOTE_EDIT_DELETE_HIS_OWN) && !check_permission(DEBIT_NOTE_EDIT_DELETE_ALL))) {
                    return $this->getCanViewHisOwnClientsCondition($staff_id);
                }
        }
        return null;
    }

    private function getPosDefaultClient():string
    {
        $posDefaultClient = settings::getValue(PosPlugin, 'pos_default_client');
        if($posDefaultClient) {
            return " OR  Client.id = $posDefaultClient";
        }
        return '';
    }

    private function getClientType($data): int
    {
        $type = 2;
        if (!empty($data['Client']['bn1']) || !empty($data['Client']['bn2'])) {
            $type = 3;
        }
        return $type;
    }
    private function redirectToIndex() {
        if (\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('clients')) {
            $this->redirect(['action' => 'index']);
        } else {
            $this->redirect('/v2/owner/entity/client/list');
        }
    }

    private function getWorkFlowTabs($client_id)
    {
        $this->loadModel('WorkOrder');
        $this->loadModel('WorkflowType');
        $workFLowTypes = $this->WorkflowType->find('all', ['fields' => ['name', 'entity_key','id']]);
        $tmp = [];
        foreach ($workFLowTypes as $type) {
            $count = $this->WorkOrder->find('count', ['conditions' => ['workflow_type_id' => $type['WorkflowType']['id'] , 'client_id' => $client_id]]);
            if ($count) {
                $tmp[] = ['entityKey' => $type['WorkflowType']['entity_key'] ,'name' => $type['WorkflowType']['name']];
            }
        }
        return $tmp;
    }

    private function getBranchCondition($table) {
        if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)) {
            if (getCakeSession(BRANCH_TRANSACTIONS_KEY) && getCakeSession(BRANCH_TRANSACTIONS_KEY) !== '-1') {
                return ' AND ' . $table . '.branch_id IN (' . getCakeSession(BRANCH_TRANSACTIONS_KEY) . ') ';
            } else if (getCakeSession(BRANCH_TRANSACTIONS_KEY) === '-1') {
                return ' AND ' . $table . '.branch_id IN (' . implode(',', array_keys(getStaffBranchesSuspended())) . ') ';
            }
        }
        return '';
    }

    private function setCustomEntityAndKey(){
        $this->set('entityCustomField', 'client_custom_field');
        $this->set('entityFieldCustomFieldKey', 'client_custom_fields');
    }

    private function setGruopPriceId($currentGruopPriceId){
        if(empty($currentGruopPriceId)) {
            $currentGruopPriceId = settings::getValue(InventoryPlugin,'default_price_list');
        }
        return $currentGruopPriceId;
    }

    private function getFirstCustomValidationMessage($message){
        $tmp = [];
        foreach($message as $key => $message){
            $tmp[$key] = $message;
            break;
        }
        return $tmp;
    }

    /**
     * get ids if user selected all pages filter in client list
     *
     * @param array $ids
     * @param $in
     * @return array
     */
    public function getIdsAllSelected(array $ids, $in): array
    {
        $staffIds = $this->data['Client']['staff'];
        $ids = $this->Client->find('list', ['fields' => ['id'], 'conditions' => ['Client.staff_id' => $staffIds]]);

        $currentStaffIds = $in;
        $itemStaffClientIds = $this->ItemStaff->find('list', ['fields' => ['item_id', 'item_id'], 'conditions' => ['ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.staff_id' => $currentStaffIds]]);
        return array_merge($ids, $itemStaffClientIds);
    }

}

