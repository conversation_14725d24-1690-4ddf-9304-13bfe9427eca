<?php
class InvoiceLayoutTagsController extends AppController {

	var $name = 'InvoiceLayoutTags';

	/**
	 * @var InvoiceLayoutTag
	 */
	var $InvoiceLayoutTag;
	var $helpers = array('Html', 'Form');

	function index() {
		$this->InvoiceLayoutTag->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('invoiceLayoutTags', $this->paginate('InvoiceLayoutTag', $conditions));
	}

	function view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('invoice layouts tag', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('invoiceLayoutTag', $this->InvoiceLayoutTag->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->InvoiceLayoutTag->create();
			if ($this->InvoiceLayoutTag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('invoice layouts tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('invoice layouts tag',true)));
			}
		}
		$invoiceLayouts = $this->InvoiceLayoutTag->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'invoice layouts tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$invoiceLayoutTag = $this->InvoiceLayoutTag->read(null, $id);
		if (!empty($this->data)) {
			if ($this->InvoiceLayoutTag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('invoice layouts tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('invoice layouts tag',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $invoiceLayoutTag;
		}
		$invoiceLayouts = $this->InvoiceLayoutTag->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
		$this->render('add');
	}

	function delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('invoice layouts tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('invoiceLayoutTag', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('invoiceLayoutTags', true);
		 } 
		$invoiceLayoutTags = $this->InvoiceLayoutTag->find('all',array('conditions'=>array('InvoiceLayoutTag.id'=>$id)));
		if (empty($invoiceLayoutTags)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->InvoiceLayoutTag->deleteAll(array('InvoiceLayoutTag.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('invoiceLayoutTags',$invoiceLayoutTags);
	}

	function owner_index() {
		$this->InvoiceLayoutTag->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('invoiceLayoutTags', $this->paginate('InvoiceLayoutTag', $conditions));
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('invoice layouts tag', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('invoiceLayoutTag', $this->InvoiceLayoutTag->read(null, $id));
	}

	function owner_add() {
		if (!empty($this->data)) {
			$this->InvoiceLayoutTag->create();
			if ($this->InvoiceLayoutTag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('invoice layouts tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('invoice layouts tag',true)));
			}
		}
		$invoiceLayouts = $this->InvoiceLayoutTag->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'invoice layouts tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$invoiceLayoutTag = $this->InvoiceLayoutTag->read(null, $id);
		if (!empty($this->data)) {
			if ($this->InvoiceLayoutTag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('invoice layouts tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('invoice layouts tag',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $invoiceLayoutTag;
		}
		$invoiceLayouts = $this->InvoiceLayoutTag->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('invoice layouts tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('invoiceLayoutTag', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('invoiceLayoutTags', true);
		 } 
		$invoiceLayoutTags = $this->InvoiceLayoutTag->find('all',array('conditions'=>array('InvoiceLayoutTag.id'=>$id)));
		if (empty($invoiceLayoutTags)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->InvoiceLayoutTag->deleteAll(array('InvoiceLayoutTag.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('invoiceLayoutTags',$invoiceLayoutTags);
	}
}
?>