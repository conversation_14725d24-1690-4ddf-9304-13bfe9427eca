<?php
use App\Services\CreditDistribution\SupplierCreditDistributionService;
use Izam\Attachment\Service\AttachmentsService;
use App\Helpers\CurrencyHelper;

App::import('Vendor', 'notification');
/**
 * @property Supplier $Supplier
 * @property PurchaseOrderPayment $PurchaseOrderPayment
 * @property SitePaymentGateway $SitePaymentGateway
 */
class PurchaseOrderPaymentsController extends AppController {

    var $name = 'PurchaseOrderPayments';

    /**
     * @var PurchaseOrderPayment
     * @var Supplier
     */
    
    var $helpers = array('html', 'form');
    var $components = array('Lmail', 'Email', 'SysEmails');

    /**
     *
     * @var LmailComponent
     */
    var $Lmail;

//----------------------
    function owner_index() {
       
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_Delete_All_Purchase_Orders) && !check_permission(Edit_Delete_his_own_created_Purchase_Orders) && !check_permission('PurchaseOrders_Add_PurchaseOrder_Payments') && !check_permission('PurchaseOrders_Add_Payments_to_All')) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->PurchaseOrderPayment->recursive = 0;


        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true);
        unset($paymentMethods['client_credit']);
        $paymentMethods['supplier_credit']=__('Supplier Credit',true);
        $this->PurchaseOrderPayment->filters = $this->PurchaseOrderPayment->getFilters(false);
        $this->PurchaseOrderPayment->filters['payment_method']['options'] = array('options' => $paymentMethods);

        $this->loadModel('Staff');
        $staffs = $this->Staff->getList();
        $this->set('staffs', $staffs);
        $this->loadModel('Supplier');
        $suppliers = $this->Supplier->find('list');
        $this->set('suppliers', $suppliers);


        if (check_permission('PurchaseOrders_View_All_PurchaseOrders') && check_permission('PurchaseOrders_View_PurchaseOrders_Details') && ifPluginActive(StaffPlugin)) {


            $this->PurchaseOrderPayment->filters['invoiced_by'] = array('more-options' => true, 'options' => array('options' => $staffs, 'type' => 'select'));
            
        }

        $conditions = $this->_filter_params(false, $this->PurchaseOrderPayment->filters);

        if (check_permission('PurchaseOrders_View_All_PurchaseOrders') && check_permission('PurchaseOrders_View_PurchaseOrders_Details') && ifPluginActive(StaffPlugin)) {
            if (isset($conditions['PurchaseOrderPayment.invoiced_by']) and !empty($conditions['PurchaseOrderPayment.invoiced_by']) || $conditions['PurchaseOrderPayment.invoiced_by'] == '0') {
                $conditions['PurchaseOrder.staff_id'] = $conditions['PurchaseOrderPayment.invoiced_by'];
                unset($conditions['PurchaseOrderPayment.invoiced_by']);
            }
        }



        if (!empty($conditions['PurchaseOrderPayment.name LIKE'])) {
            $conditions[] = "CONCAT (PurchaseOrderPayment.first_name,PurchaseOrderPayment.last_name) LIKE '{$conditions['PurchaseOrderPayment.name LIKE']}'";
        }

        if (!empty($conditions['PurchaseOrderPayment.no'])) {
            $conditions[] = " PurchaseOrder.no = '{$conditions['PurchaseOrderPayment.no']}'";
        }
        unset($conditions['PurchaseOrderPayment.supplier_id']);
        if(!empty($this->params['url']['supplier_id'])){
            $conditions['OR'] = array('PurchaseOrder.supplier_id' => $this->params['url']['supplier_id'], 'PurchaseOrderPayment.supplier_id' => $this->params['url']['supplier_id']);
            $conditions[] = "PurchaseOrderPayment.payment_method <> 'starting_balance'";
        }

        unset($conditions['PurchaseOrderPayment.name LIKE']);
        unset($conditions['PurchaseOrderPayment.no']);

        if ($site['staff_id'] != 0) {

            if (!check_permission('PurchaseOrders_View_All_PurchaseOrders') && (check_permission('PurchaseOrders_View_PurchaseOrders_Details'))) {
                $conditions['PurchaseOrder.staff_id'] = $site['staff_id'];
            }
        }

        $this->paginate['PurchaseOrderPayment']['order'] = 'PurchaseOrderPayment.id DESC';
      
        if(isset($_GET['box']) && isset($_GET['from_supplier'])) {
            
            $this->paginate['PurchaseOrderPayment']['applyBranchFind'] = false;
        }
        if(!IS_REST){
            $conditions['PurchaseOrderPayment.payment_method <>'] = 'starting_balance';
            if (empty($this->params['url']['payment_method'])) {
                $conditions['PurchaseOrderPayment.payment_method <>'] = 'supplier_credit';
            }
        }

        debug($conditions);
        if (!empty($conditions)) {
            $this->set('search_filter', true);
        } else {
            $this->set('search_filter', false);
        }

        $this->PurchaseOrderPayment->bindAttachmentRelation('purchase_order_payment');
        $this->set('PurchaseOrderPayments', $this->paginate('PurchaseOrderPayment', $conditions));

        $this->set('statuses', $this->PurchaseOrderPayment->getPaymentStatus());
        $this->set('PurchaseOrderPaymentStatus', $this->PurchaseOrderPayment->getPaymentStatus());




        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, false, true);
        $paymentMethods['starting_balance'] = __("Opening Balance", true);
        $this->set('payment_methods', $paymentMethods);

        $this->set('title_for_layout',  __('Payments', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('PurchaseOrders & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'purchase_orders';
    }

    function _filter_params($params = false, $filters = false, $passedModelName = false) {
        $conditions = array();
        if (!empty($this->params['url']['client_id'])) {
            $conditions['PurchaseOrder.client_id'] = $this->params['url']['client_id'];
        }
        return array_merge($conditions, parent::_filter_params($params, $filters, $passedModelName));
    }

    function owner_print($id = null) {
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('payment', true)), true));
            $this->redirect(array('action' => 'index'));
        }

        $this->loadModel('Client');
        $conditions = array();
        $conditions['PurchaseOrderPayment.id'] = $id;
        $PurchaseOrderPayment = $this->PurchaseOrderPayment->find('first', array('conditions' => $conditions));
        $this->set('PurchaseOrderPayment', $PurchaseOrderPayment);

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(0, true, false);
        $this->set('paymentMethods', $paymentMethods);

        $this->add_actionline($this->params['url']['ext'] == 'pdf' ? ACTION_DOWNLOAD_PO_PAYMENT : ACTION_PRINT_PO_PAYMENT, array('primary_id' => $PurchaseOrderPayment['PurchaseOrder']['id'], 'param3' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'param2' => $PurchaseOrderPayment['PurchaseOrder']['no']));
    }

//---------------------
    function owner_view($id = null) {
        $this->PurchaseOrderPayment->bindAttachmentRelation('purchase_order_payment');
	    $this->loadModel('PurchaseOrder');
	    $this->loadModel('Supplier');
	    App::import('Vendor', 'notification_2');
        if (!empty($id))
            NotificationV2::view_notificationbyref($id, NotificationV2::NOTI_CLIENT_PENDING_PAYMENT);
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('payment', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $conditions = array();
        $conditions['PurchaseOrderPayment.id'] = $id;
        $PurchaseOrderPayment = $this->PurchaseOrderPayment->find('first', array('conditions' => $conditions));
        if (!$PurchaseOrderPayment['PurchaseOrderPayment']['id']) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('payment', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $PurchaseOrderData = $this->PurchaseOrder->getPurchaseOrder($PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']);
        if($PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id'] && empty($PurchaseOrderPayment['Supplier'])){
            $PurchaseOrderPayment['Supplier'] = $this->Supplier->findById($PurchaseOrderData['PurchaseOrder']['supplier_id'])['Supplier'];
        }
        $this->set('PurchaseOrderPayment', $PurchaseOrderPayment);
        $this->set_journal($id);


        if ($PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id'] && $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method'] == 'supplier_credit'){
            $dist_result = SupplierCreditDistributionService::getSupplierPayemntID($PurchaseOrderPayment['PurchaseOrderPayment']['id'], $PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']);
             if ($dist_result['result']) {
                $this->set('supplier_payemnt_id', $dist_result['supplier_payment_id']);
            }
        }

        $this->loadModel ( 'Treasury' ) ;
        $this->loadModel ( 'ItemPermission' ) ;
        $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT)  ) ;
        
//        $this->set('treasuries', $treasury_list);
        
        $this->set('statuses', $this->PurchaseOrderPayment->getPaymentStatus());
        $this->set('title_for_layout',  __('View Payment', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('PurchaseOrders & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(0, true, false);
        $paymentMethods['supplier_credit']=__('Supplier Credit',true);
        $this->set('paymentMethods', $paymentMethods);

        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('purchase_order_payment');

        $this->set('has_templates', false);
        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
        }
        $this->set('printableTemplates', $printableTemplates);

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('purchase_order_payment');
        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'purchase_orders';
    }

//---------------------
    function owner_add($id = null) {

        $find = $this->PurchaseOrderPayment->PurchaseOrder->find('first', ['conditions' => ['PurchaseOrder.id' => $id]]);
        if (!$find) {
            $this->flashMessage(sprintf(__('%s not found', true), __('Purchase Invoice', true)));
            $this->redirect(['action' => 'index']);
        }
        $owner = getAuthOwner();

        if (!check_permission([Add_New_Purchase_Orders, Edit_Delete_All_Purchase_Orders]) || (!check_permission(Edit_Delete_his_own_created_Purchase_Orders) && $find['PurchaseOrder']['staff_id'] != $owner['staff_id'])) {
            $this->flashMessage(__('you are not allowed to edit this payment', true));
            $this->redirect($this->referer(['action' => 'view', $id]));
        }

        if (!empty($this->data)) {
            $this->PurchaseOrderPayment->create();
            if ($this->PurchaseOrderPayment->save($this->data)) {
                $PurchaseOrderPayment = $this->PurchaseOrderPayment->read(null, $this->PurchaseOrderPayment->id);
                $this->add_actionline(ACTION_ADD_PO_PAYMENT, array('primary_id' => $PurchaseOrderPayment['PurchaseOrder']['id'], 'secondary_id' => $PurchaseOrderPayment['PurchaseOrder']['client_id'], 'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'], 'param2' => $PurchaseOrderPayment['PurchaseOrder']['payment_status'], 'param3' => $PurchaseOrderPayment['PurchaseOrder']['summary_paid'], 'param4' => $PurchaseOrderPayment['PurchaseOrder']['no'], 'param5' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'param6' => $PurchaseOrderPayment['PurchaseOrder']['summary_total'], 'param7' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'], 'param8' => $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method'], 'param9' => $PurchaseOrderPayment['PurchaseOrderPayment']['transaction_id']));
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Payment', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Payment', true)));
            }
        }
        $purchase_orders = $this->PurchaseOrderPayment->PurchaseOrder->find('list');
        $this->set(compact('purchase_orders'));

        $this->set('staffs', $this->PurchaseOrderPayment->Staff->getList());
        $this->set('title_for_layout',  __('Add Payment', true));
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Invoices', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));
        $this->set('paymentMethods', $paymentMethods);
        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'purchase_orders';
    }
    function owner_add_open_balance($supplier_id){
        $error=false;
        $this->pageTitle=__('Add Opening Balance',true);
        $this->loadModel('Supplier');
        $payment=$this->Supplier->find(array('Supplier.id'=>$supplier_id));
        $pop = $this->PurchaseOrderPayment->find('first', ['applyBranchFind' => false,'recursive'=>'-1','conditions' => ['PurchaseOrderPayment.payment_method'=>'starting_balance','PurchaseOrderPayment.supplier_id' => $supplier_id]]);
        if($pop) {
            return $this->redirect(Router::url(['action' => 'edit', $pop['PurchaseOrderPayment']['id'], 'starting_balance']));
        }
        if(!empty($this->data)){

            if(empty($this->data['PurchaseOrderPayment']['date'])) {
                $this->PurchaseOrderPayment->validationErrors['date_date'] = __('Required',true);
                $this->PurchaseOrderPayment->validationErrors['date_time'] = __('Required',true);
                $error=true;
            }
            if(empty($this->data['PurchaseOrderPayment']['amount'])) {
                $this->PurchaseOrderPayment->validationErrors['amount'] = __('Required',true);
                $error=true;
            }
            if(empty($this->data['PurchaseOrderPayment']['currency_code'])) {
                $this->PurchaseOrderPayment->validationErrors['currency_code'] = __('Required',true);
                $error=true;
            }
            $this->validateOpenDayWithValidationError($this->data, 'PurchaseOrderPayment' ,'date');
            if($error==false && empty($this->PurchaseOrderPayment->validationErrors)) {
                $this->data['PurchaseOrderPayment']['amount']=(float)$this->data['PurchaseOrderPayment']['amount']*-1;
                $payment = $this->data['PurchaseOrderPayment'];
                // function addOpenBalance($client_id,$balance,$staff_id,$currency_code,$date = null)
                $result = $this->Supplier->addOpenBalance($supplier_id,$payment['amount'],getAuthOwner('staff_id'),$payment['currency_code'],$payment['date']);
                $PurchaseOrderPayment = $this->PurchaseOrderPayment->read(null,$result['date']['PurchaseOrderPayment']['id']);
                $this->add_actionline(ACTION_ADD_SUPPLIER_OPENING_BALANCE,array('primary_id' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'],'secondary_id' => $PurchaseOrderPayment['PurchaseOrderPayment']['client_id'],'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'],'param2' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'],'param2' => $PurchaseOrderPayment['Supplier']['business_name'],'param4' => $PurchaseOrderPayment['Supplier']['supplier_number']));

                $url = array('controller' => 'suppliers','action' => 'statement',$supplier_id);
                $this->redirect($url);
            }
        }
        $this->loadModel('Currency');
        $this->set('currencies',$this->Currency->getCurrencyList());
        $this->set('currency_code', isset($payment['currency_code']) ? $payment['currency_code'] : getAuthOwner('currency_code'));
        $this->set('payment',$payment);
        $this->render('edit_starting_balance');
    }
//---------------------
    function owner_edit($id = null) {
        $this->PurchaseOrderPayment->bindAttachmentRelation('purchase_order_payment');
        $this->PurchaseOrderPayment->recursive = 1;
        $payment = $this->PurchaseOrderPayment->find(array('PurchaseOrderPayment.id' => $id));


        if (!$payment) {
            $this->flashMessage(__('Payment not found', true));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        $owner = getAuthOwner();
        if (!(check_permission([Edit_Delete_All_Purchase_Orders]) || (check_permission(Edit_Delete_his_own_created_Purchase_Orders) && $payment['PurchaseOrderPayment']['staff_id'] == $owner['staff_id']))) {
            $this->flashMessage(__('you are not allowed to edit this payment', true));
            $this->redirect(array('action' => 'index'));
        }

        $this->validate_open_day($payment['PurchaseOrderPayment']['date']);
        App::import('Vendor', 'AutoNumber');
        if (!empty($this->data)) {
            if ($payment['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund) {
                $this->data['PurchaseOrderPayment']['amount'] = abs($this->data['PurchaseOrderPayment']['amount']) * -1;
            }
            /** Validate Purchase Invoice Payment Distributions **/
            if (!settings::getValue(InventoryPlugin, 'automatic_pay_po') && check_permission(Edit_Delete_All_Purchase_Orders) && !empty($this->data['paymentPurchaseInvoices'])) {
                $validation_result = SupplierCreditDistributionService::validateDistributionForm($this->data['paymentPurchaseInvoices'], $this->data['PurchaseOrderPayment']['amount']);
                if (!$validation_result['result']) {
                    $this->flashMessage($validation_result['message'], 'Errormessage', 'secondaryMessage');
                }
            } else {
                $validation_result['result'] = true;
            }

            if ($validation_result['result']) {
                $this->data['PurchaseOrderPayment']['id'] = $id;
                if (empty($this->data['PurchaseOrderPayment']['staff_id']))
                    $this->data['PurchaseOrderPayment']['staff_id'] = $owner['staff_id'];

                if($this->data['PurchaseOrderPayment']['payment_method']=="starting_balance"){
                    if($this->data['PurchaseOrderPayment']['amount']==0){
                        $this->loadModel('Supplier') ;
                        $this->flashMessage(sprintf(__('%s  has been saved', true), __('Payment', true)), 'Sucmessage');
                        $result=$this->Supplier->deleteOpenBalance($id);
                        $url = array('controller' => 'suppliers', 'action' => 'statement', $result['data']['supplier_id']);
                        $this->redirect($url);
                    }
                    $this->data['PurchaseOrderPayment']['amount']= (float) $this->data['PurchaseOrderPayment']['amount']*-1;
                }

                $attachments = explode(',',$this->data['PurchaseOrderPayment']['attachment']);
                unset($this->data['PurchaseOrderPayment']['attachment']);

                if($payment['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_INVOICE){
                    \AutoNumber::set_validate(\AutoNumber::TYPE_PURCHASE_INVOICE_PAYMENT);
                }elseif($payment['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund){
                    \AutoNumber::set_validate(\AutoNumber::TYPE_PURCHASE_REFUND_PAYMENT);
                }
                if ($this->PurchaseOrderPayment->editPayment($this->data)) {

                    if(!empty($attachments))
                    {
                        izam_resolve(AttachmentsService::class)->save('purchase_order_payment', $id, $attachments); 
                    }
                    $purchaseOrderPayment = $this->PurchaseOrderPayment->findById($id);
                    /** Check Supplier Credit Distributions **/
                    if (!settings::getValue(InvoicesPlugin, 'automatic_pay_po') && check_permission(Edit_Delete_All_Purchase_Orders) && $payment['PurchaseOrderPayment']['supplier_id']) {
                        $purchaseOrderPaymentId = $purchaseOrderPayment['PurchaseOrderPayment']['id'];
                        $dist_service = new SupplierCreditDistributionService();
                        if (!empty($this->data['paymentPurchaseInvoices']) && $purchaseOrderPayment['PurchaseOrderPayment']['status'] == PAYMENT_STATUS_COMPLETED && $payment['PurchaseOrderPayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                            /** Update Invoice Payments & Distributions **/
                            $distribution_data = $dist_service->updateSubPayments($purchaseOrderPaymentId, $this->data['paymentPurchaseInvoices']);
                            $dist_service->updateDistributions($purchaseOrderPaymentId, $distribution_data);
                        } elseif (!empty($this->data['paymentPurchaseInvoices']) && $purchaseOrderPayment['PurchaseOrderPayment']['status'] == PAYMENT_STATUS_COMPLETED && $payment['PurchaseOrderPayment']['status'] != PAYMENT_STATUS_COMPLETED) {
                            /** Insert New Invoice Payments & Distributions **/
                            $distribution_data = $dist_service->addSubPayments($this->data['paymentPurchaseInvoices'], $purchaseOrderPayment);
                            if ($distribution_data)
                                $dist_service->addDistributions($purchaseOrderPayment['PurchaseOrderPayment']['id'], $distribution_data);
                        } else {
                            /** Delete Invoice Payments & Distributions **/
                            $dist_service->deleteSubPayments($purchaseOrderPaymentId);
                            $dist_service->deleteDistributions($purchaseOrderPaymentId);
                        }
                    }
                    $PurchaseOrderPayment = $this->PurchaseOrderPayment->read(null, $id);
                    if($PurchaseOrderPayment['PurchaseOrderPayment']['payment_method']=="starting_balance"){
                        $this->add_actionline(ACTION_EDIT_SUPPLIER_OPENING_BALANCE, array('primary_id' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'secondary_id' => $PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'], 'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'], 'param2' => $PurchaseOrderPayment['Supplier']['business_name'],'param4' => $PurchaseOrderPayment['Supplier']['supplier_number']));
                    }else if (!empty($PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id']) && empty($PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id'])) {
                        $this->add_actionline(ACTION_EDIT_SUPPLIER_CREDIT, array('primary_id' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'secondary_id' => $PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'], 'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'], 'param6' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'], 'param2' => $PurchaseOrderPayment['Supplier']['business_name'], 'param4' => $PurchaseOrderPayment['Supplier']['client_number'], 'param5' => $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method']));
                    }else {
                        $this->add_actionline(ACTION_UPDATE_PO_PAYMENT,array('primary_id' => $PurchaseOrderPayment['PurchaseOrder']['id'],'secondary_id' => $PurchaseOrderPayment['PurchaseOrder']['supplier_id'],'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'],'param2' => $PurchaseOrderPayment['PurchaseOrder']['payment_status'],'param3' => $PurchaseOrderPayment['PurchaseOrder']['summary_paid'],'param4' => $PurchaseOrderPayment['PurchaseOrder']['no'],'param5' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'],'param6' => $PurchaseOrderPayment['PurchaseOrder']['summary_total'],'param7' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'],'param8' => $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method'],'param9' => $PurchaseOrderPayment['PurchaseOrderPayment']['transaction_id']));
                    }

                    if ($PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id']) {
                        $supplier_id = $PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'];
                        $currency_code = $PurchaseOrderPayment['PurchaseOrderPayment']['currency_code'];
                    } else {
                        $supplier_id = $PurchaseOrderPayment['PurchaseOrder']['supplier_id'];
                        $currency_code = $PurchaseOrderPayment['PurchaseOrderPayment']['currency_code'];
                    }
                    $this->PurchaseOrderPayment->Supplier->adjust_and_pay($supplier_id);
                    $this->PurchaseOrderPayment->Supplier->pay_invoices_from_credit($supplier_id);
                    $updateParent = false;
                    if ($payment['PurchaseOrderPayment']['status'] != PAYMENT_STATUS_COMPLETED && $this->data['PurchaseOrderPayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                        $payment = $this->PurchaseOrderPayment->findById($id);
                        $updateParent = true;
                    }

                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Payment', true)), 'Sucmessage');
                    if(empty($payment['PurchaseOrderPayment']['purchase_order_id'])){
                        $this->flashMessage(__('Credit Payment Updated Successfully', true), 'Sucmessage');
                    }
                    if($payment['PurchaseOrderPayment']['payment_method']=="starting_balance"){
                        $url = array('controller' => 'suppliers', 'action' => 'statement', $payment['PurchaseOrderPayment']['supplier_id']);
                        $this->redirect($url);
                    }
                    $url = array('action' => 'index');
                    if (!empty($this->params['url']['from_matching'])) {
                        $url = array('action' => 'edit', $id, '?' => 'box=1');
                        $this->redirect($url);
                    }
                    if (!empty($this->params['url']['box'])) {
                        $url = array('action' => 'edit', $id, '?' => 'box=1&update_parent=1');
                        $this->redirect($url);
                    }
                    if (!empty($this->params['url']['back']) and $this->params['url']['back'] == "PurchaseOrder") {
                        if($payment['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE) {
                            $url = array('controller' => 'purchase_invoices', 'action' => 'view_credit_note', $payment['PurchaseOrder']['id'], '#' => 'PaymentsBlock');
                        }else{
                            $url = array('controller' => 'purchase_invoices', 'action' => 'view', $payment['PurchaseOrder']['id'], '#' => 'PaymentsBlock');
                        }
                    }
                    if(empty($payment['PurchaseOrderPayment']['purchase_order_id']) && !empty($payment['PurchaseOrderPayment']['supplier_id'])){
                        $url = array('controller' => 'suppliers', 'action' => 'view', $payment['PurchaseOrderPayment']['supplier_id']);
                    }
                    $this->redirect($url);
                } else {
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Payment', true)));
                }
            }
        } else {
            $this->data = $payment;
            if($this->data['PurchaseOrderPayment']['payment_method']=="starting_balance"){
                $this->data['PurchaseOrderPayment']['amount']=$this->data['PurchaseOrderPayment']['amount']*-1;
            }
            $this->data['PurchaseOrderPayment']['date'] = format_date($this->data['PurchaseOrderPayment']['date']);
            $this->data['PurchaseOrderPayment']['amount'] = round($this->data['PurchaseOrderPayment']['amount'], CurrencyHelper::getFraction($payment['PurchaseOrderPayment']['currency_code']));
        }

        if (!settings::getValue(InventoryPlugin, 'automatic_pay_po')) {
            if (empty($this->data['paymentPurchaseInvoices'])) {
                $this->loadModel('SupplierCreditDistribution');
                $distributions = $this->SupplierCreditDistribution->find('all', array('conditions' => array('SupplierCreditDistribution.supplier_credit_purchase_order_payment_id' => $id)));
                foreach ($distributions as $old_distribution) {
                    $data['purchase_order_id'] = $old_distribution['SupplierCreditDistribution']['purchase_order_id'];
                    $data['distribution_amount'] = $old_distribution['SupplierCreditDistribution']['distribution_amount'];
                    $this->data['paymentPurchaseInvoices'][] = $data;
                }
            }

            $purchaseInvoicesOptions = SupplierCreditDistributionService::getPurchaseInvoicesOptions($payment['PurchaseOrderPayment']['supplier_id'], $payment['PurchaseOrderPayment']['currency_code'], $id);
            $this->set('purchaseInvoicesOptions', $purchaseInvoicesOptions);
            if($purchaseInvoicesOptions)
                $this->set('hasPurchaseInvoices', true);
        }

        $this->set('statuses', PurchaseOrderPayment::getPaymentStatus());
        $this->loadModel('ItemPermission');
        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'Treasury');
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT) ) ;
            
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));
        $this->loadModel('PurchaseOrder');
        if(!settings::getValue(InventoryPlugin, 'automatic_pay_po')){
            $supplier_credit = $this->PurchaseOrder->Supplier->supplier_credit($payment['PurchaseOrder']['supplier_id'], $payment['PurchaseOrder']['currency_code'])+$payment['PurchaseOrderPayment']['amount'];

            if ($supplier_credit <= 0) {
                unset($paymentMethods['supplier_credit']);
            } else {


                $this->set('supplier_credit', $supplier_credit);
                unset($paymentMethods['supplier_credit']);
                $paymentMethods['supplier_credit'] = sprintf(__('Supplier Credit (%s)', true), $supplier_credit);
            }
        }
 
        $this->set('currency_code', isset($payment['PurchaseOrderPayment']['currency_code']) ? $payment['PurchaseOrderPayment']['currency_code'] : getAuthOwner('currency_code'));
        $this->set('paymentMethods', $paymentMethods);
        $this->set('file_settings', $this->PurchaseOrderPayment->getFileSettings());
        $this->set('staffs', $this->PurchaseOrderPayment->Staff->getList(true, [], true));
        $this->set(array('payment' => $payment, 'PurchaseOrder' => $payment));

        $this->set('title_for_layout',  __('Edit Payment', true));
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('PurchaseOrders & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'purchase_orders';
        if($payment['PurchaseOrderPayment']['payment_method']=="starting_balance"){
            $this->loadModel('Currency');
            $this->set('currencies',$this->Currency->getCurrencyList());
            $this->render('edit_starting_balance');
        }
    }

    function owner_approve($id = false) {
        if ($this->RequestHandler->isAjax()) {
            $this->_owner_approve_ajax($id);
        } else {
            $payment = $this->PurchaseOrderPayment->find(array('PurchaseOrderPayment.id' => $id));
            if (!$payment) {
                $this->flashMessage(__('Payment not found', true));
                $this->redirect($this->referer(array('action' => 'index')));
            }

            if ($payment['PurchaseOrderPayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                $this->flashMessage(__('Payment is already completed', true), 'Notemessage');
                $this->redirect($this->referer(array('action' => 'index')));
            }
            $this->validate_open_day($payment['PurchaseOrderPayment']['date']);
            $payment['PurchaseOrderPayment']['status'] = PAYMENT_STATUS_COMPLETED;
            if ($this->PurchaseOrderPayment->save($payment, array('fieldList' => array('id', 'status'), 'validate' => false, 'callbacks' => false))) {
                $this->PurchaseOrderPayment->update_journals($payment);
                $result = $this->PurchaseOrderPayment->PurchaseOrder->updatePurchaseOrderPayments($payment['PurchaseOrder']['id']);

                $PurchaseOrderPayment = $this->PurchaseOrderPayment->read(null, $id);
                

                $this->add_actionline(ACTION_UPDATE_PURCHASEORDER_PAYMENT, array('primary_id' => $PurchaseOrderPayment['PurchaseOrder']['id'], 'secondary_id' => $PurchaseOrderPayment['PurchaseOrder']['supplier_id'], 'param1' => $PurchaseOrderPayment['Payment']['amount'], 'param2' => $PurchaseOrderPayment['PurchaseOrder']['payment_status'], 'param3' => $PurchaseOrderPayment['PurchaseOrder']['summary_paid'], 'param4' => $PurchaseOrderPayment['PurchaseOrder']['no'], 'param5' => $PurchaseOrderPayment['Payment']['id'], 'param6' => $PurchaseOrderPayment['PurchaseOrder']['summary_total'], 'param7' => $PurchaseOrderPayment['Payment']['status'], 'param8' => $PurchaseOrderPayment['Payment']['payment_method'], 'param9' => $PurchaseOrderPayment['Payment']['transaction_id']));

                $owner = getAuthOwner();
                
                

                $this->flashMessage(__('Payment has been approved', true), 'Sucmessage');
                $this->redirect($this->referer(array('action' => 'index')));
            }
        }
    }

    function _owner_approve_ajax($id = null) {

        $payment = $this->PurchaseOrderPayment->find(array('PurchaseOrderPayment.id' => $id));
        $return = array('status' => false);
        if (!$payment) {
            $return['message'] = __('Payment not found', true);
            $return['messageClass'] = 'Errormessage';
            die(json_encode($return));
        }

        if ($payment['PurchaseOrderPayment']['status'] == PAYMENT_STATUS_COMPLETED) {
            $return['message'] = __('Payment is already completed', true);
            $return['messageClass'] = 'Notemessage';
            die(json_encode($return));
        }
        $isOpened = $this->validateOpenDayWithValidationError($payment, 'PurchaseOrderPayment', 'date');
        if($isOpened !== true)
        {
            $return['message'] = $isOpened;
            $return['messageClass'] = 'Errormessage';
            die(json_encode($return));
        }
        $payment['PurchaseOrderPayment']['status'] = PAYMENT_STATUS_COMPLETED;
        if ($this->PurchaseOrderPayment->save($payment, array('fieldList' => array('id', 'status'), 'validate' => false, 'callbacks' => false))) {
            $this->PurchaseOrderPayment->update_journals($payment);
            $this->PurchaseOrderPayment->PurchaseOrder->updatePurchaseOrderPayments($payment['PurchaseOrder']['id']);
            $PurchaseOrderPayment = $this->PurchaseOrderPayment->read(null, $id);

           
            

            $this->add_actionline(ACTION_UPDATE_PURCHASEORDER_PAYMENT, array('primary_id' => $PurchaseOrderPayment['PurchaseOrder']['id'], 'secondary_id' => $PurchaseOrderPayment['PurchaseOrder']['client_id'], 'param1' => $PurchaseOrderPayment['Payment']['amount'], 'param2' => $PurchaseOrderPayment['PurchaseOrder']['payment_status'], 'param3' => $PurchaseOrderPayment['PurchaseOrder']['summary_paid'], 'param4' => $PurchaseOrderPayment['PurchaseOrder']['no'], 'param5' => $PurchaseOrderPayment['Payment']['id'], 'param6' => $PurchaseOrderPayment['Payment']['summary_total'], 'param7' => $PurchaseOrderPayment['Payment']['status'], 'param8' => $PurchaseOrderPayment['Payment']['payment_method'], 'param9' => $PurchaseOrderPayment['Payment']['transaction_id']));
           
            

            $return['status'] = true;
            $return['message'] = __('Payment has been approved', true);
            $return['messageClass'] = 'Sucmessage';
            die(json_encode($return));
        } else {
            $return['message'] = __('Could not approve payment', true);
            $return['messageClass'] = 'Errormessage';
            die(json_encode($return));
        }
    }

//---------------------
    function owner_delete($id = null, $purchase_order_id = false, $applyBranchFind = true) {
        $this->loadModel('Supplier');
        if ((empty($id) && !empty($_POST['ids'])) || !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('payment', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('payment', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {//php8 fix
            $module_name = __('payments', true);
            $verb = __('have', true);
        }
        $PurchaseOrderPayments = $this->PurchaseOrderPayment->find('all', ['applyBranchFind' => $applyBranchFind, 'conditions' => ['PurchaseOrderPayment.id' => $id]]);
        if (empty($PurchaseOrderPayments)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect(array('action' => 'index'), true);
        }
        foreach ($PurchaseOrderPayments as $PurchaseOrderPayment) {
            $this->validate_open_day($PurchaseOrderPayment['PurchaseOrderPayment']['date']);
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {



            if ($_POST['submit_btn'] == 'yes' && $this->PurchaseOrderPayment->deleteAll(array('PurchaseOrderPayment.id' => $id))) {
                foreach ($PurchaseOrderPayments as $PurchaseOrderPayment) {
                    $this->after_delete_payment($PurchaseOrderPayment,$PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id']?ACTION_DELETE_SUPPLIER_PAYMENT:ACTION_DELETE_PO_PAYMENT);
                    $this->PurchaseOrderPayment->PurchaseOrder->updatePurchaseOrderPayments($PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']);
                    $this->PurchaseOrderPayment->delete_auto_journals($PurchaseOrderPayment['PurchaseOrderPayment']['id']);
                        
                    $invoice = $this->PurchaseOrderPayment->PurchaseOrder->read(null, $PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']);
                    $PurchaseOrderPayment['PurchaseOrder'] = $invoice['PurchaseOrder'];

                    $this->Supplier->adjust_balance($invoice['PurchaseOrder']['supplier_id'], $invoice['PurchaseOrder']['currency_code']);
                    $this->Supplier->pay_pos_from_credit($invoice['PurchaseOrder']['supplier_id']);                    
                    
                    
                  //  $this->add_actionline(ACTION_DELETE_PO_PAYMENT, array('primary_id' => $PurchaseOrderPayment['PurchaseOrder']['id'], 'secondary_id' => $PurchaseOrderPayment['PurchaseOrder']['supplier_id'], 'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'], 'param2' => $PurchaseOrderPayment['PurchaseOrder']['payment_status'], 'param3' => $PurchaseOrderPayment['PurchaseOrder']['summary_paid'], 'param4' => $PurchaseOrderPayment['PurchaseOrder']['no'], 'param5' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'param6' => $PurchaseOrderPayment['PurchaseOrder']['summary_total'], 'param7' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'], 'param8' => $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method'], 'param9' => $PurchaseOrderPayment['PurchaseOrder']['transaction_id']));
                    
                }
                //ACTION_DELETE_INVOICE_PAYMENT (primary_id => purchase_order_id, sec => client_id, p1=>total, p2=> invoice_status,p3=>invoice_summary_paid, p4=>is_internal (true if it is addd by staff/admin, if by client it will be false ), p5=>payment_id, p6=>payment_amount,                                                                                             p7=>payment_status, p8=>payment_method )), p4=> total_paid, p5=>payment_amount, p6=>payment_status )
                //NotificationV::delete_notification(NOTI_CLIENT_PENDING_PAYMENT, $id);


                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                
                // Remove files . 
                izam_resolve(AttachmentsService::class)->removeAttachmentsByEntityDetails('purchase_order_payment',$id);

                // if (!$purchase_order_id)
                //     $this->redirect(array('action' => 'index'));
                if(!empty($PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id'])){
                    if($PurchaseOrderPayment['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund){
                        $this->redirect(array('controller' => 'purchase_invoices', 'action' => 'view_refund', $PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']));
                    }else if($PurchaseOrderPayment['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE){
                        $this->redirect(array('controller' => 'purchase_invoices', 'action' => 'view_credit_note', $PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']));
                    }else {
                        $this->redirect(array('controller' => 'purchase_invoices', 'action' => 'view', $PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']));
                    }
                }
                if(!empty($PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'])){
                    $this->redirect(array('controller'=>'suppliers', 'action' => 'view', $PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id']));    
                }
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('purchase_order_id', $purchase_order_id);

        $this->set('title_for_layout',  __('Delete Payment', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('PurchaseOrders & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->set('PurchaseOrderPayments', $PurchaseOrderPayments);
        $this->set('module_name', $module_name);

        $this->titleAlias = 'purchase_orders';
    }


    function after_delete_payment($PurchaseOrderPayment, $action)
    {
        $this->loadModel('Supplier');
        $this->loadModel('PurchaseOrder');
        if (!$PurchaseOrderPayment['PurchaseOrder']['supplier_id']) {
            $this->PurchaseOrderPayment->PurchaseOrder->updatePurchaseOrderPayments($PurchaseOrderPayment['PurchaseOrderPayment']['purchase_order_id']);

            if(isset($PurchaseOrderPayment['PurchaseOrder']) && $PurchaseOrderPayment['PurchaseOrder']['type']== PurchaseOrder::Purchase_Refund ){
                $this->PurchaseOrder->updatePurchaseOrderPayments($PurchaseOrderPayment['PurchaseOrderPayment']['subscription_id']);
                $this->Supplier->adjust_balance($PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'], $PurchaseOrderPayment['PurchaseOrderPayment']['currency_code']);
                $this->Supplier->pay_invoices_from_credit($PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id']);
            }

            // App::import('Vendor', 'notification_2');
            // NotificationV2::delete_notificationbyref($invoicePayment['Invoice']['id'], NotificationV2::NOTI_CLIENT_PENDING_PAYMENT);
        }

        if ($action == ACTION_DELETE_PO_PAYMENT) {
            $primary_id = $PurchaseOrderPayment['PurchaseOrder']['id'];
            $secondary_id = $PurchaseOrderPayment['PurchaseOrder']['supplier_id'];
            $dist_service = new SupplierCreditDistributionService();
            $dist_service->deletePurchaseOrderPaymentDistributionRecord($PurchaseOrderPayment['PurchaseOrderPayment']['id']);
        } else if ($action == ACTION_DELETE_SUPPLIER_PAYMENT) {
            $primary_id = $PurchaseOrderPayment['PurchaseOrderPayment']['id'];
            $secondary_id = $PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'];
            $dist_service = new SupplierCreditDistributionService();
            $dist_service->deleteSubPayments($PurchaseOrderPayment['PurchaseOrderPayment']['id']);
            $dist_service->deleteDistributions($PurchaseOrderPayment['PurchaseOrderPayment']['id']);
        } else {
            $primary_id = $PurchaseOrderPayment['PurchaseOrderPayment']['id'];
            $secondary_id = $PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'];
        }
         
        $this->add_actionline($action, array('primary_id' => $primary_id, 'secondary_id' => $secondary_id, 'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'], 'param2' => $PurchaseOrderPayment['PurchaseOrder']['payment_status'], 'param3' => $PurchaseOrderPayment['PurchaseOrder']['summary_paid'], 'param4' => $PurchaseOrderPayment['PurchaseOrder']['no'], 'param5' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'param6' => $PurchaseOrderPayment['PurchaseOrder']['summary_total'], 'param7' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'], 'param8' => $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method'], 'param9' => $PurchaseOrderPayment['PurchaseOrderPayment']['transaction_id']));
        $this->PurchaseOrderPayment->delete_auto_journals($PurchaseOrderPayment['PurchaseOrderPayment']['id']);
    }
    
}
