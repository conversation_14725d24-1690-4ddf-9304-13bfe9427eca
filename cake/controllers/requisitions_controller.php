<?php

use App\Domain\Correctors\RequsitionCorrectors\CreateAutoRequisitionsCorrector;
use App\Helpers\CurrencyHelper;
use App\Services\CostCenter\CostCenterUpdaterService;
use App\Services\CostCenter\RequisitionCostCenterHandler;
use App\Services\JournalAccount\JournalAccountHelper;
use App\Services\Requisitions\Events\RequisitionsCreated;
use App\Services\Requisitions\Events\RequisitionsUpdated;
use App\Validators\StockTransaction\ProductBalanceValidator;
use App\Validators\TrackStock\TrackStockValidator;
use App\Utils\TrackStockUtil;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest;
use Izam\Daftra\AppManager\Services\LockEntityService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\ManufacturingOrder\Exceptions\CannotExceedProductQuantity;
use Izam\ManufacturingOrder\Exceptions\CannotSelectNewProduct;
use Izam\StockRequest\Exceptions\RequestedProductNotFoundInSourceRequest;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\ManufacturingOrder\Exceptions\CannotUpdateOrDeleteFinishedManufacturingOrderRequisitions;
use Izam\ManufacturingOrder\Services\ManufacturingOrderService;

/**
 * @property Tooltip $Tooltip
 * @property Requisition $Requisition
 */
class RequisitionsController extends AppController {

    var $name = 'Requisitions';
    var $components = ['StockValidation'];

	/**
	 * @var Product
	 */
    public $Product;

    public $js_lang_labels = array(
        'The tracking data you have entered has already expired',
    );
    function beforeFilter() {
        parent::beforeFilter();
    }
    function owner_timeline ( $id ){
            $this->set('is_ajax', false);
            if ($this->RequestHandler->isAjax()) {
                $this->set('is_ajax', true);
            }

            $action_line_data = $this->Requisition->getActionLineData  ( $id ) ;
            $all_actions_lists = $action_line_data['all_actions_lists'];
            $all_action_ids = $action_line_data['all_action_ids'];
            require_once APP . 'vendors' . DS . 'Timeline.php';
            $timeline = new Timeline('All', array('secondary_id' => $id));
            $timeline->init(array('ActionLine.id' => $all_action_ids),$all_actions_lists );
            $this->set('data', $timeline->getDataArray());



            foreach ($timeline->ActionKeys as $key => $action) {
                if (in_array($key, $all_actions_lists)) {
                    $invoice_actions[$key] = $action;
                }
            }
            $this->set('actions', $invoice_actions);

    }

    function owner_index() {
        if ( !check_permission(REQUISITION_VIEW) ){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $is_ajax = false ;
        if ($this->RequestHandler->isAjax()) $is_ajax = true;
        $this->set('is_ajax' , $is_ajax ) ;
        $conditions = $this->_filter_params();
        if ( !empty($conditions['RequisitionItem.product_id']) )
        {
            $conditions[] = " Requisition.id in ( Select requisition_id from requisition_items where product_id = ".intval($conditions['RequisitionItem.product_id']).") ";
            unset($conditions['RequisitionItem.product_id']);
        }
        if (isset($this->params['url']['is_stock_request']) && isset($this->params['url']['requisitions_ids'])){
            $ids = mysqli_real_escape_string(getMysqli(),$this->params['url']['requisitions_ids']);
            $conditions["Requisition.id"]=explode(',',"$ids");
        }
        if (isset($this->params['url']['is_manufacture_order']) && isset($this->params['url']['manufacturing_order_type'])){
           $types = match ($this->params['url']['manufacturing_order_type']){
               "inbound"=>[Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP],
               "refund"=>[Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND],
               "outbound"=>[Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL],
               default => null
           };
           if ($types && count($types)){
               $conditions["Requisition.order_type"] = $types;
           }
        }
        $this->paginate['Requisition']['order'] = "Requisition.date DESC , Requisition.id DESC" ;
        if ( empty($conditions['Requisition.store_id']))
        {
            $this->loadModel('ItemPermission');
            $stores_list = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW,null,false,true) ;
            $conditions['Requisition.store_id'] = array_keys($stores_list);
        }
        // To Show only Requisitions Related to PosShift in Requisitions Tab
        if (isset($_GET['pos_shift_id'])) {
            $requisition_ids = Set::extract("{n}.Requisition.id", $this->Requisition->getPosShiftRequisitions($_GET['pos_shift_id']));
            $conditions['Requisition.id'] = $requisition_ids;
        }
        $this->Requisition->applyBranch['onFind']=false;
        $this->Requisition->bindAttachmentRelation('requisition');
        $requisitions = $this->paginate('Requisition', $conditions);
        $this->Session->write('Requisition_apply_branch_select_all', 0);
        $this->setConditionsKey('requisitions', $conditions);
        $this->Requisition->applyBranch['onFind']=true;

        $this->setup_nav_data($requisitions);
        $this->loadModel('JournalAccount') ;
        $this->loadModel('Staff') ;
        $this->loadModel('Store') ;
        $this->loadModel('ExpenseDistribution');
        if (ifPluginActive(BranchesPlugin)) {
            $this->loadModel('Branch');
        }
        foreach ( $requisitions as $k => $v ){
            // Add requisition branch name
            if (ifPluginActive(BranchesPlugin)) {
                $branch = $this->Branch->find(['Branch.id' => $v['Requisition']['branch_id']])['Branch'];
                $requisitions[$k]['Requisition']['branch_name'] = $branch['name'];
            }

             if ( !empty(Requisition::$requisition_order_types[$v['Requisition']['order_type']]['provider_class']))
            {
                $order_types = Requisition::$requisition_order_types[$v['Requisition']['order_type']];
                $model = $order_types['source_model'] ;
                if ( !isset($this->{$model})) $this->loadModel($model);
                if (isset($order_types['provider_id'])) {
                    $provider_id = $this->{$model}->find('first', ['recursive' => -1, 'fields' => $order_types['provider_id'], 'conditions' => ['id' => $v['Requisition']['order_id']]])[$model][$order_types['provider_id']];
                    $provider_class = $order_types['provider_class'];
                    if (!isset($this->{$provider_class})) $this->loadModel($provider_class);

                    $provider = $this->{$provider_class}->find('first', ['recursive' => -1, 'conditions' => ['id' => $provider_id]]);
                    $requisitions[$k]['provider']['label'] = ($provider[$provider_class]['business_name'] ?: ($provider[$provider_class]['first_name'] . ' ' . $provider[$provider_class]['last_name']));
                    $requisitions[$k]['provider']['provider_url'] = $order_types['provider_url'];
                    $requisitions[$k]['provider']['provider_id'] = $provider_id;
                }
            }elseif($v['Requisition']['type'] == Requisition::ORDER_TYPE_MANUAL_OUTBOUND && isset($v['Client']['business_name'])){// manual provider
                $requisitions[$k]['provider']['label'] = $v['Client']['business_name'];
                $requisitions[$k]['provider']['provider_url'] = ['controller' => 'clients' , 'action' => 'view'];
                $requisitions[$k]['provider']['provider_id'] = $v['Requisition']['client_id'];
                $requisitions[$k]['show_account_name_beside'] = true;
            }elseif($v['Requisition']['type'] == Requisition::ORDER_TYPE_MANUAL_INBOUND && isset($v['Supplier']['business_name'])){// manual provider
                $requisitions[$k]['provider']['label'] = $v['Supplier']['business_name'];
                $requisitions[$k]['provider']['provider_url'] = ['controller' => 'suppliers' , 'action' => 'view'];
                $requisitions[$k]['provider']['provider_id'] = $v['Requisition']['supplier_id'];
                $requisitions[$k]['show_account_name_beside'] = true;
            }
            if( !empty($v['Requisition']['journal_account_id']))
            {
                $requisitions[$k]['Requisition']['journal_account_name'] = $this->JournalAccount->find('first' , ['fields' => 'name' ,  'recursive' => -1 ,'conditions' => ['id' =>$v['Requisition']['journal_account_id'] ] ])['JournalAccount']['name'] ;
            }

            $expenseDistribution = $this->ExpenseDistribution->find('first', ['conditions' => ['ExpenseDistribution.requisition_id' => $v['Requisition']['id']]]);
            if ($expenseDistribution)
                $requisitions[$k]['ExpenseDistribution'] = $expenseDistribution['ExpenseDistribution'];
        }
        $this->set('requisitions', $requisitions);

        $this->set ('filters' , $this->Requisition->getFilters () );

        $this->set('isSummary', (isset($_GET['summary']) && $_GET['summary']));
        $this->set('stores_list', $stores_list);
        $this->set('staffs', $this->Staff->getList());
        $this->pageTitle = __("Requisitions", true);
        if(IS_REST){
			$this->set('rest_items', $requisitions);
			$this->set('rest_model_name', "Requisition");
			$this->render("index");
		}
        $this->set('title_for_layout',  __("Requisitions", true));
    }

    function owner_view($id = null) {
        if ( !check_permission(REQUISITION_VIEW) ){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->Requisition->bindAttachmentRelation('requisition');
        $requisition = $this->Requisition->getRequisition($id);
        if (ifPluginActive(BranchesPlugin) && empty($requisition)) {
            $requisition = $this->Requisition->getRequisition($id, [], false, false);
        }
        $this->setIsStockRequest($requisition , true);

        // Check If Requisition Exists in Current Branch
        if (!$requisition) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Requisition not found', true)));
            $this->flashMessage(__('Requisition not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        // Add requisition branch name
        if (ifPluginActive(BranchesPlugin)) {
            $this->loadModel('Branch');
            $branch = $this->Branch->find(['Branch.id' => $requisition['Requisition']['branch_id']])['Branch'];
            $requisition['Requisition']['branch_name'] = $branch['name'];
        }
        $this->set_journal($id);
        $this->set('requisition' , $requisition ) ;
        if(IS_REST){
			$this->set('rest_item', $requisition);
			$this->set('rest_model_name', "Requisition");
			$this->render("view");
		}
        $this->loadModel('JournalAccount') ;
        $journal_account = $this->JournalAccount->find('first' ,['recursive' => -1 ,  'conditions' => ['JournalAccount.id' => $requisition['Requisition']['journal_account_id']] ] ) ;
        $this->set('journal_account' , $journal_account );
        $this->loadModel('Store') ;
        $store = $this->Store->find('first' , ['conditions' => ['Store.id' => $requisition['Requisition']['store_id']] ]) ;
        $to_store = NULL ;
        if ( !empty($requisition['Requisition']['to_store_id']))
        {
            $to_store = $this->Store->find('first' , ['conditions' => ['Store.id' => $requisition['Requisition']['to_store_id']] ]) ;
        }

        $this->loadModel('Staff') ;
        $this->set('staffs', $this->Staff->getList());
        $this->set('to_store' , $to_store  );
        $this->set('store' , $store  );
        $this->set('order' , $this->Requisition->getOrder($requisition['Requisition']['order_id'] , $requisition['Requisition']['order_type']));
        $this->set('model' ,Requisition::$requisition_order_types[ $requisition['Requisition']['order_type']]['source_model']  );
        $this->set('attached_requisitions' , $this->Requisition->find('all' , ['recursive' => 1 ,  'conditions' => ['parent_requisition_id' => $id ] ])  );
        $show_price = false;
        $type = $requisition['Requisition']['type'] ;
        if ($type == Requisition::TYPE_INBOUND) {
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        } else if (($type == Requisition::TYPE_OUTBOUND) && check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
            $show_price = true;
        }

        if ($requisition['Requisition']['order_type'] == Requisition::ORDER_TYPE_PURCHASE_ORDER &&
            in_array($requisition['Requisition']['status'], [Requisition::STATUS_PENDING, Requisition::STATUS_ACCEPTED]) &&
            check_permission(REQUISITION_MODIFY)
        ) {
            $this->set('showDistributeExpenseButton', true);
        } else {
            $this->set('showDistributeExpenseButton', false);
        }

        if ($requisition['ExpenseDistribution'] && check_permission(REQUISITION_MODIFY))
            $this->set('showDistributionTab', true);
        else
            $this->set('showDistributionTab', false);

        $this->loadModel('PrintableTemplate');
        
        $defaultTemplate = $this->PrintableTemplate->getDefaultTemplateForType('requisition');
        if(!is_null($defaultTemplate)){
            $defaultTemplate = array_shift($defaultTemplate);
         }

        $this->set('defaultTemplate', $defaultTemplate);
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('requisition');

        $this->set('has_templates', false);
        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('requisition');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);

        App::import('Vendor', 'notification_2');
        NotificationV2::view_notificationbyref($id, [NotificationV2::NOTI_UPDATE_REQUISITION,NotificationV2::NOTI_ADD_REQUISITION]);
        $this->set('show_price', $show_price);
        $StockTransactionModel = GetObjectOrLoadModel('StockTransaction');
        $requisitionStockTransactions = $StockTransactionModel->find('all', ['recursive' => -1, 'conditions' => ['StockTransaction.order_id' => $id, 'StockTransaction.source_type' => StockTransaction::SOURCE_RQ_PURCHASE_ORDER]]);
        $ReqItemListDiscount = [];
        foreach ($requisitionStockTransactions as $stockTransaction) {
            $ReqItemListDiscount[$stockTransaction['StockTransaction']['product_id']][$stockTransaction['StockTransaction']['ref_id']] = $stockTransaction['StockTransaction']['discount'];
        }
        $this->set('req_items_discount', $ReqItemListDiscount);
        if(($type == Requisition::ORDER_TYPE_MANUAL_OUTBOUND) && $requisition['Requisition']['client_id']){
            $this->loadModel('Client');
            $client = $this->Client->find('first', ['recursive' => -1,'conditions' => ['Client.id' => $requisition['Requisition']['client_id']]]);
            $this->set('client', $client);
        }else  if(($type == Requisition::ORDER_TYPE_MANUAL_INBOUND) && $requisition['Requisition']['supplier_id']){
            $this->loadModel('Supplier');
            $supplier = $this->Supplier->find('first', ['recursive' => -1,'conditions' => ['Supplier.id' => $requisition['Requisition']['supplier_id']]]);
            $this->set('supplier', $supplier);
        }
        $this->set('title_for_layout',  __("View Requisition", true));
    }

    function owner_add($order_type = 1) {
        $this->loadModel('Store');
        $this->js_lang_labels = array_merge($this->js_lang_labels, array(
            "You cannot choose more than ",
            "items",
            "item",
        ));
        if ( !check_permission(REQUISITION_ADD) ){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if ( empty($order_type)){
            $order_type = Requisition::ORDER_TYPE_MANUAL_INBOUND ;
        }
        $this->Requisition->bindAttachmentRelation('requisition');
        $type = Requisition::$requisition_order_types[$order_type]['type'] ;
        $this->loadModel('Invoice');
        $this->loadModel('Store');
		App::import('Vendor', 'AutoNumber');

        if (!empty($this->data)){
            $this->data = \App\Helpers\TrackingNumberHelper::formatTrackingFormData($this->data,'RequisitionItem');
            TrackStockValidator::validate($this->data, \App\Utils\TrackStockUtil::REQUISITION, TrackStockUtil::getRequisitionTypeValidationMode($order_type));
            $type = $this->data['Requisition']['type'];
            $mode = $type == Requisition::TYPE_INBOUND ? 'add' : 'deduct';
            $store_balance = $this->StockValidation->validateQuantityAvailable("requisition", $this->data, $mode);
            $enable_bundles = settings::getValue(InventoryPlugin, 'enable_bundles');
            $bundle_type = settings::getValue(InventoryPlugin, 'bundle_type');
            foreach ($this->data['RequisitionItem'] as $key => $item) {
                if($item['quantity'] <= 0){
                    if (IS_REST) {
                        $this->cakeError('error400', ['message' => __("The quantity of an item cannot be negative or zero", true)]);
                    } else {
                        $this->Requisition->validationErrors['RequisitionItem'][$key]['quantity'] = __("The quantity of an item cannot be negative or zero", true);
                    }
                }
            }
            if($enable_bundles && $this->data['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUAL_INBOUND) {
                //set bundle cost as unit price
                $this->loadModel('Product');
                foreach ($this->data['RequisitionItem'] as $k => $requisitionItem) {
                    $product = $this->Product->findById($requisitionItem['product_id']);
                    if($product['Product']['type'] == Product::BUNDLE_TYPE && Product::checkProductBundleTypePack($product)) {
                        $finalCost = $this->Product->get_bundle_final_cost($product['Product']['id']);
                        if($finalCost) {
                            $this->data['RequisitionItem'][$k]['unit_price'] = $finalCost;
                        }
                    }
                }
            }
            if(isset($this->data['Requisition']) && isset($this->data['Requisition']['is_manufacturing_order'])){

                $validationResponse = $this->handleOutboundManufacturingOrderMaterialsValidations($this->data);
                if ($validationResponse){
                    CustomValidationFlash([$validationResponse]);
                    return $this->redirect($this->referer());
                }
                $errMessage = $this->onDeleteOrUpdateManufacturingOrderRequisitionValidations($this->data);
                if ($errMessage){
                    return $this->redirect($this->referer());
                }
            }

            $message = $this->handleStockRequestValidations($this->data);
            if ($message){
                return $this->redirect($this->referer());
            }

            $message = $this->handleInboundManufacturingOrderMaterialsValidations($this->data);
            if ($message){
                return $this->redirect($this->referer());
            }


            if($order_type == Requisition::ORDER_TYPE_MANUAL_OUTBOUND){
                $this->loadModel('StockTransaction');
                $received_date = $this->StockTransaction->formatDateTime($this->data['Requisition']['date'], getAuthOwner('date_format'));
                foreach($this->data['RequisitionItem'] as $k => &$requisitionItem){
                    if($transaction = $this->StockTransaction->find('first', ['conditions' => [
                        'StockTransaction.product_id' => $requisitionItem['product_id'],
                        'StockTransaction.received_date <' => $received_date, 
                        'StockTransaction.ignored' => 0, 
                        'StockTransaction.status' => StockTransaction::STATUS_PROCESSED, 
                        'StockTransaction.store_id' => $this->data['Requisition']['store_id']], 
                        'order' => 'StockTransaction.received_date DESC']))
                    {
                        $requisitionItem['unit_price'] = $transaction['StockTransaction']['purchase_price'];
                    }
                }
            }

            $this->validateOpenDayWithValidationError($this->data, 'Requisition', 'date');
            if ( $store_balance ) {
                if(empty($this->Requisition->validationErrors))
                {
                    $this->data =$this->checkRequsitionItemsForManufacturingOrders($this->data);
                    $result = $this->Requisition->addRequisition($this->data);
                }else{
                    $result['status'] = false;
                }

//            $this->Requisition->update_next_number($this->data['Requisition']['number']);
                if ($result['status']) {
                    $attachments = $this->data['Requisition']['attachment'];
                    if(isset($this->data['Requisition']['is_manufacturing_order'])){
                        $this->handleAddNewManufactureOrderMaterial($this->data);
                        $this->logAddingOutboundRequisitionToManufacturingOrder($result['data']['Requisition']['order_id'], $result['data']['Requisition']['id'] , $result['data']['Requisition'] , []);
                    }
                    $this->handleStockRequestRelation($this->data , $result['data']['Requisition']['id']);
                    $requisitionsCreated = new RequisitionsCreated($result['data']);
                    $requisitionsCreated->run();
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                    
                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save('requisition', $result['data']['Requisition']['id'], $imagesIds); 
                    }

                    $enableRequisitionItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION);
                    if ($enableRequisitionItemCostCenterDistribution) {
                        $this->loadModel('CostCenter');
                        $this->loadModel('CostCenterTransaction');
                        $costCenterService = new RequisitionCostCenterHandler($this->Requisition);
                        $service = new CostCenterUpdaterService(
                            $costCenterService,
                            $this->CostCenter,
                            $this->CostCenterTransaction
                        );
                        $service->handleCostCenters($result['data']['Requisition']['id']);
                    }

                    $this->flashMessage(__('Requisition has been saved', true), 'Sucmessage');
                    $redirect = array('action' => 'view', $result['data']['Requisition']['id']);

                    if (!empty($this->data['Requisition']['back_to_wf'])) {
                        $redirect = Router::url($this->data['Requisition']['back_to_wf'] . '#RequisitionBlock');
                    } elseif (!empty($result['data']['Requisition']['work_order_id'])) {
                        $redirect = array('controller' => 'work_orders', 'action' => 'view', $result['data']['Requisition']['work_order_id'], '#RequisitionBlock');
                    }
                    if (IS_REST) {
                        if (isset($this->data['extended']) || isset($_GET['extended'])) {
                            die(json_encode(['status' => true, 'id' => $this->Requisition->id, 'data' => $result['data']]));
                        }
                        die(json_encode(['status' => true, 'id' => $this->Requisition->id]));
                    }
                    $this->redirect($redirect);
                } else {
                    if (IS_REST) {
                        die(json_encode(['status' => false,'message' => 'Could not save the requisition, Please fix errors below','errors' => $this->Requisition->validationErrors]));
                    }
                    if (isset($this->Requisition->validationErrors['number']) && !empty($this->Requisition->validationErrors['number'])) {
                        $this->flashMessage($result['data']['Requisition']['number'].' '.__($this->Requisition->validationErrors['number'],true),'Errormessage','secondaryMessage');
                    }
                    if (!empty($this->data['Requisition']['is_stock_request'])){
                        $this->redirect("/owner/stock_request/convert_to_requisition/". $this->data['Requisition']['order_id']);
                    }

                    $this->loadModel('Product');
                    $productIds = array_map(function($item) {
                        return $item['product_id'];
                    },$this->data['RequisitionItem']);
                    $products = $this->Product->getInvoiceProductList($productIds, [], false,[],11,'requisition');
                    $this->set('products', $products);
                    
                    foreach ($this->data['RequisitionItem'] as $kk => $item) {
                        if (!empty($item['product_id'])) {
                            if(isset($products[$item['product_id']])) {
                                $product = $products[$item['product_id']];
                                $this->data['RequisitionItem'][$kk] = array_merge($item , [
                                    'Product'=> $product,
                                ]);
                            }
                        }

                        if(isset($item['lot'])) {
                            $_key = 'lot';
                            $this->data['RequisitionItem'][$kk]['tracking_data'] = '{"lot": "'.$item['lot'].'", "serial": null, "expiry_date": null}';
                        } else if(isset($item['serial'])) {
                            $_key = 'serial';
							$serialString = json_encode($item['serial']);
                            $this->data['RequisitionItem'][$kk]['tracking_data'] = '{"lot": null, "serial": '.$serialString.', "expiry_date": null}';
                        } else if(isset($item['expiry_date'])) {
                            $_key = 'expiry_date';
                            $this->data['RequisitionItem'][$kk]['tracking_data'] = '{"lot": null, "serial": null, "expiry_date": "'.$item['expiry_date'].'"}';
                        }
                        unset($this->data['RequisitionItem'][$kk][$_key]);
                    } 

                    if(!empty($this->data['Requisition']['attachment'])){                       
                        $filesId = explode(',',$this->data['Requisition']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                    }

                    $this->flashMessage(__('Could not save the requisition, Please fix errors below', true));
//                    if (!empty( $this->Requisition->validationErrors)) {
//                        $this->flashMessage(implode("<br/>",  $this->Requisition->validationErrors), 'Errormessage', 'secondaryMessage');
//                    }
                }
            }else{
                if (!empty($this->data['Requisition']['is_stock_request'])){
                    $this->redirect("/owner/stock_request/convert_to_requisition/". $this->data['Requisition']['order_id']);
                }
            }
            // To Fix Calender Formatting If User Have Different Format Other Than strtotime
            if (!empty($this->data['Requisition']['date'])) {
                $this->data['Requisition']['date'] = $this->Requisition->formatDate($this->data['Requisition']['date']);
            }
            //to handle missed unit data when validation error happen 
            $this->data['RequisitionItem'] = $this->Requisition->addRequisitionItemMultiUnits($this->data['RequisitionItem']);
        }elseif ($_GET['clone_id'] && $_GET['clone_id'] > 0) {

            if ( !check_permission(REQUISITION_ADD) ){
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }

            $requisition = $this->Requisition->getRequisition($_GET['clone_id']);
            if (ifPluginActive(BranchesPlugin) && empty($requisition)) {
                $requisition = $this->Requisition->getRequisition($_GET['clone_id'], [], false, false);
            }

            if (!$requisition) {
                if(IS_REST) $this->cakeError('error404', array('message' => __('Requisition not found', true)));
                $this->flashMessage(__('Requisition not found', true));
                $this->redirect($this->referer(array('action' => 'index'), true));
            }

            unset($requisition['Requisition']['id'],$requisition['Requisition']['number'] ,$requisition['Requisition']['date'] );
            $type = $this->data['Requisition']['type'];
            $this->data = $requisition;

        }else {
            if ( !empty($_GET['product_id']))
            {
                $this->loadModel('Product');
                $product = $this->Product->find('first' , ['recursive' => -1,'conditions' => ['id' =>$_GET['product_id'] ] ]);
                if ( $product['Product']['status'] != ProductStatusUtil::STATUS_ACTIVE) {
                    if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("You cannot transfer stock for %s products", true), __(ProductStatusUtil::getStatusName($product['Product']['status']), true))));
                    $this->flashMessage(sprintf(__("You cannot transfer stock for %s products", true), __(ProductStatusUtil::getStatusName($product['Product']['status']),true)));
                    $this->redirect($this->referer(array('action' => 'index')));
                }
                if($product) {
                    $this->data['RequisitionItem'][0] = $product;
                    $this->data['RequisitionItem'][0]['product_id'] = intval($_GET['product_id']);
                    $this->data['RequisitionItem'][0]['unit_price'] =( $product['Product']['type'] == Product::BUNDLE_TYPE && $order_type == Requisition::ORDER_TYPE_MANUAL_INBOUND) ? $this->Product->get_bundle_final_cost($product['Product']['id']) : $product['Product']['average_price'];
                    $this->data['RequisitionItem'][0]['item'] = $product['Product']['name'];
                    $this->data['RequisitionItem'][0]['stock_balance'] = $product['Product']['stock_balance'];
                    $this->data['RequisitionItem'][0]['quantity'] = 0;
                }
            }
            $this->data['Requisition']['journal_account_id'] = settings::getValue(InventoryPlugin , 'stock_transactions_default_journal_account_id');
        }
        $this->setTrackingOperation($order_type);
        $this->set('order_type', $order_type);
        $this->_settings($type);

        if ( !empty($_GET['product_id'])){
            //Add the store id based on user permission otherwise get primary store id
            $this->data['Requisition']['store_id'] = array_keys($this->viewVars['stores_list'])[0] ?? $this->viewVars['primary_store'];
        }

        if(isset($_GET['clone_id']) && !empty($_GET['clone_id'])) {
        $enableRequisitionItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION);     
        if(!empty($enableRequisitionItemCostCenterDistribution)) {
            $this->setJournalAccountForRequisitionItem($this->data['RequisitionItem']);
         }
        }

    }

    function owner_delete($id = null) {
        if ( !check_permission(REQUISITION_MODIFY) ){
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel('Post');
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('Requisition', true)));
            $referer_url = !($this->Session->check('referer_url')) ? array('action' => 'index') : $this->Session->read('referer_url');
            $this->Session->delete('referer_url');
            $this->redirect($referer_url);
        }
        $module_name = __('Requisition', true);
        if (is_array($_POST['ids']) && count($_POST['ids']) > 1) {
            $module_name = __('Requisitions', true);
        }

        $requisitions = $this->Requisition->find('all', ['conditions' => ['Requisition.id' => $id]]);
        $this->loadModel('Product');
        foreach ( $requisitions as $r ) {
            $this->validate_open_day($r['Requisition']['date']);

            if(isBranchPluginActive() && $r['Requisition']['branch_id']!=getCurrentBranchID()){

                $error_msg = sprintf(__('Can\'t delete requisitions %s  from current branch',true),strongStr($r['Requisition']['number']));
                $this->flashMessage($error_msg,'Errormessage','secondaryMessage');
                $this->redirect($this->referer(['action' => 'index'], true));
            }

            if (( (!empty($r['Requisition']['order_id']) && $r['Requisition']['status'] == Requisition::STATUS_PENDING  && !empty($this->Requisition->getOrder($r['Requisition']['order_id'], $r['Requisition']['order_type']))) || in_array($r['Requisition']['order_type'], [Requisition::ORDER_TYPE_POS_INBOUND, Requisition::ORDER_TYPE_POS_OUTBOUND,Requisition::ORDER_TYPE_STOCKTAKING_IN, Requisition::ORDER_TYPE_STOCKTAKING_OUT]))&& empty($_GET['force']))
            {
                $this->flashMessage(sprintf(__("Can't delete pending requisitions for orders", true), ucfirst($module_name)));
                $this->redirect(array('action' => 'index'));
            }

            // Check Amount Sufficient
            $type = $r['Requisition']['type'];

            $this->validateItemIsNotLocked($id, 'requisition', Router::url(['action' => 'owner_view', $id]));

            $mode = $type == Requisition::TYPE_INBOUND ? 'deduct' :'add_delete';
            $this->StockValidation->validateQuantityAvailable("requisition", $r, $mode);

            $trackingValidationResult = TrackStockValidator::validate($r, TrackStockUtil::REQUISITION, TrackStockUtil::getRequisitionTypeValidationMode($r['Requisition']['order_type']), true);
            if($trackingValidationResult !== true)
            {
                $error_msg = sprintf(__('Can\'t delete  requisitions #'.$r['Requisition']['number'],true));
                $this->flashMessage($error_msg,'danger','secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
            $errMessage = $this->onDeleteOrUpdateManufacturingOrderRequisitionValidations($r);
            if ($errMessage){
                if (str_contains($this->referer('/', true), 'from_manufacturing_order_view=1')) {
                    $this->flashMessage($errMessage,'danger','secondaryMessage');
                    return $this->redirect("/v2/owner/entity/manufacturing_order/".$r['Requisition']['order_id']."/show");
                }
                $this->redirect(array('action' => 'index'));
            }
        }

        if (empty($requisitions)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect(array('action' => 'index'));
        }
        $owner = getAuthOwner();
		if(!empty($_GET['force'])) $_POST['ids'][]=$id;
        if ((!empty($_POST['submit_btn']) || !empty($_GET['force'])) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            $relatedStockRequests = [];
            $stockRequestService = resolve(\Izam\StockRequest\Services\StockRequestService::class);
            if(is_array($id)){
                foreach($id as $currentKey => $currentId){
                    $relatedStockRequests[$currentId] = $stockRequestService->getStockRequestRelationByRequisitionId($currentId);    
                }
            }else{
                $relatedStockRequests[$id] = $stockRequestService->getStockRequestRelationByRequisitionId($id);
            }
            if (($_POST['submit_btn'] == 'yes'|| !empty($_GET['force'])) && $this->Requisition->delete_with_related($id)) {
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                foreach ( $requisitions as $rKey => $r ) {
                    $this->handleDeleteRequisition($r["Requisition"]["id"],$r );
                    $requisitions[$rKey]['RelatedStockRequests'] = $relatedStockRequests[$r['Requisition']['id']];
                    $event = new RequisitionsUpdated($requisitions[$rKey]);
                    $event->run();
                    if ( !empty($r['Requisition']['order_id']))
                    {
                        $this->Requisition->updateForOrder($r , $r['Requisition']['order_type'], false, true, 1, 'delete') ;
                    }
                }
                $customRedirectResponse = $this->checkAndHandleCustomRedirects($requisitions);
                    if ($customRedirectResponse){
                        return $customRedirectResponse;
                    }
                /**
                 * Redirects to the appropriate order view page if a valid order type is provided in the request.
                 * Currently, it only handles the "invoice" order type and redirects to the invoice view page.
                 */
                if(isset($_POST['order_type']) && in_array($_POST['order_type'], [Requisition::ORDER_TYPE_INVOICE])){
                    $orderId = (int) $_POST['order_id'];
                    switch($_POST['order_type']){
                        case Requisition::ORDER_TYPE_INVOICE:
                            $this->redirect(array('controller'=>'invoices','action' => 'view',$orderId));   
                            break;
                    }
                }
                
                $this->redirect(array('action' => 'index'));
            } else {
                $customRedirectResponse = $this->checkAndHandleCustomRedirects($requisitions);
                if ($customRedirectResponse){
                    return $customRedirectResponse;
                }
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            }
        }
        $this->set('requisitions', $requisitions);
        $this->set('module_name', $this->Requisition->title);
        $this->set('title_for_layout',  __('Delete', true) . ' ' . __('Requisition', true));
        $this->set('stop_selling_expired_items', settings::getValue(PRODUCT_TRACKING_PLUGIN, 'stop_selling_expired_tracking_items'));

    }


    /**
     * Changes the status of a requisition and updates the order
     * @param INT $requisition_id
     * @param INT $status
     */
    function owner_change_status ( $requisition_id , $status ) {
        if ( !check_permission(REQUISITION_MODIFY) || ( empty($requisition_id) && empty($_POST['ids'])) ){
            if($this->RequestHandler->isAjax()){
                die(json_encode(['error'=>true,'message'=>__('You are not allowed to view this page', TRUE)]));
            }
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $ids = $_POST['ids'];
        if ( empty($ids))
        {
            $ids[] = $requisition_id;
        }
        $status_name = ($status==Requisition::STATUS_ACCEPTED) ?__t('accept'):__t('reject');
        $status_action = ($status==Requisition::STATUS_ACCEPTED) ?__t('accepted'):__t('rejected ');
        $this->loadModel('Product');
        $dod=settings::getValue(InventoryPlugin, 'disable_overdraft');

        foreach ( $ids as $i ){
            $requisition = $this->Requisition->find('first' , ['conditions' => ['Requisition.id' => $i] ]) ;
            $ignoreUpdate= $this->validateIsManufacturingOrder($requisition);
            if ($ignoreUpdate) continue;
            $view_link='<a target="_blank" href="'. Router::url('/owner/requisitions/view/'.$i).'">'.__t('Requisition'). ' #'.strongStr($requisition['Requisition']['number']??$i).'</a>' ;

            $this->validate_open_day($requisition['Requisition']['date']);

            if(isBranchPluginActive() && $requisition['Requisition']['branch_id']!=getCurrentBranchID()){

                $error_msg = sprintf(__('can\'t %s  requisitions %s  from current branch',true),$status_name,$view_link);
                if($this->RequestHandler->isAjax()){
                   
                    $error_msg = sprintf(__('%s failed to be updated  %s',true),$view_link,$error_msg);
                    die(json_encode(['error'=>true,'message'=>$error_msg]));
                }
                
                $this->flashMessage($error_msg,'Errormessage','secondaryMessage');
                $this->redirect($this->referer(['action' => 'index'], true));
            }
            if($requisition['Requisition']['status']==Requisition::STATUS_ACCEPTED||$requisition['Requisition']['status']==Requisition::STATUS_CANCELLED) {
                $old_status_name = $requisition['Requisition']['status']==Requisition::STATUS_ACCEPTED ?__t('accepted'):__t('rejected');
                $error_msg = sprintf(__t('%s failed to be updated as it’s  already %s'),$view_link,$old_status_name);
                if($this->RequestHandler->isAjax()){
                    die(json_encode(['error'=>true,'message'=>$error_msg]));
                }
             
            }
            if($requisition['Requisition']['status']==$status) {continue;}
            if($status==Requisition::STATUS_ACCEPTED) {
                $type = $requisition['Requisition']['type'];
                $mode = $type == Requisition::TYPE_INBOUND ? 'add' : 'deduct';
                $confirmed_requisition = $requisition;
                $confirmed_requisition['Requisition']['status'] = Requisition::STATUS_ACCEPTED;
                if ($type == Requisition::TYPE_OUTBOUND){
                    $this->StockValidation->validateQuantityAvailable("requisition", $confirmed_requisition, $mode, $requisition, true);
                }
                $trackingValidationResult = TrackStockValidator::validate($requisition, TrackStockUtil::REQUISITION, TrackStockUtil::getRequisitionTypeValidationMode($requisition['Requisition']['order_type']));
                if($trackingValidationResult !== true)
                {
                    $error_msg = __('can\'t accept  requisition #' . $view_link ,true);
                    if($this->RequestHandler->isAjax()){
                       $validation_message='<ul>';
                       foreach(array_pop($trackingValidationResult) as $error){
                        $validation_message.='<li style="margin: 0px 34px;">'.$error.'</li>';
                       }
                       
                       
                        $validation_message.= '</ul>';
                        $this->Session->delete('Message');
                        $error_msg = sprintf(__t('%s failed to be updated  %s'),$view_link,$validation_message);
                        die(json_encode(['error'=>true,'message'=>$error_msg]));
                    }
                    $this->flashMessage($error_msg,'Errormessage','secondaryMessage');
                    $this->redirect(array('action' => 'edit',$i));
                }
            }

            $requisition['Requisition']['status'] = $status ;
            $this->Requisition->updateRequisition($requisition) ;
            $this->Requisition->updateForOrder($requisition['Requisition']['order_id'] , $requisition['Requisition']['order_type'] ,  false ) ;
        }

        if($this->RequestHandler->isAjax()){
            $succ_msg=sprintf(__t('%s has been %s'),$view_link,$status_action);
            die(json_encode(['error'=>false,'message'=>$succ_msg]));
        }
        $event = new RequisitionsUpdated($requisition);
        $event->run();
        $this->flashMessage(sprintf(__('%s  has been saved', true), __("Requisition",true) ), 'Sucmessage');
        if ( !empty($_GET['return_reference'])){
            $url=false;
            if (is_array(Requisition::$requisition_order_types[$requisition['Requisition']['order_type']]['view_url_array']) && is_array([$requisition['Requisition']['order_id'].'#RequisitionBlock'])) {
                $url=Requisition::$requisition_order_types[$requisition['Requisition']['order_type']]['view_url_array'] + [$requisition['Requisition']['order_id'].'#RequisitionBlock'];
            }
            $this->redirect($url);
        }else {
            $this->redirect(array('action' => 'index'));
        }
    }

    /**
     * This is a function used to trigger updating the journals/average price of Requisitions incase for Expense Distributions
     * @param $requisition_id
     */
    function owner_trigger_update($requisition_id){
        $this->loadModel('Requisition');
        $requisition = $this->Requisition->getRequisition($requisition_id);
        if (!in_array($requisition['Requisition']['status'], [Requisition::STATUS_ACCEPTED, Requisition::STATUS_MODIFIED])) {
            die(json_encode(['status' => false, 'errors' => ['Requisition is Not Received/Modified - Not Updating']]));
        }
        StockTransaction::updateForRequisition($requisition);
        $this->Requisition->update_journals($requisition, false);
        die(json_encode(['status' => true]));
    }

    /**
     *
     * @param type $type
     */
    private function _settings($type) {
        $this->loadModel('ItemPermission');
        $stores_list = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING) ;
        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList());
        $aps = '1';
        $this->loadModel('Product');
        $this->loadModel('RequisitionItem');
        $this->loadModel('Document');
        $this->set('aps', $aps);
        $this->loadModel('Store');
        $primary = $this->Store->getPrimaryStore();
        $this->set('primaryStore', $primary);
        $this->set('title_for_layout',  __('Add Requisition', true));
        if (ifPluginActive(ExpensesPlugin)) {
            $this->loadModel('Treasury');
            $this->set('treasuries', $this->Treasury->get_list());

            $this->set('primary_treasury_id', $this->Treasury->get_primary());
        }

        $enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
        if ($enable_multi_units) {
            $this->loadModel('UnitTemplate');
            $unit_templates = $this->UnitTemplate->find('list', ['fields' => 'template_name']);
            $this->set('unit_templates', $unit_templates);
        }
        $this->set('enable_multi_units', $enable_multi_units);
        $this->set('itemModel', 'RequisitionItem');
        $this->set('primary_store', $this->Store->getPrimaryStore());
        $this->set('stores_list', $stores_list);
        $show_price = false;
        $edit_price = false;
        if ($type == Requisition::TYPE_INBOUND) {
            if (check_permission(EDIT_STOCK_TRANSACTION_PRICE)) {
                $edit_price = true;
            }
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        } else if (in_array($type , [Requisition::TYPE_OUTBOUND,Requisition::TYPE_NOEFFECT]) ) {
            /*
            if (check_permission(EDIT_STOCK_TRANSACTION_PRICE) && $type === Requisition::TYPE_OUTBOUND) {
                $edit_price = true;
            }
            */
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        }
        if (isset($this->data['Requisition']['order_type']))
        {
            $this->setTrackingOperation($this->data['Requisition']['order_type']);
            $this->set('order_type', $this->data['Requisition']['order_type']);
        }

        $products_ids = [];
        foreach($this->data['RequisitionItem'] as $invoiceItem)
        {
            $products_ids[] = $invoiceItem['product_id'];
        }
        if($products_ids) {
            $products = $this->Product->getInvoiceProductList($products_ids, [], false,[],11,'requisition');
            $this->set('products', $products);
        }
        if ($type == Requisition::TYPE_OUTBOUND && settings::getValue(InventoryPlugin, SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS)) {
            $this->set(SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS, true);
        }
        $this->set('type', $type);
        $this->set('show_price', $show_price);
        $this->set('edit_price', $edit_price);
        //Loading in the controller because it should be loaded once and not for each record
        $this->set('currencyFraction' , CurrencyHelper::getFraction(getCurrentSite('currency_code')));
        \App::import('Vendor', 'AutoNumber');
        if(empty($this->data['Requisition']['number'])) {
            $this->data['Requisition']['number'] = $this->data['Requisition']['hidden_number'] = \AutoNumber::get_auto_serial(\AutoNumber::mapRequisitionTypeToAutoNumberType($type));
        }
    }

    private function setTrackingOperation($orderType)
    {
        $stockOperation = false;
        if (in_array($orderType, [
            Requisition::ORDER_TYPE_MANUAL_INBOUND,
            Requisition::ORDER_TYPE_INVOICE_REFUND,
            Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE,
            Requisition::ORDER_TYPE_PURCHASE_ORDER,
            Requisition::ORDER_TYPE_PURCHASE_REFUND
        ])) {
            $stockOperation = 'add';
        } elseif (in_array($orderType, [
            Requisition::ORDER_TYPE_MANUAL_OUTBOUND,
            Requisition::ORDER_TYPE_TRANSFER_REQUISITION,
            Requisition::ORDER_TYPE_INVOICE
        ])) {
            $stockOperation = 'deduct';
        }
        $this->set('stockOperation', $stockOperation);
    }

    private function validateItemIsNotLocked($id, $entityKey, $redirectUrl): void
    {
        try {
            /** @var LockEntityService $lockService */
            $lockService = izam_resolve(LockEntityService::class);
            $response = $lockService->isEditable($entityKey, $id, getAuthOwner('id'));

            if (!$response->isSuccess()) {
                $message = sprintf(__t('You cannot edit or delete the requisition that related to a transaction synced to %s'), __t($response->getResult()));
                if (IS_REST) {
                    $this->cakeError('error404', ['message' => $message]);
                }
                $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
                $this->redirect($redirectUrl);
            }
        } catch (\Throwable $throwable) {
            // if anything went wrong, do nothing and continue normal cycle
        }
    }

    function owner_edit($id = null) {
        if ( !check_permission(REQUISITION_MODIFY) ){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        $this->js_lang_labels = array_merge($this->js_lang_labels, (array)$this->invoice_js_labels);
        $this->loadModel('ItemPermission');
        $stores_list = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING,null,false,true) ;
       
        $otherConditions['Requisition.store_id'] = array_keys ($stores_list) ;
        if (ifPluginActive(BranchesPlugin)) {
            $otherConditions['Requisition.branch_id'] = getCurrentBranchID();
        }
        $this->Requisition->bindAttachmentRelation('requisition');
        $requisition = $this->Requisition->getRequisition($id, $otherConditions);
        $oldData = $requisition;
        $this->validateItemIsNotLocked(
            $id, 'requisition', Router::url(['action' => 'owner_view', $id])
        );
        $errMessage = $this->onDeleteOrUpdateManufacturingOrderRequisitionValidations($requisition);
        if ($errMessage){
            if (str_contains($this->referer('/', true), 'from_manufacturing_order_view=1')) {
                $this->flashMessage($errMessage,'Errormessage','secondaryMessage');
                return $this->redirect("/v2/owner/entity/manufacturing_order/".$requisition['Requisition']['order_id']."/show");
            }
            return $this->redirect($this->referer());
        }

        $requisitionWithoutBranchCondition = false;

        if(ifPluginActive(BranchesPlugin) && !$requisition) {
            // Check If Requisition Exists in Another Branch
            unset($otherConditions['Requisition.branch_id']);
            $requisitionWithoutBranchCondition = $this->Requisition->getRequisition($id, $otherConditions);
        }

        if (!$requisition && $requisitionWithoutBranchCondition) {
            $BranchesModel = GetObjectOrLoadModel('Branch');
            $branchName = $BranchesModel->find('first', ['conditions' => ['Branch.id' => $requisitionWithoutBranchCondition['Requisition']['branch_id']]])['Branch']['name'];
            if(IS_REST) $this->cakeError('error403', array('message' => sprintf(__("This Requisition is in Branch %s", true), $branchName)));
            $this->flashMessage(sprintf(__("This Requisition is in Branch %s", true), $branchName));
            $this->redirect($this->referer(['action' => 'index'], true));
        }
        if (!$requisition && !$requisitionWithoutBranchCondition) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Requisition not found', true)));
            $this->flashMessage(__('Requisition not found', true));
            $this->redirect(Router::url(['action' => 'index'], true));
        }

        // use Product name for invoice and PURCHASE Invoice
        if ($requisition && ($requisition['Requisition']['order_type'] == Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE || $requisition['Requisition']['order_type'] == Requisition::ORDER_TYPE_INVOICE || $requisition['Requisition']['order_type'] == Requisition::ORDER_TYPE_PURCHASE_ORDER || $requisition['Requisition']['order_type'] == Requisition::ORDER_TYPE_INVOICE_REFUND)) {
            foreach ($requisition['RequisitionItem'] as $key => &$item) {
                $item['item'] = $item['Product']['name'];
            }
            // Remove the refernce of &item
            unset($item);
        }
        // Set a Flag For View to prevent editing the requsition unit price as it causes issues if the requisition was not created manually
        $blockUnitPriceEdit = $requisition['Requisition']['order_id'] != "0";
        $this->set('blockUnitPriceEdit', $blockUnitPriceEdit);

        if ( in_array($requisition['Requisition']['order_type'], [Requisition::ORDER_TYPE_POS_INBOUND, Requisition::ORDER_TYPE_POS_OUTBOUND,Requisition::ORDER_TYPE_STOCKTAKING_IN, Requisition::ORDER_TYPE_STOCKTAKING_OUT])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'), true);
        }

        /**
         * Validate closed day period only in case the requisition status is not pending
         */
        if($requisition['Requisition']['status'] != Requisition::STATUS_PENDING) {
            $this->validate_open_day($requisition['Requisition']['date']);
        }

        /** Check for Expense Distribution **/
        if (isset($requisition['ExpenseDistribution'])) {
            if(IS_REST) $this->cakeError('error403', array('message' => __('You cannot edit in the requisition that has an expenses added to it', true)));
            $this->flashMessage(__('You cannot edit in the requisition that has an expenses added to it', true));
            $referer_action = Router::parse($this->referer('/', true))['action'];
            if ($referer_action == 'view')
                $this->redirect(array('action' => 'view', $id));
            else
                $this->redirect(array('action' => 'index'));
        }

        if (!empty($this->data)) {
                $this->data = \App\Helpers\TrackingNumberHelper::formatTrackingFormData($this->data,'RequisitionItem');
            if(empty($this->data['Requisition']['store_id']) && !empty($requisition['Requisition']['store_id']))
            {
                $this->data['Requisition']['store_id'] = $requisition['Requisition']['store_id'];
            }
            $result['status'] = TrackStockValidator::validate($this->data, TrackStockUtil::REQUISITION, TrackStockUtil::getRequisitionTypeValidationMode($requisition['Requisition']['order_type']));
            if ( $requisition['Requisition']['status'] == Requisition::STATUS_PENDING ){
                $this->data['Requisition']['status'] = Requisition::STATUS_ACCEPTED ;
            }
            $type = $this->data['Requisition']['type'];
            $mode = $type == Requisition::TYPE_INBOUND ? 'add' : 'deduct';
            $store_balance = $this->StockValidation->validateQuantityAvailable("requisition", $this->data, $mode, $requisition['Requisition']['status'] != Requisition::STATUS_PENDING ? $requisition : []);  
            App::import('Vendor', 'AutoNumber');
            $autonumber_type =\AutoNumber::mapRequisitionTypeToAutoNumberType($this->data['Requisition']['type']);
            \AutoNumber::set_validate($autonumber_type);
            if(empty($this->data['Requisition']['number'])){
                $this->data['Requisition']['hidden_number'] = $this->data['Requisition']['number'] = \AutoNumber::get_auto_serial($autonumber_type);
                $generated_number = true;
            }
            foreach ($this->data['RequisitionItem'] as $key => $item) {
                if($item['quantity'] <= 0){
                    if (IS_REST) {
                        $this->cakeError('error400', ['message' => __("The quantity of an item cannot be negative or zero", true)]);
                    } else {
                        $this->Requisition->validationErrors['RequisitionItem'][$key]['quantity'] = __("The quantity of an item cannot be negative or zero", true);
                    }
                }
            }
            if ($store_balance) {
                $this->validateOpenDayWithValidationError($this->data, 'Requisition', 'date');
                if(empty($this->Requisition->validationErrors))
                {
                    $this->data =$this->checkRequsitionItemsForManufacturingOrders($this->data);
                    $result = $this->Requisition->updateRequisition($this->data);
                }else{
                    $result['status'] = false;
                }
                if ($result['status'] && !empty($requisition['Requisition']['order_id'])) //Order Requisition
                {
                    //Try to update requisition related order it it fails it rolls back the changes on the order and the requisition
                    $order = $this->Requisition->getOrder($requisition['Requisition']['order_id'] , $requisition['Requisition']['order_type']) ;
                    if($this->Requisition->checkIfStockRequest($requisition)){
                        $this->loadModel('StockRequest');
                        $order = $this->StockRequest->find('first', ['conditions' => ['StockRequest.id' => $requisition['Requisition']['order_id'] ] ] );
                    }
                    $requisition_pass = $this->Requisition->updateForOrder($order , $requisition['Requisition']['order_type'] ,  false , false ) ;
                    if ( !$requisition_pass && !$this->data['Requisition']['is_stock_request'] && !(in_array($this->data['Requisition']['order_type'] , [Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND , Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL]) )){
                        $validationMessage = __t('Amount more than order' , true);
                        $this->Requisition->updateRequisition($requisition); // Updates with the old requisition -- ROLL BACK
                        $this->Requisition->updateForOrder($order , $requisition['Requisition']['order_type'] ,  false ) ;
                        $result['status'] = false ;
                        CustomValidationFlash([$validationMessage]);
                        if (isset($result['data']['RequisitionItem'])) { //to handle missed data in view again when redirect as unit factor ... , caze item updated when updateRequisition  
                            $this->data['RequisitionItem'] = $result['data']['RequisitionItem'];
                        }
                    }
                    if ($this->data['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
                        $validationResponse = $this->handleOutboundManufacturingOrderMaterialsValidations($this->data , true);
                        if ($validationResponse){
                            $this->Requisition->updateRequisition($requisition); // Updates with the old requisition -- ROLL BACK
                            $this->Requisition->updateForOrder($order , $requisition['Requisition']['order_type'] ,  false ) ;
                            CustomValidationFlash([$validationResponse]);
                            return $this->redirect($this->referer());
                        }
                    }

                    if ($this->data['Requisition']['is_stock_request']){
                        $message = $this->handleStockRequestValidations($this->data, true);
                        if ($message){
                            $validationMessage = $message;
                            $this->Requisition->updateRequisition($requisition); // Updates with the old requisition -- ROLL BACK
                            CustomValidationFlash([$validationMessage]);
                            return $this->redirect($this->referer());
                        }
                    }

                    $message = $this->handleInboundManufacturingOrderMaterialsValidations($this->data, true);
                    if ($message){
                        $validationMessage = $message;
                        $this->Requisition->updateRequisition($requisition); // Updates with the old requisition -- ROLL BACK
                        CustomValidationFlash([$validationMessage]);
                        return $this->redirect($this->referer());
                    }

                }
                if ($result['status']) {
                    if(isset($this->data['Requisition']['is_manufacturing_order'])){
                        $this->handleAddNewManufactureOrderMaterial($this->data);
                        $this->logAddingOutboundRequisitionToManufacturingOrder($this->data['Requisition']['order_id'] , $this->data['Requisition']['id'], $result['data']['Requisition'] , $oldData);
                    }
                    if(!empty($generated_number)) \AutoNumber::update_auto_serial($autonumber_type);
                    elseif($this->data['Requisition']['number']!=$this->data['Requisition']['hidden_number']) \AutoNumber::update_last_from_number($this->data['Requisition']['number'],$autonumber_type);

                    $attachments = $this->data['Requisition']['attachment'];
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                    
                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save('requisition', $id, $imagesIds); 
                    }
                    $event = new RequisitionsUpdated($requisition);
                    $event->run();

                    $enableRequisitionItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION);
                    if ($enableRequisitionItemCostCenterDistribution) {
                        $this->loadModel('CostCenter');
                        $this->loadModel('CostCenterTransaction');
                        $costCenterService = new RequisitionCostCenterHandler($this->Requisition);
                        $service = new CostCenterUpdaterService(
                            $costCenterService,
                            $this->CostCenter,
                            $this->CostCenterTransaction
                        );
                        $service->handleCostCenters($requisition['Requisition']['id']);
                    }

                    $this->flashMessage(__('Requisition has been updated', true), 'Sucmessage');
                    $redirect = array('action' => 'view', $id);
                    if (IS_REST) {
                        if ((isset($this->data['extended']) || isset($_GET['extended'])) && isset($result['data'])) {
                            die(json_encode(['status' => true, 'id' => $this->Requisition->id, 'data' => $result['data']]));
                        }
                        die(json_encode(['status' => true, 'id' => $this->Requisition->id]));
                    }
                    $this->redirect($redirect);
                } else {
                    foreach ($this->data['RequisitionItem'] as $kk => $item) {
                        if(isset($item['lot'])) {
                            $_key = 'lot';
                            $this->data['RequisitionItem'][$kk]['tracking_data'] = '{"lot": "'.$item['lot'].'", "serial": null, "expiry_date": null}';
                        } else if(isset($item['serial'])) {
                            $_key = 'serial';
	                        $serialString = json_encode($item['serial']);
	                        $this->data['RequisitionItem'][$kk]['tracking_data'] = '{"lot": null, "serial": '.$serialString.', "expiry_date": null}';
                        } else if(isset($item['expiry_date'])) {
                            $_key = 'expiry_date';
                            $this->data['RequisitionItem'][$kk]['tracking_data'] = '{"lot": null, "serial": null, "expiry_date": "'.$item['expiry_date'].'"}';
                        }
                        unset($this->data['RequisitionItem'][$kk][$_key]);
                    }   
                    if(!empty($this->data['Requisition']['attachment'])){
                        $filesId = explode(',',$this->data['Requisition']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                    }
                    $this->flashMessage(__('Could not update Requisition', true));
                    if (isset($result['message'])) {
                        if (IS_REST)
                            die(json_encode(['status' => false, 'message' => __('Couldn\'t update Requisition')]));
                        $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
//                        if (!empty( $this->Requisition->validationErrors)) {
//                            $this->flashMessage(implode("<br/>",  $this->Requisition->validationErrors), 'Errormessage', 'secondaryMessage');
//                        }
                    }
                }
            }else {  
                $requisition['Requisition']['date'] = format_datetime($requisition['Requisition']['date']);
            }


            // If this is an edit then we can't allow the user to change the store
            if(empty($this->data['Requisition']['order_id'])){
                $this->data['Requisition']['order_id'] = $requisition['Requisition']['order_id'];
                $this->data['Requisition']['store_id'] = $requisition['Requisition']['store_id'];
            }
            //to handle missed unit data when validation error happen 
            $this->data['RequisitionItem'] = $this->Requisition->addRequisitionItemMultiUnits($this->data['RequisitionItem']);

        } else {
            $requisition['Requisition']['date'] = format_datetime($requisition['Requisition']['date']);
            $this->data = $requisition;
        }
        $this->setIsStockRequest($requisition);
        $this->set('edit_price' ,(!empty($requisition['Requisition']['order_id'])) );
        $this->set('order_type', $requisition['Requisition']['order_type']);
        $this->set(compact('requisition'));
        $this->_settings($requisition['Requisition']['type']);
        $this->set('title_for_layout',  __('Edit Requisition',true));
        $this->loadModel('Store');
        $requisition_store = $this->Store->find('first', ['conditions' => ['Store.id' =>  $requisition['Requisition']['store_id']]] );
        $this->set('requisition_store',  $requisition_store);

        if(isset($this->data['Requisition']['order_type']) && $this->data['Requisition']['order_type'] == Requisition::ORDER_TYPE_INVOICE) {
            $invoice = $this->Requisition->getOrder($this->data['Requisition']['order_id'], $this->data['Requisition']['order_type']);
            $this->set('transaction_date', $invoice['Invoice']['date']);
        }

        $isRequisitionItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_JOURNAL_ACCOUNT);

        if(!empty($isRequisitionItemAccountEnabled)) {
          $this->setJournalAccountForRequisitionItem($this->data['RequisitionItem']);
        }

        $enableRequisitionItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION);
        if($enableRequisitionItemCostCenterDistribution) {
            $this->setCostCentersForRequisitionItem($this->data['RequisitionItem']);
        }

        $this->set('stop_selling_expired_items', settings::getValue(PRODUCT_TRACKING_PLUGIN, 'stop_selling_expired_tracking_items'));
        $this->render('owner_add');
    }
    function owner_journal_settings(){
        $this->loadModel('JournalAccount');
        $this->loadModel('Post');
        if (!empty($this->data)) {
            foreach ( Requisition::$requisition_order_types as $k => $type)
            {
                settings::setValue(InvoicesPlugin, 'requisition_journal_'.$k, $this->data[InvoicesPlugin]['requisition_journal_'.$k]);
            }
            $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
            $this->redirect(array('action' => 'journal_settings'));die ;
        }
        //Setting data for the view
        $enable_requisitions = settings::getValue(InventoryPlugin,'enable_requisitions');
        $this->set('enable_requisitions',$enable_requisitions);

        $enable_requisitions_po = settings::getValue(InventoryPlugin,'enable_requisitions_po');
        $this->set('enable_requisitions_po',$enable_requisitions_po);

        foreach ( Requisition::$requisition_order_types as $k => $type)
        {
            $this->data[InvoicesPlugin]['requisition_journal_'.$k] = settings::getValue(InvoicesPlugin,'requisition_journal_'.$k );
            if ( empty($this->data[InvoicesPlugin]['requisition_journal_'.$k]))
            {
                $this->data[InvoicesPlugin]['requisition_journal_'.$k] = $this->JournalAccount->find('first' , ['recursive' => -1 ,'conditions' => ['JournalAccount.entity_type' => $type['journal_account'] ] ])['JournalAccount']['id'] ;
            }
        }
    }
    function owner_summary($order_id = null,$order_type = null){
        if ( !check_permission(REQUISITION_VIEW) ){
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
       
        $order = $this->Requisition->getOrder($order_id,$order_type) ;
        if (!$order) {
            $this->flashMessage(__('Order not found', true));
            $this->redirect( array('action' => 'index'));
        }
        $item_model = Requisition::$requisition_order_types[$order_type]['item_source_model'];
        $this->loadModel('RequisitionItem');
        $items = [] ;
        $product_ids=[];
        foreach ( $order[$item_model] as $k => $i) {
        $product_ids[$i['product_id']]=$i['product_id'];
        }

        $this->loadModel('Product');
        $products=$this->Product->find('all',['recursive' => -1,'conditions'=>['Product.id'=>$product_ids]]);
        $productList=[];
        foreach($products as $product){
        $productList[$product['Product']['id']]='#'.($product['Product']['product_code']!=""?$product['Product']['product_code']:$product['Product']['id']).' '.$product['Product']['name'];
        }
        foreach ( $order[$item_model] as $k => $i){
            if(empty($i['Product']['track_stock'])) {
                continue;
            }
//            $oldQuantity = $items[$i['product_id']]['quantity'];
//            $newQuantity = $i['quantity'];
//            $oldUnitPrice = $items[$i['product_id']]['unit_price'];
//            $newUnitPrice = $i['unit_price'];
//            $avgUnitPrice = ($oldQuantity * $oldUnitPrice + $newQuantity * $newUnitPrice) / ($oldQuantity + $newQuantity);
            $rr = $this->RequisitionItem->find('first' , ['recursive' => 1, 'fields' => "SUM(quantity) as delivered_quantity, RequisitionItem.unit_price",'conditions'=> ['RequisitionItem.product_id'=> $i['product_id'],'Requisition.order_id' => $order_id , 'Requisition.order_type' => $order_type , 'Requisition.status' => [Requisition::STATUS_ACCEPTED,Requisition::STATUS_MODIFIED] ] ]);
            $i['delivered_quantity'] = ($rr[0]['delivered_quantity']?:0);
            // This was done to match the unit price of the Requisition tab (Summary) to the unit price that will appear in the Requisitions
            $i['unit_price'] = $rr['RequisitionItem']['unit_price'];
            $i['quantity'] += $items[$i['product_id']]['quantity'];
            $i['item']=$productList[$i['product_id']];
            $items[$i['product_id']] = $i;
        }
        $this->set('order_id' , $order_id);
        $this->set('order_type' , $order_type);
        $this->set('order' , $order);
        $this->set('items' , $items);
        $this->set('item_model' , $item_model);

        $show_price = false;

        if (check_permission(Add_New_Purchase_Orders)) {
            $show_price = true;
        }

        $this->set('show_price' , $show_price);
    }
    public function owner_create_order($ids=false){

        if(empty($ids)&&!empty($_POST['ids']))
            $ids=$_POST['ids'];
        else if(!empty($ids))
            $ids=explode(',',$ids);
        $this->loadModel('Requisition');
        $this->loadModel('Product');
        $stores = [];
        $type = null ; //by reference
        $productPriceListsById = [];
        $Data = $this->Requisition->createOrderFromRequisitions($ids,$type);
        if(isset($Data['InvoiceItem'])) {
            foreach($Data['InvoiceItem'] as $k => $item) {
                $stores[$item['store_id']] = $item['store_id'];
                $productPriceListsById[$item['product_id']] = $this->Product->getProductPriceListsById($item['product_id']);
            }
        } else if(isset($data['PurchaseOrderItem'])) {
            foreach($Data['PurchaseOrderItem'] as $k => $item) {
                $stores[$item['store_id']] = $item['store_id'];
            }
        }
        if(count($stores) > 1) {
            $this->flashMessage(__('Requisitions must be in the same store', TRUE));
            $this->redirect(['action' => 'index']);
        }
        if ( $Data == false ){
            $this->flashMessage(__('Requisitions must be manual and of the same type', TRUE));
            $this->redirect(['action' => 'index']);
        }
        $controller = $type['created_controller'];
        $this->Session->write('requisition_ids', $ids);
        $this->Session->write(strtolower($type['created']).'_type', 1);
        $this->Session->write(strtolower($type['created']).'_items', $Data);
        $this->Session->write('refresh_prices_based_price_list', true);
        $this->Session->write('productPriceListsById', array_filter($productPriceListsById));
        $this->redirect(array('controller' => $controller, 'action' => 'add','?' => ['work_order_id' => $Data[$type['created']]['work_order_id']] ));
    }

    function owner_auto_corrector() {
        (new CreateAutoRequisitionsCorrector())->correct();
    }

    public function api_delete($id = null) {
		$requisition = $this->Requisition->find("first", ["conditions" => array("Requisition.id" => $id)] );
		if(empty($requisition)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Requisition", true))));
		if($this->Requisition->delete($id)){
			$this->set("message", sprintf(__('%s has been deleted', true), __("Requisition", true)));
			$this->render("success");
			return;
		} else {
			$this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
		}
	}

    public function owner_fix_pos_per_invoice_requisitions() {
        $query = "select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select order_id from requisitions where order_type = 12 or order_type = 11);";
        $this->loadModel('PosShift');
        $posShifts = $this->PosShift->query($query);
        foreach ($posShifts as $posShift) {
            $this->Requisition->updateForPOS($posShift['pos_shifts']['id']);
        }
        $this->flashMessage(__('pos shifts fixed'.count($posShifts), TRUE), 'Sucmessage');
        return $this->redirect($this->referer());
    }

    private function handleStockRequestValidations($data , $editMode = false)
    {
        $message = '';
        if (isset($data['Requisition']['is_stock_request'])){
            $stockRequestRequisition = $data['Requisition'];
            $stockRequestId = $stockRequestRequisition['order_id'];
            if (isset($data['Requisition']['is_manufacturing_order'])){
                $stockRequestId = $data['Requisition']['stock_request_id'];
            }
            $stockRequestRequisitionItems = $data['RequisitionItem'];
            $stockRequestService = resolve(\Izam\StockRequest\Services\StockRequestService::class);
            try {
                $reqId=null;
                if ($editMode){
                     $reqId = $data['Requisition']['id'];
                }
                $isExceeded = $stockRequestService->isConvertedQtysExceededRequestedQtys($stockRequestId,$stockRequestRequisitionItems, $editMode , $reqId);
                if ($isExceeded){
                    $message = __('You cannot exceed the quantity that has already been entered in the Stock Request',true);
                }
            }catch (RequestedProductNotFoundInSourceRequest $exception){
                $message = __('You cannot select an item that is not Included in the stock request',true);
            }
            if ($message){
                CustomValidationFlash([$message]);
                return $message;
            }
        }
        return $message;
    }

    private function handleInboundManufacturingOrderMaterialsValidations($data , $editMode = false)
    {
        $message = '';
        if(ifPluginActive(MANUFACTURING_PLUGIN)){
            if ($data['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND){
                // validate requistion items qty
                foreach($data['RequisitionItem'] as $requisitionItem){
                    if($requisitionItem['quantity'] < 0){
                        $message = __('You cannot save the requisition with negative items quantity',true);
                        break;
                    }
                }

                $manufacturingOrderService = resolve(ManufacturingOrderService::class);
                try {
                    $reqId=null;
                    if ($editMode){
                        $reqId = $data['Requisition']['id'];
                    }
                    $isExceeded = $manufacturingOrderService->returnMaterialsRequisitionValidation($data['Requisition']['order_id'],$data['RequisitionItem'], $editMode , $reqId, $deleteMode);
                    if ($isExceeded){
                        $message = __('You cannot exceed the quantity that has already been calculated from the materials outbound requisitions',true);
                    }
                }catch (RequestedProductNotFoundInSourceRequest $exception){
                    $message = __('You cannot select a product that is not Included in the  Manufacturing Order and the outbound requisitions',true);
                }
                if ($message){
                    CustomValidationFlash([$message]);
                    return $message;
                }
            }
        }
        return $message;
    }

    private function onDeleteOrUpdateManufacturingOrderRequisitionValidations($data)
    {
        $message = '';
        if(ifPluginActive(MANUFACTURING_PLUGIN)){
            if (
                in_array(
                    $data['Requisition']['order_type'],
                    [
                        Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
                        Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
                        Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,
                        Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP,
                    ]
                )
            ){
                $manufacturingOrderService = resolve(ManufacturingOrderService::class);
                try {
                   $manufacturingOrderService->onDeleteOrUpdateManufacturingOrderRequisitionValidations($data['Requisition']['order_id']);
                }catch (CannotUpdateOrDeleteFinishedManufacturingOrderRequisitions $exception){
                    $message = sprintf(__('You cannot delete/update the requisition if the Manufacturing Order #%s already Finished or closed',true), "<a href='/v2/owner/entity/manufacturing_order/".$data['Requisition']['order_id']."/show'>".$data['Requisition']['order_number']."</a>");
                }
                if ($message){
                    CustomValidationFlash([$message]);
                    return $message;
                }
            }
        }
        return $message;
    }

    private function setIsStockRequest($requisition , $viewMode= false)
    {
        $this->loadModel('Store');

        $stockRequestOrderTypes =[
            Requisition::TYPE_INBOUND,
            Requisition::TYPE_OUTBOUND,
            Requisition::ORDER_TYPE_TRANSFER_REQUISITION,
        ];
        $orderType = $requisition['Requisition']['order_type'];
       if (!$viewMode){
           $requisition_store_one = $this->Store->find('first', ['conditions' => ['Store.id' =>   $requisition['Requisition']['store_id'] ]] );
           $this->set('storeOneIsActive',  $requisition_store_one['Store']['active'] == \Izam\Daftra\Common\Utils\StoreStatusUtil::ACTIVE );
           $requisition_store_two_is_active = false;
           if ($requisition['Requisition']['type'] == Requisition::TYPE_NOEFFECT){
               $requisition_store_two = $this->Store->find('first', ['conditions' => ['Store.id' =>   $requisition['Requisition']['to_store_id'] ]] );
               $requisition_store_two_is_active = $requisition_store_two['Store']['active']  == \Izam\Daftra\Common\Utils\StoreStatusUtil::ACTIVE;
           }
           $this->set('storeTwoIsActive',  $requisition_store_two_is_active);
       }
        // this is a stock request
        $this->set('is_stock_request' , $requisition['Requisition']['order_id'] != 0 &&  in_array($orderType , $stockRequestOrderTypes));
        $this->set('stock_request_id' , $requisition['Requisition']['order_id']);
    }

    private function validateProductOnHandAndAvailableStock($items, $storeId): void
    {
        $this->loadModel('Invoice');
        $availableProductStock = $this->Invoice->checkOnHandAndAvailableProductStock($items, $storeId);
        if ($availableProductStock['status']) {
            return;
        }
        $this->Requisition->validationErrors['RequisitionItem'] = $availableProductStock['messages'];
        if (IS_REST) {
            $this->cakeError('error400', ['message' => $availableProductStock['messages']]);
        }
    }

    private function handleOutboundManufacturingOrderMaterialsValidations($data , $editMode = false)
    {
        //if settings enabled don't validate
        if (\settings::getValue(MANUFACTURING_PLUGIN, 'exceeding_the_requested_quantity_in_manufacturing_order', null, false)){
            return '';
        }
        $message = '';
        if(ifPluginActive(MANUFACTURING_PLUGIN)){
            if ($data['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
                $manufacturingOrderService = resolve(ManufacturingOrderService::class);
                try {
                    $manufacturingOrderService->validateOutboundMaterialRequisitionData($data['Requisition']['order_id'],$data , $editMode);
                }catch (CannotSelectNewProduct $exception){
                    $message = __('You cannot select a product that is not Included in the  Manufacturing Order',true);
                }catch (CannotExceedProductQuantity $exception){
                    $message = __('You cannot exceed the requested quantity that has already been entered in the Manufacturing Order',true);
                }
            }
        }
        return $message;
    }

    private function handleAddNewManufactureOrderMaterial(array $data)
    {
        if (\settings::getValue(MANUFACTURING_PLUGIN, 'exceeding_the_requested_quantity_in_manufacturing_order', null, false)){
            $manufacturingOrderService = resolve(ManufacturingOrderService::class);
            $manufacturingOrderService->addNewManufactureOrderMaterial($data['Requisition']['order_id'],$data);
        }
    }

    private function checkRequsitionItemsForManufacturingOrders(array $data)
    {
        if (isset($data['Requisition']['order_type']) && $data['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
            foreach ($data['RequisitionItem'] as &$item){
                if (empty($item['requested_quantity'])){
                    $item['requested_quantity']="0";
                }
            }
        }
        return $data;
    }
    private function handleStockRequestRelation($data, $requisitionId)
    {
        if (isset($this->data['Requisition']['is_stock_request']) || isset($this->data['Requisition']['is_manufacturing_order'])){
            try {
                $stockRequestId =  $data['Requisition']['order_id'];
                if (isset($this->data['Requisition']['stock_request_id'])){
                    $stockRequestId = $this->data['Requisition']['stock_request_id'];
                }
                $this->loadModel('StockRequestRelation');
                $stockRequestRelation['StockRequestRelation']['related_model'] = 'App\\Models\\Requisition';
                $stockRequestRelation['StockRequestRelation']['related_model_id'] = $requisitionId;
                $stockRequestRelation['StockRequestRelation']['stock_request_id'] = $stockRequestId;
                $this->StockRequestRelation->save($stockRequestRelation);
                return true;
            }catch (Throwable $throwable){
                return false;
            }
        }
    }

    private function handleDeleteRequisition($id , $requisition)
    {
        $matchingTypes = [
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
        ];
        $stockRequestOrderTypes =[
            Requisition::TYPE_INBOUND,
            Requisition::TYPE_OUTBOUND,
            Requisition::ORDER_TYPE_TRANSFER_REQUISITION,
        ];
        $orderType = $requisition['Requisition']['order_type'];
        $orderId = $requisition['Requisition']['order_id'];
        if (isset($orderId) && in_array($orderType , $matchingTypes)){
            $this->logAddingOutboundRequisitionToManufacturingOrder($orderId , null , null , $requisition['Requisition']);
        }
        if (
            in_array($orderType, $matchingTypes) || ($orderId != 0 &&  in_array($orderType , $stockRequestOrderTypes))
        ){
            $this->loadModel('StockRequestRelation');
            $conditions = [
                'StockRequestRelation.related_model'=>'App\\Models\\Requisition',
                'StockRequestRelation.related_model_id'=>$id,
            ];
            $this->StockRequestRelation->deleteAll($conditions);
        }
    }

    private function checkOrderTypeHasCustomRedirect($requisition)
    {
        $order_type = (int) $requisition['Requisition']['order_type'];
        $order_id = $requisition['Requisition']['order_id'];
        return match ($order_type) {
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP => '/v2/owner/entity/manufacturing_order/' . $order_id . '/show',
            default => null,
        };
    }

    private function checkAndHandleCustomRedirects($requisitions)
    {
        $orderTypeHasCustomRedirect = $this->checkOrderTypeHasCustomRedirect($requisitions[0]);
        if ($orderTypeHasCustomRedirect){
            //for deleting from inside iframe
            if (isset($_POST['is_iframe'])){
                return $this->redirect($orderTypeHasCustomRedirect);
            }
            $filters = [];
            $requisitionFilters = [
                'branch_id',
                "number",
                "order_type",
                "order_number",
                "store_id",
                "work_order_id",
                "status",
                "staff_id",
                "product_id",
                "date_selector",
                "date_from",
                "date_to"
            ];
            $filtersStored = $this->Session->read('Requisition_Filter');
            foreach ($filtersStored as $key => $item){
                if (in_array($key , $requisitionFilters)){
                    $filters[$key] = $item;
                }
            }
            if (!count($filters)){
                $filters['reset']=1;
            }
            if (isset($_POST['from_iframe'])){
                $filters=['reset'=>1];
            }
            //deleting from view clicked from iframe
            return $this->redirect(array('action' => 'index', '?' => $filters ));
        }
    }

    private function validateIsManufacturingOrder($requisition)
    {
        if (!ifPluginActive(MANUFACTURING_PLUGIN)) return false;
        $ignoreUpdateStatusArray = [
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL
        ];
        if (isset($requisition["Requisition"]["order_type"]) && in_array($requisition["Requisition"]["order_type"], $ignoreUpdateStatusArray))  return true;
        return false;
    }
    private function logAddingOutboundRequisitionToManufacturingOrder($moId , $rId ,$newData , $oldData = [])
    {
        $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::REQUISITION);
        $activityLogRequestCreator = new EntityActivityLogRequestsCreator();

        unset($oldData['stock_request_id']);
        unset($oldData['is_manufacturing_order']);

        unset($newData['stock_request_id']);
        unset($newData['is_manufacturing_order']);
        $requests = $activityLogRequestCreator->create($st, $newData,  $oldData , [new ActivityLogRelationRequest($moId ,EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY)]);
        $activityLogService =  new \App\Services\ActivityLogService();
        $url = null;
        if ($rId) $url = '/owner/requisitions/view/'.$rId;
        foreach ($requests as $requestObj) {
            $requestObj->setUrl($url);
            $activityLogService->addActivity($requestObj);
        }
    }

    function owner_export($conditions_key = null, $report_template_id = false, $quick_report = false) {
        if (!check_permission([Invoices_View_All_Invoices, Invoices_View_Invoices_Details, Invoices_Edit_his_own_Invoices])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->set('title_for_layout',  __('Export Requisitions', true));
        include(dirname(__FILE__) . DS . 'requisition_export.php');
    }

    private function setJournalAccountForRequisitionItem($requisitionItems) {

        $this->loadModel('JournalCat');
        $requisitionItemsAccount = [];

        foreach ($requisitionItems as $idx => $requisitionItem) {

            $accountId = JournalAccountHelper::getSelectedAccountId($requisitionItem);

            if (!empty($accountId)) {

                $account = JournalAccountHelper::getJournalAccountById($accountId);
                $categoryOptions = JournalAccountHelper::getJournalCategoryOptions();

                if (!empty($account)) {
                    $label = JournalAccountHelper::buildOptgroupLabel(
                        $account['JournalAccount']['parent_cat_ids'],
                        $categoryOptions
                    );

                    $formatted = JournalAccountHelper::formatJournalAccountOption($account, $label);
        
                    $requisitionItemsAccount[$requisitionItem['id']] = JournalAccountHelper::buildOptgroups($formatted);

                }
            }

        }

        $this->set(compact('requisitionItemsAccount'));

    }

    private function setCostCentersForRequisitionItem($requisitionItems) { 
        $requisitionItemsCostCenters = [];
        $this->loadModel('CostCenter');
        foreach ($requisitionItems as $idx => $invoiceItem) {
            $requisitionItemsCostCenters[$invoiceItem['id']] = ['value' => $invoiceItem['cost_center_id'], 'text' => $this->CostCenter->find('first',['fields' => 'name','recursive' => 0, 'conditions' => ['id' => $invoiceItem['cost_center_id']]])['CostCenter']['name'] ];

        }

        $this->set(compact('requisitionItemsCostCenters'));
    }



}
