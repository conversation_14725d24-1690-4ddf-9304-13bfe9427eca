<?php

class QRController extends AppController
{
    var $uses = [];

    public function client_show()
    {
        $this->set('title_for_layout',  sprintf(__('%s Mobile Apps', true), Site_Full_name_NoSpace));

        if (!defined('QR_ENABLED') || !QR_ENABLED) {
            return redirect('/');
        }

        $this->set('snippetName', $this->get_snippet(
            Domain_Name_Only . '_qr', '', false
        ));
    }
}
