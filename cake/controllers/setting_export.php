<?php
set_time_limit(3600);
ini_set('memory_limit', '20G');

//echo getPaymentStatuses ( 1 );
$fields_list = array(
    'plugin_id' => array('title' => "plugin id", 'field' => 'Setting.plugin_id'),
    'key' => array('title' => "key", 'field' => 'Setting.key'),
    'value' => array('title' => "value", 'field' => 'Setting.value')
);




$count = $this->Setting->find('count', ['recursive' => -1]);
if ($count > 5000) {
    $this->flashMessage(__("You can't export more than 5000 record, Please change filters to match limit.", TRUE));
    return $this->redirect("/owner/settings/index2");
}

$alldata = $this->Setting->find('all', ['recursive' => -1]);
$itemColumns = [];
$i = 0;


$rows = [];
foreach ($fields_list as $v) {
    $rows[1][$v['title']] .= __($v['title'], true);
}
//Putting data for CSV file
$i = 2;
App::import('Vendor', 'PlaceHolder');
foreach ($alldata as $d) {
    $placeholders = [];
    foreach ($fields_list as $k => $v) {
        $field = explode('.',  $v['field']);
        $rows[$i][$v['title']] = $d[$field[0]][$field[1]];
    }
    $temp = [];
    foreach ($rows[1] as $key => $value) {
        if (!array_key_exists($key, $rows[$i])) {
            $temp[$key] = '';
        } else {
            $temp[$key] = $rows[$i][$key];
        }
    }
    $rows[$i] = $temp;
    $i++;
}


header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . 'data.csv' . '";');
$handle = fopen('php://output', 'w');

foreach ($rows as $row) {
    $csvLine = [];
    foreach ($row as $field) {
        if (strpos($field, $delimiter) !== false || strpos($field, '"') !== false || strpos($field, "\n") !== false) {
            $field = '"' . str_replace('"', '""', $field) . '"';
        }
        $csvLine[] = $field;
    }
    fwrite($handle, implode(';', $csvLine) . "\n");
}

fclose($handle);



    
die;
$this->set('fields_list', $fields_list);
