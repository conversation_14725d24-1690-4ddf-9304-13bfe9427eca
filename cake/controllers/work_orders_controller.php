<?php

use App\Helpers\UsersHelper;
use App\Services\WorkOrder\WorkOrderDeleterFactory;
use App\Services\WorkOrder\WorkOrderDeleterInterface;
use Izam\Daftra\Common\DTO\TabAction;
use Izam\Daftra\Common\Factories\SocialMediaLinkCreatorFactory;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\DynamicPermissions\DynamicPermissionService;
use App\Services\WorkOrder\WorkOrderTransactionAssigner;
use Izam\Template\TemplateRepository;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Template\Utils\TemplateTypeUtil;

App::import('Vendor', 'notification_2');
App::import('Vendor', 'settings');

class WorkOrdersController extends AppController {

    var $name = 'WorkOrders';
    var $components = array('Cookie', 'Email', 'SysEmails');
//    var $validate_schema;

   
    function beforeFilter() {
        $this->js_lang_labels[] = __('Item Tags', true);
        parent::beforeFilter();
        if ( !ifPluginActive(WorkOrderPlugin) && !ifPluginActive(WorkflowPlugin) ) {
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
    }
    
    function _createWO($id = null) {
        $view_work_order = $this->WorkOrder->generate_work_order_html ( $id,"",true ) ;
		debug ( $view_work_order ) ;
        $view = new View($this, true);
		$view->params['ext'] = 'pdf';
        $view->params['url']['ext'] = 'pdf';
		$view->set('save', true);
        $view->set('view_work_order' , $view_work_order ) ;
        $view->render('pdf/owner_view', '');
        return $filename = $view->invoiceFileName;
    }
    /** 
     * Listing all the work orders
     */
    function owner_index ( ) 
    {

        $conditions = parent::_filter_params();
        $site = getAuthOwner();
//        $this->set('staff', $site);
        if (!check_permission([VIEW_HIS_WORK_ORDERS,VIEW_ALL_WORK_ORDERS])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel ( 'Post');
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel ( 'Staff');
        
        $this->set('staffs', $this->Staff->getList());
        $invoice_statuses = $this->FollowUpStatus->getList(Post::WORK_ORDER_TYPE, true);
        $this->set('invoice_statuses', $invoice_statuses);
        $invoice_status_colors = $this->FollowUpStatus->getList(Post::WORK_ORDER_TYPE, true, array(), 'color');
        $this->set('colors_options', $this->FollowUpStatus->colors);
        $this->set('status_colors', $invoice_status_colors);

        $statuses = $this->WorkOrder->get_status_list ( );
        // warning suppress
		if(array_key_exists('WorkOrder.follow_up_status_id',$conditions) && strpos($conditions['WorkOrder.follow_up_status_id'], 'sys-general-') !== false){
			$conditions['WorkOrder.follow_up_status_id'] = str_replace('sys-general-', '',$conditions['WorkOrder.follow_up_status_id'] );
			$conditions['WorkOrder.status'] =$conditions['WorkOrder.follow_up_status_id'];
			unset($conditions['WorkOrder.follow_up_status_id']);
		}
        if (ifPluginActive(FollowupPlugin)) {
            $this->loadModel('FollowUpStatus');
            $this->loadModel('Post');
            $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::WORK_ORDER_TYPE);

			foreach($statuses as $k => $v){
				
				$general_status[$k][] = [
					'status' => $k,
					'name' => $v,
					'value' => 'sys-general-' . $k,
					'data-content' => "<span style='background: white; color: black;'>$v</span>"
				];
			}
			
			foreach($FollowUpStatus as $k => $v){
				$general_status[$v['status']][] = $v;
			}
			foreach($general_status as $group => $group_data)
			{
				foreach($group_data as $k => $data)
				$f_groups[] = $data;
			}
            $this->set('FollowUpStatuses', $f_groups);
        }
        if (check_permission(VIEW_HIS_WORK_ORDERS) && !check_permission(VIEW_ALL_WORK_ORDERS)){
            $conditions['WorkOrder.id'] = array_keys ($this->WorkOrder->get_work_orders ($site['staff_id'] ) );
        }else if (!check_permission([VIEW_HIS_WORK_ORDERS,VIEW_ALL_WORK_ORDERS])) {
            $conditions['WorkOrder.staff_id'] = -1;//gets nothing
        }
        $this->set ( 'statuses' , $statuses   );
        $this->set ( 'colored_statuses' , $this->WorkOrder->get_colored_status_list ( )  );
		
		if(isset($_GET['status']) && $_GET['status'] != -1){
			$conditions['WorkOrder.status'] = $_GET['status'];
		}

		if(count($conditions) < 1 && !isset($_GET['status'])){
			$conditions['WorkOrder.status'] = WorkOrder::STATUS_OPEN;
		}
		
		$this->paginate['WorkOrder']['order'] = ['WorkOrder.id' => 'DESC'];
		$this->WorkOrder->bindModel([
			'hasOne' => [
				'FirstFollowUpReminder' => ['className' => 'FollowUpReminder', 'foreignKey' => 'item_id','limit' => 1 , 'order' => ['FirstFollowUpReminder.date ASC'],'conditions' => ['FirstFollowUpReminder.status' => 0, 'FirstFollowUpReminder.item_type' => 8] ]
			]
		],false);
		$this->paginate['WorkOrder']['group'] = ['WorkOrder.id'];
		if(isset($conditions['WorkOrder.item_staff_id'])){
			$staff_id = $conditions['WorkOrder.item_staff_id'];
			$this->loadModel('ItemStaff');
			$WO_ids = $this->ItemStaff->find('list',[ 'fields' => ['id', 'item_id'], 'conditions' => ['ItemStaff.staff_id' => $staff_id, 'ItemStaff.item_type' => 8 ] ]);
			$conditions['WorkOrder.id'] = $WO_ids;
			unset($conditions['WorkOrder.item_staff_id']);
		}
         $conditions['WorkOrder.workflow_type_id'] = null;
//        $this->WorkOrder->Behaviors->disable('customform');
        $work_orders = $this->paginate( 'WorkOrder' , $conditions);
//        $this->WorkOrder->Behaviors->enable('customform');
		if($conditions['WorkOrder.status']){
			$this->params['url']['follow_up_status_id'] = 'sys-general-'. $conditions['WorkOrder.status'];
		}
        // warning suppress
        $staff_id = $staff_id ?? '' ;
		$this->params['url']['item_staff_id'] = $staff_id;
        $this->set ( 'work_orders' , $work_orders ) ;
		$work_order_ids = [];
		foreach($work_orders as $K => $v)
		{
			$work_order_ids[] = $v['WorkOrder']['id'];
		}
		$this->loadModel('FollowUpAction');
		$follow_up_actions = $this->FollowUpAction->find('list');
		$this->set('follow_up_actions', $follow_up_actions);
		// NOTE: "DISTINCT `WorkOrder`.`id`" -> used here because if work_order has many custom_data they will counted.
        $count_conditions =  [];
        $count_conditions['WorkOrder.workflow_type_id'] = null;
		$this->set('open_count', $this->WorkOrder->find('count', ['recursive' => -1, 'fields' => 'DISTINCT `WorkOrder`.`id`', 'conditions' => array_merge(['WorkOrder.status' => WorkOrder::STATUS_OPEN], $count_conditions)]));
		$this->set('closed_count', $this->WorkOrder->find('count', ['recursive' => -1, 'fields' => 'DISTINCT `WorkOrder`.`id`', 'conditions' => array_merge(['WorkOrder.status' => WorkOrder::STATUS_CLOSED], $count_conditions)]));
		$this->set('all_count', $this->WorkOrder->find('count', ['recursive' => -1, 'fields' => 'DISTINCT `WorkOrder`.`id`', 'conditions' => $count_conditions]));
		$this->loadModel('ItemsTag');
		$this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_WORKORDER);
		$work_order_tags = $this->ItemsTag->get_items_tags($work_order_ids,ItemsTag::TAG_ITEM_TYPE_WORKORDER);
		$this->set('tags',$work_order_tags);
        $this->pageTitle=__("Work Orders" , true );
        $this->setup_nav_data($work_orders);
		if(IS_REST){
			$this->set('rest_items', $work_orders);
			$this->set('rest_model_name', "WorkOrder");
			$this->render("index");
		}
    }
    private function _settings ( )
    {
        $this->loadModel  ('Invoice');
        $this->loadModel  ('Site');
        $ajax_clients = false;
        $clients_count = $this->WorkOrder->Client->getClientCount();
        if ($clients_count < AJAX_CLIENT_COUNT) {
            $this->set('clients', $this->WorkOrder->Client->getClientsList());
        } else {
            if (!empty($this->data['WorkOrder']['client_id']))
                $this->set('clients', $this->WorkOrder->Client->getClientsList(0, array('Client.id' => $this->data['WorkOrder']['client_id'])));
            $ajax_clients = true;
        }
//        $this->set('client_type',settings::getValue(ClientsPlugin, "client_type"));
        $this->set('ajax_clients', $ajax_clients);
        $this->loadModel('Site');
        $addClientLimit = $this->Site->check_add_client_limit();
        $this->set(compact('addClientLimit'));
        $this->set('client_type',settings::getValue(ClientsPlugin, "client_type"));
        $this->loadModel('Country');
        $this->set('countries', $this->Country->getCountryListName());
        $assignedStaff = [];
        if(isset($this->data['ItemStaff'])) {
            foreach ($this->data['ItemStaff'] as $itemStaff) {
                $assignedStaff[] = $itemStaff['staff_id'];
            }
        }
        $this->set('staff_ids', UsersHelper::getInstance()->setSelectedStaff($assignedStaff)->getList(true, [], true, true));
        $this->set ( 'budget_currencies' , $this->Currency->getCurrencyList ( [] , true ) ) ;
        $this->set ( 'statuses' , $this->WorkOrder->get_status_list ( )  );
        $invoicing_method = settings::getValue(InvoicesPlugin, 'invoicing_method');
        $this->set('default_invoice_method', $invoicing_method);
    }
    /**
     * Adding new work order + viewing the add view 
     */
    function owner_add ( ) {
        if ( !check_permission(ADD_NEW_WORK_ORDERS))
        {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
		App::import('Vendor', 'AutoNumber');
        \AutoNumber::set_validate(\AutoNumber::TYPE_WORK_ORDER);
        $this->loadModel ( 'WorkOrder');
        $this->loadModel ( 'Invoice');
        $this->loadModel ( 'Client');
        $this->loadModel ( 'Staff');
        $this->loadModel ( 'Currency');
        $this->loadModel ( 'Post');
        $this->loadModel ( 'ItemStaff');
//        $this->loadModel ( 'WorkOrdersStaff');
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'ClientAppointment');
        $site = getAuthOwner();
        
        if (!empty($this->data)) {

            //set number if new client and he didn't change or number is empty
            if(empty($this->data['WorkOrder']['number'])||(empty($this->data['WorkOrder']['id'])&&$this->data['WorkOrder']['number']==$this->data['WorkOrder']['hidden_number'])){
                $this->data['WorkOrder']['hidden_number'] = $this->data['WorkOrder']['number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_WORK_ORDER);
                $generated_number = true;
            }
            if(!empty($this->data['WorkOrder']['start_date'])) {
                $this->data['WorkOrder']['start_date'] = $this->WorkOrder->formatDate($this->data['WorkOrder']['start_date']);
            }else{
                $this->data['WorkOrder']['start_date']=date("Y-m-d");
            }

            if ( !empty( $this->data['WorkOrder']['delivery_date']) )
            {
                $this->data['WorkOrder']['delivery_date'] = $this->WorkOrder->formatDate( $this->data['WorkOrder']['delivery_date']);
            }else {
                $this->data['WorkOrder']['delivery_date'] = null ; 
            }
            $this->data['WorkOrder']['status'] = WorkOrder::STATUS_OPEN;
          
            $staff_ids = $this->data['WorkOrder']['staff_id'];
            //assigning staff_id with created ..... after getting the assigned staff_members 
            
            $this->data['WorkOrder']['staff_id'] = getAuthOwner('staff_id');
			if(empty($this->data['WorkOrder']['number'])||($this->data['WorkOrder']['number']==$this->data['WorkOrder']['hidden_number'])) $this->data['WorkOrder']['hidden_number'] = $this->data['WorkOrder']['number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_WORK_ORDER);
            $this->WorkOrder->create();
            
            $this->WorkOrder->set($this->data);
            $passed_validation = ($this->WorkOrder->validates()  );
            if ( !empty ( $this->data['WorkOrder']['is_draft']))
            {
                $this->WorkOrder->ignoreCustomValidation = true;
                $passed_validation = true ; 
                $isFound = $this->WorkOrder->find('list' , ['recursive' => -1 , 'conditions' => ['WorkOrder.number' =>$this->data['WorkOrder']['number'] ] ]);
                if ( $isFound ){
                    $found_errors['number'] = __('Work Order number already exists', true);
                }
                $this->data['WorkOrder']['status'] = WorkOrder::STATUS_DRAFT;
                unset ( $this->data['WorkOrder']['is_draft'] );
            }
            if ( $passed_validation && $this->WorkOrder->save ( $this->data ,(!$passed_validation) ) ) {
                if(!empty($generated_number)) $this->data['WorkOrder']['number'] = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_WORK_ORDER);
				else \AutoNumber::update_last_from_number($this->data['WorkOrder']['number'], \AutoNumber::TYPE_WORK_ORDER);
				
				$trigger = NotificationV2::get_trigger();
				NotificationV2::add_notification(
							NotificationV2::NOTI_UPDATE_WORK_ORDER,
							$this->WorkOrder->id,
							$staff_ids,
							$user_type = NotificationV2::NOTI_USER_TYPE_STAFF,
							$trigger['trigger_type'],
							$trigger['trigger_id'],
							NotificationV2::NOTI_ACTION_ADD,
							['status' => $this->WorkOrder->get_status_list()[$this->data['WorkOrder']['status']]  ]
							);
                $work_order_id = $this->WorkOrder->id;
                if ( !empty ( $this->data['WorkOrder']['delivery_date'] ) ){
                    $this->WorkOrder->createDeliveryAppointment ($work_order_id  );
                }
                
                $this->loadModel('Post');
                $this->ItemStaff->assign_staff_members ($staff_ids , $work_order_id , ItemStaff::WORK_ORDER_ITEM_TYPE) ;

                App::import('vendor','AutoReminder',['file'=>'AutoReminder/autoload.php']);
                $this->loadModel('AutoReminderRule');
                $this->loadModel('ReminderMessage');

                $rules = $this->AutoReminderRule->find('all',["recursive"=>-1,"conditions"=>["send_on" => 1,"entity_type" => "workOrders"]]);
                foreach ($rules as $rule) {
                    if($rule['AutoReminderRule']['timing_field'] == "modified") continue;
                    $autoReminderRule = \AutoReminder\AutoReminderFactory::create($rule);
                    $autoReminderRule->addMessages($autoReminderRule->findMatches());
                }

                
//                if ( !empty ( $this->data['WorkOrder']['delivery_date']))
//                {
//                    //add appointment to delivery
//                    $this->FollowUpReminder->save (['FollowUpReminder' => [
//                        'item_type'=>Post::CLIENT_TYPE,
//                        'item_id'=>$this->data['WorkOrder']['client_id'],
//                        'staff_id'=>getAuthOwner('staff_id'),
//                        'date'=>$this->data['WorkOrder']['delivery_date'] ,
//                        'status'=>ClientAppointment::Status_Scheduled ,
//                        'post_id'=>0 ,
//                        'recurring_appointment_id'=>NULL ,
//                        'body'=>"Appointment for ".$this->data['WorkOrder']['title'] ,
//                        ]] ) ;
//                    
//                    $this->WorkOrder->saveField ('delivery_appointment_id'  , $this->FollowUpReminder->id);
//                }
                $this->add_actionline(ACTION_ADD_WORK_ORDER, array('secondary_id' => $this->WorkOrder->id, 'primary_id' => $this->data['WorkOrder']['client_id'], 'param2' => $this->data['WorkOrder']['title'], 'param3' => $this->data['WorkOrder']['number']));
				if(IS_REST){
					$this->set('id', $this->WorkOrder->id);
					$this->render('created');
					return;
				}

                $this->flashMessage(sprintf(__('%s has been saved', true), __('Work Order', true)), 'Sucmessage');
                if(isset($this->WorkOrder->CustomModelError) && $this->WorkOrder->CustomModelError==false){
                    $this->flashMessage(__("Custom Fields can not be saved", true), 'Errormessage', 'secondaryMessage');
                }
                    $this->redirect ( Router::url(['action' => 'view' ,$work_order_id])) ;
            }else {
                if ( !empty ($this->data['WorkOrder']['delivery_date']) ){
                    $this->data['WorkOrder']['delivery_date'] = format_date ( $this->data['WorkOrder']['delivery_date']);
                }
                if ( !empty ($this->data['WorkOrder']['start_date']) ){
                    $this->data['WorkOrder']['start_date'] = format_date ( $this->data['WorkOrder']['start_date']);
                }
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->WorkOrder->validationErrors]);
                if ( !empty($found_errors)){
                    $this->WorkOrder->validationErrors['number'] = $found_errors['number'] ; 
                }
                $this->WorkOrder->validationErrors = array_merge( $this->WorkOrder->validationErrors);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Work Order', true)));
            }
        }else {
           if ( !empty($this->params['url']['clone_wo_id']))
           {
               $this->data['WorkOrder'] = $this->WorkOrder->find('first' , ['conditions' => ['WorkOrder.id' => $this->params['url']['clone_wo_id']] ])['WorkOrder'];
               unset($this->data['WorkOrder']['id']);
           }
           $this->data['WorkOrder']['hidden_number'] = $this->data['WorkOrder']['number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_WORK_ORDER);
            if (isset($this->params['url']['client_id']) && !empty($this->params['url']['client_id']) && is_numeric($this->params['url']['client_id'])) {
                $this->data['WorkOrder']['client_id'] = $_GET['client_id'];
            }
            $this->set ( 'selected_staffs' , $this->ItemStaff->find ( 'list' , ['fields'=> ['staff_id']  ,   'conditions' => ['item_id'=> $id /* warning suppress */ ?? '' , 'item_type' => Post::WORK_ORDER_TYPE ] ]) ) ;
        }
        
        
        $this->_settings() ;
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('WorkOrder'));

        $this->set('title_for_layout',  sprintf(__('Add %s', true) , __("Work Order",true)));

    }
    /**
     * Editing existing work order + viewing the add view 
     * @param int $id Identifier of the work group 
     */
    function owner_edit ($id = null  ) {
        
        $site = getAuthOwner();
        $work_order = $this->WorkOrder->findById (  $id ) ;
//		dd($work_order);
        if (!$this->WorkOrder->editable ( $work_order)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if ( empty ( $work_order ) ) {
			if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s is not found",true),__("Work Order",true))));
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect ( 'index'); die ;
        }
        if ( $work_order['WorkOrder']['status'] == WorkOrder::STATUS_CLOSED ){
			if(IS_REST) $this->cakeError('error400', ["message"=>__("Work Order is closed, you can't edit",true)]);
            $this->flashMessage(__("Work Order is closed, you can't edit",true) ) ;
            $this->redirect ( Router::url(['action' => 'view' , $id]) ); die ;
        }
        $this->loadModel ( 'Invoice');
        $this->loadModel ( 'Client');
//        $this->loadModel ( 'WorkOrdersStaff');
        $this->loadModel ( 'ItemStaff');
        $this->loadModel ( 'Staff');
        $this->loadModel ( 'Currency');
        $this->loadModel ( 'Post');
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'ClientAppointment');
        App::import('Vendor', 'AutoNumber');
        if(IS_REST) $this->data["Client"]["id"] = $id;
		
        if (!empty($this->data)) {
            $this->data['WorkOrder']['start_date'] = $this->WorkOrder->formatDate( $this->data['WorkOrder']['start_date']);
            if ( !empty( $this->data['WorkOrder']['delivery_date']) )
            {
                $this->data['WorkOrder']['delivery_date'] = $this->WorkOrder->formatDate( $this->data['WorkOrder']['delivery_date']);
            }else {
                $this->data['WorkOrder']['delivery_date'] = null ; 
            }
           
			
            $staff_ids = $this->data['WorkOrder']['staff_id'];
			$current_assigned_staff = $this->ItemStaff->find('list',array('fields' => 'staff_id','conditions' => array( 'ItemStaff.item_id' => $id,'ItemStaff.item_type' => Post::WORK_ORDER_TYPE )));
			$diff = array_diff(array_values((array)$current_assigned_staff), array_values((array)$staff_ids));
			NotificationV2::delete_post_notification($id, Post::WORK_ORDER_TYPE, $diff);
			if(empty($this->data['WorkOrder']['number'])) {
				$this->data['WorkOrder']['number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_WORK_ORDER);
				$generated = true;
			}
            unset ($this->data['WorkOrder']['staff_id'] ) ;
            if ( !empty ( $this->data['WorkOrder']['is_draft']))
            {
                $this->WorkOrder->ignoreCustomValidation = true;
                $passed_validation = true ; 
                $this->data['WorkOrder']['status'] = WorkOrder::STATUS_DRAFT;
            }else {
                $this->data['WorkOrder']['status'] = WorkOrder::STATUS_OPEN;
            }
            $this->WorkOrder->set($this->data);
            $passed_validation = ($this->WorkOrder->validates()  );
            if ( !empty ( $this->data['WorkOrder']['is_draft']))
            {
                $passed_validation = true ; 
                unset ( $this->data['WorkOrder']['is_draft'] );
            }
//            $this->WorkOrder->set ( $this->data ) ; 
            if ($passed_validation && $this->WorkOrder->save ( $this->data,(!$passed_validation))){
				if(!empty($generated)) \AutoNumber::update_auto_serial(\AutoNumber::TYPE_WORK_ORDER);
				elseif($this->data['WorkOrder']['hidden_number']!=$this->data['WorkOrder']['number']) \AutoNumber::update_last_from_number($this->data['WorkOrder']['number'], \AutoNumber::TYPE_WORK_ORDER);
				App::import('Vendor', 'notification_2');
				$trigger = NotificationV2::get_trigger();
				NotificationV2::add_notification(
							NotificationV2::NOTI_UPDATE_WORK_ORDER,
							$id,
							$staff_ids,
							$user_type = NotificationV2::NOTI_USER_TYPE_STAFF,
							$trigger['trigger_type'],
							$trigger['trigger_id'],
							NotificationV2::NOTI_ACTION_UPDATE,
							['status' => $this->WorkOrder->get_status_list()[$work_order['WorkOrder']['status']]  ]
							);
                $this->WorkOrder->updateDeliveryAppointment ( $this->WorkOrder->id ) ;
                $this->loadModel ( 'ItemStaff');
                $this->ItemStaff->assign_staff_members ($staff_ids , $this->WorkOrder->id , ItemStaff::WORK_ORDER_ITEM_TYPE) ;

                App::import('vendor','AutoReminder',['file'=>'AutoReminder/autoload.php']);
                $this->loadModel('AutoReminderRule');
                $this->loadModel('ReminderMessage');

                $rules = $this->AutoReminderRule->find('all',["recursive"=>-1,"conditions"=>["send_on" => 1,"entity_type" => "workOrders"]]);
                foreach ($rules as $rule) {
                    $autoReminderRule = \AutoReminder\AutoReminderFactory::create($rule);
                    $autoReminderRule->addMessages($autoReminderRule->findMatches());
                }
              
//                if ( !empty ( $this->data['WorkOrder']['delivery_date']))
//                {
//                    if (empty ( $work_order['WorkOrder']['delivery_appointment_id'] )  )
//                    {
//                        //add appointment to delivery
//                        $this->FollowUpReminder->save (['FollowUpReminder' => [
//                            'item_type'=>Post::CLIENT_TYPE,
//                            'item_id'=>$this->data['WorkOrder']['client_id'],
//                            'staff_id'=>getAuthOwner('staff_id'),
//                            'date'=>$this->data['WorkOrder']['delivery_date'] ,
//                            'status'=>ClientAppointment::Status_Scheduled ,
//                            'post_id'=>0 ,
//                            'recurring_appointment_id'=>NULL ,
//                            'body'=>"Appointment for ".$this->data['WorkOrder']['title'] ,
//                            ]] ) ;
//
//                        $this->WorkOrder->saveField ('delivery_appointment_id'  , $this->FollowUpReminder->id);
//                    }else {
//                        //edit appointment to delivery
//                        $this->FollowUpReminder->id = $work_order['WorkOrder']['delivery_appointment_id']  ; 
//                        $this->FollowUpReminder->saveField ('date',$this->data['WorkOrder']['delivery_date']);
//                    }
//                    
//                }

                $this->add_actionline(ACTION_EDIT_WORK_ORDER, array('secondary_id' => $id, 'primary_id' => $this->data['WorkOrder']['client_id'], 'param2' => $this->data['WorkOrder']['title'], 'param3' => $this->data['WorkOrder']['number']));
				if(IS_REST){
					$this->render('success');
					return;
				}

                $this->flashMessage(sprintf(__('%s has been saved', true), __('Work Order', true)), 'Sucmessage');
                    $this->redirect ( Router::url(['action' => 'view' ,$id])) ;
            }else {
				if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->WorkOrder->validationErrors]);
                if ( !empty ($this->data['WorkOrder']['delivery_date']) ){
                    $this->data['WorkOrder']['delivery_date'] = format_date ( $this->data['WorkOrder']['delivery_date']);
                }
                if ( !empty ($this->data['WorkOrder']['start_date']) ){
                    $this->data['WorkOrder']['start_date'] = format_date ( $this->data['WorkOrder']['start_date']);
                }
                
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Work Order', true)));
                if(isset($this->WorkOrder->validationErrors['CustomModel'])) {
                  $messages = implode('<br />', array_values($this->WorkOrder->validationErrors['CustomModel']));
                  $this->flashMessage($messages);
                }
                $this->set ( 'selected_staffs' , $this->ItemStaff->find ( 'list' , ['fields'=> ['staff_id']  ,   'conditions' => ['item_id'=> $id,'item_type'=> Post::WORK_ORDER_TYPE ] ]) ) ;
                $this->data = $work_order;
                $this->data['WorkOrder']['hidden_number']=$this->data['WorkOrder']['number'];
            }
            
        }else {
            $this->set ( 'selected_staffs' , $this->ItemStaff->find ( 'list' , ['fields'=> ['staff_id']  ,   'conditions' => ['item_id'=> $id,'item_type'=> Post::WORK_ORDER_TYPE ] ]) ) ;
			$this->data = $work_order;
			$this->data['WorkOrder']['hidden_number']=$this->data['WorkOrder']['number'];
        }
        $work_order['WorkOrder']['start_date'] = format_date ( $work_order['WorkOrder']['start_date']);
        if ( !empty ($work_order['WorkOrder']['delivery_date'] ))
        {
            $work_order['WorkOrder']['delivery_date'] = format_date ( $work_order['WorkOrder']['delivery_date']);
        }
        
        $this->loadModel('Country');
        
        
        $this->_settings() ;
       
        $this->set('title_for_layout',  sprintf(__('Edit %s', true) , __("Work Order",true))." #".$work_order['WorkOrder']['number']);
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('WorkOrder'));
        $this->render('owner_add');
    }
    /**
     * Deleting work orders
     * @param int $id Identifier of the work group 
     */
    function owner_delete($id = null) {
        $deleterFactory = new WorkOrderDeleterFactory();
        
        $deleter = $deleterFactory->create(EntityKeyTypesUtil::WORK_ORDER);
        $deleter->setRedirectUrl(Router::url(['action' => 'index']));
        $deleter->setLabel('Work order');
        
        $this->delete_work_order($deleter, $id);
    }

    public function owner_delete_workflow($entityKey, $id)
    {
        if (!DynamicPermissionService::isEmployeeHavePermission(getAuthOwner('staff_id'), 'work_order', $entityKey, 'delete'))
        {
            $this->flashMessage(__('You are not allowed to view this page', true));
            $this->redirect('/v2/owner/entity/workflow/' . $entityKey . '/list');
            die();
        }
        $this->set('entityKey', $entityKey);
        $this->set('id', $id);

        $deleterFactory = new WorkOrderDeleterFactory();
        $deleter = $deleterFactory->create(EntityKeyTypesUtil::WORK_FLOW);
        $deleter->setRedirectUrl('/v2/owner/entity/workflow/' . $entityKey . '/list');
        $deleter->setLabel('Workflow');
        
        $this->delete_work_order($deleter, $id);
    }

    /**
     * @param int|array $id
     * @param WorkOrderDeleterInterface $deleter
     * @throws Exception
     */
    private function delete_work_order($deleter, $id = null) {
        $this->_record_referer_path();

        $this->loadModel('Post');

        //If request type is post
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
			if(IS_REST){
				$this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __($deleter->getLabel(), true))]);
			}else{
				$this->flashMessage(sprintf(__('Invalid %s', true), __($deleter->getLabel(), true)));
				$referer_url = !($this->Session->check('referer_url')) ? $deleter->getRedirectUrl() : $this->Session->read('referer_url');
				$this->Session->delete('referer_url');
				$this->redirect($referer_url);
			}
        }
        $module_name = __($deleter->getLabel(), true);
        if (count($_POST['ids'] ?? []) > 1) {
            $module_name = __($deleter->getLabel(), true);
        }

        $work_orders = $this->WorkOrder->find('all', array('conditions' => array('WorkOrder.id' => $id)));
		
		foreach($work_orders as $k => $work_order){
            if (!$deleter->isDeletable($work_order)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
			$this->validate_open_day($work_order['WorkOrder']['start_date'], $deleter->getRedirectUrl());
		}
		
        if (empty($work_orders)) {
			if(IS_REST){
				$this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), ucfirst($module_name))]);
			}else{
				$this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
				$this->redirect($deleter->getRedirectUrl());
			}
        }

		if(IS_REST){
			$_POST['submit_btn'] = true;
			$_POST['ids'] = [$id];
		}
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes' && $deleter->delete($work_orders)) {
				App::import('Vendor', 'notification_2');
				NotificationV2::delete_notificationbyref($id,NotificationV2::NOTI_UPDATE_WORK_ORDER);

//                $this->add_stats(STATS_REMOVE_CLIENT, array(serialize($id)));
				if(IS_REST){
					$this->set("message", sprintf(__('%s has been deleted', true), ucfirst($module_name)));
					$this->render("success");
					return;
				}else{
					$this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
					$referer_url = $this->_get_referer_path();
					$this->redirect($deleter->getRedirectUrl());
				}
            } else {
				if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            }
        }else if ( empty ($_POST['submit_btn'])&& !empty($id)){
            $count_array = [
                'invoices_count' => $this->WorkOrder->get_invoice_count($id),
                'appointments_count' => $this->WorkOrder->get_appointments_count($id),
                'notes_count' => $this->WorkOrder->get_posts_count($id),
                'incomes_count' => $this->WorkOrder->get_expense_count($id, 1),
                'expenses_count' => $this->WorkOrder->get_expense_count($id),
                'timetracking_count' => $this->WorkOrder->get_time_tracking_count($id),
                'purchaseorders_count' => $this->WorkOrder->get_purchase_order_count($id),
                'journal_count' => $this->getJournalsCountByEntityId($id),
            ];
            
            $this->validateCanBeDeleted($count_array,$work_orders,$deleter);
            
            foreach ($count_array as $key => $value) {
                $this->set($key, $value);
            }



        }
        $this->set('work_orders', $work_orders);
        $this->set('module_name', $module_name);
        $this->set('title_for_layout',  __('Delete',true).' ' .__($deleter->getLabel(), true));
    }

    function validateCanBeDeleted($count_array, $work_orders, $deleter)
    {

        $client_id_conditions = [];
        $item_id_conditions = [];
        $i = 0;
        foreach ($work_orders as $c) {
            $client_ids[] = $c['WorkOrder']['id'];
            $client_id_conditions['work_order_id[' . $i . ']'] = $c['WorkOrder']['id'];
            $item_id_conditions['item_id[' . $i . ']'] = $c['WorkOrder']['id'];
            $i++;
        }
        $error_links = [];
        if (!empty($count_array['invoices_count'])) {
            $link = Router::url(['controller' => 'invoices', '?' => $client_id_conditions]);
            $error_links[] = '<a class="" target="_blank"  href=' . $link . '>' . __("Invoices", true) . "<b>" . $count_array['invoices_count'] . "</b></a>";
        }

        if (!empty($count_array['appointments_count'])) {
            $link = Router::url(['controller' => 'appointments', 'action' => 'index', 8,  '?' => ['compact' => 0, 'status_id' => -1] + $item_id_conditions]);
            $error_links[] = '<a class="" target="_blank"  href=' . $link . '>' . __("Appointments", true) . "<b>" . $count_array['appointments_count'] . '</b></a>';
        }

        if (!empty($count_array['notes_count'])) {
            $error_links[] = '<span class=""  href="#">' . __("Notes", true) . '<b>' . $count_array['notes_count'] . '</b></span>';
        }

        if (!empty($count_array['incomes_count'])) {
            $link = Router::url(['controller' => 'incomes', 'action' => 'index',  '?' => $client_id_conditions]);
            $error_links[] = '<a class="" target="_blank"  href=' . $link . '>' . __("Incomes", true) . "<b>" . $count_array['incomes_count'] . '</b></a>';
        }


        if (!empty($count_array['expenses_count'])) {
            $link = Router::url(['controller' => 'expenses', 'action' => 'index',  '?' => $client_id_conditions]);
            $error_links[] = '<a class="" target="_blank"  href=' . $link . '>' . __("Expenses", true) . "<b>" . $count_array['expenses_count'] . '</b></a>';
        }

        if (!empty($count_array['timetracking_count'])) {
            $link = Router::url(['controller' => 'time_tracking', 'action' => 'index',  '?' => ['list' => "", 'no_loader' => 1] + $client_id_conditions]);
            $error_links[] = '<a class="" target="_blank"  href=' . $link . '>' . __("Time Tracking", true) . "<b>" . $count_array['timetracking_count'] . '</b></a>';
        }

        if (!empty($count_array['purchaseorders_count'])) {
            $link = Router::url(['controller' => 'purchase_invoices', '?' => $client_id_conditions]);
            $error_links[] = '<a class="" target="_blank"  href=' . $link . '>' . __("Purchase Invoices", true) . "<b>" . $count_array['purchaseorders_count'] . "</b></a>";
        }

        if (!empty($count_array['journal_count'])) {
            $link = Router::url(['controller' => 'journals', 'action' => 'index',  '?' => ['entity_type' => "work_order", 'entity_id' => implode(',', $client_ids ?? [])]]);
            $error_links[] = '<a class="" target="_blank"  href=' . $link . '>' . __("Journals", true) . "<b>" . $count_array['journal_count'] . '</b></a>';
        }

        if (!empty($error_links)) {

            $message = sprintf(__t('You cannot delete the %s because it has related transactions , such as '), __t($deleter->getLabel()));
            foreach ($error_links as $link) {
                $message .= ' , ' . $link;
            }

            CustomValidationFlash([$message]);

            if (count($work_orders) == 1) {
                $deleter->setRedirectUrl(Router::url(['action' => 'view', $work_orders[0]['WorkOrder']['id']]));
            }
            $this->redirect($deleter->getRedirectUrl());
        }
    }

    /**
     * viewing work order with status and invoices related to it .
     * @param int $id Identifier of the work group 
     */
    function owner_view ( $id = null )
    {
        $this->pageTitle=__("Work Orders" , true );
         if ($this->params['url']['ext'] == 'pdf') {
             $pdf=true;
         }else{
             $pdf=false;
         }
        $site = getAuthOwner();
        $work_order = $this->WorkOrder->findById (  $id ) ;
        if($work_order['WorkOrder']['workflow_type_id'] && !$pdf) {
            return $this->redirect(Router::url(['action' => 'workflow_view', $id]));
        }
        if (!$this->WorkOrder->check_staff_permitted ( $id)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel ( 'InvoicePayment');
        
        if ( empty ( $work_order ) ) {
			if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found", true), __("Work Order", true))));
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect ( 'index'); die ;
        }

        $this->loadModel ( 'Post');
        $this->loadModel ( 'FollowUpStatus');
        $FollowUpStatus = $this->FollowUpStatus->getLisWithColorList(Post::WORK_ORDER_TYPE , false );

        $this->set('journalCount' , $this->getJournalsCountByEntityId($id));

        $this->view_work_order($FollowUpStatus, $work_order, $id);
        $this->set ( 'posts_count' , $this->WorkOrder->get_posts_count ( $id ));
        $this->set('appointments_count', $this->WorkOrder->get_appointments_count($id));
        $this->set('appointment_status_counts', $this->FollowUpReminder->get_statuses_count($id ,Post::WORK_ORDER_TYPE));
        $this->set('assignerTransactionTypes', WorkOrderTransactionAssigner::listOfTransactionsTypes());
        $this->set('currentTransactionTypes', WorkOrderTransactionAssigner::getCurrentTransactionTypesForEntity($id));
        $this->set('hasAnyTransaction', WorkOrderTransactionAssigner::hasAnyTransactions($id));
        $this->set('assignerTransactionMapper', WorkOrderTransactionAssigner::TransactionObjMapper);
        $this->set('showAssignTransaction',  check_permission (ASSIGN_TRANSACTION_TO_WORK_ORDERS) );
        $allt = "select created,'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,due_after,'payment_method',currency_code,(SELECT IF(alter_description!='',alter_description,description) COLLATE utf8_unicode_ci FROM `journals` where entity_type=(CASE WHEN invoices.type=0 THEN 'invoice' WHEN invoices.type=6 THEN 'refund_receipt' WHEN invoices.type=5 THEN 'credit_note' END) and entity_id=invoices.id limit 1) as description from invoices where work_order_id='$id' and type in(0,5,6) and draft <> 1 
                    UNION 
                    select created,'payment',id,(select no from invoices where id=invoice_id),null,client_id,date,status,invoice_id,amount,null,payment_method,currency_code, receipt_notes as descritpion from invoice_payments where (invoice_id in(select id from invoices where work_order_id='$id' and type in(0,5) and draft <> 1) OR (payment_work_order_id IS NOT NULL AND  payment_work_order_id='$id') )  and status=1 and payment_method!='client_credit'  ORDER BY `date` ASC,`created` ASC";
        $transaction_count = $this->Client->flat_query_results("select count(*)as cc from ( $allt )aaa");
        $this->set('transaction_count', $transaction_count['0']['cc']);
        if (Settings::getValue(WorkOrderPlugin, 'work_order_integrate_social_media') && $work_order['Client']['phone2']) {
            $this->setShareWithSocialMediaData($work_order,WorkOrderPlugin);
        }
		if(IS_REST){
			$this->set('rest_item', $work_order);
			$this->set('rest_model_name', "WorkOrder");
			$this->render("view");
		}
    }

    private function view_work_order($FollowUpStatus, $work_order, $id)
    {
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'InvoicePayment');

        $this->loadModel ( 'Invoice');
        $this->set ( 'stock_request_count' , $this->WorkOrder->get_stock_requests_count ( $id ));
        $this->set ( 'invoice_count' , $this->WorkOrder->get_invoice_count ( $id , Invoice::Invoice));
        $this->set ( 'creditnote_count' , $this->WorkOrder->get_invoice_count ( $id , Invoice::Credit_Note));
        $this->set ( 'refund_count' , $this->WorkOrder->get_invoice_count ( $id , Invoice::Refund_Receipt));
        $this->set ( 'estimate_count' , $this->WorkOrder->get_invoice_count ( $id , Invoice::Estimate));
        $salesOrderCount = 0;
        if (settings::getValue(SalesPlugin, SettingsUtil::ENABLE_SALES_ORDER)) {
            $salesOrderCount = $this->WorkOrder->get_invoice_count($id, Invoice::SALES_ORDER);
        }
        $this->set('sales_order_count', $salesOrderCount);

        $this->set ( 'expense_count' , $this->WorkOrder->get_expense_count ( $id , 0));
        $this->set ( 'income_count' , $this->WorkOrder->get_expense_count ( $id , 1));
        
        $this->set ( 'requisitions_count' , $this->WorkOrder->getRequisitionCount ( $id ));

        $this->set ( 'purchaseorder_count' , $this->WorkOrder->get_purchase_order_count ( $id ));
        $this->set ( 'timetracking_count' , $this->WorkOrder->get_time_tracking_count ( $id , Invoice::Estimate));
        $allt = "select created,'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,due_after,'payment_method',currency_code,(SELECT IF(alter_description!='',alter_description,description) COLLATE utf8_unicode_ci FROM `journals` where entity_type=(CASE WHEN invoices.type=0 THEN 'invoice' WHEN invoices.type=6 THEN 'refund_receipt' WHEN invoices.type=5 THEN 'credit_note' END) and entity_id=invoices.id limit 1) as description from invoices where work_order_id='$id' and type in(0,5,6) and draft <> 1 
        UNION 
        select created,'payment',id,(select no from invoices where id=invoice_id),null,client_id,date,status,invoice_id,amount,null,payment_method,currency_code, receipt_notes as descritpion from invoice_payments where (invoice_id in(select id from invoices where work_order_id='$id' and type in(0,5) and draft <> 1) OR (payment_work_order_id IS NOT NULL AND  payment_work_order_id='$id') )  and status=1 and payment_method!='client_credit' ORDER BY `date` ASC,`created` ASC";
        $transaction_count = $this->Client->flat_query_results("select count(*)as cc from ( $allt )aaa");
        $this->set('transaction_count', $transaction_count['0']['cc']);

        $has_closed = false ;
        $has_open = false ;
        $grouped_statuses = [] ;
        $pdf = $this->params['url']['ext'] == 'pdf';
//        debug ( $FollowUpStatus ) ;

        foreach ($FollowUpStatus as $k => $f  )
        {
            if ( $f['status'] == WorkOrder::STATUS_OPEN)
            {
                $grouped_statuses[__("Open",true)][$k] = $f ;
                $has_open = true ;
            }
            if ( $f['status'] == WorkOrder::STATUS_CLOSED)
            {
                $grouped_statuses[__("Closed",true)][$k] = $f ;
                $has_closed = true ;
            }
        }
//						       dd($grouped_statuses);


		
		 if ( !$has_open ) {
            $open_status = array('status' => WorkOrder::STATUS_OPEN,  'name' => __("Open",true), 'style' => 'style="border-color:green;background:green ; color:white ;"') ;
            $FollowUpStatus[WorkOrder::STATUS_OPEN*-1] = $open_status;
            $grouped_statuses[__("Open",true)][WorkOrder::STATUS_OPEN*-1] = $open_status ;
        }
		
        if ( !$has_closed ) {
            $closed_status = array('status' => WorkOrder::STATUS_CLOSED,  'name' => __("Closed",true), 'style' => 'style="border-color:grey;background:grey ; color:white ;"');
            $FollowUpStatus[WorkOrder::STATUS_CLOSED*-1] = $closed_status;
            $grouped_statuses[__("Closed",true)][WorkOrder::STATUS_CLOSED*-1] = $closed_status ;
        }
//        debug ( $grouped_statuses ) ;die ; 
        $statuses = $this->WorkOrder->get_status_list ( )  ; 
         
        $this->set('grouped_statuses', $grouped_statuses);
        $this->set('FollowUpStatus', $FollowUpStatus);
        $this->set('work_order', $work_order ) ;
        $this->set ( 'statuses' , $statuses );
        $this->set ( 'colored_statuses' , $this->WorkOrder->get_colored_status_list ( )  );
        
      
       
        
        $view_reports = false ;
        if ( check_permission (View_His_Own_Reports ) ) {
            App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
            $report_count = 0 ;
            $view_reports = true ;

            //REVENUE DATA
            $RdataArray = $this->WorkOrder->get_revenue_data ( $id ) ;
            if ( !empty ($RdataArray['reportData'] ) ){
                $report_count ++ ;
                $revenue_data = [__('Paid',true )  => 0 , __('Unpaid',true )  => 0 ] ;

                foreach ( $RdataArray['reportData'] as $e  ){
                    foreach ( $e as $currency => $v ) {
                        $rate_val = CurrencyConverter::index($currency, $work_order['WorkOrder']['budget_currency'], $work_order['WorkOrder']['start_date']);
                        $revenue_data[__('Paid',true )] += $rate_val * $v[0]['paid'];
                        $revenue_data[__('Unpaid',true )] += $rate_val * $v[0]['unpaid'];
                    }
                }
                if (count(array_filter(array_values($revenue_data), fn($item) => $item >0)) == 0) // php8 fix
                    $revenue_data = 0;
                $this->set ('revenue_data' , $revenue_data  ) ;
            }
            
            
            //EXPENSE DATA
            $EdataArray = $this->WorkOrder->get_expenses_data ( $id ) ;
            if ( !empty ($EdataArray['reportData'] ) ){
                $report_count ++ ;
                foreach ($EdataArray['reportData'] as $currency => $v )
                {
                    $rate_val = CurrencyConverter::index($currency, $work_order['WorkOrder']['budget_currency'], $work_order['WorkOrder']['start_date']);
                    foreach ( $v as $t =>$d ){
                        foreach ( $d as $e ) {
                            $expense_data[$t] += $rate_val * ($e['purchase_total_with_discount'] ?? $e['amount']);
                        }
                    }
                }
                $this->set ('expense_data' , $expense_data  ) ;
            }
            
            
            
            //BUDGET DATA
            if ( !empty ( $work_order['WorkOrder']['budget'] )  ){
                $spent = $this->WorkOrder->get_spent ( $id ) ;
                if ( $spent > 0 ) {
                    $budget_data = [__('Total Spent',true ) => $spent , __("Remaining Budget",true) => ((($work_order['WorkOrder']['budget'] - $spent )< 0) ?0 : ($work_order['WorkOrder']['budget'] - $spent ) )] ; 
                    $this->set ('budget_data' , $budget_data  ) ;
                    $report_count ++ ;
                }
                
            }
            if ( $report_count  == 0 ) {
                $view_reports = false ; 
            }
            
        }
		if(ifPluginActive(BnrPlugin)){
			 $this->loadModel ( 'Product');
			 $this->loadModel ( 'Supplier');
		$bnr_invoice=$this->Invoice->find('first',array('conditions'=>array('Invoice.work_order_id'=>$id,'Invoice.type'=>Invoice::Resellers)));	
		if($bnr_invoice){
		foreach($bnr_invoice['InvoiceItem'] as &$sitem){
		$product=$this->Product->find('first',array('recursive'=>-1,'conditions'=>array('Product.id'=>$sitem['product_id'])));	
		$sitem['Product']=$product['Product'];
		$supplier=$this->Supplier->find('first',array('recursive'=>-1,'conditions'=>array('Supplier.id'=>$sitem['col_4'])));	
		$sitem['Supplier']=$supplier['Supplier'];
		}
		
		
		
		$client_invoice=$this->Invoice->find('first',array('conditions'=>array('Invoice.source_id'=>$bnr_invoice['Invoice']['id'],'Invoice.source_type'=>Invoice::Resellers)));					
		$this->set('client_invoice',$client_invoice);
		$this->loadModel ( 'PurchaseOrder');
		$po=$this->PurchaseOrder->find('all',array('recursive'=>-1,'fields'=>array('currency_code','summary_total'),'conditions'=>array('PurchaseOrder.source_id'=>$bnr_invoice['Invoice']['id'],'PurchaseOrder.source_type'=>Invoice::Resellers)));							
		$this->set('po_currency_code',$po[0]['PurchaseOrder']['currency_code']);
		$total_po=0;
		foreach($po as $rpo){
		$total_po=$total_po+$rpo['PurchaseOrder']['summary_total'];	
		}
		
		$this->set('total_profit',$client_invoice['Invoice']['summary_total']-$total_po);
		$this->set('total_po',$total_po);
		$this->set('po_currency_code',$po[0]['PurchaseOrder']['currency_code']);
		$this->set('bnr_invoice',$bnr_invoice);
		}
		}
        $this->set ('view_reports' , $view_reports  );
        $this->set ('report_count' , $report_count  );
        //END of report charts
        
        
        $view_work_order = $this->WorkOrder->generate_work_order_html ( $id,"",$pdf ) ;
        
        $current_currency = getCurrentSite('currency_code');

        $results = $this->InvoicePayment->get_financial_results (  "{{}}.currency_code='$current_currency' AND {{}}.work_order_id = ".intval($id)." AND 1" ,10000 , 1,false, true) ;
        $this->set ( 'transaction_list' , $this->WorkOrder->get_transaction_list_table ( $id ,1 , true ) );
        $this->set ( 'financial_results' ,$results );
        
        
        $this->set ( 'appointment_statuses' , [-1 => __("All",true )]+$this->FollowUpReminder->getStatuses ( ) )  ; 
        
        $this->set ( 'view_work_order' ,$view_work_order );
        

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('work_order');

        $default_template = $this->PrintableTemplate->getDefaultTemplateForType('work_order');

        if(!empty($default_template))
        {
			$invoice_print_method = settings::getValue(InvoicesPlugin, 'invoice_print_method');
			if($invoice_print_method==settings::OPTION_PRINT_PDF || !IS_PC){
            $iframe_link = Router::url(['controller'=>'printable_templates' , 'action' => 'view' , $work_order['WorkOrder']['id'],$default_template[0]['PrintableTemplate']['id'], $default_template[0]['PrintableTemplate']['type'] ]);
			}else{
            $iframe_link = Router::url(['controller'=>'printable_templates' , 'action' => 'view_template' , $work_order['WorkOrder']['id'],$default_template[0]['PrintableTemplate']['id'], $default_template[0]['PrintableTemplate']['type'] ]);
			}
            $pdf_link = Router::url(['controller'=>'printable_templates' , 'action' => 'view' , $work_order['WorkOrder']['id'],$default_template[0]['PrintableTemplate']['id'], $default_template[0]['PrintableTemplate']['type'] ,'ext' => 'pdf']);
            $print_link = Router::url(['controller'=>'printable_templates' , 'action' => 'print_saved' , $work_order['WorkOrder']['id'],$default_template[0]['PrintableTemplate']['id'], $default_template[0]['PrintableTemplate']['type']]);
            $email_link = Router::url(['controller'=>'printable_templates' , 'action' => 'send_template' , $work_order['WorkOrder']['id'],$default_template[0]['PrintableTemplate']['id'], $default_template[0]['PrintableTemplate']['type']]);

            $this->set('default_template',true);
            $this->set('iframe_link',$iframe_link );
            $this->set('pdf_link',$pdf_link );
            $this->set('print_link',$print_link );
            $this->set('email_link',$email_link );
        }

//      dd($default_template);
		App::import('Vendor', 'notification_2');
            $this->loadModel('Requisition');
		NotificationV2::view_notificationbyref($id, NotificationV2::NOTI_UPDATE_WORK_ORDER);

        $manualOutboundRequisitions = [];
		if ((settings::getValue(InventoryPlugin, 'enable_requisitions')||settings::getValue(InventoryPlugin, 'enable_requisitions_po')) && !empty($work_order['WorkOrder']['client_id'])) {
		    $workOrderRequisitions = $this->Requisition->find('all', ['conditions' => ['Requisition.work_order_id' => $id, 'Requisition.order_type' => Requisition::ORDER_TYPE_MANUAL_OUTBOUND]]);
		    foreach ($workOrderRequisitions as $requisition) {
                $manualOutboundRequisitions[$requisition['Requisition']['id']] = "#{$requisition['Requisition']['number']} - " . format_date($requisition['Requisition']['date']);
            }
        }
		$this->set('manualOutboundRequisitions', $manualOutboundRequisitions);
		$this->set('showDeleteButton', $this->WorkOrder->editable($work_order));

        $this->set('has_templates', false);
        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

        $repo = new TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('work_order');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);
    }

    function owner_workflow_view($id = null)
    {
        $this->set('title_for_layout', __('Workflows', true ));

        /** @note taht this query adds branch condition */
        $work_order = $this->WorkOrder->findById($id);

        if (empty($work_order)) {
            if (IS_REST) {
                $this->cakeError('error404', ['message' => __('Workflow not found', true)]);
            }
            $this->flashMessage(sprintf(__("%s is not found",true),__('Workflow', true)));
            return $this->redirect ( '/');
        }

        $workflowTypeId = $work_order['WorkOrder']['workflow_type_id'];

        $entityKey = getWorkFlowTypeEntityName($workflowTypeId);
        /** @var  TemplateRepository $repo */
        $repo = new TemplateRepository(getPDO('portal'), getPDO());
        $this->set('entityKey', $entityKey);
        $viewTemplates = $repo->getEntityViewTemplates($entityKey,TemplateTypeUtil::PDF,EntityKeyTypesUtil::WORK_ORDER);
        $this->set('workflow_view_templates', $viewTemplates);

        $this->set(
            'workflow_email_templates',
            [
                'local' => $repo->getEntityViewTemplates($entityKey,TemplateTypeUtil::EMAIL)['local'] ?? [],
                'global' => $repo->getGlobalEmailTemplates('work_order', TemplateTypeUtil::EMAIL),
            ]
        );

        if (!$this->WorkOrder->canViewWorkflow($id)) {
            if (IS_REST) {
                $this->cakeError('error403');
            }

            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        $this->loadModel ( 'FollowUpStatus');
        /**
         * @var \Izam\Daftra\Workflow\WorkflowService $workFlowService
         */
        $workFlowService = izam_resolve(\Izam\Daftra\Workflow\WorkflowService::class);
        $statuses = $workFlowService->getWorkFlowTypeStatuses($entityKey);
        $FollowUpStatus = $workFlowService->createWorkFLowStatusColorsList($statuses);

        $this->loadModel('Post');
        $this->view_work_order($FollowUpStatus, $work_order, $id);

        $this->set('journalCount' , $this->getJournalsCountByEntityId($id));

        $this->loadModel('Workflow');
        $this->set ( 'workflow_stock_request_count' , $this->Workflow->get_stock_requests_count ( $id ));
        $this->set('appointments_count', $this->Workflow->get_appointments_count($id, false));
        $this->set('appointment_status_counts', $this->FollowUpReminder->get_statuses_count($id ,Post::WORKFLOW_TYPE));
        $this->set('posts_count', $this->Workflow->getPostsCount($id));

        $this->set('showEditButton', $this->WorkOrder->canEditWorkflow($id) && $this->WorkOrder->checkDynamicPermission('update'));
        $this->set('showDeleteButton', $this->WorkOrder->canDeleteWorkflow($id, false) && $this->WorkOrder->checkDynamicPermission('delete'));
        $this->set('showAssignTransaction',check_permission(ASSIGN_TRANSACTION_TO_WORKFLOWS));
        $this->set('url', '/v2/owner/entity/' .$entityKey . '/' . $id . '/show');
        $this->set('type', $entityKey);
        $this->set('id', $id);

        $workflowTypeId = $work_order['WorkOrder']['workflow_type_id'];

        $this->loadModel ( 'WorkflowType');
        $workflowType = $this->WorkflowType->findById($workflowTypeId);
        if ($workflowType !== false) {
            $workflowTypeName = $workflowType['WorkflowType']['name'];
            $this->set('_PageBreadCrumbs', $this->getWorkflowBreadCrumb($workflowTypeName, $entityKey, $id));
            $this->set('workflowTypeName', $workflowTypeName);
        }
        $builder = getEntityBuilder();
        $workflowEntity = $builder->buildEntity(getWorkFlowTypeEntityName($workflowTypeId));

        $this->loadModel('LocalEntity');
        $childrenEntities = $workflowEntity->getChildren();

        $tabActions = [];
        foreach ($childrenEntities as $childEntity) {
            $result = $this->LocalEntity->query("SELECT COUNT(*) as count FROM `{$childEntity->getTable()}` WHERE reference_id = $id");
            if (false === $result || $result[0][0]['count'] == 0) {
                continue;
            }

            $hasPermission = DynamicPermissionService::isEmployeeHavePermission(
                getAuthOwner('staff_id'),
                EntityKeyTypesUtil::LOCAL_ENTITY,
                $childEntity->getEntityKey(),
                'view'
            );

            if (!$hasPermission) {
                continue;
            }

            $action = new TabAction('/v2/owner/entity/' . $childEntity->getEntityKey() . '/list?iframe=1&filter[reference_id]=' . $id, $childEntity->getLabel());

            $tabActions[] = $action;
        }

        $this->set('tab_actions', $tabActions);
        $this->set('workflowEntity', $workflowEntity);
        $this->set('assignerTransactionTypes', WorkOrderTransactionAssigner::listOfTransactionsTypes());
        $this->set('currentTransactionTypes', WorkOrderTransactionAssigner::getCurrentTransactionTypesForEntity($id));
        $this->set('hasAnyTransaction', WorkOrderTransactionAssigner::hasAnyTransactions($id));
        $this->set('assignerTransactionMapper', WorkOrderTransactionAssigner::TransactionObjMapper);

        if (Settings::getValue(WorkflowPlugin, 'workflow_integrate_social_media') && $work_order['Client']['phone2']) {
            $this->setShareWithSocialMediaData($work_order);
        }
        
        $this->render('owner_workflow_view');

        if (IS_REST) {
            $this->set('rest_item', $work_order);
            $this->set('rest_model_name', 'WorkOrder');
            $this->render('view');
        }
    }

    private function setShareWithSocialMediaData($work_order,$plugin = WorkflowPlugin) {
        $this->set('shareWithSocialMedia', 1);
        $placeholders = PlaceHolder::work_order_place_holder($work_order);
        
        $messageTemplates = Settings::getValue($plugin, 'social_media_share_message_templates',null,false);
        $messageTemplates = json_decode($messageTemplates, true);
        $socialMediaMessageData = [];
        foreach ($messageTemplates as $key => $messageTemplate) {
            if (is_array($messageTemplate)) { // handles custom platforms
                $linkTemplate = $messageTemplate['link_template'];
                $messageTemplate = $messageTemplate['message'];
            }
            $message = PlaceHolder::replace($messageTemplate, array_keys($placeholders), $placeholders);
            $socialMediaMessageData[] = [
                'link' => SocialMediaLinkCreatorFactory::make($key, $linkTemplate ?? null)->getLink($work_order['Client']['phone2'], $message, $key),
                'label' => $key,
            ];
        }
        $this->set('socialLinks', $socialMediaMessageData);
    }
    
    private function getWorkflowBreadCrumb($workflowTypeName, $entityType, $id)
    {
        $breadcrumbs = [];

        $breadcrumbs[0]['title'] = $workflowTypeName;
        $breadcrumbs[0]['link'] = '/v2/owner/entity/workflow/' . $entityType . '/list';

        $breadcrumbs[1]['title'] = Inflector::singularize($workflowTypeName) . " #" . $id . "";
        $breadcrumbs[1]['link'] = Router::url(['controller' => 'WorkOrders', 'action' => 'workflow_view', $id, 'prefix' => 'owner'], true);
        return $breadcrumbs;
    }
    
    function owner_change_status($id, $status_id) {


        $this->loadModel('FollowUpStatus');
//$FollowUpStatus = $this->FollowUpStatus->getList(Post::INVOICE_TYPE);

        $work_order = $this->WorkOrder->find(array('WorkOrder.id' => $id));

        if (!$work_order) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if (!check_permission(EDIT_WORK_ORDER_STATUS)) {

            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $this->WorkOrder->change_status ( $id , $status_id ) ;
		
        
//        $action = ACTION_UPDATE_INVOICE_STATUS;
//        if ($is_estimate) {
//            $action = ACTION_UPDATE_ESTIMATE_STATUS;
//            $FollowUpStatus = $this->FollowUpStatus->getList(Post::ESTIMATE_TYPE);
//        } else {
//            $FollowUpStatus = $this->FollowUpStatus->getList(Post::INVOICE_TYPE);
//        }

//        $this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $id, 'param1' => $invoice['Invoice']['no'], 'param2' => $invoice['Client']['business_name'], 'param3' => $FollowUpStatus[$status_id]));
        $flash = 'Work Order status has been updated';
        
		
        $this->flashMessage(__($flash, true), 'Sucmessage', 'secondaryMessage');
        $this->redirect(array('action' => 'view' , $id));
        die();
    }
    //DEPRECATED - now we update the status when the user updates the follow_up_status
    private function owner_change_wo_status  ( $id , $status ) {
        $statuses =  $this->WorkOrder->get_status_list (); 
        
        
        if ( $this->WorkOrder->check_staff_permitted($id ) && in_array ( $status , array_keys ($statuses) ))
        {
            $work_order = $this->WorkOrder->find ( 'first' , ['conditions' => ['WorkOrder.id' => $id ] ]);
            $this->WorkOrder->id = $id ;
            $this->WorkOrder->saveField ( 'status' , $status ) ;
            $this->WorkOrder->saveField ( 'follow_up_status_id' , NULL ) ;
            $flash = 'Work Order status has been updated';
            $this->add_actionline(  ACTION_UPDATE_WORK_ORDER_STATUS , array('primary_id' => $status, 'secondary_id' => $id, 'param1' => $statuses[$status], 'param2' => $work_order['WorkOrder']['title'], 'param3' => $work_order['WorkOrder']['number']));
            $this->flashMessage(__($flash, true), 'Sucmessage', 'secondaryMessage');
            $this->redirect(array('action' => 'view' , $id));
            die();
        }else {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect ( 'index'); die ;
        }
    }
    function owner_timeline ( $id ){
        if ( $this->WorkOrder->check_staff_permitted($id ) )
        {
            $this->set('is_ajax', false);
            if ($this->RequestHandler->isAjax()) {
                $this->set('is_ajax', true);
            }
            
            $action_line_data = $this->WorkOrder->get_action_line_data  ( $id ) ;
            $all_actions_lists = $action_line_data['all_actions_lists'];
            $all_action_ids = $action_line_data['all_action_ids'];
            
            require_once APP . 'vendors' . DS . 'Timeline.php';
            $timeline = new Timeline('All', array('secondary_id' => $id));
            $timeline->init(array('ActionLine.id' => $all_action_ids),$all_actions_lists );
            $this->set('data', $timeline->getDataArray());
            
            

            foreach ($timeline->ActionKeys as $key => $action) {
                if (in_array($key, $all_actions_lists)) {
                    $invoice_actions[$key] = $action;
                }
            }
            $this->set('actions', $invoice_actions);
        }
    }
    function owner_template_settings() {
        if (!check_permission(Edit_General_Settings) ) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
//        $this->WorkOrder->get_spent ( 11);
        $this->loadModel('Site');
        $this->loadModel('Post');

//        $this->set('form_data', settings::formData(InvoicesPlugin));
        if (!empty($this->data)) {
            if (!empty($this->data['WorkOrder']['work_order_template']) )
                settings::setValue(WorkOrderPlugin, 'work_order_template', json_encode (['template' =>  $this->data['WorkOrder']['work_order_template']] ) );

            $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
            $this->Site->reload_session();
            $this->redirect("/v2/owner/work-order/settings");
            
        } else {
//            $this->data['WorkOrder']['next_workorder_number'] = settings::getValue(WorkOrderPlugin, 'next_workorder_number');
//            $this->data['WorkOrder']['budget_calculation'] = settings::getValue(WorkOrderPlugin, 'budget_calculation');
            $this->data['WorkOrder']['work_order_template'] =json_decode(settings::getValue(WorkOrderPlugin, 'work_order_template') , true ) ['template'];
            
//            echo '*'.$this->data['WorkOrder']['work_order_template'].'nour';
//            var_dump (empty ( trim($this->data['WorkOrder']['work_order_template'] ) )  ) ;
//            var_dump ( $this->data['WorkOrder']['work_order_template'] ) ;die; 
        }
    }
    function owner_settings() {
        if (!check_permission(Edit_General_Settings) ) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
//        $this->WorkOrder->get_spent ( 11);
        $this->loadModel('Site');
        $this->loadModel('Post');

//        $this->set('form_data', settings::formData(InvoicesPlugin));
        if (!empty($this->data)) {

//            if (!empty($this->data['WorkOrder']['next_workorder_number']) )
//                settings::setValue(WorkOrderPlugin, 'next_workorder_number', $this->data['WorkOrder']['next_workorder_number']);
            if (!empty($this->data['WorkOrder']['budget_calculation']) )
                settings::setValue(WorkOrderPlugin, 'budget_calculation', $this->data['WorkOrder']['budget_calculation']);
            //            if (!empty($this->data['WorkOrder']['work_order_template']) )
            //            settings::setValue(WorkOrderPlugin, 'work_order_template', json_encode (['template' =>  $this->data['WorkOrder']['work_order_template']] ) );
            
            if (isset($this->data['WorkOrder']['integrate_social_media']) ){
                settings::setValue(WorkOrderPlugin, 'work_order_integrate_social_media', $this->data['WorkOrder']['integrate_social_media']);
                
            }

            $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
            $this->Site->reload_session();
            $this->redirect(Router::url(array('action' => 'settings')));
            
        } else {
			App::import('Vendor','AutoNumber');
            $this->data['WorkOrder']['next_workorder_number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_WORK_ORDER);
            $this->data['WorkOrder']['budget_calculation'] = settings::getValue(WorkOrderPlugin, 'budget_calculation');
            $this->data['WorkOrder']['integrate_social_media'] = settings::getValue(WorkOrderPlugin, 'work_order_integrate_social_media');
//            $this->data['WorkOrder']['work_order_template'] =json_decode(settings::getValue(WorkOrderPlugin, 'work_order_template') , true ) ['template'];
//            echo '*'.$this->data['WorkOrder']['work_order_template'].'nour';
//            var_dump (empty ( trim($this->data['WorkOrder']['work_order_template'] ) )  ) ;
//            var_dump ( $this->data['WorkOrder']['work_order_template'] ) ;die; 
        }
    }

    function owner_preview ( $id ) {
        $this->loadModel ( 'Client');
        $work_order = $this->WorkOrder->findById ( $id) ;
        
        if ( empty ( $work_order ) ) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) );
            $this->redirect ( 'index'); die ;
        }
        $additional_styles = '<style type="text/css">'.'table { overflow: visible !important; }
thead { display: table-header-group }
tfoot { display: table-row-group }
tr { page-break-inside: avoid }';
        $files = array('client-statement.css',  'reports.css', 'common.css', 'work-order-template.css');
        if(CurrentSiteLang() == 'ara'){
            $files[] =  'work-order-template-ar.css';
        }
        foreach ($files as $file) {
            $additional_styles .= PHP_EOL . file_get_contents(CSS . $file);
        }
        $additional_styles .= '</style>';
        $view_work_order = $this->WorkOrder->generate_work_order_html ( $id ,$additional_styles) ;
        $this->layout = '' ; 
        $this->set ( 'style' ,$additional_styles ) ;
        $this->set ( 'view_work_order' ,$view_work_order );
        $this->set ( 'work_order' , $work_order ) ;
        
    }
    
    function owner_transaction_list($id = null) {
        $this->layout = false;

        $owner=getAuthOwner();

        $this->loadModel ('Invoice');
        $this->loadModel('InvoicePayment');
        $work_order = $this->WorkOrder->findById( $id);
        $this->set('work_order', $work_order);
        if (!$work_order) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        $this->loadModel ('Client');
        $client = $this->Client->read(null, $work_order['WorkOrder']['client_id']);
        $this->set('client', $client);

        $placeholders = array_merge(PlaceHolder::site_place_holder()+PlaceHolder::datetime_place_holder(), PlaceHolder::client_place_holder($client), PlaceHolder::staff_place_holder($owner['staff_id']));
        $this->set('placeholders', $placeholders);

        $statement_header_html = settings::getValue(0, "statement_header_html");
        $custom_header = PlaceHolder::replace($statement_header_html,array_keys($placeholders), $placeholders);
        $this->set('custom_header', $custom_header);

        $statement_footer_html = settings::getValue(0, "statement_footer_html");
        $custom_footer = PlaceHolder::replace($statement_footer_html, array_keys($placeholders), $placeholders);
        $this->set('custom_footer', $custom_footer);

        $transcation_columns_setting = settings::getValue(0, "transcation_columns_setting");
        $this->set('transcation_columns_setting', $transcation_columns_setting);

        $per_page = 600;
        $invoice_more_query = $invoice_more_query ?? "";
        $payment_more_query = $payment_more_query ?? "";
        $starting_limit =""; 
        $starting_balance = array();
        $page = $this->params['url']['page']  ? intval($this->params['url']['page']) : 1;
        $date_from = $this->params['url']['date_from'];
        $date_to = $this->params['url']['date_to'];
        if (!empty($date_from)) {
            $date_from = $this->params['url']['date_from'];
            $date_to = $this->params['url']['date_to'];

            $invoice_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
            $before_invoice_more_query = " And date <'{$date_from}'";

            $payment_start_query = " And date >='{$date_from}' And date <='{$date_to}'";
            $payment_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
        }

        if ( $page > 1 ){
			$starting_limit =  " LIMIT " . ($per_page * ($page - 1));
		}

        $allt = "select created,'invoice',id,no,type,client_id,date,payment_status,id as invoice_id,summary_total,due_after,'payment_method',currency_code,(SELECT IF(alter_description!='',alter_description,description) COLLATE utf8_unicode_ci FROM `journals` where entity_type=(CASE WHEN invoices.type=0 THEN 'invoice' WHEN invoices.type=6 THEN 'refund_receipt' WHEN invoices.type=5 THEN 'credit_note' END) and entity_id=invoices.id limit 1) as description from invoices where work_order_id='$id' and type in(0,5,6) and draft <> 1 %s
                    UNION 
                    select created,'payment',id,(select no from invoices where id=invoice_id),null,client_id,date,status,invoice_id,amount,null,payment_method,currency_code, receipt_notes as descritpion from invoice_payments where (invoice_id in(select id from invoices where work_order_id='$id' and type in(0,5) and draft <> 1) OR (payment_work_order_id IS NOT NULL AND  payment_work_order_id='$id') )  and status=1 and payment_method!='client_credit'  %s ORDER BY `date` ASC,`created` ASC";

        $invoices=[];

        $allt_starting = sprintf($allt, $payment_start_query, $payment_start_query);
        if ( $page > 1 ){
            $starting_invoice_with_payment = $this->WorkOrder->query($allt_starting. $starting_limit , false);
        }
        if(!empty($date_from)){
            $before = sprintf($allt, $before_invoice_more_query, $before_invoice_more_query);
            $before_starting_invoice_with_payment = $this->Client->query($before, false);
            if(isset($starting_invoice_with_payment)){
                $starting_invoice_with_payment=array_merge($starting_invoice_with_payment,$before_starting_invoice_with_payment);
            }else{
                $starting_invoice_with_payment=$before_starting_invoice_with_payment;
            }
        }

        foreach ($starting_invoice_with_payment as $t) {
            $t = $t[0];
            if ($t['invoice'] == 'payment' or ($t['invoice'] == 'invoice' and in_array($t['type'], array(Invoice::Credit_Note)))) {
                $t['summary_total'] = $t['summary_total'] * -1;
            }
            if(isset($_GET['show_details']) && $_GET['show_details']=='true') {
                if (in_array($t['type'], [Invoice::Invoice, Invoice::Refund_Receipt, Invoice::Estimate, Invoice::Credit_Note])) {
                    $invoices[$t['id']] = $this->Invoice->getInvoice($t['id']);
                }
            }
            $starting_balance[$t['currency_code']] +=$t['summary_total'];
        }
        $this->set('invoices', $invoices);
        $paginator_url = 'date_from=' . $this->params['url']['date_from'] . '&';
        $paginator_url .='date_from=' . $this->params['url']['date_to'] . '&';
        $this->set('paginator_url', $paginator_url);
        $this->set('starting_balance', $starting_balance);
        $invoice_with_payment = $this->WorkOrder->query($allt);

        $count = $this->WorkOrder->flat_query_results("select count(*)as cc from ( $allt )aaa");

        $allt = sprintf($allt, $payment_more_query, $payment_more_query);
        $invoice_with_payment = $this->Client->query($allt . " LIMIT $per_page OFFSET " . ($per_page * ($page - 1)));

        $this->set('transaction_count', $count[0]['cc']);

        $this->set('InvoiceTypeList', Invoice::getInvoiceTypeList());
        $this->set('payment_methods', InvoicePayment::getPaymentMethods());
        $this->set('invoice_with_payment', $invoice_with_payment);

        $paginator_url = 'date_from=' . $this->params['url']['date_from'] . '&';
        $paginator_url .= 'date_from=' . $this->params['url']['date_to'] . '&';
        $this->set('paginator_url', $paginator_url);

    }
    function owner_financial_list ( $id ) {
        echo $this->WorkOrder->get_financial_list_table ( $id);die ; 
    }
    function owner_json_find() {
        $conditions = parent::_filter_params();
        if(strpos($conditions['WorkOrder.follow_up_status_id'], 'sys-general-') !== false){
            $conditions['WorkOrder.follow_up_status_id'] = str_replace('sys-general-', '',$conditions['WorkOrder.follow_up_status_id'] );
            $conditions['WorkOrder.status'] =$conditions['WorkOrder.follow_up_status_id'];
            unset($conditions['WorkOrder.follow_up_status_id']);
        }

    //    Configure::write('debug', 0);
        $owner = getAuthOwner();
        $value = trim($_GET['q']);

        if (!empty($value)) {

            $value = mysql_escape_string($value);
            
            $work_orders = $this->WorkOrder->search_work_orders ( $value,$conditions  ) ;
            
            $result = array();
            foreach ($work_orders as $wo) {
                
                $result[] = array(
                    'name' => ('#' . $wo['WorkOrder']['number'] . ', ' . $wo['WorkOrder']['title'] ),
                    'id' => $wo['WorkOrder']['id'],
                    'details' => ''
                );
            }
            
            if (!empty($result))
                array_unshift($result, array(
                    'name' => __('Please Select', true) . ':',
                    'id' => '',
                    'details' => ''
                ));
            echo json_encode($result);
            die();
        }else {
            echo json_encode(array());
            die();
        }
    }
    
    function owner_get_spent ( $id ) {
        echo $this->WorkOrder->get_spent ( $id ) ;
        die; 
    }
    
    function owner_send_to_client($id = null) {

        $this->loadModel ( 'Site');
        $this->loadModel ( 'Client');

        $check = check_daily_email_limit();


        if (!$check['status']) {

            $owner = getAuthOwner();

            $this->Email->from = $this->config['txt.admin_mail'];
            $this->Email->to = $this->config['txt.admin_mail'];
            $this->Email->subject = $owner['business_name'] . ' Email Limit';
            $this->Email->send('Site : ' . $owner['business_name'] . '<br>' . 'Subdomin : ' . $owner['subdomain']);

            $this->flashMessage($check['message']);
            $this->redirect(array('action' => 'view', $id));
        }

        $work_order = $this->WorkOrder->find ('first' , ['conditions' => ['WorkOrder.id' => $id ]]  );
        if (empty($work_order)  ) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        $client = $this->Client->find ( 'first' , ['conditions' => ['Client.id' => $work_order['WorkOrder']['client_id']] ]);

        if (empty($client['Client']['email'])) {
            $this->flashMessage(__('The Email address for this Client is not entered', true));
            $this->redirect($this->referer(array('action' => 'view', $client['Client']['id']), true));
        }

        $this->set('work_order', $work_order);
        
        $this->loadModel('EmailLog');
        $this->loadModel('EmailTemplate');

        $email_template = $this->EmailTemplate->getDefaultTemplate('work_order');

        if ( empty ( $email_template))
        {
            $defaultTemplate = [] ; 
        }else {
            $defaultTemplate = $email_template;
        }
        
        //$this->EmailTemplate->getDefaultTemplate('paid-invoice');
        $this->set(compact('defaultTemplate'));
        
        if (!empty($this->data)) {
            if (empty($this->data['EmailTemplate']['to_email'])) {
                $this->EmailTemplate->validationErrors['to_email'] = __('Please enter client email', true);
                $error = true;
            }

            $count_email = explode(',', $this->data['EmailTemplate']['to_email']);
            $count_email_2 = explode(';', $this->data['EmailTemplate']['to_email']);
            if (!empty($this->data['EmailTemplate']['to_email']) and (count($count_email) > 5 or count($count_email_2) > 5)) {
                $this->EmailTemplate->validationErrors['to_email'] = __('You can only send 5 emails at once', true);
                $error = true;
            }
            $count_cc_email = explode(',', $this->data['EmailTemplate']['cc_email']);
            $count_cc_email_2 = explode(';', $this->data['EmailTemplate']['cc_email']);
            if (!empty($this->data['EmailTemplate']['cc_email']) and (count($count_cc_email) > 3 or count($count_cc_email_2) > 3)) {
                $this->EmailTemplate->validationErrors['cc_email'] = __('You can only send 3 cc emails at once', true);
                $error = true;
            }

            $count_bcc_email = explode(',', $this->data['EmailTemplate']['bcc_email']);
            $count_bcc_email_2 = explode(';', $this->data['EmailTemplate']['bcc_email']);
            if (!empty($this->data['EmailTemplate']['bcc_email']) and (count($count_bcc_email) > 3 or count($count_bcc_email_2) > 3)) {
                $this->EmailTemplate->validationErrors['bcc_email'] = __('You can only send 3 bcc emails at once', true);
                $error = true;
            }


            if ($error) {
                $this->flashMessage(__('Could not send the invoice to the client', true));
            } else {
                if ($row = $this->_directSendEmail($work_order, $this->data, [], $this->data['EmailTemplate']['send_post_attachments'] ? true : false)) {
                // ACTION_SEND_INVOICE (primary_id => invoice_id, sec => client_id, p1 => email_log_id, p2=> email_address)


                    $this->flashMessage(__('The Work Order has been sent', true), 'Sucmessage');

                    $this->redirect(array('action' => 'view', $id));
                } else {
           
                    $this->flashMessage(__('Could not send the email to the client', true));
                }
            }
        } else {
//            $this->loadModel('Post');
//            $this->set('post_attachment_names', $this->Post->getInvoiceAttachments($id, 'name'));
//            $this->data['EmailTemplate'] = $defaultTemplate['SystemEmail'];
//            $this->data['EmailTemplate']['body'] = $defaultTemplate['SystemEmail']['message'];
            $this->data = $defaultTemplate;
            unset($this->data["EmailTemplate"]['id']);
        }
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->set('email_templates', $this->EmailTemplate->getEmailTemplates('work_order'));
        
        $this->set('invoice_name', strtolower(__( "Work Order", true ))  ) ;
        $placeholder = $this->EmailTemplate->getPlaceHoldersList('work_order');
//print_r($placeholder);
        $this->set('PlaceHolders', $placeholder);
        $this->set('client_email', $this->Client->getClientEmails($work_order['WorkOrder']['client_id']));
        $this->set('title_for_layout',  __('Send work order to client', true));
        

    }

    function _directSendEmail($work_order, $layout_options = false, $owner = false, $send_post_attachments = false) {

        $this->loadModel('Post');
        if (empty($owner))
            $owner = getAuthOwner();

        if (is_numeric($work_order)) {
            $work_order_id = $work_order;
        } else {
            $work_order_id = $work_order['WorkOrder']['id'];
        }
        $work_order = $this->WorkOrder->find ( 'first'  , ['conditions' => ['WorkOrder.id' => $work_order_id] ] );//$this->Invoice->getInvoice($work_order_id);

        $this->loadModel('EmailTemplate');

       // debug($post_attachments);
        if (true or $work_order['Invoice']['type'] == 0) {
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate ( 'work_order') ;

            if (!empty($layout_options))
                $defaultTemplate = array_merge((array)$defaultTemplate, (array)$layout_options);
            if (!empty($layout_options['EmailTemplate']['to_email'])) {
                $emails = explode(",", $layout_options['EmailTemplate']['to_email']);
            } else {
                $emails = array();
            }

            $result = $this->SysEmails->work_order($work_order['WorkOrder'], $work_order['Client'], $owner, $defaultTemplate, $emails);

            if ($result) {
                $this->flashMessage(__('Invoice has been saved & sent', true), 'Sucmessage');
//                $this->Invoice->save(array('Invoice' => array(
//                        'id' => $work_order['Invoice']['id'],
//                        'draft' => 0,
//                        'last_sent' => date('Y-m-d')
//                    )), array('callbacks' => false, 'validation' => false, 'fieldList' => array('id', 'draft', 'last_sent')));
            } else {
              
                $this->flashMessage(sprintf(__('Could not send the invoice %s', true), $this->SysEmails->error_message), 'Errormessage', 'secondaryMessage');
            }
            return $result;
        } else {
            return false;
        }
    }
    function owner_reset_template ( ) {
        
        settings::setValue(WorkOrderPlugin, 'work_order_template' , NULL);
        $this->flashMessage(__('Template reset is successful', true), 'Sucmessage');
        $this->redirect ( ['controller' => 'work_orders' , 'action' => 'template_settings']);
    }
    function client_index(){
        $conditions = parent::_filter_params();
        $conditions['client_id'] =  getAuthClient('id') ;
        $conditions['WorkOrder.workflow_type_id'] = null;
        $work_orders = $this->paginate('WorkOrder' , $conditions);
        $this->set('work_orders' , $work_orders) ; 
    }
    function client_view($id ) {
        $work_order = $this->WorkOrder->find('first' , ['recursive' => -1 ,'conditions' => [  'id' => $id ,'client_id' => getAuthClient('id')] ]);
        if ( empty ( $work_order ) ){
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect(['controller' => "work_orders" ]);
        }

        if ($this->params['url']['ext'] == 'jpeg') {
        return $this->owner_view($id);
        }


        $this->set('postsCount', $this->WorkOrder->getClientPostsCount($id, getAuthClient('id')));
        $this->loadModel('PrintableTemplate');
        $default_template = $this->PrintableTemplate->getDefaultTemplateForType('work_order');
        $this->set('default_template', $default_template);
        $this->set('work_order' , $work_order);
    }
    function client_preview($id ) {
        $work_order = $this->WorkOrder->find('first' , ['fields' => 'id', 'recursive' => -1 ,'conditions' => [  'id' => $id ,'client_id' => getAuthClient('id')] ]);
        if ( empty ( $work_order ) ){
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect(['controller' => "work_orders" ]);
        }
        $this->owner_preview($id);
        $this->render('owner_preview') ; 
    }
    function owner_custom_fields_mapping ( ) {
        App::import('Vendor', 'settings');
        $this->loadModel('Setting');
        $this->loadModel('Post');
        $this->loadModel('CustomForm');
        $this->loadModel('CustomFormField');
        
        
        
        if ( !empty ( $this->data  ) ){
            settings::setValue(WorkOrderPlugin, 'client_workorder_mapping' , json_encode($this->data));
        }
        $this->data = json_decode ( settings::getValue(WorkOrderPlugin, 'client_workorder_mapping'  ) , true  ) ;
        $clients_form = $this->CustomForm->find('first' , ['recursive' => -1 ,  'conditions' => ['table_name' => 'clients'] ]);
        $wo_form = $this->CustomForm->find('first' , ['recursive' => -1 ,  'conditions' => ['table_name' => 'work_orders'] ]);
        $clients_form_fields_list = $this->CustomFormField->find ( 'list' , ['fields' => 'id,label' ,  'order' => 'display_order ASC', 'recursive' => -1 , 'conditions' => ['is_deleted <> 1',  'type not'=>[180,190],'custom_form_id' => $clients_form['CustomForm']['id']] ]);
        $clients_form_fields = $this->CustomFormField->find ( 'all' , ['order' => 'display_order ASC', 'recursive' => -1 , 'conditions' => ['is_deleted <> 1', 'type not'=>[180,190], 'custom_form_id' => $clients_form['CustomForm']['id']] ]);
        $wo_form_fields = $this->CustomFormField->find ( 'all' , ['order' => 'display_order ASC',  'recursive' => -1 , 'conditions' => ['is_deleted <> 1', 'type not'=>[180,190],'custom_form_id' => $wo_form['CustomForm']['id']] ]);
        
        $this->set ( 'clients_form_fields_list' , $clients_form_fields_list ) ;
        $this->set ( 'clients_form_fields' , $clients_form_fields ) ;
        $this->set ( 'wo_form_fields' , $wo_form_fields ) ;
        //supposed to get from the post
//        $clients = [
//            "client_first_name"=>"Ali",
//            "client_last_name"=>"Moka",
//            "client_some_field"=>"some_field",
//            "client_address"=>"Address"
//        ];
//
//        //supposed to get from the post
//        $workorders = [
//            "workorder_first_name"=> "Optinal",
//            "workorder_last_name"=> "Second",
//            "workorder_some_field"=> "SomeField",
//            "workorder_address"=> "Address"
//        ];
//
//        $clients_json = array_keys($clients);
//        $workorders_json = array_keys($workorders);
//
//        $json_array = [];
//        for( $i = 0 ; $i < count($clients_json) ; $i++ )
//        {
//            $json_array[$clients_json[$i]] = $workorders_json[$i];
//        }
//
//        settings::setValue(0, 'client_workorder_mapping' , json_encode($json_array));
//
//        $json = json_decode(settings::getValue(0, 'client_workorder_mapping' ));
//        dd($json);

    }
    function owner_get_mapped_fields ( $client_id ) {
        $CustomForm = GetObjectOrLoadModel('CustomForm');
        GetObjectOrLoadModel('CustomFormField');
        $form = $CustomForm->get_custom_form_table_name("clients");
        if(!$form)
        {
            die (json_encode([]));
        }

        $this->loadModel ( 'CustomTable');
        $wo_data = json_decode ( settings::getValue(WorkOrderPlugin, 'client_workorder_mapping'  ) , true  ) ;
        $clientsCustomDataModel = GetObjectOrLoadModel('ClientsCustomData'); 
        $e = $clientsCustomDataModel->findByClientId ($client_id );
//        dd($e);
        $json_return = [] ;
        foreach ( $wo_data['WorkOrder']['custom_fields'] as $k =>  $w ) {
            $val = $e['ClientsCustomData']['field_'.$w] ;
            if ( preg_match("/\d{4}-\d{2}-\d{2}/" , $val ))
            {
                $val =format_date($val);
            }
            $json_return[] = ['id' => $k , 'value' => $val  ];
        }
        
        die (json_encode($json_return)); 
    }

    function owner_convert_requisitions($workOrderID) {
        if ($this->data['Requisition']['ids']) {
            $this->loadModel('Requisition');
            $invoiceID = $this->Requisition->convertRequisitionsToInvoice($this->data['Requisition']['ids']);
            $this->Session->write('requisition_ids', $this->data['Requisition']['ids']);
            
            if ($invoiceID) {
                $this->redirect(['controller' => 'invoices', 'action' => 'edit', $invoiceID]);
            }
        } else {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('Requisitions', true)));
        }
        $this->redirect(['action' => 'view', $workOrderID]);
    }

    private function getJournalsCountByEntityId($entity_id)
    {
        $this->loadModel('Journal');
        $conditionsArr = ['Journal.entity_id' => $entity_id, 'entity_type' => 'work_order'];
        if (!check_permission(VIEW_ALL_JOURNALS) && check_permission(VIEW_OWN_JOURNALS)) {
            $conditionsArr['staff_id'] = getAuthStaff('id');
        }
        return $this->Journal->find('count', ['conditions' => $conditionsArr]);
    }

    private function setCustomEntityAndKey(){
        $this->set('entityCustomField', 'workorders_custom_field');
        $this->set('entityFieldCustomFieldKey', 'workorders_custom_fields');
    }

}