<?php

class AllfilesController extends AppController {

    var $name = 'Allfiles';

    function download($id = null,$name=null) {
        $this->loadModel('PostFile');
        $this->loadModel('Post');
        $this->loadModel('Staff');
        $this->loadModel('Client');
        $types = Post::getItemTypes();


        $row = $this->PostFile->read(null, $id);
        if(empty($name) && !empty($row)){
            $this->redirect(Router::url([$id,str_replace(' ','_',$row['Allfile']['original_file_name'])]));
        }

        //debug($row);
        //die();
        if (!$row) {
            $this->flashMessage(__('File Not Found', TRUE));
            $this->redirect('/');
        }

//        if ($row['Post']['item_type'] != Post::CLIENT_TYPE) {
//            $this->flashMessage(__('File Not Found', TRUE));
//            $this->redirect('/');
//        }

        $type = $types[$row['Post']['item_type']];
        $staff_id = getAuthOwner('staff_id');
        $staff = getAuthOwner() ;
        $client = getAuthClient();
//        debug ( $staff ) ; 
//        debug ( $client ) ; 
        if ( !$client && !($staff) ) {
            debug ('client and staff_id');
            $this->flashMessage(__('File Not Found', TRUE));
            $this->redirect('/');
        }
        
        if ($staff_id) {
            if (!check_permission($type['view_permission']) && !check_permission($type['view_assiged_permission'])) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
            
     
        }
//        debug ( $client);
        if ($client) {
            if ($row['Post']['partner_id'] != $client['id']) {
                $this->flashMessage(__('File Not Found', TRUE));
                $this->redirect('/');
            }
        }

        $path = WWW_ROOT . "files/" . SITE_HASH . "/post-files/" . $row['Allfile']['file_name'];
        if (!file_exists($path)) {
                $this->flashMessage(__('File Not Found', TRUE));
                $this->redirect('/');            
        }
        if ($staff_id) {
            $staff = $this->Staff->read(null, $staff_id);
            $this->add_actionline(STAFF_DOWNLOAD_FILE, array('staff_id'=>$staff_id,'secondary_id' => $staff_id, 'primary_id' => $id, 'param1' => $staff['Staff']['name'],'param3'=>$row['Allfile']['original_file_name'],'param4'=>$row['Allfile']['file_size'],'param5'=>$row['PostFile']['post_id']));
        }
        
        if ($client) {
            $this->add_actionline(CLIENT_DOWNLOAD_FILE, array('secondary_id' => $client['id'], 'primary_id' => $id, 'param1' => $client['business_name'],'param2' => $client['client_number'],'param3'=>$row['Allfile']['original_file_name'],'param4'=>$row['Allfile']['file_size'],'param5'=>$row['PostFile']['post_id']));
        }
        
          if (!$staff_id && !$client) {
            $staff = $this->Staff->read(null, $staff_id);
            $this->add_actionline(STAFF_DOWNLOAD_FILE, array('staff_id'=>$staff_id,'secondary_id' => $staff_id, 'primary_id' => $id, 'param1' => getAuthOwnerName(),'param3'=>$row['Allfile']['original_file_name'],'param4'=>$row['Allfile']['file_size'],'param5'=>$row['PostFile']['post_id']));
        }
	    $file_name = str_replace(' ','_',$row['Allfile']['original_file_name']);
	    header('Content-Type: application/octet-stream');
	    header('Content-Disposition: attachment; filename=' . $file_name);
	    header('Cache-Control: public, must-revalidate, max-age=0'); // HTTP/1.1
	    header('Pragma: no-cache');
	    header("Content-Length: " . filesize($path));
	    readfile($path);
	    die();
    }

}
