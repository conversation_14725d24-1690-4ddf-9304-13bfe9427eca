<?php

use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;

/**
 * @property Tooltip $Tooltip
 */
class ClientAppointmentsController extends AppController {

    var $name = 'ClientAppointments';
    var $modelName = 'ClientAppointment';
    var $helpers = array('Html', 'Form', 'Fck', 'Mixed');
    var $more_js_lables = array('Today', 'Day', 'Month');

    function owner_index() {
		  $site = getCurrentSite();
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->more_js_lables);
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $staff_id = getAuthOwner('staff_id');


        if ((!check_permission(View_All_Attachments_And_Notes_For_All_Clients) && !check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) && !check_permission(View_His_Own_Notes_Attachments_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }


        $this->loadModel('Post');
        $this->set('staffs', $this->ClientAppointment->Staff->getList());
        $statuses['-1'] = __('All', true);
        $statuses += $this->ClientAppointment->getStatuses();

        $this->set('statuses', $statuses);



        $this->loadModel('FollowUpAction');
        $actions = $this->FollowUpAction->getList(Post::CLIENT_TYPE);
        if (check_permission(Edit_General_Settings)) {
            $actions['-1'] = __('Edit Actions List', true);
        }
        $this->set('actions', $actions);
        $this->set('actionstext', $this->FollowUpAction->getList(Post::CLIENT_TYPE));


        $conditions = $this->_filter_params();

        if (isset($conditions['ClientAppointment.follow_up_status'])) {
            $conditions['Client.follow_up_status'] = $conditions['ClientAppointment.follow_up_status'];
            unset($conditions['ClientAppointment.follow_up_status']);
        }
        if (isset($conditions['ClientAppointment.category'])) {
            $conditions['LOWER(Client.category)'] = strtolower($conditions['ClientAppointment.category']);
            unset($conditions['ClientAppointment.category']);
        }

        if (!isset($conditions['ClientAppointment.status_id'])) {
            $conditions['ClientAppointment.status'] = ClientAppointment::Status_Scheduled;
            //$this->params['url']['status_id'] = ClientAppointment::Status_Scheduled;
        } elseif ($conditions['ClientAppointment.status_id'] == '-1') {
            unset($conditions['ClientAppointment.status_id']);
        }
        if (isset($conditions['ClientAppointment.status_id'])) {
            $conditions['ClientAppointment.status'] = $conditions['ClientAppointment.status_id'];
            unset($conditions['ClientAppointment.status_id']);
        }

        $conditions[] = 'Client.id is not null';



        $this->loadModel('Post');
        if (check_permission(View_All_Attachments_And_Notes_For_All_Clients) or check_permission (Edit_Delete_All_Notes_Attachments ) ) {
            
        } else if (check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) or check_permission (Edit_Delete_His_Own_Attachments_Notes_Only)  ) {
			//debug ( 'test' ) ; 
            $conditions[] = ' ( ClientAppointment.staff_id = ' . $staff_id . ' OR  ' . $staff_id . ' IN (SELECT staff_id FROM item_staffs WHERE item_staffs.item_type = ' . Post::CLIENT_TYPE . ' AND item_staffs.item_id=ClientAppointment.item_id) )';
        } else if (check_permission(View_His_Own_Notes_Attachments_Only)) {
            $conditions['ClientAppointment.staff_id'] = $staff_id;
        }



        $this->loadModel('FollowUpStatus');

        $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::CLIENT_TYPE);
        if (check_permission(Edit_General_Settings)) {
            $FollowUpStatus[] = array('name' => false, 'value' => false, 'data-divider' => "true");
            $FollowUpStatus[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> ' . __('Edit Statuses List', true) . ' </span>', 'name' => __('Edit Statuses List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
        }
        $this->set('FollowUpStatuses', $FollowUpStatus);
        $this->set('categories', $this->ClientAppointment->Client->getCategoriesList(false));


        if (count($conditions) > 1) {
            $this->set('search_filter', "true");
        }

        $this->paginate['ClientAppointment']['order'] = array('ClientAppointment.date' => 'ASC', 'ClientAppointment.id' => 'ASC');
        debug ( $conditions ) ; 
		$rows = $this->paginate('ClientAppointment', $conditions);

        $frist_date = $this->ClientAppointment->find('first', array('order' => 'ClientAppointment.date asc', 'conditions' => $conditions));


        $this->set('frist_date', $frist_date);

        //$filters = $this->ClientAppointment->getFilters();


        $is_ajax = $this->RequestHandler->isAjax();
        $this->set('is_ajax', $is_ajax);


        $client_statuses = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, true);
        $this->set('client_statuses', $client_statuses);

        $client_status_colors = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, true, array(), 'color');
        $this->set('client_status_colors', $client_status_colors);

        debug ($rows ) ;
	$this->set('site',$site);
        $this->set('client_appointments', $rows);
    }
	function owner_get_client_ajax () {
		$client_id = intval($_POST['client_id'] );
		$this->loadModel ('Client' );
		$this->Client->recursive = -1 ; 
		die (json_encode ($this->Client->findById($client_id ) ));
	}
    function owner_delete($id = null) {
		if ( !empty ($_POST['ids'] )){
			$ids = $_POST['ids'];
			$id = $_POST['ids'][0];
		}else {
			$ids = [$id];
		}
		
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $staff_id = getAuthOwner('staff_id');
        $row = $this->ClientAppointment->read(null, $id);
        if (!$row) {
            $this->flashMessage(__('Appointment not found', TRUE));
            $this->redirect(array('action' => 'index'));
        }

        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'));
        }

        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $this->loadModel('ItemStaff');
            $this->loadModel('Post');
            $client_conditions = array('ItemStaff.item_type' => Post::CLIENT_TYPE, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'id,item_id', 'conditions' => $client_conditions)));
            if (!in_array($row['ClientAppointment']['item_id'], $client_list)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index'));
            }
        }
		
        if (!empty($this->data) || !empty ($_POST['ids']) ) {
            if ($_POST['submit_btn'] == 'yes' ||!empty ($_POST['ids']) ) {
				
                $this->loadModel('Client');
                $this->loadModel('FollowUpAction');
                $this->loadModel('Post');
				$statuses = $this->ClientAppointment->getStatuses();
				$FollowUpActions = $this->FollowUpAction->getList(Post::CLIENT_TYPE);
				
				foreach ( $ids as $i ){
					$this->ClientAppointment->del($i);
					$row = $this->ClientAppointment->read(null, $i);
					$client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
					
					
					$this->add_actionline(ACTION_DELETE_CLIENT_APPOINTMENT, array('primary_id' => $i, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $row['ClientAppointment']['date'], 'param4' => $row['ClientAppointment']['date'], 'param5' => $FollowUpActions[$row['ClientAppointment']['action_id']], 'param6' => $statuses[$row['ClientAppointment']['status']], 'param7' => $row['ClientAppointment']['status_date']));
				}
				

                $this->flashMessage(__('The appointment has been deleted', true), 'Sucmessage');
                if (isset($url_params['back_to_client'])) {
                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['ClientAppointment']['item_id'], '#' => 'AppointmentsBlock'));
                }
                $this->redirect(array('action' => 'index'));
            } else {

                $this->redirect(array('action' => 'index'));
            }
			
        }
        $this->set('id', $id);
    }

    function owner_calendar() {
        
    }

    function owner_calendar_json() {
        $this->loadModel('Post');
        $this->loadModel('FollowUpAction');
        $follow_up_actions = $this->FollowUpAction->getList(Post::CLIENT_TYPE);
        $this->loadModel('FollowUpStatus');
        $follow_up_statuses = $this->FollowUpStatus->getList(Post::CLIENT_TYPE, false, array(), 'color');
        $url_params = $this->params['url'];


        $conditions = $this->_filter_params();

        if (isset($conditions['ClientAppointment.follow_up_status'])) {
            $conditions['Client.follow_up_status'] = $conditions['ClientAppointment.follow_up_status'];
            unset($conditions['ClientAppointment.follow_up_status']);
        }
        if (isset($conditions['ClientAppointment.category'])) {
            $conditions['LOWER(Client.category)'] = strtolower($conditions['ClientAppointment.category']);
            unset($conditions['ClientAppointment.category']);
        }

        if (!isset($conditions['ClientAppointment.status_id'])) {
            $conditions['ClientAppointment.status'] = ClientAppointment::Status_Scheduled;
            //$this->params['url']['status_id'] = ClientAppointment::Status_Scheduled;
        } elseif ($conditions['ClientAppointment.status_id'] == '-1') {
            unset($conditions['ClientAppointment.status_id']);
        }
        if (isset($conditions['ClientAppointment.status_id'])) {
            $conditions['ClientAppointment.status'] = $conditions['ClientAppointment.status_id'];
            unset($conditions['ClientAppointment.status_id']);
        }
        debug ( $conditions);


        $conditions[] = 'Client.id is not null';
        //$conditions['ClientAppointment.date >='] = $url_params['start'];
        //$conditions['ClientAppointment.date <='] = $url_params['end'];
        $rows = $this->ClientAppointment->find('all', array('conditions' => $conditions));

        //echo "<pre>";
        //print_r($rows);
        foreach ($rows as $key => $row) {
            $app[$key]['id'] = $row['ClientAppointment']['id'];
            $app[$key]['date'] = $row['ClientAppointment']['date'];
            $app[$key]['title'] = $row['Client']['business_name'] . ' - ' . $follow_up_actions[$row['ClientAppointment']['action_id']];

            $app[$key]['color'] = $follow_up_statuses[$row['Client']['follow_up_status']];
        }
        echo json_encode($app);
        die();
    }

    function owner_add($id = null , $item_type = 1) {
        $owner = getAuthOwner();
        $formats = getDateFormats('std');
        $dateFormat = $formats[$owner['date_format']];
        if ((!check_permission(Add_Notes_Attachments_For_All_Clients) && !check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'));
        }


        $this->__set_data($item_type);
        $this->loadModel('Post');
        $this->loadModel('Client');
        $this->loadModel('FollowUpStatus');
        $this->loadModel('ItemStaff');

        if (!empty($id) ) {
            $back = FollowUpStatus::$types_diff[$item_type]['view_url'].$id.'#AppointmentsBlock';//array('controller' => 'clients', 'action' => 'view', $id, '#' => 'AppointmentsBlock');
            if ( $item_type == Post::CLIENT_TYPE){
                $item = $this->Client->read(null, $id);
                if (!$item) {
                    $this->flashMessage(__('Client not found', true));
                    $this->redirect(array('controller' => 'clients', 'action' => 'index'));
                }
            }
        } else {
            $back = array('action' => 'index');
        }




        if (!empty($this->data)) {
			
            $this->data['ClientAppointment']['item_id'] = empty($id) ? $this->data['ClientAppointment']['item_id'] : $id;

//            $item = $this->Client->read(null, $id);
//            if (empty($item) || empty($id)) {
//                $this->flashMessage(__('Client not found', true));
//                $this->redirect(array('controller' => 'clients', 'action' => 'index'));
//            }

            if (!check_permission(Add_Notes_Attachments_For_All_Clients)) {
                $staff_id = getAuthOwner('staff_id');
                $count = $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => $item_type, 'ItemStaff.item_id' => $this->data['ClientAppointment']['item_id'], 'ItemStaff.staff_id' => $staff_id)));
				
                if ($count == 0) {
                    $this->flashMessage(__('You are not allowed to edit this client', true));
                    $this->redirect(array('controller' => 'clients', 'action' => 'index'));
                }
            }

            $this->data['ClientAppointment']['item_type'] = $item_type;
            //$this->data['ClientAppointment']['item_id'] = $id;
            $this->data['ClientAppointment']['status'] = ClientAppointment::Status_Scheduled;
            $this->data['ClientAppointment']['status_date'] = null;
            $this->data['ClientAppointment']['date'] = $this->ClientAppointment->formatDateTime($this->data['ClientAppointment']['date'] . ':00');
			
			$this->data['ClientAppointment']['staff_id'] = getAuthOwner('staff_id');
			//debug (  $this->data );die  ;
			
            if ($this->data['ClientAppointment']['recurring'] == "" or $this->data['ClientAppointment']['recurring'] == 0) {
                unset($this->data['RecurringAppointment']);

                if ($this->ClientAppointment->save($this->data)) {
                    $statuses = $this->ClientAppointment->getStatuses();
                    $FollowUpActions = $this->FollowUpAction->getList($item_type);
                    $this->add_actionline(ACTION_ADD_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $id, 'param1' => $item['Client']['business_name'], 'param2' => $item['Client']['client_number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));
                    $this->flashMessage(__('The appointment has been saved', true), 'Sucmessage');
                    $this->redirect($back);
                } else {
                    //print_r( $this->ClientAppointment->validationErrors);
                    $this->flashMessage(__('Can\'t save Appointment', TRUE));
                    
                    if (empty($this->data['ClientAppointment']['item_id']) and empty($id)) {
                        

                        $title = __('Schedule a New Appointment', true);
                    } else {
                        $item = $this->Client->read(null, $this->data['ClientAppointment']['item_id']);
                        $title = __('Schedule Appointment for', true) . $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
                    }
                }
            } else {
                $row = $this->ClientAppointment->save($this->data);
                $statuses = $this->ClientAppointment->getStatuses();
                $FollowUpActions = $this->FollowUpAction->getList($item_type);
                $this->add_actionline(ACTION_ADD_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $id, 'param1' => $item['Client']['business_name'], 'param2' => $item['Client']['client_number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));

                $this->data['RecurringAppointment']['last_generated_id'] = $this->ClientAppointment->id;
                $this->data['RecurringAppointment']['last_generated_date'] = $this->data['ClientAppointment']['date'];
                $this->data['RecurringAppointment']['active'] = 1;
                $this->data['RecurringAppointment']['unit_name'] = $this->data['RecurringAppointment']['unit_name'];
                $this->data['RecurringAppointment']['period_unit'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']]['unit'];
                $this->data['RecurringAppointment']['unit_count'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']]['unit_count'];
                if ($this->data['RecurringAppointment']['end_date'] != "") {
                    $date = $this->ClientAppointment->RecurringAppointment->formatDateTime($this->data['RecurringAppointment']['end_date']);
                    $this->data['RecurringAppointment']['end_date'] = $date;
                }

                $this->ClientAppointment->RecurringAppointment->save($this->data['RecurringAppointment']);
                $this->ClientAppointment->saveField("recurring_appointment_id", $this->ClientAppointment->RecurringAppointment->id, false);
                if (strtotime($this->data['ClientAppointment']['date']) <= strtotime(date('Y-m-d 23:59:59'))) {
                    $this->cron(getCurrentSite('id'));
                }

                $this->flashMessage(__('The appointment has been saved', true), 'Sucmessage');
                $this->redirect($back);
            }
        } else if (!empty($id)) {
            $this->data['ClientAppointment']['item_id'] = $id;
            $title = __('Schedule Appointment for', true) . $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
        } else {
            $title = __('Schedule a New Appointment', true);
        }


        $this->set('title_for_layout',  $title);
        $this->set('item_type', $item_type);
        $this->set('client_id', $id);
        $this->set('page_title', $title);
    }

    function owner_edit($id  , $item_type = 1) {
		
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'FollowUpStatus');

        $owner = getAuthOwner();
        $formats = getDateFormats('std');
        $dateFormat = $formats[$owner['date_format']];
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);

        $staff_id = getAuthOwner('staff_id');
        $row = $this->FollowUpReminder->read(null, $id);
        if (!$row) {
            $this->flashMessage(__('Appointment not found', TRUE));
            $this->redirect(array('action' => 'index'));
        }
        $row['ClientAppointment'] = $row['FollowUpReminder'];

        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'));
        }


        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $this->loadModel('ItemStaff');
            $this->loadModel('Post');
            $client_conditions = array('ItemStaff.item_type' => $item_type, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'id,item_id', 'conditions' => $client_conditions)));
            if (!in_array($row['ClientAppointment']['item_id'], $client_list)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index'));
            }
        }
        if (!empty($this->data)) {
			
//            if ($this->data['ClientAppointment']['date'] != $row['ClientAppointment']['date']) {
//                $this->data['ClientAppointment']['status'] = ClientAppointment::Status_Scheduled;
//                $this->data['ClientAppointment']['status_date'] = null;
//            }
            $this->data['ClientAppointment']['date'] = $this->ClientAppointment->formatDateTime($this->data['ClientAppointment']['date'] . ':00');
            if ($this->data['ClientAppointment']['recurring'] == "" or $this->data['ClientAppointment']['recurring'] == 0) {
                unset($this->data['RecurringAppointment']);
                $this->data['FollowUpReminder'] = $this->data['ClientAppointment'];
//                unset ($this->data['ClientAppointment'] );
                $this->FollowUpReminder->save($this->data);
                $this->loadModel('Client');
                
                $status = isset($this->data['ClientAppointment']['status']) ? $this->data['ClientAppointment']['status'] : $row['ClientAppointment']['status'];
                $status_date = isset($this->data['ClientAppointment']['status_date']) ? $this->data['ClientAppointment']['status_date'] : $row['ClientAppointment']['status_date'];
                $statuses = $this->ClientAppointment->getStatuses();
                $this->loadModel('FollowUpAction');
                $this->loadModel('Post');
                $FollowUpActions = $this->FollowUpAction->getList($item_type);
                if ( $item_type == Post::CLIENT_TYPE)
                {
                    $client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
                    $this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                }
                
                if (isset($url_params['back_to_client'])) {

                    $view_url =FollowUpStatus::$types_diff[$item_type]['view_url'];
                    $this->redirect($view_url.$row['ClientAppointment']['item_id'].'#AppointmentsBlock');
                } else {
                    $this->redirect(FollowUpStatus::$types_diff[$item_type]['url']);
                }
            } else {
                $this->data['FollowUpReminder'] = $this->data['ClientAppointment'];
                $this->FollowUpReminder->save($this->data);
                $this->loadModel('Client');
                
                $status = isset($this->data['ClientAppointment']['status']) ? $this->data['ClientAppointment']['status'] : $row['ClientAppointment']['status'];
                $status_date = isset($this->data['ClientAppointment']['status_date']) ? $this->data['ClientAppointment']['status_date'] : $row['ClientAppointment']['status_date'];
                $statuses = $this->ClientAppointment->getStatuses();
                $this->loadModel('FollowUpAction');
                $this->loadModel('Post');
                $FollowUpActions = $this->FollowUpAction->getList($item_type);
                if ( $item_type == Post::CLIENT_TYPE)
                {
                    $client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
                    $this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                }

                $this->data['RecurringAppointment']['active'] = 1;
                $this->data['RecurringAppointment']['unit_name'] = $this->data['RecurringClientAppointment']['unit_name'];
                $this->data['RecurringAppointment']['period_unit'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']]['unit'];
                $this->data['RecurringAppointment']['unit_count'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']]['unit_count'];
                if ($this->data['RecurringAppointment']['end_date'] != "") {
                    $date = $this->ClientAppointment->RecurringAppointment->formatDateTime($this->data['RecurringAppointment']['end_date']);
                    $this->data['RecurringAppointment']['end_date'] = $date;
                }
                $this->cron(getCurrentSite('id'));
                $this->ClientAppointment->RecurringAppointment->save($this->data['RecurringAppointment']);

                $this->FollowUpReminder->saveField("recurring_appointment_id", $this->ClientAppointment->RecurringAppointment->id, false);
                $this->flashMessage(__('The appointment has been saved', true), 'Sucmessage');
                if (isset($url_params['back_to_client'])) {

                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['ClientAppointment']['item_id'], '#' => 'AppointmentsBlock'));
                } else {
                    $this->redirect(array('action' => 'index'));
                }
            }
        }

//        debug($row);

        $title = __('Edit Appointment #', true) . ' ' . $id;
        $this->set('title_for_layout',  $title);
        $this->set('page_title', $title);
        $this->data = $row;
        $jsDateFormat = getDateFormats('std');
        if ($row['RecurringAppointment']['id'] != "") {
            $this->data['ClientAppointment']['recurring'] = 1;
        }
        if ($this->data['RecurringAppointment']['id'] != "" and $this->data['RecurringAppointment']['active'] == 1) {
            $this->data['ClientAppointment']['recurring'] = 1;
            if ($this->data['RecurringAppointment']['end_date'] != "0000-00-00") {
                $this->data['RecurringAppointment']['end_date'] = format_date($this->data['RecurringAppointment']['end_date']);
            } else {
                unset($this->data['RecurringAppointment']['end_date']);
            }
        }

        $this->data['ClientAppointment']['date'] = format_date(date('Y-m-d', strtotime($this->data['ClientAppointment']['date']))) . date(' H:i', strtotime($this->data['ClientAppointment']['date']));

        $this->__set_data($item_type);
        $this->render('owner_add');
    }

    function owner_dismiss($id , $item_type = 1) {
		if ( !empty ($_POST['ids'] )){
			$ids = $_POST['ids'];
			$id = $_POST['ids'][0];
		}else {
			$ids = [$id];
		}
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $is_ajax = $this->RequestHandler->isAjax();
        $this->loadModel('Post');
        $staff_id = getAuthOwner('staff_id');
        $row = $this->ClientAppointment->read(null, $id);
        if (!$row) {
            $this->flashMessage(__('Appointment not found', TRUE));
            $this->redirect(array('action' => 'index'));
        }

        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'));
        }


        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $this->loadModel('ItemStaff');
            $this->loadModel('Post');
            $client_conditions = array('ItemStaff.item_type' => $item_type, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'id,item_id', 'conditions' => $client_conditions)));
            if (!in_array($row['ClientAppointment']['item_id'], $client_list)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index'));
            }
        }
		$statuses = $this->ClientAppointment->getStatuses();
		$this->loadModel('FollowUpAction');
        $FollowUpActions = $this->FollowUpAction->getList($item_type);
		foreach ( $ids as $id ){
			$row = $this->ClientAppointment->read(null, $id);
			$this->ClientAppointment->id = $id;
			$this->ClientAppointment->saveField('status', ClientAppointment::Status_Dismissed);
			$status = ClientAppointment::Status_Dismissed;
			$this->ClientAppointment->saveField('status_date', date('Y-m-d H:i:s'));
			$status_date = date('Y-m-d H:i:s');
			$this->loadModel('Client');
			$client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
			$this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $row['ClientAppointment']['date'], 'param4' => $row['ClientAppointment']['date'], 'param5' => $FollowUpActions[$row['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
		}
        
        
        
        
        if ($is_ajax) {
            echo 'done';
            die();
        }

        $this->flashMessage(__('The appointment has been marked as dismissed', true), 'Sucmessage');
        if ($url_params['back_to_client']) {
            $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['ClientAppointment']['item_id'], '#' => 'AppointmentsBlock'));
        }
        $this->redirect(array('action' => 'index'));
    }

    function owner_done($id, $note = 0 , $item_type = 1) {
		if ( !empty ($_POST['ids'] )){
			$ids = $_POST['ids'];
			$id = $_POST['ids'][0];
		}else {
			$ids = [$id];
		}
        $is_ajax = $this->RequestHandler->isAjax();
        $staff_id = getAuthOwner('staff_id');
        $this->loadModel('Post');
        $row = $this->ClientAppointment->read(null, $id);
        if (!$row) {
            $this->flashMessage(__('Appointment not found', TRUE));
            $this->redirect(array('action' => 'index'));
        }

        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'));
        }


        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $this->loadModel('ItemStaff');
            $client_conditions = array('ItemStaff.item_type' => $item_type, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'id,item_id', 'conditions' => $client_conditions)));
            if (!in_array($row['ClientAppointment']['item_id'], $client_list)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index'));
            }
        }
		$statuses = $this->ClientAppointment->getStatuses();
		foreach ( $ids as $id ){
			
			$row = $this->ClientAppointment->read(null, $id);
			$this->ClientAppointment->id = $id;
			$this->ClientAppointment->saveField('status', ClientAppointment::Status_Done);
			$status = ClientAppointment::Status_Done;
			$status_date = date('Y-m-d H:i:s');
			$this->ClientAppointment->saveField('status_date',$status_date);
			
			$this->loadModel('Client');
			$client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
			
			$this->loadModel('FollowUpAction');
			$FollowUpActions = $this->FollowUpAction->getList($item_type);
			$this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $row['ClientAppointment']['date'], 'param4' => $row['ClientAppointment']['date'], 'param5' => $FollowUpActions[$row['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
		}
        
        if ($is_ajax) {
            echo 'done';
            die();
        }

        $this->flashMessage(__('The appointment has been marked as done', true), 'Sucmessage');
        if ($note == 1) {
            $url_params = $this->params['url'];
            $this->set('url_params', $url_params);
            if (isset($url_params['back_to_client'])) {
                $more['back_to_client'] = 1;
            }
            $this->redirect(array('controller' => 'posts', 'action' => 'post', $item_type, $row['ClientAppointment']['item_id'], 0, $id, '?' => $more));
        }
        $this->redirect(array('action' => 'index'));
    }

    function __set_data($item_type = 1) {

        foreach ($this->ClientAppointment->RecurringAppointment->period_list as $key => $value) {
            $periods[$key] = __(ucfirst($key), true);
        }

        $this->set('periods', $periods);

        $statuses = $this->ClientAppointment->getStatuses();

        $this->loadModel('Post');
        $this->loadModel('FollowUpAction');
        $actions = $this->FollowUpAction->getList($item_type);
        if (check_permission(Edit_General_Settings)) {
            $actions[] = array('name' => false, 'value' => false, 'data-divider' => "true");
            $actions[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> ' . __('Edit Actions List', true) . ' </span>', 'name' => __('Edit Actions List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
        }
        $this->set('actions', $actions);
        $this->set('statuses', $statuses);
    }

    function cron($site_id = null) {
        App::import('Vendor', 'Recurring');
        set_time_limit(360000);
        $this->autoRender = $this->autoLayout = false;
        $siteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));
        if (!$site_id) {
            $sites = $siteModel->find('all', array('conditions' => array('Site.id !=' => $site_id)));
        } else {
            $sites = $siteModel->find('all', array('conditions' => array('Site.id !=' => 44)));
        }
        $this->loadModel('Timezone');

        IzamDatabaseServiceProvider::boot(getPDO(),getPortalConfig());

        foreach ($sites as $site) {
            $GLOBALS['site'] = $site['Site'];
            $config = json_decode($site['Site']['db_config'], true);
            ConnectionManager::getDataSource('default')->swtich_db($config);
            $this->ClientAppointment->getDataSource()->swtich_db($config);

            IzamDatabaseServiceProvider::setSiteConnection(getPdo());
            removePdo();

            $this->loadModel('Timezone');
            $zone = $this->Timezone->field('zone_name', array("Timezone.id" => $site['Site']['timezone']));
            date_default_timezone_set($zone);
            $currDate = date('Y-m-d');


            if (true) {

                $RecurringAppointments = $this->ClientAppointment->RecurringAppointment->find('all', array('conditions' => array('RecurringAppointment.active' => 1, 'OR' => array('RecurringAppointment.end_date' => '0000-00-00', 'AND' => array('RecurringAppointment.end_date >= ' => $currDate)))));

                foreach ($RecurringAppointments as $RecurringAppointment) {
                    $id = $RecurringAppointment['RecurringAppointment']['id'];
                    $period = $RecurringAppointment['RecurringAppointment']['period_unit'];
                    $period_count = $RecurringAppointment['RecurringAppointment']['unit_count'];
                    $last_generated_date = $RecurringAppointment['RecurringAppointment']['last_generated_date'];
                    $this->ClientAppointment->RecurringAppointment->id = $id;
                    if(isset($RecurringAppointment['RecurringAppointment']['branch_id'])){
                        setRequestCurrentBranch($RecurringAppointment['RecurringAppointment']['branch_id']);
                    }
                    if (!strtotime($last_generated_date)) {
                        $this->ClientAppointment->RecurringAppointment->saveField("is_ended", 1, false);
                        continue;
                    }


                    $previous_dates = $this->ClientAppointment->find('list', array('fields' => array('ClientAppointment.id', 'ClientAppointment.date'), 'conditions' => array('ClientAppointment.recurring_appointment_id' => $id)));
                    $fromDate = $last_generated_date;
                    $previous_dates[] = $fromDate;
                    $fromDate = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);
                    $count = 0;
                    $nextapp = strtotime($currDate . ' +' . $period_count . ' ' . $period);
                    $nextapp = date("Y-m-d", $nextapp);
                    debug($nextapp);
                    if (strtotime($fromDate) > 0)
                        while ($fromDate <= $nextapp && (!strtotime($RecurringAppointment['RecurringAppointment']['end_date']) || $fromDate <= $RecurringAppointment['RecurringAppointment']['end_date'])) {

                            $count++;
                            if ($count > 50)
                                break;
                            $Find = $this->ClientAppointment->query("SELECT `ClientAppointment`.*  FROM `client_appointments` AS `ClientAppointment` WHERE `ClientAppointment`.`recurring_appointment_id` = $id   ORDER BY `ClientAppointment`.`id` DESC LIMIT 1", false);
                            $Find[0]['ClientAppointment']['status'] = ClientAppointment::Status_Scheduled;
                            $Find[0]['ClientAppointment']['date'] = $fromDate . ' ' . date("H:i:s", strtotime($Find[0]['ClientAppointment']['date']));
                            unset($Find[0]['ClientAppointment']['id']);
                            unset($Find[0]['ClientAppointment']['created']);
                            unset($Find[0]['ClientAppointment']['modified']);

                            $this->ClientAppointment->create();
                            $this->ClientAppointment->save($Find[0]['ClientAppointment']);
                            //   $this->add_actionline($Find[0]['ClientAppointment']['is_income'] ? ACTION_ADD_RECURRING_INCOME : ACTION_ADD_RECURRING_EXPENSE, array('primary_id' => $this->ClientAppointment->id, 'param1' => $Find[0]['ClientAppointment']['amount'], 'param2' => $Find[0]['ClientAppointment']['category'], 'param3' => $Find[0]['ClientAppointment']['vendor'], 'param6' => $Find[0]['ClientAppointment']['date'], 'staff_id' => -2));
                            $this->ClientAppointment->RecurringAppointment->saveField("last_generated_date", $fromDate, false);
                            $fromDate = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);
                        }
                }
                Configure::delete('cron_branch_id');
                if (strtotime($RecurringAppointment['RecurringAppointment']['end_date']) > 0 && $fromDate >= $RecurringAppointment['RecurringAppointment']['end_date'])
                    $this->ClientAppointment->RecurringAppointment->saveField("is_ended", 1, false);
            }
        }


        $this->autoRender = $this->autoLayout = false;
    }

}
?>
