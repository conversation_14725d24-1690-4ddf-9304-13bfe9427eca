<?php
class CostCentersJournalAccountsController  extends AppController {

	var $name = 'CostCentersJournalAccounts';

	/**
	 * @var CostCenter
	 */
	var $CostCentersJournalAccounts;
	var $helpers = array('Html', 'Form');

	function owner_export(){
		if (!check_permission(VIEW_COST_CENTERS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if(!empty($_POST['ids']))
		{
			$conditions['CostCentersJournalAccount.id'] = $_POST['ids'];
		}else{
			$conditions = $this->Session->read('CostCentersJournalAccount_Filter');

		}
		unset($conditions['ext']);
		unset($conditions['debug']);
		$this->paginate['CostCentersJournalAccount']['limit'] = 9999999;
		$journal_accouns = $this->paginate('CostCentersJournalAccount', $conditions);
		$this->set('owner', getAuthOwner());
		$this->set('report_title',__('Journal Accounts With Cost Centers',true));
		$this->set('journal_accounts', $journal_accouns);
		$this->layout = '';
	}

	function owner_index() {
		
		 if (!check_permission(VIEW_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		$this->CostCentersJournalAccount->recursive = 0;
		$conditions = $this->_filter_params();
//		$order =  ['CostCentersJournalAccount.journal_account_id' => 'DESC'];
		$this->paginate['CostCentersJournalAccount']['order'] =  ['CostCentersJournalAccount.journal_account_id' => 'DESC'];
		$cost_center_accounts = $this->paginate('CostCentersJournalAccount', $conditions, $order);
//		die(Debug($cost_center_accounts));
		$this->set('cost_center_accounts', $cost_center_accounts);
	}

	
}
?>
