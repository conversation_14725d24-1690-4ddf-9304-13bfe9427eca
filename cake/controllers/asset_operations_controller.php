<?php

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;

App::import('Vendor', 'settings');

class AssetOperationsController extends AppController {

    var $name = 'AssetOperations';
//    var $validate_schema;

   
    function beforeFilter() {
        parent::beforeFilter();
        $this->loadModel ( 'Asset');
        $this->loadModel ( 'AssetDeprecation');
    }
    function __settings ( ) {
        $this->loadModel('Asset');
        $assets = $this->Asset->find ( 'list');
        $deprecation_methods = AssetDeprecation::getDeprecationMethods () ;
        $this->set ( 'assets' , $assets );
        $this->set ( 'deprecation_methods' , $deprecation_methods );
    }
    
    function owner_index ( $asset_id = null ) {
        $conditions = [] ; 
        if ( !empty( $asset_id  )){
            $conditions['AssetOperation'] = $asset_id ; 
        }
        $this->paginate['order'] = 'AssetOperation.date DESC , AssetOperation.id DESC';
        
        if ( !empty ( $this->params['url']['asset_id']))
        {
            $conditions['asset_id'] = intval ($this->params['url']['asset_id'] ) ;
        }
        $asset_operations = $this->paginate ( 'AssetOperation' ,  $conditions) ;
        $this->set ( 'asset_operations' , $asset_operations ) ;
//        dd ( $asset_operations ) ;
        
    }
    /** 
     * Form for adding an asset Depreciation
     */
    function owner_add ( $asset_id = null  )  
    {
       if ( !empty ( $this->data ) ){
           if ( $asset_deprecation_id = $this->AssetDeprecation->add ( $this->data , $validation_errors )) {
               $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset Depreciation', true)), 'Sucmessage');
               $this->redirect (Router::url(['controller' => 'assets',  'action' => 'view' , $this->data['AssetDeprecation']['asset_id'] ]) ) ;
           } else {
               $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset Depreciation', true)));
           }
       }
       if ( !empty ( $asset_id ) ) {
           $this->data['AssetDeprecation']['asset_id'] = intval ( $asset_id   ) ; 
       }
       $this->__settings ( ) ;
    }
    function owner_write_off ( $asset_id ) {
        $asset = $this->Asset->findById ( $asset_id ) ;
        if ( empty ( $asset ) ) {
            $this->flashMessage(sprintf ( __('%s not found', TRUE) , __("Asset" , true)) );
            $this->redirect(array('action' => 'index'));die ; 
        }
        if (!$this->AssetOperation->check_date_integrity ( date('Y-m-d H:i:s'),$asset_id) ){
            $this->flashMessage(__("An Error occurred",true));
            $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $asset_id])); die ; 
        }
        if(!empty($this->data)) {

            if(!$this->data['AssetOperation']['write_off_account']){ 
                $this->flashMessage(__("Write off account is required",true) ) ;
                $this->redirect (Router::url(['controller' => 'asset_operations' , 'action' => 'write_off' , $asset_id])); die ; 
            }

            if ( true or !in_array($asset['Asset']['asset_status'] , [Asset::STATUS_DEPRECATED ,Asset::STATUS_SOLD ] )){
    //             dd($this->AssetOperation->id );
                $this->data['AssetOperation']['asset_id'] = $asset_id; 
                $this->data['AssetOperation']['type'] = AssetOperation::TYPE_WRITE_OFF; 
                $this->data['AssetOperation']['asset_current_before'] = $asset['Asset']['asset_current_value'] ; 
                $this->data['AssetOperation']['asset_current_after'] = 0;
                $this->validate_open_day(date('Y-m-d H:i:s'));
                if($this->data['AssetOperation']['write_off_account']) 
                {
                    $assetWriteOffAccountRoute =  [
                    "entity_type" => Util::ASSET_WRITE_OFF_ACCOUNT,
                    "account_id" => $this->data['AssetOperation']['write_off_account'],
                    "account_type" => "1"
                    ];
                    RouteSaverFactory::getSaver(Util::ASSET_WRITE_OFF_ACCOUNT)->save($assetWriteOffAccountRoute, $asset_id);   
                } 

                $this->AssetOperation->create ( ) ;
                if ( $this->AssetOperation->save ($this->data) ){
                    $this->flashMessage(__("Asset was written off successfully",true), 'Sucmessage');
                    $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $asset_id])); die ; 
                }else {
                    $this->flashMessage(__("An Error occurred",true) ) ;
                    $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $asset_id])); die ; 
                }
                
            }else{
                $this->flashMessage(__("An Error occurred",true));
                $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $asset_id])); die ; 
            }
        }else{
           // Get Default Account . 
            $this->loadModel('JournalAccount');
            $defaultAccount = $this->JournalAccount->find ( 'first' , ['recursive' => -1 , 'conditions' => ['entity_type' => 'other_debit' ] ]);
   
            $this->set ('default_account' ,$defaultAccount['JournalAccount']['id']);
            $this->set ('asset_id' ,$asset_id);
            $this->data['Asset'] = $asset['Asset'];
        }
    }
    function owner_re_evaluate ( $asset_id ) {
        $asset = $this->Asset->findById ( $asset_id ) ;
        
        if ( empty ( $asset ) ) {
            $this->flashMessage(sprintf ( __('%s not found', TRUE) , __("Asset" , true)) );
            $this->redirect(array('action' => 'index'));die ; 
        }
//        if ( in_array($asset['Asset']['asset_status'] , [Asset::STATUS_DEPRECATED] )){
        if ( !empty ( $this->data ) ) {
           $this->validate_open_day($this->data['AssetOperation']['date']?:date('Y-m-d H:i:s'));

           $this->data['AssetOperation']['asset_id'] = $asset_id;
           $this->data['AssetOperation']['type'] = AssetOperation::TYPE_RE_EVALUATE; 
           $this->data['AssetOperation']['asset_current_before'] = $asset['Asset']['asset_current_value'] ?? 0;
           $this->data['AssetOperation']['asset_current_after'] = $this->data['AssetOperation']['value']; 
           $this->data['AssetOperation']['date'] .= date(' H:i:s');
           $this->AssetOperation->create ( ) ;
           if ($this->AssetOperation->check_date_integrity ( $this->AssetOperation->formatDateTime($this->data['AssetOperation']['date']),$this->data['AssetOperation']['asset_id'] ) &&  $this->AssetOperation->save ( $this->data ) ){

           $this->handle_revaluate_auto_account($this->data,$this->AssetOperation->id);

            App::import('Controller', 'Assets');
                $controller = new AssetsController();
                $controller->cron(getCurrentSite('id'), $asset_id, true);
               $this->flashMessage(__("Asset re-evaluated successfully",true), 'Sucmessage');
               $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $asset_id])); die ; 
           }else{
               $this->flashMessage(__("An Error occurred",true) ) ;
           }
       }
//        }else{
//            $this->flashMessage(__("An Error occurred",true));
//            $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $asset_id])); die ; 
//        }
        $defaultQuantity = $this->Asset->getRemainingQuntity($asset['Asset']['id']);
        $this->set('defaultQuantity', $defaultQuantity);
        $this->set('asset_id', $asset_id);
        $this->data['Asset'] = $asset['Asset'];
    }
    
    function owner_delete ( $operation_id ) {
        //Reinit asset operation because the settings were overriden
        $this->AssetOperation = new AssetOperation(); 
        $operation = $this->AssetOperation->findById ( $operation_id ) ;
        if ( empty ( $operation ) ) {
            $this->flashMessage(sprintf ( __('%s not found', TRUE) , __("Operation" , true)) );
            $this->redirect(array('controller' => 'assets', 'action' => 'index'));die ; 
        }
        if ( $this->AssetOperation->have_operations_after ( $operation_id ) ) {
            $errorMessage = __("Can't delete this operation because there are other operations after it", true);
            $result = $this->AssetOperation->get_operations_after($operation_id);
            if ($result['operation']) {
                $errorMessage .= "<br>". __("Asset Operation", true) ." " . "<a href='/owner/asset_operations/edit_sell/" . $result['operation']['AssetOperation']['id'] . "' target='_blank'>" . $result['operation']['AssetOperation']['id'] . "</a>";
            }
            if ($result['deprecation']) {
                $errorMessage .= "<br>". __("Asset Depreciation", true) ." <a href='/v2/owner/asset_deprecations/view/" . $result['deprecation']['AssetDeprecation']['id'] . "' target='_blank'>" . $result['deprecation']['AssetDeprecation']['id'] . "</a>";
            }
            $this->flashMessage(__($errorMessage, TRUE) );
            $this->redirect(array('controller' => 'assets', 'action' => 'view' , $operation['AssetOperation']['asset_id'].'#OperationsBlock'));die ;
        }
        $this->validate_open_day($operation['AssetOperation']['date']);

        $this->loadModel('Asset');
        $asset = $this->Asset->findById ($operation['AssetOperation']['asset_id']);
        $this->Asset->add_action(AssetOperation::$operations[$operation['AssetOperation']['type']]['delete_action'] , $asset , $operation['AssetOperation']['value']);
        $this->AssetOperation->delete ( $operation_id ) ;
        $this->flashMessage(sprintf(__('%s has been deleted', true), AssetOperation::$operations[$operation['AssetOperation']['type']]['name']), 'Sucmessage');
        $this->redirect(['controller' =>  'assets' ,'action' => 'view',$operation['AssetOperation']['asset_id']]);
    }

    function owner_sell($asset_id)
    {
        $this->loadModel('Tax');
        $taxes = $this->Tax->getTaxList();

        $asset = $this->Asset->findById($asset_id);
        if (empty($asset)) {
            $this->flashMessage(sprintf(__('%s not found', TRUE), __("Asset", true)));
            $this->redirect(array('action' => 'index'));
            die;
        }
        if (in_array($asset['Asset']['asset_status'], [Asset::STATUS_SOLD, Asset::STATUS_DEPRECATED])) {
            $this->flashMessage(__("An Error occurred", true));
            $this->redirect(array('action' => 'index'));
            die;
        }
        if (!empty($this->data)) {
            $this->AssetOperation->validateQuantityForSelling();

            $this->validate_open_day($this->data['AssetOperation']['date'] ?: date('Y-m-d H:i:s'));

            if (empty($this->data['AssetOperation']['journal_account_id']) || empty($this->data['AssetOperation']['value'])) {
                $this->flashMessage(__("Please, enter the value for all required inputs marked with \"*\"", true));
                $this->redirect(Router::url(['controller' => 'asset_operations', 'action' => 'sell', $asset_id]));
                die;
            }

            $this->data['AssetOperation']['asset_id'] = $asset_id;
            $this->data['AssetOperation']['type'] = AssetOperation::TYPE_SELL;
            $this->data['AssetOperation']['asset_current_before'] = $asset['Asset']['asset_current_value'];
            if ($this->Asset->getRemainingQuntity($asset_id)) {
                $this->data['AssetOperation']['asset_current_after'] = $asset['Asset']['asset_current_value'] * ($this->Asset->getRemainingQuntity($asset_id) - $this->data['AssetOperation']['quantity']) / $this->Asset->getRemainingQuntity($asset_id);
            } else {
                $this->data['AssetOperation']['asset_current_after'] = 0;
            }
            $this->AssetOperation->create();

            $check_date_integrity = $this->AssetOperation->check_date_integrity($this->AssetOperation->formatDate($this->data['AssetOperation']['date']), $this->data['AssetOperation']['asset_id']);
            if ($check_date_integrity &&  $this->AssetOperation->save($this->data)) {
                $this->flashMessage(__("Asset Sold Successfully", true), 'Sucmessage');
                $this->redirect(Router::url(['controller' => 'assets', 'action' => 'view', $asset_id]));
                die;
            } else {
                $message = __("An Error occurred", true);
                if ($check_date_integrity == false) {
                    $message = __t('Error in date Please check the date of this asset deprecation');
                }
                $this->flashMessage($message);
            }
        }
        $this->data['Asset'] = $asset['Asset'];
        $this->set('taxes', $taxes);
        $defaultQuantity = $this->Asset->getRemainingQuntity($asset['Asset']['id']);
        $this->set('defaultQuantity', $defaultQuantity);
    }

    function owner_edit_write_off ( $op_id ) {
        $operation = $this->AssetOperation->find('first' , ['recursive' => -1 ,  'conditions' => ['AssetOperation.id' => $op_id] ]);
        $this->redirect ( Router::url(['controller' => 'assets' ,'action' => 'view' , $operation['AssetOperation']['asset_id'],"#OperationsBlock" ]));
    }
    function owner_edit ( $op_id ) {
        $operation = $this->AssetOperation->find('first' , ['recursive' => -1 ,  'conditions' => ['AssetOperation.id' => $op_id] ]);
        $this->redirect(
                Router::url(['controller' => 'asset_operations'  , 'action' => AssetOperation::$operations[$operation['AssetOperation']['type']]['edit_url'] , $op_id  ]) 
                ); die ; 
    }
    function owner_edit_re_evaluate ( $asset_operation_id ) {
        
        $operation = $this->AssetOperation->findById($asset_operation_id);
        if ( empty ( $operation ) ) {
            $this->flashMessage(sprintf ( __('%s not found', TRUE) , __("Operation" , true)) );
            $this->redirect(array('action' => 'index'));die ; 
        }
        if ( $this->AssetOperation->have_operations_after ( $asset_operation_id ) ) {
            $this->flashMessage(__("An Error occurred",true) ) ;
            $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $operation['AssetOperation']['asset_id'] ])); die ; 
        }
        $this->validate_open_day($operation['AssetOperation']['date']);
//        if ( in_array($asset['Asset']['asset_status'] , [Asset::STATUS_DEPRECATED] )){
        if ( !empty ( $this->data ) ) {
            $this->data['AssetOperation']['id'] = $this->AssetOperation->id = $asset_operation_id;
            $this->data['AssetOperation']['asset_current_after'] = $this->data['AssetOperation']['value'];
           if ($this->AssetOperation->check_date_integrity ( $this->AssetOperation->formatDateTime($this->data['AssetOperation']['date']),$operation['AssetOperation']['asset_id'] , $asset_operation_id) &&  $this->AssetOperation->save ( $this->data ) ){
          
           $this->handle_revaluate_auto_account($this->data,$asset_operation_id);
            
            $this->flashMessage(__("Asset re-evaluated successfully",true), 'Sucmessage');
               $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $operation['AssetOperation']['asset_id']])); die ; 
           }else{
               $this->flashMessage(__("An Error occurred",true) ) ;
           }
       }
        //        }else{
        //            $this->flashMessage(__("An Error occurred",true));
        //            $this->redirect (Router::url(['controller' => 'assets' , 'action' => 'view' , $asset_id])); die ; 
        //        }
        $this->loadModel('Journal');
        $reval_account = $this->Journal->get_auto_account(['entity_type' => Util::ASSET_RE_EVALUATION_ACCOUNT, 'entity_id' => $asset_operation_id]);
        if ($reval_account) {
            $operation['AssetOperation']['re_evaluate_account'] = $reval_account['JournalAccount']['id'];
        }
        $this->set ( 'asset_id' ,$operation['AssetOperation']['asset_id']) ;
        $this->data['Asset'] = $operation['Asset'];
        $this->data['AssetOperation'] = $operation['AssetOperation'];
        $this->data['AssetOperation']['date'] = format_date($operation['AssetOperation']['date']);
        $this->render('owner_re_evaluate');
    }

    function owner_edit_sell($operation_id)
    {
        $operation = $this->AssetOperation->findById($operation_id);
        $asset = $this->Asset->findById($operation['AssetOperation']['asset_id']);

        if (empty($operation)) {
            $this->flashMessage(sprintf(__('%s not found', TRUE), __("Operation", true)));
            $this->redirect(array('action' => 'index'));
            die;
        }
        if ($this->AssetOperation->have_operations_after($operation_id)) {
            $this->flashMessage(__("An Error occurred", true));
            $this->redirect(Router::url(['controller' => 'assets', 'action' => 'view', $operation['AssetOperation']['asset_id']]));
            die;
        }
        if (!empty($this->data)) {
            $this->data['AssetOperation']['asset_id'] = $operation['AssetOperation']['asset_id'];
            $this->AssetOperation->validateQuantityForSelling($operation_id);

            $this->validate_open_day($this->data['AssetOperation']['date'] ?: date('Y-m-d H:i:s'));

            if (empty($this->data['AssetOperation']['journal_account_id']) || empty($this->data['AssetOperation']['value'])) {
                $this->flashMessage(__("Please, enter the value for all required inputs marked with \"*\"", true));
                $this->redirect(Router::url(['controller' => 'asset_operations', 'action' => 'edit_sell', $operation_id]));
                die;
            }
            $this->data['AssetOperation']['type'] = AssetOperation::TYPE_SELL;
            $this->data['AssetOperation']['id'] =  $this->AssetOperation->id  = $operation_id;
            $asset_id = $operation['AssetOperation']['asset_id'];
            $this->data['AssetOperation']['asset_current_after'] = ($this->Asset->getRemainingQuntity($asset_id, $operation_id) > 0) ? $operation['AssetOperation']['asset_current_before'] * ($this->Asset->getRemainingQuntity($asset_id, $operation_id) - $this->data['AssetOperation']['quantity']) / $this->Asset->getRemainingQuntity($asset_id, $operation_id) : 0;
            
            $this->Asset->id = $asset_id;
            if ($this->Asset->getQuntity($asset_id) != $this->AssetOperation->getSoldQuantity($asset_id)) {
                $this->Asset->saveField('asset_current_value', $this->data['AssetOperation']['asset_current_after']);
                $this->Asset->saveField('asset_status', Asset::STATUS_DEPRECATED);
            }
            //            if ( $this->Asset->sell_asset ( $asset_id , $this->data['Asset']['sell_price'] ,$this->data['Asset']['journal_account_id'] ) ){
            if ($this->AssetOperation->check_date_integrity($this->AssetOperation->formatDateTime($this->data['AssetOperation']['date']), $operation['AssetOperation']['asset_id'], $asset_operation_id) &&  $this->AssetOperation->save($this->data)) {
                $this->flashMessage(__("Asset Sold Successfully", true), 'Sucmessage');
                $this->redirect(Router::url(['controller' => 'assets', 'action' => 'view', $operation['AssetOperation']['asset_id']]));
                die;
            } else {
                $this->flashMessage(__("An Error occurred", true));
            }
        }

        $this->data['Asset'] = $operation['Asset'];
        $this->data['AssetOperation'] = $operation['AssetOperation'];
        $this->data['AssetOperation']['date'] = format_date($operation['AssetOperation']['date']);

        $this->loadModel('Tax');
        $taxes = $this->Tax->getTaxList();
        $this->set('taxes', $taxes);
        $this->render('owner_sell');
        //        $this->set ( 'asset' , $asset ) ;
    }


    function handle_revaluate_auto_account($data, $asset_operation_id)
    {
        $this->loadModel('Journal');
        $update_journal = false;
        if ($data['AssetOperation']['re_evaluate_account']) {
            $assetDepressionAccountRoute =  [
                "entity_type" => Util::ASSET_RE_EVALUATION_ACCOUNT,
                "account_id" => $data['AssetOperation']['re_evaluate_account'],
                "account_type" => "1"
            ];
            RouteSaverFactory::getSaver(Util::ASSET_RE_EVALUATION_ACCOUNT)->save($assetDepressionAccountRoute, $asset_operation_id);
            $update_journal = 1;
        } else {

            $reval_account = $this->Journal->get_auto_account(['entity_type' => Util::ASSET_RE_EVALUATION_ACCOUNT, 'entity_id' => $asset_operation_id]);
            if ($reval_account) {
                $this->loadModel('JournalAccount');
                $defaultAccount = $this->JournalAccount->find('first', ['recursive' => -1, 'conditions' => ['entity_type' => 'other_debit']]);
                $defaultAccount = $this->Journal->get_auto_account(['entity_type' => Util::ASSET_RE_EVALUATION_ACCOUNT, 'entity_id' =>  0]);
                if (!$defaultAccount) {
                    $defaultAccount['JournalAccount']['id'] = $this->Journal->create_auto_account(['entity_type' => Util::ASSET_RE_EVALUATION_ACCOUNT, 'entity_id' => 0]);
                }
                $assetDepressionAccountRoute =  [
                    "entity_type" => Util::ASSET_RE_EVALUATION_ACCOUNT,
                    "account_id" => $defaultAccount['JournalAccount']['id'],
                    "account_type" => "1"
                ];
                RouteSaverFactory::getSaver(Util::ASSET_RE_EVALUATION_ACCOUNT)->save($assetDepressionAccountRoute, $asset_operation_id);
                $update_journal = 1;
            }
         
        }
        if ($update_journal) {
            $assetOperation = $this->AssetOperation->read(null, $asset_operation_id);
            $this->AssetOperation->update_journals($assetOperation);
        }
    }
}
