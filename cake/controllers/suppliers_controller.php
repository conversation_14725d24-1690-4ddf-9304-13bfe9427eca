<?php

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use App\Transformers\ServiceModelDataTransformer;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\AppEntitiesFormErrorHandlerDecorator;
use Izam\Daftra\Common\Queue\EventListenerMapper;
use Izam\Daftra\Common\Queue\EventTypeUtil;
use Izam\Daftra\Common\RelatedEntities\ViewChildEntitiesHelper;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Supplier\Services\SupplierService;
use Izam\Attachment\Service\AttachmentsService;
use App\Services\CreditDistribution\SupplierCreditDistributionService;
use Izam\Attachment\Models\EntityAttachment;
use Izam\Entity\Helper\EntityActivityLog;

/**
 * @property Supplier $Supplier
 * @property Currency $Currency
 * @property PurchaseOrder $PurchaseOrder
 */
class SuppliersController extends AppController {

    var $name = 'Suppliers';

    /**
     * @var Supplier AppModel
     * @var RequestHandler
     */
    var $Supplier;
    var $helpers = array('Html', 'Form');
    var $components = array('Cookie', 'RequestHandler', 'Email', 'SysEmails', 'Lmail', 'SupplierValidation');
	

    function owner_json_find($version = 'v1') {
        Configure::write('debug', 0);
        if (!empty($_GET['q'])) {
            $value = mysql_escape_string($_GET['q']);
            $suppliers = array();
            $conditions = array();
            if (!check_permission(View_All_Suppliers) && check_permission(View_his_own_Suppliers)) {
                $conditions['Supplier.staff_id'] = getAuthOwner('staff_id');
            }

            if (check_permission(View_All_Suppliers) || check_permission(View_his_own_Suppliers)) {
                $conditions['OR'] = array("CONCAT(Supplier.first_name,' ',Supplier.last_name) like '$value%'", "CONCAT(Supplier.last_name,' ',Supplier.first_name) like '% $value%'", "Supplier.business_name like '$value%'", "Supplier.business_name like '% $value%'", "Supplier.email like '$value%'", "Supplier.phone1 like '$value%'", "Supplier.phone2 like '$value%'", "Supplier.supplier_number like '%$value%'");
                if(isset($this->params['url']['source-action']) && in_array($this->params['url']['source-action'],['new-purchase-order','new-purchase-order-credit-note'])){
                    $conditions[]='(Supplier.suspend is null or Supplier.suspend=0)';
                }
                $suppliers = $this->Supplier->find('all', array('conditions' => $conditions, 'order' => 'Supplier.business_name asc', 'recursive' => -1));

                $result = array();
                foreach ($suppliers as $supplier) {
                    $full_name = '';
                    if (!empty($supplier['Supplier']['first_name']) || !empty($supplier['Supplier']['last_name']))
                        $full_name = $supplier['Supplier']['first_name'] . ' ' . $supplier['Supplier']['last_name'];
                    if ($full_name == $supplier['Supplier']['business_name'])
                        unset($full_name);

                    if ($version == 'v2') {
                        $result[] = array(
                            'text' => ('#' . $supplier['Supplier']['supplier_number'] . ', ' . $supplier['Supplier']['business_name'] . ' ' . (empty($full_name) ? '' : '(' . $full_name . ') ') ),
                            'img' => AvatarURLGenerator::generate(
                                $supplier['Supplier']['business_name'],
                                $supplier['Supplier']['id'],
                                30, null
                            ),
                            'id' => $supplier['Supplier']['id'],
                        );
                        continue;
                    }

                    $result[] = array(
                        'text' => ('#' . $supplier['Supplier']['supplier_number'] . ', ' . $supplier['Supplier']['business_name'] . ' ' . (empty($full_name) ? '' : '(' . $full_name . ') ') ),
                        'id' => $supplier['Supplier']['id'],
                        'details' => ''
                    );
                }
            }

            $result = array_map(function ($ele) {
                $ele['name'] = $ele['text'];
                return $ele;
            }, $result);
			if(!empty($result)) {
                if ($version != 'v2') {
                    array_unshift($result, array(
                        'text' => __('Please Select', true) . ':',
                        'id' => '',
                        'details' => ''
                    ));
                }
            }

            if ($version == 'v2') {
                echo json_encode(['results' => $result]);die();
            }

            echo json_encode($result);
            die();
        }
    }

    
    function owner_index() {
        if (!IS_REST) {
            if (!\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('suppliers')) {
                return $this->redirect('/v2/owner/entity/supplier/list');
            }
        }
        $this->Supplier->recursive = 0;

        $owner = getAuthOwner();
        $conditions = array();
        
        $results_count = $this->Supplier->query("select count(*) as results_count from suppliers");
        debug($results_count[0][0]["results_count"]);
        $this->set("results_count",$results_count[0][0]["results_count"]);

        $filter_conditions = $this->_filter_params();
        $conditions[] = $filter_conditions;
        if ($owner['staff_id'] != 0) {
            if (!check_permission(View_All_Suppliers) && !check_permission(View_his_own_Suppliers) && !check_permission(Add_New_Supplier)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
            if (!check_permission(View_All_Suppliers)) {
                $conditions['Supplier.staff_id'] = $owner['staff_id'];
            }
        }
        
        // $this->paginate['Supplier']['order'] = 'Supplier.supplier_number + 0';
        $this->paginate['Supplier']['order'] = 'Supplier.business_name';
        $this->Supplier->bindAttachmentRelation('supplier');

		$suppliers = $this->paginate('Supplier', $conditions);
        $this->set('suppliers', $suppliers);
    
        $this->set('staff_id', $owner['staff_id']);

        $this->loadModel('Country');
        $this->loadModel('Currency');


        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->set('site', $owner);


        $this->set('countryCodes', $this->Supplier->getCountryList());
        $this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());
        $this->set('statuses', array('' => __('Any', true), 0 => __('Active', true), 1 => __('Suspended', true)));

        $this->set('content', $this->get_snippet('suppliers'));

        $this->_filterLinks();
        $this->set('title_for_layout',  __('Suppliers', true));
		if(IS_REST){
			$this->set('rest_items', $suppliers);
			$this->set('rest_model_name', "Supplier");
			$this->render("index");
		}
    }

    function owner_suspend_users($suspend = 0, $id = false) {

        if (!check_permission(Edit_Delete_all_suppliers)) {
            $this->flashMessage(__("You are not allowed to edit this supplier", true), 'Errormessage', 'secondaryMessage');
            $this->redirectToIndex();
        }
        $this->_record_referer_path();

        if (!empty($id)) {
            $_POST['ids'] = array();
            $_POST['ids'][] = $id;
        }
        if (empty($_POST['ids']) || !is_array($_POST['ids'])) {
            $this->flashMessage(__('Invalid Suppliers', true));
            $referer_url = $this->_get_referer_path();
            $this->redirect($referer_url);
        }
        $site_id = getAuthOwner('id');
        $suppliers = $this->Supplier->find('all', array('recursive' => -1 ,'conditions' => array('Supplier.id' => $_POST['ids'])));
        if (empty($suppliers)) {
            $this->flashMessage(sprintf(__('%s not found', true), __('Supplier', true)));
            $referer_url = $this->_get_referer_path();
            $this->redirect($referer_url);
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {

            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes' && $this->Supplier->updateAll(array('Supplier.suspend' => $suspend), array('Supplier.id' => $_POST['ids']))) {
                $this->flashMessage(__('Supplier status updated', true), 'Sucmessage');
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            } else {
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            }
        }
        $this->set('action', empty($suspend) ? "Unsuspend" : "Suspend ");
        $this->set('suspend', $suspend);
        $this->set('suppliers', $suppliers);

        $this->set('title_for_layout',  __('Suspend Supplier', true));
    }

    function _filterLinks() {
        $params = $this->params['url'];
        unset($params['url'], $params['ext']);

        $filterLinks = array();

        foreach (array('name' => __('Supplier name', true), 'address' => __('Address', true), 'postal_code' => __('Postal code', true)) as $var => $title) {
            if (!empty($params[$var])) {
                $filterLinks[] = array(
                    'title' => $title,
                    'value' => $params[$var],
                    'var' => $var
                );
            }
        }

        if (isset($params['status'])) {
            if ($params['status'] == '0') {
                $filterLinks[] = array(
                    'title' => __('Status', true),
                    'value' => __('Active', true),
                    'var' => 'status'
                );
            } elseif ($params['status'] == '1') {
                $filterLinks[] = array(
                    'title' => __('Status', true),
                    'value' => __('Suspended', true),
                    'var' => 'status'
                );
            }
        }

        if (!empty($params['country_code'])) {
            $country = $this->Country->field('country', array('Country.code' => $params['country_code']));
            $filterLinks[] = array(
                'title' => __('Country', true),
                'value' => $country,
                'var' => 'country_code'
            );
        }

        $this->set(compact('filterLinks', 'params'));
    }

    //-----------------------------


    //---------------------------
    function _filter_params($params = false, $filters = [], $passedModelName = false) {
        $conditions = parent::_filter_params($params, $filters, $passedModelName);

        if (!empty($conditions['Supplier.name LIKE'])) {
            $conditions[] = "CONCAT_WS(' ', Supplier.first_name, Supplier.last_name, Supplier.business_name, Supplier.email) LIKE '{$conditions['Supplier.name LIKE']}'";
            unset($conditions['Supplier.name LIKE']);
        }

        if (!empty($conditions['Supplier.address LIKE'])) {
            $conditions[] = "CONCAT_WS(' ', Supplier.address1, Supplier.address2, Supplier.city, Supplier.state) LIKE '{$conditions['Supplier.address LIKE']}'";
            unset($conditions['Supplier.address LIKE']);
        }

        if (!empty($conditions['Supplier.status']) || (isset($conditions['Supplier.status']) && $conditions['Supplier.status'] == '0')) {
            $conditions[] = "Supplier.suspend = {$conditions['Supplier.status']}";
            unset($conditions['Supplier.status']);
        }



        return $conditions;
    }

//---------------------
    function owner_view($id = null) {
        $this->Supplier->bindAttachmentRelation('supplier');
        //get the supplier
        $supplier = $this->Supplier->find(array('Supplier.id' => $id));
        if(empty($supplier)) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('Supplier not found', true)));
            $this->flashMessage(__('Supplier not found', TRUE));
            $this->redirectToIndex();
        }
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(View_All_Suppliers) && $supplier['Supplier']['staff_id'] != $staff) || !check_permission(View_his_own_Suppliers)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to view this supplier !", true), 'Errormessage', 'secondaryMessage');
                $this->redirectToIndex();
            }
        }
        $this->set(compact('supplier'));

        //Basic settings
        $this->loadModel('Country');
        $this->loadModel('Language');
        $this->loadModel('Currency');
        $this->set('country', $this->Country->get_country_code($supplier['Supplier']['country_code']));
        $this->set('language', $this->Language->get_language_code($supplier['Supplier']['language_code']));
        $this->set('currency', $this->Currency->get_currency_code($supplier['Supplier']['default_currency_code']));

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());

		if(ifPluginActive(AccountingPlugin)){
			
        $this->Supplier->adjust_and_pay($id);
        
        }
		$this->loadModel('PurchaseOrderPayment');
        $sb=$this->PurchaseOrderPayment->find('first',array('applyBranchFind' => false, 'recursive'=>'-1','conditions'=>array('PurchaseOrderPayment.payment_method'=>'starting_balance','PurchaseOrderPayment.supplier_id'=>$id)));
        $this->set('sb',$sb);
        $this->set('supplier_starting_balance',isset($sb['PurchaseOrderPayment']) ? $sb['PurchaseOrderPayment'] : null);
        //PurchaseOrder
        $purchaseOrders = $this->Supplier->PurchaseOrder->getSupplierPurchaseOrders($id);
		$duePurchaseOrders = $this->Supplier->PurchaseOrder->getSupplierDuePurchaseOrders($id);

		$this->set('duePurchaseOrdersCount', count($duePurchaseOrders));
        $this->set('PurchaseOrderCount', count($purchaseOrders));
        $this->set('PurchaseOrders', $purchaseOrders);
		$this->set('statuses', PurchaseOrder::getStatuses());
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('supplier');

        if(!empty($printableTemplates)) {
			$this->set(compact('printableTemplates'));
        }

		
		$this->loadModel('ItemsTag');
		$this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_SUPPLIER);
		$tags = $this->ItemsTag->get_item_tags($id,ItemsTag::TAG_ITEM_TYPE_SUPPLIER,true);
		$this->set('tags',$tags);
        $Journal = GetObjectOrLoadModel('Journal');
        $account = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::SUPPLIER_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id]);

        $this->set ( $this->Supplier->get_statement_data($id)) ;
        $allt = "select 1 as my_order , created,'purchase_order',id,no,type,supplier_id,date,payment_status,id as purchase_order_id,summary_total,due_after,'payment_method',currency_code,'description' from purchase_orders where supplier_id='$id' and type in(0,5,6) and draft <> 1
            UNION select 1 as my_order , created,'Starting Balance',id,null,0,'$id',date,status,id as purchase_order_id,amount,'due_after','payment_method',currency_code,null from purchase_order_payments where supplier_id='$id' and payment_method = 'starting_balance'
            UNION select 2 as my_order,created,'payment',id,(select no from purchase_orders where id=purchase_order_id),null,supplier_id,date,status,purchase_order_id,amount,null,payment_method,currency_code,null from purchase_order_payments where (purchase_order_id in(select id from purchase_orders where supplier_id='$id' and type in(0,5) and draft <> 1) or supplier_id='$id' and purchase_order_id is null) and payment_method <> 'supplier_credit' and payment_method <> 'starting_balance' and status=1 
            UNION SELECT 1 AS my_order,JT.`created`,'journal',J.`id`,null,null,'$id',JT.`created`,1,JT.`id`,ROUND(JT.currency_credit-JT.currency_debit,2) AS `amount`,null,'none',JT.currency_code,JT.description FROM `journal_transactions` AS `JT`  LEFT JOIN `journals` AS `J` ON(`JT`.`journal_id` = `J`.`id`) WHERE J.draft = 0 and J.entity_type not in('purchase_order','purchase_order_payment','purchase_refund','debit_note', 'year_opening_balance','year_closing_balance') and JT.journal_account_id={$account['JournalAccount']['id']}
            UNION select 3 as my_order,created,'Refund Payment',id,(select no from purchase_orders where id=purchase_order_id),'6-1',supplier_id,date,status,'refund',amount,null,payment_method,currency_code,null from purchase_order_payments where (purchase_order_id in(select id from purchase_orders where supplier_id='$id' and type in(6) and draft <> 1))  ORDER BY `date` , my_order ASC,`created` ASC";
        $transaction_count = $this->Supplier->flat_query_results("select count(*)as cc from ( $allt )aaa");
        $this->set('transaction_count', $transaction_count['0']['cc']);

        $this->set('account', $account);
        $this->set('title_for_layout',  __('View Supplier', true));
		if(IS_REST){
			$this->set('rest_item', $supplier);
			$this->set('rest_model_name', "Supplier");
			$this->render("view");
		}

	    $has_permission_to_distribute = false;
	    if (!settings::getValue(InventoryPlugin, 'automatic_pay_po') && check_permission(Add_New_Purchase_Orders))
		    $has_permission_to_distribute = true;
	    $this->set('has_permission_to_distribute', $has_permission_to_distribute);

        $appEntitiesFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->set('forms', $appEntitiesFormHandler->show($id));
        $this->owner_balance($id);
        $this->set('paymentsCount',$this->getpaymentsCountBySupplierId($id));
        $this->set('forms', $this->createAdditionalFieldsFormHandlerInstance()->show($id));

        $builder = getEntityBuilder();
        $entity = $builder->buildEntity(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY);
        $this->set('tabActions', ViewChildEntitiesHelper::setTabs($id,getAuthOwner('staff_id'), $entity));
        $this->set('entity', $entity);
        $this->set('round', true);
    }

//---------------------
    function owner_add() {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        App::import('Vendor', 'settings');
        App::import('Vendor', 'notification');
        App::import('Vendor', 'sites_local');
        $this->set('bnf',  Localize::get_business_number_fields());
        
        if (!check_permission(Add_New_Supplier)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirectToIndex();
        }
        $site = getAuthOwner();


        $ajax = $this->RequestHandler->isAjax();

        if (!empty($this->data)) {
            if ($site['staff_id'] != 0) {
                $staff = getAuthStaff();

                $this->data['Supplier']['staff_id'] = $staff['id'];
            }
          
			$this->Supplier->set($this->data);
                    if(!empty($this->data['Supplier']['starting_balance']))
                    {
                        $this->validateOpenDayWithValidationError($this->data,'Supplier', 'starting_balance_date');
                    }
                    if(empty($this->Supplier->validationErrors))
                    {
                        if(
                            isset($this->data['JournalAccountRoute']['account_id']) &&
                            !empty($this->data['JournalAccountRoute']['account_id'])
                        ) {
                            $this->Supplier->disableAutoJournal = true;
                        }
                        $isAppEntitiesValid = $ajax || $additionalFieldsFormHandler->validate($this->data);

                        $result = $this->Supplier->saveSupplier($this->data, $isAppEntitiesValid);
                        $supplier_id = $this->Supplier->getLastInsertID();
                    }else{
                        $result['status'] = false;
                    }


					if ($result['status']) {
                        $attachments = $this->data['Supplier']['attachment'];
                        if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                        
                        $imagesIds = explode(',',$attachments);
                        if(!empty($imagesIds))
                        {
                            izam_resolve(AttachmentsService::class)->save('supplier', $this->Supplier->id, $imagesIds); 
                        }

                        $additionalFieldsFormHandler->store($supplier_id, $this->data);

                        izam_resolve(SupplierService::class)->insert(ServiceModelDataTransformer::transform($result['data'], 'Supplier'));
                        RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_SUPPLIER)
                            ->save($this->data['JournalAccountRoute'], $this->Supplier->id);
								if(!empty($this->data['Supplier']['starting_balance'])){
									$data=[];
								$data['PurchaseOrderPayment']['supplier_id']=$this->Supplier->id;
								$data['PurchaseOrderPayment']['amount']=$this->data['Supplier']['starting_balance']*-1;
								$data['PurchaseOrderPayment']['status']=1;
								$data['PurchaseOrderPayment']['payment_method']='starting_balance';
								$data['PurchaseOrderPayment']['staff_id']=  getAuthOwner('staff_id');
								$data['PurchaseOrderPayment']['added_by'] =0;
								$data['PurchaseOrderPayment']['date'] =$this->data['Supplier']['starting_balance_date'];
								$data['PurchaseOrderPayment']['manual_payment']=1;
								$data['PurchaseOrderPayment']['currency_code']=$this->data['Supplier']['default_currency_code'];
								 $result = $this->Supplier->PurchaseOrder->ownerAddPayment($data);
							}                
					//    $this->add_stats(STATS_CREATE_Supplier, array($supplier_id, $ajax));
					 //   $this->add_actionline(ACTION_ADD_Supplier, array('primary_id' => $supplier_id, 'secondary_id' => $supplier_id, 'param2' => $this->data['Supplier']['business_name'], 'param3' => $this->data['Supplier']['email'], 'param4' => $result['data']['supplier_number'], 'param5' => $this->data['Supplier']['phone1'] . ' ' . $this->data['Supplier']['phone2']));
						$this->add_actionline(ACTION_ADD_SUPPLIER, array('primary_id' => $supplier_id, 'secondary_id' => $supplier_id, 'param2' => $this->data['Supplier']['business_name'], 'param3' => $this->data['Supplier']['email'], 'param4' => $this->data['Supplier']['supplier_number'], 'param5' => $this->data['Supplier']['phone1'] . ' ' . $this->data['Supplier']['phone2']));

                        $newData = EntityActivityLog::getRecordWithEntityStructureWithCustomFields(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY, $supplier_id);
                        unset($newData['is_offline']);
                        EntityActivityLog::activityLogAdd(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY, $newData);
                     
						if(IS_REST){
							$this->set('id', $supplier_id);
							$this->render('created');
							return;
						}
						if ($ajax) {
							$supplier = $result['data'];
							$supplier['id'] = $supplier_id;
							die(json_encode(array('error' => false, 'errors' => false, 'supplier' => $supplier)));
						} else {
							$this->flashMessage(sprintf(__('%s has been saved', true), __('Supplier', true)), 'Sucmessage');
							$this->redirect(array('action' => 'view', $supplier_id));
						}
					} else {
                        if(!empty($this->data['Supplier']['attachment'])){                       
                            $filesId = explode(',',$this->data['Supplier']['attachment']);
                            $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                            $this->data['Attachments'] = $attachment;
                        }
					  //  $this->add_stats(STATS_CREATE_Supplier_ERROR, array(serialize($this->Supplier->validationErrors), serialize($this->Supplier->data)));
						if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Supplier->validationErrors]);
						if ($ajax) {
							die(json_encode(array('supplier' => false, 'error' => true, 'errors' => $this->Supplier->validationErrors)));
						} else {
							$this->flashMessage(__('Could not save supplier', true));
						}
					}
				 
			 
				 
			
        } else {
			App::import('Vendor', 'AutoNumber');
           $this->data['Supplier']['supplier_number'] = $this->data['Supplier']['hidden_supplier_number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_SUPPLIER);
           $this->data['Supplier']['starting_balance_date'] =format_date(date("Y-m-d"));
        }

        $this->set('addSupplierText', $this->get_snippet('add-supplier'));
        $this->loadModel('Invoice');
        $this->set('invoice_methods', Invoice::getMethods());

        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('Supplier'));

        $this->_settings($id);
        $this->set('title_for_layout',  __('Add Supplier', true));
    }


//---------------------
    function owner_edit($id = null) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        if (is_array(($this->js_lang_labels)) && is_array($this->invoice_js_labels)) { //php8 fix
            $this->js_lang_labels = array_merge($this->js_lang_labels, $this->invoice_js_labels);
        }
        $this->Supplier->bindAttachmentRelation('supplier');

        App::import('Vendor', 'settings');
        App::import('Vendor', 'notification');
        App::import('Vendor', 'sites_local');
        $this->set('bnf',  Localize::get_business_number_fields());
        $owner = getAuthOwner();

        $this->_record_referer_path();

        if ($this->RequestHandler->isAjax()) {
            $this->layout = '';
        }

        $site_id = getAuthOwner('id');
        $supplier = $this->Supplier->find(array('Supplier.id' => $id));
        if (!$supplier) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('Supplier not found', true)));
            $this->flashMessage(__('Supplier not found', true));
            $this->redirectToIndex();
        }
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(Edit_Delete_all_suppliers) && $supplier['Supplier']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_suppliers)) {
				if(IS_REST) $this->cakeError('error403');
                $this->izamFlashMessage(__("You are not allowed to edit this supplier", true), 'Errormessage', 'secondaryMessage');
                $this->redirectToIndex();
            }
        }
		if(IS_REST) $this->data["Supplier"]["id"] = $id;
		
        if (!empty($this->data)) {
            $oldData = EntityActivityLog::getRecordWithEntityStructureWithCustomFields(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY, $id, 3);

			$this->Supplier->set($this->data);

            $isAppEntitiesValid =  $additionalFieldsFormHandler->validate($this->data);

			$result = $this->Supplier->saveSupplier($this->data, $isAppEntitiesValid);
            $ajax=$this->RequestHandler->isAjax();
			if ($result['status']) {
                $attachments = $this->data['Supplier']['attachment'];
                if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                
                $imagesIds = explode(',',$attachments);
                if(!empty($imagesIds))
                {
                    izam_resolve(AttachmentsService::class)->save('supplier', $this->Supplier->id, $imagesIds); 
                }
                $additionalFieldsFormHandler->update($id, $this->data);

                $newData = EntityActivityLog::getRecordWithEntityStructureWithCustomFields(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY, $id, 3);
                EntityActivityLog::activityLogUpdate(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY, $newData, $oldData);

                JournalAccount::updateAccountName($this->data['Supplier'], 'supplier');
                izam_resolve(SupplierService::class)->update(ServiceModelDataTransformer::transform($result['data'], 'Supplier'));
                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_SUPPLIER)
                    ->save($this->data['JournalAccountRoute'], $id);
                
                $updates = array_diff_assoc($this->data['Supplier'], $supplier['Supplier']);
                $params = array('primary_id' => $this->data['Supplier']['id'], 'secondary_id' => $this->data['Supplier']['id'], 'param2' => $this->data['Supplier']['business_name'], 'param3' => $this->data['Supplier']['email'], 'param4' => $this->data['Supplier']['supplier_number'], 'param5' => $this->data['Supplier']['phone1'] . ' ' . $this->data['Supplier']['phone2']);
                $edits = [];
                foreach ($updates as $key => $update) {
                    if (!empty($update) && !in_array($key, ['bn1_label', 'bn2_label'])) {
                        $edits[] = [
                            'key' => $this->Supplier->labels[$key] ?? $key,
                            'old_value' => $supplier['Supplier'][$key],
                            'new_value' => $update
                        ];
                    }
                }
                $params['param6'] = json_encode($edits);
                $this->add_actionline(ACTION_UPDATE_SUPPLIER, $params);

				if(IS_REST){
					$this->render('success');
					return;
				}
				if ($ajax) {
					$supplier = $result['data'];
					die(json_encode(array('error' => false, 'errors' => false, 'supplier' => $supplier)));
				}else{
					$this->flashMessage(sprintf(__('%s  has been saved', true), __('Supplier', true)), 'Sucmessage');
					$referral_url = $this->_get_referer_path();
					$this->redirect($referral_url);
				}
			}else{
				if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Supplier->validationErrors]);
				if($ajax){
					die(json_encode(array('supplier' => false, 'error' => true, 'errors' => $this->Supplier->validationErrors)));
				}else{
					$this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Supplier', true)));
				}
			}
        }
        if(!empty($this->data['Supplier']['attachment'])){                       
            $filesId = explode(',',$this->data['Supplier']['attachment']);
            $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
            $this->data['Attachments'] = $attachment;
        }
        if (empty($this->data)) {
            $this->data = $supplier;
            unset($this->data['Supplier']['password']);
        }

        $this->set('addSupplierText', '');
        $this->loadModel('Invoice');
        $this->set('invoice_methods', Invoice::getMethods());

        $this->_settings($id);
        $this->set('title_for_layout',  __('Edit Supplier', true));
//		$this->Supplier->validationErrors['business_name'] = 'ttt';
//										dd($this->Supplier->validationErrors);

        $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('Supplier'));
        $this->render('owner_add');
    }

    function _settings($id = null) {
        $this->loadModel('Country');
        $this->loadModel('Language');
        $this->loadModel('Currency');

        $this->set('countryCodes', $this->Country->getCountryList());
        $this->set('languageCodes', $this->Language->getLanguageList());
        $this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());
        $Site = getObjectOrLoadModel("Site");
        $this->set('default_language', $Site->get_site_language());
        $this->set('default_currency', $Site->get_site_currency());
        $this->set('default_country', $Site->get_site_country());
        $this->loadModel('Journal');
        $this->setAccountRoute('suppliers_accounts_routing', Journal::SUPPLIER_ACCOUNT_ENTITY_TYPE, $id);
    }

//---------------------
    function owner_delete($id = null)
    {
      
        $this->_record_referer_path();

//If request type is post
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            if (IS_REST) {
                $this->cakeError("error400", ["message" => sprintf(__('Invalid %s', true), __('supplier', true))]);
            } else {
                $this->flashMessage(sprintf(__('Invalid %s', true), __('supplier', true)));
                $referer_url = !($this->Session->check('referer_url')) ? '/v2/owner/entity/supplier/list' : $this->Session->read('referer_url');
                $this->Session->delete('referer_url');
                $this->redirectToIndex();
            }
        }
        $module_name = __('supplier', true);
        if (is_countable($_POST['ids']) && count($_POST['ids']) > 1) { //php8 fix
            $module_name = __('suppliers', true);
        }

        $site_id = getAuthOwner('id');
        $suppliers = $this->Supplier->find('all', array('conditions' => array('Supplier.id' => $id)));
 
        if (empty($suppliers)) {
            if (IS_REST) {
                $this->cakeError("error404", ["message" => sprintf(__('%s not found', true), ucfirst($module_name))]);
            } else {
                $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
                $referer_url = $this->_get_referer_path();
                $this->redirectToIndex();
            }
        }
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = $owner['staff_id'];
            foreach ($suppliers as $supplier) {
                if ((!check_permission(Edit_Delete_all_suppliers) && $supplier['Supplier']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_suppliers)) {
                    if (IS_REST) $this->cakeError('error403');
                    $this->flashMessage(__("You are not allowed to edit this supplier", true), 'Errormessage', 'secondaryMessage');
                    $referer_url = $this->_get_referer_path();
                    $this->redirectToIndex();
                }
            }
        }
        if (IS_REST) {
            $_POST['submit_btn'] = true;
            $_POST['ids'] = [$id];
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {

            $id = $_POST['ids'];  
            if ($_POST['submit_btn'] == 'yes') {
                if (ifPluginActive(PURCHASE_CYCLE_PLUGIN)){

                    $this->loadModel('PurchaseOrder');

                    $hasPurchaseQuotations = $this->PurchaseOrder->find('first',array('conditions' => array('supplier_id' => $id,'PurchaseOrder.type' => [PurchaseOrder::PURCHASE_QUOTATION])));
                    if($hasPurchaseQuotations){
                        if (IS_REST) {
                            $this->cakeError("error404", ["message" => __("You cannot delete the Supplier as he’s already selected in the Purchase Quotation", true)." <a target='_blank' href='/v2/owner/entity/".EntityKeyTypesUtil::PURCHASE_QUOTATION."/".$hasPurchaseQuotations["PurchaseOrder"]["id"]."/show'>#{$hasPurchaseQuotations["PurchaseOrder"]["no"]}</a>"]);
                        } else {
                            $this->flashMessage(__("You cannot delete the Supplier as he’s already selected in the Purchase Quotation", true)." <a target='_blank' href='/v2/owner/entity/".EntityKeyTypesUtil::PURCHASE_QUOTATION."/".$hasPurchaseQuotations["PurchaseOrder"]["id"]."/show'>#{$hasPurchaseQuotations["PurchaseOrder"]["no"]}</a>", 'Errormessage', 'secondaryMessage');
                        }
                        $referer_url = $this->_get_referer_path();
                        $this->redirectToIndex();
                        exit;
                    }

                    $hasPurchaseOrders = $this->PurchaseOrder->find('first',array('conditions' => array('supplier_id' => $id,'PurchaseOrder.type' => [PurchaseOrder::PURCHASE_ORDER])));
                    if($hasPurchaseOrders){
                        if (IS_REST) {
                            $this->cakeError("error404", ["message" => __("You cannot delete the Supplier as he’s already selected in the Purchase Order", true)." <a target='_blank' href='/v2/owner/entity/".EntityKeyTypesUtil::PURCHASE_ORDER."/".$hasPurchaseOrders["PurchaseOrder"]["id"]."/show'>#{$hasPurchaseOrders["PurchaseOrder"]["no"]}</a>"]);
                        } else {
                            $this->flashMessage(__("You cannot delete the Supplier as he’s already selected in the Purchase Order", true)." <a target='_blank' href='/v2/owner/entity/".EntityKeyTypesUtil::PURCHASE_ORDER."/".$hasPurchaseOrders["PurchaseOrder"]["id"]."/show'>#{$hasPurchaseOrders["PurchaseOrder"]["no"]}</a>", 'Errormessage', 'secondaryMessage');
                        }
                        $referer_url = $this->_get_referer_path();
                        $this->redirectToIndex();
                        exit;
                    }
                }
               
                foreach ($suppliers as $supplier) {
                    if($this->Supplier->deleteAble($supplier['Supplier']['id'])) {
                        $suppliersData = [];
                        if (EventListenerMapper::hasEventListener(EventTypeUtil::SUPPLIER_DELETED)) {
                            $suppliersData = getRecordWithEntityStructure('supplier', $supplier['Supplier']['id'], 2)->toArray();
                        }
                        izam_resolve(SupplierService::class)->delete($suppliersData);
                        $this->Supplier->delete_with_related($supplier['Supplier']['id']);
                        $this->Supplier->delete_auto_accounts($supplier['Supplier']['id']);
                        EntityAttachment::where(['entity_key' => 'supplier', 'entity_id' => $supplier['Supplier']['id']])->delete();

                        $this->add_actionline(ACTION_DELETE_SUPPLIER, array('primary_id' => $supplier['Supplier']['id'], 'secondary_id' => $supplier['Supplier']['id'], 'param2' => $supplier['Supplier']['business_name'], 'param3' => $supplier['Supplier']['email'], 'param4' => $supplier['Supplier']['supplier_number'], 'param5' => $supplier['Supplier']['phone1'] . ' ' . $supplier['Supplier']['phone2']));
                    }else{
                        $referer_url = $this->_get_referer_path();
                        $this->loadModel('Expense');
                        if($expense = $this->Expense->find('first', ['conditions' => ['Expense.supplier_id' => $supplier['Supplier']['id']]])){
                            if (IS_REST) {
                                $this->cakeError("error404", ["message" => sprintf(__("You cannot delete the supplier that has already been selected for an expense %s", true), '<a target="_blank" href="' . Router::url(['controller' => 'expenses', 'action' => 'view', $expense['Expense']['id']]) . '">#' . $expense['Expense']['expense_number'] . '</a>')]);
                            } else {
                                $this->flashMessage(sprintf(__("You cannot delete the supplier that has already been selected for an expense %s", true), '<a target="_blank" href="' . Router::url(['controller' => 'expenses', 'action' => 'view', $expense['Expense']['id']]) . '">#' . $expense['Expense']['expense_number'] . '</a>'), 'Errormessage', 'secondaryMessage');
                            }
                        }
                        if (IS_REST) {
                            $this->cakeError("error404", ["message" => sprintf(__('Could not delete %s, Please check that you have deleted all %s transactions first.', true), ucfirst($module_name), ucfirst($module_name))]);
                        } else {
                            $this->flashMessage(sprintf(__('Could not delete %s, Please check that you have deleted all %s transactions first.', true), ucfirst($module_name), ucfirst($module_name)));
                        }
                        $this->redirect(Router::url(['action'=>'index']));
                    }
                }
                // $this->add_stats(STATS_REMOVE_CLIENT, array(serialize($id)));
                if (IS_REST) {
                    $this->set("message", sprintf(__('%s has been deleted', true), ucfirst($module_name)));
                    $this->render("success");
                    return;
                } else {
                    $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                    $this->redirectToIndex();
                }
            } else {
                if (IS_REST) $this->cakeError("error500", ["message" => sprintf(__('Could not delete %s, Please check that you have deleted all %s transactions first.', true), ucfirst($module_name), ucfirst($module_name))]);
                $referer_url = $this->_get_referer_path();
                $this->redirectToIndex();
            }
        }
        $this->set('suppliers', $suppliers);
        $this->set('module_name', $this->Supplier->name);
        $this->set('title_for_layout',  __('Delete Supplier', true));
    }
	function owner_delete_supplier($id = null){
//If request type is post
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) { 
			if(IS_REST){
				$this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('supplier', true))]);
			}else{
				$this->flashMessage(sprintf(__('Invalid %s', true), __('supplier', true)));
				$referer_url = !($this->Session->check('referer_url')) ? '/v2/owner/entity/supplier/list' : $this->Session->read('referer_url');
				$this->Session->delete('referer_url');
                $this->redirectToIndex();
			}
        }
        $module_name = __('supplier', true);
        if (count($_POST['ids']) > 1) {
            $module_name = __('suppliers', true);
        }
		
	 $owner = getAuthOwner('id');
        $suppliers = $this->Supplier->find('all', array('conditions' => array('Supplier.id' => $id)));
        if (empty($suppliers)) {
			if(IS_REST){
				$this->cakeError("error404", ["message"=>sprintf(__('%s not found', true),'Supplier')]);
			}else{
				$this->flashMessage(sprintf(__('%s not found', true), 'Supplier'));
				$this->redirectToIndex();
			}
        }
		if ($owner['staff_id'] != 0) {
            $staff = $owner['staff_id'];
            foreach ($suppliers as $supplier) {
                if ((!check_permission(Edit_Delete_all_suppliers) && $supplier['Supplier']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_suppliers)) {
					if(IS_REST) $this->cakeError('error403');
                    $this->flashMessage(__("You are not allowed to edit this supplier", true), 'Errormessage', 'secondaryMessage');
                    $this->redirectToIndex();
                }
            }
        }
      
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			
            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes') {
                foreach ($suppliers as $supplier) {
					$this->Supplier->delete_with_related($supplier['Supplier']['id']);
                    
                    $this->add_actionline(ACTION_DELETE_SUPPLIER, array('primary_id' => $supplier['Supplier']['id'], 'secondary_id' => $supplier['Supplier']['id'], 'param2' => $supplier['Supplier']['business_name'], 'param3' => $supplier['Supplier']['email'], 'param4' => $supplier['Supplier']['supplier_number'], 'param5' => $supplier['Supplier']['phone1'] . ' ' . $supplier['Supplier']['phone2']));
                }
				$this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
					$referer_url = $this->_get_referer_path();
					$this->redirectToIndex();
			}
 }
		$this->set('suppliers',$suppliers);
		$this->set('po_count',$this->Supplier->get_po_count($id,PurchaseOrder::PURCHASE_INVOICE));
		$this->set('pr_count',$this->Supplier->get_po_count($id,PurchaseOrder::Purchase_Refund));
		$this->set('get_payment_count',$this->Supplier->get_payment_count($id));
		$this->set('id',$id);
}
    //---------------------------


    //-----------------------------
   function owner_get_user_data($id = 0) {
        $this->autoRender = false;
        $site_id = getAuthOwner('id');
        Configure::write('debug', 0);

        $supplier = $this->Supplier->find(array('Supplier.id' => $id));

        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if (!check_permission(View_All_Suppliers) && $supplier['Supplier']['staff_id'] != $staff) {
                $result = array('error' => true, 'supplier' => false);
            } elseif (!check_permission(View_his_own_Suppliers)) {
                $result = array('error' => true, 'supplier' => false);
            }
        }
        if ($supplier) {
            unset($supplier['Supplier']['password'], $supplier['Supplier']['created'], $supplier['Supplier']['modified']);
			
			$view = new View($this, false);
			$supplier['Supplier']['full_address']=$view->element('format_address_html',$supplier['Supplier']);
			
			
            if (check_permission(Edit_Delete_all_suppliers)) {
                $readonly = 0;
            } elseif (check_permission(Edit_And_delete_his_own_added_suppliers) and $supplier['Supplier']['staff_id'] == $staff) {
                $readonly = 0;
            } else {
                $readonly = 1;
            }
            $result = array('readonly' => $readonly, 'error' => false, 'supplier' => $supplier['Supplier']);
        } else {
            $result = array('error' => true, 'supplier' => false);
        }
        die(json_encode($result));
    }

  
    function owner_load_list() {
        $this->autoRender = false;
        die(json_encode(array('error' => false, 'suppliers' => $this->Supplier->getSuppliersList())));
    }


    function confirm_password() {
        //print_r($_SESSION['user_data']);
        $this->_is_user();

        if (!$this->Supplier->check_code_confirmation($this->params['url'])) {
            $this->redirect(array('action' => 'forgot'));
        }
        if (!empty($this->data['Supplier'])) {
            if ($this->Supplier->saveConfirmPassword($this->data)) {
                $this->flashMessage(__('New Password has been saved', true), 'Sucmessage');
                $this->redirect(array('action' => 'login'));
            } else {
                $this->flashMessage(__('Failed to save new password ,try again', true));
            }
        }
        $this->set('title_for_layout',  __("Reset Password", true));
    }

    //-------------------------------------
    

    function owner_myob() {
        $owner = getAuthOwner();
//        $this->Supplier->bind(array('Country' => array('foreignKey' => false, 'type' => 'belongsTo', 'conditions' => 'Country.code = Supplier.country_code')));
        $suppliers = $this->Supplier->find('all', array('conditions' => array(), 'order' => 'Supplier.supplier_number'));
        $this->set(compact('suppliers', 'owner'));
    }
	
    function owner_statement($id = false) {
        App::import('Vendor', 'settings');
        App::import('Vendor', 'PlaceHolder');
        $supplier = $this->Supplier->find(array('Supplier.id' => $id));
        if (!$supplier) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirectToIndex();
        }
        if(ifPluginActive(AccountingPlugin)){

            $this->Supplier->adjust_and_pay($id);

        }
//        $s=$this->Supplier->supplier_credit($id,'EGP');
//        print_pre($s);
//        die();
        $this->loadModel('PurchaseOrderPayment');
        $sb=$this->PurchaseOrderPayment->find('first',array('recursive'=>'-1','conditions'=>array('PurchaseOrderPayment.payment_method'=>'starting_balance','PurchaseOrderPayment.supplier_id'=>$id)));
        $this->set('sb',$sb);
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(View_All_Suppliers) && $supplier['Supplier']['staff_id'] != $staff) || !check_permission(View_his_own_Suppliers)) {
                $this->flashMessage(__("You are not allowed to view this supplier !", true), 'Errormessage', 'secondaryMessage');
                $this->redirectToIndex();
            }
        }
        $this->set(compact('supplier'));
        if (low($this->params['url']['ext']) == 'pdf') {
            $this->owner_view_statement($id);
        }
		  $this->set('title_for_layout',  __("Suppliers", true));
    }
    function owner_balance($id = null,$currency_code=""){
        $owner = getAuthOwner();

        $Supplier = $this->Supplier->find(array('Supplier.id' => $id));
        if(!$Supplier){
            die();
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(View_All_Suppliers) && $Supplier['Supplier']['staff_id'] != $staff) || !check_permission(View_his_own_Suppliers)) {
                die();
            }
        }

        $this->set('hide_over_due',true);
        $overdueInvoices = $this->Supplier->getOverDue($id,$currency_code);
        $this->set('overduelist', $overdueInvoices);
        $OpenInvoices = $this->Supplier->getUnpaid($id,$currency_code);

        $Creditlist = $this->Supplier->getAllCredit($id,$currency_code);
//
//        print_pre($Creditlist);
//        die();
        $this->set('Openlist', $Creditlist);
        $this->set('Creditlist', $OpenInvoices);
    }
    function owner_view_statement($id = false) {
        
        $supplier = $this->Supplier->find(array('Supplier.id' => $id));
        if (!$supplier) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirectToIndex();
        }
        $this->loadModel('PurchaseOrderPayment');
        $supplier_starting_balance=$this->PurchaseOrderPayment->find('first',array('recursive' => -1,'conditions'=>array('PurchaseOrderPayment.payment_method'=>'starting_balance','PurchaseOrderPayment.supplier_id'=>$id)));

        $this->set('supplier_starting_balance',$supplier_starting_balance['PurchaseOrderPayment']);
        //Getting detailedSummaries and summaries
        $this->set ( $this->Supplier->get_statement_data($id)) ;
        $this->layout = 'box';
        $this->set(compact('supplier', 'detailedSummaries', 'summaries'));

        if ($this->action == 'owner_view_statement' && low($this->params['url']['ext']) == 'pdf') {
            $this->render('owner_statement');
        }
    }
    //OLD statement controller function - NOT USED NOW
    private function old_owner_statement($id = false) {
        $supplier = $this->Supplier->find(array('Supplier.id' => $id));
        if (!$supplier) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirectToIndex();
        }

        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
            if ((!check_permission(Suppliers_View_All_Suppliers) && $supplier['Supplier']['staff_id'] != $staff) || !check_permission(View_his_own_Suppliers)) {
                $this->flashMessage(__("You are not allowed to view this supplier !", true), 'Errormessage', 'secondaryMessage');
                $this->redirectToIndex();
            }
        }

        $this->set(compact('supplier'));
        if (low($this->params['url']['ext']) == 'pdf') {
            $this->owner_view_statement($id);
        }
    }

    //OLD view statement controller function - NOT USED NOW
    private function old_owner_view_statement($id = false) {
        $supplier = $this->Supplier->find(array('Supplier.id' => $id));
        if (!$supplier) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirectToIndex();
        }

        $summaries = $this->Supplier->query('SELECT I.currency_code, SUM(I.summary_total) as `total`, SUM(I.summary_paid) as `paid`, SUM(I.summary_unpaid) as unpaid FROM invoices as I WHERE I.supplier_id = ' . intval($id) . ' GROUP BY I.currency_code ORDER BY I.currency_code');
        $query = "
SELECT `date`, `no`, ROUND(summary_total, 2) as `amount`, currency_code, 1 as `is_invoice` FROM invoices WHERE supplier_id = :supplier_id
UNION
SELECT IP.`date` as `date`, I.`no` as `no`, ROUND(IP.amount, 2) as `amount`, IP.currency_code as currency_code, 0 as `is_invoice` FROM invoice_payments as IP JOIN (invoices as I) ON (I.id = IP.invoice_id) WHERE I.supplier_id = :supplier_id AND IP.status = 1
ORDER BY `date` DESC, currency_code ASC";

        $detailedSummaries = $this->Supplier->query(str_replace(':supplier_id', $id, $query));

        $this->layout = 'box';
        $this->set(compact('supplier', 'detailedSummaries', 'summaries'));

        if ($this->action == 'owner_view_statement' && low($this->params['url']['ext']) == 'pdf') {
            $this->render('owner_statement');
        }
    }

    function owner_send_statement($id = false) {
        $site = getAuthOwner();
        $this->loadModel('EmailTemplate');
        $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('supplier-statement');

        $supplier = $this->Supplier->find( 'first' , ['conditions' => array('Supplier.id' => $id)] );

        if (!$supplier) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirectToIndex();
        }

        $placeHolders = array(
            '{%supplier-number%}' => $supplier['Supplier']['supplier_number'],
            '{%supplier-name%}' => empty($supplier['Supplier']['first_name']) && empty($supplier['Supplier']['first_name']) ? $supplier['Supplier']['business_name'] : $supplier['Supplier']['first_name'] . ' ' . $supplier['Supplier']['last_name'],
            '{%supplier-business_name%}' => $supplier['Supplier']['business_name'],
            '{%supplier-address1%}' => $supplier['Supplier']['address1'],
            '{%supplier-address2%}' => $supplier['Supplier']['address2'],
            '{%supplier-city%}' => $supplier['Supplier']['city'],
            '{%supplier-state%}' => $supplier['Supplier']['state'],
            '{%supplier-postal-code%}' => $supplier['Supplier']['postal_code'],
            '{%supplier-country%}' => $supplier['Country']['country'],
        );

        $attachments = array($this->_createStatement($id));

        $to = $supplier['Supplier']['email'];
        debug ( $defaultTemplate ) ; 
        $defaultTemplate['EmailTemplate']['subject'] = __("Account statement for:" , true )." ".$supplier['Supplier']['business_name'] ; 
        if ($this->SysEmails->sendEmail($to, $site, $defaultTemplate, $placeHolders, $attachments, array('supplier_id' => $supplier['Supplier']['id']))) {
            $this->flashMessage(__('The statement has been sent', true), 'Sucmessage');
            $this->redirect(array('action' => 'view', $id));
        } else {
            $this->flashMessage(__('Could not send the statement', true));
            $this->redirect(array('action' => 'view', $id));
        }


        $this->loadModel('EmailTemplate');
        $this->set('emailTemplates', $this->EmailTemplate->getEmailTemplateList(2));
        $this->Set('fullEmailTemplates', $this->EmailTemplate->find('all', array('conditions' => array('EmailTemplate.type' => 2), 'order' => 'EmailTemplate.title')));

        $type = EmailTemplate::getTypes(2);
        $this->set('placeHolders', $type['placeHolders']);
        $this->set('supplier', $supplier);
        $this->loadModel('EmailLog');
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->helpers[] = 'fck';
    }

	function owner_distribute_credit($supplier_id)
	{
		$this->loadModel('Supplier');
		if (settings::getValue(InventoryPlugin, 'automatic_pay_po') || !check_permission(Add_New_Purchase_Orders)) {
			$this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
			$this->redirect(array('controller' => 'suppliers', 'action' => 'view', $supplier_id));
		}

		if (!empty($this->data)) {
			if (!empty($this->data['paymentPurchaseInvoices'])) {
				$validation_result = SupplierCreditDistributionService::validateDistributionForm($this->data['paymentPurchaseInvoices'], $this->data['balance']);
				if ($validation_result['result']) {
					$dist_service = new SupplierCreditDistributionService();
					$distribution_data = $dist_service->addSubPayments($this->data['paymentPurchaseInvoices'], null, $this->data['currency_code']);
					if ($distribution_data)
						$dist_service->addDistributions(null, $distribution_data);
					$this->flashMessage(__('Supplier Balance distributed Successfully', true),'Sucmessage');
					$this->redirect(array('controller' => 'suppliers', 'action' => 'view', $supplier_id));
				} else {
					$this->flashMessage($validation_result['message'], 'Errormessage', 'secondaryMessage');
				}
			} else {
				$this->flashMessage(sprintf(__('%s Not Found', true), __('Distributions', true)), 'Errormessage', 'secondaryMessage');
			}
		}


		$currency_code = $_GET['currency_code'];
		if (!$currency_code) {
			$this->flashMessage(sprintf(__('%s Not Found', true), __('Currency Code', true)), 'Errormessage', 'secondaryMessage');
			$this->redirect(array('controller' => 'suppliers', 'action' => 'view', $supplier_id));
		}

		$credit_list = $this->Supplier->getAllCredit($supplier_id);
		$balance = $credit_list[$currency_code];
		$this->set('balance', $balance);
		$this->set('currency_code', $currency_code);
		$this->set('supplier_id', $supplier_id);

		$purchaseInvoicesOptions = SupplierCreditDistributionService::getPurchaseInvoicesOptions($supplier_id, $currency_code);
		$hasPurchaseInvoices = SupplierCreditDistributionService::validateSupplierHasValidPurchaseInvoices($supplier_id);
		$this->set('purchaseInvoicesOptions', $purchaseInvoicesOptions);
		$this->set('hasPurchaseInvoices', $hasPurchaseInvoices);

		$supplier = $this->Supplier->findById($supplier_id);
		$breadcrumbs = array();
		$breadcrumbs[0]['title'] = __('Suppliers', true);
		$breadcrumbs[0]['link'] = Router::url(array('controller' => 'suppliers', 'action' => 'index'));
		$breadcrumbs[1]['title'] = $supplier['Supplier']['business_name']." (#".$supplier['Supplier']['supplier_number'].")";
		$breadcrumbs[1]['link'] = Router::url(array('controller' => 'suppliers', 'action' => 'view', $supplier_id));
		$breadcrumbs[2]['title'] = __('Distribute', true);
		$breadcrumbs[2]['link'] = '#';
		$this->set('breadcrumbs', $breadcrumbs);
		$this->set('title_for_layout',  __('Supplier Distribute Credit', true));
	}

	function owner_getSupplierInvoicesForCreditDistribution()
	{
		$supplier_id = $_GET['supplier_id'];
		$currency_code = $_GET['currency_code'];
		$purchaseInvoices = SupplierCreditDistributionService::getPurchaseInvoicesOptions($supplier_id, $currency_code);
		die(json_encode($purchaseInvoices));
	}

    function _createStatement($supplier_id) {
        $this->owner_view_statement($supplier_id);
        $View = new View($this, true);
        /* @var $View View */
        $View->set('save', true);
        $View->params['url']['ext'] = 'pdf';
        $View->layout = '';
        $site = getCurrentSite();

        $View->render('pdf/owner_statement', '');

        return $filename = $View->statementFileName;
    }

    function owner_autocomplete() {
        Configure::write('debug', 0);

        if (!$this->RequestHandler->isAjax()) {
            // $this->redirect(array('action' => ' index'));
        }

        if (empty($this->params['url']['term'])) {
            die(json_encode(array()));
        }
        $con = array();
        $q = '%' . mysql_real_escape_string($this->params['url']['term']) . '%';
        if (!check_permission(Suppliers_View_All_Suppliers)) {
            $con['Supplier.staff_id'] = getAuthOwner('staff_id');
        }

        $conditions = array_merge($con, array('CONCAT_WS(" ", Supplier.first_name, Supplier.last_name, Supplier.business_name) LIKE' => $q));

        $dbSuppliers = $this->Supplier->find('all', array('conditions' => $conditions, 'limit' => 10, 'order' => 'CONCAT_WS(" ", Supplier.first_name, Supplier.last_name, Supplier.business_name)', 'recursive' => -1, 'fields' => 'Supplier.id, Supplier.first_name, Supplier.last_name, Supplier.business_name'));
        $suppliers = array();

        foreach ($dbSuppliers as $supplier) {
            $suppliers[] = array('label' => $supplier['Supplier']['first_name'] . ' ' . $supplier['Supplier']['last_name'] . ' (' . $supplier['Supplier']['business_name'] . ')', 'value' => $supplier['Supplier']['id']);
        }

        die(json_encode($suppliers));
    }

    private function createAdditionalFieldsFormHandlerInstance()
    {
        return new \App\Services\LocalEntityForm\AdditionalFormsHandler(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY);
    }

    function owner_add_payment_credit($id = false, $work_order_id=false) {

        $owner = getAuthOwner();

        $this->loadModel('PurchaseOrderPayment');
        if (!empty($this->data['PurchaseOrderPayment']['supplier_id'])) {
            $id = $this->data['PurchaseOrderPayment']['supplier_id'];
        }

        $this->SupplierValidation->validateSupplierSuspended($id);
        $supplier = $this->Supplier->read(null, $id);

        if ($owner['staff_id'] != 0) {
            if (!(check_permission([Edit_Delete_All_Purchase_Orders]) || (check_permission(Edit_Delete_his_own_created_Purchase_Orders) && $supplier['Supplier']['staff_id'] == $owner['staff_id']))) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        $this->set('supplier', $supplier);
        if (!$supplier) {
            if (!IS_REST) {
                $this->flashMessage(__('Supplier not found', true));
                $this->redirectToIndex();
            } else {
                $this->cakeError("error400", ["message" => sprintf(__("%s is not found", true), __('supplier', true))]);
            }
        }

        if (!empty($this->data)) {
            /** Validate Purchase Invoice Payment Distributions **/
            if (!settings::getValue(InventoryPlugin, 'automatic_pay_po') && check_permission(Edit_Delete_All_Purchase_Orders) && !empty($this->data['paymentPurchaseInvoices'])) {
                $validation_result = SupplierCreditDistributionService::validateDistributionForm($this->data['paymentPurchaseInvoices'], $this->data['PurchaseOrderPayment']['amount']);
                if (!$validation_result['result']) {
                    $this->flashMessage($validation_result['message'], 'Errormessage', 'secondaryMessage');
                }
            } else {
                $validation_result['result'] = true;
            }

            if ($validation_result['result']) {
                $this->data['PurchaseOrderPayment']['supplier_id'] = $id;
                if (empty($this->data['PurchaseOrderPayment']['staff_id']))
                    $this->data['PurchaseOrderPayment']['staff_id'] = $owner['staff_id'];

                $this->data['PurchaseOrderPayment']['supplier_pay'] = 0;


                $attachments = [];
                if(isset($this->data['PurchaseOrderPayment']['attachment']))
                {
                    $attachments = explode(',',$this->data['PurchaseOrderPayment']['attachment']);
                    unset($this->data['PurchaseOrderPayment']['attachment']);
                }

                $result = $this->Supplier->ownerAddPayment($this->data);

                if(!empty($attachments))
                {
                    izam_resolve(AttachmentsService::class)->save('purchase_order_payment', $result['payment_id'], $attachments);
                }


                if (IS_REST) {
                    $this->set('id', $result['payment_id']);
                    $this->render('created');
                    return;
                }

                if ($result['out']) {
                    $this->redirect($result['url'], 302);
                }

                if ($result['status']) {
                    \AutoNumber::update_auto_serial(\AutoNumber::TYPE_PURCHASE_INVOICE_PAYMENT);
                    $this->PurchaseOrderPayment->alias = 'PurchaseOrderPayment';
                    $PurchaseOrderPayment = $this->PurchaseOrderPayment->read(null, $result['payment_id']);

                    /** Distribute Supplier Credit on Purchase Invoices **/
                    if (
                        !settings::getValue(InventoryPlugin, 'automatic_pay_po') &&
                        check_permission(Edit_Delete_All_Purchase_Orders) &&
                        !empty($this->data['paymentPurchaseInvoices']) &&
                        $PurchaseOrderPayment['PurchaseOrderPayment']['status'] == PAYMENT_STATUS_COMPLETED
                    ) {
                        $dist_service = new SupplierCreditDistributionService();
                        $distribution_data = $dist_service->addSubPayments($this->data['paymentPurchaseInvoices'], $PurchaseOrderPayment);
                        if ($distribution_data)
                            $dist_service->addDistributions($PurchaseOrderPayment['PurchaseOrderPayment']['id'], $distribution_data);
                    }

                    if ($result['convert_to_purchase_order_payment'] == false) {
                        $this->add_actionline(ACTION_ADD_SUPPLIER_CREDIT, array('primary_id' => 0, 'secondary_id' => $PurchaseOrderPayment['PurchaseOrderPayment']['supplier_id'], 'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'], 'param2' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'], 'param3' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'param4' => $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method'], 'param5' => $PurchaseOrderPayment['PurchaseOrderPayment']['transaction_id']));
                    } else {
                        $this->add_actionline(ACTION_ADD_PO_PAYMENT, array('primary_id' => $PurchaseOrderPayment['PurchaseOrder']['id'], 'secondary_id' => $PurchaseOrderPayment['PurchaseOrder']['supplier_id'], 'param1' => $PurchaseOrderPayment['PurchaseOrderPayment']['amount'], 'param2' => empty($PurchaseOrderPayment['PurchaseOrder']['draft']) ? $PurchaseOrderPayment['PurchaseOrder']['payment_status'] : -1, 'param3' => $PurchaseOrderPayment['PurchaseOrder']['summary_paid'], 'param4' => $PurchaseOrderPayment['PurchaseOrder']['no'], 'param5' => $PurchaseOrderPayment['PurchaseOrderPayment']['id'], 'param6' => $PurchaseOrderPayment['PurchaseOrder']['summary_total'], 'param7' => $PurchaseOrderPayment['PurchaseOrderPayment']['status'], 'param8' => $PurchaseOrderPayment['PurchaseOrderPayment']['payment_method'], 'param9' => $PurchaseOrderPayment['PurchaseOrderPayment']['transaction_id']));
                    }

                    $this->Supplier->pay_purchase_invoices_from_credit($id);
                    $this->flashMessage(__('Credit payment added successfully', true), 'Sucmessage');
                    if($work_order_id)
                        $this->redirect(array('controller'=>'work_orders', 'action' => 'view', $work_order_id));
                        else
                    $this->redirect(array('action' => 'view', $id));
                } else {
                    $this->flashMessage(__('Could not add payment ', true) . $result['error_message']);
                }
            }
        } else {
            if (empty($supplier['Supplier']['default_currency_code'])) {
                $this->data['PurchaseOrderPayment']['currency_code'] = $owner['currency_code'];
            } else {
                $this->data['PurchaseOrderPayment']['currency_code'] = $supplier['Supplier']['default_currency_code'];
            }
            if($work_order_id)
            $this->data['PurchaseOrderPayment']['payment_work_order_id'] = $work_order_id;
            if (isset($this->params['url']['amount'])) {
                $this->data['PurchaseOrderPayment']['amount'] = $this->params['url']['amount'];
            }
            if (isset($this->params['url']['currency_code'])) {
                $this->data['PurchaseOrderPayment']['currency_code'] = $this->params['url']['currency_code'];
            }
            $this->data['PurchaseOrderPayment']['date'] = format_date(date("Y-m-d"));
            $this->data['PurchaseOrderPayment']['status'] = 1;
            $this->data['PurchaseOrderPayment']['staff_id'] = $owner['staff_id'];
        }

        if (!settings::getValue(InventoryPlugin, 'automatic_pay_po') && check_permission(Add_New_Purchase_Orders)) {
            $purchaseInvoicesOptions = SupplierCreditDistributionService::getPurchaseInvoicesOptions($supplier['Supplier']['id'], $this->data['PurchaseOrderPayment']['currency_code']);
            $has_purchase_invoices = SupplierCreditDistributionService::validateSupplierHasValidPurchaseInvoices($supplier['Supplier']['id']);
            $this->set('purchaseInvoicesOptions', $purchaseInvoicesOptions);
            $this->set('hasPurchaseInvoices', $has_purchase_invoices);
        }

        $this->set('staffs', $this->PurchaseOrderPayment->Staff->getList(true, [], true));
        $this->set('statuses', PurchaseOrderPayment::getPaymentStatus());
        $this->loadModel('SitePaymentGateway');
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));

        $this->set('payment_treasury',$payment_treasury=$this->SitePaymentGateway->find('list',array('fields'=>'SitePaymentGateway.payment_gateway,SitePaymentGateway.treasury_id')));
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));

        unset($paymentMethods['supplier_credit']);
        $this->set('paymentMethods', $paymentMethods);
        $Stripe = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'stripe')));
        $this->set('Stripe', $Stripe);
        if(!empty($Stripe['SitePaymentGateway']['username'])) {
            require_once dirname(dirname(__FILE__)) . '/lib/StripeSca.php';

            $intent=\Stripe\Stripe::setApiKey($Stripe['SitePaymentGateway']['username']);
            $intent = \Stripe\SetupIntent::create([
                'usage' => 'off_session', // The default usage is off_session
            ]);
            $this->set('intent',$intent);

        }
        $offlines = array('offline', 'bank', 'cash', 'cheque', 'credit');
        foreach ($fullPG as $method) {
            if (!empty($method['SitePaymentGateway']['manually_added']))
                $offlines[] = $method['SitePaymentGateway']['payment_gateway'];
        }
        foreach ($offlines as $offline) {
            $instructions[$offline] = $this->SitePaymentGateway->field('option1', array('SitePaymentGateway.payment_gateway' => $offline, 'SitePaymentGateway.active' => 1));
        }

        $this->set('offlines', $offlines);
        $Square = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'square')));
        $this->set('Square', $Square);
        $Tap = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'tap')));
        $this->set('Tap', $Tap);

        $this->loadModel('Currency');
        $this->set('currencyCodes', $this->Currency->getCurrencyList(array(), true));
        $this->loadModel('Treasury');
        $this->loadModel('ItemPermission');

        $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW)) ;
        $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/supplier/list';

        $this->crumbs[1]['title'] = __('Add Payment', true);
        $this->crumbs[1]['url'] = '#';

        $this->set('file_settings', $this->PurchaseOrderPayment->getFileSettings());
        $this->set('content', $this->get_snippet('add-payment-owner'));
        $this->set('title_for_layout',  __('Add Payment', true));
    }




    /**
     * @var $Lmail LmailComponent
     */
    var $Lmail;

    private function getpaymentsCountBySupplierId($supplier_id)
    {
	    $this->loadModel('PurchaseOrderPayment');
        $conditions = [
            'PurchaseOrderPayment.supplier_id' => $supplier_id,
            'NOT' => [
                'PurchaseOrderPayment.payment_method' => 'supplier_credit',
                'PurchaseOrderPayment.payment_method' => 'starting_balance',
            ]
        ];

        return ($this->PurchaseOrderPayment->find('count', ['conditions' => $conditions, 'applyBranchFind' => false]));
    }

    public function json_return_unpaid_partially_paid_purchase_invoices($supplier_id){
        Configure::write('debug', 0);
        if (!empty($supplier_id)) {
            $this->loadModel('PurchaseOrder');
            $purchase_invoices = $this->PurchaseOrder->find('count', ['conditions' => ['PurchaseOrder.type' => PurchaseOrder::PURCHASE_INVOICE, 'PurchaseOrder.payment_status' => [PO_STATUS_UNPAID, PO_STATUS_PARTIAL_PAID], 'PurchaseOrder.supplier_id' => $supplier_id]]);
            echo json_encode($purchase_invoices);
            die();
        }
    }

    function owner_transaction_list($id, $send_email = 0)
    {
        $owner = getAuthOwner();
        $supplier = $this->Supplier->read(null, $id);

        $placeholders = array_merge(PlaceHolder::site_place_holder() + PlaceHolder::datetime_place_holder(), PlaceHolder::supplier_place_holder($supplier), PlaceHolder::staff_place_holder($owner['staff_id']));
        $this->set('placeholders', $placeholders);

        $statement_header_html =  settings::getValue(0, "supplier_statement_header_html",null,false);
        $custom_header = PlaceHolder::replace($statement_header_html, array_keys($placeholders), $placeholders);
        $this->set('custom_header', $custom_header);

        $statement_footer_html =  settings::getValue(0, "statement_footer_html");
        $custom_footer = PlaceHolder::replace($statement_footer_html, array_keys($placeholders), $placeholders);
        $this->set('custom_footer', $custom_footer);

        $this->set('supplier', $supplier);
        if (!$supplier) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirectToIndex();
        }
        $journal_account_id = $this->Supplier->get_journal_id($id);
        if ($journal_account_id == 0) {
            $journal_account_id = 'NULL';
        }
        $this->layout = false;
        $starting_balance = array();
        $page = $this->params['url']['page']  ? intval($this->params['url']['page']) : 1;
        $date_from = $this->params['url']['date_from'];
        $date_to = $this->params['url']['date_to'];


        $per_page = 500;
        $purchase_order_more_query = "";
        $payment_more_query = "";
        $starting_limit = "";
        if (!empty($date_from)) {

            $date_from = $this->params['url']['date_from'];
            $date_to = $this->params['url']['date_to'];

            $purchase_order_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
            $before_purchase_order_more_query = " And date <'{$date_from}'";

            $payment_start_query = " And date >='{$date_from}' And date <='{$date_to}'";

            $payment_more_query = " And date >='{$date_from}' And date <='{$date_to}'";
        }

        if (
            $page > 1
        ) {
            $starting_limit =  " LIMIT " . ($per_page * ($page - 1));
        }
        $bare_query = "select 1 as my_order,null as payment_code,`date` as created,'purchase_order',id,no,type,supplier_id,date,payment_status,id as purchase_order_id,summary_total,due_after,'payment_method',currency_code,(SELECT IF(alter_description!='',alter_description,description) COLLATE utf8_unicode_ci FROM `journals` where draft = 0 and entity_type=(CASE WHEN purchase_orders.type=0 THEN 'purchase_order' WHEN purchase_orders.type=6 THEN 'purchase_refund' WHEN purchase_orders.type=14 THEN 'purchase_debit_note' WHEN purchase_orders.type=5 THEN 'debit_note' WHEN purchase_orders.type=15 THEN 'purchase_credit_note' END) and entity_id=purchase_orders.id limit 1) as description from purchase_orders where supplier_id='$id' and type in(0,5,6,14,15) and draft <> 1 %s
                UNION select 1 as my_orderm ,code as payment_code, `date` as created,'Starting Balance',(select id from journals where draft = 0 and entity_type='purchase_order_payment' and entity_id=purchase_order_payments.id),null,0,'$id',date,status,id as purchase_order_id,amount,'due_after','payment_method',currency_code,null from purchase_order_payments where supplier_id='$id' and payment_method = 'starting_balance' %s
                UNION select 2 as my_order,code as payment_code,`date` as created,'payment',id,(select no from purchase_orders where id=purchase_order_id),null,supplier_id,date,status,purchase_order_id,amount,null,payment_method,currency_code,null from purchase_order_payments where (purchase_order_id in(select id from purchase_orders where supplier_id='$id' and type in(0,5,15) and draft <> 1) or supplier_id='$id' and purchase_order_id is null) and payment_method <> 'supplier_credit' and payment_method <> 'starting_balance' and status=1  %s 
                UNION SELECT 1 AS my_order,null as payment_code,J.`date`,'journal',J.`id`,number as no, null,'$id',J.`date`,1,JT.`id`,ROUND(JT.currency_credit-JT.currency_debit,3) AS `amount`,null,'none',JT.currency_code,JT.description FROM `journal_transactions` AS `JT`  LEFT JOIN `journals` AS `J` ON(`JT`.`journal_id` = `J`.`id`) WHERE J.draft = 0 and J.entity_type not in('purchase_order','purchase_order_payment','purchase_refund','debit_note','purchase_debit_note', 'purchase_credit_note', 'year_opening_balance','year_closing_balance') and JT.journal_account_id={$journal_account_id} %s
                UNION select 3 as my_order,code as payment_code,`date` as created,'Refund Payment',id,(select no from purchase_orders where id=purchase_order_id),'6-1',supplier_id,date,status,'refund',amount,null,payment_method,currency_code,null from purchase_order_payments where (purchase_order_id in(select id from purchase_orders where supplier_id='$id' and type in(6) and draft <> 1)) %s ORDER BY `date` , my_order ASC,`created` ASC, id ASC";

        $allt_starting = sprintf($bare_query, $payment_start_query, $payment_start_query, $payment_start_query, $payment_start_query, $payment_start_query);
        if ($page > 1) {

            $starting_purchase_order_with_payment = $this->Supplier->query($allt_starting . $starting_limit, false);
        }
        if (!empty($date_from)) {
            $before = sprintf($bare_query, $before_purchase_order_more_query, $before_purchase_order_more_query, $before_purchase_order_more_query, $before_purchase_order_more_query, $before_purchase_order_more_query);
            $before_starting_purchase_order_with_payment = $this->Supplier->query($before, false);
            if (isset($starting_purchase_order_with_payment)) {
                $starting_purchase_order_with_payment = array_merge($starting_purchase_order_with_payment, $before_starting_purchase_order_with_payment);
            } else {
                $starting_purchase_order_with_payment = $before_starting_purchase_order_with_payment;
            }
        }
        foreach ($starting_purchase_order_with_payment as $t) {
            $t = $t[0];
            if ($t['purchase_order'] == 'payment' or ($t['purchase_order'] == "Starting Balance" || ($t['purchase_order'] == 'purchase_order' && in_array($t['type'], array(PurchaseOrder::Purchase_Refund, PurchaseOrder::Debit_Note, PurchaseOrder::DEBIT_NOTE, 'income' , 'expense'))))) {
                $t['summary_total'] = $t['summary_total'] * -1;

                $negation = 1;
            }
            if ($t['type'] == "6-1") {
                $t['summary_total'] = $t['summary_total'] * -1;
            }
            $starting_balance[$t['currency_code']] += $t['summary_total'];
        }

        $paginator_url = 'date_from=' . $this->params['url']['date_from'] . '&';
        $paginator_url .= 'date_from=' . $this->params['url']['date_to'] . '&';
        $this->set('paginator_url', $paginator_url);
        $this->set('starting_balance', $starting_balance);
        $allt = sprintf($bare_query, $payment_more_query, $purchase_order_more_query, $payment_more_query, $payment_more_query, $payment_start_query);

        $purchase_order_with_payment = $this->Supplier->query($allt . " LIMIT $per_page OFFSET " . ($per_page * ($page - 1)));
        $this->loadModel('PurchaseOrder');
        $this->loadModel('InvoiceLayout');
        $layoutsList = $this->InvoiceLayout->find('all');
        $layouts = [];
        foreach ($layoutsList as $layout) {
            $layouts[$layout['InvoiceLayout']['id']] = $layout;
        }
 
        $purchase_orders = [];
        $this->loadModel('PurchaseOrderPayment');
        $this->PurchaseOrderPayment->applyBranch['onFind'] = false;
        foreach ($purchase_order_with_payment as $key => $t) {
            $t = $t[0];
            if (isset($_GET['show_details']) && $_GET['show_details'] == 'true') {
                if ($t['type'] !== null && in_array($t['type'], [PurchaseOrder::PURCHASE_INVOICE, PurchaseOrder::Purchase_Refund, PurchaseOrder::DEBIT_NOTE,  PurchaseOrder::Debit_Note])) {
                    $ids[] = $t['id'];
                }
            }
            if($t['purchase_order'] == 'payment'){
                $conditions['PurchaseOrderPayment.id'] = $t['id'];
                $purchase_order_payment = $this->PurchaseOrderPayment->find('first', array('conditions' => $conditions));
                $purchase_order_with_payment[$key][0]['code'] = !empty($purchase_order_payment['PurchaseOrderPayment']['code']) ? $purchase_order_payment['PurchaseOrderPayment']['code'] : $purchase_order_payment['PurchaseOrderPayment']['id'];
            }
        }
        $this->PurchaseOrderPayment->applyBranch['onFind'] = true;
        $this->PurchaseOrder->applyBranch['onFind'] = false;
        $this->PurchaseOrder->unbindModel(['hasMany' => ['PurchaseOrderItem']]);
        $purchase_orders_list = $this->PurchaseOrder->find('all', ['conditions' => ['PurchaseOrder.id' => $ids]]);
        $this->PurchaseOrder->applyBranch['onFind'] = true;
        
        foreach($purchase_orders_list as $purchase_order){
            $purchase_orders[$purchase_order['PurchaseOrder']['id']] = $purchase_order;
            $total_discount[$purchase_order['PurchaseOrder']['id']] = $purchase_order['PurchaseOrder']['summary_discount'];
        }

        $this->loadModel('PurchaseOrderItem');
        $this->PurchaseOrderItem->unbindModel(['belongsTo' => ['PurchaseOrder']]);
        $purchase_order_items = $this->PurchaseOrderItem->find('all', ['conditions' => ['PurchaseOrderItem.purchase_order_id' => $ids], 'recursive' => 2]);
        foreach($purchase_order_items as $item){
            $copaymentTotal[$item['PurchaseOrderItem']['purchase_order_id']] = 0;
            $item['PurchaseOrderItem']['Product'] = $item['Product'];
            $item['PurchaseOrderItem']['product_image'] = '';
            if($item['PurchaseOrderItem']['product_id']){
                $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product', $item['PurchaseOrderItem']['Product']['id']);
                if(count( $defaultS3Images))
                {
                    $item['PurchaseOrderItem']['product_image'] = $defaultS3Images[0]->files->path;
                    $item['PurchaseOrderItem']['product_image_s3'] = true;
                }elseif (isset($item['Product']['ProductMasterImage']) && count($item['Product']['ProductMasterImage'])) {
                    $item['PurchaseOrderItem']['product_image'] = 'https://' . getCurrentSite('subdomain') . appendFullPath(getCurrentSite('id'), 'product-images', $item['Product']['ProductMasterImage']['file']);
                    $item['PurchaseOrderItem']['product_image_s3'] = false;
                }
            }
            $subtotal = (float)$item['PurchaseOrderItem']['unit_price'] * (float)$item['PurchaseOrderItem']['quantity'];
            $discount_val = 0 ; 
            if ( !empty($item['PurchaseOrderItem']['discount']))
            {
                $discount_val = $this->PurchaseOrder->calculate_item_discount($item['PurchaseOrderItem'], $subtotal);
                $total_discount[$item['PurchaseOrderItem']['purchase_order_id']] += $discount_val;
                $subtotal -= $discount_val ;
                $item['PurchaseOrderItem']['discount_value'] = $discount_val;
                $item['PurchaseOrderItem']['discount_string'] = ($item['PurchaseOrderItem']['discount_type'] == PurchaseOrder::DISCOUNT_TYPE_PERCENTAGE ? $item['PurchaseOrderItem']['discount']."%":format_price_simple($item['PurchaseOrderItem']['discount'],false,false));
            }
            $item['PurchaseOrderItem']['item_subtotal'] = $subtotal ; 
            if (ifPluginActive(INSURANCE_PLUGIN)) {
                if (!empty($item['PurchaseOrderItem']['extra_details']) && is_string($item['PurchaseOrderItem']['extra_details'])){
                    $item['PurchaseOrderItem']['copayment'] = json_decode($item['PurchaseOrderItem']['extra_details'], true)['copayment'] ?? 0;
                    $copaymentTotal[$item['PurchaseOrderItem']['purchase_order_id']] += $item['PurchaseOrderItem']['copayment'];
                }
            }
            $purchase_orders[$item['PurchaseOrderItem']['purchase_order_id']]['PurchaseOrderItem'][] = $item['PurchaseOrderItem'];
            $purchase_orders[$item['PurchaseOrderItem']['purchase_order_id']]['PurchaseOrder']['summary_copayment'] = $copaymentTotal[$item['PurchaseOrderItem']['purchase_order_id']];
            $purchase_orders[$item['PurchaseOrderItem']['purchase_order_id']]['PurchaseOrder']['summary_total_discount'] = $total_discount[$item['PurchaseOrderItem']['purchase_order_id']];
        }

        $this->set('purchase_orders', $purchase_orders);
        $this->set('layouts', $layouts);
        $count = $this->Supplier->flat_query_results("select count(*)as cc from ( $allt )aaa");
        $this->set('transacti   on_count', $count[0]['cc']);
        $this->set('current_page', $page);
        $this->set('count', ceil($count[0]['cc'] / $per_page));

        $this->set('PurchaseOrderTypeList', PurchaseOrder::getTypeList());
        $this->set('payment_methods', PurchaseOrderPayment::getAllPaymentMethods());
        $this->set('purchase_order_with_payment', $purchase_order_with_payment);

        $transcation_columns_setting = settings::getValue(0, "transcation_columns_setting");
        $this->set('transcation_columns_setting', $transcation_columns_setting);

        if ($send_email == 1) {
            $this->autoRender = false;
            $v = $this->render('../suppliers/owner_transaction_list');
            $site = getAuthOwner();
            if ($this->SysEmails->sendEmail($supplier['Supplier']['email'], $site, ['EmailTemplate' => ['body' => $v, 'subject' => __("Transaction List", true)]], [], [], array('supplier_id' => $supplier['Supplier']['id']))) {
                $this->flashMessage(__('Email has been sent', true), 'Sucmessage');
            } else {
                $this->flashMessage(__('Could not send statement', true));
            }
            $this->redirect(array('action' => 'view', $id . "#TransactionList"));
            die;
        }
    }

    private function redirectToIndex() {
        if (\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('suppliers')) {
            $this->redirect(['action' => 'index']);
        } else {
            $this->redirect('/v2/owner/entity/supplier/list');
        }
    }

    
    private function setCustomEntityAndKey(){
        $this->set('entityCustomField', 'supplier_custom_field');
        $this->set('entityFieldCustomFieldKey', 'supplier_custom_fields');
    }

}
