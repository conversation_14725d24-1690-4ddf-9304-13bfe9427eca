<?php

use App\Utils\ChartOfAccountsUtil;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

App::import('Controller', 'Journals');

class JournalAccountsController extends AppController {

	var $name = 'JournalAccounts';

	/**
	 * @var JournalAccount
	 */
	var $JournalAccount;
	var $helpers = array('Html', 'Form');
    var $components = ["ItemPermissions"];


    function owner_test_deleteable()
    {
        die(var_dump($this->JournalAccount->deletAble(28)));

    }

    function _formCommon($id = false)
    {
        $this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_JOURNAL_ACCOUNT, $id);
    }

	function owner_export(){
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$conditions = $this->Session->read('JournalAccount_Filter');
		
		if(!empty($_POST['ids']))
		{
			$conditions['JournalAccount.id'] = $_POST['ids'];
		}else{
			$conditions = $this->Session->read('JournalAccount_Filter');

		}
		$report_title = __('Journal Accounts',true);
		debug($conditions);
		if(isset($conditions['has_cost_centers']))
		{
			$has_cost_centers = $conditions['has_cost_centers'];
			$this->loadModel('CostCentersJournalAccount');
			debug($has_cost_centers);
			$account_with_cost_centers = $this->CostCentersJournalAccount->find('list',['fields' => ['id','journal_account_id']]);
			if($has_cost_centers)
			{
			$report_title = __('Journal Accounts With Cost Centers',true);
			$conditions['JournalAccount.id'] = $account_with_cost_centers;
			$this->set('has_cost_centers',true);
			
			}
			else if(!$has_cost_centers){
			$report_title = __("Journal Accounts Without Cost Centers",true);

			$conditions['NOT']['JournalAccount.id'] = $account_with_cost_centers;
			$this->set('has_cost_centers',false);
			
			}
			unset($conditions['has_cost_centers']);

		}
//			$this->JournalAccount->unbindModel(['hasMany' => ['JournalTransaction']]);
//			$this->JournalAccount->bindModel(
//					['hasMany' =>[
//						'CostCentersJournalAccount'
//					]]
//					);
//		die(debug($conditions));
		unset($conditions['ext']);
		unset($conditions['debug']);
		unset($conditions['tags']);
//		die(debug($conditions));
		$this->paginate['JournalAccount']['limit'] = 9999999;
//		$journal_accouns = $this->JournalAccount->find('all',['conditions' => $conditions]);
		$journal_accouns = $this->paginate('JournalAccount', $conditions);
//		die(debug($journal_accouns));
		$this->set('owner', getAuthOwner());
		$this->set('report_title',$report_title);
		$this->set('journal_accounts', $journal_accouns);
		$this->layout = '';
	}


	function owner_index($cat_id = null) {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->JournalAccount->recursive = 0;
		$conditions = $this->_filter_params();
		if($cat_id)
			$conditions['JournalAccount.journal_cat_id']= $cat_id;
		if($cat_id){
			$this->loadModel("JournalCat");
			$this->JournalCat->recursive = -1;
			$cate = $this->JournalCat->find('first',array('conditions'=>array('JournalCat.id' => $cat_id)));
			$this->set('cate',$cate);
		}
		if(isset($_GET['has_cost_centers']))
		{
			unset($conditions['JournalAccount.has_cost_centers']);
			$has_cost_centers = $_GET['has_cost_centers'];
			$this->loadModel('CostCentersJournalAccount');
			$account_with_cost_centers = $this->CostCentersJournalAccount->find('list',['fields' => ['id','journal_account_id']]);
			if($has_cost_centers)
			{
			$conditions['JournalAccount.id'] = $account_with_cost_centers;
			$this->set('has_cost_centers',true);
			}
			else if(!$has_cost_centers){
			$this->set('has_cost_centers',false);
			$conditions['NOT']['JournalAccount.id'] = $account_with_cost_centers;
			}
			
		}
		$this->paginate = ['order' => ['JournalAccount.code' => 'ASC']];
		$permittedAccountIds = $this->JournalAccount->getPermittedAccountIds();
		if(is_array($permittedAccountIds ))
        {
            $conditions['JournalAccount.id'] = $permittedAccountIds;
        }
		$journal_accounts = $this->paginate('JournalAccount', $conditions);
		$this->set('journalAccounts', $journal_accounts);
//		die(debug($_SESSION));
		$this->set('title_for_layout',  __('Journals Accounts',true));
		if(IS_REST){
			$this->set('rest_items', $journal_accounts);
			$this->set('rest_model_name', "JournalAccount");
			$this->render("index");
		}
	}

	function owner_view($id = null) {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (!$id) {
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Journal Account', true))]);
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('account', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$journal_account = $this->JournalAccount->read(null, $id);
        $journal_account = JournalsController::calculateAccounts([$journal_account])[0];
		if(!$journal_account && IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Journal Account', true))));
		if(IS_REST){
			$this->set('rest_item', $journal_account);
			$this->set('rest_model_name', "JournalAccount");
			$this->render("view");
		}
	}

	function owner_add() {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (!empty($this->data)) {
			$this->JournalAccount->create();
			if ($this->JournalAccount->save($this->data)) {
				$this->loadModel('Journal');
				$this->Journal->updateAccountLevelsRecursive($this->data['JournalAccount']['journal_cat_id']);

				if(IS_REST){
					$this->set('id', $this->JournalAccount->id);
					$this->render('created');
					return;
				}
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('account',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->JournalAccount->validationErrors]);
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('account',true)));
			}
		}
		$this->_formCommon();
		$this->loadModel('Journal');
		$types = $this->Journal->get_journal_types_list();
		$this->loadModel('JournalCat');
		$categories = $this->JournalCat->find('all',['order'=>['JournalCat.code ASC']]);
		$tree = $this->JournalCat->buildTree($categories);
		$journalCats = $this->JournalAccount->JournalCat->find('list');
				$this->set('categories',$tree);

		$this->set(compact('journalCats','types'));
	}
	
	function owner_validate_code($code,$account_id){
		$account = $this->JournalAccount->find('first',array('conditions'=> array('JournalAccount.code'=> $code)));
		if($account && $account['JournalAccount']['id'] != $account_id)
			die(false);
		else  die(true) ;
	}
	
	
	

	function owner_edit($id = null) {
		
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->loadModel('JournalCat');
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'journal account',true)));
			$this->redirect(array('action'=>'index'));
		}
        $this->_formCommon($id);
		$journalAccount = $this->JournalAccount->read(null, $id);
		if(IS_REST){
			if(empty($journalAccount)) $this->cakeError('error404', array('message' => sprintf (__('Invalid %s.', 'journal account',true))));
			$this->data['JournalAccount']['id'] = $id;
		}
		if (preg_match('/\bpage=(\d+)\b/', $this->referer(), $matches)) {
			$pageValue = $matches[1];
			$this->set('pageNumber', $pageValue); 
		} 
		
		if (!empty($this->data)) {
			$oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL_ACCOUNT, $id, 1);
			if(empty($this->data['JournalAccount']['journal_cat_id']) || $this->data['JournalAccount']['journal_cat_id'] == -1 ) {
				$this->JournalAccount->validationErrors['journal_cat_id'] = __('This Field is Required', true);
			}

			$permissionsEmpty = true;
			if($this->data['ItemPermission']['group_type'] != ItemPermission::GROUP_EVERYONE){
				foreach ($this->data['ItemPermission']['group_id'][1] as $key => $value) {
					if(!empty($value)){
						$permissionsEmpty = false;
						break;
					}
				}
			} else {
				$permissionsEmpty = false;
			}
			if(ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)  && settings::getValue(BranchesPlugin, 'specify_accounts_branches') && $permissionsEmpty  && $this->data['ItemPermission']["group_type"][1]!=-1){
				CustomValidationFlash([__('You must at least select one branch', true)]);
				$_SESSION['ErrorMessage'] = __('You must at least select one branch', true);
				$this->JournalAccount->validationErrors['group_id'] = __('This Field is Required', true);
			}

			if (empty($this->JournalAccount->validationErrors) && $this->JournalAccount->save($this->data)) {
				$this->loadModel('Journal');
				$this->Journal->updateAccountLevelsRecursive($this->data['JournalAccount']['journal_cat_id']);

				$newData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL_ACCOUNT, $id, 1);
				$request = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRequest(
					$id,
					EntityKeyTypesUtil::JOURNAL_ACCOUNT,
					ActionLineMainOperationTypesUtil::UPDATE_ACTION,
					"#$newData->code $newData->name",
						"/v2/owner/chart-of-accounts/accounts/".$id,
					$newData->toArray(),
					$oldData->toArray()
				);
				(new \App\Services\ActivityLogService())->addActivity($request);
            	if(isset($_POST['iframe'])) {
                    $redirectData = ChartOfAccountsUtil::getSaveAccountRedirectUrl($this->data);
                } else {
                    $redirectData = array('controller'=>'journal_cats','action' => 'tree','#' =>  'folderid='.$this->data['JournalCat']['folderid'],'escape' => false);
                }
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('account',true)), 'Sucmessage');
				$action_line_data = array();
				$action_line_id = ACTION_JOURNAL_ACCOUNT_EDIT;
				$action_line_data['param3'] = $this->data['JournalAccount']['name'] . ' - ' . $this->data['JournalAccount']['code'];
				$action_line_data['param4'] = $this->data['JournalAccount']['type'];
				$cat = $this->JournalCat->find('first',array('conditions' => array('JournalCat.id' => $this->data['JournalAccount']['journal_cat_id'])));
				if($cat){
				$action_line_data['param5'] = $cat['JournalCat']['name'] . ' - ' . $cat['JournalCat']['code'];
				$action_line_data['secondary_id'] = $cat['JournalCat']['id'];
				}
				$action_line_data['param1'] = JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT;
				$action_line_data['primary_id'] = $this->data['JournalAccount']['id'];
				$this->add_actionline ($action_line_id,$action_line_data);
				if(IS_REST){
					$this->render('success');
					return;
				}
				$this->redirect($redirectData);
				
			} else {
				if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->JournalAccount->validationErrors]);
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('account',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $journalAccount;
		}
                $this->loadModel('Journal');
                $types =  $this->Journal->get_journal_types_list();

		$categories = $this->JournalCat->find('all',['order'=>['JournalCat.code ASC']]);
		$tree = $this->JournalCat->buildTree($categories);
		if(isset($_GET['folderid']) && !empty(trim($_GET['folderid']))){
			$this->set('is_ajax',true);
			$this->set('folderid', urldecode($_GET['folderid']));
		}
		$this->set('categories',$tree);
//		$journalCats = $this->JournalAccount->JournalCat->find('list');
		$this->set(compact('types'));
		$this->render('owner_add');
		$this->set('title_for_layout',  __('Edit Journal Account',true));
	}

    function owner_account_transactions($account_id = null, $fy_id = false){
        if (!$this->RequestHandler->isAjax()) {
            if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
				if (IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page',TRUE));
				if((isset($_GET['is_ajax']) && $_GET['is_ajax']))
					{
						return $this->render('../elements/not_allowed_error_message');
					}
                $this->redirect('/');
            }
        }
		$this->set("account_id",$account_id);
        $this->set("fy_id",$fy_id);
		$this->loadModel('Currency');
		$this->loadModel('Journal');
		$this->JournalAccount->recursive = -1 ;
		$journal_account = $this->JournalAccount->find('first',array('conditions' => array('JournalAccount.id' => $account_id)));
		$this->set('journal_account',$journal_account);
		$currency = $this->Journal->get_default_currency();
		$currencies = $this->JournalAccount->get_account_currencies($account_id);
//		$currencies = $this->Currency->getCurrencyList(array('Currency.code' => $currencies),true);
//		die(Debug($currencies));
		$default_currency[''] = __('All In ',true) .'['.$currency.']'; 
		if(count($currencies) == 0){
			$default_currency[''] = __('No transactions in this account',true);
		}
		if($default_currency)
		$currencies = array_merge($default_currency,$currencies);
        $total_currencies = $this->Journal->calculate_account_currency($account_id,null,$fy_id);
		if($journal_account['JournalAccount']['type'] == Journal::JOURNAL_TYPE_DEBITOR)
		{
			foreach($total_currencies as $k => &$v)
				$v = $v * -1 ;
		}
		if(count($total_currencies ?? []) == 1 && array_keys($total_currencies)[0] == $currency)
		{
			$total_currencies = null;
		}
		$this->set('total_currencies',$total_currencies);
		$this->set('currencies',$currencies);
		if($this->RequestHandler->isAjax() || (isset($_GET['is_ajax']) && $_GET['is_ajax']))
		{
			$this->set('is_ajax',true);
		}
	}

    function owner_list_transactions($account_id = null,$fy_id=false) {
        if (!$this->RequestHandler->isAjax()) {
            if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
                if (IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page',TRUE));
                $this->redirect('/');
            }
        }
		if($this->RequestHandler->isAjax()){
			$this->set("is_ajax",true);
		}
		$this->loadModel('Journal');
		$this->loadModel('JournalTransaction');
		$this->Journal->recursive = 1;
		$conditions = $this->_filter_params();	
		
		$conditions[] = "Journal.draft = 0" ;
		
		
		if(isset($_GET['ASC']) || isset($_GET['asc']) ) $order = "ASC";
		else $order = "DESC" ;
		debug($order);
		$account_id = !empty($_GET['id']) ? urlencode($_GET['id']) : $account_id;
//		$journals = $this->Journal->find('all',array('order' => array('Journal.created' =>)))
		$this->paginate = 
						array(
								'order' => array(
									'Journal.date' => $order,
									'JournalTransaction.id' => $order,
									
									)
							);

        $conditions['JournalTransaction.journal_account_id'] = $account_id ;
        $conditions['Journal.entity_type NOT']=['year_opening_balance','year_closing_balance'] ;
		if(isset($_GET['date_from']) && !empty($_GET['date_from']))
			{
			$date_from = urldecode($this->JournalAccount->formatDate($_GET['date_from']));
			$conditions[] = "Journal.date >= '$date_from'" ;
			
		}
		
		if(isset($_GET['currency_code'])  && !empty($_GET['currency_code'])){
			$currency = $_GET['currency_code']; 
			$conditions[] = 'Journal.currency_code = "'.$_GET['currency_code'].'"'; 
		}
		if(isset($_GET['date_to']) && !empty($_GET['date_to'])){
			$date_to = urldecode($this->JournalAccount->formatDate($_GET['date_to']));
			$conditions[] = "Journal.date <= '$date_to'" ;
		}
		if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)) {
			if (getCakeSession(BRANCH_TRANSACTIONS_KEY) && getCakeSession(BRANCH_TRANSACTIONS_KEY) !== '-1') {
				$conditions['Journal.branch_id'] = getCakeSession(BRANCH_TRANSACTIONS_KEY);
			} else if (getCakeSession(BRANCH_TRANSACTIONS_KEY) === '-1') {
				$conditions['Journal.branch_id'] = array_keys(getStaffBranchesSuspended());
			} else {
				//$conditions['Journal.branch_id'] = getActiveBranchIds();
			}
		}
		$currentStaffID = getAuthOwner('staff_id');
		$accountBalanceCondition = ' ';
		if (0 != $currentStaffID && check_permission(MANAGE_OWN_JOURNALS) && !check_permission(VIEW_ALL_JOURNALS)) {
			$conditions['Journal.staff_id'] = $currentStaffID;
			$accountBalanceCondition = " AND J.staff_id = $currentStaffID ";
		}

        $fy_cond=$this->Journal->get_financial_conditions($fy_id,'Journal');;
        if(!empty($fy_cond))
            $conditions[]=$fy_cond;
        $transactions = $this->paginate('JournalTransaction', $conditions);

        $this->setup_nav_data($transactions);
		$this->loadModel('Journal');
		if($order == "ASC"){
			
            $starting_balance = $this->Journal->calculate_account_balance_before($account_id,$transactions[0],$currency,$fy_id, $accountBalanceCondition);
			
			$transactions = $this->Journal->calculate_account_balance_after_transactions($transactions,$starting_balance,$order,$currency);
			
		}else{
			
            $starting_balance = $this->Journal->calculate_account_balance_before($account_id,end($transactions),$currency,$fy_id, $accountBalanceCondition);
			$transactions = $this->Journal->calculate_account_balance_after_transactions($transactions,$starting_balance,$order,$currency);
			
		}
		
		$this->set('default_currency',$this->Journal->get_default_currency());
		if(empty($currency)){
//			$this->set('show_other_currencies',true);
		$this->set('currency_code', $this->Journal->get_default_currency());
		}else{
			
			$this->set('currency_code', $currency);
		}
		$this->set('transactions', $transactions);
		
		
		$this->loadModel('Staff');
		$this->set('staffs', $this->Staff->getList());
		if (ifPluginActive(BranchesPlugin)) {
			$this->loadModel('Branch');
			$this->set('branches', $this->Branch->getList(false));
		}
		$this->set('current_staff_id',getAuthStaff('id'));
	
	}
	
	

	function owner_delete($id = null) {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Journal', true))]);
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('account',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('Account', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('Account', true);
		 }
        //prevent deletion if the accccount have transaction
		$journalAccounts = $this->JournalAccount->find('all',array('conditions'=>array('JournalAccount.id'=>$id)));
        //dd($journalAccounts);
			if(isset($_GET['folderid']) && !empty(trim($_GET['folderid']))){
			$this->set('is_ajax',true);
			$this->set('folderid', urldecode($_GET['folderid']));
		}
		$this->loadModel('JournalCat');
		$action_line_data = array();
		$action_line_id = ACTION_JOURNAL_ACCOUNT_DELETE;
        if(isset($_POST['iframe'])) {
            $redirectData = ChartOfAccountsUtil::getSaveAccountRedirectUrl($this->data);
        } else {
            if(isset($_POST['folderid'])){
                $redirectData = array('controller'=>'journal_cats','action' => 'tree','#' =>  'folderid='.$_POST['folderid'],'escape' => false);
            }else {
                $redirectData = array('controller'=>'journal_cats','action'=>'tree');
            }
        }

		foreach( $journalAccounts as $k =>  $journalAccount )
        {
            if(!$this->JournalAccount->deletAble($journalAccount) )
            {
				if(isset($_GET['iframe']) && $_GET['iframe']) {
					return $this->render("../empty");
				}
				if(IS_REST) return $this->cakeError("error400", ["message"=>sprintf (__('You can\'t delete %s for Account %s because it has transactions', true), __('account',true), $journalAccount['JournalAccount']['name'] )]);
                $this->flashMessage(sprintf (__('You can\'t delete %s for Account %s because it has transactions', true), __('account',true), $journalAccount['JournalAccount']['name'] ));
                $this->redirect($redirectData);
            }
			$action_line_data[$k]['param3'] = $journalAccount['JournalAccount']['name'] . ' - ' . $journalAccount['JournalAccount']['code'];
			$action_line_data[$k]['param4'] = $journalAccount['JournalAccount']['type'];
			$parent_cat = $this->JournalCat->find('first',array('conditions' => array('JournalCat.id' => $journalAccount['JournalAccount']['journal_cat_id'])));
			if($parent_cat){
			$action_line_data[$k]['param5'] = $parent_cat['JournalCat']['name'] . ' - ' . $parent_cat['JournalCat']['code'];
			$action_line_data[$k]['secondary_id'] = $parent_cat['JournalCat']['id'];
			}
			
			$action_line_data[$k]['param1'] = JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT;
			$action_line_data[$k]['primary_id'] = $journalAccount['JournalAccount']['id'];
			
        }


		if (empty($journalAccounts)){
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), __($module_name,true))]);
			$this->flashMessage(sprintf(__('%s not found', true), __($module_name,true)));
			 $this->redirect($redirectData);
		}
		if(IS_REST){
			$_POST['submit_btn'] = 'yes';
			$_POST['ids'] = [$id];
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->JournalAccount->deleteAll(array('JournalAccount.id'=>$_POST['ids']), $cascade = true, $callback = true)) {
		        $this->loadModel('CostCentersJournalAccount');
				$this->CostCentersJournalAccount->deleteAll(['CostCentersJournalAccount.journal_account_id' => $_POST['ids']]);

				foreach($action_line_data as $k => $action_line)
					$this->add_actionline ($action_line_id, $action_line);
				
				if(IS_REST){
					$this->set("message", sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb));
					$this->render("success");
					return;
				}
				$this->flashMessage(sprintf (__('%s deleted', true), __($module_name,true)), 'Sucmessage');
				$this->redirect($redirectData);
			}
			else{
				if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect($redirectData);
            }
		}
		$this->set('journalAccounts',$journalAccounts);
		$this->set('title_for_layout',  __('Delete Journal Account',true));
	}

	function owner_hide($accountId)
	{
		$account = $this->JournalAccount->find('first',array('conditions'=>array('JournalAccount.id'=>$accountId)));
		if(!$account || $account['JournalAccount']['is_hidden']) {
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('Invalid %s', true), __('Account', true))]);
			$this->flashMessage(sprintf (__('Invalid %s', true), __('Account', true)));
			$this->redirect(array('action'=>'index'));
		}
		$canBeHidden = $this->JournalAccount->canBeHidden($account['JournalAccount']['id']);
		if(!$canBeHidden) {
			if(IS_REST) $this->cakeError("error400", ["message"=>__('This account can not be hidden', true)]);
			$this->flashMessage(__('This account can not be hidden', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->JournalAccount->id = $accountId;
		$this->JournalAccount->saveField('is_hidden', 1);
		$request = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRequest(
			$accountId,
			EntityKeyTypesUtil::JOURNAL_ACCOUNT,
			ActionLineMainOperationTypesUtil::UPDATE_ACTION,
			"#{$account['JournalAccount']['code']} {$account['JournalAccount']['name']}",
			"/v2/owner/chart-of-accounts/accounts/".$accountId,
			['is_hidden'=> __('Yes', true)],
			['is_hidden'=>__('No', true)]
		);
		(new \App\Services\ActivityLogService())->addActivity($request);

		if(IS_REST){
			$this->set("message", __('Account has been hidden', true));
			$this->render("success");
			return;
		}
		$this->flashMessage(__('Account has been hidden', true), 'Sucmessage');
		$this->redirect("/v2/owner/chart-of-accounts/cats/" . $account['JournalAccount']['journal_cat_id']);
	}

	function owner_unhide($accountId)
	{
		$account = $this->JournalAccount->find('first',array('conditions'=>array('JournalAccount.id'=>$accountId)));
		if(!$account || !$account['JournalAccount']['is_hidden']) {
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('Invalid %s', true), __('Journal Account', true))]);
			$this->flashMessage(sprintf (__('Invalid %s', true), __('Journal Account', true)));
			$this->redirect(array('action'=>'index'));
		}
		$this->JournalAccount->id = $accountId;
		$this->JournalAccount->saveField('is_hidden', 0);
		if(IS_REST){
			$this->set("message", __('Account has been unhidden', true));
			$this->render("success");
			return;
		}
		$request = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRequest(
			$accountId,
			EntityKeyTypesUtil::JOURNAL_ACCOUNT,
			ActionLineMainOperationTypesUtil::UPDATE_ACTION,
			"#{$account['JournalAccount']['code']} {$account['JournalAccount']['name']}",
			"/v2/owner/chart-of-accounts/accounts/".$accountId,
			['is_hidden'=> __('No',true)],
			['is_hidden'=> __('Yes',true)]
		);
		(new \App\Services\ActivityLogService())->addActivity($request);

		$this->flashMessage(__('Account has been unhidden', true), 'Sucmessage');
		$this->redirect("/v2/owner/chart-of-accounts/cats/" . $account['JournalAccount']['journal_cat_id']);
	}
	//used in adavnced search element
	function owner_json_find() {
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        $result = [];
        $params = $this->params['url'];
        if(isset($params['emptyText'])) {
            $result [] = [
                'name' => $params['emptyText'],
                'id' => $params['emptyValue'],
            ];
        }
        if ($value !== '') {

			$this->loadModel('Client');
            $matches = \Izam\Daftra\Journal\Locales\TranslateService::match($value);
			$raw_value = $this->JournalAccount->getDataSource()->value($value,'string');
            $value = $this->JournalAccount->getDataSource()->value('%'.$value.'%','string');
            $accounts = array();
            $conditions = array();
            
			if ($_GET['apply_specify_branch'] && ifPluginActive(BranchesPlugin) && settings::getValue(BranchesPlugin,'specify_accounts_branches')){
	
				$JournalAccount = GetObjectOrLoadModel('JournalAccount');
					$accountIds = $JournalAccount->getPermittedAccountIds();
				
					if($accountIds && $accountIds !== true)
					{
						$conditions[] = 'JournalAccount.id IN ('.implode(',', $accountIds).')';
					}
				 
			}
			
			if (!$this->Client->global_need_clients() && !check_permission(VIEW_OWN_JOURNALS) && !check_permission(Clients_View_All_Clients) ) {
                $this->loadModel('Post');
                $conditions[] = '(JournalAccount.staff_id = ' . $owner['staff_id'] . '  OR  JournalAccount.id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.staff_id=' . $owner['staff_id'] . ' )) ';
            }

			$this->loadModel('JournalCat');
			$journalCats = $this->JournalCat->find('list');

			if($_GET['parent_id'])
			{
				$journal_cat_id = (int)$_GET['parent_id'];
				if (!empty($_GET['parent_type']) && in_array($_GET['parent_type'], ['supplier', 'client'])) {
					$conditions[] = '(FIND_IN_SET('.$journal_cat_id.',JournalAccount.parent_cat_ids) OR JournalAccount.id IN (SELECT id FROM journal_accounts WHERE entity_type = "'.$_GET['parent_type'].'"))';
				} else {
					$conditions[] = 'FIND_IN_SET('.$journal_cat_id.',JournalAccount.parent_cat_ids)';
				}
			}
            if ($this->Client->global_need_clients() || check_permission(Clients_View_All_Clients) || check_permission(Clients_View_his_own_Clients) || check_permission(VIEW_OWN_JOURNALS)) {
				
                $conditions['OR'] = array("JournalAccount.code like $value ", "lower(JournalAccount.name) like lower($value)");
                
				if(create_translate_matches_condition($matches, 'JournalAccount.name') !== '()'){
					$conditions['OR'][] = create_translate_matches_condition($matches, 'JournalAccount.name');
				}


				/**
				 * Force utf8_unicode_ci collation on the `name` field to fix partial match issues with Arabic characters.
				 * 
				 * The default collation `utf8_general_ci` does not accurately compare certain Unicode presentation forms 
				 * (e.g., `ﻳ` vs `ي`, `ﻮ` vs `و`), causing searches like LIKE '%يون%' to fail on some records.
				 * 
				 * By explicitly using `COLLATE utf8_unicode_ci`, we ensure proper character comparison and improve 
				 * search accuracy for Arabic names stored with presentation forms or composed characters.
				 */
				$conditions['OR'][] = "JournalAccount.name COLLATE utf8_unicode_ci LIKE $value";


                $this->JournalAccount->recursive = -1;
				if(!isset($_GET['display_hidden'])) {
					$conditions[] = "JournalAccount.is_hidden = 0";
				}
                $accounts = $this->JournalAccount->getJournalAccounts('all', array('limit' => 100, 'conditions' => $conditions, 'order' => array("CASE WHEN `JournalAccount`.`code` = {$raw_value} THEN 0 ELSE 1 END ASC ",'JournalAccount.name','JournalAccount.parent_cat_ids')));
                $catName = isset($_GET['cat_name']) && $_GET['cat_name'];
                foreach ($accounts as $account) {

                    $full_name = '';
                    if (!empty($account['JournalAccount']['name']))
                        $full_name = __at($account['JournalAccount']['name']) ;
                    $data = array(
						'name' => ( $account['JournalAccount']['code'] . ' - ' . __at($account['JournalAccount']['name'])  ),
                        'id' => $account['JournalAccount']['id'],
                        'details' => $account['JournalAccount']['parent_cat_ids']
                    );
                    $result[] = $data;
                }
            }

            if(isset($_GET['with_cat'])) {
				$result = array_merge($result,$this->JournalCat->journalCatSearch($_GET['q'])?:[]);
			}

            foreach ($result as &$data) {
				if($catName)
				{
					$accountCats = explode(',', $data['details']);
					$accountCatNames = [];
					foreach ($accountCats as $catId)
					{
						if(isset($journalCats[$catId]))
						{
							$accountCatNames[]= __at($journalCats[$catId]);
						}
					}
					$data['cats'] = implode((' > '), $accountCatNames);
				}
			}
			header('Content-Type: application/json');
            echo json_encode($result);
            die();
        }else {
			
            echo json_encode($result);
            die();
        }
    }

	function owner_json_find_with_id() {
        $id = trim($_GET['id']);
		if(empty($id)) {
			$this->owner_json_find();
		}else{
			$options = [
				"conditions" => [
					"JournalAccount.id" => $id
				]
			];
			$account = $this->JournalAccount->getJournalAccounts("first",$options);
			header('Content-Type: application/json');
			if(!$account){
				echo json_encode([]);
			}else{
				echo json_encode([
					"id" => $account['JournalAccount']['id'],
					'details' => $account['JournalAccount']['parent_cat_ids'],
					'name' => $account['JournalAccount']['code'] . ' - ' . __at($account['JournalAccount']['name']),
				]);
			}
            die();
		}
	}
}
?>
