<?php

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\StoreStatusUtil;
use Izam\Limitation\Utils\LimitationUtil;

class StoresController extends AppController {

    var $name = 'Stores';
    var $helpers = array('Html', 'Form');
    var $components = ["ItemPermissions"];

    
    function owner_index ( ) {
        $this->pageTitle=__('Warehouses',true);
        $this->Store->recursive = -1 ;
        $this->paginate['Store']['order'] = 'Store.name';
		$stores = $this->paginate('Store' );
        $this->loadModel('ItemPermission');
        $authStores = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE, ItemPermission::PERMISSION_VIEW, null, true);
        if(is_array($stores)) {
            $stores = array_filter($stores, function ($item) use ($authStores) {
                return in_array($item['Store']['name'], $authStores);
            });
        }
        $this->set ( 'all_stores' , $stores);//   $this->Store->get_all_stores ( ) ) ;
        $this->set("results_count", $this->Store->find ( 'count' ,['conditions' => ['Store.active' => 1 ]] ));// $results_count[0][0]["results_count"]);
        if (!check_permission(Track_Inventory) &&  !check_permission(Edit_General_Settings) ) {
                            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->setup_nav_data($stores);
		if(IS_REST){
			$this->set('rest_items', $stores);
			$this->set('rest_model_name', "Store");
			$this->render("index");
		}
    }
    function owner_add ( ) {
        $this->pageTitle=__('Add Warehouse',true);
        if (!check_permission(Edit_General_Settings)  ) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        // Check site limitation
        $this->handleSiteLimit(
            checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::WAREHOUSES_COUNT),
            ['action' => 'index']
        );

        if ( !empty ($this->data ) ) {
            if ($this->data['Store']['primary'] == 1)
            {
                if ($this->data['Store']['active']==StoreStatusUtil::SUSPEND) {
                    $this->flashMessage(__t('You cannot suspend the primary warehouse and you need to choose another primary then suspend the warehouse'),'Errormessage','secondaryMessage');
                    $this->redirect(['action'=>'add']);
                }
                $this->data['Store']['active'] = 1;
            }
            $this->Store->create () ; 
            if ( $this->data['Store']['primary'] == 1 )
            {
                $this->data['Store']['active'] = 1 ; 
                $this->Store->updateAll( ['Store.primary' => 0 ] );
            }
            if ( $this->Store->addStore($this->data) ){
                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_STORE)
                    ->save($this->data['JournalAccountRoute'], $this->Store->id);
                $details= $this->data['Store'];
                $this->add_actionline(ACTION_ADD_WAREHOUSE,array('primary_id'=>$this->Store->id,'param1'=>$details['name'],'param2'=>$details['primary'],'param3'=>$details['active']));
                if(IS_REST){
					$this->set('id', $this->Store->id);
					$this->render('created');
					return;
				}
                $this->flashMessage(sprintf(__('%s has been saved', TRUE) , $this->data['Store']['name'] ) , 'Sucmessage' );
                $this->redirect('/owner/stores');
			} else {
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Store->validationErrors]);
			}
        }
        $this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_STORE);
        $this->_formCommon();
    }

    private function _formCommon($id = null){
        $this->loadModel('Journal');
        $this->setAccountRoute('stores_accounts_routing', Journal::STORE_ACCOUNT_ENTITY_TYPE, $id);
    }

    function owner_edit (  $id = null) {

        $this->loadModel('ItemPermission') ;
        $reread = $this->Store->findById ($id ) ;
        if (!check_permission(Edit_General_Settings)  ) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        $this->pageTitle= sprintf(__('Edit Warehouse %s', true), $reread['Store']['name']);
		if(empty($reread)&&IS_REST) $this->cakeError('error404', array('message' => __('Store not found', true)));

        if(!$reread && !IS_REST){
            $this->flashMessage(__('Store not found', TRUE));
            $this->redirect(['action'=>'index']);
        }
		if(IS_REST) $this->data['Store']['id'] = $id;
        if ( !empty( $reread ) ){
            if ( !empty ($this->data ) ) {

                // Check site limitation
                if ($this->data['Store']['primary'] == 1)
                {
                    if ($this->data['Store']['active']==StoreStatusUtil::SUSPEND) {
                        $this->flashMessage(__t('You cannot suspend the primary warehouse and you need to choose another primary then suspend the warehouse'),'Errormessage','secondaryMessage');
                        $this->redirect(['action'=>'edit',$id]);
                    }
                }
                if ($this->data['Store']['active']==StoreStatusUtil::ACTIVE && ($reread['Store']['active'] !=StoreStatusUtil::ACTIVE)) {
                    $this->handleSiteLimit(
                        checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::WAREHOUSES_COUNT),
                        ['action' => 'index']
                    );
                }

                if ( $this->data['Store']['primary'] == 1 )
                {
                    $this->data['Store']['active'] = 1 ; 
                    $this->Store->updateAll( ['primary' => 0 ] );
                }

                if($this->Store->updateStore ($this->data , $id )){
                    RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_STORE)
                        ->save($this->data['JournalAccountRoute'], $id);
                }

                $details= $this->data['Store'];
                $this->add_actionline(ACTION_EDIT_WAREHOUSE,array('primary_id'=>$this->Store->id,'param1'=>$details['name'],'param2'=>$details['primary'],'param3'=>$details['active']));
                if(IS_REST){
					$this->render('success');
					return;
				}
                $this->flashMessage(sprintf(__('%s has been saved', TRUE) , $this->data['Store']['name'] ) , 'success' );
                $this->redirect(['action'=>'view',$id]);
            }else {
                $this->data = $reread ;
                $this->set('store_staffs' , $this->ItemPermission->find('all' , ['conditions' => ['item_id' => $this->data['Store']['id'] , 'item_type' => ItemPermission::ITEM_TYPE_STORE  ] ]));
            }
            $this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_STORE , $id);
             $this->_formCommon($id);
            $this->render ('owner_add') ;
        }
         
    }
    function owner_delete (  $id = null ) {
        if (!check_permission(Edit_General_Settings)  ) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $reread_store = $this->Store->findById ( $id ) ;
		if(empty($reread_store)&&IS_REST) $this->cakeError('error404', array('message' => __('Store not found', true)));
		
        if ( empty($reread_store['Store']['primary'] )  ){
            $this->loadModel ( 'StockTransaction');
            
            $transactions = $this->StockTransaction->find('first' , ['conditions'=> ['Product.id is not null' , 'StockTransaction.store_id' => $id]]);
            $shifts = null;
            if (ifPluginInstalled(PosPlugin)) {
            	$this->loadModel('PosShift');
	            $shifts = $this->PosShift->isStoreUsed($id);
            }

            //Check is used in purchase order or purchase quotation
            if(ifPluginActive(PURCHASE_CYCLE_PLUGIN)){
            	$this->loadModel('PurchaseOrder');
                $purchaseOrders = $this->PurchaseOrder->isStoreUsed($id, [PurchaseOrder::PURCHASE_ORDER]);
                if(!empty($purchaseOrders)){
                    $this->flashMessage(__("You cannot delete the warehouse as it’s already selected in the Purchase Order",true)." <a href='/v2/owner/entity/".EntityKeyTypesUtil::PURCHASE_ORDER."/".$purchaseOrders["PurchaseOrder"]["id"]."/show'>#{$purchaseOrders["PurchaseOrder"]["no"]}</a>" , 'Errormessage');
                    $this->redirect('/owner/stores');
                    exit;
                }

                $purchaseQuotations = $this->PurchaseOrder->isStoreUsed($id, [PurchaseOrder::PURCHASE_QUOTATION]);
                if(!empty($purchaseQuotations)){
                    $this->flashMessage(__("You cannot delete the warehouse as it’s already selected in the Purchase Quotation",true)." <a href='/v2/owner/entity/".EntityKeyTypesUtil::PURCHASE_QUOTATION."/".$purchaseQuotations["PurchaseOrder"]["id"]."/show'>#{$purchaseQuotations["PurchaseOrder"]["no"]}</a>" , 'Errormessage');
                    $this->redirect('/owner/stores');
                    exit;
                }
            }

            if ( empty ( $transactions ) && empty($shifts) ){
                $this->Store->delete ( $id  ) ;
				if(IS_REST){
					$this->set("message", sprintf(__('%s has been deleted', true), ucfirst("Store")));
					$this->render("success");
					return;
				}
				$details=$reread_store['Store'];
                $this->add_actionline(ACTION_DELETE_WAREHOUSE,array('primary_id'=>$id,'param1'=>$details['name'],'param2'=>$details['primary'],'param3'=>$details['active']));
                $this->flashMessage(sprintf (__('Warehouse %s deleted', TRUE) , $reread_store['Store']['name'] ) , 'Sucmessage');
            }else {
                $msg = __("This warehouse has existing transactions, you can make it inactive.", true);
                if (!empty($shifts)) {
                    if (isset($shifts['PosShift']))
                        $msg .= "<a href='/owner/pos_shifts/view/{$shifts['PosShift']['id']}'>#" . $shifts['PosShift']['id'] . "</a>";
                    elseif (isset($shifts['Category']))
                        $msg .= "<a href='/owner/categories/edit/{$shifts['Category']['id']}/?type=" . Category::CATEGORY_TYPE_POS_Device . "'>#" . $shifts['Category']['id'] . "</a>";

                }
                if (IS_REST) {
                    $this->cakeError("error500", ["message" => $msg]);
                }
                $this->flashMessage($msg, 'Errormessage');
            }
            
        } else {
            if(IS_REST) {
                $this->cakeError("error500", ["message" => __("You can't delete primary warehouse", true)]);
            }
            $this->flashMessage(__("You can't delete primary warehouse", TRUE));
        }
        $this->redirect('/owner/stores');
    }
	
	public function api_view($id = null) {
		if(!check_permission(Track_Inventory)){
			$this->cakeError('error403');
        }
		$store = $this->Store->find('first', ['conditions' => array('Store.id' => $id)] );
		if(empty($store)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Store", true))));
        else $this->set('rest_item', $store);
		$this->set('rest_model_name', "Store");
		$this->render("view");
	}

    public function owner_view($id = null)
    {
        if (!check_permission(Track_Inventory)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $store = $this->Store->find('first', ['conditions' => array('Store.id' => $id)]);
        $this->loadModel('ItemPermission');
        $this->setup_nav_view($id);

        $this->set('store_permissions', $this->Store->get_store_permissions($id));

        $this->set('store', $store);
        $this->setDefaultViewData();

        $Journal = GetObjectOrLoadModel('Journal');
        $account = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::STORE_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id]);
        $this->set('account', $account);

        $this->view = 'izam';
        $this->render('stores/show');
    }


    public function owner_timeline($id = false)
    {
        $store = $this->Store->findById($id);
        $this->set('store', $store);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Store', array('primary_id' => $id));

        $action_list = $timeline->getActionsList();
        $timeline->init(array('primary_id' => $id), $action_list);
        $data = $timeline->getDataArray();

        $this->set('data', $data);
        $this->loadModel('ActionLine');

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $actions[$key] = $action;
            }
        }
        $this->set('actions', $actions);
    }
}



?>
