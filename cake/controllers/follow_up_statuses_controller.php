<?php

use Izam\Daftra\Common\Utils\PermissionUtil;
use function PHPUnit\Framework\isNull;

class FollowUpStatusesController extends AppController {

    var $name = 'FollowUpStatuses';

    function owner_index($item_type = null, $data = null) {
        if(!check_permission(PermissionUtil::EDIT_CLIENT_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('Post');
		if(IS_REST){
			$this->loadModel ( 'Post');
			$types = [
				"client" => Post::CLIENT_TYPE,
				"invoice" => Post::INVOICE_TYPE,
				"estimate" => Post::ESTIMATE_TYPE,
				"purchase_order" => Post::PO_TYPE,
				"staff" => Post::STAFF_TYPE,
				"supplier" => Post::SUPPLIER_TYPE,
				"product" => Post::PRODUCT_TYPE,
				"work_order" => Post::WORK_ORDER_TYPE
			];
			$item_type = $types[$item_type];
			$actions = $this->paginate(array('FollowUpStatus.item_type' => $item_type));
		} else {
			$statuses = $this->FollowUpStatus->find('all', array('conditions' => array('FollowUpStatus.item_type' => $item_type)));
		}
        $this->set('statuses', $this->FollowUpStatus->get_statuses());
        $this->set('item_types', $this->Post->getItemTypes());
        $this->set('colors', $this->FollowUpStatus->getcolorlist());
        $this->set('item_type', $item_type);
		$statuses = $this->FollowUpStatus->find('all', array('conditions' => array('FollowUpStatus.item_type' => $item_type) , 'order' => ['FollowUpStatus.display_order ASC', 'FollowUpStatus.id ASC']));
        if($item_type == Post::CLIENT_TYPE && $data){
            $statuses = $data;
        }
        $this->set('datas', $statuses);
		if(IS_REST){
			$this->set('rest_items', $statuses);
			$this->set('rest_model_name', "FollowUpStatus");
			$this->render("index");
		}
    }
	
	public function api_view($id = null) {
        if(!check_permission(PermissionUtil::EDIT_CLIENT_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$follow_up_status = $this->FollowUpStatus->find("first", ["conditions" => array("FollowUpStatus.id" => $id)] );
		if(empty($follow_up_status)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Follow up status", true))));
        else $this->set('rest_item', $follow_up_status);
		$this->set('rest_model_name', "FollowUpStatus");
		$this->render("view");
	}
	
	public function api_delete($id = null) {
        if(!check_permission(PermissionUtil::EDIT_CLIENT_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$follow_up_status = $this->FollowUpStatus->find("first", ["conditions" => array("FollowUpStatus.id" => $id)] );
		if(empty($follow_up_status)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Follow up status", true))));
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel ( 'Client');
        $this->loadModel ( 'Invoice');
        if ( ifPluginActive(WorkOrderPlugin) ) {
            $this->loadModel ( 'WorkOrder');
        }
		$class = FollowUpStatus::$types_diff[$follow_up_status['FollowUpStatus']['item_type']]['class'];
		$items = $this->{$class}->find ('all' ,['conditions' =>   [
			(($class=="WorkOrder") ? $class.'.follow_up_status_id':$class.'.follow_up_status')  => $id
				] ]);
		if(!empty($items)) $this->cakeError("error500", ["message"=>__("You can't delete this status because it is assigned", true)]);
		if($this->FollowUpStatus->delete($id)){
			$this->set("message", sprintf(__('%s has been deleted', true), __("Follow up status", true)));
			$this->render("success");
			return;
		} else {
			$this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
		}
	}

    function owner_jsonlist($item_type = null) {
        $list = $this->FollowUpStatus->getLisWithColor($item_type,false,true);
        if (check_permission(Edit_General_Settings)) {

            $list[] = array('name' => false, 'value' => false, 'data_divider' => "true");
            $list[] = array('data_content' => '<span class="text"><i class="fa fa fa-cog"></i> Edit Statuses List </span>','name' => __('Edit Statuses List', true), 'value' => '-1', "data_icon" => "fa fa-cog");
        }
        echo json_encode($list);
        die();
    }

    function owner_update($item_type = null ) {
        if(!check_permission(PermissionUtil::EDIT_CLIENT_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('Post');
        $site = getAuthOwner();
        if ( is_null ( $item_type )){
            $item_type = Post::CLIENT_TYPE;
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            //$item_type = $_POST['item_type'];
            $this->FollowUpStatus->recursive = -1;
            $data = $this->FollowUpStatus->find('list', array('conditions' => array('FollowUpStatus.item_type' => $item_type)));
            $delete_data_array = array();
            foreach ($this->data['FollowUpStatus'] as $i => $item) {
                $data_posted_ids[] = $item['id'];
            }
            if (!is_array($data_posted_ids))
                $data_posted_ids = array();
            $delete_data_array = array_diff(array_keys($data), $data_posted_ids);
            $this->FollowUpStatus->deleteAll(array('FollowUpStatus.id' => $delete_data_array));
            if (!empty($this->data['FollowUpStatus'])) {
                foreach ($this->data['FollowUpStatus'] as $i => $item) {
                    $item['site_id'] = getAuthOwner('id');
                    $this->FollowUpStatus->create();
                    $data['FollowUpStatus'] = $item;
//                    $data['FollowUpStatus']['item_type'] = Post::CLIENT_TYPE;
                    $data['FollowUpStatus']['item_type'] = $item_type;
                    $data['FollowUpStatus']['status'] = $item['status'];
                    $data['FollowUpStatus']['display_order'] = $i+1;
                    if($item_type == Post::CLIENT_TYPE && $item['require_followup'] == 1 && (!is_numeric($item['followup_after']) || empty($item['followup_after']))){
                        $errors[$i]['followup_after'] = $this->FollowUpStatus->validationErrors[$i] = __('Valid number required', true);
                    }elseif (!$this->FollowUpStatus->save($data)) {
                        $errors[$i] = $this->FollowUpStatus->validationErrors;
                    }
                }
                $data = null;
                if (!empty($errors)) {
                    $this->flashMessage(__('Could not update Statuses settings', true));
                    $this->FollowUpStatus->validationErrors = $errors;
                    if($item_type == Post::CLIENT_TYPE){
                        $data = array_map(function($item){
                            return [
                                'FollowUpStatus' => $item
                            ];
                        }, $this->data['FollowUpStatus']);
                    }
                } else {
                    $this->flashMessage(__('Statuses have been updated', true), 'Sucmessage');
                    $url =FollowUpStatus::$types_diff[$item_type]['url'];
                    //$url = array('controller' => 'clients','action' => 'index');
                    if (isset($_GET['box'])) {
						 $url = array('action' => 'update' , $item_type);
                        $url['?'] = array('box' => 1, 'updateparent' => true);
                    }
                    $this->redirect($url);
                }
            }
        }
        $this->owner_index($item_type, $data);
//        $this->owner_index(Post::CLIENT_TYPE);
        $this->render('owner_index');
    }
    function owner_check_status_delete ( $id = null ) {
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel ( 'Client');
        $this->loadModel ( 'Invoice');
        $this->loadModel ( 'PurchaseOrder');
        $this->loadModel ( 'Staff');
        $customFk = ['WorkOrder', 'PurchaseRequest','QuotationRequest'];
        if ( ifPluginActive(WorkOrderPlugin) ) {
            $this->loadModel ( 'WorkOrder');
        }
        if ( ifPluginActive(PURCHASE_CYCLE_PLUGIN) ) {
            $this->loadModel ( 'PurchaseRequest');
            $this->loadModel ( 'QuotationRequest');
        }
        $reread_status = $this->FollowUpStatus->read(null ,$id ) ;  
        if( !empty( $reread_status) ){  
            $class = FollowUpStatus::$types_diff[$reread_status['FollowUpStatus']['item_type']]['class']; 
            $items = $this->{$class}->find ('all' ,['conditions' =>   [
                ((in_array($class, $customFk)) ? $class.'.follow_up_status_id':$class.'.follow_up_status')  => $id
                    ] ]);
       
            if ( !empty ( $items ) ){
                echo json_encode ( ['result' => 1  ]) ; 
                die (  ) ;
            }else {
                echo json_encode ( ['result' => 0]) ; 
                die (  ) ;
            }
        }else {
            echo json_encode ( ['result' => 0  ]) ; 
            die ( ) ;
        }
    }
}

?>
