<?php

class RulesController extends AppController {

    var $name = 'Rules';

    /**
     * @var Rule
     */
    var $helpers = array('Html', 'Form');

    function owner_index() {

        $this->Rule->recursive = 0;
        $conditions = $this->_filter_params();
        $this->set('rules', $this->paginate('Rule', $conditions));
        $this->set('title_for_layout',  __('Rule', true));
    }

    function owner_view($id = null) {
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('rule', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $this->set('rule', $this->Rule->read(null, $id));
    }

    function owner_add_rule_permission() {

        if (!empty($this->data)) {
            $this->Rule->create();
            if ($this->Rule->save($this->data)) {
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Rule', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Rule', true)));
            }
        }

        $this->set('title_for_layout',  __('Add Role', true));
    }

    function owner_add() {
        $this->loadModel('Permission');
        $this->loadModel('Plugin');
        if (!empty($this->data)) {
            foreach ($this->data['RulesPermission'] as $key => $rp) {
                if ($rp['permission_id'] == 0) {
                    unset($this->data['RulesPermission'][$key]);
                }
            }

            $this->Rule->create();
            if ($this->Rule->saveall($this->data)) {
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Rule', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Rule', true)));
            }
        }
        $getCurrentPlugin = getCurrentPlugin();
        $this->set('Pluginlist', $this->Plugin->find('list'));
        $this->set('current_plugin', $getCurrentPlugin);
        //
        foreach ($getCurrentPlugin as $plugin) {
            $plugin_array[] = $plugin['SitePlugin']['plugin_id'];
        }
        $Permission = $this->Permission->find('all', array('conditions' => array('Permission.plugin_id' => $plugin_array)));
        //print_r($Permission);
        $this->set('Permission', $Permission);
        $this->set('title_for_layout',  __('Add Role', true));
    }

    function owner_edit($id = null) {
        $this->loadModel('Permission');
        $this->loadModel('Plugin');
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), __('rule', true)));
            $this->redirect(array('action' => 'index'));
        }

        $rule = $this->Rule->read(null, $id);

        if (!empty($this->data)) {
            foreach ($this->data['RulesPermission'] as $key => $rp) {
                if ($rp['permission_id'] == 0) {
                    unset($this->data['RulesPermission'][$key]);
                }
            }
            $this->Rule->RulesPermission->deleteAll(array('RulesPermission.rule_id' => $this->data['Rule']['id']), false);
            if ($this->Rule->saveall($this->data)) {
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Rule', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Rule', true)));
            }
        }
        if (empty($this->data)) {
            
            $this->data = $rule;
            $this->set('rule',$rule);
        }
        $getCurrentPlugin = getCurrentPlugin();
        $this->set('Pluginlist', $this->Plugin->find('list'));
        $this->set('current_plugin', $getCurrentPlugin);
        //
        foreach ($getCurrentPlugin as $plugin) {
            $plugin_array[] = $plugin['SitePlugin']['plugin_id'];
        }
        $Permission = $this->Permission->find('all', array('conditions' => array('Permission.plugin_id' => $plugin_array)));
        //print_r($Permission);
        $this->set('Permission', $Permission);
        $this->set('title_for_layout',  __('Edit Role', true));
        $this->render('owner_edit');
    }

    function owner_delete($id = null) {
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('rule', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('rule', true);
        if (is_countable($id) && count($id) > 1) {
            $module_name = __('rules', true);
        }
        $conditions = array();
        $conditions['Rule.id'] = $id;
        $rules = $this->Rule->find('all', array('conditions' => $conditions));
        if (empty($rules)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->Rule->deleteAll(array('Rule.id' => $_POST['ids']))) {
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  __('Delete Rule', true));

        $this->set('rules', $rules);
        $this->set('module_name', $module_name);
    }

}
