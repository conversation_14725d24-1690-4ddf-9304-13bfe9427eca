<?php
class ClosedPeriodsController extends AppController {

	var $name = 'ClosedPeriods';

	/**
	 * @var ClosedPeriod
	 */
	var $ClosedPeriod;
	var $helpers = array('Html', 'Form');


	function action_line($type,$data){
		$action_line['primary_id'] = $data['ClosedPeriod']['id'];
		$action_line['param1'] = $data['ClosedPeriod']['from'];
		$action_line['param2'] = $data['ClosedPeriod']['to'];
		$action_line['param3'] = $data['ClosedPeriod']['active'];
		$this->add_actionline($type,$action_line);
	}
	
	function owner_index() {
		if (!check_permission(VIEW_CLOSED_PERIODS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		$this->ClosedPeriod->recursive = 0;
		$conditions = $this->_filter_params();
		if(isset($conditions['ClosedPeriod.date >='])){
			$conditions['ClosedPeriod.from >='] = $conditions['ClosedPeriod.date >='];
			unset($conditions['ClosedPeriod.date >=']);
		}
		if(isset($conditions['ClosedPeriod.date <='])){
			$conditions['ClosedPeriod.to <='] = $conditions['ClosedPeriod.date <='];
			unset($conditions['ClosedPeriod.date <=']);
		}
//				die(debug($conditions));
		$this->paginate = ['order' => ['to' => 'DESC']];
		$this->set('closedPeriods', $this->paginate('ClosedPeriod', $conditions));
	}

	function owner_view($id = null) {
		if (!check_permission(VIEW_CLOSED_PERIODS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id) {
			$this->flash(__(sprintf ('Invalid %s', 'closed period'),true), array('action'=>'index'));
		}
		$this->set('closedPeriod', $this->ClosedPeriod->read(null, $id));
	}
	/*
	 * toggle the active status of a closed period
	 */
	function owner_toggle($id = null){
		$this->loadModel('FinancialYear');
		if (!check_permission(MANAGE_CLOSED_PERIODS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id) {
			$this->flashMessage(__(sprintf ('Invalid %s', 'closed period'),true) );
			$this->redirect(['action' => 'index']);
		}
		$cp = $this->ClosedPeriod->findById($id);
		if(!$cp){
			$this->flashMessage(__(sprintf ('Invalid %s', 'closed period'),true) );
            $this->redirect(['action' => 'index']);
		}
		
		if($cp['ClosedPeriod']['reference_id']){
			$fy = $this->FinancialYear->findById($cp['ClosedPeriod']['reference_id']);
			if($fy) {
				$reheated_link="<a class='limit-link text-u-l' href='/owner/financial_years/index'> <b> ".  __('the related financial period',true) . "</b></a>";
				$this->flashMessage( sprintf (__t('This closed period is related to the financial year %s : %s. To open or manage it, please open the  %s'), $fy['FinancialYear']['start_date'],$fy['FinancialYear']['end_date'],$reheated_link));
				$this->redirect(['action' => 'index']);
			}
		}
		
		$this->ClosedPeriod->id = $cp['ClosedPeriod']['id'];
		$cp['ClosedPeriod']['active'] = !$cp['ClosedPeriod']['active']; 
		if($this->ClosedPeriod->save($cp))
		{
//			$active_text = $cp['ClosedPeriod']['active']  ? __('Activated',true) : __('Paused',true);
			$this->flashMessage(sprintf (__('The %s  has been saved', true), __('Closed Period', true)), 'Sucmessage');
			$this->redirect(['action' => 'index']);
		}
		
	}

	function owner_add() {
		
		if (!check_permission(MANAGE_CLOSED_PERIODS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!empty($this->data)) {
			$this->ClosedPeriod->create();
			
			if ($this->ClosedPeriod->save($this->data)) {
				$this->data['ClosedPeriod']['id'] = $this->ClosedPeriod->id;
				$this->action_line(ACTION_CLOSED_PERIOD_ADD,$this->data);
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('Closed Period', true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));

			} else {
			}
		}
	}

	function owner_edit($id = null) {
		
		if (!check_permission(MANAGE_CLOSED_PERIODS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'closed period',true)));
			$this->redirect(array('action'=>'index'));		}
		$closedPeriod = $this->ClosedPeriod->read(null, $id);
		if (!empty($this->data)) {
			if ($this->ClosedPeriod->save($this->data)) {
				$this->action_line(ACTION_CLOSED_PERIOD_EDIT,$this->data);
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('closed period',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
			}
		}
		if (empty($this->data)) {
			$closedPeriod['ClosedPeriod']['from'] = format_date($closedPeriod['ClosedPeriod']['from']); 
			$closedPeriod['ClosedPeriod']['to'] = format_date($closedPeriod['ClosedPeriod']['to']); 
			$this->data = $closedPeriod;
		}
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (!check_permission(MANAGE_CLOSED_PERIODS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('closed period',true)));
		}
		$module_name= __('closedPeriod', true);
		if(is_array($id) && count($id) > 1){
			$module_name= __('closedPeriods', true);
		 } 
		$closedPeriods = $this->ClosedPeriod->find('all',array('conditions'=>array('ClosedPeriod.id'=>$id)));
		if (empty($closedPeriods)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if($closedPeriods[0]['ClosedPeriod']['reference_id'] != 0){
			$this->flashMessage(sprintf(__('You cannot delete the closed period that generated automatically from the closure of the financial period so you can delete %s', true), "<a class='limit-link text-u-l' href='/owner/financial_years/view/{$closedPeriods[0]['ClosedPeriod']['reference_id']}'>".  __('the related financial period first',true) . "</a>"));
			$this->redirect(array('action'=>'index'));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->ClosedPeriod->deleteAll(array('ClosedPeriod.id'=>$_POST['ids']))) {
				foreach($closedPeriods as $k => $closed_period){
					$this->action_line(ACTION_CLOSED_PERIOD_DELETE,$closed_period);
				}
                $this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('closedPeriods',$closedPeriods);
	}
}
?>
