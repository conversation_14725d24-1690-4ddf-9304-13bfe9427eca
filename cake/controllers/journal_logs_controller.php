<?php
class JournalLogsController extends AppController {

	var $name = 'JournalLogs';

	/**
	 * @var Journal
	 */
	var $JournalLog;
	var $helpers = array('Html', 'Form');
	
	
		

		
	

	
	
	function owner_view($journal_id)
	{
		// fatal fix
		if(isset($this->Journal)) $this->Journal->recursive = -1;
		$this->loadModel('JournalLog');
		$journalLogs = $this->JournalLog->find('all',array('conditions' => array('JournalLog.journal_id' => $journal_id)));
		if(empty($journalLogs))
		{
			$this->flashMessage(sprintf(__('%s not found', true), __($module_name,true)));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		$this->set('journal_logs',$journalLogs);
		$this->loadModel('Staff');
		$this->loadModel('JournalAccount');
		$staffs = $this->Staff->getList();
		$accounts = $this->JournalAccount->find('list');
		
		if(isset($_GET['box']))
		{
			$this->layout = false;
			$this->set('isAjax',true);
		}
		
		$this->set('accounts',$accounts);
		$this->set('staffs',$staffs);
		$this->set('title_for_layout',  __('Journal Log',true).' #'.$journal_id );
	}

    function owner_index() {
set_time_limit(3600);
ini_set('memory_limit','2G');
        $conditions = $this->_filter_params();
                $owner = getAuthOwner();
                if ($owner['staff_id'] != 0) {
                        $staff = $owner['staff_id'];

                        if (!check_permission(VIEW_ALL_JOURNALS) && !check_permission(VIEW_OWN_JOURNALS)) {
                                if (IS_REST) $this->cakeError('error403');
                                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                                $this->redirect('/');
                        }

                        if (check_permission(VIEW_OWN_JOURNALS) && !check_permission(VIEW_ALL_JOURNALS)) {
                                $conditions['Journal.staff_id'] = $staff;
                        }
                }
//		debug($conditions);
        if(!empty($_GET['journal_account_id']))
        {
            debug($_GET['journal_account_id']);
            $journal_account_id = mysqli_real_escape_string( $this->JournalLog->getDataSource()->connection,$_GET['journal_account_id']);
            $conditions[] = 'JournalLog.id in(SELECT journal_log_id from journal_transaction_logs where journal_transaction_logs.journal_account_id = '.$journal_account_id.' )';
            unset($conditions['JournalTransactionLog.journal_account_id']);

        }

        if(!empty($_GET['action']))
        {
            $action = mysqli_real_escape_string( $this->JournalLog->getDataSource()->connection,$_GET['action']);
            $conditions['JournalLog.action'] = $action;
        }

//		die(debug($conditions));
//		$this->Journal->recursive = -1 ;



        $this->paginate =
            array(
//								'group' => 'JournalLog.journal_id',
            'conditions'=>$conditions	,
            'order' => array('JournalLog.created' => 'DESC'),
//								'contain' => 'JournalTransaction'
        );
//        $this->JournalLog->recursive = -1;
        $this->loadModel('JournalTransactionLog');
        $this->JournalTransactionLog->recursive = -1;
        $journal_logs = $this->paginate('JournalLog',$conditions);
        
//        foreach($journal_logs as $k => &$journal_log){
//            $temp = $this->JournalTransactionLog->find('all',array('conditions' => array('JournalTransactionLog.journal_log_id' => $journal_log['JournalLog']['id'])));
//            foreach($temp as $k2 => $v){
//                $journal_log['JournalTransactionLog'][] = $v['JournalTransactionLog'];
//            }
//        }

        $this->setup_nav_data($journal_logs);
        $journal_logs = array_merge(array('beg' => array('beg' => true)),$journal_logs,array('end' => array('end' => true)));
        $this->set('journal_logs',$journal_logs);
        $this->loadModel('JournalAccount');
        $this->loadModel('Staff');
        $accounts = $this->JournalAccount->find('list');
        $staffs = $this->Staff->getList();
        $this->set('currency', $this->JournalAccount->get_default_currency());
        $this->set('staffs',$staffs);
        $this->set('accounts',$accounts);
        $this->set('title_for_layout',  __('Journal Logs',true) );
    }
}
