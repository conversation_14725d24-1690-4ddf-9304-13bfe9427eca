<?php
/**
 * @@property Seo Seo
 */
class SeoController extends AppController {

	var $name = 'Seo';
	var $helpers = array('Html', 'Form','Fck' , 'Mixed');

	function admin_index()
    {
        $conditions = $this->_filter_params();
        
        
		$this->Seo->recursive = 0;
		$this->set('seos', $this->paginate('Seo',$conditions));
	}

	

	function admin_add() {
       
		if (!empty($this->data)) {
       
			$this->Seo->create();
			if ($this->Seo->save($this->data)) {
				$this->Session->setFlash(__('The Seo has been saved', true));
				$this->redirect(array('action'=>'index'));
			} else {
				$this->Session->setFlash(__('The Seo could not be saved. Please, try again.', true));
			}
		}
	}

	function admin_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->Session->setFlash(__('Invalid Seo', true));
			$this->redirect(array('action'=>'index'));
		}
		
		if (!empty($this->data)) {

			if ($this->Seo->save($this->data)) {
				$this->Session->setFlash(__('The Seo has been saved', true));
				$this->redirect(array('action'=>'index'));
			} else {
				$this->Session->setFlash(__('The Seo could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->Seo->read(null, $id);
		}
                $this->render('admin_add');
        
	}

	function admin_delete($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid id for Seo', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->Seo->del($id)) {
			$this->Session->setFlash(__('Seo deleted', true));
			$this->redirect(array('action'=>'index'));
		}
	}

}
?>