<?php
//App::import('Vendor', 'notification');
/**
 * @property SysEmailsComponent $SysEmails Component to handle sending system emails
 * @property InvoiceLayout $InvoiceLayout
 */

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use App\Helpers\Common\PurchaseOrderAndInvoiceHelper;
use App\Helpers\PurchaseDocumentsS3Helper;
use App\Services\CostCenter\CostCenterAssigner;
use App\Services\LocalEntityForm\AdditionalFormsHandler;
use App\Transformers\ServiceModelDataTransformer;
use App\Utils\TrackStockUtil;
use App\Validators\TrackStock\TrackStockValidator;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\ButtonServiceErrorHandlerDecorator;
use Izam\Daftra\AppManager\Services\Interfaces\ButtonServiceInterface;
use Izam\Daftra\AppManager\Services\LockEntityService;
use Izam\Daftra\AppManager\Utils\AppButtonLocationUtil;
use Izam\Daftra\Common\Queue\EventListenerMapper;
use Izam\Daftra\Common\Queue\EventTypeUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\PurchaseOrder\Services\PurchaseOrderService;
use Izam\Daftra\Common\Utils\PermissionUtil;
use ReportV2\Purchase;
use App\Services\SalesInvoicesReferrals\SalesInvoicesReferral;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use App\Helpers\CurrencyHelper;
use App\Services\CostCenter\CostCenterUpdaterService;
use App\Services\CostCenter\PurchaseInvoiceCostCenterHandler;
use App\Services\JournalAccount\JournalAccountHelper;
use Izam\Attachment\Models\EntityAttachment;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Navigation\Services\PaginationService;
use Izam\Entity\Helper\EntityHasLegacyCustomFields;

App::import('Vendor', 'settings');
App::import('Vendor', 'sites_local');
class PurchaseInvoicesController extends AppController {

    var $name = 'PurchaseOrders';
    var $helpers = ['Fck'];
    var $components = ['Email', 'SysEmails','Cookie', 'StockValidation', 'SupplierValidation', 'Security'];

    /**
     * @var EmailComponent
     */
    var $Email;

    /**
     * @var LmailComponent
     */
    var $Lmail;

    /**
     * ItemsTag
     */
    var $ItemsTag;

    /**
     * @var PurchaseOrder
     */

    var $purchase_js_labels = array(
        'Discount',
        'Paid',
        'Unpaid',
        'Balance Due:',
        'Next Payment',
        'Partial Paid',
        'Supplier Details',
        'Stock Level After',
        'You can add up to 10 documents only',
        'Documents limit',
        'You can add up to 10 reminders only',
        'Reminders limit',
        'PurchaseOrder Template',
        'Please Save the supplier details first by clicking below on Save Supplier button',
        'Shipping',
        'You need to enter deposit the amount first',
        'You can add up to 500 items only'
    );

    function beforeFilter() {

        parent::beforeFilter();
        if (!IS_PC && !empty($this->data['PurchaseOrder']['html_notes']) && strpos($this->data['PurchaseOrder']['html_notes'], '>') == false) {
            $this->data['PurchaseOrder']['html_notes'] = nl2br($this->data['PurchaseOrder']['html_notes']);
        }
    }



    function owner_index($listing = '') {

        if (!IS_REST) {
            if (!\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('purchase_orders')) {
                if (isset($this->params['url']['box']) && $this->params['url']['box']) {
                    if (isset($this->params['url']['id']) && is_array($this->params['url']['id']) && count($this->params['url']['id'])) {
                        foreach ($this->params['url']['id'] as $id) {
                            $query = '&filter[id][in][]=' . $id;
                        }
                    } elseif (isset($this->params['url']['id'])) {
                        $query = '&filter[id]=' . $this->params['url']['id'];
                    }
                    return $this->redirect('/v2/owner/entity/purchase_order/list?iframe=1&hide_actions=1&hide_filters=1' . $query);
                }
                $query = '?';
                if (isset($this->params['url']['supplier_id'])) {
                    $query .= 'filter[supplier_id][in][]=' . $this->params['url']['supplier_id'] . '&';
                }
                if (isset($this->params['url']['payment_status'])) {
                    $query .= 'filter[payment]=' . $this->params['url']['payment_status'] . '&';
                }
                return $this->redirect('/v2/owner/entity/purchase_order/list'. $query);
            }
        }

        $this->_settings();
        $conditions = array();
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(View_All_Purchase_Orders) && !check_permission(View_his_own_created_Purchase_Orders)) {
                if(IS_REST) $this->cakeError('error403');
                if ( check_permission ( Add_New_Purchase_Orders ))
                {
                    $this->redirect('/owner/purchase_invoices/add');
                }else {
                    $this->flashMessage(__('You are not allowed to view this page', TRUE));
                    $this->redirect('/');
                }
            }
        }
        $this->set('is_ajax', false);
        if (!empty($this->params['url']['ajax']) || $this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        $this->set('staff_id', $site['staff_id']);
        $this->set('country_code', $site['country_code']);

        $this->paginate['PurchaseOrder']['order'] = 'PurchaseOrder.date DESC, PurchaseOrder.id DESC';
        $this->set('enable_purchase_manual_status', settings::getValue(InvoicesPlugin, 'enable_purchase_manual_status'));
        $this->loadModel('FollowUpStatus');
        $this->loadModel('Post');
        $purchase_order_statuses = $this->FollowUpStatus->getList(Post::PO_TYPE, true);
        $this->set('purchase_order_statuses', $purchase_order_statuses);
        $purchase_order_status_colors = $this->FollowUpStatus->getList(Post::PO_TYPE, true, array(), 'color');
        $this->set('colors_options', $this->FollowUpStatus->colors);
        $this->set('purchase_order_status_colors', $purchase_order_status_colors);


        $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::PO_TYPE);

        if (check_permission(Edit_General_Settings)) {
            $FollowUpStatus[] = array('name' => false, 'value' => false, 'data-divider' => "true");
          //  $FollowUpStatus[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> Edit Statuses List </span>', 'name' => __('Edit Statuses List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
        }
        $this->set('FollowUpStatuses', $FollowUpStatus);


        if (!empty($listing)) {
            $listing = low($listing);
            unset($this->params['due_from'], $this->params['due_to'], $this->params['payment_status']);
        }



        if (!empty($_GET['keywords'])) {
            $keywords = trim(($_GET['keywords']));

            $conditions = array('OR' => array(
                'PurchaseOrder.supplier_business_name LIKE' => '%' . $keywords . '%',
                'Supplier.supplier_number LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_first_name LIKE' => '%' . $keywords . '\'%',
                'PurchaseOrder.supplier_last_name LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_email LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.no LIKE' => $keywords,
                'CONCAT(supplier_first_name,\' \',supplier_last_name) ' => '%' . $keywords . '%'
            ));
        }




        if ($site['staff_id'] != 0) {
            if (check_permission(View_his_own_created_Purchase_Orders) && !check_permission(View_All_Purchase_Orders) ) {
                $conditions['PurchaseOrder.staff_id'] = $site['staff_id'];
            }
        }
//$conditions['PurchaseOrder.site_id'] = $site['id'];
        $conditions['PurchaseOrder.type'] = 0;

        $conditions = am($conditions, $this->_filter_params());
        if (!empty ( $_GET['work_order_id']))
        {
            $conditions['PurchaseOrder.work_order_id'] = intval ( $_GET['work_order_id']);
            $this->set('workOrderId', intval ( $_GET['work_order_id']));
        }
        if (!empty ( $_GET['source_type']))
        {
            $conditions['PurchaseOrder.source_type'] = intval ( $_GET['source_type']);
        }
        if (!empty ( $_GET['source_id']))
        {
            $conditions['PurchaseOrder.source_id'] = intval ( $_GET['source_id']);
        }

        if (!empty($conditions['PurchaseOrder.item'])) {
            $keywords = $conditions['PurchaseOrder.item'];
            $conditions['OR '] = array(
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
            );
            unset($conditions['PurchaseOrder.item']);
        }

        if (!empty($conditions['PurchaseOrder.custom_field'])) {
            $keywords = $conditions['PurchaseOrder.custom_field'];
            $conditions['OR  '] = array(
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM 	purchase_order_custom_fields	 WHERE value LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM 	purchase_order_custom_fields	 WHERE value LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM 	purchase_order_custom_fields	 WHERE value LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM 	purchase_order_custom_fields	 WHERE value LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  '
            );
            unset($conditions['PurchaseOrder.custom_field']);
        }

        $this->PurchaseOrder->recursive = 0;

        $this->set(compact('listing'));
        if (isset($this->params['url']['payment_status']) and $this->params['url']['payment_status'] == "-1") {
            $conditions['PurchaseOrder.draft'] = 1;
            unset($conditions['PurchaseOrder.payment_status']);
        }
        elseif (isset($this->params['url']['payment_status']) and $this->params['url']['payment_status'] == "4") {
            $conditions[] = '(summary_total-summary_paid) < -0.05';
            unset($conditions['PurchaseOrder.payment_status']);
        } elseif (!empty($this->params['url']['payment_status']) and $this->params['url']['payment_status'] != "-1") {
            //  unset($conditions['PurchaseOrder.payment_status']);
            $conditions['PurchaseOrder.draft <>'] = 1;
        }
        $purchase_orderss = $this->paginate('PurchaseOrder', $conditions);
        if ($purchase_orderss) {
            $this->checkPurchaseInvoicesHasProductOrService($purchase_orderss);
        }
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PurchaseOrder');

        foreach ($purchase_orderss as $i => $purchase_order) {
            $purchase_orderss[$i]['LastAction'] = $this->PurchaseOrder->getLastSupplierAction($purchase_order);
            // warning suppress
            if(array_key_exists('staff_id',$purchase_orderss[$i]['LastAction']['ActionLine']))
                $purchase_orderss[$i]['LastAction']['ActionLine']['staff_name'] = $timeline->getStaffName($purchase_orderss[$i]['LastAction']['ActionLine']['staff_id']);
        }


        $this->set('PurchaseOrderAllPaymentStatus', PurchaseOrder::getPaymentStatuses());
        $this->set('purchase_orders', $purchase_orderss);

        $purchase_order_ids = array();
        foreach($purchase_orderss as $k => $po)
        {
            $purchase_order_ids[] = $po['PurchaseOrder']['id'];
        }
        $this->loadModel('ItemsTag');
        $this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER);
        $tags = $this->ItemsTag->get_items_tags($purchase_order_ids,ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER);
        $this->set('tags',$tags);

        $this->set('totalPurchaseOrders', $this->PurchaseOrder->getTotalPurchaseOrders($conditions));
        $clist = getCurrenciesList();
        $this->set('currencyCodes', getCurrenciesList(array("Currency.code" => $clist)));
        $this->set('staffs', $this->PurchaseOrder->Staff->getList());
        $this->_filterLinks();

        $this->set('content', $this->get_snippet('purchaseorders-owner'));
        $this->set('title_for_layout',  __('Purchase Invoices', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Invoices', true);
        $this->crumbs[0]['url'] = array('action' => 'index');
        $this->setup_nav_data($purchase_orderss);
        if(IS_REST){
            $this->set('rest_items', $purchase_orderss);
            $this->set('rest_model_name', "PurchaseOrder");
            $this->render("index");
        }
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        $this->set('enable_requisitions', $enable_requisitions);
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());
        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->loadModel('Requisition');
        $this->setIndexAppButtons(AppButtonLocationUtil::PURCHASE_INVOICE_INDEX);
    }

    /** duplicated in invoices controller */
    private function setIndexAppButtons(string $entityKey): void
    {
        try {
            /** @var ButtonServiceInterface $service */
            $service = izam_resolve(ButtonServiceErrorHandlerDecorator::class);
            $appsDropdown = $service->getListingButtonsByLocationKey($entityKey);

            $this->set('app_buttons', $appsDropdown->getButtons());
        } Catch (\Throwable $throwable) {
            //we do not want to break invoices, in case anything went wrong with apps
            $this->set('app_buttons', []);
        }
    }

    function owner_refund($listing = '') {
        $this->isRefund = true;
        $this->set('is_refund',true);
        $this->_settings();
        $conditions = array();
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(View_All_Purchase_Orders) && !check_permission(View_his_own_created_Purchase_Orders)) {
                if(IS_REST) $this->cakeError('error403');
                if ( check_permission ( Add_New_Purchase_Orders ))
                {
                    $this->redirect('/owner/purchase_invoices/add');
                }else {
                    $this->flashMessage(__('You are not allowed to view this page', TRUE));
                    $this->redirect('/');
                }
            }
        }
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        $this->set('staff_id', $site['staff_id']);
        $this->set('country_code', $site['country_code']);

        $this->paginate['PurchaseOrder']['order'] = 'PurchaseOrder.date DESC, PurchaseOrder.id DESC';
        if (!empty($listing)) {
            $listing = low($listing);
            unset($this->params['due_from'], $this->params['due_to'], $this->params['payment_status']);
        }
        if (!empty($_GET['keywords'])) {
            $keywords = trim(($_GET['keywords']));

            $conditions = array('OR' => array(
                'PurchaseOrder.supplier_business_name LIKE' => '%' . $keywords . '%',
                'Supplier.supplier_number LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_first_name LIKE' => '%' . $keywords . '\'%',
                'PurchaseOrder.supplier_last_name LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_email LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.no LIKE' => $keywords,
                'CONCAT(supplier_first_name,\' \',supplier_last_name) ' => '%' . $keywords . '%'
            ));
        }


        if ($site['staff_id'] != 0) {
            if (check_permission(View_his_own_created_Purchase_Orders) && !check_permission(View_All_Purchase_Orders) ) {
                $conditions['PurchaseOrder.staff_id'] = $site['staff_id'];
            }
        }
//$conditions['PurchaseOrder.site_id'] = $site['id'];
        $conditions['PurchaseOrder.type'] = PurchaseOrder::Purchase_Refund;

        $conditions = am($conditions, $this->_filter_params());
        if (!empty ( $_GET['work_order_id']))
        {
            $conditions['PurchaseOrder.work_order_id'] = intval ( $_GET['work_order_id']);
        }

        if (!empty($conditions['PurchaseOrder.item'])) {
            $keywords = $conditions['PurchaseOrder.item'];
            $conditions['OR '] = array(
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE item LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE description LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_3 LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_4 LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'% ' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'' . mysql_escape_string($keywords) . ' %\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'' . mysql_escape_string($keywords) . '\' )  ',
                'PurchaseOrder.id IN  (SELECT purchase_order_id FROM purchase_order_items WHERE col_5 LIKE \'% ' . mysql_escape_string($keywords) . ' %\' )  ',
            );
            unset($conditions['PurchaseOrder.item']);
        }
        $this->PurchaseOrder->recursive = 0;

        $this->set(compact('listing'));
        $purchase_orderss = $this->paginate('PurchaseOrder', $conditions);
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PurchaseOrder');

        foreach ($purchase_orderss as $i => $purchase_order) {
            $purchase_orderss[$i]['LastAction'] = $this->PurchaseOrder->getLastPrAction($purchase_order);

            $purchase_orderss[$i]['LastAction']['ActionLine']['staff_name'] = $timeline->getStaffName($purchase_orderss[$i]['LastAction']['ActionLine']['staff_id']);
        }


        $this->set('PurchaseOrderAllPaymentStatus', PurchaseOrder::getPaymentStatuses());
        $this->set('purchase_orders', $purchase_orderss);

        $this->set('totalPurchaseOrders', $this->PurchaseOrder->getTotalPurchaseOrders($conditions));
        $clist = $this->PurchaseOrder->getCurrencylist();
        $this->loadModel('Currency');
        $this->set('currencyCodes', $this->Currency->getCurrencyList(array("Currency.code" => $clist)));
        $this->set('staffs', $this->PurchaseOrder->Staff->getList());
        $this->_filterLinks();

        $this->set('content', $this->get_snippet('purchaseorders-owner'));
        $this->set('title_for_layout',  __('Purchase Invoice Refunds', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Refunds', true);
        $this->crumbs[0]['link'] = array('action' => 'index');
        $this->set('crumbs', $this->crumbs);    
        if(IS_REST){
            foreach ($purchase_orderss as $key => $value) {
                $purchase_orderss[$key]["PurchaseRefund"] = $purchase_orderss[$key]["PurchaseOrder"];
                unset($purchase_orderss[$key]["PurchaseOrder"]);
            }
            $this->set('rest_items', $purchase_orderss);
            $this->set('rest_model_name', "PurchaseRefund");
            $this->render("index");
        }
        $this->setIndexAppButtons(AppButtonLocationUtil::PURCHASE_REFUND_INDEX);
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        $this->set('enable_requisitions', $enable_requisitions);
        $this->loadModel('Requisition');
    }

    function _filterLinks() {
        $params = $this->params['url'];
        unset($params['url'], $params['ext']);

        $filterLinks = array();
        $action = str_replace('owner_', '', $this->action);
        if ($action != 'index') {
            $listing = low($action);
            if ($listing == 'overdue') {
                $filterLinks[] = array('title' => __('Listing', true), 'value' => __('Overdue', true), 'var' => '');
                $isListing = true;
            } elseif ($listing == 'due') {
                $filterLinks[] = array('title' => __('Listing', true), 'value' => __('Due', true), 'var' => '');
                $isListing = true;
            }
        }

        if (!empty($this->params['url']['supplier_id'])) {
            $supplier = $this->PurchaseOrder->Supplier->find(array('Supplier.id' => $this->params['url']['supplier_id']), false, false, -1);
            if ($supplier) {
                $filterLinks[] = array('title' => __('Supplier', true), 'value' => $supplier['Supplier']['business_name'], 'var' => 'supplier_id');
            }
        }



        $this->set(compact('filterLinks', 'params'));
    }

//-----------------------------------------
//-----------------------------------------
    function owner_add($id = null) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->loadModel('ItemsTag');
        $this->loadModel('SitePaymentGateway');
        App::import('Vendor', 'AutoNumber');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);
        if (!check_permission(Add_New_Purchase_Orders)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/purchase_order/list');
        }

        $this->set('po_always_paid',  $po_always_paid= settings::getValue(InventoryPlugin, 'po_always_paid'));
        $this->set('is_received',  $is_received= settings::getValue(InventoryPlugin, 'po_received'));

        $this->loadCostCentersListViewVars();

        if(isset($this->params['url']['supplier_id']) && $this->params['url']['supplier_id']!=""){

            $check_client=$this->PurchaseOrder->Supplier->find('first',array('recursive' => -1,'conditions'=>array('Supplier.id'=>$this->params['url']['supplier_id'])));

            if($check_client['Supplier']['suspend']=="1"){

                if(IS_REST) $this->cakeError("error400", ["message"=>'Supplier is suspend']);
                $this->flashMessage(__('Supplier is suspend', true));
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $this->SupplierValidation->validateSupplierSuspended($this->data['Supplier']['id'] ?: $this->data['PurchaseOrder']['supplier_id']);
        
        $tmp = false; 
        if(!empty($id)) {
          $tmp = $this->PurchaseOrder->findById($id);
        }
        if (!empty($this->data) || (!empty($id) && $tmp['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);

            foreach ($this->data['PurchaseOrderItem'] as $key => $item) {
                $resultActive = $this->PurchaseOrder->checkProductActive($item);
                if (!$resultActive['status']) {
                    if (IS_REST) {
                        $this->cakeError('error400', ['message' => $resultActive['message']]);
                    } else {
//                        $this->flashMessage($result['message']);
//                        return $this->redirect($this->referer());
                        $this->PurchaseOrder->validationErrors['PurchaseOrderItem'][$key]['item'] = $resultActive['message'];
                    }
                }
            }

            $return = checkOrderItemsStoreIsActive($this->data['PurchaseOrderItem']);
            if (!$return['status']) {
                if (IS_REST) {
                    $this->cakeError('error400', ['message' => $return['message']]);
                } else {
                    //return $this->redirect($this->referer());
                    $this->PurchaseOrder->validationErrors[] = $return['message'];
                }
            }

            if (!empty($id) && $tmp['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER) {
                $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');

                if ($tmp["PurchaseOrder"]["type"] == PurchaseOrder::PURCHASE_ORDER && !$enable_requisitions) {
                    $this->flashMessage(__("You should enable the checkbox that called Enable Requisitions for invoice and purchase invoices from the ", true).
                        '<a href="/owner/settings/inventory">'.__('Inventory Settings', true).'</a>');
                    $this->redirect($this->referer());
                }
                $this->PurchaseOrder->recursive = 1;

                $purchaseOrder = $this->PurchaseOrder->find('first', ['conditions' => [
                    'PurchaseOrder.source_id' => $id, 'PurchaseOrder.type' => PurchaseOrder::PURCHASE_INVOICE, 'PurchaseOrder.source_type' => \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::PURCHASE_ORDER
                ]]);


                if (!empty($purchaseOrder['PurchaseOrder']["id"])) {
                    if(IS_REST) $this->cakeError("error400", ["message"=>__('Could not save purchase invoice', true), ]);
                    $this->flashMessage(__('Could not save purchase invoice', true));
                    $this->redirect($this->referer());
                }

                $this->data['PurchaseOrder'] = $tmp['PurchaseOrder'];
                $this->data['PurchaseOrderItem'] = $tmp['PurchaseOrderItem'];
                $this->data['PurchaseOrderCustomField'] = $tmp['PurchaseOrderCustomField'];
                $originalPurchase = $this->PurchaseOrder->findById($id);
                if ($originalPurchase["PurchaseOrder"]['type'] == PurchaseOrder::PURCHASE_ORDER) {
                    $this->data['PurchaseOrder']['source_type'] = EntityKeyTypesUtil::PURCHASE_ORDER;
                    $this->data['PurchaseOrder']['source_id'] = $id;
                    $this->data['PurchaseOrder']['payment_status'] = 0;
                }
                $this->loadModel("JournalAccountRoute");
                $this->JournalAccountRoute->recursive = 1;
                $journalAccountRoute =  $this->JournalAccountRoute->find("first", ["conditions" => ["transaction_type" => App\Domain\JournalRoute\Util::TRANSACTION_TYPE_PURCHASE_ORDER, "entity_id" => $id]]);
                $this->data['JournalAccountRoute']['entity_type'] = $journalAccountRoute["JournalAccountRoute"]["entity_type"];
                $this->data['JournalAccountRoute']['auto_account_type'] = "fixed";
                $this->data['JournalAccountRoute']['account_type'] = $journalAccountRoute["JournalAccountRoute"]["account_type"];
                $this->data['JournalAccountRoute']['account_id'] = $journalAccountRoute["JournalAccountRoute"]["account_id"];
                $this->data['PurchaseOrder']['id'] = $this->data['PurchaseOrder']['created']= $this->data['PurchaseOrder']['modified'] = null;
                $this->data['ItemsTag']['tags'] = array_values($this->ItemsTag->get_item_tags($id,ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER,true));
                foreach ($this->data['PurchaseOrderItem'] as &$item) {
                    $item['id'] = $item['purchase_order_id'] = $item['created'] = $item['modified'] = null;
                }
                $formats = getDateFormats(null);
                $this->data['PurchaseOrder']['issue_date'] = strftime($formats[getCurrentSite('date_format')], time());
                $this->data['PurchaseOrder']['date'] = strftime($formats[getCurrentSite('date_format')], time());
                $this->data['PurchaseOrder']['date_format'] = getCurrentSite('date_format');
                $this->data['PurchaseOrder']['is_offline'] = 1;
            }
            if (!empty($id) && empty($this->data['PurchaseOrder']['no'])|| (!empty($id) && $tmp['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER)) {
                $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_INVOICE);
            }

            $this->data['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_INVOICE;
            $this->loadModel ( 'Store');
            $this->data['PurchaseOrder']['draft'] = ($this->params['url']['send'] == 'draft');
            $trackingValidationResult = TrackStockValidator::validate($this->data, TrackStockUtil::PURCHASE_ORDER,TrackStockUtil::MODE_ADD);

            $isAppEntitiesValid = $additionalFieldsFormHandler->validate($this->data, isset($id));

            /**
             * Default payment status 
             */
            if($po_always_paid)
                $this->data['Payment']['is_paid'] = $this->data['Payment']['is_paid'] ?? 1;

            /**
             *  Default is_received when flag is set
             *  */    
            if($is_received)
                $this->data['PurchaseOrder']['is_received'] = $this->data['PurchaseOrder']['is_received'] ?? 1;    
            

            if($trackingValidationResult === true && $isAppEntitiesValid)
            {
                $result = $this->PurchaseOrder->addPurchaseOrder($this->data);
            }else{
                $result['status'] = false;
            }
            if ($result['status']) {

                izam_resolve(PurchaseOrderService::class)->insert(ServiceModelDataTransformer::transform($result['data'], 'PurchaseOrder'));
                $this->handleAdjustmentRouting($result);
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $additionalFieldsFormHandler->store($re_read_po['PurchaseOrder']['id'], $this->data);
                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_PURCHASE_ORDER)
                    ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);
                
                $this->addCostCenterItemTransactions($result['data']['PurchaseOrder']['id']);


                if (!settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION) &&!empty($this->data['PurchaseOrder']['cost_center_id'])) {
                    $this->loadModel('Journal');
                    $journal = $this->PurchaseOrder->get_entity_journal($result['data']['PurchaseOrder']['id'], ['entity_type' => 'purchase_order', 'subkey' => 'sales_cost']);

                    if (!empty($journal['Journal']['id']) && !empty($journal['JournalTransaction'])) {
                        $url = $this->get_cost_center_route($journal);
                        preg_match('#/add_cost_center_v2/(\d+)/(\d+)/(\d+)#', $url, $matches);
                        list(, $journal_id, $journal_transaction_id, $journal_account_id) = $matches;

                        $purchase_journal_transaction = $journal['JournalTransaction']['sales_cost'];
                        $journal_transaction_id = $purchase_journal_transaction['id'];


                        $this->data['CostCenterTransaction'] = [
                            [
                                'journal_account_id' => $journal_transaction_id,
                                'cost_center_id' => $this->data['PurchaseOrder']['cost_center_id'],
                                'percentage' => 100,
                                'debit' => $purchase_journal_transaction['debit'],
                                'currency_debit' => $purchase_journal_transaction['currency_debit'],
                                'credit' => $purchase_journal_transaction['credit'],
                                'currency_credit' => $purchase_journal_transaction['currency_credit'],
                            ]
                        ];

                        $assigner = new \App\Services\CostCenter\CostCenterAssigner();
                        $response = $assigner->assignCostCentersToJournalTransaction(
                            $journal_id,
                            $journal_transaction_id,
                            $journal_account_id,
                            $this->data['CostCenterTransaction'],
                        );

                        if (!$response['result'] && !empty($response['message'])) {
                            $this->flashMessage($response['message']);
                        }
                    }
                }

                if (IS_REST && !empty($this->data['Payment']) && empty($this->data['Payment']['is_paid'])) {
                    $this->add_api_payments($this->data, $result['data']['PurchaseOrder']['id'], PurchaseOrder::PURCHASE_INVOICE);
                    unset($this->data['Payment']);
                }

                // ignore anything related to payment if saved as draft
                if (
                    (!empty($this->data['Payment']['is_paid']) || !empty($this->data['Deposit']['is_paid'])) && 
                    !$this->data['PurchaseOrder']['draft']
                ) {
                    if (!empty($this->data['Payment']['is_paid'])) {
                        $k = 'Payment';
                        $amount = $result['data']['PurchaseOrder']['summary_total'];
                    } else {
                        $k = 'Deposit';
                        if ($this->data['PurchaseOrder']['deposit_type'] == 1) {
                            $amount = $this->data['PurchaseOrder']['deposit'];
                        } else {
                            $amount =(float) $re_read_po['PurchaseOrder']['summary_total'] * (float) $this->data['PurchaseOrder']['deposit'] / 100;
                           
                        }
                    }
                    $treasury_id = $this->data['Payment']['treasury_id'];
                    if($k == 'Deposit' && !empty($this->data['Deposit']['is_paid']) && !empty($this->data['Deposit']['payment_method'])) {
                        if (empty(   $this->data['Deposit']['treasury_id'])) {
                           $selectedPaymentMethod = $this->SitePaymentGateway->find('first', ['conditions' => ['SitePaymentGateway.payment_gateway' => $this->data['Deposit']['payment_method'] ]]);
                           if($selectedPaymentMethod && $selectedPaymentMethod['SitePaymentGateway']['treasury_id']) {
                               $is_admin = getAuthOwner('staff_id') == 0 ? true : (bool)getAuthStaff()['Role']['is_super_admin'];
                               if(!$is_admin) {
                                   $this->loadModel('ItemPermission');
                                   $hisTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];
                                   if(isset($hisTreasuries[$selectedPaymentMethod['SitePaymentGateway']['treasury_id']])  ){
                                       $treasury_id = $selectedPaymentMethod['SitePaymentGateway']['treasury_id'];
                                   }
                               }
                               else{
                                   $treasury_id = $selectedPaymentMethod['SitePaymentGateway']['treasury_id'];
                               }
   
                           }
                       }else {
                         $treasury_id = $this->data['Deposit']['treasury_id'];
                       }
                    }
                    $owner = getAuthOwner();
                    $payment['PurchaseOrderPayment'] = array('treasury_id' => $treasury_id,'purchase_order_id' => $re_read_po['PurchaseOrder']['id'], 'amount' => $amount, 'status' => 1, 'date' => $re_read_po['PurchaseOrder']['date'], 'payment_method' => $this->data[$k]['payment_method'], 'transaction_id' => $this->data[$k]['transaction_id'], 'added_by' => 1, 'staff_id' => $owner['staff_id']);
                    $this->PurchaseOrder->ownerAddPayment($payment);

                }else{
                    $this->PurchaseOrder->Supplier->adjust_balance($re_read_po['PurchaseOrder']['supplier_id'], $re_read_po['PurchaseOrder']['currency_code']);
                    $this->PurchaseOrder->Supplier->pay_pos_from_credit($re_read_po['PurchaseOrder']['supplier_id']);
                }
                if ($id) {
                    $this->PurchaseOrder->create();
                    $this->PurchaseOrder->id = $id;
                    $originalPurchase = $this->PurchaseOrder->findById($id);
                    if (!empty($originalPurchase) && $originalPurchase['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER) {
                        $this->PurchaseOrder->saveField('payment_status', \Izam\Daftra\Common\Utils\PurchaseOrderStatusUtil::Invoiced);
                        $this->PurchaseOrder->create();
                    }

                }
                $this->PurchaseOrder->recursive = 1;
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number'],'param7' => $re_read_po['PurchaseOrder']['payment_status']);
                $this->add_actionline(ACTION_ADD_PO, $arry);
                $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
                if ( $enable_requisitions || !empty($this->data['PurchaseOrder']['requisition_ids'])) {
                    $get_pending = false ;$get_cancelled = true ;
                    $this->loadModel('Requisition');
                    if ( !empty($this->data['PurchaseOrder']['requisition_ids']))
                    {
                        $this->loadModel('StockTransaction');
                        foreach ($this->data['PurchaseOrder']['requisition_ids'] as $rid )
                        {
                            $requisition = $this->Requisition->getRequisition($rid) ;
                            $requisition['Requisition']['order_id'] =  $result['data']['PurchaseOrder']['id'] ;
                            $requisition['Requisition']['order_type'] = Requisition::ORDER_TYPE_PURCHASE_ORDER ;
                            $requisition['Requisition']['supplier_id'] = $result['data']['PurchaseOrder']['supplier_id'];
                            $this->Requisition->updateItemsPrices($requisition['RequisitionItem'], $result['data']['PurchaseOrderItem']);
                            $this->Requisition->updateRequisition($requisition) ;
                            $this->StockTransaction->deleteConvertedRequisitionOldStockTransactions($requisition, StockTransaction::SOURCE_RQ_MANUAL_INBOUND);
                            $hasTracking=false;
                            foreach ($requisition['RequisitionItem'] as $item) {
                               if (isset($item['Product']['tracking_type']) && $item['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY) {
                                $hasTracking=true;
                                break;
                               }
                            }
                            if (! $hasTracking && count($this->data['PurchaseOrder']['requisition_ids'])==1) { //stop recalculate requisition caz it will put all item to first req and this will duplicate data
                             $this->recalculateConvertedRequisition($rid);
                            }
                        }
                        $get_pending = true ;$get_cancelled = true ;
                    }
                    $this->Requisition->updateForOrder($re_read_po, Requisition::ORDER_TYPE_PURCHASE_ORDER, $get_pending,$get_cancelled);
                }else if (ifPluginActive(InventoryPlugin)) {
                    $this->loadModel('StockTransaction');
                    StockTransaction::updateForPo($result['data']['PurchaseOrder']['id']);
                }
                $this->PurchaseOrder->update_journals($re_read_po);


                if(IS_REST){
                    $this->set('id', $re_read_po['PurchaseOrder']['id']);
                    $this->render('created');
                    return;
                }

                //$this->add_stats(STATS_CREATE_PurcshaseOrder, array($result['data']['PurchaseOrder']['id']));
                $this->flashMessage(__('Purchase Invoice has been saved', true), 'Sucmessage');

                $redirect = array('action' => 'view');

                if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                    $this->_directSendEmail($result['data']);
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                    $redirect = array('action' => 'send_to_supplier');
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                    $redirect = array('action' => 'view', 'print' => 1);
                }



                $redirect[] = $result['data']['PurchaseOrder']['id'];
                if ( !empty ($this->data['PurchaseOrder']['back_to_wo'] ) && !empty ( $this->data['PurchaseOrder']['work_order_id']) )
                {
                    $redirect = Router::url( ['controller' => 'work_orders' , 'action' => 'view' ,$this->data['PurchaseOrder']['work_order_id'].'#POBlock' ]);
                }

                if (!empty($this->data['PurchaseOrder']['back_to_wf'])) {
                    $redirect = Router::url($this->data['PurchaseOrder']['back_to_wf'] . '#POBlock');
                }

                if($enable_requisitions){
                    $product_ids = [];
                    $this->loadModel('Product');
                    foreach($this->data['PurchaseOrderItem'] as $purchaseOrderItem){
                        $is_tracked = $this->Product->find('first', ['conditions' => ['Product.id' => $purchaseOrderItem['product_id']]])['Product']['track_stock'];
                        if($is_tracked)
                            $product_ids[] = $purchaseOrderItem['product_id'];
                    }
                    $requisition_saved = $this->Requisition->find('first', ['conditions' => ['Requisition.order_id' => $result['data']['PurchaseOrder']['id'], 'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_ORDER, Requisition::ORDER_TYPE_PURCHASE_REFUND]]]);
                    $this->flashMessage(__('Purchase Invoice has been saved', true), 'Sucmessage');
                    if(!$requisition_saved && !empty($product_ids) && !$this->data['PurchaseOrder']['draft']) $this->flashMessage('Failed to save Requisition (Auto Number already Exists)', 'Errormessage', 'secondaryMessage');
                } else {
                    $this->flashMessage(__('Purchase Invoice has been saved', true), 'Sucmessage');
                }
                $this->redirect($redirect);
            } else {
                $this->logExceptionsForSpecificSite(3096279,$this->data,$this->PurchaseOrder->validationErrors);
                // $this->add_stats(STATS_INVOICE_VALIDATION_ERROR, array(serialize($this->PurchaseOrder->validationErrors), serialize($this->PurchaseOrder->data)));
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);
                $this->flashMessage(__('Could not save purchase invoice', true));
                foreach($this->PurchaseOrder->validationErrors as $error){
                    if(is_array($error))
                    {
                        //because if error is array that means its an invoice item error
                        //and invoice item errors will show in each of its rows as
                        //it dosent make sense to show them in the head of page area
                        continue ;
                    }
                    $multi_errors[]=$error;
                }
                if(!empty($multi_errors)) {

                    CustomValidationFlash($multi_errors);
                }
                if (empty($_SERVER['HTTP_REFERER']) || strpos($_SERVER['HTTP_REFERER'], Router::url( ['controller' => 'purchase_invoices' , 'action' => 'add'])) === false  ) {
                    $this->redirect($this->referer());
                }
            }
        } else {
            $formats = getDateFormats('std');
            $this->data['PurchaseOrder']['received_date'] = format_date(date("Y-m-d")) . date(' H:i');
            $this->data['PurchaseOrder']['date'] = format_date(date("Y-m-d"));
            // $this->add_stats(STATS_OPEN_INVOICE_CREATION_PAGE, array($id));
            if ($id) {
                $template = $this->PurchaseOrder->getTemplate($id);
                if (!empty($template)) {
                    unset($template['PurchaseOrder']['is_offline']);
                    if (!$this->PurchaseOrder->Supplier->hasAny(array('Supplier.id' => $template['PurchaseOrder']['supplier_id']))) {

                        //unset($template['PurchaseOrder']['supplier_id'], $template['PurchaseOrder']['supplier_business_name'], $template['PurchaseOrder']['supplier_first_name'], $template['PurchaseOrder']['supplier_last_name'], $template['PurchaseOrder']['supplier_city'], $template['PurchaseOrder']['supplier_state'], $template['PurchaseOrder']['supplier_postal_code'], $template['PurchaseOrder']['supplier_country_code'], $template['PurchaseOrder']['supplier_business_name']);
                    }


                    $template['PurchaseOrder']['date'] = $template['PurchaseOrder']['issue_date'] = format_date(date("Y-m-d"));
                    $this->data = $template;
                } else {
                   // $this->flashMessage(__('Prefilled purchase invoice not found', true));
                }
            }
            if (empty($this->data['PurchaseOrder']['currency_code'])) {
                $this->data['PurchaseOrder']['currency_code'] = getAuthOwner('currency_code');
            }
            if (ifPluginActive(WorkflowPlugin)&& !empty ($_GET['work_order_id']) && !empty ($_GET['type_entity_key']) && empty($this->data['PurchaseOrder']['work_order_id'])) {
                $this->data['PurchaseOrder']['work_order_id'] = $_GET['work_order_id'];
            }

            if (empty($this->data['PurchaseOrder']['language_id'])) {
                $this->data['PurchaseOrder']['language_id'] = getAuthOwner('language_code');
            }
            if ( !empty($this->params['url']['supplier_id'])) {
                $this->data['PurchaseOrder']['supplier_id'] = intval($this->params['url']['supplier_id'] );
            }
            if (empty($this->data['PurchaseOrder']['date_format'])) {
                $this->data['PurchaseOrder']['date_format'] = getCurrentSite('date_format');
            }

            if (empty($this->data['PurchaseOrder']['issue_date'])) {
                $formats = getDateFormats(null);
                $this->data['PurchaseOrder']['issue_date'] = strftime($formats[getCurrentSite('date_format')], time());
            }

            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_INVOICE);
            //TODO set default layout
            //$this->data['PurchaseOrder']['invoice_layout_id']=1;
        }
        if (is_array($this->Session->read('purchaseorder_items'))) {
            $this->data['PurchaseOrder']['requisition_ids'] = $this->Session->read('requisition_ids');
            $more = $this->Session->read('purchaseorder_items');
            $this->data['PurchaseOrderItem'] = $more['PurchaseOrderItem'];
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
            $this->data['PurchaseOrder']['supplier_id'] = $more['PurchaseOrder']['supplier_id'];
            $this->data['PurchaseOrder']['from_manual_inbound_requisition'] = $more['PurchaseOrder']['from_manual_inbound_requisition'];
            $this->Session->delete('requisition_ids');
            $this->Session->delete('purchaseorder_items');
        }
        if ($this->Session->read('purchaseorder_type') == 1) {
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
            $this->Session->delete('purchaseorder_type');
        }



        $this->_settings($id);
        $this->_formCommon();


        $this->set('content', $this->get_snippet('create-purchaseorder'));

        $this->set('title_for_layout',  __('New Purchase Invoice', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Invoices', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/purchase_order/list';

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';

        $this->loadModel('ItemPermission');
        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'Treasury');
            $treasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW);
            $payment_treasury = $this->SitePaymentGateway->PaymentTreasury();
            $payment_treasury = array_filter($payment_treasury, fn ($treasury_id) => in_array($treasury_id, array_keys($treasuries)));
    
            $this->set('payment_treasury',$payment_treasury);
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW) ) ;
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        $this->loadModel ( 'Store');

        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) );
        $this->set ( 'primaryStore' , $this->Store->getPrimaryStore (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) ) ;
        $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id')));
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));

        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang('new-purchaseorder'));
        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));
        $this->set('HtmlModel', 'PurchaseOrderDocument');
        $this->set('isPurchaseOrder', true);
        $this->set('is_purchase_invoice', true);

        if($id) {
            $purchaseOrder = $this->PurchaseOrder->findById($id);
            $purchaseOrderItems = $purchaseOrder['PurchaseOrderItem'];
            $this->set('preserved_purchase_invoice', $purchaseOrder);

            $isPurchaseInvoiceItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);
            if(!empty($isPurchaseInvoiceItemAccountEnabled)) {
                $this->setJournalAccountForPurchaseInvoiceItem($purchaseOrderItems);
            }
            
            $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);     
            if($enablePurchaseInvoiceItemCostCenterDistribution) {
                $this->setCostCentersForPurchaseInvoiceItem($purchaseOrderItems);

            }

        }

    }

//-----------------------------------------
//-----------------------------------------
    function owner_add_debit_note($id = null) {
        //use refund function caz no different between to function , only debit note hasn't payment
        //no payment on debit note
        $this->owner_add_refund($id ,PurchaseOrder::DEBIT_NOTE);
    }
    function getFilterConditions()
    {
        $filter_conditions = $this->_filter_params();
        $pag_url_parm=[];

        foreach ($filter_conditions as $key => $condition) {
            if (is_array($condition)) {
                $filter_conditions[$key . ' LIKE'] = '%' . $filter_conditions[$key]["like"] . '%';
                unset($filter_conditions[$key]);
            }
        }

        return $filter_conditions;
    }
    function owner_view_debit_note($id = null) {
        //use refund function caz no different between to function , only debit note hasn't payment
        //no payment on debit note
        $this->owner_view_refund($id , false,PurchaseOrder::DEBIT_NOTE);
        $this->loadModel('Post');
        $this->set('post_count', $this->Post->find('count', array('conditions' => ['item_id' => $id, 'item_type' => Post::PURCHASE_DEBITNOTE_TYPE])));
        if ($this->params['url']['ext'] == 'pdf') {
            //in pdf i want it use the save view for pdf refund
            $this->render('owner_view_refund');
        }
        $this->set('entity_key', EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE);
        $this->set('title_for_layout',  sprintf(__('Debit Note %s', true), '#'.$this->viewVars['purchase_orders']['PurchaseOrder']['no']));
    }
    function owner_edit_debit_note($id = null) {
        //use refund function caz no different between to function , only debit note hasn't payment
        //no payment on debit note
        $this->owner_edit_refund($id ,PurchaseOrder::DEBIT_NOTE);
    }

    function owner_add_refund($id = null,$type = PurchaseOrder::Purchase_Refund) {
        if( ! ifPluginActive(InventoryPlugin)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->isRefund = true;
        $this->set('is_refund',true);
        if ($type == PurchaseOrder::DEBIT_NOTE) {
            $this->set('is_debit_note',true);
        }
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);
        if (!check_permission(Add_New_Purchase_Orders)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            if ($type==PurchaseOrder::DEBIT_NOTE) {
                $this->redirect('/v2/owner/entity/purchase_debit_note/list');
            }
            $this->redirect('/v2/owner/entity/purchase_refund/list');
        }
        
        $subscription = false;
        if(IS_REST) $id = $this->data['PurchaseOrder']['subscription_id'];
        if(!empty($id))
        {
            $subscription = $this->PurchaseOrder->find('first', ["conditions"=>["PurchaseOrder.id"=>$id, "PurchaseOrder.type"=> PurchaseOrder::PURCHASE_INVOICE]]);
            $po = $this->PurchaseOrder->find('count', ["conditions"=>["PurchaseOrder.id"=>$id, "PurchaseOrder.type"=> PurchaseOrder::PURCHASE_INVOICE]]);
            if (empty($id)||!$po) {
                if(IS_REST) $this->cakeError("error400", ["message"=>__('Purchase Invoice not found', true)]);
                $this->flashMessage(__('Purchase Invoice not found', true));
                if ($type = PurchaseOrder::DEBIT_NOTE) {
                    $this->redirect($this->referer('/v2/owner/entity/purchase_debit_note/list', true));
                }
                $this->redirect($this->referer('/v2/owner/entity/purchase_refund/list', true));
            }

            $isPurchaseInvoiceItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);
            if(!empty($isPurchaseInvoiceItemAccountEnabled)) {
                $this->setJournalAccountForPurchaseInvoiceItem($subscription['PurchaseOrderItem']);
                $this->set('preserved_purchase_invoice', $subscription);
            }

            $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);     
            if($enablePurchaseInvoiceItemCostCenterDistribution) {
                $this->setCostCentersForPurchaseInvoiceItem($subscription['PurchaseOrderItem']);
            }
        }
        $store_balance = true;
        $supplier_status= $this->SupplierValidation->validateSupplierSuspended($this->data['Supplier']['id'] ?: $this->data['PurchaseOrder']['supplier_id'],true);
        if (!empty($supplier_status['error'])) {
         $this->PurchaseOrder->validationErrors['supplier_id']=$supplier_status['message'];
        }
        if (!empty($this->data)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);

            if (empty($this->data['PurchaseOrder']['received_date'])) {
              $this->data['PurchaseOrder']['received_date'] = format_date(date("Y-m-d")) . date(' H:i');
            }
            if (empty($this->data['PurchaseOrder']['date'])) {
              $this->data['PurchaseOrder']['date'] = format_date(date("Y-m-d"));
            }
            if(count($this->data['PurchaseOrderItem']) == 1 ) {
               $message=__('You should enter at least one record', true);
                $firstKey = array_key_first($this->data['PurchaseOrderItem']);
                if (empty($this->data['PurchaseOrderItem'][$firstKey]['item'])) {
                    $this->PurchaseOrder->validationErrors['PurchaseOrderItem'][0]['item'] = $message;
                }
                if (empty($this->data['PurchaseOrderItem'][$firstKey]['unit_price'])) {
                    $this->PurchaseOrder->validationErrors['PurchaseOrderItem'][0]['unit_price'] = $message;
                }
                if (empty($this->data['PurchaseOrderItem'][$firstKey]['quantity'])) {
                    $this->PurchaseOrder->validationErrors['PurchaseOrderItem'][0]['quantity'] = $message;
                }
            }

            $this->validate_open_day($this->PurchaseOrder->formatDate($this->data['PurchaseOrder']['date']));
            $this->loadModel ( 'Store');
            $this->data['PurchaseOrder']['draft'] = ($this->params['url']['send'] == 'draft');
            $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
            $draft = !empty($this->data['PurchaseOrder']['draft']);
            if (!$draft && !$enable_requisitions) {
                // Since This is a Refund Of a Purchase Order It's always Received
                $this->data['PurchaseOrder']['is_received'] = '1';
            }
            if(
                ifPluginActive(InventoryPlugin) &&
                !$this->data['PurchaseOrder']['draft'] &&
                !settings::getValue(InventoryPlugin, 'enable_requisitions_po') &&
                settings::getValue(InventoryPlugin, 'disable_overdraft')
            ) {
                $newTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($this->data['PurchaseOrder']['store_id'], $this->data['PurchaseOrderItem']);
                $StockTransaction = GetObjectOrLoadModel("StockTransaction");
                foreach ($newTransactionsProductStoreQuantities as $productId => $productStoreQuantities) {
                    foreach ($productStoreQuantities as $storeId => $quantity) {
                        if(!$StockTransaction->check_balance_open($productId, $storeId, $quantity, 0, 'deduct')) {
                            $this->loadModel('Product');
                            $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $productId]]);
                            $name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                            if (IS_REST) $this->cakeError("error400", ["message" => sprintf(__('Amount not sufficient for %s', true), $name_code)]);
                            $this->flashMessage(sprintf(__('Amount not sufficient for %s', true), $name_code), 'Errormessage', 'secondaryMessage');
                            $store_balance = false;
                        }
                    }
                }
            }
            $this->data['PurchaseOrder']['type']= $type;
            /*foreach($this->data['PurchaseOrderItem'] as $item){
                $this->PurchaseOrder->PurchaseOrderItem->product_total_refund($id,$item['product_id']);
            }*/


            $this->data['PurchaseOrder']['subscription_id']=  $id;
            $trackingValidationResult = TrackStockValidator::validate($this->data, TrackStockUtil::PURCHASE_ORDER,TrackStockUtil::MODE_DEDUCT);

            $isAppEntitiesValid = $additionalFieldsFormHandler->validate($this->data);
            $result['status'] = false;
            if ($store_balance && $trackingValidationResult === true && empty($this->PurchaseOrder->validationErrors ) && $isAppEntitiesValid) {
                $result = $this->PurchaseOrder->addPurchaseOrder($this->data);
            }
            if ($result['status']) {
                if (PurchaseOrder::DEBIT_NOTE == $type){
                    PaginationService::remove(EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE);
                }

                $additionalFieldsFormHandler->store($result['data']['PurchaseOrder']['id'], $this->data);

                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);

                if($type == PurchaseOrder::DEBIT_NOTE) {
                    $saverType = Util::TRANSACTION_TYPE_PURCHASE_DEBIT_NOTE;
                } else {
                    $saverType = Util::TRANSACTION_TYPE_PURCHASE_REFUND;
                }
                $this->handleAdjustmentRouting($result);
                RouteSaverFactory::getSaver($saverType)
                    ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);

                if (IS_REST && !empty($this->data['Payment']) && empty($this->data['Payment']['is_paid'])) {
                    $this->add_api_payments($this->data, $result['data']['PurchaseOrder']['id'], PurchaseOrder::Purchase_Refund);
                    unset($this->data['Payment']);
                }

                if ($type == PurchaseOrder::Purchase_Refund) {
                    $this->PurchaseOrder->updatePurchaseOrderPayments($result['data']['PurchaseOrder']['id']);
                    if (!empty($this->data['Payment']['is_paid'])) {

                        if (!empty($this->data['Payment']['is_paid'])) {
                            $k = 'Payment';
                            $amount = $result['data']['PurchaseOrder']['summary_total'];
                        } else {
                            $k = 'Deposit';
                            $amount = $this->data['PurchaseOrder']['deposit'];
                        }
                        $owner = getAuthOwner();
                        $payment['PurchaseOrderPayment'] = array('treasury_id' => $this->data['Payment']['treasury_id'],'purchase_order_id' => $re_read_po['PurchaseOrder']['id'], 'amount' => $amount*-1, 'status' => 1, 'date' => $re_read_po['PurchaseOrder']['date'], 'payment_method' => $this->data[$k]['payment_method'], 'transaction_id' => $this->data[$k]['transaction_id'], 'added_by' => 1, 'staff_id' => $owner['staff_id']);

                        $resulti = $this->PurchaseOrder->ownerAddPayment($payment);

                    }

                }


                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $re_read_po['PurchaseOrder']['date_original'] =$re_read_po['PurchaseOrder']['date'];
                $re_read_po['PurchaseOrder']['date'] = format_datetime($re_read_po['PurchaseOrder']['date']);



                $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number'],'param7' => $re_read_po['PurchaseOrder']['payment_status']);
                $action_line = ACTION_ADD_PR;
                if ($type == PurchaseOrder::DEBIT_NOTE) {
                    $action_line = ACTION_PURCHASE_ADD_DEBIT_NOTE;
                }
                $this->add_actionline($action_line, $arry);
                $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
                if ( $enable_requisitions ) {
                    $this->loadModel('Requisition');
                    $this->Requisition->updateForOrder($re_read_po, $type == PurchaseOrder::DEBIT_NOTE ? Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE: Requisition::ORDER_TYPE_PURCHASE_REFUND, false);
                }else if (ifPluginActive(InventoryPlugin)) {
                    $this->loadModel('StockTransaction');
                    if($type == PurchaseOrder::DEBIT_NOTE ) {
                        StockTransaction::updateForPdn($result['data']['PurchaseOrder']['id']);
                    } else {
                        StockTransaction::updateForPr($result['data']['PurchaseOrder']['id']);
                    }

                }

                $re_read_po['PurchaseOrder']['date'] =$re_read_po['PurchaseOrder']['date_original'];
                $this->PurchaseOrder->update_journals($re_read_po);
                $this->addCostCenterItemTransactions($result['data']['PurchaseOrder']['id'], $type == PurchaseOrder::DEBIT_NOTE ? 'purchase_debit_note' : 'purchase_refund');
                if ($type == PurchaseOrder::Purchase_Refund) {
                    $this->PurchaseOrder->updatePurchaseOrderPayments($this->data['PurchaseOrder']['subscription_id']);
                }

                if(IS_REST){
                    $this->set('id', $re_read_po['PurchaseOrder']['id']);
                    $this->render('created');
                    return;
                }

                //$this->add_stats(STATS_CREATE_PurchaseOrder, array($result['data']['PurchaseOrder']['id']));
                $success_message=__('Purchase Refund has been saved', true);
                if ($type==PurchaseOrder::DEBIT_NOTE) {
                    $success_message=__('Debit Note Added successfully', true);
                }
                $this->flashMessage($success_message, 'Sucmessage');

                $redirect = array('action' => 'view_refund');
                if ($type==PurchaseOrder::DEBIT_NOTE) {
                    $redirect = array('action' => 'view_debit_note');
                }
                if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                    $this->_directSendEmail($result['data']);
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                    $redirect = array('action' => 'send_to_supplier');
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                    $redirect = array('action' => 'view', 'print' => 1);
                    if ($type==PurchaseOrder::DEBIT_NOTE) {
                        $redirect = array('action' => 'view_debit_note','print' => 1);
                    }
                }



                $redirect[] = $result['data']['PurchaseOrder']['id'];
                if ( !empty ($this->data['PurchaseOrder']['back_to_wo'] ) && !empty ( $this->data['PurchaseOrder']['work_order_id']) )
                {
                    $redirect = Router::url( ['controller' => 'work_orders' , 'action' => 'view' ,$this->data['PurchaseOrder']['work_order_id'].'#POBlock' ]);
                }

                if (!empty($this->data['PurchaseOrder']['back_to_wf'])) {
                    $redirect = Router::url($this->data['PurchaseOrder']['back_to_wf'] . '#POBlock');
                }

                $this->redirect($redirect);
            } else {
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);

                $error_message=__('Could not save purchase refund', true);
                if ($type==PurchaseOrder::DEBIT_NOTE) {
                    $error_message=__('Could not save purchase debit note', true);
                }
                $this->flashMessage($error_message);
                if(!empty($this->PurchaseOrder->validationErrors)) {
                    $errors = get_error_message($this->PurchaseOrder->validationErrors);
                    CustomValidationFlash($errors);
                }
            }
        } else {
            $formats = getDateFormats('std');
            $this->data['PurchaseOrder']['received_date'] = format_date(date("Y-m-d")) . date(' H:i');
            $this->data['PurchaseOrder']['date'] = format_date(date("Y-m-d"));


            // $this->add_stats(STATS_OPEN_INVOICE_CREATION_PAGE, array($id));
            if ($id) {
                $template = $this->PurchaseOrder->getTemplate($id);
                if (!empty($template)) {
                    if (!$this->PurchaseOrder->Supplier->hasAny(array('Supplier.id' => $template['PurchaseOrder']['supplier_id']))) {
                        unset($template['PurchaseOrder']['supplier_id'], $template['PurchaseOrder']['supplier_business_name'], $template['PurchaseOrder']['supplier_first_name'], $template['PurchaseOrder']['supplier_last_name'], $template['PurchaseOrder']['supplier_city'], $template['PurchaseOrder']['supplier_state'], $template['PurchaseOrder']['supplier_postal_code'], $template['PurchaseOrder']['supplier_country_code'], $template['PurchaseOrder']['supplier_business_name']);
                    }


                    $template['PurchaseOrder']['date'] = $template['PurchaseOrder']['issue_date'] = format_date(date("Y-m-d"));
                    $this->data = $template;
                    foreach ($this->data['PurchaseOrderItem'] as &$item) {
                        unset($item['id']);
                    }
                } else {
                    $this->flashMessage(__('Prefilled purchase invoice not found', true));
                }
                unset($this->data['Attachments']);
            }

            if (empty($this->data['PurchaseOrder']['currency_code'])) {
                $this->data['PurchaseOrder']['currency_code'] = getAuthOwner('currency_code');
            }

            if (empty($this->data['PurchaseOrder']['language_id'])) {
                $this->data['PurchaseOrder']['language_id'] = getAuthOwner('language_code');
            }
            if ( !empty($this->params['url']['supplier_id'])) {
                $this->data['PurchaseOrder']['supplier_id'] = intval($this->params['url']['supplier_id'] );
            }
            if (empty($this->data['PurchaseOrder']['date_format'])) {
                $this->data['PurchaseOrder']['date_format'] = getCurrentSite('date_format');
            }

            if (empty($this->data['PurchaseOrder']['issue_date'])) {
                $formats = getDateFormats(null);
                $this->data['PurchaseOrder']['issue_date'] = strftime($formats[getCurrentSite('date_format')], time());
            }

            App::import('Vendor', 'AutoNumber');
            $autoNumberType = $type == PurchaseOrder::DEBIT_NOTE ? \AutoNumber::TYPE_PURCHASE_DEBIT_NOTE : \AutoNumber::TYPE_PURCHASE_REFUND;
            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'] = \AutoNumber::get_auto_serial($autoNumberType);
            $this->data['PurchaseOrder']['purchase_order_id']=$id;
        }
        if (is_array($this->Session->read('purchaseorder_items'))) {
            $more = $this->Session->read('purchaseorder_items');
            $this->data['PurchaseOrderItem'] = $more['PurchaseOrderItem'];
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
            $this->Session->delete('requisition_ids');
            $this->Session->delete('purchaseorder_items');
        }
        if ($this->Session->read('purchaseorder_type') == 1) {
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
            $this->Session->delete('purchaseorder_type');
        }
        $this->_formCommon($id);
        $this->_settings($id);
        $this->set('purchaseorderTemplates', $this->PurchaseOrder->find('list', array('conditions' => array('PurchaseOrder.site_id' => getAuthOwner('id'), 'PurchaseOrder.type' => 1),)));


        $this->set('content', $this->get_snippet('create-purchaseorder'));
        $title_for_layout = $type == PurchaseOrder::DEBIT_NOTE ? __t('New Purchase Debit Note') : __t('New Purchase Refund');
        $this->set('title_for_layout', $title_for_layout);

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Refund', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/purchase_refund/list';

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';
        if ($type == PurchaseOrder::Purchase_Refund) {

            $this->loadModel('SitePaymentGateway');
            if (ifPluginActive(ExpensesPlugin))
            {
                $this->loadModel ( 'ItemPermission');
                $this->loadModel ( 'Treasury');
                $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT) ) ;

                $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
            }
            $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id')));
            $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));
        }

        $this->loadModel ( 'Store');
        $this->loadModel ( 'ItemPermission');
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) );
        $this->set ( 'primaryStore' , $this->Store->getPrimaryStore (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) ) ;

        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang('new-purchaseorder'));
        $this->set('isPurchaseOrder', true);
        $this->set('bnf',Localize::get_business_number_fields());
        $this->set('HtmlModel', 'PurchaseOrderDocument');

        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));
        $this->set('subscription',$subscription);

        $this->set('is_purchase_invoice_refund', true);

        $this->render('owner_add');
    }

    private function getEntityKeyByType(int $type): ?string
    {
        return match ($type) {
            PurchaseOrder::PURCHASE_INVOICE => EntityKeyTypesUtil::PURCHASE_INVOICE,
            PurchaseOrder::Purchase_Refund => EntityKeyTypesUtil::PURCHASE_REFUND,
            PurchaseOrder::DEBIT_NOTE => EntityKeyTypesUtil::PURCHASE_DEBIT_NOTE,
            default => null,
        };
    }

    protected function validateItemIsNotLocked($id, $type, $label, $redirectUrl): void
    {
        try {
            if (!$entityKey = $this->getEntityKeyByType((int)$type)) {
                return;
            }

            /** @var LockEntityService $lockService */
            $lockService = izam_resolve(LockEntityService::class);
            $response = $lockService->isEditable($entityKey, $id, getAuthOwner('id'));

            if (!$response->isSuccess()) {
                $message = sprintf(__t('You cannot edit or delete the %s that already synced to %s'), __t($label), __t($response->getResult()));
                if (IS_REST) {
                    $this->cakeError('error404', ['message' => $message]);
                }
                $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
                $this->redirect($redirectUrl);
            }
        } catch (\Throwable $throwable) {
            // if anything went wrong, do nothing and continue normal cycle
        }
    }

    private function createAdditionalFieldsFormHandlerInstance()
    {
        return new AdditionalFormsHandler(EntityKeyTypesUtil::PURCHASE_INVOICE);
    }

//-----------------------------------------
    function owner_edit($id = null) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);
        $owner = getAuthOwner();
        $this->PurchaseOrder->bindAttachmentRelation('purchase_order');
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id);
        $approvePermsForPurchases = check_permission(PermissionUtil::APPROVE_OR_REJECT_PURCHASE_REQUESTS) && check_permission(PermissionUtil::APPROVE_OR_REJECT_PURCHASE_QUOTATIONS);
        if ($purchase_orders['PurchaseOrder']['source_id'] && $purchase_orders['PurchaseOrder']['source_type'] == EntityKeyTypesUtil::PURCHASE_ORDER && !$approvePermsForPurchases) {
            $po = $this->PurchaseOrder->getPurchaseOrder($purchase_orders['PurchaseOrder']['source_id']);
            if ($po['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER) {
                if(IS_REST) $this->cakeError('error404', array('message' => __('You cannot edit in the purchase Invoice that comes from Purchase Order', true).' #'.$po['PurchaseOrder']['no']));
                $this->flashMessage(__('You cannot edit in the purchase Invoice that comes from Purchase Order', true).' # <a href="/v2/owner/entity/'. EntityKeyTypesUtil::PURCHASE_ORDER.'/'.$po['PurchaseOrder']['id'].'/show">'.$po['PurchaseOrder']['no']."</a>");
                $this->redirect($this->referer());
            }
        }
        $old_draft=$purchase_orders['PurchaseOrder']['draft'];
        if (!$purchase_orders) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Purchase Invoice not found', true)));
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        $this->loadModel('Tax');
        $this->set('changed_tax',$this->Tax->tax_compare($id,'purchase_order'));
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(Edit_Delete_All_Purchase_Orders) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $this->validate_open_day($purchase_orders['PurchaseOrder']['date']);

        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        $requisition_model = GetObjectOrLoadModel('Requisition');
        $requisition_saved = $requisition_model->find('first', ['conditions' => ['Requisition.order_id' => $id, 'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_ORDER, Requisition::ORDER_TYPE_PURCHASE_REFUND]]]);

        /** Check for Expense Distribution **/
        if ($enable_requisitions && $requisition_saved) {
            $requisition_record = $requisition_model->getRequisition($requisition_saved['Requisition']['id']);
            if (isset($requisition_record['ExpenseDistribution'])) {
                $this->flashMessage(__('You cannot edit in the purchase invoice as there\'s an expenses added to the related requisition', true));
                $referer_action = Router::parse($this->referer('/', true))['action'];
                if ($referer_action == 'view')
                    $this->redirect(array('action' => 'view', $id));
                else
                    $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        if($enable_requisitions && $purchase_orders['PurchaseOrder']['requisition_delivery_status'] == Requisition::DELIVERY_STATUS_RECEIVED && $requisition_saved)
        {
            $this->flashMessage(__("You are not allowed to edit this record as it's already received", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/purchase_order/list');
        }
        $this->validateItemIsNotLocked(
            $id, PurchaseOrder::PURCHASE_INVOICE, 'Purchase Invoice', Router::url(['action' => 'owner_view', $id])
        );

        if(IS_REST) $this->data['PurchaseOrder']['id'] = $id;


        if (!empty($this->data)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);

            $this->data['PurchaseOrder']['draft'] = $this->params['url']['send'] == 'draft';
            $this->data['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_INVOICE;

            foreach ($this->data['PurchaseOrderItem'] as $key => $item) {
                $resultActive = $this->PurchaseOrder->checkProductActive($item);
                if (!$resultActive['status']) {
                    if (IS_REST) {
                        $this->cakeError('error400', ['message' => $resultActive['message']]);
                    } else {
//                        $this->flashMessage($result['message']);
//                        return $this->redirect($this->referer());
                        $this->PurchaseOrder->validationErrors['PurchaseOrderItem'][$key]['item'] = $resultActive['message'];
                    }
                }
            }
            $map_products = [] ;
            foreach ( $purchase_orders['PurchaseOrderItem'] as $k  ) {
                $map_products[$k['product_id']] = $k;
            }

            $return = checkOrderItemsStoreIsActive($this->data['PurchaseOrderItem']);
            if (!$return['status']) {
                if (IS_REST) {
                    $this->cakeError('error400', ['message' => $return['message']]);
                } else {
                   // return $this->redirect($this->referer());
                    $this->PurchaseOrder->validationErrors[] = $return['message'];
                }
            }

            $store_balance = true ;
            $this->loadModel('Store');
            $old_is_received =  $purchase_orders['PurchaseOrder']['is_received']; // Will Return 1 or 0
            if (ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, 'disable_overdraft') && (!$enable_requisitions && $old_is_received)) {
                $StockTransaction = GetObjectOrLoadModel('StockTransaction');
                $old_store = $this->Store->find('first', ['recursive' => -1, 'conditions' => ['Store.id' => $purchase_orders['PurchaseOrder']['store_id']]]);
                $new_store = $this->Store->find('first', ['recursive' => -1, 'conditions' => ['Store.id' => $this->data['PurchaseOrder']['store_id']]]);
                if ($old_store['Store']['active'] != 1) {
                    $muti_error[] = sprintf(__('Old store %s is inactive', true), strongStr($old_store['Store']['name']));
                }
                if ($new_store['Store']['active'] != 1) {
                    $muti_error[] = sprintf(__('New store %s is inactive', true), strongStr($new_store['Store']['name']));
                }
                if (!empty($muti_error)) {
                    CustomValidationFlash($muti_error);
                }
                $products_ids = Set::extract('{n}.product_id', $this->data ['PurchaseOrderItem']);
                if($purchase_orders['PurchaseOrder']['is_received'] && !$purchase_orders['PurchaseOrder']['draft']) {
                    $oldTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($purchase_orders['PurchaseOrder']['store_id'], $purchase_orders['PurchaseOrderItem']);
                    $newTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($this->data['PurchaseOrder']['store_id'], $this->data['PurchaseOrderItem']);
                    foreach ($oldTransactionsProductStoreQuantities as $productId => $productStoreQuantities) {
                        foreach ($productStoreQuantities as $storeId => $quantity) {
                            $quantityToCheck = 0;
                            if(!isset($newTransactionsProductStoreQuantities[$productId][$storeId])) {
                                //product removed
                                $quantityToCheck = $oldTransactionsProductStoreQuantities[$productId][$storeId];
                            } else if(
                                isset($newTransactionsProductStoreQuantities[$productId][$storeId]) &&
                                $newTransactionsProductStoreQuantities[$productId][$storeId] <= $oldTransactionsProductStoreQuantities[$productId][$storeId]
                            ) {
                                //reduced quantity
                                $quantityToCheck = $oldTransactionsProductStoreQuantities[$productId][$storeId] - $newTransactionsProductStoreQuantities[$productId][$storeId];
                            }
                            if($quantityToCheck && !$StockTransaction->check_balance_open($productId, $storeId, $quantityToCheck, 0, 'deduct')) {
                                $this->loadModel('Product');
                                $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $productId]]);
                                $name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                                if (IS_REST) $this->cakeError("error400", ["message" => sprintf(__('Amount not sufficient for %s', true), $name_code)]);
                                $this->flashMessage(sprintf(__('Amount not sufficient for %s', true), $name_code), 'Errormessage', 'secondaryMessage');
                                $store_balance = false;
                            }
                        }
                    }
                }
            }
            $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
            $requisition_pass = true ;
            if ( $enable_requisitions && !$this->data['PurchaseOrder']['draft']) {
                $this->loadModel('Requisition') ;
                $requisition_pass = $this->Requisition->compare_requisitions ( $this->data , Requisition::ORDER_TYPE_PURCHASE_ORDER , false, false);
            }
            if ( !$requisition_pass){
                $this->flashMessage(__('Requisitions exceeded what the purchase invoice has', true) , 'Errormessage', 'secondaryMessage');
            }


            if ( $store_balance && $requisition_pass) {
                $trackingValidationResult = TrackStockValidator::validate($this->data, TrackStockUtil::PURCHASE_ORDER,TrackStockUtil::MODE_ADD);
                if($trackingValidationResult === true && $additionalFieldsFormHandler->validate($this->data))
                {
                    $result = $this->PurchaseOrder->updatePurchaseOrder($this->data);
                }else{
                    $result['status'] = false;
                }

                $errors  = $this->PurchaseOrder->validationErrors;
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $re_read_po['PurchaseOrder']['date'] = format_datetime($re_read_po['PurchaseOrder']['date']);
                $this->PurchaseOrder->validationErrors = $errors;
                $new_draft=$re_read_po['PurchaseOrder']['draft'];

                if ($result['status']) {
                    $additionalFieldsFormHandler->update($id, $this->data);
                    izam_resolve(PurchaseOrderService::class)->update(ServiceModelDataTransformer::transform($result['data'],'PurchaseOrder'));
                    $this->handleAdjustmentRouting($result);
                    RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_PURCHASE_ORDER)
                        ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);

                    $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
                    if ($enablePurchaseInvoiceItemCostCenterDistribution) {
                        $this->loadModel('CostCenter');
                        $this->loadModel('CostCenterTransaction');
                        $costCenterService = new PurchaseInvoiceCostCenterHandler($this->PurchaseOrder);
                        $service = new CostCenterUpdaterService(
                            $costCenterService,
                            $this->CostCenter,
                            $this->CostCenterTransaction
                        );
                        $service->handleCostCenters($re_read_po['PurchaseOrder']['id']);
                    }        
                    $this->PurchaseOrder->Supplier->adjust_balance($re_read_po['PurchaseOrder']['supplier_id'], $re_read_po['PurchaseOrder']['currency_code']);
                    $this->PurchaseOrder->Supplier->pay_pos_from_credit($re_read_po['PurchaseOrder']['supplier_id']);
                    $this->PurchaseOrder->updatePurchaseOrderPayments($result['data']['PurchaseOrder']['id']);
                    $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']);
                    $this->add_actionline(ACTION_UPDATE_PO, $arry);
                    $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
                    if ( $enable_requisitions ) {
                        $this->loadModel('Requisition');
                        $this->Requisition->updateForOrder($re_read_po, Requisition::ORDER_TYPE_PURCHASE_ORDER, false,false);
                    }else if (ifPluginActive(InventoryPlugin)) {
                        $this->loadModel('StockTransaction');
                        StockTransaction::updateForPo($id);
                    }
                    $this->PurchaseOrder->update_journals($result);
                    if($purchase_orders['PurchaseOrder']['currency_code']!=$re_read_po['PurchaseOrder']['currency_code']
                        ||strtotime($purchase_orders['PurchaseOrder']['date'])!=strtotime($re_read_po['PurchaseOrder']['date']))
                    {
                        $this->loadModel('PurchaseOrderPayment');
                        $PurchaseOrderPayments = $this->PurchaseOrderPayment->find('all', array('conditions' => array('PurchaseOrderPayment.purchase_order_id' => $id)));
                        foreach($PurchaseOrderPayments  as $payment)
                        {
                            $payment[$this->PurchaseOrderPayment->alias]['currency_code']=$re_read_po['PurchaseOrder']['currency_code'];
                            $this->PurchaseOrderPayment->save($payment);
                        }
                    }

                    if($old_draft!=$new_draft || $purchase_orders['PurchaseOrder']['supplier_id'] !== $this->data['PurchaseOrder']['supplier_id'])
                    {
                        $this->PurchaseOrder->update_payment_journals($id);
                    }

                    if (!settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION) &&!empty($this->data['PurchaseOrder']['cost_center_id'])) {
                        $this->loadModel('Journal');
                        $journal = $this->PurchaseOrder->get_entity_journal($result['data']['PurchaseOrder']['id'], ['entity_type' => 'purchase_order', 'subkey' => 'sales_cost']);

                        if (!empty($journal['Journal']['id']) && !empty($journal['JournalTransaction'])) {
                            $url = $this->get_cost_center_route($journal);
                            preg_match('#/add_cost_center_v2/(\d+)/(\d+)/(\d+)#', $url, $matches);
                            list(, $journal_id, $journal_transaction_id, $journal_account_id) = $matches;

                            $purchase_journal_transaction = $journal['JournalTransaction']['sales_cost'];
                            $journal_transaction_id = $purchase_journal_transaction['id'];


                            $this->data['CostCenterTransaction'] = [
                                [
                                    'journal_account_id' => $journal_transaction_id,
                                    'cost_center_id' => $this->data['PurchaseOrder']['cost_center_id'],
                                    'percentage' => 100,
                                    'debit' => $purchase_journal_transaction['debit'],
                                    'currency_debit' => $purchase_journal_transaction['currency_debit'],
                                    'credit' => $purchase_journal_transaction['credit'],
                                    'currency_credit' => $purchase_journal_transaction['currency_credit'],
                                ]
                            ];

                            $assigner = new \App\Services\CostCenter\CostCenterAssigner();
                            $response = $assigner->assignCostCentersToJournalTransaction(
                                $journal_id,
                                $journal_transaction_id,
                                $journal_account_id,
                                $this->data['CostCenterTransaction'],
                            );

                            if (!$response['result'] && !empty($response['message'])) {
                                $this->flashMessage($response['message']);
                            }
                        }
                    }

                    if(IS_REST){
                        $this->render('success');
                        return;
                    }
                    if($enable_requisitions){
                        $product_ids = [];
                        $this->loadModel('Product');
                        foreach($this->data['PurchaseOrderItem'] as $purchaseOrderItem){
                            $is_tracked = $this->Product->find('first', ['conditions' => ['Product.id' => $purchaseOrderItem['product_id']]])['Product']['track_stock'];
                            if($is_tracked)
                                $product_ids[] = $purchaseOrderItem['product_id'];
                        }
                        $requisition_saved = $this->Requisition->find('first', ['conditions' => ['Requisition.order_id' => $id]]);
                        $this->flashMessage(__('Purchase Invoice has been updated', true), 'Sucmessage');
                        if(!$requisition_saved && !empty($product_ids) && !$this->data['PurchaseOrder']['draft']) $this->flashMessage('Failed to save Requisition (Auto Number already Exists)', 'Errormessage', 'secondaryMessage');
                    } else {
                        $this->flashMessage(__('Purchase Invoice has been updated', true), 'Sucmessage');
                    }
                    $redirect = array('action' => 'view', $id);
                    if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                        $this->_directSendEmail($result['data']);
                        $redirect = array('action' => 'view', $id);
                    } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                        $redirect = array('action' => 'send_to_supplier', $id);
                    } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                        $redirect = array('action' => 'view', $id, 'print' => 1);
                    }
                    $this->redirect($redirect);
                } else {
                    if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);
                    $this->flashMessage(__('Could not update purchase invoice', true));
                    if(!empty($this->PurchaseOrder->validationErrors)) {
                        $errors = get_error_message($this->PurchaseOrder->validationErrors);
                        CustomValidationFlash($errors);
                    }
                    if (isset($result['message'])) {
                        $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
                    }
                }
            }

        } else {
            $this->data = $purchase_orders;
            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'];

            $this->loadCostCentersListViewVars();
            $journal = $this->PurchaseOrder->getJournal($purchase_orders['PurchaseOrder']['id']);
            $this->loadModel('CostCenterTransaction');
            $costTransaction = $this->CostCenterTransaction->find('first', ['conditions' => ['CostCenterTransaction.journal_id' => $journal['Journal']['id']]]);
            $this->data['PurchaseOrder']['cost_center_id'] = $costTransaction['CostCenterTransaction']['cost_center_id'];
            $purchase_orders['PurchaseOrder']['cost_center_id'] = $costTransaction['CostCenterTransaction']['cost_center_id'];

        }
        $this->set('purchase_orders', $purchase_orders);
        $this->_settings($id);
        $this->_formCommon($id);
        $this->loadModel('StockTransaction');
        $pstocks = array();
        $list = $this->StockTransaction->getPOTransactions($id);
        foreach ($list as $p)
            $pstocks[$p['StockTransaction']['product_id']] = $p['StockTransaction']['quantity'];
        $this->set('pstocks', $pstocks);

        $this->set('title_for_layout',  __('Edit Purchase Invoice', true));

        $this->set('isPurchaseOrder', true);
        $this->set('content', $this->get_snippet('create-purchaseorder'));
        $this->set('purchaseorderTemplates', $this->PurchaseOrder->find('list', array('conditions' => array('PurchaseOrder.site_id' => getAuthOwner('id'), 'PurchaseOrder.type' => 1),)));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Invoices', true);
        $this->crumbs[0]['link'] = '/owner/purchase_invoices';

        $this->crumbs[1]['title'] = __('Purchase Invoice', true)." #{$purchase_orders['PurchaseOrder']['no']}";
        $this->crumbs[1]['link'] = '/owner/purchase_invoices/view/'.$purchase_orders['PurchaseOrder']['id'];

        $this->crumbs[2]['title'] = __('Edit', true);
        $this->crumbs[2]['link'] = '#';
        $this->loadModel ( 'Store');
        $this->loadModel('ItemPermission');
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) );
        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang());
        $this->set('HtmlModel', 'PurchaseOrderDocument');

        $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));

        $isPurchaseInvoiceItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);

        if(!empty($isPurchaseInvoiceItemAccountEnabled)) {
          $this->setJournalAccountForPurchaseInvoiceItem($this->data['PurchaseOrderItem']);
        }

        $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);     
        if($enablePurchaseInvoiceItemCostCenterDistribution) {
            $this->setCostCentersForPurchaseInvoiceItem($this->data['PurchaseOrderItem']);
        }

        $this->set('is_purchase_invoice', true);

        $this->render('owner_add');
    }
//-----------------------------------------
    function owner_edit_refund($id = null,$type = PurchaseOrder::Purchase_Refund)
    {
        if( ! ifPluginActive(InventoryPlugin)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->isRefund = true;
        $this->set('is_refund', true);
        if ($type == PurchaseOrder::DEBIT_NOTE) {
            $this->set('is_debit_note',true);
        }
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);

        $owner = getAuthOwner();
        $this->PurchaseOrder->bindAttachmentRelation('purchase_order');
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, ['PurchaseOrder.type' =>$type]);
        $old_po=$purchase_orders;

        if (!$purchase_orders) {
            $message=__('Purchase Invoice not found',true);

            if (IS_REST) $this->cakeError('error404', ['message' => $message]);
            if ($type==PurchaseOrder::DEBIT_NOTE) {
                $this->flashMessage(__t('Debit not found'));
                $this->redirect('/v2/owner/entity/purchase_debit_note/list');
            }
            $this->flashMessage($message);
            $this->redirect($this->referer(['action' => 'index'], true));
        }


        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(Edit_Delete_All_Purchase_Orders) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
                if (IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
                if ($type==PurchaseOrder::DEBIT_NOTE) {
                    $this->redirect('/v2/owner/entity/purchase_debit_note/list');
                }
                $this->redirect('/v2/owner/entity/purchase_refund/list');
            }
        }
        $this->validate_open_day($purchase_orders['PurchaseOrder']['date']);

        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        $requisition_model = GetObjectOrLoadModel('Requisition');
        $requisition_saved = $requisition_model->find('first', ['conditions' => ['Requisition.order_id' => $id, 'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE, Requisition::ORDER_TYPE_PURCHASE_REFUND]]]);
        if ($enable_requisitions && $purchase_orders['PurchaseOrder']['requisition_delivery_status'] == Requisition::DELIVERY_STATUS_RECEIVED && $requisition_saved) {
            $this->flashMessage(__("You are not allowed to edit this record as it's already received", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/purchase_debit_note/list');
        }

        $this->validateItemIsNotLocked(
            $id, PurchaseOrder::Purchase_Refund, 'Purchase Refund', Router::url(['action' => 'view_refund', $id])
        );

        $requisition_pass = true ;
        if ( $enable_requisitions && !$this->data['PurchaseOrder']['draft']) {
            $this->loadModel('Requisition') ;
            $requisition_pass = $this->Requisition->compare_requisitions ( $this->data , Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE , false, false);
        }
        if (!$requisition_pass){
            $this->flashMessage(__('Requisitions exceeded what the purchase invoice has', true) , 'Errormessage', 'secondaryMessage');
        }

        if (IS_REST) $this->data['PurchaseOrder']['id'] = $id;
        if (!empty($this->data)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);

            if (empty($this->data['PurchaseOrder']['type'])) {
                $this->data['PurchaseOrder']['type'] = $type;
            }
            $this->data['PurchaseOrder']['draft'] = $this->params['url']['send'] == 'draft';
            $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
            $draft = !empty($this->data['PurchaseOrder']['draft']);
            if (!$draft && !$enable_requisitions) {
                // Since This is a Refund Of a Purchase Order It's always Received
                $this->data['PurchaseOrder']['is_received'] = '1';

                if (empty($purchase_order['PurchaseOrder']['is_received']) or $purchase_order['PurchaseOrder']['is_received'] != 1) {
                    $this->data['PurchaseOrder']['received_date']= $this->PurchaseOrder->formatDate($this->data['PurchaseOrder']['date']) ?: date('Y-m-d H:i:s');
                }

            }
            $map_products = [];
            foreach ($purchase_orders['PurchaseOrderItem'] as $k) {
                $map_products[$k['product_id']] = $k;
            }
            $store_balance = true;

            if(
                settings::getValue(InventoryPlugin, 'disable_overdraft') &&
                ifPluginActive(InventoryPlugin) &&
                !settings::getValue(InventoryPlugin, 'enable_requisitions_po')
            ) {
                $StockTransaction = GetObjectOrLoadModel('StockTransaction');
                $oldTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($purchase_orders['PurchaseOrder']['store_id'], $purchase_orders['PurchaseOrderItem']);
                $newTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($this->data['PurchaseOrder']['store_id'], $this->data['PurchaseOrderItem']);

                foreach ($newTransactionsProductStoreQuantities as $productId => $productStoreQuantities) {
                    foreach ($productStoreQuantities as $storeId => $quantity) {
                        $quantityToCheck = 0;
                        if(!isset($oldTransactionsProductStoreQuantities[$productId][$storeId])) {
                            //product
                            $quantityToCheck = $newTransactionsProductStoreQuantities[$productId][$storeId];
                        } else if(
                            isset($oldTransactionsProductStoreQuantities[$productId][$storeId]) &&
                            $oldTransactionsProductStoreQuantities[$productId][$storeId] <= $newTransactionsProductStoreQuantities[$productId][$storeId]
                        ) {
                            //reduced increased
                            $quantityToCheck = $newTransactionsProductStoreQuantities[$productId][$storeId] - $oldTransactionsProductStoreQuantities[$productId][$storeId];
                        }
                        if($quantityToCheck && !$StockTransaction->check_balance_open($productId, $storeId, $quantityToCheck, 0, 'deduct')) {
                            $this->loadModel('Product');
                            $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $productId]]);
                            $name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                            if (IS_REST) $this->cakeError("error400", ["message" => sprintf(__('Amount not sufficient for %s', true), $name_code)]);
                            $this->flashMessage(sprintf(__('Amount not sufficient for %s', true), $name_code), 'Errormessage', 'secondaryMessage');
                            $store_balance = false;
                        }
                    }
                }
            }

            if ($store_balance && $requisition_pass) {
                $trackingValidationResult = TrackStockValidator::validate($this->data, TrackStockUtil::PURCHASE_ORDER, TrackStockUtil::MODE_DEDUCT);
                if ($trackingValidationResult === true && $additionalFieldsFormHandler->validate($this->data)) {
                    $result = $this->PurchaseOrder->updatePurchaseOrder($this->data);
                } else {
                    $result['status'] = false;
                }

                if ($result['status']) {

                    $additionalFieldsFormHandler->update($result['data']['PurchaseOrder']['id'], $this->data);
                    //as discussed with elwan
                    $this->PurchaseOrder->PurchaseOrderPayment->alias = 'PurchaseOrderPayment';
                    $re_read_po = $this->PurchaseOrder->getPurchaseOrder($result['data']['PurchaseOrder']['id']);
                    $this->handleAdjustmentRouting($result);
                    RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_PURCHASE_REFUND)
                        ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);
                    $last_payment = $this->PurchaseOrder->PurchaseOrderPayment->find('first', ['conditions' => ['purchase_order_id' => $id]]);
                    $amount = $re_read_po['PurchaseOrder']['summary_total'];
                    if(isset($this->data['Payment']['is_paid']))
                    {
                        if (!empty($this->data['Payment']['is_paid']) || !empty($this->data['Deposit']['is_paid'])) {
                            if ($last_payment) {
                                $this->PurchaseOrder->PurchaseOrderPayment->delete($last_payment['PurchaseOrderPayment']['id']);
                            }
                            $payment['PurchaseOrderPayment'] = ['manual_payment' => 1, 'purchase_order_id' => $id, 'amount' => $amount * -1, 'status' => 1, 'date' => $this->data['PurchaseOrder']['date'], 'payment_method' => $this->data['Payment']['payment_method'], 'transaction_id' => $this->data['Payment']['transaction_id'], 'added_by' => 1, 'staff_id' => $owner['staff_id']];
                            $this->PurchaseOrder->ownerAddPayment($payment);
                        } else {
                            if ($last_payment) {
                                $this->PurchaseOrder->PurchaseOrderPayment->delete($last_payment['PurchaseOrderPayment']['id']);
                            }
                        }
                    }

                    $this->PurchaseOrder->updatePurchaseOrderPayments($id);
                    $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                    $re_read_po['PurchaseOrder']['date'] = format_datetime($re_read_po['PurchaseOrder']['date']);
                    $this->PurchaseOrder->updatePurchaseOrderPayments($re_read_po['PurchaseOrder']['subscription_id']);
                    if(
                        ($old_po['PurchaseOrder']['draft']!=$re_read_po['PurchaseOrder']['draft']|| $purchase_orders['PurchaseOrder']['supplier_id'] !== $this->data['PurchaseOrder']['supplier_id'])
                        ||(strtotime($old_po['PurchaseOrder']['date'])!=strtotime($re_read_po['PurchaseOrder']['date']))
                        ||($old_po['PurchaseOrder']['currency_code']!=$re_read_po['PurchaseOrder']['currency_code'])
                    )
                    {

                        $this->PurchaseOrder->update_payment_journals($id);
                    }
                    $arry = ['primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']];
                    $action_line = ACTION_EDIT_PR;
                    if ($type == PurchaseOrder::DEBIT_NOTE) {
                        $action_line = ACTION_PURCHASE_UPDATE_DEBIT_NOTE;
                    }
                    $this->add_actionline($action_line, $arry);
                    $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
                    if ($enable_requisitions) {
                        $this->loadModel('Requisition');
                        $this->Requisition->updateForOrder($re_read_po, $type == PurchaseOrder::DEBIT_NOTE ? Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE : Requisition::ORDER_TYPE_PURCHASE_REFUND, false, false);
                    } else if (ifPluginActive(InventoryPlugin)) {
                        $this->loadModel('StockTransaction');
                        if($type == PurchaseOrder::DEBIT_NOTE ) {
                            StockTransaction::updateForPdn($result['data']['PurchaseOrder']['id']);
                        } else {
                            StockTransaction::updateForPr($result['data']['PurchaseOrder']['id']);
                        }
                    }
                    $this->PurchaseOrder->update_journals($result);

                    $this->addCostCenterItemTransactions($result['data']['PurchaseOrder']['id'], $type == PurchaseOrder::DEBIT_NOTE ? 'purchase_debit_note' : 'purchase_refund');

                    if ($purchase_orders['PurchaseOrder']['currency_code'] != $re_read_po['PurchaseOrder']['currency_code']) {
                        $this->loadModel('PurchaseOrderPayment');
                        $PurchaseOrderPayments = $this->PurchaseOrderPayment->find('all', ['conditions' => ['PurchaseOrderPayment.purchase_order_id' => $id]]);
                        foreach ($PurchaseOrderPayments as $payment) {
                            $payment[$this->PurchaseOrderPayment->alias]['currency_code'] = $re_read_po['PurchaseOrder']['currency_code'];
                            $this->PurchaseOrderPayment->save($payment);
                        }
                    }
                    if (IS_REST) {
                        $this->render('success');
                        return;
                    }
                    // Start Save Attachments To S3 .
                    $attachmentsId = explode(',',$result['data']['purchaseDocuments_s3']);
                    izam_resolve(AttachmentsService::class)->save(EntityKeyTypesUtil::PURCHASE_INVOICE, $id, $attachmentsId);

                    $success_message=__('Purchase Refund has been updated', true);
                    if ($type==PurchaseOrder::DEBIT_NOTE) {
                        $success_message=__('Purchase Debit Note has been Updated', true);
                    }
                    $this->flashMessage($success_message, 'Sucmessage');


                    $redirect = ['action' => 'view_refund', $id];
                    if ($type==PurchaseOrder::DEBIT_NOTE) {
                        $redirect = array('action' => 'view_debit_note',$id);
                    }

                    if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                        $this->_directSendEmail($result['data']);
                        $redirect = ['action' => 'view_refund', $id];
                        if ($type==PurchaseOrder::DEBIT_NOTE) {
                            $redirect = array('action' => 'view_debit_note',$id);
                        }
                    } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                        $redirect = ['action' => 'send_to_supplier', $id];
                    } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                        $redirect = ['action' => 'view_refund', $id, 'print' => 1];
                        if ($type==PurchaseOrder::DEBIT_NOTE) {
                            $redirect = array('action' => 'view_debit_note',$id,'print' => 1);
                        }
                    }
                    $this->redirect($redirect);
                } else {
                    if (IS_REST) $this->cakeError("error400", ["message" => $result['message'], "validation_errors" => $this->PurchaseOrder->validationErrors]);
                   $error_message=__('Could not update purchase refund', true);
                    if ($type==PurchaseOrder::DEBIT_NOTE) {
                        $error_message=__('Could not update debit note', true);
                    }
                    $this->flashMessage($error_message);

                    if(!empty($this->PurchaseOrder->validationErrors)) {
                        $errors = get_error_message($this->PurchaseOrder->validationErrors);
                        CustomValidationFlash($errors);
                    }
    
                    if (isset($result['message'])) {
                        $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
                    }
                }
            }

        } else {
            $this->data = $purchase_orders;
            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'];
        }

        $this->set('purchase_orders', $purchase_orders);
        $this->_settings($id);
        $this->_formCommon($id);
        $this->loadModel('SitePaymentGateway');
        $this->loadModel('StockTransaction');
        $pstocks = [];
        $list = $this->StockTransaction->getPOTransactions($id);
        foreach ($list as $p)
            $pstocks[$p['StockTransaction']['product_id']] = $p['StockTransaction']['quantity'];
        $this->set('pstocks', $pstocks);
        $title_for_layout = $type == PurchaseOrder::DEBIT_NOTE ? __t('Edit Purchase Debit Note') : __t('Edit Purchase Refund');
        $this->set('title_for_layout', $title_for_layout);

        $this->set('isPurchaseOrder', true);
        $this->set('content', $this->get_snippet('create-purchaseorder'));
        $this->set('purchaseorderTemplates', $this->PurchaseOrder->find('list', ['conditions' => ['PurchaseOrder.site_id' => getAuthOwner('id'), 'PurchaseOrder.type' => 1],]));
        $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id')));
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));
        $this->setPurchaseEditRefundCrumbs($purchase_orders);
        $this->loadModel('Store');
        $this->loadModel('ItemPermission');
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE, ItemPermission::PERMISSION_INVOICING));
        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang());
        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'Treasury');
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT) ) ;
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        $isPurchaseInvoiceItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);
        if(!empty($isPurchaseInvoiceItemAccountEnabled)) {
            $this->setJournalAccountForPurchaseInvoiceItem($this->data['PurchaseOrderItem']);
        }

        $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
        if($enablePurchaseInvoiceItemCostCenterDistribution) {
            $this->setCostCentersForPurchaseInvoiceItem($this->data['PurchaseOrderItem']);
        }

        $this->set('HtmlModel', 'PurchaseOrderDocument');
        $this->set('bnf',Localize::get_business_number_fields());
        $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));
        $this->set('is_purchase_invoice_refund', true);
        $this->render('owner_add');
    }

    function setPurchaseEditRefundCrumbs($purchase_orders){
        $this->crumbs = [];
        $this->crumbs[0]['title'] =  __('Purchase Refunds', true) ;
        $this->crumbs[0]['link'] = '/v2/owner/entity/purchase_refund/list';

        $this->crumbs[1]['title'] = __('Purchase Refunds', true)." #{$purchase_orders['PurchaseOrder']['no']}";
        $this->crumbs[1]['link'] = '/owner/purchase_invoices/view_refund/'.$purchase_orders['PurchaseOrder']['id'];

        if ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::DEBIT_NOTE) {
            $this->crumbs[0]['title'] =  __('Debit Note', true) ;
            $this->crumbs[0]['link'] = '/v2/owner/entity/purchase_debit_note/list';

            $this->crumbs[1]['title'] = __('Debit Note', true)." #{$purchase_orders['PurchaseOrder']['no']}";
            $this->crumbs[1]['link'] = '/owner/purchase_invoices/view_debit_note/'.$purchase_orders['PurchaseOrder']['id'];
        }



        $this->crumbs[2]['title'] = __('Edit', true);
        $this->crumbs[2]['link'] = '#';

        $this->set('crumbs', $this->crumbs);


    }
    function owner_timeline_row($id = null)
    {
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));
        $this->set('data', $timeline->getDataArray());
        echo $timeline->view_action($id);
        die();
    }

    function owner_timeline($id = false) {
        $this->set('purchase_order_id', $id);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        $purchase_orders = $this->PurchaseOrder->find('first',['recursive' => -1,'conditions'=>['PurchaseOrder.id'=>$id]]);
//
//
//        if (!$purchase_orders) {
//            $this->flashMessage(__('PurchaseOrder not found', true));
//            $this->redirect($this->referer(array('action' => 'index'), true));
//        }

        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));

        if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::Purchase_Refund){
            $action_list = $timeline->getPurchaseRefundActionsList();
        }elseif ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::DEBIT_NOTE) {
            $action_list = $timeline->getPurchaseDebitNoteActionsList();
        }
        elseif ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::CREDIT_NOTE) {
            $action_list = $timeline->getPurchaseCreditNoteActionsList();
        }
        else{
            $action_list = $timeline->getPurchaseOrderActionsList();
        }
        $timeline->init(array('primary_id' => $id), $action_list);

        $this->set('data', $timeline->getDataArray());

        $this->loadModel('ActionLine');

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $purchase_orders_actions[$key] = $action;
            }
        }
        $this->set('actions', $purchase_orders_actions);
        $this->set('po_type',$purchase_orders['PurchaseOrder']['type']);
        //$this->set('jsonParams', $timeline->jsonAdapter(), false);
    }

    function owner_actionlist() {
        $this->loadModel('ActionLine');
        $actionlists = $this->ActionKeys;
        foreach ($actionlists as $actionlist) {
            echo $actionlist['class'] . "<br>";
        }
        die();
    }

    function view_order($id = false) {


        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.supplier_id' => getAuth('SUPPLIER', 'id'), ));
        $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id']);
        if (!$purchase_orders) { 
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        $this->add_actionline(ACTION_SUPPLIER_VIEW_PO, array('staff_id' => -4, 'secondary_id' => $purchase_orders['PurchaseOrder']['supplier_id'], 'primary_id' => $purchase_orders['PurchaseOrder']['id'], 'param4' => $purchase_orders['PurchaseOrder']['no']));

//		$this->_setBranded();
        $this->set('purchase_orders', $purchase_orders);
        $emailLogs = $this->PurchaseOrder->EmailLog->find('all', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1, 'limit' => 10, 'order' => 'EmailLog.sent_date DESC'));
        $emailCount = $this->PurchaseOrder->EmailLog->find('count', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1));

        $this->set(compact('emailLogs', 'emailCount'));

        if($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE){
            $module_name = 'Purchase Debit Note';
        }elseif($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund){
            $module_name = 'Purchase Refund';
        }elseif($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE){
            $module_name = 'Purchase Credit Note';
        }else{
            $module_name = 'Purchase Invoice';
        }
        $this->set('module_name', $module_name);

        $this->set('title_for_layout',  sprintf(__("$module_name #%s", true), $purchase_orders['PurchaseOrder']['no']));

        if ($this->params['url']['ext'] == 'pdf') {
            $this->_setBranded();
        }

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->find('list'));



        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Invoices', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/purchase_order/list';

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';
        $this->layout = "supplier";

        $invoicesReferral = new SalesInvoicesReferral;
        $this->set('sip_hash', $invoicesReferral->getSIPHash(
            invoiceId: $purchase_orders['PurchaseOrder']['id'],
            siteId: $purchase_orders['PurchaseOrder']['site_id'],
            model: 'PurchaseOrder',
        ));
    }

    function owner_status($id = null) {

        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => 0));
        $this->set('purchase_orders', $purchase_orders);
        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());
        $this->set('PurchaseOrderAllPaymentStatus', PurchaseOrder::getPaymentStatuses());
        $RequisitionModel = GetObjectOrLoadModel('Requisition');
        $requisition_exists = $RequisitionModel->find('first',
            [
                'conditions' => [
                    'Requisition.order_id' => $id,
                    'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_REFUND, Requisition::ORDER_TYPE_PURCHASE_ORDER]
                ],
                'applyBranchFind' => false
            ]
        );
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po') || $requisition_exists;
        if (!empty($purchase_orders['PurchaseOrder']['draft']) || empty($enable_requisitions) && empty($purchase_orders['PurchaseOrder']['requisition_delivery_status'])) {
            $this->render('../elements/purchase_order-status');
        } elseif (empty($purchase_orders['PurchaseOrder']['draft'])) {
            $this->render('../elements/purchase_order-payment_status');
        }
    }


    private function handleAdjustmentRouting($result){

        $this->loadModel('Journal') ;
        $adjustment_routing = settings::getValue(AccountingPlugin , "purchases_adjustment_accounts_routing");

        //This to set a default account (Discount allowed for adjustment in case no account is there)
        //This is a custom scenario for routing
        $defaultAdjustmentAccount =  $this->Journal->get_auto_account(['entity_type' => 'discount_received', 'entity_id' => 0]);
        if(!$defaultAdjustmentAccount) {
            $defaultAdjustmentAccount['JournalAccount']['id'] = $this->Journal->create_auto_account(['entity_type' => 'discount_received', 'entity_id' => 0]);
        }

        if($adjustment_routing != settings::MANUAL_ACCOUNTS_ROUTING) {
            unset($this->data['JournalAccountAdjustmentRoute']);
        }

        if(!is_null($this->data['JournalAccountAdjustmentRoute']) && (!isset($this->data['JournalAccountAdjustmentRoute']['account_id']) || $this->data['JournalAccountAdjustmentRoute']['account_id'] == "")) {
          $this->loadModel('Journal');
          $this->data['JournalAccountAdjustmentRoute']['account_id'] = $defaultAdjustmentAccount['JournalAccount']['id'];
        }

        $adjustmentAccount =  $this->Journal->get_auto_account(['entity_type' => 'purchases_adjustment', 'entity_id' => 0]);

        if($adjustment_routing == settings::AUTOMATIC_ACCOUNTS_ROUTING && $adjustmentAccount && $adjustmentAccount['JournalAccount']['entity_type'] == "purchases_adjustment") {
            $this->data['JournalAccountAdjustmentRoute']['entity_type'] = 'purchases_adjustment';
            $this->data['JournalAccountAdjustmentRoute']['account_type'] = 1;
            $this->data['JournalAccountAdjustmentRoute']['account_id'] = $defaultAdjustmentAccount['JournalAccount']['id'];
        } else if($adjustment_routing == settings::MANUAL_ACCOUNTS_ROUTING && !isset($this->data['JournalAccountAdjustmentRoute'])) {
            $this->data['JournalAccountAdjustmentRoute']['entity_type'] = 'purchases_adjustment';
            $this->data['JournalAccountAdjustmentRoute']['account_type'] = 1;
            $this->data['JournalAccountAdjustmentRoute']['account_id'] = $defaultAdjustmentAccount['JournalAccount']['id'];
        }

        if(!is_null($this->data['JournalAccountAdjustmentRoute'])) {
            RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_INVOICE)
            ->save($this->data['JournalAccountAdjustmentRoute'], $result['data']['PurchaseOrder']['id']);
        }

    }


    function owner_change_status($id, $status_id) {
        $this->loadModel('FollowUpStatus');
        $this->loadModel('Post');
        $purchase_order = $this->PurchaseOrder->find(array('PurchaseOrder.id' => $id));
        if (!$purchase_order) {
            $this->flashMessage(__('Purchase invoice not found', TRUE));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }
        getAuthStaff('id');
        $this->PurchaseOrder->id = $id;
        $this->PurchaseOrder->saveField('follow_up_status', $status_id);
        $this->add_actionline(ACTION_CHANGE_FOLLOW_UP_STATUS, [
            'primary_id' => $id,
            'secondary_id' => $purchase_order['PurchaseOrder']['follow_up_status'] ?? null,
            'param1' => $status_id,
            'param2' => "Purchase Invoice",
            'param3' => json_encode([
                'controller' => 'purchase_invoices',
                'action' => 'view',
                $id,
            ]),
            'param4' => $purchase_order['PurchaseOrder']['no'] ?? null
        ]);
        $flash = 'Purchase invoice status has been updated';
        $this->flashMessage(__($flash, true), 'Sucmessage', 'secondaryMessage');
        $this->redirect(array('action' => 'view', $id));
        die();
    }

    function owner_view($id = false, $alt_template_id = false) {
        App::import('Vendor', 'settings');
        $this->set('alt_template_id', $alt_template_id);
        $po_setting =  (settings::formData(InventoryPlugin)) ;
        $this->set('enable_purchase_manual_status', settings::getValue(InvoicesPlugin, 'enable_purchase_manual_status'));
        $this->loadModel('FollowUpStatus');
        $this->set('po_setting',$po_setting);
        $this->loadModel('StockTransaction');
        $this->loadModel('Post');
        $this->loadModel('CostCenter');
        $this->PurchaseOrder->bindAttachmentRelation('purchase_order');
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => 0));
        if (!$purchase_orders) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Purchase Invoice not found', true)));
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect('/v2/owner/entity/purchase_order/list');
        }
        $this->loadModel('PurchaseOrderDocument');
        $PurchaseOrderDocuments = $this->PurchaseOrder->PurchaseOrderDocument->find('all',array('order'=>'PurchaseOrderDocument.id desc','conditions'=>array('PurchaseOrderDocument.purchase_order_id'=>$id)));
        $this->set('PurchaseOrderDocuments',$PurchaseOrderDocuments);
        $PurchaseOrderDocumentCount = $this->PurchaseOrder->PurchaseOrderDocument->find('count',array('conditions'=>array('PurchaseOrderDocument.purchase_order_id'=>$id)));
        $this->set('PurchaseOrderDocumentCount',$PurchaseOrderDocumentCount);
        $this->set("show_discount", $purchase_orders['show_discount']);
        $RequisitionModel = GetObjectOrLoadModel('Requisition');
        $requisition_exists = $RequisitionModel->find('first',
            [
                'conditions' => [
                    'Requisition.order_id' => $id,
                    'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_REFUND, Requisition::ORDER_TYPE_PURCHASE_ORDER]
                ],
                'applyBranchFind' => false
            ]);
        $this->set('requisition_exists', $requisition_exists);


        $this->loadModel('Post');
//        $this->set('note_followup_list', $note_followup_list);
        $this->set('post_count', $this->Post->find('count', array('conditions' => ['item_id' => $id, 'item_type' => Post::PO_TYPE])));

        $this->set('refund_count',$this->PurchaseOrder->get_refund_count($id,true));
        $this->set('refund_list',$this->PurchaseOrder->get_refund_list($id,true));
        $this->set('debit_note_list',$this->PurchaseOrder->get_debit_note_list($id,true));

        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(View_All_Purchase_Orders) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(View_his_own_created_Purchase_Orders)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $purchasesLayouts = GetObjectOrLoadModel('InvoiceLayout')->find('list', array('conditions' => array('InvoiceLayout.layout_type' => InvoiceLayout::TYPE_PO, 'InvoiceLayout.alt_template' => 1)));
        $this->set('purchasesLayouts', $purchasesLayouts);

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('purchase_order');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);

        $this->set_journal($id);
        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->set('PurchaseOrderAllPaymentStatus', PurchaseOrder::getPaymentStatuses());
        $this->set('PurchaseOrderPaymentStatus', PurchaseOrderPayment::getPaymentStatus());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->set('stocks', StockTransaction::getPOTransactions($id));

        $emailLogs = $this->PurchaseOrder->EmailLog->find('all', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1, 'limit' => 10, 'order' => 'EmailLog.sent_date DESC'));
        $emailCount = $this->PurchaseOrder->EmailLog->find('count', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1));

        $this->set(compact('emailLogs', 'emailCount'));


        $this->_getLayout($alt_template_id ?: $purchase_orders['PurchaseOrder']['invoice_layout_id']);

        $this->set('title_for_layout',  sprintf(__('Purchase Invoice #%s', true), $purchase_orders['PurchaseOrder']['no']));

        if ($this->params['url']['ext'] == 'pdf') {
            $this->_setBranded();
        }

        $country = getCountry($purchase_orders['PurchaseOrder']['supplier_country_code']);
        $supplierCountry = $country['Country']['country'];
        $this->set('supplierCountry', $supplierCountry);

        $country = getCountry(getCurrentSite('country_code'));
        $ownerCountry = $country["country"];
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Store');
        $stores = $this->Store->get_all_store_list();
        $this->set('stores', $stores);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->find('list'));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Invoices', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/purchase_order/list';

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';


        require_once APP . 'vendors' . DS . 'Timeline.php';

        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));
        $this->loadModel('ActionLine');


        $action_list = $timeline->getPurchaseOrderActionsList();
        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $invoice_actions[$key] = $action;
            }
        }
        $this->set('purchase_orders', $purchase_orders);

        if (!empty($purchase_orders['PurchaseOrder']['source_id'])) {
            $this->set('original_purchase_order', $this->PurchaseOrder->findById($purchase_orders['PurchaseOrder']['source_id']));
        }

        $this->set('purchase_orders_id', $id);
        $this->set('actions', $invoice_actions);
        $this->loadModel('SitePaymentGateway');
        $pm=$this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));
        $pm['supplier_credit']=__('Supplier Credit',true);
        $this->set('payment_methods', $pm);
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));

        $this->loadModel('ItemsTag');
        $this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER);
        $tags = $this->ItemsTag->get_item_tags($id,ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER,true);
        $this->set('tags',$tags);

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('purchaseorder');

        if(!empty($printableTemplates))
            $this->set(compact('printableTemplates'));
        if(IS_REST){
            $this->set('rest_item', $purchase_orders);
            $this->set('rest_model_name', "PurchaseOrder");
            $this->render("view");
        }
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po') || $requisition_exists;
        if ( $enable_requisitions){
            $this->loadModel('Requisition') ;
        }
        $this->set('enable_requisitions' ,$enable_requisitions);
        $FollowUpStatus = $this->FollowUpStatus->getLisWithColorList(Post::MANUAL_PURCHASE_ORDER);
        $this->set('FollowUpStatus', $FollowUpStatus);

        $service = izam_resolve(ButtonServiceErrorHandlerDecorator::class);
        $appsDropdown = $service->getByLocationKey(AppButtonLocationUtil::PURCHASE_INVOICE_VIEW, $id);
        $this->set('apps_dropdown', $appsDropdown); 

        $purchase_invoice_journal = $this->PurchaseOrder->get_entity_journal($id, ['entity_type' => 'purchase_order', 'subkey' => 'sales_cost']);
        $credit_note_count = settings::getValue(InventoryPlugin, 'enable_purchase_credit_note') ? $this->PurchaseOrder->get_credit_note_count($id, true) : 0;
        $this->set('entity_key', EntityKeyTypesUtil::PURCHASE_INVOICE);
        $this->set('credit_note_count',$credit_note_count);
        $this->set('journal', $purchase_invoice_journal);
        $this->set('add_cost_center_url', $this->get_cost_center_route($purchase_invoice_journal));
        $this->set('cost_centers', $this->CostCenter->find('list'));
        $this->set('forms', $this->createAdditionalFieldsFormHandlerInstance()->show($id));


        $isPurchaseInvoiceItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);

        if(!empty($isPurchaseInvoiceItemAccountEnabled)) {
            $salesCostAccountIds = array_filter(Set::extract('{n}.sales_cost_account_id', $purchase_orders['PurchaseOrderItem']));
            $this->loadModel('JournalAccount');
            $this->set('invoiceItemAccounts', $this->JournalAccount->find('list', ['conditions' => ['JournalAccount.id' => $salesCostAccountIds]]));
        }
        
       $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
       if ($enablePurchaseInvoiceItemCostCenterDistribution) {
            $invoiceItemCostCenters = array_filter(Set::extract('{n}.cost_center_id', $purchase_orders['PurchaseOrderItem']));
            $this->loadModel('CostCenter');
            $this->set('invoiceItemCostCenters', $this->CostCenter->find('list', ['conditions' => ['CostCenter.id' => $invoiceItemCostCenters]]));

       }

    }

    private function get_cost_center_route($journal)
    {
        //Incase the purchase invoice was a draft it has no journal
        if(!$journal){
            return false;
        }
        $this->loadModel('CostCenterTransaction');
        $transactions = $journal['JournalTransaction'];
        $assigned_cost_centers = $this->CostCenterTransaction->getJournalCostTransaction($journal['Journal']['id']);
        $store_journal_transaction = array_values(array_filter($journal['JournalTransaction'], function($transaction) {
            return $transaction['subkey'] == 'purchases'
             || ($transaction['currency_debit'] > 0 && str_starts_with($transaction['subkey'], 'store_'))
             || ($transaction['currency_debit'] > 0 && strpos($transaction['subkey'], 'sales_cost') !== false);
        }));

        if (!empty($assigned_cost_centers) && is_null($assigned_cost_centers[0]['CostCenterTransaction']['journal_transaction_id'])) {
            return  Router::url(array('controller' => 'cost_centers', 'action' => 'add_cost_center', $journal['Journal']['id'], $store_journal_transaction[0]['journal_account_id']));
        }

        return  Router::url(array('controller' => 'cost_centers', 'action' => 'add_cost_center_v2', $journal['Journal']['id'], $store_journal_transaction[0]['id'], $store_journal_transaction[0]['journal_account_id']));
    }

    function owner_view_refund($id = false, $alt_template_id = false,$type = PurchaseOrder::Purchase_Refund) {

        if( ! ifPluginActive(InventoryPlugin)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $this->isRefund = true;
        if ($type == PurchaseOrder::DEBIT_NOTE) {
            $this->set('is_debit_note',true);
        }
        App::import('Vendor', 'settings');
        $po_setting =  ( settings::formData(InventoryPlugin)) ;
        $this->set('po_setting',$po_setting);
        $this->loadModel('StockTransaction');
        $this->PurchaseOrder->bindAttachmentRelation('purchase_order');
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, ['PurchaseOrder.type' => $type]);
        if(!empty($purchase_orders['PurchaseOrder']['subscription_id']))
        {
            $source_po = $this->PurchaseOrder->getPurchaseOrder($purchase_orders['PurchaseOrder']['subscription_id'], array('PurchaseOrder.type' => 0));
            $this->set('source_po',$source_po);
        }

        if (!$purchase_orders) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Purchase Invoice not found', true)));
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(View_All_Purchase_Orders) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(View_his_own_created_Purchase_Orders)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to edit this purchase invoice ", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }
//		$this->_setBranded();
        $this->set_journal($id,$type == PurchaseOrder::DEBIT_NOTE ? 'purchase_debit_note' : 'purchase_refund');
        $this->set('client_nav',$this->setup_nav_view($id));
        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());


        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->set('stocks', $type == PurchaseOrder::DEBIT_NOTE ? StockTransaction::getPDNTransactions($id) : StockTransaction::getPRTransactions($id));

        $emailLogs = $this->PurchaseOrder->EmailLog->find('all', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1, 'limit' => 10, 'order' => 'EmailLog.sent_date DESC'));
        $emailCount = $this->PurchaseOrder->EmailLog->find('count', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1));

        $this->set(compact('emailLogs', 'emailCount'));



        $this->set('title_for_layout',  sprintf(__('Purchase Refund %s', true), '#'.$purchase_orders['PurchaseOrder']['no']));

        if ($this->params['url']['ext'] == 'pdf') {
            $this->_setBranded();
        }

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->find('list'));





        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase refund', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/purchase_refund/list';

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';


        require_once APP . 'vendors' . DS . 'Timeline.php';

        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));
        $this->loadModel('ActionLine');

        if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::Purchase_Refund){
            $action_list = $timeline->getPurchaseRefundActionsList();
        }elseif ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::DEBIT_NOTE) {
            $action_list = $timeline->getPurchaseDebitNoteActionsList();
        }

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $invoice_actions[$key] = $action;
            }
        }
        $this->set('purchase_orders', $purchase_orders);

        $this->set('purchase_orders_id', $id);
        $this->set('actions', $invoice_actions);

        if ($type == PurchaseOrder::Purchase_Refund) {
            $this->set('PurchaseOrderAllPaymentStatus', PurchaseOrder::getPaymentStatuses());
            $this->set('PurchaseOrderPaymentStatus', PurchaseOrderPayment::getPaymentStatus());
            $this->loadModel('SitePaymentGateway');
            $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id')));
            $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));
        }

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('purchaseorder');
        $this->loadModel('Requisition') ;
        $this->set('enable_requisitions' , settings::getValue(InventoryPlugin,'enable_requisitions_po'));

        $purchasesLayouts = GetObjectOrLoadModel('InvoiceLayout')->find('list', array('conditions' => array('InvoiceLayout.layout_type' => InvoiceLayout::TYPE_PO, 'InvoiceLayout.alt_template' => 1)));
        $this->set('purchasesLayouts', $purchasesLayouts);

        $this->set('has_templates', false);
        if(!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('purchase_order');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);

        $this->loadModel('PurchaseOrderDocument');
        $PurchaseOrderDocuments = $this->PurchaseOrder->PurchaseOrderDocument->find('all',array('order'=>'PurchaseOrderDocument.id desc','conditions'=>array('PurchaseOrderDocument.purchase_order_id'=>$id)));
        $this->set('PurchaseOrderDocuments',$PurchaseOrderDocuments);
        $PurchaseOrderDocumentCount = $this->PurchaseOrder->PurchaseOrderDocument->find('count',array('conditions'=>array('PurchaseOrderDocument.purchase_order_id'=>$id)));
        $this->set('PurchaseOrderDocumentCount',$PurchaseOrderDocumentCount);          
        $this->set('HtmlModel', 'PurchaseOrderDocument');  

        $layout = $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id']);
        $this->set('layout', $layout);

        if(IS_REST){
            $purchase_orders["PurchaseRefund"] = $purchase_orders["PurchaseOrder"];
            unset($purchase_orders["PurchaseOrder"]);
            $this->set('rest_item', $purchase_orders);
            $this->set('rest_model_name', "PurchaseRefund");
            $this->render("view");
        }

        $service = izam_resolve(ButtonServiceErrorHandlerDecorator::class);
        $appsDropdown = $service->getByLocationKey(AppButtonLocationUtil::PURCHASE_REFUND_VIEW, $id);
        $this->set('forms', $this->createAdditionalFieldsFormHandlerInstance()->show($id));
        $this->set('apps_dropdown', $appsDropdown);
        $this->_getLayout($alt_template_id ?: $purchase_orders['PurchaseOrder']['invoice_layout_id']);

        $isPurchaseInvoiceItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);

        if(!empty($isPurchaseInvoiceItemAccountEnabled)) {
            $salesCostAccountIds = array_filter(Set::extract('{n}.sales_cost_account_id', $purchase_orders['PurchaseOrderItem']));
            $this->loadModel('JournalAccount');
            $this->set('invoiceItemAccounts', $this->JournalAccount->find('list', ['conditions' => ['JournalAccount.id' => $salesCostAccountIds]]));
        }
        
       $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
       if ($enablePurchaseInvoiceItemCostCenterDistribution) {
            $invoiceItemCostCenters = array_filter(Set::extract('{n}.cost_center_id', $purchase_orders['PurchaseOrderItem']));
            $this->loadModel('CostCenter');
            $this->set('invoiceItemCostCenters', $this->CostCenter->find('list', ['conditions' => ['CostCenter.id' => $invoiceItemCostCenters]]));

       }
       
    }

//-----------------------------
    function owner_pdf($id = false) {
        $this->owner_preview($id);
        $this->params['url']['ext'] = 'pdf';
        $this->render('pdf/owner_view');
    }

//-----------------------------
    function owner_jpeg($id = false) {
        $this->owner_preview($id);
        $this->params['url']['ext'] = 'jpeg';
        $this->render('jpeg/owner_view');
    }

//--------------------------------
    function owner_delete($id = null) {

        if( ! ifPluginActive(InventoryPlugin)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }

        $owner = getAuthOwner();
        if (!check_permission(Edit_Delete_All_Purchase_Orders) && !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
            if(IS_REST) {
                $this->cakeError("error400", ["message"=>__("You are not allowed to delete purchase invoices", true)]);
            }
            $this->flashMessage(__("You are not allowed to delete purchase invoices", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/purchase_order/list');
        }
        $this->_record_referer_path();
//If request type is post
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        $module_name = __('Purchase Invoice', true);
        $verb = __('has', true);
        if (!empty($_POST['ids']) && count($_POST['ids']) > 1) {
            $verb = __('have', true);
            $module_name = __('Purchase Invoices', true);
        }
        if (!$id && empty($_POST)) {
            if(IS_REST){
                $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), $module_name)]);
            }else{
                $this->flashMessage(sprintf(__('Invalid %s', true), $module_name));
                $referer_url = !($this->Session->check('referer_url')) ? '/v2/owner/entity/purchase_order/list' : $this->Session->read('referer_url');
                $this->Session->delete('referer_url');
                $this->redirect($referer_url);
            }
        }

        //$site_id = getAuthOwner('id');

        $conditions = array('PurchaseOrder.id' => $id);
        if ($owner['staff_id'] != 0 and !check_permission(Edit_Delete_All_Purchase_Orders)) {
            $conditions['PurchaseOrder.staff_id'] = $owner['staff_id'];
        }

        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        $requisition_model = GetObjectOrLoadModel('Requisition');

        $purchase_orderss = $this->PurchaseOrder->find('all', array('conditions' => $conditions));

        $title_for_layout=  __('Delete Purchase Invoice', true);
        if (isset($purchase_orderss[0]['PurchaseOrder']['type']) && $purchase_orderss[0]['PurchaseOrder']['type']==PurchaseOrder::CREDIT_NOTE) {
            $module_name = __('Credit Note', true);
            $verb = __('has', true);
            $title_for_layout=  __('Delete Credit Note', true);

        }

        $purchaseOrdersEntities = [];
        foreach($purchase_orderss as $k => $purchase_order)
        {
            $purchaseOrdersEntities[$purchase_order['PurchaseOrder']['id']] = []; 

            if (EventListenerMapper::hasEventListener(EventTypeUtil::PURCHASE_ORDER_DELETED)) {
                $purchaseOrdersEntities[$purchase_order['PurchaseOrder']['id']] = getRecordWithEntityStructure('purchase_order', $purchase_order['PurchaseOrder']['id'],2)->toArray();
            }

            $this->validate_open_day($purchase_order['PurchaseOrder']['date'], ['action' => 'index']);

            $requisition_saved = $requisition_model->find('first', ['conditions' => ['Requisition.order_id' => $purchase_order['PurchaseOrder']['id'], 'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_ORDER, Requisition::ORDER_TYPE_PURCHASE_REFUND]]]);
            if($enable_requisitions && $purchase_order['PurchaseOrder']['requisition_delivery_status'] == Requisition::DELIVERY_STATUS_RECEIVED && $requisition_saved)
            {
                if (IS_REST){
                    $this->cakeError("error400", [
                        "message" => sprintf(__("You are not allowed to edit this record as it's already received", true), $module_name)
                    ]);
                }

                $this->flashMessage(__("You are not allowed to edit this record as it's already received", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }

            $trackingValidationResult = TrackStockValidator::validate(
                $purchase_order,
                TrackStockUtil::PURCHASE_ORDER,
                TrackStockUtil::getPurchaseOrderValidationType($purchase_order['PurchaseOrder']['type']),
                true
            );
            if($trackingValidationResult !== true)
            {
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }

            $this->validateItemIsNotLocked(
                $id, $purchase_order['PurchaseOrder']['type'], 'Purchase Refund', Router::url(['action' => 'owner_view', $id])
            );
        }
        if (empty($purchase_orderss)) {
            if(IS_REST){
                $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), ucfirst($module_name))]);
            }else{
                $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }
        if(IS_REST){
            $_POST['submit_btn'] = 'yes';
            $_POST['ids'] = [$id];
        }
        $StockTransaction = GetObjectOrLoadModel("StockTransaction");
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            $delete_conditions = array('PurchaseOrder.id' => $id);
            if ($owner['staff_id'] != 0 and !check_permission(Edit_Delete_All_Purchase_Orders)) {
                $delete_conditions['PurchaseOrder.staff_id'] = $owner['staff_id'];
            }
            $store_balance = true ;
            if ($_POST['submit_btn'] == 'yes' && ifPluginActive(InventoryPlugin) )
            {
                $this->loadModel('Requisition');
                foreach ( $purchase_orderss as $po ) {
                    $requisition_count = $this->Requisition->find('count' ,  ['recursive' => -1 , 'conditions' => ['Requisition.status' =>[Requisition::STATUS_ACCEPTED,Requisition::STATUS_MODIFIED] ,  'Requisition.order_id' =>$po['PurchaseOrder']['id'] , 'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_ORDER,Requisition::ORDER_TYPE_PURCHASE_REFUND] ] ]);
                    if ( $requisition_count > 0 ){
                        if(IS_REST){
                            $this->cakeError("error401", ["message"=>sprintf(__('You cant delete the %s', true), $module_name)]);
                        } else {
                            $this->flashMessage(sprintf(__('You cant delete the %s', true), $module_name));
                            $this->redirect('/v2/owner/entity/purchase_order/list');
                        }
                    }
                    if (settings::getValue(InventoryPlugin, 'disable_overdraft') ){
                        $map_products = [] ;
                        foreach ( $po['PurchaseOrderItem'] as $k  ) {
                            $map_products[$k['product_id']]['quantity'] = 0;
                        }

                        $StockTransactionList=StockTransaction::getPOTransactions($id, false);
                        foreach($StockTransactionList as $stock_transaction){
                            $map_products[$stock_transaction['StockTransaction']['product_id']]['quantity']=$map_products[$stock_transaction['StockTransaction']['product_id']]['quantity']+$stock_transaction['StockTransaction']['quantity'];
                        }

                        if($po['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_INVOICE){
                            if(
                                ifPluginActive(InventoryPlugin) &&
                                !$po['PurchaseOrder']['draft'] &&
                                !settings::getValue(InventoryPlugin, 'enable_requisitions_po') &&
                                settings::getValue(InventoryPlugin, 'disable_overdraft')
                            ) {
                                $newTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($po['PurchaseOrder']['store_id'], $po['PurchaseOrderItem']);
                                foreach ($newTransactionsProductStoreQuantities as $productId => $productStoreQuantities) {
                                    foreach ($productStoreQuantities as $storeId => $quantity) {
                                        if(!$StockTransaction->check_balance_open($productId, $storeId, $quantity, 0, 'deduct')) {
                                            $this->loadModel('Product');
                                            $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $productId]]);
                                            $name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                                            if (IS_REST) {
                                                $this->cakeError("error400", ["message"=>__('This quantity already has a transaction', true)]);
                                            }
                                            $this->flashMessage(__('This quantity already has a transaction', true), 'Errormessage', 'secondaryMessage');
                                            $store_balance = false;
                                        }
                                    }
                                }
                            }
                        }

                        if ( !$store_balance ) {
                            break ; //Breaking from the outside foreach if there is an item that failed check balance
                        }
                    }
                }
            }
            if ($store_balance&& $_POST['submit_btn'] == 'yes' && $this->PurchaseOrder->delete_related_items($id) && $this->PurchaseOrder->deleteAll($delete_conditions,false) ) {

                foreach ($purchase_orderss as $purchase_orders) {

                    if (EventListenerMapper::hasEventListener(EventTypeUtil::PURCHASE_ORDER_DELETED)) {
                        izam_resolve(PurchaseOrderService::class)->delete($purchaseOrdersEntities[$purchase_orders['PurchaseOrder']['id']]);
                    }
                    //  NotificationV::delete_notificationbyref($purchase_orders['PurchaseOrder']['id'], RECURRING_INVOICE_GENERATED);


                    // (primary_id => purchase_order_id, sec => supplier_id, p1 => total, p2=> purchaseorder_status)    
                    if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::Purchase_Refund){
                        $this->add_actionline(ACTION_DELETE_PR, array('primary_id' => $purchase_orders['PurchaseOrder']['id'], 'secondary_id' => $purchase_orders['PurchaseOrderItem']['product_id'], 'param1' => $purchase_orders['PurchaseOrder']['summary_total'], 'param2' => $purchase_orders['PurchaseOrder']['payment_status'], 'param3' => $purchase_orders['PurchaseOrder']['summary_paid'], 'param4' => $purchase_orders['PurchaseOrder']['no'], 'param5' => $purchase_orders['Supplier']['business_name'], 'param6' => $purchase_orders['Supplier']['supplier_number']));
                        $this->PurchaseOrder->updatePurchaseOrderPayments($purchase_orders['PurchaseOrder']['subscription_id']);
                    }else{
                        $this->add_actionline(ACTION_DELETE_PO, array('primary_id' => $purchase_orders['PurchaseOrder']['id'], 'secondary_id' => $purchase_orders['PurchaseOrderItem']['product_id'], 'param1' => $purchase_orders['PurchaseOrder']['summary_total'], 'param2' => $purchase_orders['PurchaseOrder']['payment_status'], 'param3' => $purchase_orders['PurchaseOrder']['summary_paid'], 'param4' => $purchase_orders['PurchaseOrder']['no'], 'param5' => $purchase_orders['Supplier']['business_name'], 'param6' => $purchase_orders['Supplier']['supplier_number']));
                    }
                    if (ifPluginActive(InventoryPlugin)) {
                        $purchase_orders['PurchaseOrderItem'] = [] ;
                        if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::Purchase_Refund){
                            StockTransaction::updateForPr($purchase_orders);
                        }else{
                            StockTransaction::updateForPo($purchase_orders);
                        }
                    }
                    $this->PurchaseOrder->delete_auto_journals($purchase_orders['PurchaseOrder']['id']);
                    $this->PurchaseOrder->Supplier->adjust_balance($purchase_orders['PurchaseOrder']['supplier_id'], $purchase_orders['PurchaseOrder']['currency_code']);
                    $this->PurchaseOrder->Supplier->pay_pos_from_credit($purchase_orders['Supplier']['supplier_id']);
                    if ($purchase_orders["PurchaseOrder"]['source_id']) {
                        $po = $this->PurchaseOrder->findById($purchase_orders["PurchaseOrder"]['source_id']);
                        if ($po['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER) {
                            $this->PurchaseOrder->create();
                            $this->PurchaseOrder->id = $po["PurchaseOrder"]['id'];
                            $this->PurchaseOrder->data['PurchaseOrder']['store_id'] = $po["PurchaseOrder"]['store_id'];
                            $this->PurchaseOrder->save(['PurchaseOrder' => [
                                'payment_status' => \Izam\Daftra\Common\Utils\PurchaseOrderStatusUtil::Pending
                            ]]);
                        }
                    }

                    izam_resolve(AttachmentsService::class)->removeAttachmentsByEntityDetails('purchase_order',$purchase_orders['PurchaseOrder']['id']);

                }


                if(IS_REST){
                    $this->set("message", sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb));
                    $this->render("success");
                    return;
                }else{
                    $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                    $referer_url = $this->_get_referer_path();
                    $this->redirect('/v2/owner/entity/purchase_order/list');
                }
            } else {
                //$referer_url = $this->_get_referer_path();
                //  $this->redirect($referer_url);
                if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $this->set('purchase_orders', $purchase_orderss);
        $this->set('module_name', $module_name);

        $this->set('title_for_layout',$title_for_layout);
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Refunds', true);
        $this->crumbs[0]['url'] = '/v2/owner/entity/purchase_refund/list';

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';
    }

//---------------------------


    function owner_send_to_supplier($id = null) {
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id);
        $check = check_daily_email_limit();

        $this->crumbs = array();
        $this->loadModel('EmailTemplate');
        if($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund){
            $viewAction = "view_refund";
            $purchase_orders_type = "Purchase Refund";
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-refund');
        }elseif($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE){
            $viewAction = "view_debit_note";
            $purchase_orders_type = "Purchase Debit Note";
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-debitnote');
        }elseif($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE){
            $viewAction = "view_credit_note";
            $purchase_orders_type = "Purchase Credit Note";
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-creditnote');
        }else{
            $viewAction = "view";
            $purchase_orders_type = "Purchase Invoice";
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-orders');
        }

        if (!$check['status']) {  
            $this->flashMessage($check['message']);  
            $this->redirect(array('action' => $viewAction, $id));
        }

        if (empty($purchase_orders) || empty($purchase_orders_type)) {
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        if (empty($purchase_orders['Supplier']['email'])) {
            $this->flashMessage(__('The Email address for this Supplier is not entered', true));
            $this->redirect($this->referer(array('action' => $viewAction, $purchase_orders['Supplier']['id']), true));
        }
        $this->set('purchase_orders', $purchase_orders);
        $this->set('purchase_order', $purchase_orders);


        $this->loadModel('EmailLog');
        $this->loadModel('EmailTemplate');

        $this->set(compact('defaultTemplate'));

        if (!empty($this->data)) {
            if ($this->_directSendEmail($purchase_orders, $this->data)) {  
                $this->flashMessage(__('The ' . $purchase_orders_type . ' has been sent', true), 'Sucmessage');
                $this->redirect(array('action' => $viewAction, $id));
            } else {  
                $this->flashMessage(sprintf(__('Could not send %s', true), __('Purchase Invoice', true)));
            }
        } else {
            $this->data = $defaultTemplate;
            unset($this->data["EmailTemplate"]['id']);
        }
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->set('email_templates', $this->EmailTemplate->getEmailTemplates($purchase_orders_type));
        $this->set('purchaseorder_type', $purchase_orders_type);
        $this->set('PlaceHolders', $this->EmailTemplate->getPlaceHoldersList('purchase-orders'));
        $this->set('supplier_email', $this->PurchaseOrder->Supplier->getSupplierEmails($purchase_orders['Supplier']['id']));
        $this->set('title_for_layout',  __('Send ' . $purchase_orders_type . ' to supplier', true));
    }

    function view($id = null, $type = 0) {
        $this->Cookie->domain = Domain_Short_Name;
        $this->Cookie->write('trk', 'system_supplier',false,3.154e+7); // 1 year
        $this->_checkHash($id, $type);
    }

    function _checkHash($id, $type = 0) {
        $conditions = array('PurchaseOrder.type' => $type);
        if ($type == 1) {
            $conditions['PurchaseOrder.draft !='] = 1;
        }
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, $conditions);
        if (!$purchase_orders) {
            $this->redirect(array('controller' => 'suppliers', 'action' => 'login'));
        }

        $purchase_ordersHash = $this->PurchaseOrder->getHash($purchase_orders);
        if ($purchase_ordersHash == $_GET['hash']) {
            $supplier = $this->PurchaseOrder->Supplier->findById($purchase_orders['PurchaseOrder']['supplier_id']);
            $this->PurchaseOrder->Supplier->reload_session($supplier);

            $this->redirect(array('action' => 'view_order', $id));
        } else {
            $this->redirect("/?error=404");
        }
    }

    function _createPurchaseOrder($id = null) {
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id);

        $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id'], $purchase_orders['Site']['id']);



        $view = new View($this, true);
        $view->set('purchase_orders', $purchase_orders);
        $view->set(compact('purchaseorder'));
        $view->set('save', true);

        $view->params['ext'] = 'pdf';
        $view->params['url']['ext'] = 'pdf';
        $view->layout = '';
        $site = $purchase_orders['Site'];
        $view->set($this->_setBranded($purchase_orders['Site']['id']));
        if (!$site) {
            $SiteModel = getSiteModel();
            $site = $SiteModel->findById($purchase_orders['PurchaseOrder']['site_id']);
            $view->set('site', $site['Site']);
        } else {
            $view->set(compact('site'));
        }

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $view->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $view->set('ownerCountry', $ownerCountry);

        $layout = $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id']);
        $view->set('layout', $layout);

        $view->render('pdf/owner_view');
        return $filename = $view->invoiceFileName;
    }

//-------------------------------------
    function _filter_params($params = false, $filters = [], $passedModelName = false) {
        $conditions = parent::_filter_params($params, $filters);
        if (!empty($this->params['url']['supplier_id'])) {
            $conditions['PurchaseOrder.supplier_id'] = intval($this->params['url']['supplier_id']);
        }

        if (!empty($this->params['url']['id'])) {
            $conditions['PurchaseOrder.id'] = $this->params['url']['id'];
        }




        if (!empty($conditions['PurchaseOrder.due_date <='])) {
            $conditions[] = "DATE_ADD(`PurchaseOrder`.`date`,INTERVAL `PurchaseOrder`.`due_after` DAY) <= '{$conditions['PurchaseOrder.due_date <=']}'";
        }
        unset($conditions['PurchaseOrder.due_date <=']);

        if (!empty($conditions['PurchaseOrder.due_date >='])) {
            $conditions[] = "DATE_ADD(`PurchaseOrder`.`date`,INTERVAL `PurchaseOrder`.`due_after` DAY) >= '{$conditions['PurchaseOrder.due_date >=']}'";
        }
        unset($conditions['PurchaseOrder.due_date >=']);

        if (!empty($this->params['url']['draft'])) {
            $conditions['PurchaseOrder.draft'] = 1;
        }

        if (!empty($this->params['url']['subscription_id'])) {
            $conditions['PurchaseOrder.subscription_id'] = intval($this->params['url']['subscription_id']);
        }

        if (!empty($this->params['url']['follow_up_status'])) {
            $conditions['PurchaseOrder.follow_up_status'] = intval($this->params['url']['follow_up_status']);
        }

        return $conditions;
    }

//----------------------------
    function _settings($id = null) {
        $this->loadModel('Tax');
        $this->set('tax_count',$this->Tax->find('count'));
        $this->set('paymentStatuses', PurchaseOrder::getPaymentStatusesList());
        $this->set('DeliveryStatuses', PurchaseOrder::getDeliveryStatuses());
        App::import('Vendor', 'settings');
        $this->loadModel('Journal');

        if($this->isRefund)
        {
            $this->setAccountRoute('purchase_returns_accounts_routing', Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE, $id);
        }else{
            $this->setAccountRoute('purchases_accounts_routing', Journal::PURCHASES_ACCOUNT_ENTITY_TYPE, $id);
        }

        $po_setting =settings::formData(InventoryPlugin) ;
        $this->set('po_setting',$po_setting);
        $this->loadModel('Product');
        $this->loadModel('PurchaseOrderItem');


        $this->loadModel('PurchaseOrderTax');

        if (empty($this->data['PurchaseOrder']['id'])) {
            $this->set('taxes', $this->PurchaseOrderTax->getPurchaseOrderTaxList());
            $this->set('jsTaxes', $this->PurchaseOrderTax->getJSONList());
        } else {
            $this->set('taxes', $this->PurchaseOrderTax->getPurchaseOrderTaxList($this->data['PurchaseOrder']['id']));
            $this->Set('jsTaxes', $this->PurchaseOrderTax->getJSONList($this->data['PurchaseOrder']['id']));
        }

        // $this->set('sendWhens', PurchaseOrder::getRemindersWhenList());

        $this->loadModel('EmailTemplate');
        $this->set('emailTemplates', $this->EmailTemplate->getEmailTemplateList());

        $this->set('languages', array());
        $this->loadModel('Term');
        $this->set('termsConditions', $this->Term->getTermList());
//		$this->set('termsFiles', $this->Term->getTermList('file'));

        $ajax_suppliers = false;
        $suppliers_count = $this->PurchaseOrder->Supplier->getSupplierCount();
        if ($suppliers_count < AJAX_CLIENT_COUNT) {
            $this->set('suppliers', $this->PurchaseOrder->Supplier->getSuppliersList());
        } else {
            $this->set('suppliers', $this->PurchaseOrder->Supplier->getSuppliersList());
            $ajax_suppliers = true;
        }
        $this->set('ajax_suppliers', $ajax_suppliers);

        $this->set("discount_types" , $this->PurchaseOrder->getDiscountTypes( ) );
        $discount_option =settings::getValue(InventoryPlugin,"purchase_discount_option" ) ;
        $this->set("discount_option" , ($discount_option?:'1' ) );

        $this->set('currencies', getCurrenciesList());

        $this->set('countries', getCountriesList());

        $this->loadModel('InvoiceLayout');
        if ($this->action == 'owner_add_debit_note') {
            $this->set('default_pol', $this->InvoiceLayout->field('id', array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default_purchase_debit_note' => true)));
        }else{
            $this->set('default_pol', $this->InvoiceLayout->field('id', array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default' => true)));
        }
        $this->set('default_prl', $this->InvoiceLayout->field('id', array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default_estimate' => true)));
        $invoiceLayoutsconditions[] = "InvoiceLayout.template_id <> 0";
        $invoiceLayoutsconditions['InvoiceLayout.layout_type']=InvoiceLayout::TYPE_PO;
        $invoiceLayouts = $this->InvoiceLayout->find('list', array('conditions' => $invoiceLayoutsconditions, 'order' => 'InvoiceLayout.name'));
        $this->set('invoiceLayouts', $invoiceLayouts);

        $invoiceLayouts2 = $this->InvoiceLayout->find('all', array('conditions'=>array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO),'fields' => 'id,label_item,label_tax1,label_tax2,label_quantity,label_unit_price,label_item_total,label_from_date,label_to_date,label_discount,label_description,label_subtotal', 'order' => 'InvoiceLayout.name'));
        foreach ($invoiceLayouts2 as $key => $invoiceLayoutslist) {
			$invoiceLayoutslist['InvoiceLayout']['label_tax1']=__('Tax 1',true);
			$invoiceLayoutslist['InvoiceLayout']['label_tax2']=__('Tax 2',true);
            $LayoutLable[$invoiceLayoutslist['InvoiceLayout']['id']] = $invoiceLayoutslist['InvoiceLayout'];
        }

        $invoiceLayoutsColumns = $this->InvoiceLayout->find('all', array('conditions'=>array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'item_columns IS NOT NULL AND item_columns <> \'\' ') ,'fields' => 'item_columns', 'order' => 'InvoiceLayout.name'));
        $item_columns=array();
        foreach ($invoiceLayoutsColumns as $key => $IL) {
//            debug($IL);
            $item_columns[$IL['InvoiceLayout']['id']] = json_decode($IL['InvoiceLayout']['item_columns'],true);

            //$this->data['Invoice']['item_columns'];
            //   $item_columns=  json_decode($this->data['Invoice']['item_columns'],true);
            if(isset($this->data['Invoice']['id']) && !empty($this->data['Invoice']['id'])){

                $item_columns[$IL['InvoiceLayout']['id']]=  json_decode($this->data['Invoice']['item_columns'],true);

            }


            //   debug($item_columns[$IL['InvoiceLayout']['id']] );
        }
        $this->set('item_columns',$item_columns);

        $LayoutLable[0] = array('label_item' => __('Item', true), 'label_tax1' => __('Tax 1', true), 'label_tax2' => __('Tax 2', true), 'label_quantity' => __('Quantity', true), 'label_unit_price' => __('Unit Price', true), 'label_item_total' => __('Item Total', true), 'label_from_date' => __('From Date', true), 'label_to_date' => __('To Date', true), 'label_discount' => __('Discount', true), 'label_description' => __('Description', true), 'label_subtotal' => __('Subtotal', true));
        $LayoutLable[-1] = array('label_item' => __('Item', true), 'label_tax1' => __('Tax 1', true), 'label_tax2' => __('Tax 2', true), 'label_quantity' => __('Hours', true), 'label_unit_price' => __('Hour Rate', true), 'label_item_total' => __('Item Total', true), 'label_from_date' => __('From Date', true), 'label_to_date' => __('To Date', true), 'label_discount' => __('Discount', true), 'label_description' => __('Description', true), 'label_subtotal' => __('Subtotal', true));

        $this->set('LayoutLable', $LayoutLable);

        $dbCustomFields = $this->InvoiceLayout->InvoiceLayoutCustomField->find('all', array('InvoiceLayoutCustomField.invoice_layout_id' => array_keys($invoiceLayouts)));
        $layoutCustomFields = array();
        $layoutCustomFields['0'] = $layoutCustomFields['-1'] = array();
        foreach ($dbCustomFields as $customField) {
            $layoutId = $customField['InvoiceLayoutCustomField']['invoice_layout_id'];
            if (empty($layoutCustomFields[$layoutId])) {
                $layoutCustomFields[$layoutId] = array();
            }
            $layoutCustomFields[$layoutId][] = $customField['InvoiceLayoutCustomField'];
        }


        $this->loadModel('Document');
        $this->set('documents', $this->Document->getInvoiceDocumentList());
        $this->set('documents_list', $this->Document->getDocumentList());
        $this->set('upload_settings', $this->Document->getFileSettings());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());
        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('dateFormats', getDateFormats(true));

//Ajax_product_search

        $this->loadModel('Product');
        $aps = '1';
        $products_ids = [];
        $products=[];
        $this->set('products',$products);
        if(!empty($this->data['PurchaseOrderItem']))
        {
            foreach($this->data['PurchaseOrderItem'] as $invoiceItem)
            {
                $products_ids[] = $invoiceItem['product_id'];
            }
            $products = $this->Product->getInvoiceProductList(false, ['Product.id' => $products_ids], true, $this->data['PurchaseOrderItem']) ;
            $this->set('products',$products);
        }


        $this->loadModel('Document');
        $this->set('aps', $aps);




        if (empty($this->data['PurchaseOrder']['language_id'])) {
            $ownerLanguage = getAuthOwner('language_id');
            if (!$ownerLanguage) {
                $ownerLanguage = 41;
            }
            $language = $languages[$ownerLanguage];
        } else {
            $language = $languages[$this->data['PurchaseOrder']['language_id']];
        }

        $this->set('purchaseorderLanguage', $language);




        if ( ifPluginActive(WorkOrderPlugin) ) {
            $this->loadModel('WorkOrder');
            if ( !empty ( $_GET['work_order_id'] )  ) {
                $this->data['PurchaseOrder']['work_order_id'] =  $_GET['work_order_id'];
            }
            $this->set ( 'work_order_ids' ,["" => '['.__("Choose from a list" , true ).']'] + $this->WorkOrder->get_work_orders ( ['WorkOrder.status'=>WorkOrder::STATUS_OPEN] ) ) ;
        }
        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
        if ( $enable_multi_units ) {
            $this->loadModel('UnitTemplate');
            $unit_templates = $this->UnitTemplate->find('list' , ['fields' => 'template_name']);
            $this->set('unit_templates' ,$unit_templates );
        }
        $this->set('enable_multi_units' ,$enable_multi_units );

        // warning suppress
        // $this->set(compact('addSupplierLimit'));

        if (!empty($this->data['PurchaseOrderCustomField'])) {
            foreach ($layoutCustomFields as &$lcfs) {

                $icfs = $this->data['PurchaseOrderCustomField'];
                foreach ($lcfs as &$lcf) {
                    foreach ($icfs as $id => $icf) {
                        if (trim(strtolower($icf['label'])) == trim(strtolower($lcf['label']))) {

                            $lcf['value'] = $icf['value'];
                            unset($icfs[$id]);
                        }
                    }
                }
                foreach ($icfs as $id => $icf) {
                    $lcfs[] = $icf;
                }
            }
        }

        $zerolayout = json_decode(settings::getValue(InvoicesPlugin, 'initial_po_custom_fields'), true) /*warning suppress*/ ?? [];

        $this->set('default_email_button', settings::getValue(InvoicesPlugin, 'default_email_button'));
        $this->set('default_print_button', settings::getValue(InvoicesPlugin, 'default_print_button'));

        foreach ($zerolayout as $line => $val2) {
            $layoutCustomFields[0][$line]['label'] = $val2['label'];
            $layoutCustomFields[0][$line]['value'] = $val2['value'];
            $layoutCustomFields[0][$line]['placeholder'] = $val2['placeholder'];
            $layoutCustomFields[0][$line]['display_order'] = 1;
            $layoutCustomFields[0][$line]['invoice_layout_id'] = 0;
            $layoutCustomFields[0][$line]['id'] = $line;
        }

        //warning suppress
        $this->data['PurchaseOrderCustomField'] = $this->data['PurchaseOrderCustomField'] ?? [];
        foreach ($this->data['PurchaseOrderCustomField'] as $line => $val2) {
            $layoutCustomFields[-99][$line]['label'] = $val2['label'];
            $layoutCustomFields[-99][$line]['value'] = $val2['value'];
            $layoutCustomFields[-99][$line]['placeholder'] = $val2['placeholder'];
            $layoutCustomFields[-99][$line]['display_order'] = 1;
            $layoutCustomFields[-99][$line]['invoice_layout_id'] = 0;
            $layoutCustomFields[-99][$line]['id'] = $line;
        }

        $this->set('layoutCustomFields', $layoutCustomFields);
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        if ( $enable_requisitions){
            $this->loadModel('Requisition') ;
        }
        $this->set('enable_requisitions' ,$enable_requisitions);
        $this->loadModel('SitePaymentGateway');
        $this->set('payment_treasury',$this->SitePaymentGateway->PaymentTreasury());
        if (ifPluginActive(PRODUCT_TRACKING_PLUGIN)) {
            $requisitionStatus = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
            $this->set('stockOperation', 'add');
            $this->set('addStockTracking', empty($requisitionStatus));
        }
    }

    function format_price($price, $code = false) {
        $this->layout = $this->autoRender = $this->autoLayout = false;
        echo format_price($price, $code);
        exit();
    }

    function _directSendEmail($purchase_orders, $layout_options = false, $owner = false) {

        if (empty($owner))
            $owner = getAuthOwner();

        if (is_numeric($purchase_orders)) {
            $purchase_orders_id = $purchase_orders;
        } else {
            $purchase_orders_id = $purchase_orders['PurchaseOrder']['id'];
        }

        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($purchase_orders_id);

        $this->loadModel('EmailTemplate');
        if ($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE) {
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-debitnote');

            if (!empty($layout_options))
                $defaultTemplate = array_merge($defaultTemplate, $layout_options);
            if (!empty($layout_options['EmailTemplate']['to_email'])) {
                $emails = explode(",", $layout_options['EmailTemplate']['to_email']);
            } else {
                $emails = array();
            }

            $result = $this->SysEmails->order($purchase_orders['PurchaseOrder'], $purchase_orders['Supplier'], $owner, $defaultTemplate, $purchase_orders['PurchaseOrderDocument'], false, $emails,$purchase_orders['Attachments']);
            if ($result) {
                $this->flashMessage(__('Purchase Debit Note has been saved & sent', true), 'Sucmessage');
                $this->PurchaseOrder->save(array('PurchaseOrder' => array(
                    'id' => $purchase_orders['PurchaseOrder']['id'],
                    'draft' => 0,
                    'last_sent' => date('Y-m-d')
                )), array('callbacks' => false, 'validation' => false, 'fieldList' => array('id', 'draft', 'last_sent')));
            } else {
                $this->flashMessage(sprintf(__('Failed to send the Purchase Debit Note, %s', true),$this->SysEmails->error_message), 'Errormessage', 'secondaryMessage');
            }
            return $result;
        }elseif($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE){
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-creditnote');
            if (!empty($layout_options))
                $defaultTemplate = array_merge($defaultTemplate, $layout_options);
            if (!empty($layout_options['EmailTemplate']['to_email'])) {
                $emails = explode(",", $layout_options['EmailTemplate']['to_email']);
            } else {
                $emails = array();
            }
            $result = $this->SysEmails->order($purchase_orders['PurchaseOrder'], $purchase_orders['Supplier'], $owner, $defaultTemplate, $purchase_orders['PurchaseOrderDocument'], false, $emails);
            if ($result) {
                $this->flashMessage(__('Purchase Credit Note has been saved & sent', true), 'Sucmessage');
                $this->PurchaseOrder->save(array('PurchaseOrder' => array(
                    'id' => $purchase_orders['PurchaseOrder']['id'],
                    'draft' => 0,
                    'last_sent' => date('Y-m-d')
                )), array('callbacks' => false, 'validation' => false, 'fieldList' => array('id', 'draft', 'last_sent')));
            } else {
                $this->flashMessage(sprintf(__('Failed to send the Purchase Credit Note, %s', true),$this->SysEmails->error_message), 'Errormessage', 'secondaryMessage');
            }
            return $result;
        }elseif ($purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund) {
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-refund');

            if (!empty($layout_options))
                $defaultTemplate = array_merge($defaultTemplate, $layout_options);
            if (!empty($layout_options['EmailTemplate']['to_email'])) {
                $emails = explode(",", $layout_options['EmailTemplate']['to_email']);
            } else {
                $emails = array();
            }

            $result = $this->SysEmails->order($purchase_orders['PurchaseOrder'], $purchase_orders['Supplier'], $owner, $defaultTemplate, $purchase_orders['PurchaseOrderDocument'], false, $emails,$purchase_orders['Attachments']);
            if ($result) {
                $this->flashMessage(__('Purchase Refund has been saved & sent', true), 'Sucmessage');
                $this->PurchaseOrder->save(array('PurchaseOrder' => array(
                    'id' => $purchase_orders['PurchaseOrder']['id'],
                    'draft' => 0,
                    'last_sent' => date('Y-m-d')
                )), array('callbacks' => false, 'validation' => false, 'fieldList' => array('id', 'draft', 'last_sent')));
            } else {
                $this->flashMessage(sprintf(__('Failed to send the Purchase Refund, %s', true),$this->SysEmails->error_message), 'Errormessage', 'secondaryMessage');
            }
            return $result;
        }else{
            $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-orders');

            if (!empty($layout_options))
                $defaultTemplate = array_merge($defaultTemplate, $layout_options);
            if (!empty($layout_options['EmailTemplate']['to_email'])) {
                $emails = explode(",", $layout_options['EmailTemplate']['to_email']);
            } else {
                $emails = array();
            }

            $result = $this->SysEmails->order($purchase_orders['PurchaseOrder'], $purchase_orders['Supplier'], $owner, $defaultTemplate, $purchase_orders['PurchaseOrderDocument'], false, $emails,$purchase_orders['Attachments']);
            if ($result) {
                $this->flashMessage(__('Purchase Invoice has been saved & sent', true), 'Sucmessage');
                $this->PurchaseOrder->save(array('PurchaseOrder' => array(
                    'id' => $purchase_orders['PurchaseOrder']['id'],
                    'draft' => 0,
                    'last_sent' => date('Y-m-d')
                )), array('callbacks' => false, 'validation' => false, 'fieldList' => array('id', 'draft', 'last_sent')));
            } else {
                $this->flashMessage(sprintf(__('Failed to send the %s, %s', true), __('Purchase Invoice', true), $this->SysEmails->error_message), 'Errormessage', 'secondaryMessage');
            }
            return $result;
        }
    }

    function owner_autocomplete() {
        Configure::write('debug', 0);
        if (!$this->RequestHandler->isAjax()) {
            $this->redirect(array('action' => ' index'));
        }

        if (empty($this->params['url']['term'])) {
            die(json_encode(array()));
        }
        $keywords = trim($this->params['url']['term']);
        $dbNos = $this->PurchaseOrder->find('all', array('limit' => 15, 'conditions' => array('PurchaseOrder.type' => 0,
            'OR' => array(
                'PurchaseOrder.supplier_business_name LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_first_name LIKE' => '%' . $keywords . '\'%',
                'PurchaseOrder.supplier_last_name LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_email LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.no LIKE' => $keywords,
                'CONCAT(supplier_first_name,\' \',supplier_last_name) ' => '%' . $keywords . '%'
            )), 'recursive' => -1, 'fields' => 'PurchaseOrder.id, PurchaseOrder.no, PurchaseOrder.date, PurchaseOrder.supplier_business_name, PurchaseOrder.supplier_first_name,  PurchaseOrder.supplier_last_name', 'order' => 'PurchaseOrder.no, PurchaseOrder.date DESC, PurchaseOrder.supplier_first_name, PurchaseOrder.supplier_last_name'));
        $nos = array();
        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getCurrentSite('date_format')];
        foreach ($dbNos as $purchase_orders) {
            $nos[] = array('label' => $purchase_orders['PurchaseOrder']['no'] . ' - ' . $purchase_orders['PurchaseOrder']['supplier_business_name'] . ' ' . (!empty($purchase_orders['PurchaseOrder']['supplier_first_name']) ? '(' . $purchase_orders['PurchaseOrder']['supplier_first_name'] . ' ' . $purchase_orders['PurchaseOrder']['supplier_last_name'] . ')' : '') . ' - ' . date($ownerFormat, strtotime($purchase_orders['PurchaseOrder']['date'])), 'value' => $purchase_orders['PurchaseOrder']['id']);
        }

        die(json_encode($nos));
    }

    //--------------------------------
    function owner_preview($id = false, $alt_template_id = false) {
        App::import('Vendor', 'settings');
        $this->set('alt_template_id', $alt_template_id);
        $po_setting =  ( settings::formData(InventoryPlugin)) ;
        $this->set('po_setting',$po_setting);
        if (!empty($this->data)) {
            if (!empty($this->data['PurchaseOrder']['id'])) {

                $oldPurchaseOrder = $this->PurchaseOrder->getPurchaseOrder($this->data['PurchaseOrder']['id']);
                $this->data['PurchaseOrder'] = is_array($oldPurchaseOrder['PurchaseOrder'])? array_merge($oldPurchaseOrder['PurchaseOrder'], $this->data['PurchaseOrder']):null;
            } else {

                $this->data['PurchaseOrder'] = array_merge($this->data['PurchaseOrder'], array('paid' => 0.0, 'payment_status' => 0));
            }

            $purchase_orders = $this->PurchaseOrder->prepareForView($this->data);
        } else {
            $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => array(6,0, 2,PurchaseOrder::DEBIT_NOTE, PurchaseOrder::CREDIT_NOTE)) , true );
            if (!$purchase_orders) {
                $this->flashMessage(__('Purchase Invoice not found', true));
                $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
            }

        }

        $row=$this->PurchaseOrder->Supplier->read(null,$purchase_orders['PurchaseOrder']['supplier_id']);

        $purchase_orders['Supplier']=$row['Supplier'];
        $this->set('purchase_orders', $purchase_orders);
        $tmp = getCurrentSite('purchaseorder_default_title');


        if (!empty($tmp)) {
            $purchase_orders_default_title = $tmp;
        } else {
            $purchase_orders_default_title = __('Purchase Invoice', true);
        }

        $this->set('purchaseorder_default_title', $purchase_orders_default_title);


        $supplierCountry = getCountry($purchase_orders['PurchaseOrder']['supplier_country_code']);
        $supplierCountry = $supplierCountry['country'];
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = getCountry(getCurrentSite('country_code'));
        $this->set('ownerCountry', $ownerCountry['country']);

        $this->loadModel('Store');
        $stores = $this->Store->get_all_store_list();
        $this->set('stores', $stores);

//		$this->_setBranded();

        $this->set('taxes', $taxes = $this->PurchaseOrder->PurchaseOrderTax->getPurchaseOrderTaxList($id));
        $this->set("show_discount", $purchase_orders['show_discount']);
        $this->_getLayout($alt_template_id ?: $purchase_orders['PurchaseOrder']['invoice_layout_id']);

        $this->layout = '';

        $isPurchaseInvoiceItemAccountEnabled = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);

        if(!empty($isPurchaseInvoiceItemAccountEnabled)) {
            $salesCostAccountIds = array_filter(Set::extract('{n}.sales_cost_account_id', $purchase_orders['PurchaseOrderItem']));
            $this->loadModel('JournalAccount');
            $this->set('invoiceItemAccounts', $this->JournalAccount->find('list', ['conditions' => ['JournalAccount.id' => $salesCostAccountIds]]));
        }

       $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
       if ($enablePurchaseInvoiceItemCostCenterDistribution) {
            $invoiceItemCostCenters = array_filter(Set::extract('{n}.cost_center_id', $purchase_orders['PurchaseOrderItem']));
            $this->loadModel('CostCenter');
            $this->set('invoiceItemCostCenters', $this->CostCenter->find('list', ['conditions' => ['CostCenter.id' => $invoiceItemCostCenters]]));

       }
       
    }

    //--------------------------------
    function preview($id = false) {
        App::import('Vendor', 'settings');
        $po_setting =  ( settings::formData(InventoryPlugin)) ;
        $this->set('po_setting',$po_setting);

        if (!empty($this->data)) {

            $this->data['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_INVOICE;
            if (!empty($this->data['PurchaseOrder']['id'])) {
                $oldPurchaseOrder = $this->PurchaseOrder->getPurchaseOrder($this->data['PurchaseOrder']['id']);

                $this->data['PurchaseOrder'] = array_merge($oldPurchaseOrder['PurchaseOrder'], $this->data['PurchaseOrder']);
            } else {
                $this->data['PurchaseOrder'] = array_merge($this->data['PurchaseOrder'], array('paid' => 0.0, 'payment_status' => 0));
            }

            $purchase_orders = $this->PurchaseOrder->prepareForView($this->data);
        } else {
            $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, []);

            if (!$purchase_orders) {
                $this->flashMessage(__('Purchase Invoice not found', true));
                $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
            }

        }

        $this->set('purchase_orders', $purchase_orders);
        $tmp = getCurrentSite('purchaseorder_default_title');


        if (!empty($tmp)) {
            $purchase_orders_default_title = $tmp;
        } else {
            $purchase_orders_default_title = __('Purchase Invoice', true);
        }

        $this->set('purchaseorder_default_title', $purchase_orders_default_title);

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Store');
        $stores = $this->Store->get_all_store_list();
        $this->set('stores', $stores);

//		$this->_setBranded();

        $this->set('taxes', $taxes = $this->PurchaseOrder->PurchaseOrderTax->getPurchaseOrderTaxList($id));

        $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id']);

        $this->layout = '';
    }

    function _getLayout($layout_id = false, $site_id = false, $purchase_refund = false) {

        $this->loadModel('InvoiceLayout');
        $layout = false;
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }
        if ($layout_id && $layout_id != '-1') {
            $layout = $this->InvoiceLayout->find(array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.id' => $layout_id));
        } else if (!$purchase_refund) {
            $layout = $this->InvoiceLayout->find(array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default' => 1));
        } else if ($purchase_refund) {
            $layout = $this->InvoiceLayout->find(array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default_purchase_refund' => 1));
        }

        if (!empty($layout) && !empty($layout['InvoiceLayout']['bug_v']) && $layout['InvoiceLayout']['bug_v'] == 3) {

            $layout = false;
            debug('BUG_V');
        }

        if ($layout_id == -1) {
            $layout = -1;
        }


        $this->set('layout', $layout);

        if (!empty($layout)) {
            if ($layout['InvoiceLayout']['template_id']) {
                $parentLayout = $this->InvoiceLayout->findById($layout['InvoiceLayout']['template_id']);
            } else {
                $parentLayout = $layout;
            }
            $this->set(compact('parentLayout'));
        }

        return $layout;
    }

    function owner_print_log($id = null) {
        $re_read_po = $this->PurchaseOrder->read(null, $id);
        $this->add_actionline(ACTION_PRINT_PURCHASE_ORDER, array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param4' => $re_read_po['PurchaseOrder']['no']));
        die();
    }


    function owner_print($type, $purchase_invoice_id) {
        $this->loadModel('Staff');
        $this->loadModel('Country');
        $this->loadModel('Product');
        $this->loadModel('Store');
        $this->loadModel('PurchaseOrder');

        //get the barcode for this invoice
//        App::import('Vendor', 'OIBarcode', array('file' => 'OIBarcode/OIBarcode.php'));
//        $invoice_barcode = OIBarcode::getBarcodeImage(OIBarcode::getAutoBarcode(INVOICE_BARCODE, $purchase_invoice_id ), [ 'style'=> ['width'=>'20%','height'=>'30px'] ] );
//        $invoice_barcode_number = OIBarcode::getAutoBarcode(INVOICE_BARCODE, $purchase_invoice_id);
//        $this->set('invoice_barcode', $invoice_barcode);
//        $this->set('invoice_barcode_number', $invoice_barcode_number);

        $reread_purchase_invoice = $this->PurchaseOrder->read(null, $purchase_invoice_id);
        if (!in_array($type, ['packing_slip', 'pick_list', 'shipping_label']) || empty($reread_purchase_invoice)) {
            $this->flashMessage(__('Incorrect Request', TRUE));
            $this->redirect('/');
        }
        $purchase_order = $this->PurchaseOrder->prepareForView($reread_purchase_invoice);

        $ownerCountry = $this->Country->get_country_code(getCurrentSite('country_code'));
        $products = $this->Product->find('list', ['fields' => ['product_code', 'name', 'id'], 'conditions' => ['id' => array_column($purchase_order['PurchaseOrderItem'], 'product_id')]]);
        $this->set('store', $this->Store->findById($purchase_order['Invoice']['store_id']));
        $this->set('products', $products);
        $this->set('ownerCountry', $ownerCountry);
        $this->set('clientCountry', $purchase_order ['clientCountry']);
        $this->set('staffs', $this->Staff->getList());
        $this->set('print_type', $type);
        $this->set('purchase_order', $purchase_order);
        $this->set('layout', $this->_getLayout($purchase_order['Invoice']['invoice_layout_id']));
    }
    
    function print_log($id = null) {
        $re_read_po = $this->PurchaseOrder->read(null, $id);
        $this->add_actionline(ACTION_SUPPLIER_PRINT_PO, array('staff_id' => -4, 'primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param4' => $re_read_po['PurchaseOrder']['no']));
        die();
    }

    function owner_make_received($id = null) {
        $purchase_order = $this->PurchaseOrder->getPurchaseOrder($id);
        if (empty($purchase_order)) {
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(Edit_Delete_All_Purchase_Orders) && $purchase_order['PurchaseOrder']['staff_id'] != $staff) || !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $this->validate_open_day($purchase_order['PurchaseOrder']['date']);
        $this->PurchaseOrder->id = $id;


        $tempStatus = $purchase_order['PurchaseOrder']['is_received'];
        $purchase_order['PurchaseOrder']['is_received'] = true; //for validation
        $trackingValidationResult = TrackStockValidator::validate(
            $purchase_order,
            TrackStockUtil::PURCHASE_ORDER,
            TrackStockUtil::getPurchaseOrderValidationType($purchase_order['PurchaseOrder']['type'])
        );
        $purchase_order['PurchaseOrder']['is_received'] = $tempStatus;
        if($trackingValidationResult !== true)
        {
            $this->flashMessage(__('Could not update purchase invoice', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }
        if (!empty($purchase_order['PurchaseOrder']['draft'])) {
            App::import('Vendor', 'AutoNumber');
            if(strpos($purchase_order['PurchaseOrder']['no'], 'draft-') !== false) {
                if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE){
                    $this->PurchaseOrder->saveField('no', \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_CREDIT_NOTE));
                    \AutoNumber::update_auto_serial(\AutoNumber::TYPE_PURCHASE_CREDIT_NOTE);
                }else{
                    $this->PurchaseOrder->saveField('no', \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_INVOICE));
                    \AutoNumber::update_auto_serial(\AutoNumber::TYPE_PURCHASE_INVOICE);
                }
            }
            $this->PurchaseOrder->saveField('draft', false);
            $this->PurchaseOrder->update_payment_journals($id);
        }

        if (empty($purchase_order['PurchaseOrder']['is_received']) or $purchase_order['PurchaseOrder']['is_received'] != 1) {
            $this->PurchaseOrder->saveField('is_received', true);
            $this->PurchaseOrder->saveField('received_date', date('Y-m-d H:i:s'));
        }

        $this->loadModel('StockTransaction');
        StockTransaction::updateForPo($id);

        $re_read_po = $this->PurchaseOrder->read(null, $id);
        debug($re_read_po );
        $this->PurchaseOrder->update_journals($re_read_po);
        $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => empty($re_read_po['PurchaseOrder']['draft']) ? $re_read_po['PurchaseOrder']['is_received'] : -1, 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']);
        $this->add_actionline(ACTION_UPDATE_PO, $arry);



        $this->flashMessage(__('Purchase Invoice has been updated', true), 'Sucmessage');
        if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE){
            $redirect = array('action' => 'view_credit_note', $id);
        }elseif($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund){
            $redirect = array('action' => 'view_refund', $id);
        }elseif($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE){
            $redirect = array('action' => 'view_debit_note', $id);
        }else{
            $redirect = array('action' => 'view', $id);
        }
        $this->redirect($redirect);
    }

    function owner_make_unreceived($id = null) {
        $purchase_order = $this->PurchaseOrder->getPurchaseOrder($id);
        if (empty($purchase_order)) {
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(Edit_Delete_All_Purchase_Orders) && $purchase_order['PurchaseOrder']['staff_id'] != $staff) || !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }
        $this->validate_open_day($purchase_order['PurchaseOrder']['date']);
        $store_balance = true ;
        if ( settings::getValue(InventoryPlugin, 'disable_overdraft')&& ifPluginActive(InventoryPlugin)  )
        {
            $po = $purchase_order ;
            $map_products = [] ;
            foreach ( $po['PurchaseOrderItem'] as $k  ) {
                $map_products[$k['product_id']] = $k;
            }
            $StockTransaction = GetObjectOrLoadModel('StockTransaction');
            if(
                ifPluginActive(InventoryPlugin) &&
                !$po['PurchaseOrder']['draft'] &&
                !settings::getValue(InventoryPlugin, 'enable_requisitions_po') &&
                settings::getValue(InventoryPlugin, 'disable_overdraft')
            ) {
                $newTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($po['PurchaseOrder']['store_id'], $po['PurchaseOrderItem']);
                foreach ($newTransactionsProductStoreQuantities as $productId => $productStoreQuantities) {
                    foreach ($productStoreQuantities as $storeId => $quantity) {
                        if(!$StockTransaction->check_balance_open($productId, $storeId, $quantity, 0, 'deduct')) {
                            $this->flashMessage(__('This quantity already has a transaction', true), 'Errormessage', 'secondaryMessage');
                            $this->redirect (Router::url ( ['action' => 'view' , $id] )) ;die ;
                        }
                    }
                }
            }
        }

        $this->PurchaseOrder->id = $id;
        $purchase_order['PurchaseOrder']['draft'] = false;
        $trackingValidationResult = TrackStockValidator::validate(
            $purchase_order,
            TrackStockUtil::PURCHASE_ORDER,
            TrackStockUtil::getPurchaseOrderValidationType($purchase_order['PurchaseOrder']['type']),
            true
        );
        if($trackingValidationResult !== true)
        {
            $this->flashMessage(__('Could not update purchase invoice', true));
            $this->referer('/v2/owner/entity/purchase_order/list', true);
        }

        if (!empty($purchase_order['PurchaseOrder']['draft'])) {
            $this->PurchaseOrder->saveField('draft', false);
            $this->PurchaseOrder->update_payment_journals($id);
        }

        if (!empty($purchase_order['PurchaseOrder']['is_received'])) {
            $this->PurchaseOrder->saveField('is_received', false);
            $this->PurchaseOrder->saveField('ignored_date', date('Y-m-d H:i:s'));
        }

        $re_read_po = $this->PurchaseOrder->read(null, $id);
        $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => empty($re_read_po['PurchaseOrder']['draft']) ? $re_read_po['PurchaseOrder']['is_received'] : -1, 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']);
        $this->add_actionline(ACTION_UPDATE_PO, $arry);

        $this->loadModel('StockTransaction');
        StockTransaction::updateForPo($id);

        $this->PurchaseOrder->update_journals($re_read_po);

        $this->flashMessage(__('Purchase Invoice has been updated', true), 'Sucmessage');
        $redirect = array('action' => 'view', $id);

        if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund){
            $redirect = array('action' => 'view_refund', $id);
        }elseif($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE){
            $redirect = array('action' => 'view_debit_note', $id);
        }


        $this->redirect($redirect);
    }

    function owner_make_draft($id = null) {
        $purchase_order = $this->PurchaseOrder->getPurchaseOrder($id);
        if (empty($purchase_order)) {
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(Edit_Delete_All_Purchase_Orders) && $purchase_order['PurchaseOrder']['staff_id'] != $staff) || !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }
        $trackingValidationResult = TrackStockValidator::validate(
            $purchase_order,
            TrackStockUtil::PURCHASE_ORDER,
            TrackStockUtil::getPurchaseOrderValidationType($purchase_order['PurchaseOrder']['type']),
            true
        );
        if($trackingValidationResult !== true)
        {
            $this->redirect($this->referer());

        }
        $this->PurchaseOrder->id = $id;
        $this->validate_open_day($purchase_order['PurchaseOrder']['date']);
        if (empty($purchase_order['PurchaseOrder']['draft'])) {
            $this->PurchaseOrder->saveField('draft', true);
            $this->PurchaseOrder->update_payment_journals($id);
        }

        if (!empty($purchase_order['PurchaseOrder']['is_received'])) {
            $this->PurchaseOrder->saveField('is_received', false);
            $this->PurchaseOrder->saveField('ignored_date', date('Y-m-d H:i:s'));
        }
        //TODO handle stock transactions update and requisitions update
        $re_read_po = $this->PurchaseOrder->read(null, $id);
        $this->PurchaseOrder->update_journals($re_read_po);
        $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => empty($re_read_po['PurchaseOrder']['draft']) ? $re_read_po['PurchaseOrder']['is_received'] : -1, 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']);
        $this->add_actionline(ACTION_UPDATE_PO, $arry);

        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        if ($enable_requisitions) {
            $this->loadModel('Requisition');
            $this->Requisition->updateForOrder($re_read_po, Requisition::ORDER_TYPE_PURCHASE_ORDER, false, false);
        } else if (ifPluginActive(InventoryPlugin)) {
            $this->loadModel('StockTransaction');
            StockTransaction::updateForPo($id);
        }

        $this->flashMessage(__('Purchase Invoice has been updated', true), 'Sucmessage');
        $redirect = array('action' => 'view', $id);

        if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund){
            $redirect = array('action' => 'view_refund', $id);
        }elseif($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE){
            $redirect = array('action' => 'view_debit_note', $id);
        }

        $this->redirect($redirect);
    }
    function owner_docs($id=null){
        $this->_settings();
        $PurchaseOrder = $this->PurchaseOrder->getPurchaseOrder($id);
        $this->set('HtmlModel', 'PurchaseOrderDocument');      
        $this->data=$PurchaseOrder;

    }
    function owner_add_payment($id = false) {
        $this->loadModel('PurchaseOrderPayment');
        $purchaseorder = $this->PurchaseOrder->read(null, $id);

        if (!$purchaseorder) {
            $this->flashMessage(__('Purchase invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list'));
        }


        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');

            if (!check_permission(Edit_Delete_his_own_created_Purchase_Orders) || (!check_permission(Edit_Delete_All_Purchase_Orders) && $purchaseorder['PurchaseOrder']['staff_id'] != $staff)) {
                $this->flashMessage(__("You are not allowed to edit this Purchase invoice ", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }

        }
        /*  if ($invoice['Invoice']['payment_status'] == 2 || $invoice['Invoice']['summary_unpaid'] == 0) {
          $this->flashMessage(__('Invoice is alreay paid', true), 'Notemessage');
          $this->redirect($this->referer(array('action' => 'index')));
          } */

        if (!empty($this->data)) {
            if ($purchaseorder['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund) {
                $this->data['PurchaseOrderPayment']['amount'] = abs($this->data['PurchaseOrderPayment']['amount']) * -1;
            }
            $this->data['PurchaseOrderPayment']['purchase_order_id'] = $id;

            if (empty($this->data['PurchaseOrderPayment']['staff_id']) || !check_permission(Edit_Delete_All_Purchase_Orders))
                $this->data['PurchaseOrderPayment']['staff_id'] = $owner['staff_id'];

            $this->validateOpenDayWithValidationError($this->data, 'PurchaseOrderPayment', 'date');
            if(empty($this->PurchaseOrderPayment->validationErrors))
            {
                $attachments = explode(',',$this->data['PurchaseOrderPayment']['attachment']);
                unset($this->data['PurchaseOrderPayment']['attachment']);
                $result = $this->PurchaseOrder->ownerAddPayment($this->data);
  
                if(!empty($attachments))
                {
                    izam_resolve(AttachmentsService::class)->save('purchase_order_payment', $result['payment_id'], $attachments); 
                }
            }else{
                $result['status'] = false;
            }

            if ($result['status']) {
                if ('1' === $purchaseorder['PurchaseOrder']['draft']) {
                    $this->PurchaseOrder->updatePurchaseOrderAfterPayment($purchaseorder['PurchaseOrder']['id']);
                }
                $purchase_order_payment = $this->PurchaseOrderPayment->read(null, $result['payment_id']);
                $action_line_type=ACTION_ADD_PO_PAYMENT;
                if($purchaseorder['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE){
                    $action_line_type=ACTION_ADD_PCN_PAYMENT;
                }
                //(primary_id => invoice_id, sec => client_id, p1=>total, p2=> invoice_status, p3=>invoice_summary_paid, p4=>payment_id, p5=>payment_amount, p6=>payment_status, p7=>payment_method )                
                $this->add_actionline($action_line_type, array('primary_id' => $purchase_order_payment['PurchaseOrder']['id'], 'secondary_id' => $purchase_order_payment['PurchaseOrder']['client_id'], 'param1' => $purchase_order_payment['PurchaseOrderPayment']['amount'], 'param2' => empty($purchase_order_payment['PurchaseOrder']['draft']) ? $purchase_order_payment['PurchaseOrder']['payment_status'] : -1, 'param3' => $purchase_order_payment['PurchaseOrder']['summary_paid'], 'param4' => $purchase_order_payment['PurchaseOrder']['no'], 'param5' => $purchase_order_payment['PurchaseOrderPayment']['id'], 'param6' => $purchase_order_payment['PurchaseOrder']['summary_total'], 'param7' => $purchase_order_payment['PurchaseOrderPayment']['status'], 'param8' => $purchase_order_payment['PurchaseOrderPayment']['payment_method'], 'param9' => $purchase_order_payment['PurchaseOrderPayment']['transaction_id']));

                if($purchaseorder['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund) {

                    $this->flashMessage(__('The payment has been added to the Purchase refund', true), 'Sucmessage');

                    $this->redirect(array('action' => 'view_refund', $id));
                }else if($purchaseorder['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE) {

                    $this->flashMessage(__('The payment has been added to the Purchase Credit Note', true), 'Sucmessage');

                    $this->redirect(array('action' => 'view_credit_note', $id));
                } else {

                    $this->flashMessage(__('The payment has been added to the Purchase invoice', true), 'Sucmessage');

                    $this->redirect(array('action' => 'view', $id));
                }

            } else {
                //  print_r($result['here']);
                $this->flashMessage(__('Could not add payment ' . $result['error_message'], true));
            }
        } else {

            $formats = getDateFormats('std');
            $this->data['PurchaseOrderPayment']['date'] = format_date(date("Y-m-d"));
            $this->data['PurchaseOrderPayment']['status'] = 1;
            $this->data['PurchaseOrderPayment']['staff_id'] = $owner['staff_id'];
            if ($purchaseorder['PurchaseOrder']['summary_deposit'] && $purchaseorder['PurchaseOrder']['summary_deposit'] <= $purchaseorder['PurchaseOrder']['summary_unpaid']) {
                $this->data['PurchaseOrderPayment']['amount'] = $purchaseorder['PurchaseOrder']['summary_deposit'];
            } else {
                $this->data['PurchaseOrderPayment']['amount'] = $purchaseorder['PurchaseOrder']['summary_unpaid'];
            }
            $this->data['PurchaseOrderPayment']['amount'] = round($this->data['PurchaseOrderPayment']['amount'], CurrencyHelper::getFraction($purchaseorder['PurchaseOrder']['currency_code']));
        }

        if($purchaseorder['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund) {
            $this->data['PurchaseOrderPayment']['amount'] *= -1;
        } 

        $this->loadModel('SitePaymentGateway');
        $this->set('payment_treasury',$payment_treasury=$this->SitePaymentGateway->PaymentTreasury());
        $this->set('staffs', $this->PurchaseOrderPayment->Staff->getList());
        $this->set('statuses', PurchaseOrderPayment::getPaymentStatus());
        $this->loadModel('SitePaymentGateway');
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));
        unset($paymentMethods['client_credit']);
        if(!settings::getValue(InventoryPlugin, 'automatic_pay_po')){
            $supplier_credit = $this->PurchaseOrder->Supplier->supplier_credit($purchaseorder['PurchaseOrder']['supplier_id'], $purchaseorder['PurchaseOrder']['currency_code']);

            if ($supplier_credit <= 0) {
                unset($paymentMethods['supplier_credit']);
            } else {
                $this->set('supplier_credit', $supplier_credit);
                unset($paymentMethods['supplier_credit']);
                $paymentMethods['supplier_credit'] = sprintf(__('Supplier Credit (%s)', true), $supplier_credit);
            }
        }

        $this->set('paymentMethods', $paymentMethods);
        $Stripe = $this->SitePaymentGateway->find('first', array('conditions' => array('payment_gateway' => 'stripe')));
        $this->set('Stripe', $Stripe);
        $this->set(compact('purchaseorder'));

        $this->set('file_settings', $this->PurchaseOrder->PurchaseOrderPayment->getFileSettings());

        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'ItemPermission');
            $this->loadModel ( 'Treasury');
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW) ) ;

            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        $this->set('content', $this->get_snippet('add-payment-purchase-orders'));

        $this->set('title_for_layout',  __('Add Payment', true));
    }
    function owner_delete_n ( $id  ){
        $conditions = array('PurchaseOrder.id' => $id);

        var_dump($this->PurchaseOrder->delete_related_items ( $id )) ;
//        $purchase_orders = $this->PurchaseOrder->find('first', array('conditions' => $conditions));
//        if (ifPluginActive(InventoryPlugin)) {
//            StockTransaction::updateForPo($purchase_orders);
//        }
        die ;
    }

    function recalculate_po()
    {
        $po_ids=array(142,143,147,148,149,150,152,186,207,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224);
        foreach($po_ids as $id)
        {
            $data=$this->PurchaseOrder->findById($id);
            $this->PurchaseOrder->updatePurchaseOrder($data);
        }
    }

    public function loadCostCentersListViewVars()
    {
        if (ifPluginActive(AccountingPlugin)) {
            $this->loadModel('CostCenter');
            $cost_centers_list = $this->CostCenter->find('list', [
                'conditions' => [
                    'CostCenter.is_primary' => 0
                ],
                'fields' => ['id', 'name'],
                'order' => ['CostCenter.name' => 'ASC']
            ]);

            if (count($cost_centers_list) > 0) {
                $this->set('cost_centers_list', $cost_centers_list);
            }
        }
    }

    private function _formCommon($id = null) {
        $this->loadModel('Journal');
        $this->loadModel('JournalAccountRoute');
        $this->loadModel('Store');
        $this->loadModel('ItemPermission');
        $this->set('primary_store', $this->Store->getPrimaryStore(ItemPermission::PERMISSION_INVOICING));
        $this->set('bnf',Localize::get_business_number_fields());
        $this->set('more_than_1_store', \App\Helpers\Common\PurchaseOrderAndInvoiceHelper::invoiceHasMoreThanOneStore($this->data['PurchaseOrder']['store_id'], $this->data['PurchaseOrderItem']));

        // Cost Centers section - load only if chart of accounts plugin is active and user has cost centers
        $this->loadCostCentersListViewVars();

        $adjustment_routing = settings::getValue(AccountingPlugin , "adjustment_accounts_routing") ;
        if($adjustment_routing == Settings::MANUAL_ACCOUNTS_ROUTING){
            $this->loadModel('Journal');
            $this->loadModel("JournalAccountRoute");
            $this->set('adjustmentRoutedAccountEntityType',Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE);
            if($id) {
                $this->set('adjustmentRoutedAccountEntityId',$id);
                $accountRoute = $this->JournalAccountRoute->Journal->get_auto_account(['entity_type' => Journal::PURCHASES_ADJUSTMENT_ACCOUNT_ENTITY_TYPE, 'entity_id'=> $id]);
                $this->set('adjustmentAccountRoute', $accountRoute);
            }
        }
    }

    private function checkPurchaseInvoicesHasProductOrService($purchase_orderss): void
    {
        $PurchaseOrderIds = array_column(array_column($purchase_orderss, 'PurchaseOrder'), 'id');
        $PurchaseOrderIds = implode(',', $PurchaseOrderIds);

        $PurchaseOrderItems = GetObjectOrLoadModel('PurchaseOrderItem');
        $PurchaseOrderItemsData = $PurchaseOrderItems->find('all',
            ['conditions' => [
                "PurchaseOrderItem.purchase_order_id in ($PurchaseOrderIds)",
                "Product.type <> 2",
            ],
                'group' => ['PurchaseOrderItem.purchase_order_id'],
                'fields' => ['PurchaseOrderItem.purchase_order_id'],
            ]);
        if ($PurchaseOrderItemsData) {
            $PurchaseOrderIdHasProduct = (array_column(array_column($PurchaseOrderItemsData, 'PurchaseOrderItem'), 'purchase_order_id'));
            foreach ($purchase_orderss as &$purchase_orders) {
                if (in_array($purchase_orders['PurchaseOrder']['id'], $PurchaseOrderIdHasProduct)) {
                    $purchase_orders['PurchaseOrder']['hasProduct'] = true;
                    continue;
                }
                $purchase_orders['PurchaseOrder']['hasProduct'] = false;
            }
        }
    }

    function owner_over_paid($id = false) {
	    set_time_limit(600);
          $purchase_invoice = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => 0) );
         if (!$purchase_invoice) {
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }
        if(isset($_GET['over_paid']) and $_GET['over_paid']=="1"){
        $this->PurchaseOrder->fix_overpaid($id,false,false);
        $this->redirect($this->referer(array('action' => 'view',$id), true));
        }elseif(isset($_GET['over_paid']) and $_GET['over_paid']=="2"){
        $this->PurchaseOrder->fix_overpaid($id,false,true);
        $this->redirect($this->referer(array('action' => 'view',$id), true));
        }else{
        $this->flashMessage(__('Please select an option', true));
            $this->redirect($this->referer(array('action' => 'view',$id), true));
        }
        die();
    }


    function owner_add_credit_note($id = null) {
        $is_purchase_credit_note_enabled = settings::getValue(InventoryPlugin, 'enable_purchase_credit_note');
        if(!$is_purchase_credit_note_enabled) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/purchase_order/list');
        }
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->loadModel('ItemsTag');
        $this->loadModel('SitePaymentGateway');
        App::import('Vendor', 'AutoNumber');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);
        if (!check_permission(Add_New_Purchase_Orders)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/purchase_order/list');
        }
        if(isset($this->params['url']['supplier_id']) && $this->params['url']['supplier_id']!=""){

            $check_client=$this->PurchaseOrder->Supplier->find('first',array('recursive' => -1,'conditions'=>array('Supplier.id'=>$this->params['url']['supplier_id'])));

            if($check_client['Supplier']['suspend']=="1"){

                if(IS_REST) $this->cakeError("error400", ["message"=>'Supplier is suspend']);
                $this->flashMessage(__('Supplier is suspend', true));
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $this->SupplierValidation->validateSupplierSuspended($this->data['Supplier']['id'] ?: $this->data['PurchaseOrder']['supplier_id']);

        $tmp = false;
        if(!empty($id)) {
          $tmp = $this->PurchaseOrder->findById($id);
        }

        if (!empty($this->data)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);

            $this->data['PurchaseOrder']['type'] = PurchaseOrder::CREDIT_NOTE;
            $this->loadModel ( 'Store');
            $this->data['PurchaseOrder']['draft'] = ($this->params['url']['send'] == 'draft');
            $this->data['PurchaseOrder']['subscription_id']=  $id;
            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_CREDIT_NOTE);

            $isAppEntitiesValid = $additionalFieldsFormHandler->validate($this->data);

            foreach ($this->data['PurchaseOrderItem'] as $key => &$item) {
                $item['quantity'] = 1; //Default quantity for credit note
                $item['item'] = $item['description'];
             }

            /**
             * As requested the validation for item should be removed because the item is removed
             */
            unset($this->PurchaseOrder->PurchaseOrderItem->validate['item']);

            $this->PurchaseOrder->PurchaseOrderItem->validate['unit_price']  = array('rule' => 'notEmpty', 'message' => __('Required', true));

            if(count($this->data['PurchaseOrderItem']) == 1 && empty($this->data['PurchaseOrderItem'][0]['description']) && empty($this->data['PurchaseOrderItem'][0]['unit_price'])) {
                $this->PurchaseOrder->validationErrors['PurchaseOrderItem'] = __('You should enter at least one record', true);
            }

            if($isAppEntitiesValid && empty($this->PurchaseOrder->validationErrors))
            {
                $result = $this->PurchaseOrder->addPurchaseOrder($this->data);
            }else{
                $result['status'] = false;
            }
            if ($result['status']) {

                izam_resolve(PurchaseOrderService::class)->insert(ServiceModelDataTransformer::transform($result['data'], 'PurchaseOrder'));
                $this->handleAdjustmentRouting($result);
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $additionalFieldsFormHandler->store($re_read_po['PurchaseOrder']['id'], $this->data);
                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_PURCHASE_CREDIT_NOTE)
                    ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);

                if (IS_REST && !empty($this->data['Payment']) && empty($this->data['Payment']['is_paid'])) {
                    $this->add_api_payments($this->data, $result['data']['PurchaseOrder']['id'], PurchaseOrder::CREDIT_NOTE);
                    unset($this->data['Payment']);
                }

                // ignore anything related to payment if saved as draft
                if (
                    (!empty($this->data['Payment']['is_paid']) || !empty($this->data['Deposit']['is_paid'])) &&
                    !$this->data['PurchaseOrder']['draft']
                ) {
                    if (!empty($this->data['Payment']['is_paid'])) {
                        $k = 'Payment';
                        $amount = $result['data']['PurchaseOrder']['summary_total'];
                    } else {
                        $k = 'Deposit';
                        if ($this->data['PurchaseOrder']['deposit_type'] == 1) {
                            $amount = $this->data['PurchaseOrder']['deposit'];
                        } else {
                            $amount =(float) $re_read_po['PurchaseOrder']['summary_total'] * (float) $this->data['PurchaseOrder']['deposit'] / 100;

                        }
                    }
                    $treasury_id = $this->data['Payment']['treasury_id'];
                    if($k == 'Deposit' && !empty($this->data['Deposit']['is_paid']) && !empty($this->data['Deposit']['payment_method'])) {
                        if (empty(   $this->data['Deposit']['treasury_id'])) {
                           $selectedPaymentMethod = $this->SitePaymentGateway->find('first', ['conditions' => ['SitePaymentGateway.payment_gateway' => $this->data['Deposit']['payment_method'] ]]);
                           if($selectedPaymentMethod && $selectedPaymentMethod['SitePaymentGateway']['treasury_id']) {
                               $is_admin = getAuthOwner('staff_id') == 0 ? true : (bool)getAuthStaff()['Role']['is_super_admin'];
                               if(!$is_admin) {
                                   $this->loadModel('ItemPermission');
                                   $hisTreasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT,null,true) ?: [];
                                   if(isset($hisTreasuries[$selectedPaymentMethod['SitePaymentGateway']['treasury_id']])  ){
                                       $treasury_id = $selectedPaymentMethod['SitePaymentGateway']['treasury_id'];
                                   }
                               }
                               else{
                                   $treasury_id = $selectedPaymentMethod['SitePaymentGateway']['treasury_id'];
                               }

                           }
                       }else {
                         $treasury_id = $this->data['Deposit']['treasury_id'];
                       }
                    }
                    $owner = getAuthOwner();
                    $payment['PurchaseOrderPayment'] = array('treasury_id' => $treasury_id,'purchase_order_id' => $re_read_po['PurchaseOrder']['id'], 'amount' => $amount, 'status' => 1, 'date' => $re_read_po['PurchaseOrder']['date'], 'payment_method' => $this->data[$k]['payment_method'], 'transaction_id' => $this->data[$k]['transaction_id'], 'added_by' => 1, 'staff_id' => $owner['staff_id']);
                    $this->PurchaseOrder->ownerAddPayment($payment);

                }else{
                    $this->PurchaseOrder->Supplier->adjust_balance($re_read_po['PurchaseOrder']['supplier_id'], $re_read_po['PurchaseOrder']['currency_code']);
                    $this->PurchaseOrder->Supplier->pay_pos_from_credit($re_read_po['PurchaseOrder']['supplier_id']);
                }

                $this->PurchaseOrder->recursive = 1;
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number'],'param7' => $re_read_po['PurchaseOrder']['payment_status']);
                $this->add_actionline(ACTION_ADD_PURCHASE_CREDIT_NOTE, $arry);
                $this->PurchaseOrder->update_journals($re_read_po);


                if(IS_REST){
                    $this->set('id', $re_read_po['PurchaseOrder']['id']);
                    $this->render('created');
                    return;
                }

                $this->flashMessage(__('Credit Note Added successfully', true), 'Sucmessage');

                $redirect = array('action' => 'owner_view_credit_note');

                if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                    $this->_directSendEmail($result['data']);
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                    $redirect = array('action' => 'send_to_supplier');
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                    $redirect = array('action' => 'owner_view_credit_note', 'print' => 1);
                }



                $redirect[] = $result['data']['PurchaseOrder']['id'];

                $this->flashMessage(__('Credit Note Added successfully', true), 'Sucmessage');

                $this->redirect($redirect);
            } else {
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);
                $this->flashMessage(__('Could not save purchase credit note', true));
                foreach($this->PurchaseOrder->validationErrors as $error){
                    if(is_array($error))
                    {
                        //because if error is array that means its an invoice item error
                        //and invoice item errors will show in each of its rows as
                        //it dosent make sense to show them in the head of page area
                        continue ;
                    }
                    $multi_errors[]=$error;
                }
                if(!empty($multi_errors)) {

                    CustomValidationFlash($multi_errors);
                }
                if (empty($_SERVER['HTTP_REFERER']) || strpos($_SERVER['HTTP_REFERER'], Router::url( ['controller' => 'purchase_invoices' , 'action' => 'add'])) === false  ) {
                    $this->redirect($this->referer());
                }
            }
        } else {
            $formats = getDateFormats('std');
            $this->data['PurchaseOrder']['received_date'] = format_date(date("Y-m-d")) . date(' H:i');
            $this->data['PurchaseOrder']['date'] = format_date(date("Y-m-d"));
            if ($id) {
                $template = $this->PurchaseOrder->getTemplate($id);
                if (!empty($template)) {
                    unset($template['PurchaseOrder']['is_offline']);
                    $template['PurchaseOrder']['date'] = $template['PurchaseOrder']['issue_date'] = format_date(date("Y-m-d"));
                    $this->data = $template;
                }
            }

            if (empty($this->data['PurchaseOrder']['currency_code'])) {
                $this->data['PurchaseOrder']['currency_code'] = getAuthOwner('currency_code');
            }
            if (ifPluginActive(WorkflowPlugin)&& !empty ($_GET['work_order_id']) && !empty ($_GET['type_entity_key']) && empty($this->data['PurchaseOrder']['work_order_id'])) {
                $this->data['PurchaseOrder']['work_order_id'] = $_GET['work_order_id'];
            }

            if (empty($this->data['PurchaseOrder']['language_id'])) {
                $this->data['PurchaseOrder']['language_id'] = getAuthOwner('language_code');
            }
            if ( !empty($this->params['url']['supplier_id'])) {
                $this->data['PurchaseOrder']['supplier_id'] = intval($this->params['url']['supplier_id'] );
            }
            if (empty($this->data['PurchaseOrder']['date_format'])) {
                $this->data['PurchaseOrder']['date_format'] = getCurrentSite('date_format');
            }

            if (empty($this->data['PurchaseOrder']['issue_date'])) {
                $formats = getDateFormats(null);
                $this->data['PurchaseOrder']['issue_date'] = strftime($formats[getCurrentSite('date_format')], time());
            }

            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_CREDIT_NOTE);
            // Only Copy InvoiceItem description
            $items = $this->data['PurchaseOrderItem'];
            unset($this->data['PurchaseOrderItem']);
            unset($this->data['Attachments']);
            foreach($items as $key => $item){
                $this->data['PurchaseOrderItem'][$key]['description'] = $item['description'] ?? '';
            }

            $clearValues = ['discount_amount', 'discount','adjustment_label','adjustment_value', 'html_notes','shipping_amount','shipping_options'];

            foreach($clearValues as $key){
                unset($this->data['PurchaseOrder'][$key]);
            }
        }
        if (is_array($this->Session->read('purchaseorder_items'))) {
            $this->data['PurchaseOrder']['requisition_ids'] = $this->Session->read('requisition_ids');
            $more = $this->Session->read('purchaseorder_items');
            $this->data['PurchaseOrderItem'] = $more['PurchaseOrderItem'];
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
            $this->Session->delete('requisition_ids');
            $this->Session->delete('purchaseorder_items');
        }
        if ($this->Session->read('purchaseorder_type') == 1) {
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
            $this->Session->delete('purchaseorder_type');
        }
        $this->set('po_always_paid',  $po_always_paid= settings::getValue(InventoryPlugin, 'po_always_paid'));
        $this->set('is_received',  $is_received= settings::getValue(InventoryPlugin, 'po_received'));

        if($po_always_paid)
            $this->data['Payment']['is_paid']=1;

        if($is_received)
            $this->data['PurchaseOrder']['is_received']=1;

        $this->_settings($id);
        $this->_formCommon();


        $this->set('content', $this->get_snippet('create-purchaseorder'));

        $this->set('title_for_layout',  __('New Purchase Credit Note', true));



        $this->loadModel('ItemPermission');
        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'Treasury');
            $treasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW);
            $payment_treasury = $this->SitePaymentGateway->PaymentTreasury();
            $payment_treasury = array_filter($payment_treasury, fn ($treasury_id) => in_array($treasury_id, array_keys($treasuries)));

            $this->set('payment_treasury',$payment_treasury);
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW) ) ;
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        $this->loadModel ( 'Store');

        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) );
        $this->set ( 'primaryStore' , $this->Store->getPrimaryStore (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) ) ;
        $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id')));
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));

        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang('new-purchaseorder'));
        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));
        $this->set('is_credit_note',true);
        $this->set('HtmlModel', 'PurchaseOrderDocument');
        $this->set('subscription', $tmp);
        $this->render('owner_add');
    }

    //-----------------------------------------
    function owner_edit_credit_note($id = null) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);
        $owner = getAuthOwner();
        $this->PurchaseOrder->bindAttachmentRelation('purchase_order');
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id);
        $old_draft=$purchase_orders['PurchaseOrder']['draft'];
        if (!$purchase_orders) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Purchase Credit Note not found', true)));
            $this->flashMessage(__('Purchase Credit Note not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        $this->loadModel('Tax');
        $this->set('changed_tax',$this->Tax->tax_compare($id,'purchase_order'));
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(Edit_Delete_All_Purchase_Orders) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to Edit this Purchase Credit note", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $this->validate_open_day($purchase_orders['PurchaseOrder']['date']);

        $this->validateItemIsNotLocked(
            $id, PurchaseOrder::CREDIT_NOTE, 'Purchase Credit Note', Router::url(['action' => 'owner_view_credit_note', $id])
        );

        if(IS_REST) $this->data['PurchaseOrder']['id'] = $id;


        if (!empty($this->data)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);

            $this->data['PurchaseOrder']['draft'] = $this->params['url']['send'] == 'draft';
            $this->data['PurchaseOrder']['type'] = PurchaseOrder::CREDIT_NOTE;

            unset($this->PurchaseOrder->PurchaseOrderItem->validate['item']);

            $this->PurchaseOrder->PurchaseOrderItem->validate['unit_price']  = array('rule' => 'notEmpty', 'message' => __('Required', true));

            if(count($this->data['PurchaseOrderItem']) == 1 && empty($this->data['PurchaseOrderItem'][0]['description']) && empty($this->data['PurchaseOrderItem'][0]['unit_price'])) {
                $this->PurchaseOrder->validationErrors['PurchaseOrderItem'] = __('You should enter at least one record', true);
            }

            foreach ($this->data['PurchaseOrderItem'] as $key => &$item) {
                $item['quantity'] = 1; //Default quantity for credit note
                $item['item'] = $item['description'];
             }

            if($additionalFieldsFormHandler->validate($this->data) && empty($this->PurchaseOrder->validationErrors))
            {
                $result = $this->PurchaseOrder->updatePurchaseOrder($this->data);
            }else{
                $result['status'] = false;
            }

            $errors  = $this->PurchaseOrder->validationErrors;
            $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
            $re_read_po['PurchaseOrder']['date'] = format_datetime($re_read_po['PurchaseOrder']['date']);
            $this->PurchaseOrder->validationErrors = $errors;
            $new_draft=$re_read_po['PurchaseOrder']['draft'];

            if ($result['status']) {
                $additionalFieldsFormHandler->update($id, $this->data);
                izam_resolve(PurchaseOrderService::class)->update(ServiceModelDataTransformer::transform($result['data'],'PurchaseOrder'));
                $this->handleAdjustmentRouting($result);
                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_PURCHASE_CREDIT_NOTE)
                    ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);

                $this->PurchaseOrder->Supplier->adjust_balance($re_read_po['PurchaseOrder']['supplier_id'], $re_read_po['PurchaseOrder']['currency_code']);
                $this->PurchaseOrder->Supplier->pay_pos_from_credit($re_read_po['PurchaseOrder']['supplier_id']);
                $this->PurchaseOrder->updatePurchaseOrderPayments($result['data']['PurchaseOrder']['id']);
                $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']);
                $this->add_actionline(ACTION_UPDATE_PURCHASE_CREDIT_NOTE, $arry);
                $this->PurchaseOrder->update_journals($result);
                if($purchase_orders['PurchaseOrder']['currency_code']!=$re_read_po['PurchaseOrder']['currency_code']
                    ||strtotime($purchase_orders['PurchaseOrder']['date'])!=strtotime($re_read_po['PurchaseOrder']['date']))
                {
                    $this->loadModel('PurchaseOrderPayment');
                    $PurchaseOrderPayments = $this->PurchaseOrderPayment->find('all', array('conditions' => array('PurchaseOrderPayment.purchase_order_id' => $id)));
                    foreach($PurchaseOrderPayments  as $payment)
                    {
                        $payment[$this->PurchaseOrderPayment->alias]['currency_code']=$re_read_po['PurchaseOrder']['currency_code'];
                        $this->PurchaseOrderPayment->save($payment);
                    }
                }

                if($old_draft!=$new_draft || $purchase_orders['PurchaseOrder']['supplier_id'] !== $this->data['PurchaseOrder']['supplier_id'])
                {
                    $this->PurchaseOrder->update_payment_journals($id);
                }
                if(IS_REST){
                    $this->render('success');
                    return;
                }
                $this->flashMessage(__('Credit Note Updated successfully', true), 'Sucmessage');
                $redirect = array('action' => 'view_credit_note', $id);
                if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                    $this->_directSendEmail($result['data']);
                    $redirect = array('action' => 'view_credit_note', $id);
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                    $redirect = array('action' => 'send_to_supplier', $id);
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                    $redirect = array('action' => 'view_credit_note', $id, 'print' => 1);
                }
                $this->redirect($redirect);
            } else {
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);
                $this->flashMessage(__('Could not update purchase credit note', true));
                if(!empty($this->PurchaseOrder->validationErrors)) {
                    $errors = get_error_message($this->PurchaseOrder->validationErrors);
                    CustomValidationFlash($errors);
                }
                if (isset($result['message'])) {
                    $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
                }
            }

        } else {
            $this->data = $purchase_orders;
            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'];

        }
        $this->set('purchase_orders', $purchase_orders);
        $this->_settings($id);
        $this->_formCommon($id);
        $this->set('title_for_layout',  __('Edit Purchase Credit Note', true));
        $this->set('content', $this->get_snippet('create-purchaseorder'));
        $this->set('purchaseorderTemplates', $this->PurchaseOrder->find('list', array('conditions' => array('PurchaseOrder.site_id' => getAuthOwner('id'), 'PurchaseOrder.type' => 1),)));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Credit Notes', true);
        $this->crumbs[0]['link'] = '/v2/owner/entity/purchase_order/list?filter[type]='.PurchaseOrder::CREDIT_NOTE;

        $this->crumbs[1]['title'] = __('Credit Note', true)." #{$purchase_orders['PurchaseOrder']['no']}";
        $this->crumbs[1]['link'] = '/owner/purchase_invoices/view_credit_note/'.$purchase_orders['PurchaseOrder']['id'];

        $this->crumbs[2]['title'] = __('Edit', true);
        $this->crumbs[2]['link'] = '#';
        $this->loadModel ( 'Store');
        $this->loadModel('ItemPermission');
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) );
        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang());
        $this->set('is_credit_note',true);
        $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));
        $this->set('HtmlModel', 'PurchaseOrderDocument');
        $this->render('owner_add');
    }

    function owner_view_credit_note($id = false, $alt_template_id = false)
    {
        App::import('Vendor', 'settings');
        $this->set('alt_template_id', $alt_template_id);
        $po_setting =  (settings::formData(InventoryPlugin));
        $this->set('enable_purchase_manual_status', settings::getValue(InvoicesPlugin, 'enable_purchase_manual_status'));
        $this->loadModel('FollowUpStatus');
        $this->set('po_setting', $po_setting);
        $this->loadModel('StockTransaction');
        $this->loadModel('Post');
        $this->loadModel('CostCenter');

        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => PurchaseOrder::CREDIT_NOTE));
        if (!$purchase_orders) {
            if (IS_REST) $this->cakeError('error404', array('message' => __('Purchase Credit Note not found', true)));
            $this->flashMessage(__('Purchase Credit Note not found', true));
            $this->redirect(array('action' => 'index'));
        }

        $this->loadModel('PurchaseOrderDocument');
        $PurchaseOrderDocuments = $this->PurchaseOrder->PurchaseOrderDocument->find('all', array('order' => 'PurchaseOrderDocument.id desc', 'conditions' => array('PurchaseOrderDocument.purchase_order_id' => $id)));
        $this->set('PurchaseOrderDocuments', $PurchaseOrderDocuments);
        $PurchaseOrderDocumentCount = $this->PurchaseOrder->PurchaseOrderDocument->find('count', array('conditions' => array('PurchaseOrderDocument.purchase_order_id' => $id)));
        $this->set('PurchaseOrderDocumentCount', $PurchaseOrderDocumentCount);

        $this->set("show_discount", $purchase_orders['show_discount']);

        $this->loadModel('Post');
        $this->set('post_count', $this->Post->find('count', array('conditions' => ['item_id' => $id, 'item_type' => Post::PURCHASE_CREDITNOTE_TYPE])));

        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(View_All_Purchase_Orders) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(View_his_own_created_Purchase_Orders)) {
                if (IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to view this page", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
        }

        $purchasesLayouts = GetObjectOrLoadModel('InvoiceLayout')->find('list', array('conditions' => array('InvoiceLayout.layout_type' => InvoiceLayout::TYPE_PO, 'InvoiceLayout.alt_template' => 1)));
        $this->set('purchasesLayouts', $purchasesLayouts);

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('purchase_order');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);

        $this->set_journal($id, Journal::JOURNAL_ENTITY_TYPE_PURCHASE_CREDIT_NOTE);

        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->set('PurchaseOrderAllPaymentStatus', PurchaseOrder::getPaymentStatuses());
        $this->set('PurchaseOrderPaymentStatus', PurchaseOrderPayment::getPaymentStatus());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $emailLogs = $this->PurchaseOrder->EmailLog->find('all', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1, 'limit' => 10, 'order' => 'EmailLog.sent_date DESC'));
        $emailCount = $this->PurchaseOrder->EmailLog->find('count', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1));

        $this->set(compact('emailLogs', 'emailCount'));

        $this->_getLayout($alt_template_id ?: $purchase_orders['PurchaseOrder']['invoice_layout_id']);

        $this->set('title_for_layout',  sprintf(__('Purchase Credit Note #%s', true), $purchase_orders['PurchaseOrder']['no']));

        if ($this->params['url']['ext'] == 'pdf') {
            $this->_setBranded();
        }

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->find('list'));

        require_once APP . 'vendors' . DS . 'Timeline.php';

        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));
        $this->loadModel('ActionLine');

        $action_list = $timeline->getPurchaseCreditNoteActionsList();
        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $purchase_credit_note_actions[$key] = $action;
            }
        }
        $this->set('purchase_orders', $purchase_orders);
        if($purchase_orders['PurchaseOrder']['subscription_id']){
            $subscription = $this->PurchaseOrder->getPurchaseOrder($purchase_orders['PurchaseOrder']['subscription_id'], array('PurchaseOrder.type' => PurchaseOrder::PURCHASE_INVOICE));
            $this->set('subscription', $subscription);
        }

        $this->set('purchase_orders_id', $id);
        $this->set('actions', $purchase_credit_note_actions);
        $this->loadModel('SitePaymentGateway');
        $pm = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));
        $pm['supplier_credit'] = __('Supplier Credit', true);
        $this->set('payment_methods', $pm);
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));

        $this->loadModel('ItemsTag');
        $this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER);
        $tags = $this->ItemsTag->get_item_tags($id, ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER, true);
        $this->set('tags', $tags);

        if (IS_REST) {
            $this->set('rest_item', $purchase_orders);
            $this->set('rest_model_name', "PurchaseOrder");
            $this->render("view");
        }

        $FollowUpStatus = $this->FollowUpStatus->getLisWithColorList(Post::PURCHASE_CREDITNOTE_TYPE);
        $this->set('FollowUpStatus', $FollowUpStatus);

        $service = izam_resolve(ButtonServiceErrorHandlerDecorator::class);
        $appsDropdown = $service->getByLocationKey(AppButtonLocationUtil::PURCHASE_CREDIT_NOTE_VIEW, $id);

        $this->set('apps_dropdown', $appsDropdown);
        $this->set('entity_key', 'purchase_order');
        $this->set('forms', $this->createAdditionalFieldsFormHandlerInstance()->show($id));
    }

    public function logExceptionsForSpecificSite($site_id, $data, $exceptions) {
        if(getCurrentSite('id') == $site_id) {
            $directory = dirname(dirname(__FILE__)) . DS . 'webroot' . DS . 'files' . DS . 'purchase-invoice-data';
            if (!is_dir($directory)) {
                $status = mkdir($directory, 0755, true); 
            }
            $fileName = date('Y-m-d').'.txt';
            file_put_contents($directory.DS.$fileName, '-----Time '.date('H:i a').' ------'."\r\n", FILE_APPEND);
            if($exceptions) {
                file_put_contents($directory.DS.$fileName, print_r($exceptions, true)."\r\n", FILE_APPEND);     
            }
            file_put_contents($directory.DS.$fileName, print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10), true), FILE_APPEND);
            file_put_contents($directory.DS.$fileName, print_r($data, true), FILE_APPEND);
            file_put_contents($directory.DS.$fileName, '-----End ------'."\r\n", FILE_APPEND);
        }

    }

    private function add_api_payments($data, $purchase_order_id, $type){
        $owner = getAuthOwner();
        foreach ($data['Payment'] as $payment) {
            if (isset($payment['amount'])&& $payment['amount'] != 0) {
                // check if payment status is draft (this value is coming from invoices import)
                $paymentStatus = $payment['draft'] == 1 ? PAYMENT_STATUS_DRAFT : PAYMENT_STATUS_COMPLETED;
                $payment['PurchaseOrderPayment'] = ['manual_payment' => 1, 'purchase_order_id' => $purchase_order_id, 'amount' => $payment['amount'], 'status' => $paymentStatus, 'date' => !empty($payment['date']) ? format_date($payment['date']): format_date(date("Y-m-d")), 'payment_method' => $payment['payment_method'], 'transaction_id' => $payment['transaction_id'], 'added_by' => 1, 'staff_id' => $owner['staff_id'], 'type' => $type];
                $this->PurchaseOrder->ownerAddPayment($payment);
            }
        }
    }


    private function recalculateConvertedRequisition($requisition_id)  {
        $this->loadModel('Requisition');
        $requisition = $this->Requisition->getRequisition($requisition_id);
        $newRequisitions = $this->Requisition->recalculateRequisition($requisition);
        foreach ($newRequisitions as $requisition) {
            $this->Requisition->updateRequisition($requisition);
        }
       return true; 
    }

    function owner_update_draft($id, $draft_status = 1)
    {

        $owner = getAuthOwner();
        $purchase_order = $this->PurchaseOrder->getPurchaseOrder($id);
        $purchase_order_type = $purchase_order['PurchaseOrder']['type'];
        if (!$purchase_order) {
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer('/v2/owner/entity/purchase_order/list', true));
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if (check_permission(Edit_Delete_All_Purchase_Orders) || (check_permission(Edit_Delete_his_own_created_Purchase_Orders) && $owner['staff_id'] == $purchase_orders['PurchaseOrder']['staff_id'])) {
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_order/list');
            }
        }

        $this->validate_open_day($purchase_order['PurchaseOrder']['date']);
        $this->data = $purchase_order;

        $this->data['PurchaseOrder']['draft'] = $draft_status;


        $canBeDrafted = true;

        // convert all invoice payments to completed status
        $this->loadModel('PurchaseOrderPayment');
        $this->PurchaseOrderPayment->updateAll(['status' => PAYMENT_STATUS_COMPLETED], ['purchase_order_id' => $purchase_order['PurchaseOrder']['id'], 'status' => PAYMENT_STATUS_DRAFT]);
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');

        if ($canBeDrafted) {
            $result = $this->PurchaseOrder->updatePurchaseOrder($this->data);
        } else {
            $result['status'] = false;
        }
        if ($result['status']) {
            $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
            izam_resolve(PurchaseOrderService::class)->update(ServiceModelDataTransformer::transform($result['data'], 'PurchaseOrder'));
            $this->handleAdjustmentRouting($result);
            RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_PURCHASE_ORDER)
                ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);

            $this->PurchaseOrder->Supplier->adjust_balance($re_read_po['PurchaseOrder']['supplier_id'], $re_read_po['PurchaseOrder']['currency_code']);
            $this->PurchaseOrder->Supplier->pay_pos_from_credit($re_read_po['PurchaseOrder']['supplier_id']);
            $this->PurchaseOrder->updatePurchaseOrderPayments($result['data']['PurchaseOrder']['id']);
            $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']);
            $this->add_actionline(ACTION_UPDATE_PO, $arry);
            $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
            if ($enable_requisitions) {
                $this->loadModel('Requisition');
                $this->Requisition->updateForOrder($re_read_po, Requisition::ORDER_TYPE_PURCHASE_ORDER, false, false);
            } else if (ifPluginActive(InventoryPlugin)) {
                $this->loadModel('StockTransaction');
                StockTransaction::updateForPo($id);
            }
            $this->PurchaseOrder->update_journals($result);

             $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
            if ($enablePurchaseInvoiceItemCostCenterDistribution) {
                $this->loadModel('CostCenter');
                $this->loadModel('CostCenterTransaction');
                $costCenterService = new PurchaseInvoiceCostCenterHandler($this->PurchaseOrder);
                $service = new CostCenterUpdaterService(
                    $costCenterService,
                    $this->CostCenter,
                    $this->CostCenterTransaction
                );
                $service->handleCostCenters($result['data']['PurchaseOrder']['id']);
            }  

            if (
                $purchase_order['PurchaseOrder']['currency_code'] != $re_read_po['PurchaseOrder']['currency_code']
                || strtotime($purchase_order['PurchaseOrder']['date']) != strtotime($re_read_po['PurchaseOrder']['date'])
            ) {
                $this->loadModel('PurchaseOrderPayment');
                $PurchaseOrderPayments = $this->PurchaseOrderPayment->find('all', array('conditions' => array('PurchaseOrderPayment.purchase_order_id' => $id)));
                foreach ($PurchaseOrderPayments  as $payment) {
                    $payment[$this->PurchaseOrderPayment->alias]['currency_code'] = $re_read_po['PurchaseOrder']['currency_code'];
                    $this->PurchaseOrderPayment->save($payment);
                }
            }

            $this->PurchaseOrder->update_payment_journals($id);
            if (IS_REST) {
                $this->render('success');
                return;
            }
            if ($enable_requisitions) {
                $product_ids = [];
                $this->loadModel('Product');
                foreach ($this->data['PurchaseOrderItem'] as $purchaseOrderItem) {
                    $is_tracked = $this->Product->find('first', ['conditions' => ['Product.id' => $purchaseOrderItem['product_id']]])['Product']['track_stock'];
                    if ($is_tracked)
                        $product_ids[] = $purchaseOrderItem['product_id'];
                }
                $requisition_saved = $this->Requisition->find('first', ['conditions' => ['Requisition.order_id' => $id]]);
                $this->flashMessage(__('Purchase Invoice has been updated', true), 'Sucmessage');
                if (!$requisition_saved && !empty($product_ids) && !$this->data['PurchaseOrder']['draft']) $this->flashMessage('Failed to save Requisition (Auto Number already Exists)', 'Errormessage', 'secondaryMessage');
            } else {
                $this->flashMessage(__('Purchase Invoice has been updated', true), 'Sucmessage');
            }
            $redirect = array('action' => 'view', $id);
            if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                $this->_directSendEmail($result['data']);
                $redirect = array('action' => 'view', $id);
            } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                $redirect = array('action' => 'send_to_supplier', $id);
            } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                $redirect = array('action' => 'view', $id, 'print' => 1);
            }
            $this->redirect($redirect);
        } else {
            if (IS_REST) $this->cakeError("error400", ["message" => $result['message'], "validation_errors" => $this->PurchaseOrder->validationErrors]);
            $this->flashMessage(__('Could not update purchase invoice', true));
            if (!empty($this->PurchaseOrder->validationErrors)) {
                $errors = get_error_message($this->PurchaseOrder->validationErrors);
                CustomValidationFlash($errors);
            }
            if (isset($result['message'])) {
                $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
            }
        }
    }

    private function setJournalAccountForPurchaseInvoiceItem($purchaseInvoiceItems) {

        $this->loadModel('JournalCat');
        $purchaseInvoiceItemsAccount = [];

        foreach ($purchaseInvoiceItems as $idx => $purchaseInvoiceItem) {

            $accountId = JournalAccountHelper::getSelectedAccountId($purchaseInvoiceItem);

            if (!empty($accountId)) {

                $account = JournalAccountHelper::getJournalAccountById($accountId);
                $categoryOptions = JournalAccountHelper::getJournalCategoryOptions();

                if (!empty($account)) {
                    $label = JournalAccountHelper::buildOptgroupLabel(
                        $account['JournalAccount']['parent_cat_ids'],
                        $categoryOptions
                    );

                    $formatted = JournalAccountHelper::formatJournalAccountOption($account, $label);
        
                    $purchaseInvoiceItemsAccount[$purchaseInvoiceItem['id']] = JournalAccountHelper::buildOptgroups($formatted);

                }
            }

        }

        $this->set(compact('purchaseInvoiceItemsAccount'));

    }

    private function setCostCentersForPurchaseInvoiceItem($purchaseInvoiceItems) { 
        $purchaseInvoiceItemsCostCenters = [];
        $this->loadModel('CostCenter');
        foreach ($purchaseInvoiceItems as $idx => $invoiceItem) {
            $purchaseInvoiceItemsCostCenters[$invoiceItem['id']] = ['value' => $invoiceItem['cost_center_id'], 'text' => $this->CostCenter->find('first',['fields' => 'name','recursive' => 0, 'conditions' => ['id' => $invoiceItem['cost_center_id']]])['CostCenter']['name'] ];

        }

        $this->set(compact('purchaseInvoiceItemsCostCenters'));
    }


    private function addCostCenterItemTransactions($purchase_order_id, $entity_type = false) {
        $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
        if ($enablePurchaseInvoiceItemCostCenterDistribution) {
        $this->loadModel('CostCenter');
        $this->loadModel('CostCenterTransaction');
        $costCenterService = new PurchaseInvoiceCostCenterHandler($this->PurchaseOrder);
        $service = new CostCenterUpdaterService(
            $costCenterService,
            $this->CostCenter,
            $this->CostCenterTransaction
        );
        $service->handleCostCenters($purchase_order_id, $entity_type);
        }
    }

}
