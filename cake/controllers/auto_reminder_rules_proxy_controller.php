<?php
/** @property SysEmailsComponent $SysEmails */
class AutoReminderRulesProxyController extends AppController {
	var $uses = [];
    var $components = array('SysEmails');
	
	function _invoice($invoice, $client, $site, $template) {
		$this->loadModel("Invoice");
		return $this->SysEmails->invoice($invoice, $client, $site, $template);
	}

    function _appointment($appointment, $receiver, $site, $template) {
        return $this->SysEmails->appointment($appointment, $receiver, $site, $template);
    }

    function _workOrder($workOrder, $receiver, $site, $template) {
        return $this->SysEmails->work_order($workOrder, $receiver, $site, $template);
    }
	
}