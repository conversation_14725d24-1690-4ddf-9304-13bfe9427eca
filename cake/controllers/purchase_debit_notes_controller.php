<?php

use App\Utils\TrackStockUtil;
use Izam\Daftra\Common\Queue\EventListenerMapper;
use Izam\Daftra\Common\Queue\EventTypeUtil;
use App\Validators\TrackStock\TrackStockValidator;
use App\Helpers\Common\PurchaseOrderAndInvoiceHelper;
use Izam\Daftra\PurchaseOrder\Services\PurchaseOrderService;

App::import('Controller', 'PurchaseInvoices');

class PurchaseDebitNotesController extends PurchaseInvoicesController
{
    var $name = 'PurchaseOrders';
    var $uses = ['PurchaseOrder'];
    function owner_delete($id = null)
    {
        $owner = getAuthOwner();
        if(!ifPluginActive(InventoryPlugin)){
            $this->flashMessage(__("Plugin is not installed", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        if (!check_permission(Edit_Delete_All_Purchase_Orders) && !check_permission(Edit_Delete_his_own_created_Purchase_Orders)) {
            if (IS_REST) {
                $this->cakeError("error400", ["message" => __("You are not allowed to delete purchase debit note", true)]);
            }
            $this->flashMessage(__("You are not allowed to delete purchase debit note", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/v2/owner/entity/purchase_debit_note/list');
        }
        $this->_record_referer_path();
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        $module_name = __('Purchase Debit Note', true);
        $verb = __('has', true);
        if (!empty($_POST['ids']) && count($_POST['ids']) > 1) {
            $verb = __('have', true);
            $module_name = __('Purchase Debit Notes', true);
        }
        if (!$id && empty($_POST)) {
            if (IS_REST) {
                $this->cakeError("error400", ["message" => sprintf(__('Invalid %s', true), $module_name)]);
            } else {
                $this->flashMessage(sprintf(__('Invalid %s', true), $module_name));
                $this->redirect('/v2/owner/entity/purchase_debit_note/list');
            }
        }

        $conditions = array('PurchaseOrder.id' => $id);
        if ($owner['staff_id'] != 0 and !check_permission(Edit_Delete_All_Purchase_Orders)) {
            $conditions['PurchaseOrder.staff_id'] = $owner['staff_id'];
        }

        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        $requisition_model = GetObjectOrLoadModel('Requisition');

        $purchase_orderss = $this->PurchaseOrder->find('all', array('conditions' => $conditions));
        $purchaseOrdersEntities = [];
        foreach ($purchase_orderss as $k => $purchase_order) {
            $purchaseOrdersEntities[$purchase_order['PurchaseOrder']['id']] = [];

            if (EventListenerMapper::hasEventListener(EventTypeUtil::PURCHASE_ORDER_DELETED)) {
                $purchaseOrdersEntities[$purchase_order['PurchaseOrder']['id']] = getRecordWithEntityStructure('purchase_debit_note', $purchase_order['PurchaseOrder']['id'], 2)->toArray();
            }
            $this->validate_open_day($purchase_order['PurchaseOrder']['date'], ['action' => 'index']);
            $requisition_saved = $requisition_model->find('first', ['conditions' => ['Requisition.order_id' => $purchase_order['PurchaseOrder']['id'], 'Requisition.order_type' => Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE]]);
            if ($enable_requisitions && $purchase_order['PurchaseOrder']['requisition_delivery_status'] == Requisition::DELIVERY_STATUS_RECEIVED && $requisition_saved) {
                if (IS_REST) {
                    $this->cakeError("error400", [
                        "message" => sprintf(__("You are not allowed to edit this record as it's already received", true), $module_name)
                    ]);
                }

                $this->flashMessage(__("You are not allowed to edit this record as it's already received", true), 'Errormessage', 'secondaryMessage');
                $this->redirect('/v2/owner/entity/purchase_debit_note/list');
            }

            $trackingValidationResult = TrackStockValidator::validate(
                $purchase_order,
                TrackStockUtil::PURCHASE_ORDER,
                TrackStockUtil::getPurchaseOrderValidationType($purchase_order['PurchaseOrder']['type']),
                true
            );
            if ($trackingValidationResult !== true) {
                $this->redirect('/v2/owner/entity/purchase_debit_note/list');
            }

            parent::validateItemIsNotLocked(
                $id,
                $purchase_order['PurchaseOrder']['type'],
                'Purchase Debit Note',
                Router::url(['controller' => 'purchase_invoices', 'action' => 'owner_view_debit_note', $id])
            );
        }
        if (empty($purchase_orderss)) {
            if (IS_REST) {
                $this->cakeError("error404", ["message" => sprintf(__('%s not found', true), ucfirst($module_name))]);
            } else {
                $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
                $this->redirect('/v2/owner/entity/purchase_debit_note/list');
            }
        }
        if (IS_REST) {
            $_POST['submit_btn'] = 'yes';
            $_POST['ids'] = [$id];
        }
        $StockTransaction = GetObjectOrLoadModel("StockTransaction");
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            $delete_conditions = array('PurchaseOrder.id' => $id);
            if ($owner['staff_id'] != 0 and !check_permission(Edit_Delete_All_Purchase_Orders)) {
                $delete_conditions['PurchaseOrder.staff_id'] = $owner['staff_id'];
            }
            $store_balance = true;
            if ($_POST['submit_btn'] == 'yes' && ifPluginActive(InventoryPlugin)) {
                $this->loadModel('Requisition');
                foreach ($purchase_orderss as $po) {
                    $requisition_count = $this->Requisition->find('count',  ['recursive' => -1, 'conditions' => ['Requisition.status' => [Requisition::STATUS_ACCEPTED, Requisition::STATUS_MODIFIED],  'Requisition.order_id' => $po['PurchaseOrder']['id'], 'Requisition.order_type' => Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE]]);
                    if ($requisition_count > 0) {
                        if (IS_REST) {
                            $this->cakeError("error401", ["message" => sprintf(__('You cant delete the %s', true), $module_name)]);
                        } else {
                            $this->flashMessage(sprintf(__('You cant delete the %s', true), $module_name));
                            $this->redirect('/v2/owner/entity/purchase_debit_note/list');
                        }
                    }
                    if (settings::getValue(InventoryPlugin, 'disable_overdraft')) {
                        $map_products = [];
                        foreach ($po['PurchaseOrderItem'] as $k) {
                            $map_products[$k['product_id']]['quantity'] = 0;
                        }
                        $StockTransactionList = StockTransaction::getPDNTransactions($id, false);
                        foreach ($StockTransactionList as $stock_transaction) {
                            $map_products[$stock_transaction['StockTransaction']['product_id']]['quantity'] = $map_products[$stock_transaction['StockTransaction']['product_id']]['quantity'] + $stock_transaction['StockTransaction']['quantity'];
                        }
                        if ($po['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE) {
                            if (
                                ifPluginActive(InventoryPlugin) &&
                                !$po['PurchaseOrder']['draft'] &&
                                !settings::getValue(InventoryPlugin, 'enable_requisitions_po') &&
                                settings::getValue(InventoryPlugin, 'disable_overdraft')
                            ) {
                                $newTransactionsProductStoreQuantities = PurchaseOrderAndInvoiceHelper::getStoreIdQuantitiesInTransactions($po['PurchaseOrder']['store_id'], $po['PurchaseOrderItem']);
                                foreach ($newTransactionsProductStoreQuantities as $productId => $productStoreQuantities) {
                                    foreach ($productStoreQuantities as $storeId => $quantity) {
                                        if (!$StockTransaction->check_balance_open($productId, $storeId, $quantity, 0, 'add_delete')) {
                                            $this->loadModel('Product');
                                            $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $productId]]);
                                            $name_code = $product['Product']['name'] . ' #' . (empty($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                                            if (IS_REST) {
                                                $this->cakeError("error400", ["message" => __('This quantity already has a transaction', true)]);
                                            }
                                            $this->flashMessage(__('This quantity already has a transaction', true), 'Errormessage', 'secondaryMessage');
                                            $store_balance = false;
                                        }
                                    }
                                }
                            }
                        }
                        if (!$store_balance) {
                            break; //Breaking from the outside foreach if there is an item that failed check balance
                        }
                    }
                }
            }
            if ($store_balance && $_POST['submit_btn'] == 'yes' && $this->PurchaseOrder->delete_related_items($id) && $this->PurchaseOrder->deleteAll($delete_conditions)) {
                foreach ($purchase_orderss as $purchase_orders) {
                    if (EventListenerMapper::hasEventListener(EventTypeUtil::PURCHASE_ORDER_DELETED)) {
                        izam_resolve(PurchaseOrderService::class)->delete($purchaseOrdersEntities[$purchase_orders['PurchaseOrder']['id']]);
                    }

                    $this->add_actionline(ACTION_DELETE_DEBITNOTE, array('primary_id' => $purchase_orders['PurchaseOrder']['id'], 'secondary_id' => $purchase_orders['PurchaseOrderItem']['product_id'], 'param1' => $purchase_orders['PurchaseOrder']['summary_total'], 'param2' => $purchase_orders['PurchaseOrder']['payment_status'], 'param3' => $purchase_orders['PurchaseOrder']['summary_paid'], 'param4' => $purchase_orders['PurchaseOrder']['no'], 'param5' => $purchase_orders['Supplier']['business_name'], 'param6' => $purchase_orders['Supplier']['supplier_number']));

                    if (ifPluginActive(InventoryPlugin)) {
                        $purchase_orders['PurchaseOrderItem'] = [];
                        StockTransaction::updateForPdn($purchase_orders);
                    }
                    $this->PurchaseOrder->delete_auto_journals($purchase_orders['PurchaseOrder']['id']);
                    $this->PurchaseOrder->Supplier->adjust_balance($purchase_orders['PurchaseOrder']['supplier_id'], $purchase_orders['PurchaseOrder']['currency_code']);
                    $this->PurchaseOrder->Supplier->pay_pos_from_credit($purchase_orders['Supplier']['supplier_id']);
                    if ($purchase_orders["PurchaseOrder"]['source_id']) {
                        $po = $this->PurchaseOrder->findById($purchase_orders["PurchaseOrder"]['source_id']);
                        if ($po['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE) {
                            $this->PurchaseOrder->create();
                            $this->PurchaseOrder->id = $po["PurchaseOrder"]['id'];
                            $this->PurchaseOrder->save(['PurchaseOrder' => [
                                'payment_status' => \Izam\Daftra\Common\Utils\PurchaseOrderStatusUtil::Pending
                            ]]);
                        }
                    }
                }

                if (IS_REST) {
                    $this->set("message", sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb));
                    $this->render("success");
                    return;
                } else {
                    $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                    $referer_url = $this->_get_referer_path();
                    $this->redirect('/v2/owner/entity/purchase_debit_note/list');
                }
            } else {
                if (IS_REST) $this->cakeError("error500", ["message" => __("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect('/v2/owner/entity/purchase_debit_note/list');
            }
        }

        $this->set('purchase_orders', $purchase_orderss);
        $this->set('module_name', $module_name);

        $this->set('title_for_layout',  sprintf(__('Delete %s', true), __('Purchase Debit Note', true)));
    }
}
