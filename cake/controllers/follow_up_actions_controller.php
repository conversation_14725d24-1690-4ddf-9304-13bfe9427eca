<?php

use Izam\Daftra\Common\Utils\PermissionUtil;

class FollowUpActionsController extends AppController {

    var $name = 'FollowUpActions';

    function owner_index($item_type = null) {
        if(!check_permission(PermissionUtil::EDIT_CLIENT_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel('Post');
		if(IS_REST){
			$this->loadModel ( 'Post');
			$types = [
				"client" => Post::CLIENT_TYPE,
				"invoice" => Post::INVOICE_TYPE,
				"estimate" => Post::ESTIMATE_TYPE,
				"purchase_order" => Post::PO_TYPE,
				"staff" => Post::STAFF_TYPE,
				"supplier" => Post::SUPPLIER_TYPE,
				"product" => Post::PRODUCT_TYPE,
				"work_order" => Post::WORK_ORDER_TYPE
			];
			$item_type = $types[$item_type];
			$actions = $this->paginate(array('FollowUpAction.item_type' => $item_type));
		} else {
			$actions = $this->FollowUpAction->find('all', array('conditions' => array('FollowUpAction.item_type' => $item_type)));
		}
        $this->set('datas', $actions);
        $this->set ( 'item_type' , $item_type ) ;
		if(IS_REST){
			$this->set('rest_items', $actions);
			$this->set('rest_model_name', "FollowUpAction");
			$this->render("index");
		}
    }
	
	public function api_view($id = null) {
        if(!check_permission(PermissionUtil::EDIT_CLIENT_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$follow_up_action = $this->FollowUpAction->find("first", ["conditions" => array("FollowUpAction.id" => $id)] );
		if(empty($follow_up_action)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Follow up action", true))));
        else $this->set('rest_item', $follow_up_action);
		$this->set('rest_model_name', "FollowUpAction");
		$this->render("view");
	}
	
	public function api_delete($id = null) {
		$follow_up_action = $this->FollowUpAction->find("first", ["conditions" => array("FollowUpAction.id" => $id)] );
		if(empty($follow_up_action)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Follow up action", true))));
        $this->loadModel ( 'FollowUpReminder');
		$items = $this->FollowUpReminder->find ('all' ,['conditions' =>   ['action_id' => $id] ]);
		if(!empty($items)) $this->cakeError("error500", ["message"=>__("You can't delete this action because it is assigned", true)]);
		if($this->FollowUpAction->delete($id)){
			$this->set("message", sprintf(__('%s has been deleted', true), __("Follow up action", true)));
			$this->render("success");
			return;
		} else {
			$this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
		}
	}

    function owner_jsonlist($item_type = null) {
        $list_array = $this->FollowUpAction->find('list', array('conditions' => array('FollowUpAction.item_type' => $item_type)));
        foreach ($list_array as $key => $item) {
            $list[] = array('name' => $item, 'value' => $key);
        }
        if (check_permission(Edit_General_Settings)) {

            $list[] = array('name' => false, 'value' => false, 'data_divider' => "true");
            $list[] = array('name' => __('Edit Actions List',true), 'value' => '-1', "data_icon" => "fa fa-cog");
        }
        echo json_encode($list);
        die();
    }

    function owner_update($item_type = 1) {
        if(!check_permission(PermissionUtil::EDIT_CLIENT_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('Post');
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Settings_Set_Taxes)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $this->FollowUpAction->recursive = -1;
            $data = $this->FollowUpAction->find('list', array('conditions' => array('item_type' => $item_type)));
            $delete_data_array = array();
            foreach ($this->data['FollowUpAction'] as $i => $item) {
                $data_posted_ids[] = $item['id'];
            }
            if (!is_array($data_posted_ids))
                $data_posted_ids = array();
            $delete_data_array = array_diff(array_keys($data), $data_posted_ids);
            $this->FollowUpAction->deleteAll(array('FollowUpAction.id' => $delete_data_array));

            if (!empty($this->data['FollowUpAction'])) {
                foreach ($this->data['FollowUpAction'] as $i => $item) {
                    $item['site_id'] = getAuthOwner('id');
                    $this->FollowUpAction->create();
                    $data['FollowUpAction'] = $item;
                    $data['FollowUpAction']['item_type'] =$item_type ;
                    if (!$this->FollowUpAction->save($data)) {
                        $errors[$i] = $this->Tax->validationErrors;
                    }
                }
                if (!empty($errors)) {
                    $this->flashMessage(__('Could not update Actions', true));
                    $this->Tax->validationErrors = $errors;
                } else {
                    $this->flashMessage(__('Actions have been updated', true), 'Sucmessage');
                    $url = array('action' => 'update' , $item_type);
                    if (isset($_GET['box'])) {
                        $url['?'] = array('box' => 1, 'updateparent' => true);
                    }
                    if (isset($_GET['callback'])) {
                        $url['?']['callback'] = $_GET['callback'];
                    }
                    $this->redirect($url);
                }
            }
        }
        $this->set ( 'item_type' , $item_type ) ;
        $this->owner_index($item_type);
        $this->render('owner_index');
    }

    function owner_check_status_delete ( $id = null ) {
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel ( 'FollowUpAction');
        $this->loadModel ( 'FollowUpReminder');
        $reread_status = $this->FollowUpAction->read(null ,$id ) ;
        if( !empty( $reread_status) ){
            $class = FollowUpStatus::$types_diff[$reread_status['FollowUpAction']['item_type']]['class'];
            $items = $this->FollowUpReminder->find ('all' ,['conditions' =>   ['action_id' => $id] ]);
//            print_r ( $items ) ; 
            if ( !empty ( $items ) ){
                echo json_encode ( ['result' => 1  ]) ; 
                die (  ) ;
            }else {
                echo json_encode ( ['result' => 0]) ; 
                die (  ) ;
            }
        }else {
            echo json_encode ( ['result' => 0  ]) ; 
            die ( ) ;
        }
    }
}

?>
