<?php

use Izam\Entity\Repositories\SchemaPortalRepository;
use Izam\Entity\Service\SpecBuilder;
use Izam\View\Form\Element\NotImplemented;
use Laminas\Form\Factory;

/**
 * @property SysEmailsComponent $SysEmails Component to handle sending system emails
 */
class DynamicController extends AppController
{

    var $uses = [];

    function add()
    {
        $specBuilder = new SpecBuilder();
        $fields = (new SchemaPortalRepository())->get('rental_unit');
        $spec = $specBuilder->build($fields);
        $factory = new Factory();
        $form = $factory->createForm($spec);
        $form->setLabel('rental_unit');

        $notImplementedCount = 0;

        foreach ($form as $element) {
            if ($element instanceof NotImplemented) {
                $notImplementedCount++;
            }
        }

        $this->setDefaultViewData();
        $this->view = 'izam';
        $this->set('form', $form);
        $this->set('notImplementedCount', $notImplementedCount);
        $this->set('key', 'rental_unit');
        $this->render('e/add2');
    }
}
