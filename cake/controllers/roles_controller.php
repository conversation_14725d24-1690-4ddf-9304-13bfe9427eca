<?php

use Izam\Daftra\Common\Utils\RoleTypeUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;

class RolesController extends AppController {

    var $name = 'Roles';

    /**
     * @var Role
     */
    var $helpers = array('Html', 'Form');

    function owner_index() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Add_Roles) && !check_permission(Edit_Roles) ) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        debug($this->Session->read('STAFF'));
        $this->Role->recursive = 0;
        $conditions = $this->_filter_params();
        $conditions['id !='] = Staff::OWNER_ROLE_ID;
        $roles = $this->paginate('Role', $conditions);
        $this->set('roles', $roles);
		if(IS_REST){
            $rest_items = $roles;
            $this->set('rest_items', $rest_items);
            $this->set('rest_model_name', "Role");
			$this->render("index");
		}
        $this->set('title_for_layout',  __('Role', true));
    }

    function owner_view($id = null) {
        if(!IS_REST){
            $this->redirect(array('action'=>'edit', $id));
        }

        if (!$id || ($id == Staff::OWNER_ROLE_ID)) {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('Invalid %s.', true), __('role', true))));
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('role', true)), true));
            $this->redirect(array('action' => 'index'));
        }

        $this->set('rest_item', $this->Role->read(null, $id));
        $this->set('rest_model_name', 'Role');
        if(IS_REST){
            $this->render("view");
        }
    }

    function owner_add_role_permission() {

        if (!empty($this->data)) {
            $this->Role->create();
            if ($this->Role->save($this->data)) {

                $this->flashMessage(sprintf(__('%s has been saved', true), __('Role', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Role', true)));
            }
        }

        $this->set('title_for_layout',  __('Add Role', true));
    }

    function owner_add($id = null) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Add_Roles)) {
                $this->izamFlashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        if(!empty($id)){
            $role = $this->Role->read(null, $id);
            unset($role['Role']['id']);
            unset($role['Role']['name']);
            $this->set('role', $role);
            $this->set('is_cloned', true);
        }

        $employee_permissions=$this->Role->getEmployeePermissions();

        $this->loadModel('Permission');
        $this->loadModel('Plugin');
        if (!empty($this->data)) {
            foreach ($this->data['RolesPermission'] as $key => $rp) {
                if ($rp['permission_id'] == 0) {
                    unset($this->data['RolesPermission'][$key]);
                }

                if ($this->data['Role']['type']==RoleTypeUtil::EMPLOYEE ) {
                    if (!in_array($rp['permission_id'],$employee_permissions)) {
                        unset($this->data['RolesPermission'][$key]);
                    }
                }
            }
            if ($this->data['Role']['type']==RoleTypeUtil::EMPLOYEE) {
                $this->data['Role']['is_super_admin'] = 0;
            }
            if ($this->data['Role']['is_super_admin'] == "1") {
                unset($this->data['RolesPermission']);
            }
     
            $this->Role->create();
            if ($this->Role->saveall($this->data)) {
                $this->add_actionline(ACTION_ADD_ROLE, array('primary_id' => $this->Role->id, 'param1' => $this->data['Role']['name'], 'param2' => json_encode($this->data['RolesPermission'])));
                $roles = $this->Role->read(null, $this->Role->id);
                Cache::write('role_' . getCurrentSite('id') . '_' . $this->Role->id, $roles);
				delete_menus();
                $message = sprintf(__('%s has been saved', true), __('Role', true));
				if(IS_REST){
                    $role_id =  $this->Role->id;
                    $this->set('id', $role_id);
                    $this->set('name', $this->data['Role']['name']);
                    $this->set('message', $message);
                    $this->render('created');
                    return;
                }
                $this->flashMessage($message, 'Sucmessage');
                $this->Role->debug_ostar_cached_roles($this->Role->id,true);
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Role', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                if(IS_REST){
                     $this->cakeError("error400", ["message"=>sprintf(__('%s could not be saved. Please, try again', true), __('Role', true)), "validation_errors"=>$this->Role->validationErrors]);
                    return;
                }
                $this->izamFlashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Role', true)), 'danger');
            }
        }
        $getCurrentPlugin = getCurrentPlugin();

        $this->set('Pluginlist', $this->Plugin->find('list', array('order' => 'Plugin.display_order asc')));
        $this->set('current_plugin', $getCurrentPlugin);
        //
        foreach ($getCurrentPlugin as $plugin) {
            $plugin_array[] = $plugin['SitePlugin']['plugin_id'];
        }
        $plugin_array[]=FollowupPlugin;
        $isBeta = getCurrentSite('beta_version') == '1';
        $conditions = array('Permission.display_order > ' => -1, 'Permission.plugin_id' => array_unique($plugin_array));
        if (!$isBeta)
            $conditions[] = ['Permission.is_beta' => 0];
        if (!settings::getValue(PluginUtil::SalesPlugin, SettingsUtil::ENABLE_SALES_ORDER)){
            $conditions['AND'] = ["Permission.id NOT BETWEEN ".PermissionUtil::SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS." AND ". PermissionUtil::SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER];
        }
        if (!settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS)){
            $conditions['AND'] = ["Permission.id NOT IN (". join(",",[
                PermissionUtil::ADD_STOCK_REQUEST,
                PermissionUtil::EDIT_STOCK_REQUEST,
                PermissionUtil::DELETE_STOCK_REQUEST,
                PermissionUtil::VIEW_STOCK_REQUESTS,
                PermissionUtil::APPROVE_OR_REJECT_STOCK_REQUEST
            ]) . ")"];
        }
        $Permission = $this->Permission->find('all', array('order' => 'Permission.display_order ASC', 'conditions' => $conditions));

        foreach ($Permission as $k => $item) {
            if($item['Permission']['id'] == VIEW_INVOICE_PROFIT && !Settings::getValue(InvoicesPlugin, 'display_invoices_profit')) {
                unset($Permission[$k]);
            }
        }

        //print_r($Permission);
        $this->set('Permission', $Permission);
        $this->set('employee_permissions', $employee_permissions);

        $this->set('title_for_layout',  __('Add Role', true));

        $this->setDefaultViewData();
        $this->view = 'izam';
        $this->render('roles/form');
    }

    function owner_edit($id = null) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_Roles)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index'));
            }
        }
        $employee_permissions=$this->Role->getEmployeePermissions();

        $this->loadModel('Permission');
        $this->loadModel('Plugin');
        if ((!$id && empty($this->data)) || ($id == Staff::OWNER_ROLE_ID)) {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('Invalid %s.', true), __('role', true))));
            $this->flashMessage(sprintf(__('Invalid %s.', true), __('role', true)));
            $this->redirect(array('action' => 'index'));
        }

        $role = $this->Role->read(null, $id);

        if(empty($role)){
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('Invalid %s.', true), __('role', true))));
            $this->flashMessage(sprintf(__('Invalid %s.', true), __('role', true)));
            $this->redirect(array('action' => 'index'));
        }

        if (!empty($this->data)) {
            foreach ($this->data['RolesPermission'] as $key => $rp) {
                if ($rp['permission_id'] == 0) {
                    unset($this->data['RolesPermission'][$key]);
                }
                if ($this->data['Role']['type']==RoleTypeUtil::EMPLOYEE) {
                    if (!in_array($rp['permission_id'],$employee_permissions)) {
                        unset($this->data['RolesPermission'][$key]);
                    }
                }

            }
            if ($this->data['Role']['type']==RoleTypeUtil::EMPLOYEE) {
                $this->data['Role']['is_super_admin'] =0;
            }
            if ($this->data['Role']['is_super_admin'] == "1") {
                unset($this->data['RolesPermission']);
            }
            $this->Role->RolesPermission->deleteAll(array('RolesPermission.role_id' => $this->data['Role']['id']), false);
            if (!isset($this->data['Role']['is_super_admin'])) {
                $this->data['Role']['is_super_admin'] = 0;
            }
            if ($this->Role->saveall($this->data)) {
                $this->loadModel('Staff');
                $this->Staff->query("update staffs set auth_id=auth_id+1 where role_id=".$id);
                $this->add_actionline(ACTION_EDIT_ROLE, array('primary_id' => $this->Role->id, 'param1' => $this->data['Role']['name'], 'param2' => json_encode($this->data['RolesPermission'])));
                $roles = $this->Role->read(null, $id);
                Cache::write('role_' . getCurrentSite('id') . '_' . $id, $roles);
				delete_menus();
                if(IS_REST){
                    $this->render('success');
                    return;
                }
                $this->Role->debug_ostar_cached_roles($id,true);
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Role', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->izamFlashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Role', true)), 'danger');
            }
        }
        if (empty($this->data)) {

            $this->data = $role;
            $this->set('role', $role);
        }
        $getCurrentPlugin = getCurrentPlugin();
        $this->set('Pluginlist', $this->Plugin->find('list', array('order' => 'Plugin.display_order asc')));
        $this->set('current_plugin', $getCurrentPlugin);
        //
        foreach ($getCurrentPlugin as $plugin) {
            $plugin_array[] = $plugin['SitePlugin']['plugin_id'];
        }
        $isBeta = getCurrentSite('beta_version') == '1';
        $conditions = array('Permission.display_order > ' => -1, 'Permission.plugin_id' => array_unique($plugin_array));
        if (!$isBeta)
            $conditions[] = ['Permission.is_beta' => 0];
        if (!settings::getValue(PluginUtil::SalesPlugin, SettingsUtil::ENABLE_SALES_ORDER)){
            $conditions['AND'] = ["Permission.id NOT BETWEEN ".PermissionUtil::SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS." AND ". PermissionUtil::SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER];
        }

        if (!settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS)){
            $conditions['AND'] = ["Permission.id NOT BETWEEN ".PermissionUtil::ADD_STOCK_REQUEST." AND ". PermissionUtil::APPROVE_OR_REJECT_STOCK_REQUEST];
        }
        $Permission = $this->Permission->find('all', array('order' => ['Permission.display_order ASC', 'Permission.id ASC'], 'conditions' => $conditions));
        //print_r($Permission);
        $this->set('Permission', $Permission);
        $this->set('employee_permissions', $employee_permissions);
        $this->set('title_for_layout',  __('Edit Role', true));

        $this->setDefaultViewData();
        $this->view = 'izam';
        $this->render('roles/form');
//        $this->render('owner_add');
    }

    function owner_edit_blocked($id = null) {
       
        if (!check_permission(Edit_Roles) ) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        
        $role = $this->Role->read(null, $id);
        if (isset($this->data['Role']['links'])) {
            $links = explode("\n",trim($this->data['Role']['links']));
            foreach ($links as $key => &$link) {
                $link = trim($link);
                $link = preg_replace("(^https?://$_SERVER[SERVER_NAME]/)", "", $link);
                $link = preg_replace("(^$_SERVER[SERVER_NAME]/)", "", $link);
                $link = preg_replace("(^/)", "", $link);
                if (empty($link)) {
                    unset($links[$key]);
                }
            }
            settings::setValue(0, 'blocked_role_'.$id, json_encode(array_values($links)),false);
            $this->flashMessage(sprintf(__('%s  has been saved', true), __('Role', true)), 'Sucmessage');
            $this->redirect(array('action' => 'index'));
        }
        $this->data = $role;
        $links = settings::getValue(0, 'blocked_role_'.$id,null,false,false);
        if ($links) {
            $this->data['Role']['links'] = implode("\n", json_decode($links));
        }
    }

    function owner_delete($id = null) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_Roles)) {
                if(IS_REST){
                    $this->cakeError("error400", ["message"=> 'You are not allowed to view this page']);
                }
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST) || ($id == Staff::OWNER_ROLE_ID)) {
            if(IS_REST){
                $this->cakeError("error400", ["message"=> sprintf(__('Invalid %s', true), __('role', true))]);
            }
            $this->flashMessage(sprintf(__('Invalid %s', true), __('role', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('role', true);
        if (is_countable($id) && count($id) > 1) {
            $module_name = __('roles', true);
        }
        $conditions = array();
        $conditions['Role.id'] = $id;
        $roles = $this->Role->find('all', array('conditions' => $conditions));

        if (empty($roles)) {
            if(IS_REST){
                $this->cakeError("error400", ["message"=> sprintf(__('%s not found', true), ucfirst($module_name))]);
            }
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        foreach ($roles as $key => $role) {
            $count_staff = $this->Role->Staff->find('count', array('conditions' => array('Staff.role_id' => $role['Role']['id'], 'Staff.deleted_at IS NULL')));

            if ($count_staff > 0) {
                unset($roles[$key]);
            }
        }

        if (empty($roles)) {
            if(IS_REST){
                $this->cakeError("error400", ["message"=> sprintf(__('The selected role is assigned to one or more staff members', true), ucfirst($module_name))]);
            }
            $this->flashMessage(sprintf(__('The selected role is assigned to one or more staff members', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['ids']) || IS_REST) {
            $ids = isset($_POST['ids']) ? $_POST['ids'] : $id;
            if (($_POST['submit_btn'] == 'yes' || IS_REST) && $this->Role->deleteAll(array('Role.id' => $ids))) {
                foreach ($roles as $key => $role) {
                    $this->add_actionline(ACTION_DELETE_ROLE, array('primary_id' => $role['Role']['id'], 'param1' => $role['Role']['name']));
                }
				delete_menus();
                if(IS_REST){
                    $this->render("success");
                    return;
                }   
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  __('Delete Role', true));

        $this->set('roles', $roles);
        $this->set('module_name', $module_name);
    }

    public function owner_inital($id = null) {
        $this->loadModel('Permission');
        $this->loadModel('Plugin');
        $getCurrentPlugin = getCurrentPlugin();
        $pluginList = $this->Plugin->find('list', array('order' => 'Plugin.display_order asc'));
        $employee_permissions=$this->Role->getEmployeePermissions();
        foreach ($getCurrentPlugin as $plugin) {
            $plugin_array[] = $plugin['SitePlugin']['plugin_id'];
        }
        $plugin_array[]=FollowupPlugin;
        $isBeta = getCurrentSite('beta_version') == '1';
        $conditions = array('Permission.display_order > ' => -1, 'Permission.plugin_id' => array_unique($plugin_array));
        if (!$isBeta)
            $conditions[] = ['Permission.is_beta' => 0];
        if (!settings::getValue(PluginUtil::SalesPlugin, SettingsUtil::ENABLE_SALES_ORDER)){
            $conditions['AND'] = ["Permission.id NOT BETWEEN ".PermissionUtil::SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS." AND ". PermissionUtil::SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER];
        }
        if (!settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS)){
            $conditions['AND'] = ["Permission.id NOT IN (". join(",",[
                PermissionUtil::ADD_STOCK_REQUEST,
                PermissionUtil::EDIT_STOCK_REQUEST,
                PermissionUtil::DELETE_STOCK_REQUEST,
                PermissionUtil::VIEW_STOCK_REQUESTS,
                PermissionUtil::APPROVE_OR_REJECT_STOCK_REQUEST
            ]) . ")"];
        }
        $Permission = $this->Permission->find('all', array('order' => 'Permission.display_order ASC', 'conditions' => $conditions));

        foreach ($Permission as $k => $item) {
            if($item['Permission']['id'] == VIEW_INVOICE_PROFIT && !Settings::getValue(InvoicesPlugin, 'display_invoices_profit')) {
                unset($Permission[$k]);
            }
        }
        $pluginsData = $permissionMapping= [];
        foreach ($getCurrentPlugin as $key => $main_plugin) {
            $have_permission = false;
            foreach ($Permission as $plist) {
                if (in_array($plist['Permission']['id'], $employee_permissions)) {
                    if ($main_plugin['SitePlugin']['plugin_id'] == $plist['Permission']['plugin_id']) {
                        $permissionList[$main_plugin['SitePlugin']['plugin_id']][$plist['Permission']['id']] = $plist['Permission']['permission_name'];
                        $permissionMapping[$plist['Permission']['id']] = $plist['Plugin']['id'];
                        $pluginsData[$plist['Plugin']['id']] = $plist['Plugin'];
                    }
                }
            }
            if ($have_permission == false) {
                unset($getCurrentPlugin[$key]);
            }
        }

        die(json_encode(['permissionList' => $permissionList, 'permissionMapping' => $permissionMapping, 'pluginsData' => $pluginsData]));

    }

}
