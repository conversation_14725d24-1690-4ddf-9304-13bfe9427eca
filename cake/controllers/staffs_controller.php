<?php

use App\Services\UserSessionRedisService;
use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\Auth\AuthUserTypeUtil;

class StaffsController extends AppController {

    var $Staff;
    var $helpers = array('Html', 'Form');
    var $components = array('RequestHandler', 'Email', 'SysEmails', 'Lmail');


    function owner_json_find2($is_report = null) {
//        Configure::write('debug', 0);
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        if (!empty($value)) {
            $value = mysql_escape_string($value);
            $conditions = array();
            $conditions['OR'] = array("CONCAT(Staff.id) = '$value' ", "Staff.name like '%$value%'", "Staff.middle_name like '%$value%'", "Staff.last_name like '%$value%'", "Staff.email_address like '$value%'", "Staff.mobile like '$value%'", "Staff.home_phone like '$value%'", "Staff.business_phone like '$value%'", "Staff.id like '$value%'","Staff.code like '%$value%'");
            if ($_GET['conditions']) {
                $conditions['AND'] = $_GET['conditions'];
            }
            if (ifPluginActive(BranchesPlugin)) {
                $staffBranchIds = getStaffBranchesIDsSuspended();
                $staffBranchIdsCommaSeparated = join(",",$staffBranchIds);
                $accessableStaffCondition = "Staff.branch_id in ($staffBranchIdsCommaSeparated)";
                if(is_array($conditions['AND'])){
                    $conditions['AND'][] = $accessableStaffCondition;
                }else{
                    $conditions['AND'] = [$accessableStaffCondition];
                }
            }
            $staff = $this->Staff->find('all', array('limit' => 30, 'conditions' => $conditions, 'order' => 'Staff.id asc', 'recursive' => -1));
            $result = array();
            $s3FileManager = new \App\Services\S3FileManager();
            foreach ($staff as $employee) {
                $full_name = '';
                if (!empty($employee['Staff']['name']) || !empty($employee['Staff']['last_name']))
                    $full_name = $employee['Staff']['name'] . ' ' . $employee['Staff']['last_name'];

                    $image_url = resizeImage($s3FileManager->getUrlAttribute($employee['Staff']['photo'], $employee['Staff']['name']), ['w' => 40, 'h' => 40, 'c' => 0]) ;
                    
                    if (empty($employee['Staff']['photo']) ) {
                        $image_url = CDN_ASSETS_URL."imgs/account-def.png";
                    }
                    $result[] = array(
                    'name' => ( (empty($full_name) ? '' :  $full_name ) ). ' ' . '#' . $employee['Staff']['code'] ?? $employee['Staff']['id'] ,
                    'id' => $employee['Staff']['id'],
                    'img' => '<img src="'.$image_url.'" alt="name">',
                    'details' => '',
                    'email' => sprintf('(%s)',$employee['Staff']['email_address']),

                );
            }

            if (!empty($result))
                array_unshift($result, array(
                    'name' => __('Please Select', true) . ':',
                    'id' => '',
                    'details' => ''
                ));
                
            echo json_encode($result, JSON_UNESCAPED_SLASHES);
            die();
        }else {
            echo json_encode(array());
            die();
        }
    }


    function owner_json_find() {

        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        if (!empty($value)) {

            $value = $this->Staff->getDataSource()->value('%'.$value.'%','string');
            $conditions = ['Staff.name LIKE '.$value ];
            $result  = $this->Staff->find('list',['conditions' => $conditions]);
            echo json_encode($result);
            die();
        }else {

            echo json_encode(   $this->Staff->find('list'));
            die();
        }
    }
	public function api_view($id = null) {
		if (!check_permission(Staff_Edit_Staffs)) {
			$this->cakeError('error403');
        }
		$staff = $this->Staff->find("first", ["conditions"=>['Staff.id'=>$id]] );
		if(empty($staff)) $this->cakeError('error404', array('message' => __('Staff not found', true)));
        else $this->set('rest_item', $staff);
		$this->set('rest_model_name', "Staff");
		$this->render("view");
	}

    /**
     * @deprecated
     * @ remove after resolving all redirects to this function
     */
    function owner_index() {

        if (!IS_REST) {
            $this->redirectToIndex();
        }

        if (!check_permission(Staff_Edit_Staffs)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect("/");
        }
        $conditions = $this->_filter_params();
        $owner_id=getAuthOwner('staff_id');
        if (ifPluginActive(ExpensesPlugin))
        {
            if($owner_id==0){
                $this->loadModel('Treasury');
                $this->set('treasuries',$this->Treasury->find('list'));
            }else {
                $this->loadModel('ItemPermission');
                $this->loadModel('Treasury');
                $withdraw_treasuries = ($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW) ?: []);
                $deposit_treasuries = ($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT) ?: []);
                $this->set('treasuries',($withdraw_treasuries + $deposit_treasuries));
            }
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        if($owner_id != 0 && ifPluginActive(BranchesPlugin)) {
            $accessBranches = getStaffBranchesSuspended();
            $conditions[] = 'Staff.branch_id IN (' . implode(',', array_keys($accessBranches)) . ')';
        }
	    $conditions[] = 'Staff.deleted_at IS NULL';
        $this->paginate['Staff'] = array('conditions' => $conditions,
            'order' => array('Staff.name asc'),
        );
        $this->set('countryCodes', $this->Staff->getCountryList());
		$staffs = $this->paginate();
        $this->set('staffs', $staffs);
        $this->set('title_for_layout',  __('Staff Members', true));
		if(IS_REST){
			$this->set('rest_items', $staffs);
			$this->set('rest_model_name', "Staff");
			$this->render("index");
		}
    }

    function owner_view($id = null) {
        $this->redirect(array('action'=>'edit', $id));
    }

    function owner_send_login_details($id = false) {
        $referral_url = array('action' => 'index');

        $staff = $this->Staff->find(array('Staff.id' => $id));

        if (!$staff) {
            if (IS_REST) {
                return $this->cakeError('error404', ['message' => 'Staff not found']);
            }
            $this->flashMessage(__('Staff not found', true));
            $this->redirect($referral_url);
        }

        if (getCurrentSite('plan_id') == 1) {
            if (IS_REST) {
                return $this->cakeError('error400', ['message' => 'We cannot send the login details while the site plan is free.']);
            }
            $this->flashMessage(__('We cannot send the login details while the site plan is free.', true));
            $this->redirect($referral_url);
        }

        if(isset($staff['Staff']['role_id']) && ($staff['Staff']['role_id'] == Staff::OWNER_ROLE_ID)){
            if (IS_REST) {
                return $this->cakeError('error400', ['message' => 'Login details could not be sent!']);
            }
            $this->flashMessage(__('Login details could not be sent!', true));
            $this->redirect($referral_url);
        }

        $staff['Staff']['email'] = $staff['Staff']['email_address'];
        $new_password = substr(base64_encode(HashPassword(time() . mt_rand())), 0, 7);
        $staff['Staff']['password_view'] = $staff['Staff']['password'] = $new_password;
        if ($this->Staff->saveField('password', $staff['Staff']['password_view'])) {
            $this->Staff->UpdateAuthCount($id);
            $this->set('user', $staff['Staff']);
            $result = $this->_send_email_to_user($staff['Staff']);
            if ($result) {
                if (IS_REST) {
                    return $this->render('success');
                }

                $this->flashMessage(__('Login details have been sent.', true), 'Sucmessage');
            } else {
                if ($this->SysEmails->error_message) {
                    if (IS_REST) {
                        return $this->cakeError('error400', ['message' => $this->SysEmails->error_message]);
                    }
                    $this->flashMessage(__($this->SysEmails->error_message, true));
                }
                if (IS_REST) {
                    return $this->cakeError('error400', ['message' => 'Login details could not be sent!']);
                }
                $this->flashMessage(__('Login details could not be sent!', true));
            }
        } else {
            if (IS_REST) {
                return $this->cakeError('error400', ['message' => 'Login details could not be sent!']);
            }
            $this->flashMessage(__('Login details could not be sent!', true));
        }

        $this->set('title_for_layout',  __('Staff Members', true));
        $this->redirect($referral_url);
    }

    //--------------------------
    function _send_email_to_user($staff) {
        $site = getAuthOwner();
//
//		$this->Lmail->to = $to;
//		$this->Lmail->sendAs = 'html';
//		$this->Lmail->layout = 'contact';
//		$this->Lmail->template = 'contact';
//		$this->Lmail->from = $site['business_name'] . '<' . $site['email'] . '>';
//		$this->Lmail->subject = "{$site['business_name']}: " . sprintf(__('Welcome to your user account at %s', true), $site['business_name']);
//		$this->Lmail->language = $this->Client->get_client_language($client_id);

        $this->_setBranded();
        return $this->SysEmails->staffDetails($staff, $site);
//		if ($this->Lmail->send()) {
//			return true;
//		}
//		return false;
    }

    function owner_add() {
        //die ( 'test' ) ;
        $this->loadModel('Store');
        $this->_settings();
        if (!check_permission(Staff_Add_New_Staffs)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $site = getAuthOwner();
        $this->loadModel('Site');

        $result = $this->Site->check_add_staff_limit($site['id']);

        $ajax = $this->RequestHandler->isAjax();
        if (empty($result['status'])) {
			if(IS_REST) $this->cakeError("error402", ["message"=>$result['message']]);
            if ($ajax) {
                die(json_encode(array(
                    'message' => $result['message'],
                    'class' => 'Errormessage',
                    'error' => true
                )));
            } else {
                $this->_flashLimitMessage($result['message'], __('staffs', true));
                $this->redirect(array('action' => 'index'));
            }
        }

		$this->loadModel('Shift');
        $owner=getAuthOwner();
        if($owner['staff_id']==0) {
            $this->Shift->applyBranch['onFind'] = false;
        }
		$shifts = $this->Shift->find('list');
		$this->set('shifts', $shifts);
        $this->loadModel('Role');
        if (!empty($this->data)) {
            $this->data['Staff']['active'] = 1;
            if ($this->Staff->save($this->data)) {

                $role = $this->Role->read(null, $this->data['Staff']['role_id']);
                $this->add_actionline(ACTION_ADD_STAFF, array('primary_id' => $this->Staff->id, 'param5' => $this->data['Staff']['name'], 'param2' => $this->data['Staff']['email_address'], 'param3' => $this->data['Staff']['role_id'], 'param4' => $role['Role']['name']));
                $dbSite = $this->Site->find(array('Site.id' => $site['id']), 'current_staff_count');
                $this->Site->id = $site['id'];
                $this->Site->saveField('current_staff_count', $dbSite['Site']['current_staff_count'] + 1);

				if(IS_REST){
					$this->set('id', $this->Staff->id);
					$this->render('created');
					return;
				}
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Staff', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Staff->validationErrors]);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Staff', true)));
            }
        }
        $this->loadModel('Role');
        $this->set('hourlyRateCurrencyCodes', $this->Currency->getCurrencyList());
        $this->set('title_for_layout',  __('Add Staff Member', true));
        $this->loadModel ( 'Site');
        $this->loadModel ( 'Plan');
        $sl=$this->Site->getLimits();

        $this->set('staff_limit',$sl['staff']['limit']);
        $this->set('staff_count', $this->Staff->find('count'));
        $this->set('staff_over_limit', $sl['staff']['limit']-$this->Staff->find('count'));
        //echo getCurrentSite('plan_id');
        $this->loadModel('ItemPermission');
        $this->set('default_stores', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW));
        $this->set('primary_store_id', $this->Store->getPrimaryStore());
        $this->set('roles', $this->Role->find('list'));
    }

    function owner_edit($id = null) {
        $this->redirect("/v2/owner/staff/$id/edit");
        $this->loadModel('Store');
        $this->_settings();
        if (!check_permission(Staff_Edit_Staffs)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
		$this->loadModel('ItemStaff');



		$this->loadModel('Shift');
        $owner=getAuthOwner();
        if($owner['staff_id']==0) {
            $this->Shift->applyBranch['onFind'] = false;
        }
		$shifts = $this->Shift->find('list');
		$this->set('shifts', $shifts);
        $site = getAuthOwner();
        $this->loadModel('Role');
        $this->loadModel('Site');
        $this->set('roles', $this->Role->find('list'));
		if(IS_REST) $this->data['Staff']['id'] = $id;
        if (!empty($this->data)) {
            if (empty($this->data['Staff']['password'])) {
                unset($this->data['Staff']['password']);
            }

            if ($this->Staff->save($this->data)) {

                if ($_POST['update_all_time_records'] == 1) {
                    $this->loadModel('TimeTracking');
                    $allTimes = $this->TimeTracking->get_time_records($id);
                    debug($allTimes);
                    foreach ($allTimes as $t) {
                        $this->TimeTracking->update_time_records($id, $this->data['Staff']['hourly_rate']); //$hourly_rate );
                    }
                }
                ///debug ( $this->data ) ;die ; 
                $this->loadModel('Role');
                $role = $this->Role->read(null, $this->data['Staff']['role_id']);
                $this->add_actionline(ACTION_EDIT_STAFF, array('primary_id' => $id, 'param5' => $this->data['Staff']['name'], 'param2' => $this->data['Staff']['email_address'], 'param3' => $this->data['Staff']['role_id'], 'param4' => $role['Role']['name']));
				if(IS_REST){
					$this->render('success');
					return;
				}
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Staff', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
				if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Staff->validationErrors]);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Staff', true)));
            }
        }else {
            $this->data = $this->Staff->read(NULL, $id);
            $owner=getAuthOwner();
            if($owner['staff_id']==0) {
                $this->ItemStaff->applyBranch['onFind'] = false;
            }
			$staff_shifts = $this->ItemStaff->get_staff_items_list($id,ItemStaff::SHIFT_ITEM_TYPE);
			$this->data['Staff']['shifts'] = $staff_shifts;
//			dd($this->data);
            unset($this->data['Staff']['password']);
        }
        $this->loadModel('ItemPermission');
        $this->set('default_stores', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW , $id));
        $this->set('primary_store_id', $this->Store->getPrimaryStore());
        $this->loadModel('Currency');
        $this->set('hourlyRateCurrencyCodes', $this->Currency->getCurrencyList());
        if (ifPluginActive(BranchesPlugin)) {
            $this->data['Staff']['branches'] = $this->ItemStaff->get_staff_items_list($id,ItemStaff::BRANCH_ITEM_TYPE);
        }
        $this->set('title_for_layout',  __('Edit Staff Member', true));
        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        $this->loadModel('Site');
        $site = getAuthOwner();
        if (!check_permission(Staff_Edit_Staffs)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
		if(IS_REST){
			$_POST['submit_btn'] = 'yes';
			$_POST['ids'] = [$id];
		}
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes') {
                $ids = $_POST['ids'];
                $module_name = __('staff', true);
                foreach ($ids as $id) {
                    if (hasTransactions($id,HAS_TRANSACTION_TYPE_STAFF)){
                        $this->flashMessage(sprintf(__('%s #%s can not be deleted because he/she has transactions, disable him instead', true), $module_name, $id));
                        $this->redirect($this->referer(array('action' => 'index'), true));
                    }
                    $staff = $this->Staff->read(null, $id);
                    if ($this->Staff->delete($id)) {
                        $this->add_actionline(ACTION_DELETE_STAFF, array('primary_id' => $id, 'param5' => $staff['Staff']['name'], 'param2' => $staff['Staff']['email_address']));
						if(IS_REST){
							$this->set("message", sprintf(__('%s has been deleted', true), ucfirst($module_name)));
							$this->render("success");
							return;
						}
                    }
					if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                    $this->flashMessage(sprintf(__('%s has been deleted', true), __('Staff', true)), 'Sucmessage');
                    $this->redirect(array('action' => 'index'));
                }
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }
        $conditions = array('Staff.id' => $id);
        $staffs = $this->Staff->find('all', array('conditions' => $conditions));
        debug($staffs);
        $this->set('staffs', $staffs);

//        if ($this->Staff->delete($id)) {
//            $dbSite = $this->Site->find(array('Site.id' => $site['id']), 'current_staff_count');
//            $this->Site->id = $site['id'];
//            $this->Site->saveField('current_staff_count', $dbSite['Site']['current_staff_count'] - 1);
//        
//        $this->flashMessage(sprintf(__('%s has been deleted', true), __('Staff', true)), 'Sucmessage');
//        $this->redirect(array('action' => 'index'));
//        }else{
//        $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Staff', true)));
//         $this->redirect(array('action' => 'index'));
//        }
    }

    function owner_account_info() {
        $this->set('Staff', getAuthStaff());
    }

    function owner_change_email() {
        $staff = getAuthStaff();
        if(isset($staff['role_id']) && ($staff['role_id'] == Staff::OWNER_ROLE_ID)) {
            $this->redirect(array('controller'=>'sites', 'action' => 'change_email'));
        }
        if (!empty($this->data)) {
            if ($this->Staff->ChangeEmail($this->data)) {
                $this->add_actionline(ACTION_CHNAGE_EMAIL, array('param3' => $staff['email_address'], 'param4' => $this->data['Staff']['email_address']));
                $this->flashMessage(__('The Email address has been saved', true), 'Sucmessage');
                $this->Staff->reload_session();
                $this->redirect(array('action' => 'change_email'));
            } else {
                $this->flashMessage(__('The password could not be saved. Please, try again.', true));
            }
        }
        if (empty($this->data)) {
            $this->data['Staff'] = $staff;
        }
    }

    function owner_change_password() {

        $staff = getAuthStaff();
        if(isset($staff['role_id']) && ($staff['role_id'] == Staff::OWNER_ROLE_ID)) {
            $this->redirect(array('controller'=>'sites', 'action' => 'change_password'));
        }
        if (!empty($this->data)) {
            if ($this->Staff->ChangePassword($this->data, $staff)) {
                $this->Staff->UpdateAuthCount($staff['id']);
                AuthHelper::revoke(AuthUserTypeUtil::STAFF, $staff['id']);
                clearSession("staff",getAuthStaff('id'),session_id());
                if(useRedis()) {
                    try {
                        $userSessionRedisService = resolve(UserSessionRedisService::class);
                        $userSessionRedisService->revokeSessions($staff['id']);
                    } catch (\Exception $e) {
                        \Rollbar\Rollbar::log(\Rollbar\Payload\Level::WARNING, 'Failed to revoke session from Redis: ' . $e->getMessage());
                    }
                }

                $this->add_actionline(ACTION_CHNAGE_PASSWORD);
                $this->flashMessage(__('The password has been saved', true), 'Sucmessage');
                $this->redirect(array('action' => 'change_password'));
            } else {
                $this->flashMessage(__('The password could not be saved. Please, try again.', true));
            }
        }
        $this->crumbs[0]['title'] = __('Change Password', true);
        $this->crumbs[0]['url'] = array('action' => 'change_password');
        $this->set('title_for_layout',  __('Change Password', true));
    }

    function owner_change_staff_password($id = null) {
        $this->set('title_for_layout',  __('Change Staff Password', true));
		if (!check_permission(Staff_Edit_Staffs)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $owner = getAuthOwner();

        $staff = $this->Staff->find(array('Staff.id' => $id));
        if (!$staff) {
            $this->flashMessage(__("Staff not found", true), 'Errormessage', 'secondaryMessage');
            $this->redirectToIndex();
        }
        $isOwnerStaff = isset($staff['Staff']['role_id']) && ($staff['Staff']['role_id'] == Staff::OWNER_ROLE_ID);
        if ($isOwnerStaff && isOwner()) {
            $this->redirect('/owner/owners/change_password');
        }
        if ($isOwnerStaff) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        if ($owner['staff_id'] != 0) {
            if (!check_permission(Staff_Edit_Staffs)) {
                $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
                $this->redirect($this->referer());
            }
        }

        $this->set('staff', $staff);

        if (!empty($this->data)) {

            $this->Staff->validate = array('password' => array(
                    'rule' => array('minLength', 6),'required'=>true, 'allowEmpty' => false, 'message' => __('The password should be 6 characters at least', true)
                ), 'confirm_password' => array('rule' => 'checkPasswd','required'=>true, 'allowEmpty' => false, 'message' => __('Please enter confirm password', true)));

            $this->Staff->set($this->data);
            if ($this->Staff->validates()) {
                $this->Staff->query("update staffs set auth_id=auth_id+1 where id=".$id);
                $this->flashMessage(sprintf(__('Staff password has been changed', true), __('Staff', true)), 'Sucmessage');
                $this->Staff->id = $id;
                //$this->data['Client']['password'] = HashPassword($this->data['Client']['password']);
                $this->Staff->applyBranch['onSave'] = false; //to avoid change staff branch
                $row = $this->Staff->save($this->data, false);

                if ($row) {

                    $this->Staff->UpdateAuthCount($id);
                    AuthHelper::revoke(AuthUserTypeUtil::STAFF, $id);
                    $this->add_actionline(ACTION_OWNER_CHNAGE_STAFF_PASSWORD, array('primary_id' => $staff['Staff']['id'], 'param2' => $staff['Staff']['name'], 'param3' => $staff['Staff']['email'], 'param4' => $staff['Staff']['name']));
                    $this->redirect(laravel_employees_route);
                }
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Employee', true)));
            }
        }
        $this->set('id', $id);
    }

    function owner_login_as($staff_id = false) {
        $this->loadModel("Site");
        if ($this->Site->isFreeAndCreatedFromMoreThanOneMonth($_SERVER["SERVER_NAME"])) {
            $this->flashMessage(__("You cannot log in to the system using one of the employees after you exceed the trial period in the free plan", true), 'Errormessage', 'secondaryMessage');
            $this->redirect("/");
        }
		if (!check_permission(Staff_Edit_Staffs)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $staff = $this->Staff->findById($staff_id);
        if (!$staff) {
            $this->flashMessage(__('Staff not found', TRUE));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (isset($staff['Staff']['role_id']) && ($staff['Staff']['role_id'] == Staff::OWNER_ROLE_ID)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You cannot login as owner", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $query = 'UPDATE staffs SET last_login = NOW() WHERE id = ' . $staff_id;
        $this->Staff->query($query);
        $this->add_actionline(ACTION_LOGIN_AS_STAFF, array('primary_id' => $staff['Staff']['id'], 'param2' => $staff['Staff']['name']));

        $this->Staff->reload_session($staff_id);
        $this->Site->reload_session();
        Cache::delete(getNotificationCacheKey());
        $this->redirect('/');
    }

    function _settings() {
        App::import('Vendor', 'settings');
        $enable_maximum_discount= settings::getValue(InventoryPlugin, "enable_maximum_discount"  );
        $this->set ('enable_maximum_discount', $enable_maximum_discount );
        $this->loadModel('Client');
        $this->loadModel('Country');
        $this->loadModel('Language');
        $this->loadModel('Currency');

        $this->set('countryCodes', $this->Country->getCountryList());
        $this->set('languageCodes', $this->Language->getLanguageList());
        $this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());

        $this->loadModel('Site');
        $this->set('default_language', $this->Site->get_site_language());
        $this->set('default_currency', $this->Site->get_site_currency());
        $this->set('default_country', $this->Site->get_site_country());
        if (ifPluginActive(ExpensesPlugin))
        {
            $owner_id=getAuthOwner('staff_id');
            if($owner_id==0){
                $this->loadModel('Treasury');
                $this->set('treasuries',$this->Treasury->find('list'));
            }else {
                $this->loadModel('ItemPermission');
                $this->loadModel('Treasury');
                $withdraw_treasuries = ($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW) ?: []);
                $deposit_treasuries = ($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT) ?: []);
                $this->set('treasuries',($withdraw_treasuries + $deposit_treasuries));
            }
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
    }

    function owner_get_old_staff_rate() {

        $staff_id = $_POST['staff_id'];
        $staff_member = $this->Staff->findById($staff_id);
        $this->loadModel('TimeTracking');
        $count = $this->TimeTracking->find('count', array('conditions' => array('TimeTracking.staff_id' => $staff_id)));
        die(json_encode(['hourly_rate' => $staff_member['Staff']['hourly_rate'], 'count' => $count]));
    }

    function owner_update_current_branch($branch_id) {
	    if (in_array($branch_id, getStaffBranchesIDs())){
	        settings::setValue(BranchesPlugin,'branch-'.getAuthOwner('staff_id'),$branch_id);
            $session = new CakeSession;
            $session->write('branch_id', $branch_id);
        }
        $this->flashMessage(sprintf(__('branch has been changed', true), __('branch', true)), 'Sucmessage');
	    if(isset($_GET['last_url'])){

            header ('Location: '.urldecode(urlencode(base64_decode($_GET['last_url']))));
            die();
        }
        $this->redirect('/');
    }

    function owner_toggle_active($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', 'staff',true)));
            $this->redirect(array('action'=>'index'));
        }
        $staff = $this->Staff->read(null, $id);

        $this->Staff->UpdateAuthCount($id);
        if ($staff['Staff']['active'] == 1) {
            $this->Staff->setStaffStatus($id,0);
        } else {
            $site_id = getCurrentSite('id');
            $this->loadModel('Site');
            $check = $this->Site->check_add_staff_limit($site_id);
            if (!$check['status']) {
                $this->flashMessage($check['message']);
            } else {
                $this->Staff->setStaffStatus($id,1);
            }
        }
        $this->redirect(array('action'=>'index'));
    }

    private function redirectToIndex()
    {
        if (\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('staffs')) {
            $this->redirect('/v2/owner/staff');
        } else {
            $this->redirect('/v2/owner/entity/staff/list');
        }
    }
}

