<?php

use Izam\Attachment\Models\EntityAttachment;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Aws\Aws;
use Izam\Limitation\Utils\LimitationUtil;
use Izam\Logging\Service\RollbarLogService;

class CategoriesController extends AppController {

    var $name = 'Categories';

    /**
     * @var Category
     */
    var $Category;
    var $helpers = array('Html', 'Form');

    function owner_index($type = null) {
        if(!check_permission(PermissionUtil::View_All_Products) && !check_permission(PermissionUtil::View_his_own_Products) && !str_contains($this->referer(), '/pos/')){
            if(IS_REST) $this->cakeError('error404', array('message' => __('You are not allowed to access Product', true)));
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (empty($type) && isset($_GET['type'])) {
            $type = $_GET['type'];
        }
        if( !empty($type) )
        {
            if(!in_array($type,array_keys(Category::get_category_types())))
                $this->cakeError('error404', array('message' => __(sprintf ('Invalid %s', 'category'),true)));
            $this->set('title_for_layout',  Category::get_category_types()[$type]);
            $this->Category->breadCrumbsBaseName = $this->pageTitle;
            $model_name = 'Category';
            if (IS_REST) {
                $model_name = Category::get_category_types()[$type].'Category';
                $this->Category->alias = $model_name;
            }
            $conditions = array(["{$model_name}.category_type"=> $type]);
        }
        else
        {
            $this->cakeError('error404', array('message' => __(sprintf ('Invalid %s', 'category'),true)));
        }

        $form_filters = $this->_filter_params();
        $new_form_filters = [];

        foreach ($form_filters as $key => $form_filter) {
            $new_form_filters[] = [$key => $form_filter];
        }

        $applyBranchFind = true;
        if ($type != Category::CATEGORY_TYPE_PRODUCT) {
            $applyBranchFind = false;
        }

        $this->set('type',$type);
        $this->Category->bindAttachmentRelation('category');

        $this->paginate[$model_name] = [
            'conditions' => array_merge($conditions, $new_form_filters),
            'applyBranchFind' => $applyBranchFind
        ];
		$categories = $this->paginate( [] );
        $awsService = new Aws;
		// Fetch sub categories' parent categories
        foreach ($categories as $index => $category) {
            $categories[$index]['Category']['parent_category'] = $this->Category->getCategoryParent($category['Category']['parent_id']);
 
            if(!empty($category['Attachments'])){
                RollbarLogService::logTempFilesToRollbar($category['Attachmnets'][0]['is_temp'],$category['Attachments'][0]);
                $categories[$index]['CategoriesCategory']['image_full_path'] = $awsService->getPermanentUrl($category['Attachments'][0]['path']);
            }
        }
		$this->setup_nav_data($categories);
        $this->set('categories', $categories);
		$this->set('rest_items', $categories);
		$this->set('rest_model_name', $model_name);
        $this->set('filters' , $this->Category->getFilters());
        $this->Category->alias = 'Category';
        if(IS_REST) $this->render('index');
    }

    function owner_view($id = null) {
        $this->Category->bindAttachmentRelation('category');

        if(!check_permission(PermissionUtil::View_All_Products) && !check_permission(PermissionUtil::View_his_own_Products)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$category = $this->Category->read(null, $id);
        if (empty($category)) {
			if(IS_REST) $this->cakeError('error404', array('message' => __(sprintf ('Invalid %s', 'category'),true)));
            $this->flashMessage( sprintf(__('Invalid %s', true), __('category', true)) );
            $this->redirect(array('owner'=>true,'action' => 'index'));
        }
        $this->set('category', $category);
		$this->set('rest_item', $category);
		$this->set('rest_model_name', "Category");
		if(IS_REST) $this->render("view");
    }

    function owner_add($type = null) {
        if(!check_permission(PermissionUtil::Proudcts_Add_New_Proudct)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!empty($this->data)) {
            $this->Category->create();
            if ($category = $this->Category->save($this->data)) {
                $this->data['Category']['id'] = $this->Category->getLastInsertID();
                
                $attachments = $this->data['Category']['attachments'];
                if(!empty($attachments)){
                    $this->attachS3imageToCategory($attachments,$this->data['Category']['id']);
                }

                if (IS_REST) {
                    echo json_encode([
                        "result" => "success",
                        "code" => 202,
                        "Category" => $this->data["Category"]
                    ]);
                    die;
                }
                $label = $_GET['type'] == 3 ? 'The POS Device' : 'Category';
                $this->flashMessage(sprintf(__('%s Saved', true), __($label,true)), 'Sucmessage');
                $this->redirect(array('action' => 'index',$this->data['Category']['category_type']));
            }
        }
        if (empty($type) && isset($_GET['type'])) {
            $type = $_GET['type'];
        }

        $this->set('action','add?type='.$type);
        
        if( !empty($type) )
        {
            $conditions = array('conditions'=>['Category.category_type'=> $type]);
            $this->set('type', $type);
	        $this->loadTypeMetaData($type);
            $custom_view = Category::$CATEGORY_TYPES[$type]['views']['add'];
            $this->set('title_for_layout',  Category::get_category_types()[$type]);
            $this->Category->breadCrumbsBaseName = $this->pageTitle;
            $this->set('indexTitle', $this->pageTitle);
            $this->set('file_settings',$this->Category->getFileSettings());

            $this->set('categories_list',$this->Category->find('list',$conditions));
            $custom_view ? $this->render($custom_view) : null;
        } else {
            $this->cakeError('error404', array('message' => __(sprintf ('Invalid %s', 'category'),true)));
        }
    }

    function owner_edit($id = null) {
        $this->Category->bindAttachmentRelation('category');

        if(!check_permission(PermissionUtil::Edit_Delete_all_Products)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('category',true)));
            $this->redirect(array('action' => 'index','?'=>['type'=> Category::CATEGORY_TYPE_PRODUCT]));
        }

        $type = (isset($_GET['type']) && '' != trim($_GET['type'])) ? $_GET['type'] : null; 

        $conditions['Category.id'] = $id;

        if ($type) {
            $conditions['Category.category_type'] = $_GET['type'];
        }
        $applyBranchSave = true;
        $applyBranchFind = true;
        if ($type != Category::CATEGORY_TYPE_PRODUCT) {
            $applyBranchSave = false;
            $applyBranchFind = false;
        }
        $this->Category->applyBranch['onFind'] = $applyBranchFind;
        $category = $this->Category->find('first', ['conditions' => $conditions]);

        if (!empty($this->data)) {
            if ($this->data['Category']['status'] == Category::POS_DEVICE_ACTIVE && !empty($category['Category']['mac_address'])) {
                $this->handleSiteLimit(
                    checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::POS_TERMINALS),
                    'index/3'
                );
            }
            $this->Category->applyBranch['onSave'] = $applyBranchSave;
            if ($this->Category->save($this->data)) {
                $this->data['Category']['id'] = $this->Category->id;

                $attachments = $this->data['Category']['attachments'];
                $this->attachS3imageToCategory($attachments,$this->data['Category']['id']);
                // remove normal attachment .
                if(!empty($attachments)){
                    $this->Category->saveField('image', '');
                }

                if (IS_REST) {
                    echo json_encode([
                        "result" => "success",
                        "code" => 202,
                        "Category" => $this->data["Category"]
                    ]);
                    die;
                }
                $this->flashMessage(sprintf(__('%s updated', true), __('category',true)), 'Sucmessage');
                $this->redirect(array('action' => 'index','?'=>['type'=> $this->data['Category']['category_type']]));
            }
        }
        if (empty($this->data)) {
            $this->data = $category;
        }
        $type = $category['Category']['category_type'];
        $this->set('title_for_layout',  Category::get_category_types()[$type]);
        $this->Category->breadCrumbsBaseName = $this->pageTitle;
        $this->set('indexTitle', $this->pageTitle);
        $this->set('type', $type);
        $this->set('action','edit/'.$id.'?type='.$type);
	    $this->loadTypeMetaData($type);
        $custom_view = Category::$CATEGORY_TYPES[$type]['views']['edit'];
		$this->set('file_settings',$this->Category->getFileSettings());

        $this->set('categories_list',$this->Category->find('list',['conditions'=>['Category.category_type'=> $category['Category']['category_type'], 'Category.id <>'=>$category['Category']['id']]]));
        $custom_view ? $this->render($custom_view) : $this->render('owner_add');
    }

    function owner_delete($type = 1, $id = null) {

        if(!check_permission(PermissionUtil::Edit_Delete_all_Products)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->set('type', $type);

        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('category',true))]);
            $this->flashMessage(sprintf (__('Invalid %s', true), __('category',true)) );
            $this->redirect(array('action' => 'index', $type));
        }
        $module_name= __('category', true);
        $verb = __('has', true);
        if(count((array)$id) > 1){
            $verb = __('have', true);
            $module_name= __('categories', true);
        }
        $applyBranchFind = true;
        if ($type != Category::CATEGORY_TYPE_PRODUCT) {
            $applyBranchFind = false;
        }
        $this->Category->applyBranch['onFind'] = $applyBranchFind;
        $categories = $this->Category->find('all', ['conditions' => [
            'Category.category_type' => $type,
            'Category.id' => $id,
        ]]);
        if (empty($categories)){
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), $module_name)]);
            $this->flashMessage( sprintf(__('Error deleting the %s', true), __('category',true)) );
            $this->redirect(array('action' => 'index', $type));
        }

        try {
            $this->Category->canCategoryBeDeleted($id, $categories[0]['Category']['category_type']);
        } catch (Exception $e) {
            if(IS_REST) return $this->cakeError("error400", ["message"=> $e->getMessage()]);
            $this->flashMessage($e->getMessage());
            $this->redirect($this->referer(array('action' => 'index')));
        }

		if(IS_REST){
			$_POST['submit_btn'] = true;
			$_POST['ids'] = [$id];
		}
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->Category->deleteAll(['Category.id'=>$_POST['ids']])) {
				// This is needed becuase deleteAll doesn't call afterDelete on $Model->afterDelete() for some reason
				$this->Category->updateLastUpdatedAtForCategories($categories[0]['Category']['category_type']);
                $this->loadModel('ItemsCategory');
                $this->ItemsCategory->deleteAll(array('ItemsCategory.category_id'=>$_POST['ids']));

                EntityAttachment::where(['entity_key' => 'category'])->whereIn('entity_id' , $_POST['ids'])->delete();

				if(IS_REST){
					$this->set("message", sprintf(__('%s %s been deleted', true), ucwords($module_name), $verb));
					$this->render("success");
					return;
				}
                $this->flashMessage(sprintf (__('%s has been deleted', true), $module_name), 'Sucmessage');
                $this->redirect(array('action' => 'index', $type));
            }
            else{
				if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect(array('action'=>'index'));
            }
        }
        $this->set('categories',$categories);
    }

	function owner_json_find() {

        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        if (!empty($value)) {

            $value = $this->Category->getDataSource()->value('%'.$value.'%','string');
			$conditions = ['Category.name LIKE '.$value ];
			if($categoryType =  $_GET['cat_type'])
			{
				$conditions['Category.category_type'] = $categoryType;
			}
			$result  = $this->Category->find('list',['conditions' => $conditions]);
            if (isset($_GET['format_response']) && $_GET['format_response']) {
                $response = [];
                foreach ($result as $key => $category) {
                    $response[] = [
                        'id' => $key,
                        'text' => $category
                    ];
                }
                echo json_encode($response);
                die();
            }
            echo json_encode($result);
            die();
        }else {

            echo json_encode(array());
            die();
        }
    }

    function owner_json_find_advanced_search() {

        $conditions = [];

        if (isset($_GET['type'])) {
            $conditions[] = ['Category.category_type' => $_GET['type']];
        }

        $name = $_GET['name'];
        if (!empty($name)) {
            $conditions[] = ['Category.name LIKE "%'.$name.'%"'];
        }

        $parent_category = $_GET['parent_category'];
        if (!empty($parent_category)) {
            $conditions[] = [
                'Category.parent_id IS NULL',
                'Category.name LIKE "%'.$parent_category.'%"'
            ];
        }

        $categories = $this->Category->find('all', ['conditions' => $conditions, 'fields' => ['id', 'name']]);

        if (!empty($categories)) {
            $categories = array_column($categories, 'Category');
            echo json_encode($categories);
            die();
        } else {
            echo json_encode(array());
            die();
        }
    }

    private function loadTypeMetaData($type) {
	    if(!Category::$CATEGORY_TYPES[$type]['load_models_from_callback']){
		    foreach (Category::$CATEGORY_TYPES[$type]['metadata'] as $key => $metadata_model) {
			    if (!empty($metadata_model)) {
				    $this->loadModel($metadata_model);
				    if (Category::$CATEGORY_TYPES[$type]['metadata_fields'][$key])
					    $metadata_value = $this->{$metadata_model}->find('list', [
						    'conditions' => [Category::$CATEGORY_TYPES[$type]['metadata_conditions'][$key]],
						    'fields' => Category::$CATEGORY_TYPES[$type]['metadata_fields'][$key]
					    ]);
				    else
					    $metadata_value = $this->{$metadata_model}->find('list', [
						    'conditions' => [Category::$CATEGORY_TYPES[$type]['metadata_conditions'][$key]]
					    ]);
				    $this->set("{$metadata_model}", $metadata_value);
			    }
		    }
	    }
	    if(!empty(Category::$CATEGORY_TYPES[$type]['callback']) && method_exists($this,Category::$CATEGORY_TYPES[$type]['callback'])){
		    $this->{Category::$CATEGORY_TYPES[$type]['callback']}();
	    }
    }

    function owner_toggle_status($id = null){
        $type = (isset($_GET['type']) && '' != trim($_GET['type'])) ? $_GET['type'] : null;
        $category = $this->Category->find('first', ['conditions' => ['Category.category_type' => $type, 'Category.id' => $id]]);
        
        if (!$id || $_GET['type'] != Category::CATEGORY_TYPE_POS_Device || !$category) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('category',true)));
            $this->redirect(array('action' => 'index','?'=>['type'=> Category::CATEGORY_TYPE_POS_Device]));
        }

        if ($type == Category::CATEGORY_TYPE_POS_Device && $category['Category']['status'] == Category::POS_DEVICE_INACTIVE && !empty($category['Category']['mac_address'])) {
            $this->handleSiteLimit(
                checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::POS_TERMINALS),
                'index/3'
            );
        }

        $category['Category']['status'] = !$category['Category']['status'];

        if ($this->Category->save($category)){
            $this->flashMessage(sprintf(__('%s updated', true), __('category',true)), 'Sucmessage');
        } else {
            $this->flashMessage(sprintf(__("Couldn't update", true)));
        }

        $this->redirect(array('action' => 'index','?'=>['type'=> Category::CATEGORY_TYPE_POS_Device]));
    }

    private function attachS3imageToCategory($attachments, $entity_id)
    {
        if (IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors" => __("Attachments must be a string separate by ,", true)]);

        $imagesIds = explode(',', $attachments);
        if (!empty($imagesIds)) {
            return  izam_resolve(AttachmentsService::class)->save('category', $entity_id, $imagesIds);
        }
    }

    public function api_update_display_order($type = null) {
        $data = json_decode(file_get_contents('php://input'), true);
        if (empty($type)) {
            $this->cakeError("error400", ["message" => __("Type is required", true)]);
        }
        foreach ($data as $key => $category_id) {
            if(!is_numeric($key) || !is_numeric($category_id)) {
                $this->cakeError("error400", ["message" => __("Invalid data", true)]);
            }
            $this->Category->updateAll(['display_order' => $key], ['id' => $category_id, 'category_type' => $type]);
        }
        $this->set('message', __('Display order updated', true));
        $this->set('status', 'success');
        $this->render('success');
    }

    public function api_update_category_items($type = null) {
        $data = json_decode(file_get_contents('php://input'), true);
        if (empty($type)) {
            $this->cakeError("error400", ["message" => __("Type is required", true)]);
        }
        $this->loadModel('ItemsCategory');
        foreach ($data as $category_id => $items) {
            if(!is_numeric($category_id)) {
                $this->cakeError("error400", ["message" => __("Invalid data", true)]);
            }
            $this->ItemsCategory->deleteAll(['item_type' => $type, 'category_id' => $category_id]);
            foreach ($items as $key => $item_id) {
                if(!is_numeric($key) || !is_numeric($item_id)) {
                    $this->cakeError("error400", ["message" => __("Invalid data", true)]);
                }
                $updated_data[] = ['item_type' => $type, 'category_id' => $category_id, 'item_id' => $item_id, 'display_order' => $key];
            }
        }
        $this->ItemsCategory->saveAll($updated_data);
        $this->set('message', __('Display order updated', true));
        $this->set('status', 'success');
        $this->render('success');
    }

}

