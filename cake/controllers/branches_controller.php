<?php

use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
	use Izam\Daftra\Common\Utils\SettingsUtil;
	use Izam\Limitation\Utils\LimitationUtil;

class BranchesController extends AppController {

	var $name = 'Branches';
    var $components = array('RequestHandler');

	/**
	 * @var Branch
	 */
	var $Branch;
	var $helpers = array('Html', 'Form');
    public function beforeFilter()
    {
        if(!ifPluginActive(BranchesPlugin)){
            if(IS_REST){
                return $this->cakeError("error403", ["message" => "Branches Plugin Is not enabled"]);
            }
            $this->flashMessage(__("Branches Plugin Is not enabled", true));
            $this->redirect($this->referer());
        }
        parent::beforeFilter();
    }
	function owner_index() {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->set('title_for_layout',  __('Branches',true));
		$this->Branch->recursive = 0;
		$conditions = $this->_filter_params();
        $branches = $this->paginate('Branch', $conditions);
		$this->set('branches', $this->paginate('Branch', $conditions));
		$this->set('primaryBranchID',settings::getValue(BranchesPlugin, 'main_branch'));
        if(IS_REST){
            $this->set('rest_items', $this->paginate('Branch', ['Branch.id' => array_keys(getStaffBranchesSuspended())]));
            $this->set('rest_model_name', "Branch");
            $this->render("index");
        }
	}

	function owner_view($id = null) {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->redirect(array('action'=>'edit', $id));
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('branch', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$branch = $this->Branch->read(null, $id);
        $this->set('title_for_layout',  __($branch['Branch']['name'],true));
        $this->set('branch', $branch);
	}

	function owner_add() {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->set('title_for_layout',  sprintf(__('Add %s',true),__('Branch',true)));
	    $site_id = getCurrentSite('id');
	    $this->loadModel('Site');

        if (checkIfLimitExistsV2(getCurrentSite('id'), LimitationUtil::BRANCHES_COUNT)) {
            $this->handleSiteLimit(
                checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::BRANCHES_COUNT)
            );
        } elseif (checkIfLimitExistsV2(getCurrentSite('id'), LimitationUtil::USER_BRANCH_COUNT)) {
            $this->handleSiteLimit(
                checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::USER_BRANCH_COUNT)
            );
        } else {
            $check = $this->Site->check_add_branch_limit($site_id);
            if (!$check['status']) {
                $this->_flashLimitMessage($check['message'], __('branches', true));
                $this->redirect(array('action' => 'index'));
            }
        }
        App::import('Vendor', 'AutoNumber');
		if (!empty($this->data)) {
            \AutoNumber::set_validate(\AutoNumber::TYPE_BRANCHES);
            //set number if new client and he didn't change or number is empty
            if(empty($this->data['Branch']['code'])||(empty($this->data['Branch']['id'])&&$this->data['Branch']['code']==$this->data['Branch']['default_code'])){
                $this->data['Branch']['default_code'] = $this->data['Branch']['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_BRANCHES);
                $generated_number = true;
            }
			$this->Branch->create();
			$this->data['Branch']['staff_id'] = getAuthOwner('staff_id');
            if(!empty($this->data['Branch']['longitude']) && !empty($this->data['Branch']['latitude']) && !empty($this->data['Branch']['saved_map_zoom'])){
                $this->data['Branch']['map_location'] = $this->data['Branch']['longitude'] . "," . $this->data['Branch']['latitude'] . "," . $this->data['Branch']['saved_map_zoom'] ;
                unset($this->data['Branch']['longitude']);
                unset($this->data['Branch']['latitude']);
                unset($this->data['Branch']['saved_map_zoom']);
            }
            $canSave = true;
            if ($this->Branch->find('count',['conditions' => ['Branch.name' => $this->data['Branch']['name']]])) {
                if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('The %s could not be saved because the name and code must be unique', true), __('branch',true))]);
                $this->flashMessage(sprintf (__('The %s could not be saved because the name and code must be unique', true), __('branch',true)));
                $this->Branch->validationErrors = ['name' => __('this branch name already exist',true)];
                $canSave = false;
            }
            if ($this->Branch->find('count',['conditions' => ['Branch.code' => $this->data['Branch']['code']]])) {
                if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('The %s could not be saved because the name and code must be unique', true), __('branch',true))]);
                $this->flashMessage(sprintf (__('The %s could not be saved because the name and code must be unique', true), __('branch',true)));
                $this->Branch->validationErrors['code'] = __('this branch code already exist',true);
                $canSave = false;
            }
			if ($canSave && $this->Branch->save($this->data)) {
                if(!empty($generated_number)) \AutoNumber::update_auto_serial(\AutoNumber::TYPE_BRANCHES);
                elseif($this->data['Branch']['code']!=$this->data['Branch']['default_code']) \AutoNumber::update_last_from_number($this->data['Branch']['code'],\AutoNumber::TYPE_BRANCHES);

                if (settings::getValue(InventoryPlugin, 'enable_tracking_numbers')) {
	                $this->loadModel('Setting');
                    $enable_tracking_numbers = [
                        'Setting' => [
                            'plugin_id' => InventoryPlugin,
                            'key' => 'enable_tracking_numbers',
                            'value' => 1,
                            'branch_id' => $this->Branch->getLastInsertID()
                        ]
                    ];
                    $this->Setting->create();
                    $this->Setting->save($enable_tracking_numbers);
                }
                
                if(IS_REST){
					$this->set('id', $this->Branch->id);
					$this->render('created');
					return;
				}
                $this->flashMessage(sprintf (__('The %s has been saved', true), __('branch',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else if ($canSave) {
                if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('The %s could not be saved. Please, try again', true), __('branch',true))]);
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('branch',true)));
			}
		}
        $this->data['Branch']['code'] = $this->data['Branch']['default_code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_BRANCHES);
        $this->loadAddEditData();
	}

	function owner_edit($id = null) {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        App::import('Vendor', 'AutoNumber');
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'branch',true)));
			$this->redirect(array('action'=>'index'));
		}
		$branch = $this->Branch->read(null, $id);
		if (!empty($this->data)) {
            \AutoNumber::set_validate(\AutoNumber::TYPE_BRANCHES);
		    unset($this->data['Branch']['status']);
            if(!empty($this->data['Branch']['longitude']) && !empty($this->data['Branch']['latitude']) && !empty($this->data['Branch']['saved_map_zoom'])) {
                $this->data['Branch']['map_location'] = $this->data['Branch']['longitude'] . "," . $this->data['Branch']['latitude'] . "," . $this->data['Branch']['saved_map_zoom'] ;
                unset($this->data['Branch']['longitude']);
                unset($this->data['Branch']['latitude']);
                unset($this->data['Branch']['saved_map_zoom']);
            }
			if ($this->Branch->save($this->data)) {
                if(!empty($generated_number)) \AutoNumber::update_auto_serial(\AutoNumber::TYPE_BRANCHES);
                elseif($this->data['Branch']['code']!=$this->data['Branch']['default_code']) \AutoNumber::update_last_from_number($this->data['Branch']['code'],\AutoNumber::TYPE_BRANCHES);
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('branch',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('branch',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $branch;
            $this->data['Branch']['default_code'] = $this->data['Branch']['code'];
		}
        $this->set('title_for_layout',  sprintf(__('Edit %s',true),$branch['Branch']['name']));
        $this->loadAddEditData();
        $this->render('owner_add');
	}

	function owner_delete($id = null) {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 }
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('branch',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name = __('branch', true);
		if (is_array($id) && count($id) > 1) {
			$module_name = __('branches', true);
		}
		$branches = $this->Branch->find('all',array('conditions'=>array('Branch.id'=>$id)));

        $main_branch = settings::getValue(BranchesPlugin, 'main_branch');

        if ((is_array($id) && in_array($main_branch, $id)) || $id == $main_branch) {
            $this->flashMessage(__('You cannot delete or deactivate the main branch', true));
            $this->redirect(array('action' => 'index'));
        }

		if (empty($branches)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids']) && $_POST['submit_btn'] == 'yes') {
            foreach ($id as $item_id) {
                if (hasTransactions($item_id,HAS_TRANSACTION_TYPE_BRANCHES) && !is_array(hasTransactions($item_id,HAS_TRANSACTION_TYPE_BRANCHES))){
                    $this->flashMessage(sprintf(__('%s #%s can not be deleted because it has transactions, disable it instead', true), $module_name, $item_id));
                    $this->redirect($this->referer(array('action' => 'index'), true));
                }elseif(is_array(hasTransactions($item_id,HAS_TRANSACTION_TYPE_BRANCHES))){  
                     $this->flashMessage(sprintf(__('%s #%s can not be deleted because it has transactions in (%s), disable it instead', true), $module_name, $item_id , __('Activity Logs', true)));
                    $this->redirect($this->referer(array('action' => 'index'), true));
                }
            }
			if ($this->Branch->deleteAll(array('Branch.id'=>$_POST['ids']))) {
			    $this->Branch->afterDelete();
                $this->loadModel('Staff');
                $this->Staff->updateAuthCountByBranchId($id);
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
                $this->redirect(array('action'=>'index'));
			} else {
                $this->flashMessage(__('Item was not deleted. If you see this again, please contact customer support.', true));
                $this->redirect(array('action'=>'index'));
			}
		} else if ($_POST['submit_btn'] == 'no') {
            $this->redirect(array('action'=>'index'));
        }
		$this->set('branches',$branches);
	}

	function owner_toggle_active($id = null, $status = Branch::STATUS_ACTIVE) {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.',true),__('branch',true)));
            $this->redirect(array('action'=>'index'));
        }

        $branch = $this->Branch->read(null, $id);
        if($id == settings::getValue(BranchesPlugin, 'main_branch') && $branch['Branch']['status'] == Branch::STATUS_ACTIVE){
            if ($status == Branch::STATUS_SUSPENDED)  {
                $this->flashMessage(__('You cannot suspend the primary branch and you need to choose another primary then suspend the branch', true));
            } else {
                $this->flashMessage(__('You cannot delete or deactivate the main branch', true));
            }

            $this->redirect(array('action'=>'index'));
        }
        $saved = true;
        if (in_array($status, [Branch::STATUS_SUSPENDED, Branch::STATUS_INACTIVE])) {
            $this->Branch->setBranchStatus($id,$status);
            $this->loadModel('Staff');
            $this->Staff->updateAuthCountByBranchId($id);
        } else {
            if (checkIfLimitExistsV2(getCurrentSite('id'), LimitationUtil::BRANCHES_COUNT)) {
                $this->handleSiteLimit(
                    checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::BRANCHES_COUNT)
                );
                $this->Branch->setBranchStatus($id,Branch::STATUS_ACTIVE);
            } elseif (checkIfLimitExistsV2(getCurrentSite('id'), LimitationUtil::USER_BRANCH_COUNT)) {
                $this->handleSiteLimit(
                    checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::USER_BRANCH_COUNT)
                );
                $this->Branch->setBranchStatus($id,Branch::STATUS_ACTIVE);
            } else {
                $site_id = getCurrentSite('id');
                $this->loadModel('Site');

                $check = $this->Site->check_add_branch_limit($site_id);
                if (!$check['status']) {
                    $saved = false;
                    $this->flashMessage($check['message']);
                } else {
                    $this->Branch->setBranchStatus($id, Branch::STATUS_ACTIVE);
                }
            }
        }
        if ($saved) {
            $array['primary_id'] = $id;
            $array['secondary_id'] = 0;
            $array['param1'] = $branch['Branch']['name'];
            $array['param2'] = $status;
            $this->add_actionline(ACTION_UPDATE_BRANCH, $array);
        }
        $this->redirect(array('action'=>'index'));
    }

    function owner_settings() {

        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!empty($this->data)) {

            if (empty($this->data['settings'][BranchesPlugin]['main_branch'])) {
                $this->flashMessage(__('Main branch should not be empty', true));
                $this->redirect(array('action' => 'settings'));
            }

            $oldSettings['settings'][BranchesPlugin]['main_branch'] = settings::getValue(BranchesPlugin, 'main_branch');
            $oldSettings['settings'][BranchesPlugin]['share_clients'] = settings::getValue(BranchesPlugin, 'share_clients');
            $oldSettings['settings'][BranchesPlugin]['share_products'] = settings::getValue(BranchesPlugin, 'share_products');
            $oldSettings['settings'][BranchesPlugin]['share_suppliers'] = settings::getValue(BranchesPlugin, 'share_suppliers');
            $oldSettings['settings'][BranchesPlugin]['specify_accounts_branches'] = settings::getValue(BranchesPlugin, 'specify_accounts_branches');
	        $oldSettings['settings'][BranchesPlugin]['share_cost_centers'] = settings::getValue(BranchesPlugin, 'share_cost_centers');
            $newSettings = $this->data['settings'];
            settings::setData($this->data['settings']);
            $this->flashMessage(__('Settings have been saved', true), 'Sucmessage');
	        setLastUpdatedAt(PluginUtil::BranchesPlugin, SettingsUtil::BRANCHES_SHARE_SETTINGS_UPDATED_AT);
            $SettingsStructure=settings::getStructure();


            foreach($newSettings[BranchesPlugin] as $key=>$value){
                $options=$SettingsStructure[BranchesPlugin][$key]['options'];

                if ($oldSettings['settings'][BranchesPlugin][$key]!=$value) {
                    $array['primary_id'] = $key;
                    $array['secondary_id'] = 0;
                    $array['param1'] = isset($SettingsStructure[$key][$key]['label']) ? $SettingsStructure[$key][$key]['label'] : __(Inflector::humanize($key), true);



                    $array['param3'] = $options[$oldSettings['settings'][BranchesPlugin][$key]];
                    $array['param2'] = $options[$value];

                    $this->add_actionline(ACTION_UPDATE_BRANCH_SETTINGS, $array);
                }

            }

            $this->Branch->settingsUpdated($oldSettings['settings'][BranchesPlugin], $newSettings[BranchesPlugin]);
        }else{
            $this->data['settings'][BranchesPlugin]['main_branch'] = settings::getValue(BranchesPlugin, 'main_branch');
            $this->data['settings'][BranchesPlugin]['share_clients'] = settings::getValue(BranchesPlugin, 'share_clients');
            $this->data['settings'][BranchesPlugin]['share_products'] = settings::getValue(BranchesPlugin, 'share_products');
            $this->data['settings'][BranchesPlugin]['share_suppliers'] = settings::getValue(BranchesPlugin, 'share_suppliers');
            $this->data['settings'][BranchesPlugin]['specify_accounts_branches'] = settings::getValue(BranchesPlugin, 'specify_accounts_branches');
	        $this->data['settings'][BranchesPlugin]['share_cost_centers'] = settings::getValue(BranchesPlugin, 'share_cost_centers');
        }
        //Get Default Settings


        $this->set('branches',$this->Branch->find('list',['conditions'=>['status' => Branch::STATUS_ACTIVE]]));
    }

    private function loadAddEditData() {
        $this->loadModel('Country');
        $this->set('countryCodes',$this->Country->getCountryListName());
        $this->loadModel('Staff');
        $this->set('adminStaffs',$this->Staff->find('list'));
        $this->loadModel('Site');
        $this->set('default_country', $this->Site->get_site_country());
        //TODO ADD SMTP get list
        $this->set('smtpAccounts',['1' => 'Main SMTP Account']);
        $this->set("geocode_api",$this->config['txt.google_geocode_api']);
        $this->set("google_maps_api",$this->config['txt.google_maps_api']);
        if(!empty($this->data['Branch']['map_location'])){
            $location = explode(",",$this->data['Branch']['map_location'] );
            $this->data['Branch']['longitude'] = $location[0];
            $this->data['Branch']['latitude'] = $location[1];
            $this->data['Branch']['zoom'] = $location[2];
        }
    }


    /**
     * sets a session value to use with specify accounts branch in reports
     * @param $branchId
     */
    function owner_set_report_account_branch($branchId)
    {
        if(getAuthOwner() && settings::getValue(BranchesPlugin, 'specify_accounts_branches')){
            $this->Session->write(ACCOUNTS_BRANCH_KEY, $branchId);
        }
        die();
    }

    /**
     * sets a session value to use with specify accounts branch in reports
     * @param $branchId
     */
    function owner_set_report_transactions_branch($branchId)
    {
        $this->RequestHandler->respondAs('json');
        if(ifPluginActive(BranchesPlugin)){
            $this->Session->write(BRANCH_TRANSACTIONS_KEY, $branchId);
            return die(json_encode(['code' => 200, 'message' => 'transactions branch changed']));

        }
        return die(\GuzzleHttp\json_encode(['code' => 401, 'message' => 'not authorized']));
    }

	public function owner_all() {
		if (ifPluginInstalled(BranchesPlugin)) {
			$this->loadModel('Branch');
			$this->set('rest_items', $this->Branch->getList(false));
		} else {
			$this->set('rest_items', []);
		}
		$this->set('rest_model_name', "Branch");
		return $this->render("index");
	}

	public function api_branches_settings() {
		if (!ifPluginActive(BranchesPlugin)) {
			return $this->cakeError("error403", ["message" => "Branches Plugin Is not enabled"]);
		}
		$oldSettings['main_branch'] = settings::getValue(BranchesPlugin, 'main_branch');
		$oldSettings['share_clients'] = (boolean) settings::getValue(BranchesPlugin, 'share_clients');
		$oldSettings['share_products'] = (boolean) settings::getValue(BranchesPlugin, 'share_products');
		$oldSettings['share_suppliers'] = (boolean) settings::getValue(BranchesPlugin, 'share_suppliers');
		$oldSettings['specify_accounts_branches'] = (boolean) settings::getValue(BranchesPlugin, 'specify_accounts_branches');
		$oldSettings['share_cost_centers'] = (boolean) settings::getValue(BranchesPlugin, 'share_cost_centers');
		$this->set('rest_model_name', 'CustomForm');
		$this->set('rest_item', [
			'data' => $oldSettings,
		]);
		return $this->render("view");
	}
}
