<?php

use Izam\Attachment\Models\EntityAttachment;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Aws\Aws;

class DocumentsController extends AppController {

    var $name = 'Documents';

    /**
     * @var Document
     */
    var $Document;
    var $helpers = array('Html', 'Form');

    function owner_index() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->Document->recursive = 2;
        $conditions = $this->_filter_params();
        // warning suppress
        if (array_key_exists('title', $this->params['url']) && $this->params['url']['title']) {
            $conditions['OR'][]['Document.title LIKE'] = "%" . $this->params['url']['title'] . "%";
            $conditions['OR'][]['Document.file LIKE'] = "%" . $this->params['url']['title'] . "%";
            unset($conditions['Document.title LIKE']);
        }
        $this->Document->bindAttachmentRelation('document');

        $this->set('documents', $this->paginate('Document', $conditions));

        $this->set('title_for_layout',  __('Document', true));
        $this->set('content', $this->get_snippet('documents'));
    }

    function owner_ajax() {
        $rows = $this->Document->getInvoiceDocumentList();
        echo json_encode($rows);
        die();
    }

    function owner_view($id = null) {
//        if (!check_permission(Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('document', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $this->Document->bindAttachmentRelation('document');
        $document = $this->Document->read(null, $id);
        if(!$document){
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('document', true)), true));
             $this->redirect(array('action' => 'index'));   
         }
        // Used to check if documnet uploaded with normal way || s3 .
         if(!count($document['Attachments'])){
         $file_path='files' . DS . SITE_HASH . DS . 'documents' . DS.$document['Document']['file'];
         $ext=strtolower(end(explode('.', $file_path)));
         $document['Document']['size']=filesize($file_path);
         $document['Document']['ext']=$ext;
         $document['Document']['path'] = $document['Document']['file_full_path'];
         }else{
            $document['Document']['size'] = $document['Attachments'][0]['file_size'];
            $document['Document']['ext']  = $document['Attachments'][0]['mime_type'];
            $document['Document']['path'] = "/v2/owner/entity/files/preview/" . $document['Attachments'][0]['id'];
        }
        echo json_encode($document['Document']);
        die();
        $this->set('document', $document);
    }

    function owner_preview($id = null) {
//        if (!check_permission(Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('document', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $this->Document->bindAttachmentRelation('document');

        $document = $this->Document->read(null, $id);
        if(!$document){
           $this->flashMessage(__(sprintf(__('Invalid %s', true), __('document', true)), true));
            $this->redirect(array('action' => 'index'));   
        }
        $url = $document['Document']['file_full_path'];
         
        if(!empty($document['Attachments'])){
            $url =  Aws::getProxyUrl($document['Attachments'][0]['path']);
        }

        $this->redirect($url);
    }

    function owner_add() {
//        if (!check_permission(Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $site = getAuthOwner();
        $this->loadModel('Site');
        $check = $this->Site->check_add_document_limit();
        if (!$check['status']) {
            $this->_flashLimitMessage($check['message'], __('documents', true));
            $this->redirect(array('action' => 'index'));
        }
        if (!empty($this->data)) {
            $this->Document->create();
            $this->data['Document']['site_id'] = getAuthOwner('id');
            if ($this->Document->save($this->data)) {

                $attachments = $this->data['Document']['attachment'];
                $attachmentsid = explode(',', $attachments);
                izam_resolve(AttachmentsService::class)->save('document', $this->Document->id, $attachmentsid); 

                if ($this->data['Document']['ajax'] == 1) {
                    $row = $this->Document->read(null, $this->Document->id);
                    //print_r($row);
                    // echo $this->Document->id;
                    $file_path='files' . DS . SITE_HASH . DS . 'documents' . DS.$row['Document']['file'];
                    $file_path_arr = explode('.', $file_path);
                    $ext=strtolower(end($file_path_arr));
                    die(json_encode(array('ext'=>$ext,'title' => $row['Document']['title'],'size'=>filesize($file_path), 'path' => $row['Document']['file_full_path'], 'status' => true, 'id' => $this->Document->id)));
                }
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Document', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                // Used To get attachments details in case of Validation error occurred. 
                if(!empty($this->data['Document']['attachment'])){                       
                    $filesId = explode(',',$this->data['Document']['attachment']);
                    $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                    $this->data['Attachments'] = $attachment;
                  }

                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Document', true)));
            }
        }
        $this->set('file_settings', $this->Document->getFileSettings());

        $this->set('title_for_layout',  __('Add Document', true));
    }

    function owner_edit($id = null) {
//        if (!check_permission(Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $site = getAuthOwner();
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), __('document', true)));
            $this->redirect(array('action' => 'index'));
        }
        $owner_id = getAuthOwner('id');
        $this->Document->bindAttachmentRelation('document');
        $document = $this->Document->read(null, $id);
        $this->loadModel('Site');
        if (!empty($this->data)) {
            if (empty($this->data['Document']['id'])) {
                $check = $this->Site->check_add_document_limit();
                if (!$check['status']) {
                    $this->_flashLimitMessage($check['message'], __('documents', true));
                    $this->redirect(array('action' => 'index'));
                }
            }
            if ($this->Document->save($this->data)) {

                $attachments = $this->data['Document']['attachment'];
                $attachmentsid = explode(',', $attachments);
                izam_resolve(AttachmentsService::class)->save('document', $this->data['Document']['id'], $attachmentsid); 

                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Document', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                   // Used To get attachments details in case of Validation error occurred. 
                   if(!empty($this->data['Document']['attachment'])){                       
                    $filesId = explode(',',$this->data['Document']['attachment']);
                    $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                    $this->data['Attachments'] = $attachment;
                  }
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Document', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $document;
        }
        $this->set('file_settings', $this->Document->getFileSettings());
        $this->set('title_for_layout',  __('Edit Document', true));
        $this->render('owner_add');
    }

    function owner_delete($id = null) {
//        if (!check_permission(Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $site = getAuthOwner();
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('document', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('document', true);
        if (is_countable($id) && count($id) > 1) {
            $module_name = __('documents', true);
        }
        $conditions = array();
        $conditions['Document.site_id'] = getAuthOwner('id');
        $conditions['Document.id'] = $id;
        $documents = $this->Document->find('all', array('conditions' => $conditions));
        if (empty($documents)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->Document->deleteAll(array('Document.id' => $_POST['ids']))) {
                EntityAttachment::where('entity_key','document')->whereIn('entity_id' , $_POST['ids'])->delete();
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  __('Delete Document', true));

        $this->set('documents', $documents);
        $this->set('module_name', $module_name);
    }

    function owner_json_find() {
        $result = array();
        $value = trim($_GET['q']);
        if (empty($value)) {
            echo json_encode($result);
            die();
        }
        $conditions['OR'][]['Document.title like'] = "%$value%";
        $conditions['OR'][]['Document.file like'] = "%$value%";
        $documents = $this->Document->find('all', array('conditions' => $conditions));
        foreach ($documents as $doc) {
            $result[] = array(
                'name' => ($doc['Document']['title']),
                'id' => $doc['Document']['id'],
                'file' => $doc['Document']['file_full_path']
            );
        }
        echo json_encode($result);
        die();
    }

 function download($id = null,$DownloadModel='InvoiceDocument') {
     $staff_id=  getAuthStaff('id');
     
     $client=  getAuthClient();
if($DownloadModel=='PurchaseOrderDocument') {
    $this->loadModel('PurchaseOrderDocument');
    $row = $this->PurchaseOrderDocument->read(null, $id);
}else{
    $this->loadModel('InvoiceDocument');
    $row = $this->InvoiceDocument->read(null, $id);
}
   //  echo "<pre>";print_r($row);
    // die();
     
     
     
        $this->loadModel('Staff');
        //$row = $this->Document->read(null, $id);

        if (!$row) {
            $this->flashMessage(__('File Not Found', TRUE));
            $this->redirect('/');
        }
        
        $path = WWW_ROOT . "files/" . SITE_HASH . "/documents/" . $row['Document']['file'];
        
        if (!file_exists($path)) {
                $this->flashMessage(__('File Not Found', TRUE));
                $this->redirect('/');            
        }
        if ($staff_id) {
             if ((!check_permission(Invoices_View_All_Invoices) && $row['Invoice']['staff_id'] != $staff_id) || !check_permission(Invoices_View_Invoices_Details)) {
                $this->flashMessage(__("You are not allowed to view this file", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
            
            $staff = $this->Staff->read(null, $staff_id);
            $this->add_actionline(STAFF_DOWNLOAD_INVOICE_DOCUMENT, array('staff_id'=>$staff_id,'secondary_id' => $id, 'primary_id' => $row['Invoice']['id'], 'param1' => $staff['Staff']['name'],'param3'=>$row['Document']['file'],'param4'=>filesize($path),'param5'=>$row['Document']['id'],'param6'=>$row['Invoice']['no']));

           
        }
        
        if ($client) {
            
            $this->add_actionline(CLIENT_DOWNLOAD_INVOICE_DOCUMENT, array('secondary_id' => $client['id'], 'primary_id' => $id, 'param1' => $client['business_name'],'param2' => $client['client_number'],'param3'=>$row['Document']['file'],'param4'=>filesize($path),'param5'=>$row['Document']['id'],'param6'=>$row['Invoice']['no']));
            
        }
        
          if (!$staff_id and !$client) {
               
            //$staff = $this->Staff->read(null, $id);
            $this->add_actionline(STAFF_DOWNLOAD_INVOICE_DOCUMENT, array('staff_id'=>$staff_id,'secondary_id' => $staff_id, 'primary_id' => $id, 'param1' => getAuthOwnerName(),'param3'=>$row['Document']['file'],'param4'=>filesize($path),'param5'=>$row['Document']['id'],'param6'=>$row['Invoice']['no']));
         
        }
          if (in_array($DownloadModel, ['InvoiceDocument'])) {
              $extension = pathinfo($row['Document']['file'], PATHINFO_EXTENSION);
              $fileName = pathinfo($row['Document']['title'], PATHINFO_FILENAME);
              $file_name = sprintf("%s.%s", $fileName, $extension);
          } else {
              $file_name = $row['Document']['file'];
          }
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename=' . $file_name);
        header("Content-Length: " . filesize($path));
        readfile($path);
        die();
    }
 function download_doc($id = null) {
     if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
     $staff_id=  getAuthStaff('id');
     
     $row=$this->Document->read(null,$id);
   //  echo "<pre>";print_r($row);
    // die();
     
     
     
        $this->loadModel('Staff');
        //$row = $this->Document->read(null, $id);

        if (!$row) {
            $this->flashMessage(__('File Not Found', TRUE));
            $this->redirect('/');
        }
        
        $path = WWW_ROOT . "files/" . SITE_HASH . "/documents/" . $row['Document']['file'];
        
        if (!file_exists($path)) {
                $this->flashMessage(__('File Not Found', TRUE));
                $this->redirect('/');            
        }
        if ($staff_id) {
            $staff = $this->Staff->read(null, $staff_id);
            $this->add_actionline(STAFF_DOWNLOAD_INVOICE_DOCUMENT, array('staff_id'=>$staff_id,'secondary_id' => $id, 'primary_id' => $row['Invoice']['id'], 'param1' => $staff['Staff']['name'],'param3'=>$row['Document']['file'],'param4'=>filesize($path),'param5'=>$row['Document']['id'],'param6'=>$row['Invoice']['no']));

           
        }
       
          if (!$staff_id) {
               
            //$staff = $this->Staff->read(null, $id);
            $this->add_actionline(STAFF_DOWNLOAD_INVOICE_DOCUMENT, array('staff_id'=>$staff_id,'secondary_id' => $staff_id, 'primary_id' => $id, 'param1' => getAuthOwnerName(),'param3'=>$row['Document']['file'],'param4'=>filesize($path),'param5'=>$row['Document']['id'],'param6'=>$row['Invoice']['no']));
         
        }
        
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $row['Document']['file'].'"');
        header("Content-Length: " . filesize($path));
        readfile($path);
        die();
    }

    function addBreadCrumbs($model, $url, $data = [])
    {
        App::import('vendor','BreadCrumbs',['file'=>'BreadCrumbs/autoload.php']);
        $tempUrl = [];
        foreach($url as $k => $v) {
            $tempUrl[$k] = $v;
        }
        $breadCrumbs = \BreadCrumbs\BreadCrumbs::generateBreadCrumbs($model, $tempUrl, $data);
        if($breadCrumbs) {           
            $formattedBC = [];
            foreach($breadCrumbs as $k => $v) {
                foreach($v as $link => $title) {
                    if($k == 1){
                        $link = explode('/',$link);
                        $link = "/v2/owner/entity/files/preview/".$this->getFileId(end($link));
                        }
                    $formattedBC[] = ['link' => $link, 'title' => __($title,true)];
                }
            }
            $this->set('_PageBreadCrumbs', $formattedBC);
        }
    }

    private function getFileId($entityid){
        $attachment = izam_resolve(AttachmentsService::class)->getAttachments('document', $entityid)->toarray(); 
        $attachment = array_shift($attachment);
        return $attachment['files']['id'];
    }
}
