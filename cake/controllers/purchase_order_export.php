<?php

use Izam\Attachment\Service\AttachmentsService;

$this->loadModel('PurchaseOrderItem');
$this->set('conditions_key', $conditions_key);

set_time_limit(3600);
ini_set('memory_limit', '20G');

$order_list = array(
	'date_desc' => array('title' => __('Date', true) . ' (' . __('Recent First', true) . ')', 'fields' => 'PurchaseOrder.date DESC, PurchaseOrder.id DESC'),
	'date_asc' => array('title' => __('Date', true) . ' (' . __('Oldest First', true) . ')', 'fields' => 'PurchaseOrder.date , PurchaseOrder.id '),
	'no_desc' => array('title' => __('Number', true) . ' (' . __('Biggest First', true) . ')', 'fields' => 'PurchaseOrder.no DESC'),
	'no_asc' => array('title' => __('Number', true) . ' (' . __('Smallest First', true) . ')', 'fields' => 'PurchaseOrder.no'),
	'amount_desc' => array('title' => __('Total', true) . ' (' . __('Largest First', true) . ')', 'fields' => 'PurchaseOrder.summary_total DESC'),
	'amount_asc' => array('title' => __('Total', true) . ' (' . __('Lowest First', true) . ')', 'fields' => 'PurchaseOrder.summary_total'),
	'status_paid_first' => array('title' => __('Status', true) . ' (' . __('Paid First', true) . ')', 'fields' => 'PurchaseOrder.payment_status DESC'),
	'amount_unpaid_first' => array('title' => __('Status', true) . ' (' . __('Unpaid First', true) . ')', 'fields' => 'PurchaseOrder.payment_status'),
);

$fields_list = array(
	'id' => array('title' => "ID", 'field' => 'PurchaseOrder.id'),
	'no' => array('title' => "$purchase_order_type_name Number", 'field' => 'PurchaseOrder.no'),
	'Supplier_no' => array('title' => 'Supplier Number', 'field' => 'Supplier.Supplier_number'), //Supplier.no
	'date' => array('title' => "$purchase_order_type_name Date", 'field' => ('PurchaseOrder.date')),
	'issue_date' => array('title' => 'Issue Date', 'field' => ('PurchaseOrder.issue_date')),
	'payment_status' => array('title' => 'Payment Status', 'field' => ('PurchaseOrder.payment_status')), //Switched to the correct value PurchaseOrder::getPaymentStatuses
	'currency_code' => array('title' => 'Currency', 'field' => ('PurchaseOrder.currency_code')),
	'Supplier_business_name' => array('title' => 'Supplier Business Name', 'field' => ('Supplier.business_name')),
	'Supplier_first_name' => array('title' => 'Supplier First Name', 'field' => ('Supplier.first_name')),
	'Supplier_last_name' => array('title' => 'Supplier Last Name', 'field' => ('Supplier.last_name')),
	'Supplier_full_name' => array('title' => 'Supplier Full Name', 'field' => ("CONCAT(Supplier.business_name, ' (' , Supplier.first_name , ' ',Supplier.last_name , ')' ) ")),  //Business Name (First + Last) Or Business Name 
	'Supplier_email' => array('title' => 'Supplier Email', 'field' => ("Supplier.email")),
	'Supplier_phone1' => array('title' => 'Supplier Phone1', 'field' => ("Supplier.phone1")),
	'Supplier_phone2' => array('title' => 'Supplier Phone2', 'field' => ("Supplier.phone2")),
	'Supplier_address1' => array('title' => 'Supplier Line Address1', 'field' => ('Supplier.address1')),
	'Supplier_address2' => array('title' => 'Supplier Line Address2', 'field' => ('Supplier.address2')),
	'Supplier_postal_code' => array('title' => 'Supplier Postal Code', 'field' => ('Supplier.postal_code')),
	'Supplier_city' => array('title' => 'Supplier City', 'field' => ('Supplier.city')),
	'Supplier_state' => array('title' => 'Supplier State', 'field' => ('Supplier.state')),
	'Supplier_full_address' => array('title' => 'Supplier Full Address', 'field' => ('CONCAT(Supplier.address1,"\n",Supplier.address2,"\n",Supplier.city,", ",Supplier.state, ", ",Supplier.postal_code,"\n" , Supplier.country_code)')), //Address 1\n Address 2 \City, State  Postal Code\nCountry Code
	'Supplier_country_code' => array('title' => 'Supplier Country Code', 'field' => ('Supplier.country_code')),
	'Supplier_secondary_name' => array('title' => 'Supplier Shipping Name', 'field' => ('Supplier.secondary_name')),
	'Supplier_secondary_full_address' => array('title' => 'Supplier Full Shipping Address', 'field' => ('CONCAT(Supplier.secondary_address1,"\n",Supplier.secondary_address2,"\n",Supplier.secondary_city,", ",Supplier.secondary_state, ", ",Supplier.secondary_postal_code,"\n" , Supplier.secondary_country_code)')),     //Address 1\n Address 2 \City, State  Postal Code\nCountry Code
	'Supplier_secondary_country_code' => array('title' => 'Supplier Shipping Country Code', 'field' => ('Supplier.secondary_country_code')),
	'Supplier_secondary_address1' => array('title' => 'Supplier Shipping Address1', 'field' => ('Supplier.secondary_address1')),
	'Supplier_secondary_address2' => array('title' => 'Supplier Shipping Address2', 'field' => ('Supplier.secondary_address2')),
	'Supplier_secondary_city' => array('title' => 'Supplier Shipping City', 'field' => ('Supplier.secondary_city')),
	'Supplier_secondary_state' => array('title' => 'Supplier Shipping State', 'field' => ('Supplier.secondary_state')),
	'Supplier_secondary_postal_code' => array('title' => 'Supplier Shipping Postal Code', 'field' => ('Supplier.secondary_postal_code')),
	'staff_id' => array('title' => 'Staff Member ID', 'field' => ('PurchaseOrder.staff_id')), //Staff.id
	'staff_name' => array('title' => 'Staff Member Name', 'field' => ('Staff.name')), //Staff.name
	'draft' => array('title' => 'Is Draft', 'field' => ('PurchaseOrder.draft')), //1=>Yes, 0=> No
	'discount_precentage' => array('title' => 'Discount Percentage', 'field' => ('PurchaseOrder.discount')),
	'deposit' => array('title' => 'Deposit', 'field' => ('PurchaseOrder.deposit')),
	'terms' => array('title' => 'Payment Terms', 'field' => ('PurchaseOrder.terms')),
	'summary_subtotal' => array('title' => 'Subtotal', 'field' => ('PurchaseOrder.summary_subtotal')),
	'summary_discount' => array('title' => 'Discount', 'field' => ('PurchaseOrder.summary_discount')),
	'discount_amount' => array('title' => 'Discount Amount', 'field' => ('PurchaseOrder.discount_amount')),
	'shipping_amount' => array('title' => 'Shipping Fees', 'field' => ('PurchaseOrder.shipping_amount')),
	'summary_total' => array('title' => 'Total', 'field' => ('PurchaseOrder.summary_total')),
	'summary_paid' => array('title' => 'Total Paid', 'field' => ('PurchaseOrder.summary_paid')),
	'summary_unpaid' => array('title' => 'Balance Due', 'field' => ('PurchaseOrder.summary_unpaid')),
	'summary_deposit' => array('title' => 'Deposit', 'field' => ('PurchaseOrder.summary_deposit')),
	'notes' => array('title' => 'notes', 'field' => ('PurchaseOrder.html_notes')), //html_notes
	'from' => array('title' => 'From Date', 'field' => ('PurchaseOrder.from')),
	'to' => array('title' => 'To Date', 'field' => ('PurchaseOrder.to')),
	'created' => array('title' => 'Created', 'field' => ('PurchaseOrder.created')),
	'modified' => array('title' => 'Modified', 'field' => ('PurchaseOrder.modified')),
	'due_after' => array('title' => 'Due date', 'field' => ('PurchaseOrder.due_after')),
);

$item_fields_list = array(
	'item' => array('title' => 'Item', 'field' => ('PurchaseOrderItem.item')),
	'description' => array('title' => 'Description', 'field' => ('PurchaseOrderItem.description')),
	'unit_price' => array('title' => 'Unit Price', 'field' => ('PurchaseOrderItem.unit_price')),
	'quantity' => array('title' => 'Quantity', 'field' => ('PurchaseOrderItem.quantity')),
	'discount' => array('title' => 'Discount', 'field' => ('PurchaseOrderItem.discount')),
	'Tax1 value' => array('title' => 'Tax1 value', 'field' => ('PurchaseOrderItem.summary_tax1')),
	'Tax1 name' => array('title' => 'Tax1 name', 'field' => ('PurchaseOrderTax.tax1')),
	'Tax2 value' => array('title' => 'Tax2 value', 'field' => ('PurchaseOrderItem.summary_tax2')),
	'Tax2 name' => array('title' => 'Tax2 name', 'field' => ('PurchaseOrderTax.tax2')),
	'subtotal' => array('title' => 'Subtotal', 'field' => ('PurchaseOrderItem.subtotal')),
	'product_id' => array('title' => 'Product ID', 'field' => ('PurchaseOrderItem.product_id')),
	'product_code' => array('title' => 'Product Code', 'field' => ('PurchaseOrderItem.product_code')),

);

if (!empty($conditions_key)) {
	$conditions = $this->Session->read($conditions_key);
}


if (isset($_POST['ids']) && $_POST['ids']) {


	$this->set('ids', $_POST['ids']);
	$conditions['PurchaseOrder.id'] = !is_array($_POST['ids']) ? explode(',', $_POST['ids']) : $_POST['ids'];
}

if (!empty($this->params['url']['filter_ids'])) {
	$conditions['PurchaseOrder.id'] = explode(',', $this->params['url']['filter_ids']);
} else if (isset($_POST['filter_ids']) && $_POST['filter_ids']) {
	$conditions['PurchaseOrder.id'] = explode(',', $_POST['filter_ids']);
}

$item_columns_list = [];
$itemColumnsNames = [];
$itemColumnsLabels = [];
$conditions['PurchaseOrder.type'] = $purchase_order_type;

$count = $this->PurchaseOrder->find('count', ['recursive' => -1, 'conditions' => $conditions]);

if ($count > 5000) {
	$this->flashMessage(__("You can't export more than 5000 record, Please change filters to match limit.", TRUE));
	return $this->redirect("/owner/purchase_orders");
}

$purchase_orders_item_colums = $this->PurchaseOrder->find('all', ['recursive' => -1, 'conditions' => $conditions + ['PurchaseOrder.item_columns !=' => '']]);
$itemColumns = [];
$i = 0;
foreach ($purchase_orders_item_colums as $item) {
	foreach (json_decode($item['PurchaseOrder']['item_columns'], true) as $key => $item_values) {
		if (is_array($item_values) && !in_array($item_values['label'], $itemColumnsLabels)) {
			$i++;
			$item_columns_list[$i] = $item_values['label'];
			$itemColumnsNames[$i] = $item_values['name'];
			$itemColumnsLabels[$i] = $item_values['label'];
		}
	}
}

$this->set('item_columns_list', $item_columns_list);

$this->loadModel('PurchaseOrderCustomField');
$this->PurchaseOrderCustomField->recursive = -1;
$custom_conditions = []; //conditions for purchase order custom field
foreach ($conditions as $k => $v) {
	if (strpos($k, 'Supplier.') === false) {
		$custom_conditions[$k] = $v; // Removing Supplier conditions from purchase order custom fields
	}
}
$params['conditions'] = $custom_conditions;
if ($conditions["PurchaseOrder.id"]) {
	$alldata = $this->PurchaseOrderCustomField->find('all', ['conditions' => ['purchase_order_id' => $conditions["PurchaseOrder.id"]]]);
} else {
	$alldata = $this->PurchaseOrderCustomField->find('all');
}
$all_custom_fields = [];
foreach ($alldata as $d) {
	$fields_list[$d['PurchaseOrderCustomField']['label']] = ['title' => $d['PurchaseOrderCustomField']['label'], 'field' => 'custom'];
}

if (empty($conditions)) {

	$this->flashMessage(__("Invalid list to export", true), 'Errormessage');
	$this->redirect(array('action' => 'index'));
}
if ($report_template_id != false && $quick_report) {
	$this->loadModel('SavedReport');
	//Get the Data
	$sr = $this->SavedReport->findById($report_template_id);
	$this->data = json_decode($sr['SavedReport']['data'], true);
	if (empty($this->data)) {
		$this->flashMessage(__("Couldn't find this report template", true), 'Errormessage');
		$this->redirect(array('action' => 'export', $conditions_key));
	}
}

// Default post data
$data = $this->data;


/**
 * If data is passed in the url, then we are exporting to a file
 * We support both get and post in the form action
 */

if (!empty($this->params['url']['data']) && !empty($this->params['url']['data']['export_to'])) {
	$data = $this->params['url']['data'];
}

if (!empty($data)) {

	$params['conditions'] = $conditions;
	$params['order'] = 'PurchaseOrder.date DESC, PurchaseOrder.id DESC';
	if (!empty($data['order']) && in_array($data['order'], array_keys($order_list))) {
		$params['order'] = $order_list[$data['order']]['fields'];
	}
	$params['fields'] = [];
	$this->loadModel('Product');

	$unbind = [
		'EmailLog',
		'PurchaseOrderDocument',
	];

	$this->PurchaseOrder->unbindModel(
		['hasMany' => $unbind]
	);
	$products = [];

	$count = $this->PurchaseOrder->find('count', $params);

	if ($count > 5000) {
		$this->flashMessage(__("You can't export more than 5000 record, Please change filters to match limit.", TRUE));
		return $this->redirect("/owner/purchase_orders");
	}
	$this->PurchaseOrder->unbindModel(
		['hasMany' => $unbind]
	);
	$this->PurchaseOrder->recursive = 1;
	$alldata = $this->PurchaseOrder->find('all', $params);

	$ProductImage = GetObjectOrLoadModel('ProductImage');

	foreach ($alldata as &$purchase_order) {
		foreach ($purchase_order['PurchaseOrderItem'] as &$item) {

			if (!empty($item['product_id'])) {
				if (!isset($products[$item['product_id']])) {
					$product = $this->Product->find('first', array('recursive' => -1, 'conditions' => ['Product.id' => $item['product_id']]));
					$products[$item['product_id']] = $product;
				}


				$ProductImageRow = $ProductImage->find('first', ['conditions' => ['ProductImage.product_id' => $item['product_id'], 'ProductImage.default' => 1]]);
				$masterImage = resolve(AttachmentsService::class)->getDefault('product', $item['product_id']);

				$productMasterImage = '';
				if (!empty($ProductImageRow['ProductImage']['file_full_path'])) {
					$productMasterImage = ("https://" . getCurrentSite('subdomain') . '/' . $ProductImageRow['ProductImage']['file_full_path']);
				} elseif (isset($masterImage[0]->files)) {
					$productMasterImage = "https://" . getCurrentSite('subdomain') . "/v2/owner/entity/files/preview/" . ($masterImage[0]->files->id);
				}

				$item['product_image'] = $productMasterImage;
				$item['barcode'] = $products[$item['product_id']]['Product']['barcode'];
				$item['product_code'] = $products[$item['product_id']]['Product']['product_code'];
			}
		}
	}

	//If they didn't select anything .
	if (empty($data['item_fields_select']) && ($data['item_select'] == "items" || $data['item_select'] == "item_columns")) {

		$data['item_fields_select'] = array_keys($item_fields_list);
	}
	if (empty($data['item_columns_select']) && $data['item_select'] == "item_columns") {
		$data['item_columns_select'] = array_keys($item_columns_list);
	}
	if (empty($data['fields_select'])) {
		$data['fields_select'] = array_keys($fields_list);
	}

	$rows = [];

	foreach ($data['fields_select'] as $v) {
		$rows[1][$fields_list[$v]['title']] .= __($fields_list[$v]['title'], true);
	}
	foreach ($data['item_fields_select'] as $v) {
		$rows[1][$item_fields_list[$v]['title']] .= __($item_fields_list[$v]['title'], true);
	}

	foreach ($data['item_columns_select'] as $v) {
		$rows[1][$v] .= __($item_columns_list[$v], true);
	}

	if (isset($_POST['save_as_template']) && $_POST['save_as_template'] == "1") {
		$template_name = $_POST['template_name'];
		$this->loadModel('SavedReport');
		$data['SavedReport'] = ['site_id' => getCurrentSite('id'), 'title' => $template_name, 'type' => PURCHASE_ORDERS_EXPORT, 'data' => json_encode($data)];
		$this->SavedReport->set($data);
		$this->SavedReport->save($data);
	}
	//Putting data for CSV file
	$i = 2;
	App::import('Vendor', 'PlaceHolder');
	foreach ($alldata as $d) {
		$placeholders = [];
		if (count($d['PurchaseOrderCustomField']) > 0) {
			foreach ($d['PurchaseOrderCustomField'] as $customField) {

				if (!empty($placeholders)) {
					break;
				}

				foreach ($customField as $k => $v) {
					preg_match('#{%(.*?)%}#', $v, $matches);
					if (!empty($matches)) {
						/** this can have much performance optimization */
						$placeholders = array_merge(
							PlaceHolder::purchaseorders_get_all_place_holders($d),
							PlaceHolder::purchase_order_payment_place_holder($d['PurchaseOrderPayment'][0] ?? []),
						);
						break;
					}
				}
			}
		}
		$currentTaxes = [];
		foreach ($d['PurchaseOrderTax'] as $t) {
			$currentTaxes[$t['tax_id']] = $t['name'];
		}
		if ($d['PurchaseOrder']['staff_id'] == 0) {
			$d['Staff']['name'] = getCurrentSite('first_name') . " " . getCurrentSite('last_name');
		}
		$d['PurchaseOrder']['html_notes'] = strip_tags($d['PurchaseOrder']['html_notes']);
		$d['PurchaseOrder']['terms'] = strip_tags($d['PurchaseOrder']['terms']);
		foreach ($data['fields_select'] as $k => $v) {
			$field = explode('.',  $fields_list[$v]['field']);

			if ($v == "payment_status") {

				$rows[$i][$fields_list[$v]['title']] = PurchaseOrder::getPaymentStatuses()[$d['PurchaseOrder']['payment_status']];
			} else if ($v == "Supplier_full_name") {
				$rows[$i][$fields_list[$v]['title']] = ($d['Supplier']['first_name'] == "" && $d['Supplier']['last_name'] == "") ?  "{$d['Supplier']['business_name']} ({$d['Supplier']['first_name']} {$d['Supplier']['last_name']})" : $d['Supplier']['business_name'];
			} else if ($v ==  "Supplier_secondary_full_address") {
				$rows[$i][$fields_list[$v]['title']] = "{$d['Supplier']['secondary_address1']}\n{$d['Supplier']['secondary_address2']}\n{$d['Supplier']['secondary_city']}, {$d['Supplier']['secondary_state']},{$d['Supplier']['secondary_postal_code']}\n{$d['Supplier']['secondary_country_code']}";;
			} else if ($v ==  "Supplier_full_address") {
				$rows[$i][$fields_list[$v]['title']] = "{$d['Supplier']['address1']}\n{$d['Supplier']['address2']}\n{$d['Supplier']['city']}, {$d['Supplier']['state']},{$d['Supplier']['postal_code']}\n{$d['Supplier']['country_code']}";
			} else if ($v == "Supplier_no" ) {
    			$rows[$i][$fields_list[$v]['title']] =  $d['Supplier']['supplier_number'];
			} else if ($fields_list[$v]['field']  == "custom") {
				foreach ($d['PurchaseOrderCustomField'] as $cc) {
					if ($cc['label'] == $fields_list[$v]['title']) {
						$rows[$i][$fields_list[$v]['title']] = empty($placeholders) ? $cc['value'] : PlaceHolder::replace($cc['value'], array_keys($placeholders), array_values($placeholders), true);
					}
				}
			} else if ($v == "due_after") {
				$rows[$i][$fields_list[$v]['title']] = date('Y-m-d', strtotime('+' . $d['PurchaseOrder']['due_after'] . ' days', strtotime($d['PurchaseOrder']['date'])));
			} else {
				$rows[$i][$fields_list[$v]['title']] = $d[$field[0]][$field[1]];
			}
		}
		$temp = [];
		foreach ($rows[1] as $key => $value) {
			if (is_array($rows[$i]) && !array_key_exists($key, $rows[$i])) {
				$temp[$key] = '';
			} else {
				$temp[$key] = $rows[$i][$key];
			}
		}
		$rows[$i] = $temp;

		$keymap = [];
		$keymap['field1'] = 'item';
		$keymap['field2'] = 'description';
		$keymap['field3'] = 'col_3';
		$keymap['field4'] = 'col_4';
		$keymap['field5'] = 'col_5';

		if (!empty($data['item_fields_select']) && ($data['item_select'] == "items" || $data['item_select'] == "item_columns")) {

			foreach ($d['PurchaseOrderItem'] as $dinv_item) {
				$rows[$i] = $temp;
				foreach ($data['item_fields_select'] as $v) {

					$field = explode('.', $item_fields_list[$v]['field']);
					$model = $field[0];
					if ($model == "PurchaseOrderTax") {
						$rows[$i][$item_fields_list[$v]['title']] = $currentTaxes[$dinv_item[$field[1]]];
					} else {
						$rows[$i][$item_fields_list[$v]['title']] = $dinv_item[$field[1]];
					}
				}


				foreach ($data['item_columns_select'] as $NewKey => $vcl) {
					$final_name = null;
					$label = $itemColumnsLabels[$vcl];
					$name = $itemColumnsNames[$vcl];
					$decode = json_decode($d['PurchaseOrder']['item_columns'], true);

					foreach ($decode as $keyy => $vvalue) {

						if ($vvalue['name'] == $name) {
							$final_name = $keymap[$keyy];
							break;
						}
					}
					$rows[$i][$vcl] = ($dinv_item[$final_name]) ? $dinv_item[$final_name] : $dinv_item[$name];
				}

				$i++;
			}
		}

		$i++;
	}
	App::import('Vendor', 'csv');
	if ($data['export_to'] == 'xml') {
		$exporter = new ExportDataExcel('browser', 'data.xml');
	} else if ($data['export_to'] == 'csv_semicolon') {
		$exporter = new ExportDataCSV('browser', 'data.csv', ";");
	} else if ($data['export_to'] == 'excel') {
		$exporter = new ExportDataExcel('browser', 'data.xls');
	} else {
		$exporter = new ExportDataCSV('browser', 'data.csv');
	}
	$exporter->initialize();

	foreach ($rows as $r) {
		$exporter->addRow($r);
	}

	$exporter->finalize();
	die;
}
if ($report_template_id) {
	$this->loadModel('SavedReport');
	$sr = $this->SavedReport->findById($report_template_id);
	$this->data = json_decode($sr['SavedReport']['data'], true);
	$this->set('report_template_id', $report_template_id);
}
$this->set('order_list', $order_list);
$this->set('fields_list', $fields_list);
$this->set('item_fields_list', $item_fields_list);
