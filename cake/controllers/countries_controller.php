<?php
class CountriesController extends AppController {

	var $name = 'Countries';

	/**
	 * @var Country
	 */
	var $Country;
	var $helpers = array('Html', 'Form');
    public $uses = null;
	function owner_index() {
		$countries = getCountriesList();
		$this->set('countries', $countries);
        if(IS_REST){
            $this->set('rest_items', $countries);
            $this->set('rest_model_name', "Country");
            $this->render("index");
        }
	}
//
//	function view($id = null) {
//		if (!$id) {
//			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('country', true)),true));
//			$this->redirect(array('action'=>'index'));
//		}
//		$this->set('country', $this->Country->read(null, $id));
//	}
//
//	function add() {
//		if (!empty($this->data)) {
//			$this->Country->create();
//			if ($this->Country->save($this->data)) {
//				$this->flashMessage(sprintf (__('The %s has been saved', true), __('country',true)), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			} else {
//				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('country',true)));
//			}
//		}
//	}
//
//	function edit($id = null) {
//		if (!$id && empty($this->data)) {
//			$this->flashMessage(sprintf (__('Invalid %s.', 'country',true)));
//			$this->redirect(array('action'=>'index'));
//		}
//		$country = $this->Country->read(null, $id);
//		if (!empty($this->data)) {
//			if ($this->Country->save($this->data)) {
//				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('country',true)), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			} else {
//				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('country',true)));
//			}
//		}
//		if (empty($this->data)) {
//			$this->data = $country;
//		}
//		$this->render('add');
//	}
//
//	function delete($id = null) {
//		if (empty($id) && !empty ($_POST['ids'])) {
//			$id = $_POST['ids'];
//		}
//		if (!$id && empty ($_POST)) {
//			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('country',true)));
//			$this->redirect(array('action'=>'index'));
//		}
//		$module_name= __('country', true);
//		if(count($_POST['ids']) > 1) {
//			$module_name= __('countries', true);
//		}
//		$countries = $this->Country->find('all',array('conditions'=>array('Country.id'=>$id)));
//		if (empty($countries)) {
//			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
//			$this->redirect($this->referer(array('action' => 'index'), true));
//		}
//		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
//			if ($_POST['submit_btn'] == 'yes' && $this->Country->deleteAll(array('Country.id'=>$_POST['ids']))) {
//				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			}
//			else {
//				$this->redirect(array('action'=>'index'));
//			}
//		}
//		$this->set('countries',$countries);
//	}
//
//	function get_languages(){
//		$this->loadModel('Language');
//		$data=file_get_contents('languages.txt');
//		$languages=array();
//		$languages_data=explode("\r\n", $data);
//		foreach($languages_data as $language){
//			$data=array();
//			$language1=explode("\t",$language);
//			$data['Language']['code2']=$language1[3];
//			$data['Language']['code3']=$language1[4];
//			$data['Language']['name']=$language1[1];
//			$this->Language->create();
//			$this->Language->save($data);
//		}
//	}
//
//	function get_currencies() {
//		$data=file_get_contents('countryinfo.txt');
//		$currencies1=array();
//
//		$currencies1=explode("\r\n", $data);
//		$this->loadModel('CountryInfo');
//		foreach($currencies1 as $currency1) {
//			$data=array();
//			$currency=array();
//			$currency=explode("\t", $currency1);
//			$data['CountryInfo']['name']=$currency[4];
//			$data['CountryInfo']['code2']=$currency[0];
//			$data['CountryInfo']['code3']=$currency[1];
//			$data['CountryInfo']['currency']=$currency[10];
//			$language=(!empty ($currency[15])?explode(',',$currency[15]):"");
//			$data['CountryInfo']['language']=!empty ($language)?$language[0]:'';
//			$data['CountryInfo']['postal_code']=$currency[13];
//			$this->CountryInfo->create();
//			$this->CountryInfo->save($data);
//		}
//
//		exit();
//	}
//
//	function set_currencies() {
//		$this->loadModel('CountryInfo');
//		$this->loadModel('Currency');
//
//		$countries=$this->CountryInfo->find('all');
//		foreach($countries as $country) {
//			$data=array();
//			$locale='en_US';
//			if($country['CountryInfo']['language']) {
//				$locale=$country['CountryInfo']['language'];
//			}
//			$data['Currency']['country']= $country['CountryInfo']['name'];
//			$data['Currency']['code']=$data['Currency']['name']= $country['CountryInfo']['currency'];
//			$cFormatter = new NumberFormatter($locale, NumberFormatter::CURRENCY);
//			$data['Currency']['symbol']= $cFormatter->getSymbol(NumberFormatter::CURRENCY_SYMBOL);
//			$value = $cFormatter->formatCurrency('100', $data['Currency']['code']);
//			$data['Currency']['format']= preg_replace('/([\d, ])+(\.00)?/', '%0.2f', $value);
//			$data['Currency']['format']= str_replace('١٠٠٫٠٠', '%0.2f', $data['Currency']['format']);
//
//			$this->Currency->create();
//			$this->Currency->save($data);
//
//
//			debug($data);
//		}
//		exit();
//	}
//
//	function get_currencies2() {
//		$data=file_get_contents('countries.txt');
//		$currencies1=array();
//
//		$currencies1=explode("\r\n\r\n", $data);
//		$this->loadModel('Currency2');
//		foreach($currencies1 as $currency1) {
//			$data=array();
//			$currency=array();
//			$currency=explode("\t", $currency1);
//			$data['Currency2']['name']=$currency[2];
//			$data['Currency2']['code']=$currency[0];
//			$this->Currency2->create();
//			$this->Currency2->save($data);
//		}
//
//		exit();
//	}
//
//	function update_currencies() {
//		$this->loadModel('Currency2');
//		$this->loadModel('Currency');
//		$currencies=$this->Currency->find('all');
//		foreach($currencies as $currency) {
//			$currency2= $this->Currency2->findByCode($currency['Currency']['code']);
//			if(!empty ($currency2['Currency2']['name'])) {
//				$data=array();
//				$data['Currency']['id']=$currency['Currency']['id'];
//				$data['Currency']['name']=$currency2['Currency2']['name'];
//				$this->Currency->save($data);
//			}
//		}
//		exit();
//	}
}