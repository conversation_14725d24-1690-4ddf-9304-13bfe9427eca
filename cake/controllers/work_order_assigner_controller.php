<?php

use App\Services\WorkOrder\WorkOrderTransactionAssigner;

class WorkOrderAssignerController extends AppController
{
    public $uses = [];

    public function getMysqli()
    {
        if (is_null($this->mysqli)) {
            $db_config = json_decode(getCurrentSite('db_config'), true);
            return $this->mysqli = connectToDatabase($db_config);
        } else {
            return  $this->mysqli;
        }
    }

    public function owner_get_suggestions($type)
    {
        $result = [];
        $requestParams = $_GET;
        $isWorkflow = (int)$requestParams['is_workflow'];
        $data = WorkOrderTransactionAssigner::getSuggestions($type,$isWorkflow);
        foreach ($data['suggestions'] as $suggestion) {
            $result[] = array(
                'text' => $data['label'].' #'.$suggestion[$data['modelKey']][$data['code']],
                'id' => $suggestion[$data['modelKey']]['id'],
            );
        }
        echo json_encode($result);
        die();
    }

    public function owner_search($type)
    {
        Configure::write('debug', 0);
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        $result = [];
        if (!empty($value)) {
            $value = mysqli_real_escape_string($this->getMysqli(), $value);
            $requestParams   = $_GET;
            $isWorkflow = (int)$requestParams['is_workflow'];
            $data = WorkOrderTransactionAssigner::getSearchResult($type, $value, $isWorkflow);
            foreach ($data['result'] as $record) {
                $result[] = array(
                    'text' => $data['label'].' #'.$record[$data['modelKey']][$data['code']],
                    'id' => $record[$data['modelKey']]['id'],
                );
            }

            echo json_encode($result, JSON_UNESCAPED_SLASHES);
            die();
        }
        die;
    }

    public function owner_search_in_assigned($id, $type)
    {
        Configure::write('debug', 0);
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        $result = [];
        if (!empty($value)) {
            $value = mysqli_real_escape_string($this->getMysqli(), $value);
            $requestParams   = $_GET;
            $isWorkflow = (int)$requestParams['is_workflow'];
            $data = WorkOrderTransactionAssigner::getSearchResult($type, $value, $isWorkflow, $id);
            foreach ($data['result'] as $record) {
                $result[] = array(
                    'text' => $data['label'].' #'.$record[$data['modelKey']][$data['code']],
                    'id' => $record[$data['modelKey']]['id'],
                );
            }

            echo json_encode($result, JSON_UNESCAPED_SLASHES);
            die();
        }
        die;
    }

    public function owner_get_assigned($id, $type)
    {
        $result = [];
        $requestParams = $_GET;
        $isWorkflow = (int)$requestParams['is_workflow'];
        $data = WorkOrderTransactionAssigner::getAssigned($type,$isWorkflow, $id);
        foreach ($data['suggestions'] as $suggestion) {
            $result[] = array(
                'text' => $data['label'].' #'.$suggestion[$data['modelKey']][$data['code']],
                'id' => $suggestion[$data['modelKey']]['id'],
            );
        }
        echo json_encode($result);
        die();
    }

    public function owner_assign_transaction()
    {
        $this->loadModel('WorkOrder');
        $data   = $_POST;
        $work_order = $this->WorkOrder->find(array('WorkOrder.id' => $data['workflow_id']));
        $isWorkflow = $work_order['WorkOrder']['workflow_type_id'] >= 1;
        if (!$work_order) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if($data['transaction_id'] == ''){
            $this->flashMessage(sprintf(__("Please select transaction to assign",true),__("Work Order",true)) ) ;
            if($isWorkflow){
                $this->redirect(array('controller' => 'work_orders','action' => 'workflow_view' , $data['workflow_id']));
            } else {
                $this->redirect(array('controller' => 'work_orders','action' => 'view' , $data['workflow_id']));
            }
        }

        try {
            $result = WorkOrderTransactionAssigner::assignTransaction($data['transaction'], $data['transaction_id'], $data['workflow_id'], $isWorkflow);
            if($result) {
                $this->add_actionline(ACTION_ASSIGN_WORK_ORDER_TRANSACTON, [
                    'primary_id' => $data['workflow_id'],
                    'secondary_id' => $data['transaction_id'],
                    'param1' => $data['transaction'],
                    'param2' => ($isWorkflow) ? 1 : 0,
                    'param3' => $work_order['WorkOrder']['number'],
                ]);

                $assignedLabel = $result['assignedLabel'];
                $assignedToLabel = ($isWorkflow ? __('Workflow', true) : __('Work Order', true)).' #'.$work_order['WorkOrder']['number'];
                $this->flashMessage(sprintf(__('%s has been assigned successfully to %s', true), $assignedLabel, $assignedToLabel), 'Sucmessage', 'secondaryMessage');
                if($isWorkflow){
                    $this->redirect(array('controller' => 'work_orders','action' => 'workflow_view' , $data['workflow_id']));
                } else {
                    $this->redirect(array('controller' => 'work_orders','action' => 'view' , $data['workflow_id']));
                }
            }
    
        } catch (\Throwable $th) {
            $this->flashMessage($th->getMessage());
            if (!$this->RequestHandler->isAjax()) {
                $this->redirect(array('controller' => 'work_orders','action' => 'view' , $data['workflow_id']));
            }
        }
        die;
    }

    public function owner_unassign_transaction()
    {
        $this->loadModel('WorkOrder');
        $data   = $_POST;
        $work_order = $this->WorkOrder->find(array('WorkOrder.id' => $data['workflow_id']));
        $isWorkflow = $work_order['WorkOrder']['workflow_type_id'] >= 1;
        if (!$work_order) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) ) ;
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if($data['transaction_id'] == ''){
            $this->flashMessage(sprintf(__("Please select transaction to unassign",true),__("Work Order",true)) ) ;
            if($isWorkflow){
                $this->redirect(array('controller' => 'work_orders','action' => 'workflow_view' , $data['workflow_id']));
            } else {
                $this->redirect(array('controller' => 'work_orders','action' => 'view' , $data['workflow_id']));
            }
        }

        try {
            $result = WorkOrderTransactionAssigner::unassignTransaction($data['transaction'], $data['transaction_id'], $data['workflow_id'], $isWorkflow);
            if($result) {
                $this->add_actionline(ACTION_UNASSIGN_WORK_ORDER_TRANSACTON, [
                    'primary_id' => $data['workflow_id'],
                    'secondary_id' => $data['transaction_id'],
                    'param1' => $data['transaction'],
                    'param2' => ($isWorkflow) ? 1 : 0,
                    'param3' => $work_order['WorkOrder']['number'],
                ]);

                $assignedLabel = $result['unassignedLabel'];
                $assignedToLabel = ($isWorkflow ? __('Workflow', true) : __('Work Order', true)).' #'.$work_order['WorkOrder']['number'];
                $this->flashMessage(sprintf(__('%s has been successfully removed from %s', true), $assignedLabel, $assignedToLabel), 'Sucmessage', 'secondaryMessage');
                if($isWorkflow){
                    $this->redirect(array('controller' => 'work_orders','action' => 'workflow_view' , $data['workflow_id']));
                } else {
                    $this->redirect(array('controller' => 'work_orders','action' => 'view' , $data['workflow_id']));
                }
            }
    
        } catch (\Throwable $th) {
            $this->flashMessage($th->getMessage());
            if (!$this->RequestHandler->isAjax()) {
                $this->redirect(array('controller' => 'work_orders','action' => 'view' , $data['workflow_id']));
            }
        }
        die;
    }


}
