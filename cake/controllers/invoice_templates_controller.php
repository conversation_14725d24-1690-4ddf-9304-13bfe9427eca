<?php

class InvoiceTemplatesController extends AppController {

	var $name = 'InvoiceTemplates';

	/**
	 * @var InvoiceTemplate
	 */
	var $InvoiceTemplate;
	var $helpers = array('Html', 'Form');

	function admin_index() {
		$this->InvoiceTemplate->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('invoiceTemplates', $this->paginate('InvoiceTemplate', $conditions));
	}

	function admin_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf(__('Invalid %s', true), __('prefilled invoice', true)), true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('invoiceTemplate', $this->InvoiceTemplate->read(null, $id));
	}

	function admin_add() {
		if (!empty($this->data)) {
			$this->InvoiceTemplate->create();
			if ($this->InvoiceTemplate->save($this->data)) {
				$this->flashMessage(sprintf(__('%s has been saved', true), __('Prefilled invoice', true)), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Prefilled invoice', true)));
			}
		}
	}

	function admin_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf(__('Invalid %s.', 'prefilled invoice', true)));
			$this->redirect(array('action' => 'index'));
		}
		$invoiceTemplate = $this->InvoiceTemplate->read(null, $id);
		if (!empty($this->data)) {
			if ($this->InvoiceTemplate->save($this->data)) {
				$this->flashMessage(sprintf(__('%s has been saved', true), __('Prefilled invoice', true)), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Prefilled invoice', true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $invoiceTemplate;
		}
		$this->render('admin_add');
	}

	function admin_delete($id = null) {
		if (empty($id) && !empty($_POST['ids'])) {
			$id = $_POST['ids'];
		}
		if (!$id && empty($_POST)) {
			$this->flashMessage(sprintf(__('Invalid %s', true), __('prefilled invoice', true)));
			$this->redirect(array('action' => 'index'));
		}
		$module_name = __('prefilled invoice', true);
		$verb = __('has', true);
		if (is_countable($id) && count($id) > 1) {
			$verb = __('have', true);
			$module_name = __('prefilled invoices', true);
		}
		$invoiceTemplates = $this->InvoiceTemplate->find('all', array('conditions' => array('InvoiceTemplate.id' => $id)));
		if (empty($invoiceTemplates)) {
			$this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->InvoiceTemplate->deleteAll(array('InvoiceTemplate.id' => $_POST['ids']))) {
				$this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->redirect(array('action' => 'index'));
			}
		}
		$this->set('invoiceTemplates', $invoiceTemplates);
	}

}

?>