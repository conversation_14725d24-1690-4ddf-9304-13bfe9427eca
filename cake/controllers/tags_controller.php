<?php
class TagsController extends AppController {

	var $name = 'Tags';

	/**
	 * @var Tag
	 */
	var $Tag;
	var $helpers = array('Html', 'Form');
	
	function owner_get_tags()
	{
		debug($this->params);
        if (isset($_GET['q']) && !empty($_GET['q'])){
            $conditions[] = ['Tag.name LIKE ' => "%{$_GET['q']}%"];
        }
		if(!isset($_GET['all_tags']) || $_GET['all_tags'] != 0){
            $tags['tags'] =	array_values($this->Tag->find('list', ['conditions' => $conditions]));
        }
		if(!empty($this->params['url']['item_id']) && !empty($this->params['url']['model_name']))
		{
			$this->loadModel('ItemsTag');
			$item_tag_type = ItemsTag::model_name_to_tag_type($this->params['url']['model_name']);
			$filter_url = ItemsTag::item_tag_type_for_filter_url($item_tag_type);
			$tags['filter_url'] = $filter_url;
			$tags['tag_type'] = $item_tag_type;
		$tags['selected'] = array_values($this->ItemsTag->get_items_tags($this->params['url']['item_id'],$item_tag_type));
		}
        die(json_encode(($tags ?? null)));
	}
	function owner_index() {
		$this->Tag->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('tags', $this->paginate('Tag', $conditions));
	}

	function owner_replace_product_tags()
    {
        $counter = $this->Tag->replaceOldProductTags();
        dd("{$counter} tags saved");
    }
	
	function owner_test()
	{
		
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('tag', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('tag', $this->Tag->read(null, $id));
	}

	function owner_add() {
		if (!empty($this->data)) {
			$this->Tag->create();
			if ($this->Tag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('tag',true)));
			}
		}
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$tag = $this->Tag->read(null, $id);
		if (!empty($this->data)) {
			if ($this->Tag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('tag',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $tag;
		}
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('tag', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('tags', true);
		 } 
		$tags = $this->Tag->find('all',array('conditions'=>array('Tag.id'=>$id)));
		if (empty($tags)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->Tag->deleteAll(array('Tag.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('tags',$tags);
	}
}
?>