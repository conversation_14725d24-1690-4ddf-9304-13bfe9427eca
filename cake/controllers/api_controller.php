<?php

use Izam\Daftra\Common\Utils\PermissionUtil;

class ApiController extends AppController {

    var $name = "Settings";
    var $validate_schema;
    function beforeFilter() {
        parent::beforeFilter();

        App::import('Vendor', 'settings');
        if (!in_array($this->action, array('apikey','owner_apikey', 'owner_client_settings'))) {
            header('Content-Type: application/json');
            $apikey = $_SERVER['HTTP_APIKEY'];
            if ($apikey=='' || $apikey != settings::getValue(0, "apikey")) {
                http_response_code(401);
                die(json_encode(["success" => 0, "message" => "Not authenticated"]));
            }

            define('IS_API', true);
        }
    }
    function owner_apikey() {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        App::import('Vendor', 'settings');
        $this->loadModel('Site');
        $apikey = settings::getValue(0, "apikey");
        if (!$apikey) {
            settings::setValue("0", "apikey", hash("sha256", time()));
            $apikey = settings::getValue(0, "apikey");
        }
        $pincode = $this->Site->get_pincode();

        $this->data['Settings']['apikey'] = $apikey;
        $this->data['Settings']['pincode'] = $pincode;
        $this->render('/api/apikey');
        //die(json_encode(["success" => 1 ,"apikey" => $apikey , "message" => "API Key Generation Success" ] ));
    }

    function apikey() {
        $owner=  getAuthOwner();
        if(empty($owner)){
         die(json_encode(["success" => 0 ,"apikey" => false] ));    
        }else{
        App::import('Vendor', 'settings');
        $this->loadModel('Site');
        $apikey = settings::getValue(0, "apikey");
        if (!$apikey) {
            settings::setValue("0", "apikey", hash("sha256", time()));
            $apikey = settings::getValue(0, "apikey");
        }
        $pincode = $this->Site->get_pincode();

        $this->data['Settings']['apikey'] = $apikey;
        $this->data['Settings']['pincode'] = $pincode;
       
       die(json_encode(["success" => 1 ,"apikey" => $apikey] ));
        }
    }

    function api_listing($model = null){
        switch ($model) {
            //Handle special cases
            case 'DateFormats':
                $data = getDateFormats(true);
                break;
            //Default Case
            default:
                $this->loadModel($model);
                if (method_exists($this->{$model},"getList")) {
                    //If there is getList
                    $data = $this->{$model}->getList();
                } else if (method_exists($this->{$model},"get{$model}List")) {
                    //If there is get{Model}List like getLanguageList()
                    $data = $this->{$model}->{"get{$model}List"}();
                } else {
                    //else then load the model and find list
                    $listParams = [];
                    //Process fields
                    if (isset($_GET['fields'])) {
                        if (count($_GET['fields']) != 2)
                            $this->cakeError('error400');
                        $listParams['fields'] = $_GET['fields'];
                        unset($_GET['fields']);
                    }
                    //Process conditions
                    if (isset($_GET['conditions'])) {
                        $listParams['conditions'] = $_GET['conditions'];
                    }
                    $this->{$model}->recursive = -1;
                    $data = $this->{$model}->find('list', $listParams);
                }
        }
        $this->set('rest_item', ['Listing' => $data]);
        $this->set('rest_model_name', "Listing");
        $this->set('rest_force_object', true);  //Setting this variable force converting all arrays into json objects
        $this->render('view');
    }

    function api_execute_sql() {
        $data = [];
        $dbCredentials = settings::getValue(0, 'api_db_user', null,false, false);
        $dbCredentials = $dbCredentials ? json_decode($dbCredentials, true) : [];
        if (((IS_REST && TRUE_REST) || isSuperAdmin()) && $dbCredentials) {
            if (isset($this->data, $this->data['sql'])) {
                $query = trim($this->data['sql']);
                $dbConn=connectToDatabase($dbCredentials);
                mysqli_query($dbConn,"SET NAMES utf8");
                if (mysqli_connect_errno())
                {
                    $data['db_response'] = "Failed to connect to MySQL: " . mysqli_connect_error();
                } else {
                    $queryType = strtolower(str_replace('(','',explode(' ', $query)[0]));
                    switch ($queryType) {
                        case 'select':
                            $data = $this->executeSelectSql($dbConn, $query);
                            break;
                        case 'insert':
                            $data = $this->executeInsertSql($dbConn, $query);
                            break;
                        default:
                            $data = $this->executeGenericSql($dbConn, $query);
                            break;
                    }
                }
            }
        } else {
            return $this->cakeError('error403');
        }
        $this->set('rest_item', ['db_response' => $data]);
        $this->set('rest_model_name', "db_response");
        $this->set('rest_force_object', true);  //Setting this variable force converting all arrays into json objects
        return $this->render('view');
    }

    private function saveExpenses($data, $id = null, $is_income = false) {
        $this->loadModel('Expense');

        if (isset($data['expense'])) {

            $data['Expense'] = $data['expense'];
            unset($data['expense']);
            $data['RecurringExpense'] = $data['recurring_expense'];
            unset($data['recurring_expense']);
            unset($data['Expense']['id']);
            $data['Expense']['is_income'] = $is_income ? 1 : 0;
            if (!empty($id)) {

                $data['Expense']['id'] = $id;
                $res = $this->Expense->save($data);
                $res['status'] = $res ? true : false;
                $action = $is_income ? ACTION_UPDATE_INCOME : ACTION_UPDATE_EXPENSE;
            } else {
                $action = $is_income ? ACTION_ADD_INCOME : ACTION_ADD_EXPENSE;
                $res = $this->Expense->save($data);
                $id = $this->Expense->getLastInsertID();

                $res['status'] = $res ? true : false;
            }
            if ($res && $data['Expense']['recurring'] == "" or $data['Expense']['recurring'] == 0) {
                $data['RecurringExpense']['last_generated_id'] = $id;
                $data['RecurringExpense']['last_generated_date'] = $data['Expense']['date'];
                $data['RecurringExpense']['active'] = 1;
                $data['RecurringExpense']['unit_name'] = $data['RecurringExpense']['unit_name'];
                $data['RecurringExpense']['period_unit'] = $this->Expense->RecurringExpense->period_list[$data['RecurringExpense']['unit_name']]['unit'];
                $data['RecurringExpense']['unit_count'] = $this->Expense->RecurringExpense->period_list[$data['RecurringExpense']['unit_name']]['unit_count'];
                /* if ($this->data['RecurringExpense']['end_date'] != "") {

                  $date = DateTime::createFromFormat($dateFormat, $data['RecurringExpense']['end_date']);
                  $this->data['RecurringExpense']['end_date'] = $date->format('Y-m-d');
                  } */

                $this->Expense->RecurringExpense->save($data['RecurringExpense']);
                $this->Expense->saveField("recurring_expense_id", $this->Expense->RecurringExpense->id, false);
                /* if ($this->data['Expense']['date'] < date('Y-m-d'))
                  $this->Expense->cron(getCurrentSite('id')); */
            }
            if ($res['status']) {
                $this->add_actionline($action, array('primary_id' => $id, 'param1' => $data['Expense']['amount'], 'param2' => $data['Expense']['category'], 'param3' => $data['Expense']['vendor'], 'param6' => $data['Expense']['date']));

                //$this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $id, 'param4' => $data['Product']['name'], 'param2' => $data['Product']['unit_price'], 'param3' => $data['Product']['default_quantity']));
                die(json_encode([ "success" => 1, "id" => $id]));
            } else {
                $errors = "";
                foreach ($this->Expense->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Expense data"]));
        }
    }

    public function expenses($id = null) {
        $this->loadModel('Expense');
        $is_income = $this->params['is_income'];

        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->saveExpenses(json_decode(file_get_contents("php://input"), true), null, $is_income));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->saveExpenses(json_decode(file_get_contents("php://input"), true), $id, $is_income));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $expense = $this->Expense->findById($id);

                if ($this->Expense->delete($id)) {

                    $this->add_actionline($is_income ? ACTION_DELETE_INCOME : ACTION_DELETE_EXPENSE, array('primary_id' => $expense['Expense']['id'], 'param1' => $expense['Expense']['amount'], 'param2' => $expense['Expense']['category'], 'param3' => $expense['Expense']['vendor']));
                    //$this->add_actionline(ACTION_DELETE_PRODUCT, array('primary_id' => $product['Product']['id'], 'secondary_id' => $product['Product']['id'], 'param4' => $product['Product']['name'], 'param2' => $product['Product']['unit_price'], 'param3' => $product['Product']['default_quantity']));
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->Expense->recursive = -1;
            if (empty($id)) {
                $Expense = $this->Expense->findAll();
                $success = 1;
                die(json_encode(["success" => $success, "expenses" => $Expense]));
            } else {
                $id = intval($id);
                $Expense = $this->Expense->findById($id);
                die(json_encode(["success" => empty($Expense) ? 0 : 1, "expense" => $Expense['Expense']]));
            }
        } else {
            http_response_code(405);
            die(json_encode(["success" => 0, "message" => "Method not allowed"]));
        }
    }

    private function saveProduct($data, $id = null) {
        $this->loadModel('Product');

        if (isset($data['product'])) {

            $data['Product'] = $data['product'];
            unset($data['product']);
            unset($data['Product']['id']);

            if (!empty($id)) {

                $data['Product']['id'] = $id;
                $res = $this->Product->save($data);
                $res['status'] = $res ? true : false;
                $action = ACTION_EDIT_PRODUCT;
            } else {
                $action = ACTION_ADD_PRODUCT;
                $res = $this->Product->save($data);
                $res['status'] = $res ? true : false;
                $id = $this->Product->getLastInsertID();
            }
            if ($res['status']) {

                $this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $data['Supplier']['id'], 'param4' => $data['Product']['name'], 'param2' => $data['Product']['unit_price'], 'param3' => $data['Product']['default_quantity']));
                die(json_encode([ "success" => 1, "id" => $id]));
            } else {
                $errors = "";
                foreach ($this->Product->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Product data"]));
        }
    }

    public function products($id = null) {
        $this->loadModel('Product');
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->saveProduct(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->saveProduct(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $product = $this->Product->findById($id);

                if ($this->Product->delete($id)) {

                    $this->add_actionline(ACTION_DELETE_PRODUCT, array('primary_id' => $product['Product']['id'], 'secondary_id' => $product['Supplier']['id'], 'param4' => $product['Product']['name'], 'param2' => $product['Product']['unit_price'], 'param3' => $product['Product']['default_quantity']));
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->Product->recursive = -1;
            if (empty($id)) {
                $Product = $this->Product->findAll();
                $success = 1;
                die(json_encode(["success" => $success, "products" => $Product]));
            } else {
                $id = intval($id);
                $Product = $this->Product->findById($id);
                die(json_encode(["success" => empty($Product) ? 0 : 1, "product" => $Product['Product']]));
            }
        } else {
            http_response_code(405);
            die(json_encode(["success" => 0, "message" => "Method not allowed"]));
        }
    }

    private function saveSupplier($data, $id = null) {
        $this->loadModel('Supplier');

        if (isset($data['supplier'])) {

            $data['Supplier'] = $data['supplier'];
            unset($data['supplier']);
            unset($data['Supplier']['id']);

            if (!empty($id)) {

                $data['Supplier']['id'] = $id;
                $res = $this->Supplier->save($data);
                $res['status'] = $res ? true : false;
                $action = ACTION_UPDATE_SUPPLIER;
            } else {
                $action = ACTION_ADD_SUPPLIER;
                $res = $this->Supplier->save($data);
                $res['status'] = $res ? true : false;
                $id = $this->Supplier->getLastInsertID();
            }
            if ($res['status']) {
                $this->add_actionline($action, array('primary_id' => $id, /*'secondary_id' => $id,*/ 'param2' => $data['Supplier']['business_name'], 'param3' => $data['Supplier']['email'], 'param4' => $data['Supplier']['supplier_number'], 'param5' => $data['Supplier']['phone1'] . ' ' . $data['Supplier']['phone2']));
                //$this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $id, 'param4' => $data['Product']['name'], 'param2' => $data['Product']['unit_price'], 'param3' => $data['Product']['default_quantity']));
                die(json_encode([ "success" => 1, "id" => $id]));
            } else {
                $errors = "";
                foreach ($this->Supplier->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Supplier data"]));
        }
    }

    public function suppliers($id = null) {
        $this->loadModel('Supplier');
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->saveSupplier(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->saveSupplier(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $supplier = $this->Supplier->findById($id);

                if ($this->Supplier->delete($id)) {

                    //NEED ACTION LINE
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->Supplier->recursive = -1;
            if (empty($id)) {
                $suppliers = $this->Supplier->findAll();
                $success = 1;
                die(json_encode(["success" => $success, "suppliers" => $suppliers]));
            } else {
                $id = intval($id);
                $supplier = $this->Supplier->findById($id);
                die(json_encode(["success" => empty($supplier) ? 0 : 1, "supplier" => $supplier['Supplier']]));
            }
        } else {
            http_response_code(405);
            die(json_encode(["success" => 0, "message" => "Method not allowed"]));
        }
    }

    private function savePurchaseOrder($data, $id = null) {
        $this->loadModel('PurchaseOrder');

        if (isset($data['purchase_order'])) {
            //$data['PurchaseOrderItem'] = $data['purchase_order']['PurchaseOrderItem'];
            $data['PurchaseOrder'] = $data['purchase_order'];
            unset($data['purchase_order']);
            unset($data['PurchaseOrder']['id']);
            if (isset($data['PurchaseOrder']['supplier_id']) || $data['PurchaseOrder']['supplier_id'] == "") {
                $data['Supplier']['supplier_from_data'] = false;
            }

            for ($i = 0; $i < count($data['PurchaseOrderItem']); $i++) {
                unset($data['PurchaseOrderItem'][$i]['purchase_order_id']);
                unset($data['PurchaseOrderItem'][$i]['id']);
            }
            if (!empty($id)) {

                $data['PurchaseOrder']['id'] = $id;
                $res = $this->PurchaseOrder->updatePurchaseOrder($data);
                //$res['status'] = $res ? true : false;
                $action = ACTION_UPDATE_PO;
            } else {
                $action = ACTION_ADD_PO;
                $res = $this->PurchaseOrder->addPurchaseOrder($data);
                //$res['status'] = $res ? true : false;
                //$id = $this->PurchaseOrder->getLastInsertID ();
            }
            if ($res['status']) {
                $id = $res['data']['PurchaseOrder']['id'];
                $this->PurchaseOrder->recursive = 2;
                $re_read_po = $this->PurchaseOrder->findById($id);
                $this->loadModel('StockTransaction');
                StockTransaction::updateForPo($re_read_po);
                $arry = array('primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']);
                $this->add_actionline($action, $arry);
                //$this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $id, 'param4' => $data['Product']['name'], 'param2' => $data['Product']['unit_price'], 'param3' => $data['Product']['default_quantity']));
                die(json_encode([ "success" => 1, "id" => $id]));
            } else {
                $errors = "";
                debug($this->PurchaseOrder->validationErrors);
                foreach ($this->PurchaseOrder->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Supplier data"]));
        }
    }

    public function purchase_orders($id = null) {
        $this->loadModel('PurchaseOrder');
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->savePurchaseOrder(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->savePurchaseOrder(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $purchase_orders = $this->PurchaseOrder->findById($id);
                $delete_conditions = array('PurchaseOrder.id' => $id);
                if ($this->PurchaseOrder->deleteAll($delete_conditions) && $this->PurchaseOrder->delete_related_items($id)) {

                    $this->add_actionline(ACTION_DELETE_PO, array('primary_id' => $purchase_orders['PurchaseOrder']['id'], 'secondary_id' => $purchase_orders['PurchaseOrder']['supplier_id'], 'param1' => $purchase_orders['PurchaseOrder']['summary_total'], 'param2' => $purchase_orders['PurchaseOrder']['payment_status'], 'param3' => $purchase_orders['PurchaseOrder']['summary_paid'], 'param4' => $purchase_orders['PurchaseOrder']['no'], 'param5' => $purchase_orders['Supplier']['business_name'], 'param6' => $purchase_orders['Supplier']['supplier_number']));
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->PurchaseOrder->recursive = 1;
            if (empty($id)) {
                $PurchaseOrder = $this->PurchaseOrder->findAll();
                $json_data = [];
                $i = 0;
                foreach ($PurchaseOrder as $k => $v) {
                    $json_data[$i]['PurchaseOrder'] = $v['PurchaseOrder'];
                    $json_data[$i]['PurchaseOrderItem'] = $v['PurchaseOrderItem'];
                    $i++;
                }
                $success = 1;
                die(json_encode(["success" => $success, "purchase_orders" => $json_data]));
            } else {
                $id = intval($id);
                $PurchaseOrder = $this->PurchaseOrder->findById($id);

                die(json_encode(["success" => empty($PurchaseOrder) ? 0 : 1, "purchase_order" => $PurchaseOrder['PurchaseOrder'], 'purchase_order_item' => $PurchaseOrder['PurchaseOrderItem']]));
            }
        } else {
            http_response_code(405);
            die(json_encode(["success" => 0, "message" => "Method not allowed"]));
        }
    }

    public function upload_expense_base64() {
        $json_data = json_decode(file_get_contents("php://input"), true);
        $expense_id = $json_data ['expense_id'];
        $name = $json_data ['file_name'];
        $base64_string = $json_data ['file_content'];
        $base64data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64_string));

        try {
            if (file_put_contents(WWW_ROOT . "files/" . SITE_HASH . "/" . $name, $base64data)) {
                //echo strlen(base64_decode ($base64_string)) ; 
                $filedata = ["name" => $name, "tmp_name" => WWW_ROOT . "files/" . SITE_HASH . "/" . $name, "size" => strlen($base64data)];
                require_once APP . 'vendors' . DS . 'FileUploader.php';
                $status = FileUploader::Postupload($filedata, 122);
                if ($status['error']) {
                    echo json_encode(array('post_file' => false, 'upgrade_link' => $status['upgrade_link'], 'error' => true, 'msg' => $status['msg']));
                    die();
                } else {
                    $row = FileUploader::ReadHash($status['hash']);

                    $uniqid = substr(uniqid(), 8);
                    $base_name = substr($row['file_name'], 0, strrpos($row['file_name'], '.'));
                    $ext = substr($row['file_name'], strrpos($row['file_name'], '.'));
                    $fname = Inflector::slug($base_name) . $ext;
                    $filename = $uniqid . '_' . $fname;
                    if (!file_exists(WWW_ROOT . "/files/" . SITE_HASH . "/documents/")) {
                        @mkdir(WWW_ROOT . "/files/" . SITE_HASH . "/documents/");
                    }
                    $my_file = WWW_ROOT . "/files/" . SITE_HASH . "/documents/" . $filename;
                    rename($row['file_path'], $my_file);
                    //unlink ($filedata['tmp_name'] );
                    $this->loadModel("Expense");
                    $this->Expense->id = $expense_id;
                    $this->Expense->saveField("file", $filename, false);
                    FileUploader::RemoveHash($status['hash']);
                }
            }
            die;
        } catch (Exception $e) {
            http_response_code(500);
            die(json_encode(['success' => 0, 'message' => "Failed: " . $e->getMessage()]));
        }
    }

    public function upload_postfile_base64() {
        $json_data = json_decode(file_get_contents("php://input"), true);
        $post_id = $json_data ['post_id'];
        $name = $json_data ['file_name'];
        $base64_string = $json_data ['file_content'];
        $base64data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64_string));
        $this->loadModel("Post");
        $post_id = 99;
        try {
            if (file_put_contents(WWW_ROOT . "files/" . SITE_HASH . "/" . $name, $base64data)) {
                //echo strlen(base64_decode ($base64_string)) ; 
                $filedata = ["name" => $name, "tmp_name" => WWW_ROOT . "files/" . SITE_HASH . "/" . $name, "size" => strlen($base64data)];
                require_once APP . 'vendors' . DS . 'FileUploader.php';
                $status = FileUploader::Postupload($filedata, 122);
                if ($status['error']) {
                    echo json_encode(array('post_file' => false, 'upgrade_link' => $status['upgrade_link'], 'error' => true, 'msg' => $status['msg']));
                    die();
                } else {
                    $postdata = $this->Post->findById($post_id);
                    $row = FileUploader::ReadHash($status['hash']);
                    print_r($row);
                    $uniqid = substr(uniqid(), 8);
                    $base_name = substr($row['file_name'], 0, strrpos($row['file_name'], '.'));
                    $ext = substr($row['file_name'], strrpos($row['file_name'], '.'));
                    $fname = Inflector::slug($base_name) . $ext;
                    $filename = $uniqid . '_' . $fname;
                    //@mkdir(WWW_ROOT . "/files/" . SITE_HASH . "/post-files/");
                    rename($row['file_path'], WWW_ROOT . "/files/" . SITE_HASH . "/post-files/" . $filename);
                    unlink($row['file_path']);
                    $data['Allfile']['user_id'] = $postdata['Post']['item_id'];
                    $data['Allfile']['date'] = date("Y-m-d H:i:s");
                    $data['Allfile']['file_size'] = $row['file_size'];
                    $data['Allfile']['file_type'] = substr($ext, 1);
                    $data['Allfile']['file_name'] = $filename;
                    $data['Allfile']['original_file_name'] = $row['file_name'];
                    $data['Allfile']['md5'] = md5_file(WWW_ROOT . "files/" . SITE_HASH . "/post-files/" . $filename);
                    $this->loadModel('Allfile');
                    $this->loadModel('PostFile');
                    $this->Allfile->create();
                    if ($this->Allfile->save($data)) {

                        if ($post_id != 0) {
                            $data1['PostFile']['post_id'] = $post_id;
                            $data1['PostFile']['allfile_id'] = $this->Allfile->getLastInsertID();
                            $this->Post->PostFile->save($data1);
                            //echo json_encode(array('post_file' => true, 'error' => false, 'id' => $this->Post->PostFile->getLastInsertID()));
                            die(json_encode(['success' => 1, 'id' => $this->Post->PostFile->getLastInsertID()]));
                        }
                    } else {
                        $verrors = "";
                        foreach ($this->Allfile->validationErrors as $k => $vv) {
                            $verrors .= $k . " " . $vv . ", ";
                        }
                        http_response_code(500);
                        die(json_encode(['success' => 0, 'message' => "Failed: $verrors "]));
                    }
                    FileUploader::RemoveHash($status['hash']);
                }
            }
            die;
        } catch (Exception $e) {
            http_response_code(500);
            die(json_encode(['success' => 0, 'message' => "Failed: " . $e->getMessage()]));
        }
    }

    private function savePost($data, $id = null) {
        $this->loadModel('Post');
        if (isset($data['post'])) {

            $data['Post'] = $data['post'];
            unset($data['post']);
            unset($data['Post']['id']);
            $this->loadModel('Client');
            $this->Client->recursive = -1;
            $client = $this->Client->findById($data['Post']['item_id']);
            if (empty($client)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Failed : Item doesn't exist"]));
            }
            if (!empty($id)) {

                $data['Post']['id'] = $id;
                $res = $this->Post->save($data);
                $res['status'] = $res ? true : false;
                $action = ACTION_UPDATE_THE_POST;
            } else {
                $this->loadModel('FollowUpStatus');
                $followUpStatus = [];
                if (!empty($data['Post']['status_id'])) {
                    $status_id = intval($data['Post']['status_id']);
                    $followUpStatus = $this->FollowUpStatus->findById($status_id);
                } else if (!empty($data['Post']['status_name'])) {
                    $status_name = trim($data['Post']['status_name']);
                    $followUpStatus = $this->FollowUpStatus->findByName($status_name);
                    if (empty($followUpStatus)) {
                        $followUpStatus = $this->FollowUpStatus->save(['FollowUpStatus' => [
                                'item_type' => 1,
                                'name' => $status_name,
                                'color' => 'white',
                                'staff_id' => -3]]);
						$followUpStatus['FollowUpStatus']['id'] = $this->FollowUpStatus->id;
                    }
                }
                if (!empty($followUpStatus)) {
					$data['Post']['status_id'] = $followUpStatus['FollowUpStatus']['id'];
				} else {
                    $data['Post']['status_id'] = null;
                }

                $this->loadModel('FollowUpAction');
                $followUpAction = [];
                if (!empty($data['Post']['action_id'])) {
                    $action_id = intval($data['Post']['action_id']);
                    $followUpAction = $this->FollowUpAction->findById($action_id);
                } else if (!empty($data['Post']['action_name'])) {
                    $action_name = trim($data['Post']['action_name']);
                    $followUpAction = $this->FollowUpAction->findByName($action_name);
                    if (empty($followUpAction)) {
                        $followUpAction = $this->FollowUpAction->save(['FollowUpAction' => [
                                'item_type' => 1,
                                'name' => $action_name,
                                'staff_id' => -3]]);
                    }
					$followUpAction['FollowUpAction']['id'] = $this->FollowUpAction->id;
                }
                if (!empty($followUpAction)) {
					$data['Post']['action_id'] = $followUpAction['FollowUpAction']['id'];
				} else {
                    $data['Post']['action_id'] = null;
				}


                $action = ACTION_ADD_NEW_POST;
                $res = $this->Post->save($data);
                $res['status'] = $res ? true : false;
                debug($this->Post->validationErrors);
                $id = $this->Post->getLastInsertID();
            }
            if ($res['status']) {
                $this->loadModel('FollowUpStatus');
                $statuses = $this->FollowUpStatus->getList($data['Post']['item_type']);
                $this->add_actionline($action, array('staff_id' => -3, 'secondary_id' => $data['Post']['item_id'], 'primary_id' => $id, 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $data['Post']['client_permissions'], 'param4' => $this->Post->formatDateTime($data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']]));
                die(json_encode([ "success" => 1, "id" => $id]));
            } else {
                $errors = "";
                foreach ($this->Post->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Post data"]));
        }
    }

    public function posts($id = null) {
        //die ( $_SERVER['REQUEST_METHOD'] ) ;
        $this->loadModel('Post');
        //$this->InvoicePayment->recursive = -1 ;
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->savePost(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->savePost(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $post = $this->Post->findById($id);
                if ($this->Post->delete($id)) {
                    $this->add_actionline(ACTION_REMOVE_THE_POST, array('staff_id' => -3, 'primary_id' => $post['Post']['id'], 'secondary_id' => $post['Post']['item_id']));
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->InvoicePayment->recursive = -1;
            if (empty($id)) {
                $payments = $this->Post->findAll();
                $success = 1;
                die(json_encode(["success" => $success, "posts" => $payments]));
            } else {
                $id = intval($id);
                $post = $this->Post->findById($id);
                die(json_encode(["success" => empty($post) ? 0 : 1, "Post" => $post['Post']]));
            }
        } else {
            http_response_code(405);
            die(json_encode(["success" => 0, "message" => "Method not allowed"]));
        }
    }

    private function savePayment($data, $id = null) {
        $this->loadModel('Invoice');
        $this->loadModel('InvoicePayment');
        if (isset($data['payment'])) {

            $data['InvoicePayment'] = $data['payment'];
            unset($data['payment']);
            if (!isset($data['InvoicePayment']['amount']) || empty($data['InvoicePayment']['amount'])) {
                $invoice = $this->Invoice->findById($data['InvoicePayment']['invoice_id']);
                if (!$invoice) {
                    http_response_code(400);
                    die(json_encode(["success" => 0, "message" => "Invoice ID not valid"]));
                }
                if ($invoice['Invoice']['summary_deposit'] && $invoice['Invoice']['summary_deposit'] <= $invoice['Invoice']['summary_unpaid']) {
                    $data['InvoicePayment']['amount'] = $invoice['Invoice']['summary_deposit'];
                } else {
                    $data['InvoicePayment']['amount'] = $invoice['Invoice']['summary_unpaid'];
                }
            } else {
                $data['InvoicePayment']['amount'] = round($data['InvoicePayment']['amount'], 2);
            }

            unset($data['InvoicePayment']['id']);
            $this->Invoice->recursive = -1;
            $data['Invoice'] = $this->Invoice->findById($data['InvoicePayment']['invoice_id']);
            $data['Invoice'] = $data['Invoice']['Invoice'];
            if (empty($data['Invoice'])) {
                die(json_encode(["success" => 0, "message" => "Incorrect invoice_id"]));
            }
            if (!empty($id)) {

                $data['InvoicePayment']['id'] = $id;
                $res = $this->InvoicePayment->save($data);
                $res['status'] = $res ? true : false;
                $action = ACTION_UPDATE_INVOICE_PAYMENT;
            } else {
                $action = ACTION_ADD_INVOICE_PAYMENT;
                //$res = $this->InvoicePayment->save($data) ;
                $res = $this->Invoice->ownerAddPayment($data);
                $id = $this->InvoicePayment->getLastInsertID();
            }

            if ($res['status']) {

                $this->add_actionline($action, array('primary_id' => $data['Invoice']['id'], 'secondary_id' => $data['Invoice']['client_id'], 'param1' => $data['InvoicePayment']['amount'], 'param2' => $data['Invoice']['payment_status'], 'param3' => $data['Invoice']['summary_paid'], 'param4' => $data['InvoicePayment']['no'], 'param5' => $id, 'param6' => $data['Invoice']['summary_total'], 'param7' => $data['InvoicePayment']['status'], 'param8' => $data['InvoicePayment']['payment_method'], 'param9' => $data['InvoicePayment']['transaction_id']));
                die(json_encode([ "success" => 1, "id" => $id]));
            } else {
                $errors = "";
                foreach ($this->InvoicePayment->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Payment data"]));
        }
    }

    public function payments($id = null) {
        //die ( $_SERVER['REQUEST_METHOD'] ) ;
        $this->loadModel('InvoicePayment');
        //$this->InvoicePayment->recursive = -1 ;
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->savePayment(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->savePayment(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {

                //$this->InvoicePayment->recursive = 2 ;
                $invoicePayment = $this->InvoicePayment->findById($id);

                if ($this->InvoicePayment->delete($id)) {


                    $this->add_actionline(ACTION_DELETE_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['InvoicePayment']['no'], 'param5' => $id, 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->InvoicePayment->recursive = -1;
            if (empty($id)) {
                $payments = $this->InvoicePayment->findAll();
                $success = 1;
                die(json_encode(["success" => $success, "payments" => $payments]));
            } else {
                $id = intval($id);
                $payment = $this->InvoicePayment->findById($id);
                die(json_encode(["success" => empty($payment) ? 0 : 1, "payment" => $payment['InvoicePayment']]));
            }
        } else {
            http_response_code(405);
            die(json_encode(["success" => 0, "message" => "Method not allowed"]));
        }
    }

    private function saveInvoice($data, $id = null) {
        $this->loadModel('Invoice');
        $this->Invoice->recursive = -1;
        if (isset($data['invoice'])) {
            $data['Invoice'] = $data['invoice'];
            unset($data['invoice']);
            $data['Invoice']['site_id'] = 0;
            if (!isset($data['Invoice']['staff_id'])) {
//				debug ('nour'  );
                $data['Invoice']['staff_id'] = -1;
            }
            unset($data['Invoice']['id']);
            if (!empty($id)) {
                $data['Invoice']['id'] = $id;
                $res = $this->Invoice->updateInvoice($data);
                $action = ACTION_UPDATE_INVOICE;
            } else {

                $res = $this->Invoice->addInvoice($data);

                $action = ACTION_ADD_INVOICE;
                $id = $this->Invoice->getLastInsertID();
            }
            $re_read_invoice = $this->Invoice->getInvoice($id);
            $arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => empty($re_read_invoice['Invoice']['draft']) ? $re_read_invoice['Invoice']['payment_status'] : -1, 'param3' => $re_read_invoice['Invoice']['summary_paid'], 'param4' => $re_read_invoice['Invoice']['no'], 'param5' => $re_read_invoice['Client']['business_name'], 'param6' => $re_read_invoice['Client']['client_number']);
            $this->loadModel('StockTransaction');
            StockTransaction::updateForInvoice($re_read_invoice);
            if ($res['status']) {
                //debug ( $res );
                $this->add_actionline($action, $arry);
                die(json_encode([ "success" => 1, "invoice_number" => $res['data']['Invoice']['no'], "id" => $id]));
            } else {
                $errors = "";
                foreach ($this->Invoice->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Invoice data"]));
        }
    }

    public function invoices($id = null) {
        //die ( $_SERVER['REQUEST_METHOD'] ) ;
        $this->loadModel('Invoice');

        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->saveInvoice(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->saveInvoice(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $invoice = $this->Invoice->findById($id);
                if ($this->Invoice->delete($id)) {
                    $this->add_actionline(ACTION_DELETE_INVOICE, array('primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Invoice']['client_id'], 'param1' => $invoice['Invoice']['summary_total'], 'param2' => $invoice['Invoice']['payment_status'], 'param3' => $invoice['Invoice']['summary_paid'], 'param4' => $invoice['Invoice']['no'], 'param5' => $invoice['Client']['business_name'], 'param6' => $invoice['Client']['client_number']));
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->Invoice->recursive = 1;
            if (empty($id)) {
                if (!empty($_GET['number'])) {
                    $invoice_number = intval($_GET['number']);
                    $conditions['Invoice.no'] = $invoice_number;
                }
                $invoices = $this->Invoice->find('all' , ['conditions'=>$conditions]);
                $json_data = [];
                $i = 0;
                foreach ($PurchaseOrder as $k => $v) {
                    $json_data[$i]['Invoice'] = $v['Invoice'];
                    $json_data[$i]['InvoiceItem'] = $v['InvoiceItem'];
                    $i++;
                }
                die(json_encode(["success" => empty($invoices)?0:1, "invoices" => $invoices]));
            } else {
                $id = intval($id);
                $invoice = $this->Invoice->findById($id);
                die(json_encode(["success" => empty($invoice) ? 0 : 1, "invoice" => $invoice['Invoice'], "invoice_items" => $invoice['InvoiceItem']]));
            }
        } else {
            http_response_code(405);
            die("Method not allowed");
        }
    }

    private function saveClient($postdata, $id = null) {
        $this->loadModel('Client');
        //Extracting client data from post
        // = json_decode(file_get_contents("php://input"), true )  ;
	
        if (isset($postdata['client'])) {
		
            $data['Client'] = $postdata['client'];
            $this->Client->validate['business_name'] = array('notempty21' => ['rule' => 'notEmpty', 'message' => __('Required', true), 'required' => true]);
            $this->Client->validate['email'] = array();
            unset($data['Client']['id']);
			
            if (!empty($id)) {
                $data['Client']['id'] = $id;
                $action = ACTION_UPDATE_CLIENT;
            } else {
                $action = ACTION_ADD_CLIENT;
            }
            //Saving to the database
            if(IS_API){
                unset($this->Client->validate['phone2']);
            }
            $clientData = $this->Client->saveClient($data);
			
            if ($clientData['status']) {
                $client_id = $this->Client->getLastInsertID();
                $this->add_actionline(ACTION_ADD_CLIENT, array('primary_id' => $client_id, 'secondary_id' => $client_id, 'param2' => $data['Client']['business_name'], 'param3' => $data['Client']['email'], 'param4' => $clientData['data']['client_number'], 'param5' => $data['Client']['phone1'] . ' ' . $data['Client']['phone2']));
                die(json_encode(["success" => 1, "client_number" => $clientData['data']['client_number'], "client_id" => $clientData['data']['id']]));
            } else {
                $errors = "";
                foreach ($this->Client->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }

                die(json_encode(["success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        }
		else if(isset($postdata['client_detail']))
		{
			$data['ClientDetail'] = $postdata['client_detail'];
			$this->loadModel('ClientDetail');
			$this->ClientDetail->create();
			if($this->ClientDetail->save($data)){
				die(json_encode(["success" => 1, "client_id" => $data['ClientDetail']['client_id']]));
			}else{
				die(json_encode(["success" => 0, "message" => "Failed"], JSON_UNESCAPED_UNICODE));
			}
		}
		else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing Client data"]));
        }
    }

    public function clients($id = null) {
        //die ( $_SERVER['REQUEST_METHOD'] ) ;
        $this->loadModel('Client');
        $this->Client->recursive = -1;
		
		
				
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
			
            die($this->saveClient(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->saveClient(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $client = $this->Client->findById($id);
                if ($this->Client->delete($id)) {
                    $this->add_actionline(ACTION_DELETE_CLIENT, array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param4' => $client['Client']['client_number'], 'param5' => $client['Client']['phone1'] . ' ' . $client['Client']['phone2']));
                    die(json_encode(["success" => 1]));
                } else {
                    http_response_code(500);
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            if (empty($id)) {
                $conditions = [];
                if (!empty($_GET['number'])) {
                    $client_number = intval($_GET['number']);
                    $conditions['client_number'] = $client_number;
                }
                if (!empty($_GET['email'])) {
                    $client_email = $_GET['email'];
                    $conditions['email'] = $client_email;
                }
                if (!empty($_GET['phone1'])) {
                    $client_phone1 = $_GET['phone1'];
                    $conditions['phone1'] = $client_phone1;
                }
                if (!empty($_GET['phone2'])) {
                    $client_phone2 = $_GET['phone2'];
                    $conditions['phone2'] = $client_phone2;
                }
				if(!empty($_GET['name']))
				{
					$client_business_name = $_GET['name'];
					$conditions['business_name'] = $client_business_name ; 
				}
                $clients = $this->Client->find('all', ['conditions' => $conditions]);
                $success = empty($clients) ? 0 : 1;
                die(json_encode(["success" => $success, "clients" => $clients]));
            } else {
                $id = intval($id);
                $client = $this->Client->findById($id);
                die(json_encode(["success" => empty($client) ? 0 : 1, "client" => $client['Client']]));
            }
        } else {
            http_response_code(405);
            die("Method not allowed");
        }
    }

    private function saveClientAppointment($data, $id = null) {
        $this->loadModel('ClientAppointment');
        $this->loadModel('FollowUpAction');
        $this->loadModel('Post');
        $this->loadModel('Client');
        //echo 'test' ; die ;
        unset($data['ClientAppointment']['id']);
        if (!empty($id)) {
            $data['ClientAppointment']['id'] = $id;
            $action = ACTION_EDIT_CLIENT_APPOINTMENT;
        } else {
            $action = ACTION_ADD_CLIENT_APPOINTMENT;
        }
        $data['ClientAppointment']['item_type'] = Post::CLIENT_TYPE;
        //$this->data['ClientAppointment']['item_id'] = $id;
        $data['ClientAppointment']['status'] = ClientAppointment::Status_Scheduled;
        $data['ClientAppointment']['status_date'] = null;
        //$data['ClientAppointment']['date'] = $this->ClientAppointment->formatDateTime($data['ClientAppointment']['date'] . ':00');
        if ($data['ClientAppointment']['recurring'] == "" or $data['ClientAppointment']['recurring'] == 0) {
            unset($data['RecurringAppointment']);
            $this->loadModel('FollowUpAction');
                $followUpAction = [];
                if (!empty($data['ClientAppointment']['action_id'])) {
                    $action_id = intval($data['ClientAppointment']['action_id']);
                    $followUpAction = $this->FollowUpAction->findById($action_id);
                } else if (!empty($data['ClientAppointment']['action_name'])) {
                    $action_name = trim($data['ClientAppointment']['action_name']);
                    $followUpAction = $this->FollowUpAction->findByName($action_name);
                    if (empty($followUpAction)) {
                        $followUpAction = $this->FollowUpAction->save(['FollowUpAction' => [
                                'item_type' => 1,
                                'name' => $action_name,
                                'staff_id' => -3]]);
						$followUpAction['FollowUpAction']['id'] = $this->FollowUpAction->id;
                    }
                }
				if (!empty($followUpAction)) {
					$data['ClientAppointment']['action_id'] = $followUpAction['FollowUpAction']['id'];
				} else {
                    $data['ClientAppointment']['action_id'] = null;
                }
            if ($this->ClientAppointment->save($data)) {
                $statuses = $this->ClientAppointment->getStatuses();
                $FollowUpActions = $this->FollowUpAction->getList(Post::CLIENT_TYPE);
                $item = $this->Client->findById($data['ClientAppointment']['item_id']);
                $id = $this->ClientAppointment->getLastInsertID();

                if(isset($data['ClientAppointment']['partner_type']) && isset($data['ClientAppointment']['partner_id'])){
                    $this->loadModel('FollowUpReminder');
                    $this->FollowUpReminder->id= $id;
                    $this->FollowUpReminder->save(['item_id'=>$data['ClientAppointment']['item_id'],'item_type'=> 1,'partner_type'=>$data['ClientAppointment']['partner_type'], 'partner_id'=>$data['ClientAppointment']['partner_id']]);
                }

                $this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $item['Client']['id'], 'param1' => $item['Client']['business_name'], 'param2' => $item['Client']['client_number'], 'param3' => $data['ClientAppointment']['date'], 'param4' => $data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));
                debug($this->ClientAppointment->validationErrors);

                die(json_encode(["success" => 1, 'id' => $id]));
            } else {
                $verrors = "";
                foreach ($this->ClientAppointment->validationErrors as $k => $vv) {
                    $verrors .= $k . " " . $vv . ", ";
                }
                http_response_code(500);
                die(json_encode(['success' => 0, 'message' => "Failed: $verrors "], JSON_UNESCAPED_UNICODE));
            }
        } else {
            if ($this->ClientAppointment->save($data)) {
                $cAppId = $this->ClientAppointment->getLastInsertID();
                $statuses = $this->ClientAppointment->getStatuses();
                $FollowUpActions = $this->FollowUpAction->getList(Post::CLIENT_TYPE);
                $this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $item['Client']['id'], 'param1' => $item['Client']['business_name'], 'param2' => $item['Client']['client_number'], 'param3' => $data['ClientAppointment']['date'], 'param4' => $data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));

                $data['RecurringAppointment']['last_generated_id'] = $this->ClientAppointment->id;
                $data['RecurringAppointment']['last_generated_date'] = $data['ClientAppointment']['date'];
                $data['RecurringAppointment']['active'] = 1;
                $data['RecurringAppointment']['unit_name'] = $data['RecurringAppointment']['unit_name'];
                $data['RecurringAppointment']['period_unit'] = $this->ClientAppointment->RecurringAppointment->period_list[$data['RecurringAppointment']['unit_name']]['unit'];
                $data['RecurringAppointment']['unit_count'] = $this->ClientAppointment->RecurringAppointment->period_list[$data['RecurringAppointment']['unit_name']]['unit_count'];
                if ($data['RecurringAppointment']['end_date'] != "") {
                    $date = $this->ClientAppointment->RecurringAppointment->formatDateTime($data['RecurringAppointment']['end_date']);
                    $data['RecurringAppointment']['end_date'] = $date;
                }

                $this->ClientAppointment->RecurringAppointment->save($data['RecurringAppointment']);
                $his->ClientAppointment->saveField("recurring_appointment_id", $this->ClientAppointment->RecurringAppointment->id, false);
                die(json_encode(["success" => 1, 'id' => $cAppId]));
            } else {
                $verrors = "";
                foreach ($this->ClientAppointment->validationErrors as $k => $vv) {
                    $verrors .= $k . " " . $vv . ", ";
                }
                http_response_code(500);
                die(json_encode(['success' => 0, 'message' => "Failed: $verrors "], JSON_UNESCAPED_UNICODE));
            }
        }
    }

    public function my_client_appointments($id = null) {

        $this->loadModel('ClientAppointment');
        $this->loadModel('Client');

        ///die ( $_SERVER['REQUEST_METHOD'] ) ;
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->saveClientAppointment(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->saveClientAppointment(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                $statuses = $this->ClientAppointment->getStatuses();
                $FollowUpActions = $this->FollowUpAction->getList(Post::CLIENT_TYPE);
                $data = $this->ClientAppointment->findById($id);
                debug($data);
                if ($this->ClientAppointment->delete($id)) {
                    $this->add_actionline(ACTION_DELETE_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $data['Client']['id'], 'param1' => $data['Client']['business_name'], 'param2' => $data['Client']['client_number'], 'param3' => $data['ClientAppointment']['date'], 'param4' => $data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));
                    die(json_encode(["success" => 1]));
                } else {
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->ClientAppointment->recursive = -1;
            $this->Client->recursive = -1;
            if ($id == null) {
                $clients = $this->ClientAppointment->findAll();
                die(json_encode(["success" => 1, "client_appointments" => $clients]));
            } else {
                $id = intval($id);
                $client = $this->ClientAppointment->findById($id);

                die(json_encode(["success" => empty($client) ? 0 : 1, "client_appointment" => $client]));
            }
        } else {
            http_response_code(405);
            die("Method not allowed");
        }
    }

    private function saveTimeTracking($data, $id = null) {
        $this->loadModel(TimeTracking);

        if (isset($data['TimeTracking'])) {
            unset($data['TimeTracking']['id']);
            if (!empty($id)) {
                $data['TimeTracking']['id'] = $id;
                $res = $this->TimeTracking->saveTime($data);

                $action = ACTION_EDIT_TIME;
            } else {
                $action = ACTION_ADD_TIME;
                $res = $this->TimeTracking->saveTime($data);

                $id = $this->TimeTracking->getLastInsertID();
            }
            if ($res['status']) {
                debug($res);
                $this->add_actionline($action, array('primary_id' => $id, 'param1' => $res['data']['TimeTracking']['time'], 'param2' => $res['data']['TimeTracking']['project_id'], 'param3' => $res['data']['TimeTracking']['activity_id'], 'param4' => $res['data']['TimeTracking']['project_id'], 'param5' => $res['data']['TimeTracking']['activity_id'], 'param6' => $res['data']['TimeTracking']['date']));
                die(json_encode([ "success" => 1, "id" => $id]));
            } else {
                $errors = "";
                foreach ($this->TimeTracking->validationErrors as $key => $value) {
                    $errors .= "$key: $value, ";
                }
                http_response_code(500);
                die(json_encode([ "success" => 0, "message" => "Failed: $errors"], JSON_UNESCAPED_UNICODE));
            }
        } else {
            http_response_code(400);
            die(json_encode(["success" => 0, "message" => "Missing TimeTracking data"]));
        }
    }

    public function time_trackings($id = null) {
        $this->loadModel('TimeTracking');
        ///die ( $_SERVER['REQUEST_METHOD'] ) ;
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            die($this->saveTimeTracking(json_decode(file_get_contents("php://input"), true)));
        } else if ($_SERVER['REQUEST_METHOD'] == "PUT") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                die($this->saveTimeTracking(json_decode(file_get_contents("php://input"), true), $id));
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
            if (empty($id)) {
                http_response_code(400);
                die(json_encode(["success" => 0, "message" => "Missing ID"]));
            } else {
                if ($this->TimeTracking->delete($id)) {
                    //$this->add_actionline(ACTION_DELETE_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $id, 'param1' => $data['Client']['business_name'], 'param2' => $data['Client']['client_number'], 'param3' => $data['ClientAppointment']['date'], 'param4' => $data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));
                    die(json_encode(["success" => 1]));
                } else {
                    die(json_encode(["success" => 0, "message" => "Failed to delete"]));
                }
            }
        } else if ($_SERVER['REQUEST_METHOD'] == "GET") {
            $this->TimeTracking->recursive = -1;
            if ($id == null) {
                $time_trackings = $this->TimeTracking->findAll();
                die(json_encode(["success" => 1, "time_trackings" => $time_trackings]));
            } else {
                $id = intval($id);
                $time_tracking = $this->TimeTracking->findById($id);

                die(json_encode(["success" => empty($time_tracking) ? 0 : 1, "time_tracking" => $time_tracking['TimeTracking']]));
            }
        } else {
            http_response_code(405);
            die("Method not allowed");
        }
    }

    public function owner_client_settings() {
        App::import('Vendor', 'settings');
        $this->loadModel('Site');
        $apikey = settings::getValue(0, "apikey");
        if (!$apikey) {
            settings::setValue("0", "apikey", hash("sha256", time()));
            $apikey = settings::getValue(0, "apikey");
        }
        $this->data['apikey'] = $apikey;
        //debug (  $this->data ['Settings'] );
        if (isset($this->data ['Settings']['sent']) && $this->data ['Settings']['sent']) {
            unset($this->data ['Settings']['sent']);
            /* $client_data = getCurrentSite() ;
              $client_data['client_number'] = $client_data['id'] ;
              foreach ($client_data as $k => $v ) {$client_data[ '$'.$k ] = $v ; unset ($client_data[$k] );}
              $string = str_replace ( array_keys ($client_data ), array_values ($client_data) , json_encode ($this->data ['Settings']) ) ; */

            settings::setValue(0, "clients_api", json_encode($this->data ['Settings']));
            $this->flashMessage(sprintf(__('%s  has been saved', true), __('Settings', true)), 'Sucmessage');
        }
        $this->data['Settings'] = json_decode(settings::getValue(0, "clients_api"), true);


        $this->render('/api/client_settings');
    }
	
	public function owner_send_message()
	{
		App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		$sms_error = '';
		$sms_campaign_id = \SendSMS\SendSMS::sendMessage(array_map(function($elem) use($client){
				return ['phone'=>$elem, 'country_code'=>$client['Client']['country_code'], 'ref_id'=>$client['Client']['id'], 'ref_type'=>\SendSMS\SendSMS::CLIENT];
		}, explode(",", $this->data['SmsTemplate']['to_phone'])), $this->data['SmsTemplate']['body'],$sms_error);

	}

    private function executeSelectSql($dbConn, $query)
    {
        $result = mysqli_query($dbConn, $query);
        $data = ['data' => []];
        if ($result) {
            $data['status'] = true;
            while ($row = mysqli_fetch_assoc($result)) {
                $data['data'][] = $row;
            }
        } else {
            $data['status'] = false;
            $data['message'] = "mysql error: ".mysqli_error($dbConn);
        }
        return $data;
    }

    private function executeInsertSql($dbConn, $query)
    {
        $result = mysqli_query($dbConn, $query);
        if ($result) {
            return ['id' => mysqli_insert_id($dbConn), 'status' => true];
        } else {
            return ['status' => false, 'message' => "mysql error: ".mysqli_error($dbConn)];
        }
    }

    private function executeGenericSql($dbConn, $query)
    {
        $result = mysqli_query($dbConn, $query);
        if ($result) {
            return ['status' => true];
        } else {
            return ['status' => false, 'message' => "mysql error: ".mysqli_error($dbConn)];
        }
    }
}
