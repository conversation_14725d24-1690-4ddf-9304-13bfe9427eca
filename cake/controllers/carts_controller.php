<?php

class CartsController extends AppController {

	var $name = 'Carts';

	/**
	 * @var Content
	 */
	var $Cart;
	var $helpers = array('Html', 'Form');

	function test()
	{
				$this->loadModel('Invoice');
				die($this->Invoice->getNextInvoiceNo());
	}
	
	public function add_to_cart($product_id=null,$qty = 1)
	{
		App::import('Vendor','CartClass', array('file' => 'class.Cart.php'));

		$data = array();
		if($product_id && $this->RequestHandler->isAjax()){
			
			if($cart_item_data = $this->Cart->setItemData($product_id))
			{
				$cart = new ShopingCart();
//				$cart->clear();	
				$data['status'] = 'success';
				
				if($cart->isItemExists($product_id,$cart_item_data)){
					
					$data['exists'] = true;
				}
                $cart->add($product_id,$qty, $cart_item_data);

                $cart_item = $cart->getItem($product_id);
				$data['product']['price'] = $cart_item['attributes']['price'];
				$data['product']['name'] = $cart_item['attributes']['name'];
				$data['product']['image'] = $cart_item['attributes']['image'];
				$data['product']['description'] = $cart_item['attributes']['description'];
				$data['product']['quantity'] = $cart_item['quantity'];
				$data['cart_total'] = $cart->getAttributeTotal('price');
				die(json_encode($data));
			}
		}
		$data['status'] = 'fail';
		die(json_encode($data));
	}


    public function update_qty($product_id = null,$qty = 1)
    {
        App::import('Vendor','CartClass', array('file' => 'class.Cart.php'));

        $data = array();
        if($product_id && $this->RequestHandler->isAjax()){

            if($cart_item_data = $this->Cart->setItemData($product_id))
            {
                $cart = new ShopingCart();
//				$cart->clear();
                $data['status'] = 'success';

                if($cart->isItemExists($product_id,$cart_item_data)){
                    $cart->update($product_id, ['quantity' => $qty], $cart_item_data);
                    $data['exists'] = true;
                }else {
                    $cart->add($product_id,$qty, $cart_item_data);
                }
	            $cart->update(-1, ['attributes' => ['up_to_date' => false]], $cart_item_data);

                $cart_item = $cart->getItem($product_id);
                $data['product']['price'] = $cart_item['attributes']['price'];
                $data['product']['name'] = $cart_item['attributes']['name'];
                $data['product']['code'] = $cart_item['attributes']['code'];
                $data['product']['discount'] = $cart_item['attributes']['discount'];
                $data['product']['discount_type'] = $cart_item['attributes']['discount_type'];
                $data['product']['original_price'] = $cart_item['attributes']['original_price'];
                $data['product']['tax1'] = $cart_item['attributes']['tax1'];
                $data['product']['tax2'] = $cart_item['attributes']['tax2'];
                $data['product']['image'] = $cart_item['attributes']['image'];
                $data['product']['unit_factor'] = $cart_item['attributes']['unit_factor'];
                $data['product']['unit_factor_id'] = $cart_item['attributes']['unit_factor_id'];
                $data['product']['description'] = $cart_item['attributes']['description'];
                $data['product']['quantity'] = $cart_item['quantity'];
                $data['cart_total'] = $cart->getAttributeTotal('price');
                die(json_encode($data));
            }
        }
        $data['status'] = 'fail';
        die(json_encode($data));
    }
	
	public function remove_from_cart($product_id = null)
	{
		App::import('Vendor','CartClass', array('file' => 'class.Cart.php'));
		
		$data = array();
		if($product_id && $this->RequestHandler->isAjax()){
				
			$cart = new ShopingCart();
			
			if($cart_item_data = $this->Cart->setItemData($product_id))
			{
				if($cart->isItemExists($product_id,$cart_item_data)){
					$cart->remove($product_id, $cart_item_data);
					$data['status'] = 'success';
					$data['cart_total'] = format_price_simple($cart->getAttributeTotal('price'));
					$data['cart_total_items'] = format_price($cart->getTotalItem());
					$cart->update(-1, ['attributes' => ['up_to_date' => false]], $cart_item_data);
					die(json_encode($data));
				}
			}
		}
		$data['status'] = 'fail';
		die(json_encode($data));
	}
	
	function update(){
		App::import('Vendor','CartClass', array('file' => 'class.Cart.php'));

		$cart = new ShopingCart([]);
		foreach($_POST as $k => $qty){
			$key_parts = explode('-', $k);
			$item_id = end($key_parts);
			if(is_numeric($item_id)){
				$cart->update($item_id, ['quantity' => $qty], $cart->getItem($item_id)['attributes']);
			}
		}
		if($invoice = $cart->getItem(-1)){
			//if invoice is set and the total is changed set update flag
			debug($cart->getAttributeTotal('price'));

			if($invoice['attributes']['invoice_total'] != $cart->getAttributeTotal('price')){
									
				$cart->update(-1, ['quantity' => 0, 'attributes' => ['up_to_date' => false, 'invoice_total' =>$cart->getAttributeTotal('price') ]], $invoice['attributes']);
			}
		}
		$this->redirect('/client/website_front/contents/checkout');
	}
	
}
?>