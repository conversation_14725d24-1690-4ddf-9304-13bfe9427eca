<?php

use App\Helpers\UsersHelper;

class PosShiftTransactionsController extends AppController {

    var $name = 'PosShiftTransactions';

    /**
     * @var PosShiftTransaction
     */
    var $PosShiftTransaction;
    var $helpers = array('Html', 'Form');

    function owner_index() {
        $this->PosShiftTransaction->recursive = 0;
        $conditions = $this->_filter_params();
        $this->set('posShiftTransactions', $this->paginate('PosShiftTransaction', $conditions));
    }

    function owner_view($id = null) {
        if (!$id) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Transaction', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $this->set('posShiftTransaction', $this->PosShiftTransaction->read(null, $id));
    }

    function owner_add($type = null) {
        if(!$type)
            $this->cakeError('error404');
        $this->loadModel('ItemPermission');
        if($type != ItemPermission::PERMISSION_DEPOSIT && $type != ItemPermission::PERMISSION_WITHDRAW)
            $this->cakeError('error404');
        if(!$this->PosShiftTransaction->PosShift->getOpenedSessionID()) {
            $this->flashMessage(__('Please add pos shift first'));
            $this->redirect(Router::url(['controller' => 'posShifts', 'action' => 'open']));
        }
        $this->loadModel('Staff');
        $treasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,$type);
        $staffs = $this->Staff->getList();
        if (!empty($this->data)) {
            $this->PosShiftTransaction->create();
            $this->data['PosShiftTransaction']['staff_id'] = getAuthOwner('staff_id');
            $this->data['PosShiftTransaction']['currency'] = getCurrentSite('currency_code');
            if($this->data['PosShiftTransaction']['partner_type'] == PosShiftTransaction::PARTNER_STAFF){
                if($this->data['PosShiftTransaction']['staff_id'] != $this->data['PosShiftTransaction']['partner_id']) {
                    if (!authStaff($this->data['PosShiftTransaction']['partner_id'], $this->data['PosShiftTransaction']['password'])) {
                        $this->flashMessage(sprintf(__('The %s could not be saved. Wrong Staff password', true), __('Transaction', true)));
                        $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$this->data['PosShiftTransaction']['pos_shift_id']));
                    }
                }
            }
            $this->data['PosShiftTransaction']['date_time'] = $this->PosShiftTransaction->formatDateTime($this->data['PosShiftTransaction']['date_time']);
            if ($this->PosShiftTransaction->save($this->data)) {
                $action_line = [];
                $action_line['primary_id'] = $this->data['PosShiftTransaction']['pos_shift_id'];
                $action_line['secondary_id'] = $this->PosShiftTransaction->getInsertID();
                $action_line['param1'] = $this->data['PosShiftTransaction']['amount'];
                $action_line['param2'] = $this->data['PosShiftTransaction']['currency'];
                $action_line['param3'] = $this->data['PosShiftTransaction']['type'] == PosShiftTransaction::TYPE_IN ? 'Cash In' : 'Cash Out';
                $action_line['param4'] = $this->data['PosShiftTransaction']['type'] == PosShiftTransaction::TYPE_IN ? 'From' : 'To';
                $action_line['param5'] = $this->data['PosShiftTransaction']['partner_type'] == PosShiftTransaction::PARTNER_STAFF ? $staffs[$this->data['PosShiftTransaction']['partner_id']] : $treasuries[$this->data['PosShiftTransaction']['partner_id']];
                $this->PosShiftTransaction->add_actionline(ACTION_POS_SESSION_TRANSACTION_ADD,$action_line);
                $this->flashMessage(sprintf (__('The %s has been saved', true), __('Transaction',true)), 'Sucmessage');
                $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$this->data['PosShiftTransaction']['pos_shift_id']));
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Transaction',true)));
                $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$this->data['PosShiftTransaction']['pos_shift_id']));
            }

        }
        $this->set(compact('treasuries','staffs'));
    }

    function owner_edit($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', 'Transaction',true)));
            $this->redirect(array('action'=>'index'));
        }
        $posShiftTransaction = $this->PosShiftTransaction->read(null, $id);
        if ($posShiftTransaction['PosShift']['status'] != PosShift::STATUS_OPENED) {
            $this->flashMessage(sprintf (__('The %s is not opened, make sure it\'s opened', true), __('POS Session',true)));
            $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$posShiftTransaction['PosShiftTransaction']['pos_shift_id']));
        }
        if (!empty($this->data)) {
            if($posShiftTransaction['PosShiftTransaction']['partner_type'] == PosShiftTransaction::PARTNER_STAFF){
                if($posShiftTransaction['PosShiftTransaction']['staff_id'] != $posShiftTransaction['PosShiftTransaction']['partner_id']) {
                    if (!authStaff($posShiftTransaction['PosShiftTransaction']['partner_id'], $this->data['PosShiftTransaction']['password'])) {
                        $this->flashMessage(sprintf(__('The %s could not be saved. Wrong Staff password', true), __('Transaction', true)));
                        $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$posShiftTransaction['PosShiftTransaction']['pos_shift_id']));
                    }
                }
            }
            if ($this->PosShiftTransaction->save($this->data)) {
                $this->flashMessage(sprintf (__('The %s  has been saved', true), __('Transaction',true)), 'Sucmessage');
                $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$posShiftTransaction['PosShiftTransaction']['pos_shift_id']));
            } else {
                $this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('Transaction',true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $posShiftTransaction;
        }
        $this->loadModel('ItemPermission');
        $this->loadModel('Staff');
        $treasuries = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,$_GET['type']);
        $staffs = $this->Staff->getList();
        $this->set(compact('treasuries','staffs'));
        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('Transaction',true)));
            $this->redirect(array('action'=>'index'));
        }
        $module_name= __('Transaction', true);
        $posShiftTransaction = $this->PosShiftTransaction->read(null, $id);
        if (empty($posShiftTransaction)){
            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {

            if ($_POST['submit_btn'] == 'yes') {
                if($posShiftTransaction['PosShiftTransaction']['partner_type'] == PosShiftTransaction::PARTNER_STAFF){
                    if($posShiftTransaction['PosShiftTransaction']['staff_id'] != $posShiftTransaction['PosShiftTransaction']['partner_id']) {
                        if (!authStaff($posShiftTransaction['PosShiftTransaction']['partner_id'], $this->data['PosShiftTransaction']['password'])) {
                            $this->flashMessage(sprintf(__('The %s could not be deleted. Wrong Staff password', true), __('Transaction', true)));
                            $this->redirect(array('action'=>'delete',$posShiftTransaction['PosShiftTransaction']['id']));
                        }
                    }
                }
//                if($this->PosShiftTransaction->deleteAll(array('PosShiftTransaction.id'=>$_POST['ids']))) {
                    foreach($_POST['ids'] as $id){
                        $this->PosShiftTransaction->delete($id);
                    }
                    $this->flashMessage(sprintf(__('%s deleted', true), $module_name), 'Sucmessage');
                    $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$posShiftTransaction['PosShiftTransaction']['pos_shift_id']));
                //}
            }
            else{
                $this->redirect(array('controller' => 'pos_shifts','action'=>'view',$posShiftTransaction['PosShiftTransaction']['pos_shift_id']));
            }
        }
        $this->set('posShiftTransaction',$posShiftTransaction);
    }

    function owner_view_session($session_id) {
        $this->set('title_for_layout',  __('POS Session Cash Movement',true));
        $this->PosShiftTransaction->recursive = -1;
        $conditions = ['pos_shift_id' => $session_id];
        $this->set('posShiftTransactions', $this->PosShiftTransaction->find('all', ['conditions' => $conditions]));
        $this->set('posShift', $this->PosShiftTransaction->PosShift->find('first',['conditions' => ['PosShift.id' => $session_id]]));
        $this->loadModel('ItemPermission');
        $this->loadModel('Staff');
        $treasuriesDeposit = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT);
        $treasuriesWithdraw = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW);
        $staffs = UsersHelper::getInstance()->getList();
        $this->set(compact('treasuriesDeposit','treasuriesWithdraw','staffs'));
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
    }
}
?>