<?php

use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\StocktakingSourceEntryUtil;

/**
 * @property Tooltip $Tooltip
 */
class StocktakingsController extends AppController {

    var $name = 'Stocktakings';

    var $Product;

    function beforeFilter() {
        parent::beforeFilter();

    }
    function owner_index ( ) {
        if(!check_permission([PermissionUtil::REQUISITION_VIEW])){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!IS_REST) {
            return $this->redirect('/v2/owner/entity/stocktaking/list');
        }
        $this->loadModel('RequisitionItem');
        $this->loadModel('Requisition');
        $bind_model_arr = [
            'hasOne' => [
                'RequisitionIn' => ['className' => 'Requisition', 'recursive' => 1, 'foreignKey' => 'order_id', 'conditions' => ['RequisitionIn.order_type' => Requisition::ORDER_TYPE_STOCKTAKING_IN]],
                'RequisitionOut' => ['className' => 'Requisition', 'recursive' => 1, 'foreignKey' => 'order_id', 'conditions' => ['RequisitionOut.order_type' => Requisition::ORDER_TYPE_STOCKTAKING_OUT]],
            ],
            'belongsTo' => [
                'Store' => ['className' =>'Store','recursive' => -1],
            ]
        ];
        $this->Stocktaking->bindModel($bind_model_arr ,false);
        $this->set('filters' , $this->Stocktaking->getFilters());
        $conditions = $this->_filter_params();
        $this->loadModel("ItemPermission");
        $stores_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING,null,false,true);
        $stores_listIDs = array_keys($stores_list);
        if (!isset($conditions['Stocktaking.store_id']) || !in_array($conditions['Stocktaking.store_id'], $stores_listIDs)) {
            $conditions['Stocktaking.store_id'] = $stores_listIDs;
        }
        $this->paginate['order'] =  'Stocktaking.created desc';
        $this->paginate['Stocktaking']['group'] =  ['Stocktaking.id'];
        $stocktakings = $this->paginate ('Stocktaking',$conditions) ;
        foreach ( $stocktakings as $k => $stock){
            if ( !empty($stock['RequisitionIn']))
            {
                $stocktakings[$k]['RequisitionIn']['sum_all'] = $this->RequisitionItem->find('first' , ['fields' =>'SUM(unit_price*quantity) as SUM_ALL', 'conditions' => ['requisition_id' => $stock['RequisitionIn']['id'] ] ])[0]['SUM_ALL'];
            }
            if ( !empty($stock['RequisitionOut']))
            {
                $stocktakings[$k]['RequisitionOut']['sum_all'] = $this->RequisitionItem->find('first' , ['fields' => 'SUM(unit_price*quantity) as SUM_ALL', 'conditions' => ['requisition_id' => $stock['RequisitionOut']['id'] ] ])[0]['SUM_ALL'];
            }
        }
        $this->set ( 'stocktakings' , $stocktakings ) ;
        $this->pageTitle=__("Stocktakings" , true );
    }
    function owner_view ( $id = null  ) {
        if(!check_permission([PermissionUtil::REQUISITION_VIEW])){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('Requisition');
        $bind_model_arr = [
            'hasOne' => array(
                'RequisitionIn' => array('className' =>'Requisition','recursive' => 1, 'foreignKey' => 'order_id' , 'conditions' => ['RequisitionIn.order_type' => Requisition::ORDER_TYPE_STOCKTAKING_IN]) ,
                'RequisitionOut' => array('className' =>'Requisition','recursive' => 1, 'foreignKey' => 'order_id', 'conditions' => ['RequisitionOut.order_type' => Requisition::ORDER_TYPE_STOCKTAKING_OUT  ]),
            ),];
        $this->Stocktaking->bindModel($bind_model_arr ,false);
        $stocktaking = $this->Stocktaking->getStocktaking($id);
        if ( empty($stocktaking)){
            $this->flashMessage(__('Stocktaking not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        $requisitions = $this->Requisition->find('all' , ['applyBranchFind' => false, 'conditions' => ['Requisition.order_id' => $id,'Requisition.order_type' => [Requisition::ORDER_TYPE_STOCKTAKING_IN,Requisition::ORDER_TYPE_STOCKTAKING_OUT] ] ]);
        $this->set('statuses', Stocktaking::$statuses[$stocktaking['Stocktaking']['status']]);
        $this->set('requisitions', $requisitions);
        $this->set('stocktaking', $this->getTRackingNumbersAndType($stocktaking));

        $this->loadModel('PrintableTemplate');
        $this->set('defaultTemplate', $this->PrintableTemplate->getDefaultTemplateForType('stocktaking'));

        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('stocktaking');

        $this->set('has_templates', false);
        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('stocktaking');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);
    }

    function owner_timeline ( $id ){
            $this->set('is_ajax', false);
            if ($this->RequestHandler->isAjax()) {
                $this->set('is_ajax', true);
            }

            $action_line_data = $this->Stocktaking->getActionLineData  ( $id ) ;
            $all_actions_lists = $action_line_data['all_actions_lists'];
            $all_action_ids = $action_line_data['all_action_ids'];

            require_once APP . 'vendors' . DS . 'Timeline.php';
            $timeline = new Timeline('All', array('secondary_id' => $id));
            $timeline->init(array('ActionLine.id' => $all_action_ids),$all_actions_lists );
            $this->set('data', $timeline->getDataArray());



            foreach ($timeline->ActionKeys as $key => $action) {
                if (in_array($key, $all_actions_lists)) {
                    $invoice_actions[$key] = $action;
                }
            }
            $this->set('actions', $invoice_actions);

    }
    function owner_add($id = null) {
        if(!check_permission([PermissionUtil::REQUISITION_ADD])){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('StocktakingRecord');
        $this->loadModel('Store');
        App::import('Vendor', 'AutoNumber');
        if (!empty($this->data)) {
            $this->data['Stocktaking']['source_entry'] = IS_REST ? StocktakingSourceEntryUtil::getSourceMobileApp() : StocktakingSourceEntryUtil::getSourceWeb();
            $stocktaking=$this->data;
            $dod=settings::getValue(InventoryPlugin, 'disable_overdraft');
            $willSave = true;
            if ($willSave) {
                if (empty( $this->data['Stocktaking']['branch_id'])) {
                    $this->data['Stocktaking']['branch_id']= getCurrentBranchID();
                }
                $result = $this->Stocktaking->addStocktaking($this->data);
                if ($result['status']) {
                    $this->flashMessage(sprintf(__('%s has been saved', true), __('Stocktaking', true)), 'Sucmessage');
                    if (IS_REST)
                        die(json_encode(['status' => true, 'id' => $this->Stocktaking->id]));


                    $this->redirect('/v2/owner/stocktakings/'.$result['data']['Stocktaking']['id']);
                } else {
                    if (IS_REST)
                        die(json_encode(['status' => false, 'message' => 'Could not save the Stocktaking, Please fix errors below', 'errors' => $this->Stocktaking->validationErrors]));
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Stocktaking', true)));
//                    if (!empty( $this->Stocktaking->validationErrors)) {
//                        $this->flashMessage(implode("<br/>", $this->Stocktaking->validationErrors), 'Errormessage', 'secondaryMessage');
//                    }
                }
            }
        }else {
            $this->data['Stocktaking']['number'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_STOCKTAKING);
        }
        $this->_settings();
    }
    function owner_adjust($id = null){
        if(!check_permission([PermissionUtil::REQUISITION_ADD])){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        set_time_limit(500);
        ini_set('memory_limit', '10G');

        $StocktakingModel = GetObjectOrLoadModel('Stocktaking');
        $result = $StocktakingModel->find('first', ['recursive' => 2,'applyBranchFind' => false, 'conditions' => [
            'Stocktaking.id' => $id,
            'Stocktaking.status' => Stocktaking::STATUS_DRAFT
        ]]);
        $StocktakingRecord = GetObjectOrLoadModel('StocktakingRecord');
        $stocktakingRecords = $StocktakingRecord->findByStocktakingId($id);
        if(!$stocktakingRecords) {
            $this->flashMessage(__("The stocktaking can't be adjusted as it dose'nt contain any records", true), 'Errormessage');
            return $this->redirect(['controller' => 'stocktakings', 'action'=>'view',  $id]);
        }

        $dod = settings::getValue(InventoryPlugin, 'disable_overdraft');
        if ($dod && ifPluginActive(InventoryPlugin)) {
            $stocktaking = $this->Stocktaking->getStocktaking($id);
            $this->loadModel('Product');

            foreach ($stocktaking['StocktakingRecord'] as $citem) {
                if ($citem['shortage_value'] < 0) {
                    $type = 'deduct';
                } else {
                    $type = 'add';
                }
                if ($citem['Product']['tracking_type'] == \App\Utils\TrackStockUtil::QUANTITY_ONLY && !GetObjectOrLoadModel('StockTransaction')->check_balance_open($citem['product_id'], $stocktaking['Stocktaking']['store_id'], abs($citem['shortage_value']), 0, $type)) {
                    $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $citem['product_id']]]);
                    $name_code = $product['Product']['name'] . ' #' . (empty($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                    $error_msg = sprintf(__('Amount not sufficient for %s', true), strongStr($name_code)) .' '. __t('You cannot save the transaction with negative total amount');
                    $this->flashMessage($error_msg, 'Errormessage', 'secondaryMessage');
                    return $this->redirect(['controller' => 'stocktakings', 'action' => 'view',  $id]);
                    break;
                }
            }
        }
        $is_stocktaking_adjustable = $this->Stocktaking->isStockTakingAdjustable($id);
        if(!$is_stocktaking_adjustable) {
            $this->flashMessage(__("The stocktaking can't be adjusted as some items don't contain physical count", true), 'Errormessage');
            return $this->redirect(['controller' => 'stocktakings', 'action'=>'view',  $id]);
        }
        $this->set('is_stocktaking_adjustable', $is_stocktaking_adjustable);
        if ($result){
            $quantity_validate = $StocktakingModel->validateEqualQuantity($result);
            if (!empty($quantity_validate)) {
                return $this->redirect(['controller' => 'stocktakings', 'action' => 'view',  $id]);
            }
          $validation =  $StocktakingModel->updateForRequisition($result);
          if (!empty($validation) ) {
            return $this->redirect(['controller' => 'stocktakings', 'action' => 'view',  $id]);

          }

            
            

            $StocktakingModel->id = $id;
            $StocktakingModel->saveField('status', Stocktaking::STATUS_ADJUSTED);
            $this->flashMessage(sprintf(__('The %s has been Adjusted successfully', true), __('Stocktaking', true)), 'Sucmessage');
            return $this->redirect(['controller' => 'stocktakings', 'action'=>'view',  $id]);
        }
        $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Stocktaking', true)));
        return $this->redirect(['controller' => 'stocktakings', 'action'=>'view', $id]);
    }


    function owner_edit($id = null) {
        if(!check_permission([PermissionUtil::REQUISITION_MODIFY])){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $stocktaking = $this->Stocktaking->getStocktaking($id);
        if (!$stocktaking) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Stocktaking",true)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if ($stocktaking['Stocktaking']['status'] == Stocktaking::STATUS_ADJUSTED) {
            $this->flashMessage(__("You cannot edit in an adjusted stocktaking",true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        $store_active= $this->Stocktaking->isStoreActive(['store_id'=>$stocktaking['Stocktaking']['store_id']]);
        if (!$store_active) {
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($this->data)) {
            App::import('Vendor', 'AutoNumber');
            \AutoNumber::set_validate(\AutoNumber::TYPE_STOCKTAKING);
            $products = [];
            $willSave = true;
            foreach ($this->data['StocktakingRecord'] as $invoiceItem) {
                $products[] = $invoiceItem['product_id'];

            }

            foreach ($stocktaking['StocktakingRecord'] as $invoiceItem) {
            $oldproduct[$invoiceItem['product_id']]=$invoiceItem['shortage_value'];

            }

            $dod=settings::getValue(InventoryPlugin, 'disable_overdraft');
            if ($dod && ifPluginActive(InventoryPlugin))
            {


                foreach ($this->data['StocktakingRecord'] as $citem) {

                    $real_difference=$citem['shortage_value']-$oldproduct[$citem['product_id']];
                    if($real_difference<0){
                        $type='deduct';
                    }else{
                        $type='add';
                    }

                    if (!GetObjectOrLoadModel('StockTransaction')->check_balance_open($citem['product_id'], $stocktaking['Stocktaking']['store_id'], abs($real_difference), 0, $type)) {

                        $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $citem['product_id']]]);
                        $name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                        $error_msg = sprintf(__('Can\'t update  Stocktaking #' . $stocktaking['Stocktaking']['number'] . ' Amount not sufficient for %s', true), strongStr($name_code));
                        $this->flashMessage($error_msg, 'Errormessage', 'secondaryMessage');
                        $willSave = false;
                        break;
                    }
                }
            }

            $this->loadModel('Product');
            $oldRecursive = $this->Product->recursive;
            $this->Product->recursive = -1;
            $productsData = $this->Product->find('all', ['conditions' => ['Product.id' => $products]]);
            foreach ($productsData as $product) {
                if ($product['Product']['tracking_type'] != \App\Utils\TrackStockUtil::QUANTITY_ONLY) {
                    $this->flashMessage(__('You cannot choose any product with tracking type in the stocktaking', true));
                    $willSave = false;
                    break;
                }
            }
            $this->Product->recursive = $oldRecursive;
            if ($willSave) {
                $result = $this->Stocktaking->updateStocktaking($this->data, false);
                if ($result['status']) {
                    //$this->Stocktaking->updateForRequisition($result['data']);
                    $this->flashMessage(sprintf(__('%s has been saved', true), __('Stocktaking', true)), 'Sucmessage');
                    $this->redirect('/v2/owner/stocktakings/'.$result['data']['Stocktaking']['id']);
                } else {
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Stocktaking', true)));
                    if (isset($result['message'])) {
                        if (IS_REST)
                            die(json_encode(['status' => false, 'message' => $result['message']]));
                        $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
//                        if (!empty( $this->Stocktaking->validationErrors)) {
//                            $this->flashMessage(implode("<br/>", $this->Stocktaking->validationErrors), 'Errormessage', 'secondaryMessage');
//                        }
                    }
                }
            }
        } else {
            $this->data = $stocktaking;
        }
        $this->set(compact('stocktaking'));
        $this->_settings($id);
        $this->render('owner_add');
    }
    function owner_delete($id = null) {
        if(!check_permission([PermissionUtil::REQUISITION_MODIFY])){
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('Post');
        $this->loadModel('Product');
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('Stocktaking', true)));
            $referer_url = !($this->Session->check('referer_url')) ? array('action' => 'index') : $this->Session->read('referer_url');
            $this->Session->delete('referer_url');
            $this->redirect($referer_url);
        }
        $module_name = __('Stocktaking', true);
        if (isset($_POST['ids']) && is_array($_POST['ids']) && count($_POST['ids']) > 1) {
            $module_name = __('Stocktakings', true);
        }

        $stocktakings = $this->Stocktaking->find('all', array('applyBranchFind' => false, 'conditions' => array('Stocktaking.id' => $id)));
        if (empty($stocktakings)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect(array('action' => 'index'));
        }
        $dod=settings::getValue(InventoryPlugin, 'disable_overdraft');
        foreach ($stocktakings as $stocktaking) {
            if ($stocktaking['Stocktaking']['status'] != Stocktaking::STATUS_ADJUSTED) {
                continue;
            }
            $this->validate_open_day($stocktaking['StockTaking']['date']);
            if ($dod) {
                foreach ($stocktaking['StocktakingRecord'] as $citem) {
                    if ($dod && !GetObjectOrLoadModel('StockTransaction')->check_balance_open($citem['product_id'], $stocktaking['Stocktaking']['store_id'], $citem['shortage_value'], 0, 'deduct')) {
                        $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $citem['product_id']]]);
                        $name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                        $error_msg = sprintf(__("Can't delete Stocktaking #%s Amount not sufficient for %s", true), $stocktaking['Stocktaking']['number'], strongStr($name_code));
                        $this->flashMessage($error_msg, 'Errormessage', 'secondaryMessage');
                        $this->redirect(['action' => 'index']);
                        break;
                    }
                }
            }
        }
        if (IS_REST && !empty($id)) {
            if ($this->Stocktaking->deleteWithRelated($id)) {
                $this->set("message", sprintf(__('%s has been deleted', true), ucfirst($module_name)));
                return $this->render("success");
            }
            return $this->cakeError("error500", ["message" => sprintf(__('Could not delete StockTaking %s, ', true), $id)]);
        }
        if (!empty($_POST['submit_btn']) && !empty($id)) {
            if ($_POST['submit_btn'] == 'yes' && $this->Stocktaking->deleteWithRelated($id)) {
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            }
        }
        $this->set('stocktakings', $stocktakings);
        $this->set('module_name', $module_name);
        $this->set('title_for_layout',  __('Delete',true).' ' .__($module_name, true));

    }
    function owner_update_draft($id = null){
        $this->loadModel('Product');
        $stocktaking = $this->Stocktaking->getStocktaking($id);
        if (!$stocktaking) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Stocktaking",true)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        $dod=settings::getValue(InventoryPlugin, 'disable_overdraft');
        if ($dod) {
            foreach ($stocktaking['StocktakingRecord'] as $citem) {
                if ($dod && !GetObjectOrLoadModel('StockTransaction')->check_balance_open($citem['product_id'], $stocktaking['Stocktaking']['store_id'], $citem['shortage_value'], 0, 'deduct')) {
                    $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $citem['product_id']]]);
                    $name_code = $product['Product']['name'] . ' #' . (empty ($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']);
                    $error_msg = sprintf(__("Can't delete Stocktaking #%s Amount not sufficient for %s", true), $stocktaking['Stocktaking']['number'], strongStr($name_code));
                    $this->flashMessage($error_msg, 'Errormessage', 'secondaryMessage');
                    if($_GET['back']=="listing"){
                        $this->redirect(['action' => 'index']);
                    }else {
                        $this->redirect(['action' => 'view', $id]);
                    }
                    break;
                }
            }
        }
        $this->Stocktaking->deleteRequisition($id);
        $this->Stocktaking->id=$id;
        $this->Stocktaking->saveField('status',Stocktaking::STATUS_DRAFT);
        $this->flashMessage(sprintf(__('%s has been updated', true), 'Stocktaking'), 'Sucmessage');
        if($_GET['back']=="listing"){
            $this->redirect(['action' => 'index']);
        }else {
            $this->redirect(['action' => 'view', $id]);
        }

    }
    function _settings ( $editID = false ){
        $aps = '1';
        $this->loadModel('Product');
        $productOnlyConditions = ['track_stock' => 1] ;
        $products_limit = 11;
        if ($editID) {
            $StocktakingRecordsModel = GetObjectOrLoadModel('StocktakingRecord');
            $productIds = $StocktakingRecordsModel->find('list', [
                'conditions' => ['StocktakingRecord.stocktaking_id' => $editID],
                'fields' => ['StocktakingRecord.product_id'],
            ]);
            $products_limit = count($productIds);
            if ($productIds) {
                $productOnlyConditions['Product.id'] = $productIds;
            }
        }
//        $products = $this->Product->getInvoiceProductList(false, $productOnlyConditions, true, [], $products_limit);
//        $this->set('products', $products);
        $this->set('aps', $aps);
        $this->loadModel('Store');
        $primary = $this->Store->getPrimaryStore();
        $this->set('primaryStore', $primary);
        $this->set('title_for_layout',  sprintf(__('Add %s', true) , __("Stocktaking",true)));
        $this->loadModel('ItemPermission');
        $this->set('primary_store', $this->Store->getPrimaryStore());
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING));
    }


    private function getTRackingNumbersAndType($items)
    {
        if(!ifPluginActive(PRODUCT_TRACKING_PLUGIN)) return $items;
        $this->loadModel('StockingTrackingNumbers');
        foreach($items['StocktakingRecord'] as &$item){
            if($item['Product']['tracking_type'] != \App\Utils\TrackStockUtil::QUANTITY_ONLY){
                $trackingNumbers = $this->StockingTrackingNumbers->find('all',['conditions' => ['record_id' => $item['id'] ]]);
                $item['tracking_numbers_type'] = ['tracking_numbers' => $trackingNumbers , 'type' => $item['Product']['tracking_type']];   
            }
        }
        return $items;
    }
}
?>
