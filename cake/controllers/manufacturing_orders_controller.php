<?php

use App\Validators\ManufacturingOrders\FinishValidator;
use App\Helpers\ManufacturingOrderHelper;
use App\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\PermissionUtil as PermissionUtilAlias;
use App\Helpers\CurrencyHelper;
use App\Services\Requisitions\Events\RequisitionsCreated;
use App\Services\Requisitions\Events\RequisitionsUpdated;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\ManufacturingOrderStatusUtil;

class ManufacturingOrdersController extends AppController {

	var $name = 'ManufacturingOrders';

    /**
     * @var StockRequest
	 */

    public $StockRequest;
	/**
	 * @var ManufacturingOrder
	 */
    public $ManufacturingOrder;

    public $autoRender = false;
	var $helpers = array('Html', 'Form');
    var $components = ['StockValidation'];

    public function owner_return_manufacturing_orders_materials($id)
    {
        $this->loadModel('StockRequest');
        $this->loadModel('Requisition');
        $this->loadModel('Store');
        $this->loadModel('Product');
        $this->loadModel('ManufacturingOrder');

        $manufacturingOrdersService = resolve(\Izam\ManufacturingOrder\Services\ManufacturingOrderService::class);

        if (!check_permission(PermissionUtilAlias::REQUISITION_ADD)) {
            $this->flashMessage(__('You are not allowed to perform this action', TRUE));
            $this->redirect('/v2/owner/entity/manufacturing_order/list');
        }
        
        $manufacturingOrder = $this->ManufacturingOrder->find('first', ['conditions' => ['ManufacturingOrder.id' => $id]]);

        if(in_array($manufacturingOrder["ManufacturingOrder"]["status"], [ManufacturingOrderStatusUtil::FINISHED, ManufacturingOrderStatusUtil::CLOSED])){
            $this->redirect($this->referer());
        }
      
        $orderType = Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND;
       
        $requisitionData = $manufacturingOrdersService->getRequisitionsCanRefund($id);
        if (count($requisitionData[0]['RequisitionItem'])){
            $requisition = $requisitionData[0];
             
            $this->Product->recursive = -1;
            $productIds = array_map(function($item) {
                return $item['product_id'];
            },$requisition['RequisitionItem']);
            $products = $this->Product->getInvoiceProductList($productIds, [], false,[],11,'requisition');
            $this->set('products', $products);
            foreach ($requisition['RequisitionItem'] as $itemIndex => $item) {

                if (!empty($item['product_id'])) {
                    if(isset($products[$item['product_id']])) {
                        $product[0] = $products[$item['product_id']];
                        $requisition['RequisitionItem'][$itemIndex] = $this->setRequisitionItemsData($product , $manufacturingOrder['ManufacturingOrderMaterial'], $item );
                    }
                }
            }
                      
            $type = Requisition::TYPE_INBOUND;
            App::import('Vendor', 'AutoNumber');
            $requisition['Requisition']['number'] = $this->data['Requisition']['hidden_number'] = \AutoNumber::get_auto_serial(\AutoNumber::mapRequisitionTypeToAutoNumberType($type));
            $requisition['Requisition']['order_type'] = $orderType;
            $requisition['Requisition']['order_number'] = $manufacturingOrder["ManufacturingOrder"]["code"];
            $requisition['Requisition']['journal_account_id'] = $manufacturingOrder["ManufacturingOrder"]["journal_account_id"];
            $requisition['Requisition']['order_id'] = $id;
            $this->set('is_manufacturing_order', true);

            $this->_settings($orderType,$type);
            $this->data = $requisition;
            
            $this->render('/requisitions/owner_add');
            return;
        }
        $message = __('All material quantities have already been refunded', true);
        $this->flashMessage($message);
        $this->redirect($this->referer());
    }

    private function _settings($orderType,$type) {
        $this->loadModel('ItemPermission');
        $stores_list = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING) ;
        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList());
        $aps = '1';
        $this->loadModel('RequisitionItem');
        $this->loadModel('Document');
        $this->set('aps', $aps);
        $primary = $this->Store->getPrimaryStore();
        $this->set('primaryStore', $primary);
        $this->set('title_for_layout',  __('Add Requisition', true));
        if (ifPluginActive(ExpensesPlugin)) {
            $this->loadModel('Treasury');
            $this->set('treasuries', $this->Treasury->get_list());

            $this->set('primary_treasury_id', $this->Treasury->get_primary());
        }

        $enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
        if ($enable_multi_units) {
            $this->loadModel('UnitTemplate');
            $unit_templates = $this->UnitTemplate->find('list', ['fields' => 'template_name']);
            $this->set('unit_templates', $unit_templates);
        }
        $this->set('enable_multi_units', $enable_multi_units);
        $this->set('itemModel', 'RequisitionItem');
        $this->set('primary_store', $this->Store->getPrimaryStore());
        $this->set('stores_list', $stores_list);
        $show_price = false;
        $edit_price = false;
        if (in_array($orderType, [Requisition::TYPE_INBOUND, Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND])) {
            if (check_permission(EDIT_STOCK_TRANSACTION_PRICE)) {
                $edit_price = true;
            }
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        } else if (in_array($orderType , [Requisition::TYPE_OUTBOUND,Requisition::TYPE_NOEFFECT]) ) {
            /*
            if (check_permission(EDIT_STOCK_TRANSACTION_PRICE) && $orderType === Requisition::TYPE_OUTBOUND) {
                $edit_price = true;
            }
            */
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        }
        $this->set('order_type', $orderType);
        $this->set('type', $type);
        $this->set('show_price', $show_price);
        $this->set('edit_price', $edit_price);
        //Loading in the controller because it should be loaded once and not for each record
        $this->set('currencyFraction' , CurrencyHelper::getFraction(getCurrentSite('currency_code')));

    }

	function owner_finish($order_id){
        if(!$this->canEditOrDeleteOrder($order_id)){
            $this->flashMessage(__('You are not allowed to perform this action', TRUE));
            return $this->redirect('/v2/owner/entity/manufacturing_order/'.$order_id.'/show');
        }
		$session = new CakeSession;

		$order = $this->ManufacturingOrder->find(['ManufacturingOrder.id' => $order_id ], [], null, 2);

        if($order['ManufacturingOrder']['status'] != ManufacturingOrderStatusUtil::IN_PROGRESS){
            $this->flashMessage(__('The manufacturing order must be in progress in order to finish it.', true));
            return $this->redirect($this->referer('/'));
        }
        $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY);
        $oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY, $order_id, 1)->toArray();

		$requisitionsData = ManufacturingOrderHelper::formatFinishOrderRequisitionsData($order, $this->params['form']);		

		$ClosedPeriod = GetObjectOrLoadModel('ClosedPeriod');
		$validationResult = FinishValidator::validate($ClosedPeriod, $order, $requisitionsData, $this->params['form']);
		if(!$validationResult) {
			$errors = $session->check('Message.CustomError')?$session->read('Message.CustomError')['message']:[];
			$_SESSION['err_messages'] = $errors;
            $session->write('finishOldData', $this->params['form']);
			$this->redirect($this->referer('/'));
		}

        $this->startTransaction();        
        try {
            // TODO : make the transaction work with depreciation & requisitions (currently on error it is not rolling back)
            ManufacturingOrderHelper::depreciateOrderOperationsAssets($order, $this->params['form']['delivery_date']?? null);

            // insert $requisitionsData into 'requisitions' table
            /** @var Requisition $Requisition */
            $Requisition = GetObjectOrLoadModel('Requisition');
            foreach ($requisitionsData as $key => $requisition) {
                $requisition = $Requisition->addRequisition($requisition);
                if (!$requisition['status']) {
                    $_SESSION['err_messages'] = ['requisitions' => [__('Failed to add requisition', true)]];
                    throw new Exception(__('Failed to add requisition', true));
                }
                $requisitionsCreated = new RequisitionsCreated($requisition['data']);
                $requisitionsCreated->run();    
            }
    
            $order['ManufacturingOrder']['status'] = 'finished';
            $order['ManufacturingOrder']['delivery_date'] = $this->params['form']['delivery_date']?? null;
            $this->ManufacturingOrder->save($order);
            $this->commitTransaction();

            $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
            $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY, $order_id, 1)->toArray();
            $requests = $activityLogRequestCreator->create($st, $newData, $oldData);
            $activityLogService =  new \App\Services\ActivityLogService();
            foreach ($requests as $requestObj) {
                $activityLogService->addActivity($requestObj);
            }
            $this->flashMessage(__('Manufacturing Order Is Finished Successfully', true), 'success');
            return $this->redirect($this->referer('/'));
        } catch (\Throwable $th) {
            $this->rollbackTransaction();
            $session->write('finishOldData', $this->params['form']);
            $this->flashMessage(__('Failed To Finish Manufacturing Order', true), 'danger');
            return $this->redirect($this->referer('/'));
        }
	}

    private function canEditOrDeleteOrder($orderId){
        if(!check_permission(EDIT_DELETE_ALL_MANUFACTURING_ORDERS)){
            /** @var \Izam\ManufacturingOrder\Services\ManufacturingOrderService */
            $manufacturingOrdersService = resolve(\Izam\ManufacturingOrder\Services\ManufacturingOrderService::class);
            $authUserOrderIds = $manufacturingOrdersService->getStaffOrderIds(getAuthOwner('staff_id'));
            if(!check_permission(EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS) || !in_array( $orderId ,$authUserOrderIds)){
                return false;
            }
        }
        return true;
    }

	function owner_undo_completion($order_id){
        if(!$this->canEditOrDeleteOrder($order_id)){
            $this->flashMessage(__('You are not allowed to perform this action', TRUE));
            return $this->redirect('/v2/owner/entity/manufacturing_order/'.$order_id.'/show');
        }

		$session = new CakeSession;

		$order = $this->ManufacturingOrder->find(['ManufacturingOrder.id' => $order_id ]);

        $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY);
        $oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY, $order_id, 1)->toArray();

		$Requisition = GetObjectOrLoadModel('Requisition');
		$requisitions = ManufacturingOrderHelper::getOrderRequisitions($Requisition, $order);

		$ClosedPeriod = GetObjectOrLoadModel('ClosedPeriod');
		$validationResult = FinishValidator::validateUndoCompletion($this->StockValidation, $ClosedPeriod, $requisitions, $order);
		if(!$validationResult) {
			$errors = $session->check('Message.CustomError')?$session->read('Message.CustomError')['message']:[];
            $errors = flattenArray($errors);
            $fallbackMsg = __('The given data was invalid.', true);
            $validationMessage = !empty($errors) && !empty($errors[0]) ? $errors[0] : $fallbackMsg;
            $this->flashMessage($validationMessage, 'danger');
			return $this->redirect($this->referer('/'));
		}

        $this->startTransaction();        
        try {
            ManufacturingOrderHelper::undoDepreciationOrderOperationsAssets($order);

            if(!ManufacturingOrderHelper::deleteOrderRequisitions($Requisition, $requisitions)) {
                throw new Exception(__('Failed To Undo Completion Of Manufacturing Order', true));
            }

            foreach($requisitions as $requisition) {
                $event = new RequisitionsUpdated($requisition);
                $event->run();
            }
            
            $order['ManufacturingOrder']['delivery_date'] = null;
            $order['ManufacturingOrder']['status'] = 'in_progress';
            $this->ManufacturingOrder->save($order);
            $this->commitTransaction();

            $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
            $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY, $order_id, 1)->toArray();
            $requests = $activityLogRequestCreator->create($st, $newData, $oldData);
            $activityLogService =  new \App\Services\ActivityLogService();
            foreach ($requests as $requestObj) {
                $activityLogService->addActivity($requestObj);
            }
            $this->flashMessage(__('Manufacturing Order has been changed to be in-progress', true), 'success');
            return $this->redirect($this->referer('/'));
        } catch (\Throwable $th) {
            $this->rollbackTransaction();
            $this->flashMessage(__('Failed To Undo Completion Of Manufacturing Order', true), 'danger');
            return $this->redirect($this->referer('/'));
        }

	}

    public function owner_convert_to_requisition($id)
    {
        $this->loadModel('ManufacturingOrder');
        $this->loadModel('Requisition');
        $this->loadModel('Store');
        $this->loadModel('Product');
        $this->loadModel('ManufacturingOrderMaterial');
        $allowsExceeding =settings::getValue(MANUFACTURING_PLUGIN, 'exceeding_the_requested_quantity_in_manufacturing_order', null, false);
        $this->set('allowsExceeding', $allowsExceeding);

        //handle case if you want to transfer to req from a suspended store.
        $manufactureOrder = $this->ManufacturingOrder->find('first', ['conditions' => ['ManufacturingOrder.id' => $id ] ] );
        if (!check_permission(PermissionUtilAlias::REQUISITION_ADD) || !isset($manufactureOrder) || $manufactureOrder["ManufacturingOrder"]["status"] == ManufacturingOrderStatusUtil::DRAFT ) {
            $this->flashMessage(__('You are not allowed to perform this action', TRUE));
            return $this->redirect('/v2/owner/entity/manufacturing_order/'.$id.'/show');
        }
        $outbound = Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL;
        $requisitionData = $this->Requisition->compare_requisitions($manufactureOrder, $outbound, false , false );
        if ($requisitionData && $requisitionData !== true){
            //here we are using "" as there is no store id in manufacture order material
            $requisitionDataItems = $requisitionData[""]["RequisitionItem"];
            $productIds = [];
            foreach ($requisitionDataItems as $value){
                $productIds[] = $value['product_id'];
            }
            $this->Product->recursive = -1;
            $this->Product->disableBranchFind();
            $products = $this->Product->getInvoiceProductList($productIds, [], false,[],11,'requisition');
            $this->set('products', $products);
            foreach ($requisitionDataItems as $itemIndex => $item) {
                if (!empty($item['product_id'])) {
                    if(isset($products[$item['product_id']])) {
                        $product[0] = $products[$item['product_id']];
                        $requisition['RequisitionItem'][$itemIndex] = $this->setRequisitionItemsData($product , $manufactureOrder['ManufacturingOrderMaterial'], $item );
                    }
                }
            }

            $this->data =$this->setRequisitionDefaultData($manufactureOrder,$requisition);
            $this->render('/requisitions/owner_add');
            return;
        }
        if (is_bool($requisitionData) && !$allowsExceeding){
            $message = __('All product quantities have already been converted', true);
            $this->flashMessage($message);
            $this->redirect($this->referer());
        }
        $this->data = $this->setRequisitionDefaultData($manufactureOrder);
        $this->render('/requisitions/owner_add');
        return ;

    }

    private function outbound_settings($orderType,$type) {
        $this->loadModel('ItemPermission');
        $stores_list = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING) ;
        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList());
        $aps = '1';
        $this->loadModel('RequisitionItem');
        $this->loadModel('Document');
        $this->set('aps', $aps);
        $primary = $this->Store->getPrimaryStore();
        $this->set('primaryStore', $primary);
        $this->set('title_for_layout',  __('Add Requisition', true));
        if (ifPluginActive(ExpensesPlugin)) {
            $this->loadModel('Treasury');
            $this->set('treasuries', $this->Treasury->get_list());

            $this->set('primary_treasury_id', $this->Treasury->get_primary());
        }

        $enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
        if ($enable_multi_units) {
            $this->loadModel('UnitTemplate');
            $unit_templates = $this->UnitTemplate->find('list', ['fields' => 'template_name']);
            $this->set('unit_templates', $unit_templates);
        }
        $this->set('enable_multi_units', $enable_multi_units);
        $this->set('itemModel', 'RequisitionItem');
        $this->set('primary_store', $this->Store->getPrimaryStore());
        $this->set('stores_list', $stores_list);
        $this->set('order_type', $orderType);
        $this->set('type', $type);
        $this->set('show_price', true);
        $this->set('edit_price', false);
        $this->set('stockOperation', true);
        $this->set('is_manufacturing_order', true);
        //Loading in the controller because it should be loaded once and not for each record
        $this->set('currencyFraction' , CurrencyHelper::getFraction(getCurrentSite('currency_code')));

    }

    private function setRequisitionDefaultData($manufactureOrder,$requisition =null)
    {
        $orderType = Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL;
        $type = Requisition::TYPE_OUTBOUND;
        App::import('Vendor', 'AutoNumber');
        $requisition['Requisition']['number'] = $this->data['Requisition']['hidden_number'] = \AutoNumber::get_auto_serial(\AutoNumber::mapRequisitionTypeToAutoNumberType($type));
        $requisition['Requisition']['order_number']=$manufactureOrder['ManufacturingOrder']['code'];
        $requisition['Requisition']['journal_account_id'] = $manufactureOrder["ManufacturingOrder"]["journal_account_id"];
        $this->outbound_settings($orderType,$type);
        $this->set('mo_id', $manufactureOrder['ManufacturingOrder']['id']);
        $requisition['Requisition']['order_id']=$manufactureOrder['ManufacturingOrder']['id'];
        $this->data['order_id']=$manufactureOrder['ManufacturingOrder']['id'];
        return $requisition;
    }

    public function owner_view($id)
    {
        return $this->redirect('/v2/owner/entity/manufacturing_order/'.$id .'/show');
    }

    private function setRequisitionItemsData($product, $itemsSource , $item )
    {
        foreach ($itemsSource as $source){
            if ($source['product_id'] == $item['product_id']){
                $productSourceData = $source;
            }
        }
        return array_merge($item , [
            'Product'=>$product[0],
            'unit_price'=>$product[0]['average_price'],
            'item'=>$product[0]['name'],
            'unit_name'=>$productSourceData['unit_name'] ?? 'kg',
            'unit_small_name'=> $productSourceData['unit_small_name'] ?? null,
            'unit_factor'=> $productSourceData['factor'] ?? null,
            'unit_factor_id'=>$productSourceData['unit_factor_id'] ?? null,
        ]);
    }

    public function api_update_manufacturing_orders_journal($id)
    {
        $this->loadModel('ManufacturingOrder');
        $manufacturingOrder = $this->ManufacturingOrder->find('first', ['conditions' => ['ManufacturingOrder.id' => $id]]);
        if(!$manufacturingOrder) {
            $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Manufacturing Order', true))));
        }
        $this->ManufacturingOrder->update_journals($manufacturingOrder);
        return die(json_encode(['status' => 200, 'message' => 'journal updated successfully']));
    }
    public function api_update_order_inbound_requisition($id)
    {
        $this->loadModel('ManufacturingOrder');
        $this->loadModel('RequisitionItem');
        $this->loadModel('Requisition');
        $conditions = [
            'Requisition.order_id' => $id,
            'Requisition.order_type' => Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,
        ];
        $requisition = $this->Requisition->find('first', ['applyBranchFind' => false, 'conditions' =>$conditions ]);
        if (isset($requisition["RequisitionItem"])){
            $requisition["RequisitionItem"][0]["unit_price"]=$this->data["RequisitionItem"]["newAmount"];
            $this->Requisition->updateRequisition($requisition);
            return die(json_encode(['status' => 200, 'message' => 'journal updated successfully']));
        }
    }

}

