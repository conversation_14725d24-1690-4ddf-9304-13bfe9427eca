<?php

use App\Services\SalesInvoicesReferrals\SalesInvoicesReferral;

class SalesInvoicesReferralController extends AppController
{
    /**
     * This route is handling the importing a client invoice
     * into a new account as a purchase invoice & versa-vise.
     * @see https://izam.atlassian.net/browse/DAFTRA-58317
     * @see https://izam.atlassian.net/browse/DAFTRA-58339
     */
    public function update_target_site_id()
    {
        $hashString = $this->params['url']['sip_hash'];
        $invoicesReferral = new SalesInvoicesReferral;
        $siteId = getCurrentSite('id');
        if (!$siteId) {
            return $this->cakeError('error403');
        }

        $hashInfo = $invoicesReferral->decodeSIPHash($hashString);
        if (!$hashInfo) {
            return $this->cakeError('error403');
        }

        $model = $invoicesReferral->findByTrackingHashString($hashString);

        if (!$model) {
            return $this->cakeError('error403');
        }

        /*if ($model["target_site_id"] == $siteId) {
            return $this->cakeError('error403');
        }*/

        if (!$model["target_site_id"]) {
            $invoicesReferral->updateTargetSiteByHashString(
                hashString: $hashString,
                targetSiteId: $siteId,
            );
        }

        if (getAuthOwner()) {
            $this->redirect('/');
            exit;
        }

        $this->redirect('https://www.' . Domain_Short_Name . '/sites/login_to/' . $siteId);
        exit;
    }

    /**
     * This route is handling the importing a client invoice
     * into a new account as a purchase invoice & versa-vise.
     * @see https://izam.atlassian.net/browse/DAFTRA-58317
     * @see https://izam.atlassian.net/browse/DAFTRA-58339
     */
    public function import()
    {
        $hashString = $this->params['url']['sip_hash'];
        $invoicesReferral = new SalesInvoicesReferral;
        $siteId = getCurrentSite('id');
        $languageCodeId = getCurrentSite('language_code');

        // $this->loadModel('SalesInvoicesReferral');
        if (!$siteId) {
            return $this->cakeError('error403');
        }

        if (!$hashInfo = $invoicesReferral->decodeSIPHash($hashString)) {
            return $this->cakeError('error403');
        }

        if (getAuth('CLIENT', 'language_code')) {
            $languageCodeId = getAuth('CLIENT', 'language_code');
        }

        $languageCode = 'ar';
        if ($languageCodeId == '41') {
            $languageCode = 'en';
        }

        // Validate that the hash is holding the current site id
        if ($hashInfo['site_id'] != $siteId) {
            return $this->cakeError('error403');
        }

        $authId = null;
        if ($hashInfo['model'] == 'PurchaseOrder') {
            $authId = getAuth('SUPPLIER', 'id');
        } else if ($hashInfo['model'] == 'Invoice') {
            $authId = getAuth('CLIENT', 'id');
        }

        $model = $invoicesReferral->findByTrackingHashString($hashString);

        $redirectTo = 'register';
        if ($invoicesReferral->checkForExistsUsers($authId, $hashInfo['model'], $siteId)) {
            $redirectTo = 'dashboard';
        }

        $this->loadModel($hashInfo['model']);

        $this->{$hashInfo['model']}->Behaviors->attach('Containable');
        $invoice = $this->{$hashInfo['model']}->find('first', [
            'conditions' => [
                $hashInfo['model'] . '.id' => $hashInfo['invoice_id']
            ],
            'contain' => [
                'PurchaseOrder',
                'Supplier',
                'Staff',
                'Site',
                'Client',
                'InvoiceItem' => [
                    'Product'
                ],
                'InvoiceCustomField',
                'InvoicePayment',
                'InvoiceTax',
                'PurchaseOrderItem' => [
                    'Product'
                ],
                'PurchaseOrderCustomField',
                'PurchaseOrderTax',
                'PurchaseOrderPayment'
            ]
        ]);

        if (!$invoice) {
            return $this->cakeError('error403');
        }

        $queryString = http_build_query([
            'sip_hash' => $hashString,
            'email' => $invoice['Client']['email'],
            'business_name' => $invoice['Client']['business_name'],
            'phone' => $invoice['Client']['phone1'],
        ]);

        $redirectUrl = sprintf(
            'https://www.%s/%s/%s?%s',
            Domain_Short_Name,
            $languageCode,
            $redirectTo,
            $queryString
        );

        $trkValue = '';
        if ($hashInfo['model'] == 'Invoice') {
            $data = [
                'status' => 'pending',
                'metainfo' => json_encode([
                    'tracking_code' => $hashString,
                ]),
                'invoice' => json_encode([
                    'Site' => $invoice['Site'],
                    'Client' => $invoice['Client'],
                    'Invoice' => $invoice['Invoice'],
                    'InvoiceTax' => $invoice['InvoiceTax'],
                    'InvoiceItem' => $invoice['InvoiceItem'],
                    'InvoicePayment' => $invoice['InvoicePayment'],
                    'InvoiceDocument' => $invoice['InvoiceDocument'],
                    'InvoiceCustomField' => $invoice['InvoiceCustomField'],
                ]),
                'source_site_id' => $hashInfo['site_id'],
                'model' => $hashInfo['model']
            ];
            $trkValue = 'Convert Sales Invoice To Purchase Invoice';
        } elseif ($hashInfo['model'] = 'PurchaseOrder') {
            $data = [
                'status' => 'pending',
                'metainfo' => json_encode([
                    'tracking_code' => $hashString,
                ]),
                'invoice' => json_encode([
                    'Site' => $invoice['Site'],
                    'Client' => $invoice['Supplier'],
                    'Invoice' => $invoice['PurchaseOrder'],
                    'InvoiceTax' => $invoice['PurchaseOrderTax'],
                    'InvoiceItem' => $invoice['PurchaseOrderItem'],
                    'InvoicePayment' => $invoice['PurchaseOrderPayment'],
                    'InvoiceDocument' => $invoice['PurchaseOrderDocument'],
                    'InvoiceCustomField' => $invoice['PurchaseOrderCustomField'],
                ]),
                'source_site_id' => $hashInfo['site_id'],
                'model' => $hashInfo['model']
            ];
            $trkValue = 'Convert Purchase Invoice To Sales Invoice';
        }

        $this->Cookie->domain = Domain_Short_Name;
        $this->Cookie->write('sip_hash', $hashString, false, 3.154e+7);
        $trk = $this->Cookie->read('trk');
        if (
            !$trk || (is_string($trk) && (
                strpos($trk, 'Convert Purchase Invoice') === false ||
                strpos($trk, 'Convert Sales Invoice') === false
            ))
        ) {
            $trk = $trkValue;
        }
        $this->Cookie->write('trk', $trk, false, 3.154e+7);

        if (!$model) {
            $invoicesReferral->insertNewReferral($data);
        }

        $this->redirect($redirectUrl);
        exit;
    }
}
