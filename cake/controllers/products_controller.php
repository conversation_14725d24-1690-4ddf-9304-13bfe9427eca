<?php

/**
 * @property Product $Product ProductModel
 * @property ItemsCategory $ItemsCategory
 *
 */

use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil as EntityEntityKeyTypesUtil;
use App\Transformers\ServiceModelDataTransformer;
use App\Utils\TrackStockUtil;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\AppEntitiesFormErrorHandlerDecorator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Limitation\Utils\LimitationUtil;
use Izam\Daftra\Product\Services\ProductService;
use App\Services\Report\ReportFactory;
use App\Services\Report\ReportUtil;
use App\Utils\ReportLimitTrait;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Aws\Aws;
use Izam\Daftra\ActivityLog\DetailsEntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Queue\EventListenerMapper;
use Izam\Daftra\Common\Queue\EventTypeUtil;
use Izam\Daftra\Common\Utils\StockTransactionUtil;
use App\vendors\Services\InvoiceItemService;
use Izam\Attachment\Models\EntityAttachment;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Aws\S3UploadHandler;

App::import('Vendor', 'settings');
App::import('Vendor', 'OIBarcode', array('file' => 'OIBarcode/OIBarcode.php'));
class ProductsController extends AppController {
    use ReportLimitTrait;
    var $name = 'Products';
    var $helpers = array('Html', 'Form');

    public function __construct() {
         parent::__construct();
        $this->loadModel ( 'Product');
        $this->loadModel ( 'Store');
    }
    function owner_ajax_get_balance($product_id , $store_id = null ){
        $product = $this->Product->find('first' , ['recursive' => -1 , 'conditions' => ['id' => $product_id] ]);
        if ( empty($product)){
            die("");
        }else {
            if ( empty($store_id)){
                die( $product['Product']['stock_balance'] );
            }else {
                $this->loadModel('StoreStockBalance');
                $store_balance = $this->StoreStockBalance->find('first' ,['recursive'=>-1,'conditions' => ['product_id' => $product_id,'store_id'=>$store_id]] );
                die($store_balance['StoreStockBalance']['balance']);
            }
        }
    }

    function owner_import()
    {
        if (!check_permission( [PermissionUtil::View_All_Products, PermissionUtil::View_his_own_Products])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $sampleFile = ['name' => 'products-sample.csv', 'path' => '/samples/products-sample.csv'];
        $this->set('sample_file', $sampleFile);
        $this->set('_PageBreadCrumbs', [
            ['title' => __("Products", true), 'link' => '/owner/products'],
            ['title' => __("Import", true), 'link' => '#'],
        ]);
        return parent::owner_import(); // TODO: Change the autogenerated stub
    }

    function owner_index() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(View_All_Products) && !check_permission(View_his_own_Products)) {
				if(IS_REST) $this->cakeError('error403');
				$this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        $this->Product->recursive = 1;
        $conditions = $this->_filter_params();
        $this->loadModel('ItemsCategory');

        unset($conditions['Product.category LIKE']);//OLD category
        if(isset($conditions['Product.category'])) {
            unset($conditions['Product.category']);
          }
        if ( !empty ( $this->params['url']['category']))
        {

            $this->loadModel('ItemsCategory');
            $item_ids = array_values ($this->ItemsCategory->find('list',['fields' => 'item_id' , 'conditions'=>['category_id' => $this->params['url']['category'],
                'ItemsCategory.item_type' => Category::CATEGORY_TYPE_PRODUCT ] ]) );

            $conditions['Product.id'] = $item_ids;
        }

        $this->loadModel('ProductAttributeOption');
        if (isset($this->params['url']['multiple-attributes']) && !empty($this->params['url']['multiple-attributes']) && count($this->params['url']['multiple-attributes']) ){
            unset($conditions['Product.multiple-attributes']);
           $multipleAttributes = $this->params['url']['multiple-attributes'];
           $mSelectedAttributes = [];
           foreach ($multipleAttributes as $index =>$singleAttribute){
               $details = explode(" : ", $singleAttribute );
               $mSelectedAttributes[$index]['attribute'] = $details[0];
               $mSelectedAttributes[$index]['option']  = $details[1];
           }
            $ids = $this->generateMultipleAttributeCondition($mSelectedAttributes);
            $conditions['Product.id'] = $ids;
        }
        if (isset($this->params['url']['attributes']) && count($this->params['url']['attributes']))
        {
            $selectedAttribute = $this->params['url']['attributes'];
            $checkAttributeAvailable = array_filter($selectedAttribute, function ($attribute) {
                return !empty($attribute['option']);
            });
            if (!empty($checkAttributeAvailable)) {
                unset($conditions['Product.attributes']);
                $item_ids =$this->generateMultipleAttributeCondition($selectedAttribute);
                $conditions['Product.id'] = $item_ids;
            }
        }

        if (isset($conditions['Product.keywords LIKE'])) {
            $keyword = $conditions['Product.keywords LIKE'];
            $keyword = str_replace(['\\'],['\\\\\\'],$keyword);

            unset($conditions['Product.keywords LIKE']);
			$keywords = explode(' ',$keyword);
			debug($keywords);
			if(count($keywords) == 1){
            $conditions['OR']['Product.name like '] = $keywords[0];
			}
			else{

				foreach($keywords as $k => $v){
				$v = str_replace('%', '', $v);
				$v = '%'.$v.'%';
				$conditions['OR']['AND'][]['Product.name like '] = $v;
				}
			}

            $conditions['OR']['Product.product_code like '] = $keyword;
            $idx=str_replace('%', '', $keyword);
            if(is_int($idx)) {
                $conditions['OR']['Product.id'] = $idx;
            }
            $conditions['OR']['Product.barcode like '] = $keyword;
            if (!empty($this->params['url']['barcode'])) {
             $conditions['OR']['ProductItemBarcode.barcode like '] = $keyword;
            }
            $conditions['OR']['Product.tags like '] = $keyword;
        }

        if (isset($conditions['Product.barcode LIKE'])) {
            $conditions['OR']['ProductItemBarcode.barcode like '] = $conditions['Product.barcode LIKE'];
            unset($conditions['Product.barcode LIKE']);
            $conditions['OR']['Product.barcode like '] = $conditions['OR']['ProductItemBarcode.barcode like '];
        }
        $has_stock_condition=(!empty($conditions['Product.status'])&&$conditions['Product.status'] != 4);
        $havingConditions = "";
        if (isset($conditions['Product.status'])) {
            
            if ($conditions['Product.status'] == 1){
                $havingConditions = " HAVING (Product.status IS NULL OR Product.status = " . ProductStatusUtil::STATUS_ACTIVE . ") AND ((Product.track_stock IS NULL OR Product.track_stock = 0) OR (COALESCE(sum(StockTransaction.quantity),0) > 0 ) ) ";
            }else if ($conditions['Product.status'] == 2){
                $havingConditions = " HAVING ( (Product.status IS NULL OR Product.status != " . ProductStatusUtil::STATUS_INACTIVE . ") AND  Product.track_stock = 1 AND  Product.track_stock  IS NOT NULL  AND  ( COALESCE(sum(StockTransaction.quantity),0) IS NULL OR COALESCE(sum(StockTransaction.quantity),0) <= 0 OR (COALESCE(sum(StockTransaction.quantity),0) <= Product.low_stock_thershold AND  Product.low_stock_thershold IS NOT NULL )  ) AND COALESCE(sum(StockTransaction.quantity),0) > 0 ) ";
            }else if ($conditions['Product.status'] == 3){
                $havingConditions = " HAVING  ( (Product.status IS NULL OR Product.status = " . ProductStatusUtil::STATUS_ACTIVE . ") AND  Product.track_stock = 1 AND  Product.track_stock  IS NOT NULL  AND  ( COALESCE(sum(StockTransaction.quantity),0) IS NOT NULL AND COALESCE(sum(StockTransaction.quantity),0) <= 0)  )";
            }else if ($conditions['Product.status'] == 4){
                $conditions[] = '(Product.status = ' . ProductStatusUtil::STATUS_INACTIVE . '  AND Product.status  IS NOT NULL )';
            }else if ($conditions['Product.status'] == 5){
                $conditions[] = '(Product.status = ' . ProductStatusUtil::STATUS_SUSPENDED . ') ';
            }elseif (is_array($conditions['Product.status']) 
                    && in_array(2, $conditions['Product.status']) 
                    && in_array(3, $conditions['Product.status'])) {

                $conditions[] = "(Product.status IS NULL OR Product.status = 0) 
                    AND Product.track_stock = 1 
                    AND Product.track_stock IS NOT NULL  
                    AND (Product.stock_balance IS NULL 
                        OR Product.stock_balance <= 0 
                        OR (Product.stock_balance <= Product.low_stock_thershold 
                            AND Product.low_stock_thershold IS NOT NULL )  
                    )";
            }

            
            unset($conditions['Product.status']);
        }

        if ($site['staff_id'] != 0) {
            if (!check_permission(View_All_Products) && check_permission(View_his_own_Products)) {
                $conditions['Product.staff_id'] = $site['staff_id'];
            }
        }
        /*$this->paginate['Product'] = array('conditions' => $conditions,
            'order' => array('Product.name asc'),
        );*/

//		$this->paginate['order'] =  array('Product.name asc'); removed becauses it makes the navigation dosent work
        $this->paginate['Product']['order'] =  'Product.name asc';
        $this->set_service_product_settings();
        if(isset($_GET['type']))
        {
            if($_GET['type']==Product::PRODUCT_TYPE){
                $conditions[] = '(Product.type = 0 or Product.type = 1 or Product.type is null)';
            }else {
                $conditions['Product.type'] = $_GET['type'];
            }
        }
        $this->loadModel('ItemPermission');
        $stores_list =  $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW);
        $this->set('stores_list',$stores_list);

        if ($this->Product->hasCustomForm()) {
            $this->paginate['Product']['fields'][] = 'CustomModel.*';
        }

        $this->paginate['Product']['group'] = ["Product.id $havingConditions"];

        if (!empty($this->params['url']['barcode'])) {
            $this->paginate['Product']['joins'][] = [
                'table' => 'product_item_barcode',
                'alias' => 'ProductItemBarcode',
                'type' => 'LEFT',
                'applyBranchFind' => true,
                'conditions' => [
                    'Product.id = ProductItemBarcode.product_id'
                ]
            ];
        }

        $stock_join=[ 
            'table' => 'stock_transactions',
            'alias' => 'StockTransaction',
            'type' => 'LEFT',
            'applyBranchFind' => false,
            'conditions' => [
                'Product.id = StockTransaction.product_id',
                'StockTransaction.status = 4',
                'StockTransaction.store_id' => array_keys($stores_list),
                '(StockTransaction.ignored = 0 or StockTransaction.ignored is null)',
            ]
        ];

        $stock_field='COALESCE(sum(StockTransaction.quantity),0) as product_store_balance';

        $this->paginate['Product']['fields'] = ['Product.*', 'UnitTemplate.*', 'Supplier.*'];

        if($has_stock_condition)
        {
            $this->paginate['Product']['joins'][] = $stock_join;
            $this->paginate['Product']['fields'][]=$stock_field;
        }
        $tmp=$this->Product->hasMany;
        $this->Product->hasMany=[];

		// This Forces the load of ProductImages with Paginate when the GET param "with_images" is set
		if (isset($_GET['with_images']) || IS_REST) {
		    $this->Product->hasMany['ProductImage'] = ['order' => 'ProductImage.default desc'];
	    }
      
		$custom_field_filter_check = function($key) { return strpos($key, 'field') === 0; };
		if (count(array_filter(array_keys($_GET), $custom_field_filter_check)) <= 0) {
		    $this->Product->Behaviors->detach('customform');
	    }

        $this->Product->bindAttachmentRelation('product');
        if (!empty($_GET['load_custom_data'])) {
            $this->bindCustomDataRelation();
        }
        $products = $this->paginate($conditions);
        $paging=$this->params['paging'];
        
        $fixedConditions = $this->paginate['Product'];
        if(!empty($conditions)) {
            $fixedConditions['conditions']=$conditions;
        }
        $this->setConditionsKey('products', $fixedConditions);
        $this->set('export_conditions_keys_group_mode', true);

        $this->setup_nav_data($products);
        $products_ids = Set::extract('{n}.Product.id',$products);

        //Get the stock count for only found products (Not All products) if it has not stock count coditions
        if(true||!$has_stock_condition)
        {
            $this->paginate['Product']['joins'][] = $stock_join;
            $this->paginate['Product']['fields'][]=$stock_field;
            $conditions['Product.id']=$products_ids;
            $default_recursive = $this->Product->recursive;
            if(getCurrentSite('id') == "2167871") {
                $this->paginate['Product']['fields'] = ['Product.*', $stock_field];
                $this->Product->recursive = -1;
            }   
            $products_with_stock = $this->paginate($conditions);
            $this->Product->recursive = $default_recursive;
            //map product with stock with the value of Product id
            $temp = [];
            foreach ($products_with_stock as $item) {
                $temp[$item['Product']['id']] = $item;
            }
            $products_with_stock = $temp;
            $this->params['paging']=$paging;
            foreach($products as $i=>$p)
            {
                $productWithStock = $products_with_stock[$p['Product']['id']]??null;
                // warning suppress
                if(isset($productWithStock[0]['product_store_balance']))
                    $products[$i]['Product']['product_store_balance']=$productWithStock[0]['product_store_balance'];
                else if(isset($productWithStock['Product']['product_store_balance']))
                    $products[$i]['Product']['product_store_balance']=$productWithStock['Product']['product_store_balance'];
                else
                    $products[$i]['Product']['product_store_balance']=0;
            }
        }
    //    print_pre($products_with_stock);
      //  print_pre($products);
     //   die();


        //binding categories,storestockbalance model to the product
        $this->Product->bindModel(
            array('hasAndBelongsToMany' =>
                [ 'Category' =>
                    [
                        'className' => 'Category',
                        'joinTable' => 'items_categories',
                        'foreignKey' => 'item_id',
                        'associationForeignKey' => 'category_id',
                        'conditions' => [ 'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT, 'category_type' => Category::CATEGORY_TYPE_PRODUCT]
                    ]
                ]
            )
        );


        $this->Product->hasMany=$tmp;

        if (IS_REST) {
            $this->Product->bindModel(
                array('hasAndBelongsToMany' =>
                    [ 'Category' =>
                        [
                            'className' => 'Category',
                            'joinTable' => 'items_categories',
                            'foreignKey' => 'item_id',
                            'associationForeignKey' => 'category_id',
                            'conditions' => [ 'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT, 'category_type' => Category::CATEGORY_TYPE_PRODUCT]
                        ]
                    ]
                )
            );
            $this->Product->bindModel(['hasMany' => array('StoreStockBalance' => array('className' =>'StoreStockBalance', 'foreignKey' => 'product_id'))],false);
        }
        $productData = $this->Product->find('all',[ 'conditions' => ['Product.id' => $products_ids]]);
        $product_stocks = Set::combine($productData, '{n}.Product.id', '{n}.StoreStockBalance');
        $product_categories = Set::combine($productData, '{n}.Product.id', '{n}.Category');

        $products_ids = array();
        App::import('vendor','WarehouseService' , ['file' => 'WarehouseService.php']);
        $ws = new WarehouseService();
        $this->loadModel ('ItemPermission');
        foreach($products as $k => $product)
        {
            $products[$k]['Product']['bundle_final_cost'] = $this->Product->get_bundle_final_cost($product['Product']['id']);
            $products[$k]['ProductCategory'] = $product_categories[$product['Product']['id']];
            $products[$k]['ProductStock'] = $this->prepareStockBalance($product_stocks[$product['Product']['id']]);
            if (IS_REST) {
                foreach ($products[$k]['ProductCategory'] as &$productCategory) {
                    if (isset($productCategory['ItemsCategory'])) {
                        unset($productCategory['ItemsCategory']);
                    }
                }
                foreach ($products[$k]['ProductStock'] as &$productStockRecod) {
                    unset($productStockRecod['created']);
                    unset($productStockRecod['modified']);
                }
            }

            //$products[$k]['Product']['stores_balance'] = $products[$k]['Product']['product_store_balance'];
            $products[$k]['Product']['stock_balance'] = $products[$k]['Product']['product_store_balance'];
            $ws = new WarehouseService();
            $productStock = $ws->getStaffProductStock(ItemPermission::PERMISSION_VIEW, $products[$k]["Product"]['id'], getAuthOwner('staff_id'),null,true);
            $showProductPendingQTY = settings::getValue(InventoryPlugin, SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS);
            if ($showProductPendingQTY && IS_REST) {
                $pendingQTY = $this->getProductPendingQuantity($ws, $product['Product']['id']);
                $products[$k]["Product"]["productPendingQTY"] = $pendingQTY;
                $products[$k]["Product"]["productAvailableQTY"] = $this->combineProductStock($productStock, $pendingQTY);
            }

            /** this code  consume a lot of resources and the above store balance joins do it,
             * if the above join (ProductStockBalance) not working correctly just fix it
            // Edited to be exactly the same way we get the stock balance inside product view
            $productStock = $ws->getStaffProductStock(ItemPermission::PERMISSION_VIEW, $products[$k]['Product']['id'], getAuthOwner('staff_id'));
            $products[$k]['Product']['stores_balance'] = array_sum($productStock);
            */
            $products_ids[] = $product['Product']['id'];
        }
        // Adding Product Images
	    $ProductImageModel = GetObjectOrLoadModel('ProductImage');
	    $product_images = $ProductImageModel->find('list', ['recursive' => -1, 'conditions' => ['ProductImage.default' => '1'], 'fields' => ['ProductImage.product_id', 'ProductImage.file']]);
		$current_site = getCurrentSite();
        $this->loadModel('ItemsTag');
        $products_tags = $this->ItemsTag->get_items_tags($products_ids,ItemsTag::TAG_ITEM_TYPE_PRODUCT);

	    foreach ($products as &$product) {
		     // Make s3 images as default.  
             $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product',$product['Product']['id']);
             if(count( $defaultS3Images))
             {
                 $product['ProductMasterImage']['file_full_path']  =  $defaultS3Images[0]->files->path;
             }elseif (isset($product_images[$product['Product']['id']])) {
			    $product['ProductMasterImage']['file_full_path'] = appendFullPath($current_site['id'], 'product-images', $product_images[$product['Product']['id']]);
		    }
                        
            if (isset($products_tags[$product['Product']['id']])) {
                $product['Product']['tags']= implode(',',$products_tags[$product['Product']['id']]) ;
            }
	    }
	    unset($product);
        $counts = $this->Product->getTypeCounts();
        $this->set('counts', $counts);

        $this->set('products', $products);
        $this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_PRODUCT);
	    $this->set ( 'enable_multi_units' , settings::getValue(InventoryPlugin, 'enable_multi_units') );
	    $this->set('products_tags',$products_tags);
        $this->set('categories', $this->Product->getCategoriesList(false));
//        $this->set('brands', $this->Product->getBrandsList(false));

		$this->setup_nav_data($products);
        $this->set('content', $this->get_snippet('products'));
        $this->set ( 'enable_bundles' , settings::getValue(InventoryPlugin, 'enable_bundles') );
        $show_buy_price = ( check_permission(Proudcts_Add_New_Proudct)||check_permission(Edit_Delete_all_Products)||check_permission(Add_New_Purchase_Orders)||check_permission(Edit_Delete_All_Purchase_Orders));
        $this->set ( 'show_buy_price' , $show_buy_price);
        $this->set('title_for_layout',  h(__('Products & Services', true)));
		if(IS_REST){
            $this->addProductImages($products);
			$this->set('rest_items', $products);
			$this->set('rest_model_name', "Product");
			$this->render("index");
		}

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('product');
        if(!empty($printableTemplates))
            $this->set(compact('printableTemplates'));
    }

    private function addProductImages(&$products)
    {
        //i need to get images caz when custom field behavior removed it doesn't return images relations
        $ProductImageModel = GetObjectOrLoadModel('ProductImage');
	    $product_images = $ProductImageModel->find('all', ['recursive' => -1,'order'=>"ProductImage.default desc"]);
        $images_by_product=[];
        foreach($product_images as $image){ //group image by  product id
            $images_by_product[$image['ProductImage']['product_id']][]=$image['ProductImage'];
        }
        foreach ($products as &$product) {
		    if (isset( $images_by_product[$product['Product']['id']])) {
			    $product['ProductImage'] = $images_by_product[$product['Product']['id']];
		    }
            $product = $this->prepareProductDataForApi($product);
	    }
    }
	function api_list_brands() {
		$this->loadModel('Product');
		$brand_filter = false;
		if (isset($_GET['brand']) && !empty($_GET['brand'])) {
			$brand_filter = $_GET['brand'];
		}
		$brands = $this->Product->getFilteredBrandsList($brand_filter);
		$this->layout = false;
		$this->autoRender = false;
		$this->RequestHandler->respondAs('json');
		die(json_encode(['brands' => $brands]));
	}

    function __init_form_data($id=false) {
        $this->set('brands_list', $this->Product->getBrandsList());
//      $this->set('categories_list', $this->Product->getCategoriesList());
        $this->loadModel('Category');
        $list = $this->Category->find('list',['fields'=>'name,name','conditions'=>['Category.category_type' => Category::CATEGORY_TYPE_PRODUCT]]);
        if (!empty($id)) {
        	$product = $this->Product->find('first', ['recursive' => -1, 'conditions' => [
        		'Product.id' => $id
	        ]]);
        }
		debug($list);
        $categories_list = array();

        foreach( $list as $k => $v )
        {
            $categories_list[$k] = ["id"=>$k, "name"=>$v];
        }
        $this->categories_list = $categories_list;
        $this->set('categories_list', $categories_list );

        $this->set('tags_list', $this->Product->getTagsList());
        $this->set('suppliers', $this->Product->Supplier->getSuppliersList());
        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
        $product_sales_routing = settings::getValue(AccountingPlugin,'product_sales_accounts_routing');
        if(!empty($id) && !empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING)
        {
            $this->set('sales_account_id', $this->Product->getSalesAccount($id));
        }
        
        if(!empty($id) && !empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING)
        {
            $this->set('sales_cost_account_id', $this->Product->getSalesCostAccount($id));
        }

        $this->set('product_sales_routing',$product_sales_routing );
        if ( $enable_multi_units ) {
            $this->loadModel('UnitTemplate');
            $unit_templates_list = $this->UnitTemplate->find('list' , ['recursive' => -1  ,'fields' => 'template_name','conditions'=>['UnitTemplate.active'=>1]]);
            $this->set('unit_templates' ,$unit_templates_list );

            $this->loadModel('UnitFactor');
            $factors = $this->UnitFactor->find('all' , ['recursive' => -1]);
            $factors_list = []  ;
            foreach ( $factors as $f ) {
                $factors_list[$f['UnitFactor']['unit_template_id']][$f['UnitFactor']['id']] = $f['UnitFactor'];
            }
            $unit_templates = $this->UnitTemplate->find('all' , ['recursive' => -1 ]);
            foreach ( $unit_templates as  $t ){
                $factors_list[$t['UnitTemplate']['id']][0] = ['id' => 0 , 'factor_name' => $t['UnitTemplate']['main_unit_name'], 'factor' => 1 ];
            }
            $this->set('factors_list' ,$factors_list ) ;
        }
        $this->set('enable_multi_units' ,$enable_multi_units );
        $conditions = ['Product.name is not null'];
        if (!empty($id) && $product['Product']['type'] != PRODUCT::BUNDLE_TYPE){
	        $conditions['Product.id'] = $id;
        }
        $list_p = $this->Product->find("all",['recursive' => -1, 'fields' => 'id,name,unit_template_id,product_code', 'conditions'=> $conditions]);

        $enableMultiUnits = settings::getValue(InventoryPlugin, 'enable_multi_units');
        foreach($list_p as $p)
        {
            if (!$enableMultiUnits && isset($p['Product']['product_code'])) {
                $p['Product']['name'] = $p['Product']['name'] . ' #' . $p['Product']['product_code'];
            }
            $products_list[$p['Product']['id']] = $p['Product'];
        }

        $this->set('tracking_types',$this->Product->getTrackingTypes());

        $this->Product->add_multi_units($products_list) ;

        $this->set( 'products_list', $products_list );
        $this->set ( 'enable_bundles' , settings::getValue(InventoryPlugin, 'enable_bundles') );
        $this->set ( 'advanced_pricing_options' , settings::getValue(InventoryPlugin, 'advanced_pricing_options') );

        $this->set('statusList', ProductStatusUtil::getStatusesList());
        $this->set('bundle_types', Product::getBundleTypes());
        $this->set('default_bundle_type', settings::getValue(InventoryPlugin, 'bundle_type'));

    }

    function saveSalesAccount($id = null)
    {

        if(!empty($id) && isset($this->data['Product']['sales_account_id']))
        {
            $this->loadModel('JournalAccountRoute');
           return  $this->JournalAccountRoute->saveRoutedAccount($data=['JournalAccountRoute'=>[
                'entity_type'=>'product_sales',
                'entity_id'=>$id,
                'account_id'=>$this->data['Product']['sales_account_id'],
                'account_type'=>JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT,
            ]]);
        }
    }

    function saveSalesCostAccount($id = null)
    {

        if(!empty($id) && isset($this->data['Product']['sales_cost_account_id']))
        {
           $this->loadModel('JournalAccountRoute');
           return  $this->JournalAccountRoute->saveRoutedAccount($data=['JournalAccountRoute'=>[
                'entity_type'=>'product_cost_sales',
                'entity_id'=>$id,
                'account_id'=>$this->data['Product']['sales_cost_account_id'],
                'account_type'=>JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT,
            ]]);
        }
    }

    private function createAppEntitiesFormHandlerInstance()
    {
        return new AppEntitiesFormErrorHandlerDecorator(EntityKeyTypesUtil::PRODUCT_ENTITY_KEY);
    }

    function owner_add($id = 1, $clone_id = null) {
        $appEntitiesFormHandler = $this->createAppEntitiesFormHandlerInstance();
        $this->Product->bindAttachmentRelation('product');

        if($id === 1 && settings::getValue(InvoicesPlugin, 'sold_item_type') == Settings::OPTION_SOLD_TYPE_SERVICES) {
            if (IS_REST) {
                return $this->owner_add(2);
            }
            // To handle iframe case . 
            if(!empty($this->params['url']['box'])) {
                return $this->redirect(Router::url(['action' => 'add', 2, '?' => ['box' => 1]]));
            }
            return $this->redirect(Router::url(['action' => 'add', 2]));
        }
        $formError=false;
         if (!check_permission(Proudcts_Add_New_Proudct) && !($this->data['Product']['source_type'] == "package" && check_permission(MANAGE_PACKAGES) )) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

        App::import('Vendor', 'AutoNumber');
        if(isset($this->params['url']['success'])){
        $product_row=$this->Product->read(null,$id);
        if (!empty($this->params['url']['box'])) {
            /**
                * Get Store balances for the product to be used in add invoice and add purchase invoice
            **/    
            App::import('vendor','WarehouseService' , ['file' => 'WarehouseService.php']);
            $ws = new WarehouseService();        
            $pStock = $ws->getStaffProductStock(1, $product_row['Product']['id'] , getAuthOwner('staff_id'));
            $product_row['Product']['store_balance'] = $pStock;
        }
        $this->set('product',$product_row);
        $this->render('owner_add');

        }

        if(isset($this->data['Product']['booking']) || $this->params['url']['booking']){
            $this->set('booking', 1);
            $this->Product->validate['duration_minutes'] = ['rule' => 'notempty', 'message' => __('You need to enter the duration of the service in order for it to be bookable', true)];
        }
        if ($id == 3) {
            $this->handleSiteLimit(
                checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::BUNDLES_ADD),
                ['action' => 'index']
            );
        }

        $this->__init_form_data();

        $owner = getAuthOwner() ;
        if (!empty($this->data)) {
            $enabled_product_tracking = Settings::getValue(InventoryPlugin, 'enable_tracking_numbers');
            if ($enabled_product_tracking && $this->data['Product']['tracking_type'] != 'quantity_only') {
                $this->handleSiteLimit(
                    checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::TRACKING_NUMBERS),
                    ['action' => 'index']
                );
            }

            //suspend backword compatibility
            $this->data['Product']['deactivate'] = 0;

            if (in_array($this->data['Product']['status'], [ProductStatusUtil::STATUS_INACTIVE, ProductStatusUtil::STATUS_SUSPENDED])) {
                $this->data['Product']['deactivate'] = $this->data['Product']['status'];
            }

            $this->data['Product']['status'] = $this->data['Product']['deactivate'];
            foreach ($this->data['priceList'] as $priceListValue) {
                if ($priceListValue <= 0) {
                    $this->flashMessage(__("The number of price list must not be zero.", true), 'Errormessage', 'secondaryMessage');
                    $formError=true;
                }
            }
            \AutoNumber::set_validate(\AutoNumber::TYPE_PRODUCT);
            //set number if new client and he didn't change or number is empty
            if($this->data['Product']['product_code']==$this->data['Product']['default_product_code']){
                $this->data['Product']['default_product_code'] = $this->data['Product']['product_code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PRODUCT);
                $generated_number = true;
            }
            unset( $this->data['ProductBundle']['Tempid'] );

            $this->Product->create();
            if (is_array($this->data['Product']['category']))
                $this->data['Product']['category'] = $this->data['Product']['category'][0];
            if (empty($this->data['Product']['category']))
                $this->data['Product']['category'] = "";
            if (is_array($this->data['Product']['brand']))
                $this->data['Product']['brand'] = $this->data['Product']['brand'][0];
            if (empty($this->data['Product']['brand']))
                $this->data['Product']['brand'] = "";
            if (is_array($this->data['Product']['tags']))
                $this->data['Product']['tags'] = implode(",", $this->data['Product']['tags']);
            if (empty($this->data['Product']['tags']))
                $this->data['Product']['tags'] ="";
            if ( $this->data['Product']['type']  == settings::OPTION_SOLD_TYPE_BUNDLES && empty($this->data['Product']['buy_price']) ){
                $this->data['Product']['buy_price'] = $this->Product->get_bundle_cost ($this->data );
            }
            if (!empty($this->data['Product']['brand'])){
                $this->loadModel('Brand');
                $this->data['Product']['brand_id'] = $this->Brand->findOrCreate($this->data['Product']['brand']);
            }
            
            $this->data['Product']['site_id'] = getAuthOwner('id');
            $this->data['Product']['staff_id'] = getAuthOwner('staff_id');

            $store_balance = true ;
            if (settings::getValue(InventoryPlugin, 'disable_overdraft') && ifPluginActive(InventoryPlugin) && $this->data['Product']['initial_stock_level'] < 0  )
            {

                $this->flashMessage(__('Amount not sufficient', true), 'Errormessage', 'secondaryMessage');
                $store_balance = false ;
            }
            $this->Product->set($this->data);
            if (isset($this->data['ProductAttributeOption']) && isset($this->data['Product']['item_group_id'])){
                $duplicatedProduct = $this->validateProductAttributeOptions($this->data['ProductAttributeOption'],$this->data['Product']['item_group_id']);
                if ($duplicatedProduct){
                    $this->flashMessage( sprintf(__t("The attribute options for the product named '#%s' have already been selected and cannot be repeated") ,'<a href="/owner/products/view/'. $duplicatedProduct['products']['id'] .'"'. '>'.$duplicatedProduct['products']['name'] .' '.$duplicatedProduct['products']['product_code'].'</a>'), 'Errormessage', 'secondaryMessage');
                    $this->redirect($this->referer(array('action' => 'add',$this->data['Product']['type'],$clone_id), true));
                    return;
                }
            }
            if ($this->Product->validates()) {
                if ( $this->viewVars['enable_multi_units'] && !empty ( $this->data['Product']['default_buy_factor_id']))
                {
                    $factor = (float)$this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$this->data['Product']['default_buy_factor_id']]['factor'];
                    $this->data['Product']['buy_price'] = $factor != 0 ? (float)$this->data['Product']['buy_price'] / $factor : false;
                }
                if ( $this->viewVars['enable_multi_units'] && !empty ( $this->data['Product']['default_retail_factor_id']))
                {
                    $this->data['Product']['unit_price'] = $this->data['Product']['unit_price'] ?? 0;
                    $unit_factor=(double) $this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$this->data['Product']['default_retail_factor_id']]['factor'];
                    if (!empty($unit_factor)) {
                        $this->data['Product']['unit_price'] = (double) $this->data['Product']['unit_price'] / $unit_factor ;
                    }
                }
                $this->data['Product']['average_price'] = $this->data['Product']['buy_price'];
                $this->Product->set($this->data);
            }
            if(isset($this->data['Product']['booking']) && !isset($this->data['Product']['Category'])){
                $this->Product->validationErrors['Category'] = __("You need to enter the category of the service in order for it to be bookable", true);
            }
            // Validate that the Products of the Bundle are all quantity_only otherwise show validation error
            $bundle_valid = true;
            $bundleValidationErrorMessage = 'You cannot choose product with type serial number , Lot number or expiry date in the bundle';
            if ($id == Product::BUNDLE_TYPE) {
                foreach ($this->data['ProductBundle'] as $product) {
                    $this->Product = GetObjectOrLoadModel('Product');
                    $product_item = $this->Product->findById($product['product_id'][0]);
                    if(!$product_item){
                        $this->Product->validationErrors['ProductBundle'][$key]['bundle_product_id'] = __("This item doesn't exist or exist in another branch", true);
                        $formError=true;
                    }else{
                        if ($product_item['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY) {
                            $bundle_valid = false;
                        }
                        if ($product_item['Product']['source_type'] == "package") {
                            if(IS_REST) {
                                $this->cakeError('error403');
                            }
                            $bundle_valid = false;
                            $bundleValidationErrorMessage = "You cannot select the service with type package in the bundle";
                        }
                        if (!$bundle_valid) {
                            break;
                        }
                    }
                }
            }

            $barcodes_status = true;

            if (ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, 'enable_multi_units') && $id != Product::SERVICE_TYPE) {
                $this->loadModel('ItemBarcode');

                // Validate product barcodes
                $barcodes = array_merge(array_column((array)$this->data['ItemBarcode'], 'barcode'), [$this->data['Product']['barcode']]);

                $barcodes = array_filter($barcodes, function ($barcode) {
                    return !empty($barcode);
                });
                
                $barcodes_count = 0;
                if (!empty($barcodes)) {

                    $product_conditions[] = "Product.barcode IN ('".implode("','", $barcodes)."')";
                    if (ifPluginActive(BranchesPlugin)  && empty(settings::getValue(BranchesPlugin, 'share_products')) ) {
                        $product_conditions [] = 'Product.branch_id = ' .getCurrentBranchID();
                    }
                    $barcodes_count = $this->ItemBarcode->find('count', ['conditions' => [$product_conditions]])
                        + $this->Product->find('count', ['conditions' => ["Product.barcode IN ('".implode("','", $barcodes)."')"]]);
                }

                if ((count($barcodes) > count(array_unique($barcodes))) || $barcodes_count > 0) {
                   $barcodes_status = false;
                }
            }

            if ( $store_balance && ($appEntitiesFormHandler->validate($this->data) & $this->Product->validates()) && $bundle_valid && !$formError && $barcodes_status) {
                    if(IS_REST && !empty($this->data['Product']['tags'])){
                                        $this->data['ItemsTag']['tags'] = explode(",", $this->data['Product']['tags']);
                                    }
                $categories = [];
                $categories_id = [];

                if (IS_REST) {
                    if(!empty($this->data['Product']['category'])){
                    $categories = [$this->data['Product']['category']];
                    }

                    if(!empty($this->data['Product']['categories_id']) && is_array($this->data['Product']['categories_id'])){
                    $categories_id = $this->data['Product']['categories_id'];
                    }

                } elseif(is_array($this->data['Product']['Category'])) {
                    $categories = $this->data['Product']['Category'];
                }

                //$new_inserts = [];
                $this->loadModel('Category');
                //if the categories are new then you insert them in the categories table and attach the ids
                foreach ($categories as $string) {
                    $this->Category->findOrCreate($string, Category::CATEGORY_TYPE_PRODUCT);
                }
                if (!empty($this->data['Product']['type']) && Product::SERVICE_TYPE == $this->data['Product']['type']) {
                    $this->data['Product']['track_stock'] = 0;
                }
                unset($this->data['Product']['Category']);
                $result = $this->Product->save($this->data);
                $data = $this->data;
                $data['Product']['id'] = $this->Product->id;
                if ($result) {

                    $imagesIds = explode(',',$this->data['Product']['attachment']);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save('product', $this->Product->id, $imagesIds); 
                    }

                    $appEntitiesFormHandler->save($this->Product->id, $this->data);
                    izam_resolve(ProductService::class)->insert(ServiceModelDataTransformer::transform($data, 'Product'));
                    if(isset($this->Product->CustomModelError) && $this->Product->CustomModelError==false){
                        $this->flashMessage(__("Custom Fields can not be saved", true), 'Errormessage', 'secondaryMessage');
                    }

                    if (!empty($generated_number)) \AutoNumber::update_auto_serial(\AutoNumber::TYPE_PRODUCT);
                    elseif ($this->data['Product']['product_code'] != $this->data['Product']['default_product_code']) \AutoNumber::update_last_from_number($this->data['Product']['product_code'], \AutoNumber::TYPE_PRODUCT);

                $product_id = $this->Product->id;
                $this->updateProductPriceGroups($product_id, isset($this->data['priceList'])?$this->data['priceList'] : []);

                //save the bundles according to this product_id
                $this->loadModel('ProductBundle');

                $bundles = $this->data['ProductBundle'];
                foreach ($bundles as $product) {
                    $product['bundle_product_id'] = $product_id;
                    $product['product_id'] = $product['product_id'][0];
                    $data['ProductBundle'] = $product;
                    $data['ProductBundle']['quantity'] =(float) $data['ProductBundle']['quantity'] * (float) (empty($data['ProductBundle']['unit_factor']) ? 1 : $data['ProductBundle']['unit_factor']);
                    $this->ProductBundle->create();
                    if (!$this->ProductBundle->save($data)) {
                        $this->Product->deleteAll(['Product.id' => $product_id]);
                        $this->ProductBundle->deleteAll(['ProductBundle.bundle_product_id' => $product_id]);
                        if (IS_REST) $this->cakeError("error400", ["message" => "Error in Bundle", "validation_errors" => $this->ProductBundle->validationErrors]);
                        $this->flashMessage(__("Error while saving please try again", true), 'Errormessage', 'secondaryMessage');
                        $this->redirect(array('action' => 'add', 3));
                    }
                }

                $this->loadModel('ItemsCategory');

                    foreach ($categories_id as $category_id) {
                        $this->ItemsCategory->create();
                        $obj['ItemsCategory'] = [
                            'item_id' => $product_id,
                            'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT,
                            'category_id' => $category_id
                        ];
                        $this->ItemsCategory->save($obj);
                    }

                    foreach ($categories as $category_name) {
                        $this->ItemsCategory->create();
                        $obj['ItemsCategory'] = [
                            'item_id' => $product_id,
                            'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT,
                            'category_id' => $this->Category->findOrCreate($category_name, Category::CATEGORY_TYPE_PRODUCT)
                        ];
                        $this->ItemsCategory->save($obj);
                    }
                    $this->saveSalesAccount($this->Product->id);
                    $this->saveSalesCostAccount($this->Product->id);

                // $this->add_actionline(ACTION_ADD_PRODUCT, array('primary_id' => $this->Product->id, 'secondary_id' => $this->Product->id, 'param4' => $this->data['Product']['name'], 'param2' => $this->data['Product']['unit_price'], 'param3' => $this->data['Product']['default_quantity']));
                $createdId = $this->Product->id;
                $newData = $this->getRecordWithEntityStructureWithCustomFields(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY, $createdId, 1);
                $st = getEntityBuilder()->buildEntity(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY);
                $addedRelations = [];
                if (!empty($this->data['Product']['clone_id'])) {
                    $newData['is_clone'] = ['product' => ['id' => $this->data['Product']['clone_id']]];
                    $addedRelations[] = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest($this->data['Product']['clone_id'], EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY);
                }
                $newData['tracking_type'] = $newData['track_stock'] ? $newData['tracking_type'] : null;
                $requests = (new DetailsEntityActivityLogRequestsCreator(

                ))->create($st, $newData, [], $addedRelations);

                $activityLogService =  new \App\Services\ActivityLogService();
                foreach ($requests as $requestObj) {
                    $activityLogService->addActivity($requestObj);
                }
                if (ifPluginActive(InventoryPlugin)) {
                    if (isset($this->data['Product']['track_stock']) && $this->data['Product']['track_stock'] == "1" && !empty ($this->data['Product']['initial_stock_level'])) {
                        $this->loadModel('StockTransaction');
                        $data['date'] = date("Y-m-d H:i:s");
                        $data['currency_code'] = getAuthOwner('currency_code');
                        $data['source_type'] = StockTransaction::SOURCE_MANUAL;
                        $data['order_id'] = NULL;
                        $data['product_id'] = $this->Product->id;
                        $data['status'] = StockTransaction::STATUS_PROCESSED;
                        $data['transaction_type'] = StockTransaction::TRANSACTION_IN;
                        $data['quantity'] = (float)$this->data['Product']['initial_stock_level'];
                        $data['price'] = (float)$this->data['Product']['buy_price'];
                        $data['staff_id'] = getAuthOwner('staff_id');
                        $data['total_price'] = $data['quantity'] * $data['price'];
                        $data['store_id'] = $this->data['Product']['store_id'];
                        $data['fetch_from_raw'] = 0;
                        $data['added_by'] = getAuthOwner('staff_id');
                        $product = $this->data;
                        StockTransaction::saveTransaction($data, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));
                    }
                }

                if (!empty($this->data['ItemBarcode'])) {
                    foreach ($this->data['ItemBarcode'] as $item_barcode) {
                        if ($item_barcode['unit_price'] != '') {
                            $factor_value = $this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$item_barcode['unit_factor_id']]['factor'];
                            $item_barcode['unit_price'] = $factor_value != 0 ? $item_barcode['unit_price'] / $factor_value : false;
                        }
                        $record = [
                            'ItemBarcode' => array_merge($item_barcode,
                                [
                                    'product_id' => $product_id,
                                    'created' => date("Y:m:d H:i:s"),
                                    'modified' => date("Y:m:d H:i:s"),
                                ]
                            )
                        ];
                        $this->ItemBarcode->create();
                        $this->ItemBarcode->save($record);
                    }
                }
                $this->loadModel('ItemGroupAttributeOption');
                $this->loadModel('ProductAttributeOption');
                if (!empty($this->data['ProductAttributeOption']) && isset($this->data['Product']['item_group_id'])) {
                    $this->loadModel( 'ItemGroupAttribute');
                    $itemGroupAttributes = $this->ItemGroupAttribute->find('all', ['conditions' => ['ItemGroupAttribute.item_group_id' => $this->data['Product']['item_group_id'] ]] );
                    foreach ($this->data['ProductAttributeOption'] as $productAttributeOption) {
                        if (empty($productAttributeOption['attribute']) || empty($productAttributeOption['option']) ){
                            continue;
                        }
                        $this->createItemGroupAttributeOption($itemGroupAttributes , $productAttributeOption);
                        //as from now we only clone so avoid deleting old ones
                        unset($productAttributeOption['id']);
                        $record = [
                            'ProductAttributeOption' => array_merge($productAttributeOption,
                                [
                                    'product_id' => $product_id,
                                    'created' => date("Y:m:d H:i:s"),
                                    'modified' => date("Y:m:d H:i:s"),
                                ]
                            )
                        ];
                        $this->ProductAttributeOption->create();
                        $this->ProductAttributeOption->save($record);
                    }
                }

                if (IS_REST) {
                    $this->set('id', $product_id);
                    
                    // Upload Attachments.
                    $attachments = $this->data['Product']['attachment'];
                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                       izam_resolve(AttachmentsService::class)->save('product', $product_id, $imagesIds); 
                    }

                    $this->render('created');
                    return;
                }

                $this->flashMessage(sprintf(__('%s has been saved', true), __('Product', true)), 'Sucmessage');

                $action = 'index';
                if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_new')
                    $action = 'add';
                $redirect = array('action' => $action,$this->data['Product']['type'],'?reset=1');
                if (!empty($this->params['url']['box'])) {
                    $closeModal = (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_new') ? 0 : 1;
                    $redirect = array('action' => 'add', $this->Product->id, '?' => array('box' => 1, 'success' => 1,"close-modal" => $closeModal));
                }

                if(isset($this->data['Product']['booking'])){
                    $this->redirect(['controller' => 'bookings', 'action' => 'add']);
                }
                $this->redirect($redirect);
            }else {
                //if not saved
                if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Product->validationErrors]);
                //$this->Product->validationErrors = $this->Product->validationErrors ;
                $errors = $this->ModelName->validationErrors;
                debug($errors);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Product', true)));
            }

            } else {

                foreach( $this->data['Product']['Category'] as $index => $category )
                {
                    $categories_list[$index] = ["id" => $category, "name" => $category];
                }
                $this->set('categories_values', $categories_list );

                // Used To get attachments details in case of Validation error occurred. 
                if (!empty($this->data['Product']['attachment'])) {
                    $filesId = explode(',', $this->data['Product']['attachment']);
                    $attachments = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                    $this->data['Attachments'] = $attachments;
                }
				if(IS_REST) {

                    $response = [
                        "message" => null,
                        "validation_errors" => $this->Product->validationErrors
                    ];

                    if ($this->Product->id) {
                        $response['error_type'] = \Izam\Daftra\Common\Utils\ErrorTypes::DUPLICATED;
                        $response['extra_data'] = ['id' => $this->Product->id];
                    }

                    $this->cakeError("error400", $response);
                }
                $errors = $this->ModelName->validationErrors;
                if ($barcodes_status == false) {
                    $this->flashMessage(__("Product barcodes must be unique.", true), 'Errormessage', 'secondaryMessage');
                }
                if($bundle_valid)
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Product', true)));
                else
                    $this->flashMessage(__($bundleValidationErrorMessage, true), 'Errormessage', 'secondaryMessage');
            }
        } else {
            if(!empty($clone_id)){
                $product = $this->Product->find('first', ['conditions' => ['Product.id' => $clone_id]]);
                if($product){
                    $this->data = $product;
                    unset($this->data['Product']['id']);
                    unset($this->data['Product']['barcode']);
                    $this->data['Product']['low_stock_thershold'] = 0;
                    $this->data['Product']['track_stock'] = true;
                    $this->data['Product']['initial_stock_level'] = 0;
                    $categories_list = array();
                    $this->loadModel('ItemsCategory');
                    $categories = $this->ItemsCategory->find('all', ['conditions' => ['ItemsCategory.item_type'=>ItemsCategory::ITEM_TYPE_PRODUCT , 'ItemsCategory.item_id'=>$clone_id] ]);
                    foreach( $categories as $index => $category )
                    {
                        $categories_list[$category['Category']['id']] = ["id"=> $category['Category']['name'], "name"=>$category['Category']['name']];
                    }
                    $this->set('categories_values', $categories_list );

                    if (!empty($product['Product']['brand'])) {
                        $brands = explode(",", $product['Product']['brand']);
                        if (count($brands) > 0) {
                            $this->data['Product']['brand'] = json_encode($brands);
                        }
                    }
                    
                    $this->loadModel('ProductBundle');
                    $product_bundles = $this->ProductBundle->find('all',['conditions'=>['ProductBundle.bundle_product_id'=>$clone_id]]);
                    $pb = [] ;
                    foreach ($product_bundles as $p ) {
                        unset($p['ProductBundle']['id']);
                        unset($p['ProductBundle']['bundle_product_id']);
                        $pb[] = $p ;
                    }
                    $this->set('product_bundles', $pb );
                    
                    $this->loadModel('GroupPrice');
                    $this->loadModel('ProductPrice');
                    // get price lists assigned to this product
                    $ProductPrice = $this->ProductPrice->find('all',array('conditions'=>array('ProductPrice.product_id'=>$clone_id)));
                    // get all price lists
                    $activeGroupPrices = $this->GroupPrice->getActiveList();
                    $allGroupPrices = $this->GroupPrice->getAll();
                    foreach ($ProductPrice as $price_row) {
                        $PriceList[$price_row['ProductPrice']['group_price_id']] = array(
                            'id' => $price_row['ProductPrice']['id'],
                            'price' => $price_row['ProductPrice']['price'],
                            'name' => $allGroupPrices[$price_row['ProductPrice']['group_price_id']]
                        );
                        unset($activeGroupPrices[$price_row['ProductPrice']['group_price_id']]);
                    }

                    $this->set('GroupPrices', $activeGroupPrices);
                    $this->set('PriceList', $PriceList);
                    $this->set('allowPriceList', ((count($activeGroupPrices ?? [])+ count($PriceList ?? [])) > 0));
                }else{
                    $this->flashMessage(__('Product not found', true));
                    $this->redirect($this->referer(array('action' => 'index'), true));
                }
            }else{
                $this->data['Product']['low_stock_thershold'] = 0;
                $this->data['Product']['track_stock'] = true;
                $this->data['Product']['initial_stock_level'] = 0;
                $this->loadModel('GroupPrice');
                $GroupPrices = $this->GroupPrice->getActiveList();
                $this->set('GroupPrices', $GroupPrices);
                $this->set('allowPriceList', (count($GroupPrices) > 0));
            }
            $this->data['Product']['product_code'] = $this->data['Product']['default_product_code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PRODUCT);
            $this->data['Product']['type'] =$id;
        }
        $this->set('clone_id', $clone_id);
        $this->set('primary_store', $this->Store->getPrimaryStore() );
        $tracking_type = TrackStockUtil::QUANTITY_ONLY;
        if (!empty($this->data['Product']['tracking_type'])) {
            $tracking_type = $this->data['Product']['tracking_type'];
        }
        $this->set('tracking_type', $tracking_type);
        $this->loadModel('ItemPermission');
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING));
        $this->loadModel('Supplier');
        $this->set('supplier_count',$this->Supplier->find('count'));
        $this->loadModel('Tax');
        $this->set('taxes', $this->Tax->getTaxList());

        // set default product tax if exist change it from product setting
        $this->data['Product']['tax1']=$this->data['Product']['tax1'] ?? settings::getValue(0, 'default_product_tax_1_id');
        $this->data['Product']['tax2']=$this->data['Product']['tax2'] ?? settings::getValue(0, 'default_product_tax_2_id');

        $this->set('you_sell',$id);
        $this->set('enable_requisitions',settings::getValue(InventoryPlugin, 'enable_requisitions'));
        $this->set('enable_requisitions_po',settings::getValue(InventoryPlugin, 'enable_requisitions_po'));
        $this->set('discount_types',$this->Product->getDiscountTypes());
        if (empty($id)){
        $this->set_service_product_settings();
        }

        $forms = $appEntitiesFormHandler->getAppsCreateForms($this->data);
        $this->set('forms', $forms);

        $ProductTypes=Product::ProductTypes();
        $this->set('title_for_layout',  sprintf(__('Add %s', true),$ProductTypes[$id]));
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('Product'));

    }

    function set_service_product_settings() {
        App::import('Vendor', 'settings');
        $you_sell = settings::getValue(InvoicesPlugin, 'sold_item_type');
        $this->set('you_sell', $you_sell);
        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
        $this->set('enable_multi_units' , $enable_multi_units) ;
        $this->set(settings::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS ,  settings::getValue(InventoryPlugin,settings::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS));
    }

    function owner_remove_photo($id = null) {
        $row = $this->Product->ProductImage->read(null, $id);
        if (!$row) {
            echo 'false';
            die();
        }

        $path = WWW_ROOT . "/files/" . SITE_HASH . "/product-images/" . $row['ProductImage']['file'];
        unlink($path);
        $this->Product->ProductImage->delete($id);
        echo 'true';
        die();
    }

    function owner_set_default($id = null) {
        $row = $this->Product->ProductImage->read(null, $id);
        //debug($row);
        if (!$row) {
            echo 'false';
            die();
        }
        $this->Product->ProductImage->updateAll(array('ProductImage.default' => 0),array('ProductImage.product_id'=>$row['ProductImage']['product_id'],'ProductImage.id !=' => $id));
        $this->Product->ProductImage->updateAll(array('ProductImage.default' => 1),array('ProductImage.product_id'=>$row['ProductImage']['product_id'],'ProductImage.id' => $id));
 
        // Remove s3 default image 
        izam_resolve(AttachmentsService::class)->removeDefault('product',$row['ProductImage']['product_id']);
     
        echo 'true';
        die();
    }

    /**
     * This function is supposed to fix the JPG / JPEG image orientation in case it was wrong 
     * @param mixed $imagePath 
     * @param mixed $saveTo 
     * @return bool 
     */
    private function orientateJpgImages($imagePath, $saveTo) {

        $exif = exif_read_data($imagePath);
        if (!empty($exif['Orientation']) && in_array($exif['Orientation'], [2, 3, 4, 5, 6, 7, 8])) {
            $image = imagecreatefromjpeg($imagePath);
            if (in_array($exif['Orientation'], [3, 4])) {
                $image = imagerotate($image, 180, 0);
            }
            if (in_array($exif['Orientation'], [5, 6])) {
                $image = imagerotate($image, -90, 0);
            }
            if (in_array($exif['Orientation'], [7, 8])) {
                $image = imagerotate($image, 90, 0);
            }
            if (in_array($exif['Orientation'], [2, 5, 7, 4])) {
                imageflip($image, IMG_FLIP_HORIZONTAL);
            }
            imagejpeg($image, $saveTo, 90);
            return true;
        }
        return false;
    }

    function owner_add_photo($id = null) {
        $this->set('upload_settings', $this->Product->ProductImage->upload_settings);
        $ajax = $this->RequestHandler->isAjax();

        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');
        $product = $this->Product->find(array('Product.id' => $id));
        if (!$product) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        if (!empty($this->data) and $ajax == true) {
            require_once APP . 'vendors' . DS . 'FileUploader.php';
            $status = FileUploader::Postupload($this->data['ProductImage']['file'], $this->Product->ProductImage->upload_settings['file']);
            if($status['error']){
             echo json_encode(array('post_file' => false, 'error' => true, 'msg' => $status['msg']));
             die();
            }
            $row = FileUploader::ReadHash($status['hash']);
            $uniqid = substr(uniqid(), 8);
            $base_name = substr($row['file_name'], 0, strrpos($row['file_name'], '.'));
            $ext = substr($row['file_name'], strrpos($row['file_name'], '.'));
            $fname = Inflector::slug($base_name) . $ext;
            $filename = $uniqid . '_' . $fname;
            @mkdir(WWW_ROOT . "/files/" . SITE_HASH . "/product-images/");
            $isOreintated = false;
            /**
             * Check if image is of type jpg then check for its exif information and fix orientation if it was wrong
             */
            if(in_array($ext, ['.jpg','.jpeg'])) {
               $isOreintated = $this->orientateJpgImages($row['file_path'], WWW_ROOT . "/files/".SITE_HASH."/product-images/" . $filename);
            }
            //Save the image only in case it was not saved in the previous orientation call
            if(!$isOreintated) {
                move_uploaded_file($row['file_path'], WWW_ROOT . "/files/".SITE_HASH."/product-images/" . $filename);
            }
            $data['ProductImage']['product_id'] = $id;
            $data['ProductImage']['file'] = $filename;
            $conditions['ProductImage.product_id'] = $id;
            if ($this->Product->ProductImage->find('count', array('conditions' => $conditions)) == 0) {
                $data['ProductImage']['default'] = 1;
            } else {
                $data['ProductImage']['default'] = 0;
            }

            $this->Product->ProductImage->save($data);
            
            echo json_encode(array('id'=>$this->Product->ProductImage->getLastInsertID(),'url'=>"/files/".SITE_HASH."/product-images/" . $filename));
            FileUploader::RemoveHash($status['hash']);
            die();
        }
    }



    function owner_photos($id = null) {
        $this->set('upload_settings', $this->Product->ProductImage->upload_settings);
        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');

         // Load Attachmnets . 
         $this->Product->bindModel(
            array(
                'hasAndBelongsToMany' =>
                [
                    'Attachmnets' =>
                    [
                        'className' => 'FileModel',
                        'joinTable' => 'entity_attachments',
                        'foreignKey' => 'entity_id',
                        'associationForeignKey' => 'file_id',
                        'conditions'  => ['EntityAttachment.entity_key = "product" and EntityAttachment.deleted_at IS NULL']
                    ]
                ]
            )
        );

        $product = $this->Product->find(array('Product.id' => $id));
        if (!$product) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        $conditions['ProductImage.product_id'] = $id;
        $photos = $this->Product->ProductImage->find('all', array('conditions' => $conditions));

        // Make s3 images as default.  
        $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product',$product['Product']['id']);
        if(count( $defaultS3Images))
        {
            $product['defaultImage']['id']  =  $defaultS3Images[0]->file_id;
            $product['defaultImage']['is_s3'] = true;
        }elseif (count($product['ProductMasterImage'])) {
            $product['defaultImage']['id'] =  $product['ProductMasterImage']['id'];
            $product['defaultImage']['is_s3'] = false;
        } 

        $this->set('id', $id);
        $this->set('data', $photos);
        $this->set('productDetails', $product);
    }

    function owner_manual_stock_adjust($id = null) {

        $this->loadModel('ItemPermission');
        $store_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE, ItemPermission::PERMISSION_STOCK_UPDATING);

        $this->set('store_list',$store_list);
        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');
		if(IS_REST) $id = $this->data["StockTransaction"]["product_id"];
        $product = $this->Product->find(array('Product.id' => $id));
        if (!$product) {
			if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Product', true))));
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        if (!check_permission(Adjust_Product_Inventory)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        if ($product['Product']['status'] != ProductStatusUtil::STATUS_ACTIVE) {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("You cannot create stock transactions for %s products", true), __(ProductStatusUtil::getStatusName($product['Product']['status']), true))));
            $this->flashMessage(sprintf(__("You cannot create stock transactions for %s products", true), __(ProductStatusUtil::getStatusName($product['Product']['status']),true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        if ($product['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY){
            if(IS_REST) $this->cakeError('error403');
            $requisitions_url = Router::url(['controller' => 'requisitions','action' => 'index']);
            $string = __("Manage Requisitions", true);
            $req_html = "<a href=\"$requisitions_url\">$string</a>";
            $this->flashMessage(sprintf(__("You can use this option using Requisitions %s Link", true), $req_html), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('StockTransaction');
        $this->StockTransaction->bindAttachmentRelation('stock_transaction');
        $this->set('product_stock', StockTransaction::getProductStock($id));
        $this->set('this_stock', 0);
        $this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList(array(), true));
        $pp = [$product['Product']] ;
        $this->Product->add_multi_units( $pp, true ) ;
        $product['Product'] = $pp[0];
        debug ( $product ) ;
        $this->set('product', $product);
        if (!empty($this->data)) {
            $AUTO_INCREMENT = $this->Product->query("SELECT AUTO_INCREMENT FROM information_schema.tables WHERE table_name = 'stock_transactions' AND table_schema = DATABASE( ) ;");
            $this->data['StockTransaction']['price'] = (float)$this->data['StockTransaction']['price'];

            if (empty($this->data['StockTransaction']['price'])) {
                $this->data['StockTransaction']['price'] = $product['Product']['average_price'];
            }

            if (empty($this->data['StockTransaction']['price']) && $this->data['StockTransaction']['transaction_type'] == 2) {
                $this->data['StockTransaction']['price'] = $product['Product']['average_price'];
            }


            $data['currency_code'] = $this->data['StockTransaction']['currency_code'];
            if (empty($this->data['StockTransaction']['received_date'])) {
                $data['received_date'] = date("Y-m-d H:i:s");
            } else {
                $data['received_date'] = $this->Product->formatDateTime($this->data['StockTransaction']['received_date']);
            }
            $data['source_type'] = StockTransaction::SOURCE_MANUAL;
            $data['order_id'] = $AUTO_INCREMENT[0]['tables']['AUTO_INCREMENT'];
            $data['product_id'] = $id;
            $data['store_id'] = $this->data['StockTransaction']['store_id'];
            $data['journal_account_id'] = $this->data['StockTransaction']['journal_account_id'];
            $data['status'] = StockTransaction::STATUS_PROCESSED;
            $data['fetch_from_raw'] = (!empty($this->data['StockTransaction']['fetch_from_raw'])?1:0);
            $store_balance = true ;
            if ($this->data['StockTransaction']['transaction_type'] == 1) {
                $data['transaction_type'] = StockTransaction::TRANSACTION_IN;
                $data['quantity'] = (float)$this->data['StockTransaction']['quantity'];
	            if (settings::getValue(InventoryPlugin, 'disable_overdraft') && ifPluginActive(InventoryPlugin)) {
		            //We will check the bundle components for availablilty
		            if (
			            $data['fetch_from_raw']
			            && settings::getValue(InventoryPlugin, 'enable_bundles')
			            && $product['Product']['type'] == Product::BUNDLE_TYPE
		            ) {
			            if (!GetObjectOrLoadModel('StockTransaction')->checkBalanceOpenBundles($id, $data['store_id'], $data['quantity'], 0, 'deduct')) {
				            $this->flashMessage(__('Components amounts are not sufficient.', true), 'Errormessage', 'secondaryMessage');
				            $store_balance = false;
			            }
		            }
	            }
            } else {
                $data['transaction_type'] = StockTransaction::TRANSACTION_OUT;
	            $data['quantity'] = (float)$this->data['StockTransaction']['quantity'];
                if($transaction = $this->StockTransaction->find('first', ['conditions' => [
                    'StockTransaction.product_id' => $id, 
                    'StockTransaction.received_date <' => $data['received_date'], 
                    'StockTransaction.ignored' => 0, 
                    'StockTransaction.status' => StockTransaction::STATUS_PROCESSED, 
                    'StockTransaction.store_id' => $this->data['StockTransaction']['store_id']], 
                    'order' => 'StockTransaction.received_date DESC'])
                ){
                    $this->data['StockTransaction']['price'] = $transaction['StockTransaction']['purchase_price'];
                    $this->data['StockTransaction']['purchase_price'] = $transaction['StockTransaction']['purchase_price'];
                }
                if (settings::getValue(InventoryPlugin, 'disable_overdraft') && ifPluginActive(InventoryPlugin) )
                {
                    if ( !GetObjectOrLoadModel('StockTransaction')->check_balance_open ( $id ,$data['store_id'], $data['quantity'] ,0, 'deduct' ) ) {
                        $this->loadModel ( 'Product');
                        $product = $this->Product->find ( 'first',['recursive' => -1 , 'conditions' => ['Product.id' => $id]]);
                        $name_code = $product['Product']['name'].' #'.(empty ( $product['Product']['product_code'])?$product['Product']['id']: $product['Product']['product_code']) ;
						if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Amount not sufficient for %s', true) , $name_code )]);
                        $this->flashMessage(sprintf(__('Amount not sufficient for %s', true) , $name_code ), 'Errormessage', 'secondaryMessage');
                        $store_balance = false  ;
                    }
                }
            }
            $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
            if ( $enable_multi_units ) {
                $data['unit_factor_id'] = $this->data['StockTransaction']['unit_factor_id'];;
                $data['unit_factor'] = $this->data['StockTransaction']['unit_factor'];
                $data['unit_name'] = $this->data['StockTransaction']['unit_name'];
                $data['unit_small_name'] = $this->data['StockTransaction']['unit_small_name'];
            }
            if ( $store_balance ) {
                $data['price'] = (float)$this->data['StockTransaction']['price'];

                $data['notes'] = $this->data['StockTransaction']['notes'];
                $this->loadModel ('TransactionCategory') ;

                            $data['transaction_category_id'] = $this->TransactionCategory->findOrCreate($this->data['StockTransaction']['transaction_category_id'][0] );
                $data['staff_id'] = $owner['staff_id'];
                $data['added_by'] = $owner['staff_id'];
                $data['total_price'] = $data['quantity'] * $data['price'];
                $data['branch_id'] = getCurrentBranchID();
				$this->StockTransaction->set( $data );
				$valid = $this->StockTransaction->validates();
				if($valid){
					$stock_transaction_id= StockTransaction::saveTransaction($data, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));
                    $attachments = $this->data['StockTransaction']['attachment'];
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                    
                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save('stock_transaction', $stock_transaction_id, $imagesIds); 
                    }
					if(IS_REST){
					$this->set('id', $stock_transaction_id);
					$this->render('created');
					return;
				}
					$this->flashMessage(sprintf(__('Manual Stock Adjustment has been saved', true), __('Product', true)), 'Sucmessage');
					$this->redirect(array('action' => 'view', $id));
				}else{
					if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->StockTransaction->validationErrors]);
					$this->flashMessage(sprintf(__('Manual Stock Adjustment couldn\'t be saved', true)));
                    if (!empty( $this->StockTransaction->validationErrors)) {
                        $this->flashMessage(implode("<br/>", $this->StockTransaction->validationErrors), 'Errormessage', 'secondaryMessage');
                    }

                }
            }



        }else {
            if ( $product['Product']['type'] == settings::OPTION_SOLD_TYPE_BUNDLES){
                $this->loadModel('ProductBundle') ;
                $bundle_products  = $this->ProductBundle->find('all' , ['recursive' => -1 ,  'conditions' =>['bundle_product_id' =>$product['Product']['id'] ]  ]) ;
                $bp = [] ;
                foreach ( $bundle_products as $b){
                    $bp['ProductBundle'][] = $b['ProductBundle'] ;
                }
                $this->data['StockTransaction']['add_price'] = $this->Product->get_bundle_cost($bp);
                $this->data['StockTransaction']['detract_price'] = $product['Product']['average_price'];
                $this->data['StockTransaction']['price'] = $this->Product->get_bundle_cost($bp);
            }else {
                $this->data['StockTransaction'] ['price'] = $product['Product']['average_price']  ;
            }
            $this->data['StockTransaction']['fetch_from_raw'] = true ;
            $this->data['StockTransaction'] ['store_id'] = $this->Store->getPrimaryStore();
            $this->data['StockTransaction']['currency_code'] = getAuthOwner('currency_code');
        }

        if(!empty($this->data['StockTransaction']['attachment'])){                       
            $filesId = explode(',',$this->data['StockTransaction']['attachment']);
            $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
            $this->data['Attachments'] = $attachment;
        }

        $this->loadModel ('TransactionCategory') ;
        $transaction_categories = $this->TransactionCategory->find ('list' );
        foreach ( $transaction_categories as $k => $v ) {
                if ( !empty ( $v)){
                    $transaction_categories[$v] = $v ;
                }
                unset ( $transaction_categories[$k] );
        }
        $this->set ('transaction_category_ids' , $transaction_categories );
        $this->set ( 'product_id' , $id);
        if ( ifPluginActive ( AccountingPlugin))
        {
            $this->loadModel('JournalAccount');
            $this->loadModel('JournalCat');
            $this->JournalAccount->recursive = -1;
            $accounts = $this->JournalAccount->find('all');
            $catsList = $this->JournalCat->find('list');
            $this->set ( compact ('accounts' , 'catsList' ) );
        }

        $unit_factors = []  ;
        foreach ( $product['Product']['unit_factors'] as $f   )
        {
            $unit_factors[$f['id']] = $f['factor_name'];
        }
        $this->set('unit_factors',$unit_factors ) ;
        $this->set('enable_multi_units' ,settings::getValue(InventoryPlugin,'enable_multi_units') );
    }

    function owner_stock_transactions($id = null) {
        if (!check_permission(Adjust_Product_Inventory)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $product = $this->Product->find(array('Product.id' => $id));
        if (!$product) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        $this->loadModel('StockTransaction');
        $conditions = array('product_id' => $id);

        if (!empty($this->params['url']['source_type'])) {
            $source_type = explode(",", $this->params['url']['source_type']);
            if (count($source_type) == 1) {
                $conditions += array('StockTransaction.source_type' => $source_type[0]);
            } else {
                $conditions += array('StockTransaction.source_type' => $source_type);
            }
        }

        $order = array('StockTransaction.received_date' => 'DESC', 'StockTransaction.id' => 'DESC');
        if (!empty($this->params['url']['order']))
            $order = array('StockTransaction.received_date' => 'ASC', 'StockTransaction.id' => 'ASC');


        $this->paginate['StockTransaction'] = array('conditions' => $conditions,
            'order' => $order,
            'limit' => 100,
        );

        $data = $this->StockTransaction->paginate();

        $this->set('default_currency',$this->StockTransaction->get_default_currency() );

        $this->set('Sources', $this->StockTransaction->getSources());
        $this->set('data', $data);
    }

    function owner_receive_stock_transaction($id = null) {
        $owner = getAuthOwner();
        $this->loadModel('StockTransaction');
        $stock = $this->StockTransaction->find(array('StockTransaction.id' => $id));
        $product = $stock;
        if (!$stock) {
            $this->redirect($this->referer(array('action' => 'index')));
        }
        if (!check_permission(Adjust_Product_Inventory)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $this->StockTransaction->id = $id;

        $data = $stock['StockTransaction'];
        $data['status'] = 4;
        $data['received_date'] = date("Y-m-d H:i:s");
        StockTransaction::saveTransaction($data, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));

        if ($stock['StockTransaction']['source_type'] == StockTransaction::SOURCE_PO)  {
            $this->updatePurchaseOrderJournals($stock['StockTransaction']['order_id']);
        }

        $this->redirect(Router::url(array('action' => 'view', $stock['StockTransaction']['product_id'], '#' => 'StockTransactionsBlock')));
    }

    function owner_delete_stock_transaction($id = null, $restore = 0) {
        $owner = getAuthOwner();
        $this->loadModel('StockTransaction');
		$this->loadModel('TransactionCategory');
        $stock = $this->StockTransaction->find(array('StockTransaction.id' => $id));
        $product = $stock;
        if (!$stock) {
            $this->redirect($this->referer(array('action' => 'index')));
        }
        if (!check_permission(Adjust_Product_Inventory)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $is_ajax = false ;
        if ($this->RequestHandler->isAjax()) {
            $is_ajax = true  ;
        }
		$this->StockTransaction->set($stock['StockTransaction']);
		$valid = $this->StockTransaction->validates();
	    $this->TransactionCategory->removeIfNotUsed($stock['StockTransaction']['transaction_category_id']);
		if(!$valid ){
			$this->flashMessage(sprintf(__('Manual Stock Adjustment couldn\'t be saved', true)));
			if($is_ajax)
			die(json_encode(['errors' => $this->StockTransaction->validationErrors],JSON_UNESCAPED_SLASHES));
		}
        $this->StockTransaction->id = $id;
        if ($restore == 0) {
            if (settings::getValue(InventoryPlugin, 'disable_overdraft') && ifPluginActive(InventoryPlugin) )
            {
                if ( !GetObjectOrLoadModel('StockTransaction')->check_balance_open ( $stock['StockTransaction']['product_id'] ,$stock['StockTransaction']['store_id'], 0 ,$stock['StockTransaction']['quantity'], 'add' )&&$stock['StockTransaction']['quantity']> 0  ) {
                    if ( $is_ajax ) {

                        die ( json_encode(['result' => '-12']) ) ;
                    }else {
                        $this->loadModel ( 'Product');
                        $product = $this->Product->find ( 'first',['recursive' => -1 , 'conditions' => ['Product.id' => $stock['StockTransaction']['product_id']]]);
                        $name_code = $product['Product']['name'].' #'.(empty ( $product['Product']['product_code'])?$product['Product']['id']: $product['Product']['product_code']) ;
                        $this->flashMessage(sprintf(__('Amount not sufficient for %s', true) , $name_code ), 'Errormessage', 'secondaryMessage');
                        $this->redirect(Router::url(array('action' => 'view', $stock['StockTransaction']['product_id'], '#' => 'StockTransactionsBlock')));die ;
                    }
                }
            }
            if ($stock['StockTransaction']['source_type'] == StockTransaction::SOURCE_MANUAL) {
                StockTransaction::removeTransaction($id, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));
            } else {
                $data = $stock['StockTransaction'];
                $data['ignored'] = 1;
                $data['ignored_date'] = date("Y-m-d H:i:s");
                StockTransaction::saveTransaction($data, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));
            }
        } else {
            $data = $stock['StockTransaction'];
            $data['ignored'] = 0;
            $data['ignored_date'] = NULL;
            StockTransaction::saveTransaction($data, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));
        }

        if ($stock['StockTransaction']['source_type'] == StockTransaction::SOURCE_PO)  {
            $this->updatePurchaseOrderJournals($stock['StockTransaction']['order_id']);
        }

		if($is_ajax)
			die();
        $this->redirect(Router::url(array('action' => 'view', $stock['StockTransaction']['product_id'], '#' => 'StockTransactionsBlock')));
    }

    private function updatePurchaseOrderJournals($purchaseOrderId)
    {
        $this->PurchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
        $purchaseOrder = $this->PurchaseOrderModel->getPurchaseOrder($purchaseOrderId);
        $this->PurchaseOrderModel->update_journals($purchaseOrder);
    }

    function owner_edit_stock_transaction($id = null) {
        $this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList(array(), true));
	$this->loadModel ('TransactionCategory') ;

        $owner = getAuthOwner();
        $this->loadModel('StockTransaction');
        $this->StockTransaction->bindAttachmentRelation('stock_transaction');
        $stock = $this->StockTransaction->find(array('StockTransaction.id' => $id));
        $this->validate_open_day($stock['StockTransaction']['received_date']);
        if (!$stock) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('Stock Transaction not found', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        $this->loadModel('ItemPermission' ) ;


        $store_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE, ItemPermission::PERMISSION_STOCK_UPDATING);



        if (!check_permission(Adjust_Product_Inventory) || !in_array($stock['StockTransaction']['store_id'] , array_keys($store_list) ) && !getAuthOwner('is_super_admin')  ) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');

        if (!empty($this->data)) {
                $this->data['StockTransaction']['id'] = $id;
                $org_date=$this->data;
                if ($stock['StockTransaction']['source_type'] != StockTransaction::SOURCE_MANUAL) {
                        $this->data['StockTransaction']=array_merge($stock['StockTransaction'],$this->data['StockTransaction']);
                }
               
                $this->data['StockTransaction']['received_date'] = $this->StockTransaction->formatDateTime($this->data['StockTransaction']['received_date'], getAuthOwner('date_format'));
                // Checking if the price is empty not having legit 0 price
                if (empty($this->data['StockTransaction']['price']) && $this->data['StockTransaction']['price'] != '0') {
                    $this->data['StockTransaction']['price'] = $stock['Product']['average_price'];
                    if($this->data['StockTransaction']['transaction_type'] == StockTransaction::TRANSACTION_OUT
                        && $transaction = $this->StockTransaction->find('first', ['conditions' => [
                            'StockTransaction.product_id' => $stock['StockTransaction']['product_id'], 
                            'StockTransaction.received_date <' => $this->data['StockTransaction']['received_date'], 
                            'StockTransaction.ignored' => 0, 
                            'StockTransaction.status' => StockTransaction::STATUS_PROCESSED, 
                            'StockTransaction.store_id' => $this->data['StockTransaction']['store_id']], 
                            'order' => 'StockTransaction.received_date DESC'])
                    ){
                        $this->data['StockTransaction']['price'] = $transaction['StockTransaction']['purchase_price'];
                        $this->data['StockTransaction']['purchase_price'] = $transaction['StockTransaction']['purchase_price'];
                    }
                }
                $this->data['StockTransaction']['source_type'] = $stock['StockTransaction']['source_type'];
                $this->data['StockTransaction']['order_id'] = $stock['StockTransaction']['order_id'];
                $this->data['StockTransaction']['product_id'] = $stock['StockTransaction']['product_id'];
                $this->data['StockTransaction']['status'] = $stock['StockTransaction']['status'];
                $this->data['StockTransaction']['staff_id'] = $owner['staff_id'];
                $this->data['StockTransaction']['total_price'] = (float) $this->data['StockTransaction']['quantity'] * (float) $this->data['StockTransaction']['price'];
                if ($this->data['StockTransaction']['transaction_type'] == StockTransaction::TRANSACTION_IN) {
                    $this->data['StockTransaction']['transaction_type'] = StockTransaction::TRANSACTION_IN;
                } else if(isset($org_date['StockTransaction']['quantity'])&&isset($org_date['StockTransaction']['transaction_type'])) {
                    $this->data['StockTransaction']['transaction_type'] = StockTransaction::TRANSACTION_OUT;
                }

                $store_balance = true ;
                if (settings::getValue(InventoryPlugin, 'disable_overdraft') && ifPluginActive(InventoryPlugin) )
                {
                    $product = $this->Product->find ( 'first',['recursive' => -1 , 'conditions' => ['Product.id' => $stock['StockTransaction']['product_id']]]);
                    if ( !GetObjectOrLoadModel('StockTransaction')->check_balance_open ( $stock['StockTransaction']['product_id'] ,$stock['StockTransaction']['store_id'], $this->data['StockTransaction']['quantity'] ,$stock['StockTransaction']['quantity'], ($this->data['StockTransaction']['transaction_type'] == StockTransaction::TRANSACTION_IN?'add':'deduct') ) ) {
                        $this->loadModel ( 'Product');
                        $name_code = $product['Product']['name'].' #'.(empty ( $product['Product']['product_code'])?$product['Product']['id']: $product['Product']['product_code']) ;
						if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Amount not sufficient for %s', true) , $name_code )]);
                        $this->flashMessage(sprintf(__('Amount not sufficient for %s', true) , $name_code ), 'Errormessage', 'secondaryMessage');
                        $this->redirect(Router::url(array('action' => 'view', $stock['StockTransaction']['product_id'], '#' => 'StockTransactionsBlock'))); die ;
                    }

                    //We will check the bundle components for availablilty 
                    if(
                        $this->data['StockTransaction']['fetch_from_raw']
                        &&settings::getValue(InventoryPlugin, 'enable_bundles')
                        &&$product['Product']['type'] == Product::BUNDLE_TYPE
                        )
                    {
                        if(!GetObjectOrLoadModel('StockTransaction')->checkBalanceOpenBundles( $stock['StockTransaction']['product_id'] ,$stock['StockTransaction']['store_id'], $this->data['StockTransaction']['quantity'] ,$stock['StockTransaction']['quantity'], ($this->data['StockTransaction']['transaction_type'] == StockTransaction::TRANSACTION_IN?'deduct':'add'))){
                            $this->flashMessage(__('Components amounts are not sufficient.', true) , 'Errormessage', 'secondaryMessage');
                            // $store_balance=false;
                            $this->redirect(Router::url(array('action' => 'view', $stock['StockTransaction']['product_id'], '#' => 'StockTransactionsBlock'))); die ;
                        }
                    }
                }
                $product = $stock;
				if (!empty($stock['StockTransaction']['transaction_category_id']) && empty($this->data['StockTransaction']['transaction_category_id'])) {
					$this->TransactionCategory->removeIfNotUsed($stock['StockTransaction']['transaction_category_id']);
				}
				$this->data['StockTransaction']['transaction_category_id'] = $data['transaction_category_id'] = $this->TransactionCategory->findOrCreate($this->data['StockTransaction']['transaction_category_id'][0] );


				$this->StockTransaction->set( $this->data['StockTransaction'] );
				$valid = $this->StockTransaction->validates();
				if($valid){
					StockTransaction::saveTransaction($this->data['StockTransaction'], array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));
					// This Updates The Old store_stock_balance with the new balance so the product listing/view can show correct stock numbers
					StockTransaction::updateProductBalance($product['Product']['id'] , $stock['StockTransaction']['store_id']);
					if(IS_REST){
						$this->render('success');
						return;
					}
                    $attachments = $this->data['StockTransaction']['attachment'];
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                    
                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save('stock_transaction', $this->StockTransaction->id, $imagesIds); 
                    }
					if (empty($this->params['url']['box'])) {
						$this->flashMessage(sprintf(__('Stock Transaction has been saved', true), __('Product', true)), 'Sucmessage');
						$this->redirect(Router::url(array('action' => 'view', $stock['StockTransaction']['product_id'], '#' => 'StockTransactionsBlock')));
					} else {
                        $this->flashMessage(sprintf(__('Stock Transaction has been saved', true), __('Product', true)), 'Sucmessage');
					}

				}else{
                    if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->StockTransaction->validationErrors]);
					if (empty($this->params['url']['box'])) {
						$this->flashMessage(__('Manual Stock Adjustment couldn\'t be saved', true));
					} else {
						$this->flashMessage(sprintf(__('Manual Stock Adjustment couldn\'t be saved', true)));
					}
				}
        } else {
            $this->data = $stock;
            $this->data['StockTransaction']['fetch_from_raw'] = (!empty($this->data['StockTransaction']['fetch_from_raw'])?true:false) ;
            if ($this->data['StockTransaction']['quantity'] < 0) {
                $this->data['StockTransaction']['quantity'] = $this->data['StockTransaction']['quantity'] * -1;
            }
        }

        if(!empty($this->data['StockTransaction']['attachment'])){                       
            $filesId = explode(',',$this->data['StockTransaction']['attachment']);
            $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
            $this->data['Attachments'] = $attachment;
        }

        $transaction_categories = $this->TransactionCategory->find ('list' );
        $stock['StockTransaction']['transaction_category_id'] = $transaction_categories[$stock['StockTransaction']['transaction_category_id']];
        foreach ( $transaction_categories as $k => $v ) {
                $transaction_categories[$v] = $v ;
                unset ( $transaction_categories[$k] );
        }


        if ( ifPluginActive ( AccountingPlugin))
        {
            $this->loadModel('JournalAccount');
            $this->loadModel('JournalCat');
            $this->JournalAccount->recursive = -1;
            $accounts = $this->JournalAccount->find('all');
            $catsList = $this->JournalCat->find('list');
            $this->set ( compact ('accounts' , 'catsList' ) );
        }
		//print_r ( $stock ) ;
		//die ( $stock );
	$this->set ('transaction_category_ids' , $transaction_categories );
        $this->set('id', $id);
        $this->set ( 'is_edit' , true);
        $this->set ( 'product_id' , $stock['StockTransaction']['product_id']);
		$this->set ( 'store_list' , $store_list);//$this->Store->get_store_list());
                $pp = [$stock['Product']] ;
        $this->Product->add_multi_units( $pp ) ;
        $stock['Product'] = $pp[0];
        debug ( $stock ) ;
        $this->set('product', $stock);
        $this->set('product_stock', StockTransaction::getProductStock($stock['Product']['id']));
        $this->set('this_stock', $stock['StockTransaction']['quantity']);
        $this->set_service_product_settings();
        $unit_factors = []  ;
        foreach ( $stock['Product']['unit_factors'] as $f   )
        {
            $unit_factors[$f['id']] = $f['factor_name'];
        }
        debug ( $unit_factors);
        $this->set('primary_store', $this->Store->getPrimaryStore() );
        $this->set('unit_factors',$unit_factors ) ;
        $this->set('enable_multi_units' , $enable_multi_units);
        if ($stock['StockTransaction']['source_type'] == StockTransaction::SOURCE_MANUAL) {
            $this->render('owner_manual_stock_adjust');
        }


    }

    function owner_reload_transactions() {

    }

    function owner_view($id = null) {
        $this->Product->bindAttachmentRelation('product');

        $is_ajax=$this->RequestHandler->isAjax();
        if(!empty($_GET['is_ajax']))
            $is_ajax=true;
        $this->set('is_ajax' , $is_ajax);
//		die('2222');
		$conditions = ['Product.id' => $id];
		$site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission([View_All_Products,View_his_own_Products])) {
				if(IS_REST) $this->cakeError('error403');
				$this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
            if (!check_permission(View_All_Products) && check_permission(View_his_own_Products)) {
                $conditions['Product.staff_id'] = $site['staff_id'];
            }
        }
		App::import('Vendor', 'notification_2');
        if(ifPluginActive(ProductTracking))
        {
            $this->set('trackingTypes', TrackStockUtil::getTrackingTypes());
        }
        $this->loadModel('ItemsCategory');
        $this->Product->bindModel(
            array('hasAndBelongsToMany' =>
                [ 'Category' =>
                    [
                        'className' => 'Category',
                        'joinTable' => 'items_categories',
                        'foreignKey' => 'item_id',
                        'associationForeignKey' => 'category_id',
                        'conditions' => [ 'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT, 'category_type' => Category::CATEGORY_TYPE_PRODUCT]
                    ]
                ]
            )
        );

        // Load Attachmnets . 
        $this->Product->bindModel(
            array(
                'hasAndBelongsToMany' =>
                [
                    'Attachmnets' =>
                    [
                        'className' => 'FileModel',
                        'joinTable' => 'entity_attachments',
                        'foreignKey' => 'entity_id',
                        'associationForeignKey' => 'file_id',
                        'conditions'  => ['EntityAttachment.entity_key = "product" and EntityAttachment.deleted_at IS NULL']
                    ]
                ]
            )
        );

        $product = $this->Product->find($conditions);
        
        // Make s3 images as default.  
        $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product',$product['Product']['id']);
        if(count( $defaultS3Images))
        {
            $product['defaultImage']['id'] = $defaultS3Images[0]->file_id;
            $product['defaultImage']['path'] = $defaultS3Images[0]->files->path;
            $product['defaultImage']['is_s3'] = true;
         }elseif (is_countable($product['ProductMasterImage'])) {
            $product['defaultImage']['id'] =  $product['ProductMasterImage']['id'];
            $product['defaultImage']['path'] = $product['ProductMasterImage']['file_full_path'];
            $product['defaultImage']['is_s3'] = false;
        } 

        if (!$product) {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Product', true))));
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        $product['ProductCategory'] = $product['Category'];
        if (IS_REST) {
            foreach ($product['ProductCategory'] as &$productCategory) {
                if (isset($productCategory['ItemsCategory'])) {
                    unset($productCategory['ItemsCategory']);
                }
            }
        }

        if (ifPluginActive(BranchesPlugin)) {
            $current_branch_id = getCurrentBranchID();
            $product_branch_id = $product['Product']['branch_id'];
            if($current_branch_id != $product_branch_id && !isModelSharable('Product')){
                if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('%s not found in the current branch.', true), __('Product', true))));
                $this->flashMessage(sprintf(__('%s not found in the current branch.', true), __('Product', true)));
                $this->redirect($this->referer(array('action' => 'index')));
            }
        }

        $bundles = [] ;
        if ( $product['Product']['type'] == settings::OPTION_SOLD_TYPE_BUNDLES)
        {
            $this->loadModel('ProductBundle');
            $joins = [['table' => 'products' , 'alias' => 'Product' , 'type' => 'LEFT',
                    'conditions' => array(
                        'Product.id = ProductBundle.product_id',
                    )  ]];
            $bundles = $this->ProductBundle->find('all' ,['fields'=> '*', 'joins' => $joins ,  'conditions' => ['bundle_product_id'=>$id] ] );
        }
        $this->set ( compact('bundles'));
        $pp = [$product['Product']] ;
        $this->Product->add_multi_units( $pp ,true ) ;
        $product['Product'] = $pp[0];
        


        if(array_key_exists('update', $this->params['url'])
            && $this->params['url']['update']=="true"){
        $this->flashMessage(sprintf(__('%s has been saved', true), __('Product', true)), 'Sucmessage');
        }

        if (ifPluginActive(CREDIT_PLUGIN) && $product['Product']['source_type'] == "package" && !is_null($product['Product']['source_id'])) {
            $this->loadModel('Package');
            $package = $this->Package->find('first', ['conditions' => ['Package.id' => $product['Product']['source_id']]]);
            $this->set('package', $package);
        }

        $this->set('product', $product);
        $this->set('bundle_final_cost', $this->Product->get_bundle_final_cost($id));



        if (!empty($product['Product']['track_stock']) && !empty($this->params['url']['noti']))
            NotificationV2::view_notificationbyref($id, NotificationV2::Product_Low_Stock);





		$this->set('products_nav',$this->setup_nav_view($id));
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Product', array('primary_id' => $id));
        $this->loadModel('ActionLine');
        $action_list1 = $timeline->getProductActionsList();
        $action_list2 = $timeline->getStockActionsList();
        $action_list = array_merge($action_list1, $action_list2);

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $product_actions[$key] = $action;
            }
        }
        $showActionLineTap= $this->ActionLine->find('first', array('recursive'=> -1, 'conditions' => ['secondary_id'=>$id])) ?1:0;
        $this->set('showActionLineTap', $showActionLineTap);
        $this->set('actions', $product_actions);
        $this->loadModel ( 'ItemPermission' );
        App::import('vendor','WarehouseService' , ['file' => 'WarehouseService.php']);
        $ws = new WarehouseService();
        $productStock = $ws->getStaffProductStock(ItemPermission::PERMISSION_VIEW, $product['Product']['id'], getAuthOwner('staff_id'),null,true);
        $stores_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE , ItemPermission::PERMISSION_VIEW,null,false,true);
        $showProductPendingQTY = settings::getValue(InventoryPlugin, SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS);
        $this->set('showProductPendingQTY', false);
        if ($showProductPendingQTY) {
            $this->set('showProductPendingQTY', true);
            $pendingQTY = $this->getProductPendingQuantity($ws, $product['Product']['id']);
            $this->set('productPendingQTY', $pendingQTY);
            $this->set('productAvailableQTY', $this->combineProductStock($productStock, $pendingQTY));
            if (IS_REST) {
                $product["Product"]["productPendingQTY"] = $pendingQTY;
                $product["Product"]["productAvailableQTY"] = $this->combineProductStock($productStock, $pendingQTY);
            }
        }
        $this->set('stock_level', array_sum($productStock) );
        $this->set('productStock', $productStock);

        $this->set('sold_7', $this->Product->getSoldQuantity($id, date('Y-m-d 00:00:00', strtotime('-7 Days')), date('Y-m-d 23:59:59', strtotime('-1 Days'))));
        $this->set('sold_7_before', $this->Product->getSoldQuantity($id, date('Y-m-d 00:00:00', strtotime('-14 Days')), date('Y-m-d 23:59:59', strtotime('-8 Days'))));


        $this->set('sold_28', $this->Product->getSoldQuantity($id, date('Y-m-d 00:00:00', strtotime('-28 Days')), date('Y-m-d 23:59:59', strtotime('-1 Days'))));
        $this->set('sold_28_before', $this->Product->getSoldQuantity($id, date('Y-m-d 00:00:00', strtotime('-56 Days')), date('Y-m-d 23:59:59', strtotime('-29 Days'))));


        $this->set('sold_all', $this->Product->getSoldQuantity($id));
        $this->set('title_for_layout',  h($product['Product']['name'] . ' - ' . __('Products & Services', true)));
        $this->set_service_product_settings();




//        $this->loadModel ( 'Store' );
//        $stores_list = $this->Store->get_store_list();

//        unset($stores_list[1]);
//        dd($stores_list);
        $this->set ( 'store_list' , $stores_list );
        $this->loadModel ( 'StoreStockBalance');
        if ( count ($stores_list ) > 1){
            $this->StoreStockBalance->recursive = 1;
            $ssb  = $this->StoreStockBalance->find('all' , ['conditions' => ['product_id' => $id , 'Store.active' => 1, 'Store.id' => array_keys($stores_list)]] );
            $this->set ( 'different_stores' , $ssb);
        }
        $this->set ( 'warehouses' , $this->Store->get_store_list_transactions($id));
        $default_currency = $this->Product->get_default_currency ( ) ;
        $this->set ( 'default_currency' , $default_currency) ;


		$this->loadModel('ItemsTag');
		$this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_PRODUCT);
		$tags = $this->ItemsTag->get_item_tags($id,ItemsTag::TAG_ITEM_TYPE_PRODUCT,true);
	    if (is_array($tags)) {
            $product['Product']['tags']= implode(',',$tags) ;
        }
		$this->set('tags',$tags);


		//get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('product', false);

        $printableTemplates = $this->getActivePrintableTemplates($printableTemplates);

        $printableTemplate_barcode = $this->PrintableTemplate->getPrintableTemplatesList('product_item_barcode');

        //
        $this->loadModel('ItemBarcode');
        $barcodeCount = $this->ItemBarcode->find('count', ['conditions' => ['product_id' => $id]]);

        $show_buy_price = ( check_permission(Proudcts_Add_New_Proudct)||check_permission(Edit_Delete_all_Products)||check_permission(Add_New_Purchase_Orders)||check_permission(Edit_Delete_All_Purchase_Orders));
        $this->set ( 'show_buy_price' , $show_buy_price);

        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
        $factor = NULL ;
        $purchaseFactor = null;
        $sellFactor = null;
        if ( $enable_multi_units && !empty($product['Product']['unit_template_id']) )
        {
            $factor = $this->Product->get_default_factor($product);
            $unit_factors = $product['Product']['unit_factors'];
            $purchaseFactor = $product['Product']['unit_factors'][array_search($product['Product']['default_buy_factor_id'], array_column($unit_factors, 'id'))];
            $sellFactor = $product['Product']['unit_factors'][array_search($product['Product']['default_retail_factor_id'], array_column($unit_factors, 'id'))];
        }
        $this->set('factor' ,$factor);
        $this->set('purchaseFactor', $purchaseFactor);
        $this->set('sellFactor', $sellFactor);
        $this->set('enable_multi_units' ,$enable_multi_units );

        $this->loadModel('StockTransaction');
        $stock_sources = $this->StockTransaction->getSources() ;
        $this->set('sources',$stock_sources );

        $this->loadModel('TransactionCategory');
        $categories = $this->TransactionCategory->getProductCategories($id) ;
        foreach ( $categories as $k => $c ) {
            $stock_sources['cat_'.$k] = $c ;
        }
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions');
        $enable_requisitions_po = settings::getValue(InventoryPlugin, 'enable_requisitions_po');

        $enable_requisitions=$enable_requisitions_po||$enable_requisitions;

        if (ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, 'enable_multi_units') && $product['Product']['type'] != Product::SERVICE_TYPE) {
            $this->__init_form_data($id);
            $this->loadModel ('ItemBarcode');
            $itemBacode = $this->ItemBarcode->findAllByProduct_id($product['Product']['id']);
            $itemBacode = $itemBacode ?: [];
            $item_barcodes = array_column($itemBacode, 'ItemBarcode');

            foreach ($item_barcodes as $index => $item_barcode) {
                $item_barcodes[$index]['unit_factor'] = $this->viewVars['factors_list'][$product["Product"]["unit_template_id"]][$item_barcode['unit_factor_id']]["factor_name"];
                if (! is_null($item_barcode['unit_price'])) {
                    $item_barcodes[$index]['unit_price'] = round($item_barcode['unit_price'] * $this->viewVars['factors_list'][$product['Product']['unit_template_id']][$item_barcode['unit_factor_id']]['factor'], 6);
                }
            }
            $this->set('item_barcodes', $item_barcodes);
        }
        if ( $enable_requisitions ){
            $this->loadModel('Requisition');
        }
        $this->set('enable_requisitions',$enable_requisitions);
        $this->set('stock_sources' ,$stock_sources);
        $this->set('barcode_count', $barcodeCount);
        $this->set('printableTemplate_barcode', $printableTemplate_barcode);

        $this->set('has_templates', false);
        if(!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

		if(IS_REST){
            $this->set('rest_item', $this->prepareProductDataForApi($product));
			$this->set('rest_model_name', "Product");
			$this->render("view");
		}

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('product');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);

        $appEntitiesFormHandler = $this->createAppEntitiesFormHandlerInstance();
        $this->set('forms', $appEntitiesFormHandler->show($id));

    }
    function owner_delete_transfer_stock ( $id ) {
        if (!check_permission(Adjust_Product_Inventory)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $this->loadModel ( 'StockTransfer') ;
        //Save default branch find setting
        $defaultOnFind = $this->StockTransfer->applyBranch['onFind'];
        $this->StockTransfer->applyBranch['onFind'] = false;
        $transfer = $this->StockTransfer->findById ($id) ;
        $this->validate_open_day($transfer['StockTransfer']['transfer_date'], ['action' => 'index']);

        $this->StockTransfer->delete_stock_transfer ( $id ) ;
        //Restore default branch find setting
        $this->StockTransfer->applyBranch['onFind'] = $defaultOnFind;
        die ();
    }
    function owner_edit_transfer_stock ( $id ) {
        if (!check_permission(Adjust_Product_Inventory)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
        $this->loadModel ( 'Store') ;
        $this->loadModel ( 'StockTransaction') ;
        $this->loadModel ( 'StockTransfer') ;
        $this->StockTransfer->applyBranch['onFind'] = false;
        $transfer = $this->StockTransfer->findById ($id) ;
        $this->validate_open_day($transfer['StockTransfer']['transfer_date'], ['action' => 'index']);
        if ( !empty ( $transfer ) ) {
            if ( !empty ( $this->data ) ) {
                if(!empty($this->data['Product']['transfer_date']))
                {
                    $this->validateOpenDayWithValidationError($this->data,'Product', 'transfer_date');
                }
                if(empty($this->Product->validationErrors))
                {
                    $transfer_id = $this->Store->transfer_stock ( $transfer['StockTransfer']['product_id'] , $this->data['Product']['from_store_id'] ,$this->data['Product']['to_store_id'] , $this->data['Product']['quantity'] ,  $this->data['Product']['transfer_date'] ,$this->data['Product']['notes'] , $id );
                }else{
                    $transfer_id = -100;
                }
                 if ( $transfer_id > 0  ) {
                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Product', true)), 'Sucmessage');
                    $this->redirect('/owner/products/view/'.$transfer['StockTransfer']['product_id']);
                  }else if ( $transfer_id == -1 ){
                      $this->flashMessage(sprintf(__("Can't find store", true), __('Product', true)), 'Errormessage', 'secondaryMessage');

                  }else if ( $transfer_id == -2 ){
                      $this->flashMessage(sprintf(__("Store doesn't have the desired amount", true), __('Product', true)), 'Errormessage', 'secondaryMessage');
                  }else {
                      $this->flashMessage(sprintf(__("Didn't save transfer", true), __('Product', true)), 'Errormessage', 'secondaryMessage');
                  }
            }

        }
        if ( empty ( $transfer['StockTransfer']['transfer_date'] ) ) {
            $add_transaction = $this->StockTransaction->findById ($transfer['StockTransfer']['add_transaction_id']) ;
            $transfer['StockTransfer']['transfer_date'] = format_datetime ( $add_transaction ['StockTransaction']['received_date'] ) ;
        }
        $this->data['Product'] = $transfer['StockTransfer'];
        $this->set_transfer_data ($transfer['StockTransfer']['product_id']);
        $this->set ( 'action' ,'/edit_transfer_stock/' );
        $this->set ( 'transfer_quantity' ,$transfer['StockTransfer']['quantity'] );
        $this->render ('owner_transfer') ;
    }
    private function set_transfer_data ($product_id) {
        $this->loadModel ( 'Store' ) ;
        $this->loadModel ( 'StoreStockBalance' ) ;
        $this->loadModel('ItemPermission');
        $store_list= $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING);
        $from_store_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING);
//        die(debug($from_store_list));
        $this->set ( 'json_store_balance' , json_encode ($this->StoreStockBalance->find ('list' , ['fields'=> "store_id,balance" ,  'conditions' => ['product_id' => $product_id]] )) );
        $this->set ( 'store_list' ,$store_list);
        $this->set ( 'from_store_list' ,$from_store_list);
        $this->set ( 'product_id' ,$product_id);
        $product = $this->Product->find ( 'first' , ['recursive'=> -1 , 'conditions' => ['Product.id' => $product_id]]);
        $this->set ( 'product' ,$product);
    }
    function owner_transfer_stock ( $product_id = null ) {
        $product = $this->Product->find ( 'first' , ['recursive'=> -1 , 'conditions' => ['Product.id' => $product_id]]);
        if (empty ( $product ) || !check_permission(TRANSFER_STOCK_PERMISSION ) ){
            $this->flashMessage(__("You are not allowed to view this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'view' , $product_id));
        }
        if ($product['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY){
            if(IS_REST) $this->cakeError('error403');
            $requisitions_url = Router::url(['controller' => 'requisitions','action' => 'index']);
            $string = __("Manage Requisitions", true);
            $req_html = "<a href=\"$requisitions_url\">$string</a>";
            $this->flashMessage(sprintf(__("You can use this option using Requisitions %s Link", true), $req_html), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if ($product['Product']['status'] != ProductStatusUtil::STATUS_ACTIVE) {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("You cannot create stock transactions for %s products", true), __(ProductStatusUtil::getStatusName($product['Product']['status']), true))));
            $this->flashMessage(sprintf(__("You cannot create stock transactions for %s products", true), __(ProductStatusUtil::getStatusName($product['Product']['status']),true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        $this->loadModel ( 'Store');
        $this->set_transfer_data ($product_id);
        if ( !empty ($this->data ) )
        {
            if(!empty($this->data['Product']['transfer_date']))
            {
                $this->validateOpenDayWithValidationError($this->data,'Product', 'transfer_date');
            }
            if(empty($this->Product->validationErrors))
            {
                $transfer_id = $this->Store->transfer_stock ( $product_id , $this->data['Product']['from_store_id'] ,$this->data['Product']['to_store_id'] , $this->data['Product']['quantity'] ,  $this->data['Product']['transfer_date'] ,$this->data['Product']['notes'] );
            }else{
                $transfer_id = -100;
            }

            if ( $transfer_id > 0  ) {
              $this->flashMessage(sprintf(__('%s  has been saved', true), __('Product', true)), 'Sucmessage');
              $this->redirect('/owner/products/view/'.$product_id);
            }else if ( $transfer_id == -1 ){
                $this->flashMessage(sprintf(__("Can't find store", true), __('Product', true)), 'Errormessage', 'secondaryMessage');

            }else if ( $transfer_id == -2 ){
                $this->flashMessage(sprintf(__("Store doesn't have the desired amount", true), __('Product', true)), 'Errormessage', 'secondaryMessage');
            }else if ( $transfer_id == -3 ){
                $this->Product->validationErrors['from_store_id']=__('You cant transfer to the same store',true);
                $this->Product->validationErrors['to_store_id']=__('You cant transfer to the same store',true);
                $this->flashMessage(sprintf(__("You cant transfer to the same store", true), __('Product', true)), 'Errormessage', 'secondaryMessage');
            }else if ( $transfer_id == -5 ){
            $this->flashMessage( __t('You cannot add the transaction through a suspended warehouse'), 'Errormessage', 'secondaryMessage');
            } else {
                $this->flashMessage(sprintf(__("Didn't save transfer", true), __('Product', true)), 'Errormessage', 'secondaryMessage');
            }

        }
         $this->set ( 'action' ,'/transfer_stock/'.$product_id );
        $this->render ( 'owner_transfer');
    }
    //function owner_get

    private function validate_bar_code($new_barcodes)
    {
        $this->loadModel ('ItemBarcode');
        $product_count = $this->Product->find('count', ['conditions' => ['Product.barcode IN (' . implode(",", $new_barcodes) . ')']]);
        $barcodes_status = $product_count ? false : true;
        if ($barcodes_status) {

            $product_conditions=[];

            if (ifPluginActive(BranchesPlugin)  && empty(settings::getValue(BranchesPlugin, 'share_products')) ) {
                $product_conditions=['Product.branch_id' => getCurrentBranchID()];
            }
            $belongsTo = array(
                'Product' => array('className' => 'Product', 'foreignKey' => 'product_id', 'dependant' => false, 'conditions' => $product_conditions)
            );
            $this->ItemBarcode->bindModel(array('belongsTo' => $belongsTo), false);
            $quotedBarcodes = array_map(fn($barcode) => "'" . addslashes($barcode) . "'", $new_barcodes);
            $barcodes = $this->ItemBarcode->find('all', ['conditions' => ['ItemBarcode.barcode IN (' . implode(",", $quotedBarcodes) . ')']]);
            foreach ($barcodes as $barcode) {
                if (isset($barcode['Product']['id'])) {
                    $barcodes_status = false;
                    break;
                }
            }
        }

        return $barcodes_status;
    }

    private function  getRecordWithEntityStructureWithCustomFields($entityKey, $id, $level = 1)
    {

        $data = getRecordWithEntityStructure($entityKey, $id, $level)->toArray();
    
        // To track changes in files . 
        $attachmentsIds = izam_resolve(AttachmentsService::class)->getAttachmentIds('product', $data['id']);
        $data['Files'] = $this->getS3FilesDetailsForActivityLog($attachmentsIds);
       
        $customForm = GetObjectOrLoadModel('CustomForm');


        $result = $customForm->get_display_form_fields($entityKey, $id);
        foreach ($result['fields'] as $field) {
            $data[$field['CustomFormField']['label']] =$customForm->getFieldValue($field['CustomFormField']['real_value'],$field['CustomFormField'])  ;
        }
        $this->loadModel('ItemsTag');
        $tags =   array_values($this->ItemsTag->get_items_tags($id, ItemsTag::TAG_ITEM_TYPE_PRODUCT));
        $data['tags'] = implode(',', $tags[0]??[]);
        return $data;
    }


    function owner_edit($id = null) {
        $this->Product->bindAttachmentRelation('product');

        if (!check_permission( [PermissionUtil::Edit_Delete_all_Products, PermissionUtil::Edit_And_delete_his_own_added_Products])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $appEntitiesFormHandler = $this->createAppEntitiesFormHandlerInstance();
    	set_time_limit(999);
    	ini_set('memory_limit', '4G');
		$this->loadModel ('StockTransaction');
        App::import('Vendor', 'AutoNumber');
        $this->__init_form_data($id);
        $formError=false;
        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');
        $product = $this->Product->find(array('Product.id' => $id));

        if (ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, 'enable_multi_units') && $product['Product']['type'] != Product::SERVICE_TYPE) {
            $this->loadModel ('ItemBarcode');
            $itemBacode = $this->ItemBarcode->findAllByProduct_id($product['Product']['id']);
            $itemBacode = $itemBacode ?: [];
            $item_barcodes = array_column($itemBacode, 'ItemBarcode');
            foreach ($item_barcodes as $index => $item_barcode) {
                if (! is_null($item_barcode['unit_price'])) {
                    $item_barcodes[$index]['unit_price'] = round($item_barcode['unit_price'] * $this->viewVars['factors_list'][$product['Product']['unit_template_id']][$item_barcode['unit_factor_id']]['factor'], 6);
                }
            }
        }
        if (!$product) {
			if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Product', true))));
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        if (ifPluginActive(BranchesPlugin)) {
            $current_branch_id = getCurrentBranchID();
            $product_branch_id = $product['Product']['branch_id'];
            if($current_branch_id != $product_branch_id && !isModelSharable('Product')){
                if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('%s not found in the current branch.', true), __('Product', true))));
                $this->flashMessage(sprintf(__('%s not found in the current branch.', true), __('Product', true)));
                $this->redirect($this->referer(array('action' => 'index')));
            }
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if (( (!check_permission(Edit_Delete_all_Products) && $product['Product']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_Products) ) && !($product['Product']['source_type'] == "package" && check_permission(MANAGE_PACKAGES) )  ) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to edit this product ", true), 'Errormessage', 'secondaryMessage');
                $this->redirect($this->referer());
            }
        }
		if(IS_REST) $this->data['Product']['id'] = $id;

		$disable_track_stock = true;
        if ($product['Product']['type'] == Product::PRODUCT_TYPE || $product['Product']['type'] == Product::BUNDLE_TYPE) {
            $this->loadModel('StockTransaction');
            $this->loadModel('RequisitionItem');
            $stock_transactions = $this->StockTransaction->find('first', ['conditions' => ['StockTransaction.product_id' => $id, 'StockTransaction.status' => StockTransaction::STATUS_PROCESSED], 'recursive' => -1]);
            $requisition_items = $this->RequisitionItem->find('first', ['conditions' => ['RequisitionItem.product_id' => $id, ], 'recursive' => -1]);
            if (empty($stock_transactions) && empty($requisition_items)) {
                $disable_track_stock = false;
            }
        }
        $this->set('disable_track_stock', $disable_track_stock);
        $belongsToItemGroup=false;
        $itemGroupAttributes=null;
        if (isset($product['Product']['item_group_id']) && isset($product['ProductAttributeOption']) && count($product['ProductAttributeOption']) ){
            $belongsToItemGroup=true;
            $this->loadModel( 'ItemGroupAttribute');
            $itemGroupAttributes = $this->ItemGroupAttribute->find('all', ['conditions' => ['ItemGroupAttribute.item_group_id' => $product['Product']['item_group_id']]] );
            foreach ($itemGroupAttributes as $itemGroupAttribute){
                foreach ($product['ProductAttributeOption'] as &$productAttribute){
                    if ($productAttribute['attribute'] == $itemGroupAttribute['ItemGroupAttribute']['name']){
                        $productAttribute['other_options'] =$itemGroupAttribute['ItemGroupAttributeOption'];
                    }
                }
            }
        }
        if (!empty($this->data)) {
            if($this->data['Product']['bundle_type'] != null && $product['Product']['bundle_type'] !== $this->data['Product']['bundle_type'] && $disable_track_stock && $product['Product']['bundle_type'] != null){
                if(IS_REST) $this->cakeError('error400', array('message' => __("You cannot change the bundle type as there’s a related stock transactions.", true)));
                $this->flashMessage(__("You cannot change the bundle type as there’s a related stock transactions.", true), 'Errormessage', 'secondaryMessage');
                $this->redirect($this->referer());
            }
            if (Product::BUNDLE_TYPE == $this->data['Product']['type'] && !empty($this->data['ProductBundle'])){
                foreach ($this->data['ProductBundle'] as $productInBundle){
                    if ($productInBundle['product_id'][0] == $id){
                        if(IS_REST) $this->cakeError('error400', array('message' => __("You cannot select a bundled product from within the same bundle.", true)));
                        $this->flashMessage(__("You cannot select a bundled product from within the same bundle.", true), 'Errormessage', 'secondaryMessage');
                        $this->redirect($this->referer());
                    }
                }
            }
            if(IS_REST){
                $this->loadModel('ProductBundle');
                $items = $this->ProductBundle->find('all',['conditions'=>['ProductBundle.bundle_product_id'=>$id]]);
                foreach($items as $item){
                    $this->data['ProductBundle'][] = $item['ProductBundle'];
                }
            }
            if ($belongsToItemGroup && count($itemGroupAttributes)){
                $this->loadModel('ProductAttributeOption');
                $this->loadModel('ItemGroupAttributeOption');
                $result = $this->validateProductAttributeOptions($this->data['ProductAttributeOption'],$this->data['Product']['item_group_id']);
                if ($result && $result['products']['id'] != $this->data['Product']['id']){
                    $this->flashMessage( sprintf(__t("The attribute options for the product named '#%s' have already been selected and cannot be repeated") ,'<a href="/owner/products/view/'. $result['products']['id'] .'"'. '>'.$result['products']['name'] .' '.$result['products']['product_code'].'</a>'), 'Errormessage', 'secondaryMessage');
                    $this->redirect($this->referer(array('action' => 'edit',$this->data['Product']['id']), true));
                }
                foreach ($this->data['ProductAttributeOption'] as $option){
                    $this->ProductAttributeOption->update($option['id'], ['ProductAttributeOption'=>$option ]);
                    $this->createItemGroupAttributeOption($itemGroupAttributes , $option);

                }
            }

            //suspend backword compatibility
            $this->data['Product']['deactivate'] = 0;

            if (in_array($this->data['Product']['status'], [ProductStatusUtil::STATUS_INACTIVE, ProductStatusUtil::STATUS_SUSPENDED])) {
                $this->data['Product']['deactivate'] = $this->data['Product']['status'];

                if ($this->Product->isProductLinkedToShippingOptions($this->data['Product']['id'])) {
                    $message = __('You cannot change the status to inactive or suspended for the service that has already been selected in the <a href="/v2/owner/shipping_options/settings">cash on delivery fee settings</a>.', true);
                    $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
                    $this->redirect( array('action' => 'edit', $this->data['Product']['id'] ));
                    return;
                }
                try {
                    $this->Product->isDefaultServiceInBooking($this->data['Product']['id']);
                } catch (Exception $e) {
                    $this->flashMessage($e->getMessage());
                    $this->redirect($this->referer(array('action' => 'index')));
                }

            }
            foreach ($this->data['priceList'] as $priceListValue) {
                if ($priceListValue <= 0) {
                    $this->flashMessage(__("The number of price list must not be zero.", true), 'Errormessage', 'secondaryMessage');
                    $formError=true;
                }
            }
            $oldData = $this->getRecordWithEntityStructureWithCustomFields(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY, $id, 2);

            $bundle_valid = true;
            $bundleValidationErrorMessage = "You cannot choose product with type serial number , Lot number or expiry date in the bundle";
            if ($this->data['Product']['type'] == Product::BUNDLE_TYPE) {
                foreach ($this->data['ProductBundle'] as $key => $productBundle) {
                    $this->Product = GetObjectOrLoadModel('Product');
                    $bundleProduct = $this->Product->findById($productBundle['product_id'][0]);
                    if(!$bundleProduct){
                        $this->Product->validationErrors['ProductBundle'][$key]['bundle_product_id'] = __("This item doesn't exist or exist in another branch", true);
                        $formError=true;
                    }else{
                        if ($bundleProduct['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY) {
                            $this->flashMessage(__($bundleValidationErrorMessage, true), 'Errormessage', 'secondaryMessage');
                            $bundle_valid = false;
                        }
                        if ($bundleProduct['Product']['source_type'] == "package") {
                            if(IS_REST) {
                                $this->cakeError('error403');
                            }
                            $bundle_valid = false;
                            $bundleValidationErrorMessage = "You cannot select the service with type package in the bundle";
                        }
                        if (!$bundle_valid) {
                            break;
                        }
                    }
                }
            }

            \AutoNumber::set_validate(\AutoNumber::TYPE_PRODUCT);
 
	        if ($this->viewVars['enable_multi_units'] && !empty ($this->data['Product']['default_buy_factor_id'])) {
                $den = (float)$this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$this->data['Product']['default_buy_factor_id']]['factor']; 
		        $this->data['Product']['buy_price'] = round($den != 0 ? (float)$this->data['Product']['buy_price'] / $den : 0.0, 8);
	        }
	        if ($this->viewVars['enable_multi_units'] && !empty ($this->data['Product']['default_retail_factor_id'])) {
                $den = (float)$this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$this->data['Product']['default_retail_factor_id']]['factor'];
		        $this->data['Product']['unit_price'] = round($den != 0 ? (float)$this->data['Product']['unit_price'] / $den : 0.0, 8);
	        }
            $this->loadModel ( 'StockTransaction' ) ;
            $has_transactions = $this->StockTransaction->find('first', ['conditions' => ['StockTransaction.product_id' => $id]]);
            // If Product is Bundle and has no transactions then edit it's average_price by it's buy_price
            if(!$has_transactions){
                $this->data['Product']['average_price'] = $this->data['Product']['buy_price'];
            }
            if ( empty ( $product['Product']['average_price']))
            {
                $this->data['Product']['average_price'] = $this->data['Product']['buy_price'] ;
            }

            if ( empty ( $product['Product']['buy_price']) && !empty ( $this->data['Product']['buy_price']) )
            {
                $this->StockTransaction->updateAll  (['purchase_price' =>$this->data['Product']['buy_price']] , ['purchase_price' => 0 , 'product_id' => $id] ) ;
            }
            $this->data['Product']['id'] = $id;
            $this->data['Product']['site_id'] = $site_id;

            if (is_array($this->data['Product']['category']))
                $this->data['Product']['category'] = $this->data['Product']['category'][0];
            if (empty($this->data['Product']['category']))
                $this->data['Product']['category'] = "";
            if (is_array($this->data['Product']['brand']))
                $this->data['Product']['brand'] = $this->data['Product']['brand'][0];
            if (empty($this->data['Product']['brand']) && !IS_REST)
                $this->data['Product']['brand'] = "";
            if (is_array($this->data['Product']['tags']))
                $this->data['Product']['tags'] = implode(",", $this->data['Product']['tags']);
            if (empty($this->data['Product']['tags']))
                $this->data['Product']['tags'] = "";

            if (!empty($this->data['Product']['brand'])){
                $this->loadModel('Brand');
                $this->data['Product']['brand_id'] = $this->Brand->findOrCreate($this->data['Product']['brand']);
            }

            if(IS_REST && !empty($this->data['Product']['tags'])){
                $this->data['ItemsTag']['tags'] = explode(",", $this->data['Product']['tags']);
            }
			App::import('Vendor', 'notification_2');
            if ((float)$product['Product']['stock_balance'] <= (float)$this->data['Product']['low_stock_thershold'] && !empty($this->data['Product']['track_stock'])) {
                $more = array('param1' => $product['Product']['stock_balance']);

                NotificationV2::add_notification(NotificationV2::Product_Low_Stock, $id, [getAuthOwner('staff_id')],NotificationV2::NOTI_USER_TYPE_STAFF,null,null,null, $more);
            } else {
                NotificationV2::delete_notificationbyref($id, NotificationV2::Product_Low_Stock);
            }
            $this->Product->set ($this->data);

            // Validate product barcodes
            $exist_barcodes = array_column($item_barcodes ?? [], 'barcode');
            $request_barcodes = array_column($this->data['ItemBarcode'] ?? [], 'barcode');
            $new_barcodes = array_diff($request_barcodes, $exist_barcodes);

            $barcodes_status = true;

            if (ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, 'enable_multi_units') && $product['Product']['type'] != Product::SERVICE_TYPE) {
                if (count($request_barcodes) > count(array_unique($request_barcodes))) {
                    $barcodes_status = false;
                } elseif (!empty($new_barcodes)) {
                    $new_barcodes = array_filter($new_barcodes, function ($barcode) {
                        return !empty($barcode);
                    });

                    if (!empty($new_barcodes)) {
                        $barcodes_status=$this->validate_bar_code($new_barcodes);
                    }

                }
            }

            if (ifPluginActive(ProductTracking) && ($product['Product']['type'] == Product::PRODUCT_TYPE || $product['Product']['type'] == Product::BUNDLE_TYPE)) {
                if($product['Product']['tracking_type'] != $this->data['Product']['tracking_type']){
                    $stock_transactions = $this->StockTransaction->find('first', ['conditions' => ['StockTransaction.product_id' => $id, 'StockTransaction.status' => StockTransaction::STATUS_PROCESSED], 'recursive' => -1]);
                    $requisition_items = $this->RequisitionItem->find('first', ['conditions' => ['RequisitionItem.product_id' => $id, ], 'recursive' => -1]);
                    $allow_negative_tracking = settings::getValue(ProductTracking, 'allow_negative_tracking');
                    $hasEmptyTransactions = empty($stock_transactions) && empty($requisition_items);
                    if(!$hasEmptyTransactions && !$allow_negative_tracking) {
                        $inventory_settings_url = Router::url(['controller' => 'settings', 'action' => 'inventory']);
                        $message = sprintf(__('You cannot change the tracking type for the Item while %s not enabled', true), '<a href="'.$inventory_settings_url.'" target="_blank">'.__("Allow negative tracking items", true).'</a>');
                        if(IS_REST) $this->cakeError("error400", ["message"=> $message, "validation_errors"=>$this->Product->validationErrors]);
                        $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
                        $this->redirect( array('action' => 'edit', $product['Product']['id'] ));
                    }
                }
                if($product['Product']['tracking_type'] != $this->data['Product']['tracking_type']){
                    $this->add_actionline(ACTION_EDIT_PRODUCT, array('primary_id' => $product['Product']['id'], 'secondary_id' => $product['Product']['id'],'param2' => $product['Product']['tracking_type'],'param8' => $product['Product']['tracking_type'], 'param9' => $this->data['Product']['tracking_type']));
                }
            }
            if($product['Product']['unit_template_id'] != $this->data['Product']['unit_template_id']){
                $this->loadModel('UnitTemplate');
                $old_unit_template = $this->UnitTemplate->find('first', ['conditions' => ['id' =>$product['Product']['unit_template_id']]]);
                $new_unit_template = $this->UnitTemplate->find('first', ['conditions' => ['id' =>$this->data['Product']['unit_template_id']]]);
                $this->add_actionline(ACTION_EDIT_PRODUCT, array('primary_id' => $product['Product']['product_code'], 'secondary_id' => $product['Product']['id'],'param2' => $this->data['Product']['unit_template_id'],'param7' => $old_unit_template['UnitTemplate']['template_name'], 'param8' => $new_unit_template['UnitTemplate']['template_name']));
            }
            // $this->Product->validates () validation already occures in $this->Product->save() .
            $this->Product->applyBranch['onSave'] = false;
            if (!$formError && $bundle_valid && ($appEntitiesFormHandler->validate($this->data) & $this->Product->validates ()) && $result = $this->Product->save($this->data) && $barcodes_status) {

                if (empty($this->data['ItemsTag']) && !IS_REST) {
                    $this->loadModel('ItemsTag');
                    $this->ItemsTag->delete_item_tags($this->data['Product']['id']);
                }

                if (ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, 'enable_multi_units') && $product['Product']['type'] != Product::SERVICE_TYPE) {
                    $this->loadModel ('ItemBarcode');
                    // delete all previous product item barcodes in case of (using single barcode instead of multiple or updating or inserting new barcodes)
                    $this->loadModel ('ItemBarcode');
                    $this->ItemBarcode->deleteAll(['ItemBarcode.product_id' => $product['Product']['id']]);
                }

                if (!empty($this->data['ItemBarcode'])) {
                    // insert new product item barcodes
                    foreach ($this->data['ItemBarcode'] as $item_barcode) {
                        if ($item_barcode['unit_price'] != '') {
                            $factor = $this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$item_barcode['unit_factor_id']]['factor'];
                            $item_barcode['unit_price'] = $factor != 0 ? $item_barcode['unit_price'] / $factor : false;
                        }
                        $record = [
                            'ItemBarcode' => array_merge($item_barcode,
                                [
                                    'product_id' => $product['Product']['id'],
                                    'created' => date("Y:m:d H:i:s"),
                                    'modified' => date("Y:m:d H:i:s"),
                                ]
                            )
                        ];
                        $this->loadModel ('ItemBarcode');
                        $this->ItemBarcode->create();
                        $this->ItemBarcode->save($record);
                    }
                }

                $data = $this->data; 
                $data['Product']['id'] = $id;
                izam_resolve(ProductService::class)->update(ServiceModelDataTransformer::transform($data, 'Product'));
                if (isset($this->data['Product']['attachment'])) {
                    $imagesIds = explode(',', $this->data['Product']['attachment']);
                    izam_resolve(AttachmentsService::class)->save('product', $id, $imagesIds);
                }
            
                if (ifPluginActive(CREDIT_PLUGIN) && $product['Product']['source_type'] == "package" && !is_null($product['Product']['source_id']) && !IS_REST) {
                    $this->loadModel('Package');
                    $package = $this->Package->find('first', ['conditions' => ['Package.id' => $product['Product']['source_id']]]);
                    $needUpdate = false;
                    if (($package['Package']['price'] != $this->data['Product']['unit_price']) && isset($this->data['Product']['unit_price'])) {
                        $package['Package']['price'] = $this->data['Product']['unit_price'];
                        $needUpdate = true;
                    }

                    if ($package['Package']['name'] != $this->data['Product']['name'] && isset($this->data['Product']['name'])) {
                        $package['Package']['name'] = $this->data['Product']['name'];
                        $needUpdate = true;
                    }

                    if ($package['Package']['status'] == $this->data['Product']['status'] && isset($this->data['Product']['status']) ) {
                        $package['Package']['status'] = !$this->data['Product']['status'];
                        $needUpdate = true;
                    }
                    if ($needUpdate) {
                        $this->Package->save($package);
                    }
                }

                $product_id = $this->data['Product']['id'];
                if ($result) {
                    $appEntitiesFormHandler->update($id, $this->data);
                    if(isset($this->Product->CustomModelError) && $this->Product->CustomModelError==false){
                        $this->flashMessage(__("Custom Fields can not be saved", true), 'Errormessage', 'secondaryMessage');
                    }

                    if(!empty($generated_number)) \AutoNumber::update_auto_serial(\AutoNumber::TYPE_PRODUCT);
                    elseif($this->data['Product']['product_code']!=$this->data['Product']['default_product_code']) \AutoNumber::update_last_from_number($this->data['Product']['product_code'],\AutoNumber::TYPE_PRODUCT);
                }
                // update group prices
                $this->updateProductPriceGroups($product_id, isset($this->data['priceList'])?$this->data['priceList'] : []);
                //save the bundles according to this product_id
                $this->loadModel('ProductBundle');

                $this->ProductBundle->deleteAll( [ 'ProductBundle.bundle_product_id' => $product_id ] );
//                dd($this->ProductBundle->find('all'));
                unset($this->data['ProductBundle']['Tempid']);
                $bundles = $this->data['ProductBundle'];
//                dd($bundles );
                foreach( $bundles as $product )
                {
                    $product['bundle_product_id'] = $product_id;
                    $product['product_id'] =  $product['product_id'][0];
                    $data['ProductBundle'] = $product;
                    $data['ProductBundle']['quantity'] = $data['ProductBundle']['quantity'] * (empty($data['ProductBundle']['unit_factor']) ? 1 : $data['ProductBundle']['unit_factor']);

                    $this->ProductBundle->create();
                    if(! $this->ProductBundle->save( $data ) )
                    {
						if(IS_REST) $this->cakeError("error400", ["message"=>"Error in Bundle", "validation_errors"=>$this->ProductBundle->validationErrors]);
                        $this->flashMessage(__("Error while saving Bundles data try again", true), 'Errormessage', 'secondaryMessage');
                        $this->redirect( array('action' => 'edit', $product_id ));
                    }
                }

                /*
                * get new categories ids
                * new_categories_id  used in mobile Api (new feature).
                */
                $new_categories = [];
                $new_categories_id = [];

                if (IS_REST) {
                    if(!empty($this->data['Product']['category'])){
                    $new_categories = [$this->data['Product']['category']];
                    }

                    if(!empty($this->data['Product']['categories_id']) && is_array($this->data['Product']['categories_id'])){
                    $new_categories_id = $this->data['Product']['categories_id'];
                    }

                } elseif(is_array($this->data['Product']['Category'])) {
                    $new_categories = $this->data['Product']['Category'];
                }

                //if the categories are new then you insert them in the categories table and attach the ids
				$this->loadModel('Category');
                foreach($new_categories as $key => $string)
                {
                    $this->Category->findOrCreate($string,Category::CATEGORY_TYPE_PRODUCT);
                }

				unset( $this->data['Product']['Category'] );

                //get old categories ids
                $this->loadModel('ItemsCategory');
//                $old_categories = $this->ItemsCategory->find( "list" ,['fields'=>['ItemsCategory.category_id'], 'conditions' => ['ItemsCategory.item_type' => 'product' , 'ItemsCategory.item_id' => $product_id ] ] );//['Category'];

                //delete old ids
                $headers = array_change_key_case(getallheaders(), CASE_LOWER);
                if (!IS_REST || isset($headers['x-app-name'])) {
                    $this->ItemsCategory->deleteAll(array('ItemsCategory.item_type' => ItemsCategory::ITEM_TYPE_PRODUCT, 'ItemsCategory.item_id' => $product_id));
                }
                debug($new_categories);
                foreach( $new_categories as $category_name )
                {
                    $this->ItemsCategory->create();
                    $obj['ItemsCategory'] = [
                        'item_id' => $product_id,
                        'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT,
                        'category_id' => $this->Category->findOrCreate($category_name,Category::CATEGORY_TYPE_PRODUCT)
                    ];
                    $temp[] =$obj;
                    $this->ItemsCategory->save($obj);
                }

                foreach ($new_categories_id as $category_id) {
                    $this->ItemsCategory->create();
                    $obj['ItemsCategory'] = [
                        'item_id' => $product_id,
                        'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT,
                        'category_id' => $category_id
                    ];
                    $temp[] =$obj;
                    $this->ItemsCategory->save($obj);
                }


//                $this->add_actionline(ACTION_EDIT_PRODUCT, array('primary_id' => $id, 'secondary_id' => $id, 'param4' => $this->data['Product']['name'], 'param2' => $this->data['Product']['unit_price'], 'param3' => $this->data['Product']['default_quantity']));
                $this->saveSalesAccount( $id);
                $this->saveSalesCostAccount( $id);
                // $this->add_actionline(ACTION_EDIT_PRODUCT, array('primary_id' => $id, 'secondary_id' => $id, 'param4' => $this->data['Product']['name'], 'param2' => $this->data['Product']['unit_price']));
                if (ifPluginActive(InventoryPlugin)) {
                    if ($product['Product']['track_stock'] == "1" && !empty($this->data['Product']['initial_stock_level'])) {
                        $this->loadModel('StockTransaction');
                        //  StockTransaction::SaveProductStock($id,1, 1, 0, date("Y-m-d H:i:s"), null, $this->data['Product']['initial_stock_level'], $this->data['Product']['buy_price'], $this->data['Product']['buy_price'], $owner['staff_id'], 0,null);
                        $data['currency_code'] = getAuthOwner('currency_code');
                        $data['date'] = date("Y-m-d H:i:s");
                        $data['source_type'] = StockTransaction::SOURCE_MANUAL;
                        $data['order_id'] = 0;
                        $data['product_id'] = $id;
                        $data['ref_id'] = $id;
                        $data['status'] = StockTransaction::STATUS_PROCESSED;
                        $data['transaction_type'] = StockTransaction::TRANSACTION_IN;
                        $data['quantity'] = (float)$this->data['Product']['initial_stock_level'];
                        $data['price'] = (float)$this->data['Product']['buy_price'];
                        $data['staff_id'] = $owner['staff_id'];
                        $data['total_price'] = $data['quantity'] * $data['price'];
                        debug($product);
                        StockTransaction::saveTransaction($data, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']),$this->data['Product']['store_id']);
                    }
                }

                $newData = $this->getRecordWithEntityStructureWithCustomFields(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY, $id, 2);
                $st = getEntityBuilder()->buildEntity(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY);
                

                if (!empty($this->data['Product']['attachment'])) {
                    $filesId = explode(',', $this->data['Product']['attachment']);
                    $attachmentsIds = $this->getS3FilesDetailsForActivityLog($filesId);
                    $newData = array_merge($newData,['Files' => $attachmentsIds]);
                }

                $requests = (new DetailsEntityActivityLogRequestsCreator())->create($st, $newData, $oldData, []);

                $activityLogService =  new \App\Services\ActivityLogService();

                foreach ($requests as $requestObj) {
                    $activityLogService->addActivity($requestObj);
                }

				if(IS_REST){
					$this->render('success');
					return;
				}

                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Product', true)), 'Sucmessage');
                $action = 'view';

                if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_new')
                {
                    $action = 'add';
                    $this->redirect(array('action' => $action));
                }
                else{
                    $this->redirect(array('action' => $action, $id));
                }

//                $this->redirect(array('action' => 'view', $id));
            } else {

                // Used To get attachments details in case of Validation error occurred. 
                if (!empty($this->data['Product']['attachment'])) {
                    $filesId = explode(',', $this->data['Product']['attachment']);
                    $attachments = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                    $this->data['Attachments'] = $attachments;
                }

                $this->set('you_sell',$this->data['Product']['type']);
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Product->validationErrors]);
                $this->Product->validationErrors = $this->Product->validationErrors ;
                if ($barcodes_status == false) {
                    $this->flashMessage(__("Product barcodes must be unique.", true), 'Errormessage', 'secondaryMessage');
                }
                if($bundle_valid){
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Product', true)));
                } else {
                    $this->flashMessage(__($bundleValidationErrorMessage, true), 'Errormessage', 'secondaryMessage');
                }
            }
        } else {

            $this->data = array_merge($product, ['ItemBarcode' => $item_barcodes]);
            $this->data['Product']['default_product_code'] = $this->data['Product']['product_code'];
            if ( $this->viewVars['enable_multi_units'] && !empty ( $this->data['Product']['default_buy_factor_id']))
            {
                $this->data['Product']['buy_price'] = round($this->data['Product']['buy_price'] *$this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$this->data['Product']['default_buy_factor_id']]['factor'], 6);
            }
            if ( $this->viewVars['enable_multi_units'] && !empty ( $this->data['Product']['default_retail_factor_id']))
            {
                $this->data['Product']['unit_price'] = round($this->data['Product']['unit_price'] *$this->viewVars['factors_list'][$this->data['Product']['unit_template_id']][$this->data['Product']['default_retail_factor_id']]['factor'], 5);
             }
            $this->set('you_sell',$this->data['Product']['type']);
            if (!empty($product['Product']['tags'])) {
                $tags = explode(",", $product['Product']['tags']);
                if (count($tags) > 0) {
                    $this->data['Product']['tags'] = json_encode($tags);
                }
            }
            if (!empty($product['Product']['brand'])) {
                $brands = explode(",", $product['Product']['brand']);
                if (count($brands) > 0) {
                    $this->data['Product']['brand'] = json_encode($brands);
                }
            }
        }
        $this->set('discount_types',$this->Product->getDiscountTypes());

        if (ifPluginActive(InventoryPlugin)) {
            $this->loadModel('StockTransaction');
            $this->set('product_stock_count', StockTransaction::getProductStock($id));
        }

        //$this->set_service_product_settings();
        $this->loadModel('Tax');
        $this->set('taxes', $this->Tax->getTaxList());
        $this->loadModel('GroupPrice');
        $this->loadModel('ProductPrice');
        // get price lists assigned to this product
        $ProductPrice = $this->ProductPrice->find('all',array('conditions'=>array('ProductPrice.product_id'=>$id)));
        // get all price lists
        $activeGroupPrices = $this->GroupPrice->getActiveList();
        $allGroupPrices = $this->GroupPrice->getAll();
        foreach ($ProductPrice as $price_row) {
            $name = $allGroupPrices[$price_row['ProductPrice']['group_price_id']];
            if ($name) {
                $PriceList[$price_row['ProductPrice']['group_price_id']] = [
                    'id' => $price_row['ProductPrice']['id'],
                    'price' => $price_row['ProductPrice']['price'],
                    'name' => $name
                ];
                unset($activeGroupPrices[$price_row['ProductPrice']['group_price_id']]);
            }
        }

        $this->set('GroupPrices', $activeGroupPrices);
        $this->set('PriceList', $PriceList);
        $this->set('allowPriceList', ((count($activeGroupPrices ?? [])+ count($PriceList ?? [])) > 0));

        $this->loadModel('Supplier');
        $this->set('tracking_type',$product['Product']['tracking_type']);
        $this->set('supplier_count',$this->Supplier->find('count'));
        $this->set('primary_store', $this->Store->getPrimaryStore() );
        $this->loadModel('ItemPermission');
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW) );
        $this->set('title_for_layout',  h($product['Product']['name'] . ' - ' . __('Products & Services', true)));
        //get the categories as json for the magicsuggest
        $this->loadModel('ItemsCategory');
        $categories = $this->ItemsCategory->find('all', ['conditions' => ['ItemsCategory.item_type'=>ItemsCategory::ITEM_TYPE_PRODUCT , 'ItemsCategory.item_id'=>$id] ]);

        $categories_list = array();

        foreach( $categories as $index => $category )
        {
            $categories_list[$category['Category']['id']] = ["id"=> $category['Category']['name'], "name"=>$category['Category']['name']];
        }

        $this->set('categories_values', $categories_list );

        //load the bundles and send them to the model
        $this->loadModel('ProductBundle');
        $product_bundles = $this->ProductBundle->find('all',['conditions'=>['ProductBundle.bundle_product_id'=>$id]]);
        $pb = [] ;
        foreach ($product_bundles as $p ) {
            $pb[$p['ProductBundle']['id']] = $p ;
        }
        $this->set('product_bundles', $pb );
        $this->set('enable_requisitions',settings::getValue(InventoryPlugin, 'enable_requisitions'));
        $this->set('enable_requisitions_po',settings::getValue(InventoryPlugin, 'enable_requisitions_po'));

        $forms = $appEntitiesFormHandler->getAppsEditForms($id,  $this->data);
        $this->set('forms', $forms);
        $this->setCustomEntityAndKey();
        $this->set('rulesCustom',\App\Helpers\CustmFieldsFilesValidation::extractFilesValidation('Product'));

        $this->render('owner_add');
    }

    function owner_delete($id = null) {

        if ($_POST['submit_btn'] == 'no'){ $this->redirect(array('action' => 'index')); }
        if(isset($id) && !empty($id)){
            $product = $this->Product->find(array('Product.id' => $id));
            $owner = getAuthOwner();
            if (!check_permission(Edit_Delete_all_Products) && !check_permission(Edit_And_delete_his_own_added_Products) && !($product['Product']['source_type'] == "package" && check_permission(MANAGE_PACKAGES) )) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to delete products", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }

            if (ifPluginActive(BranchesPlugin)) {
                $current_branch_id = getCurrentBranchID();
                $product_branch_id = $product['Product']['branch_id'];
                if($current_branch_id != $product_branch_id && !isModelSharable('Product')){
                    if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('%s not found in the current branch.', true), __('Product', true))));
                    $this->flashMessage(sprintf(__('%s not found in the current branch.', true), __('Product', true)));
                    $this->redirect($this->referer(array('action' => 'index')));
                }
            }
        }

        $x_time = 1.5; //default_value
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            if (is_countable($id) && count($id) > 1000) {
                ini_set('memory_limit', '10G');
                $x_time = 5;
            }
            if (is_countable($id) && count($id) > 5000) {
                $this->flashMessage(__("You can't delete more than 5,000 products at one time.", true));
                $this->redirect(array('action' => 'index'));
            }
        }
        $this->setTimeOut($id,$x_time);
        if (!$id && empty($_POST)) {
			if(IS_REST){
				$this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('product', true))]);
			}else{
				$this->flashMessage(sprintf(__('Invalid %s', true), __('product', true)));
				$this->redirect(array('action' => 'index'));
			}
        }
        $module_name = __('product', true);
        $verb = __('has', true);
        if (count((array)$id) > 1) {
            $verb = __('have', true);
            $module_name = __('products', true);
        }
        $conditions = array();
        // $conditions['Product.site_id'] = getAuthOwner('id');
        $conditions['Product.id'] = $id;
        if ($owner['staff_id'] != 0 && !check_permission(Edit_Delete_all_Products)) {
            $conditions['Product.staff_id'] = $owner['staff_id'];
        }
        $products = $this->Product->find('all', array('conditions' => $conditions));
        if (empty($products)) {
			if(IS_REST){
				$this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), ucfirst($module_name))]);
			}else{
				$this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
				$this->redirect($this->referer(array('action' => 'index'), true));
			}
        }
        if (ifPluginActive(CREDIT_PLUGIN)) {
            foreach ($products as $product) {
                if (!isset($product['Product']['id']) || !$product['Product']['id']) {
                    continue;
                }
                if ($this->Product->isRelatedToPackage($product['Product']['id'])) {
                    $this->flashMessage(sprintf(__('You cannot delete the service that related to package. (%s)', true), sprintf('<a href="/v2/owner/packages/%s" target="_blank">%s</a>', $product['Product']['source_id'], sprintf('%s #%s', __('Package', true), $product['Product']['source_id']))), 'Errormessage', 'secondaryMessage');
                    $this->redirect($this->referer(array('action' => 'index'), true));
                }
            }
        }

        try {
            $this->Product->canProductBeDeleted($id);
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage());
            $this->redirect($this->referer(array('action' => 'index')));
        }


		if(IS_REST){
			$_POST['submit_btn'] = 'yes';
			$_POST['ids'] = [$id];
		}
		$productEntities = [];
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $has_transactions = false ;
            foreach ( $products as $p  ){
                $productEntities[$p['Product']['id']] = [];
                if (EventListenerMapper::hasEventListener(EventTypeUtil::PRODUCT_DELETED)) {
                    $productEntities[$p['Product']['id']] = getRecordWithEntityStructure('product', $p['Product']['id'], 2)->toArray();
                }

                if ($p['Product']['track_stock'] == '1' && $this->Product->has_transactions($p['Product']['id'])) {
                    if ($stock_taking_id= $this->Product->has_stock_taking_transactions($p['Product']['id'])) {
                        $reheated_link="<a  href='/v2/owner/stocktakings/{$stock_taking_id}'> <b> ".  __('Stocktaking',true) . "</b></a>";
                        $this->flashMessage( sprintf (__("This Product has existing in %s , you can make it inactive.",true),$reheated_link));
                    }
                    $has_transactions = true;
                    break;
                }
            }
			if (!$has_transactions && $_POST['submit_btn'] == 'yes') {
                if (ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, 'enable_multi_units')) {
                    // We have to delete the product item barcodes first, before attempting to delete the product itself since it's a constraint.
                    $this->loadModel('ItemBarcode');
                    // delete product barcodes
                    $this->ItemBarcode->deleteAll(['ItemBarcode.product_id' => $id]);
                }
			}

            $oldProductsData = [];
            foreach ($products as $product) {
                $oldProductsData[$product['Product']['id']] = $this->getRecordWithEntityStructureWithCustomFields(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY, $product['Product']['id'], 1);
            }

            if (!$has_transactions && $_POST['submit_btn'] == 'yes' && $this->Product->delete_all_transactions($id) && $this->Product->deleteAll($conditions,true, true)) {

                $this->loadModel('ProductBundle');
                $this->loadModel('ProductPrice');


                // delete price lists assigned to this product
                $this->ProductPrice->deleteAll(['ProductPrice.product_id'=>$id]);
                $this->ProductBundle->deleteAll(['ProductBundle.bundle_product_id' => $id ] );
                $JAR = GetObjectOrLoadModel('JournalAccountRoute');
                foreach ($products as $product) {
                    $st = getEntityBuilder()->buildEntity(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY);
                    $requests = (new DetailsEntityActivityLogRequestsCreator())->setLabel($product["Product"]["name"])->create($st, [], $oldProductsData[$product['Product']['id']], []);
                    $activityLogService =  new \App\Services\ActivityLogService();
                    foreach ($requests as $requestObj) {
                        $activityLogService->addActivity($requestObj);
                    }

                    izam_resolve(ProductService::class)->delete($productEntities[$product['Product']['id']]);
                    $JAR->deleteAll([
                        'JournalAccountRoute.entity_id' => $product['Product']['id'],
                        'JournalAccountRoute.entity_type' => Journal::PRODUCT_SALES_ENTITY_TYPE
                    ]);
                    EntityAttachment::where(['entity_key' => 'product', 'entity_id' => $product['Product']['id']])->delete();
                    $this->add_actionline(ACTION_DELETE_PRODUCT, array('primary_id' => $product['Product']['id'], 'secondary_id' => $product['Product']['id'], 'param4' => $product['Product']['name'], 'param2' => $product['Product']['unit_price'], 'param3' => $product['Product']['default_quantity']));

                }
				if(IS_REST){
					$this->set("message", sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb));
					$this->render("success");
					return;
				}
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
				if(IS_REST) {
					if($has_transactions) $this->cakeError("error500", ["message"=>__("This Product has existing transactions, you can make it inactive.", true)]);
					else $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
				}
                if ( $has_transactions ) {
                    $this->flashMessage(__("This Product has existing transactions or stocktakings, you can make it inactive.",true)  , 'Errormessage', 'secondaryMessage' );
                }
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  h(__('Delete products & service', true)));

        $this->set('products', $products);
        $this->set('module_name', $module_name);
        $this->render('owner_delete2');
    }

    function beforeFilter() {
        $this->redirectToConfirmation();
        parent::beforeFilter();
        $this->titleAlias = __('Products', true);
    }

    function owner_json_findx($value) {

        $this->loadModel('Product');
        $site_id = getAuthOwner('id');
        $dbproducts = $this->Product->find('all', array('conditions' => array('OR' => array("Product.name like '% $value%'", "Product.name like '$value%'")), 'limit' => 7, 'order' => 'Product.name asc', 'recursive' => -1));

        $products = array();
        foreach ($dbproducts as $prod) {
            $products[$prod['Product']['id']] = $prod['Product'];
        }

        echo json_encode($products);
        die();
    }

    function owner_json_find_category()
	{
        $this->loadModel('Category');
		if (!empty($_GET['q']) && strlen($_GET['q']) >= 2) {
			$value = mysql_escape_string($_GET['q']);
			$conditions = [];
            $cats = $this->Category->find('list' , ['conditions' => ['Category.name LIKE' => "%$value%",'Category.category_type' => Category::CATEGORY_TYPE_PRODUCT]]);
			$result = [];  
			foreach ($cats as $catId => $catName) {
				$result[] = [
					'name' => $catName,
					'id' => $catId,
					'details' => '',
				];
			}
			if (!empty($result))
				array_unshift($result, [
					'name' => __('[Any Category]', true),
					'id' => '',
					'details' => '',
				]);
			echo json_encode($result);
			die();
		}
	}

	function owner_json_find_brand()
	{
		if (!empty($_GET['q'])) {
			$value = mysql_escape_string($_GET['q']);
			$conditions = [];
			$conditions["Product.brand LIKE"] = "%$value%";
			$products = $this->Product->find('all', ['conditions' => $conditions, 'limit' => 20, 'order' => 'Product.name asc', 'recursive' => -1]);
			$result = [];
			foreach ($products as $product) {
				$result[] = [
					'name' => $product['Product']['brand'],
					'id' => $product['Product']['brand'],
					'details' => '',
				];
			}
			if (!empty($result))
				array_unshift($result, [
					'name' => __('[Any Brand]', true),
					'id' => '',
					'details' => '',
				]);
			echo json_encode($result);
			die();
		}
	}

    function owner_json_find_product_multiprint_attrs(){
        $id = (int) $_GET['product_id'];
        $product = $this->Product->findById($id);
        $barcodes = $this->Product->getBarcodeListByProduct($product);
        $data = ['type' => $product['Product']['tracking_type']];
        $itemType = $data['type'];
        $product['Product']['barcodes'] = $barcodes; 
        $this->set('itemType', $itemType);
        $this->set('product', $product['Product']);
        $this->set('id',  $id);
        $this->set('index', '#index');
        $data['field'] = $this->render('/elements/tracking_type_field'); 
        die(json_encode($data));
    }

	function owner_json_find() {
        Configure::write('debug', 0);
        if (!empty($_GET['q'])) {
            $value = mysql_escape_string($_GET['q']);
            $clients = array();
            $conditions = array();


            if (check_permission(Clients_View_All_Clients) || check_permission(Clients_View_his_own_Clients)) {
                $conditions['OR'] = array("CONCAT(Product.name,' ',COALESCE( Product.product_code,  '' )) like '%$value%'");
                $products = $this->Product->find('all', array('conditions' => $conditions, 'order' => 'Product.name asc', 'recursive' => -1, 'limit' => 500));

                $result = array();
                foreach ($products as $product) {
                    $result[] = array(
                        'name' => $product['Product']['name'],
                        'id' => $product['Product']['id'],
                        'details' => ''
                    );
                }
            }
            if (!empty($result))
                array_unshift($result, array(
                    'name' => __('Please Select', true) . ':',
                    'id' => '',
                    'details' => ''
                ));
            echo json_encode($result);
            die();
        }
    }

    function owner_load() {
        die(json_encode(
                        array(
                            'products' => $this->Product->getProductList(),
                            'jsProducts' => $this->Product->getInvoiceProductList()
        )));
    }

    function owner_stocktaking_sheet() {

        // This code added to handle values added in 2 texetareas (quantity & Remarks) , 'textareaValues' will added in pdf case only. 
        if(!empty($_GET['textareaValues'])){
            $extra_details = json_decode($_GET['textareaValues'],true);
            $this->set ('extra_details' , $extra_details );
        }
        
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(View_All_Products) && !check_permission(View_his_own_Products)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        $this->loadModel('Category');
        $allCats = $this->Category->getProductCategoriesWithParentList();
        $categories = $this->Category->flattenArray($allCats);
        $this->set('categories', $categories);

//        $this->Product->recursive = 0;
        $group_bys = array('category' => __('Category', true), 'brand' => __('Brand', true));
        $order_bys = array('name.asc' => __('Product Name', true), 'stock_balance.asc' => __('Stock Level Ascending', true), 'stock_balance.desc' => __('Stock Level Descending', true));
        if (!empty($_GET['data']))
            $this->data = $_GET['data'];
        if (!empty($this->data)) {
            $conditions['Product.track_stock'] = true;
            $order = array();
            if (!empty($this->data['category']))
            {   //OLD category
                $this->loadModel('ItemsCategory');
                $item_ids = array_values ($this->ItemsCategory->find('list',['fields' => 'item_id' , 'conditions'=>['category_id' => $this->data['category'] ] ]) );
                $conditions['Product.id'] = $item_ids;
            }

            if (!empty($this->data['brand']))
                $conditions['Product.brand'] = $this->data['brand'];

            if (!empty($this->data['group_by']) && in_array($this->data['group_by'], array_keys($group_bys)))
                $order['Product.' . $this->data['group_by']] = 'ASC';

            if (!empty($this->data['order_by']) && in_array($this->data['order_by'], array_keys($order_bys))) {
                $tmp = explode('.', $this->data['order_by']);
                $order['Product.' . $tmp[0]] = $tmp[1];
            }
            $havingOption = '';
            if (!empty($this->data['hide_zero_values'])) {
                $havingOption = 'HAVING ROUND(all_balance, 2) > 0 OR ROUND(all_balance, 2) < 0';
            }
            $options = [] ;
            $options['fields'] = ['Product.*','Supplier.*'];
            $options['fields'][] = 'sum(StockTransaction.quantity) as all_balance';
            $options['group'] = array('Product.id '.$havingOption);
            $options['joins']= [['table' => 'stock_transactions',
                'alias' => 'StockTransaction',
                'type' => 'LEFT',
                'applyBranchFind' => false,
                'conditions' => array(
                    'Product.id = StockTransaction.product_id',
                )]
            ];
            if ( !empty($order['Product.stock_balance']))
            {
                $order['all_balance'] = $order['Product.stock_balance'] ;
                unset($order['Product.stock_balance']);
            }

            if (!empty($this->data['store'])) {
                $conditions['StockTransaction.store_id'] = $this->data['store'];
                $this->set ( 'have_store' , 1 );
            }
            else
            {
                $this->loadModel ( 'ItemPermission' );
                $stores_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE , ItemPermission::PERMISSION_VIEW,null,false,true);
                $conditions['StockTransaction.store_id'] =  array_keys($stores_list);
                $this->set ( 'have_store' , 1 );
            }

            $conditions['StockTransaction.ignored'] = 0;
            $conditions['StockTransaction.status'] = 4;

            $conditions['OR'][]['Product.status <>'] = ProductStatusUtil::STATUS_INACTIVE;
            $conditions['OR'][] = 'Product.status IS NULL';

//			$options['fields'][] = ' (Select categories.name from categories, categories_items where   categories_items.item_id = Product.id AND categories_items.category_id=categories.id AND  categorie_items.type=\'product\'  Limit 1 )  as  category2 ';
			//categories section

			$categories_conditions =  [];
			$this->loadModel('ItemsCategory');

            if (!empty($this->data['category'])) {
                $cats = $this->Category->getAllDescendantCategoryIds($allCats, $this->data['category']);

				$conditions['Product.id'] = $this
                    ->ItemsCategory->find('list', [
                        'fields' => ['ItemsCategory.item_id'],
                        'conditions' => ['ItemsCategory.category_id' => $cats,
                            'ItemsCategory.item_type=' . ItemsCategory::ITEM_TYPE_PRODUCT
                        ]
                    ]);
			}
//            if (!empty($this->data['brand']))
//                $conditions['Product.category'] = $this->data['brand'];

            $this->Product->unbindModel(['hasOne' => ['ProductMasterImage'],'hasMany' => ['ProductImage']]);
            $categories_conditions[] = 'ItemsCategories.item_type=' . ItemsCategory::ITEM_TYPE_PRODUCT;
            $this->loadModel('Category');
            $this->Product->bindModel(
                array('hasMany' => array(
                    'ItemsCategories' => array(
                        'className' => 'ItemsCategories',
                        'foreignKey' => 'item_id',
                        'conditions' => $categories_conditions,
                    )
                )
                ));
			//end categories section
            $options['conditions'] = $conditions;
            $order['Product.name'] = 'ASC';
            
            if(!empty($_GET['sort_column'])){
                unset($order['Product.name'] );
                $sort_col = sprintf('%s.%s', 'Product',$_GET['sort_column']);
                $order[$sort_col] = $_GET['sort_direction'];
            }

            $options['order'] = $order;
            $order['Product.stock_balance'] = 'DESC';
            $this->set('data', $this->data);

            if ($site['staff_id'] != 0) {
                if (!check_permission(View_All_Products) && check_permission(View_his_own_Products)) {
                    $conditions['Product.staff_id'] = $site['staff_id'];
                }
            }
//			die(debug($this->Product->find('all', $options)));
            $products = $this->Product->find('all', $options);



            $this->loadModel('Category');
           // print_pre($products);

            foreach($products as $k => $product){

                $dd=Set::extract('/ItemsCategories/category_id',$product);
                if(!empty($dd)) {
                    $cat_list = $this->Category->find('list',array('conditions' => ['Category.id'=>$dd]));
                    $product[$k]['Product']['category']=implode(',',$cat_list);
                    }
            }

            $this->set ( 'enable_multi_units' , settings::getValue(InventoryPlugin, 'enable_multi_units') );
            if ( $this->viewVars['enable_multi_units']){
                foreach ( $products as $k => $product ) {
                    $products[$k]['factor']  = $this->Product->get_default_factor ( $product ) ;
                }
            }
            //Low Performance, updated above in else condition
            /*
            if(empty($this->viewVars['have_store']))
            {
                App::import('vendor','WarehouseService' , ['file' => 'WarehouseService.php']);
                $ws = new WarehouseService();
                $this->loadModel('ItemPermission');
                foreach($products as $k => $p){
                    $products[$k]['Product']['stock_balance'] = array_sum($ws->getStaffProductStock(ItemPermission::PERMISSION_VIEW, $p['Product']['id'],getAuthOwner('staff_id')));
                }
            }
            */
            $this->set('products', $products );
        }

        $this->loadModel('ItemPermission');
        $this->set('stores', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW,null,false,true) );


        $this->set('brands', $this->Product->getBrandsList(false));
        $this->set('order_bys', $order_bys);
        $this->set('group_bys', $group_bys);
        $showTotalCount = false;
        if (!empty($this->data['total_count'])) {
            $showTotalCount = true;
        }
        $this->set('showTotalCount', $showTotalCount);

        $this->set_service_product_settings();
        $this->set('owner', $site);
        $this->set('content', $this->get_snippet('products'));
        $this->set('title_for_layout',  h(__('Stocktaking Sheet', true)));
    }


    function owner_worthsheet() {  
        $this->loadModel ( 'ItemsCategory' ) ;
        $this->loadModel ( 'StockTransaction' ) ;
        App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(View_All_Products) && !check_permission(View_his_own_Products)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->Product->recursive = 0;
        $this->loadModel('ItemPermission');
        $authStores = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW,null,false,true) ;
        $authStoreIds = array_keys($authStores);
        $group_bys = array('category' => __('Category', true), 'brand' => __('Brand', true));
        $order_bys = array('name.asc' => __('Product Name', true), 'stock_balance.asc' => __('Stock Level Ascending', true), 'stock_balance.desc' => __('Stock Level Descending', true));

        if (!empty($_GET['data']))
            $this->data = $_GET['data'];
        if (!empty($this->data)) {
            $conditions['Product.track_stock'] = true;
            $conditions['OR'] = [
                'Product.status' => [ProductStatusUtil::STATUS_ACTIVE, ProductStatusUtil::STATUS_SUSPENDED],
                'Product.status IS NULL'
            ];
            $order = array();
            if (!empty($this->data['category']))
            {
                $categories=$this->getCategoryChildrenIds($this->data['category']);
                $conditions['ItemsCategory.category_id'] = $categories;
            }
            if (!empty($this->data['brand']))
                $conditions['Product.brand'] = $this->data['brand'];

            if (!empty($this->data['group_by']) && array_key_exists($this->data['group_by'], $group_bys))
                $order['Product.' . $this->data['group_by']] = 'ASC';

            if (!empty($this->data['order_by']) && array_key_exists($this->data['order_by'], $order_bys)) {
                $tmp = explode('.', $this->data['order_by']);
                $order['Product.' . $tmp[0]] = $tmp[1];
            }
            $options = [] ;
            if (!empty($this->data['store']) && in_array($this->data['store'], $authStoreIds)) {
                $conditions['StockTransaction.store_id'] = $this->data['store'];
                $this->set ( 'have_store' , 1 );
            } else {
                $conditions['StockTransaction.store_id'] = $authStoreIds;
            }

            $owner = getAuthOwner();
            if (!empty($_GET['date_from'])) {
                $date_from = $this->Product->formatDate($_GET['date_from'], $owner['date_format']);
                $conditions[] = "StockTransaction.received_date > '$date_from 00:00:00'";
            }

            if (!empty($_GET['date_to'])) {
                $date_from = $this->Product->formatDate($_GET['date_to'], $owner['date_format']);
                $conditions[] = "StockTransaction.received_date < '$date_from 23:59:59'";
            }

            if(isset($_GET['supplier_id']) && $_GET['supplier_id']) {
                $conditions['Supplier.id'] = $_GET['supplier_id'];
            }
            if(isset($_GET['brand_id']) && !empty($_GET['brand_id'])) {
                $conditions['Product.brand_id'] = $_GET['brand_id'];
            }
            App::import('Vendor', 'settings');
            $options['fields'] = ['Product.*','Supplier.*', 'average_price' , "sum(StockTransaction.quantity) as all_balance"];
        
            $havingOption = '';
            if (!empty($this->data['hide_zero_values'])) {
                $havingOption = 'HAVING ROUND(all_balance, 2) > 0 OR ROUND(all_balance, 2) < 0';
            }

            $options['group'] = array('Product.id '.$havingOption);

            $options['joins']= [['table' => 'stock_transactions',
                'alias' => 'StockTransaction',
                'type' => 'LEFT',
                'conditions' => array(
                    'Product.id = StockTransaction.product_id',
                )],
                [
                    'table' => "(Select DISTINCT item_id , item_type, max(category_id) as category_id from items_categories ".((!empty($this->data['category'])?(" Where category_id IN( ".implode(',',$categories).')'):""))." group by item_id)",
                'alias' => 'ItemsCategory',
                'type' => 'LEFT',
                'conditions' => array(
                    'Product.id = ItemsCategory.item_id AND ItemsCategory.item_type = '.ItemsCategory::ITEM_TYPE_PRODUCT,
                )]
                ];
            $conditions['StockTransaction.status'] = StockTransaction::STATUS_PROCESSED;
            $conditions[] = " (StockTransaction.ignored = 0 OR StockTransaction.ignored is null ) ";
            $options['order'] = $order;
            $order['Product.name'] = 'ASC';
            $order['Product.stock_balance'] = 'DESC';
            $this->set('data', $this->data);
            if ($site['staff_id'] != 0) {
                if (!check_permission(View_All_Products) && check_permission(View_his_own_Products)) {
                    $conditions['Product.staff_id'] = $site['staff_id'];
                }
            }
            $options['conditions'] = $conditions;
	        $this->Product->Behaviors->disable('customform');
            $all_products = $this->Product->find('all', $options) ;
	        $this->Product->Behaviors->enable('customform');
            $this->set ( 'enable_multi_units' , settings::getValue(InventoryPlugin, 'enable_multi_units') );
            if ( $this->viewVars['enable_multi_units'] ){
                foreach ( $all_products as $k => $i ) {
                    $all_products[$k]['factor']  = $this->Product->get_default_factor ( $i ) ;
                }
            }
            $this->set('products', $all_products);
        }

        $this->set('stores', $authStores);
        $this->set('categories', $this->Product->getCategoriesList(false));
        $this->set('brands', $this->Product->getBrandsList(false));
        $this->set('order_bys', $order_bys);
        $this->set('group_bys', $group_bys);
        $this->set_service_product_settings();
        $this->set('owner', $site);
        $this->set('content', $this->get_snippet('products'));
        $this->set('title_for_layout',  h(__('Estimated Inventory Value', true)));
    }

    function getCategoryChildrenIds($cat_id)  {
        $sql="SELECT child.id ,child.name,child.parent_id FROM categories AS parent JOIN categories AS child ON parent.id = child.parent_id WHERE parent.id = {$cat_id}";
        $categories=$this->Product->query($sql);
        $categoriesIds=[$cat_id];
        if (!empty($categories)) {
            foreach ($categories as $key => $category) {
                $categoriesIds[]=$category['child']['id'];
            }
        }
        return $categoriesIds;
    }
    function owner_timeline($id = false) {
        $this->set('invoice_id', $id);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
//        $invoice = $this->Invoice->getInvoice($id, array('Invoice.type' => 0));
//
//
//        if (!$invoice) {
//            $this->flashMessage(__('Invoice not found', true));
//            $this->redirect($this->referer(array('action' => 'index'), true));
//        }

        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Product', array('secondary_id' => $id));


        $action_list1 = $timeline->getProductActionsList();
        $action_list2 = $timeline->getStockActionsList();
        $action_list = array_merge($action_list1, $action_list2);
        $timeline->init(array('secondary_id' => $id), $action_list);
        $data = $timeline->getDataArray();
//        debug($data1);
//
//        $timeline = new Timeline('Product', array('secondary_id' => $id));
//        $action_list2 = $timeline->getStockActionsList();
//        $timeline->init(array('secondary_id' => $id), $action_list2);
//        $data2 = $timeline->getDataArray();
//        // debug($data2);
//        $count_start = $data1['pagination']['total'] + $data2['pagination']['total'];
//        debug($count_start);
//        foreach ($data2['data'] as $key => $value) {
//            debug($value);
//            foreach ($value as $key2 => $value2) {
//                $count_start++;
//
//                $data2['data'][$key][$count_start] = $value2;
//                unset($data2['data'][$key][$key2]);
//            }
//        }
//        foreach ($data2['data'] as $key => $value) {
//            if (isset($data1['data'][$key])) {
//                foreach ($data1['data'][$key] as $key3 => $value3) {
//                    $data2['data'][$key][$key3] = $value3;
//                }
//            }
//        }
//        if(is_array($data2['data']) and is_array($data1['data'])){
//        $data2['data'] = $data2['data'] + $data1['data'];
//        }elseif(is_array($data1['data'])){
//        $data2=$data1;
//        }
//
//
//
//        $action_list = array_merge($action_list1, $action_list2);
//
//
        $this->set('data', $data);
        $this->loadModel('ActionLine');

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $invoice_actions[$key] = $action;
            }
        }
		debug ( $data);
        $this->set('actions', $invoice_actions);

        //$this->set('jsonParams', $timeline->jsonAdapter(), false);
    }

    function owner_store_summary()
    {
        $getUrl = $_GET['url'];
        unset($_GET['url']);
        $items = [];
        $this->loadModel('StockTransaction');
        $types = $this->StockTransaction->getSources();

        $all_cats = $types;
        $cat_param = StockTransaction::getGroupedSources();
        $income_cats = [];
        $outcome_cats = [];
        foreach ($cat_param as $key => $cat) {
            if ($cat['transaction_type'] == StockTransaction::TRANSACTION_OUT)
                $outcome_cats[$key] = $cat;
            else
                $income_cats[$key] = $cat;
        }
        $income_cats['TotalIN'] = ['label' => __('Total',true)];
        $outcome_cats['TotalOUT'] = ['label' => __('Total',true)];
        $this->loadModel('ItemPermission');
        $allowedStores = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE, ItemPermission::PERMISSION_VIEW,null,false,true);
        if (!empty($_GET)) {
            $time_condition = "";
            if (isset ($this->params['url']['date_range_selector']) && $this->params['url']['date_range_selector'] == "lastyear") {
                $time_condition = " AND YEAR(ST.`received_date`) = YEAR(CURRENT_DATE - INTERVAL 1 Year) ";
            } else if (isset ($this->params['url']['date_range_selector']) && $this->params['url']['date_range_selector'] == "lastmonth") {
                $time_condition = " AND YEAR(ST.`received_date`) = YEAR(CURRENT_DATE - INTERVAL 1 YEAR) AND MONTH(ST.`received_date`) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH) ";
            } else {
                if (isset ($this->params['url']['date_range_from']) && $this->params['url']['date_range_from'] != "") {
                    $time_condition .= " AND ST.`received_date` >= '" . $this->Product->formatDate($this->params['url']['date_range_from']) . "'";
                }
                if (isset ($this->params['url']['date_range_to']) && $this->params['url']['date_range_to'] != "") {
                    $time_condition .= " AND ST.`received_date` <= '" . $this->Product->formatDate($this->params['url']['date_range_to']) . " 23:59:59'";
                }
            }

            $store_where = " AND ST.store_id IN ( ".implode(',',array_keys($allowedStores))." ) ";
            if ($this->params['url']['store'] != "") {
                $store_where = "  AND ST.store_id = {$this->params['url']['store'] } ";
            }
            if ($this->params['url']['product_id'] != "") {
                if (substr($this->params['url']['product_id']??'', 0, 1) === 'g') {// if the product is item group record
                    $itemGroupId = intval(str_replace("g","",$this->params['url']['product_id']));
                    $store_where .= "  AND P.item_group_id = " . $itemGroupId. " ";
                }else{
                    $store_where .= "  AND P.id = " . intval($this->params['url']['product_id']) . " ";
                }
            }

            $product_category = " WHERE ";
            if ($this->params['url']['category'] != "") {

                $product_category = " LEFT JOIN items_categories IC ON IC.item_id = ST.product_id WHERE IC.item_type = 1 AND IC.category_id = ".intval($this->params['url']['category'])." AND ";
            }
            $category_where = "";
            if ($this->params['url']['product_type'] != "" && substr($this->params['url']['product_type'], 0, 4) != "cat_") {
                $category_where = "  AND ST.source_type = {$this->params['url']['product_type']}";
            }
            $groupedSources = StockTransaction::getGroupedSources();
            $groupedSourcesFields = array_keys($groupedSources);
            $query = "SELECT P.id, P.category, P.name, SUM(ST.quantity) AS quantity, ST.source_type, ST.transaction_type FROM stock_transactions ST
              LEFT JOIN products P ON P.id = ST.product_id {$product_category}    ST.status = 4 AND ST.ignored = 0  {$time_condition} {$store_where} {$category_where} AND  P.status != 1  GROUP BY ST.product_id,ST.source_type,ST.transaction_type";

            $data = [];
            $stockTransactions = $this->StockTransaction->flat_query_results($query);
            foreach ($stockTransactions as $stockTransaction) {
                if (!isset($data[$stockTransaction['id']])) {
                    $data[$stockTransaction['id']] = ['product_id' => $stockTransaction['id'], 'product_name' => $stockTransaction['name']];
                    foreach ($groupedSourcesFields as $groupedSourcesField) {
                        $data[$stockTransaction['id']][$groupedSourcesField] = 0;
                    }
                    $data[$stockTransaction['id']]['TotalIN'] = 0;
                    $data[$stockTransaction['id']]['TotalOUT'] = 0;
                    $data[$stockTransaction['id']]['Total'] = 0;
                }
                $groupSource = StockTransaction::getSourceGroup($stockTransaction['source_type'],$stockTransaction['transaction_type']);
                $data[$stockTransaction['id']][$groupSource] += $stockTransaction['quantity'];
                if ($stockTransaction['transaction_type'] == StockTransaction::TRANSACTION_OUT) {
                    $data[$stockTransaction['id']]['TotalOUT'] += $stockTransaction['quantity'];
                    $data[$stockTransaction['id']]['Total'] += $stockTransaction['quantity'];
                } else {
                    $data[$stockTransaction['id']]['TotalIN'] += $stockTransaction['quantity'];
                    $data[$stockTransaction['id']]['Total'] += $stockTransaction['quantity'];
                }
            }
            $this->set('enable_multi_units', settings::getValue(InventoryPlugin, 'enable_multi_units'));
            if ($this->viewVars['enable_multi_units']) {
                foreach ($data as $k => $i) {
                    $data[$k]['factor'] = $this->Product->get_default_factor($i['id']);
                }
            }
        }
        $_GET['url'] = $getUrl;

 
//        $this->set('products', $this->Product->find('list', ['recursive' => -1, 'conditions' => ['track_stock' => 1]]));
        $this->set('stores', $allowedStores);
        $this->set('items', $data);
        $this->set ( 'outcome_cats' , $outcome_cats ) ;
        $this->set ( 'income_cats' , $income_cats ) ;
        $this->set ('cat_param' ,$cat_param);
        $this->set('productTypes', $all_cats);
        $this->set('title_for_layout', __('Inventory Transactions Summary', true));
    }

    // TODO remove deprecated functino
	function old_owner_store_summary ( ) {


                if (ifPluginActive(WorkOrderPlugin)){
                    $this->loadModel('WorkOrder');
                    $this->set ( 'WorkOrders', $this->WorkOrder->get_work_orders ( ['status' => [WorkOrder::STATUS_OPEN ,WorkOrder::STATUS_CLOSED ] ] )) ;
                }
                $getUrl = $_GET['url'];
                unset($_GET['url']);
                $items = [] ;
                $types = [
                    StockTransaction::SOURCE_MANUAL => __("Manual" , true ),
                    StockTransaction::SOURCE_INVOICE => __("Invoice" , true ),
                    StockTransaction::SOURCE_PO => __("Purchase Invoice" , true ),
                    StockTransaction::STATUS_TRANSFER => __("Transfer" , true ),
                ];

                $all_cats = $types;
                $this->loadModel ('TransactionCategory' );
                $cat_list= $this->TransactionCategory->find ( 'list' , ['conditions' => ['deleted = 0']]);

                $cat_param = false ;
                foreach ($cat_list as $k => $v ) {
                    $all_cats["cat_".$k] = $v;
                }
                if ( !empty($_GET)){
                    $time_condition = "" ;
                    if ( isset ($this->params['url']['date_range_selector']) &&$this->params['url']['date_range_selector'] == "lastyear" ){
                        $time_condition =" and YEAR(st.`received_date`) = YEAR(CURRENT_DATE - INTERVAL 1 Year) ";
                    }else if ( isset ($this->params['url']['date_range_selector']) &&$this->params['url']['date_range_selector'] == "lastmonth" ){
                        $time_condition =" and YEAR(st.`received_date`) = YEAR(CURRENT_DATE - INTERVAL 1 YEAR) AND MONTH(st.`received_date`) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH) ";
                    }else {
                        if ( isset ($this->params['url']['date_range_from']) && $this->params['url']['date_range_from'] != ""   )
                        {
                            $time_condition .= " and st.`received_date` >= '". $this->Product->formatDate($this->params['url']['date_range_from'] )."'" ;
                        }
                        if ( isset ($this->params['url']['date_range_to']) && $this->params['url']['date_range_to'] != "" )
                        {
                            $time_condition .= " and st.`received_date` <= '". $this->Product->formatDate($this->params['url']['date_range_to'] )."'" ;
                        }
                    }



                    if ( !empty($this->params['url']['work_order_id']) ||  isset ($this->params['url']['product_type']) && substr ($this->params['url']['product_type'], 0, 4) != "cat_"&& substr ($this->params['url']['product_type'], 0, 4) != "")
                    {
                        $income_cats= [];
                        $outcome_cats= [];
                    }else {
                        $q_income = "select tc.id , tc.name   from stock_transactions st
                    inner join transaction_categories tc on tc.id = st.transaction_category_id
                    where ignored <> 1 $time_condition and transaction_type = 1  group by tc.id";
                        $income_cats = $this->flat_query_results ($q_income);

                        $q2_outcome = "select tc.id ,tc.name   from stock_transactions st
                    inner join transaction_categories tc on tc.id = st.transaction_category_id
                    where ignored <> 1 $time_condition and transaction_type = 2  group by tc.id" ;
                        $outcome_cats =$this->flat_query_results ($q2_outcome );

                        foreach ( $income_cats as $k => $v ){
                            //$all_cats ["cat_".$v['id']] = $v['name'] ;
                            if ( isset ($this->params['url']['product_type']) && substr ($this->params['url']['product_type'], 0, 4) == "cat_"  ){
                                $cat_id =intval(substr ($this->params['url']['product_type'], 4) );
                                $cat_param = true ;
                                if ( $cat_id>0 && $cat_id != $v['id']){
                                    unset ($income_cats[$k]);
                                }
                            }
                        }
                        foreach ( $outcome_cats as $k => $v ){
                            //$all_cats ["cat_".$v['id']] = $v['name'] ;
                            if ( isset ($this->params['url']['product_type']) && substr ($this->params['url']['product_type'], 0, 4) == "cat_"  ){
                                $cat_id =intval(substr ($this->params['url']['product_type'], 4) );
                                $cat_param = true ;
                                if ( $cat_id>0 && $cat_id != $v['id']){
                                    unset ($outcome_cats[$k]);
                                }
                            }
                        }
                    }

                    $this->loadModel('Requisition');
                    $requisition_sources = [];
                    foreach($this->Requisition->stocktransaction_sources as $k => $v){
                        $requisition_sources[$v['StockTransactionSource']][] = $k ;
                    }
                    $income_fields = "" ;
                    foreach ( $income_cats as $a ){
                        $income_fields .=" sum(case when st.transaction_category_id = {$a['id']}  and (ref_id is  null or ref_id = 0 )  and transaction_type = 1  then quantity else 0 end) as income_{$a['id']} ," ;
                    }

                    $outcome_fields = "" ;
                    foreach ( $outcome_cats as $a ){
                        $outcome_fields .=" sum(case when st.transaction_category_id = {$a['id']}  and (ref_id is  null or ref_id = 0 ) and transaction_type = 2  then quantity else 0 end) as outcome_{$a['id']} ," ;
                    }

                    $store_where = "" ;
                    if ($this->params['url']['store'] != "" )
                    {
                        $store_where = "  and ssb.store_id = {$this->params['url']['store'] } ";
                    }
                    if ($this->params['url']['product_id'] != "" )
                    {
                        $store_where .= "  and pr.id = ".intval($this->params['url']['product_id'])." ";
                    }
                    $category_where = "";
                    if ($this->params['url']['product_type'] != "" &&substr ($this->params['url']['product_type'], 0, 4) != "cat_" )
                    {
                        $category_where = "  and (st.source_type = {$this->params['url']['product_type'] } OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION ." AND rq.order_type in (".implode(",",$requisition_sources[$this->params['url']['product_type']]) .") ) )";
                    }
                    $requisitions_wo_po_where = " 1 ";
                    $requisitions_work_order_where = " 1 ";
                    if ( ifPluginActive(WorkOrderPlugin)&& !empty ( $this->params['url']['work_order_id']))
                    {
                        $invoices_where = "invoices.work_order_id = ".intval($this->params['url']['work_order_id']);
                        $requisitions_wo_po_where = "po_rq.work_order_id = ".intval($this->params['url']['work_order_id']);
                        $requisitions_work_order_where = "inv_rq.work_order_id = ".intval($this->params['url']['work_order_id']);
                        $purchase_orders_where = "po.work_order_id = ".intval($this->params['url']['work_order_id']);
                        $work_param = true ;
                    }else { $invoices_where = $purchase_orders_where = '1'; $work_param = false ; }

                    $requisitions_pr_where = " OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION." AND rq.order_type = ".Requisition::ORDER_TYPE_PURCHASE_REFUND." AND $requisitions_wo_po_where ) ";
                    $requisitions_po_where = " OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION." AND rq.order_type = ".Requisition::ORDER_TYPE_PURCHASE_ORDER." AND $requisitions_wo_po_where ) ";

                    $requisitions_rr_where = " OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION." AND rq.order_type = ".Requisition::ORDER_TYPE_INVOICE_REFUND." AND $requisitions_work_order_where ) ";
                    $requisitions_cn_where = " OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION." AND rq.order_type = ".Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE." AND $requisitions_work_order_where  ) ";
                    $requisitions_invoices_where = " OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION." AND rq.order_type = ".Requisition::ORDER_TYPE_INVOICE." AND $requisitions_work_order_where  ) ";

                    $manual_requisitions_where = " OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION." AND rq.order_type in (".Requisition::ORDER_TYPE_MANUAL_INBOUND." , ".Requisition::ORDER_TYPE_MANUAL_OUTBOUND."  ) ) ";
                    $requisitions_transfer_where = " OR (st.source_type = ".StockTransaction::SOURCE_REQUISITION." AND rq.order_type = ".Requisition::ORDER_TYPE_TRANSFER_REQUISITION." ) ";

                    $requisitions_join = " left join requisitions rq on st.order_id = rq.id AND st.source_type = ".StockTransaction::SOURCE_REQUISITION ." ";
                    $requisitions_invoices_join = " left join invoices inv_rq on inv_rq.id = rq.order_id and rq.order_type in (".Requisition::ORDER_TYPE_INVOICE." , ".Requisition::ORDER_TYPE_INVOICE_REFUND." , ".Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE."  ) " ;
                    $requisitions_po_join = " left join purchase_orders po_rq on po_rq.id = rq.order_id and rq.order_type in (".Requisition::ORDER_TYPE_PURCHASE_ORDER." , ".Requisition::ORDER_TYPE_PURCHASE_REFUND."  ) " ;

                    $this->set ( 'work_param' , $work_param ) ;
                    $query = "select (ssb.balance ) as total_stock_balance , pr.stock_balance ,pr.id,pr.name ,
                        sum(case when ((st.order_id is not null and st.order_id <> 0 ) and po.id is not null and st.source_type= ".StockTransaction::SOURCE_PO." and $purchase_orders_where ) $requisitions_po_where and transaction_type = 1 then quantity else null end) as pos ,
                        sum(case when ((st.order_id is not null and st.order_id <> 0 ) and invoices.`type` = ".Invoice::Credit_Note." and st.source_type= ".StockTransaction::SOURCE_CN." and $invoices_where ) $requisitions_cn_where and transaction_type = 1  then quantity else null end) as pos_cn ,
                        sum(case when ((st.order_id is not null and st.order_id <> 0 ) and invoices.`type` = ".Invoice::Refund_Receipt." and st.source_type= ".StockTransaction::SOURCE_RR." and $invoices_where ) $requisitions_rr_where and transaction_type = 1 then quantity else null end) as pos_refund ,
                        $income_fields
                sum(case when (st.source_type = ".StockTransaction::STATUS_TRANSFER." $requisitions_transfer_where ) /*and (ref_id is null or ref_id = 0 ) and (transaction_category_id is null or transaction_category_id = 0 )*/ and transaction_type = 1 and ".($work_param ? 0 : 1)." then st.quantity else null end) as pos_transfers,
		sum(case when (st.source_type in ( ".StockTransaction::SOURCE_MANUAL.",".StockTransaction::SOURCE_BUNDLE_RECON." ) $manual_requisitions_where ) /*and (ref_id is null or ref_id = 0 )*/ and (transaction_category_id is null or transaction_category_id = 0 ) and transaction_type = 1 and ".($work_param ? 0 : 1)." then st.quantity else null end) as pos_others,
		sum(case when ((st.order_id is not null and st.order_id <> 0 )  and invoices.`type` = ".Invoice::Invoice." and st.source_type= ".StockTransaction::SOURCE_INVOICE." and $invoices_where) $requisitions_invoices_where /*and transaction_type = 2*/ then quantity else null end) as mns,
		sum(case when ((st.order_id is not null and st.order_id <> 0 )  and st.source_type= ".StockTransaction::SOURCE_PR." and $purchase_orders_where ) $requisitions_pr_where /*and transaction_type = 2*/ then quantity else null end) as purchase_refunds
                , $outcome_fields
		sum(case when (st.source_type = ".StockTransaction::STATUS_TRANSFER." $requisitions_transfer_where ) /*and (ref_id is null or ref_id = 0 ) and (transaction_category_id is null or transaction_category_id = 0 ) */ and transaction_type = 2 and ".($work_param ? 0 : 1)." then st.quantity else null end) as mns_transfers,
		sum(case when (st.source_type in ( ".StockTransaction::SOURCE_MANUAL.",".StockTransaction::SOURCE_BUNDLE.") $manual_requisitions_where ) /*and(ref_id is null or ref_id = 0 )*/ and (transaction_category_id is null or transaction_category_id = 0 ) and transaction_type = 2 and ".($work_param ? 0 : 1)." then st.quantity else null end) as mns_others
		   from products pr
		left join stock_transactions st on st.product_id = pr.id
                $requisitions_join
                $requisitions_invoices_join
                $requisitions_po_join
                left join store_stock_balance ssb on ssb.product_id = pr.id and ssb.store_id = st.store_id
                left join invoices  on invoices.id = st.order_id
                left join purchase_orders po on po.id = st.order_id
                where (st.status = 4 or ssb.balance is null ) $category_where $store_where $time_condition and ( st.ignored = 0 OR st.ignored is null ) and track_stock <> 0 and (pr.status <> ". ProductStatusUtil::STATUS_INACTIVE ." or ssb.balance is null or pr.status is null ) group by pr.id  ";
                    $items = $this->flat_query_results(  $query);
                    debug($query);
                    $this->set ( 'enable_multi_units' , settings::getValue(InventoryPlugin, 'enable_multi_units') );
                    if ( $this->viewVars['enable_multi_units'] ){
                        foreach ( $items as $k => $i ) {
                            $items[$k]['factor']  = $this->Product->get_default_factor ( $i['id'] ) ;
                        }
                    }
                }
                $_GET['url'] = $getUrl ;

                $this->loadModel('ItemPermission');
        $this->set('products' ,$this->Product->find('list' , ['recursive'=> -1 , 'conditions' => ['track_stock' => 1 ] ]) );
		$this->set('stores', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW));
		$this->set ( 'items' , $items ) ;
		$this->set ( 'outcome_cats' , $outcome_cats ) ;
		$this->set ( 'income_cats' , $income_cats ) ;
                $this->set ('cat_param' ,$cat_param);
                //debug ( $all_cats ) ;
                $this->set ( 'productTypes' , $all_cats );
	}
        function owner_products_profit (  ) {
        if (!check_permission(View_His_Own_Reports)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->set('default_currency',getCurrentSite('currency_code'));
            set_time_limit(999);
            ini_set('memory_limit','2G');
            $this->set('title_for_layout',  h(__('Product Sales Profit', true)));
            App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
            $this->loadModel ( 'StockTransaction');
            //Initializing all report types with all needed fields
            $report_types = [
                'client' => ['group_by' => 'client_id' , 'column_name' => "Client name" , 'select_id'=> 'client_id' , 'select_name' => 'business_name','view_url' => Router::url(['controller' => 'clients' ,'action' => 'view'])],
                'product' => ['group_by' => 'id' , 'column_name' => "Product Name" , 'select_id' => 'id' , 'select_name' => 'name' , 'view_url' => Router::url(['controller' => 'products' ,'action' => 'view'])],
                'staff' => ['group_by' => 'staff_id' , 'column_name' => "Staff name" , 'select_id' => 'staff_id' , 'select_name' => 'staff_name' , 'view_url' => Router::url(['controller' => 'staffs' ,'action' => 'edit'])],
                'sales_person' => ['group_by' => 'staff_id' , 'column_name' => "Sales Person" , 'select_id' => 'staff_id' , 'select_name' => 'staff_name' , 'view_url' => Router::url(['controller' => 'staffs' ,'action' => 'edit'])]
                ];
            if ( !empty ( $this->params['url']['type'] ) && in_array ($this->params['url']['type'] , array_keys ($report_types ) ) ) {
                $type = $this->params['url']['type'];
            }else {
                $type = 'product';
            }
                //Getting time condition to concatentate to the query
                $time_condition = "" ;
	            if (empty($this->params['url']['date_range_selector']) && empty($this->params['url']['date_range_from']) && empty($this->params['url']['date_range_to'])) {
		            $this->params['url']['date_range_selector'] = "lastmonth";
	            }
                if ( isset ($this->params['url']['date_range_selector']) &&$this->params['url']['date_range_selector'] == "lastyear" ){
                    $time_condition =" and YEAR(st.`received_date`) = YEAR(CURRENT_DATE - INTERVAL 1 Year) ";
                }else if ( isset ($this->params['url']['date_range_selector']) &&$this->params['url']['date_range_selector'] == "lastmonth" ){
                    $time_condition =" and date_format(st.`received_date`,'%Y-%m')=date_format((CURRENT_DATE - INTERVAL 1 MONTH),'%Y-%m') ";
                }else {
                    if ( isset ($this->params['url']['date_range_from']) && $this->params['url']['date_range_from'] != ""   )
                    {
                        $time_condition .= " and st.`received_date` >= '". $this->Product->formatDate($this->params['url']['date_range_from'] )." 00:00:00'" ;
                    }
                    if ( isset ($this->params['url']['date_range_to']) && $this->params['url']['date_range_to'] != "" )
                    {
                        $time_condition .= " and st.`received_date` <= '". $this->Product->formatDate($this->params['url']['date_range_to'] )." 23:59:59'" ;
                    }
                }

                if(isset($this->params['url']['category']) && $this->params['url']['category'] != ""){
                    $ItemsCategory = GetObjectOrLoadModel('ItemsCategory');
                    $categoryProducts = $ItemsCategory->find('list', ['fields' => ['id','item_id'], 'conditions' => [
                        'ItemsCategory.category_id' => $this->params['url']['category'],
                        'ItemsCategory.item_type' => ItemsCategory::ITEM_TYPE_PRODUCT
                        ]]);
                    if($categoryProducts) {
                        $category_condition = "AND pr.id in (".implode(',', $categoryProducts).")";
                    } else {
                        // show nothing if use select empty category
                        $categoryProducts=[0,0];
                        $category_condition = "AND pr.id in (".implode(',', $categoryProducts).")";
                    }
                } else {
                    $category_condition = "";
                }

            $itemGroupCondition = "";
      
            if(isset($this->params['url']['item_group_id']) && $this->params['url']['item_group_id'] != ""){
                $selectedItemGroup = addslashes($this->params['url']['item_group_id']);
                $itemGroupCondition = "AND pr.item_group_id = '".intval(str_replace("g", "",$selectedItemGroup))."'";
            }

	        $brandCondition = "";
	        if (isset($this->params['url']['brand']) && $this->params['url']['brand'] != "") {
				$selectedBrand = addslashes($this->params['url']['brand']);
				$brandCondition = "AND pr.brand = '$selectedBrand'";
	        }

            $staffCondition = "";
            if (isset($this->params['url']['staff_id']) && is_array($this->params['url']['staff_id']) && !empty($this->params['url']['staff_id']) && !empty($this->params['url']['staff_id'][0])) {
                if($type == 'sales_person'){
                    $staffCondition = "AND (SP_IS_invoice.staff_id IN( ".implode(',',$this->params['url']['staff_id']).") OR SP_IS_items.staff_id IN( ".implode(',',$this->params['url']['staff_id'])."))";
                }else{
                    $staffCondition = "AND staffs.id IN( ".implode(',',$this->params['url']['staff_id']).")";
                }
            }

                $branchCondition = "";
                if (ifPluginActive(BranchesPlugin)) {
                    $allowedBranches = getStaffBranchesIDsSuspended();
                    $branchesData = $this->params['url']['branch_id'];
                    if (isset($branchesData) && !empty($branchesData)) {
                        if (!is_array($branchesData))
                            $branchesData = [$branchesData];
                        foreach ($branchesData as $key => $value) {
                            if (!in_array($value, $allowedBranches))
                                unset($branchesData[$key]);
                        }
                    }
                    if (empty($branchesData))
                        $branchesData = $allowedBranches;
                    $branchCondition = " and st.branch_id IN (" . implode(',', $branchesData) . ')';
                }
                //Sort and sort order to be passed to dataTable.
                $sort = 0 ;
                if ( !empty ( $this->params['url']['sort'])){
                    $sort = intval ( $this->params['url']['sort']);
                }
                $sort_order = "ASC";
                if ( !empty ( $this->params['url']['sort_order'])){
                    $sort_order = $this->params['url']['sort_order'] =='desc' ? "DESC" : "ASC";
                }
                $this->set( 'sort_order' , $sort_order );
                $this->set( 'sort' , $sort );

                //Getting calculation method : either AVG or FIFO
                App::import('Vendor', 'settings');
                $calculation_method = StockTransaction::CALC_METHOD_AVERAGE;
                if ( empty ( $calculation_method )){
                        $calculation_method = StockTransaction::CALC_METHOD_AVERAGE;
                }
                $this->set ( 'calculation_method' , $calculation_method ) ;
                $invoice_case= " st.source_type in( ".StockTransaction::SOURCE_INVOICE.",".StockTransaction::SOURCE_RQ_INVOICE." ) ";
                $refund_case = " st.source_type in( ".StockTransaction::SOURCE_CN.",".StockTransaction::SOURCE_RR.",".StockTransaction::SOURCE_RQ_INVOICE_REFUND.",".StockTransaction::SOURCE_RQ_INVOICE_CREDIT_NOTE.") ";
                $all_case = " st.source_type in( ".StockTransaction::SOURCE_CN.",".StockTransaction::SOURCE_RR.",".StockTransaction::SOURCE_INVOICE.") ";
                //Handle requisitions
                $req_case = " st.source_type in( ".StockTransaction::SOURCE_RQ_INVOICE_CREDIT_NOTE.",".StockTransaction::SOURCE_RQ_INVOICE_REFUND.",".StockTransaction::SOURCE_RQ_INVOICE.") ";
                $requisition_join = " left join requisitions rq on rq.id = st.order_id and st.source_type in ( ".StockTransaction::SOURCE_RQ_INVOICE_CREDIT_NOTE.",".StockTransaction::SOURCE_RQ_INVOICE_REFUND.",".StockTransaction::SOURCE_RQ_INVOICE.")";
                $invoices_join = " join invoices on (invoices.id = st.order_id and $all_case) or (invoices.id = rq.order_id and $req_case) ";
                list($staff_select_fields, $staffsJoin, $invoice_items_join, $pos_invoices_join, $pos_staff_join) = $this->getStaffSelectFieldsAndJoinForProfitReport($type);
               //Getting all stocktransactions with all their glory
                $query = "( select st.purchase_price , st.quantity , st.product_id, st.id as stock_id , "
                        . "sum( case when $refund_case then st.discount else 0 END ) as discount_rr , sum( case when $invoice_case then st.discount else 0 END ) as discount,st.currency_code,average_price , $staff_select_fields ,clients.id as client_id , clients.business_name, clients.client_number  , pr.stock_balance ,pr.id,pr.name ,
                        coalesce(sum(case when $refund_case then st.quantity else null end),0) as refunds ,
                        coalesce(sum(case when $refund_case then (st.quantity*price) else null end),0) as refunds_price ,

                        coalesce(sum(case when $invoice_case  then st.quantity else null end),0) as sales,
                        coalesce(sum(case when $invoice_case  then (st.quantity*price) else 0 end),0) as sales_price,

                        coalesce(sum(case when $all_case then st.purchase_price else 0 end ),0) as sum_purchase_price,
                        average_price  as sum_avg_price ,
                        sum(case when $all_case then (st.purchase_price * st.quantity) else 0 end ) as sum_all_averages
                        from products pr
                        left join stock_transactions st on st.product_id = pr.id
                        $requisition_join
                        $invoices_join
                        $invoice_items_join
                        $staffsJoin
                        left join clients  on clients.id = invoices.client_id
                        where st.`received_date` IS NOT NULL AND st.`received_date` != '0000-00-00 00:00:00' and (st.status = 4 ) and st.ignored <> 1 $category_condition $time_condition $branchCondition $brandCondition $itemGroupCondition $staffCondition and pr.track_stock <> 0 and (pr.status <> ". ProductStatusUtil::STATUS_INACTIVE ." or pr.status is null )
                                group by st.id ) ";

                if (ifPluginInstalled(PosPlugin)) {
                    $pos_inbound = " st.source_type in( ".StockTransaction::SOURCE_RQ_POS_INBOUND." ) ";
                    $pos_outbound = " st.source_type in( ".StockTransaction::SOURCE_RQ_POS_OUTBOUND." ) ";
                    $pos_case = " st.source_type in( ".StockTransaction::SOURCE_RQ_POS_OUTBOUND.",".StockTransaction::SOURCE_RQ_POS_INBOUND.") ";
                    $query .= "union all
                    ( select st.purchase_price , st.quantity , st.product_id , st.id as stock_id , "
                        . "sum( case when $pos_inbound then st.discount else 0 END ) as discount_rr , sum( case when $pos_outbound then st.discount else 0 END ) as discount,st.currency_code,average_price, $staff_select_fields ,0 as client_id , 'unknown client' as business_name, 0 as client_number  , pr.stock_balance ,pr.id,pr.name ,
                        coalesce(sum(case when $pos_inbound then st.quantity else null end),0) as refunds ,
                        coalesce(sum(case when $pos_inbound then (st.quantity*price) else null end),0) as refunds_price ,

                        coalesce(sum(case when $pos_outbound  then st.quantity else null end),0) as sales,
                        coalesce(sum(case when $pos_outbound  then (st.quantity*price) else 0 end),0) as sales_price,

                        coalesce(sum(case when $all_case then st.purchase_price else 0 end ),0) as sum_purchase_price,
                        average_price  as sum_avg_price ,
                        sum(case when $pos_case then (st.purchase_price * st.quantity) else 0 end ) as sum_all_averages
                        from products pr left join stock_transactions st on st.product_id = pr.id
                        join requisitions req on req.id = st.order_id and $pos_case
                        left join pos_shifts pos on req.order_id = pos.id
                        $pos_invoices_join
                        $pos_staff_join
                        where st.`received_date` IS NOT NULL AND st.`received_date` != '0000-00-00 00:00:00' and (st.status = 4 ) and st.ignored <> 1 $category_condition $time_condition $branchCondition $brandCondition $itemGroupCondition $staffCondition and pr.track_stock <> 0 and (pr.status <> " . ProductStatusUtil::STATUS_INACTIVE . " or pr.status is null )
                        group by st.id ) ";
                }
                debug($query);
                $items = [];
                if(!isset($this->params['url']['threshold_limit'])){
                    $items = $this->Product->flat_query_results( $query );
                    $this->set('records_count', is_countable($items) ? count($items) : 0);
                }

                if(!$this->params['url']['is_summary'] && !isset($this->params['url']['threshold_limit'])){
                    $this->displayLimitThreshold($items,false, 'v2');
                }
                // dd($items);
                $default_currency = $this->Product->get_default_currency ( ) ;
                $this->Product->get_default_currency ( ) ;
                $new_items = $all_currencies = [] ;
                if(is_array($items)) {
                    $all_currencies = array_unique (array_column ($items , 'currency_code') ) ;
                }
                $currency_exchanges = [] ;
                foreach ( $all_currencies as $c  )
                {
                    $rate_val = CurrencyConverter::index($c, $default_currency, date('Y-m-d'));
                    $currency_exchanges[$c] = $rate_val ;
                }
                foreach ( $items as $i ) {
                    $sort_by = $i[$report_types [$type]['group_by']] ;

                    $new_items[$sort_by]['id'] = $i['id'];
                    $new_items[$sort_by]['client_id'] = $i['client_id'];
                    $new_items[$sort_by]['staff_id'] = $i['staff_id'];

                    $new_items[$sort_by]['staff_name'] = $i['staff_name'];
                    $new_items[$sort_by]['business_name'] = $i['business_name'];
                    $new_items[$sort_by]['client_number'] = $i['client_number'];
                    $new_items[$sort_by]['name'] = $i['name'];

                    if (!isset($new_items[$sort_by]['purchase_price_sum'])) $new_items[$sort_by]['purchase_price_sum'] = 0;
                    if (!isset($new_items[$sort_by]['sales'])) $new_items[$sort_by]['sales'] = 0;
                    if (!isset($new_items[$sort_by]['refunds'])) $new_items[$sort_by]['refunds'] = 0;
                    if (!isset($new_items[$sort_by]['discount'])) $new_items[$sort_by]['discount'] = 0;
                    if (!isset($new_items[$sort_by]['discount_rr'])) $new_items[$sort_by]['discount_rr'] = 0;

                    if (!isset($new_items[$sort_by]['refunds_price'])) $new_items[$sort_by]['refunds_price'] = 0;
                    if (!isset($new_items[$sort_by]['sales_price'])) $new_items[$sort_by]['sales_price'] = 0;
                    if (!isset($new_items[$sort_by]['sum_all_averages'])) $new_items[$sort_by]['sum_all_averages'] = 0;

                    $new_items[$sort_by]['purchase_price_sum'] += $i['purchase_price'] * $i['quantity'];
                    $new_items[$sort_by]['sales'] += $i['sales'];
                    $new_items[$sort_by]['refunds'] += $i['refunds'];
                    $rate_val = $currency_exchanges[$i['currency_code']];
                    $new_items[$sort_by]['discount'] += $rate_val*$i['discount']*$i['quantity'];
                    $new_items[$sort_by]['discount_rr'] += $rate_val*$i['discount_rr']*$i['quantity'];

                    $new_items[$sort_by]['refunds_price'] += $rate_val * $i['refunds_price'] ;
                    $new_items[$sort_by]['sales_price']+= $rate_val * $i['sales_price'] ;
                    $new_items[$sort_by]['sum_all_averages']= $new_items[$sort_by]['purchase_price_sum'];
                }
                $this->set ( 'enable_multi_units' , settings::getValue(InventoryPlugin, 'enable_multi_units') );
                if ( $this->viewVars['enable_multi_units'] && $type == "product"){
                    foreach ( $new_items as $product_id => $k ) {
                        $new_items[$product_id]['factor']  = $this->Product->get_default_factor ( $product_id ) ;
                    }
                }
                if($sort > 0){
                    $new_items = $this->sortItemsByCustomKey($new_items, $sort, $sort_order);
                }

                $this->loadModel('Category');
                $products_categories = $this->Category->find('list');
				$this->set('products_brands', $this->Product->getBrandsList(false));
                $this->loadModel('Staff');
                $this->set('staffs', $this->Staff->getStaffList());
                $this->set ( 'default_currency', $default_currency);
                $this->loadModel('ItemPermission');
                $this->set ( 'stores', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW) );
                $this->set ( 'items' , $new_items ) ;
                $this->set ( 'report_types' , $report_types ) ;
                $this->set('products_categories', $products_categories);
                $this->set ( 'reports_types' , array_keys ($report_types ) ) ;
                $this->set ( 'type' , $type ) ;
	}

	private function flat_query_results  ( $query ) {
		$result = $this->Product->query ($query) ;
		foreach ( $result as $k => $r ){
			$temp  = array_values ( $r)  ;
			$temp2 = [] ;
			foreach ( $temp as $k2 => $r2 )
			{
				$temp2= array_merge ( $temp2 , $r2 );

			}
			$result[$k] = $temp2 ;
		}
		return $result ;
	}

	public function owner_get_transaction_categories () {
		if ( $this->RequestHandler->isAjax() ){
			 $this->loadModel ('TransactionCategory') ;
			$all = $this->TransactionCategory->find ('list' );
			die (json_encode($all));
		}die ;
	}

    public function owner_get_product_store_stock ( $product_id, $store_id =null ) {

        $this->loadModel ( 'StoreStockBalance');
        $balance = $this->StoreStockBalance->find('first' , ['conditions' => ['product_id' => $product_id , 'store_id' => $store_id]]);
        if ( empty ( $balance ) ) {
            die ( '0' ) ;
        }else {
            die ( $balance['StoreStockBalance']['balance']);
        }
    }

    public function owner_get_auto_barcode($itemm_type, $item_id = false)
    {
        App::import('Vendor', 'OIBarcode', array('file' => 'OIBarcode/OIBarcode.php'));
        die( OIBarcode::getAutoBarcodeForProduct($itemm_type,$item_id) ) ;
    }
    function owner_update_profit_margin(){
        if (!check_permission( [PermissionUtil::Adjust_Product_Inventory])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if (!empty($_POST['submit_btn'])&&!empty($_POST['ids']) && !empty($this->data['Product']['profit_margin']) ) {
            $products = $this->Product->find('all' ,['recursive' => -1 ,  'conditions' => ['id' => $_POST['ids'] ,  'buy_price <> "" and buy_price is not null ' ] ] );
            foreach ( $products as $p ) {
                $buy_price = (float)$p['Product']['buy_price'] ;
                $profit_margin = (float)$this->data['Product']['profit_margin'];//$p['Product']['profit_margin'] ;
                $sell_price =  $buy_price + ($buy_price * $profit_margin / 100 );
                $this->Product->update($p['Product']['id'] , ['Product' => ['unit_price' => $sell_price , 'profit_margin' => $profit_margin] ] );
            }
            $this->flashMessage(sprintf(__('%s has been saved', true), __('Products', true)), 'Sucmessage');
            $this->redirect(['controller' => 'products' , 'action' => 'index']);
        }
    }


    public function owner_recalculate_average( $product_id = null ) {
        set_time_limit(999999);
        ini_set("memory_limit", "10G");

        $this->loadModel ( 'StockTransaction') ;
        $this->StockTransaction->average_on_all (  $product_id) ;
        $this->autoRender = $this->autoLayout = false;
    }


    public function updateProductPriceGroups($productId, $productPriceGroups)
    {

        $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::PRICE_LIST_ITEMS);
        $activityLogService =  new \App\Services\ActivityLogService();
        $requests = [];

        $this->loadModel('GroupPrice');
        $this->loadModel('ProductPrice');
        $this->loadModel('Product');
        $product = $this->Product->find(array('Product.id' => $productId));
        // $productGroupPrices = $this->set('GroupPrices', $this->GroupPrice->find('list'));
        $ProductPrice = $this->ProductPrice->find('all', array('conditions'=> array('ProductPrice.product_id' => $productId)));

        $productGroupPrices = [];
        foreach($ProductPrice as $price_row){
            $productGroupPrices[$price_row['ProductPrice']['group_price_id']]= array(
                'id'=> $price_row['ProductPrice']['id'],
                'price'=> $price_row['ProductPrice']['price'],

            );
        }
        $productGroupPricesWillBeAdded = [];
        $productGroupPricesWillBeUpdated = [];
        foreach ($productPriceGroups as $groupId => $price) {
            if (isset($productGroupPrices[$groupId])) {
                if ($price) {
                    $oldPrice = $productGroupPrices[$groupId]['price'];
                    if ($price != $oldPrice) {
                        $productGroupPricesWillBeUpdated[] = [
                            'id' => $productGroupPrices[$groupId]['id'],
                            'group_price_id'=> $groupId,
                            'price' => $price,
                            'product_id' => $productId
                        ];
                    }
                    unset($productGroupPrices[$groupId]);
                }
            } else {
                $productGroupPricesWillBeAdded[] = [
                    'group_price_id'=> $groupId,
                    'price' => $price,
                    'product_id' => $productId
                ];
            }

        }
        foreach ($productGroupPricesWillBeAdded as $productGroupPrice) {
            $this->ProductPrice->create();
            $result = $this->ProductPrice->save($productGroupPrice);
            if ($result) {
//                $priceList = $this->GroupPrice->find(array('GroupPrice.id' => $result['ProductPrice']['group_price_id']));
//                \ActivityLog\Facades\ActivityLogFacade::save(
//                    \ActivityLog\Utils\ActionLineMainOperationTypesUtil::ASSIGNED_PRODUCT_TO_ACTION,
//                    [
//                        'primary_id' => $this->ProductPrice->id,
//                        'price_list_id' => $result['ProductPrice']['group_price_id'],
//                        'product_id' => $result['ProductPrice']['product_id'],
//                        'price' => $result['ProductPrice']['price'],
//                        'price_list_name' => $priceList['GroupPrice']['name'],
//                        'product_name' => $product['Product']['name'],
//                    ]);
            }
                $addedRelations= [
                    new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest($result['ProductPrice']['group_price_id'], EntityKeyTypesUtil::PRICE_LIST),
                    new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest( $product['Product']['id'], EntityKeyTypesUtil::PRODUCT_ENTITY_KEY)
                ];
                $newData = ['id' => $result['ProductPrice']['product_id']];
                $detailsRequest = new DetailsEntityActivityLogRequestsCreator();
                $groupPrice = $this->GroupPrice->findById($result['ProductPrice']['group_price_id']);
                $detailsRequest->setLabel($product["Product"]["name"]." ".__("in", true)." ".$groupPrice["GroupPrice"]["name"]);
                $requests = array_merge($detailsRequest->create($st, $newData, [], $addedRelations), $requests);

            }
        foreach ($productGroupPricesWillBeUpdated as $productGroupPrice) {
            $this->ProductPrice->create();
            $oldData =  getRecordWithEntityStructure(EntityKeyTypesUtil::PRICE_LIST_ITEMS, $productGroupPrice["id"], 1)->toArray();
            $this->ProductPrice->set($productGroupPrice);
            $result = $this->ProductPrice->save();
            if ( $result) {
//                $priceList = $this->GroupPrice->find(array('GroupPrice.id' => $result['ProductPrice']['group_price_id']));
//                \ActivityLog\Facades\ActivityLogFacade::save(
//                    \ActivityLog\Utils\ActionLineMainOperationTypesUtil::UPDATED_PRODUCT_PRICE_IN_ACTION,
//                    [
//                        'primary_id' => $this->ProductPrice->id,
//                        'price_list_id' => $result['ProductPrice']['group_price_id'],
//                        'product_id' => $result['ProductPrice']['product_id'],
//                        'price' => $result['ProductPrice']['price'],
//                        'price_list_name' => $priceList['GroupPrice']['name'],
//                        'product_name' => $product['Product']['name'],
//                    ]);
                $addedRelations= [
                    new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest($result['ProductPrice']['group_price_id'], EntityKeyTypesUtil::PRICE_LIST),
                    new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest( $product['Product']['id'], EntityKeyTypesUtil::PRODUCT_ENTITY_KEY),
                ];
                $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::PRICE_LIST_ITEMS, $this->ProductPrice->id, 1)->toArray();
                $newData['id'] = $this->ProductPrice->id;
                $oldData['id'] = $this->ProductPrice->id;
                $requestDetails = new DetailsEntityActivityLogRequestsCreator();
                $groupPrice = $this->GroupPrice->findById($result['ProductPrice']['group_price_id']);
                $requestDetails->setLabel($product["Product"]["name"]." ".__("in", true)." ".$groupPrice["GroupPrice"]["name"]);
                $requests = array_merge($requestDetails->create($st, $newData, $oldData, $addedRelations), $requests);
            }
        }
        foreach ($productGroupPrices as $group_price_id => $productGroupPrice) {
            $this->ProductPrice->create();
            $idOfRecordWillBeDeleted = $productGroupPrice['id'];
            $oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::PRICE_LIST_ITEMS, $idOfRecordWillBeDeleted, 1)->toArray();
            $oldData["id"] = $idOfRecordWillBeDeleted;
            $priceList = $this->GroupPrice->find(array('GroupPrice.id' => $group_price_id));
            $result = $this->ProductPrice->delete($idOfRecordWillBeDeleted);
            if ($result) {
                $addedRelations = [
                    new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest($priceList['GroupPrice']['id'], EntityKeyTypesUtil::PRICE_LIST),
                    new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest( $product['Product']['id'], EntityKeyTypesUtil::PRODUCT_ENTITY_KEY)
                ];


                $requests = array_merge((new DetailsEntityActivityLogRequestsCreator())->setLabel($product["Product"]["name"]." ".__("in", true)." ".$priceList["GroupPrice"]["name"])->create($st, [], $oldData, $addedRelations), $requests);
            }
        }

        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }
    }


    public function owner_barcodes_printable_templates($id = null, $template_id = null) {
        $barcodes = [];
        $product = $this->Product->find ( 'first' , ['conditions' => ['Product.id' => $id]]);
        
        if(isset($product['Product']['barcode']) && !empty($product['Product']['barcode'])) {
            $barcodes[] = ['title' => __("Main Barcode", true),'type' => 'default','id' => $id, 'barcode' => $product['Product']['barcode']];
        }
        $this->loadModel('ItemBarcode');
        $barcodeList = $this->ItemBarcode->find('all', ['conditions' => ['product_id' => $product['Product']['id']]]);
        $unitTemplate = $product['UnitTemplate'];
        $this->loadModel('UnitTemplate');
        $this->loadModel('UnitFactor');
        $factors = $this->UnitFactor->find('all' , ['recursive' => -1]);
        $factors_list = []  ;
        foreach ( $factors as $f ) {
            $factors_list[$f['UnitFactor']['unit_template_id']][$f['UnitFactor']['id']] = $f['UnitFactor'];
        }
        $unit_templates = $this->UnitTemplate->find('all' , ['recursive' => -1 ]);
        foreach ( $unit_templates as  $t ){
            $factors_list[$t['UnitTemplate']['id']][0] = ['id' => 0 , 'factor_name' => $t['UnitTemplate']['main_unit_name'], 'factor' => 1 ];
        }

        foreach($barcodeList as $barcode) { 
           $title = $factors_list[$product['Product']['unit_template_id']][$barcode['ItemBarcode']['unit_factor_id']]['factor_name'];
           $barcodes[] = ['title' => $title,'id' => $barcode['ItemBarcode']['id'], 'barcode' => $barcode['ItemBarcode']['barcode']]; 
        }

        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('product');
        $printableTemplate_barcode = $this->PrintableTemplate->getPrintableTemplatesList('product_item_barcode');

        if($template_id) { 
            foreach($printableTemplates as $template) {
                if($template['PrintableTemplate']['id'] == $template_id) {
                    $this->set('current_printable_template', $template);
                }
            }
        }

        $this->set('barcodes', $barcodes);
        $this->set('product', $product);
        $this->set('printableTemplates', $printableTemplates);
        $this->set('printableTemplate_barcode', $printableTemplate_barcode);
    }

    public function beforeRender()
    {
        parent::beforeRender();
        if (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == ReportUtil::XLSX) {
            $this->autoRender = false;
            $this->theme = false;
            $this->autoLayout = $this->layout = false;
            $reportAction = $this->params['action'];
            $reportType = $this->params['url']['report_type'];
            $reportName = str_replace('owner_', '', $reportAction);
            $element = ReportFactory::element($reportAction, $reportType); 
            ReportFactory::init(ReportUtil::XLSX)->export($reportName, $this, $element, $reportType ? $reportType : $this->params['pass'][0]);
        }
    }

    private function setCustomEntityAndKey(){
        $this->set('entityCustomField', 'product_custom_field');
        $this->set('entityFieldCustomFieldKey', 'product_custom_fields');
    }

    public function _filter_params($params = false, $filters = array(), $passedModelName = false)
    {
        $conditions = parent::_filter_params($params);

        if (!empty($this->params['url']['barcode'])) {
            $barcode = $this->params['url']['barcode'];
            $isDataMatrix = $this->isDatamatrix($barcode);
            if ($isDataMatrix) {
                $barcode = substr($barcode, 2, 14);
            }
            $conditions['Product.barcode LIKE'] = "%{$barcode}%";
        }

        return $conditions;
    }

    protected function isDatamatrix(string $barcode): bool
    {
        $identifiers = ['01', '10', '17', '21'];
        foreach ($identifiers as $identifier) {
            if (!str_contains($barcode, $identifier)) {
                return false;
            }
        }
        return (bool) preg_match('/^01\d{14}/', $barcode);
    }

    function owner_remove_photo_redirect($id = null) {
        $row = $this->Product->ProductImage->read(null, $id);
        if (!$row) {
            echo 'false';
            die();
        }

        $path = WWW_ROOT . "/files/" . SITE_HASH . "/product-images/" . $row['ProductImage']['file'];
        unlink($path);
        $this->Product->ProductImage->delete($id);
        return $this->redirect($this->referer());
    }

    function owner_confirm_csv()
    {
        $this->set('report_url',base64_decode($_GET['report_url']));
        $this->set('download_url',base64_decode($_GET['download_url']));
        $this->set('records_count',$_GET['records_count']);
        $this->set('handler',$_GET['handler']);
    }

    private function prepareProductDataForApi($data)
    {

        $attachments = $data['Attachments'] ?? [];
        $tmp = [];
        $awsService = new Aws;
        foreach ($attachments as $attach) {
            $tmp[] = [
                "id" => $attach['id'],
                "product_id" => $attach['EntityAttachment']['entity_id'],
                "default" => $attach['EntityAttachment']['is_default'],
                "file" => $attach['name'],
                "created" => $attach['created_at'],
                "modified" => $attach['updated_at'],
                "file_full_path" => $awsService->getProxyUrl($attach['path'])
            ];
        }

        $data['ProductImageS3'] = $tmp;
        return $data;
    }

    private function getS3FilesDetailsForActivityLog($attachments_ids)
    {
        $files = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($attachments_ids);
        $tmp = [];
        foreach ($files as $file) {
            $tmp[] = $file->name;
        }
        return (!empty($tmp)) ? implode(',', $tmp) : '';
    }

    public function api_is_product_can_be_deleted() {
        $itemGroupProductsIds = json_decode(file_get_contents('php://input'))->itemGroupProductsIds;

        $errMsg = '';
        foreach($itemGroupProductsIds as $productId){
            $product = $this->Product->find('first' , ['recursive' => -1 , 'conditions' => ['id' => $productId] ]);
       
            try {
                $this->Product->canProductBeDeleted($productId);
            } catch (Exception $e) {
                $errMsg = sprintf(__("You cannot delete product %s: ", true), $product['Product']['name']);
                $errMsg .= $e->getMessage();
                break;
            }
            if ($product['Product']['track_stock'] == '1'  && $this->Product->has_transactions($productId)) {
                $errMsg = sprintf(__("You cannot delete product %s: ", true), $product['Product']['name']) . __("This Product has existing transactions or stocktakings, you can make it inactive.",true);
                break;
            }
        }

        $this->layout = false;
		$this->autoRender = false;
		$this->RequestHandler->respondAs('json');

         die(json_encode(['status' => true, 'message' => $errMsg]));


    }

    public function api_file_from_url()
    {
        $rawBody = file_get_contents('php://input');
        $jsonData = json_decode($rawBody, true);

        if ($jsonData === null) {
            die(json_encode(['status' => false, 'message' => "Invalid JSON!"]));
        }

        $s3Service = izam_resolve(S3UploadHandler::class);
        $siteHash = dechex(crc32(getCurrentSite('id')));
        $imageUrl = $jsonData['file_path'];

        $content = $this->fetchRemoteFile($imageUrl);
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($content);

        if (!str_starts_with($mimeType, 'image/')) {
            die(json_encode(['status' => false, 'message' => $mimeType." image type is not supported"]));
        }

        try {
            $tmpFile = tempnam(sys_get_temp_dir(), 'img_');
            file_put_contents($tmpFile, $content);
            $systemFile = $s3Service->uploadFile([
                'tmp_name' => $tmpFile,
                'name' => basename(parse_url($imageUrl, PHP_URL_PATH)),
            ], $siteHash, 'product');
            unlink($tmpFile);
            die(json_encode(['status' => true, 'message' => "Success", 'file_id' => $systemFile['file_id']]));
        } catch (Exception $e) {
            unlink($tmpFile);
            die(json_encode(['status' => false, 'message' => $e->getMessage()]));
        }
    }

    function encodeUrlPath($url) {
        $parsed = parse_url($url);

        if (!isset($parsed['scheme']) || !isset($parsed['host'])) {
            return false;
        }

        $encodedPath = '';
        if (isset($parsed['path'])) {
            $parts = explode('/', $parsed['path']);
            $encodedParts = array_map('rawurlencode', $parts);
            $encodedPath = implode('/', $encodedParts);
        }

        return $parsed['scheme'] . '://' . $parsed['host'] . $encodedPath .
            (isset($parsed['query']) ? '?' . $parsed['query'] : '') .
            (isset($parsed['fragment']) ? '#' . $parsed['fragment'] : '');
    }

    function fetchRemoteFile($url) {
        $url = $this->encodeUrlPath($url);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // handle redirects
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // timeout
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        $data = curl_exec($ch);

        if (curl_errno($ch)) {
            return false;
        }

        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code != 200) {
            return false;
        }

        return $data;
    }

    private function getProductPendingQuantity($warehouseService, $productId): array
    {
        $staffId = getAuthOwner('staff_id');
        $stockPendingQTY = $warehouseService->getStaffProductStock(ItemPermission::PERMISSION_VIEW, $productId, $staffId, null, true, StockTransactionUtil::STATUS_PENDING);
        $invoiceItemService = new InvoiceItemService($stockPendingQTY);
        return $invoiceItemService->getStaffProductPendingStock(ItemPermission::PERMISSION_VIEW, $productId, $staffId, null);
    }

    private function combineProductStock($stockQTY, $invoiceItemQTY): array
    {
        $keys = array_merge(array_keys($stockQTY), array_keys($invoiceItemQTY));
        $result = [];
        foreach (array_unique($keys) as $key) {
            $result[$key] = (isset($stockQTY[$key]) ? $stockQTY[$key] : 0) + (isset($invoiceItemQTY[$key]) ? $invoiceItemQTY[$key] : 0);
        }
        return $result;
    }

    private function prepareStockBalance($data)
    {
        $StockTransactionModel = GetObjectOrLoadModel('StockTransaction');
        foreach ($data as $key => $item) {

            $item = $StockTransactionModel->find('count', ['conditions' => ['product_id' => $item['product_id'], 'store_id' => $item['store_id']]]);
            if (!$item) {
                unset($data[$key]);
            }
        }
        return $data;
    }

    private function validateProductAttributeOptions($attributeOptions , $itemGroupId)
    {
        $branchId = getCurrentBranchID();
        $query = "select * from `products` where `item_group_id` = ".$itemGroupId ." and `branch_id` = " .$branchId;
        $append = " and exists ";
        foreach ($attributeOptions as $attributeOption){
            $subQuery = "(select * from `product_attribute_options` where `products`.`id` = `product_attribute_options`.`product_id` and `attribute` = " . '"'. $attributeOption['attribute'] . '"'." and `option` = ". '"'. $attributeOption['option'] . '"'. ")";
            $query .= $append . $subQuery;
        }
        $queryResult = $this->Product->query($query);
        if (is_countable($queryResult) && count($queryResult) > 0){
           return $queryResult[0];
        }
        return null;
    }

    private function createItemGroupAttributeOption($itemGroupAttributes , $productAttributeOption)
    {
        unset($productAttributeOption['id']);
        foreach ($itemGroupAttributes as $itemGroupAttribute){
            if ($productAttributeOption['attribute'] == $itemGroupAttribute['ItemGroupAttribute']['name']){
                $options = [];
                foreach ($itemGroupAttribute['ItemGroupAttributeOption'] as $itemGAO){
                    $options[] = $itemGAO['value'];
                }
                if (!in_array($productAttributeOption['option'] , $options)){
                    $record = [
                        'ItemGroupAttributeOption' => [
                            'value'=> $productAttributeOption['option'],
                            'item_group_attribute_id' => $itemGroupAttribute['ItemGroupAttribute']['id'],
                            'created' => date("Y:m:d H:i:s"),
                            'modified' => date("Y:m:d H:i:s"),
                        ]
                    ];
                    $this->ItemGroupAttributeOption->create();
                    $this->ItemGroupAttributeOption->save($record);
                }
            }
        }
    }

    function sortItemsByCustomKey(array $items, string $sortKey , string $sortOrder = 'ASC'): array {
        $keys = [
            1 => 'sales',
            2 => 'sales_price',
            3 => 'refunds',
            4 => 'refunds_price',
            5 => 'net_sales'
        ];

        $key = $keys[$sortKey] ?? 'sales';

        usort($items, function($a, $b) use ($sortOrder, $key) {
            if ($sortOrder == 'ASC') {
                if ($key == 'net_sales') {
                    return abs($a['refunds'] + $a['sales']) <=> abs($b['refunds'] + $b['sales']);
                }
                return abs($a[$key]) <=> abs($b[$key]);
            } else {
                if ($key == 'net_sales') {
                    return abs($b['refunds'] + $b['sales'])  <=> abs($a['refunds'] + $a['sales']);
                }
                return abs($b[$key]) <=> abs($a[$key]);
            }
        });

        return $items;
    }


    public function generateMultipleAttributeCondition($selectedAttribute)
    {
        $paQuery = "SELECT product_id, COUNT(product_id) AS COUNT_ID FROM product_attribute_options ";
        $firstMatch = false;
        $sentFiltersCount = 0;
        $selectedAttribute = array_filter($selectedAttribute, function ($attribute) {
            return !empty($attribute['option']);
        });
        if (count($selectedAttribute)) {
            $paQuery .= 'WHERE ';
        }
        foreach ($selectedAttribute as $index => $attribute){
            if (isset($attribute['attribute']) && isset($attribute['option'])){
                if ($firstMatch){
                    $paQuery.= " OR ";
                }
                $sentFiltersCount++;
                $attributeName = $attribute['attribute'];
                $option = $attribute['option'];
                $paQuery .= "(attribute = '{$attributeName}' AND `option` = '{$option}') ";
                $firstMatch = true;
            }
        }
        $paAppend = " GROUP BY product_id HAVING COUNT_ID = {$sentFiltersCount}";
        $paQuery.= $paAppend;
        $qProducts = $this->ProductAttributeOption->query($paQuery);
        $item_ids = [];
        foreach ($qProducts as $qp ){
            $item_ids[] = $qp['product_attribute_options']['product_id'];
        }
        return $item_ids;
    }

    function owner_change_status ($product_id, $status) {
        $isAjax = $this->RequestHandler->isAjax();
        if ( !check_permission( [PermissionUtil::Edit_Delete_all_Products, PermissionUtil::Edit_And_delete_his_own_added_Products])|| ( empty($product_id) && empty($_POST['ids'])) ){
            if($isAjax){
                die(json_encode(['error'=>true,'message'=>__('You are not allowed to view this page', TRUE)]));
            }
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        
        $status_action = match((int)$status){
            ProductStatusUtil::STATUS_ACTIVE => __t('Activated'),
            ProductStatusUtil::STATUS_INACTIVE => __t('Deactivated'),
            ProductStatusUtil::STATUS_SUSPENDED => __t('Suspended'),
        };
        $productData = $this->Product->find('first' , ['recursive' => -1 , 'conditions' => ['id' => $product_id] ]);
        if (!$productData) {
            if($isAjax) die(json_encode(['error'=>true,'message'=>sprintf(__("%s not found.", true), __('Product', true))]));
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        if (ifPluginActive(BranchesPlugin)) {
            $current_branch_id = getCurrentBranchID();
            $product_branch_id = $productData['Product']['branch_id'];
            if($current_branch_id != $product_branch_id && !isModelSharable('Product')){
                if($isAjax) die(json_encode(['error'=>true,'message'=>sprintf(__('%s not found in the current branch.', true), __('Product', true))]));
                $this->flashMessage(sprintf(__('%s not found in the current branch.', true), __('Product', true)));
                $this->redirect($this->referer(array('action' => 'index')));
            }
        }
        $owner = getAuthOwner() ;

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if (( (!check_permission(Edit_Delete_all_Products) && $productData['Product']['staff_id'] != $staff) || !check_permission(Edit_And_delete_his_own_added_Products) ) && !($productData['Product']['source_type'] == "package" && check_permission(MANAGE_PACKAGES) )  ) {
                if($isAjax) die(json_encode(['error'=>true,'message'=>__("You are not allowed to edit this product ", true)]));
                $this->flashMessage(__("You are not allowed to edit this product ", true), 'Errormessage', 'secondaryMessage');
                $this->redirect($this->referer());
            }
        }

        $view_link='<a target="_blank" href="'. Router::url('/owner/products/view/'.$product_id).'">'.__t('Product'). ' #'.strongStr($productData['Product']['product_code']??$product_id).'</a>' ;
        $productData['Product']['status'] = $status;

        if (in_array($status, [ProductStatusUtil::STATUS_INACTIVE, ProductStatusUtil::STATUS_SUSPENDED])) {
            $productData['Product']['deactivate'] = $status;

            if ($this->Product->isProductLinkedToShippingOptions($product_id)) {
                $error_msg = __('You cannot change the status to inactive or suspended for the service that has already been selected in the <a href="/v2/owner/shipping_options/settings">cash on delivery fee settings</a>.', true);
                if($isAjax){
                    $error_msg = sprintf(__('%s failed to be updated  %s',true),$view_link,$error_msg);
                    die(json_encode(['error'=>true,'message'=>$error_msg]));
                }
                $this->flashMessage($error_msg, 'Errormessage', 'secondaryMessage');
                $this->redirect($this->referer());
                
            }
            try {
                $this->Product->isDefaultServiceInBooking($product_id);
            } catch (Exception $e) {
                $this->flashMessage($e->getMessage());
                $this->redirect($this->referer(array('action' => 'index')));
            }
        }else{
            $productData['Product']['deactivate'] = 0;
        }        
        $oldData = $this->getRecordWithEntityStructureWithCustomFields(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY, $product_id, 2);

        if($result = $this->Product->save($productData)){
            $succ_msg=sprintf(__t('%s has been %s'),$view_link,$status_action);
            $newData = $this->getRecordWithEntityStructureWithCustomFields(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY, $product_id, 2);
            $st = getEntityBuilder()->buildEntity(EntityEntityKeyTypesUtil::PRODUCT_ENTITY_KEY);
            $requests = (new DetailsEntityActivityLogRequestsCreator())->create($st, $newData, $oldData, []);
            $activityLogService =  new \App\Services\ActivityLogService();
            foreach ($requests as $requestObj) {
                $activityLogService->addActivity($requestObj);
            }
            if($isAjax){
                die(json_encode(['error'=>false,'message'=>$succ_msg]));
            }
            $this->flashMessage($succ_msg, 'Sucmessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }else{
            $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Product->validationErrors]);
        }
    }

    function owner_export() {
        if(isset($_POST['ids']) && $_POST['ids']){
			$this->set('ids', $_POST['ids']);
		}else{
            $this->redirect('index');
        }
        if (!check_permission([View_his_own_Products, Edit_And_delete_his_own_added_Products])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
    }

    private function getActivePrintableTemplates($printableTemplates) {
        $tmp = [];
        foreach ($printableTemplates as $template) {
            if($template['PrintableTemplate']['for_galary_only'] != 1){
              $tmp[] = $template;
            }
        }
        return $tmp;
    }

    private function bindCustomDataRelation()
    {
        if (\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('products')) {
            $this->Product->bindModel(
                [
                    'hasMany' =>
                        [
                            'ProductsCustomData' =>
                                [
                                    'custom_model_name' => 'ProductsCustomData',
                                    'custom_data_table_name' => 'products_custom_data'
                                ]
                        ]
                ]
            );
        }
    }

    private function getStaffSelectFieldsAndJoinForProfitReport($type) {
        if($type == 'sales_person'){
            $staff_select_fields = "
                COALESCE(
                    CASE 
                        WHEN invoices.sales_person_id = " . Invoice::MULTIPLE_SALES_PERSONS . " THEN staff_items.id
                        ELSE staff_invoice.id
                    END
                ) AS staff_id,
                COALESCE(
                    CASE 
                        WHEN invoices.sales_person_id = " . Invoice::MULTIPLE_SALES_PERSONS . " THEN staff_items.name
                        ELSE staff_invoice.name
                    END
                ) AS staff_name";
            $staffsJoin = ' LEFT JOIN 
                item_staffs SP_IS_items ON SP_IS_items.item_id = invoice_items.id 
                    AND SP_IS_items.item_type = 15
                    AND invoices.sales_person_id = ' . Invoice::MULTIPLE_SALES_PERSONS . '
            LEFT JOIN 
                staffs staff_items ON staff_items.id = SP_IS_items.staff_id
            LEFT JOIN 
                item_staffs SP_IS_invoice ON SP_IS_invoice.item_id = invoices.id 
                    AND SP_IS_invoice.item_type = 12
                    AND invoices.sales_person_id != ' . Invoice::MULTIPLE_SALES_PERSONS . '
            LEFT JOIN 
                staffs staff_invoice ON staff_invoice.id = SP_IS_invoice.staff_id';
            $invoice_items_join = " LEFT JOIN 
                invoice_items ON invoices.id = invoice_items.invoice_id 
                AND st.product_id = invoice_items.product_id
                AND invoices.sales_person_id = " . Invoice::MULTIPLE_SALES_PERSONS;
            $pos_invoices_join = " LEFT JOIN invoices ON invoices.pos_shift_id = pos.id
                                    LEFT JOIN invoice_items ON invoice_items.invoice_id = invoices.id AND st.product_id = invoice_items.product_id AND invoices.sales_person_id = " . Invoice::MULTIPLE_SALES_PERSONS;
            $pos_staff_join = $staffsJoin;
        }else{
            $staff_select_fields = 'staffs.id as staff_id , staffs.name as staff_name';
            $staffsJoin = 'left join staffs  on staffs.id = invoices.staff_id';
            $invoice_items_join = '';
            $pos_invoices_join = '';
            $pos_staff_join = "left join staffs on staffs.id = pos.staff_id";
        }
        return [$staff_select_fields, $staffsJoin, $invoice_items_join, $pos_invoices_join, $pos_staff_join];
    }
}
