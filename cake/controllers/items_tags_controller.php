<?php
class ItemsTagsController extends AppController {

	var $name = 'ItemsTags';

	/**
	 * @var ItemsTag
	 */
	var $ItemsTag;
	var $helpers = array('Html', 'Form');

	
	function owner_save_ajax()
	{
		$data = $_POST['tags_item_data'];
//		$data = array('tags' => array('eee','1sda'), 'item_id' => 2, 'item_type' =>4 );
		$data['tags'] = $data['tags'];
		if(empty($data['item_type']) && !empty($data['model_name']))
		{
			$data['item_type'] = ItemsTag::model_name_to_tag_type($data['model_name']);
		}
		if(!empty($data['tags']) && !empty($data['item_id']) && !empty($data['item_type'])){
			$data = $this->ItemsTag->save_item_tags($data, $data['item_id'], $data['item_type']);
		}
		
		die();
	}

	function owner_index() {
		$this->ItemsTag->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('itemsTags', $this->paginate('ItemsTag', $conditions));
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('items tag', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('itemsTag', $this->ItemsTag->read(null, $id));
	}

	function owner_add() {
		if (!empty($this->data)) {
			$this->ItemsTag->create();
			if ($this->ItemsTag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('items tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('items tag',true)));
			}
		}
		$tags = $this->ItemsTag->Tag->find('list');
		$this->set(compact('tags'));
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'items tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$itemsTag = $this->ItemsTag->read(null, $id);
		if (!empty($this->data)) {
			if ($this->ItemsTag->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('items tag',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('items tag',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $itemsTag;
		}
		$tags = $this->ItemsTag->Tag->find('list');
		$this->set(compact('tags'));
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('items tag',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('itemsTag', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('itemsTags', true);
		 } 
		$itemsTags = $this->ItemsTag->find('all',array('conditions'=>array('ItemsTag.id'=>$id)));
		if (empty($itemsTags)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->ItemsTag->deleteAll(array('ItemsTag.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('itemsTags',$itemsTags);
	}
}
?>