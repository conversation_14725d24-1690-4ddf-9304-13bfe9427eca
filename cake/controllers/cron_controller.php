<?php

use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\Services\RedisLockService;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use Rollbar\Payload\Level;
use Rollbar\Rollbar;

/** @property SmsCampaign $SmsCampaign */
class CronController extends AppController{
	var $uses = [];
	function send_sms_campaign(){
		App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		$this->loadModel("SmsCampaign");
		$smsCampaign = $this->SmsCampaign->find("first", ["conditions"=>["SmsCampaign.site_id !="=>44,"SmsCampaign.state"=>"ready"], "recursive"=>-1]);
		if(!empty($smsCampaign)){
			var_dump($smsCampaign['SmsCampaign']['id']);
			$this->loadModel('Site');
			$site = $this->Site->findById($smsCampaign['SmsCampaign']['site_id']);
			$GLOBALS['site'] = $site['Site'];
			$config = json_decode($site['Site']['db_config'], true);
			ConnectionManager::getDataSource('default')->swtich_db($config);
			\SendSMS\SendSMS::sendCampaign($smsCampaign['SmsCampaign']['id']);
		}
		exit;
	}
	function update_sms_prices() {
		App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		SendSMS\SendSMS::update_sms_prices();
		exit;
	}
	function auto_reminder_msgs($site_id=null) {

        App::import('Vendor', 'oilogger');
        Oilogger::initSession('auto_reminder_msgs_' . date('Y_m_d') . '.txt');
		App::import('vendor','AutoReminder',['file'=>'AutoReminder/autoload.php']);
		$siteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));
        $conditions = array('Site.status'=>SITE_STATUS_ACTIVE,'Site.first_login'=>0);
        if ($site_id) {
            $conditions['Site.id'] = $site_id;
        }
        $sites = $siteModel->find('all', array('conditions' => $conditions));
        Oilogger::log('Started......................');
        Oilogger::log('Sites Count ' . count($sites));
        $this->loadModel('Timezone');
        $this->loadModel('AutoReminderRule');
        $this->loadModel('ReminderMessage');
        IzamDatabaseServiceProvider::boot(getPDO(),getPortalConfig());
        foreach ($sites as $site) {
            $this->Timezone->query("select now() ",false); // to keep portal connection alive
        try {
            Oilogger::log(' Current Site: ' . $site['Site']['id']);
            $GLOBALS['site'] = $site['Site'];
            $config = json_decode($site['Site']['db_config'], true);
            ConnectionManager::getDataSource('default')->swtich_db($config);

            $this->AutoReminderRule->getDataSource()->swtich_db($config);
            IzamDatabaseServiceProvider::setSiteConnection(getPdo());
            removePdo();


            $zone = $this->Timezone->field('zone_name', array("Timezone.id" => $site['Site']['timezone']));
            date_default_timezone_set($zone);
            $this->AutoReminderRule->applyBranch['onFind']=false;
            $this->AutoReminderRule->applyBranch['onSave']=false;
			$rules = $this->AutoReminderRule->find('all',["","recursive"=>-1]);

			foreach ($rules as $rule) {
				$autoReminderRule = \AutoReminder\AutoReminderFactory::create($rule);
				$autoReminderRule->addMessages($autoReminderRule->findMatches(false,$zone));

			}
        } catch (Throwable $exception) {
            Rollbar::log(Level::CRITICAL,'[auto_reminder_msgs] exception : ' . $exception->getMessage(), ['file'=> $exception->getFile(), 'line' => $exception->getLine()]);
            Oilogger::log('[auto_reminder_msgs] cron exception : ' . $exception->getMessage() . ' - ' . $exception->getFile() . ' - ' . $exception->getLine());
        }

		}
        if(php_sapi_name()=='cli'){
            die();
        }
		exit;
	}

	public function run_queue($siteId, $runTime = "background", $listenerId = null, $eventId = null) {
        $Runner = new \App\Services\QueueRunner();
        $Runner->runSiteEvents($siteId, $runTime, $listenerId, $eventId);
        $this->autoRender = false;
        die();
    }

    public function cake_listener_execute($listener_id, $server_id = null) {
        $redisLockAquired = RedisLockService::acquireLock("lock_queue_listener_id:" . $listener_id);
        if (!$redisLockAquired) {
            exit;
        }
        $Runner = new \App\Services\QueueRunner();
        $Runner->runListenerProcess($listener_id, $server_id);
    }


    public function import_bulk_using_json_file($siteId, $branchId, $staff_id, $id)
    {
        IzamDatabaseServiceProvider::addConnection(getIzamDatabaseConfig('queue_database'), 'queue_server');

        $Site = GetObjectOrLoadModel('Site');
        $site = $Site->findById($siteId);
        setCurrentSite($siteId);
        db_reconnect('default', json_decode($site['Site']['db_config'],true));
        IzamDatabaseServiceProvider::boot(getPDO(),getPortalConfig());
        IzamDatabaseServiceProvider::addConnection(getIzamDatabaseConfig('queue_database'), 'local_queue');
        setRequestCurrentBranch($branchId);

        if ($staff_id == 0) {
            $type = "owner";
            $staff_id = $site['Site']['id'];
        } else {
            $type = "staff";
        }
        $sessionData = AuthHelper::generateLoggedUserSessionData($type, $staff_id);
        AuthHelper::setLoggedUserSessionData($sessionData);

        \Izam\Limitation\LimitationServiceProvider::boot(new \App\Helpers\PluginHelper(), ['current_site_connection' => getPDO(), 'portal_connection' => getPDO('portal')]);

        $entityRepo = new \Izam\Daftra\Common\Repositories\EntityAppDataRepository();
        $entityRepo->deleteOldImportRecords();
        $entityAppData = $entityRepo->find($id);
        if (empty($entityAppData)) {
            return;
        }
        $entityKey = $entityAppData->entity_key;
        $file_name = json_decode($entityAppData->data, true)['file'];
        $data = json_decode(file_get_contents($file_name), true);

        $pluralEntityKey = ucfirst(singularToPlural($entityKey));
        App::import('Controller', $pluralEntityKey);
        $controllerName = "{$pluralEntityKey}Controller";
        $controller = new $controllerName;
        $controller->constructClasses();
        $controller->fillImportJsonData($data, $entityAppData, $entityKey);

        if (file_exists($file_name)) {
            unlink($file_name);
        }

        exit('done');
    }

    public function delete_old_record_unique_ids() 
    {
        $this->loadModel('RecordUniqueId');
        $this->RecordUniqueId->deleteAll(['created_at <' => date('Y-m-d H:i:s', strtotime('-1 hour'))]);
        die('done');
    }
    
}
