<?php

use Izam\Daftra\Portal\Models\Plan as PortalPlan;

class PlansController extends AppController {

    var $name = 'Plans';

    /**
     * @var Plan
     */
    var $Plan;
    var $helpers = array('Html', 'Form');

    function admin_index() {
        $this->Plan->recursive = 0;
        $conditions = $this->_filter_params();
        $this->set('plans', $this->paginate('Plan', $conditions));
    }

    function admin_view($id = null) {
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('plan', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $this->set('plan', $this->Plan->read(null, $id));
    }

    function admin_add() {
        if (!empty($this->data)) {
            if (PortalPlan::create($this->data['Plan'])) {
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Plan', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('plan', true)));
            }
        }
    }

    function admin_edit($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', 'plan', true)));
            $this->redirect(array('action' => 'index'));
        }
        $plan = $this->Plan->read(null, $id);
        if (!empty($this->data)) {
            if (PortalPlan::where('id', $id)->update($this->data['Plan'])) {
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Plan', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Plan', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $plan;
        }

        $this->set(compact('plan'));
        $this->render('admin_add');
    }

    function admin_delete($id = null) {
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('plan', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('plan', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {
            $module_name = __('plans', true);
            $verb = __('have', true);
        }
        $plans = $this->Plan->find('all', array('conditions' => array('Plan.id' => $id)));
        if (empty($plans)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && PortalPlan::whereIn('id', $_POST['ids'])->delete()) {
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }
        $this->set('plans', $plans);
    }

    public function admin_update_display_order() {

        $params = $this->params['form'];
        if (!empty($params)) {
            $succeed = false;
            foreach ($params as $field => $value) {
                if (strpos($field, 'display_order') === false) {
                    continue;
                }
                $id = substr($field, strlen('display_order_'));
                if (PortalPlan::where('id', $id)->update(array('display_order' => intval($value)))) {
                    $succeed = true;
                }
            }
            if ($succeed) {
                $this->flashMessage(__('Display order has been updated', true), 'Sucmessage');
            } else {
                $this->flashMessage(__('Cannot update display order', true));
            }
        } else {
            $this->flashMessage(__('Cannot update display order', true));
        }
        $this->redirect($this->referer(array('action' => 'index'), true));
    }

    function index() {
        $this->redirect('/');
        die();
        $this->set('plans', $this->Plan->find('all', array('order' => '(Plan.display_order = 0 OR Plan.display_order IS NULL), Plan.display_order, Plan.price')));
    }

}