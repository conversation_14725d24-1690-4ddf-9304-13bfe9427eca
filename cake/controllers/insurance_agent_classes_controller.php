<?php
class InsuranceAgentClassesController extends AppController {

    var $name = 'InsuranceAgentClasses';

    /**
     * @var InsuranceAgent
     */
    var $InsuranceAgentClass;
    var $helpers = array('Html', 'Form');

    function owner_index($agentID = null) {
        if (!$agentID) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Insurance Agent', true)),true));
            $this->redirect(array('controller' => 'insurance_agents', 'action'=>'index'));
        }
        $insuranceAgent = $this->InsuranceAgentClass->InsuranceAgent->find('first',['conditions' => ['InsuranceAgent.id' => $agentID]]);
        if (!$insuranceAgent) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Insurance Agent', true)),true));
            $this->redirect(array('controller' => 'insurance_agents', 'action'=>'index'));
        }
        $this->set('insuranceAgent', $insuranceAgent);
        $this->InsuranceAgentClass->recursive = 0;
        $conditions = $this->_filter_params();
        $conditions[] = 'InsuranceAgentClass.insurance_agent_id = '.$agentID;
        $this->set('insuranceAgentClasses', $this->paginate('InsuranceAgentClass', $conditions));
    }

    function owner_view($id = null) {
        if (!$id) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Class', true)),true));
            $this->redirect(array('controller' => 'insurance_agents', 'action'=>'index'));
        }
        $this->set('insuranceAgentClass', $this->InsuranceAgentClass->read(null, $id));
    }

    function owner_add($agentID = null) {
        $this->setBreadCrumbs($agentID);
        if (!empty($this->data)) {
            return $this->commonSave();
        }
        if (!$agentID) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Insurance Agent', true)),true));
            $this->redirect(array('controller' => 'insurance_agents', 'action'=>'index'));
        }
        $this->data['InsuranceAgentClass']['insurance_agent_id'] = $agentID;
    }

    function owner_edit($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', true), __('Class',true)));
            $this->redirect(array('action'=>'index'));
        }
        $insuranceAgentClass = $this->InsuranceAgentClass->read(null, $id);
        $this->setBreadCrumbs($insuranceAgentClass['InsuranceAgent']['id'], 'edit', $insuranceAgentClass['InsuranceAgentClass']['name']);
        if (!empty($this->data)) {
            //Delete Old Many to Many Data
            $this->loadModel('InsuranceAgentPricingGroup');
            $this->loadModel('InsuranceAgentPricingCategory');
            $oldPricingGroupsIDs = [];
            foreach ($insuranceAgentClass['InsuranceAgentPricingGroup'] as $priceGroup) {
                $oldPricingGroupsIDs[] = $priceGroup['id'];
            }
            $this->InsuranceAgentPricingCategory->deleteAll(['InsuranceAgentPricingCategory.insurance_agent_pricing_group_id' => $oldPricingGroupsIDs]);
            $this->InsuranceAgentPricingGroup->deleteAll(['InsuranceAgentPricingGroup.id' => $oldPricingGroupsIDs]);
            return $this->commonSave(true);
        }
        if (empty($this->data)) {
            $this->loadModel('InsuranceAgentPricingCategory');
            foreach ($insuranceAgentClass['InsuranceAgentPricingGroup'] as &$insuranceAgentClassPricingGroup) {
                $insuranceAgentClassPricingGroup['category_ids'] = [];
                $pricingCategories = $this->InsuranceAgentPricingCategory->find('all', ['conditions' => ['InsuranceAgentPricingCategory.insurance_agent_pricing_group_id' => $insuranceAgentClassPricingGroup['id']]]);
                foreach ($pricingCategories as $pricingCategory) {
                    $insuranceAgentClassPricingGroup['category_ids'][$pricingCategory['InsuranceAgentPricingCategory']['product_category_id']] = $pricingCategory['ProductCategory']['name'];
                }
                $insuranceAgentClassPricingGroup['copayment_percentage_client'] = 100 - $insuranceAgentClassPricingGroup['copayment_percentage'];
            }
            $this->data = $insuranceAgentClass;
        }
        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('Class',true)));
            $this->redirect(array('action'=>'index'));
        }
        $module_name= __('Class', true);
        $insuranceAgentClass = $this->InsuranceAgentClass->read(null, $id);
        $this->setBreadCrumbs($insuranceAgentClass['InsuranceAgent']['id'], 'delete', $insuranceAgentClass['InsuranceAgentClass']['name']);
        if (empty($insuranceAgentClass)){
            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer(array('controller' => 'insurance_agents', 'action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes') {
                $this->loadModel('ClientInsuranceClass');
                $clientsCount = $this->ClientInsuranceClass->find('count',['conditions' => ['ClientInsuranceClass.insurance_agent_class_id' => $_POST['ids']]]);
                if ($clientsCount == 0) {
                    if ($this->InsuranceAgentClass->deleteAll(array('InsuranceAgentClass.id' => $_POST['ids']))) {
                        $this->flashMessage(sprintf(__('%s deleted', true), $module_name), 'Sucmessage');
                        $this->redirect(array('controller' => 'insurance_agents', 'action' => 'view', $insuranceAgentClass['InsuranceAgentClass']['insurance_agent_id']));
                    }
                } else {
                    $this->flashMessage(__('there is one or more class assigned to clients', true));
                    $this->redirect(array('controller' => 'insurance_agents', 'action' => 'view', $insuranceAgentClass['InsuranceAgentClass']['insurance_agent_id']));
                }
            }
            else{
                $this->redirect(array('controller' => 'insurance_agents', 'action'=>'view',$insuranceAgentClass['InsuranceAgentClass']['insurance_agent_id']));
            }
        }
        $this->set('insuranceAgentClass',$insuranceAgentClass);
    }

    function owner_ajaxApplyInsurance() {
        if (isset($_POST['data'])) {
            $invoiceData = $_POST['data'];
            $responseStatus = $this->InsuranceAgentClass->applyInsurance($invoiceData);
            die(json_encode($responseStatus ? $invoiceData : false));
        } else {
            http_response_code(400);
            die(json_encode(['message' => 'Error']));
        }
    }

    function api_ajaxApplyInsurance() {
        if (!empty($this->data)) {
            $invoiceData = $this->data;
            $responseStatus = $this->InsuranceAgentClass->applyInsurance($invoiceData);
            die(json_encode($responseStatus ? $invoiceData : false));
        } else {
            http_response_code(400);
            die(json_encode(['message' => 'Error']));
        }
    }

    private function commonSave() {
        $hasError = false;
        if(empty($this->data['InsuranceAgentClass']['name'])){
            $this->flashMessage(sprintf(__('The %s name can not be empty. Please, try again', true), __('Class',true)));
            $hasError = true;
        }
        $this->InsuranceAgentClass->create();
        unset($this->data['InsuranceAgentPricingGroup']['mmmm']);
        $insuranceClassGroups = $this->data['InsuranceAgentPricingGroup'];
        $this->loadModel('Category');
        $insuranceClassGroupsData = $this->data['InsuranceAgentPricingGroup'];
        $insuranceCategoryIDs = [];
        foreach ($insuranceClassGroups as $key => $insuranceClassGroup) {
            // warning suppress
            if (is_string($insuranceClassGroup['category_ids'])) $insuranceClassGroup['category_ids'] = explode(',', $insuranceClassGroup['category_ids']);
            $categories = $this->Category->find('list', ['conditions' => ['Category.id' => $insuranceClassGroup['category_ids']]]);
            unset($insuranceClassGroupsData[$key]['category_ids']);
            foreach ($insuranceClassGroup['category_ids'] as $category_id) {
                $insuranceClassGroupsData[$key]['category_ids'][$category_id] = $categories[$category_id];
                if (in_array($category_id, $insuranceCategoryIDs)) {
                    $this->flashMessage(__(sprintf (__('The %s could not be saved. You can not add the same category twice', true), __('Insurance Agent', true)),true));
                    $hasError = true;
                }
            }
            $insuranceCategoryIDs = array_merge($insuranceCategoryIDs, $insuranceClassGroup['category_ids']);
        }
        if ($hasError) {
            $this->data['InsuranceAgentPricingGroup'] = $insuranceClassGroupsData;
            $this->render('owner_add');
            return;
        }
        unset($this->data['InsuranceAgentPricingGroup']);
        //TODO: Make this a transaction
        if ($this->InsuranceAgentClass->save($this->data)) {
            foreach ($insuranceClassGroups as &$insuranceClassGroup) {
                $insuranceGroupCategoryIDs = $insuranceClassGroup['category_ids'];
                $insuranceClassGroup['insurance_agent_class_id'] = $this->InsuranceAgentClass->id;
                unset($insuranceClassGroup['category_ids']);
                $this->loadModel('InsuranceAgentPricingGroup');
                unset($this->InsuranceAgentPricingGroup->id);
                if ($this->InsuranceAgentPricingGroup->save($insuranceClassGroup)) {
                    $this->loadModel('InsuranceAgentPricingCategory');
                    $pricingCategories = [];
                    foreach ($insuranceGroupCategoryIDs as $categoryID){
                        $pricingCategories[]['InsuranceAgentPricingCategory'] = ['product_category_id' => $categoryID, 'insurance_agent_pricing_group_id' => $this->InsuranceAgentPricingGroup->id];
                    }
                    $this->InsuranceAgentPricingCategory->saveAll($pricingCategories);
                }
            }
            $this->flashMessage(sprintf (__('The %s has been saved', true), __('Class',true)), 'Sucmessage');
            if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_new') {
                return $this->redirect(['controller' => 'insurance_agent_classes', 'action' => 'add', $this->data['InsuranceAgentClass']['insurance_agent_id']]);
            } else {
                return $this->redirect(array('controller' => 'insurance_agents', 'action'=>'view', $this->data['InsuranceAgentClass']['insurance_agent_id']));
            }
        } else {
            $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Class',true)));
        }
    }

    private function setBreadCrumbs($agentID, $operation = 'add', $name = '') {
        $this->loadModel('InsuranceAgent');
        $agent = $this->InsuranceAgent->find('first', ['conditions' => ['InsuranceAgent.id' => $agentID ?: $this->data['InsuranceAgentClass']['insurance_agent_id']]])['InsuranceAgent'];
        $breadCrumbs = [
            ['title' => __('Insurance Agents', true), 'link' => Router::url(array('controller' => 'insurance_agents', 'action'=>'index'))],
            ['title' => $agent['name'] . ' #' . $agentID, 'link' => Router::url(array('controller' => 'insurance_agents', 'action'=>'view', $agentID ?: $this->data['InsuranceAgentClass']['insurance_agent_id']))],
        ];
        switch ($operation) {
            case 'add':
                $breadCrumbs[] = ['title' => sprintf(__('Add %s', true), __('Class', true)), 'link' => ''];
                break;
            case 'delete':
                $breadCrumbs[] = ['title' => sprintf(__('Delete %s', true), $name), 'link' => ''];
                break;
            case 'edit':
                $breadCrumbs[] = ['title' => sprintf(__('Edit %s', true), $name), 'link' => ''];
                break;

        }
        $this->set('_PageBreadCrumbs', $breadCrumbs);
    }

    public function addBreadCrumbs($model, $url, $data = [])
    {
        return;
    }
}
?>
