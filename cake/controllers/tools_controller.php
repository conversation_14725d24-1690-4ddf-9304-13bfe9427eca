<?php

class ToolsController extends AppController {
    var $uses = [];

    function whatismyip() {
        echo 'get_real_ip : ' .  get_real_ip() . '<br>';

        $server_keys = [
            'HTTP_CLIENT_IP',
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'REMOTE_ADDR',
        ];
        foreach ($server_keys as $server_key){
            echo  $server_key . '  : ' .  ($_SERVER[$server_key] ?? 'n\a')  . '<br>';
        }
        die();
    }
}
?>
