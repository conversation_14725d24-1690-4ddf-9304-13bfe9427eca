<?php
App::import('Vendor', 'SendSMS', array('file' => 'SendSMS/PlivoSendSMS.php'));

class SmsLogsController extends AppController {

	var $name = 'SmsLogs';

	/**
	 * @var SmsLog
	 */
	var $SmsLog;
	var $helpers = array('Html', 'Form');

	function owner_index() {
		$this->SmsLog->recursive = 0;
		$site_id = getAuthOwner('id');
		$conditions = $this->_filter_params();
		$conditions['SmsCampaign.site_id'] = $site_id;
        $this->paginate['SmsLog']['order']=['SmsLog.id'=>'desc'];
		$smsLogs = $this->paginate('SmsLog', $conditions );
		$this->set('smsLogs', $smsLogs);
	}

//	function owner_view($id = null) {
//		if (!$id) {
//			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('sms log', true)),true));
//			$this->redirect(array('action'=>'index'));
//		}
//		$this->set('smsLog', $this->SmsLog->read(null, $id));
//	}
//
//	function owner_add() {
//		if (!empty($this->data)) {
//			$this->SmsLog->create();
//			if ($this->SmsLog->save($this->data)) {
//				$this->flashMessage(sprintf (__('The %s has been saved', true), __('sms log',true)), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			} else {
//				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('sms log',true)));
//			}
//		}
//		$sites = $this->SmsLog->Site->find('list');
//		$users = $this->SmsLog->User->find('list');
//		$this->set(compact('sites', 'users'));
//	}
//
//	function owner_edit($id = null) {
//		if (!$id && empty($this->data)) {
//			$this->flashMessage(sprintf (__('Invalid %s.', 'sms log',true)));
//			$this->redirect(array('action'=>'index'));
//		}
//		$smsLog = $this->SmsLog->read(null, $id);
//		if (!empty($this->data)) {
//			if ($this->SmsLog->save($this->data)) {
//				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('sms log',true)), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			} else {
//				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('sms log',true)));
//			}
//		}
//		if (empty($this->data)) {
//			$this->data = $smsLog;
//		}
//		$sites = $this->SmsLog->Site->find('list');
//		$users = $this->SmsLog->User->find('list');
//		$this->set(compact('sites','users'));
//		$this->render('owner_add');
//	}
//
//	function owner_delete($id = null) {
//		if (empty($id) && !empty ($_POST['ids'])) {
//			$id = $_POST['ids'];
//		 } 
// 		if (!$id && empty ($_POST)) {
//			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('sms log',true)));
//			$this->redirect(array('action'=>'index'));
//		}
//		$module_name= __('smsLog', true);
//		if(count($id) > 1){
//			$module_name= __('smsLogs', true);
//		 } 
//		$smsLogs = $this->SmsLog->find('all',array('conditions'=>array('SmsLog.id'=>$id)));
//		if (empty($smsLogs)){
//			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
//			$this->redirect($this->referer(array('action' => 'index'), true));
//		}
//		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
//			if ($_POST['submit_btn'] == 'yes' && $this->SmsLog->deleteAll(array('SmsLog.id'=>$_POST['ids']))) {
//				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			}
//			else{
//				$this->redirect(array('action'=>'index'));
//			}
//		}
//		$this->set('smsLogs',$smsLogs);
//	}
//
//    function owner_send_to_all_clients()
//    {
//
//        //die(debug(PlivoSendSMS::parseCountryIso("EG", "1155478998")));
//        $owner = getAuthOwner();
//        $this->loadModel('Client');
//        $clients = $this->Client->find('all',array('Client.staff_id = ' . $owner['staff_id']));
//        $this->set('clients', $clients);
//
//        //get the current active package details
//        $current_package = PlivoSendSMS::getActivePackageForCurrentUser();
//        $this->set('current_package', $current_package );
//
//        if(!empty($this->data))
//        {
//            //$response = PlivoSendSMS::sendMessage("01119501276", "testing testing ");
//            //die(debug($response));
//            $clients_ids = array();
//            $message = $this->data['SmsLog']["message"];
//
//            foreach($this->data['SmsLog'] as $key => $value)
//            {
//                if($value == 1)
//                      $clients_ids[] = $key;
//            }
//
//            $this->loadModel('Client');
//            $clients = $this->Client->find('all', array( 'conditions'=>array('Client.id'=>$clients_ids ) ) );
//
//            $have_sent_to = array();
//
//            //loop on the clients get country codes and
//            foreach($clients as $client)
//            {
//                $name = $client['Client']['business_name'];
//                $phone1 = $client['Client']['phone1'];
//                $phone2 = $client['Client']['phone2'];
//
//                $phone = (!empty($phone1) ? $phone1 :$phone2);
//                $phone = str_replace(' ', '', $phone);
//                //parse the phone number with the country code
//                $country_code = $client['Client']['country_code'];
//                if(empty($country_code ))
//                {
//                    PlivoSendSMS::insertSmsLog(
//                        /*site_id*/      getAuthOwner('id'),/*user_id*/       $client['Client']['id'] , /*message*/       $message,
//                        /*phone*/        $phone            ,/*message_price*/ 0                       , /*country_id*/    0,
//                        /*real_cost*/    0                 ,/*sms_size*/      mb_strlen($message)     , /*sent_date*/     date("Y/m/d/h/m/s") ,
//                        /*ref_type*/     1                 ,/*ref_id*/        getAuthOwner('user_id') , /*is_sent*/       0,
//                        /*error_number*/ 101               ,/*order_id*/      $current_package['id']  , /*credits_after*/ 0
//                    );
//                    continue;
//                }
//                $phone = PlivoSendSMS::preparePhone($country_code, $phone);
//
//
//                //get message price
//                //load country_prices_model
//                $this->loadModel('SmsCountryPrice');
//                $country_details = $this->SmsCountryPrice->find('all',array('conditions'=>array('SmsCountryPrice.country_code'=>$country_code)));
//                //get the price of message = price_sms * factor
//                $message_price = $country_details[0]['SmsCountryPrice']['price_sms'] * $country_details[0]['SmsCountryPrice']['factor'];
//
//                //check for the System credit
//                if(PlivoSendSMS::checkSystemCredit())
//                {
//                    //check for user credit
//                    if(PlivoSendSMS::checkCurrentUserCredit($message_price))
//                    {
//                        //send the message
//                        $response = PlivoSendSMS::sendMessage($phone, $message);
//
//                        //die(debug($response));
//                        //success responce will not be false
//                        if( $response != false)
//                        {
//                            //subtract the cost from the current active package
//                            PlivoSendSMS::updateCurrentPackage($message_price);
//
//                            //insert message into sms_logs
//                            $current_remaining = PlivoSendSMS::getActivePackageForCurrentUser();
//                            $current_package = PlivoSendSMS::getActivePackageForCurrentUser();
//                            PlivoSendSMS::insertSmsLog(
//                               /*site_id*/      getAuthOwner('id')                                  ,/*user_id*/       $client['Client']['id'] ,/*message*/        $message,
//                               /*phone*/        $phone                                              ,/*message_price*/ $message_price          ,/*country_id*/     $country_details[0]['SmsCountryPrice']['id'],
//                               /*real_cost*/    $country_details[0]['SmsCountryPrice']['price_sms'] ,/*sms_size*/      mb_strlen($message)     ,/*sent_date*/      date("Y/m/d/h/m/s") ,
//                               /*ref_type*/     1                                                   ,/*ref_id*/        getAuthOwner('user_id') ,/*is_sent*/        1,
//                               /*error_number*/ 100                                                   ,/*order_id*/      $current_package['id']  ,/*credits_after*/  $current_remaining['remaining']
//                            );
//                            $have_sent_to[] = $name;
//
//
//                        }
//                        else // fail response will be false
//                        {
//
//                            //insert message into sms_logs with the error of the message
//                            $current_remaining = PlivoSendSMS::getActivePackageForCurrentUser();
//                            $current_package = PlivoSendSMS::getActivePackageForCurrentUser();
//                            PlivoSendSMS::insertSmsLog(
//                                /*site_id*/      getAuthOwner('id')                                  ,/*user_id*/       $client['Client']['id'] ,/*message*/        $message,
//                                /*phone*/        $phone                                              ,/*message_price*/ $message_price          ,/*country_id*/     $country_details[0]['SmsCountryPrice']['id'],
//                                /*real_cost*/    $country_details[0]['SmsCountryPrice']['price_sms'] ,/*sms_size*/      mb_strlen($message)     ,/*sent_date*/      date("Y/m/d") ,
//                                /*ref_type*/     1                                                   ,/*ref_id*/        getAuthOwner('user_id') ,/*is_sent*/        0,
//                                /*error_number*/ 102                                                 ,/*order_id*/      $current_package['id']  ,/*credits_after*/  $current_remaining['remaining']
//                            );
//                        }
//
//                    }
//                    else
//                    {
//                        $this->flashMessage(__('Not enough credit in Your Account', true));
//                        break;
//                    }
//
//                }
//                else
//                {
//                    $this->flashMessage(__('Not enough credit in the System Account', true));
//                    break;
//                }
//
//            }
//            //die(debug($have_sent_to));
//
//            //$this->flashMessage(sprintf (__('%s deleted', true), $have_sent_to), 'Sucmessage');
//            if(!empty($have_sent_to)){
//                $names = implode(', ', $have_sent_to);
//                $this->flashMessage(sprintf (__('message have been sent to %s', true), $names), 'Sucmessage');
//            }
//            $this->redirect(array('action'=>'send_to_all_clients'));
//        }
//
//
//    }
//    function owner_test()
//    {
//        //die(debug(getAuthOwner('user_id')));
//    }
}
?>
