<?php

use Izam\Daftra\Portal\Models\Contact as PortalContact;
use Izam\Daftra\Portal\Services\ContactService;


/**
 * @property EmailComponent $Email
 */
class ContactsController extends AppController {
	var $name = 'Contacts';
	var $helpers = array ('Html', 'Form' );
	var $components = array ('Session', 'Email' );
	
	function admin_index() {
		$this->Contact->recursive = 0;
		$conditions = $this->_filter_params();
		$this->paginate ['Contact'] ['order'] = 'Contact.id desc';
		$this->set ( 'contacts', $this->paginate ( 'Contact', $conditions ) );
	}
	//--------------------------------
	function admin_view($id = null) {
		if (! $id) {
			$this->Session->setFlash (__( 'Invalid Contact.', true ));
			$this->redirect ( array ('action' => 'index' ) );
		}
		$contact = $this->Contact->read ( null, $id );
		$this->set ( 'contact', $contact );
	}
	
	//--------------------------------
	function admin_delete($id = null) {
		if (! $id) {
			$this->flashMessage ( __ ( 'Invalid id for Contact', true ) );
			$this->redirect ( array ('action' => 'index' ) );
		}
		if (PortalContact::where('id', $id)->delete()) {
			$this->flashMessage ( __ ( 'Contact deleted', true ), 'Sucmessage' );
			$this->redirect ( array ('action' => 'index' ) );
		}
	}
	//--------------------------------
	function admin_delete_multi() {
		if (empty ( $_POST ['ids'] ) || ! is_array ( $_POST ['ids'] )) {
			$this->flashMessage ( __ ( 'Invalid ids for Contact', true ) );
			$this->redirect ( array ('action' => 'index' ) );
		}
		if (PortalContact::whereIn('id', $_POST['ids'])->delete()) {
			$this->flashMessage ( __ ( 'Contact items deleted', true ), 'Sucmessage' );
			$this->redirect ( array ('action' => 'index' ) );
		} else {
			$this->flashMessage ( __ ( 'Unknown error', true ) );
			$this->redirect ( array ('action' => 'index' ) );
		}
	
	}
	//--------------------------------
	
	
	function _filter_params($params = false, $filters = array(), $passedModelName = false) {
        $conditions = parent::_filter_params();
		
		debug($conditions); 

        if (!empty($conditions['Client.name LIKE'])) {
            $conditions[] = "CONCAT_WS(' ', Client.first_name, Client.last_name, Client.business_name, Client.email) LIKE '{$conditions['Client.name LIKE']}'";
            unset($conditions['Client.name LIKE']);
        }

        if (!empty($conditions['Client.address LIKE'])) {
            $conditions[] = "CONCAT_WS(' ', Client.address1, Client.address2, Client.city, Client.state) LIKE '{$conditions['Client.address LIKE']}'";
            unset($conditions['Client.address LIKE']);
        }

        if (!empty($conditions['Client.status']) || (isset($conditions['Client.status']) && $conditions['Client.status'] == '0')) {
            $conditions[] = "Client.suspend = {$conditions['Client.status']}";
            unset($conditions['Client.status']);
        }



        return $conditions;
    }

	function owner_contactus() {
		$this->pageTitle .= 'Contact us';

		$this->set ( 'sitename', $this->config ['txt.site_name'] );
		if (! empty ( $this->data ) || $_POST['support_channel']) {
			$support_channel = $_POST['support_channel'];
			if(isset($support_channel) && $support_channel == 1){
				$this->prepare_data_for_email();
			}

            if ($this->data['Contact']['type'] == 0) {
                $to = explode(',', $this->config ['txt.sales_mail']);
            } else {
                $to = explode(',', $this->config ['txt.admin_mail']);
            }

			$this->Contact->set ( $this->data );
            if (!$this->Contact->validates()) {
                if (IS_REST) {
                    http_response_code(400);
                    return json_encode($this->Contact->validationErrors);
                } else {
                    $this->set('errors', $this->Contact->validationErrors);
                    return;
                }
            }
			if (ContactService::create($this->data['Contact'])) {
				$this->Email->to = $to[0];
                array_shift($to);
                if (!empty($to)) {
                    $this->Email->cc = $to;
                }
				$this->Email->sendAs = 'html';
				$this->Email->layout = 'contact';
				$this->Email->template = 'contact';
			//	$this->Email->from = $this->data ['Contact'] ['name'] . '<' . $this->data ['Contact'] ['email'] . '>';
                $this->Email->replyTo = $this->data['Contact']['email'];
                $this->Email->from = $this->data['Contact']['name'] . '<' . str_replace("@","_",$this->data['Contact']['email'])."@".Domain_Short_Name . '>';
				$this->Email->subject = "{$this->config['txt.site_name']}: {$this->data['Contact']['subject']}";

                $attachments = [];
				foreach($_FILES as $file){
					$attachments[$file['name']] = $file['tmp_name'];
				}
				$this->Email->attachments = $attachments;
				$this->Email->delivery = "mail";
                $this->data['Contact']['message'] .= "\r\n Site ID: ". getCurrentSite('id');
                $this->data['Contact']['message'] .= "\r\n Business Name: ". getCurrentSite('business_name');
                $this->data['Contact']['message'] .= "\r\n PIN NUMBER: ". getCurrentSite('support_pin');
                if ($this->data['Contact']['type'] == 1 && isset($_POST['phone'])){
                    $this->data['Contact']['message'] .= "\r\n Phone Number: ". $_POST['phone'];
                }
                if ($this->Email->send ( $this->data ['Contact'] ['message'] )) {
					$this->data = array ();
					if($support_channel){
						http_response_code(200);
						die(json_encode(['message' => 'Your message has been sent.', 'code' => 200]));
					}
					$this->flashMessage ( __ ( 'Your message has been sent.', true ), 'Sucmessage' );
				} else {
					if($support_channel){
						http_response_code(400);
						die(json_encode(['message' => 'can\'t send email', 'code' => 400]));
					}
					$this->flashMessage ( __ ( 'can\'t send email', true ) );
				}
			} else {
				if($support_channel){
					http_response_code(400);
					die(json_encode(['message' => 'Your message could not be saved, please check for input errors and try again.', 'code' => 400, 'errors' => $this->Contact->ValidationErrors]));
				}
				$this->flashMessage ( __ ( 'Your message could not be saved, please check for input errors and try again.', true ) );
			}
		}
	}

	function prepare_data_for_email() {
		$site_data = getCurrentSite();
		$this->data['Contact']['site_id'] = $site_data['id'];
		$this->data['Contact']['site_name'] = $site_data['business_name'];
		$this->data['Contact']['country_code'] = $site_data['country_code'];
		$this->data['Contact']['plan_id'] = $site_data['plan_id'];
		$this->data['Contact']['expiry_date'] = $site_data['expiry_date'];
		$this->data['Contact']['user_id'] = $site_data['user_id'];
		$this->data['Contact']['name'] = $site_data['first_name'] . ' ' . $site_data['last_name'];
		$this->data['Contact']['phone'] = $site_data['phone1'] ?? $site_data['phone2'];
		$this->data['Contact']['email'] = $site_data['email'];
		$this->data['Contact']['subject'] = $_POST['subject'];
		$this->data['Contact']['message'] = $_POST['message'];
		$this->data['Contact']['type'] = $_POST['type'];
	}
}

