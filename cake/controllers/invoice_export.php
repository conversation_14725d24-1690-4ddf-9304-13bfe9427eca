<?php

use Izam\Attachment\Service\AttachmentsService;

		$this->loadModel ('InvoiceItem' );
		$this->set('conditions_key',$conditions_key);

	 set_time_limit ( 3600);
	 ini_set('memory_limit','20G');

		$order_list=array(
			'date_desc'=>array('title'=>__('Date',true).' ('.__('Recent First',true).')','fields'=>'Invoice.date DESC, Invoice.id DESC'),
			'date_asc'=>array('title'=>__('Date',true).' ('.__('Oldest First',true).')','fields'=>'Invoice.date , Invoice.id '),
			'no_desc'=>array('title'=>__('Number',true).' ('.__('Biggest First',true).')', 'fields'=>'Invoice.no DESC'),
			'no_asc'=>array('title'=>__('Number',true).' ('.__('Smallest First',true).')','fields'=>'Invoice.no'),
			'amount_desc'=>array('title'=>__('Total',true).' ('.__('Largest First',true).')','fields'=>'Invoice.summary_total DESC'),
			'amount_asc'=>array('title'=>__('Total',true).' ('.__('Lowest First',true).')','fields'=>'Invoice.summary_total'),
			'status_paid_first'=>array('title'=>__('Status',true).' ('.__('Paid First',true).')','fields'=>'Invoice.payment_status DESC'),
			'amount_unpaid_first'=>array('title'=>__('Status',true).' ('.__('Unpaid First',true).')','fields'=>'Invoice.payment_status'),
		);
		
		//echo getPaymentStatuses ( 1 );
		$fields_list=array(
			'id'=> array ('title' => "ID" , 'field' => 'Invoice.id'),
			'no'=> array ('title' =>"$invoice_type_name Number" , 'field' => 'Invoice.no' ),
			'client_no'=> array ('title' =>'Client Number', 'field' =>'Client.client_number'),//Client.no
			'date'=> array ('title' => "$invoice_type_name Date",'field' => ('Invoice.date' )),
			'issue_date'=> array ('title' =>'Issue Date','field' => ('Invoice.issue_date' ) ),
			'payment_status'=> array ('title' =>'Payment Status', 'field' =>  ('Invoice.payment_status' )),//Switched to the correct value Invoice::getPaymentStatuses
			'currency_code'=> array ('title' =>'Currency','field' => ('Invoice.currency_code' )),
			'client_business_name'=> array ('title' =>'Client Business Name','field' => ('Client.business_name' )),
			'client_first_name'=> array ('title' =>'Client First Name','field' => ('Client.first_name' )),
			'client_last_name'=> array ('title' =>'Client Last Name','field' => ('Client.last_name' )),
			'client_full_name'=> array ('title' =>'Client Full Name','field' => ("CONCAT(Client.business_name, ' (' , Client.first_name , ' ',Client.last_name , ')' ) ")),  //Business Name (First + Last) Or Business Name 
			'client_email'=> array ('title' =>'Client Email','field' => ("Client.email" )),
			'client_phone1'=> array ('title' =>'Client Phone1','field' => ("Client.phone1" )),
			'client_phone2'=> array ('title' =>'Client Phone2','field' => ("Client.phone2" )),
			'client_address1'=> array ('title' =>'Client Line Address1','field' => ('Client.address1' )),
			'client_address2'=> array ('title' =>'Client Line Address2','field' =>('Client.address2' )),
			'client_postal_code'=> array ('title' =>'Client Postal Code','field' =>('Client.postal_code' )),
			'client_city'=> array ('title' =>'Client City','field' => ('Client.city' )),
			'client_state'=> array ('title' =>'Client State','field' => ('Client.state' )),
			'client_full_address'=> array ('title' =>'Client Full Address', 'field' =>  ('CONCAT(Client.address1,"\n",Client.address2,"\n",Client.city,", ",Client.state, ", ",Client.postal_code,"\n" , Client.country_code)')),//Address 1\n Address 2 \City, State  Postal Code\nCountry Code
			'client_country_code'=> array ('title' =>'Client Country Code','field' => ('Client.country_code' )),
			'client_secondary_name'=> array ('title' =>'Client Shipping Name','field' => ('Client.secondary_name' )),
			'client_secondary_full_address'=> array ('title' =>'Client Full Shipping Address', 'field' =>('CONCAT(Client.secondary_address1,"\n",Client.secondary_address2,"\n",Client.secondary_city,", ",Client.secondary_state, ", ",Client.secondary_postal_code,"\n" , Client.secondary_country_code)')),     //Address 1\n Address 2 \City, State  Postal Code\nCountry Code
			'client_secondary_country_code'=> array ('title' =>'Client Shipping Country Code','field' =>('Client.secondary_country_code' )),
			'client_secondary_address1'=> array ('title' =>'Client Shipping Address1','field' =>('Client.secondary_address1' )),
			'client_secondary_address2'=> array ('title' =>'Client Shipping Address2','field' =>('Client.secondary_address2' )),
			'client_secondary_city'=> array ('title' =>'Client Shipping City','field' => ('Client.secondary_city' )),
			'client_secondary_state'=> array ('title' =>'Client Shipping State','field' => ('Client.secondary_state' )),
			'client_secondary_postal_code'=> array ('title' =>'Client Shipping Postal Code','field' => ('Client.secondary_postal_code' )),
			'staff_id'=> array ('title' =>'Staff Member ID', 'field' =>  ('Staff.code' )),//Staff.id
			'staff_name'=> array ('title' =>'Staff Member Name', 'field' =>  ('Staff.name' )), //Staff.name
			'draft'=> array ('title' =>'Is Draft', 'field' =>('Invoice.draft' )), //1=>Yes, 0=> No
			'discount_precentage'=> array ('title' =>'Discount Percentage','field' =>('Invoice.discount' )),
			'deposit'=> array ('title' =>'Deposit','field' => ('Invoice.deposit' )),
			'terms'=> array ('title' =>'Payment Terms','field' => ('Invoice.terms' )),
//			'tax1'=> array ('title' =>'Tax1 Name','field' => ('Invoice.label_tax1' )),	
//			'tax2'=> array ('title' =>'Tax2 Name','field' =>('Invoice.label_tax2' )),	
			'summary_subtotal'=> array ('title' =>'Subtotal','field' => ('Invoice.summary_subtotal' )),
			'summary_discount'=> array ('title' =>'Discount','field' =>('Invoice.summary_discount' )),
//			'summary_tax1'=> array ('title' =>'Tax1 value','field' =>('Invoice.summary_tax1' )),
//			'summary_tax2'=> array ('title' =>'Tax2 Value','field' =>('Invoice.summary_tax2' )),
			'discount_amount'=> array ('title' =>'Discount Amount','field' => ('Invoice.discount_amount' )),
			'shipping_option_name'=> array ('title' =>'Shipping Name','field' =>('ShippingOption.name' )),
			'shipping_amount'=> array ('title' =>'Shipping Fees','field' =>('Invoice.shipping_amount' )),
			'summary_total'=> array ('title' =>'Total','field' => ('Invoice.summary_total' )),
			'summary_paid'=> array ('title' =>'Total Paid','field' => ('Invoice.summary_paid' )),
			'summary_unpaid'=> array ('title' =>'Balance Due','field' => ('Invoice.summary_unpaid' )),
			'summary_deposit'=> array ('title' =>'Deposit','field' => ('Invoice.summary_deposit' )),
			'notes'=> array ('title' =>'notes','field' => ('Invoice.html_notes' )), //html_notes
			'from'=> array ('title' =>'From Date','field' => ('Invoice.from' )),
			'to'=> array ('title' =>'To Date','field' => ('Invoice.to' )),
			'created'=> array ('title' =>'Created','field' => ('Invoice.created' )),
			'modified'=> array ('title' =>'Modified','field' =>('Invoice.modified' )),
			'due_after' => array('title' => 'Due date', 'field' => ('Invoice.due_after')),
		);

		$item_fields_list=array(
			'item'=>array ('title' =>'Item', 'field' => ( 'InvoiceItem.item' )),
			'description'=>array ('title' =>'Description', 'field' => ( 'InvoiceItem.description' )),
			'unit_price'=>array ('title' =>'Unit Price','field' =>( 'InvoiceItem.unit_price' )),
			'quantity'=>array ('title' =>'Quantity','field' =>( 'InvoiceItem.quantity' )),
			'discount'=>array ('title' =>'Discount','field' =>( 'InvoiceItem.discount' )),
			'Tax1 value'=>array ('title' =>'Tax1 value','field' =>( 'InvoiceItem.summary_tax1' )),
			'Tax1 name'=>array ('title' =>'Tax1 name','field' =>( 'InvoiceTax.tax1' )),
			'Tax2 value'=>array ('title' =>'Tax2 value','field' =>( 'InvoiceItem.summary_tax2' )),
			'Tax2 name'=>array ('title' =>'Tax2 name','field' =>( 'InvoiceTax.tax2' )),
			'subtotal'=>array ('title' =>'Subtotal','field' =>( 'InvoiceItem.subtotal' )),
			'product_id'=>array ('title' =>'Product ID','field' =>( 'InvoiceItem.product_id' )),
			'product_code'=>array ('title' =>'Product Code','field' =>( 'InvoiceItem.product_code' )),
		
		);

		if(!empty($conditions_key)) {
			$conditions = $this->Session->read($conditions_key);
		}


		if(isset($_POST['ids']) && $_POST['ids']){
			
		
			$this->set('ids',$_POST['ids']);
			$conditions['Invoice.id'] = !is_array($_POST['ids'])?explode(',', $_POST['ids']):$_POST['ids'];
		}
		
		if(!empty($this->params['url']['filter_ids'])){
			$conditions['Invoice.id'] = explode(',', $this->params['url']['filter_ids']);
		} else if(isset($_POST['filter_ids']) && $_POST['filter_ids'])
		{
			$conditions['Invoice.id'] = explode(',', $_POST['filter_ids']);
		}

		$customTempData = [];
		$item_columns_list = [];
		$itemColumnsNames = [];
		$itemColumnsLabels = [];
		$conditions['Invoice.type']=$invoice_type;

$count = $this->Invoice->find('count', ['recursive' => -1, 'conditions' => $conditions]);

if ($count > 5000) {
	$this->flashMessage(__("You can't export more than 5000 record, Please change filters to match limit.", TRUE));
	return $this->redirect("/owner/invoices");
}

		$invoices_item_colums = $this->Invoice->find('all', ['recursive' => -1, 'conditions' => $conditions + ['Invoice.item_columns !=' => '']]);
		$itemColumns=[];
		$i=0;
		foreach ($invoices_item_colums as $item) {
			foreach (json_decode($item['Invoice']['item_columns'], true) as $key => $item_values) {
				if(is_array($item_values) && !in_array($item_values['label'],$itemColumnsLabels)) {
					$i++;
					$item_columns_list[$i] = $item_values['label'];
					$itemColumnsNames[$i] = $item_values['name'];
					$itemColumnsLabels[$i] = $item_values['label'];
				}
			}
		}

$this->set('item_columns_list',$item_columns_list);

		$this->loadModel('InvoiceCustomField');
			$this->InvoiceCustomField->recursive = -1;
                        $custom_conditions = [] ; //conditions for invoice custom field
                        foreach ( $conditions as $k => $v ) {
                            if ( strpos($k , 'Client.') === false ){
                                $custom_conditions[$k] = $v ; // Removing Client conditions from invoice custom fields
                            }
                        }
			$params['conditions']=$custom_conditions;
			if ($conditions["Invoice.id"]) {
				$alldata = $this->InvoiceCustomField->find('all', ['conditions' => ['invoice_id' => $conditions["Invoice.id"]]]) ;
			} else {
				$alldata = $this->InvoiceCustomField->find('all' ) ;
			}
			$all_custom_fields = [];
			foreach ($alldata as $d){
				//$all_custom_fields[$d['InvoiceCustomField']['label']] =['title' => $d['InvoiceCustomField']['label'] , 'field' => 'custom' ];
				$fields_list[$d['InvoiceCustomField']['label']] =['title' => $d['InvoiceCustomField']['label'] , 'field' => 'custom' ];
				$customTempData[$d['InvoiceCustomField']['label']] = $d['InvoiceCustomField']['value'];
			}

		if(empty($conditions))
		{
			
			$this->flashMessage(__("Invalid list to export", true), 'Errormessage');
			$this->redirect(array('action' => 'index'));
		}
		if($report_template_id!=false&&$quick_report)
		{
			$this->loadModel ('SavedReport' ) ;
			//Get the Data
			$sr = $this->SavedReport->findById($report_template_id);
			$this->data=json_decode ( $sr['SavedReport']['data'] ,true);
			if(empty($this->data))
			{
				$this->flashMessage(__("Couldn't find this report template", true), 'Errormessage');
				$this->redirect(array('action' => 'export',$conditions_key));
			}
		}
		
 		// Default post data
		$data = $this->data;


		/**
		 * If data is passed in the url, then we are exporting to a file
		 * We support both get and post in the form action
		 */

		if(!empty($this->params['url']['data']) && !empty($this->params['url']['data']['export_to'])){
			$data = $this->params['url']['data'];
		}
		
		if(!empty($data))
		{	

			$params['conditions']=$conditions;
			$params['order'] = 'Invoice.date DESC, Invoice.id DESC';
			if(!empty($data['order'])&&in_array($data['order'],array_keys($order_list)))
			{
				$params['order']=$order_list[$data['order']]['fields'];
			}
			$params ['fields'] = [] ;
            $this->loadModel('Product');
			//$params['limit'] = 10 ;
			$this->Invoice->bindModel(
				array('belongsTo' => array(
					'ShippingOption' => array(
						'className' => 'ShippingOption',
					)
				)
				));
			$unbind = [
				'InvoiceReminder',
				'EmailLog',
				'InvoiceDocument',
			];

			$this->Invoice->unbindModel(
				['hasMany' => $unbind]
			);
			$products = [];

			$count = $this->Invoice->find('count' , $params );

			if($count > 5000 ) {
				$this->flashMessage(__("You can't export more than 5000 record, Please change filters to match limit.", TRUE));
				return $this->redirect("/owner/invoices");
			}
			$this->Invoice->unbindModel(
				['hasMany' => $unbind]
			);
			$this->Invoice->recursive = 1;
			$alldata = $this->Invoice->find('all' , $params ) ;

			$ProductImage= GetObjectOrLoadModel('ProductImage');

			foreach($alldata as &$invoice){
			 foreach($invoice['InvoiceItem'] as &$item){
         
			 if(!empty($item['product_id'])){
				 if(!isset($products[$item['product_id']])) {
					 $product=$this->Product->find('first',array('recursive'=>-1,'conditions'=>['Product.id'=>$item['product_id']])) ;
					 $products[$item['product_id']] = $product;
				 }


				 $ProductImageRow=$ProductImage->find('first',['conditions'=>['ProductImage.product_id'=>$item['product_id'],'ProductImage.default'=>1]]);
				 $masterImage = resolve(AttachmentsService::class)->getDefault('product' ,$item['product_id']);
 
				 $productMasterImage = '';
				 if(!empty($ProductImageRow['ProductImage']['file_full_path'])){
					 $productMasterImage = ("https://".getCurrentSite('subdomain').'/'.$ProductImageRow['ProductImage']['file_full_path']);
				 }elseif(isset($masterImage[0]->files)){
					 $productMasterImage = "https://".getCurrentSite('subdomain')."/v2/owner/entity/files/preview/" . ($masterImage[0]->files->id);
				 }
				  
				 $item['product_image']= $productMasterImage ;
				 $item['barcode']=$products[$item['product_id']]['Product']['barcode'];
				 $item['product_code']=$products[$item['product_id']]['Product']['product_code']; 
				}
             }
            }

			//If they didn't select anything .
			if (empty ($data['item_fields_select']) && ($data['item_select'] == "items" || $data['item_select']=="item_columns") ){

				$data['item_fields_select'] = array_keys ($item_fields_list);
			}
			if (empty ($data['item_columns_select']) && $data['item_select']=="item_columns" ){
				$data['item_columns_select'] = array_keys ($item_columns_list);
			}
			if (empty ($data['fields_select']) ){
				$data['fields_select'] = array_keys ($fields_list);
			}

			$rows = [] ;
			foreach ( $data['fields_select'] as $v ){
				$rows[1][$fields_list[$v]['title']] .= __($fields_list[$v]['title'] , true  );
			}
			foreach ( $data['item_fields_select'] as $v ){
				$rows[1][ $item_fields_list[$v]['title'] ].= __($item_fields_list[$v]['title'] , true  );
			}
 
			foreach ( $data['item_columns_select'] as $v ){
				$rows[1][ $v ].= __($item_columns_list[$v] , true  );
			}
			//debug ( $rows ) ; 
			if ( isset ($_POST['save_as_template'] ) && $_POST['save_as_template'] =="1" )
			{
				$template_name = $_POST['template_name'] ; 
				$this->loadModel ('SavedReport' );
				$data['SavedReport'] = ['site_id' => getCurrentSite('id')  , 'title' => $template_name , 'type' => INVOICES_EXPORT , 'data' => json_encode($data) ];
				$this->SavedReport->set ($data );
				$this->SavedReport->save ($data);
			}

			$applyCustomFields = false;
			
			foreach($data['fields_select'] as $k => $fieldSelected) {
				if( $customTempData[$fieldSelected] ) {
					$applyCustomFields = true;
                    break;
				}
			}

			//Putting data for CSV file
			$i = 2;
			App::import('Vendor', 'PlaceHolder');
			foreach ( $alldata as $d ) {
                $placeholders = [];
                if ($applyCustomFields) {
					if (count($d['InvoiceCustomField']) > 0) {
					
						foreach ($d['InvoiceCustomField'] as $customField) {

							if (!empty($placeholders)) {
								break;
							}

							foreach ($customField as $k => $v) {
								preg_match('#{%(.*?)%}#', $v, $matches);
								if (!empty($matches)) {
									/** this can have much performance optimization */
									$placeholders = array_merge(
										PlaceHolder::invoice_get_all_place_holders($d),
										PlaceHolder::invoice_payment_place_holder($d['InvoicePayment'][0] ?? []),
										PlaceHolder::invoice_custom_field_value_adder($d)
									);
									break;
								}
							}
							
						}
					}
				}	
                            $currentTaxes = [] ;
                            foreach($d['InvoiceTax'] as $t){
                                $currentTaxes[$t['tax_id']] = $t['name'];
                            }
				if ( $d['Invoice']['staff_id'] == 0 ){
					$d['Staff']['name'] = getCurrentSite('first_name' )." ".getCurrentSite('last_name');
				}
				$d['Invoice']['html_notes'] = strip_tags ($d['Invoice']['html_notes']  );
				$d['Invoice']['terms'] = strip_tags ($d['Invoice']['terms']  );
				foreach ($data['fields_select'] as $k => $v ){
					$field = explode ('.',  $fields_list[$v]['field'] );

					if ( $v =="payment_status" ){
						if($d['Invoice']['type'] == Invoice::Estimate) {
							$rows[$i][$fields_list[$v]['title']] = Invoice::getEstimateStatuses()[$d['Invoice']['payment_status']] ;
						} else {
							$rows[$i][$fields_list[$v]['title']] = Invoice::getPaymentStatuses()[$d['Invoice']['payment_status']] ;
						}

					}else if ( $v =="client_full_name" ){
						$rows[$i][$fields_list[$v]['title']] = ( $d['Client']['first_name']==""&& $d['Client']['last_name']=="")?  "{$d['Client']['business_name']} ({$d['Client']['first_name']} {$d['Client']['last_name']})" : $d['Client']['business_name'];
					}else if ( $v ==  "client_secondary_full_address" ){
						$rows[$i][$fields_list[$v]['title']] = "{$d['Client']['secondary_address1']}\n{$d['Client']['secondary_address2']}\n{$d['Client']['secondary_city']}, {$d['Client']['secondary_state']},{$d['Client']['secondary_postal_code']}\n{$d['Client']['secondary_country_code']}" ;  ;
					}else if ( $v ==  "client_full_address" ){
						$rows[$i][$fields_list[$v]['title']] = "{$d['Client']['address1']}\n{$d['Client']['address2']}\n{$d['Client']['city']}, {$d['Client']['state']},{$d['Client']['postal_code']}\n{$d['Client']['country_code']}" ;
					}else if ( $v ==  "staff_id" ){
						$rows[$i][$fields_list[$v]['title']] = $d['Staff']['code']? "#{$d['Staff']['code']}":$d['Staff']['id'];
					}
					else if ($fields_list[$v]['field']  == "custom" )  {
						foreach ( $d['InvoiceCustomField'] as $cc ){
							if( $cc['label'] == $fields_list[$v]['title'] ){
								$rows[$i][$fields_list[$v]['title']] = empty($placeholders) ? $cc['value'] : PlaceHolder::replace($cc['value'], array_keys($placeholders), array_values($placeholders),true);
							}
						}
					}else if ($v == "due_after" )  {
						$rows[$i][$fields_list[$v]['title']] = date('Y-m-d', strtotime('+' . $d['Invoice']['due_after'] . ' days', strtotime($d['Invoice']['date'])));
					}else {
						$rows[$i][$fields_list[$v]['title']] = $d[$field[0]][$field[1]];
					}

				}
				$temp = [];
				foreach ($rows[1] as $key => $value) {
					if (is_array($rows[$i]) && !array_key_exists($key, $rows[$i])) {
						$temp[$key] = '';
					}else{
						$temp[$key] = $rows[$i][$key];
					}
				}
				$rows[$i] = $temp;

				$keymap=[];
				$keymap['field1'] = 'item';
				$keymap['field2'] = 'description';
				$keymap['field3'] = 'col_3';
				$keymap['field4'] = 'col_4';
				$keymap['field5'] = 'col_5';

				if (!empty ($data['item_fields_select']) && ($data['item_select'] == "items" || $data['item_select'] == "item_columns") ) {
  
					foreach ($d['InvoiceItem'] as $dinv_item) {
						$rows[$i] = $temp;
						foreach ($data['item_fields_select'] as $v) {

							$field = explode('.', $item_fields_list[$v]['field']);
							$model = $field[0];
							if ($model == "InvoiceTax") {
								$rows[$i][$item_fields_list[$v]['title']] = $currentTaxes[$dinv_item[$field[1]]];
							} else {
								$rows[$i][$item_fields_list[$v]['title']] = $dinv_item[$field[1]];

							}

						}

						
						foreach ($data['item_columns_select'] as $NewKey => $vcl) {
							$final_name = null;
							$label = $itemColumnsLabels[$vcl];
							$name = $itemColumnsNames[$vcl];
							$decode = json_decode($d['Invoice']['item_columns'], true);
						 
							foreach ($decode as $keyy => $vvalue) {

								if ($vvalue['name'] == $name) {
									$final_name = $keymap[$keyy];
 									   break;
								}
							}
			                $rows[$i][$vcl] = ($dinv_item[$final_name])? $dinv_item[$final_name] : $dinv_item[$name];
                         
						}
					 
						$i++;
					}

				}

				$i ++;
			}
			App::import('Vendor', 'csv');
			if(  $data['export_to'] == 'xml' ){
				$exporter = new ExportDataExcel('browser', 'data.xml');
			}else if(  $data['export_to'] == 'csv_semicolon' ){
				$exporter = new ExportDataCSV('browser', 'data.csv' , ";" );
			}else if(  $data['export_to']== 'excel' ){
				$exporter = new ExportDataExcel('browser', 'data.xls');
			}else {
				$exporter = new ExportDataCSV('browser', 'data.csv');
			} 
			$exporter->initialize();
			
			foreach ( $rows as $r ){
				$exporter->addRow($r );
			}
			
			$exporter->finalize();die ; 
			 
		}
		if ( $report_template_id ){
			$this->loadModel('SavedReport');
			$sr = $this->SavedReport->findById($report_template_id);
			$this->data=json_decode ( $sr['SavedReport']['data'] ,true);
			$this->set ( 'report_template_id' , $report_template_id );
		}
		$this->set ( 'order_list' , $order_list );
		$this->set ( 'fields_list' , $fields_list );
		$this->set ( 'item_fields_list' , $item_fields_list );

?>
