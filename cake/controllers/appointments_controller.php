<?php

use App\Helpers\UsersHelper;
use Izam\Daftra\Workflow\Repositories\WorkflowTypeRepository;
use Izam\Daftra\Workflow\WorkflowService;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use Izam\Daftra\Common\Factories\SocialMediaLinkCreatorFactory;

/**
 * @property Tooltip $Tooltip
 */
class AppointmentsController extends AppController {

    var $name = 'ClientAppointments';
    var $helpers = array('Html', 'Form', 'Fck', 'Mixed');
    var $more_js_lables = array('Today', 'Day', 'Month');

	
	function check_filter_conditions() {
		return ;
	}

    function owner_index($item_type = 0 , $partner_type = 0, $response_type = 'data') {

        $this->loadModel('Timezone');
        $this->loadModel('FollowUpReminder');
        $this->loadModel('Post');
        $this->loadModel('Invoice');
        $this->loadModel('Client');
        $this->loadModel('FollowUpAction');
        $this->loadModel('FollowUpStatus');
        $this->loadModel('ClientAppointment');
        $this->loadModel('ItemStaff');
        if ( isset ( $_GET['no_client']) && $_GET['no_client'] == 1)
        {
            $this->set ( 'no_client' , 1 ) ;
        }
        
        if ( !isset ( FollowUpStatus::$types_diff[$item_type])  && !isset(FollowUpReminder::$partner_types[$partner_type])){
			if(IS_REST) $this->cakeError('error400');
            //$this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect('/owner/appointments/index/0/1');
        }

        // warning suppress
        $name = null;
        $class_name = null;
        if (array_key_exists($item_type, FollowUpStatus::$types_diff))
        {
            $name=FollowUpStatus::$types_diff[$item_type]['name'];
            $name=empty($name)?'All':($name.'s');
            $class_name = FollowUpStatus::$types_diff[$item_type]['class'];
        }

        $this->set('title_for_layout',   __($name." Appointments", true ));
        // end warning suppress
        //bind related models
//		$this->FollowUpReminder->bindRelatedModels($item_type);
      
		$site = getCurrentSite();
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->more_js_lables);
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $staff_id = getAuthOwner('staff_id');



        if ((!check_permission(View_All_Attachments_And_Notes_For_All_Clients) && !check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) && !check_permission(View_His_Own_Notes_Attachments_Only))) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->FollowUpReminder->applyBranch['onFind'] = true;

        if(ifPluginActive(BookingPlugin))
        {
            $bookingsCount = $this->FollowUpReminder->getTypeAppointmentsCount(FollowUpReminder::BOOKING_TYPE);
            $this->set('bookingsCount', $bookingsCount);
        }


        $this->set('back_to_client',$_GET['back_to_client'] ?? null);
        $this->set('staffs', $this->ClientAppointment->Staff->getList());
        $statuses['-1'] = __('All', true);
        $statuses += $this->ClientAppointment->getStatuses();
		unset ( $statuses[0]);

        $this->set('statuses', $statuses);

        $actions = $this->FollowUpAction->getList([1,2,3,8], false , [] , true);
        $item_types = FollowUpReminder::getItemTypes ( );
        foreach ( $actions as $type => $f ) {
            if (  !empty($item_types[$type]['name'] ))
                $actions[$item_types[$type]['name']] = $f;
            unset ( $actions [$type]);
        }

        $this->set('actions', $actions);
        $this->set('actionstext', $this->FollowUpAction->getList([1,2,3,8]));


        $conditions = $this->_filter_params();

        // check assign_staff_id
        if( isset($this->params['url']['assign_staff_id']) && !empty($this->params['url']['assign_staff_id']) )
        {
            $assign_staff_id = (int)$this->params['url']['assign_staff_id'];
            $assign_staff_id = mysql_escape_string( $assign_staff_id );
            $this->loadModel('Post');
            $conditions[] = '(FollowUpReminder.item_id IN (SELECT item_staffs.item_id FROM item_staffs WHERE item_staffs.item_type = ' . Post::CLIENT_TYPE . ' AND item_staffs.staff_id=' . $assign_staff_id . ' )) ';
        }

		if(isset($conditions['FollowUpReminder.item_staff_id'])){
			$conditions['FollowUpReminder.id'] = $this->ItemStaff->get_staff_items_list($conditions['FollowUpReminder.item_staff_id'], ItemStaff::APPOINTMENT_ITEM_TYPE);
//			$conditions['ItemStaff.id'] = $conditions['FollowUpReminder.item_staff_id'];
			
			unset($conditions['FollowUpReminder.item_staff_id']);
		}
        unset ( $conditions['FollowUpReminder.status_id'] ) ;
        unset ( $conditions['FollowUpReminder.item_id'] ) ;
        unset ( $conditions['FollowUpReminder.follow_up_status'] ) ;

        if($item_type)
		{
			$conditions['FollowUpReminder.item_type'] = $item_type;
		}
		
		if($partner_type){
			$conditions['FollowUpReminder.partner_type'] = $partner_type;
		}
        
			//when we filter with item_id
            // warning suppress
            $item_id = null;
            $partner_id = null;
            if (array_key_exists('item_id', $_GET))
                $item_id = $_GET['item_id'];
            if (array_key_exists('partner_id', $_GET))
                $partner_id = $_GET['partner_id'];
            // end warning suppress

            if ( $partner_id ) {
				$this->set ( 'partner_id' ,$partner_id);
				$conditions['FollowUpReminder.partner_id'] = $partner_id;
            }else if($item_id){
				$this->set ( 'item_id' ,$item_id);
                $conditions[] = "FollowUpReminder.item_id = {$item_id} OR FollowUpReminder.partner_id = {$item_id}";
            }
          
          
        
        if (!empty($_GET['follow_up_status']) ) {
//            $conditions[] = " ( Client.follow_up_status = ".intval($_GET['follow_up_status'])." OR Invoice.follow_up_status = ".intval($_GET['follow_up_status'])." ) ";
        }
        if (isset($_GET['action_id'])&&$_GET['action_id']>0 ) {
            $conditions['FollowUpReminder.action_id'] = intval($_GET['action_id']);
            //unset($conditions['FollowUpReminder.follow_up_status']);
        }
        if (isset($conditions['FollowUpReminder.category']) && $item_type == FollowUpReminder::CLIENT_TYPE) {
            $conditions['LOWER(Client.category)'] = strtolower($conditions['FollowUpReminder.category']);
            unset($conditions['FollowUpReminder.category']);
        }
        
        if (!empty($_GET['id'])) {
            $conditions['FollowUpReminder.id'] = intval($_GET['id']);
        }

        if (empty($_GET['status_id'])  ) {

            $_GET['status_id']=$this->params['url']['status_id']='0';
            $conditions['FollowUpReminder.status'] = ClientAppointment::Status_Scheduled;
        } else if ($_GET['status_id'] && $_GET['status_id'] > 0) {
            $conditions['FollowUpReminder.status'] = intval ($_GET['status_id'] );
        }
		$client_count = $this->FollowUpReminder->getTypeAppointmentsCount(FollowUpReminder::CLIENT_TYPE);
		
		$invoice_count = $this->FollowUpReminder->getTypeAppointmentsCount(FollowUpReminder::INVOICE_TYPE, null, null, null);
		$estimate_count = $this->FollowUpReminder->getTypeAppointmentsCount(FollowUpReminder::ESTIMATE_TYPE, null,null, null);
        if (ifPluginActive(WorkOrderPlugin)){
                $work_order_count = $this->FollowUpReminder->getTypeAppointmentsCount(FollowUpReminder::WORK_ORDER_TYPE, null,null, null);
        }else {
                $work_order_count = 0 ;
        }

        $workflowTabLabel = 'Workflows';
        if (ifPluginActive(WorkflowPlugin)) {
            $workflowCount = $this->FollowUpReminder->getTypeAppointmentsCount(FollowUpReminder::WORKFLOW_TYPE, null,null, null);

            if ($workflowCount > 0) {
                /** @var WorkflowTypeRepository $workflowTypesRepository */
                $workflowTypesRepository = izam_resolve(WorkflowTypeRepository::class);
                $activeCount = $workflowTypesRepository->getActiveCount();

                if (1 === $activeCount) {
                    $defaultWorkflow = $workflowTypesRepository->getActive();
                    if (count($defaultWorkflow) > 0) {
                        $workflowTabLabel = $defaultWorkflow[0]['name'];
                    }
                }
            }
        } else {
            $workflowCount = 0 ;
        }

        $this->set ( 'client_count'  , $client_count ) ;
        $this->set ( 'invoice_count'  , $invoice_count ) ;
        $this->set ( 'estimate_count'  , $estimate_count ) ;
        $this->set ( 'work_order_count'  , $work_order_count ) ;
        $this->set('workflow_count', $workflowCount);
        $this->set('workflow_tab_label', $workflowTabLabel);

        if (check_permission(View_All_Attachments_And_Notes_For_All_Clients) or check_permission (Edit_Delete_All_Notes_Attachments ) ) {
            
        } else if (check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) or check_permission (Edit_Delete_His_Own_Attachments_Notes_Only)  ) {
			//debug ( 'test' ) ; 
            $conditions[] = ' ( FollowUpReminder.staff_id ='.$staff_id.' OR '.$staff_id.' IN (SELECT staff_id from appointments_staffs WHERE appointments_staffs.follow_up_reminder_id = FollowUpReminder.id  )'
                    . 'OR FollowUpReminder.id IN (SELECT item_id from item_staffs WHERE (item_staffs.item_type = '.ItemStaff::APPOINTMENT_ITEM_TYPE.' OR item_staffs.item_type = '.ItemStaff::BOOKING_ITEM_TYPE.') AND staff_id = '.$staff_id.' ) OR FollowUpReminder.staff_id = ' . $staff_id . ' OR  ' . $staff_id . ' IN (SELECT staff_id FROM item_staffs WHERE item_staffs.item_type = ' . $item_type . ' AND item_staffs.item_id=FollowUpReminder.item_id) )';
   

			
		} else if (check_permission(View_His_Own_Notes_Attachments_Only)) {
            
            $conditions[] = '(FollowUpReminder.staff_id = '.$staff_id.' OR  '.$staff_id.' IN (SELECT staff_id from appointments_staffs WHERE appointments_staffs.follow_up_reminder_id = FollowUpReminder.id  ) OR FollowUpReminder.id IN (SELECT item_id from item_staffs WHERE item_staffs.item_type = '.ItemStaff::APPOINTMENT_ITEM_TYPE.' AND staff_id = '.$staff_id.' ))';
        }
        if ( ifPluginActive(WorkOrderPlugin) && !empty( $_GET['work_order_id'])){
            $conditions['FollowUpReminder.item_id'] = intval ( $_GET['work_order_id'] ) ;
            $conditions['FollowUpReminder.item_type'] = FollowUpReminder::WORK_ORDER_TYPE;
        }

        $FollowUpStatus = $this->FollowUpStatus->getLisWithColor(Post::INVOICE_TYPE);
        $FollowUpStatus = array_merge($FollowUpStatus,  $this->FollowUpStatus->getLisWithColor(Post::CLIENT_TYPE));


        $this->set('FollowUpStatuses', $FollowUpStatus);



        if (count($conditions) > 1) {
            $this->set('search_filter', "true");
        }
		if(IS_REST) $_GET['compact']= $this->params['url']['compact']="0";
        if ($response_type != 'json' &&  (!empty ($_GET['compact']) or !isset ( $_GET['compact']) or $_GET['compact'] == ""))
        {
            $_GET['compact']= $this->params['url']['compact']= 1;
//            $this->paginate['FollowUpReminder']['group'] = ["FollowUpReminder.item_id", "FollowUpReminder.item_type"];
              if ( ifPluginActive(WorkOrderPlugin)) {
            $more_f=",WorkOrder.* ";
              }else{
             $more_f=""     ;
              }

            $this->paginate['FollowUpReminder']['fields'] = "`FollowUpReminder`.*";
        }else {
            $this->paginate['FollowUpReminder']['limit'] = 300;
			$this->paginate['FollowUpReminder']['fields'] = "`FollowUpReminder`.*";
        }
        if ( !empty ( $_GET['exclude_app_id']))
        {
            $conditions['FollowUpReminder.id <>'] = intval (  $_GET['exclude_app_id']);
        }

        if ( !empty ( $_GET['follow_up_status']))
        {
            $conditions['COALESCE(Invoice.follow_up_status,Client.follow_up_status)'] = intval (  $_GET['follow_up_status']);
        }

        $conditions_key = 'appointments_' . md5(serialize($conditions));
        $this->Session->write($conditions_key, $conditions);
        $this->set('conditions_key', $conditions_key);
        $count = $this->FollowUpReminder->find('count', ["conditions" => $conditions]);

		$appointment_ids = array();
		$original_rows = [];
        $this->paginate['FollowUpReminder']['order'] =  'FollowUpReminder.date asc';
        $this->paginate['FollowUpReminder']['joins']= [
            [
                'table' => 'invoices',
                'alias' => 'Invoice',
                'type' => 'LEFT',
                'conditions' => array('FollowUpReminder.item_id = Invoice.id', 'FollowUpReminder.item_type' => 2 )
            ],
            [
                'table' => 'clients',
                'alias' => 'Client',
                'type' => 'LEFT',
                'conditions' => array('FollowUpReminder.item_id = Client.id', 'FollowUpReminder.item_type' => 1 )
            ]
        ];
		$rows = $this->paginate('FollowUpReminder', $conditions );
        $this->setup_nav_data($rows, ['model_name' => 'FollowUpReminder']);

        if ( $response_type == 'json' ) {
            $app = []  ; 
            foreach ($rows as $key => $row) {
                $app[$key]['id'] = $row['FollowUpReminder']['id'];
                $app[$key]['start'] = date('Y-m-d H:i',strtotime($row['FollowUpReminder']['date']));
                $app[$key]['end'] = date('Y-m-d H:i',strtotime($row['FollowUpReminder']['end_date']));
//                $app[$key]['title'] = $row['FollowUpReminder']['duration'];
                if(!isset($row['Partner']) && isset($row['FollowUpReminder']['partner_id'])) {
                    $partnerData = $this->FollowUpReminder->getAppointmentPartner($row['FollowUpReminder']['partner_type'], $row['FollowUpReminder']['partner_id']);
                    $partnerData = $this->FollowUpReminder->setPartnerRaltedData($partnerData, $row['FollowUpReminder']['partner_type']);
                    $row['Partner'] = $partnerData;
                }
                $app[$key]['title'] = $row['Partner']['name'];
                if($row['FollowUpReminder']['item_type'] == FollowUpReminder::WORK_ORDER_TYPE) {
                    $workOrder = $this->FollowUpReminder->getAppointmentRelatedItem($row['FollowUpReminder']['item_type'], $row['FollowUpReminder']['item_id']);
                    if($app[$key]['title']) {
                        $app[$key]['title'] .= ' - '.$workOrder['WorkOrder']['title'];
                    } else {
                        $app[$key]['title'] = $workOrder['WorkOrder']['title'];
                    }
                }
                $app[$key]['color'] = $FollowUpStatus[$row['Client']['follow_up_status']];
            }
            echo json_encode($app);
            $this->autoRender = false;
            die ; 
        }
        $frist_date = $this->FollowUpReminder->find('first', array('order' => 'FollowUpReminder.date asc', 'conditions' => $conditions));
        $frist_date['ClientAppointment'] = $frist_date['FollowUpReminder'];

        $this->set('frist_date', $frist_date);

        $filters = $this->FollowUpReminder->getFilters();
        $this->set('filters', $filters);

        $is_ajax = $this->RequestHandler->isAjax();
        $this->set('is_ajax', $is_ajax);


        $client_statuses = $this->FollowUpStatus->getList([1,2,3], true);
        $this->set('client_statuses', $client_statuses);

        $client_status_colors = $this->FollowUpStatus->getList([1,2,3], true, array(), 'color');
        $this->set('client_status_colors', $client_status_colors);

        
	$this->set('item_type',$item_type);
	$this->set('partner_type',$partner_type);

	$this->set('site',$site);
	$staffs = UsersHelper::getInstance()->getList(); 
		$relatedModels = [];
//		$start = microtime(true);
        $this->ItemStaff->recursive = -1;
       
        if ($shareWithSocialMedia = Settings::getValue(ClientsPlugin, 'client_integrate_social_media')) {
            $this->set('shareWithSocialMedia', $shareWithSocialMedia);
            $messageTemplates = Settings::getValue(FollowupPlugin, 'social_media_share_message_templates', null, false);
        }
        foreach ($rows as $k => $v ){
            $typeItemModel = FollowUpReminder::$types_data[$v['FollowUpReminder']['item_type']]['item_model'];
            if(!$typeItemModel) {
                //unset temp bookings;
                unset($rows[$k]);
                continue;
            }
            $assignedStaffs = $this->ItemStaff->getAssignedStaff(ItemStaff::APPOINTMENT_ITEM_TYPE, $v['FollowUpReminder']['id']);
            foreach($assignedStaffs as $assignedStaff){
                if(isset($assignedStaff['ItemStaff']['staff_id'])){
                    $v['FollowUpReminder']['AssignedStaff'][] = $assignedStaff['ItemStaff']['staff_id'];
                }
            }
            $new_rest_row = $v;
            $new_rest_row["{$typeItemModel}Appointment"] = $new_rest_row['FollowUpReminder'];
            unset($new_rest_row['FollowUpReminder']);
            $original_rows[] = $new_rest_row;
            $relatedModel = isset($relatedModels[$typeItemModel]) ? $relatedModels[$typeItemModel] : $relatedModels[$typeItemModel] = GetObjectOrLoadModel($typeItemModel);
			$relatedModel->recursive = -1;
            // warning suppress
			$rows[$k][$typeItemModel] = $relatedModel->find('first',['applyBranchFind' => false,'conditions' =>['id'=>$v['FollowUpReminder']['item_id']]])[$typeItemModel] ?? null;
			$appItemStaff = $this->ItemStaff->find('all', ['conditions' => ['ItemStaff.item_id' => $v['FollowUpReminder']['id'], 'ItemStaff.item_type' => ItemStaff::APPOINTMENT_ITEM_TYPE]]);
            if(empty($rows[$k][$typeItemModel]))
			{
				unset($rows[$k]);
				continue;
			}
            
            if ($typeItemModel=='Client') {
                if ($shareWithSocialMedia && $rows[$k]['Client']['phone2']) {
                    $rows[$k]['FollowUpReminder']['socialLinks'] = $this->getShareWithSocialMediaData($rows[$k], FollowupPlugin,$messageTemplates);
                }
            }
          

			$apptStaff = [];
            foreach($appItemStaff as $staffIndex => $itemStaff){
				$apptStaff[] =(isset($staffs[$itemStaff['ItemStaff']['staff_id']])?$staffs[$itemStaff['ItemStaff']['staff_id']]:null);

			}
			$rows[$k]['staffs'] = implode(', ',array_filter($apptStaff));

            // get client staff names
            if( getCurrentSite( 'id' ) == '2034448' )
            {
                $assign_staff_ids=$this->ItemStaff->find('list', array('fields'=>'id,staff_id','conditions' => array('ItemStaff.item_type' => ItemStaff::CLIENT_ITEM_TYPE, 'ItemStaff.item_id' => $rows[$k]['Client']['id'])));
                foreach($assign_staff_ids as $staff_id){
                    $rows[$k]['staff_assignd'][$staff_id]=$staffs[$staff_id];
                }
            }

        }

        $this->set('count', $count);

        $this->set('client_appointments', $rows);
		 
		App::import('Vendor', 'notification_2');
		

		if(count($appointment_ids));
		{
			NotificationV2::view_notificationbyref(array_values($appointment_ids), NotificationV2::NOTI_UPDATE_APPOINTMENT);
		}

		$this->loadModel('ItemsTag');
		$this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_APPOINTMENT);
		$tags = $this->ItemsTag->get_items_tags($appointment_ids,ItemsTag::TAG_ITEM_TYPE_APPOINTMENT);
    
		$this->set('tags',$tags);

       $this->printableTemplatesList();
 
        $this->set('client_appointments', $rows);
		if(IS_REST){
			$this->set('rest_items', $original_rows);
			$this->set('rest_model_name', "{$typeItemModel}Appointment");
			$this->render("index");
		} else {
			$this->render ('/client_appointments/owner_index');
		}
    }

    function printableTemplatesList()  {
        $this->loadModel('PrintableTemplate');
                
        $defaultTemplate = $this->PrintableTemplate->getDefaultTemplateForType('appointments');
        if(!is_null($defaultTemplate)){
            $defaultTemplate = array_shift($defaultTemplate);
        }

        $this->set('defaultTemplate', $defaultTemplate);
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('appointments');

        $this->set('has_templates', false);
        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('follow_up_reminder');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);
      
    }
	
	function client_index()
	{
		App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);

		$client_settings = getClientSettings();
		
		if(!isset($client_settings['client_permission_view_appointment']) || $client_settings['client_permission_view_appointment'] == false){
			$this->flashMessage(__("You are not allowed to view this page", true));
			$this->redirect('/');
		}
		$this->loadModel('FollowUpReminder');
		$client = getAuthClient();
		$appointments = $this->FollowUpReminder->getPartnerClientAppointments($client['id'], true);
        $relatedModels = [];
		 foreach ($appointments as $k => $v ){
			$typeItemModel = FollowUpReminder::$types_data[$v['FollowUpReminder']['item_type']]['item_model'];
            $relatedModel = isset($relatedModels[$typeItemModel]) ? $relatedModels[$typeItemModel] : $relatedModels[$typeItemModel] = GetObjectOrLoadModel($typeItemModel);
			$relatedModel->recursive = -1;
			$appointments[$k][$typeItemModel] = $relatedModel->findById($v['FollowUpReminder']['item_id'])[$typeItemModel];
			if(empty($appointments[$k][$typeItemModel]))
			{
				unset($appointments[$k]);
				continue;
			}
        }


		$this->loadModel('Staff');
		$staffs = $this->Staff->find('list');
		$this->loadModel('Invoice');
		$this->set('staffs',$staffs);
		$this->set('appointments',$appointments);
	}
	
	function owner_next($id = null)
	{
		
		$nav_key = 'ClientAppointments.nav';
		$nav = $this->Session->read($nav_key);
		if($nav && count($nav) > 1){
			$item_nav_index = array_search($id,$nav);
			if($item_nav_index !== false)
			{
				if($item_nav_index >= 19)
				{
					$url = $nav['url'];
					
					if($nav['paging'][$nav['model_name']]['page'] < $nav['paging']['model_name']['pageCount'])
					{
						if($nav['paging'][$nav['model_name']]['page'] == 1)
						{
							
							$pos = strrpos( $url,'/');
							$new_url = substr_replace($url ,'/index/page:2/',$pos);
						}else{
						
						$new_url = str_replace('page:'.$nav['paging'][$nav['model_name']]['page'], 'page:'.($nav['paging'][$nav['model_name']]['page']+1), $nav['url']);
						}					
						
						
					}else{
						$this->flashMessage(__("{$nav['model_name']} not found", true));
						$this->redirect(Router::url(['controller' => $nav['view_controller'],'action' => 'index']));
						
					}
					
					$nav['next_page'] = true ;
					$this->Session->write($nav_key,$nav);
					$this->redirect(Router::url($new_url));
				}else{
					debug($item_nav_index);
						$item_nav_next_index = $item_nav_index + 1;
					$new_url = $this->get_link($nav[$item_nav_next_index]).'?nav_source=appointment&appointment_id='.$nav[$item_nav_next_index].'#AppointmentsBlock';
					$this->redirect(Router::url($new_url));
				}
			
			}
		}
		$this->flashMessage(__("{$nav['model_name']} not found", true));
		$this->redirect(Router::url(['controller' => $nav['view_controller'],'action' => 'index']));
		
	}
	
	function owner_previous($id = null)
	{
		$nav_key = $_GET['nav_key'] ? $_GET['nav_key']  : $this->name.'.nav';
		$nav = $this->Session->read($nav_key);
		
		if($nav && count($nav) > 1){	
			$item_nav_index = array_search($id,$nav);
			if($item_nav_index !== false)
			{
				
				if($item_nav_index <= 0)
				{
					$url = $nav['url'];
					
					if($nav['paging'][$nav['model_name']]['page'] > 1)
					{
						$new_url = str_replace('page:'.$nav['paging'][$nav['model_name']]['page'], 'page:'.($nav['paging'][$nav['model_name']]['page']-1), $nav['url']);
					}else{
							$this->flashMessage(__("{$nav['model_name']} not found", true));
						$this->redirect(Router::url(['controller' => $nav['view_controller'],'action' => 'index']));
						
						
					}
					
					$nav['prev_page'] = true ;
					$this->Session->write($nav_key,$nav);
					$this->redirect(Router::url($new_url));
				}else{
					$item_nav_prev_index = $item_nav_index - 1;
					
						$this->redirect(Router::url($this->get_link($nav[$item_nav_prev_index]).'?nav_source=appointment&appointment_id='.$nav[$item_nav_prev_index].'#AppointmentsBlock'));
				}
			
			}
		}
			$this->flashMessage(__("$modelName not found", true));
						$this->redirect(Router::url(['controller' => $nav['view_controller'], 'action' => 'index']));
						
		
		
	}
	
	public function api_view($id, $type) {
        $this->loadModel('FollowUpReminder');
		$modules = [1=>"Client appointment", 2=>"Invoice appointment", 3=>"Estimate appointment", 8=>"Work order appointment"];
		$module = $modules[$type];
		$appointment = $this->FollowUpReminder->find('first', ["conditions"=>['FollowUpReminder.id'=>$id, 'FollowUpReminder.item_type' => $type]] );
		if(empty($appointment)) $this->cakeError('error404', array('message' => __('Appointment not found', true)));
        $this->loadModel('ItemStaff');
		if ($owner['staff_id'] != 0) {
            $staff = getAuthStaff('id');
			$assigned = $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => FollowUpReminder::APPOINTMENT_TYPE, 'ItemStaff.item_id' => $id, 'ItemStaff.staff_id' => $staff)))!=0;
            if (
					!check_permission(View_All_Attachments_And_Notes_For_All_Clients) &&
					!(check_permission(View_His_Own_Notes_Attachments_Only) && $appointment['FollowUpReminder']['staff_id'] == $staff) &&
					!(check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) && $assigned)
				) {
				$this->cakeError('error403');
            }
        }
        $assignedStaffs = $this->ItemStaff->getAssignedStaff(ItemStaff::APPOINTMENT_ITEM_TYPE, $id);
        foreach($assignedStaffs as $assignedStaff){
            $appointment['FollowUpReminder']['AssignedStaff']['staff_id'][] = $assignedStaff['ItemStaff']['staff_id'];
        }
        $this->set('rest_item', $appointment);
		$this->set('rest_model_name', "FollowUpReminder");
		$this->render("view");
	}
	
    function owner_export($conditions_key, $report_template_id = false, $quick_report = false) {
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'Post');
	$this->set('conditions_key',$conditions_key);

	 set_time_limit ( 30*60);
	 ini_set('memory_limit','2G');

		$order_list=array(
			'date_desc'=>array('title'=>__('Date',true).' ('.__('Recent First',true).')','fields'=>'FollowUpReminder.date DESC, FollowUpReminder.id DESC'),
			'date_asc'=>array('title'=>__('Date',true).' ('.__('Oldest First',true).')','fields'=>'FollowUpReminder.date , FollowUpReminder.id '),
		);
		
		//echo getPaymentStatuses ( 1 );
		$fields_list=array(
			'client_full_address'=> array ('title' =>'Client Full Address', 'field' =>  ('item_id')),//Address 1\n Address 2 \City, State  Postal Code\nCountry Code
			'client_name'=> array ('title' =>'Client Name', 'field' =>  ('item_id')),//Address 1\n Address 2 \City, State  Postal Code\nCountry Code
			'id'=> array ('title' =>'ID', 'field' =>  ('id')),
			'type'=> array ('title' =>'Type', 'field' =>  ('type')),
			'item_number'=> array ('title' =>'Number', 'field' =>  ('item_id')),
			'client_phone'=> array ('title' =>'Client Phone', 'field' =>  ('item_id')),
                        'item_action'=> array ('title' =>'Action', 'field' =>  ('FollowUpReminder.action_id')),
                        'item_follow_up_status'=> array ('title' =>'Status', 'field' =>  ('follow_up_status')),
                        'item_date'=> array ('title' =>'Date', 'field' =>  ('FollowUpReminder.date')),
                        'item_notes'=> array ('title' =>'Notes', 'field' =>  ('FollowUpReminder.body')),
		);
		
		$types = [
                  FollowUpReminder::CLIENT_TYPE => __("Client" , true ) ,  
                  FollowUpReminder::INVOICE_TYPE   => __("Invoice" , true ) , 
                  FollowUpReminder::ESTIMATE_TYPE    => __("Estimate" , true ) ,
                ];
		
		$conditions=$this->Session->read($conditions_key);
		
		if(empty($conditions))
		{
			$this->flashMessage(__("Invalid list to export", true), 'Errormessage');
			$this->redirect(array('action' => 'index'));
		}
		
		$bind_model_arr = [
                    'belongsTo' => array(
                        'Client' => array('className' =>'Client', 'foreignKey' => 'item_id' , 'conditions' => ['item_type' => FollowUpReminder::CLIENT_TYPE]) ,
                        'Invoice' => array('className' =>'Invoice', 'foreignKey' => 'item_id', 'conditions' => ['item_type' => [FollowUpReminder::ESTIMATE_TYPE,FollowUpReminder::INVOICE_TYPE]  ]),
                    )];
                if (ifPluginActive(WorkOrderPlugin)){
                    $bind_model_arr['belongsTo']['WorkOrder'] = array('className' =>'WorkOrder', 'foreignKey' => 'item_id', 'conditions' => ['item_type' => FollowUpReminder::WORK_ORDER_TYPE  ]);
                }
                $this->FollowUpReminder->bindModel($bind_model_arr ,false);
		if(!empty($this->data) )
		{
			$this->layout = '' ;
			$params['conditions']=$conditions;
                        debug ( $conditions ) ;
			$params['order'] = 'FollowUpReminder.date DESC, FollowUpReminder.id DESC';
			if(!empty($this->data['order'])&&in_array($this->data['order'],array_keys($order_list)))
			{
				$params['order']=$order_list[$this->data['order']]['fields'];
			}
			$params ['fields'] = [] ;
            $params ['group'] = ['FollowUpReminder.id'];

			//$params['limit'] = 10 ; 
			$alldata = $this->FollowUpReminder->find('all' , $params ) ;
			
			//If they didn't select anything .
			if (empty ($this->data['fields_select']) ){
				$this->data['fields_select'] = array_keys ($fields_list);
			}
			
			$rows = [] ;
			foreach ( $this->data['fields_select'] as $v ){
                                debug ( $v ) ; 
				$rows[1][$fields_list[$v]['title']] = __($fields_list[$v]['title'] , true  );
			}
                        debug ( $rows );
                        debug ( $this->data['fields_select'] ) ; 
			
			//Putting data for CSV file
			$i = 2;
                        $this->loadModel ( 'FollowUpStatus');
                        $this->loadModel ( 'FollowUpAction');
                        $this->loadModel ( 'Client');
                        $this->loadModel ( 'Invoice');
                        $this->Invoice->recursive = -1 ; 
                        $this->Client->recursive = -1 ; 
                        $FollowUpStatuses = $this->FollowUpStatus->getList([1,2,3]);
                        $FollowUpActions = $this->FollowUpAction->getList([1,2,3]);
			foreach ( $alldata as $d ) {
                            
                            $client = [] ; 
                            $invoice = [] ; 
                            // Gettings invoice , client for further use 
                            if ( $d['FollowUpReminder']['item_type'] == FollowUpReminder::CLIENT_TYPE ) {
                                $client = $this->Client->find ( 'first' , [ 'conditions' => ['Client.id' =>$d['FollowUpReminder']['item_id'] ]]);
                            }else {
                                $invoice = $this->Invoice->find ( 'first' , [  'conditions' => ['Invoice.id' =>$d['FollowUpReminder']['item_id'] ]]);
                                $client = $this->Client->find ( 'first' , [  'conditions' => ['Client.id' =>$invoice ['Invoice']['client_id'] ]]);
                            }


                            foreach ($this->data['fields_select'] as $k => $v ){
                                    $field = explode ('.',  $fields_list[$v]['field'] );
                                    if ( $v=='type' ) {
                                        $rows[$i][$fields_list[$v]['title']] = $types[$d['FollowUpReminder']['item_type']] ; 
                                    }else if ( $v=='id' ) {
                                        $rows[$i][$fields_list[$v]['title']] = $d['FollowUpReminder']['id'] ; 
                                    }else if ( $v == 'client_full_address' ) {
                                        $view = new View (  ) ;
                                        $ee  =strip_tags($view->element ('format_address_html' , $client['Client'] ) ) ; 
                                        $rows[$i][$fields_list[$v]['title']] = $ee  ;
                                    }else if ( $v == 'client_phone' ) {
                                        $rows[$i][$fields_list[$v]['title']] = empty($client['Client']['phone1']) ?$client['Client']['phone2']: $client['Client']['phone1'] ;
                                    }else if ( $v == 'client_name' ) {
                                        $rows[$i][$fields_list[$v]['title']] = $client['Client']['business_name'] .' '. $client['Client']['first_name'].' '.$client['Client']['last_name'] ;
                                    }else if ( $v == 'item_number' ) {
                                        if ( $d['FollowUpReminder']['item_type'] == FollowUpReminder::CLIENT_TYPE ) {
                                            $rows[$i][$fields_list[$v]['title']] = $client['Client']['client_number'];
                                        }else {
                                            $rows[$i][$fields_list[$v]['title']] = $invoice['Invoice']['no'];
                                        }
                                    }else if ( $v == 'item_follow_up_status' ) {
                                        if ( $d['FollowUpReminder']['item_type'] == FollowUpReminder::CLIENT_TYPE ) {
                                            $rows[$i][$fields_list[$v]['title']] = $FollowUpStatuses[$client ['Client']['follow_up_status']];
                                        }else {
                                            $rows[$i][$fields_list[$v]['title']] = $FollowUpStatuses[$invoice ['Invoice']['follow_up_status']];
                                        }
                                    }else if ( $v == 'item_action' ) {
                                        $rows[$i][$fields_list[$v]['title']] = $FollowUpActions[$d['FollowUpReminder']['action_id']];
                                    }else if ( $v == 'item_notes' ) {
                                        $rows[$i][$fields_list[$v]['title']] = trim(html_entity_decode(strip_tags ($d['FollowUpReminder']['body'] ))) ;
                                    }else {
                                            $rows[$i][$fields_list[$v]['title']] = $d[$field[0]][$field[1]];
                                    }
                            }
                            $i ++;
			}
			debug ( $rows ) ;
			App::import('Vendor', 'csv');
			if(  $this->data['export_to'] == 'xml' ){
				$exporter = new ExportDataExcel('browser', 'data.xml');
			}else if(  $this->data['export_to'] == 'csv_semicolon' ){
				$exporter = new ExportDataCSV('browser', 'data.csv' , ";" );
			}else if(  $this->data['export_to']== 'excel' ){
				$exporter = new ExportDataExcel('browser', 'data.xls');
			}else {
				$exporter = new ExportDataCSV('browser', 'data.csv');
				/*header('Content-Disposition: attachment; filename=data.csv');
				$data = "";
				foreach ( $rows as $r ){
					foreach ( $r as $k=>$s ){
						$data .= $s .",";
					}
					$data .= "\n";
				}
				$utf8_with_bom = chr(239) . chr(187) . chr(191) . $data;
				echo $utf8_with_bom; exit () ;*/
			} 
			$exporter->initialize();
			
			foreach ( $rows as $r ){
				$exporter->addRow($r );
			}
			
			$exporter->finalize();die ; 
			 
		}
		
		$this->set ( 'order_list' , $order_list );
		$this->set ( 'fields_list' , $fields_list );
                echo $this->render ('/client_appointments/owner_export');die ; 
		
			
	
    }
    function owner_get_client_ajax () {
            $client_id = intval($_POST['client_id'] );
            $this->loadModel ('Client' );
            $this->Client->recursive = -1 ; 
            die (json_encode ($this->Client->findById($client_id ) ));
    }
    function beforeFilter(){
        if(!empty($_POST['conditions_link']))
            $_POST['conditions_link'] = "client_".$_POST['conditions_link'];
        parent::beforeFilter();
    }
    function owner_delete($id = null) {
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'ClientAppointment');
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel('Post');
        $this->loadModel('Invoice');
        $this->loadModel('Client');
        $this->loadModel('ItemStaff');
        if ( !empty ($_POST['ids'] )){
                $ids = $_POST['ids'];
                $id = $_POST['ids'][0];
        }else {
                $ids = [$id];
        }
		
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $staff_id = getAuthOwner('staff_id');
        $row = $this->FollowUpReminder->read(null, $id);

        $item_type = $row['FollowUpReminder']['item_type'];
        if (empty ($row)) {
			if(IS_REST){
				$this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Appointment', true))]);
			}else{
				$this->flashMessage(__('Appointment not found', TRUE));
				$this->redirect(array('action' => 'index'), $item_type);
			}
        }

        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'), $item_type);
        }

        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $item = $row['FollowUpReminder']['item_id'];
            $client_conditions = array('ItemStaff.item_type' => $item_type, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'ItemStaff.id,ItemStaff.item_id', 'conditions' => $client_conditions)));
            $follow_up_conditions = array('FollowUpReminder.item_type' => $item_type, 'FollowUpReminder.staff_id' => $staff_id);
            $follow_ups_list = array_values($this->FollowUpReminder->find('list', array('fields' => 'id,item_id', 'conditions' => $follow_up_conditions)));
            if (!in_array($id, $client_list) && !in_array($item, $follow_ups_list)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index'), $item_type);
            }
        }
		if(IS_REST){
			$_POST['submit_btn'] = true;
			$_POST['ids'] = [$id];
		}
        if (!empty($this->data) || !empty ($ids) ) {
            if ($_POST['submit_btn'] == 'yes' && !empty ($ids) ) {
				
                //$this->loadModel('Client');
                $this->loadModel('FollowUpAction');
                $statuses = $this->ClientAppointment->getStatuses();
                $FollowUpActions = $this->FollowUpAction->getList(FollowUpReminder::CLIENT_TYPE);
                App::import('Vendor', 'notification_2');
                foreach ( $ids as $i ){
                        $row = $this->FollowUpReminder->read(null, $i);
                        $relatedItem = $this->FollowUpReminder->getAppointmentRelatedItem($item_type, $row['FollowUpReminder']['item_id']);
                        $this->FollowUpReminder->del($i);
                        $rows_count=$this->FollowUpReminder->find('count',array('conditions'=>array('FollowUpReminder.recurring_appointment_id'=>$row['FollowUpReminder']['recurring_appointment_id'])));
                        if($rows_count==0){
                        $this->FollowUpReminder->RecurringAppointment->delete($row['FollowUpReminder']['recurring_appointment_id']);
                        }
                        
                        
                        $item_type=$row['FollowUpReminder']['item_type'];
                        $row['ClientAppointment'] = $row['FollowUpReminder'];
                        if ( $item_type == FollowUpReminder::CLIENT_TYPE ) {
                            $client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
                            $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                                ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                                : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                            $this->add_actionline(ACTION_DELETE_CLIENT_APPOINTMENT, array(
                                'primary_id' => $i, 'secondary_id' => $row['ClientAppointment']['item_id'],
                                'param1' => $client['Client']['business_name'],
                                'param2' => $client['Client']['client_number'],
                                'param3' => $row['ClientAppointment']['date'],
                                'param4' => $row['ClientAppointment']['date'],
                                'param5' => $param5,
                                'param6' => $statuses[$row['ClientAppointment']['status']],
                                'param7' => $row['ClientAppointment']['status_date']
                            ));
                        }else if($item_type == FollowUpReminder::WORK_ORDER_TYPE)
                        {
                            $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                                ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                                : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                            $this->add_actionline(ACTION_DELETE_WORK_ORDER_APPOINTMENT, array(
                                'primary_id' => $i, 'secondary_id' => $row['ClientAppointment']['item_id'],
                                'param1' => $relatedItem['WorkOrder']['title'],
                                'param2' => $relatedItem['WorkOrder']['number'],
                                'param3' => $row['ClientAppointment']['date'],
                                'param4' => $row['ClientAppointment']['date'],
                                'param5' => $param5,
                                'param6' => $statuses[$row['ClientAppointment']['status']],
                                'param7' => $row['ClientAppointment']['status_date']
                            ));
                        }else {
                            $action = ACTION_DELETE_INVOICE_APPOINTMENT ; 
                            if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
                                $action = ACTION_DELETE_ESTIMATE_APPOINTMENT ; 
                            }
                            $client = $this->Invoice->read(null, $row['ClientAppointment']['item_id']);
                            $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                                ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                                : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                            $this->add_actionline($action, array('primary_id' =>  $row['ClientAppointment']['item_id'] , 'secondary_id' =>$i, 'param1' => $client['Invoice']['no'], 'param3' => $row['ClientAppointment']['date'], 'param4' => $row['ClientAppointment']['date'], 'param5' => $param5, 'param6' => $statuses[$row['ClientAppointment']['status']], 'param7' => $row['ClientAppointment']['status_date']));
                        }
				NotificationV2::delete_notificationbyref($i,NotificationV2::NOTI_UPDATE_APPOINTMENT);

                }
				if(IS_REST){
					$this->set("message", sprintf(__('%s has been deleted', true), ucfirst($module_name)));
					$this->render("success");
					return;
				}

                $this->flashMessage(__('The appointment has been deleted', true), 'Sucmessage');
                if ( !empty ( $_POST['redirect_url']) ){
                    $this->redirect ( $_POST['redirect_url'] .'#AppointmentsBlock') ;die ; 
                }
                if (isset($url_params['back_to_client'])) {
                    $this->redirect ( FollowUpStatus::$types_diff[$item_type]['view_url'].$row['FollowUpReminder']['item_id'].'#AppointmentsBlock');
//                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['ClientAppointment']['item_id'], '#' => 'AppointmentsBlock'));
                }
                $this->redirect(array('action' => 'index' , $item_type));
            }
            else if($_POST['submit_btn'] == 'no' && !empty($ids)){
                $this->redirect(array('action' => 'index' , $item_type));
            }
			
        }
		if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
        $this->set('id', $id);
        $this->render ('/client_appointments/owner_delete');
    }



    function owner_calendar_json($item_type = 1) {
        $this->loadModel('Post');
        $this->loadModel('FollowUpAction');
        $this->loadModel('ClientAppointment');
        $this->loadModel('FollowUpReminder');
        $follow_up_actions = $this->FollowUpAction->getList($item_type);
        $this->loadModel('FollowUpStatus');
        $follow_up_statuses = $this->FollowUpStatus->getList($item_type, false, array(), 'color');
        $url_params = $this->params['url'];

        
        $class_name = FollowUpStatus::$types_diff[$item_type]['class'];
        $this->FollowUpReminder->bindModel( ['belongsTo' => array($class_name => array('className' =>$class_name, 'foreignKey' => 'item_id'))],false);
        $conditions = $this->_filter_params();
        unset ( $conditions['FollowUpReminder.status_id'] ) ;
        unset ( $conditions['FollowUpReminder.item_id'] ) ;
        unset ( $conditions['FollowUpReminder.follow_up_status'] ) ;
//        $conditions =[];
        $conditions['FollowUpReminder.item_type'] = $item_type;
        if ( isset ( $_GET['item_id'] ) &&$_GET['item_id'] > 0 )
        {
          $item_id = intval ($_GET['item_id']);
          $conditions['FollowUpReminder.item_id'] =   $item_id;
          
          $this->set ( 'item_id' ,$item_id);
        }
        if ( isset ( $_GET['date_from'] ) &&$_GET['date_from'] != "" )
        {
          $conditions['FollowUpReminder.date >='] =   $_GET['date_from'];
        }
        if ( isset ( $_GET['date_to'] ) &&$_GET['date_to'] != "" )
        {
          $conditions['FollowUpReminder.date <='] =   $_GET['date_to'];
        }
        $conditions['status'] = ClientAppointment::Status_Scheduled;
        if ( !is_array($item_type)){
            $conditions[] = $class_name.'.id is not null';
        }
        
        
        $rows = $this->FollowUpReminder->find('all', array('conditions' => $conditions));
        debug ( $conditions ) ;debug ( $rows );
        //echo "<pre>";
        //print_r($rows);
        foreach ($rows as $key => $row) {
            $app[$key]['id'] = $row['FollowUpReminder']['id'];
            $app[$key]['date'] = $row['FollowUpReminder']['date'];
            $app[$key]['title'] = $row['Client']['business_name'] . ' - ' . $follow_up_actions[$row['FollowUpReminder']['action_id']];

            $app[$key]['color'] = $follow_up_statuses[$row['Client']['follow_up_status']];
        }
        echo json_encode($app);
        $this->autoRender = false;
        //die();
    }


    function _submitCommon($data, $itemType, $item)
    {

        $data['ClientAppointment']['date'] = $this->ClientAppointment->formatDateTime($data['ClientAppointment']['date'] );
        $data['ClientAppointment']['end_date'] = $this->ClientAppointment->formatDateTime($data['ClientAppointment']['end_date'] );

        $data = $this->FollowUpReminder->set_type_related_data($itemType, $data, $item);
        if ( !$data['ClientAppointment']['choose_staff']){
            unset ( $data['ClientAppointment']['staff_id']);
        }
        return $data;
    }

    function owner_add($id = null , $item_type = 1) {


        $owner = getAuthOwner();
        $formats = getDateFormats('std');
        $dateFormat = $formats[$owner['date_format']];
        if ((!check_permission(Add_Notes_Attachments_For_All_Clients) && !check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only))) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'), $item_type);
        }
        $this->loadModel('Post');
        $this->loadModel('ItemStaff');

        if (ifPluginActive(WorkOrderPlugin) || ifPluginActive(WorkflowPlugin)) {
            $this->loadModel('WorkOrder');
        }
        $this->loadModel('Client');
        $this->loadModel('Invoice');
        $this->loadModel('FollowUpStatus');
		$modelName = 'FollowUpReminder';
        $this->loadModel('ClientAppointment');
        $this->loadModel('FollowUpReminder');
        $this->loadModel('ItemStaff');
        $this->loadModel('Staff');
        if(in_array($item_type, [ FollowUpReminder::CLIENT_TYPE, FollowUpReminder::INVOICE_TYPE])  && empty($id) && !empty($this->data['ClientAppointment']['item_id']))
        {
            $id = $this->data['ClientAppointment']['item_id'];
        }

        if (!empty($id) || $item_type !=  FollowUpReminder::CLIENT_TYPE )
        {
            if(!isset(FollowUpStatus::$types_diff[$item_type])) {
                $message = sprintf(__('Invalid followup status', true));
                if(IS_REST) $this->cakeError('error404', ["message" => $message]);
                $this->flashMessage($message);
                return;
            }

            $item = $this->FollowUpStatus->getItemRecord($item_type, $id);
            if ($item_type ==  FollowUp::CLIENT_TYPE && isset($item['Client']['suspend']) && $item['Client']['suspend'] == 1) {
                $message = __("You cannot create a new transaction for a suspended client", true);
                if (IS_REST) {
                    $this->cakeError('error404', ["message" => $message]);
                    die();
                }
                $this->flashMessage($message);
                $this->redirect($this->referer());
            }
            if ( empty ( $item))
            {

                $url=FollowUpStatus::$types_diff[$item_type]['url'];
                if($url) {

                    $message = sprintf(__('%s not found', true), FollowUpStatus::$types_diff[$item_type]['name']);
                }else{
                    $message = __('You have followed invalid url',true);
                }
                if(IS_REST) $this->cakeError('error404', ["message" => $message]);
                $this->flashMessage($message);
                $this->redirect( empty($url)?'/':$url );
            }

        }
        $this->__set_data($item_type, $item);


        if (!empty($this->data)) {

            $duration = $this->data['ClientAppointment']['duration'];
            if(!is_numeric($duration) && strpos($duration, ':') !== false ){
                $time = explode(':',$duration);
                if((empty($time[0]) && $time[0] == "") || (empty($time[1]) && $time[1] == "")){
                    $this->flashMessage(__("Please enter a valid duration", true));
                    return;
                }
            }

			$beforesave=$this->data;
			//set appointment item id in case the id is empty
            $this->data['ClientAppointment']['item_id'] = empty($id) ? $this->data['ClientAppointment']['item_id'] : $id;
            $id = $this->data['ClientAppointment']['item_id'];

            //getting the redirect url
            $back = $this->FollowUpStatus->getTypeUrl($item_type, $id);



            if (!check_permission(Add_Notes_Attachments_For_All_Clients) && !check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only)) {
                $staff_id = getAuthOwner('staff_id');
                $count = $this->ItemStaff->find('count', array('conditions' => array('ItemStaff.item_type' => $item_type, 'ItemStaff.item_id' => $this->data['ClientAppointment']['item_id'], 'ItemStaff.staff_id' => $staff_id)));

                if ($count == 0) {
                    $this->flashMessage(__('You are not allowed to edit this client', true));
                    $this->redirect(array('controller' => 'clients', 'action' => 'index'));
                }
            }
            if(IS_REST){
                foreach($this->data['ClientAppointment']['staff_id'] as $staff_id){
                    if(!in_array($staff_id, array_keys(UsersHelper::getInstance()->getList()))){
                        $this->cakeError('error400', ["message" => 'Invalid Staff']);
                    }
                }
            }

            //init data
            $this->data['ClientAppointment']['item_type'] = $item_type;
            $this->data['ClientAppointment']['status'] = ClientAppointment::Status_Scheduled;
            $this->data['ClientAppointment']['status_date'] = null;
     
            if((isset($this->data['ClientAppointment']['date']) && !is_string($this->data['ClientAppointment']['date'])) 
            || (isset($this->data['ClientAppointment']['end_date']) && !is_string($this->data['ClientAppointment']['end_date']))) {
                $message = __('Invalid date type format',true);
                if(IS_REST) $this->cakeError('error404', ["message" => $message]);
                $this->flashMessage(__("Please enter a valid date", true));
            }

            $this->data = $this->_submitCommon($this->data, $item_type, $item);

            if(mb_detect_encoding($this->data['ClientAppointment']['body']) == "UTF-8") {
                $this->FollowUpReminder->validate['body']['rule']['1'] *= 2;
            }

            $statuses = $this->ClientAppointment->getStatuses();
            $FollowUpActions = $this->FollowUpAction->getList($item_type);

            if ($this->data['ClientAppointment']['recurring'] == "" or $this->data['ClientAppointment']['recurring'] == 0) {
                unset($this->data['RecurringAppointment']);
                if(!isset($this->data['ClientAppointment']['duration']) || $this->data['ClientAppointment']['duration'] == ''){
                    $this->data['ClientAppointment']['duration'] = 1;
                }
                $this->data['FollowUpReminder']=$this->data['ClientAppointment'];
                if ($this->FollowUpReminder->save($this->data)) {


                    if ( $item_type == FollowUpReminder::CLIENT_TYPE ) {

                        $this->add_actionline(ACTION_ADD_CLIENT_APPOINTMENT, array(
                            'primary_id' => $this->FollowUpReminder->id,
                            'secondary_id' => $id,
                            'param1' => $item['Client']['business_name'],
                            'param2' => $item['Client']['client_number'],
                            'param3' => $this->data['ClientAppointment']['date'],
                            'param4' => $this->data['ClientAppointment']['date'],
                            'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']]['name'] ?? '',
                            'param6' => $statuses[ClientAppointment::Status_Scheduled],
                            'param7' => null
                        ));
                    } elseif($item_type == FollowUpReminder::WORK_ORDER_TYPE)
                    {
                        $this->add_actionline(ACTION_ADD_WORK_ORDER_APPOINTMENT, array(
                            'primary_id' => $this->FollowUpReminder->id,
                            'secondary_id' => $item['WorkOrder']['id'],
                            'param1' => $item['WorkOrder']['title'],
                            'param2' => $item['WorkOrder']['number'],
                            'param3' => $this->data['ClientAppointment']['date'],
                            'param4' => $this->data['ClientAppointment']['date'],
                            'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']]['name'] ?? '',
                            'param6' => $statuses[ClientAppointment::Status_Scheduled],
                            'param7' => null
                        ));
                    } elseif($item_type == FollowUpReminder::WORKFLOW_TYPE) {
                        $this->loadModel('Workflow');
                        $this->Workflow->addCreateWorkflowAppointmentActivityLog($id, $item['WorkOrder']['workflow_type_id']);
                    }else {
                        $action = ACTION_ADD_INVOICE_APPOINTMENT ; 
                        if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
                            $action = ACTION_ADD_ESTIMATE_APPOINTMENT ; 
                        }
                        $this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $this->FollowUpReminder->id, 'param1' => $item['Invoice']['no'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']]['name'] ?? '', 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));
                    }
					
					if(IS_REST){
						$this->set('id', $this->FollowUpReminder->id);
						$this->render('created');
						return;
					}
                    $this->flashMessage(__('The appointment has been saved', true), 'Sucmessage');
                    $this->redirect($back);
                } else {

                    if(empty($this->data['ClientAppointment']['duration'])) {
                        $this->ClientAppointment->validationErrors['duration'] = __('Required',true);

                    }

                    debug($this->data['ClientAppointment']);
                    debug($this->FollowUpReminder->validationErrors);
                    if(empty($this->data['ClientAppointment']['date_date'])) {
                        $this->ClientAppointment->validationErrors['date_date'] = __('Required',true);
                    }

                    if(empty($this->data['ClientAppointment']['date_time'])) {
                        $this->ClientAppointment->validationErrors['date_time'] = __('Required',true);
                    }

                    $this->ClientAppointment->validationErrors['item_id'] = __('Required', true);
					if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->FollowUpReminder->validationErrors]);


                    $this->flashMessage(__('Can\'t save Appointment', TRUE));
                    if (empty($this->data['ClientAppointment']['item_id']) and empty($id)) {
                        $title = __('Schedule a New Appointment', true);
                    } else {
                        $title = __('Schedule Appointment for', true) ; 
                        if  ( $item_type == FollowUpReminder::CLIENT_TYPE ){
                            $title .= $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
                        }else {
                            $title .=  ' (#' .($item['Invoice']['no']?$item['Invoice']['no']:$item['Invoice']['id']).')';
                        }
                    }
                }
            } else {
                $this->data['FollowUpReminder']=$this->data['ClientAppointment'];
                $row = $this->FollowUpReminder->save($this->data);

                if(!$row)
                {
                    $result = $row;
                    if(empty($this->data['ClientAppointment']['duration'])) {
                        $this->ClientAppointment->validationErrors['duration'] = __('Required',true);

                    }


                    if(empty($this->data['ClientAppointment']['date']) || $this->FollowUpReminder->validationErrors['date']) {
                        if(empty($this->ClientAppointment->validationErrors['date_date'])) {
                            $this->ClientAppointment->validationErrors['date_date'] = __('Required',true);
                        }

                        $this->ClientAppointment->validationErrors['date_time'] = __('Required',true);
                    }

                    $this->ClientAppointment->validationErrors['item_id'] = __('Required', true);
                    if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->FollowUpReminder->validationErrors]);


                    $this->flashMessage(__('Can\'t save Appointment', TRUE));
                    if (empty($this->data['ClientAppointment']['item_id']) and empty($id)) {
                        $title = __('Schedule a New Appointment', true);
                    } else {
                        $title = __('Schedule Appointment for', true) ;
                        if  ( $item_type == FollowUpReminder::CLIENT_TYPE ){
                            $title .= $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
                        }else {
                            $title .=  ' (#' .($item['Invoice']['no']?$item['Invoice']['no']:$item['Invoice']['id']).')';
                        }
                    }
                }else{
                    $capp_id = $this->FollowUpReminder->id;

                    if ( $item_type == FollowUpReminder::CLIENT_TYPE ) {
                        $this->add_actionline(ACTION_ADD_CLIENT_APPOINTMENT, array('primary_id' => $this->FollowUpReminder->id, 'secondary_id' => $id, 'param1' => $item['Client']['business_name'], 'param2' => $item['Client']['client_number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));

                    } elseif($item_type == FollowUpReminder::WORK_ORDER_TYPE)
                    {
                        $this->add_actionline(ACTION_ADD_WORK_ORDER_APPOINTMENT, array('primary_id' => $this->FollowUpReminder->id, 'secondary_id' => $item['WorkOrder']['id'], 'param1' => $item['WorkOrder']['title'], 'param2' => $item['WorkOrder']['number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));
                    } elseif($item_type == FollowUpReminder::WORKFLOW_TYPE) {
                        $this->loadModel('Workflow');
                        $this->Workflow->addCreateWorkflowAppointmentActivityLog($id, $item['WorkOrder']['workflow_type_id']);
                    } else {
                        $action = ACTION_ADD_INVOICE_APPOINTMENT ;
                        if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
                            $action = ACTION_ADD_ESTIMATE_APPOINTMENT ;
                        }
                        $this->add_actionline($action, array('primary_id' => $id, 'secondary_id' => $this->FollowUpReminder->id, 'param1' => $item['Invoice']['no'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[ClientAppointment::Status_Scheduled], 'param7' => null));
                    }

                    $this->data['RecurringAppointment']['last_generated_id'] = $this->FollowUpReminder->id;
                    $this->data['RecurringAppointment']['last_generated_date'] = $this->data['ClientAppointment']['date'];
                    $this->data['RecurringAppointment']['active'] = 1;

                    $this->data['RecurringAppointment']['period_unit'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']]['unit'];
                    $this->data['RecurringAppointment']['unit_count'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']]['unit_count'];
                    if ($this->data['RecurringAppointment']['end_date'] != "") {
                        $date = $this->ClientAppointment->RecurringAppointment->formatDateTime($this->data['RecurringAppointment']['end_date']);
                        $this->data['RecurringAppointment']['end_date'] = $date;
                    }

                    $this->ClientAppointment->RecurringAppointment->save($this->data['RecurringAppointment']);
                    $this->FollowUpReminder->id = $capp_id ;
                    $recurringId = $this->ClientAppointment->RecurringAppointment->id;
                    $this->FollowUpReminder->saveField("recurring_appointment_id", $recurringId, false);
                    $this->cron(getCurrentSite('id'), $recurringId);

                    if(IS_REST){
                        $this->set('id', $this->FollowUpReminder->id);
                        $this->render('created');
                        return;
                    }
                    $this->flashMessage(__('The appointment has been saved', true), 'Sucmessage');
                    $this->redirect($back);
                }

            }
            //Assigning staff members to the appointment.
					
        }else{
            $this->_record_referer_path();
        }

        if (!empty($id)) {
            $item = $this->FollowUpStatus->getItemRecord($item_type, $id);
            $this->data['ClientAppointment']['item_id'] = $id;
            $title = __('Schedule Appointment for', true) ;
            $breadcrumbs_title = "" ; 
            if  ( $item_type == FollowUpReminder::CLIENT_TYPE ){
                $breadcrumbs_title .= $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
            }else if ( $item_type == FollowUpReminder::WORK_ORDER_TYPE ){
                $breadcrumbs_title .= $item['WorkOrder']['title'] . ' (#' . $item['WorkOrder']['number'] . ')';
            }else {
                $breadcrumbs_title .=  sprintf(__(FollowUpStatus::$types_diff[$item_type]['name'].  ' %s',true),'#' .($item['Invoice']['no']?$item['Invoice']['no']:$item['Invoice']['id']));
            }
            $title .= $breadcrumbs_title;
        } else {
            $title = __('Schedule a New Appointment', true);
        }
        if ( $item_type == FollowUpReminder::WORK_ORDER_TYPE ) {
//            $this->loadModel ( 'WorkOrdersStaff') ;
//            $this->set ( 'selected_staffs' , $this->WorkOrdersStaff->find ( 'list' , ['fields'=> ['staff_id']  ,   'conditions' => ['work_order_id'=> $id ] ]) ) ;
            $this->loadModel ( 'Post') ;
            $this->loadModel ( 'ItemStaff') ;
            $this->set ( 'selected_staffs' , $this->ItemStaff->find ( 'list' , ['fields'=> ['staff_id']  ,   'conditions' => ['item_id'=> $id , 'item_type' => FollowUpReminder::WORK_ORDER_TYPE ] ]) ) ;
        }

        $this->set('title_for_layout',  $title);
        $all_staff=UsersHelper::getInstance()->getList(true, ['OR' => ['Staff.active' => 1]]);
        $this->set('staff_ids', $all_staff);
        $this->set('staff_count', count($all_staff));
        $this->set('item_type', $item_type);
        $this->set('client_id', $id);
        $this->set('page_title', $title);
        $this->set('breadcrumbs_title', $breadcrumbs_title);
    }

    function owner_edit($id = null) {
        $this->loadModel ( 'Staff');
        $this->loadModel ( 'Post');
        $this->loadModel ( 'ItemStaff');
//        $this->loadModel ( 'AppointmentsStaff');
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel('Client');
             if ( ifPluginActive(WorkOrderPlugin)){
        $this->loadModel('WorkOrder');
    }
        $this->loadModel('Invoice');
        
        $owner = getAuthOwner();
        $formats = getDateFormats('std');
        $dateFormat = $formats[$owner['date_format']];
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);

        $staff_id = getAuthOwner('staff_id');
        $row = $this->FollowUpReminder->read(null, $id);
        
        $item_type = $row['FollowUpReminder']['item_type'];
        if (!$row) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('Appointment not found', true)));
            $this->flashMessage(__('Appointment not found', TRUE));
            $this->redirect(array('action' => 'index', $item_type));
        }
        $partnerType = isset($row['FollowUpReminder']['partner_type']) ? $row['FollowUpReminder']['partner_type'] : false;

        $row['ClientAppointment'] = $row['FollowUpReminder']; // For creating

        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect("/");
        }

        $oldAssignedStaff = $this->ItemStaff->getAssignedStaff(ItemStaff::APPOINTMENT_ITEM_TYPE, $id);
        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $item = $row['FollowUpReminder']['item_id'];
            $this->loadModel('ItemStaff');
            $this->loadModel('Post');

            $follow_up_conditions = array('FollowUpReminder.item_type' => $item_type, 'FollowUpReminder.staff_id' => $staff_id);
            $follow_ups_list = array_values($this->FollowUpReminder->find('list', array('fields' => 'id,item_id', 'conditions' => $follow_up_conditions)));
            $client_conditions = array('ItemStaff.item_type' => $item_type, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'ItemStaff.id,ItemStaff.item_id', 'conditions' => $client_conditions)));
            $isAssignedStaff = $this->ItemStaff->find('first', ['conditions' => ['ItemStaff.item_type' => ItemStaff::APPOINTMENT_ITEM_TYPE, 'ItemStaff.staff_id' => $staff_id, 'ItemStaff.item_id' => $id]]);
            if (!in_array($id, $client_list) && !in_array($item, $follow_ups_list) && !$isAssignedStaff) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect("/");
            }
        }
		if(IS_REST) $this->data['ClientAppointment']['id'] = $id;
        if (!empty($this->data))
            {
                if(IS_REST){
                    $staff_list_ids = array_keys(UsersHelper::getInstance()->getList());
                    foreach($this->data['ClientAppointment']['staff_id'] as $staff_id){
                        if(!in_array($staff_id, $staff_list_ids)){
                            $this->cakeError('error400', ["message" => 'Invalid Staff']);
                        }
                    }
                }

                $this->loadModel('FollowUpAction');
                $this->loadModel('Post');
                $FollowUpActions = $this->FollowUpAction->getList($item_type);

            if((isset($this->data['ClientAppointment']['date']) && !is_string($this->data['ClientAppointment']['date'])) 
            || (isset($this->data['ClientAppointment']['end_date']) && !is_string($this->data['ClientAppointment']['end_date']))) {
                $message = __('Invalid date type format',true);
                if(IS_REST) $this->cakeError('error404', ["message" => $message]);
                $this->flashMessage(__("Please enter a valid date", true));
            }

            $item = $this->FollowUpStatus->getItemRecord($item_type, $row['ClientAppointment']['item_id']);
            if(IS_REST){
                $this->data['ClientAppointment'] = array_merge($row['ClientAppointment'], $this->data['ClientAppointment']);
            }
            $this->data = $this->_submitCommon($this->data, $item_type, $item);
            $duration = $this->data['ClientAppointment']['duration'];
            if(!is_numeric($duration) && strpos($duration, ':') !== false ){
                $time = explode(':',$duration);
                if((empty($time[0]) && $time[0] == "") || (empty($time[1]) && $time[1] == "")){
                    $this->flashMessage(__("Please enter a valid duration", true));
                    return;
                }
            }
            if ($this->data['ClientAppointment']['recurring'] == "" or $this->data['ClientAppointment']['recurring'] == 0) {
               
                $this->data['FollowUpReminder'] = $this->data['ClientAppointment'];
                $result = $this->FollowUpReminder->save($this->data);
                if($result)
                {
                    if($this->data['RecurringAppointment']['id']!=""){

                        $this->FollowUpReminder->RecurringAppointment->id = $this->data['RecurringAppointment']['id'];
                        $this->FollowUpReminder->RecurringAppointment->saveField("active", 0, false);
                    }
                    $this->loadModel('Client');

                    $status = isset($this->data['ClientAppointment']['status']) ? $this->data['ClientAppointment']['status'] : $row['ClientAppointment']['status'];
                    $status_date = isset($this->data['ClientAppointment']['status_date']) ? $this->data['ClientAppointment']['status_date'] : $row['ClientAppointment']['status_date'];
                    $statuses = $this->ClientAppointment->getStatuses();


                    if ( $item_type == FollowUpReminder::CLIENT_TYPE) {
                        $client = $this->Client->read(null, $row['ClientAppointment']['item_id']);

                        $this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                    } elseif($item_type == FollowUpReminder::WORK_ORDER_TYPE)
                    {
                        $this->add_actionline(ACTION_EDIT_WORK_ORDER_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $item['WorkOrder']['title'], 'param2' => $item['WorkOrder']['number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                    } elseif($item_type == FollowUpReminder::WORKFLOW_TYPE) {
                        $newAssignedStaff = $this->ItemStaff->getAssignedStaff(ItemStaff::APPOINTMENT_ITEM_TYPE, $id);
                        $this->loadModel('Workflow');
                        $this->Workflow->addUpdateWorkflowAppointmentActivityLog($item, $row['ClientAppointment'], $oldAssignedStaff, $newAssignedStaff);
                    }else {
                        $action = ACTION_EDIT_INVOICE_APPOINTMENT;
                        if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
                            $action = ACTION_EDIT_ESTIMATE_APPOINTMENT ;
                        }
                        $client = $this->Invoice->read ( null , $row['ClientAppointment']['item_id']);
                        $this->add_actionline($action, array('primary_id' => $row['ClientAppointment']['item_id'], 'secondary_id' =>$id , 'param1' => $client['Invoice']['no'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                    }

                    if(IS_REST){
                        $this->render('success');
                        return;
                    }
                    if (isset($url_params['back_to_client'])) {

                        $view_url =FollowUpStatus::$types_diff[$item_type]['view_url'];
                        $this->redirect($view_url.$row['ClientAppointment']['item_id'].'#AppointmentsBlock');
                    } else {
                        $this->flashMessage(__("The appointment has been saved", true), 'Sucmessage');
                        $referer_url = !($this->Session->check('referer_url')) ? array('action' => 'index',$item_type, $partnerType) : $this->Session->read('referer_url');

                        if (false != strpos($referer_url, 'iframe=1')) {
                            $view_url = FollowUpStatus::$types_diff[$item_type]['view_url'];
                            $this->redirect($view_url.$row['ClientAppointment']['item_id'].'#AppointmentsBlock');
                        }

                        $this->redirect($referer_url);
                    }
                }else{
                    $this->ClientAppointment->validationErrors = $this->FollowUpReminder->validationErrors;
                    $this->flashMessage(__("The appointment couldn't be saved", true));

                }

            } else {

                $this->data['FollowUpReminder'] = $this->data['ClientAppointment'];

                $this->FollowUpReminder->save($this->data);


                $status = isset($this->data['ClientAppointment']['status']) ? $this->data['ClientAppointment']['status'] : $row['ClientAppointment']['status'];
                $status_date = isset($this->data['ClientAppointment']['status_date']) ? $this->data['ClientAppointment']['status_date'] : $row['ClientAppointment']['status_date'];
                $statuses = $this->ClientAppointment->getStatuses();
                $this->loadModel('FollowUpAction');
                $this->loadModel('Post');
                $FollowUpActions = $this->FollowUpAction->getList($item_type);
                if ( $item_type == FollowUpReminder::CLIENT_TYPE) {
                    $client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
                    $this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                } elseif($item_type == FollowUpReminder::WORK_ORDER_TYPE)
                {
                    $this->add_actionline(ACTION_EDIT_WORK_ORDER_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $item['WorkOrder']['title'], 'param2' => $item['WorkOrder']['number'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                } elseif($item_type == FollowUpReminder::WORKFLOW_TYPE) {
                    $newAssignedStaff = $this->ItemStaff->getAssignedStaff(ItemStaff::APPOINTMENT_ITEM_TYPE, $id);
                    $this->loadModel('Workflow');
                    $this->Workflow->addUpdateWorkflowAppointmentActivityLog($item, $row['ClientAppointment'], $oldAssignedStaff, $newAssignedStaff);
                } else {
                    $action = ACTION_EDIT_INVOICE_APPOINTMENT; 
                    if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
                        $action = ACTION_EDIT_ESTIMATE_APPOINTMENT ; 
                    }
                    $client = $this->Invoice->read ( null , $row['ClientAppointment']['item_id']);
                    $this->add_actionline($action, array('primary_id' => $row['ClientAppointment']['item_id'], 'secondary_id' =>$id , 'param1' => $client['Invoice']['no'], 'param3' => $this->data['ClientAppointment']['date'], 'param4' => $this->data['ClientAppointment']['date'], 'param5' => $FollowUpActions[$this->data['ClientAppointment']['action_id']], 'param6' => $statuses[$status], 'param7' => $status_date));
                }


                $this->data['RecurringAppointment']['active'] = 1;
                if(empty($this->data['RecurringAppointment']['id'])){
                $this->data['RecurringAppointment']['last_generated_id'] = $id;
                $this->data['RecurringAppointment']['last_generated_date'] = $this->data['ClientAppointment']['date'];                    
                }
                
                $this->data['RecurringAppointment']['period_unit'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']] ['unit'];
                $this->data['RecurringAppointment']['unit_count'] = $this->ClientAppointment->RecurringAppointment->period_list[$this->data['RecurringAppointment']['unit_name']] ['unit_count'];
                if ($this->data['RecurringAppointment']['end_date'] != "") {
                    $date = $this->ClientAppointment->RecurringAppointment->formatDateTime($this->data['RecurringAppointment']['end_date']);
                    $this->data['RecurringAppointment']['end_date'] = $date;
                }

               $res= $this->ClientAppointment->RecurringAppointment->save($this->data['RecurringAppointment']);
                $recurringId = $this->ClientAppointment->RecurringAppointment->id;
                $this->FollowUpReminder->saveField("recurring_appointment_id", $this->ClientAppointment->RecurringAppointment->id, false);
                $this->cron(getCurrentSite('id'), $recurringId);

				if(IS_REST){
					$this->render('success');
					return;
				}
                $this->flashMessage(__('The appointment has been saved', true), 'Sucmessage');
                
                if (isset($url_params['back_to_client'])) {
                    $view_url =FollowUpStatus::$types_diff[$item_type]['view_url'];
                    $this->redirect($view_url.$row['ClientAppointment']['item_id'].'#AppointmentsBlock');
//                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['ClientAppointment']['item_id'], '#' => 'AppointmentsBlock'));
                } else {
                    $this->redirect(array('action' => 'index',$item_type, $partnerType));
                }
            }
        }else {
            $this->_record_referer_path();
            $selectedStaff = $this->ItemStaff->find ( 'list' , ['fields'=> ['staff_id']  ,   'conditions' => ['item_id'=> $id , 'item_type' => FollowUpReminder::APPOINTMENT_TYPE] ]);
            $this->set ( 'selected_staffs' , $selectedStaff ) ;
        }

//        debug($row);

        $item = $this->FollowUpStatus->getItemRecord($item_type, $row['ClientAppointment']['item_id']);

        $breadcrumbs_title = "" ; 
        if  ( $item_type == FollowUpReminder::CLIENT_TYPE ){
            $breadcrumbs_title .= $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
        }else if ( $item_type == FollowUpReminder::WORK_ORDER_TYPE ){
            $breadcrumbs_title .= $item['WorkOrder']['title'] . ' (#' . $item['WorkOrder']['number'] . ')';
        } else if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
            $breadcrumbs_title .=  __("Estimate",true).' #' .($item['Invoice']['no']?$item['Invoice']['no']:$item['Invoice']['id']);
        }else if ( $item_type == FollowUpReminder::INVOICE_TYPE ) {
            $breadcrumbs_title .=  __("Invoice",true).' #' .($item['Invoice']['no']?$item['Invoice']['no']:$item['Invoice']['id']);
        }
        $title = __('Edit Appointment #', true) . ' ' . $id;
        $this->set('title_for_layout',  $title);
        $this->set('page_title', $title);
        $this->data = $row;
        $jsDateFormat = getDateFormats('std');

        if ($this->data['RecurringAppointment']['id'] != "" and $this->data['RecurringAppointment']['active'] == 1) {
            $this->data['ClientAppointment']['recurring'] = 1;
            if ($this->data['RecurringAppointment']['end_date'] != "0000-00-00") {
            $this->data['RecurringAppointment']['end_date'] = format_date($this->data['RecurringAppointment']['end_date']);

            } else {
                unset($this->data['RecurringAppointment']['end_date']);
            }
        }
        $this->set('staff_ids', UsersHelper::getInstance()->setSelectedStaff($selectedStaff)->getList());
//        $this->set('staff_ids', $this->Staff->getList());
        $this->set('staff_count', $this->Staff->find('count'));
       // $this->data['ClientAppointment']['date'] = format_date(date('Y-m-d', strtotime($this->data['ClientAppointment']['date']))) . date(' H:i', strtotime($this->data['ClientAppointment']['date']));
        $this->set ( 'breadcrumbs_title' ,$breadcrumbs_title );
        $this->set ( 'item_type' ,$item_type );
        $this->set ( 'is_edit' ,1 );
        $this->set ( 'client_id' , $row['ClientAppointment']['item_id'] );
        $this->__set_data($item_type, $item);
        $this->render('owner_add');
    }

    function owner_dismiss($id = null) {
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'ClientAppointment');
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel('Post');
        $this->loadModel('Invoice');
        $this->loadModel('ItemStaff');
        if ( !empty ($_POST['ids'] )){
                $ids = $_POST['ids'];
                $id = $_POST['ids'][0];
        }else {
                $ids = [$id];
        }
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $is_ajax = $this->RequestHandler->isAjax();
        
        $staff_id = getAuthOwner('staff_id');
        $row = $this->FollowUpReminder->read(null, $id);
        $item_type = $row['FollowUpReminder']['item_type'];
        $partnerType = isset($row['FollowUpReminder']['partner_type']) ? $row['FollowUpReminder']['partner_type'] : false;

        if (!$row) {
            $this->flashMessage(__('Appointment not found', TRUE));
            $this->redirect(array('action' => 'index',$item_type));
        }
        
        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect("/");
        }
        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $item = $row['FollowUpReminder']['item_id'];
            $client_conditions = array('ItemStaff.item_type' => $item_type, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'ItemStaff.id,ItemStaff.item_id', 'conditions' => $client_conditions)));
            $follow_up_conditions = array('FollowUpReminder.item_type' => $item_type, 'FollowUpReminder.staff_id' => $staff_id);
            $follow_ups_list = array_values($this->FollowUpReminder->find('list', array('fields' => 'id,item_id', 'conditions' => $follow_up_conditions)));
            $this->loadModel('ItemStaff');
            $isAssignedStaff = $this->ItemStaff->find('first', ['conditions' => ['ItemStaff.item_type' => ItemStaff::APPOINTMENT_ITEM_TYPE, 'ItemStaff.staff_id' => $staff_id, 'ItemStaff.item_id' => $id]]);

            if (!in_array($id, $client_list) && !in_array($item, $follow_ups_list) && !$isAssignedStaff) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index', $item_type));
            }
        }
        $itemRecord = $this->FollowUpStatus->getItemRecord($item_type, $row['ClientAppointment']['item_id']);
        $statuses = $this->ClientAppointment->getStatuses();
        $this->loadModel('FollowUpAction');
        $FollowUpActions = $this->FollowUpAction->getList($item_type);
		App::import('Vendor', 'notification_2');
        foreach ( $ids as $id ){
                $row = $this->FollowUpReminder->read(null, $id);
                $row['ClientAppointment'] = $row['FollowUpReminder'];
                $this->FollowUpReminder->id = $id;
				
                $this->FollowUpReminder->saveField('status', ClientAppointment::Status_Dismissed);
				
                $status = ClientAppointment::Status_Dismissed;
                $this->FollowUpReminder->saveField('status_date', date('Y-m-d H:i:s'));
                $status_date = date('Y-m-d H:i:s');

                
                if ( $item_type == FollowUpReminder::CLIENT_TYPE ) {
                    $this->loadModel('Client');
                    $client = $this->Client->read(null, $row['ClientAppointment']['item_id']);
                    $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                        ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                        : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                    $this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array(
                        'primary_id' => $id,
                        'secondary_id' => $row['ClientAppointment']['item_id'],
                        'param1' => $client['Client']['business_name'],
                        'param2' => $client['Client']['client_number'],
                        'param3' => $row['ClientAppointment']['date'],
                        'param4' => $row['ClientAppointment']['date'],
                        'param5' => $param5,
                        'param6' => $statuses[$status],
                        'param7' => $status_date
                    ));
                }elseif($item_type == FollowUpReminder::WORK_ORDER_TYPE)
                {
                    $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                        ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                        : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                    $this->add_actionline(ACTION_EDIT_WORK_ORDER_APPOINTMENT, array(
                        'primary_id' => $id,
                        'secondary_id' => $row['ClientAppointment']['item_id'],
                        'param1' => $itemRecord['WorkOrder']['title'],
                        'param2' => $itemRecord['WorkOrder']['number'],
                        'param3' => $row['ClientAppointment']['date'],
                        'param4' => $row['ClientAppointment']['date'],
                        'param5' => $param5,
                        'param6' => $statuses[$status],
                        'param7' => $status_date
                    ));
                }else {
                    $action = ACTION_EDIT_INVOICE_APPOINTMENT; 
                    if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
                        $action = ACTION_EDIT_ESTIMATE_APPOINTMENT ; 
                    }
                    $client = $this->Invoice->read ( null , $row['ClientAppointment']['item_id']);
                    $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                        ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                        : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                    $this->add_actionline($action, array('primary_id' => $row['ClientAppointment']['item_id'], 'secondary_id' =>$id , 'param1' => $client['Invoice']['no'], 'param3' => $row['ClientAppointment']['date'], 'param4' => $row['ClientAppointment']['date'], 'param5' => $param5, 'param6' => $statuses[$status], 'param7' => $status_date));
                }
				
				$data['action'] = NotificationV2::NOTI_ACTION_DISMISS;
				NotificationV2::update_notification($id, NotificationV2::NOTI_UPDATE_APPOINTMENT,$data );


        }
            
        if ($is_ajax) {
            echo 'done';
            die();
        }

        $this->flashMessage(__('The appointment has been marked as dismissed', true), 'Sucmessage');
        if ($url_params['back_to_client']) {
            $this->redirect ( FollowUpStatus::$types_diff[$item_type]['view_url'].$row['FollowUpReminder']['item_id'].'#AppointmentsBlock');
        }
        $this->redirect(array('action' => 'index' , $item_type, $partnerType));
    }

    function owner_done($id = null, $note = 0 ) {
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'ClientAppointment');
        $this->loadModel ( 'FollowUpStatus');
        $this->loadModel('Post');
        $this->loadModel('Invoice');
        $this->loadModel('ItemStaff');
        
        if ( !empty ($_POST['ids'] )){
                $ids = $_POST['ids'];
                $id = $_POST['ids'][0];
        }else {
                $ids = [$id];
        }
        
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);
        $is_ajax = $this->RequestHandler->isAjax();
        $staff_id = getAuthOwner('staff_id');
        $this->loadModel('Post');
        $row = $this->FollowUpReminder->read(null, $id);
        $item_type = $row['FollowUpReminder']['item_type'];
        $partnerType = isset($row['FollowUpReminder']['partner_type']) ? $row['FollowUpReminder']['partner_type'] : false;
        if (!$row) {
            $this->flashMessage(__('Appointment not found', TRUE));
            $this->redirect(array('action' => 'index'), $item_type);
        }

        if ((!check_permission(Edit_Delete_All_Notes_Attachments) && !check_permission(Edit_Delete_His_Own_Attachments_Notes_Only))) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect(array('action' => 'index'), $item_type);
        }

        if (!check_permission(Edit_Delete_All_Notes_Attachments)) {
            $item = $row['FollowUpReminder']['item_id'];
            $this->loadModel('ItemStaff');
            $client_conditions = array('ItemStaff.item_type' => $item_type, 'ItemStaff.staff_id' => $staff_id);
            $client_list = array_values($this->ItemStaff->find('list', array('fields' => 'ItemStaff.id,ItemStaff.item_id', 'conditions' => $client_conditions)));
            $follow_up_conditions = array('FollowUpReminder.item_type' => $item_type, 'FollowUpReminder.staff_id' => $staff_id);
            $follow_ups_list = array_values($this->FollowUpReminder->find('list', array('fields' => 'id,item_id', 'conditions' => $follow_up_conditions)));
            $this->loadModel('ItemStaff');
            $isAssignedStaff = $this->ItemStaff->find('first', ['conditions' => ['ItemStaff.item_type' => ItemStaff::APPOINTMENT_ITEM_TYPE, 'ItemStaff.staff_id' => $staff_id, 'ItemStaff.item_id' => $id]]);

            if (!in_array($id, $client_list) && !in_array($item, $follow_ups_list) && !$isAssignedStaff) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect(array('action' => 'index'), $item_type);
            }
        }
		$statuses = $this->ClientAppointment->getStatuses();
		App::import('Vendor', 'notification_2');
		foreach ( $ids as $id ){
			
			$row = $this->FollowUpReminder->read(null, $id);
                        $row['ClientAppointment'] = $row['FollowUpReminder'];
			$this->FollowUpReminder->id = $id;
			$this->FollowUpReminder->saveField('status', ClientAppointment::Status_Done);
			$status = ClientAppointment::Status_Done;
			$status_date = date('Y-m-d H:i:s');
			$this->FollowUpReminder->saveField('status_date',$status_date);
            $itemRecord = $this->FollowUpStatus->getItemRecord($item_type, $row['ClientAppointment']['item_id']);

            $this->loadModel('FollowUpAction');
                        $FollowUpActions = $this->FollowUpAction->getList($item_type);
                        if ( $item_type == FollowUpReminder::CLIENT_TYPE) {
                            $this->loadModel('Client');
                            $client = $this->Client->read(null, $row['ClientAppointment']['item_id']);

                            $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                                ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                                : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                            $this->add_actionline(ACTION_EDIT_CLIENT_APPOINTMENT, array('primary_id' => $id, 'secondary_id' => $row['ClientAppointment']['item_id'], 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $row['ClientAppointment']['date'], 'param4' => $row['ClientAppointment']['date'], 'param5' => $param5, 'param6' => $statuses[$status], 'param7' => $status_date));

                        }elseif($item_type == FollowUpReminder::WORK_ORDER_TYPE)
                        {
                            $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                                ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                                : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');

                            $this->add_actionline(ACTION_EDIT_WORK_ORDER_APPOINTMENT, array(
                                'primary_id' => $id,
                                'secondary_id' => $row['ClientAppointment']['item_id'],
                                'param1' => $itemRecord['WorkOrder']['title'],
                                'param2' => $itemRecord['WorkOrder']['number'],
                                'param3' => $row['ClientAppointment']['date'],
                                'param4' => $row['ClientAppointment']['date'],
                                'param5' => $param5,
                                'param6' => $statuses[$status],
                                'param7' => $status_date
                            ));
                        }else {
                            $action = ACTION_EDIT_INVOICE_APPOINTMENT; 
                            if ( $item_type == FollowUpReminder::ESTIMATE_TYPE ) {
                                $action = ACTION_EDIT_ESTIMATE_APPOINTMENT ; 
                            }
                            $client = $this->Invoice->read ( null , $row['ClientAppointment']['item_id']);
                            $param5 = is_string($FollowUpActions[$row['ClientAppointment']['action_id']])
                                ? $FollowUpActions[$row['ClientAppointment']['action_id']]
                                : ($FollowUpActions[$row['ClientAppointment']['action_id']]['name'] ?? '');
                            $this->add_actionline($action, array('primary_id' => $row['ClientAppointment']['item_id'], 'secondary_id' =>$id , 'param1' => $client['Invoice']['no'], 'param3' => $row['ClientAppointment']['date'], 'param4' => $row['ClientAppointment']['date'], 'param5' => $param5, 'param6' => $statuses[$status], 'param7' => $status_date));
                        }
				$data['action'] = NotificationV2::NOTI_ACTION_DONE;
				NotificationV2::update_notification($id, NotificationV2::NOTI_UPDATE_APPOINTMENT,$data );

		}
        
        if ($is_ajax) {
            echo 'done';
            die();
        }

        $this->flashMessage(__('The appointment has been marked as done', true), 'Sucmessage');
       
        if ($note == 1) {
            if (isset($url_params['back_to_client'])) {
                $more['back_to_client'] = 1;
            }

            $this->redirect(array('controller' => 'posts', 'action' => 'post', $item_type, $row['FollowUpReminder']['item_id'], 0, $id, '?' => $more));
        }
        if ($url_params['back_to_client']) {
            $this->redirect ( FollowUpStatus::$types_diff[$item_type]['view_url'].$row['FollowUpReminder']['item_id'].'#AppointmentsBlock');
    //            $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['ClientAppointment']['item_id'], '#' => 'AppointmentsBlock'));
        }
        $this->redirect(array('action' => 'index' , $item_type, $partnerType));
    }

    function __set_data($item_type = 1, $item = null) {

        foreach ($this->ClientAppointment->RecurringAppointment->period_list as $key => $value) {
            $periods[$key] = __(ucfirst($key), true);
        }

        $this->set('periods', $periods);

        $statuses = $this->ClientAppointment->getStatuses();

        $this->loadModel('Post');
        $this->loadModel('FollowUpAction');

        if ($item_type == FollowUpReminder::WORKFLOW_TYPE) {
            $service = izam_resolve(WorkflowService::class);
            $actions = $service->getWorkflowTypeActionsFormatted(
                getWorkFlowTypeEntityName($item['WorkOrder']['workflow_type_id'])
            );
        } else {
            $actions = $this->FollowUpAction->getList($item_type);
            if (check_permission(Edit_General_Settings)) {
                $actions[] = array('name' => false, 'value' => false, 'data-divider' => "true");
                $actions[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> ' . __('Edit Actions List', true) . ' </span>', 'name' => __('Edit Actions List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
            }
        }
        
        $this->set('partners_label', FollowUpReminder::$partner_types[FollowUpReminder::$types_data[$item_type]['partner_type']]['label']);
        $this->set('actions', $actions);
//        print_r ( $f_groups  ) ;die ; 
//        $this->set('actions', $f_groups);
        $this->set('statuses', $statuses);
    }

    function test_cron($site_id = null, $recurringAppointmentId = null)
    {
        set_time_limit(3600);
        App::import('Vendor', 'oilogger');
        Oilogger::initSession('appointments_test_cron_' . date('Y_m_d') . '.txt');
        Oilogger::log('Started......................');
        Configure::write('debug', 0);
        Oilogger::log('Note: Debug is off now');
        $this->loadModel('Post');
        $this->loadModel('ClientAppointment');
        $this->loadModel('FollowUpReminder');
        App::import('Vendor', 'Recurring');

        $this->autoRender = $this->autoLayout = false;
        $this->loadModel("Site");
        $sitesToRun=[];
        $conditions = array('Site.status' => SITE_STATUS_ACTIVE);
        if ($site_id) {
            $conditions['Site.id'] = $site_id;
        }
        $sites = $this->Site->find('all', array('recursive' => -1, 'order' => 'Site.id desc', 'conditions' => $conditions));
        Oilogger::log('Sites Count ' . count($sites));
        $this->loadModel('Timezone');

        IzamDatabaseServiceProvider::boot(getPDO(), getPortalConfig());

        foreach ($sites as $site) {
            Oilogger::log('Current Site: ' . $site['Site']['id']);
            $GLOBALS['site'] = $site['Site'];
            $config = json_decode($site['Site']['db_config'], true);
            try {
                ConnectionManager::getDataSource('default')->swtich_db($config);
                $this->FollowUpReminder->getDataSource()->swtich_db($config);

                IzamDatabaseServiceProvider::setSiteConnection(getPdo());
                removePdo();

                $this->loadModel('Timezone');
                $zone = $this->Timezone->field('zone_name', array("Timezone.id" => $site['Site']['timezone']));
                date_default_timezone_set($zone);
                $currDate = date('Y-m-d');

                $recurringConditions = array('RecurringAppointment.active' => 1, 'OR' => array('RecurringAppointment.end_date' => '0000-00-00', 'AND' => array('RecurringAppointment.end_date >= ' => $currDate)));
                if ($recurringAppointmentId) {
                    $recurringConditions['RecurringAppointment.id'] = $recurringAppointmentId;
                    unset ($recurringConditions['OR']);
                }
                $RecurringAppointmentsCount = $this->FollowUpReminder->RecurringAppointment->find('count', array('conditions' => $recurringConditions));

                var_dump($RecurringAppointmentsCount);

                Oilogger::log('Recurring Appointments Count ' . $RecurringAppointmentsCount);

                if($RecurringAppointmentsCount>0){

                    $sitesToRun[]=$site['Site']['id'];
                }

            } catch (Throwable $e) {
            echo $e->getMessage()." \n\r";
            }
        }
        print_r($sitesToRun);
        die();
    }
    function cron($site_id = null, $recurringAppointmentId = null) {
        set_time_limit(3600);
	    App::import('Vendor', 'oilogger');
        Oilogger::initSession('appointments_' . date('Y_m_d') . '.txt');
        Oilogger::log('Started......................');
        Configure::write('debug', 0);
        Oilogger::log('Note: Debug is off now');
        $this->loadModel('Post');
        $this->loadModel('ClientAppointment');
        $this->loadModel('FollowUpReminder');
        App::import('Vendor', 'Recurring');

        $this->autoRender = $this->autoLayout = false;
        $this->loadModel("Site");
      
		$conditions = array('Site.status'=>SITE_STATUS_ACTIVE);
		if ($site_id) {
            $conditions['Site.id'] = $site_id;
        }
		$sites = $this->Site->find('all', array('recursive' => -1,'order'=>'Site.id desc','conditions' => $conditions));
        Oilogger::log('Sites Count ' . count($sites));
        $this->loadModel('Timezone');

        IzamDatabaseServiceProvider::boot(getPDO(), getPortalConfig());

        foreach ($sites as $site) {
            Oilogger::log('Current Site: ' . $site['Site']['id']);
            $GLOBALS['site'] = $site['Site'];
            $config = json_decode($site['Site']['db_config'], true);
            try {
                ConnectionManager::getDataSource('default')->swtich_db($config);
                $this->FollowUpReminder->getDataSource()->swtich_db($config);

                IzamDatabaseServiceProvider::setSiteConnection(getPdo());
	            removePdo();

                $this->loadModel('Timezone');
                $zone = $this->Timezone->field('zone_name', array("Timezone.id" => $site['Site']['timezone']));
                date_default_timezone_set($zone);
                $currDate = date('Y-m-d');

                $recurringConditions = array('RecurringAppointment.active' => 1, 'OR' => array('RecurringAppointment.end_date' => '0000-00-00', 'AND' => array('RecurringAppointment.end_date >= ' => $currDate)));
                if ($recurringAppointmentId) {
                    $recurringConditions['RecurringAppointment.id'] = $recurringAppointmentId;
                    unset ($recurringConditions['OR']);
                }
                $RecurringAppointments = $this->FollowUpReminder->RecurringAppointment->find('all', array('conditions' => $recurringConditions));

                Oilogger::log('Recurring Appointments Count ' . count($RecurringAppointments));
                //	print_r($RecurringAppointments);

                foreach ($RecurringAppointments as $RecurringAppointment) {
                    if(isset($RecurringAppointment['RecurringAppointment']['branch_id'])){
                        setRequestCurrentBranch($RecurringAppointment['RecurringAppointment']['branch_id']);
                    }
                    $id = $RecurringAppointment['RecurringAppointment']['id'];

                    $period = $RecurringAppointment['RecurringAppointment']['period_unit'];
                    $period_count = $RecurringAppointment['RecurringAppointment']['unit_count'];
                    $last_generated_date = $RecurringAppointment['RecurringAppointment']['last_generated_date'];

                    //echo $id."|".$period."|".$period_count."|".$last_generated_date."\n";

                    $this->FollowUpReminder->RecurringAppointment->id = $id;
                    if (!strtotime($last_generated_date)) {
                        $this->FollowUpReminder->RecurringAppointment->saveField("is_ended", 1, false);
                        continue;
                    }


                    $previous_dates = $this->FollowUpReminder->find('list', array('fields' => array('FollowUpReminder.id', 'FollowUpReminder.date'), 'conditions' => array('FollowUpReminder.recurring_appointment_id' => $id)));

                    $fromDate = $last_generated_date;
                    $previous_dates[] = $fromDate;

                    $fromDate = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);

                    $count = 0;
                    $nextapp = strtotime($currDate . ' +' . $period_count . ' ' . $period);
                    $nextapp = date("Y-m-d", $nextapp);
                    // echo "Next App".$fromDate."\n";

                    if ($nextapp < date("Y-m-d")) {
                        //echo "Insert New App\n";
                    }

                    //echo "Starttotime ".strtotime($RecurringAppointment['RecurringAppointment']['end_date'])."\n";


                    if (strtotime($fromDate) > 0) {

                        while (strtotime($fromDate) <= strtotime($nextapp) && (!strtotime($RecurringAppointment['RecurringAppointment']['end_date']) || strtotime($RecurringAppointment['RecurringAppointment']['end_date']) < 0 || $fromDate <= $RecurringAppointment['RecurringAppointment']['end_date'])) {

                            //  echo "Insert New App\n";
                            $count++;
                            if ($count > 50)
                                break;
                            $Find = $this->FollowUpReminder->query("SELECT `FollowUpReminder`.*  FROM `follow_up_reminders` AS `FollowUpReminder` WHERE `FollowUpReminder`.`recurring_appointment_id` = " . $RecurringAppointment['RecurringAppointment']['id'] . " ORDER BY `FollowUpReminder`.`id` DESC LIMIT 1", false);

                            if (empty($Find)) {
                                break;
                            }
                            $Find[0]['FollowUpReminder']['status'] = ClientAppointment::Status_Scheduled;
                            $du = strtotime($Find[0]['FollowUpReminder']['end_date']) - strtotime($Find[0]['FollowUpReminder']['date']);
                            $Find[0]['FollowUpReminder']['date'] = $fromDate . ' ' . date("H:i:s", strtotime($Find[0]['FollowUpReminder']['date']));
                            $end_date = date('Y-m-d H:i', strtotime($this->data['FollowUpReminder']['date'] . ' +' . $du . ' SECONDS'));
                            $Find[0]['FollowUpReminder']['duration'] = ($du / 3600);
                            $this->loadModel('ItemStaff');
                            $assigned_staff = $this->ItemStaff->find('list', array('fields' => 'ItemStaff.staff_id', 'conditions' => array('ItemStaff.item_id' => $Find[0]['FollowUpReminder']['id'], 'ItemStaff.item_type' => ItemStaff::APPOINTMENT_ITEM_TYPE)));
                            unset($Find[0]['FollowUpReminder']['id']);
                            $this->FollowUpReminder->create();

                            if ($test = $this->FollowUpReminder->save($Find[0]['FollowUpReminder'])) {

                                $follow_up_remineder_id = $this->FollowUpReminder->id;
                                $this->ItemStaff->assign_staff_members($assigned_staff , $follow_up_remineder_id , FollowUpReminder::APPOINTMENT_TYPE) ;

                                $item_type = $Find[0]['FollowUpReminder']['item_type'];
                                $id = $Find[0]['FollowUpReminder']['item_id'];
                                $this->loadModel('Client');
                                $this->loadModel('Post');
                                if ($item_type == FollowUpReminder::CLIENT_TYPE) {
                                    $client = $this->Client->find('first', array('conditions' => array('Client.id' => $id)));
                                    $notification_name = $client['Client']['business_name'];
                                } else if ($item_type == FollowUpReminder::INVOICE_TYPE) {
                                    $notification_name = 'Invoice #' . $id;
                                } else if ($item_type == FollowUpReminder::ESTIMATE_TYPE) {
                                    $notification_name = 'Estimate #' . $id;
                                } else if ($item_type == FollowUpReminder::WORK_ORDER_TYPE) {
                                    $notification_name = 'Work order #' . $id;
                                }

                                App::import('Vendor', 'notification_2');
                                if ($Find[0]['staff_id'] == 0) {
                                    $trigger_type = NotificationV2::NOTI_TRIGGER_OWNER;
                                } else {
                                    $trigger_type = NotificationV2::NOTI_TRIGGER_STAFF;
                                }


                                $trigger = NotificationV2::get_trigger();
                                $usersNotifications = array_values($assigned_staff);

                                foreach ($usersNotifications as $key => $user) {
                                    if ($user == getCurrentSite('id')) {
                                        $usersNotifications[$key] = 0;
                                    }
                                }

                                NotificationV2::add_notification(
                                    NotificationV2::NOTI_UPDATE_APPOINTMENT,
                                    $this->FollowUpReminder->id,
                                    $usersNotifications,
                                    $user_type = NotificationV2::NOTI_USER_TYPE_STAFF,
                                    $trigger_type,
                                    $Find[0]['FollowUpReminder']['staff_id'],
                                    NotificationV2::NOTI_ACTION_ADD,
                                    ['appointment_date' => $Find[0]['FollowUpReminder']['date'], 'name' => $notification_name]
                                );

                                $this->FollowUpReminder->RecurringAppointment->saveField("last_generated_id", $follow_up_remineder_id, false);
                                $this->FollowUpReminder->RecurringAppointment->saveField("last_generated_date", $fromDate, false);

                                $fromDate = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);
                                //  echo "Saved\n";
                            } else {


                            }

                            //   $this->add_actionline($Find[0]['ClientAppointment']['is_income'] ? ACTION_ADD_RECURRING_INCOME : ACTION_ADD_RECURRING_EXPENSE, array('primary_id' => $this->ClientAppointment->id, 'param1' => $Find[0]['ClientAppointment']['amount'], 'param2' => $Find[0]['ClientAppointment']['category'], 'param3' => $Find[0]['ClientAppointment']['vendor'], 'param6' => $Find[0]['ClientAppointment']['date'], 'staff_id' => -2));

                        }
                    }

                    if (strtotime($RecurringAppointment['RecurringAppointment']['end_date']) > 0 && $fromDate >= $RecurringAppointment['RecurringAppointment']['end_date'])
                        $this->FollowUpReminder->RecurringAppointment->saveField("is_ended", 1, false);
                }
                Configure::delete('cron_branch_id');

            } catch (Throwable $exception) {
                Configure::delete('cron_branch_id');
                continue;
            }
        }


        $this->autoRender = $this->autoLayout = false;
    }
    
    
    function owner_get_staff  ( ) {
//        $this->loadModel ( 'AppointmentsStaff' ) ;
        $this->loadModel ( 'ItemStaff' ) ;
        print_r ( $this->ItemStaff->find ( 'all'));die ; 
    }
    function get_link ( $app_id ) {
        $this->loadModel ( 'FollowUpReminder');
        $this->loadModel ( 'FollowUpStatus');
        $reminder = $this->FollowUpReminder->findById ( $app_id );
        $item_id = $reminder['FollowUpReminder']['item_id'];
        $item_type = $reminder['FollowUpReminder']['item_type'];
        return ( Router::url(FollowUpStatus::$types_diff[$item_type]['view_url'].$item_id , true)) ;
    }

	
	function client_view($id = null)
	{
		$this->loadModel ( 'FollowUpReminder') ;
		$client_id = getAuthClient('id');
		$conditions = ['FollowUpReminder.id' => $id, 'FollowUpReminder.partner_id' => $client_id, 'FollowUpReminder.partner_type' => FollowUpReminder::CLIENT_PARTNER_TYPE, 'FollowUpReminder.share_with_partner' => 1];
		$appointment = $this->FollowUpReminder->find('first',['conditions' => $conditions]);
		
		if(!$appointment)
		{
			$this->flashMessage(sprintf(__('%s not found', true), __('Appointment',true)));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('appointment',$appointment);
	}

    public function getShareWithSocialMediaData($appointment,$plugin,$messageTemplates=null) {
        $placeholders = PlaceHolder::appointment_place_holder($appointment) + PlaceHolder::client_place_holder($appointment);
        if (empty($messageTemplates)) {
            $messageTemplates = Settings::getValue($plugin, 'social_media_share_message_templates');
        }
        $messageTemplates = json_decode($messageTemplates, true);
        $socialMediaMessageData = [];
        foreach ($messageTemplates as $key => $messageTemplate) {
            // handles custom platforms
            if (is_array($messageTemplate)) { 
                $linkTemplate = $messageTemplate['link_template'];
                $messageTemplate = $messageTemplate['message'];
            }
            $message = PlaceHolder::replace($messageTemplate, array_keys($placeholders), $placeholders);
            $socialMediaMessageData[] = [
                'link' => SocialMediaLinkCreatorFactory::make($key, $linkTemplate ?? null)->getLink($appointment['Client']['phone2'], $message, $key),
                'label' => $key,
            ];
        }
        return $socialMediaMessageData;
    }

}
