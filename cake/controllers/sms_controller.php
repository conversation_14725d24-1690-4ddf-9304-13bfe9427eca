<?php
/** @property SmsCampaign $SmsCampaign */
/** @property Client $Client */

use App\Repositories\WhatsAppTemplateRepository;
use App\Services\WhatsAppTemplateService;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Limitation\Utils\LimitationUtil;
use SendSMS\WhatsAppSendSMS;

/** @property FollowUpReminder $FollowUpReminder */
class SmsController extends AppController {
	var $uses = [];
	function beforeFilter(){
		parent::beforeFilter();
		if(!sms_enabled()){
			$this->flashMessage(__('You need to enable the SMS plugin first', true));
			$this->redirect(["controller"=>"owners", "action"=>"plugin_manager",2]);
		}
	}
	
	function owner_index($redirect = false){
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        \App::import('Vendor', 'settings');
        $external = \settings::getValue(0, "sms_external",null,false,false);
        if(!empty($external)){
            $sms_settings = json_decode(\settings::getValue(0, "sms_settings",null,false,false),true);

        }
		if($redirect){
            $this->redirect(["controller"=>"sms", "action"=>"settings", $sms_settings["api"]]);
		}
		\App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		$this->set('APIs', \SendSMS\APIs::getExternalCountryAPIs(getAuthOwner("country_code")));
		$this->set('snippet', $this->get_snippet('sms-welcome'));
		$this->set('enable_api', (isset($sms_settings["api"])?$sms_settings["api"]:false));
	}
	
	function owner_settings($class)
    {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->handleSiteLimit(
            checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::LINK_SMS_GATEWAYS),
            'index'
        );

		\App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		\App::import('Vendor', 'settings');
		if(empty($class)||empty(\SendSMS\APIs::$api[$class])){
			$this->flashMessage(__('Wrong SMS gateway', true));
			$this->redirect(["action"=>"index"]);
		}
		if(empty(\SendSMS\APIs::getExternalCountryAPIs(getAuthOwner("country_code"))[$class])){
			$this->flashMessage(__('SMS gateway not available in your country', true));
			$this->redirect(["action"=>"index"]);
		}
		if(!empty($this->data)){
			$sms_settings = ["api"=>$class,$class=>$this->data];
			if(class_exists("\\SendSMS\\".$class)) {
				$class_name = "\\SendSMS\\".$class;
				$object = new $class_name($sms_settings);
				if(!$object->verify_credentials()){
                    if(method_exists($object,'getErrorMsg')){
                        $this->flashMessage($object->getErrorMsg());
                    }else {
                        $this->flashMessage(__(sprintf(__('Invalid %s', true), __('Credentials', true)), true));
                    }
				} else {
					\settings::setValue(0, "sms_external", 1,false);
					\settings::setValue(0, "sms_settings", json_encode($sms_settings),false);
					$this->flashMessage(__('Data Saved', true), 'Sucmessage');
                    $url = $this->getRedirectUrl($class);
                    $this->redirect($url);
				}
			}
		}elseif(!empty(\settings::getValue(0, "sms_external",null,false,false))){
			$sms_settings = json_decode(\settings::getValue(0, "sms_settings",null,false,false),true);
			$this->data = $sms_settings[$class];
		}
		$snippet = $this->get_snippet('sms-' . strtolower($class));
		if(empty($snippet)) $snippet = str_replace(["{{%website_name%}}", "{{%website_url%}}"], [\SendSMS\APIs::$api[$class]["label"], \SendSMS\APIs::$api[$class]["url"]], $this->get_snippet('sms-default'));
		$this->set('snippet', $snippet);
		$this->set('class', $class);
		$this->set('api', \SendSMS\APIs::$api[$class]);
	}

    public function owner_whatsapp_settings()
    {
        \App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));

        $smsSettings = json_decode(\settings::getValue(0, "sms_settings",null,false,false),true);
        $whatsappTemplateRepo = new WhatsAppTemplateRepository();

        $service = new WhatsAppTemplateService(
            $whatsappTemplateRepo,
            new WhatsAppSendSMS($smsSettings)
        );
        $this->set('templates', $whatsappTemplateRepo->getAllTemplates());
        if ($this->data) {
            if ($this->data['create_templates']) {
                $response =  $service->createInitialTemplates();
                dd($response);
            }
        }
    }

    private function getRedirectUrl($class)
    {
        if ($class === 'WhatsAppSendSMS') {
            return ['action' => 'whatsapp_settings'];
        }
        return ['action' => 'index'];
    }

	public function owner_send_message()
	{
		$this->autoRender = false;
		$this->RequestHandler->respondAs('json');

		$rawInput = file_get_contents('php://input');
		$data = json_decode($rawInput, true);

		$errors = [];
		// Validate phone
		if (empty($data['phone'])) {
			$errors['phone'] = 'Phone number is required.';
		} elseif (!preg_match('/^\+?\d{6,15}$/', $data['phone'])) {
			$errors['phone'] = 'Phone number must be numeric and valid.';
		}

		// Validate country code (2 uppercase letters)
		if (empty($data['country_code'])) {
			$errors['country_code'] = 'Country code is required.';
		} elseif (!preg_match('/^[A-Z]{2}$/', $data['country_code'])) {
			$errors['country_code'] = 'Country code must be 2 uppercase letters.';
		}

		// Validate message
		if (empty($data['message'])) {
			$errors['message'] = 'Message content is required.';
		}

		if (!empty($errors)) {
			return json_encode([
				'status' => false,
				'message' => 'Validation failed',
				'errors' => $errors
			]);
		}

		App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		$sms_error = '';
		$sms_campaign_id = \SendSMS\SendSMS::sendMessage([
				[
					'phone'=>$data['phone'], 
					'country_code'=>$data['country_code']
				]
			], 
			$data['message'],
			$sms_error
		);

		return json_encode([
			'status' => true,
			'message' => 'Sms Sent Successfully',
			'data' => $sms_campaign_id
		]);
	}
}

