<?php

use Izam\Daftra\Portal\Models\SitesEnquiry as PortalSitesEnquiry;
use Izam\Daftra\Portal\Services\SitesEnquiryService;

App::import('Vendor', 'Ticket', array('file' => 'Ticket.php'));
class SitesEnquiriesController extends AppController {

    var $name = 'SitesEnquiries';

    /**
     * @var SitesEnquiry
     */
    var $SitesEnquiry;
    var $helpers = array('Html', 'Form');
    var $components = array('Session', 'Email', 'RequestHandler');

    function owner_view($id = null) {
        $row = $this->SitesEnquiry->read(null, $id);
        $this->set('row', $row);
    }

    function owner_index($type = false) {
 
        $site = getAuthOwner();
        
        $ajax = $this->RequestHandler->isAjax();
        $sent = false;
        if (isset($_GET['sent']))
            $sent = true;
        if (!empty($this->data)) {
            $sent = true;
            if ($type && empty($this->data['SitesEnquiry']['type'])) {
                $this->data['SitesEnquiry']['type'] = $type;
            } else {
                $this->data['SitesEnquiry']['type'] = 5;
                $type = 5;
            }
            $site_id = getAuthOwner('id');
            $this->data['SitesEnquiry']['site_id'] = $site_id;

            $this->loadModel('SitesEnquiry');
            $this->data = $this->SitesEnquiry->invokeBehaviorBeforeSave('SitesEnquiry', 'file', $this->data);
            $result = SitesEnquiryService::create($this->data['SitesEnquiry']);

            $message = nl2br($this->data['SitesEnquiry']['description']) . '<br/><br/><br/>---------<br/>From #' . $site['id'] . ' ( ' . $site['business_name'] . ' ) - ' . $site['country_code'] . ' -  Plan: ' . $site['plan_id'] . ' ExpDate:' . $site['expiry_date'];
            //
          if(strpos($site['email'], "yahoo")==true){
                  $data = array(
            'name' => $site['first_name'] . ' ' . $site['last_name'],
            'email' =>$site['email'],
            'subject' => $this->config['txt.site_name'] . ': ' . $this->data['SitesEnquiry']['subject'],
            'message' =>'data:text/html;charset=utf-8,'. $message,
            'ip' => get_real_ip(),
        );
            
            $data['attachments'][] =array($result['file'] =>'data:application/octet-stream;base64,' .base64_encode(file_get_contents(getcwd() . "/files/pdfs/" . $result['file'])));
            $ticket=Ticket::Create($data);
            if($ticket['status']==true){
             $mail_result=true;   
            }else{
                $mail_result=false;
            }
          }else{
          $mail_result=false;    
          }


            if (!$mail_result) {

                $this->Email->to = $this->config['txt.admin_mail'];
                $this->Email->delivery = 'smtp';
//                $array = array(
//                    'port' => 25,
//                    'timeout' => '30',
//                    'host' => "localhost",
//                    'client' => "Daftra Smtp"
//                );
                $array = [];
                $this->Email->smtpOptions = $array;
                $this->Email->sendAs = 'html';
                $this->Email->from = $site['first_name'] . ' ' . $site['last_name'] . '<' . (str_replace("@", "_", $site['email']) . '@' . Domain_Short_Name) . '>';
                $this->Email->replyTo = $site['email'];
                $this->Email->bcc = '<EMAIL>'; // Single email address

                $this->Email->subject = $this->config['txt.site_name'] . ': ' . $this->data['SitesEnquiry']['subject'];

                if (!empty($result['file'])) {
                    $this->Email->attachments = array($result['file'] => getcwd() . "/files/pdfs/" . $result['file']);
                }

                $mail_result = $this->Email->send($message);
            }

            if ($mail_result) {
                if ($ajax) {
                    die(json_encode(array(
                        'message' => __('Thank you, your enquiry has been submitted', true),
                        'class' => 'SuccessMessage',
                        'error' => false
                    )));
                } else {
                    $this->flashMessage(__('Thank you. Your message will be passed to one of our customer service colleagues who will be in contact as soon as possible.', true), 'Sucmessage');
                    if (isset($_GET['box'])) {
                        $this->redirect(array('action' => 'index', '?' => array('box' => 1, 'sent' => 1)));
                    } else {
                        $this->redirect(array('action' => 'index', '?' => array('sent' => 1)));

                    }
                }
            } else {
                if ($ajax) {
                    die(json_encode(array(
                        'message' => 'Error during submitting your enquiry',
                        'class' => 'Errormessage',
                        'error' => true
                    )));
                } else {
                    $this->flashMessage(__('Error during submitting your enquiry', true));
                    if (isset($_GET['box'])) {
                        $this->redirect(array('action' => 'index', '?' => array('box' => 1)));
                    } else {
                        $this->redirect(array('action' => 'index'));
                    }
                }
            }
        }
        $this->set('sent', $sent);
        $this->set('file_settings', $this->SitesEnquiry->getFileSettings());
    }

}

?>
