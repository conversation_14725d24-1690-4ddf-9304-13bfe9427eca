<?php
class ShiftDaysController extends AppController {

	var $name = 'ShiftDays';

	/**
	 * @var ShiftDay
	 */
	var $ShiftDay;
	var $helpers = array('Html', 'Form');

	

	function owner_index() {
		$this->ShiftDay->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('shiftDays', $this->paginate('ShiftDay', $conditions));
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('shift day', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('shiftDay', $this->ShiftDay->read(null, $id));
	}

	function owner_add() {
		if (!empty($this->data)) {
			$this->ShiftDay->create();
			
			if ($this->ShiftDay->saveAll($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('shift day',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('shift day',true)));
			}
		}
		$Shift = GetObjectOrLoadModel('Shift');
		$shifts = $Shift->find('list');
		$this->set('shifts', $shifts);
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'shift day',true)));
			$this->redirect(array('action'=>'index'));
		}
		$shiftDay = $this->ShiftDay->read(null, $id);
		if (!empty($this->data)) {
			if ($this->ShiftDay->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('shift day',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('shift day',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $shiftDay;
		}
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('shift day',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('shiftDay', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('shiftDays', true);
		 } 
		$shiftDays = $this->ShiftDay->find('all',array('conditions'=>array('ShiftDay.id'=>$id)));
		if (empty($shiftDays)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->ShiftDay->deleteAll(array('ShiftDay.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('shiftDays',$shiftDays);
	}
}
?>