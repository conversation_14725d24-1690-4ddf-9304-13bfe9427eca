<?php
class ItemsCategoriesController extends AppController {

	var $name = 'ItemsCategories';

	/**
	 * @var ItemsCategory
	 */
	var $ItemsCategory;
	var $helpers = array('Html', 'Form');

	function index() {
		$this->ItemsCategory->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('itemsCategories', $this->paginate('ItemsCategory', $conditions));
	}

	function view($id = null) {
		if (!$id) {
			$this->flash(__(sprintf ('Invalid %s', 'items category'),true), array('action'=>'index'));
		}
		$this->set('itemsCategory', $this->ItemsCategory->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->ItemsCategory->create();
			if ($this->ItemsCategory->save($this->data)) {
				$this->flash(sprintf(__('%s saved', true), __('items category',true)),array('action'=>'index'));
			} else {
			}
		}
		$categories = $this->ItemsCategory->Category->find('list');
		$this->set(compact('categories'));
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flash(sprintf (__('Invalid %s', true), __('items category',true)), array('action'=>'index'));
		}
		$itemsCategory = $this->ItemsCategory->read(null, $id);
		if (!empty($this->data)) {
			if ($this->ItemsCategory->save($this->data)) {
				$this->flash(sprintf (__('The %s  has been saved', true), __('items category',true)), array('action'=>'index'));
			} else {
			}
		}
		if (empty($this->data)) {
			$this->data = $itemsCategory;
		}
		$categories = $this->ItemsCategory->Category->find('list');
		$this->set(compact('categories'));
		$this->render('add');
	}

	function delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flash(sprintf (__('Invalid %s', true), __('items category',true)), array('action'=>'index'));
		}
		$module_name= __('itemsCategory', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('itemsCategories', true);
		 } 
		$itemsCategories = $this->ItemsCategory->find('all',array('conditions'=>array('ItemsCategory.id'=>$id)));
		if (empty($itemsCategories)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->ItemsCategory->deleteAll(array('ItemsCategory.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s has been deleted', true), $module_name), array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('itemsCategories',$itemsCategories);
	}

	function owner_index() {
		$this->ItemsCategory->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('itemsCategories', $this->paginate('ItemsCategory', $conditions));
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flash(__(sprintf ('Invalid %s', 'items category'),true), array('action'=>'index'));
		}
		$this->set('itemsCategory', $this->ItemsCategory->read(null, $id));
	}

	function owner_add() {
		if (!empty($this->data)) {
			$this->ItemsCategory->create();
			if ($this->ItemsCategory->save($this->data)) {
				$this->flash(sprintf(__('%s saved', true), __('items category',true)),array('action'=>'index'));
			} else {
			}
		}
		$categories = $this->ItemsCategory->Category->find('list');
		$this->set(compact('categories'));
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flash(sprintf (__('Invalid %s', true), __('items category',true)), array('action'=>'index'));
		}
		$itemsCategory = $this->ItemsCategory->read(null, $id);
		if (!empty($this->data)) {
			if ($this->ItemsCategory->save($this->data)) {
				$this->flash(sprintf (__('The %s  has been saved', true), __('items category',true)), array('action'=>'index'));
			} else {
			}
		}
		if (empty($this->data)) {
			$this->data = $itemsCategory;
		}
		$categories = $this->ItemsCategory->Category->find('list');
		$this->set(compact('categories'));
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flash(sprintf (__('Invalid %s', true), __('items category',true)), array('action'=>'index'));
		}
		$module_name= __('itemsCategory', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('itemsCategories', true);
		 } 
		$itemsCategories = $this->ItemsCategory->find('all',array('conditions'=>array('ItemsCategory.id'=>$id)));
		if (empty($itemsCategories)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->ItemsCategory->deleteAll(array('ItemsCategory.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s has been deleted', true), $module_name), array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('itemsCategories',$itemsCategories);
	}
}
?>